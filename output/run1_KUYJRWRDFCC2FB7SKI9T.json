{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 6.599215703317896e-05, "Y": 0.0}, {"X": 1.0, "Y": 2.822401984303724e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "db7d039e-c924-449f-89b0-5b604329cf28", "Relationships": [{"Type": "CHILD", "Ids": ["fb73b9d0-ceae-46b6-9f8f-995a01cd2e7b", "8600ba49-5651-446d-b360-9d65d2c9f2f5", "b339c108-c38f-4f24-b5ff-fb3d5693c034", "6da4b42e-6191-4703-bda1-3ce4f029011a", "31512752-7418-457d-a2b6-067beda4efb8", "f7365d27-e027-4cde-b715-b552e8e9a530", "c9c86928-d625-4571-bba9-6045d8ffaefa", "41253fb4-536e-4c0e-adca-875c0c85cb8d", "481a6c84-80f5-4bbd-9b25-ca78ec04b7e3", "5d0ec025-9974-4cd4-b015-2a45cced3eac", "14546df6-3771-4a87-b8d5-9eb3a73544cc", "c740e845-9cd0-4ec6-9bc3-c5db1574d6a0", "9c1cb75e-3b33-4bcb-a2a7-63cd13babe71", "3389620d-57a6-4d75-afd0-d3810a202a19", "540c8b0f-a719-4c48-8ddc-8aeb3e60868b", "3ad62db2-dcd5-4f5e-87dc-0e1852a6b95f", "2e9a6931-0024-496b-ac32-118c0276d4c6", "ef4032a3-649b-48c1-9882-b8293b4b4576", "f7d7f785-4596-4957-be72-469ac4203c98", "35e3b2f6-ad5f-4594-ade6-2185db683d83", "e1bdbf55-55cd-4b06-b412-e07bef8366d0", "3ab249f4-4571-42ea-92ff-e599f12ea571", "2fe40b3b-2306-4db1-be27-bd4327f79b8e", "76db6054-c097-436d-8369-2720674bcc29", "92ff7d8c-cebf-4eaf-a463-d2f1ad13db0f", "a650935f-c507-430c-b517-709cf7f1c77b", "bc46b524-d1bf-4480-b3e6-9bf6bbbfb1e1", "a325a236-3723-4aab-ab13-0d73e2613f09", "5f4517dc-1c14-462f-b3bc-ada1f06a5959", "cd2a45bf-dc6f-47de-896d-bb81231f84d8", "efdf5070-9fdc-46df-9576-b682cf4a7fb8", "c669d8b3-ffa7-43ca-a8e2-ed0581b48de0", "2dcf98e8-756d-4fec-a4af-5a101c57274c", "31e60358-9fa8-4384-88e3-ac605de9936b", "88d2dcfd-c842-49d7-bee3-fac73e31fad1", "95207412-b753-4429-a57e-becad50fa178", "2a2b82cd-5ee5-4714-992c-04f5c86d1128", "652de9d1-e37d-4272-a93a-ff5d60ade568", "939d4ed1-0285-45f1-8a5f-e839f1212d2d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Packing Slip", "Geometry": {"BoundingBox": {"Width": 0.25169363617897034, "Height": 0.035985205322504044, "Left": 0.624079167842865, "Top": 0.05689126253128052}, "Polygon": [{"X": 0.624079167842865, "Y": 0.05689126253128052}, {"X": 0.8756729364395142, "Y": 0.05690433084964752}, {"X": 0.8757727742195129, "Y": 0.09287646412849426}, {"X": 0.6241368055343628, "Y": 0.09286366403102875}]}, "Id": "fb73b9d0-ceae-46b6-9f8f-995a01cd2e7b", "Relationships": [{"Type": "CHILD", "Ids": ["7d427359-8340-4316-b869-cfe180cb3e8d", "8febac9d-215b-4444-a643-b81a516d427e"]}]}, {"BlockType": "LINE", "Confidence": 98.76693725585938, "Text": "FAB MASTERS, INC.", "Geometry": {"BoundingBox": {"Width": 0.3374997675418854, "Height": 0.024569757282733917, "Left": 0.08424477279186249, "Top": 0.08058545738458633}, "Polygon": [{"X": 0.0842672660946846, "Y": 0.08058545738458633}, {"X": 0.42172837257385254, "Y": 0.0806027501821518}, {"X": 0.42174455523490906, "Y": 0.10515521466732025}, {"X": 0.08424477279186249, "Y": 0.10513816028833389}]}, "Id": "8600ba49-5651-446d-b360-9d65d2c9f2f5", "Relationships": [{"Type": "CHILD", "Ids": ["634af63a-17ea-487e-ab53-b60c1a5ca1de", "a81f7602-9288-43b9-bf68-fae9d0d60dd0", "c7048ad2-bc57-443f-963c-c55c99dad272"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "42015", "Geometry": {"BoundingBox": {"Width": 0.03739466518163681, "Height": 0.010262549854815006, "Left": 0.8332256078720093, "Top": 0.09814078360795975}, "Polygon": [{"X": 0.8332256078720093, "Y": 0.09814078360795975}, {"X": 0.8705920577049255, "Y": 0.09814267605543137}, {"X": 0.8706203103065491, "Y": 0.10840333253145218}, {"X": 0.8332520723342896, "Y": 0.10840144753456116}]}, "Id": "b339c108-c38f-4f24-b5ff-fb3d5693c034", "Relationships": [{"Type": "CHILD", "Ids": ["de103d54-0855-442e-a533-de9e37d14ea2"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.05986490100622177, "Height": 0.008148731663823128, "Left": 0.0759674459695816, "Top": 0.16843514144420624}, "Polygon": [{"X": 0.0759752169251442, "Y": 0.16843514144420624}, {"X": 0.13583233952522278, "Y": 0.16843806207180023}, {"X": 0.13582684099674225, "Y": 0.17658387124538422}, {"X": 0.0759674459695816, "Y": 0.1765809804201126}]}, "Id": "6da4b42e-6191-4703-bda1-3ce4f029011a", "Relationships": [{"Type": "CHILD", "Ids": ["c31e27b9-564f-4429-87e5-dfb0ecccc588"]}]}, {"BlockType": "LINE", "Confidence": 99.49942779541016, "Text": "901 Joliet St", "Geometry": {"BoundingBox": {"Width": 0.08273833245038986, "Height": 0.009575898759067059, "Left": 0.2827449142932892, "Top": 0.16359812021255493}, "Polygon": [{"X": 0.2827449142932892, "Y": 0.16359812021255493}, {"X": 0.365479439496994, "Y": 0.16360215842723846}, {"X": 0.36548322439193726, "Y": 0.17317402362823486}, {"X": 0.28274500370025635, "Y": 0.17317001521587372}]}, "Id": "31512752-7418-457d-a2b6-067beda4efb8", "Relationships": [{"Type": "CHILD", "Ids": ["f6733c3b-c3ca-4ed7-ba84-0ff80cfc382a", "dd997f67-1cdc-4f08-be30-61558d6c01c6", "cba7c0d4-855a-48e9-ade5-9e2f1cafa805"]}]}, {"BlockType": "LINE", "Confidence": 99.87369537353516, "Text": "Ship To: RexCon LLC", "Geometry": {"BoundingBox": {"Width": 0.14361661672592163, "Height": 0.010382348671555519, "Left": 0.5213417410850525, "Top": 0.16395577788352966}, "Polygon": [{"X": 0.5213417410850525, "Y": 0.16395577788352966}, {"X": 0.6649397611618042, "Y": 0.16396278142929077}, {"X": 0.6649583578109741, "Y": 0.17433811724185944}, {"X": 0.5213533639907837, "Y": 0.1743311583995819}]}, "Id": "f7365d27-e027-4cde-b715-b552e8e9a530", "Relationships": [{"Type": "CHILD", "Ids": ["7bd0fc28-f5db-4a96-b8e0-8544450a0a90", "268e977e-93d3-4c75-90f1-7f9ba4be9e2e", "7d17c28b-3d0d-4f9c-a4f5-246c76f8f3fb", "7b1c7915-74a7-4f04-9b28-609370eeea2e"]}]}, {"BlockType": "LINE", "Confidence": 99.59163665771484, "Text": "Janesville, WI 53548", "Geometry": {"BoundingBox": {"Width": 0.13950203359127045, "Height": 0.010391097515821457, "Left": 0.2827231287956238, "Top": 0.1810404360294342}, "Polygon": [{"X": 0.2827231287956238, "Y": 0.1810404360294342}, {"X": 0.42221832275390625, "Y": 0.18104717135429382}, {"X": 0.4222251772880554, "Y": 0.19143153727054596}, {"X": 0.28272324800491333, "Y": 0.19142484664916992}]}, "Id": "c9c86928-d625-4571-bba9-6045d8ffaefa", "Relationships": [{"Type": "CHILD", "Ids": ["598c0c37-26cd-41ff-9b78-3ec814f01acb", "a37089b3-f1df-4442-bcf9-ed8067c6da3b", "39b97cf1-b537-4ad7-a302-bf21c8c21cc2"]}]}, {"BlockType": "LINE", "Confidence": 99.3494873046875, "Text": "2841 Whiting Rd.", "Geometry": {"BoundingBox": {"Width": 0.12016178667545319, "Height": 0.010937057435512543, "Left": 0.582475483417511, "Top": 0.18217191100120544}, "Polygon": [{"X": 0.582475483417511, "Y": 0.18217191100120544}, {"X": 0.7026157975196838, "Y": 0.18217770755290985}, {"X": 0.7026373147964478, "Y": 0.19310897588729858}, {"X": 0.582490861415863, "Y": 0.19310320913791656}]}, "Id": "41253fb4-536e-4c0e-adca-875c0c85cb8d", "Relationships": [{"Type": "CHILD", "Ids": ["62e66752-fb40-4101-b57f-87b0fee90432", "3a18a802-798a-451b-9ce6-2d2ddd070aaa", "e3f0fe5f-232d-41ad-a8b7-2682a2b0d1b1"]}]}, {"BlockType": "LINE", "Confidence": 99.87633514404297, "Text": "Phone: ************", "Geometry": {"BoundingBox": {"Width": 0.13745468854904175, "Height": 0.009694014675915241, "Left": 0.282889723777771, "Top": 0.19817394018173218}, "Polygon": [{"X": 0.282889723777771, "Y": 0.19817394018173218}, {"X": 0.42033809423446655, "Y": 0.19818049669265747}, {"X": 0.42034441232681274, "Y": 0.20786795020103455}, {"X": 0.28288981318473816, "Y": 0.20786142349243164}]}, "Id": "481a6c84-80f5-4bbd-9b25-ca78ec04b7e3", "Relationships": [{"Type": "CHILD", "Ids": ["259d894e-8105-4afd-9120-6186a699f34a", "bb3e70b7-1dd1-4159-a651-1fa56c49ac92"]}]}, {"BlockType": "LINE", "Confidence": 99.86819458007812, "Text": "Burlington, WI 53105", "Geometry": {"BoundingBox": {"Width": 0.15055479109287262, "Height": 0.010560383088886738, "Left": 0.5826622843742371, "Top": 0.1993805170059204}, "Polygon": [{"X": 0.5826622843742371, "Y": 0.1993805170059204}, {"X": 0.7331948280334473, "Y": 0.19938769936561584}, {"X": 0.7332171201705933, "Y": 0.20994089543819427}, {"X": 0.5826771855354309, "Y": 0.20993375778198242}]}, "Id": "5d0ec025-9974-4cd4-b015-2a45cced3eac", "Relationships": [{"Type": "CHILD", "Ids": ["9c00a7ac-130c-4dce-9024-9841732ce0db", "7d0764fa-8738-4b28-be19-c0c61bb6dc46", "b3384382-6a7f-460f-9a7a-d290a557c24a"]}]}, {"BlockType": "LINE", "Confidence": 99.91619873046875, "Text": "Fax: ************", "Geometry": {"BoundingBox": {"Width": 0.12036333978176117, "Height": 0.009676949121057987, "Left": 0.2828007638454437, "Top": 0.21583282947540283}, "Polygon": [{"X": 0.2828007638454437, "Y": 0.21583282947540283}, {"X": 0.403158575296402, "Y": 0.21583852171897888}, {"X": 0.4031641185283661, "Y": 0.22550977766513824}, {"X": 0.2828008532524109, "Y": 0.22550413012504578}]}, "Id": "14546df6-3771-4a87-b8d5-9eb3a73544cc", "Relationships": [{"Type": "CHILD", "Ids": ["46b7aed7-bd6d-4565-8cb4-321ed2e4cb1b", "58ac3481-f5d0-4a97-80c4-d418f02751de"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "Ship Date: 9/19/2025", "Geometry": {"BoundingBox": {"Width": 0.15741246938705444, "Height": 0.010921889916062355, "Left": 0.07123711705207825, "Top": 0.2518969178199768}, "Polygon": [{"X": 0.07124777138233185, "Y": 0.2518969178199768}, {"X": 0.2286495864391327, "Y": 0.2519041895866394}, {"X": 0.22864694893360138, "Y": 0.2628188133239746}, {"X": 0.07123711705207825, "Y": 0.2628116011619568}]}, "Id": "c740e845-9cd0-4ec6-9bc3-c5db1574d6a0", "Relationships": [{"Type": "CHILD", "Ids": ["d59ae3f6-972b-44ff-837f-30e9e2ae5cb7", "e7cfae8c-d321-4f87-b74b-08491322abc0", "21d4b0ab-feff-4bb4-936c-e611b0d57370"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "Sales Person: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.1287911981344223, "Height": 0.009194109588861465, "Left": 0.07057289779186249, "Top": 0.2692306339740753}, "Polygon": [{"X": 0.07058189064264297, "Y": 0.2692306339740753}, {"X": 0.1993640810251236, "Y": 0.2692365050315857}, {"X": 0.19936060905456543, "Y": 0.2784247398376465}, {"X": 0.07057289779186249, "Y": 0.2784188985824585}]}, "Id": "9c1cb75e-3b33-4bcb-a2a7-63cd13babe71", "Relationships": [{"Type": "CHILD", "Ids": ["301ab9b4-4ce4-412c-af11-bf671283b67b", "78778a3f-514b-453a-90a2-a25e79062214", "677a6544-c14e-4670-9026-052768b77bb4"]}]}, {"BlockType": "LINE", "Confidence": 99.93110656738281, "Text": "Bill To: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10055765509605408, "Height": 0.009082045406103134, "Left": 0.5221142768859863, "Top": 0.26970770955085754}, "Polygon": [{"X": 0.5221142768859863, "Y": 0.26970770955085754}, {"X": 0.6226574778556824, "Y": 0.26971229910850525}, {"X": 0.6226719617843628, "Y": 0.278789758682251}, {"X": 0.5221245288848877, "Y": 0.27878519892692566}]}, "Id": "3389620d-57a6-4d75-afd0-d3810a202a19", "Relationships": [{"Type": "CHILD", "Ids": ["1265ba33-2c4c-443c-afef-91eaec14a072", "a25be167-e860-4a20-868a-2533c8f8f99c", "11317b93-79be-4c3d-8323-f7a75218d124"]}]}, {"BlockType": "LINE", "Confidence": 99.20194244384766, "Text": "Purchase Order: PUR3165-105095", "Geometry": {"BoundingBox": {"Width": 0.24324731528759003, "Height": 0.009162222035229206, "Left": 0.07038415968418121, "Top": 0.2863258719444275}, "Polygon": [{"X": 0.07039313018321991, "Y": 0.2863258719444275}, {"X": 0.313630074262619, "Y": 0.28633686900138855}, {"X": 0.31363147497177124, "Y": 0.2954881191253662}, {"X": 0.07038415968418121, "Y": 0.2954771816730499}]}, "Id": "540c8b0f-a719-4c48-8ddc-8aeb3e60868b", "Relationships": [{"Type": "CHILD", "Ids": ["51952e1f-e1e6-48b2-819e-babc5c51117c", "7ff420aa-15b3-40d8-ad8b-773c7a198d0b", "b466953a-9aa0-4690-9e36-b266b75ca1d9"]}]}, {"BlockType": "LINE", "Confidence": 99.93436431884766, "Text": "Shipped Via: CPU", "Geometry": {"BoundingBox": {"Width": 0.1363130360841751, "Height": 0.01237233355641365, "Left": 0.06857012957334518, "Top": 0.30264273285865784}, "Polygon": [{"X": 0.06858234852552414, "Y": 0.30264273285865784}, {"X": 0.2048831731081009, "Y": 0.3026488423347473}, {"X": 0.2048788070678711, "Y": 0.3150150775909424}, {"X": 0.06857012957334518, "Y": 0.3150090277194977}]}, "Id": "3ad62db2-dcd5-4f5e-87dc-0e1852a6b95f", "Relationships": [{"Type": "CHILD", "Ids": ["8b9f8297-77a7-4cb7-b06e-12bed38b90f6", "c1ff8472-b260-49d6-ae86-31c0cde4ec13", "37ce6b65-343d-426c-8962-0dd6943ffaf8"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Part Number", "Geometry": {"BoundingBox": {"Width": 0.09104909002780914, "Height": 0.008867303840816021, "Left": 0.3334810137748718, "Top": 0.38307473063468933}, "Polygon": [{"X": 0.3334810137748718, "Y": 0.38307473063468933}, {"X": 0.4245241582393646, "Y": 0.38307860493659973}, {"X": 0.42453011870384216, "Y": 0.3919420540332794}, {"X": 0.33348318934440613, "Y": 0.3919382095336914}]}, "Id": "2e9a6931-0024-496b-ac32-118c0276d4c6", "Relationships": [{"Type": "CHILD", "Ids": ["8f5f5da2-5508-4ff9-b9ef-26f8b7674c7f", "dc3fc3ae-02d2-44ed-817d-5b4e7ead0db1"]}]}, {"BlockType": "LINE", "Confidence": 99.89665985107422, "Text": "Our Job#", "Geometry": {"BoundingBox": {"Width": 0.06524189561605453, "Height": 0.008746534585952759, "Left": 0.5325307250022888, "Top": 0.3839450478553772}, "Polygon": [{"X": 0.5325307250022888, "Y": 0.3839450478553772}, {"X": 0.5977596640586853, "Y": 0.38394781947135925}, {"X": 0.5977725982666016, "Y": 0.39269158244132996}, {"X": 0.5325409770011902, "Y": 0.3926888406276703}]}, "Id": "ef4032a3-649b-48c1-9882-b8293b4b4576", "Relationships": [{"Type": "CHILD", "Ids": ["064a5ce4-2d3f-4f53-bc99-e2ab50bbcbf4", "dc63fa30-28d1-4d43-844e-f0660257a2ab"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Order Quantity", "Geometry": {"BoundingBox": {"Width": 0.10689304023981094, "Height": 0.010670298710465431, "Left": 0.6307603120803833, "Top": 0.3837084174156189}, "Polygon": [{"X": 0.6307603120803833, "Y": 0.3837084174156189}, {"X": 0.7376306653022766, "Y": 0.38371291756629944}, {"X": 0.737653374671936, "Y": 0.3943787217140198}, {"X": 0.6307777166366577, "Y": 0.39437422156333923}]}, "Id": "f7d7f785-4596-4957-be72-469ac4203c98", "Relationships": [{"Type": "CHILD", "Ids": ["b451e04b-ce6f-46ed-9e87-e98c8f87d08e", "a75ab57a-be3c-47fd-8184-eb76b928f230"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Ship Quantity", "Geometry": {"BoundingBox": {"Width": 0.09942514449357986, "Height": 0.011011947877705097, "Left": 0.780119776725769, "Top": 0.3829593360424042}, "Polygon": [{"X": 0.780119776725769, "Y": 0.3829593360424042}, {"X": 0.879514217376709, "Y": 0.38296353816986084}, {"X": 0.8795449137687683, "Y": 0.3939712941646576}, {"X": 0.7801454067230225, "Y": 0.3939671218395233}]}, "Id": "35e3b2f6-ad5f-4594-ade6-2185db683d83", "Relationships": [{"Type": "CHILD", "Ids": ["1f368592-c03b-4276-9c55-382eb5ce8dc9", "8079929e-9157-4871-8853-2618f3b8fccd"]}]}, {"BlockType": "LINE", "Confidence": 89.62784576416016, "Text": "544-15247-81, RV R00", "Geometry": {"BoundingBox": {"Width": 0.16036571562290192, "Height": 0.011365697719156742, "Left": 0.0695548951625824, "Top": 0.40522831678390503}, "Polygon": [{"X": 0.06956606358289719, "Y": 0.40522831678390503}, {"X": 0.2299206256866455, "Y": 0.4052349925041199}, {"X": 0.22991792857646942, "Y": 0.4165940284729004}, {"X": 0.0695548951625824, "Y": 0.41658738255500793}]}, "Id": "e1bdbf55-55cd-4b06-b412-e07bef8366d0", "Relationships": [{"Type": "CHILD", "Ids": ["e634adff-13a2-4ca6-8c99-7b3e43c0b214", "e07b5d3d-15da-4172-b3c6-5208f7db5adc", "46d48a6a-90cc-41cd-92ce-da23b5caada9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "41932", "Geometry": {"BoundingBox": {"Width": 0.04479703679680824, "Height": 0.009580817073583603, "Left": 0.534371554851532, "Top": 0.4073173999786377}, "Polygon": [{"X": 0.534371554851532, "Y": 0.4073173999786377}, {"X": 0.5791552662849426, "Y": 0.4073192775249481}, {"X": 0.5791685581207275, "Y": 0.4168982207775116}, {"X": 0.5343828797340393, "Y": 0.41689637303352356}]}, "Id": "3ab249f4-4571-42ea-92ff-e599f12ea571", "Relationships": [{"Type": "CHILD", "Ids": ["17b35d8b-a72a-4d28-ab71-625e819f0d9e"]}]}, {"BlockType": "LINE", "Confidence": 99.17889404296875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.0071354815736413, "Height": 0.008984205313026905, "Left": 0.7451134920120239, "Top": 0.40707215666770935}, "Polygon": [{"X": 0.7451134920120239, "Y": 0.40707215666770935}, {"X": 0.7522292733192444, "Y": 0.4070724546909332}, {"X": 0.752249002456665, "Y": 0.416056364774704}, {"X": 0.7451329231262207, "Y": 0.4160560667514801}]}, "Id": "2fe40b3b-2306-4db1-be27-bd4327f79b8e", "Relationships": [{"Type": "CHILD", "Ids": ["ac47cb58-fb50-4385-88e0-376641e71412"]}]}, {"BlockType": "LINE", "Confidence": 98.52379608154297, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.006607647519558668, "Height": 0.009356127120554447, "Left": 0.8926771283149719, "Top": 0.40589457750320435}, "Polygon": [{"X": 0.8926771283149719, "Y": 0.40589457750320435}, {"X": 0.8992578387260437, "Y": 0.4058948755264282}, {"X": 0.8992847800254822, "Y": 0.4152507185935974}, {"X": 0.8927038311958313, "Y": 0.4152504503726959}]}, "Id": "76db6054-c097-436d-8369-2720674bcc29", "Relationships": [{"Type": "CHILD", "Ids": ["8bdde8fa-fb8a-4d25-b88e-ce83d132f4c9"]}]}, {"BlockType": "LINE", "Confidence": 99.37629699707031, "Text": "502-09283-80,RV R02", "Geometry": {"BoundingBox": {"Width": 0.1614183634519577, "Height": 0.01147456280887127, "Left": 0.06798099726438522, "Top": 0.4288734197616577}, "Polygon": [{"X": 0.06799235194921494, "Y": 0.4288734197616577}, {"X": 0.22939935326576233, "Y": 0.4288800060749054}, {"X": 0.22939662635326385, "Y": 0.44034796953201294}, {"X": 0.06798099726438522, "Y": 0.44034141302108765}]}, "Id": "92ff7d8c-cebf-4eaf-a463-d2f1ad13db0f", "Relationships": [{"Type": "CHILD", "Ids": ["5182b95a-cca9-4822-bf0c-1596faada994", "56d32cdb-1ad7-4848-90fc-43330c91d322"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "41936", "Geometry": {"BoundingBox": {"Width": 0.045039404183626175, "Height": 0.009109150618314743, "Left": 0.5341386795043945, "Top": 0.43116968870162964}, "Polygon": [{"X": 0.5341386795043945, "Y": 0.43116968870162964}, {"X": 0.5791653990745544, "Y": 0.4311715364456177}, {"X": 0.5791780948638916, "Y": 0.4402788579463959}, {"X": 0.5341494083404541, "Y": 0.44027701020240784}]}, "Id": "a650935f-c507-430c-b517-709cf7f1c77b", "Relationships": [{"Type": "CHILD", "Ids": ["a076be92-3369-4cd2-9e4b-5cc958016b94"]}]}, {"BlockType": "LINE", "Confidence": 98.93475341796875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.0071219890378415585, "Height": 0.00903438962996006, "Left": 0.7452986240386963, "Top": 0.4306319057941437}, "Polygon": [{"X": 0.7452986240386963, "Y": 0.4306319057941437}, {"X": 0.7524007558822632, "Y": 0.43063220381736755}, {"X": 0.7524206042289734, "Y": 0.4396663010120392}, {"X": 0.7453181743621826, "Y": 0.4396660029888153}]}, "Id": "bc46b524-d1bf-4480-b3e6-9bf6bbbfb1e1", "Relationships": [{"Type": "CHILD", "Ids": ["70cb5935-7a76-49cc-9a96-1b777576b1e8"]}]}, {"BlockType": "LINE", "Confidence": 98.91361999511719, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.0067777689546346664, "Height": 0.009319725446403027, "Left": 0.8928496837615967, "Top": 0.4296402037143707}, "Polygon": [{"X": 0.8928496837615967, "Y": 0.4296402037143707}, {"X": 0.8996005654335022, "Y": 0.4296404719352722}, {"X": 0.8996274471282959, "Y": 0.43895992636680603}, {"X": 0.8928762674331665, "Y": 0.43895965814590454}]}, "Id": "a325a236-3723-4aab-ab13-0d73e2613f09", "Relationships": [{"Type": "CHILD", "Ids": ["effd7ae4-c6b5-4bbe-8118-04174adb81db"]}]}, {"BlockType": "LINE", "Confidence": 98.68134307861328, "Text": "524-14659-86,RV R09", "Geometry": {"BoundingBox": {"Width": 0.16330574452877045, "Height": 0.01169147714972496, "Left": 0.06577382236719131, "Top": 0.452280193567276}, "Polygon": [{"X": 0.06578551232814789, "Y": 0.452280193567276}, {"X": 0.22907955944538116, "Y": 0.4522867500782013}, {"X": 0.22907675802707672, "Y": 0.46397167444229126}, {"X": 0.06577382236719131, "Y": 0.46396514773368835}]}, "Id": "5f4517dc-1c14-462f-b3bc-ada1f06a5959", "Relationships": [{"Type": "CHILD", "Ids": ["e734b756-2b48-4574-95ba-22d8b34bdf1a", "80bf37b5-59a5-4714-9ac5-d4189ca601cc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "41939", "Geometry": {"BoundingBox": {"Width": 0.04513927549123764, "Height": 0.009279871359467506, "Left": 0.534461498260498, "Top": 0.454617977142334}, "Polygon": [{"X": 0.534461498260498, "Y": 0.454617977142334}, {"X": 0.5795878767967224, "Y": 0.45461979508399963}, {"X": 0.5796008110046387, "Y": 0.46389785408973694}, {"X": 0.5344724655151367, "Y": 0.4638960659503937}]}, "Id": "cd2a45bf-dc6f-47de-896d-bb81231f84d8", "Relationships": [{"Type": "CHILD", "Ids": ["9256bdfd-4319-4490-b00d-5cd4eb45c192"]}]}, {"BlockType": "LINE", "Confidence": 99.35486602783203, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008686013519763947, "Height": 0.009210746735334396, "Left": 0.7450685501098633, "Top": 0.4539314806461334}, "Polygon": [{"X": 0.7450685501098633, "Y": 0.4539314806461334}, {"X": 0.753734290599823, "Y": 0.4539318382740021}, {"X": 0.7537546157836914, "Y": 0.4631422162055969}, {"X": 0.745088517665863, "Y": 0.46314188838005066}]}, "Id": "efdf5070-9fdc-46df-9576-b682cf4a7fb8", "Relationships": [{"Type": "CHILD", "Ids": ["e344ffa7-b6af-4a1b-9d05-5cf00cc545d1"]}]}, {"BlockType": "LINE", "Confidence": 98.48114776611328, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008101689629256725, "Height": 0.009276757016777992, "Left": 0.893018901348114, "Top": 0.453212708234787}, "Polygon": [{"X": 0.893018901348114, "Y": 0.453212708234787}, {"X": 0.9010937809944153, "Y": 0.45321303606033325}, {"X": 0.9011206030845642, "Y": 0.46248945593833923}, {"X": 0.8930453062057495, "Y": 0.46248912811279297}]}, "Id": "c669d8b3-ffa7-43ca-a8e2-ed0581b48de0", "Relationships": [{"Type": "CHILD", "Ids": ["41d9724d-dac6-4e0e-a309-13838e2504dc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipping", "Geometry": {"BoundingBox": {"Width": 0.06305529177188873, "Height": 0.01238721888512373, "Left": 0.5359132885932922, "Top": 0.6444990634918213}, "Polygon": [{"X": 0.5359132885932922, "Y": 0.6444990634918213}, {"X": 0.5989502668380737, "Y": 0.6445012092590332}, {"X": 0.5989685654640198, "Y": 0.6568862795829773}, {"X": 0.5359280109405518, "Y": 0.6568840742111206}]}, "Id": "2dcf98e8-756d-4fec-a4af-5a101c57274c", "Relationships": [{"Type": "CHILD", "Ids": ["218369ac-aff4-4460-a8c4-eb6b3a5925cf"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.04552079737186432, "Height": 0.009537101723253727, "Left": 0.6686421632766724, "Top": 0.6447721123695374}, "Polygon": [{"X": 0.6686421632766724, "Y": 0.6447721123695374}, {"X": 0.7141437530517578, "Y": 0.6447737216949463}, {"X": 0.7141629457473755, "Y": 0.6543092131614685}, {"X": 0.6686593890190125, "Y": 0.6543076634407043}]}, "Id": "31e60358-9fa8-4384-88e3-ac605de9936b", "Relationships": [{"Type": "CHILD", "Ids": ["166643f2-56db-4c15-bfb6-d670f884d0ec"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Inspector", "Geometry": {"BoundingBox": {"Width": 0.07158830016851425, "Height": 0.011886224150657654, "Left": 0.8076614141464233, "Top": 0.645178496837616}, "Polygon": [{"X": 0.8076614141464233, "Y": 0.645178496837616}, {"X": 0.8792166113853455, "Y": 0.6451810002326965}, {"X": 0.8792496919631958, "Y": 0.6570647358894348}, {"X": 0.8076905608177185, "Y": 0.657062292098999}]}, "Id": "88d2dcfd-c842-49d7-bee3-fac73e31fad1", "Relationships": [{"Type": "CHILD", "Ids": ["5a02e46c-ffc9-45b5-8a5f-9ed1017f127e"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Shipment visually", "Geometry": {"BoundingBox": {"Width": 0.1366502195596695, "Height": 0.01249418593943119, "Left": 0.055859845131635666, "Top": 0.6657568216323853}, "Polygon": [{"X": 0.055872902274131775, "Y": 0.6657568216323853}, {"X": 0.19251006841659546, "Y": 0.6657614707946777}, {"X": 0.19250494241714478, "Y": 0.6782510280609131}, {"X": 0.055859845131635666, "Y": 0.6782464385032654}]}, "Id": "95207412-b753-4429-a57e-becad50fa178", "Relationships": [{"Type": "CHILD", "Ids": ["6053d819-e0b5-4f9e-97ef-618005453886", "526bf9fa-7ff9-4a4d-8747-1d038a916f3b"]}]}, {"BlockType": "LINE", "Confidence": 99.96392822265625, "Text": "checked for damage", "Geometry": {"BoundingBox": {"Width": 0.1565200537443161, "Height": 0.012692270800471306, "Left": 0.05383848771452904, "Top": 0.6814243793487549}, "Polygon": [{"X": 0.05385187268257141, "Y": 0.6814243793487549}, {"X": 0.21035854518413544, "Y": 0.6814296245574951}, {"X": 0.21035438776016235, "Y": 0.6941166520118713}, {"X": 0.05383848771452904, "Y": 0.6941114664077759}]}, "Id": "2a2b82cd-5ee5-4714-992c-04f5c86d1128", "Relationships": [{"Type": "CHILD", "Ids": ["3fb74e34-0853-4b17-ae64-072c9a0efb74", "99aaba74-bd6f-47a0-baca-36f5603f256f", "a8569185-7cb3-47e6-9b9c-f934ff39a08b"]}]}, {"BlockType": "LINE", "Confidence": 96.91400909423828, "Text": "Received by: <PERSON>er", "Geometry": {"BoundingBox": {"Width": 0.497711181640625, "Height": 0.08874966949224472, "Left": 0.38447171449661255, "Top": 0.7514253258705139}, "Polygon": [{"X": 0.38447171449661255, "Y": 0.7514253258705139}, {"X": 0.8819348216056824, "Y": 0.7514409422874451}, {"X": 0.8821828961372375, "Y": 0.8401750326156616}, {"X": 0.38451454043388367, "Y": 0.8401606678962708}]}, "Id": "652de9d1-e37d-4272-a93a-ff5d60ade568", "Relationships": [{"Type": "CHILD", "Ids": ["b28ab834-36a8-451f-8447-4e740671e4fa", "0ec66d52-e715-4c12-aadb-e85a2a1876b1", "48836474-5d8a-46d9-a7e7-3a2d332013cf", "2f0841d7-0168-4461-87e5-4602a3966b2a"]}]}, {"BlockType": "LINE", "Confidence": 28.599531173706055, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.07589344680309296, "Height": 0.04385434091091156, "Left": 0.4466918110847473, "Top": 0.8429462313652039}, "Polygon": [{"X": 0.4466918110847473, "Y": 0.8429462313652039}, {"X": 0.5225359797477722, "Y": 0.8429483771324158}, {"X": 0.5225852727890015, "Y": 0.8868005871772766}, {"X": 0.44672563672065735, "Y": 0.8867985010147095}]}, "Id": "939d4ed1-0285-45f1-8a5f-e839f1212d2d", "Relationships": [{"Type": "CHILD", "Ids": ["7c070013-b4a1-49e0-befd-95b83d2655c7"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1610337346792221, "Height": 0.031889840960502625, "Left": 0.624079167842865, "Top": 0.05689126253128052}, "Polygon": [{"X": 0.624079167842865, "Y": 0.05689126253128052}, {"X": 0.7850378751754761, "Y": 0.056899622082710266}, {"X": 0.7851129174232483, "Y": 0.08878110349178314}, {"X": 0.6241302490234375, "Y": 0.08877289295196533}]}, "Id": "7d427359-8340-4316-b869-cfe180cb3e8d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Slip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07535921037197113, "Height": 0.029208954423666, "Left": 0.8004135489463806, "Top": 0.06366751343011856}, "Polygon": [{"X": 0.8004135489463806, "Y": 0.06366751343011856}, {"X": 0.8756917119026184, "Y": 0.06367141008377075}, {"X": 0.8757727742195129, "Y": 0.09287646412849426}, {"X": 0.8004843592643738, "Y": 0.09287263453006744}]}, "Id": "8febac9d-215b-4444-a643-b81a516d427e"}, {"BlockType": "WORD", "Confidence": 96.75163269042969, "Text": "FAB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06482704728841782, "Height": 0.018232211470603943, "Left": 0.08424506336450577, "Top": 0.08659403771162033}, "Polygon": [{"X": 0.08426176011562347, "Y": 0.08659403771162033}, {"X": 0.14907211065292358, "Y": 0.08659735321998596}, {"X": 0.149060919880867, "Y": 0.10482624918222427}, {"X": 0.08424506336450577, "Y": 0.10482297837734222}]}, "Id": "634af63a-17ea-487e-ab53-b60c1a5ca1de"}, {"BlockType": "WORD", "Confidence": 99.61834716796875, "Text": "MASTERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17124201357364655, "Height": 0.02456130087375641, "Left": 0.1642051488161087, "Top": 0.08058955520391464}, "Polygon": [{"X": 0.1642184853553772, "Y": 0.08058955520391464}, {"X": 0.33544090390205383, "Y": 0.08059832453727722}, {"X": 0.33544716238975525, "Y": 0.10515085607767105}, {"X": 0.1642051488161087, "Y": 0.10514219850301743}]}, "Id": "a81f7602-9288-43b9-bf68-fae9d0d60dd0"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07441211491823196, "Height": 0.019579557701945305, "Left": 0.34732919931411743, "Top": 0.08064927905797958}, "Polygon": [{"X": 0.34732919931411743, "Y": 0.08064927905797958}, {"X": 0.4217284321784973, "Y": 0.08065309375524521}, {"X": 0.4217413067817688, "Y": 0.10022883862257004}, {"X": 0.3473352789878845, "Y": 0.100225068628788}]}, "Id": "c7048ad2-bc57-443f-963c-c55c99dad272"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "42015", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03739466518163681, "Height": 0.010262549854815006, "Left": 0.8332256078720093, "Top": 0.09814078360795975}, "Polygon": [{"X": 0.8332256078720093, "Y": 0.09814078360795975}, {"X": 0.8705920577049255, "Y": 0.09814267605543137}, {"X": 0.8706203103065491, "Y": 0.10840333253145218}, {"X": 0.8332520723342896, "Y": 0.10840144753456116}]}, "Id": "de103d54-0855-442e-a533-de9e37d14ea2"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05986490100622177, "Height": 0.008148731663823128, "Left": 0.0759674459695816, "Top": 0.16843514144420624}, "Polygon": [{"X": 0.0759752169251442, "Y": 0.16843514144420624}, {"X": 0.13583233952522278, "Y": 0.16843806207180023}, {"X": 0.13582684099674225, "Y": 0.17658387124538422}, {"X": 0.0759674459695816, "Y": 0.1765809804201126}]}, "Id": "c31e27b9-564f-4429-87e5-dfb0ecccc588"}, {"BlockType": "WORD", "Confidence": 99.65182495117188, "Text": "901", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021282993257045746, "Height": 0.008733869530260563, "Left": 0.2827449142932892, "Top": 0.16359812021255493}, "Polygon": [{"X": 0.2827449142932892, "Y": 0.16359812021255493}, {"X": 0.30402693152427673, "Y": 0.1635991632938385}, {"X": 0.30402788519859314, "Y": 0.17233198881149292}, {"X": 0.28274497389793396, "Y": 0.17233096063137054}]}, "Id": "f6733c3b-c3ca-4ed7-ba84-0ff80cfc382a"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03732231631875038, "Height": 0.009315690025687218, "Left": 0.3102016746997833, "Top": 0.16363970935344696}, "Polygon": [{"X": 0.3102016746997833, "Y": 0.16363970935344696}, {"X": 0.3475210964679718, "Y": 0.1636415272951126}, {"X": 0.3475239872932434, "Y": 0.17295539379119873}, {"X": 0.310202956199646, "Y": 0.17295359075069427}]}, "Id": "dd997f67-1cdc-4f08-be30-61558d6c01c6"}, {"BlockType": "WORD", "Confidence": 99.5533676147461, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013780208304524422, "Height": 0.009083053097128868, "Left": 0.351703017950058, "Top": 0.16409097611904144}, "Polygon": [{"X": 0.351703017950058, "Y": 0.16409097611904144}, {"X": 0.36547964811325073, "Y": 0.16409164667129517}, {"X": 0.36548322439193726, "Y": 0.17317402362823486}, {"X": 0.35170602798461914, "Y": 0.17317335307598114}]}, "Id": "cba7c0d4-855a-48e9-ade5-9e2f1cafa805"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030584251508116722, "Height": 0.010376869700849056, "Left": 0.5213417410850525, "Top": 0.16395577788352966}, "Polygon": [{"X": 0.5213417410850525, "Y": 0.16395577788352966}, {"X": 0.5519128441810608, "Y": 0.16395726799964905}, {"X": 0.5519260168075562, "Y": 0.1743326485157013}, {"X": 0.5213533639907837, "Y": 0.1743311583995819}]}, "Id": "7bd0fc28-f5db-4a96-b8e0-8544450a0a90"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020970845595002174, "Height": 0.008381696417927742, "Left": 0.5568208694458008, "Top": 0.16453105211257935}, "Polygon": [{"X": 0.5568208694458008, "Y": 0.16453105211257935}, {"X": 0.5777800679206848, "Y": 0.16453208029270172}, {"X": 0.577791690826416, "Y": 0.17291274666786194}, {"X": 0.5568316578865051, "Y": 0.17291173338890076}]}, "Id": "268e977e-93d3-4c75-90f1-7f9ba4be9e2e"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "RexCon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05133075267076492, "Height": 0.008685371838510036, "Left": 0.5840223431587219, "Top": 0.16499482095241547}, "Polygon": [{"X": 0.5840223431587219, "Y": 0.16499482095241547}, {"X": 0.6353387236595154, "Y": 0.16499732434749603}, {"X": 0.6353530883789062, "Y": 0.17368020117282867}, {"X": 0.5840346217155457, "Y": 0.1736777126789093}]}, "Id": "7d17c28b-3d0d-4f9c-a4f5-246c76f8f3fb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024058878421783447, "Height": 0.00841022189706564, "Left": 0.6408990621566772, "Top": 0.16569644212722778}, "Polygon": [{"X": 0.6408990621566772, "Y": 0.16569644212722778}, {"X": 0.6649428606033325, "Y": 0.1656976044178009}, {"X": 0.6649579405784607, "Y": 0.1741066575050354}, {"X": 0.640913188457489, "Y": 0.17410549521446228}]}, "Id": "7b1c7915-74a7-4f04-9b28-609370eeea2e"}, {"BlockType": "WORD", "Confidence": 98.9636459350586, "Text": "Janesville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07069333642721176, "Height": 0.01038779690861702, "Left": 0.2827231287956238, "Top": 0.1810404360294342}, "Polygon": [{"X": 0.2827231287956238, "Y": 0.1810404360294342}, {"X": 0.3534129559993744, "Y": 0.1810438483953476}, {"X": 0.35341647267341614, "Y": 0.19142822921276093}, {"X": 0.28272324800491333, "Y": 0.19142484664916992}]}, "Id": "598c0c37-26cd-41ff-9b78-3ec814f01acb"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017065314576029778, "Height": 0.008750119246542454, "Left": 0.35780608654022217, "Top": 0.18159624934196472}, "Polygon": [{"X": 0.35780608654022217, "Y": 0.18159624934196472}, {"X": 0.3748675584793091, "Y": 0.18159706890583038}, {"X": 0.3748714029788971, "Y": 0.190346360206604}, {"X": 0.3578092157840729, "Y": 0.19034554064273834}]}, "Id": "a37089b3-f1df-4442-bcf9-ed8067c6da3b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "53548", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03856329619884491, "Height": 0.008726758882403374, "Left": 0.383661150932312, "Top": 0.18160028755664825}, "Polygon": [{"X": 0.383661150932312, "Y": 0.18160028755664825}, {"X": 0.4222186803817749, "Y": 0.18160215020179749}, {"X": 0.4222244620323181, "Y": 0.19032704830169678}, {"X": 0.3836653530597687, "Y": 0.19032520055770874}]}, "Id": "39b97cf1-b537-4ad7-a302-bf21c8c21cc2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2841", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03377735987305641, "Height": 0.008396350778639317, "Left": 0.5824756622314453, "Top": 0.18228097259998322}, "Polygon": [{"X": 0.5824756622314453, "Y": 0.18228097259998322}, {"X": 0.6162399053573608, "Y": 0.18228259682655334}, {"X": 0.6162530183792114, "Y": 0.19067732989788055}, {"X": 0.5824874639511108, "Y": 0.19067570567131042}]}, "Id": "62e66752-fb40-4101-b57f-87b0fee90432"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05410090088844299, "Height": 0.010933879762887955, "Left": 0.6217163801193237, "Top": 0.18217380344867706}, "Polygon": [{"X": 0.6217163801193237, "Y": 0.18217380344867706}, {"X": 0.675797164440155, "Y": 0.182176411151886}, {"X": 0.6758173108100891, "Y": 0.19310767948627472}, {"X": 0.6217337846755981, "Y": 0.19310510158538818}]}, "Id": "3a18a802-798a-451b-9ce6-2d2ddd070aaa"}, {"BlockType": "WORD", "Confidence": 98.12738800048828, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02142469212412834, "Height": 0.00855549518018961, "Left": 0.6812109351158142, "Top": 0.1837177723646164}, "Polygon": [{"X": 0.6812109351158142, "Y": 0.1837177723646164}, {"X": 0.7026188373565674, "Y": 0.18371880054473877}, {"X": 0.702635645866394, "Y": 0.19227325916290283}, {"X": 0.681226909160614, "Y": 0.19227224588394165}]}, "Id": "e3f0fe5f-232d-41ad-a8b7-2682a2b0d1b1"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04643847048282623, "Height": 0.009586525149643421, "Left": 0.282889723777771, "Top": 0.19817394018173218}, "Polygon": [{"X": 0.282889723777771, "Y": 0.19817394018173218}, {"X": 0.32932600378990173, "Y": 0.19817616045475006}, {"X": 0.32932817935943604, "Y": 0.20776046812534332}, {"X": 0.28288981318473816, "Y": 0.20775826275348663}]}, "Id": "259d894e-8105-4afd-9120-6186a699f34a"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08593160659074783, "Height": 0.0089025953784585, "Left": 0.3344127833843231, "Top": 0.19896535575389862}, "Polygon": [{"X": 0.3344127833843231, "Y": 0.19896535575389862}, {"X": 0.42033860087394714, "Y": 0.19896945357322693}, {"X": 0.42034441232681274, "Y": 0.20786795020103455}, {"X": 0.3344150185585022, "Y": 0.20786386728286743}]}, "Id": "bb3e70b7-1dd1-4159-a651-1fa56c49ac92"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Burlington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07726425677537918, "Height": 0.010556908324360847, "Left": 0.5826622843742371, "Top": 0.1993805170059204}, "Polygon": [{"X": 0.5826622843742371, "Y": 0.1993805170059204}, {"X": 0.659907877445221, "Y": 0.1993841975927353}, {"X": 0.6599265336990356, "Y": 0.2099374234676361}, {"X": 0.5826771855354309, "Y": 0.20993375778198242}]}, "Id": "9c00a7ac-130c-4dce-9024-9841732ce0db"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020685406401753426, "Height": 0.008307846263051033, "Left": 0.6647068858146667, "Top": 0.20021580159664154}, "Polygon": [{"X": 0.6647068858146667, "Y": 0.20021580159664154}, {"X": 0.6853766441345215, "Y": 0.20021679997444153}, {"X": 0.6853923201560974, "Y": 0.20852366089820862}, {"X": 0.6647217869758606, "Y": 0.20852267742156982}]}, "Id": "7d0764fa-8738-4b28-be19-c0c61bb6dc46"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "53105", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0431038960814476, "Height": 0.008489375934004784, "Left": 0.6901123523712158, "Top": 0.20104306936264038}, "Polygon": [{"X": 0.6901123523712158, "Y": 0.20104306936264038}, {"X": 0.733198344707489, "Y": 0.20104512572288513}, {"X": 0.7332162261009216, "Y": 0.20953243970870972}, {"X": 0.6901285648345947, "Y": 0.20953039824962616}]}, "Id": "b3384382-6a7f-460f-9a7a-d290a557c24a"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Fax:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027408231049776077, "Height": 0.009034736081957817, "Left": 0.2828007638454437, "Top": 0.21583282947540283}, "Polygon": [{"X": 0.2828007638454437, "Y": 0.21583282947540283}, {"X": 0.3102077543735504, "Y": 0.2158341258764267}, {"X": 0.3102090060710907, "Y": 0.2248675674200058}, {"X": 0.2828008532524109, "Y": 0.22486628592014313}]}, "Id": "46b7aed7-bd6d-4565-8cb4-321ed2e4cb1b"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0877380296587944, "Height": 0.009104970842599869, "Left": 0.3154260814189911, "Top": 0.21640481054782867}, "Polygon": [{"X": 0.3154260814189911, "Y": 0.21640481054782867}, {"X": 0.40315890312194824, "Y": 0.21640895307064056}, {"X": 0.4031641185283661, "Y": 0.22550977766513824}, {"X": 0.3154275417327881, "Y": 0.22550566494464874}]}, "Id": "58ac3481-f5d0-4a97-80c4-d418f02751de"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030665764585137367, "Height": 0.010202412493526936, "Left": 0.07123711705207825, "Top": 0.25261059403419495}, "Polygon": [{"X": 0.07124707102775574, "Y": 0.25261059403419495}, {"X": 0.10190287977457047, "Y": 0.25261202454566956}, {"X": 0.10189437866210938, "Y": 0.262813001871109}, {"X": 0.07123711705207825, "Y": 0.2628116011619568}]}, "Id": "d59ae3f6-972b-44ff-837f-30e9e2ae5cb7"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0361129492521286, "Height": 0.007943755015730858, "Left": 0.10703787207603455, "Top": 0.25279098749160767}, "Polygon": [{"X": 0.10704430192708969, "Y": 0.25279098749160767}, {"X": 0.14315082132816315, "Y": 0.2527926564216614}, {"X": 0.14314572513103485, "Y": 0.2607347369194031}, {"X": 0.10703787207603455, "Y": 0.26073309779167175}]}, "Id": "e7cfae8c-d321-4f87-b74b-08491322abc0"}, {"BlockType": "WORD", "Confidence": 99.60538482666016, "Text": "9/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07968702167272568, "Height": 0.009645819664001465, "Left": 0.1489625722169876, "Top": 0.2519005239009857}, "Polygon": [{"X": 0.14896848797798157, "Y": 0.2519005239009857}, {"X": 0.2286495864391327, "Y": 0.2519041895866394}, {"X": 0.22864724695682526, "Y": 0.2615463435649872}, {"X": 0.1489625722169876, "Y": 0.2615426778793335}]}, "Id": "21d4b0ab-feff-4bb4-936c-e611b0d57370"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03723111003637314, "Height": 0.008271503262221813, "Left": 0.07057289779186249, "Top": 0.2701490819454193}, "Polygon": [{"X": 0.07058098912239075, "Y": 0.2701490819454193}, {"X": 0.10780400037765503, "Y": 0.2701507806777954}, {"X": 0.10779733955860138, "Y": 0.2784205973148346}, {"X": 0.07057289779186249, "Y": 0.2784188985824585}]}, "Id": "301ab9b4-4ce4-412c-af11-bf671283b67b"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Person:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051901549100875854, "Height": 0.007962396368384361, "Left": 0.11278021335601807, "Top": 0.26996999979019165}, "Polygon": [{"X": 0.1127864345908165, "Y": 0.26996999979019165}, {"X": 0.16468176245689392, "Y": 0.26997238397598267}, {"X": 0.1646774560213089, "Y": 0.27793240547180176}, {"X": 0.11278021335601807, "Y": 0.27793005108833313}]}, "Id": "78778a3f-514b-453a-90a2-a25e79062214"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02495482936501503, "Height": 0.008164663799107075, "Left": 0.17440925538539886, "Top": 0.26923537254333496}, "Polygon": [{"X": 0.1744132936000824, "Y": 0.26923537254333496}, {"X": 0.1993640810251236, "Y": 0.2692365050315857}, {"X": 0.19936099648475647, "Y": 0.27740004658699036}, {"X": 0.17440925538539886, "Y": 0.2773989140987396}]}, "Id": "677a6544-c14e-4670-9026-052768b77bb4"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021486306563019753, "Height": 0.008532991632819176, "Left": 0.5221142768859863, "Top": 0.26970770955085754}, "Polygon": [{"X": 0.5221142768859863, "Y": 0.26970770955085754}, {"X": 0.5435901284217834, "Y": 0.26970869302749634}, {"X": 0.5436006188392639, "Y": 0.27824071049690247}, {"X": 0.5221238732337952, "Y": 0.27823972702026367}]}, "Id": "1265ba33-2c4c-443c-afef-91eaec14a072"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02150598354637623, "Height": 0.008554386906325817, "Left": 0.5481189489364624, "Top": 0.27010345458984375}, "Polygon": [{"X": 0.5481189489364624, "Y": 0.27010345458984375}, {"X": 0.5696134567260742, "Y": 0.27010443806648254}, {"X": 0.5696249604225159, "Y": 0.27865785360336304}, {"X": 0.5481296181678772, "Y": 0.27865687012672424}]}, "Id": "a25be167-e860-4a20-868a-2533c8f8f99c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Astec", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03908542916178703, "Height": 0.008404731750488281, "Left": 0.5835865139961243, "Top": 0.2703850269317627}, "Polygon": [{"X": 0.5835865139961243, "Y": 0.2703850269317627}, {"X": 0.6226585507392883, "Y": 0.27038681507110596}, {"X": 0.6226719617843628, "Y": 0.278789758682251}, {"X": 0.5835983753204346, "Y": 0.2787880003452301}]}, "Id": "11317b93-79be-4c3d-8323-f7a75218d124"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0636889860033989, "Height": 0.008237187750637531, "Left": 0.07038415968418121, "Top": 0.2872428596019745}, "Polygon": [{"X": 0.07039222866296768, "Y": 0.2872428596019745}, {"X": 0.1340731531381607, "Y": 0.2872457206249237}, {"X": 0.13406752049922943, "Y": 0.29548004269599915}, {"X": 0.07038415968418121, "Y": 0.2954771816730499}]}, "Id": "51952e1f-e1e6-48b2-819e-babc5c51117c"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Order:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04417858645319939, "Height": 0.008257766254246235, "Left": 0.1387200504541397, "Top": 0.2863289713859558}, "Polygon": [{"X": 0.13872550427913666, "Y": 0.2863289713859558}, {"X": 0.1828986406326294, "Y": 0.2863309681415558}, {"X": 0.18289487063884735, "Y": 0.29458674788475037}, {"X": 0.1387200504541397, "Y": 0.2945847511291504}]}, "Id": "7ff420aa-15b3-40d8-ad8b-773c7a198d0b"}, {"BlockType": "WORD", "Confidence": 97.80432891845703, "Text": "PUR3165-105095", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12454897165298462, "Height": 0.008480510674417019, "Left": 0.18908239901065826, "Top": 0.28636065125465393}, "Polygon": [{"X": 0.18908601999282837, "Y": 0.28636065125465393}, {"X": 0.313630074262619, "Y": 0.2863662838935852}, {"X": 0.3136313855648041, "Y": 0.2948411703109741}, {"X": 0.18908239901065826, "Y": 0.29483556747436523}]}, "Id": "b466953a-9aa0-4690-9e36-b266b75ca1d9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06294430047273636, "Height": 0.01206358615309, "Left": 0.06857012957334518, "Top": 0.3029482364654541}, "Polygon": [{"X": 0.06858205050230026, "Y": 0.3029482364654541}, {"X": 0.13151443004608154, "Y": 0.30295103788375854}, {"X": 0.1315060555934906, "Y": 0.3150118291378021}, {"X": 0.06857012957334518, "Y": 0.3150090277194977}]}, "Id": "8b9f8297-77a7-4cb7-b06e-12bed38b90f6"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Via:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030406219884753227, "Height": 0.009464539587497711, "Left": 0.13653209805488586, "Top": 0.3026457726955414}, "Polygon": [{"X": 0.13653844594955444, "Y": 0.3026457726955414}, {"X": 0.16693830490112305, "Y": 0.3026471436023712}, {"X": 0.16693329811096191, "Y": 0.3121103346347809}, {"X": 0.13653209805488586, "Y": 0.31210896372795105}]}, "Id": "c1ff8472-b260-49d6-ae86-31c0cde4ec13"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "CPU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03251951187849045, "Height": 0.009604290127754211, "Left": 0.17236362397670746, "Top": 0.3027218282222748}, "Polygon": [{"X": 0.17236846685409546, "Y": 0.3027218282222748}, {"X": 0.2048831433057785, "Y": 0.3027232885360718}, {"X": 0.2048797458410263, "Y": 0.3123261332511902}, {"X": 0.17236362397670746, "Y": 0.3123247027397156}]}, "Id": "37ce6b65-343d-426c-8962-0dd6943ffaf8"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Part", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029891908168792725, "Height": 0.008723056875169277, "Left": 0.3334810137748718, "Top": 0.3830811679363251}, "Polygon": [{"X": 0.3334810137748718, "Y": 0.3830811679363251}, {"X": 0.36336958408355713, "Y": 0.38308241963386536}, {"X": 0.36337292194366455, "Y": 0.39180421829223633}, {"X": 0.33348315954208374, "Y": 0.39180296659469604}]}, "Id": "8f5f5da2-5508-4ff9-b9ef-26f8b7674c7f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05663171038031578, "Height": 0.008865848183631897, "Left": 0.3678984045982361, "Top": 0.38307619094848633}, "Polygon": [{"X": 0.3678984045982361, "Y": 0.38307619094848633}, {"X": 0.4245241582393646, "Y": 0.38307860493659973}, {"X": 0.42453011870384216, "Y": 0.3919420540332794}, {"X": 0.367902010679245, "Y": 0.3919396698474884}]}, "Id": "dc3fc3ae-02d2-44ed-817d-5b4e7ead0db1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026728976517915726, "Height": 0.008598661050200462, "Left": 0.5325307250022888, "Top": 0.3839450478553772}, "Polygon": [{"X": 0.5325307250022888, "Y": 0.3839450478553772}, {"X": 0.5592485070228577, "Y": 0.38394618034362793}, {"X": 0.5592596530914307, "Y": 0.3925437331199646}, {"X": 0.5325407981872559, "Y": 0.39254260063171387}]}, "Id": "064a5ce4-2d3f-4f53-bc99-e2ab50bbcbf4"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "Job#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03565026819705963, "Height": 0.00873421411961317, "Left": 0.5621223449707031, "Top": 0.3839573860168457}, "Polygon": [{"X": 0.5621223449707031, "Y": 0.3839573860168457}, {"X": 0.5977597236633301, "Y": 0.3839588761329651}, {"X": 0.5977725982666016, "Y": 0.39269158244132996}, {"X": 0.5621337890625, "Y": 0.39269009232521057}]}, "Id": "dc63fa30-28d1-4d43-844e-f0660257a2ab"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0409124530851841, "Height": 0.008860785514116287, "Left": 0.630760908126831, "Top": 0.38405361771583557}, "Polygon": [{"X": 0.630760908126831, "Y": 0.38405361771583557}, {"X": 0.6716572046279907, "Y": 0.38405534625053406}, {"X": 0.6716733574867249, "Y": 0.39291441440582275}, {"X": 0.6307753324508667, "Y": 0.39291268587112427}]}, "Id": "b451e04b-ce6f-46ed-9e87-e98c8f87d08e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06218637526035309, "Height": 0.010668408125638962, "Left": 0.6754670143127441, "Top": 0.3837102949619293}, "Polygon": [{"X": 0.6754670143127441, "Y": 0.3837102949619293}, {"X": 0.7376306653022766, "Y": 0.38371291756629944}, {"X": 0.737653374671936, "Y": 0.3943787217140198}, {"X": 0.6754866242408752, "Y": 0.39437609910964966}]}, "Id": "a75ab57a-be3c-47fd-8184-eb76b928f230"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03198744356632233, "Height": 0.010630444623529911, "Left": 0.7801206707954407, "Top": 0.38333800435066223}, "Polygon": [{"X": 0.7801206707954407, "Y": 0.38333800435066223}, {"X": 0.8120818138122559, "Y": 0.38333937525749207}, {"X": 0.8121080994606018, "Y": 0.39396846294403076}, {"X": 0.7801454067230225, "Y": 0.3939671218395233}]}, "Id": "1f368592-c03b-4276-9c55-382eb5ce8dc9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06351478397846222, "Height": 0.01044925395399332, "Left": 0.8160285949707031, "Top": 0.38296085596084595}, "Polygon": [{"X": 0.8160285949707031, "Y": 0.38296085596084595}, {"X": 0.879514217376709, "Y": 0.38296353816986084}, {"X": 0.8795433640480042, "Y": 0.3934101164340973}, {"X": 0.81605464220047, "Y": 0.3934074342250824}]}, "Id": "8079929e-9157-4871-8853-2618f3b8fccd"}, {"BlockType": "WORD", "Confidence": 98.26211547851562, "Text": "544-15247-81,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10769312083721161, "Height": 0.011041541583836079, "Left": 0.0695548951625824, "Top": 0.4055503010749817}, "Polygon": [{"X": 0.06956575065851212, "Y": 0.4055503010749817}, {"X": 0.177248015999794, "Y": 0.40555477142333984}, {"X": 0.1772426962852478, "Y": 0.4165918529033661}, {"X": 0.0695548951625824, "Y": 0.41658738255500793}]}, "Id": "e634adff-13a2-4ca6-8c99-7b3e43c0b214"}, {"BlockType": "WORD", "Confidence": 95.75115203857422, "Text": "RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01942882500588894, "Height": 0.009402110241353512, "Left": 0.17805327475070953, "Top": 0.4054223597049713}, "Polygon": [{"X": 0.17805776000022888, "Y": 0.4054223597049713}, {"X": 0.19748209416866302, "Y": 0.4054231643676758}, {"X": 0.19747845828533173, "Y": 0.4148244559764862}, {"X": 0.17805327475070953, "Y": 0.41482365131378174}]}, "Id": "e07b5d3d-15da-4172-b3c6-5208f7db5adc"}, {"BlockType": "WORD", "Confidence": 74.8702621459961, "Text": "R00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027300851419568062, "Height": 0.009539958089590073, "Left": 0.2026197612285614, "Top": 0.40523386001586914}, "Polygon": [{"X": 0.20262323319911957, "Y": 0.40523386001586914}, {"X": 0.2299206256866455, "Y": 0.4052349925041199}, {"X": 0.22991836071014404, "Y": 0.4147738218307495}, {"X": 0.2026197612285614, "Y": 0.4147726893424988}]}, "Id": "46d48a6a-90cc-41cd-92ce-da23b5caada9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "41932", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04479703679680824, "Height": 0.009580817073583603, "Left": 0.534371554851532, "Top": 0.4073173999786377}, "Polygon": [{"X": 0.534371554851532, "Y": 0.4073173999786377}, {"X": 0.5791552662849426, "Y": 0.4073192775249481}, {"X": 0.5791685581207275, "Y": 0.4168982207775116}, {"X": 0.5343828797340393, "Y": 0.41689637303352356}]}, "Id": "17b35d8b-a72a-4d28-ab71-625e819f0d9e"}, {"BlockType": "WORD", "Confidence": 99.17889404296875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0071354815736413, "Height": 0.008984205313026905, "Left": 0.7451134920120239, "Top": 0.40707215666770935}, "Polygon": [{"X": 0.7451134920120239, "Y": 0.40707215666770935}, {"X": 0.7522292733192444, "Y": 0.4070724546909332}, {"X": 0.752249002456665, "Y": 0.416056364774704}, {"X": 0.7451329231262207, "Y": 0.4160560667514801}]}, "Id": "ac47cb58-fb50-4385-88e0-376641e71412"}, {"BlockType": "WORD", "Confidence": 98.52379608154297, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006607647519558668, "Height": 0.009356127120554447, "Left": 0.8926771283149719, "Top": 0.40589457750320435}, "Polygon": [{"X": 0.8926771283149719, "Y": 0.40589457750320435}, {"X": 0.8992578387260437, "Y": 0.4058948755264282}, {"X": 0.8992847800254822, "Y": 0.4152507185935974}, {"X": 0.8927038311958313, "Y": 0.4152504503726959}]}, "Id": "8bdde8fa-fb8a-4d25-b88e-ce83d132f4c9"}, {"BlockType": "WORD", "Confidence": 99.36941528320312, "Text": "502-09283-80,RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12873230874538422, "Height": 0.011473235674202442, "Left": 0.06798099726438522, "Top": 0.4288734197616577}, "Polygon": [{"X": 0.06799235194921494, "Y": 0.4288734197616577}, {"X": 0.19671331346035004, "Y": 0.42887866497039795}, {"X": 0.1967088282108307, "Y": 0.4403466582298279}, {"X": 0.06798099726438522, "Y": 0.44034141302108765}]}, "Id": "5182b95a-cca9-4822-bf0c-1596faada994"}, {"BlockType": "WORD", "Confidence": 99.38317108154297, "Text": "R02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027472157031297684, "Height": 0.009404157288372517, "Left": 0.20192715525627136, "Top": 0.42909854650497437}, "Polygon": [{"X": 0.20193059742450714, "Y": 0.42909854650497437}, {"X": 0.22939930856227875, "Y": 0.4290996789932251}, {"X": 0.22939705848693848, "Y": 0.4385027289390564}, {"X": 0.20192715525627136, "Y": 0.43850159645080566}]}, "Id": "56d32cdb-1ad7-4848-90fc-43330c91d322"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "41936", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045039404183626175, "Height": 0.009109150618314743, "Left": 0.5341386795043945, "Top": 0.43116968870162964}, "Polygon": [{"X": 0.5341386795043945, "Y": 0.43116968870162964}, {"X": 0.5791653990745544, "Y": 0.4311715364456177}, {"X": 0.5791780948638916, "Y": 0.4402788579463959}, {"X": 0.5341494083404541, "Y": 0.44027701020240784}]}, "Id": "a076be92-3369-4cd2-9e4b-5cc958016b94"}, {"BlockType": "WORD", "Confidence": 98.93475341796875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0071219890378415585, "Height": 0.00903438962996006, "Left": 0.7452986240386963, "Top": 0.4306319057941437}, "Polygon": [{"X": 0.7452986240386963, "Y": 0.4306319057941437}, {"X": 0.7524007558822632, "Y": 0.43063220381736755}, {"X": 0.7524206042289734, "Y": 0.4396663010120392}, {"X": 0.7453181743621826, "Y": 0.4396660029888153}]}, "Id": "70cb5935-7a76-49cc-9a96-1b777576b1e8"}, {"BlockType": "WORD", "Confidence": 98.91361999511719, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0067777689546346664, "Height": 0.009319725446403027, "Left": 0.8928496837615967, "Top": 0.4296402037143707}, "Polygon": [{"X": 0.8928496837615967, "Y": 0.4296402037143707}, {"X": 0.8996005654335022, "Y": 0.4296404719352722}, {"X": 0.8996274471282959, "Y": 0.43895992636680603}, {"X": 0.8928762674331665, "Y": 0.43895965814590454}]}, "Id": "effd7ae4-c6b5-4bbe-8118-04174adb81db"}, {"BlockType": "WORD", "Confidence": 99.29607391357422, "Text": "524-14659-86,RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12959308922290802, "Height": 0.011690132319927216, "Left": 0.06577382236719131, "Top": 0.452280193567276}, "Polygon": [{"X": 0.06578551232814789, "Y": 0.452280193567276}, {"X": 0.19536691904067993, "Y": 0.45228540897369385}, {"X": 0.19536226987838745, "Y": 0.4639703333377838}, {"X": 0.06577382236719131, "Y": 0.46396514773368835}]}, "Id": "e734b756-2b48-4574-95ba-22d8b34bdf1a"}, {"BlockType": "WORD", "Confidence": 98.06660461425781, "Text": "R09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027574447914958, "Height": 0.009626601822674274, "Left": 0.20150503516197205, "Top": 0.45258697867393494}, "Polygon": [{"X": 0.20150859653949738, "Y": 0.45258697867393494}, {"X": 0.2290794849395752, "Y": 0.45258811116218567}, {"X": 0.22907717525959015, "Y": 0.4622136056423187}, {"X": 0.20150503516197205, "Y": 0.4622125029563904}]}, "Id": "80bf37b5-59a5-4714-9ac5-d4189ca601cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "41939", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04513927549123764, "Height": 0.009279871359467506, "Left": 0.534461498260498, "Top": 0.454617977142334}, "Polygon": [{"X": 0.534461498260498, "Y": 0.454617977142334}, {"X": 0.5795878767967224, "Y": 0.45461979508399963}, {"X": 0.5796008110046387, "Y": 0.46389785408973694}, {"X": 0.5344724655151367, "Y": 0.4638960659503937}]}, "Id": "9256bdfd-4319-4490-b00d-5cd4eb45c192"}, {"BlockType": "WORD", "Confidence": 99.35486602783203, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008686013519763947, "Height": 0.009210746735334396, "Left": 0.7450685501098633, "Top": 0.4539314806461334}, "Polygon": [{"X": 0.7450685501098633, "Y": 0.4539314806461334}, {"X": 0.753734290599823, "Y": 0.4539318382740021}, {"X": 0.7537546157836914, "Y": 0.4631422162055969}, {"X": 0.745088517665863, "Y": 0.46314188838005066}]}, "Id": "e344ffa7-b6af-4a1b-9d05-5cf00cc545d1"}, {"BlockType": "WORD", "Confidence": 98.48114776611328, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008101689629256725, "Height": 0.009276757016777992, "Left": 0.893018901348114, "Top": 0.453212708234787}, "Polygon": [{"X": 0.893018901348114, "Y": 0.453212708234787}, {"X": 0.9010937809944153, "Y": 0.45321303606033325}, {"X": 0.9011206030845642, "Y": 0.46248945593833923}, {"X": 0.8930453062057495, "Y": 0.46248912811279297}]}, "Id": "41d9724d-dac6-4e0e-a309-13838e2504dc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06305529177188873, "Height": 0.01238721888512373, "Left": 0.5359132885932922, "Top": 0.6444990634918213}, "Polygon": [{"X": 0.5359132885932922, "Y": 0.6444990634918213}, {"X": 0.5989502668380737, "Y": 0.6445012092590332}, {"X": 0.5989685654640198, "Y": 0.6568862795829773}, {"X": 0.5359280109405518, "Y": 0.6568840742111206}]}, "Id": "218369ac-aff4-4460-a8c4-eb6b3a5925cf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04552079737186432, "Height": 0.009537101723253727, "Left": 0.6686421632766724, "Top": 0.6447721123695374}, "Polygon": [{"X": 0.6686421632766724, "Y": 0.6447721123695374}, {"X": 0.7141437530517578, "Y": 0.6447737216949463}, {"X": 0.7141629457473755, "Y": 0.6543092131614685}, {"X": 0.6686593890190125, "Y": 0.6543076634407043}]}, "Id": "166643f2-56db-4c15-bfb6-d670f884d0ec"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07158830016851425, "Height": 0.011886224150657654, "Left": 0.8076614141464233, "Top": 0.645178496837616}, "Polygon": [{"X": 0.8076614141464233, "Y": 0.645178496837616}, {"X": 0.8792166113853455, "Y": 0.6451810002326965}, {"X": 0.8792496919631958, "Y": 0.6570647358894348}, {"X": 0.8076905608177185, "Y": 0.657062292098999}]}, "Id": "5a02e46c-ffc9-45b5-8a5f-9ed1017f127e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07445713877677917, "Height": 0.012492098845541477, "Left": 0.055859845131635666, "Top": 0.6657568216323853}, "Polygon": [{"X": 0.055872902274131775, "Y": 0.6657568216323853}, {"X": 0.13031698763370514, "Y": 0.6657593250274658}, {"X": 0.13030825555324554, "Y": 0.678248941898346}, {"X": 0.055859845131635666, "Y": 0.6782464385032654}]}, "Id": "6053d819-e0b5-4f9e-97ef-618005453886"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "visually", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05700886249542236, "Height": 0.012061936780810356, "Left": 0.13550104200839996, "Top": 0.6661468744277954}, "Polygon": [{"X": 0.135509192943573, "Y": 0.6661468744277954}, {"X": 0.19250990450382233, "Y": 0.666148841381073}, {"X": 0.19250495731830597, "Y": 0.6782088279724121}, {"X": 0.13550104200839996, "Y": 0.6782069206237793}]}, "Id": "526bf9fa-7ff9-4a4d-8747-1d038a916f3b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "checked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0631863921880722, "Height": 0.010398434475064278, "Left": 0.05384090542793274, "Top": 0.6814243793487549}, "Polygon": [{"X": 0.05385187268257141, "Y": 0.6814243793487549}, {"X": 0.11702729761600494, "Y": 0.681426465511322}, {"X": 0.11701938509941101, "Y": 0.6918228268623352}, {"X": 0.05384090542793274, "Y": 0.6918206810951233}]}, "Id": "3fb74e34-0853-4b17-ae64-072c9a0efb74"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02196030505001545, "Height": 0.010014278814196587, "Left": 0.12271260470151901, "Top": 0.6819454431533813}, "Polygon": [{"X": 0.12271995842456818, "Y": 0.6819454431533813}, {"X": 0.1446729153394699, "Y": 0.6819462180137634}, {"X": 0.14466658234596252, "Y": 0.6919597387313843}, {"X": 0.12271260470151901, "Y": 0.691959023475647}]}, "Id": "99aaba74-bd6f-47a0-baca-36f5603f256f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060841258615255356, "Height": 0.012251201085746288, "Left": 0.14951713383197784, "Top": 0.6818654537200928}, "Polygon": [{"X": 0.14952461421489716, "Y": 0.6818654537200928}, {"X": 0.2103583961725235, "Y": 0.6818674802780151}, {"X": 0.21035438776016235, "Y": 0.6941166520118713}, {"X": 0.14951713383197784, "Y": 0.694114625453949}]}, "Id": "a8569185-7cb3-47e6-9b9c-f934ff39a08b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11571912467479706, "Height": 0.018581002950668335, "Left": 0.3844916820526123, "Top": 0.7927913665771484}, "Polygon": [{"X": 0.3844916820526123, "Y": 0.7927913665771484}, {"X": 0.500191867351532, "Y": 0.7927948236465454}, {"X": 0.5002108216285706, "Y": 0.8113723397254944}, {"X": 0.384500652551651, "Y": 0.8113689422607422}]}, "Id": "b28ab834-36a8-451f-8447-4e740671e4fa"}, {"BlockType": "WORD", "Confidence": 99.69486999511719, "Text": "by:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03837142884731293, "Height": 0.022242346778512, "Left": 0.513337254524231, "Top": 0.7931475639343262}, "Polygon": [{"X": 0.513337254524231, "Y": 0.7931475639343262}, {"X": 0.5516806840896606, "Y": 0.7931487560272217}, {"X": 0.5517086982727051, "Y": 0.8153899312019348}, {"X": 0.5133613348007202, "Y": 0.8153887987136841}]}, "Id": "0ec66d52-e715-4c12-aadb-e85a2a1876b1"}, {"BlockType": "WORD", "Confidence": 95.82968139648438, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16513924300670624, "Height": 0.05816025659441948, "Left": 0.5630221962928772, "Top": 0.7514309287071228}, "Polygon": [{"X": 0.5630221962928772, "Y": 0.7514309287071228}, {"X": 0.7280404567718506, "Y": 0.7514361143112183}, {"X": 0.7281614542007446, "Y": 0.8095912337303162}, {"X": 0.5630985498428345, "Y": 0.8095862865447998}]}, "Id": "48836474-5d8a-46d9-a7e7-3a2d332013cf"}, {"BlockType": "WORD", "Confidence": 92.14126586914062, "Text": "parker", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14900311827659607, "Height": 0.05829668417572975, "Left": 0.7331798076629639, "Top": 0.7818783521652222}, "Polygon": [{"X": 0.7331798076629639, "Y": 0.7818783521652222}, {"X": 0.8820199370384216, "Y": 0.7818828821182251}, {"X": 0.8821828961372375, "Y": 0.8401750326156616}, {"X": 0.7333024144172668, "Y": 0.8401707410812378}]}, "Id": "2f0841d7-0168-4461-87e5-4602a3966b2a"}, {"BlockType": "WORD", "Confidence": 28.599531173706055, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07589344680309296, "Height": 0.04385434091091156, "Left": 0.4466918110847473, "Top": 0.8429462313652039}, "Polygon": [{"X": 0.4466918110847473, "Y": 0.8429462313652039}, {"X": 0.5225359797477722, "Y": 0.8429483771324158}, {"X": 0.5225852727890015, "Y": 0.8868005871772766}, {"X": 0.44672563672065735, "Y": 0.8867985010147095}]}, "Id": "7c070013-b4a1-49e0-befd-95b83d2655c7"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1aed383d-9964-4437-8a78-47522d1d2d5c", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1aed383d-9964-4437-8a78-47522d1d2d5c", "content-type": "application/x-amz-json-1.1", "content-length": "62177", "date": "Wed, 24 Sep 2025 10:52:37 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "f1750285-820e-4caa-b49c-5cb288553353", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 10:52:39 GMT", "content-type": "application/json", "content-length": "969", "connection": "keep-alive", "x-amzn-requestid": "f1750285-820e-4caa-b49c-5cb288553353"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user provided a single page that appears to be a packing slip. It's a \"Packing Slip\" containing items, shipping details, etc. The doc type: pack_list? The definition: packing list. Indeed the keywords: \"Packing Slip\", \"Ship Date\", \"Purchase Order\", etc. The pack_list is the proper type. The page is a single page. So we return an array with one object: page_no: 1, doc_type: \"pack_list\".\n\nWe must use the tool classify_logistics_doc_type. The tool expects an argument: documents: array of objects with page_no and doc_type. Yes. We'll call the tool."}}}, {"toolUse": {"toolUseId": "tooluse_uz9QrrePRvqAbMDT7usajA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2662, "outputTokens": 151, "totalTokens": 2813}, "metrics": {"latencyMs": 799}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/15bad1bd_KUYJRWRDFCC2FB7SKI9T.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nPacking Slip\nFAB MASTERS, INC.\n42015\nAddress:\n901 Joliet St\nShip To: RexCon LLC\nJanesville, WI 53548\n2841 Whiting Rd.\nPhone: ************\nBurlington, WI 53105\nFax: ************\nShip Date: 9/19/2025\nSales Person: Zac\nBill To: Astec\nPurchase Order: PUR3165-105095\nShipped Via: CPU\nPart Number\nOur Job#\nOrder Quantity\nShip Quantity\n544-15247-81, RV R00\n41932\n1\n1\n502-09283-80,RV R02\n41936\n1\n1\n524-14659-86,RV R09\n41939\n2\n2\nShipping\nDriver\nInspector\nShipment visually\nchecked for damage\nReceived by: <PERSON>\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}