2025-09-24 18:13:54,219 - INFO - Logging initialized. Log file: logs/test_classification_20250924_181354.log
2025-09-24 18:13:54,219 - INFO - 📁 Found 13 files to process
2025-09-24 18:13:54,219 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:13:54,219 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 18:13:54,219 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 18:13:54,219 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 18:13:54,219 - INFO - ⬆️ [18:13:54] Uploading: A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:13:58,654 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/A8UNJQR3VGLF8G4MC11J.pdf -> s3://document-extraction-logistically/temp/d2239549_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:13:58,654 - INFO - 🔍 [18:13:58] Starting classification: A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:13:58,655 - INFO - ⬆️ [18:13:58] Uploading: AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:13:58,657 - INFO - Initializing TextractProcessor...
2025-09-24 18:13:58,677 - INFO - Initializing BedrockProcessor...
2025-09-24 18:13:58,684 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d2239549_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:13:58,684 - INFO - Processing PDF from S3...
2025-09-24 18:13:58,684 - INFO - Downloading PDF from S3 to /tmp/tmpvznfjt0p/d2239549_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:13:59,600 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/AK2S46MQ9UJW5Q43TRRV.pdf -> s3://document-extraction-logistically/temp/0c7d308c_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:13:59,600 - INFO - 🔍 [18:13:59] Starting classification: AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:13:59,601 - INFO - ⬆️ [18:13:59] Uploading: EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:13:59,601 - INFO - Initializing TextractProcessor...
2025-09-24 18:13:59,616 - INFO - Initializing BedrockProcessor...
2025-09-24 18:13:59,621 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0c7d308c_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:13:59,621 - INFO - Processing PDF from S3...
2025-09-24 18:13:59,621 - INFO - Downloading PDF from S3 to /tmp/tmp206u2v5l/0c7d308c_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:14:00,234 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/EAMCIVO8E5KY4U6ZYWK2.jpg -> s3://document-extraction-logistically/temp/e1de4f45_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:14:00,234 - INFO - 🔍 [18:14:00] Starting classification: EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:14:00,234 - INFO - ⬆️ [18:14:00] Uploading: F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:00,235 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:00,251 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:00,254 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e1de4f45_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:14:00,254 - INFO - Processing image from S3...
2025-09-24 18:14:01,091 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 18:14:01,091 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:01,092 - INFO - Splitting PDF d2239549_A8UNJQR3VGLF8G4MC11J into 1 pages
2025-09-24 18:14:01,094 - INFO - Split PDF into 1 pages
2025-09-24 18:14:01,094 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:01,095 - INFO - Expected pages: [1]
2025-09-24 18:14:01,214 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/F4RZ1UDKLCELJG8T6R9G.png -> s3://document-extraction-logistically/temp/44051483_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:01,215 - INFO - 🔍 [18:14:01] Starting classification: F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:01,215 - INFO - ⬆️ [18:14:01] Uploading: KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:01,216 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:01,236 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:01,241 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/44051483_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:01,242 - INFO - Processing image from S3...
2025-09-24 18:14:01,634 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:14:01,634 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:01,635 - INFO - Splitting PDF 0c7d308c_AK2S46MQ9UJW5Q43TRRV into 1 pages
2025-09-24 18:14:01,659 - INFO - Split PDF into 1 pages
2025-09-24 18:14:01,659 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:01,659 - INFO - Expected pages: [1]
2025-09-24 18:14:02,368 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/KBFTEJIYFMKC7ZILXM5M.png -> s3://document-extraction-logistically/temp/d89e70ee_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:02,369 - INFO - 🔍 [18:14:02] Starting classification: KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:02,370 - INFO - ⬆️ [18:14:02] Uploading: LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:02,371 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:02,388 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:02,391 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d89e70ee_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:02,391 - INFO - Processing image from S3...
2025-09-24 18:14:02,960 - INFO - S3 Image temp/e1de4f45_EAMCIVO8E5KY4U6ZYWK2.jpg: Extracted 386 characters, 34 lines
2025-09-24 18:14:02,961 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:02,961 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:05,326 - INFO - S3 Image temp/d89e70ee_KBFTEJIYFMKC7ZILXM5M.png: Extracted 661 characters, 29 lines
2025-09-24 18:14:05,326 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:05,326 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:05,574 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0962c0f3-e52e-48c2-86ac-c45b9f6bafc2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:05 GMT', 'content-type': 'application/json', 'content-length': '975', 'connection': 'keep-alive', 'x-amzn-requestid': '0962c0f3-e52e-48c2-86ac-c45b9f6bafc2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We must output via tool classify_logistics_doc_type with one object: page_no 1, doc_type determined.\n\nWe have a document with "Trip Sheet", "Trip Ticket", includes weights, driver, trailer, BOL, etc. This looks like a scale ticket or trip sheet. Keywords: Gross, Tare, Net, Date In/Out, driver, trailer. That matches scale_ticket definition. Also Trip Ticket maybe a type of scale ticket. The presence of weight details: Gross, Tare, Net, weight in lbs. So doc_type = scale_ticket.\n\nThus produce JSON array with one object: page_no:1, doc_type:"scale_ticket".\n\nWe need to use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2693, 'outputTokens': 172, 'totalTokens': 2865}, 'metrics': {'latencyMs': 1453}}
2025-09-24 18:14:05,574 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e1de4f45_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:14:05,934 - INFO - Page 1: Extracted 373 characters, 33 lines from d2239549_A8UNJQR3VGLF8G4MC11J_3cd307cb_page_001.pdf
2025-09-24 18:14:05,935 - INFO - Successfully processed page 1
2025-09-24 18:14:05,935 - INFO - Combined 1 pages into final text
2025-09-24 18:14:05,935 - INFO - Text validation for d2239549_A8UNJQR3VGLF8G4MC11J: 390 characters, 1 pages
2025-09-24 18:14:05,936 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:05,936 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:06,027 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf -> s3://document-extraction-logistically/temp/4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:06,028 - INFO - 🔍 [18:14:06] Starting classification: LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:06,029 - INFO - ⬆️ [18:14:06] Uploading: MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:06,031 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:06,092 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:06,094 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:06,095 - INFO - Processing PDF from S3...
2025-09-24 18:14:06,095 - INFO - Downloading PDF from S3 to /tmp/tmp6jgvdvce/4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:06,993 - INFO - Page 1: Extracted 279 characters, 22 lines from 0c7d308c_AK2S46MQ9UJW5Q43TRRV_134b3ee3_page_001.pdf
2025-09-24 18:14:06,993 - INFO - Successfully processed page 1
2025-09-24 18:14:06,994 - INFO - Combined 1 pages into final text
2025-09-24 18:14:06,994 - INFO - Text validation for 0c7d308c_AK2S46MQ9UJW5Q43TRRV: 296 characters, 1 pages
2025-09-24 18:14:06,994 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:06,995 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:07,342 - INFO - S3 Image temp/44051483_F4RZ1UDKLCELJG8T6R9G.png: Extracted 648 characters, 28 lines
2025-09-24 18:14:07,342 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:07,343 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:07,843 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '797048ae-3561-4d98-a2f5-2caddac654e1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:07 GMT', 'content-type': 'application/json', 'content-length': '1024', 'connection': 'keep-alive', 'x-amzn-requestid': '797048ae-3561-4d98-a2f5-2caddac654e1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The content looks like a scale ticket: Ticket number, Date, Time, Customer, Truck, Trailer, Destination, P.O., D.O., Seal, comment, Grown, Tare, Net, Total, Materials & Services, etc. Contains Gross, Tare, Net? Actually shows Grown 75200 L Out Scale 3, Tare 32100 LIn Scale 3, Net 431001. Total 21.55. So yes weight-related keywords. So should be scale_ticket.\n\nCheck if any other doc type? Could also be "combined_carrier_documents" if multiple? But seems just one. So classify as "scale_ticket".\n\nWe must output tool call classify_logistics_doc_type with array of objects: each object with page_no and doc_type. Only one page: page_no 1. So output accordingly.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2763, 'outputTokens': 198, 'totalTokens': 2961}, 'metrics': {'latencyMs': 1273}}
2025-09-24 18:14:07,844 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d89e70ee_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:07,896 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '62c43aa7-16f4-4e1b-bfc6-b6dd87bea11f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:07 GMT', 'content-type': 'application/json', 'content-length': '681', 'connection': 'keep-alive', 'x-amzn-requestid': '62c43aa7-16f4-4e1b-bfc6-b6dd87bea11f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to inspect content. It is a scale ticket: "ELECTRONIC SCALE TICKET" heading, contains gross, tare, net, truck, trailer, etc. So doc_type: scale_ticket. The page has no continuation indicator. So output: array with one object page_no:1 doc_type:"scale_ticket". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2697, 'outputTokens': 105, 'totalTokens': 2802}, 'metrics': {'latencyMs': 781}}
2025-09-24 18:14:07,897 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d2239549_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:14:08,515 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/MZ3X0YRE37Y7NHS45V2U.pdf -> s3://document-extraction-logistically/temp/bb91bd70_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:08,516 - INFO - 🔍 [18:14:08] Starting classification: MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:08,517 - INFO - ⬆️ [18:14:08] Uploading: NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:08,518 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:08,543 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:08,546 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bb91bd70_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:08,547 - INFO - Processing PDF from S3...
2025-09-24 18:14:08,547 - INFO - Downloading PDF from S3 to /tmp/tmpwxib9tvi/bb91bd70_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:09,126 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '713bfc87-fbee-43dc-99f1-e30661d007d5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:08 GMT', 'content-type': 'application/json', 'content-length': '809', 'connection': 'keep-alive', 'x-amzn-requestid': '713bfc87-fbee-43dc-99f1-e30661d007d5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Text indicates "WEIGHED ON A FAIRBANKS SCALE", "WEIGHT" terms like "WEIGHED", "NETES"? There\'s "LOOP ID", "OUTBOUND DATE", "INBOUND". This looks like a scale ticket. Keywords: Gross, Tare, Net, Weighed At, etc. The header includes "WEIGHED ON A FAIRBANKS SCALE". So doc_type likely "scale_ticket". We need to output via classify_logistics_doc_type tool with array containing page_no=1 and doc_type="scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2643, 'outputTokens': 142, 'totalTokens': 2785}, 'metrics': {'latencyMs': 943}}
2025-09-24 18:14:09,126 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0c7d308c_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:14:09,201 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/NLUB3HCM1QWW6FXJBEY3.pdf -> s3://document-extraction-logistically/temp/837ba135_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:09,202 - INFO - 🔍 [18:14:09] Starting classification: NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:09,203 - INFO - ⬆️ [18:14:09] Uploading: OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:09,206 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:09,225 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:09,229 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/837ba135_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:09,229 - INFO - Processing PDF from S3...
2025-09-24 18:14:09,229 - INFO - Downloading PDF from S3 to /tmp/tmpgqn4wg6b/837ba135_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:09,349 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '237dc6d7-a468-4f66-92d2-1ef0a14666c0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:09 GMT', 'content-type': 'application/json', 'content-length': '558', 'connection': 'keep-alive', 'x-amzn-requestid': '237dc6d7-a468-4f66-92d2-1ef0a14666c0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': "We have one page. It's a scale ticket: contains Gross, Tare, Net, weight, etc. So doc_type should be scale_ticket. No continuation. So output JSON array with one object for page1. Use tool classify_logistics_doc_type with documents array."}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2765, 'outputTokens': 73, 'totalTokens': 2838}, 'metrics': {'latencyMs': 831}}
2025-09-24 18:14:09,349 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/44051483_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:09,862 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/OPDBRSVH28PEQZSD5FO9.pdf -> s3://document-extraction-logistically/temp/292b24d3_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:09,863 - INFO - 🔍 [18:14:09] Starting classification: OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:09,863 - INFO - ⬆️ [18:14:09] Uploading: Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:09,865 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:09,880 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:09,886 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/292b24d3_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:09,886 - INFO - Processing PDF from S3...
2025-09-24 18:14:09,886 - INFO - Downloading PDF from S3 to /tmp/tmpg15nl4ap/292b24d3_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:10,512 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Y2EQEYPYUUYHAD9BZKXO.jpg -> s3://document-extraction-logistically/temp/db1176d4_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:10,512 - INFO - 🔍 [18:14:10] Starting classification: Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:10,514 - INFO - ⬆️ [18:14:10] Uploading: Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:10,514 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:10,537 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:10,541 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/db1176d4_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:10,541 - INFO - Processing image from S3...
2025-09-24 18:14:11,148 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Z1N2YIEHAA2FERWDQ38E.pdf -> s3://document-extraction-logistically/temp/d7adaddc_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:11,148 - INFO - 🔍 [18:14:11] Starting classification: Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:11,149 - INFO - ⬆️ [18:14:11] Uploading: ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:11,150 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:11,171 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:11,175 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d7adaddc_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:11,175 - INFO - Processing PDF from S3...
2025-09-24 18:14:11,175 - INFO - Downloading PDF from S3 to /tmp/tmpgh_4qjlp/d7adaddc_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:11,336 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:14:11,336 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:11,337 - INFO - Splitting PDF 837ba135_NLUB3HCM1QWW6FXJBEY3 into 1 pages
2025-09-24 18:14:11,339 - INFO - Split PDF into 1 pages
2025-09-24 18:14:11,339 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:11,339 - INFO - Expected pages: [1]
2025-09-24 18:14:11,349 - INFO - Downloaded PDF size: 2.0 MB
2025-09-24 18:14:11,349 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:11,350 - INFO - Splitting PDF bb91bd70_MZ3X0YRE37Y7NHS45V2U into 1 pages
2025-09-24 18:14:11,355 - INFO - Split PDF into 1 pages
2025-09-24 18:14:11,356 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:11,356 - INFO - Expected pages: [1]
2025-09-24 18:14:11,824 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZA6FSHKD588WC7VTH3MO.pdf -> s3://document-extraction-logistically/temp/389e8e6c_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:11,824 - INFO - 🔍 [18:14:11] Starting classification: ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:11,825 - INFO - ⬆️ [18:14:11] Uploading: ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:11,830 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:11,839 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:11,840 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/389e8e6c_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:11,841 - INFO - Processing PDF from S3...
2025-09-24 18:14:11,841 - INFO - Downloading PDF from S3 to /tmp/tmpes6tup3j/389e8e6c_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:11,886 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:14:11,887 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:11,889 - INFO - Splitting PDF 292b24d3_OPDBRSVH28PEQZSD5FO9 into 1 pages
2025-09-24 18:14:11,891 - INFO - Split PDF into 1 pages
2025-09-24 18:14:11,891 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:11,891 - INFO - Expected pages: [1]
2025-09-24 18:14:12,560 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZY3XO73EAB4BC4957F0V.pdf -> s3://document-extraction-logistically/temp/3cceb628_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:12,560 - INFO - 🔍 [18:14:12] Starting classification: ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:12,562 - INFO - Initializing TextractProcessor...
2025-09-24 18:14:12,578 - INFO - Initializing BedrockProcessor...
2025-09-24 18:14:12,587 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3cceb628_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:12,589 - INFO - Processing PDF from S3...
2025-09-24 18:14:12,592 - INFO - Downloading PDF from S3 to /tmp/tmpbgiudq54/3cceb628_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:12,600 - INFO - 

EAMCIVO8E5KY4U6ZYWK2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:12,600 - INFO - 

✓ Saved result: output/run1_EAMCIVO8E5KY4U6ZYWK2.json
2025-09-24 18:14:12,755 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:14:12,756 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:12,757 - INFO - Splitting PDF d7adaddc_Z1N2YIEHAA2FERWDQ38E into 1 pages
2025-09-24 18:14:12,758 - INFO - Split PDF into 1 pages
2025-09-24 18:14:12,759 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:12,759 - INFO - Expected pages: [1]
2025-09-24 18:14:12,911 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e1de4f45_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:14:12,917 - INFO - 

KBFTEJIYFMKC7ZILXM5M.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:12,917 - INFO - 

✓ Saved result: output/run1_KBFTEJIYFMKC7ZILXM5M.json
2025-09-24 18:14:13,218 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d89e70ee_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:13,233 - INFO - 

A8UNJQR3VGLF8G4MC11J.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:13,233 - INFO - 

✓ Saved result: output/run1_A8UNJQR3VGLF8G4MC11J.json
2025-09-24 18:14:13,527 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d2239549_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:14:13,533 - INFO - 

AK2S46MQ9UJW5Q43TRRV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:13,533 - INFO - 

✓ Saved result: output/run1_AK2S46MQ9UJW5Q43TRRV.json
2025-09-24 18:14:13,833 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0c7d308c_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:14:13,855 - INFO - 

F4RZ1UDKLCELJG8T6R9G.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:13,855 - INFO - 

✓ Saved result: output/run1_F4RZ1UDKLCELJG8T6R9G.json
2025-09-24 18:14:14,155 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/44051483_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:14,185 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:14:14,185 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:14,188 - INFO - Splitting PDF 389e8e6c_ZA6FSHKD588WC7VTH3MO into 1 pages
2025-09-24 18:14:14,191 - INFO - Split PDF into 1 pages
2025-09-24 18:14:14,191 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:14,191 - INFO - Expected pages: [1]
2025-09-24 18:14:14,247 - INFO - Downloaded PDF size: 1.7 MB
2025-09-24 18:14:14,248 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:14,249 - INFO - Splitting PDF 4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX into 2 pages
2025-09-24 18:14:14,254 - INFO - Split PDF into 2 pages
2025-09-24 18:14:14,254 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:14,255 - INFO - Expected pages: [1, 2]
2025-09-24 18:14:14,834 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 18:14:14,835 - INFO - Splitting PDF into individual pages...
2025-09-24 18:14:14,837 - INFO - Splitting PDF 3cceb628_ZY3XO73EAB4BC4957F0V into 1 pages
2025-09-24 18:14:14,868 - INFO - S3 Image temp/db1176d4_Y2EQEYPYUUYHAD9BZKXO.jpg: Extracted 1944 characters, 95 lines
2025-09-24 18:14:14,868 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:14,868 - INFO - Split PDF into 1 pages
2025-09-24 18:14:14,868 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:14,869 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:14:14,873 - INFO - Expected pages: [1]
2025-09-24 18:14:16,034 - INFO - Page 1: Extracted 330 characters, 26 lines from 837ba135_NLUB3HCM1QWW6FXJBEY3_9b344539_page_001.pdf
2025-09-24 18:14:16,034 - INFO - Successfully processed page 1
2025-09-24 18:14:16,035 - INFO - Combined 1 pages into final text
2025-09-24 18:14:16,035 - INFO - Text validation for 837ba135_NLUB3HCM1QWW6FXJBEY3: 347 characters, 1 pages
2025-09-24 18:14:16,036 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:16,036 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:16,560 - INFO - Page 1: Extracted 295 characters, 22 lines from d7adaddc_Z1N2YIEHAA2FERWDQ38E_9582962c_page_001.pdf
2025-09-24 18:14:16,560 - INFO - Successfully processed page 1
2025-09-24 18:14:16,561 - INFO - Combined 1 pages into final text
2025-09-24 18:14:16,561 - INFO - Text validation for d7adaddc_Z1N2YIEHAA2FERWDQ38E: 312 characters, 1 pages
2025-09-24 18:14:16,561 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:16,561 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:16,689 - INFO - Page 1: Extracted 1059 characters, 66 lines from 292b24d3_OPDBRSVH28PEQZSD5FO9_d99baefe_page_001.pdf
2025-09-24 18:14:16,689 - INFO - Successfully processed page 1
2025-09-24 18:14:16,690 - INFO - Combined 1 pages into final text
2025-09-24 18:14:16,690 - INFO - Text validation for 292b24d3_OPDBRSVH28PEQZSD5FO9: 1076 characters, 1 pages
2025-09-24 18:14:16,690 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:16,690 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:18,107 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4366bdc6-7ca6-42af-ae4a-92c9c7f75f9a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:17 GMT', 'content-type': 'application/json', 'content-length': '2150', 'connection': 'keep-alive', 'x-amzn-requestid': '4366bdc6-7ca6-42af-ae4a-92c9c7f75f9a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify the page type. It appears to be a "Solid Waste Manifest" with details: date, time, truck, trailer, gross weight, tare weight, net weight, etc. The page includes "S WASTE SOLID WASTE MANIFEST". It has fields like "Gross 73340 L Out Scale 3", "Tare 32660 1. In Scale 3". It also has the "seal", "P.O.", "D.O." etc. It is a manifest for waste, not a standard invoice or BOL. It might be a "scale ticket" or "log" type. Let\'s analyze.\n\nDefinition: scale ticket: weight record from a scale for vehicle/load. Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, Date, Time, Ticket no. This page has Gross, Tare, Net, Truck, Driver, Date, Time. So it fits scale ticket.\n\nBut also "S WASTE SOLID WASTE MANIFEST" suggests a manifest. But the classification categories: "scale_ticket" is the best fit. There\'s also "log" (driver log, activity record). But "log" includes driver log, logbook, hours of service, etc. This page is more a weight record; not a log of hours. It\'s a manifest that includes weight details. The "scale ticket" category is defined to include weight records. So we should classify as "scale_ticket".\n\nBut could it be "log" or "scale_ticket"? Let\'s see the difference: log is activity record with times, driver, vehicle ID etc. This page is basically a manifest with weight. The presence of "Gross", "Tare", "Net" indicates weight. The page includes "S WASTE SOLID WASTE MANIFEST". The question: is "solid waste manifest" a type of "scale ticket"? The definitions: scale ticket is weight record. That page is indeed a weight record. So I think "scale_ticket" is appropriate.\n\nWe should produce a JSON array with one object: page_no:1, doc_type:"scale_ticket".\n\nWe must use the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3145, 'outputTokens': 467, 'totalTokens': 3612}, 'metrics': {'latencyMs': 2026}}
2025-09-24 18:14:18,108 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/db1176d4_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:18,138 - INFO - 

Y2EQEYPYUUYHAD9BZKXO.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:18,138 - INFO - 

✓ Saved result: output/run1_Y2EQEYPYUUYHAD9BZKXO.json
2025-09-24 18:14:18,488 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/db1176d4_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:18,605 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b6ceedff-c8bc-4e32-bbd1-5f49c3d94390', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:18 GMT', 'content-type': 'application/json', 'content-length': '719', 'connection': 'keep-alive', 'x-amzn-requestid': 'b6ceedff-c8bc-4e32-bbd1-5f49c3d94390'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to determine doc_type. This page contains weight info: "WEIGHED ON A FAIRBANKS SCALE", "GROSS", "TARE", "NET", "WEIGHER", "SCALE". Likely a Scale Ticket. Also includes "CARRIER COMMODITY GRS #413" maybe but still weight. So doc_type = scale_ticket.\n\nWe must output array of objects with page_no and doc_type.\n\nReturn via classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2656, 'outputTokens': 120, 'totalTokens': 2776}, 'metrics': {'latencyMs': 775}}
2025-09-24 18:14:18,606 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd257c521-4d2c-426f-8d33-e29304f57204', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:18 GMT', 'content-type': 'application/json', 'content-length': '1376', 'connection': 'keep-alive', 'x-amzn-requestid': 'd257c521-4d2c-426f-8d33-e29304f57204'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. Let\'s analyze content.\n\nPage shows: Hamtramck Recycling & Transfer Station. Ticket: 423087-1. Date. Customer, Hauler, Truck. Times In: 7:15am weight 40,440 lb. Out 8:53am weight 33,560 lb. Net 6,880 lb. Product Description, Amount, MISC, Miscellaneous, 3.44 tn. Thank you for business.\n\nThis looks like a transfer station ticket, likely a "scale ticket" or "log" showing weight. Keywords: weight, net weight, gross, tare? It shows "In" weight, "Out" weight, net weight. That aligns with scale ticket definition: weight record from a scale, includes gross, tare, net, weighed at, truck, driver etc. It has weight on a transfer station. So classify as scale_ticket.\n\nCheck if it\'s a log? It is a record of vehicle activity. But scale ticket is more specific weight record. The page is likely a scale ticket from a transfer station. So doc_type: scale_ticket.\n\nReturn the result: array of documents with page_no 1 and doc_type "scale_ticket".\n\nWe need to use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2666, 'outputTokens': 279, 'totalTokens': 2945}, 'metrics': {'latencyMs': 1440}}
2025-09-24 18:14:18,606 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d7adaddc_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:18,607 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/837ba135_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:18,620 - INFO - 

Z1N2YIEHAA2FERWDQ38E.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:18,620 - INFO - 

✓ Saved result: output/run1_Z1N2YIEHAA2FERWDQ38E.json
2025-09-24 18:14:18,924 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d7adaddc_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:18,936 - INFO - 

NLUB3HCM1QWW6FXJBEY3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:18,936 - INFO - 

✓ Saved result: output/run1_NLUB3HCM1QWW6FXJBEY3.json
2025-09-24 18:14:19,087 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f5ab192c-2346-43f3-96b1-c52f5870ce07', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:18 GMT', 'content-type': 'application/json', 'content-length': '1244', 'connection': 'keep-alive', 'x-amzn-requestid': 'f5ab192c-2346-43f3-96b1-c52f5870ce07'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only one page. The content looks like a scale ticket? It includes scale in gross weight, net weight, scale out Tare weight, etc. It also has "BILL OF LADING 531834" and "INVOICE" sections. But main focus: scale ticket definition: weight-related keywords: Gross, Net, Weighed At. Yes. So doc_type should be scale_ticket. It also contains invoice details but it\'s a scale ticket with invoice? Let\'s see: It has "INVOICE" header with QTY, UNIT, DESCRIPTION, RATE, EXTENSION, TAX, TOTAL. But the presence of scale weight fields indicates scale ticket. The classification rule: If contains weight keywords, then it\'s scale_ticket. It also has "BILL OF LADING" but that\'s a header. But weight keywords override. So classify as scale_ticket.\n\nWe must output via function call classify_logistics_doc_type. Provide documents array: one object: page_no 1, doc_type "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2901, 'outputTokens': 228, 'totalTokens': 3129}, 'metrics': {'latencyMs': 1185}}
2025-09-24 18:14:19,087 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/292b24d3_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:19,260 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/837ba135_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:19,281 - INFO - 

OPDBRSVH28PEQZSD5FO9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:19,281 - INFO - 

✓ Saved result: output/run1_OPDBRSVH28PEQZSD5FO9.json
2025-09-24 18:14:19,642 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/292b24d3_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:20,300 - INFO - Page 1: Extracted 1075 characters, 67 lines from 389e8e6c_ZA6FSHKD588WC7VTH3MO_e1476fbe_page_001.pdf
2025-09-24 18:14:20,301 - INFO - Successfully processed page 1
2025-09-24 18:14:20,301 - INFO - Combined 1 pages into final text
2025-09-24 18:14:20,301 - INFO - Text validation for 389e8e6c_ZA6FSHKD588WC7VTH3MO: 1092 characters, 1 pages
2025-09-24 18:14:20,301 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:20,301 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:20,366 - INFO - Page 1: Extracted 4293 characters, 145 lines from bb91bd70_MZ3X0YRE37Y7NHS45V2U_17532ee9_page_001.pdf
2025-09-24 18:14:20,366 - INFO - Successfully processed page 1
2025-09-24 18:14:20,367 - INFO - Combined 1 pages into final text
2025-09-24 18:14:20,367 - INFO - Text validation for bb91bd70_MZ3X0YRE37Y7NHS45V2U: 4310 characters, 1 pages
2025-09-24 18:14:20,369 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:20,369 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:20,611 - INFO - Page 1: Extracted 1052 characters, 70 lines from 3cceb628_ZY3XO73EAB4BC4957F0V_45dd32c0_page_001.pdf
2025-09-24 18:14:20,611 - INFO - Successfully processed page 1
2025-09-24 18:14:20,611 - INFO - Combined 1 pages into final text
2025-09-24 18:14:20,612 - INFO - Text validation for 3cceb628_ZY3XO73EAB4BC4957F0V: 1069 characters, 1 pages
2025-09-24 18:14:20,612 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:20,612 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:22,045 - INFO - Page 2: Extracted 2206 characters, 75 lines from 4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX_e5897632_page_002.pdf
2025-09-24 18:14:22,045 - INFO - Successfully processed page 2
2025-09-24 18:14:22,169 - INFO - Page 1: Extracted 2153 characters, 69 lines from 4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX_e5897632_page_001.pdf
2025-09-24 18:14:22,169 - INFO - Successfully processed page 1
2025-09-24 18:14:22,169 - INFO - Combined 2 pages into final text
2025-09-24 18:14:22,170 - INFO - Text validation for 4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX: 4395 characters, 2 pages
2025-09-24 18:14:22,170 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:14:22,170 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:14:22,411 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c251f417-43f7-40d0-bdca-3d112706ea20', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:22 GMT', 'content-type': 'application/json', 'content-length': '705', 'connection': 'keep-alive', 'x-amzn-requestid': 'c251f417-43f7-40d0-bdca-3d112706ea20'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 provided. It looks like a scale ticket/certificate from a public weighmaster. Keywords: "PUBLIC WEIGHMASTER\'S CERTIFICATE OF WEIGHT & MEASURE", "GROSS WEIGHT", "WEIGHT & MEASURE", "TICKET #". This aligns with scale ticket. So doc_type: scale_ticket.\n\nNo continuation. Output single object with page_no 1.\n\nLet\'s produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3778, 'outputTokens': 109, 'totalTokens': 3887}, 'metrics': {'latencyMs': 725}}
2025-09-24 18:14:22,412 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bb91bd70_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:22,523 - INFO - 

MZ3X0YRE37Y7NHS45V2U.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:22,523 - INFO - 

✓ Saved result: output/run1_MZ3X0YRE37Y7NHS45V2U.json
2025-09-24 18:14:22,719 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6a143725-9388-462a-912c-fbf54b06b53a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:22 GMT', 'content-type': 'application/json', 'content-length': '1254', 'connection': 'keep-alive', 'x-amzn-requestid': '6a143725-9388-462a-912c-fbf54b06b53a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify doc_type. The content includes terms like "SCALE IN GROSS WEIGHT", "SCALE OUT TARE WEIGHT", "INVOICE", "BILL OF LADING 531837", "NET AMOUNT". It looks like a scale ticket with invoice details. There is "BILL OF LADING" but also "SCALE IN GROSS WEIGHT" etc. The description says: For scale ticket, if contains weight keywords like Gross, Tare, Net, Weighed At, etc. It also includes invoice details. But classification: It may be a "scale_ticket" because of weight info. But also invoice. But instruction: If document contains weight keywords, classify as scale_ticket even if also invoice. So we should classify as scale_ticket.\n\nCheck if any other type: invoice: would have "Invoice" and amount due. Here there\'s "INVOICE" heading but also weight. Since scale ticket has weight, choose scale_ticket.\n\nThus output a single object: page_no:1, doc_type:"scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2910, 'outputTokens': 237, 'totalTokens': 3147}, 'metrics': {'latencyMs': 1204}}
2025-09-24 18:14:22,719 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/389e8e6c_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:22,831 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bb91bd70_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:22,854 - INFO - 

ZA6FSHKD588WC7VTH3MO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:22,854 - INFO - 

✓ Saved result: output/run1_ZA6FSHKD588WC7VTH3MO.json
2025-09-24 18:14:23,177 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/389e8e6c_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:23,743 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '76d44a61-5f92-4e5a-a9e5-0ebd9b1cc1f4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:23 GMT', 'content-type': 'application/json', 'content-length': '2083', 'connection': 'keep-alive', 'x-amzn-requestid': '76d44a61-5f92-4e5a-a9e5-0ebd9b1cc1f4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single-page document. Need to classify based on content. Let\'s analyze text.\n\nWe see headings like "WEIGHMASTER", "IN Joann C. OUT - Taylor K.", "CUSTOMER", "DATE/TIME IN", "DATE/TIME OUT", "VEHICLE", "CONTAINER", "REFERENCE", "Contract: EARTHWATCH MSW", "BILL OF LADING", "SCALE IN GROSS WEIGHT", "NET TONS", "INBOUND SCALE OUT", "TARE WEIGHT", "NET WEIGHT", "INVOICE", "QTY.", "UNIT", "DESCRIPTION", "RATE", "EXTENSION", "TAX", "TOTAL", "120.00 YD", "Tracking QTY", "21.49 tn", "MSW", "Origin:SUFFOLK-MA 100%", "FEE-HANDLING SERVICE", "Signature", "Hours of operation", "NET AMOUNT", "TENDERED", "CHANGE", "The undersigned individual signing this document on behalf of Customer acknowledges..."\n\nThis looks like a combination of a scale ticket (weight info) and an invoice or maybe a combined carrier documents. There\'s "BILL OF LADING" and "INVOICE" and "SCALE IN GROSS WEIGHT" etc. The page contains multiple document headers: "BILL OF LADING" and "INVOICE". Also "WEIGHMASTER" which is a scale ticket. So per the rule: if a page contains multiple distinct document headers, classify as combined_carrier_documents. But is that appropriate? The definition says: Combined Carrier Documents: page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.). This page seems to contain BOL, invoice, scale ticket. So yes, combined_carrier_documents.\n\nLet\'s double-check that it doesn\'t match a scale ticket alone: It has weight info, but also invoice and BOL. So classification should be combined_carrier_documents.\n\nThus we return JSON: {page_no:1, doc_type:"combined_carrier_documents"}.\n\nWe must output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2897, 'outputTokens': 449, 'totalTokens': 3346}, 'metrics': {'latencyMs': 1896}}
2025-09-24 18:14:23,744 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3cceb628_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:23,780 - INFO - 

ZY3XO73EAB4BC4957F0V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-24 18:14:23,780 - INFO - 

✓ Saved result: output/run1_ZY3XO73EAB4BC4957F0V.json
2025-09-24 18:14:24,152 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3cceb628_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:24,594 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '52d0698b-c61e-4591-b68d-527e65b18a7c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:44:24 GMT', 'content-type': 'application/json', 'content-length': '1377', 'connection': 'keep-alive', 'x-amzn-requestid': '52d0698b-c61e-4591-b68d-527e65b18a7c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. These pages appear to be CAT Scale Guarantee documents, which mention TICKET NUMBER, and include weight information, and also a "PUBLIC WEIGHMASTER\'S CERTIFICATE OF WEIGHT & MEASURE". This seems like a scale ticket / weight certificate. The type "scale_ticket" is defined as weight record from a scale for vehicle/load. The page includes weight, date, ticket number. It also includes "Public Weighmaster\'s Certificate of Weight & Measure". That is a certificate. According to definitions, "scale_ticket" includes keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, Ticket no. So yes. Should classify as "scale_ticket". Both pages appear similar. Are they continuation? Page 1 is one ticket. Page 2 is another ticket. They are separate documents. So each page gets doc_type scale_ticket. Output must be via tool. Need to return array of objects with page_no and doc_type. Provide exactly two objects. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"},{"page_no":2,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3806, 'outputTokens': 246, 'totalTokens': 4052}, 'metrics': {'latencyMs': 1185}}
2025-09-24 18:14:24,594 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:24,647 - INFO - 

LVWIMLJ8Q7EH6QLW7RTX.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        },
        {
            "page_no": 2,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:14:24,648 - INFO - 

✓ Saved result: output/run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:14:24,953 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4afbbe8d_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:24,955 - INFO - 
📊 Processing Summary:
2025-09-24 18:14:24,955 - INFO -    Total files: 13
2025-09-24 18:14:24,956 - INFO -    Successful: 13
2025-09-24 18:14:24,956 - INFO -    Failed: 0
2025-09-24 18:14:24,956 - INFO -    Duration: 30.74 seconds
2025-09-24 18:14:24,956 - INFO -    Output directory: output
2025-09-24 18:14:24,956 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:14:24,956 - INFO -    📄 A8UNJQR3VGLF8G4MC11J.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,956 - INFO -    📄 AK2S46MQ9UJW5Q43TRRV.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,956 - INFO -    📄 EAMCIVO8E5KY4U6ZYWK2.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 F4RZ1UDKLCELJG8T6R9G.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 KBFTEJIYFMKC7ZILXM5M.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 LVWIMLJ8Q7EH6QLW7RTX.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"},{"page_no":2,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 MZ3X0YRE37Y7NHS45V2U.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 NLUB3HCM1QWW6FXJBEY3.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 OPDBRSVH28PEQZSD5FO9.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,957 - INFO -    📄 Y2EQEYPYUUYHAD9BZKXO.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,958 - INFO -    📄 Z1N2YIEHAA2FERWDQ38E.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,958 - INFO -    📄 ZA6FSHKD588WC7VTH3MO.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:14:24,958 - INFO -    📄 ZY3XO73EAB4BC4957F0V.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-24 18:14:24,959 - INFO - 
============================================================================================================================================
2025-09-24 18:14:24,959 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:14:24,959 - INFO - ============================================================================================================================================
2025-09-24 18:14:24,959 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:14:24,959 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:14:24,959 - INFO - A8UNJQR3VGLF8G4MC11J.pdf                           1      scale_ticket                             run1_A8UNJQR3VGLF8G4MC11J.json                                                  
2025-09-24 18:14:24,959 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:14:24,959 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A8UNJQR3VGLF8G4MC11J.json
2025-09-24 18:14:24,959 - INFO - 
2025-09-24 18:14:24,959 - INFO - AK2S46MQ9UJW5Q43TRRV.pdf                           1      scale_ticket                             run1_AK2S46MQ9UJW5Q43TRRV.json                                                  
2025-09-24 18:14:24,960 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:14:24,960 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AK2S46MQ9UJW5Q43TRRV.json
2025-09-24 18:14:24,960 - INFO - 
2025-09-24 18:14:24,960 - INFO - EAMCIVO8E5KY4U6ZYWK2.jpg                           1      scale_ticket                             run1_EAMCIVO8E5KY4U6ZYWK2.json                                                  
2025-09-24 18:14:24,960 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:14:24,960 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EAMCIVO8E5KY4U6ZYWK2.json
2025-09-24 18:14:24,960 - INFO - 
2025-09-24 18:14:24,960 - INFO - F4RZ1UDKLCELJG8T6R9G.png                           1      scale_ticket                             run1_F4RZ1UDKLCELJG8T6R9G.json                                                  
2025-09-24 18:14:24,960 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:14:24,960 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_F4RZ1UDKLCELJG8T6R9G.json
2025-09-24 18:14:24,960 - INFO - 
2025-09-24 18:14:24,960 - INFO - KBFTEJIYFMKC7ZILXM5M.png                           1      scale_ticket                             run1_KBFTEJIYFMKC7ZILXM5M.json                                                  
2025-09-24 18:14:24,960 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:14:24,960 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KBFTEJIYFMKC7ZILXM5M.json
2025-09-24 18:14:24,961 - INFO - 
2025-09-24 18:14:24,961 - INFO - LVWIMLJ8Q7EH6QLW7RTX.pdf                           1      scale_ticket                             run1_LVWIMLJ8Q7EH6QLW7RTX.json                                                  
2025-09-24 18:14:24,961 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:24,961 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:14:24,961 - INFO - 
2025-09-24 18:14:24,961 - INFO - LVWIMLJ8Q7EH6QLW7RTX.pdf                           2      scale_ticket                             run1_LVWIMLJ8Q7EH6QLW7RTX.json                                                  
2025-09-24 18:14:24,961 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:14:24,961 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:14:24,961 - INFO - 
2025-09-24 18:14:24,961 - INFO - MZ3X0YRE37Y7NHS45V2U.pdf                           1      scale_ticket                             run1_MZ3X0YRE37Y7NHS45V2U.json                                                  
2025-09-24 18:14:24,961 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:14:24,961 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MZ3X0YRE37Y7NHS45V2U.json
2025-09-24 18:14:24,961 - INFO - 
2025-09-24 18:14:24,961 - INFO - NLUB3HCM1QWW6FXJBEY3.pdf                           1      scale_ticket                             run1_NLUB3HCM1QWW6FXJBEY3.json                                                  
2025-09-24 18:14:24,961 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:14:24,961 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NLUB3HCM1QWW6FXJBEY3.json
2025-09-24 18:14:24,962 - INFO - 
2025-09-24 18:14:24,962 - INFO - OPDBRSVH28PEQZSD5FO9.pdf                           1      scale_ticket                             run1_OPDBRSVH28PEQZSD5FO9.json                                                  
2025-09-24 18:14:24,962 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:14:24,962 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OPDBRSVH28PEQZSD5FO9.json
2025-09-24 18:14:24,962 - INFO - 
2025-09-24 18:14:24,962 - INFO - Y2EQEYPYUUYHAD9BZKXO.jpg                           1      scale_ticket                             run1_Y2EQEYPYUUYHAD9BZKXO.json                                                  
2025-09-24 18:14:24,962 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:14:24,962 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y2EQEYPYUUYHAD9BZKXO.json
2025-09-24 18:14:24,962 - INFO - 
2025-09-24 18:14:24,962 - INFO - Z1N2YIEHAA2FERWDQ38E.pdf                           1      scale_ticket                             run1_Z1N2YIEHAA2FERWDQ38E.json                                                  
2025-09-24 18:14:24,962 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:14:24,962 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z1N2YIEHAA2FERWDQ38E.json
2025-09-24 18:14:24,962 - INFO - 
2025-09-24 18:14:24,962 - INFO - ZA6FSHKD588WC7VTH3MO.pdf                           1      scale_ticket                             run1_ZA6FSHKD588WC7VTH3MO.json                                                  
2025-09-24 18:14:24,962 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:14:24,962 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZA6FSHKD588WC7VTH3MO.json
2025-09-24 18:14:24,962 - INFO - 
2025-09-24 18:14:24,963 - INFO - ZY3XO73EAB4BC4957F0V.pdf                           1      combined_carrier_d...                    run1_ZY3XO73EAB4BC4957F0V.json                                                  
2025-09-24 18:14:24,963 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:14:24,963 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZY3XO73EAB4BC4957F0V.json
2025-09-24 18:14:24,963 - INFO - 
2025-09-24 18:14:24,963 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:14:24,963 - INFO - Total entries: 14
2025-09-24 18:14:24,963 - INFO - ============================================================================================================================================
2025-09-24 18:14:24,963 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:14:24,963 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:14:24,963 - INFO -   1. A8UNJQR3VGLF8G4MC11J.pdf            Page 1   → scale_ticket    | run1_A8UNJQR3VGLF8G4MC11J.json
2025-09-24 18:14:24,963 - INFO -   2. AK2S46MQ9UJW5Q43TRRV.pdf            Page 1   → scale_ticket    | run1_AK2S46MQ9UJW5Q43TRRV.json
2025-09-24 18:14:24,963 - INFO -   3. EAMCIVO8E5KY4U6ZYWK2.jpg            Page 1   → scale_ticket    | run1_EAMCIVO8E5KY4U6ZYWK2.json
2025-09-24 18:14:24,963 - INFO -   4. F4RZ1UDKLCELJG8T6R9G.png            Page 1   → scale_ticket    | run1_F4RZ1UDKLCELJG8T6R9G.json
2025-09-24 18:14:24,963 - INFO -   5. KBFTEJIYFMKC7ZILXM5M.png            Page 1   → scale_ticket    | run1_KBFTEJIYFMKC7ZILXM5M.json
2025-09-24 18:14:24,963 - INFO -   6. LVWIMLJ8Q7EH6QLW7RTX.pdf            Page 1   → scale_ticket    | run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:14:24,963 - INFO -   7. LVWIMLJ8Q7EH6QLW7RTX.pdf            Page 2   → scale_ticket    | run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:14:24,964 - INFO -   8. MZ3X0YRE37Y7NHS45V2U.pdf            Page 1   → scale_ticket    | run1_MZ3X0YRE37Y7NHS45V2U.json
2025-09-24 18:14:24,964 - INFO -   9. NLUB3HCM1QWW6FXJBEY3.pdf            Page 1   → scale_ticket    | run1_NLUB3HCM1QWW6FXJBEY3.json
2025-09-24 18:14:24,964 - INFO -  10. OPDBRSVH28PEQZSD5FO9.pdf            Page 1   → scale_ticket    | run1_OPDBRSVH28PEQZSD5FO9.json
2025-09-24 18:14:24,964 - INFO -  11. Y2EQEYPYUUYHAD9BZKXO.jpg            Page 1   → scale_ticket    | run1_Y2EQEYPYUUYHAD9BZKXO.json
2025-09-24 18:14:24,964 - INFO -  12. Z1N2YIEHAA2FERWDQ38E.pdf            Page 1   → scale_ticket    | run1_Z1N2YIEHAA2FERWDQ38E.json
2025-09-24 18:14:24,964 - INFO -  13. ZA6FSHKD588WC7VTH3MO.pdf            Page 1   → scale_ticket    | run1_ZA6FSHKD588WC7VTH3MO.json
2025-09-24 18:14:24,964 - INFO -  14. ZY3XO73EAB4BC4957F0V.pdf            Page 1   → combined_carrier_documents | run1_ZY3XO73EAB4BC4957F0V.json
2025-09-24 18:14:24,964 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:14:24,964 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 30.735814, 'processed_files': [{'filename': 'A8UNJQR3VGLF8G4MC11J.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/A8UNJQR3VGLF8G4MC11J.pdf'}, {'filename': 'AK2S46MQ9UJW5Q43TRRV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/AK2S46MQ9UJW5Q43TRRV.pdf'}, {'filename': 'EAMCIVO8E5KY4U6ZYWK2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/EAMCIVO8E5KY4U6ZYWK2.jpg'}, {'filename': 'F4RZ1UDKLCELJG8T6R9G.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/F4RZ1UDKLCELJG8T6R9G.png'}, {'filename': 'KBFTEJIYFMKC7ZILXM5M.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/KBFTEJIYFMKC7ZILXM5M.png'}, {'filename': 'LVWIMLJ8Q7EH6QLW7RTX.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}, {'page_no': 2, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf'}, {'filename': 'MZ3X0YRE37Y7NHS45V2U.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/MZ3X0YRE37Y7NHS45V2U.pdf'}, {'filename': 'NLUB3HCM1QWW6FXJBEY3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/NLUB3HCM1QWW6FXJBEY3.pdf'}, {'filename': 'OPDBRSVH28PEQZSD5FO9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/OPDBRSVH28PEQZSD5FO9.pdf'}, {'filename': 'Y2EQEYPYUUYHAD9BZKXO.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Y2EQEYPYUUYHAD9BZKXO.jpg'}, {'filename': 'Z1N2YIEHAA2FERWDQ38E.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Z1N2YIEHAA2FERWDQ38E.pdf'}, {'filename': 'ZA6FSHKD588WC7VTH3MO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZA6FSHKD588WC7VTH3MO.pdf'}, {'filename': 'ZY3XO73EAB4BC4957F0V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZY3XO73EAB4BC4957F0V.pdf'}]}
