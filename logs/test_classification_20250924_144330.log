2025-09-24 14:43:30,120 - INFO - Logging initialized. Log file: logs/test_classification_20250924_144330.log
2025-09-24 14:43:30,120 - INFO - 📁 Found 8 files to process
2025-09-24 14:43:30,120 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:43:30,120 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 14:43:30,120 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 14:43:30,120 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 14:43:30,120 - INFO - ⬆️ [14:43:30] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:31,725 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/db5ee4be_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:31,726 - INFO - 🔍 [14:43:31] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:31,727 - INFO - ⬆️ [14:43:31] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:31,730 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:31,754 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:31,759 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/db5ee4be_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:31,760 - INFO - Processing PDF from S3...
2025-09-24 14:43:31,760 - INFO - Downloading PDF from S3 to /tmp/tmpyvt16hhd/db5ee4be_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:33,474 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:43:33,474 - INFO - Splitting PDF into individual pages...
2025-09-24 14:43:33,474 - INFO - Splitting PDF db5ee4be_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 14:43:33,476 - INFO - Split PDF into 1 pages
2025-09-24 14:43:33,476 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:43:33,476 - INFO - Expected pages: [1]
2025-09-24 14:43:37,435 - INFO - Page 1: Extracted 1260 characters, 84 lines from db5ee4be_BQJUG5URFR2GH9ECWFV4_0134ea94_page_001.pdf
2025-09-24 14:43:37,436 - INFO - Successfully processed page 1
2025-09-24 14:43:37,436 - INFO - Combined 1 pages into final text
2025-09-24 14:43:37,436 - INFO - Text validation for db5ee4be_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 14:43:37,436 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:37,436 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:37,848 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/e9db1b5d_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:37,849 - INFO - 🔍 [14:43:37] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:37,849 - INFO - ⬆️ [14:43:37] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:37,855 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:37,866 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:37,869 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e9db1b5d_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:37,869 - INFO - Processing image from S3...
2025-09-24 14:43:38,822 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/698e1e2d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:38,822 - INFO - 🔍 [14:43:38] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:38,823 - INFO - ⬆️ [14:43:38] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:38,826 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:38,845 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:38,848 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/698e1e2d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:38,849 - INFO - Processing PDF from S3...
2025-09-24 14:43:38,849 - INFO - Downloading PDF from S3 to /tmp/tmp81bvk0b3/698e1e2d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:39,570 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/5345bacc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:39,570 - INFO - 🔍 [14:43:39] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:39,571 - INFO - ⬆️ [14:43:39] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:39,574 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:39,598 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:39,603 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5345bacc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:39,603 - INFO - Processing PDF from S3...
2025-09-24 14:43:39,603 - INFO - Downloading PDF from S3 to /tmp/tmpydfbye8r/5345bacc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:40,196 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '14e071ba-ab00-43f9-878d-8be4e6e072ef', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:40 GMT', 'content-type': 'application/json', 'content-length': '816', 'connection': 'keep-alive', 'x-amzn-requestid': '14e071ba-ab00-43f9-878d-8be4e6e072ef'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to check if it is a continuation of previous? There\'s no previous page. So we need to analyze content.\n\nThis page contains "ELD Records", driver details, odometer, times, etc. This seems like a Log type: driver log with times, event/status. That matches \'log\' definition: driver log. So classify as log.\n\nWe must output JSON via tool call classify_logistics_doc_type. Format: {documents:[{page_no:1, doc_type:"log"}]}\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 136, 'totalTokens': 3064}, 'metrics': {'latencyMs': 823}}
2025-09-24 14:43:40,196 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/db5ee4be_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:41,055 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:43:41,056 - INFO - Splitting PDF into individual pages...
2025-09-24 14:43:41,057 - INFO - Splitting PDF 698e1e2d_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 14:43:41,062 - INFO - Split PDF into 1 pages
2025-09-24 14:43:41,062 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:43:41,062 - INFO - Expected pages: [1]
2025-09-24 14:43:41,522 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:43:41,523 - INFO - Splitting PDF into individual pages...
2025-09-24 14:43:41,525 - INFO - Splitting PDF 5345bacc_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 14:43:41,535 - INFO - Split PDF into 7 pages
2025-09-24 14:43:41,535 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:43:41,535 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 14:43:41,623 - INFO - S3 Image temp/e9db1b5d_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 14:43:41,623 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:41,623 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:43,987 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/6a2f1bc8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:43,988 - INFO - 🔍 [14:43:43] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:43,988 - INFO - ⬆️ [14:43:43] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:43,990 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:44,007 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:44,010 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6a2f1bc8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:44,010 - INFO - Processing image from S3...
2025-09-24 14:43:45,361 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a6db4a0b-39cc-4147-901a-2b52b36b764a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:45 GMT', 'content-type': 'application/json', 'content-length': '1847', 'connection': 'keep-alive', 'x-amzn-requestid': 'a6db4a0b-39cc-4147-901a-2b52b36b764a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify the document type. Let\'s examine content. It looks like a log of truck arrival times and work orders. It\'s a log or ingate/outgate? Let\'s parse.\n\nPage content: "DATE: 16-Jun Metal Finishing" then "UNIT #: 6063/283420 Receipt" then "Truck Ready Time:" etc. There\'s "Driver: KARRA". "Truck Departure:" "DEPARTURE TIME: 5am". Then "PICK UP DEL Dock Time" maybe columns. Then customer/work order numbers, etc. It seems like a log of arrival/departure times for trucks that delivered items. Possibly a "log" document: Activity record. It includes driver name, truck ready, departure time, pick up, etc. Could be ingate/outgate? Ingates: record of vehicle entering facility, with time in. Outgate: exit. This page shows arrival times and departure times? It shows "Truck Ready Time" and "Truck Departure" maybe departure time. Could be a gate log. The type "log" seems appropriate.\n\nCheck for ingate/outgate: Keywords: "Truck Ready Time", "Truck Departure", "Dock Time". This looks like an arrival/departure log for equipment at a facility. Ingate/outgate might have "Truck In" "Truck Out" "Gate In" "Gate Out". But "Truck Ready Time" may indicate ready for loading. This could be part of a logbook.\n\nThus classify as "log".\n\nWe must return one object for page 1 with page_no 1 and doc_type "log".\n\nReturn via tool classify_logistics_doc_type: documents array with single entry: {page_no:1, doc_type:"log"}.\n\nLet\'s produce the JSON accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2598, 'outputTokens': 380, 'totalTokens': 2978}, 'metrics': {'latencyMs': 1777}}
2025-09-24 14:43:45,362 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e9db1b5d_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:45,837 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/3dabc2d0_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:45,837 - INFO - 🔍 [14:43:45] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:45,838 - INFO - ⬆️ [14:43:45] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:45,840 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:45,857 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:45,888 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3dabc2d0_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:45,889 - INFO - Processing image from S3...
2025-09-24 14:43:46,143 - INFO - Page 1: Extracted 519 characters, 34 lines from 698e1e2d_KE7TCH9TPQZFVA5CZ3HT_2416da58_page_001.pdf
2025-09-24 14:43:46,143 - INFO - Successfully processed page 1
2025-09-24 14:43:46,143 - INFO - Combined 1 pages into final text
2025-09-24 14:43:46,143 - INFO - Text validation for 698e1e2d_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 14:43:46,144 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:46,144 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:46,183 - INFO - Page 1: Extracted 1731 characters, 110 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_001.pdf
2025-09-24 14:43:46,183 - INFO - Successfully processed page 1
2025-09-24 14:43:46,343 - INFO - Page 2: Extracted 1821 characters, 105 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_002.pdf
2025-09-24 14:43:46,343 - INFO - Successfully processed page 2
2025-09-24 14:43:46,448 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/14417d58_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:46,449 - INFO - 🔍 [14:43:46] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:46,449 - INFO - ⬆️ [14:43:46] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:46,452 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:46,466 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:46,471 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/14417d58_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:46,471 - INFO - Processing image from S3...
2025-09-24 14:43:46,497 - INFO - Page 4: Extracted 2242 characters, 148 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_004.pdf
2025-09-24 14:43:46,497 - INFO - Successfully processed page 4
2025-09-24 14:43:46,534 - INFO - Page 5: Extracted 2059 characters, 131 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_005.pdf
2025-09-24 14:43:46,534 - INFO - Successfully processed page 5
2025-09-24 14:43:46,589 - INFO - Page 6: Extracted 1973 characters, 129 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_006.pdf
2025-09-24 14:43:46,589 - INFO - Successfully processed page 6
2025-09-24 14:43:46,766 - INFO - Page 3: Extracted 2265 characters, 147 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_003.pdf
2025-09-24 14:43:46,766 - INFO - Successfully processed page 3
2025-09-24 14:43:47,531 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/a07414af_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:47,532 - INFO - 🔍 [14:43:47] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:47,533 - INFO - Initializing TextractProcessor...
2025-09-24 14:43:47,548 - INFO - Initializing BedrockProcessor...
2025-09-24 14:43:47,552 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a07414af_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:47,553 - INFO - Processing image from S3...
2025-09-24 14:43:47,573 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:47,573 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 14:43:47,882 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/db5ee4be_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:47,891 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:47,891 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 14:43:48,256 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e9db1b5d_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:48,333 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9fb31973-3c86-4df1-b520-fa62b905967e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:48 GMT', 'content-type': 'application/json', 'content-length': '844', 'connection': 'keep-alive', 'x-amzn-requestid': '9fb31973-3c86-4df1-b520-fa62b905967e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. We examine keywords. The page has "Driver Detention Certificate". That suggests a detention certificate, but classification list: we have "log" type with subcategory "Driver detention certificate" under log. Also "log" definition includes "Driver detention certificate" as one of the possible log types. So we classify doc_type: "log". The classification tool expects an array of objects with page_no and doc_type. Since only page1, page_no 1.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2626, 'outputTokens': 127, 'totalTokens': 2753}, 'metrics': {'latencyMs': 691}}
2025-09-24 14:43:48,334 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/698e1e2d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:48,351 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:48,351 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 14:43:48,541 - INFO - S3 Image temp/14417d58_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 14:43:48,542 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:48,542 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:48,655 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/698e1e2d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:48,752 - INFO - Page 7: Extracted 417 characters, 27 lines from 5345bacc_O2IU5G77LYNTYE0RP1TI_6d1d27d7_page_007.pdf
2025-09-24 14:43:48,752 - INFO - Successfully processed page 7
2025-09-24 14:43:48,753 - INFO - Combined 7 pages into final text
2025-09-24 14:43:48,753 - INFO - Text validation for 5345bacc_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 14:43:48,753 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:48,754 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:49,449 - INFO - S3 Image temp/6a2f1bc8_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 14:43:49,449 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:49,449 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:49,682 - INFO - S3 Image temp/a07414af_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 14:43:49,682 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:49,682 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:49,843 - INFO - S3 Image temp/3dabc2d0_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 14:43:49,843 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:43:49,843 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:43:50,769 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b7b046cb-14a6-469d-9fec-cbd082709ec8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:50 GMT', 'content-type': 'application/json', 'content-length': '1025', 'connection': 'keep-alive', 'x-amzn-requestid': 'b7b046cb-14a6-469d-9fec-cbd082709ec8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content is:\n\nHOLD 8\nUCO\nU.U\nMAX\n45.8 F\n\nIt seems like a temperature log maybe? "HOLD 8" could be a hold time? "UCO" maybe "Uncoated"? "45.8 F" is temperature. The page likely is a temperature log entry. The doc types include log: Temperature log/reading. That has keywords like Temperature, degree Fahrenheit, etc. So this page is log. Also could be a fuel receipt? No. It\'s log. So doc_type: "log". Page number 1. Output must be a call to classify_logistics_doc_type with documents array containing object.\n\nSo we produce:\n\n{\n "documents": [\n  {"page_no": 1, "doc_type": "log"}\n ]\n}\n\nCheck if continuation: no. So classification.\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2475, 'outputTokens': 201, 'totalTokens': 2676}, 'metrics': {'latencyMs': 968}}
2025-09-24 14:43:50,770 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/14417d58_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:50,776 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:50,776 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 14:43:51,120 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/14417d58_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:51,467 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd973f8ca-df07-40f5-a01f-088ec7792d07', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:51 GMT', 'content-type': 'application/json', 'content-length': '664', 'connection': 'keep-alive', 'x-amzn-requestid': 'd973f8ca-df07-40f5-a01f-088ec7792d07'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF page content. The page is a driver’s daily log. The doc type should be "log". So we need to return a JSON array of objects? According to tool signature, need to call classify_logistics_doc_type with documents array each object containing page_no and doc_type. For one page, page_no=1, doc_type="log".\n\nWe must output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3574, 'outputTokens': 98, 'totalTokens': 3672}, 'metrics': {'latencyMs': 703}}
2025-09-24 14:43:51,467 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6a2f1bc8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:51,515 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:51,515 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 14:43:51,570 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '619d4e6f-a46c-40b7-930f-27489364e683', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:51 GMT', 'content-type': 'application/json', 'content-length': '1580', 'connection': 'keep-alive', 'x-amzn-requestid': '619d4e6f-a46c-40b7-930f-27489364e683'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content is a Thermo King service watch log. This is a temperature log with timestamps, etc. That matches the "log" category: activity record, temperature log. So each page likely is a log. No continuation markers? Check page numbers. There is no "continued" marker. So each page is a log. But we should check if any page is continuation of previous? Possibly page5-6 might be continuation. But we should treat each page as separate page but same document type: log. The instruction: For each page, output an object with page_no and doc_type. Must use classify_logistics_doc_type tool.\n\nThe tool expects an array with documents: [{page_no, doc_type}]. So we need to call tool with documents array of 7 pages.\n\nThus output:\n\n{\n "documents": [\n   {"page_no":1,"doc_type":"log"},\n   {"page_no":2,"doc_type":"log"},\n   ...\n   {"page_no":7,"doc_type":"log"}\n ]\n}\n\nWe need to confirm no other type. It\'s a log: temperature log. So doc_type: log. Output exactly that.\n\nProceed.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 8272, 'outputTokens': 318, 'totalTokens': 8590}, 'metrics': {'latencyMs': 1575}}
2025-09-24 14:43:51,571 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5345bacc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:51,819 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6a2f1bc8_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:51,934 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:51,935 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:52,222 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5345bacc_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:52,594 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0ac9380b-47f0-4521-b35b-5cde2040c709', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:52 GMT', 'content-type': 'application/json', 'content-length': '1475', 'connection': 'keep-alive', 'x-amzn-requestid': '0ac9380b-47f0-4521-b35b-5cde2040c709'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content: "<page1> THERMO KING +1 5h </page1>". Very minimal. Likely a page from a log? The text "THERMO KING" could be a brand maybe. "+1" maybe a quantity. "5h" maybe hours. Could be a log entry. Not enough. It might be a log page: driver log, log entry with time. But also could be part of a packing list? "THERMO KING" might be an item. Could be a packing list with item name, quantity, maybe time? "5h" maybe hours or weight? "THERMO KING" could be a brand name "Thermo King" (air conditioning trucks). So maybe a log of temperature? "THERMO KING" indicates a brand of refrigeration unit. "5h" might be hours of operation. Could be a log. The page likely is a log page. There are no other headers. "THERMO KING" might not be a header. But could be a log entry: e.g., "THERMO KING 5h" might indicate a unit used for 5 hours. So log page seems plausible.\n\nGiven the instruction to classify each page. There is only one page. If not continuation, classify as log. There is no indication of continuation. So output doc_type "log". Provide array of single object with page_no 1 and doc_type "log". Use tool.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2467, 'outputTokens': 314, 'totalTokens': 2781}, 'metrics': {'latencyMs': 1602}}
2025-09-24 14:43:52,594 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a07414af_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:52,600 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:52,600 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 14:43:52,875 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '408135e5-bfed-4ace-a8a9-9f17e15f53c9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:13:52 GMT', 'content-type': 'application/json', 'content-length': '1841', 'connection': 'keep-alive', 'x-amzn-requestid': '408135e5-bfed-4ace-a8a9-9f17e15f53c9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. We need to identify if it\'s a continuation of previous page? No previous page. So we need to determine doc_type. The content looks like a schedule of truck departures, work orders, SO numbers, etc. It seems like an "ingate" or "outgate"? But ingate/outgate are gate records. This looks more like a "work order schedule" or "driver schedule". It might be some kind of "inbound/Outbound" schedule. But not one of specified types. Maybe "log" (activity log). The doc might be a driver log or gate log. It lists times, work orders. It could be a "ingate" or "outgate" but not clear.\n\nLet\'s analyze keywords: "Truck Ready Time", "Truck Departure", "Dock Time", "CUSTOMER", "WORK ORDER #". Also "**********" etc. No clear "Bill of Lading", "Proof of Delivery", "Invoice", etc. "SO" stands for Sales Order. Could be "so_confirmation" but that is seller confirmation. This page is a schedule of pick-ups. Might be a "tender_from_cust"? No. It\'s a schedule for a carrier to pick up. Could be "ingate" if it\'s a gate-in record with times. But ingate usually records truck entering facility. Here we have "Truck Ready Time" and "Truck Departure" times. No mention of gate in/out. There is "Dock Time" maybe indicates docking.\n\n"Log" as defined: Activity record (driver log, tracking log) with dock time. That matches: "Driver log" with times, docks, etc. So likely "log".\n\nThus output: page_no 1, doc_type log. Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2752, 'outputTokens': 387, 'totalTokens': 3139}, 'metrics': {'latencyMs': 1839}}
2025-09-24 14:43:52,875 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3dabc2d0_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:52,889 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a07414af_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:52,909 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:43:52,909 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 14:43:53,200 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3dabc2d0_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:53,201 - INFO - 
📊 Processing Summary:
2025-09-24 14:43:53,201 - INFO -    Total files: 8
2025-09-24 14:43:53,201 - INFO -    Successful: 8
2025-09-24 14:43:53,201 - INFO -    Failed: 0
2025-09-24 14:43:53,201 - INFO -    Duration: 23.08 seconds
2025-09-24 14:43:53,201 - INFO -    Output directory: output
2025-09-24 14:43:53,201 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:43:53,202 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,202 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:43:53,203 - INFO - 
============================================================================================================================================
2025-09-24 14:43:53,203 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:43:53,203 - INFO - ============================================================================================================================================
2025-09-24 14:43:53,203 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:43:53,203 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:43:53,203 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 14:43:53,203 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:43:53,203 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 14:43:53,203 - INFO - 
2025-09-24 14:43:53,203 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 14:43:53,203 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:43:53,203 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 14:43:53,203 - INFO - 
2025-09-24 14:43:53,203 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-24 14:43:53,204 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:43:53,204 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 14:43:53,204 - INFO - 
2025-09-24 14:43:53,204 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,204 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,204 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,204 - INFO - 
2025-09-24 14:43:53,204 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,204 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,204 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,204 - INFO - 
2025-09-24 14:43:53,204 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,204 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,204 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,204 - INFO - 
2025-09-24 14:43:53,204 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,204 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,204 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,204 - INFO - 
2025-09-24 14:43:53,205 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,205 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,205 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,205 - INFO - 
2025-09-24 14:43:53,205 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,205 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,205 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,205 - INFO - 
2025-09-24 14:43:53,205 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 14:43:53,205 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:43:53,205 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,205 - INFO - 
2025-09-24 14:43:53,205 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 14:43:53,205 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:43:53,205 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 14:43:53,205 - INFO - 
2025-09-24 14:43:53,205 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 14:43:53,205 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:43:53,205 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 14:43:53,206 - INFO - 
2025-09-24 14:43:53,206 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 14:43:53,206 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:43:53,206 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 14:43:53,206 - INFO - 
2025-09-24 14:43:53,206 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      log                  run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 14:43:53,206 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:43:53,206 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 14:43:53,206 - INFO - 
2025-09-24 14:43:53,206 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:43:53,206 - INFO - Total entries: 14
2025-09-24 14:43:53,206 - INFO - ============================================================================================================================================
2025-09-24 14:43:53,206 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:43:53,206 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:43:53,206 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 14:43:53,206 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 14:43:53,206 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 14:43:53,206 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,206 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,206 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,207 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,207 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,207 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,207 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 14:43:53,207 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 14:43:53,207 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 14:43:53,207 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 14:43:53,207 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → log             | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 14:43:53,207 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:43:53,207 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 23.080812, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
