2025-09-24 18:39:48,103 - INFO - Logging initialized. Log file: logs/test_classification_20250924_183948.log
2025-09-24 18:39:48,103 - INFO - 📁 Found 8 files to process
2025-09-24 18:39:48,103 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:39:48,103 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 18:39:48,103 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 18:39:48,103 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 18:39:48,103 - INFO - ⬆️ [18:39:48] Uploading: CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:39:50,686 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/CGEBXHEWWZJUW5X1DMTQ.pdf -> s3://document-extraction-logistically/temp/9039f665_CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:39:50,686 - INFO - 🔍 [18:39:50] Starting classification: CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:39:50,687 - INFO - ⬆️ [18:39:50] Uploading: CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:39:50,688 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:50,709 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:50,715 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9039f665_CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:39:50,715 - INFO - Processing PDF from S3...
2025-09-24 18:39:50,716 - INFO - Downloading PDF from S3 to /tmp/tmp1otulkwh/9039f665_CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:39:51,316 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/CKVP76ZL5R35ESXI2X8V.pdf -> s3://document-extraction-logistically/temp/bde88dd8_CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:39:51,316 - INFO - 🔍 [18:39:51] Starting classification: CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:39:51,317 - INFO - ⬆️ [18:39:51] Uploading: CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:39:51,317 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:51,333 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:51,343 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bde88dd8_CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:39:51,344 - INFO - Processing PDF from S3...
2025-09-24 18:39:51,345 - INFO - Downloading PDF from S3 to /tmp/tmpf5g4ixoh/bde88dd8_CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:39:52,064 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/CSHPL0CORX3ZIMLNPIJ9.pdf -> s3://document-extraction-logistically/temp/3a5afd77_CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:39:52,064 - INFO - 🔍 [18:39:52] Starting classification: CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:39:52,065 - INFO - ⬆️ [18:39:52] Uploading: GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:39:52,066 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:52,083 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:52,086 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3a5afd77_CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:39:52,086 - INFO - Processing PDF from S3...
2025-09-24 18:39:52,086 - INFO - Downloading PDF from S3 to /tmp/tmpcgytkrnw/3a5afd77_CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:39:52,310 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:39:52,310 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:52,311 - INFO - Splitting PDF 9039f665_CGEBXHEWWZJUW5X1DMTQ into 2 pages
2025-09-24 18:39:52,329 - INFO - Split PDF into 2 pages
2025-09-24 18:39:52,329 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:52,329 - INFO - Expected pages: [1, 2]
2025-09-24 18:39:52,621 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:52,621 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:52,622 - WARNING - incorrect startxref pointer(3)
2025-09-24 18:39:52,622 - INFO - Splitting PDF bde88dd8_CKVP76ZL5R35ESXI2X8V into 1 pages
2025-09-24 18:39:52,624 - INFO - Split PDF into 1 pages
2025-09-24 18:39:52,624 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:52,624 - INFO - Expected pages: [1]
2025-09-24 18:39:52,672 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/GT7XCRTDS1NJ3EQW28RB.pdf -> s3://document-extraction-logistically/temp/36980918_GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:39:52,673 - INFO - 🔍 [18:39:52] Starting classification: GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:39:52,674 - INFO - ⬆️ [18:39:52] Uploading: K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:39:52,675 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:52,697 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:52,702 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36980918_GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:39:52,702 - INFO - Processing PDF from S3...
2025-09-24 18:39:52,702 - INFO - Downloading PDF from S3 to /tmp/tmptebwbesr/36980918_GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:39:53,437 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/K84CC5KPSIV30IRH4YFM.pdf -> s3://document-extraction-logistically/temp/802a5720_K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:39:53,437 - INFO - 🔍 [18:39:53] Starting classification: K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:39:53,439 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:53,440 - INFO - ⬆️ [18:39:53] Uploading: S841OVW7JKQAK870531G.pdf
2025-09-24 18:39:53,454 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:53,463 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/802a5720_K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:39:53,464 - INFO - Processing PDF from S3...
2025-09-24 18:39:53,465 - INFO - Downloading PDF from S3 to /tmp/tmppgo0zxfc/802a5720_K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:39:53,776 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:53,777 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:53,778 - INFO - Splitting PDF 3a5afd77_CSHPL0CORX3ZIMLNPIJ9 into 2 pages
2025-09-24 18:39:53,782 - INFO - Split PDF into 2 pages
2025-09-24 18:39:53,782 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:53,782 - INFO - Expected pages: [1, 2]
2025-09-24 18:39:54,057 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:54,057 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:54,058 - WARNING - incorrect startxref pointer(3)
2025-09-24 18:39:54,058 - INFO - Splitting PDF 36980918_GT7XCRTDS1NJ3EQW28RB into 1 pages
2025-09-24 18:39:54,061 - INFO - Split PDF into 1 pages
2025-09-24 18:39:54,061 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:54,061 - INFO - Expected pages: [1]
2025-09-24 18:39:54,065 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/S841OVW7JKQAK870531G.pdf -> s3://document-extraction-logistically/temp/bd4b3bdb_S841OVW7JKQAK870531G.pdf
2025-09-24 18:39:54,065 - INFO - 🔍 [18:39:54] Starting classification: S841OVW7JKQAK870531G.pdf
2025-09-24 18:39:54,065 - INFO - ⬆️ [18:39:54] Uploading: XMNE2C5G4G8Z8UOIR2N4.pdf
2025-09-24 18:39:54,067 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:54,082 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:54,084 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bd4b3bdb_S841OVW7JKQAK870531G.pdf
2025-09-24 18:39:54,084 - INFO - Processing PDF from S3...
2025-09-24 18:39:54,084 - INFO - Downloading PDF from S3 to /tmp/tmpyr98731k/bd4b3bdb_S841OVW7JKQAK870531G.pdf
2025-09-24 18:39:54,680 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:54,680 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:54,682 - INFO - Splitting PDF 802a5720_K84CC5KPSIV30IRH4YFM into 1 pages
2025-09-24 18:39:54,684 - INFO - Split PDF into 1 pages
2025-09-24 18:39:54,686 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:54,688 - INFO - Expected pages: [1]
2025-09-24 18:39:54,688 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/XMNE2C5G4G8Z8UOIR2N4.pdf -> s3://document-extraction-logistically/temp/44713d57_XMNE2C5G4G8Z8UOIR2N4.pdf
2025-09-24 18:39:54,689 - INFO - 🔍 [18:39:54] Starting classification: XMNE2C5G4G8Z8UOIR2N4.pdf
2025-09-24 18:39:54,694 - INFO - ⬆️ [18:39:54] Uploading: YJFD47SFUPDUAFXOG6RA.pdf
2025-09-24 18:39:54,696 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:54,744 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:54,747 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/44713d57_XMNE2C5G4G8Z8UOIR2N4.pdf
2025-09-24 18:39:54,747 - INFO - Processing PDF from S3...
2025-09-24 18:39:54,747 - INFO - Downloading PDF from S3 to /tmp/tmpp_fij420/44713d57_XMNE2C5G4G8Z8UOIR2N4.pdf
2025-09-24 18:39:55,269 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:55,270 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:55,271 - INFO - Splitting PDF bd4b3bdb_S841OVW7JKQAK870531G into 1 pages
2025-09-24 18:39:55,272 - INFO - Split PDF into 1 pages
2025-09-24 18:39:55,272 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:55,273 - INFO - Expected pages: [1]
2025-09-24 18:39:55,434 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/tender_from_cust/YJFD47SFUPDUAFXOG6RA.pdf -> s3://document-extraction-logistically/temp/7b79b19a_YJFD47SFUPDUAFXOG6RA.pdf
2025-09-24 18:39:55,435 - INFO - 🔍 [18:39:55] Starting classification: YJFD47SFUPDUAFXOG6RA.pdf
2025-09-24 18:39:55,436 - INFO - Initializing TextractProcessor...
2025-09-24 18:39:55,448 - INFO - Initializing BedrockProcessor...
2025-09-24 18:39:55,452 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7b79b19a_YJFD47SFUPDUAFXOG6RA.pdf
2025-09-24 18:39:55,453 - INFO - Processing PDF from S3...
2025-09-24 18:39:55,453 - INFO - Downloading PDF from S3 to /tmp/tmpk3ftrupj/7b79b19a_YJFD47SFUPDUAFXOG6RA.pdf
2025-09-24 18:39:56,178 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:56,179 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:56,181 - INFO - Splitting PDF 44713d57_XMNE2C5G4G8Z8UOIR2N4 into 2 pages
2025-09-24 18:39:56,190 - INFO - Split PDF into 2 pages
2025-09-24 18:39:56,190 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:56,190 - INFO - Expected pages: [1, 2]
2025-09-24 18:39:56,578 - INFO - Page 2: Extracted 972 characters, 43 lines from 9039f665_CGEBXHEWWZJUW5X1DMTQ_3e592938_page_002.pdf
2025-09-24 18:39:56,579 - INFO - Successfully processed page 2
2025-09-24 18:39:56,953 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:39:56,953 - INFO - Splitting PDF into individual pages...
2025-09-24 18:39:56,955 - INFO - Splitting PDF 7b79b19a_YJFD47SFUPDUAFXOG6RA into 2 pages
2025-09-24 18:39:56,961 - INFO - Split PDF into 2 pages
2025-09-24 18:39:56,961 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:39:56,961 - INFO - Expected pages: [1, 2]
2025-09-24 18:39:57,562 - INFO - Page 2: Extracted 587 characters, 21 lines from 3a5afd77_CSHPL0CORX3ZIMLNPIJ9_cf936e9c_page_002.pdf
2025-09-24 18:39:57,563 - INFO - Successfully processed page 2
2025-09-24 18:39:57,656 - INFO - Page 1: Extracted 1756 characters, 112 lines from 9039f665_CGEBXHEWWZJUW5X1DMTQ_3e592938_page_001.pdf
2025-09-24 18:39:57,657 - INFO - Successfully processed page 1
2025-09-24 18:39:57,657 - INFO - Combined 2 pages into final text
2025-09-24 18:39:57,657 - INFO - Text validation for 9039f665_CGEBXHEWWZJUW5X1DMTQ: 2764 characters, 2 pages
2025-09-24 18:39:57,657 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:39:57,657 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:39:57,922 - INFO - Page 1: Extracted 2019 characters, 90 lines from bde88dd8_CKVP76ZL5R35ESXI2X8V_68233599_page_001.pdf
2025-09-24 18:39:57,922 - INFO - Successfully processed page 1
2025-09-24 18:39:57,922 - INFO - Combined 1 pages into final text
2025-09-24 18:39:57,922 - INFO - Text validation for bde88dd8_CKVP76ZL5R35ESXI2X8V: 2036 characters, 1 pages
2025-09-24 18:39:57,923 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:39:57,923 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:39:58,265 - INFO - Page 1: Extracted 1424 characters, 73 lines from 3a5afd77_CSHPL0CORX3ZIMLNPIJ9_cf936e9c_page_001.pdf
2025-09-24 18:39:58,266 - INFO - Successfully processed page 1
2025-09-24 18:39:58,266 - INFO - Combined 2 pages into final text
2025-09-24 18:39:58,266 - INFO - Text validation for 3a5afd77_CSHPL0CORX3ZIMLNPIJ9: 2047 characters, 2 pages
2025-09-24 18:39:58,267 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:39:58,267 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:39:59,340 - INFO - Page 1: Extracted 1743 characters, 94 lines from 802a5720_K84CC5KPSIV30IRH4YFM_a93d4072_page_001.pdf
2025-09-24 18:39:59,340 - INFO - Successfully processed page 1
2025-09-24 18:39:59,340 - INFO - Combined 1 pages into final text
2025-09-24 18:39:59,340 - INFO - Text validation for 802a5720_K84CC5KPSIV30IRH4YFM: 1760 characters, 1 pages
2025-09-24 18:39:59,341 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:39:59,341 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:39:59,691 - INFO - Page 1: Extracted 2018 characters, 91 lines from 36980918_GT7XCRTDS1NJ3EQW28RB_73ba5765_page_001.pdf
2025-09-24 18:39:59,691 - INFO - Successfully processed page 1
2025-09-24 18:39:59,692 - INFO - Combined 1 pages into final text
2025-09-24 18:39:59,692 - INFO - Text validation for 36980918_GT7XCRTDS1NJ3EQW28RB: 2035 characters, 1 pages
2025-09-24 18:39:59,692 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:39:59,692 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:39:59,841 - INFO - Page 1: Extracted 1372 characters, 88 lines from bd4b3bdb_S841OVW7JKQAK870531G_a47e7779_page_001.pdf
2025-09-24 18:39:59,841 - INFO - Successfully processed page 1
2025-09-24 18:39:59,841 - INFO - Combined 1 pages into final text
2025-09-24 18:39:59,842 - INFO - Text validation for bd4b3bdb_S841OVW7JKQAK870531G: 1389 characters, 1 pages
2025-09-24 18:39:59,842 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:39:59,842 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:39:59,956 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9659e2b1-53ab-4a33-ad88-68e8c78aed04', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:09:59 GMT', 'content-type': 'application/json', 'content-length': '815', 'connection': 'keep-alive', 'x-amzn-requestid': '9659e2b1-53ab-4a33-ad88-68e8c78aed04'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to determine doc_type. The page contains BOL Manifest, BOL Manifest Nbr, details about truck, pallet, weight, etc. This looks like a Bill of Lading Manifest (BOL Manifest). So doc_type likely "bol". It\'s not invoice, not shipment invoice. It has a BOL Manifest header. The doc_type enum includes "bol" for Bill of Lading. So we classify as bol. Also ensure that it\'s not combined_carrier_documents. It\'s just one page. So output: page_no 1, doc_type "bol". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3182, 'outputTokens': 141, 'totalTokens': 3323}, 'metrics': {'latencyMs': 807}}
2025-09-24 18:39:59,957 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bde88dd8_CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:39:59,991 - INFO - 

CKVP76ZL5R35ESXI2X8V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:39:59,991 - INFO - 

✓ Saved result: output/run1_CKVP76ZL5R35ESXI2X8V.json
2025-09-24 18:40:00,291 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bde88dd8_CKVP76ZL5R35ESXI2X8V.pdf
2025-09-24 18:40:00,294 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '018b5c92-ba35-4e1b-955d-976893ab22d3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:10:00 GMT', 'content-type': 'application/json', 'content-length': '1567', 'connection': 'keep-alive', 'x-amzn-requestid': '018b5c92-ba35-4e1b-955d-976893ab22d3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be an "Oshkosh NGDV POM Inbound Load Book Confirmation". This looks like a load confirmation from a customer to a carrier, maybe a tender. The header: "Oshkosh NGDV POM Inbound Load Book Confirmation". It\'s a load confirmation. According to definitions: tender_from_cust — Load Tender from Customer. Keywords: Load Tender, Tender, Request to Carrier. This seems to be a load confirmation. It\'s basically a load book confirmation, not a POD or invoice. So likely tender_from_cust.\n\nCheck if any other type: "Load Book Confirmation" is basically a tender. So classify as tender_from_cust.\n\nNeed to ensure there\'s no continuation. Both pages are part of same doc. Page2 continues the content. There\'s no "continued" text but it\'s a continuation. We need to check: The instructions say if a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page). So we output two objects: page 1 doc_type tender_from_cust, page 2 same.\n\nWe need to use the tool classify_logistics_doc_type. Provide documents array with page_no and doc_type. The output must be via the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3457, 'outputTokens': 300, 'totalTokens': 3757}, 'metrics': {'latencyMs': 1485}}
2025-09-24 18:40:00,295 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9039f665_CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:40:00,332 - INFO - 

CGEBXHEWWZJUW5X1DMTQ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 18:40:00,332 - INFO - 

✓ Saved result: output/run1_CGEBXHEWWZJUW5X1DMTQ.json
2025-09-24 18:40:00,628 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9039f665_CGEBXHEWWZJUW5X1DMTQ.pdf
2025-09-24 18:40:00,862 - INFO - Page 2: Extracted 530 characters, 21 lines from 7b79b19a_YJFD47SFUPDUAFXOG6RA_d2b66a60_page_002.pdf
2025-09-24 18:40:00,862 - INFO - Successfully processed page 2
2025-09-24 18:40:00,879 - INFO - Page 1: Extracted 1570 characters, 75 lines from 44713d57_XMNE2C5G4G8Z8UOIR2N4_e3f7c2fc_page_001.pdf
2025-09-24 18:40:00,879 - INFO - Successfully processed page 1
2025-09-24 18:40:01,152 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fd0a9224-fe98-4f81-9caa-5670283e47d2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:10:01 GMT', 'content-type': 'application/json', 'content-length': '666', 'connection': 'keep-alive', 'x-amzn-requestid': 'fd0a9224-fe98-4f81-9caa-5670283e47d2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page present. The document is a carrier load tender. Keywords: "CARRIER LOAD TENDER", "LOAD TENDER". This matches tender_from_cust definition: Load Tender from Customer. So doc_type tender_from_cust. Provide array with single object: page_no 1, doc_type "tender_from_cust".\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3184, 'outputTokens': 100, 'totalTokens': 3284}, 'metrics': {'latencyMs': 656}}
2025-09-24 18:40:01,153 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/802a5720_K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:40:01,169 - INFO - Page 2: Extracted 769 characters, 25 lines from 44713d57_XMNE2C5G4G8Z8UOIR2N4_e3f7c2fc_page_002.pdf
2025-09-24 18:40:01,170 - INFO - Successfully processed page 2
2025-09-24 18:40:01,171 - INFO - Combined 2 pages into final text
2025-09-24 18:40:01,172 - INFO - Text validation for 44713d57_XMNE2C5G4G8Z8UOIR2N4: 2375 characters, 2 pages
2025-09-24 18:40:01,181 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:40:01,184 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:40:01,198 - INFO - 

K84CC5KPSIV30IRH4YFM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 18:40:01,198 - INFO - 

✓ Saved result: output/run1_K84CC5KPSIV30IRH4YFM.json
2025-09-24 18:40:01,490 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/802a5720_K84CC5KPSIV30IRH4YFM.pdf
2025-09-24 18:40:01,708 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dddc50c2-d2f3-42cb-9fb7-6f034651213f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:10:01 GMT', 'content-type': 'application/json', 'content-length': '845', 'connection': 'keep-alive', 'x-amzn-requestid': 'dddc50c2-d2f3-42cb-9fb7-6f034651213f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify into a doc_type. The content shows BOL Manifest, includes BOL number, truck, trailer, origin/destination. This appears to be a BOL Manifest. The doc_type enum includes "bol". Also there is a "BOL Manifest Nbr: 106980941". So likely type is bol. Check if there are any other doc types. There\'s mention of "Carrier Rate" maybe. But main header: "BOL Manifest". So bol.\n\nWe must return tool call with documents array containing one object: page_no: 1, doc_type: "bol".\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3182, 'outputTokens': 147, 'totalTokens': 3329}, 'metrics': {'latencyMs': 839}}
2025-09-24 18:40:01,709 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36980918_GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:40:01,714 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '36c7e79d-3770-4e04-822e-00fac5a98f5a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:10:01 GMT', 'content-type': 'application/json', 'content-length': '611', 'connection': 'keep-alive', 'x-amzn-requestid': '36c7e79d-3770-4e04-822e-00fac5a98f5a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. The page text shows "CARRIER LOAD TENDER" header, "Load Tender" etc. So it\'s tender_from_cust. So doc_type tender_from_cust. Output format: use tool classify_logistics_doc_type with array of objects. page_no 1, doc_type tender_from_cust.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3025, 'outputTokens': 93, 'totalTokens': 3118}, 'metrics': {'latencyMs': 629}}
2025-09-24 18:40:01,716 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bd4b3bdb_S841OVW7JKQAK870531G.pdf
2025-09-24 18:40:01,762 - INFO - 

GT7XCRTDS1NJ3EQW28RB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:40:01,762 - INFO - 

✓ Saved result: output/run1_GT7XCRTDS1NJ3EQW28RB.json
2025-09-24 18:40:01,846 - INFO - Page 1: Extracted 1674 characters, 98 lines from 7b79b19a_YJFD47SFUPDUAFXOG6RA_d2b66a60_page_001.pdf
2025-09-24 18:40:01,846 - INFO - Successfully processed page 1
2025-09-24 18:40:01,847 - INFO - Combined 2 pages into final text
2025-09-24 18:40:01,847 - INFO - Text validation for 7b79b19a_YJFD47SFUPDUAFXOG6RA: 2240 characters, 2 pages
2025-09-24 18:40:01,847 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:40:01,847 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:40:02,072 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36980918_GT7XCRTDS1NJ3EQW28RB.pdf
2025-09-24 18:40:02,094 - INFO - 

S841OVW7JKQAK870531G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 18:40:02,094 - INFO - 

✓ Saved result: output/run1_S841OVW7JKQAK870531G.json
2025-09-24 18:40:02,347 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e865d2b3-7cfa-4e50-b256-22d66be561c6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:10:02 GMT', 'content-type': 'application/json', 'content-length': '3125', 'connection': 'keep-alive', 'x-amzn-requestid': 'e865d2b3-7cfa-4e50-b256-22d66be561c6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document is a "Carrier Load Tender" which is a load tender from customer? Actually the header: "Carrier Load Tender". It appears to be a tender request from the carrier? Wait "Carrier Load Tender" indicates the carrier is tendering a load to a customer. But the header says "Carrier: Aim Nationwide Services LLC" and "Bill To: PREPAID Send invoice & BOL CC to RPM C/O CASS Tariff SAINT LO, MO". This is a load tender. It\'s from the carrier to the customer? The spec: "tender_from_cust — Load Tender from Customer". This is a load tender from customer to carrier. But here it\'s "Carrier Load Tender" with a carrier listed. So is it a tender from the carrier to the customer? The definition: "Load Tender from Customer". Maybe the tool expects "tender_from_cust" if it is a tender from customer. But the header says "Carrier Load Tender" maybe it\'s from carrier. However the spec says tender_from_cust definition: "Customer\'s load tender or request to a carrier to transport a load." The header says "Carrier Load Tender", but the content shows "Carrier: Aim Nationwide Services LLC". That might be the carrier receiving the load tender? It\'s confusing. We should look at keywords: "Carrier Load Tender". This phrase appears. There\'s no "Load Tender" alone. The spec says tender_from_cust has keywords "Load Tender, Tender, Tender from, Request to Carrier". This page has "Carrier Load Tender". Not exactly "Load Tender". But the label "Carrier Load Tender" might be the type. The spec didn\'t include "Carrier Load Tender" but maybe it\'s the same as tender_from_cust. I\'m leaning to tender_from_cust. Because it\'s a tender from carrier? Actually tender_from_cust is load tender from customer to carrier. This is a load tender from carrier to customer? But the spec didn\'t include that. But likely it\'s tender_from_cust nonetheless. Alternatively it could be "order" or "po"? But it is a tender, not a purchase order. So tender_from_cust is best.\n\nThus both pages are the same document; page 2 is continuation. According to instructions, each page still gets same doc_type. So we produce two entries.\n\nCheck continuation: page 2 shows "Page 2 of 2" but not "continued". But it\'s the second page of the load tender. According to instructions: "If document is not from any of categories mentioned, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign the same doc_type as previous page." But page 2 obviously is continuation. So both pages get tender_from_cust.\n\nTherefore output array of two objects: page_no 1 doc_type \'tender_from_cust\', page_no 2 doc_type \'tender_from_cust\'.\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3317, 'outputTokens': 638, 'totalTokens': 3955}, 'metrics': {'latencyMs': 2888}}
2025-09-24 18:40:02,348 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3a5afd77_CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:40:02,415 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bd4b3bdb_S841OVW7JKQAK870531G.pdf
2025-09-24 18:40:02,458 - INFO - 

CSHPL0CORX3ZIMLNPIJ9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 18:40:02,458 - INFO - 

✓ Saved result: output/run1_CSHPL0CORX3ZIMLNPIJ9.json
2025-09-24 18:40:02,752 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3a5afd77_CSHPL0CORX3ZIMLNPIJ9.pdf
2025-09-24 18:40:03,308 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '77905b24-e874-4580-b1f0-8605de670f30', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:10:03 GMT', 'content-type': 'application/json', 'content-length': '892', 'connection': 'keep-alive', 'x-amzn-requestid': '77905b24-e874-4580-b1f0-8605de670f30'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. They are a "Carrier Load Tender". According to definitions: tender_from_cust — Load Tender from Customer. Yes. So each page has doc_type "tender_from_cust". The page 2 seems a continuation (has "Page 2 of 2"). So we should classify both as tender_from_cust.\n\nWe must output via function call classify_logistics_doc_type with array of objects: page_no and doc_type.\n\nThus output: [{page_no:1, doc_type:"tender_from_cust"}, {page_no:2, doc_type:"tender_from_cust"}].\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3369, 'outputTokens': 165, 'totalTokens': 3534}, 'metrics': {'latencyMs': 903}}
2025-09-24 18:40:03,309 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/44713d57_XMNE2C5G4G8Z8UOIR2N4.pdf
2025-09-24 18:40:03,348 - INFO - 

XMNE2C5G4G8Z8UOIR2N4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 18:40:03,348 - INFO - 

✓ Saved result: output/run1_XMNE2C5G4G8Z8UOIR2N4.json
2025-09-24 18:40:03,678 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/44713d57_XMNE2C5G4G8Z8UOIR2N4.pdf
