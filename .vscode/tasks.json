{"version": "2.0.0", "tasks": [{"label": "Run test_classification.py (<PERSON><PERSON><PERSON>)", "type": "shell", "command": "${workspaceFolder}/venv/bin/python", "args": ["${workspaceFolder}/scripts/test_classification.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction"}}, "problemMatcher": []}, {"label": "Run test_classification.py (Custom Args)", "type": "shell", "command": "${workspaceFolder}/venv/bin/python", "args": ["${workspaceFolder}/scripts/test_classification.py", "${input:folderPath}", "--max-files", "${input:maxFiles}", "--run-number", "${input:runNumber}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction"}}, "problemMatcher": []}, {"label": "Install Python Dependencies", "type": "shell", "command": "${workspaceFolder}/venv/bin/pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}], "inputs": [{"id": "folderPath", "description": "Enter the folder path to process", "default": "${workspaceFolder}/verified_docs/log", "type": "promptString"}, {"id": "maxFiles", "description": "Enter maximum number of files to process", "default": "5", "type": "promptString"}, {"id": "runNumber", "description": "Enter run number", "default": "1", "type": "promptString"}]}