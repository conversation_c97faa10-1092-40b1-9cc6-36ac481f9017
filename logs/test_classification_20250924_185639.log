2025-09-24 18:56:39,383 - INFO - Logging initialized. Log file: logs/test_classification_20250924_185639.log
2025-09-24 18:56:39,383 - INFO - 📁 Found 10 files to process
2025-09-24 18:56:39,383 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:56:39,383 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 18:56:39,383 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 18:56:39,383 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 18:56:39,383 - INFO - ⬆️ [18:56:39] Uploading: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:41,600 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf -> s3://document-extraction-logistically/temp/72ebbd92_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:41,600 - INFO - 🔍 [18:56:41] Starting classification: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:41,601 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:41,602 - INFO - ⬆️ [18:56:41] Uploading: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:41,622 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:41,628 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/72ebbd92_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:41,628 - INFO - Processing PDF from S3...
2025-09-24 18:56:41,628 - INFO - Downloading PDF from S3 to /tmp/tmpaxglawly/72ebbd92_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:42,623 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf -> s3://document-extraction-logistically/temp/a771d0eb_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:42,624 - INFO - 🔍 [18:56:42] Starting classification: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:42,625 - INFO - ⬆️ [18:56:42] Uploading: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:42,626 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:42,646 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:42,650 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a771d0eb_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:42,650 - INFO - Processing PDF from S3...
2025-09-24 18:56:42,650 - INFO - Downloading PDF from S3 to /tmp/tmpr1wdf5b3/a771d0eb_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:43,339 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf -> s3://document-extraction-logistically/temp/cd2a3633_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:43,339 - INFO - 🔍 [18:56:43] Starting classification: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:43,340 - INFO - ⬆️ [18:56:43] Uploading: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:43,342 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:43,353 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:43,355 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cd2a3633_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:43,355 - INFO - Processing PDF from S3...
2025-09-24 18:56:43,355 - INFO - Downloading PDF from S3 to /tmp/tmpqdsd89px/cd2a3633_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:43,441 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:56:43,441 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:43,442 - INFO - Splitting PDF 72ebbd92_G0H0K1LRWDOG7LXAKKQ7 into 1 pages
2025-09-24 18:56:43,446 - INFO - Split PDF into 1 pages
2025-09-24 18:56:43,446 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:43,447 - INFO - Expected pages: [1]
2025-09-24 18:56:44,044 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf -> s3://document-extraction-logistically/temp/c17aaebc_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:44,044 - INFO - 🔍 [18:56:44] Starting classification: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:44,044 - INFO - ⬆️ [18:56:44] Uploading: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:44,047 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:44,057 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:44,060 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c17aaebc_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:44,060 - INFO - Processing PDF from S3...
2025-09-24 18:56:44,061 - INFO - Downloading PDF from S3 to /tmp/tmp47ro4g2a/c17aaebc_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:44,868 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:56:44,868 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:44,870 - INFO - Splitting PDF a771d0eb_GKNF55W2CF2JR6EBXMPX into 1 pages
2025-09-24 18:56:44,875 - INFO - Split PDF into 1 pages
2025-09-24 18:56:44,875 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:44,875 - INFO - Expected pages: [1]
2025-09-24 18:56:44,912 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf -> s3://document-extraction-logistically/temp/412b4bf7_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:44,912 - INFO - 🔍 [18:56:44] Starting classification: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:44,913 - INFO - ⬆️ [18:56:44] Uploading: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:44,913 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:44,931 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:44,934 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/412b4bf7_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:44,935 - INFO - Processing PDF from S3...
2025-09-24 18:56:44,935 - INFO - Downloading PDF from S3 to /tmp/tmpjw_8gv55/412b4bf7_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:44,978 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:56:44,978 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:44,980 - INFO - Splitting PDF cd2a3633_KDFG6JJAZV41YZP4TEGN into 1 pages
2025-09-24 18:56:44,984 - INFO - Split PDF into 1 pages
2025-09-24 18:56:44,984 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:44,984 - INFO - Expected pages: [1]
2025-09-24 18:56:45,364 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:56:45,364 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:45,365 - INFO - Splitting PDF c17aaebc_MMNWUYGBLL1K5KSJBNOT into 1 pages
2025-09-24 18:56:45,366 - INFO - Split PDF into 1 pages
2025-09-24 18:56:45,366 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:45,367 - INFO - Expected pages: [1]
2025-09-24 18:56:45,567 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf -> s3://document-extraction-logistically/temp/675c1678_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:45,567 - INFO - 🔍 [18:56:45] Starting classification: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:45,568 - INFO - ⬆️ [18:56:45] Uploading: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:45,571 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:45,577 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:45,579 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/675c1678_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:45,579 - INFO - Processing PDF from S3...
2025-09-24 18:56:45,579 - INFO - Downloading PDF from S3 to /tmp/tmpnll3wpkq/675c1678_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:46,202 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf -> s3://document-extraction-logistically/temp/b7440965_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:46,202 - INFO - 🔍 [18:56:46] Starting classification: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:46,203 - INFO - ⬆️ [18:56:46] Uploading: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:46,203 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:46,224 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:46,256 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b7440965_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:46,256 - INFO - Processing PDF from S3...
2025-09-24 18:56:46,257 - INFO - Downloading PDF from S3 to /tmp/tmpyi1_evg2/b7440965_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:46,906 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf -> s3://document-extraction-logistically/temp/4dcbcd75_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:46,907 - INFO - 🔍 [18:56:46] Starting classification: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:46,908 - INFO - ⬆️ [18:56:46] Uploading: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:46,909 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:46,930 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:46,934 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4dcbcd75_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:46,934 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:56:46,935 - INFO - Processing PDF from S3...
2025-09-24 18:56:46,935 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:46,935 - INFO - Downloading PDF from S3 to /tmp/tmp47d7tn47/4dcbcd75_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:46,940 - INFO - Splitting PDF 675c1678_O1YJBQBLYAU6D0SDDKAU into 1 pages
2025-09-24 18:56:46,942 - INFO - Split PDF into 1 pages
2025-09-24 18:56:46,942 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:46,942 - INFO - Expected pages: [1]
2025-09-24 18:56:47,079 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:56:47,079 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:47,080 - INFO - Splitting PDF 412b4bf7_NFMA1926AJ8R3TDZQALU into 1 pages
2025-09-24 18:56:47,081 - INFO - Split PDF into 1 pages
2025-09-24 18:56:47,081 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:47,081 - INFO - Expected pages: [1]
2025-09-24 18:56:47,872 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:56:47,872 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:47,873 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg -> s3://document-extraction-logistically/temp/c846733f_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:47,873 - INFO - 🔍 [18:56:47] Starting classification: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:47,875 - INFO - Splitting PDF b7440965_OLQ7TIWW6EVTC6BXA1II into 1 pages
2025-09-24 18:56:47,875 - INFO - ⬆️ [18:56:47] Uploading: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:47,883 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:47,892 - INFO - Split PDF into 1 pages
2025-09-24 18:56:47,893 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:47,893 - INFO - Expected pages: [1]
2025-09-24 18:56:47,906 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:47,917 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c846733f_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:47,918 - INFO - Processing image from S3...
2025-09-24 18:56:48,269 - INFO - Page 1: Extracted 667 characters, 55 lines from 72ebbd92_G0H0K1LRWDOG7LXAKKQ7_9bbddc3b_page_001.pdf
2025-09-24 18:56:48,269 - INFO - Successfully processed page 1
2025-09-24 18:56:48,269 - INFO - Combined 1 pages into final text
2025-09-24 18:56:48,269 - INFO - Text validation for 72ebbd92_G0H0K1LRWDOG7LXAKKQ7: 684 characters, 1 pages
2025-09-24 18:56:48,270 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:48,270 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:48,762 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:56:48,763 - INFO - Splitting PDF into individual pages...
2025-09-24 18:56:48,763 - INFO - Splitting PDF 4dcbcd75_V3BCW6BW9XVKKO6WY2YJ into 1 pages
2025-09-24 18:56:48,764 - INFO - Split PDF into 1 pages
2025-09-24 18:56:48,764 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:56:48,764 - INFO - Expected pages: [1]
2025-09-24 18:56:48,787 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg -> s3://document-extraction-logistically/temp/fc004b50_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:48,787 - INFO - 🔍 [18:56:48] Starting classification: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:48,787 - INFO - Initializing TextractProcessor...
2025-09-24 18:56:48,803 - INFO - Initializing BedrockProcessor...
2025-09-24 18:56:48,806 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fc004b50_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:48,806 - INFO - Processing image from S3...
2025-09-24 18:56:49,110 - INFO - Page 1: Extracted 695 characters, 58 lines from cd2a3633_KDFG6JJAZV41YZP4TEGN_0a9b79fa_page_001.pdf
2025-09-24 18:56:49,111 - INFO - Successfully processed page 1
2025-09-24 18:56:49,111 - INFO - Combined 1 pages into final text
2025-09-24 18:56:49,111 - INFO - Text validation for cd2a3633_KDFG6JJAZV41YZP4TEGN: 712 characters, 1 pages
2025-09-24 18:56:49,112 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:49,112 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:49,294 - INFO - Page 1: Extracted 493 characters, 42 lines from a771d0eb_GKNF55W2CF2JR6EBXMPX_dc7abac3_page_001.pdf
2025-09-24 18:56:49,295 - INFO - Successfully processed page 1
2025-09-24 18:56:49,295 - INFO - Combined 1 pages into final text
2025-09-24 18:56:49,295 - INFO - Text validation for a771d0eb_GKNF55W2CF2JR6EBXMPX: 510 characters, 1 pages
2025-09-24 18:56:49,296 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:49,296 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:49,382 - INFO - Page 1: Extracted 372 characters, 28 lines from c17aaebc_MMNWUYGBLL1K5KSJBNOT_1bb31ec2_page_001.pdf
2025-09-24 18:56:49,383 - INFO - Successfully processed page 1
2025-09-24 18:56:49,383 - INFO - Combined 1 pages into final text
2025-09-24 18:56:49,383 - INFO - Text validation for c17aaebc_MMNWUYGBLL1K5KSJBNOT: 389 characters, 1 pages
2025-09-24 18:56:49,384 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:49,384 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:50,080 - INFO - Page 1: Extracted 311 characters, 32 lines from 675c1678_O1YJBQBLYAU6D0SDDKAU_6d1d85df_page_001.pdf
2025-09-24 18:56:50,081 - INFO - Successfully processed page 1
2025-09-24 18:56:50,081 - INFO - Combined 1 pages into final text
2025-09-24 18:56:50,082 - INFO - Text validation for 675c1678_O1YJBQBLYAU6D0SDDKAU: 328 characters, 1 pages
2025-09-24 18:56:50,082 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:50,083 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:51,055 - INFO - S3 Image temp/c846733f_Y9K6Z2GSAP496UQLOOGU.jpeg: Extracted 542 characters, 30 lines
2025-09-24 18:56:51,055 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:51,056 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:51,323 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ba512b2d-a002-4529-b05d-1de890a6a669', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:51 GMT', 'content-type': 'application/json', 'content-length': '1833', 'connection': 'keep-alive', 'x-amzn-requestid': 'ba512b2d-a002-4529-b05d-1de890a6a669'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to categorize.\n\nLet\'s analyze the content. It appears to be a receipt, maybe a carrier receipt. It says "Receipt #:", "Bill Code:", "Carrier: BLUE STAR", "Purchase Orders", "Total Initial Pallets", "Total Finished Pallets", "Total Case Count", "Total Weight", "Base Charge", "Convenience Fee", "Total Cost", "Payments". This looks like a lumper receipt? Might be a "lumper receipt" for loading/unloading labor? However lumper receipts include PO, door number, date-time, initial pallets, finished pallets, carrier, service charge, total amount. This page includes many of those. It also includes "BOL:" but no BOL number? It has "Comments", "Canned Comments", "Unloaders: 2". That seems like a POD? But no signature or delivered info. It\'s a receipt for the carrier\'s charges.\n\nWe also have "Receipt #:" maybe a receipt from the carrier? This could be a "lumper receipt" used by carriers to bill for loading/unloading.\n\nThus doc_type likely lumper_receipt.\n\nCheck differences: lumper receipt is "Invoice or Receipt for services provided (loading/unloading labor)". It says "PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount". This page has "Purchase Orders" and "Door:2", "Work Date:2025-09-19". It has "Carrier: BLUE STAR", "Base Charge 180.00", "Convenience Fee 10.00", "Total Cost 190.00". So yes, lumper receipt.\n\nThus output: page 1, doc_type: lumper_receipt.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2837, 'outputTokens': 379, 'totalTokens': 3216}, 'metrics': {'latencyMs': 1808}}
2025-09-24 18:56:51,324 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/72ebbd92_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:51,342 - INFO - 

G0H0K1LRWDOG7LXAKKQ7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:51,342 - INFO - 

✓ Saved result: output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:56:51,498 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6ff7d47f-291e-436c-858d-e6544963b35e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:51 GMT', 'content-type': 'application/json', 'content-length': '983', 'connection': 'keep-alive', 'x-amzn-requestid': '6ff7d47f-291e-436c-858d-e6544963b35e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Need to determine doc_type. Text shows a receipt? It mentions "BOL Number" and "PO #", "Paid By", "Payment Method visa", "Services" with pallets, convenience fee, grand total. This looks like a "lumper_receipt" (invoice for loading/unloading labor). The description says lumper_receipt: keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. Here we have PO #, services, pallets, convenience fee, total. So lumper_receipt. Also could be an invoice but since it has PO and service charge, lumper_receipt. So output page 1 doc_type lumper_receipt.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2725, 'outputTokens': 177, 'totalTokens': 2902}, 'metrics': {'latencyMs': 930}}
2025-09-24 18:56:51,499 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c17aaebc_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:51,600 - INFO - Page 1: Extracted 460 characters, 36 lines from 412b4bf7_NFMA1926AJ8R3TDZQALU_d410dc70_page_001.pdf
2025-09-24 18:56:51,600 - INFO - Successfully processed page 1
2025-09-24 18:56:51,601 - INFO - Combined 1 pages into final text
2025-09-24 18:56:51,601 - INFO - Text validation for 412b4bf7_NFMA1926AJ8R3TDZQALU: 477 characters, 1 pages
2025-09-24 18:56:51,602 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:51,602 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:51,628 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9c5485d4-221e-4254-9796-a06e867d10ae', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:51 GMT', 'content-type': 'application/json', 'content-length': '1205', 'connection': 'keep-alive', 'x-amzn-requestid': '9c5485d4-221e-4254-9796-a06e867d10ae'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content looks like a receipt or invoice? It has Purchase Orders, Vendor, Total Initial Pallets, Finished Pallets, Trailer Number, etc. It includes "Carrier: DREAM LAND" "Bill Code: RCOD30274" "Total Cost: 460.00" "Payments: CapstonePay". This seems like a carrier invoice or receipt? It says "Receipt #" and "Payments". Could be an invoice. There\'s no HS codes, so not comm_invoice. No mention of loading/unloading labor? It has "Unloaders:1" and "Add Fee". Could be lumper_receipt? But lumper receipt is for loading/unloading labor. This has some fees and charges but seems more like a general invoice for freight charges. The presence of "Bill Code" and "Total Cost" suggests a carrier invoice. So classify as invoice.\n\nReturn JSON with documents: [{"page_no": 1, "doc_type":"invoice"}]'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "invoice"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2852, 'outputTokens': 231, 'totalTokens': 3083}, 'metrics': {'latencyMs': 1219}}
2025-09-24 18:56:51,628 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cd2a3633_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:51,658 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/72ebbd92_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:51,673 - INFO - 

MMNWUYGBLL1K5KSJBNOT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:51,674 - INFO - 

✓ Saved result: output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:56:51,730 - INFO - Page 1: Extracted 657 characters, 54 lines from b7440965_OLQ7TIWW6EVTC6BXA1II_839f4b4f_page_001.pdf
2025-09-24 18:56:51,730 - INFO - Successfully processed page 1
2025-09-24 18:56:51,731 - INFO - Combined 1 pages into final text
2025-09-24 18:56:51,731 - INFO - Text validation for b7440965_OLQ7TIWW6EVTC6BXA1II: 674 characters, 1 pages
2025-09-24 18:56:51,732 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:51,732 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:51,753 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bca0650c-d9d0-4708-8701-be3691ff6d5d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:51 GMT', 'content-type': 'application/json', 'content-length': '1334', 'connection': 'keep-alive', 'x-amzn-requestid': 'bca0650c-d9d0-4708-8701-be3691ff6d5d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to parse content. It\'s a receipt/invoice? It says "Subtotal", "Convenience Fee", "Total Charge". No explicit header like "Invoice". But it\'s a billing type, likely invoice. But check differences: It\'s from IDSH "Innovative Distribution Services" - maybe a billing receipt. It includes PO(s), Vendor(s), etc. No HS code, no customs. So likely "invoice". Not a lumper receipt because no door number? It has door number 20. But lumper receipt includes PO, door number, date-time, initial pallets, finished pallets, service charge, total amount. This page has PO(s): 2644663. It has door number. It has driver name. Could be lumper receipt. But lumper receipt is for loading/unloading labor. This seems more like a billing for a relay checkout. The presence of "Billing Type: Relay Checkout" suggests it\'s an invoice from a relay payment system. So classify as "invoice". So output JSON with page 1, doc_type: "invoice".\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2767, 'outputTokens': 248, 'totalTokens': 3015}, 'metrics': {'latencyMs': 1169}}
2025-09-24 18:56:51,753 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a771d0eb_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:51,972 - INFO - S3 Image temp/fc004b50_ZOBA55ELUA2GRE3UJM3I.jpg: Extracted 709 characters, 59 lines
2025-09-24 18:56:51,972 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:51,972 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:51,976 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c17aaebc_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:51,992 - INFO - 

KDFG6JJAZV41YZP4TEGN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:56:51,992 - INFO - 

✓ Saved result: output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:56:52,292 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cd2a3633_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:52,305 - INFO - 

GKNF55W2CF2JR6EBXMPX.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:56:52,305 - INFO - 

✓ Saved result: output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:56:52,339 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b8bdf1f5-0877-41ed-8278-1f210eb597b0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:52 GMT', 'content-type': 'application/json', 'content-length': '1067', 'connection': 'keep-alive', 'x-amzn-requestid': 'b8bdf1f5-0877-41ed-8278-1f210eb597b0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Here we have only page1 content. We need to determine doc_type. The text shows Receipt, Payment, PO#, Carrier, Activity, etc. This looks like a lumper receipt? Let\'s review: lumper receipt keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO#, Door, Carrier, Activity: Total Unloading, Fee, Amount, Convenience Fee. It is a receipt for loading/unloading labor. So lumper_receipt. Also invoice? But invoice would have more billing details. This is a receipt. So doc_type = lumper_receipt.\n\nWe output:\n\n{\n "documents": [\n   {"page_no": 1, "doc_type": "lumper_receipt"}\n ]\n}\n\nWe must use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2711, 'outputTokens': 196, 'totalTokens': 2907}, 'metrics': {'latencyMs': 1007}}
2025-09-24 18:56:52,339 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/675c1678_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:52,596 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a771d0eb_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:52,604 - INFO - 

O1YJBQBLYAU6D0SDDKAU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:52,604 - INFO - 

✓ Saved result: output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:56:52,774 - INFO - Page 1: Extracted 343 characters, 28 lines from 4dcbcd75_V3BCW6BW9XVKKO6WY2YJ_4950b1b2_page_001.pdf
2025-09-24 18:56:52,774 - INFO - Successfully processed page 1
2025-09-24 18:56:52,774 - INFO - Combined 1 pages into final text
2025-09-24 18:56:52,774 - INFO - Text validation for 4dcbcd75_V3BCW6BW9XVKKO6WY2YJ: 360 characters, 1 pages
2025-09-24 18:56:52,775 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:56:52,775 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:56:52,901 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/675c1678_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:53,478 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ce9fc411-8601-43ec-aff6-b9092b9d73b3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:53 GMT', 'content-type': 'application/json', 'content-length': '1360', 'connection': 'keep-alive', 'x-amzn-requestid': 'ce9fc411-8601-43ec-aff6-b9092b9d73b3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We should decide doc_type. Text suggests receipt. Contains receipt#, base charge, total due, payment. Also PO, driver, etc. This looks like "lumper receipt" or "invoice". Let\'s differentiate.\n\nLumper receipt: Definition: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page includes PO, door/dock, driver, pallets start/finish, base charge, processing fee, total due. So lumper receipt likely. Also invoice: Bill issued by a carrier for goods; but includes amounts but not goods details? This includes truck details. Lumper receipt matches. So doc_type: lumper_receipt.\n\nCheck if any other doc type fits: invoice. But invoice vs lumper receipt: lumper receipt has "service charge" like base charge, processing fee. This is a service receipt. So lumper_receipt.\n\nThus output JSON with page 1.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "lumper_receipt"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2816, 'outputTokens': 252, 'totalTokens': 3068}, 'metrics': {'latencyMs': 1174}}
2025-09-24 18:56:53,479 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c846733f_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:53,495 - INFO - 

Y9K6Z2GSAP496UQLOOGU.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:53,495 - INFO - 

✓ Saved result: output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:56:53,762 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b53b984d-9c1b-428f-b35c-defebc11c4f7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:53 GMT', 'content-type': 'application/json', 'content-length': '818', 'connection': 'keep-alive', 'x-amzn-requestid': 'b53b984d-9c1b-428f-b35c-defebc11c4f7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have single page. Need to classify. Let\'s examine content. It seems to be a receipt or invoice. Contains "Receipt #", "Bill Code", "Carrier", "Total Finished Pallets", "Total Weight", "Base Charge", "Convenience Fee", "Total Cost". Also "Payments: Amount CapstonePay-". This appears to be a carrier invoice or fee receipt. No HS codes, no commercial invoice. So likely "invoice". It is a carrier invoice. So doc_type: "invoice". Output JSON with documents array with page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2834, 'outputTokens': 132, 'totalTokens': 2966}, 'metrics': {'latencyMs': 808}}
2025-09-24 18:56:53,763 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b7440965_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:53,847 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c846733f_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:53,857 - INFO - 

OLQ7TIWW6EVTC6BXA1II.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:56:53,857 - INFO - 

✓ Saved result: output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:56:54,147 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b7440965_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:54,406 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '38c482ef-6a43-4a9c-8cc7-1520ba31f6e5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:54 GMT', 'content-type': 'application/json', 'content-length': '1589', 'connection': 'keep-alive', 'x-amzn-requestid': '38c482ef-6a43-4a9c-8cc7-1520ba31f6e5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this page. The content seems like a form for a freight handler, with PO, vendor name, etc. It includes "Load No", "Tractor No", "Door No", "Trailer No", "Payment Type", "Driver Name", "Date", "PO No", "Vendor Name", "Vendor No", "Qty", "Weight", "Charge", "Fee". This looks like a receipt or invoice for freight services. However, it\'s not necessarily a commercial invoice because not seeing HS codes or customs info. Could be a lumper receipt? Lumper receipts include PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. That seems close. The page may be a lumper receipt form. There\'s no mention of "Service Charge" but "Charge" and "Fee". There\'s PO no, driver name, date, load no. I think lumper receipt is appropriate.\n\nCheck if other types might fit: invoice - general carrier invoice. The presence of PO No, driver name, date, and charges. But lumper receipt is more specific. The instructions mention that if invoice has some keywords for lumper receipt like PO, door number, driver etc then lumper receipt. Yes. So classify as lumper_receipt.\n\nThus output:\n\n{\n  "documents": [\n    {"page_no": 1, "doc_type": "lumper_receipt"}\n  ]\n}\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2768, 'outputTokens': 312, 'totalTokens': 3080}, 'metrics': {'latencyMs': 1564}}
2025-09-24 18:56:54,406 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/412b4bf7_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:54,418 - INFO - 

NFMA1926AJ8R3TDZQALU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:54,418 - INFO - 

✓ Saved result: output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:56:54,759 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/412b4bf7_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:55,188 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2bbec784-76c9-4b9f-9b40-cbd74a623cbb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:55 GMT', 'content-type': 'application/json', 'content-length': '1101', 'connection': 'keep-alive', 'x-amzn-requestid': '2bbec784-76c9-4b9f-9b40-cbd74a623cbb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to determine doc_type.\n\nText looks like receipt, not invoice. Keywords: "Receipt number", "GENERAL RECEIPT", "CASHIER NAME", "Payment Method: Cash". This likely a lumper receipt? The lumper receipt definition: Invoice or receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This receipt is for 5plts. It shows quantity, price, subtotal, total. Looks like a lumper receipt. There\'s no invoice, it\'s a receipt. It\'s a "Receipt" for loading/unloading. So doc_type: lumper_receipt.\n\nThus output JSON: page 1 lumper_receipt.'}}}, {'text': '{\n    "documents": [\n        {\n            "page_no": 1,\n            "doc_type": "lumper_receipt"\n        }\n    ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2722, 'outputTokens': 199, 'totalTokens': 2921}, 'metrics': {'latencyMs': 1052}}
2025-09-24 18:56:55,188 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4dcbcd75_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:55,203 - INFO - 

V3BCW6BW9XVKKO6WY2YJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:55,203 - INFO - 

✓ Saved result: output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:56:55,242 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b1528c87-54bf-46bd-b670-c4e303c07a75', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:26:55 GMT', 'content-type': 'application/json', 'content-length': '2093', 'connection': 'keep-alive', 'x-amzn-requestid': 'b1528c87-54bf-46bd-b670-c4e303c07a75'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1 content. Need to determine document type. Let\'s analyze. We have Capstone Logistics header. There\'s Receipt #, Work Date, Bill Code, Carrier: FLAWLESS, Dock: DRY, Door:4. Purchase Orders, Vendor: 4511545251 ANCHOR PACKAGING. Total initial pallets, total finished pallets, case count. Trailer number, Tractor number, BOL: Comments, etc. There\'s Add Fee, PO: 4511545251, Total Add Charges, Base Charge, Convenience Fee, Total Cost. Payments: Amount, CapstonePay, $360.00 etc.\n\nThis looks like a Carrier invoice for loading/unloading? It includes charges, total cost, payments. It also has a PO number. Could it be lumper receipt? The description: lumper receipt: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO, Door number, Date-Time, initial pallets, finished pallets, carrier, etc. So lumper receipt fits.\n\nBut also could be invoice. Invoice definitions: Bill issued by a carrier for goods being transported. It includes charges, etc. The difference between invoice and lumper receipt: lumper receipt has the specific fields: PO, Door number, Date-Time, initial pallets, finished pallets, carrier, service charge, total amount. This page indeed contains those. So lumper receipt is likely correct.\n\nAlso "Receipt #" indicates a receipt. The page shows payments. It might be a lumper receipt indeed. So doc_type: lumper_receipt.\n\nWe must output JSON with documents array: page_no: 1, doc_type: lumper_receipt.\n\nCheck if it\'s continuation page? No, it\'s first page. No "continued". So classification stands.\n\nReturn using tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2851, 'outputTokens': 417, 'totalTokens': 3268}, 'metrics': {'latencyMs': 1964}}
2025-09-24 18:56:55,242 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fc004b50_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:55,488 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4dcbcd75_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:55,505 - INFO - 

ZOBA55ELUA2GRE3UJM3I.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:56:55,505 - INFO - 

✓ Saved result: output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:56:55,832 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fc004b50_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:55,833 - INFO - 
📊 Processing Summary:
2025-09-24 18:56:55,833 - INFO -    Total files: 10
2025-09-24 18:56:55,833 - INFO -    Successful: 10
2025-09-24 18:56:55,834 - INFO -    Failed: 0
2025-09-24 18:56:55,834 - INFO -    Duration: 16.45 seconds
2025-09-24 18:56:55,834 - INFO -    Output directory: output
2025-09-24 18:56:55,834 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:56:55,834 - INFO -    📄 G0H0K1LRWDOG7LXAKKQ7.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,834 - INFO -    📄 GKNF55W2CF2JR6EBXMPX.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:56:55,834 - INFO -    📄 KDFG6JJAZV41YZP4TEGN.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:56:55,834 - INFO -    📄 MMNWUYGBLL1K5KSJBNOT.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,834 - INFO -    📄 NFMA1926AJ8R3TDZQALU.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,834 - INFO -    📄 O1YJBQBLYAU6D0SDDKAU.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,834 - INFO -    📄 OLQ7TIWW6EVTC6BXA1II.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:56:55,835 - INFO -    📄 V3BCW6BW9XVKKO6WY2YJ.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,835 - INFO -    📄 Y9K6Z2GSAP496UQLOOGU.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,835 - INFO -    📄 ZOBA55ELUA2GRE3UJM3I.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:56:55,835 - INFO - 
============================================================================================================================================
2025-09-24 18:56:55,835 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:56:55,835 - INFO - ============================================================================================================================================
2025-09-24 18:56:55,836 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:56:55,836 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:56:55,836 - INFO - G0H0K1LRWDOG7LXAKKQ7.pdf                           1      lumper_receipt                           run1_G0H0K1LRWDOG7LXAKKQ7.json                                                  
2025-09-24 18:56:55,836 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:56:55,836 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:56:55,836 - INFO - 
2025-09-24 18:56:55,836 - INFO - GKNF55W2CF2JR6EBXMPX.pdf                           1      invoice                                  run1_GKNF55W2CF2JR6EBXMPX.json                                                  
2025-09-24 18:56:55,836 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:56:55,836 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:56:55,836 - INFO - 
2025-09-24 18:56:55,836 - INFO - KDFG6JJAZV41YZP4TEGN.pdf                           1      invoice                                  run1_KDFG6JJAZV41YZP4TEGN.json                                                  
2025-09-24 18:56:55,836 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:56:55,836 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:56:55,836 - INFO - 
2025-09-24 18:56:55,836 - INFO - MMNWUYGBLL1K5KSJBNOT.pdf                           1      lumper_receipt                           run1_MMNWUYGBLL1K5KSJBNOT.json                                                  
2025-09-24 18:56:55,836 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:56:55,837 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:56:55,837 - INFO - 
2025-09-24 18:56:55,837 - INFO - NFMA1926AJ8R3TDZQALU.pdf                           1      lumper_receipt                           run1_NFMA1926AJ8R3TDZQALU.json                                                  
2025-09-24 18:56:55,837 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:56:55,837 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:56:55,837 - INFO - 
2025-09-24 18:56:55,837 - INFO - O1YJBQBLYAU6D0SDDKAU.pdf                           1      lumper_receipt                           run1_O1YJBQBLYAU6D0SDDKAU.json                                                  
2025-09-24 18:56:55,837 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:56:55,837 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:56:55,837 - INFO - 
2025-09-24 18:56:55,837 - INFO - OLQ7TIWW6EVTC6BXA1II.pdf                           1      invoice                                  run1_OLQ7TIWW6EVTC6BXA1II.json                                                  
2025-09-24 18:56:55,837 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:56:55,837 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:56:55,837 - INFO - 
2025-09-24 18:56:55,837 - INFO - V3BCW6BW9XVKKO6WY2YJ.pdf                           1      lumper_receipt                           run1_V3BCW6BW9XVKKO6WY2YJ.json                                                  
2025-09-24 18:56:55,837 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:56:55,837 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:56:55,837 - INFO - 
2025-09-24 18:56:55,838 - INFO - Y9K6Z2GSAP496UQLOOGU.jpeg                          1      lumper_receipt                           run1_Y9K6Z2GSAP496UQLOOGU.json                                                  
2025-09-24 18:56:55,838 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:56:55,838 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:56:55,838 - INFO - 
2025-09-24 18:56:55,838 - INFO - ZOBA55ELUA2GRE3UJM3I.jpg                           1      lumper_receipt                           run1_ZOBA55ELUA2GRE3UJM3I.json                                                  
2025-09-24 18:56:55,838 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:56:55,838 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:56:55,838 - INFO - 
2025-09-24 18:56:55,838 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:56:55,838 - INFO - Total entries: 10
2025-09-24 18:56:55,838 - INFO - ============================================================================================================================================
2025-09-24 18:56:55,838 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:56:55,838 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:56:55,838 - INFO -   1. G0H0K1LRWDOG7LXAKKQ7.pdf            Page 1   → lumper_receipt  | run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:56:55,838 - INFO -   2. GKNF55W2CF2JR6EBXMPX.pdf            Page 1   → invoice         | run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:56:55,838 - INFO -   3. KDFG6JJAZV41YZP4TEGN.pdf            Page 1   → invoice         | run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:56:55,838 - INFO -   4. MMNWUYGBLL1K5KSJBNOT.pdf            Page 1   → lumper_receipt  | run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:56:55,839 - INFO -   5. NFMA1926AJ8R3TDZQALU.pdf            Page 1   → lumper_receipt  | run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:56:55,839 - INFO -   6. O1YJBQBLYAU6D0SDDKAU.pdf            Page 1   → lumper_receipt  | run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:56:55,839 - INFO -   7. OLQ7TIWW6EVTC6BXA1II.pdf            Page 1   → invoice         | run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:56:55,839 - INFO -   8. V3BCW6BW9XVKKO6WY2YJ.pdf            Page 1   → lumper_receipt  | run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:56:55,839 - INFO -   9. Y9K6Z2GSAP496UQLOOGU.jpeg           Page 1   → lumper_receipt  | run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:56:55,839 - INFO -  10. ZOBA55ELUA2GRE3UJM3I.jpg            Page 1   → lumper_receipt  | run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:56:55,839 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:56:55,840 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 16.449831, 'processed_files': [{'filename': 'G0H0K1LRWDOG7LXAKKQ7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf'}, {'filename': 'GKNF55W2CF2JR6EBXMPX.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf'}, {'filename': 'KDFG6JJAZV41YZP4TEGN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf'}, {'filename': 'MMNWUYGBLL1K5KSJBNOT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf'}, {'filename': 'NFMA1926AJ8R3TDZQALU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf'}, {'filename': 'O1YJBQBLYAU6D0SDDKAU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf'}, {'filename': 'OLQ7TIWW6EVTC6BXA1II.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf'}, {'filename': 'V3BCW6BW9XVKKO6WY2YJ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf'}, {'filename': 'Y9K6Z2GSAP496UQLOOGU.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg'}, {'filename': 'ZOBA55ELUA2GRE3UJM3I.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg'}]}
