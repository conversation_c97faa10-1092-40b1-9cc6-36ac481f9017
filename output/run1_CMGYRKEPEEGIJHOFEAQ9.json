{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 6.772848593072922e-08, "Y": 1.0}]}, "Id": "f0b0f05d-e517-4588-9141-5c74e33df78d", "Relationships": [{"Type": "CHILD", "Ids": ["157e901f-7249-4614-9654-aed54918de1a", "97591f17-fb58-459e-9a6a-32f4247b039e", "edc354db-3ef2-4020-83a3-b304e8162634", "3e65f182-0a59-47a9-a2c7-3886651e85be", "2e82e603-c78a-460c-9a9d-08969e5c8d2f", "2b3cfc33-1796-4ff8-9e84-37fe8d5b3909", "6b449f4d-4f2e-49ad-9c2a-884b34907d56", "ef443818-0b1e-439a-9dbd-288d4123e9e2", "92d4bc04-128d-4b40-bd60-118425a9a81f", "a2e128ec-8a3a-4613-a65b-6cf4ccfda61d", "7ee16fdc-636a-45d1-bf9a-c1cb746811d6", "81f49c74-15b2-4926-8bde-8e0c5d898f1d", "bc4463b4-9425-4b0c-8cf2-4e4342bfe50e", "579e0b21-1d65-4a6f-b432-66141e83782a", "a05f6ee1-1062-4ed5-bc94-7b766dd163e9", "dfe37624-599e-4868-af2f-a365d38f55b4", "2a339bc4-336f-4607-87ac-7cbe745eb750", "a5076746-7913-4096-b8b0-da3b605d6d86", "ad8e743a-ac04-436b-86fd-752c83258731", "7a0ecd7d-8158-4746-a372-4e3a99d925da", "6a230645-8de1-4e59-a8e1-8fef743c7b40", "11726d08-8d4c-4574-9d8c-39ffdca4edfd", "ca1defcd-357d-4278-8832-e48601810f07", "4d8570e4-20e0-4a8e-a455-5854fe5a3ba8", "b5860288-19a0-4985-b386-8764719c4600", "76bc1087-e21a-4f31-bc1c-add6ddee755d", "d7d26673-9a3e-4c5d-af79-67ccaea63946", "05b04228-9af4-4bf9-a79b-23ecd86c2b8f", "d81b0958-07c3-45f2-a6ee-7dae787e4040", "85e95e79-2de1-4b88-97ec-5c55cc19e650", "6512baff-1289-445c-ad18-de57d93ad24b", "c5a90d5d-eb8a-498d-81c4-13c1e542f9ff", "dc18176c-08cd-4cd2-b5fb-191b5a7f2680", "bd196a47-b166-420d-8209-a21ebc6053f0", "17215107-25b1-4e39-afd3-3ca619671849", "b6506afa-b970-43c7-bd1e-0126ab9d3fe8", "e1769743-98b7-4472-9bd1-1d7efac4192d", "ac4930d3-c97d-4398-b05a-d71b72852cc1", "5a2f946d-4bba-457c-a9bb-b1b73912edf1", "e978df6d-5c35-41fb-abf0-275c2535be2b", "e972c29f-cd6e-431e-a187-bef0eb0ec154", "ea1e07d8-fcb7-47c3-a0ed-3d5ce5567d3f", "32d94f1e-b959-4f91-8c2a-e4d796f4404f"]}]}, {"BlockType": "LINE", "Confidence": 75.52515411376953, "Text": "UA TERM", "Geometry": {"BoundingBox": {"Width": 0.03432120010256767, "Height": 0.005333135835826397, "Left": 0.0901661217212677, "Top": 0.05348280072212219}, "Polygon": [{"X": 0.0901661217212677, "Y": 0.05348280072212219}, {"X": 0.12448728084564209, "Y": 0.05348305031657219}, {"X": 0.12448732554912567, "Y": 0.058815937489271164}, {"X": 0.09016616642475128, "Y": 0.05881568416953087}]}, "Id": "157e901f-7249-4614-9654-aed54918de1a", "Relationships": [{"Type": "CHILD", "Ids": ["b8c81bd6-eb40-46c6-a85c-9436db045dbf", "b718adeb-8ed2-42b7-b7bf-b1547c3f499b"]}]}, {"BlockType": "LINE", "Confidence": 99.9*********, "Text": "Invoice #0393", "Geometry": {"BoundingBox": {"Width": 0.2030700296163559, "Height": 0.020159687846899033, "Left": 0.24391114711761475, "Top": 0.04477821663022041}, "Polygon": [{"X": 0.24391114711761475, "Y": 0.04477821663022041}, {"X": 0.4469810724258423, "Y": 0.0447796955704689}, {"X": 0.44698116183280945, "Y": 0.06493790447711945}, {"X": 0.2439112812280655, "Y": 0.06493640691041946}]}, "Id": "97591f17-fb58-459e-9a6a-32f4247b039e", "Relationships": [{"Type": "CHILD", "Ids": ["88ed9637-2df3-4e23-827b-990a43258907", "0724cbfc-11d5-43f6-8def-3195ccfb26d2"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Date 09/19/2025", "Geometry": {"BoundingBox": {"Width": 0.12222680449485779, "Height": 0.010365447029471397, "Left": 0.6845170855522156, "Top": 0.04357806593179703}, "Polygon": [{"X": 0.6845170855522156, "Y": 0.04357806593179703}, {"X": 0.806743860244751, "Y": 0.04357895255088806}, {"X": 0.806743860244751, "Y": 0.053943511098623276}, {"X": 0.6845171451568604, "Y": 0.053942617028951645}]}, "Id": "edc354db-3ef2-4020-83a3-b304e8162634", "Relationships": [{"Type": "CHILD", "Ids": ["1d0e2fbd-8a51-4127-add7-cdb75ef545aa", "151eb6a4-ceaf-4286-97d6-c8e0d972f168"]}]}, {"BlockType": "LINE", "Confidence": 99.7608413696289, "Text": "TO:", "Geometry": {"BoundingBox": {"Width": 0.028115976601839066, "Height": 0.010342413559556007, "Left": 0.053904805332422256, "Top": 0.10600501298904419}, "Polygon": [{"X": 0.053904805332422256, "Y": 0.10600501298904419}, {"X": 0.08202069252729416, "Y": 0.1060052290558815}, {"X": 0.08202078193426132, "Y": 0.11634742468595505}, {"X": 0.05390489846467972, "Y": 0.11634721606969833}]}, "Id": "3e65f182-0a59-47a9-a2c7-3886651e85be", "Relationships": [{"Type": "CHILD", "Ids": ["6d2037b1-34af-4cac-8cea-48e47baf75c9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "FROM:", "Geometry": {"BoundingBox": {"Width": 0.05293869227170944, "Height": 0.01023032981902361, "Left": 0.46065059304237366, "Top": 0.10602857917547226}, "Polygon": [{"X": 0.46065059304237366, "Y": 0.10602857917547226}, {"X": 0.5135892629623413, "Y": 0.1060289815068245}, {"X": 0.5135892629623413, "Y": 0.11625891178846359}, {"X": 0.46065065264701843, "Y": 0.11625850945711136}]}, "Id": "2e82e603-c78a-460c-9a9d-08969e5c8d2f", "Relationships": [{"Type": "CHILD", "Ids": ["9bafe1dc-26dd-4a00-91da-45fc140ed00b"]}]}, {"BlockType": "LINE", "Confidence": 99.9*********, "Text": "Envoy Logistics", "Geometry": {"BoundingBox": {"Width": 0.11387618631124496, "Height": 0.012741407379508018, "Left": 0.05518842861056328, "Top": 0.12153452634811401}, "Polygon": [{"X": 0.05518842861056328, "Y": 0.12153452634811401}, {"X": 0.1690645068883896, "Y": 0.12153539806604385}, {"X": 0.16906461119651794, "Y": 0.13427592813968658}, {"X": 0.05518854036927223, "Y": 0.13427504897117615}]}, "Id": "2b3cfc33-1796-4ff8-9e84-37fe8d5b3909", "Relationships": [{"Type": "CHILD", "Ids": ["6ea0b5c4-3284-41a9-85a3-920266d53ce5", "1539f87d-33dc-4bbd-ac71-f9af5ff20709"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "UA Team Trucking LLC", "Geometry": {"BoundingBox": {"Width": 0.18249788880348206, "Height": 0.012952901422977448, "Left": 0.4608854353427887, "Top": 0.12141112238168716}, "Polygon": [{"X": 0.4608854353427887, "Y": 0.12141112238168716}, {"X": 0.643383264541626, "Y": 0.12141251564025879}, {"X": 0.6433833241462708, "Y": 0.1343640238046646}, {"X": 0.46088552474975586, "Y": 0.1343626230955124}]}, "Id": "6b449f4d-4f2e-49ad-9c2a-884b34907d56", "Relationships": [{"Type": "CHILD", "Ids": ["fd687c57-91fa-4346-82d7-5ca81b820783", "9e3ebeab-001a-4396-860a-59cdb8b339b2", "11db00ba-5fc0-4919-9ee0-505cad9488cd", "8ba035b7-a486-42a3-a93f-0d313f3cf0a9"]}]}, {"BlockType": "LINE", "Confidence": 99.89424133300781, "Text": "PO Box 2803", "Geometry": {"BoundingBox": {"Width": 0.09723436087369919, "Height": 0.010225314646959305, "Left": 0.05535098537802696, "Top": 0.13691751658916473}, "Polygon": [{"X": 0.05535098537802696, "Y": 0.13691751658916473}, {"X": 0.15258526802062988, "Y": 0.13691826164722443}, {"X": 0.15258534252643585, "Y": 0.14714282751083374}, {"X": 0.05535107105970383, "Y": 0.14714206755161285}]}, "Id": "ef443818-0b1e-439a-9dbd-288d4123e9e2", "Relationships": [{"Type": "CHILD", "Ids": ["5a28eaa3-081b-4e49-90eb-89f3f686dd5b", "fe5ff62d-31bf-4cac-8271-0b4662af9f3f", "042e05a1-65e2-40ab-a384-65dc728adf70"]}]}, {"BlockType": "LINE", "Confidence": 99.98371887207031, "Text": "27612 Lorraine Ave", "Geometry": {"BoundingBox": {"Width": 0.144646555185318, "Height": 0.010302279144525528, "Left": 0.46024465560913086, "Top": 0.13696281611919403}, "Polygon": [{"X": 0.46024465560913086, "Y": 0.13696281611919403}, {"X": 0.6048911809921265, "Y": 0.13696393370628357}, {"X": 0.6048912405967712, "Y": 0.14726510643959045}, {"X": 0.46024471521377563, "Y": 0.14726397395133972}]}, "Id": "92d4bc04-128d-4b40-bd60-118425a9a81f", "Relationships": [{"Type": "CHILD", "Ids": ["ba19fe17-71cf-457b-a2ca-48bf8c4f3edf", "b13bc6c7-354e-45ef-8a50-65810414f8c6", "9b791233-c5d6-4a26-9120-35d953aef66f"]}]}, {"BlockType": "LINE", "Confidence": 99.93409729003906, "Text": "Oshkosh, WI 54903", "Geometry": {"BoundingBox": {"Width": 0.1451713889837265, "Height": 0.01201240811496973, "Left": 0.05442748963832855, "Top": 0.15267525613307953}, "Polygon": [{"X": 0.05442748963832855, "Y": 0.15267525613307953}, {"X": 0.1995987892150879, "Y": 0.15267638862133026}, {"X": 0.19959887862205505, "Y": 0.16468766331672668}, {"X": 0.05442759394645691, "Y": 0.16468653082847595}]}, "Id": "a2e128ec-8a3a-4613-a65b-6cf4ccfda61d", "Relationships": [{"Type": "CHILD", "Ids": ["39f796ba-5419-4ea0-9900-6e97f4e335f3", "1df8bc7f-1c48-4e73-8c93-28c9abddb1d4", "f9c22f6d-a4f5-4cc2-9fb8-dea40051eb63"]}]}, {"BlockType": "LINE", "Confidence": 99.96717834472656, "Text": "Warren, MI 48093", "Geometry": {"BoundingBox": {"Width": 0.13265445828437805, "Height": 0.01188640482723713, "Left": 0.46016108989715576, "Top": 0.15277890861034393}, "Polygon": [{"X": 0.46016108989715576, "Y": 0.15277890861034393}, {"X": 0.5928155183792114, "Y": 0.1527799367904663}, {"X": 0.5928155779838562, "Y": 0.1646653115749359}, {"X": 0.46016114950180054, "Y": 0.16466426849365234}]}, "Id": "7ee16fdc-636a-45d1-bf9a-c1cb746811d6", "Relationships": [{"Type": "CHILD", "Ids": ["1ee4c616-1ff8-46fb-8d8b-dc59ca180553", "e9fbe9c3-1697-4951-bce4-08388204889f", "0a17937a-7749-48f1-9ea6-877cfc9393e0"]}]}, {"BlockType": "LINE", "Confidence": 99.90885162353516, "Text": "Phone: (*************", "Geometry": {"BoundingBox": {"Width": 0.16965273022651672, "Height": 0.012933090329170227, "Left": 0.46112146973609924, "Top": 0.16808439791202545}, "Polygon": [{"X": 0.46112146973609924, "Y": 0.16808439791202545}, {"X": 0.6307741403579712, "Y": 0.1680857390165329}, {"X": 0.630774199962616, "Y": 0.18101748824119568}, {"X": 0.461121529340744, "Y": 0.18101614713668823}]}, "Id": "81f49c74-15b2-4926-8bde-8e0c5d898f1d", "Relationships": [{"Type": "CHILD", "Ids": ["d91c8c10-dd86-4808-9d90-7b7f7e0a9146", "250e73d3-308d-4d7a-81ce-80f657165a37", "6b8b55d7-f631-432d-a02f-e76f37540276"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "Tax ID#: *********", "Geometry": {"BoundingBox": {"Width": 0.1497696489095688, "Height": 0.010240699164569378, "Left": 0.46030858159065247, "Top": 0.18371152877807617}, "Polygon": [{"X": 0.46030858159065247, "Y": 0.18371152877807617}, {"X": 0.6100781559944153, "Y": 0.18371272087097168}, {"X": 0.6100782155990601, "Y": 0.19395223259925842}, {"X": 0.46030861139297485, "Y": 0.19395102560520172}]}, "Id": "bc4463b4-9425-4b0c-8cf2-4e4342bfe50e", "Relationships": [{"Type": "CHILD", "Ids": ["44c416ef-9d23-4750-977f-525d8a7ddef8", "149e5b32-f707-4a22-a9d1-6ffd8b4c7a35", "3d0e98fb-65d0-4b9f-8cce-270add04cce2"]}]}, {"BlockType": "LINE", "Confidence": 99.7021484375, "Text": "Load #", "Geometry": {"BoundingBox": {"Width": 0.05308220162987709, "Height": 0.010426806285977364, "Left": 0.1219518631696701, "Top": 0.23216144740581512}, "Polygon": [{"X": 0.1219518631696701, "Y": 0.23216144740581512}, {"X": 0.17503398656845093, "Y": 0.23216189444065094}, {"X": 0.1750340759754181, "Y": 0.24258826673030853}, {"X": 0.12195195257663727, "Y": 0.24258781969547272}]}, "Id": "579e0b21-1d65-4a6f-b432-66141e83782a", "Relationships": [{"Type": "CHILD", "Ids": ["41827a93-bbb5-440f-ad2c-438e853e3bb3", "352549e7-c93c-4aee-a935-d5828cf938d4"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Shipment Info", "Geometry": {"BoundingBox": {"Width": 0.11015240103006363, "Height": 0.012987201102077961, "Left": 0.41991087794303894, "Top": 0.2319207340478897}, "Polygon": [{"X": 0.41991087794303894, "Y": 0.2319207340478897}, {"X": 0.5300632119178772, "Y": 0.23192162811756134}, {"X": 0.530063271522522, "Y": 0.2449079304933548}, {"X": 0.4199109673500061, "Y": 0.24490702152252197}]}, "Id": "a05f6ee1-1062-4ed5-bc94-7b766dd163e9", "Relationships": [{"Type": "CHILD", "Ids": ["70439e97-a1a0-455d-9900-01b0df843188", "948601fc-961c-4c13-b865-4062d0431fb7"]}]}, {"BlockType": "LINE", "Confidence": 99.9*********, "Text": "Primary Fee", "Geometry": {"BoundingBox": {"Width": 0.094489187002182, "Height": 0.012791975401341915, "Left": 0.7274353504180908, "Top": 0.23206663131713867}, "Polygon": [{"X": 0.7274353504180908, "Y": 0.23206663131713867}, {"X": 0.8219245076179504, "Y": 0.23206740617752075}, {"X": 0.8219245672225952, "Y": 0.24485860764980316}, {"X": 0.7274354100227356, "Y": 0.2448578178882599}]}, "Id": "dfe37624-599e-4868-af2f-a365d38f55b4", "Relationships": [{"Type": "CHILD", "Ids": ["535347f7-c1b4-4d97-b248-4851ad8c3ba0", "14df2200-0284-49ca-8bf4-ee7d435b3d15"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "Sub Total", "Geometry": {"BoundingBox": {"Width": 0.0744684711098671, "Height": 0.010316669940948486, "Left": 0.8534550070762634, "Top": 0.23208299279212952}, "Polygon": [{"X": 0.8534550070762634, "Y": 0.23208299279212952}, {"X": 0.9279235005378723, "Y": 0.23208358883857727}, {"X": 0.9279235005378723, "Y": 0.242399662733078}, {"X": 0.8534550666809082, "Y": 0.24239903688430786}]}, "Id": "2a339bc4-336f-4607-87ac-7cbe745eb750", "Relationships": [{"Type": "CHILD", "Ids": ["dc735b31-1d25-4615-a840-2d803a90ff34", "35c930da-c72c-4b00-947f-4fe4d73102ad"]}]}, {"BlockType": "LINE", "Confidence": 99.59721374511719, "Text": "COUNET11778509", "Geometry": {"BoundingBox": {"Width": 0.14290457963943481, "Height": 0.0106600197032094, "Left": 0.065498948097229, "Top": 0.26047149300575256}, "Polygon": [{"X": 0.065498948097229, "Y": 0.26047149300575256}, {"X": 0.20840345323085785, "Y": 0.26047268509864807}, {"X": 0.20840352773666382, "Y": 0.2711315155029297}, {"X": 0.06549903750419617, "Y": 0.2711303234100342}]}, "Id": "a5076746-7913-4096-b8b0-da3b605d6d86", "Relationships": [{"Type": "CHILD", "Ids": ["e3c51618-f03d-4a08-9e40-b572445dbd04"]}]}, {"BlockType": "LINE", "Confidence": 99.99349212646484, "Text": "Pickup Date: 09/19/2025", "Geometry": {"BoundingBox": {"Width": 0.18084710836410522, "Height": 0.013018850237131119, "Left": 0.2488209456205368, "Top": 0.260477215051651}, "Polygon": [{"X": 0.2488209456205368, "Y": 0.260477215051651}, {"X": 0.42966797947883606, "Y": 0.2604787051677704}, {"X": 0.4296680688858032, "Y": 0.2734960615634918}, {"X": 0.24882103502750397, "Y": 0.27349454164505005}]}, "Id": "ad8e743a-ac04-436b-86fd-752c83258731", "Relationships": [{"Type": "CHILD", "Ids": ["136cd674-c09d-4d1d-937f-ce2b5d7da0e6", "b1fcc583-8708-4c53-b0a2-3355e283b44c", "48229abe-4f3c-473c-96d3-61ee138d3506"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$400.00", "Geometry": {"BoundingBox": {"Width": 0.0593765564262867, "Height": 0.012042077258229256, "Left": 0.7191163897514343, "Top": 0.2600824236869812}, "Polygon": [{"X": 0.7191163897514343, "Y": 0.2600824236869812}, {"X": 0.7784929275512695, "Y": 0.2600829303264618}, {"X": 0.7784929871559143, "Y": 0.2721244990825653}, {"X": 0.7191164493560791, "Y": 0.2721240222454071}]}, "Id": "7a0ecd7d-8158-4746-a372-4e3a99d925da", "Relationships": [{"Type": "CHILD", "Ids": ["c94bb9d7-828c-4053-af68-e373c9b3d59c"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$400.00", "Geometry": {"BoundingBox": {"Width": 0.059907104820013046, "Height": 0.01203219871968031, "Left": 0.8463203310966492, "Top": 0.2600804269313812}, "Polygon": [{"X": 0.8463203310966492, "Y": 0.2600804269313812}, {"X": 0.9062274098396301, "Y": 0.2600809335708618}, {"X": 0.9062274694442749, "Y": 0.272112637758255}, {"X": 0.846320390701294, "Y": 0.2721121311187744}]}, "Id": "6a230645-8de1-4e59-a8e1-8fef743c7b40", "Relationships": [{"Type": "CHILD", "Ids": ["08348969-c77e-4b57-8ae5-7f48c0738c45"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Delivery Date: 09/19/2025", "Geometry": {"BoundingBox": {"Width": 0.19066283106803894, "Height": 0.012735988944768906, "Left": 0.2490769773721695, "Top": 0.27606815099716187}, "Polygon": [{"X": 0.2490769773721695, "Y": 0.27606815099716187}, {"X": 0.43973973393440247, "Y": 0.2760697603225708}, {"X": 0.43973979353904724, "Y": 0.28880414366722107}, {"X": 0.24907706677913666, "Y": 0.28880250453948975}]}, "Id": "11726d08-8d4c-4574-9d8c-39ffdca4edfd", "Relationships": [{"Type": "CHILD", "Ids": ["3f4bb93a-3232-4cc1-8d55-e3a5c9fb889c", "2a5c787a-05c1-4f6c-a70a-e465f6fa0af3", "f5317d37-1e20-43b3-9c41-9babea62d6e7"]}]}, {"BlockType": "LINE", "Confidence": 99.93612670898438, "Text": "Weight: 2000.0", "Geometry": {"BoundingBox": {"Width": 0.11125058680772781, "Height": 0.01288254838436842, "Left": 0.2479972094297409, "Top": 0.2914324402809143}, "Polygon": [{"X": 0.2479972094297409, "Y": 0.2914324402809143}, {"X": 0.35924771428108215, "Y": 0.2914333939552307}, {"X": 0.35924777388572693, "Y": 0.3043150007724762}, {"X": 0.24799729883670807, "Y": 0.3043140470981598}]}, "Id": "ca1defcd-357d-4278-8832-e48601810f07", "Relationships": [{"Type": "CHILD", "Ids": ["7c80e3e2-2af4-4d1a-90e4-e5a35136c1fb", "d31b682c-53a0-4f41-b592-5818f0b520bd"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Quantity: 0.0", "Geometry": {"BoundingBox": {"Width": 0.09357327967882156, "Height": 0.012782574631273746, "Left": 0.2482532262802124, "Top": 0.3068913221359253}, "Polygon": [{"X": 0.2482532262802124, "Y": 0.3068913221359253}, {"X": 0.3418264389038086, "Y": 0.30689212679862976}, {"X": 0.34182649850845337, "Y": 0.31967389583587646}, {"X": 0.24825331568717957, "Y": 0.319673091173172}]}, "Id": "4d8570e4-20e0-4a8e-a455-5854fe5a3ba8", "Relationships": [{"Type": "CHILD", "Ids": ["c58a0035-d2ef-414b-b57b-995d45f71ad6", "536d349a-e0fd-4f0e-9476-88e168098e94"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "From:", "Geometry": {"BoundingBox": {"Width": 0.03634960949420929, "Height": 0.00898495689034462, "Left": 0.2487715631723404, "Top": 0.33448922634124756}, "Polygon": [{"X": 0.2487715631723404, "Y": 0.33448922634124756}, {"X": 0.2851211130619049, "Y": 0.33448952436447144}, {"X": 0.2851211726665497, "Y": 0.3434741795063019}, {"X": 0.24877162277698517, "Y": 0.3434738516807556}]}, "Id": "b5860288-19a0-4985-b386-8764719c4600", "Relationships": [{"Type": "CHILD", "Ids": ["049aba08-86b3-450d-81e0-ac8e45267214"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipper", "Geometry": {"BoundingBox": {"Width": 0.054014381021261215, "Height": 0.011277100071310997, "Left": 0.24797606468200684, "Top": 0.3478551208972931}, "Polygon": [{"X": 0.24797606468200684, "Y": 0.3478551208972931}, {"X": 0.3019903600215912, "Y": 0.3478555977344513}, {"X": 0.30199044942855835, "Y": 0.35913223028182983}, {"X": 0.2479761391878128, "Y": 0.35913175344467163}]}, "Id": "76bc1087-e21a-4f31-bc1c-add6ddee755d", "Relationships": [{"Type": "CHILD", "Ids": ["843d78e1-0223-4228-9c95-d9b025e64da9"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Velocity", "Geometry": {"BoundingBox": {"Width": 0.05013558268547058, "Height": 0.011131834238767624, "Left": 0.24809324741363525, "Top": 0.3612671494483948}, "Polygon": [{"X": 0.24809324741363525, "Y": 0.3612671494483948}, {"X": 0.29822877049446106, "Y": 0.3612675964832306}, {"X": 0.29822883009910583, "Y": 0.3723990023136139}, {"X": 0.24809332191944122, "Y": 0.3723985552787781}]}, "Id": "d7d26673-9a3e-4c5d-af79-67ccaea63946", "Relationships": [{"Type": "CHILD", "Ids": ["ff94922e-f809-42ed-96bf-77c8abcb8316"]}]}, {"BlockType": "LINE", "Confidence": 99.65182495117188, "Text": "2068 E Street", "Geometry": {"BoundingBox": {"Width": 0.08851003646850586, "Height": 0.009046459570527077, "Left": 0.24784338474273682, "Top": 0.374592661857605}, "Polygon": [{"X": 0.24784338474273682, "Y": 0.374592661857605}, {"X": 0.3363533616065979, "Y": 0.37459343671798706}, {"X": 0.3363534212112427, "Y": 0.3836391270160675}, {"X": 0.2478434443473816, "Y": 0.38363832235336304}]}, "Id": "05b04228-9af4-4bf9-a79b-23ecd86c2b8f", "Relationships": [{"Type": "CHILD", "Ids": ["48fa29d7-0edc-4209-a1df-ef72fd6529eb", "72862e63-34ac-44d2-96d0-c7e799e30bf2", "5e8c2b1d-17b3-4f6d-9a5b-abbb85cc89db"]}]}, {"BlockType": "LINE", "Confidence": 99.89749145507812, "Text": "Belleville, MI 48111", "Geometry": {"BoundingBox": {"Width": 0.12154576927423477, "Height": 0.010571862570941448, "Left": 0.24876055121421814, "Top": 0.3878004848957062}, "Polygon": [{"X": 0.24876055121421814, "Y": 0.3878004848957062}, {"X": 0.37030625343322754, "Y": 0.3878015875816345}, {"X": 0.3703063428401947, "Y": 0.3983723521232605}, {"X": 0.2487606406211853, "Y": 0.39837124943733215}]}, "Id": "d81b0958-07c3-45f2-a6ee-7dae787e4040", "Relationships": [{"Type": "CHILD", "Ids": ["48591b8a-b78c-47c6-98b5-94e7c7bcde40", "3508dab3-c26a-46df-a7c4-daab0ba2f7e4", "98c784ef-a786-46f3-95cd-e5297c3e5c62"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "To:", "Geometry": {"BoundingBox": {"Width": 0.02044912427663803, "Height": 0.00911638978868723, "Left": 0.2478071004152298, "Top": 0.4133202135562897}, "Polygon": [{"X": 0.2478071004152298, "Y": 0.4133202135562897}, {"X": 0.26825615763664246, "Y": 0.4133204221725464}, {"X": 0.26825621724128723, "Y": 0.4224366247653961}, {"X": 0.24780716001987457, "Y": 0.4224364161491394}]}, "Id": "85e95e79-2de1-4b88-97ec-5c55cc19e650", "Relationships": [{"Type": "CHILD", "Ids": ["6d2dc70b-0738-4c81-a0d3-4f2e784941bf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Consignee", "Geometry": {"BoundingBox": {"Width": 0.0731496661901474, "Height": 0.01125477347522974, "Left": 0.24828116595745087, "Top": 0.4266597628593445}, "Polygon": [{"X": 0.24828116595745087, "Y": 0.4266597628593445}, {"X": 0.3214307725429535, "Y": 0.4266604483127594}, {"X": 0.32143083214759827, "Y": 0.43791455030441284}, {"X": 0.24828125536441803, "Y": 0.4379138648509979}]}, "Id": "6512baff-1289-445c-ad18-de57d93ad24b", "Relationships": [{"Type": "CHILD", "Ids": ["08c5c2c9-6259-4ffa-9bb0-85c30bb01b94"]}]}, {"BlockType": "LINE", "Confidence": 99.76692199707031, "Text": "BRIDGEWATER INTERIORS-WARREN", "Geometry": {"BoundingBox": {"Width": 0.2578567564487457, "Height": 0.008866710588335991, "Left": 0.24856384098529816, "Top": 0.4400305151939392}, "Polygon": [{"X": 0.24856384098529816, "Y": 0.4400305151939392}, {"X": 0.5064205527305603, "Y": 0.4400328993797302}, {"X": 0.5064206123352051, "Y": 0.44889724254608154}, {"X": 0.24856390058994293, "Y": 0.44889482855796814}]}, "Id": "c5a90d5d-eb8a-498d-81c4-13c1e542f9ff", "Relationships": [{"Type": "CHILD", "Ids": ["0c5d7e8c-5006-4add-b8ea-858743faaa80", "71d41800-d7ad-45aa-a0f7-24d865ae05ad"]}]}, {"BlockType": "LINE", "Confidence": 99.97721099853516, "Text": "7500 TANK ROAD", "Geometry": {"BoundingBox": {"Width": 0.11970607936382294, "Height": 0.009291811846196651, "Left": 0.24811379611492157, "Top": 0.4531574547290802}, "Polygon": [{"X": 0.24811379611492157, "Y": 0.4531574547290802}, {"X": 0.36781981587409973, "Y": 0.45315855741500854}, {"X": 0.3678198754787445, "Y": 0.46244925260543823}, {"X": 0.24811385571956635, "Y": 0.4624481499195099}]}, "Id": "dc18176c-08cd-4cd2-b5fb-191b5a7f2680", "Relationships": [{"Type": "CHILD", "Ids": ["473f3a3c-b728-4896-9732-bc0ac8dbb108", "761be1b4-c35d-4dba-89d5-abe9ce18016e", "1a04de0d-1066-4a47-9267-e53dcffab322"]}]}, {"BlockType": "LINE", "Confidence": 99.96040344238281, "Text": "Warren, MI 48093", "Geometry": {"BoundingBox": {"Width": 0.11536718159914017, "Height": 0.010591945610940456, "Left": 0.24773253500461578, "Top": 0.4665999412536621}, "Polygon": [{"X": 0.24773253500461578, "Y": 0.4665999412536621}, {"X": 0.3630996346473694, "Y": 0.46660101413726807}, {"X": 0.36309972405433655, "Y": 0.47719189524650574}, {"X": 0.24773260951042175, "Y": 0.4771908223628998}]}, "Id": "bd196a47-b166-420d-8209-a21ebc6053f0", "Relationships": [{"Type": "CHILD", "Ids": ["9f6a2b08-4d29-4d91-92d4-b84de0bea979", "12a4fa12-1400-41e5-968c-151267724715", "43fc11ea-4644-448a-856a-023c2a509fb0"]}]}, {"BlockType": "LINE", "Confidence": 99.87753295898438, "Text": "Customer Required Info: Order #: COUNET11778509", "Geometry": {"BoundingBox": {"Width": 0.39469820261001587, "Height": 0.013096736744046211, "Left": 0.24842818081378937, "Top": 0.49214690923690796}, "Polygon": [{"X": 0.24842818081378937, "Y": 0.49214690923690796}, {"X": 0.6431263089179993, "Y": 0.4921506345272064}, {"X": 0.643126368522644, "Y": 0.5052436590194702}, {"X": 0.24842827022075653, "Y": 0.5052398443222046}]}, "Id": "17215107-25b1-4e39-afd3-3ca619671849", "Relationships": [{"Type": "CHILD", "Ids": ["ccf8c1c6-2d35-4512-92ff-fd0f197ec1f2", "33c9f551-a3f0-4e1a-aab9-3bdd608f210a", "cc4954bf-ed72-47e5-a652-4deebb1f421e", "c4a3fe9b-6af8-482e-bb8e-ca49f7ee2466", "5a9d884b-3dc6-4b97-9fef-d2228e22ad91", "8915a488-de2f-470d-ad87-282eab990009"]}]}, {"BlockType": "LINE", "Confidence": 99.97198486328125, "Text": "Primary Fee Based on Flat Fee: $400.00", "Geometry": {"BoundingBox": {"Width": 0.29886847734451294, "Height": 0.013341166079044342, "Left": 0.24892054498195648, "Top": 0.5073962807655334}, "Polygon": [{"X": 0.24892054498195648, "Y": 0.5073962807655334}, {"X": 0.5477889776229858, "Y": 0.5073991417884827}, {"X": 0.5477890372276306, "Y": 0.5207374691963196}, {"X": 0.24892064929008484, "Y": 0.5207346081733704}]}, "Id": "b6506afa-b970-43c7-bd1e-0126ab9d3fe8", "Relationships": [{"Type": "CHILD", "Ids": ["941224cb-72fa-4e06-8dd6-3b26010c8c34", "a945d136-3865-486a-b02c-b123ca987090", "a2db03cf-5f19-4cae-8cfa-7202843ee7ae", "73987bb9-3dfe-4bd9-96d1-5c72dd44873b", "9409eb7c-efc4-4e02-bcea-1432addb4f50", "2aff00bf-a405-427f-b5f0-9bc502c9bc00", "6ea63c94-ba8c-4c62-be8b-d6e8313279a1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Accessory Fees", "Geometry": {"BoundingBox": {"Width": 0.11769489198923111, "Height": 0.012762044556438923, "Left": 0.2480010986328125, "Top": 0.5234552621841431}, "Polygon": [{"X": 0.2480010986328125, "Y": 0.5234552621841431}, {"X": 0.36569592356681824, "Y": 0.5234563946723938}, {"X": 0.365695983171463, "Y": 0.5362173318862915}, {"X": 0.24800118803977966, "Y": 0.5362161993980408}]}, "Id": "e1769743-98b7-4472-9bd1-1d7efac4192d", "Relationships": [{"Type": "CHILD", "Ids": ["5aa1f154-34a7-4cc7-bce1-22722d0c2125", "96fb09c6-cb0b-447f-893e-28d8b5235fb9"]}]}, {"BlockType": "LINE", "Confidence": 99.98697662353516, "Text": "Balance Due: $400.00", "Geometry": {"BoundingBox": {"Width": 0.16832716763019562, "Height": 0.012277155183255672, "Left": 0.7664555907249451, "Top": 0.5635388493537903}, "Polygon": [{"X": 0.7664555907249451, "Y": 0.5635388493537903}, {"X": 0.9347827434539795, "Y": 0.563540518283844}, {"X": 0.9347827434539795, "Y": 0.5758159756660461}, {"X": 0.7664556503295898, "Y": 0.5758143663406372}]}, "Id": "ac4930d3-c97d-4398-b05a-d71b72852cc1", "Relationships": [{"Type": "CHILD", "Ids": ["848688d5-d2c4-4067-987b-bf4473fe6c25", "d49a33ef-0e8e-4c03-b7c1-511ffa31dea3", "cde8f4c8-5283-43c2-9c9f-352099fe4896"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Invoice Total", "Geometry": {"BoundingBox": {"Width": 0.10040028393268585, "Height": 0.010418668389320374, "Left": 0.5111465454101562, "Top": 0.6050035357475281}, "Polygon": [{"X": 0.5111465454101562, "Y": 0.6050035357475281}, {"X": 0.6115468144416809, "Y": 0.6050045490264893}, {"X": 0.6115468144416809, "Y": 0.6154221892356873}, {"X": 0.511146605014801, "Y": 0.6154212355613708}]}, "Id": "5a2f946d-4bba-457c-a9bb-b1b73912edf1", "Relationships": [{"Type": "CHILD", "Ids": ["33cd10a4-98da-4336-8b0d-6e7f8f22b49f", "a1f9915f-b580-406a-b509-7b6dc29951d2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$400.00", "Geometry": {"BoundingBox": {"Width": 0.06002386659383774, "Height": 0.012233763933181763, "Left": 0.8759718537330627, "Top": 0.6046354174613953}, "Polygon": [{"X": 0.8759718537330627, "Y": 0.6046354174613953}, {"X": 0.9359956979751587, "Y": 0.604636013507843}, {"X": 0.9359957575798035, "Y": 0.6168691515922546}, {"X": 0.8759719133377075, "Y": 0.6168685555458069}]}, "Id": "e978df6d-5c35-41fb-abf0-275c2535be2b", "Relationships": [{"Type": "CHILD", "Ids": ["95d5473b-da05-49fd-8378-64e638b393c8"]}]}, {"BlockType": "LINE", "Confidence": 99.9*********, "Text": "Balance Due", "Geometry": {"BoundingBox": {"Width": 0.09847541898488998, "Height": 0.010127543471753597, "Left": 0.5116085410118103, "Top": 0.6331833600997925}, "Polygon": [{"X": 0.5116085410118103, "Y": 0.6331833600997925}, {"X": 0.6100839376449585, "Y": 0.6331843733787537}, {"X": 0.6100839376449585, "Y": 0.6433109045028687}, {"X": 0.5116086006164551, "Y": 0.6433098912239075}]}, "Id": "e972c29f-cd6e-431e-a187-bef0eb0ec154", "Relationships": [{"Type": "CHILD", "Ids": ["b128f84f-9c6f-4048-bbd1-c0ff3534e321", "41465506-6115-47db-9ca0-b1ad1e15c546"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$400.00", "Geometry": {"BoundingBox": {"Width": 0.05985615402460098, "Height": 0.012059591710567474, "Left": 0.8757067918777466, "Top": 0.6328343749046326}, "Polygon": [{"X": 0.8757067918777466, "Y": 0.6328343749046326}, {"X": 0.9355629086494446, "Y": 0.6328349709510803}, {"X": 0.9355629086494446, "Y": 0.6448939442634583}, {"X": 0.8757067918777466, "Y": 0.6448933482170105}]}, "Id": "ea1e07d8-fcb7-47c3-a0ed-3d5ce5567d3f", "Relationships": [{"Type": "CHILD", "Ids": ["a256a1ba-5f41-495e-b384-376671edb2f1"]}]}, {"BlockType": "LINE", "Confidence": 99.92781066894531, "Text": "We appreciate your business!", "Geometry": {"BoundingBox": {"Width": 0.21832723915576935, "Height": 0.013131972402334213, "Left": 0.39042484760284424, "Top": 0.6677703857421875}, "Polygon": [{"X": 0.39042484760284424, "Y": 0.6677703857421875}, {"X": 0.6087520122528076, "Y": 0.667772650718689}, {"X": 0.6087520718574524, "Y": 0.680902361869812}, {"X": 0.390424907207489, "Y": 0.6809000968933105}]}, "Id": "32d94f1e-b959-4f91-8c2a-e4d796f4404f", "Relationships": [{"Type": "CHILD", "Ids": ["dcf2c46e-b69d-45a6-ab60-f5e4f44bfb30", "d707e64e-5324-42c7-82a6-8d91bdbd9116", "ec90bf30-6bd2-41eb-86ca-02f5e3b6ed56", "583a7971-35b3-4021-8347-5ba3029f4d6a"]}]}, {"BlockType": "WORD", "Confidence": 92.29571533203125, "Text": "UA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01156032457947731, "Height": 0.005149626638740301, "Left": 0.0901661291718483, "Top": 0.053666144609451294}, "Polygon": [{"X": 0.0901661291718483, "Y": 0.053666144609451294}, {"X": 0.10172640532255173, "Y": 0.05366622656583786}, {"X": 0.10172645002603531, "Y": 0.05881576985120773}, {"X": 0.09016616642475128, "Y": 0.05881568416953087}]}, "Id": "b8c81bd6-eb40-46c6-a85c-9436db045dbf"}, {"BlockType": "WORD", "Confidence": 58.754581451416016, "Text": "TERM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0211739894002676, "Height": 0.005175849888473749, "Left": 0.10331333428621292, "Top": 0.05348289757966995}, "Polygon": [{"X": 0.10331333428621292, "Y": 0.05348289757966995}, {"X": 0.12448728084564209, "Y": 0.05348305031657219}, {"X": 0.12448732554912567, "Y": 0.058658748865127563}, {"X": 0.1033133789896965, "Y": 0.05865859240293503}]}, "Id": "b718adeb-8ed2-42b7-b7bf-b1547c3f499b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10176874697208405, "Height": 0.02001429721713066, "Left": 0.24391114711761475, "Top": 0.04477821663022041}, "Polygon": [{"X": 0.24391114711761475, "Y": 0.04477821663022041}, {"X": 0.34567975997924805, "Y": 0.04477895796298981}, {"X": 0.3456798791885376, "Y": 0.06479251384735107}, {"X": 0.2439112812280655, "Y": 0.06479176878929138}]}, "Id": "88ed9637-2df3-4e23-827b-990a43258907"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "#0393", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09107372164726257, "Height": 0.019804587587714195, "Left": 0.35590746998786926, "Top": 0.0451333187520504}, "Polygon": [{"X": 0.35590746998786926, "Y": 0.0451333187520504}, {"X": 0.4469810724258423, "Y": 0.045133981853723526}, {"X": 0.44698116183280945, "Y": 0.06493790447711945}, {"X": 0.3559075891971588, "Y": 0.06493723392486572}]}, "Id": "0724cbfc-11d5-43f6-8def-3195ccfb26d2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034969840198755264, "Height": 0.01036409568041563, "Left": 0.6845170855522156, "Top": 0.04357806593179703}, "Polygon": [{"X": 0.6845170855522156, "Y": 0.04357806593179703}, {"X": 0.7194868922233582, "Y": 0.04357831925153732}, {"X": 0.7194869518280029, "Y": 0.053942158818244934}, {"X": 0.6845171451568604, "Y": 0.05394190549850464}]}, "Id": "1d0e2fbd-8a51-4127-add7-cdb75ef545aa"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "09/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08204200118780136, "Height": 0.010292735882103443, "Left": 0.7247018814086914, "Top": 0.04365077614784241}, "Polygon": [{"X": 0.7247018814086914, "Y": 0.04365077614784241}, {"X": 0.806743860244751, "Y": 0.04365137219429016}, {"X": 0.806743860244751, "Y": 0.053943511098623276}, {"X": 0.7247019410133362, "Y": 0.05394291132688522}]}, "Id": "151eb6a4-ceaf-4286-97d6-c8e0d972f168"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "TO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028115976601839066, "Height": 0.010342413559556007, "Left": 0.053904805332422256, "Top": 0.10600501298904419}, "Polygon": [{"X": 0.053904805332422256, "Y": 0.10600501298904419}, {"X": 0.08202069252729416, "Y": 0.1060052290558815}, {"X": 0.08202078193426132, "Y": 0.11634742468595505}, {"X": 0.05390489846467972, "Y": 0.11634721606969833}]}, "Id": "6d2037b1-34af-4cac-8cea-48e47baf75c9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FROM:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05293869227170944, "Height": 0.01023032981902361, "Left": 0.46065059304237366, "Top": 0.10602857917547226}, "Polygon": [{"X": 0.46065059304237366, "Y": 0.10602857917547226}, {"X": 0.5135892629623413, "Y": 0.1060289815068245}, {"X": 0.5135892629623413, "Y": 0.11625891178846359}, {"X": 0.46065065264701843, "Y": 0.11625850945711136}]}, "Id": "9bafe1dc-26dd-4a00-91da-45fc140ed00b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Envoy", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04483307898044586, "Height": 0.012700404040515423, "Left": 0.05518842861056328, "Top": 0.12153452634811401}, "Polygon": [{"X": 0.05518842861056328, "Y": 0.12153452634811401}, {"X": 0.10002140700817108, "Y": 0.12153486907482147}, {"X": 0.10002151131629944, "Y": 0.1342349350452423}, {"X": 0.05518854036927223, "Y": 0.13423459231853485}]}, "Id": "6ea0b5c4-3284-41a9-85a3-920266d53ce5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06340060383081436, "Height": 0.012690558098256588, "Left": 0.10566400736570358, "Top": 0.12158537656068802}, "Polygon": [{"X": 0.10566400736570358, "Y": 0.12158537656068802}, {"X": 0.16906452178955078, "Y": 0.12158586084842682}, {"X": 0.16906461119651794, "Y": 0.13427592813968658}, {"X": 0.10566411167383194, "Y": 0.13427545130252838}]}, "Id": "1539f87d-33dc-4bbd-ac71-f9af5ff20709"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "UA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023203816264867783, "Height": 0.010218033567070961, "Left": 0.4608854353427887, "Top": 0.1215057224035263}, "Polygon": [{"X": 0.4608854353427887, "Y": 0.1215057224035263}, {"X": 0.484089195728302, "Y": 0.12150589376688004}, {"X": 0.4840892553329468, "Y": 0.13172374665737152}, {"X": 0.46088549494743347, "Y": 0.1317235678434372}]}, "Id": "fd687c57-91fa-4346-82d7-5ca81b820783"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Team", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04293079674243927, "Height": 0.010092655196785927, "Left": 0.48847633600234985, "Top": 0.1215585321187973}, "Polygon": [{"X": 0.48847633600234985, "Y": 0.1215585321187973}, {"X": 0.5314071178436279, "Y": 0.12155885994434357}, {"X": 0.5314071178436279, "Y": 0.13165117800235748}, {"X": 0.48847639560699463, "Y": 0.13165085017681122}]}, "Id": "9e3ebeab-001a-4396-860a-59cdb8b339b2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trucking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06996908783912659, "Height": 0.012952038086950779, "Left": 0.5363172888755798, "Top": 0.12141169607639313}, "Polygon": [{"X": 0.5363172888755798, "Y": 0.12141169607639313}, {"X": 0.6062862873077393, "Y": 0.1214122325181961}, {"X": 0.606286346912384, "Y": 0.13436374068260193}, {"X": 0.5363173484802246, "Y": 0.13436320424079895}]}, "Id": "11db00ba-5fc0-4919-9ee0-505cad9488cd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030967941507697105, "Height": 0.010297728702425957, "Left": 0.6124153733253479, "Top": 0.1214313805103302}, "Polygon": [{"X": 0.6124153733253479, "Y": 0.1214313805103302}, {"X": 0.643383264541626, "Y": 0.1214316189289093}, {"X": 0.6433833241462708, "Y": 0.1317291110754013}, {"X": 0.6124154329299927, "Y": 0.1317288726568222}]}, "Id": "8ba035b7-a486-42a3-a93f-0d313f3cf0a9"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022529130801558495, "Height": 0.010224733501672745, "Left": 0.05535098537802696, "Top": 0.13691751658916473}, "Polygon": [{"X": 0.05535098537802696, "Y": 0.13691751658916473}, {"X": 0.07788002490997314, "Y": 0.13691768050193787}, {"X": 0.07788011431694031, "Y": 0.14714224636554718}, {"X": 0.05535107105970383, "Y": 0.14714206755161285}]}, "Id": "5a28eaa3-081b-4e49-90eb-89f3f686dd5b"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027575479820370674, "Height": 0.01006870437413454, "Left": 0.08364683389663696, "Top": 0.13702690601348877}, "Polygon": [{"X": 0.08364683389663696, "Y": 0.13702690601348877}, {"X": 0.11122222989797592, "Y": 0.13702711462974548}, {"X": 0.11122231185436249, "Y": 0.14709560573101044}, {"X": 0.08364691585302353, "Y": 0.14709539711475372}]}, "Id": "fe5ff62d-31bf-4cac-8271-0b4662af9f3f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "2803", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036418575793504715, "Height": 0.009968490339815617, "Left": 0.11616677045822144, "Top": 0.1371190994977951}, "Polygon": [{"X": 0.11616677045822144, "Y": 0.1371190994977951}, {"X": 0.15258526802062988, "Y": 0.1371193826198578}, {"X": 0.15258534252643585, "Y": 0.14708758890628815}, {"X": 0.116166852414608, "Y": 0.14708730578422546}]}, "Id": "042e05a1-65e2-40ab-a384-65dc728adf70"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "27612", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045689843595027924, "Height": 0.010023397393524647, "Left": 0.46024465560913086, "Top": 0.13706377148628235}, "Polygon": [{"X": 0.46024465560913086, "Y": 0.13706377148628235}, {"X": 0.505934476852417, "Y": 0.1370641142129898}, {"X": 0.5059345364570618, "Y": 0.14708717167377472}, {"X": 0.46024471521377563, "Y": 0.14708681404590607}]}, "Id": "ba19fe17-71cf-457b-a2ca-48bf8c4f3edf"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Lorraine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059605684131383896, "Height": 0.010232219472527504, "Left": 0.5117624402046204, "Top": 0.137032613158226}, "Polygon": [{"X": 0.5117624402046204, "Y": 0.137032613158226}, {"X": 0.5713680386543274, "Y": 0.13703307509422302}, {"X": 0.5713680982589722, "Y": 0.14726483821868896}, {"X": 0.5117624998092651, "Y": 0.14726437628269196}]}, "Id": "b13bc6c7-354e-45ef-8a50-65810414f8c6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ave", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028747620061039925, "Height": 0.010276228189468384, "Left": 0.5761436223983765, "Top": 0.13696371018886566}, "Polygon": [{"X": 0.5761436223983765, "Y": 0.13696371018886566}, {"X": 0.6048911809921265, "Y": 0.13696393370628357}, {"X": 0.6048912405967712, "Y": 0.14723993837833405}, {"X": 0.5761436223983765, "Y": 0.14723971486091614}]}, "Id": "9b791233-c5d6-4a26-9120-35d953aef66f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Oshkosh,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06891629099845886, "Height": 0.012011808343231678, "Left": 0.05442748963832855, "Top": 0.15267525613307953}, "Polygon": [{"X": 0.05442748963832855, "Y": 0.15267525613307953}, {"X": 0.12334367632865906, "Y": 0.1526757925748825}, {"X": 0.12334378063678741, "Y": 0.16468706727027893}, {"X": 0.05442759394645691, "Y": 0.16468653082847595}]}, "Id": "39f796ba-5419-4ea0-9900-6e97f4e335f3"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019764332100749016, "Height": 0.010022100061178207, "Left": 0.12877394258975983, "Top": 0.15274089574813843}, "Polygon": [{"X": 0.12877394258975983, "Y": 0.15274089574813843}, {"X": 0.14853818714618683, "Y": 0.15274104475975037}, {"X": 0.1485382616519928, "Y": 0.16276299953460693}, {"X": 0.1287740170955658, "Y": 0.1627628356218338}]}, "Id": "1df8bc7f-1c48-4e73-8c93-28c9abddb1d4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "54903", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045259106904268265, "Height": 0.010170790366828442, "Left": 0.1543397605419159, "Top": 0.15279699862003326}, "Polygon": [{"X": 0.1543397605419159, "Y": 0.15279699862003326}, {"X": 0.1995987892150879, "Y": 0.15279734134674072}, {"X": 0.19959886372089386, "Y": 0.16296778619289398}, {"X": 0.15433983504772186, "Y": 0.16296742856502533}]}, "Id": "f9c22f6d-a4f5-4cc2-9fb8-dea40051eb63"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05818558484315872, "Height": 0.011885819025337696, "Left": 0.46016108989715576, "Top": 0.15277890861034393}, "Polygon": [{"X": 0.46016108989715576, "Y": 0.15277890861034393}, {"X": 0.5183466076850891, "Y": 0.15277935564517975}, {"X": 0.5183466672897339, "Y": 0.16466473042964935}, {"X": 0.46016114950180054, "Y": 0.16466426849365234}]}, "Id": "1ee4c616-1ff8-46fb-8d8b-dc59ca180553"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01676059141755104, "Height": 0.009941663593053818, "Left": 0.5243140459060669, "Top": 0.15293854475021362}, "Polygon": [{"X": 0.5243140459060669, "Y": 0.15293854475021362}, {"X": 0.5410745739936829, "Y": 0.15293866395950317}, {"X": 0.5410746335983276, "Y": 0.16288019716739655}, {"X": 0.5243141055107117, "Y": 0.162880077958107}]}, "Id": "e9fbe9c3-1697-4951-bce4-08388204889f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "48093", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046036962419748306, "Height": 0.010232462547719479, "Left": 0.5467785596847534, "Top": 0.15278084576129913}, "Polygon": [{"X": 0.5467785596847534, "Y": 0.15278084576129913}, {"X": 0.5928155183792114, "Y": 0.15278120338916779}, {"X": 0.5928155779838562, "Y": 0.1630133092403412}, {"X": 0.5467786192893982, "Y": 0.16301293671131134}]}, "Id": "0a17937a-7749-48f1-9ea6-877cfc9393e0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051269445568323135, "Height": 0.010335197672247887, "Left": 0.46112146973609924, "Top": 0.1681002527475357}, "Polygon": [{"X": 0.46112146973609924, "Y": 0.1681002527475357}, {"X": 0.5123908519744873, "Y": 0.16810065507888794}, {"X": 0.5123909115791321, "Y": 0.17843544483184814}, {"X": 0.461121529340744, "Y": 0.1784350425004959}]}, "Id": "d91c8c10-dd86-4808-9d90-7b7f7e0a9146"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "(586)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037305355072021484, "Height": 0.012932042591273785, "Left": 0.518079400062561, "Top": 0.16808484494686127}, "Polygon": [{"X": 0.518079400062561, "Y": 0.16808484494686127}, {"X": 0.5553846955299377, "Y": 0.16808514297008514}, {"X": 0.5553847551345825, "Y": 0.18101689219474792}, {"X": 0.5180794596672058, "Y": 0.18101659417152405}]}, "Id": "250e73d3-308d-4d7a-81ce-80f657165a37"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "208-3007", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06992312520742416, "Height": 0.010069643147289753, "Left": 0.5608510375022888, "Top": 0.16829721629619598}, "Polygon": [{"X": 0.5608510375022888, "Y": 0.16829721629619598}, {"X": 0.6307741403579712, "Y": 0.16829776763916016}, {"X": 0.630774199962616, "Y": 0.17836686968803406}, {"X": 0.5608510971069336, "Y": 0.17836631834506989}]}, "Id": "6b8b55d7-f631-432d-a02f-e76f37540276"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027791563421487808, "Height": 0.010188048705458641, "Left": 0.46030858159065247, "Top": 0.18371152877807617}, "Polygon": [{"X": 0.46030858159065247, "Y": 0.18371152877807617}, {"X": 0.4881000816822052, "Y": 0.18371175229549408}, {"X": 0.48810014128685, "Y": 0.19389957189559937}, {"X": 0.46030861139297485, "Y": 0.19389936327934265}]}, "Id": "44c416ef-9d23-4750-977f-525d8a7ddef8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "ID#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028770247474312782, "Height": 0.010191313922405243, "Left": 0.4931556284427643, "Top": 0.18372943997383118}, "Polygon": [{"X": 0.4931556284427643, "Y": 0.18372943997383118}, {"X": 0.5219258069992065, "Y": 0.18372966349124908}, {"X": 0.5219258666038513, "Y": 0.19392074644565582}, {"X": 0.49315568804740906, "Y": 0.19392052292823792}]}, "Id": "149e5b32-f707-4a22-a9d1-6ffd8b4c7a35"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "*********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08236636221408844, "Height": 0.010115872137248516, "Left": 0.5277118682861328, "Top": 0.18383635580539703}, "Polygon": [{"X": 0.5277118682861328, "Y": 0.18383635580539703}, {"X": 0.6100781559944153, "Y": 0.18383701145648956}, {"X": 0.6100782155990601, "Y": 0.19395223259925842}, {"X": 0.5277119278907776, "Y": 0.1939515769481659}]}, "Id": "3d0e98fb-65d0-4b9f-8cce-270add04cce2"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038220953196287155, "Height": 0.01018323190510273, "Left": 0.1219518631696701, "Top": 0.23216144740581512}, "Polygon": [{"X": 0.1219518631696701, "Y": 0.23216144740581512}, {"X": 0.1601727455854416, "Y": 0.2321617603302002}, {"X": 0.16017282009124756, "Y": 0.2423446923494339}, {"X": 0.12195195257663727, "Y": 0.24234436452388763}]}, "Id": "41827a93-bbb5-440f-ad2c-438e853e3bb3"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009821812622249126, "Height": 0.010193074122071266, "Left": 0.1652122586965561, "Top": 0.23239518702030182}, "Polygon": [{"X": 0.1652122586965561, "Y": 0.23239518702030182}, {"X": 0.17503398656845093, "Y": 0.2323952615261078}, {"X": 0.1750340759754181, "Y": 0.24258826673030853}, {"X": 0.16521233320236206, "Y": 0.24258817732334137}]}, "Id": "352549e7-c93c-4aee-a935-d5828cf938d4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07550541311502457, "Height": 0.012986915186047554, "Left": 0.41991087794303894, "Top": 0.2319207340478897}, "Polygon": [{"X": 0.41991087794303894, "Y": 0.2319207340478897}, {"X": 0.49541622400283813, "Y": 0.23192134499549866}, {"X": 0.4954162836074829, "Y": 0.24490764737129211}, {"X": 0.4199109673500061, "Y": 0.24490702152252197}]}, "Id": "70439e97-a1a0-455d-9900-01b0df843188"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Info", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029595443978905678, "Height": 0.010373065248131752, "Left": 0.500467836856842, "Top": 0.2320474088191986}, "Polygon": [{"X": 0.500467836856842, "Y": 0.2320474088191986}, {"X": 0.5300632119178772, "Y": 0.2320476472377777}, {"X": 0.530063271522522, "Y": 0.2424204796552658}, {"X": 0.5004678964614868, "Y": 0.2424202263355255}]}, "Id": "948601fc-961c-4c13-b865-4062d0431fb7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Primary", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06139915809035301, "Height": 0.012791701592504978, "Left": 0.7274353504180908, "Top": 0.23206663131713867}, "Polygon": [{"X": 0.7274353504180908, "Y": 0.23206663131713867}, {"X": 0.7888344526290894, "Y": 0.23206713795661926}, {"X": 0.7888345122337341, "Y": 0.24485832452774048}, {"X": 0.7274354100227356, "Y": 0.2448578178882599}]}, "Id": "535347f7-c1b4-4d97-b248-4851ad8c3ba0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027664488181471825, "Height": 0.010214968584477901, "Left": 0.7942600250244141, "Top": 0.23213942348957062}, "Polygon": [{"X": 0.7942600250244141, "Y": 0.23213942348957062}, {"X": 0.8219245076179504, "Y": 0.23213964700698853}, {"X": 0.8219245672225952, "Y": 0.2423543930053711}, {"X": 0.7942600846290588, "Y": 0.24235416948795319}]}, "Id": "14df2200-0284-49ca-8bf4-ee7d435b3d15"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Sub", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030996734276413918, "Height": 0.01031631138175726, "Left": 0.8534550070762634, "Top": 0.23208299279212952}, "Polygon": [{"X": 0.8534550070762634, "Y": 0.23208299279212952}, {"X": 0.8844517469406128, "Y": 0.2320832461118698}, {"X": 0.8844517469406128, "Y": 0.24239930510520935}, {"X": 0.8534550666809082, "Y": 0.24239903688430786}]}, "Id": "dc735b31-1d25-4615-a840-2d803a90ff34"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03883356600999832, "Height": 0.01031253207474947, "Left": 0.8890899419784546, "Top": 0.23208382725715637}, "Polygon": [{"X": 0.8890899419784546, "Y": 0.23208382725715637}, {"X": 0.9279235005378723, "Y": 0.23208414018154144}, {"X": 0.9279235005378723, "Y": 0.24239635467529297}, {"X": 0.8890899419784546, "Y": 0.2423960417509079}]}, "Id": "35c930da-c72c-4b00-947f-4fe4d73102ad"}, {"BlockType": "WORD", "Confidence": 99.59721374511719, "Text": "COUNET11778509", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14290457963943481, "Height": 0.0106600197032094, "Left": 0.065498948097229, "Top": 0.26047149300575256}, "Polygon": [{"X": 0.065498948097229, "Y": 0.26047149300575256}, {"X": 0.20840345323085785, "Y": 0.26047268509864807}, {"X": 0.20840352773666382, "Y": 0.2711315155029297}, {"X": 0.06549903750419617, "Y": 0.2711303234100342}]}, "Id": "e3c51618-f03d-4a08-9e40-b572445dbd04"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Pickup", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04871654137969017, "Height": 0.013017741031944752, "Left": 0.2488209456205368, "Top": 0.260477215051651}, "Polygon": [{"X": 0.2488209456205368, "Y": 0.260477215051651}, {"X": 0.2975373864173889, "Y": 0.26047760248184204}, {"X": 0.2975374758243561, "Y": 0.2734949588775635}, {"X": 0.24882103502750397, "Y": 0.27349454164505005}]}, "Id": "136cd674-c09d-4d1d-937f-ce2b5d7da0e6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03806602209806442, "Height": 0.01009963359683752, "Left": 0.30339452624320984, "Top": 0.2607192099094391}, "Polygon": [{"X": 0.30339452624320984, "Y": 0.2607192099094391}, {"X": 0.3414604663848877, "Y": 0.26071950793266296}, {"X": 0.34146052598953247, "Y": 0.270818829536438}, {"X": 0.3033945858478546, "Y": 0.2708185315132141}]}, "Id": "b1fcc583-8708-4c53-b0a2-3355e283b44c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08234947919845581, "Height": 0.010132407769560814, "Left": 0.347318559885025, "Top": 0.2607641816139221}, "Polygon": [{"X": 0.347318559885025, "Y": 0.2607641816139221}, {"X": 0.42966797947883606, "Y": 0.26076486706733704}, {"X": 0.42966803908348083, "Y": 0.2708965837955475}, {"X": 0.3473186194896698, "Y": 0.27089589834213257}]}, "Id": "48229abe-4f3c-473c-96d3-61ee138d3506"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0593765564262867, "Height": 0.012042077258229256, "Left": 0.7191163897514343, "Top": 0.2600824236869812}, "Polygon": [{"X": 0.7191163897514343, "Y": 0.2600824236869812}, {"X": 0.7784929275512695, "Y": 0.2600829303264618}, {"X": 0.7784929871559143, "Y": 0.2721244990825653}, {"X": 0.7191164493560791, "Y": 0.2721240222454071}]}, "Id": "c94bb9d7-828c-4053-af68-e373c9b3d59c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059907104820013046, "Height": 0.01203219871968031, "Left": 0.8463203310966492, "Top": 0.2600804269313812}, "Polygon": [{"X": 0.8463203310966492, "Y": 0.2600804269313812}, {"X": 0.9062274098396301, "Y": 0.2600809335708618}, {"X": 0.9062274694442749, "Y": 0.272112637758255}, {"X": 0.846320390701294, "Y": 0.2721121311187744}]}, "Id": "08348969-c77e-4b57-8ae5-7f48c0738c45"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Delivery", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05900536850094795, "Height": 0.012734874151647091, "Left": 0.2490769773721695, "Top": 0.27606815099716187}, "Polygon": [{"X": 0.2490769773721695, "Y": 0.27606815099716187}, {"X": 0.30808225274086, "Y": 0.27606862783432007}, {"X": 0.30808234214782715, "Y": 0.28880301117897034}, {"X": 0.24907706677913666, "Y": 0.28880250453948975}]}, "Id": "3f4bb93a-3232-4cc1-8d55-e3a5c9fb889c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03803449124097824, "Height": 0.010193600319325924, "Left": 0.31355124711990356, "Top": 0.27610883116722107}, "Polygon": [{"X": 0.31355124711990356, "Y": 0.27610883116722107}, {"X": 0.35158565640449524, "Y": 0.27610915899276733}, {"X": 0.3515857458114624, "Y": 0.28630244731903076}, {"X": 0.31355130672454834, "Y": 0.2863021194934845}]}, "Id": "2a5c787a-05c1-4f6c-a70a-e465f6fa0af3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08242739737033844, "Height": 0.010121524333953857, "Left": 0.3573123812675476, "Top": 0.27621909976005554}, "Polygon": [{"X": 0.3573123812675476, "Y": 0.27621909976005554}, {"X": 0.43973973393440247, "Y": 0.27621978521347046}, {"X": 0.43973979353904724, "Y": 0.2863406240940094}, {"X": 0.3573124408721924, "Y": 0.2863399088382721}]}, "Id": "f5317d37-1e20-43b3-9c41-9babea62d6e7"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055739741772413254, "Height": 0.01288207434117794, "Left": 0.2479972094297409, "Top": 0.2914324402809143}, "Polygon": [{"X": 0.2479972094297409, "Y": 0.2914324402809143}, {"X": 0.3037368655204773, "Y": 0.2914329171180725}, {"X": 0.30373695492744446, "Y": 0.304314523935318}, {"X": 0.24799729883670807, "Y": 0.3043140470981598}]}, "Id": "7c80e3e2-2af4-4d1a-90e4-e5a35136c1fb"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "2000.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05014564096927643, "Height": 0.010004796087741852, "Left": 0.3091021180152893, "Top": 0.291731059551239}, "Polygon": [{"X": 0.3091021180152893, "Y": 0.291731059551239}, {"X": 0.35924771428108215, "Y": 0.29173147678375244}, {"X": 0.35924777388572693, "Y": 0.30173584818840027}, {"X": 0.30910220742225647, "Y": 0.30173543095588684}]}, "Id": "d31b682c-53a0-4f41-b592-5818f0b520bd"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Quantity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06538420170545578, "Height": 0.012782331556081772, "Left": 0.2482532262802124, "Top": 0.3068913221359253}, "Polygon": [{"X": 0.2482532262802124, "Y": 0.3068913221359253}, {"X": 0.3136373460292816, "Y": 0.30689188838005066}, {"X": 0.3136374354362488, "Y": 0.31967365741729736}, {"X": 0.24825331568717957, "Y": 0.319673091173172}]}, "Id": "c58a0035-d2ef-414b-b57b-995d45f71ad6"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "0.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022388804703950882, "Height": 0.01012467686086893, "Left": 0.3194376826286316, "Top": 0.3071277439594269}, "Polygon": [{"X": 0.3194376826286316, "Y": 0.3071277439594269}, {"X": 0.3418264389038086, "Y": 0.3071279227733612}, {"X": 0.34182649850845337, "Y": 0.31725242733955383}, {"X": 0.31943777203559875, "Y": 0.3172522187232971}]}, "Id": "536d349a-e0fd-4f0e-9476-88e168098e94"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "From:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03634960949420929, "Height": 0.00898495689034462, "Left": 0.2487715631723404, "Top": 0.33448922634124756}, "Polygon": [{"X": 0.2487715631723404, "Y": 0.33448922634124756}, {"X": 0.2851211130619049, "Y": 0.33448952436447144}, {"X": 0.2851211726665497, "Y": 0.3434741795063019}, {"X": 0.24877162277698517, "Y": 0.3434738516807556}]}, "Id": "049aba08-86b3-450d-81e0-ac8e45267214"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054014381021261215, "Height": 0.011277100071310997, "Left": 0.24797606468200684, "Top": 0.3478551208972931}, "Polygon": [{"X": 0.24797606468200684, "Y": 0.3478551208972931}, {"X": 0.3019903600215912, "Y": 0.3478555977344513}, {"X": 0.30199044942855835, "Y": 0.35913223028182983}, {"X": 0.2479761391878128, "Y": 0.35913175344467163}]}, "Id": "843d78e1-0223-4228-9c95-d9b025e64da9"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Velocity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05013558268547058, "Height": 0.011131834238767624, "Left": 0.24809324741363525, "Top": 0.3612671494483948}, "Polygon": [{"X": 0.24809324741363525, "Y": 0.3612671494483948}, {"X": 0.29822877049446106, "Y": 0.3612675964832306}, {"X": 0.29822883009910583, "Y": 0.3723990023136139}, {"X": 0.24809332191944122, "Y": 0.3723985552787781}]}, "Id": "ff94922e-f809-42ed-96bf-77c8abcb8316"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2068", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03172808140516281, "Height": 0.008745230734348297, "Left": 0.24784338474273682, "Top": 0.3746849298477173}, "Polygon": [{"X": 0.24784338474273682, "Y": 0.3746849298477173}, {"X": 0.27957141399383545, "Y": 0.37468522787094116}, {"X": 0.2795714735984802, "Y": 0.383430153131485}, {"X": 0.2478434443473816, "Y": 0.3834298849105835}]}, "Id": "48fa29d7-0edc-4209-a1df-ef72fd6529eb"}, {"BlockType": "WORD", "Confidence": 98.99453735351562, "Text": "E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008485376834869385, "Height": 0.008800259791314602, "Left": 0.28484559059143066, "Top": 0.37461769580841064}, "Polygon": [{"X": 0.28484559059143066, "Y": 0.37461769580841064}, {"X": 0.2933309078216553, "Y": 0.3746177852153778}, {"X": 0.29333096742630005, "Y": 0.3834179639816284}, {"X": 0.28484565019607544, "Y": 0.38341787457466125}]}, "Id": "72862e63-34ac-44d2-96d0-c7e799e30bf2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03823317587375641, "Height": 0.009046012535691261, "Left": 0.2981202304363251, "Top": 0.3745931088924408}, "Polygon": [{"X": 0.2981202304363251, "Y": 0.3745931088924408}, {"X": 0.3363533616065979, "Y": 0.37459343671798706}, {"X": 0.3363534212112427, "Y": 0.3836391270160675}, {"X": 0.29812029004096985, "Y": 0.38363876938819885}]}, "Id": "5e8c2b1d-17b3-4f6d-9a5b-abbb85cc89db"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Belleville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05921011418104172, "Height": 0.010568451136350632, "Left": 0.24876055121421814, "Top": 0.3878033459186554}, "Polygon": [{"X": 0.24876055121421814, "Y": 0.3878033459186554}, {"X": 0.3079706132411957, "Y": 0.3878038823604584}, {"X": 0.30797067284584045, "Y": 0.39837178587913513}, {"X": 0.2487606406211853, "Y": 0.39837124943733215}]}, "Id": "48591b8a-b78c-47c6-98b5-94e7c7bcde40"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014843649230897427, "Height": 0.00892088282853365, "Left": 0.31341612339019775, "Top": 0.3878971040248871}, "Polygon": [{"X": 0.31341612339019775, "Y": 0.3878971040248871}, {"X": 0.3282597064971924, "Y": 0.38789722323417664}, {"X": 0.32825976610183716, "Y": 0.39681798219680786}, {"X": 0.31341618299484253, "Y": 0.3968178331851959}]}, "Id": "3508dab3-c26a-46df-a7c4-daab0ba2f7e4"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "48111", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03752328082919121, "Height": 0.00899704173207283, "Left": 0.332783043384552, "Top": 0.38780122995376587}, "Polygon": [{"X": 0.332783043384552, "Y": 0.38780122995376587}, {"X": 0.37030625343322754, "Y": 0.3878015875816345}, {"X": 0.3703063130378723, "Y": 0.3967982828617096}, {"X": 0.3327831029891968, "Y": 0.39679795503616333}]}, "Id": "98c784ef-a786-46f3-95cd-e5297c3e5c62"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02044912427663803, "Height": 0.00911638978868723, "Left": 0.2478071004152298, "Top": 0.4133202135562897}, "Polygon": [{"X": 0.2478071004152298, "Y": 0.4133202135562897}, {"X": 0.26825615763664246, "Y": 0.4133204221725464}, {"X": 0.26825621724128723, "Y": 0.4224366247653961}, {"X": 0.24780716001987457, "Y": 0.4224364161491394}]}, "Id": "6d2dc70b-0738-4c81-a0d3-4f2e784941bf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Consignee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0731496661901474, "Height": 0.01125477347522974, "Left": 0.24828116595745087, "Top": 0.4266597628593445}, "Polygon": [{"X": 0.24828116595745087, "Y": 0.4266597628593445}, {"X": 0.3214307725429535, "Y": 0.4266604483127594}, {"X": 0.32143083214759827, "Y": 0.43791455030441284}, {"X": 0.24828125536441803, "Y": 0.4379138648509979}]}, "Id": "08c5c2c9-6259-4ffa-9bb0-85c30bb01b94"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "BRIDGEWATER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1065833643078804, "Height": 0.00886530987918377, "Left": 0.24856384098529816, "Top": 0.4400305151939392}, "Polygon": [{"X": 0.24856384098529816, "Y": 0.4400305151939392}, {"X": 0.3551471531391144, "Y": 0.440031498670578}, {"X": 0.35514721274375916, "Y": 0.4488958418369293}, {"X": 0.24856390058994293, "Y": 0.44889482855796814}]}, "Id": "0c5d7e8c-5006-4add-b8ea-858743faaa80"}, {"BlockType": "WORD", "Confidence": 99.56314086914062, "Text": "INTERIORS-WARREN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1464131474494934, "Height": 0.008756555616855621, "Left": 0.36000746488571167, "Top": 0.4400528371334076}, "Polygon": [{"X": 0.36000746488571167, "Y": 0.4400528371334076}, {"X": 0.5064205527305603, "Y": 0.44005417823791504}, {"X": 0.5064206123352051, "Y": 0.4488093852996826}, {"X": 0.36000752449035645, "Y": 0.44880804419517517}]}, "Id": "71d41800-d7ad-45aa-a0f7-24d865ae05ad"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "7500", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031430866569280624, "Height": 0.008818821981549263, "Left": 0.24811379611492157, "Top": 0.45362961292266846}, "Polygon": [{"X": 0.24811379611492157, "Y": 0.45362961292266846}, {"X": 0.2795445919036865, "Y": 0.45362991094589233}, {"X": 0.2795446515083313, "Y": 0.46244844794273376}, {"X": 0.24811385571956635, "Y": 0.4624481499195099}]}, "Id": "473f3a3c-b728-4896-9732-bc0ac8dbb108"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038580525666475296, "Height": 0.008992412127554417, "Left": 0.28389373421669006, "Top": 0.45315778255462646}, "Polygon": [{"X": 0.28389373421669006, "Y": 0.45315778255462646}, {"X": 0.3224742114543915, "Y": 0.4531581401824951}, {"X": 0.32247427105903625, "Y": 0.4621501863002777}, {"X": 0.28389379382133484, "Y": 0.46214982867240906}]}, "Id": "761be1b4-c35d-4dba-89d5-abe9ce18016e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ROAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04058050736784935, "Height": 0.008884851820766926, "Left": 0.32723936438560486, "Top": 0.45343369245529175}, "Polygon": [{"X": 0.32723936438560486, "Y": 0.45343369245529175}, {"X": 0.36781981587409973, "Y": 0.4534340798854828}, {"X": 0.3678198754787445, "Y": 0.4623185694217682}, {"X": 0.32723942399024963, "Y": 0.46231818199157715}]}, "Id": "1a04de0d-1066-4a47-9267-e53dcffab322"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050636254251003265, "Height": 0.010591337457299232, "Left": 0.24773253500461578, "Top": 0.4665999412536621}, "Polygon": [{"X": 0.24773253500461578, "Y": 0.4665999412536621}, {"X": 0.2983687222003937, "Y": 0.4666004180908203}, {"X": 0.29836878180503845, "Y": 0.477191299200058}, {"X": 0.24773260951042175, "Y": 0.4771908223628998}]}, "Id": "9f6a2b08-4d29-4d91-92d4-b84de0bea979"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014939446933567524, "Height": 0.008903578855097294, "Left": 0.3036796450614929, "Top": 0.46664324402809143}, "Polygon": [{"X": 0.3036796450614929, "Y": 0.46664324402809143}, {"X": 0.3186190128326416, "Y": 0.46664339303970337}, {"X": 0.3186190724372864, "Y": 0.47554683685302734}, {"X": 0.3036797046661377, "Y": 0.4755466878414154}]}, "Id": "12a4fa12-1400-41e5-968c-151267724715"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "48093", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03986712172627449, "Height": 0.00881007220596075, "Left": 0.32323259115219116, "Top": 0.46680498123168945}, "Polygon": [{"X": 0.32323259115219116, "Y": 0.46680498123168945}, {"X": 0.3630996346473694, "Y": 0.4668053686618805}, {"X": 0.36309969425201416, "Y": 0.4756150543689728}, {"X": 0.32323265075683594, "Y": 0.4756146967411041}]}, "Id": "43fc11ea-4644-448a-856a-023c2a509fb0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07218888401985168, "Height": 0.010248247534036636, "Left": 0.24842818081378937, "Top": 0.4924495816230774}, "Polygon": [{"X": 0.24842818081378937, "Y": 0.4924495816230774}, {"X": 0.3206169903278351, "Y": 0.4924502670764923}, {"X": 0.32061707973480225, "Y": 0.5026978254318237}, {"X": 0.24842825531959534, "Y": 0.5026971101760864}]}, "Id": "ccf8c1c6-2d35-4512-92ff-fd0f197ec1f2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Required", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0657878965139389, "Height": 0.012718030251562595, "Left": 0.32553091645240784, "Top": 0.492523193359375}, "Polygon": [{"X": 0.32553091645240784, "Y": 0.492523193359375}, {"X": 0.39131873846054077, "Y": 0.49252381920814514}, {"X": 0.39131882786750793, "Y": 0.5052412152290344}, {"X": 0.325531005859375, "Y": 0.5052406191825867}]}, "Id": "33c9f551-a3f0-4e1a-aab9-3bdd608f210a"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Info:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030652036890387535, "Height": 0.010279811918735504, "Left": 0.3978461027145386, "Top": 0.49244993925094604}, "Polygon": [{"X": 0.3978461027145386, "Y": 0.49244993925094604}, {"X": 0.4284980893135071, "Y": 0.4924502372741699}, {"X": 0.42849814891815186, "Y": 0.5027297735214233}, {"X": 0.39784616231918335, "Y": 0.5027294754981995}]}, "Id": "cc4954bf-ed72-47e5-a652-4deebb1f421e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042279865592718124, "Height": 0.010462116450071335, "Left": 0.43428000807762146, "Top": 0.49227362871170044}, "Polygon": [{"X": 0.43428000807762146, "Y": 0.49227362871170044}, {"X": 0.4765598177909851, "Y": 0.4922740161418915}, {"X": 0.4765598773956299, "Y": 0.5027357339859009}, {"X": 0.43428006768226624, "Y": 0.5027353167533875}]}, "Id": "c4a3fe9b-6af8-482e-bb8e-ca49f7ee2466"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013772254809737206, "Height": 0.00982552021741867, "Left": 0.48079541325569153, "Top": 0.4929518401622772}, "Polygon": [{"X": 0.48079541325569153, "Y": 0.4929518401622772}, {"X": 0.4945676326751709, "Y": 0.4929519593715668}, {"X": 0.4945676624774933, "Y": 0.5027773380279541}, {"X": 0.4807954728603363, "Y": 0.5027772188186646}]}, "Id": "5a9d884b-3dc6-4b97-9fef-d2228e22ad91"}, {"BlockType": "WORD", "Confidence": 99.59881591796875, "Text": "COUNET11778509", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14321038126945496, "Height": 0.010481234639883041, "Left": 0.4999159872531891, "Top": 0.492149293422699}, "Polygon": [{"X": 0.4999159872531891, "Y": 0.492149293422699}, {"X": 0.6431263089179993, "Y": 0.4921506345272064}, {"X": 0.643126368522644, "Y": 0.5026305317878723}, {"X": 0.49991604685783386, "Y": 0.5026291608810425}]}, "Id": "8915a488-de2f-470d-ad87-282eab990009"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Primary", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05613038316369057, "Height": 0.012862595729529858, "Left": 0.24892054498195648, "Top": 0.5078725218772888}, "Polygon": [{"X": 0.24892054498195648, "Y": 0.5078725218772888}, {"X": 0.3050508499145508, "Y": 0.5078730583190918}, {"X": 0.30505093932151794, "Y": 0.5207351446151733}, {"X": 0.24892064929008484, "Y": 0.5207346081733704}]}, "Id": "941224cb-72fa-4e06-8dd6-3b26010c8c34"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027166016399860382, "Height": 0.010306435637176037, "Left": 0.3108733296394348, "Top": 0.5078610181808472}, "Polygon": [{"X": 0.3108733296394348, "Y": 0.5078610181808472}, {"X": 0.3380392789840698, "Y": 0.507861316204071}, {"X": 0.338039368391037, "Y": 0.5181674957275391}, {"X": 0.310873419046402, "Y": 0.5181671977043152}]}, "Id": "a945d136-3865-486a-b02c-b123ca987090"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Based", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04577130451798439, "Height": 0.010386924259364605, "Left": 0.3438252806663513, "Top": 0.5077660083770752}, "Polygon": [{"X": 0.3438252806663513, "Y": 0.5077660083770752}, {"X": 0.38959652185440063, "Y": 0.5077664256095886}, {"X": 0.3895966112613678, "Y": 0.5181528925895691}, {"X": 0.3438253700733185, "Y": 0.5181524753570557}]}, "Id": "a2db03cf-5f19-4cae-8cfa-7202843ee7ae"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017434148117899895, "Height": 0.007456941995769739, "Left": 0.39508938789367676, "Top": 0.510581374168396}, "Polygon": [{"X": 0.39508938789367676, "Y": 0.510581374168396}, {"X": 0.4125235080718994, "Y": 0.5105815529823303}, {"X": 0.4125235378742218, "Y": 0.518038272857666}, {"X": 0.39508944749832153, "Y": 0.5180381536483765}]}, "Id": "73987bb9-3dfe-4bd9-96d1-5c72dd44873b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Flat", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027046285569667816, "Height": 0.010443937964737415, "Left": 0.41895052790641785, "Top": 0.5077545046806335}, "Polygon": [{"X": 0.41895052790641785, "Y": 0.5077545046806335}, {"X": 0.4459967613220215, "Y": 0.5077547430992126}, {"X": 0.44599682092666626, "Y": 0.5181984305381775}, {"X": 0.4189505875110626, "Y": 0.5181981921195984}]}, "Id": "9409eb7c-efc4-4e02-bcea-1432addb4f50"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031468529254198074, "Height": 0.010448907501995564, "Left": 0.45123422145843506, "Top": 0.5078584551811218}, "Polygon": [{"X": 0.45123422145843506, "Y": 0.5078584551811218}, {"X": 0.48270270228385925, "Y": 0.5078587532043457}, {"X": 0.48270276188850403, "Y": 0.5183073282241821}, {"X": 0.45123428106307983, "Y": 0.5183070302009583}]}, "Id": "2aff00bf-a405-427f-b5f0-9bc502c9bc00"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "$400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059428222477436066, "Height": 0.012258187867701054, "Left": 0.48836079239845276, "Top": 0.5073986053466797}, "Polygon": [{"X": 0.48836079239845276, "Y": 0.5073986053466797}, {"X": 0.5477889776229858, "Y": 0.5073991417884827}, {"X": 0.5477890372276306, "Y": 0.519656777381897}, {"X": 0.48836085200309753, "Y": 0.5196561813354492}]}, "Id": "6ea63c94-ba8c-4c62-be8b-d6e8313279a1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Accessory", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07663111388683319, "Height": 0.012660537846386433, "Left": 0.2480010986328125, "Top": 0.5235564112663269}, "Polygon": [{"X": 0.2480010986328125, "Y": 0.5235564112663269}, {"X": 0.3246321380138397, "Y": 0.5235571265220642}, {"X": 0.3246322274208069, "Y": 0.5362169146537781}, {"X": 0.24800118803977966, "Y": 0.5362161993980408}]}, "Id": "5aa1f154-34a7-4cc7-bce1-22722d0c2125"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fees", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03534436225891113, "Height": 0.010250058956444263, "Left": 0.3303516209125519, "Top": 0.5234560966491699}, "Polygon": [{"X": 0.3303516209125519, "Y": 0.5234560966491699}, {"X": 0.36569592356681824, "Y": 0.5234563946723938}, {"X": 0.365695983171463, "Y": 0.5337061285972595}, {"X": 0.33035168051719666, "Y": 0.5337057709693909}]}, "Id": "96fb09c6-cb0b-447f-893e-28d8b5235fb9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Balance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0625152513384819, "Height": 0.010523717850446701, "Left": 0.7664555907249451, "Top": 0.563807487487793}, "Polygon": [{"X": 0.7664555907249451, "Y": 0.563807487487793}, {"X": 0.8289707899093628, "Y": 0.5638080835342407}, {"X": 0.8289708495140076, "Y": 0.5743312239646912}, {"X": 0.7664555907249451, "Y": 0.5743305683135986}]}, "Id": "848688d5-d2c4-4067-987b-bf4473fe6c25"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Due:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03518511354923248, "Height": 0.010248943231999874, "Left": 0.8347940444946289, "Top": 0.5639014840126038}, "Polygon": [{"X": 0.8347940444946289, "Y": 0.5639014840126038}, {"X": 0.8699791431427002, "Y": 0.5639017820358276}, {"X": 0.8699791431427002, "Y": 0.5741503834724426}, {"X": 0.8347940444946289, "Y": 0.5741500854492188}]}, "Id": "d49a33ef-0e8e-4c03-b7c1-511ffa31dea3"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "$400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0590832456946373, "Height": 0.012276082299649715, "Left": 0.875699520111084, "Top": 0.5635399222373962}, "Polygon": [{"X": 0.875699520111084, "Y": 0.5635399222373962}, {"X": 0.9347827434539795, "Y": 0.563540518283844}, {"X": 0.9347827434539795, "Y": 0.5758159756660461}, {"X": 0.875699520111084, "Y": 0.5758154392242432}]}, "Id": "cde8f4c8-5283-43c2-9c9f-352099fe4896"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05635680630803108, "Height": 0.010275598615407944, "Left": 0.5111465454101562, "Top": 0.6050776839256287}, "Polygon": [{"X": 0.5111465454101562, "Y": 0.6050776839256287}, {"X": 0.5675033330917358, "Y": 0.6050782203674316}, {"X": 0.5675033926963806, "Y": 0.6153532862663269}, {"X": 0.511146605014801, "Y": 0.6153526902198792}]}, "Id": "33cd10a4-98da-4336-8b0d-6e7f8f22b49f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039201993495225906, "Height": 0.010418055579066277, "Left": 0.5723448395729065, "Top": 0.6050041913986206}, "Polygon": [{"X": 0.5723448395729065, "Y": 0.6050041913986206}, {"X": 0.6115468144416809, "Y": 0.6050045490264893}, {"X": 0.6115468144416809, "Y": 0.6154221892356873}, {"X": 0.5723448991775513, "Y": 0.6154218316078186}]}, "Id": "a1f9915f-b580-406a-b509-7b6dc29951d2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06002386659383774, "Height": 0.012233763933181763, "Left": 0.8759718537330627, "Top": 0.6046354174613953}, "Polygon": [{"X": 0.8759718537330627, "Y": 0.6046354174613953}, {"X": 0.9359956979751587, "Y": 0.604636013507843}, {"X": 0.9359957575798035, "Y": 0.6168691515922546}, {"X": 0.8759719133377075, "Y": 0.6168685555458069}]}, "Id": "95d5473b-da05-49fd-8378-64e638b393c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Balance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062414661049842834, "Height": 0.010127175599336624, "Left": 0.5116085410118103, "Top": 0.6331833600997925}, "Polygon": [{"X": 0.5116085410118103, "Y": 0.6331833600997925}, {"X": 0.5740231275558472, "Y": 0.633184015750885}, {"X": 0.5740231871604919, "Y": 0.643310546875}, {"X": 0.5116086006164551, "Y": 0.6433098912239075}]}, "Id": "b128f84f-9c6f-4048-bbd1-c0ff3534e321"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03042510151863098, "Height": 0.009954959154129028, "Left": 0.5796588659286499, "Top": 0.6332359313964844}, "Polygon": [{"X": 0.5796588659286499, "Y": 0.6332359313964844}, {"X": 0.6100839376449585, "Y": 0.633236289024353}, {"X": 0.6100839376449585, "Y": 0.6431909203529358}, {"X": 0.5796589255332947, "Y": 0.6431906223297119}]}, "Id": "41465506-6115-47db-9ca0-b1ad1e15c546"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05985615402460098, "Height": 0.012059591710567474, "Left": 0.8757067918777466, "Top": 0.6328343749046326}, "Polygon": [{"X": 0.8757067918777466, "Y": 0.6328343749046326}, {"X": 0.9355629086494446, "Y": 0.6328349709510803}, {"X": 0.9355629086494446, "Y": 0.6448939442634583}, {"X": 0.8757067918777466, "Y": 0.6448933482170105}]}, "Id": "a256a1ba-5f41-495e-b384-376671edb2f1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "We", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02461167983710766, "Height": 0.009961524046957493, "Left": 0.39042484760284424, "Top": 0.66783207654953}, "Polygon": [{"X": 0.39042484760284424, "Y": 0.66783207654953}, {"X": 0.41503646969795227, "Y": 0.6678323149681091}, {"X": 0.41503652930259705, "Y": 0.677793562412262}, {"X": 0.390424907207489, "Y": 0.6777933239936829}]}, "Id": "dcf2c46e-b69d-45a6-ab60-f5e4f44bfb30"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "appreciate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07700812816619873, "Height": 0.013116665184497833, "Left": 0.4202737510204315, "Top": 0.667784571647644}, "Polygon": [{"X": 0.4202737510204315, "Y": 0.667784571647644}, {"X": 0.4972818195819855, "Y": 0.6677853465080261}, {"X": 0.49728187918663025, "Y": 0.6809012293815613}, {"X": 0.4202738404273987, "Y": 0.6809003949165344}]}, "Id": "d707e64e-5324-42c7-82a6-8d91bdbd9116"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03318518400192261, "Height": 0.010290587320923805, "Left": 0.5018907785415649, "Top": 0.6703010201454163}, "Polygon": [{"X": 0.5018907785415649, "Y": 0.6703010201454163}, {"X": 0.5350759029388428, "Y": 0.6703013777732849}, {"X": 0.5350759625434875, "Y": 0.6805915832519531}, {"X": 0.5018908381462097, "Y": 0.6805912256240845}]}, "Id": "ec90bf30-6bd2-41eb-86ca-02f5e3b6ed56"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "business!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06916053593158722, "Height": 0.010420238599181175, "Left": 0.5395915508270264, "Top": 0.6677719354629517}, "Polygon": [{"X": 0.5395915508270264, "Y": 0.6677719354629517}, {"X": 0.6087520122528076, "Y": 0.667772650718689}, {"X": 0.6087520718574524, "Y": 0.6781921982765198}, {"X": 0.5395916104316711, "Y": 0.6781914830207825}]}, "Id": "583a7971-35b3-4021-8347-5ba3029f4d6a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "aa2736c6-032c-4ee0-89a0-cb2ce727b96d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "aa2736c6-032c-4ee0-89a0-cb2ce727b96d", "content-type": "application/x-amz-json-1.1", "content-length": "74890", "date": "Wed, 24 Sep 2025 12:59:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "7c5c01e3-1dfc-44d3-b58b-9518543fce48", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:59:41 GMT", "content-type": "application/json", "content-length": "749", "connection": "keep-alive", "x-amzn-requestid": "7c5c01e3-1dfc-44d3-b58b-9518543fce48"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. We need to output a single object with page_no: 1, doc_type.\n\nWe need to decide if this is invoice or comm_invoice or lumper_receipt or other. The document contains \"UA TERM Invoice #0393\" and appears to be a carrier invoice. It has PO Box? There's no HS code, no customs. It's an invoice. Not lumper because no lumper-specific. So classify as invoice. Output via tool call.\n\n"}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"invoice\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2778, "outputTokens": 120, "totalTokens": 2898}, "metrics": {"latencyMs": 717}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/c2a71800_CMGYRKEPEEGIJHOFEAQ9.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nUA TERM\nInvoice #0393\nDate 09/19/2025\nTO:\nFROM:\nEnvoy Logistics\nUA Team Trucking LLC\nPO Box 2803\n27612 Lorraine Ave\nOshkosh, WI 54903\nWarren, MI 48093\nPhone: (*************\nTax ID#: *********\nLoad #\nShipment Info\nPrimary Fee\nSub Total\nCOUNET11778509\nPickup Date: 09/19/2025\n$400.00\n$400.00\nDelivery Date: 09/19/2025\nWeight: 2000.0\nQuantity: 0.0\nFrom:\nShipper\nVelocity\n2068 E Street\nBelleville, MI 48111\nTo:\nConsignee\nBRIDGEWATER INTERIORS-WARREN\n7500 TANK ROAD\nWarren, MI 48093\nCustomer Required Info: Order #: COUNET11778509\nPrimary Fee Based on Flat Fee: $400.00\nAccessory Fees\nBalance Due: $400.00\nInvoice Total\n$400.00\nBalance Due\n$400.00\nWe appreciate your business!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}