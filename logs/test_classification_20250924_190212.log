2025-09-24 19:02:12,180 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190212.log
2025-09-24 19:02:12,181 - INFO - 📁 Found 12 files to process
2025-09-24 19:02:12,181 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:02:12,181 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 19:02:12,181 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 19:02:12,181 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 19:02:12,181 - INFO - ⬆️ [19:02:12] Uploading: BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:14,349 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf -> s3://document-extraction-logistically/temp/7f42ccf2_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:14,350 - INFO - 🔍 [19:02:14] Starting classification: BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:14,350 - INFO - ⬆️ [19:02:14] Uploading: E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:14,351 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:14,373 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:14,380 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7f42ccf2_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:14,380 - INFO - Processing PDF from S3...
2025-09-24 19:02:14,380 - INFO - Downloading PDF from S3 to /tmp/tmp1gjpv48a/7f42ccf2_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:18,110 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:02:18,110 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:18,111 - INFO - Splitting PDF 7f42ccf2_BJYFRYT0V8O045HGDXJ6 into 1 pages
2025-09-24 19:02:18,116 - INFO - Split PDF into 1 pages
2025-09-24 19:02:18,116 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:18,117 - INFO - Expected pages: [1]
2025-09-24 19:02:18,758 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg -> s3://document-extraction-logistically/temp/f484a675_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:18,758 - INFO - 🔍 [19:02:18] Starting classification: E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:18,759 - INFO - ⬆️ [19:02:18] Uploading: EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:18,761 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:18,777 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:18,782 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f484a675_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:18,783 - INFO - Processing image from S3...
2025-09-24 19:02:19,424 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf -> s3://document-extraction-logistically/temp/74a6d8bd_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:19,424 - INFO - 🔍 [19:02:19] Starting classification: EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:19,425 - INFO - ⬆️ [19:02:19] Uploading: GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:19,433 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:19,448 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:19,452 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/74a6d8bd_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:19,452 - INFO - Processing PDF from S3...
2025-09-24 19:02:19,452 - INFO - Downloading PDF from S3 to /tmp/tmp78j9j2q2/74a6d8bd_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:20,039 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf -> s3://document-extraction-logistically/temp/f4a879bf_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:20,040 - INFO - 🔍 [19:02:20] Starting classification: GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:20,041 - INFO - ⬆️ [19:02:20] Uploading: HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:20,041 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:20,060 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:20,064 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f4a879bf_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:20,065 - INFO - Processing PDF from S3...
2025-09-24 19:02:20,065 - INFO - Downloading PDF from S3 to /tmp/tmpvwnwchg9/f4a879bf_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:20,684 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg -> s3://document-extraction-logistically/temp/de3cd93d_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:20,685 - INFO - 🔍 [19:02:20] Starting classification: HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:20,686 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:20,686 - INFO - ⬆️ [19:02:20] Uploading: OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:20,706 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:20,712 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/de3cd93d_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:20,713 - INFO - Processing image from S3...
2025-09-24 19:02:20,767 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:02:20,767 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:20,769 - INFO - Splitting PDF 74a6d8bd_EKMN29XJBZVFPLOYTKU7 into 1 pages
2025-09-24 19:02:20,771 - INFO - Split PDF into 1 pages
2025-09-24 19:02:20,772 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:20,772 - INFO - Expected pages: [1]
2025-09-24 19:02:21,348 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf -> s3://document-extraction-logistically/temp/43963e80_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:21,348 - INFO - 🔍 [19:02:21] Starting classification: OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:21,350 - INFO - ⬆️ [19:02:21] Uploading: T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:21,358 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:21,376 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:21,382 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/43963e80_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:21,382 - INFO - Processing PDF from S3...
2025-09-24 19:02:21,383 - INFO - Downloading PDF from S3 to /tmp/tmpiuxybcog/43963e80_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:21,649 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:02:21,650 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:21,651 - INFO - Splitting PDF f4a879bf_GHXKMFASS6CGN362R5BW into 1 pages
2025-09-24 19:02:21,658 - INFO - Split PDF into 1 pages
2025-09-24 19:02:21,658 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:21,658 - INFO - Expected pages: [1]
2025-09-24 19:02:21,975 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png -> s3://document-extraction-logistically/temp/4408cbb9_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:21,975 - INFO - 🔍 [19:02:21] Starting classification: T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:21,976 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:21,978 - INFO - ⬆️ [19:02:21] Uploading: UGM225037492OGWQHUHR.png
2025-09-24 19:02:21,985 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:21,990 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4408cbb9_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:21,990 - INFO - Processing image from S3...
2025-09-24 19:02:22,565 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png -> s3://document-extraction-logistically/temp/2c871dfd_UGM225037492OGWQHUHR.png
2025-09-24 19:02:22,566 - INFO - 🔍 [19:02:22] Starting classification: UGM225037492OGWQHUHR.png
2025-09-24 19:02:22,567 - INFO - ⬆️ [19:02:22] Uploading: VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:22,567 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:22,588 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:22,591 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2c871dfd_UGM225037492OGWQHUHR.png
2025-09-24 19:02:22,591 - INFO - Processing image from S3...
2025-09-24 19:02:22,603 - INFO - S3 Image temp/f484a675_E88AUSUP8065SEI9GD5Q.jpg: Extracted 516 characters, 26 lines
2025-09-24 19:02:22,603 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:22,603 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:23,172 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf -> s3://document-extraction-logistically/temp/f0640456_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:23,172 - INFO - 🔍 [19:02:23] Starting classification: VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:23,173 - INFO - ⬆️ [19:02:23] Uploading: WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:23,180 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:23,183 - INFO - Page 1: Extracted 395 characters, 28 lines from 7f42ccf2_BJYFRYT0V8O045HGDXJ6_90085053_page_001.pdf
2025-09-24 19:02:23,197 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:23,197 - INFO - Successfully processed page 1
2025-09-24 19:02:23,199 - INFO - Combined 1 pages into final text
2025-09-24 19:02:23,200 - INFO - Text validation for 7f42ccf2_BJYFRYT0V8O045HGDXJ6: 412 characters, 1 pages
2025-09-24 19:02:23,201 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f0640456_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:23,202 - INFO - Processing PDF from S3...
2025-09-24 19:02:23,202 - INFO - Downloading PDF from S3 to /tmp/tmpnvziwzyr/f0640456_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:23,202 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:23,207 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:23,212 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:02:23,212 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:23,214 - INFO - Splitting PDF 43963e80_OO7629V25ZJ22FHL0PAA into 2 pages
2025-09-24 19:02:23,222 - INFO - Split PDF into 2 pages
2025-09-24 19:02:23,222 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:23,222 - INFO - Expected pages: [1, 2]
2025-09-24 19:02:23,941 - INFO - S3 Image temp/de3cd93d_HZNQ971O2V9MGG5MU1T3.jpeg: Extracted 655 characters, 33 lines
2025-09-24 19:02:23,941 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:23,942 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:24,119 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf -> s3://document-extraction-logistically/temp/5cb7d34d_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:24,119 - INFO - 🔍 [19:02:24] Starting classification: WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:24,119 - INFO - ⬆️ [19:02:24] Uploading: YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:24,121 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:24,136 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:24,140 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5cb7d34d_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:24,140 - INFO - Processing PDF from S3...
2025-09-24 19:02:24,140 - INFO - Downloading PDF from S3 to /tmp/tmps7zqm549/5cb7d34d_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:24,335 - INFO - S3 Image temp/4408cbb9_T5U48WX7H3FNSZPBDBLZ.png: Extracted 199 characters, 25 lines
2025-09-24 19:02:24,335 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:24,335 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:24,647 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:02:24,647 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:24,648 - INFO - Splitting PDF f0640456_VY0AAD98JWYCZUAHKROB into 1 pages
2025-09-24 19:02:24,648 - INFO - Split PDF into 1 pages
2025-09-24 19:02:24,648 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:24,648 - INFO - Expected pages: [1]
2025-09-24 19:02:24,677 - INFO - S3 Image temp/2c871dfd_UGM225037492OGWQHUHR.png: Extracted 223 characters, 18 lines
2025-09-24 19:02:24,677 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:24,677 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:24,688 - INFO - Page 1: Extracted 525 characters, 25 lines from 74a6d8bd_EKMN29XJBZVFPLOYTKU7_1de88e27_page_001.pdf
2025-09-24 19:02:24,689 - INFO - Successfully processed page 1
2025-09-24 19:02:24,689 - INFO - Combined 1 pages into final text
2025-09-24 19:02:24,689 - INFO - Text validation for 74a6d8bd_EKMN29XJBZVFPLOYTKU7: 542 characters, 1 pages
2025-09-24 19:02:24,690 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:24,690 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:24,702 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf -> s3://document-extraction-logistically/temp/d4d5d0cd_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:24,702 - INFO - 🔍 [19:02:24] Starting classification: YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:24,703 - INFO - ⬆️ [19:02:24] Uploading: ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:24,704 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:24,719 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:24,723 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d4d5d0cd_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:24,723 - INFO - Processing PDF from S3...
2025-09-24 19:02:24,723 - INFO - Downloading PDF from S3 to /tmp/tmpoeiwgw4d/d4d5d0cd_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:24,790 - INFO - Page 1: Extracted 404 characters, 28 lines from f4a879bf_GHXKMFASS6CGN362R5BW_d725a3d3_page_001.pdf
2025-09-24 19:02:24,790 - INFO - Successfully processed page 1
2025-09-24 19:02:24,791 - INFO - Combined 1 pages into final text
2025-09-24 19:02:24,791 - INFO - Text validation for f4a879bf_GHXKMFASS6CGN362R5BW: 421 characters, 1 pages
2025-09-24 19:02:24,791 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:24,791 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:25,319 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'db72723e-7f20-4566-afda-8a023dafa9fa', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:25 GMT', 'content-type': 'application/json', 'content-length': '1598', 'connection': 'keep-alive', 'x-amzn-requestid': 'db72723e-7f20-4566-afda-8a023dafa9fa'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The page content: HUSKY Terminal And Stevedoring, Inc. YANG MING LINE. Looks like an Export Inward or Outbound? The header says "FULL-OUT" "IMPORT-OUT EIR". It has container info, vessel, voyage, driver license, trucking co, truck license, etc. This looks like a gate-out document, specifically an Outgate document. Let\'s check definitions.\n\nOutgate: Record of vehicle/container exiting a facility. Keywords: Outgate, Departed, Gate Out, Full out, etc. The page has "FULL-OUT" which indicates full out. Also has driver license, truck license, etc. So it\'s an outgate. There\'s also "IMPORT-OUT EIR". EIR stands for Export Inward Receipt? But "IMPORT-OUT EIR" maybe means EIR for import out? Not sure.\n\nBut we have "FULL-OUT" and "CONTAINER GROSS WEIGHT(LBS)" so it\'s a gate-out. Outgate doc type.\n\nWe must output JSON with documents array containing one object for page 1 with doc_type "outgate". Let\'s ensure no other classification fits better. "scale_ticket" would involve weight but also scale location etc. This page includes weight but also container and driver license, etc. The presence of "FULL-OUT" indicates outgate. Outgate doc is more likely.\n\nThus output.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "outgate"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2783, 'outputTokens': 327, 'totalTokens': 3110}, 'metrics': {'latencyMs': 1573}}
2025-09-24 19:02:25,320 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f484a675_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:25,604 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf -> s3://document-extraction-logistically/temp/43715a69_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:25,604 - INFO - 🔍 [19:02:25] Starting classification: ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:25,605 - INFO - Initializing TextractProcessor...
2025-09-24 19:02:25,617 - INFO - Initializing BedrockProcessor...
2025-09-24 19:02:25,622 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/43715a69_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:25,623 - INFO - Processing PDF from S3...
2025-09-24 19:02:25,631 - INFO - Downloading PDF from S3 to /tmp/tmp65z9cjce/43715a69_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:25,641 - INFO - 

E88AUSUP8065SEI9GD5Q.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:25,641 - INFO - 

✓ Saved result: output/run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 19:02:25,774 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6a504009-8427-4b10-87a8-4db7525b22e6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:25 GMT', 'content-type': 'application/json', 'content-length': '1396', 'connection': 'keep-alive', 'x-amzn-requestid': '6a504009-8427-4b10-87a8-4db7525b22e6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to determine document type. The content: "CSX J1 GATE RECEIPT" "Chicago 59th St, IL" "For questions call, 1-877-SHIPCSX" "FFAU 217080" "PAGE 1" "040L 0000W 0000H" "J1 RECEIPT DETAILS (1)" "Activity Associated Load or Seal(s) Booking # Transportation Company Date & Time Equipment Empty & Driver Out-Gate MCCZ 417372 000106 Load --- Desert Eagle Express Inc (DEEE) 07/24/25 14:05 Adi, Abdallah Damage is captured on recorded images at AGS locations".\n\nThis seems to be an outgate or ingate receipt? The header says "J1 GATE RECEIPT" and includes "Out-Gate" column. It says "Out-Gate". So it\'s likely an outgate document. The page shows "Out-Gate" as part of heading. This is a record of vehicle/container exiting a facility. So doc_type is outgate.\n\nCheck keywords: Outgate, Departed, Gate Out, etc. The page has "Out-Gate" term. So classify as outgate.\n\nThus output JSON with documents array containing page_no 1, doc_type "outgate".\n\nWe must use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2734, 'outputTokens': 311, 'totalTokens': 3045}, 'metrics': {'latencyMs': 1414}}
2025-09-24 19:02:25,774 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7f42ccf2_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:25,949 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f484a675_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:25,962 - INFO - 

BJYFRYT0V8O045HGDXJ6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:25,962 - INFO - 

✓ Saved result: output/run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 19:02:26,246 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7f42ccf2_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:26,247 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:02:26,248 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:26,249 - INFO - Splitting PDF d4d5d0cd_YASUN3YXXTIFXRHDIKNF into 1 pages
2025-09-24 19:02:26,251 - INFO - Split PDF into 1 pages
2025-09-24 19:02:26,251 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:26,251 - INFO - Expected pages: [1]
2025-09-24 19:02:26,385 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4d0027af-dd0f-4db7-9df2-1b6f524b2811', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:26 GMT', 'content-type': 'application/json', 'content-length': '1253', 'connection': 'keep-alive', 'x-amzn-requestid': '4d0027af-dd0f-4db7-9df2-1b6f524b2811'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Let\'s analyze the content. It seems like an Equipment Inspection Record, also called Interchange Receipt. The text includes "Interchange Receipt (J1)" and "Equipment Inspection Record". Also "Outgate: LOGPARCHI IL 07/07/2025 2315". This is a gate-out record. The keywords for outgate: "Outgate", "Gate Out", "Release", "Time Out", "Exit Time". So this page likely is an outgate document.\n\nAlso "Equipment Inspection Record" appears, but outgate is a type. The classification categories include ingate and outgate. This page is outgate, as it says Outgate: LOGPARCHI IL 07/07/2025 2315. So doc_type: outgate.\n\nCheck if any other category might fit. "Equipment Inspection Record" could be a log type? But outgate is more specific. So we will classify page 1 as outgate.\n\nReturn JSON with documents array containing one object: page_no: 1, doc_type: "outgate".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2820, 'outputTokens': 248, 'totalTokens': 3068}, 'metrics': {'latencyMs': 1259}}
2025-09-24 19:02:26,386 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/de3cd93d_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:26,402 - INFO - 

HZNQ971O2V9MGG5MU1T3.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:26,402 - INFO - 

✓ Saved result: output/run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 19:02:26,428 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be8b0a14-5127-4d5e-90b7-a323cc41a265', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:26 GMT', 'content-type': 'application/json', 'content-length': '572', 'connection': 'keep-alive', 'x-amzn-requestid': 'be8b0a14-5127-4d5e-90b7-a323cc41a265'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify. The content: "BNSF RAILWAY OUT-GATE BNSF Railway Equipment Inspection Record" indicates Outgate Document. Keywords: Outgate, Release ID, etc. So doc_type = outgate. No continuation, just single page. Output accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2791, 'outputTokens': 80, 'totalTokens': 2871}, 'metrics': {'latencyMs': 607}}
2025-09-24 19:02:26,429 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/74a6d8bd_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:26,577 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ea0ec404-43b5-42fd-9f55-b429de3e67fb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:26 GMT', 'content-type': 'application/json', 'content-length': '1048', 'connection': 'keep-alive', 'x-amzn-requestid': 'ea0ec404-43b5-42fd-9f55-b429de3e67fb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The text: "License Activity Container Chassis Genset Line Cat Weight (MT) Sz/Tp Vessel/Voyage Date Time In Time Out D12713DWA FULL OUT MEDU4189050 OWN MSC Import 23 40DR MSCLUCI/524A 07/16/2025 9:54 AM 10:44 AM". Looks like an outgate or ingate? It lists "FULL OUT" and "Time In" and "Time Out". This looks like an "outgate" record: record of vehicle/container exiting a facility. It has "FULL OUT" maybe indicating full out. Contains Time In and Time Out. So doc_type: outgate.\n\nBut check "ingate" is vehicle/container entering facility. Outgate is exiting. So this is outgate. So page 1: outgate. No continuation. Provide JSON with documents array containing page_no 1 and doc_type outgate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2681, 'outputTokens': 216, 'totalTokens': 2897}, 'metrics': {'latencyMs': 1067}}
2025-09-24 19:02:26,578 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4408cbb9_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:26,695 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/de3cd93d_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:26,708 - INFO - 

EKMN29XJBZVFPLOYTKU7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:26,708 - INFO - 

✓ Saved result: output/run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 19:02:26,948 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '441a80e3-2692-49a4-8467-80f49cbfcd06', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:26 GMT', 'content-type': 'application/json', 'content-length': '850', 'connection': 'keep-alive', 'x-amzn-requestid': '441a80e3-2692-49a4-8467-80f49cbfcd06'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify the document type. The content: "J1 GATE RECEIPT" and "Out-Gate" etc. It looks like a gate receipt, an outgate document. So doc_type should be outgate. Let\'s check the keywords: Outgate, Gate Receipt, Out-Gate, etc. That matches outgate definition.\n\nWe also have "J1 GATE RECEIPT" which is a gate receipt. So classify as outgate.\n\nWe must produce JSON with documents array containing one object: page_no 1, doc_type "outgate".\n\nReturn via tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2738, 'outputTokens': 151, 'totalTokens': 2889}, 'metrics': {'latencyMs': 898}}
2025-09-24 19:02:26,948 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f4a879bf_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:27,001 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/74a6d8bd_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:27,005 - INFO - 

T5U48WX7H3FNSZPBDBLZ.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:27,005 - INFO - 

✓ Saved result: output/run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 19:02:27,308 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4408cbb9_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:27,315 - INFO - Page 2: Extracted 457 characters, 4 lines from 43963e80_OO7629V25ZJ22FHL0PAA_7ff13dcb_page_002.pdf
2025-09-24 19:02:27,316 - INFO - Successfully processed page 2
2025-09-24 19:02:27,327 - INFO - 

GHXKMFASS6CGN362R5BW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:27,327 - INFO - 

✓ Saved result: output/run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 19:02:27,399 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1cc7c723-bdcb-4206-8ce0-2d0aed42007c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:27 GMT', 'content-type': 'application/json', 'content-length': '1784', 'connection': 'keep-alive', 'x-amzn-requestid': '1cc7c723-bdcb-4206-8ce0-2d0aed42007c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The text appears to be some port authority container delivery info. Keywords: "Deliver Import Container", "Container", "Booking", "Vessel", "Seals". This looks like a customs/doc, maybe a customs document, or a shipping manifest. Let\'s see categories: customs_doc: "General customs paperwork (declarations, certificates, permits)." Keywords: Customs, Declaration, Import/Export Declaration, Customs Broker. This page has "Deliver Import Container" indicates import container. Could be a customs document. There\'s no HS code, no declared value. Could be a port authority manifest. Maybe "customs_doc" is appropriate.\n\nOther categories: "ingate" or "outgate"? It says "Truck ID" "Container" maybe it\'s an ingate or outgate. But ingate document: "Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In". We have "Truck ID" and maybe the time "01-Mar-2023 06:39:43". It might be a gate-in record. But the title "Deliver Import Container" suggests a container delivery. Could be a delivery receipt at port. Not exactly ingate: ingate is for vehicle/container entering a facility. This might be a gate-in for container? Not sure.\n\nWe need to choose among "customs_doc", "ingate", "outgate", "other". The best is customs_doc. There\'s no other explicit header. There\'s no "Bill of Lading" etc.\n\nThus classify as customs_doc. Page 1, doc_type: customs_doc.\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2676, 'outputTokens': 350, 'totalTokens': 3026}, 'metrics': {'latencyMs': 1540}}
2025-09-24 19:02:27,399 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2c871dfd_UGM225037492OGWQHUHR.png
2025-09-24 19:02:27,666 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f4a879bf_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:27,673 - INFO - 

UGM225037492OGWQHUHR.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 19:02:27,673 - INFO - 

✓ Saved result: output/run1_UGM225037492OGWQHUHR.json
2025-09-24 19:02:27,700 - INFO - Page 1: Extracted 669 characters, 29 lines from 43963e80_OO7629V25ZJ22FHL0PAA_7ff13dcb_page_001.pdf
2025-09-24 19:02:27,700 - INFO - Successfully processed page 1
2025-09-24 19:02:27,701 - INFO - Combined 2 pages into final text
2025-09-24 19:02:27,701 - INFO - Text validation for 43963e80_OO7629V25ZJ22FHL0PAA: 1162 characters, 2 pages
2025-09-24 19:02:27,701 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:27,701 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:27,966 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2c871dfd_UGM225037492OGWQHUHR.png
2025-09-24 19:02:27,970 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:02:27,970 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:27,971 - INFO - Splitting PDF 43715a69_ZAJMVPYOM22ETECQRW3L into 1 pages
2025-09-24 19:02:27,973 - INFO - Split PDF into 1 pages
2025-09-24 19:02:27,973 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:27,973 - INFO - Expected pages: [1]
2025-09-24 19:02:29,236 - INFO - Page 1: Extracted 840 characters, 41 lines from f0640456_VY0AAD98JWYCZUAHKROB_c57db341_page_001.pdf
2025-09-24 19:02:29,236 - INFO - Successfully processed page 1
2025-09-24 19:02:29,236 - INFO - Combined 1 pages into final text
2025-09-24 19:02:29,236 - INFO - Text validation for f0640456_VY0AAD98JWYCZUAHKROB: 857 characters, 1 pages
2025-09-24 19:02:29,237 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:29,237 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:30,150 - INFO - Page 1: Extracted 486 characters, 22 lines from d4d5d0cd_YASUN3YXXTIFXRHDIKNF_3cdf5ee4_page_001.pdf
2025-09-24 19:02:30,151 - INFO - Successfully processed page 1
2025-09-24 19:02:30,151 - INFO - Combined 1 pages into final text
2025-09-24 19:02:30,152 - INFO - Text validation for d4d5d0cd_YASUN3YXXTIFXRHDIKNF: 503 characters, 1 pages
2025-09-24 19:02:30,152 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:30,152 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:30,382 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:02:30,382 - INFO - Splitting PDF into individual pages...
2025-09-24 19:02:30,384 - INFO - Splitting PDF 5cb7d34d_WRP4157FNOQ52T6661B4 into 1 pages
2025-09-24 19:02:30,386 - INFO - Split PDF into 1 pages
2025-09-24 19:02:30,386 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:02:30,386 - INFO - Expected pages: [1]
2025-09-24 19:02:30,740 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'aa43f512-4d66-4a8e-90d0-3a7c7be33804', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:30 GMT', 'content-type': 'application/json', 'content-length': '2031', 'connection': 'keep-alive', 'x-amzn-requestid': 'aa43f512-4d66-4a8e-90d0-3a7c7be33804'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 contains Outgate/ingate? It\'s an email with attachments. It includes "DEPARTED" and "OUTGATE" style. It says "This terminal is equipped with an automated gate system. DAMAGE IS NOT REPORTED ON INGATE OR OUTGATE RECEIPTS." So page1 seems like an outgate receipt or ingate? The document seems to be an InGate/OutGate record. It has equipment, departure date, carrier, shipper, driver, load weight, seal number. This looks like an outgate or ingate document. The keywords: "DEPARTED", "OUTGATE", "INGATE". The document is likely an outgate or ingate record. Which one? It includes "DEPARTED:" and "CHECKED BY", "RCD DEFECTS". This is likely an Outgate Document. The key field: "OUTGATE" is not explicitly there but it says "DEPARTED". The tags: "INGATE OR OUTGATE RECEIPTS" are mentioned. So classification: outgate. Page2 is email text, not a logistics page. It\'s probably continuation? It\'s not a continuation of previous page. It\'s just email footer. It might not be a logistics doc. According to rule: If page is not within categories, but if it\'s a continuation of previous, assign same type. But it\'s not continuation. It\'s a separate email. Should we classify as other? It\'s not a logistics doc. So page2 as other. But we must consider that page2 is part of the same email. It\'s not a continuance of prior page, but it\'s part of the same email. But classification rule: If a page is not from categories, classify as other. So page2 -> other.\n\nThus output:\n\n{\n"documents": [\n{"page_no":1,"doc_type":"outgate"},\n{"page_no":2,"doc_type":"other"}\n]\n}\n\nUse tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2931, 'outputTokens': 436, 'totalTokens': 3367}, 'metrics': {'latencyMs': 1853}}
2025-09-24 19:02:30,740 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/43963e80_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:30,750 - INFO - 

OO7629V25ZJ22FHL0PAA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 19:02:30,751 - INFO - 

✓ Saved result: output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:02:31,027 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/43963e80_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:31,355 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '742cdf16-4ac4-41d5-93fc-0224b0127b68', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:31 GMT', 'content-type': 'application/json', 'content-length': '1219', 'connection': 'keep-alive', 'x-amzn-requestid': '742cdf16-4ac4-41d5-93fc-0224b0127b68'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 given. We need to examine content. It\'s a document with title "APPROVED" "MAERSK" "Reuse empty container" etc. Looks like a container approval or request to approve reuse of empty container. The keywords: "APPROVED", "Reuse empty container", "Container number", "Export booking ref.", "Import location", "Export location", etc. Not a typical invoice, POD, BOL, etc. It appears to be a container approval/booking confirmation. Not matching any provided categories except maybe "other". We should check if any category matches. There\'s no "invoice", "bol", "pod", "rate_confirmation", etc. None. So classify as "other". The instructions: if page is continuation of previous page, assign same type. But it\'s just one page. So output:\n\n{\n "documents": [\n   {"page_no": 1, "doc_type": "other"}\n ]\n}\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2858, 'outputTokens': 220, 'totalTokens': 3078}, 'metrics': {'latencyMs': 1049}}
2025-09-24 19:02:31,355 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f0640456_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:31,375 - INFO - 

VY0AAD98JWYCZUAHKROB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 19:02:31,375 - INFO - 

✓ Saved result: output/run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 19:02:31,658 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f0640456_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:33,146 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '92d2ad95-1dd1-4056-802c-dd733d05619d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:32 GMT', 'content-type': 'application/json', 'content-length': '1722', 'connection': 'keep-alive', 'x-amzn-requestid': '92d2ad95-1dd1-4056-802c-dd733d05619d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to determine doc type.\n\nContent: Looks like a pickup card or terminal card. Keywords: "PNCT", "PICKUP". No obvious invoice, BOL, POD, etc. Not a packing list. Could be a load card or gate card. Could be ingate document? It shows pickup info, container, etc. Might be an ingate or outgate? It says "Welcome to PNCT" terminal. "Save this card to exit the terminal." So it\'s a card for exit. Might be an ingate or outgate? The content is about pickup and container. Could be a "ingate" document: record of vehicle entering. But the card says to exit the terminal. Could be an outgoing load card. The doc type list: ingate, outgate. The card is for exit. Likely "outgate" because it\'s for exiting terminal. But it says pickup. Or maybe it\'s a "ingate" because it shows pickup and container. But outgate typically has departure time, exit. The page says "WELCOME TO PNCT" "SAVE THIS CARD TO EXIT THE TERMINAL." So use to exit, maybe it\'s a card to be used as an exit. This seems like an "ingate" or "outgate" card. Not sure. The label is "PNCT" maybe "Port Terminal Card Terminal"? Might be a "ingate" or "outgate" for container terminal. The doc type list: ingate and outgate.\n\nWe should pick one. The card is for exit. That is "outgate". So doc_type = outgate.\n\nReturn JSON with page_no 1, doc_type outgate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2780, 'outputTokens': 371, 'totalTokens': 3151}, 'metrics': {'latencyMs': 1797}}
2025-09-24 19:02:33,146 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d4d5d0cd_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:33,161 - INFO - 

YASUN3YXXTIFXRHDIKNF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:33,161 - INFO - 

✓ Saved result: output/run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 19:02:33,446 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d4d5d0cd_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:34,497 - INFO - Page 1: Extracted 1557 characters, 33 lines from 43715a69_ZAJMVPYOM22ETECQRW3L_11b945cc_page_001.pdf
2025-09-24 19:02:34,497 - INFO - Successfully processed page 1
2025-09-24 19:02:34,497 - INFO - Combined 1 pages into final text
2025-09-24 19:02:34,497 - INFO - Text validation for 43715a69_ZAJMVPYOM22ETECQRW3L: 1574 characters, 1 pages
2025-09-24 19:02:34,498 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:34,498 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:37,602 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd22416f4-409b-46c9-80b8-04cfa811505a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:37 GMT', 'content-type': 'application/json', 'content-length': '1173', 'connection': 'keep-alive', 'x-amzn-requestid': 'd22416f4-409b-46c9-80b8-04cfa811505a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The content: "Intermodal Gate Information" etc. This looks like an ingate or outgate document. It has Equipment ID, Terminal, Date and Time, Activity: Departure, L/E Status: Load, etc. This is likely an ingate or outgate document. The header says "Intermodal Gate Information". It\'s about equipment departure, so might be outgate? Activity: Departure (so the vehicle is leaving). Could be outgate. But ingate would be arrival. The page says "Intermodal Gate Information - HAMU 249602 06/24/2025 11:47" and "Activity: Departure". So it\'s an outgate record. So doc_type: outgate.\n\nWe need to return JSON with documents array containing one object: page_no: 1, doc_type: "outgate".\n\nCheck if any other type fits: ingate vs outgate. "Activity: Departure" indicates departure from facility, so outgate. So output.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2979, 'outputTokens': 225, 'totalTokens': 3204}, 'metrics': {'latencyMs': 1248}}
2025-09-24 19:02:37,603 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/43715a69_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:37,634 - INFO - 

ZAJMVPYOM22ETECQRW3L.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:37,634 - INFO - 

✓ Saved result: output/run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 19:02:37,960 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/43715a69_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:39,401 - INFO - Page 1: Extracted 1574 characters, 82 lines from 5cb7d34d_WRP4157FNOQ52T6661B4_000b085a_page_001.pdf
2025-09-24 19:02:39,402 - INFO - Successfully processed page 1
2025-09-24 19:02:39,402 - INFO - Combined 1 pages into final text
2025-09-24 19:02:39,402 - INFO - Text validation for 5cb7d34d_WRP4157FNOQ52T6661B4: 1591 characters, 1 pages
2025-09-24 19:02:39,402 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:02:39,402 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:02:41,679 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '396cedb7-047c-4912-9de4-6101a93f1277', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:32:41 GMT', 'content-type': 'application/json', 'content-length': '949', 'connection': 'keep-alive', 'x-amzn-requestid': '396cedb7-047c-4912-9de4-6101a93f1277'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It looks like an Outgate/Ingate document describing truck entry and exit. Keywords: "GATE OUT", "Truck In", "Truck Out", "EIR Number", "Container", "Type", etc. This matches the \'outgate\' or \'ingate\' docs. The format: "GATE OUT" indicates outgate. There\'s mention of \'Truck In\' and \'Truck Out\', but the header says GATE OUT. Could be an outgate document. The doc type \'outgate\' matches. There\'s no \'ingate\' header. So classify as \'outgate\'.\n\nWe must return JSON with documents array having page_no: 1, doc_type: "outgate".\n\nWe must use the tool classify_logistics_doc_type.\n\nLet\'s do it.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3159, 'outputTokens': 180, 'totalTokens': 3339}, 'metrics': {'latencyMs': 1080}}
2025-09-24 19:02:41,679 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5cb7d34d_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:41,710 - INFO - 

WRP4157FNOQ52T6661B4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:02:41,710 - INFO - 

✓ Saved result: output/run1_WRP4157FNOQ52T6661B4.json
2025-09-24 19:02:42,041 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5cb7d34d_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:42,043 - INFO - 
📊 Processing Summary:
2025-09-24 19:02:42,043 - INFO -    Total files: 12
2025-09-24 19:02:42,043 - INFO -    Successful: 12
2025-09-24 19:02:42,043 - INFO -    Failed: 0
2025-09-24 19:02:42,043 - INFO -    Duration: 29.86 seconds
2025-09-24 19:02:42,043 - INFO -    Output directory: output
2025-09-24 19:02:42,043 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:02:42,043 - INFO -    📄 BJYFRYT0V8O045HGDXJ6.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 E88AUSUP8065SEI9GD5Q.jpg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 EKMN29XJBZVFPLOYTKU7.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 GHXKMFASS6CGN362R5BW.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 HZNQ971O2V9MGG5MU1T3.jpeg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 OO7629V25ZJ22FHL0PAA.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 19:02:42,044 - INFO -    📄 T5U48WX7H3FNSZPBDBLZ.png: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 UGM225037492OGWQHUHR.png: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 19:02:42,044 - INFO -    📄 VY0AAD98JWYCZUAHKROB.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 19:02:42,044 - INFO -    📄 WRP4157FNOQ52T6661B4.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 YASUN3YXXTIFXRHDIKNF.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,044 - INFO -    📄 ZAJMVPYOM22ETECQRW3L.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:02:42,045 - INFO - 
============================================================================================================================================
2025-09-24 19:02:42,045 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:02:42,045 - INFO - ============================================================================================================================================
2025-09-24 19:02:42,045 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:02:42,045 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:02:42,045 - INFO - BJYFRYT0V8O045HGDXJ6.pdf                           1      outgate                                  run1_BJYFRYT0V8O045HGDXJ6.json                                                  
2025-09-24 19:02:42,045 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:02:42,045 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 19:02:42,045 - INFO - 
2025-09-24 19:02:42,045 - INFO - E88AUSUP8065SEI9GD5Q.jpg                           1      outgate                                  run1_E88AUSUP8065SEI9GD5Q.json                                                  
2025-09-24 19:02:42,046 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:02:42,046 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 19:02:42,046 - INFO - 
2025-09-24 19:02:42,046 - INFO - EKMN29XJBZVFPLOYTKU7.pdf                           1      outgate                                  run1_EKMN29XJBZVFPLOYTKU7.json                                                  
2025-09-24 19:02:42,046 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:02:42,046 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 19:02:42,046 - INFO - 
2025-09-24 19:02:42,046 - INFO - GHXKMFASS6CGN362R5BW.pdf                           1      outgate                                  run1_GHXKMFASS6CGN362R5BW.json                                                  
2025-09-24 19:02:42,046 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:02:42,046 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 19:02:42,046 - INFO - 
2025-09-24 19:02:42,046 - INFO - HZNQ971O2V9MGG5MU1T3.jpeg                          1      outgate                                  run1_HZNQ971O2V9MGG5MU1T3.json                                                  
2025-09-24 19:02:42,046 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:02:42,046 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 19:02:42,046 - INFO - 
2025-09-24 19:02:42,046 - INFO - OO7629V25ZJ22FHL0PAA.pdf                           1      outgate                                  run1_OO7629V25ZJ22FHL0PAA.json                                                  
2025-09-24 19:02:42,046 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:42,046 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:02:42,047 - INFO - 
2025-09-24 19:02:42,047 - INFO - OO7629V25ZJ22FHL0PAA.pdf                           2      other                                    run1_OO7629V25ZJ22FHL0PAA.json                                                  
2025-09-24 19:02:42,047 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:02:42,047 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:02:42,047 - INFO - 
2025-09-24 19:02:42,047 - INFO - T5U48WX7H3FNSZPBDBLZ.png                           1      outgate                                  run1_T5U48WX7H3FNSZPBDBLZ.json                                                  
2025-09-24 19:02:42,047 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:02:42,047 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 19:02:42,047 - INFO - 
2025-09-24 19:02:42,047 - INFO - UGM225037492OGWQHUHR.png                           1      customs_doc                              run1_UGM225037492OGWQHUHR.json                                                  
2025-09-24 19:02:42,047 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png
2025-09-24 19:02:42,047 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UGM225037492OGWQHUHR.json
2025-09-24 19:02:42,047 - INFO - 
2025-09-24 19:02:42,047 - INFO - VY0AAD98JWYCZUAHKROB.pdf                           1      other                                    run1_VY0AAD98JWYCZUAHKROB.json                                                  
2025-09-24 19:02:42,047 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:02:42,047 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 19:02:42,047 - INFO - 
2025-09-24 19:02:42,047 - INFO - WRP4157FNOQ52T6661B4.pdf                           1      outgate                                  run1_WRP4157FNOQ52T6661B4.json                                                  
2025-09-24 19:02:42,047 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:02:42,047 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WRP4157FNOQ52T6661B4.json
2025-09-24 19:02:42,048 - INFO - 
2025-09-24 19:02:42,048 - INFO - YASUN3YXXTIFXRHDIKNF.pdf                           1      outgate                                  run1_YASUN3YXXTIFXRHDIKNF.json                                                  
2025-09-24 19:02:42,048 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:02:42,048 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 19:02:42,048 - INFO - 
2025-09-24 19:02:42,048 - INFO - ZAJMVPYOM22ETECQRW3L.pdf                           1      outgate                                  run1_ZAJMVPYOM22ETECQRW3L.json                                                  
2025-09-24 19:02:42,048 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:02:42,048 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 19:02:42,048 - INFO - 
2025-09-24 19:02:42,048 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:02:42,048 - INFO - Total entries: 13
2025-09-24 19:02:42,048 - INFO - ============================================================================================================================================
2025-09-24 19:02:42,048 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:02:42,048 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:02:42,048 - INFO -   1. BJYFRYT0V8O045HGDXJ6.pdf            Page 1   → outgate         | run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 19:02:42,048 - INFO -   2. E88AUSUP8065SEI9GD5Q.jpg            Page 1   → outgate         | run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 19:02:42,048 - INFO -   3. EKMN29XJBZVFPLOYTKU7.pdf            Page 1   → outgate         | run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 19:02:42,049 - INFO -   4. GHXKMFASS6CGN362R5BW.pdf            Page 1   → outgate         | run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 19:02:42,049 - INFO -   5. HZNQ971O2V9MGG5MU1T3.jpeg           Page 1   → outgate         | run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 19:02:42,049 - INFO -   6. OO7629V25ZJ22FHL0PAA.pdf            Page 1   → outgate         | run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:02:42,049 - INFO -   7. OO7629V25ZJ22FHL0PAA.pdf            Page 2   → other           | run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:02:42,049 - INFO -   8. T5U48WX7H3FNSZPBDBLZ.png            Page 1   → outgate         | run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 19:02:42,049 - INFO -   9. UGM225037492OGWQHUHR.png            Page 1   → customs_doc     | run1_UGM225037492OGWQHUHR.json
2025-09-24 19:02:42,049 - INFO -  10. VY0AAD98JWYCZUAHKROB.pdf            Page 1   → other           | run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 19:02:42,049 - INFO -  11. WRP4157FNOQ52T6661B4.pdf            Page 1   → outgate         | run1_WRP4157FNOQ52T6661B4.json
2025-09-24 19:02:42,049 - INFO -  12. YASUN3YXXTIFXRHDIKNF.pdf            Page 1   → outgate         | run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 19:02:42,049 - INFO -  13. ZAJMVPYOM22ETECQRW3L.pdf            Page 1   → outgate         | run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 19:02:42,049 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:02:42,049 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 29.861824, 'processed_files': [{'filename': 'BJYFRYT0V8O045HGDXJ6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf'}, {'filename': 'E88AUSUP8065SEI9GD5Q.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg'}, {'filename': 'EKMN29XJBZVFPLOYTKU7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf'}, {'filename': 'GHXKMFASS6CGN362R5BW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf'}, {'filename': 'HZNQ971O2V9MGG5MU1T3.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg'}, {'filename': 'OO7629V25ZJ22FHL0PAA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf'}, {'filename': 'T5U48WX7H3FNSZPBDBLZ.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png'}, {'filename': 'UGM225037492OGWQHUHR.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png'}, {'filename': 'VY0AAD98JWYCZUAHKROB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf'}, {'filename': 'WRP4157FNOQ52T6661B4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf'}, {'filename': 'YASUN3YXXTIFXRHDIKNF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf'}, {'filename': 'ZAJMVPYOM22ETECQRW3L.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf'}]}
