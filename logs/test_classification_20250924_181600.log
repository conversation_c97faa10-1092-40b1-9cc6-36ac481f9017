2025-09-24 18:16:00,813 - INFO - Logging initialized. Log file: logs/test_classification_20250924_181600.log
2025-09-24 18:16:00,814 - INFO - 📁 Found 13 files to process
2025-09-24 18:16:00,814 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:16:00,814 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 18:16:00,814 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 18:16:00,814 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 18:16:00,814 - INFO - ⬆️ [18:16:00] Uploading: A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:03,845 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/A8UNJQR3VGLF8G4MC11J.pdf -> s3://document-extraction-logistically/temp/6d58c97b_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:03,846 - INFO - 🔍 [18:16:03] Starting classification: A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:03,847 - INFO - ⬆️ [18:16:03] Uploading: AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:03,848 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:03,878 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:03,889 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6d58c97b_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:03,890 - INFO - Processing PDF from S3...
2025-09-24 18:16:03,890 - INFO - Downloading PDF from S3 to /tmp/tmpsvkptztx/6d58c97b_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:04,502 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/AK2S46MQ9UJW5Q43TRRV.pdf -> s3://document-extraction-logistically/temp/4cdc58a8_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:04,502 - INFO - 🔍 [18:16:04] Starting classification: AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:04,503 - INFO - ⬆️ [18:16:04] Uploading: EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:04,504 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:04,524 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:04,530 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4cdc58a8_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:04,530 - INFO - Processing PDF from S3...
2025-09-24 18:16:04,531 - INFO - Downloading PDF from S3 to /tmp/tmp3l7uk0vi/4cdc58a8_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:05,108 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/EAMCIVO8E5KY4U6ZYWK2.jpg -> s3://document-extraction-logistically/temp/df25ba6c_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:05,108 - INFO - 🔍 [18:16:05] Starting classification: EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:05,108 - INFO - ⬆️ [18:16:05] Uploading: F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:05,109 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:05,118 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:05,120 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/df25ba6c_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:05,120 - INFO - Processing image from S3...
2025-09-24 18:16:05,733 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/F4RZ1UDKLCELJG8T6R9G.png -> s3://document-extraction-logistically/temp/dafccb79_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:05,733 - INFO - 🔍 [18:16:05] Starting classification: F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:05,734 - INFO - ⬆️ [18:16:05] Uploading: KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:05,734 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:05,760 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:05,764 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dafccb79_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:05,764 - INFO - Processing image from S3...
2025-09-24 18:16:06,348 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/KBFTEJIYFMKC7ZILXM5M.png -> s3://document-extraction-logistically/temp/9d87cf31_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:06,349 - INFO - 🔍 [18:16:06] Starting classification: KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:06,350 - INFO - ⬆️ [18:16:06] Uploading: LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:06,350 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:06,372 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:06,377 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9d87cf31_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:06,377 - INFO - Processing image from S3...
2025-09-24 18:16:06,459 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 18:16:06,460 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:06,461 - INFO - Splitting PDF 6d58c97b_A8UNJQR3VGLF8G4MC11J into 1 pages
2025-09-24 18:16:06,463 - INFO - Split PDF into 1 pages
2025-09-24 18:16:06,463 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:06,464 - INFO - Expected pages: [1]
2025-09-24 18:16:06,989 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:16:06,990 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:06,992 - INFO - Splitting PDF 4cdc58a8_AK2S46MQ9UJW5Q43TRRV into 1 pages
2025-09-24 18:16:07,028 - INFO - Split PDF into 1 pages
2025-09-24 18:16:07,028 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:07,028 - INFO - Expected pages: [1]
2025-09-24 18:16:07,255 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf -> s3://document-extraction-logistically/temp/553b1c3a_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:07,256 - INFO - 🔍 [18:16:07] Starting classification: LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:07,257 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:07,257 - INFO - ⬆️ [18:16:07] Uploading: MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:07,268 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:07,272 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/553b1c3a_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:07,275 - INFO - Processing PDF from S3...
2025-09-24 18:16:07,275 - INFO - Downloading PDF from S3 to /tmp/tmp60ovixa1/553b1c3a_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:07,867 - INFO - S3 Image temp/df25ba6c_EAMCIVO8E5KY4U6ZYWK2.jpg: Extracted 386 characters, 34 lines
2025-09-24 18:16:07,867 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:07,867 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:08,155 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/MZ3X0YRE37Y7NHS45V2U.pdf -> s3://document-extraction-logistically/temp/e0c1ca96_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:08,155 - INFO - 🔍 [18:16:08] Starting classification: MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:08,158 - INFO - ⬆️ [18:16:08] Uploading: NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:08,160 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:08,182 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:08,197 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e0c1ca96_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:08,198 - INFO - Processing PDF from S3...
2025-09-24 18:16:08,198 - INFO - Downloading PDF from S3 to /tmp/tmpju1hdb_w/e0c1ca96_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:08,779 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/NLUB3HCM1QWW6FXJBEY3.pdf -> s3://document-extraction-logistically/temp/25c649ea_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:08,780 - INFO - 🔍 [18:16:08] Starting classification: NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:08,780 - INFO - ⬆️ [18:16:08] Uploading: OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:08,782 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:08,800 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:08,805 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/25c649ea_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:08,805 - INFO - Processing PDF from S3...
2025-09-24 18:16:08,806 - INFO - Downloading PDF from S3 to /tmp/tmpve7ixh8c/25c649ea_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:09,021 - INFO - S3 Image temp/dafccb79_F4RZ1UDKLCELJG8T6R9G.png: Extracted 648 characters, 28 lines
2025-09-24 18:16:09,021 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:09,021 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:09,082 - INFO - S3 Image temp/9d87cf31_KBFTEJIYFMKC7ZILXM5M.png: Extracted 661 characters, 29 lines
2025-09-24 18:16:09,083 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:09,083 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:09,370 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/OPDBRSVH28PEQZSD5FO9.pdf -> s3://document-extraction-logistically/temp/5a933e29_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:09,371 - INFO - 🔍 [18:16:09] Starting classification: OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:09,372 - INFO - ⬆️ [18:16:09] Uploading: Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:09,374 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:09,392 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:09,393 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5a933e29_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:09,394 - INFO - Processing PDF from S3...
2025-09-24 18:16:09,394 - INFO - Downloading PDF from S3 to /tmp/tmpsxxes0_p/5a933e29_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:09,746 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '70edee27-1650-401e-8dc7-947e4cb9327f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:09 GMT', 'content-type': 'application/json', 'content-length': '639', 'connection': 'keep-alive', 'x-amzn-requestid': '70edee27-1650-401e-8dc7-947e4cb9327f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content indicates a "Trip Sheet" with truck weight info. Keywords: Trip Ticket, Gross, Tare, Net, Date In, Date Out. Likely a Scale Ticket. It contains weight data. So doc_type: scale_ticket. No continuation marker. So output array with one object: page_no 1, doc_type "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2694, 'outputTokens': 96, 'totalTokens': 2790}, 'metrics': {'latencyMs': 688}}
2025-09-24 18:16:09,746 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/df25ba6c_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:09,963 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Y2EQEYPYUUYHAD9BZKXO.jpg -> s3://document-extraction-logistically/temp/7b0eafde_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:09,963 - INFO - 🔍 [18:16:09] Starting classification: Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:09,964 - INFO - ⬆️ [18:16:09] Uploading: Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:09,964 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:09,981 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:09,991 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7b0eafde_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:09,991 - INFO - Processing image from S3...
2025-09-24 18:16:10,546 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Z1N2YIEHAA2FERWDQ38E.pdf -> s3://document-extraction-logistically/temp/7e1e036e_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:10,547 - INFO - 🔍 [18:16:10] Starting classification: Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:10,547 - INFO - ⬆️ [18:16:10] Uploading: ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:10,549 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:10,567 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:10,571 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7e1e036e_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:10,571 - INFO - Processing PDF from S3...
2025-09-24 18:16:10,571 - INFO - Downloading PDF from S3 to /tmp/tmpybpv5wm5/7e1e036e_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:11,160 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZA6FSHKD588WC7VTH3MO.pdf -> s3://document-extraction-logistically/temp/c33f3d8f_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:11,160 - INFO - 🔍 [18:16:11] Starting classification: ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:11,161 - INFO - ⬆️ [18:16:11] Uploading: ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:11,161 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:11,182 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:11,185 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c33f3d8f_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:11,185 - INFO - Processing PDF from S3...
2025-09-24 18:16:11,186 - INFO - Downloading PDF from S3 to /tmp/tmpyrect_sy/c33f3d8f_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:11,424 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ee7afefd-52a7-47a7-9fcf-8c3dcc578cc5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:11 GMT', 'content-type': 'application/json', 'content-length': '1011', 'connection': 'keep-alive', 'x-amzn-requestid': 'ee7afefd-52a7-47a7-9fcf-8c3dcc578cc5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. Let\'s examine text.\n\nPage shows "Ticket 1396602", "Scale House", "Ticket", date/time, customer, truck, trailer, destination, P.O., D.O, seal, comment. Then "Grown 75200 L Out Scale 3", "Tare: 32100 LIn Scale 3", "Net 431001." Total 21.55. Materials & Services. Origin, Material, Quantity: 21.55 Ton. Weighmaster TOM. This looks like a Scale Ticket: weight record from a scale for vehicle/load. Keywords: Gross, Tare, Net, Weighed At, Ticket no, weight. Yes. So doc_type: scale_ticket.\n\nWe must output via tool call classify_logistics_doc_type with documents array: one object: page_no 1, doc_type "scale_ticket".\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2764, 'outputTokens': 204, 'totalTokens': 2968}, 'metrics': {'latencyMs': 1138}}
2025-09-24 18:16:11,424 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9d87cf31_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:11,653 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7e70e876-b94d-4983-b726-50db353bf9e1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:10 GMT', 'content-type': 'application/json', 'content-length': '744', 'connection': 'keep-alive', 'x-amzn-requestid': '7e70e876-b94d-4983-b726-50db353bf9e1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. It\'s a scale ticket. It contains scale info: Ticket number, Date, Time, Truck, Trailer, Destination, O, D, Seal, Gross, Tare, Net, Tons, etc. So doc_type: scale_ticket. It\'s not a continuation, just first page. So output a list with one object: page_no 1, doc_type "scale_ticket". Use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_qkcDBiflR7e3mrr3fN5Y0g', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2766, 'outputTokens': 106, 'totalTokens': 2872}, 'metrics': {'latencyMs': 760}}
2025-09-24 18:16:11,654 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dafccb79_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:11,742 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZY3XO73EAB4BC4957F0V.pdf -> s3://document-extraction-logistically/temp/36ade61b_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:11,742 - INFO - 🔍 [18:16:11] Starting classification: ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:11,744 - INFO - Initializing TextractProcessor...
2025-09-24 18:16:11,759 - INFO - Initializing BedrockProcessor...
2025-09-24 18:16:11,765 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36ade61b_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:11,767 - INFO - Processing PDF from S3...
2025-09-24 18:16:11,768 - INFO - Downloading PDF from S3 to /tmp/tmp966hxlap/36ade61b_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:11,783 - INFO - 

EAMCIVO8E5KY4U6ZYWK2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:11,783 - INFO - 

✓ Saved result: output/run1_EAMCIVO8E5KY4U6ZYWK2.json
2025-09-24 18:16:12,071 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/df25ba6c_EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:12,076 - INFO - 

KBFTEJIYFMKC7ZILXM5M.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:12,077 - INFO - 

✓ Saved result: output/run1_KBFTEJIYFMKC7ZILXM5M.json
2025-09-24 18:16:12,212 - INFO - Page 1: Extracted 279 characters, 22 lines from 4cdc58a8_AK2S46MQ9UJW5Q43TRRV_951c631e_page_001.pdf
2025-09-24 18:16:12,212 - INFO - Successfully processed page 1
2025-09-24 18:16:12,213 - INFO - Combined 1 pages into final text
2025-09-24 18:16:12,214 - INFO - Text validation for 4cdc58a8_AK2S46MQ9UJW5Q43TRRV: 296 characters, 1 pages
2025-09-24 18:16:12,214 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:12,214 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:12,372 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9d87cf31_KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:12,388 - INFO - 

F4RZ1UDKLCELJG8T6R9G.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:12,388 - INFO - 

✓ Saved result: output/run1_F4RZ1UDKLCELJG8T6R9G.json
2025-09-24 18:16:12,553 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:16:12,553 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:12,553 - INFO - Splitting PDF 5a933e29_OPDBRSVH28PEQZSD5FO9 into 1 pages
2025-09-24 18:16:12,554 - INFO - Split PDF into 1 pages
2025-09-24 18:16:12,554 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:12,554 - INFO - Expected pages: [1]
2025-09-24 18:16:12,571 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:16:12,571 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:12,572 - INFO - Splitting PDF 7e1e036e_Z1N2YIEHAA2FERWDQ38E into 1 pages
2025-09-24 18:16:12,572 - INFO - Split PDF into 1 pages
2025-09-24 18:16:12,572 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:12,572 - INFO - Expected pages: [1]
2025-09-24 18:16:12,677 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dafccb79_F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:12,784 - INFO - Page 1: Extracted 373 characters, 33 lines from 6d58c97b_A8UNJQR3VGLF8G4MC11J_f9dd6e62_page_001.pdf
2025-09-24 18:16:12,785 - INFO - Successfully processed page 1
2025-09-24 18:16:12,785 - INFO - Combined 1 pages into final text
2025-09-24 18:16:12,785 - INFO - Text validation for 6d58c97b_A8UNJQR3VGLF8G4MC11J: 390 characters, 1 pages
2025-09-24 18:16:12,785 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:12,785 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:14,080 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 18:16:14,081 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:14,084 - INFO - Splitting PDF 36ade61b_ZY3XO73EAB4BC4957F0V into 1 pages
2025-09-24 18:16:14,089 - INFO - Split PDF into 1 pages
2025-09-24 18:16:14,089 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:14,089 - INFO - Expected pages: [1]
2025-09-24 18:16:14,100 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '30c4817a-9e68-49d5-9f51-52c1c5ed8b36', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:13 GMT', 'content-type': 'application/json', 'content-length': '691', 'connection': 'keep-alive', 'x-amzn-requestid': '30c4817a-9e68-49d5-9f51-52c1c5ed8b36'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have single page. Text indicates "WEIGHED ON A FAIRBANKS SCALE" and contains weight-related keywords: WEIGHED, SCALE, etc. Also contains "Gross", "Net" maybe not present but weight context. The page is likely a Scale Ticket. So doc_type "scale_ticket". Page 1. No continuation indicator. So output array with one object: page_no 1, doc_type: "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2644, 'outputTokens': 107, 'totalTokens': 2751}, 'metrics': {'latencyMs': 738}}
2025-09-24 18:16:14,100 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4cdc58a8_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:14,114 - INFO - 

AK2S46MQ9UJW5Q43TRRV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:14,115 - INFO - 

✓ Saved result: output/run1_AK2S46MQ9UJW5Q43TRRV.json
2025-09-24 18:16:14,405 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4cdc58a8_AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:14,634 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a8943259-35dd-408e-a563-b529d3f862a7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:14 GMT', 'content-type': 'application/json', 'content-length': '636', 'connection': 'keep-alive', 'x-amzn-requestid': 'a8943259-35dd-408e-a563-b529d3f862a7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. The page content: "ELECTRONIC SCALE TICKET" and weight information: GROSS, TARE, NET, dates, truck, trailer, seal. So that matches scale_ticket definition. We should output object with page_no 1 and doc_type "scale_ticket". Use tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2698, 'outputTokens': 94, 'totalTokens': 2792}, 'metrics': {'latencyMs': 640}}
2025-09-24 18:16:14,634 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6d58c97b_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:14,646 - INFO - 

A8UNJQR3VGLF8G4MC11J.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:14,646 - INFO - 

✓ Saved result: output/run1_A8UNJQR3VGLF8G4MC11J.json
2025-09-24 18:16:14,879 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:16:14,879 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:14,882 - INFO - Splitting PDF c33f3d8f_ZA6FSHKD588WC7VTH3MO into 1 pages
2025-09-24 18:16:14,884 - INFO - Split PDF into 1 pages
2025-09-24 18:16:14,884 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:14,884 - INFO - Expected pages: [1]
2025-09-24 18:16:14,933 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6d58c97b_A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:15,316 - INFO - S3 Image temp/7b0eafde_Y2EQEYPYUUYHAD9BZKXO.jpg: Extracted 1944 characters, 95 lines
2025-09-24 18:16:15,316 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:15,316 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:15,413 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:16:15,413 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:15,415 - INFO - Splitting PDF 25c649ea_NLUB3HCM1QWW6FXJBEY3 into 1 pages
2025-09-24 18:16:15,416 - INFO - Split PDF into 1 pages
2025-09-24 18:16:15,417 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:15,417 - INFO - Expected pages: [1]
2025-09-24 18:16:16,280 - INFO - Downloaded PDF size: 1.7 MB
2025-09-24 18:16:16,280 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:16,282 - INFO - Splitting PDF 553b1c3a_LVWIMLJ8Q7EH6QLW7RTX into 2 pages
2025-09-24 18:16:16,289 - INFO - Split PDF into 2 pages
2025-09-24 18:16:16,289 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:16,289 - INFO - Expected pages: [1, 2]
2025-09-24 18:16:16,490 - INFO - Page 1: Extracted 295 characters, 22 lines from 7e1e036e_Z1N2YIEHAA2FERWDQ38E_ea142b3e_page_001.pdf
2025-09-24 18:16:16,490 - INFO - Successfully processed page 1
2025-09-24 18:16:16,490 - INFO - Combined 1 pages into final text
2025-09-24 18:16:16,491 - INFO - Text validation for 7e1e036e_Z1N2YIEHAA2FERWDQ38E: 312 characters, 1 pages
2025-09-24 18:16:16,491 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:16,491 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:16,539 - INFO - Downloaded PDF size: 2.0 MB
2025-09-24 18:16:16,540 - INFO - Splitting PDF into individual pages...
2025-09-24 18:16:16,541 - INFO - Splitting PDF e0c1ca96_MZ3X0YRE37Y7NHS45V2U into 1 pages
2025-09-24 18:16:16,547 - INFO - Split PDF into 1 pages
2025-09-24 18:16:16,547 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:16:16,547 - INFO - Expected pages: [1]
2025-09-24 18:16:17,799 - INFO - Page 1: Extracted 1059 characters, 66 lines from 5a933e29_OPDBRSVH28PEQZSD5FO9_bc5d1714_page_001.pdf
2025-09-24 18:16:17,799 - INFO - Successfully processed page 1
2025-09-24 18:16:17,800 - INFO - Combined 1 pages into final text
2025-09-24 18:16:17,800 - INFO - Text validation for 5a933e29_OPDBRSVH28PEQZSD5FO9: 1076 characters, 1 pages
2025-09-24 18:16:17,800 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:17,801 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:18,354 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6faeac45-bb08-4e44-b782-1e29cac14289', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:18 GMT', 'content-type': 'application/json', 'content-length': '625', 'connection': 'keep-alive', 'x-amzn-requestid': '6faeac45-bb08-4e44-b782-1e29cac14289'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with weight information. It\'s a scale ticket. Keywords: WEIGHED ON A FAIRBANKS SCALE, GROSS, TARE, NET, WEIGHER. So doc_type should be scale_ticket. We need to output via tool classify_logistics_doc_type. Provide documents array with one object: page_no:1, doc_type:"scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2657, 'outputTokens': 94, 'totalTokens': 2751}, 'metrics': {'latencyMs': 673}}
2025-09-24 18:16:18,354 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7e1e036e_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:18,373 - INFO - 

Z1N2YIEHAA2FERWDQ38E.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:18,373 - INFO - 

✓ Saved result: output/run1_Z1N2YIEHAA2FERWDQ38E.json
2025-09-24 18:16:18,668 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7e1e036e_Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:19,824 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c40b59b5-ec4e-45f4-98e0-db53d85f03a0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:19 GMT', 'content-type': 'application/json', 'content-length': '3491', 'connection': 'keep-alive', 'x-amzn-requestid': 'c40b59b5-ec4e-45f4-98e0-db53d85f03a0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify document type. Let\'s examine content. This looks like a "Solid Waste Manifest" or "Ingate" / "Outgate" type. It includes "DATE TIME IN / OUT", "Time In", "Time Out", "Truck", "Trailer", "Seal", "P.O.", "D.O.", "SHIPPER", "Destination", "Gross", "Tare", "Net", "Materials & Services", "BILLED WEIGHT", "Origin", "Material", "Quantity", "Signature", "Received". This seems like a gate entry/exit manifest for waste. The header "S WASTE SOLID WASTE MANIFEST" is there. It includes a vehicle in and out times. That suggests an "ingate" or "outgate" document. Which one? It says "DATE TIME IN / OUT". It has "Time In" 11:17, "Time Out" 12:11. Also "Gross 73340 L Out Scale 3" and "Tare 32660" and "Net 40680". It looks like it records the weigh-in and weigh-out. There\'s also "By signing above, I declare that I did NOT deposit any PROHIBITED WASTES" etc. It includes a signature of the driver and "CONSIGNEE SIGN HERE" maybe. It says "By signing above, I declare that I did NOT deposit any PROHIBITED WASTES". That might be a "scale ticket" or "weight and inspection certificate"? But it also has IN and OUT scale data. It looks more like a "Solid Waste Manifest" which is a form for waste disposal, maybe a "scale ticket" because it contains weight info. According to the definitions, "Scale Ticket" is weight record from a scale; keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, date, time, ticket no. This page definitely has Gross, Tare, Net, Truck, Driver, weight, date, time. So it likely is a Scale Ticket. However, it also has a manifest header. But the question is to classify into one of the enumerated types: we have "scale_ticket", "ingate", "outgate", "log", etc. Could also be "ingate" because it\'s a gate entry. But ingate def: record of vehicle entering a facility, includes truck number, time-in stamp, inspection notes, seal #. This page has truck #, time in, time out, seal, driver signature. Ingate vs outgate: ingate would have time in, outgate would have time out. This page has both. So it\'s a combined entry/exit. "Combined Carrier Documents" could be used if multiple document headers present. But we see only one header "S WASTE SOLID WASTE MANIFEST". That suggests a single document type. It\'s a "Solid Waste Manifest", which is basically a gate record. In logistic terms, that could be "ingate" or "outgate". But since it contains both in and out times, maybe it\'s a "gate log" or "log". But the best match is "scale_ticket" because it has weight info. But the "scale ticket" is for weight record for billing/compliance, typically a smaller form with weight details. This one is a manifest with weight and also other details. But likely they categorize as "scale_ticket". The definitions: "Scale Ticket" includes weight record from a scale for vehicle/load. Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, date, time, ticket no. This page matches those. So classify as "scale_ticket".\n\nThus output: page 1, doc_type "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3146, 'outputTokens': 797, 'totalTokens': 3943}, 'metrics': {'latencyMs': 3257}}
2025-09-24 18:16:19,824 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7b0eafde_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:19,860 - INFO - 

Y2EQEYPYUUYHAD9BZKXO.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:19,860 - INFO - 

✓ Saved result: output/run1_Y2EQEYPYUUYHAD9BZKXO.json
2025-09-24 18:16:20,006 - INFO - Page 1: Extracted 1075 characters, 67 lines from c33f3d8f_ZA6FSHKD588WC7VTH3MO_5f98b78e_page_001.pdf
2025-09-24 18:16:20,006 - INFO - Successfully processed page 1
2025-09-24 18:16:20,006 - INFO - Combined 1 pages into final text
2025-09-24 18:16:20,006 - INFO - Text validation for c33f3d8f_ZA6FSHKD588WC7VTH3MO: 1092 characters, 1 pages
2025-09-24 18:16:20,007 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:20,007 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:20,148 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7b0eafde_Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:20,257 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '745dbd39-e4a3-4d72-844e-3a03ecde2dac', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:20 GMT', 'content-type': 'application/json', 'content-length': '1373', 'connection': 'keep-alive', 'x-amzn-requestid': '745dbd39-e4a3-4d72-844e-3a03ecde2dac'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. We must decide doc_type. Need to analyze content. This looks like a scale ticket: has gross weight, tare weight, net weight, scale in/out, vehicle, container, bill of lading number and a reference. Contains weight keywords: Gross, Tare, Net, Weighed At, etc. Also mentions "SCALE IN GROSS WEIGHT", "INVOICE", but still weight. So likely "scale_ticket".\n\nCheck definitions: scale ticket keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. This page has weight: "SCALE IN GROSS WEIGHT 73,740", "INBOUND SCALE OUT TARE WEIGHT 32,440", "NET WEIGHT 41,300". Also "DATE/TIME IN" and "DATE/TIME OUT". So matches scale ticket. Not a BOL because BOL is typical shipping doc but this page is more like a weigh ticket. It is also a load tender? No. So "scale_ticket".\n\nWe need to output via tool classify_logistics_doc_type with documents array containing one object: {page_no:1, doc_type:"scale_ticket"}.\n\nProceed.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2902, 'outputTokens': 285, 'totalTokens': 3187}, 'metrics': {'latencyMs': 1315}}
2025-09-24 18:16:20,257 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5a933e29_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:20,299 - INFO - Page 1: Extracted 330 characters, 26 lines from 25c649ea_NLUB3HCM1QWW6FXJBEY3_65b36fb0_page_001.pdf
2025-09-24 18:16:20,300 - INFO - Successfully processed page 1
2025-09-24 18:16:20,301 - INFO - Combined 1 pages into final text
2025-09-24 18:16:20,301 - INFO - 

OPDBRSVH28PEQZSD5FO9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:20,301 - INFO - Text validation for 25c649ea_NLUB3HCM1QWW6FXJBEY3: 347 characters, 1 pages
2025-09-24 18:16:20,302 - INFO - 

✓ Saved result: output/run1_OPDBRSVH28PEQZSD5FO9.json
2025-09-24 18:16:20,304 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:20,304 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:20,542 - INFO - Page 1: Extracted 1052 characters, 70 lines from 36ade61b_ZY3XO73EAB4BC4957F0V_bd6e5988_page_001.pdf
2025-09-24 18:16:20,543 - INFO - Successfully processed page 1
2025-09-24 18:16:20,543 - INFO - Combined 1 pages into final text
2025-09-24 18:16:20,543 - INFO - Text validation for 36ade61b_ZY3XO73EAB4BC4957F0V: 1069 characters, 1 pages
2025-09-24 18:16:20,544 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:20,544 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:20,624 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5a933e29_OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:22,366 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2e718cd7-540b-4e33-8ece-f32e3f96f4c0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:22 GMT', 'content-type': 'application/json', 'content-length': '894', 'connection': 'keep-alive', 'x-amzn-requestid': '2e718cd7-540b-4e33-8ece-f32e3f96f4c0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. Let\'s analyze content. It\'s a document likely from a recycling and transfer station. It lists Ticket, Date, Customer, Hauler, Truck, times in and out, weights, net weight. Likely a "scale ticket" because it has weight info. Keywords: "Ticket", "In", "Out", "lb", "Net". That matches scale ticket definition. So doc_type: "scale_ticket". Also page number? We should output array with objects for pages. There\'s one page, page_no: 1. So output: documents: [{page_no:1, doc_type:"scale_ticket"}]. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2667, 'outputTokens': 156, 'totalTokens': 2823}, 'metrics': {'latencyMs': 805}}
2025-09-24 18:16:22,366 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/25c649ea_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:22,379 - INFO - 

NLUB3HCM1QWW6FXJBEY3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:22,380 - INFO - 

✓ Saved result: output/run1_NLUB3HCM1QWW6FXJBEY3.json
2025-09-24 18:16:22,444 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a541d5fc-7ead-453c-aa2c-09a9622a13e8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:22 GMT', 'content-type': 'application/json', 'content-length': '1215', 'connection': 'keep-alive', 'x-amzn-requestid': 'a541d5fc-7ead-453c-aa2c-09a9622a13e8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1. Determine doc type.\n\nWe have text: It shows weighmaster, date/time in/out, scale in gross weight, net tons, scale out tare weight, net weight invoice? It includes "BILL OF LADING 531837" and "SCALE IN GROSS WEIGHT" etc. It looks like a Scale Ticket: weight record from a scale for vehicle/load. Terms: Gross, Tare, Net, Weighed At, etc. Also has Invoice? But it\'s a scale ticket.\n\nAlso it has "BILL OF LADING" header, but it\'s a scale ticket page. The key is that there are weight terms: "SCALE IN GROSS WEIGHT", "NET TONS" etc. So classify as scale_ticket.\n\nAlso the page includes some invoice details but likely a scale ticket (a scale ticket can include invoice number? But the main indicator is weight.\n\nThus doc_type: scale_ticket. Single page, not continuation.\n\nReturn JSON object with page_no:1 doc_type: "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2911, 'outputTokens': 235, 'totalTokens': 3146}, 'metrics': {'latencyMs': 1239}}
2025-09-24 18:16:22,444 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c33f3d8f_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:22,663 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/25c649ea_NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:22,674 - INFO - 

ZA6FSHKD588WC7VTH3MO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:22,674 - INFO - 

✓ Saved result: output/run1_ZA6FSHKD588WC7VTH3MO.json
2025-09-24 18:16:22,963 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c33f3d8f_ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:23,230 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'efc88c06-c685-4463-ad6c-8997e5091c32', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:23 GMT', 'content-type': 'application/json', 'content-length': '1466', 'connection': 'keep-alive', 'x-amzn-requestid': 'efc88c06-c685-4463-ad6c-8997e5091c32'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We need to decide doc_type. Let\'s analyze content.\n\nThe page shows a "SITE TICKET #", WEIGHMASTER, IN OUT times, vehicle details, reference, contract: EARTHWATCH MSW, BILL OF LADING 531839, scale in gross weight, net tons, inbound scale out tare weight, net weight. Then invoice QTY., UNIT, DESCRIPTION, RATE, EXTENSION, TAX, TOTAL. There\'s "INVOICE" heading. Also "SCALE IN GROSS WEIGHT" etc. This seems like a scale ticket because it has weight info, gross, tare, net weight, etc. But also includes invoice details. However per rule: if contains weight-related keywords like Gross, Tare, Net, Weighed At, etc, then it is a scale ticket. The presence of scale values indicates scale ticket. So doc_type = scale_ticket.\n\nCheck for other categories: invoice would have invoice fields like amount due, but here we have net amount but not typical invoice. There is "INVOICE" heading but likely part of scale ticket. The page includes "BILL OF LADING" but only number.\n\nThus classify as scale_ticket. Output JSON array: one object with page_no 1 and doc_type "scale_ticket". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2898, 'outputTokens': 286, 'totalTokens': 3184}, 'metrics': {'latencyMs': 1461}}
2025-09-24 18:16:23,230 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36ade61b_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:23,256 - INFO - 

ZY3XO73EAB4BC4957F0V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:23,256 - INFO - 

✓ Saved result: output/run1_ZY3XO73EAB4BC4957F0V.json
2025-09-24 18:16:23,546 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36ade61b_ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:23,703 - INFO - Page 1: Extracted 2153 characters, 69 lines from 553b1c3a_LVWIMLJ8Q7EH6QLW7RTX_e3f042b6_page_001.pdf
2025-09-24 18:16:23,704 - INFO - Successfully processed page 1
2025-09-24 18:16:23,769 - INFO - Page 2: Extracted 2206 characters, 75 lines from 553b1c3a_LVWIMLJ8Q7EH6QLW7RTX_e3f042b6_page_002.pdf
2025-09-24 18:16:23,769 - INFO - Successfully processed page 2
2025-09-24 18:16:23,769 - INFO - Combined 2 pages into final text
2025-09-24 18:16:23,770 - INFO - Text validation for 553b1c3a_LVWIMLJ8Q7EH6QLW7RTX: 4395 characters, 2 pages
2025-09-24 18:16:23,770 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:23,770 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:24,867 - INFO - Page 1: Extracted 4293 characters, 145 lines from e0c1ca96_MZ3X0YRE37Y7NHS45V2U_c171a9b7_page_001.pdf
2025-09-24 18:16:24,867 - INFO - Successfully processed page 1
2025-09-24 18:16:24,867 - INFO - Combined 1 pages into final text
2025-09-24 18:16:24,867 - INFO - Text validation for e0c1ca96_MZ3X0YRE37Y7NHS45V2U: 4310 characters, 1 pages
2025-09-24 18:16:24,868 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:16:24,868 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:16:25,893 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9de64160-35db-4f49-8162-fbd9eddf4839', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:25 GMT', 'content-type': 'application/json', 'content-length': '945', 'connection': 'keep-alive', 'x-amzn-requestid': '9de64160-35db-4f49-8162-fbd9eddf4839'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Each page contains a "CAT SCALE GUARANTEE" and a "PUBLIC WEIGHMASTER\'S CERTIFICATE OF WEIGHT & MEASURE". The content indicates a scale ticket, weight certificate, with weight and measure. This is a scale ticket. The keywords include Gross, Net, weight, etc. So classification: "scale_ticket".\n\nAlso note that page 2 seems a continuation? Actually page 2 is a separate guarantee ticket. No "continued". So each page is its own document. So output two objects: page_no 1, doc_type "scale_ticket". page_no 2, doc_type "scale_ticket". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"},{"page_no":2,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3807, 'outputTokens': 163, 'totalTokens': 3970}, 'metrics': {'latencyMs': 910}}
2025-09-24 18:16:25,893 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/553b1c3a_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:25,948 - INFO - 

LVWIMLJ8Q7EH6QLW7RTX.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        },
        {
            "page_no": 2,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:25,949 - INFO - 

✓ Saved result: output/run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:16:26,282 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/553b1c3a_LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:26,927 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'becdd263-7572-415f-a3dc-39af1031a8cd', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:46:26 GMT', 'content-type': 'application/json', 'content-length': '786', 'connection': 'keep-alive', 'x-amzn-requestid': 'becdd263-7572-415f-a3dc-39af1031a8cd'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It contains a "PUBLIC WEIGHMASTER\'S CERTIFICATE OF WEIGHT & MEASURE" and a "CAT SCALE GUARANTEE". This looks like a weigh ticket. Document type likely "scale_ticket". The page includes weight data, gross weight, etc. It also references "WEIGHT & MEASURE". The keywords: Gross Weight, Weigh Ticket, weighmaster, weigh master. So scale_ticket. No mention of invoice, etc. So doc_type is scale_ticket. The page number is 1. Output array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3779, 'outputTokens': 129, 'totalTokens': 3908}, 'metrics': {'latencyMs': 753}}
2025-09-24 18:16:26,928 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e0c1ca96_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:26,981 - INFO - 

MZ3X0YRE37Y7NHS45V2U.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 18:16:26,981 - INFO - 

✓ Saved result: output/run1_MZ3X0YRE37Y7NHS45V2U.json
2025-09-24 18:16:27,312 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e0c1ca96_MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:27,314 - INFO - 
📊 Processing Summary:
2025-09-24 18:16:27,314 - INFO -    Total files: 13
2025-09-24 18:16:27,314 - INFO -    Successful: 13
2025-09-24 18:16:27,315 - INFO -    Failed: 0
2025-09-24 18:16:27,315 - INFO -    Duration: 26.50 seconds
2025-09-24 18:16:27,315 - INFO -    Output directory: output
2025-09-24 18:16:27,315 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:16:27,315 - INFO -    📄 A8UNJQR3VGLF8G4MC11J.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 AK2S46MQ9UJW5Q43TRRV.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 EAMCIVO8E5KY4U6ZYWK2.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 F4RZ1UDKLCELJG8T6R9G.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 KBFTEJIYFMKC7ZILXM5M.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 LVWIMLJ8Q7EH6QLW7RTX.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"},{"page_no":2,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 MZ3X0YRE37Y7NHS45V2U.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 NLUB3HCM1QWW6FXJBEY3.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 OPDBRSVH28PEQZSD5FO9.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 Y2EQEYPYUUYHAD9BZKXO.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,315 - INFO -    📄 Z1N2YIEHAA2FERWDQ38E.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,316 - INFO -    📄 ZA6FSHKD588WC7VTH3MO.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,316 - INFO -    📄 ZY3XO73EAB4BC4957F0V.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 18:16:27,316 - INFO - 
============================================================================================================================================
2025-09-24 18:16:27,316 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:16:27,316 - INFO - ============================================================================================================================================
2025-09-24 18:16:27,316 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:16:27,317 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:16:27,317 - INFO - A8UNJQR3VGLF8G4MC11J.pdf                           1      scale_ticket                             run1_A8UNJQR3VGLF8G4MC11J.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/A8UNJQR3VGLF8G4MC11J.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A8UNJQR3VGLF8G4MC11J.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - AK2S46MQ9UJW5Q43TRRV.pdf                           1      scale_ticket                             run1_AK2S46MQ9UJW5Q43TRRV.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/AK2S46MQ9UJW5Q43TRRV.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AK2S46MQ9UJW5Q43TRRV.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - EAMCIVO8E5KY4U6ZYWK2.jpg                           1      scale_ticket                             run1_EAMCIVO8E5KY4U6ZYWK2.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/EAMCIVO8E5KY4U6ZYWK2.jpg
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EAMCIVO8E5KY4U6ZYWK2.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - F4RZ1UDKLCELJG8T6R9G.png                           1      scale_ticket                             run1_F4RZ1UDKLCELJG8T6R9G.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/F4RZ1UDKLCELJG8T6R9G.png
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_F4RZ1UDKLCELJG8T6R9G.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - KBFTEJIYFMKC7ZILXM5M.png                           1      scale_ticket                             run1_KBFTEJIYFMKC7ZILXM5M.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/KBFTEJIYFMKC7ZILXM5M.png
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KBFTEJIYFMKC7ZILXM5M.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - LVWIMLJ8Q7EH6QLW7RTX.pdf                           1      scale_ticket                             run1_LVWIMLJ8Q7EH6QLW7RTX.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - LVWIMLJ8Q7EH6QLW7RTX.pdf                           2      scale_ticket                             run1_LVWIMLJ8Q7EH6QLW7RTX.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - MZ3X0YRE37Y7NHS45V2U.pdf                           1      scale_ticket                             run1_MZ3X0YRE37Y7NHS45V2U.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/MZ3X0YRE37Y7NHS45V2U.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MZ3X0YRE37Y7NHS45V2U.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - NLUB3HCM1QWW6FXJBEY3.pdf                           1      scale_ticket                             run1_NLUB3HCM1QWW6FXJBEY3.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/NLUB3HCM1QWW6FXJBEY3.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NLUB3HCM1QWW6FXJBEY3.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - OPDBRSVH28PEQZSD5FO9.pdf                           1      scale_ticket                             run1_OPDBRSVH28PEQZSD5FO9.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/OPDBRSVH28PEQZSD5FO9.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OPDBRSVH28PEQZSD5FO9.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - Y2EQEYPYUUYHAD9BZKXO.jpg                           1      scale_ticket                             run1_Y2EQEYPYUUYHAD9BZKXO.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Y2EQEYPYUUYHAD9BZKXO.jpg
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y2EQEYPYUUYHAD9BZKXO.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - Z1N2YIEHAA2FERWDQ38E.pdf                           1      scale_ticket                             run1_Z1N2YIEHAA2FERWDQ38E.json                                                  
2025-09-24 18:16:27,317 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Z1N2YIEHAA2FERWDQ38E.pdf
2025-09-24 18:16:27,317 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z1N2YIEHAA2FERWDQ38E.json
2025-09-24 18:16:27,317 - INFO - 
2025-09-24 18:16:27,317 - INFO - ZA6FSHKD588WC7VTH3MO.pdf                           1      scale_ticket                             run1_ZA6FSHKD588WC7VTH3MO.json                                                  
2025-09-24 18:16:27,318 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZA6FSHKD588WC7VTH3MO.pdf
2025-09-24 18:16:27,318 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZA6FSHKD588WC7VTH3MO.json
2025-09-24 18:16:27,318 - INFO - 
2025-09-24 18:16:27,318 - INFO - ZY3XO73EAB4BC4957F0V.pdf                           1      scale_ticket                             run1_ZY3XO73EAB4BC4957F0V.json                                                  
2025-09-24 18:16:27,318 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZY3XO73EAB4BC4957F0V.pdf
2025-09-24 18:16:27,318 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZY3XO73EAB4BC4957F0V.json
2025-09-24 18:16:27,318 - INFO - 
2025-09-24 18:16:27,318 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:16:27,318 - INFO - Total entries: 14
2025-09-24 18:16:27,318 - INFO - ============================================================================================================================================
2025-09-24 18:16:27,318 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:16:27,318 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:16:27,318 - INFO -   1. A8UNJQR3VGLF8G4MC11J.pdf            Page 1   → scale_ticket    | run1_A8UNJQR3VGLF8G4MC11J.json
2025-09-24 18:16:27,318 - INFO -   2. AK2S46MQ9UJW5Q43TRRV.pdf            Page 1   → scale_ticket    | run1_AK2S46MQ9UJW5Q43TRRV.json
2025-09-24 18:16:27,318 - INFO -   3. EAMCIVO8E5KY4U6ZYWK2.jpg            Page 1   → scale_ticket    | run1_EAMCIVO8E5KY4U6ZYWK2.json
2025-09-24 18:16:27,318 - INFO -   4. F4RZ1UDKLCELJG8T6R9G.png            Page 1   → scale_ticket    | run1_F4RZ1UDKLCELJG8T6R9G.json
2025-09-24 18:16:27,318 - INFO -   5. KBFTEJIYFMKC7ZILXM5M.png            Page 1   → scale_ticket    | run1_KBFTEJIYFMKC7ZILXM5M.json
2025-09-24 18:16:27,318 - INFO -   6. LVWIMLJ8Q7EH6QLW7RTX.pdf            Page 1   → scale_ticket    | run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:16:27,318 - INFO -   7. LVWIMLJ8Q7EH6QLW7RTX.pdf            Page 2   → scale_ticket    | run1_LVWIMLJ8Q7EH6QLW7RTX.json
2025-09-24 18:16:27,318 - INFO -   8. MZ3X0YRE37Y7NHS45V2U.pdf            Page 1   → scale_ticket    | run1_MZ3X0YRE37Y7NHS45V2U.json
2025-09-24 18:16:27,318 - INFO -   9. NLUB3HCM1QWW6FXJBEY3.pdf            Page 1   → scale_ticket    | run1_NLUB3HCM1QWW6FXJBEY3.json
2025-09-24 18:16:27,318 - INFO -  10. OPDBRSVH28PEQZSD5FO9.pdf            Page 1   → scale_ticket    | run1_OPDBRSVH28PEQZSD5FO9.json
2025-09-24 18:16:27,318 - INFO -  11. Y2EQEYPYUUYHAD9BZKXO.jpg            Page 1   → scale_ticket    | run1_Y2EQEYPYUUYHAD9BZKXO.json
2025-09-24 18:16:27,318 - INFO -  12. Z1N2YIEHAA2FERWDQ38E.pdf            Page 1   → scale_ticket    | run1_Z1N2YIEHAA2FERWDQ38E.json
2025-09-24 18:16:27,318 - INFO -  13. ZA6FSHKD588WC7VTH3MO.pdf            Page 1   → scale_ticket    | run1_ZA6FSHKD588WC7VTH3MO.json
2025-09-24 18:16:27,318 - INFO -  14. ZY3XO73EAB4BC4957F0V.pdf            Page 1   → scale_ticket    | run1_ZY3XO73EAB4BC4957F0V.json
2025-09-24 18:16:27,318 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:16:27,319 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 26.500307, 'processed_files': [{'filename': 'A8UNJQR3VGLF8G4MC11J.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/A8UNJQR3VGLF8G4MC11J.pdf'}, {'filename': 'AK2S46MQ9UJW5Q43TRRV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/AK2S46MQ9UJW5Q43TRRV.pdf'}, {'filename': 'EAMCIVO8E5KY4U6ZYWK2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/EAMCIVO8E5KY4U6ZYWK2.jpg'}, {'filename': 'F4RZ1UDKLCELJG8T6R9G.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/F4RZ1UDKLCELJG8T6R9G.png'}, {'filename': 'KBFTEJIYFMKC7ZILXM5M.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/KBFTEJIYFMKC7ZILXM5M.png'}, {'filename': 'LVWIMLJ8Q7EH6QLW7RTX.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}, {'page_no': 2, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/LVWIMLJ8Q7EH6QLW7RTX.pdf'}, {'filename': 'MZ3X0YRE37Y7NHS45V2U.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/MZ3X0YRE37Y7NHS45V2U.pdf'}, {'filename': 'NLUB3HCM1QWW6FXJBEY3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/NLUB3HCM1QWW6FXJBEY3.pdf'}, {'filename': 'OPDBRSVH28PEQZSD5FO9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/OPDBRSVH28PEQZSD5FO9.pdf'}, {'filename': 'Y2EQEYPYUUYHAD9BZKXO.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Y2EQEYPYUUYHAD9BZKXO.jpg'}, {'filename': 'Z1N2YIEHAA2FERWDQ38E.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/Z1N2YIEHAA2FERWDQ38E.pdf'}, {'filename': 'ZA6FSHKD588WC7VTH3MO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZA6FSHKD588WC7VTH3MO.pdf'}, {'filename': 'ZY3XO73EAB4BC4957F0V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/scale_ticket/ZY3XO73EAB4BC4957F0V.pdf'}]}
