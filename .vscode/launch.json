{"version": "0.2.0", "configurations": [{"name": "Debug test_classification.py (Default Test)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/test_classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": [], "env": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction", "DEBUG_ON_ERROR": "true"}, "envFile": "${workspaceFolder}/.env", "python": "${workspaceFolder}/venv/bin/python", "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "subProcess": true, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}, {"localRoot": "/home/<USER>/Documents/repositories/document-data-extraction", "remoteRoot": "/home/<USER>/Documents/repositories/document-data-extraction"}]}, {"name": "Debug test_classification.py (Custom Folder)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/test_classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["${workspaceFolder}/verified_docs_v1/weight_and_inspection_cert", "--max-files", "2", "--debug", "--output-dir", "debug_output", "--run-number", "1"], "env": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction", "DEBUG_ON_ERROR": "true"}, "envFile": "${workspaceFolder}/.env", "python": "${workspaceFolder}/venv/bin/python", "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "subProcess": true, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}, {"localRoot": "/home/<USER>/Documents/repositories/document-data-extraction", "remoteRoot": "/home/<USER>/Documents/repositories/document-data-extraction"}]}, {"name": "Debug test_classification.py (Step Into All Code)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/test_classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["${workspaceFolder}/verified_docs_v1/weight_and_inspection_cert", "--max-files", "1", "--debug-on-start", "--debug", "--output-dir", "debug_output", "--run-number", "1"], "env": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction", "DEBUG_ON_ERROR": "true"}, "envFile": "${workspaceFolder}/.env", "python": "${workspaceFolder}/venv/bin/python", "justMyCode": false, "stopOnEntry": true, "showReturnValue": true, "redirectOutput": true, "subProcess": true, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}, {"localRoot": "/home/<USER>/Documents/repositories/document-data-extraction", "remoteRoot": "/home/<USER>/Documents/repositories/document-data-extraction"}]}, {"name": "Debug test_classification.py (Single File Quick Test)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/test_classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["${workspaceFolder}/verified_docs_v1/weight_and_inspection_cert", "--max-files", "1", "--debug", "--output-dir", "Augment_test/debug_output", "--run-number", "1"], "env": {"PYTHONPATH": "${workspaceFolder}:/home/<USER>/Documents/repositories/document-data-extraction", "DEBUG_ON_ERROR": "true"}, "envFile": "${workspaceFolder}/.env", "python": "${workspaceFolder}/venv/bin/python", "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "subProcess": true, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}, {"localRoot": "/home/<USER>/Documents/repositories/document-data-extraction", "remoteRoot": "/home/<USER>/Documents/repositories/document-data-extraction"}]}, {"name": "Debug External Classification Module", "type": "debugpy", "request": "launch", "program": "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", "console": "integratedTerminal", "cwd": "/home/<USER>/Documents/repositories/document-data-extraction", "args": ["s3://document-extraction-logistically/temp/test_file.pdf"], "env": {"PYTHONPATH": "/home/<USER>/Documents/repositories/document-data-extraction:${workspaceFolder}", "DEBUG_ON_ERROR": "true"}, "envFile": "${workspaceFolder}/.env", "python": "/home/<USER>/Documents/repositories/document-data-extraction/venv/bin/python", "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "subProcess": true, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}, {"localRoot": "/home/<USER>/Documents/repositories/document-data-extraction", "remoteRoot": "/home/<USER>/Documents/repositories/document-data-extraction"}]}]}