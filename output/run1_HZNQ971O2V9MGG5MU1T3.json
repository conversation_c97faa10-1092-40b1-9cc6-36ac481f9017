{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "33df9810-8729-478b-8f30-ea7ebed83217", "Relationships": [{"Type": "CHILD", "Ids": ["bca0f23b-25fa-415e-a024-7fe0fcbfad1b", "f77ca1cf-3b23-46b6-ba8c-a3b424c3eb73", "b2d21824-4100-4d8f-86b6-a04349310fc6", "94b85198-0333-445e-affd-aa591c5717ee", "e38efa6b-ad6f-41c8-8867-218b1b60dacc", "95f841c6-a3aa-4a76-a255-69d64cf1ca9e", "a5142b35-4cab-4b3e-817e-c40fa26770db", "1ce77c04-8d5e-409c-b63a-35cc7fe557a6", "df026c7f-c84d-46d5-b6f7-7d09296273a3", "4590faef-0140-464a-93ec-97ac24087c7e", "ce41ed5d-f29e-42d1-8b1c-cac5a38136d3", "bc3ad204-3d0e-4582-af01-48d2918ce7f3", "926fee02-77e0-4370-866c-89faed8170b1", "b18c8fb8-e5bb-4d42-8206-6d5464e3205c", "0c82ba6c-341c-47ab-9fec-847daaf79b02", "7d53cf7d-e987-45f0-b96b-64801f259686", "101b2ecb-622e-4b52-aa03-c49a0e9e7499", "2636e3f5-dd11-43cc-b82a-818c054ef42f", "ba0d4a76-6fd2-47b2-8547-6e8089ea43e2", "c03a401d-7c73-4f87-96d8-a9398dd864c1", "91e71ceb-4b4e-4758-9875-90d167581f15", "3658763f-64d2-45a8-955a-6d229728e627", "74e1ec38-7f4d-45d4-9c02-eae1ab39b19d", "93b525be-4192-4f7b-84d3-8da269fd8234", "4c34cc53-40d6-4e90-bdc2-6bfad1a8e181", "726ae3fc-1974-420e-a95a-8f8b4c7db367", "fcd310ed-f7f5-44d7-a2ee-7aacd6132759", "b07afc9a-3ea9-419f-bca0-1f18eb57d709", "5dbc782c-994b-4d68-ba79-a781ac1f8b00", "5c72acc9-e949-4170-b6dc-48bd0386416c", "4acec9df-3843-4ed5-a5e8-f4b0b4b2be9a", "f1ca2640-848a-4f56-bba3-2f412713ac64", "e64e03af-2961-4680-b428-6706cbf9bc77"]}]}, {"BlockType": "LINE", "Confidence": 99.03519439697266, "Text": "Interchange Receipt (J1)", "Geometry": {"BoundingBox": {"Width": 0.11466830223798752, "Height": 0.020970260724425316, "Left": 0.008977596648037434, "Top": 0.012684768997132778}, "Polygon": [{"X": 0.008978134021162987, "Y": 0.012684768997132778}, {"X": 0.12364589422941208, "Y": 0.01268563698977232}, {"X": 0.1236455887556076, "Y": 0.03365502879023552}, {"X": 0.008977596648037434, "Y": 0.03365422040224075}]}, "Id": "bca0f23b-25fa-415e-a024-7fe0fcbfad1b", "Relationships": [{"Type": "CHILD", "Ids": ["dc10d77b-97cc-4911-9b83-729ff06ce995", "53ce9842-781e-4a8d-982c-997036b7adba", "c93623c7-0d98-4fb4-b509-6ea6206ac8f6"]}]}, {"BlockType": "LINE", "Confidence": 91.55113983154297, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.005264751613140106, "Height": 0.013869701884686947, "Left": 0.9390263557434082, "Top": 0.014371751807630062}, "Polygon": [{"X": 0.9390263557434082, "Y": 0.014371751807630062}, {"X": 0.9442902207374573, "Y": 0.014371790923178196}, {"X": 0.9442911148071289, "Y": 0.02824145369231701}, {"X": 0.9390272498130798, "Y": 0.028241414576768875}]}, "Id": "f77ca1cf-3b23-46b6-ba8c-a3b424c3eb73", "Relationships": [{"Type": "CHILD", "Ids": ["decd42db-18cc-419f-af8a-e8e3bd2aac41"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "CLOSE", "Geometry": {"BoundingBox": {"Width": 0.021151792258024216, "Height": 0.013647965155541897, "Left": 0.9601471424102783, "Top": 0.014819605275988579}, "Polygon": [{"X": 0.9601471424102783, "Y": 0.014819605275988579}, {"X": 0.98129802942276, "Y": 0.014819764532148838}, {"X": 0.9812989830970764, "Y": 0.02846757136285305}, {"X": 0.96014803647995, "Y": 0.028467418625950813}]}, "Id": "b2d21824-4100-4d8f-86b6-a04349310fc6", "Relationships": [{"Type": "CHILD", "Ids": ["5ac79c81-ad98-4692-b4a2-fb5a45eefcf3"]}]}, {"BlockType": "LINE", "Confidence": 99.97017669677734, "Text": "BNSF Railway Company", "Geometry": {"BoundingBox": {"Width": 0.10760553181171417, "Height": 0.020155293866991997, "Left": 0.039698485285043716, "Top": 0.05792037770152092}, "Polygon": [{"X": 0.03969894349575043, "Y": 0.05792037770152092}, {"X": 0.1473040133714676, "Y": 0.05792107433080673}, {"X": 0.1473037749528885, "Y": 0.07807566970586777}, {"X": 0.039698485285043716, "Y": 0.07807502895593643}]}, "Id": "94b85198-0333-445e-affd-aa591c5717ee", "Relationships": [{"Type": "CHILD", "Ids": ["949aafaf-a380-43bd-b3b1-ef10f3323c49", "116e15de-18ed-4620-86c5-060bb4cf208e", "2e3f6a8b-fea9-4546-9ea4-d1cc78d2c820"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Equipment Inspection Record", "Geometry": {"BoundingBox": {"Width": 0.1345120221376419, "Height": 0.02056906744837761, "Left": 0.03962114825844765, "Top": 0.08567120879888535}, "Polygon": [{"X": 0.039621613919734955, "Y": 0.08567120879888535}, {"X": 0.17413316667079926, "Y": 0.08567198365926743}, {"X": 0.17413297295570374, "Y": 0.10624027997255325}, {"X": 0.03962114825844765, "Y": 0.10623956471681595}]}, "Id": "e38efa6b-ad6f-41c8-8867-218b1b60dacc", "Relationships": [{"Type": "CHILD", "Ids": ["0b18ac11-c9ab-40e7-baf4-d0f6eea92e55", "383d9561-369d-4fd2-ab59-95a2258cfcef", "10db9318-52e4-41cd-9f38-62a55d0ff5de"]}]}, {"BlockType": "LINE", "Confidence": 99.65421295166016, "Text": "YMMU 405514 LOAD", "Geometry": {"BoundingBox": {"Width": 0.09346465766429901, "Height": 0.016503991559147835, "Left": 0.03930288553237915, "Top": 0.1424565613269806}, "Polygon": [{"X": 0.039303261786699295, "Y": 0.1424565613269806}, {"X": 0.13276754319667816, "Y": 0.14245696365833282}, {"X": 0.13276731967926025, "Y": 0.15896055102348328}, {"X": 0.03930288553237915, "Y": 0.15896017849445343}]}, "Id": "95f841c6-a3aa-4a76-a255-69d64cf1ca9e", "Relationships": [{"Type": "CHILD", "Ids": ["ca048bf8-378e-4674-9e47-13f94d96ac7a", "2458db41-be2f-46c7-98f7-4d88660400ab", "b1084848-427b-4ef9-93b3-1410f7168988"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "FLAT 1234", "Geometry": {"BoundingBox": {"Width": 0.04827870801091194, "Height": 0.016550859436392784, "Left": 0.03952128440141678, "Top": 0.1703771948814392}, "Polygon": [{"X": 0.039521656930446625, "Y": 0.1703771948814392}, {"X": 0.08779998868703842, "Y": 0.17037737369537354}, {"X": 0.08779969066381454, "Y": 0.18692806363105774}, {"X": 0.03952128440141678, "Y": 0.1869278997182846}]}, "Id": "a5142b35-4cab-4b3e-817e-c40fa26770db", "Relationships": [{"Type": "CHILD", "Ids": ["567bcae1-261e-4e68-8225-02c9b5415717", "c32fe38c-5374-45fd-afba-4bc8f7ede97e"]}]}, {"BlockType": "LINE", "Confidence": 99.79771423339844, "Text": "Outgate: LOGPARCHI IL 07/07/2025 2315", "Geometry": {"BoundingBox": {"Width": 0.1948988437652588, "Height": 0.02119915746152401, "Left": 0.03942124918103218, "Top": 0.22523315250873566}, "Polygon": [{"X": 0.03942172974348068, "Y": 0.22523315250873566}, {"X": 0.23432010412216187, "Y": 0.22523361444473267}, {"X": 0.2343200147151947, "Y": 0.2464323192834854}, {"X": 0.03942124918103218, "Y": 0.24643196165561676}]}, "Id": "1ce77c04-8d5e-409c-b63a-35cc7fe557a6", "Relationships": [{"Type": "CHILD", "Ids": ["ba26c4ba-55ff-4660-aad1-4e0fc45fbe85", "b0bc2c86-414f-4fc9-99ec-e639536cf7f2", "f046dad8-74fa-41f4-8ef4-fdba293a3ccf", "57953be6-f3b5-48e7-9dd4-643238e13c1d", "e152d663-0d42-4954-a786-14844052a38e"]}]}, {"BlockType": "LINE", "Confidence": 99.73603057861328, "Text": "Patron: YANGMINAMERI", "Geometry": {"BoundingBox": {"Width": 0.1116105392575264, "Height": 0.01699373684823513, "Left": 0.0396168977022171, "Top": 0.25424259901046753}, "Polygon": [{"X": 0.03961728513240814, "Y": 0.25424259901046753}, {"X": 0.1512274444103241, "Y": 0.25424277782440186}, {"X": 0.15122723579406738, "Y": 0.2712363302707672}, {"X": 0.0396168977022171, "Y": 0.2712361812591553}]}, "Id": "df026c7f-c84d-46d5-b6f7-7d09296273a3", "Relationships": [{"Type": "CHILD", "Ids": ["c08508d9-3eff-4152-9b89-a6ed3f3a9472", "24d05012-9111-4b01-8982-3079c86ecbd6"]}]}, {"BlockType": "LINE", "Confidence": 99.79437255859375, "Text": "Trucker: DPBN IC To:", "Geometry": {"BoundingBox": {"Width": 0.09690611809492111, "Height": 0.017740052193403244, "Left": 0.03919679671525955, "Top": 0.2813551425933838}, "Polygon": [{"X": 0.039197199046611786, "Y": 0.2813551425933838}, {"X": 0.13610291481018066, "Y": 0.28135523200035095}, {"X": 0.13610267639160156, "Y": 0.29909518361091614}, {"X": 0.03919679671525955, "Y": 0.29909515380859375}]}, "Id": "4590faef-0140-464a-93ec-97ac24087c7e", "Relationships": [{"Type": "CHILD", "Ids": ["983bc4e8-e6fa-4048-85ba-655ee238d686", "8731f66b-7c6e-478f-960f-531ab410c754", "60485aeb-6205-4bd6-847c-93d997248e71", "86db6845-1b6b-454c-bd33-05eb0e56c661"]}]}, {"BlockType": "LINE", "Confidence": 99.84095764160156, "Text": "License: 5177", "Geometry": {"BoundingBox": {"Width": 0.06304343789815903, "Height": 0.01715356856584549, "Left": 0.03959345445036888, "Top": 0.31008943915367126}, "Polygon": [{"X": 0.03959384560585022, "Y": 0.31008943915367126}, {"X": 0.1026368960738182, "Y": 0.31008943915367126}, {"X": 0.10263661295175552, "Y": 0.32724300026893616}, {"X": 0.03959345445036888, "Y": 0.32724300026893616}]}, "Id": "ce41ed5d-f29e-42d1-8b1c-cac5a38136d3", "Relationships": [{"Type": "CHILD", "Ids": ["20e198c1-41c7-44eb-9e5f-3365c18852c7", "61c2c8ba-4a1d-4d85-a640-9145fe656941"]}]}, {"BlockType": "LINE", "Confidence": 99.60249328613281, "Text": "Seal: YMAT715808", "Geometry": {"BoundingBox": {"Width": 0.08547191321849823, "Height": 0.01776265911757946, "Left": 0.03942125663161278, "Top": 0.3375374674797058}, "Polygon": [{"X": 0.03942165896296501, "Y": 0.3375374972820282}, {"X": 0.1248931735754013, "Y": 0.3375374674797058}, {"X": 0.12489291280508041, "Y": 0.35530006885528564}, {"X": 0.03942125663161278, "Y": 0.3553001284599304}]}, "Id": "bc3ad204-3d0e-4582-af01-48d2918ce7f3", "Relationships": [{"Type": "CHILD", "Ids": ["682dfaa8-4ac2-4de5-b0f8-f17393c8ee3d", "a2993626-6c0a-46a7-b43f-af0156b26448"]}]}, {"BlockType": "LINE", "Confidence": 99.80598449707031, "Text": "Cust: YANGMINMARLI", "Geometry": {"BoundingBox": {"Width": 0.10145194083452225, "Height": 0.017191030085086823, "Left": 0.03951337933540344, "Top": 0.36621221899986267}, "Polygon": [{"X": 0.03951377049088478, "Y": 0.3662123382091522}, {"X": 0.1409653276205063, "Y": 0.36621221899986267}, {"X": 0.14096510410308838, "Y": 0.38340309262275696}, {"X": 0.03951337933540344, "Y": 0.3834032714366913}]}, "Id": "926fee02-77e0-4370-866c-89faed8170b1", "Relationships": [{"Type": "CHILD", "Ids": ["2a45f942-78c6-4581-a98f-1917daf96b05", "c521498b-d97b-4af0-820f-8da76c629cf4"]}]}, {"BlockType": "LINE", "Confidence": 99.88175201416016, "Text": "Release ID: 6555CC", "Geometry": {"BoundingBox": {"Width": 0.09034254401922226, "Height": 0.017561480402946472, "Left": 0.039571113884449005, "Top": 0.39362913370132446}, "Polygon": [{"X": 0.03957151249051094, "Y": 0.3936293125152588}, {"X": 0.12991365790367126, "Y": 0.39362913370132446}, {"X": 0.12991340458393097, "Y": 0.4111904203891754}, {"X": 0.039571113884449005, "Y": 0.41119062900543213}]}, "Id": "b18c8fb8-e5bb-4d42-8206-6d5464e3205c", "Relationships": [{"Type": "CHILD", "Ids": ["77a73919-b212-4858-88ea-5df78fc20dde", "c4d6ad7a-dd94-431a-91c2-b8ca9b5f7e54", "889288fa-9955-4586-9735-87e8b5278ba0"]}]}, {"BlockType": "LINE", "Confidence": 99.9378890991211, "Text": "Gross WGT: 51762", "Geometry": {"BoundingBox": {"Width": 0.0846831426024437, "Height": 0.017522742971777916, "Left": 0.039505261927843094, "Top": 0.42174869775772095}, "Polygon": [{"X": 0.03950566053390503, "Y": 0.42174890637397766}, {"X": 0.12418840080499649, "Y": 0.42174869775772095}, {"X": 0.1241881474852562, "Y": 0.4392711818218231}, {"X": 0.039505261927843094, "Y": 0.4392714202404022}]}, "Id": "0c82ba6c-341c-47ab-9fec-847daaf79b02", "Relationships": [{"Type": "CHILD", "Ids": ["101df188-2ed0-4bac-994d-bf49263c005c", "d19f52b0-d437-4c86-983a-6ca614516eb1", "ce81db72-3779-4033-93a5-af394a7c8cee"]}]}, {"BlockType": "LINE", "Confidence": 99.8985595703125, "Text": "Unit cannot return empty w/out waybill", "Geometry": {"BoundingBox": {"Width": 0.18224908411502838, "Height": 0.021491549909114838, "Left": 0.03957672417163849, "Top": 0.4489491581916809}, "Polygon": [{"X": 0.03957721218466759, "Y": 0.4489497244358063}, {"X": 0.22182579338550568, "Y": 0.4489491581916809}, {"X": 0.22182568907737732, "Y": 0.4704400300979614}, {"X": 0.03957672417163849, "Y": 0.47044068574905396}]}, "Id": "7d53cf7d-e987-45f0-b96b-64801f259686", "Relationships": [{"Type": "CHILD", "Ids": ["86512501-0c78-4da8-ad8b-a72aa4aabde5", "de7c9790-8725-4f61-a4cd-c6c6751a36de", "30787ad1-87a1-4ef6-baab-b18ad21c5f4c", "afb0b18f-b920-4485-aa0c-5cd01731b53f", "a96e1bc9-0bf3-4e47-9c38-25d8d2de8a05", "34d1ea60-2755-4ec6-bc95-e0ef4a040e58"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Damage:", "Geometry": {"BoundingBox": {"Width": 0.04004255682229996, "Height": 0.019995776936411858, "Left": 0.03969172015786171, "Top": 0.48507174849510193}, "Polygon": [{"X": 0.03969217464327812, "Y": 0.48507192730903625}, {"X": 0.07973427325487137, "Y": 0.48507174849510193}, {"X": 0.07973390072584152, "Y": 0.5050673484802246}, {"X": 0.03969172015786171, "Y": 0.5050675272941589}]}, "Id": "101b2ecb-622e-4b52-aa03-c49a0e9e7499", "Relationships": [{"Type": "CHILD", "Ids": ["3503e1fd-806c-45b5-8307-90f9ad1c2326"]}]}, {"BlockType": "LINE", "Confidence": 97.77538299560547, "Text": "PHOTOGRAPHIC INSPECTION - Equipment defects are", "Geometry": {"BoundingBox": {"Width": 0.24705159664154053, "Height": 0.021525084972381592, "Left": 0.039665210992097855, "Top": 0.5185543298721313}, "Polygon": [{"X": 0.03966570273041725, "Y": 0.5185555219650269}, {"X": 0.2867167890071869, "Y": 0.5185543298721313}, {"X": 0.2867168188095093, "Y": 0.5400781035423279}, {"X": 0.039665210992097855, "Y": 0.5400794148445129}]}, "Id": "2636e3f5-dd11-43cc-b82a-818c054ef42f", "Relationships": [{"Type": "CHILD", "Ids": ["e31563e5-8c79-48c3-a108-11d6b5cdb24f", "b8b6cc93-a369-4322-8d4b-95095fe0e49b", "fe49222c-d43e-4603-9937-9aece4955f60", "926b8d49-53d5-42e6-9485-1bff89cd4d9d", "ed23b3b9-36d6-42c9-8efa-a74731c07393", "4fa711b9-2972-40d9-9c00-65f7812c06e6"]}]}, {"BlockType": "LINE", "Confidence": 99.83079528808594, "Text": "captured on recorded images.", "Geometry": {"BoundingBox": {"Width": 0.13587264716625214, "Height": 0.021341972053050995, "Left": 0.03956186771392822, "Top": 0.5464046597480774}, "Polygon": [{"X": 0.03956235200166702, "Y": 0.5464054346084595}, {"X": 0.17543451488018036, "Y": 0.5464046597480774}, {"X": 0.17543430626392365, "Y": 0.5677458047866821}, {"X": 0.03956186771392822, "Y": 0.567746639251709}]}, "Id": "ba0d4a76-6fd2-47b2-8547-6e8089ea43e2", "Relationships": [{"Type": "CHILD", "Ids": ["998fcd2c-bd73-4112-8fae-b51c931d078e", "5975d263-fafd-48ba-85fe-ec0739381cb6", "4f081612-4b70-4a5d-8b53-570babbf44f1", "53c0773c-945d-4010-8453-d5b423402bd2"]}]}, {"BlockType": "LINE", "Confidence": 99.8606948852539, "Text": "Vessel Nme: YM UNANIMITY", "Geometry": {"BoundingBox": {"Width": 0.13036809861660004, "Height": 0.017450403422117233, "Left": 0.039174024015665054, "Top": 0.5816830992698669}, "Polygon": [{"X": 0.03917441889643669, "Y": 0.5816839337348938}, {"X": 0.169542133808136, "Y": 0.5816830992698669}, {"X": 0.16954195499420166, "Y": 0.5991325974464417}, {"X": 0.039174024015665054, "Y": 0.5991334915161133}]}, "Id": "c03a401d-7c73-4f87-96d8-a9398dd864c1", "Relationships": [{"Type": "CHILD", "Ids": ["bddaf91c-5f6b-41e5-abbb-61c2c42600c3", "560b9bdf-55d5-410f-90dc-ee8cf8c13394", "7ca3f2cd-b702-481b-a3da-16c2bfe8f50f", "c497af5e-7390-4f53-b346-a99c825e1bb9"]}]}, {"BlockType": "LINE", "Confidence": 99.66159057617188, "Text": "Nbr: 080E Bkg:", "Geometry": {"BoundingBox": {"Width": 0.06837465614080429, "Height": 0.020829616114497185, "Left": 0.039615292102098465, "Top": 0.609186053276062}, "Polygon": [{"X": 0.03961576521396637, "Y": 0.6091865301132202}, {"X": 0.10798994451761246, "Y": 0.609186053276062}, {"X": 0.1079896092414856, "Y": 0.6300151348114014}, {"X": 0.039615292102098465, "Y": 0.6300156712532043}]}, "Id": "91e71ceb-4b4e-4758-9875-90d167581f15", "Relationships": [{"Type": "CHILD", "Ids": ["8337555e-c1bd-407d-81e6-2f1a8e252522", "08572a3b-905d-48ab-a22d-2a5ffe675453", "285fafda-2538-43be-b0c7-be6bebdfab43"]}]}, {"BlockType": "LINE", "Confidence": 99.8680191040039, "Text": "Intermodal Safe Container Certification", "Geometry": {"BoundingBox": {"Width": 0.17992107570171356, "Height": 0.01812107488512993, "Left": 0.03931828960776329, "Top": 0.6435997486114502}, "Polygon": [{"X": 0.03931869938969612, "Y": 0.6436011791229248}, {"X": 0.21923936903476715, "Y": 0.6435997486114502}, {"X": 0.2192392647266388, "Y": 0.6617193222045898}, {"X": 0.03931828960776329, "Y": 0.661720871925354}]}, "Id": "3658763f-64d2-45a8-955a-6d229728e627", "Relationships": [{"Type": "CHILD", "Ids": ["286f8dc6-60ad-487f-b23a-ef9fb987c91c", "14cb8817-40f9-4c19-ba72-247169382065", "87414b1f-0073-4905-a6f0-d3f5ce39e074", "5fb67e02-c5c2-43aa-9a7e-08e9d693fe0e"]}]}, {"BlockType": "LINE", "Confidence": 99.47773742675781, "Text": "Cargo Weight: 43162 lbs", "Geometry": {"BoundingBox": {"Width": 0.11282668262720108, "Height": 0.021587742492556572, "Left": 0.03954498842358589, "Top": 0.6713494658470154}, "Polygon": [{"X": 0.03954548016190529, "Y": 0.6713504195213318}, {"X": 0.15237167477607727, "Y": 0.6713494658470154}, {"X": 0.15237142145633698, "Y": 0.6929361820220947}, {"X": 0.03954498842358589, "Y": 0.6929371953010559}]}, "Id": "74e1ec38-7f4d-45d4-9c02-eae1ab39b19d", "Relationships": [{"Type": "CHILD", "Ids": ["f1bad6e5-82f2-40b4-9e26-feb862c45a28", "428cea97-54e7-4431-aca7-3ee2613b4375", "13f60215-50a3-4887-926c-5edb44bd1364", "f7238864-d97e-4c81-a26b-856ea5a1d9eb"]}]}, {"BlockType": "LINE", "Confidence": 99.92662048339844, "Text": "Certify Party: YANG MING NO", "Geometry": {"BoundingBox": {"Width": 0.13441729545593262, "Height": 0.0210751760751009, "Left": 0.03949768468737602, "Top": 0.6993991732597351}, "Polygon": [{"X": 0.039498161524534225, "Y": 0.6994004249572754}, {"X": 0.17391496896743774, "Y": 0.6993991732597351}, {"X": 0.17391476035118103, "Y": 0.720473051071167}, {"X": 0.03949768468737602, "Y": 0.720474362373352}]}, "Id": "93b525be-4192-4f7b-84d3-8da269fd8234", "Relationships": [{"Type": "CHILD", "Ids": ["bd4513b3-d73d-48b2-bf47-d732c3a6541c", "ed4ea109-91b2-4b82-97ee-f3404270ddb8", "5190d5c5-0c0c-458e-89e4-ea10ac564a31", "22ead54a-ce31-4025-a14a-059fc8ccc6da", "3adea980-abb0-4a5f-9f50-56a77d211aa4"]}]}, {"BlockType": "LINE", "Confidence": 99.89424133300781, "Text": "Certify Date: 06/23/2025", "Geometry": {"BoundingBox": {"Width": 0.11772822588682175, "Height": 0.02101290039718151, "Left": 0.03949036821722984, "Top": 0.7274128794670105}, "Polygon": [{"X": 0.039490845054388046, "Y": 0.727414071559906}, {"X": 0.1572185903787613, "Y": 0.7274128794670105}, {"X": 0.1572183519601822, "Y": 0.7484245300292969}, {"X": 0.03949036821722984, "Y": 0.7484257817268372}]}, "Id": "4c34cc53-40d6-4e90-bdc2-6bfad1a8e181", "Relationships": [{"Type": "CHILD", "Ids": ["b263907c-f98c-423e-8100-a1cf3fa0cec6", "41051e58-39a5-46a6-bfca-cb49132f00ef", "b9f34822-a947-4a21-b494-e4a4532b8244"]}]}, {"BlockType": "LINE", "Confidence": 99.79472351074219, "Text": "Commodity: PLATES NON-C", "Geometry": {"BoundingBox": {"Width": 0.12531232833862305, "Height": 0.021214798092842102, "Left": 0.03933687508106232, "Top": 0.7556847929954529}, "Polygon": [{"X": 0.03933735936880112, "Y": 0.7556861042976379}, {"X": 0.16464920341968536, "Y": 0.7556847929954529}, {"X": 0.16464897990226746, "Y": 0.7768981456756592}, {"X": 0.03933687508106232, "Y": 0.7768995761871338}]}, "Id": "726ae3fc-1974-420e-a95a-8f8b4c7db367", "Relationships": [{"Type": "CHILD", "Ids": ["656a695a-369b-4e32-8f9d-c210047807fc", "00d22f6e-d759-43a4-9d45-a69e0df2a19e", "f2642114-d5d1-4eea-a479-380cf28cfe1a"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "Conversion Party:", "Geometry": {"BoundingBox": {"Width": 0.0810767412185669, "Height": 0.020208388566970825, "Left": 0.03934594988822937, "Top": 0.7844797968864441}, "Polygon": [{"X": 0.03934640809893608, "Y": 0.7844806909561157}, {"X": 0.12042269110679626, "Y": 0.7844797968864441}, {"X": 0.12042238563299179, "Y": 0.8046872019767761}, {"X": 0.03934594988822937, "Y": 0.8046881556510925}]}, "Id": "fcd310ed-f7f5-44d7-a2ee-7aacd6132759", "Relationships": [{"Type": "CHILD", "Ids": ["aa8e1ffb-01e5-4721-8ea0-c8b4d430a729", "f5f13d59-53be-4bc1-af0f-2b2f9485621d"]}]}, {"BlockType": "LINE", "Confidence": 99.21337127685547, "Text": "BNSF RAILWAY CO", "Geometry": {"BoundingBox": {"Width": 0.08582274615764618, "Height": 0.016373366117477417, "Left": 0.03965958580374718, "Top": 0.8130185008049011}, "Polygon": [{"X": 0.03965995833277702, "Y": 0.8130195140838623}, {"X": 0.12548233568668365, "Y": 0.8130185008049011}, {"X": 0.12548209726810455, "Y": 0.829390823841095}, {"X": 0.03965958580374718, "Y": 0.8293918967247009}]}, "Id": "b07afc9a-3ea9-419f-bca0-1f18eb57d709", "Relationships": [{"Type": "CHILD", "Ids": ["d67d5892-22ee-4042-a8db-e8dd9ca3ed5a", "83c1338e-1d91-469d-b175-cb77432d4cdd", "a12a89eb-b7a9-4713-90bb-11f844762cbd"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Conversion Date:", "Geometry": {"BoundingBox": {"Width": 0.07791857421398163, "Height": 0.017202168703079224, "Left": 0.039526600390672684, "Top": 0.8406387567520142}, "Polygon": [{"X": 0.03952699154615402, "Y": 0.8406397700309753}, {"X": 0.11744517087936401, "Y": 0.8406387567520142}, {"X": 0.11744491010904312, "Y": 0.8578399419784546}, {"X": 0.039526600390672684, "Y": 0.8578409552574158}]}, "Id": "5dbc782c-994b-4d68-ba79-a781ac1f8b00", "Relationships": [{"Type": "CHILD", "Ids": ["622b595c-cc84-42b3-bcfe-0c902e1a96c5", "b86e5066-e6af-4f5f-8515-84c695dbe637"]}]}, {"BlockType": "LINE", "Confidence": 99.7510757446289, "Text": "07/07/2025", "Geometry": {"BoundingBox": {"Width": 0.0570371113717556, "Height": 0.020525168627500534, "Left": 0.0395677387714386, "Top": 0.8675714135169983}, "Polygon": [{"X": 0.039568204432725906, "Y": 0.8675721883773804}, {"X": 0.0966048464179039, "Y": 0.8675714135169983}, {"X": 0.09660449624061584, "Y": 0.8880957961082458}, {"X": 0.0395677387714386, "Y": 0.8880965709686279}]}, "Id": "5c72acc9-e949-4170-b6dc-48bd0386416c", "Relationships": [{"Type": "CHILD", "Ids": ["cef914ef-6ed9-4164-8327-1601fdc049b1"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "PRINT", "Geometry": {"BoundingBox": {"Width": 0.019923320040106773, "Height": 0.014124037697911263, "Left": 0.90708988904953, "Top": 0.905482292175293}, "Polygon": [{"X": 0.90708988904953, "Y": 0.9054825901985168}, {"X": 0.9270123839378357, "Y": 0.905482292175293}, {"X": 0.9270132184028625, "Y": 0.9196060299873352}, {"X": 0.9070907831192017, "Y": 0.9196063280105591}]}, "Id": "4acec9df-3843-4ed5-a5e8-f4b0b4b2be9a", "Relationships": [{"Type": "CHILD", "Ids": ["225c1a69-b4e6-493e-bd31-89d62b02b313"]}]}, {"BlockType": "LINE", "Confidence": 74.84683990478516, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.0058517795987427235, "Height": 0.014519419521093369, "Left": 0.9455345869064331, "Top": 0.90543133020401}, "Polygon": [{"X": 0.9455345869064331, "Y": 0.9054314494132996}, {"X": 0.9513854384422302, "Y": 0.90543133020401}, {"X": 0.9513863921165466, "Y": 0.9199506640434265}, {"X": 0.9455355405807495, "Y": 0.9199507832527161}]}, "Id": "f1ca2640-848a-4f56-bba3-2f412713ac64", "Relationships": [{"Type": "CHILD", "Ids": ["5c64eda4-23ac-40a9-9678-da63c9549248"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "CLOSE", "Geometry": {"BoundingBox": {"Width": 0.0216877032071352, "Height": 0.014395234175026417, "Left": 0.9663415551185608, "Top": 0.9059008359909058}, "Polygon": [{"X": 0.9663415551185608, "Y": 0.9059011340141296}, {"X": 0.9880282878875732, "Y": 0.9059008359909058}, {"X": 0.9880293011665344, "Y": 0.920295774936676}, {"X": 0.9663425087928772, "Y": 0.9202960729598999}]}, "Id": "e64e03af-2961-4680-b428-6706cbf9bc77", "Relationships": [{"Type": "CHILD", "Ids": ["5813dfb4-81e7-408b-a221-87725b78a1bc"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Interchange", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05637389048933983, "Height": 0.020230678841471672, "Left": 0.008977596648037434, "Top": 0.013423939235508442}, "Polygon": [{"X": 0.008978115394711494, "Y": 0.013423939235508442}, {"X": 0.06535148620605469, "Y": 0.013424364849925041}, {"X": 0.06535107642412186, "Y": 0.03365461900830269}, {"X": 0.008977596648037434, "Y": 0.03365422040224075}]}, "Id": "dc10d77b-97cc-4911-9b83-729ff06ce995"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03429880738258362, "Height": 0.019103432074189186, "Left": 0.06801457703113556, "Top": 0.014098764397203922}, "Polygon": [{"X": 0.0680149644613266, "Y": 0.014098764397203922}, {"X": 0.10231338441371918, "Y": 0.014099023304879665}, {"X": 0.10231306403875351, "Y": 0.03320219740271568}, {"X": 0.06801457703113556, "Y": 0.03320195525884628}]}, "Id": "53ce9842-781e-4a8d-982c-997036b7adba"}, {"BlockType": "WORD", "Confidence": 97.1641845703125, "Text": "(J1)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018750516697764397, "Height": 0.020559191703796387, "Left": 0.10489538311958313, "Top": 0.012685494497418404}, "Polygon": [{"X": 0.10489571839570999, "Y": 0.012685494497418404}, {"X": 0.12364589422941208, "Y": 0.01268563698977232}, {"X": 0.1236455962061882, "Y": 0.03324468806385994}, {"X": 0.10489538311958313, "Y": 0.033244553953409195}]}, "Id": "c93623c7-0d98-4fb4-b509-6ea6206ac8f6"}, {"BlockType": "WORD", "Confidence": 91.55113983154297, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005264751613140106, "Height": 0.013869701884686947, "Left": 0.9390263557434082, "Top": 0.014371751807630062}, "Polygon": [{"X": 0.9390263557434082, "Y": 0.014371751807630062}, {"X": 0.9442902207374573, "Y": 0.014371790923178196}, {"X": 0.9442911148071289, "Y": 0.02824145369231701}, {"X": 0.9390272498130798, "Y": 0.028241414576768875}]}, "Id": "decd42db-18cc-419f-af8a-e8e3bd2aac41"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CLOSE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021151792258024216, "Height": 0.013647965155541897, "Left": 0.9601471424102783, "Top": 0.014819605275988579}, "Polygon": [{"X": 0.9601471424102783, "Y": 0.014819605275988579}, {"X": 0.98129802942276, "Y": 0.014819764532148838}, {"X": 0.9812989830970764, "Y": 0.02846757136285305}, {"X": 0.96014803647995, "Y": 0.028467418625950813}]}, "Id": "5ac79c81-ad98-4692-b4a2-fb5a45eefcf3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BNSF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023997006937861443, "Height": 0.016074610874056816, "Left": 0.039698559790849686, "Top": 0.05876392126083374}, "Polygon": [{"X": 0.039698924869298935, "Y": 0.05876392126083374}, {"X": 0.06369556486606598, "Y": 0.058764077723026276}, {"X": 0.06369523704051971, "Y": 0.0748385339975357}, {"X": 0.039698559790849686, "Y": 0.07483838498592377}]}, "Id": "949aafaf-a380-43bd-b3b1-ef10f3323c49"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Railway", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0359937958419323, "Height": 0.020124511793255806, "Left": 0.06644900143146515, "Top": 0.05792055279016495}, "Polygon": [{"X": 0.06644941121339798, "Y": 0.05792055279016495}, {"X": 0.10244280099868774, "Y": 0.05792078375816345}, {"X": 0.10244246572256088, "Y": 0.0780450627207756}, {"X": 0.06644900143146515, "Y": 0.0780448466539383}]}, "Id": "116e15de-18ed-4620-86c5-060bb4cf208e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042190391570329666, "Height": 0.019249334931373596, "Left": 0.10511361807584763, "Top": 0.05882633849978447}, "Polygon": [{"X": 0.1051139310002327, "Y": 0.05882633849978447}, {"X": 0.1473040133714676, "Y": 0.05882660672068596}, {"X": 0.1473037749528885, "Y": 0.07807566970586777}, {"X": 0.10511361807584763, "Y": 0.07807542383670807}]}, "Id": "2e3f6a8b-fea9-4546-9ea4-d1cc78d2c820"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04884885996580124, "Height": 0.019953958690166473, "Left": 0.03962114825844765, "Top": 0.08628586679697037}, "Polygon": [{"X": 0.03962159901857376, "Y": 0.08628586679697037}, {"X": 0.08847000449895859, "Y": 0.08628614991903305}, {"X": 0.08846964687108994, "Y": 0.10623982548713684}, {"X": 0.03962114825844765, "Y": 0.10623956471681595}]}, "Id": "0b18ac11-c9ab-40e7-baf4-d0f6eea92e55"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048887137323617935, "Height": 0.019874874502420425, "Left": 0.09090311825275421, "Top": 0.08629456162452698}, "Polygon": [{"X": 0.09090347588062286, "Y": 0.08629456162452698}, {"X": 0.13979025185108185, "Y": 0.08629483729600906}, {"X": 0.13978999853134155, "Y": 0.1061694324016571}, {"X": 0.09090311825275421, "Y": 0.10616917908191681}]}, "Id": "383d9561-369d-4fd2-ab59-95a2258cfcef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Record", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0314943864941597, "Height": 0.01717657782137394, "Left": 0.14263878762722015, "Top": 0.0856718048453331}, "Polygon": [{"X": 0.14263901114463806, "Y": 0.0856718048453331}, {"X": 0.17413316667079926, "Y": 0.08567198365926743}, {"X": 0.17413300275802612, "Y": 0.10284838080406189}, {"X": 0.14263878762722015, "Y": 0.10284820944070816}]}, "Id": "10db9318-52e4-41cd-9f38-62a55d0ff5de"}, {"BlockType": "WORD", "Confidence": 99.363037109375, "Text": "YMMU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02829817123711109, "Height": 0.016012979671359062, "Left": 0.03930289298295975, "Top": 0.1425839364528656}, "Polygon": [{"X": 0.039303258061409, "Y": 0.1425839364528656}, {"X": 0.06760106235742569, "Y": 0.14258407056331635}, {"X": 0.06760074198246002, "Y": 0.1585969179868698}, {"X": 0.03930289298295975, "Y": 0.15859681367874146}]}, "Id": "ca048bf8-378e-4674-9e47-13f94d96ac7a"}, {"BlockType": "WORD", "Confidence": 99.677734375, "Text": "405514", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03445858508348465, "Height": 0.016503743827342987, "Left": 0.07027335464954376, "Top": 0.14245669543743134}, "Polygon": [{"X": 0.07027368247509003, "Y": 0.14245669543743134}, {"X": 0.10473193973302841, "Y": 0.14245684444904327}, {"X": 0.10473167151212692, "Y": 0.15896044671535492}, {"X": 0.07027335464954376, "Y": 0.15896029770374298}]}, "Id": "2458db41-be2f-46c7-98f7-4d88660400ab"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025291496887803078, "Height": 0.01640164852142334, "Left": 0.10747604817152023, "Top": 0.14249743521213531}, "Polygon": [{"X": 0.10747631639242172, "Y": 0.14249743521213531}, {"X": 0.13276754319667816, "Y": 0.14249755442142487}, {"X": 0.13276731967926025, "Y": 0.15889908373355865}, {"X": 0.10747604817152023, "Y": 0.1588989943265915}]}, "Id": "b1084848-427b-4ef9-93b3-1410f7168988"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FLAT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022485272958874702, "Height": 0.01644585095345974, "Left": 0.03952128440141678, "Top": 0.17046956717967987}, "Polygon": [{"X": 0.039521656930446625, "Y": 0.17046956717967987}, {"X": 0.06200655549764633, "Y": 0.17046964168548584}, {"X": 0.06200621649622917, "Y": 0.18691541254520416}, {"X": 0.03952128440141678, "Y": 0.1869153380393982}]}, "Id": "567bcae1-261e-4e68-8225-02c9b5415717"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1234", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022802939638495445, "Height": 0.016550764441490173, "Left": 0.06499705463647842, "Top": 0.17037729918956757}, "Polygon": [{"X": 0.06499738991260529, "Y": 0.17037729918956757}, {"X": 0.08779998868703842, "Y": 0.17037737369537354}, {"X": 0.08779969066381454, "Y": 0.18692806363105774}, {"X": 0.06499705463647842, "Y": 0.18692798912525177}]}, "Id": "c32fe38c-5374-45fd-afba-4bc8f7ede97e"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Outgate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03955237939953804, "Height": 0.02025277353823185, "Left": 0.03942124918103218, "Top": 0.22617925703525543}, "Polygon": [{"X": 0.03942170739173889, "Y": 0.22617925703525543}, {"X": 0.07897362858057022, "Y": 0.2261793464422226}, {"X": 0.07897324115037918, "Y": 0.24643203616142273}, {"X": 0.03942124918103218, "Y": 0.24643196165561676}]}, "Id": "ba26c4ba-55ff-4660-aad1-4e0fc45fbe85"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "LOGPARCHI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05428289249539375, "Height": 0.016945064067840576, "Left": 0.08207935094833374, "Top": 0.22622369229793549}, "Polygon": [{"X": 0.08207967132329941, "Y": 0.22622369229793549}, {"X": 0.1363622397184372, "Y": 0.22622382640838623}, {"X": 0.1363620162010193, "Y": 0.24316875636577606}, {"X": 0.08207935094833374, "Y": 0.2431686520576477}]}, "Id": "b0bc2c86-414f-4fc9-99ec-e639536cf7f2"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009822641499340534, "Height": 0.01693997159600258, "Left": 0.13915380835533142, "Top": 0.2263292521238327}, "Polygon": [{"X": 0.13915403187274933, "Y": 0.2263292521238327}, {"X": 0.14897644519805908, "Y": 0.2263292670249939}, {"X": 0.14897623658180237, "Y": 0.24326921999454498}, {"X": 0.13915380835533142, "Y": 0.2432692050933838}]}, "Id": "f046dad8-74fa-41f4-8ef4-fdba293a3ccf"}, {"BlockType": "WORD", "Confidence": 99.63628387451172, "Text": "07/07/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057170361280441284, "Height": 0.020528560504317284, "Left": 0.1512848138809204, "Top": 0.22523342072963715}, "Polygon": [{"X": 0.1512850672006607, "Y": 0.22523342072963715}, {"X": 0.2084551751613617, "Y": 0.2252335548400879}, {"X": 0.20845504105091095, "Y": 0.24576199054718018}, {"X": 0.1512848138809204, "Y": 0.24576187133789062}]}, "Id": "57953be6-f3b5-48e7-9dd4-643238e13c1d"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "2315", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022782817482948303, "Height": 0.01715719699859619, "Left": 0.21153727173805237, "Top": 0.22621287405490875}, "Polygon": [{"X": 0.21153739094734192, "Y": 0.22621287405490875}, {"X": 0.23432008922100067, "Y": 0.22621293365955353}, {"X": 0.2343200296163559, "Y": 0.24337007105350494}, {"X": 0.21153727173805237, "Y": 0.24337002635002136}]}, "Id": "e152d663-0d42-4954-a786-14844052a38e"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Patron:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033434782177209854, "Height": 0.01699364185333252, "Left": 0.0396168977022171, "Top": 0.25424259901046753}, "Polygon": [{"X": 0.03961728513240814, "Y": 0.25424259901046753}, {"X": 0.07305168360471725, "Y": 0.2542426288127899}, {"X": 0.0730513483285904, "Y": 0.27123621106147766}, {"X": 0.0396168977022171, "Y": 0.2712361812591553}]}, "Id": "c08508d9-3eff-4152-9b89-a6ed3f3a9472"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "YANGMINAMERI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07538194209337234, "Height": 0.016471678391098976, "Left": 0.07584549486637115, "Top": 0.2545066475868225}, "Polygon": [{"X": 0.07584581524133682, "Y": 0.2545066475868225}, {"X": 0.1512274295091629, "Y": 0.25450676679611206}, {"X": 0.15122723579406738, "Y": 0.27097830176353455}, {"X": 0.07584549486637115, "Y": 0.2709782123565674}]}, "Id": "24d05012-9111-4b01-8982-3079c86ecbd6"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "Trucker:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03849977254867554, "Height": 0.017740022391080856, "Left": 0.03919679671525955, "Top": 0.2813551425933838}, "Polygon": [{"X": 0.039197199046611786, "Y": 0.2813551425933838}, {"X": 0.07769656926393509, "Y": 0.2813551723957062}, {"X": 0.07769622653722763, "Y": 0.29909515380859375}, {"X": 0.03919679671525955, "Y": 0.29909515380859375}]}, "Id": "983bc4e8-e6fa-4048-85ba-655ee238d686"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "DPBN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025550734251737595, "Height": 0.01636418327689171, "Left": 0.08062437176704407, "Top": 0.282556414604187}, "Polygon": [{"X": 0.08062467724084854, "Y": 0.282556414604187}, {"X": 0.10617510229349136, "Y": 0.2825564444065094}, {"X": 0.10617483407258987, "Y": 0.298920601606369}, {"X": 0.08062437176704407, "Y": 0.298920601606369}]}, "Id": "8731f66b-7c6e-478f-960f-531ab410c754"}, {"BlockType": "WORD", "Confidence": 99.69486999511719, "Text": "IC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010743687860667706, "Height": 0.016285406425595284, "Left": 0.10889671742916107, "Top": 0.2824781537055969}, "Polygon": [{"X": 0.10889697819948196, "Y": 0.2824781537055969}, {"X": 0.11964040249586105, "Y": 0.2824781835079193}, {"X": 0.11964015662670135, "Y": 0.29876357316970825}, {"X": 0.10889671742916107, "Y": 0.29876357316970825}]}, "Id": "60485aeb-6205-4bd6-847c-93d997248e71"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014248416759073734, "Height": 0.016389360651373863, "Left": 0.12185447663068771, "Top": 0.2824810743331909}, "Polygon": [{"X": 0.12185472249984741, "Y": 0.2824810743331909}, {"X": 0.13610289990901947, "Y": 0.2824811041355133}, {"X": 0.13610267639160156, "Y": 0.2988704442977905}, {"X": 0.12185447663068771, "Y": 0.2988704442977905}]}, "Id": "86db6845-1b6b-454c-bd33-05eb0e56c661"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037191372364759445, "Height": 0.0168240275233984, "Left": 0.03959345817565918, "Top": 0.3102814257144928}, "Polygon": [{"X": 0.03959384188055992, "Y": 0.3102814257144928}, {"X": 0.07678482681512833, "Y": 0.3102814257144928}, {"X": 0.07678450644016266, "Y": 0.32710546255111694}, {"X": 0.03959345817565918, "Y": 0.32710546255111694}]}, "Id": "20e198c1-41c7-44eb-9e5f-3365c18852c7"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "5177", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023047739639878273, "Height": 0.017153551802039146, "Left": 0.07958915829658508, "Top": 0.31008943915367126}, "Polygon": [{"X": 0.07958947867155075, "Y": 0.31008943915367126}, {"X": 0.1026368960738182, "Y": 0.31008943915367126}, {"X": 0.10263661295175552, "Y": 0.32724300026893616}, {"X": 0.07958915829658508, "Y": 0.32724300026893616}]}, "Id": "61c2c8ba-4a1d-4d85-a640-9145fe656941"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02266412228345871, "Height": 0.01776263304054737, "Left": 0.03942125663161278, "Top": 0.3375374972820282}, "Polygon": [{"X": 0.03942165896296501, "Y": 0.3375374972820282}, {"X": 0.06208537891507149, "Y": 0.3375374972820282}, {"X": 0.06208501383662224, "Y": 0.35530009865760803}, {"X": 0.03942125663161278, "Y": 0.3553001284599304}]}, "Id": "682dfaa8-4ac2-4de5-b0f8-f17393c8ee3d"}, {"BlockType": "WORD", "Confidence": 99.20500183105469, "Text": "YMAT715808", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06011665612459183, "Height": 0.017146751284599304, "Left": 0.06477651000022888, "Top": 0.3380031883716583}, "Polygon": [{"X": 0.06477686017751694, "Y": 0.3380031883716583}, {"X": 0.12489316612482071, "Y": 0.3380031883716583}, {"X": 0.12489291280508041, "Y": 0.35514986515045166}, {"X": 0.06477651000022888, "Y": 0.35514992475509644}]}, "Id": "a2993626-6c0a-46a7-b43f-af0156b26448"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Cust:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02312466688454151, "Height": 0.01719094254076481, "Left": 0.03951337933540344, "Top": 0.36621230840682983}, "Polygon": [{"X": 0.03951377049088478, "Y": 0.3662123382091522}, {"X": 0.0626380443572998, "Y": 0.36621230840682983}, {"X": 0.06263769418001175, "Y": 0.3834032118320465}, {"X": 0.03951337933540344, "Y": 0.3834032714366913}]}, "Id": "2a45f942-78c6-4581-a98f-1917daf96b05"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "YANGMINMARLI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07535448670387268, "Height": 0.016952941194176674, "Left": 0.06561083346605301, "Top": 0.36627906560897827}, "Polygon": [{"X": 0.06561117619276047, "Y": 0.36627915501594543}, {"X": 0.1409653127193451, "Y": 0.36627906560897827}, {"X": 0.14096510410308838, "Y": 0.38323190808296204}, {"X": 0.06561083346605301, "Y": 0.3832320272922516}]}, "Id": "c521498b-d97b-4af0-820f-8da76c629cf4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Release", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03561282530426979, "Height": 0.017561381682753563, "Left": 0.039571113884449005, "Top": 0.393629252910614}, "Polygon": [{"X": 0.03957151249051094, "Y": 0.3936293125152588}, {"X": 0.0751839354634285, "Y": 0.393629252910614}, {"X": 0.07518360018730164, "Y": 0.41119053959846497}, {"X": 0.039571113884449005, "Y": 0.41119062900543213}]}, "Id": "77a73919-b212-4858-88ea-5df78fc20dde"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014213226735591888, "Height": 0.016521677374839783, "Left": 0.07771811634302139, "Top": 0.3944394290447235}, "Polygon": [{"X": 0.07771843671798706, "Y": 0.3944394588470459}, {"X": 0.09193134307861328, "Y": 0.3944394290447235}, {"X": 0.09193105250597, "Y": 0.4109610617160797}, {"X": 0.07771811634302139, "Y": 0.4109610915184021}]}, "Id": "c4d6ad7a-dd94-431a-91c2-b8ca9b5f7e54"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "6555CC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03494023159146309, "Height": 0.016763469204306602, "Left": 0.09497341513633728, "Top": 0.39442411065101624}, "Polygon": [{"X": 0.09497370570898056, "Y": 0.394424170255661}, {"X": 0.12991364300251007, "Y": 0.39442411065101624}, {"X": 0.12991340458393097, "Y": 0.4111874997615814}, {"X": 0.09497341513633728, "Y": 0.4111875891685486}]}, "Id": "889288fa-9955-4586-9735-87e8b5278ba0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025923579931259155, "Height": 0.017117556184530258, "Left": 0.03950526565313339, "Top": 0.42200329899787903}, "Polygon": [{"X": 0.03950565308332443, "Y": 0.4220033586025238}, {"X": 0.06542884558439255, "Y": 0.42200329899787903}, {"X": 0.06542849540710449, "Y": 0.4391207993030548}, {"X": 0.03950526565313339, "Y": 0.4391208589076996}]}, "Id": "101df188-2ed0-4bac-994d-bf49263c005c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "WGT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024290017783641815, "Height": 0.0169710423797369, "Left": 0.06794095784425735, "Top": 0.4219624102115631}, "Polygon": [{"X": 0.06794130057096481, "Y": 0.4219624698162079}, {"X": 0.09223097562789917, "Y": 0.4219624102115631}, {"X": 0.09223067760467529, "Y": 0.438933402299881}, {"X": 0.06794095784425735, "Y": 0.43893346190452576}]}, "Id": "d19f52b0-d437-4c86-983a-6ca614516eb1"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "51762", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028754981234669685, "Height": 0.017522579059004784, "Left": 0.09543342143297195, "Top": 0.42174869775772095}, "Polygon": [{"X": 0.09543372690677643, "Y": 0.4217487573623657}, {"X": 0.12418840080499649, "Y": 0.42174869775772095}, {"X": 0.1241881474852562, "Y": 0.4392711818218231}, {"X": 0.09543342143297195, "Y": 0.4392712712287903}]}, "Id": "ce81db72-3779-4033-93a5-af394a7c8cee"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018848495557904243, "Height": 0.01724087819457054, "Left": 0.03957679495215416, "Top": 0.44998228549957275}, "Polygon": [{"X": 0.0395771861076355, "Y": 0.44998234510421753}, {"X": 0.05842529237270355, "Y": 0.44998228549957275}, {"X": 0.0584249310195446, "Y": 0.4672231078147888}, {"X": 0.03957679495215416, "Y": 0.4672231674194336}]}, "Id": "86512501-0c78-4da8-ad8b-a72aa4aabde5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "cannot", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03098691999912262, "Height": 0.01624262146651745, "Left": 0.06117555499076843, "Top": 0.45103180408477783}, "Polygon": [{"X": 0.061175890266895294, "Y": 0.4510319232940674}, {"X": 0.09216247498989105, "Y": 0.45103180408477783}, {"X": 0.09216219186782837, "Y": 0.46727433800697327}, {"X": 0.06117555499076843, "Y": 0.46727442741394043}]}, "Id": "de7c9790-8725-4f61-a4cd-c6c6751a36de"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "return", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028673816472291946, "Height": 0.016330914571881294, "Left": 0.09487973898649216, "Top": 0.45085495710372925}, "Polygon": [{"X": 0.09488002210855484, "Y": 0.4508550465106964}, {"X": 0.1235535517334938, "Y": 0.45085495710372925}, {"X": 0.1235533133149147, "Y": 0.46718576550483704}, {"X": 0.09487973898649216, "Y": 0.4671858549118042}]}, "Id": "30787ad1-87a1-4ef6-baab-b18ad21c5f4c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028692392632365227, "Height": 0.01972283609211445, "Left": 0.12637846171855927, "Top": 0.4507175385951996}, "Polygon": [{"X": 0.12637874484062195, "Y": 0.45071762800216675}, {"X": 0.15507085621356964, "Y": 0.4507175385951996}, {"X": 0.15507061779499054, "Y": 0.4704402685165405}, {"X": 0.12637846171855927, "Y": 0.4704403877258301}]}, "Id": "afb0b18f-b920-4485-aa0c-5cd01731b53f"}, {"BlockType": "WORD", "Confidence": 99.57927703857422, "Text": "w/out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02883187308907509, "Height": 0.02060944214463234, "Left": 0.15737804770469666, "Top": 0.4491330087184906}, "Polygon": [{"X": 0.15737828612327576, "Y": 0.44913309812545776}, {"X": 0.18620991706848145, "Y": 0.4491330087184906}, {"X": 0.18620973825454712, "Y": 0.4697423577308655}, {"X": 0.15737804770469666, "Y": 0.46974244713783264}]}, "Id": "a96e1bc9-0bf3-4e47-9c38-25d8d2de8a05"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "waybill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03296549245715141, "Height": 0.02133619412779808, "Left": 0.18886031210422516, "Top": 0.4489491581916809}, "Polygon": [{"X": 0.18886049091815948, "Y": 0.44894924759864807}, {"X": 0.22182579338550568, "Y": 0.4489491581916809}, {"X": 0.22182568907737732, "Y": 0.47028523683547974}, {"X": 0.18886031210422516, "Y": 0.4702853560447693}]}, "Id": "34d1ea60-2755-4ec6-bc95-e0ef4a040e58"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Damage:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04004255682229996, "Height": 0.019995776936411858, "Left": 0.03969172015786171, "Top": 0.48507174849510193}, "Polygon": [{"X": 0.03969217464327812, "Y": 0.48507192730903625}, {"X": 0.07973427325487137, "Y": 0.48507174849510193}, {"X": 0.07973390072584152, "Y": 0.5050673484802246}, {"X": 0.03969172015786171, "Y": 0.5050675272941589}]}, "Id": "3503e1fd-806c-45b5-8307-90f9ad1c2326"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PHOTOGRAPHIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07441538572311401, "Height": 0.017047276720404625, "Left": 0.03966529667377472, "Top": 0.5193352103233337}, "Polygon": [{"X": 0.03966568410396576, "Y": 0.5193355679512024}, {"X": 0.11408068239688873, "Y": 0.5193352103233337}, {"X": 0.11408041417598724, "Y": 0.5363820791244507}, {"X": 0.03966529667377472, "Y": 0.5363824963569641}]}, "Id": "e31563e5-8c79-48c3-a108-11d6b5cdb24f"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "INSPECTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05868450552225113, "Height": 0.016714291647076607, "Left": 0.11663658916950226, "Top": 0.5194926857948303}, "Polygon": [{"X": 0.11663684248924255, "Y": 0.5194929838180542}, {"X": 0.17532110214233398, "Y": 0.5194926857948303}, {"X": 0.17532093822956085, "Y": 0.5362066626548767}, {"X": 0.11663658916950226, "Y": 0.5362069606781006}]}, "Id": "b8b6cc93-a369-4322-8d4b-95095fe0e49b"}, {"BlockType": "WORD", "Confidence": 87.05426788330078, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0040960111655294895, "Height": 0.005050892475992441, "Left": 0.1769694685935974, "Top": 0.5259955525398254}, "Polygon": [{"X": 0.176969513297081, "Y": 0.5259956121444702}, {"X": 0.18106546998023987, "Y": 0.5259955525398254}, {"X": 0.1810654252767563, "Y": 0.531046450138092}, {"X": 0.1769694685935974, "Y": 0.531046450138092}]}, "Id": "fe49222c-d43e-4603-9937-9aece4955f60"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04848421737551689, "Height": 0.021076848730444908, "Left": 0.18423259258270264, "Top": 0.5190017819404602}, "Polygon": [{"X": 0.18423278629779816, "Y": 0.5190020203590393}, {"X": 0.23271681368350983, "Y": 0.5190017819404602}, {"X": 0.23271672427654266, "Y": 0.5400784015655518}, {"X": 0.18423259258270264, "Y": 0.5400786399841309}]}, "Id": "926b8d49-53d5-42e6-9485-1bff89cd4d9d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "defects", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03354039788246155, "Height": 0.017845677211880684, "Left": 0.23539026081562042, "Top": 0.5185544490814209}, "Polygon": [{"X": 0.2353903204202652, "Y": 0.5185545682907104}, {"X": 0.2689306437969208, "Y": 0.5185544490814209}, {"X": 0.2689306437969208, "Y": 0.5363999009132385}, {"X": 0.23539026081562042, "Y": 0.5364000797271729}]}, "Id": "ed23b3b9-36d6-42c9-8efa-a74731c07393"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015470431186258793, "Height": 0.013420374132692814, "Left": 0.27124637365341187, "Top": 0.5230730175971985}, "Polygon": [{"X": 0.27124640345573425, "Y": 0.523073136806488}, {"X": 0.2867167890071869, "Y": 0.5230730175971985}, {"X": 0.2867168188095093, "Y": 0.5364933013916016}, {"X": 0.27124637365341187, "Y": 0.5364934206008911}]}, "Id": "4fa711b9-2972-40d9-9c00-65f7812c06e6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04031157121062279, "Height": 0.021334685385227203, "Left": 0.03956186771392822, "Top": 0.5464051961898804}, "Polygon": [{"X": 0.03956235200166702, "Y": 0.5464054346084595}, {"X": 0.07987344264984131, "Y": 0.5464051961898804}, {"X": 0.07987304031848907, "Y": 0.5677396059036255}, {"X": 0.03956186771392822, "Y": 0.5677398443222046}]}, "Id": "998fcd2c-bd73-4112-8fae-b51c931d078e"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011531590484082699, "Height": 0.013540244661271572, "Left": 0.08257528394460678, "Top": 0.5509831309318542}, "Polygon": [{"X": 0.08257553726434708, "Y": 0.550983190536499}, {"X": 0.09410687536001205, "Y": 0.5509831309318542}, {"X": 0.09410664439201355, "Y": 0.5645232796669006}, {"X": 0.08257528394460678, "Y": 0.5645233392715454}]}, "Id": "5975d263-fafd-48ba-85fe-ec0739381cb6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04018139839172363, "Height": 0.01725723408162594, "Left": 0.09697222709655762, "Top": 0.5469871759414673}, "Polygon": [{"X": 0.0969725251197815, "Y": 0.5469873547554016}, {"X": 0.13715362548828125, "Y": 0.5469871759414673}, {"X": 0.13715338706970215, "Y": 0.5642441511154175}, {"X": 0.09697222709655762, "Y": 0.5642443895339966}]}, "Id": "4f081612-4b70-4a5d-8b53-570babbf44f1"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "images.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03534180298447609, "Height": 0.020515868440270424, "Left": 0.14009271562099457, "Top": 0.5472301244735718}, "Polygon": [{"X": 0.14009298384189606, "Y": 0.5472303628921509}, {"X": 0.17543451488018036, "Y": 0.5472301244735718}, {"X": 0.17543430626392365, "Y": 0.5677458047866821}, {"X": 0.14009271562099457, "Y": 0.5677460432052612}]}, "Id": "53c0773c-945d-4010-8453-d5b423402bd2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029502198100090027, "Height": 0.01744975708425045, "Left": 0.039174024015665054, "Top": 0.5816837549209595}, "Polygon": [{"X": 0.03917441889643669, "Y": 0.5816839337348938}, {"X": 0.06867621839046478, "Y": 0.5816837549209595}, {"X": 0.06867587566375732, "Y": 0.599133312702179}, {"X": 0.039174024015665054, "Y": 0.5991334915161133}]}, "Id": "bddaf91c-5f6b-41e5-abbb-61c2c42600c3"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "Nme:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023903051391243935, "Height": 0.016929171979427338, "Left": 0.07159692794084549, "Top": 0.5821470022201538}, "Polygon": [{"X": 0.07159726321697235, "Y": 0.5821471810340881}, {"X": 0.09549998492002487, "Y": 0.5821470022201538}, {"X": 0.095499686896801, "Y": 0.5990760326385498}, {"X": 0.07159692794084549, "Y": 0.5990762114524841}]}, "Id": "560b9bdf-55d5-410f-90dc-ee8cf8c13394"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "YM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014058067463338375, "Height": 0.016396597027778625, "Left": 0.09818055480718613, "Top": 0.5822556614875793}, "Polygon": [{"X": 0.09818083792924881, "Y": 0.5822557806968689}, {"X": 0.11223862320184708, "Y": 0.5822556614875793}, {"X": 0.11223836243152618, "Y": 0.598652184009552}, {"X": 0.09818055480718613, "Y": 0.5986523032188416}]}, "Id": "7ca3f2cd-b702-481b-a3da-16c2bfe8f50f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "UNANIMITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05457087606191635, "Height": 0.016054444015026093, "Left": 0.11497124284505844, "Top": 0.5826635360717773}, "Polygon": [{"X": 0.11497149616479874, "Y": 0.582663893699646}, {"X": 0.1695421189069748, "Y": 0.5826635360717773}, {"X": 0.16954195499420166, "Y": 0.5987175703048706}, {"X": 0.11497124284505844, "Y": 0.598717987537384}]}, "Id": "c497af5e-7390-4f53-b346-a99c825e1bb9"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Nbr:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019548023119568825, "Height": 0.01748790591955185, "Left": 0.039615366607904434, "Top": 0.6093540191650391}, "Polygon": [{"X": 0.03961576148867607, "Y": 0.6093541383743286}, {"X": 0.05916338786482811, "Y": 0.6093540191650391}, {"X": 0.05916302278637886, "Y": 0.6268417239189148}, {"X": 0.039615366607904434, "Y": 0.6268419027328491}]}, "Id": "8337555e-c1bd-407d-81e6-2f1a8e252522"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "080E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0227655116468668, "Height": 0.017026757821440697, "Left": 0.06212751939892769, "Top": 0.6100413203239441}, "Polygon": [{"X": 0.062127869576215744, "Y": 0.6100414991378784}, {"X": 0.08489303290843964, "Y": 0.6100413203239441}, {"X": 0.08489271998405457, "Y": 0.6270679235458374}, {"X": 0.06212751939892769, "Y": 0.6270681023597717}]}, "Id": "08572a3b-905d-48ab-a22d-2a5ffe675453"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Bkg:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02011938765645027, "Height": 0.020829249173402786, "Left": 0.08787056058645248, "Top": 0.609186053276062}, "Polygon": [{"X": 0.08787093311548233, "Y": 0.6091861724853516}, {"X": 0.10798994451761246, "Y": 0.609186053276062}, {"X": 0.1079896092414856, "Y": 0.6300151348114014}, {"X": 0.08787056058645248, "Y": 0.6300153136253357}]}, "Id": "285fafda-2538-43be-b0c7-be6bebdfab43"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Intermodal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05149143934249878, "Height": 0.017731448635458946, "Left": 0.03931829333305359, "Top": 0.6438378691673279}, "Polygon": [{"X": 0.03931869566440582, "Y": 0.6438382863998413}, {"X": 0.09080973267555237, "Y": 0.6438378691673279}, {"X": 0.0908094123005867, "Y": 0.6615688800811768}, {"X": 0.03931829333305359, "Y": 0.6615692973136902}]}, "Id": "286f8dc6-60ad-487f-b23a-ef9fb987c91c"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Safe", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020313790068030357, "Height": 0.017218943685293198, "Left": 0.09344752877950668, "Top": 0.6441889405250549}, "Polygon": [{"X": 0.09344783425331116, "Y": 0.6441891193389893}, {"X": 0.11376132071018219, "Y": 0.6441889405250549}, {"X": 0.1137610524892807, "Y": 0.6614077091217041}, {"X": 0.09344752877950668, "Y": 0.6614078879356384}]}, "Id": "14cb8817-40f9-4c19-ba72-247169382065"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0447438545525074, "Height": 0.01724981516599655, "Left": 0.1161850094795227, "Top": 0.6443281173706055}, "Polygon": [{"X": 0.1161852777004242, "Y": 0.6443284749984741}, {"X": 0.1609288603067398, "Y": 0.6443281173706055}, {"X": 0.1609286665916443, "Y": 0.6615775227546692}, {"X": 0.1161850094795227, "Y": 0.6615778803825378}]}, "Id": "87414b1f-0073-4905-a6f0-d3f5ce39e074"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Certification", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055998217314481735, "Height": 0.018120035529136658, "Left": 0.16324114799499512, "Top": 0.6435997486114502}, "Polygon": [{"X": 0.16324134171009064, "Y": 0.6436002254486084}, {"X": 0.21923936903476715, "Y": 0.6435997486114502}, {"X": 0.2192392647266388, "Y": 0.6617193222045898}, {"X": 0.16324114799499512, "Y": 0.661719799041748}]}, "Id": "5fb67e02-c5c2-43aa-9a7e-08e9d693fe0e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Cargo", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026589160785079002, "Height": 0.020147884264588356, "Left": 0.03954499587416649, "Top": 0.6725696921348572}, "Polygon": [{"X": 0.0395454503595829, "Y": 0.6725699305534363}, {"X": 0.06613415479660034, "Y": 0.6725696921348572}, {"X": 0.06613374501466751, "Y": 0.6927173137664795}, {"X": 0.03954499587416649, "Y": 0.6927175521850586}]}, "Id": "f1bad6e5-82f2-40b4-9e26-feb862c45a28"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03559133782982826, "Height": 0.020785696804523468, "Left": 0.06858084350824356, "Top": 0.6721512079238892}, "Polygon": [{"X": 0.06858125329017639, "Y": 0.6721515655517578}, {"X": 0.10417217761278152, "Y": 0.6721512079238892}, {"X": 0.10417183488607407, "Y": 0.6929365992546082}, {"X": 0.06858084350824356, "Y": 0.692936897277832}]}, "Id": "428cea97-54e7-4431-aca7-3ee2613b4375"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "43162", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02865324169397354, "Height": 0.016955798491835594, "Left": 0.10727158188819885, "Top": 0.6727917790412903}, "Polygon": [{"X": 0.10727185755968094, "Y": 0.6727920770645142}, {"X": 0.1359248161315918, "Y": 0.6727917790412903}, {"X": 0.1359245926141739, "Y": 0.6897473335266113}, {"X": 0.10727158188819885, "Y": 0.6897476315498352}]}, "Id": "13f60215-50a3-4887-926c-5edb44bd1364"}, {"BlockType": "WORD", "Confidence": 98.52678680419922, "Text": "lbs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01351008377969265, "Height": 0.017808418720960617, "Left": 0.13886159658432007, "Top": 0.6713494658470154}, "Polygon": [{"X": 0.13886182010173798, "Y": 0.6713495850563049}, {"X": 0.15237167477607727, "Y": 0.6713494658470154}, {"X": 0.15237146615982056, "Y": 0.6891577243804932}, {"X": 0.13886159658432007, "Y": 0.6891578435897827}]}, "Id": "f7238864-d97e-4c81-a26b-856ea5a1d9eb"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030677277594804764, "Height": 0.02093246951699257, "Left": 0.03949768468737602, "Top": 0.6994001865386963}, "Polygon": [{"X": 0.039498161524534225, "Y": 0.6994004249572754}, {"X": 0.07017496228218079, "Y": 0.6994001865386963}, {"X": 0.07017455250024796, "Y": 0.7203323245048523}, {"X": 0.03949768468737602, "Y": 0.7203326225280762}]}, "Id": "bd4513b3-d73d-48b2-bf47-d732c3a6541c"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Party:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02719869464635849, "Height": 0.01990506239235401, "Left": 0.07289200276136398, "Top": 0.7005689740180969}, "Polygon": [{"X": 0.07289239764213562, "Y": 0.700569212436676}, {"X": 0.10009069740772247, "Y": 0.7005689740180969}, {"X": 0.10009036213159561, "Y": 0.7204737663269043}, {"X": 0.07289200276136398, "Y": 0.7204740643501282}]}, "Id": "ed4ea109-91b2-4b82-97ee-f3404270ddb8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "YANG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02615789882838726, "Height": 0.016627760604023933, "Left": 0.10274706780910492, "Top": 0.7006412148475647}, "Polygon": [{"X": 0.102747343480587, "Y": 0.7006414532661438}, {"X": 0.12890496850013733, "Y": 0.7006412148475647}, {"X": 0.12890473008155823, "Y": 0.717268705368042}, {"X": 0.10274706780910492, "Y": 0.7172690033912659}]}, "Id": "5190d5c5-0c0c-458e-89e4-ea10ac564a31"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "MING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0254342220723629, "Height": 0.016840659081935883, "Left": 0.13179780542850494, "Top": 0.7006378769874573}, "Polygon": [{"X": 0.13179802894592285, "Y": 0.7006381154060364}, {"X": 0.15723201632499695, "Y": 0.7006378769874573}, {"X": 0.15723182260990143, "Y": 0.717478334903717}, {"X": 0.13179780542850494, "Y": 0.7174785733222961}]}, "Id": "22ead54a-ce31-4025-a14a-059fc8ccc6da"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "NO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01403701864182949, "Height": 0.016607685014605522, "Left": 0.1598779410123825, "Top": 0.7006962299346924}, "Polygon": [{"X": 0.15987813472747803, "Y": 0.7006963491439819}, {"X": 0.17391495406627655, "Y": 0.7006962299346924}, {"X": 0.17391479015350342, "Y": 0.7173038125038147}, {"X": 0.1598779410123825, "Y": 0.7173039317131042}]}, "Id": "3adea980-abb0-4a5f-9f50-56a77d211aa4"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030753053724765778, "Height": 0.02074040099978447, "Left": 0.03949036821722984, "Top": 0.7276248931884766}, "Polygon": [{"X": 0.03949084132909775, "Y": 0.7276251912117004}, {"X": 0.07024341821670532, "Y": 0.7276248931884766}, {"X": 0.07024300843477249, "Y": 0.7483649849891663}, {"X": 0.03949036821722984, "Y": 0.7483652830123901}]}, "Id": "b263907c-f98c-423e-8100-a1cf3fa0cec6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024088336154818535, "Height": 0.016977258026599884, "Left": 0.0728967934846878, "Top": 0.7288756966590881}, "Polygon": [{"X": 0.07289712876081467, "Y": 0.7288759350776672}, {"X": 0.09698513150215149, "Y": 0.7288756966590881}, {"X": 0.09698484092950821, "Y": 0.7458527088165283}, {"X": 0.0728967934846878, "Y": 0.7458529472351074}]}, "Id": "41051e58-39a5-46a6-bfca-cb49132f00ef"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "06/23/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05740126594901085, "Height": 0.021012267097830772, "Left": 0.09981732815504074, "Top": 0.7274128794670105}, "Polygon": [{"X": 0.0998176857829094, "Y": 0.7274134755134583}, {"X": 0.1572185903787613, "Y": 0.7274128794670105}, {"X": 0.1572183519601822, "Y": 0.7484245300292969}, {"X": 0.09981732815504074, "Y": 0.7484251260757446}]}, "Id": "b9f34822-a947-4a21-b494-e4a4532b8244"}, {"BlockType": "WORD", "Confidence": 99.48181915283203, "Text": "Commodity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054863978177309036, "Height": 0.021214043721556664, "Left": 0.03933687508106232, "Top": 0.7556855082511902}, "Polygon": [{"X": 0.03933735936880112, "Y": 0.7556861042976379}, {"X": 0.09420085698366165, "Y": 0.7556855082511902}, {"X": 0.0942004844546318, "Y": 0.776898980140686}, {"X": 0.03933687508106232, "Y": 0.7768995761871338}]}, "Id": "656a695a-369b-4e32-8f9d-c210047807fc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PLATES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03413344919681549, "Height": 0.01648515835404396, "Left": 0.09726005792617798, "Top": 0.7569363117218018}, "Polygon": [{"X": 0.09726034104824066, "Y": 0.7569366693496704}, {"X": 0.13139350712299347, "Y": 0.7569363117218018}, {"X": 0.13139328360557556, "Y": 0.7734211087226868}, {"X": 0.09726005792617798, "Y": 0.7734214663505554}]}, "Id": "00d22f6e-d759-43a4-9d45-a69e0df2a19e"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "NON-C", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03051406517624855, "Height": 0.01705063320696354, "Left": 0.1341351419687271, "Top": 0.7566030025482178}, "Polygon": [{"X": 0.13413536548614502, "Y": 0.7566033601760864}, {"X": 0.16464920341968536, "Y": 0.7566030025482178}, {"X": 0.16464902460575104, "Y": 0.7736533284187317}, {"X": 0.1341351419687271, "Y": 0.7736536860466003}]}, "Id": "f2642114-d5d1-4eea-a479-380cf28cfe1a"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Conversion", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05089927464723587, "Height": 0.017334062606096268, "Left": 0.039346013218164444, "Top": 0.784480094909668}, "Polygon": [{"X": 0.03934640809893608, "Y": 0.7844806909561157}, {"X": 0.09024528414011002, "Y": 0.784480094909668}, {"X": 0.09024497866630554, "Y": 0.8018136024475098}, {"X": 0.039346013218164444, "Y": 0.8018141984939575}]}, "Id": "aa8e1ffb-01e5-4721-8ea0-c8b4d430a729"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Party:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027146782726049423, "Height": 0.020127763971686363, "Left": 0.09327590465545654, "Top": 0.7845597863197327}, "Polygon": [{"X": 0.0932762548327446, "Y": 0.7845600843429565}, {"X": 0.12042268365621567, "Y": 0.7845597863197327}, {"X": 0.12042238563299179, "Y": 0.8046872019767761}, {"X": 0.09327590465545654, "Y": 0.8046875}]}, "Id": "f5f13d59-53be-4bc1-af0f-2b2f9485621d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BNSF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02394932322204113, "Height": 0.01633761078119278, "Left": 0.03965958580374718, "Top": 0.8130542635917664}, "Polygon": [{"X": 0.039659954607486725, "Y": 0.8130545616149902}, {"X": 0.06360890716314316, "Y": 0.8130542635917664}, {"X": 0.0636085793375969, "Y": 0.829391598701477}, {"X": 0.03965958580374718, "Y": 0.8293918967247009}]}, "Id": "d67d5892-22ee-4042-a8db-e8dd9ca3ed5a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RAILWAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04381127282977104, "Height": 0.016110850498080254, "Left": 0.0663815438747406, "Top": 0.8131080865859985}, "Polygon": [{"X": 0.06638187170028687, "Y": 0.8131086230278015}, {"X": 0.11019282042980194, "Y": 0.8131080865859985}, {"X": 0.11019255965948105, "Y": 0.8292183876037598}, {"X": 0.0663815438747406, "Y": 0.8292189240455627}]}, "Id": "83c1338e-1d91-469d-b175-cb77432d4cdd"}, {"BlockType": "WORD", "Confidence": 97.67916870117188, "Text": "CO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012690878473222256, "Height": 0.016253473237156868, "Left": 0.11279144883155823, "Top": 0.8130185008049011}, "Polygon": [{"X": 0.11279170960187912, "Y": 0.8130186796188354}, {"X": 0.12548233568668365, "Y": 0.8130185008049011}, {"X": 0.12548209726810455, "Y": 0.8292717933654785}, {"X": 0.11279144883155823, "Y": 0.8292719721794128}]}, "Id": "a12a89eb-b7a9-4713-90bb-11f844762cbd"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Conversion", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05066140368580818, "Height": 0.017047833651304245, "Left": 0.03952660411596298, "Top": 0.8406391143798828}, "Polygon": [{"X": 0.03952699154615402, "Y": 0.8406397700309753}, {"X": 0.09018800407648087, "Y": 0.8406391143798828}, {"X": 0.09018770605325699, "Y": 0.8576862812042236}, {"X": 0.03952660411596298, "Y": 0.8576869368553162}]}, "Id": "622b595c-cc84-42b3-bcfe-0c902e1a96c5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024127379059791565, "Height": 0.016849396750330925, "Left": 0.09331778436899185, "Top": 0.8409908413887024}, "Polygon": [{"X": 0.09331808239221573, "Y": 0.8409911394119263}, {"X": 0.11744516342878342, "Y": 0.8409908413887024}, {"X": 0.11744491010904312, "Y": 0.8578399419784546}, {"X": 0.09331778436899185, "Y": 0.8578402400016785}]}, "Id": "b86e5066-e6af-4f5f-8515-84c695dbe637"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "07/07/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0570371113717556, "Height": 0.020525168627500534, "Left": 0.0395677387714386, "Top": 0.8675714135169983}, "Polygon": [{"X": 0.039568204432725906, "Y": 0.8675721883773804}, {"X": 0.0966048464179039, "Y": 0.8675714135169983}, {"X": 0.09660449624061584, "Y": 0.8880957961082458}, {"X": 0.0395677387714386, "Y": 0.8880965709686279}]}, "Id": "cef914ef-6ed9-4164-8327-1601fdc049b1"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "PRINT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019923320040106773, "Height": 0.014124037697911263, "Left": 0.90708988904953, "Top": 0.905482292175293}, "Polygon": [{"X": 0.90708988904953, "Y": 0.9054825901985168}, {"X": 0.9270123839378357, "Y": 0.905482292175293}, {"X": 0.9270132184028625, "Y": 0.9196060299873352}, {"X": 0.9070907831192017, "Y": 0.9196063280105591}]}, "Id": "225c1a69-b4e6-493e-bd31-89d62b02b313"}, {"BlockType": "WORD", "Confidence": 74.84683990478516, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0058517795987427235, "Height": 0.014519419521093369, "Left": 0.9455345869064331, "Top": 0.90543133020401}, "Polygon": [{"X": 0.9455345869064331, "Y": 0.9054314494132996}, {"X": 0.9513854384422302, "Y": 0.90543133020401}, {"X": 0.9513863921165466, "Y": 0.9199506640434265}, {"X": 0.9455355405807495, "Y": 0.9199507832527161}]}, "Id": "5c64eda4-23ac-40a9-9678-da63c9549248"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "CLOSE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0216877032071352, "Height": 0.014395234175026417, "Left": 0.9663415551185608, "Top": 0.9059008359909058}, "Polygon": [{"X": 0.9663415551185608, "Y": 0.9059011340141296}, {"X": 0.9880282878875732, "Y": 0.9059008359909058}, {"X": 0.9880293011665344, "Y": 0.920295774936676}, {"X": 0.9663425087928772, "Y": 0.9202960729598999}]}, "Id": "5813dfb4-81e7-408b-a221-87725b78a1bc"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "9c81741a-de76-4fdb-b955-fecf43499851", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "9c81741a-de76-4fdb-b955-fecf43499851", "content-type": "application/x-amz-json-1.1", "content-length": "66620", "date": "Wed, 24 Sep 2025 13:32:23 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "4d0027af-dd0f-4db7-9df2-1b6f524b2811", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:26 GMT", "content-type": "application/json", "content-length": "1253", "connection": "keep-alive", "x-amzn-requestid": "4d0027af-dd0f-4db7-9df2-1b6f524b2811"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. Let's analyze the content. It seems like an Equipment Inspection Record, also called Interchange Receipt. The text includes \"Interchange Receipt (J1)\" and \"Equipment Inspection Record\". Also \"Outgate: LOGPARCHI IL 07/07/2025 2315\". This is a gate-out record. The keywords for outgate: \"Outgate\", \"Gate Out\", \"Release\", \"Time Out\", \"Exit Time\". So this page likely is an outgate document.\n\nAlso \"Equipment Inspection Record\" appears, but outgate is a type. The classification categories include ingate and outgate. This page is outgate, as it says Outgate: LOGPARCHI IL 07/07/2025 2315. So doc_type: outgate.\n\nCheck if any other category might fit. \"Equipment Inspection Record\" could be a log type? But outgate is more specific. So we will classify page 1 as outgate.\n\nReturn JSON with documents array containing one object: page_no: 1, doc_type: \"outgate\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2820, "outputTokens": 248, "totalTokens": 3068}, "metrics": {"latencyMs": 1259}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/de3cd93d_HZNQ971O2V9MGG5MU1T3.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nInterchange Receipt (J1)\nX\nCLOSE\nBNSF Railway Company\nEquipment Inspection Record\nYMMU 405514 LOAD\nFLAT 1234\nOutgate: LOGPARCHI IL 07/07/2025 2315\nPatron: YANGMINAMERI\nTrucker: DPBN IC To:\nLicense: 5177\nSeal: YMAT715808\nCust: YANGMINMARLI\nRelease ID: 6555CC\nGross WGT: 51762\nUnit cannot return empty w/out waybill\nDamage:\nPHOTOGRAPHIC INSPECTION - Equipment defects are\ncaptured on recorded images.\nVessel Nme: YM UNANIMITY\nNbr: 080E Bkg:\nIntermodal Safe Container Certification\nCargo Weight: 43162 lbs\nCertify Party: YANG MING NO\nCertify Date: 06/23/2025\nCommodity: PLATES NON-C\nConversion Party:\nBNSF RAILWAY CO\nConversion Date:\n07/07/2025\nPRINT\nX\nCLOSE\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}