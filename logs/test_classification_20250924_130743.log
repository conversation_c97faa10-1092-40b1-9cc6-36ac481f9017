2025-09-24 13:07:43,149 - INFO - Logging initialized. Log file: logs/test_classification_20250924_130743.log
2025-09-24 13:07:43,150 - INFO - 📁 Found 13 files to process
2025-09-24 13:07:43,150 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:07:43,150 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 13:07:43,150 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 13:07:43,150 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 13:07:43,150 - INFO - ⬆️ [13:07:43] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:44,963 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/55bdbda8_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:44,964 - INFO - 🔍 [13:07:44] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:44,964 - INFO - ⬆️ [13:07:44] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:07:44,967 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:44,979 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:44,984 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55bdbda8_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:44,984 - INFO - Processing PDF from S3...
2025-09-24 13:07:44,985 - INFO - Downloading PDF from S3 to /tmp/tmpzehs_5mc/55bdbda8_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:45,949 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:07:45,949 - INFO - 🔍 [13:07:45] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:07:45,950 - INFO - ⬆️ [13:07:45] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:45,952 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:45,972 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:45,976 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:07:45,976 - INFO - Processing PDF from S3...
2025-09-24 13:07:45,977 - INFO - Downloading PDF from S3 to /tmp/tmphf2umq2s/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:07:46,551 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:07:46,552 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:46,554 - INFO - Splitting PDF 55bdbda8_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 13:07:46,557 - INFO - Split PDF into 1 pages
2025-09-24 13:07:46,557 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:46,557 - INFO - Expected pages: [1]
2025-09-24 13:07:47,307 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:47,307 - INFO - 🔍 [13:07:47] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:47,307 - INFO - ⬆️ [13:07:47] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:07:47,308 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:47,324 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:47,327 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:47,327 - INFO - Processing PDF from S3...
2025-09-24 13:07:47,328 - INFO - Downloading PDF from S3 to /tmp/tmpo2ja92v2/ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:47,992 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:07:47,993 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:47,994 - INFO - Splitting PDF 0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 13:07:48,000 - INFO - Split PDF into 2 pages
2025-09-24 13:07:48,000 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:48,000 - INFO - Expected pages: [1, 2]
2025-09-24 13:07:48,716 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/49467323_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:07:48,716 - INFO - 🔍 [13:07:48] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:07:48,717 - INFO - ⬆️ [13:07:48] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:48,722 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:48,738 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:48,744 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/49467323_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:07:48,744 - INFO - Processing PDF from S3...
2025-09-24 13:07:48,745 - INFO - Downloading PDF from S3 to /tmp/tmpeyupnc4l/49467323_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:07:49,357 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:49,357 - INFO - 🔍 [13:07:49] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:49,358 - INFO - ⬆️ [13:07:49] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:49,360 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:49,379 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:49,384 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:49,385 - INFO - Processing PDF from S3...
2025-09-24 13:07:49,385 - INFO - Downloading PDF from S3 to /tmp/tmpum05_jpo/51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:49,767 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:07:49,767 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:49,768 - INFO - Splitting PDF ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 13:07:49,769 - INFO - Split PDF into 3 pages
2025-09-24 13:07:49,770 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:49,770 - INFO - Expected pages: [1, 2, 3]
2025-09-24 13:07:50,018 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:50,018 - INFO - 🔍 [13:07:50] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:50,019 - INFO - ⬆️ [13:07:50] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:07:50,021 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:50,044 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:50,048 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:50,048 - INFO - Processing PDF from S3...
2025-09-24 13:07:50,048 - INFO - Downloading PDF from S3 to /tmp/tmpaalx3xls/45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:50,578 - INFO - Page 1: Extracted 589 characters, 36 lines from 55bdbda8_I_QHD3LC0DU6S8O2YVVS60_2b64e15c_page_001.pdf
2025-09-24 13:07:50,578 - INFO - Successfully processed page 1
2025-09-24 13:07:50,579 - INFO - Combined 1 pages into final text
2025-09-24 13:07:50,579 - INFO - Text validation for 55bdbda8_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 13:07:50,580 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:50,580 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:50,785 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/713042b9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:07:50,786 - INFO - 🔍 [13:07:50] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:07:50,787 - INFO - ⬆️ [13:07:50] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:07:50,789 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:50,841 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:50,845 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/713042b9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:07:50,846 - INFO - Processing PDF from S3...
2025-09-24 13:07:50,846 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:07:50,846 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:50,846 - INFO - Downloading PDF from S3 to /tmp/tmpng5e25eo/713042b9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:07:50,853 - INFO - Splitting PDF 51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 13:07:50,857 - INFO - Split PDF into 2 pages
2025-09-24 13:07:50,857 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:50,857 - INFO - Expected pages: [1, 2]
2025-09-24 13:07:51,552 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/4f1589e6_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:07:51,552 - INFO - 🔍 [13:07:51] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:07:51,553 - INFO - ⬆️ [13:07:51] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:07:51,555 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:51,574 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:51,577 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4f1589e6_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:07:51,578 - INFO - Processing PDF from S3...
2025-09-24 13:07:51,578 - INFO - Downloading PDF from S3 to /tmp/tmpgmjoi5ly/4f1589e6_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:07:51,771 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 13:07:51,771 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:51,774 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 13:07:51,774 - INFO - Splitting PDF 49467323_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 13:07:51,810 - INFO - Split PDF into 2 pages
2025-09-24 13:07:51,810 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:51,810 - INFO - Expected pages: [1, 2]
2025-09-24 13:07:51,911 - INFO - Page 2: Extracted 764 characters, 54 lines from 0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C_b81796de_page_002.pdf
2025-09-24 13:07:51,911 - INFO - Successfully processed page 2
2025-09-24 13:07:52,122 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:07:52,122 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:52,124 - INFO - Splitting PDF 45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 13:07:52,126 - INFO - Split PDF into 1 pages
2025-09-24 13:07:52,126 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:52,127 - INFO - Expected pages: [1]
2025-09-24 13:07:52,209 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/a3dcecff_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:07:52,209 - INFO - 🔍 [13:07:52] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:07:52,210 - INFO - ⬆️ [13:07:52] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:07:52,211 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:52,228 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:52,231 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a3dcecff_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:07:52,231 - INFO - Processing PDF from S3...
2025-09-24 13:07:52,231 - INFO - Downloading PDF from S3 to /tmp/tmp8sfkwrep/a3dcecff_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:07:52,793 - INFO - Page 1: Extracted 854 characters, 69 lines from 0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C_b81796de_page_001.pdf
2025-09-24 13:07:52,794 - INFO - Successfully processed page 1
2025-09-24 13:07:52,794 - INFO - Combined 2 pages into final text
2025-09-24 13:07:52,794 - INFO - Text validation for 0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 13:07:52,795 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:52,795 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:52,868 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/7705c1ad_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:07:52,868 - INFO - 🔍 [13:07:52] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:07:52,869 - INFO - ⬆️ [13:07:52] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:07:52,870 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:52,890 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:52,894 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7705c1ad_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:07:52,894 - INFO - Processing PDF from S3...
2025-09-24 13:07:52,894 - INFO - Downloading PDF from S3 to /tmp/tmpdwm2ktf1/7705c1ad_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:07:53,329 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55bdbda8_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:53,525 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/3cb49471_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:07:53,525 - INFO - 🔍 [13:07:53] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:07:53,526 - INFO - ⬆️ [13:07:53] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:07:53,526 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:53,537 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:53,539 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3cb49471_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:07:53,539 - INFO - Processing PDF from S3...
2025-09-24 13:07:53,540 - INFO - Downloading PDF from S3 to /tmp/tmp1b1xi17e/3cb49471_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:07:53,601 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:07:53,602 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:53,603 - INFO - Splitting PDF 4f1589e6_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 13:07:53,605 - INFO - Split PDF into 1 pages
2025-09-24 13:07:53,605 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:53,605 - INFO - Expected pages: [1]
2025-09-24 13:07:54,037 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:07:54,038 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:54,039 - INFO - Splitting PDF a3dcecff_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 13:07:54,042 - INFO - Split PDF into 1 pages
2025-09-24 13:07:54,042 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:54,042 - INFO - Expected pages: [1]
2025-09-24 13:07:54,170 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/2b5f9321_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:07:54,170 - INFO - 🔍 [13:07:54] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:07:54,172 - INFO - ⬆️ [13:07:54] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:07:54,174 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:54,197 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:54,202 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2b5f9321_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:07:54,202 - INFO - Processing PDF from S3...
2025-09-24 13:07:54,202 - INFO - Downloading PDF from S3 to /tmp/tmpr93ffe0s/2b5f9321_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:07:54,268 - INFO - Page 3: Extracted 850 characters, 59 lines from ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N_f2f2c5b6_page_003.pdf
2025-09-24 13:07:54,269 - INFO - Successfully processed page 3
2025-09-24 13:07:54,359 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:07:54,359 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:54,360 - INFO - Splitting PDF 713042b9_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 13:07:54,362 - INFO - Split PDF into 1 pages
2025-09-24 13:07:54,363 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:54,363 - INFO - Expected pages: [1]
2025-09-24 13:07:54,461 - INFO - Page 2: Extracted 850 characters, 59 lines from ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N_f2f2c5b6_page_002.pdf
2025-09-24 13:07:54,462 - INFO - Successfully processed page 2
2025-09-24 13:07:54,562 - INFO - Page 1: Extracted 1511 characters, 86 lines from 51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU_ab23ae7f_page_001.pdf
2025-09-24 13:07:54,562 - INFO - Successfully processed page 1
2025-09-24 13:07:54,641 - INFO - Page 1: Extracted 980 characters, 76 lines from ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N_f2f2c5b6_page_001.pdf
2025-09-24 13:07:54,641 - INFO - Successfully processed page 1
2025-09-24 13:07:54,642 - INFO - Combined 3 pages into final text
2025-09-24 13:07:54,642 - INFO - Text validation for ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 13:07:54,642 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:54,642 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:54,849 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/d37844fb_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:07:54,850 - INFO - 🔍 [13:07:54] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:07:54,852 - INFO - Initializing TextractProcessor...
2025-09-24 13:07:54,868 - INFO - Initializing BedrockProcessor...
2025-09-24 13:07:54,881 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d37844fb_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:07:54,888 - INFO - Page 2: Extracted 913 characters, 56 lines from 51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU_ab23ae7f_page_002.pdf
2025-09-24 13:07:54,889 - INFO - Processing PDF from S3...
2025-09-24 13:07:54,891 - INFO - Successfully processed page 2
2025-09-24 13:07:54,894 - INFO - Downloading PDF from S3 to /tmp/tmpk7n9gihy/d37844fb_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:07:54,895 - INFO - Combined 2 pages into final text
2025-09-24 13:07:54,895 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 13:07:54,899 - INFO - Text validation for 51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 13:07:54,899 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:07:54,902 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:54,902 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:54,927 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:07:54,928 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:54,929 - INFO - Splitting PDF 7705c1ad_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 13:07:54,931 - INFO - Split PDF into 1 pages
2025-09-24 13:07:54,931 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:54,931 - INFO - Expected pages: [1]
2025-09-24 13:07:55,214 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55bdbda8_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:07:55,256 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:07:55,257 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:55,259 - INFO - Splitting PDF 3cb49471_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 13:07:55,260 - INFO - Split PDF into 1 pages
2025-09-24 13:07:55,260 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:55,260 - INFO - Expected pages: [1]
2025-09-24 13:07:55,658 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:07:55,659 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:55,659 - INFO - Splitting PDF 2b5f9321_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 13:07:55,660 - INFO - Split PDF into 1 pages
2025-09-24 13:07:55,660 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:55,660 - INFO - Expected pages: [1]
2025-09-24 13:07:56,964 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:07:56,964 - INFO - Splitting PDF into individual pages...
2025-09-24 13:07:56,966 - INFO - Splitting PDF d37844fb_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 13:07:56,975 - INFO - Split PDF into 1 pages
2025-09-24 13:07:56,976 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:07:56,976 - INFO - Expected pages: [1]
2025-09-24 13:07:57,255 - INFO - Page 1: Extracted 443 characters, 72 lines from 45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I_3d4a6728_page_001.pdf
2025-09-24 13:07:57,255 - INFO - Successfully processed page 1
2025-09-24 13:07:57,255 - INFO - Combined 1 pages into final text
2025-09-24 13:07:57,255 - INFO - Text validation for 45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 13:07:57,255 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:57,255 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:57,565 - INFO - Page 2: Extracted 540 characters, 29 lines from 49467323_NMFC_OR9EL08KIKNQPZ3UV3HH_0dda8621_page_002.pdf
2025-09-24 13:07:57,566 - INFO - Successfully processed page 2
2025-09-24 13:07:57,999 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:58,037 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 3,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:07:58,037 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:07:58,094 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:58,227 - INFO - Page 1: Extracted 1120 characters, 87 lines from 49467323_NMFC_OR9EL08KIKNQPZ3UV3HH_0dda8621_page_001.pdf
2025-09-24 13:07:58,227 - INFO - Successfully processed page 1
2025-09-24 13:07:58,227 - INFO - Combined 2 pages into final text
2025-09-24 13:07:58,227 - INFO - Text validation for 49467323_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 13:07:58,228 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:58,228 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:58,250 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-24 13:07:58,251 - ERROR - Processing failed for s3://document-extraction-logistically/temp/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:07:58,251 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:07:58,252 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:07:58,252 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:07:58,266 - INFO - Page 1: Extracted 732 characters, 59 lines from 4f1589e6_W_DCY7SLNMWUXIENOREHQF_6f350577_page_001.pdf
2025-09-24 13:07:58,266 - INFO - Successfully processed page 1
2025-09-24 13:07:58,267 - INFO - Combined 1 pages into final text
2025-09-24 13:07:58,267 - INFO - Text validation for 4f1589e6_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 13:07:58,267 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:58,267 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:58,360 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ae1a5ba4_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:07:58,372 - INFO - Page 1: Extracted 626 characters, 49 lines from a3dcecff_W_DFY1VDZWR7NBDLJV02G2_58f8895d_page_001.pdf
2025-09-24 13:07:58,374 - INFO - Successfully processed page 1
2025-09-24 13:07:58,376 - INFO - Combined 1 pages into final text
2025-09-24 13:07:58,377 - INFO - Text validation for a3dcecff_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 13:07:58,381 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:58,382 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:58,396 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:07:58,396 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:07:58,723 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/51ef6e8c_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:07:58,724 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:07:58,724 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:07:58,724 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:07:59,046 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:07:59,340 - INFO - Page 1: Extracted 802 characters, 30 lines from 3cb49471_W_K9VSARJOKAIZHNJ5RBDT_860086e7_page_001.pdf
2025-09-24 13:07:59,340 - INFO - Successfully processed page 1
2025-09-24 13:07:59,341 - INFO - Combined 1 pages into final text
2025-09-24 13:07:59,341 - INFO - Text validation for 3cb49471_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 13:07:59,341 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:59,342 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:59,405 - INFO - Page 1: Extracted 517 characters, 31 lines from 7705c1ad_W_HFPAXYL947DH59AB12FL_5a51ca69_page_001.pdf
2025-09-24 13:07:59,405 - INFO - Successfully processed page 1
2025-09-24 13:07:59,406 - INFO - Combined 1 pages into final text
2025-09-24 13:07:59,406 - INFO - Text validation for 7705c1ad_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 13:07:59,407 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:59,407 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:07:59,445 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:59,470 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:07:59,470 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:07:59,798 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/45c52f75_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:07:59,881 - INFO - Page 1: Extracted 580 characters, 48 lines from 2b5f9321_W_WRKSHW76B3QUG47QWR75_13294cda_page_001.pdf
2025-09-24 13:07:59,882 - INFO - Successfully processed page 1
2025-09-24 13:07:59,882 - INFO - Combined 1 pages into final text
2025-09-24 13:07:59,883 - INFO - Text validation for 2b5f9321_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 13:07:59,883 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:07:59,883 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:08:00,515 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4f1589e6_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:08:00,544 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 13:08:00,544 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:08:00,765 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a3dcecff_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:08:00,880 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4f1589e6_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:08:00,902 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:08:00,902 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:08:01,231 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a3dcecff_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:08:01,568 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7705c1ad_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:08:01,588 - INFO - Page 1: Extracted 939 characters, 64 lines from 713042b9_W_A34CDFDJ66EDOZEKZWJL_905f2d0f_page_001.pdf
2025-09-24 13:08:01,589 - INFO - Successfully processed page 1
2025-09-24 13:08:01,590 - INFO - Combined 1 pages into final text
2025-09-24 13:08:01,590 - INFO - Text validation for 713042b9_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 13:08:01,599 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:08:01,600 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:08:01,602 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:08:01,602 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:08:01,723 - INFO - Page 1: Extracted 528 characters, 31 lines from d37844fb_W_XCJLXZK140FUS8020ZAG_40b8fe8b_page_001.pdf
2025-09-24 13:08:01,723 - INFO - Successfully processed page 1
2025-09-24 13:08:01,724 - INFO - Combined 1 pages into final text
2025-09-24 13:08:01,724 - INFO - Text validation for d37844fb_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 13:08:01,725 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:08:01,725 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:08:01,828 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3cb49471_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:08:01,929 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7705c1ad_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:08:01,949 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 13:08:01,950 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:08:02,072 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/49467323_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:08:02,276 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3cb49471_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:08:02,298 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:08:02,298 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:08:02,620 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/49467323_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:08:02,654 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2b5f9321_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:08:02,664 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:08:02,664 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:08:02,985 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2b5f9321_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:08:04,009 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/713042b9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:08:04,035 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:08:04,035 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:08:04,111 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d37844fb_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:08:04,359 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/713042b9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:08:04,384 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:08:04,385 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:08:04,714 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d37844fb_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:08:04,714 - INFO - 
📊 Processing Summary:
2025-09-24 13:08:04,715 - INFO -    Total files: 13
2025-09-24 13:08:04,715 - INFO -    Successful: 12
2025-09-24 13:08:04,715 - INFO -    Failed: 1
2025-09-24 13:08:04,715 - INFO -    Duration: 21.56 seconds
2025-09-24 13:08:04,715 - INFO -    Output directory: output
2025-09-24 13:08:04,715 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:08:04,715 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:08:04,715 - ERROR - 
❌ Errors:
2025-09-24 13:08:04,715 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:08:04,716 - INFO - 
============================================================================================================================================
2025-09-24 13:08:04,716 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:08:04,716 - INFO - ============================================================================================================================================
2025-09-24 13:08:04,716 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:08:04,716 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:08:04,716 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      inspection_cert      run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 13:08:04,716 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:08:04,716 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:08:04,716 - INFO - 
2025-09-24 13:08:04,716 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      1      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 13:08:04,716 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:08:04,716 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:08:04,716 - INFO - 
2025-09-24 13:08:04,716 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      2      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 13:08:04,716 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:08:04,716 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:08:04,716 - INFO - 
2025-09-24 13:08:04,716 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      3      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 13:08:04,716 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:08:04,716 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:08:04,716 - INFO - 
2025-09-24 13:08:04,716 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 13:08:04,716 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:08:04,717 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:08:04,717 - INFO - 
2025-09-24 13:08:04,717 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 13:08:04,717 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:08:04,717 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:08:04,717 - INFO - 
2025-09-24 13:08:04,717 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 13:08:04,717 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:08:04,717 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:08:04,717 - INFO - 
2025-09-24 13:08:04,717 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 13:08:04,717 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:08:04,717 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:08:04,717 - INFO - 
2025-09-24 13:08:04,717 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 13:08:04,717 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      weight_and_inspect... run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 13:08:04,718 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      scale_ticket         run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 13:08:04,718 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      weight_and_inspect... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 13:08:04,718 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 13:08:04,718 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      scale_ticket         run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 13:08:04,718 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 13:08:04,718 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:08:04,718 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:08:04,718 - INFO - 
2025-09-24 13:08:04,718 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      weight_and_inspect... run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 13:08:04,719 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:08:04,719 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:08:04,719 - INFO - 
2025-09-24 13:08:04,719 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:08:04,719 - INFO - Total entries: 16
2025-09-24 13:08:04,719 - INFO - ============================================================================================================================================
2025-09-24 13:08:04,719 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:08:04,719 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:08:04,719 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:08:04,719 - INFO -   2. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:08:04,719 - INFO -   3. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:08:04,719 - INFO -   4. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:08:04,719 - INFO -   5. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:08:04,719 - INFO -   6. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:08:04,719 - INFO -   7. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:08:04,719 - INFO -   8. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:08:04,719 - INFO -   9. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:08:04,719 - INFO -  10. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → weight_and_inspection_cert | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:08:04,720 - INFO -  11. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → scale_ticket    | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:08:04,720 - INFO -  12. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → weight_and_inspection_cert | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:08:04,720 - INFO -  13. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:08:04,720 - INFO -  14. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → scale_ticket    | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:08:04,720 - INFO -  15. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:08:04,720 - INFO -  16. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → weight_and_inspection_cert | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:08:04,720 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:08:04,720 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 13:08:04,720 - ERROR - 🐛 Debug info: {'exception': Exception('Unexpected tool response format from Bedrock'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response\n    raise Exception("Stop reason is not tool_use")\nException: Stop reason is not tool_use\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock\n    content = bedrock_processor.extract_tool_response(response)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response\n    raise Exception("Unexpected tool response format from Bedrock")\nException: Unexpected tool response format from Bedrock\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf', 's3_uri': 's3://document-extraction-logistically/temp/0e4479f1_NMFC_DH0JZ2JWDGRHD26BX74C.pdf', 'location': 'process_single_file'}
2025-09-24 13:08:04,720 - ERROR - 🐛 Location: process_single_file
2025-09-24 13:08:04,720 - ERROR - 🐛 Exception: Unexpected tool response format from Bedrock
2025-09-24 13:08:04,720 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

