{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.989490270614624, "Height": 0.6724876165390015, "Left": 0.004402820486575365, "Top": 0.15152771770954132}, "Polygon": [{"X": 0.004402820486575365, "Y": 0.15152771770954132}, {"X": 0.9938892722129822, "Y": 0.1515403836965561}, {"X": 0.9938930869102478, "Y": 0.8240081667900085}, {"X": 0.004409163724631071, "Y": 0.8240153193473816}]}, "Id": "d3deb145-0a38-4798-996b-0dcaa08d2428", "Relationships": [{"Type": "CHILD", "Ids": ["8c7b9297-90d3-401f-944e-d9a18d747719", "8490c6ae-ebd9-44fa-9f13-2e43e380df94", "65bbaf17-f4ac-4e87-8b23-958802fdf13b", "f8874478-a9ac-4d1e-9838-3216f937d41e", "7fdcb6bf-05ba-4a0f-9ad3-8a2adff83092", "df489837-c5b5-44f8-a2d6-5fb5d34e01b5", "978e1fc4-87af-4426-83bc-7ed8446259c5", "c445a4f6-7ff9-40b2-9999-2c30252daabc", "d2b0b12b-4a1f-4873-9c1e-901c6ceaf05a", "d7ffbcce-e24c-4c34-998d-33536515ed8f", "67dcd9b1-ec3b-4cc4-89e9-d1061b5a2d4e", "38c1e954-a44b-4dbd-9eba-28779f16f25b", "cf1896c8-746d-4316-833e-f6e5a547c4c3", "b66265d1-4d13-4b1e-8491-6bb6822b7f71", "9da67d31-ea1e-4bec-bad3-e6e0f5dab3ac", "d8714da1-0f89-4b6a-82ed-3a97da4cd6e3", "a1877db5-4caf-4025-a944-4448dd419506", "cc8f445c-3a16-44b0-a8a5-a4a015bee27a", "d72e770a-5f62-4bcd-ada0-8753d121dd3f", "666c9599-9ffc-4285-9630-bdbe68055504", "fef2e578-0d52-48d4-b48b-92c591119467", "40f2a851-06cd-44d3-a512-afe93511ba4f", "6375a271-31a7-40c5-b3c1-5ecffacf97f1", "67558427-4947-4b07-a06b-d3d89f5ac391", "f8bb39ed-dd6c-4e67-b603-f05f2687b6ef", "70cdcc26-1a5a-4fb6-8ae4-c5fa77eca642", "cb77ebc2-135e-4bea-8416-4180c0cf1809", "39010e56-cb58-4d16-885f-756d2f932d50"]}]}, {"BlockType": "LINE", "Confidence": 79.79911041259766, "Text": "R", "Geometry": {"BoundingBox": {"Width": 0.009888289496302605, "Height": 0.003928722348064184, "Left": 0.9128271341323853, "Top": 0.20693755149841309}, "Polygon": [{"X": 0.9128271341323853, "Y": 0.20693755149841309}, {"X": 0.922715425491333, "Y": 0.20693765580654144}, {"X": 0.922715425491333, "Y": 0.2108662724494934}, {"X": 0.9128271341323853, "Y": 0.21086616814136505}]}, "Id": "8c7b9297-90d3-401f-944e-d9a18d747719", "Relationships": [{"Type": "CHILD", "Ids": ["87ebac75-b0e0-42dc-9949-f8544b2c4fcc"]}]}, {"BlockType": "LINE", "Confidence": 77.4170913696289, "Text": "BNSF", "Geometry": {"BoundingBox": {"Width": 0.8091115951538086, "Height": 0.026706647127866745, "Left": 0.1117909774184227, "Top": 0.20526422560214996}, "Polygon": [{"X": 0.1117909774184227, "Y": 0.20526422560214996}, {"X": 0.9209024310112, "Y": 0.20527330040931702}, {"X": 0.9209026098251343, "Y": 0.231970876455307}, {"X": 0.1117912232875824, "Y": 0.23196245729923248}]}, "Id": "8490c6ae-ebd9-44fa-9f13-2e43e380df94", "Relationships": [{"Type": "CHILD", "Ids": ["f2811e5d-4a35-484b-b9b1-9e21c02fc8db"]}]}, {"BlockType": "LINE", "Confidence": 95.82848358154297, "Text": "RAILWAY", "Geometry": {"BoundingBox": {"Width": 0.3560348451137543, "Height": 0.012528890743851662, "Left": 0.456515371799469, "Top": 0.24629908800125122}, "Polygon": [{"X": 0.456515371799469, "Y": 0.24629908800125122}, {"X": 0.8125501275062561, "Y": 0.24630263447761536}, {"X": 0.8125502467155457, "Y": 0.25882798433303833}, {"X": 0.45651546120643616, "Y": 0.25882455706596375}]}, "Id": "65bbaf17-f4ac-4e87-8b23-958802fdf13b", "Relationships": [{"Type": "CHILD", "Ids": ["5f84d111-6a0d-433d-abfe-774b8e422363"]}]}, {"BlockType": "LINE", "Confidence": 99.1651382446289, "Text": "IN-GATE", "Geometry": {"BoundingBox": {"Width": 0.12356900423765182, "Height": 0.008898710831999779, "Left": 0.43883252143859863, "Top": 0.2676897943019867}, "Polygon": [{"X": 0.43883252143859863, "Y": 0.2676897943019867}, {"X": 0.5624014735221863, "Y": 0.2676909267902374}, {"X": 0.562401533126831, "Y": 0.276588499546051}, {"X": 0.4388325810432434, "Y": 0.2765873670578003}]}, "Id": "f8874478-a9ac-4d1e-9838-3216f937d41e", "Relationships": [{"Type": "CHILD", "Ids": ["87d0d5a5-89e0-4346-a3c7-aa64b18560ec"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "BNSF Railway", "Geometry": {"BoundingBox": {"Width": 0.1670115888118744, "Height": 0.009754893369972706, "Left": 0.4166542887687683, "Top": 0.2933105528354645}, "Polygon": [{"X": 0.4166542887687683, "Y": 0.2933105528354645}, {"X": 0.5836657881736755, "Y": 0.2933119833469391}, {"X": 0.5836659073829651, "Y": 0.3030654489994049}, {"X": 0.4166543781757355, "Y": 0.3030640780925751}]}, "Id": "7fdcb6bf-05ba-4a0f-9ad3-8a2adff83092", "Relationships": [{"Type": "CHILD", "Ids": ["9d2cb2b7-d7d2-44a2-9662-a40b7d975449", "fdc2aae4-85f3-4a17-88b7-fa53c2d90aca"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Equipment Inspection Record", "Geometry": {"BoundingBox": {"Width": 0.3759171962738037, "Height": 0.010138296522200108, "Left": 0.312275767326355, "Top": 0.30910518765449524}, "Polygon": [{"X": 0.312275767326355, "Y": 0.30910518765449524}, {"X": 0.6881929039955139, "Y": 0.3091082274913788}, {"X": 0.6881929636001587, "Y": 0.319243460893631}, {"X": 0.31227585673332214, "Y": 0.319240540266037}]}, "Id": "df489837-c5b5-44f8-a2d6-5fb5d34e01b5", "Relationships": [{"Type": "CHILD", "Ids": ["09a6d730-75c2-4fff-9b2c-bc4ade144472", "14a5ddac-baf1-4c33-b9b4-37047f426257", "c60d7755-c9c6-4566-8b39-473aa05a89ed"]}]}, {"BlockType": "LINE", "Confidence": 99.9275894165039, "Text": "YMMU 416178 EMPTY", "Geometry": {"BoundingBox": {"Width": 0.23621515929698944, "Height": 0.007744785863906145, "Left": 0.06692345440387726, "Top": 0.34135904908180237}, "Polygon": [{"X": 0.06692345440387726, "Y": 0.34135904908180237}, {"X": 0.3031385540962219, "Y": 0.34136074781417847}, {"X": 0.3031386137008667, "Y": 0.3491038382053375}, {"X": 0.06692352145910263, "Y": 0.3491021990776062}]}, "Id": "978e1fc4-87af-4426-83bc-7ed8446259c5", "Relationships": [{"Type": "CHILD", "Ids": ["25866c42-3f3c-4a53-ae7b-657f5cbcaf4d", "fa64b0c0-5de0-4380-87fa-93200dbe4f15", "c2ffbd31-6d63-4740-a801-7dc4a253afe6"]}]}, {"BlockType": "LINE", "Confidence": 99.77100372314453, "Text": "MAEC 692165", "Geometry": {"BoundingBox": {"Width": 0.15199874341487885, "Height": 0.007690004538744688, "Left": 0.06615248322486877, "Top": 0.35777419805526733}, "Polygon": [{"X": 0.06615248322486877, "Y": 0.35777419805526733}, {"X": 0.21815116703510284, "Y": 0.3577752113342285}, {"X": 0.21815122663974762, "Y": 0.3654641807079315}, {"X": 0.06615255773067474, "Y": 0.3654632270336151}]}, "Id": "c445a4f6-7ff9-40b2-9999-2c30252daabc", "Relationships": [{"Type": "CHILD", "Ids": ["9b6949e1-a6d4-4979-ada0-75f97ba6945b", "25bf2d33-f650-4885-ad9c-2bc1a2b1bef1"]}]}, {"BlockType": "LINE", "Confidence": 99.90708923339844, "Text": "Ingate: LOGPARCHI IL 08/13/2025 1442", "Geometry": {"BoundingBox": {"Width": 0.49891650676727295, "Height": 0.010095872916281223, "Left": 0.06799521297216415, "Top": 0.38931989669799805}, "Polygon": [{"X": 0.06799521297216415, "Y": 0.38931989669799805}, {"X": 0.5669116377830505, "Y": 0.3893227279186249}, {"X": 0.5669116973876953, "Y": 0.3994157612323761}, {"X": 0.06799530982971191, "Y": 0.3994130492210388}]}, "Id": "d2b0b12b-4a1f-4873-9c1e-901c6ceaf05a", "Relationships": [{"Type": "CHILD", "Ids": ["8f7500cb-9693-4aef-90a4-7dd93dca655b", "00378a58-7b6b-429f-8ced-ff0589d12a18", "e8d3bfdd-101b-438d-bc18-a9ccaec5503e", "6e81c2a4-8d7b-40f4-aa96-d19db096690c", "e8600667-f913-463a-a929-9b9fb21447c4"]}]}, {"BlockType": "LINE", "Confidence": 99.6334457397461, "Text": "Shipper: YANGMINAMERI Dest: LOGPARCHI IL", "Geometry": {"BoundingBox": {"Width": 0.5564395189285278, "Height": 0.010077064856886864, "Left": 0.06732454895973206, "Top": 0.40544235706329346}, "Polygon": [{"X": 0.06732454895973206, "Y": 0.40544235706329346}, {"X": 0.6237639784812927, "Y": 0.40544527769088745}, {"X": 0.6237640380859375, "Y": 0.41551944613456726}, {"X": 0.06732464581727982, "Y": 0.4155166745185852}]}, "Id": "d7ffbcce-e24c-4c34-998d-33536515ed8f", "Relationships": [{"Type": "CHILD", "Ids": ["1f5e52b4-0404-4959-af82-6ba116a1c86b", "2557f587-be91-48e1-91d6-10f175d98b5a", "80c5de8f-a605-47fa-b72f-74d3e2a51f1a", "009f2041-c02b-4c47-8f60-0e86552523af", "add54372-f8d0-40cc-b455-6b532cda7df8"]}]}, {"BlockType": "LINE", "Confidence": 99.86048889160156, "Text": "Trucker: SEXS Tractor: AUTOGATE", "Geometry": {"BoundingBox": {"Width": 0.43143272399902344, "Height": 0.007872168906033039, "Left": 0.06672006100416183, "Top": 0.4220048785209656}, "Polygon": [{"X": 0.06672006100416183, "Y": 0.4220048785209656}, {"X": 0.4981527328491211, "Y": 0.42200690507888794}, {"X": 0.49815279245376587, "Y": 0.42987704277038574}, {"X": 0.0667201355099678, "Y": 0.42987510561943054}]}, "Id": "67dcd9b1-ec3b-4cc4-89e9-d1061b5a2d4e", "Relationships": [{"Type": "CHILD", "Ids": ["b5c48e8c-5c0e-4755-bd4c-236683676290", "27415fbf-62ab-4d4d-a5b2-ed56c222ba76", "397284df-e274-4c23-b27a-825c6e03c49a", "2a33ae68-8f13-403d-aaf0-08c0999fa842"]}]}, {"BlockType": "LINE", "Confidence": 99.77220153808594, "Text": "License:", "Geometry": {"BoundingBox": {"Width": 0.10731687396764755, "Height": 0.0077404906041920185, "Left": 0.06653141230344772, "Top": 0.43807652592658997}, "Polygon": [{"X": 0.06653141230344772, "Y": 0.43807652592658997}, {"X": 0.1738482117652893, "Y": 0.4380769729614258}, {"X": 0.17384828627109528, "Y": 0.4458169937133789}, {"X": 0.0665314793586731, "Y": 0.4458165764808655}]}, "Id": "38c1e954-a44b-4dbd-9eba-28779f16f25b", "Relationships": [{"Type": "CHILD", "Ids": ["074c7523-7f74-4db8-9a8c-0206118be913"]}]}, {"BlockType": "LINE", "Confidence": 97.46477508544922, "Text": "*****1271 (Displaying the last *****4 digits)", "Geometry": {"BoundingBox": {"Width": 0.6216581463813782, "Height": 0.0100106755271554, "Left": 0.1936964988708496, "Top": 0.4378235340118408}, "Polygon": [{"X": 0.1936964988708496, "Y": 0.4378235340118408}, {"X": 0.815354585647583, "Y": 0.4378261864185333}, {"X": 0.8153546452522278, "Y": 0.44783419370651245}, {"X": 0.19369658827781677, "Y": 0.4478317201137543}]}, "Id": "cf1896c8-746d-4316-833e-f6e5a547c4c3", "Relationships": [{"Type": "CHILD", "Ids": ["4e8a4c87-525d-41fb-82f4-511202073ca9", "459a8822-f4ed-4a1c-b5dd-fac874aad3cf", "d212da9a-d899-4be6-ba8a-24e351447693", "cb9be34b-ca01-4d4e-b08b-c0dd288e8ea9", "34eff593-04b6-4caa-aebf-0d2e3fba357e", "4611e273-a01b-4ea3-b9f3-03083ba85d5d"]}]}, {"BlockType": "LINE", "Confidence": 99.88719940185547, "Text": "Seal: IC From:", "Geometry": {"BoundingBox": {"Width": 0.20411086082458496, "Height": 0.00796541292220354, "Left": 0.06721862405538559, "Top": 0.4539932310581207}, "Polygon": [{"X": 0.06721862405538559, "Y": 0.4539932310581207}, {"X": 0.271329402923584, "Y": 0.4539940059185028}, {"X": 0.27132949233055115, "Y": 0.461958646774292}, {"X": 0.06721869856119156, "Y": 0.4619579017162323}]}, "Id": "b66265d1-4d13-4b1e-8491-6bb6822b7f71", "Relationships": [{"Type": "CHILD", "Ids": ["00eacbab-5b80-497d-9274-d7f717410822", "be2a1686-5607-4b3c-854e-c5f4951a201f", "58101dc3-66e9-433c-850e-456d6cc3dc97"]}]}, {"BlockType": "LINE", "Confidence": 99.20156860351562, "Text": "LGT- 40 HT- 8.6 KP- 36 AXL -096 NU-", "Geometry": {"BoundingBox": {"Width": 0.4869934618473053, "Height": 0.007733907550573349, "Left": 0.06641772389411926, "Top": 0.4701486825942993}, "Polygon": [{"X": 0.06641772389411926, "Y": 0.4701486825942993}, {"X": 0.5534111261367798, "Y": 0.47015029191970825}, {"X": 0.5534111857414246, "Y": 0.4778825640678406}, {"X": 0.06641779839992523, "Y": 0.4778810739517212}]}, "Id": "9da67d31-ea1e-4bec-bad3-e6e0f5dab3ac", "Relationships": [{"Type": "CHILD", "Ids": ["7b044276-d74e-4329-931a-5c46c774bead", "76c0fe38-14c9-4750-acc6-cd0722f83ff8", "7db4117b-12f1-41af-a6fd-c40f23b1e3a4", "c7c03a69-7597-42c0-8359-6f520fad457a", "0bc2a0dc-95b0-476e-b983-261b10318666", "0fcf6b6c-fda6-4687-97fe-719345961e04", "5c45e336-9a70-438f-b907-f358d680a971", "08aaa689-5fca-4576-90c0-af2774d9fa04", "8d4ea2f1-6b30-4b34-a67a-610d1404bc30"]}]}, {"BlockType": "LINE", "Confidence": 99.55204010009766, "Text": "WGT: TOFC- COFC-", "Geometry": {"BoundingBox": {"Width": 0.23711317777633667, "Height": 0.007437653373926878, "Left": 0.06576299667358398, "Top": 0.48685699701309204}, "Polygon": [{"X": 0.06576299667358398, "Y": 0.48685699701309204}, {"X": 0.3028761148452759, "Y": 0.48685765266418457}, {"X": 0.30287617444992065, "Y": 0.4942946434020996}, {"X": 0.06576306372880936, "Y": 0.49429401755332947}]}, "Id": "d8714da1-0f89-4b6a-82ed-3a97da4cd6e3", "Relationships": [{"Type": "CHILD", "Ids": ["937913e2-5761-4eac-88a0-778a04de6418", "baf833dc-0e3a-48dc-aadd-920db7f0d276", "28553f2a-4ad8-464e-bdba-b193f5a5858b"]}]}, {"BlockType": "LINE", "Confidence": 98.70311737060547, "Text": "WGT: G- T- C-", "Geometry": {"BoundingBox": {"Width": 0.208794504404068, "Height": 0.007297751493752003, "Left": 0.06584112346172333, "Top": 0.5029089450836182}, "Polygon": [{"X": 0.06584112346172333, "Y": 0.5029089450836182}, {"X": 0.27463555335998535, "Y": 0.5029094219207764}, {"X": 0.2746356129646301, "Y": 0.5102066993713379}, {"X": 0.0658411905169487, "Y": 0.5102062225341797}]}, "Id": "a1877db5-4caf-4025-a944-4448dd419506", "Relationships": [{"Type": "CHILD", "Ids": ["7d8cb60d-2359-4fae-aa28-152add74246b", "d1483b2f-477c-4c8b-84d4-7da654ab845a", "2067dc54-0035-4c92-8774-2573199c7bee", "3e82a757-1b20-49c1-b6cb-38f333539403"]}]}, {"BlockType": "LINE", "Confidence": 99.88745880126953, "Text": "HAZMAT Dec: N", "Geometry": {"BoundingBox": {"Width": 0.180548295378685, "Height": 0.007327827159315348, "Left": 0.06677938997745514, "Top": 0.53493332862854}, "Polygon": [{"X": 0.06677938997745514, "Y": 0.53493332862854}, {"X": 0.24732761085033417, "Y": 0.5349335670471191}, {"X": 0.24732768535614014, "Y": 0.5422611832618713}, {"X": 0.06677945703268051, "Y": 0.5422609448432922}]}, "Id": "cc8f445c-3a16-44b0-a8a5-a4a015bee27a", "Relationships": [{"Type": "CHILD", "Ids": ["a6ac6d63-9f7b-4803-aebf-f406ae1ecd00", "be6292d2-5915-475d-9ac9-43f8ae493354", "a02709be-8be7-49e4-8acc-7ca475360d02"]}]}, {"BlockType": "LINE", "Confidence": 96.40306091308594, "Text": "Damage:", "Geometry": {"BoundingBox": {"Width": 0.09263195097446442, "Height": 0.009257418103516102, "Left": 0.06708609312772751, "Top": 0.567176878452301}, "Polygon": [{"X": 0.06708609312772751, "Y": 0.567176878452301}, {"X": 0.15971796214580536, "Y": 0.567176878452301}, {"X": 0.15971803665161133, "Y": 0.576434314250946}, {"X": 0.06708617508411407, "Y": 0.5764342546463013}]}, "Id": "d72e770a-5f62-4bcd-ada0-8753d121dd3f", "Relationships": [{"Type": "CHILD", "Ids": ["32072bae-a6c4-4d6c-8708-9b10708a70bb"]}]}, {"BlockType": "LINE", "Confidence": 95.22769927978516, "Text": "Current -", "Geometry": {"BoundingBox": {"Width": 0.12473507225513458, "Height": 0.007442047353833914, "Left": 0.06652258336544037, "Top": 0.5832008719444275}, "Polygon": [{"X": 0.06652258336544037, "Y": 0.5832008719444275}, {"X": 0.19125759601593018, "Y": 0.5832008719444275}, {"X": 0.19125765562057495, "Y": 0.5906428694725037}, {"X": 0.06652265787124634, "Y": 0.5906429290771484}]}, "Id": "666c9599-9ffc-4285-9630-bdbe68055504", "Relationships": [{"Type": "CHILD", "Ids": ["deeadb43-6c7d-430b-ad5d-9c8f0caaa2b0", "ea065568-104a-41b8-b425-e434cc0e5e9d"]}]}, {"BlockType": "LINE", "Confidence": 97.35414123535156, "Text": "PHOTOGRAPHIC INSPECTION - Equipment defects are captured on", "Geometry": {"BoundingBox": {"Width": 0.822012722492218, "Height": 0.010042184963822365, "Left": 0.06663818657398224, "Top": 0.5985441207885742}, "Polygon": [{"X": 0.06663818657398224, "Y": 0.5985445380210876}, {"X": 0.8886508345603943, "Y": 0.5985441207885742}, {"X": 0.8886508941650391, "Y": 0.6085855960845947}, {"X": 0.0666382759809494, "Y": 0.608586311340332}]}, "Id": "fef2e578-0d52-48d4-b48b-92c591119467", "Relationships": [{"Type": "CHILD", "Ids": ["c3db25c1-6a89-4479-8a1a-78dea045209b", "c777b9c5-166c-443d-8740-f719e5b5f96a", "73f1be9d-a63b-46fa-aed1-ebbf4ea9887f", "1f370d32-b6df-49ac-b203-f37055ed3363", "3a131f59-c0b9-4f7f-8026-d8b2e59c7323", "51ffee41-6f4b-47e1-90aa-e5fb91b103c8", "6d92bbeb-5817-4e4f-9f6a-a92c04dfba43"]}]}, {"BlockType": "LINE", "Confidence": 99.56153869628906, "Text": "recorded images.", "Geometry": {"BoundingBox": {"Width": 0.2176249921321869, "Height": 0.009813722223043442, "Left": 0.067342109978199, "Top": 0.6147170662879944}, "Polygon": [{"X": 0.067342109978199, "Y": 0.6147172451019287}, {"X": 0.2849670350551605, "Y": 0.6147170662879944}, {"X": 0.2849670946598053, "Y": 0.6245304942131042}, {"X": 0.06734219938516617, "Y": 0.6245307922363281}]}, "Id": "40f2a851-06cd-44d3-a512-afe93511ba4f", "Relationships": [{"Type": "CHILD", "Ids": ["38c8ec9d-64a4-4631-942f-db8334a3c7aa", "7ea028b9-19fe-483d-bf52-ee19d17d9102"]}]}, {"BlockType": "LINE", "Confidence": 90.30731964111328, "Text": "Previous -", "Geometry": {"BoundingBox": {"Width": 0.13814951479434967, "Height": 0.007955786772072315, "Left": 0.06667149811983109, "Top": 0.6308003067970276}, "Polygon": [{"X": 0.06667149811983109, "Y": 0.6308004856109619}, {"X": 0.2048209309577942, "Y": 0.6308003067970276}, {"X": 0.20482100546360016, "Y": 0.6387558579444885}, {"X": 0.06667156517505646, "Y": 0.6387560963630676}]}, "Id": "6375a271-31a7-40c5-b3c1-5ecffacf97f1", "Relationships": [{"Type": "CHILD", "Ids": ["02037ee9-4808-4383-b2ec-af3cd73f5dae", "826b3d50-3fc4-4097-a0da-a768a05d8dbe"]}]}, {"BlockType": "LINE", "Confidence": 97.18353271484375, "Text": "Inspected - No Damage Found", "Geometry": {"BoundingBox": {"Width": 0.37395310401916504, "Height": 0.009685158729553223, "Left": 0.06852547824382782, "Top": 0.6471375823020935}, "Polygon": [{"X": 0.06852547824382782, "Y": 0.6471382975578308}, {"X": 0.4424785077571869, "Y": 0.6471375823020935}, {"X": 0.44247859716415405, "Y": 0.6568218469619751}, {"X": 0.06852556765079498, "Y": 0.6568227410316467}]}, "Id": "67558427-4947-4b07-a06b-d3d89f5ac391", "Relationships": [{"Type": "CHILD", "Ids": ["5676caea-7a5d-4986-bf59-6fa4dcf2d12e", "67aeb28d-c509-47c3-8127-06e068bb4362", "2f1607e7-22c3-4431-b5f9-524916041047", "a750de0c-1dfc-43d8-a19d-7b5f07b1327d", "97a4c484-34cf-4078-8f59-b591304350ba"]}]}, {"BlockType": "LINE", "Confidence": 99.68431091308594, "Text": "Roadability:", "Geometry": {"BoundingBox": {"Width": 0.16363786160945892, "Height": 0.009600772522389889, "Left": 0.06622336804866791, "Top": 0.66310054063797}, "Polygon": [{"X": 0.06622336804866791, "Y": 0.6631009578704834}, {"X": 0.22986115515232086, "Y": 0.66310054063797}, {"X": 0.22986122965812683, "Y": 0.6727008819580078}, {"X": 0.06622345745563507, "Y": 0.672701358795166}]}, "Id": "f8bb39ed-dd6c-4e67-b603-f05f2687b6ef", "Relationships": [{"Type": "CHILD", "Ids": ["50047e0c-f75e-4a3b-8461-0f73ffb30ec6"]}]}, {"BlockType": "LINE", "Confidence": 99.64878845214844, "Text": "No Roadability Defects Reported", "Geometry": {"BoundingBox": {"Width": 0.4320249855518341, "Height": 0.009856642223894596, "Left": 0.06628839671611786, "Top": 0.6794352531433105}, "Polygon": [{"X": 0.06628839671611786, "Y": 0.6794365644454956}, {"X": 0.498313307762146, "Y": 0.6794352531433105}, {"X": 0.49831336736679077, "Y": 0.6892905235290527}, {"X": 0.06628848612308502, "Y": 0.6892919540405273}]}, "Id": "70cdcc26-1a5a-4fb6-8ae4-c5fa77eca642", "Relationships": [{"Type": "CHILD", "Ids": ["9268f378-aef5-46be-a77a-20b5ced93555", "211767f5-6f10-4db6-b1d5-1a86606892e7", "3097f5f1-0eb0-4f06-86cf-cdb6708865ff", "ccf2a07d-969f-4676-9b08-1e81a0fdfabc"]}]}, {"BlockType": "LINE", "Confidence": 99.45720672607422, "Text": "Driver :", "Geometry": {"BoundingBox": {"Width": 0.12093052268028259, "Height": 0.007897925563156605, "Left": 0.06685889512300491, "Top": 0.6952858567237854}, "Polygon": [{"X": 0.06685889512300491, "Y": 0.6952862739562988}, {"X": 0.18778935074806213, "Y": 0.6952858567237854}, {"X": 0.1877894252538681, "Y": 0.7031833529472351}, {"X": 0.06685896962881088, "Y": 0.7031837701797485}]}, "Id": "cb77ebc2-135e-4bea-8416-4180c0cf1809", "Relationships": [{"Type": "CHILD", "Ids": ["709ea30b-cec3-40da-8905-6b91e6cbe310", "96e91280-edde-414e-9fc9-7c12ffa63b01"]}]}, {"BlockType": "LINE", "Confidence": 99.89397430419922, "Text": "IEP DOT#: 1909823", "Geometry": {"BoundingBox": {"Width": 0.2337900549173355, "Height": 0.008443315513432026, "Left": 0.06813165545463562, "Top": 0.7109136581420898}, "Polygon": [{"X": 0.06813165545463562, "Y": 0.7109145522117615}, {"X": 0.30192163586616516, "Y": 0.7109136581420898}, {"X": 0.3019217252731323, "Y": 0.7193560004234314}, {"X": 0.06813173741102219, "Y": 0.7193569540977478}]}, "Id": "39010e56-cb58-4d16-885f-756d2f932d50", "Relationships": [{"Type": "CHILD", "Ids": ["ef1757c8-76cc-42ee-9177-6d08ecc8603e", "18aa289a-b420-44fd-8a05-2f31918c49c3", "048720c1-5e98-4d52-aa57-f535925f5aff"]}]}, {"BlockType": "WORD", "Confidence": 79.79911041259766, "Text": "R", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009888289496302605, "Height": 0.003928722348064184, "Left": 0.9128271341323853, "Top": 0.20693755149841309}, "Polygon": [{"X": 0.9128271341323853, "Y": 0.20693755149841309}, {"X": 0.922715425491333, "Y": 0.20693765580654144}, {"X": 0.922715425491333, "Y": 0.2108662724494934}, {"X": 0.9128271341323853, "Y": 0.21086616814136505}]}, "Id": "87ebac75-b0e0-42dc-9949-f8544b2c4fcc"}, {"BlockType": "WORD", "Confidence": 77.4170913696289, "Text": "BNSF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.8091115951538086, "Height": 0.026706647127866745, "Left": 0.1117909774184227, "Top": 0.20526422560214996}, "Polygon": [{"X": 0.1117909774184227, "Y": 0.20526422560214996}, {"X": 0.9209024310112, "Y": 0.20527330040931702}, {"X": 0.9209026098251343, "Y": 0.231970876455307}, {"X": 0.1117912232875824, "Y": 0.23196245729923248}]}, "Id": "f2811e5d-4a35-484b-b9b1-9e21c02fc8db"}, {"BlockType": "WORD", "Confidence": 95.82848358154297, "Text": "RAILWAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3560348451137543, "Height": 0.012528890743851662, "Left": 0.456515371799469, "Top": 0.24629908800125122}, "Polygon": [{"X": 0.456515371799469, "Y": 0.24629908800125122}, {"X": 0.8125501275062561, "Y": 0.24630263447761536}, {"X": 0.8125502467155457, "Y": 0.25882798433303833}, {"X": 0.45651546120643616, "Y": 0.25882455706596375}]}, "Id": "5f84d111-6a0d-433d-abfe-774b8e422363"}, {"BlockType": "WORD", "Confidence": 99.1651382446289, "Text": "IN-GATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12356900423765182, "Height": 0.008898710831999779, "Left": 0.43883252143859863, "Top": 0.2676897943019867}, "Polygon": [{"X": 0.43883252143859863, "Y": 0.2676897943019867}, {"X": 0.5624014735221863, "Y": 0.2676909267902374}, {"X": 0.562401533126831, "Y": 0.276588499546051}, {"X": 0.4388325810432434, "Y": 0.2765873670578003}]}, "Id": "87d0d5a5-89e0-4346-a3c7-aa64b18560ec"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BNSF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055360276252031326, "Height": 0.007277493830770254, "Left": 0.4166542887687683, "Top": 0.2938641309738159}, "Polygon": [{"X": 0.4166542887687683, "Y": 0.2938641309738159}, {"X": 0.47201451659202576, "Y": 0.2938646078109741}, {"X": 0.47201457619667053, "Y": 0.3011416494846344}, {"X": 0.4166543483734131, "Y": 0.3011411726474762}]}, "Id": "9d2cb2b7-d7d2-44a2-9662-a40b7d975449"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Railway", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09796439111232758, "Height": 0.00975430104881525, "Left": 0.4857015013694763, "Top": 0.29331114888191223}, "Polygon": [{"X": 0.4857015013694763, "Y": 0.29331114888191223}, {"X": 0.5836657881736755, "Y": 0.2933119833469391}, {"X": 0.5836659073829651, "Y": 0.3030654489994049}, {"X": 0.4857015609741211, "Y": 0.30306464433670044}]}, "Id": "fdc2aae4-85f3-4a17-88b7-fa53c2d90aca"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1245800256729126, "Height": 0.010136334225535393, "Left": 0.312275767326355, "Top": 0.30910518765449524}, "Polygon": [{"X": 0.312275767326355, "Y": 0.30910518765449524}, {"X": 0.4368557035923004, "Y": 0.3091062009334564}, {"X": 0.4368557929992676, "Y": 0.3192415237426758}, {"X": 0.31227585673332214, "Y": 0.319240540266037}]}, "Id": "09a6d730-75c2-4fff-9b2c-bc4ade144472"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1373906284570694, "Height": 0.010070309042930603, "Left": 0.4531587064266205, "Top": 0.3091075122356415}, "Polygon": [{"X": 0.4531587064266205, "Y": 0.3091075122356415}, {"X": 0.5905492305755615, "Y": 0.3091086149215698}, {"X": 0.5905493497848511, "Y": 0.3191778063774109}, {"X": 0.45315879583358765, "Y": 0.31917673349380493}]}, "Id": "14a5ddac-baf1-4c33-b9b4-37047f426257"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Record", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08339102566242218, "Height": 0.007871421054005623, "Left": 0.6048019528388977, "Top": 0.30926984548568726}, "Polygon": [{"X": 0.6048019528388977, "Y": 0.30926984548568726}, {"X": 0.6881929039955139, "Y": 0.3092705309391022}, {"X": 0.6881929636001587, "Y": 0.31714126467704773}, {"X": 0.6048020124435425, "Y": 0.3171406090259552}]}, "Id": "c60d7755-c9c6-4566-8b39-473aa05a89ed"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "YMMU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0551733635365963, "Height": 0.007237165700644255, "Left": 0.06692345440387726, "Top": 0.3418654203414917}, "Polygon": [{"X": 0.06692345440387726, "Y": 0.3418654203414917}, {"X": 0.12209675461053848, "Y": 0.34186580777168274}, {"X": 0.12209682166576385, "Y": 0.34910258650779724}, {"X": 0.06692352145910263, "Y": 0.3491021990776062}]}, "Id": "25866c42-3f3c-4a53-ae7b-657f5cbcaf4d"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "416178", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08110275864601135, "Height": 0.007716091815382242, "Left": 0.13711126148700714, "Top": 0.34135955572128296}, "Polygon": [{"X": 0.13711126148700714, "Y": 0.34135955572128296}, {"X": 0.21821396052837372, "Y": 0.3413601517677307}, {"X": 0.2182140350341797, "Y": 0.34907564520835876}, {"X": 0.1371113359928131, "Y": 0.3490750789642334}]}, "Id": "fa64b0c0-5de0-4380-87fa-93200dbe4f15"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "EMPTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06960640102624893, "Height": 0.00722555723041296, "Left": 0.23353220522403717, "Top": 0.3418417274951935}, "Polygon": [{"X": 0.23353220522403717, "Y": 0.3418417274951935}, {"X": 0.3031385540962219, "Y": 0.3418422043323517}, {"X": 0.3031386137008667, "Y": 0.3490672707557678}, {"X": 0.23353226482868195, "Y": 0.3490667939186096}]}, "Id": "c2ffbd31-6d63-4740-a801-7dc4a253afe6"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "MAEC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05565357580780983, "Height": 0.007204977795481682, "Left": 0.06615249067544937, "Top": 0.35825860500335693}, "Polygon": [{"X": 0.06615249067544937, "Y": 0.35825860500335693}, {"X": 0.12180600315332413, "Y": 0.3582589626312256}, {"X": 0.1218060627579689, "Y": 0.36546358466148376}, {"X": 0.06615255773067474, "Y": 0.3654632270336151}]}, "Id": "9b6949e1-a6d4-4979-ada0-75f97ba6945b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "692165", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08026830106973648, "Height": 0.007662774063646793, "Left": 0.13788293302059174, "Top": 0.35777467489242554}, "Polygon": [{"X": 0.13788293302059174, "Y": 0.35777467489242554}, {"X": 0.21815116703510284, "Y": 0.3577752113342285}, {"X": 0.21815122663974762, "Y": 0.36543744802474976}, {"X": 0.1378829926252365, "Y": 0.36543694138526917}]}, "Id": "25bf2d33-f650-4885-ad9c-2bc1a2b1bef1"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Ingate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09135361015796661, "Height": 0.009326519444584846, "Left": 0.06799522042274475, "Top": 0.3900870382785797}, "Polygon": [{"X": 0.06799522042274475, "Y": 0.3900870382785797}, {"X": 0.1593487560749054, "Y": 0.3900875449180603}, {"X": 0.15934883058071136, "Y": 0.3994135558605194}, {"X": 0.06799530982971191, "Y": 0.3994130492210388}]}, "Id": "8f7500cb-9693-4aef-90a4-7dd93dca655b"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "LOGPARCHI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12424632161855698, "Height": 0.007420937065035105, "Left": 0.1775718331336975, "Top": 0.3901536464691162}, "Polygon": [{"X": 0.1775718331336975, "Y": 0.3901536464691162}, {"X": 0.3018181025981903, "Y": 0.3901543617248535}, {"X": 0.3018181622028351, "Y": 0.39757460355758667}, {"X": 0.17757190763950348, "Y": 0.39757391810417175}]}, "Id": "00378a58-7b6b-429f-8ced-ff0589d12a18"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02605067752301693, "Height": 0.007156717590987682, "Left": 0.31891849637031555, "Top": 0.3901968002319336}, "Polygon": [{"X": 0.31891849637031555, "Y": 0.3901968002319336}, {"X": 0.34496909379959106, "Y": 0.39019694924354553}, {"X": 0.34496915340423584, "Y": 0.39735352993011475}, {"X": 0.3189185559749603, "Y": 0.3973533809185028}]}, "Id": "e8d3bfdd-101b-438d-bc18-a9ccaec5503e"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "08/13/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1366131603717804, "Height": 0.008642471395432949, "Left": 0.3609703481197357, "Top": 0.38932156562805176}, "Polygon": [{"X": 0.3609703481197357, "Y": 0.38932156562805176}, {"X": 0.49758344888687134, "Y": 0.38932234048843384}, {"X": 0.4975835084915161, "Y": 0.3979640305042267}, {"X": 0.3609704077243805, "Y": 0.397963285446167}]}, "Id": "6e81c2a4-8d7b-40f4-aa96-d19db096690c"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "1442", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052362166345119476, "Height": 0.007712852209806442, "Left": 0.5145495533943176, "Top": 0.3897472321987152}, "Polygon": [{"X": 0.5145495533943176, "Y": 0.3897472321987152}, {"X": 0.5669116377830505, "Y": 0.3897475302219391}, {"X": 0.5669116973876953, "Y": 0.39746010303497314}, {"X": 0.5145496129989624, "Y": 0.39745980501174927}]}, "Id": "e8600667-f913-463a-a929-9b9fb21447c4"}, {"BlockType": "WORD", "Confidence": 98.64476776123047, "Text": "Shipper:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10654047876596451, "Height": 0.01007484458386898, "Left": 0.06732454895973206, "Top": 0.40544235706329346}, "Polygon": [{"X": 0.06732454895973206, "Y": 0.40544235706329346}, {"X": 0.17386494576931, "Y": 0.4054429233074188}, {"X": 0.17386503517627716, "Y": 0.4155172109603882}, {"X": 0.06732464581727982, "Y": 0.4155166745185852}]}, "Id": "1f5e52b4-0404-4959-af82-6ba116a1c86b"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "YANGMINAMERI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16537834703922272, "Height": 0.0072106565348804, "Left": 0.19271451234817505, "Top": 0.4062150716781616}, "Polygon": [{"X": 0.19271451234817505, "Y": 0.4062150716781616}, {"X": 0.3580927848815918, "Y": 0.40621593594551086}, {"X": 0.3580928444862366, "Y": 0.4134257137775421}, {"X": 0.19271457195281982, "Y": 0.41342490911483765}]}, "Id": "2557f587-be91-48e1-91d6-10f175d98b5a"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Dest:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0656229704618454, "Height": 0.0074626081623137, "Left": 0.37305763363838196, "Top": 0.40605199337005615}, "Polygon": [{"X": 0.37305763363838196, "Y": 0.40605199337005615}, {"X": 0.4386805295944214, "Y": 0.4060523509979248}, {"X": 0.43868058919906616, "Y": 0.4135146141052246}, {"X": 0.37305769324302673, "Y": 0.41351428627967834}]}, "Id": "80c5de8f-a605-47fa-b72f-74d3e2a51f1a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LOGPARCHI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12420117855072021, "Height": 0.007240511942654848, "Left": 0.45704904198646545, "Top": 0.4062008857727051}, "Polygon": [{"X": 0.45704904198646545, "Y": 0.4062008857727051}, {"X": 0.5812501907348633, "Y": 0.4062015414237976}, {"X": 0.5812501907348633, "Y": 0.41344141960144043}, {"X": 0.45704910159111023, "Y": 0.4134407937526703}]}, "Id": "009f2041-c02b-4c47-8f60-0e86552523af"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026246942579746246, "Height": 0.00737467035651207, "Left": 0.5975170731544495, "Top": 0.4061018228530884}, "Polygon": [{"X": 0.5975170731544495, "Y": 0.4061018228530884}, {"X": 0.6237639784812927, "Y": 0.40610194206237793}, {"X": 0.6237640380859375, "Y": 0.41347649693489075}, {"X": 0.5975171327590942, "Y": 0.4134763479232788}]}, "Id": "add54372-f8d0-40cc-b455-6b532cda7df8"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Trucker:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1070210188627243, "Height": 0.007862058468163013, "Left": 0.06672006100416183, "Top": 0.4220048785209656}, "Polygon": [{"X": 0.06672006100416183, "Y": 0.4220048785209656}, {"X": 0.17374101281166077, "Y": 0.42200538516044617}, {"X": 0.17374107241630554, "Y": 0.4298669397830963}, {"X": 0.0667201355099678, "Y": 0.4298664331436157}]}, "Id": "b5c48e8c-5c0e-4755-bd4c-236683676290"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SEXS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054528966546058655, "Height": 0.007349994964897633, "Left": 0.19257670640945435, "Top": 0.4225149154663086}, "Polygon": [{"X": 0.19257670640945435, "Y": 0.4225149154663086}, {"X": 0.24710561335086823, "Y": 0.4225151836872101}, {"X": 0.247105672955513, "Y": 0.42986491322517395}, {"X": 0.19257676601409912, "Y": 0.42986467480659485}]}, "Id": "27415fbf-62ab-4d4d-a5b2-ed56c222ba76"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Tractor:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10757347196340561, "Height": 0.0073256599716842175, "Left": 0.26151564717292786, "Top": 0.4225507974624634}, "Polygon": [{"X": 0.26151564717292786, "Y": 0.4225507974624634}, {"X": 0.3690890669822693, "Y": 0.42255130410194397}, {"X": 0.36908912658691406, "Y": 0.429876446723938}, {"X": 0.26151570677757263, "Y": 0.4298759698867798}]}, "Id": "397284df-e274-4c23-b27a-825c6e03c49a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "AUTOGATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11172507703304291, "Height": 0.007183517329394817, "Left": 0.38642770051956177, "Top": 0.4225822389125824}, "Polygon": [{"X": 0.38642770051956177, "Y": 0.4225822389125824}, {"X": 0.4981527328491211, "Y": 0.422582745552063}, {"X": 0.49815279245376587, "Y": 0.4297657608985901}, {"X": 0.38642776012420654, "Y": 0.4297652542591095}]}, "Id": "2a33ae68-8f13-403d-aaf0-08c0999fa842"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10731687396764755, "Height": 0.0077404906041920185, "Left": 0.06653141230344772, "Top": 0.43807652592658997}, "Polygon": [{"X": 0.06653141230344772, "Y": 0.43807652592658997}, {"X": 0.1738482117652893, "Y": 0.4380769729614258}, {"X": 0.17384828627109528, "Y": 0.4458169937133789}, {"X": 0.0665314793586731, "Y": 0.4458165764808655}]}, "Id": "074c7523-7f74-4db8-9a8c-0206118be913"}, {"BlockType": "WORD", "Confidence": 95.42729949951172, "Text": "*****1271", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12168983370065689, "Height": 0.007605519611388445, "Left": 0.1936964988708496, "Top": 0.4381487965583801}, "Polygon": [{"X": 0.1936964988708496, "Y": 0.4381487965583801}, {"X": 0.31538626551628113, "Y": 0.4381493031978607}, {"X": 0.3153863251209259, "Y": 0.4457543194293976}, {"X": 0.19369657337665558, "Y": 0.445753812789917}]}, "Id": "4e8a4c87-525d-41fb-82f4-511202073ca9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "(Displaying", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1492658257484436, "Height": 0.009928626008331776, "Left": 0.3352644145488739, "Top": 0.4379042685031891}, "Polygon": [{"X": 0.3352644145488739, "Y": 0.4379042685031891}, {"X": 0.48453018069267273, "Y": 0.43790489435195923}, {"X": 0.4845302402973175, "Y": 0.4478328824043274}, {"X": 0.33526450395584106, "Y": 0.44783228635787964}]}, "Id": "459a8822-f4ed-4a1c-b5dd-fac874aad3cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04116710647940636, "Height": 0.007834844291210175, "Left": 0.4989117980003357, "Top": 0.4381100833415985}, "Polygon": [{"X": 0.4989117980003357, "Y": 0.4381100833415985}, {"X": 0.54007887840271, "Y": 0.43811026215553284}, {"X": 0.5400789380073547, "Y": 0.4459449350833893}, {"X": 0.49891185760498047, "Y": 0.44594478607177734}]}, "Id": "d212da9a-d899-4be6-ba8a-24e351447693"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "last", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05333405360579491, "Height": 0.0077665504068136215, "Left": 0.555949330329895, "Top": 0.4380941092967987}, "Polygon": [{"X": 0.555949330329895, "Y": 0.4380941092967987}, {"X": 0.6092833280563354, "Y": 0.4380943477153778}, {"X": 0.6092833876609802, "Y": 0.4458606541156769}, {"X": 0.5559493899345398, "Y": 0.44586044549942017}]}, "Id": "cb9be34b-ca01-4d4e-b08b-c0dd288e8ea9"}, {"BlockType": "WORD", "Confidence": 89.69895935058594, "Text": "*****4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08077520877122879, "Height": 0.00736625213176012, "Left": 0.6251943707466125, "Top": 0.4384070336818695}, "Polygon": [{"X": 0.6251943707466125, "Y": 0.4384070336818695}, {"X": 0.7059695720672607, "Y": 0.43840739130973816}, {"X": 0.7059695720672607, "Y": 0.44577330350875854}, {"X": 0.6251944303512573, "Y": 0.4457729756832123}]}, "Id": "34eff593-04b6-4caa-aebf-0d2e3fba357e"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "digits)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09366264939308167, "Height": 0.00994192250072956, "Left": 0.7216919660568237, "Top": 0.4378257691860199}, "Polygon": [{"X": 0.7216919660568237, "Y": 0.4378257691860199}, {"X": 0.815354585647583, "Y": 0.4378261864185333}, {"X": 0.8153546452522278, "Y": 0.4477677047252655}, {"X": 0.7216920256614685, "Y": 0.44776731729507446}]}, "Id": "4611e273-a01b-4ea3-b9f3-03083ba85d5d"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.064565010368824, "Height": 0.007833474315702915, "Left": 0.06721862405538559, "Top": 0.4539932310581207}, "Polygon": [{"X": 0.06721862405538559, "Y": 0.4539932310581207}, {"X": 0.13178355991840363, "Y": 0.45399346947669983}, {"X": 0.1317836344242096, "Y": 0.4618266820907593}, {"X": 0.06721869856119156, "Y": 0.46182647347450256}]}, "Id": "00eacbab-5b80-497d-9274-d7f717410822"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "IC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025900647044181824, "Height": 0.007277723401784897, "Left": 0.165655717253685, "Top": 0.4546036124229431}, "Polygon": [{"X": 0.165655717253685, "Y": 0.4546036124229431}, {"X": 0.19155630469322205, "Y": 0.45460373163223267}, {"X": 0.19155636429786682, "Y": 0.4618813395500183}, {"X": 0.16565579175949097, "Y": 0.46188125014305115}]}, "Id": "be2a1686-5607-4b3c-854e-c5f4951a201f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "From:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06525211036205292, "Height": 0.007478287443518639, "Left": 0.20607736706733704, "Top": 0.4544803500175476}, "Polygon": [{"X": 0.20607736706733704, "Y": 0.4544803500175476}, {"X": 0.271329402923584, "Y": 0.4544805884361267}, {"X": 0.27132949233055115, "Y": 0.461958646774292}, {"X": 0.2060774266719818, "Y": 0.4619584083557129}]}, "Id": "58101dc3-66e9-433c-850e-456d6cc3dc97"}, {"BlockType": "WORD", "Confidence": 99.36543273925781, "Text": "LGT-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05459316819906235, "Height": 0.007271813228726387, "Left": 0.06641772389411926, "Top": 0.4704934358596802}, "Polygon": [{"X": 0.06641772389411926, "Y": 0.4704934358596802}, {"X": 0.12101082503795624, "Y": 0.4704936146736145}, {"X": 0.12101089209318161, "Y": 0.4777652621269226}, {"X": 0.06641779094934464, "Y": 0.4777650833129883}]}, "Id": "7b044276-d74e-4329-931a-5c46c774bead"}, {"BlockType": "WORD", "Confidence": 99.49238586425781, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02528589405119419, "Height": 0.007586735766381025, "Left": 0.1372261792421341, "Top": 0.4701884388923645}, "Polygon": [{"X": 0.1372261792421341, "Y": 0.4701884388923645}, {"X": 0.16251200437545776, "Y": 0.47018852829933167}, {"X": 0.16251207888126373, "Y": 0.4777751863002777}, {"X": 0.13722625374794006, "Y": 0.47777509689331055}]}, "Id": "76c0fe38-14c9-4750-acc6-cd0722f83ff8"}, {"BlockType": "WORD", "Confidence": 96.88755798339844, "Text": "HT-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04057462885975838, "Height": 0.0072516812942922115, "Left": 0.17826682329177856, "Top": 0.4705547094345093}, "Polygon": [{"X": 0.17826682329177856, "Y": 0.4705547094345093}, {"X": 0.21884138882160187, "Y": 0.47055482864379883}, {"X": 0.21884146332740784, "Y": 0.4778063893318176}, {"X": 0.17826689779758453, "Y": 0.4778062701225281}]}, "Id": "7db4117b-12f1-41af-a6fd-c40f23b1e3a4"}, {"BlockType": "WORD", "Confidence": 99.69647216796875, "Text": "8.6", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038457468152046204, "Height": 0.0077278451062738895, "Left": 0.2352415919303894, "Top": 0.4701492190361023}, "Polygon": [{"X": 0.2352415919303894, "Y": 0.4701492190361023}, {"X": 0.27369898557662964, "Y": 0.47014936804771423}, {"X": 0.2736990451812744, "Y": 0.47787708044052124}, {"X": 0.23524165153503418, "Y": 0.4778769612312317}]}, "Id": "c7c03a69-7597-42c0-8359-6f520fad457a"}, {"BlockType": "WORD", "Confidence": 98.89608764648438, "Text": "KP-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04137325659394264, "Height": 0.007169569842517376, "Left": 0.2893833518028259, "Top": 0.47056129574775696}, "Polygon": [{"X": 0.2893833518028259, "Y": 0.47056129574775696}, {"X": 0.3307565450668335, "Y": 0.4705614447593689}, {"X": 0.33075660467147827, "Y": 0.4777308702468872}, {"X": 0.2893834114074707, "Y": 0.47773075103759766}]}, "Id": "0bc2a0dc-95b0-476e-b983-261b10318666"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "36", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024985268712043762, "Height": 0.007594674825668335, "Left": 0.34661170840263367, "Top": 0.47018879652023315}, "Polygon": [{"X": 0.34661170840263367, "Y": 0.47018879652023315}, {"X": 0.37159690260887146, "Y": 0.4701888859272003}, {"X": 0.37159696221351624, "Y": 0.4777834713459015}, {"X": 0.34661176800727844, "Y": 0.4777834117412567}]}, "Id": "0fcf6b6c-fda6-4687-97fe-719345961e04"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "AXL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04274413734674454, "Height": 0.007198840379714966, "Left": 0.38634806871414185, "Top": 0.47049447894096375}, "Polygon": [{"X": 0.38634806871414185, "Y": 0.47049447894096375}, {"X": 0.4290921688079834, "Y": 0.4704945981502533}, {"X": 0.4290921986103058, "Y": 0.4776933193206787}, {"X": 0.3863481283187866, "Y": 0.4776931703090668}]}, "Id": "5c45e336-9a70-438f-b907-f358d680a971"}, {"BlockType": "WORD", "Confidence": 99.13763427734375, "Text": "-096", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0536680705845356, "Height": 0.007553249131888151, "Left": 0.44399604201316833, "Top": 0.4702945053577423}, "Polygon": [{"X": 0.44399604201316833, "Y": 0.4702945053577423}, {"X": 0.49766406416893005, "Y": 0.47029468417167664}, {"X": 0.49766412377357483, "Y": 0.47784775495529175}, {"X": 0.4439961016178131, "Y": 0.4778475761413574}]}, "Id": "08aaa689-5fca-4576-90c0-af2774d9fa04"}, {"BlockType": "WORD", "Confidence": 99.5533676147461, "Text": "NU-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0415768176317215, "Height": 0.007443395908921957, "Left": 0.5118343830108643, "Top": 0.47043919563293457}, "Polygon": [{"X": 0.5118343830108643, "Y": 0.47043919563293457}, {"X": 0.5534111261367798, "Y": 0.4704393148422241}, {"X": 0.5534111857414246, "Y": 0.4778825640678406}, {"X": 0.511834442615509, "Y": 0.477882444858551}]}, "Id": "8d4ea2f1-6b30-4b34-a67a-610d1404bc30"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "WGT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05291106924414635, "Height": 0.007320260629057884, "Left": 0.06576299667358398, "Top": 0.48685699701309204}, "Polygon": [{"X": 0.06576299667358398, "Y": 0.48685699701309204}, {"X": 0.11867400258779526, "Y": 0.486857146024704}, {"X": 0.11867406219244003, "Y": 0.4941772520542145}, {"X": 0.06576306372880936, "Y": 0.49417710304260254}]}, "Id": "937913e2-5761-4eac-88a0-778a04de6418"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "TOFC-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0689951479434967, "Height": 0.0072347247041761875, "Left": 0.13630060851573944, "Top": 0.48691704869270325}, "Polygon": [{"X": 0.13630060851573944, "Y": 0.48691704869270325}, {"X": 0.20529569685459137, "Y": 0.48691725730895996}, {"X": 0.20529575645923615, "Y": 0.494151771068573}, {"X": 0.13630066812038422, "Y": 0.49415159225463867}]}, "Id": "baf833dc-0e3a-48dc-aadd-920db7f0d276"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "COFC-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06941131502389908, "Height": 0.00741805462166667, "Left": 0.23346486687660217, "Top": 0.48687657713890076}, "Polygon": [{"X": 0.23346486687660217, "Y": 0.48687657713890076}, {"X": 0.3028761148452759, "Y": 0.48687678575515747}, {"X": 0.30287617444992065, "Y": 0.4942946434020996}, {"X": 0.23346492648124695, "Y": 0.4942944645881653}]}, "Id": "28553f2a-4ad8-464e-bdba-b193f5a5858b"}, {"BlockType": "WORD", "Confidence": 99.65182495117188, "Text": "WGT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05228018760681152, "Height": 0.0072936718352139, "Left": 0.06584112346172333, "Top": 0.5029089450836182}, "Polygon": [{"X": 0.06584112346172333, "Y": 0.5029089450836182}, {"X": 0.11812124401330948, "Y": 0.5029090642929077}, {"X": 0.11812131106853485, "Y": 0.5102025866508484}, {"X": 0.0658411905169487, "Y": 0.5102025270462036}]}, "Id": "7d8cb60d-2359-4fae-aa28-152add74246b"}, {"BlockType": "WORD", "Confidence": 99.0344009399414, "Text": "G-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02674882486462593, "Height": 0.007162159774452448, "Left": 0.13626761734485626, "Top": 0.5030443072319031}, "Polygon": [{"X": 0.13626761734485626, "Y": 0.5030443072319031}, {"X": 0.16301637887954712, "Y": 0.5030443668365479}, {"X": 0.1630164384841919, "Y": 0.5102064609527588}, {"X": 0.13626767694950104, "Y": 0.510206401348114}]}, "Id": "d1483b2f-477c-4c8b-84d4-7da654ab845a"}, {"BlockType": "WORD", "Confidence": 99.23369598388672, "Text": "T-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027060935273766518, "Height": 0.007263579405844212, "Left": 0.19187192618846893, "Top": 0.5029414892196655}, "Polygon": [{"X": 0.19187192618846893, "Y": 0.5029414892196655}, {"X": 0.21893279254436493, "Y": 0.5029415488243103}, {"X": 0.2189328670501709, "Y": 0.5102050304412842}, {"X": 0.1918719857931137, "Y": 0.5102049708366394}]}, "Id": "2067dc54-0035-4c92-8774-2573199c7bee"}, {"BlockType": "WORD", "Confidence": 96.89253234863281, "Text": "C-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026658933609724045, "Height": 0.0072793071158230305, "Left": 0.24797669053077698, "Top": 0.5029253363609314}, "Polygon": [{"X": 0.24797669053077698, "Y": 0.5029253363609314}, {"X": 0.27463555335998535, "Y": 0.5029253959655762}, {"X": 0.2746356129646301, "Y": 0.5102046728134155}, {"X": 0.24797675013542175, "Y": 0.5102046132087708}]}, "Id": "3e82a757-1b20-49c1-b6cb-38f333539403"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "HAZMAT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08314745873212814, "Height": 0.007133671082556248, "Left": 0.06677938997745514, "Top": 0.5349438190460205}, "Polygon": [{"X": 0.06677938997745514, "Y": 0.5349438190460205}, {"X": 0.1499267816543579, "Y": 0.5349439382553101}, {"X": 0.14992684125900269, "Y": 0.5420774817466736}, {"X": 0.06677945703268051, "Y": 0.542077362537384}]}, "Id": "a6ac6d63-9f7b-4803-aebf-f406ae1ecd00"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Dec:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05148855969309807, "Height": 0.007327656261622906, "Left": 0.16416168212890625, "Top": 0.5349334478378296}, "Polygon": [{"X": 0.16416168212890625, "Y": 0.5349334478378296}, {"X": 0.21565018594264984, "Y": 0.5349335074424744}, {"X": 0.21565024554729462, "Y": 0.5422611236572266}, {"X": 0.16416175663471222, "Y": 0.5422610640525818}]}, "Id": "be6292d2-5915-475d-9ac9-43f8ae493354"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013624181039631367, "Height": 0.006926688831299543, "Left": 0.23370349407196045, "Top": 0.5351540446281433}, "Polygon": [{"X": 0.23370349407196045, "Y": 0.5351540446281433}, {"X": 0.24732762575149536, "Y": 0.5351540446281433}, {"X": 0.24732768535614014, "Y": 0.5420807003974915}, {"X": 0.23370355367660522, "Y": 0.5420807003974915}]}, "Id": "a02709be-8be7-49e4-8acc-7ca475360d02"}, {"BlockType": "WORD", "Confidence": 96.40306091308594, "Text": "Damage:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09263195097446442, "Height": 0.009257418103516102, "Left": 0.06708609312772751, "Top": 0.567176878452301}, "Polygon": [{"X": 0.06708609312772751, "Y": 0.567176878452301}, {"X": 0.15971796214580536, "Y": 0.567176878452301}, {"X": 0.15971803665161133, "Y": 0.576434314250946}, {"X": 0.06708617508411407, "Y": 0.5764342546463013}]}, "Id": "32072bae-a6c4-4d6c-8708-9b10708a70bb"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Current", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09693437814712524, "Height": 0.007442045491188765, "Left": 0.06652258336544037, "Top": 0.5832008719444275}, "Polygon": [{"X": 0.06652258336544037, "Y": 0.5832008719444275}, {"X": 0.16345690190792084, "Y": 0.5832008719444275}, {"X": 0.1634569615125656, "Y": 0.5906428694725037}, {"X": 0.06652265787124634, "Y": 0.5906429290771484}]}, "Id": "deeadb43-6c7d-430b-ad5d-9c8f0caaa2b0"}, {"BlockType": "WORD", "Confidence": 90.56441497802734, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01234498992562294, "Height": 0.002366391709074378, "Left": 0.17891265451908112, "Top": 0.5863065123558044}, "Polygon": [{"X": 0.17891265451908112, "Y": 0.5863065123558044}, {"X": 0.19125762581825256, "Y": 0.5863065123558044}, {"X": 0.19125764071941376, "Y": 0.5886728763580322}, {"X": 0.1789126694202423, "Y": 0.5886728763580322}]}, "Id": "ea065568-104a-41b8-b425-e434cc0e5e9d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PHOTOGRAPHIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16631709039211273, "Height": 0.007143075577914715, "Left": 0.06663818657398224, "Top": 0.5992878675460815}, "Polygon": [{"X": 0.06663818657398224, "Y": 0.5992879271507263}, {"X": 0.2329552322626114, "Y": 0.5992878675460815}, {"X": 0.23295529186725616, "Y": 0.6064308285713196}, {"X": 0.06663825362920761, "Y": 0.6064309477806091}]}, "Id": "c3db25c1-6a89-4479-8a1a-78dea045209b"}, {"BlockType": "WORD", "Confidence": 81.6368408203125, "Text": "INSPECTION -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16430312395095825, "Height": 0.00755054596811533, "Left": 0.24919520318508148, "Top": 0.5991098880767822}, "Polygon": [{"X": 0.24919520318508148, "Y": 0.5991100072860718}, {"X": 0.41349828243255615, "Y": 0.5991098880767822}, {"X": 0.4134983420372009, "Y": 0.6066603064537048}, {"X": 0.24919526278972626, "Y": 0.6066604256629944}]}, "Id": "c777b9c5-166c-443d-8740-f719e5b5f96a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1249178946018219, "Height": 0.01004171371459961, "Left": 0.42845627665519714, "Top": 0.5985442996025085}, "Polygon": [{"X": 0.42845627665519714, "Y": 0.5985443592071533}, {"X": 0.5533741116523743, "Y": 0.5985442996025085}, {"X": 0.553374171257019, "Y": 0.6085858941078186}, {"X": 0.4284563362598419, "Y": 0.6085860133171082}]}, "Id": "73f1be9d-a63b-46fa-aed1-ebbf4ea9887f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "defects", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09637561440467834, "Height": 0.00815624464303255, "Left": 0.5682578682899475, "Top": 0.5986154675483704}, "Polygon": [{"X": 0.5682578682899475, "Y": 0.5986155271530151}, {"X": 0.6646334528923035, "Y": 0.5986154675483704}, {"X": 0.6646335124969482, "Y": 0.6067716479301453}, {"X": 0.5682579278945923, "Y": 0.60677170753479}]}, "Id": "1f370d32-b6df-49ac-b203-f37055ed3363"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04086104780435562, "Height": 0.005916612222790718, "Left": 0.680266797542572, "Top": 0.6006024479866028}, "Polygon": [{"X": 0.680266797542572, "Y": 0.6006024479866028}, {"X": 0.7211278080940247, "Y": 0.6006024479866028}, {"X": 0.7211278080940247, "Y": 0.6065189838409424}, {"X": 0.680266797542572, "Y": 0.6065190434455872}]}, "Id": "3a131f59-c0b9-4f7f-8026-d8b2e59c7323"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11149716377258301, "Height": 0.009583630599081516, "Left": 0.7357311844825745, "Top": 0.598892092704773}, "Polygon": [{"X": 0.7357311844825745, "Y": 0.5988921523094177}, {"X": 0.8472282886505127, "Y": 0.598892092704773}, {"X": 0.8472283482551575, "Y": 0.6084756851196289}, {"X": 0.7357312440872192, "Y": 0.6084757447242737}]}, "Id": "51ffee41-6f4b-47e1-90aa-e5fb91b103c8"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027399402111768723, "Height": 0.005846453830599785, "Left": 0.8612514734268188, "Top": 0.6005455255508423}, "Polygon": [{"X": 0.8612514734268188, "Y": 0.6005455851554871}, {"X": 0.8886508941650391, "Y": 0.6005455255508423}, {"X": 0.8886508941650391, "Y": 0.606391966342926}, {"X": 0.8612515330314636, "Y": 0.6063920259475708}]}, "Id": "6d92bbeb-5817-4e4f-9f6a-a92c04dfba43"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11032381653785706, "Height": 0.007953734137117863, "Left": 0.0673421174287796, "Top": 0.6149188876152039}, "Polygon": [{"X": 0.0673421174287796, "Y": 0.6149190068244934}, {"X": 0.1776658594608307, "Y": 0.6149188876152039}, {"X": 0.17766593396663666, "Y": 0.622872531414032}, {"X": 0.06734218448400497, "Y": 0.6228726506233215}]}, "Id": "38c8ec9d-64a4-4631-942f-db8334a3c7aa"}, {"BlockType": "WORD", "Confidence": 99.12308502197266, "Text": "images.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0917135700583458, "Height": 0.009813559241592884, "Left": 0.1932535320520401, "Top": 0.6147170662879944}, "Polygon": [{"X": 0.1932535320520401, "Y": 0.6147171258926392}, {"X": 0.2849670350551605, "Y": 0.6147170662879944}, {"X": 0.2849670946598053, "Y": 0.6245304942131042}, {"X": 0.19325362145900726, "Y": 0.6245306134223938}]}, "Id": "7ea028b9-19fe-483d-bf52-ee19d17d9102"}, {"BlockType": "WORD", "Confidence": 99.56314086914062, "Text": "Previous", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11071793735027313, "Height": 0.007955745793879032, "Left": 0.06667149811983109, "Top": 0.6308003067970276}, "Polygon": [{"X": 0.06667149811983109, "Y": 0.6308004856109619}, {"X": 0.17738936841487885, "Y": 0.6308003067970276}, {"X": 0.17738942801952362, "Y": 0.6387558579444885}, {"X": 0.06667156517505646, "Y": 0.6387560963630676}]}, "Id": "02037ee9-4808-4383-b2ec-af3cd73f5dae"}, {"BlockType": "WORD", "Confidence": 81.05149841308594, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012050786055624485, "Height": 0.00169064337387681, "Left": 0.19277019798755646, "Top": 0.6350849270820618}, "Polygon": [{"X": 0.19277019798755646, "Y": 0.6350849866867065}, {"X": 0.20482097566127777, "Y": 0.6350849270820618}, {"X": 0.20482099056243896, "Y": 0.636775553226471}, {"X": 0.19277021288871765, "Y": 0.6367756128311157}]}, "Id": "826b3d50-3fc4-4097-a0da-a768a05d8dbe"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Inspected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1312677413225174, "Height": 0.009467250667512417, "Left": 0.06852547824382782, "Top": 0.6473554968833923}, "Polygon": [{"X": 0.06852547824382782, "Y": 0.6473557353019714}, {"X": 0.19979314506053925, "Y": 0.6473554968833923}, {"X": 0.19979321956634521, "Y": 0.6568224430084229}, {"X": 0.06852556765079498, "Y": 0.6568227410316467}]}, "Id": "5676caea-7a5d-4986-bf59-6fa4dcf2d12e"}, {"BlockType": "WORD", "Confidence": 86.7343978881836, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012426775880157948, "Height": 0.002270860830321908, "Left": 0.20667919516563416, "Top": 0.6508283615112305}, "Polygon": [{"X": 0.20667919516563416, "Y": 0.6508283615112305}, {"X": 0.21910595893859863, "Y": 0.6508283615112305}, {"X": 0.21910597383975983, "Y": 0.6530991792678833}, {"X": 0.20667921006679535, "Y": 0.6530992388725281}]}, "Id": "67aeb28d-c509-47c3-8127-06e068bb4362"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02786347083747387, "Height": 0.007305776700377464, "Left": 0.233542338013649, "Top": 0.6475440263748169}, "Polygon": [{"X": 0.233542338013649, "Y": 0.6475440859794617}, {"X": 0.2614057660102844, "Y": 0.6475440263748169}, {"X": 0.2614058256149292, "Y": 0.6548497080802917}, {"X": 0.23354241251945496, "Y": 0.6548497676849365}]}, "Id": "2f1607e7-22c3-4431-b5f9-524916041047"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08279287070035934, "Height": 0.00911432784050703, "Left": 0.27599501609802246, "Top": 0.6476717591285706}, "Polygon": [{"X": 0.27599501609802246, "Y": 0.6476719379425049}, {"X": 0.35878780484199524, "Y": 0.6476717591285706}, {"X": 0.3587878942489624, "Y": 0.6567859053611755}, {"X": 0.2759951055049896, "Y": 0.6567860841751099}]}, "Id": "a750de0c-1dfc-43d8-a19d-7b5f07b1327d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Found", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06939487159252167, "Height": 0.00773957883939147, "Left": 0.3730837106704712, "Top": 0.6471375823020935}, "Polygon": [{"X": 0.3730837106704712, "Y": 0.6471377015113831}, {"X": 0.4424785077571869, "Y": 0.6471375823020935}, {"X": 0.44247856736183167, "Y": 0.6548770070075989}, {"X": 0.37308377027511597, "Y": 0.6548771262168884}]}, "Id": "97a4c484-34cf-4078-8f59-b591304350ba"}, {"BlockType": "WORD", "Confidence": 99.68431091308594, "Text": "Roadability:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16363786160945892, "Height": 0.009600772522389889, "Left": 0.06622336804866791, "Top": 0.66310054063797}, "Polygon": [{"X": 0.06622336804866791, "Y": 0.6631009578704834}, {"X": 0.22986115515232086, "Y": 0.66310054063797}, {"X": 0.22986122965812683, "Y": 0.6727008819580078}, {"X": 0.06622345745563507, "Y": 0.672701358795166}]}, "Id": "50047e0c-f75e-4a3b-8461-0f73ffb30ec6"}, {"BlockType": "WORD", "Confidence": 98.60491180419922, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027502603828907013, "Height": 0.007487867958843708, "Left": 0.06628839671611786, "Top": 0.6799670457839966}, "Polygon": [{"X": 0.06628839671611786, "Y": 0.6799671053886414}, {"X": 0.0937909334897995, "Y": 0.6799670457839966}, {"X": 0.09379100054502487, "Y": 0.6874548196792603}, {"X": 0.06628846377134323, "Y": 0.6874549388885498}]}, "Id": "9268f378-aef5-46be-a77a-20b5ced93555"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Roadability", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.153622105717659, "Height": 0.009855813346803188, "Left": 0.10820027440786362, "Top": 0.6794359683990479}, "Polygon": [{"X": 0.10820027440786362, "Y": 0.679436445236206}, {"X": 0.26182231307029724, "Y": 0.6794359683990479}, {"X": 0.261822372674942, "Y": 0.6892912983894348}, {"X": 0.10820036381483078, "Y": 0.689291775226593}]}, "Id": "211767f5-6f10-4db6-b1d5-1a86606892e7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Defects", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09576736390590668, "Height": 0.007888374850153923, "Left": 0.2759510278701782, "Top": 0.679548978805542}, "Polygon": [{"X": 0.2759510278701782, "Y": 0.6795492768287659}, {"X": 0.37171831727027893, "Y": 0.679548978805542}, {"X": 0.3717183768749237, "Y": 0.6874370574951172}, {"X": 0.275951087474823, "Y": 0.6874373555183411}]}, "Id": "3097f5f1-0eb0-4f06-86cf-cdb6708865ff"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Reported", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11137610673904419, "Height": 0.00964435562491417, "Left": 0.3869372606277466, "Top": 0.6794779896736145}, "Polygon": [{"X": 0.3869372606277466, "Y": 0.6794782876968384}, {"X": 0.498313307762146, "Y": 0.6794779896736145}, {"X": 0.49831336736679077, "Y": 0.6891219615936279}, {"X": 0.38693735003471375, "Y": 0.6891223192214966}]}, "Id": "ccf2a07d-969f-4676-9b08-1e81a0fdfabc"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08255026489496231, "Height": 0.007897795177996159, "Left": 0.06685889512300491, "Top": 0.695285975933075}, "Polygon": [{"X": 0.06685889512300491, "Y": 0.6952862739562988}, {"X": 0.14940908551216125, "Y": 0.695285975933075}, {"X": 0.14940916001796722, "Y": 0.7031834721565247}, {"X": 0.06685896962881088, "Y": 0.7031837701797485}]}, "Id": "709ea30b-cec3-40da-8905-6b91e6cbe310"}, {"BlockType": "WORD", "Confidence": 99.0234375, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005499562714248896, "Height": 0.005155513994395733, "Left": 0.18228985369205475, "Top": 0.6979404091835022}, "Polygon": [{"X": 0.18228985369205475, "Y": 0.6979404091835022}, {"X": 0.18778938055038452, "Y": 0.6979404091835022}, {"X": 0.1877894252538681, "Y": 0.7030959129333496}, {"X": 0.18228989839553833, "Y": 0.7030959129333496}]}, "Id": "96e91280-edde-414e-9fc9-7c12ffa63b01"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "IEP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03918862342834473, "Height": 0.006888851523399353, "Left": 0.06813167035579681, "Top": 0.7120257019996643}, "Polygon": [{"X": 0.06813167035579681, "Y": 0.7120258808135986}, {"X": 0.10732023417949677, "Y": 0.7120257019996643}, {"X": 0.10732029378414154, "Y": 0.7189143896102905}, {"X": 0.06813172996044159, "Y": 0.7189145684242249}]}, "Id": "ef1757c8-76cc-42ee-9177-6d08ecc8603e"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "DOT#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06547030806541443, "Height": 0.00844265054911375, "Left": 0.12231358140707016, "Top": 0.7109140753746033}, "Polygon": [{"X": 0.12231358140707016, "Y": 0.7109143733978271}, {"X": 0.18778382241725922, "Y": 0.7109140753746033}, {"X": 0.18778389692306519, "Y": 0.7193564772605896}, {"X": 0.12231365591287613, "Y": 0.7193567752838135}]}, "Id": "18aa289a-b420-44fd-8a05-2f31918c49c3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1909823", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0942058265209198, "Height": 0.007853712886571884, "Left": 0.20771588385105133, "Top": 0.7114921808242798}, "Polygon": [{"X": 0.20771588385105133, "Y": 0.7114925980567932}, {"X": 0.30192163586616516, "Y": 0.7114921808242798}, {"X": 0.3019217252731323, "Y": 0.7193455100059509}, {"X": 0.2077159583568573, "Y": 0.7193459272384644}]}, "Id": "048720c1-5e98-4d52-aa57-f535925f5aff"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "904f6bc4-2ad8-4fc1-b739-fc733693eb6e", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "904f6bc4-2ad8-4fc1-b739-fc733693eb6e", "content-type": "application/x-amz-json-1.1", "content-length": "60121", "date": "Wed, 24 Sep 2025 13:28:34 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ea28930c-3d52-4624-a7d8-d4499ee06ddc", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:28:38 GMT", "content-type": "application/json", "content-length": "557", "connection": "keep-alive", "x-amzn-requestid": "ea28930c-3d52-4624-a7d8-d4499ee06ddc"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. Keywords: \"IN-GATE\", \"Equipment Inspection Record\", \"Ingate:\". So likely ingate document. So doc_type \"ingate\". Output JSON with one entry."}}}, {"text": "{\n  \"documents\": [\n    {\n      \"page_no\": 1,\n      \"doc_type\": \"ingate\"\n    }\n  ]\n}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2789, "outputTokens": 80, "totalTokens": 2869}, "metrics": {"latencyMs": 602}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/543ced16_HGPDC5MO1QA1H930SDUI.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nR\nBNSF\nRAILWAY\nIN-GATE\nBNSF Railway\nEquipment Inspection Record\nYMMU 416178 EMPTY\nMAEC 692165\nIngate: LOGPARCHI IL 08/13/2025 1442\nShipper: YANGMINAMERI Dest: LO<PERSON>ARCHI IL\nTrucker: SEXS Tractor: AUTOGATE\nLicense:\n*****1271 (Displaying the last *****4 digits)\nSeal: IC From:\nLGT- 40 HT- 8.6 KP- 36 AXL -096 NU-\nWGT: TOFC- COFC-\nWGT: G- T- C-\nHAZMAT Dec: N\nDamage:\nCurrent -\nPHOTOGRAPHIC INSPECTION - Equipment defects are captured on\nrecorded images.\nPrevious -\nInspected - No Damage Found\nRoadability:\nNo Roadability Defects Reported\nDriver :\nIEP DOT#: 1909823\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}