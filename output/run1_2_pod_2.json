{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pod"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9467098116874695, "Height": 0.9212359189987183, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.5061863450682722e-05, "Y": 0.0}, {"X": 0.932135820388794, "Y": 0.01744088903069496}, {"X": 0.9467098116874695, "Y": 0.8992170691490173}, {"X": 0.0, "Y": 0.9212359189987183}]}, "Id": "4e8b4f1d-629d-41c6-a5bd-9d325144d5d3", "Relationships": [{"Type": "CHILD", "Ids": ["cb274eb8-6ee7-4f49-aa47-dedfd6b27485", "337e0720-1594-413f-979e-a36b52723450", "5a112f68-8ad8-4c16-a303-a15bd73c0da1", "5696039f-f309-4af8-bbcb-9c29df44db23", "f5b74f4b-522b-43b0-a000-57a4cbd63828", "b63890eb-5fda-417a-8ff8-3de3458cee28", "3f69ec1e-8a10-434d-9f3d-8e32068adc41", "76d85cbc-7c9a-4130-9bc0-2a7f9ba29bac", "868a8f21-b442-45f6-981c-3f5da2f13d78", "596dd04b-ca8a-47d3-beac-51edc4f2fa4a", "b44e948c-c4cf-4f89-8286-4b050c3afc55", "018f32e0-21d9-42bd-adc7-42ba930a9f72", "38010a0e-7ee0-4255-b216-6d2035bb3fca", "d3787c41-c2c0-436d-a198-aa0af3acdda0", "2f83e3df-fa66-4cf9-b9e2-7e76c8eb80e1", "deb8b369-8d3e-41ae-a8c4-cc9571185aec", "e0dca055-7d6d-4c2a-a148-65dc6117a1d1", "2dd48268-5de1-4610-9143-f4cab1a6f376", "5a41e042-48da-4b67-b7bb-7830118e99f1", "51a65a07-662e-4fbe-8f8c-0d17ca333ce8", "10e32ef9-0a5d-4510-93fe-e832aca8c0ad", "423210d4-fa66-4ca2-9e7d-2ab34877c26c", "58eb2d5a-5ba0-4c14-82e5-f85d21c23e69", "6158eade-6b10-4bd0-a54c-a65d3c4982a5", "bbe69fab-d11c-4871-9b5f-2c3c39d68ccd", "02ecfcb5-6377-4774-81d5-684471d55408", "11402d37-b758-4304-a941-150d10a15fe6", "c0cafd10-c52f-42a9-b1ec-2c34e88fa579", "4b013f0a-40cc-41bf-9c1c-99fe727ef3f2", "f7b6c283-5be4-4845-9895-9c274f43902c", "944ee5f4-428d-46d2-815b-9f311e15ede3", "350ffcbb-36d5-4535-bc7d-62cac4cd2549", "7386a598-be6c-4145-aa24-46ce5941f0cb", "b2091935-84e0-43a2-907a-b18df308eb12", "4f9b3b4c-7442-4040-95ca-aafab0cbb0d7", "8219b3f1-b17b-493e-a3d4-6bcc3cca8796", "91cf4bf6-8030-4dad-bb6f-97685b798ff4", "9e8abdf8-23ed-43f5-ac03-c4e3fe4c07a8", "cf1519ff-9dd2-44f6-a4ac-f4f948909900", "24481b4e-5b54-4983-b61a-7c3124ed23e4", "17fac2a9-7b6c-4f6e-a90c-c05f86a1a8a1", "356d1699-8e10-417f-9433-d337fdf638e6", "d9a58374-7f7c-4900-ad54-f0c6b6b95a05", "6e1b84c2-dcf9-4a42-8bd6-98ae03d1d92a", "45b32784-fe66-4cbd-ac24-fe7c7e367d35", "abbd2b1f-f28a-414c-9302-a0e35b489f72", "88172dbe-44cb-48fd-a4d1-2bd49952abb5", "d889eb82-a511-4eb8-bed1-43abf4ae6ddc", "70781b15-bb8f-4ed9-9942-9976f93f8c11", "8c84ca63-2d4e-4a50-8c3b-a15bd8745f62", "36b4cae8-b526-4853-a51d-c4e537936467", "537a7719-e2fa-494d-869f-ca65e941a7b5", "bbf9f69f-b768-4f1c-aa1b-78ef2f646017"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "DELIVERY TICKET", "Geometry": {"BoundingBox": {"Width": 0.21212910115718842, "Height": 0.029848236590623856, "Left": 0.3543730080127716, "Top": 0.05660296231508255}, "Polygon": [{"X": 0.3543730080127716, "Y": 0.05660296231508255}, {"X": 0.5662798881530762, "Y": 0.06103134900331497}, {"X": 0.5665020942687988, "Y": 0.0864512026309967}, {"X": 0.3544820547103882, "Y": 0.08230089396238327}]}, "Id": "cb274eb8-6ee7-4f49-aa47-dedfd6b27485", "Relationships": [{"Type": "CHILD", "Ids": ["2049413e-5940-45b7-bd11-740ecf0edf93", "29c214dd-9fe4-4734-ad9f-e7353c0b51e7"]}]}, {"BlockType": "LINE", "Confidence": 99.71520233154297, "Text": "VENTURE", "Geometry": {"BoundingBox": {"Width": 0.10643017292022705, "Height": 0.022257667034864426, "Left": 0.18954220414161682, "Top": 0.11186268925666809}, "Polygon": [{"X": 0.18954220414161682, "Y": 0.11186268925666809}, {"X": 0.29591134190559387, "Y": 0.11376683413982391}, {"X": 0.29597237706184387, "Y": 0.13412036001682281}, {"X": 0.18955738842487335, "Y": 0.13232654333114624}]}, "Id": "337e0720-1594-413f-979e-a36b52723450", "Relationships": [{"Type": "CHILD", "Ids": ["573c3ed2-71fd-4d4b-a0c8-61232f8973f2"]}]}, {"BlockType": "LINE", "Confidence": 97.4364242553711, "Text": "METALS+", "Geometry": {"BoundingBox": {"Width": 0.10320871323347092, "Height": 0.01862342283129692, "Left": 0.19068047404289246, "Top": 0.12992437183856964}, "Polygon": [{"X": 0.19068047404289246, "Y": 0.12992437183856964}, {"X": 0.29383936524391174, "Y": 0.13167604804039001}, {"X": 0.29388919472694397, "Y": 0.14854779839515686}, {"X": 0.1906934678554535, "Y": 0.14688482880592346}]}, "Id": "5a112f68-8ad8-4c16-a303-a15bd73c0da1", "Relationships": [{"Type": "CHILD", "Ids": ["b0318cc9-2010-42cb-bc17-bf76aeaf8a8d"]}]}, {"BlockType": "LINE", "Confidence": 51.895694732666016, "Text": "/ I I", "Geometry": {"BoundingBox": {"Width": 0.06662678718566895, "Height": 0.009281681850552559, "Left": 0.22219237685203552, "Top": 0.14659902453422546}, "Polygon": [{"X": 0.22219237685203552, "Y": 0.14659902453422546}, {"X": 0.2887957990169525, "Y": 0.147675022482872}, {"X": 0.28881916403770447, "Y": 0.15588070452213287}, {"X": 0.22220417857170105, "Y": 0.15483257174491882}]}, "Id": "5696039f-f309-4af8-bbcb-9c29df44db23", "Relationships": [{"Type": "CHILD", "Ids": ["9455a8f1-73b3-4c6f-afaa-db8b1fdbcd32", "43ce2085-01a0-458c-b563-d99c8bf9a0ba", "0f0758fe-29dd-4771-adf7-fcaa87cfbff2"]}]}, {"BlockType": "LINE", "Confidence": 99.67743682861328, "Text": "Wills Point", "Geometry": {"BoundingBox": {"Width": 0.07687439769506454, "Height": 0.013910086825489998, "Left": 0.07186924666166306, "Top": 0.17405062913894653}, "Polygon": [{"X": 0.07189172506332397, "Y": 0.17405062913894653}, {"X": 0.1487436443567276, "Y": 0.17517566680908203}, {"X": 0.14874206483364105, "Y": 0.18796071410179138}, {"X": 0.07186924666166306, "Y": 0.18688541650772095}]}, "Id": "f5b74f4b-522b-43b0-a000-57a4cbd63828", "Relationships": [{"Type": "CHILD", "Ids": ["36310924-0865-4f44-b9ce-0a04678c78b4", "dbb8681c-eada-47d0-bc1a-40375157a25a"]}]}, {"BlockType": "LINE", "Confidence": 99.75102233886719, "Text": "503 Co Rd 3805", "Geometry": {"BoundingBox": {"Width": 0.1183370053768158, "Height": 0.01595895178616047, "Left": 0.07209765911102295, "Top": 0.18929846584796906}, "Polygon": [{"X": 0.07212280482053757, "Y": 0.18929846584796906}, {"X": 0.19042378664016724, "Y": 0.1909387856721878}, {"X": 0.19043466448783875, "Y": 0.20525741577148438}, {"X": 0.07209765911102295, "Y": 0.2037031054496765}]}, "Id": "b63890eb-5fda-417a-8ff8-3de3458cee28", "Relationships": [{"Type": "CHILD", "Ids": ["9cc68d68-f4b1-4f4b-8c14-0d6bfa965665", "720e1b2f-771d-4937-b27f-2219ece281d0", "7b71789f-f51b-408b-96bb-0b90ad3cc65f", "6395149f-f023-40e3-8861-a6dca56f3900"]}]}, {"BlockType": "LINE", "Confidence": 99.06210327148438, "Text": "Shipment #", "Geometry": {"BoundingBox": {"Width": 0.07423777133226395, "Height": 0.013291774317622185, "Left": 0.5595621466636658, "Top": 0.19818413257598877}, "Polygon": [{"X": 0.5595621466636658, "Y": 0.19818413257598877}, {"X": 0.6336754560470581, "Y": 0.19920355081558228}, {"X": 0.6337999105453491, "Y": 0.2114758938550949}, {"X": 0.5596678256988525, "Y": 0.2105037271976471}]}, "Id": "3f69ec1e-8a10-434d-9f3d-8e32068adc41", "Relationships": [{"Type": "CHILD", "Ids": ["ceb780a1-afb1-44b7-86df-8a7021d15fd2", "eae73a2d-3ca2-464b-bca9-4961ecf71dea"]}]}, {"BlockType": "LINE", "Confidence": 96.90668487548828, "Text": "Wills Point, TX USA 75169", "Geometry": {"BoundingBox": {"Width": 0.19166482985019684, "Height": 0.01954401656985283, "Left": 0.0717538595199585, "Top": 0.2046458125114441}, "Polygon": [{"X": 0.07178401201963425, "Y": 0.2046458125114441}, {"X": 0.26337945461273193, "Y": 0.20715315639972687}, {"X": 0.26341870427131653, "Y": 0.22418983280658722}, {"X": 0.0717538595199585, "Y": 0.22184887528419495}]}, "Id": "76d85cbc-7c9a-4130-9bc0-2a7f9ba29bac", "Relationships": [{"Type": "CHILD", "Ids": ["99e53e3a-5d15-479d-90bd-5fae292e706b", "2db84b49-e74d-42c7-87b8-27fa6a800b7a", "cda5defb-4871-4751-a0cc-90fdb73cde19", "03408fe6-3012-4abe-a21d-f0ee315c72a4", "98658789-9e44-4ec9-b9be-3022a78482c1"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "903-873-3811", "Geometry": {"BoundingBox": {"Width": 0.09940139949321747, "Height": 0.014773895032703876, "Left": 0.07251351326704025, "Top": 0.22209669649600983}, "Polygon": [{"X": 0.07253717631101608, "Y": 0.22209669649600983}, {"X": 0.17190992832183838, "Y": 0.2233092188835144}, {"X": 0.17191490530967712, "Y": 0.23687060177326202}, {"X": 0.07251351326704025, "Y": 0.2357264757156372}]}, "Id": "868a8f21-b442-45f6-981c-3f5da2f13d78", "Relationships": [{"Type": "CHILD", "Ids": ["eaceb478-729b-4139-94d1-bccae7b4d4c7"]}]}, {"BlockType": "LINE", "Confidence": 90.61444091796875, "Text": "WIL106604", "Geometry": {"BoundingBox": {"Width": 0.12030307203531265, "Height": 0.01689854823052883, "Left": 0.558576226234436, "Top": 0.22992601990699768}, "Polygon": [{"X": 0.558576226234436, "Y": 0.22992601990699768}, {"X": 0.678708016872406, "Y": 0.23137997090816498}, {"X": 0.6788793206214905, "Y": 0.24682457745075226}, {"X": 0.5587090849876404, "Y": 0.24546730518341064}]}, "Id": "596dd04b-ca8a-47d3-beac-51edc4f2fa4a", "Relationships": [{"Type": "CHILD", "Ids": ["01ecb6da-1f27-4d4e-98ee-fc3e02488ac9"]}]}, {"BlockType": "LINE", "Confidence": 99.7048110961914, "Text": "Contract Luvata Appleton May 2025 PO 303735", "Geometry": {"BoundingBox": {"Width": 0.34780997037887573, "Height": 0.022704176604747772, "Left": 0.07299509644508362, "Top": 0.27489933371543884}, "Polygon": [{"X": 0.07302911579608917, "Y": 0.27489933371543884}, {"X": 0.4206959903240204, "Y": 0.2782100439071655}, {"X": 0.42080506682395935, "Y": 0.2976034879684448}, {"X": 0.07299509644508362, "Y": 0.29463979601860046}]}, "Id": "b44e948c-c4cf-4f89-8286-4b050c3afc55", "Relationships": [{"Type": "CHILD", "Ids": ["6a006e86-5b4a-448c-b287-7f3d20f20810", "f8116a0d-5ce8-450b-b530-d176efa67c69", "5e95e60a-6755-4195-b21a-e6b5cd422de4", "ef4a04cf-3499-4775-990d-76dc081d20f6", "c161a74c-6592-4e6d-aac2-24e86ad3b7d6", "dd41b924-7b9b-4405-aedb-3398f90164b2", "75f1a607-2340-47da-af29-a036f4e864be"]}]}, {"BlockType": "LINE", "Confidence": 99.4591064453125, "Text": "Contract # 14307", "Geometry": {"BoundingBox": {"Width": 0.12391911447048187, "Height": 0.01125797163695097, "Left": 0.5582339763641357, "Top": 0.29241207242012024}, "Polygon": [{"X": 0.5582339763641357, "Y": 0.29241207242012024}, {"X": 0.6820397973060608, "Y": 0.29350799322128296}, {"X": 0.6821531057357788, "Y": 0.3036700487136841}, {"X": 0.5583212375640869, "Y": 0.30263975262641907}]}, "Id": "018f32e0-21d9-42bd-adc7-42ba930a9f72", "Relationships": [{"Type": "CHILD", "Ids": ["10af7493-fe94-48d8-9e1a-a027139a55d0", "45978053-ef5e-48e9-8125-9e3b749f88ac", "117cd469-436b-4939-a110-fe683571775d"]}]}, {"BlockType": "LINE", "Confidence": 99.0147705078125, "Text": "Pack List #: 22234", "Geometry": {"BoundingBox": {"Width": 0.1350330114364624, "Height": 0.011375267058610916, "Left": 0.547493040561676, "Top": 0.307947039604187}, "Polygon": [{"X": 0.547493040561676, "Y": 0.307947039604187}, {"X": 0.682411253452301, "Y": 0.3090316951274872}, {"X": 0.6825259923934937, "Y": 0.3193223178386688}, {"X": 0.5475790500640869, "Y": 0.3183101415634155}]}, "Id": "38010a0e-7ee0-4255-b216-6d2035bb3fca", "Relationships": [{"Type": "CHILD", "Ids": ["f70d3b6b-ebe0-44a6-8802-c4c59407a310", "f2a02f5d-1385-4855-8ee9-688607c0124a", "b81c0b25-bb6b-40b2-bc83-e19853bd9dc7", "3635b672-0bd1-4673-a91b-0e744af4e8e5"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "Ship To:", "Geometry": {"BoundingBox": {"Width": 0.06450069695711136, "Height": 0.013641909696161747, "Left": 0.08381610363721848, "Top": 0.3144974708557129}, "Polygon": [{"X": 0.08383582532405853, "Y": 0.3144974708557129}, {"X": 0.14831680059432983, "Y": 0.31498220562934875}, {"X": 0.14831507205963135, "Y": 0.32813939452171326}, {"X": 0.08381610363721848, "Y": 0.3276977241039276}]}, "Id": "d3787c41-c2c0-436d-a198-aa0af3acdda0", "Relationships": [{"Type": "CHILD", "Ids": ["3f07f43c-978e-40ae-8eb0-f988605c3991", "b1013d6a-0ce2-430a-95e5-bf78ae37ba82"]}]}, {"BlockType": "LINE", "Confidence": 97.52885437011719, "Text": "Mill Order # 303735", "Geometry": {"BoundingBox": {"Width": 0.1099468320608139, "Height": 0.011578820645809174, "Left": 0.22148990631103516, "Top": 0.32025083899497986}, "Polygon": [{"X": 0.22148990631103516, "Y": 0.32025083899497986}, {"X": 0.3313964903354645, "Y": 0.3210505545139313}, {"X": 0.33143675327301025, "Y": 0.33182963728904724}, {"X": 0.22150523960590363, "Y": 0.33109068870544434}]}, "Id": "2f83e3df-fa66-4cf9-b9e2-7e76c8eb80e1", "Relationships": [{"Type": "CHILD", "Ids": ["ccb922af-d3e2-4117-9dbb-169c443e2c56", "c9eaa6e9-357c-4c03-afcc-bdf550b6f5c7", "36016d09-910c-4e77-baff-37e7538cf4f9", "ad7c12af-a47c-4933-a499-b629a537d162"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Sold To", "Geometry": {"BoundingBox": {"Width": 0.053381841629743576, "Height": 0.010919008404016495, "Left": 0.47360777854919434, "Top": 0.3271019160747528}, "Polygon": [{"X": 0.47360777854919434, "Y": 0.3271019160747528}, {"X": 0.5269067287445068, "Y": 0.3274758756160736}, {"X": 0.5269896388053894, "Y": 0.338020920753479}, {"X": 0.4736790060997009, "Y": 0.3376760482788086}]}, "Id": "deb8b369-8d3e-41ae-a8c4-cc9571185aec", "Relationships": [{"Type": "CHILD", "Ids": ["27bfab32-6974-470e-97e1-c70af1a22241", "cef5aeab-e5ca-4592-8a17-bd306dd6614f"]}]}, {"BlockType": "LINE", "Confidence": 99.85677337646484, "Text": "Purchase Order #303735", "Geometry": {"BoundingBox": {"Width": 0.13129426538944244, "Height": 0.009637069888412952, "Left": 0.56935054063797, "Top": 0.3303796350955963}, "Polygon": [{"X": 0.56935054063797, "Y": 0.3303796350955963}, {"X": 0.7005441188812256, "Y": 0.331282377243042}, {"X": 0.7006447911262512, "Y": 0.3400167226791382}, {"X": 0.5694275498390198, "Y": 0.33917391300201416}]}, "Id": "e0dca055-7d6d-4c2a-a148-65dc6117a1d1", "Relationships": [{"Type": "CHILD", "Ids": ["f2e1475b-5d85-4978-9f59-0a8e0ef14eb0", "fcda0089-6e7e-46d8-896e-dca045501400", "f0231582-a254-45f4-a5a0-b5d228947e35"]}]}, {"BlockType": "LINE", "Confidence": 90.95832824707031, "Text": "Luvata - Appleton", "Geometry": {"BoundingBox": {"Width": 0.12604616582393646, "Height": 0.015292556956410408, "Left": 0.0832778662443161, "Top": 0.3432195484638214}, "Polygon": [{"X": 0.08329986780881882, "Y": 0.3432195484638214}, {"X": 0.2093072384595871, "Y": 0.3439830541610718}, {"X": 0.20932403206825256, "Y": 0.35851210355758667}, {"X": 0.0832778662443161, "Y": 0.35784196853637695}]}, "Id": "2dd48268-5de1-4610-9143-f4cab1a6f376", "Relationships": [{"Type": "CHILD", "Ids": ["9bce8b13-497d-4514-a2f7-5e7388222309", "b3d35737-88ee-4f16-a718-c6831807de9a"]}]}, {"BlockType": "LINE", "Confidence": 86.85009002685547, "Text": "553 <PERSON>.", "Geometry": {"BoundingBox": {"Width": 0.10057447105646133, "Height": 0.0126572847366333, "Left": 0.08306264877319336, "Top": 0.35895800590515137}, "Polygon": [{"X": 0.08308103680610657, "Y": 0.35895800590515137}, {"X": 0.18362967669963837, "Y": 0.3594869077205658}, {"X": 0.1836371123790741, "Y": 0.37161529064178467}, {"X": 0.08306264877319336, "Y": 0.3711484968662262}]}, "Id": "5a41e042-48da-4b67-b7bb-7830118e99f1", "Relationships": [{"Type": "CHILD", "Ids": ["54a2770c-1dea-485e-9761-4714fe0ed8ed", "20c804ad-a134-44d8-a728-3460b2c13658", "62f2bc6b-3775-4088-a104-3568782ca7e5"]}]}, {"BlockType": "LINE", "Confidence": 89.00032043457031, "Text": "Luvata - Appleton", "Geometry": {"BoundingBox": {"Width": 0.11305529624223709, "Height": 0.013472761027514935, "Left": 0.47346463799476624, "Top": 0.35556331276893616}, "Polygon": [{"X": 0.47346463799476624, "Y": 0.35556331276893616}, {"X": 0.5864028334617615, "Y": 0.3561892509460449}, {"X": 0.5865199565887451, "Y": 0.36903607845306396}, {"X": 0.47355160117149353, "Y": 0.36848556995391846}]}, "Id": "51a65a07-662e-4fbe-8f8c-0d17ca333ce8", "Relationships": [{"Type": "CHILD", "Ids": ["2c435364-2172-483f-be2c-d6f571b653fc", "cb345939-6050-435a-b9cb-3ec00d2038a8"]}]}, {"BlockType": "LINE", "Confidence": 97.83960723876953, "Text": "Kimberly, WI US 54136", "Geometry": {"BoundingBox": {"Width": 0.16631297767162323, "Height": 0.01422072108834982, "Left": 0.08295272290706635, "Top": 0.37414655089378357}, "Polygon": [{"X": 0.08297324180603027, "Y": 0.37414655089378357}, {"X": 0.2492387890815735, "Y": 0.374892920255661}, {"X": 0.24926570057868958, "Y": 0.38836729526519775}, {"X": 0.08295272290706635, "Y": 0.3877354562282562}]}, "Id": "10e32ef9-0a5d-4510-93fe-e832aca8c0ad", "Relationships": [{"Type": "CHILD", "Ids": ["95502356-df3d-47c4-bfff-3f33c0533461", "2cbef5a6-b80d-48fd-8893-3990ecfb3f6c", "d3a8ac73-b438-4952-8da2-d10715c34633", "7b6ee256-1d45-41a7-a82a-ab5766ba774d"]}]}, {"BlockType": "LINE", "Confidence": 95.99801635742188, "Text": "553 <PERSON>.", "Geometry": {"BoundingBox": {"Width": 0.08930440247058868, "Height": 0.011336388066411018, "Left": 0.47348669171333313, "Top": 0.3701978921890259}, "Polygon": [{"X": 0.47348669171333313, "Y": 0.3701978921890259}, {"X": 0.562697172164917, "Y": 0.37062472105026245}, {"X": 0.562791109085083, "Y": 0.38153427839279175}, {"X": 0.4735604524612427, "Y": 0.3811580240726471}]}, "Id": "423210d4-fa66-4ca2-9e7d-2ab34877c26c", "Relationships": [{"Type": "CHILD", "Ids": ["19f7fdf1-34c8-4f1b-ab08-4883f8a7ade3", "8d3b4717-b40b-4afd-87a4-f162cd225060", "4901e877-3705-49ba-bca4-3177211577a4"]}]}, {"BlockType": "LINE", "Confidence": 95.91229248046875, "Text": "Kimberly, WI US 54136", "Geometry": {"BoundingBox": {"Width": 0.14952237904071808, "Height": 0.013518000021576881, "Left": 0.47303304076194763, "Top": 0.38441258668899536}, "Polygon": [{"X": 0.47303304076194763, "Y": 0.38441258668899536}, {"X": 0.6224279999732971, "Y": 0.3850173354148865}, {"X": 0.6225554347038269, "Y": 0.3979305922985077}, {"X": 0.47312045097351074, "Y": 0.39742639660835266}]}, "Id": "58eb2d5a-5ba0-4c14-82e5-f85d21c23e69", "Relationships": [{"Type": "CHILD", "Ids": ["f39381f5-03c8-4a56-bfa7-f9c790c7303a", "209b1606-5211-4ecd-be93-dceab8dabed7", "22cf92b8-142a-4036-a0f9-451be12c98b5", "9cf8722e-c1ea-4704-af98-f6bd349e0dc1"]}]}, {"BlockType": "LINE", "Confidence": 90.72325134277344, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.03737465664744377, "Height": 0.00836128182709217, "Left": 0.1317957639694214, "Top": 0.4220038652420044}, "Polygon": [{"X": 0.13179974257946014, "Y": 0.4220038652420044}, {"X": 0.16916786134243011, "Y": 0.42208099365234375}, {"X": 0.16917040944099426, "Y": 0.4303651452064514}, {"X": 0.1317957639694214, "Y": 0.43030375242233276}]}, "Id": "6158eade-6b10-4bd0-a54c-a65d3c4982a5", "Relationships": [{"Type": "CHILD", "Ids": ["cbb4e2c8-6191-4886-97c3-6e1846781be9"]}]}, {"BlockType": "LINE", "Confidence": 96.91197204589844, "Text": "Date in:", "Geometry": {"BoundingBox": {"Width": 0.04174409434199333, "Height": 0.008546258322894573, "Left": 0.47013694047927856, "Top": 0.42877092957496643}, "Polygon": [{"X": 0.47013694047927856, "Y": 0.42877092957496643}, {"X": 0.511817216873169, "Y": 0.4288438856601715}, {"X": 0.5118809938430786, "Y": 0.4373171925544739}, {"X": 0.47019338607788086, "Y": 0.43726256489753723}]}, "Id": "bbe69fab-d11c-4871-9b5f-2c3c39d68ccd", "Relationships": [{"Type": "CHILD", "Ids": ["0a686a62-1a35-4f4c-bb3b-a4940ccb4575", "fb07909a-bcf1-42c4-b07f-d72136d8756b"]}]}, {"BlockType": "LINE", "Confidence": 60.7292366027832, "Text": "6/15/2025", "Geometry": {"BoundingBox": {"Width": 0.052595291286706924, "Height": 0.008023938164114952, "Left": 0.5443468689918518, "Top": 0.4299773573875427}, "Polygon": [{"X": 0.5443468689918518, "Y": 0.4299773573875427}, {"X": 0.5968682169914246, "Y": 0.4300663471221924}, {"X": 0.596942126750946, "Y": 0.43800127506256104}, {"X": 0.5444121956825256, "Y": 0.437934011220932}]}, "Id": "02ecfcb5-6377-4774-81d5-684471d55408", "Relationships": [{"Type": "CHILD", "Ids": ["aa74f1f9-4404-44c8-99cc-baea94dbc0a1"]}]}, {"BlockType": "LINE", "Confidence": 71.14984893798828, "Text": "Time In", "Geometry": {"BoundingBox": {"Width": 0.041742824018001556, "Height": 0.008042280562222004, "Left": 0.666053056716919, "Top": 0.4308325946331024}, "Polygon": [{"X": 0.666053056716919, "Y": 0.4308325946331024}, {"X": 0.7077029347419739, "Y": 0.43090176582336426}, {"X": 0.7077958583831787, "Y": 0.43887487053871155}, {"X": 0.6661391854286194, "Y": 0.4388231337070465}]}, "Id": "11402d37-b758-4304-a941-150d10a15fe6", "Relationships": [{"Type": "CHILD", "Ids": ["a361eb0e-29ae-4feb-9828-19f8201f76fb", "5cc6ae9c-34f6-44ed-bde8-3d720d8423f1"]}]}, {"BlockType": "LINE", "Confidence": 94.1405258178711, "Text": "9:49 AM", "Geometry": {"BoundingBox": {"Width": 0.04257948324084282, "Height": 0.008018949069082737, "Left": 0.7389572858810425, "Top": 0.4316715896129608}, "Polygon": [{"X": 0.7389572858810425, "Y": 0.4316715896129608}, {"X": 0.7814317345619202, "Y": 0.43174052238464355}, {"X": 0.7815367579460144, "Y": 0.4396905303001404}, {"X": 0.7390553951263428, "Y": 0.4396393895149231}]}, "Id": "c0cafd10-c52f-42a9-b1ec-2c34e88fa579", "Relationships": [{"Type": "CHILD", "Ids": ["e9e8e507-dde9-4f9b-b15f-c756ae5ceec4", "227e8949-88c0-4b20-b219-048c024008d5"]}]}, {"BlockType": "LINE", "Confidence": 90.89359283447266, "Text": "Truck #: 1055", "Geometry": {"BoundingBox": {"Width": 0.08229950815439224, "Height": 0.009389580227434635, "Left": 0.12176165729761124, "Top": 0.4465324282646179}, "Polygon": [{"X": 0.12176812440156937, "Y": 0.4465324282646179}, {"X": 0.2040514498949051, "Y": 0.446599543094635}, {"X": 0.20406116545200348, "Y": 0.45592200756073}, {"X": 0.12176165729761124, "Y": 0.4558940529823303}]}, "Id": "4b013f0a-40cc-41bf-9c1c-99fe727ef3f2", "Relationships": [{"Type": "CHILD", "Ids": ["9db87ed7-a252-4d3b-8857-171114d61b09", "ac82358d-3db3-4c56-ad37-e9cfc1270d43", "c359c39b-e5ce-4b28-86a0-87afa4316d03"]}]}, {"BlockType": "LINE", "Confidence": 95.85539245605469, "Text": "Date Out 6/16/2025", "Geometry": {"BoundingBox": {"Width": 0.12591947615146637, "Height": 0.008960535749793053, "Left": 0.4703095555305481, "Top": 0.45249709486961365}, "Polygon": [{"X": 0.4703095555305481, "Y": 0.45249709486961365}, {"X": 0.596146285533905, "Y": 0.45256271958351135}, {"X": 0.5962290167808533, "Y": 0.46145761013031006}, {"X": 0.4703690707683563, "Y": 0.46145033836364746}]}, "Id": "f7b6c283-5be4-4845-9895-9c274f43902c", "Relationships": [{"Type": "CHILD", "Ids": ["74063747-efa0-45b4-8df4-a729216dff77", "621941b1-4683-49e3-9efe-04452a2de221", "644e91f5-834d-4627-b6d1-c2dd5244a88d"]}]}, {"BlockType": "LINE", "Confidence": 93.64397430419922, "Text": "Time Out:", "Geometry": {"BoundingBox": {"Width": 0.05168360099196434, "Height": 0.007982082664966583, "Left": 0.6647686958312988, "Top": 0.454377681016922}, "Polygon": [{"X": 0.6647686958312988, "Y": 0.454377681016922}, {"X": 0.7163581252098083, "Y": 0.45439979434013367}, {"X": 0.7164523005485535, "Y": 0.462359756231308}, {"X": 0.6648544669151306, "Y": 0.4623591899871826}]}, "Id": "944ee5f4-428d-46d2-815b-9f311e15ede3", "Relationships": [{"Type": "CHILD", "Ids": ["8fa1e462-f8ea-4321-b00a-de4a36eb8643", "7f8fd0a5-6369-4572-8f9c-eb2404d1175f"]}]}, {"BlockType": "LINE", "Confidence": 98.72647857666016, "Text": "12:35 PM", "Geometry": {"BoundingBox": {"Width": 0.04798574000597, "Height": 0.007926561869680882, "Left": 0.7389745116233826, "Top": 0.4550369381904602}, "Polygon": [{"X": 0.7389745116233826, "Y": 0.4550369381904602}, {"X": 0.7868549227714539, "Y": 0.4550558626651764}, {"X": 0.786960244178772, "Y": 0.4629625082015991}, {"X": 0.7390720248222351, "Y": 0.4629634916782379}]}, "Id": "350ffcbb-36d5-4535-bc7d-62cac4cd2549", "Relationships": [{"Type": "CHILD", "Ids": ["dc835140-6145-4099-9c4e-308596fa69c2", "491a4618-651e-401e-8c25-a30d6d6e2934"]}]}, {"BlockType": "LINE", "Confidence": 93.90943908691406, "Text": "Order #: 303735", "Geometry": {"BoundingBox": {"Width": 0.09685397148132324, "Height": 0.009365187026560307, "Left": 0.12215427309274673, "Top": 0.47153884172439575}, "Polygon": [{"X": 0.12216063588857651, "Y": 0.4715832471847534}, {"X": 0.21899566054344177, "Y": 0.47153884172439575}, {"X": 0.21900825202465057, "Y": 0.4808136820793152}, {"X": 0.12215427309274673, "Y": 0.4809040427207947}]}, "Id": "7386a598-be6c-4145-aa24-46ce5941f0cb", "Relationships": [{"Type": "CHILD", "Ids": ["a5adb8c3-a415-42ab-9d7a-901b003f6766", "a2d092a2-f463-441c-bb43-d5046c5096d2", "e0a78f57-055f-4adb-ba5c-9e9cf3c982fb"]}]}, {"BlockType": "LINE", "Confidence": 94.70055389404297, "Text": "Booking #:", "Geometry": {"BoundingBox": {"Width": 0.05497482046484947, "Height": 0.009787413291633129, "Left": 0.48506882786750793, "Top": 0.47576767206192017}, "Polygon": [{"X": 0.48506882786750793, "Y": 0.4758053123950958}, {"X": 0.5399647951126099, "Y": 0.47576767206192017}, {"X": 0.5400436520576477, "Y": 0.4854896664619446}, {"X": 0.48513665795326233, "Y": 0.48555508255958557}]}, "Id": "b2091935-84e0-43a2-907a-b18df308eb12", "Relationships": [{"Type": "CHILD", "Ids": ["b988f7ed-31c1-41ac-a4d8-0cf631f64d1a", "8ceae15e-1eee-4e86-af57-43b93cd8d2b1"]}]}, {"BlockType": "LINE", "Confidence": 85.74059295654297, "Text": "Container #: 225340", "Geometry": {"BoundingBox": {"Width": 0.12025206536054611, "Height": 0.009388026781380177, "Left": 0.09859022498130798, "Top": 0.49648550152778625}, "Polygon": [{"X": 0.09860102832317352, "Y": 0.4966939687728882}, {"X": 0.218829944729805, "Y": 0.49648550152778625}, {"X": 0.2188422828912735, "Y": 0.5056089758872986}, {"X": 0.09859022498130798, "Y": 0.5058735013008118}]}, "Id": "4f9b3b4c-7442-4040-95ca-aafab0cbb0d7", "Relationships": [{"Type": "CHILD", "Ids": ["1302afcc-f46e-443a-a550-23cbc4975da9", "dda1ae30-626c-4d3a-9ee0-d1779e7c19f0", "463530f6-a33f-48ef-a01d-d2fc9bb338f5"]}]}, {"BlockType": "LINE", "Confidence": 99.7312240600586, "Text": "See Packing List for Details", "Geometry": {"BoundingBox": {"Width": 0.15816432237625122, "Height": 0.010842375457286835, "Left": 0.08023959398269653, "Top": 0.521098256111145}, "Polygon": [{"X": 0.08025579899549484, "Y": 0.5215718746185303}, {"X": 0.23838578164577484, "Y": 0.521098256111145}, {"X": 0.23840391635894775, "Y": 0.5313836932182312}, {"X": 0.08023959398269653, "Y": 0.5319406390190125}]}, "Id": "8219b3f1-b17b-493e-a3d4-6bcc3cca8796", "Relationships": [{"Type": "CHILD", "Ids": ["78232bda-1481-436d-bf1f-2bade60acad0", "9677b24b-cd83-465a-bf1c-b5b2d72ff6d9", "aed73147-63f8-4382-adb8-f1b151023c59", "f17edd18-bdff-4eb5-95db-688bf696e04e", "0ad7df90-9d57-41ed-908c-09198eca5a73"]}]}, {"BlockType": "LINE", "Confidence": 99.84471130371094, "Text": "ALL WEIGHTS ARE REPORTED IN LB", "Geometry": {"BoundingBox": {"Width": 0.20252516865730286, "Height": 0.010154313407838345, "Left": 0.4711689054965973, "Top": 0.5218394994735718}, "Polygon": [{"X": 0.4711689054965973, "Y": 0.5224674344062805}, {"X": 0.6735911965370178, "Y": 0.5218394994735718}, {"X": 0.6736940741539001, "Y": 0.5312658548355103}, {"X": 0.4712323248386383, "Y": 0.5319938063621521}]}, "Id": "91cf4bf6-8030-4dad-bb6f-97685b798ff4", "Relationships": [{"Type": "CHILD", "Ids": ["0d3cf511-24d6-44ff-ad13-39c69a2664a8", "db03c0a4-34e9-4350-addc-d7ee00178792", "88f7ac91-4eb1-4d02-9b5a-2b0c4337c9ce", "e7960890-5e06-4780-976d-4942632b6730", "b5c23792-0926-457c-85cd-18981887663f", "4322564b-ce98-4302-949b-3ca6ec1a3f93"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Material", "Geometry": {"BoundingBox": {"Width": 0.0613228864967823, "Height": 0.010366171598434448, "Left": 0.12814313173294067, "Top": 0.5456355810165405}, "Polygon": [{"X": 0.12814873456954956, "Y": 0.545895516872406}, {"X": 0.18945862352848053, "Y": 0.5456355810165405}, {"X": 0.18946601450443268, "Y": 0.5557101964950562}, {"X": 0.12814313173294067, "Y": 0.5560017228126526}]}, "Id": "9e8abdf8-23ed-43f5-ac03-c4e3fe4c07a8", "Relationships": [{"Type": "CHILD", "Ids": ["973f24c4-52eb-4470-9f40-c8a157f36ddb"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Gross Weight", "Geometry": {"BoundingBox": {"Width": 0.09370774775743484, "Height": 0.012115252204239368, "Left": 0.49190405011177063, "Top": 0.5452661514282227}, "Polygon": [{"X": 0.49190405011177063, "Y": 0.545669436454773}, {"X": 0.5855062007904053, "Y": 0.5452661514282227}, {"X": 0.5856118202209473, "Y": 0.5569211840629578}, {"X": 0.49198710918426514, "Y": 0.5573813915252686}]}, "Id": "cf1519ff-9dd2-44f6-a4ac-f4f948909900", "Relationships": [{"Type": "CHILD", "Ids": ["77830e95-53cf-480a-83fa-79ba40b7e67f", "b1234fbb-3824-46e1-ab55-963e87fed3f2"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.08270184695720673, "Height": 0.012172567658126354, "Left": 0.6162768602371216, "Top": 0.5457671284675598}, "Polygon": [{"X": 0.6162768602371216, "Y": 0.5461271405220032}, {"X": 0.6988441348075867, "Y": 0.5457671284675598}, {"X": 0.6989786624908447, "Y": 0.5575287342071533}, {"X": 0.6163914799690247, "Y": 0.5579397082328796}]}, "Id": "24481b4e-5b54-4983-b61a-7c3124ed23e4", "Relationships": [{"Type": "CHILD", "Ids": ["f61df6b6-4ba6-4511-950f-667be3664e7a", "5e9d44ab-3356-4201-b055-ea0e554fa01c"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Net Weight", "Geometry": {"BoundingBox": {"Width": 0.07501520216464996, "Height": 0.012025277130305767, "Left": 0.7363812327384949, "Top": 0.5459045767784119}, "Polygon": [{"X": 0.7363812327384949, "Y": 0.5462334156036377}, {"X": 0.8112356066703796, "Y": 0.5459045767784119}, {"X": 0.8113964200019836, "Y": 0.5575549602508545}, {"X": 0.7365242838859558, "Y": 0.5579298734664917}]}, "Id": "17fac2a9-7b6c-4f6e-a90c-c05f86a1a8a1", "Relationships": [{"Type": "CHILD", "Ids": ["7f05253c-8c01-42c6-a5a2-f9b0d8a0885b", "a8aef333-06d2-474c-bd62-b59beb4ebb0a"]}]}, {"BlockType": "LINE", "Confidence": 99.92536163330078, "Text": "Copper Cathode Medium Chops", "Geometry": {"BoundingBox": {"Width": 0.22553785145282745, "Height": 0.013337078504264355, "Left": 0.1280786544084549, "Top": 0.5612228512763977}, "Polygon": [{"X": 0.1280854344367981, "Y": 0.5623679161071777}, {"X": 0.35356611013412476, "Y": 0.5612228512763977}, {"X": 0.35361650586128235, "Y": 0.5732746720314026}, {"X": 0.1280786544084549, "Y": 0.5745599269866943}]}, "Id": "356d1699-8e10-417f-9433-d337fdf638e6", "Relationships": [{"Type": "CHILD", "Ids": ["2ef89ab2-64b4-4f55-8195-0ee73b379f2a", "1db1bc4b-c6b2-4935-8502-e05130b38e8d", "e00ba043-6f23-4a7a-805c-99e8800d6bd3", "233af2fb-cb1a-4cd6-a6ee-a3dc1d31b36d"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "44,835.00", "Geometry": {"BoundingBox": {"Width": 0.06407298892736435, "Height": 0.010785887017846107, "Left": 0.5212148427963257, "Top": 0.5614290833473206}, "Polygon": [{"X": 0.5212148427963257, "Y": 0.5617585778236389}, {"X": 0.5851935148239136, "Y": 0.5614290833473206}, {"X": 0.5852878093719482, "Y": 0.5718506574630737}, {"X": 0.521295428276062, "Y": 0.5722149610519409}]}, "Id": "d9a58374-7f7c-4900-ad54-f0c6b6b95a05", "Relationships": [{"Type": "CHILD", "Ids": ["c971cf9b-c2d6-4334-8730-6bda9300dbe9"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "835.00", "Geometry": {"BoundingBox": {"Width": 0.04462321475148201, "Height": 0.009870517067611217, "Left": 0.6540040373802185, "Top": 0.5614732503890991}, "Polygon": [{"X": 0.6540040373802185, "Y": 0.5617039799690247}, {"X": 0.6985173225402832, "Y": 0.5614732503890991}, {"X": 0.6986272931098938, "Y": 0.571090579032898}, {"X": 0.6541052460670471, "Y": 0.5713437795639038}]}, "Id": "6e1b84c2-dcf9-4a42-8bd6-98ae03d1d92a", "Relationships": [{"Type": "CHILD", "Ids": ["3b13f79e-4008-4928-819f-6068e39b1784"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "44,000.00", "Geometry": {"BoundingBox": {"Width": 0.06305070966482162, "Height": 0.01071843784302473, "Left": 0.7476527094841003, "Top": 0.5617641806602478}, "Polygon": [{"X": 0.7476527094841003, "Y": 0.5620931386947632}, {"X": 0.8105607032775879, "Y": 0.5617641806602478}, {"X": 0.810703456401825, "Y": 0.5721192955970764}, {"X": 0.7477821707725525, "Y": 0.5724826455116272}]}, "Id": "45b32784-fe66-4cbd-ac24-fe7c7e367d35", "Relationships": [{"Type": "CHILD", "Ids": ["e43cf33f-04bd-4d7d-b2cc-c8d561e47e50"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Shipment Weights:", "Geometry": {"BoundingBox": {"Width": 0.13491535186767578, "Height": 0.013250143267214298, "Left": 0.3248084485530853, "Top": 0.5930936932563782}, "Polygon": [{"X": 0.3248084485530853, "Y": 0.5940045714378357}, {"X": 0.4596453011035919, "Y": 0.5930936932563782}, {"X": 0.4597238004207611, "Y": 0.6053471565246582}, {"X": 0.3248525559902191, "Y": 0.6063438057899475}]}, "Id": "abbd2b1f-f28a-414c-9302-a0e35b489f72", "Relationships": [{"Type": "CHILD", "Ids": ["af623a2e-dc95-4745-b51a-4717fa64840c", "fb398f0e-56dd-4ece-8e5a-c2e85ecaf40c"]}]}, {"BlockType": "LINE", "Confidence": 93.27427673339844, "Text": "78,080.00", "Geometry": {"BoundingBox": {"Width": 0.06415561586618423, "Height": 0.011034147813916206, "Left": 0.5212807655334473, "Top": 0.5923334360122681}, "Polygon": [{"X": 0.5212807655334473, "Y": 0.592766523361206}, {"X": 0.5853407979011536, "Y": 0.5923334360122681}, {"X": 0.5854364037513733, "Y": 0.6028991937637329}, {"X": 0.5213624238967896, "Y": 0.6033676266670227}]}, "Id": "88172dbe-44cb-48fd-a4d1-2bd49952abb5", "Relationships": [{"Type": "CHILD", "Ids": ["6e7b1f76-ce57-4d0e-87d2-1f41ef6be848"]}]}, {"BlockType": "LINE", "Confidence": 99.6314926147461, "Text": "33,260.00", "Geometry": {"BoundingBox": {"Width": 0.06453767418861389, "Height": 0.011185291223227978, "Left": 0.6348206996917725, "Top": 0.5925306677818298}, "Polygon": [{"X": 0.6348206996917725, "Y": 0.5929694175720215}, {"X": 0.6992358565330505, "Y": 0.5925306677818298}, {"X": 0.6993584036827087, "Y": 0.603240966796875}, {"X": 0.6349291205406189, "Y": 0.6037159562110901}]}, "Id": "d889eb82-a511-4eb8-bed1-43abf4ae6ddc", "Relationships": [{"Type": "CHILD", "Ids": ["189ee663-2e5e-43d2-ab3d-61f93be47cf3"]}]}, {"BlockType": "LINE", "Confidence": 99.7510757446289, "Text": "44,820.00", "Geometry": {"BoundingBox": {"Width": 0.06311559677124023, "Height": 0.010742926970124245, "Left": 0.7482593655586243, "Top": 0.5928544402122498}, "Polygon": [{"X": 0.7482593655586243, "Y": 0.59328693151474}, {"X": 0.8112332820892334, "Y": 0.5928544402122498}, {"X": 0.8113749623298645, "Y": 0.6031306385993958}, {"X": 0.7483879327774048, "Y": 0.6035973429679871}]}, "Id": "70781b15-bb8f-4ed9-9942-9976f93f8c11", "Relationships": [{"Type": "CHILD", "Ids": ["bc2d2574-ea9b-4871-b97a-59daf26df56d"]}]}, {"BlockType": "LINE", "Confidence": 98.24657440185547, "Text": "Delivered By:", "Geometry": {"BoundingBox": {"Width": 0.0932646244764328, "Height": 0.01425720565021038, "Left": 0.06549563258886337, "Top": 0.7823114395141602}, "Polygon": [{"X": 0.06551935523748398, "Y": 0.7838307619094849}, {"X": 0.15875914692878723, "Y": 0.7823114395141602}, {"X": 0.15876026451587677, "Y": 0.7949886322021484}, {"X": 0.06549563258886337, "Y": 0.7965686321258545}]}, "Id": "8c84ca63-2d4e-4a50-8c3b-a15bd8745f62", "Relationships": [{"Type": "CHILD", "Ids": ["7cc95dab-cc04-4efa-a2a4-636a5e4f17bd", "ba1d193e-3244-4883-84e7-53ebd3778dfc"]}]}, {"BlockType": "LINE", "Confidence": 99.6314926147461, "Text": "Date:", "Geometry": {"BoundingBox": {"Width": 0.033802106976509094, "Height": 0.010510919615626335, "Left": 0.6045927405357361, "Top": 0.7721703052520752}, "Polygon": [{"X": 0.6045927405357361, "Y": 0.7727153897285461}, {"X": 0.6382941603660583, "Y": 0.7721703052520752}, {"X": 0.638394832611084, "Y": 0.782118558883667}, {"X": 0.6046865582466125, "Y": 0.7826812267303467}]}, "Id": "36b4cae8-b526-4853-a51d-c4e537936467", "Relationships": [{"Type": "CHILD", "Ids": ["063c893b-e06c-46c2-b6a7-1d44f1c6eac4"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "Received By:", "Geometry": {"BoundingBox": {"Width": 0.09400860220193863, "Height": 0.01458863914012909, "Left": 0.06354221701622009, "Top": 0.8254024982452393}, "Polygon": [{"X": 0.063566654920578, "Y": 0.8271402716636658}, {"X": 0.15755002200603485, "Y": 0.8254024982452393}, {"X": 0.15755082666873932, "Y": 0.8381916284561157}, {"X": 0.06354221701622009, "Y": 0.8399911522865295}]}, "Id": "537a7719-e2fa-494d-869f-ca65e941a7b5", "Relationships": [{"Type": "CHILD", "Ids": ["be1fd230-13b0-4ffb-9740-5a48e9c20952", "6780f32d-efc6-46e0-9020-10d2712c30cb"]}]}, {"BlockType": "LINE", "Confidence": 98.65533447265625, "Text": "Date:", "Geometry": {"BoundingBox": {"Width": 0.03486527130007744, "Height": 0.011263852939009666, "Left": 0.605013906955719, "Top": 0.8130304217338562}, "Polygon": [{"X": 0.605013906955719, "Y": 0.813666820526123}, {"X": 0.639771580696106, "Y": 0.8130304217338562}, {"X": 0.6398791670799255, "Y": 0.8236384391784668}, {"X": 0.6051139235496521, "Y": 0.8242942690849304}]}, "Id": "bbf9f69f-b768-4f1c-aa1b-78ef2f646017", "Relationships": [{"Type": "CHILD", "Ids": ["39ebd206-be07-4266-b164-1d64873fde4f"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DELIVERY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12105404585599899, "Height": 0.024819811806082726, "Left": 0.3543730080127716, "Top": 0.05660296231508255}, "Polygon": [{"X": 0.3543730080127716, "Y": 0.05660296231508255}, {"X": 0.4752752184867859, "Y": 0.05912955477833748}, {"X": 0.4754270613193512, "Y": 0.08142277598381042}, {"X": 0.3544681966304779, "Y": 0.07903466373682022}]}, "Id": "2049413e-5940-45b7-bd11-740ecf0edf93"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "TICKET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08696549385786057, "Height": 0.019703024998307228, "Left": 0.47953662276268005, "Top": 0.06674817949533463}, "Polygon": [{"X": 0.47953662276268005, "Y": 0.06674817949533463}, {"X": 0.5663453936576843, "Y": 0.0685284212231636}, {"X": 0.5665020942687988, "Y": 0.0864512026309967}, {"X": 0.47966083884239197, "Y": 0.08475127816200256}]}, "Id": "29c214dd-9fe4-4734-ad9f-e7353c0b51e7"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "VENTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10643017292022705, "Height": 0.022257667034864426, "Left": 0.18954220414161682, "Top": 0.11186268925666809}, "Polygon": [{"X": 0.18954220414161682, "Y": 0.11186268925666809}, {"X": 0.29591134190559387, "Y": 0.11376683413982391}, {"X": 0.29597237706184387, "Y": 0.13412036001682281}, {"X": 0.18955738842487335, "Y": 0.13232654333114624}]}, "Id": "573c3ed2-71fd-4d4b-a0c8-61232f8973f2"}, {"BlockType": "WORD", "Confidence": 97.4364242553711, "Text": "METALS+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10320871323347092, "Height": 0.01862342283129692, "Left": 0.19068047404289246, "Top": 0.12992437183856964}, "Polygon": [{"X": 0.19068047404289246, "Y": 0.12992437183856964}, {"X": 0.29383936524391174, "Y": 0.13167604804039001}, {"X": 0.29388919472694397, "Y": 0.14854779839515686}, {"X": 0.1906934678554535, "Y": 0.14688482880592346}]}, "Id": "b0318cc9-2010-42cb-bc17-bf76aeaf8a8d"}, {"BlockType": "WORD", "Confidence": 49.00151443481445, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02803843654692173, "Height": 0.007348909974098206, "Left": 0.22219237685203552, "Top": 0.14659902453422546}, "Polygon": [{"X": 0.25021684169769287, "Y": 0.14705176651477814}, {"X": 0.2502308189868927, "Y": 0.15394793450832367}, {"X": 0.22220227122306824, "Y": 0.15350502729415894}, {"X": 0.22219237685203552, "Y": 0.14659902453422546}]}, "Id": "9455a8f1-73b3-4c6f-afaa-db8b1fdbcd32"}, {"BlockType": "WORD", "Confidence": 57.7110595703125, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015076599083840847, "Height": 0.005203653126955032, "Left": 0.2519756257534027, "Top": 0.14945444464683533}, "Polygon": [{"X": 0.2670403718948364, "Y": 0.1496959924697876}, {"X": 0.26705220341682434, "Y": 0.15465810894966125}, {"X": 0.2519858777523041, "Y": 0.15442036092281342}, {"X": 0.2519756257534027, "Y": 0.14945444464683533}]}, "Id": "43ce2085-01a0-458c-b563-d99c8bf9a0ba"}, {"BlockType": "WORD", "Confidence": 48.974510192871094, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01989445649087429, "Height": 0.0057122958824038506, "Left": 0.2689247131347656, "Top": 0.15016841888427734}, "Polygon": [{"X": 0.2888038158416748, "Y": 0.15048669278621674}, {"X": 0.28881916403770447, "Y": 0.15588070452213287}, {"X": 0.2689377963542938, "Y": 0.15556789934635162}, {"X": 0.2689247131347656, "Y": 0.15016841888427734}]}, "Id": "0f0758fe-29dd-4771-adf7-fcaa87cfbff2"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03533361852169037, "Height": 0.01160009391605854, "Left": 0.071872279047966, "Top": 0.17405062913894653}, "Polygon": [{"X": 0.07189172506332397, "Y": 0.17405062913894653}, {"X": 0.10720589756965637, "Y": 0.1745675951242447}, {"X": 0.10719478130340576, "Y": 0.18565072119235992}, {"X": 0.071872279047966, "Y": 0.185153529047966}]}, "Id": "36310924-0865-4f44-b9ce-0a04678c78b4"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Point", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0373331643640995, "Height": 0.01245970744639635, "Left": 0.11141037195920944, "Top": 0.1755010038614273}, "Polygon": [{"X": 0.11142128705978394, "Y": 0.1755010038614273}, {"X": 0.14874354004859924, "Y": 0.1760457158088684}, {"X": 0.14874206483364105, "Y": 0.18796071410179138}, {"X": 0.11141037195920944, "Y": 0.18743851780891418}]}, "Id": "dbb8681c-eada-47d0-bc1a-40375157a25a"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "503", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027795592322945595, "Height": 0.011066662147641182, "Left": 0.07210412621498108, "Top": 0.18929846584796906}, "Polygon": [{"X": 0.07212280482053757, "Y": 0.18929846584796906}, {"X": 0.09989972412586212, "Y": 0.18968361616134644}, {"X": 0.09988735616207123, "Y": 0.2003651261329651}, {"X": 0.07210412621498108, "Y": 0.1999949812889099}]}, "Id": "9cc68d68-f4b1-4f4b-8c14-0d6bfa965665"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Co", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021312767639756203, "Height": 0.011098223738372326, "Left": 0.10403040796518326, "Top": 0.19075046479701996}, "Polygon": [{"X": 0.10404197871685028, "Y": 0.19075046479701996}, {"X": 0.1253431737422943, "Y": 0.19104473292827606}, {"X": 0.12533648312091827, "Y": 0.20184868574142456}, {"X": 0.10403040796518326, "Y": 0.20156607031822205}]}, "Id": "720e1b2f-771d-4937-b27f-2219ece281d0"}, {"BlockType": "WORD", "Confidence": 99.37101745605469, "Text": "Rd", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02058042213320732, "Height": 0.011273111216723919, "Left": 0.12976829707622528, "Top": 0.19183854758739471}, "Polygon": [{"X": 0.1297740787267685, "Y": 0.19183854758739471}, {"X": 0.1503487229347229, "Y": 0.19212201237678528}, {"X": 0.1503477394580841, "Y": 0.2031116634607315}, {"X": 0.12976829707622528, "Y": 0.202839657664299}]}, "Id": "7b71789f-f51b-408b-96bb-0b90ad3cc65f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "3805", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03593524917960167, "Height": 0.011841126717627048, "Left": 0.15449941158294678, "Top": 0.1934162974357605}, "Polygon": [{"X": 0.15449942648410797, "Y": 0.1934162974357605}, {"X": 0.1904260367155075, "Y": 0.19390898942947388}, {"X": 0.19043466448783875, "Y": 0.20525741577148438}, {"X": 0.15449941158294678, "Y": 0.20478542149066925}]}, "Id": "6395149f-f023-40e3-8861-a6dca56f3900"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06264431774616241, "Height": 0.013139736838638783, "Left": 0.5595621466636658, "Top": 0.19818413257598877}, "Polygon": [{"X": 0.5595621466636658, "Y": 0.19818413257598877}, {"X": 0.62208491563797, "Y": 0.1990441232919693}, {"X": 0.622206449508667, "Y": 0.21132385730743408}, {"X": 0.5596678256988525, "Y": 0.2105037271976471}]}, "Id": "ceb780a1-afb1-44b7-86df-8a7021d15fd2"}, {"BlockType": "WORD", "Confidence": 98.13397216796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008965037763118744, "Height": 0.009528503753244877, "Left": 0.6248186826705933, "Top": 0.20035062730312347}, "Polygon": [{"X": 0.6248186826705933, "Y": 0.20035062730312347}, {"X": 0.6336882710456848, "Y": 0.20047204196453094}, {"X": 0.633783757686615, "Y": 0.20987913012504578}, {"X": 0.6249123811721802, "Y": 0.20976205170154572}]}, "Id": "eae73a2d-3ca2-464b-bca9-4961ecf71dea"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036158494651317596, "Height": 0.012165691703557968, "Left": 0.07176348567008972, "Top": 0.2046458125114441}, "Polygon": [{"X": 0.07178401201963425, "Y": 0.2046458125114441}, {"X": 0.10792197287082672, "Y": 0.20511874556541443}, {"X": 0.10791043192148209, "Y": 0.21681150794029236}, {"X": 0.07176348567008972, "Y": 0.216359943151474}]}, "Id": "99e53e3a-5d15-479d-90bd-5fae292e706b"}, {"BlockType": "WORD", "Confidence": 86.73728942871094, "Text": "Point,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04130168631672859, "Height": 0.01379486545920372, "Left": 0.1118619367480278, "Top": 0.20649226009845734}, "Polygon": [{"X": 0.11187394708395004, "Y": 0.20649226009845734}, {"X": 0.1531636267900467, "Y": 0.20702983438968658}, {"X": 0.15316323935985565, "Y": 0.22028712928295135}, {"X": 0.1118619367480278, "Y": 0.21977731585502625}]}, "Id": "2db84b49-e74d-42c7-87b8-27fa6a800b7a"}, {"BlockType": "WORD", "Confidence": 99.64285278320312, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020181966945528984, "Height": 0.011037099175155163, "Left": 0.15772996842861176, "Top": 0.20877805352210999}, "Polygon": [{"X": 0.15772996842861176, "Y": 0.20877805352210999}, {"X": 0.17790661752223969, "Y": 0.20903900265693665}, {"X": 0.1779119372367859, "Y": 0.21981514990329742}, {"X": 0.15773069858551025, "Y": 0.2195652276277542}]}, "Id": "cda5defb-4871-4751-a0cc-90fdb73cde19"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "USA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033111974596977234, "Height": 0.01177679467946291, "Left": 0.18259714543819427, "Top": 0.2102673500776291}, "Polygon": [{"X": 0.18259714543819427, "Y": 0.2102673500776291}, {"X": 0.21569444239139557, "Y": 0.21069343388080597}, {"X": 0.2157091200351715, "Y": 0.22204415500164032}, {"X": 0.1826038956642151, "Y": 0.22163715958595276}]}, "Id": "03408fe6-3012-4abe-a21d-f0ee315c72a4"}, {"BlockType": "WORD", "Confidence": 98.40800476074219, "Text": "75169", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04469956457614899, "Height": 0.01204736065119505, "Left": 0.21871912479400635, "Top": 0.2121424674987793}, "Polygon": [{"X": 0.21871912479400635, "Y": 0.2121424674987793}, {"X": 0.26339226961135864, "Y": 0.21271435916423798}, {"X": 0.26341870427131653, "Y": 0.22418983280658722}, {"X": 0.2187347561120987, "Y": 0.22364406287670135}]}, "Id": "98658789-9e44-4ec9-b9be-3022a78482c1"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "903-873-3811", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09940139949321747, "Height": 0.014773895032703876, "Left": 0.07251351326704025, "Top": 0.22209669649600983}, "Polygon": [{"X": 0.07253717631101608, "Y": 0.22209669649600983}, {"X": 0.17190992832183838, "Y": 0.2233092188835144}, {"X": 0.17191490530967712, "Y": 0.23687060177326202}, {"X": 0.07251351326704025, "Y": 0.2357264757156372}]}, "Id": "eaceb478-729b-4139-94d1-bccae7b4d4c7"}, {"BlockType": "WORD", "Confidence": 90.61444091796875, "Text": "WIL106604", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12030307203531265, "Height": 0.01689854823052883, "Left": 0.558576226234436, "Top": 0.22992601990699768}, "Polygon": [{"X": 0.558576226234436, "Y": 0.22992601990699768}, {"X": 0.678708016872406, "Y": 0.23137997090816498}, {"X": 0.6788793206214905, "Y": 0.24682457745075226}, {"X": 0.5587090849876404, "Y": 0.24546730518341064}]}, "Id": "01ecb6da-1f27-4d4e-98ee-fc3e02488ac9"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Contract", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06229643151164055, "Height": 0.012180906720459461, "Left": 0.07300908118486404, "Top": 0.27489933371543884}, "Polygon": [{"X": 0.07302911579608917, "Y": 0.27489933371543884}, {"X": 0.1353055089712143, "Y": 0.2754923701286316}, {"X": 0.13530080020427704, "Y": 0.28708022832870483}, {"X": 0.07300908118486404, "Y": 0.2865237891674042}]}, "Id": "6a006e86-5b4a-448c-b287-7f3d20f20810"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04813513904809952, "Height": 0.011186688207089901, "Left": 0.1545042097568512, "Top": 0.2784546911716461}, "Polygon": [{"X": 0.1545042246580124, "Y": 0.2784546911716461}, {"X": 0.20262843370437622, "Y": 0.2789061367511749}, {"X": 0.20263934135437012, "Y": 0.2896413803100586}, {"X": 0.1545042097568512, "Y": 0.28921622037887573}]}, "Id": "f8116a0d-5ce8-450b-b530-d176efa67c69"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06221672520041466, "Height": 0.01321751531213522, "Left": 0.2059992104768753, "Top": 0.2802242040634155}, "Polygon": [{"X": 0.2059992104768753, "Y": 0.2802242040634155}, {"X": 0.2681855857372284, "Y": 0.28080347180366516}, {"X": 0.26821595430374146, "Y": 0.2934417128562927}, {"X": 0.20601299405097961, "Y": 0.29290255904197693}]}, "Id": "5e95e60a-6755-4195-b21a-e6b5cd422de4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "May", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0296764113008976, "Height": 0.012880945578217506, "Left": 0.27244970202445984, "Top": 0.2825174927711487}, "Polygon": [{"X": 0.27244970202445984, "Y": 0.2825174927711487}, {"X": 0.30208680033683777, "Y": 0.28279104828834534}, {"X": 0.30212610960006714, "Y": 0.29539844393730164}, {"X": 0.27248114347457886, "Y": 0.2951440215110779}]}, "Id": "ef4a04cf-3499-4775-990d-76dc081d20f6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03439951315522194, "Height": 0.010438144207000732, "Left": 0.30568039417266846, "Top": 0.28403085470199585}, "Polygon": [{"X": 0.30568039417266846, "Y": 0.28403085470199585}, {"X": 0.3400402367115021, "Y": 0.2843458354473114}, {"X": 0.3400799036026001, "Y": 0.2944689989089966}, {"X": 0.3057127892971039, "Y": 0.2941718101501465}]}, "Id": "c161a74c-6592-4e6d-aac2-24e86ad3b7d6"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022114012390375137, "Height": 0.010221537202596664, "Left": 0.34414687752723694, "Top": 0.2851870357990265}, "Polygon": [{"X": 0.34414687752723694, "Y": 0.2851870357990265}, {"X": 0.3662160634994507, "Y": 0.2853884696960449}, {"X": 0.3662608861923218, "Y": 0.29540857672691345}, {"X": 0.34418708086013794, "Y": 0.2952185273170471}]}, "Id": "dd41b924-7b9b-4405-aedb-3398f90164b2"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "303735", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050756074488162994, "Height": 0.011323150247335434, "Left": 0.37004899978637695, "Top": 0.2862803637981415}, "Polygon": [{"X": 0.37004899978637695, "Y": 0.2862803637981415}, {"X": 0.4207439720630646, "Y": 0.2867407500743866}, {"X": 0.42080506682395935, "Y": 0.2976034879684448}, {"X": 0.37009862065315247, "Y": 0.2971714437007904}]}, "Id": "75f1a607-2340-47da-af29-a036f4e864be"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Contract", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055630024522542953, "Height": 0.010541162453591824, "Left": 0.5582339763641357, "Top": 0.29241207242012024}, "Polygon": [{"X": 0.5582339763641357, "Y": 0.29241207242012024}, {"X": 0.6137664914131165, "Y": 0.2929036319255829}, {"X": 0.613864004611969, "Y": 0.30295321345329285}, {"X": 0.5583199858665466, "Y": 0.3024906814098358}]}, "Id": "10af7493-fe94-48d8-9e1a-a027139a55d0"}, {"BlockType": "WORD", "Confidence": 98.515625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009316734038293362, "Height": 0.009277528151869774, "Left": 0.6162128448486328, "Top": 0.2936059534549713}, "Polygon": [{"X": 0.6162128448486328, "Y": 0.2936059534549713}, {"X": 0.6254380941390991, "Y": 0.29368728399276733}, {"X": 0.6255295872688293, "Y": 0.30288347601890564}, {"X": 0.6163026094436646, "Y": 0.302806556224823}]}, "Id": "45978053-ef5e-48e9-8125-9e3b749f88ac"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "14307", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03983991965651512, "Height": 0.009802082553505898, "Left": 0.6423131823539734, "Top": 0.29386794567108154}, "Polygon": [{"X": 0.6423131823539734, "Y": 0.29386794567108154}, {"X": 0.6820477247238159, "Y": 0.2942182123661041}, {"X": 0.6821531057357788, "Y": 0.3036700487136841}, {"X": 0.6424108147621155, "Y": 0.3033393919467926}]}, "Id": "117cd469-436b-4939-a110-fe683571775d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Pack", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032518330961465836, "Height": 0.010333273559808731, "Left": 0.547493040561676, "Top": 0.307947039604187}, "Polygon": [{"X": 0.547493040561676, "Y": 0.307947039604187}, {"X": 0.5799208283424377, "Y": 0.30820775032043457}, {"X": 0.5800113677978516, "Y": 0.31828030943870544}, {"X": 0.5475767850875854, "Y": 0.31803658604621887}]}, "Id": "f70d3b6b-ebe0-44a6-8802-c4c59407a310"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "List", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023269223049283028, "Height": 0.00998806580901146, "Left": 0.5833339691162109, "Top": 0.30865320563316345}, "Polygon": [{"X": 0.5833339691162109, "Y": 0.30865320563316345}, {"X": 0.6065096259117126, "Y": 0.30883902311325073}, {"X": 0.60660320520401, "Y": 0.3186412751674652}, {"X": 0.5834228992462158, "Y": 0.31846725940704346}]}, "Id": "f2a02f5d-1385-4855-8ee9-688607c0124a"}, {"BlockType": "WORD", "Confidence": 96.70260620117188, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01265718787908554, "Height": 0.009347951970994473, "Left": 0.6085822582244873, "Top": 0.3092683255672455}, "Polygon": [{"X": 0.6085822582244873, "Y": 0.3092683255672455}, {"X": 0.6211482882499695, "Y": 0.3093688189983368}, {"X": 0.621239423751831, "Y": 0.31861627101898193}, {"X": 0.6086710095405579, "Y": 0.3185218572616577}]}, "Id": "b81c0b25-bb6b-40b2-bc83-e19853bd9dc7"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "22234", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04145541414618492, "Height": 0.009717509150505066, "Left": 0.6410706043243408, "Top": 0.30960479378700256}, "Polygon": [{"X": 0.6410706043243408, "Y": 0.30960479378700256}, {"X": 0.6824213266372681, "Y": 0.3099352717399597}, {"X": 0.6825259923934937, "Y": 0.3193223178386688}, {"X": 0.6411672830581665, "Y": 0.3190121054649353}]}, "Id": "3635b672-0bd1-4673-a91b-0e744af4e8e5"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03544464707374573, "Height": 0.013442897237837315, "Left": 0.08381610363721848, "Top": 0.3144974708557129}, "Polygon": [{"X": 0.08383582532405853, "Y": 0.3144974708557129}, {"X": 0.1192607507109642, "Y": 0.31476378440856934}, {"X": 0.11925093084573746, "Y": 0.3279403746128082}, {"X": 0.08381610363721848, "Y": 0.3276977241039276}]}, "Id": "3f07f43c-978e-40ae-8eb0-f988605c3991"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025304757058620453, "Height": 0.010647419840097427, "Left": 0.12301183491945267, "Top": 0.3164021968841553}, "Polygon": [{"X": 0.12301880866289139, "Y": 0.3164021968841553}, {"X": 0.14831659197807312, "Y": 0.3165903091430664}, {"X": 0.1483152061700821, "Y": 0.3270496129989624}, {"X": 0.12301183491945267, "Y": 0.3268749713897705}]}, "Id": "b1013d6a-0ce2-430a-95e5-bf78ae37ba82"}, {"BlockType": "WORD", "Confidence": 98.7960433959961, "Text": "Mill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019435137510299683, "Height": 0.008791860193014145, "Left": 0.22148990631103516, "Top": 0.32025083899497986}, "Polygon": [{"X": 0.22148990631103516, "Y": 0.32025083899497986}, {"X": 0.24090927839279175, "Y": 0.3203921318054199}, {"X": 0.24092504382133484, "Y": 0.3290427029132843}, {"X": 0.2215021550655365, "Y": 0.3289099633693695}]}, "Id": "ccb922af-d3e2-4117-9dbb-169c443e2c56"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03291814774274826, "Height": 0.009386369958519936, "Left": 0.24372772872447968, "Top": 0.32095274329185486}, "Polygon": [{"X": 0.24372772872447968, "Y": 0.32095274329185486}, {"X": 0.27662229537963867, "Y": 0.3211911618709564}, {"X": 0.27664586901664734, "Y": 0.33033910393714905}, {"X": 0.24374498426914215, "Y": 0.33011606335639954}]}, "Id": "c9eaa6e9-357c-4c03-afcc-bdf550b6f5c7"}, {"BlockType": "WORD", "Confidence": 92.52610778808594, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008143196813762188, "Height": 0.0074262190610170364, "Left": 0.27855807542800903, "Top": 0.3225177228450775}, "Polygon": [{"X": 0.27855807542800903, "Y": 0.3225177228450775}, {"X": 0.2866807281970978, "Y": 0.32257604598999023}, {"X": 0.2867012619972229, "Y": 0.3299439251422882}, {"X": 0.27857738733291626, "Y": 0.3298886716365814}]}, "Id": "36016d09-910c-4e77-baff-37e7538cf4f9"}, {"BlockType": "WORD", "Confidence": 99.02922058105469, "Text": "303735", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04310966283082962, "Height": 0.009421991184353828, "Left": 0.28832706809043884, "Top": 0.32240766286849976}, "Polygon": [{"X": 0.28832706809043884, "Y": 0.32240766286849976}, {"X": 0.3314027190208435, "Y": 0.3227173984050751}, {"X": 0.33143675327301025, "Y": 0.33182963728904724}, {"X": 0.2883528769016266, "Y": 0.331540048122406}]}, "Id": "ad7c12af-a47c-4933-a499-b629a537d162"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Sold", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03167306259274483, "Height": 0.01039942167699337, "Left": 0.47360777854919434, "Top": 0.3271019160747528}, "Polygon": [{"X": 0.47360777854919434, "Y": 0.3271019160747528}, {"X": 0.5052054524421692, "Y": 0.3273236155509949}, {"X": 0.5052808523178101, "Y": 0.33750131726264954}, {"X": 0.4736764430999756, "Y": 0.3372962772846222}]}, "Id": "27bfab32-6974-470e-97e1-c70af1a22241"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018351713195443153, "Height": 0.009797192178666592, "Left": 0.5086379051208496, "Top": 0.32822373509407043}, "Polygon": [{"X": 0.5086379051208496, "Y": 0.32822373509407043}, {"X": 0.526913583278656, "Y": 0.32835111021995544}, {"X": 0.5269896388053894, "Y": 0.338020920753479}, {"X": 0.5087102651596069, "Y": 0.33790266513824463}]}, "Id": "cef5aeab-e5ca-4592-8a17-bd306dd6614f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04940490052103996, "Height": 0.008916242979466915, "Left": 0.56935054063797, "Top": 0.3303796350955963}, "Polygon": [{"X": 0.56935054063797, "Y": 0.3303796350955963}, {"X": 0.6186713576316833, "Y": 0.3307190239429474}, {"X": 0.6187554001808167, "Y": 0.339295893907547}, {"X": 0.5694258213043213, "Y": 0.33897852897644043}]}, "Id": "f2e1475b-5d85-4978-9f59-0a8e0ef14eb0"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031187433749437332, "Height": 0.008490277454257011, "Left": 0.6213704943656921, "Top": 0.33107826113700867}, "Polygon": [{"X": 0.6213704943656921, "Y": 0.33107826113700867}, {"X": 0.6524709463119507, "Y": 0.33129170536994934}, {"X": 0.652557909488678, "Y": 0.33956852555274963}, {"X": 0.6214522123336792, "Y": 0.3393685221672058}]}, "Id": "fcda0089-6e7e-46d8-896e-dca045501400"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "#303735", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046275533735752106, "Height": 0.008504712954163551, "Left": 0.6543692350387573, "Top": 0.3315120041370392}, "Polygon": [{"X": 0.6543692350387573, "Y": 0.3315120041370392}, {"X": 0.700550377368927, "Y": 0.33182844519615173}, {"X": 0.7006447911262512, "Y": 0.3400167226791382}, {"X": 0.6544559001922607, "Y": 0.3397200405597687}]}, "Id": "f0231582-a254-45f4-a5a0-b5d228947e35"}, {"BlockType": "WORD", "Confidence": 82.83183288574219, "Text": "Luvata -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05945662781596184, "Height": 0.012154749594628811, "Left": 0.08328206837177277, "Top": 0.3432195484638214}, "Polygon": [{"X": 0.08329986780881882, "Y": 0.3432195484638214}, {"X": 0.1427386999130249, "Y": 0.3435797095298767}, {"X": 0.14273574948310852, "Y": 0.3553743064403534}, {"X": 0.08328206837177277, "Y": 0.3550497889518738}]}, "Id": "9bce8b13-497d-4514-a2f7-5e7388222309"}, {"BlockType": "WORD", "Confidence": 99.08482360839844, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06313036382198334, "Height": 0.012614418752491474, "Left": 0.14619366824626923, "Top": 0.34589770436286926}, "Polygon": [{"X": 0.14619582891464233, "Y": 0.34589770436286926}, {"X": 0.2093098908662796, "Y": 0.346272736787796}, {"X": 0.20932403206825256, "Y": 0.35851210355758667}, {"X": 0.14619366824626923, "Y": 0.35817646980285645}]}, "Id": "b3d35737-88ee-4f16-a718-c6831807de9a"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "553", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02747008018195629, "Height": 0.010304783470928669, "Left": 0.0830656886100769, "Top": 0.35895800590515137}, "Polygon": [{"X": 0.08308103680610657, "Y": 0.35895800590515137}, {"X": 0.11053577065467834, "Y": 0.35910242795944214}, {"X": 0.11052633076906204, "Y": 0.36926278471946716}, {"X": 0.0830656886100769, "Y": 0.36913254857063293}]}, "Id": "54a2770c-1dea-485e-9761-4714fe0ed8ed"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04581073671579361, "Height": 0.011294081807136536, "Left": 0.11481790989637375, "Top": 0.3597561717033386}, "Polygon": [{"X": 0.11482720077037811, "Y": 0.3597561717033386}, {"X": 0.16062723100185394, "Y": 0.35999560356140137}, {"X": 0.16062864661216736, "Y": 0.37105026841163635}, {"X": 0.11481790989637375, "Y": 0.3708365559577942}]}, "Id": "20c804ad-a134-44d8-a728-3460b2c13658"}, {"BlockType": "WORD", "Confidence": 61.187217712402344, "Text": "Ct.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019575893878936768, "Height": 0.010184190236032009, "Left": 0.16406121850013733, "Top": 0.3614310920238495}, "Polygon": [{"X": 0.16406121850013733, "Y": 0.3614310920238495}, {"X": 0.18363092839717865, "Y": 0.3615320026874542}, {"X": 0.1836371123790741, "Y": 0.37161529064178467}, {"X": 0.1640632450580597, "Y": 0.371524453163147}]}, "Id": "62f2bc6b-3775-4088-a104-3568782ca7e5"}, {"BlockType": "WORD", "Confidence": 85.04524230957031, "Text": "Luvata -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05121101438999176, "Height": 0.010983625426888466, "Left": 0.47346463799476624, "Top": 0.35556331276893616}, "Polygon": [{"X": 0.47346463799476624, "Y": 0.35556331276893616}, {"X": 0.5245921015739441, "Y": 0.35584667325019836}, {"X": 0.5246756672859192, "Y": 0.3665469288825989}, {"X": 0.4735368490219116, "Y": 0.36629191040992737}]}, "Id": "2c435364-2172-483f-be2c-d6f571b653fc"}, {"BlockType": "WORD", "Confidence": 92.95539855957031, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05731382220983505, "Height": 0.011806095950305462, "Left": 0.5292060971260071, "Top": 0.35722997784614563}, "Polygon": [{"X": 0.5292060971260071, "Y": 0.35722997784614563}, {"X": 0.58641517162323, "Y": 0.357543021440506}, {"X": 0.5865199565887451, "Y": 0.36903607845306396}, {"X": 0.5292972326278687, "Y": 0.3687572181224823}]}, "Id": "cb345939-6050-435a-b9cb-3ec00d2038a8"}, {"BlockType": "WORD", "Confidence": 95.18753814697266, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06676705181598663, "Height": 0.013842531479895115, "Left": 0.08295272290706635, "Top": 0.37414655089378357}, "Polygon": [{"X": 0.08297324180603027, "Y": 0.37414655089378357}, {"X": 0.14971977472305298, "Y": 0.37444618344306946}, {"X": 0.14971838891506195, "Y": 0.3879891037940979}, {"X": 0.08295272290706635, "Y": 0.3877354562282562}]}, "Id": "95502356-df3d-47c4-bfff-3f33c0533461"}, {"BlockType": "WORD", "Confidence": 97.72062683105469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02044104039669037, "Height": 0.010312682949006557, "Left": 0.15390683710575104, "Top": 0.37586766481399536}, "Polygon": [{"X": 0.15390697121620178, "Y": 0.37586766481399536}, {"X": 0.17434360086917877, "Y": 0.3759579360485077}, {"X": 0.1743478775024414, "Y": 0.3861803412437439}, {"X": 0.15390683710575104, "Y": 0.386100709438324}]}, "Id": "2cbef5a6-b80d-48fd-8893-3990ecfb3f6c"}, {"BlockType": "WORD", "Confidence": 99.0555191040039, "Text": "US", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02179727330803871, "Height": 0.01014026626944542, "Left": 0.17919893562793732, "Top": 0.377084344625473}, "Polygon": [{"X": 0.17919893562793732, "Y": 0.377084344625473}, {"X": 0.2009863704442978, "Y": 0.377179354429245}, {"X": 0.20099620521068573, "Y": 0.38722461462020874}, {"X": 0.17920416593551636, "Y": 0.38714075088500977}]}, "Id": "d3a8ac73-b438-4952-8da2-d10715c34633"}, {"BlockType": "WORD", "Confidence": 99.39472961425781, "Text": "54136", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043660350143909454, "Height": 0.010764557868242264, "Left": 0.205605149269104, "Top": 0.3775012493133545}, "Polygon": [{"X": 0.205605149269104, "Y": 0.3775012493133545}, {"X": 0.24924437701702118, "Y": 0.3776908814907074}, {"X": 0.24926550686359406, "Y": 0.38826578855514526}, {"X": 0.2056165635585785, "Y": 0.3880997598171234}]}, "Id": "7b6ee256-1d45-41a7-a82a-ab5766ba774d"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "553", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024015067145228386, "Height": 0.009983034804463387, "Left": 0.47348669171333313, "Top": 0.3701978921890259}, "Polygon": [{"X": 0.47348669171333313, "Y": 0.3701978921890259}, {"X": 0.49743038415908813, "Y": 0.3703124523162842}, {"X": 0.49750176072120667, "Y": 0.3801809251308441}, {"X": 0.4735531806945801, "Y": 0.38007861375808716}]}, "Id": "19f7fdf1-34c8-4f1b-ab08-4883f8a7ade3"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041554149240255356, "Height": 0.010273469612002373, "Left": 0.5012946128845215, "Top": 0.3708917796611786}, "Polygon": [{"X": 0.5012946128845215, "Y": 0.3708917796611786}, {"X": 0.5427662134170532, "Y": 0.37108898162841797}, {"X": 0.5428487658500671, "Y": 0.3811652362346649}, {"X": 0.5013684630393982, "Y": 0.3809897005558014}]}, "Id": "8d3b4717-b40b-4afd-87a4-f162cd225060"}, {"BlockType": "WORD", "Confidence": 88.55130004882812, "Text": "Ct.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017571667209267616, "Height": 0.009852007031440735, "Left": 0.5452194213867188, "Top": 0.3716822862625122}, "Polygon": [{"X": 0.5452194213867188, "Y": 0.3716822862625122}, {"X": 0.5627070069313049, "Y": 0.3717648983001709}, {"X": 0.562791109085083, "Y": 0.38153427839279175}, {"X": 0.5453000068664551, "Y": 0.3814605474472046}]}, "Id": "4901e877-3705-49ba-bca4-3177211577a4"}, {"BlockType": "WORD", "Confidence": 84.44495391845703, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05931295081973076, "Height": 0.01321363914757967, "Left": 0.47303304076194763, "Top": 0.38441258668899536}, "Polygon": [{"X": 0.47303304076194763, "Y": 0.38441258668899536}, {"X": 0.5322426557540894, "Y": 0.3846522569656372}, {"X": 0.5323460102081299, "Y": 0.39762622117996216}, {"X": 0.47312045097351074, "Y": 0.39742639660835266}]}, "Id": "f39381f5-03c8-4a56-bfa7-f9c790c7303a"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017890170216560364, "Height": 0.00952759850770235, "Left": 0.5363960266113281, "Top": 0.3862103223800659}, "Polygon": [{"X": 0.5363960266113281, "Y": 0.3862103223800659}, {"X": 0.5542064905166626, "Y": 0.38628101348876953}, {"X": 0.5542862415313721, "Y": 0.3957379162311554}, {"X": 0.5364722609519958, "Y": 0.39567601680755615}]}, "Id": "209b1606-5211-4ecd-be93-dceab8dabed7"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "US", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02001776359975338, "Height": 0.009716352447867393, "Left": 0.5585771799087524, "Top": 0.38667014241218567}, "Polygon": [{"X": 0.5585771799087524, "Y": 0.38667014241218567}, {"X": 0.5785087943077087, "Y": 0.3867488503456116}, {"X": 0.578594982624054, "Y": 0.3963865041732788}, {"X": 0.5586594343185425, "Y": 0.3963177800178528}]}, "Id": "22cf92b8-142a-4036-a0f9-451be12c98b5"}, {"BlockType": "WORD", "Confidence": 99.73552703857422, "Text": "54136", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03999849408864975, "Height": 0.009650126099586487, "Left": 0.5825434327125549, "Top": 0.38691529631614685}, "Polygon": [{"X": 0.5825434327125549, "Y": 0.38691529631614685}, {"X": 0.6224482655525208, "Y": 0.38707253336906433}, {"X": 0.6225419640541077, "Y": 0.39656540751457214}, {"X": 0.5826293230056763, "Y": 0.3964278995990753}]}, "Id": "9cf8722e-c1ea-4704-af98-f6bd349e0dc1"}, {"BlockType": "WORD", "Confidence": 90.72325134277344, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03737465664744377, "Height": 0.00836128182709217, "Left": 0.1317957639694214, "Top": 0.4220038652420044}, "Polygon": [{"X": 0.13179974257946014, "Y": 0.4220038652420044}, {"X": 0.16916786134243011, "Y": 0.42208099365234375}, {"X": 0.16917040944099426, "Y": 0.4303651452064514}, {"X": 0.1317957639694214, "Y": 0.43030375242233276}]}, "Id": "cbb4e2c8-6191-4886-97c3-6e1846781be9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025197768583893776, "Height": 0.008116474375128746, "Left": 0.47013694047927856, "Top": 0.42877092957496643}, "Polygon": [{"X": 0.47013694047927856, "Y": 0.42877092957496643}, {"X": 0.4952767491340637, "Y": 0.42881491780281067}, {"X": 0.4953346848487854, "Y": 0.4368874132633209}, {"X": 0.4701906740665436, "Y": 0.43685391545295715}]}, "Id": "0a686a62-1a35-4f4c-bb3b-a4940ccb4575"}, {"BlockType": "WORD", "Confidence": 93.87276458740234, "Text": "in:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01383238285779953, "Height": 0.00813322514295578, "Left": 0.4980486333370209, "Top": 0.4291839599609375}, "Polygon": [{"X": 0.4980486333370209, "Y": 0.4291839599609375}, {"X": 0.5118199586868286, "Y": 0.42920780181884766}, {"X": 0.5118809938430786, "Y": 0.4373171925544739}, {"X": 0.498107373714447, "Y": 0.43729913234710693}]}, "Id": "fb07909a-bcf1-42c4-b07f-d72136d8756b"}, {"BlockType": "WORD", "Confidence": 60.7292366027832, "Text": "6/15/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052595291286706924, "Height": 0.008023938164114952, "Left": 0.5443468689918518, "Top": 0.4299773573875427}, "Polygon": [{"X": 0.5443468689918518, "Y": 0.4299773573875427}, {"X": 0.5968682169914246, "Y": 0.4300663471221924}, {"X": 0.596942126750946, "Y": 0.43800127506256104}, {"X": 0.5444121956825256, "Y": 0.437934011220932}]}, "Id": "aa74f1f9-4404-44c8-99cc-baea94dbc0a1"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026081781834363937, "Height": 0.00791134499013424, "Left": 0.666053056716919, "Top": 0.4308325946331024}, "Polygon": [{"X": 0.666053056716919, "Y": 0.4308325946331024}, {"X": 0.6920457482337952, "Y": 0.4308757483959198}, {"X": 0.6921348571777344, "Y": 0.4387439489364624}, {"X": 0.6661379933357239, "Y": 0.4387114942073822}]}, "Id": "a361eb0e-29ae-4feb-9828-19f8201f76fb"}, {"BlockType": "WORD", "Confidence": 42.46651840209961, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012980068102478981, "Height": 0.007837467826902866, "Left": 0.6948158144950867, "Top": 0.43103742599487305}, "Polygon": [{"X": 0.6948158144950867, "Y": 0.43103742599487305}, {"X": 0.7077047824859619, "Y": 0.43105870485305786}, {"X": 0.7077958583831787, "Y": 0.43887487053871155}, {"X": 0.6949048638343811, "Y": 0.43885886669158936}]}, "Id": "5cc6ae9c-34f6-44ed-bde8-3d720d8423f1"}, {"BlockType": "WORD", "Confidence": 89.21855163574219, "Text": "9:49", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023182353004813194, "Height": 0.007946155034005642, "Left": 0.7389572858810425, "Top": 0.4316715896129608}, "Polygon": [{"X": 0.7389572858810425, "Y": 0.4316715896129608}, {"X": 0.7620384097099304, "Y": 0.43170905113220215}, {"X": 0.762139618396759, "Y": 0.43961775302886963}, {"X": 0.7390547394752502, "Y": 0.43958988785743713}]}, "Id": "e9e8e507-dde9-4f9b-b15f-c756ae5ceec4"}, {"BlockType": "WORD", "Confidence": 99.0625, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01789201609790325, "Height": 0.007871263660490513, "Left": 0.7636447548866272, "Top": 0.43181928992271423}, "Polygon": [{"X": 0.7636447548866272, "Y": 0.43181928992271423}, {"X": 0.7814331650733948, "Y": 0.43184804916381836}, {"X": 0.7815367579460144, "Y": 0.4396905303001404}, {"X": 0.7637454867362976, "Y": 0.439669132232666}]}, "Id": "227e8949-88c0-4b20-b219-048c024008d5"}, {"BlockType": "WORD", "Confidence": 75.87989807128906, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03370414674282074, "Height": 0.008900873363018036, "Left": 0.1217619851231575, "Top": 0.4465324282646179}, "Polygon": [{"X": 0.12176812440156937, "Y": 0.4465324282646179}, {"X": 0.1554659605026245, "Y": 0.4465599060058594}, {"X": 0.15546613931655884, "Y": 0.45543327927589417}, {"X": 0.1217619851231575, "Y": 0.4554210305213928}]}, "Id": "9db87ed7-a252-4d3b-8857-171114d61b09"}, {"BlockType": "WORD", "Confidence": 98.59135437011719, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012077627703547478, "Height": 0.008097633719444275, "Left": 0.15821708738803864, "Top": 0.4475705623626709}, "Polygon": [{"X": 0.15821708738803864, "Y": 0.4475705623626709}, {"X": 0.17029203474521637, "Y": 0.4475797712802887}, {"X": 0.17029471695423126, "Y": 0.455668181180954}, {"X": 0.15821771323680878, "Y": 0.4556639492511749}]}, "Id": "ac82358d-3db3-4c56-ad37-e9cfc1270d43"}, {"BlockType": "WORD", "Confidence": 98.20950317382812, "Text": "1055", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02812754362821579, "Height": 0.008152617141604424, "Left": 0.1759336292743683, "Top": 0.4477693736553192}, "Polygon": [{"X": 0.1759336292743683, "Y": 0.4477693736553192}, {"X": 0.20405268669128418, "Y": 0.44779059290885925}, {"X": 0.20406116545200348, "Y": 0.45592200756073}, {"X": 0.17593729496002197, "Y": 0.45591244101524353}]}, "Id": "c359c39b-e5ce-4b28-86a0-87afa4316d03"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024550767615437508, "Height": 0.007835078053176403, "Left": 0.4703095555305481, "Top": 0.45249709486961365}, "Polygon": [{"X": 0.4703095555305481, "Y": 0.45249709486961365}, {"X": 0.4948042631149292, "Y": 0.4525098502635956}, {"X": 0.49486032128334045, "Y": 0.46033215522766113}, {"X": 0.4703616201877594, "Y": 0.4603293240070343}]}, "Id": "74063747-efa0-45b4-8df4-a729216dff77"}, {"BlockType": "WORD", "Confidence": 88.27227783203125, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021611837670207024, "Height": 0.00807888898998499, "Left": 0.49760159850120544, "Top": 0.45254048705101013}, "Polygon": [{"X": 0.49760159850120544, "Y": 0.45254048705101013}, {"X": 0.519151508808136, "Y": 0.4525516927242279}, {"X": 0.5192134380340576, "Y": 0.46061936020851135}, {"X": 0.49765995144844055, "Y": 0.46061718463897705}]}, "Id": "621941b1-4683-49e3-9efe-04452a2de221"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "6/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05231180042028427, "Height": 0.008039681240916252, "Left": 0.5439172387123108, "Top": 0.45341795682907104}, "Polygon": [{"X": 0.5439172387123108, "Y": 0.45341795682907104}, {"X": 0.5961544513702393, "Y": 0.45344278216362}, {"X": 0.5962290167808533, "Y": 0.46145761013031006}, {"X": 0.5439831018447876, "Y": 0.4614546000957489}]}, "Id": "644e91f5-834d-4627-b6d1-c2dd5244a88d"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02627493068575859, "Height": 0.007583187893033028, "Left": 0.6647686958312988, "Top": 0.454377681016922}, "Polygon": [{"X": 0.6647686958312988, "Y": 0.454377681016922}, {"X": 0.6909580826759338, "Y": 0.45438888669013977}, {"X": 0.6910436153411865, "Y": 0.4619608521461487}, {"X": 0.6648501753807068, "Y": 0.4619600176811218}]}, "Id": "8fa1e462-f8ea-4321-b00a-de4a36eb8643"}, {"BlockType": "WORD", "Confidence": 87.39616394042969, "Text": "Out:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02308724820613861, "Height": 0.007905099540948868, "Left": 0.6933650374412537, "Top": 0.4544546604156494}, "Polygon": [{"X": 0.6933650374412537, "Y": 0.4544546604156494}, {"X": 0.7163589000701904, "Y": 0.4544644355773926}, {"X": 0.7164523005485535, "Y": 0.462359756231308}, {"X": 0.6934547424316406, "Y": 0.4623595178127289}]}, "Id": "7f8fd0a5-6369-4572-8f9c-eb2404d1175f"}, {"BlockType": "WORD", "Confidence": 97.92171478271484, "Text": "12:35", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02830228954553604, "Height": 0.007686277385801077, "Left": 0.7389745116233826, "Top": 0.4550369381904602}, "Polygon": [{"X": 0.7389745116233826, "Y": 0.4550369381904602}, {"X": 0.7671777606010437, "Y": 0.4550480842590332}, {"X": 0.7672767639160156, "Y": 0.4627229869365692}, {"X": 0.7390691041946411, "Y": 0.4627232253551483}]}, "Id": "dc835140-6145-4099-9c4e-308596fa69c2"}, {"BlockType": "WORD", "Confidence": 99.53125, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01750854402780533, "Height": 0.007902998477220535, "Left": 0.7694516777992249, "Top": 0.45505985617637634}, "Polygon": [{"X": 0.7694516777992249, "Y": 0.45505985617637634}, {"X": 0.7868551015853882, "Y": 0.4550667405128479}, {"X": 0.786960244178772, "Y": 0.4629625082015991}, {"X": 0.7695540189743042, "Y": 0.4629628658294678}]}, "Id": "491a4618-651e-401e-8c25-a30d6d6e2934"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0341232530772686, "Height": 0.008565099909901619, "Left": 0.12215480208396912, "Top": 0.4715676009654999}, "Polygon": [{"X": 0.12216063588857651, "Y": 0.4715832471847534}, {"X": 0.15627774596214294, "Y": 0.4715676009654999}, {"X": 0.15627805888652802, "Y": 0.48010221123695374}, {"X": 0.12215480208396912, "Y": 0.48013269901275635}]}, "Id": "a5adb8c3-a415-42ab-9d7a-901b003f6766"}, {"BlockType": "WORD", "Confidence": 82.04240417480469, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011360567063093185, "Height": 0.007805385161191225, "Left": 0.15839239954948425, "Top": 0.4723900854587555}, "Polygon": [{"X": 0.15839239954948425, "Y": 0.47239577770233154}, {"X": 0.16975046694278717, "Y": 0.4723900854587555}, {"X": 0.16975297033786774, "Y": 0.48018527030944824}, {"X": 0.1583930253982544, "Y": 0.4801954925060272}]}, "Id": "a2d092a2-f463-441c-bb43-d5046c5096d2"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "303735", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04392945021390915, "Height": 0.008566739037632942, "Left": 0.17507879436016083, "Top": 0.4722879230976105}, "Polygon": [{"X": 0.17507879436016083, "Y": 0.47230973839759827}, {"X": 0.21899668872356415, "Y": 0.4722879230976105}, {"X": 0.21900825202465057, "Y": 0.4808136820793152}, {"X": 0.1750824898481369, "Y": 0.48085466027259827}]}, "Id": "e0a78f57-055f-4adb-ba5c-9e9cf3c982fb"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042282991111278534, "Height": 0.009778709150850773, "Left": 0.48506882786750793, "Top": 0.4757763743400574}, "Polygon": [{"X": 0.48506882786750793, "Y": 0.4758053123950958}, {"X": 0.5272755026817322, "Y": 0.4757763743400574}, {"X": 0.5273518562316895, "Y": 0.48550477623939514}, {"X": 0.48513665795326233, "Y": 0.48555508255958557}]}, "Id": "b988f7ed-31c1-41ac-a4d8-0cf631f64d1a"}, {"BlockType": "WORD", "Confidence": 89.48003387451172, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01072537899017334, "Height": 0.008252800442278385, "Left": 0.529310941696167, "Top": 0.47634321451187134}, "Polygon": [{"X": 0.529310941696167, "Y": 0.4763508439064026}, {"X": 0.5399694442749023, "Y": 0.47634321451187134}, {"X": 0.5400363206863403, "Y": 0.4845837950706482}, {"X": 0.5293759703636169, "Y": 0.48459601402282715}]}, "Id": "8ceae15e-1eee-4e86-af57-43b93cd8d2b1"}, {"BlockType": "WORD", "Confidence": 99.6827163696289, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05762343481183052, "Height": 0.008590824902057648, "Left": 0.09859102964401245, "Top": 0.49659407138824463}, "Polygon": [{"X": 0.09860102832317352, "Y": 0.4966939687728882}, {"X": 0.15621417760849, "Y": 0.49659407138824463}, {"X": 0.15621446073055267, "Y": 0.5050601363182068}, {"X": 0.09859102964401245, "Y": 0.5051848888397217}]}, "Id": "1302afcc-f46e-443a-a550-23cbc4975da9"}, {"BlockType": "WORD", "Confidence": 58.18658447265625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011458178050816059, "Height": 0.0080776521936059, "Left": 0.15858598053455353, "Top": 0.4973505735397339}, "Polygon": [{"X": 0.15858598053455353, "Y": 0.4973708987236023}, {"X": 0.1700415313243866, "Y": 0.4973505735397339}, {"X": 0.1700441539287567, "Y": 0.5054032206535339}, {"X": 0.15858665108680725, "Y": 0.5054282546043396}]}, "Id": "dda1ae30-626c-4d3a-9ee0-d1779e7c19f0"}, {"BlockType": "WORD", "Confidence": 99.35247802734375, "Text": "225340", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04385821148753166, "Height": 0.00869056861847639, "Left": 0.17498408257961273, "Top": 0.4970148801803589}, "Polygon": [{"X": 0.17498408257961273, "Y": 0.497092068195343}, {"X": 0.2188306599855423, "Y": 0.4970148801803589}, {"X": 0.2188422828912735, "Y": 0.5056089758872986}, {"X": 0.1749877780675888, "Y": 0.5057054162025452}]}, "Id": "463530f6-a33f-48ef-a01d-d2fc9bb338f5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "See", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02351127751171589, "Height": 0.008412908762693405, "Left": 0.08024271577596664, "Top": 0.5215277075767517}, "Polygon": [{"X": 0.08025575429201126, "Y": 0.5215981006622314}, {"X": 0.10375399887561798, "Y": 0.5215277075767517}, {"X": 0.10374508798122406, "Y": 0.5298601984977722}, {"X": 0.08024271577596664, "Y": 0.5299406051635742}]}, "Id": "78232bda-1481-436d-bf1f-2bade60acad0"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046187710016965866, "Height": 0.010492800734937191, "Left": 0.10732642561197281, "Top": 0.5213524699211121}, "Polygon": [{"X": 0.10733671486377716, "Y": 0.521490752696991}, {"X": 0.15351414680480957, "Y": 0.5213524699211121}, {"X": 0.15351390838623047, "Y": 0.5316826105117798}, {"X": 0.10732642561197281, "Y": 0.5318452715873718}]}, "Id": "9677b24b-cd83-465a-bf1c-b5b2d72ff6d9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "List", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021014254540205002, "Height": 0.008656499907374382, "Left": 0.1567956656217575, "Top": 0.5215960741043091}, "Polygon": [{"X": 0.1567956656217575, "Y": 0.5216593742370605}, {"X": 0.17780572175979614, "Y": 0.5215960741043091}, {"X": 0.1778099089860916, "Y": 0.5301800966262817}, {"X": 0.15679606795310974, "Y": 0.5302525758743286}]}, "Id": "aed73147-63f8-4382-adb8-f1b151023c59"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015871189534664154, "Height": 0.008862530812621117, "Left": 0.18058790266513824, "Top": 0.5215318202972412}, "Polygon": [{"X": 0.18058790266513824, "Y": 0.5215795636177063}, {"X": 0.196451336145401, "Y": 0.5215318202972412}, {"X": 0.196459099650383, "Y": 0.5303394198417664}, {"X": 0.18059273064136505, "Y": 0.5303943753242493}]}, "Id": "f17edd18-bdff-4eb5-95db-688bf696e04e"}, {"BlockType": "WORD", "Confidence": 98.88392639160156, "Text": "Details", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039466891437768936, "Height": 0.008659251034259796, "Left": 0.19893497228622437, "Top": 0.5216935873031616}, "Polygon": [{"X": 0.19893497228622437, "Y": 0.5218129754066467}, {"X": 0.2383868396282196, "Y": 0.5216935873031616}, {"X": 0.238401859998703, "Y": 0.5302162766456604}, {"X": 0.19894294440746307, "Y": 0.5303528308868408}]}, "Id": "0ad7df90-9d57-41ed-908c-09198eca5a73"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02154392935335636, "Height": 0.007777324877679348, "Left": 0.4711717963218689, "Top": 0.5228367447853088}, "Polygon": [{"X": 0.4711717963218689, "Y": 0.5229038596153259}, {"X": 0.4926609694957733, "Y": 0.5228367447853088}, {"X": 0.4927157461643219, "Y": 0.5305383205413818}, {"X": 0.4712231457233429, "Y": 0.5306140184402466}]}, "Id": "0d3cf511-24d6-44ff-ad13-39c69a2664a8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WEIGHTS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05392380803823471, "Height": 0.008780626580119133, "Left": 0.49474194645881653, "Top": 0.5222272276878357}, "Polygon": [{"X": 0.49474194645881653, "Y": 0.5223942995071411}, {"X": 0.54859459400177, "Y": 0.5222272276878357}, {"X": 0.5486657619476318, "Y": 0.5308167338371277}, {"X": 0.4948035776615143, "Y": 0.5310078859329224}]}, "Id": "db03c0a4-34e9-4350-addc-d7ee00178792"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "ARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024452602490782738, "Height": 0.008235727436840534, "Left": 0.5514639019966125, "Top": 0.5228798985481262}, "Polygon": [{"X": 0.5514639019966125, "Y": 0.5229564309120178}, {"X": 0.5758443474769592, "Y": 0.5228798985481262}, {"X": 0.5759165287017822, "Y": 0.531028687953949}, {"X": 0.5515320301055908, "Y": 0.5311156511306763}]}, "Id": "88f7ac91-4eb1-4d02-9b5a-2b0c4337c9ce"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "REPORTED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06357213854789734, "Height": 0.008527224883437157, "Left": 0.5786811709403992, "Top": 0.5229507088661194}, "Polygon": [{"X": 0.5786811709403992, "Y": 0.5231510400772095}, {"X": 0.6421682238578796, "Y": 0.5229507088661194}, {"X": 0.6422533392906189, "Y": 0.5312500596046448}, {"X": 0.5787554383277893, "Y": 0.5314779281616211}]}, "Id": "e7960890-5e06-4780-976d-4942632b6730"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "IN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011817348189651966, "Height": 0.007663652300834656, "Left": 0.6449434757232666, "Top": 0.5237053036689758}, "Polygon": [{"X": 0.6449434757232666, "Y": 0.5237428545951843}, {"X": 0.6566803455352783, "Y": 0.5237053036689758}, {"X": 0.6567608118057251, "Y": 0.5313267707824707}, {"X": 0.6450221538543701, "Y": 0.5313689708709717}]}, "Id": "b5c23792-0926-457c-85cd-18981887663f"}, {"BlockType": "WORD", "Confidence": 99.37181091308594, "Text": "LB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014355259016156197, "Height": 0.007779053412377834, "Left": 0.6593382358551025, "Top": 0.5234880447387695}, "Polygon": [{"X": 0.6593382358551025, "Y": 0.5235335230827332}, {"X": 0.67360919713974, "Y": 0.5234880447387695}, {"X": 0.6736935377120972, "Y": 0.5312158465385437}, {"X": 0.659420371055603, "Y": 0.5312671065330505}]}, "Id": "4322564b-ce98-4302-949b-3ca6ec1a3f93"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Material", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0613228864967823, "Height": 0.010366171598434448, "Left": 0.12814313173294067, "Top": 0.5456355810165405}, "Polygon": [{"X": 0.12814873456954956, "Y": 0.545895516872406}, {"X": 0.18945862352848053, "Y": 0.5456355810165405}, {"X": 0.18946601450443268, "Y": 0.5557101964950562}, {"X": 0.12814313173294067, "Y": 0.5560017228126526}]}, "Id": "973f24c4-52eb-4470-9f40-c8a157f36ddb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04130930453538895, "Height": 0.009613722562789917, "Left": 0.49190467596054077, "Top": 0.5455789566040039}, "Polygon": [{"X": 0.49190467596054077, "Y": 0.5457568168640137}, {"X": 0.5331390500068665, "Y": 0.5455789566040039}, {"X": 0.5332139730453491, "Y": 0.5549946427345276}, {"X": 0.49197158217430115, "Y": 0.5551927089691162}]}, "Id": "77830e95-53cf-480a-83fa-79ba40b7e67f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04832473024725914, "Height": 0.011892120353877544, "Left": 0.5372870564460754, "Top": 0.5452661514282227}, "Polygon": [{"X": 0.5372870564460754, "Y": 0.5454738736152649}, {"X": 0.5855062007904053, "Y": 0.5452661514282227}, {"X": 0.5856118202209473, "Y": 0.5569211840629578}, {"X": 0.5373810529708862, "Y": 0.5571582913398743}]}, "Id": "b1234fbb-3824-46e1-ab55-963e87fed3f2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030124913901090622, "Height": 0.009014266543090343, "Left": 0.6162781119346619, "Top": 0.5461270213127136}, "Polygon": [{"X": 0.6162781119346619, "Y": 0.5462581515312195}, {"X": 0.646311342716217, "Y": 0.5461270213127136}, {"X": 0.6464030146598816, "Y": 0.5549961924552917}, {"X": 0.6163643002510071, "Y": 0.555141270160675}]}, "Id": "f61df6b6-4ba6-4511-950f-667be3664e7a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04920634627342224, "Height": 0.012005836702883244, "Left": 0.6497723460197449, "Top": 0.5457671284675598}, "Polygon": [{"X": 0.6497723460197449, "Y": 0.5459811091423035}, {"X": 0.6988441348075867, "Y": 0.5457671284675598}, {"X": 0.6989786624908447, "Y": 0.5575287342071533}, {"X": 0.6498950719833374, "Y": 0.5577729940414429}]}, "Id": "5e9d44ab-3356-4201-b055-ea0e554fa01c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023297008126974106, "Height": 0.009500911459326744, "Left": 0.7363812327384949, "Top": 0.5461316108703613}, "Polygon": [{"X": 0.7363812327384949, "Y": 0.5462334156036377}, {"X": 0.7595588564872742, "Y": 0.5461316108703613}, {"X": 0.7596782445907593, "Y": 0.5555192232131958}, {"X": 0.7364961504936218, "Y": 0.5556325316429138}]}, "Id": "7f05253c-8c01-42c6-a5a2-f9b0d8a0885b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04914350435137749, "Height": 0.011722219176590443, "Left": 0.762252926826477, "Top": 0.546078085899353}, "Polygon": [{"X": 0.762252926826477, "Y": 0.5462937355041504}, {"X": 0.8112379908561707, "Y": 0.546078085899353}, {"X": 0.8113964200019836, "Y": 0.5575549602508545}, {"X": 0.7623999118804932, "Y": 0.55780029296875}]}, "Id": "a8aef333-06d2-474c-bd62-b59beb4ebb0a"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Copper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054162152111530304, "Height": 0.01244308054447174, "Left": 0.1280786693096161, "Top": 0.5620929002761841}, "Polygon": [{"X": 0.1280854344367981, "Y": 0.5623679161071777}, {"X": 0.1822337657213211, "Y": 0.5620929002761841}, {"X": 0.1822408139705658, "Y": 0.5742273926734924}, {"X": 0.1280786693096161, "Y": 0.5745360255241394}]}, "Id": "2ef89ab2-64b4-4f55-8195-0ee73b379f2a"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Cathode", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05987217649817467, "Height": 0.010560424998402596, "Left": 0.18523290753364563, "Top": 0.5617846250534058}, "Polygon": [{"X": 0.18523290753364563, "Y": 0.5620886087417603}, {"X": 0.24508564174175262, "Y": 0.5617846250534058}, {"X": 0.2451050877571106, "Y": 0.5720096826553345}, {"X": 0.1852395236492157, "Y": 0.5723450779914856}]}, "Id": "1db1bc4b-c6b2-4935-8502-e05130b38e8d"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Medium", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055536650121212006, "Height": 0.010275418870151043, "Left": 0.24966858327388763, "Top": 0.5617061853408813}, "Polygon": [{"X": 0.24966858327388763, "Y": 0.5619887113571167}, {"X": 0.30517369508743286, "Y": 0.5617061853408813}, {"X": 0.30520522594451904, "Y": 0.5716705918312073}, {"X": 0.2496885508298874, "Y": 0.5719816088676453}]}, "Id": "e00ba043-6f23-4a7a-805c-99e8800d6bd3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Chops", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04477647319436073, "Height": 0.01209667045623064, "Left": 0.3088400363922119, "Top": 0.561432957649231}, "Polygon": [{"X": 0.3088400363922119, "Y": 0.5616605877876282}, {"X": 0.3535670042037964, "Y": 0.561432957649231}, {"X": 0.35361650586128235, "Y": 0.5732746720314026}, {"X": 0.30887851119041443, "Y": 0.5735296607017517}]}, "Id": "233af2fb-cb1a-4cd6-a6ee-a3dc1d31b36d"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "44,835.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06407298892736435, "Height": 0.010785887017846107, "Left": 0.5212148427963257, "Top": 0.5614290833473206}, "Polygon": [{"X": 0.5212148427963257, "Y": 0.5617585778236389}, {"X": 0.5851935148239136, "Y": 0.5614290833473206}, {"X": 0.5852878093719482, "Y": 0.5718506574630737}, {"X": 0.521295428276062, "Y": 0.5722149610519409}]}, "Id": "c971cf9b-c2d6-4334-8730-6bda9300dbe9"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "835.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04462321475148201, "Height": 0.009870517067611217, "Left": 0.6540040373802185, "Top": 0.5614732503890991}, "Polygon": [{"X": 0.6540040373802185, "Y": 0.5617039799690247}, {"X": 0.6985173225402832, "Y": 0.5614732503890991}, {"X": 0.6986272931098938, "Y": 0.571090579032898}, {"X": 0.6541052460670471, "Y": 0.5713437795639038}]}, "Id": "3b13f79e-4008-4928-819f-6068e39b1784"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "44,000.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06305070966482162, "Height": 0.01071843784302473, "Left": 0.7476527094841003, "Top": 0.5617641806602478}, "Polygon": [{"X": 0.7476527094841003, "Y": 0.5620931386947632}, {"X": 0.8105607032775879, "Y": 0.5617641806602478}, {"X": 0.810703456401825, "Y": 0.5721192955970764}, {"X": 0.7477821707725525, "Y": 0.5724826455116272}]}, "Id": "e43cf33f-04bd-4d7d-b2cc-c8d561e47e50"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06981779634952545, "Height": 0.012023011222481728, "Left": 0.3248096704483032, "Top": 0.593878448009491}, "Polygon": [{"X": 0.3248096704483032, "Y": 0.5943509936332703}, {"X": 0.394569456577301, "Y": 0.593878448009491}, {"X": 0.39462748169898987, "Y": 0.6053874492645264}, {"X": 0.32485097646713257, "Y": 0.6059014797210693}]}, "Id": "af623a2e-dc95-4745-b51a-4717fa64840c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Weights:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06236163154244423, "Height": 0.012713845819234848, "Left": 0.3973621726036072, "Top": 0.5930936932563782}, "Polygon": [{"X": 0.3973621726036072, "Y": 0.5935144424438477}, {"X": 0.4596453011035919, "Y": 0.5930936932563782}, {"X": 0.4597238004207611, "Y": 0.6053471565246582}, {"X": 0.3974248468875885, "Y": 0.6058075428009033}]}, "Id": "fb398f0e-56dd-4ece-8e5a-c2e85ecaf40c"}, {"BlockType": "WORD", "Confidence": 93.27427673339844, "Text": "78,080.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06415561586618423, "Height": 0.011034147813916206, "Left": 0.5212807655334473, "Top": 0.5923334360122681}, "Polygon": [{"X": 0.5212807655334473, "Y": 0.592766523361206}, {"X": 0.5853407979011536, "Y": 0.5923334360122681}, {"X": 0.5854364037513733, "Y": 0.6028991937637329}, {"X": 0.5213624238967896, "Y": 0.6033676266670227}]}, "Id": "6e7b1f76-ce57-4d0e-87d2-1f41ef6be848"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "33,260.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06453767418861389, "Height": 0.011185291223227978, "Left": 0.6348206996917725, "Top": 0.5925306677818298}, "Polygon": [{"X": 0.6348206996917725, "Y": 0.5929694175720215}, {"X": 0.6992358565330505, "Y": 0.5925306677818298}, {"X": 0.6993584036827087, "Y": 0.603240966796875}, {"X": 0.6349291205406189, "Y": 0.6037159562110901}]}, "Id": "189ee663-2e5e-43d2-ab3d-61f93be47cf3"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "44,820.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06311559677124023, "Height": 0.010742926970124245, "Left": 0.7482593655586243, "Top": 0.5928544402122498}, "Polygon": [{"X": 0.7482593655586243, "Y": 0.59328693151474}, {"X": 0.8112332820892334, "Y": 0.5928544402122498}, {"X": 0.8113749623298645, "Y": 0.6031306385993958}, {"X": 0.7483879327774048, "Y": 0.6035973429679871}]}, "Id": "bc2d2574-ea9b-4871-b97a-59daf26df56d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Delivered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06725474447011948, "Height": 0.01167316734790802, "Left": 0.0654992014169693, "Top": 0.7829810380935669}, "Polygon": [{"X": 0.06551890075206757, "Y": 0.784077525138855}, {"X": 0.13275393843650818, "Y": 0.7829810380935669}, {"X": 0.13274914026260376, "Y": 0.7935214638710022}, {"X": 0.0654992014169693, "Y": 0.7946542501449585}]}, "Id": "7cc95dab-cc04-4efa-a2a4-636a5e4f17bd"}, {"BlockType": "WORD", "Confidence": 96.56230163574219, "Text": "By:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021263206377625465, "Height": 0.013037407770752907, "Left": 0.13749705255031586, "Top": 0.7823114395141602}, "Polygon": [{"X": 0.1375015825033188, "Y": 0.78265780210495}, {"X": 0.15875914692878723, "Y": 0.7823114395141602}, {"X": 0.15876026451587677, "Y": 0.7949886322021484}, {"X": 0.13749705255031586, "Y": 0.7953488230705261}]}, "Id": "ba1d193e-3244-4883-84e7-53ebd3778dfc"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033802106976509094, "Height": 0.010510919615626335, "Left": 0.6045927405357361, "Top": 0.7721703052520752}, "Polygon": [{"X": 0.6045927405357361, "Y": 0.7727153897285461}, {"X": 0.6382941603660583, "Y": 0.7721703052520752}, {"X": 0.638394832611084, "Y": 0.782118558883667}, {"X": 0.6046865582466125, "Y": 0.7826812267303467}]}, "Id": "063c893b-e06c-46c2-b6a7-1d44f1c6eac4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06701146811246872, "Height": 0.01205664873123169, "Left": 0.06354586035013199, "Top": 0.8260182738304138}, "Polygon": [{"X": 0.0635664314031601, "Y": 0.8272573351860046}, {"X": 0.1305573284626007, "Y": 0.8260182738304138}, {"X": 0.13055191934108734, "Y": 0.8367987871170044}, {"X": 0.06354586035013199, "Y": 0.8380749225616455}]}, "Id": "be1fd230-13b0-4ffb-9740-5a48e9c20952"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "By:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022285453975200653, "Height": 0.01321572158485651, "Left": 0.13526536524295807, "Top": 0.8254024982452393}, "Polygon": [{"X": 0.13527053594589233, "Y": 0.8258144855499268}, {"X": 0.15755002200603485, "Y": 0.8254024982452393}, {"X": 0.15755082666873932, "Y": 0.8381916284561157}, {"X": 0.13526536524295807, "Y": 0.8386182188987732}]}, "Id": "6780f32d-efc6-46e0-9020-10d2712c30cb"}, {"BlockType": "WORD", "Confidence": 98.65533447265625, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03486527130007744, "Height": 0.011263852939009666, "Left": 0.605013906955719, "Top": 0.8130304217338562}, "Polygon": [{"X": 0.605013906955719, "Y": 0.813666820526123}, {"X": 0.639771580696106, "Y": 0.8130304217338562}, {"X": 0.6398791670799255, "Y": 0.8236384391784668}, {"X": 0.6051139235496521, "Y": 0.8242942690849304}]}, "Id": "39ebd206-be07-4266-b164-1d64873fde4f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "ad8706bd-ec71-449f-b6b9-1fac14a084ea", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "ad8706bd-ec71-449f-b6b9-1fac14a084ea", "content-type": "application/x-amz-json-1.1", "content-length": "94658", "date": "Wed, 24 Sep 2025 13:35:02 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c237ebc0-57bb-4f29-962e-40c74ee18dad", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:35:07 GMT", "content-type": "application/json", "content-length": "747", "connection": "keep-alive", "x-amzn-requestid": "c237ebc0-57bb-4f29-962e-40c74ee18dad"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need classify each page. Only page1 provided. It's a Delivery Ticket. It has \"DELIVERY TICKET\" header. Possibly it's a POD or Delivery Ticket. In definitions, POD: Proof of Delivery. Delivery Ticket could be a POD. The fields: Received By, Delivered By. So likely doc_type = pod. There's no explicit \"Proof of Delivery\", but delivery ticket is essentially a POD. So output page 1 as pod.\n\nReturn JSON with documents array.\n\n"}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"pod\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2872, "outputTokens": 113, "totalTokens": 2985}, "metrics": {"latencyMs": 706}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7f62e6a0_2_pod_2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nDELIVERY TICKET\nVENTURE\nMETALS+\n/ I I\nWills Point\n503 Co Rd 3805\nShipment #\nWills Point, TX USA 75169\n903-873-3811\nWIL106604\nContract <PERSON>vat<PERSON> Appleton May 2025 PO 303735\nContract # 14307\nPack List #: 22234\nShip To:\nMill Order # 303735\nSold To\nPurchase Order #303735\nLuvata - Appleton\n553 Carter Ct.\nLuvata - Appleton\nKimberly, WI US 54136\n553 Carter Ct.\nKimberly, WI US 54136\nDriver\nDate in:\n6/15/2025\nTime In\n9:49 AM\nTruck #: 1055\nDate Out 6/16/2025\nTime Out:\n12:35 PM\nOrder #: 303735\nBooking #:\nContainer #: 225340\nSee Packing List for Details\nALL WEIGHTS ARE REPORTED IN LB\nMaterial\nGross Weight\nTare Weight\nNet Weight\nCopper Cathode Medium Chops\n44,835.00\n835.00\n44,000.00\nShipment Weights:\n78,080.00\n33,260.00\n44,820.00\nDelivered By:\nDate:\nReceived By:\nDate:\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}