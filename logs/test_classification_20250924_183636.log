2025-09-24 18:36:36,106 - INFO - Logging initialized. Log file: logs/test_classification_20250924_183636.log
2025-09-24 18:36:36,106 - INFO - 📁 Found 8 files to process
2025-09-24 18:36:36,106 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:36:36,106 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 18:36:36,106 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 18:36:36,106 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 18:36:36,106 - INFO - ⬆️ [18:36:36] Uploading: CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:36:39,591 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf -> s3://document-extraction-logistically/temp/74c4d607_CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:36:39,591 - INFO - 🔍 [18:36:39] Starting classification: CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:36:39,591 - INFO - ⬆️ [18:36:39] Uploading: EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:39,592 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:39,603 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:39,610 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/74c4d607_CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:36:39,610 - INFO - Processing PDF from S3...
2025-09-24 18:36:39,610 - INFO - Downloading PDF from S3 to /tmp/tmp_cx46l3f/74c4d607_CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:36:40,405 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/EHU9I6PL4HKG590OCG8Y.pdf -> s3://document-extraction-logistically/temp/49a18cb4_EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:40,405 - INFO - 🔍 [18:36:40] Starting classification: EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:40,406 - INFO - ⬆️ [18:36:40] Uploading: FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:40,408 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:40,425 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:40,428 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/49a18cb4_EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:40,429 - INFO - Processing PDF from S3...
2025-09-24 18:36:40,429 - INFO - Downloading PDF from S3 to /tmp/tmp2v1b3hua/49a18cb4_EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:41,108 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/FIXBJNBFB5JY1H27XN70.pdf -> s3://document-extraction-logistically/temp/2eb1a12e_FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:41,108 - INFO - 🔍 [18:36:41] Starting classification: FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:41,109 - INFO - ⬆️ [18:36:41] Uploading: MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:41,112 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:41,133 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:41,136 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2eb1a12e_FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:41,137 - INFO - Processing PDF from S3...
2025-09-24 18:36:41,138 - INFO - Downloading PDF from S3 to /tmp/tmp54qnfgv5/2eb1a12e_FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:41,800 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/MMITIPOIZN33LXTLSS38.pdf -> s3://document-extraction-logistically/temp/d19f2c5b_MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:41,800 - INFO - 🔍 [18:36:41] Starting classification: MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:41,801 - INFO - ⬆️ [18:36:41] Uploading: RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:41,804 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:41,828 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:41,832 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d19f2c5b_MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:41,832 - INFO - Processing PDF from S3...
2025-09-24 18:36:41,832 - INFO - Downloading PDF from S3 to /tmp/tmpn44l5yun/d19f2c5b_MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:41,971 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 18:36:41,971 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:41,974 - INFO - Splitting PDF 74c4d607_CY588RKQ3XR0K6IZ4AIS into 7 pages
2025-09-24 18:36:41,997 - INFO - Split PDF into 7 pages
2025-09-24 18:36:41,997 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:41,997 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 18:36:42,406 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/RYX8J947D5KTZDL0KST2.pdf -> s3://document-extraction-logistically/temp/b5ce3307_RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:42,406 - INFO - 🔍 [18:36:42] Starting classification: RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:42,407 - INFO - ⬆️ [18:36:42] Uploading: TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:42,407 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:42,419 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:42,422 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b5ce3307_RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:42,422 - INFO - Processing PDF from S3...
2025-09-24 18:36:42,422 - INFO - Downloading PDF from S3 to /tmp/tmp78x0v_3v/b5ce3307_RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:42,591 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 18:36:42,591 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:42,595 - INFO - Splitting PDF 49a18cb4_EHU9I6PL4HKG590OCG8Y into 4 pages
2025-09-24 18:36:42,618 - INFO - Split PDF into 4 pages
2025-09-24 18:36:42,618 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:42,618 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 18:36:42,996 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TO25VR90OYONDFSZE4OI.pdf -> s3://document-extraction-logistically/temp/deaee8f5_TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:42,996 - INFO - 🔍 [18:36:42] Starting classification: TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:42,996 - INFO - ⬆️ [18:36:42] Uploading: TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:42,997 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:43,008 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:43,011 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/deaee8f5_TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:43,012 - INFO - Processing PDF from S3...
2025-09-24 18:36:43,012 - INFO - Downloading PDF from S3 to /tmp/tmphi_48r6d/deaee8f5_TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:43,391 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 18:36:43,391 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:43,396 - INFO - Splitting PDF 2eb1a12e_FIXBJNBFB5JY1H27XN70 into 1 pages
2025-09-24 18:36:43,544 - INFO - Split PDF into 1 pages
2025-09-24 18:36:43,544 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:43,544 - INFO - Expected pages: [1]
2025-09-24 18:36:43,557 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:36:43,557 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:43,557 - INFO - Splitting PDF d19f2c5b_MMITIPOIZN33LXTLSS38 into 1 pages
2025-09-24 18:36:43,560 - INFO - Split PDF into 1 pages
2025-09-24 18:36:43,560 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:43,560 - INFO - Expected pages: [1]
2025-09-24 18:36:43,566 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TVSLUSRVSJE6NIEE9QDG.pdf -> s3://document-extraction-logistically/temp/ec95439c_TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:43,566 - INFO - 🔍 [18:36:43] Starting classification: TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:43,567 - INFO - ⬆️ [18:36:43] Uploading: ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:36:43,572 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:43,582 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:43,584 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ec95439c_TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:43,585 - INFO - Processing PDF from S3...
2025-09-24 18:36:43,585 - INFO - Downloading PDF from S3 to /tmp/tmpvjkkf_v8/ec95439c_TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:44,150 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/ZZNEF9U1F99S8XPET2O2.pdf -> s3://document-extraction-logistically/temp/becbbddc_ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:36:44,150 - INFO - 🔍 [18:36:44] Starting classification: ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:36:44,151 - INFO - Initializing TextractProcessor...
2025-09-24 18:36:44,163 - INFO - Initializing BedrockProcessor...
2025-09-24 18:36:44,166 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/becbbddc_ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:36:44,166 - INFO - Processing PDF from S3...
2025-09-24 18:36:44,166 - INFO - Downloading PDF from S3 to /tmp/tmpkxj_3c85/becbbddc_ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:36:44,452 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:36:44,453 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:44,454 - INFO - Splitting PDF b5ce3307_RYX8J947D5KTZDL0KST2 into 1 pages
2025-09-24 18:36:44,466 - INFO - Split PDF into 1 pages
2025-09-24 18:36:44,466 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:44,466 - INFO - Expected pages: [1]
2025-09-24 18:36:44,745 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:36:44,746 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:44,747 - INFO - Splitting PDF ec95439c_TVSLUSRVSJE6NIEE9QDG into 3 pages
2025-09-24 18:36:44,750 - INFO - Split PDF into 3 pages
2025-09-24 18:36:44,750 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:44,750 - INFO - Expected pages: [1, 2, 3]
2025-09-24 18:36:44,798 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:36:44,798 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:44,800 - INFO - Splitting PDF deaee8f5_TO25VR90OYONDFSZE4OI into 1 pages
2025-09-24 18:36:44,801 - INFO - Split PDF into 1 pages
2025-09-24 18:36:44,801 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:44,801 - INFO - Expected pages: [1]
2025-09-24 18:36:46,241 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 18:36:46,241 - INFO - Splitting PDF into individual pages...
2025-09-24 18:36:46,249 - INFO - Splitting PDF becbbddc_ZZNEF9U1F99S8XPET2O2 into 1 pages
2025-09-24 18:36:46,256 - INFO - Split PDF into 1 pages
2025-09-24 18:36:46,256 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:36:46,256 - INFO - Expected pages: [1]
2025-09-24 18:36:46,723 - INFO - Page 3: Extracted 554 characters, 23 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_003.pdf
2025-09-24 18:36:46,723 - INFO - Successfully processed page 3
2025-09-24 18:36:46,765 - INFO - Page 6: Extracted 963 characters, 51 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_006.pdf
2025-09-24 18:36:46,766 - INFO - Successfully processed page 6
2025-09-24 18:36:46,904 - INFO - Page 1: Extracted 2065 characters, 116 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_001.pdf
2025-09-24 18:36:46,905 - INFO - Successfully processed page 1
2025-09-24 18:36:47,016 - INFO - Page 4: Extracted 1159 characters, 48 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_004.pdf
2025-09-24 18:36:47,016 - INFO - Successfully processed page 4
2025-09-24 18:36:47,340 - INFO - Page 2: Extracted 3820 characters, 261 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_002.pdf
2025-09-24 18:36:47,341 - INFO - Successfully processed page 2
2025-09-24 18:36:48,142 - INFO - Page 2: Extracted 1200 characters, 52 lines from 49a18cb4_EHU9I6PL4HKG590OCG8Y_cacbf557_page_002.pdf
2025-09-24 18:36:48,142 - INFO - Successfully processed page 2
2025-09-24 18:36:48,228 - INFO - Page 4: Extracted 2317 characters, 130 lines from 49a18cb4_EHU9I6PL4HKG590OCG8Y_cacbf557_page_004.pdf
2025-09-24 18:36:48,229 - INFO - Successfully processed page 4
2025-09-24 18:36:48,369 - INFO - Page 5: Extracted 3556 characters, 147 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_005.pdf
2025-09-24 18:36:48,369 - INFO - Successfully processed page 5
2025-09-24 18:36:49,139 - INFO - Page 3: Extracted 5123 characters, 359 lines from 49a18cb4_EHU9I6PL4HKG590OCG8Y_cacbf557_page_003.pdf
2025-09-24 18:36:49,139 - INFO - Successfully processed page 3
2025-09-24 18:36:49,171 - INFO - Page 1: Extracted 3048 characters, 229 lines from 49a18cb4_EHU9I6PL4HKG590OCG8Y_cacbf557_page_001.pdf
2025-09-24 18:36:49,171 - INFO - Successfully processed page 1
2025-09-24 18:36:49,171 - INFO - Combined 4 pages into final text
2025-09-24 18:36:49,172 - INFO - Text validation for 49a18cb4_EHU9I6PL4HKG590OCG8Y: 11762 characters, 4 pages
2025-09-24 18:36:49,172 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:49,172 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:49,383 - INFO - Page 1: Extracted 3302 characters, 151 lines from d19f2c5b_MMITIPOIZN33LXTLSS38_fcb18164_page_001.pdf
2025-09-24 18:36:49,384 - INFO - Successfully processed page 1
2025-09-24 18:36:49,384 - INFO - Combined 1 pages into final text
2025-09-24 18:36:49,384 - INFO - Text validation for d19f2c5b_MMITIPOIZN33LXTLSS38: 3319 characters, 1 pages
2025-09-24 18:36:49,384 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:49,384 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:49,538 - INFO - Page 2: Extracted 2188 characters, 171 lines from ec95439c_TVSLUSRVSJE6NIEE9QDG_89c6049e_page_002.pdf
2025-09-24 18:36:49,538 - INFO - Successfully processed page 2
2025-09-24 18:36:49,571 - INFO - Page 1: Extracted 1843 characters, 98 lines from 2eb1a12e_FIXBJNBFB5JY1H27XN70_04a72d90_page_001.pdf
2025-09-24 18:36:49,571 - INFO - Successfully processed page 1
2025-09-24 18:36:49,571 - INFO - Combined 1 pages into final text
2025-09-24 18:36:49,571 - INFO - Text validation for 2eb1a12e_FIXBJNBFB5JY1H27XN70: 1860 characters, 1 pages
2025-09-24 18:36:49,572 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:49,572 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:49,650 - INFO - Page 3: Extracted 490 characters, 20 lines from ec95439c_TVSLUSRVSJE6NIEE9QDG_89c6049e_page_003.pdf
2025-09-24 18:36:49,650 - INFO - Successfully processed page 3
2025-09-24 18:36:49,675 - INFO - Page 7: Extracted 46 characters, 3 lines from 74c4d607_CY588RKQ3XR0K6IZ4AIS_15b751d7_page_007.pdf
2025-09-24 18:36:49,675 - INFO - Successfully processed page 7
2025-09-24 18:36:49,676 - INFO - Combined 7 pages into final text
2025-09-24 18:36:49,677 - INFO - Text validation for 74c4d607_CY588RKQ3XR0K6IZ4AIS: 12294 characters, 7 pages
2025-09-24 18:36:49,678 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:49,679 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:50,212 - INFO - Page 1: Extracted 1504 characters, 94 lines from b5ce3307_RYX8J947D5KTZDL0KST2_a0627488_page_001.pdf
2025-09-24 18:36:50,212 - INFO - Successfully processed page 1
2025-09-24 18:36:50,212 - INFO - Combined 1 pages into final text
2025-09-24 18:36:50,213 - INFO - Text validation for b5ce3307_RYX8J947D5KTZDL0KST2: 1521 characters, 1 pages
2025-09-24 18:36:50,213 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:50,213 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:50,599 - INFO - Page 1: Extracted 2406 characters, 142 lines from ec95439c_TVSLUSRVSJE6NIEE9QDG_89c6049e_page_001.pdf
2025-09-24 18:36:50,599 - INFO - Successfully processed page 1
2025-09-24 18:36:50,599 - INFO - Combined 3 pages into final text
2025-09-24 18:36:50,599 - INFO - Text validation for ec95439c_TVSLUSRVSJE6NIEE9QDG: 5139 characters, 3 pages
2025-09-24 18:36:50,599 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:50,599 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:50,651 - INFO - Page 1: Extracted 3108 characters, 200 lines from deaee8f5_TO25VR90OYONDFSZE4OI_f6cee154_page_001.pdf
2025-09-24 18:36:50,652 - INFO - Successfully processed page 1
2025-09-24 18:36:50,652 - INFO - Combined 1 pages into final text
2025-09-24 18:36:50,652 - INFO - Text validation for deaee8f5_TO25VR90OYONDFSZE4OI: 3125 characters, 1 pages
2025-09-24 18:36:50,652 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:50,652 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:51,668 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '392c4e7f-052e-4dca-9389-f5342f95fd97', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:51 GMT', 'content-type': 'application/json', 'content-length': '730', 'connection': 'keep-alive', 'x-amzn-requestid': '392c4e7f-052e-4dca-9389-f5342f95fd97'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1 content. It\'s a SHIPPER\'S LETTER OF INSTRUCTIONS (SLI). This is a customs documentation for export. This falls under customs_doc category? The definition says "Customs Document, Certificate of Origin". The SLI is a shipper instruction for export; it\'s a customs related document. So doc_type: customs_doc. No other type matches. So produce output via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3445, 'outputTokens': 109, 'totalTokens': 3554}, 'metrics': {'latencyMs': 1035}}
2025-09-24 18:36:51,669 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d19f2c5b_MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:51,720 - INFO - 

MMITIPOIZN33LXTLSS38.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 18:36:51,720 - INFO - 

✓ Saved result: output/run1_MMITIPOIZN33LXTLSS38.json
2025-09-24 18:36:51,842 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '19c69a64-62c2-45c7-9646-0ca7153bba52', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:51 GMT', 'content-type': 'application/json', 'content-length': '777', 'connection': 'keep-alive', 'x-amzn-requestid': '19c69a64-62c2-45c7-9646-0ca7153bba52'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page with "CERTIFICATION OF ORIGIN" USMCA. This is a customs document, specifically a certificate of origin. The task: classify each page. The page has keywords like "CERTIFICATE OF ORIGIN" and "USMCA". According to enum: customs_doc — Customs Document, Certificate of Origin. So doc_type is customs_doc. Must output via tool call classify_logistics_doc_type with documents array containing object {page_no:1, doc_type:"customs_doc"}.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3123, 'outputTokens': 122, 'totalTokens': 3245}, 'metrics': {'latencyMs': 1013}}
2025-09-24 18:36:51,843 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2eb1a12e_FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:52,040 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4933f56f-698f-4ae5-825a-d8d461883c04', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:51 GMT', 'content-type': 'application/json', 'content-length': '1308', 'connection': 'keep-alive', 'x-amzn-requestid': '4933f56f-698f-4ae5-825a-d8d461883c04'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Let\'s look at each page.\n\nPage1: Contains "COMMERCIAL INVOICE / CERTIFICATE OF ORIGIN" header. Also has many HS codes, incoterms, etc. So it\'s a commercial invoice. So doc_type: comm_invoice.\n\nPage2: Continuation of commercial invoice. Doesn’t have header but page 2 shows signature and "THIS INVOICE IS TRUE AND CORRECT". It\'s likely part of same commercial invoice. So same doc_type comm_invoice.\n\nPage3: Starts with "Work" and "CCC EL SAUZ Packing List". So it\'s a packing list. So doc_type: pack_list.\n\nPage4: This page seems to continue the packing list, with more items. So same doc_type: pack_list.\n\nThus pages 1 & 2: comm_invoice. Pages 3 & 4: pack_list.\n\nReturn array of objects each with page_no and doc_type.\n\nWe must use the tool classify_logistics_doc_type.\n\nLet\'s produce JSON array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"pack_list"},{"page_no":4,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 7464, 'outputTokens': 255, 'totalTokens': 7719}, 'metrics': {'latencyMs': 1635}}
2025-09-24 18:36:52,041 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/49a18cb4_EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:52,795 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d19f2c5b_MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:36:52,824 - INFO - 

FIXBJNBFB5JY1H27XN70.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 18:36:52,824 - INFO - 

✓ Saved result: output/run1_FIXBJNBFB5JY1H27XN70.json
2025-09-24 18:36:53,039 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '722b4751-42bb-43bd-9379-971b6e95aa89', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:52 GMT', 'content-type': 'application/json', 'content-length': '1030', 'connection': 'keep-alive', 'x-amzn-requestid': '722b4751-42bb-43bd-9379-971b6e95aa89'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We have cargo release ACE status notification, not typical categories. It seems like a customs entry or release notice. Among provided types: customs_doc? The description: Customs Document, Certificate of Origin. This notification is a customs entry release message. It contains entry #, Bill of Lading #, etc. The most appropriate is customs_doc. It\'s not an invoice, BOL, POD, etc. So classify as customs_doc.\n\nCheck if it\'s continuation? No. So output page 1.\n\nWe must call tool classify_logistics_doc_type with documents array: {page_no:1, doc_type:"customs_doc"}.\n\n'}}}, {'toolUse': {'toolUseId': 'tooluse_vrN3t88RQA6O-7PDjLE78w', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2977, 'outputTokens': 156, 'totalTokens': 3133}, 'metrics': {'latencyMs': 1600}}
2025-09-24 18:36:53,040 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b5ce3307_RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:53,144 - INFO - Page 1: Extracted 3318 characters, 148 lines from becbbddc_ZZNEF9U1F99S8XPET2O2_e9ea4780_page_001.pdf
2025-09-24 18:36:53,144 - INFO - Successfully processed page 1
2025-09-24 18:36:53,145 - INFO - Combined 1 pages into final text
2025-09-24 18:36:53,145 - INFO - Text validation for becbbddc_ZZNEF9U1F99S8XPET2O2: 3335 characters, 1 pages
2025-09-24 18:36:53,145 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:36:53,145 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:36:53,149 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2eb1a12e_FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:36:53,259 - INFO - 

EHU9I6PL4HKG590OCG8Y.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "pack_list"
        },
        {
            "page_no": 4,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:36:53,259 - INFO - 

✓ Saved result: output/run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:36:53,556 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/49a18cb4_EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:36:53,571 - INFO - 

RYX8J947D5KTZDL0KST2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 18:36:53,571 - INFO - 

✓ Saved result: output/run1_RYX8J947D5KTZDL0KST2.json
2025-09-24 18:36:53,615 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3aa35902-0ad1-43cd-8199-979e5eaa8d6b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:53 GMT', 'content-type': 'application/json', 'content-length': '1387', 'connection': 'keep-alive', 'x-amzn-requestid': '3aa35902-0ad1-43cd-8199-979e5eaa8d6b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content. The content appears to be a "CERTIFICATION OF ORIGIN" document, which is a customs document. It includes HS codes, origin info, etc. That matches customs_doc. There\'s no clear header like "CERTIFICATE OF ORIGIN" but we see "CERTIFICATION OF ORIGIN" in the text. That aligns with customs_doc category. The doc_type should be "customs_doc".\n\nCheck if it\'s continuation: It says "Page 3/3" and earlier "BOL Page 2" but that line seems at top: "BOL Page 2". That may indicate a Bill of Lading previously. But the content is mostly certification of origin. The page might be part of a combined carrier documents? The page shows "BOL Page 2" but the content is a certification of origin. It also shows "Page 3/3" at bottom. So the page might be part of a multi-page certificate of origin. The doc type is customs_doc. There\'s no indication of continuation. It\'s page 3 of 3, so it\'s the last page. But we still classify as customs_doc.\n\nThus output: array with one object: page_no 1, doc_type "customs_doc". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3712, 'outputTokens': 276, 'totalTokens': 3988}, 'metrics': {'latencyMs': 1657}}
2025-09-24 18:36:53,616 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/deaee8f5_TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:53,717 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b10ce918-e334-49da-ba70-0dd46abb766e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:53 GMT', 'content-type': 'application/json', 'content-length': '1122', 'connection': 'keep-alive', 'x-amzn-requestid': 'b10ce918-e334-49da-ba70-0dd46abb766e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 3 pages from a customs entry form (CBP Form 3461). This looks like a customs entry document. It\'s basically a customs declaration with BOL numbers, etc. Should classify as customs_doc? The document type is "Customs Document" or "Customs Declaration" maybe "customs_doc". The page contains line items, BOL info. So likely "customs_doc".\n\nCheck definition: customs_doc — General customs paperwork (declarations, certificates, permits). Yes.\n\nThus page1, page2, page3 all are same document. Should output 3 objects, each with doc_type: "customs_doc". Use tool call.\n\nWe need to output JSON via tool call. Format: {documents: [{page_no: 1, doc_type: "customs_doc"}, ...]}. Let\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"customs_doc"},{"page_no":3,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4187, 'outputTokens': 214, 'totalTokens': 4401}, 'metrics': {'latencyMs': 1884}}
2025-09-24 18:36:53,718 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ec95439c_TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:53,885 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b5ce3307_RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:36:53,925 - INFO - 

TO25VR90OYONDFSZE4OI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 18:36:53,925 - INFO - 

✓ Saved result: output/run1_TO25VR90OYONDFSZE4OI.json
2025-09-24 18:36:54,229 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/deaee8f5_TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:36:54,292 - INFO - 

TVSLUSRVSJE6NIEE9QDG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 3,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 18:36:54,292 - INFO - 

✓ Saved result: output/run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:36:54,575 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ec95439c_TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:36:55,458 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '07654868-eaa0-486c-938b-1e64a7edc3b9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:06:55 GMT', 'content-type': 'application/json', 'content-length': '648', 'connection': 'keep-alive', 'x-amzn-requestid': '07654868-eaa0-486c-938b-1e64a7edc3b9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content is clearly a customs invoice. It\'s a commercial invoice for customs. It contains HS codes, country of origin, etc. So it\'s a comm_invoice. The doc type should be "comm_invoice". There\'s no indication of continuation. So output one object: page_no 1, doc_type "comm_invoice". Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3679, 'outputTokens': 91, 'totalTokens': 3770}, 'metrics': {'latencyMs': 1029}}
2025-09-24 18:36:55,459 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/becbbddc_ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:36:55,508 - INFO - 

ZZNEF9U1F99S8XPET2O2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 18:36:55,508 - INFO - 

✓ Saved result: output/run1_ZZNEF9U1F99S8XPET2O2.json
2025-09-24 18:36:55,841 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/becbbddc_ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:37:00,387 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'edec4a42-c732-4a86-8510-abc2f871fdd0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:07:00 GMT', 'content-type': 'application/json', 'content-length': '9458', 'connection': 'keep-alive', 'x-amzn-requestid': 'edec4a42-c732-4a86-8510-abc2f871fdd0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page into the doc_type enum.\n\nWe have 7 pages. Let\'s analyze each.\n\nPage1: It\'s a "Commercial Accounting Declaration" (CAD). This looks like a customs declaration form. Keywords: "Commercial Accounting Declaration", "Declaration", "Total Duties & Taxes", etc. This is a customs document. According to enums, "customs_doc" is for general customs paperwork. So doc_type should be customs_doc. There\'s no other category like "commercial accounting declaration". So assign customs_doc.\n\nPage2: This page has "Commercial Accounting Declaration" again but with invoice details, including "Invoice No.", "Invoice Value", "Purchase Order No.", etc. It seems to be a commercial invoice (international). It has HS codes, etc. Keywords: "Invoice No.", "Invoice Value", "HS Code", "Country of Origin", "Tariff Code", etc. So it\'s a commercial invoice. doc_type: comm_invoice.\n\nPage3: "SHIPMENT NOTIFICATION . LIVE FREIGHT" and "PLEASE NOTE - THIS IS A FAILED PARS". This looks like a shipment notification, maybe a tender or a notification? But not exactly. It says "TO: STRAITLINK GLOBAL LOGISTICS" and mentions "Port - 405". It\'s a notification for a shipment. Could be tender or shipment notification. There\'s no explicit "Load Tender" but "Shipment Notification". Might be a "tender_from_cust"? But tender_from_cust: "Customer\'s load tender or request to a carrier to transport a load." This looks like a notification from Speedy Transport to Straits. It might be a "tender_from_cust". But the header says "SHIPMENT NOTIFICATION . LIVE FREIGHT". Might be a "shipment notification" which is not exactly any defined type. It may be considered "other". But maybe "tender_from_cust" is close. But we should check if "tender_from_cust" requires "Load Tender" etc. The page has "PLEASE NOTE - THIS IS A FAILED PARS\\nIT MUST BE RESUBMITTED UNDER PORT 405". Might be a notification sent. Not a formal tender. I\'d classify as "other" or maybe "tender_from_cust". But we can consider "tender_from_cust" only if has "Load Tender" or "Tender from". It doesn\'t. So "other".\n\nPage4: "CARGO CONTROL DOCUMENT POST AUDIT". This looks like a customs cargo control document, maybe a "customs_doc" or "cargo control document". It\'s a posting audit. Might be a customs document. There\'s no invoice. So classify as customs_doc.\n\nPage5: This is a "BILL OF LADING". It has BOL #, shipper, consignee, etc. So doc_type: bol.\n\nPage6: This has "Commercial Invoice" heading and includes invoice details, tax info. It\'s a commercial invoice. So doc_type: comm_invoice.\n\nPage7: "Canadian Customs Invoice Images" maybe an image of a customs invoice. So doc_type: comm_invoice? Or maybe it\'s just a screenshot of a customs invoice. It\'s likely a customs invoice image. The enum \'comm_invoice\' covers commercial invoices. So page7: comm_invoice.\n\nCheck continuation: Are any pages continuation of previous? Page3 says "Page 1 of 5". So page3 is part of a multi-page document. Page3 is page1 of 5. Page4 is page2 of 5? It doesn\'t show page number but maybe. Page5 says "PAGE 1 of 1". So it\'s separate. Page2 doesn\'t have page number but it\'s part of the same doc as page1? Page1 says "Commercial Accounting Declaration" and page2 also "Commercial Accounting Declaration". Page1 might be page 1 of 2? It doesn\'t show. But page2 has invoice details. The \'Commercial Accounting Declaration\' might be a customs declaration. Page1 may be the declaration page, page2 the invoice. They might be part of same customs doc? But page2 is a commercial invoice, separate. However, they both have "Commercial Accounting Declaration" header. Maybe it\'s the same document: a customs declaration form that includes invoice details? But page2 has separate "Commercial Accounting Declaration" heading again. It could be a separate page of the same form. So they both should be same doc_type: customs_doc or comm_invoice? Wait page2 includes HS codes and invoice value. That seems commercial invoice. But page1 is a declaration. They might be part of a customs form that includes invoice. Honestly, the classification should be based on the content. Page1 has no invoice, just declaration. It\'s customs_doc. Page2 has invoice detail, so comm_invoice. But are they part of same multi-page doc? The rule: If a page is continuation of previous page, assign same doc_type as previous page. But we need to detect continuation. Page1 says no page number but page2 may not. There\'s no "continued" text. But they both have same header. But no explicit continuation indicator. The rule says "Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: "continued", "continued on next page", "continued on next", etc., or if it indicates pagination like "page 2 of 3", or any other signal indicating continuation of the previous page/document." So we only consider if page indicates "Page 2 of 3" or similar. Page1 doesn\'t. Page2 doesn\'t. So we treat them separately. So page1 customs_doc, page2 comm_invoice.\n\nPage3: "Page 1 of 5" indicates continuation? But it\'s the start of a new doc, but page4 may be page 2 of 5. But page3 says "Page 1 of 5", page4 no page number. But page3 is a separate doc. Not a continuation of page2. So page3 other.\n\nPage4: No page indicator but likely continuation of page3. But we don\'t consider unless "Page 2 of 5" or similar. So page4 separate doc. But since it\'s part of same shipment notification, but we don\'t assign same doc_type automatically. But maybe we should classify page4 as same doc_type as page3? The rule says "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." But we only know it\'s part of a multi-page single document if there\'s pagination or continuation. Page3 says "Page 1 of 5". Page4 likely is page 2 of 5 but not indicated. But we might infer it\'s part of same doc. But the rule says we should detect continuation by text. Page4 does not contain "continued" or pagination. But it might still be considered continuation because page3 says Page 1 of 5. But the rule says "Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following... or if it indicates pagination like "page 2 of 3", or any other signal indicating continuation of the previous page/document." It doesn\'t say we should assume if previous page says page 1 of 5, that the next is page 2 of 5. But maybe we should. The phrasing "if it indicates pagination like \'page 2 of 3\'" is an example. There is no example of "Page 1 of 5" being considered as a continuation of the next page. But "page 1 of 5" indicates continuation, but we need the next page to have "page 2 of 5" or similar. But we don\'t have that. So maybe we treat page4 as separate doc. But maybe we treat it as same doc_type as page3 because the content looks like a cargo control document. But it\'s a separate doc type. The page4 is a cargo control document. That may be a customs document. So doc_type customs_doc.\n\nBut page3 is not a customs doc. So maybe page4 is different. But it might be a cargo control document which is a customs doc. So we classify page4 as customs_doc.\n\nPage5 is BOL. Page6 is comm_invoice. Page7 is comm_invoice.\n\nNow check page3: "SHIPMENT NOTIFICATION . LIVE FREIGHT" and "FAILED PARS". This is a shipment notification. Not a defined type. So other.\n\nThus mapping:\n\nPage1: customs_doc\nPage2: comm_invoice\nPage3: other\nPage4: customs_doc\nPage5: bol\nPage6: comm_invoice\nPage7: comm_invoice\n\nCheck if any page is part of multi-page same doc: Page3 says Page 1 of 5. Page4 likely part of the same doc. But page4 has no header. But does page4 start with "CARGO CONTROL DOCUMENT POST AUDIT"? Yes. That is a distinct document type. But if it\'s part of same doc, they\'d share same type (shipment notification). But page4 is cargo control doc, not a notification. So maybe it\'s a separate doc. But given the content, page4 is a cargo control document. So classify as customs_doc. But we need to see if any other doc_type matches cargo control. No. So customs_doc.\n\nDouble-check: customs_doc definition: General customs paperwork (declarations, certificates, permits). Cargo control documents are customs paperwork, so yes.\n\nNow check page2: It\'s a commercial invoice with HS codes, etc. So comm_invoice.\n\nNow page6: It has "Commercial Invoice" header. So comm_invoice.\n\nPage7: "Canadian Customs Invoice Images" probably a scan of a customs invoice. So comm_invoice.\n\nThus final.\n\nWe need to output JSON array of objects with page_no and doc_type. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"other"},{"page_no":4,"doc_type":"customs_doc"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"comm_invoice"},{"page_no":7,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 6311, 'outputTokens': 2121, 'totalTokens': 8432}, 'metrics': {'latencyMs': 9493}}
2025-09-24 18:37:00,388 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/74c4d607_CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,514 - INFO - 

CY588RKQ3XR0K6IZ4AIS.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        },
        {
            "page_no": 4,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 5,
            "doc_type": "bol"
        },
        {
            "page_no": 6,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 7,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 18:37:00,514 - INFO - 

✓ Saved result: output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,842 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/74c4d607_CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,846 - INFO - 
📊 Processing Summary:
2025-09-24 18:37:00,847 - INFO -    Total files: 8
2025-09-24 18:37:00,847 - INFO -    Successful: 8
2025-09-24 18:37:00,847 - INFO -    Failed: 0
2025-09-24 18:37:00,847 - INFO -    Duration: 24.74 seconds
2025-09-24 18:37:00,847 - INFO -    Output directory: output
2025-09-24 18:37:00,847 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:37:00,847 - INFO -    📄 CY588RKQ3XR0K6IZ4AIS.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"other"},{"page_no":4,"doc_type":"customs_doc"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"comm_invoice"},{"page_no":7,"doc_type":"comm_invoice"}]}
2025-09-24 18:37:00,847 - INFO -    📄 EHU9I6PL4HKG590OCG8Y.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"pack_list"},{"page_no":4,"doc_type":"pack_list"}]}
2025-09-24 18:37:00,847 - INFO -    📄 FIXBJNBFB5JY1H27XN70.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 18:37:00,847 - INFO -    📄 MMITIPOIZN33LXTLSS38.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 18:37:00,848 - INFO -    📄 RYX8J947D5KTZDL0KST2.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 18:37:00,848 - INFO -    📄 TO25VR90OYONDFSZE4OI.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 18:37:00,848 - INFO -    📄 TVSLUSRVSJE6NIEE9QDG.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"customs_doc"},{"page_no":3,"doc_type":"customs_doc"}]}
2025-09-24 18:37:00,848 - INFO -    📄 ZZNEF9U1F99S8XPET2O2.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 18:37:00,848 - INFO - 
============================================================================================================================================
2025-09-24 18:37:00,848 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:37:00,848 - INFO - ============================================================================================================================================
2025-09-24 18:37:00,848 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:37:00,849 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:37:00,849 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           1      customs_doc                              run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,849 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,849 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,849 - INFO - 
2025-09-24 18:37:00,849 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           2      comm_invoice                             run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,849 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,849 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,849 - INFO - 
2025-09-24 18:37:00,849 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           3      other                                    run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,849 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,849 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,849 - INFO - 
2025-09-24 18:37:00,850 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           4      customs_doc                              run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,850 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,850 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,850 - INFO - 
2025-09-24 18:37:00,850 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           5      bol                                      run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,850 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,850 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,850 - INFO - 
2025-09-24 18:37:00,850 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           6      comm_invoice                             run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,850 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,850 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,850 - INFO - 
2025-09-24 18:37:00,850 - INFO - CY588RKQ3XR0K6IZ4AIS.pdf                           7      comm_invoice                             run1_CY588RKQ3XR0K6IZ4AIS.json                                                  
2025-09-24 18:37:00,850 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf
2025-09-24 18:37:00,850 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,850 - INFO - 
2025-09-24 18:37:00,850 - INFO - EHU9I6PL4HKG590OCG8Y.pdf                           1      comm_invoice                             run1_EHU9I6PL4HKG590OCG8Y.json                                                  
2025-09-24 18:37:00,850 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:37:00,850 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,850 - INFO - 
2025-09-24 18:37:00,851 - INFO - EHU9I6PL4HKG590OCG8Y.pdf                           2      comm_invoice                             run1_EHU9I6PL4HKG590OCG8Y.json                                                  
2025-09-24 18:37:00,851 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:37:00,851 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,851 - INFO - 
2025-09-24 18:37:00,851 - INFO - EHU9I6PL4HKG590OCG8Y.pdf                           3      pack_list                                run1_EHU9I6PL4HKG590OCG8Y.json                                                  
2025-09-24 18:37:00,851 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:37:00,851 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,851 - INFO - 
2025-09-24 18:37:00,851 - INFO - EHU9I6PL4HKG590OCG8Y.pdf                           4      pack_list                                run1_EHU9I6PL4HKG590OCG8Y.json                                                  
2025-09-24 18:37:00,851 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/EHU9I6PL4HKG590OCG8Y.pdf
2025-09-24 18:37:00,851 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,851 - INFO - 
2025-09-24 18:37:00,851 - INFO - FIXBJNBFB5JY1H27XN70.pdf                           1      customs_doc                              run1_FIXBJNBFB5JY1H27XN70.json                                                  
2025-09-24 18:37:00,851 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/FIXBJNBFB5JY1H27XN70.pdf
2025-09-24 18:37:00,851 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FIXBJNBFB5JY1H27XN70.json
2025-09-24 18:37:00,851 - INFO - 
2025-09-24 18:37:00,851 - INFO - MMITIPOIZN33LXTLSS38.pdf                           1      customs_doc                              run1_MMITIPOIZN33LXTLSS38.json                                                  
2025-09-24 18:37:00,851 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/MMITIPOIZN33LXTLSS38.pdf
2025-09-24 18:37:00,851 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MMITIPOIZN33LXTLSS38.json
2025-09-24 18:37:00,851 - INFO - 
2025-09-24 18:37:00,852 - INFO - RYX8J947D5KTZDL0KST2.pdf                           1      customs_doc                              run1_RYX8J947D5KTZDL0KST2.json                                                  
2025-09-24 18:37:00,852 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/RYX8J947D5KTZDL0KST2.pdf
2025-09-24 18:37:00,852 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RYX8J947D5KTZDL0KST2.json
2025-09-24 18:37:00,852 - INFO - 
2025-09-24 18:37:00,852 - INFO - TO25VR90OYONDFSZE4OI.pdf                           1      customs_doc                              run1_TO25VR90OYONDFSZE4OI.json                                                  
2025-09-24 18:37:00,852 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TO25VR90OYONDFSZE4OI.pdf
2025-09-24 18:37:00,852 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TO25VR90OYONDFSZE4OI.json
2025-09-24 18:37:00,852 - INFO - 
2025-09-24 18:37:00,852 - INFO - TVSLUSRVSJE6NIEE9QDG.pdf                           1      customs_doc                              run1_TVSLUSRVSJE6NIEE9QDG.json                                                  
2025-09-24 18:37:00,852 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:37:00,852 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:37:00,852 - INFO - 
2025-09-24 18:37:00,852 - INFO - TVSLUSRVSJE6NIEE9QDG.pdf                           2      customs_doc                              run1_TVSLUSRVSJE6NIEE9QDG.json                                                  
2025-09-24 18:37:00,852 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:37:00,852 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:37:00,852 - INFO - 
2025-09-24 18:37:00,852 - INFO - TVSLUSRVSJE6NIEE9QDG.pdf                           3      customs_doc                              run1_TVSLUSRVSJE6NIEE9QDG.json                                                  
2025-09-24 18:37:00,852 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TVSLUSRVSJE6NIEE9QDG.pdf
2025-09-24 18:37:00,852 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:37:00,852 - INFO - 
2025-09-24 18:37:00,852 - INFO - ZZNEF9U1F99S8XPET2O2.pdf                           1      comm_invoice                             run1_ZZNEF9U1F99S8XPET2O2.json                                                  
2025-09-24 18:37:00,853 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/ZZNEF9U1F99S8XPET2O2.pdf
2025-09-24 18:37:00,853 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZZNEF9U1F99S8XPET2O2.json
2025-09-24 18:37:00,853 - INFO - 
2025-09-24 18:37:00,853 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:37:00,853 - INFO - Total entries: 19
2025-09-24 18:37:00,853 - INFO - ============================================================================================================================================
2025-09-24 18:37:00,853 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:37:00,853 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:37:00,853 - INFO -   1. CY588RKQ3XR0K6IZ4AIS.pdf            Page 1   → customs_doc     | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   2. CY588RKQ3XR0K6IZ4AIS.pdf            Page 2   → comm_invoice    | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   3. CY588RKQ3XR0K6IZ4AIS.pdf            Page 3   → other           | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   4. CY588RKQ3XR0K6IZ4AIS.pdf            Page 4   → customs_doc     | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   5. CY588RKQ3XR0K6IZ4AIS.pdf            Page 5   → bol             | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   6. CY588RKQ3XR0K6IZ4AIS.pdf            Page 6   → comm_invoice    | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   7. CY588RKQ3XR0K6IZ4AIS.pdf            Page 7   → comm_invoice    | run1_CY588RKQ3XR0K6IZ4AIS.json
2025-09-24 18:37:00,853 - INFO -   8. EHU9I6PL4HKG590OCG8Y.pdf            Page 1   → comm_invoice    | run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,853 - INFO -   9. EHU9I6PL4HKG590OCG8Y.pdf            Page 2   → comm_invoice    | run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,853 - INFO -  10. EHU9I6PL4HKG590OCG8Y.pdf            Page 3   → pack_list       | run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,854 - INFO -  11. EHU9I6PL4HKG590OCG8Y.pdf            Page 4   → pack_list       | run1_EHU9I6PL4HKG590OCG8Y.json
2025-09-24 18:37:00,854 - INFO -  12. FIXBJNBFB5JY1H27XN70.pdf            Page 1   → customs_doc     | run1_FIXBJNBFB5JY1H27XN70.json
2025-09-24 18:37:00,854 - INFO -  13. MMITIPOIZN33LXTLSS38.pdf            Page 1   → customs_doc     | run1_MMITIPOIZN33LXTLSS38.json
2025-09-24 18:37:00,854 - INFO -  14. RYX8J947D5KTZDL0KST2.pdf            Page 1   → customs_doc     | run1_RYX8J947D5KTZDL0KST2.json
2025-09-24 18:37:00,854 - INFO -  15. TO25VR90OYONDFSZE4OI.pdf            Page 1   → customs_doc     | run1_TO25VR90OYONDFSZE4OI.json
2025-09-24 18:37:00,854 - INFO -  16. TVSLUSRVSJE6NIEE9QDG.pdf            Page 1   → customs_doc     | run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:37:00,854 - INFO -  17. TVSLUSRVSJE6NIEE9QDG.pdf            Page 2   → customs_doc     | run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:37:00,854 - INFO -  18. TVSLUSRVSJE6NIEE9QDG.pdf            Page 3   → customs_doc     | run1_TVSLUSRVSJE6NIEE9QDG.json
2025-09-24 18:37:00,854 - INFO -  19. ZZNEF9U1F99S8XPET2O2.pdf            Page 1   → comm_invoice    | run1_ZZNEF9U1F99S8XPET2O2.json
2025-09-24 18:37:00,854 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:37:00,854 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 24.740109, 'processed_files': [{'filename': 'CY588RKQ3XR0K6IZ4AIS.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'other'}, {'page_no': 4, 'doc_type': 'customs_doc'}, {'page_no': 5, 'doc_type': 'bol'}, {'page_no': 6, 'doc_type': 'comm_invoice'}, {'page_no': 7, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/CY588RKQ3XR0K6IZ4AIS.pdf'}, {'filename': 'EHU9I6PL4HKG590OCG8Y.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'pack_list'}, {'page_no': 4, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/EHU9I6PL4HKG590OCG8Y.pdf'}, {'filename': 'FIXBJNBFB5JY1H27XN70.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/FIXBJNBFB5JY1H27XN70.pdf'}, {'filename': 'MMITIPOIZN33LXTLSS38.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/MMITIPOIZN33LXTLSS38.pdf'}, {'filename': 'RYX8J947D5KTZDL0KST2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/RYX8J947D5KTZDL0KST2.pdf'}, {'filename': 'TO25VR90OYONDFSZE4OI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TO25VR90OYONDFSZE4OI.pdf'}, {'filename': 'TVSLUSRVSJE6NIEE9QDG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'customs_doc'}, {'page_no': 3, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/TVSLUSRVSJE6NIEE9QDG.pdf'}, {'filename': 'ZZNEF9U1F99S8XPET2O2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/customs_doc/ZZNEF9U1F99S8XPET2O2.pdf'}]}
