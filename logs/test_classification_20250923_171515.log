2025-09-23 17:15:15,548 - INFO - Logging initialized. Log file: logs/test_classification_20250923_171515.log
2025-09-23 17:15:15,548 - INFO - 📁 Found 1 files to process
2025-09-23 17:15:15,549 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 17:15:15,549 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 17:15:15,549 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 17:15:15,549 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 17:15:15,549 - INFO - ⬆️ [17:15:15] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:21,799 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/1ca2f8de_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:21,799 - INFO - 🔍 [17:15:21] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:21,800 - INFO - Initializing TextractProcessor...
2025-09-23 17:15:21,817 - INFO - Initializing BedrockProcessor...
2025-09-23 17:15:21,825 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1ca2f8de_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:21,826 - INFO - Processing PDF from S3...
2025-09-23 17:15:21,827 - INFO - Downloading PDF from S3 to /tmp/tmpzsdxwhuh/1ca2f8de_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:24,271 - INFO - Downloaded PDF size: 1.1 MB
2025-09-23 17:15:24,271 - INFO - Splitting PDF into individual pages...
2025-09-23 17:15:24,275 - INFO - Splitting PDF 1ca2f8de_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 17:15:24,284 - INFO - Split PDF into 7 pages
2025-09-23 17:15:24,285 - INFO - Processing pages with Textract in parallel...
2025-09-23 17:15:24,285 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 17:15:29,351 - INFO - Page 3: Extracted 929 characters, 64 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_003.pdf
2025-09-23 17:15:29,352 - INFO - Successfully processed page 3
2025-09-23 17:15:29,431 - INFO - Page 4: Extracted 1151 characters, 63 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_004.pdf
2025-09-23 17:15:29,431 - INFO - Successfully processed page 4
2025-09-23 17:15:29,452 - INFO - Page 5: Extracted 2022 characters, 27 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_005.pdf
2025-09-23 17:15:29,452 - INFO - Successfully processed page 5
2025-09-23 17:15:29,722 - INFO - Page 2: Extracted 1174 characters, 29 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_002.pdf
2025-09-23 17:15:29,722 - INFO - Successfully processed page 2
2025-09-23 17:15:30,305 - INFO - Page 1: Extracted 1168 characters, 45 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_001.pdf
2025-09-23 17:15:30,306 - INFO - Successfully processed page 1
2025-09-23 17:15:31,155 - INFO - Page 6: Extracted 4159 characters, 115 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_006.pdf
2025-09-23 17:15:31,155 - INFO - Successfully processed page 6
2025-09-23 17:15:33,010 - INFO - Page 7: Extracted 788 characters, 55 lines from 1ca2f8de_Purchase_Order_VRSD0E9045781_0e05cf35_page_007.pdf
2025-09-23 17:15:33,010 - INFO - Successfully processed page 7
2025-09-23 17:15:33,011 - INFO - Combined 7 pages into final text
2025-09-23 17:15:33,012 - INFO - Text validation for 1ca2f8de_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 17:15:33,012 - INFO - Analyzing document types with Bedrock...
2025-09-23 17:15:33,013 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 17:15:37,885 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-23 17:15:37,885 - ERROR - Processing failed for s3://document-extraction-logistically/temp/1ca2f8de_Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:15:37,886 - ERROR - ✗ Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:15:39,113 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1ca2f8de_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:39,114 - INFO - 
📊 Processing Summary:
2025-09-23 17:15:39,114 - INFO -    Total files: 1
2025-09-23 17:15:39,115 - INFO -    Successful: 0
2025-09-23 17:15:39,115 - INFO -    Failed: 1
2025-09-23 17:15:39,115 - INFO -    Duration: 23.57 seconds
2025-09-23 17:15:39,115 - INFO -    Output directory: output
2025-09-23 17:15:39,115 - ERROR - 
❌ Errors:
2025-09-23 17:15:39,115 - ERROR -    Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:15:39,115 - INFO - 
📋 No files were successfully processed.
2025-09-23 17:15:39,115 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 23.565409, 'processed_files': []}
