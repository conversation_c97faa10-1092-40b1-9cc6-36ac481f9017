{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9995843172073364, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 2.9406249879571078e-08}, {"X": 1.0, "Y": 0.9995570182800293}, {"X": 0.0, "Y": 0.9995843172073364}]}, "Id": "f3ce16f4-9063-4127-b160-eea9ce7dd4d9", "Relationships": [{"Type": "CHILD", "Ids": ["2ba77645-0fba-4d96-8843-4b350beeddc9", "288fab89-abd9-479f-bf66-7397abbf444f", "bba78d11-aa5f-4208-af2d-ded9dc5903c3", "b18ff9c5-192c-400f-82bd-f43a2879cb45", "d2dfa89a-c857-47e7-8e41-a9280d2b4fd7", "c177df8e-de5c-410e-8701-4538da695273", "f809c54c-3bc4-489b-bf91-923f3e66fb32", "a3d9979d-6cee-47db-b9be-152d9b54201b", "86304c36-9bc7-427b-b1e8-d187615799f8", "20cc8eda-0ba9-4914-91ba-1c1c94d4e173", "9e17bc35-56a9-4271-854a-5900a613fd6c", "ab35b917-f588-4402-b828-139aadc5a3cf", "0b047bef-e46e-4d16-b494-d77022daf8df", "44276620-d655-4966-a53d-8297588902cb", "f4433712-ba93-4dbb-98b9-d5ff6433135b", "0a184042-6d75-49f7-b1ef-b9af1f58b3a7", "0c3105c8-3a35-4c24-a42a-9f1986e89523", "97852e77-3dd7-4e59-8c07-96ab7438eee5", "8d4d8966-56fe-47ad-a6b8-05fec61b4963", "4d87e731-553d-4a4e-a6ba-acef2a8ee25e", "9b960789-354a-4737-a73c-edb96df44b65", "4dda06aa-ac77-4c63-a91b-ef7bdb7365cc", "902c575c-13e4-4c1b-94d2-d08470fbcc7f", "96ed0e0d-eee7-48ce-b22b-b51fc34f6ac7", "66ddfe56-4ed6-471b-9ee8-40bce9af4a23", "1a3d7773-8722-4552-94b3-d032faa7a372", "cf9821c7-eaf0-424b-9abb-c65233f41a15", "9a53f8c2-b17f-4329-834c-ff7164bba199"]}]}, {"BlockType": "LINE", "Confidence": 99.93383026123047, "Text": "Gate In Interchange", "Geometry": {"BoundingBox": {"Width": 0.1634933054447174, "Height": 0.015546564012765884, "Left": 0.051388949155807495, "Top": 0.03538558632135391}, "Polygon": [{"X": 0.05138900876045227, "Y": 0.03538558632135391}, {"X": 0.2148822546005249, "Y": 0.03539394959807396}, {"X": 0.21488219499588013, "Y": 0.0509321503341198}, {"X": 0.051388949155807495, "Y": 0.050923995673656464}]}, "Id": "2ba77645-0fba-4d96-8843-4b350beeddc9", "Relationships": [{"Type": "CHILD", "Ids": ["cc06ab0e-e658-43b8-9894-d20986e06ff0", "3e24c872-96c9-40d3-9f8d-c1318594e393", "f9dbfce2-0814-4a0d-b8f4-0d2c2d706c2d"]}]}, {"BlockType": "LINE", "Confidence": 97.40194702148438, "Text": "Integrated Industries -", "Geometry": {"BoundingBox": {"Width": 0.18481653928756714, "Height": 0.01557258889079094, "Left": 0.029862839728593826, "Top": 0.06080309674143791}, "Polygon": [{"X": 0.02986290119588375, "Y": 0.06080309674143791}, {"X": 0.21467937529087067, "Y": 0.060812171548604965}, {"X": 0.2146793156862259, "Y": 0.07637568563222885}, {"X": 0.029862839728593826, "Y": 0.0763668492436409}]}, "Id": "288fab89-abd9-479f-bf66-7397abbf444f", "Relationships": [{"Type": "CHILD", "Ids": ["369315c4-1dfc-46a6-a950-1aeee3ff8567", "3eb9be84-d27d-4071-b80a-503969f6923e", "b3b5f776-f03c-4323-ac9c-df3b90004ce6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CHICAGO", "Geometry": {"BoundingBox": {"Width": 0.07598911970853806, "Height": 0.012348714284598827, "Left": 0.030034122988581657, "Top": 0.07931121438741684}, "Polygon": [{"X": 0.030034171417355537, "Y": 0.07931121438741684}, {"X": 0.10602324455976486, "Y": 0.07931482791900635}, {"X": 0.10602319240570068, "Y": 0.09165992587804794}, {"X": 0.030034122988581657, "Y": 0.0916563868522644}]}, "Id": "bba78d11-aa5f-4208-af2d-ded9dc5903c3", "Relationships": [{"Type": "CHILD", "Ids": ["3acad0d0-bc32-4197-8a89-ca3dec9ef216"]}]}, {"BlockType": "LINE", "Confidence": 99.53712463378906, "Text": "2750 W 35th Street", "Geometry": {"BoundingBox": {"Width": 0.15696991980075836, "Height": 0.012710228562355042, "Left": 0.03049110807478428, "Top": 0.09941843897104263}, "Polygon": [{"X": 0.03049115650355816, "Y": 0.09941843897104263}, {"X": 0.1874610334634781, "Y": 0.09942565113306046}, {"X": 0.1874609738588333, "Y": 0.11212866753339767}, {"X": 0.03049110807478428, "Y": 0.11212161928415298}]}, "Id": "b18ff9c5-192c-400f-82bd-f43a2879cb45", "Relationships": [{"Type": "CHILD", "Ids": ["e7ee6f01-05a2-4451-9810-84c117fa8cd8", "ef9610c1-9f9e-487d-ac7b-41df28ac33db", "1cd0c198-67a7-4f47-a8a0-499af9aed0c7", "57e63e33-3abf-4fb5-bd13-2f3b8ae49ccb"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Chicago", "Geometry": {"BoundingBox": {"Width": 0.06338164210319519, "Height": 0.015825361013412476, "Left": 0.029981615021824837, "Top": 0.11950840801000595}, "Polygon": [{"X": 0.02998167648911476, "Y": 0.11950840801000595}, {"X": 0.09336326271295547, "Y": 0.11951121687889099}, {"X": 0.0933631956577301, "Y": 0.13533377647399902}, {"X": 0.029981615021824837, "Y": 0.13533104956150055}]}, "Id": "d2dfa89a-c857-47e7-8e41-a9280d2b4fd7", "Relationships": [{"Type": "CHILD", "Ids": ["e6cb9290-6a1c-4c3a-8f60-b7dbcca14bc4"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "IL", "Geometry": {"BoundingBox": {"Width": 0.017048683017492294, "Height": 0.011979324743151665, "Left": 0.18004728853702545, "Top": 0.12023251503705978}, "Polygon": [{"X": 0.18004733324050903, "Y": 0.12023251503705978}, {"X": 0.19709596037864685, "Y": 0.12023327499628067}, {"X": 0.19709591567516327, "Y": 0.132211834192276}, {"X": 0.18004728853702545, "Y": 0.1322111040353775}]}, "Id": "c177df8e-de5c-410e-8701-4538da695273", "Relationships": [{"Type": "CHILD", "Ids": ["cd12deb7-a20f-4333-a1bf-2fac1f9e0659"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "TIR#: 733330", "Geometry": {"BoundingBox": {"Width": 0.11888027936220169, "Height": 0.012673025019466877, "Left": 0.029679562896490097, "Top": 0.1664007306098938}, "Polygon": [{"X": 0.029679611325263977, "Y": 0.1664007306098938}, {"X": 0.1485598385334015, "Y": 0.1664055436849594}, {"X": 0.1485597938299179, "Y": 0.179073765873909}, {"X": 0.029679562896490097, "Y": 0.17906907200813293}]}, "Id": "f809c54c-3bc4-489b-bf91-923f3e66fb32", "Relationships": [{"Type": "CHILD", "Ids": ["21b4918d-e40d-43d3-8d95-95448fc841f9", "c6c151ff-d335-400a-a6ce-0cc3e84a2317"]}]}, {"BlockType": "LINE", "Confidence": 98.87336730957031, "Text": "Date/Time In:", "Geometry": {"BoundingBox": {"Width": 0.11309968680143356, "Height": 0.01510962750762701, "Left": 0.030555592849850655, "Top": 0.1868586540222168}, "Polygon": [{"X": 0.03055565059185028, "Y": 0.1868586540222168}, {"X": 0.14365527033805847, "Y": 0.18686304986476898}, {"X": 0.1436552107334137, "Y": 0.20196828246116638}, {"X": 0.030555592849850655, "Y": 0.20196402072906494}]}, "Id": "a3d9979d-6cee-47db-b9be-152d9b54201b", "Relationships": [{"Type": "CHILD", "Ids": ["855bd6f9-7a29-4a4f-8f21-ed8e23bd5a6b", "89c6afd8-baa6-4642-8fcb-9b563617fc4e"]}]}, {"BlockType": "LINE", "Confidence": 99.31348419189453, "Text": "7/23/2025 12:20:00 PM", "Geometry": {"BoundingBox": {"Width": 0.1948787420988083, "Height": 0.01532899308949709, "Left": 0.030215714126825333, "Top": 0.20715484023094177}, "Polygon": [{"X": 0.03021577186882496, "Y": 0.20715484023094177}, {"X": 0.22509445250034332, "Y": 0.20716208219528198}, {"X": 0.22509439289569855, "Y": 0.222483828663826}, {"X": 0.030215714126825333, "Y": 0.22247682511806488}]}, "Id": "86304c36-9bc7-427b-b1e8-d187615799f8", "Relationships": [{"Type": "CHILD", "Ids": ["0f10424a-dda1-43dc-a99d-8da00d1a0401", "cd22c5d4-6f3c-4cb1-a1f0-ce04aec2f6c1", "988a516e-9c5a-455c-95f7-7fcf3e892f7f"]}]}, {"BlockType": "LINE", "Confidence": 99.90563201904297, "Text": "AcctCN: CHIMSC", "Geometry": {"BoundingBox": {"Width": 0.1394094079732895, "Height": 0.012406066060066223, "Left": 0.02951226197183132, "Top": 0.23864372074604034}, "Polygon": [{"X": 0.029512308537960052, "Y": 0.23864372074604034}, {"X": 0.16892166435718536, "Y": 0.23864854872226715}, {"X": 0.16892161965370178, "Y": 0.25104978680610657}, {"X": 0.02951226197183132, "Y": 0.2510451078414917}]}, "Id": "20cc8eda-0ba9-4914-91ba-1c1c94d4e173", "Relationships": [{"Type": "CHILD", "Ids": ["144b9542-e59d-43aa-bbb7-be9d15d5df97", "778fca24-88e3-4a71-bfec-8f8f7fe52dbc"]}]}, {"BlockType": "LINE", "Confidence": 98.99932098388672, "Text": "AcctCH:", "Geometry": {"BoundingBox": {"Width": 0.06278356909751892, "Height": 0.012453309260308743, "Left": 0.02962317503988743, "Top": 0.2593015134334564}, "Polygon": [{"X": 0.02962322346866131, "Y": 0.2593015134334564}, {"X": 0.0924067422747612, "Y": 0.25930359959602356}, {"X": 0.09240669012069702, "Y": 0.27175483107566833}, {"X": 0.02962317503988743, "Y": 0.27175283432006836}]}, "Id": "9e17bc35-56a9-4271-854a-5900a613fd6c", "Relationships": [{"Type": "CHILD", "Ids": ["1b79b0eb-5406-4a85-af42-d4f84e94b581"]}]}, {"BlockType": "LINE", "Confidence": 99.88610076904297, "Text": "SizeCN: 45HC", "Geometry": {"BoundingBox": {"Width": 0.11659853905439377, "Height": 0.012691093608736992, "Left": 0.029905928298830986, "Top": 0.27951109409332275}, "Polygon": [{"X": 0.029905976727604866, "Y": 0.27951109409332275}, {"X": 0.1465044617652893, "Y": 0.27951472997665405}, {"X": 0.14650441706180573, "Y": 0.2922021746635437}, {"X": 0.029905928298830986, "Y": 0.29219865798950195}]}, "Id": "ab35b917-f588-4402-b828-139aadc5a3cf", "Relationships": [{"Type": "CHILD", "Ids": ["135dba9d-36a1-4675-b38e-36b8ca2b052e", "5c420c72-b3a2-40f4-975d-5150a6b4a39b"]}]}, {"BlockType": "LINE", "Confidence": 99.31739807128906, "Text": "SizeCH:", "Geometry": {"BoundingBox": {"Width": 0.06035061180591583, "Height": 0.012479154393076897, "Left": 0.030152326449751854, "Top": 0.29995226860046387}, "Polygon": [{"X": 0.030152373015880585, "Y": 0.29995226860046387}, {"X": 0.09050293266773224, "Y": 0.29995405673980713}, {"X": 0.09050288796424866, "Y": 0.3124314248561859}, {"X": 0.030152326449751854, "Y": 0.3124296963214874}]}, "Id": "0b047bef-e46e-4d16-b494-d77022daf8df", "Relationships": [{"Type": "CHILD", "Ids": ["777fb735-05ca-4635-a25f-1741a0138878"]}]}, {"BlockType": "LINE", "Confidence": 94.44036865234375, "Text": "Cont : MEDU9551199", "Geometry": {"BoundingBox": {"Width": 0.17574794590473175, "Height": 0.012755108997225761, "Left": 0.03020704723894596, "Top": 0.3317888677120209}, "Polygon": [{"X": 0.03020709566771984, "Y": 0.3317888677120209}, {"X": 0.20595499873161316, "Y": 0.3317936360836029}, {"X": 0.20595493912696838, "Y": 0.3445439636707306}, {"X": 0.03020704723894596, "Y": 0.3445394039154053}]}, "Id": "44276620-d655-4966-a53d-8297588902cb", "Relationships": [{"Type": "CHILD", "Ids": ["053fc853-2777-4830-a8e9-352cac53e9af", "a493124d-7b1b-4bc2-9cdf-93cf74a69a4a", "b9da8c3e-377e-43eb-a1a0-a9cea35467ef"]}]}, {"BlockType": "LINE", "Confidence": 98.32479858398438, "Text": "Chas :", "Geometry": {"BoundingBox": {"Width": 0.048093151301145554, "Height": 0.012736489996314049, "Left": 0.030096299946308136, "Top": 0.3520280122756958}, "Polygon": [{"X": 0.030096348375082016, "Y": 0.3520280122756958}, {"X": 0.07818945497274399, "Y": 0.3520292341709137}, {"X": 0.07818940281867981, "Y": 0.3647645115852356}, {"X": 0.030096299946308136, "Y": 0.3647633194923401}]}, "Id": "f4433712-ba93-4dbb-98b9-d5ff6433135b", "Relationships": [{"Type": "CHILD", "Ids": ["1d8e1040-6a52-4eca-92b2-4d198449a968", "67ee1895-4e32-480f-9060-94d004fcbffd"]}]}, {"BlockType": "LINE", "Confidence": 98.23202514648438, "Text": "Gens :", "Geometry": {"BoundingBox": {"Width": 0.04911847412586212, "Height": 0.012522434815764427, "Left": 0.030218206346035004, "Top": 0.3725905418395996}, "Polygon": [{"X": 0.030218254774808884, "Y": 0.3725905418395996}, {"X": 0.07933668047189713, "Y": 0.37259170413017273}, {"X": 0.07933662831783295, "Y": 0.3851129710674286}, {"X": 0.030218206346035004, "Y": 0.38511186838150024}]}, "Id": "0a184042-6d75-49f7-b1ef-b9af1f58b3a7", "Relationships": [{"Type": "CHILD", "Ids": ["cf0faff8-4819-40f3-bd6b-ae94064da253", "e47e7494-508c-4fca-84dd-f2155ab73a3b"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "Remarks:", "Geometry": {"BoundingBox": {"Width": 0.07461658865213394, "Height": 0.012686639092862606, "Left": 0.030736325308680534, "Top": 0.40456801652908325}, "Polygon": [{"X": 0.030736373737454414, "Y": 0.40456801652908325}, {"X": 0.10535290837287903, "Y": 0.4045695960521698}, {"X": 0.10535285621881485, "Y": 0.41725465655326843}, {"X": 0.030736325308680534, "Y": 0.41725313663482666}]}, "Id": "0c3105c8-3a35-4c24-a42a-9f1986e89523", "Relationships": [{"Type": "CHILD", "Ids": ["7611451a-62ae-4c54-96b7-968b22fbfbfd"]}]}, {"BlockType": "LINE", "Confidence": 98.57382202148438, "Text": "Load/Empty: E", "Geometry": {"BoundingBox": {"Width": 0.1354791224002838, "Height": 0.015621237456798553, "Left": 0.030626783147454262, "Top": 0.4372060000896454}, "Polygon": [{"X": 0.030626842752099037, "Y": 0.4372060000896454}, {"X": 0.16610591113567352, "Y": 0.43720850348472595}, {"X": 0.16610583662986755, "Y": 0.45282724499702454}, {"X": 0.030626783147454262, "Y": 0.4528249204158783}]}, "Id": "97852e77-3dd7-4e59-8c07-96ab7438eee5", "Relationships": [{"Type": "CHILD", "Ids": ["8a94122b-691b-4f4b-8122-eeb03ba6d369", "497b2ef8-7552-4f02-ad93-550293d75f5e"]}]}, {"BlockType": "LINE", "Confidence": 89.47916412353516, "Text": "Net Weight: 0", "Geometry": {"BoundingBox": {"Width": 0.13551737368106842, "Height": 0.015511134639382362, "Left": 0.030769918113946915, "Top": 0.4575783312320709}, "Polygon": [{"X": 0.03076997771859169, "Y": 0.4575783312320709}, {"X": 0.16628728806972504, "Y": 0.4575805962085724}, {"X": 0.16628722846508026, "Y": 0.47308945655822754}, {"X": 0.030769918113946915, "Y": 0.4730873703956604}]}, "Id": "8d4d8966-56fe-47ad-a6b8-05fec61b4963", "Relationships": [{"Type": "CHILD", "Ids": ["d7aed0d6-25e5-496a-933d-6d4666cee6f2", "1c83cd12-0900-49b7-a944-c0bbb0513eef", "ed934dfe-d331-4f81-a12e-56d52380c2a4"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Container Damages:", "Geometry": {"BoundingBox": {"Width": 0.16136309504508972, "Height": 0.015329251997172832, "Left": 0.02938017062842846, "Top": 0.48629331588745117}, "Polygon": [{"X": 0.029380228370428085, "Y": 0.48629331588745117}, {"X": 0.19074325263500214, "Y": 0.4862956404685974}, {"X": 0.19074319303035736, "Y": 0.5016225576400757}, {"X": 0.02938017062842846, "Y": 0.5016204118728638}]}, "Id": "4d87e731-553d-4a4e-a6ba-acef2a8ee25e", "Relationships": [{"Type": "CHILD", "Ids": ["5a09ba9f-70c3-41b7-9646-786f8c18c4aa", "e1888bf7-46e5-4c35-9785-222efb31831d"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Chassis Damages:", "Geometry": {"BoundingBox": {"Width": 0.14328071475028992, "Height": 0.015323344618082047, "Left": 0.02926817163825035, "Top": 0.5344220399856567}, "Polygon": [{"X": 0.029268231242895126, "Y": 0.5344220399856567}, {"X": 0.17254889011383057, "Y": 0.5344235301017761}, {"X": 0.1725488156080246, "Y": 0.5497453808784485}, {"X": 0.02926817163825035, "Y": 0.5497440695762634}]}, "Id": "9b960789-354a-4737-a73c-edb96df44b65", "Relationships": [{"Type": "CHILD", "Ids": ["41ba24c8-16e4-431e-b905-cdf27a056e7f", "df9a5e5c-8eee-428c-9f6d-ce69ade504b2"]}]}, {"BlockType": "LINE", "Confidence": 99.57659149169922, "Text": "Tire Information:", "Geometry": {"BoundingBox": {"Width": 0.13792157173156738, "Height": 0.01302784401923418, "Left": 0.02852313220500946, "Top": 0.5801174640655518}, "Polygon": [{"X": 0.02852318249642849, "Y": 0.5801174640655518}, {"X": 0.16644470393657684, "Y": 0.5801184177398682}, {"X": 0.16644464433193207, "Y": 0.5931453108787537}, {"X": 0.02852313220500946, "Y": 0.5931445360183716}]}, "Id": "4dda06aa-ac77-4c63-a91b-ef7bdb7365cc", "Relationships": [{"Type": "CHILD", "Ids": ["be7bbd11-15e3-4530-867c-49380751c691", "f02a6bd5-f766-4aad-b1d2-7ca47c349f50"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "SCAC: SSRT", "Geometry": {"BoundingBox": {"Width": 0.11541096121072769, "Height": 0.01254047080874443, "Left": 0.028960566967725754, "Top": 0.6280825734138489}, "Polygon": [{"X": 0.028960615396499634, "Y": 0.6280825734138489}, {"X": 0.14437152445316315, "Y": 0.6280829310417175}, {"X": 0.14437147974967957, "Y": 0.6406230926513672}, {"X": 0.028960566967725754, "Y": 0.6406228542327881}]}, "Id": "902c575c-13e4-4c1b-94d2-d08470fbcc7f", "Relationships": [{"Type": "CHILD", "Ids": ["2d7f91c9-c4ff-404d-9fbb-fc8fe1a62f0a", "223067c5-d282-4205-8692-ec20ba1145ad"]}]}, {"BlockType": "LINE", "Confidence": 99.93836975097656, "Text": "Carrier: SUPER STAR FREIG", "Geometry": {"BoundingBox": {"Width": 0.22811433672904968, "Height": 0.012924445793032646, "Left": 0.029162565246224403, "Top": 0.648241400718689}, "Polygon": [{"X": 0.029162615537643433, "Y": 0.648241400718689}, {"X": 0.25727689266204834, "Y": 0.6482416987419128}, {"X": 0.25727683305740356, "Y": 0.6611658334732056}, {"X": 0.029162565246224403, "Y": 0.6611657738685608}]}, "Id": "96ed0e0d-eee7-48ce-b22b-b51fc34f6ac7", "Relationships": [{"Type": "CHILD", "Ids": ["15e1f7c6-db0c-4cae-a144-aed8c994502b", "68d8c283-aa2f-4194-afef-6242a57293d0", "abb5b1e5-183c-4443-bf4b-a388280a17fa", "5da24446-d3e1-4de8-8f13-14797c6d18bf"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "Driver: 470", "Geometry": {"BoundingBox": {"Width": 0.10270343720912933, "Height": 0.012824621982872486, "Left": 0.02978135086596012, "Top": 0.6688089370727539}, "Polygon": [{"X": 0.029781399294734, "Y": 0.6688089966773987}, {"X": 0.1324847936630249, "Y": 0.6688089370727539}, {"X": 0.13248473405838013, "Y": 0.6816334128379822}, {"X": 0.02978135086596012, "Y": 0.6816335916519165}]}, "Id": "66ddfe56-4ed6-471b-9ee8-40bce9af4a23", "Relationships": [{"Type": "CHILD", "Ids": ["85c6ee68-0283-4831-8088-051d1f495e4b", "462c0b4f-aa13-4572-9da4-945bcde07d5b"]}]}, {"BlockType": "LINE", "Confidence": 99.0489501953125, "Text": "Truck#:", "Geometry": {"BoundingBox": {"Width": 0.06316521018743515, "Height": 0.012448742985725403, "Left": 0.029917892068624496, "Top": 0.6892077922821045}, "Polygon": [{"X": 0.029917940497398376, "Y": 0.689207911491394}, {"X": 0.09308309853076935, "Y": 0.6892077922821045}, {"X": 0.09308305382728577, "Y": 0.7016563415527344}, {"X": 0.029917892068624496, "Y": 0.7016565203666687}]}, "Id": "1a3d7773-8722-4552-94b3-d032faa7a372", "Relationships": [{"Type": "CHILD", "Ids": ["cc82e342-0cdb-431b-aef4-75985fa8e251"]}]}, {"BlockType": "LINE", "Confidence": 99.28936767578125, "Text": "7/23/2025 12:20:00 PM", "Geometry": {"BoundingBox": {"Width": 0.19522996246814728, "Height": 0.01503751240670681, "Left": 0.026753360405564308, "Top": 0.7198354005813599}, "Polygon": [{"X": 0.026753418147563934, "Y": 0.7198362946510315}, {"X": 0.22198332846164703, "Y": 0.7198354005813599}, {"X": 0.22198325395584106, "Y": 0.7348718047142029}, {"X": 0.026753360405564308, "Y": 0.7348729372024536}]}, "Id": "cf9821c7-eaf0-424b-9abb-c65233f41a15", "Relationships": [{"Type": "CHILD", "Ids": ["7556e2cf-4ce3-424b-bd50-caf7bd8ab0fc", "cd2bcf55-0dc2-4fb0-9a76-100a72acb3da", "ee75641b-1737-45ba-a569-dacbdf340567"]}]}, {"BlockType": "LINE", "Confidence": 99.9473876953125, "Text": "Gate In Receipt", "Geometry": {"BoundingBox": {"Width": 0.12545381486415863, "Height": 0.015519856475293636, "Left": 0.07732104510068893, "Top": 0.8219107389450073}, "Polygon": [{"X": 0.07732110470533371, "Y": 0.8219123482704163}, {"X": 0.20277486741542816, "Y": 0.8219107389450073}, {"X": 0.2027747929096222, "Y": 0.8374288082122803}, {"X": 0.07732104510068893, "Y": 0.8374305963516235}]}, "Id": "9a53f8c2-b17f-4329-834c-ff7164bba199", "Relationships": [{"Type": "CHILD", "Ids": ["9a063d27-78b0-4494-8c3d-ef8635ddb985", "496cfa8b-2e5f-45d4-83d7-bbd8274c1f76", "2973cd28-5f83-4ae3-bf9c-2d5ccdf28d22"]}]}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037642884999513626, "Height": 0.012569524347782135, "Left": 0.05138896033167839, "Top": 0.03560244292020798}, "Polygon": [{"X": 0.05138900876045227, "Y": 0.03560244292020798}, {"X": 0.08903184533119202, "Y": 0.035604365170001984}, {"X": 0.08903179317712784, "Y": 0.04817196726799011}, {"X": 0.05138896033167839, "Y": 0.04817007854580879}]}, "Id": "cc06ab0e-e658-43b8-9894-d20986e06ff0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01795567385852337, "Height": 0.012333896942436695, "Left": 0.09319727122783661, "Top": 0.03576715663075447}, "Polygon": [{"X": 0.09319732338190079, "Y": 0.03576715663075447}, {"X": 0.11115294694900513, "Y": 0.03576807305216789}, {"X": 0.11115289479494095, "Y": 0.04810105264186859}, {"X": 0.09319727122783661, "Y": 0.04810015484690666}]}, "Id": "3e24c872-96c9-40d3-9f8d-c1318594e393"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Interchange", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09898057579994202, "Height": 0.015543263405561447, "Left": 0.11590168625116348, "Top": 0.03538888692855835}, "Polygon": [{"X": 0.11590174585580826, "Y": 0.03538888692855835}, {"X": 0.2148822546005249, "Y": 0.03539394959807396}, {"X": 0.21488219499588013, "Y": 0.0509321503341198}, {"X": 0.11590168625116348, "Y": 0.050927214324474335}]}, "Id": "f9dbfce2-0814-4a0d-b8f4-0d2c2d706c2d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Integrated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08667169511318207, "Height": 0.015556582249701023, "Left": 0.029862839728593826, "Top": 0.06081441044807434}, "Polygon": [{"X": 0.0298628993332386, "Y": 0.06081441044807434}, {"X": 0.11653453856706619, "Y": 0.060818664729595184}, {"X": 0.11653447151184082, "Y": 0.07637099176645279}, {"X": 0.029862839728593826, "Y": 0.0763668492436409}]}, "Id": "369315c4-1dfc-46a6-a950-1aeee3ff8567"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Industries", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08302097022533417, "Height": 0.012977675534784794, "Left": 0.12091848999261856, "Top": 0.060807567089796066}, "Polygon": [{"X": 0.12091854214668274, "Y": 0.060807567089796066}, {"X": 0.20393946766853333, "Y": 0.060811642557382584}, {"X": 0.20393940806388855, "Y": 0.07378524541854858}, {"X": 0.12091848999261856, "Y": 0.07378125190734863}]}, "Id": "3eb9be84-d27d-4071-b80a-503969f6923e"}, {"BlockType": "WORD", "Confidence": 92.63372802734375, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006508160382509232, "Height": 0.0036897393874824047, "Left": 0.20817118883132935, "Top": 0.06624958664178848}, "Polygon": [{"X": 0.20817120373249054, "Y": 0.06624958664178848}, {"X": 0.21467934548854828, "Y": 0.06624990701675415}, {"X": 0.21467933058738708, "Y": 0.06993933022022247}, {"X": 0.20817118883132935, "Y": 0.0699390172958374}]}, "Id": "b3b5f776-f03c-4323-ac9c-df3b90004ce6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CHICAGO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07598911970853806, "Height": 0.012348714284598827, "Left": 0.030034122988581657, "Top": 0.07931121438741684}, "Polygon": [{"X": 0.030034171417355537, "Y": 0.07931121438741684}, {"X": 0.10602324455976486, "Y": 0.07931482791900635}, {"X": 0.10602319240570068, "Y": 0.09165992587804794}, {"X": 0.030034122988581657, "Y": 0.0916563868522644}]}, "Id": "3acad0d0-bc32-4197-8a89-ca3dec9ef216"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2750", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040071383118629456, "Height": 0.012580457143485546, "Left": 0.03049110807478428, "Top": 0.09954296052455902}, "Polygon": [{"X": 0.03049115650355816, "Y": 0.09954296052455902}, {"X": 0.07056248933076859, "Y": 0.09954480081796646}, {"X": 0.070562444627285, "Y": 0.11212341487407684}, {"X": 0.03049110807478428, "Y": 0.11212161928415298}]}, "Id": "e7ee6f01-05a2-4451-9810-84c117fa8cd8"}, {"BlockType": "WORD", "Confidence": 98.2372055053711, "Text": "W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017023732885718346, "Height": 0.012297402136027813, "Left": 0.07490985840559006, "Top": 0.09959579259157181}, "Polygon": [{"X": 0.07490990310907364, "Y": 0.09959579259157181}, {"X": 0.09193359315395355, "Y": 0.09959657490253448}, {"X": 0.09193354099988937, "Y": 0.1118931993842125}, {"X": 0.07490985840559006, "Y": 0.11189243197441101}]}, "Id": "ef9610c1-9f9e-487d-ac7b-41df28ac33db"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "35th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03679698333144188, "Height": 0.012703781016170979, "Left": 0.09636901319026947, "Top": 0.09942146390676498}, "Polygon": [{"X": 0.09636906534433365, "Y": 0.09942146390676498}, {"X": 0.13316600024700165, "Y": 0.09942315518856049}, {"X": 0.13316594064235687, "Y": 0.11212524771690369}, {"X": 0.09636901319026947, "Y": 0.11212359368801117}]}, "Id": "1cd0c198-67a7-4f47-a8a0-499af9aed0c7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0497392900288105, "Height": 0.012384173460304737, "Left": 0.13772174715995789, "Top": 0.09966380149126053}, "Polygon": [{"X": 0.13772179186344147, "Y": 0.09966380149126053}, {"X": 0.1874610334634781, "Y": 0.09966608881950378}, {"X": 0.1874609738588333, "Y": 0.11204797774553299}, {"X": 0.13772174715995789, "Y": 0.11204574257135391}]}, "Id": "57e63e33-3abf-4fb5-bd13-2f3b8ae49ccb"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06338164210319519, "Height": 0.015825361013412476, "Left": 0.029981615021824837, "Top": 0.11950840801000595}, "Polygon": [{"X": 0.02998167648911476, "Y": 0.11950840801000595}, {"X": 0.09336326271295547, "Y": 0.11951121687889099}, {"X": 0.0933631956577301, "Y": 0.13533377647399902}, {"X": 0.029981615021824837, "Y": 0.13533104956150055}]}, "Id": "e6cb9290-6a1c-4c3a-8f60-b7dbcca14bc4"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017048683017492294, "Height": 0.011979324743151665, "Left": 0.18004728853702545, "Top": 0.12023251503705978}, "Polygon": [{"X": 0.18004733324050903, "Y": 0.12023251503705978}, {"X": 0.19709596037864685, "Y": 0.12023327499628067}, {"X": 0.19709591567516327, "Y": 0.132211834192276}, {"X": 0.18004728853702545, "Y": 0.1322111040353775}]}, "Id": "cd12deb7-a20f-4333-a1bf-2fac1f9e0659"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "TIR#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04743287339806557, "Height": 0.012285283766686916, "Left": 0.029679562896490097, "Top": 0.1666865348815918}, "Polygon": [{"X": 0.029679611325263977, "Y": 0.1666865348815918}, {"X": 0.07711243629455566, "Y": 0.1666884571313858}, {"X": 0.07711238414049149, "Y": 0.17897182703018188}, {"X": 0.029679562896490097, "Y": 0.17896994948387146}]}, "Id": "21b4918d-e40d-43d3-8d95-95448fc841f9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "733330", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06078348308801651, "Height": 0.012670672498643398, "Left": 0.08777636289596558, "Top": 0.16640308499336243}, "Polygon": [{"X": 0.08777641505002975, "Y": 0.16640308499336243}, {"X": 0.1485598385334015, "Y": 0.1664055436849594}, {"X": 0.1485597938299179, "Y": 0.179073765873909}, {"X": 0.08777636289596558, "Y": 0.1790713667869568}]}, "Id": "c6c151ff-d335-400a-a6ce-0cc3e84a2317"}, {"BlockType": "WORD", "Confidence": 98.24955749511719, "Text": "Date/Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08627592772245407, "Height": 0.015108618885278702, "Left": 0.030555592849850655, "Top": 0.1868586540222168}, "Polygon": [{"X": 0.03055565059185028, "Y": 0.1868586540222168}, {"X": 0.11683152616024017, "Y": 0.1868620067834854}, {"X": 0.1168314591050148, "Y": 0.2019672691822052}, {"X": 0.030555592849850655, "Y": 0.20196402072906494}]}, "Id": "855bd6f9-7a29-4a4f-8f21-ed8e23bd5a6b"}, {"BlockType": "WORD", "Confidence": 99.4971694946289, "Text": "In:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023475514724850655, "Height": 0.012487081810832024, "Left": 0.12017975747585297, "Top": 0.18721361458301544}, "Polygon": [{"X": 0.12017980962991714, "Y": 0.18721361458301544}, {"X": 0.14365527033805847, "Y": 0.18721453845500946}, {"X": 0.1436552256345749, "Y": 0.19970069825649261}, {"X": 0.12017975747585297, "Y": 0.19969981908798218}]}, "Id": "89c6afd8-baa6-4642-8fcb-9b563617fc4e"}, {"BlockType": "WORD", "Confidence": 99.01944732666016, "Text": "7/23/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0892254039645195, "Height": 0.014929740689694881, "Left": 0.030215714126825333, "Top": 0.2075503021478653}, "Polygon": [{"X": 0.03021577000617981, "Y": 0.2075503021478653}, {"X": 0.11944111436605453, "Y": 0.20755361020565033}, {"X": 0.11944105476140976, "Y": 0.22248004376888275}, {"X": 0.030215714126825333, "Y": 0.22247682511806488}]}, "Id": "0f10424a-dda1-43dc-a99d-8da00d1a0401"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "12:20:00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07177425920963287, "Height": 0.012661296874284744, "Left": 0.12479864060878754, "Top": 0.20751772820949554}, "Polygon": [{"X": 0.12479869276285172, "Y": 0.20751772820949554}, {"X": 0.1965728998184204, "Y": 0.20752039551734924}, {"X": 0.19657284021377563, "Y": 0.22017902135849}, {"X": 0.12479864060878754, "Y": 0.22017642855644226}]}, "Id": "cd22c5d4-6f3c-4cb1-a1f0-ce04aec2f6c1"}, {"BlockType": "WORD", "Confidence": 99.3213882446289, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023801134899258614, "Height": 0.012711894698441029, "Left": 0.20129331946372986, "Top": 0.20716120302677155}, "Polygon": [{"X": 0.20129337906837463, "Y": 0.20716120302677155}, {"X": 0.22509445250034332, "Y": 0.20716208219528198}, {"X": 0.22509440779685974, "Y": 0.2198731005191803}, {"X": 0.20129331946372986, "Y": 0.21987223625183105}]}, "Id": "988a516e-9c5a-455c-95f7-7fcf3e892f7f"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "AcctCN:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06324603408575058, "Height": 0.012403505854308605, "Left": 0.02951226197183132, "Top": 0.23864372074604034}, "Polygon": [{"X": 0.029512308537960052, "Y": 0.23864372074604034}, {"X": 0.09275829792022705, "Y": 0.23864591121673584}, {"X": 0.09275824576616287, "Y": 0.2510472238063812}, {"X": 0.02951226197183132, "Y": 0.2510451078414917}]}, "Id": "144b9542-e59d-43aa-bbb7-be9d15d5df97"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CHIMSC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06568502634763718, "Height": 0.012240233831107616, "Left": 0.10323663800954819, "Top": 0.238719180226326}, "Polygon": [{"X": 0.10323669016361237, "Y": 0.238719180226326}, {"X": 0.16892166435718536, "Y": 0.23872146010398865}, {"X": 0.16892161965370178, "Y": 0.2509594261646271}, {"X": 0.10323663800954819, "Y": 0.2509572207927704}]}, "Id": "778fca24-88e3-4a71-bfec-8f8f7fe52dbc"}, {"BlockType": "WORD", "Confidence": 98.99932098388672, "Text": "AcctCH:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06278356909751892, "Height": 0.012453309260308743, "Left": 0.02962317503988743, "Top": 0.2593015134334564}, "Polygon": [{"X": 0.02962322346866131, "Y": 0.2593015134334564}, {"X": 0.0924067422747612, "Y": 0.25930359959602356}, {"X": 0.09240669012069702, "Y": 0.27175483107566833}, {"X": 0.02962317503988743, "Y": 0.27175283432006836}]}, "Id": "1b79b0eb-5406-4a85-af42-d4f84e94b581"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "SizeCN:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060813792049884796, "Height": 0.012689405120909214, "Left": 0.029905928298830986, "Top": 0.27951109409332275}, "Polygon": [{"X": 0.029905976727604866, "Y": 0.27951109409332275}, {"X": 0.09071972221136093, "Y": 0.27951300144195557}, {"X": 0.09071967005729675, "Y": 0.29220050573349}, {"X": 0.029905928298830986, "Y": 0.29219865798950195}]}, "Id": "135dba9d-36a1-4675-b38e-36b8ca2b052e"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "45HC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04386734589934349, "Height": 0.012495459988713264, "Left": 0.10263711959123611, "Top": 0.2796904742717743}, "Polygon": [{"X": 0.10263717174530029, "Y": 0.2796904742717743}, {"X": 0.1465044617652893, "Y": 0.2796918451786041}, {"X": 0.14650441706180573, "Y": 0.2921859323978424}, {"X": 0.10263711959123611, "Y": 0.29218459129333496}]}, "Id": "5c420c72-b3a2-40f4-975d-5150a6b4a39b"}, {"BlockType": "WORD", "Confidence": 99.31739807128906, "Text": "SizeCH:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06035061180591583, "Height": 0.012479154393076897, "Left": 0.030152326449751854, "Top": 0.29995226860046387}, "Polygon": [{"X": 0.030152373015880585, "Y": 0.29995226860046387}, {"X": 0.09050293266773224, "Y": 0.29995405673980713}, {"X": 0.09050288796424866, "Y": 0.3124314248561859}, {"X": 0.030152326449751854, "Y": 0.3124296963214874}]}, "Id": "777fb735-05ca-4635-a25f-1741a0138878"}, {"BlockType": "WORD", "Confidence": 92.21918487548828, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04271184653043747, "Height": 0.012638498097658157, "Left": 0.03020704723894596, "Top": 0.33190202713012695}, "Polygon": [{"X": 0.03020709566771984, "Y": 0.33190202713012695}, {"X": 0.07291889190673828, "Y": 0.3319031894207001}, {"X": 0.0729188472032547, "Y": 0.3445405066013336}, {"X": 0.03020704723894596, "Y": 0.3445394039154053}]}, "Id": "053fc853-2777-4830-a8e9-352cac53e9af"}, {"BlockType": "WORD", "Confidence": 91.17108154296875, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004199707880616188, "Height": 0.009253614582121372, "Left": 0.07287533581256866, "Top": 0.3348900377750397}, "Polygon": [{"X": 0.07287537306547165, "Y": 0.3348900377750397}, {"X": 0.0770750418305397, "Y": 0.3348901569843292}, {"X": 0.07707500457763672, "Y": 0.34414365887641907}, {"X": 0.07287533581256866, "Y": 0.3441435396671295}]}, "Id": "a493124d-7b1b-4bc2-9cdf-93cf74a69a4a"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "MEDU9551199", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11788187175989151, "Height": 0.012628268450498581, "Left": 0.08807312697172165, "Top": 0.3317904472351074}, "Polygon": [{"X": 0.08807317912578583, "Y": 0.3317904472351074}, {"X": 0.20595499873161316, "Y": 0.3317936360836029}, {"X": 0.20595493912696838, "Y": 0.3444187045097351}, {"X": 0.08807312697172165, "Y": 0.3444156348705292}]}, "Id": "b9da8c3e-377e-43eb-a1a0-a9cea35467ef"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03897034749388695, "Height": 0.012736267410218716, "Left": 0.030096299946308136, "Top": 0.3520280122756958}, "Polygon": [{"X": 0.030096348375082016, "Y": 0.3520280122756958}, {"X": 0.06906665116548538, "Y": 0.3520289957523346}, {"X": 0.0690665990114212, "Y": 0.3647642731666565}, {"X": 0.030096299946308136, "Y": 0.3647633194923401}]}, "Id": "1d8e1040-6a52-4eca-92b2-4d198449a968"}, {"BlockType": "WORD", "Confidence": 96.767578125, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.003969634883105755, "Height": 0.009431987069547176, "Left": 0.07421980798244476, "Top": 0.35519158840179443}, "Polygon": [{"X": 0.07421984523534775, "Y": 0.35519158840179443}, {"X": 0.0781894400715828, "Y": 0.3551916778087616}, {"X": 0.07818940281867981, "Y": 0.3646235764026642}, {"X": 0.07421980798244476, "Y": 0.364623486995697}]}, "Id": "67ee1895-4e32-480f-9060-94d004fcbffd"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Gens", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03982005640864372, "Height": 0.012522224336862564, "Left": 0.030218206346035004, "Top": 0.3725905418395996}, "Polygon": [{"X": 0.030218254774808884, "Y": 0.3725905418395996}, {"X": 0.07003826647996902, "Y": 0.372591495513916}, {"X": 0.07003821432590485, "Y": 0.3851127624511719}, {"X": 0.030218206346035004, "Y": 0.38511186838150024}]}, "Id": "cf0faff8-4819-40f3-bd6b-ae94064da253"}, {"BlockType": "WORD", "Confidence": 96.591796875, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0037746301386505365, "Height": 0.009039524011313915, "Left": 0.07556203752756119, "Top": 0.37562239170074463}, "Polygon": [{"X": 0.07556207478046417, "Y": 0.37562239170074463}, {"X": 0.07933666557073593, "Y": 0.3756224811077118}, {"X": 0.07933662831783295, "Y": 0.3846619129180908}, {"X": 0.07556203752756119, "Y": 0.38466182351112366}]}, "Id": "e47e7494-508c-4fca-84dd-f2155ab73a3b"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Remarks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07461658865213394, "Height": 0.012686639092862606, "Left": 0.030736325308680534, "Top": 0.40456801652908325}, "Polygon": [{"X": 0.030736373737454414, "Y": 0.40456801652908325}, {"X": 0.10535290837287903, "Y": 0.4045695960521698}, {"X": 0.10535285621881485, "Y": 0.41725465655326843}, {"X": 0.030736325308680534, "Y": 0.41725313663482666}]}, "Id": "7611451a-62ae-4c54-96b7-968b22fbfbfd"}, {"BlockType": "WORD", "Confidence": 98.45663452148438, "Text": "Load/Empty:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10317765176296234, "Height": 0.015620681457221508, "Left": 0.030626783147454262, "Top": 0.4372060000896454}, "Polygon": [{"X": 0.030626842752099037, "Y": 0.4372060000896454}, {"X": 0.13380444049835205, "Y": 0.4372079074382782}, {"X": 0.13380436599254608, "Y": 0.45282667875289917}, {"X": 0.030626783147454262, "Y": 0.4528249204158783}]}, "Id": "8a94122b-691b-4f4b-8122-eeb03ba6d369"}, {"BlockType": "WORD", "Confidence": 98.69100952148438, "Text": "E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00913878995925188, "Height": 0.011976954527199268, "Left": 0.15696711838245392, "Top": 0.4379102289676666}, "Polygon": [{"X": 0.1569671630859375, "Y": 0.4379102289676666}, {"X": 0.16610591113567352, "Y": 0.43791040778160095}, {"X": 0.16610585153102875, "Y": 0.4498871862888336}, {"X": 0.15696711838245392, "Y": 0.4498870372772217}]}, "Id": "497b2ef8-7552-4f02-ad93-550293d75f5e"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02832021750509739, "Height": 0.012781369499862194, "Left": 0.03076992742717266, "Top": 0.45788440108299255}, "Polygon": [{"X": 0.03076997585594654, "Y": 0.45788440108299255}, {"X": 0.05909014493227005, "Y": 0.45788484811782837}, {"X": 0.05909009650349617, "Y": 0.47066575288772583}, {"X": 0.03076992742717266, "Y": 0.47066530585289}]}, "Id": "d7aed0d6-25e5-496a-933d-6d4666cee6f2"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06282815337181091, "Height": 0.015509964898228645, "Left": 0.06272634863853455, "Top": 0.4575788676738739}, "Polygon": [{"X": 0.06272640824317932, "Y": 0.4575788676738739}, {"X": 0.12555450201034546, "Y": 0.45757991075515747}, {"X": 0.12555444240570068, "Y": 0.4730888307094574}, {"X": 0.06272634863853455, "Y": 0.4730878472328186}]}, "Id": "1c83cd12-0900-49b7-a944-c0bbb0513eef"}, {"BlockType": "WORD", "Confidence": 68.74342346191406, "Text": "0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009784296154975891, "Height": 0.012323513627052307, "Left": 0.15650299191474915, "Top": 0.458030641078949}, "Polygon": [{"X": 0.15650303661823273, "Y": 0.458030641078949}, {"X": 0.16628728806972504, "Y": 0.4580308198928833}, {"X": 0.16628722846508026, "Y": 0.4703541696071625}, {"X": 0.15650299191474915, "Y": 0.47035402059555054}]}, "Id": "ed934dfe-d331-4f81-a12e-56d52380c2a4"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07849885523319244, "Height": 0.01278152596205473, "Left": 0.029380179941654205, "Top": 0.48629331588745117}, "Polygon": [{"X": 0.029380228370428085, "Y": 0.48629331588745117}, {"X": 0.10787903517484665, "Y": 0.4862944483757019}, {"X": 0.10787898302078247, "Y": 0.4990748465061188}, {"X": 0.029380179941654205, "Y": 0.4990738034248352}]}, "Id": "5a09ba9f-70c3-41b7-9646-786f8c18c4aa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Damages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07825422286987305, "Height": 0.01519003789871931, "Left": 0.11248903721570969, "Top": 0.4864325225353241}, "Polygon": [{"X": 0.11248909682035446, "Y": 0.4864325225353241}, {"X": 0.19074325263500214, "Y": 0.48643365502357483}, {"X": 0.19074319303035736, "Y": 0.5016225576400757}, {"X": 0.11248903721570969, "Y": 0.5016215443611145}]}, "Id": "e1888bf7-46e5-4c35-9785-222efb31831d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05985976755619049, "Height": 0.012585942633450031, "Left": 0.029268182814121246, "Top": 0.5344220399856567}, "Polygon": [{"X": 0.029268231242895126, "Y": 0.5344220399856567}, {"X": 0.08912795037031174, "Y": 0.5344226956367493}, {"X": 0.08912789821624756, "Y": 0.5470079779624939}, {"X": 0.029268182814121246, "Y": 0.5470074415206909}]}, "Id": "41ba24c8-16e4-431e-b905-cdf27a056e7f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Damages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07851768285036087, "Height": 0.015230217017233372, "Left": 0.0940312072634697, "Top": 0.5345152020454407}, "Polygon": [{"X": 0.09403126686811447, "Y": 0.5345152020454407}, {"X": 0.17254889011383057, "Y": 0.5345159769058228}, {"X": 0.1725488156080246, "Y": 0.5497453808784485}, {"X": 0.0940312072634697, "Y": 0.5497446656227112}]}, "Id": "df9a5e5c-8eee-428c-9f6d-ce69ade504b2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Tire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031987983733415604, "Height": 0.012705051340162754, "Left": 0.02852313220500946, "Top": 0.5804396271705627}, "Polygon": [{"X": 0.02852318063378334, "Y": 0.5804396271705627}, {"X": 0.060511115938425064, "Y": 0.5804398655891418}, {"X": 0.060511067509651184, "Y": 0.5931447148323059}, {"X": 0.02852313220500946, "Y": 0.5931445360183716}]}, "Id": "be7bbd11-15e3-4530-867c-49380751c691"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "Information:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10178706794977188, "Height": 0.013014139607548714, "Left": 0.06465762853622437, "Top": 0.5801177024841309}, "Polygon": [{"X": 0.06465768069028854, "Y": 0.5801177024841309}, {"X": 0.16644470393657684, "Y": 0.5801184177398682}, {"X": 0.16644464433193207, "Y": 0.5931318402290344}, {"X": 0.06465762853622437, "Y": 0.5931312441825867}]}, "Id": "f02a6bd5-f766-4aad-b1d2-7ca47c349f50"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SCAC:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04714086651802063, "Height": 0.012443650513887405, "Left": 0.028960566967725754, "Top": 0.6280825734138489}, "Polygon": [{"X": 0.028960615396499634, "Y": 0.6280825734138489}, {"X": 0.07610143721103668, "Y": 0.6280827522277832}, {"X": 0.0761013850569725, "Y": 0.6405262351036072}, {"X": 0.028960566967725754, "Y": 0.6405261754989624}]}, "Id": "2d7f91c9-c4ff-404d-9fbb-fc8fe1a62f0a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04237717390060425, "Height": 0.012424689717590809, "Left": 0.1019943505525589, "Top": 0.6281983852386475}, "Polygon": [{"X": 0.10199440270662308, "Y": 0.6281983852386475}, {"X": 0.14437152445316315, "Y": 0.628198504447937}, {"X": 0.14437147974967957, "Y": 0.6406230926513672}, {"X": 0.1019943505525589, "Y": 0.6406229734420776}]}, "Id": "223067c5-d282-4205-8692-ec20ba1145ad"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06014907732605934, "Height": 0.012806354090571404, "Left": 0.029162567108869553, "Top": 0.648241400718689}, "Polygon": [{"X": 0.029162615537643433, "Y": 0.648241400718689}, {"X": 0.0893116444349289, "Y": 0.6482414603233337}, {"X": 0.08931159228086472, "Y": 0.6610477566719055}, {"X": 0.029162567108869553, "Y": 0.6610476970672607}]}, "Id": "15e1f7c6-db0c-4cae-a144-aed8c994502b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SUPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05427370220422745, "Height": 0.01257514301687479, "Left": 0.10191681981086731, "Top": 0.6484489440917969}, "Polygon": [{"X": 0.10191686451435089, "Y": 0.6484489440917969}, {"X": 0.15619051456451416, "Y": 0.6484490036964417}, {"X": 0.15619046986103058, "Y": 0.6610240936279297}, {"X": 0.10191681981086731, "Y": 0.6610240936279297}]}, "Id": "68d8c283-aa2f-4194-afef-6242a57293d0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "STAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042741864919662476, "Height": 0.012748361565172672, "Left": 0.16045454144477844, "Top": 0.6484174728393555}, "Polygon": [{"X": 0.16045460104942322, "Y": 0.6484174728393555}, {"X": 0.20319640636444092, "Y": 0.6484175324440002}, {"X": 0.20319634675979614, "Y": 0.6611658334732056}, {"X": 0.16045454144477844, "Y": 0.6611658334732056}]}, "Id": "abb5b1e5-183c-4443-bf4b-a388280a17fa"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "FREIG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04945159703493118, "Height": 0.012424513697624207, "Left": 0.20782530307769775, "Top": 0.6484673023223877}, "Polygon": [{"X": 0.20782536268234253, "Y": 0.6484673023223877}, {"X": 0.25727689266204834, "Y": 0.6484673619270325}, {"X": 0.25727683305740356, "Y": 0.6608917713165283}, {"X": 0.20782530307769775, "Y": 0.6608917713165283}]}, "Id": "5da24446-d3e1-4de8-8f13-14797c6d18bf"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Driver:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053694870322942734, "Height": 0.012824603356420994, "Left": 0.02978135086596012, "Top": 0.6688089966773987}, "Polygon": [{"X": 0.029781399294734, "Y": 0.6688089966773987}, {"X": 0.083476223051548, "Y": 0.6688089966773987}, {"X": 0.08347617089748383, "Y": 0.6816335320472717}, {"X": 0.02978135086596012, "Y": 0.6816335916519165}]}, "Id": "85c6ee68-0283-4831-8088-051d1f495e4b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "470", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030619649216532707, "Height": 0.012533321976661682, "Left": 0.10186514258384705, "Top": 0.6690369248390198}, "Polygon": [{"X": 0.10186519473791122, "Y": 0.6690369248390198}, {"X": 0.1324847936630249, "Y": 0.6690369248390198}, {"X": 0.13248473405838013, "Y": 0.6815702319145203}, {"X": 0.10186514258384705, "Y": 0.6815702319145203}]}, "Id": "462c0b4f-aa13-4572-9da4-945bcde07d5b"}, {"BlockType": "WORD", "Confidence": 99.0489501953125, "Text": "Truck#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06316521018743515, "Height": 0.012448742985725403, "Left": 0.029917892068624496, "Top": 0.6892077922821045}, "Polygon": [{"X": 0.029917940497398376, "Y": 0.689207911491394}, {"X": 0.09308309853076935, "Y": 0.6892077922821045}, {"X": 0.09308305382728577, "Y": 0.7016563415527344}, {"X": 0.029917892068624496, "Y": 0.7016565203666687}]}, "Id": "cc82e342-0cdb-431b-aef4-75985fa8e251"}, {"BlockType": "WORD", "Confidence": 99.16194915771484, "Text": "7/23/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08949572592973709, "Height": 0.015037032775580883, "Left": 0.026753360405564308, "Top": 0.7198358774185181}, "Polygon": [{"X": 0.026753418147563934, "Y": 0.7198362946510315}, {"X": 0.11624908447265625, "Y": 0.7198358774185181}, {"X": 0.11624902486801147, "Y": 0.7348724007606506}, {"X": 0.026753360405564308, "Y": 0.7348729372024536}]}, "Id": "7556e2cf-4ce3-424b-bd50-caf7bd8ab0fc"}, {"BlockType": "WORD", "Confidence": 99.443359375, "Text": "12:20:00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07170917838811874, "Height": 0.012535465881228447, "Left": 0.12157607823610306, "Top": 0.7203783988952637}, "Polygon": [{"X": 0.12157613039016724, "Y": 0.7203787565231323}, {"X": 0.1932852566242218, "Y": 0.7203783988952637}, {"X": 0.19328519701957703, "Y": 0.7329134941101074}, {"X": 0.12157607823610306, "Y": 0.7329138517379761}]}, "Id": "cd2bcf55-0dc2-4fb0-9a76-100a72acb3da"}, {"BlockType": "WORD", "Confidence": 99.2627944946289, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0237532127648592, "Height": 0.012589812278747559, "Left": 0.1982301026582718, "Top": 0.7203041315078735}, "Polygon": [{"X": 0.19823016226291656, "Y": 0.7203042507171631}, {"X": 0.22198331356048584, "Y": 0.7203041315078735}, {"X": 0.22198326885700226, "Y": 0.7328938245773315}, {"X": 0.1982301026582718, "Y": 0.7328939437866211}]}, "Id": "ee75641b-1737-45ba-a569-dacbdf340567"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037852391600608826, "Height": 0.012608914636075497, "Left": 0.07732105255126953, "Top": 0.8221630454063416}, "Polygon": [{"X": 0.07732110470533371, "Y": 0.8221635222434998}, {"X": 0.11517344415187836, "Y": 0.8221630454063416}, {"X": 0.11517339199781418, "Y": 0.8347713947296143}, {"X": 0.07732105255126953, "Y": 0.8347719311714172}]}, "Id": "9a063d27-78b0-4494-8c3d-ef8635ddb985"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017909834161400795, "Height": 0.012497699819505215, "Left": 0.11925497651100159, "Top": 0.8221560716629028}, "Polygon": [{"X": 0.11925502866506577, "Y": 0.8221563100814819}, {"X": 0.13716481626033783, "Y": 0.8221560716629028}, {"X": 0.13716475665569305, "Y": 0.8346534967422485}, {"X": 0.11925497651100159, "Y": 0.8346537947654724}]}, "Id": "496cfa8b-2e5f-45d4-83d7-bbd8274c1f76"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0603087842464447, "Height": 0.01551893725991249, "Left": 0.14246608316898346, "Top": 0.8219107389450073}, "Polygon": [{"X": 0.14246614277362823, "Y": 0.8219115138053894}, {"X": 0.20277486741542816, "Y": 0.8219107389450073}, {"X": 0.2027747929096222, "Y": 0.8374288082122803}, {"X": 0.14246608316898346, "Y": 0.8374296426773071}]}, "Id": "2973cd28-5f83-4ae3-bf9c-2d5ccdf28d22"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "5fe6e2ca-d691-42ec-8b56-4d645f4c5141", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "5fe6e2ca-d691-42ec-8b56-4d645f4c5141", "content-type": "application/x-amz-json-1.1", "content-length": "46908", "date": "Thu, 18 Sep 2025 07:52:56 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "57ab20d2-daff-479f-90bd-35b09c275662", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:52:58 GMT", "content-type": "application/json", "content-length": "631", "connection": "keep-alive", "x-amzn-requestid": "57ab20d2-daff-479f-90bd-35b09c275662"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. This is a gate in receipt. Could be ingate. Keywords: \"Gate In\", \"Receipt\", \"Time In\", \"Truck\", \"Container\". This matches ingate. So doc_type: ingate. Output array with page 1."}}}, {"toolUse": {"toolUseId": "tooluse_rgPw8LxaRBCKUPUBWwNdLw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2238, "outputTokens": 99, "totalTokens": 2337}, "metrics": {"latencyMs": 695}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/4eb8da08_25_ingate_4.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nGate In Interchange\nIntegrated Industries -\nCHICAGO\n2750 W 35th Street\nChicago\nIL\nTIR#: 733330\nDate/Time In:\n7/23/2025 12:20:00 PM\nAcctCN: CHIMSC\nAcctCH:\nSizeCN: 45HC\nSizeCH:\nCont : MEDU9551199\nChas :\nGens :\nRemarks:\nLoad/Empty: E\nNet Weight: 0\nContainer Damages:\nChassis Damages:\nTire Information:\nSCAC: SSRT\nCarrier: SUPER STAR FREIG\nDriver: 470\nTruck#:\n7/23/2025 12:20:00 PM\nGate In Receipt\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}