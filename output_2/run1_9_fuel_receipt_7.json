{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.5179116129875183, "Height": 0.7104941010475159, "Left": 0.155848890542984, "Top": 0.17204533517360687}, "Polygon": [{"X": 0.155848890542984, "Y": 0.17204533517360687}, {"X": 0.6192569732666016, "Y": 0.17381639778614044}, {"X": 0.6737605333328247, "Y": 0.8452073931694031}, {"X": 0.2100600302219391, "Y": 0.8825394511222839}]}, "Id": "b666fe0d-2903-4e23-9c9b-63871849320e", "Relationships": [{"Type": "CHILD", "Ids": ["075a10b3-82fc-4e07-91fb-a169f8f4a860", "5318c566-e68e-4551-9bdf-75f7572bbdf1", "a5c20795-d163-49e9-8a0c-175de1a49ea0", "8f290e46-b844-4f07-acaa-84146f16cc0c", "91eb6a3c-4364-4b93-ac4c-ff955d5044ee", "29d88383-5f5f-4638-a406-932187237976", "6ad357db-fdc9-4178-b714-1555bc87c684", "cedd693d-ff20-4e0a-bab9-ecf7d0088629", "7f3ef0e9-3e38-4ed8-90ac-d0cb5e3dfcc9", "9c273145-9281-458e-b26e-85ce011de8d5", "959d8599-a005-4ee8-be40-106b52f58d0b", "3fc36434-8cc7-43f0-836a-9d46538da0e0", "6c08729c-67a6-45d6-9618-83b41161d49b", "e7b88a85-5f84-40c6-88d8-03b7a16b6617", "ff0ad489-858a-4d48-bc3d-773e32ab2ba1", "eb325783-cf3c-4076-b1a3-765e7c06adae", "7a86c541-18e6-440d-8f00-584b66afb3be", "209b35be-b411-4273-8857-0a271ffbc392", "d96d3214-3eb3-4b21-bf72-0e370765d47d", "873c638b-e08d-4d6a-9c56-55389935a524", "1492a00e-90bb-4833-ac38-73ea078feaab", "790e5edf-5481-4e48-aab4-5d8bad660ba2", "25f8e24e-43ca-4adc-a897-b0907e014169", "523450f6-2011-43c3-9cb6-d0e3837effa3", "8318a667-fb23-4912-87af-a52587c6e1de"]}]}, {"BlockType": "LINE", "Confidence": 99.9373550415039, "Text": "KWIK TRIP #1286", "Geometry": {"BoundingBox": {"Width": 0.23443806171417236, "Height": 0.02045808546245098, "Left": 0.272352933883667, "Top": 0.33005109429359436}, "Polygon": [{"X": 0.272352933883667, "Y": 0.3336541950702667}, {"X": 0.5054827332496643, "Y": 0.33005109429359436}, {"X": 0.5067909955978394, "Y": 0.3464353680610657}, {"X": 0.2736574411392212, "Y": 0.3505091667175293}]}, "Id": "075a10b3-82fc-4e07-91fb-a169f8f4a860", "Relationships": [{"Type": "CHILD", "Ids": ["dfeab5b6-f0c4-4a34-9aa2-be8f63434c81", "cfc68fc2-4461-4807-af57-9850c00d194c", "8c283a94-55e3-451c-b71a-c4c5f016d196"]}]}, {"BlockType": "LINE", "Confidence": 99.87857818603516, "Text": "485 N. BUSINESS PKWY", "Geometry": {"BoundingBox": {"Width": 0.3142130970954895, "Height": 0.02322215586900711, "Left": 0.24076944589614868, "Top": 0.34961020946502686}, "Polygon": [{"X": 0.24076944589614868, "Y": 0.3552316427230835}, {"X": 0.5536212921142578, "Y": 0.34961020946502686}, {"X": 0.5549825429916382, "Y": 0.3665536344051361}, {"X": 0.2421255111694336, "Y": 0.37283235788345337}]}, "Id": "5318c566-e68e-4551-9bdf-75f7572bbdf1", "Relationships": [{"Type": "CHILD", "Ids": ["544ed3d1-d614-4542-a38f-b0774448fc9b", "01429cd5-e07d-4e57-82ce-4eda9f4ba2f0", "f4ec044f-4e6e-4b79-872b-af79cab0d683", "155435cf-75f2-4d67-89be-3efd8dc5e291"]}]}, {"BlockType": "LINE", "Confidence": 99.28003692626953, "Text": "MARATHON, WI", "Geometry": {"BoundingBox": {"Width": 0.1914023905992508, "Height": 0.022453507408499718, "Left": 0.3037833869457245, "Top": 0.3740712106227875}, "Polygon": [{"X": 0.3037833869457245, "Y": 0.37803032994270325}, {"X": 0.4937456548213959, "Y": 0.3740712106227875}, {"X": 0.4951857626438141, "Y": 0.3921429514884949}, {"X": 0.30522021651268005, "Y": 0.39652472734451294}]}, "Id": "a5c20795-d163-49e9-8a0c-175de1a49ea0", "Relationships": [{"Type": "CHILD", "Ids": ["1b17ba51-db83-4bb4-bf75-e0de47282631", "cebb8ca8-3dc6-4ea2-b8e4-1d1fb478e423"]}]}, {"BlockType": "LINE", "Confidence": 94.98823547363281, "Text": "Invoice# 0099770", "Geometry": {"BoundingBox": {"Width": 0.27050110697746277, "Height": 0.023332009091973305, "Left": 0.24452900886535645, "Top": 0.41773882508277893}, "Polygon": [{"X": 0.24452900886535645, "Y": 0.42481228709220886}, {"X": 0.5137734413146973, "Y": 0.41773882508277893}, {"X": 0.5150300860404968, "Y": 0.43347424268722534}, {"X": 0.24578140676021576, "Y": 0.4410708248615265}]}, "Id": "8f290e46-b844-4f07-acaa-84146f16cc0c", "Relationships": [{"Type": "CHILD", "Ids": ["1730218c-288c-4453-9631-05c3b6f87c02", "2f508af9-f0e0-48f5-86c7-2f31d3f60271"]}]}, {"BlockType": "LINE", "Confidence": 98.53787994384766, "Text": "Trans # 0900-9770", "Geometry": {"BoundingBox": {"Width": 0.30524078011512756, "Height": 0.025061076506972313, "Left": 0.24200522899627686, "Top": 0.44000235199928284}, "Polygon": [{"X": 0.24200522899627686, "Y": 0.4488573372364044}, {"X": 0.5459937453269958, "Y": 0.44000235199928284}, {"X": 0.547245979309082, "Y": 0.45561978220939636}, {"X": 0.24325282871723175, "Y": 0.4650634229183197}]}, "Id": "91eb6a3c-4364-4b93-ac4c-ff955d5044ee", "Relationships": [{"Type": "CHILD", "Ids": ["ce64b47b-a00e-410a-add9-7d897526ca4c", "9fae6499-a87c-434a-9db9-1fbbe5e6fef8", "4e7acafd-fcb4-4eba-a07f-a98325bff7d8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.06473590433597565, "Height": 0.017152423039078712, "Left": 0.2456754893064499, "Top": 0.4714689254760742}, "Polygon": [{"X": 0.2456754893064499, "Y": 0.473508358001709}, {"X": 0.3092464804649353, "Y": 0.4714689254760742}, {"X": 0.31041139364242554, "Y": 0.48646703362464905}, {"X": 0.24683921039104462, "Y": 0.4886213541030884}]}, "Id": "29d88383-5f5f-4638-a406-932187237976", "Relationships": [{"Type": "CHILD", "Ids": ["546ae390-12fe-4904-9210-f43b5157187c"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "08/11/2025", "Geometry": {"BoundingBox": {"Width": 0.1562185287475586, "Height": 0.023123061284422874, "Left": 0.4082087278366089, "Top": 0.46244966983795166}, "Polygon": [{"X": 0.4082087278366089, "Y": 0.4673985242843628}, {"X": 0.562994658946991, "Y": 0.46244966983795166}, {"X": 0.5644272565841675, "Y": 0.4802808165550232}, {"X": 0.40963929891586304, "Y": 0.4855727255344391}]}, "Id": "6ad357db-fdc9-4178-b714-1555bc87c684", "Relationships": [{"Type": "CHILD", "Ids": ["fa20a3ee-839d-400f-b391-e6eb37138021"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.06588426977396011, "Height": 0.018551209941506386, "Left": 0.24619336426258087, "Top": 0.49363288283348083}, "Polygon": [{"X": 0.24619336426258087, "Y": 0.4958787262439728}, {"X": 0.310820996761322, "Y": 0.49363288283348083}, {"X": 0.3120776414871216, "Y": 0.5098121762275696}, {"X": 0.24744871258735657, "Y": 0.5121840834617615}]}, "Id": "cedd693d-ff20-4e0a-bab9-ecf7d0088629", "Relationships": [{"Type": "CHILD", "Ids": ["497dd205-5d08-4ac4-8803-3ab54f8e2fd8"]}]}, {"BlockType": "LINE", "Confidence": 99.5606460571289, "Text": "01:36 PM", "Geometry": {"BoundingBox": {"Width": 0.12544599175453186, "Height": 0.019543442875146866, "Left": 0.4090748429298401, "Top": 0.4867000877857208}, "Polygon": [{"X": 0.4090748429298401, "Y": 0.4910300374031067}, {"X": 0.5333220362663269, "Y": 0.4867000877857208}, {"X": 0.5345208048820496, "Y": 0.5016830563545227}, {"X": 0.41027218103408813, "Y": 0.5062435269355774}]}, "Id": "7f3ef0e9-3e38-4ed8-90ac-d0cb5e3dfcc9", "Relationships": [{"Type": "CHILD", "Ids": ["2a9d10b1-2b71-4b08-8020-a3c2c5619045", "f18ea4fc-48a4-44c0-a7fe-8a5a3eafbbc2"]}]}, {"BlockType": "LINE", "Confidence": 99.93058013916016, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.317791223526001, "Height": 0.03329800069332123, "Left": 0.25066372752189636, "Top": 0.53013014793396}, "Polygon": [{"X": 0.25066372752189636, "Y": 0.5428981184959412}, {"X": 0.566868245601654, "Y": 0.53013014793396}, {"X": 0.568454921245575, "Y": 0.5498829483985901}, {"X": 0.2522445023059845, "Y": 0.5634281635284424}]}, "Id": "9c273145-9281-458e-b26e-85ce011de8d5", "Relationships": [{"Type": "CHILD", "Ids": ["d9fe72f3-1e2b-47f9-9087-052a5c633e51", "62ca5304-8114-402b-8096-ec6c6d959860", "bb2055cd-b972-4609-9b58-dadd3f753a74"]}]}, {"BlockType": "LINE", "Confidence": 99.32398223876953, "Text": "01", "Geometry": {"BoundingBox": {"Width": 0.02861510030925274, "Height": 0.01618233323097229, "Left": 0.26909199357032776, "Top": 0.5655109882354736}, "Polygon": [{"X": 0.26909199357032776, "Y": 0.5667002201080322}, {"X": 0.29654955863952637, "Y": 0.5655109882354736}, {"X": 0.29770711064338684, "Y": 0.5804545879364014}, {"X": 0.27024906873703003, "Y": 0.5816932916641235}]}, "Id": "959d8599-a005-4ee8-be40-106b52f58d0b", "Relationships": [{"Type": "CHILD", "Ids": ["f000c589-993d-4c43-affd-aa1fd9be5348"]}]}, {"BlockType": "LINE", "Confidence": 98.87047576904297, "Text": "120.611 3.429", "Geometry": {"BoundingBox": {"Width": 0.21915876865386963, "Height": 0.024784503504633904, "Left": 0.3498867452144623, "Top": 0.5535644292831421}, "Polygon": [{"X": 0.3498867452144623, "Y": 0.5629996657371521}, {"X": 0.5678452849388123, "Y": 0.5535644292831421}, {"X": 0.5690455436706543, "Y": 0.5685081481933594}, {"X": 0.3510843515396118, "Y": 0.5783489346504211}]}, "Id": "3fc36434-8cc7-43f0-836a-9d46538da0e0", "Relationships": [{"Type": "CHILD", "Ids": ["cb05b586-f3e8-467f-adde-8870618e1498", "adc5d735-f32c-4322-8978-7170cce6a97c"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.11258604377508163, "Height": 0.020808033645153046, "Left": 0.2563735544681549, "Top": 0.6078961491584778}, "Polygon": [{"X": 0.2563735544681549, "Y": 0.6133341789245605}, {"X": 0.3677741289138794, "Y": 0.6078961491584778}, {"X": 0.36895960569381714, "Y": 0.6230608224868774}, {"X": 0.25755706429481506, "Y": 0.6287041902542114}]}, "Id": "6c08729c-67a6-45d6-9618-83b41161d49b", "Relationships": [{"Type": "CHILD", "Ids": ["85e28d75-ad94-4734-acd0-e175cf17d859"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.09234723448753357, "Height": 0.01980678364634514, "Left": 0.47891855239868164, "Top": 0.5983253717422485}, "Polygon": [{"X": 0.47891855239868164, "Y": 0.6027772426605225}, {"X": 0.570046603679657, "Y": 0.5983253717422485}, {"X": 0.5712657570838928, "Y": 0.6135079264640808}, {"X": 0.48013684153556824, "Y": 0.6181321740150452}]}, "Id": "e7b88a85-5f84-40c6-88d8-03b7a16b6617", "Relationships": [{"Type": "CHILD", "Ids": ["30b32416-c415-4f63-aa69-47918e4b39cb"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.06407122313976288, "Height": 0.017800550907850266, "Left": 0.2585572898387909, "Top": 0.6336776614189148}, "Polygon": [{"X": 0.2585572898387909, "Y": 0.6369287371635437}, {"X": 0.3215070366859436, "Y": 0.6336776614189148}, {"X": 0.32262852787971497, "Y": 0.6481173038482666}, {"X": 0.2596776783466339, "Y": 0.6514782309532166}]}, "Id": "ff0ad489-858a-4d48-bc3d-773e32ab2ba1", "Relationships": [{"Type": "CHILD", "Ids": ["4a6c70b0-f975-4120-99b7-e9160fb5a687"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "413.58", "Geometry": {"BoundingBox": {"Width": 0.09202641993761063, "Height": 0.019810494035482407, "Left": 0.48059502243995667, "Top": 0.6205868124961853}, "Polygon": [{"X": 0.48059502243995667, "Y": 0.6252754330635071}, {"X": 0.5714207887649536, "Y": 0.6205868124961853}, {"X": 0.5726214647293091, "Y": 0.6355393528938293}, {"X": 0.4817948341369629, "Y": 0.640397310256958}]}, "Id": "eb325783-cf3c-4076-b1a3-765e7c06adae", "Relationships": [{"Type": "CHILD", "Ids": ["37b9b045-f853-401a-8ae1-bb1636461ecf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.08053364604711533, "Height": 0.019385183230042458, "Left": 0.2609926760196686, "Top": 0.6781381368637085}, "Polygon": [{"X": 0.2609926760196686, "Y": 0.6826732158660889}, {"X": 0.34038156270980835, "Y": 0.6781381368637085}, {"X": 0.3415263295173645, "Y": 0.6928467154502869}, {"X": 0.2621360421180725, "Y": 0.697523295879364}]}, "Id": "7a86c541-18e6-440d-8f00-584b66afb3be", "Relationships": [{"Type": "CHILD", "Ids": ["80654bc9-5399-4bb4-a632-5500cd27ff90"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "413.58", "Geometry": {"BoundingBox": {"Width": 0.0921490415930748, "Height": 0.021437181159853935, "Left": 0.46742796897888184, "Top": 0.6650109887123108}, "Polygon": [{"X": 0.46742796897888184, "Y": 0.6701937317848206}, {"X": 0.5582892298698425, "Y": 0.6650109887123108}, {"X": 0.5595769882202148, "Y": 0.681083619594574}, {"X": 0.46871477365493774, "Y": 0.6864481568336487}]}, "Id": "209b35be-b411-4273-8857-0a271ffbc392", "Relationships": [{"Type": "CHILD", "Ids": ["cc000d85-2540-432c-80da-149dbcb0c4c8"]}]}, {"BlockType": "LINE", "Confidence": 99.875244140625, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.2954259216785431, "Height": 0.03573299199342728, "Left": 0.26638686656951904, "Top": 0.7092283964157104}, "Polygon": [{"X": 0.26638686656951904, "Y": 0.7276186347007751}, {"X": 0.5604726076126099, "Y": 0.7092283964157104}, {"X": 0.5618127584457397, "Y": 0.7259581685066223}, {"X": 0.2677225172519684, "Y": 0.7449613809585571}]}, "Id": "d96d3214-3eb3-4b21-bf72-0e370765d47d", "Relationships": [{"Type": "CHILD", "Ids": ["2ef6d9d5-6890-4a64-bc6b-3adc26f51e29", "7be722a0-bdb3-4079-8d37-f3fcac3a0bdf", "7252f57c-14ec-489e-ba6c-36f02ef31d25"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.1409192532300949, "Height": 0.024143047630786896, "Left": 0.26893672347068787, "Top": 0.7413554191589355}, "Polygon": [{"X": 0.26893672347068787, "Y": 0.7504798173904419}, {"X": 0.****************, "Y": 0.7413554191589355}, {"X": 0.****************, "Y": 0.7561216354370117}, {"X": 0.27009350061416626, "Y": 0.7654984593391418}]}, "Id": "873c638b-e08d-4d6a-9c56-55389935a524", "Relationships": [{"Type": "CHILD", "Ids": ["c8df2fd5-c145-42c3-adbc-a88adce0b62f", "07cb762e-dc77-414e-8e49-c5ec762e3633"]}]}, {"BlockType": "LINE", "Confidence": 93.60032653808594, "Text": "1591", "Geometry": {"BoundingBox": {"Width": 0.05331262946128845, "Height": 0.018192576244473457, "Left": 0.****************, "Top": 0.7542839646339417}, "Polygon": [{"X": 0.****************, "Y": 0.7578286528587341}, {"X": 0.543390691280365, "Y": 0.7542839646339417}, {"X": 0.****************, "Y": 0.7688374519348145}, {"X": 0.*****************, "Y": 0.7724765539169312}]}, "Id": "1492a00e-90bb-4833-ac38-73ea078feaab", "Relationships": [{"Type": "CHILD", "Ids": ["bee92a6d-ebe7-4e85-8fae-c970588bb6b7"]}]}, {"BlockType": "LINE", "Confidence": 99.86248779296875, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.0953131765127182, "Height": 0.022371068596839905, "Left": 0.2727143466472626, "Top": 0.7873691916465759}, "Polygon": [{"X": 0.2727143466472626, "Y": 0.794005274772644}, {"X": 0.3668138384819031, "Y": 0.7873691916465759}, {"X": 0.3680275082588196, "Y": 0.8029259443283081}, {"X": 0.27392634749412537, "Y": 0.8097403049468994}]}, "Id": "790e5edf-5481-4e48-aab4-5d8bad660ba2", "Relationships": [{"Type": "CHILD", "Ids": ["a6f56f76-b104-4bb0-8f31-e5f6f153eaaf", "12ccddc8-e6e7-4760-8c02-97f2de9fb6ca"]}]}, {"BlockType": "LINE", "Confidence": 99.76403045654297, "Text": "P24671", "Geometry": {"BoundingBox": {"Width": 0.08693446218967438, "Height": 0.0212814062833786, "Left": 0.48928913474082947, "Top": 0.7731947302818298}, "Polygon": [{"X": 0.48928913474082947, "Y": 0.7792457342147827}, {"X": 0.5750149488449097, "Y": 0.7731947302818298}, {"X": 0.576223611831665, "Y": 0.7882638573646545}, {"X": 0.49049699306488037, "Y": 0.7944761514663696}]}, "Id": "25f8e24e-43ca-4adc-a897-b0907e014169", "Relationships": [{"Type": "CHILD", "Ids": ["626a5806-6f03-4e2a-a87a-c4e2fdfb243d"]}]}, {"BlockType": "LINE", "Confidence": 98.87416076660156, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.27569860219955444, "Height": 0.037133101373910904, "Left": 0.2749030590057373, "Top": 0.7960060834884644}, "Polygon": [{"X": 0.2749030590057373, "Y": 0.816085934638977}, {"X": 0.5492838025093079, "Y": 0.7960060834884644}, {"X": 0.5506016612052917, "Y": 0.8124959468841553}, {"X": 0.27621668577194214, "Y": 0.833139181137085}]}, "Id": "523450f6-2011-43c3-9cb6-d0e3837effa3", "Relationships": [{"Type": "CHILD", "Ids": ["6043cafe-feb2-4543-a04d-bc491814faf3", "d1f93a83-aeb5-4a5d-8ad2-851bbe5c35ba"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "AUTH REF#: P24671", "Geometry": {"BoundingBox": {"Width": 0.2565692365169525, "Height": 0.03830242156982422, "Left": 0.2920001149177551, "Top": 0.8170053958892822}, "Polygon": [{"X": 0.2920001149177551, "Y": 0.8363317251205444}, {"X": 0.5471002459526062, "Y": 0.8170053958892822}, {"X": 0.5485693216323853, "Y": 0.8353973031044006}, {"X": 0.2934649586677551, "Y": 0.8553078174591064}]}, "Id": "8318a667-fb23-4912-87af-a52587c6e1de", "Relationships": [{"Type": "CHILD", "Ids": ["93dbb60d-f18f-4d77-85da-05225aa72ae4", "d5d5f2b1-8d2d-46da-ba23-dd6abdbd75ae", "fcd72b08-6f46-4d6d-bdfd-ff08ba6f2775"]}]}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06310347467660904, "Height": 0.01566913165152073, "Left": 0.2725200951099396, "Top": 0.3348400294780731}, "Polygon": [{"X": 0.2725200951099396, "Y": 0.3358137607574463}, {"X": 0.33448511362075806, "Y": 0.3348400294780731}, {"X": 0.335623562335968, "Y": 0.34942635893821716}, {"X": 0.2736574411392212, "Y": 0.3505091667175293}]}, "Id": "dfeab5b6-f0c4-4a34-9aa2-be8f63434c81"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06354018300771713, "Height": 0.015378065407276154, "Left": 0.3500605523586273, "Top": 0.33235400915145874}, "Polygon": [{"X": 0.3500605523586273, "Y": 0.33332517743110657}, {"X": 0.41247302293777466, "Y": 0.33235400915145874}, {"X": 0.41360074281692505, "Y": 0.34665217995643616}, {"X": 0.3511873483657837, "Y": 0.3477320671081543}]}, "Id": "cfc68fc2-4461-4807-af57-9850c00d194c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "#1286", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07828468084335327, "Height": 0.015444801189005375, "Left": 0.428325355052948, "Top": 0.33005109429359436}, "Polygon": [{"X": 0.428325355052948, "Y": 0.3312435746192932}, {"X": 0.5054827332496643, "Y": 0.33005109429359436}, {"X": 0.5066100358963013, "Y": 0.34416916966438293}, {"X": 0.4294518530368805, "Y": 0.34549587965011597}]}, "Id": "8c283a94-55e3-451c-b71a-c4c5f016d196"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "485", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04777342453598976, "Height": 0.014918376691639423, "Left": 0.24104227125644684, "Top": 0.3579139709472656}, "Polygon": [{"X": 0.24104227125644684, "Y": 0.3587726354598999}, {"X": 0.2877316176891327, "Y": 0.3579139709472656}, {"X": 0.2888156771659851, "Y": 0.37189534306526184}, {"X": 0.2421255111694336, "Y": 0.37283235788345337}]}, "Id": "544ed3d1-d614-4542-a38f-b0774448fc9b"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027259668335318565, "Height": 0.014402158558368683, "Left": 0.30490604043006897, "Top": 0.35665786266326904}, "Polygon": [{"X": 0.30490604043006897, "Y": 0.3571378290653229}, {"X": 0.3310832679271698, "Y": 0.35665786266326904}, {"X": 0.3321657180786133, "Y": 0.370536208152771}, {"X": 0.305988073348999, "Y": 0.37106001377105713}]}, "Id": "01429cd5-e07d-4e57-82ce-4eda9f4ba2f0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "BUSINESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12485526502132416, "Height": 0.017663296312093735, "Left": 0.352632999420166, "Top": 0.35221242904663086}, "Polygon": [{"X": 0.352632999420166, "Y": 0.35445255041122437}, {"X": 0.4762800931930542, "Y": 0.35221242904663086}, {"X": 0.47748827934265137, "Y": 0.36740490794181824}, {"X": 0.3538394570350647, "Y": 0.3698757290840149}]}, "Id": "f4ec044f-4e6e-4b79-872b-af79cab0d683"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "PKWY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06170138344168663, "Height": 0.016256367787718773, "Left": 0.49312955141067505, "Top": 0.34961020946502686}, "Polygon": [{"X": 0.49312955141067505, "Y": 0.3506971299648285}, {"X": 0.5536212921142578, "Y": 0.34961020946502686}, {"X": 0.5548309087753296, "Y": 0.3646667003631592}, {"X": 0.49433860182762146, "Y": 0.3658665716648102}]}, "Id": "155435cf-75f2-4d67-89be-3efd8dc5e291"}, {"BlockType": "WORD", "Confidence": 98.60969543457031, "Text": "MARATHON,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14052848517894745, "Height": 0.020841289311647415, "Left": 0.30382707715034485, "Top": 0.37568342685699463}, "Polygon": [{"X": 0.30382707715034485, "Y": 0.3785926103591919}, {"X": 0.44295990467071533, "Y": 0.37568342685699463}, {"X": 0.4443555474281311, "Y": 0.3933154046535492}, {"X": 0.30522021651268005, "Y": 0.39652472734451294}]}, "Id": "1b17ba51-db83-4bb4-bf75-e0de47282631"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032490573823451996, "Height": 0.014954978600144386, "Left": 0.46239036321640015, "Top": 0.3740712106227875}, "Polygon": [{"X": 0.46239036321640015, "Y": 0.37472471594810486}, {"X": 0.4937456548213959, "Y": 0.3740712106227875}, {"X": 0.49488094449043274, "Y": 0.38831770420074463}, {"X": 0.46352532505989075, "Y": 0.3890261948108673}]}, "Id": "cebb8ca8-3dc6-4ea2-b8e4-1d1fb478e423"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1294076144695282, "Height": 0.019625317305326462, "Left": 0.24452900886535645, "Top": 0.4214455187320709}, "Polygon": [{"X": 0.24452900886535645, "Y": 0.42481228709220886}, {"X": 0.3726818263530731, "Y": 0.4214455187320709}, {"X": 0.37393662333488464, "Y": 0.43745505809783936}, {"X": 0.24578140676021576, "Y": 0.4410708248615265}]}, "Id": "1730218c-288c-4453-9631-05c3b6f87c02"}, {"BlockType": "WORD", "Confidence": 90.40438079833984, "Text": "0099770", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11063180863857269, "Height": 0.018013345077633858, "Left": 0.40437108278274536, "Top": 0.4182029962539673}, "Polygon": [{"X": 0.40437108278274536, "Y": 0.4210844039916992}, {"X": 0.5138105154037476, "Y": 0.4182029962539673}, {"X": 0.5150029063224792, "Y": 0.43313318490982056}, {"X": 0.4055621325969696, "Y": 0.4362163543701172}]}, "Id": "2f508af9-f0e0-48f5-86c7-2f31d3f60271"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08294067531824112, "Height": 0.017728252336382866, "Left": 0.24207203090190887, "Top": 0.4473351836204529}, "Polygon": [{"X": 0.24207203090190887, "Y": 0.4497252106666565}, {"X": 0.32383039593696594, "Y": 0.4473351836204529}, {"X": 0.3250127136707306, "Y": 0.46252351999282837}, {"X": 0.24325282871723175, "Y": 0.4650634229183197}]}, "Id": "ce64b47b-a00e-410a-add9-7d897526ca4c"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018792616203427315, "Height": 0.015643998980522156, "Left": 0.35655564069747925, "Top": 0.44574201107025146}, "Polygon": [{"X": 0.35655564069747925, "Y": 0.44625651836395264}, {"X": 0.37416502833366394, "Y": 0.44574201107025146}, {"X": 0.3753482401371002, "Y": 0.4608392119407654}, {"X": 0.35773858428001404, "Y": 0.4613859951496124}]}, "Id": "9fae6499-a87c-434a-9db9-1fbbe5e6fef8"}, {"BlockType": "WORD", "Confidence": 95.79022216796875, "Text": "0900-9770", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1410032957792282, "Height": 0.01956305094063282, "Left": 0.4062114357948303, "Top": 0.44000235199928284}, "Polygon": [{"X": 0.4062114357948303, "Y": 0.4440741240978241}, {"X": 0.5459937453269958, "Y": 0.44000235199928284}, {"X": 0.5472147464752197, "Y": 0.4552297294139862}, {"X": 0.4074307680130005, "Y": 0.4595654010772705}]}, "Id": "4e7acafd-fcb4-4eba-a07f-a98325bff7d8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06473590433597565, "Height": 0.017152423039078712, "Left": 0.2456754893064499, "Top": 0.4714689254760742}, "Polygon": [{"X": 0.2456754893064499, "Y": 0.473508358001709}, {"X": 0.3092464804649353, "Y": 0.4714689254760742}, {"X": 0.31041139364242554, "Y": 0.48646703362464905}, {"X": 0.24683921039104462, "Y": 0.4886213541030884}]}, "Id": "546ae390-12fe-4904-9210-f43b5157187c"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "08/11/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1562185287475586, "Height": 0.023123061284422874, "Left": 0.4082087278366089, "Top": 0.46244966983795166}, "Polygon": [{"X": 0.4082087278366089, "Y": 0.4673985242843628}, {"X": 0.562994658946991, "Y": 0.46244966983795166}, {"X": 0.5644272565841675, "Y": 0.4802808165550232}, {"X": 0.40963929891586304, "Y": 0.4855727255344391}]}, "Id": "fa20a3ee-839d-400f-b391-e6eb37138021"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06588426977396011, "Height": 0.018551209941506386, "Left": 0.24619336426258087, "Top": 0.49363288283348083}, "Polygon": [{"X": 0.24619336426258087, "Y": 0.4958787262439728}, {"X": 0.310820996761322, "Y": 0.49363288283348083}, {"X": 0.3120776414871216, "Y": 0.5098121762275696}, {"X": 0.24744871258735657, "Y": 0.5121840834617615}]}, "Id": "497dd205-5d08-4ac4-8803-3ab54f8e2fd8"}, {"BlockType": "WORD", "Confidence": 99.24824523925781, "Text": "01:36", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07884778827428818, "Height": 0.01786092296242714, "Left": 0.40907949209213257, "Top": 0.48838260769844055}, "Polygon": [{"X": 0.40907949209213257, "Y": 0.4910893738269806}, {"X": 0.48673364520072937, "Y": 0.48838260769844055}, {"X": 0.48792728781700134, "Y": 0.5033932328224182}, {"X": 0.41027218103408813, "Y": 0.5062435269355774}]}, "Id": "2a9d10b1-2b71-4b08-8020-a3c2c5619045"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030883321538567543, "Height": 0.01590903289616108, "Left": 0.503624439239502, "Top": 0.4867000877857208}, "Polygon": [{"X": 0.503624439239502, "Y": 0.4877350330352783}, {"X": 0.5333220362663269, "Y": 0.4867000877857208}, {"X": 0.5345077514648438, "Y": 0.5015196800231934}, {"X": 0.5048098564147949, "Y": 0.502609133720398}]}, "Id": "f18ea4fc-48a4-44c0-a7fe-8a5a3eafbbc2"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06484366953372955, "Height": 0.021935883909463882, "Left": 0.25075310468673706, "Top": 0.5414922833442688}, "Polygon": [{"X": 0.25075310468673706, "Y": 0.5440590977668762}, {"X": 0.314103901386261, "Y": 0.5414922833442688}, {"X": 0.3155967891216278, "Y": 0.5607144236564636}, {"X": 0.2522445023059845, "Y": 0.5634281635284424}]}, "Id": "d9fe72f3-1e2b-47f9-9087-052a5c633e51"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1267758160829544, "Height": 0.02499541826546192, "Left": 0.3325636088848114, "Top": 0.5345438718795776}, "Polygon": [{"X": 0.3325636088848114, "Y": 0.5396002531051636}, {"X": 0.45778459310531616, "Y": 0.5345438718795776}, {"X": 0.4593394100666046, "Y": 0.5541809797286987}, {"X": 0.3341160714626312, "Y": 0.5595393180847168}]}, "Id": "62ca5304-8114-402b-8096-ec6c6d959860"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07767537236213684, "Height": 0.01901659555733204, "Left": 0.4904605448246002, "Top": 0.53013014793396}, "Polygon": [{"X": 0.4904605448246002, "Y": 0.5332154035568237}, {"X": 0.566868245601654, "Y": 0.53013014793396}, {"X": 0.5681359171867371, "Y": 0.5459114909172058}, {"X": 0.49172747135162354, "Y": 0.5491467714309692}]}, "Id": "bb2055cd-b972-4609-9b58-dadd3f753a74"}, {"BlockType": "WORD", "Confidence": 99.32398223876953, "Text": "01", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02861510030925274, "Height": 0.01618233323097229, "Left": 0.26909199357032776, "Top": 0.5655109882354736}, "Polygon": [{"X": 0.26909199357032776, "Y": 0.5667002201080322}, {"X": 0.29654955863952637, "Y": 0.5655109882354736}, {"X": 0.29770711064338684, "Y": 0.5804545879364014}, {"X": 0.27024906873703003, "Y": 0.5816932916641235}]}, "Id": "f000c589-993d-4c43-affd-aa1fd9be5348"}, {"BlockType": "WORD", "Confidence": 98.04926300048828, "Text": "120.611", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10690240561962128, "Height": 0.01933751255273819, "Left": 0.3498867452144623, "Top": 0.5584218502044678}, "Polygon": [{"X": 0.3498867452144623, "Y": 0.5629996657371521}, {"X": 0.45563608407974243, "Y": 0.5584218502044678}, {"X": 0.45678913593292236, "Y": 0.5729923844337463}, {"X": 0.3510383367538452, "Y": 0.5777593851089478}]}, "Id": "cb05b586-f3e8-467f-adde-8870618e1498"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "3.429", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0767444595694542, "Height": 0.018218940123915672, "Left": 0.4923010468482971, "Top": 0.5537005066871643}, "Polygon": [{"X": 0.4923010468482971, "Y": 0.5569725036621094}, {"X": 0.5678561925888062, "Y": 0.5537005066871643}, {"X": 0.5690455436706543, "Y": 0.5685081481933594}, {"X": 0.49348965287208557, "Y": 0.5719194412231445}]}, "Id": "adc5d735-f32c-4322-8978-7170cce6a97c"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11258604377508163, "Height": 0.020808033645153046, "Left": 0.2563735544681549, "Top": 0.6078961491584778}, "Polygon": [{"X": 0.2563735544681549, "Y": 0.6133341789245605}, {"X": 0.3677741289138794, "Y": 0.6078961491584778}, {"X": 0.36895960569381714, "Y": 0.6230608224868774}, {"X": 0.25755706429481506, "Y": 0.6287041902542114}]}, "Id": "85e28d75-ad94-4734-acd0-e175cf17d859"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09234723448753357, "Height": 0.01980678364634514, "Left": 0.47891855239868164, "Top": 0.5983253717422485}, "Polygon": [{"X": 0.47891855239868164, "Y": 0.6027772426605225}, {"X": 0.570046603679657, "Y": 0.5983253717422485}, {"X": 0.5712657570838928, "Y": 0.6135079264640808}, {"X": 0.48013684153556824, "Y": 0.6181321740150452}]}, "Id": "30b32416-c415-4f63-aa69-47918e4b39cb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06407122313976288, "Height": 0.017800550907850266, "Left": 0.2585572898387909, "Top": 0.6336776614189148}, "Polygon": [{"X": 0.2585572898387909, "Y": 0.6369287371635437}, {"X": 0.3215070366859436, "Y": 0.6336776614189148}, {"X": 0.32262852787971497, "Y": 0.6481173038482666}, {"X": 0.2596776783466339, "Y": 0.6514782309532166}]}, "Id": "4a6c70b0-f975-4120-99b7-e9160fb5a687"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "413.58", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09202641993761063, "Height": 0.019810494035482407, "Left": 0.48059502243995667, "Top": 0.6205868124961853}, "Polygon": [{"X": 0.48059502243995667, "Y": 0.6252754330635071}, {"X": 0.5714207887649536, "Y": 0.6205868124961853}, {"X": 0.5726214647293091, "Y": 0.6355393528938293}, {"X": 0.4817948341369629, "Y": 0.640397310256958}]}, "Id": "37b9b045-f853-401a-8ae1-bb1636461ecf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08053364604711533, "Height": 0.019385183230042458, "Left": 0.2609926760196686, "Top": 0.6781381368637085}, "Polygon": [{"X": 0.2609926760196686, "Y": 0.6826732158660889}, {"X": 0.34038156270980835, "Y": 0.6781381368637085}, {"X": 0.3415263295173645, "Y": 0.6928467154502869}, {"X": 0.2621360421180725, "Y": 0.697523295879364}]}, "Id": "80654bc9-5399-4bb4-a632-5500cd27ff90"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "413.58", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0921490415930748, "Height": 0.021437181159853935, "Left": 0.46742796897888184, "Top": 0.6650109887123108}, "Polygon": [{"X": 0.46742796897888184, "Y": 0.6701937317848206}, {"X": 0.5582892298698425, "Y": 0.6650109887123108}, {"X": 0.5595769882202148, "Y": 0.681083619594574}, {"X": 0.46871477365493774, "Y": 0.6864481568336487}]}, "Id": "cc000d85-2540-432c-80da-149dbcb0c4c8"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12626343965530396, "Height": 0.024910910055041313, "Left": 0.26640602946281433, "Top": 0.7200504541397095}, "Polygon": [{"X": 0.26640602946281433, "Y": 0.7278674840927124}, {"X": 0.3913506269454956, "Y": 0.7200504541397095}, {"X": 0.3926694691181183, "Y": 0.7368876934051514}, {"X": 0.2677225172519684, "Y": 0.7449613809585571}]}, "Id": "2ef6d9d5-6890-4a64-bc6b-3adc26f51e29"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06254015117883682, "Height": 0.01861685700714588, "Left": 0.40712323784828186, "Top": 0.7149981260299683}, "Polygon": [{"X": 0.40712323784828186, "Y": 0.7188365459442139}, {"X": 0.46850258111953735, "Y": 0.7149981260299683}, {"X": 0.4696633815765381, "Y": 0.7296656966209412}, {"X": 0.4082832634449005, "Y": 0.733614981174469}]}, "Id": "7be722a0-bdb3-4079-8d37-f3fcac3a0bdf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0766325443983078, "Height": 0.020685533061623573, "Left": 0.****************, "Top": 0.7092283964157104}, "Polygon": [{"X": 0.****************, "Y": 0.713941216468811}, {"X": 0.5604726076126099, "Y": 0.7092283964157104}, {"X": 0.5617402195930481, "Y": 0.725052535533905}, {"X": 0.****************, "Y": 0.7299138903617859}]}, "Id": "7252f57c-14ec-489e-ba6c-36f02ef31d25"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0798720121383667, "Height": 0.01991235837340355, "Left": 0.26893672347068787, "Top": 0.7453396320343018}, "Polygon": [{"X": 0.26893672347068787, "Y": 0.7504798173904419}, {"X": 0.3476696014404297, "Y": 0.7453396320343018}, {"X": 0.34880873560905457, "Y": 0.7599719762802124}, {"X": 0.2700745165348053, "Y": 0.7652519941329956}]}, "Id": "c8df2fd5-c145-42c3-adbc-a88adce0b62f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04455427825450897, "Height": 0.017638271674513817, "Left": 0.3653016984462738, "Top": 0.7413950562477112}, "Polygon": [{"X": 0.3653016984462738, "Y": 0.7442285418510437}, {"X": 0.*****************, "Y": 0.7413950562477112}, {"X": 0.****************, "Y": 0.7561216354370117}, {"X": 0.3664569854736328, "Y": 0.7590333223342896}]}, "Id": "07cb762e-dc77-414e-8e49-c5ec762e3633"}, {"BlockType": "WORD", "Confidence": 93.60032653808594, "Text": "1591", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05331262946128845, "Height": 0.018192576244473457, "Left": 0.****************, "Top": 0.7542839646339417}, "Polygon": [{"X": 0.****************, "Y": 0.7578286528587341}, {"X": 0.543390691280365, "Y": 0.7542839646339417}, {"X": 0.****************, "Y": 0.7688374519348145}, {"X": 0.*****************, "Y": 0.7724765539169312}]}, "Id": "bee92a6d-ebe7-4e85-8fae-c970588bb6b7"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06306250393390656, "Height": 0.019730284810066223, "Left": 0.2727429270744324, "Top": 0.7900099754333496}, "Polygon": [{"X": 0.2727429270744324, "Y": 0.7943764925003052}, {"X": 0.3346208930015564, "Y": 0.7900099754333496}, {"X": 0.3358054459095001, "Y": 0.8052593469619751}, {"X": 0.27392634749412537, "Y": 0.8097403049468994}]}, "Id": "a6f56f76-b104-4bb0-8f31-e5f6f153eaaf"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017892273142933846, "Height": 0.015785198658704758, "Left": 0.35005855560302734, "Top": 0.7873691916465759}, "Polygon": [{"X": 0.35005855560302734, "Y": 0.788550853729248}, {"X": 0.3668138384819031, "Y": 0.7873691916465759}, {"X": 0.36795082688331604, "Y": 0.8019430637359619}, {"X": 0.3511953055858612, "Y": 0.8031544089317322}]}, "Id": "12ccddc8-e6e7-4760-8c02-97f2de9fb6ca"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "P24671", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08693446218967438, "Height": 0.0212814062833786, "Left": 0.48928913474082947, "Top": 0.7731947302818298}, "Polygon": [{"X": 0.48928913474082947, "Y": 0.7792457342147827}, {"X": 0.5750149488449097, "Y": 0.7731947302818298}, {"X": 0.576223611831665, "Y": 0.7882638573646545}, {"X": 0.49049699306488037, "Y": 0.7944761514663696}]}, "Id": "626a5806-6f03-4e2a-a87a-c4e2fdfb243d"}, {"BlockType": "WORD", "Confidence": 97.75808715820312, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13565437495708466, "Height": 0.026884444057941437, "Left": 0.2749030590057373, "Top": 0.8062547445297241}, "Polygon": [{"X": 0.2749030590057373, "Y": 0.816085934638977}, {"X": 0.40924131870269775, "Y": 0.8062547445297241}, {"X": 0.41055741906166077, "Y": 0.8230320811271667}, {"X": 0.27621668577194214, "Y": 0.833139181137085}]}, "Id": "6043cafe-feb2-4543-a04d-bc491814faf3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12022663652896881, "Height": 0.023051802068948746, "Left": 0.43020927906036377, "Top": 0.7962996959686279}, "Polygon": [{"X": 0.43020927906036377, "Y": 0.8050199151039124}, {"X": 0.5493072867393494, "Y": 0.7962996959686279}, {"X": 0.5504359006881714, "Y": 0.8104218244552612}, {"X": 0.4313366711139679, "Y": 0.8193514943122864}]}, "Id": "d1f93a83-aeb5-4a5d-8ad2-851bbe5c35ba"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06379390507936478, "Height": 0.022296803072094917, "Left": 0.29210972785949707, "Top": 0.8330110311508179}, "Polygon": [{"X": 0.29210972785949707, "Y": 0.8377519845962524}, {"X": 0.3545472025871277, "Y": 0.8330110311508179}, {"X": 0.35590365529060364, "Y": 0.8504346013069153}, {"X": 0.2934649586677551, "Y": 0.8553078174591064}]}, "Id": "93dbb60d-f18f-4d77-85da-05225aa72ae4"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07209411263465881, "Height": 0.020182017236948013, "Left": 0.37069129943847656, "Top": 0.8252372145652771}, "Polygon": [{"X": 0.37069129943847656, "Y": 0.8306135535240173}, {"X": 0.4416292607784271, "Y": 0.8252372145652771}, {"X": 0.4427854120731354, "Y": 0.8399149179458618}, {"X": 0.371846467256546, "Y": 0.8454192280769348}]}, "Id": "d5d5f2b1-8d2d-46da-ba23-dd6abdbd75ae"}, {"BlockType": "WORD", "Confidence": 99.67215728759766, "Text": "P24671", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08643611520528793, "Height": 0.021023854613304138, "Left": 0.46181511878967285, "Top": 0.8170053958892822}, "Polygon": [{"X": 0.46181511878967285, "Y": 0.8234665989875793}, {"X": 0.5471002459526062, "Y": 0.8170053958892822}, {"X": 0.5482512712478638, "Y": 0.831415057182312}, {"X": 0.46296530961990356, "Y": 0.8380292654037476}]}, "Id": "fcd72b08-6f46-4d6d-bdfd-ff08ba6f2775"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "57614ff0-3a33-4d53-9d64-a519a3dae26d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "57614ff0-3a33-4d53-9d64-a519a3dae26d", "content-type": "application/x-amz-json-1.1", "content-length": "37097", "date": "Thu, 18 Sep 2025 08:51:24 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "0783afc8-5aa2-44e5-a2ed-c6f68eeb5680", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:27 GMT", "content-type": "application/json", "content-length": "1056", "connection": "keep-alive", "x-amzn-requestid": "0783afc8-5aa2-44e5-a2ed-c6f68eeb5680"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify page1. The content: \"KWIK TRIP #1286\", \"Invoice# 0099770\". So it's an invoice. It's a fuel receipt? It says Pump Quantity Price, product DSL2, amount 413.58. That looks like a fuel receipt, but also invoice? The keywords: Invoice#, Pump Quantity Price, Product, etc. This looks like a fuel receipt. It's a pump receipt. The system says fuel_receipt doc_type: keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. This has Pump Quantity Price. Also product DSL2. It's a fuel receipt. But also has Invoice#. But the presence of pump, quantity, price suggests a fuel receipt. The doc_type is fuel_receipt. Let's classify."}}}, {"toolUse": {"toolUseId": "tooluse_MBYAnnxYSdaB8LBC-sOZNw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2239, "outputTokens": 201, "totalTokens": 2440}, "metrics": {"latencyMs": 1023}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/6f84522b_9_fuel_receipt_7.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP #1286\n485 N. BUSINESS PKWY\nMARATHON, WI\nInvoice# 0099770\nTrans # 0900-9770\nDate\n08/11/2025\nTime\n01:36 PM\nPump Quantity Price\n01\n120.611 3.429\nProduct\nAmount\nDSL2\n413.58\nTOTAL\n413.58\nNBS/KWIK TRIP FLEET\nCARD# ICR\n1591\nAUTH #\nP24671\nRESPONSE: APPROVED\nAUTH REF#: P24671\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}