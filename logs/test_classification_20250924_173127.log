2025-09-24 17:31:27,352 - INFO - Logging initialized. Log file: logs/test_classification_20250924_173127.log
2025-09-24 17:31:27,353 - INFO - 📁 Found 16 files to process
2025-09-24 17:31:27,353 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:31:27,353 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-24 17:31:27,353 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-24 17:31:27,353 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-24 17:31:27,353 - INFO - ⬆️ [17:31:27] Uploading: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:31:29,397 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg -> s3://document-extraction-logistically/temp/de920c3d_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:31:29,398 - INFO - 🔍 [17:31:29] Starting classification: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:31:29,399 - INFO - ⬆️ [17:31:29] Uploading: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:29,405 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:29,426 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:29,435 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/de920c3d_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:31:29,436 - INFO - Processing image from S3...
2025-09-24 17:31:30,823 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf -> s3://document-extraction-logistically/temp/beb4a23e_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:30,823 - INFO - 🔍 [17:31:30] Starting classification: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:30,824 - INFO - ⬆️ [17:31:30] Uploading: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:30,825 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:30,845 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:30,850 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/beb4a23e_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:30,850 - INFO - Processing PDF from S3...
2025-09-24 17:31:30,851 - INFO - Downloading PDF from S3 to /tmp/tmpfiy9v4w8/beb4a23e_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:31,774 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf -> s3://document-extraction-logistically/temp/23bcd790_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:31,774 - INFO - 🔍 [17:31:31] Starting classification: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:31,775 - INFO - ⬆️ [17:31:31] Uploading: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:31,776 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:31,803 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:31,806 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/23bcd790_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:31,806 - INFO - Processing PDF from S3...
2025-09-24 17:31:31,807 - INFO - Downloading PDF from S3 to /tmp/tmpjirfbmdc/23bcd790_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:32,764 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf -> s3://document-extraction-logistically/temp/24da1d16_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:32,764 - INFO - 🔍 [17:31:32] Starting classification: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:32,765 - INFO - ⬆️ [17:31:32] Uploading: S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:32,768 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:32,795 - INFO - S3 Image temp/de920c3d_AOYIL346IKT06LKO6D2R.jpg: Extracted 882 characters, 69 lines
2025-09-24 17:31:32,798 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:32,798 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:32,800 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:32,803 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/24da1d16_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:32,804 - INFO - Processing PDF from S3...
2025-09-24 17:31:32,804 - INFO - Downloading PDF from S3 to /tmp/tmpmh03ymo4/24da1d16_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:32,940 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:31:32,940 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:32,943 - INFO - Splitting PDF beb4a23e_GKYUUU2YTZ1YTPGXHO51 into 3 pages
2025-09-24 17:31:32,968 - INFO - Split PDF into 3 pages
2025-09-24 17:31:32,968 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:32,968 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:31:33,418 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf -> s3://document-extraction-logistically/temp/fb5d73b1_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:33,418 - INFO - 🔍 [17:31:33] Starting classification: S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:33,418 - INFO - ⬆️ [17:31:33] Uploading: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:33,419 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:33,428 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:33,430 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fb5d73b1_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:33,430 - INFO - Processing PDF from S3...
2025-09-24 17:31:33,431 - INFO - Downloading PDF from S3 to /tmp/tmpccq_b0kf/fb5d73b1_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:33,724 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:31:33,725 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:33,726 - INFO - Splitting PDF 23bcd790_I6IM526GOVKAWH0B5WKE into 1 pages
2025-09-24 17:31:33,728 - INFO - Split PDF into 1 pages
2025-09-24 17:31:33,728 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:33,729 - INFO - Expected pages: [1]
2025-09-24 17:31:34,046 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf -> s3://document-extraction-logistically/temp/c807aed0_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:34,046 - INFO - 🔍 [17:31:34] Starting classification: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:34,046 - INFO - ⬆️ [17:31:34] Uploading: YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:31:34,047 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:34,056 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:34,060 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c807aed0_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:34,060 - INFO - Processing PDF from S3...
2025-09-24 17:31:34,060 - INFO - Downloading PDF from S3 to /tmp/tmp5ztyl162/c807aed0_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:34,654 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:31:34,654 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:34,655 - WARNING - incorrect startxref pointer(1)
2025-09-24 17:31:34,658 - INFO - Splitting PDF 24da1d16_KK3PKFLS4ROTEZ7LOMMD into 1 pages
2025-09-24 17:31:34,715 - INFO - Split PDF into 1 pages
2025-09-24 17:31:34,715 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:34,715 - INFO - Expected pages: [1]
2025-09-24 17:31:35,005 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 17:31:35,005 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:35,007 - INFO - Splitting PDF fb5d73b1_S92OW12RYFF5G3READPW into 1 pages
2025-09-24 17:31:35,009 - INFO - Split PDF into 1 pages
2025-09-24 17:31:35,009 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:35,009 - INFO - Expected pages: [1]
2025-09-24 17:31:35,347 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 17:31:35,347 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:35,350 - INFO - Splitting PDF c807aed0_TQSZJLFFGGKB4NFN12RD into 1 pages
2025-09-24 17:31:35,352 - INFO - Split PDF into 1 pages
2025-09-24 17:31:35,352 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:35,353 - INFO - Expected pages: [1]
2025-09-24 17:31:35,534 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/YC2ROS9NUB31VO0ABEV5.jpeg -> s3://document-extraction-logistically/temp/7a41e466_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:31:35,534 - INFO - 🔍 [17:31:35] Starting classification: YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:31:35,535 - INFO - ⬆️ [17:31:35] Uploading: bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:35,541 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:35,554 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:35,559 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7a41e466_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:31:35,559 - INFO - Processing image from S3...
2025-09-24 17:31:35,933 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ec57649f-c5eb-4f03-9eb3-fe59acfaa574', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:35 GMT', 'content-type': 'application/json', 'content-length': '1264', 'connection': 'keep-alive', 'x-amzn-requestid': 'ec57649f-c5eb-4f03-9eb3-fe59acfaa574'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We need to decide doc_type. The page contains Delivery Receipt, with details: Delivery Receipt, Freight, Consignee, Shipper, Bill of Lading Number, Purchase Order Number, description. It looks like a Delivery Receipt or Proof of Delivery? The keywords: "Delivery Receipt", "PRO Number" and "Delivery Receipt" and "Signature". Typical POD includes "Proof of Delivery" or "Delivery Receipt". The page says "Delivery Receipt". That indicates POD. The doc_type list includes "pod" for Proof of Delivery. So we classify as pod.\n\nWe must check if any other type fits better: "pod" includes keywords: "Proof of Delivery", "Delivery Ticket", "POD", "Received by", "Delivered", "Delivery Receipt". Yes. So doc_type = pod.\n\nWe need to output JSON via the tool classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "pod". Return via tool.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2795, 'outputTokens': 223, 'totalTokens': 3018}, 'metrics': {'latencyMs': 1109}}
2025-09-24 17:31:35,934 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/de920c3d_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:31:36,587 - INFO - Page 1: Extracted 643 characters, 34 lines from beb4a23e_GKYUUU2YTZ1YTPGXHO51_30607320_page_001.pdf
2025-09-24 17:31:36,588 - INFO - Successfully processed page 1
2025-09-24 17:31:37,245 - INFO - Page 3: Extracted 1566 characters, 104 lines from beb4a23e_GKYUUU2YTZ1YTPGXHO51_30607320_page_003.pdf
2025-09-24 17:31:37,245 - INFO - Successfully processed page 3
2025-09-24 17:31:37,394 - INFO - Page 2: Extracted 1426 characters, 86 lines from beb4a23e_GKYUUU2YTZ1YTPGXHO51_30607320_page_002.pdf
2025-09-24 17:31:37,394 - INFO - Successfully processed page 2
2025-09-24 17:31:37,394 - INFO - Combined 3 pages into final text
2025-09-24 17:31:37,394 - INFO - Text validation for beb4a23e_GKYUUU2YTZ1YTPGXHO51: 3690 characters, 3 pages
2025-09-24 17:31:37,395 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:37,395 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:38,581 - INFO - S3 Image temp/7a41e466_YC2ROS9NUB31VO0ABEV5.jpeg: Extracted 454 characters, 31 lines
2025-09-24 17:31:38,581 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:38,581 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:39,299 - INFO - Page 1: Extracted 1921 characters, 143 lines from 23bcd790_I6IM526GOVKAWH0B5WKE_712d3b50_page_001.pdf
2025-09-24 17:31:39,299 - INFO - Successfully processed page 1
2025-09-24 17:31:39,300 - INFO - Combined 1 pages into final text
2025-09-24 17:31:39,300 - INFO - Text validation for 23bcd790_I6IM526GOVKAWH0B5WKE: 1938 characters, 1 pages
2025-09-24 17:31:39,300 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:39,300 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:39,446 - INFO - Page 1: Extracted 1453 characters, 86 lines from 24da1d16_KK3PKFLS4ROTEZ7LOMMD_445916e1_page_001.pdf
2025-09-24 17:31:39,446 - INFO - Successfully processed page 1
2025-09-24 17:31:39,446 - INFO - Combined 1 pages into final text
2025-09-24 17:31:39,446 - INFO - Text validation for 24da1d16_KK3PKFLS4ROTEZ7LOMMD: 1470 characters, 1 pages
2025-09-24 17:31:39,447 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:39,447 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:39,690 - INFO - Page 1: Extracted 1312 characters, 65 lines from c807aed0_TQSZJLFFGGKB4NFN12RD_fab68dd7_page_001.pdf
2025-09-24 17:31:39,690 - INFO - Successfully processed page 1
2025-09-24 17:31:39,691 - INFO - Combined 1 pages into final text
2025-09-24 17:31:39,691 - INFO - Text validation for c807aed0_TQSZJLFFGGKB4NFN12RD: 1329 characters, 1 pages
2025-09-24 17:31:39,691 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:39,691 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:40,014 - INFO - Page 1: Extracted 1576 characters, 111 lines from fb5d73b1_S92OW12RYFF5G3READPW_8a4efb6c_page_001.pdf
2025-09-24 17:31:40,014 - INFO - Successfully processed page 1
2025-09-24 17:31:40,014 - INFO - Combined 1 pages into final text
2025-09-24 17:31:40,014 - INFO - Text validation for fb5d73b1_S92OW12RYFF5G3READPW: 1593 characters, 1 pages
2025-09-24 17:31:40,015 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:40,015 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:40,316 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2a1764f0-16d9-4059-8919-2865751d8869', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:40 GMT', 'content-type': 'application/json', 'content-length': '560', 'connection': 'keep-alive', 'x-amzn-requestid': '2a1764f0-16d9-4059-8919-2865751d8869'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It appears to be a packing slip. That matches pack_list. Pack list keywords: Packing List, Pack List, Contents, Qty, Item Description. Yes. So doc_type: pack_list. Provide array with page_no 1 and doc_type "pack_list".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2642, 'outputTokens': 77, 'totalTokens': 2719}, 'metrics': {'latencyMs': 536}}
2025-09-24 17:31:40,317 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7a41e466_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:31:40,369 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf -> s3://document-extraction-logistically/temp/df9e701d_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:40,369 - INFO - 🔍 [17:31:40] Starting classification: bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:40,369 - INFO - ⬆️ [17:31:40] Uploading: bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:40,370 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:40,378 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:40,381 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/df9e701d_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:40,381 - INFO - Processing PDF from S3...
2025-09-24 17:31:40,381 - INFO - Downloading PDF from S3 to /tmp/tmppkq46cr7/df9e701d_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:41,194 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c813e9bb-ba23-410d-9454-9c0ed7866d2f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:41 GMT', 'content-type': 'application/json', 'content-length': '2838', 'connection': 'keep-alive', 'x-amzn-requestid': 'c813e9bb-ba23-410d-9454-9c0ed7866d2f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There are 3 pages. The first page is a FedEx proof-of-delivery (POD). It contains "proof-of-delivery" header, delivered status, signed for by, etc. So doc_type is pod.\n\nSecond page appears to be a delivery receipt freight bill, which is basically a freight bill / invoice? It contains "Freight Bill" header, charges, etc. This is likely an invoice? But it\'s a freight bill, which is essentially an invoice for freight charges. Does it contain commercial invoice keywords? No. It\'s just a freight bill. In the prompt, doc_type \'invoice\' is for carrier invoice. Freight bill might be considered an invoice. There\'s no mention of commercial invoice. So classify as invoice. However, could it be a combined carrier documents? It contains both BOL (Bill of Lading) and freight bill. But the page contains Bill of Lading number and freight bill number. It might be a combined? But we should decide.\n\nThe third page is similar to second page, also a delivery receipt freight bill. It\'s likely similar type. It might be same type as page2. But page3 says "Page 1 of 2" meaning page3 is first of two. Page2 was page 2 of 2 for earlier? Actually the second page had "Page 2 of 2". That was page2. Third page says "Page 1 of 2". So page3 is a separate document? The PDF likely contains first document pages 1-2, second document pages 1-2. We need to classify each page individually. Page2 is "Page 2 of 2" of a delivery receipt; page3 is "Page 1 of 2" of another delivery receipt. They are similar. So each page gets a doc_type. For page2, it\'s a Freight Bill / Invoice. For page3, same.\n\nBut it\'s important to check if page3 is a continuation of previous page? The text "Page 1 of 2" indicates it\'s the start of a new document, so it\'s not a continuation. So assign doc_type invoice.\n\nThus output:\n\nPage1: pod\n\nPage2: invoice\n\nPage3: invoice\n\nBut check if page2 might be "combined_carrier_documents"? It contains BOL header, freight bill, etc. But the doc_type definitions: combined_carrier_documents: multiple distinct document headers on one page. This page seems to contain BOL and freight bill, but BOL appears as "Bill of Lading 11746" not a header. But there is "Freight Bill 6435169926 R0". But it\'s basically freight bill. So I\'d keep invoice.\n\nThus output array of objects with page_no and doc_type.\n\nWe must use the tool classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3788, 'outputTokens': 619, 'totalTokens': 4407}, 'metrics': {'latencyMs': 2597}}
2025-09-24 17:31:41,194 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/beb4a23e_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:41,439 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e15df9ba-3f02-4ce1-854b-4973dbbc1527', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:41 GMT', 'content-type': 'application/json', 'content-length': '786', 'connection': 'keep-alive', 'x-amzn-requestid': 'e15df9ba-3f02-4ce1-854b-4973dbbc1527'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. The content seems like a Delivery Receipt, with "Delivery Receipt" title, includes Pro Number, date, etc. This is a Proof of Delivery page. The keywords: "Delivery Receipt", "Delivered", "Consignee Signature", "Consignee Name", "Date", "Driver Name", etc. So doc_type is pod (Proof of Delivery). So output JSON array of one object: page_no: 1, doc_type: "pod". Use tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2996, 'outputTokens': 128, 'totalTokens': 3124}, 'metrics': {'latencyMs': 831}}
2025-09-24 17:31:41,443 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/24da1d16_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:41,681 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e3d2c96d-3bdc-4628-ba8e-5011733488a8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:41 GMT', 'content-type': 'application/json', 'content-length': '863', 'connection': 'keep-alive', 'x-amzn-requestid': 'e3d2c96d-3bdc-4628-ba8e-5011733488a8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Determine document type. The page header: DELIVERY RECEIPT. It contains Received by, driver details, manifest number, etc. It\'s a delivery receipt. That is essentially POD? Proof of Delivery. The definition: POD includes signatures, dates, condition notes. The page includes Received by, driver name, delivery date, driver arrival time, etc. It is a delivery receipt. So classify as pod. The presence of "DELIVERY RECEIPT" indicates POD. So doc_type: pod. No continuation. So output array with one object: page_no 1, doc_type: pod.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2926, 'outputTokens': 142, 'totalTokens': 3068}, 'metrics': {'latencyMs': 817}}
2025-09-24 17:31:41,682 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c807aed0_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:42,174 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf -> s3://document-extraction-logistically/temp/b1f196d5_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:42,175 - INFO - 🔍 [17:31:42] Starting classification: bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:42,175 - INFO - ⬆️ [17:31:42] Uploading: bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:42,178 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:42,199 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:42,205 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b1f196d5_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:42,205 - INFO - Processing PDF from S3...
2025-09-24 17:31:42,206 - INFO - Downloading PDF from S3 to /tmp/tmp007zrs0e/b1f196d5_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:42,595 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '200565ce-e831-47a7-9c90-a5248ec2bea6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:42 GMT', 'content-type': 'application/json', 'content-length': '1486', 'connection': 'keep-alive', 'x-amzn-requestid': '200565ce-e831-47a7-9c90-a5248ec2bea6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. Determine doc type. Let\'s analyze content. It starts "Delivery" and "SEFL FREIGHT BILL NUMBER" etc. It looks like a freight bill, which is a type of invoice? The freight bill is similar to an invoice. The definition says invoice: Bill issued by a carrier for goods being transported. This page includes freight bill number, amount, charges, etc. There\'s no commercial invoice due to maybe not international. So likely "invoice". However we need to consider "combined_carrier_documents" if multiple documents. But it seems just one document: a freight bill. So doc_type "invoice".\n\nCheck for "invoice" keywords: "FREIGHT BILL NUMBER", "BILL TO", "TOTAL COLLECT", "CHARGES" etc. That matches.\n\nCheck for "comm_invoice": would have HS Code, customs. Not present.\n\nCheck for "bol": would have Bill of Lading, B/L. This has Freight Bill not BOL. So not bol.\n\nThus output: page_no 1, doc_type "invoice".\n\nReturn JSON with array of documents? The function expects an object with documents array. Provide exactly: { documents: [{ page_no: 1, doc_type: "invoice" }] }.\n\nLet\'s produce.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3088, 'outputTokens': 282, 'totalTokens': 3370}, 'metrics': {'latencyMs': 1429}}
2025-09-24 17:31:42,595 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fb5d73b1_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:42,863 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf -> s3://document-extraction-logistically/temp/02995b19_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:42,864 - INFO - 🔍 [17:31:42] Starting classification: bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:42,865 - INFO - ⬆️ [17:31:42] Uploading: bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:42,867 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:42,886 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:42,890 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/02995b19_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:42,890 - INFO - Processing PDF from S3...
2025-09-24 17:31:42,891 - INFO - Downloading PDF from S3 to /tmp/tmpzf7j4csw/02995b19_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:43,406 - INFO - Downloaded PDF size: 1.8 MB
2025-09-24 17:31:43,407 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:43,409 - INFO - Splitting PDF df9e701d_bol_DV1B199A0TJGUQ3FETIU into 3 pages
2025-09-24 17:31:43,452 - INFO - Split PDF into 3 pages
2025-09-24 17:31:43,452 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:43,452 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:31:43,802 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf -> s3://document-extraction-logistically/temp/05d156e3_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:43,802 - INFO - 🔍 [17:31:43] Starting classification: bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:43,803 - INFO - ⬆️ [17:31:43] Uploading: bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:43,804 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:43,813 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:43,816 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/05d156e3_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:43,816 - INFO - Processing PDF from S3...
2025-09-24 17:31:43,817 - INFO - Downloading PDF from S3 to /tmp/tmpbhyt5ulp/05d156e3_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:44,181 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a8438e7c-b3a3-44a9-827a-f83c588581dc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:44 GMT', 'content-type': 'application/json', 'content-length': '4109', 'connection': 'keep-alive', 'x-amzn-requestid': 'a8438e7c-b3a3-44a9-827a-f83c588581dc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify doc_type. Let\'s examine content.\n\nThis looks like a shipping receipt, maybe a "Receipt" from Estes Express. It lists "Date", "Origin", "Destination", "P.O.", "Shipper BL or GBL Number", "Consignee", etc. There\'s a header "RECEIPT". There\'s mention of "Receiver\'s Signature", "PO", "Driver". There\'s mention of "Lumper SVC". The page mentions "Call (714) 523-1122 for inquiries regarding this freight bill." So it seems to be a freight bill receipt, perhaps a "Receipt" or "Invoice" for freight. Could be an "invoice" (carrier invoice). But there\'s no explicit "Invoice" header. It\'s a "RECEIPT". The classification list includes "invoice" (carrier invoice), "comm_invoice", "bol", "pod", etc.\n\nBut also "bol" (Bill of Lading) would have shipping details, but also "Shipper", "Consignee", "BL number", "Marks & Numbers" etc. This page has "Shipper BL or GBL Number" and "BGL". It also has "Shipper" and "Consignee". So might be a Bill of Lading (BOL). But BOL usually includes marks and numbers, description of goods, quantity, weight, etc. This page includes "Description", "Weight/Lbs", "Total Charges". Also "Shipper Load Count". It might be a "Bill of Lading" with charges. In the header, there is "RECEIPT". The page has "Shipper BL or GBL Number". Also "EVRI11054989" maybe a bill of lading number. There\'s also "Shipper Load Count". This looks like a "Bill of Lading" (BOL). But it also might be a "Carrier Invoice" because it has "Total Charges". However "invoice" vs "bol" difference: BOL is a transport contract and receipt for goods; it may not show charges. But the presence of charges suggests an invoice. But the header says "RECEIPT". Could be a "Receipt" of freight bill. The classification list includes "invoice". That is a Bill issued by a carrier for goods being transported. Typical invoice would include charges. It might also have "Invoice No". Not present. But "Shipper BL or GBL Number" maybe the invoice number? There\'s also "EVRI11054989" repeated. Not sure.\n\nAlso "Shipper Load Count" and "Total Charges" maybe part of a "Bill of Lading" but also invoice. The presence of "Receipt" indicates maybe a "Receipt" of freight bill. But the classification list does not have "receipt". So we choose the best category: either "invoice" or "bol".\n\nThe page also has "Shipper BL or GBL Number" and "Consignee" details, which are typical of BOL. The BOL header often "BOL" or "Bill of Lading" or "B/L". But here it\'s "RECEIPT". So maybe this is a "Receipt" from carrier that is also a "Bill of Lading". Many carriers produce a "Receipt" that includes charges. But I\'d classify as "invoice" because it includes charges and "Total Charges". That matches "invoice" definition: Bill issued by a carrier for goods being transported. The presence of "Total Charges" and "Call for inquiries regarding this freight bill" indicates it\'s a freight bill. So "invoice".\n\nCheck for "comm_invoice" difference: if HS/HTS codes etc. Not present. So not comm_invoice. "lumper_receipt": might have service charges for unloading. This page mentions "Lumper SVC" and "Unloading SVCS - REQD BY GONSIGNEE". But it\'s not a lumper receipt; those usually only cover loading/unloading labor. This page includes all freight charges and loads. So not lumper_receipt.\n\n"BOL" also usually doesn\'t include charges. But some BOL may include charges. But the presence of "Total Charges" suggests it\'s an invoice.\n\nThus classify page1 as "invoice".\n\nWe need to output via tool call classify_logistics_doc_type with documents array: {page_no: 1, doc_type: "invoice"}.\n\nLet\'s produce that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3202, 'outputTokens': 918, 'totalTokens': 4120}, 'metrics': {'latencyMs': 3718}}
2025-09-24 17:31:44,182 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/23bcd790_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:44,728 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:31:44,729 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:44,730 - INFO - Splitting PDF 02995b19_bol_IJW9IJUOROYY1SWO0B17 into 1 pages
2025-09-24 17:31:44,734 - INFO - Split PDF into 1 pages
2025-09-24 17:31:44,734 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:44,734 - INFO - Expected pages: [1]
2025-09-24 17:31:45,056 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf -> s3://document-extraction-logistically/temp/19ff5ff5_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:45,056 - INFO - 🔍 [17:31:45] Starting classification: bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:45,057 - INFO - ⬆️ [17:31:45] Uploading: bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:31:45,059 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:45,083 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:45,093 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/19ff5ff5_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:45,093 - INFO - Processing PDF from S3...
2025-09-24 17:31:45,096 - INFO - Downloading PDF from S3 to /tmp/tmpp1ovl03c/19ff5ff5_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:45,104 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 17:31:45,105 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:45,112 - INFO - Splitting PDF b1f196d5_bol_HEMTH4BHCWXWE044I0SK into 2 pages
2025-09-24 17:31:45,127 - INFO - Split PDF into 2 pages
2025-09-24 17:31:45,127 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:45,127 - INFO - Expected pages: [1, 2]
2025-09-24 17:31:46,165 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:31:46,165 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:46,167 - INFO - Splitting PDF 05d156e3_bol_K88AFHJU4HIQUZYTMOF4 into 1 pages
2025-09-24 17:31:46,171 - INFO - Split PDF into 1 pages
2025-09-24 17:31:46,171 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:46,171 - INFO - Expected pages: [1]
2025-09-24 17:31:47,589 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf -> s3://document-extraction-logistically/temp/be74ab46_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:31:47,589 - INFO - 🔍 [17:31:47] Starting classification: bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:31:47,590 - INFO - ⬆️ [17:31:47] Uploading: bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:31:47,591 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:47,610 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:47,614 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/be74ab46_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:31:47,615 - INFO - Processing PDF from S3...
2025-09-24 17:31:47,615 - INFO - Downloading PDF from S3 to /tmp/tmp_oxwrb18/be74ab46_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:31:47,807 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 17:31:47,808 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:47,810 - INFO - Splitting PDF 19ff5ff5_bol_P2EZG64EJI98NT69TSAL into 1 pages
2025-09-24 17:31:47,816 - INFO - Split PDF into 1 pages
2025-09-24 17:31:47,816 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:47,816 - INFO - Expected pages: [1]
2025-09-24 17:31:48,805 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf -> s3://document-extraction-logistically/temp/7ff66994_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:31:48,806 - INFO - 🔍 [17:31:48] Starting classification: bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:31:48,807 - INFO - ⬆️ [17:31:48] Uploading: pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:31:48,809 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:48,832 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:48,836 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7ff66994_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:31:48,837 - INFO - Processing PDF from S3...
2025-09-24 17:31:48,837 - INFO - Downloading PDF from S3 to /tmp/tmpe383g4x6/7ff66994_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:31:49,069 - INFO - Page 3: Extracted 990 characters, 86 lines from df9e701d_bol_DV1B199A0TJGUQ3FETIU_8d7e099c_page_003.pdf
2025-09-24 17:31:49,069 - INFO - Successfully processed page 3
2025-09-24 17:31:49,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg -> s3://document-extraction-logistically/temp/2bb88c60_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:31:49,476 - INFO - 🔍 [17:31:49] Starting classification: pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:31:49,476 - INFO - ⬆️ [17:31:49] Uploading: pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:31:49,478 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:49,495 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:49,498 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2bb88c60_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:31:49,498 - INFO - Processing image from S3...
2025-09-24 17:31:50,076 - INFO - Page 1: Extracted 2979 characters, 110 lines from df9e701d_bol_DV1B199A0TJGUQ3FETIU_8d7e099c_page_001.pdf
2025-09-24 17:31:50,077 - INFO - Successfully processed page 1
2025-09-24 17:31:50,091 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg -> s3://document-extraction-logistically/temp/d1116033_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:31:50,091 - INFO - 🔍 [17:31:50] Starting classification: pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:31:50,092 - INFO - Initializing TextractProcessor...
2025-09-24 17:31:50,099 - INFO - Initializing BedrockProcessor...
2025-09-24 17:31:50,103 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d1116033_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:31:50,104 - INFO - Processing image from S3...
2025-09-24 17:31:50,119 - INFO - 

AOYIL346IKT06LKO6D2R.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:31:50,119 - INFO - 

✓ Saved result: output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:31:50,427 - INFO - Page 2: Extracted 2112 characters, 162 lines from df9e701d_bol_DV1B199A0TJGUQ3FETIU_8d7e099c_page_002.pdf
2025-09-24 17:31:50,427 - INFO - Successfully processed page 2
2025-09-24 17:31:50,427 - INFO - Combined 3 pages into final text
2025-09-24 17:31:50,428 - INFO - Text validation for df9e701d_bol_DV1B199A0TJGUQ3FETIU: 6135 characters, 3 pages
2025-09-24 17:31:50,428 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:50,428 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:50,432 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/de920c3d_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:31:50,439 - INFO - 

YC2ROS9NUB31VO0ABEV5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:31:50,439 - INFO - 

✓ Saved result: output/run1_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:31:50,679 - INFO - Downloaded PDF size: 3.3 MB
2025-09-24 17:31:50,680 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:50,681 - INFO - Splitting PDF be74ab46_bol_PK5AIZFCER7DUFL15N01 into 1 pages
2025-09-24 17:31:50,689 - INFO - Split PDF into 1 pages
2025-09-24 17:31:50,689 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:50,689 - INFO - Expected pages: [1]
2025-09-24 17:31:50,739 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7a41e466_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:31:50,777 - INFO - 

GKYUUU2YTZ1YTPGXHO51.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:31:50,777 - INFO - 

✓ Saved result: output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:31:51,070 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/beb4a23e_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:31:51,103 - INFO - 

KK3PKFLS4ROTEZ7LOMMD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:31:51,104 - INFO - 

✓ Saved result: output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:31:51,252 - INFO - Page 1: Extracted 3736 characters, 122 lines from 02995b19_bol_IJW9IJUOROYY1SWO0B17_444acc02_page_001.pdf
2025-09-24 17:31:51,252 - INFO - Successfully processed page 1
2025-09-24 17:31:51,253 - INFO - Combined 1 pages into final text
2025-09-24 17:31:51,253 - INFO - Text validation for 02995b19_bol_IJW9IJUOROYY1SWO0B17: 3753 characters, 1 pages
2025-09-24 17:31:51,253 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:51,253 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:51,394 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/24da1d16_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:31:51,415 - INFO - 

TQSZJLFFGGKB4NFN12RD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:31:51,415 - INFO - 

✓ Saved result: output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:31:51,720 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c807aed0_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:31:51,725 - INFO - Page 1: Extracted 3181 characters, 88 lines from b1f196d5_bol_HEMTH4BHCWXWE044I0SK_623259de_page_001.pdf
2025-09-24 17:31:51,727 - INFO - Successfully processed page 1
2025-09-24 17:31:51,748 - INFO - Downloaded PDF size: 1.3 MB
2025-09-24 17:31:51,750 - INFO - Splitting PDF into individual pages...
2025-09-24 17:31:51,755 - INFO - Splitting PDF 7ff66994_bol_RCNPMJFRSZTXQWBGLB9R into 1 pages
2025-09-24 17:31:51,763 - INFO - 

S92OW12RYFF5G3READPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:31:51,763 - INFO - Split PDF into 1 pages
2025-09-24 17:31:51,763 - INFO - 

✓ Saved result: output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:31:51,763 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:31:51,765 - INFO - Expected pages: [1]
2025-09-24 17:31:51,987 - INFO - Page 2: Extracted 3230 characters, 90 lines from b1f196d5_bol_HEMTH4BHCWXWE044I0SK_623259de_page_002.pdf
2025-09-24 17:31:51,987 - INFO - Successfully processed page 2
2025-09-24 17:31:51,988 - INFO - Combined 2 pages into final text
2025-09-24 17:31:51,988 - INFO - Text validation for b1f196d5_bol_HEMTH4BHCWXWE044I0SK: 6447 characters, 2 pages
2025-09-24 17:31:51,988 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:51,988 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:52,083 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fb5d73b1_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:31:52,118 - INFO - 

I6IM526GOVKAWH0B5WKE.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:31:52,118 - INFO - 

✓ Saved result: output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:31:52,412 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/23bcd790_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:31:52,502 - INFO - S3 Image temp/2bb88c60_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg: Extracted 515 characters, 39 lines
2025-09-24 17:31:52,502 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:52,503 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:52,742 - INFO - Page 1: Extracted 2991 characters, 109 lines from 05d156e3_bol_K88AFHJU4HIQUZYTMOF4_720b2864_page_001.pdf
2025-09-24 17:31:52,742 - INFO - Successfully processed page 1
2025-09-24 17:31:52,743 - INFO - Combined 1 pages into final text
2025-09-24 17:31:52,743 - INFO - Text validation for 05d156e3_bol_K88AFHJU4HIQUZYTMOF4: 3008 characters, 1 pages
2025-09-24 17:31:52,743 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:52,743 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:53,167 - INFO - S3 Image temp/d1116033_pack_list_P6RAGXD27RW4QUXN96TH.jpeg: Extracted 456 characters, 31 lines
2025-09-24 17:31:53,167 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:53,167 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:53,388 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2f34107e-b6a1-4b2e-b0e8-8bffd6e79f39', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:53 GMT', 'content-type': 'application/json', 'content-length': '1036', 'connection': 'keep-alive', 'x-amzn-requestid': '2f34107e-b6a1-4b2e-b0e8-8bffd6e79f39'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. The content is a waybill. Keywords: WAYBILL, watchpoint, Waybill #, SHIP FROM, SHIP TO, BILL TO, SHIPMENT INFORMATION, handling unit, etc. Waybill is a bill of lading type? It is a transport document; likely "bol" Bill of Lading (or Waybill). The doc type "bol" is defined for Bill of Lading, includes keywords "Bill of Lading", B/L, Shipper, Consignee. This document is a Waybill, which is a kind of Bill of Lading. So classify as bol. Not invoice, not POD, etc. It\'s a transport document. So return array with one object: page_no 1, doc_type "bol".\n\nWe must use the tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_0CWV7LgASfa9OvVFQ3cFeg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3512, 'outputTokens': 187, 'totalTokens': 3699}, 'metrics': {'latencyMs': 1001}}
2025-09-24 17:31:53,389 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/02995b19_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:53,442 - INFO - 

bol_IJW9IJUOROYY1SWO0B17.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:31:53,442 - INFO - 

✓ Saved result: output/run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:31:53,557 - INFO - Page 1: Extracted 1608 characters, 74 lines from 19ff5ff5_bol_P2EZG64EJI98NT69TSAL_39db8df5_page_001.pdf
2025-09-24 17:31:53,557 - INFO - Successfully processed page 1
2025-09-24 17:31:53,558 - INFO - Combined 1 pages into final text
2025-09-24 17:31:53,558 - INFO - Text validation for 19ff5ff5_bol_P2EZG64EJI98NT69TSAL: 1625 characters, 1 pages
2025-09-24 17:31:53,558 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:53,558 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:31:53,757 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/02995b19_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:31:53,967 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '04e02046-ac11-4097-a4c6-af1b31b4cd88', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:53 GMT', 'content-type': 'application/json', 'content-length': '774', 'connection': 'keep-alive', 'x-amzn-requestid': '04e02046-ac11-4097-a4c6-af1b31b4cd88'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Both appear identical but maybe slight differences: page1 says UNIFRON STRAIGHT BILL OF LADING, page2 says UNIFROM STRAIGHT BILL OF LADING (typo). Both contain "Bill of Lading NO 873960". So it\'s Bill of Lading. That is doc_type "bol". No other type. There\'s no continuation indicator. So page1 bol, page2 bol. Output an array with documents: page_no and doc_type. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4119, 'outputTokens': 135, 'totalTokens': 4254}, 'metrics': {'latencyMs': 803}}
2025-09-24 17:31:53,967 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b1f196d5_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:54,024 - INFO - 

bol_HEMTH4BHCWXWE044I0SK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:31:54,024 - INFO - 

✓ Saved result: output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:31:54,366 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b1f196d5_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:31:54,378 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c04b6e2d-7ed1-4aa4-9e6d-1b51a5321d62', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:54 GMT', 'content-type': 'application/json', 'content-length': '2764', 'connection': 'keep-alive', 'x-amzn-requestid': 'c04b6e2d-7ed1-4aa4-9e6d-1b51a5321d62'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document seems to be a Bill of Lading with shipment details. Page1 has "BILL OF LADING". That is a BOL. The subsequent pages appear to be freight bill information, maybe an invoice. But page2 and page3 look like freight bill or perhaps a freight bill (like an invoice). They contain "FREIGHT BILL NO." and charges, etc. They might be a carrier invoice or freight bill. But the header "FREIGHT BILL NO." suggests perhaps this is a freight bill, which is a type of invoice. In the definitions, "invoice" is carrier invoice; "comm_invoice" for commercial invoice. This is domestic, likely carrier invoice. Keywords: "FREIGHT BILL NO.", "RATE CHARGES", "PAY THIS AMOUNT". So that would be "invoice".\n\nBut we need to decide doc_type for each page. Page1 is Bill of Lading. So doc_type: "bol". Page2 and page3 appear to be the same document type; likely invoice, but could also be "combined_carrier_documents" if multiple documents appear on same page. But each page is separate; page2 and page3 contain the freight bill details. Page2 has "PAGE 1 OF 3", page3 "PAGE 3 OF 3". These could be part of a multi-page freight bill. So each page is the same doc_type. The doc_type for page2 and page3 is "invoice". Or maybe "invoice" but check if it\'s "combined_carrier_documents"? Combined would be if a single page contains multiple documents. Not the case.\n\nThus output: array of objects for page 1, 2, 3.\n\nWe must call the function classify_logistics_doc_type with documents array each containing page_no and doc_type. Wait the function expects an array of objects each with page_no and doc_type. So we need to pass the whole array. The function returns any. We need to output via tool call only. So we produce a call to classify_logistics_doc_type with documents array:\n\n[\n {page_no: 1, doc_type: "bol"},\n {page_no: 2, doc_type: "invoice"},\n {page_no: 3, doc_type: "invoice"}\n]\n\nMake sure doc_type matches enum: "bol", "invoice". Yes.\n\nLet\'s check if page2 or page3 might be "invoice" or "combined_carrier_documents". They contain only freight bill info. There\'s no other header. So "invoice".\n\nNo continuation signals? Page2 and page3 have "PAGE 1 OF 3" etc. They are part of same document. They are not continuation text. So classification per page is fine.\n\nThus final.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4606, 'outputTokens': 600, 'totalTokens': 5206}, 'metrics': {'latencyMs': 2798}}
2025-09-24 17:31:54,379 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/df9e701d_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:54,455 - INFO - 

bol_DV1B199A0TJGUQ3FETIU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:31:54,456 - INFO - 

✓ Saved result: output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:31:54,469 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'decd59c3-9adb-484c-8af5-4a7b73aed89b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:54 GMT', 'content-type': 'application/json', 'content-length': '455', 'connection': 'keep-alive', 'x-amzn-requestid': 'decd59c3-9adb-484c-8af5-4a7b73aed89b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify the page. It\'s a Bill of Lading. The doc_type is "bol". Provide JSON with page_no 1. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3269, 'outputTokens': 54, 'totalTokens': 3323}, 'metrics': {'latencyMs': 516}}
2025-09-24 17:31:54,469 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/05d156e3_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:54,653 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f080adf4-74a4-43b7-9b03-a54ebfdb5551', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:54 GMT', 'content-type': 'application/json', 'content-length': '980', 'connection': 'keep-alive', 'x-amzn-requestid': 'f080adf4-74a4-43b7-9b03-a54ebfdb5551'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page given. We need to identify the document type from keywords.\n\nPage contains "Packing Slip", includes shipping details, order quantity, ship quantity, etc. That looks like a packing slip or packing list. The doc type options include "pack_list" for Packing List. There\'s also "packing slip" but not a separate type. The definition for pack_list: Packing List. Use that. So doc_type "pack_list".\n\nWe need to output using the tool classify_logistics_doc_type with an array of objects for each page. There\'s only one page. So output: {page_no:1, doc_type:"pack_list"}.\n\nLet\'s produce the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2677, 'outputTokens': 161, 'totalTokens': 2838}, 'metrics': {'latencyMs': 919}}
2025-09-24 17:31:54,654 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2bb88c60_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:31:54,744 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/df9e701d_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:31:54,782 - INFO - 

bol_K88AFHJU4HIQUZYTMOF4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:31:54,782 - INFO - 

✓ Saved result: output/run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:31:55,073 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/05d156e3_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:31:55,097 - INFO - 

pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:31:55,098 - INFO - 

✓ Saved result: output/run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:31:55,399 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2bb88c60_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:31:55,401 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e0c03816-c6db-4147-b9aa-c5c543f57031', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:55 GMT', 'content-type': 'application/json', 'content-length': '1223', 'connection': 'keep-alive', 'x-amzn-requestid': 'e0c03816-c6db-4147-b9aa-c5c543f57031'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to output via tool classify_logistics_doc_type with an array of one object: page_no 1, doc_type.\n\nWe need to determine doc_type. The page seems to be a Packing Slip, but the list of allowed types: pack_list, invoice, bol, etc. Packing Slip is typically a packing list. The description: "Packing Slip" is basically a packing list. The keywords: "Packing Slip", "Part Number", "Order Quantity", "Ship Quantity", "Ship To". This matches pack_list? The pack_list definition: "Itemized list of shipment contents for inventory/receiving checks." Keyword: "Packing List". This page says "Packing Slip". But likely pack_list. We should classify as pack_list.\n\nCheck if any other doc type matches: invoice? Not enough. So pack_list.\n\nThus output: documents: [{page_no:1, doc_type:"pack_list"}].\n\nWe must call the function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2641, 'outputTokens': 222, 'totalTokens': 2863}, 'metrics': {'latencyMs': 1041}}
2025-09-24 17:31:55,402 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d1116033_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:31:55,417 - INFO - 

pack_list_P6RAGXD27RW4QUXN96TH.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:31:55,417 - INFO - 

✓ Saved result: output/run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:31:55,522 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fd93878a-2efd-4417-892a-47c2360bd616', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:01:55 GMT', 'content-type': 'application/json', 'content-length': '783', 'connection': 'keep-alive', 'x-amzn-requestid': 'fd93878a-2efd-4417-892a-47c2360bd616'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have only page1 text. It\'s a Bill of Lading. It\'s a "Straight Bill of Lading - Short Form". So doc_type: bol.\n\nWe need to output a JSON array? The tool expects an object with documents array: each object has page_no and doc_type. Must use tool classify_logistics_doc_type. Provide array with one item page_no:1 doc_type:"bol".\n\nLet\'s call tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_xowIczeiTg-jQXwvBCThKA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2948, 'outputTokens': 109, 'totalTokens': 3057}, 'metrics': {'latencyMs': 755}}
2025-09-24 17:31:55,522 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/19ff5ff5_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:55,721 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d1116033_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:31:55,751 - INFO - 

bol_P2EZG64EJI98NT69TSAL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:31:55,752 - INFO - 

✓ Saved result: output/run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:31:56,043 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/19ff5ff5_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:31:59,725 - INFO - Page 1: Extracted 3032 characters, 118 lines from be74ab46_bol_PK5AIZFCER7DUFL15N01_82d53248_page_001.pdf
2025-09-24 17:31:59,725 - INFO - Successfully processed page 1
2025-09-24 17:31:59,725 - INFO - Combined 1 pages into final text
2025-09-24 17:31:59,725 - INFO - Text validation for be74ab46_bol_PK5AIZFCER7DUFL15N01: 3049 characters, 1 pages
2025-09-24 17:31:59,726 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:31:59,726 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:32:01,643 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '299a657d-680a-453a-974b-9fb41db59d07', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:02:01 GMT', 'content-type': 'application/json', 'content-length': '535', 'connection': 'keep-alive', 'x-amzn-requestid': '299a657d-680a-453a-974b-9fb41db59d07'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this single page. The page contains a Bill of Lading. So doc_type: bol. There\'s no other header like invoice. So bol. Only one page. So output array with one object: page_no:1, doc_type:"bol". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3287, 'outputTokens': 74, 'totalTokens': 3361}, 'metrics': {'latencyMs': 553}}
2025-09-24 17:32:01,644 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/be74ab46_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:32:01,684 - INFO - 

bol_PK5AIZFCER7DUFL15N01.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:32:01,684 - INFO - 

✓ Saved result: output/run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:32:01,978 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/be74ab46_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:32:02,394 - INFO - Page 1: Extracted 2992 characters, 114 lines from 7ff66994_bol_RCNPMJFRSZTXQWBGLB9R_17ad234a_page_001.pdf
2025-09-24 17:32:02,394 - INFO - Successfully processed page 1
2025-09-24 17:32:02,395 - INFO - Combined 1 pages into final text
2025-09-24 17:32:02,396 - INFO - Text validation for 7ff66994_bol_RCNPMJFRSZTXQWBGLB9R: 3009 characters, 1 pages
2025-09-24 17:32:02,396 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:32:02,397 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:32:04,414 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b59a34d5-2ab1-40c5-a08c-07a7318c70ba', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:02:04 GMT', 'content-type': 'application/json', 'content-length': '567', 'connection': 'keep-alive', 'x-amzn-requestid': 'b59a34d5-2ab1-40c5-a08c-07a7318c70ba'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. It\'s a Bill of Lading. The doc type is bol. The page has header "BILL OF LADING". So doc_type: bol. We need to output via tool classify_logistics_doc_type with documents array containing object for page_no 1, doc_type "bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3269, 'outputTokens': 82, 'totalTokens': 3351}, 'metrics': {'latencyMs': 585}}
2025-09-24 17:32:04,414 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7ff66994_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:32:04,468 - INFO - 

bol_RCNPMJFRSZTXQWBGLB9R.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:32:04,468 - INFO - 

✓ Saved result: output/run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:32:04,822 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7ff66994_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:32:04,823 - INFO - 
📊 Processing Summary:
2025-09-24 17:32:04,823 - INFO -    Total files: 16
2025-09-24 17:32:04,823 - INFO -    Successful: 16
2025-09-24 17:32:04,823 - INFO -    Failed: 0
2025-09-24 17:32:04,823 - INFO -    Duration: 37.47 seconds
2025-09-24 17:32:04,823 - INFO -    Output directory: output
2025-09-24 17:32:04,823 - INFO - 
📋 Successfully Processed Files:
2025-09-24 17:32:04,823 - INFO -    📄 AOYIL346IKT06LKO6D2R.jpg: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:32:04,824 - INFO -    📄 GKYUUU2YTZ1YTPGXHO51.pdf: {"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 17:32:04,824 - INFO -    📄 I6IM526GOVKAWH0B5WKE.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 17:32:04,824 - INFO -    📄 KK3PKFLS4ROTEZ7LOMMD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:32:04,824 - INFO -    📄 S92OW12RYFF5G3READPW.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 17:32:04,824 - INFO -    📄 TQSZJLFFGGKB4NFN12RD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:32:04,824 - INFO -    📄 YC2ROS9NUB31VO0ABEV5.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_DV1B199A0TJGUQ3FETIU.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_HEMTH4BHCWXWE044I0SK.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_IJW9IJUOROYY1SWO0B17.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_K88AFHJU4HIQUZYTMOF4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_P2EZG64EJI98NT69TSAL.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_PK5AIZFCER7DUFL15N01.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:32:04,824 - INFO -    📄 bol_RCNPMJFRSZTXQWBGLB9R.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:32:04,825 - INFO -    📄 pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:32:04,825 - INFO -    📄 pack_list_P6RAGXD27RW4QUXN96TH.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:32:04,826 - INFO - 
============================================================================================================================================
2025-09-24 17:32:04,826 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 17:32:04,826 - INFO - ============================================================================================================================================
2025-09-24 17:32:04,826 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 17:32:04,826 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:32:04,826 - INFO - AOYIL346IKT06LKO6D2R.jpg                           1      pod                                      run1_AOYIL346IKT06LKO6D2R.json                                                  
2025-09-24 17:32:04,826 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:32:04,826 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:32:04,826 - INFO - 
2025-09-24 17:32:04,826 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           1      pod                                      run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:32:04,826 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:32:04,826 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:32:04,826 - INFO - 
2025-09-24 17:32:04,826 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           2      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:32:04,827 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:32:04,827 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:32:04,827 - INFO - 
2025-09-24 17:32:04,827 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           3      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:32:04,827 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:32:04,827 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:32:04,827 - INFO - 
2025-09-24 17:32:04,827 - INFO - I6IM526GOVKAWH0B5WKE.pdf                           1      invoice                                  run1_I6IM526GOVKAWH0B5WKE.json                                                  
2025-09-24 17:32:04,827 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:32:04,827 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:32:04,827 - INFO - 
2025-09-24 17:32:04,827 - INFO - KK3PKFLS4ROTEZ7LOMMD.pdf                           1      pod                                      run1_KK3PKFLS4ROTEZ7LOMMD.json                                                  
2025-09-24 17:32:04,827 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:32:04,827 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:32:04,827 - INFO - 
2025-09-24 17:32:04,827 - INFO - S92OW12RYFF5G3READPW.pdf                           1      invoice                                  run1_S92OW12RYFF5G3READPW.json                                                  
2025-09-24 17:32:04,827 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf
2025-09-24 17:32:04,827 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:32:04,827 - INFO - 
2025-09-24 17:32:04,828 - INFO - TQSZJLFFGGKB4NFN12RD.pdf                           1      pod                                      run1_TQSZJLFFGGKB4NFN12RD.json                                                  
2025-09-24 17:32:04,828 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:32:04,828 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:32:04,828 - INFO - 
2025-09-24 17:32:04,828 - INFO - YC2ROS9NUB31VO0ABEV5.jpeg                          1      pack_list                                run1_YC2ROS9NUB31VO0ABEV5.json                                                  
2025-09-24 17:32:04,828 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:32:04,828 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:32:04,828 - INFO - 
2025-09-24 17:32:04,828 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       1      bol                                      run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:32:04,828 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:32:04,828 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:32:04,828 - INFO - 
2025-09-24 17:32:04,828 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       2      invoice                                  run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:32:04,828 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:32:04,828 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:32:04,828 - INFO - 
2025-09-24 17:32:04,829 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       3      invoice                                  run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:32:04,829 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:32:04,829 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:32:04,829 - INFO - 
2025-09-24 17:32:04,829 - INFO - bol_HEMTH4BHCWXWE044I0SK.pdf                       1      bol                                      run1_bol_HEMTH4BHCWXWE044I0SK.json                                              
2025-09-24 17:32:04,829 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:32:04,829 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:32:04,829 - INFO - 
2025-09-24 17:32:04,829 - INFO - bol_HEMTH4BHCWXWE044I0SK.pdf                       2      bol                                      run1_bol_HEMTH4BHCWXWE044I0SK.json                                              
2025-09-24 17:32:04,829 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:32:04,829 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:32:04,829 - INFO - 
2025-09-24 17:32:04,829 - INFO - bol_IJW9IJUOROYY1SWO0B17.pdf                       1      bol                                      run1_bol_IJW9IJUOROYY1SWO0B17.json                                              
2025-09-24 17:32:04,829 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:32:04,829 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:32:04,829 - INFO - 
2025-09-24 17:32:04,829 - INFO - bol_K88AFHJU4HIQUZYTMOF4.pdf                       1      bol                                      run1_bol_K88AFHJU4HIQUZYTMOF4.json                                              
2025-09-24 17:32:04,829 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:32:04,830 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:32:04,830 - INFO - 
2025-09-24 17:32:04,830 - INFO - bol_P2EZG64EJI98NT69TSAL.pdf                       1      bol                                      run1_bol_P2EZG64EJI98NT69TSAL.json                                              
2025-09-24 17:32:04,830 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:32:04,830 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:32:04,830 - INFO - 
2025-09-24 17:32:04,830 - INFO - bol_PK5AIZFCER7DUFL15N01.pdf                       1      bol                                      run1_bol_PK5AIZFCER7DUFL15N01.json                                              
2025-09-24 17:32:04,830 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:32:04,830 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:32:04,830 - INFO - 
2025-09-24 17:32:04,830 - INFO - bol_RCNPMJFRSZTXQWBGLB9R.pdf                       1      bol                                      run1_bol_RCNPMJFRSZTXQWBGLB9R.json                                              
2025-09-24 17:32:04,830 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:32:04,830 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:32:04,830 - INFO - 
2025-09-24 17:32:04,830 - INFO - pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg                1      pack_list                                run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json                                        
2025-09-24 17:32:04,830 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:32:04,830 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:32:04,831 - INFO - 
2025-09-24 17:32:04,831 - INFO - pack_list_P6RAGXD27RW4QUXN96TH.jpeg                1      pack_list                                run1_pack_list_P6RAGXD27RW4QUXN96TH.json                                        
2025-09-24 17:32:04,831 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:32:04,831 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:32:04,831 - INFO - 
2025-09-24 17:32:04,831 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:32:04,831 - INFO - Total entries: 21
2025-09-24 17:32:04,831 - INFO - ============================================================================================================================================
2025-09-24 17:32:04,831 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 17:32:04,831 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:32:04,831 - INFO -   1. AOYIL346IKT06LKO6D2R.jpg            Page 1   → pod             | run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:32:04,831 - INFO -   2. GKYUUU2YTZ1YTPGXHO51.pdf            Page 1   → pod             | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:32:04,831 - INFO -   3. GKYUUU2YTZ1YTPGXHO51.pdf            Page 2   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:32:04,831 - INFO -   4. GKYUUU2YTZ1YTPGXHO51.pdf            Page 3   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:32:04,831 - INFO -   5. I6IM526GOVKAWH0B5WKE.pdf            Page 1   → invoice         | run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:32:04,831 - INFO -   6. KK3PKFLS4ROTEZ7LOMMD.pdf            Page 1   → pod             | run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:32:04,831 - INFO -   7. S92OW12RYFF5G3READPW.pdf            Page 1   → invoice         | run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:32:04,831 - INFO -   8. TQSZJLFFGGKB4NFN12RD.pdf            Page 1   → pod             | run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:32:04,831 - INFO -   9. YC2ROS9NUB31VO0ABEV5.jpeg           Page 1   → pack_list       | run1_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:32:04,832 - INFO -  10. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 1   → bol             | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:32:04,832 - INFO -  11. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 2   → invoice         | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:32:04,832 - INFO -  12. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 3   → invoice         | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:32:04,832 - INFO -  13. bol_HEMTH4BHCWXWE044I0SK.pdf        Page 1   → bol             | run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:32:04,832 - INFO -  14. bol_HEMTH4BHCWXWE044I0SK.pdf        Page 2   → bol             | run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:32:04,832 - INFO -  15. bol_IJW9IJUOROYY1SWO0B17.pdf        Page 1   → bol             | run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:32:04,832 - INFO -  16. bol_K88AFHJU4HIQUZYTMOF4.pdf        Page 1   → bol             | run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:32:04,832 - INFO -  17. bol_P2EZG64EJI98NT69TSAL.pdf        Page 1   → bol             | run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:32:04,832 - INFO -  18. bol_PK5AIZFCER7DUFL15N01.pdf        Page 1   → bol             | run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:32:04,832 - INFO -  19. bol_RCNPMJFRSZTXQWBGLB9R.pdf        Page 1   → bol             | run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:32:04,832 - INFO -  20. pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg Page 1   → pack_list       | run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:32:04,832 - INFO -  21. pack_list_P6RAGXD27RW4QUXN96TH.jpeg Page 1   → pack_list       | run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:32:04,832 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:32:04,832 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 37.470034, 'processed_files': [{'filename': 'AOYIL346IKT06LKO6D2R.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg'}, {'filename': 'GKYUUU2YTZ1YTPGXHO51.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf'}, {'filename': 'I6IM526GOVKAWH0B5WKE.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf'}, {'filename': 'KK3PKFLS4ROTEZ7LOMMD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf'}, {'filename': 'S92OW12RYFF5G3READPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf'}, {'filename': 'TQSZJLFFGGKB4NFN12RD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf'}, {'filename': 'YC2ROS9NUB31VO0ABEV5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/YC2ROS9NUB31VO0ABEV5.jpeg'}, {'filename': 'bol_DV1B199A0TJGUQ3FETIU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf'}, {'filename': 'bol_HEMTH4BHCWXWE044I0SK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf'}, {'filename': 'bol_IJW9IJUOROYY1SWO0B17.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf'}, {'filename': 'bol_K88AFHJU4HIQUZYTMOF4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf'}, {'filename': 'bol_P2EZG64EJI98NT69TSAL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf'}, {'filename': 'bol_PK5AIZFCER7DUFL15N01.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf'}, {'filename': 'bol_RCNPMJFRSZTXQWBGLB9R.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf'}, {'filename': 'pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg'}, {'filename': 'pack_list_P6RAGXD27RW4QUXN96TH.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg'}]}
