{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9891355037689209, "Height": 0.9994568228721619, "Left": 0.0044363331981003284, "Top": 0.0}, "Polygon": [{"X": 0.0044363331981003284, "Y": 0.0}, {"X": 0.9934962391853333, "Y": 0.0}, {"X": 0.9935718178749084, "Y": 0.9994176626205444}, {"X": 0.0044953529722988605, "Y": 0.9994568228721619}]}, "Id": "392d09ff-eaa7-486b-bd20-6c8727373ed3", "Relationships": [{"Type": "CHILD", "Ids": ["f43b0138-feef-43ff-b4b8-650e107923e9", "f253ea5f-853b-4d60-a568-164fbee41c85", "7fb603d0-b62f-46ba-a593-7b981bf0e350", "389bc093-32e1-42f8-8a37-8c67e7f2e473", "46148757-012f-4af3-b13f-823e9592d06c", "9655bf76-4548-4d2c-b73b-80b093e43092", "63966042-aa38-4997-89d0-fdf61101d0bd", "eb6b5add-ce8b-48c6-9ba1-b76de5e127b4", "7f5fa8b5-b856-43d1-90df-e42862bb2fa5", "f4f9a2ad-cf9a-4645-9f84-af7cb4655d3a", "9a90e202-8c45-49dc-9ede-2d167decd126", "af414311-cbed-4a59-8760-9cea78b15131", "48544a0d-64ef-4768-81f1-c28d57105be0", "09f966b9-d1ce-44a2-96e8-bbe5bc8abed1", "21a5896b-9025-421f-8145-7410692c6675", "d8b7b86f-2c07-477a-83de-2b8dcfe3b240", "19ecf083-a061-4b22-b0e7-74ff8ce3c350", "e132ddef-faa0-40a3-994c-637f45d8bd26", "1990e856-598d-41b4-92b7-e1d6f0264b87", "48aabc02-986b-4fb7-84aa-1d8d4e9b0773", "d4c4ec51-e4c4-4863-9ba6-210b19945bd6", "260a9678-7237-4291-a803-32a2013343ac"]}]}, {"BlockType": "LINE", "Confidence": 54.36244583129883, "Text": "Silmon", "Geometry": {"BoundingBox": {"Width": 0.2921173572540283, "Height": 0.07360445708036423, "Left": 0.1974511444568634, "Top": 0.02767270617187023}, "Polygon": [{"X": 0.1974511444568634, "Y": 0.027681028470396996}, {"X": 0.48956355452537537, "Y": 0.02767270617187023}, {"X": 0.4895685017108917, "Y": 0.10126858949661255}, {"X": 0.1974557340145111, "Y": 0.10127715766429901}]}, "Id": "f43b0138-feef-43ff-b4b8-650e107923e9", "Relationships": [{"Type": "CHILD", "Ids": ["7c6a9283-77c1-416a-83b9-57a14a207352"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "32036", "Geometry": {"BoundingBox": {"Width": 0.11408688873052597, "Height": 0.024979395791888237, "Left": 0.6848393082618713, "Top": 0.047398727387189865}, "Polygon": [{"X": 0.6848393082618713, "Y": 0.04740200191736221}, {"X": 0.7989243865013123, "Y": 0.047398727387189865}, {"X": 0.7989261746406555, "Y": 0.07237481325864792}, {"X": 0.6848410367965698, "Y": 0.07237812131643295}]}, "Id": "f253ea5f-853b-4d60-a568-164fbee41c85", "Relationships": [{"Type": "CHILD", "Ids": ["abb8978f-63bb-4c02-ad10-91cc757565ea"]}]}, {"BlockType": "LINE", "Confidence": 89.75765228271484, "Text": "CONCESSION SUPPLIES", "Geometry": {"BoundingBox": {"Width": 0.17532150447368622, "Height": 0.030035974457859993, "Left": 0.2968471050262451, "Top": 0.09042897820472717}, "Polygon": [{"X": 0.2968471050262451, "Y": 0.09043410420417786}, {"X": 0.4721665680408478, "Y": 0.09042897820472717}, {"X": 0.47216859459877014, "Y": 0.12045977264642715}, {"X": 0.29684901237487793, "Y": 0.12046495825052261}]}, "Id": "7fb603d0-b62f-46ba-a593-7b981bf0e350", "Relationships": [{"Type": "CHILD", "Ids": ["64ee3d57-c867-4f17-b63a-a8e1ec7c21c0"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "209 CLAIBORNE", "Geometry": {"BoundingBox": {"Width": 0.18220408260822296, "Height": 0.015332836657762527, "Left": 0.6404649615287781, "Top": 0.0951630249619484}, "Polygon": [{"X": 0.6404649615287781, "Y": 0.0951683521270752}, {"X": 0.8226678967475891, "Y": 0.0951630249619484}, {"X": 0.8226690292358398, "Y": 0.11049049347639084}, {"X": 0.640466034412384, "Y": 0.11049585789442062}]}, "Id": "389bc093-32e1-42f8-8a37-8c67e7f2e473", "Relationships": [{"Type": "CHILD", "Ids": ["a6f54ab1-919b-43e6-85ea-66b0c245db6e", "7ca8fef0-761f-4b28-b7a9-3d75372a5fb1"]}]}, {"BlockType": "LINE", "Confidence": 98.5985336303711, "Text": "WEST MONROE, LA", "Geometry": {"BoundingBox": {"Width": 0.10574501752853394, "Height": 0.008058500476181507, "Left": 0.15491849184036255, "Top": 0.11233442276716232}, "Polygon": [{"X": 0.15491849184036255, "Y": 0.11233753710985184}, {"X": 0.2606630027294159, "Y": 0.11233442276716232}, {"X": 0.2606635093688965, "Y": 0.12038979679346085}, {"X": 0.15491898357868195, "Y": 0.12039292603731155}]}, "Id": "46148757-012f-4af3-b13f-823e9592d06c", "Relationships": [{"Type": "CHILD", "Ids": ["428a8d78-14fb-4b56-b34d-4f1e0de56fe8", "f90e6aad-7dee-418f-987e-b845ef7f6965", "00b3fd81-7242-4117-8f22-31593bbf3215"]}]}, {"BlockType": "LINE", "Confidence": 95.63626098632812, "Text": "(318) 322-2661", "Geometry": {"BoundingBox": {"Width": 0.08560341596603394, "Height": 0.00829710066318512, "Left": 0.16677160561084747, "Top": 0.12365603446960449}, "Polygon": [{"X": 0.16677160561084747, "Y": 0.12365856766700745}, {"X": 0.2523745000362396, "Y": 0.12365603446960449}, {"X": 0.2523750066757202, "Y": 0.13195058703422546}, {"X": 0.16677211225032806, "Y": 0.1319531351327896}]}, "Id": "9655bf76-4548-4d2c-b73b-80b093e43092", "Relationships": [{"Type": "CHILD", "Ids": ["b0508fb0-4eb8-4482-a55a-8457afe867e1", "9e47a345-deb1-48e7-92a6-840fadf49ffc"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "WEST MONROE, LA 71291", "Geometry": {"BoundingBox": {"Width": 0.290327250957489, "Height": 0.01737060397863388, "Left": 0.5831807851791382, "Top": 0.11627984046936035}, "Polygon": [{"X": 0.5831807851791382, "Y": 0.11628840863704681}, {"X": 0.8735067844390869, "Y": 0.11627984046936035}, {"X": 0.8735080361366272, "Y": 0.1336418241262436}, {"X": 0.5831819772720337, "Y": 0.13365045189857483}]}, "Id": "63966042-aa38-4997-89d0-fdf61101d0bd", "Relationships": [{"Type": "CHILD", "Ids": ["8effc288-b2dd-42e5-a6cb-9d6504c43626", "646c6330-8dad-492c-9360-67e85519f65f", "8ebaabe0-f9b2-414c-a6f2-dcbd9cf0e54a", "9242d6af-f22a-431a-b76c-f8c90095f404"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "318-322-2661", "Geometry": {"BoundingBox": {"Width": 0.14840681850910187, "Height": 0.014620110392570496, "Left": 0.6546400189399719, "Top": 0.13810864090919495}, "Polygon": [{"X": 0.6546400189399719, "Y": 0.13811305165290833}, {"X": 0.8030457496643066, "Y": 0.13810864090919495}, {"X": 0.8030468225479126, "Y": 0.15272431075572968}, {"X": 0.6546410322189331, "Y": 0.15272875130176544}]}, "Id": "eb6b5add-ce8b-48c6-9ba1-b76de5e127b4", "Relationships": [{"Type": "CHILD", "Ids": ["73235d66-9106-46ab-9925-e862b4f34ef8"]}]}, {"BlockType": "LINE", "Confidence": 99.7265625, "Text": "FAX 318-323-9357", "Geometry": {"BoundingBox": {"Width": 0.19954000413417816, "Height": 0.014901816844940186, "Left": 0.6307929754257202, "Top": 0.15931813418865204}, "Polygon": [{"X": 0.6307929754257202, "Y": 0.15932410955429077}, {"X": 0.8303319215774536, "Y": 0.15931813418865204}, {"X": 0.8303329944610596, "Y": 0.1742139309644699}, {"X": 0.6307940483093262, "Y": 0.17421995103359222}]}, "Id": "7f5fa8b5-b856-43d1-90df-e42862bb2fa5", "Relationships": [{"Type": "CHILD", "Ids": ["ba1600e2-dc0f-46d8-ac9c-74b43e443725", "aa6884d4-9a2e-419c-9a94-232e1e792af2"]}]}, {"BlockType": "LINE", "Confidence": 78.97520446777344, "Text": "DATE 7-25-2520", "Geometry": {"BoundingBox": {"Width": 0.32298341393470764, "Height": 0.036257773637771606, "Left": 0.5074390172958374, "Top": 0.17893552780151367}, "Polygon": [{"X": 0.5074390172958374, "Y": 0.17894528806209564}, {"X": 0.8304197788238525, "Y": 0.17893552780151367}, {"X": 0.8304224014282227, "Y": 0.21518340706825256}, {"X": 0.5074414610862732, "Y": 0.21519330143928528}]}, "Id": "f4f9a2ad-cf9a-4645-9f84-af7cb4655d3a", "Relationships": [{"Type": "CHILD", "Ids": ["16e4af6c-13c9-45fa-8c87-fcc4dbbb1a34", "fbc74b60-a431-4b9b-99ff-dd6c24280eef"]}]}, {"BlockType": "LINE", "Confidence": 99.83119201660156, "Text": "SOLD TO", "Geometry": {"BoundingBox": {"Width": 0.10394595563411713, "Height": 0.014508751221001148, "Left": 0.10745946317911148, "Top": 0.21596278250217438}, "Polygon": [{"X": 0.10745946317911148, "Y": 0.21596597135066986}, {"X": 0.21140451729297638, "Y": 0.21596278250217438}, {"X": 0.211405411362648, "Y": 0.23046833276748657}, {"X": 0.10746034234762192, "Y": 0.23047153651714325}]}, "Id": "9a90e202-8c45-49dc-9ede-2d167decd126", "Relationships": [{"Type": "CHILD", "Ids": ["cf2078a1-d5aa-45d6-a165-e1a44b8ce722", "a3cec757-b404-4aaa-a8f4-0e6a8dd44391"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "QUAN.", "Geometry": {"BoundingBox": {"Width": 0.05463389307260513, "Height": 0.010962341912090778, "Left": 0.11531512439250946, "Top": 0.23868289589881897}, "Polygon": [{"X": 0.11531512439250946, "Y": 0.23868457973003387}, {"X": 0.16994833946228027, "Y": 0.23868289589881897}, {"X": 0.169949010014534, "Y": 0.24964353442192078}, {"X": 0.11531578749418259, "Y": 0.24964523315429688}]}, "Id": "af414311-cbed-4a59-8760-9cea78b15131", "Relationships": [{"Type": "CHILD", "Ids": ["25eeaac0-59d0-40f9-8881-985a2855771b"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "ITEM", "Geometry": {"BoundingBox": {"Width": 0.039972543716430664, "Height": 0.010415595024824142, "Left": 0.36513346433639526, "Top": 0.23995952308177948}, "Polygon": [{"X": 0.36513346433639526, "Y": 0.23996075987815857}, {"X": 0.405105322599411, "Y": 0.23995952308177948}, {"X": 0.4051060080528259, "Y": 0.25037387013435364}, {"X": 0.3651341497898102, "Y": 0.2503751218318939}]}, "Id": "48544a0d-64ef-4768-81f1-c28d57105be0", "Relationships": [{"Type": "CHILD", "Ids": ["8a669112-1b70-4eac-b5a5-a6f57769c8a0"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "COST", "Geometry": {"BoundingBox": {"Width": 0.04852243885397911, "Height": 0.010716155171394348, "Left": 0.6031320691108704, "Top": 0.2402563989162445}, "Polygon": [{"X": 0.6031320691108704, "Y": 0.2402578890323639}, {"X": 0.6516537666320801, "Y": 0.2402563989162445}, {"X": 0.6516545414924622, "Y": 0.2509710490703583}, {"X": 0.6031328439712524, "Y": 0.25097253918647766}]}, "Id": "09f966b9-d1ce-44a2-96e8-bbe5bc8abed1", "Relationships": [{"Type": "CHILD", "Ids": ["4a12ef9e-226a-4b28-b9c8-8d0c59b1c0e1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "SELL", "Geometry": {"BoundingBox": {"Width": 0.04177948087453842, "Height": 0.010572502389550209, "Left": 0.6833388805389404, "Top": 0.24037060141563416}, "Polygon": [{"X": 0.6833388805389404, "Y": 0.24037189781665802}, {"X": 0.7251176238059998, "Y": 0.24037060141563416}, {"X": 0.7251183390617371, "Y": 0.25094181299209595}, {"X": 0.6833396553993225, "Y": 0.2509430944919586}]}, "Id": "21a5896b-9025-421f-8145-7410692c6675", "Relationships": [{"Type": "CHILD", "Ids": ["7356ea74-f49e-4760-8285-836689c21ef3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.05406275764107704, "Height": 0.010945531539618969, "Left": 0.7862653136253357, "Top": 0.2400636225938797}, "Polygon": [{"X": 0.7862653136253357, "Y": 0.2400652915239334}, {"X": 0.840327262878418, "Y": 0.2400636225938797}, {"X": 0.8403280973434448, "Y": 0.25100746750831604}, {"X": 0.7862661480903625, "Y": 0.25100916624069214}]}, "Id": "d8b7b86f-2c07-477a-83de-2b8dcfe3b240", "Relationships": [{"Type": "CHILD", "Ids": ["7ea3b2ba-444c-4876-834f-ddff96921727"]}]}, {"BlockType": "LINE", "Confidence": 99.36264038085938, "Text": "<PERSON>mper Fee", "Geometry": {"BoundingBox": {"Width": 0.5441909432411194, "Height": 0.07441578805446625, "Left": 0.20469434559345245, "Top": 0.2917174994945526}, "Polygon": [{"X": 0.20469434559345245, "Y": 0.2917346656322479}, {"X": 0.7488799691200256, "Y": 0.2917174994945526}, {"X": 0.7488852739334106, "Y": 0.3661156892776489}, {"X": 0.20469897985458374, "Y": 0.36613330245018005}]}, "Id": "19ecf083-a061-4b22-b0e7-74ff8ce3c350", "Relationships": [{"Type": "CHILD", "Ids": ["5bb576a1-fc65-45f7-aabe-7c8f46a42985", "1be07462-85fe-4a5c-92bb-3e51f8cfef71"]}]}, {"BlockType": "LINE", "Confidence": 69.99043273925781, "Text": "$250", "Geometry": {"BoundingBox": {"Width": 0.3842025399208069, "Height": 0.1000114381313324, "Left": 0.20637500286102295, "Top": 0.38391613960266113}, "Polygon": [{"X": 0.20637500286102295, "Y": 0.38392865657806396}, {"X": 0.5905706882476807, "Y": 0.38391613960266113}, {"X": 0.5905775427818298, "Y": 0.48391464352607727}, {"X": 0.20638124644756317, "Y": 0.48392757773399353}]}, "Id": "e132ddef-faa0-40a3-994c-637f45d8bd26", "Relationships": [{"Type": "CHILD", "Ids": ["d134b509-4919-46bf-b259-75e03af13863"]}]}, {"BlockType": "LINE", "Confidence": 42.87687301635742, "Text": "SilMon's", "Geometry": {"BoundingBox": {"Width": 0.37623241543769836, "Height": 0.0811411440372467, "Left": 0.28149402141571045, "Top": 0.6181434988975525}, "Polygon": [{"X": 0.28149402141571045, "Y": 0.6181567907333374}, {"X": 0.6577208042144775, "Y": 0.6181434988975525}, {"X": 0.6577264666557312, "Y": 0.699271023273468}, {"X": 0.2814992070198059, "Y": 0.6992846727371216}]}, "Id": "1990e856-598d-41b4-92b7-e1d6f0264b87", "Relationships": [{"Type": "CHILD", "Ids": ["6e5368db-2949-486e-a578-4c7af2389527"]}]}, {"BlockType": "LINE", "Confidence": 96.6690902709961, "Text": "PEREGRINE CORPORATION TO REORDER CALL 318-325-4788", "Geometry": {"BoundingBox": {"Width": 0.3961341977119446, "Height": 0.010896729305386543, "Left": 0.3170023560523987, "Top": 0.9216983318328857}, "Polygon": [{"X": 0.3170023560523987, "Y": 0.921713650226593}, {"X": 0.7131357789039612, "Y": 0.9216983318328857}, {"X": 0.7131365537643433, "Y": 0.9325796961784363}, {"X": 0.317003071308136, "Y": 0.9325950741767883}]}, "Id": "48aabc02-986b-4fb7-84aa-1d8d4e9b0773", "Relationships": [{"Type": "CHILD", "Ids": ["95bf332b-aa48-4eb1-aab6-2f14c2971fb3", "f3e5ef6e-356b-4a5b-874b-799215849b76", "a24bc33e-5bf8-4aba-ba0c-b2adf2d5391c", "ad7f91e4-904c-41d2-b35b-83045742f218", "7d0ef7fa-0b60-4bb3-bc2a-64c5700902ef", "009980ab-c81b-48ab-8f38-b556847970ad"]}]}, {"BlockType": "LINE", "Confidence": 99.14082336425781, "Text": "CS", "Geometry": {"BoundingBox": {"Width": 0.029330965131521225, "Height": 0.01236520241945982, "Left": 0.8191398978233337, "Top": 0.9675211906433105}, "Polygon": [{"X": 0.8191398978233337, "Y": 0.9675223231315613}, {"X": 0.8484699726104736, "Y": 0.9675211906433105}, {"X": 0.8484708666801453, "Y": 0.9798852205276489}, {"X": 0.8191407918930054, "Y": 0.9798863530158997}]}, "Id": "d4c4ec51-e4c4-4863-9ba6-210b19945bd6", "Relationships": [{"Type": "CHILD", "Ids": ["d1500261-1d40-429a-be1e-2b0c87fa467f"]}]}, {"BlockType": "LINE", "Confidence": 99.31819915771484, "Text": "CamScanner", "Geometry": {"BoundingBox": {"Width": 0.11248935014009476, "Height": 0.012357148341834545, "Left": 0.8568819165229797, "Top": 0.9708267450332642}, "Polygon": [{"X": 0.8568819165229797, "Y": 0.9708311557769775}, {"X": 0.9693703055381775, "Y": 0.9708267450332642}, {"X": 0.9693712592124939, "Y": 0.9831794500350952}, {"X": 0.8568828105926514, "Y": 0.9831838607788086}]}, "Id": "260a9678-7237-4291-a803-32a2013343ac", "Relationships": [{"Type": "CHILD", "Ids": ["b0e19954-88b5-4cef-b71a-b11593395dc7"]}]}, {"BlockType": "WORD", "Confidence": 54.36244583129883, "Text": "Silmon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2921173572540283, "Height": 0.07360445708036423, "Left": 0.1974511444568634, "Top": 0.02767270617187023}, "Polygon": [{"X": 0.1974511444568634, "Y": 0.027681028470396996}, {"X": 0.48956355452537537, "Y": 0.02767270617187023}, {"X": 0.4895685017108917, "Y": 0.10126858949661255}, {"X": 0.1974557340145111, "Y": 0.10127715766429901}]}, "Id": "7c6a9283-77c1-416a-83b9-57a14a207352"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "32036", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11408688873052597, "Height": 0.024979395791888237, "Left": 0.6848393082618713, "Top": 0.047398727387189865}, "Polygon": [{"X": 0.6848393082618713, "Y": 0.04740200191736221}, {"X": 0.7989243865013123, "Y": 0.047398727387189865}, {"X": 0.7989261746406555, "Y": 0.07237481325864792}, {"X": 0.6848410367965698, "Y": 0.07237812131643295}]}, "Id": "abb8978f-63bb-4c02-ad10-91cc757565ea"}, {"BlockType": "WORD", "Confidence": 89.75765228271484, "Text": "CONCESSION SUPPLIES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17532150447368622, "Height": 0.030035974457859993, "Left": 0.2968471050262451, "Top": 0.09042897820472717}, "Polygon": [{"X": 0.2968471050262451, "Y": 0.09043410420417786}, {"X": 0.4721665680408478, "Y": 0.09042897820472717}, {"X": 0.47216859459877014, "Y": 0.12045977264642715}, {"X": 0.29684901237487793, "Y": 0.12046495825052261}]}, "Id": "64ee3d57-c867-4f17-b63a-a8e1ec7c21c0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "209", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04046572372317314, "Height": 0.014486136846244335, "Left": 0.6404649615287781, "Top": 0.0952616035938263}, "Polygon": [{"X": 0.6404649615287781, "Y": 0.0952627882361412}, {"X": 0.6809296607971191, "Y": 0.0952616035938263}, {"X": 0.6809306740760803, "Y": 0.1097465455532074}, {"X": 0.6404659748077393, "Y": 0.1097477376461029}]}, "Id": "a6f54ab1-919b-43e6-85ea-66b0c245db6e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CLAIBORNE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13539917767047882, "Height": 0.015331458300352097, "Left": 0.6872698664665222, "Top": 0.0951630249619484}, "Polygon": [{"X": 0.6872698664665222, "Y": 0.09516698122024536}, {"X": 0.8226678967475891, "Y": 0.0951630249619484}, {"X": 0.8226690292358398, "Y": 0.11049049347639084}, {"X": 0.6872709393501282, "Y": 0.11049447953701019}]}, "Id": "7ca8fef0-761f-4b28-b7a9-3d75372a5fb1"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "WEST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02967119589447975, "Height": 0.007347204722464085, "Left": 0.15491849184036255, "Top": 0.11233802139759064}, "Polygon": [{"X": 0.15491849184036255, "Y": 0.11233889311552048}, {"X": 0.184589222073555, "Y": 0.11233802139759064}, {"X": 0.184589684009552, "Y": 0.11968434602022171}, {"X": 0.15491893887519836, "Y": 0.11968522518873215}]}, "Id": "428a8d78-14fb-4b56-b34d-4f1e0de56fe8"}, {"BlockType": "WORD", "Confidence": 96.70380401611328, "Text": "MONROE,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05514479801058769, "Height": 0.008057006634771824, "Left": 0.18716715276241302, "Top": 0.1123349666595459}, "Polygon": [{"X": 0.18716715276241302, "Y": 0.11233659088611603}, {"X": 0.24231144785881042, "Y": 0.1123349666595459}, {"X": 0.24231195449829102, "Y": 0.12039034068584442}, {"X": 0.18716765940189362, "Y": 0.12039197236299515}]}, "Id": "f90e6aad-7dee-418f-987e-b845ef7f6965"}, {"BlockType": "WORD", "Confidence": 99.404296875, "Text": "LA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01490211021155119, "Height": 0.007556072901934385, "Left": 0.24576137959957123, "Top": 0.11254320293664932}, "Polygon": [{"X": 0.24576137959957123, "Y": 0.11254364252090454}, {"X": 0.2606630027294159, "Y": 0.11254320293664932}, {"X": 0.2606634795665741, "Y": 0.12009883671998978}, {"X": 0.24576185643672943, "Y": 0.120099276304245}]}, "Id": "00b3fd81-7242-4117-8f22-31593bbf3215"}, {"BlockType": "WORD", "Confidence": 97.15501403808594, "Text": "(318)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029830818995833397, "Height": 0.008295450359582901, "Left": 0.16677160561084747, "Top": 0.12365768104791641}, "Polygon": [{"X": 0.16677160561084747, "Y": 0.12365856766700745}, {"X": 0.19660189747810364, "Y": 0.12365768104791641}, {"X": 0.19660241901874542, "Y": 0.13195225596427917}, {"X": 0.16677211225032806, "Y": 0.1319531351327896}]}, "Id": "b0508fb0-4eb8-4482-a55a-8457afe867e1"}, {"BlockType": "WORD", "Confidence": 94.11750793457031, "Text": "322-2661", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05180995911359787, "Height": 0.007270782254636288, "Left": 0.20056502521038055, "Top": 0.124114029109478}, "Polygon": [{"X": 0.20056502521038055, "Y": 0.12411556392908096}, {"X": 0.252374529838562, "Y": 0.124114029109478}, {"X": 0.2523749768733978, "Y": 0.1313832700252533}, {"X": 0.20056547224521637, "Y": 0.13138481974601746}]}, "Id": "9e47a345-deb1-48e7-92a6-840fadf49ffc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06874241679906845, "Height": 0.015109334141016006, "Left": 0.5831807851791382, "Top": 0.11628638207912445}, "Polygon": [{"X": 0.5831807851791382, "Y": 0.11628840863704681}, {"X": 0.6519221663475037, "Y": 0.11628638207912445}, {"X": 0.6519231796264648, "Y": 0.1313936710357666}, {"X": 0.5831817984580994, "Y": 0.13139571249485016}]}, "Id": "8effc288-b2dd-42e5-a6cb-9d6504c43626"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "MONROE,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11122560501098633, "Height": 0.01722722314298153, "Left": 0.6573342680931091, "Top": 0.11642102152109146}, "Polygon": [{"X": 0.6573342680931091, "Y": 0.11642429977655411}, {"X": 0.7685586810112, "Y": 0.11642102152109146}, {"X": 0.7685598731040955, "Y": 0.1336449384689331}, {"X": 0.6573355197906494, "Y": 0.13364824652671814}]}, "Id": "646c6330-8dad-492c-9360-67e85519f65f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02893340028822422, "Height": 0.014988679438829422, "Left": 0.776194155216217, "Top": 0.11665290594100952}, "Polygon": [{"X": 0.776194155216217, "Y": 0.11665375530719757}, {"X": 0.8051264882087708, "Y": 0.11665290594100952}, {"X": 0.8051275610923767, "Y": 0.1316407173871994}, {"X": 0.776195228099823, "Y": 0.13164158165454865}]}, "Id": "8ebaabe0-f9b2-414c-a6f2-dcbd9cf0e54a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "71291", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06256172806024551, "Height": 0.014902083203196526, "Left": 0.8109461665153503, "Top": 0.11717959493398666}, "Polygon": [{"X": 0.8109461665153503, "Y": 0.1171814426779747}, {"X": 0.8735068440437317, "Y": 0.11717959493398666}, {"X": 0.8735079169273376, "Y": 0.1320798248052597}, {"X": 0.8109472990036011, "Y": 0.13208168745040894}]}, "Id": "9242d6af-f22a-431a-b76c-f8c90095f404"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "318-322-2661", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14840681850910187, "Height": 0.014620110392570496, "Left": 0.6546400189399719, "Top": 0.13810864090919495}, "Polygon": [{"X": 0.6546400189399719, "Y": 0.13811305165290833}, {"X": 0.8030457496643066, "Y": 0.13810864090919495}, {"X": 0.8030468225479126, "Y": 0.15272431075572968}, {"X": 0.6546410322189331, "Y": 0.15272875130176544}]}, "Id": "73235d66-9106-46ab-9925-e862b4f34ef8"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "FAX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04227934032678604, "Height": 0.014599584974348545, "Left": 0.6307929754257202, "Top": 0.1593228429555893}, "Polygon": [{"X": 0.6307929754257202, "Y": 0.15932410955429077}, {"X": 0.6730713248252869, "Y": 0.1593228429555893}, {"X": 0.673072338104248, "Y": 0.1739211529493332}, {"X": 0.6307939887046814, "Y": 0.17392243444919586}]}, "Id": "ba1600e2-dc0f-46d8-ac9c-74b43e443725"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "318-323-9357", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15203413367271423, "Height": 0.014885973185300827, "Left": 0.678298830986023, "Top": 0.1593325436115265}, "Polygon": [{"X": 0.678298830986023, "Y": 0.1593371033668518}, {"X": 0.8303319215774536, "Y": 0.1593325436115265}, {"X": 0.8303329944610596, "Y": 0.1742139309644699}, {"X": 0.6782999038696289, "Y": 0.17421850562095642}]}, "Id": "aa6884d4-9a2e-419c-9a94-232e1e792af2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0585799477994442, "Height": 0.014382990077137947, "Left": 0.5074401497840881, "Top": 0.19611932337284088}, "Polygon": [{"X": 0.5074401497840881, "Y": 0.19612109661102295}, {"X": 0.5660191178321838, "Y": 0.19611932337284088}, {"X": 0.566020131111145, "Y": 0.21050050854682922}, {"X": 0.5074411630630493, "Y": 0.21050231158733368}]}, "Id": "16e4af6c-13c9-45fa-8c87-fcc4dbbb1a34"}, {"BlockType": "WORD", "Confidence": 57.97970962524414, "Text": "7-25-2520", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.24332945048809052, "Height": 0.036255333572626114, "Left": 0.5870929956436157, "Top": 0.17893552780151367}, "Polygon": [{"X": 0.5870929956436157, "Y": 0.17894288897514343}, {"X": 0.8304197788238525, "Y": 0.17893552780151367}, {"X": 0.8304224014282227, "Y": 0.21518340706825256}, {"X": 0.5870954990386963, "Y": 0.2151908576488495}]}, "Id": "fbc74b60-a431-4b9b-99ff-dd6c24280eef"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SOLD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06511842459440231, "Height": 0.014226775616407394, "Left": 0.10745946317911148, "Top": 0.21596397459506989}, "Polygon": [{"X": 0.10745946317911148, "Y": 0.21596597135066986}, {"X": 0.17257700860500336, "Y": 0.21596397459506989}, {"X": 0.1725778877735138, "Y": 0.2301887422800064}, {"X": 0.10746032744646072, "Y": 0.23019075393676758}]}, "Id": "cf2078a1-d5aa-45d6-a165-e1a44b8ce722"}, {"BlockType": "WORD", "Confidence": 99.67215728759766, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033328887075185776, "Height": 0.014180868864059448, "Left": 0.17807653546333313, "Top": 0.2162884920835495}, "Polygon": [{"X": 0.17807653546333313, "Y": 0.21628950536251068}, {"X": 0.21140453219413757, "Y": 0.2162884920835495}, {"X": 0.211405411362648, "Y": 0.23046833276748657}, {"X": 0.17807741463184357, "Y": 0.23046936094760895}]}, "Id": "a3cec757-b404-4aaa-a8f4-0e6a8dd44391"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "QUAN.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05463389307260513, "Height": 0.010962341912090778, "Left": 0.11531512439250946, "Top": 0.23868289589881897}, "Polygon": [{"X": 0.11531512439250946, "Y": 0.23868457973003387}, {"X": 0.16994833946228027, "Y": 0.23868289589881897}, {"X": 0.169949010014534, "Y": 0.24964353442192078}, {"X": 0.11531578749418259, "Y": 0.24964523315429688}]}, "Id": "25eeaac0-59d0-40f9-8881-985a2855771b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "ITEM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039972543716430664, "Height": 0.010415595024824142, "Left": 0.36513346433639526, "Top": 0.23995952308177948}, "Polygon": [{"X": 0.36513346433639526, "Y": 0.23996075987815857}, {"X": 0.405105322599411, "Y": 0.23995952308177948}, {"X": 0.4051060080528259, "Y": 0.25037387013435364}, {"X": 0.3651341497898102, "Y": 0.2503751218318939}]}, "Id": "8a669112-1b70-4eac-b5a5-a6f57769c8a0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "COST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04852243885397911, "Height": 0.010716155171394348, "Left": 0.6031320691108704, "Top": 0.2402563989162445}, "Polygon": [{"X": 0.6031320691108704, "Y": 0.2402578890323639}, {"X": 0.6516537666320801, "Y": 0.2402563989162445}, {"X": 0.6516545414924622, "Y": 0.2509710490703583}, {"X": 0.6031328439712524, "Y": 0.25097253918647766}]}, "Id": "4a12ef9e-226a-4b28-b9c8-8d0c59b1c0e1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SELL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04177948087453842, "Height": 0.010572502389550209, "Left": 0.6833388805389404, "Top": 0.24037060141563416}, "Polygon": [{"X": 0.6833388805389404, "Y": 0.24037189781665802}, {"X": 0.7251176238059998, "Y": 0.24037060141563416}, {"X": 0.7251183390617371, "Y": 0.25094181299209595}, {"X": 0.6833396553993225, "Y": 0.2509430944919586}]}, "Id": "7356ea74-f49e-4760-8285-836689c21ef3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05406275764107704, "Height": 0.010945531539618969, "Left": 0.7862653136253357, "Top": 0.2400636225938797}, "Polygon": [{"X": 0.7862653136253357, "Y": 0.2400652915239334}, {"X": 0.840327262878418, "Y": 0.2400636225938797}, {"X": 0.8403280973434448, "Y": 0.25100746750831604}, {"X": 0.7862661480903625, "Y": 0.25100916624069214}]}, "Id": "7ea3b2ba-444c-4876-834f-ddff96921727"}, {"BlockType": "WORD", "Confidence": 98.83430480957031, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.3118745684623718, "Height": 0.06314363330602646, "Left": 0.20469504594802856, "Top": 0.302989661693573}, "Polygon": [{"X": 0.20469504594802856, "Y": 0.3029995262622833}, {"X": 0.5165653228759766, "Y": 0.302989661693573}, {"X": 0.5165696144104004, "Y": 0.3661231994628906}, {"X": 0.20469897985458374, "Y": 0.36613330245018005}]}, "Id": "5bb576a1-fc65-45f7-aabe-7c8f46a42985"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Fee", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16419591009616852, "Height": 0.06548695266246796, "Left": 0.5846887230873108, "Top": 0.2917174994945526}, "Polygon": [{"X": 0.5846887230873108, "Y": 0.29172268509864807}, {"X": 0.7488799691200256, "Y": 0.2917174994945526}, {"X": 0.7488846778869629, "Y": 0.35719916224479675}, {"X": 0.5846932530403137, "Y": 0.35720446705818176}]}, "Id": "1be07462-85fe-4a5c-92bb-3e51f8cfef71"}, {"BlockType": "WORD", "Confidence": 69.99043273925781, "Text": "$250", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.3842025399208069, "Height": 0.1000114381313324, "Left": 0.20637500286102295, "Top": 0.38391613960266113}, "Polygon": [{"X": 0.20637500286102295, "Y": 0.38392865657806396}, {"X": 0.5905706882476807, "Y": 0.38391613960266113}, {"X": 0.5905775427818298, "Y": 0.48391464352607727}, {"X": 0.20638124644756317, "Y": 0.48392757773399353}]}, "Id": "d134b509-4919-46bf-b259-75e03af13863"}, {"BlockType": "WORD", "Confidence": 42.87687301635742, "Text": "SilMon's", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.37623241543769836, "Height": 0.0811411440372467, "Left": 0.28149402141571045, "Top": 0.6181434988975525}, "Polygon": [{"X": 0.28149402141571045, "Y": 0.6181567907333374}, {"X": 0.6577208042144775, "Y": 0.6181434988975525}, {"X": 0.6577264666557312, "Y": 0.699271023273468}, {"X": 0.2814992070198059, "Y": 0.6992846727371216}]}, "Id": "6e5368db-2949-486e-a578-4c7af2389527"}, {"BlockType": "WORD", "Confidence": 81.9541244506836, "Text": "PEREGRINE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.076560840010643, "Height": 0.009515229612588882, "Left": 0.31700244545936584, "Top": 0.9230798482894897}, "Polygon": [{"X": 0.31700244545936584, "Y": 0.9230827689170837}, {"X": 0.3935626745223999, "Y": 0.9230798482894897}, {"X": 0.39356330037117004, "Y": 0.9325920939445496}, {"X": 0.317003071308136, "Y": 0.9325950741767883}]}, "Id": "95bf332b-aa48-4eb1-aab6-2f14c2971fb3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "CORPORATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0978667214512825, "Height": 0.009504728950560093, "Left": 0.39583903551101685, "Top": 0.9229446053504944}, "Polygon": [{"X": 0.39583903551101685, "Y": 0.92294842004776}, {"X": 0.4937051236629486, "Y": 0.9229446053504944}, {"X": 0.49370574951171875, "Y": 0.9324455261230469}, {"X": 0.395839661359787, "Y": 0.9324493408203125}]}, "Id": "f3e5ef6e-356b-4a5b-874b-799215849b76"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017969990149140358, "Height": 0.009262333624064922, "Left": 0.5129005312919617, "Top": 0.9227567315101624}, "Polygon": [{"X": 0.5129005312919617, "Y": 0.9227574467658997}, {"X": 0.5308698415756226, "Y": 0.9227567315101624}, {"X": 0.5308704972267151, "Y": 0.9320183992385864}, {"X": 0.5129011273384094, "Y": 0.932019054889679}]}, "Id": "a24bc33e-5bf8-4aba-ba0c-b2adf2d5391c"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "REORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06331741809844971, "Height": 0.009260749444365501, "Left": 0.5325835347175598, "Top": 0.9224567413330078}, "Polygon": [{"X": 0.5325835347175598, "Y": 0.9224591851234436}, {"X": 0.5959003567695618, "Y": 0.9224567413330078}, {"X": 0.5959009528160095, "Y": 0.9317150115966797}, {"X": 0.5325841903686523, "Y": 0.9317174553871155}]}, "Id": "ad7f91e4-904c-41d2-b35b-83045742f218"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "CALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03383259475231171, "Height": 0.00924746971577406, "Left": 0.5986921191215515, "Top": 0.9223136901855469}, "Polygon": [{"X": 0.5986921191215515, "Y": 0.9223150014877319}, {"X": 0.6325240731239319, "Y": 0.9223136901855469}, {"X": 0.6325247287750244, "Y": 0.9315598607063293}, {"X": 0.598692774772644, "Y": 0.9315611720085144}]}, "Id": "7d0ef7fa-0b60-4bb3-bc2a-64c5700902ef"}, {"BlockType": "WORD", "Confidence": 98.9269790649414, "Text": "318-325-4788", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07802277058362961, "Height": 0.009221493266522884, "Left": 0.6351136565208435, "Top": 0.9216983318328857}, "Polygon": [{"X": 0.6351136565208435, "Y": 0.9217013716697693}, {"X": 0.7131357789039612, "Y": 0.9216983318328857}, {"X": 0.7131364345550537, "Y": 0.9309167861938477}, {"X": 0.635114312171936, "Y": 0.9309198260307312}]}, "Id": "009980ab-c81b-48ab-8f38-b556847970ad"}, {"BlockType": "WORD", "Confidence": 99.14082336425781, "Text": "CS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029330965131521225, "Height": 0.01236520241945982, "Left": 0.8191398978233337, "Top": 0.9675211906433105}, "Polygon": [{"X": 0.8191398978233337, "Y": 0.9675223231315613}, {"X": 0.8484699726104736, "Y": 0.9675211906433105}, {"X": 0.8484708666801453, "Y": 0.9798852205276489}, {"X": 0.8191407918930054, "Y": 0.9798863530158997}]}, "Id": "d1500261-1d40-429a-be1e-2b0c87fa467f"}, {"BlockType": "WORD", "Confidence": 99.31819915771484, "Text": "CamScanner", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11248935014009476, "Height": 0.012357148341834545, "Left": 0.8568819165229797, "Top": 0.9708267450332642}, "Polygon": [{"X": 0.8568819165229797, "Y": 0.9708311557769775}, {"X": 0.9693703055381775, "Y": 0.9708267450332642}, {"X": 0.9693712592124939, "Y": 0.9831794500350952}, {"X": 0.8568828105926514, "Y": 0.9831838607788086}]}, "Id": "b0e19954-88b5-4cef-b71a-b11593395dc7"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "56d2fe4d-40fb-47ad-be6a-124b1531c151", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "56d2fe4d-40fb-47ad-be6a-124b1531c151", "content-type": "application/x-amz-json-1.1", "content-length": "31957", "date": "Thu, 18 Sep 2025 10:34:01 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "acfc10e0-76b3-4e6a-a1af-2c18e1d35307", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:34:03 GMT", "content-type": "application/json", "content-length": "1003", "connection": "keep-alive", "x-amzn-requestid": "acfc10e0-76b3-4e6a-a1af-2c18e1d35307"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It looks like an invoice? It has \"Lumper Fee $250\". It says \"SOLD TO\". Items, cost, sell, total. Looks like a lumper receipt? It has \"Lumper Fee $250\". The description says: lumper receipt: \"If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\" It has \"Lumper Fee\". So likely lumper_receipt. There's no \"Invoice\" header. It's a charge for lumper. So doc_type = lumper_receipt.\n\nWe need to output JSON via tool call. Single page. Provide array of documents: page_no and doc_type."}}}, {"toolUse": {"toolUseId": "tooluse_cM0UVeQ1SLOSPTnZy4opNA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2269, "outputTokens": 188, "totalTokens": 2457}, "metrics": {"latencyMs": 1139}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/529200bc_21_lumper_receipt_6.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nSilmon\n32036\nCONCESSION SUPPLIES\n209 CLAIBORNE\nWEST MONROE, LA\n(318) 322-2661\nWEST MONROE, LA 71291\n318-322-2661\nFAX 318-323-9357\nDATE 7-25-2520\nSOLD TO\nQUAN.\nITEM\nCOST\nSELL\nTOTAL\nLumper Fee\n$250\nSilMon's\nPEREGRINE CORPORATION TO REORDER CALL 318-325-4788\nCS\nCamScanner\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}