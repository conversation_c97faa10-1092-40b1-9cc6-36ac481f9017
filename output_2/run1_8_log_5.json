{"classification_result": {"documents": [{"page_no": 1, "doc_type": "other"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "18663b91-4e58-435b-8210-825c7cc68775", "Relationships": [{"Type": "CHILD", "Ids": ["9b393a06-8f63-4d48-b57b-64fc847bb193", "15b487ca-7103-46c0-91cf-b1c3a4bd356d", "1d816f8d-2822-4da6-9276-a792ac8f6d10", "3fd56c4b-54e6-4c2e-8da4-b0904d1a9c51", "93383502-6f6e-49e9-bb6c-928fd98819d3", "339675ee-2313-4675-8cd8-6cf244bff337", "cd8cb391-35c5-40e0-a927-52de912c99cb", "9a9eaff4-6df3-4400-8f1a-252ca57fa8df", "b8d627ed-d07f-4ea4-83ee-57fe00b9f287", "c41e859a-824d-4e9b-a847-dabe4f72c499", "b3ba8d22-0c24-42b6-86fd-3f1461d81014", "1034df18-b5f9-42e8-bb87-66df73c3449d", "744f0129-fb9c-4bdb-ab58-df1a49ce3d36", "c56afcfa-f6f1-412d-bb12-2aad0097ba3f", "c0413134-3750-457f-9b3f-7e34612b56c7", "1b2da9e5-f928-4323-b0aa-e5a762ef8e27", "7bf96508-ac72-4268-af8b-b845c449b482", "7daa57bf-511f-46b4-a1d4-fd6f0693c4b5", "e12468ba-24c7-49f2-a59d-1ce9093de984", "f22d6d85-a7d9-403b-b34c-48f893263dc4", "170eda7c-9607-4c35-9b83-36681344934f", "bb483293-253d-48ea-ad18-e79bf19386da", "129d5572-4ffc-4d8c-b3b0-f01ef3ae9b0b", "70079a32-30dd-4015-9c47-209bc1e13664", "79b79fa6-53ff-4525-9a0b-63ca4dd01254", "0f35b26e-7dca-4b68-95ea-65bc45d7319a", "4dcf91d0-2bc4-4b56-9b70-aac78e20d62a", "eba9ade0-8900-4876-86ba-97e5ca80db59", "a7f2177b-0066-4d9e-b6f6-b948094fab17", "dec59db5-a6d8-476c-931e-f636e1a8c9d4", "c9bc61e8-aac9-48ef-b59b-3351d34c685a", "4483775c-1949-44b7-8cd8-de22a7a10ff3", "479e3bad-c35c-4494-ad7c-fc7aa3c62f2c", "38b4f1da-6da3-4f67-9e4e-74fae7483c80", "f0a690d6-71df-4189-9235-c6cd702e6bfa", "46f487ce-fe48-4fac-ac50-41f2cd67b731", "a33292f6-1fb7-4301-9aff-8fdb092b4d76", "171cbfa1-bb41-41da-bb4e-62da8c5d1036", "6c641fd6-1267-478e-8ba3-0dc6604e4dba"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "09:57", "Geometry": {"BoundingBox": {"Width": 0.11713004112243652, "Height": 0.015407799743115902, "Left": 0.11504612863063812, "Top": 0.020125476643443108}, "Polygon": [{"X": 0.11504612863063812, "Y": 0.020125722512602806}, {"X": 0.23217593133449554, "Y": 0.020125476643443108}, {"X": 0.23217616975307465, "Y": 0.03553306683897972}, {"X": 0.11504639685153961, "Y": 0.035533275455236435}]}, "Id": "9b393a06-8f63-4d48-b57b-64fc847bb193", "Relationships": [{"Type": "CHILD", "Ids": ["373785a2-7f51-4b6a-ad97-d3afcc21081e"]}]}, {"BlockType": "LINE", "Confidence": 86.41920471191406, "Text": "5Gc", "Geometry": {"BoundingBox": {"Width": 0.05371016263961792, "Height": 0.012664314359426498, "Left": 0.7862324118614197, "Top": 0.02172193117439747}, "Polygon": [{"X": 0.7862324118614197, "Y": 0.021722042933106422}, {"X": 0.8399425148963928, "Y": 0.02172193117439747}, {"X": 0.8399425745010376, "Y": 0.03438614681363106}, {"X": 0.7862324714660645, "Y": 0.03438624367117882}]}, "Id": "15b487ca-7103-46c0-91cf-b1c3a4bd356d", "Relationships": [{"Type": "CHILD", "Ids": ["b1209f9a-8698-4394-bc58-26f5c85e1133"]}]}, {"BlockType": "LINE", "Confidence": 99.55974578857422, "Text": "94", "Geometry": {"BoundingBox": {"Width": 0.04216840863227844, "Height": 0.011626769788563251, "Left": 0.8645285367965698, "Top": 0.02136772871017456}, "Polygon": [{"X": 0.8645285367965698, "Y": 0.021367816254496574}, {"X": 0.9066969156265259, "Y": 0.02136772871017456}, {"X": 0.9066969752311707, "Y": 0.03299441933631897}, {"X": 0.8645285964012146, "Y": 0.03299449756741524}]}, "Id": "1d816f8d-2822-4da6-9276-a792ac8f6d10", "Relationships": [{"Type": "CHILD", "Ids": ["e278923d-e7d0-4471-9fa2-2d9841831727"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Done", "Geometry": {"BoundingBox": {"Width": 0.09560747444629669, "Height": 0.01437131967395544, "Left": 0.048845116049051285, "Top": 0.06749148666858673}, "Polygon": [{"X": 0.048845116049051285, "Y": 0.06749159097671509}, {"X": 0.14445234835147858, "Y": 0.06749148666858673}, {"X": 0.14445258677005768, "Y": 0.08186273276805878}, {"X": 0.04884537681937218, "Y": 0.08186280727386475}]}, "Id": "3fd56c4b-54e6-4c2e-8da4-b0904d1a9c51", "Relationships": [{"Type": "CHILD", "Ids": ["dbbe922f-23d5-4b8a-ba5d-7ee6d7deb89a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Retake", "Geometry": {"BoundingBox": {"Width": 0.11854176968336105, "Height": 0.014670895412564278, "Left": 0.8341136574745178, "Top": 0.06713664531707764}, "Polygon": [{"X": 0.8341136574745178, "Y": 0.06713677942752838}, {"X": 0.9526553750038147, "Y": 0.06713664531707764}, {"X": 0.9526554346084595, "Y": 0.0818074494600296}, {"X": 0.8341137766838074, "Y": 0.08180754631757736}]}, "Id": "93383502-6f6e-49e9-bb6c-928fd98819d3", "Relationships": [{"Type": "CHILD", "Ids": ["b0d13eb1-44e6-41f9-8eb8-8746130baf0f"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "DATE: 24-Feb", "Geometry": {"BoundingBox": {"Width": 0.13439719378948212, "Height": 0.007935116067528725, "Left": 0.2616932988166809, "Top": 0.26063257455825806}, "Polygon": [{"X": 0.2616932988166809, "Y": 0.26063257455825806}, {"X": 0.39609038829803467, "Y": 0.2606329619884491}, {"X": 0.3960905075073242, "Y": 0.26856768131256104}, {"X": 0.26169341802597046, "Y": 0.2685672640800476}]}, "Id": "339675ee-2313-4675-8cd8-6cf244bff337", "Relationships": [{"Type": "CHILD", "Ids": ["aa56bc22-eee9-4b9b-ae37-7be674d5087f", "3aa1c5e2-1416-4335-9e1b-744d82791253"]}]}, {"BlockType": "LINE", "Confidence": 99.7132797241211, "Text": "UNIT #: 92644/283420", "Geometry": {"BoundingBox": {"Width": 0.21596987545490265, "Height": 0.00892727356404066, "Left": 0.2449866235256195, "Top": 0.28213176131248474}, "Polygon": [{"X": 0.2449866235256195, "Y": 0.28213176131248474}, {"X": 0.4609563946723938, "Y": 0.28213247656822205}, {"X": 0.46095648407936096, "Y": 0.2910590171813965}, {"X": 0.24498675763607025, "Y": 0.2910582423210144}]}, "Id": "cd8cb391-35c5-40e0-a927-52de912c99cb", "Relationships": [{"Type": "CHILD", "Ids": ["d7211965-c1d3-41e7-af8e-1280573bd8b1", "61ae8e85-1444-4fdb-bfa5-d2192b8f27c8", "b96e055a-df2e-4c35-b365-37aad6fc72fb"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "Truck Ready Time: 5:00", "Geometry": {"BoundingBox": {"Width": 0.2777754068374634, "Height": 0.011000653728842735, "Left": 0.46594560146331787, "Top": 0.29109087586402893}, "Polygon": [{"X": 0.46594560146331787, "Y": 0.29109087586402893}, {"X": 0.7437209486961365, "Y": 0.2910918593406677}, {"X": 0.7437210083007812, "Y": 0.302091509103775}, {"X": 0.4659457206726074, "Y": 0.30209043622016907}]}, "Id": "9a9eaff4-6df3-4400-8f1a-252ca57fa8df", "Relationships": [{"Type": "CHILD", "Ids": ["2c7888e1-3c8f-4a10-931d-950180dbfe96", "1fbb90ca-be30-47d8-97f7-a951bd34c0c7", "c0ecb2c0-a083-4f27-b4fa-1de47f1e94d3", "fe38e1dc-353b-4691-b2cf-2c34934b4348"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.16027680039405823, "Height": 0.007915043272078037, "Left": 0.23588930070400238, "Top": 0.3040817677974701}, "Polygon": [{"X": 0.23588930070400238, "Y": 0.3040817677974701}, {"X": 0.39616599678993225, "Y": 0.30408239364624023}, {"X": 0.3961661159992218, "Y": 0.31199681758880615}, {"X": 0.23588943481445312, "Y": 0.3119961619377136}]}, "Id": "b8d627ed-d07f-4ea4-83ee-57fe00b9f287", "Relationships": [{"Type": "CHILD", "Ids": ["375d1a31-d6d4-454d-a9a2-3ab6a7be5dc9", "c2856912-158f-488e-bfff-abe6e3966508"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.1443394422531128, "Height": 0.009371564723551273, "Left": 0.4660872220993042, "Top": 0.31439220905303955}, "Polygon": [{"X": 0.4660872220993042, "Y": 0.31439220905303955}, {"X": 0.6104265451431274, "Y": 0.3143928050994873}, {"X": 0.610426664352417, "Y": 0.32376375794410706}, {"X": 0.46608734130859375, "Y": 0.3237631320953369}]}, "Id": "c41e859a-824d-4e9b-a847-dabe4f72c499", "Relationships": [{"Type": "CHILD", "Ids": ["ac309c19-a920-4e92-9be9-7da933bca54e", "7f9c4d68-4390-4ba3-959f-d859141120c6"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "5:00", "Geometry": {"BoundingBox": {"Width": 0.07429980486631393, "Height": 0.00934901274740696, "Left": 0.6597591042518616, "Top": 0.3140066862106323}, "Polygon": [{"X": 0.6597591042518616, "Y": 0.3140066862106323}, {"X": 0.7340588569641113, "Y": 0.3140069842338562}, {"X": 0.7340589165687561, "Y": 0.32335570454597473}, {"X": 0.6597592234611511, "Y": 0.32335537672042847}]}, "Id": "b3ba8d22-0c24-42b6-86fd-3f1461d81014", "Relationships": [{"Type": "CHILD", "Ids": ["9fe73f7c-f186-467d-b863-c7870ed385cf"]}]}, {"BlockType": "LINE", "Confidence": 99.91429901123047, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.22560490667819977, "Height": 0.0084666283801198, "Left": 0.14832307398319244, "Top": 0.3255603015422821}, "Polygon": [{"X": 0.14832307398319244, "Y": 0.3255603015422821}, {"X": 0.37392786145210266, "Y": 0.3255612850189209}, {"X": 0.3739279806613922, "Y": 0.33402690291404724}, {"X": 0.14832322299480438, "Y": 0.33402588963508606}]}, "Id": "1034df18-b5f9-42e8-bb87-66df73c3449d", "Relationships": [{"Type": "CHILD", "Ids": ["286c4e33-80d6-46c7-8d80-5f9a3781c604", "e2a5987a-9e8e-4587-994b-c68a8583ef69", "ea0a14f4-0915-4ad0-beb4-ce13f725513d"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "PICK", "Geometry": {"BoundingBox": {"Width": 0.040713440626859665, "Height": 0.006804634816944599, "Left": 0.34796833992004395, "Top": 0.34465473890304565}, "Polygon": [{"X": 0.34796833992004395, "Y": 0.34465473890304565}, {"X": 0.38868170976638794, "Y": 0.34465491771698}, {"X": 0.3886817991733551, "Y": 0.3514593839645386}, {"X": 0.3479684591293335, "Y": 0.35145917534828186}]}, "Id": "744f0129-fb9c-4bdb-ab58-df1a49ce3d36", "Relationships": [{"Type": "CHILD", "Ids": ["3a2db856-2ec6-48c3-bd29-dfffc49d5b7d"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.09310941398143768, "Height": 0.007601017132401466, "Left": 0.8243534564971924, "Top": 0.34984537959098816}, "Polygon": [{"X": 0.8243534564971924, "Y": 0.34984537959098816}, {"X": 0.9174628257751465, "Y": 0.349845826625824}, {"X": 0.9174628853797913, "Y": 0.3574464023113251}, {"X": 0.8243535161018372, "Y": 0.35744592547416687}]}, "Id": "c56afcfa-f6f1-412d-bb12-2aad0097ba3f", "Relationships": [{"Type": "CHILD", "Ids": ["cf52bd6c-716b-41d6-a323-00c12574e68b", "1dc04073-853c-4712-9620-98c1238d5676"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.098092220723629, "Height": 0.007674383465200663, "Left": 0.17871658504009247, "Top": 0.35688072443008423}, "Polygon": [{"X": 0.17871658504009247, "Y": 0.35688072443008423}, {"X": 0.2768086791038513, "Y": 0.35688120126724243}, {"X": 0.27680879831314087, "Y": 0.36455509066581726}, {"X": 0.17871670424938202, "Y": 0.36455458402633667}]}, "Id": "c0413134-3750-457f-9b3f-7e34612b56c7", "Relationships": [{"Type": "CHILD", "Ids": ["b376dbbd-6cfb-4719-9b15-7c0138bae1bf"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.023527460172772408, "Height": 0.007326602004468441, "Left": 0.3566812574863434, "Top": 0.35591238737106323}, "Polygon": [{"X": 0.3566812574863434, "Y": 0.35591238737106323}, {"X": 0.3802086114883423, "Y": 0.3559125065803528}, {"X": 0.38020873069763184, "Y": 0.36323899030685425}, {"X": 0.35668134689331055, "Y": 0.3632388710975647}]}, "Id": "1b2da9e5-f928-4323-b0aa-e5a762ef8e27", "Relationships": [{"Type": "CHILD", "Ids": ["a2492b5b-b3c5-45b3-bda8-9ecd1c4cdd00"]}]}, {"BlockType": "LINE", "Confidence": 99.72098541259766, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.03430702164769173, "Height": 0.007433968596160412, "Left": 0.4145145118236542, "Top": 0.3556877374649048}, "Polygon": [{"X": 0.4145145118236542, "Y": 0.3556877374649048}, {"X": 0.44882145524024963, "Y": 0.3556878864765167}, {"X": 0.4488215446472168, "Y": 0.3631216883659363}, {"X": 0.4145146310329437, "Y": 0.36312150955200195}]}, "Id": "7bf96508-ac72-4268-af8b-b845c449b482", "Relationships": [{"Type": "CHILD", "Ids": ["dbbe6090-82e7-49da-8fd5-955c7ffed4d5"]}]}, {"BlockType": "LINE", "Confidence": 99.6875, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.14092165231704712, "Height": 0.007967273704707623, "Left": 0.5665446519851685, "Top": 0.3553646504878998}, "Polygon": [{"X": 0.5665446519851685, "Y": 0.3553646504878998}, {"X": 0.7074662446975708, "Y": 0.3553653359413147}, {"X": 0.7074663043022156, "Y": 0.3633319139480591}, {"X": 0.5665447115898132, "Y": 0.3633311986923218}]}, "Id": "7daa57bf-511f-46b4-a1d4-fd6f0693c4b5", "Relationships": [{"Type": "CHILD", "Ids": ["66f770d2-9936-4019-840b-c8eae9fae085", "bc81b9e4-f98d-4820-8a0c-b50724fa48fd", "fd57634a-1a93-4967-ab44-ba6c54d998ee"]}]}, {"BlockType": "LINE", "Confidence": 67.92729187011719, "Text": "x", "Geometry": {"BoundingBox": {"Width": 0.03763885423541069, "Height": 0.024331852793693542, "Left": 0.07340550422668457, "Top": 0.37663552165031433}, "Polygon": [{"X": 0.07340550422668457, "Y": 0.37663552165031433}, {"X": 0.11104393005371094, "Y": 0.37663573026657104}, {"X": 0.11104435473680496, "Y": 0.40096738934516907}, {"X": 0.07340593636035919, "Y": 0.40096715092658997}]}, "Id": "e12468ba-24c7-49f2-a59d-1ce9093de984", "Relationships": [{"Type": "CHILD", "Ids": ["3b2bbb4c-74fa-474a-a6a6-2e3f099e5f2e"]}]}, {"BlockType": "LINE", "Confidence": 79.84786987304688, "Text": "I small Sk", "Geometry": {"BoundingBox": {"Width": 0.11732590943574905, "Height": 0.011845052242279053, "Left": 0.1811721920967102, "Top": 0.37889063358306885}, "Polygon": [{"X": 0.1811721920967102, "Y": 0.37889063358306885}, {"X": 0.29849791526794434, "Y": 0.3788912892341614}, {"X": 0.29849809408187866, "Y": 0.3907356858253479}, {"X": 0.18117238581180573, "Y": 0.390735000371933}]}, "Id": "f22d6d85-a7d9-403b-b34c-48f893263dc4", "Relationships": [{"Type": "CHILD", "Ids": ["9fa6173c-df80-4c03-bb46-452981c771ef", "25b81c5d-0da7-4f78-8bbf-5a2c6cb28d31", "9ba040ed-87f7-4dcb-b536-268b65df24d3"]}]}, {"BlockType": "LINE", "Confidence": 97.76945495605469, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.04892664775252342, "Height": 0.012097621336579323, "Left": 0.4636434316635132, "Top": 0.3771483600139618}, "Polygon": [{"X": 0.4636434316635132, "Y": 0.3771483600139618}, {"X": 0.5125699639320374, "Y": 0.3771486282348633}, {"X": 0.5125700831413269, "Y": 0.38924598693847656}, {"X": 0.4636435806751251, "Y": 0.3892456889152527}]}, "Id": "170eda7c-9607-4c35-9b83-36681344934f", "Relationships": [{"Type": "CHILD", "Ids": ["f385d826-3c0f-49be-a7a7-dd9b03b3ae4a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "0705", "Geometry": {"BoundingBox": {"Width": 0.0678027868270874, "Height": 0.011791517026722431, "Left": 0.8269667625427246, "Top": 0.3760887086391449}, "Polygon": [{"X": 0.8269667625427246, "Y": 0.3760887086391449}, {"X": 0.8947694897651672, "Y": 0.37608906626701355}, {"X": 0.894769549369812, "Y": 0.38788020610809326}, {"X": 0.8269668221473694, "Y": 0.3878798186779022}]}, "Id": "bb483293-253d-48ea-ad18-e79bf19386da", "Relationships": [{"Type": "CHILD", "Ids": ["b35555ac-2614-4067-86b2-937a65eea325"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.15297523140907288, "Height": 0.009664046578109264, "Left": 0.12229514122009277, "Top": 0.3942706882953644}, "Polygon": [{"X": 0.12229514122009277, "Y": 0.3942706882953644}, {"X": 0.2752702236175537, "Y": 0.394271582365036}, {"X": 0.27527037262916565, "Y": 0.4039347469806671}, {"X": 0.1222953051328659, "Y": 0.4039338231086731}]}, "Id": "129d5572-4ffc-4d8c-b3b0-f01ef3ae9b0b", "Relationships": [{"Type": "CHILD", "Ids": ["0c2a6869-87b4-4d28-a7ef-8f0e98997459", "fa6d9b8b-ca64-4919-8238-0c600513ef28"]}]}, {"BlockType": "LINE", "Confidence": 89.28551483154297, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010949739255011082, "Height": 0.0059378803707659245, "Left": 0.36353549361228943, "Top": 0.39598196744918823}, "Polygon": [{"X": 0.36353549361228943, "Y": 0.39598196744918823}, {"X": 0.3744851350784302, "Y": 0.395982027053833}, {"X": 0.37448522448539734, "Y": 0.4019198417663574}, {"X": 0.3635355532169342, "Y": 0.40191978216171265}]}, "Id": "70079a32-30dd-4015-9c47-209bc1e13664", "Relationships": [{"Type": "CHILD", "Ids": ["675e9035-c82b-4484-842e-1ef3b59bdf70"]}]}, {"BlockType": "LINE", "Confidence": 91.65596771240234, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011345498263835907, "Height": 0.006103744730353355, "Left": 0.4261852204799652, "Top": 0.395738810300827}, "Polygon": [{"X": 0.4261852204799652, "Y": 0.395738810300827}, {"X": 0.43753066658973694, "Y": 0.3957388699054718}, {"X": 0.4375307261943817, "Y": 0.40184256434440613}, {"X": 0.4261853098869324, "Y": 0.40184247493743896}]}, "Id": "79b79fa6-53ff-4525-9a0b-63ca4dd01254", "Relationships": [{"Type": "CHILD", "Ids": ["0ebfa4e6-f0dc-4fd3-a339-25de4db14ac4"]}]}, {"BlockType": "LINE", "Confidence": 99.37519836425781, "Text": "*********** ***********", "Geometry": {"BoundingBox": {"Width": 0.19458195567131042, "Height": 0.007458490785211325, "Left": 0.4639710485935211, "Top": 0.39384007453918457}, "Polygon": [{"X": 0.4639710485935211, "Y": 0.39384007453918457}, {"X": 0.6585529446601868, "Y": 0.3938412070274353}, {"X": 0.6585530042648315, "Y": 0.4012985825538635}, {"X": 0.4639711380004883, "Y": 0.4012974202632904}]}, "Id": "0f35b26e-7dca-4b68-95ea-65bc45d7319a", "Relationships": [{"Type": "CHILD", "Ids": ["1a17dcb1-27d0-4d9b-a32d-1cb347002d57", "618f01f2-6bf0-4e90-b15a-c14ee8f8f659"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "0710", "Geometry": {"BoundingBox": {"Width": 0.0725608617067337, "Height": 0.00999318715184927, "Left": 0.8231115937232971, "Top": 0.40375179052352905}, "Polygon": [{"X": 0.8231115937232971, "Y": 0.40375179052352905}, {"X": 0.8956723809242249, "Y": 0.4037522077560425}, {"X": 0.8956724405288696, "Y": 0.4137449562549591}, {"X": 0.8231116533279419, "Y": 0.4137445092201233}]}, "Id": "4dcf91d0-2bc4-4b56-9b70-aac78e20d62a", "Relationships": [{"Type": "CHILD", "Ids": ["473f6796-1c79-49c0-a012-dc8370c22fe4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0755", "Geometry": {"BoundingBox": {"Width": 0.06733584403991699, "Height": 0.012236219830811024, "Left": 0.8282186985015869, "Top": 0.4362069368362427}, "Polygon": [{"X": 0.8282186985015869, "Y": 0.4362069368362427}, {"X": 0.8955544829368591, "Y": 0.4362073838710785}, {"X": 0.8955545425415039, "Y": 0.44844314455986023}, {"X": 0.8282188177108765, "Y": 0.4484426975250244}]}, "Id": "eba9ade0-8900-4876-86ba-97e5ca80db59", "Relationships": [{"Type": "CHILD", "Ids": ["95aa84fe-4b30-4315-9d60-8d1786e00387"]}]}, {"BlockType": "LINE", "Confidence": 69.3919448852539, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.05929342284798622, "Height": 0.045260827988386154, "Left": 0.0594523586332798, "Top": 0.4380947947502136}, "Polygon": [{"X": 0.0594523586332798, "Y": 0.4380947947502136}, {"X": 0.11874500662088394, "Y": 0.43809518218040466}, {"X": 0.11874578148126602, "Y": 0.4833556115627289}, {"X": 0.05945317819714546, "Y": 0.48335516452789307}]}, "Id": "a7f2177b-0066-4d9e-b6f6-b948094fab17", "Relationships": [{"Type": "CHILD", "Ids": ["74ea912c-3298-4f93-9faf-9f2811e368a1"]}]}, {"BlockType": "LINE", "Confidence": 89.5838623046875, "Text": "3sk", "Geometry": {"BoundingBox": {"Width": 0.056661881506443024, "Height": 0.011778688058257103, "Left": 0.15157723426818848, "Top": 0.4495817720890045}, "Polygon": [{"X": 0.15157723426818848, "Y": 0.4495817720890045}, {"X": 0.20823892951011658, "Y": 0.44958215951919556}, {"X": 0.2082391083240509, "Y": 0.46136045455932617}, {"X": 0.151577427983284, "Y": 0.46136003732681274}]}, "Id": "dec59db5-a6d8-476c-931e-f636e1a8c9d4", "Relationships": [{"Type": "CHILD", "Ids": ["e2b25b5e-d282-4365-88f5-2dc4af8a55e6"]}]}, {"BlockType": "LINE", "Confidence": 88.65214538574219, "Text": "2sk", "Geometry": {"BoundingBox": {"Width": 0.059546131640672684, "Height": 0.012924063950777054, "Left": 0.4554550051689148, "Top": 0.44218820333480835}, "Polygon": [{"X": 0.4554550051689148, "Y": 0.44218820333480835}, {"X": 0.5150009989738464, "Y": 0.4421885907649994}, {"X": 0.515001118183136, "Y": 0.4551122486591339}, {"X": 0.45545515418052673, "Y": 0.4551118314266205}]}, "Id": "c9bc61e8-aac9-48ef-b59b-3351d34c685a", "Relationships": [{"Type": "CHILD", "Ids": ["16f243b7-6c01-46ba-a256-aeddf12e3e39"]}]}, {"BlockType": "LINE", "Confidence": 99.9216079711914, "Text": "Automation & Modular", "Geometry": {"BoundingBox": {"Width": 0.19269391894340515, "Height": 0.007872416637837887, "Left": 0.12197329103946686, "Top": 0.46106934547424316}, "Polygon": [{"X": 0.12197329103946686, "Y": 0.46106934547424316}, {"X": 0.31466710567474365, "Y": 0.4610707461833954}, {"X": 0.3146671950817108, "Y": 0.4689417779445648}, {"X": 0.1219734251499176, "Y": 0.4689403474330902}]}, "Id": "4483775c-1949-44b7-8cd8-de22a7a10ff3", "Relationships": [{"Type": "CHILD", "Ids": ["d2cb6913-dd4a-41da-b59f-0b48c603c80b", "25717d81-945d-46cf-b9b8-447979497817", "8916862d-04f6-4158-837a-e1ba7b8c6b9e"]}]}, {"BlockType": "LINE", "Confidence": 87.73197937011719, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010778182186186314, "Height": 0.005908254999667406, "Left": 0.364133358001709, "Top": 0.46260178089141846}, "Polygon": [{"X": 0.364133358001709, "Y": 0.46260178089141846}, {"X": 0.37491145730018616, "Y": 0.46260184049606323}, {"X": 0.37491151690483093, "Y": 0.4685100317001343}, {"X": 0.36413341760635376, "Y": 0.4685099422931671}]}, "Id": "479e3bad-c35c-4494-ad7c-fc7aa3c62f2c", "Relationships": [{"Type": "CHILD", "Ids": ["57dec8ae-8d3d-4cbd-b71d-8712ebba3b72"]}]}, {"BlockType": "LINE", "Confidence": 85.74797058105469, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011137685738503933, "Height": 0.005786150228232145, "Left": 0.42676064372062683, "Top": 0.46268731355667114}, "Polygon": [{"X": 0.42676064372062683, "Y": 0.46268731355667114}, {"X": 0.43789827823638916, "Y": 0.4626874029636383}, {"X": 0.43789833784103394, "Y": 0.4684734642505646}, {"X": 0.426760733127594, "Y": 0.4684734046459198}]}, "Id": "38b4f1da-6da3-4f67-9e4e-74fae7483c80", "Relationships": [{"Type": "CHILD", "Ids": ["c77dec95-b735-4ae2-9be6-1c6324763f47"]}]}, {"BlockType": "LINE", "Confidence": 99.08482360839844, "Text": "SO-2116134", "Geometry": {"BoundingBox": {"Width": 0.08608701080083847, "Height": 0.006733916234225035, "Left": 0.46426793932914734, "Top": 0.4611221253871918}, "Polygon": [{"X": 0.46426793932914734, "Y": 0.4611221253871918}, {"X": 0.5503548979759216, "Y": 0.4611227512359619}, {"X": 0.5503549575805664, "Y": 0.4678560197353363}, {"X": 0.4642680287361145, "Y": 0.46785539388656616}]}, "Id": "f0a690d6-71df-4189-9235-c6cd702e6bfa", "Relationships": [{"Type": "CHILD", "Ids": ["cf8f66cb-71d0-4821-8914-b0834ad58882"]}]}, {"BlockType": "LINE", "Confidence": 99.5655288696289, "Text": "2116736", "Geometry": {"BoundingBox": {"Width": 0.14135520160198212, "Height": 0.01314903236925602, "Left": 0.5804247856140137, "Top": 0.4552437663078308}, "Polygon": [{"X": 0.5804247856140137, "Y": 0.4552437663078308}, {"X": 0.7217798829078674, "Y": 0.455244779586792}, {"X": 0.721780002117157, "Y": 0.4683927893638611}, {"X": 0.580424964427948, "Y": 0.4683917462825775}]}, "Id": "46f487ce-fe48-4fac-ac50-41f2cd67b731", "Relationships": [{"Type": "CHILD", "Ids": ["b17769df-12bb-4986-8c24-906088bfac68"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0815", "Geometry": {"BoundingBox": {"Width": 0.05711528658866882, "Height": 0.010189207270741463, "Left": 0.8299935460090637, "Top": 0.47014498710632324}, "Polygon": [{"X": 0.8299935460090637, "Y": 0.47014498710632324}, {"X": 0.8871087431907654, "Y": 0.47014540433883667}, {"X": 0.8871088027954102, "Y": 0.48033419251441956}, {"X": 0.8299936056137085, "Y": 0.48033374547958374}]}, "Id": "a33292f6-1fb7-4301-9aff-8fdb092b4d76", "Relationships": [{"Type": "CHILD", "Ids": ["427bb0e6-f717-4c50-a45d-5dbfafa97d66"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Detroit", "Geometry": {"BoundingBox": {"Width": 0.05822712928056717, "Height": 0.007702106609940529, "Left": 0.48029184341430664, "Top": 0.7640868425369263}, "Polygon": [{"X": 0.48029184341430664, "Y": 0.7640868425369263}, {"X": 0.5385189056396484, "Y": 0.7640876173973083}, {"X": 0.5385189652442932, "Y": 0.7717889547348022}, {"X": 0.4802919328212738, "Y": 0.7717881202697754}]}, "Id": "171cbfa1-bb41-41da-bb4e-62da8c5d1036", "Relationships": [{"Type": "CHILD", "Ids": ["a75e82d6-5cf4-4d86-bea6-37a4c972d8a9"]}]}, {"BlockType": "LINE", "Confidence": 85.14867401123047, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.055564768612384796, "Height": 0.02554180473089218, "Left": 0.05213910713791847, "Top": 0.9272449016571045}, "Polygon": [{"X": 0.05213910713791847, "Y": 0.9272449016571045}, {"X": 0.10770343244075775, "Y": 0.9272458553314209}, {"X": 0.10770387947559357, "Y": 0.9527866840362549}, {"X": 0.05213957279920578, "Y": 0.9527857303619385}]}, "Id": "6c641fd6-1267-478e-8ba3-0dc6604e4dba", "Relationships": [{"Type": "CHILD", "Ids": ["796e1a73-afe0-4af4-8019-c05cc513f2f6"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "09:57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11713004112243652, "Height": 0.015407799743115902, "Left": 0.11504612863063812, "Top": 0.020125476643443108}, "Polygon": [{"X": 0.11504612863063812, "Y": 0.020125722512602806}, {"X": 0.23217593133449554, "Y": 0.020125476643443108}, {"X": 0.23217616975307465, "Y": 0.03553306683897972}, {"X": 0.11504639685153961, "Y": 0.035533275455236435}]}, "Id": "373785a2-7f51-4b6a-ad97-d3afcc21081e"}, {"BlockType": "WORD", "Confidence": 86.41920471191406, "Text": "5Gc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05371016263961792, "Height": 0.012664314359426498, "Left": 0.7862324118614197, "Top": 0.02172193117439747}, "Polygon": [{"X": 0.7862324118614197, "Y": 0.021722042933106422}, {"X": 0.8399425148963928, "Y": 0.02172193117439747}, {"X": 0.8399425745010376, "Y": 0.03438614681363106}, {"X": 0.7862324714660645, "Y": 0.03438624367117882}]}, "Id": "b1209f9a-8698-4394-bc58-26f5c85e1133"}, {"BlockType": "WORD", "Confidence": 99.55974578857422, "Text": "94", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04216840863227844, "Height": 0.011626769788563251, "Left": 0.8645285367965698, "Top": 0.02136772871017456}, "Polygon": [{"X": 0.8645285367965698, "Y": 0.021367816254496574}, {"X": 0.9066969156265259, "Y": 0.02136772871017456}, {"X": 0.9066969752311707, "Y": 0.03299441933631897}, {"X": 0.8645285964012146, "Y": 0.03299449756741524}]}, "Id": "e278923d-e7d0-4471-9fa2-2d9841831727"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Done", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09560747444629669, "Height": 0.01437131967395544, "Left": 0.048845116049051285, "Top": 0.06749148666858673}, "Polygon": [{"X": 0.048845116049051285, "Y": 0.06749159097671509}, {"X": 0.14445234835147858, "Y": 0.06749148666858673}, {"X": 0.14445258677005768, "Y": 0.08186273276805878}, {"X": 0.04884537681937218, "Y": 0.08186280727386475}]}, "Id": "dbbe922f-23d5-4b8a-ba5d-7ee6d7deb89a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Retake", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11854176968336105, "Height": 0.014670895412564278, "Left": 0.8341136574745178, "Top": 0.06713664531707764}, "Polygon": [{"X": 0.8341136574745178, "Y": 0.06713677942752838}, {"X": 0.9526553750038147, "Y": 0.06713664531707764}, {"X": 0.9526554346084595, "Y": 0.0818074494600296}, {"X": 0.8341137766838074, "Y": 0.08180754631757736}]}, "Id": "b0d13eb1-44e6-41f9-8eb8-8746130baf0f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053708065301179886, "Height": 0.0074724433943629265, "Left": 0.2616932988166809, "Top": 0.26109498739242554}, "Polygon": [{"X": 0.2616932988166809, "Y": 0.26109498739242554}, {"X": 0.31540125608444214, "Y": 0.2610951364040375}, {"X": 0.3154013752937317, "Y": 0.26856744289398193}, {"X": 0.26169341802597046, "Y": 0.2685672640800476}]}, "Id": "aa56bc22-eee9-4b9b-ae37-7be674d5087f"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "24-Feb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058785051107406616, "Height": 0.007875480689108372, "Left": 0.3373054265975952, "Top": 0.26063278317451477}, "Polygon": [{"X": 0.3373054265975952, "Y": 0.26063278317451477}, {"X": 0.39609038829803467, "Y": 0.2606329619884491}, {"X": 0.39609047770500183, "Y": 0.26850828528404236}, {"X": 0.33730554580688477, "Y": 0.26850807666778564}]}, "Id": "3aa1c5e2-1416-4335-9e1b-744d82791253"}, {"BlockType": "WORD", "Confidence": 99.53044891357422, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046839639544487, "Height": 0.007610750384628773, "Left": 0.2449866235256195, "Top": 0.2827391028404236}, "Polygon": [{"X": 0.2449866235256195, "Y": 0.2827391028404236}, {"X": 0.29182615876197815, "Y": 0.2827392816543579}, {"X": 0.2918262779712677, "Y": 0.2903498709201813}, {"X": 0.24498674273490906, "Y": 0.29034969210624695}]}, "Id": "d7211965-c1d3-41e7-af8e-1280573bd8b1"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019844060763716698, "Height": 0.007605979219079018, "Left": 0.29551127552986145, "Top": 0.282666951417923}, "Polygon": [{"X": 0.29551127552986145, "Y": 0.282666951417923}, {"X": 0.31535521149635315, "Y": 0.28266704082489014}, {"X": 0.3153553307056427, "Y": 0.29027295112609863}, {"X": 0.2955113649368286, "Y": 0.29027286171913147}]}, "Id": "61ae8e85-1444-4fdb-bfa5-d2192b8f27c8"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "92644/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12454775720834732, "Height": 0.00892695877701044, "Left": 0.33640873432159424, "Top": 0.2821320593357086}, "Polygon": [{"X": 0.33640873432159424, "Y": 0.2821320593357086}, {"X": 0.4609563946723938, "Y": 0.28213247656822205}, {"X": 0.46095648407936096, "Y": 0.2910590171813965}, {"X": 0.3364088535308838, "Y": 0.29105857014656067}]}, "Id": "b96e055a-df2e-4c35-b365-37aad6fc72fb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04772192984819412, "Height": 0.007759837433695793, "Left": 0.46594563126564026, "Top": 0.2926507890224457}, "Polygon": [{"X": 0.46594563126564026, "Y": 0.2926507890224457}, {"X": 0.5136674642562866, "Y": 0.29265096783638}, {"X": 0.5136675238609314, "Y": 0.3004106283187866}, {"X": 0.4659457206726074, "Y": 0.3004104495048523}]}, "Id": "2c7888e1-3c8f-4a10-931d-950180dbfe96"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05483131483197212, "Height": 0.009648546576499939, "Left": 0.5178360939025879, "Top": 0.29244232177734375}, "Polygon": [{"X": 0.5178360939025879, "Y": 0.29244232177734375}, {"X": 0.5726673007011414, "Y": 0.2924425005912781}, {"X": 0.5726674199104309, "Y": 0.3020908534526825}, {"X": 0.5178362131118774, "Y": 0.3020906448364258}]}, "Id": "1fbb90ca-be30-47d8-97f7-a951bd34c0c7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04734331741929054, "Height": 0.0076330783776938915, "Left": 0.5779169797897339, "Top": 0.2926827073097229}, "Polygon": [{"X": 0.5779169797897339, "Y": 0.2926827073097229}, {"X": 0.6252602338790894, "Y": 0.2926828861236572}, {"X": 0.6252602934837341, "Y": 0.300315797328949}, {"X": 0.5779170989990234, "Y": 0.30031561851501465}]}, "Id": "c0ecb2c0-a083-4f27-b4fa-1de47f1e94d3"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0667114108800888, "Height": 0.010374313220381737, "Left": 0.6770095825195312, "Top": 0.2910916209220886}, "Polygon": [{"X": 0.6770095825195312, "Y": 0.2910916209220886}, {"X": 0.7437209486961365, "Y": 0.2910918593406677}, {"X": 0.7437210083007812, "Y": 0.3014659583568573}, {"X": 0.6770097017288208, "Y": 0.3014656901359558}]}, "Id": "fe38e1dc-353b-4691-b2cf-2c34934b4348"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07398182898759842, "Height": 0.007552543189376593, "Left": 0.23588931560516357, "Top": 0.304443895816803}, "Polygon": [{"X": 0.23588931560516357, "Y": 0.304443895816803}, {"X": 0.30987101793289185, "Y": 0.30444419384002686}, {"X": 0.3098711371421814, "Y": 0.3119964599609375}, {"X": 0.23588943481445312, "Y": 0.3119961619377136}]}, "Id": "375d1a31-d6d4-454d-a9a2-3ab6a7be5dc9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058671046048402786, "Height": 0.0073663778603076935, "Left": 0.3374950587749481, "Top": 0.30408215522766113}, "Polygon": [{"X": 0.3374950587749481, "Y": 0.30408215522766113}, {"X": 0.39616599678993225, "Y": 0.30408239364624023}, {"X": 0.3961660861968994, "Y": 0.3114485442638397}, {"X": 0.3374951481819153, "Y": 0.3114483058452606}]}, "Id": "c2856912-158f-488e-bfff-abe6e3966508"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048247452825307846, "Height": 0.007814805023372173, "Left": 0.4660872220993042, "Top": 0.31439220905303955}, "Polygon": [{"X": 0.4660872220993042, "Y": 0.31439220905303955}, {"X": 0.5143345594406128, "Y": 0.3143923878669739}, {"X": 0.5143346786499023, "Y": 0.3222070038318634}, {"X": 0.46608731150627136, "Y": 0.3222067952156067}]}, "Id": "ac309c19-a920-4e92-9be9-7da933bca54e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09259023517370224, "Height": 0.009291324764490128, "Left": 0.5178364515304565, "Top": 0.3144724369049072}, "Polygon": [{"X": 0.5178364515304565, "Y": 0.3144724369049072}, {"X": 0.6104265451431274, "Y": 0.31447282433509827}, {"X": 0.610426664352417, "Y": 0.32376375794410706}, {"X": 0.5178365111351013, "Y": 0.323763370513916}]}, "Id": "7f9c4d68-4390-4ba3-959f-d859141120c6"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07429980486631393, "Height": 0.00934901274740696, "Left": 0.6597591042518616, "Top": 0.3140066862106323}, "Polygon": [{"X": 0.6597591042518616, "Y": 0.3140066862106323}, {"X": 0.7340588569641113, "Y": 0.3140069842338562}, {"X": 0.7340589165687561, "Y": 0.32335570454597473}, {"X": 0.6597592234611511, "Y": 0.32335537672042847}]}, "Id": "9fe73f7c-f186-467d-b863-c7870ed385cf"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10694697499275208, "Height": 0.00777052016928792, "Left": 0.14832308888435364, "Top": 0.3262558579444885}, "Polygon": [{"X": 0.14832308888435364, "Y": 0.3262558579444885}, {"X": 0.25526994466781616, "Y": 0.32625633478164673}, {"X": 0.2552700638771057, "Y": 0.33402636647224426}, {"X": 0.14832322299480438, "Y": 0.33402588963508606}]}, "Id": "286c4e33-80d6-46c7-8d80-5f9a3781c604"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049339666962623596, "Height": 0.007473318371921778, "Left": 0.26071897149086, "Top": 0.3261425495147705}, "Polygon": [{"X": 0.26071897149086, "Y": 0.3261425495147705}, {"X": 0.3100585341453552, "Y": 0.3261427581310272}, {"X": 0.3100586533546448, "Y": 0.33361586928367615}, {"X": 0.26071909070014954, "Y": 0.33361563086509705}]}, "Id": "e2a5987a-9e8e-4587-994b-c68a8583ef69"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03681879863142967, "Height": 0.007908767089247704, "Left": 0.33710917830467224, "Top": 0.3255611062049866}, "Polygon": [{"X": 0.33710917830467224, "Y": 0.3255611062049866}, {"X": 0.37392786145210266, "Y": 0.3255612850189209}, {"X": 0.3739279806613922, "Y": 0.33346986770629883}, {"X": 0.3371092975139618, "Y": 0.3334697186946869}]}, "Id": "ea0a14f4-0915-4ad0-beb4-ce13f725513d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040713440626859665, "Height": 0.006804634816944599, "Left": 0.34796833992004395, "Top": 0.34465473890304565}, "Polygon": [{"X": 0.34796833992004395, "Y": 0.34465473890304565}, {"X": 0.38868170976638794, "Y": 0.34465491771698}, {"X": 0.3886817991733551, "Y": 0.3514593839645386}, {"X": 0.3479684591293335, "Y": 0.35145917534828186}]}, "Id": "3a2db856-2ec6-48c3-bd29-dfffc49d5b7d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044255927205085754, "Height": 0.007356932852417231, "Left": 0.8243534564971924, "Top": 0.3500892221927643}, "Polygon": [{"X": 0.8243534564971924, "Y": 0.3500892221927643}, {"X": 0.8686093688011169, "Y": 0.350089430809021}, {"X": 0.8686094284057617, "Y": 0.3574461340904236}, {"X": 0.8243535161018372, "Y": 0.35744592547416687}]}, "Id": "cf52bd6c-716b-41d6-a323-00c12574e68b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04497066140174866, "Height": 0.007032638881355524, "Left": 0.8724921941757202, "Top": 0.34984561800956726}, "Polygon": [{"X": 0.8724921941757202, "Y": 0.34984561800956726}, {"X": 0.9174628257751465, "Y": 0.349845826625824}, {"X": 0.9174628853797913, "Y": 0.35687825083732605}, {"X": 0.872492253780365, "Y": 0.35687801241874695}]}, "Id": "1dc04073-853c-4712-9620-98c1238d5676"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.098092220723629, "Height": 0.007674383465200663, "Left": 0.17871658504009247, "Top": 0.35688072443008423}, "Polygon": [{"X": 0.17871658504009247, "Y": 0.35688072443008423}, {"X": 0.2768086791038513, "Y": 0.35688120126724243}, {"X": 0.27680879831314087, "Y": 0.36455509066581726}, {"X": 0.17871670424938202, "Y": 0.36455458402633667}]}, "Id": "b376dbbd-6cfb-4719-9b15-7c0138bae1bf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023527460172772408, "Height": 0.007326602004468441, "Left": 0.3566812574863434, "Top": 0.35591238737106323}, "Polygon": [{"X": 0.3566812574863434, "Y": 0.35591238737106323}, {"X": 0.3802086114883423, "Y": 0.3559125065803528}, {"X": 0.38020873069763184, "Y": 0.36323899030685425}, {"X": 0.35668134689331055, "Y": 0.3632388710975647}]}, "Id": "a2492b5b-b3c5-45b3-bda8-9ecd1c4cdd00"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03430702164769173, "Height": 0.007433968596160412, "Left": 0.4145145118236542, "Top": 0.3556877374649048}, "Polygon": [{"X": 0.4145145118236542, "Y": 0.3556877374649048}, {"X": 0.44882145524024963, "Y": 0.3556878864765167}, {"X": 0.4488215446472168, "Y": 0.3631216883659363}, {"X": 0.4145146310329437, "Y": 0.36312150955200195}]}, "Id": "dbbe6090-82e7-49da-8fd5-955c7ffed4d5"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05563005432486534, "Height": 0.007411662023514509, "Left": 0.5665446519851685, "Top": 0.35591980814933777}, "Polygon": [{"X": 0.5665446519851685, "Y": 0.35591980814933777}, {"X": 0.6221746206283569, "Y": 0.35592010617256165}, {"X": 0.6221746802330017, "Y": 0.36333146691322327}, {"X": 0.5665447115898132, "Y": 0.3633311986923218}]}, "Id": "66f770d2-9936-4019-840b-c8eae9fae085"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062220748513936996, "Height": 0.007461553905159235, "Left": 0.6268037557601929, "Top": 0.3557145595550537}, "Polygon": [{"X": 0.6268037557601929, "Y": 0.3557145595550537}, {"X": 0.6890243887901306, "Y": 0.3557148873806}, {"X": 0.6890244483947754, "Y": 0.3631761372089386}, {"X": 0.6268038153648376, "Y": 0.36317580938339233}]}, "Id": "bc81b9e4-f98d-4820-8a0c-b50724fa48fd"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012180105783045292, "Height": 0.007636393420398235, "Left": 0.695286214351654, "Top": 0.3553652763366699}, "Polygon": [{"X": 0.695286214351654, "Y": 0.3553652763366699}, {"X": 0.7074662446975708, "Y": 0.3553653359413147}, {"X": 0.7074663043022156, "Y": 0.36300167441368103}, {"X": 0.6952862739562988, "Y": 0.36300161480903625}]}, "Id": "fd57634a-1a93-4967-ab44-ba6c54d998ee"}, {"BlockType": "WORD", "Confidence": 67.92729187011719, "Text": "x", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03763885423541069, "Height": 0.024331852793693542, "Left": 0.07340550422668457, "Top": 0.37663552165031433}, "Polygon": [{"X": 0.07340550422668457, "Y": 0.37663552165031433}, {"X": 0.11104393005371094, "Y": 0.37663573026657104}, {"X": 0.11104435473680496, "Y": 0.40096738934516907}, {"X": 0.07340593636035919, "Y": 0.40096715092658997}]}, "Id": "3b2bbb4c-74fa-474a-a6a6-2e3f099e5f2e"}, {"BlockType": "WORD", "Confidence": 73.32210540771484, "Text": "I", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.004757540766149759, "Height": 0.011665849015116692, "Left": 0.1811721920967102, "Top": 0.37889063358306885}, "Polygon": [{"X": 0.1811721920967102, "Y": 0.37889063358306885}, {"X": 0.185929536819458, "Y": 0.37889066338539124}, {"X": 0.18592973053455353, "Y": 0.3905564844608307}, {"X": 0.18117238581180573, "Y": 0.3905564546585083}]}, "Id": "9fa6173c-df80-4c03-bb46-452981c771ef"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "small", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07031191885471344, "Height": 0.01183638907968998, "Left": 0.19183644652366638, "Top": 0.37889909744262695}, "Polygon": [{"X": 0.19183644652366638, "Y": 0.37889909744262695}, {"X": 0.2621481716632843, "Y": 0.378899484872818}, {"X": 0.26214835047721863, "Y": 0.3907354772090912}, {"X": 0.1918366253376007, "Y": 0.39073505997657776}]}, "Id": "25b81c5d-0da7-4f78-8bbf-5a2c6cb28d31"}, {"BlockType": "WORD", "Confidence": 66.37854766845703, "Text": "Sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.029764074832201004, "Height": 0.010773315094411373, "Left": 0.26873400807380676, "Top": 0.3794848918914795}, "Polygon": [{"X": 0.26873400807380676, "Y": 0.3794848918914795}, {"X": 0.2984979450702667, "Y": 0.3794850707054138}, {"X": 0.29849809408187866, "Y": 0.39025822281837463}, {"X": 0.2687341868877411, "Y": 0.3902580440044403}]}, "Id": "9ba040ed-87f7-4dcb-b536-268b65df24d3"}, {"BlockType": "WORD", "Confidence": 97.76945495605469, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04892664775252342, "Height": 0.012097621336579323, "Left": 0.4636434316635132, "Top": 0.3771483600139618}, "Polygon": [{"X": 0.4636434316635132, "Y": 0.3771483600139618}, {"X": 0.5125699639320374, "Y": 0.3771486282348633}, {"X": 0.5125700831413269, "Y": 0.38924598693847656}, {"X": 0.4636435806751251, "Y": 0.3892456889152527}]}, "Id": "f385d826-3c0f-49be-a7a7-dd9b03b3ae4a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "0705", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0678027868270874, "Height": 0.011791517026722431, "Left": 0.8269667625427246, "Top": 0.3760887086391449}, "Polygon": [{"X": 0.8269667625427246, "Y": 0.3760887086391449}, {"X": 0.8947694897651672, "Y": 0.37608906626701355}, {"X": 0.894769549369812, "Y": 0.38788020610809326}, {"X": 0.8269668221473694, "Y": 0.3878798186779022}]}, "Id": "b35555ac-2614-4067-86b2-937a65eea325"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07109121978282928, "Height": 0.007817145437002182, "Left": 0.12229514122009277, "Top": 0.3943314254283905}, "Polygon": [{"X": 0.12229514122009277, "Y": 0.3943314254283905}, {"X": 0.1933862417936325, "Y": 0.39433184266090393}, {"X": 0.19338636100292206, "Y": 0.402148574590683}, {"X": 0.12229527533054352, "Y": 0.40214815735816956}]}, "Id": "0c2a6869-87b4-4d28-a7ef-8f0e98997459"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gunsight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07651526480913162, "Height": 0.009663602337241173, "Left": 0.19875510036945343, "Top": 0.3942711353302002}, "Polygon": [{"X": 0.19875510036945343, "Y": 0.3942711353302002}, {"X": 0.2752702236175537, "Y": 0.394271582365036}, {"X": 0.27527037262916565, "Y": 0.4039347469806671}, {"X": 0.19875526428222656, "Y": 0.4039342701435089}]}, "Id": "fa6d9b8b-ca64-4919-8238-0c600513ef28"}, {"BlockType": "WORD", "Confidence": 89.28551483154297, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010949739255011082, "Height": 0.0059378803707659245, "Left": 0.36353549361228943, "Top": 0.39598196744918823}, "Polygon": [{"X": 0.36353549361228943, "Y": 0.39598196744918823}, {"X": 0.3744851350784302, "Y": 0.395982027053833}, {"X": 0.37448522448539734, "Y": 0.4019198417663574}, {"X": 0.3635355532169342, "Y": 0.40191978216171265}]}, "Id": "675e9035-c82b-4484-842e-1ef3b59bdf70"}, {"BlockType": "WORD", "Confidence": 91.65596771240234, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011345498263835907, "Height": 0.006103744730353355, "Left": 0.4261852204799652, "Top": 0.395738810300827}, "Polygon": [{"X": 0.4261852204799652, "Y": 0.395738810300827}, {"X": 0.43753066658973694, "Y": 0.3957388699054718}, {"X": 0.4375307261943817, "Y": 0.40184256434440613}, {"X": 0.4261853098869324, "Y": 0.40184247493743896}]}, "Id": "0ebfa4e6-f0dc-4fd3-a339-25de4db14ac4"}, {"BlockType": "WORD", "Confidence": 99.4736557006836, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09484575688838959, "Height": 0.006871933117508888, "Left": 0.4639710485935211, "Top": 0.39442604780197144}, "Polygon": [{"X": 0.4639710485935211, "Y": 0.39442604780197144}, {"X": 0.5588167309761047, "Y": 0.3944265842437744}, {"X": 0.5588167905807495, "Y": 0.40129798650741577}, {"X": 0.4639711380004883, "Y": 0.4012974202632904}]}, "Id": "1a17dcb1-27d0-4d9b-a32d-1cb347002d57"}, {"BlockType": "WORD", "Confidence": 99.27674865722656, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09489914029836655, "Height": 0.0068511986173689365, "Left": 0.563653826713562, "Top": 0.3938406705856323}, "Polygon": [{"X": 0.563653826713562, "Y": 0.3938406705856323}, {"X": 0.6585529446601868, "Y": 0.3938412070274353}, {"X": 0.6585530042648315, "Y": 0.4006918668746948}, {"X": 0.5636538863182068, "Y": 0.40069130063056946}]}, "Id": "618f01f2-6bf0-4e90-b15a-c14ee8f8f659"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "0710", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0725608617067337, "Height": 0.00999318715184927, "Left": 0.8231115937232971, "Top": 0.40375179052352905}, "Polygon": [{"X": 0.8231115937232971, "Y": 0.40375179052352905}, {"X": 0.8956723809242249, "Y": 0.4037522077560425}, {"X": 0.8956724405288696, "Y": 0.4137449562549591}, {"X": 0.8231116533279419, "Y": 0.4137445092201233}]}, "Id": "473f6796-1c79-49c0-a012-dc8370c22fe4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0755", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06733584403991699, "Height": 0.012236219830811024, "Left": 0.8282186985015869, "Top": 0.4362069368362427}, "Polygon": [{"X": 0.8282186985015869, "Y": 0.4362069368362427}, {"X": 0.8955544829368591, "Y": 0.4362073838710785}, {"X": 0.8955545425415039, "Y": 0.44844314455986023}, {"X": 0.8282188177108765, "Y": 0.4484426975250244}]}, "Id": "95aa84fe-4b30-4315-9d60-8d1786e00387"}, {"BlockType": "WORD", "Confidence": 69.3919448852539, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05929342284798622, "Height": 0.045260827988386154, "Left": 0.0594523586332798, "Top": 0.4380947947502136}, "Polygon": [{"X": 0.05945317819714546, "Y": 0.48335516452789307}, {"X": 0.0594523586332798, "Y": 0.4380947947502136}, {"X": 0.11874500662088394, "Y": 0.43809518218040466}, {"X": 0.11874578148126602, "Y": 0.4833556115627289}]}, "Id": "74ea912c-3298-4f93-9faf-9f2811e368a1"}, {"BlockType": "WORD", "Confidence": 89.5838623046875, "Text": "3sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.056661881506443024, "Height": 0.011778688058257103, "Left": 0.15157723426818848, "Top": 0.4495817720890045}, "Polygon": [{"X": 0.15157723426818848, "Y": 0.4495817720890045}, {"X": 0.20823892951011658, "Y": 0.44958215951919556}, {"X": 0.2082391083240509, "Y": 0.46136045455932617}, {"X": 0.151577427983284, "Y": 0.46136003732681274}]}, "Id": "e2b25b5e-d282-4365-88f5-2dc4af8a55e6"}, {"BlockType": "WORD", "Confidence": 88.65214538574219, "Text": "2sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.059546131640672684, "Height": 0.012924063950777054, "Left": 0.4554550051689148, "Top": 0.44218820333480835}, "Polygon": [{"X": 0.4554550051689148, "Y": 0.44218820333480835}, {"X": 0.5150009989738464, "Y": 0.4421885907649994}, {"X": 0.515001118183136, "Y": 0.4551122486591339}, {"X": 0.45545515418052673, "Y": 0.4551118314266205}]}, "Id": "16f243b7-6c01-46ba-a256-aeddf12e3e39"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Automation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09870389848947525, "Height": 0.007369108498096466, "Left": 0.12197329849004745, "Top": 0.46157196164131165}, "Polygon": [{"X": 0.12197329849004745, "Y": 0.46157196164131165}, {"X": 0.22067707777023315, "Y": 0.46157267689704895}, {"X": 0.2206771969795227, "Y": 0.4689410626888275}, {"X": 0.1219734251499176, "Y": 0.4689403474330902}]}, "Id": "d2cb6913-dd4a-41da-b59f-0b48c603c80b"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014428775757551193, "Height": 0.007246694527566433, "Left": 0.22665175795555115, "Top": 0.4615752398967743}, "Polygon": [{"X": 0.22665175795555115, "Y": 0.4615752398967743}, {"X": 0.2410804182291031, "Y": 0.46157532930374146}, {"X": 0.24108052253723145, "Y": 0.4688219130039215}, {"X": 0.2266518622636795, "Y": 0.46882182359695435}]}, "Id": "25717d81-945d-46cf-b9b8-447979497817"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Modular", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06844867765903473, "Height": 0.007787647191435099, "Left": 0.2462185174226761, "Top": 0.4610702395439148}, "Polygon": [{"X": 0.2462185174226761, "Y": 0.4610702395439148}, {"X": 0.31466710567474365, "Y": 0.4610707461833954}, {"X": 0.3146671950817108, "Y": 0.46885791420936584}, {"X": 0.24621863663196564, "Y": 0.46885740756988525}]}, "Id": "8916862d-04f6-4158-837a-e1ba7b8c6b9e"}, {"BlockType": "WORD", "Confidence": 87.73197937011719, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010778182186186314, "Height": 0.005908254999667406, "Left": 0.364133358001709, "Top": 0.46260178089141846}, "Polygon": [{"X": 0.364133358001709, "Y": 0.46260178089141846}, {"X": 0.37491145730018616, "Y": 0.46260184049606323}, {"X": 0.37491151690483093, "Y": 0.4685100317001343}, {"X": 0.36413341760635376, "Y": 0.4685099422931671}]}, "Id": "57dec8ae-8d3d-4cbd-b71d-8712ebba3b72"}, {"BlockType": "WORD", "Confidence": 85.74797058105469, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011137685738503933, "Height": 0.005786150228232145, "Left": 0.42676064372062683, "Top": 0.46268731355667114}, "Polygon": [{"X": 0.42676064372062683, "Y": 0.46268731355667114}, {"X": 0.43789827823638916, "Y": 0.4626874029636383}, {"X": 0.43789833784103394, "Y": 0.4684734642505646}, {"X": 0.426760733127594, "Y": 0.4684734046459198}]}, "Id": "c77dec95-b735-4ae2-9be6-1c6324763f47"}, {"BlockType": "WORD", "Confidence": 99.08482360839844, "Text": "SO-2116134", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08608701080083847, "Height": 0.006733916234225035, "Left": 0.46426793932914734, "Top": 0.4611221253871918}, "Polygon": [{"X": 0.46426793932914734, "Y": 0.4611221253871918}, {"X": 0.5503548979759216, "Y": 0.4611227512359619}, {"X": 0.5503549575805664, "Y": 0.4678560197353363}, {"X": 0.4642680287361145, "Y": 0.46785539388656616}]}, "Id": "cf8f66cb-71d0-4821-8914-b0834ad58882"}, {"BlockType": "WORD", "Confidence": 99.5655288696289, "Text": "2116736", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14135520160198212, "Height": 0.01314903236925602, "Left": 0.5804247856140137, "Top": 0.4552437663078308}, "Polygon": [{"X": 0.5804247856140137, "Y": 0.4552437663078308}, {"X": 0.7217798829078674, "Y": 0.455244779586792}, {"X": 0.721780002117157, "Y": 0.4683927893638611}, {"X": 0.580424964427948, "Y": 0.4683917462825775}]}, "Id": "b17769df-12bb-4986-8c24-906088bfac68"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0815", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05711528658866882, "Height": 0.010189207270741463, "Left": 0.8299935460090637, "Top": 0.47014498710632324}, "Polygon": [{"X": 0.8299935460090637, "Y": 0.47014498710632324}, {"X": 0.8871087431907654, "Y": 0.47014540433883667}, {"X": 0.8871088027954102, "Y": 0.48033419251441956}, {"X": 0.8299936056137085, "Y": 0.48033374547958374}]}, "Id": "427bb0e6-f717-4c50-a45d-5dbfafa97d66"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Detroit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05822712928056717, "Height": 0.007702106609940529, "Left": 0.48029184341430664, "Top": 0.7640868425369263}, "Polygon": [{"X": 0.48029184341430664, "Y": 0.7640868425369263}, {"X": 0.5385189056396484, "Y": 0.7640876173973083}, {"X": 0.5385189652442932, "Y": 0.7717889547348022}, {"X": 0.4802919328212738, "Y": 0.7717881202697754}]}, "Id": "a75e82d6-5cf4-4d86-bea6-37a4c972d8a9"}, {"BlockType": "WORD", "Confidence": 85.14867401123047, "Text": "4", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.055564768612384796, "Height": 0.02554180473089218, "Left": 0.05213910713791847, "Top": 0.9272449016571045}, "Polygon": [{"X": 0.05213910713791847, "Y": 0.9272449016571045}, {"X": 0.10770343244075775, "Y": 0.9272458553314209}, {"X": 0.10770387947559357, "Y": 0.9527866840362549}, {"X": 0.05213957279920578, "Y": 0.9527857303619385}]}, "Id": "796e1a73-afe0-4af4-8019-c05cc513f2f6"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "cd2dca38-b66e-4865-80b8-84f14840b65f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "cd2dca38-b66e-4865-80b8-84f14840b65f", "content-type": "application/x-amz-json-1.1", "content-length": "51126", "date": "Thu, 18 Sep 2025 09:00:32 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "30b5ee6a-ad9b-4055-827c-4eaf8e928b79", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 09:00:36 GMT", "content-type": "application/json", "content-length": "2474", "connection": "keep-alive", "x-amzn-requestid": "30b5ee6a-ad9b-4055-827c-4eaf8e928b79"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have to classify the first page. The text appears to be some kind of work order or load ticket. There's no explicit header indicating Invoice, etc. The content: \"DATE: 24-Feb\", \"UNIT #: 92644/283420\", \"Truck Ready Time: 5:00\", \"DRIVER: KARRA\", \"Truck Departure: 5:00\", \"DEPARTURE TIME: 5am\", \"PICK Dock Time CUSTOMER UP DEL WORK ORDER #\", \"*********** *********** ...\". It lists several SO numbers, maybe \"Sales Order\" references. It's likely a \"Work Order\" or \"Load Ticket\" maybe a \"bol\"? Let's see if it has BOL or Bill of Lading? No. Does it have shipment description? No. It has \"Truck Ready Time\", \"Driver\", \"Work Order #\", \"SO\". This might be a \"Load Ticket\" or \"Transportation Work Order.\" In the list of doc types: we have \"combined_carrier_documents\", \"log\", \"ingate/outgate\". But this looks more like a \"work order\" or \"load ticket\" used to schedule a truck. That might be considered \"ingate\" or \"outgate\"? The page content does not show gates, but a \"Truck Ready Time\". Could be a \"Load Ticket\" which might be \"ingate\" or \"outgate\" but not exactly. Could be \"log\" but no logbook style. \"Load Ticket\" is not an enum. \"combined_carrier_documents\" includes multiple documents. This seems just a single doc.\n\nGiven the classification options, maybe \"other\" is appropriate because it doesn't match any known categories. But we should think: does it match \"po\"? No, no Purchase Order. \"so_confirmation\"? No. \"comm_invoice\"? No. \"rate_confirmation\"? No. \"pod\"? No. \"bol\"? No. \"log\"? No. \"scale_ticket\"? No weight. \"ingate/outgate\"? Might have arrival time but not gate-specific. It includes \"Truck Ready Time\" and \"Departure Time\". Could be \"ingate\" if it's a document that indicates the truck is ready to go in? But ingate usually has \"Truck In\" time and \"seal number\". Not present.\n\nThus likely \"other\".\n\nBut the instructions: Must output using tool call classify_logistics_doc_type with array of objects: {page_no: 1, doc_type: \"other\"}.\n\nLet's produce that."}}}, {"toolUse": {"toolUseId": "tooluse_0fdXM8hGQEaD5GhSfJ7lAg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "other"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2263, "outputTokens": 551, "totalTokens": 2814}, "metrics": {"latencyMs": 2286}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/168b7bbb_8_log_5.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n09:57\n5Gc\n94\nDone\nRetake\nDATE: 24-Feb\nUNIT #: 92644/283420\nTruck Ready Time: 5:00\nDRIVER: KARRA\nTruck Departure:\n5:00\nDEPARTURE TIME: 5am\nPICK\nDock Time\nCUSTOMER\nUP\nDEL\nWORK ORDER #\nx\nI small Sk\nISK\n0705\nWilliams Gunsight\nX\nX\n*********** ***********\n0710\n0755\nX\n3sk\n2sk\nAutomation & Modular\nX\nX\nSO-2116134\n2116736\n0815\nDetroit\n4\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}