2025-09-23 17:19:21,030 - INFO - Logging initialized. Log file: logs/test_classification_20250923_171921.log
2025-09-23 17:19:21,030 - INFO - 📁 Found 1 files to process
2025-09-23 17:19:21,030 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 17:19:21,030 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 17:19:21,030 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 17:19:21,030 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 17:19:21,031 - INFO - ⬆️ [17:19:21] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:19:26,505 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/0e183435_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:19:26,505 - INFO - 🔍 [17:19:26] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:19:26,507 - INFO - Initializing TextractProcessor...
2025-09-23 17:19:26,522 - INFO - Initializing BedrockProcessor...
2025-09-23 17:19:26,528 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0e183435_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:19:26,528 - INFO - Processing PDF from S3...
2025-09-23 17:19:26,528 - INFO - Downloading PDF from S3 to /tmp/tmpu3d0igrx/0e183435_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:19:30,601 - INFO - Downloaded PDF size: 1.1 MB
2025-09-23 17:19:30,601 - INFO - Splitting PDF into individual pages...
2025-09-23 17:19:30,604 - INFO - Splitting PDF 0e183435_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 17:19:30,616 - INFO - Split PDF into 7 pages
2025-09-23 17:19:30,616 - INFO - Processing pages with Textract in parallel...
2025-09-23 17:19:30,616 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 17:19:35,247 - INFO - Page 3: Extracted 929 characters, 64 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_003.pdf
2025-09-23 17:19:35,248 - INFO - Successfully processed page 3
2025-09-23 17:19:35,283 - INFO - Page 2: Extracted 1174 characters, 29 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_002.pdf
2025-09-23 17:19:35,284 - INFO - Successfully processed page 2
2025-09-23 17:19:35,310 - INFO - Page 1: Extracted 1168 characters, 45 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_001.pdf
2025-09-23 17:19:35,310 - INFO - Successfully processed page 1
2025-09-23 17:19:35,502 - INFO - Page 4: Extracted 1151 characters, 63 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_004.pdf
2025-09-23 17:19:35,502 - INFO - Successfully processed page 4
2025-09-23 17:19:35,834 - INFO - Page 5: Extracted 2022 characters, 27 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_005.pdf
2025-09-23 17:19:35,834 - INFO - Successfully processed page 5
2025-09-23 17:19:37,239 - INFO - Page 6: Extracted 4159 characters, 115 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_006.pdf
2025-09-23 17:19:37,239 - INFO - Successfully processed page 6
2025-09-23 17:19:39,109 - INFO - Page 7: Extracted 788 characters, 55 lines from 0e183435_Purchase_Order_VRSD0E9045781_b59adb84_page_007.pdf
2025-09-23 17:19:39,109 - INFO - Successfully processed page 7
2025-09-23 17:19:39,110 - INFO - Combined 7 pages into final text
2025-09-23 17:19:39,111 - INFO - Text validation for 0e183435_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 17:19:39,112 - INFO - Analyzing document types with Bedrock...
2025-09-23 17:19:39,112 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 17:19:44,877 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-23 17:19:44,877 - ERROR - Processing failed for s3://document-extraction-logistically/temp/0e183435_Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:19:44,878 - ERROR - ✗ Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:19:46,102 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0e183435_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:19:46,103 - INFO - 
📊 Processing Summary:
2025-09-23 17:19:46,104 - INFO -    Total files: 1
2025-09-23 17:19:46,104 - INFO -    Successful: 0
2025-09-23 17:19:46,104 - INFO -    Failed: 1
2025-09-23 17:19:46,104 - INFO -    Duration: 25.07 seconds
2025-09-23 17:19:46,104 - INFO -    Output directory: output
2025-09-23 17:19:46,104 - ERROR - 
❌ Errors:
2025-09-23 17:19:46,104 - ERROR -    Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:19:46,104 - INFO - 
📋 No files were successfully processed.
2025-09-23 17:19:46,104 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 25.072759, 'processed_files': []}
