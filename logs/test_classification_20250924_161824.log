2025-09-24 16:18:24,681 - INFO - Logging initialized. Log file: logs/test_classification_20250924_161824.log
2025-09-24 16:18:24,682 - INFO - 📁 Found 12 files to process
2025-09-24 16:18:24,682 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 16:18:24,682 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 16:18:24,682 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 16:18:24,682 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 16:18:24,682 - INFO - ⬆️ [16:18:24] Uploading: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:27,057 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf -> s3://document-extraction-logistically/temp/b193a44b_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:27,057 - INFO - 🔍 [16:18:27] Starting classification: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:27,058 - INFO - ⬆️ [16:18:27] Uploading: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:27,058 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:27,079 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:27,085 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b193a44b_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:27,085 - INFO - Processing PDF from S3...
2025-09-24 16:18:27,085 - INFO - Downloading PDF from S3 to /tmp/tmpidmtxd07/b193a44b_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:28,938 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:18:28,938 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:28,941 - INFO - Splitting PDF b193a44b_AYEA5J1NILYPMWA7PN4V into 2 pages
2025-09-24 16:18:28,946 - INFO - Split PDF into 2 pages
2025-09-24 16:18:28,947 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:28,947 - INFO - Expected pages: [1, 2]
2025-09-24 16:18:29,941 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf -> s3://document-extraction-logistically/temp/38b614df_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:29,942 - INFO - 🔍 [16:18:29] Starting classification: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:29,942 - INFO - ⬆️ [16:18:29] Uploading: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:29,945 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:29,965 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:29,970 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/38b614df_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:29,970 - INFO - Processing PDF from S3...
2025-09-24 16:18:29,970 - INFO - Downloading PDF from S3 to /tmp/tmpczj2x5r2/38b614df_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:31,550 - INFO - Page 2: Extracted 216 characters, 11 lines from b193a44b_AYEA5J1NILYPMWA7PN4V_55ecb5ea_page_002.pdf
2025-09-24 16:18:31,550 - INFO - Successfully processed page 2
2025-09-24 16:18:32,504 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf -> s3://document-extraction-logistically/temp/3c893abf_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:32,505 - INFO - 🔍 [16:18:32] Starting classification: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:32,505 - INFO - ⬆️ [16:18:32] Uploading: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:32,508 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:32,526 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:32,531 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3c893abf_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:32,531 - INFO - Processing PDF from S3...
2025-09-24 16:18:32,532 - INFO - Downloading PDF from S3 to /tmp/tmphv2uh8m7/3c893abf_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:32,577 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:18:32,578 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:32,583 - INFO - Splitting PDF 38b614df_C3I3XLR18U7J6P1N2LZR into 1 pages
2025-09-24 16:18:32,691 - INFO - Split PDF into 1 pages
2025-09-24 16:18:32,691 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:32,691 - INFO - Expected pages: [1]
2025-09-24 16:18:34,042 - INFO - Page 1: Extracted 2153 characters, 114 lines from b193a44b_AYEA5J1NILYPMWA7PN4V_55ecb5ea_page_001.pdf
2025-09-24 16:18:34,042 - INFO - Successfully processed page 1
2025-09-24 16:18:34,043 - INFO - Combined 2 pages into final text
2025-09-24 16:18:34,043 - INFO - Text validation for b193a44b_AYEA5J1NILYPMWA7PN4V: 2405 characters, 2 pages
2025-09-24 16:18:34,043 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:34,043 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:34,207 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf -> s3://document-extraction-logistically/temp/a03d58bd_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:34,207 - INFO - 🔍 [16:18:34] Starting classification: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:34,207 - INFO - ⬆️ [16:18:34] Uploading: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:34,208 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:34,215 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:34,216 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a03d58bd_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:34,217 - INFO - Processing PDF from S3...
2025-09-24 16:18:34,217 - INFO - Downloading PDF from S3 to /tmp/tmp2078f8b_/a03d58bd_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:35,417 - INFO - Downloaded PDF size: 1.4 MB
2025-09-24 16:18:35,417 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:35,418 - INFO - Splitting PDF 3c893abf_C94JBR3RYYOOM5J2PFUM into 1 pages
2025-09-24 16:18:35,421 - INFO - Split PDF into 1 pages
2025-09-24 16:18:35,421 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:35,421 - INFO - Expected pages: [1]
2025-09-24 16:18:35,533 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg -> s3://document-extraction-logistically/temp/c14d381d_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:35,534 - INFO - 🔍 [16:18:35] Starting classification: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:35,534 - INFO - ⬆️ [16:18:35] Uploading: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:35,536 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:35,556 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:35,560 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c14d381d_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:35,560 - INFO - Processing image from S3...
2025-09-24 16:18:36,231 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf -> s3://document-extraction-logistically/temp/0b7b3826_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:36,231 - INFO - 🔍 [16:18:36] Starting classification: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:36,231 - INFO - ⬆️ [16:18:36] Uploading: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:36,232 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:36,246 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:36,251 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0b7b3826_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:36,252 - INFO - Processing PDF from S3...
2025-09-24 16:18:36,252 - INFO - Downloading PDF from S3 to /tmp/tmp1jay6qol/0b7b3826_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:36,787 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 16:18:36,787 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:36,788 - INFO - Splitting PDF a03d58bd_DY3D94HTH1ZH420GMDO6 into 1 pages
2025-09-24 16:18:36,790 - INFO - Split PDF into 1 pages
2025-09-24 16:18:36,790 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:36,790 - INFO - Expected pages: [1]
2025-09-24 16:18:36,912 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf -> s3://document-extraction-logistically/temp/238d12c3_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:36,912 - INFO - 🔍 [16:18:36] Starting classification: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:36,913 - INFO - ⬆️ [16:18:36] Uploading: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:36,913 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:36,922 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:36,925 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/238d12c3_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:36,925 - INFO - Processing PDF from S3...
2025-09-24 16:18:36,926 - INFO - Downloading PDF from S3 to /tmp/tmpe81twico/238d12c3_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:37,753 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '943ca652-388b-4cd4-a139-7365a7e18e92', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:37 GMT', 'content-type': 'application/json', 'content-length': '1690', 'connection': 'keep-alive', 'x-amzn-requestid': '943ca652-388b-4cd4-a139-7365a7e18e92'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be a "Sea Freight FCL Delivery Order" from Green Worldwide Shipping. This looks like a delivery order, basically a shipping order. Which category matches? There is no direct category for "delivery order" but maybe "bol" for Bill of Lading? But it\'s a delivery order, not a BOL. The doc type "bol" is Bill of Lading. This is a delivery order. The definitions given: "bol — \'Devilery Order\' or \'Bill of Lading\'". Yes, "bol" includes "Delivery Order" or "Bill of Lading". The page includes "Sea Freight FCL Delivery Order" header. So doc_type should be bol. Also, there are no repeated pages? It is a two-page document. The second page is just "END OF DOCUMENT". The second page should be same type bol, not continuation? It says "Continued Over" on page1 (maybe page1 continues). So page2 is continuation. Should assign same type. According to instructions: If page is part of multi-page single document: each page still gets same doc_type. So both pages should be bol. Also check continuation detection: page1 ends with "Continued Over". That indicates continuation. Page2 is end. So both are bol.\n\nThus output array: {page_no:1, doc_type:"bol"}, {page_no:2, doc_type:"bol"}.\n\nWe must call function classify_logistics_doc_type with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3281, 'outputTokens': 338, 'totalTokens': 3619}, 'metrics': {'latencyMs': 1768}}
2025-09-24 16:18:37,754 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b193a44b_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:38,121 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:18:38,122 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:38,123 - INFO - Splitting PDF 0b7b3826_GJ7Z12W1O0CQO9E9H39H into 2 pages
2025-09-24 16:18:38,138 - INFO - Split PDF into 2 pages
2025-09-24 16:18:38,138 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:38,138 - INFO - Expected pages: [1, 2]
2025-09-24 16:18:38,633 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf -> s3://document-extraction-logistically/temp/90c65f19_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:38,633 - INFO - 🔍 [16:18:38] Starting classification: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:38,634 - INFO - ⬆️ [16:18:38] Uploading: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:38,636 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:38,655 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:38,660 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/90c65f19_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:38,660 - INFO - Processing PDF from S3...
2025-09-24 16:18:38,661 - INFO - Downloading PDF from S3 to /tmp/tmpc3aayvav/90c65f19_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:38,905 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:18:38,906 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:38,907 - INFO - Splitting PDF 238d12c3_N9XYNNZR54XFORX6RGH2 into 2 pages
2025-09-24 16:18:38,910 - INFO - Split PDF into 2 pages
2025-09-24 16:18:38,910 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:38,910 - INFO - Expected pages: [1, 2]
2025-09-24 16:18:39,244 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf -> s3://document-extraction-logistically/temp/6c62a8a2_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:39,244 - INFO - 🔍 [16:18:39] Starting classification: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:39,245 - INFO - ⬆️ [16:18:39] Uploading: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:39,246 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:39,265 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:39,273 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6c62a8a2_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:39,274 - INFO - Processing PDF from S3...
2025-09-24 16:18:39,274 - INFO - Downloading PDF from S3 to /tmp/tmpyqtk3cj_/6c62a8a2_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:39,300 - INFO - S3 Image temp/c14d381d_G7K3JPMT2OXZWEI7RRNQ.jpg: Extracted 2237 characters, 96 lines
2025-09-24 16:18:39,300 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:39,300 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:39,405 - INFO - Page 1: Extracted 1847 characters, 114 lines from 38b614df_C3I3XLR18U7J6P1N2LZR_7e4072a4_page_001.pdf
2025-09-24 16:18:39,406 - INFO - Successfully processed page 1
2025-09-24 16:18:39,406 - INFO - Combined 1 pages into final text
2025-09-24 16:18:39,406 - INFO - Text validation for 38b614df_C3I3XLR18U7J6P1N2LZR: 1864 characters, 1 pages
2025-09-24 16:18:39,407 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:39,407 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:39,894 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf -> s3://document-extraction-logistically/temp/01fbb649_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:39,894 - INFO - 🔍 [16:18:39] Starting classification: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:39,895 - INFO - ⬆️ [16:18:39] Uploading: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:39,896 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:39,912 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:39,923 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/01fbb649_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:39,923 - INFO - Processing PDF from S3...
2025-09-24 16:18:39,924 - INFO - Downloading PDF from S3 to /tmp/tmpo9qznfyy/01fbb649_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:40,834 - INFO - Page 2: Extracted 0 characters, 0 lines from 0b7b3826_GJ7Z12W1O0CQO9E9H39H_70874c18_page_002.pdf
2025-09-24 16:18:40,834 - INFO - Successfully processed page 2
2025-09-24 16:18:41,098 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:18:41,098 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:41,099 - INFO - Splitting PDF 6c62a8a2_ODBS2NBTU6S3GF9JVE6V into 1 pages
2025-09-24 16:18:41,104 - INFO - Split PDF into 1 pages
2025-09-24 16:18:41,104 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:41,104 - INFO - Expected pages: [1]
2025-09-24 16:18:41,153 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '06d6c060-03ef-4418-b72b-694e82263296', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:40 GMT', 'content-type': 'application/json', 'content-length': '606', 'connection': 'keep-alive', 'x-amzn-requestid': '06d6c060-03ef-4418-b72b-694e82263296'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to output classification for each page. Only one page. Determine doc_type.\n\nPage shows Bill of Lading header. Contains typical BOL. So doc_type: bol.\n\nCheck continuation: no.\n\nReturn via function.'}}}, {'toolUse': {'toolUseId': 'tooluse_5AsAZPeDS4ilSotyNuNw2Q', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3060, 'outputTokens': 62, 'totalTokens': 3122}, 'metrics': {'latencyMs': 530}}
2025-09-24 16:18:41,153 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/38b614df_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:41,245 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:18:41,245 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:41,246 - INFO - Splitting PDF 90c65f19_NFG5QQXEN21TEZZ9SYPW into 2 pages
2025-09-24 16:18:41,251 - INFO - Split PDF into 2 pages
2025-09-24 16:18:41,251 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:41,251 - INFO - Expected pages: [1, 2]
2025-09-24 16:18:41,543 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f96c34b8-8841-4652-bed7-6e91b0d5798f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:41 GMT', 'content-type': 'application/json', 'content-length': '970', 'connection': 'keep-alive', 'x-amzn-requestid': 'f96c34b8-8841-4652-bed7-6e91b0d5798f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content is a "DELIVERY ORDER" and includes details: "DELIVERY ORDER" header, references to delivery, load details, shipping info. It\'s a delivery order, which is a type of Bill of Lading or Delivery Order. The categories: bol — Bill of Lading. The description indicates a delivery order, but that is a type of BOL. The keywords: "DELIVERY ORDER", "B/L OR AWB NO", "HAWB NO", "CARRIERS LOCAL AGENT". It\'s a bill of lading or delivery order; likely bol. So doc_type: bol. Page number 1. So output: documents array with one object: page_no: 1, doc_type: "bol".\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3226, 'outputTokens': 184, 'totalTokens': 3410}, 'metrics': {'latencyMs': 1059}}
2025-09-24 16:18:41,543 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c14d381d_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:41,674 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:18:41,674 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:41,675 - INFO - Splitting PDF 01fbb649_SMX8DOTQ89U191SMP0TO into 1 pages
2025-09-24 16:18:41,676 - INFO - Split PDF into 1 pages
2025-09-24 16:18:41,676 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:41,676 - INFO - Expected pages: [1]
2025-09-24 16:18:42,088 - INFO - Page 1: Extracted 3353 characters, 164 lines from 3c893abf_C94JBR3RYYOOM5J2PFUM_26d1260a_page_001.pdf
2025-09-24 16:18:42,089 - INFO - Successfully processed page 1
2025-09-24 16:18:42,089 - INFO - Combined 1 pages into final text
2025-09-24 16:18:42,089 - INFO - Text validation for 3c893abf_C94JBR3RYYOOM5J2PFUM: 3370 characters, 1 pages
2025-09-24 16:18:42,090 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:42,090 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:42,505 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg -> s3://document-extraction-logistically/temp/e8cbcde9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:42,506 - INFO - 🔍 [16:18:42] Starting classification: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:42,507 - INFO - ⬆️ [16:18:42] Uploading: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:42,509 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:42,528 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:42,532 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e8cbcde9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:42,533 - INFO - Processing image from S3...
2025-09-24 16:18:42,680 - INFO - Page 1: Extracted 1185 characters, 52 lines from a03d58bd_DY3D94HTH1ZH420GMDO6_7386a697_page_001.pdf
2025-09-24 16:18:42,680 - INFO - Successfully processed page 1
2025-09-24 16:18:42,680 - INFO - Combined 1 pages into final text
2025-09-24 16:18:42,680 - INFO - Text validation for a03d58bd_DY3D94HTH1ZH420GMDO6: 1202 characters, 1 pages
2025-09-24 16:18:42,681 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:42,681 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:43,136 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf -> s3://document-extraction-logistically/temp/fea4d9cd_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:43,136 - INFO - 🔍 [16:18:43] Starting classification: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:43,138 - INFO - Initializing TextractProcessor...
2025-09-24 16:18:43,151 - INFO - Initializing BedrockProcessor...
2025-09-24 16:18:43,158 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fea4d9cd_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:43,160 - INFO - Processing PDF from S3...
2025-09-24 16:18:43,162 - INFO - Downloading PDF from S3 to /tmp/tmptp4xf1ui/fea4d9cd_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:43,195 - INFO - 

AYEA5J1NILYPMWA7PN4V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:43,195 - INFO - 

✓ Saved result: output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:18:43,244 - INFO - Page 2: Extracted 1932 characters, 48 lines from 238d12c3_N9XYNNZR54XFORX6RGH2_8aed781b_page_002.pdf
2025-09-24 16:18:43,245 - INFO - Successfully processed page 2
2025-09-24 16:18:43,487 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b193a44b_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:43,506 - INFO - 

C3I3XLR18U7J6P1N2LZR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:43,506 - INFO - 

✓ Saved result: output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:18:43,733 - INFO - Page 1: Extracted 4034 characters, 142 lines from 0b7b3826_GJ7Z12W1O0CQO9E9H39H_70874c18_page_001.pdf
2025-09-24 16:18:43,733 - INFO - Successfully processed page 1
2025-09-24 16:18:43,734 - INFO - Combined 2 pages into final text
2025-09-24 16:18:43,734 - INFO - Text validation for 0b7b3826_GJ7Z12W1O0CQO9E9H39H: 4069 characters, 2 pages
2025-09-24 16:18:43,734 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:43,734 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:43,790 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/38b614df_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:43,811 - INFO - 

G7K3JPMT2OXZWEI7RRNQ.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:43,811 - INFO - 

✓ Saved result: output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:18:44,012 - INFO - Page 1: Extracted 1616 characters, 146 lines from 238d12c3_N9XYNNZR54XFORX6RGH2_8aed781b_page_001.pdf
2025-09-24 16:18:44,012 - INFO - Successfully processed page 1
2025-09-24 16:18:44,012 - INFO - Combined 2 pages into final text
2025-09-24 16:18:44,012 - INFO - Text validation for 238d12c3_N9XYNNZR54XFORX6RGH2: 3584 characters, 2 pages
2025-09-24 16:18:44,012 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:44,012 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:44,097 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c14d381d_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:44,375 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '33aaaab3-b622-445a-9f3e-49ca8f425a9a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:44 GMT', 'content-type': 'application/json', 'content-length': '519', 'connection': 'keep-alive', 'x-amzn-requestid': '33aaaab3-b622-445a-9f3e-49ca8f425a9a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We must classify each page. Only one page. We analyze content. It\'s a Bill of Lading. Keywords: "BILL OF LADING", "BOL". So doc_type: bol. Not a combined page. So output: page_no 1 doc_type bol. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2789, 'outputTokens': 76, 'totalTokens': 2865}, 'metrics': {'latencyMs': 534}}
2025-09-24 16:18:44,376 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a03d58bd_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:44,388 - INFO - 

DY3D94HTH1ZH420GMDO6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:44,388 - INFO - 

✓ Saved result: output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:18:44,672 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a03d58bd_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:44,690 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a105ff3a-1246-4af4-8230-eded414bb4b1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:44 GMT', 'content-type': 'application/json', 'content-length': '1389', 'connection': 'keep-alive', 'x-amzn-requestid': 'a105ff3a-1246-4af4-8230-eded414bb4b1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We must determine document type. The text shows "Air Waybill". That\'s a commercial invoice? Actually Air Waybill is a transport document, not invoice. It\'s like a Bill of Lading for air. But doc types: bol (Bill of Lading) includes BOL, Bill of Lading, etc. But Air Waybill is a type of Bill of Lading for air. Should be bol? But bol definition: "Bill of Lading" includes BOL, Shipper, Consignee, etc. Air Waybill is similar. I think bol. But check other types: comm_invoice requires HS codes and customs values. This has HS code and declared value etc. But it\'s an Air Waybill, not invoice. The doc type list includes "combined_carrier_documents" if multiple headers. But only one header: Air Waybill. So bol seems correct.\n\nBut also note "Invoice" requires "Invoice", "Invoice No". Not present. "scale_ticket" requires weight terms. It has weight but also BOL. But scale ticket would have weight and gross, etc. But this includes more. So bol.\n\nThus output: page 1 => bol.\n\nWe must return JSON via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3445, 'outputTokens': 273, 'totalTokens': 3718}, 'metrics': {'latencyMs': 1420}}
2025-09-24 16:18:44,691 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3c893abf_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:44,741 - INFO - 

C94JBR3RYYOOM5J2PFUM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:44,741 - INFO - 

✓ Saved result: output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:18:45,029 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3c893abf_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:45,259 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 16:18:45,260 - INFO - Splitting PDF into individual pages...
2025-09-24 16:18:45,263 - INFO - Splitting PDF fea4d9cd_Z10BLMPIZ96XLUZ5NUPF into 1 pages
2025-09-24 16:18:45,338 - INFO - Split PDF into 1 pages
2025-09-24 16:18:45,338 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:18:45,338 - INFO - Expected pages: [1]
2025-09-24 16:18:45,940 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9be6a40c-4376-4dc7-ab65-93a5d7cf537f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:45 GMT', 'content-type': 'application/json', 'content-length': '723', 'connection': 'keep-alive', 'x-amzn-requestid': '9be6a40c-4376-4dc7-ab65-93a5d7cf537f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Both contain Bill of Lading. Page1 clearly "BILL OF LADING" header. Page2 continues details, signatures, etc. It\'s same BOL. So doc_type: bol for both pages.\n\nWe must return array of objects for each page. Format expected: use tool classify_logistics_doc_type with documents array containing objects with page_no and doc_type.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3557, 'outputTokens': 111, 'totalTokens': 3668}, 'metrics': {'latencyMs': 715}}
2025-09-24 16:18:45,941 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/238d12c3_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:45,986 - INFO - 

N9XYNNZR54XFORX6RGH2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:45,986 - INFO - 

✓ Saved result: output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:18:46,059 - INFO - Page 1: Extracted 2057 characters, 76 lines from 6c62a8a2_ODBS2NBTU6S3GF9JVE6V_62af6704_page_001.pdf
2025-09-24 16:18:46,059 - INFO - Successfully processed page 1
2025-09-24 16:18:46,060 - INFO - Combined 1 pages into final text
2025-09-24 16:18:46,060 - INFO - Text validation for 6c62a8a2_ODBS2NBTU6S3GF9JVE6V: 2074 characters, 1 pages
2025-09-24 16:18:46,060 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:46,060 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:46,312 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/238d12c3_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:46,391 - INFO - S3 Image temp/e8cbcde9_Y7P2IPB0S079FZE11EEA.jpg: Extracted 821 characters, 52 lines
2025-09-24 16:18:46,391 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:46,391 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:46,658 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9d1a2b69-879b-4def-ac85-e0a8fe472835', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:46 GMT', 'content-type': 'application/json', 'content-length': '1953', 'connection': 'keep-alive', 'x-amzn-requestid': '9d1a2b69-879b-4def-ac85-e0a8fe472835'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a page with a Bill of Lading. So doc_type "bol". Need to output for page1: doc_type bol. For page2: it\'s empty. But instructions: For every page must return exactly one object. Page2 is empty but still part of PDF. Should we classify as continuation? There\'s no text. Page2 likely continuation? The page shows nothing. Could be blank continuation? The content indicates page2 blank. According to instructions: "If document is not from any of catagories mentioned, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign the same doc_type as previous page." The page is blank but might be continuation. There\'s no "continued" word. However blank page may be a continuation. But guidelines: "strictly check if the current page is a continuation of the previous page by checking if the page starts with any of the following: \'continued\', \'continued on next page\', \'continued on next\', etc., or if it indicates pagination like \'page 2 of 3\', or any other signal indicating continuation of the previous page/document." The page doesn\'t have such. So we must decide classification. Since no content, but could be considered a continuation? The instruction says if not from any categories, classify as other. But check if continuation: The page is blank, not start with "continued". No pagination. So it\'s not continuation. So classify as other. But maybe the PDF has a second page that is blank but part of the document. Should we classify as other? I\'d go with other. So output two objects: page1 bol, page2 other. Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3585, 'outputTokens': 369, 'totalTokens': 3954}, 'metrics': {'latencyMs': 1619}}
2025-09-24 16:18:46,658 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0b7b3826_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:46,701 - INFO - 

GJ7Z12W1O0CQO9E9H39H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:18:46,701 - INFO - 

✓ Saved result: output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:18:46,986 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0b7b3826_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:47,191 - INFO - Page 1: Extracted 2916 characters, 126 lines from 01fbb649_SMX8DOTQ89U191SMP0TO_cfddba34_page_001.pdf
2025-09-24 16:18:47,191 - INFO - Successfully processed page 1
2025-09-24 16:18:47,191 - INFO - Combined 1 pages into final text
2025-09-24 16:18:47,191 - INFO - Text validation for 01fbb649_SMX8DOTQ89U191SMP0TO: 2933 characters, 1 pages
2025-09-24 16:18:47,191 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:47,191 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:47,573 - INFO - Page 2: Extracted 1028 characters, 92 lines from 90c65f19_NFG5QQXEN21TEZZ9SYPW_70b5f884_page_002.pdf
2025-09-24 16:18:47,573 - INFO - Successfully processed page 2
2025-09-24 16:18:48,240 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7503d8ff-2810-40ea-bb24-f3c55a27a305', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:48 GMT', 'content-type': 'application/json', 'content-length': '574', 'connection': 'keep-alive', 'x-amzn-requestid': '7503d8ff-2810-40ea-bb24-f3c55a27a305'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with a header "BILL OF LADING". The document contains PO #, carier, route, ship date, etc. So it\'s a Bill of Lading. That matches bol. Just one page. Need to return array of objects, each with page_no and doc_type. Only one page. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2759, 'outputTokens': 86, 'totalTokens': 2845}, 'metrics': {'latencyMs': 597}}
2025-09-24 16:18:48,251 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e8cbcde9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:48,253 - INFO - Page 1: Extracted 1621 characters, 166 lines from 90c65f19_NFG5QQXEN21TEZZ9SYPW_70b5f884_page_001.pdf
2025-09-24 16:18:48,258 - INFO - Successfully processed page 1
2025-09-24 16:18:48,259 - INFO - Combined 2 pages into final text
2025-09-24 16:18:48,260 - INFO - Text validation for 90c65f19_NFG5QQXEN21TEZZ9SYPW: 2685 characters, 2 pages
2025-09-24 16:18:48,272 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:48,274 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:48,281 - INFO - 

Y7P2IPB0S079FZE11EEA.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:48,282 - INFO - 

✓ Saved result: output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:18:48,564 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e8cbcde9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:48,801 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '93eb3ec2-5d0f-4125-a01f-ffc0d54d57a8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:48 GMT', 'content-type': 'application/json', 'content-length': '467', 'connection': 'keep-alive', 'x-amzn-requestid': '93eb3ec2-5d0f-4125-a01f-ffc0d54d57a8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page with Bill of Lading. Should classify as bol. Probably page 1. No continuation. Output one object with page_no:1 doc_type:"bol". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3258, 'outputTokens': 56, 'totalTokens': 3314}, 'metrics': {'latencyMs': 508}}
2025-09-24 16:18:48,802 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/01fbb649_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:48,848 - INFO - 

SMX8DOTQ89U191SMP0TO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:48,848 - INFO - 

✓ Saved result: output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:18:48,976 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f5855c82-1405-474b-8d36-f862e4f1e927', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:48 GMT', 'content-type': 'application/json', 'content-length': '1886', 'connection': 'keep-alive', 'x-amzn-requestid': 'f5855c82-1405-474b-8d36-f862e4f1e927'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It\'s a "Load Confirmation" from a broker. Might be a "tender_from_cust" or "load tender" or "load confirmation". The categories: tender_from_cust is Load Tender from Customer. This page likely is a "Load Confirmation" or "Load Tender" from a broker to a carrier. It\'s not a BOL, not invoice, not POD, etc.\n\nWe need to classify into one of the enum types: invoice, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, comm_invoice, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, tender_from_cust, so_confirmation, ingate, outgate, other.\n\nThe page includes "Load Confirmation # 1536427" and "Agreed Rate". It seems to confirm a load for the carrier. The broker is "THREE-D BROKERS LTD." The page includes "Load Confirmation" which is basically a tender. It\'s not a rate confirmation? It says "Agreed Rate" and "Rates include all Add-on and Surcharges." It might be a "tender_from_cust". The enum "tender_from_cust" is "Load Tender from Customer". Keywords: Load Tender, Tender, Tender from, Request to Carrier. This page has "Load Confirmation" but not explicitly "Load Tender". However, it\'s a broker load confirmation. But it\'s essentially a confirmation of load. I think the best match is "tender_from_cust". There\'s no other category that fits.\n\nWe should output the tool call with documents array: one object: {page_no: 1, doc_type: "tender_from_cust"}.\n\nReturn exactly that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3046, 'outputTokens': 391, 'totalTokens': 3437}, 'metrics': {'latencyMs': 1731}}
2025-09-24 16:18:48,976 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6c62a8a2_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:49,136 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/01fbb649_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:49,164 - INFO - 

ODBS2NBTU6S3GF9JVE6V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 16:18:49,164 - INFO - 

✓ Saved result: output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:18:49,455 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6c62a8a2_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:50,184 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6d492241-1747-4ac8-b875-50e578125b68', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:50 GMT', 'content-type': 'application/json', 'content-length': '779', 'connection': 'keep-alive', 'x-amzn-requestid': '6d492241-1747-4ac8-b875-50e578125b68'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document seems to be a "PKG LIST" - Packing List. The content includes header "PKG LIST" and lots of item details. There is no explicit "Invoice" or "BOL". So the type is pack_list.\n\nWe need to output using the tool classify_logistics_doc_type with documents array containing two objects: page_no 1 with doc_type "pack_list", page_no 2 same.\n\nReturn the tool call JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3738, 'outputTokens': 127, 'totalTokens': 3865}, 'metrics': {'latencyMs': 770}}
2025-09-24 16:18:50,185 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/90c65f19_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:50,234 - INFO - 

NFG5QQXEN21TEZZ9SYPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:18:50,234 - INFO - 

✓ Saved result: output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:18:50,519 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/90c65f19_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:51,116 - INFO - Page 1: Extracted 3550 characters, 185 lines from fea4d9cd_Z10BLMPIZ96XLUZ5NUPF_44e01b51_page_001.pdf
2025-09-24 16:18:51,116 - INFO - Successfully processed page 1
2025-09-24 16:18:51,116 - INFO - Combined 1 pages into final text
2025-09-24 16:18:51,117 - INFO - Text validation for fea4d9cd_Z10BLMPIZ96XLUZ5NUPF: 3567 characters, 1 pages
2025-09-24 16:18:51,117 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:18:51,117 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:18:53,251 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a80b3073-d089-4aa9-a1bc-9eafdd0434d3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:48:53 GMT', 'content-type': 'application/json', 'content-length': '843', 'connection': 'keep-alive', 'x-amzn-requestid': 'a80b3073-d089-4aa9-a1bc-9eafdd0434d3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine doc type.\n\nPage content: It says "BILL OF LADING" prominently. Also has details of shipment, consignor, consignee, vessel, order no, etc. This is a Bill of Lading. Not a commercial invoice because it lacks HS codes? Actually it lists UN and NMFC, not HS. So it\'s a regular BOL. So doc_type: bol.\n\nCheck for continuation: it is page 1 of 1. No continuation. So we output array with one object: page_no:1, doc_type:"bol".\n\nReturn via tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3730, 'outputTokens': 150, 'totalTokens': 3880}, 'metrics': {'latencyMs': 901}}
2025-09-24 16:18:53,251 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fea4d9cd_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:53,303 - INFO - 

Z10BLMPIZ96XLUZ5NUPF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:18:53,303 - INFO - 

✓ Saved result: output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:18:53,592 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fea4d9cd_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:53,594 - INFO - 
📊 Processing Summary:
2025-09-24 16:18:53,594 - INFO -    Total files: 12
2025-09-24 16:18:53,594 - INFO -    Successful: 12
2025-09-24 16:18:53,594 - INFO -    Failed: 0
2025-09-24 16:18:53,594 - INFO -    Duration: 28.91 seconds
2025-09-24 16:18:53,594 - INFO -    Output directory: output
2025-09-24 16:18:53,595 - INFO - 
📋 Successfully Processed Files:
2025-09-24 16:18:53,595 - INFO -    📄 AYEA5J1NILYPMWA7PN4V.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:18:53,595 - INFO -    📄 C3I3XLR18U7J6P1N2LZR.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,595 - INFO -    📄 C94JBR3RYYOOM5J2PFUM.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,595 - INFO -    📄 DY3D94HTH1ZH420GMDO6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,595 - INFO -    📄 G7K3JPMT2OXZWEI7RRNQ.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,595 - INFO -    📄 GJ7Z12W1O0CQO9E9H39H.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 16:18:53,595 - INFO -    📄 N9XYNNZR54XFORX6RGH2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:18:53,595 - INFO -    📄 NFG5QQXEN21TEZZ9SYPW.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-24 16:18:53,595 - INFO -    📄 ODBS2NBTU6S3GF9JVE6V.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"}]}
2025-09-24 16:18:53,595 - INFO -    📄 SMX8DOTQ89U191SMP0TO.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,596 - INFO -    📄 Y7P2IPB0S079FZE11EEA.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,596 - INFO -    📄 Z10BLMPIZ96XLUZ5NUPF.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:18:53,596 - INFO - 
============================================================================================================================================
2025-09-24 16:18:53,596 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 16:18:53,596 - INFO - ============================================================================================================================================
2025-09-24 16:18:53,597 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 16:18:53,597 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:18:53,597 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           1      bol                                      run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:18:53,597 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:53,597 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:18:53,597 - INFO - 
2025-09-24 16:18:53,597 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           2      bol                                      run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:18:53,597 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:18:53,597 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:18:53,597 - INFO - 
2025-09-24 16:18:53,597 - INFO - C3I3XLR18U7J6P1N2LZR.pdf                           1      bol                                      run1_C3I3XLR18U7J6P1N2LZR.json                                                  
2025-09-24 16:18:53,597 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:18:53,597 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:18:53,597 - INFO - 
2025-09-24 16:18:53,597 - INFO - C94JBR3RYYOOM5J2PFUM.pdf                           1      bol                                      run1_C94JBR3RYYOOM5J2PFUM.json                                                  
2025-09-24 16:18:53,597 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:18:53,597 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:18:53,598 - INFO - 
2025-09-24 16:18:53,598 - INFO - DY3D94HTH1ZH420GMDO6.pdf                           1      bol                                      run1_DY3D94HTH1ZH420GMDO6.json                                                  
2025-09-24 16:18:53,598 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:18:53,598 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:18:53,598 - INFO - 
2025-09-24 16:18:53,598 - INFO - G7K3JPMT2OXZWEI7RRNQ.jpg                           1      bol                                      run1_G7K3JPMT2OXZWEI7RRNQ.json                                                  
2025-09-24 16:18:53,598 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:18:53,598 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:18:53,598 - INFO - 
2025-09-24 16:18:53,598 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           1      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:18:53,598 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:53,598 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:18:53,598 - INFO - 
2025-09-24 16:18:53,598 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           2      other                                    run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:18:53,598 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:18:53,598 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:18:53,598 - INFO - 
2025-09-24 16:18:53,598 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           1      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:18:53,598 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:53,599 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:18:53,599 - INFO - 
2025-09-24 16:18:53,599 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           2      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:18:53,599 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:18:53,599 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:18:53,599 - INFO - 
2025-09-24 16:18:53,599 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           1      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:18:53,599 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:53,599 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:18:53,599 - INFO - 
2025-09-24 16:18:53,599 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           2      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:18:53,599 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:18:53,599 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:18:53,599 - INFO - 
2025-09-24 16:18:53,599 - INFO - ODBS2NBTU6S3GF9JVE6V.pdf                           1      tender_from_cust                         run1_ODBS2NBTU6S3GF9JVE6V.json                                                  
2025-09-24 16:18:53,599 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:18:53,599 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:18:53,599 - INFO - 
2025-09-24 16:18:53,599 - INFO - SMX8DOTQ89U191SMP0TO.pdf                           1      bol                                      run1_SMX8DOTQ89U191SMP0TO.json                                                  
2025-09-24 16:18:53,599 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:18:53,600 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:18:53,600 - INFO - 
2025-09-24 16:18:53,600 - INFO - Y7P2IPB0S079FZE11EEA.jpg                           1      bol                                      run1_Y7P2IPB0S079FZE11EEA.json                                                  
2025-09-24 16:18:53,600 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:18:53,600 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:18:53,600 - INFO - 
2025-09-24 16:18:53,600 - INFO - Z10BLMPIZ96XLUZ5NUPF.pdf                           1      bol                                      run1_Z10BLMPIZ96XLUZ5NUPF.json                                                  
2025-09-24 16:18:53,600 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:18:53,600 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:18:53,600 - INFO - 
2025-09-24 16:18:53,600 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:18:53,600 - INFO - Total entries: 16
2025-09-24 16:18:53,600 - INFO - ============================================================================================================================================
2025-09-24 16:18:53,600 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 16:18:53,600 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:18:53,600 - INFO -   1. AYEA5J1NILYPMWA7PN4V.pdf            Page 1   → bol             | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:18:53,600 - INFO -   2. AYEA5J1NILYPMWA7PN4V.pdf            Page 2   → bol             | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:18:53,600 - INFO -   3. C3I3XLR18U7J6P1N2LZR.pdf            Page 1   → bol             | run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:18:53,600 - INFO -   4. C94JBR3RYYOOM5J2PFUM.pdf            Page 1   → bol             | run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:18:53,600 - INFO -   5. DY3D94HTH1ZH420GMDO6.pdf            Page 1   → bol             | run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:18:53,600 - INFO -   6. G7K3JPMT2OXZWEI7RRNQ.jpg            Page 1   → bol             | run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:18:53,601 - INFO -   7. GJ7Z12W1O0CQO9E9H39H.pdf            Page 1   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:18:53,601 - INFO -   8. GJ7Z12W1O0CQO9E9H39H.pdf            Page 2   → other           | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:18:53,601 - INFO -   9. N9XYNNZR54XFORX6RGH2.pdf            Page 1   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:18:53,601 - INFO -  10. N9XYNNZR54XFORX6RGH2.pdf            Page 2   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:18:53,601 - INFO -  11. NFG5QQXEN21TEZZ9SYPW.pdf            Page 1   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:18:53,601 - INFO -  12. NFG5QQXEN21TEZZ9SYPW.pdf            Page 2   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:18:53,601 - INFO -  13. ODBS2NBTU6S3GF9JVE6V.pdf            Page 1   → tender_from_cust | run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:18:53,601 - INFO -  14. SMX8DOTQ89U191SMP0TO.pdf            Page 1   → bol             | run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:18:53,601 - INFO -  15. Y7P2IPB0S079FZE11EEA.jpg            Page 1   → bol             | run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:18:53,601 - INFO -  16. Z10BLMPIZ96XLUZ5NUPF.pdf            Page 1   → bol             | run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:18:53,601 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:18:53,602 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 28.912133, 'processed_files': [{'filename': 'AYEA5J1NILYPMWA7PN4V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf'}, {'filename': 'C3I3XLR18U7J6P1N2LZR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf'}, {'filename': 'C94JBR3RYYOOM5J2PFUM.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf'}, {'filename': 'DY3D94HTH1ZH420GMDO6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf'}, {'filename': 'G7K3JPMT2OXZWEI7RRNQ.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg'}, {'filename': 'GJ7Z12W1O0CQO9E9H39H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf'}, {'filename': 'N9XYNNZR54XFORX6RGH2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf'}, {'filename': 'NFG5QQXEN21TEZZ9SYPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf'}, {'filename': 'ODBS2NBTU6S3GF9JVE6V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf'}, {'filename': 'SMX8DOTQ89U191SMP0TO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf'}, {'filename': 'Y7P2IPB0S079FZE11EEA.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg'}, {'filename': 'Z10BLMPIZ96XLUZ5NUPF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf'}]}
