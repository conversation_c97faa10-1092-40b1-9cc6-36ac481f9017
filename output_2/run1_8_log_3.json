{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0022182585671544075, "Y": 0.0}, {"X": 1.0, "Y": 0.004608245566487312}, {"X": 0.9987070560455322, "Y": 1.0}, {"X": 0.0, "Y": 0.9995465874671936}]}, "Id": "708916b2-51f7-4e20-bc53-b9815aa97152", "Relationships": [{"Type": "CHILD", "Ids": ["a037050f-7972-46dd-a9d8-cabf06b8ede7", "65629b20-ccff-4308-bf4e-4c52e85fc9b2", "5169014d-3a5f-4964-ad2e-83cc7d6c978f", "20654690-761c-434d-93bb-020ef7a57cd4", "c186bcc9-511b-4ff9-a11c-b1d9ec1cdc5f", "b3bfa467-a129-41ea-b2e1-de26f9b46568", "e38172da-92ff-4cae-9b81-5f99ac5d659e", "0904cccb-d774-40db-a44c-65885b178438", "2baaed55-cef3-4e95-aec2-ab446528cb7a", "a22cbea4-c519-4eec-b20e-491df80369bb", "e3a75424-83cf-46ff-a288-3a42f7d539ed", "55501a98-2415-472a-bdba-13a0337b4294", "45033012-d9ea-4e1c-b2cb-b35c727ac9be", "3a9650cc-12b8-403d-9c72-04dc0067a50d", "8cfe2080-1435-46df-a053-d7c7c3228a42", "b5a1faa9-d2b7-4b90-8403-83fae563a018", "1d204079-124b-4faa-9f2a-440ac499fd17", "2f9bb0d2-077b-48da-95a4-58eaaae9d642", "59154b78-5960-4e21-8b62-b40b27e8323e", "9aba9de5-24b9-4963-9920-2b96ca81b088", "72860680-6d83-40de-ab40-0c084514fd33", "401598fa-6e03-46ad-89d6-ad4c09ed4a06", "c5768ca4-9cb2-4336-9bed-7019518d6e86", "25cba6f8-2c7a-46f7-859b-72e4b33<PERSON><PERSON>"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "DATE: 20-Feb", "Geometry": {"BoundingBox": {"Width": 0.1579749882221222, "Height": 0.03215382620692253, "Left": 0.21548213064670563, "Top": 0.0429195910692215}, "Polygon": [{"X": 0.21636752784252167, "Y": 0.0429195910692215}, {"X": 0.373457133769989, "Y": 0.04811938852071762}, {"X": 0.37257885932922363, "Y": 0.07507342100143433}, {"X": 0.21548213064670563, "Y": 0.06989724189043045}]}, "Id": "a037050f-7972-46dd-a9d8-cabf06b8ede7", "Relationships": [{"Type": "CHILD", "Ids": ["a328ae3c-a244-49a9-bafe-085fbacdbc19", "e0982660-28b4-4fb7-9a99-0f2ea9386f9b"]}]}, {"BlockType": "LINE", "Confidence": 99.74555969238281, "Text": "UNIT #: 92644/283420", "Geometry": {"BoundingBox": {"Width": 0.2517589330673218, "Height": 0.04012957215309143, "Left": 0.1949082463979721, "Top": 0.11189242452383041}, "Polygon": [{"X": 0.1959584802389145, "Y": 0.11189242452383041}, {"X": 0.4466671645641327, "Y": 0.12009291350841522}, {"X": 0.44563040137290955, "Y": 0.15202198922634125}, {"X": 0.1949082463979721, "Y": 0.1438661813735962}]}, "Id": "65629b20-ccff-4308-bf4e-4c52e85fc9b2", "Relationships": [{"Type": "CHILD", "Ids": ["edf7ed7b-c74f-4e40-aacd-94d3bb02f92d", "ff0d443a-6ed1-4955-b01a-1ad4b7d8aeed", "574d9b52-9615-47a5-9eac-70acbc82d97d"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Truck Ready Time: 5:00", "Geometry": {"BoundingBox": {"Width": 0.3100365698337555, "Height": 0.049046456813812256, "Left": 0.47112029790878296, "Top": 0.14964473247528076}, "Polygon": [{"X": 0.4723871946334839, "Y": 0.14964473247528076}, {"X": 0.7811568379402161, "Y": 0.15969444811344147}, {"X": 0.779910147190094, "Y": 0.19869118928909302}, {"X": 0.47112029790878296, "Y": 0.1887088268995285}]}, "Id": "5169014d-3a5f-4964-ad2e-83cc7d6c978f", "Relationships": [{"Type": "CHILD", "Ids": ["f18c82f0-3e2d-4379-8654-a758291858c5", "831c5343-e27e-4d47-8697-ce60d67bfe2c", "1d56db13-4125-4fc8-bd31-50232305eff5", "c2065e67-0e1d-4eb1-bb2e-6d5dbc2dcf9d"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.18603163957595825, "Height": 0.03132793307304382, "Left": 0.18472658097743988, "Top": 0.18267029523849487}, "Polygon": [{"X": 0.1855602264404297, "Y": 0.18267029523849487}, {"X": 0.37075820565223694, "Y": 0.18865393102169037}, {"X": 0.36993247270584106, "Y": 0.2139982283115387}, {"X": 0.18472658097743988, "Y": 0.2080407738685608}]}, "Id": "20654690-761c-434d-93bb-020ef7a57cd4", "Relationships": [{"Type": "CHILD", "Ids": ["b022cf17-c76e-43fb-a031-0451a78ecab1", "7348a477-e689-44da-b824-9890cb681f2a"]}]}, {"BlockType": "LINE", "Confidence": 98.97720336914062, "Text": "Truck Departure: 5:00", "Geometry": {"BoundingBox": {"Width": 0.31131747364997864, "Height": 0.05023849755525589, "Left": 0.4700527489185333, "Top": 0.2184925228357315}, "Polygon": [{"X": 0.4713609218597412, "Y": 0.2184925228357315}, {"X": 0.781370222568512, "Y": 0.2284623384475708}, {"X": 0.7800830006599426, "Y": 0.268731027841568}, {"X": 0.4700527489185333, "Y": 0.25883105397224426}]}, "Id": "c186bcc9-511b-4ff9-a11c-b1d9ec1cdc5f", "Relationships": [{"Type": "CHILD", "Ids": ["c5c0348e-8a6b-452f-a2ec-63a1ed463cd8", "5b15e914-499d-4913-ad92-ba292b5680ae", "933e8c44-ab1b-423d-bea8-edfaec4460da"]}]}, {"BlockType": "LINE", "Confidence": 99.78707885742188, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.2669418156147003, "Height": 0.04037947952747345, "Left": 0.07733608037233353, "Top": 0.24358193576335907}, "Polygon": [{"X": 0.07839035242795944, "Y": 0.24358193576335907}, {"X": 0.34427788853645325, "Y": 0.2520764470100403}, {"X": 0.34323784708976746, "Y": 0.2839614152908325}, {"X": 0.07733608037233353, "Y": 0.2755142152309418}]}, "Id": "b3bfa467-a129-41ea-b2e1-de26f9b46568", "Relationships": [{"Type": "CHILD", "Ids": ["a792a7d4-df98-400a-b26c-57a91b7d6f56", "10b4d158-b569-4e63-9550-813503408c59", "da3199e3-f61b-4ad4-a81c-cc308881f3e5"]}]}, {"BlockType": "LINE", "Confidence": 99.68112182617188, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.05514424294233322, "Height": 0.022276688367128372, "Left": 0.31274813413619995, "Top": 0.31856173276901245}, "Polygon": [{"X": 0.31341975927352905, "Y": 0.31856173276901245}, {"X": 0.36789238452911377, "Y": 0.32028132677078247}, {"X": 0.3672226369380951, "Y": 0.3408384323120117}, {"X": 0.31274813413619995, "Y": 0.3391250669956207}]}, "Id": "e38172da-92ff-4cae-9b81-5f99ac5d659e", "Relationships": [{"Type": "CHILD", "Ids": ["9c74aaae-e808-4c86-851a-6fb1088b5076"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.11779531836509705, "Height": 0.03273661062121391, "Left": 0.1132243275642395, "Top": 0.3450755476951599}, "Polygon": [{"X": 0.11418301612138748, "Y": 0.3450755476951599}, {"X": 0.23101964592933655, "Y": 0.3487423360347748}, {"X": 0.23006665706634521, "Y": 0.3778121769428253}, {"X": 0.1132243275642395, "Y": 0.37416431307792664}]}, "Id": "0904cccb-d774-40db-a44c-65885b178438", "Relationships": [{"Type": "CHILD", "Ids": ["c5ec6d8a-8cb7-4c2a-b9b2-f984cb3cb159"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.027638239786028862, "Height": 0.024810055270791054, "Left": 0.32599076628685, "Top": 0.35430312156677246}, "Polygon": [{"X": 0.32677316665649414, "Y": 0.35430312156677246}, {"X": 0.3536290228366852, "Y": 0.35514557361602783}, {"X": 0.352847695350647, "Y": 0.37911316752433777}, {"X": 0.32599076628685, "Y": 0.3782743215560913}]}, "Id": "2baaed55-cef3-4e95-aec2-ab446528cb7a", "Relationships": [{"Type": "CHILD", "Ids": ["840ad88f-f625-4ed0-b2d9-0a781b7bb3ea"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.03969407081604004, "Height": 0.02513890154659748, "Left": 0.40297356247901917, "Top": 0.35680967569351196}, "Polygon": [{"X": 0.4037516415119171, "Y": 0.35680967569351196}, {"X": 0.4426676332950592, "Y": 0.35803043842315674}, {"X": 0.4418911039829254, "Y": 0.3819485902786255}, {"X": 0.40297356247901917, "Y": 0.38073301315307617}]}, "Id": "a22cbea4-c519-4eec-b20e-491df80369bb", "Relationships": [{"Type": "CHILD", "Ids": ["3ae4e78e-4fe1-4071-a2cd-f8bb1e65c301"]}]}, {"BlockType": "LINE", "Confidence": 99.94791412353516, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.1641554832458496, "Height": 0.029660876840353012, "Left": 0.5835081338882446, "Top": 0.36434274911880493}, "Polygon": [{"X": 0.5843002796173096, "Y": 0.36434274911880493}, {"X": 0.7476636171340942, "Y": 0.36946555972099304}, {"X": 0.7468781471252441, "Y": 0.39400362968444824}, {"X": 0.5835081338882446, "Y": 0.3889032304286957}]}, "Id": "e3a75424-83cf-46ff-a288-3a42f7d539ed", "Relationships": [{"Type": "CHILD", "Ids": ["5a809d4c-ebb0-4fa5-8dc6-20468c9afbd0", "7b4e60be-2962-452f-9aba-3bb3db44f0bb", "7edaa82a-cb21-491f-b6ec-5458c3fc506c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.10653107613325119, "Height": 0.026315394788980484, "Left": 0.8774412870407104, "Top": 0.3616449534893036}, "Polygon": [{"X": 0.8781731128692627, "Y": 0.3616449534893036}, {"X": 0.9839723110198975, "Y": 0.36496976017951965}, {"X": 0.9832445383071899, "Y": 0.3879603445529938}, {"X": 0.8774412870407104, "Y": 0.3846491873264313}]}, "Id": "55501a98-2415-472a-bdba-13a0337b4294", "Relationships": [{"Type": "CHILD", "Ids": ["4cd2fe19-97a7-4884-bb42-20b0ef6d18c1", "78ff73dc-9752-4193-bfdd-755f639e6fee"]}]}, {"BlockType": "LINE", "Confidence": 84.02682495117188, "Text": "Isk", "Geometry": {"BoundingBox": {"Width": 0.05760449171066284, "Height": 0.05161376670002937, "Left": 0.2268626093864441, "Top": 0.4105585515499115}, "Polygon": [{"X": 0.22849825024604797, "Y": 0.4105585515499115}, {"X": 0.28446710109710693, "Y": 0.4122955799102783}, {"X": 0.2828361392021179, "Y": 0.4621722996234894}, {"X": 0.2268626093864441, "Y": 0.4604508578777313}]}, "Id": "45033012-d9ea-4e1c-b2cb-b35c727ac9be", "Relationships": [{"Type": "CHILD", "Ids": ["dd2c1e3e-8a29-4b23-89ae-5ed7c534e022"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1040", "Geometry": {"BoundingBox": {"Width": 0.06970304250717163, "Height": 0.03342719376087189, "Left": 0.8907788395881653, "Top": 0.42256876826286316}, "Polygon": [{"X": 0.89177405834198, "Y": 0.42256876826286316}, {"X": 0.9604818820953369, "Y": 0.42470449209213257}, {"X": 0.9594902992248535, "Y": 0.45599597692489624}, {"X": 0.8907788395881653, "Y": 0.45387229323387146}]}, "Id": "3a9650cc-12b8-403d-9c72-04dc0067a50d", "Relationships": [{"Type": "CHILD", "Ids": ["23355d8b-2573-4a87-b6e9-a6111794c59f"]}]}, {"BlockType": "LINE", "Confidence": 59.43339538574219, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.05564732104539871, "Height": 0.046167951077222824, "Left": 0.4481831192970276, "Top": 0.42416319251060486}, "Polygon": [{"X": 0.4496272802352905, "Y": 0.42416319251060486}, {"X": 0.5038304328918457, "Y": 0.42584335803985596}, {"X": 0.5023903250694275, "Y": 0.4703311324119568}, {"X": 0.4481831192970276, "Y": 0.4686644375324249}]}, "Id": "8cfe2080-1435-46df-a053-d7c7c3228a42", "Relationships": [{"Type": "CHILD", "Ids": ["ee7b0801-848a-400e-bc65-de0eb75d9177"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Parkn Manufacturing", "Geometry": {"BoundingBox": {"Width": 0.20832109451293945, "Height": 0.040868084877729416, "Left": 0.04540211334824562, "Top": 0.45253515243530273}, "Polygon": [{"X": 0.04654354974627495, "Y": 0.45253515243530273}, {"X": 0.2537232041358948, "Y": 0.45890969038009644}, {"X": 0.2525937855243683, "Y": 0.49340322613716125}, {"X": 0.04540211334824562, "Y": 0.4870685338973999}]}, "Id": "b5a1faa9-d2b7-4b90-8403-83fae563a018", "Relationships": [{"Type": "CHILD", "Ids": ["50da30c1-e368-4ea7-b980-b4ce9163a281", "c9c80267-ab01-45be-b299-f82496619c8d"]}]}, {"BlockType": "LINE", "Confidence": 91.66035461425781, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01628855988383293, "Height": 0.025888467207551003, "Left": 0.3308030962944031, "Top": 0.4660031199455261}, "Polygon": [{"X": 0.3316322863101959, "Y": 0.4660031199455261}, {"X": 0.3470916450023651, "Y": 0.4664783775806427}, {"X": 0.3462630808353424, "Y": 0.4918915927410126}, {"X": 0.3308030962944031, "Y": 0.4914185404777527}]}, "Id": "1d204079-124b-4faa-9f2a-440ac499fd17", "Relationships": [{"Type": "CHILD", "Ids": ["6ddd4387-859e-4abc-9f76-bd8ace62e950"]}]}, {"BlockType": "LINE", "Confidence": 89.10963439941406, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.16032011806964874, "Height": 0.029697904363274574, "Left": 0.4126451015472412, "Top": 0.46869713068008423}, "Polygon": [{"X": 0.41345176100730896, "Y": 0.46869713068008423}, {"X": 0.5729652643203735, "Y": 0.47360068559646606}, {"X": 0.5721652507781982, "Y": 0.49839502573013306}, {"X": 0.4126451015472412, "Y": 0.4935135543346405}]}, "Id": "2f9bb0d2-077b-48da-95a4-58eaaae9d642", "Relationships": [{"Type": "CHILD", "Ids": ["0c032f38-5773-4caa-bb12-202f67e8e0d9", "31f00516-f429-4130-9233-bb401585e8b7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1055", "Geometry": {"BoundingBox": {"Width": 0.07424264401197433, "Height": 0.04109891876578331, "Left": 0.8813396692276001, "Top": 0.510494589805603}, "Polygon": [{"X": 0.8825761675834656, "Y": 0.510494589805603}, {"X": 0.955582320690155, "Y": 0.5127276182174683}, {"X": 0.9543505907058716, "Y": 0.551593542098999}, {"X": 0.8813396692276001, "Y": 0.5493764281272888}]}, "Id": "59154b78-5960-4e21-8b62-b40b27e8323e", "Relationships": [{"Type": "CHILD", "Ids": ["14e1e05a-4928-4aec-93c2-afe883b9dad5"]}]}, {"BlockType": "LINE", "Confidence": 71.42219543457031, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.06532468646764755, "Height": 0.047232694923877716, "Left": 0.4515901505947113, "Top": 0.620549201965332}, "Polygon": [{"X": 0.4530608654022217, "Y": 0.620549201965332}, {"X": 0.5169148445129395, "Y": 0.6224579811096191}, {"X": 0.5154489874839783, "Y": 0.6677818894386292}, {"X": 0.4515901505947113, "Y": 0.665889322757721}]}, "Id": "9aba9de5-24b9-4963-9920-2b96ca81b088", "Relationships": [{"Type": "CHILD", "Ids": ["6f34ce3f-c372-45db-8467-d3d12320a1c3"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "0830", "Geometry": {"BoundingBox": {"Width": 0.07970772683620453, "Height": 0.037620823830366135, "Left": 0.8743544220924377, "Top": 0.6406471729278564}, "Polygon": [{"X": 0.8754768371582031, "Y": 0.6406471729278564}, {"X": 0.9540621042251587, "Y": 0.6429930329322815}, {"X": 0.9529443383216858, "Y": 0.6782680153846741}, {"X": 0.8743544220924377, "Y": 0.6759377717971802}]}, "Id": "72860680-6d83-40de-ab40-0c084514fd33", "Relationships": [{"Type": "CHILD", "Ids": ["05969997-0a35-4bd8-a1c8-268c302b165f"]}]}, {"BlockType": "LINE", "Confidence": 96.56635284423828, "Text": "Alumalloy - Avon Lake", "Geometry": {"BoundingBox": {"Width": 0.22078944742679596, "Height": 0.03716304898262024, "Left": 0.045977212488651276, "Top": 0.658808708190918}, "Polygon": [{"X": 0.04699138551950455, "Y": 0.658808708190918}, {"X": 0.26676666736602783, "Y": 0.6653161644935608}, {"X": 0.2657638192176819, "Y": 0.6959717869758606}, {"X": 0.045977212488651276, "Y": 0.689501941204071}]}, "Id": "401598fa-6e03-46ad-89d6-ad4c09ed4a06", "Relationships": [{"Type": "CHILD", "Ids": ["f6856d5c-1f01-4726-8569-30e27df39481", "b3d75bae-3901-4112-92f1-6f63e0ecfe3f", "9b21ba50-9f87-4773-8774-6661c964ac83"]}]}, {"BlockType": "LINE", "Confidence": 91.06285858154297, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15923504531383514, "Height": 0.029367107897996902, "Left": 0.4088922441005707, "Top": 0.6709004044532776}, "Polygon": [{"X": 0.40969493985176086, "Y": 0.6709004044532776}, {"X": 0.5681272745132446, "Y": 0.6755902767181396}, {"X": 0.5673311352729797, "Y": 0.700267493724823}, {"X": 0.4088922441005707, "Y": 0.6955994367599487}]}, "Id": "c5768ca4-9cb2-4336-9bed-7019518d6e86", "Relationships": [{"Type": "CHILD", "Ids": ["27a36624-529f-4300-a373-e44145569281", "30796841-53a2-4f09-8367-c1d195465800"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "0840", "Geometry": {"BoundingBox": {"Width": 0.08023902028799057, "Height": 0.03638775274157524, "Left": 0.8741713166236877, "Top": 0.7459439039230347}, "Polygon": [{"X": 0.875255286693573, "Y": 0.7459439039230347}, {"X": 0.9544103145599365, "Y": 0.7482596635818481}, {"X": 0.9533308744430542, "Y": 0.782331645488739}, {"X": 0.8741713166236877, "Y": 0.7800310254096985}]}, "Id": "25cba6f8-2c7a-46f7-859b-72e4b33<PERSON><PERSON>", "Relationships": [{"Type": "CHILD", "Ids": ["706216fc-b479-4ee1-a0b0-443dbbc8423a"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06366045027971268, "Height": 0.02693450078368187, "Left": 0.21555152535438538, "Top": 0.0429195910692215}, "Polygon": [{"X": 0.21636752784252167, "Y": 0.0429195910692215}, {"X": 0.27921199798583984, "Y": 0.04499979689717293}, {"X": 0.2783986032009125, "Y": 0.06985409557819366}, {"X": 0.21555152535438538, "Y": 0.06778259575366974}]}, "Id": "a328ae3c-a244-49a9-bafe-085fbacdbc19"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "20-Feb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06987423449754715, "Height": 0.028067966923117638, "Left": 0.3035447597503662, "Top": 0.04700545221567154}, "Polygon": [{"X": 0.3043878674507141, "Y": 0.04700545221567154}, {"X": 0.37341898679733276, "Y": 0.04928998276591301}, {"X": 0.37257885932922363, "Y": 0.07507342100143433}, {"X": 0.3035447597503662, "Y": 0.07279881834983826}]}, "Id": "e0982660-28b4-4fb7-9a99-0f2ea9386f9b"}, {"BlockType": "WORD", "Confidence": 99.60857391357422, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05581699684262276, "Height": 0.026889165863394737, "Left": 0.19513103365898132, "Top": 0.11198543757200241}, "Polygon": [{"X": 0.19595542550086975, "Y": 0.11198543757200241}, {"X": 0.250948041677475, "Y": 0.11378417909145355}, {"X": 0.2501259744167328, "Y": 0.1388746052980423}, {"X": 0.19513103365898132, "Y": 0.13708356022834778}]}, "Id": "edf7ed7b-c74f-4e40-aacd-94d3bb02f92d"}, {"BlockType": "WORD", "Confidence": 99.63787078857422, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02441558614373207, "Height": 0.025526700541377068, "Left": 0.2539274990558624, "Top": 0.1138150691986084}, "Polygon": [{"X": 0.25473853945732117, "Y": 0.1138150691986084}, {"X": 0.2783430814743042, "Y": 0.11458715796470642}, {"X": 0.27753302454948425, "Y": 0.13934177160263062}, {"X": 0.2539274990558624, "Y": 0.13857294619083405}]}, "Id": "ff0d443a-6ed1-4955-b01a-1ad4b7d8aeed"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "92644/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1445275843143463, "Height": 0.035232093185186386, "Left": 0.302094429731369, "Top": 0.11678989976644516}, "Polygon": [{"X": 0.30309343338012695, "Y": 0.11678989976644516}, {"X": 0.44662201404571533, "Y": 0.12148348987102509}, {"X": 0.44563040137290955, "Y": 0.15202198922634125}, {"X": 0.302094429731369, "Y": 0.14735287427902222}]}, "Id": "574d9b52-9615-47a5-9eac-70acbc82d97d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056382160633802414, "Height": 0.026549579575657845, "Left": 0.4713059961795807, "Top": 0.1582314521074295}, "Polygon": [{"X": 0.47210872173309326, "Y": 0.1582314521074295}, {"X": 0.5276881456375122, "Y": 0.16003774106502533}, {"X": 0.5268877744674683, "Y": 0.1847810298204422}, {"X": 0.4713059961795807, "Y": 0.1829824298620224}]}, "Id": "f18c82f0-3e2d-4379-8654-a758291858c5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06513089686632156, "Height": 0.0327693447470665, "Left": 0.530707061290741, "Top": 0.15993928909301758}, "Polygon": [{"X": 0.5316998362541199, "Y": 0.15993928909301758}, {"X": 0.5958380103111267, "Y": 0.16202381253242493}, {"X": 0.5948485136032104, "Y": 0.19270864129066467}, {"X": 0.530707061290741, "Y": 0.19063511490821838}]}, "Id": "831c5343-e27e-4d47-8697-ce60d67bfe2c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05584581568837166, "Height": 0.026303930208086967, "Left": 0.6008630990982056, "Top": 0.1634168177843094}, "Polygon": [{"X": 0.6016536951065063, "Y": 0.1634168177843094}, {"X": 0.6567089557647705, "Y": 0.16520576179027557}, {"X": 0.655920684337616, "Y": 0.1897207498550415}, {"X": 0.6008630990982056, "Y": 0.1879393458366394}]}, "Id": "1d56db13-4125-4fc8-bd31-50232305eff5"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0994606614112854, "Height": 0.03780429810285568, "Left": 0.6816961765289307, "Top": 0.1564934253692627}, "Polygon": [{"X": 0.6828081607818604, "Y": 0.1564934253692627}, {"X": 0.7811568379402161, "Y": 0.15969444811344147}, {"X": 0.7800506353378296, "Y": 0.19429773092269897}, {"X": 0.6816961765289307, "Y": 0.19111575186252594}]}, "Id": "c2065e67-0e1d-4eb1-bb2e-6d5dbc2dcf9d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08547724783420563, "Height": 0.02809329703450203, "Left": 0.18472658097743988, "Top": 0.18267029523849487}, "Polygon": [{"X": 0.1855602264404297, "Y": 0.18267029523849487}, {"X": 0.2702038288116455, "Y": 0.18540507555007935}, {"X": 0.2693738043308258, "Y": 0.2107635885477066}, {"X": 0.18472658097743988, "Y": 0.2080407738685608}]}, "Id": "b022cf17-c76e-43fb-a031-0451a78ecab1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06880593299865723, "Height": 0.027407344430685043, "Left": 0.3019501566886902, "Top": 0.18652324378490448}, "Polygon": [{"X": 0.30277442932128906, "Y": 0.18652324378490448}, {"X": 0.3707560896873474, "Y": 0.18871967494487762}, {"X": 0.36993467807769775, "Y": 0.21393059194087982}, {"X": 0.3019501566886902, "Y": 0.21174374222755432}]}, "Id": "7348a477-e689-44da-b824-9890cb681f2a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055950768291950226, "Height": 0.026835797354578972, "Left": 0.47026529908180237, "Top": 0.2272041141986847}, "Polygon": [{"X": 0.47107839584350586, "Y": 0.2272041141986847}, {"X": 0.5262160301208496, "Y": 0.2289746105670929}, {"X": 0.5254052877426147, "Y": 0.2540399134159088}, {"X": 0.47026529908180237, "Y": 0.252277135848999}]}, "Id": "c5c0348e-8a6b-452f-a2ec-63a1ed463cd8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10819485038518906, "Height": 0.03401782736182213, "Left": 0.5298004746437073, "Top": 0.23014459013938904}, "Polygon": [{"X": 0.5307899117469788, "Y": 0.23014459013938904}, {"X": 0.6379953026771545, "Y": 0.2335864007472992}, {"X": 0.6370113492012024, "Y": 0.26416242122650146}, {"X": 0.5298004746437073, "Y": 0.26073890924453735}]}, "Id": "5b15e914-499d-4913-ad92-ba292b5680ae"}, {"BlockType": "WORD", "Confidence": 96.9714584350586, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10368028283119202, "Height": 0.03505605086684227, "Left": 0.6776899695396423, "Top": 0.22516083717346191}, "Polygon": [{"X": 0.6787104606628418, "Y": 0.22516083717346191}, {"X": 0.781370222568512, "Y": 0.2284623384475708}, {"X": 0.7803551554679871, "Y": 0.2602168917655945}, {"X": 0.6776899695396423, "Y": 0.2569335997104645}]}, "Id": "933e8c44-ab1b-423d-bea8-edfaec4460da"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13017912209033966, "Height": 0.03603455424308777, "Left": 0.07733608037233353, "Top": 0.24358193576335907}, "Polygon": [{"X": 0.07839035242795944, "Y": 0.24358193576335907}, {"X": 0.20751520991325378, "Y": 0.24770718812942505}, {"X": 0.20646785199642181, "Y": 0.27961650490760803}, {"X": 0.07733608037233353, "Y": 0.2755142152309418}]}, "Id": "a792a7d4-df98-400a-b26c-57a91b7d6f56"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05738750845193863, "Height": 0.02622099779546261, "Left": 0.21226535737514496, "Top": 0.2549988925457001}, "Polygon": [{"X": 0.21306678652763367, "Y": 0.2549988925457001}, {"X": 0.2696528732776642, "Y": 0.2568044364452362}, {"X": 0.2688537836074829, "Y": 0.2812198996543884}, {"X": 0.21226535737514496, "Y": 0.2794220745563507}]}, "Id": "10b4d158-b569-4e63-9550-813503408c59"}, {"BlockType": "WORD", "Confidence": 99.44097137451172, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042970240116119385, "Height": 0.026401376351714134, "Left": 0.3011181056499481, "Top": 0.25654202699661255}, "Polygon": [{"X": 0.30193716287612915, "Y": 0.25654202699661255}, {"X": 0.3440883457660675, "Y": 0.2578873038291931}, {"X": 0.34327107667922974, "Y": 0.2829434275627136}, {"X": 0.3011181056499481, "Y": 0.2816040515899658}]}, "Id": "da3199e3-f61b-4ad4-a81c-cc308881f3e5"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05514424294233322, "Height": 0.022276688367128372, "Left": 0.31274813413619995, "Top": 0.31856173276901245}, "Polygon": [{"X": 0.31341975927352905, "Y": 0.31856173276901245}, {"X": 0.36789238452911377, "Y": 0.32028132677078247}, {"X": 0.3672226369380951, "Y": 0.3408384323120117}, {"X": 0.31274813413619995, "Y": 0.3391250669956207}]}, "Id": "9c74aaae-e808-4c86-851a-6fb1088b5076"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11779531836509705, "Height": 0.03273661062121391, "Left": 0.1132243275642395, "Top": 0.3450755476951599}, "Polygon": [{"X": 0.11418301612138748, "Y": 0.3450755476951599}, {"X": 0.23101964592933655, "Y": 0.3487423360347748}, {"X": 0.23006665706634521, "Y": 0.3778121769428253}, {"X": 0.1132243275642395, "Y": 0.37416431307792664}]}, "Id": "c5ec6d8a-8cb7-4c2a-b9b2-f984cb3cb159"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027638239786028862, "Height": 0.024810055270791054, "Left": 0.32599076628685, "Top": 0.35430312156677246}, "Polygon": [{"X": 0.32677316665649414, "Y": 0.35430312156677246}, {"X": 0.3536290228366852, "Y": 0.35514557361602783}, {"X": 0.352847695350647, "Y": 0.37911316752433777}, {"X": 0.32599076628685, "Y": 0.3782743215560913}]}, "Id": "840ad88f-f625-4ed0-b2d9-0a781b7bb3ea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03969407081604004, "Height": 0.02513890154659748, "Left": 0.40297356247901917, "Top": 0.35680967569351196}, "Polygon": [{"X": 0.4037516415119171, "Y": 0.35680967569351196}, {"X": 0.4426676332950592, "Y": 0.35803043842315674}, {"X": 0.4418911039829254, "Y": 0.3819485902786255}, {"X": 0.40297356247901917, "Y": 0.38073301315307617}]}, "Id": "3ae4e78e-4fe1-4071-a2cd-f8bb1e65c301"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06476809084415436, "Height": 0.026079218834638596, "Left": 0.5835183262825012, "Top": 0.3645056188106537}, "Polygon": [{"X": 0.5842950344085693, "Y": 0.3645056188106537}, {"X": 0.6482864022254944, "Y": 0.3665122389793396}, {"X": 0.6475123167037964, "Y": 0.3905848562717438}, {"X": 0.5835183262825012, "Y": 0.3885868489742279}]}, "Id": "5a809d4c-ebb0-4fa5-8dc6-20468c9afbd0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07329865545034409, "Height": 0.026687582954764366, "Left": 0.6532099843025208, "Top": 0.3666556775569916}, "Polygon": [{"X": 0.653995156288147, "Y": 0.3666556775569916}, {"X": 0.726508617401123, "Y": 0.36892953515052795}, {"X": 0.7257263660430908, "Y": 0.3933432698249817}, {"X": 0.6532099843025208, "Y": 0.391079306602478}]}, "Id": "7b4e60be-2962-452f-9aba-3bb3db44f0bb"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014958512037992477, "Height": 0.024634690955281258, "Left": 0.7327051162719727, "Top": 0.3690207600593567}, "Polygon": [{"X": 0.7334799766540527, "Y": 0.3690207600593567}, {"X": 0.7476636171340942, "Y": 0.36946555972099304}, {"X": 0.7468892931938171, "Y": 0.3936554491519928}, {"X": 0.7327051162719727, "Y": 0.39321261644363403}]}, "Id": "7edaa82a-cb21-491f-b6ec-5458c3fc506c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05118170753121376, "Height": 0.0243005882948637, "Left": 0.8774502873420715, "Top": 0.3616449534893036}, "Polygon": [{"X": 0.8781731128692627, "Y": 0.3616449534893036}, {"X": 0.9286319613456726, "Y": 0.3632306754589081}, {"X": 0.9279110431671143, "Y": 0.38594555854797363}, {"X": 0.8774502873420715, "Y": 0.3843662738800049}]}, "Id": "4cd2fe19-97a7-4884-bb42-20b0ef6d18c1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05099555850028992, "Height": 0.023978594690561295, "Left": 0.9329580664634705, "Top": 0.3639817535877228}, "Polygon": [{"X": 0.9336689710617065, "Y": 0.3639817535877228}, {"X": 0.983953595161438, "Y": 0.3655618131160736}, {"X": 0.9832445383071899, "Y": 0.3879603445529938}, {"X": 0.9329580664634705, "Y": 0.38638660311698914}]}, "Id": "78ff73dc-9752-4193-bfdd-755f639e6fee"}, {"BlockType": "WORD", "Confidence": 84.02682495117188, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05760449171066284, "Height": 0.05161376670002937, "Left": 0.2268626093864441, "Top": 0.4105585515499115}, "Polygon": [{"X": 0.22849825024604797, "Y": 0.4105585515499115}, {"X": 0.28446710109710693, "Y": 0.4122955799102783}, {"X": 0.2828361392021179, "Y": 0.4621722996234894}, {"X": 0.2268626093864441, "Y": 0.4604508578777313}]}, "Id": "dd2c1e3e-8a29-4b23-89ae-5ed7c534e022"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1040", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06970304250717163, "Height": 0.03342719376087189, "Left": 0.8907788395881653, "Top": 0.42256876826286316}, "Polygon": [{"X": 0.89177405834198, "Y": 0.42256876826286316}, {"X": 0.9604818820953369, "Y": 0.42470449209213257}, {"X": 0.9594902992248535, "Y": 0.45599597692489624}, {"X": 0.8907788395881653, "Y": 0.45387229323387146}]}, "Id": "23355d8b-2573-4a87-b6e9-a6111794c59f"}, {"BlockType": "WORD", "Confidence": 59.43339538574219, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05564732104539871, "Height": 0.046167951077222824, "Left": 0.4481831192970276, "Top": 0.42416319251060486}, "Polygon": [{"X": 0.4496272802352905, "Y": 0.42416319251060486}, {"X": 0.5038304328918457, "Y": 0.42584335803985596}, {"X": 0.5023903250694275, "Y": 0.4703311324119568}, {"X": 0.4481831192970276, "Y": 0.4686644375324249}]}, "Id": "ee7b0801-848a-400e-bc65-de0eb75d9177"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Parkn", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05719675123691559, "Height": 0.027103153988718987, "Left": 0.04570475593209267, "Top": 0.45253515243530273}, "Polygon": [{"X": 0.04654354974627495, "Y": 0.45253515243530273}, {"X": 0.10290151089429855, "Y": 0.4542691707611084}, {"X": 0.10206511616706848, "Y": 0.47963830828666687}, {"X": 0.04570475593209267, "Y": 0.47791221737861633}]}, "Id": "50da30c1-e368-4ea7-b980-b4ce9163a281"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Manufacturing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14374566078186035, "Height": 0.03725487366318703, "Left": 0.10992428660392761, "Top": 0.4561483561992645}, "Polygon": [{"X": 0.11100832372903824, "Y": 0.4561483561992645}, {"X": 0.25366994738578796, "Y": 0.46053647994995117}, {"X": 0.2525937855243683, "Y": 0.49340322613716125}, {"X": 0.10992428660392761, "Y": 0.4890412390232086}]}, "Id": "c9c80267-ab01-45be-b299-f82496619c8d"}, {"BlockType": "WORD", "Confidence": 91.66035461425781, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01628855988383293, "Height": 0.025888467207551003, "Left": 0.3308030962944031, "Top": 0.4660031199455261}, "Polygon": [{"X": 0.3316322863101959, "Y": 0.4660031199455261}, {"X": 0.3470916450023651, "Y": 0.4664783775806427}, {"X": 0.3462630808353424, "Y": 0.4918915927410126}, {"X": 0.3308030962944031, "Y": 0.4914185404777527}]}, "Id": "6ddd4387-859e-4abc-9f76-bd8ace62e950"}, {"BlockType": "WORD", "Confidence": 79.57369995117188, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015035339631140232, "Height": 0.02525186352431774, "Left": 0.4126451015472412, "Top": 0.46869713068008423}, "Polygon": [{"X": 0.41345176100730896, "Y": 0.46869713068008423}, {"X": 0.42768046259880066, "Y": 0.46913453936576843}, {"X": 0.42687439918518066, "Y": 0.4939489960670471}, {"X": 0.4126451015472412, "Y": 0.4935135543346405}]}, "Id": "0c032f38-5773-4caa-bb12-202f67e8e0d9"}, {"BlockType": "WORD", "Confidence": 98.64556884765625, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1111287996172905, "Height": 0.024160338565707207, "Left": 0.46177488565444946, "Top": 0.47211411595344543}, "Polygon": [{"X": 0.46244874596595764, "Y": 0.47211411595344543}, {"X": 0.5729036927223206, "Y": 0.4755083918571472}, {"X": 0.5722336769104004, "Y": 0.4962744414806366}, {"X": 0.46177488565444946, "Y": 0.4928929805755615}]}, "Id": "31f00516-f429-4130-9233-bb401585e8b7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1055", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07424264401197433, "Height": 0.04109891876578331, "Left": 0.8813396692276001, "Top": 0.510494589805603}, "Polygon": [{"X": 0.8825761675834656, "Y": 0.510494589805603}, {"X": 0.955582320690155, "Y": 0.5127276182174683}, {"X": 0.9543505907058716, "Y": 0.551593542098999}, {"X": 0.8813396692276001, "Y": 0.5493764281272888}]}, "Id": "14e1e05a-4928-4aec-93c2-afe883b9dad5"}, {"BlockType": "WORD", "Confidence": 71.42219543457031, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06532468646764755, "Height": 0.047232694923877716, "Left": 0.4515901505947113, "Top": 0.620549201965332}, "Polygon": [{"X": 0.4530608654022217, "Y": 0.620549201965332}, {"X": 0.5169148445129395, "Y": 0.6224579811096191}, {"X": 0.5154489874839783, "Y": 0.6677818894386292}, {"X": 0.4515901505947113, "Y": 0.665889322757721}]}, "Id": "6f34ce3f-c372-45db-8467-d3d12320a1c3"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "0830", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07970772683620453, "Height": 0.037620823830366135, "Left": 0.8743544220924377, "Top": 0.6406471729278564}, "Polygon": [{"X": 0.8754768371582031, "Y": 0.6406471729278564}, {"X": 0.9540621042251587, "Y": 0.6429930329322815}, {"X": 0.9529443383216858, "Y": 0.6782680153846741}, {"X": 0.8743544220924377, "Y": 0.6759377717971802}]}, "Id": "05969997-0a35-4bd8-a1c8-268c302b165f"}, {"BlockType": "WORD", "Confidence": 89.88619995117188, "Text": "Alumalloy -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1093287542462349, "Height": 0.033881813287734985, "Left": 0.045977212488651276, "Top": 0.658808708190918}, "Polygon": [{"X": 0.04699138551950455, "Y": 0.658808708190918}, {"X": 0.15530596673488617, "Y": 0.6620158553123474}, {"X": 0.1542973667383194, "Y": 0.6926905512809753}, {"X": 0.045977212488651276, "Y": 0.689501941204071}]}, "Id": "f6856d5c-1f01-4726-8569-30e27df39481"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "Avon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05033715069293976, "Height": 0.025274360552430153, "Left": 0.16340602934360504, "Top": 0.6641214489936829}, "Polygon": [{"X": 0.16418872773647308, "Y": 0.6641214489936829}, {"X": 0.2137431800365448, "Y": 0.6655882596969604}, {"X": 0.21296246349811554, "Y": 0.6893958449363708}, {"X": 0.16340602934360504, "Y": 0.6879356503486633}]}, "Id": "b3d75bae-3901-4112-92f1-6f63e0ecfe3f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Lake", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04594001919031143, "Height": 0.02567380852997303, "Left": 0.22079934179782867, "Top": 0.664814293384552}, "Polygon": [{"X": 0.221597358584404, "Y": 0.664814293384552}, {"X": 0.2667393684387207, "Y": 0.666150689125061}, {"X": 0.2659431993961334, "Y": 0.6904881000518799}, {"X": 0.22079934179782867, "Y": 0.6891577839851379}]}, "Id": "9b21ba50-9f87-4773-8774-6661c964ac83"}, {"BlockType": "WORD", "Confidence": 93.99175262451172, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01586502231657505, "Height": 0.025142852216959, "Left": 0.4088922441005707, "Top": 0.6709004044532776}, "Polygon": [{"X": 0.40969493985176086, "Y": 0.6709004044532776}, {"X": 0.4247572422027588, "Y": 0.6713462471961975}, {"X": 0.42395517230033875, "Y": 0.6960432529449463}, {"X": 0.4088922441005707, "Y": 0.6955994367599487}]}, "Id": "27a36624-529f-4300-a373-e44145569281"}, {"BlockType": "WORD", "Confidence": 88.13396453857422, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11303995549678802, "Height": 0.02407018095254898, "Left": 0.45501336455345154, "Top": 0.6745582222938538}, "Polygon": [{"X": 0.4556865692138672, "Y": 0.6745582222938538}, {"X": 0.5680533051490784, "Y": 0.6778830289840698}, {"X": 0.5673840045928955, "Y": 0.6986284255981445}, {"X": 0.45501336455345154, "Y": 0.6953166127204895}]}, "Id": "30796841-53a2-4f09-8367-c1d195465800"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "0840", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08023902028799057, "Height": 0.03638775274157524, "Left": 0.8741713166236877, "Top": 0.7459439039230347}, "Polygon": [{"X": 0.875255286693573, "Y": 0.7459439039230347}, {"X": 0.9544103145599365, "Y": 0.7482596635818481}, {"X": 0.9533308744430542, "Y": 0.782331645488739}, {"X": 0.8741713166236877, "Y": 0.7800310254096985}]}, "Id": "706216fc-b479-4ee1-a0b0-443dbbc8423a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "ffcdcf87-e497-42b1-af2c-03f9d825205d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "ffcdcf87-e497-42b1-af2c-03f9d825205d", "content-type": "application/x-amz-json-1.1", "content-length": "35355", "date": "Thu, 18 Sep 2025 09:00:30 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "346f7b19-3595-4876-9858-9cb4673f5dc8", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 09:00:33 GMT", "content-type": "application/json", "content-length": "1229", "connection": "keep-alive", "x-amzn-requestid": "346f7b19-3595-4876-9858-9cb4673f5dc8"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It lists work orders, truck ready times, driver, etc. It's like a schedule or dispatch sheet. It might be a \"log\" or \"ingate/outgate\"? It doesn't contain explicit header. It has \"WORK ORDER #\" and \"Truck Ready Time\", \"Truck Departure\". It looks like an inbound/outbound schedule. Could be an \"ingate\" or \"outgate\" or maybe a \"log\" (driver log). But \"ingate\" typically has truck/container number, entry time, etc. This page has truck ready time and departure time. Might be a \"log\" of scheduling, maybe a driver log or a \"load schedule\" not a standard doc type. The closest is \"log\". \"log\" is for driver log or activity record. This might be a \"log\" of truck dispatch. There's no driver logs like hours, etc. But it's a dispatch schedule. The best fit: log. So classify as \"log\"."}}}, {"toolUse": {"toolUseId": "tooluse_u3-UgObKTH-VnVhdBYX-vQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "log"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2230, "outputTokens": 237, "totalTokens": 2467}, "metrics": {"latencyMs": 1204}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/a9d888ca_8_log_3.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nDATE: 20-Feb\nUNIT #: 92644/283420\nTruck Ready Time: 5:00\nDRIVER: <PERSON>ARRA\nTruck Departure: 5:00\nDEPARTURE TIME: 5am\nPICK-\nCUSTOMER\nUP\nDEL\nWORK ORDER #\nDock Time\nIsk\n1040\n1sk\nParkn Manufacturing\nX\nX ***********\n1055\n1sk\n0830\nAlumalloy - Avon Lake\nX ***********\n0840\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}