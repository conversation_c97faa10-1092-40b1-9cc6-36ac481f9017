{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 3.503550516370524e-08}, {"X": 1.0, "Y": 1.0}, {"X": 2.1172418485093658e-08, "Y": 1.0}]}, "Id": "70c808b7-0b9a-425f-88c6-c30d7e32806f", "Relationships": [{"Type": "CHILD", "Ids": ["bba5d1eb-52c2-44ea-a344-d3f83401fafe", "dfe9728e-3a6e-4fb9-83e5-58e770109d3f", "261ad53f-ad29-4d00-849e-76e95cabebe2", "0c291503-9ab9-4cf1-bb97-05a3419ec783", "02c0c965-aed6-455f-8d56-aee4af45cd69", "fc0ad21e-5a8d-4153-99ec-7ac6e934a74d", "9d7f3ccf-efd3-4e23-8b33-7fb5327ddbce", "c17cb27a-7f5e-4cee-b2c4-bd9af90cb192", "d239c1b0-d994-45e4-9d33-5ccb8b9b6dd7", "c359c969-b0d1-4334-a57a-7ed1bae43fa5", "e8110bcd-e1dc-42af-bcdc-638c2b7e7560", "74e27bd7-ff55-4834-bf77-012e2345efa1", "ab1342c7-d580-40a5-b06b-063d428d5991", "a16decf1-0c15-4947-8076-7d029097172e", "2cec5c01-648c-4282-9cc6-5e328daa77fa", "ef5de816-f419-4a65-b039-857b74de29b4", "31b516aa-818f-44d2-8286-4b7010917a87", "d11c481c-3430-4082-a2e8-348764ac4fcd", "e495dde1-4d26-462d-a8cc-089a6678f262", "6bc730be-bf68-442f-8e2a-f50715aabe54", "d8a7d706-105d-46fe-8369-b3dadeb35919", "009c7640-a64f-4321-89a6-7a88bb7e436a", "737b96d3-1229-4adc-abcb-789a1cc6f95f", "43eeba74-4a58-44de-be62-db429a44adc5", "da2b7776-756d-4d9c-b58c-e3c0dde9f767"]}]}, {"BlockType": "LINE", "Confidence": 82.87129211425781, "Text": "®", "Geometry": {"BoundingBox": {"Width": 0.019983643665909767, "Height": 0.010080519132316113, "Left": 0.9119921326637268, "Top": 0.04303837940096855}, "Polygon": [{"X": 0.9119921922683716, "Y": 0.04303837940096855}, {"X": 0.9319757223129272, "Y": 0.043038662523031235}, {"X": 0.9319756627082825, "Y": 0.05311889946460724}, {"X": 0.9119921326637268, "Y": 0.05311862379312515}]}, "Id": "bba5d1eb-52c2-44ea-a344-d3f83401fafe", "Relationships": [{"Type": "CHILD", "Ids": ["8dec9c20-b320-461e-b6b8-448dc48b4acc"]}]}, {"BlockType": "LINE", "Confidence": 93.42414855957031, "Text": "BNSF", "Geometry": {"BoundingBox": {"Width": 0.820660412311554, "Height": 0.038768377155065536, "Left": 0.11075517535209656, "Top": 0.043917179107666016}, "Polygon": [{"X": 0.11075517535209656, "Y": 0.043917179107666016}, {"X": 0.9314155578613281, "Y": 0.04392874613404274}, {"X": 0.931415319442749, "Y": 0.08268555253744125}, {"X": 0.11075539886951447, "Y": 0.08267523348331451}]}, "Id": "dfe9728e-3a6e-4fb9-83e5-58e770109d3f", "Relationships": [{"Type": "CHILD", "Ids": ["c47b2e5b-fefe-4fae-bdca-06b901010c5a"]}]}, {"BlockType": "LINE", "Confidence": 96.96209716796875, "Text": "RAILWAY", "Geometry": {"BoundingBox": {"Width": 0.3639156222343445, "Height": 0.01953524723649025, "Left": 0.4541711211204529, "Top": 0.10633545368909836}, "Polygon": [{"X": 0.4541711211204529, "Y": 0.10633545368909836}, {"X": 0.8180867433547974, "Y": 0.1063397005200386}, {"X": 0.8180866241455078, "Y": 0.1258707046508789}, {"X": 0.4541711211204529, "Y": 0.12586674094200134}]}, "Id": "261ad53f-ad29-4d00-849e-76e95cabebe2", "Relationships": [{"Type": "CHILD", "Ids": ["89571c06-2606-4895-977b-488eb5365286"]}]}, {"BlockType": "LINE", "Confidence": 95.40318298339844, "Text": "OUT-GATE", "Geometry": {"BoundingBox": {"Width": 0.1439637988805771, "Height": 0.013259302824735641, "Left": 0.4283369779586792, "Top": 0.13937565684318542}, "Polygon": [{"X": 0.4283369779586792, "Y": 0.13937565684318542}, {"X": 0.5723007917404175, "Y": 0.139377161860466}, {"X": 0.5723007321357727, "Y": 0.15263496339321136}, {"X": 0.4283369779586792, "Y": 0.15263354778289795}]}, "Id": "0c291503-9ab9-4cf1-bb97-05a3419ec783", "Relationships": [{"Type": "CHILD", "Ids": ["dd073f77-1d9e-4523-b5e8-e4e9d4fa0673"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "BNSF Railway", "Geometry": {"BoundingBox": {"Width": 0.16888299584388733, "Height": 0.014339571818709373, "Left": 0.4157722592353821, "Top": 0.17866408824920654}, "Polygon": [{"X": 0.4157722592353821, "Y": 0.17866408824920654}, {"X": 0.584655225276947, "Y": 0.17866557836532593}, {"X": 0.584655225276947, "Y": 0.19300365447998047}, {"X": 0.4157722592353821, "Y": 0.19300226867198944}]}, "Id": "02c0c965-aed6-455f-8d56-aee4af45cd69", "Relationships": [{"Type": "CHILD", "Ids": ["529ed5cd-8cb0-4489-9b97-9d590f9fdc22", "8650957c-f458-40ab-8d64-4103d0f8e373"]}]}, {"BlockType": "LINE", "Confidence": 99.85059356689453, "Text": "Equipment Inspection Record", "Geometry": {"BoundingBox": {"Width": 0.3800037205219269, "Height": 0.014748997986316681, "Left": 0.3103874921798706, "Top": 0.20354194939136505}, "Polygon": [{"X": 0.3103874921798706, "Y": 0.20354194939136505}, {"X": 0.6903912425041199, "Y": 0.20354494452476501}, {"X": 0.6903911828994751, "Y": 0.21829095482826233}, {"X": 0.3103875517845154, "Y": 0.21828818321228027}]}, "Id": "fc0ad21e-5a8d-4153-99ec-7ac6e934a74d", "Relationships": [{"Type": "CHILD", "Ids": ["81da8838-8ebc-4d1e-804c-59fd943ea373", "4e90209c-2f9b-4c16-9372-893d2fa07127", "f903c8b3-7c5c-4c31-8334-ad19a2331c48"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "TRHU 344640 LOAD", "Geometry": {"BoundingBox": {"Width": 0.22560442984104156, "Height": 0.011494968086481094, "Left": 0.06075325608253479, "Top": 0.25385379791259766}, "Polygon": [{"X": 0.06075325608253479, "Y": 0.25385379791259766}, {"X": 0.2863576412200928, "Y": 0.2538551390171051}, {"X": 0.28635767102241516, "Y": 0.26534876227378845}, {"X": 0.06075333058834076, "Y": 0.26534754037857056}]}, "Id": "9d7f3ccf-efd3-4e23-8b33-7fb5327ddbce", "Relationships": [{"Type": "CHILD", "Ids": ["36e2cef4-1153-4dfb-a5b2-529a68335a9c", "6ed679d4-ee13-4604-b45f-9acba1278298", "cf61e055-6934-4edd-96e4-a4d0240b0710"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "TSPZ 214424", "Geometry": {"BoundingBox": {"Width": 0.1539134979248047, "Height": 0.011505592614412308, "Left": 0.060898035764694214, "Top": 0.2787908911705017}, "Polygon": [{"X": 0.060898035764694214, "Y": 0.2787908911705017}, {"X": 0.21481148898601532, "Y": 0.2787916660308838}, {"X": 0.2148115336894989, "Y": 0.2902964949607849}, {"X": 0.06089811027050018, "Y": 0.29029580950737}]}, "Id": "c17cb27a-7f5e-4cee-b2c4-bd9af90cb192", "Relationships": [{"Type": "CHILD", "Ids": ["7a1f8fde-defd-4dca-8167-e0c876025be8", "a006bdec-ade8-4513-848a-bd6d876c82f2"]}]}, {"BlockType": "LINE", "Confidence": 99.68614196777344, "Text": "Outgate: STPAUL MN 07/28/2025 1530", "Geometry": {"BoundingBox": {"Width": 0.4782565236091614, "Height": 0.014065657742321491, "Left": 0.06085161492228508, "Top": 0.3286037743091583}, "Polygon": [{"X": 0.06085161492228508, "Y": 0.3286037743091583}, {"X": 0.539108157157898, "Y": 0.32860520482063293}, {"X": 0.539108157157898, "Y": 0.34266942739486694}, {"X": 0.06085170805454254, "Y": 0.3426682651042938}]}, "Id": "d239c1b0-d994-45e4-9d33-5ccb8b9b6dd7", "Relationships": [{"Type": "CHILD", "Ids": ["63c9f537-640a-48bc-b9eb-60217428c725", "47d789a8-1e7a-446a-9d30-d8bce4e3dc54", "153b3ea8-4d5e-4942-a140-41ff60707724", "b9aa88c3-c08c-41f1-b4e5-7896500ac325", "801c7902-8d85-4f58-923a-5b376eafda2c"]}]}, {"BlockType": "LINE", "Confidence": 99.56682586669922, "Text": "Patron: OOCLUSA", "Geometry": {"BoundingBox": {"Width": 0.21180620789527893, "Height": 0.010919044725596905, "Left": 0.061138249933719635, "Top": 0.35440927743911743}, "Polygon": [{"X": 0.061138249933719635, "Y": 0.35440927743911743}, {"X": 0.2729444205760956, "Y": 0.35440969467163086}, {"X": 0.27294445037841797, "Y": 0.3653283417224884}, {"X": 0.061138324439525604, "Y": 0.36532801389694214}]}, "Id": "c359c969-b0d1-4334-a57a-7ed1bae43fa5", "Relationships": [{"Type": "CHILD", "Ids": ["dd335c3e-8b50-4ee9-bc3b-8b61b58f24f7", "a0f7e065-9e91-4adc-9928-7e4f2a5eeb33"]}]}, {"BlockType": "LINE", "Confidence": 99.56682586669922, "Text": "Trucker: CBQT", "Geometry": {"BoundingBox": {"Width": 0.18306343257427216, "Height": 0.01364572998136282, "Left": 0.0606771856546402, "Top": 0.3785955011844635}, "Polygon": [{"X": 0.0606771856546402, "Y": 0.3785955011844635}, {"X": 0.24374057352542877, "Y": 0.3785956799983978}, {"X": 0.24374061822891235, "Y": 0.3922412395477295}, {"X": 0.06067727506160736, "Y": 0.39224115014076233}]}, "Id": "e8110bcd-e1dc-42af-bcdc-638c2b7e7560", "Relationships": [{"Type": "CHILD", "Ids": ["2290d29d-9124-4edd-8e85-7371d2edd528", "113d8b44-ee53-49ef-a9cc-fd012ee9f704"]}]}, {"BlockType": "LINE", "Confidence": 98.6512451171875, "Text": "IC To:", "Geometry": {"BoundingBox": {"Width": 0.0779917761683464, "Height": 0.011050740256905556, "Left": 0.3023529648780823, "Top": 0.3792499601840973}, "Polygon": [{"X": 0.3023529648780823, "Y": 0.3792499601840973}, {"X": 0.3803447186946869, "Y": 0.37925004959106445}, {"X": 0.3803447484970093, "Y": 0.3903006911277771}, {"X": 0.30235299468040466, "Y": 0.3903006613254547}]}, "Id": "74e27bd7-ff55-4834-bf77-012e2345efa1", "Relationships": [{"Type": "CHILD", "Ids": ["60a39adb-58e2-431d-b676-e23b38b3e1bb", "3d4a08af-34cd-4e0f-b614-20b1f845d83c"]}]}, {"BlockType": "LINE", "Confidence": 98.87894439697266, "Text": "License:", "Geometry": {"BoundingBox": {"Width": 0.10777163505554199, "Height": 0.011567912064492702, "Left": 0.06082591786980629, "Top": 0.40340831875801086}, "Polygon": [{"X": 0.06082591786980629, "Y": 0.40340831875801086}, {"X": 0.1685974895954132, "Y": 0.40340831875801086}, {"X": 0.16859754920005798, "Y": 0.41497617959976196}, {"X": 0.06082599237561226, "Y": 0.41497623920440674}]}, "Id": "ab1342c7-d580-40a5-b06b-063d428d5991", "Relationships": [{"Type": "CHILD", "Ids": ["be0e4f6c-ca68-45f1-97ff-ee43b7d1b44c"]}]}, {"BlockType": "LINE", "Confidence": 97.88650512695312, "Text": "*****0420 (Displaying the last *****4 digits)", "Geometry": {"BoundingBox": {"Width": 0.6292718648910522, "Height": 0.014414440840482712, "Left": 0.18973121047019958, "Top": 0.40307796001434326}, "Polygon": [{"X": 0.18973121047019958, "Y": 0.40307796001434326}, {"X": 0.8190031051635742, "Y": 0.40307798981666565}, {"X": 0.8190030455589294, "Y": 0.4174920916557312}, {"X": 0.18973127007484436, "Y": 0.41749241948127747}]}, "Id": "a16decf1-0c15-4947-8076-7d029097172e", "Relationships": [{"Type": "CHILD", "Ids": ["7c5e19c5-94f4-4ef2-ae2c-a2e7ce4ee38e", "5512b700-d558-4d25-97b7-cf5f761b1dc1", "550dbe71-1cc8-4261-b685-075ed204ce8d", "f87a7d89-38d4-4b7a-b65e-526c296f197e", "830ccc04-a52a-419e-959e-222eaac18662", "4e9bcdd0-41f1-4823-8c85-6ef12eef8745"]}]}, {"BlockType": "LINE", "Confidence": 99.64734649658203, "Text": "Seal: 1693791", "Geometry": {"BoundingBox": {"Width": 0.18057584762573242, "Height": 0.011798543855547905, "Left": 0.06155220419168472, "Top": 0.42839565873146057}, "Polygon": [{"X": 0.06155220419168472, "Y": 0.4283958077430725}, {"X": 0.24212801456451416, "Y": 0.42839565873146057}, {"X": 0.24212805926799774, "Y": 0.44019395112991333}, {"X": 0.06155228242278099, "Y": 0.44019418954849243}]}, "Id": "2cec5c01-648c-4282-9cc6-5e328daa77fa", "Relationships": [{"Type": "CHILD", "Ids": ["d24c61a6-f840-407d-a609-5b38f941af65", "8c159842-d83c-4fa4-938e-0090826e48cb"]}]}, {"BlockType": "LINE", "Confidence": 99.40290832519531, "Text": "Cust: OOCLUSA", "Geometry": {"BoundingBox": {"Width": 0.18372823297977448, "Height": 0.010904673486948013, "Left": 0.06098480895161629, "Top": 0.45411622524261475}, "Polygon": [{"X": 0.06098480895161629, "Y": 0.4541165828704834}, {"X": 0.24471300840377808, "Y": 0.45411622524261475}, {"X": 0.24471303820610046, "Y": 0.46502044796943665}, {"X": 0.06098487973213196, "Y": 0.46502089500427246}]}, "Id": "ef5de816-f419-4a65-b039-857b74de29b4", "Relationships": [{"Type": "CHILD", "Ids": ["21e92a17-300f-45e8-88a4-2508d8256721", "96880d78-f586-4a9f-9619-20127aaf1e90"]}]}, {"BlockType": "LINE", "Confidence": 99.84162139892578, "Text": "Release ID: C9CC7D", "Geometry": {"BoundingBox": {"Width": 0.2537321448326111, "Height": 0.011531738564372063, "Left": 0.06057652086019516, "Top": 0.47845083475112915}, "Polygon": [{"X": 0.06057652086019516, "Y": 0.47845157980918884}, {"X": 0.31430864334106445, "Y": 0.47845083475112915}, {"X": 0.31430867314338684, "Y": 0.4899817407131195}, {"X": 0.06057659909129143, "Y": 0.48998257517814636}]}, "Id": "31b516aa-818f-44d2-8286-4b7010917a87", "Relationships": [{"Type": "CHILD", "Ids": ["63180ec2-92d1-4d6c-9d74-06c2086f1655", "cb3e23af-71f0-4d27-bdd1-8762ba4b1e43", "e5dd917d-70ae-437a-b60a-92e176c2cacb"]}]}, {"BlockType": "LINE", "Confidence": 99.27628326416016, "Text": "Gross WGT: 10488", "Geometry": {"BoundingBox": {"Width": 0.22410941123962402, "Height": 0.01133391447365284, "Left": 0.060795318335294724, "Top": 0.5034791827201843}, "Polygon": [{"X": 0.060795318335294724, "Y": 0.5034800171852112}, {"X": 0.28490468859672546, "Y": 0.5034791827201843}, {"X": 0.28490471839904785, "Y": 0.5148121118545532}, {"X": 0.06079539284110069, "Y": 0.5148130655288696}]}, "Id": "d11c481c-3430-4082-a2e8-348764ac4fcd", "Relationships": [{"Type": "CHILD", "Ids": ["9b64a351-1493-4b11-a9ea-745ed266b677", "d013d009-5c4e-4020-ba4d-755dab2282b4", "4484e710-8013-4559-ac44-9360d3127972"]}]}, {"BlockType": "LINE", "Confidence": 99.88988494873047, "Text": "Unit cannot return empty w/out waybill", "Geometry": {"BoundingBox": {"Width": 0.5347848534584045, "Height": 0.014378487132489681, "Left": 0.06040169298648834, "Top": 0.5278697609901428}, "Polygon": [{"X": 0.06040169298648834, "Y": 0.5278723239898682}, {"X": 0.5951865911483765, "Y": 0.5278697609901428}, {"X": 0.5951865315437317, "Y": 0.5422453284263611}, {"X": 0.060401786118745804, "Y": 0.5422482490539551}]}, "Id": "e495dde1-4d26-462d-a8cc-089a6678f262", "Relationships": [{"Type": "CHILD", "Ids": ["874b1cca-e36a-452e-ab4d-1928217adbcc", "d9d920a0-7281-42f5-ac0a-afc638e0042f", "5d16dcbd-de3c-4802-bf8d-63cd320b18ad", "9a263d09-bd60-4842-94b3-f2d45921d8a4", "f35e1aaa-4437-4ddd-98a2-98400b437b0d", "2f29f2b5-ab46-4c7a-9aee-fac7a574ee2f"]}]}, {"BlockType": "LINE", "Confidence": 99.62837982177734, "Text": "Stop Orders: Stg Due 07/29/25 DAYS 1-2/$175 3-3/$450 4-7/$250", "Geometry": {"BoundingBox": {"Width": 0.8593094944953918, "Height": 0.01457833033055067, "Left": 0.061625778675079346, "Top": 0.5525948405265808}, "Polygon": [{"X": 0.061625778675079346, "Y": 0.5525997877120972}, {"X": 0.9209352731704712, "Y": 0.5525948405265808}, {"X": 0.9209351539611816, "Y": 0.5671676993370056}, {"X": 0.061625875532627106, "Y": 0.567173182964325}]}, "Id": "6bc730be-bf68-442f-8e2a-f50715aabe54", "Relationships": [{"Type": "CHILD", "Ids": ["561950a7-58bd-417d-8315-86b68a33e0e0", "8922627d-4d98-4755-a2bb-64afba5137bb", "8805a101-ef33-4e65-a064-1db51e6a5ed7", "b49819b7-6350-405b-97bc-583ffb2219a1", "f1ccc0ea-b789-44cb-92b9-85b0736dd8a4", "b81ef4a4-eca1-45eb-95ad-f74a355874c3", "54dee466-3225-40c7-9b5d-c12801b47b8a", "5f093c44-7679-4129-a493-a3d424d53c1d", "b8af8c17-87ea-4aa0-a671-b5b021901dab"]}]}, {"BlockType": "LINE", "Confidence": 94.308837890625, "Text": "Damage:", "Geometry": {"BoundingBox": {"Width": 0.09429822862148285, "Height": 0.01325172558426857, "Left": 0.06052682176232338, "Top": 0.5789388418197632}, "Polygon": [{"X": 0.06052682176232338, "Y": 0.5789394974708557}, {"X": 0.15482497215270996, "Y": 0.5789388418197632}, {"X": 0.15482504665851593, "Y": 0.5921899080276489}, {"X": 0.060526907444000244, "Y": 0.5921905636787415}]}, "Id": "d8a7d706-105d-46fe-8369-b3dadeb35919", "Relationships": [{"Type": "CHILD", "Ids": ["3053ebd6-d716-443c-b478-3405ce43b2b9"]}]}, {"BlockType": "LINE", "Confidence": 97.10417175292969, "Text": "PHOTOGRAPHIC INSPECTION - Equipment defects are captured on", "Geometry": {"BoundingBox": {"Width": 0.8325077295303345, "Height": 0.014412688091397285, "Left": 0.06102219969034195, "Top": 0.6026877760887146}, "Polygon": [{"X": 0.06102219969034195, "Y": 0.6026942133903503}, {"X": 0.8935299515724182, "Y": 0.6026877760887146}, {"X": 0.8935298323631287, "Y": 0.6170935034751892}, {"X": 0.06102229282259941, "Y": 0.6171004176139832}]}, "Id": "009c7640-a64f-4321-89a6-7a88bb7e436a", "Relationships": [{"Type": "CHILD", "Ids": ["2ac88adb-6107-4ee7-bb17-87956c41c684", "bd052f26-8e94-4748-8616-f94c4ab7aa60", "086ddf63-c700-4201-a1ca-6edce14d6cc5", "217a41b8-cec1-4c44-908e-4c9c144b0f83", "f1f802ec-dfc0-48a7-94eb-0da925066b07", "bd635066-9841-44c5-9b97-b141de3546ac", "11ffd38f-1e20-45bc-9691-3d2535ad2f34", "c35cf72b-35af-4f29-8053-a1d23dc957e8"]}]}, {"BlockType": "LINE", "Confidence": 99.42203521728516, "Text": "recorded images.", "Geometry": {"BoundingBox": {"Width": 0.22027088701725006, "Height": 0.01448060292750597, "Left": 0.06107040122151375, "Top": 0.6274914145469666}, "Polygon": [{"X": 0.06107040122151375, "Y": 0.6274933815002441}, {"X": 0.2813412547111511, "Y": 0.6274914145469666}, {"X": 0.2813412845134735, "Y": 0.6419699788093567}, {"X": 0.06107049807906151, "Y": 0.6419720649719238}]}, "Id": "737b96d3-1229-4adc-abcb-789a1cc6f95f", "Relationships": [{"Type": "CHILD", "Ids": ["c64e82e9-a577-40cc-a8e5-084af509c923", "c3f3cfe2-7922-40dd-9976-9ddac9870e91"]}]}, {"BlockType": "LINE", "Confidence": 98.9382553100586, "Text": "Vessel Nme: CMA CGM ENDURANCE", "Geometry": {"BoundingBox": {"Width": 0.4089612066745758, "Height": 0.01196817122399807, "Left": 0.06011942774057388, "Top": 0.6526077389717102}, "Polygon": [{"X": 0.06011942774057388, "Y": 0.6526116728782654}, {"X": 0.4690806567668915, "Y": 0.6526077389717102}, {"X": 0.4690806567668915, "Y": 0.6645717024803162}, {"X": 0.06011950597167015, "Y": 0.6645758748054504}]}, "Id": "43eeba74-4a58-44de-be62-db429a44adc5", "Relationships": [{"Type": "CHILD", "Ids": ["065272f6-16c1-4218-86e3-d6defaa60ddc", "839c1cfc-13b0-4806-9674-99d4d66b1bdb", "257c2b24-3237-462e-b647-63f11391fe08", "e4258d3b-1cb5-4ccd-8cf6-a573fdf07f88", "5b32ce5a-5bcc-40a3-a105-0f561628e69c"]}]}, {"BlockType": "LINE", "Confidence": 98.89244842529297, "Text": "Nbr: 010 Bkg: **********", "Geometry": {"BoundingBox": {"Width": 0.33833062648773193, "Height": 0.013980749063193798, "Left": 0.05975925177335739, "Top": 0.6777158975601196}, "Polygon": [{"X": 0.05975925177335739, "Y": 0.6777195334434509}, {"X": 0.39808985590934753, "Y": 0.6777158975601196}, {"X": 0.3980898857116699, "Y": 0.6916928291320801}, {"X": 0.059759341180324554, "Y": 0.6916966438293457}]}, "Id": "da2b7776-756d-4d9c-b58c-e3c0dde9f767", "Relationships": [{"Type": "CHILD", "Ids": ["afb4bede-1c64-4e8b-aa56-8873801d4b76", "41751e7f-2c2e-40c9-9caa-498ee0c3e598", "62a26989-da10-4b84-b30b-185df8dd1174", "47991a33-b75f-4b93-868c-c6d4c1a107f8"]}]}, {"BlockType": "WORD", "Confidence": 82.87129211425781, "Text": "®", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019983643665909767, "Height": 0.010080519132316113, "Left": 0.9119921326637268, "Top": 0.04303837940096855}, "Polygon": [{"X": 0.9119921922683716, "Y": 0.04303837940096855}, {"X": 0.9319757223129272, "Y": 0.043038662523031235}, {"X": 0.9319756627082825, "Y": 0.05311889946460724}, {"X": 0.9119921326637268, "Y": 0.05311862379312515}]}, "Id": "8dec9c20-b320-461e-b6b8-448dc48b4acc"}, {"BlockType": "WORD", "Confidence": 93.42414855957031, "Text": "BNSF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.820660412311554, "Height": 0.038768377155065536, "Left": 0.11075517535209656, "Top": 0.043917179107666016}, "Polygon": [{"X": 0.11075517535209656, "Y": 0.043917179107666016}, {"X": 0.9314155578613281, "Y": 0.04392874613404274}, {"X": 0.931415319442749, "Y": 0.08268555253744125}, {"X": 0.11075539886951447, "Y": 0.08267523348331451}]}, "Id": "c47b2e5b-fefe-4fae-bdca-06b901010c5a"}, {"BlockType": "WORD", "Confidence": 96.96209716796875, "Text": "RAILWAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3639156222343445, "Height": 0.01953524723649025, "Left": 0.4541711211204529, "Top": 0.10633545368909836}, "Polygon": [{"X": 0.4541711211204529, "Y": 0.10633545368909836}, {"X": 0.8180867433547974, "Y": 0.1063397005200386}, {"X": 0.8180866241455078, "Y": 0.1258707046508789}, {"X": 0.4541711211204529, "Y": 0.12586674094200134}]}, "Id": "89571c06-2606-4895-977b-488eb5365286"}, {"BlockType": "WORD", "Confidence": 95.40318298339844, "Text": "OUT-GATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1439637988805771, "Height": 0.013259302824735641, "Left": 0.4283369779586792, "Top": 0.13937565684318542}, "Polygon": [{"X": 0.4283369779586792, "Y": 0.13937565684318542}, {"X": 0.5723007917404175, "Y": 0.139377161860466}, {"X": 0.5723007321357727, "Y": 0.15263496339321136}, {"X": 0.4283369779586792, "Y": 0.15263354778289795}]}, "Id": "dd073f77-1d9e-4523-b5e8-e4e9d4fa0673"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BNSF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055728085339069366, "Height": 0.010566101409494877, "Left": 0.4157722592353821, "Top": 0.1800144910812378}, "Polygon": [{"X": 0.4157722592353821, "Y": 0.1800144910812378}, {"X": 0.47150033712387085, "Y": 0.1800149828195572}, {"X": 0.47150033712387085, "Y": 0.1905806064605713}, {"X": 0.4157722592353821, "Y": 0.19058012962341309}]}, "Id": "529ed5cd-8cb0-4489-9b97-9d590f9fdc22"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Railway", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09900401532649994, "Height": 0.014338955283164978, "Left": 0.4856512248516083, "Top": 0.1786646991968155}, "Polygon": [{"X": 0.4856512248516083, "Y": 0.1786646991968155}, {"X": 0.584655225276947, "Y": 0.17866557836532593}, {"X": 0.584655225276947, "Y": 0.19300365447998047}, {"X": 0.4856512248516083, "Y": 0.1930028349161148}]}, "Id": "8650957c-f458-40ab-8d64-4103d0f8e373"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12433454394340515, "Height": 0.014747136272490025, "Left": 0.3103874921798706, "Top": 0.20354194939136505}, "Polygon": [{"X": 0.3103874921798706, "Y": 0.20354194939136505}, {"X": 0.43472203612327576, "Y": 0.20354293286800385}, {"X": 0.43472206592559814, "Y": 0.2182890921831131}, {"X": 0.3103875517845154, "Y": 0.21828818321228027}]}, "Id": "81da8838-8ebc-4d1e-804c-59fd943ea373"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13944101333618164, "Height": 0.01440532598644495, "Left": 0.45241114497184753, "Top": 0.20361538231372833}, "Polygon": [{"X": 0.45241114497184753, "Y": 0.20361538231372833}, {"X": 0.5918521285057068, "Y": 0.20361647009849548}, {"X": 0.5918521285057068, "Y": 0.2180207073688507}, {"X": 0.45241114497184753, "Y": 0.21801969408988953}]}, "Id": "4e90209c-2f9b-4c16-9372-893d2fa07127"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Record", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08457588404417038, "Height": 0.011772762052714825, "Left": 0.6058153510093689, "Top": 0.20384515821933746}, "Polygon": [{"X": 0.6058153510093689, "Y": 0.20384515821933746}, {"X": 0.6903912425041199, "Y": 0.2038458287715912}, {"X": 0.6903911828994751, "Y": 0.21561792492866516}, {"X": 0.6058153510093689, "Y": 0.21561729907989502}]}, "Id": "f903c8b3-7c5c-4c31-8334-ad19a2331c48"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRHU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05634864419698715, "Height": 0.010473479516804218, "Left": 0.06075326353311539, "Top": 0.2548743784427643}, "Polygon": [{"X": 0.06075326353311539, "Y": 0.2548743784427643}, {"X": 0.11710184812545776, "Y": 0.25487470626831055}, {"X": 0.11710190773010254, "Y": 0.26534783840179443}, {"X": 0.06075333058834076, "Y": 0.26534754037857056}]}, "Id": "36e2cef4-1153-4dfb-a5b2-529a68335a9c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "344640", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08111117780208588, "Height": 0.011489692144095898, "Left": 0.13316693902015686, "Top": 0.2538542151451111}, "Polygon": [{"X": 0.13316693902015686, "Y": 0.2538542151451111}, {"X": 0.21427807211875916, "Y": 0.2538546919822693}, {"X": 0.21427813172340393, "Y": 0.26534390449523926}, {"X": 0.13316701352596283, "Y": 0.26534348726272583}]}, "Id": "6ed679d4-ee13-4604-b45f-9acba1278298"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05577697604894638, "Height": 0.010451649315655231, "Left": 0.23058070242404938, "Top": 0.2548952102661133}, "Polygon": [{"X": 0.23058070242404938, "Y": 0.2548952102661133}, {"X": 0.2863576412200928, "Y": 0.25489553809165955}, {"X": 0.28635767102241516, "Y": 0.265346884727478}, {"X": 0.23058074712753296, "Y": 0.26534655690193176}]}, "Id": "cf61e055-6934-4edd-96e4-a4d0240b0710"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TSPZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05492274835705757, "Height": 0.010547004640102386, "Left": 0.06089804321527481, "Top": 0.2797490656375885}, "Polygon": [{"X": 0.06089804321527481, "Y": 0.2797490656375885}, {"X": 0.11582072824239731, "Y": 0.2797493040561676}, {"X": 0.11582078784704208, "Y": 0.2902960479259491}, {"X": 0.06089811027050018, "Y": 0.29029580950737}]}, "Id": "7a1f8fde-defd-4dca-8167-e0c876025be8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "214424", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0819830521941185, "Height": 0.011405165307223797, "Left": 0.1328284740447998, "Top": 0.27879124879837036}, "Polygon": [{"X": 0.1328284740447998, "Y": 0.27879124879837036}, {"X": 0.21481148898601532, "Y": 0.2787916660308838}, {"X": 0.2148115336894989, "Y": 0.29019641876220703}, {"X": 0.13282854855060577, "Y": 0.2901960611343384}]}, "Id": "a006bdec-ade8-4513-848a-bd6d876c82f2"}, {"BlockType": "WORD", "Confidence": 99.32318115234375, "Text": "Outgate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10776156187057495, "Height": 0.013320769183337688, "Left": 0.06085161864757538, "Top": 0.32934775948524475}, "Polygon": [{"X": 0.06085161864757538, "Y": 0.32934775948524475}, {"X": 0.16861312091350555, "Y": 0.329348087310791}, {"X": 0.16861318051815033, "Y": 0.3426685333251953}, {"X": 0.06085170805454254, "Y": 0.3426682651042938}]}, "Id": "63c9f537-640a-48bc-b9eb-60217428c725"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "STPAUL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08357518166303635, "Height": 0.010912615805864334, "Left": 0.18867912888526917, "Top": 0.32933762669563293}, "Polygon": [{"X": 0.18867912888526917, "Y": 0.32933762669563293}, {"X": 0.27225425839424133, "Y": 0.3293378949165344}, {"X": 0.2722543179988861, "Y": 0.34025025367736816}, {"X": 0.18867917358875275, "Y": 0.34025004506111145}]}, "Id": "47d789a8-1e7a-446a-9d30-d8bce4e3dc54"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "MN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028897777199745178, "Height": 0.010517151094973087, "Left": 0.28609931468963623, "Top": 0.32946139574050903}, "Polygon": [{"X": 0.28609931468963623, "Y": 0.32946139574050903}, {"X": 0.3149970769882202, "Y": 0.3294614851474762}, {"X": 0.3149971067905426, "Y": 0.33997854590415955}, {"X": 0.2860993444919586, "Y": 0.3399784564971924}]}, "Id": "153b3ea8-4d5e-4942-a140-41ff60707724"}, {"BlockType": "WORD", "Confidence": 99.28571319580078, "Text": "07/28/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1379479169845581, "Height": 0.012991303578019142, "Left": 0.33050116896629333, "Top": 0.3286045789718628}, "Polygon": [{"X": 0.33050116896629333, "Y": 0.3286045789718628}, {"X": 0.46844908595085144, "Y": 0.3286049962043762}, {"X": 0.46844908595085144, "Y": 0.3415958881378174}, {"X": 0.3305011987686157, "Y": 0.34159553050994873}]}, "Id": "b9aa88c3-c08c-41f1-b4e5-7896500ac325"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1530", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05297505483031273, "Height": 0.01152174174785614, "Left": 0.4861330986022949, "Top": 0.32874637842178345}, "Polygon": [{"X": 0.4861330986022949, "Y": 0.32874637842178345}, {"X": 0.539108157157898, "Y": 0.3287465274333954}, {"X": 0.539108157157898, "Y": 0.3402681350708008}, {"X": 0.4861330986022949, "Y": 0.34026798605918884}]}, "Id": "801c7902-8d85-4f58-923a-5b376eafda2c"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "Patron:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09399504214525223, "Height": 0.010918864980340004, "Left": 0.061138249933719635, "Top": 0.35440927743911743}, "Polygon": [{"X": 0.061138249933719635, "Y": 0.35440927743911743}, {"X": 0.1551332324743271, "Y": 0.35440948605537415}, {"X": 0.15513329207897186, "Y": 0.3653281629085541}, {"X": 0.061138324439525604, "Y": 0.36532801389694214}]}, "Id": "dd335c3e-8b50-4ee9-bc3b-8b61b58f24f7"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "OOCLUSA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09922078251838684, "Height": 0.010607878677546978, "Left": 0.17372366786003113, "Top": 0.35443180799484253}, "Polygon": [{"X": 0.17372366786003113, "Y": 0.35443180799484253}, {"X": 0.2729444205760956, "Y": 0.35443201661109924}, {"X": 0.27294445037841797, "Y": 0.3650397062301636}, {"X": 0.1737237274646759, "Y": 0.36503955721855164}]}, "Id": "a0f7e065-9e91-4adc-9928-7e4f2a5eeb33"}, {"BlockType": "WORD", "Confidence": 99.13365173339844, "Text": "Trucker:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10844885557889938, "Height": 0.011421321891248226, "Left": 0.0606771856546402, "Top": 0.3785955011844635}, "Polygon": [{"X": 0.0606771856546402, "Y": 0.3785955011844635}, {"X": 0.1691259890794754, "Y": 0.37859562039375305}, {"X": 0.16912603378295898, "Y": 0.3900168240070343}, {"X": 0.06067726016044617, "Y": 0.3900167644023895}]}, "Id": "2290d29d-9124-4edd-8e85-7371d2edd528"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CBQT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055760473012924194, "Height": 0.012603998184204102, "Left": 0.18798014521598816, "Top": 0.3796372413635254}, "Polygon": [{"X": 0.18798014521598816, "Y": 0.3796372413635254}, {"X": 0.24374057352542877, "Y": 0.37963730096817017}, {"X": 0.24374061822891235, "Y": 0.3922412395477295}, {"X": 0.18798020482063293, "Y": 0.3922412097454071}]}, "Id": "113d8b44-ee53-49ef-a9cc-fd012ee9f704"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "IC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026131685823202133, "Height": 0.011050711385905743, "Left": 0.3023529648780823, "Top": 0.3792499601840973}, "Polygon": [{"X": 0.3023529648780823, "Y": 0.3792499601840973}, {"X": 0.3284846246242523, "Y": 0.3792499899864197}, {"X": 0.3284846544265747, "Y": 0.3903006613254547}, {"X": 0.30235299468040466, "Y": 0.3903006613254547}]}, "Id": "60a39adb-58e2-431d-b676-e23b38b3e1bb"}, {"BlockType": "WORD", "Confidence": 97.33179473876953, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0372392013669014, "Height": 0.010631073266267776, "Left": 0.3431055545806885, "Top": 0.3795401155948639}, "Polygon": [{"X": 0.3431055545806885, "Y": 0.3795401155948639}, {"X": 0.3803447186946869, "Y": 0.3795401453971863}, {"X": 0.3803447484970093, "Y": 0.3901711702346802}, {"X": 0.3431055545806885, "Y": 0.3901711702346802}]}, "Id": "3d4a08af-34cd-4e0f-b614-20b1f845d83c"}, {"BlockType": "WORD", "Confidence": 98.87894439697266, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10777163505554199, "Height": 0.011567912064492702, "Left": 0.06082591786980629, "Top": 0.40340831875801086}, "Polygon": [{"X": 0.06082591786980629, "Y": 0.40340831875801086}, {"X": 0.1685974895954132, "Y": 0.40340831875801086}, {"X": 0.16859754920005798, "Y": 0.41497617959976196}, {"X": 0.06082599237561226, "Y": 0.41497623920440674}]}, "Id": "be0e4f6c-ca68-45f1-97ff-ee43b7d1b44c"}, {"BlockType": "WORD", "Confidence": 97.5390625, "Text": "*****0420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12338832765817642, "Height": 0.01133925374597311, "Left": 0.18973121047019958, "Top": 0.40370750427246094}, "Polygon": [{"X": 0.18973121047019958, "Y": 0.40370750427246094}, {"X": 0.313119500875473, "Y": 0.40370750427246094}, {"X": 0.3131195306777954, "Y": 0.41504672169685364}, {"X": 0.18973125517368317, "Y": 0.415046751499176}]}, "Id": "7c5e19c5-94f4-4ef2-ae2c-a2e7ce4ee38e"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "(Displaying", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15070487558841705, "Height": 0.014350819401443005, "Left": 0.33295583724975586, "Top": 0.40314149856567383}, "Polygon": [{"X": 0.33295583724975586, "Y": 0.40314149856567383}, {"X": 0.4836607277393341, "Y": 0.4031415283679962}, {"X": 0.4836607277393341, "Y": 0.4174922704696655}, {"X": 0.33295586705207825, "Y": 0.4174923300743103}]}, "Id": "5512b700-d558-4d25-97b7-cf5f761b1dc1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04054466262459755, "Height": 0.011606953106820583, "Left": 0.4991607964038849, "Top": 0.4034961760044098}, "Polygon": [{"X": 0.4991608262062073, "Y": 0.4034961760044098}, {"X": 0.5397054553031921, "Y": 0.4034961760044098}, {"X": 0.5397054553031921, "Y": 0.41510310769081116}, {"X": 0.4991607964038849, "Y": 0.41510313749313354}]}, "Id": "550dbe71-1cc8-4261-b685-075ed204ce8d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "last", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053065329790115356, "Height": 0.011555680073797703, "Left": 0.5567961931228638, "Top": 0.40352341532707214}, "Polygon": [{"X": 0.5567961931228638, "Y": 0.40352341532707214}, {"X": 0.6098615527153015, "Y": 0.40352341532707214}, {"X": 0.6098614931106567, "Y": 0.4150790870189667}, {"X": 0.5567961931228638, "Y": 0.4150790870189667}]}, "Id": "f87a7d89-38d4-4b7a-b65e-526c296f197e"}, {"BlockType": "WORD", "Confidence": 90.53491973876953, "Text": "*****4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0810895562171936, "Height": 0.010509219951927662, "Left": 0.6270456314086914, "Top": 0.40400800108909607}, "Polygon": [{"X": 0.6270456910133362, "Y": 0.40400800108909607}, {"X": 0.708135187625885, "Y": 0.40400800108909607}, {"X": 0.708135187625885, "Y": 0.41451719403266907}, {"X": 0.6270456314086914, "Y": 0.41451722383499146}]}, "Id": "830ccc04-a52a-419e-959e-222eaac18662"}, {"BlockType": "WORD", "Confidence": 99.6827163696289, "Text": "digits)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0950343906879425, "Height": 0.014376976527273655, "Left": 0.7239686846733093, "Top": 0.40307798981666565}, "Polygon": [{"X": 0.7239687442779541, "Y": 0.40307798981666565}, {"X": 0.8190031051635742, "Y": 0.40307798981666565}, {"X": 0.8190030455589294, "Y": 0.41745492815971375}, {"X": 0.7239686846733093, "Y": 0.41745495796203613}]}, "Id": "4e9bcdd0-41f1-4823-8c85-6ef12eef8745"}, {"BlockType": "WORD", "Confidence": 99.33374786376953, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06479740142822266, "Height": 0.01179843582212925, "Left": 0.06155220419168472, "Top": 0.42839574813842773}, "Polygon": [{"X": 0.06155220419168472, "Y": 0.4283958077430725}, {"X": 0.126349538564682, "Y": 0.42839574813842773}, {"X": 0.12634961307048798, "Y": 0.44019410014152527}, {"X": 0.06155228242278099, "Y": 0.44019418954849243}]}, "Id": "d24c61a6-f840-407d-a609-5b38f941af65"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "1693791", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09527307003736496, "Height": 0.011343587189912796, "Left": 0.14685498178005219, "Top": 0.42854124307632446}, "Polygon": [{"X": 0.14685498178005219, "Y": 0.4285413324832916}, {"X": 0.24212801456451416, "Y": 0.42854124307632446}, {"X": 0.24212805926799774, "Y": 0.4398847222328186}, {"X": 0.14685504138469696, "Y": 0.43988484144210815}]}, "Id": "8c159842-d83c-4fa4-938e-0090826e48cb"}, {"BlockType": "WORD", "Confidence": 98.96524047851562, "Text": "Cust:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0655704066157341, "Height": 0.010751188732683659, "Left": 0.06098480895161629, "Top": 0.45412787795066833}, "Polygon": [{"X": 0.06098480895161629, "Y": 0.4541279971599579}, {"X": 0.1265551596879959, "Y": 0.45412787795066833}, {"X": 0.12655521929264069, "Y": 0.4648789167404175}, {"X": 0.06098487973213196, "Y": 0.4648790657520294}]}, "Id": "21e92a17-300f-45e8-88a4-2508d8256721"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "OOCLUSA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0991286039352417, "Height": 0.010904473252594471, "Left": 0.14558443427085876, "Top": 0.45411622524261475}, "Polygon": [{"X": 0.14558443427085876, "Y": 0.4541164040565491}, {"X": 0.24471300840377808, "Y": 0.45411622524261475}, {"X": 0.24471303820610046, "Y": 0.46502044796943665}, {"X": 0.14558449387550354, "Y": 0.46502068638801575}]}, "Id": "96880d78-f586-4a9f-9619-20127aaf1e90"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Release", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0979958325624466, "Height": 0.011440597474575043, "Left": 0.06057652086019516, "Top": 0.47845128178596497}, "Polygon": [{"X": 0.06057652086019516, "Y": 0.47845157980918884}, {"X": 0.15857230126857758, "Y": 0.47845128178596497}, {"X": 0.15857236087322235, "Y": 0.48989155888557434}, {"X": 0.06057659909129143, "Y": 0.4898918867111206}]}, "Id": "63180ec2-92d1-4d6c-9d74-06c2086f1655"}, {"BlockType": "WORD", "Confidence": 99.6045913696289, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035865187644958496, "Height": 0.010285770520567894, "Left": 0.17528560757637024, "Top": 0.47917115688323975}, "Polygon": [{"X": 0.17528560757637024, "Y": 0.4791712760925293}, {"X": 0.21115075051784515, "Y": 0.47917115688323975}, {"X": 0.21115079522132874, "Y": 0.48945680260658264}, {"X": 0.17528565227985382, "Y": 0.4894569218158722}]}, "Id": "cb3e23af-71f0-4d27-bdd1-8762ba4b1e43"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "C9CC7D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08388243615627289, "Height": 0.011206550523638725, "Left": 0.23042622208595276, "Top": 0.4787754416465759}, "Polygon": [{"X": 0.23042622208595276, "Y": 0.4787757098674774}, {"X": 0.31430864334106445, "Y": 0.4787754416465759}, {"X": 0.31430867314338684, "Y": 0.4899817407131195}, {"X": 0.23042626678943634, "Y": 0.489982008934021}]}, "Id": "e5dd917d-70ae-437a-b60a-92e176c2cacb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06926427781581879, "Height": 0.010814121924340725, "Left": 0.06079532206058502, "Top": 0.5039989948272705}, "Polygon": [{"X": 0.06079532206058502, "Y": 0.5039992332458496}, {"X": 0.13005954027175903, "Y": 0.5039989948272705}, {"X": 0.1300595998764038, "Y": 0.5148127675056458}, {"X": 0.06079539284110069, "Y": 0.5148130655288696}]}, "Id": "9b64a351-1493-4b11-a9ea-745ed266b677"}, {"BlockType": "WORD", "Confidence": 97.83860778808594, "Text": "WGT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051798511296510696, "Height": 0.010511621832847595, "Left": 0.14527571201324463, "Top": 0.504146158695221}, "Polygon": [{"X": 0.14527571201324463, "Y": 0.5041463971138}, {"X": 0.19707417488098145, "Y": 0.504146158695221}, {"X": 0.19707423448562622, "Y": 0.5146575570106506}, {"X": 0.1452757716178894, "Y": 0.5146577954292297}]}, "Id": "d013d009-5c4e-4020-ba4d-755dab2282b4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "10488", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06752387434244156, "Height": 0.011216497980058193, "Left": 0.21738086640834808, "Top": 0.5034791827201843}, "Polygon": [{"X": 0.21738086640834808, "Y": 0.5034794211387634}, {"X": 0.28490468859672546, "Y": 0.5034791827201843}, {"X": 0.28490471839904785, "Y": 0.514695405960083}, {"X": 0.21738091111183167, "Y": 0.5146956443786621}]}, "Id": "4484e710-8013-4559-ac44-9360d3127972"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05502227693796158, "Height": 0.01172830630093813, "Left": 0.06040169298648834, "Top": 0.5279879570007324}, "Polygon": [{"X": 0.06040169298648834, "Y": 0.5279882550239563}, {"X": 0.11542390286922455, "Y": 0.5279879570007324}, {"X": 0.11542396992444992, "Y": 0.5397160053253174}, {"X": 0.06040177121758461, "Y": 0.5397163033485413}]}, "Id": "874b1cca-e36a-452e-ab4d-1928217adbcc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "cannot", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0824773758649826, "Height": 0.010347818955779076, "Left": 0.13201098144054413, "Top": 0.5294040441513062}, "Polygon": [{"X": 0.13201098144054413, "Y": 0.5294044613838196}, {"X": 0.21448832750320435, "Y": 0.5294040441513062}, {"X": 0.21448835730552673, "Y": 0.539751410484314}, {"X": 0.1320110410451889, "Y": 0.5397518277168274}]}, "Id": "d9d920a0-7281-42f5-ac0a-afc638e0042f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "return", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08449260145425797, "Height": 0.010564297437667847, "Left": 0.23029972612857819, "Top": 0.5291438698768616}, "Polygon": [{"X": 0.23029972612857819, "Y": 0.5291442275047302}, {"X": 0.31479230523109436, "Y": 0.5291438698768616}, {"X": 0.31479233503341675, "Y": 0.5397077202796936}, {"X": 0.23029977083206177, "Y": 0.539708137512207}]}, "Id": "5d16dcbd-de3c-4802-bf8d-63cd320b18ad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06961534172296524, "Height": 0.012797609902918339, "Left": 0.3298160135746002, "Top": 0.5294491648674011}, "Polygon": [{"X": 0.3298160135746002, "Y": 0.5294495224952698}, {"X": 0.39943134784698486, "Y": 0.5294491648674011}, {"X": 0.39943137764930725, "Y": 0.542246401309967}, {"X": 0.3298160433769226, "Y": 0.5422467589378357}]}, "Id": "9a263d09-bd60-4842-94b3-f2d45921d8a4"}, {"BlockType": "WORD", "Confidence": 99.4385757446289, "Text": "w/out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06893676519393921, "Height": 0.012471437454223633, "Left": 0.4132349491119385, "Top": 0.5283997058868408}, "Polygon": [{"X": 0.4132349491119385, "Y": 0.5284000039100647}, {"X": 0.4821717143058777, "Y": 0.5283997058868408}, {"X": 0.4821717143058777, "Y": 0.540870726108551}, {"X": 0.4132349491119385, "Y": 0.5408711433410645}]}, "Id": "f35e1aaa-4437-4ddd-98a2-98400b437b0d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "waybill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09764764457941055, "Height": 0.014228824526071548, "Left": 0.4975389242172241, "Top": 0.5278697609901428}, "Polygon": [{"X": 0.4975389242172241, "Y": 0.527870237827301}, {"X": 0.5951865911483765, "Y": 0.5278697609901428}, {"X": 0.5951865315437317, "Y": 0.5420980453491211}, {"X": 0.4975389242172241, "Y": 0.5420985817909241}]}, "Id": "2f29f2b5-ab46-4c7a-9aee-fac7a574ee2f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Stop", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05501529574394226, "Height": 0.013263666070997715, "Left": 0.06162578612565994, "Top": 0.5539094805717468}, "Polygon": [{"X": 0.06162578612565994, "Y": 0.5539098381996155}, {"X": 0.11664100736379623, "Y": 0.5539094805717468}, {"X": 0.1166410818696022, "Y": 0.5671728253364563}, {"X": 0.061625875532627106, "Y": 0.567173182964325}]}, "Id": "561950a7-58bd-417d-8315-86b68a33e0e0"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Orders:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09391037374734879, "Height": 0.01146477647125721, "Left": 0.13148021697998047, "Top": 0.5532478094100952}, "Polygon": [{"X": 0.13148021697998047, "Y": 0.5532483458518982}, {"X": 0.22539053857326508, "Y": 0.5532478094100952}, {"X": 0.22539058327674866, "Y": 0.5647119879722595}, {"X": 0.13148027658462524, "Y": 0.5647125840187073}]}, "Id": "8922627d-4d98-4755-a2bb-64afba5137bb"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Stg", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04066794365644455, "Height": 0.01328217051923275, "Left": 0.24518193304538727, "Top": 0.5538741946220398}, "Polygon": [{"X": 0.24518193304538727, "Y": 0.5538744330406189}, {"X": 0.28584983944892883, "Y": 0.5538741946220398}, {"X": 0.2858498692512512, "Y": 0.5671561360359192}, {"X": 0.24518197774887085, "Y": 0.5671563744544983}]}, "Id": "8805a101-ef33-4e65-a064-1db51e6a5ed7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04114880785346031, "Height": 0.010638578794896603, "Left": 0.30092164874076843, "Top": 0.5541306138038635}, "Polygon": [{"X": 0.30092164874076843, "Y": 0.5541308522224426}, {"X": 0.34207043051719666, "Y": 0.5541306138038635}, {"X": 0.34207046031951904, "Y": 0.5647689700126648}, {"X": 0.3009216785430908, "Y": 0.5647692084312439}]}, "Id": "b49819b7-6350-405b-97bc-583ffb2219a1"}, {"BlockType": "WORD", "Confidence": 99.61276245117188, "Text": "07/29/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11015155166387558, "Height": 0.01274366769939661, "Left": 0.3583710491657257, "Top": 0.5531731247901917}, "Polygon": [{"X": 0.3583710491657257, "Y": 0.5531737208366394}, {"X": 0.4685226082801819, "Y": 0.5531731247901917}, {"X": 0.4685226082801819, "Y": 0.5659160614013672}, {"X": 0.3583710789680481, "Y": 0.5659167766571045}]}, "Id": "f1ccc0ea-b789-44cb-92b9-85b0736dd8a4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DAYS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05582190677523613, "Height": 0.011126063764095306, "Left": 0.4842175543308258, "Top": 0.5537718534469604}, "Polygon": [{"X": 0.4842175543308258, "Y": 0.5537721514701843}, {"X": 0.5400394201278687, "Y": 0.5537718534469604}, {"X": 0.5400394201278687, "Y": 0.5648975372314453}, {"X": 0.4842175543308258, "Y": 0.564897894859314}]}, "Id": "b81ef4a4-eca1-45eb-95ad-f74a355874c3"}, {"BlockType": "WORD", "Confidence": 98.84147644042969, "Text": "1-2/$175", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11078697443008423, "Height": 0.013769766315817833, "Left": 0.5556741952896118, "Top": 0.5525963306427002}, "Polygon": [{"X": 0.5556742548942566, "Y": 0.552596926689148}, {"X": 0.666461169719696, "Y": 0.5525963306427002}, {"X": 0.666461169719696, "Y": 0.5663653612136841}, {"X": 0.5556741952896118, "Y": 0.5663660764694214}]}, "Id": "54dee466-3225-40c7-9b5d-c12801b47b8a"}, {"BlockType": "WORD", "Confidence": 99.36862182617188, "Text": "3-3/$450", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11107093840837479, "Height": 0.013825586996972561, "Left": 0.6832265257835388, "Top": 0.5527200698852539}, "Polygon": [{"X": 0.6832265853881836, "Y": 0.5527206659317017}, {"X": 0.7942975163459778, "Y": 0.5527200698852539}, {"X": 0.7942973971366882, "Y": 0.5665449500083923}, {"X": 0.6832265257835388, "Y": 0.5665456056594849}]}, "Id": "5f093c44-7679-4129-a493-a3d424d53c1d"}, {"BlockType": "WORD", "Confidence": 99.25064086914062, "Text": "4-7/$250", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11031504720449448, "Height": 0.013780943118035793, "Left": 0.8106202483177185, "Top": 0.5528386831283569}, "Polygon": [{"X": 0.8106203079223633, "Y": 0.5528392791748047}, {"X": 0.9209352731704712, "Y": 0.5528386831283569}, {"X": 0.9209351539611816, "Y": 0.5666189193725586}, {"X": 0.8106202483177185, "Y": 0.5666195750236511}]}, "Id": "b8af8c17-87ea-4aa0-a671-b5b021901dab"}, {"BlockType": "WORD", "Confidence": 94.308837890625, "Text": "Damage:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09429822862148285, "Height": 0.01325172558426857, "Left": 0.06052682176232338, "Top": 0.5789388418197632}, "Polygon": [{"X": 0.06052682176232338, "Y": 0.5789394974708557}, {"X": 0.15482497215270996, "Y": 0.5789388418197632}, {"X": 0.15482504665851593, "Y": 0.5921899080276489}, {"X": 0.060526907444000244, "Y": 0.5921905636787415}]}, "Id": "3053ebd6-d716-443c-b478-3405ce43b2b9"}, {"BlockType": "WORD", "Confidence": 87.2448959350586, "Text": "PHOTOGRAPHIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1677280217409134, "Height": 0.01089018490165472, "Left": 0.061022207140922546, "Top": 0.6038254499435425}, "Polygon": [{"X": 0.061022207140922546, "Y": 0.6038267612457275}, {"X": 0.22875018417835236, "Y": 0.6038254499435425}, {"X": 0.22875022888183594, "Y": 0.6147142648696899}, {"X": 0.06102227792143822, "Y": 0.6147156357765198}]}, "Id": "2ac88adb-6107-4ee7-bb17-87956c41c684"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "INSPECTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14027193188667297, "Height": 0.011041272431612015, "Left": 0.24576576054096222, "Top": 0.603636622428894}, "Polygon": [{"X": 0.24576576054096222, "Y": 0.6036376953125}, {"X": 0.386037677526474, "Y": 0.603636622428894}, {"X": 0.386037677526474, "Y": 0.6146767735481262}, {"X": 0.2457657903432846, "Y": 0.614677906036377}]}, "Id": "bd052f26-8e94-4748-8616-f94c4ab7aa60"}, {"BlockType": "WORD", "Confidence": 90.1612319946289, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010905712842941284, "Height": 0.0024995049461722374, "Left": 0.4011203944683075, "Top": 0.6085343956947327}, "Polygon": [{"X": 0.4011203944683075, "Y": 0.6085345149040222}, {"X": 0.4120261073112488, "Y": 0.6085343956947327}, {"X": 0.4120261073112488, "Y": 0.6110337972640991}, {"X": 0.4011204242706299, "Y": 0.6110339164733887}]}, "Id": "086ddf63-c700-4201-a1ca-6edce14d6cc5"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12536461651325226, "Height": 0.014407005161046982, "Left": 0.42793262004852295, "Top": 0.6026903986930847}, "Polygon": [{"X": 0.42793262004852295, "Y": 0.6026913523674011}, {"X": 0.553297221660614, "Y": 0.6026903986930847}, {"X": 0.553297221660614, "Y": 0.6170963644981384}, {"X": 0.42793262004852295, "Y": 0.6170973777770996}]}, "Id": "217a41b8-cec1-4c44-908e-4c9c144b0f83"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "defects", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09765483438968658, "Height": 0.01188809797167778, "Left": 0.5688146352767944, "Top": 0.6028881669044495}, "Polygon": [{"X": 0.5688146352767944, "Y": 0.6028889417648315}, {"X": 0.6664694547653198, "Y": 0.6028881669044495}, {"X": 0.666469395160675, "Y": 0.6147754788398743}, {"X": 0.5688146352767944, "Y": 0.6147762537002563}]}, "Id": "f1f802ec-dfc0-48a7-94eb-0da925066b07"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040649108588695526, "Height": 0.008701621554791927, "Left": 0.6826351284980774, "Top": 0.6060327887535095}, "Polygon": [{"X": 0.6826351284980774, "Y": 0.6060330867767334}, {"X": 0.7232842445373535, "Y": 0.6060327887535095}, {"X": 0.7232841849327087, "Y": 0.6147340536117554}, {"X": 0.6826351284980774, "Y": 0.614734411239624}]}, "Id": "bd635066-9841-44c5-9b97-b141de3546ac"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11150017380714417, "Height": 0.014037293381989002, "Left": 0.7393861413002014, "Top": 0.6028796434402466}, "Polygon": [{"X": 0.7393862009048462, "Y": 0.6028804779052734}, {"X": 0.850886344909668, "Y": 0.6028796434402466}, {"X": 0.8508862257003784, "Y": 0.6169160008430481}, {"X": 0.7393861413002014, "Y": 0.6169168949127197}]}, "Id": "11ffd38f-1e20-45bc-9691-3d2535ad2f34"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027207614853978157, "Height": 0.008531107567250729, "Left": 0.8663223385810852, "Top": 0.6059954166412354}, "Polygon": [{"X": 0.8663223385810852, "Y": 0.6059956550598145}, {"X": 0.8935299515724182, "Y": 0.6059954166412354}, {"X": 0.8935298919677734, "Y": 0.6145262718200684}, {"X": 0.8663223385810852, "Y": 0.6145265102386475}]}, "Id": "c35cf72b-35af-4f29-8053-a1d23dc957e8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11221075803041458, "Height": 0.01163275633007288, "Left": 0.06107040494680405, "Top": 0.6278415322303772}, "Polygon": [{"X": 0.06107040494680405, "Y": 0.6278425455093384}, {"X": 0.17328110337257385, "Y": 0.6278415322303772}, {"X": 0.17328116297721863, "Y": 0.6394732594490051}, {"X": 0.061070479452610016, "Y": 0.6394743323326111}]}, "Id": "c64e82e9-a577-40cc-a8e5-084af509c923"}, {"BlockType": "WORD", "Confidence": 98.84407043457031, "Text": "images.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09202485531568527, "Height": 0.014479413628578186, "Left": 0.18931643664836884, "Top": 0.6274914145469666}, "Polygon": [{"X": 0.18931643664836884, "Y": 0.6274922490119934}, {"X": 0.2813412547111511, "Y": 0.6274914145469666}, {"X": 0.2813412845134735, "Y": 0.6419699788093567}, {"X": 0.1893164962530136, "Y": 0.6419708728790283}]}, "Id": "c3f3cfe2-7922-40dd-9976-9ddac9870e91"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08322659879922867, "Height": 0.011965013109147549, "Left": 0.06011942774057388, "Top": 0.6526108980178833}, "Polygon": [{"X": 0.06011942774057388, "Y": 0.6526116728782654}, {"X": 0.14334596693515778, "Y": 0.6526108980178833}, {"X": 0.14334602653980255, "Y": 0.6645750403404236}, {"X": 0.06011950597167015, "Y": 0.6645758748054504}]}, "Id": "065272f6-16c1-4218-86e3-d6defaa60ddc"}, {"BlockType": "WORD", "Confidence": 96.48497009277344, "Text": "Nme:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052292849868535995, "Height": 0.01049974374473095, "Left": 0.15884502232074738, "Top": 0.653610110282898}, "Polygon": [{"X": 0.15884502232074738, "Y": 0.6536106467247009}, {"X": 0.2111378312110901, "Y": 0.653610110282898}, {"X": 0.21113787591457367, "Y": 0.6641093492507935}, {"X": 0.15884508192539215, "Y": 0.6641098856925964}]}, "Id": "839c1cfc-13b0-4806-9674-99d4d66b1bdb"}, {"BlockType": "WORD", "Confidence": 98.7244873046875, "Text": "CMA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04231550171971321, "Height": 0.010282253846526146, "Left": 0.2304477095603943, "Top": 0.653792142868042}, "Polygon": [{"X": 0.2304477095603943, "Y": 0.6537925601005554}, {"X": 0.2727631628513336, "Y": 0.653792142868042}, {"X": 0.2727632224559784, "Y": 0.6640740036964417}, {"X": 0.23044773936271667, "Y": 0.6640744209289551}]}, "Id": "257c2b24-3237-462e-b647-63f11391fe08"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "CGM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0419670045375824, "Height": 0.010505695827305317, "Left": 0.2869470417499542, "Top": 0.6536963582038879}, "Polygon": [{"X": 0.2869470417499542, "Y": 0.6536967754364014}, {"X": 0.32891401648521423, "Y": 0.6536963582038879}, {"X": 0.3289140462875366, "Y": 0.6642016172409058}, {"X": 0.2869470715522766, "Y": 0.664202094078064}]}, "Id": "e4258d3b-1cb5-4ccd-8cf6-a573fdf07f88"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ENDURANCE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1256960779428482, "Height": 0.010487234219908714, "Left": 0.3433845639228821, "Top": 0.6535326242446899}, "Polygon": [{"X": 0.3433845639228821, "Y": 0.6535338163375854}, {"X": 0.4690806567668915, "Y": 0.6535326242446899}, {"X": 0.4690806567668915, "Y": 0.6640185713768005}, {"X": 0.34338459372520447, "Y": 0.6640198230743408}]}, "Id": "5b32ce5a-5bcc-40a3-a105-0f561628e69c"}, {"BlockType": "WORD", "Confidence": 96.29544067382812, "Text": "Nbr:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05242554470896721, "Height": 0.011396434158086777, "Left": 0.05975925177335739, "Top": 0.6778323650360107}, "Polygon": [{"X": 0.05975925177335739, "Y": 0.6778329014778137}, {"X": 0.11218473315238953, "Y": 0.6778323650360107}, {"X": 0.1121847927570343, "Y": 0.6892282366752625}, {"X": 0.05975932627916336, "Y": 0.6892287731170654}]}, "Id": "afb4bede-1c64-4e8b-aa56-8873801d4b76"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "010", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03902723640203476, "Height": 0.011502212844789028, "Left": 0.13293547928333282, "Top": 0.677871584892273}, "Polygon": [{"X": 0.13293547928333282, "Y": 0.6778720021247864}, {"X": 0.1719626635313034, "Y": 0.677871584892273}, {"X": 0.171962708234787, "Y": 0.6893733739852905}, {"X": 0.1329355388879776, "Y": 0.689373791217804}]}, "Id": "41751e7f-2c2e-40c9-9caa-498ee0c3e598"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "Bkg:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051386039704084396, "Height": 0.01384886633604765, "Left": 0.18786253035068512, "Top": 0.677846372127533}, "Polygon": [{"X": 0.18786253035068512, "Y": 0.6778469085693359}, {"X": 0.23924852907657623, "Y": 0.677846372127533}, {"X": 0.23924857378005981, "Y": 0.6916946172714233}, {"X": 0.1878626048564911, "Y": 0.6916952133178711}]}, "Id": "62a26989-da10-4b84-b30b-185df8dd1174"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13819454610347748, "Height": 0.011590363457798958, "Left": 0.25989532470703125, "Top": 0.6777158975601196}, "Polygon": [{"X": 0.25989532470703125, "Y": 0.677717387676239}, {"X": 0.39808985590934753, "Y": 0.6777158975601196}, {"X": 0.3980898857116699, "Y": 0.6893047094345093}, {"X": 0.259895384311676, "Y": 0.6893062591552734}]}, "Id": "47991a33-b75f-4b93-868c-c6d4c1a107f8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "556af387-a60f-44d5-9b30-e3f037c7829b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "556af387-a60f-44d5-9b30-e3f037c7829b", "content-type": "application/x-amz-json-1.1", "content-length": "53767", "date": "Wed, 24 Sep 2025 13:32:23 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "be8b0a14-5127-4d5e-90b7-a323cc41a265", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:26 GMT", "content-type": "application/json", "content-length": "572", "connection": "keep-alive", "x-amzn-requestid": "be8b0a14-5127-4d5e-90b7-a323cc41a265"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have one page. Need to classify. The content: \"BNSF RAILWAY OUT-GATE BNSF Railway Equipment Inspection Record\" indicates Outgate Document. Keywords: Outgate, Release ID, etc. So doc_type = outgate. No continuation, just single page. Output accordingly."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2791, "outputTokens": 80, "totalTokens": 2871}, "metrics": {"latencyMs": 607}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/74a6d8bd_EKMN29XJBZVFPLOYTKU7.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n®\nBNSF\nRAILWAY\nOUT-GATE\nBNSF Railway\nEquipment Inspection Record\nTRHU 344640 LOAD\nTSPZ 214424\nOutgate: STPAUL MN 07/28/2025 1530\nPatron: OOCLUSA\nTrucker: CBQT\nIC To:\nLicense:\n*****0420 (Displaying the last *****4 digits)\nSeal: 1693791\nCust: OOCLUSA\nRelease ID: C9CC7D\nGross WGT: 10488\nUnit cannot return empty w/out waybill\nStop Orders: Stg Due 07/29/25 DAYS 1-2/$175 3-3/$450 4-7/$250\nDamage:\nPHOTOGRAPHIC INSPECTION - Equipment defects are captured on\nrecorded images.\nVessel Nme: CMA CGM ENDURANCE\nNbr: 010 Bkg: **********\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}