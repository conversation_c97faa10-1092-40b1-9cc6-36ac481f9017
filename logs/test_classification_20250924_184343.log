2025-09-24 18:43:43,291 - INFO - Logging initialized. Log file: logs/test_classification_20250924_184343.log
2025-09-24 18:43:43,291 - INFO - 📁 Found 10 files to process
2025-09-24 18:43:43,292 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:43:43,292 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 18:43:43,292 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 18:43:43,292 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 18:43:43,292 - INFO - ⬆️ [18:43:43] Uploading: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:45,076 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf -> s3://document-extraction-logistically/temp/263612d7_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:45,076 - INFO - 🔍 [18:43:45] Starting classification: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:45,077 - INFO - ⬆️ [18:43:45] Uploading: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:45,079 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:45,109 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:45,123 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/263612d7_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:45,123 - INFO - Processing PDF from S3...
2025-09-24 18:43:45,123 - INFO - Downloading PDF from S3 to /tmp/tmp5g2nj0tk/263612d7_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:46,151 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf -> s3://document-extraction-logistically/temp/3a68798c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:46,151 - INFO - 🔍 [18:43:46] Starting classification: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:46,152 - INFO - ⬆️ [18:43:46] Uploading: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:43:46,154 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:46,169 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:46,174 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3a68798c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:46,175 - INFO - Processing PDF from S3...
2025-09-24 18:43:46,175 - INFO - Downloading PDF from S3 to /tmp/tmpq_9pkoos/3a68798c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:46,864 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf -> s3://document-extraction-logistically/temp/019c241e_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:43:46,865 - INFO - 🔍 [18:43:46] Starting classification: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:43:46,866 - INFO - ⬆️ [18:43:46] Uploading: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:46,868 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:46,893 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:46,896 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/019c241e_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:43:46,897 - INFO - Processing PDF from S3...
2025-09-24 18:43:46,897 - INFO - Downloading PDF from S3 to /tmp/tmpw8n7jxsm/019c241e_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:43:46,902 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:43:46,903 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:46,903 - INFO - Splitting PDF 263612d7_G0H0K1LRWDOG7LXAKKQ7 into 1 pages
2025-09-24 18:43:46,906 - INFO - Split PDF into 1 pages
2025-09-24 18:43:46,906 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:46,906 - INFO - Expected pages: [1]
2025-09-24 18:43:47,492 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf -> s3://document-extraction-logistically/temp/490496b6_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:47,493 - INFO - 🔍 [18:43:47] Starting classification: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:47,493 - INFO - ⬆️ [18:43:47] Uploading: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:43:47,495 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:47,520 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:47,524 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/490496b6_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:47,524 - INFO - Processing PDF from S3...
2025-09-24 18:43:47,524 - INFO - Downloading PDF from S3 to /tmp/tmp_2mf3_93/490496b6_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:48,362 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:43:48,362 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:48,364 - INFO - Splitting PDF 019c241e_KDFG6JJAZV41YZP4TEGN into 1 pages
2025-09-24 18:43:48,372 - INFO - Split PDF into 1 pages
2025-09-24 18:43:48,372 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:48,372 - INFO - Expected pages: [1]
2025-09-24 18:43:48,633 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf -> s3://document-extraction-logistically/temp/33c41382_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:43:48,633 - INFO - 🔍 [18:43:48] Starting classification: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:43:48,634 - INFO - ⬆️ [18:43:48] Uploading: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:48,636 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:48,655 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:48,660 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33c41382_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:43:48,660 - INFO - Processing PDF from S3...
2025-09-24 18:43:48,660 - INFO - Downloading PDF from S3 to /tmp/tmp9epqihyp/33c41382_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:43:48,738 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:43:48,738 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:48,739 - INFO - Splitting PDF 3a68798c_GKNF55W2CF2JR6EBXMPX into 1 pages
2025-09-24 18:43:48,746 - INFO - Split PDF into 1 pages
2025-09-24 18:43:48,746 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:48,746 - INFO - Expected pages: [1]
2025-09-24 18:43:48,810 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:43:48,810 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:48,811 - INFO - Splitting PDF 490496b6_MMNWUYGBLL1K5KSJBNOT into 1 pages
2025-09-24 18:43:48,814 - INFO - Split PDF into 1 pages
2025-09-24 18:43:48,814 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:48,814 - INFO - Expected pages: [1]
2025-09-24 18:43:49,239 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf -> s3://document-extraction-logistically/temp/b0174a47_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:49,239 - INFO - 🔍 [18:43:49] Starting classification: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:49,240 - INFO - ⬆️ [18:43:49] Uploading: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:43:49,246 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:49,259 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:49,264 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b0174a47_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:49,264 - INFO - Processing PDF from S3...
2025-09-24 18:43:49,265 - INFO - Downloading PDF from S3 to /tmp/tmpupz1xnqc/b0174a47_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:49,837 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf -> s3://document-extraction-logistically/temp/99c2117e_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:43:49,837 - INFO - 🔍 [18:43:49] Starting classification: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:43:49,838 - INFO - ⬆️ [18:43:49] Uploading: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:43:49,840 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:49,897 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:49,900 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/99c2117e_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:43:49,900 - INFO - Processing PDF from S3...
2025-09-24 18:43:49,900 - INFO - Downloading PDF from S3 to /tmp/tmpuc7ha8t_/99c2117e_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:43:50,378 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:43:50,378 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:50,380 - INFO - Splitting PDF 33c41382_NFMA1926AJ8R3TDZQALU into 1 pages
2025-09-24 18:43:50,383 - INFO - Split PDF into 1 pages
2025-09-24 18:43:50,383 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:50,383 - INFO - Expected pages: [1]
2025-09-24 18:43:50,462 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf -> s3://document-extraction-logistically/temp/6a456ee3_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:43:50,463 - INFO - 🔍 [18:43:50] Starting classification: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:43:50,464 - INFO - ⬆️ [18:43:50] Uploading: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:43:50,465 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:50,483 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:50,489 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6a456ee3_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:43:50,490 - INFO - Processing PDF from S3...
2025-09-24 18:43:50,491 - INFO - Downloading PDF from S3 to /tmp/tmpfoxwlcm_/6a456ee3_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:43:50,502 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:43:50,502 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:50,504 - INFO - Splitting PDF b0174a47_O1YJBQBLYAU6D0SDDKAU into 1 pages
2025-09-24 18:43:50,506 - INFO - Split PDF into 1 pages
2025-09-24 18:43:50,506 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:50,506 - INFO - Expected pages: [1]
2025-09-24 18:43:51,285 - INFO - Page 1: Extracted 667 characters, 55 lines from 263612d7_G0H0K1LRWDOG7LXAKKQ7_d37af533_page_001.pdf
2025-09-24 18:43:51,286 - INFO - Successfully processed page 1
2025-09-24 18:43:51,286 - INFO - Combined 1 pages into final text
2025-09-24 18:43:51,287 - INFO - Text validation for 263612d7_G0H0K1LRWDOG7LXAKKQ7: 684 characters, 1 pages
2025-09-24 18:43:51,287 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:51,287 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:51,479 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:43:51,479 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:51,480 - INFO - Splitting PDF 99c2117e_OLQ7TIWW6EVTC6BXA1II into 1 pages
2025-09-24 18:43:51,485 - INFO - Split PDF into 1 pages
2025-09-24 18:43:51,485 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:51,485 - INFO - Expected pages: [1]
2025-09-24 18:43:51,583 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg -> s3://document-extraction-logistically/temp/27801a29_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:43:51,583 - INFO - 🔍 [18:43:51] Starting classification: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:43:51,584 - INFO - ⬆️ [18:43:51] Uploading: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:43:51,585 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:51,601 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:51,606 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/27801a29_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:43:51,606 - INFO - Processing image from S3...
2025-09-24 18:43:51,932 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:43:51,932 - INFO - Splitting PDF into individual pages...
2025-09-24 18:43:51,933 - INFO - Splitting PDF 6a456ee3_V3BCW6BW9XVKKO6WY2YJ into 1 pages
2025-09-24 18:43:51,933 - INFO - Split PDF into 1 pages
2025-09-24 18:43:51,933 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:43:51,933 - INFO - Expected pages: [1]
2025-09-24 18:43:52,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg -> s3://document-extraction-logistically/temp/ed9beab7_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:43:52,476 - INFO - 🔍 [18:43:52] Starting classification: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:43:52,477 - INFO - Initializing TextractProcessor...
2025-09-24 18:43:52,496 - INFO - Initializing BedrockProcessor...
2025-09-24 18:43:52,502 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ed9beab7_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:43:52,503 - INFO - Processing image from S3...
2025-09-24 18:43:53,054 - INFO - Page 1: Extracted 311 characters, 32 lines from b0174a47_O1YJBQBLYAU6D0SDDKAU_8283e394_page_001.pdf
2025-09-24 18:43:53,055 - INFO - Successfully processed page 1
2025-09-24 18:43:53,055 - INFO - Combined 1 pages into final text
2025-09-24 18:43:53,056 - INFO - Text validation for b0174a47_O1YJBQBLYAU6D0SDDKAU: 328 characters, 1 pages
2025-09-24 18:43:53,056 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:53,056 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:54,095 - INFO - Page 1: Extracted 372 characters, 28 lines from 490496b6_MMNWUYGBLL1K5KSJBNOT_a3b48b7a_page_001.pdf
2025-09-24 18:43:54,095 - INFO - Successfully processed page 1
2025-09-24 18:43:54,096 - INFO - Combined 1 pages into final text
2025-09-24 18:43:54,096 - INFO - Text validation for 490496b6_MMNWUYGBLL1K5KSJBNOT: 389 characters, 1 pages
2025-09-24 18:43:54,096 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:54,097 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:54,190 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '30e43658-8fdb-4919-ad37-e821a80fa4cc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:13:54 GMT', 'content-type': 'application/json', 'content-length': '1660', 'connection': 'keep-alive', 'x-amzn-requestid': '30e43658-8fdb-4919-ad37-e821a80fa4cc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page: page1. We need to determine document type.\n\nLet\'s analyze the content. The page contains:\n\n- Capstone Logistics header\n- Receipt #, Work Date, Bill Code, Carrier, Dock, Door\n- Purchase Orders, Vendor, Total Initial Pallets, Total Finished Pallets, Total Case Count, Total Weight, Trailer Number, Tractor Number, BOL, Comments, Canned Comments, Unloaders, Base Charge, Convenience Fee, Total Cost, Payments, Amount CapstonePay, Total Payments.\n\nThis looks like a "receipt" or invoice? The page has base charge, convenience fee, total cost, payments. Also has "Receipt #". This is likely a "Carrier Invoice". But we should check if it is a commercial invoice. There is no HS code, no customs. So likely \'invoice\'.\n\nBut also might be a \'bol\' or \'pod\'? It includes BOL: but just a header? It has no "Bill of Lading" text. It\'s more like a billing summary for a load. It includes total cost, base charge, convenience fee, payments. So it\'s an invoice, maybe a \'carrier invoice\'.\n\nThus doc_type: invoice. Page 1.\n\nWe must return an array of objects. There\'s only page 1. Output:\n\n{\n  documents: [\n    {page_no:1, doc_type:"invoice"}\n  ]\n}\n\nBut we must use tool classify_logistics_doc_type. So we call the function with JSON.\n\nLet\'s produce JSON accordingly.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2777, 'outputTokens': 331, 'totalTokens': 3108}, 'metrics': {'latencyMs': 1612}}
2025-09-24 18:43:54,191 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/263612d7_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:54,208 - INFO - 

G0H0K1LRWDOG7LXAKKQ7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:43:54,208 - INFO - 

✓ Saved result: output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:43:54,375 - INFO - Page 1: Extracted 493 characters, 42 lines from 3a68798c_GKNF55W2CF2JR6EBXMPX_e672e044_page_001.pdf
2025-09-24 18:43:54,375 - INFO - Successfully processed page 1
2025-09-24 18:43:54,376 - INFO - Combined 1 pages into final text
2025-09-24 18:43:54,376 - INFO - Text validation for 3a68798c_GKNF55W2CF2JR6EBXMPX: 510 characters, 1 pages
2025-09-24 18:43:54,376 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:54,376 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:54,511 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/263612d7_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:43:55,831 - INFO - Page 1: Extracted 460 characters, 36 lines from 33c41382_NFMA1926AJ8R3TDZQALU_302e4232_page_001.pdf
2025-09-24 18:43:55,831 - INFO - Successfully processed page 1
2025-09-24 18:43:55,832 - INFO - Combined 1 pages into final text
2025-09-24 18:43:55,832 - INFO - Text validation for 33c41382_NFMA1926AJ8R3TDZQALU: 477 characters, 1 pages
2025-09-24 18:43:55,832 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:55,833 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:57,222 - INFO - S3 Image temp/27801a29_Y9K6Z2GSAP496UQLOOGU.jpeg: Extracted 542 characters, 30 lines
2025-09-24 18:43:57,222 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:57,222 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:57,306 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f1f54a74-ee8d-475c-9f3b-b1be525f6d0a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:13:56 GMT', 'content-type': 'application/json', 'content-length': '1003', 'connection': 'keep-alive', 'x-amzn-requestid': 'f1f54a74-ee8d-475c-9f3b-b1be525f6d0a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify. Let\'s identify keywords. The page shows "IDSH", "Innovative Distribution Services", address, Fed ID#, some status, date, truck, trailer, door, carrier, PO(s) 2644663, Vendor(s) AMERCAREROYAL LLC, Subtotal, Convenience Fee, Total Charge, Billing Type: Relay Checkout, etc. It looks like a billing statement or invoice from a carrier? Fields: subtotal, convenience fee, total charge, billing type, payment type. It seems like a carrier invoice. No HS codes, no customs. It\'s a bill for services. Could be "invoice" type. There\'s no "Invoice" header but it\'s an invoice-like. So doc_type: invoice. We need to output JSON via tool call. Page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2707, 'outputTokens': 184, 'totalTokens': 2891}, 'metrics': {'latencyMs': 999}}
2025-09-24 18:43:57,307 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3a68798c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:57,326 - INFO - 

GKNF55W2CF2JR6EBXMPX.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:43:57,326 - INFO - 

✓ Saved result: output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:43:57,529 - INFO - Page 1: Extracted 657 characters, 54 lines from 99c2117e_OLQ7TIWW6EVTC6BXA1II_aab1744c_page_001.pdf
2025-09-24 18:43:57,529 - INFO - Successfully processed page 1
2025-09-24 18:43:57,529 - INFO - Combined 1 pages into final text
2025-09-24 18:43:57,530 - INFO - Text validation for 99c2117e_OLQ7TIWW6EVTC6BXA1II: 674 characters, 1 pages
2025-09-24 18:43:57,530 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:57,530 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:57,631 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3a68798c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:43:57,774 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dd639980-135c-4a6c-8cd5-63860e828bb0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:13:57 GMT', 'content-type': 'application/json', 'content-length': '2161', 'connection': 'keep-alive', 'x-amzn-requestid': 'dd639980-135c-4a6c-8cd5-63860e828bb0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify document type. Let\'s parse content.\n\nText: "TWS of Carteret LLC ... Receipt#, PO#, Carrier: RAF TRUCKING ... Invoice # ... Activity: Total Unloading $275.00 Fee Amount: ... Convenience Fee: ... Total: $284.62"\n\nThis looks like a carrier invoice? It has Invoice #, amounts, PO#, Carrier. It\'s a charge for unloading. Keywords: Invoice #, Amount. So likely "invoice" (carrier invoice). Or could be "lumper_receipt"? Lumper receipt has keywords like PO, Door number, Date-Time, initial pallets, finished pallets, service charge, total amount. Here we have a PO# and a Door: 40. "Total Unloading" maybe lumper service. But lumper receipts usually have "Door number" in header as "Door: X" yep. Also "PO#" maybe. Let\'s check lumper_receipt definition: "Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount." This page includes PO#, Door, Carrier, service charge (unloading), total amount. So yes, lumper_receipt seems to fit. Also invoice has "Invoice #". But lumper receipts may also have invoice #? Not sure. The difference: lumper receipt is a separate document for unloading/loading services. This page seems to be a receipt for unloading. So classify as lumper_receipt.\n\nCheck if any other classification: invoice has "Invoice No, Amount Due, Total Due, Bill To, Bill From". This page has "Invoice #". But the presence of "Receipt#" maybe indicates a receipt. "Total Unloading" suggests a specific fee. Also "Convenience Fee" is a separate fee.\n\nThus likely lumper_receipt.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing one object: page_no: 1, doc_type: "lumper_receipt".\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2651, 'outputTokens': 442, 'totalTokens': 3093}, 'metrics': {'latencyMs': 2057}}
2025-09-24 18:43:57,775 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b0174a47_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:57,788 - INFO - 

O1YJBQBLYAU6D0SDDKAU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:43:57,788 - INFO - 

✓ Saved result: output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:43:57,806 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'de7ceabe-11a4-42bd-ae03-6384e806646f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:13:57 GMT', 'content-type': 'application/json', 'content-length': '2891', 'connection': 'keep-alive', 'x-amzn-requestid': 'de7ceabe-11a4-42bd-ae03-6384e806646f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. We need to examine keywords. The text shows "Relay Sale#", "Paid By", "Services", "BOL Number", "PO #", "Driver Name". It\'s likely a receipt for a service from a relay or fuel sales? The header "Relay Sale#" suggests maybe a fuel receipt or a relay service. The word "BOL Number" appears but that could be just included on a receipt. The page shows Payment method, Visa. That seems like a fuel receipt or a relay sale receipt. The keywords for fuel_receipt: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. But the content doesn\'t mention Fuel explicitly. "Relay Sale" could be a fuel sale at a relay station. But not explicitly "Fuel". Could be a driver convenience fee receipt. "Convenience Fee" appears. For "lumper_receipt": it mentions PO, Door number, Date-Time, initial pallets, etc. This has "Pallets" and "Convenience Fee". lumper receipts are for loading/unloading labor, but this has "relay sale" and payment method. Could be a "fuel receipt" but no fuel. Could be "other". Let\'s consider "combined_carrier_documents": No, only one header.\n\nThe keywords "BOL Number" appears but no "Bill of Lading" text. So maybe it\'s a receipt for a fuel or relay sale. The category "fuel_receipt" might be best if it includes "Fuel" or "Diesel", but not present. "lumper_receipt" requires lumper-related descriptions like PO, Door number, initial pallets, finished pallets, etc. There\'s "Driver Name", "BOL Number", "PO #", "Pallets". That matches some lumper receipts: they include "Pallets" and "PO #". But also "Convenience Fee". But lumper receipts: "service charge, total amount". This page shows "Convenience Fee" and "Grand Total". Could be a lumper receipt. However lumper receipts are usually for loading/unloading. The presence of "Relay Sale#" indicates a sale at a relay station, maybe for fuel. "Paid By:" etc. Could be a "fuel receipt" but missing "Fuel". The presence of "BOL Number" might be part of the receipt for a service at a relay. The "PO #" suggests a purchase order from the company. "Driver Name" indicates driver. "Convenience Fee" is typical for fuel receipts. So likely it\'s a fuel receipt. Even if "Fuel" not present, it\'s the best match: "fuel_receipt" includes "Pump #" etc. But here "Relay Sale#" could be a fuel sale. So output doc_type "fuel_receipt". Ensure output format: call tool classify_logistics_doc_type with documents array containing page_no:1, doc_type:"fuel_receipt".\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2665, 'outputTokens': 619, 'totalTokens': 3284}, 'metrics': {'latencyMs': 2521}}
2025-09-24 18:43:57,807 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/490496b6_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:58,116 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b0174a47_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:43:58,127 - INFO - 

MMNWUYGBLL1K5KSJBNOT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:43:58,127 - INFO - 

✓ Saved result: output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:43:58,446 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/490496b6_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:43:58,487 - INFO - S3 Image temp/ed9beab7_ZOBA55ELUA2GRE3UJM3I.jpg: Extracted 709 characters, 59 lines
2025-09-24 18:43:58,488 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:58,488 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:43:59,392 - INFO - Page 1: Extracted 695 characters, 58 lines from 019c241e_KDFG6JJAZV41YZP4TEGN_e0efde52_page_001.pdf
2025-09-24 18:43:59,393 - INFO - Successfully processed page 1
2025-09-24 18:43:59,393 - INFO - Combined 1 pages into final text
2025-09-24 18:43:59,394 - INFO - Text validation for 019c241e_KDFG6JJAZV41YZP4TEGN: 712 characters, 1 pages
2025-09-24 18:43:59,394 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:43:59,395 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:44:00,160 - INFO - Page 1: Extracted 343 characters, 28 lines from 6a456ee3_V3BCW6BW9XVKKO6WY2YJ_d7d29d2c_page_001.pdf
2025-09-24 18:44:00,160 - INFO - Successfully processed page 1
2025-09-24 18:44:00,160 - INFO - Combined 1 pages into final text
2025-09-24 18:44:00,161 - INFO - Text validation for 6a456ee3_V3BCW6BW9XVKKO6WY2YJ: 360 characters, 1 pages
2025-09-24 18:44:00,161 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:44:00,161 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:44:00,376 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6b9166e4-ef77-4ab8-9222-3259f8b143f8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:14:00 GMT', 'content-type': 'application/json', 'content-length': '2573', 'connection': 'keep-alive', 'x-amzn-requestid': '6b9166e4-ef77-4ab8-9222-3259f8b143f8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page in ascending order. The user provided page1 content. We need to identify if page1 is part of a multi-page or single page. We see it looks like a "PO" maybe? Let\'s analyze content.\n\nPage content includes:\n\n- Express Freight Handlers, Inc.\n- PO Box...\n- Carrier: MFS TRANSPORT\n- Load No: 546-250922-10263774\n- Tractor No: 3398\n- Door No: 13\n- Trailer No: 92940\n- Payment Type: Relay\n- Driver Name: MOHAMED\n- Date: 09/22/2025\n- Signature:\n- PO No:\n- Vendor Name:\n- Vendor No:\n- Qty:\n- Weight:\n- Charge:\n- Fee:\n- Add\'l Info:\n- 669254\n- LINDAR FORMING\n- 293800\n- 932\n- 0.0\n- $140.0\n- $7.7\n\nThe content looks like a "PO No:" header but it\'s blank? Actually there is PO No: but no number. It also includes "Load No:" etc. It might be a "Purchase Order" or "PO" type. Typically a PO document includes PO No, items, quantity etc. The page includes "Qty", "Weight", "Charge", "Fee". Looks like a purchase order? But maybe it\'s a "Load Confirmation" or "Load Order"? Let\'s see definitions.\n\nWe have "po" type: Purchase Order. Keywords: Purchase Order, PO#, Buyer, PO Number. This page includes "PO No:" but no number. Might be a PO. Also includes "Load No", "Tractor No", etc. Not typical for PO. A PO would have vendor, buyer, line items, etc. The vendor fields exist: Vendor Name, Vendor No. So maybe it\'s a PO for freight services. The presence of "PO No:" suggests it\'s a PO. It\'s not an invoice. There\'s no Invoice header. It\'s not a BOL. There\'s no "Bill of Lading" words. It\'s not a POD. It\'s not a rate confirmation. It\'s not a combined carrier docs. It\'s not a pack list. It\'s not a clear to pay. It\'s not a scale ticket. So likely "po".\n\nBut we also need to classify each page exactly. There\'s only page1. So we will output a JSON array with one object: page_no: 1, doc_type: "po".\n\nCheck if there is anything else: "Express Freight Handlers, Inc." is a logistics company. This looks like a PO. So we output accordingly.\n\nWe must use the tool classify_logistics_doc_type. We\'ll call it with documents array.\n\nLet\'s produce:\n\n{\n  "documents": [\n    { "page_no": 1, "doc_type": "po" }\n  ]\n}\n\nWe need to output exactly that via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2708, 'outputTokens': 616, 'totalTokens': 3324}, 'metrics': {'latencyMs': 2732}}
2025-09-24 18:44:00,376 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33c41382_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:44:00,389 - INFO - 

NFMA1926AJ8R3TDZQALU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 18:44:00,389 - INFO - 

✓ Saved result: output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:44:00,734 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33c41382_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:44:01,019 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '95afcdd3-ee0b-4753-bd1a-4664d8bb84df', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:14:00 GMT', 'content-type': 'application/json', 'content-length': '1264', 'connection': 'keep-alive', 'x-amzn-requestid': '95afcdd3-ee0b-4753-bd1a-4664d8bb84df'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to determine doc_type. The page contains: "Receipt #" , "Bill Code", "Carrier", "Dock", "Door", "Purchase Orders", "Vendor", "Total Initial Pallets", "Total Finished Pallets", "Total Case Count", "Trailer Number", "Base Charge", "Convenience Fee", "Total Cost", "Payments". It looks like a delivery receipt or a load receipt. Could be a "Delivery Receipt" or "Load Receipt" which might be equivalent to a "delivery receipt" but not in the enum list. Maybe "other". However, we have "Receipt #" and "Bill Code" but not "Invoice". It\'s a receipt from carrier. Not a POD? POD usually has "Proof of Delivery" and signature. This page doesn\'t show signature. Might be a "delivery receipt" used for internal logs. But not in categories. So classify as "other".\n\nWe must output via tool classify_logistics_doc_type. Provide array of documents: for page 1, doc_type: "other".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2791, 'outputTokens': 232, 'totalTokens': 3023}, 'metrics': {'latencyMs': 1324}}
2025-09-24 18:44:01,019 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ed9beab7_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:44:01,043 - INFO - 

ZOBA55ELUA2GRE3UJM3I.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:44:01,044 - INFO - 

✓ Saved result: output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:44:01,354 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ed9beab7_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:44:01,461 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7c5ac5fb-4cce-49c2-b41e-89241da85d4b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:14:01 GMT', 'content-type': 'application/json', 'content-length': '1115', 'connection': 'keep-alive', 'x-amzn-requestid': '7c5ac5fb-4cce-49c2-b41e-89241da85d4b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine type. Look for keywords. The page has "Receipt #", "Bill Code", "Carrier", "BOL", "Total Cost", "Payments". Looks like a carrier invoice? Terms: "Base Charge", "Convenience Fee", "Total Cost", "Payments". \'Receipt #\' suggests receipt/invoice. No HS code, no customs. Might be invoice. Could also be lumper receipt? But includes BOL and charges for loading? It says "Unloaders: 1". Could be a carrier invoice for loading/unloading services? But typical lumper receipt has PO, Door number, Date-Time, etc. This has BOL: blank? Actually BOL: maybe BOL number missing? Might be invoice for freight services. So classify as invoice. Also check if continuation? No.\n\nThus output: page_no: 1, doc_type: "invoice". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2774, 'outputTokens': 209, 'totalTokens': 2983}, 'metrics': {'latencyMs': 1181}}
2025-09-24 18:44:01,462 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/99c2117e_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:44:01,488 - INFO - 

OLQ7TIWW6EVTC6BXA1II.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:44:01,488 - INFO - 

✓ Saved result: output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:44:01,807 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/99c2117e_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:44:02,347 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b7071123-f1ec-4f75-bc2a-4b316a270f0c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:14:02 GMT', 'content-type': 'application/json', 'content-length': '2007', 'connection': 'keep-alive', 'x-amzn-requestid': 'b7071123-f1ec-4f75-bc2a-4b316a270f0c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1 content. We need to analyze.\n\nDocument: It looks like a receipt or invoice from Capstone Logistics. Contains Receipt #, Bill Code, Carrier, etc. There is a PO: 21927, and charges: Base Charge, Convenience Fee, total cost. It looks like a carrier invoice, perhaps? But it also includes Purchase Orders, Vendor, etc. It includes billed charges, payments. No customs, no weight, no scale, no POD.\n\nWe need to differentiate between invoice vs comm_invoice vs lumper_receipt.\n\nIt says "Receipt #:" and "Bill Code:" and "Carrier:" and "Total Cost:" and "Payments:".\n\nIt also includes a PO: 21927. But it also lists "Purchase Orders Vendor 21927" etc. The content is like a bill of "capstone" for a carrier service. Likely a carrier invoice.\n\nCheck for lumper_receipt: lumper receipts have PO, door number, date-time, initial pallets, finished pallets, service charge, etc. This could be lumper receipt? Actually lumper receipts often are for unloading labor, with PO, door number, etc. But this includes total cost and includes payment details. It also includes "Base Charge" and "Convenience Fee", not typical lumper. But lumper receipt might include "Base Charge"? Not sure.\n\ncomm_invoice would have HS or HTS codes, customs value, etc. This has none. So not comm_invoice.\n\nThus likely "invoice".\n\nBut check if this is a combined_carrier_documents? It only shows one page, with no multi headers. So just "invoice".\n\nThus output: page 1: doc_type: "invoice".\n\nWe need to output via function call classify_logistics_doc_type with array of documents: {page_no:1, doc_type:"invoice"}.\n\nReturn that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2792, 'outputTokens': 403, 'totalTokens': 3195}, 'metrics': {'latencyMs': 1740}}
2025-09-24 18:44:02,348 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/019c241e_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:44:02,370 - INFO - 

KDFG6JJAZV41YZP4TEGN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:44:02,370 - INFO - 

✓ Saved result: output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:44:02,688 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7a7d8fcd-1ed8-48a6-8a2a-9864e7dd89d8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:14:02 GMT', 'content-type': 'application/json', 'content-length': '1194', 'connection': 'keep-alive', 'x-amzn-requestid': '7a7d8fcd-1ed8-48a6-8a2a-9864e7dd89d8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. The text: "Receipt number 128521 CAPE STONE LUMPERS, LLC ... GENERAL RECEIPT ... CASHIER NAME BILL S. ... CUSTOMER NAME RCH TRUCKING ... Item(s) Quantity Price ... 5plts into12 ********* 140.00 ... SUBTOTAL 140.00 TOTAL 140.00 USD ... Payment Method: Cash ...". This looks like a receipt for labor by a lumpers company. This matches lumper_receipt definition: "Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount". Here we have item(s) 5plts, quantity, price, total. So lumper_receipt. There\'s no invoice header. It\'s a general receipt. So classify as lumper_receipt.\n\nThus output a tool call: classify_logistics_doc_type with documents array: page_no:1, doc_type:"lumper_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2662, 'outputTokens': 238, 'totalTokens': 2900}, 'metrics': {'latencyMs': 1230}}
2025-09-24 18:44:02,689 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/019c241e_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:44:02,690 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6a456ee3_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:44:02,707 - INFO - 

V3BCW6BW9XVKKO6WY2YJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:44:02,707 - INFO - 

✓ Saved result: output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:44:02,792 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b0b9b4d4-56db-4352-a68e-5c28bc1551be', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:14:02 GMT', 'content-type': 'application/json', 'content-length': '1161', 'connection': 'keep-alive', 'x-amzn-requestid': 'b0b9b4d4-56db-4352-a68e-5c28bc1551be'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Determine doc_type. Look at keywords: "RECEIPT#", "Payment", "Carrier", "Total Due". This looks like a receipt or invoice. But check definition: invoice – Bill issued by a carrier for goods being transported. Keywords: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc. The page has "RECEIPT#" but also "Total Due: $200.00" and "Payment: $200.00". It is a receipt from a carrier? It says "Carrier: RANGE TRUCKING". This looks like a fuel receipt? No. It is a carrier invoice for transport. So doc_type likely "invoice". It\'s not a commercial invoice because no HS codes etc. Not lumper receipt because no lumper service. So classify as "invoice". Since only one page, return list with one object: page_no 1, doc_type "invoice". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2756, 'outputTokens': 224, 'totalTokens': 2980}, 'metrics': {'latencyMs': 1195}}
2025-09-24 18:44:02,793 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/27801a29_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:44:03,019 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6a456ee3_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:44:03,038 - INFO - 

Y9K6Z2GSAP496UQLOOGU.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:44:03,038 - INFO - 

✓ Saved result: output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:44:03,405 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/27801a29_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:44:03,406 - INFO - 
📊 Processing Summary:
2025-09-24 18:44:03,406 - INFO -    Total files: 10
2025-09-24 18:44:03,406 - INFO -    Successful: 10
2025-09-24 18:44:03,406 - INFO -    Failed: 0
2025-09-24 18:44:03,406 - INFO -    Duration: 20.11 seconds
2025-09-24 18:44:03,406 - INFO -    Output directory: output
2025-09-24 18:44:03,406 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:44:03,406 - INFO -    📄 G0H0K1LRWDOG7LXAKKQ7.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:44:03,406 - INFO -    📄 GKNF55W2CF2JR6EBXMPX.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:44:03,406 - INFO -    📄 KDFG6JJAZV41YZP4TEGN.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:44:03,406 - INFO -    📄 MMNWUYGBLL1K5KSJBNOT.pdf: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:44:03,406 - INFO -    📄 NFMA1926AJ8R3TDZQALU.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 18:44:03,406 - INFO -    📄 O1YJBQBLYAU6D0SDDKAU.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:44:03,407 - INFO -    📄 OLQ7TIWW6EVTC6BXA1II.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:44:03,407 - INFO -    📄 V3BCW6BW9XVKKO6WY2YJ.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:44:03,407 - INFO -    📄 Y9K6Z2GSAP496UQLOOGU.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:44:03,407 - INFO -    📄 ZOBA55ELUA2GRE3UJM3I.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 18:44:03,407 - INFO - 
============================================================================================================================================
2025-09-24 18:44:03,407 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:44:03,407 - INFO - ============================================================================================================================================
2025-09-24 18:44:03,407 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:44:03,407 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:44:03,407 - INFO - G0H0K1LRWDOG7LXAKKQ7.pdf                           1      invoice                                  run1_G0H0K1LRWDOG7LXAKKQ7.json                                                  
2025-09-24 18:44:03,408 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:44:03,408 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:44:03,408 - INFO - 
2025-09-24 18:44:03,408 - INFO - GKNF55W2CF2JR6EBXMPX.pdf                           1      invoice                                  run1_GKNF55W2CF2JR6EBXMPX.json                                                  
2025-09-24 18:44:03,408 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:44:03,408 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:44:03,408 - INFO - 
2025-09-24 18:44:03,408 - INFO - KDFG6JJAZV41YZP4TEGN.pdf                           1      invoice                                  run1_KDFG6JJAZV41YZP4TEGN.json                                                  
2025-09-24 18:44:03,408 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:44:03,408 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:44:03,408 - INFO - 
2025-09-24 18:44:03,408 - INFO - MMNWUYGBLL1K5KSJBNOT.pdf                           1      fuel_receipt                             run1_MMNWUYGBLL1K5KSJBNOT.json                                                  
2025-09-24 18:44:03,408 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:44:03,408 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:44:03,408 - INFO - 
2025-09-24 18:44:03,408 - INFO - NFMA1926AJ8R3TDZQALU.pdf                           1      po                                       run1_NFMA1926AJ8R3TDZQALU.json                                                  
2025-09-24 18:44:03,408 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:44:03,408 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:44:03,408 - INFO - 
2025-09-24 18:44:03,408 - INFO - O1YJBQBLYAU6D0SDDKAU.pdf                           1      lumper_receipt                           run1_O1YJBQBLYAU6D0SDDKAU.json                                                  
2025-09-24 18:44:03,408 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:44:03,409 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:44:03,409 - INFO - 
2025-09-24 18:44:03,409 - INFO - OLQ7TIWW6EVTC6BXA1II.pdf                           1      invoice                                  run1_OLQ7TIWW6EVTC6BXA1II.json                                                  
2025-09-24 18:44:03,409 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:44:03,409 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:44:03,409 - INFO - 
2025-09-24 18:44:03,409 - INFO - V3BCW6BW9XVKKO6WY2YJ.pdf                           1      lumper_receipt                           run1_V3BCW6BW9XVKKO6WY2YJ.json                                                  
2025-09-24 18:44:03,409 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:44:03,409 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:44:03,409 - INFO - 
2025-09-24 18:44:03,409 - INFO - Y9K6Z2GSAP496UQLOOGU.jpeg                          1      invoice                                  run1_Y9K6Z2GSAP496UQLOOGU.json                                                  
2025-09-24 18:44:03,409 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:44:03,409 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:44:03,409 - INFO - 
2025-09-24 18:44:03,409 - INFO - ZOBA55ELUA2GRE3UJM3I.jpg                           1      other                                    run1_ZOBA55ELUA2GRE3UJM3I.json                                                  
2025-09-24 18:44:03,409 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:44:03,409 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:44:03,409 - INFO - 
2025-09-24 18:44:03,409 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:44:03,409 - INFO - Total entries: 10
2025-09-24 18:44:03,409 - INFO - ============================================================================================================================================
2025-09-24 18:44:03,409 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:44:03,409 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:44:03,410 - INFO -   1. G0H0K1LRWDOG7LXAKKQ7.pdf            Page 1   → invoice         | run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:44:03,410 - INFO -   2. GKNF55W2CF2JR6EBXMPX.pdf            Page 1   → invoice         | run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:44:03,410 - INFO -   3. KDFG6JJAZV41YZP4TEGN.pdf            Page 1   → invoice         | run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:44:03,410 - INFO -   4. MMNWUYGBLL1K5KSJBNOT.pdf            Page 1   → fuel_receipt    | run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:44:03,410 - INFO -   5. NFMA1926AJ8R3TDZQALU.pdf            Page 1   → po              | run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:44:03,410 - INFO -   6. O1YJBQBLYAU6D0SDDKAU.pdf            Page 1   → lumper_receipt  | run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:44:03,410 - INFO -   7. OLQ7TIWW6EVTC6BXA1II.pdf            Page 1   → invoice         | run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:44:03,410 - INFO -   8. V3BCW6BW9XVKKO6WY2YJ.pdf            Page 1   → lumper_receipt  | run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:44:03,410 - INFO -   9. Y9K6Z2GSAP496UQLOOGU.jpeg           Page 1   → invoice         | run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:44:03,410 - INFO -  10. ZOBA55ELUA2GRE3UJM3I.jpg            Page 1   → other           | run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:44:03,410 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:44:03,410 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 20.113809, 'processed_files': [{'filename': 'G0H0K1LRWDOG7LXAKKQ7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf'}, {'filename': 'GKNF55W2CF2JR6EBXMPX.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf'}, {'filename': 'KDFG6JJAZV41YZP4TEGN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf'}, {'filename': 'MMNWUYGBLL1K5KSJBNOT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf'}, {'filename': 'NFMA1926AJ8R3TDZQALU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf'}, {'filename': 'O1YJBQBLYAU6D0SDDKAU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf'}, {'filename': 'OLQ7TIWW6EVTC6BXA1II.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf'}, {'filename': 'V3BCW6BW9XVKKO6WY2YJ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf'}, {'filename': 'Y9K6Z2GSAP496UQLOOGU.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg'}, {'filename': 'ZOBA55ELUA2GRE3UJM3I.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg'}]}
