{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.40972021222114563, "Height": 0.9062283635139465, "Left": 0.27433934807777405, "Top": 0.057905592024326324}, "Polygon": [{"X": 0.2854083180427551, "Y": 0.057905592024326324}, {"X": 0.6294946670532227, "Y": 0.061070170253515244}, {"X": 0.6840595602989197, "Y": 0.9557186961174011}, {"X": 0.27433934807777405, "Y": 0.9641339778900146}]}, "Id": "c0c05b09-0da2-4670-b394-a4b6b6d8cd62", "Relationships": [{"Type": "CHILD", "Ids": ["35af9599-0887-4a50-921e-a0bc1b9e220d", "fd80aa8d-117a-40c3-acfc-1583e7e194fd", "51810428-36fa-4f96-a2d5-3f15f94395a9", "7bac3735-f8ec-42b3-90eb-1703014040d5", "ad955531-a10d-4fed-a2df-da391c9bf2d6", "1239d5ab-f4ac-4eb9-a9ba-25952d6e0f92", "9859ce75-efd9-4506-91e4-f9793822bcff", "cc16deba-65d6-4293-9082-482d00c074be", "62a19ca3-fec7-4d2a-9bf8-fcd009f150ef", "280a9f93-b00b-47c1-9b0c-4415829e544f", "f5a80e44-86bd-4c50-9d25-11d746820c5a", "d0fbeccb-5614-4adb-b7cf-03dec50f6b7f", "268e4d03-b2db-4f66-b4bc-4d5e5c4b2d92", "d731ed6a-888f-4e68-a6f9-ed50c311cd51", "428bf155-28a1-410d-bead-5270e30a0012", "7ea8d999-7077-46b9-9b27-465931f5c7c8", "fdeac1f3-df71-4399-aa11-a8bd5f9359c8", "55b2883b-c28c-4987-888e-2a1ed6be7591", "055d2ba7-9ecf-4f55-a271-ed8730f33ef0", "6ed1aba7-7374-4d3b-b96c-565aabd13f9b", "c447089c-9358-4212-bb80-e33fc6083974", "8accb716-e682-4e77-86fe-ea70c920cbf6", "1f19ea7e-5932-4de9-a596-9665bd735638", "7cf83b3b-0104-4cf9-be13-c656b4337808", "f59b5755-0025-48e4-b224-3ad700dbfda6", "ac2e13fb-4233-4ac9-b070-b83bd93cbe01", "aba7dd57-c333-4bf3-8e4a-cdc0f0c7d9fa", "e010b961-f342-4c97-9363-620adc188808", "7fa64316-bb4b-4097-8ce9-5ae489d70692", "ca1a42ef-e2cf-44cb-ae77-e450af2bb5f3", "dbe8b660-900d-4b5c-9661-1f91223d48fe", "0a121c4e-887e-4b3c-960e-d3b1855e7d76", "4932885e-e6b2-49b9-9bc9-23842babb631", "b909fc43-87d6-4f08-89c1-d3d57abc63ad", "ceca9747-6f19-4253-ab7e-477ea3225686"]}]}, {"BlockType": "LINE", "Confidence": 99.96392822265625, "Text": "EXXON EXPRESS PAY", "Geometry": {"BoundingBox": {"Width": 0.19592967629432678, "Height": 0.0136892544105649, "Left": 0.35224464535713196, "Top": 0.11890909820795059}, "Polygon": [{"X": 0.35224464535713196, "Y": 0.11890909820795059}, {"X": 0.5476458072662354, "Y": 0.12031807005405426}, {"X": 0.5481743216514587, "Y": 0.13259835541248322}, {"X": 0.3522692024707794, "Y": 0.13126537203788757}]}, "Id": "35af9599-0887-4a50-921e-a0bc1b9e220d", "Relationships": [{"Type": "CHILD", "Ids": ["82bb558a-2fa1-4992-bbe3-7835d9912d86", "eaf56498-8fee-4815-bbc6-8ebc2d56c7af", "d6a728e9-1b5d-4d42-bbe4-b4965df82787"]}]}, {"BlockType": "LINE", "Confidence": 99.98759460449219, "Text": "580 LONE STAR DRIVE", "Geometry": {"BoundingBox": {"Width": 0.2199183702468872, "Height": 0.014958161860704422, "Left": 0.34069108963012695, "Top": 0.14957499504089355}, "Polygon": [{"X": 0.3406970500946045, "Y": 0.14957499504089355}, {"X": 0.5599929094314575, "Y": 0.15093454718589783}, {"X": 0.5606094598770142, "Y": 0.16453316807746887}, {"X": 0.34069108963012695, "Y": 0.1632687747478485}]}, "Id": "fd80aa8d-117a-40c3-acfc-1583e7e194fd", "Relationships": [{"Type": "CHILD", "Ids": ["9c86cff3-fb4a-4ce1-ac0d-3b1f81ee01d5", "6bca2eaa-34c4-427a-8058-6e20af036e64", "7a6bdc81-75a4-47e4-a210-2d22ba2d8512", "e83b0327-5630-43e2-bd0b-288cb442a15a"]}]}, {"BlockType": "LINE", "Confidence": 99.9378890991211, "Text": "PARADISE QUICKSTOP #", "Geometry": {"BoundingBox": {"Width": 0.23329652845859528, "Height": 0.015428060665726662, "Left": 0.34091442823410034, "Top": 0.16489063203334808}, "Polygon": [{"X": 0.34091994166374207, "Y": 0.16489063203334808}, {"X": 0.573533833026886, "Y": 0.16621561348438263}, {"X": 0.5742109417915344, "Y": 0.180318683385849}, {"X": 0.34091442823410034, "Y": 0.17909881472587585}]}, "Id": "51810428-36fa-4f96-a2d5-3f15f94395a9", "Relationships": [{"Type": "CHILD", "Ids": ["6230e7b8-651b-4527-ac8d-2952484a5f10", "18daeebd-69f3-4acb-b559-641e2bd5c1de", "123ff453-d39f-45ae-a3e5-3b7211c77683"]}]}, {"BlockType": "LINE", "Confidence": 96.7322998046875, "Text": "XXXXXXXXX0001", "Geometry": {"BoundingBox": {"Width": 0.14988823235034943, "Height": 0.013753502629697323, "Left": 0.34017515182495117, "Top": 0.1823374480009079}, "Polygon": [{"X": 0.34018221497535706, "Y": 0.1823374480009079}, {"X": 0.4896678626537323, "Y": 0.18310314416885376}, {"X": 0.4900633990764618, "Y": 0.1960909515619278}, {"X": 0.34017515182495117, "Y": 0.19538749754428864}]}, "Id": "7bac3735-f8ec-42b3-90eb-1703014040d5", "Relationships": [{"Type": "CHILD", "Ids": ["7c1d04af-0bd4-4bc5-954c-b765ad45afcc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "HOOKS", "Geometry": {"BoundingBox": {"Width": 0.05814799666404724, "Height": 0.01160817313939333, "Left": 0.34088459610939026, "Top": 0.2001017928123474}, "Polygon": [{"X": 0.3408890664577484, "Y": 0.2001017928123474}, {"X": 0.3989010751247406, "Y": 0.2003650665283203}, {"X": 0.3990325927734375, "Y": 0.21170996129512787}, {"X": 0.34088459610939026, "Y": 0.21146780252456665}]}, "Id": "ad955531-a10d-4fed-a2df-da391c9bf2d6", "Relationships": [{"Type": "CHILD", "Ids": ["23b750fc-112e-49b6-b5a8-14dc14bd7529"]}]}, {"BlockType": "LINE", "Confidence": 98.2800521850586, "Text": ", TX", "Geometry": {"BoundingBox": {"Width": 0.043919797986745834, "Height": 0.014618240296840668, "Left": 0.48427653312683105, "Top": 0.19828884303569794}, "Polygon": [{"X": 0.48427653312683105, "Y": 0.19828884303569794}, {"X": 0.527645468711853, "Y": 0.1984892040491104}, {"X": 0.5281963348388672, "Y": 0.2129070907831192}, {"X": 0.4846987724304199, "Y": 0.212726891040802}]}, "Id": "1239d5ab-f4ac-4eb9-a9ba-25952d6e0f92", "Relationships": [{"Type": "CHILD", "Ids": ["c8d7eae1-5b49-447c-8f3c-2ad0b9fd012e", "50fdc8a8-ec8a-4442-95b2-c4f2ee7b3efd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "75561", "Geometry": {"BoundingBox": {"Width": 0.05529950186610222, "Height": 0.011499155312776566, "Left": 0.34109926223754883, "Top": 0.21658317744731903}, "Polygon": [{"X": 0.341103196144104, "Y": 0.21658317744731903}, {"X": 0.3962745666503906, "Y": 0.2168036550283432}, {"X": 0.39639878273010254, "Y": 0.2280823290348053}, {"X": 0.34109926223754883, "Y": 0.22788187861442566}]}, "Id": "9859ce75-efd9-4506-91e4-f9793822bcff", "Relationships": [{"Type": "CHILD", "Ids": ["8b4576b5-f82a-492e-8a61-7deecb6c00f6"]}]}, {"BlockType": "LINE", "Confidence": 99.86248779296875, "Text": "05/13/2025 590412430", "Geometry": {"BoundingBox": {"Width": 0.23583224415779114, "Height": 0.018295034766197205, "Left": 0.34093961119651794, "Top": 0.22842618823051453}, "Polygon": [{"X": 0.3409462571144104, "Y": 0.22842618823051453}, {"X": 0.5759363770484924, "Y": 0.22927378118038177}, {"X": 0.5767718553543091, "Y": 0.24672122299671173}, {"X": 0.34093961119651794, "Y": 0.24600692093372345}]}, "Id": "cc16deba-65d6-4293-9082-482d00c074be", "Relationships": [{"Type": "CHILD", "Ids": ["3cf1f4b9-bab9-4f0f-a107-0e5b70ff5dcf", "6b7b685c-978d-4a9f-a61d-943a30d7c77e"]}]}, {"BlockType": "LINE", "Confidence": 99.76971435546875, "Text": "08:56:09 AM", "Geometry": {"BoundingBox": {"Width": 0.12962239980697632, "Height": 0.014085020869970322, "Left": 0.34087786078453064, "Top": 0.24708795547485352}, "Polygon": [{"X": 0.3408832252025604, "Y": 0.24708795547485352}, {"X": 0.47014328837394714, "Y": 0.24747487902641296}, {"X": 0.47050026059150696, "Y": 0.26117298007011414}, {"X": 0.34087786078453064, "Y": 0.2608436048030853}]}, "Id": "62a19ca3-fec7-4d2a-9bf8-fcd009f150ef", "Relationships": [{"Type": "CHILD", "Ids": ["afc6b639-895b-4762-80bc-16974d0ebddf", "dc0afc20-e792-4b42-a86e-2dc22c3f86c1"]}]}, {"BlockType": "LINE", "Confidence": 75.34120178222656, "Text": "XXXXXXXXXXXXXXX8412", "Geometry": {"BoundingBox": {"Width": 0.2270612269639969, "Height": 0.0158492811024189, "Left": 0.3399864435195923, "Top": 0.2779603898525238}, "Polygon": [{"X": 0.33999523520469666, "Y": 0.2779603898525238}, {"X": 0.5663475394248962, "Y": 0.27840813994407654}, {"X": 0.567047655582428, "Y": 0.2938096523284912}, {"X": 0.3399864435195923, "Y": 0.2934763431549072}]}, "Id": "280a9f93-b00b-47c1-9b0c-4415829e544f", "Relationships": [{"Type": "CHILD", "Ids": ["f2355f25-2274-482d-b3cc-91315c10b01f"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "TCH Fuel", "Geometry": {"BoundingBox": {"Width": 0.09620760381221771, "Height": 0.012651937082409859, "Left": 0.3389332890510559, "Top": 0.29757392406463623}, "Polygon": [{"X": 0.33894309401512146, "Y": 0.29757392406463623}, {"X": 0.4349072277545929, "Y": 0.2977018654346466}, {"X": 0.4351409077644348, "Y": 0.31022587418556213}, {"X": 0.3389332890510559, "Y": 0.3101373612880707}]}, "Id": "f5a80e44-86bd-4c50-9d25-11d746820c5a", "Relationships": [{"Type": "CHILD", "Ids": ["11a0b38a-c0c7-46ea-bf6b-2a59c1fa4beb", "655a274a-02dd-4216-93b2-39d6a443c268"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "INVOICE 051693", "Geometry": {"BoundingBox": {"Width": 0.16597290337085724, "Height": 0.014313063584268093, "Left": 0.3418831527233124, "Top": 0.31263312697410583}, "Polygon": [{"X": 0.3418857455253601, "Y": 0.31263312697410583}, {"X": 0.5073849558830261, "Y": 0.31277182698249817}, {"X": 0.5078560709953308, "Y": 0.3269461691379547}, {"X": 0.3418831527233124, "Y": 0.3268848657608032}]}, "Id": "d0fbeccb-5614-4adb-b7cf-03dec50f6b7f", "Relationships": [{"Type": "CHILD", "Ids": ["094fa704-8d0e-4571-b6f0-8f3e5ee8e4cf", "19b3a834-ac01-4c93-b0cb-7e10fc6dbd68"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "AUTH 210836", "Geometry": {"BoundingBox": {"Width": 0.13214069604873657, "Height": 0.013407156802713871, "Left": 0.340076208114624, "Top": 0.33064645528793335}, "Polygon": [{"X": 0.340083509683609, "Y": 0.33064645528793335}, {"X": 0.4718702435493469, "Y": 0.3306788504123688}, {"X": 0.472216933965683, "Y": 0.3440278172492981}, {"X": 0.340076208114624, "Y": 0.34405359625816345}]}, "Id": "268e4d03-b2db-4f66-b4bc-4d5e5c4b2d92", "Relationships": [{"Type": "CHILD", "Ids": ["18f946e4-e6f0-4f77-b2af-9ae184d59eba", "48ef96dc-4229-4f0a-9dbd-2096cd8f69f3"]}]}, {"BlockType": "LINE", "Confidence": 99.9157943725586, "Text": "PUMP# 13", "Geometry": {"BoundingBox": {"Width": 0.09633312374353409, "Height": 0.013401183299720287, "Left": 0.3400532603263855, "Top": 0.36551135778427124}, "Polygon": [{"X": 0.3400605022907257, "Y": 0.3655981719493866}, {"X": 0.4361388683319092, "Y": 0.36551135778427124}, {"X": 0.436386376619339, "Y": 0.37878334522247314}, {"X": 0.3400532603263855, "Y": 0.3789125680923462}]}, "Id": "d731ed6a-888f-4e68-a6f9-ed50c311cd51", "Relationships": [{"Type": "CHILD", "Ids": ["6de90140-e8e5-4c53-9354-cdfedf5c5f7d", "998c2634-2348-44d4-8ed8-f65d6aac46a1"]}]}, {"BlockType": "LINE", "Confidence": 99.6583023071289, "Text": "Diesel 1", "Geometry": {"BoundingBox": {"Width": 0.09432636201381683, "Height": 0.012914023362100124, "Left": 0.3398655354976654, "Top": 0.4005368947982788}, "Polygon": [{"X": 0.33987289667129517, "Y": 0.40073058009147644}, {"X": 0.43396252393722534, "Y": 0.4005368947982788}, {"X": 0.43419191241264343, "Y": 0.4132172763347626}, {"X": 0.3398655354976654, "Y": 0.4134509265422821}]}, "Id": "428bf155-28a1-410d-bead-5270e30a0012", "Relationships": [{"Type": "CHILD", "Ids": ["f79cb226-2b05-4110-94e1-65524664dd3e", "4e4d15d0-e131-472b-ac29-3f7f70c66dcc"]}]}, {"BlockType": "LINE", "Confidence": 99.4345932006836, "Text": "24.616G", "Geometry": {"BoundingBox": {"Width": 0.0856003537774086, "Height": 0.013262216001749039, "Left": 0.4983149468898773, "Top": 0.39768263697624207}, "Polygon": [{"X": 0.4983149468898773, "Y": 0.3978503942489624}, {"X": 0.5832918882369995, "Y": 0.39768263697624207}, {"X": 0.5839152932167053, "Y": 0.41073980927467346}, {"X": 0.4987192153930664, "Y": 0.4109448492527008}]}, "Id": "7ea8d999-7077-46b9-9b27-465931f5c7c8", "Relationships": [{"Type": "CHILD", "Ids": ["bf58f8e8-0758-4a9f-a933-b9e0825d8439"]}]}, {"BlockType": "LINE", "Confidence": 95.77965545654297, "Text": "PRICE/GAL", "Geometry": {"BoundingBox": {"Width": 0.10867486149072647, "Height": 0.013879196718335152, "Left": 0.340284526348114, "Top": 0.4183305501937866}, "Polygon": [{"X": 0.34029123187065125, "Y": 0.4186173975467682}, {"X": 0.44867587089538574, "Y": 0.4183305501937866}, {"X": 0.4489594101905823, "Y": 0.43187353014945984}, {"X": 0.340284526348114, "Y": 0.43220973014831543}]}, "Id": "fdeac1f3-df71-4399-aa11-a8bd5f9359c8", "Relationships": [{"Type": "CHILD", "Ids": ["00c2b461-fc5c-4bba-b980-c853116c579a"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "$3.399", "Geometry": {"BoundingBox": {"Width": 0.07294952869415283, "Height": 0.014125720597803593, "Left": 0.5112459063529968, "Top": 0.4151552617549896}, "Polygon": [{"X": 0.5112459063529968, "Y": 0.4153398275375366}, {"X": 0.5835330486297607, "Y": 0.4151552617549896}, {"X": 0.5841954350471497, "Y": 0.42906248569488525}, {"X": 0.5117104649543762, "Y": 0.42928096652030945}]}, "Id": "55b2883b-c28c-4987-888e-2a1ed6be7591", "Relationships": [{"Type": "CHILD", "Ids": ["d66bf89c-5565-4b02-974e-e03d13c5f01b"]}]}, {"BlockType": "LINE", "Confidence": 99.5458984375, "Text": "FUEL TOTAL $ 83.67", "Geometry": {"BoundingBox": {"Width": 0.2466721087694168, "Height": 0.015452401712536812, "Left": 0.33888500928878784, "Top": 0.45088598132133484}, "Polygon": [{"X": 0.33889612555503845, "Y": 0.45180538296699524}, {"X": 0.5848717093467712, "Y": 0.45088598132133484}, {"X": 0.5855571031570435, "Y": 0.46529850363731384}, {"X": 0.33888500928878784, "Y": 0.4663383960723877}]}, "Id": "055d2ba7-9ecf-4f55-a271-ed8730f33ef0", "Relationships": [{"Type": "CHILD", "Ids": ["a54f48da-acf5-435b-bfaa-330bbeb3bc88", "bbc5c3cf-288f-4c7a-b85d-fe0f5ad13803", "8c3d9d9a-220a-4624-85a2-78ed545fa5ed", "dc262666-7eda-4fae-b76b-50c061977c7e"]}]}, {"BlockType": "LINE", "Confidence": 99.46717834472656, "Text": "TOTAL = $ 83.67", "Geometry": {"BoundingBox": {"Width": 0.20112298429012299, "Height": 0.015012252144515514, "Left": 0.38693174719810486, "Top": 0.5041924715042114}, "Polygon": [{"X": 0.38693174719810486, "Y": 0.5052961111068726}, {"X": 0.5873978734016418, "Y": 0.5041924715042114}, {"X": 0.5880547761917114, "Y": 0.****************}, {"X": 0.3870511054992676, "Y": 0.****************}]}, "Id": "6ed1aba7-7374-4d3b-b96c-565aabd13f9b", "Relationships": [{"Type": "CHILD", "Ids": ["8bd53014-1a81-475c-a88f-1efd922697b9", "4d31be00-617d-450a-8d6d-f8c1cf21f860", "8303c688-dc86-44c5-ad7c-c23d743951b3", "178381a3-0121-4589-9956-e4411f1aff91"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "CREDIT", "Geometry": {"BoundingBox": {"Width": 0.07523081451654434, "Height": 0.013127401471138, "Left": 0.3370814025402069, "Top": 0.543509304523468}, "Polygon": [{"X": 0.3370952606201172, "Y": 0.****************}, {"X": 0.****************, "Y": 0.543509304523468}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.3370814025402069, "Y": 0.****************}]}, "Id": "c447089c-9358-4212-bb80-e33fc6083974", "Relationships": [{"Type": "CHILD", "Ids": ["10f9fb2c-19e6-44e9-b0a2-29a6904a535a"]}]}, {"BlockType": "LINE", "Confidence": 97.734375, "Text": "$ 83.67", "Geometry": {"BoundingBox": {"Width": 0.1006607785820961, "Height": 0.01505692582577467, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.589262068271637, "Y": 0.****************}, {"X": 0.5899443030357361, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "8accb716-e682-4e77-86fe-ea70c920cbf6", "Relationships": [{"Type": "CHILD", "Ids": ["7bb6db53-4435-4652-af08-eccc1e43d19e", "fbb03dde-121c-43bd-850f-55d1458b7a6f"]}]}, {"BlockType": "LINE", "Confidence": 99.05462646484375, "Text": "Customer-activated Purchase/Capture", "Geometry": {"BoundingBox": {"Width": 0.22737643122673035, "Height": 0.018164247274398804, "Left": 0.33430033922195435, "Top": 0.5969215631484985}, "Polygon": [{"X": 0.33432653546333313, "Y": 0.5988645553588867}, {"X": 0.5610061287879944, "Y": 0.5969215631484985}, {"X": 0.5616768002510071, "Y": 0.6130155324935913}, {"X": 0.33430033922195435, "Y": 0.615085780620575}]}, "Id": "1f19ea7e-5932-4de9-a596-9665bd735638", "Relationships": [{"Type": "CHILD", "Ids": ["3fcac71d-e310-4f30-aed7-d9d37cce0a59", "0268446e-14f5-4621-a09f-773d6a390560"]}]}, {"BlockType": "LINE", "Confidence": 77.84571838378906, "Text": "Site #: 0000008004816757", "Geometry": {"BoundingBox": {"Width": 0.15646947920322418, "Height": 0.01525123044848442, "Left": 0.3343777656555176, "Top": 0.6180891990661621}, "Polygon": [{"X": 0.3343997895717621, "Y": 0.6195329427719116}, {"X": 0.490461528301239, "Y": 0.6180891990661621}, {"X": 0.49084722995758057, "Y": 0.6318219304084778}, {"X": 0.3343777656555176, "Y": 0.6333404779434204}]}, "Id": "7cf83b3b-0104-4cf9-be13-c656b4337808", "Relationships": [{"Type": "CHILD", "Ids": ["a6f490e4-484c-452a-98f6-0df72ee85534", "e149f545-f9ea-4ba7-99c8-669108bbc6b8", "db1d8b83-44a5-4bad-822a-733b76efc449"]}]}, {"BlockType": "LINE", "Confidence": 99.80229949951172, "Text": "Shift Number 1", "Geometry": {"BoundingBox": {"Width": 0.09120272099971771, "Height": 0.015171535313129425, "Left": 0.3341400921344757, "Top": 0.6381615400314331}, "Polygon": [{"X": 0.3341633975505829, "Y": 0.6390613913536072}, {"X": 0.42512089014053345, "Y": 0.6381615400314331}, {"X": 0.4253427982330322, "Y": 0.6523879766464233}, {"X": 0.3341400921344757, "Y": 0.6533330678939819}]}, "Id": "f59b5755-0025-48e4-b224-3ad700dbfda6", "Relationships": [{"Type": "CHILD", "Ids": ["ad0846f7-b73c-470e-ad23-b19886ed3ba5", "ad0a4a4b-cea9-459d-b349-8f9bbbe36449", "01764dae-e08a-44ef-a321-5355ed2bbe53"]}]}, {"BlockType": "LINE", "Confidence": 99.86713409423828, "Text": "Sequence Number 58096", "Geometry": {"BoundingBox": {"Width": 0.13810336589813232, "Height": 0.018485039472579956, "Left": 0.33442243933677673, "Top": 0.6570566892623901}, "Polygon": [{"X": 0.33444923162460327, "Y": 0.6585065722465515}, {"X": 0.47211188077926636, "Y": 0.6570566892623901}, {"X": 0.47252580523490906, "Y": 0.6740097999572754}, {"X": 0.33442243933677673, "Y": 0.6755416989326477}]}, "Id": "ac2e13fb-4233-4ac9-b070-b83bd93cbe01", "Relationships": [{"Type": "CHILD", "Ids": ["35837ecb-1708-4429-aabe-5073828c6818", "d515d03e-0318-413a-8e90-9439f3774bbc", "f3491e11-c96d-416f-94b4-e0a4f4697ddd"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Swiped", "Geometry": {"BoundingBox": {"Width": 0.04065830633044243, "Height": 0.01744830794632435, "Left": 0.33436277508735657, "Top": 0.678260326385498}, "Polygon": [{"X": 0.3343895971775055, "Y": 0.6787141561508179}, {"X": 0.37491855025291443, "Y": 0.678260326385498}, {"X": 0.3750210702419281, "Y": 0.6952307224273682}, {"X": 0.33436277508735657, "Y": 0.6957086324691772}]}, "Id": "aba7dd57-c333-4bf3-8e4a-cdc0f0c7d9fa", "Relationships": [{"Type": "CHILD", "Ids": ["dec446bc-7523-486c-a2d5-4057276b78e7"]}]}, {"BlockType": "LINE", "Confidence": 85.84492492675781, "Text": "APPROVED 210836", "Geometry": {"BoundingBox": {"Width": 0.10639365017414093, "Height": 0.01574724353849888, "Left": 0.3344919979572296, "Top": 0.696584165096283}, "Polygon": [{"X": 0.33451446890830994, "Y": 0.6978389024734497}, {"X": 0.44062095880508423, "Y": 0.696584165096283}, {"X": 0.44088566303253174, "Y": 0.7110225558280945}, {"X": 0.3344919979572296, "Y": 0.7123313546180725}]}, "Id": "e010b961-f342-4c97-9363-620adc188808", "Relationships": [{"Type": "CHILD", "Ids": ["6dc89071-26ca-404d-8300-b39e90ec25aa", "1a69f3d8-2d5c-4ecf-aabe-f3e5bb0b3b49"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Customer Information", "Geometry": {"BoundingBox": {"Width": 0.13265672326087952, "Height": 0.01622610352933407, "Left": 0.3346213400363922, "Top": 0.716018557548523}, "Polygon": [{"X": 0.33464351296424866, "Y": 0.7176693081855774}, {"X": 0.466941773891449, "Y": 0.716018557548523}, {"X": 0.46727806329727173, "Y": 0.7305259108543396}, {"X": 0.3346213400363922, "Y": 0.7322446703910828}]}, "Id": "7fa64316-bb4b-4097-8ce9-5ae489d70692", "Relationships": [{"Type": "CHILD", "Ids": ["f5bde418-dd39-4326-b4a4-d69217634013", "e16157d0-95f5-4c36-81a8-5c5c22c4845b"]}]}, {"BlockType": "LINE", "Confidence": 90.90547943115234, "Text": "Name: HP DISTRIBUTION", "Geometry": {"BoundingBox": {"Width": 0.14584209024906158, "Height": 0.016375910490751266, "Left": 0.33483412861824036, "Top": 0.7362344861030579}, "Polygon": [{"X": 0.33485546708106995, "Y": 0.7381472587585449}, {"X": 0.4803081750869751, "Y": 0.7362344861030579}, {"X": 0.48067623376846313, "Y": 0.7506231069564819}, {"X": 0.33483412861824036, "Y": 0.7526103854179382}]}, "Id": "ca1a42ef-e2cf-44cb-ae77-e450af2bb5f3", "Relationships": [{"Type": "CHILD", "Ids": ["2347c474-1ca2-4c09-a7f0-f90b79405f13", "2fc1020e-cf39-419b-b6a2-cb0f78b83aeb", "cdd6226c-0d68-47e4-a790-f5de7786de29"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "City: OLATHE", "Geometry": {"BoundingBox": {"Width": 0.08076298236846924, "Height": 0.019381089136004448, "Left": 0.3347426652908325, "Top": 0.7571379542350769}, "Polygon": [{"X": 0.3347697854042053, "Y": 0.7582496404647827}, {"X": 0.41526100039482117, "Y": 0.7571379542350769}, {"X": 0.41550564765930176, "Y": 0.7753550410270691}, {"X": 0.3347426652908325, "Y": 0.7765190005302429}]}, "Id": "dbe8b660-900d-4b5c-9661-1f91223d48fe", "Relationships": [{"Type": "CHILD", "Ids": ["048d7cbc-a23d-4f28-a50f-8b36747de983", "d8fbd224-9008-4100-8d00-63642402ee7e"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "State: KS", "Geometry": {"BoundingBox": {"Width": 0.06116371974349022, "Height": 0.016178609803318977, "Left": 0.33465874195098877, "Top": 0.7777979373931885}, "Polygon": [{"X": 0.3346816301345825, "Y": 0.7786812782287598}, {"X": 0.3956734836101532, "Y": 0.7777979373931885}, {"X": 0.3958224654197693, "Y": 0.7930599451065063}, {"X": 0.33465874195098877, "Y": 0.7939765453338623}]}, "Id": "0a121c4e-887e-4b3c-960e-d3b1855e7d76", "Relationships": [{"Type": "CHILD", "Ids": ["2ed37c21-8784-493e-bddd-820bd961ce0b", "bb880def-b04f-4cfd-8b2b-374d315ea90b"]}]}, {"BlockType": "LINE", "Confidence": 99.01974487304688, "Text": "Acc.: 132643", "Geometry": {"BoundingBox": {"Width": 0.08208019286394119, "Height": 0.016688603907823563, "Left": 0.3342275619506836, "Top": 0.7981279492378235}, "Polygon": [{"X": 0.3342517912387848, "Y": 0.7993690371513367}, {"X": 0.4161001145839691, "Y": 0.7981279492378235}, {"X": 0.4163077473640442, "Y": 0.8135303258895874}, {"X": 0.3342275619506836, "Y": 0.8148165941238403}]}, "Id": "4932885e-e6b2-49b9-9bc9-23842babb631", "Relationships": [{"Type": "CHILD", "Ids": ["c2077257-74b8-458e-b2ea-78c066ee3f7d", "ec287cf9-67c7-436d-a35d-d70d76cf3319"]}]}, {"BlockType": "LINE", "Confidence": 99.37728881835938, "Text": "Odometer: 274352", "Geometry": {"BoundingBox": {"Width": 0.10906081646680832, "Height": 0.018117502331733704, "Left": 0.3341817855834961, "Top": 0.8178001046180725}, "Polygon": [{"X": 0.33420756459236145, "Y": 0.819520890712738}, {"X": 0.44294288754463196, "Y": 0.8178001046180725}, {"X": 0.443242609500885, "Y": 0.8341329097747803}, {"X": 0.3341817855834961, "Y": 0.835917592048645}]}, "Id": "b909fc43-87d6-4f08-89c1-d3d57abc63ad", "Relationships": [{"Type": "CHILD", "Ids": ["addc4bd3-8973-45df-a1ea-4f43fe160d4f", "2d27dcac-b5cc-4ff2-9ec0-9e7a79a8a68d"]}]}, {"BlockType": "LINE", "Confidence": 99.30497741699219, "Text": "Trip #: 4408", "Geometry": {"BoundingBox": {"Width": 0.08312000334262848, "Height": 0.02016131393611431, "Left": 0.3340495526790619, "Top": 0.8402074575424194}, "Polygon": [{"X": 0.33407941460609436, "Y": 0.841578483581543}, {"X": 0.4169161319732666, "Y": 0.8402074575424194}, {"X": 0.41716957092285156, "Y": 0.8589417338371277}, {"X": 0.3340495526790619, "Y": 0.8603687882423401}]}, "Id": "ceca9747-6f19-4253-ab7e-477ea3225686", "Relationships": [{"Type": "CHILD", "Ids": ["72044229-6347-4179-8a44-3a0857dd24c8", "d729203c-1640-479e-b176-975835d7b802", "64b841c3-50e9-4aad-993d-d7ae2b9ea227"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "EXXON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05786612257361412, "Height": 0.011695860885083675, "Left": 0.35224661231040955, "Top": 0.11989569664001465}, "Polygon": [{"X": 0.35224661231040955, "Y": 0.11989569664001465}, {"X": 0.4099535346031189, "Y": 0.12030992656946182}, {"X": 0.41011273860931396, "Y": 0.1315915584564209}, {"X": 0.3522690534591675, "Y": 0.13119785487651825}]}, "Id": "82bb558a-2fa1-4992-bbe3-7835d9912d86"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "EXPRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08072920143604279, "Height": 0.011625202372670174, "Left": 0.421574205160141, "Top": 0.12066103518009186}, "Polygon": [{"X": 0.421574205160141, "Y": 0.12066103518009186}, {"X": 0.5019340515136719, "Y": 0.12123716622591019}, {"X": 0.502303421497345, "Y": 0.13228623569011688}, {"X": 0.4217575192451477, "Y": 0.13173818588256836}]}, "Id": "eaf56498-8fee-4815-bbc6-8ebc2d56c7af"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03451504185795784, "Height": 0.011126633733510971, "Left": 0.5135990381240845, "Top": 0.12007256597280502}, "Polygon": [{"X": 0.5135990381240845, "Y": 0.12007256597280502}, {"X": 0.5476458072662354, "Y": 0.12031807005405426}, {"X": 0.5481141209602356, "Y": 0.1311991959810257}, {"X": 0.5139899849891663, "Y": 0.1309654414653778}]}, "Id": "d6a728e9-1b5d-4d42-bbe4-b4965df82787"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "580", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03469226881861687, "Height": 0.011775407940149307, "Left": 0.34069132804870605, "Top": 0.15115471184253693}, "Polygon": [{"X": 0.3406963646411896, "Y": 0.15115471184253693}, {"X": 0.3753051161766052, "Y": 0.15136748552322388}, {"X": 0.375383585691452, "Y": 0.16293013095855713}, {"X": 0.34069132804870605, "Y": 0.16273005306720734}]}, "Id": "9c86cff3-fb4a-4ce1-ac0d-3b1f81ee01d5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LONE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04550861567258835, "Height": 0.011277410201728344, "Left": 0.3877868354320526, "Top": 0.15252378582954407}, "Polygon": [{"X": 0.3877868354320526, "Y": 0.15252378582954407}, {"X": 0.43308818340301514, "Y": 0.15280066430568695}, {"X": 0.43329542875289917, "Y": 0.1638011932373047}, {"X": 0.3878902792930603, "Y": 0.16354013979434967}]}, "Id": "6bca2eaa-34c4-427a-8058-6e20af036e64"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "STAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04602464661002159, "Height": 0.010982183739542961, "Left": 0.4454279839992523, "Top": 0.15203474462032318}, "Polygon": [{"X": 0.4454279839992523, "Y": 0.15203474462032318}, {"X": 0.491121381521225, "Y": 0.15231528878211975}, {"X": 0.4914526343345642, "Y": 0.1630169153213501}, {"X": 0.44565755128860474, "Y": 0.1627519428730011}]}, "Id": "7a6bdc81-75a4-47e4-a210-2d22ba2d8512"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DRIVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05726071447134018, "Height": 0.011812184937298298, "Left": 0.5032517910003662, "Top": 0.15058277547359467}, "Polygon": [{"X": 0.5032517910003662, "Y": 0.15058277547359467}, {"X": 0.5599929094314575, "Y": 0.15093454718589783}, {"X": 0.5605124831199646, "Y": 0.1623949557542801}, {"X": 0.503636360168457, "Y": 0.16206392645835876}]}, "Id": "e83b0327-5630-43e2-bd0b-288cb442a15a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PARADISE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09284334629774094, "Height": 0.011851252987980843, "Left": 0.34091442823410034, "Top": 0.16773302853107452}, "Polygon": [{"X": 0.3409188389778137, "Y": 0.16773302853107452}, {"X": 0.433543860912323, "Y": 0.16825196146965027}, {"X": 0.4337577819824219, "Y": 0.17958427965641022}, {"X": 0.34091442823410034, "Y": 0.17909881472587585}]}, "Id": "6230e7b8-651b-4527-ac8d-2952484a5f10"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "QUICKSTOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10426542162895203, "Height": 0.012823943980038166, "Left": 0.4456956684589386, "Top": 0.16623571515083313}, "Polygon": [{"X": 0.4456956684589386, "Y": 0.16623571515083313}, {"X": 0.549435019493103, "Y": 0.16682405769824982}, {"X": 0.5499610900878906, "Y": 0.17905965447425842}, {"X": 0.44595852494239807, "Y": 0.17851194739341736}]}, "Id": "18daeebd-69f3-4acb-b559-641e2bd5c1de"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013212187215685844, "Height": 0.010819819755852222, "Left": 0.5608376860618591, "Top": 0.1661432832479477}, "Polygon": [{"X": 0.5608376860618591, "Y": 0.1661432832479477}, {"X": 0.573533833026886, "Y": 0.16621561348438263}, {"X": 0.5740498304367065, "Y": 0.17696310579776764}, {"X": 0.5613254904747009, "Y": 0.1768951565027237}]}, "Id": "123ff453-d39f-45ae-a3e5-3b7211c77683"}, {"BlockType": "WORD", "Confidence": 96.7322998046875, "Text": "XXXXXXXXX0001", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14988823235034943, "Height": 0.013753502629697323, "Left": 0.34017515182495117, "Top": 0.1823374480009079}, "Polygon": [{"X": 0.34018221497535706, "Y": 0.1823374480009079}, {"X": 0.4896678626537323, "Y": 0.18310314416885376}, {"X": 0.4900633990764618, "Y": 0.1960909515619278}, {"X": 0.34017515182495117, "Y": 0.19538749754428864}]}, "Id": "7c1d04af-0bd4-4bc5-954c-b765ad45afcc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "HOOKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05814799666404724, "Height": 0.01160817313939333, "Left": 0.34088459610939026, "Top": 0.2001017928123474}, "Polygon": [{"X": 0.3408890664577484, "Y": 0.2001017928123474}, {"X": 0.3989010751247406, "Y": 0.2003650665283203}, {"X": 0.3990325927734375, "Y": 0.21170996129512787}, {"X": 0.34088459610939026, "Y": 0.21146780252456665}]}, "Id": "23b750fc-112e-49b6-b5a8-14dc14bd7529"}, {"BlockType": "WORD", "Confidence": 98.35698699951172, "Text": ",", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0050649503245949745, "Height": 0.006658837199211121, "Left": 0.4845046103000641, "Top": 0.20608823001384735}, "Polygon": [{"X": 0.4845046103000641, "Y": 0.20608823001384735}, {"X": 0.4893687963485718, "Y": 0.2061094492673874}, {"X": 0.4895695745944977, "Y": 0.21274706721305847}, {"X": 0.4846987724304199, "Y": 0.212726891040802}]}, "Id": "c8d7eae1-5b49-447c-8f3c-2ad0b9fd012e"}, {"BlockType": "WORD", "Confidence": 98.203125, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024155760183930397, "Height": 0.010927719064056873, "Left": 0.5039030313491821, "Top": 0.1983795166015625}, "Polygon": [{"X": 0.5039030313491821, "Y": 0.1983795166015625}, {"X": 0.527645468711853, "Y": 0.1984892040491104}, {"X": 0.5280587673187256, "Y": 0.2093072384595871}, {"X": 0.5042635202407837, "Y": 0.20920582115650177}]}, "Id": "50fdc8a8-ec8a-4442-95b2-c4f2ee7b3efd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "75561", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05529950186610222, "Height": 0.011499155312776566, "Left": 0.34109926223754883, "Top": 0.21658317744731903}, "Polygon": [{"X": 0.341103196144104, "Y": 0.21658317744731903}, {"X": 0.3962745666503906, "Y": 0.2168036550283432}, {"X": 0.39639878273010254, "Y": 0.2280823290348053}, {"X": 0.34109926223754883, "Y": 0.22788187861442566}]}, "Id": "8b4576b5-f82a-492e-8a61-7deecb6c00f6"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "05/13/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11801565438508987, "Height": 0.014721178449690342, "Left": 0.34093961119651794, "Top": 0.2316431999206543}, "Polygon": [{"X": 0.3409450352191925, "Y": 0.2316431999206543}, {"X": 0.4586150646209717, "Y": 0.2320551723241806}, {"X": 0.4589552581310272, "Y": 0.24636436998844147}, {"X": 0.34093961119651794, "Y": 0.24600692093372345}]}, "Id": "3cf1f4b9-bab9-4f0f-a107-0e5b70ff5dcf"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "590412430", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10588514804840088, "Height": 0.01363923866301775, "Left": 0.4706861674785614, "Top": 0.22889414429664612}, "Polygon": [{"X": 0.4706861674785614, "Y": 0.22889414429664612}, {"X": 0.5759363770484924, "Y": 0.22927378118038177}, {"X": 0.5765712857246399, "Y": 0.2425333857536316}, {"X": 0.4710356891155243, "Y": 0.2421990931034088}]}, "Id": "6b7b685c-978d-4a9f-a61d-943a30d7c77e"}, {"BlockType": "WORD", "Confidence": 99.57848358154297, "Text": "08:56:09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09398981928825378, "Height": 0.012804435566067696, "Left": 0.34087786078453064, "Top": 0.24827800691127777}, "Polygon": [{"X": 0.3408827483654022, "Y": 0.24827800691127777}, {"X": 0.4346323013305664, "Y": 0.24855495989322662}, {"X": 0.4348676800727844, "Y": 0.2610824406147003}, {"X": 0.34087786078453064, "Y": 0.2608436048030853}]}, "Id": "afc6b639-895b-4762-80bc-16974d0ebddf"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023156220093369484, "Height": 0.01107764057815075, "Left": 0.44727396965026855, "Top": 0.24740642309188843}, "Polygon": [{"X": 0.44727396965026855, "Y": 0.24740642309188843}, {"X": 0.47014328837394714, "Y": 0.24747487902641296}, {"X": 0.4704301953315735, "Y": 0.2584840655326843}, {"X": 0.4475095272064209, "Y": 0.258423775434494}]}, "Id": "dc0afc20-e792-4b42-a86e-2dc22c3f86c1"}, {"BlockType": "WORD", "Confidence": 75.34120178222656, "Text": "XXXXXXXXXXXXXXX8412", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2270612269639969, "Height": 0.0158492811024189, "Left": 0.3399864435195923, "Top": 0.2779603898525238}, "Polygon": [{"X": 0.33999523520469666, "Y": 0.2779603898525238}, {"X": 0.5663475394248962, "Y": 0.27840813994407654}, {"X": 0.567047655582428, "Y": 0.2938096523284912}, {"X": 0.3399864435195923, "Y": 0.2934763431549072}]}, "Id": "f2355f25-2274-482d-b3cc-91315c10b01f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "TCH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03643665090203285, "Height": 0.011666781269013882, "Left": 0.3389332890510559, "Top": 0.2985040843486786}, "Polygon": [{"X": 0.33894234895706177, "Y": 0.2985040843486786}, {"X": 0.37529346346855164, "Y": 0.29855144023895264}, {"X": 0.37536993622779846, "Y": 0.31017088890075684}, {"X": 0.3389332890510559, "Y": 0.3101373612880707}]}, "Id": "11a0b38a-c0c7-46ea-bf6b-2a59c1fa4beb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fuel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04685355722904205, "Height": 0.011490339413285255, "Left": 0.3882668912410736, "Top": 0.2976396977901459}, "Polygon": [{"X": 0.3882668912410736, "Y": 0.2976396977901459}, {"X": 0.4349072277545929, "Y": 0.2977018654346466}, {"X": 0.43512046337127686, "Y": 0.3091300427913666}, {"X": 0.3883723318576813, "Y": 0.30908533930778503}]}, "Id": "655a274a-02dd-4216-93b2-39d6a443c268"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "INVOICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08165399730205536, "Height": 0.012370078824460506, "Left": 0.3418831527233124, "Top": 0.3145449459552765}, "Polygon": [{"X": 0.34188541769981384, "Y": 0.3145449459552765}, {"X": 0.42333707213401794, "Y": 0.314608097076416}, {"X": 0.42353716492652893, "Y": 0.32691502571105957}, {"X": 0.3418831527233124, "Y": 0.3268848657608032}]}, "Id": "094fa704-8d0e-4571-b6f0-8f3e5ee8e4cf"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "051693", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07181643694639206, "Height": 0.012793551199138165, "Left": 0.43599173426628113, "Top": 0.3127119839191437}, "Polygon": [{"X": 0.43599173426628113, "Y": 0.3127119839191437}, {"X": 0.5073849558830261, "Y": 0.31277182698249817}, {"X": 0.5078082084655762, "Y": 0.3255055248737335}, {"X": 0.43623194098472595, "Y": 0.32547569274902344}]}, "Id": "19b3a834-ac01-4c93-b0cb-7e10fc6dbd68"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04724903404712677, "Height": 0.012205393053591251, "Left": 0.340076208114624, "Top": 0.3318482041358948}, "Polygon": [{"X": 0.3400828540325165, "Y": 0.3318482041358948}, {"X": 0.38721635937690735, "Y": 0.33185791969299316}, {"X": 0.387325257062912, "Y": 0.34404438734054565}, {"X": 0.340076208114624, "Y": 0.34405359625816345}]}, "Id": "18f946e4-e6f0-4f77-b2af-9ae184d59eba"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "210836", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07202047854661942, "Height": 0.012336391024291515, "Left": 0.40016940236091614, "Top": 0.33066120743751526}, "Polygon": [{"X": 0.40016940236091614, "Y": 0.33066120743751526}, {"X": 0.4718702435493469, "Y": 0.3306788504123688}, {"X": 0.47218987345695496, "Y": 0.3429860472679138}, {"X": 0.40031179785728455, "Y": 0.34299761056900024}]}, "Id": "48ef96dc-4229-4f0a-9dbd-2096cd8f69f3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "PUMP#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061068080365657806, "Height": 0.01278444193303585, "Left": 0.3400532603263855, "Top": 0.3661281168460846}, "Polygon": [{"X": 0.34006020426750183, "Y": 0.3661843240261078}, {"X": 0.40097370743751526, "Y": 0.3661281168460846}, {"X": 0.4011213481426239, "Y": 0.3788306415081024}, {"X": 0.3400532603263855, "Y": 0.3789125680923462}]}, "Id": "6de90140-e8e5-4c53-9354-cdfedf5c5f7d"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "13", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023304974660277367, "Height": 0.01155840139836073, "Left": 0.4130488932132721, "Top": 0.36551135778427124}, "Polygon": [{"X": 0.4130488932132721, "Y": 0.3655322194099426}, {"X": 0.4361388683319092, "Y": 0.36551135778427124}, {"X": 0.436353862285614, "Y": 0.3770400583744049}, {"X": 0.4132108688354492, "Y": 0.37706977128982544}]}, "Id": "998c2634-2348-44d4-8ed8-f65d6aac46a1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07240243256092072, "Height": 0.012869004160165787, "Left": 0.3398655354976654, "Top": 0.4005819261074066}, "Polygon": [{"X": 0.33987289667129517, "Y": 0.40073058009147644}, {"X": 0.41209349036216736, "Y": 0.4005819261074066}, {"X": 0.4122679829597473, "Y": 0.41327157616615295}, {"X": 0.3398655354976654, "Y": 0.4134509265422821}]}, "Id": "f79cb226-2b05-4110-94e1-65524664dd3e"}, {"BlockType": "WORD", "Confidence": 99.32637023925781, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008319541811943054, "Height": 0.011291200295090675, "Left": 0.4258476197719574, "Top": 0.40057849884033203}, "Polygon": [{"X": 0.4258476197719574, "Y": 0.40059521794319153}, {"X": 0.43396326899528503, "Y": 0.40057849884033203}, {"X": 0.43416717648506165, "Y": 0.4118499159812927}, {"X": 0.4260334074497223, "Y": 0.41186970472335815}]}, "Id": "4e4d15d0-e131-472b-ac29-3f7f70c66dcc"}, {"BlockType": "WORD", "Confidence": 99.4345932006836, "Text": "24.616G", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0856003537774086, "Height": 0.013262216001749039, "Left": 0.4983149468898773, "Top": 0.39768263697624207}, "Polygon": [{"X": 0.4983149468898773, "Y": 0.3978503942489624}, {"X": 0.5832918882369995, "Y": 0.39768263697624207}, {"X": 0.5839152932167053, "Y": 0.41073980927467346}, {"X": 0.4987192153930664, "Y": 0.4109448492527008}]}, "Id": "bf58f8e8-0758-4a9f-a933-b9e0825d8439"}, {"BlockType": "WORD", "Confidence": 95.77965545654297, "Text": "PRICE/GAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10867486149072647, "Height": 0.013879196718335152, "Left": 0.340284526348114, "Top": 0.4183305501937866}, "Polygon": [{"X": 0.34029123187065125, "Y": 0.4186173975467682}, {"X": 0.44867587089538574, "Y": 0.4183305501937866}, {"X": 0.4489594101905823, "Y": 0.43187353014945984}, {"X": 0.340284526348114, "Y": 0.43220973014831543}]}, "Id": "00c2b461-fc5c-4bba-b980-c853116c579a"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "$3.399", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07294952869415283, "Height": 0.014125720597803593, "Left": 0.5112459063529968, "Top": 0.4151552617549896}, "Polygon": [{"X": 0.5112459063529968, "Y": 0.4153398275375366}, {"X": 0.5835330486297607, "Y": 0.4151552617549896}, {"X": 0.5841954350471497, "Y": 0.42906248569488525}, {"X": 0.5117104649543762, "Y": 0.42928096652030945}]}, "Id": "d66bf89c-5565-4b02-974e-e03d13c5f01b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048909686505794525, "Height": 0.012340622022747993, "Left": 0.33888500928878784, "Top": 0.45399776101112366}, "Polygon": [{"X": 0.3388943076133728, "Y": 0.454183965921402}, {"X": 0.3876877427101135, "Y": 0.45399776101112366}, {"X": 0.38779470324516296, "Y": 0.46613219380378723}, {"X": 0.33888500928878784, "Y": 0.4663383960723877}]}, "Id": "a54f48da-acf5-435b-bfaa-330bbeb3bc88"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06222457066178322, "Height": 0.01190238632261753, "Left": 0.3998355269432068, "Top": 0.4533562958240509}, "Polygon": [{"X": 0.3998355269432068, "Y": 0.4535919725894928}, {"X": 0.4617880582809448, "Y": 0.4533562958240509}, {"X": 0.4620600938796997, "Y": 0.46499860286712646}, {"X": 0.39996618032455444, "Y": 0.4652586579322815}]}, "Id": "bbc5c3cf-288f-4c7a-b85d-fe0f5ad13803"}, {"BlockType": "WORD", "Confidence": 99.091796875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01342830341309309, "Height": 0.013865859247744083, "Left": 0.4871888756752014, "Top": 0.4515383541584015}, "Polygon": [{"X": 0.4871888756752014, "Y": 0.45158708095550537}, {"X": 0.5001901984214783, "Y": 0.4515383541584015}, {"X": 0.5006172060966492, "Y": 0.46534937620162964}, {"X": 0.4875807762145996, "Y": 0.465404212474823}]}, "Id": "8c3d9d9a-220a-4624-85a2-78ed545fa5ed"}, {"BlockType": "WORD", "Confidence": 99.091796875, "Text": "83.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061388976871967316, "Height": 0.013025863096117973, "Left": 0.5240901112556458, "Top": 0.45088598132133484}, "Polygon": [{"X": 0.5240901112556458, "Y": 0.45111316442489624}, {"X": 0.5848717093467712, "Y": 0.45088598132133484}, {"X": 0.5854791402816772, "Y": 0.46365830302238464}, {"X": 0.5245459079742432, "Y": 0.46391186118125916}]}, "Id": "dc262666-7eda-4fae-b76b-50c061977c7e"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06322432309389114, "Height": 0.012634980492293835, "Left": 0.386945515871048, "Top": 0.5065507292747498}, "Polygon": [{"X": 0.386945515871048, "Y": 0.5069007277488708}, {"X": 0.4499146640300751, "Y": 0.5065507292747498}, {"X": 0.4501698315143585, "Y": 0.5188093185424805}, {"X": 0.38705092668533325, "Y": 0.5191857218742371}]}, "Id": "8bd53014-1a81-475c-a88f-1efd922697b9"}, {"BlockType": "WORD", "Confidence": 99.14800262451172, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013346925377845764, "Height": 0.006545821204781532, "Left": 0.46191683411598206, "Top": 0.5103152990341187}, "Polygon": [{"X": 0.46191683411598206, "Y": 0.5103902816772461}, {"X": 0.4750975966453552, "Y": 0.5103152990341187}, {"X": 0.475263774394989, "Y": 0.5167832374572754}, {"X": 0.46206653118133545, "Y": 0.5168611407279968}]}, "Id": "4d31be00-617d-450a-8d6d-f8c1cf21f860"}, {"BlockType": "WORD", "Confidence": 99.453125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013264195062220097, "Height": 0.013756806030869484, "Left": 0.48854121565818787, "Top": 0.5048403739929199}, "Polygon": [{"X": 0.48854121565818787, "Y": 0.5049111843109131}, {"X": 0.5013836622238159, "Y": 0.5048403739929199}, {"X": 0.5018054246902466, "Y": 0.5185204148292542}, {"X": 0.4889289438724518, "Y": 0.5185971856117249}]}, "Id": "8303c688-dc86-44c5-ad7c-c23d743951b3"}, {"BlockType": "WORD", "Confidence": 99.4140625, "Text": "83.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06217293068766594, "Height": 0.012747634202241898, "Left": 0.****************, "Top": 0.5041924715042114}, "Polygon": [{"X": 0.****************, "Y": 0.5045315027236938}, {"X": 0.5873978734016418, "Y": 0.5041924715042114}, {"X": 0.5879867076873779, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "178381a3-0121-4589-9956-e4411f1aff91"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "CREDIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07523081451654434, "Height": 0.013127401471138, "Left": 0.3370814025402069, "Top": 0.543509304523468}, "Polygon": [{"X": 0.3370952606201172, "Y": 0.****************}, {"X": 0.****************, "Y": 0.543509304523468}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.3370814025402069, "Y": 0.****************}]}, "Id": "10f9fb2c-19e6-44e9-b0a2-29a6904a535a"}, {"BlockType": "WORD", "Confidence": 96.298828125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013607455417513847, "Height": 0.014475191943347454, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.502447783946991, "Y": 0.****************}, {"X": 0.502890944480896, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "7bb6db53-4435-4652-af08-eccc1e43d19e"}, {"BlockType": "WORD", "Confidence": 99.169921875, "Text": "83.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06260302662849426, "Height": 0.01316982600837946, "Left": 0.527265191078186, "Top": 0.5402618646621704}, "Polygon": [{"X": 0.527265191078186, "Y": 0.5406773090362549}, {"X": 0.5892626643180847, "Y": 0.5402618646621704}, {"X": 0.5898682475090027, "Y": 0.5529889464378357}, {"X": 0.5277193784713745, "Y": 0.5534316897392273}]}, "Id": "fbb03dde-121c-43bd-850f-55d1458b7a6f"}, {"BlockType": "WORD", "Confidence": 98.34602355957031, "Text": "Customer-activated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11754953861236572, "Height": 0.015091994777321815, "Left": 0.3343028426170349, "Top": 0.598453164100647}, "Polygon": [{"X": 0.3343255817890167, "Y": 0.5994603633880615}, {"X": 0.4515610933303833, "Y": 0.598453164100647}, {"X": 0.45185238122940063, "Y": 0.6124808192253113}, {"X": 0.3343028426170349, "Y": 0.6135451793670654}]}, "Id": "3fcac71d-e310-4f30-aed7-d9d37cce0a59"}, {"BlockType": "WORD", "Confidence": 99.76322937011719, "Text": "Purchase/Capture", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10411178320646286, "Height": 0.01703871227800846, "Left": 0.45756497979164124, "Top": 0.5969215631484985}, "Polygon": [{"X": 0.45756497979164124, "Y": 0.5978081822395325}, {"X": 0.5610061287879944, "Y": 0.5969215631484985}, {"X": 0.5616768002510071, "Y": 0.6130155324935913}, {"X": 0.4579189419746399, "Y": 0.6139602661132812}]}, "Id": "0268446e-14f5-4621-a09f-773d6a390560"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Site", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026627859100699425, "Height": 0.013557132333517075, "Left": 0.3343777656555176, "Top": 0.619783341884613}, "Polygon": [{"X": 0.3343989849090576, "Y": 0.6200294494628906}, {"X": 0.3609596788883209, "Y": 0.619783341884613}, {"X": 0.36100560426712036, "Y": 0.6330820322036743}, {"X": 0.3343777656555176, "Y": 0.6333404779434204}]}, "Id": "a6f490e4-484c-452a-98f6-0df72ee85534"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012744901701807976, "Height": 0.012871015816926956, "Left": 0.3662455976009369, "Top": 0.619874894618988}, "Polygon": [{"X": 0.3662455976009369, "Y": 0.6199923157691956}, {"X": 0.37890297174453735, "Y": 0.619874894618988}, {"X": 0.37899050116539, "Y": 0.6326228976249695}, {"X": 0.366302490234375, "Y": 0.6327459216117859}]}, "Id": "e149f545-f9ea-4ba7-99c8-669108bbc6b8"}, {"BlockType": "WORD", "Confidence": 33.77311706542969, "Text": "0000008004816757", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10408898442983627, "Height": 0.013742565177381039, "Left": 0.38673028349876404, "Top": 0.6180891990661621}, "Polygon": [{"X": 0.38673028349876404, "Y": 0.6190488338470459}, {"X": 0.490461528301239, "Y": 0.6180891990661621}, {"X": 0.4908192753791809, "Y": 0.6308260560035706}, {"X": 0.38683709502220154, "Y": 0.6318317651748657}]}, "Id": "db1d8b83-44a5-4bad-822a-733b76efc449"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Shift", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03369184583425522, "Height": 0.014604107476770878, "Left": 0.3341400921344757, "Top": 0.6387289762496948}, "Polygon": [{"X": 0.3341633975505829, "Y": 0.6390613913536072}, {"X": 0.3677644729614258, "Y": 0.6387289762496948}, {"X": 0.36783191561698914, "Y": 0.6529839634895325}, {"X": 0.3341400921344757, "Y": 0.6533330678939819}]}, "Id": "ad0846f7-b73c-470e-ad23-b19886ed3ba5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039499614387750626, "Height": 0.013289711438119411, "Left": 0.37366580963134766, "Top": 0.6388044357299805}, "Polygon": [{"X": 0.37366580963134766, "Y": 0.6391941905021667}, {"X": 0.4129940867424011, "Y": 0.6388044357299805}, {"X": 0.413165420293808, "Y": 0.6516867280006409}, {"X": 0.3737412691116333, "Y": 0.6520941853523254}]}, "Id": "ad0a4a4b-cea9-459d-b349-8f9bbbe36449"}, {"BlockType": "WORD", "Confidence": 99.64604187011719, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005725864786654711, "Height": 0.012504030019044876, "Left": 0.4195985198020935, "Top": 0.6387603878974915}, "Polygon": [{"X": 0.4195985198020935, "Y": 0.6388152241706848}, {"X": 0.4251302182674408, "Y": 0.6387603878974915}, {"X": 0.42532438039779663, "Y": 0.6512072086334229}, {"X": 0.4197796583175659, "Y": 0.6512644290924072}]}, "Id": "01764dae-e08a-44ef-a321-5355ed2bbe53"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Sequence", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05323966220021248, "Height": 0.01616300828754902, "Left": 0.33442243933677673, "Top": 0.659378707408905}, "Polygon": [{"X": 0.3344469964504242, "Y": 0.6599403023719788}, {"X": 0.3875306248664856, "Y": 0.659378707408905}, {"X": 0.3876620829105377, "Y": 0.6749511361122131}, {"X": 0.33442243933677673, "Y": 0.6755416989326477}]}, "Id": "35837ecb-1708-4429-aabe-5073828c6818"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039305854588747025, "Height": 0.01331415493041277, "Left": 0.3936237394809723, "Top": 0.6577954888343811}, "Polygon": [{"X": 0.3936237394809723, "Y": 0.6582075953483582}, {"X": 0.4327108860015869, "Y": 0.6577954888343811}, {"X": 0.4329296052455902, "Y": 0.6706799268722534}, {"X": 0.3937475383281708, "Y": 0.671109676361084}]}, "Id": "d515d03e-0318-413a-8e90-9439f3774bbc"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "58096", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03307010978460312, "Height": 0.013204086571931839, "Left": 0.43935537338256836, "Top": 0.6570566892623901}, "Polygon": [{"X": 0.43935537338256836, "Y": 0.6574016809463501}, {"X": 0.47211188077926636, "Y": 0.6570566892623901}, {"X": 0.4724254906177521, "Y": 0.6699010133743286}, {"X": 0.43958979845046997, "Y": 0.6702607870101929}]}, "Id": "f3491e11-c96d-416f-94b4-e0a4f4697ddd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Swiped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04065830633044243, "Height": 0.01744830794632435, "Left": 0.33436277508735657, "Top": 0.678260326385498}, "Polygon": [{"X": 0.3343895971775055, "Y": 0.6787141561508179}, {"X": 0.37491855025291443, "Y": 0.678260326385498}, {"X": 0.3750210702419281, "Y": 0.6952307224273682}, {"X": 0.33436277508735657, "Y": 0.6957086324691772}]}, "Id": "dec446bc-7523-486c-a2d5-4057276b78e7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0537681020796299, "Height": 0.01431447733193636, "Left": 0.3344919979572296, "Top": 0.698016881942749}, "Polygon": [{"X": 0.33451318740844727, "Y": 0.6986525654792786}, {"X": 0.3881440758705139, "Y": 0.698016881942749}, {"X": 0.3882600963115692, "Y": 0.711669921875}, {"X": 0.3344919979572296, "Y": 0.7123313546180725}]}, "Id": "6dc89071-26ca-404d-8300-b39e90ec25aa"}, {"BlockType": "WORD", "Confidence": 71.68984985351562, "Text": "210836", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04038885608315468, "Height": 0.013860289007425308, "Left": 0.4004771411418915, "Top": 0.696584165096283}, "Polygon": [{"X": 0.4004771411418915, "Y": 0.6970588564872742}, {"X": 0.44062095880508423, "Y": 0.696584165096283}, {"X": 0.44086599349975586, "Y": 0.7099508047103882}, {"X": 0.40062183141708374, "Y": 0.710444450378418}]}, "Id": "1a69f3d8-2d5c-4ecf-aabe-f3e5bb0b3b49"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05332792177796364, "Height": 0.013620592653751373, "Left": 0.3346213400363922, "Top": 0.7186241149902344}, "Polygon": [{"X": 0.3346410393714905, "Y": 0.7192907333374023}, {"X": 0.3878405690193176, "Y": 0.7186241149902344}, {"X": 0.38794925808906555, "Y": 0.7315537333488464}, {"X": 0.3346213400363922, "Y": 0.7322446703910828}]}, "Id": "f5bde418-dd39-4326-b4a4-d69217634013"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07188063859939575, "Height": 0.0147877037525177, "Left": 0.3953824043273926, "Top": 0.716018557548523}, "Polygon": [{"X": 0.3953824043273926, "Y": 0.7169114351272583}, {"X": 0.466941773891449, "Y": 0.716018557548523}, {"X": 0.46726304292678833, "Y": 0.7298782467842102}, {"X": 0.39551883935928345, "Y": 0.730806291103363}]}, "Id": "e16157d0-95f5-4c36-81a8-5c5c22c4845b"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0324096716940403, "Height": 0.014000899158418179, "Left": 0.33483412861824036, "Top": 0.7386094927787781}, "Polygon": [{"X": 0.3348541557788849, "Y": 0.7390355467796326}, {"X": 0.36718231439590454, "Y": 0.7386094927787781}, {"X": 0.36724379658699036, "Y": 0.7521687746047974}, {"X": 0.33483412861824036, "Y": 0.7526103854179382}]}, "Id": "2347c474-1ca2-4c09-a7f0-f90b79405f13"}, {"BlockType": "WORD", "Confidence": 72.97512817382812, "Text": "HP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020693747326731682, "Height": 0.014083849266171455, "Left": 0.3748238682746887, "Top": 0.7373512983322144}, "Polygon": [{"X": 0.3748238682746887, "Y": 0.7376216650009155}, {"X": 0.3953825831413269, "Y": 0.7373512983322144}, {"X": 0.39551761746406555, "Y": 0.7511547207832336}, {"X": 0.37490615248680115, "Y": 0.7514351606369019}]}, "Id": "2fc1020e-cf39-419b-b6a2-cb0f78b83aeb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DISTRIBUTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07922664284706116, "Height": 0.0142713263630867, "Left": 0.401422917842865, "Top": 0.7363840341567993}, "Polygon": [{"X": 0.401422917842865, "Y": 0.7374218106269836}, {"X": 0.4803119897842407, "Y": 0.7363840341567993}, {"X": 0.48064956068992615, "Y": 0.749580442905426}, {"X": 0.4015672206878662, "Y": 0.7506553530693054}]}, "Id": "cdd6226c-0d68-47e4-a790-f5de7786de29"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "City:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03262776508927345, "Height": 0.018712574616074562, "Left": 0.3347426652908325, "Top": 0.7578064799308777}, "Polygon": [{"X": 0.3347697854042053, "Y": 0.7582556009292603}, {"X": 0.36728760600090027, "Y": 0.7578064799308777}, {"X": 0.3673704266548157, "Y": 0.7760487794876099}, {"X": 0.3347426652908325, "Y": 0.7765190005302429}]}, "Id": "048d7cbc-a23d-4f28-a50f-8b36747de983"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "OLATHE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04025190323591232, "Height": 0.014400924555957317, "Left": 0.375194787979126, "Top": 0.7571379542350769}, "Polygon": [{"X": 0.375194787979126, "Y": 0.7576913237571716}, {"X": 0.41526100039482117, "Y": 0.7571379542350769}, {"X": 0.4154466986656189, "Y": 0.7709657549858093}, {"X": 0.37527793645858765, "Y": 0.7715388536453247}]}, "Id": "d8fbd224-9008-4100-8d00-63642402ee7e"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "State:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03944970294833183, "Height": 0.014821258373558521, "Left": 0.33465874195098877, "Top": 0.7791552543640137}, "Polygon": [{"X": 0.33468005061149597, "Y": 0.779726505279541}, {"X": 0.37402641773223877, "Y": 0.7791552543640137}, {"X": 0.3741084337234497, "Y": 0.7933853268623352}, {"X": 0.33465874195098877, "Y": 0.7939765453338623}]}, "Id": "2ed37c21-8784-493e-bddd-820bd961ce0b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013738519512116909, "Height": 0.013752116821706295, "Left": 0.3820672035217285, "Top": 0.7777979373931885}, "Polygon": [{"X": 0.3820672035217285, "Y": 0.7779949903488159}, {"X": 0.3956734836101532, "Y": 0.7777979373931885}, {"X": 0.3958057165145874, "Y": 0.7913463711738586}, {"X": 0.38216549158096313, "Y": 0.7915500402450562}]}, "Id": "bb880def-b04f-4cfd-8b2b-374d315ea90b"}, {"BlockType": "WORD", "Confidence": 98.03950500488281, "Text": "Acc.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03313225135207176, "Height": 0.014488106593489647, "Left": 0.3342275619506836, "Top": 0.8003284931182861}, "Polygon": [{"X": 0.33424949645996094, "Y": 0.800831139087677}, {"X": 0.36729687452316284, "Y": 0.8003284931182861}, {"X": 0.36735981702804565, "Y": 0.8142973780632019}, {"X": 0.3342275619506836, "Y": 0.8148165941238403}]}, "Id": "c2077257-74b8-458e-b2ea-78c066ee3f7d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "132643", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04061980918049812, "Height": 0.014931004494428635, "Left": 0.3756730556488037, "Top": 0.7981279492378235}, "Polygon": [{"X": 0.3756730556488037, "Y": 0.7987409830093384}, {"X": 0.4161001145839691, "Y": 0.7981279492378235}, {"X": 0.41629284620285034, "Y": 0.8124252557754517}, {"X": 0.37575963139533997, "Y": 0.8130589723587036}]}, "Id": "ec287cf9-67c7-436d-a35d-d70d76cf3319"}, {"BlockType": "WORD", "Confidence": 98.75458526611328, "Text": "Odometer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06052955240011215, "Height": 0.015407819300889969, "Left": 0.3341817855834961, "Top": 0.8205097913742065}, "Polygon": [{"X": 0.3342044949531555, "Y": 0.8214690685272217}, {"X": 0.3945746123790741, "Y": 0.8205097913742065}, {"X": 0.39471134543418884, "Y": 0.8349270820617676}, {"X": 0.3341817855834961, "Y": 0.835917592048645}]}, "Id": "addc4bd3-8973-45df-a1ea-4f43fe160d4f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "274352", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04108935222029686, "Height": 0.014541400596499443, "Left": 0.40210816264152527, "Top": 0.8178001046180725}, "Polygon": [{"X": 0.40210816264152527, "Y": 0.8184463381767273}, {"X": 0.44294288754463196, "Y": 0.8178001046180725}, {"X": 0.4431975185871124, "Y": 0.8316748738288879}, {"X": 0.4022591710090637, "Y": 0.8323414921760559}]}, "Id": "2d27dcac-b5cc-4ff2-9ec0-9e7a79a8a68d"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Trip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02829389087855816, "Height": 0.018846485763788223, "Left": 0.3340495526790619, "Top": 0.8415222764015198}, "Polygon": [{"X": 0.33407875895500183, "Y": 0.8419893980026245}, {"X": 0.36227816343307495, "Y": 0.8415222764015198}, {"X": 0.362343430519104, "Y": 0.8598830103874207}, {"X": 0.3340495526790619, "Y": 0.8603687882423401}]}, "Id": "72044229-6347-4179-8a44-3a0857dd24c8"}, {"BlockType": "WORD", "Confidence": 99.65740203857422, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013035054318606853, "Height": 0.01317590195685625, "Left": 0.36825618147850037, "Top": 0.842046856880188}, "Polygon": [{"X": 0.36825618147850037, "Y": 0.8422616124153137}, {"X": 0.3812004625797272, "Y": 0.842046856880188}, {"X": 0.3812912404537201, "Y": 0.8550019264221191}, {"X": 0.3683163821697235, "Y": 0.855222761631012}]}, "Id": "d729203c-1640-479e-b176-975835d7b802"}, {"BlockType": "WORD", "Confidence": 98.32588958740234, "Text": "4408", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027972718700766563, "Height": 0.013680599629878998, "Left": 0.3891220688819885, "Top": 0.8402074575424194}, "Polygon": [{"X": 0.3891220688819885, "Y": 0.8406674861907959}, {"X": 0.4169161319732666, "Y": 0.8402074575424194}, {"X": 0.41709479689598083, "Y": 0.8534148335456848}, {"X": 0.38923385739326477, "Y": 0.8538880944252014}]}, "Id": "64b841c3-50e9-4aad-993d-d7ae2b9ea227"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d405e976-3d43-4246-aeee-74ad712e554b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d405e976-3d43-4246-aeee-74ad712e554b", "content-type": "application/x-amz-json-1.1", "content-length": "57445", "date": "Thu, 18 Sep 2025 08:51:12 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "b804d0ae-3e2e-499f-8477-5161e1e44928", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:14 GMT", "content-type": "application/json", "content-length": "806", "connection": "keep-alive", "x-amzn-requestid": "b804d0ae-3e2e-499f-8477-5161e1e44928"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have to classify each page. There's only page1. The page content: \"EXXON EXPRESS PAY ... FUEL TOTAL $ 83.67 ... Invoice 051693 ... Fuel Receipt.\" It seems a fuel receipt. Keywords: Fuel, Diesel, Pump, Price, FUEL TOTAL. So doc_type: fuel_receipt.\n\nWe must output with tool call:\n\n{\n \"documents\": [\n  {\"page_no\": 1, \"doc_type\": \"fuel_receipt\"}\n ]\n}\n\nLet's produce the tool call."}}}, {"toolUse": {"toolUseId": "tooluse_5KpTEraQTZyOs4Dq9FcEMA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2317, "outputTokens": 144, "totalTokens": 2461}, "metrics": {"latencyMs": 845}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/a30d14bd_9_fuel_receipt_3.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n<PERSON><PERSON><PERSON><PERSON> EXPRESS PAY\n580 LONE STAR DRIVE\nPARADISE QUICKSTOP #\nXXXXXXXXX0001\nHOOKS\n, TX\n75561\n05/13/2025 590412430\n08:56:09 AM\nXXXXXXXXXXXXXXX8412\nTCH Fuel\nINVOICE 051693\nAUTH 210836\nPUMP# 13\nDiesel 1\n24.616G\nPRICE/GAL\n$3.399\nFUEL TOTAL $ 83.67\nTOTAL = $ 83.67\nCREDIT\n$ 83.67\nCustomer-activated Purchase/Capture\nSite #: 0000008004816757\nShift Number 1\nSequence Number 58096\nSwiped\nAPPROVED 210836\nCustomer Information\nName: HP DISTRIBUTION\nCity: OLATHE\nState: KS\nAcc.: 132643\nOdometer: 274352\nTrip #: 4408\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}