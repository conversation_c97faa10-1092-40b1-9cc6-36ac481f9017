#!/usr/bin/env python3
"""
Script to provide summary statistics of token usage from the CSV report.
"""

import csv
import os


def analyze_token_usage():
    """Analyze token usage from the CSV report."""
    csv_file = "/home/<USER>/Desktop/test_ligistically/Augment_test/token_analysis_report.csv"
    
    if not os.path.exists(csv_file):
        print(f"Error: CSV file not found at {csv_file}")
        return
    
    # Read CSV data
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        rows = list(reader)
    
    # Extract numeric values
    input_tokens = []
    output_tokens = []
    total_tokens = []
    
    for row in rows:
        try:
            if row['Input Tokens'] != 'N/A':
                input_tokens.append(int(row['Input Tokens']))
                output_tokens.append(int(row['Output Tokens']))
                total_tokens.append(int(row['Total Tokens']))
        except ValueError:
            continue
    
    if not total_tokens:
        print("No valid token data found in CSV file.")
        return
    
    # Calculate statistics
    print("=" * 60)
    print("TOKEN USAGE ANALYSIS REPORT")
    print("=" * 60)
    print(f"Total files processed: {len(rows)}")
    print(f"Files with valid token data: {len(total_tokens)}")
    print()
    
    print("INPUT TOKENS:")
    print(f"  Total: {sum(input_tokens):,}")
    print(f"  Average: {sum(input_tokens) / len(input_tokens):.1f}")
    print(f"  Min: {min(input_tokens):,}")
    print(f"  Max: {max(input_tokens):,}")
    print()
    
    print("OUTPUT TOKENS:")
    print(f"  Total: {sum(output_tokens):,}")
    print(f"  Average: {sum(output_tokens) / len(output_tokens):.1f}")
    print(f"  Min: {min(output_tokens):,}")
    print(f"  Max: {max(output_tokens):,}")
    print()
    
    print("TOTAL TOKENS:")
    print(f"  Grand Total: {sum(total_tokens):,}")
    print(f"  Average per file: {sum(total_tokens) / len(total_tokens):.1f}")
    print(f"  Min: {min(total_tokens):,}")
    print(f"  Max: {max(total_tokens):,}")
    print()
    
    # Find files with highest token usage
    print("TOP 5 FILES BY TOTAL TOKENS:")
    file_tokens = [(rows[i]['File Name (Hyperlink)'], total_tokens[i]) for i in range(len(total_tokens))]
    file_tokens.sort(key=lambda x: x[1], reverse=True)
    
    for i, (filename, tokens) in enumerate(file_tokens[:5], 1):
        # Extract just the filename from the hyperlink
        filename_only = filename.split('"')[-2] if '"' in filename else filename
        print(f"  {i}. {filename_only}: {tokens:,} tokens")
    
    print("=" * 60)


if __name__ == "__main__":
    analyze_token_usage()
