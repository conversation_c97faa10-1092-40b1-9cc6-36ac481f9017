2025-09-18 13:21:28,704 - INFO - Logging initialized. Log file: logs/test_classification_20250918_132128.log
2025-09-18 13:21:28,704 - INFO - 📁 Found 11 files to process
2025-09-18 13:21:28,704 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:21:28,704 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 13:21:28,704 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 13:21:28,704 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 13:21:28,704 - INFO - ⬆️ [13:21:28] Uploading: 1_bol_1.pdf
2025-09-18 13:21:30,299 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_1.pdf -> s3://document-extraction-logistically/temp/ae8ab5c2_1_bol_1.pdf
2025-09-18 13:21:30,300 - INFO - 🔍 [13:21:30] Starting classification: 1_bol_1.pdf
2025-09-18 13:21:30,300 - INFO - ⬆️ [13:21:30] Uploading: 1_bol_10.pdf
2025-09-18 13:21:30,302 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:30,324 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:30,330 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ae8ab5c2_1_bol_1.pdf
2025-09-18 13:21:30,331 - INFO - Processing PDF from S3...
2025-09-18 13:21:30,331 - INFO - Downloading PDF from S3 to /tmp/tmpu6f4ne8z/ae8ab5c2_1_bol_1.pdf
2025-09-18 13:21:31,813 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_10.pdf -> s3://document-extraction-logistically/temp/ebeeb6b3_1_bol_10.pdf
2025-09-18 13:21:31,813 - INFO - 🔍 [13:21:31] Starting classification: 1_bol_10.pdf
2025-09-18 13:21:31,813 - INFO - ⬆️ [13:21:31] Uploading: 1_bol_11.pdf
2025-09-18 13:21:31,814 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:31,823 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:31,825 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ebeeb6b3_1_bol_10.pdf
2025-09-18 13:21:31,825 - INFO - Processing PDF from S3...
2025-09-18 13:21:31,825 - INFO - Downloading PDF from S3 to /tmp/tmpooub6tyl/ebeeb6b3_1_bol_10.pdf
2025-09-18 13:21:31,855 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:31,856 - INFO - Splitting PDF ae8ab5c2_1_bol_1 into 1 pages
2025-09-18 13:21:31,858 - INFO - Split PDF into 1 pages
2025-09-18 13:21:31,858 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:31,859 - INFO - Expected pages: [1]
2025-09-18 13:21:32,686 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_11.pdf -> s3://document-extraction-logistically/temp/949ad253_1_bol_11.pdf
2025-09-18 13:21:32,686 - INFO - 🔍 [13:21:32] Starting classification: 1_bol_11.pdf
2025-09-18 13:21:32,687 - INFO - ⬆️ [13:21:32] Uploading: 1_bol_2.pdf
2025-09-18 13:21:32,689 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:32,709 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:32,713 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/949ad253_1_bol_11.pdf
2025-09-18 13:21:32,713 - INFO - Processing PDF from S3...
2025-09-18 13:21:32,714 - INFO - Downloading PDF from S3 to /tmp/tmpze5in386/949ad253_1_bol_11.pdf
2025-09-18 13:21:33,300 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_2.pdf -> s3://document-extraction-logistically/temp/8e2ba91b_1_bol_2.pdf
2025-09-18 13:21:33,301 - INFO - 🔍 [13:21:33] Starting classification: 1_bol_2.pdf
2025-09-18 13:21:33,301 - INFO - ⬆️ [13:21:33] Uploading: 1_bol_3.pdf
2025-09-18 13:21:33,303 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:33,324 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:33,330 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8e2ba91b_1_bol_2.pdf
2025-09-18 13:21:33,330 - INFO - Processing PDF from S3...
2025-09-18 13:21:33,331 - INFO - Downloading PDF from S3 to /tmp/tmpt0jjkke0/8e2ba91b_1_bol_2.pdf
2025-09-18 13:21:33,940 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_3.pdf -> s3://document-extraction-logistically/temp/7e470e50_1_bol_3.pdf
2025-09-18 13:21:33,941 - INFO - 🔍 [13:21:33] Starting classification: 1_bol_3.pdf
2025-09-18 13:21:33,942 - INFO - ⬆️ [13:21:33] Uploading: 1_bol_4.pdf
2025-09-18 13:21:33,944 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:33,963 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:33,967 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7e470e50_1_bol_3.pdf
2025-09-18 13:21:33,968 - INFO - Processing PDF from S3...
2025-09-18 13:21:33,968 - INFO - Downloading PDF from S3 to /tmp/tmpcaedv78s/7e470e50_1_bol_3.pdf
2025-09-18 13:21:34,166 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:34,167 - INFO - Splitting PDF ebeeb6b3_1_bol_10 into 1 pages
2025-09-18 13:21:34,169 - INFO - Split PDF into 1 pages
2025-09-18 13:21:34,169 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:34,169 - INFO - Expected pages: [1]
2025-09-18 13:21:34,545 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_4.pdf -> s3://document-extraction-logistically/temp/f72b3a3a_1_bol_4.pdf
2025-09-18 13:21:34,546 - INFO - 🔍 [13:21:34] Starting classification: 1_bol_4.pdf
2025-09-18 13:21:34,547 - INFO - ⬆️ [13:21:34] Uploading: 1_bol_5.pdf
2025-09-18 13:21:34,547 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:34,563 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:34,568 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f72b3a3a_1_bol_4.pdf
2025-09-18 13:21:34,568 - INFO - Processing PDF from S3...
2025-09-18 13:21:34,569 - INFO - Downloading PDF from S3 to /tmp/tmprty31r0u/f72b3a3a_1_bol_4.pdf
2025-09-18 13:21:35,099 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:35,100 - INFO - Splitting PDF 949ad253_1_bol_11 into 1 pages
2025-09-18 13:21:35,102 - INFO - Split PDF into 1 pages
2025-09-18 13:21:35,102 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:35,102 - INFO - Expected pages: [1]
2025-09-18 13:21:35,152 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_5.pdf -> s3://document-extraction-logistically/temp/96085ef7_1_bol_5.pdf
2025-09-18 13:21:35,152 - INFO - 🔍 [13:21:35] Starting classification: 1_bol_5.pdf
2025-09-18 13:21:35,153 - INFO - ⬆️ [13:21:35] Uploading: 1_bol_6.pdf
2025-09-18 13:21:35,154 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:35,170 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:35,173 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/96085ef7_1_bol_5.pdf
2025-09-18 13:21:35,173 - INFO - Processing PDF from S3...
2025-09-18 13:21:35,174 - INFO - Downloading PDF from S3 to /tmp/tmp_07ux4o_/96085ef7_1_bol_5.pdf
2025-09-18 13:21:35,608 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:35,610 - INFO - Splitting PDF 8e2ba91b_1_bol_2 into 1 pages
2025-09-18 13:21:35,612 - INFO - Split PDF into 1 pages
2025-09-18 13:21:35,612 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:35,612 - INFO - Expected pages: [1]
2025-09-18 13:21:35,769 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_6.pdf -> s3://document-extraction-logistically/temp/131fca1b_1_bol_6.pdf
2025-09-18 13:21:35,770 - INFO - 🔍 [13:21:35] Starting classification: 1_bol_6.pdf
2025-09-18 13:21:35,771 - INFO - ⬆️ [13:21:35] Uploading: 1_bol_7.pdf
2025-09-18 13:21:35,773 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:35,819 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:35,821 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/131fca1b_1_bol_6.pdf
2025-09-18 13:21:35,821 - INFO - Processing PDF from S3...
2025-09-18 13:21:35,821 - INFO - Downloading PDF from S3 to /tmp/tmp7icvnvxs/131fca1b_1_bol_6.pdf
2025-09-18 13:21:36,294 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:36,295 - INFO - Splitting PDF 7e470e50_1_bol_3 into 1 pages
2025-09-18 13:21:36,297 - INFO - Split PDF into 1 pages
2025-09-18 13:21:36,297 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:36,297 - INFO - Expected pages: [1]
2025-09-18 13:21:36,497 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:36,498 - INFO - Splitting PDF f72b3a3a_1_bol_4 into 1 pages
2025-09-18 13:21:36,499 - INFO - Split PDF into 1 pages
2025-09-18 13:21:36,499 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:36,499 - INFO - Expected pages: [1]
2025-09-18 13:21:36,799 - INFO - Page 1: Extracted 2681 characters, 83 lines from ae8ab5c2_1_bol_1_3005bb29_page_001.pdf
2025-09-18 13:21:36,799 - INFO - Successfully processed page 1
2025-09-18 13:21:36,799 - INFO - Combined 1 pages into final text
2025-09-18 13:21:36,800 - INFO - Text validation for ae8ab5c2_1_bol_1: 2698 characters, 1 pages
2025-09-18 13:21:36,800 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:36,800 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:36,803 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_7.pdf -> s3://document-extraction-logistically/temp/06688bab_1_bol_7.pdf
2025-09-18 13:21:36,803 - INFO - 🔍 [13:21:36] Starting classification: 1_bol_7.pdf
2025-09-18 13:21:36,803 - INFO - ⬆️ [13:21:36] Uploading: 1_bol_8.pdf
2025-09-18 13:21:36,803 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:36,810 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:36,813 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/06688bab_1_bol_7.pdf
2025-09-18 13:21:36,813 - INFO - Processing PDF from S3...
2025-09-18 13:21:36,813 - INFO - Downloading PDF from S3 to /tmp/tmp28zev0b5/06688bab_1_bol_7.pdf
2025-09-18 13:21:36,959 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:36,961 - INFO - Splitting PDF 96085ef7_1_bol_5 into 1 pages
2025-09-18 13:21:36,962 - INFO - Split PDF into 1 pages
2025-09-18 13:21:36,962 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:36,962 - INFO - Expected pages: [1]
2025-09-18 13:21:37,871 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:37,873 - INFO - Splitting PDF 131fca1b_1_bol_6 into 1 pages
2025-09-18 13:21:37,874 - INFO - Split PDF into 1 pages
2025-09-18 13:21:37,875 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:37,875 - INFO - Expected pages: [1]
2025-09-18 13:21:38,689 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ae8ab5c2_1_bol_1.pdf
2025-09-18 13:21:39,389 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_8.pdf -> s3://document-extraction-logistically/temp/306cf5ec_1_bol_8.pdf
2025-09-18 13:21:39,390 - INFO - 🔍 [13:21:39] Starting classification: 1_bol_8.pdf
2025-09-18 13:21:39,390 - INFO - ⬆️ [13:21:39] Uploading: 1_bol_9.pdf
2025-09-18 13:21:39,392 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:39,413 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:39,416 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/306cf5ec_1_bol_8.pdf
2025-09-18 13:21:39,417 - INFO - Processing PDF from S3...
2025-09-18 13:21:39,417 - INFO - Downloading PDF from S3 to /tmp/tmp5w73nnvg/306cf5ec_1_bol_8.pdf
2025-09-18 13:21:40,104 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_9.pdf -> s3://document-extraction-logistically/temp/676c7509_1_bol_9.pdf
2025-09-18 13:21:40,104 - INFO - 🔍 [13:21:40] Starting classification: 1_bol_9.pdf
2025-09-18 13:21:40,106 - INFO - Initializing TextractProcessor...
2025-09-18 13:21:40,126 - INFO - Initializing BedrockProcessor...
2025-09-18 13:21:40,132 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/676c7509_1_bol_9.pdf
2025-09-18 13:21:40,133 - INFO - Processing PDF from S3...
2025-09-18 13:21:40,134 - INFO - Downloading PDF from S3 to /tmp/tmptltl_1xl/676c7509_1_bol_9.pdf
2025-09-18 13:21:40,180 - INFO - 

1_bol_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:40,180 - INFO - 

✓ Saved result: output/run1_1_bol_1.json
2025-09-18 13:21:40,464 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:40,465 - INFO - Splitting PDF 06688bab_1_bol_7 into 1 pages
2025-09-18 13:21:40,466 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ae8ab5c2_1_bol_1.pdf
2025-09-18 13:21:40,467 - INFO - Split PDF into 1 pages
2025-09-18 13:21:40,467 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:40,467 - INFO - Expected pages: [1]
2025-09-18 13:21:41,050 - INFO - Page 1: Extracted 2034 characters, 111 lines from ebeeb6b3_1_bol_10_63008b1a_page_001.pdf
2025-09-18 13:21:41,050 - INFO - Successfully processed page 1
2025-09-18 13:21:41,050 - INFO - Combined 1 pages into final text
2025-09-18 13:21:41,051 - INFO - Text validation for ebeeb6b3_1_bol_10: 2051 characters, 1 pages
2025-09-18 13:21:41,051 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:41,051 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:41,409 - INFO - Page 1: Extracted 1008 characters, 67 lines from f72b3a3a_1_bol_4_2df70d6c_page_001.pdf
2025-09-18 13:21:41,409 - INFO - Successfully processed page 1
2025-09-18 13:21:41,410 - INFO - Combined 1 pages into final text
2025-09-18 13:21:41,410 - INFO - Text validation for f72b3a3a_1_bol_4: 1025 characters, 1 pages
2025-09-18 13:21:41,410 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:41,410 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:41,735 - INFO - Page 1: Extracted 1081 characters, 102 lines from 949ad253_1_bol_11_7ea304b7_page_001.pdf
2025-09-18 13:21:41,735 - INFO - Successfully processed page 1
2025-09-18 13:21:41,737 - INFO - Combined 1 pages into final text
2025-09-18 13:21:41,737 - INFO - Text validation for 949ad253_1_bol_11: 1098 characters, 1 pages
2025-09-18 13:21:41,737 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:41,737 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:41,913 - INFO - Page 1: Extracted 1026 characters, 65 lines from 96085ef7_1_bol_5_c4a58e3b_page_001.pdf
2025-09-18 13:21:41,913 - INFO - Successfully processed page 1
2025-09-18 13:21:41,914 - INFO - Combined 1 pages into final text
2025-09-18 13:21:41,914 - INFO - Text validation for 96085ef7_1_bol_5: 1043 characters, 1 pages
2025-09-18 13:21:41,915 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:41,915 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:42,031 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:42,033 - INFO - Splitting PDF 306cf5ec_1_bol_8 into 1 pages
2025-09-18 13:21:42,036 - INFO - Split PDF into 1 pages
2025-09-18 13:21:42,036 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:42,036 - INFO - Expected pages: [1]
2025-09-18 13:21:42,162 - INFO - Page 1: Extracted 2981 characters, 114 lines from 8e2ba91b_1_bol_2_d8aa7d4c_page_001.pdf
2025-09-18 13:21:42,163 - INFO - Successfully processed page 1
2025-09-18 13:21:42,163 - INFO - Combined 1 pages into final text
2025-09-18 13:21:42,163 - INFO - Text validation for 8e2ba91b_1_bol_2: 2998 characters, 1 pages
2025-09-18 13:21:42,163 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:42,163 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:42,742 - INFO - Splitting PDF into individual pages...
2025-09-18 13:21:42,744 - INFO - Splitting PDF 676c7509_1_bol_9 into 1 pages
2025-09-18 13:21:42,750 - INFO - Split PDF into 1 pages
2025-09-18 13:21:42,750 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:21:42,750 - INFO - Expected pages: [1]
2025-09-18 13:21:42,856 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ebeeb6b3_1_bol_10.pdf
2025-09-18 13:21:42,901 - INFO - 

1_bol_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:42,901 - INFO - 

✓ Saved result: output/run1_1_bol_10.json
2025-09-18 13:21:43,152 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f72b3a3a_1_bol_4.pdf
2025-09-18 13:21:43,185 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ebeeb6b3_1_bol_10.pdf
2025-09-18 13:21:43,213 - INFO - 

1_bol_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:43,213 - INFO - 

✓ Saved result: output/run1_1_bol_4.json
2025-09-18 13:21:43,495 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f72b3a3a_1_bol_4.pdf
2025-09-18 13:21:43,604 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/949ad253_1_bol_11.pdf
2025-09-18 13:21:43,645 - INFO - 

1_bol_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:43,645 - INFO - 

✓ Saved result: output/run1_1_bol_11.json
2025-09-18 13:21:43,815 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/96085ef7_1_bol_5.pdf
2025-09-18 13:21:43,823 - INFO - Page 1: Extracted 2964 characters, 117 lines from 131fca1b_1_bol_6_f144cc93_page_001.pdf
2025-09-18 13:21:43,825 - INFO - Successfully processed page 1
2025-09-18 13:21:43,826 - INFO - Combined 1 pages into final text
2025-09-18 13:21:43,826 - INFO - Text validation for 131fca1b_1_bol_6: 2981 characters, 1 pages
2025-09-18 13:21:43,827 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:43,827 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:43,868 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8e2ba91b_1_bol_2.pdf
2025-09-18 13:21:43,934 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/949ad253_1_bol_11.pdf
2025-09-18 13:21:43,964 - INFO - 

1_bol_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:43,964 - INFO - 

✓ Saved result: output/run1_1_bol_5.json
2025-09-18 13:21:44,251 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/96085ef7_1_bol_5.pdf
2025-09-18 13:21:44,286 - INFO - 

1_bol_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:44,286 - INFO - 

✓ Saved result: output/run1_1_bol_2.json
2025-09-18 13:21:44,573 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8e2ba91b_1_bol_2.pdf
2025-09-18 13:21:45,510 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/131fca1b_1_bol_6.pdf
2025-09-18 13:21:45,552 - INFO - 

1_bol_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:45,552 - INFO - 

✓ Saved result: output/run1_1_bol_6.json
2025-09-18 13:21:45,835 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/131fca1b_1_bol_6.pdf
2025-09-18 13:21:47,011 - INFO - Page 1: Extracted 3577 characters, 94 lines from 7e470e50_1_bol_3_8eebfef3_page_001.pdf
2025-09-18 13:21:47,011 - INFO - Successfully processed page 1
2025-09-18 13:21:47,011 - INFO - Combined 1 pages into final text
2025-09-18 13:21:47,011 - INFO - Text validation for 7e470e50_1_bol_3: 3594 characters, 1 pages
2025-09-18 13:21:47,012 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:47,012 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:48,308 - INFO - Page 1: Extracted 4332 characters, 118 lines from 06688bab_1_bol_7_8ba2a081_page_001.pdf
2025-09-18 13:21:48,308 - INFO - Successfully processed page 1
2025-09-18 13:21:48,309 - INFO - Combined 1 pages into final text
2025-09-18 13:21:48,310 - INFO - Text validation for 06688bab_1_bol_7: 4349 characters, 1 pages
2025-09-18 13:21:48,311 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:48,311 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:49,012 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7e470e50_1_bol_3.pdf
2025-09-18 13:21:49,054 - INFO - 

1_bol_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:49,055 - INFO - 

✓ Saved result: output/run1_1_bol_3.json
2025-09-18 13:21:49,347 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7e470e50_1_bol_3.pdf
2025-09-18 13:21:49,465 - INFO - Page 1: Extracted 4799 characters, 118 lines from 306cf5ec_1_bol_8_a8a0f7e8_page_001.pdf
2025-09-18 13:21:49,465 - INFO - Successfully processed page 1
2025-09-18 13:21:49,465 - INFO - Combined 1 pages into final text
2025-09-18 13:21:49,465 - INFO - Text validation for 306cf5ec_1_bol_8: 4816 characters, 1 pages
2025-09-18 13:21:49,465 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:49,466 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:50,576 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/06688bab_1_bol_7.pdf
2025-09-18 13:21:50,633 - INFO - 

1_bol_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:50,633 - INFO - 

✓ Saved result: output/run1_1_bol_7.json
2025-09-18 13:21:50,873 - INFO - Page 1: Extracted 4253 characters, 172 lines from 676c7509_1_bol_9_e2b75758_page_001.pdf
2025-09-18 13:21:50,873 - INFO - Successfully processed page 1
2025-09-18 13:21:50,874 - INFO - Combined 1 pages into final text
2025-09-18 13:21:50,874 - INFO - Text validation for 676c7509_1_bol_9: 4270 characters, 1 pages
2025-09-18 13:21:50,875 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:21:50,875 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:21:50,930 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/06688bab_1_bol_7.pdf
2025-09-18 13:21:51,497 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/306cf5ec_1_bol_8.pdf
2025-09-18 13:21:51,550 - INFO - 

1_bol_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:51,550 - INFO - 

✓ Saved result: output/run1_1_bol_8.json
2025-09-18 13:21:51,845 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/306cf5ec_1_bol_8.pdf
2025-09-18 13:21:52,661 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/676c7509_1_bol_9.pdf
2025-09-18 13:21:52,717 - INFO - 

1_bol_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:21:52,717 - INFO - 

✓ Saved result: output/run1_1_bol_9.json
2025-09-18 13:21:53,011 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/676c7509_1_bol_9.pdf
2025-09-18 13:21:53,012 - INFO - 
📊 Processing Summary:
2025-09-18 13:21:53,013 - INFO -    Total files: 11
2025-09-18 13:21:53,013 - INFO -    Successful: 11
2025-09-18 13:21:53,013 - INFO -    Failed: 0
2025-09-18 13:21:53,013 - INFO -    Duration: 24.31 seconds
2025-09-18 13:21:53,013 - INFO -    Output directory: output
2025-09-18 13:21:53,013 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:21:53,013 - INFO -    📄 1_bol_1.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_10.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_11.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_3.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_5.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_7.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_8.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,014 - INFO -    📄 1_bol_9.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 13:21:53,015 - INFO - 
========================================================================================================================
2025-09-18 13:21:53,015 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:21:53,015 - INFO - ========================================================================================================================
2025-09-18 13:21:53,015 - INFO - PDF FILE LINK                                                PAGE     CLASSIFIED CATEGORY           
2025-09-18 13:21:53,015 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:21:53,015 - INFO - 1_bol_1.pdf                                                  1        bol                           
2025-09-18 13:21:53,015 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_1.pdf
2025-09-18 13:21:53,016 - INFO - 
2025-09-18 13:21:53,016 - INFO - 1_bol_10.pdf                                                 1        bol                           
2025-09-18 13:21:53,016 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_10.pdf
2025-09-18 13:21:53,016 - INFO - 
2025-09-18 13:21:53,016 - INFO - 1_bol_11.pdf                                                 1        bol                           
2025-09-18 13:21:53,016 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_11.pdf
2025-09-18 13:21:53,016 - INFO - 
2025-09-18 13:21:53,016 - INFO - 1_bol_2.pdf                                                  1        bol                           
2025-09-18 13:21:53,016 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_2.pdf
2025-09-18 13:21:53,016 - INFO - 
2025-09-18 13:21:53,016 - INFO - 1_bol_3.pdf                                                  1        bol                           
2025-09-18 13:21:53,016 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_3.pdf
2025-09-18 13:21:53,016 - INFO - 
2025-09-18 13:21:53,017 - INFO - 1_bol_4.pdf                                                  1        bol                           
2025-09-18 13:21:53,017 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_4.pdf
2025-09-18 13:21:53,017 - INFO - 
2025-09-18 13:21:53,017 - INFO - 1_bol_5.pdf                                                  1        bol                           
2025-09-18 13:21:53,017 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_5.pdf
2025-09-18 13:21:53,017 - INFO - 
2025-09-18 13:21:53,017 - INFO - 1_bol_6.pdf                                                  1        bol                           
2025-09-18 13:21:53,017 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_6.pdf
2025-09-18 13:21:53,017 - INFO - 
2025-09-18 13:21:53,017 - INFO - 1_bol_7.pdf                                                  1        bol                           
2025-09-18 13:21:53,017 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_7.pdf
2025-09-18 13:21:53,017 - INFO - 
2025-09-18 13:21:53,017 - INFO - 1_bol_8.pdf                                                  1        bol                           
2025-09-18 13:21:53,017 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_8.pdf
2025-09-18 13:21:53,018 - INFO - 
2025-09-18 13:21:53,018 - INFO - 1_bol_9.pdf                                                  1        bol                           
2025-09-18 13:21:53,018 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_9.pdf
2025-09-18 13:21:53,018 - INFO - 
2025-09-18 13:21:53,018 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:21:53,018 - INFO - Total entries: 11
2025-09-18 13:21:53,018 - INFO - ========================================================================================================================
2025-09-18 13:21:53,018 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:21:53,018 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:21:53,018 - INFO -   1. 1_bol_1.pdf                              Page 1   → bol
2025-09-18 13:21:53,018 - INFO -   2. 1_bol_10.pdf                             Page 1   → bol
2025-09-18 13:21:53,018 - INFO -   3. 1_bol_11.pdf                             Page 1   → bol
2025-09-18 13:21:53,018 - INFO -   4. 1_bol_2.pdf                              Page 1   → bol
2025-09-18 13:21:53,018 - INFO -   5. 1_bol_3.pdf                              Page 1   → bol
2025-09-18 13:21:53,018 - INFO -   6. 1_bol_4.pdf                              Page 1   → bol
2025-09-18 13:21:53,019 - INFO -   7. 1_bol_5.pdf                              Page 1   → bol
2025-09-18 13:21:53,019 - INFO -   8. 1_bol_6.pdf                              Page 1   → bol
2025-09-18 13:21:53,019 - INFO -   9. 1_bol_7.pdf                              Page 1   → bol
2025-09-18 13:21:53,019 - INFO -  10. 1_bol_8.pdf                              Page 1   → bol
2025-09-18 13:21:53,019 - INFO -  11. 1_bol_9.pdf                              Page 1   → bol
2025-09-18 13:21:53,019 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:21:53,019 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 24.308303, 'processed_files': [{'filename': '1_bol_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_1.pdf'}, {'filename': '1_bol_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_10.pdf'}, {'filename': '1_bol_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_11.pdf'}, {'filename': '1_bol_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_2.pdf'}, {'filename': '1_bol_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_3.pdf'}, {'filename': '1_bol_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_4.pdf'}, {'filename': '1_bol_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_5.pdf'}, {'filename': '1_bol_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_6.pdf'}, {'filename': '1_bol_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_7.pdf'}, {'filename': '1_bol_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_8.pdf'}, {'filename': '1_bol_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/1_bol/1_bol_9.pdf'}]}
