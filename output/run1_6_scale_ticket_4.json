{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9746862649917603, "Height": 1.0, "Left": 0.013133395463228226, "Top": 0.0}, "Polygon": [{"X": 0.01372542418539524, "Y": 0.0}, {"X": 0.9878196716308594, "Y": 9.52283062360948e-06}, {"X": 0.9876441359519958, "Y": 1.0}, {"X": 0.013133395463228226, "Y": 0.999885618686676}]}, "Id": "287ee70f-07c0-4534-974a-a19354d42975", "Relationships": [{"Type": "CHILD", "Ids": ["e013f148-6c89-49d9-ab25-85a4ab88865f", "25527393-82eb-4ad5-b0fd-873f489707ac", "4d428fd5-27b5-4f35-ba7b-7841ad7cbf85", "27051f9f-ae3b-4e65-900a-f88446d74a4c", "36f2f18f-04e5-43d2-b349-c9ba339defa9", "db08c2ec-77c6-4ed6-a17e-478ffb3326f3", "1cadfe3c-9b12-4cf0-8357-26e7331afe25", "e3cc535f-c8c8-45a1-87a1-2d7dcfe1e3a3", "439ab9fe-a7af-48d1-9974-168e98a4558d", "65ac49a4-94b8-4890-ba1c-47503d6a11d0", "851d6a8b-d56f-4b6e-9553-4b9bcbec846a", "1cfa4cd1-3e65-4a3f-8fe2-ef025d63bbb7", "55df045f-bd39-4721-b38a-5ae405a026a4", "d4547dbc-ad0f-4355-8d99-bed1cdcbaa6f", "fdef3350-27af-4878-911a-9ae673e9eb82", "d54537d6-b9aa-41d7-b7ff-4a899557ef88", "04eab481-94da-40f0-b30d-aa4b75c76ecd", "27ea846d-2cd9-455d-9247-080030fe75c6", "72336efb-dd83-4f36-83b4-1f7181fdeec4", "cadd75e3-62ca-4367-bb1e-7b1f1630f21d", "228c704c-a98d-444e-8c1a-801faff88d26", "0ffc9659-1332-4962-8bef-a63618400fcf", "7c21eab1-c8b4-4494-b686-12f419855362", "1e55e8f9-65b2-46b1-ae1f-4d8db67c261a", "2cb76fdc-027e-4096-8f34-edb98e959bd4", "a06fbc6c-a2a0-4a9d-9c4e-a8299fe9c2b6", "45572aad-2ade-4436-9193-d4f187a25639", "121f5fcf-13df-4d61-8db9-32efdfe5e39a", "f302ffc5-352a-4cf2-a5f7-0de8c0976ae0", "0e173cfb-9e19-4ebe-8590-cd7b794ac85d", "ec45fbfe-d038-4472-99a9-51c9e6c7a3c0", "2784e40c-f367-4186-973b-27172baf417d", "4a5675a5-f895-443f-a538-facea687ab18", "cf48f86a-1e95-46e9-b4ac-8e784a6bdb14", "729f9f3d-8f5c-4e69-a189-42a5cac5da02", "e6f2096c-9029-425a-ac2b-c465a7110147", "0e915150-0835-41c2-99dd-0bcb2ef24510", "1fa97677-00e4-447f-84f1-bace255b9e51"]}]}, {"BlockType": "LINE", "Confidence": 99.91755676269531, "Text": "ELECTRONIC SCALE TICKET", "Geometry": {"BoundingBox": {"Width": 0.31597304344177246, "Height": 0.024880940094590187, "Left": 0.3993188440799713, "Top": 0.010396572761237621}, "Polygon": [{"X": 0.39932942390441895, "Y": 0.010396572761237621}, {"X": 0.7152918577194214, "Y": 0.010470840148627758}, {"X": 0.7152846455574036, "Y": 0.03527751564979553}, {"X": 0.3993188440799713, "Y": 0.03520360216498375}]}, "Id": "e013f148-6c89-49d9-ab25-85a4ab88865f", "Relationships": [{"Type": "CHILD", "Ids": ["427ea565-8af1-4f16-87f1-d7caeb205316", "acd091b0-d543-4a1c-bdad-a59a367ac605", "bbad2a40-177b-4f6b-bdfe-b0c65117f5f5"]}]}, {"BlockType": "LINE", "Confidence": 68.63221740722656, "Text": "EIL", "Geometry": {"BoundingBox": {"Width": 0.07379066944122314, "Height": 0.027249764651060104, "Left": 0.7669656872749329, "Top": 0.0038279935251921415}, "Polygon": [{"X": 0.7669730186462402, "Y": 0.0038279935251921415}, {"X": 0.840756356716156, "Y": 0.0038453585002571344}, {"X": 0.8407498598098755, "Y": 0.031077757477760315}, {"X": 0.7669656872749329, "Y": 0.031060483306646347}]}, "Id": "25527393-82eb-4ad5-b0fd-873f489707ac", "Relationships": [{"Type": "CHILD", "Ids": ["3a0aa9cb-c3f5-480a-bef2-75c0305c0de9"]}]}, {"BlockType": "LINE", "Confidence": 73.07328033447266, "Text": "NC", "Geometry": {"BoundingBox": {"Width": 0.06511225551366806, "Height": 0.030571557581424713, "Left": 0.7649180293083191, "Top": 0.03962692245841026}, "Polygon": [{"X": 0.7649262547492981, "Y": 0.03962692245841026}, {"X": 0.8300302624702454, "Y": 0.03964213654398918}, {"X": 0.8300228118896484, "Y": 0.07019847631454468}, {"X": 0.7649180293083191, "Y": 0.07018335163593292}]}, "Id": "4d428fd5-27b5-4f35-ba7b-7841ad7cbf85", "Relationships": [{"Type": "CHILD", "Ids": ["ecb00f6e-f21a-45bb-9b88-79f662924f2a"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "DATE:", "Geometry": {"BoundingBox": {"Width": 0.0560796782374382, "Height": 0.016097577288746834, "Left": 0.16496825218200684, "Top": 0.06779459118843079}, "Polygon": [{"X": 0.16497673094272614, "Y": 0.06779459118843079}, {"X": 0.22104793787002563, "Y": 0.0678076222538948}, {"X": 0.22103984653949738, "Y": 0.08389216661453247}, {"X": 0.16496825218200684, "Y": 0.08387918025255203}]}, "Id": "27051f9f-ae3b-4e65-900a-f88446d74a4c", "Relationships": [{"Type": "CHILD", "Ids": ["d4e2d4ad-cd49-4de3-b6d9-f61738e8eea1"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "MAILING", "Geometry": {"BoundingBox": {"Width": 0.08374965935945511, "Height": 0.016218237578868866, "Left": 0.16687875986099243, "Top": 0.1219445988535881}, "Polygon": [{"X": 0.1668872833251953, "Y": 0.1219445988535881}, {"X": 0.25062841176986694, "Y": 0.12196385115385056}, {"X": 0.2506204843521118, "Y": 0.13816283643245697}, {"X": 0.16687875986099243, "Y": 0.1381436437368393}]}, "Id": "36f2f18f-04e5-43d2-b349-c9ba339defa9", "Relationships": [{"Type": "CHILD", "Ids": ["816ed772-3677-4ec6-ac2d-fa3a0c2aef1a"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "ADDRESS", "Geometry": {"BoundingBox": {"Width": 0.08829937130212784, "Height": 0.016302989795804024, "Left": 0.16692252457141876, "Top": 0.14857086539268494}, "Polygon": [{"X": 0.16693109273910522, "Y": 0.14857086539268494}, {"X": 0.2552219033241272, "Y": 0.1485910713672638}, {"X": 0.2552139461040497, "Y": 0.1648738533258438}, {"X": 0.16692252457141876, "Y": 0.16485372185707092}]}, "Id": "db08c2ec-77c6-4ed6-a17e-478ffb3326f3", "Relationships": [{"Type": "CHILD", "Ids": ["0c241441-9ed4-49ab-ab51-19fd8e6d2799"]}]}, {"BlockType": "LINE", "Confidence": 98.9308853149414, "Text": "DLA DISPOSITION SERVICES - COLUMBUS", "Geometry": {"BoundingBox": {"Width": 0.4137175977230072, "Height": 0.024917976930737495, "Left": 0.30360445380210876, "Top": 0.14137618243694305}, "Polygon": [{"X": 0.30361607670783997, "Y": 0.14137618243694305}, {"X": 0.717322051525116, "Y": 0.14147095382213593}, {"X": 0.7173148393630981, "Y": 0.1662941575050354}, {"X": 0.30360445380210876, "Y": 0.16619983315467834}]}, "Id": "1cadfe3c-9b12-4cf0-8357-26e7331afe25", "Relationships": [{"Type": "CHILD", "Ids": ["cdee5ee5-403a-4475-a875-a6b0f0de26da", "deae9ae4-ff83-442b-9f48-6553d86a639c", "5603f488-95ac-4e34-be12-ab9980a45015", "8f42bc31-7cb2-4441-9cd7-b30f1d3a1a7a", "2387f5c5-a7ce-45c0-a348-91671d38da59"]}]}, {"BlockType": "LINE", "Confidence": 99.4625244140625, "Text": "3990 E. BROAD ST, BLDG 30", "Geometry": {"BoundingBox": {"Width": 0.27718833088874817, "Height": 0.02507944218814373, "Left": 0.3036785423755646, "Top": 0.1669808030128479}, "Polygon": [{"X": 0.30369025468826294, "Y": 0.1669808030128479}, {"X": 0.5808668732643127, "Y": 0.16704398393630981}, {"X": 0.5808581113815308, "Y": 0.19206024706363678}, {"X": 0.3036785423755646, "Y": 0.19199737906455994}]}, "Id": "e3cc535f-c8c8-45a1-87a1-2d7dcfe1e3a3", "Relationships": [{"Type": "CHILD", "Ids": ["91b625a3-b8a8-4f7a-bc0b-aa81bbfbb51b", "32f6bfc6-70c9-42b6-9056-33fe34144970", "bb93a8dc-f9a9-4e36-bf9b-15d99d468e5d", "b84d307b-5ef2-45ce-b889-1d098fdaa0f1", "54621e0a-80d2-47d8-a13f-badd72f53074", "e09d4855-dbf4-4046-aa2f-ce61ac802572"]}]}, {"BlockType": "LINE", "Confidence": 97.92457580566406, "Text": "COLUMBUS, OH 43213", "Geometry": {"BoundingBox": {"Width": 0.23125481605529785, "Height": 0.02756737731397152, "Left": 0.304225891828537, "Top": 0.19232718646526337}, "Polygon": [{"X": 0.30423876643180847, "Y": 0.19232718646526337}, {"X": 0.5354807376861572, "Y": 0.1923796385526657}, {"X": 0.5354705452919006, "Y": 0.21989455819129944}, {"X": 0.304225891828537, "Y": 0.21984240412712097}]}, "Id": "439ab9fe-a7af-48d1-9974-168e98a4558d", "Relationships": [{"Type": "CHILD", "Ids": ["29804e7d-d4e3-4c6f-b6ac-064a54f9dc40", "db025c74-9e80-4cfa-a125-7b31fdfb44da", "86e7586d-27b5-4af2-a2c1-d09ad7526090"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "PH:", "Geometry": {"BoundingBox": {"Width": 0.031554773449897766, "Height": 0.01434240397065878, "Left": 0.17142674326896667, "Top": 0.22778171300888062}, "Polygon": [{"X": 0.17143425345420837, "Y": 0.22778171300888062}, {"X": 0.20298151671886444, "Y": 0.22778882086277008}, {"X": 0.20297418534755707, "Y": 0.24212411046028137}, {"X": 0.17142674326896667, "Y": 0.2421170324087143}]}, "Id": "65ac49a4-94b8-4890-ba1c-47503d6a11d0", "Relationships": [{"Type": "CHILD", "Ids": ["4f8087a6-3ea1-44ad-87a4-f758692f4320"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "614-692-2055", "Geometry": {"BoundingBox": {"Width": 0.13938185572624207, "Height": 0.02168992906808853, "Left": 0.3035326302051544, "Top": 0.22282302379608154}, "Polygon": [{"X": 0.30354276299476624, "Y": 0.22282302379608154}, {"X": 0.4429144859313965, "Y": 0.22285443544387817}, {"X": 0.44290563464164734, "Y": 0.24451294541358948}, {"X": 0.3035326302051544, "Y": 0.2444816678762436}]}, "Id": "851d6a8b-d56f-4b6e-9553-4b9bcbec846a", "Relationships": [{"Type": "CHILD", "Ids": ["8fd5b1f8-78a0-4330-86e8-d37573062b5c"]}]}, {"BlockType": "LINE", "Confidence": 99.84135437011719, "Text": "SHIP TO", "Geometry": {"BoundingBox": {"Width": 0.07392463088035583, "Height": 0.015275117941200733, "Left": 0.17197537422180176, "Top": 0.2545347213745117}, "Polygon": [{"X": 0.17198336124420166, "Y": 0.2545347213745117}, {"X": 0.2458999902009964, "Y": 0.2545512616634369}, {"X": 0.2458924800157547, "Y": 0.2698098123073578}, {"X": 0.17197537422180176, "Y": 0.2697933316230774}]}, "Id": "1cfa4cd1-3e65-4a3f-8fe2-ef025d63bbb7", "Relationships": [{"Type": "CHILD", "Ids": ["ef1d1122-84f6-4d8b-abf9-409d90e4b493", "52109c83-ea83-4324-b849-537298c5887e"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "ADDRESS", "Geometry": {"BoundingBox": {"Width": 0.08604981750249863, "Height": 0.01442012470215559, "Left": 0.17269042134284973, "Top": 0.2812638580799103}, "Polygon": [{"X": 0.17269796133041382, "Y": 0.2812638580799103}, {"X": 0.25874024629592896, "Y": 0.28128302097320557}, {"X": 0.25873321294784546, "Y": 0.29568397998809814}, {"X": 0.17269042134284973, "Y": 0.29566487669944763}]}, "Id": "55df045f-bd39-4721-b38a-5ae405a026a4", "Relationships": [{"Type": "CHILD", "Ids": ["2b382727-cb67-411c-b15d-d1e82a2dec92"]}]}, {"BlockType": "LINE", "Confidence": 98.72389221191406, "Text": "DLA DISPOSITION SERVICES - COLUMBUS", "Geometry": {"BoundingBox": {"Width": 0.4115481376647949, "Height": 0.02423100173473358, "Left": 0.3057725131511688, "Top": 0.26993730664253235}, "Polygon": [{"X": 0.30578380823135376, "Y": 0.26993730664253235}, {"X": 0.7173206806182861, "Y": 0.2700291872024536}, {"X": 0.7173136472702026, "Y": 0.2941683232784271}, {"X": 0.3057725131511688, "Y": 0.2940768897533417}]}, "Id": "d4547dbc-ad0f-4355-8d99-bed1cdcbaa6f", "Relationships": [{"Type": "CHILD", "Ids": ["1b3dbe74-b05b-4459-807e-1c68e5bd83ee", "0f0c06b3-1a6e-4460-820c-c3e3b347e2cf", "0e4b9706-ea3b-4ab2-b109-a3f22c0592c9", "35edd957-27ce-46fe-b694-c3f6677cf753", "69dc59f8-8aa2-4dba-962f-22cc6ac2f1de"]}]}, {"BlockType": "LINE", "Confidence": 99.90872192382812, "Text": "320 N. JAMES RD, BLDG 30", "Geometry": {"BoundingBox": {"Width": 0.26637598872184753, "Height": 0.02317989431321621, "Left": 0.30567094683647156, "Top": 0.2967543303966522}, "Polygon": [{"X": 0.3056817650794983, "Y": 0.2967543303966522}, {"X": 0.5720469355583191, "Y": 0.2968134582042694}, {"X": 0.5720387697219849, "Y": 0.319934219121933}, {"X": 0.30567094683647156, "Y": 0.3198753297328949}]}, "Id": "fdef3350-27af-4878-911a-9ae673e9eb82", "Relationships": [{"Type": "CHILD", "Ids": ["9c1427d8-44c6-49f9-a944-7e59241bc65e", "e89ceb9d-69b2-4119-b51a-c81b0b4c42b4", "46de9f22-8217-4ca4-b552-c3d288da1e3a", "17a05a30-eb39-4b07-acfc-913108f96e8f", "26f30395-2645-42ef-846d-e33f09fd5dcd", "3c992519-067f-44d7-aab2-b1e64b984952"]}]}, {"BlockType": "LINE", "Confidence": 99.09378814697266, "Text": "COLUMBUS, OH 43213", "Geometry": {"BoundingBox": {"Width": 0.22497661411762238, "Height": 0.022814173251390457, "Left": 0.30492866039276123, "Top": 0.3239269554615021}, "Polygon": [{"X": 0.30493929982185364, "Y": 0.3239269554615021}, {"X": 0.5299052596092224, "Y": 0.32397663593292236}, {"X": 0.5298967957496643, "Y": 0.3467411398887634}, {"X": 0.30492866039276123, "Y": 0.34669169783592224}]}, "Id": "d54537d6-b9aa-41d7-b7ff-4a899557ef88", "Relationships": [{"Type": "CHILD", "Ids": ["11ff5dab-938b-4a50-a443-d5c7b2a272d9", "559b39a4-2ab7-47c8-971f-f15652c39e1e", "3be053cf-cd8c-4c6c-b975-144fc8eea1a1"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "REMARKS:", "Geometry": {"BoundingBox": {"Width": 0.09324616938829422, "Height": 0.014375017955899239, "Left": 0.17604592442512512, "Top": 0.3978177309036255}, "Polygon": [{"X": 0.17605341970920563, "Y": 0.3978177309036255}, {"X": 0.26929208636283875, "Y": 0.3978379964828491}, {"X": 0.2692851722240448, "Y": 0.41219276189804077}, {"X": 0.17604592442512512, "Y": 0.4121725261211395}]}, "Id": "04eab481-94da-40f0-b30d-aa4b75c76ecd", "Relationships": [{"Type": "CHILD", "Ids": ["c613de8b-1812-4d8a-8a0b-0273b0c35b0b"]}]}, {"BlockType": "LINE", "Confidence": 11.671416282653809, "Text": "04", "Geometry": {"BoundingBox": {"Width": 0.054424893110990524, "Height": 0.019910182803869247, "Left": 0.6363657712936401, "Top": 0.45869338512420654}, "Polygon": [{"X": 0.6363722681999207, "Y": 0.45869338512420654}, {"X": 0.6907906532287598, "Y": 0.4587050676345825}, {"X": 0.6907846331596375, "Y": 0.4786035716533661}, {"X": 0.6363657712936401, "Y": 0.4785919487476349}]}, "Id": "27ea846d-2cd9-455d-9247-080030fe75c6", "Relationships": [{"Type": "CHILD", "Ids": ["b2b8c52d-9fdb-45de-a9f1-4413a535ebf7"]}]}, {"BlockType": "LINE", "Confidence": 83.23561096191406, "Text": "11-01-22", "Geometry": {"BoundingBox": {"Width": 0.0875665619969368, "Height": 0.017177661880850792, "Left": 0.7373987436294556, "Top": 0.4641183912754059}, "Polygon": [{"X": 0.7374036312103271, "Y": 0.4641183912754059}, {"X": 0.8249652981758118, "Y": 0.46413716673851013}, {"X": 0.8249611258506775, "Y": 0.48129603266716003}, {"X": 0.7373987436294556, "Y": 0.48127734661102295}]}, "Id": "72336efb-dd83-4f36-83b4-1f7181fdeec4", "Relationships": [{"Type": "CHILD", "Ids": ["d8110589-4d40-4d49-ae77-d0b11128ff24"]}]}, {"BlockType": "LINE", "Confidence": 51.12344741821289, "Text": "LOOP to 0661", "Geometry": {"BoundingBox": {"Width": 0.1357785314321518, "Height": 0.019780846312642097, "Left": 0.6439326405525208, "Top": 0.5403339266777039}, "Polygon": [{"X": 0.6439390182495117, "Y": 0.5403339266777039}, {"X": 0.7797111868858337, "Y": 0.5403625965118408}, {"X": 0.7797059416770935, "Y": 0.5601148009300232}, {"X": 0.6439326405525208, "Y": 0.5600862503051758}]}, "Id": "cadd75e3-62ca-4367-bb1e-7b1f1630f21d", "Relationships": [{"Type": "CHILD", "Ids": ["b01ffd5d-bdf6-450e-b8c4-09e708905e34", "238fa4b0-affa-46cc-b1b4-2a805bc483b7", "2deec3c5-a76f-466d-b81c-441280a2614b"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "DRIVER", "Geometry": {"BoundingBox": {"Width": 0.06685557216405869, "Height": 0.013200006447732449, "Left": 0.16791431605815887, "Top": 0.5685729384422302}, "Polygon": [{"X": 0.1679212599992752, "Y": 0.5685729384422302}, {"X": 0.23476989567279816, "Y": 0.5685869455337524}, {"X": 0.23476333916187286, "Y": 0.5817729234695435}, {"X": 0.16791431605815887, "Y": 0.581758975982666}]}, "Id": "228c704c-a98d-444e-8c1a-801faff88d26", "Relationships": [{"Type": "CHILD", "Ids": ["821974ba-1781-4691-b78f-b56474b0c617"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "SIGNATURE:", "Geometry": {"BoundingBox": {"Width": 0.11224745213985443, "Height": 0.014481700025498867, "Left": 0.17075137794017792, "Top": 0.5916962027549744}, "Polygon": [{"X": 0.17075896263122559, "Y": 0.5916962027549744}, {"X": 0.28299883008003235, "Y": 0.5917195677757263}, {"X": 0.2829919457435608, "Y": 0.6061778664588928}, {"X": 0.17075137794017792, "Y": 0.6061545014381409}]}, "Id": "0ffc9659-1332-4962-8bef-a63618400fcf", "Relationships": [{"Type": "CHILD", "Ids": ["7e82bde7-02fb-45bb-aa39-ea5832bd14e8"]}]}, {"BlockType": "LINE", "Confidence": 32.05755615234375, "Text": "INSOLND 32490 15", "Geometry": {"BoundingBox": {"Width": 0.20308881998062134, "Height": 0.01938629522919655, "Left": 0.6445860862731934, "Top": 0.6210573315620422}, "Polygon": [{"X": 0.6445923447608948, "Y": 0.6210573315620422}, {"X": 0.8476749062538147, "Y": 0.6210994124412537}, {"X": 0.8476703763008118, "Y": 0.6404436230659485}, {"X": 0.6445860862731934, "Y": 0.6404016613960266}]}, "Id": "7c21eab1-c8b4-4494-b686-12f419855362", "Relationships": [{"Type": "CHILD", "Ids": ["81ac6d32-68d0-4e29-b00b-db3a24ffdc4a", "93251663-10fc-4f9e-b79c-c17036f85d55", "9beae776-ba2a-4b16-baa1-d72b414705c0"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "WEIGHER:", "Geometry": {"BoundingBox": {"Width": 0.09482968598604202, "Height": 0.01397744845598936, "Left": 0.16569936275482178, "Top": 0.6442453265190125}, "Polygon": [{"X": 0.16570672392845154, "Y": 0.6442453265190125}, {"X": 0.2605290412902832, "Y": 0.6442648768424988}, {"X": 0.2605222761631012, "Y": 0.6582227945327759}, {"X": 0.16569936275482178, "Y": 0.6582033038139343}]}, "Id": "1e55e8f9-65b2-46b1-ae1f-4d8db67c261a", "Relationships": [{"Type": "CHILD", "Ids": ["e4a01173-0bd7-4ba7-9625-c8ca6efb33b4"]}]}, {"BlockType": "LINE", "Confidence": 84.5360336303711, "Text": "12:44", "Geometry": {"BoundingBox": {"Width": 0.055771127343177795, "Height": 0.017212191596627235, "Left": 0.5562214255332947, "Top": 0.6670264005661011}, "Polygon": [{"X": 0.5562276244163513, "Y": 0.6670264005661011}, {"X": 0.6119925379753113, "Y": 0.667037844657898}, {"X": 0.6119867563247681, "Y": 0.684238612651825}, {"X": 0.5562214255332947, "Y": 0.6842271685600281}]}, "Id": "2cb76fdc-027e-4096-8f34-edb98e959bd4", "Relationships": [{"Type": "CHILD", "Ids": ["98067a1f-f4f4-4434-b59c-eddf946a060e"]}]}, {"BlockType": "LINE", "Confidence": 98.08374786376953, "Text": "3152", "Geometry": {"BoundingBox": {"Width": 0.0459548644721508, "Height": 0.016446229070425034, "Left": 0.5659252405166626, "Top": 0.6871525049209595}, "Polygon": [{"X": 0.5659310817718506, "Y": 0.6871525049209595}, {"X": 0.6118801236152649, "Y": 0.6871618628501892}, {"X": 0.6118745803833008, "Y": 0.70359867811203}, {"X": 0.5659252405166626, "Y": 0.7035893797874451}]}, "Id": "a06fbc6c-a2a0-4a9d-9c4e-a8299fe9c2b6", "Relationships": [{"Type": "CHILD", "Ids": ["6bb9206b-6a2c-47c9-93dc-6c510a2edf7b"]}]}, {"BlockType": "LINE", "Confidence": 41.24043273925781, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.39087897539138794, "Height": 0.06406599283218384, "Left": 0.2457098662853241, "Top": 0.7298856973648071}, "Polygon": [{"X": 0.24574139714241028, "Y": 0.7298856973648071}, {"X": 0.6365888714790344, "Y": 0.7299648523330688}, {"X": 0.636568009853363, "Y": 0.793951690196991}, {"X": 0.2457098662853241, "Y": 0.7938737273216248}]}, "Id": "45572aad-2ade-4436-9193-d4f187a25639", "Relationships": [{"Type": "CHILD", "Ids": ["424d294e-54be-4a3a-8a39-623b53356e19", "b37b4932-c017-4a57-aebb-aef816dad204"]}]}, {"BlockType": "LINE", "Confidence": 90.63078308105469, "Text": "14:57", "Geometry": {"BoundingBox": {"Width": 0.055600397288799286, "Height": 0.01779528707265854, "Left": 0.64554762840271, "Top": 0.7462238669395447}, "Polygon": [{"X": 0.6455533504486084, "Y": 0.7462238669395447}, {"X": 0.7011480331420898, "Y": 0.7462350726127625}, {"X": 0.7011427283287048, "Y": 0.7640191316604614}, {"X": 0.64554762840271, "Y": 0.7640079855918884}]}, "Id": "121f5fcf-13df-4d61-8db9-32efdfe5e39a", "Relationships": [{"Type": "CHILD", "Ids": ["964053e5-2e21-4cfd-ab0f-ab50fdc1e960"]}]}, {"BlockType": "LINE", "Confidence": 83.47676086425781, "Text": "11-01-22", "Geometry": {"BoundingBox": {"Width": 0.08937840908765793, "Height": 0.018076036125421524, "Left": 0.7448363900184631, "Top": 0.7487182021141052}, "Polygon": [{"X": 0.744841456413269, "Y": 0.7487182021141052}, {"X": 0.8342148065567017, "Y": 0.7487362027168274}, {"X": 0.8342104554176331, "Y": 0.7667942643165588}, {"X": 0.7448363900184631, "Y": 0.7667763233184814}]}, "Id": "f302ffc5-352a-4cf2-a5f7-0de8c0976ae0", "Relationships": [{"Type": "CHILD", "Ids": ["da77fd68-46d7-4ede-a0e3-aedeec4c9656"]}]}, {"BlockType": "LINE", "Confidence": 92.61419677734375, "Text": "11/2/2020", "Geometry": {"BoundingBox": {"Width": 0.14967866241931915, "Height": 0.03506859764456749, "Left": 0.4705485999584198, "Top": 0.7740284204483032}, "Polygon": [{"X": 0.47056248784065247, "Y": 0.7740284204483032}, {"X": 0.6202272772789001, "Y": 0.7740584015846252}, {"X": 0.6202155947685242, "Y": 0.8090969920158386}, {"X": 0.4705485999584198, "Y": 0.8090672492980957}]}, "Id": "0e173cfb-9e19-4ebe-8590-cd7b794ac85d", "Relationships": [{"Type": "CHILD", "Ids": ["47dc3666-d973-4ca7-ab6a-5b52f410042d"]}]}, {"BlockType": "LINE", "Confidence": 48.84908676147461, "Text": "58260 In GROSS", "Geometry": {"BoundingBox": {"Width": 0.15591120719909668, "Height": 0.01798521727323532, "Left": 0.66522616147995, "Top": 0.8069742918014526}, "Polygon": [{"X": 0.6652317643165588, "Y": 0.8069742918014526}, {"X": 0.8211373686790466, "Y": 0.8070053458213806}, {"X": 0.8211329579353333, "Y": 0.8249595165252686}, {"X": 0.66522616147995, "Y": 0.8249286413192749}]}, "Id": "ec45fbfe-d038-4472-99a9-51c9e6c7a3c0", "Relationships": [{"Type": "CHILD", "Ids": ["4e38300d-f61b-4f7e-9fd8-3a57168a0818", "23bbe30b-56d2-4641-9a48-879ae6d1e6e4", "04111ae1-b69f-46d4-8afd-1b27529ad995"]}]}, {"BlockType": "LINE", "Confidence": 35.57916259765625, "Text": "PA", "Geometry": {"BoundingBox": {"Width": 0.3912660479545593, "Height": 0.10972226411104202, "Left": 0.15533116459846497, "Top": 0.8131336569786072}, "Polygon": [{"X": 0.15538941323757172, "Y": 0.8131336569786072}, {"X": 0.5465972423553467, "Y": 0.8132113218307495}, {"X": 0.5465573072433472, "Y": 0.9228559136390686}, {"X": 0.15533116459846497, "Y": 0.9227801561355591}]}, "Id": "2784e40c-f367-4186-973b-27172baf417d", "Relationships": [{"Type": "CHILD", "Ids": ["62dd5793-7bef-41a8-83cd-679d2610956d"]}]}, {"BlockType": "LINE", "Confidence": 42.23014831542969, "Text": "400", "Geometry": {"BoundingBox": {"Width": 0.034365251660346985, "Height": 0.016346897929906845, "Left": 0.6903668642044067, "Top": 0.8265507817268372}, "Polygon": [{"X": 0.6903718113899231, "Y": 0.8265507817268372}, {"X": 0.7247321605682373, "Y": 0.8265575766563416}, {"X": 0.7247274518013, "Y": 0.8428977131843567}, {"X": 0.6903668642044067, "Y": 0.8428909182548523}]}, "Id": "4a5675a5-f895-443f-a538-facea687ab18", "Relationships": [{"Type": "CHILD", "Ids": ["86da4d33-d7cb-4560-8575-8214e1205a0f"]}]}, {"BlockType": "LINE", "Confidence": 27.813297271728516, "Text": "5", "Geometry": {"BoundingBox": {"Width": 0.021958019584417343, "Height": 0.015524042770266533, "Left": 0.7364304065704346, "Top": 0.8285086750984192}, "Polygon": [{"X": 0.736434817314148, "Y": 0.8285086750984192}, {"X": 0.7583884596824646, "Y": 0.8285130262374878}, {"X": 0.7583842277526855, "Y": 0.8440327048301697}, {"X": 0.7364304065704346, "Y": 0.8440284132957458}]}, "Id": "cf48f86a-1e95-46e9-b4ac-8e784a6bdb14", "Relationships": [{"Type": "CHILD", "Ids": ["0afc4f4a-a363-4678-bd40-8cca5c99c7ca"]}]}, {"BlockType": "LINE", "Confidence": 72.61260223388672, "Text": "TARE", "Geometry": {"BoundingBox": {"Width": 0.04690805822610855, "Height": 0.01722428947687149, "Left": 0.766647219657898, "Top": 0.8278085589408875}, "Polygon": [{"X": 0.7666518688201904, "Y": 0.8278085589408875}, {"X": 0.8135553002357483, "Y": 0.8278177976608276}, {"X": 0.8135509490966797, "Y": 0.845032811164856}, {"X": 0.766647219657898, "Y": 0.8450235724449158}]}, "Id": "729f9f3d-8f5c-4e69-a189-42a5cac5da02", "Relationships": [{"Type": "CHILD", "Ids": ["251f38c7-8b60-4439-a78d-dcfd77e58851"]}]}, {"BlockType": "LINE", "Confidence": 61.77196502685547, "Text": "in NET", "Geometry": {"BoundingBox": {"Width": 0.06661691516637802, "Height": 0.01607624627649784, "Left": 0.7357911467552185, "Top": 0.8482106328010559}, "Polygon": [{"X": 0.7357957363128662, "Y": 0.8482106328010559}, {"X": 0.8024080991744995, "Y": 0.8482237458229065}, {"X": 0.80240398645401, "Y": 0.8642868995666504}, {"X": 0.7357911467552185, "Y": 0.8642738461494446}]}, "Id": "e6f2096c-9029-425a-ac2b-c465a7110147", "Relationships": [{"Type": "CHILD", "Ids": ["89e20459-f02b-4fa9-9c0e-e5f74f64a3dd", "4be8c269-880c-4670-ab8c-d3106d69a519"]}]}, {"BlockType": "LINE", "Confidence": 21.733198165893555, "Text": "&", "Geometry": {"BoundingBox": {"Width": 0.01951032504439354, "Height": 0.01703607477247715, "Left": 0.5947649478912354, "Top": 0.8667954206466675}, "Polygon": [{"X": 0.5947707891464233, "Y": 0.8667954206466675}, {"X": 0.6142752766609192, "Y": 0.8667992353439331}, {"X": 0.6142695546150208, "Y": 0.8838315010070801}, {"X": 0.5947649478912354, "Y": 0.8838276863098145}]}, "Id": "0e915150-0835-41c2-99dd-0bcb2ef24510", "Relationships": [{"Type": "CHILD", "Ids": ["c1854609-dc68-4766-b3ff-aed0b45ac6e2"]}]}, {"BlockType": "LINE", "Confidence": 89.41107177734375, "Text": "11-2-22", "Geometry": {"BoundingBox": {"Width": 0.1454796940088272, "Height": 0.024066345766186714, "Left": 0.3973536789417267, "Top": 0.9159774780273438}, "Polygon": [{"X": 0.39736396074295044, "Y": 0.9159774780273438}, {"X": 0.5428333878517151, "Y": 0.9160057306289673}, {"X": 0.5428245663642883, "Y": 0.9400438666343689}, {"X": 0.3973536789417267, "Y": 0.9400157928466797}]}, "Id": "1fa97677-00e4-447f-84f1-bace255b9e51", "Relationships": [{"Type": "CHILD", "Ids": ["b0c57f28-2487-4002-80d4-44eca6a17594"]}]}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "ELECTRONIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14704151451587677, "Height": 0.024841424077749252, "Left": 0.3993188440799713, "Top": 0.010396572761237621}, "Polygon": [{"X": 0.39932942390441895, "Y": 0.010396572761237621}, {"X": 0.5463603734970093, "Y": 0.010431132279336452}, {"X": 0.5463513135910034, "Y": 0.03523799777030945}, {"X": 0.3993188440799713, "Y": 0.03520360216498375}]}, "Id": "427ea565-8af1-4f16-87f1-d7caeb205316"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0724017471075058, "Height": 0.017427686601877213, "Left": 0.5528454780578613, "Top": 0.011306204833090305}, "Polygon": [{"X": 0.5528517961502075, "Y": 0.011306204833090305}, {"X": 0.6252472400665283, "Y": 0.011323218233883381}, {"X": 0.6252414584159851, "Y": 0.028733890503644943}, {"X": 0.5528454780578613, "Y": 0.028716934844851494}]}, "Id": "acd091b0-d543-4a1c-bdad-a59a367ac605"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "TICKET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08304726332426071, "Height": 0.017754187807440758, "Left": 0.6322442889213562, "Top": 0.011528587900102139}, "Polygon": [{"X": 0.6322500705718994, "Y": 0.011528587900102139}, {"X": 0.7152915596961975, "Y": 0.011548102833330631}, {"X": 0.715286374092102, "Y": 0.02928277477622032}, {"X": 0.6322442889213562, "Y": 0.0292633268982172}]}, "Id": "bbad2a40-177b-4f6b-bdfe-b0c65117f5f5"}, {"BlockType": "WORD", "Confidence": 68.63221740722656, "Text": "EIL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07379066944122314, "Height": 0.027249764651060104, "Left": 0.7669656872749329, "Top": 0.0038279935251921415}, "Polygon": [{"X": 0.7669730186462402, "Y": 0.0038279935251921415}, {"X": 0.840756356716156, "Y": 0.0038453585002571344}, {"X": 0.8407498598098755, "Y": 0.031077757477760315}, {"X": 0.7669656872749329, "Y": 0.031060483306646347}]}, "Id": "3a0aa9cb-c3f5-480a-bef2-75c0305c0de9"}, {"BlockType": "WORD", "Confidence": 73.07328033447266, "Text": "NC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06511225551366806, "Height": 0.030571557581424713, "Left": 0.7649180293083191, "Top": 0.03962692245841026}, "Polygon": [{"X": 0.7649262547492981, "Y": 0.03962692245841026}, {"X": 0.8300302624702454, "Y": 0.03964213654398918}, {"X": 0.8300228118896484, "Y": 0.07019847631454468}, {"X": 0.7649180293083191, "Y": 0.07018335163593292}]}, "Id": "ecb00f6e-f21a-45bb-9b88-79f662924f2a"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0560796782374382, "Height": 0.016097577288746834, "Left": 0.16496825218200684, "Top": 0.06779459118843079}, "Polygon": [{"X": 0.16497673094272614, "Y": 0.06779459118843079}, {"X": 0.22104793787002563, "Y": 0.0678076222538948}, {"X": 0.22103984653949738, "Y": 0.08389216661453247}, {"X": 0.16496825218200684, "Y": 0.08387918025255203}]}, "Id": "d4e2d4ad-cd49-4de3-b6d9-f61738e8eea1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "MAILING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08374965935945511, "Height": 0.016218237578868866, "Left": 0.16687875986099243, "Top": 0.1219445988535881}, "Polygon": [{"X": 0.1668872833251953, "Y": 0.1219445988535881}, {"X": 0.25062841176986694, "Y": 0.12196385115385056}, {"X": 0.2506204843521118, "Y": 0.13816283643245697}, {"X": 0.16687875986099243, "Y": 0.1381436437368393}]}, "Id": "816ed772-3677-4ec6-ac2d-fa3a0c2aef1a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ADDRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08829937130212784, "Height": 0.016302989795804024, "Left": 0.16692252457141876, "Top": 0.14857086539268494}, "Polygon": [{"X": 0.16693109273910522, "Y": 0.14857086539268494}, {"X": 0.2552219033241272, "Y": 0.1485910713672638}, {"X": 0.2552139461040497, "Y": 0.1648738533258438}, {"X": 0.16692252457141876, "Y": 0.16485372185707092}]}, "Id": "0c241441-9ed4-49ab-ab51-19fd8e6d2799"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "DLA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03958554193377495, "Height": 0.014810402877628803, "Left": 0.3036046624183655, "Top": 0.15100052952766418}, "Polygon": [{"X": 0.3036115765571594, "Y": 0.15100052952766418}, {"X": 0.34319019317626953, "Y": 0.15100957453250885}, {"X": 0.3431835174560547, "Y": 0.16581092774868011}, {"X": 0.3036046624183655, "Y": 0.16580189764499664}]}, "Id": "cdee5ee5-403a-4475-a875-a6b0f0de26da"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "DISPOSITION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12665703892707825, "Height": 0.02270433120429516, "Left": 0.3484846353530884, "Top": 0.1435346156358719}, "Polygon": [{"X": 0.3484947979450226, "Y": 0.1435346156358719}, {"X": 0.4751416742801666, "Y": 0.14356361329555511}, {"X": 0.47513270378112793, "Y": 0.1662389487028122}, {"X": 0.3484846353530884, "Y": 0.16621007025241852}]}, "Id": "deae9ae4-ff83-442b-9f48-6553d86a639c"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "SERVICES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0963425561785698, "Height": 0.015337194316089153, "Left": 0.4805351197719574, "Top": 0.14141671359539032}, "Polygon": [{"X": 0.4805411100387573, "Y": 0.14141671359539032}, {"X": 0.5768776535987854, "Y": 0.1414387822151184}, {"X": 0.5768722891807556, "Y": 0.15675391256809235}, {"X": 0.4805351197719574, "Y": 0.15673190355300903}]}, "Id": "5603f488-95ac-4e34-be12-ab9980a45015"}, {"BlockType": "WORD", "Confidence": 95.11579132080078, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007777062710374594, "Height": 0.003729692194610834, "Left": 0.5825867056846619, "Top": 0.14858977496623993}, "Polygon": [{"X": 0.5825880169868469, "Y": 0.14858977496623993}, {"X": 0.5903638005256653, "Y": 0.1485915631055832}, {"X": 0.5903624892234802, "Y": 0.15231947600841522}, {"X": 0.5825867056846619, "Y": 0.15231770277023315}]}, "Id": "8f42bc31-7cb2-4441-9cd7-b30f1d3a1a7a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "COLUMBUS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12067362666130066, "Height": 0.014433598145842552, "Left": 0.5966482162475586, "Top": 0.1422593891620636}, "Polygon": [{"X": 0.596653163433075, "Y": 0.1422593891620636}, {"X": 0.7173218131065369, "Y": 0.14228703081607819}, {"X": 0.7173176407814026, "Y": 0.1566929817199707}, {"X": 0.5966482162475586, "Y": 0.15666542947292328}]}, "Id": "2387f5c5-a7ce-45c0-a348-91671d38da59"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "3990", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04977942630648613, "Height": 0.015109634026885033, "Left": 0.3036785423755646, "Top": 0.1768990308046341}, "Polygon": [{"X": 0.30368560552597046, "Y": 0.1768990308046341}, {"X": 0.3534579575061798, "Y": 0.17691035568714142}, {"X": 0.3534512221813202, "Y": 0.19200865924358368}, {"X": 0.3036785423755646, "Y": 0.19199737906455994}]}, "Id": "91b625a3-b8a8-4f7a-bc0b-aa81bbfbb51b"}, {"BlockType": "WORD", "Confidence": 97.25447082519531, "Text": "E.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015978273004293442, "Height": 0.013894418254494667, "Left": 0.35990235209465027, "Top": 0.17658782005310059}, "Polygon": [{"X": 0.3599085211753845, "Y": 0.17658782005310059}, {"X": 0.375880628824234, "Y": 0.17659145593643188}, {"X": 0.3758745491504669, "Y": 0.1904822438955307}, {"X": 0.35990235209465027, "Y": 0.1904786080121994}]}, "Id": "32f6bfc6-70c9-42b6-9056-33fe34144970"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "BROAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06965865939855576, "Height": 0.018472004681825638, "Left": 0.3827837407588959, "Top": 0.1710250824689865}, "Polygon": [{"X": 0.38279175758361816, "Y": 0.1710250824689865}, {"X": 0.45244237780570984, "Y": 0.17104095220565796}, {"X": 0.4524349272251129, "Y": 0.18949709832668304}, {"X": 0.3827837407588959, "Y": 0.18948128819465637}]}, "Id": "bb93a8dc-f9a9-4e36-bf9b-15d99d468e5d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ST,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02897030860185623, "Height": 0.016793159767985344, "Left": 0.45798707008361816, "Top": 0.16899298131465912}, "Polygon": [{"X": 0.45799383521080017, "Y": 0.16899298131465912}, {"X": 0.4869574010372162, "Y": 0.168999582529068}, {"X": 0.4869508445262909, "Y": 0.1857861429452896}, {"X": 0.45798707008361816, "Y": 0.18577957153320312}]}, "Id": "b84d307b-5ef2-45ce-b889-1d098fdaa0f1"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "BLDG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05420282110571861, "Height": 0.014812265522778034, "Left": 0.49435052275657654, "Top": 0.16702425479888916}, "Polygon": [{"X": 0.4943562150001526, "Y": 0.16702425479888916}, {"X": 0.5485533475875854, "Y": 0.16703660786151886}, {"X": 0.5485479831695557, "Y": 0.18183653056621552}, {"X": 0.49435052275657654, "Y": 0.1818242073059082}]}, "Id": "54621e0a-80d2-47d8-a13f-badd72f53074"}, {"BlockType": "WORD", "Confidence": 99.68669891357422, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02583177760243416, "Height": 0.014258000999689102, "Left": 0.5550350546836853, "Top": 0.16708414256572723}, "Polygon": [{"X": 0.5550402402877808, "Y": 0.16708414256572723}, {"X": 0.5808668732643127, "Y": 0.16709004342556}, {"X": 0.5808618664741516, "Y": 0.18134215474128723}, {"X": 0.5550350546836853, "Y": 0.18133628368377686}]}, "Id": "e09d4855-dbf4-4046-aa2f-ce61ac802572"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "COLUMBUS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11954750120639801, "Height": 0.02146313339471817, "Left": 0.304225891828537, "Top": 0.19840623438358307}, "Polygon": [{"X": 0.30423590540885925, "Y": 0.19840623438358307}, {"X": 0.4237733781337738, "Y": 0.1984333097934723}, {"X": 0.4237644672393799, "Y": 0.21986937522888184}, {"X": 0.304225891828537, "Y": 0.21984240412712097}]}, "Id": "29804e7d-d4e3-4c6f-b6ac-064a54f9dc40"}, {"BlockType": "WORD", "Confidence": 94.43020629882812, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030667968094348907, "Height": 0.015181094408035278, "Left": 0.4297281503677368, "Top": 0.19649232923984528}, "Polygon": [{"X": 0.4297344386577606, "Y": 0.19649232923984528}, {"X": 0.4603961408138275, "Y": 0.1964992880821228}, {"X": 0.46039003133773804, "Y": 0.21167342364788055}, {"X": 0.4297281503677368, "Y": 0.2116665095090866}]}, "Id": "db025c74-9e80-4cfa-a125-7b31fdfb44da"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "43213", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06477638334035873, "Height": 0.017509449273347855, "Left": 0.4707043170928955, "Top": 0.19236494600772858}, "Polygon": [{"X": 0.47071126103401184, "Y": 0.19236494600772858}, {"X": 0.5354807376861572, "Y": 0.1923796385526657}, {"X": 0.5354742407798767, "Y": 0.20987439155578613}, {"X": 0.4707043170928955, "Y": 0.20985975861549377}]}, "Id": "86e7586d-27b5-4af2-a2c1-d09ad7526090"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PH:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031554773449897766, "Height": 0.01434240397065878, "Left": 0.17142674326896667, "Top": 0.22778171300888062}, "Polygon": [{"X": 0.17143425345420837, "Y": 0.22778171300888062}, {"X": 0.20298151671886444, "Y": 0.22778882086277008}, {"X": 0.20297418534755707, "Y": 0.24212411046028137}, {"X": 0.17142674326896667, "Y": 0.2421170324087143}]}, "Id": "4f8087a6-3ea1-44ad-87a4-f758692f4320"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "614-692-2055", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13938185572624207, "Height": 0.02168992906808853, "Left": 0.3035326302051544, "Top": 0.22282302379608154}, "Polygon": [{"X": 0.30354276299476624, "Y": 0.22282302379608154}, {"X": 0.4429144859313965, "Y": 0.22285443544387817}, {"X": 0.44290563464164734, "Y": 0.24451294541358948}, {"X": 0.3035326302051544, "Y": 0.2444816678762436}]}, "Id": "8fd5b1f8-78a0-4330-86e8-d37573062b5c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "SHIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042304426431655884, "Height": 0.014471771195530891, "Left": 0.17197579145431519, "Top": 0.2545347213745117}, "Polygon": [{"X": 0.17198336124420166, "Y": 0.2545347213745117}, {"X": 0.21428021788597107, "Y": 0.2545441687107086}, {"X": 0.2142728865146637, "Y": 0.26900649070739746}, {"X": 0.17197579145431519, "Y": 0.26899704337120056}]}, "Id": "ef1d1122-84f6-4d8b-abf9-409d90e4b493"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02669389545917511, "Height": 0.014171546325087547, "Left": 0.2192055583000183, "Top": 0.2556382715702057}, "Polygon": [{"X": 0.21921269595623016, "Y": 0.2556382715702057}, {"X": 0.24589945375919342, "Y": 0.2556442618370056}, {"X": 0.2458924800157547, "Y": 0.2698098123073578}, {"X": 0.2192055583000183, "Y": 0.26980385184288025}]}, "Id": "52109c83-ea83-4324-b849-537298c5887e"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ADDRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08604981750249863, "Height": 0.01442012470215559, "Left": 0.17269042134284973, "Top": 0.2812638580799103}, "Polygon": [{"X": 0.17269796133041382, "Y": 0.2812638580799103}, {"X": 0.25874024629592896, "Y": 0.28128302097320557}, {"X": 0.25873321294784546, "Y": 0.29568397998809814}, {"X": 0.17269042134284973, "Y": 0.29566487669944763}]}, "Id": "2b382727-cb67-411c-b15d-d1e82a2dec92"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DLA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038502130657434464, "Height": 0.014192958362400532, "Left": 0.3057725131511688, "Top": 0.2798924744129181}, "Polygon": [{"X": 0.3057791590690613, "Y": 0.2798924744129181}, {"X": 0.3442746698856354, "Y": 0.27990105748176575}, {"X": 0.344268262386322, "Y": 0.29408544301986694}, {"X": 0.3057725131511688, "Y": 0.2940768897533417}]}, "Id": "1b3dbe74-b05b-4459-807e-1c68e5bd83ee"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "DISPOSITION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12701693177223206, "Height": 0.020233789458870888, "Left": 0.3492412567138672, "Top": 0.2734990417957306}, "Polygon": [{"X": 0.34925034642219543, "Y": 0.2734990417957306}, {"X": 0.47625818848609924, "Y": 0.2735273838043213}, {"X": 0.47625023126602173, "Y": 0.29373282194137573}, {"X": 0.3492412567138672, "Y": 0.293704628944397}]}, "Id": "0f0c06b3-1a6e-4460-820c-c3e3b347e2cf"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "SERVICES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09675949066877365, "Height": 0.0165406484156847, "Left": 0.48094213008880615, "Top": 0.27088695764541626}, "Polygon": [{"X": 0.48094862699508667, "Y": 0.27088695764541626}, {"X": 0.5777016282081604, "Y": 0.27090853452682495}, {"X": 0.5776958465576172, "Y": 0.2874276041984558}, {"X": 0.48094213008880615, "Y": 0.2874060571193695}]}, "Id": "0e4b9706-ea3b-4ab2-b109-a3f22c0592c9"}, {"BlockType": "WORD", "Confidence": 94.19642639160156, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00809548981487751, "Height": 0.0036270671989768744, "Left": 0.5829697251319885, "Top": 0.277271032333374}, "Polygon": [{"X": 0.5829709768295288, "Y": 0.277271032333374}, {"X": 0.5910652279853821, "Y": 0.2772728502750397}, {"X": 0.5910639762878418, "Y": 0.2808981239795685}, {"X": 0.5829697251319885, "Y": 0.28089630603790283}]}, "Id": "35edd957-27ce-46fe-b694-c3f6677cf753"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "COLUMBUS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12040164321660995, "Height": 0.01643431931734085, "Left": 0.596919059753418, "Top": 0.2700023353099823}, "Polygon": [{"X": 0.5969246625900269, "Y": 0.2700023353099823}, {"X": 0.7173206806182861, "Y": 0.2700291872024536}, {"X": 0.7173159122467041, "Y": 0.28643664717674255}, {"X": 0.596919059753418, "Y": 0.286409854888916}]}, "Id": "69dc59f8-8aa2-4dba-962f-22cc6ac2f1de"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "320", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03642487898468971, "Height": 0.01473768800497055, "Left": 0.30567094683647156, "Top": 0.3051457107067108}, "Polygon": [{"X": 0.3056778311729431, "Y": 0.3051457107067108}, {"X": 0.34209582209587097, "Y": 0.3051537871360779}, {"X": 0.3420891761779785, "Y": 0.31988340616226196}, {"X": 0.30567094683647156, "Y": 0.3198753297328949}]}, "Id": "9c1427d8-44c6-49f9-a944-7e59241bc65e"}, {"BlockType": "WORD", "Confidence": 99.73552703857422, "Text": "N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019578561186790466, "Height": 0.013876480981707573, "Left": 0.34823429584503174, "Top": 0.3045549988746643}, "Polygon": [{"X": 0.34824052453041077, "Y": 0.3045549988746643}, {"X": 0.3678128719329834, "Y": 0.3045593500137329}, {"X": 0.3678067624568939, "Y": 0.3184314966201782}, {"X": 0.34823429584503174, "Y": 0.318427175283432}]}, "Id": "e89ceb9d-69b2-4119-b51a-c81b0b4c42b4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "JAMES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06625821441411972, "Height": 0.017166364938020706, "Left": 0.3734114170074463, "Top": 0.30113789439201355}, "Polygon": [{"X": 0.373418927192688, "Y": 0.30113789439201355}, {"X": 0.4396696388721466, "Y": 0.30115261673927307}, {"X": 0.4396626055240631, "Y": 0.31830427050590515}, {"X": 0.3734114170074463, "Y": 0.3182896375656128}]}, "Id": "46de9f22-8217-4ca4-b552-c3d288da1e3a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "RD,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03326067700982094, "Height": 0.01660206913948059, "Left": 0.4454376995563507, "Top": 0.29998311400413513}, "Polygon": [{"X": 0.4454444646835327, "Y": 0.29998311400413513}, {"X": 0.47869837284088135, "Y": 0.2999904751777649}, {"X": 0.47869184613227844, "Y": 0.3165851831436157}, {"X": 0.4454376995563507, "Y": 0.31657782196998596}]}, "Id": "17a05a30-eb39-4b07-acfc-913108f96e8f"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "BLDG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05386074259877205, "Height": 0.01557063590735197, "Left": 0.48580920696258545, "Top": 0.29747021198272705}, "Polygon": [{"X": 0.48581525683403015, "Y": 0.29747021198272705}, {"X": 0.539669930934906, "Y": 0.2974821627140045}, {"X": 0.5396642088890076, "Y": 0.3130408227443695}, {"X": 0.48580920696258545, "Y": 0.3130289316177368}]}, "Id": "26f30395-2645-42ef-846d-e33f09fd5dcd"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025886572897434235, "Height": 0.015033701434731483, "Left": 0.5461603403091431, "Top": 0.2968077063560486}, "Polygon": [{"X": 0.5461658239364624, "Y": 0.2968077063560486}, {"X": 0.5720469355583191, "Y": 0.2968134582042694}, {"X": 0.5720416307449341, "Y": 0.3118414282798767}, {"X": 0.5461603403091431, "Y": 0.3118356764316559}]}, "Id": "3c992519-067f-44d7-aab2-b1e64b984952"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "COLUMBUS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11958572268486023, "Height": 0.01907452568411827, "Left": 0.30492866039276123, "Top": 0.3276434540748596}, "Polygon": [{"X": 0.30493757128715515, "Y": 0.3276434540748596}, {"X": 0.42451438307762146, "Y": 0.32766982913017273}, {"X": 0.42450645565986633, "Y": 0.3467179834842682}, {"X": 0.30492866039276123, "Y": 0.34669169783592224}]}, "Id": "11ff5dab-938b-4a50-a443-d5c7b2a272d9"}, {"BlockType": "WORD", "Confidence": 97.71903228759766, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030391449108719826, "Height": 0.014730686321854591, "Left": 0.4303402900695801, "Top": 0.3261784017086029}, "Polygon": [{"X": 0.43034636974334717, "Y": 0.3261784017086029}, {"X": 0.46073171496391296, "Y": 0.32618510723114014}, {"X": 0.4607258141040802, "Y": 0.34090909361839294}, {"X": 0.4303402900695801, "Y": 0.3409024178981781}]}, "Id": "559b39a4-2ab7-47c8-971f-f15652c39e1e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "43213", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06362996250391006, "Height": 0.015708114951848984, "Left": 0.46627530455589294, "Top": 0.32396259903907776}, "Polygon": [{"X": 0.46628156304359436, "Y": 0.32396259903907776}, {"X": 0.5299052596092224, "Y": 0.32397663593292236}, {"X": 0.5298994183540344, "Y": 0.33967071771621704}, {"X": 0.46627530455589294, "Y": 0.3396567106246948}]}, "Id": "3be053cf-cd8c-4c6c-b975-144fc8eea1a1"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "REMARKS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09324616938829422, "Height": 0.014375017955899239, "Left": 0.17604592442512512, "Top": 0.3978177309036255}, "Polygon": [{"X": 0.17605341970920563, "Y": 0.3978177309036255}, {"X": 0.26929208636283875, "Y": 0.3978379964828491}, {"X": 0.2692851722240448, "Y": 0.41219276189804077}, {"X": 0.17604592442512512, "Y": 0.4121725261211395}]}, "Id": "c613de8b-1812-4d8a-8a0b-0273b0c35b0b"}, {"BlockType": "WORD", "Confidence": 11.671416282653809, "Text": "04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054424893110990524, "Height": 0.019910182803869247, "Left": 0.6363657712936401, "Top": 0.45869338512420654}, "Polygon": [{"X": 0.6363722681999207, "Y": 0.45869338512420654}, {"X": 0.6907906532287598, "Y": 0.4587050676345825}, {"X": 0.6907846331596375, "Y": 0.4786035716533661}, {"X": 0.6363657712936401, "Y": 0.4785919487476349}]}, "Id": "b2b8c52d-9fdb-45de-a9f1-4413a535ebf7"}, {"BlockType": "WORD", "Confidence": 83.23561096191406, "Text": "11-01-22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0875665619969368, "Height": 0.017177661880850792, "Left": 0.7373987436294556, "Top": 0.4641183912754059}, "Polygon": [{"X": 0.7374036312103271, "Y": 0.4641183912754059}, {"X": 0.8249652981758118, "Y": 0.46413716673851013}, {"X": 0.8249611258506775, "Y": 0.48129603266716003}, {"X": 0.7373987436294556, "Y": 0.48127734661102295}]}, "Id": "d8110589-4d40-4d49-ae77-d0b11128ff24"}, {"BlockType": "WORD", "Confidence": 90.07015228271484, "Text": "LOOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0488399937748909, "Height": 0.01895984448492527, "Left": 0.6439329385757446, "Top": 0.5403339266777039}, "Polygon": [{"X": 0.6439390182495117, "Y": 0.5403339266777039}, {"X": 0.6927729249000549, "Y": 0.54034423828125}, {"X": 0.6927672028541565, "Y": 0.559293806552887}, {"X": 0.6439329385757446, "Y": 0.5592835545539856}]}, "Id": "b01ffd5d-bdf6-450e-b8c4-09e708905e34"}, {"BlockType": "WORD", "Confidence": 41.240631103515625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02260267548263073, "Height": 0.01715119183063507, "Left": 0.7030095458030701, "Top": 0.5419790744781494}, "Polygon": [{"X": 0.7030146718025208, "Y": 0.5419790744781494}, {"X": 0.725612223148346, "Y": 0.5419838428497314}, {"X": 0.7256073355674744, "Y": 0.5591302514076233}, {"X": 0.7030095458030701, "Y": 0.5591254830360413}]}, "Id": "238fa4b0-affa-46cc-b1b4-2a805bc483b7"}, {"BlockType": "WORD", "Confidence": 22.059551239013672, "Text": "0661", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04499681293964386, "Height": 0.016497431322932243, "Left": 0.7347134947776794, "Top": 0.5436173677444458}, "Polygon": [{"X": 0.7347182035446167, "Y": 0.5436173677444458}, {"X": 0.7797103524208069, "Y": 0.5436268448829651}, {"X": 0.7797059416770935, "Y": 0.5601148009300232}, {"X": 0.7347134947776794, "Y": 0.5601053237915039}]}, "Id": "2deec3c5-a76f-466d-b81c-441280a2614b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DRIVER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06685557216405869, "Height": 0.013200006447732449, "Left": 0.16791431605815887, "Top": 0.5685729384422302}, "Polygon": [{"X": 0.1679212599992752, "Y": 0.5685729384422302}, {"X": 0.23476989567279816, "Y": 0.5685869455337524}, {"X": 0.23476333916187286, "Y": 0.5817729234695435}, {"X": 0.16791431605815887, "Y": 0.581758975982666}]}, "Id": "821974ba-1781-4691-b78f-b56474b0c617"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "SIGNATURE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11224745213985443, "Height": 0.014481700025498867, "Left": 0.17075137794017792, "Top": 0.5916962027549744}, "Polygon": [{"X": 0.17075896263122559, "Y": 0.5916962027549744}, {"X": 0.28299883008003235, "Y": 0.5917195677757263}, {"X": 0.2829919457435608, "Y": 0.6061778664588928}, {"X": 0.17075137794017792, "Y": 0.6061545014381409}]}, "Id": "7e82bde7-02fb-45bb-aa39-ea5832bd14e8"}, {"BlockType": "WORD", "Confidence": 40.36272430419922, "Text": "INSOLND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07997429370880127, "Height": 0.017756110057234764, "Left": 0.6445866227149963, "Top": 0.6210573315620422}, "Polygon": [{"X": 0.6445923447608948, "Y": 0.6210573315620422}, {"X": 0.7245609164237976, "Y": 0.6210739016532898}, {"X": 0.7245557904243469, "Y": 0.6388134360313416}, {"X": 0.6445866227149963, "Y": 0.6387969255447388}]}, "Id": "81ac6d32-68d0-4e29-b00b-db3a24ffdc4a"}, {"BlockType": "WORD", "Confidence": 25.069555282592773, "Text": "32490", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057881079614162445, "Height": 0.017446625977754593, "Left": 0.7575283646583557, "Top": 0.6223767995834351}, "Polygon": [{"X": 0.7575331330299377, "Y": 0.6223767995834351}, {"X": 0.8154094219207764, "Y": 0.6223887801170349}, {"X": 0.8154050707817078, "Y": 0.6398234367370605}, {"X": 0.7575283646583557, "Y": 0.6398114562034607}]}, "Id": "93251663-10fc-4f9e-b79c-c17036f85d55"}, {"BlockType": "WORD", "Confidence": 30.740394592285156, "Text": "15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02341609075665474, "Height": 0.016379520297050476, "Left": 0.8242581486701965, "Top": 0.6240640878677368}, "Polygon": [{"X": 0.8242621421813965, "Y": 0.6240640878677368}, {"X": 0.8476741909980774, "Y": 0.6240689158439636}, {"X": 0.8476703763008118, "Y": 0.6404436230659485}, {"X": 0.8242581486701965, "Y": 0.6404387950897217}]}, "Id": "9beae776-ba2a-4b16-baa1-d72b414705c0"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "WEIGHER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09482968598604202, "Height": 0.01397744845598936, "Left": 0.16569936275482178, "Top": 0.6442453265190125}, "Polygon": [{"X": 0.16570672392845154, "Y": 0.6442453265190125}, {"X": 0.2605290412902832, "Y": 0.6442648768424988}, {"X": 0.2605222761631012, "Y": 0.6582227945327759}, {"X": 0.16569936275482178, "Y": 0.6582033038139343}]}, "Id": "e4a01173-0bd7-4ba7-9625-c8ca6efb33b4"}, {"BlockType": "WORD", "Confidence": 84.5360336303711, "Text": "12:44", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055771127343177795, "Height": 0.017212191596627235, "Left": 0.5562214255332947, "Top": 0.6670264005661011}, "Polygon": [{"X": 0.5562276244163513, "Y": 0.6670264005661011}, {"X": 0.6119925379753113, "Y": 0.667037844657898}, {"X": 0.6119867563247681, "Y": 0.684238612651825}, {"X": 0.5562214255332947, "Y": 0.6842271685600281}]}, "Id": "98067a1f-f4f4-4434-b59c-eddf946a060e"}, {"BlockType": "WORD", "Confidence": 98.08374786376953, "Text": "3152", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0459548644721508, "Height": 0.016446229070425034, "Left": 0.5659252405166626, "Top": 0.6871525049209595}, "Polygon": [{"X": 0.5659310817718506, "Y": 0.6871525049209595}, {"X": 0.6118801236152649, "Y": 0.6871618628501892}, {"X": 0.6118745803833008, "Y": 0.70359867811203}, {"X": 0.5659252405166626, "Y": 0.7035893797874451}]}, "Id": "6bb9206b-6a2c-47c9-93dc-6c510a2edf7b"}, {"BlockType": "WORD", "Confidence": 58.86878204345703, "Text": "<PERSON><PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17351943254470825, "Height": 0.055048540234565735, "Left": 0.24571429193019867, "Top": 0.7298856973648071}, "Polygon": [{"X": 0.24574139714241028, "Y": 0.7298856973648071}, {"X": 0.4192337095737457, "Y": 0.7299208045005798}, {"X": 0.4192107021808624, "Y": 0.7849342823028564}, {"X": 0.24571429193019867, "Y": 0.7848995923995972}]}, "Id": "424d294e-54be-4a3a-8a39-623b53356e19"}, {"BlockType": "WORD", "Confidence": 23.612085342407227, "Text": "He<PERSON>r", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.20493084192276, "Height": 0.06063946336507797, "Left": 0.43165692687034607, "Top": 0.7333122491836548}, "Polygon": [{"X": 0.43168196082115173, "Y": 0.7333122491836548}, {"X": 0.6365877389907837, "Y": 0.7333536744117737}, {"X": 0.636568009853363, "Y": 0.793951690196991}, {"X": 0.43165692687034607, "Y": 0.7939108610153198}]}, "Id": "b37b4932-c017-4a57-aebb-aef816dad204"}, {"BlockType": "WORD", "Confidence": 90.63078308105469, "Text": "14:57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055600397288799286, "Height": 0.01779528707265854, "Left": 0.64554762840271, "Top": 0.7462238669395447}, "Polygon": [{"X": 0.6455533504486084, "Y": 0.7462238669395447}, {"X": 0.7011480331420898, "Y": 0.7462350726127625}, {"X": 0.7011427283287048, "Y": 0.7640191316604614}, {"X": 0.64554762840271, "Y": 0.7640079855918884}]}, "Id": "964053e5-2e21-4cfd-ab0f-ab50fdc1e960"}, {"BlockType": "WORD", "Confidence": 83.47676086425781, "Text": "11-01-22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08937840908765793, "Height": 0.018076036125421524, "Left": 0.7448363900184631, "Top": 0.7487182021141052}, "Polygon": [{"X": 0.744841456413269, "Y": 0.7487182021141052}, {"X": 0.8342148065567017, "Y": 0.7487362027168274}, {"X": 0.8342104554176331, "Y": 0.7667942643165588}, {"X": 0.7448363900184631, "Y": 0.7667763233184814}]}, "Id": "da77fd68-46d7-4ede-a0e3-aedeec4c9656"}, {"BlockType": "WORD", "Confidence": 92.61419677734375, "Text": "11/2/2020", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14967866241931915, "Height": 0.03506859764456749, "Left": 0.4705485999584198, "Top": 0.7740284204483032}, "Polygon": [{"X": 0.47056248784065247, "Y": 0.7740284204483032}, {"X": 0.6202272772789001, "Y": 0.7740584015846252}, {"X": 0.6202155947685242, "Y": 0.8090969920158386}, {"X": 0.4705485999584198, "Y": 0.8090672492980957}]}, "Id": "47dc3666-d973-4ca7-ab6a-5b52f410042d"}, {"BlockType": "WORD", "Confidence": 72.45077514648438, "Text": "58260", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05887312442064285, "Height": 0.017965994775295258, "Left": 0.66522616147995, "Top": 0.8069742918014526}, "Polygon": [{"X": 0.6652317643165588, "Y": 0.8069742918014526}, {"X": 0.7240992784500122, "Y": 0.8069860339164734}, {"X": 0.7240940928459167, "Y": 0.8249403238296509}, {"X": 0.66522616147995, "Y": 0.8249286413192749}]}, "Id": "4e38300d-f61b-4f7e-9fd8-3a57168a0818"}, {"BlockType": "WORD", "Confidence": 13.596240997314453, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021688375622034073, "Height": 0.016423355787992477, "Left": 0.7343248724937439, "Top": 0.8077173829078674}, "Polygon": [{"X": 0.7343295216560364, "Y": 0.8077173829078674}, {"X": 0.7560132741928101, "Y": 0.807721734046936}, {"X": 0.7560087442398071, "Y": 0.824140727519989}, {"X": 0.7343248724937439, "Y": 0.8241364359855652}]}, "Id": "23bbe30b-56d2-4641-9a48-879ae6d1e6e4"}, {"BlockType": "WORD", "Confidence": 60.500240325927734, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055170997977256775, "Height": 0.01688331738114357, "Left": 0.7659662365913391, "Top": 0.8076465725898743}, "Polygon": [{"X": 0.765970766544342, "Y": 0.8076465725898743}, {"X": 0.8211371898651123, "Y": 0.8076575398445129}, {"X": 0.821133017539978, "Y": 0.8245298862457275}, {"X": 0.7659662365913391, "Y": 0.8245189785957336}]}, "Id": "04111ae1-b69f-46d4-8afd-1b27529ad995"}, {"BlockType": "WORD", "Confidence": 35.57916259765625, "Text": "PA", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.3912660479545593, "Height": 0.10972226411104202, "Left": 0.15533116459846497, "Top": 0.8131336569786072}, "Polygon": [{"X": 0.15538941323757172, "Y": 0.8131336569786072}, {"X": 0.5465972423553467, "Y": 0.8132113218307495}, {"X": 0.5465573072433472, "Y": 0.9228559136390686}, {"X": 0.15533116459846497, "Y": 0.9227801561355591}]}, "Id": "62dd5793-7bef-41a8-83cd-679d2610956d"}, {"BlockType": "WORD", "Confidence": 42.23014831542969, "Text": "400", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034365251660346985, "Height": 0.016346897929906845, "Left": 0.6903668642044067, "Top": 0.8265507817268372}, "Polygon": [{"X": 0.6903718113899231, "Y": 0.8265507817268372}, {"X": 0.7247321605682373, "Y": 0.8265575766563416}, {"X": 0.7247274518013, "Y": 0.8428977131843567}, {"X": 0.6903668642044067, "Y": 0.8428909182548523}]}, "Id": "86da4d33-d7cb-4560-8575-8214e1205a0f"}, {"BlockType": "WORD", "Confidence": 27.813297271728516, "Text": "5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021958019584417343, "Height": 0.015524042770266533, "Left": 0.7364304065704346, "Top": 0.8285086750984192}, "Polygon": [{"X": 0.7364304065704346, "Y": 0.8440284132957458}, {"X": 0.736434817314148, "Y": 0.8285086750984192}, {"X": 0.7583884596824646, "Y": 0.8285130262374878}, {"X": 0.7583842277526855, "Y": 0.8440327048301697}]}, "Id": "0afc4f4a-a363-4678-bd40-8cca5c99c7ca"}, {"BlockType": "WORD", "Confidence": 72.61260223388672, "Text": "TARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04690805822610855, "Height": 0.01722428947687149, "Left": 0.766647219657898, "Top": 0.8278085589408875}, "Polygon": [{"X": 0.7666518688201904, "Y": 0.8278085589408875}, {"X": 0.8135553002357483, "Y": 0.8278177976608276}, {"X": 0.8135509490966797, "Y": 0.845032811164856}, {"X": 0.766647219657898, "Y": 0.8450235724449158}]}, "Id": "251f38c7-8b60-4439-a78d-dcfd77e58851"}, {"BlockType": "WORD", "Confidence": 26.75522232055664, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022934401407837868, "Height": 0.015477005392313004, "Left": 0.7357911467552185, "Top": 0.84880131483078}, "Polygon": [{"X": 0.7357955574989319, "Y": 0.84880131483078}, {"X": 0.7587255835533142, "Y": 0.848805844783783}, {"X": 0.7587213516235352, "Y": 0.8642783164978027}, {"X": 0.7357911467552185, "Y": 0.8642738461494446}]}, "Id": "89e20459-f02b-4fa9-9c0e-e5f74f64a3dd"}, {"BlockType": "WORD", "Confidence": 96.78870391845703, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035266563296318054, "Height": 0.01592062972486019, "Left": 0.7671415209770203, "Top": 0.8482168316841125}, "Polygon": [{"X": 0.7671458125114441, "Y": 0.8482168316841125}, {"X": 0.8024080991744995, "Y": 0.8482237458229065}, {"X": 0.8024040460586548, "Y": 0.8641374707221985}, {"X": 0.7671415209770203, "Y": 0.8641305565834045}]}, "Id": "4be8c269-880c-4670-ab8c-d3106d69a519"}, {"BlockType": "WORD", "Confidence": 21.733198165893555, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01951032504439354, "Height": 0.01703607477247715, "Left": 0.5947649478912354, "Top": 0.8667954206466675}, "Polygon": [{"X": 0.6142752766609192, "Y": 0.8667992353439331}, {"X": 0.6142695546150208, "Y": 0.8838315010070801}, {"X": 0.5947649478912354, "Y": 0.8838276863098145}, {"X": 0.5947707891464233, "Y": 0.8667954206466675}]}, "Id": "c1854609-dc68-4766-b3ff-aed0b45ac6e2"}, {"BlockType": "WORD", "Confidence": 89.41107177734375, "Text": "11-2-22", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1454796940088272, "Height": 0.024066345766186714, "Left": 0.3973536789417267, "Top": 0.9159774780273438}, "Polygon": [{"X": 0.39736396074295044, "Y": 0.9159774780273438}, {"X": 0.5428333878517151, "Y": 0.9160057306289673}, {"X": 0.5428245663642883, "Y": 0.9400438666343689}, {"X": 0.3973536789417267, "Y": 0.9400157928466797}]}, "Id": "b0c57f28-2487-4002-80d4-44eca6a17594"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "94654bd5-cf6b-4670-bdab-a25e1e68c068", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "94654bd5-cf6b-4670-bdab-a25e1e68c068", "content-type": "application/x-amz-json-1.1", "content-length": "57539", "date": "Wed, 24 Sep 2025 13:39:06 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "1698ca42-9da5-4fd6-a3fd-b14fdbd445dd", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:39:08 GMT", "content-type": "application/json", "content-length": "619", "connection": "keep-alive", "x-amzn-requestid": "1698ca42-9da5-4fd6-a3fd-b14fdbd445dd"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. The content: \"ELECTRONIC SCALE TICKET\" header. Keywords: \"WEIGHER\", \"GROSS\", \"TARE\", \"NET\". So classification: scale_ticket. No continuation indicator. So output JSON with documents array containing object page_no:1, doc_type:\"scale_ticket\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2785, "outputTokens": 90, "totalTokens": 2875}, "metrics": {"latencyMs": 545}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/1c67ee71_6_scale_ticket_4.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nELECTRONIC SCALE TICKET\nEIL\nNC\nDATE:\nMAILING\nADDRESS\nDLA DISPOSITION SERVICES - COLUMBUS\n3990 E. BROAD ST, BLDG 30\nCOLUMBUS, OH 43213\nPH:\n614-692-2055\nSHIP TO\nADDRESS\nDLA DISPOSITION SERVICES - COLUMBUS\n320 N. JAMES RD, BLDG 30\nCOLUMBUS, OH 43213\nREMARKS:\n04\n11-01-22\nLOOP to 0661\nDRIVER\nSIGNATURE:\nINSOLND 32490 15\nWEIGHER:\n12:44\n3152\n<PERSON><PERSON><PERSON>r\n14:57\n11-01-22\n11/2/2020\n58260 In GROSS\nPA\n400\n5\nTARE\nin NET\n&\n11-2-22\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}