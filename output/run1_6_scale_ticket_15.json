{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 3.0253713703132235e-05, "Y": 0.0}, {"X": 1.0, "Y": 2.9934017220512033e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "5340e4c8-8055-4ed4-878c-6a42097ee5a1", "Relationships": [{"Type": "CHILD", "Ids": ["ab2edd83-54a7-42d2-aa37-0fdcf7706564", "67090530-6464-4a61-822c-01c8852355a6", "e2a67a8c-a4ee-44da-b03e-24fc32ffb7e0", "af4fa6a8-2a13-4581-9b51-6e76a3273627", "91e82509-7a4e-4d60-9efc-0d44653a2437", "18be4a66-7c88-42e3-8534-1ff61f495188", "5d855389-2193-469a-9d67-71487ba7832d", "a944d47b-dfa7-4707-a749-ae9e840e7d28", "3e78d336-f098-40e3-a31f-d34f696fd98c", "614ca84a-84d2-469d-ab62-11c25b91c6e9", "c94f8e25-2f32-457a-9808-ed8fe0b036db", "e61a6f38-92f4-4c7b-a88d-4eaddf4bf716", "6daa2920-31b8-4325-afa7-e98572381df7", "424eb7a6-a0db-484e-940d-49784f34ba73", "edd3a04c-37e3-4929-bdd7-f86d8d7deef8", "05025f18-94e1-48bf-9bac-a0dbee82e026", "322a9e8a-4ba3-444e-b39b-9228d48c009a", "f28a0b86-ea46-4b2e-a433-b28a7b32e6b8", "f00825cd-2753-4a93-9ff4-0a4522123aa1", "57f9c511-0920-4f88-8c38-667dee69e2b9", "6a153fe5-677e-46f2-b8ce-a6169733297d", "52d5be07-8476-4e54-aba0-9368b2a68eed", "8ba97e15-0495-469a-bc8d-74b639ac5903", "28375f8b-beb2-432b-91a6-35452bb1f2a0", "f9e2c991-29be-4c5a-8374-370edd46e501", "9a3693c0-da7c-46fe-b128-343e71b3ad60", "d8f67f24-ae02-49f4-aa12-8a9484d21b1d", "951ebb03-8f86-4bd1-a435-5a000ce96926", "5d721d37-033d-4af1-aeb0-4ae8e90c09fc"]}]}, {"BlockType": "LINE", "Confidence": 97.82923889160156, "Text": "*1242095*", "Geometry": {"BoundingBox": {"Width": 0.06936799734830856, "Height": 0.011635170318186283, "Left": 0.31395968794822693, "Top": 0.060400351881980896}, "Polygon": [{"X": 0.31402650475502014, "Y": 0.060400351881980896}, {"X": 0.3833276927471161, "Y": 0.06074417009949684}, {"X": 0.3832613229751587, "Y": 0.0720355212688446}, {"X": 0.31395968794822693, "Y": 0.0716918408870697}]}, "Id": "ab2edd83-54a7-42d2-aa37-0fdcf7706564", "Relationships": [{"Type": "CHILD", "Ids": ["66130392-80c4-4cf0-b9b3-d9b531c3df19"]}]}, {"BlockType": "LINE", "Confidence": 89.37471771240234, "Text": "<PERSON>lla <PERSON>ling or New England", "Geometry": {"BoundingBox": {"Width": 0.23200149834156036, "Height": 0.02170529030263424, "Left": 0.31109243631362915, "Top": 0.09503933787345886}, "Polygon": [{"X": 0.31121405959129333, "Y": 0.09503933787345886}, {"X": 0.5430939197540283, "Y": 0.09618832916021347}, {"X": 0.5429747700691223, "Y": 0.11674463003873825}, {"X": 0.31109243631362915, "Y": 0.1155964732170105}]}, "Id": "67090530-6464-4a61-822c-01c8852355a6", "Relationships": [{"Type": "CHILD", "Ids": ["1d9faebc-ccc4-445e-8209-3342dabb7da5", "f40f354f-bed7-490a-8bc9-4f5c23b7a90f", "90ef2f72-cf58-4d63-8fb4-37735e9b01a0", "b48b1f55-a322-41ff-84b9-fa4785bbf868", "8c7a911f-4957-4a7e-bb02-3734eb4ce268"]}]}, {"BlockType": "LINE", "Confidence": 96.92958068847656, "Text": "A Division of Casella Waste Systems Inc", "Geometry": {"BoundingBox": {"Width": 0.2591709494590759, "Height": 0.017858123406767845, "Left": 0.31320837140083313, "Top": 0.12613186240196228}, "Polygon": [{"X": 0.31330642104148865, "Y": 0.12613186240196228}, {"X": 0.5723792910575867, "Y": 0.12741418182849884}, {"X": 0.5722834467887878, "Y": 0.14398999512195587}, {"X": 0.31320837140083313, "Y": 0.142708420753479}]}, "Id": "e2a67a8c-a4ee-44da-b03e-24fc32ffb7e0", "Relationships": [{"Type": "CHILD", "Ids": ["218a730e-f0b2-47d5-ab29-d14324b35412", "beade4ad-c80e-4168-9f39-c49d065aa047", "d1f0d848-fc1b-47dd-ae4b-4f1c4e9e21d5", "4f5139ca-5852-4329-85ed-6d2d06a5ed80", "25bd5832-14b8-4fc0-8c39-33fecf816e29", "13b62e21-a088-4b3d-90ae-6c8b5b01f567", "c8e89de2-a81f-4ff2-8ee3-f65daa211e66"]}]}, {"BlockType": "LINE", "Confidence": 72.08979034423828, "Text": "bebunker HILL INDUSTRIAL PARK", "Geometry": {"BoundingBox": {"Width": 0.2604175806045532, "Height": 0.015084944665431976, "Left": 0.3177105188369751, "Top": 0.14159095287322998}, "Polygon": [{"X": 0.3177920877933502, "Y": 0.14159095287322998}, {"X": 0.5781280994415283, "Y": 0.14287881553173065}, {"X": 0.5780483484268188, "Y": 0.15667590498924255}, {"X": 0.3177105188369751, "Y": 0.15538868308067322}]}, "Id": "af4fa6a8-2a13-4581-9b51-6e76a3273627", "Relationships": [{"Type": "CHILD", "Ids": ["7f8fddad-960e-4d34-b8eb-83fbbd5f1691", "3c12e21e-b4d6-40df-bff4-5f74d9892b54", "d9b648c8-2514-4aff-a343-6ec1cd1849c3", "3175ac07-061f-4d40-8dba-18c4c94fca5a"]}]}, {"BlockType": "LINE", "Confidence": 95.30519104003906, "Text": "617-242-7746P SCALE HOUSE617-242-3649F", "Geometry": {"BoundingBox": {"Width": 0.3111901879310608, "Height": 0.01852438412606716, "Left": 0.31289297342300415, "Top": 0.1550382524728775}, "Polygon": [{"X": 0.31299343705177307, "Y": 0.1550382524728775}, {"X": 0.6240831613540649, "Y": 0.15657643973827362}, {"X": 0.6239853501319885, "Y": 0.1735626459121704}, {"X": 0.31289297342300415, "Y": 0.1720253825187683}]}, "Id": "91e82509-7a4e-4d60-9efc-0d44653a2437", "Relationships": [{"Type": "CHILD", "Ids": ["11b0f53c-e8a2-4b12-97b1-38fbf34544f5", "05fb5c06-1c89-4a3d-ab4e-daa6e3988de6", "7c007002-cfd3-42b9-bb46-2cd1b5551f13"]}]}, {"BlockType": "LINE", "Confidence": 83.43132019042969, "Text": "Ticket 1242095", "Geometry": {"BoundingBox": {"Width": 0.10478275269269943, "Height": 0.01385817863047123, "Left": 0.3122906982898712, "Top": 0.22036512196063995}, "Polygon": [{"X": 0.31236961483955383, "Y": 0.22036512196063995}, {"X": 0.41707342863082886, "Y": 0.2208816260099411}, {"X": 0.41699522733688354, "Y": 0.23422330617904663}, {"X": 0.3122906982898712, "Y": 0.23370705544948578}]}, "Id": "18be4a66-7c88-42e3-8534-1ff61f495188", "Relationships": [{"Type": "CHILD", "Ids": ["cf91ae09-b4ac-405e-89ed-9c6ddc017137", "0e350354-fc3c-46ba-a0b7-a2e45210e0a4"]}]}, {"BlockType": "LINE", "Confidence": 77.05915069580078, "Text": "Date 10/6/2022", "Geometry": {"BoundingBox": {"Width": 0.1041073352098465, "Height": 0.014262218028306961, "Left": 0.320504754781723, "Top": 0.23747386038303375}, "Polygon": [{"X": 0.32058602571487427, "Y": 0.23747386038303375}, {"X": 0.4246120750904083, "Y": 0.2379866987466812}, {"X": 0.4245315194129944, "Y": 0.2517360746860504}, {"X": 0.320504754781723, "Y": 0.25122347474098206}]}, "Id": "5d855389-2193-469a-9d67-71487ba7832d", "Relationships": [{"Type": "CHILD", "Ids": ["2010fb34-7144-4403-b7c9-e0ca8a3653ce", "0481249c-e930-42c9-874d-421fd725940b"]}]}, {"BlockType": "LINE", "Confidence": 95.75830841064453, "Text": "Time: 17:16:09 - 18:56:34", "Geometry": {"BoundingBox": {"Width": 0.16555625200271606, "Height": 0.015059046447277069, "Left": 0.31958502531051636, "Top": 0.2551310658454895}, "Polygon": [{"X": 0.3196692168712616, "Y": 0.2551310658454895}, {"X": 0.4851412773132324, "Y": 0.2559463083744049}, {"X": 0.4850582480430603, "Y": 0.27019011974334717}, {"X": 0.31958502531051636, "Y": 0.2693752646446228}]}, "Id": "a944d47b-dfa7-4707-a749-ae9e840e7d28", "Relationships": [{"Type": "CHILD", "Ids": ["6ccb5236-dbc7-4f4d-bc59-77ab9281a454", "93fe6ff4-39b8-41d4-af03-797b72bf0203", "ba80aed5-9f10-4bf5-8d3e-545a51420628", "49a4a897-3589-4f16-9871-753d606c20ae"]}]}, {"BlockType": "LINE", "Confidence": 94.80757904052734, "Text": "Customer: KO00508/EARTHWATCH WASTE SYSTEM", "Geometry": {"BoundingBox": {"Width": 0.35813209414482117, "Height": 0.019088050350546837, "Left": 0.324215292930603, "Top": 0.28927043080329895}, "Polygon": [{"X": 0.32431769371032715, "Y": 0.28927043080329895}, {"X": 0.6823474168777466, "Y": 0.29103222489356995}, {"X": 0.6822481751441956, "Y": 0.30835849046707153}, {"X": 0.324215292930603, "Y": 0.3065977692604065}]}, "Id": "3e78d336-f098-40e3-a31f-d34f696fd98c", "Relationships": [{"Type": "CHILD", "Ids": ["7440f5d0-9c0c-4595-88e1-115b6461bdc3", "37164c00-b1ec-44f9-b02f-ed3c935cbb83", "fd1aed08-f18a-401e-83d0-7d9e5da296e7", "3e5c3c6f-9b18-481f-858b-4a9504efe5ce"]}]}, {"BlockType": "LINE", "Confidence": 97.35580444335938, "Text": "Truck: 280230", "Geometry": {"BoundingBox": {"Width": 0.09441805630922318, "Height": 0.011949609965085983, "Left": 0.3359290361404419, "Top": 0.3053954839706421}, "Polygon": [{"X": 0.3359968364238739, "Y": 0.3053954839706421}, {"X": 0.4303470849990845, "Y": 0.3058595061302185}, {"X": 0.43027985095977783, "Y": 0.3173450827598572}, {"X": 0.3359290361404419, "Y": 0.3168812692165375}]}, "Id": "614ca84a-84d2-469d-ab62-11c25b91c6e9", "Relationships": [{"Type": "CHILD", "Ids": ["0e4f4c77-eb8e-457a-9e99-5bbb71d862fd", "f6b5f3d0-eac9-4037-85d2-169b24f1783c"]}]}, {"BlockType": "LINE", "Confidence": 88.17711639404297, "Text": "Trailer: 19065", "Geometry": {"BoundingBox": {"Width": 0.09021579474210739, "Height": 0.011810170486569405, "Left": 0.328146755695343, "Top": 0.3218071758747101}, "Polygon": [{"X": 0.3282139003276825, "Y": 0.3218071758747101}, {"X": 0.4183625280857086, "Y": 0.32225024700164795}, {"X": 0.4182959198951721, "Y": 0.33361735939979553}, {"X": 0.328146755695343, "Y": 0.3331744372844696}]}, "Id": "c94f8e25-2f32-457a-9808-ed8fe0b036db", "Relationships": [{"Type": "CHILD", "Ids": ["d0a8b8f7-83d4-49fc-bae2-19d8b2663a6a", "aba62a49-43bb-4244-a3cb-fbbd072ea44a"]}]}, {"BlockType": "LINE", "Confidence": 96.40814208984375, "Text": "Destination: A/A", "Geometry": {"BoundingBox": {"Width": 0.10788533091545105, "Height": 0.012242062948644161, "Left": 0.3131125569343567, "Top": 0.33830708265304565}, "Polygon": [{"X": 0.3131818175315857, "Y": 0.33830708265304565}, {"X": 0.42099788784980774, "Y": 0.3388366997241974}, {"X": 0.4209292531013489, "Y": 0.3505491614341736}, {"X": 0.3131125569343567, "Y": 0.35001978278160095}]}, "Id": "e61a6f38-92f4-4c7b-a88d-4eaddf4bf716", "Relationships": [{"Type": "CHILD", "Ids": ["66f778c0-adcc-4757-b3c5-564dd0c3df78", "86b4c6cb-c8ba-49fb-b09c-caa5cd9a4432"]}]}, {"BlockType": "LINE", "Confidence": 98.29042053222656, "Text": "P.O.: EARTH100622-3", "Geometry": {"BoundingBox": {"Width": 0.14777721464633942, "Height": 0.012590057216584682, "Left": 0.341135174036026, "Top": 0.3561957776546478}, "Polygon": [{"X": 0.3412051796913147, "Y": 0.3561957776546478}, {"X": 0.4889124035835266, "Y": 0.35692086815834045}, {"X": 0.48884326219558716, "Y": 0.3687858581542969}, {"X": 0.341135174036026, "Y": 0.3680610656738281}]}, "Id": "6daa2920-31b8-4325-afa7-e98572381df7", "Relationships": [{"Type": "CHILD", "Ids": ["a249ea6d-aa48-4724-94a0-e921b1c041d6", "93c59f06-5548-4b45-ac18-0f3e8439c5ad"]}]}, {"BlockType": "LINE", "Confidence": 98.5254898071289, "Text": "D.O.: 2277043", "Geometry": {"BoundingBox": {"Width": 0.09322088211774826, "Height": 0.011321824975311756, "Left": 0.3413318991661072, "Top": 0.37284141778945923}, "Polygon": [{"X": 0.3413960039615631, "Y": 0.37284141778945923}, {"X": 0.43455278873443604, "Y": 0.37329843640327454}, {"X": 0.4344891905784607, "Y": 0.3841632306575775}, {"X": 0.3413318991661072, "Y": 0.38370636105537415}]}, "Id": "424eb7a6-a0db-484e-940d-49784f34ba73", "Relationships": [{"Type": "CHILD", "Ids": ["dbde1089-499a-48af-bec6-d0b39386ef86", "783f6327-5b5f-4a54-967d-aa18699525c7"]}]}, {"BlockType": "LINE", "Confidence": 99.80229949951172, "Text": "Seal: 519044", "Geometry": {"BoundingBox": {"Width": 0.08182479441165924, "Height": 0.011214593425393105, "Left": 0.3408919870853424, "Top": 0.38938456773757935}, "Polygon": [{"X": 0.34095579385757446, "Y": 0.38938456773757935}, {"X": 0.42271676659584045, "Y": 0.38978543877601624}, {"X": 0.4226534068584442, "Y": 0.4005991518497467}, {"X": 0.3408919870853424, "Y": 0.40019842982292175}]}, "Id": "edd3a04c-37e3-4929-bdd7-f86d8d7deef8", "Relationships": [{"Type": "CHILD", "Ids": ["e4fb549d-8974-4bc8-90c5-5540869da371", "b9533df0-7279-461a-b766-da035e3c506f"]}]}, {"BlockType": "LINE", "Confidence": 94.90798950195312, "Text": "Comment: PARAKA CORPORATION", "Geometry": {"BoundingBox": {"Width": 0.24553163349628448, "Height": 0.01466277800500393, "Left": 0.3283481001853943, "Top": 0.4223615825176239}, "Polygon": [{"X": 0.32842761278152466, "Y": 0.4223615825176239}, {"X": 0.5738797187805176, "Y": 0.4235635995864868}, {"X": 0.5738018751144409, "Y": 0.4370243549346924}, {"X": 0.3283481001853943, "Y": 0.43582287430763245}]}, "Id": "05025f18-94e1-48bf-9bac-a0dbee82e026", "Relationships": [{"Type": "CHILD", "Ids": ["ecbfdc61-91b3-41d5-bd21-f6a476d4f85b", "cb54d900-539c-4a54-9c47-eed0a47f8af7", "10573341-0a47-44ef-9f7c-b357737d5db6"]}]}, {"BlockType": "LINE", "Confidence": 91.07029724121094, "Text": "Gross: 75220 L Out Scale 3", "Geometry": {"BoundingBox": {"Width": 0.18159380555152893, "Height": 0.012697604484856129, "Left": 0.3166877329349518, "Top": 0.48309969902038574}, "Polygon": [{"X": 0.31675755977630615, "Y": 0.48309969902038574}, {"X": 0.4982815384864807, "Y": 0.4839867055416107}, {"X": 0.4982127845287323, "Y": 0.4957973062992096}, {"X": 0.3166877329349518, "Y": 0.49491065740585327}]}, "Id": "322a9e8a-4ba3-444e-b39b-9228d48c009a", "Relationships": [{"Type": "CHILD", "Ids": ["db133b20-6fe2-4c26-a28d-b878557810ca", "cc20b4a7-b277-48f3-abc9-a1ae09b02284", "6576ddeb-4414-44b6-99cf-700dff266ba1", "a785e1a4-1f81-4c7a-9583-2764d1996189", "a9a2d348-7e12-43f7-8a96-72d078723018", "a3678387-0800-4295-8957-db80b934584b"]}]}, {"BlockType": "LINE", "Confidence": 99.27080535888672, "Text": "Tare: 33720 L In Scale 3", "Geometry": {"BoundingBox": {"Width": 0.16737152636051178, "Height": 0.012590100057423115, "Left": 0.3197777569293976, "Top": 0.49978455901145935}, "Polygon": [{"X": 0.31984734535217285, "Y": 0.49978455901145935}, {"X": 0.48714929819107056, "Y": 0.500601589679718}, {"X": 0.4870806932449341, "Y": 0.5123746395111084}, {"X": 0.3197777569293976, "Y": 0.511557936668396}]}, "Id": "f28a0b86-ea46-4b2e-a433-b28a7b32e6b8", "Relationships": [{"Type": "CHILD", "Ids": ["9843cd09-db0c-437c-82aa-1bfa88a498ef", "4cfde778-9b0f-4247-ae42-93b2a125f09b", "c3488593-0500-431f-92e6-e0c6e0f2c04c", "904242e4-3442-43a2-a517-e21dfcf494b0", "9e0502ec-52b5-40f6-bfa4-e523e49a228e", "e8967fda-b574-4015-b862-5d24d6a86466"]}]}, {"BlockType": "LINE", "Confidence": 98.9224624633789, "Text": "Net: 41500 L", "Geometry": {"BoundingBox": {"Width": 0.0931902751326561, "Height": 0.010771139524877071, "Left": 0.32456353306770325, "Top": 0.5166094899177551}, "Polygon": [{"X": 0.3246244788169861, "Y": 0.5166094899177551}, {"X": 0.41775381565093994, "Y": 0.5170640349388123}, {"X": 0.4176933467388153, "Y": 0.527380645275116}, {"X": 0.32456353306770325, "Y": 0.5269262790679932}]}, "Id": "f00825cd-2753-4a93-9ff4-0a4522123aa1", "Relationships": [{"Type": "CHILD", "Ids": ["8ea81e60-a1a0-4009-87ec-4e19100caafa", "587844ec-f99f-43af-a061-29aee4f1bca1", "6fd4c8fa-8990-403f-97f7-22632804db77"]}]}, {"BlockType": "LINE", "Confidence": 98.96354675292969, "Text": "Tons: 20.75", "Geometry": {"BoundingBox": {"Width": 0.07428757846355438, "Height": 0.010443630628287792, "Left": 0.3197498917579651, "Top": 0.5419918298721313}, "Polygon": [{"X": 0.3198094964027405, "Y": 0.5419918298721313}, {"X": 0.39403748512268066, "Y": 0.5423537492752075}, {"X": 0.3939782679080963, "Y": 0.5524354577064514}, {"X": 0.3197498917579651, "Y": 0.5520736575126648}]}, "Id": "57f9c511-0920-4f88-8c38-667dee69e2b9", "Relationships": [{"Type": "CHILD", "Ids": ["26e18808-7edb-4e4c-9cb8-c51153fbebb6", "d118bc80-e74b-4e48-b4ad-0f829805f020"]}]}, {"BlockType": "LINE", "Confidence": 99.96745300292969, "Text": "Materials & Services", "Geometry": {"BoundingBox": {"Width": 0.13292700052261353, "Height": 0.010792572982609272, "Left": 0.3128376305103302, "Top": 0.5759910941123962}, "Polygon": [{"X": 0.31289762258529663, "Y": 0.5759910941123962}, {"X": 0.4457646310329437, "Y": 0.5766381621360779}, {"X": 0.4457052946090698, "Y": 0.5867836475372314}, {"X": 0.3128376305103302, "Y": 0.5861368179321289}]}, "Id": "6a153fe5-677e-46f2-b8ce-a6169733297d", "Relationships": [{"Type": "CHILD", "Ids": ["55107d48-726b-4fba-9bd2-649491e5980a", "bb0c458b-2c90-44cc-bd4d-d9909335dbcc", "04111ed9-ce76-4f0b-9380-374a0bc8764e"]}]}, {"BlockType": "LINE", "Confidence": 99.6146240234375, "Text": "Origin: NA/NON APPLICABLE", "Geometry": {"BoundingBox": {"Width": 0.20861291885375977, "Height": 0.013081224635243416, "Left": 0.3315693140029907, "Top": 0.6195129156112671}, "Polygon": [{"X": 0.3316405415534973, "Y": 0.6195129156112671}, {"X": 0.5401822328567505, "Y": 0.620526909828186}, {"X": 0.5401122570037842, "Y": 0.632594108581543}, {"X": 0.3315693140029907, "Y": 0.6315805315971375}]}, "Id": "52d5be07-8476-4e54-aba0-9368b2a68eed", "Relationships": [{"Type": "CHILD", "Ids": ["99d338bb-5f54-436d-8b14-913fff33f074", "3435e38d-3c04-40af-bb24-a3ea50724615", "4315e934-52ba-432e-a2dc-95f122d22cb3"]}]}, {"BlockType": "LINE", "Confidence": 98.50471496582031, "Text": "Material: 539/OBR6-MXD COMMODITIES BALED", "Geometry": {"BoundingBox": {"Width": 0.34142813086509705, "Height": 0.012859317474067211, "Left": 0.32419121265411377, "Top": 0.6367433667182922}, "Polygon": [{"X": 0.32425740361213684, "Y": 0.6367433667182922}, {"X": 0.6656193733215332, "Y": 0.6384021639823914}, {"X": 0.6655551195144653, "Y": 0.6496026515960693}, {"X": 0.32419121265411377, "Y": 0.6479445695877075}]}, "Id": "8ba97e15-0495-469a-bc8d-74b639ac5903", "Relationships": [{"Type": "CHILD", "Ids": ["7f9e37df-5028-4a1a-abc3-82314af7f1d3", "ffd48f94-2e2d-4851-ac44-dfab841a6283", "8fb9aecb-c607-4510-bf17-8d6df3f2daf2", "7d03d974-7a80-4c54-b773-539c84772fe7"]}]}, {"BlockType": "LINE", "Confidence": 89.3183364868164, "Text": "Quantity. 20.75 Ton", "Geometry": {"BoundingBox": {"Width": 0.12691234052181244, "Height": 0.012754366733133793, "Left": 0.3233567476272583, "Top": 0.65323805809021}, "Polygon": [{"X": 0.3234284520149231, "Y": 0.65323805809021}, {"X": 0.45026907324790955, "Y": 0.6538540720939636}, {"X": 0.45019814372062683, "Y": 0.6659924387931824}, {"X": 0.3233567476272583, "Y": 0.6653767228126526}]}, "Id": "28375f8b-beb2-432b-91a6-35452bb1f2a0", "Relationships": [{"Type": "CHILD", "Ids": ["c975a7e2-c489-418c-bb00-cf2005750317", "9c6665ae-8883-43a7-b8f0-299e0b370f74", "0bd12038-c57c-4d7e-bc68-ecf81456bf73"]}]}, {"BlockType": "LINE", "Confidence": 95.69066619873047, "Text": "Weighmaster: TOM", "Geometry": {"BoundingBox": {"Width": 0.1261318027973175, "Height": 0.012868843972682953, "Left": 0.3085758686065674, "Top": 0.760949969291687}, "Polygon": [{"X": 0.30864840745925903, "Y": 0.760949969291687}, {"X": 0.4347076714038849, "Y": 0.7615597248077393}, {"X": 0.4346359372138977, "Y": 0.7738187909126282}, {"X": 0.3085758686065674, "Y": 0.773209273815155}]}, "Id": "f9e2c991-29be-4c5a-8374-370edd46e501", "Relationships": [{"Type": "CHILD", "Ids": ["58b82c1d-5871-412e-86a7-34189f37c570", "98076157-fd30-4b57-92b1-340cd1d559fc"]}]}, {"BlockType": "LINE", "Confidence": 98.75458526611328, "Text": "Driver:", "Geometry": {"BoundingBox": {"Width": 0.04479655250906944, "Height": 0.009979024529457092, "Left": 0.3280671536922455, "Top": 0.7869653701782227}, "Polygon": [{"X": 0.3281247913837433, "Y": 0.7869653701782227}, {"X": 0.3728637099266052, "Y": 0.787181556224823}, {"X": 0.37280628085136414, "Y": 0.7969443798065186}, {"X": 0.3280671536922455, "Y": 0.796728253364563}]}, "Id": "9a3693c0-da7c-46fe-b128-343e71b3ad60", "Relationships": [{"Type": "CHILD", "Ids": ["2403b271-4397-4f87-9473-432b68c1882d"]}]}, {"BlockType": "LINE", "Confidence": 94.0353775024414, "Text": "By signing above, I declare that il did NOT", "Geometry": {"BoundingBox": {"Width": 0.34255027770996094, "Height": 0.01640838012099266, "Left": 0.3082296848297119, "Top": 0.8325653672218323}, "Polygon": [{"X": 0.3083169758319855, "Y": 0.8325653672218323}, {"X": 0.6507799625396729, "Y": 0.8342176675796509}, {"X": 0.650695264339447, "Y": 0.8489737510681152}, {"X": 0.3082296848297119, "Y": 0.8473223447799683}]}, "Id": "d8f67f24-ae02-49f4-aa12-8a9484d21b1d", "Relationships": [{"Type": "CHILD", "Ids": ["37fb0c71-241a-4563-8acc-8de312266528", "5961864f-4734-44d9-822b-a3b7c3086521", "2da082ed-a098-40b9-9391-23de30e60f20", "cc9deb23-e5f8-4c38-9311-3c5057cc25b4", "3dd08aa1-fbea-4f80-8f53-fbfbf9775e78", "44dcb969-45b7-4dc3-9091-ae145ce5dfbe", "e806f1c6-e92d-4333-b87d-48607776ba04", "c9d54fac-a3ea-474a-b0ea-f6426437e063", "1319eb1c-426b-48c6-871d-499d1512217b"]}]}, {"BlockType": "LINE", "Confidence": 99.96034240722656, "Text": "deposit any PROHIBITED WASTES", "Geometry": {"BoundingBox": {"Width": 0.27761831879615784, "Height": 0.01679309830069542, "Left": 0.30733582377433777, "Top": 0.8514628410339355}, "Polygon": [{"X": 0.3074272572994232, "Y": 0.8514628410339355}, {"X": 0.5849541425704956, "Y": 0.8528009057044983}, {"X": 0.5848649144172668, "Y": 0.8682559132575989}, {"X": 0.30733582377433777, "Y": 0.8669186234474182}]}, "Id": "951ebb03-8f86-4bd1-a435-5a000ce96926", "Relationships": [{"Type": "CHILD", "Ids": ["b6479638-32df-40be-a951-fd40721bfe19", "699f5e4b-3b7f-45f2-84a3-6741bf17f449", "1edf532d-4642-4dc4-beae-3c2036595d78", "fe72881b-bfa3-4aea-a6fe-9ec663140e74"]}]}, {"BlockType": "LINE", "Confidence": 99.63960266113281, "Text": "Scanned with CamScanner", "Geometry": {"BoundingBox": {"Width": 0.2997681498527527, "Height": 0.016474559903144836, "Left": 0.5998212099075317, "Top": 0.9680048823356628}, "Polygon": [{"X": 0.5999079346656799, "Y": 0.9680048823356628}, {"X": 0.8995894193649292, "Y": 0.969443678855896}, {"X": 0.8995049595832825, "Y": 0.9844794869422913}, {"X": 0.5998212099075317, "Y": 0.9830414652824402}]}, "Id": "5d721d37-033d-4af1-aeb0-4ae8e90c09fc", "Relationships": [{"Type": "CHILD", "Ids": ["02b4dd5d-2531-4bc4-8dcc-fda147e979cc", "15febafe-caef-4c5b-9fb2-5e3366e26701", "06b132cc-f738-4e59-adcf-f4b610443923"]}]}, {"BlockType": "WORD", "Confidence": 97.82923889160156, "Text": "*1242095*", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06936799734830856, "Height": 0.011635170318186283, "Left": 0.31395968794822693, "Top": 0.060400351881980896}, "Polygon": [{"X": 0.31402650475502014, "Y": 0.060400351881980896}, {"X": 0.3833276927471161, "Y": 0.06074417009949684}, {"X": 0.3832613229751587, "Y": 0.0720355212688446}, {"X": 0.31395968794822693, "Y": 0.0716918408870697}]}, "Id": "66130392-80c4-4cf0-b9b3-d9b531c3df19"}, {"BlockType": "WORD", "Confidence": 99.00410461425781, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047886233776807785, "Height": 0.013375353999435902, "Left": 0.3111363351345062, "Top": 0.09503933787345886}, "Polygon": [{"X": 0.31121405959129333, "Y": 0.09503933787345886}, {"X": 0.3590225577354431, "Y": 0.09527623653411865}, {"X": 0.35894516110420227, "Y": 0.10841469466686249}, {"X": 0.3111363351345062, "Y": 0.10817790776491165}]}, "Id": "1d9faebc-ccc4-445e-8209-3342dabb7da5"}, {"BlockType": "WORD", "Confidence": 99.44355773925781, "Text": "Recycling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06414727866649628, "Height": 0.01617136038839817, "Left": 0.3638531565666199, "Top": 0.09796074032783508}, "Polygon": [{"X": 0.3639465272426605, "Y": 0.09796074032783508}, {"X": 0.42800042033195496, "Y": 0.09827810525894165}, {"X": 0.4279075562953949, "Y": 0.1141320988535881}, {"X": 0.3638531565666199, "Y": 0.11381491273641586}]}, "Id": "f40f354f-bed7-490a-8bc9-4f5c23b7a90f"}, {"BlockType": "WORD", "Confidence": 48.57322311401367, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019098740071058273, "Height": 0.010328927077353, "Left": 0.4319731891155243, "Top": 0.10136546939611435}, "Polygon": [{"X": 0.43203312158584595, "Y": 0.10136546939611435}, {"X": 0.45107194781303406, "Y": 0.10145978629589081}, {"X": 0.45101213455200195, "Y": 0.11169439554214478}, {"X": 0.4319731891155243, "Y": 0.1116001084446907}]}, "Id": "90ef2f72-cf58-4d63-8fb4-37735e9b01a0"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "New", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02930966392159462, "Height": 0.01024057436734438, "Left": 0.45683324337005615, "Top": 0.10250933468341827}, "Polygon": [{"X": 0.4568922221660614, "Y": 0.10250933468341827}, {"X": 0.4861429035663605, "Y": 0.10265424102544785}, {"X": 0.48608407378196716, "Y": 0.11274991184473038}, {"X": 0.45683324337005615, "Y": 0.11260505765676498}]}, "Id": "b48b1f55-a322-41ff-84b9-fa4785bbf868"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "England", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052169069647789, "Height": 0.013696210458874702, "Left": 0.49088358879089355, "Top": 0.1030484139919281}, "Polygon": [{"X": 0.49096187949180603, "Y": 0.1030484139919281}, {"X": 0.5430526733398438, "Y": 0.10330646485090256}, {"X": 0.5429747700691223, "Y": 0.11674463003873825}, {"X": 0.49088358879089355, "Y": 0.11648669838905334}]}, "Id": "8c7a911f-4957-4a7e-bb02-3734eb4ce268"}, {"BlockType": "WORD", "Confidence": 94.17829895019531, "Text": "A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010607630014419556, "Height": 0.009761106222867966, "Left": 0.3132476508617401, "Top": 0.1263548582792282}, "Polygon": [{"X": 0.3133050799369812, "Y": 0.1263548582792282}, {"X": 0.32385528087615967, "Y": 0.12640708684921265}, {"X": 0.32379791140556335, "Y": 0.13611596822738647}, {"X": 0.3132476508617401, "Y": 0.13606376945972443}]}, "Id": "218a730e-f0b2-47d5-ab29-d14324b35412"}, {"BlockType": "WORD", "Confidence": 93.68682861328125, "Text": "Division", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05929846689105034, "Height": 0.011927010491490364, "Left": 0.32164883613586426, "Top": 0.1261734962463379}, "Polygon": [{"X": 0.32171761989593506, "Y": 0.1261734962463379}, {"X": 0.3809473216533661, "Y": 0.12646666169166565}, {"X": 0.38087889552116394, "Y": 0.1381005048751831}, {"X": 0.32164883613586426, "Y": 0.1378074586391449}]}, "Id": "beade4ad-c80e-4168-9f39-c49d065aa047"}, {"BlockType": "WORD", "Confidence": 96.20854949951172, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016172057017683983, "Height": 0.009849348105490208, "Left": 0.3854249119758606, "Top": 0.12856341898441315}, "Polygon": [{"X": 0.3854823410511017, "Y": 0.12856341898441315}, {"X": 0.40159696340560913, "Y": 0.12864316999912262}, {"X": 0.4015396237373352, "Y": 0.13841275870800018}, {"X": 0.3854249119758606, "Y": 0.1383330374956131}]}, "Id": "d1f0d848-fc1b-47dd-ae4b-4f1c4e9e21d5"}, {"BlockType": "WORD", "Confidence": 97.13906860351562, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04872939735651016, "Height": 0.011038156226277351, "Left": 0.40021800994873047, "Top": 0.12888260185718536}, "Polygon": [{"X": 0.4002813994884491, "Y": 0.12888260185718536}, {"X": 0.44894739985466003, "Y": 0.12912346422672272}, {"X": 0.4488842785358429, "Y": 0.13992075622081757}, {"X": 0.40021800994873047, "Y": 0.13967998325824738}]}, "Id": "4f5139ca-5852-4329-85ed-6d2d06a5ed80"}, {"BlockType": "WORD", "Confidence": 99.85192108154297, "Text": "Waste", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03863433003425598, "Height": 0.010500683449208736, "Left": 0.4538421928882599, "Top": 0.13009808957576752}, "Polygon": [{"X": 0.4539024531841278, "Y": 0.13009808957576752}, {"X": 0.49247655272483826, "Y": 0.1302890032529831}, {"X": 0.49241650104522705, "Y": 0.14059877395629883}, {"X": 0.4538421928882599, "Y": 0.14040794968605042}]}, "Id": "25bd5832-14b8-4fc0-8c39-33fecf816e29"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Systems", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0521756187081337, "Height": 0.012927940115332603, "Left": 0.4963516294956207, "Top": 0.1309441775083542}, "Polygon": [{"X": 0.4964253902435303, "Y": 0.1309441775083542}, {"X": 0.5485272407531738, "Y": 0.13120201230049133}, {"X": 0.5484538078308105, "Y": 0.14387211203575134}, {"X": 0.4963516294956207, "Y": 0.14361438155174255}]}, "Id": "13b62e21-a088-4b3d-90ae-6c8b5b01f567"}, {"BlockType": "WORD", "Confidence": 97.49122619628906, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019428865984082222, "Height": 0.0100339874625206, "Left": 0.5529258251190186, "Top": 0.13157446682453156}, "Polygon": [{"X": 0.5529834032058716, "Y": 0.13157446682453156}, {"X": 0.5723546743392944, "Y": 0.13167034089565277}, {"X": 0.572297215461731, "Y": 0.14160846173763275}, {"X": 0.5529258251190186, "Y": 0.14151263236999512}]}, "Id": "c8e89de2-a81f-4ff2-8ee3-f65daa211e66"}, {"BlockType": "WORD", "Confidence": 45.590721130371094, "Text": "bebunker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0801999643445015, "Height": 0.011673789471387863, "Left": 0.31772541999816895, "Top": 0.14159095287322998}, "Polygon": [{"X": 0.3177920877933502, "Y": 0.14159095287322998}, {"X": 0.39792537689208984, "Y": 0.1419873684644699}, {"X": 0.3978591561317444, "Y": 0.15326474606990814}, {"X": 0.31772541999816895, "Y": 0.15286849439144135}]}, "Id": "7f8fddad-960e-4d34-b8eb-83fbbd5f1691"}, {"BlockType": "WORD", "Confidence": 44.25362777709961, "Text": "HILL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03572849929332733, "Height": 0.010275071486830711, "Left": 0.4028872847557068, "Top": 0.1439487785100937}, "Polygon": [{"X": 0.4029465615749359, "Y": 0.1439487785100937}, {"X": 0.4386157989501953, "Y": 0.14412522315979004}, {"X": 0.4385567009449005, "Y": 0.15422384440898895}, {"X": 0.4028872847557068, "Y": 0.15404747426509857}]}, "Id": "3c12e21e-b4d6-40df-bff4-5f74d9892b54"}, {"BlockType": "WORD", "Confidence": 98.70037078857422, "Text": "INDUSTRIAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09075640887022018, "Height": 0.011353195644915104, "Left": 0.4432333707809448, "Top": 0.14482441544532776}, "Polygon": [{"X": 0.4432971477508545, "Y": 0.14482441544532776}, {"X": 0.5339897871017456, "Y": 0.14527301490306854}, {"X": 0.5339264869689941, "Y": 0.1561776101589203}, {"X": 0.4432333707809448, "Y": 0.15572918951511383}]}, "Id": "d9b648c8-2514-4aff-a343-6ec1cd1849c3"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "PARK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03933507949113846, "Height": 0.010251585394144058, "Left": 0.538771390914917, "Top": 0.1464243233203888}, "Polygon": [{"X": 0.5388297438621521, "Y": 0.1464243233203888}, {"X": 0.5781064629554749, "Y": 0.14661858975887299}, {"X": 0.5780483484268188, "Y": 0.15667590498924255}, {"X": 0.538771390914917, "Y": 0.15648169815540314}]}, "Id": "3175ac07-061f-4d40-8dba-18c4c94fca5a"}, {"BlockType": "WORD", "Confidence": 93.5618667602539, "Text": "617-242-7746P", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10111503303050995, "Height": 0.013142040930688381, "Left": 0.31291866302490234, "Top": 0.1550382524728775}, "Polygon": [{"X": 0.31299343705177307, "Y": 0.1550382524728775}, {"X": 0.4140337109565735, "Y": 0.15553784370422363}, {"X": 0.4139595627784729, "Y": 0.16818030178546906}, {"X": 0.31291866302490234, "Y": 0.16768093407154083}]}, "Id": "11b0f53c-e8a2-4b12-97b1-38fbf34544f5"}, {"BlockType": "WORD", "Confidence": 94.775390625, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04905740171670914, "Height": 0.010536763817071915, "Left": 0.4210442006587982, "Top": 0.1590912640094757}, "Polygon": [{"X": 0.4211045205593109, "Y": 0.1590912640094757}, {"X": 0.47010159492492676, "Y": 0.1593335121870041}, {"X": 0.47004151344299316, "Y": 0.16962803900241852}, {"X": 0.4210442006587982, "Y": 0.1693858802318573}]}, "Id": "05fb5c06-1c89-4a3d-ab4e-daa6e3988de6"}, {"BlockType": "WORD", "Confidence": 97.57832336425781, "Text": "HOUSE617-242-3649F", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1491563767194748, "Height": 0.013748466037213802, "Left": 0.4749038815498352, "Top": 0.15981417894363403}, "Polygon": [{"X": 0.47497978806495667, "Y": 0.15981417894363403}, {"X": 0.6240602731704712, "Y": 0.16055120527744293}, {"X": 0.6239853501319885, "Y": 0.1735626459121704}, {"X": 0.4749038815498352, "Y": 0.17282596230506897}]}, "Id": "7c007002-cfd3-42b9-bb46-2cd1b5551f13"}, {"BlockType": "WORD", "Confidence": 67.16857147216797, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04291741922497749, "Height": 0.011655844748020172, "Left": 0.312301903963089, "Top": 0.22036512196063995}, "Polygon": [{"X": 0.31236961483955383, "Y": 0.22036512196063995}, {"X": 0.3552193343639374, "Y": 0.22057649493217468}, {"X": 0.355151891708374, "Y": 0.23202097415924072}, {"X": 0.312301903963089, "Y": 0.23180967569351196}]}, "Id": "cf91ae09-b4ac-405e-89ed-9c6ddc017137"}, {"BlockType": "WORD", "Confidence": 99.69407653808594, "Text": "1242095", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05439151078462601, "Height": 0.010975967161357403, "Left": 0.3626664876937866, "Top": 0.22324733436107635}, "Polygon": [{"X": 0.362729549407959, "Y": 0.22324733436107635}, {"X": 0.41705799102783203, "Y": 0.2235153168439865}, {"X": 0.41699522733688354, "Y": 0.23422330617904663}, {"X": 0.3626664876937866, "Y": 0.23395542800426483}]}, "Id": "0e350354-fc3c-46ba-a0b7-a2e45210e0a4"}, {"BlockType": "WORD", "Confidence": 54.949974060058594, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03567809611558914, "Height": 0.011899679899215698, "Left": 0.3205167055130005, "Top": 0.23747386038303375}, "Polygon": [{"X": 0.32058602571487427, "Y": 0.23747386038303375}, {"X": 0.3561948239803314, "Y": 0.23764941096305847}, {"X": 0.35612571239471436, "Y": 0.24937354028224945}, {"X": 0.3205167055130005, "Y": 0.2491980642080307}]}, "Id": "2010fb34-7144-4403-b7c9-e0ca8a3653ce"}, {"BlockType": "WORD", "Confidence": 99.16832733154297, "Text": "10/6/2022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06310898810625076, "Height": 0.011126695200800896, "Left": 0.36148589849472046, "Top": 0.24060937762260437}, "Polygon": [{"X": 0.36154961585998535, "Y": 0.24060937762260437}, {"X": 0.4245948791503906, "Y": 0.24092015624046326}, {"X": 0.4245315194129944, "Y": 0.2517360746860504}, {"X": 0.36148589849472046, "Y": 0.2514254152774811}]}, "Id": "0481249c-e930-42c9-874d-421fd725940b"}, {"BlockType": "WORD", "Confidence": 95.97496795654297, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03767063841223717, "Height": 0.011637111194431782, "Left": 0.31960150599479675, "Top": 0.2551310658454895}, "Polygon": [{"X": 0.3196692168712616, "Y": 0.2551310658454895}, {"X": 0.3572721481323242, "Y": 0.2553163170814514}, {"X": 0.3572046756744385, "Y": 0.2667681574821472}, {"X": 0.31960150599479675, "Y": 0.26658299565315247}]}, "Id": "6ccb5236-dbc7-4f4d-bc59-77ab9281a454"}, {"BlockType": "WORD", "Confidence": 98.916015625, "Text": "17:16:09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05414709821343422, "Height": 0.010190028697252274, "Left": 0.362822562456131, "Top": 0.2581615149974823}, "Polygon": [{"X": 0.36288100481033325, "Y": 0.2581615149974823}, {"X": 0.4169696569442749, "Y": 0.2584279775619507}, {"X": 0.4169114828109741, "Y": 0.26835155487060547}, {"X": 0.362822562456131, "Y": 0.26808518171310425}]}, "Id": "93fe6ff4-39b8-41d4-af03-797b72bf0203"}, {"BlockType": "WORD", "Confidence": 88.84536743164062, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005118990316987038, "Height": 0.0023772984277457, "Left": 0.4213391840457916, "Top": 0.2638442814350128}, "Polygon": [{"X": 0.42135295271873474, "Y": 0.2638442814350128}, {"X": 0.4264581799507141, "Y": 0.26386940479278564}, {"X": 0.426444411277771, "Y": 0.2662215828895569}, {"X": 0.4213391840457916, "Y": 0.26619642972946167}]}, "Id": "ba80aed5-9f10-4bf5-8d3e-545a51420628"}, {"BlockType": "WORD", "Confidence": 99.296875, "Text": "18:56:34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05341571941971779, "Height": 0.010215654037892818, "Left": 0.43170052766799927, "Top": 0.2599744498729706}, "Polygon": [{"X": 0.4317588210105896, "Y": 0.2599744498729706}, {"X": 0.48511627316474915, "Y": 0.26023730635643005}, {"X": 0.4850582480430603, "Y": 0.27019011974334717}, {"X": 0.43170052766799927, "Y": 0.26992735266685486}]}, "Id": "49a4a897-3589-4f16-9871-753d606c20ae"}, {"BlockType": "WORD", "Confidence": 87.93307495117188, "Text": "Customer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06673996150493622, "Height": 0.011794360354542732, "Left": 0.32424992322921753, "Top": 0.28927043080329895}, "Polygon": [{"X": 0.32431769371032715, "Y": 0.28927043080329895}, {"X": 0.39098989963531494, "Y": 0.2895984947681427}, {"X": 0.39092254638671875, "Y": 0.30106478929519653}, {"X": 0.32424992322921753, "Y": 0.30073684453964233}]}, "Id": "7440f5d0-9c0c-4595-88e1-115b6461bdc3"}, {"BlockType": "WORD", "Confidence": 91.84490203857422, "Text": "KO00508/EARTHWATCH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17145557701587677, "Height": 0.013506592251360416, "Left": 0.39537447690963745, "Top": 0.29161974787712097}, "Polygon": [{"X": 0.39544883370399475, "Y": 0.29161974787712097}, {"X": 0.566830039024353, "Y": 0.2924630343914032}, {"X": 0.5667567849159241, "Y": 0.3051263391971588}, {"X": 0.39537447690963745, "Y": 0.3042834401130676}]}, "Id": "37164c00-b1ec-44f9-b02f-ed3c935cbb83"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "WASTE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05017950013279915, "Height": 0.010992341674864292, "Left": 0.5708192586898804, "Top": 0.2956540584564209}, "Polygon": [{"X": 0.5708814263343811, "Y": 0.2956540584564209}, {"X": 0.6209987998008728, "Y": 0.2959006130695343}, {"X": 0.620936930179596, "Y": 0.3066464066505432}, {"X": 0.5708192586898804, "Y": 0.3063999116420746}]}, "Id": "fd1aed08-f18a-401e-83d0-7d9e5da296e7"}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "SYSTEM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05770363658666611, "Height": 0.00991800893098116, "Left": 0.6245996952056885, "Top": 0.29844045639038086}, "Polygon": [{"X": 0.6246551871299744, "Y": 0.29844045639038086}, {"X": 0.6823033690452576, "Y": 0.29872405529022217}, {"X": 0.6822481751441956, "Y": 0.30835849046707153}, {"X": 0.6245996952056885, "Y": 0.3080749809741974}]}, "Id": "3e5c3c6f-9b18-481f-858b-4a9504efe5ce"}, {"BlockType": "WORD", "Confidence": 94.78973388671875, "Text": "Truck:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04228206351399422, "Height": 0.011030959896743298, "Left": 0.3359329402446747, "Top": 0.3053954839706421}, "Polygon": [{"X": 0.3359968364238739, "Y": 0.3053954839706421}, {"X": 0.3782150149345398, "Y": 0.30560311675071716}, {"X": 0.3781513571739197, "Y": 0.31642645597457886}, {"X": 0.3359329402446747, "Y": 0.31621891260147095}]}, "Id": "0e4f4c77-eb8e-457a-9e99-5bbb71d862fd"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "280230", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04770244285464287, "Height": 0.010223560966551304, "Left": 0.3826358914375305, "Top": 0.30712154507637024}, "Polygon": [{"X": 0.3826946020126343, "Y": 0.30712154507637024}, {"X": 0.4303383231163025, "Y": 0.3073558211326599}, {"X": 0.43027985095977783, "Y": 0.3173450827598572}, {"X": 0.3826358914375305, "Y": 0.31711089611053467}]}, "Id": "f6b5f3d0-eac9-4037-85d2-169b24f1783c"}, {"BlockType": "WORD", "Confidence": 76.36399841308594, "Text": "Trailer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04610583931207657, "Height": 0.010874190367758274, "Left": 0.32815098762512207, "Top": 0.3218071758747101}, "Polygon": [{"X": 0.3282139003276825, "Y": 0.3218071758747101}, {"X": 0.37425684928894043, "Y": 0.32203346490859985}, {"X": 0.3741942048072815, "Y": 0.3326813578605652}, {"X": 0.32815098762512207, "Y": 0.3324551582336426}]}, "Id": "d0a8b8f7-83d4-49fc-bae2-19d8b2663a6a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "19065", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03772742673754692, "Height": 0.009970528073608875, "Left": 0.38062584400177, "Top": 0.32364681363105774}, "Polygon": [{"X": 0.38068339228630066, "Y": 0.32364681363105774}, {"X": 0.41835325956344604, "Y": 0.3238319456577301}, {"X": 0.4182959198951721, "Y": 0.33361735939979553}, {"X": 0.38062584400177, "Y": 0.33343228697776794}]}, "Id": "aba62a49-43bb-4244-a3cb-fbbd072ea44a"}, {"BlockType": "WORD", "Confidence": 92.94483947753906, "Text": "Destination:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07656341046094894, "Height": 0.011384970508515835, "Left": 0.3131166994571686, "Top": 0.33830708265304565}, "Polygon": [{"X": 0.3131818175315857, "Y": 0.33830708265304565}, {"X": 0.3896801173686981, "Y": 0.3386828601360321}, {"X": 0.38961541652679443, "Y": 0.34969207644462585}, {"X": 0.3131166994571686, "Y": 0.34931644797325134}]}, "Id": "66f778c0-adcc-4757-b3c5-564dd0c3df78"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "A/A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026932334527373314, "Height": 0.00952256191521883, "Left": 0.3940519392490387, "Top": 0.3410266041755676}, "Polygon": [{"X": 0.39410707354545593, "Y": 0.3410266041755676}, {"X": 0.42098426818847656, "Y": 0.34115859866142273}, {"X": 0.4209292531013489, "Y": 0.3505491614341736}, {"X": 0.3940519392490387, "Y": 0.35041719675064087}]}, "Id": "86b4c6cb-c8ba-49fb-b09c-caa5cd9a4432"}, {"BlockType": "WORD", "Confidence": 98.08912658691406, "Text": "P.O.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03199295327067375, "Height": 0.009956990368664265, "Left": 0.34114736318588257, "Top": 0.3561957776546478}, "Polygon": [{"X": 0.3412051796913147, "Y": 0.3561957776546478}, {"X": 0.3731403052806854, "Y": 0.3563525676727295}, {"X": 0.37308263778686523, "Y": 0.3661527931690216}, {"X": 0.34114736318588257, "Y": 0.3659960627555847}]}, "Id": "a249ea6d-aa48-4724-94a0-e921b1c041d6"}, {"BlockType": "WORD", "Confidence": 98.49170684814453, "Text": "EARTH100622-3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1109062135219574, "Height": 0.01160546112805605, "Left": 0.37800148129463196, "Top": 0.3571803867816925}, "Polygon": [{"X": 0.3780665397644043, "Y": 0.3571803867816925}, {"X": 0.48890769481658936, "Y": 0.35772448778152466}, {"X": 0.48884326219558716, "Y": 0.3687858581542969}, {"X": 0.37800148129463196, "Y": 0.36824196577072144}]}, "Id": "93c59f06-5548-4b45-ac18-0f3e8439c5ad"}, {"BlockType": "WORD", "Confidence": 97.4814682006836, "Text": "D.O.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03401770070195198, "Height": 0.010034357197582722, "Left": 0.34133777022361755, "Top": 0.37284141778945923}, "Polygon": [{"X": 0.3413960039615631, "Y": 0.37284141778945923}, {"X": 0.37535548210144043, "Y": 0.37300801277160645}, {"X": 0.3752974271774292, "Y": 0.3828757703304291}, {"X": 0.34133777022361755, "Y": 0.38270920515060425}]}, "Id": "dbde1089-499a-48af-bec6-d0b39386ef86"}, {"BlockType": "WORD", "Confidence": 99.56951904296875, "Text": "2277043", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05456007644534111, "Height": 0.010150627233088017, "Left": 0.3799869418144226, "Top": 0.3740125894546509}, "Polygon": [{"X": 0.380045086145401, "Y": 0.3740125894546509}, {"X": 0.4345470368862152, "Y": 0.3742799758911133}, {"X": 0.4344891905784607, "Y": 0.3841632306575775}, {"X": 0.3799869418144226, "Y": 0.3838959336280823}]}, "Id": "783f6327-5b5f-4a54-967d-aa18699525c7"}, {"BlockType": "WORD", "Confidence": 99.6827163696289, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03035447746515274, "Height": 0.010152951814234257, "Left": 0.34089675545692444, "Top": 0.38938456773757935}, "Polygon": [{"X": 0.34095579385757446, "Y": 0.38938456773757935}, {"X": 0.3712512254714966, "Y": 0.3895331025123596}, {"X": 0.3711923658847809, "Y": 0.39953750371932983}, {"X": 0.34089675545692444, "Y": 0.39938902854919434}]}, "Id": "e4fb549d-8974-4bc8-90c5-5540869da371"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "519044", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04704640805721283, "Height": 0.010313917882740498, "Left": 0.375666081905365, "Top": 0.3902852237224579}, "Polygon": [{"X": 0.3757253885269165, "Y": 0.3902852237224579}, {"X": 0.422712504863739, "Y": 0.39051559567451477}, {"X": 0.4226534068584442, "Y": 0.4005991518497467}, {"X": 0.375666081905365, "Y": 0.4003688395023346}]}, "Id": "b9533df0-7279-461a-b766-da035e3c506f"}, {"BlockType": "WORD", "Confidence": 87.0567626953125, "Text": "Comment:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06625436246395111, "Height": 0.010924084112048149, "Left": 0.3283649981021881, "Top": 0.4223615825176239}, "Polygon": [{"X": 0.32842761278152466, "Y": 0.4223615825176239}, {"X": 0.3946193754673004, "Y": 0.42268574237823486}, {"X": 0.3945571184158325, "Y": 0.433285653591156}, {"X": 0.3283649981021881, "Y": 0.4329616129398346}]}, "Id": "ecbfdc61-91b3-41d5-bd21-f6a476d4f85b"}, {"BlockType": "WORD", "Confidence": 97.87308502197266, "Text": "PARAKA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06336359679698944, "Height": 0.010939722880721092, "Left": 0.3983917832374573, "Top": 0.4237721264362335}, "Polygon": [{"X": 0.3984541893005371, "Y": 0.4237721264362335}, {"X": 0.4617553651332855, "Y": 0.42408210039138794}, {"X": 0.46169331669807434, "Y": 0.43471184372901917}, {"X": 0.3983917832374573, "Y": 0.4344019591808319}]}, "Id": "cb54d900-539c-4a54-9c47-eed0a47f8af7"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "CORPORATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10838963091373444, "Height": 0.011533956974744797, "Left": 0.4654758870601654, "Top": 0.4254903793334961}, "Polygon": [{"X": 0.4655401110649109, "Y": 0.4254903793334961}, {"X": 0.573865532875061, "Y": 0.4260208308696747}, {"X": 0.5738018751144409, "Y": 0.4370243549346924}, {"X": 0.4654758870601654, "Y": 0.4364941120147705}]}, "Id": "10573341-0a47-44ef-9f7c-b357737d5db6"}, {"BlockType": "WORD", "Confidence": 98.99374389648438, "Text": "Gross:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04069795086979866, "Height": 0.00968192145228386, "Left": 0.3167014718055725, "Top": 0.48309969902038574}, "Polygon": [{"X": 0.31675755977630615, "Y": 0.48309969902038574}, {"X": 0.35739943385124207, "Y": 0.48329827189445496}, {"X": 0.35734355449676514, "Y": 0.4927816092967987}, {"X": 0.3167014718055725, "Y": 0.4925830662250519}]}, "Id": "db133b20-6fe2-4c26-a28d-b878557810ca"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "75220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039868634194135666, "Height": 0.009570030495524406, "Left": 0.3696237802505493, "Top": 0.48416781425476074}, "Polygon": [{"X": 0.36967897415161133, "Y": 0.48416781425476074}, {"X": 0.4094924032688141, "Y": 0.4843623638153076}, {"X": 0.4094374179840088, "Y": 0.4937378466129303}, {"X": 0.3696237802505493, "Y": 0.4935433864593506}]}, "Id": "cc20b4a7-b277-48f3-abc9-a1ae09b02284"}, {"BlockType": "WORD", "Confidence": 49.08243179321289, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009671376086771488, "Height": 0.009152197279036045, "Left": 0.41338294744491577, "Top": 0.48501336574554443}, "Polygon": [{"X": 0.4134363532066345, "Y": 0.48501336574554443}, {"X": 0.4230543375015259, "Y": 0.48506036400794983}, {"X": 0.4230009913444519, "Y": 0.4941655695438385}, {"X": 0.41338294744491577, "Y": 0.4941186010837555}]}, "Id": "6576ddeb-4414-44b6-99cf-700dff266ba1"}, {"BlockType": "WORD", "Confidence": 99.34410858154297, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023814937099814415, "Height": 0.009353119879961014, "Left": 0.426374226808548, "Top": 0.4855843484401703}, "Polygon": [{"X": 0.42642831802368164, "Y": 0.4855843484401703}, {"X": 0.45018914341926575, "Y": 0.48570045828819275}, {"X": 0.45013517141342163, "Y": 0.4949374794960022}, {"X": 0.426374226808548, "Y": 0.4948213994503021}]}, "Id": "a785e1a4-1f81-4c7a-9583-2764d1996189"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03386589512228966, "Height": 0.009759546257555485, "Left": 0.4536636173725128, "Top": 0.48598527908325195}, "Polygon": [{"X": 0.4537196755409241, "Y": 0.48598527908325195}, {"X": 0.4875295162200928, "Y": 0.48615050315856934}, {"X": 0.48747360706329346, "Y": 0.49574482440948486}, {"X": 0.4536636173725128, "Y": 0.49557968974113464}]}, "Id": "a9a2d348-7e12-43f7-8a96-72d078723018"}, {"BlockType": "WORD", "Confidence": 99.29528045654297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007187751587480307, "Height": 0.008739553391933441, "Left": 0.49107643961906433, "Top": 0.48693186044692993}, "Polygon": [{"X": 0.4911271333694458, "Y": 0.48693186044692993}, {"X": 0.4982641935348511, "Y": 0.48696672916412354}, {"X": 0.498213529586792, "Y": 0.49567142128944397}, {"X": 0.49107643961906433, "Y": 0.49563655257225037}]}, "Id": "a3678387-0800-4295-8957-db80b934584b"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "Tare:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0325118750333786, "Height": 0.010187622159719467, "Left": 0.3197880685329437, "Top": 0.49978455901145935}, "Polygon": [{"X": 0.31984734535217285, "Y": 0.49978455901145935}, {"X": 0.3522999584674835, "Y": 0.4999430477619171}, {"X": 0.35224083065986633, "Y": 0.5099721550941467}, {"X": 0.3197880685329437, "Y": 0.5098137259483337}]}, "Id": "9843cd09-db0c-437c-82aa-1bfa88a498ef"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "33720", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03994288295507431, "Height": 0.009738489985466003, "Left": 0.3647253215312958, "Top": 0.5010092854499817}, "Polygon": [{"X": 0.36478152871131897, "Y": 0.5010092854499817}, {"X": 0.4046682119369507, "Y": 0.5012040734291077}, {"X": 0.4046122133731842, "Y": 0.5107477903366089}, {"X": 0.3647253215312958, "Y": 0.5105530619621277}]}, "Id": "4cfde778-9b0f-4247-ae42-93b2a125f09b"}, {"BlockType": "WORD", "Confidence": 97.45156860351562, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009251260198652744, "Height": 0.009305992163717747, "Left": 0.40889450907707214, "Top": 0.5018593072891235}, "Polygon": [{"X": 0.4089488387107849, "Y": 0.5018593072891235}, {"X": 0.4181457757949829, "Y": 0.5019041895866394}, {"X": 0.4180915057659149, "Y": 0.5111652612686157}, {"X": 0.40889450907707214, "Y": 0.5111203789710999}]}, "Id": "c3488593-0500-431f-92e6-e0c6e0f2c04c"}, {"BlockType": "WORD", "Confidence": 99.01786041259766, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013264147564768791, "Height": 0.009810893796384335, "Left": 0.42200154066085815, "Top": 0.5019635558128357}, "Polygon": [{"X": 0.422058641910553, "Y": 0.5019635558128357}, {"X": 0.4352656900882721, "Y": 0.5020280480384827}, {"X": 0.43520867824554443, "Y": 0.511774480342865}, {"X": 0.42200154066085815, "Y": 0.511709988117218}]}, "Id": "904242e4-3442-43a2-a517-e21dfcf494b0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033619385212659836, "Height": 0.00963678676635027, "Left": 0.4423995912075043, "Top": 0.502642035484314}, "Polygon": [{"X": 0.442454993724823, "Y": 0.502642035484314}, {"X": 0.4760189950466156, "Y": 0.5028059482574463}, {"X": 0.4759637415409088, "Y": 0.5122787952423096}, {"X": 0.4423995912075043, "Y": 0.5121150016784668}]}, "Id": "9e0502ec-52b5-40f6-bfa4-e523e49a228e"}, {"BlockType": "WORD", "Confidence": 99.61834716796875, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0071524977684021, "Height": 0.009112494066357613, "Left": 0.47998109459877014, "Top": 0.5032621622085571}, "Polygon": [{"X": 0.4800340235233307, "Y": 0.5032621622085571}, {"X": 0.48713359236717224, "Y": 0.5032968521118164}, {"X": 0.4870806932449341, "Y": 0.5123746395111084}, {"X": 0.47998109459877014, "Y": 0.5123400092124939}]}, "Id": "e8967fda-b574-4015-b862-5d24d6a86466"}, {"BlockType": "WORD", "Confidence": 99.17350769042969, "Text": "Net:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026890529319643974, "Height": 0.009816017001867294, "Left": 0.3245672583580017, "Top": 0.5166094899177551}, "Polygon": [{"X": 0.3246244788169861, "Y": 0.5166094899177551}, {"X": 0.35145777463912964, "Y": 0.5167404413223267}, {"X": 0.3514007031917572, "Y": 0.5264255404472351}, {"X": 0.3245672583580017, "Y": 0.5262945890426636}]}, "Id": "8ea81e60-a1a0-4009-87ec-4e19100caafa"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "41500", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03935612365603447, "Height": 0.009268770925700665, "Left": 0.3644939064979553, "Top": 0.5177735090255737}, "Polygon": [{"X": 0.36454734206199646, "Y": 0.5177735090255737}, {"X": 0.4038500189781189, "Y": 0.5179653763771057}, {"X": 0.4037967622280121, "Y": 0.5270423293113708}, {"X": 0.3644939064979553, "Y": 0.5268505811691284}]}, "Id": "587844ec-f99f-43af-a061-29aee4f1bca1"}, {"BlockType": "WORD", "Confidence": 97.9063720703125, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009818828664720058, "Height": 0.009272006340324879, "Left": 0.40792855620384216, "Top": 0.518108606338501}, "Polygon": [{"X": 0.4079826772212982, "Y": 0.518108606338501}, {"X": 0.4177474081516266, "Y": 0.5181562900543213}, {"X": 0.4176933467388153, "Y": 0.527380645275116}, {"X": 0.40792855620384216, "Y": 0.5273330211639404}]}, "Id": "6fd4c8fa-8990-403f-97f7-22632804db77"}, {"BlockType": "WORD", "Confidence": 98.32748413085938, "Text": "Tons:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0344206877052784, "Height": 0.009963513351976871, "Left": 0.3197515904903412, "Top": 0.5419918298721313}, "Polygon": [{"X": 0.3198094964027405, "Y": 0.5419918298721313}, {"X": 0.3541722893714905, "Y": 0.542159378528595}, {"X": 0.3541145622730255, "Y": 0.5519553422927856}, {"X": 0.3197515904903412, "Y": 0.5517878532409668}]}, "Id": "26e18808-7edb-4e4c-9cb8-c51153fbebb6"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "20.75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035463109612464905, "Height": 0.009373500943183899, "Left": 0.3585692048072815, "Top": 0.5430619716644287}, "Polygon": [{"X": 0.3586234152317047, "Y": 0.5430619716644287}, {"X": 0.3940322995185852, "Y": 0.5432345867156982}, {"X": 0.3939782679080963, "Y": 0.5524354577064514}, {"X": 0.3585692048072815, "Y": 0.5522628426551819}]}, "Id": "d118bc80-e74b-4e48-b4ad-0f829805f020"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Materials", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05946385860443115, "Height": 0.010248019360005856, "Left": 0.31283873319625854, "Top": 0.5759910941123962}, "Polygon": [{"X": 0.31289762258529663, "Y": 0.5759910941123962}, {"X": 0.3723025918006897, "Y": 0.5762803554534912}, {"X": 0.3722440004348755, "Y": 0.5862390995025635}, {"X": 0.31283873319625854, "Y": 0.5859498977661133}]}, "Id": "55107d48-726b-4fba-9bd2-649491e5980a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01215983834117651, "Height": 0.009380309842526913, "Left": 0.3764941096305847, "Top": 0.5768941640853882}, "Polygon": [{"X": 0.3765489459037781, "Y": 0.5768941640853882}, {"X": 0.388653963804245, "Y": 0.576953113079071}, {"X": 0.3885991871356964, "Y": 0.5862745046615601}, {"X": 0.3764941096305847, "Y": 0.5862155556678772}]}, "Id": "bb0c458b-2c90-44cc-bd4d-d9909335dbcc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Services", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052703388035297394, "Height": 0.00943439919501543, "Left": 0.39305558800697327, "Top": 0.5773492455482483}, "Polygon": [{"X": 0.3931094706058502, "Y": 0.5773492455482483}, {"X": 0.44575896859169006, "Y": 0.577605664730072}, {"X": 0.4457052946090698, "Y": 0.5867836475372314}, {"X": 0.39305558800697327, "Y": 0.5865273475646973}]}, "Id": "04111ed9-ce76-4f0b-9380-374a0bc8764e"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "Origin:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04339207708835602, "Height": 0.012278199195861816, "Left": 0.3315693140029907, "Top": 0.6195129156112671}, "Polygon": [{"X": 0.3316405415534973, "Y": 0.6195129156112671}, {"X": 0.37496137619018555, "Y": 0.6197235584259033}, {"X": 0.37489038705825806, "Y": 0.6317911148071289}, {"X": 0.3315693140029907, "Y": 0.6315805315971375}]}, "Id": "99d338bb-5f54-436d-8b14-913fff33f074"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "NA/NON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05989810451865196, "Height": 0.009247828274965286, "Left": 0.37971600890159607, "Top": 0.6202352643013}, "Polygon": [{"X": 0.3797686696052551, "Y": 0.6202352643013}, {"X": 0.43961411714553833, "Y": 0.6205262541770935}, {"X": 0.43956172466278076, "Y": 0.6294831037521362}, {"X": 0.37971600890159607, "Y": 0.6291921734809875}]}, "Id": "3435e38d-3c04-40af-bb24-a3ea50724615"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "APPLICABLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0967099517583847, "Height": 0.010084218345582485, "Left": 0.44346755743026733, "Top": 0.6208680868148804}, "Polygon": [{"X": 0.4435237944126129, "Y": 0.6208680868148804}, {"X": 0.5401775240898132, "Y": 0.6213380694389343}, {"X": 0.5401217937469482, "Y": 0.6309522986412048}, {"X": 0.44346755743026733, "Y": 0.6304824948310852}]}, "Id": "4315e934-52ba-432e-a2dc-95f122d22cb3"}, {"BlockType": "WORD", "Confidence": 98.00701904296875, "Text": "Material:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05658949166536331, "Height": 0.009761719964444637, "Left": 0.3242013454437256, "Top": 0.6367433667182922}, "Polygon": [{"X": 0.32425740361213684, "Y": 0.6367433667182922}, {"X": 0.3807908296585083, "Y": 0.637018084526062}, {"X": 0.3807350695133209, "Y": 0.6465050578117371}, {"X": 0.3242013454437256, "Y": 0.6462304592132568}]}, "Id": "7f9e37df-5028-4a1a-abc3-82314af7f1d3"}, {"BlockType": "WORD", "Confidence": 97.83084106445312, "Text": "539/OBR6-MXD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10909897834062576, "Height": 0.009877696633338928, "Left": 0.3856588900089264, "Top": 0.6371699571609497}, "Polygon": [{"X": 0.3857138156890869, "Y": 0.6371699571609497}, {"X": 0.49475786089897156, "Y": 0.6376998424530029}, {"X": 0.4947034418582916, "Y": 0.6470476984977722}, {"X": 0.3856588900089264, "Y": 0.6465179920196533}]}, "Id": "ffd48f94-2e2d-4851-ac44-dfab841a6283"}, {"BlockType": "WORD", "Confidence": 98.32748413085938, "Text": "COMMODITIES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10968264937400818, "Height": 0.01021244190633297, "Left": 0.4987153112888336, "Top": 0.638456404209137}, "Polygon": [{"X": 0.49877163767814636, "Y": 0.638456404209137}, {"X": 0.6083979606628418, "Y": 0.6389890909194946}, {"X": 0.608342170715332, "Y": 0.6486688256263733}, {"X": 0.4987153112888336, "Y": 0.64813631772995}]}, "Id": "8fb9aecb-c607-4510-bf17-8d6df3f2daf2"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "BALED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0524984747171402, "Height": 0.009435215033590794, "Left": 0.613109290599823, "Top": 0.6401674747467041}, "Polygon": [{"X": 0.6131621599197388, "Y": 0.6401674747467041}, {"X": 0.665607750415802, "Y": 0.6404222846031189}, {"X": 0.6655551195144653, "Y": 0.6496026515960693}, {"X": 0.613109290599823, "Y": 0.6493479013442993}]}, "Id": "7d03d974-7a80-4c54-b773-539c84772fe7"}, {"BlockType": "WORD", "Confidence": 68.66051483154297, "Text": "Quantity.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058948639780282974, "Height": 0.012424448505043983, "Left": 0.3233567476272583, "Top": 0.65323805809021}, "Polygon": [{"X": 0.3234284520149231, "Y": 0.65323805809021}, {"X": 0.382305383682251, "Y": 0.6535239815711975}, {"X": 0.38223403692245483, "Y": 0.6656625270843506}, {"X": 0.3233567476272583, "Y": 0.6653767228126526}]}, "Id": "c975a7e2-c489-418c-bb00-cf2005750317"}, {"BlockType": "WORD", "Confidence": 99.50035858154297, "Text": "20.75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03457600623369217, "Height": 0.00933137908577919, "Left": 0.3875028192996979, "Top": 0.6536481976509094}, "Polygon": [{"X": 0.38755667209625244, "Y": 0.6536481976509094}, {"X": 0.42207881808280945, "Y": 0.6538158655166626}, {"X": 0.4220251441001892, "Y": 0.6629796028137207}, {"X": 0.3875028192996979, "Y": 0.6628119945526123}]}, "Id": "9c6665ae-8883-43a7-b8f0-299e0b370f74"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "Ton", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024411803111433983, "Height": 0.009614494629204273, "Left": 0.42585545778274536, "Top": 0.6540493369102478}, "Polygon": [{"X": 0.4259110689163208, "Y": 0.6540493369102478}, {"X": 0.4502672553062439, "Y": 0.6541675925254822}, {"X": 0.450211763381958, "Y": 0.6636638045310974}, {"X": 0.42585545778274536, "Y": 0.6635456085205078}]}, "Id": "0bd12038-c57c-4d7e-bc68-ecf81456bf73"}, {"BlockType": "WORD", "Confidence": 92.54623413085938, "Text": "Weighmaster:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08668167889118195, "Height": 0.01267809234559536, "Left": 0.3085758686065674, "Top": 0.760949969291687}, "Polygon": [{"X": 0.30864840745925903, "Y": 0.760949969291687}, {"X": 0.3952575623989105, "Y": 0.7613689303398132}, {"X": 0.39518555998802185, "Y": 0.7736280560493469}, {"X": 0.3085758686065674, "Y": 0.773209273815155}]}, "Id": "58b82c1d-5871-412e-86a7-34189f37c570"}, {"BlockType": "WORD", "Confidence": 98.83509826660156, "Text": "TOM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03551974892616272, "Height": 0.009818433783948421, "Left": 0.39918383955955505, "Top": 0.7620888948440552}, "Polygon": [{"X": 0.3992404639720917, "Y": 0.7620888948440552}, {"X": 0.4347035884857178, "Y": 0.7622604370117188}, {"X": 0.4346471428871155, "Y": 0.7719073295593262}, {"X": 0.39918383955955505, "Y": 0.7717358469963074}]}, "Id": "98076157-fd30-4b57-92b1-340cd1d559fc"}, {"BlockType": "WORD", "Confidence": 98.75458526611328, "Text": "Driver:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04479655250906944, "Height": 0.009979024529457092, "Left": 0.3280671536922455, "Top": 0.7869653701782227}, "Polygon": [{"X": 0.3281247913837433, "Y": 0.7869653701782227}, {"X": 0.3728637099266052, "Y": 0.787181556224823}, {"X": 0.37280628085136414, "Y": 0.7969443798065186}, {"X": 0.3280671536922455, "Y": 0.796728253364563}]}, "Id": "2403b271-4397-4f87-9473-432b68c1882d"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019730260595679283, "Height": 0.014671525917947292, "Left": 0.30823075771331787, "Top": 0.8325653672218323}, "Polygon": [{"X": 0.3083169758319855, "Y": 0.8325653672218323}, {"X": 0.3279610276222229, "Y": 0.8326601386070251}, {"X": 0.32787492871284485, "Y": 0.8472368717193604}, {"X": 0.30823075771331787, "Y": 0.8471421599388123}]}, "Id": "37fb0c71-241a-4563-8acc-8de312266528"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "signing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05868439003825188, "Height": 0.014778271317481995, "Left": 0.3327901065349579, "Top": 0.8329450488090515}, "Polygon": [{"X": 0.33287566900253296, "Y": 0.8329450488090515}, {"X": 0.39147448539733887, "Y": 0.833227813243866}, {"X": 0.3913893401622772, "Y": 0.8477233648300171}, {"X": 0.3327901065349579, "Y": 0.847440779209137}]}, "Id": "5961864f-4734-44d9-822b-a3b7c3086521"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "above,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05491365119814873, "Height": 0.014238759875297546, "Left": 0.39667922258377075, "Top": 0.8332704901695251}, "Polygon": [{"X": 0.3967612683773041, "Y": 0.8332704901695251}, {"X": 0.4515928626060486, "Y": 0.8335350155830383}, {"X": 0.4515112340450287, "Y": 0.8475092649459839}, {"X": 0.39667922258377075, "Y": 0.8472448587417603}]}, "Id": "2da082ed-a098-40b9-9391-23de30e60f20"}, {"BlockType": "WORD", "Confidence": 92.80612182617188, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0036842254921793938, "Height": 0.011425729840993881, "Left": 0.45795416831970215, "Top": 0.8343266844749451}, "Polygon": [{"X": 0.45802077651023865, "Y": 0.8343266844749451}, {"X": 0.4616383910179138, "Y": 0.8343441486358643}, {"X": 0.4615718126296997, "Y": 0.8457524180412292}, {"X": 0.45795416831970215, "Y": 0.8457349538803101}]}, "Id": "cc9deb23-e5f8-4c38-9311-3c5057cc25b4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "declare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06149774417281151, "Height": 0.012433284893631935, "Left": 0.4670744240283966, "Top": 0.8342603445053101}, "Polygon": [{"X": 0.46714526414871216, "Y": 0.8342603445053101}, {"X": 0.5285722017288208, "Y": 0.8345566987991333}, {"X": 0.5285017490386963, "Y": 0.8466936349868774}, {"X": 0.4670744240283966, "Y": 0.8463973999023438}]}, "Id": "3dd08aa1-fbea-4f80-8f53-fbfbf9775e78"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033633362501859665, "Height": 0.01202031783759594, "Left": 0.5338762998580933, "Top": 0.8351235389709473}, "Polygon": [{"X": 0.5339450836181641, "Y": 0.8351235389709473}, {"X": 0.567509651184082, "Y": 0.835285484790802}, {"X": 0.5674410462379456, "Y": 0.847143828868866}, {"X": 0.5338762998580933, "Y": 0.8469820022583008}]}, "Id": "44dcb969-45b7-4dc3-9091-ae145ce5dfbe"}, {"BlockType": "WORD", "Confidence": 53.7310676574707, "Text": "il", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004274290520697832, "Height": 0.011270930990576744, "Left": 0.5730514526367188, "Top": 0.8359144330024719}, "Polygon": [{"X": 0.5731164813041687, "Y": 0.8359144330024719}, {"X": 0.577325701713562, "Y": 0.8359346985816956}, {"X": 0.5772606730461121, "Y": 0.8471853137016296}, {"X": 0.5730514526367188, "Y": 0.847165048122406}]}, "Id": "e806f1c6-e92d-4333-b87d-48607776ba04"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "did", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02533680386841297, "Height": 0.011935822665691376, "Left": 0.5830366611480713, "Top": 0.8354626893997192}, "Polygon": [{"X": 0.5831049084663391, "Y": 0.8354626893997192}, {"X": 0.6083734631538391, "Y": 0.8355845808982849}, {"X": 0.6083053946495056, "Y": 0.8473985195159912}, {"X": 0.5830366611480713, "Y": 0.8472766280174255}]}, "Id": "c9d54fac-a3ea-474a-b0ea-f6426437e063"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NOT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036433879286050797, "Height": 0.011879656463861465, "Left": 0.614332377910614, "Top": 0.8364363312721252}, "Polygon": [{"X": 0.614399790763855, "Y": 0.8364363312721252}, {"X": 0.6507662534713745, "Y": 0.836611807346344}, {"X": 0.6506990194320679, "Y": 0.8483160138130188}, {"X": 0.614332377910614, "Y": 0.8481406569480896}]}, "Id": "1319eb1c-426b-48c6-871d-499d1512217b"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "deposit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06106634810566902, "Height": 0.014614454470574856, "Left": 0.3073425590991974, "Top": 0.8514628410339355}, "Polygon": [{"X": 0.3074272572994232, "Y": 0.8514628410339355}, {"X": 0.3684088885784149, "Y": 0.8517568707466125}, {"X": 0.3683246374130249, "Y": 0.8660773038864136}, {"X": 0.3073425590991974, "Y": 0.8657834529876709}]}, "Id": "b6479638-32df-40be-a951-fd40721bfe19"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02916560508310795, "Height": 0.01179179735481739, "Left": 0.37381085753440857, "Top": 0.855587363243103}, "Polygon": [{"X": 0.37387940287590027, "Y": 0.855587363243103}, {"X": 0.40297645330429077, "Y": 0.8557276129722595}, {"X": 0.4029081165790558, "Y": 0.8673791289329529}, {"X": 0.37381085753440857, "Y": 0.8672389388084412}]}, "Id": "699f5e4b-3b7f-45f2-84a3-6741bf17f449"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PROHIBITED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10172966867685318, "Height": 0.013018500059843063, "Left": 0.4088815450668335, "Top": 0.8532465100288391}, "Polygon": [{"X": 0.40895500779151917, "Y": 0.8532465100288391}, {"X": 0.5106112360954285, "Y": 0.8537366390228271}, {"X": 0.5105383992195129, "Y": 0.8662649989128113}, {"X": 0.4088815450668335, "Y": 0.8657751679420471}]}, "Id": "1edf532d-4642-4dc4-beae-3c2036595d78"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "WASTES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06907268613576889, "Height": 0.011700140312314034, "Left": 0.5158686637878418, "Top": 0.8546884655952454}, "Polygon": [{"X": 0.5159347057342529, "Y": 0.8546884655952454}, {"X": 0.5849413275718689, "Y": 0.8550211191177368}, {"X": 0.5848757028579712, "Y": 0.8663886189460754}, {"X": 0.5158686637878418, "Y": 0.8660560250282288}]}, "Id": "fe72881b-bfa3-4aea-a6fe-9ec663140e74"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Scanned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09689473360776901, "Height": 0.014852718450129032, "Left": 0.5998212099075317, "Top": 0.9686533212661743}, "Polygon": [{"X": 0.5999041795730591, "Y": 0.9686533212661743}, {"X": 0.6967159509658813, "Y": 0.969118058681488}, {"X": 0.6966336965560913, "Y": 0.9835060238838196}, {"X": 0.5998212099075317, "Y": 0.9830414652824402}]}, "Id": "02b4dd5d-2531-4bc4-8dcc-fda147e979cc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0462963804602623, "Height": 0.01439471635967493, "Left": 0.7025807499885559, "Top": 0.9687324166297913}, "Polygon": [{"X": 0.7026616930961609, "Y": 0.9687324166297913}, {"X": 0.7488771080970764, "Y": 0.96895432472229}, {"X": 0.7487964630126953, "Y": 0.9831271171569824}, {"X": 0.7025807499885559, "Y": 0.982905387878418}]}, "Id": "15febafe-caef-4c5b-9fb2-5e3366e26701"}, {"BlockType": "WORD", "Confidence": 98.97819519042969, "Text": "CamScanner", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1443621963262558, "Height": 0.014967546798288822, "Left": 0.7552272081375122, "Top": 0.9687510132789612}, "Polygon": [{"X": 0.7553083896636963, "Y": 0.9687510132789612}, {"X": 0.8995894193649292, "Y": 0.969443678855896}, {"X": 0.8995092511177063, "Y": 0.9837185144424438}, {"X": 0.7552272081375122, "Y": 0.9830262064933777}]}, "Id": "06b132cc-f738-4e59-adcf-f4b610443923"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "a77650d4-1125-4fd3-a60b-46f95d3c7373", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "a77650d4-1125-4fd3-a60b-46f95d3c7373", "content-type": "application/x-amz-json-1.1", "content-length": "65823", "date": "Wed, 24 Sep 2025 13:39:02 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "585bf681-2aec-4edd-ba44-988ce87f653b", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:39:05 GMT", "content-type": "application/json", "content-length": "829", "connection": "keep-alive", "x-amzn-requestid": "585bf681-2aec-4edd-ba44-988ce87f653b"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need classify each page. Only one page provided. Determine document type. The content includes \"Ticket\", \"Scale\", \"Gross\", \"Tare\", \"Net\", \"Tons\", \"Weighmaster\". Also \"Ticket 1242095\", \"Date\", \"Time\", \"Truck\", \"Trailer\", \"Destination\", \"P.O.\", \"D.O.\", \"Seal\". Likely a Scale Ticket. It includes weight info. So doc_type = \"scale_ticket\". Ensure no other candidate. It's not invoice, not pod, etc. It's a scale ticket. So output JSON with one object page_no 1 scale_ticket."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2839, "outputTokens": 146, "totalTokens": 2985}, "metrics": {"latencyMs": 859}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/345ebd0c_6_scale_ticket_15.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n*1242095*\nCasella Recycling or New England\nA Division of Casella Waste Systems Inc\nbebunker HILL INDUSTRIAL PARK\n617-242-7746P SCALE HOUSE617-242-3649F\nTicket 1242095\nDate 10/6/2022\nTime: 17:16:09 - 18:56:34\nCustomer: KO00508/EARTHWATCH WASTE SYSTEM\nTruck: 280230\nTrailer: 19065\nDestination: A/A\nP.O.: EARTH100622-3\nD.O.: 2277043\nSeal: 519044\nComment: PARAKA CORPORATION\nGross: 75220 L Out Scale 3\nTare: 33720 L In Scale 3\nNet: 41500 L\nTons: 20.75\nMaterials & Services\nOrigin: NA/NON APPLICABLE\nMaterial: 539/OBR6-MXD COMMODITIES BALED\nQuantity. 20.75 Ton\nWeighmaster: TOM\nDriver:\nBy signing above, I declare that il did NOT\ndeposit any PROHIBITED WASTES\nScanned with CamScanner\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}