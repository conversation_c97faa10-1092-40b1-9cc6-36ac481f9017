{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9418635964393616, "Left": 0.0, "Top": 0.05338424816727638}, "Polygon": [{"X": 0.05808423459529877, "Y": 0.07546376436948776}, {"X": 0.9514235258102417, "Y": 0.05338424816727638}, {"X": 1.0, "Y": 0.9952478408813477}, {"X": 0.0, "Y": 0.9943272471427917}]}, "Id": "cc64273d-c4f3-4781-b48e-4c2417fc8219", "Relationships": [{"Type": "CHILD", "Ids": ["df320ccd-3fae-43c1-a752-58634e79e4d2", "8fd9f71e-5d5c-4d0d-b99a-d0f7435a0e22", "fb393f5f-8cb0-40a8-bf71-273b3f312e23", "660b9ad4-9246-42ac-b94a-f7cf34150f13", "95a0f0d0-a3ae-46bd-8ce7-615384b0a2e0", "0d2f0ec8-fde6-4834-86fe-c3598137727e", "d78fc680-c1c6-461a-8564-3b6affd1895b", "3c5bba0d-dd60-4c86-83fc-35f621ce969c", "c9af13a3-4ecc-4eba-812f-b6481940cf59", "f84dc9bc-e072-4e0a-b775-1aa85fd08c60", "cc8734e8-84bf-4121-8793-9213baeae257", "ec7284d2-ff86-42bd-9837-b1aac6b249fe", "9767334e-7cf7-4f5d-b9e3-34eecd6f9c91", "d115cd73-27c4-4959-adeb-c364092b4e6e", "1675ae93-4669-4513-b934-0aead5ddc8d3", "c5044652-9913-4739-b753-24c072479de9", "9bf9e815-e55c-4169-9f29-5257273abe09", "404dccb1-755a-4447-b556-6313b41a8adc", "aaa645d6-35f9-4b25-8bc5-c6a8fc210500", "dd87a145-bf88-409d-a626-bab56cc7c502", "11a852a9-b828-4541-ab59-a900a779d4ea", "ff2449af-d015-408e-b2f6-f32808ecbeaa", "5d180cc2-e0ab-41f0-b5a0-6a402052668f", "43723324-12f7-4498-9718-6eb834c36b03", "f5aefa28-3a6e-4553-ab7f-d056639b7c70", "dbe260c7-97f2-43bd-a38a-1088fabdd694", "ea2daf4c-ba44-407b-9546-23039b64fa6d", "b00ec7cc-2991-4ab8-846c-c70a7d36007d", "d58a67f9-4f32-439c-989d-9470fc58aec1", "784e4fa2-a626-49c4-9e63-5009b5609f8b", "734d1066-2153-4251-82e9-b0e634e39ac2", "f3da31bd-fd80-475e-b7b3-84e40c22272d", "8f37b1ba-b158-44d2-8cd0-9ee63c7e25b5", "e84edf5c-3104-41ce-9910-e52758dc4753"]}]}, {"BlockType": "LINE", "Confidence": 99.86597442626953, "Text": "HENRICH INDUSTRIES TRIP SHEET", "Geometry": {"BoundingBox": {"Width": 0.3390580415725708, "Height": 0.025138890370726585, "Left": 0.32658466696739197, "Top": 0.09306266903877258}, "Polygon": [{"X": 0.32759982347488403, "Y": 0.10108087211847305}, {"X": 0.6640934348106384, "Y": 0.09306266903877258}, {"X": 0.6656426787376404, "Y": 0.11028153449296951}, {"X": 0.32658466696739197, "Y": 0.11820155382156372}]}, "Id": "df320ccd-3fae-43c1-a752-58634e79e4d2", "Relationships": [{"Type": "CHILD", "Ids": ["45f73756-bcc7-451e-907e-b05ee2b3d005", "e0187316-fcd5-4035-9449-f226bcf976bb", "8ddee138-55c5-422b-8e39-a104aca50546", "df7026ee-fc53-421b-a430-94b54bbe446e"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "ND Paper", "Geometry": {"BoundingBox": {"Width": 0.07715506851673126, "Height": 0.012350318022072315, "Left": 0.4509306252002716, "Top": 0.13624128699302673}, "Polygon": [{"X": 0.45097944140434265, "Y": 0.13798750936985016}, {"X": 0.5277791619300842, "Y": 0.13624128699302673}, {"X": 0.5280857086181641, "Y": 0.14685969054698944}, {"X": 0.4509306252002716, "Y": 0.14859160780906677}]}, "Id": "8fd9f71e-5d5c-4d0d-b99a-d0f7435a0e22", "Relationships": [{"Type": "CHILD", "Ids": ["497749dd-f70a-47e4-936d-0b99d67288d5", "5d2f08e3-25c9-4cf5-bfd8-34db44512fa5"]}]}, {"BlockType": "LINE", "Confidence": 98.55388641357422, "Text": "ND", "Geometry": {"BoundingBox": {"Width": 0.027839353308081627, "Height": 0.0110611068084836, "Left": 0.7134345173835754, "Top": 0.1303151696920395}, "Polygon": [{"X": 0.7134345173835754, "Y": 0.13092002272605896}, {"X": 0.7400009632110596, "Y": 0.1303151696920395}, {"X": 0.7412738800048828, "Y": 0.14077626168727875}, {"X": 0.7145851850509644, "Y": 0.1413762867450714}]}, "Id": "fb393f5f-8cb0-40a8-bf71-273b3f312e23", "Relationships": [{"Type": "CHILD", "Ids": ["0504cec2-8ab0-4e90-a521-72ed6c3bcb9f"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "ND PAPER", "Geometry": {"BoundingBox": {"Width": 0.1399666666984558, "Height": 0.021805481985211372, "Left": 0.7602521181106567, "Top": 0.1226176843047142}, "Polygon": [{"X": 0.7602521181106567, "Y": 0.1257382482290268}, {"X": 0.896645724773407, "Y": 0.1226176843047142}, {"X": 0.9002187848091125, "Y": 0.1413467526435852}, {"X": 0.7626959681510925, "Y": 0.14442317187786102}]}, "Id": "660b9ad4-9246-42ac-b94a-f7cf34150f13", "Relationships": [{"Type": "CHILD", "Ids": ["221052e9-274f-46e3-9f82-b96cad3b29a1", "ed0343ec-c217-4b29-8aaa-d7605a71c801"]}]}, {"BlockType": "LINE", "Confidence": 98.57870483398438, "Text": "Biron Scale(Biron)", "Geometry": {"BoundingBox": {"Width": 0.1501293182373047, "Height": 0.014061768539249897, "Left": 0.41386646032333374, "Top": 0.15115049481391907}, "Polygon": [{"X": 0.4140866696834564, "Y": 0.1544841080904007}, {"X": 0.5635207891464233, "Y": 0.15115049481391907}, {"X": 0.5639957785606384, "Y": 0.16190728545188904}, {"X": 0.41386646032333374, "Y": 0.16521227359771729}]}, "Id": "95a0f0d0-a3ae-46bd-8ce7-615384b0a2e0", "Relationships": [{"Type": "CHILD", "Ids": ["14e815b5-7829-476d-aad4-a81fd7cb1a91", "27ea59eb-0207-4d47-87cd-0b31113711ee"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "78823", "Geometry": {"BoundingBox": {"Width": 0.045767538249492645, "Height": 0.00877908244729042, "Left": 0.1314745396375656, "Top": 0.17380377650260925}, "Polygon": [{"X": 0.13257449865341187, "Y": 0.17478293180465698}, {"X": 0.17724208533763885, "Y": 0.17380377650260925}, {"X": 0.17629030346870422, "Y": 0.1816100776195526}, {"X": 0.1314745396375656, "Y": 0.18258285522460938}]}, "Id": "0d2f0ec8-fde6-4834-86fe-c3598137727e", "Relationships": [{"Type": "CHILD", "Ids": ["53f1e951-98d9-47dc-9b1c-318b53c02a8c"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Source", "Geometry": {"BoundingBox": {"Width": 0.04799948260188103, "Height": 0.008133865892887115, "Left": 0.4853397011756897, "Top": 0.17155136168003082}, "Polygon": [{"X": 0.4853397011756897, "Y": 0.17259152233600616}, {"X": 0.5331210494041443, "Y": 0.17155136168003082}, {"X": 0.5333392024040222, "Y": 0.17865118384361267}, {"X": 0.4854121208190918, "Y": 0.17968522012233734}]}, "Id": "d78fc680-c1c6-461a-8564-3b6affd1895b", "Relationships": [{"Type": "CHILD", "Ids": ["85520061-14f0-4275-b992-e9dad100fed2"]}]}, {"BlockType": "LINE", "Confidence": 91.61046600341797, "Text": "GPH_WHIT_WI_COUNTRYR(GP HARMON RECYCI", "Geometry": {"BoundingBox": {"Width": 0.36084040999412537, "Height": 0.027477672323584557, "Left": 0.5749691128730774, "Top": 0.15216629207134247}, "Polygon": [{"X": 0.5749691128730774, "Y": 0.1600373536348343}, {"X": 0.9317930936813354, "Y": 0.15216629207134247}, {"X": 0.9358095526695251, "Y": 0.1718982607126236}, {"X": 0.5759283900260925, "Y": 0.17964395880699158}]}, "Id": "3c5bba0d-dd60-4c86-83fc-35f621ce969c", "Relationships": [{"Type": "CHILD", "Ids": ["c69ea236-5ea0-4738-964d-16dfae6db2f1", "3c530d6d-0298-4d73-b9a6-3a0cff6d4071", "e2d4b5c6-67f7-41b8-987c-1682f9935ea5"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Contract", "Geometry": {"BoundingBox": {"Width": 0.05886415019631386, "Height": 0.00917639210820198, "Left": 0.09048488736152649, "Top": 0.1890794336795807}, "Polygon": [{"X": 0.09173476696014404, "Y": 0.19031906127929688}, {"X": 0.14934903383255005, "Y": 0.1890794336795807}, {"X": 0.1482921987771988, "Y": 0.19702468812465668}, {"X": 0.09048488736152649, "Y": 0.19825582206249237}]}, "Id": "c9af13a3-4ecc-4eba-812f-b6481940cf59", "Relationships": [{"Type": "CHILD", "Ids": ["bd519898-cf4a-4908-a182-f7ba1e7487e5"]}]}, {"BlockType": "LINE", "Confidence": 99.8313980102539, "Text": "023594 (AMERICAN CHUNG NAM)", "Geometry": {"BoundingBox": {"Width": 0.22770297527313232, "Height": 0.01707966811954975, "Left": 0.19301408529281616, "Top": 0.1838645190000534}, "Polygon": [{"X": 0.1944045126438141, "Y": 0.18872997164726257}, {"X": 0.4207170605659485, "Y": 0.1838645190000534}, {"X": 0.4205031991004944, "Y": 0.19612988829612732}, {"X": 0.19301408529281616, "Y": 0.200944185256958}]}, "Id": "f84dc9bc-e072-4e0a-b775-1aa85fd08c60", "Relationships": [{"Type": "CHILD", "Ids": ["552e2b8c-2f9b-4351-88e0-7ce075f028f1", "e4469e63-82cf-459b-a24e-c71588e52b10", "f746ab6f-e579-4dfb-bcb2-4b968ad6e2bc", "103b0fd6-64f7-4be4-b354-2b7a61b00f8a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Destination", "Geometry": {"BoundingBox": {"Width": 0.0788196250796318, "Height": 0.009892363101243973, "Left": 0.4848133623600006, "Top": 0.18287056684494019}, "Polygon": [{"X": 0.4848133623600006, "Y": 0.18455298244953156}, {"X": 0.5632756948471069, "Y": 0.18287056684494019}, {"X": 0.5636329650878906, "Y": 0.19109229743480682}, {"X": 0.48489490151405334, "Y": 0.19276292622089386}]}, "Id": "cc8734e8-84bf-4121-8793-9213baeae257", "Relationships": [{"Type": "CHILD", "Ids": ["e5cfd069-7ad8-4b7a-87b0-7af71522ff88"]}]}, {"BlockType": "LINE", "Confidence": 96.2989273071289, "Text": "BIRON(BIRON MILL)", "Geometry": {"BoundingBox": {"Width": 0.138777494430542, "Height": 0.013775968924164772, "Left": 0.5767961740493774, "Top": 0.17835348844528198}, "Polygon": [{"X": 0.5767961740493774, "Y": 0.1813087910413742}, {"X": 0.7143996953964233, "Y": 0.17835348844528198}, {"X": 0.7155736684799194, "Y": 0.1892012506723404}, {"X": 0.5773292183876038, "Y": 0.192129448056221}]}, "Id": "ec7284d2-ff86-42bd-9837-b1aac6b249fe", "Relationships": [{"Type": "CHILD", "Ids": ["97f926a3-6d82-4415-b3b0-1c0869f57e0b", "d6098680-b167-4184-8a7e-56ca8d142eff"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.04323795065283775, "Height": 0.009017092175781727, "Left": 0.4839390516281128, "Top": 0.20876893401145935}, "Polygon": [{"X": 0.4839390516281128, "Y": 0.20966170728206635}, {"X": 0.52695232629776, "Y": 0.20876893401145935}, {"X": 0.5271769762039185, "Y": 0.2168997824192047}, {"X": 0.4840158522129059, "Y": 0.2177860289812088}]}, "Id": "9767334e-7cf7-4f5d-b9e3-34eecd6f9c91", "Relationships": [{"Type": "CHILD", "Ids": ["9c73d4ae-6617-4d0a-a7f5-6f16b8962416"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "RORY", "Geometry": {"BoundingBox": {"Width": 0.04295200854539871, "Height": 0.009025379084050655, "Left": 0.5770378708839417, "Top": 0.20735515654087067}, "Polygon": [{"X": 0.5770378708839417, "Y": 0.2082347869873047}, {"X": 0.6194456815719604, "Y": 0.20735515654087067}, {"X": 0.6199898719787598, "Y": 0.21550734341144562}, {"X": 0.5774354338645935, "Y": 0.2163805365562439}]}, "Id": "d115cd73-27c4-4959-adeb-c364092b4e6e", "Relationships": [{"Type": "CHILD", "Ids": ["22c5c501-90c8-43c1-af78-3d86c93d6ed8"]}]}, {"BlockType": "LINE", "Confidence": 99.9206771850586, "Text": "Trailer 5310220", "Geometry": {"BoundingBox": {"Width": 0.13299870491027832, "Height": 0.014222174882888794, "Left": 0.7814507484436035, "Top": 0.19542351365089417}, "Polygon": [{"X": 0.7814507484436035, "Y": 0.1981571465730667}, {"X": 0.9122440814971924, "Y": 0.19542351365089417}, {"X": 0.9144494533538818, "Y": 0.20693965256214142}, {"X": 0.7830105423927307, "Y": 0.20964568853378296}]}, "Id": "1675ae93-4669-4513-b934-0aead5ddc8d3", "Relationships": [{"Type": "CHILD", "Ids": ["6dfd59c6-b10a-4015-b8a1-345caac9360e", "5b57bb02-ee79-4622-aaee-26230f06c06a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "BOL", "Geometry": {"BoundingBox": {"Width": 0.03112552873790264, "Height": 0.00875944271683693, "Left": 0.08126364648342133, "Top": 0.22898171842098236}, "Polygon": [{"X": 0.08255729079246521, "Y": 0.22959133982658386}, {"X": 0.11238917708396912, "Y": 0.22898171842098236}, {"X": 0.111196368932724, "Y": 0.23713622987270355}, {"X": 0.08126364648342133, "Y": 0.23774117231369019}]}, "Id": "c5044652-9913-4739-b753-24c072479de9", "Relationships": [{"Type": "CHILD", "Ids": ["df358a31-ce45-4519-af04-d64cc21fa69c"]}]}, {"BlockType": "LINE", "Confidence": 99.94412994384766, "Text": "Trip Ticket ********", "Geometry": {"BoundingBox": {"Width": 0.15954364836215973, "Height": 0.013654287904500961, "Left": 0.07699957489967346, "Top": 0.2409137785434723}, "Polygon": [{"X": 0.07867179065942764, "Y": 0.2440769374370575}, {"X": 0.2365432232618332, "Y": 0.2409137785434723}, {"X": 0.2355562001466751, "Y": 0.25143706798553467}, {"X": 0.07699957489967346, "Y": 0.25456807017326355}]}, "Id": "9bf9e815-e55c-4169-9f29-5257273abe09", "Relationships": [{"Type": "CHILD", "Ids": ["794afdaa-9794-4018-b8ac-f5fb14dd5fe2", "035f3273-5757-4301-9a8d-5aa2e3f74154", "21b90c78-d69c-4849-81df-f01d48700aae"]}]}, {"BlockType": "LINE", "Confidence": 99.32079315185547, "Text": "Product OCC 11D", "Geometry": {"BoundingBox": {"Width": 0.15054774284362793, "Height": 0.012358397245407104, "Left": 0.4806426763534546, "Top": 0.2356569468975067}, "Polygon": [{"X": 0.4806426763534546, "Y": 0.23864930868148804}, {"X": 0.6305273771286011, "Y": 0.2356569468975067}, {"X": 0.6311904191970825, "Y": 0.24504980444908142}, {"X": 0.480717271566391, "Y": 0.24801534414291382}]}, "Id": "404dccb1-755a-4447-b556-6313b41a8adc", "Relationships": [{"Type": "CHILD", "Ids": ["d77b370e-bd07-42d0-acae-00c73d7a904e", "613cfcf4-2c94-4cc6-a07a-c1bccc6db40f", "b2266719-77c2-4e41-9e39-036d2acdfbcd"]}]}, {"BlockType": "LINE", "Confidence": 98.6962890625, "Text": "Load #", "Geometry": {"BoundingBox": {"Width": 0.05354659631848335, "Height": 0.01036371011286974, "Left": 0.07465773075819016, "Top": 0.2586604952812195}, "Polygon": [{"X": 0.07614710181951523, "Y": 0.2596811354160309}, {"X": 0.1282043308019638, "Y": 0.2586604952812195}, {"X": 0.12691426277160645, "Y": 0.2680131494998932}, {"X": 0.07465773075819016, "Y": 0.26902422308921814}]}, "Id": "aaa645d6-35f9-4b25-8bc5-c6a8fc210500", "Relationships": [{"Type": "CHILD", "Ids": ["02dc3c03-8653-4542-b0d3-1df79fede265", "73fc3c28-1d68-4c95-a21f-b1160c268477"]}]}, {"BlockType": "LINE", "Confidence": 99.69567108154297, "Text": "BIR-78823", "Geometry": {"BoundingBox": {"Width": 0.07914793491363525, "Height": 0.010941104032099247, "Left": 0.16557471454143524, "Top": 0.25794991850852966}, "Polygon": [{"X": 0.16672295331954956, "Y": 0.25947579741477966}, {"X": 0.2447226494550705, "Y": 0.25794991850852966}, {"X": 0.2438763529062271, "Y": 0.2673795223236084}, {"X": 0.16557471454143524, "Y": 0.26889100670814514}]}, "Id": "dd87a145-bf88-409d-a626-bab56cc7c502", "Relationships": [{"Type": "CHILD", "Ids": ["daa7d682-8695-456b-99fa-3b85d9233e43"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Tons", "Geometry": {"BoundingBox": {"Width": 0.03607175499200821, "Height": 0.009114332497119904, "Left": 0.17663787305355072, "Top": 0.34122732281684875}, "Polygon": [{"X": 0.17760421335697174, "Y": 0.34183409810066223}, {"X": 0.21270963549613953, "Y": 0.34122732281684875}, {"X": 0.21186195313930511, "Y": 0.3497411012649536}, {"X": 0.17663787305355072, "Y": 0.35034167766571045}]}, "Id": "11a852a9-b828-4541-ab59-a900a779d4ea", "Relationships": [{"Type": "CHILD", "Ids": ["3ef99317-06ad-468d-bf71-7bc72cde3448"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "LB", "Geometry": {"BoundingBox": {"Width": 0.019689010456204414, "Height": 0.008601011708378792, "Left": 0.268192321062088, "Top": 0.3401064872741699}, "Polygon": [{"X": 0.26883038878440857, "Y": 0.340435653924942}, {"X": 0.28788134455680847, "Y": 0.3401064872741699}, {"X": 0.2873060703277588, "Y": 0.34838157892227173}, {"X": 0.268192321062088, "Y": 0.34870749711990356}]}, "Id": "ff2449af-d015-408e-b2f6-f32808ecbeaa", "Relationships": [{"Type": "CHILD", "Ids": ["c1d9872f-6c1f-418e-97a6-9c6554981eee"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Gross", "Geometry": {"BoundingBox": {"Width": 0.04551136866211891, "Height": 0.010337567888200283, "Left": 0.05348406359553337, "Top": 0.35572683811187744}, "Polygon": [{"X": 0.055034950375556946, "Y": 0.3564714193344116}, {"X": 0.09899543225765228, "Y": 0.35572683811187744}, {"X": 0.09761057049036026, "Y": 0.36532869935035706}, {"X": 0.05348406359553337, "Y": 0.3660644292831421}]}, "Id": "5d180cc2-e0ab-41f0-b5a0-6a402052668f", "Relationships": [{"Type": "CHILD", "Ids": ["a50e6289-f765-4547-a9f6-f460a206fdc3"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "37.64", "Geometry": {"BoundingBox": {"Width": 0.03860550373792648, "Height": 0.0098249027505517, "Left": 0.1714421957731247, "Top": 0.35520851612091064}, "Polygon": [{"X": 0.1724989265203476, "Y": 0.35584309697151184}, {"X": 0.21004769206047058, "Y": 0.35520851612091064}, {"X": 0.209127277135849, "Y": 0.3644060790538788}, {"X": 0.1714421957731247, "Y": 0.36503341794013977}]}, "Id": "43723324-12f7-4498-9718-6eb834c36b03", "Relationships": [{"Type": "CHILD", "Ids": ["7241f374-9daa-4820-adeb-ea8455cda906"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "75,280", "Geometry": {"BoundingBox": {"Width": 0.04691743478178978, "Height": 0.01099210511893034, "Left": 0.24149785935878754, "Top": 0.35347801446914673}, "Polygon": [{"X": 0.24238887429237366, "Y": 0.35425639152526855}, {"X": 0.2884152829647064, "Y": 0.35347801446914673}, {"X": 0.2877105176448822, "Y": 0.36370161175727844}, {"X": 0.24149785935878754, "Y": 0.36447012424468994}]}, "Id": "f5aefa28-3a6e-4553-ab7f-d056639b7c70", "Relationships": [{"Type": "CHILD", "Ids": ["36b51355-df96-4400-ba60-8cff5ce28927"]}]}, {"BlockType": "LINE", "Confidence": 99.08561706542969, "Text": "Date In", "Geometry": {"BoundingBox": {"Width": 0.050652071833610535, "Height": 0.01046175416558981, "Left": 0.31493109464645386, "Top": 0.352691113948822}, "Polygon": [{"X": 0.3154902756214142, "Y": 0.35353752970695496}, {"X": 0.3655831515789032, "Y": 0.352691113948822}, {"X": 0.3652152419090271, "Y": 0.3623164892196655}, {"X": 0.31493109464645386, "Y": 0.3631528615951538}]}, "Id": "dbe260c7-97f2-43bd-a38a-1088fabdd694", "Relationships": [{"Type": "CHILD", "Ids": ["df7c32ad-1e8d-443c-97a3-0540bdd33720", "4573f63e-43e8-41c4-af7b-cf27fbaba19a"]}]}, {"BlockType": "LINE", "Confidence": 99.814453125, "Text": "9/16/25 16:15:12", "Geometry": {"BoundingBox": {"Width": 0.1169465035200119, "Height": 0.011654216796159744, "Left": 0.38996422290802, "Top": 0.3505082130432129}, "Polygon": [{"X": 0.3902393579483032, "Y": 0.35247600078582764}, {"X": 0.5067358016967773, "Y": 0.3505082130432129}, {"X": 0.5069107413291931, "Y": 0.3602180778980255}, {"X": 0.38996422290802, "Y": 0.36216241121292114}]}, "Id": "ea2daf4c-ba44-407b-9546-23039b64fa6d", "Relationships": [{"Type": "CHILD", "Ids": ["41eabf3e-ddd6-49e6-9e00-c77568ce5217", "99f3f2e6-32b2-40a3-b30d-5acf8b82e2b2"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.034239061176776886, "Height": 0.010116534307599068, "Left": 0.050253674387931824, "Top": 0.3695423901081085}, "Polygon": [{"X": 0.05180557444691658, "Y": 0.370083749294281}, {"X": 0.08449273556470871, "Y": 0.3695423901081085}, {"X": 0.08306333422660828, "Y": 0.3791241943836212}, {"X": 0.050253674387931824, "Y": 0.37965890765190125}]}, "Id": "b00ec7cc-2991-4ab8-846c-c70a7d36007d", "Relationships": [{"Type": "CHILD", "Ids": ["006ec732-0be6-480e-8d7f-b5d3ffe56a51"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "15.95", "Geometry": {"BoundingBox": {"Width": 0.037199150770902634, "Height": 0.009656614623963833, "Left": 0.1705176830291748, "Top": 0.3689396381378174}, "Polygon": [{"X": 0.17155706882476807, "Y": 0.3695370852947235}, {"X": 0.20771683752536774, "Y": 0.3689396381378174}, {"X": 0.20680613815784454, "Y": 0.3780057430267334}, {"X": 0.1705176830291748, "Y": 0.3785962760448456}]}, "Id": "d58a67f9-4f32-439c-989d-9470fc58aec1", "Relationships": [{"Type": "CHILD", "Ids": ["e45d299d-82dd-4631-a361-d29d5aa75070"]}]}, {"BlockType": "LINE", "Confidence": 99.4834213256836, "Text": "31,900", "Geometry": {"BoundingBox": {"Width": 0.04729463532567024, "Height": 0.0107685886323452, "Left": 0.2395012080669403, "Top": 0.3671674430370331}, "Polygon": [{"X": 0.24037690460681915, "Y": 0.3679350018501282}, {"X": 0.28679585456848145, "Y": 0.3671674430370331}, {"X": 0.2861030399799347, "Y": 0.37717828154563904}, {"X": 0.2395012080669403, "Y": 0.3779360353946686}]}, "Id": "784e4fa2-a626-49c4-9e63-5009b5609f8b", "Relationships": [{"Type": "CHILD", "Ids": ["dfadfbf9-6494-4f17-8381-612869c96bd6"]}]}, {"BlockType": "LINE", "Confidence": 99.90478515625, "Text": "Date Out 9/16/25 16:58:06", "Geometry": {"BoundingBox": {"Width": 0.19344639778137207, "Height": 0.013352555222809315, "Left": 0.3134426176548004, "Top": 0.3648749887943268}, "Polygon": [{"X": 0.3140367269515991, "Y": 0.36805376410484314}, {"X": 0.506706178188324, "Y": 0.3648749887943268}, {"X": 0.5068889856338501, "Y": 0.37509000301361084}, {"X": 0.3134426176548004, "Y": 0.3782275319099426}]}, "Id": "734d1066-2153-4251-82e9-b0e634e39ac2", "Relationships": [{"Type": "CHILD", "Ids": ["36fc18cf-b5f1-48fc-8ebe-27c17a9009eb", "97ef7ff8-8630-4b11-a8e5-eae828c861f8", "fea6f139-d390-4038-995c-c9b7a659a8a0", "0051d1cb-ab3c-4f26-bc00-18890b11d89d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Net", "Geometry": {"BoundingBox": {"Width": 0.026731213554739952, "Height": 0.009963869117200375, "Left": 0.04925573989748955, "Top": 0.3842046558856964}, "Polygon": [{"X": 0.05079960078001022, "Y": 0.38461169600486755}, {"X": 0.07598695158958435, "Y": 0.3842046558856964}, {"X": 0.0745367482304573, "Y": 0.3937666714191437}, {"X": 0.04925573989748955, "Y": 0.39416852593421936}]}, "Id": "f3da31bd-fd80-475e-b7b3-84e40c22272d", "Relationships": [{"Type": "CHILD", "Ids": ["7bd7de8d-4494-4cc0-913d-f284cbecf6a3"]}]}, {"BlockType": "LINE", "Confidence": 99.56233978271484, "Text": "21.69", "Geometry": {"BoundingBox": {"Width": 0.039131466299295425, "Height": 0.009938446804881096, "Left": 0.16661952435970306, "Top": 0.3831329047679901}, "Polygon": [{"X": 0.16769753396511078, "Y": 0.38374677300453186}, {"X": 0.20575100183486938, "Y": 0.3831329047679901}, {"X": 0.20481160283088684, "Y": 0.3924650251865387}, {"X": 0.16661952435970306, "Y": 0.39307132363319397}]}, "Id": "8f37b1ba-b158-44d2-8cd0-9ee63c7e25b5", "Relationships": [{"Type": "CHILD", "Ids": ["de2c4921-cc86-477c-8744-17e590bbfb46"]}]}, {"BlockType": "LINE", "Confidence": 99.37281036376953, "Text": "43,380", "Geometry": {"BoundingBox": {"Width": 0.04820170998573303, "Height": 0.010992868803441525, "Left": 0.23832827806472778, "Top": 0.3817949891090393}, "Polygon": [{"X": 0.23922348022460938, "Y": 0.3825581669807434}, {"X": 0.2865299880504608, "Y": 0.3817949891090393}, {"X": 0.28582435846328735, "Y": 0.39203494787216187}, {"X": 0.23832827806472778, "Y": 0.3927878439426422}]}, "Id": "e84edf5c-3104-41ce-9910-e52758dc4753", "Relationships": [{"Type": "CHILD", "Ids": ["f56e05f2-2694-47a8-b816-5a23615a4a35"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "HENRICH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08122843503952026, "Height": 0.011787164956331253, "Left": 0.32658466696739197, "Top": 0.10641439259052277}, "Polygon": [{"X": 0.32717058062553406, "Y": 0.1083199754357338}, {"X": 0.40781310200691223, "Y": 0.10641439259052277}, {"X": 0.4075782597064972, "Y": 0.11630963534116745}, {"X": 0.32658466696739197, "Y": 0.11820155382156372}]}, "Id": "45f73756-bcc7-451e-907e-b05ee2b3d005"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "INDUSTRIES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1194230243563652, "Height": 0.014268052764236927, "Left": 0.41904523968696594, "Top": 0.09904450923204422}, "Polygon": [{"X": 0.4192594289779663, "Y": 0.10186616331338882}, {"X": 0.5380793809890747, "Y": 0.09904450923204422}, {"X": 0.5384682416915894, "Y": 0.11051403731107712}, {"X": 0.41904523968696594, "Y": 0.11331256479024887}]}, "Id": "e0187316-fcd5-4035-9449-f226bcf976bb"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044426608830690384, "Height": 0.009473908692598343, "Left": 0.549630880355835, "Top": 0.09665568172931671}, "Polygon": [{"X": 0.549630880355835, "Y": 0.09770024567842484}, {"X": 0.5935636162757874, "Y": 0.09665568172931671}, {"X": 0.5940575003623962, "Y": 0.10509126633405685}, {"X": 0.5499600172042847, "Y": 0.10612958669662476}]}, "Id": "8ddee138-55c5-422b-8e39-a104aca50546"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SHEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059548601508140564, "Height": 0.010189197026193142, "Left": 0.6053364276885986, "Top": 0.09306266903877258}, "Polygon": [{"X": 0.6053364276885986, "Y": 0.09446276724338531}, {"X": 0.6640934348106384, "Y": 0.09306266903877258}, {"X": 0.6648849844932556, "Y": 0.1018604263663292}, {"X": 0.6058974266052246, "Y": 0.1032518595457077}]}, "Id": "df7026ee-fc53-421b-a430-94b54bbe446e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02381204627454281, "Height": 0.00955931656062603, "Left": 0.4509379267692566, "Top": 0.13744820654392242}, "Polygon": [{"X": 0.45097944140434265, "Y": 0.13798750936985016}, {"X": 0.4746982753276825, "Y": 0.13744820654392242}, {"X": 0.47474995255470276, "Y": 0.14647197723388672}, {"X": 0.4509379267692566, "Y": 0.1470075249671936}]}, "Id": "497749dd-f70a-47e4-936d-0b99d67288d5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Paper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048068683594465256, "Height": 0.011576777324080467, "Left": 0.4800170063972473, "Top": 0.1363600194454193}, "Polygon": [{"X": 0.4800170063972473, "Y": 0.13744594156742096}, {"X": 0.5277826189994812, "Y": 0.1363600194454193}, {"X": 0.5280857086181641, "Y": 0.14685969054698944}, {"X": 0.48010140657424927, "Y": 0.14793680608272552}]}, "Id": "5d2f08e3-25c9-4cf5-bfd8-34db44512fa5"}, {"BlockType": "WORD", "Confidence": 98.55388641357422, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027839353308081627, "Height": 0.0110611068084836, "Left": 0.7134345173835754, "Top": 0.1303151696920395}, "Polygon": [{"X": 0.7134345173835754, "Y": 0.13092002272605896}, {"X": 0.7400009632110596, "Y": 0.1303151696920395}, {"X": 0.7412738800048828, "Y": 0.14077626168727875}, {"X": 0.7145851850509644, "Y": 0.1413762867450714}]}, "Id": "0504cec2-8ab0-4e90-a521-72ed6c3bcb9f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04423600807785988, "Height": 0.01742425002157688, "Left": 0.7605416774749756, "Top": 0.1269989162683487}, "Polygon": [{"X": 0.7605416774749756, "Y": 0.12795224785804749}, {"X": 0.8023195862770081, "Y": 0.1269989162683487}, {"X": 0.8047776818275452, "Y": 0.1434817910194397}, {"X": 0.7626959681510925, "Y": 0.14442317187786102}]}, "Id": "221052e9-274f-46e3-9f82-b96cad3b29a1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PAPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09006065130233765, "Height": 0.019286254420876503, "Left": 0.8098906874656677, "Top": 0.1226176843047142}, "Polygon": [{"X": 0.8098906874656677, "Y": 0.12460256367921829}, {"X": 0.896645724773407, "Y": 0.1226176843047142}, {"X": 0.8999513387680054, "Y": 0.13994500041007996}, {"X": 0.8125309944152832, "Y": 0.14190393686294556}]}, "Id": "ed0343ec-c217-4b29-8aaa-d7605a71c801"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Biron", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0429370142519474, "Height": 0.009631750173866749, "Left": 0.4138976037502289, "Top": 0.15406298637390137}, "Polygon": [{"X": 0.41407573223114014, "Y": 0.1550162434577942}, {"X": 0.4568346440792084, "Y": 0.15406298637390137}, {"X": 0.4568169116973877, "Y": 0.16274811327457428}, {"X": 0.4138976037502289, "Y": 0.16369473934173584}]}, "Id": "14e815b5-7829-476d-aad4-a81fd7cb1a91"}, {"BlockType": "WORD", "Confidence": 97.18669891357422, "Text": "Scale(Biron)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10156701505184174, "Height": 0.01299261674284935, "Left": 0.4624287784099579, "Top": 0.15115049481391907}, "Polygon": [{"X": 0.4624287784099579, "Y": 0.15340568125247955}, {"X": 0.5635207891464233, "Y": 0.15115049481391907}, {"X": 0.5639957785606384, "Y": 0.16190728545188904}, {"X": 0.462432861328125, "Y": 0.16414311528205872}]}, "Id": "27ea59eb-0207-4d47-87cd-0b31113711ee"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "78823", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045767538249492645, "Height": 0.00877908244729042, "Left": 0.1314745396375656, "Top": 0.17380377650260925}, "Polygon": [{"X": 0.13257449865341187, "Y": 0.17478293180465698}, {"X": 0.17724208533763885, "Y": 0.17380377650260925}, {"X": 0.17629030346870422, "Y": 0.1816100776195526}, {"X": 0.1314745396375656, "Y": 0.18258285522460938}]}, "Id": "53f1e951-98d9-47dc-9b1c-318b53c02a8c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Source", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04799948260188103, "Height": 0.008133865892887115, "Left": 0.4853397011756897, "Top": 0.17155136168003082}, "Polygon": [{"X": 0.4853397011756897, "Y": 0.17259152233600616}, {"X": 0.5331210494041443, "Y": 0.17155136168003082}, {"X": 0.5333392024040222, "Y": 0.17865118384361267}, {"X": 0.4854121208190918, "Y": 0.17968522012233734}]}, "Id": "85520061-14f0-4275-b992-e9dad100fed2"}, {"BlockType": "WORD", "Confidence": 78.27566528320312, "Text": "GPH_WHIT_WI_COUNTRYR(GP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22732295095920563, "Height": 0.019844768568873405, "Left": 0.5751990079879761, "Top": 0.15979918837547302}, "Polygon": [{"X": 0.5751990079879761, "Y": 0.16473646461963654}, {"X": 0.8003342151641846, "Y": 0.15979918837547302}, {"X": 0.8025219440460205, "Y": 0.17476700246334076}, {"X": 0.5759283900260925, "Y": 0.17964395880699158}]}, "Id": "c69ea236-5ea0-4738-964d-16dfae6db2f1"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "HARMON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06846670806407928, "Height": 0.01100014615803957, "Left": 0.8032466769218445, "Top": 0.15652333199977875}, "Polygon": [{"X": 0.8032466769218445, "Y": 0.15799105167388916}, {"X": 0.870029091835022, "Y": 0.15652333199977875}, {"X": 0.871713399887085, "Y": 0.16606704890727997}, {"X": 0.8046530485153198, "Y": 0.16752347350120544}]}, "Id": "3c530d6d-0298-4d73-b9a6-3a0cff6d4071"}, {"BlockType": "WORD", "Confidence": 96.70220947265625, "Text": "RECYCI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05945790186524391, "Height": 0.012019406072795391, "Left": 0.8745267987251282, "Top": 0.15216629207134247}, "Polygon": [{"X": 0.8745267987251282, "Y": 0.15342950820922852}, {"X": 0.9317930936813354, "Y": 0.15216629207134247}, {"X": 0.9339846968650818, "Y": 0.162933349609375}, {"X": 0.8764485716819763, "Y": 0.16418570280075073}]}, "Id": "e2d4b5c6-67f7-41b8-987c-1682f9935ea5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Contract", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05886415019631386, "Height": 0.00917639210820198, "Left": 0.09048488736152649, "Top": 0.1890794336795807}, "Polygon": [{"X": 0.09173476696014404, "Y": 0.19031906127929688}, {"X": 0.14934903383255005, "Y": 0.1890794336795807}, {"X": 0.1482921987771988, "Y": 0.19702468812465668}, {"X": 0.09048488736152649, "Y": 0.19825582206249237}]}, "Id": "bd519898-cf4a-4908-a182-f7ba1e7487e5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "023594", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04791136831045151, "Height": 0.009156856685876846, "Left": 0.1934771090745926, "Top": 0.18771986663341522}, "Polygon": [{"X": 0.1944045126438141, "Y": 0.18872997164726257}, {"X": 0.2413884699344635, "Y": 0.18771986663341522}, {"X": 0.24062319099903107, "Y": 0.1958737075328827}, {"X": 0.1934771090745926, "Y": 0.19687673449516296}]}, "Id": "552e2b8c-2f9b-4351-88e0-7ce075f028f1"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "(AMERICAN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07986485958099365, "Height": 0.01099953893572092, "Left": 0.24511665105819702, "Top": 0.186972513794899}, "Polygon": [{"X": 0.24597129225730896, "Y": 0.1886688619852066}, {"X": 0.3249815106391907, "Y": 0.186972513794899}, {"X": 0.3244388997554779, "Y": 0.1962892860174179}, {"X": 0.24511665105819702, "Y": 0.19797204434871674}]}, "Id": "e4469e63-82cf-459b-a24e-c71588e52b10"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "CHUNG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051739297807216644, "Height": 0.009441991336643696, "Left": 0.32904690504074097, "Top": 0.18684378266334534}, "Polygon": [{"X": 0.3295164704322815, "Y": 0.18794304132461548}, {"X": 0.3807862102985382, "Y": 0.18684378266334534}, {"X": 0.38049858808517456, "Y": 0.19519439339637756}, {"X": 0.32904690504074097, "Y": 0.19628576934337616}]}, "Id": "f746ab6f-e579-4dfb-bcb2-4b968ad6e2bc"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "NAM)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035723112523555756, "Height": 0.01026838831603527, "Left": 0.38494598865509033, "Top": 0.18661397695541382}, "Polygon": [{"X": 0.385255366563797, "Y": 0.18737266957759857}, {"X": 0.4206691086292267, "Y": 0.18661397695541382}, {"X": 0.4205031991004944, "Y": 0.19612988829612732}, {"X": 0.38494598865509033, "Y": 0.19688236713409424}]}, "Id": "103b0fd6-64f7-4be4-b354-2b7a61b00f8a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Destination", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0788196250796318, "Height": 0.009892363101243973, "Left": 0.4848133623600006, "Top": 0.18287056684494019}, "Polygon": [{"X": 0.4848133623600006, "Y": 0.18455298244953156}, {"X": 0.5632756948471069, "Y": 0.18287056684494019}, {"X": 0.5636329650878906, "Y": 0.19109229743480682}, {"X": 0.48489490151405334, "Y": 0.19276292622089386}]}, "Id": "e5cfd069-7ad8-4b7a-87b0-7af71522ff88"}, {"BlockType": "WORD", "Confidence": 97.18112182617188, "Text": "BIRON(BIRON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09678377211093903, "Height": 0.012044325470924377, "Left": 0.5768371820449829, "Top": 0.18008512258529663}, "Polygon": [{"X": 0.5768371820449829, "Y": 0.18214216828346252}, {"X": 0.6727173924446106, "Y": 0.18008512258529663}, {"X": 0.6736209988594055, "Y": 0.1900898665189743}, {"X": 0.5773292183876038, "Y": 0.192129448056221}]}, "Id": "97f926a3-6d82-4415-b3b0-1c0869f57e0b"}, {"BlockType": "WORD", "Confidence": 95.41673278808594, "Text": "MILL)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03816225752234459, "Height": 0.01058058999478817, "Left": 0.6772969365119934, "Top": 0.17835348844528198}, "Polygon": [{"X": 0.6772969365119934, "Y": 0.17915034294128418}, {"X": 0.7143996953964233, "Y": 0.17835348844528198}, {"X": 0.7154592275619507, "Y": 0.1881438046693802}, {"X": 0.6782000660896301, "Y": 0.1889340728521347}]}, "Id": "d6098680-b167-4184-8a7e-56ca8d142eff"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04323795065283775, "Height": 0.009017092175781727, "Left": 0.4839390516281128, "Top": 0.20876893401145935}, "Polygon": [{"X": 0.4839390516281128, "Y": 0.20966170728206635}, {"X": 0.52695232629776, "Y": 0.20876893401145935}, {"X": 0.5271769762039185, "Y": 0.2168997824192047}, {"X": 0.4840158522129059, "Y": 0.2177860289812088}]}, "Id": "9c73d4ae-6617-4d0a-a7f5-6f16b8962416"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RORY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04295200854539871, "Height": 0.009025379084050655, "Left": 0.5770378708839417, "Top": 0.20735515654087067}, "Polygon": [{"X": 0.5770378708839417, "Y": 0.2082347869873047}, {"X": 0.6194456815719604, "Y": 0.20735515654087067}, {"X": 0.6199898719787598, "Y": 0.21550734341144562}, {"X": 0.5774354338645935, "Y": 0.2163805365562439}]}, "Id": "22c5c501-90c8-43c1-af78-3d86c93d6ed8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049480143934488297, "Height": 0.009676910936832428, "Left": 0.7818328142166138, "Top": 0.19996877014636993}, "Polygon": [{"X": 0.7818328142166138, "Y": 0.20097088813781738}, {"X": 0.8299564123153687, "Y": 0.19996877014636993}, {"X": 0.8313129544258118, "Y": 0.20865124464035034}, {"X": 0.7830105423927307, "Y": 0.20964568853378296}]}, "Id": "6dfd59c6-b10a-4015-b8a1-345caac9360e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "5310220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06214600056409836, "Height": 0.011169523000717163, "Left": 0.8519980311393738, "Top": 0.19542351365089417}, "Polygon": [{"X": 0.8519980311393738, "Y": 0.19668267667293549}, {"X": 0.9122440814971924, "Y": 0.19542351365089417}, {"X": 0.9141440391540527, "Y": 0.2053447961807251}, {"X": 0.8536413311958313, "Y": 0.20659303665161133}]}, "Id": "5b57bb02-ee79-4622-aaee-26230f06c06a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BOL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03112552873790264, "Height": 0.00875944271683693, "Left": 0.08126364648342133, "Top": 0.22898171842098236}, "Polygon": [{"X": 0.08255729079246521, "Y": 0.22959133982658386}, {"X": 0.11238917708396912, "Y": 0.22898171842098236}, {"X": 0.111196368932724, "Y": 0.23713622987270355}, {"X": 0.08126364648342133, "Y": 0.23774117231369019}]}, "Id": "df358a31-ce45-4519-af04-d64cc21fa69c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029338888823986053, "Height": 0.010949439369142056, "Left": 0.07701493054628372, "Top": 0.2435223013162613}, "Polygon": [{"X": 0.07867179065942764, "Y": 0.2440769374370575}, {"X": 0.10635381937026978, "Y": 0.2435223013162613}, {"X": 0.10481556504964828, "Y": 0.2539226710796356}, {"X": 0.07701493054628372, "Y": 0.2544717490673065}]}, "Id": "794afdaa-9794-4018-b8ac-f5fb14dd5fe2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04401356726884842, "Height": 0.00967529695481062, "Left": 0.10917875915765762, "Top": 0.2430010586977005}, "Polygon": [{"X": 0.11046794056892395, "Y": 0.24385660886764526}, {"X": 0.15319232642650604, "Y": 0.2430010586977005}, {"X": 0.1520586907863617, "Y": 0.2518281042575836}, {"X": 0.10917875915765762, "Y": 0.2526763379573822}]}, "Id": "035f3273-5757-4301-9a8d-5aa2e3f74154"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06855281442403793, "Height": 0.01071251556277275, "Left": 0.16788281500339508, "Top": 0.24206088483333588}, "Polygon": [{"X": 0.16902346909046173, "Y": 0.24340945482254028}, {"X": 0.23643562197685242, "Y": 0.24206088483333588}, {"X": 0.2355562001466751, "Y": 0.25143706798553467}, {"X": 0.16788281500339508, "Y": 0.2527734041213989}]}, "Id": "21b90c78-d69c-4849-81df-f01d48700aae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05707243084907532, "Height": 0.010016974061727524, "Left": 0.4806426763534546, "Top": 0.23751550912857056}, "Polygon": [{"X": 0.4806426763534546, "Y": 0.23864930868148804}, {"X": 0.537433385848999, "Y": 0.23751550912857056}, {"X": 0.5377150774002075, "Y": 0.2464083433151245}, {"X": 0.480713427066803, "Y": 0.24753248691558838}]}, "Id": "d77b370e-bd07-42d0-acae-00c73d7a904e"}, {"BlockType": "WORD", "Confidence": 99.35965728759766, "Text": "OCC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033722832798957825, "Height": 0.009184643626213074, "Left": 0.566024899482727, "Top": 0.23714213073253632}, "Polygon": [{"X": 0.566024899482727, "Y": 0.2378048151731491}, {"X": 0.5992574691772461, "Y": 0.23714213073253632}, {"X": 0.5997477769851685, "Y": 0.24566948413848877}, {"X": 0.5663965344429016, "Y": 0.2463267743587494}]}, "Id": "613cfcf4-2c94-4cc6-a07a-c1bccc6db40f"}, {"BlockType": "WORD", "Confidence": 98.60271453857422, "Text": "11D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02689911611378193, "Height": 0.008760257624089718, "Left": 0.6042801737785339, "Top": 0.23665286600589752}, "Polygon": [{"X": 0.6042801737785339, "Y": 0.23717756569385529}, {"X": 0.6305976510047913, "Y": 0.23665286600589752}, {"X": 0.6311793327331543, "Y": 0.24489255249500275}, {"X": 0.6047709584236145, "Y": 0.24541312456130981}]}, "Id": "b2266719-77c2-4e41-9e39-036d2acdfbcd"}, {"BlockType": "WORD", "Confidence": 99.375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03902813419699669, "Height": 0.00991938915103674, "Left": 0.07468326389789581, "Top": 0.25894466042518616}, "Polygon": [{"X": 0.07614710181951523, "Y": 0.2596811354160309}, {"X": 0.1137114018201828, "Y": 0.25894466042518616}, {"X": 0.11238886415958405, "Y": 0.26813432574272156}, {"X": 0.07468326389789581, "Y": 0.2688640356063843}]}, "Id": "02dc3c03-8653-4542-b0d3-1df79fede265"}, {"BlockType": "WORD", "Confidence": 98.017578125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011133105494081974, "Height": 0.008773313835263252, "Left": 0.11696475744247437, "Top": 0.2594323456287384}, "Polygon": [{"X": 0.11818321794271469, "Y": 0.25962650775909424}, {"X": 0.12809786200523376, "Y": 0.2594323456287384}, {"X": 0.12691426277160645, "Y": 0.2680131494998932}, {"X": 0.11696475744247437, "Y": 0.2682056427001953}]}, "Id": "73fc3c28-1d68-4c95-a21f-b1160c268477"}, {"BlockType": "WORD", "Confidence": 99.69567108154297, "Text": "BIR-78823", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07914793491363525, "Height": 0.010941104032099247, "Left": 0.16557471454143524, "Top": 0.25794991850852966}, "Polygon": [{"X": 0.16672295331954956, "Y": 0.25947579741477966}, {"X": 0.2447226494550705, "Y": 0.25794991850852966}, {"X": 0.2438763529062271, "Y": 0.2673795223236084}, {"X": 0.16557471454143524, "Y": 0.26889100670814514}]}, "Id": "daa7d682-8695-456b-99fa-3b85d9233e43"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Tons", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03607175499200821, "Height": 0.009114332497119904, "Left": 0.17663787305355072, "Top": 0.34122732281684875}, "Polygon": [{"X": 0.17760421335697174, "Y": 0.34183409810066223}, {"X": 0.21270963549613953, "Y": 0.34122732281684875}, {"X": 0.21186195313930511, "Y": 0.3497411012649536}, {"X": 0.17663787305355072, "Y": 0.35034167766571045}]}, "Id": "3ef99317-06ad-468d-bf71-7bc72cde3448"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019689010456204414, "Height": 0.008601011708378792, "Left": 0.268192321062088, "Top": 0.3401064872741699}, "Polygon": [{"X": 0.26883038878440857, "Y": 0.340435653924942}, {"X": 0.28788134455680847, "Y": 0.3401064872741699}, {"X": 0.2873060703277588, "Y": 0.34838157892227173}, {"X": 0.268192321062088, "Y": 0.34870749711990356}]}, "Id": "c1d9872f-6c1f-418e-97a6-9c6554981eee"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04551136866211891, "Height": 0.010337567888200283, "Left": 0.05348406359553337, "Top": 0.35572683811187744}, "Polygon": [{"X": 0.055034950375556946, "Y": 0.3564714193344116}, {"X": 0.09899543225765228, "Y": 0.35572683811187744}, {"X": 0.09761057049036026, "Y": 0.36532869935035706}, {"X": 0.05348406359553337, "Y": 0.3660644292831421}]}, "Id": "a50e6289-f765-4547-a9f6-f460a206fdc3"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "37.64", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03860550373792648, "Height": 0.0098249027505517, "Left": 0.1714421957731247, "Top": 0.35520851612091064}, "Polygon": [{"X": 0.1724989265203476, "Y": 0.35584309697151184}, {"X": 0.21004769206047058, "Y": 0.35520851612091064}, {"X": 0.209127277135849, "Y": 0.3644060790538788}, {"X": 0.1714421957731247, "Y": 0.36503341794013977}]}, "Id": "7241f374-9daa-4820-adeb-ea8455cda906"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "75,280", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04691743478178978, "Height": 0.01099210511893034, "Left": 0.24149785935878754, "Top": 0.35347801446914673}, "Polygon": [{"X": 0.24238887429237366, "Y": 0.35425639152526855}, {"X": 0.2884152829647064, "Y": 0.35347801446914673}, {"X": 0.2877105176448822, "Y": 0.36370161175727844}, {"X": 0.24149785935878754, "Y": 0.36447012424468994}]}, "Id": "36b51355-df96-4400-ba60-8cff5ce28927"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033032625913619995, "Height": 0.009711642749607563, "Left": 0.3149574398994446, "Top": 0.3529883921146393}, "Polygon": [{"X": 0.3154902756214142, "Y": 0.35353752970695496}, {"X": 0.3479900658130646, "Y": 0.3529883921146393}, {"X": 0.3475753962993622, "Y": 0.36215710639953613}, {"X": 0.3149574398994446, "Y": 0.3627000153064728}]}, "Id": "df7c32ad-1e8d-443c-97a3-0540bdd33720"}, {"BlockType": "WORD", "Confidence": 98.2005386352539, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01290335413068533, "Height": 0.009524676017463207, "Left": 0.35266798734664917, "Top": 0.3530005216598511}, "Polygon": [{"X": 0.35307031869888306, "Y": 0.3532116413116455}, {"X": 0.36557134985923767, "Y": 0.3530005216598511}, {"X": 0.3652152419090271, "Y": 0.3623164892196655}, {"X": 0.35266798734664917, "Y": 0.36252519488334656}]}, "Id": "4573f63e-43e8-41c4-af7b-cf27fbaba19a"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "9/16/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051322467625141144, "Height": 0.01002431195229292, "Left": 0.38997912406921387, "Top": 0.35161349177360535}, "Polygon": [{"X": 0.3902393579483032, "Y": 0.35247600078582764}, {"X": 0.4413015842437744, "Y": 0.35161349177360535}, {"X": 0.44122758507728577, "Y": 0.36078500747680664}, {"X": 0.38997912406921387, "Y": 0.36163780093193054}]}, "Id": "41eabf3e-ddd6-49e6-9e00-c77568ce5217"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "16:15:12", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059661537408828735, "Height": 0.0103955939412117, "Left": 0.447249174118042, "Top": 0.35081443190574646}, "Polygon": [{"X": 0.4473024904727936, "Y": 0.35181793570518494}, {"X": 0.5067413449287415, "Y": 0.35081443190574646}, {"X": 0.5069107413291931, "Y": 0.3602180778980255}, {"X": 0.447249174118042, "Y": 0.36121001839637756}]}, "Id": "99f3f2e6-32b2-40a3-b30d-5acf8b82e2b2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034239061176776886, "Height": 0.010116534307599068, "Left": 0.050253674387931824, "Top": 0.3695423901081085}, "Polygon": [{"X": 0.05180557444691658, "Y": 0.370083749294281}, {"X": 0.08449273556470871, "Y": 0.3695423901081085}, {"X": 0.08306333422660828, "Y": 0.3791241943836212}, {"X": 0.050253674387931824, "Y": 0.37965890765190125}]}, "Id": "006ec732-0be6-480e-8d7f-b5d3ffe56a51"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "15.95", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037199150770902634, "Height": 0.009656614623963833, "Left": 0.1705176830291748, "Top": 0.3689396381378174}, "Polygon": [{"X": 0.17155706882476807, "Y": 0.3695370852947235}, {"X": 0.20771683752536774, "Y": 0.3689396381378174}, {"X": 0.20680613815784454, "Y": 0.3780057430267334}, {"X": 0.1705176830291748, "Y": 0.3785962760448456}]}, "Id": "e45d299d-82dd-4631-a361-d29d5aa75070"}, {"BlockType": "WORD", "Confidence": 99.4834213256836, "Text": "31,900", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04729463532567024, "Height": 0.0107685886323452, "Left": 0.2395012080669403, "Top": 0.3671674430370331}, "Polygon": [{"X": 0.24037690460681915, "Y": 0.3679350018501282}, {"X": 0.28679585456848145, "Y": 0.3671674430370331}, {"X": 0.2861030399799347, "Y": 0.37717828154563904}, {"X": 0.2395012080669403, "Y": 0.3779360353946686}]}, "Id": "dfadfbf9-6494-4f17-8381-612869c96bd6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03319040685892105, "Height": 0.009845324791967869, "Left": 0.3134932518005371, "Top": 0.36751511693000793}, "Polygon": [{"X": 0.3140367269515991, "Y": 0.36805376410484314}, {"X": 0.34668365120887756, "Y": 0.36751511693000793}, {"X": 0.34626007080078125, "Y": 0.37682822346687317}, {"X": 0.3134932518005371, "Y": 0.377360463142395}]}, "Id": "36fc18cf-b5f1-48fc-8ebe-27c17a9009eb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02767002582550049, "Height": 0.009745616465806961, "Left": 0.35113242268562317, "Top": 0.36727073788642883}, "Polygon": [{"X": 0.3515373170375824, "Y": 0.367720365524292}, {"X": 0.37880244851112366, "Y": 0.36727073788642883}, {"X": 0.3784976601600647, "Y": 0.37657204270362854}, {"X": 0.35113242268562317, "Y": 0.3770163357257843}]}, "Id": "97ef7ff8-8630-4b11-a8e5-eae828c861f8"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "9/16/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05162561684846878, "Height": 0.010264439508318901, "Left": 0.3890678286552429, "Top": 0.3662506937980652}, "Polygon": [{"X": 0.3893371522426605, "Y": 0.3670975863933563}, {"X": 0.4406934380531311, "Y": 0.3662506937980652}, {"X": 0.44061553478240967, "Y": 0.3756783604621887}, {"X": 0.3890678286552429, "Y": 0.37651512026786804}]}, "Id": "fea6f139-d390-4038-995c-c9b7a659a8a0"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "16:58:06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059843871742486954, "Height": 0.010557345114648342, "Left": 0.44704514741897583, "Top": 0.3655032813549042}, "Polygon": [{"X": 0.4470999538898468, "Y": 0.3664858639240265}, {"X": 0.5067174434661865, "Y": 0.3655032813549042}, {"X": 0.5068889856338501, "Y": 0.37509000301361084}, {"X": 0.44704514741897583, "Y": 0.3760606348514557}]}, "Id": "0051d1cb-ab3c-4f26-bc00-18890b11d89d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026731213554739952, "Height": 0.009963869117200375, "Left": 0.04925573989748955, "Top": 0.3842046558856964}, "Polygon": [{"X": 0.05079960078001022, "Y": 0.38461169600486755}, {"X": 0.07598695158958435, "Y": 0.3842046558856964}, {"X": 0.0745367482304573, "Y": 0.3937666714191437}, {"X": 0.04925573989748955, "Y": 0.39416852593421936}]}, "Id": "7bd7de8d-4494-4cc0-913d-f284cbecf6a3"}, {"BlockType": "WORD", "Confidence": 99.56233978271484, "Text": "21.69", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039131466299295425, "Height": 0.009938446804881096, "Left": 0.16661952435970306, "Top": 0.3831329047679901}, "Polygon": [{"X": 0.16769753396511078, "Y": 0.38374677300453186}, {"X": 0.20575100183486938, "Y": 0.3831329047679901}, {"X": 0.20481160283088684, "Y": 0.3924650251865387}, {"X": 0.16661952435970306, "Y": 0.39307132363319397}]}, "Id": "de2c4921-cc86-477c-8744-17e590bbfb46"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "43,380", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04820170998573303, "Height": 0.010992868803441525, "Left": 0.23832827806472778, "Top": 0.3817949891090393}, "Polygon": [{"X": 0.23922348022460938, "Y": 0.3825581669807434}, {"X": 0.2865299880504608, "Y": 0.3817949891090393}, {"X": 0.28582435846328735, "Y": 0.39203494787216187}, {"X": 0.23832827806472778, "Y": 0.3927878439426422}]}, "Id": "f56e05f2-2694-47a8-b816-5a23615a4a35"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "90e9f6e1-a5ab-424b-b1c1-bfd8775072c8", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "90e9f6e1-a5ab-424b-b1c1-bfd8775072c8", "content-type": "application/x-amz-json-1.1", "content-length": "48201", "date": "Wed, 24 Sep 2025 12:46:07 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "70edee27-1650-401e-8dc7-947e4cb9327f", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:09 GMT", "content-type": "application/json", "content-length": "639", "connection": "keep-alive", "x-amzn-requestid": "70edee27-1650-401e-8dc7-947e4cb9327f"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify page1. The content indicates a \"Trip Sheet\" with truck weight info. Keywords: Trip Ticket, <PERSON>, <PERSON><PERSON>, Net, Date In, Date Out. Likely a Scale Ticket. It contains weight data. So doc_type: scale_ticket. No continuation marker. So output array with one object: page_no 1, doc_type \"scale_ticket\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2694, "outputTokens": 96, "totalTokens": 2790}, "metrics": {"latencyMs": 688}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/df25ba6c_EAMCIVO8E5KY4U6ZYWK2.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n<PERSON><PERSON><PERSON><PERSON> INDUSTRIES TRIP SHEET\nND Paper\nND\nND PAPER\nBiron Scale(Biron)\n78823\nSource\nGPH_WHIT_WI_COUNTRYR(GP HARMON RECYCI\nContract\n023594 (AMERICAN CHUNG NAM)\nDestination\nBIRON(BIRON MILL)\nDriver\nRORY\nTrailer 5310220\nBOL\nTrip Ticket ********\nProduct OCC 11D\nLoad #\nBIR-78823\nTons\nLB\nGross\n37.64\n75,280\nDate In\n9/16/25 16:15:12\nTare\n15.95\n31,900\nDate Out 9/16/25 16:58:06\nNet\n21.69\n43,380\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}