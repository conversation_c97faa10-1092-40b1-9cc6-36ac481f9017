2025-09-24 16:06:10,480 - INFO - Logging initialized. Log file: logs/test_classification_20250924_160610.log
2025-09-24 16:06:10,480 - INFO - 📁 Found 12 files to process
2025-09-24 16:06:10,480 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 16:06:10,480 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 16:06:10,480 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 16:06:10,480 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 16:06:10,481 - INFO - ⬆️ [16:06:10] Uploading: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:12,698 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf -> s3://document-extraction-logistically/temp/ba8f0878_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:12,698 - INFO - 🔍 [16:06:12] Starting classification: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:12,699 - INFO - ⬆️ [16:06:12] Uploading: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:12,701 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:12,720 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:12,728 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba8f0878_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:12,729 - INFO - Processing PDF from S3...
2025-09-24 16:06:12,729 - INFO - Downloading PDF from S3 to /tmp/tmp9zxowd77/ba8f0878_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:14,821 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:06:14,822 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:14,825 - INFO - Splitting PDF ba8f0878_AYEA5J1NILYPMWA7PN4V into 2 pages
2025-09-24 16:06:14,831 - INFO - Split PDF into 2 pages
2025-09-24 16:06:14,831 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:14,831 - INFO - Expected pages: [1, 2]
2025-09-24 16:06:15,945 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf -> s3://document-extraction-logistically/temp/63e0b06f_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:15,946 - INFO - 🔍 [16:06:15] Starting classification: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:15,947 - INFO - ⬆️ [16:06:15] Uploading: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:15,950 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:15,966 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:15,972 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/63e0b06f_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:15,972 - INFO - Processing PDF from S3...
2025-09-24 16:06:15,973 - INFO - Downloading PDF from S3 to /tmp/tmpsk7ni76p/63e0b06f_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:17,746 - INFO - Page 2: Extracted 216 characters, 11 lines from ba8f0878_AYEA5J1NILYPMWA7PN4V_107c9048_page_002.pdf
2025-09-24 16:06:17,747 - INFO - Successfully processed page 2
2025-09-24 16:06:18,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf -> s3://document-extraction-logistically/temp/3393ffa4_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:18,475 - INFO - 🔍 [16:06:18] Starting classification: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:18,476 - INFO - ⬆️ [16:06:18] Uploading: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:18,479 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:18,500 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:18,507 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3393ffa4_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:18,508 - INFO - Processing PDF from S3...
2025-09-24 16:06:18,508 - INFO - Downloading PDF from S3 to /tmp/tmprsy5cjoq/3393ffa4_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:18,606 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:06:18,607 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:18,611 - INFO - Splitting PDF 63e0b06f_C3I3XLR18U7J6P1N2LZR into 1 pages
2025-09-24 16:06:18,719 - INFO - Split PDF into 1 pages
2025-09-24 16:06:18,720 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:18,720 - INFO - Expected pages: [1]
2025-09-24 16:06:19,963 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf -> s3://document-extraction-logistically/temp/f42330e9_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:19,963 - INFO - 🔍 [16:06:19] Starting classification: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:19,964 - INFO - ⬆️ [16:06:19] Uploading: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:19,965 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:19,981 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:19,986 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f42330e9_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:19,986 - INFO - Processing PDF from S3...
2025-09-24 16:06:19,987 - INFO - Downloading PDF from S3 to /tmp/tmp73dvlbs4/f42330e9_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:20,585 - INFO - Page 1: Extracted 2153 characters, 114 lines from ba8f0878_AYEA5J1NILYPMWA7PN4V_107c9048_page_001.pdf
2025-09-24 16:06:20,586 - INFO - Successfully processed page 1
2025-09-24 16:06:20,586 - INFO - Combined 2 pages into final text
2025-09-24 16:06:20,586 - INFO - Text validation for ba8f0878_AYEA5J1NILYPMWA7PN4V: 2405 characters, 2 pages
2025-09-24 16:06:20,586 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:20,587 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:21,080 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg -> s3://document-extraction-logistically/temp/c16213d5_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:21,080 - INFO - 🔍 [16:06:21] Starting classification: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:21,081 - INFO - ⬆️ [16:06:21] Uploading: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:21,087 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:21,097 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:21,101 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c16213d5_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:21,102 - INFO - Processing image from S3...
2025-09-24 16:06:21,227 - INFO - Downloaded PDF size: 1.4 MB
2025-09-24 16:06:21,227 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:21,228 - INFO - Splitting PDF 3393ffa4_C94JBR3RYYOOM5J2PFUM into 1 pages
2025-09-24 16:06:21,232 - INFO - Split PDF into 1 pages
2025-09-24 16:06:21,232 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:21,233 - INFO - Expected pages: [1]
2025-09-24 16:06:21,669 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf -> s3://document-extraction-logistically/temp/15cb58d0_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:21,669 - INFO - 🔍 [16:06:21] Starting classification: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:21,670 - INFO - ⬆️ [16:06:21] Uploading: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:21,672 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:21,689 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:21,694 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15cb58d0_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:21,694 - INFO - Processing PDF from S3...
2025-09-24 16:06:21,694 - INFO - Downloading PDF from S3 to /tmp/tmph9d72kqz/15cb58d0_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:22,295 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf -> s3://document-extraction-logistically/temp/fc0aebfc_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:22,295 - INFO - 🔍 [16:06:22] Starting classification: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:22,296 - INFO - ⬆️ [16:06:22] Uploading: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:22,298 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:22,316 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:22,320 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fc0aebfc_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:22,321 - INFO - Processing PDF from S3...
2025-09-24 16:06:22,321 - INFO - Downloading PDF from S3 to /tmp/tmp4n5augdv/fc0aebfc_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:22,598 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 16:06:22,598 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:22,599 - INFO - Splitting PDF f42330e9_DY3D94HTH1ZH420GMDO6 into 1 pages
2025-09-24 16:06:22,601 - INFO - Split PDF into 1 pages
2025-09-24 16:06:22,601 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:22,601 - INFO - Expected pages: [1]
2025-09-24 16:06:23,451 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:06:23,451 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:23,454 - INFO - Splitting PDF 15cb58d0_GJ7Z12W1O0CQO9E9H39H into 2 pages
2025-09-24 16:06:23,475 - INFO - Split PDF into 2 pages
2025-09-24 16:06:23,475 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:23,475 - INFO - Expected pages: [1, 2]
2025-09-24 16:06:23,580 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '213ea3a0-45ec-4e26-9110-48f9306e93ac', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:23 GMT', 'content-type': 'application/json', 'content-length': '1604', 'connection': 'keep-alive', 'x-amzn-requestid': '213ea3a0-45ec-4e26-9110-48f9306e93ac'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page with doc_type. We have a document: Sea Freight FCL Delivery Order. Terms: shipping, delivery order. This is like a Delivery Order or maybe a Bill of Lading? It looks like a shipping document. The page mentions EBL - Express Bill of Lading, OCEAN BILL OF LADING, HOUSE BILL OF LADING. It\'s a delivery order. But we need to pick a type from list. There\'s no explicit "Delivery Order" type in list. But maybe it\'s a "bol" (Bill of Lading) because includes BOL numbers. But this seems more like a Delivery Order - not exactly BOL. However, the list includes "bol" for Bill of Lading. The presence of "OCEAN BILL OF LADING" and "HOUSE BILL OF LADING" and "EBL - Express Bill of Lading" indicates it\'s a BOL. Also "SHIPMENT DETAILS" etc. So doc_type likely "bol".\n\nWe must output for page1 and page2. Both pages are part of same document, page2 is continuation. We must check if page2 is a continuation: It starts with "Sea Freight FCL Delivery Order" and "Page 2 of 2" etc. It is a continuation of page1. So we assign same doc_type "bol". Also page2 includes "END OF DOCUMENT". No other header.\n\nThus output array of objects: two items: page_no 1 and 2, doc_type "bol".\n\nNeed to call the function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3277, 'outputTokens': 341, 'totalTokens': 3618}, 'metrics': {'latencyMs': 1808}}
2025-09-24 16:06:23,581 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ba8f0878_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:23,769 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf -> s3://document-extraction-logistically/temp/9afe8e1c_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:23,769 - INFO - 🔍 [16:06:23] Starting classification: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:23,770 - INFO - ⬆️ [16:06:23] Uploading: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:23,772 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:23,792 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:23,803 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9afe8e1c_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:23,803 - INFO - Processing PDF from S3...
2025-09-24 16:06:23,804 - INFO - Downloading PDF from S3 to /tmp/tmplc9fan7d/9afe8e1c_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:24,375 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf -> s3://document-extraction-logistically/temp/dd4cc42e_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:24,375 - INFO - 🔍 [16:06:24] Starting classification: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:24,376 - INFO - ⬆️ [16:06:24] Uploading: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:24,377 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:24,397 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:24,403 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dd4cc42e_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:24,403 - INFO - Processing PDF from S3...
2025-09-24 16:06:24,404 - INFO - Downloading PDF from S3 to /tmp/tmpjho1mwqq/dd4cc42e_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:24,421 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:06:24,421 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:24,422 - INFO - Splitting PDF fc0aebfc_N9XYNNZR54XFORX6RGH2 into 2 pages
2025-09-24 16:06:24,424 - INFO - Split PDF into 2 pages
2025-09-24 16:06:24,424 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:24,424 - INFO - Expected pages: [1, 2]
2025-09-24 16:06:24,992 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf -> s3://document-extraction-logistically/temp/11575f80_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:24,992 - INFO - 🔍 [16:06:24] Starting classification: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:24,994 - INFO - ⬆️ [16:06:24] Uploading: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:24,994 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:25,021 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:25,038 - INFO - S3 Image temp/c16213d5_G7K3JPMT2OXZWEI7RRNQ.jpg: Extracted 2237 characters, 96 lines
2025-09-24 16:06:25,040 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/11575f80_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:25,040 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:25,040 - INFO - Processing PDF from S3...
2025-09-24 16:06:25,040 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:25,044 - INFO - Downloading PDF from S3 to /tmp/tmpd0prtx0z/11575f80_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:25,821 - INFO - Page 1: Extracted 1847 characters, 114 lines from 63e0b06f_C3I3XLR18U7J6P1N2LZR_5a23f05d_page_001.pdf
2025-09-24 16:06:25,822 - INFO - Successfully processed page 1
2025-09-24 16:06:25,822 - INFO - Combined 1 pages into final text
2025-09-24 16:06:25,822 - INFO - Text validation for 63e0b06f_C3I3XLR18U7J6P1N2LZR: 1864 characters, 1 pages
2025-09-24 16:06:25,822 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:25,822 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:26,162 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:06:26,162 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:26,163 - INFO - Splitting PDF dd4cc42e_ODBS2NBTU6S3GF9JVE6V into 1 pages
2025-09-24 16:06:26,168 - INFO - Split PDF into 1 pages
2025-09-24 16:06:26,169 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:26,169 - INFO - Expected pages: [1]
2025-09-24 16:06:26,320 - INFO - Page 2: Extracted 0 characters, 0 lines from 15cb58d0_GJ7Z12W1O0CQO9E9H39H_747a03c6_page_002.pdf
2025-09-24 16:06:26,320 - INFO - Successfully processed page 2
2025-09-24 16:06:26,492 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:06:26,492 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:26,494 - INFO - Splitting PDF 9afe8e1c_NFG5QQXEN21TEZZ9SYPW into 2 pages
2025-09-24 16:06:26,499 - INFO - Split PDF into 2 pages
2025-09-24 16:06:26,499 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:26,499 - INFO - Expected pages: [1, 2]
2025-09-24 16:06:26,843 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:06:26,844 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:26,845 - INFO - Splitting PDF 11575f80_SMX8DOTQ89U191SMP0TO into 1 pages
2025-09-24 16:06:26,846 - INFO - Split PDF into 1 pages
2025-09-24 16:06:26,847 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:26,847 - INFO - Expected pages: [1]
2025-09-24 16:06:27,262 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '41dc13de-6b2f-42ab-8a98-ed496e1d2049', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:27 GMT', 'content-type': 'application/json', 'content-length': '1060', 'connection': 'keep-alive', 'x-amzn-requestid': '41dc13de-6b2f-42ab-8a98-ed496e1d2049'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page provided. We must analyze whether this is a document type. The page contains "DELIVERY ORDER" heading, details: shipping info, etc. Delivery Order is like a purchase order for shipping? Delivery order is a type of order? Not in the list. It\'s a document that authorizes pickup and delivery. It is not a bill of lading, invoice, POD, rate confirmation, etc. It\'s a delivery order, which is not in the list. So default to other unless there is a category that matches. There\'s no "delivery order" type. So classify as other. But we need to check if it\'s continuation: no. So doc_type=other for page 1.\n\nReturn tool call with documents array containing one object: page_no: 1, doc_type: "other".\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3222, 'outputTokens': 186, 'totalTokens': 3408}, 'metrics': {'latencyMs': 961}}
2025-09-24 16:06:27,262 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c16213d5_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:27,309 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg -> s3://document-extraction-logistically/temp/9f3ea21a_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:27,309 - INFO - 🔍 [16:06:27] Starting classification: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:27,310 - INFO - ⬆️ [16:06:27] Uploading: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:27,312 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:27,328 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:27,332 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9f3ea21a_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:27,332 - INFO - Processing image from S3...
2025-09-24 16:06:27,669 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5141d951-0c8c-4c56-a289-868cf1f08a3b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:27 GMT', 'content-type': 'application/json', 'content-length': '599', 'connection': 'keep-alive', 'x-amzn-requestid': '5141d951-0c8c-4c56-a289-868cf1f08a3b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. We need to output using function. Determine document type. The content includes "Bill of Lading" header. So doc_type should be "bol". It\'s a single page. No continuation. So output array with one object: page_no:1, doc_type:"bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3056, 'outputTokens': 85, 'totalTokens': 3141}, 'metrics': {'latencyMs': 668}}
2025-09-24 16:06:27,669 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/63e0b06f_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:27,962 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf -> s3://document-extraction-logistically/temp/f8aa49d5_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:27,962 - INFO - 🔍 [16:06:27] Starting classification: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:27,964 - INFO - Initializing TextractProcessor...
2025-09-24 16:06:28,017 - INFO - Initializing BedrockProcessor...
2025-09-24 16:06:28,020 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f8aa49d5_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:28,021 - INFO - Processing PDF from S3...
2025-09-24 16:06:28,022 - INFO - Downloading PDF from S3 to /tmp/tmp3fg5ms6f/f8aa49d5_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:28,042 - INFO - 

AYEA5J1NILYPMWA7PN4V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:28,043 - INFO - 

✓ Saved result: output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:06:28,321 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba8f0878_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:28,351 - INFO - 

G7K3JPMT2OXZWEI7RRNQ.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:06:28,351 - INFO - 

✓ Saved result: output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:06:28,632 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c16213d5_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:28,666 - INFO - 

C3I3XLR18U7J6P1N2LZR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:28,667 - INFO - 

✓ Saved result: output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:06:28,946 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/63e0b06f_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:29,081 - INFO - Page 2: Extracted 1932 characters, 48 lines from fc0aebfc_N9XYNNZR54XFORX6RGH2_7da306e2_page_002.pdf
2025-09-24 16:06:29,081 - INFO - Successfully processed page 2
2025-09-24 16:06:29,379 - INFO - Page 1: Extracted 4034 characters, 142 lines from 15cb58d0_GJ7Z12W1O0CQO9E9H39H_747a03c6_page_001.pdf
2025-09-24 16:06:29,380 - INFO - Successfully processed page 1
2025-09-24 16:06:29,380 - INFO - Combined 2 pages into final text
2025-09-24 16:06:29,380 - INFO - Text validation for 15cb58d0_GJ7Z12W1O0CQO9E9H39H: 4069 characters, 2 pages
2025-09-24 16:06:29,381 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:29,381 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:29,888 - INFO - Page 1: Extracted 1616 characters, 146 lines from fc0aebfc_N9XYNNZR54XFORX6RGH2_7da306e2_page_001.pdf
2025-09-24 16:06:29,888 - INFO - Successfully processed page 1
2025-09-24 16:06:29,888 - INFO - Combined 2 pages into final text
2025-09-24 16:06:29,888 - INFO - Text validation for fc0aebfc_N9XYNNZR54XFORX6RGH2: 3584 characters, 2 pages
2025-09-24 16:06:29,888 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:29,888 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:30,088 - INFO - Page 1: Extracted 3353 characters, 164 lines from 3393ffa4_C94JBR3RYYOOM5J2PFUM_c5abcff8_page_001.pdf
2025-09-24 16:06:30,089 - INFO - Successfully processed page 1
2025-09-24 16:06:30,089 - INFO - Combined 1 pages into final text
2025-09-24 16:06:30,089 - INFO - Text validation for 3393ffa4_C94JBR3RYYOOM5J2PFUM: 3370 characters, 1 pages
2025-09-24 16:06:30,090 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:30,090 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:30,122 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 16:06:30,122 - INFO - Splitting PDF into individual pages...
2025-09-24 16:06:30,125 - INFO - Splitting PDF f8aa49d5_Z10BLMPIZ96XLUZ5NUPF into 1 pages
2025-09-24 16:06:30,203 - INFO - Split PDF into 1 pages
2025-09-24 16:06:30,203 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:06:30,203 - INFO - Expected pages: [1]
2025-09-24 16:06:30,392 - INFO - Page 1: Extracted 1185 characters, 52 lines from f42330e9_DY3D94HTH1ZH420GMDO6_826e7527_page_001.pdf
2025-09-24 16:06:30,392 - INFO - Successfully processed page 1
2025-09-24 16:06:30,393 - INFO - Combined 1 pages into final text
2025-09-24 16:06:30,393 - INFO - Text validation for f42330e9_DY3D94HTH1ZH420GMDO6: 1202 characters, 1 pages
2025-09-24 16:06:30,393 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:30,393 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:31,774 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5e11d0b8-725e-40ad-a4c1-496d6966e6cf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:31 GMT', 'content-type': 'application/json', 'content-length': '700', 'connection': 'keep-alive', 'x-amzn-requestid': '5e11d0b8-725e-40ad-a4c1-496d6966e6cf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have to classify each page. Page 1 clearly contains "BILL OF LADING". So doc_type: bol. Page 2 continues with BOL content. Starting with "1" maybe page 2 of 2. It\'s a continuation. So same bol. So output two objects with page_no 1 and 2 both doc_type bol. Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_skHDJrX7TPOROV48KAicZA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3553, 'outputTokens': 102, 'totalTokens': 3655}, 'metrics': {'latencyMs': 720}}
2025-09-24 16:06:31,774 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fc0aebfc_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:31,834 - INFO - Page 1: Extracted 2057 characters, 76 lines from dd4cc42e_ODBS2NBTU6S3GF9JVE6V_7e23e608_page_001.pdf
2025-09-24 16:06:31,835 - INFO - Successfully processed page 1
2025-09-24 16:06:31,835 - INFO - Combined 1 pages into final text
2025-09-24 16:06:31,835 - INFO - Text validation for dd4cc42e_ODBS2NBTU6S3GF9JVE6V: 2074 characters, 1 pages
2025-09-24 16:06:31,836 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:31,836 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:31,869 - INFO - 

N9XYNNZR54XFORX6RGH2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:31,869 - INFO - 

✓ Saved result: output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:06:32,158 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fc0aebfc_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:32,216 - INFO - S3 Image temp/9f3ea21a_Y7P2IPB0S079FZE11EEA.jpg: Extracted 821 characters, 52 lines
2025-09-24 16:06:32,216 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:32,216 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:32,239 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9849088f-6bd0-411b-ba8f-367ed1d6041f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:32 GMT', 'content-type': 'application/json', 'content-length': '918', 'connection': 'keep-alive', 'x-amzn-requestid': '9849088f-6bd0-411b-ba8f-367ed1d6041f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. It looks like an Air Waybill (AWB). Is that a Bill of Lading? Actually AWB is a carrier document for air freight, similar to BOL but for air. The category list includes bol, pod, invoice, etc. We need to decide which doc_type applies. The AWB is essentially a Bill of Lading for air transport. The definition for bol: Bill of Lading or Delivery Order, legal transport document. AWB is also a Bill of Lading for air. So likely bol. There\'s no invoice, no POD, no rate confirmation, etc. It has "Air Waybill" header. So doc_type: bol. So output with page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3441, 'outputTokens': 170, 'totalTokens': 3611}, 'metrics': {'latencyMs': 976}}
2025-09-24 16:06:32,240 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3393ffa4_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:32,279 - INFO - Page 1: Extracted 2916 characters, 126 lines from 11575f80_SMX8DOTQ89U191SMP0TO_c2f1638d_page_001.pdf
2025-09-24 16:06:32,279 - INFO - Successfully processed page 1
2025-09-24 16:06:32,280 - INFO - Combined 1 pages into final text
2025-09-24 16:06:32,281 - INFO - Text validation for 11575f80_SMX8DOTQ89U191SMP0TO: 2933 characters, 1 pages
2025-09-24 16:06:32,284 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:32,285 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:32,302 - INFO - 

C94JBR3RYYOOM5J2PFUM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:32,302 - INFO - 

✓ Saved result: output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:06:32,391 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd383eaa9-85d1-4125-bdbe-d8ca951d0614', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:32 GMT', 'content-type': 'application/json', 'content-length': '806', 'connection': 'keep-alive', 'x-amzn-requestid': 'd383eaa9-85d1-4125-bdbe-d8ca951d0614'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content is a Bill of Lading. The keywords: "BILL OF LADING", "B/L", "Consignee", etc. So doc_type: bol.\n\nWe need to output JSON via tool classify_logistics_doc_type with documents array, each has page_no and doc_type. Use only that tool. There\'s one page. Let\'s produce:\n\n{\n  "documents": [\n    {"page_no": 1, "doc_type": "bol"}\n  ]\n}\n\nNow call the function.'}}}, {'toolUse': {'toolUseId': 'tooluse_19bGO_9HSreG8-ROmvuGzQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2785, 'outputTokens': 123, 'totalTokens': 2908}, 'metrics': {'latencyMs': 765}}
2025-09-24 16:06:32,392 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f42330e9_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:32,581 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3393ffa4_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:32,594 - INFO - 

DY3D94HTH1ZH420GMDO6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:32,594 - INFO - 

✓ Saved result: output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:06:32,713 - INFO - Page 2: Extracted 1028 characters, 92 lines from 9afe8e1c_NFG5QQXEN21TEZZ9SYPW_fe2fd15a_page_002.pdf
2025-09-24 16:06:32,714 - INFO - Successfully processed page 2
2025-09-24 16:06:32,889 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f42330e9_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:33,538 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f4f04f99-7cec-4575-ab3a-8b621b3c6ccf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:33 GMT', 'content-type': 'application/json', 'content-length': '3139', 'connection': 'keep-alive', 'x-amzn-requestid': 'f4f04f99-7cec-4575-ab3a-8b621b3c6ccf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page with doc_type. Page1 looks like a Bill of Lading (Conocimiento de embarque recto) with header "BILL OF LADING #". So doc_type: bol. There\'s no continuation note. Page2 is empty? It shows <page2> </page2>. Probably empty page, but still part of same document? We need to decide. The instructions: For every page, return object. If page is part of multi-page single document, assign same doc_type. Page2 has no content, might be continuation? It doesn\'t show "continued" or pagination. But it\'s likely just a blank page following BOL. Since it\'s part of same document, continuation? Might be a blank page appended. According to rule: If document is not from any categories, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign same doc_type as previous page. How to determine continuation: page starts with "continued" etc. But page2 has no content. It might be blank. Is that considered continuation? It\'s ambiguous. But since it\'s part of same PDF, likely just a blank page appended. The best classification: other? But the instruction: If not from any categories, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign same doc_type. Since page2 is blank, maybe continuation is not indicated. But maybe it\'s just a blank page included. It\'s not a continuation. So classify as other. But is blank page considered part of same doc? The instruction says if page is part of multi-page single document, each page still gets same doc_type. But how do we decide that? We need to detect continuation signals. There\'s no signal. So we might not assign bol to page2. But maybe the PDF has a blank page after. I\'d lean to classify as other. But maybe we should assign bol to page2 as same doc. But guidelines: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." So we need to detect that it\'s part of same doc. But how? The guidelines: "Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \'continued\', \'continued on next page\', \'continued on next\', etc., or if it indicates pagination like \'page 2 of 3\', or any other signal indicating continuation of the previous page/document." So we only treat as continuation if those signals present. Page2 does not. So we do not treat as continuation. So we classify as other. So output: page1 bol, page2 other.\n\nWe must output via tool call classify_logistics_doc_type. Provide array of objects per page? The tool expects an array of documents objects, each having page_no and doc_type. So pass array of two items.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3581, 'outputTokens': 637, 'totalTokens': 4218}, 'metrics': {'latencyMs': 2895}}
2025-09-24 16:06:33,538 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15cb58d0_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:33,591 - INFO - 

GJ7Z12W1O0CQO9E9H39H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:06:33,591 - INFO - 

✓ Saved result: output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:06:33,912 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15cb58d0_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:33,959 - INFO - Page 1: Extracted 1621 characters, 166 lines from 9afe8e1c_NFG5QQXEN21TEZZ9SYPW_fe2fd15a_page_001.pdf
2025-09-24 16:06:33,960 - INFO - Successfully processed page 1
2025-09-24 16:06:33,960 - INFO - Combined 2 pages into final text
2025-09-24 16:06:33,960 - INFO - Text validation for 9afe8e1c_NFG5QQXEN21TEZZ9SYPW: 2685 characters, 2 pages
2025-09-24 16:06:33,961 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:33,961 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:34,049 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '59f7cf03-4211-4825-9b8d-dcff8a01fbe7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:33 GMT', 'content-type': 'application/json', 'content-length': '526', 'connection': 'keep-alive', 'x-amzn-requestid': '59f7cf03-4211-4825-9b8d-dcff8a01fbe7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page 1 present. The text includes "BILL OF LADING" header. So doc_type is bol. We must output via tool call. Provide documents array with one entry: page_no 1, doc_type "bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2755, 'outputTokens': 72, 'totalTokens': 2827}, 'metrics': {'latencyMs': 575}}
2025-09-24 16:06:34,049 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9f3ea21a_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:34,070 - INFO - 

Y7P2IPB0S079FZE11EEA.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:34,070 - INFO - 

✓ Saved result: output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:06:34,073 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f21dab2a-ba43-43ff-a88c-4d14d86aee1b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:33 GMT', 'content-type': 'application/json', 'content-length': '640', 'connection': 'keep-alive', 'x-amzn-requestid': 'f21dab2a-ba43-43ff-a88c-4d14d86aee1b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It appears to be a Bill of Lading. Contains header "VICs Bill of Lading - Order". Keywords: Bill of Lading, Shipper, Consignee, etc. So doc_type is bol. There\'s no continuation indicator. So output page 1 bol. Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_XzzlSKx-Sw6ITaD1z8m9SA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3254, 'outputTokens': 82, 'totalTokens': 3336}, 'metrics': {'latencyMs': 610}}
2025-09-24 16:06:34,073 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/11575f80_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:34,134 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '912f7bd5-9293-4144-bc95-dcacaa36d188', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:33 GMT', 'content-type': 'application/json', 'content-length': '1134', 'connection': 'keep-alive', 'x-amzn-requestid': '912f7bd5-9293-4144-bc95-dcacaa36d188'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We must decide doc_type. This page looks like a Load Confirmation (similar to "Load confirmation# ...". This is a type of "tender_from_cust"?? Actually tender_from_cust is load tender from customer to carrier. This page is a load confirmation with carrier and broker info. It\'s a "Load Confirmation" which is basically a confirmation of load details. In classification list, we have "tender_from_cust" for load tender from customer. We also have possibly "combined_carrier_documents" but it\'s just one type. There\'s no invoice, no BOL, no POD, etc. It\'s a load confirmation. Not in list. So default to "other". But check if it\'s a continuation of previous page? There\'s only one page, no continuation. So answer: doc_type "other". Provide JSON with page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3042, 'outputTokens': 200, 'totalTokens': 3242}, 'metrics': {'latencyMs': 1069}}
2025-09-24 16:06:34,134 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dd4cc42e_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:34,348 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9f3ea21a_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:34,383 - INFO - 

SMX8DOTQ89U191SMP0TO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:34,384 - INFO - 

✓ Saved result: output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:06:34,662 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/11575f80_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:34,690 - INFO - 

ODBS2NBTU6S3GF9JVE6V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:06:34,691 - INFO - 

✓ Saved result: output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:06:34,982 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dd4cc42e_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:35,952 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ae94632c-8b84-4eb7-9e4c-50a7bda89530', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:35 GMT', 'content-type': 'application/json', 'content-length': '806', 'connection': 'keep-alive', 'x-amzn-requestid': 'ae94632c-8b84-4eb7-9e4c-50a7bda89530'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be a Packing List (PKG LIST). So doc_type is pack_list. Both pages show PKG LIST. They are part of the same multi-page document. The first page shows "Page 1 of 2". The second page shows "Page 2 of 2". So each page should be classified as pack_list.\n\nWe must output exactly two objects, one per page, with page_no and doc_type. Use the tool classify_logistics_doc_type. No extra text.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3734, 'outputTokens': 138, 'totalTokens': 3872}, 'metrics': {'latencyMs': 803}}
2025-09-24 16:06:35,953 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9afe8e1c_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:36,012 - INFO - 

NFG5QQXEN21TEZZ9SYPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:06:36,012 - INFO - 

✓ Saved result: output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:06:36,297 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9afe8e1c_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:36,313 - INFO - Page 1: Extracted 3550 characters, 185 lines from f8aa49d5_Z10BLMPIZ96XLUZ5NUPF_29c89d40_page_001.pdf
2025-09-24 16:06:36,313 - INFO - Successfully processed page 1
2025-09-24 16:06:36,314 - INFO - Combined 1 pages into final text
2025-09-24 16:06:36,314 - INFO - Text validation for f8aa49d5_Z10BLMPIZ96XLUZ5NUPF: 3567 characters, 1 pages
2025-09-24 16:06:36,314 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:06:36,314 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:06:38,431 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3f9a74ec-cef2-472c-aadf-1a8a32aaa613', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:36:38 GMT', 'content-type': 'application/json', 'content-length': '724', 'connection': 'keep-alive', 'x-amzn-requestid': '3f9a74ec-cef2-472c-aadf-1a8a32aaa613'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The page contains a Bill of Lading. There\'s header "BILL OF LADING". It contains shipping info, consignor, consignee, vessel, order number, etc. So doc_type is "bol". No other hint of invoice. It\'s a Bill of Lading.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing one object: page_no:1, doc_type:"bol".\n\nLet\'s do it.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3726, 'outputTokens': 116, 'totalTokens': 3842}, 'metrics': {'latencyMs': 729}}
2025-09-24 16:06:38,431 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f8aa49d5_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:38,473 - INFO - 

Z10BLMPIZ96XLUZ5NUPF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:06:38,473 - INFO - 

✓ Saved result: output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:06:38,780 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f8aa49d5_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:38,780 - INFO - 
📊 Processing Summary:
2025-09-24 16:06:38,781 - INFO -    Total files: 12
2025-09-24 16:06:38,781 - INFO -    Successful: 12
2025-09-24 16:06:38,781 - INFO -    Failed: 0
2025-09-24 16:06:38,781 - INFO -    Duration: 28.30 seconds
2025-09-24 16:06:38,781 - INFO -    Output directory: output
2025-09-24 16:06:38,781 - INFO - 
📋 Successfully Processed Files:
2025-09-24 16:06:38,781 - INFO -    📄 AYEA5J1NILYPMWA7PN4V.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:06:38,781 - INFO -    📄 C3I3XLR18U7J6P1N2LZR.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:06:38,781 - INFO -    📄 C94JBR3RYYOOM5J2PFUM.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:06:38,781 - INFO -    📄 DY3D94HTH1ZH420GMDO6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:06:38,781 - INFO -    📄 G7K3JPMT2OXZWEI7RRNQ.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 16:06:38,781 - INFO -    📄 GJ7Z12W1O0CQO9E9H39H.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 16:06:38,781 - INFO -    📄 N9XYNNZR54XFORX6RGH2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:06:38,781 - INFO -    📄 NFG5QQXEN21TEZZ9SYPW.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-24 16:06:38,781 - INFO -    📄 ODBS2NBTU6S3GF9JVE6V.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 16:06:38,781 - INFO -    📄 SMX8DOTQ89U191SMP0TO.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:06:38,782 - INFO -    📄 Y7P2IPB0S079FZE11EEA.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:06:38,782 - INFO -    📄 Z10BLMPIZ96XLUZ5NUPF.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:06:38,782 - INFO - 
============================================================================================================================================
2025-09-24 16:06:38,782 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 16:06:38,782 - INFO - ============================================================================================================================================
2025-09-24 16:06:38,782 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 16:06:38,782 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:06:38,782 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           1      bol                                      run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:06:38,782 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:38,782 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:06:38,782 - INFO - 
2025-09-24 16:06:38,782 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           2      bol                                      run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:06:38,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:06:38,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:06:38,783 - INFO - 
2025-09-24 16:06:38,783 - INFO - C3I3XLR18U7J6P1N2LZR.pdf                           1      bol                                      run1_C3I3XLR18U7J6P1N2LZR.json                                                  
2025-09-24 16:06:38,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:06:38,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:06:38,783 - INFO - 
2025-09-24 16:06:38,783 - INFO - C94JBR3RYYOOM5J2PFUM.pdf                           1      bol                                      run1_C94JBR3RYYOOM5J2PFUM.json                                                  
2025-09-24 16:06:38,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:06:38,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:06:38,783 - INFO - 
2025-09-24 16:06:38,783 - INFO - DY3D94HTH1ZH420GMDO6.pdf                           1      bol                                      run1_DY3D94HTH1ZH420GMDO6.json                                                  
2025-09-24 16:06:38,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:06:38,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:06:38,783 - INFO - 
2025-09-24 16:06:38,783 - INFO - G7K3JPMT2OXZWEI7RRNQ.jpg                           1      other                                    run1_G7K3JPMT2OXZWEI7RRNQ.json                                                  
2025-09-24 16:06:38,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:06:38,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:06:38,783 - INFO - 
2025-09-24 16:06:38,783 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           1      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:06:38,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:38,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:06:38,784 - INFO - 
2025-09-24 16:06:38,784 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           2      other                                    run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:06:38,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:06:38,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:06:38,784 - INFO - 
2025-09-24 16:06:38,784 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           1      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:06:38,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:38,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:06:38,784 - INFO - 
2025-09-24 16:06:38,784 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           2      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:06:38,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:06:38,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:06:38,784 - INFO - 
2025-09-24 16:06:38,784 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           1      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:06:38,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:38,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:06:38,784 - INFO - 
2025-09-24 16:06:38,784 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           2      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:06:38,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:06:38,784 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:06:38,784 - INFO - 
2025-09-24 16:06:38,784 - INFO - ODBS2NBTU6S3GF9JVE6V.pdf                           1      other                                    run1_ODBS2NBTU6S3GF9JVE6V.json                                                  
2025-09-24 16:06:38,784 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:06:38,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:06:38,785 - INFO - 
2025-09-24 16:06:38,785 - INFO - SMX8DOTQ89U191SMP0TO.pdf                           1      bol                                      run1_SMX8DOTQ89U191SMP0TO.json                                                  
2025-09-24 16:06:38,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:06:38,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:06:38,785 - INFO - 
2025-09-24 16:06:38,785 - INFO - Y7P2IPB0S079FZE11EEA.jpg                           1      bol                                      run1_Y7P2IPB0S079FZE11EEA.json                                                  
2025-09-24 16:06:38,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:06:38,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:06:38,785 - INFO - 
2025-09-24 16:06:38,785 - INFO - Z10BLMPIZ96XLUZ5NUPF.pdf                           1      bol                                      run1_Z10BLMPIZ96XLUZ5NUPF.json                                                  
2025-09-24 16:06:38,785 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:06:38,785 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:06:38,785 - INFO - 
2025-09-24 16:06:38,785 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:06:38,785 - INFO - Total entries: 16
2025-09-24 16:06:38,785 - INFO - ============================================================================================================================================
2025-09-24 16:06:38,785 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 16:06:38,785 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:06:38,785 - INFO -   1. AYEA5J1NILYPMWA7PN4V.pdf            Page 1   → bol             | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:06:38,785 - INFO -   2. AYEA5J1NILYPMWA7PN4V.pdf            Page 2   → bol             | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:06:38,785 - INFO -   3. C3I3XLR18U7J6P1N2LZR.pdf            Page 1   → bol             | run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:06:38,786 - INFO -   4. C94JBR3RYYOOM5J2PFUM.pdf            Page 1   → bol             | run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:06:38,786 - INFO -   5. DY3D94HTH1ZH420GMDO6.pdf            Page 1   → bol             | run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:06:38,786 - INFO -   6. G7K3JPMT2OXZWEI7RRNQ.jpg            Page 1   → other           | run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:06:38,786 - INFO -   7. GJ7Z12W1O0CQO9E9H39H.pdf            Page 1   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:06:38,786 - INFO -   8. GJ7Z12W1O0CQO9E9H39H.pdf            Page 2   → other           | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:06:38,786 - INFO -   9. N9XYNNZR54XFORX6RGH2.pdf            Page 1   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:06:38,786 - INFO -  10. N9XYNNZR54XFORX6RGH2.pdf            Page 2   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:06:38,786 - INFO -  11. NFG5QQXEN21TEZZ9SYPW.pdf            Page 1   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:06:38,786 - INFO -  12. NFG5QQXEN21TEZZ9SYPW.pdf            Page 2   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:06:38,786 - INFO -  13. ODBS2NBTU6S3GF9JVE6V.pdf            Page 1   → other           | run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:06:38,786 - INFO -  14. SMX8DOTQ89U191SMP0TO.pdf            Page 1   → bol             | run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:06:38,786 - INFO -  15. Y7P2IPB0S079FZE11EEA.jpg            Page 1   → bol             | run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:06:38,786 - INFO -  16. Z10BLMPIZ96XLUZ5NUPF.pdf            Page 1   → bol             | run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:06:38,786 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:06:38,786 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 28.300127, 'processed_files': [{'filename': 'AYEA5J1NILYPMWA7PN4V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf'}, {'filename': 'C3I3XLR18U7J6P1N2LZR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf'}, {'filename': 'C94JBR3RYYOOM5J2PFUM.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf'}, {'filename': 'DY3D94HTH1ZH420GMDO6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf'}, {'filename': 'G7K3JPMT2OXZWEI7RRNQ.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg'}, {'filename': 'GJ7Z12W1O0CQO9E9H39H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf'}, {'filename': 'N9XYNNZR54XFORX6RGH2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf'}, {'filename': 'NFG5QQXEN21TEZZ9SYPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf'}, {'filename': 'ODBS2NBTU6S3GF9JVE6V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf'}, {'filename': 'SMX8DOTQ89U191SMP0TO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf'}, {'filename': 'Y7P2IPB0S079FZE11EEA.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg'}, {'filename': 'Z10BLMPIZ96XLUZ5NUPF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf'}]}
