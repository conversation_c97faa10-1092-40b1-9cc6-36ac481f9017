{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 2.429595857833533e-09, "Y": 1.0}]}, "Id": "ac952f90-a0d5-4d5c-bbd4-e224d1f8e4fe", "Relationships": [{"Type": "CHILD", "Ids": ["9d028fad-54c6-46fb-8867-dbad047db281", "4f4cc992-2949-430f-bcf1-1fb2e37632bb", "24d1b66d-e2cc-4fa3-90ff-9ff6c870637c", "bcbe4f2b-0c79-435a-980a-e07e005461a2", "0ef639ff-eac2-4cf6-b888-f578148143c0", "a08d4225-b0d7-45c5-a0b4-2ea04ca77ff7", "a3a90439-ec24-43c5-96fe-bef12fd01b28", "8341609b-86a2-4786-89de-8eba661dd60f", "43baaada-3439-4339-8ba2-e7c639f22942", "17570d11-a1da-4671-bdbb-76ad3827bd34", "02bb7809-9ce7-4a95-a29d-0b514e609e15", "d5b34662-afb7-4eea-a7af-bd0322bc1385", "45cbe879-317c-4a83-a8ac-1c9c9f2bda8c", "1c818898-3b23-4769-be35-481434d50214", "57901349-f642-4dff-8fdb-64b9856caea4", "8a3ccdb5-a59a-4da0-8c96-0b999f5be094", "f8cf90e5-821a-464c-a924-08c93562f1ee", "5c0163af-c7a1-4d10-8889-6a1ac9cb8041", "b3c241fd-ff7a-4ca6-b67c-54b042b5127c", "be9f83fd-1d99-4590-95d0-e9628eab0662", "52cc7b4c-ce2c-4d97-8df4-c91092939c26", "6f70962f-315c-45a4-b5a4-6565bcdda13a", "12e4c0b9-69ab-402d-a492-6189e698f2c5", "11da1828-47ec-41c1-a56c-58a225f5cab5", "fd65d0f0-30a4-4c89-a24c-894ebab1fb3c", "c95e16e4-0400-4802-b5b0-bb8ada5daa2a", "fbc97231-0f1a-4b2f-830f-2a25227fd772", "fc9dc910-a201-4ce1-b9d0-6fddfb7f8ba1", "ccd81a03-d60a-409e-a497-33b10ce3c21d", "abe229fa-8ff7-49fe-bffa-ab26ac6f0c8b", "1d49ab5c-6303-417f-b794-bd6eda93911d"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Rush Logistics", "Geometry": {"BoundingBox": {"Width": 0.2437131702899933, "Height": 0.028128722682595253, "Left": 0.06390712410211563, "Top": 0.049257177859544754}, "Polygon": [{"X": 0.06390712410211563, "Y": 0.049257177859544754}, {"X": 0.3076202869415283, "Y": 0.049258943647146225}, {"X": 0.3076202869415283, "Y": 0.07738590240478516}, {"X": 0.06390728056430817, "Y": 0.07738418877124786}]}, "Id": "9d028fad-54c6-46fb-8867-dbad047db281", "Relationships": [{"Type": "CHILD", "Ids": ["530071d7-90e3-48b1-a3e7-d15640563300", "c506d1ba-0d8a-406b-b318-d032c396c4d0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Invoice", "Geometry": {"BoundingBox": {"Width": 0.1321944147348404, "Height": 0.023091847077012062, "Left": 0.6224165558815002, "Top": 0.055565763264894485}, "Polygon": [{"X": 0.6224166750907898, "Y": 0.055565763264894485}, {"X": 0.7546109557151794, "Y": 0.05556671321392059}, {"X": 0.7546107769012451, "Y": 0.0786576122045517}, {"X": 0.6224165558815002, "Y": 0.07865668088197708}]}, "Id": "4f4cc992-2949-430f-bcf1-1fb2e37632bb", "Relationships": [{"Type": "CHILD", "Ids": ["773ecb0d-205d-41de-8290-d31f12c8f28a"]}]}, {"BlockType": "LINE", "Confidence": 99.92732238769531, "Text": "PO Box 2041", "Geometry": {"BoundingBox": {"Width": 0.10497890412807465, "Height": 0.0115349180996418, "Left": 0.06362505257129669, "Top": 0.07915620505809784}, "Polygon": [{"X": 0.06362505257129669, "Y": 0.07915620505809784}, {"X": 0.16860392689704895, "Y": 0.07915694266557693}, {"X": 0.16860395669937134, "Y": 0.09069111943244934}, {"X": 0.06362511962652206, "Y": 0.09069039672613144}]}, "Id": "24d1b66d-e2cc-4fa3-90ff-9ff6c870637c", "Relationships": [{"Type": "CHILD", "Ids": ["083e022b-c13a-4949-bf02-5227983eebf0", "ef2ce1c7-d11d-48d5-86a7-e9ded5362a86", "49e58f1c-7651-4c1d-bb0c-f0357f7eadda"]}]}, {"BlockType": "LINE", "Confidence": 99.9373550415039, "Text": "Manitowoc, WI 54221-2041", "Geometry": {"BoundingBox": {"Width": 0.2257995456457138, "Height": 0.013591865077614784, "Left": 0.06344549357891083, "Top": 0.0970669612288475}, "Polygon": [{"X": 0.06344549357891083, "Y": 0.0970669612288475}, {"X": 0.28924503922462463, "Y": 0.09706851094961166}, {"X": 0.28924503922462463, "Y": 0.11065882444381714}, {"X": 0.0634455755352974, "Y": 0.11065729707479477}]}, "Id": "bcbe4f2b-0c79-435a-980a-e07e005461a2", "Relationships": [{"Type": "CHILD", "Ids": ["70b58ad2-3fc1-4adc-9c96-8c52911ce455", "f0e74a9f-f1e2-415c-ac6e-87d17cc45ae3", "9aa7c559-2e0e-4bc4-94c8-c0b550c7fcc0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date:", "Geometry": {"BoundingBox": {"Width": 0.04843584820628166, "Height": 0.012864592485129833, "Left": 0.6224931478500366, "Top": 0.11709334701299667}, "Polygon": [{"X": 0.6224932074546814, "Y": 0.11709334701299667}, {"X": 0.6709290146827698, "Y": 0.11709366738796234}, {"X": 0.6709288954734802, "Y": 0.12995792925357819}, {"X": 0.6224931478500366, "Y": 0.12995761632919312}]}, "Id": "0ef639ff-eac2-4cf6-b888-f578148143c0", "Relationships": [{"Type": "CHILD", "Ids": ["8a9f5f0a-c86f-4d3f-80c3-f9d7bab0ebcb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "9/10/2025", "Geometry": {"BoundingBox": {"Width": 0.0927022248506546, "Height": 0.013324791565537453, "Left": 0.7396032214164734, "Top": 0.11683223396539688}, "Polygon": [{"X": 0.7396033406257629, "Y": 0.11683223396539688}, {"X": 0.8323054909706116, "Y": 0.11683285981416702}, {"X": 0.8323053121566772, "Y": 0.13015702366828918}, {"X": 0.7396032214164734, "Y": 0.13015641272068024}]}, "Id": "a08d4225-b0d7-45c5-a0b4-2ea04ca77ff7", "Relationships": [{"Type": "CHILD", "Ids": ["f226cbe3-7d87-4990-a48d-bc59a07f65db"]}]}, {"BlockType": "LINE", "Confidence": 99.90560150146484, "Text": "Invoice # 80494", "Geometry": {"BoundingBox": {"Width": 0.17467136681079865, "Height": 0.01308764424175024, "Left": 0.6226204633712769, "Top": 0.14672306180000305}, "Polygon": [{"X": 0.6226205229759216, "Y": 0.14672306180000305}, {"X": 0.7972918152809143, "Y": 0.14672419428825378}, {"X": 0.7972916960716248, "Y": 0.15981070697307587}, {"X": 0.6226204633712769, "Y": 0.15980960428714752}]}, "Id": "a3a90439-ec24-43c5-96fe-bef12fd01b28", "Relationships": [{"Type": "CHILD", "Ids": ["25b946d3-4a18-4df6-898f-cfd92bf47b98", "7f2bddaa-33be-448b-ab18-a360f0721142", "d62c791b-1e9c-4438-a739-e6aa2eee138a"]}]}, {"BlockType": "LINE", "Confidence": 99.92595672607422, "Text": "Bill To:", "Geometry": {"BoundingBox": {"Width": 0.06047353520989418, "Height": 0.01141141913831234, "Left": 0.06362532079219818, "Top": 0.18177592754364014}, "Polygon": [{"X": 0.06362532079219818, "Y": 0.18177592754364014}, {"X": 0.12409880012273788, "Y": 0.18177630007266998}, {"X": 0.12409885227680206, "Y": 0.19318734109401703}, {"X": 0.06362538039684296, "Y": 0.19318696856498718}]}, "Id": "8341609b-86a2-4786-89de-8eba661dd60f", "Relationships": [{"Type": "CHILD", "Ids": ["3cf6b5a3-6644-492f-b11a-936c8f74b050", "878467ce-b775-46c6-89d9-2a5aa1556db9"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Wisconsin Aluminum Foundry", "Geometry": {"BoundingBox": {"Width": 0.24408014118671417, "Height": 0.014501109719276428, "Left": 0.10761571675539017, "Top": 0.2042175680398941}, "Polygon": [{"X": 0.10761571675539017, "Y": 0.2042175680398941}, {"X": 0.35169586539268494, "Y": 0.2042190432548523}, {"X": 0.35169583559036255, "Y": 0.21871867775917053}, {"X": 0.10761578381061554, "Y": 0.21871724724769592}]}, "Id": "43baaada-3439-4339-8ba2-e7c639f22942", "Relationships": [{"Type": "CHILD", "Ids": ["c76fd446-0efa-47ef-8f46-ad16d78ad4b6", "d9bd26b3-d6b6-44d2-9ca4-4d63ced8818c", "7f3aa551-71d1-436f-bdc6-e903bb269065"]}]}, {"BlockType": "LINE", "Confidence": 99.28472137451172, "Text": "838 S 16th Street", "Geometry": {"BoundingBox": {"Width": 0.1353078931570053, "Height": 0.01165222842246294, "Left": 0.10842309892177582, "Top": 0.2221064269542694}, "Polygon": [{"X": 0.10842309892177582, "Y": 0.2221064269542694}, {"X": 0.24373097717761993, "Y": 0.22210721671581268}, {"X": 0.24373099207878113, "Y": 0.23375865817070007}, {"X": 0.10842315107584, "Y": 0.2337578684091568}]}, "Id": "17570d11-a1da-4671-bdbb-76ad3827bd34", "Relationships": [{"Type": "CHILD", "Ids": ["11a09fdb-949b-429d-84c7-461423c3fe2e", "29bb901e-30fc-408f-bf2f-dc5014b11298", "fca77799-4f56-47b5-8aea-915f0ba82b93", "dc03b6b1-0c66-4eb9-aeac-9beb412497c6"]}]}, {"BlockType": "LINE", "Confidence": 99.76861572265625, "Text": "Manitowoc, WI 54221-0246", "Geometry": {"BoundingBox": {"Width": 0.22324690222740173, "Height": 0.013542069122195244, "Left": 0.107597716152668, "Top": 0.24008524417877197}, "Polygon": [{"X": 0.107597716152668, "Y": 0.24008524417877197}, {"X": 0.33084461092948914, "Y": 0.24008652567863464}, {"X": 0.33084461092948914, "Y": 0.25362733006477356}, {"X": 0.10759778320789337, "Y": 0.2536260485649109}]}, "Id": "02bb7809-9ce7-4a95-a29d-0b514e609e15", "Relationships": [{"Type": "CHILD", "Ids": ["fbf7da92-d5a7-4bcb-a6c5-3c16082cee91", "96344962-e703-4d7c-bbd5-0f11d014cc42", "f51ea921-19ad-4db2-b013-2a6bf680c647"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Terms", "Geometry": {"BoundingBox": {"Width": 0.049888838082551956, "Height": 0.011133874766528606, "Left": 0.8384196162223816, "Top": 0.2614395022392273}, "Polygon": [{"X": 0.8384197354316711, "Y": 0.2614395022392273}, {"X": 0.8883084058761597, "Y": 0.26143980026245117}, {"X": 0.8883082866668701, "Y": 0.2725733816623688}, {"X": 0.8384196162223816, "Y": 0.2725731134414673}]}, "Id": "d5b34662-afb7-4eea-a7af-bd0322bc1385", "Relationships": [{"Type": "CHILD", "Ids": ["cc234d26-3f34-48e0-bc14-8dc0f047a214"]}]}, {"BlockType": "LINE", "Confidence": 99.84375, "Text": "Net 30", "Geometry": {"BoundingBox": {"Width": 0.05243139714002609, "Height": 0.01122019998729229, "Left": 0.8371315002441406, "Top": 0.2868618667125702}, "Polygon": [{"X": 0.8371316194534302, "Y": 0.2868618667125702}, {"X": 0.8895628452301025, "Y": 0.28686216473579407}, {"X": 0.889562726020813, "Y": 0.2980820834636688}, {"X": 0.8371315002441406, "Y": 0.29808181524276733}]}, "Id": "45cbe879-317c-4a83-a8ac-1c9c9f2bda8c", "Relationships": [{"Type": "CHILD", "Ids": ["63e27e71-67ec-4561-9436-230116de6df7", "18c23dbc-a552-46fd-a32a-8118f487a8c1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.06850722432136536, "Height": 0.014069943688809872, "Left": 0.10263389348983765, "Top": 0.3152766227722168}, "Polygon": [{"X": 0.10263389348983765, "Y": 0.3152766227722168}, {"X": 0.17114107310771942, "Y": 0.31527698040008545}, {"X": 0.171141117811203, "Y": 0.32934656739234924}, {"X": 0.10263396054506302, "Y": 0.329346239566803}]}, "Id": "1c818898-3b23-4769-be35-481434d50214", "Relationships": [{"Type": "CHILD", "Ids": ["5c7659e3-fc6f-4b7d-bcee-654248c566b1"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.09187772870063782, "Height": 0.014621327631175518, "Left": 0.37604278326034546, "Top": 0.3152211606502533}, "Polygon": [{"X": 0.37604281306266785, "Y": 0.3152211606502533}, {"X": 0.4679205119609833, "Y": 0.3152216076850891}, {"X": 0.4679204821586609, "Y": 0.3298424780368805}, {"X": 0.37604278326034546, "Y": 0.3298420011997223}]}, "Id": "57901349-f642-4dff-8fdb-64b9856caea4", "Relationships": [{"Type": "CHILD", "Ids": ["07aeeede-61b6-4cfe-a8b7-e076ce829fe3"]}]}, {"BlockType": "LINE", "Confidence": 99.80797576904297, "Text": "Service Date(s)", "Geometry": {"BoundingBox": {"Width": 0.11958684027194977, "Height": 0.014226341620087624, "Left": 0.6473989486694336, "Top": 0.3152162730693817}, "Polygon": [{"X": 0.6473990678787231, "Y": 0.3152162730693817}, {"X": 0.7669858336448669, "Y": 0.31521689891815186}, {"X": 0.7669856548309326, "Y": 0.3294426202774048}, {"X": 0.6473989486694336, "Y": 0.32944202423095703}]}, "Id": "8a3ccdb5-a59a-4da0-8c96-0b999f5be094", "Relationships": [{"Type": "CHILD", "Ids": ["248bf508-b2b7-4e89-87db-f591d8541a9a", "f8c7aa34-f4e9-446f-8735-0295784a8b4d"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.05213546007871628, "Height": 0.009195451624691486, "Left": 0.8372529745101929, "Top": 0.3162197470664978}, "Polygon": [{"X": 0.8372530937194824, "Y": 0.3162197470664978}, {"X": 0.8893884420394897, "Y": 0.3162200152873993}, {"X": 0.8893883228302002, "Y": 0.3254151940345764}, {"X": 0.8372529745101929, "Y": 0.3254149258136749}]}, "Id": "f8cf90e5-821a-464c-a924-08c93562f1ee", "Relationships": [{"Type": "CHILD", "Ids": ["84151c6a-164a-456f-ae81-b4ccfaee0a3f"]}]}, {"BlockType": "LINE", "Confidence": 98.66789245605469, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004502392373979092, "Height": 0.008554420433938503, "Left": 0.203502357006073, "Top": 0.33984750509262085}, "Polygon": [{"X": 0.203502357006073, "Y": 0.33984750509262085}, {"X": 0.20800472795963287, "Y": 0.33984753489494324}, {"X": 0.20800475776195526, "Y": 0.3484019339084625}, {"X": 0.20350238680839539, "Y": 0.34840190410614014}]}, "Id": "5c0163af-c7a1-4d10-8889-6a1ac9cb8041", "Relationships": [{"Type": "CHILD", "Ids": ["65b9f867-2bda-42d8-a4a9-9d1d9423b1df"]}]}, {"BlockType": "LINE", "Confidence": 99.96040344238281, "Text": "PT Solutions to Wisconsin Aluminum Foundry", "Geometry": {"BoundingBox": {"Width": 0.2792831361293793, "Height": 0.011124838143587112, "Left": 0.21913716197013855, "Top": 0.3394916355609894}, "Polygon": [{"X": 0.21913716197013855, "Y": 0.3394916355609894}, {"X": 0.4984202980995178, "Y": 0.3394930064678192}, {"X": 0.49842023849487305, "Y": 0.350616455078125}, {"X": 0.21913719177246094, "Y": 0.35061511397361755}]}, "Id": "b3c241fd-ff7a-4ca6-b67c-54b042b5127c", "Relationships": [{"Type": "CHILD", "Ids": ["b69f5c4a-f7e7-46da-9cd9-419120f99a89", "1507824c-8ae6-4ef2-9e86-cfe8adb100fc", "141af7e9-25d5-4c55-a069-0f9b983db6fe", "d34437fa-4042-4169-93dd-875ee38c35e1", "53b6c31d-c75a-4a05-b992-5085385310cb", "51d2b073-d15b-441b-a797-17fc6e90cf5b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "9/10/2025", "Geometry": {"BoundingBox": {"Width": 0.0600881353020668, "Height": 0.0091851269826293, "Left": 0.6342009902000427, "Top": 0.33947068452835083}, "Polygon": [{"X": 0.6342010498046875, "Y": 0.33947068452835083}, {"X": 0.6942890882492065, "Y": 0.3394709825515747}, {"X": 0.6942890286445618, "Y": 0.3486558198928833}, {"X": 0.6342009902000427, "Y": 0.3486555218696594}]}, "Id": "be9f83fd-1d99-4590-95d0-e9628eab0662", "Relationships": [{"Type": "CHILD", "Ids": ["e9e3d66d-f766-4067-bf7c-308f1acb2f75"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "50.00", "Geometry": {"BoundingBox": {"Width": 0.032778121531009674, "Height": 0.008877232670783997, "Left": 0.8943625688552856, "Top": 0.3397272825241089}, "Polygon": [{"X": 0.8943626880645752, "Y": 0.3397272825241089}, {"X": 0.9271406531333923, "Y": 0.3397274315357208}, {"X": 0.9271405339241028, "Y": 0.3486045002937317}, {"X": 0.8943625688552856, "Y": 0.34860435128211975}]}, "Id": "52cc7b4c-ce2c-4d97-8df4-c91092939c26", "Relationships": [{"Type": "CHILD", "Ids": ["8f75a4b9-f069-46cd-ba36-93916cb3a163"]}]}, {"BlockType": "LINE", "Confidence": 98.6670913696289, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004544499330222607, "Height": 0.008379020728170872, "Left": 0.20341965556144714, "Top": 0.35320529341697693}, "Polygon": [{"X": 0.20341965556144714, "Y": 0.35320529341697693}, {"X": 0.20796413719654083, "Y": 0.3532053232192993}, {"X": 0.20796415209770203, "Y": 0.36158430576324463}, {"X": 0.20341967046260834, "Y": 0.36158427596092224}]}, "Id": "6f70962f-315c-45a4-b5a4-6565bcdda13a", "Relationships": [{"Type": "CHILD", "Ids": ["a5aca0a6-3a12-40dc-9fd7-7dcddecf36d8"]}]}, {"BlockType": "LINE", "Confidence": 99.97315216064453, "Text": "load from Wisconsin Aluminum Foundry to Stecker", "Geometry": {"BoundingBox": {"Width": 0.30716243386268616, "Height": 0.01113966852426529, "Left": 0.21926525235176086, "Top": 0.35283157229423523}, "Polygon": [{"X": 0.21926525235176086, "Y": 0.35283157229423523}, {"X": 0.526427686214447, "Y": 0.3528330624103546}, {"X": 0.5264276266098022, "Y": 0.3639712631702423}, {"X": 0.21926528215408325, "Y": 0.3639698028564453}]}, "Id": "12e4c0b9-69ab-402d-a492-6189e698f2c5", "Relationships": [{"Type": "CHILD", "Ids": ["ae1203f7-7804-42ad-8b81-681cec83c93c", "ece8f185-9ecf-40ba-a733-38fd0f83d306", "02199c6d-726d-4934-b4de-82b415f88ee8", "84ad60ec-062d-4ed6-b414-3a317a334694", "afde1440-bf72-4702-99db-fd2c4f265df9", "a5134070-0f2c-4460-926f-fb7be286fb3f", "5347601c-e976-45ac-a5ac-332fc673c374"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "9/10/2025", "Geometry": {"BoundingBox": {"Width": 0.060070522129535675, "Height": 0.009245275519788265, "Left": 0.6342395544052124, "Top": 0.35291606187820435}, "Polygon": [{"X": 0.6342396140098572, "Y": 0.35291606187820435}, {"X": 0.6943100690841675, "Y": 0.35291633009910583}, {"X": 0.6943100094795227, "Y": 0.3621613383293152}, {"X": 0.6342395544052124, "Y": 0.3621610403060913}]}, "Id": "11da1828-47ec-41c1-a56c-58a225f5cab5", "Relationships": [{"Type": "CHILD", "Ids": ["7318c7aa-9c5a-40b3-aa2e-7de0196f9a5b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "125.00", "Geometry": {"BoundingBox": {"Width": 0.03972916677594185, "Height": 0.00911482609808445, "Left": 0.8877460956573486, "Top": 0.352885365486145}, "Polygon": [{"X": 0.8877462148666382, "Y": 0.352885365486145}, {"X": 0.9274752736091614, "Y": 0.35288554430007935}, {"X": 0.9274751543998718, "Y": 0.3620001971721649}, {"X": 0.8877460956573486, "Y": 0.3619999885559082}]}, "Id": "fd65d0f0-30a4-4c89-a24c-894ebab1fb3c", "Relationships": [{"Type": "CHILD", "Ids": ["d66b4f91-63b7-4107-8d1a-eb8219867e48"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total:", "Geometry": {"BoundingBox": {"Width": 0.0455128476023674, "Height": 0.010990474373102188, "Left": 0.6518933773040771, "Top": 0.7359299063682556}, "Polygon": [{"X": 0.6518934369087219, "Y": 0.7359299063682556}, {"X": 0.6974061727523804, "Y": 0.7359299659729004}, {"X": 0.6974061131477356, "Y": 0.7469203472137451}, {"X": 0.6518933773040771, "Y": 0.7469202876091003}]}, "Id": "c95e16e4-0400-4802-b5b0-bb8ada5daa2a", "Relationships": [{"Type": "CHILD", "Ids": ["34250e05-0d95-44a7-968a-6c11d8cef37e"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "$175.00", "Geometry": {"BoundingBox": {"Width": 0.04787103459239006, "Height": 0.009521720930933952, "Left": 0.887804388999939, "Top": 0.7365929484367371}, "Polygon": [{"X": 0.8878045082092285, "Y": 0.7365929484367371}, {"X": 0.9356754422187805, "Y": 0.7365930676460266}, {"X": 0.9356752634048462, "Y": 0.7461146712303162}, {"X": 0.887804388999939, "Y": 0.7461146116256714}]}, "Id": "fbc97231-0f1a-4b2f-830f-2a25227fd772", "Relationships": [{"Type": "CHILD", "Ids": ["09e161e8-7c3c-4861-b590-9c2df0f03656"]}]}, {"BlockType": "LINE", "Confidence": 99.83383178710938, "Text": "Remit payment to Rush Logistics, PO Box 2041 Manitowoc, WI 54221-2041. Please", "Geometry": {"BoundingBox": {"Width": 0.508660078048706, "Height": 0.011383787728846073, "Left": 0.24514393508434296, "Top": 0.8182295560836792}, "Polygon": [{"X": 0.24514393508434296, "Y": 0.8182295560836792}, {"X": 0.7538039684295654, "Y": 0.818230152130127}, {"X": 0.7538039088249207, "Y": 0.8296133279800415}, {"X": 0.24514394998550415, "Y": 0.8296128511428833}]}, "Id": "fc9dc910-a201-4ce1-b9d0-6fddfb7f8ba1", "Relationships": [{"Type": "CHILD", "Ids": ["c887cc42-a422-42a5-b3b5-77d48f9188ac", "200b5d03-09ca-4381-b821-811ecc9de053", "fb01a3b2-0e9f-4190-a127-fb7ac4c38caf", "19cfd0e0-2f6d-4194-a8a8-3714d188f7c3", "beb11027-ccc8-43c5-8bd9-ba93a6ff6b3a", "0a782cb9-9483-4359-91e6-77cecceb46bb", "93d87391-da8f-4636-b855-230573a6122a", "474b41b1-94a0-4c7d-a0e6-2adec0e31363", "ba49fb66-31db-46e5-8a28-4a43f3083eb6", "6c60a7e5-115e-4c5d-9580-12efcc91df55", "580030ef-0a53-40d7-81fa-8b67c4367643", "eb064131-960b-4a78-88a0-ae07e89b2e16"]}]}, {"BlockType": "LINE", "Confidence": 99.53300476074219, "Text": "contact our office with any questions regarding this invoice at 920-686-7797 or e-mail <EMAIL>.", "Geometry": {"BoundingBox": {"Width": 0.6992630958557129, "Height": 0.011849149130284786, "Left": 0.1489267349243164, "Top": 0.8314335346221924}, "Polygon": [{"X": 0.1489267349243164, "Y": 0.8314335346221924}, {"X": 0.8481898307800293, "Y": 0.8314342498779297}, {"X": 0.8481897115707397, "Y": 0.8432826995849609}, {"X": 0.1489267796278, "Y": 0.8432820439338684}]}, "Id": "ccd81a03-d60a-409e-a497-33b10ce3c21d", "Relationships": [{"Type": "CHILD", "Ids": ["a653accf-fd93-46dc-85f4-7c3ba4fdbac5", "c360084b-efa1-4bcb-ab01-f8735c0467b6", "27144268-548d-4c95-bf69-87d72ff2d0bb", "c54d95cf-9bf3-4d22-8c6f-48c0c0170464", "ae44c170-cbe8-43cd-9c0f-d6caae48060b", "380da5dd-6408-4889-a4e0-e8c7d04b6dea", "2174b987-2c93-45a8-918e-225ca00ab31d", "92bb5e05-a613-4e93-bb66-c6deba2b9982", "b1f550fc-203b-4835-9f77-ec09380646c9", "0127ff0f-ace8-47e7-a37d-8e762f486e60", "d7e20a78-b546-43f4-8e09-7c71ecf51ced", "46de2088-bcdb-46b1-a823-2299fd810744", "79697c44-e6c7-4d46-bcda-059e966e0add", "6fa4532f-bc52-413e-bcee-b7bfaab45509"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Rush Logistics", "Geometry": {"BoundingBox": {"Width": 0.182720348238945, "Height": 0.021204356104135513, "Left": 0.40850830078125, "Top": 0.867041289806366}, "Polygon": [{"X": 0.4085083305835724, "Y": 0.867041289806366}, {"X": 0.5912286639213562, "Y": 0.8670414090156555}, {"X": 0.5912285447120667, "Y": 0.8882456421852112}, {"X": 0.40850830078125, "Y": 0.8882455229759216}]}, "Id": "abe229fa-8ff7-49fe-bffa-ab26ac6f0c8b", "Relationships": [{"Type": "CHILD", "Ids": ["e822adb7-843d-4725-a781-4ae61efd83d3", "612ef28c-9c77-41cd-95d8-b8bcec9f241a"]}]}, {"BlockType": "LINE", "Confidence": 99.9502182006836, "Text": "Thank you for your business!", "Geometry": {"BoundingBox": {"Width": 0.22763118147850037, "Height": 0.014970229007303715, "Left": 0.384990394115448, "Top": 0.89393550157547}, "Polygon": [{"X": 0.3849904239177704, "Y": 0.89393550157547}, {"X": 0.6126216053962708, "Y": 0.8939356207847595}, {"X": 0.6126214861869812, "Y": 0.9089057445526123}, {"X": 0.384990394115448, "Y": 0.9089056253433228}]}, "Id": "1d49ab5c-6303-417f-b794-bd6eda93911d", "Relationships": [{"Type": "CHILD", "Ids": ["262b5238-1c8a-4c16-889a-a5c145f16e51", "642797f8-ef3a-4175-8be8-04764f9510d9", "3213ec89-6f90-443f-9a91-43b1b51fe341", "9ef495b9-9a20-4ef9-aa21-2fde5f76458a", "e3a5c834-0fa5-4cd0-8f4a-d664537fde0a"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08567474037408829, "Height": 0.02157900109887123, "Left": 0.06390712410211563, "Top": 0.04946206882596016}, "Polygon": [{"X": 0.06390712410211563, "Y": 0.04946206882596016}, {"X": 0.14958178997039795, "Y": 0.04946269094944}, {"X": 0.14958186447620392, "Y": 0.07104106992483139}, {"X": 0.06390725076198578, "Y": 0.07104046642780304}]}, "Id": "530071d7-90e3-48b1-a3e7-d15640563300"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14753589034080505, "Height": 0.02812802419066429, "Left": 0.16008441150188446, "Top": 0.049257874488830566}, "Polygon": [{"X": 0.16008441150188446, "Y": 0.049257874488830566}, {"X": 0.3076202869415283, "Y": 0.049258943647146225}, {"X": 0.3076202869415283, "Y": 0.07738590240478516}, {"X": 0.16008451581001282, "Y": 0.07738485932350159}]}, "Id": "c506d1ba-0d8a-406b-b318-d032c396c4d0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1321944147348404, "Height": 0.023091847077012062, "Left": 0.6224165558815002, "Top": 0.055565763264894485}, "Polygon": [{"X": 0.6224166750907898, "Y": 0.055565763264894485}, {"X": 0.7546109557151794, "Y": 0.05556671321392059}, {"X": 0.7546107769012451, "Y": 0.0786576122045517}, {"X": 0.6224165558815002, "Y": 0.07865668088197708}]}, "Id": "773ecb0d-205d-41de-8290-d31f12c8f28a"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024742741137742996, "Height": 0.011372053064405918, "Left": 0.06362505257129669, "Top": 0.07931851595640182}, "Polygon": [{"X": 0.06362505257129669, "Y": 0.07931851595640182}, {"X": 0.08836773782968521, "Y": 0.07931868731975555}, {"X": 0.08836779743432999, "Y": 0.09069056808948517}, {"X": 0.06362511962652206, "Y": 0.09069039672613144}]}, "Id": "083e022b-c13a-4949-bf02-5227983eebf0"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03248305991292, "Height": 0.01132878940552473, "Left": 0.09372066706418991, "Top": 0.07923697680234909}, "Polygon": [{"X": 0.09372066706418991, "Y": 0.07923697680234909}, {"X": 0.12620368599891663, "Y": 0.079237200319767}, {"X": 0.1262037307024002, "Y": 0.0905657634139061}, {"X": 0.09372072666883469, "Y": 0.09056553989648819}]}, "Id": "ef2ce1c7-d11d-48d5-86a7-e9ded5362a86"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2041", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03747051581740379, "Height": 0.011521103791892529, "Left": 0.13113343715667725, "Top": 0.07915667444467545}, "Polygon": [{"X": 0.13113343715667725, "Y": 0.07915667444467545}, {"X": 0.16860392689704895, "Y": 0.07915694266557693}, {"X": 0.16860395669937134, "Y": 0.09067778289318085}, {"X": 0.13113349676132202, "Y": 0.09067752212285995}]}, "Id": "49e58f1c-7651-4c1d-bb0c-f0357f7eadda"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Manitowoc,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09355318546295166, "Height": 0.0135688791051507, "Left": 0.06344549357891083, "Top": 0.09708905220031738}, "Polygon": [{"X": 0.06344549357891083, "Y": 0.09708905220031738}, {"X": 0.1569986343383789, "Y": 0.09708969295024872}, {"X": 0.1569986790418625, "Y": 0.11065793037414551}, {"X": 0.0634455755352974, "Y": 0.11065729707479477}]}, "Id": "70b58ad2-3fc1-4adc-9c96-8c52911ce455"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025910155847668648, "Height": 0.011506874114274979, "Left": 0.16217248141765594, "Top": 0.09707570821046829}, "Polygon": [{"X": 0.16217248141765594, "Y": 0.09707570821046829}, {"X": 0.18808260560035706, "Y": 0.09707588702440262}, {"X": 0.18808265030384064, "Y": 0.10858257859945297}, {"X": 0.16217252612113953, "Y": 0.10858240723609924}]}, "Id": "f0e74a9f-f1e2-415c-ac6e-87d17cc45ae3"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "54221-2041", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09203576296567917, "Height": 0.011474826373159885, "Left": 0.19720928370952606, "Top": 0.09706787765026093}, "Polygon": [{"X": 0.19720928370952606, "Y": 0.09706787765026093}, {"X": 0.28924503922462463, "Y": 0.09706851094961166}, {"X": 0.28924503922462463, "Y": 0.10854270309209824}, {"X": 0.19720931351184845, "Y": 0.10854208469390869}]}, "Id": "9aa7c559-2e0e-4bc4-94c8-c0b550c7fcc0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04843584820628166, "Height": 0.012864592485129833, "Left": 0.6224931478500366, "Top": 0.11709334701299667}, "Polygon": [{"X": 0.6224932074546814, "Y": 0.11709334701299667}, {"X": 0.6709290146827698, "Y": 0.11709366738796234}, {"X": 0.6709288954734802, "Y": 0.12995792925357819}, {"X": 0.6224931478500366, "Y": 0.12995761632919312}]}, "Id": "8a9f5f0a-c86f-4d3f-80c3-f9d7bab0ebcb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "9/10/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0927022248506546, "Height": 0.013324791565537453, "Left": 0.7396032214164734, "Top": 0.11683223396539688}, "Polygon": [{"X": 0.7396033406257629, "Y": 0.11683223396539688}, {"X": 0.8323054909706116, "Y": 0.11683285981416702}, {"X": 0.8323053121566772, "Y": 0.13015702366828918}, {"X": 0.7396032214164734, "Y": 0.13015641272068024}]}, "Id": "f226cbe3-7d87-4990-a48d-bc59a07f65db"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06878694891929626, "Height": 0.013014760799705982, "Left": 0.6226204633712769, "Top": 0.14672306180000305}, "Polygon": [{"X": 0.6226205229759216, "Y": 0.14672306180000305}, {"X": 0.6914073824882507, "Y": 0.14672350883483887}, {"X": 0.6914072632789612, "Y": 0.15973782539367676}, {"X": 0.6226204633712769, "Y": 0.15973739326000214}]}, "Id": "25b946d3-4a18-4df6-898f-cfd92bf47b98"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012237037532031536, "Height": 0.012704947963356972, "Left": 0.6968389749526978, "Top": 0.14689576625823975}, "Polygon": [{"X": 0.6968390345573425, "Y": 0.14689576625823975}, {"X": 0.7090759873390198, "Y": 0.14689584076404572}, {"X": 0.7090758681297302, "Y": 0.15960071980953217}, {"X": 0.6968389749526978, "Y": 0.159600630402565}]}, "Id": "7f2bddaa-33be-448b-ab18-a360f0721142"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "80494", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05690103396773338, "Height": 0.01306363008916378, "Left": 0.7403907775878906, "Top": 0.14674708247184753}, "Polygon": [{"X": 0.7403908967971802, "Y": 0.14674708247184753}, {"X": 0.7972918152809143, "Y": 0.14674745500087738}, {"X": 0.7972916960716248, "Y": 0.15981070697307587}, {"X": 0.7403907775878906, "Y": 0.15981034934520721}]}, "Id": "d62c791b-1e9c-4438-a739-e6aa2eee138a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02898159809410572, "Height": 0.01138651929795742, "Left": 0.06362532079219818, "Top": 0.18177592754364014}, "Polygon": [{"X": 0.06362532079219818, "Y": 0.18177592754364014}, {"X": 0.09260685741901398, "Y": 0.18177610635757446}, {"X": 0.09260691702365875, "Y": 0.1931624412536621}, {"X": 0.06362538039684296, "Y": 0.19316226243972778}]}, "Id": "3cf6b5a3-6644-492f-b11a-936c8f74b050"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026539767161011696, "Height": 0.01125596184283495, "Left": 0.09755908697843552, "Top": 0.18193137645721436}, "Polygon": [{"X": 0.09755908697843552, "Y": 0.18193137645721436}, {"X": 0.12409880012273788, "Y": 0.1819315403699875}, {"X": 0.12409885227680206, "Y": 0.19318734109401703}, {"X": 0.0975591391324997, "Y": 0.1931871771812439}]}, "Id": "878467ce-b775-46c6-89d9-2a5aa1556db9"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Wisconsin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0828835591673851, "Height": 0.011330096051096916, "Left": 0.10761571675539017, "Top": 0.20424604415893555}, "Polygon": [{"X": 0.10761571675539017, "Y": 0.20424604415893555}, {"X": 0.19049924612045288, "Y": 0.20424655079841614}, {"X": 0.19049927592277527, "Y": 0.2155761420726776}, {"X": 0.10761576890945435, "Y": 0.21557565033435822}]}, "Id": "c76fd446-0efa-47ef-8f46-ad16d78ad4b6"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Aluminum", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08542655408382416, "Height": 0.011407279409468174, "Left": 0.19499742984771729, "Top": 0.20421810448169708}, "Polygon": [{"X": 0.19499742984771729, "Y": 0.20421810448169708}, {"X": 0.28042396903038025, "Y": 0.20421861112117767}, {"X": 0.28042396903038025, "Y": 0.21562537550926208}, {"X": 0.19499745965003967, "Y": 0.2156248688697815}]}, "Id": "d9bd26b3-d6b6-44d2-9ca4-4d63ced8818c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Foundry", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06675761938095093, "Height": 0.014293793588876724, "Left": 0.284938246011734, "Top": 0.2044248878955841}, "Polygon": [{"X": 0.284938246011734, "Y": 0.2044248878955841}, {"X": 0.35169586539268494, "Y": 0.20442529022693634}, {"X": 0.35169583559036255, "Y": 0.21871867775917053}, {"X": 0.284938246011734, "Y": 0.2187182903289795}]}, "Id": "7f3aa551-71d1-436f-bdc6-e903bb269065"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "838", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028229819610714912, "Height": 0.011525141075253487, "Left": 0.10842309892177582, "Top": 0.2222328931093216}, "Polygon": [{"X": 0.10842309892177582, "Y": 0.2222328931093216}, {"X": 0.136652871966362, "Y": 0.22223305702209473}, {"X": 0.13665291666984558, "Y": 0.23375803232192993}, {"X": 0.10842315107584, "Y": 0.2337578684091568}]}, "Id": "11a09fdb-949b-429d-84c7-461423c3fe2e"}, {"BlockType": "WORD", "Confidence": 97.38301086425781, "Text": "S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009780270047485828, "Height": 0.010849968530237675, "Left": 0.14265358448028564, "Top": 0.2225067913532257}, "Polygon": [{"X": 0.14265358448028564, "Y": 0.2225067913532257}, {"X": 0.1524338275194168, "Y": 0.22250685095787048}, {"X": 0.1524338573217392, "Y": 0.2333567589521408}, {"X": 0.14265362918376923, "Y": 0.23335669934749603}]}, "Id": "29bb901e-30fc-408f-bf2f-dc5014b11298"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "16th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03295043855905533, "Height": 0.011528602801263332, "Left": 0.15981115400791168, "Top": 0.2221067249774933}, "Polygon": [{"X": 0.15981115400791168, "Y": 0.2221067249774933}, {"X": 0.19276155531406403, "Y": 0.2221069186925888}, {"X": 0.1927615851163864, "Y": 0.2336353361606598}, {"X": 0.15981119871139526, "Y": 0.23363514244556427}]}, "Id": "fca77799-4f56-47b5-8aea-915f0ba82b93"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04551323130726814, "Height": 0.011047504842281342, "Left": 0.19821776449680328, "Top": 0.2223939150571823}, "Polygon": [{"X": 0.19821776449680328, "Y": 0.2223939150571823}, {"X": 0.24373097717761993, "Y": 0.2223941832780838}, {"X": 0.24373099207878113, "Y": 0.23344142735004425}, {"X": 0.19821779429912567, "Y": 0.23344115912914276}]}, "Id": "dc03b6b1-0c66-4eb9-aeac-9beb412497c6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Manitowoc,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09358149021863937, "Height": 0.013449857011437416, "Left": 0.107597716152668, "Top": 0.2401767373085022}, "Polygon": [{"X": 0.107597716152668, "Y": 0.2401767373085022}, {"X": 0.20117917656898499, "Y": 0.24017727375030518}, {"X": 0.20117920637130737, "Y": 0.25362658500671387}, {"X": 0.10759778320789337, "Y": 0.2536260485649109}]}, "Id": "fbf7da92-d5a7-4bcb-a6c5-3c16082cee91"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025286220014095306, "Height": 0.011189454235136509, "Left": 0.20653672516345978, "Top": 0.24028854072093964}, "Polygon": [{"X": 0.20653672516345978, "Y": 0.24028854072093964}, {"X": 0.2318229228258133, "Y": 0.24028868973255157}, {"X": 0.2318229377269745, "Y": 0.251477986574173}, {"X": 0.20653675496578217, "Y": 0.2514778673648834}]}, "Id": "96344962-e703-4d7c-bbd5-0f11d014cc42"}, {"BlockType": "WORD", "Confidence": 99.609375, "Text": "54221-0246", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09447454661130905, "Height": 0.01169182825833559, "Left": 0.23637007176876068, "Top": 0.24008598923683167}, "Polygon": [{"X": 0.23637007176876068, "Y": 0.24008598923683167}, {"X": 0.33084461092948914, "Y": 0.24008652567863464}, {"X": 0.33084461092948914, "Y": 0.2517778277397156}, {"X": 0.23637008666992188, "Y": 0.2517772912979126}]}, "Id": "f51ea921-19ad-4db2-b013-2a6bf680c647"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Terms", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049888838082551956, "Height": 0.011133874766528606, "Left": 0.8384196162223816, "Top": 0.2614395022392273}, "Polygon": [{"X": 0.8384197354316711, "Y": 0.2614395022392273}, {"X": 0.8883084058761597, "Y": 0.26143980026245117}, {"X": 0.8883082866668701, "Y": 0.2725733816623688}, {"X": 0.8384196162223816, "Y": 0.2725731134414673}]}, "Id": "cc234d26-3f34-48e0-bc14-8dc0f047a214"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029170846566557884, "Height": 0.01085032057017088, "Left": 0.8371315002441406, "Top": 0.2870331108570099}, "Polygon": [{"X": 0.8371316194534302, "Y": 0.2870331108570099}, {"X": 0.8663023114204407, "Y": 0.2870332896709442}, {"X": 0.8663021922111511, "Y": 0.29788345098495483}, {"X": 0.8371315002441406, "Y": 0.2978833019733429}]}, "Id": "63e27e71-67ec-4561-9436-230116de6df7"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01884155347943306, "Height": 0.011220019310712814, "Left": 0.8707213401794434, "Top": 0.2868620753288269}, "Polygon": [{"X": 0.8707214593887329, "Y": 0.2868620753288269}, {"X": 0.8895628452301025, "Y": 0.28686216473579407}, {"X": 0.889562726020813, "Y": 0.2980820834636688}, {"X": 0.8707213401794434, "Y": 0.29808199405670166}]}, "Id": "18c23dbc-a552-46fd-a32a-8118f487a8c1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06850722432136536, "Height": 0.014069943688809872, "Left": 0.10263389348983765, "Top": 0.3152766227722168}, "Polygon": [{"X": 0.10263389348983765, "Y": 0.3152766227722168}, {"X": 0.17114107310771942, "Y": 0.31527698040008545}, {"X": 0.171141117811203, "Y": 0.32934656739234924}, {"X": 0.10263396054506302, "Y": 0.329346239566803}]}, "Id": "5c7659e3-fc6f-4b7d-bcee-654248c566b1"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09187772870063782, "Height": 0.014621327631175518, "Left": 0.37604278326034546, "Top": 0.3152211606502533}, "Polygon": [{"X": 0.37604281306266785, "Y": 0.3152211606502533}, {"X": 0.4679205119609833, "Y": 0.3152216076850891}, {"X": 0.4679204821586609, "Y": 0.3298424780368805}, {"X": 0.37604278326034546, "Y": 0.3298420011997223}]}, "Id": "07aeeede-61b6-4cfe-a8b7-e076ce829fe3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05788329243659973, "Height": 0.011226649396121502, "Left": 0.6473990082740784, "Top": 0.3152434527873993}, "Polygon": [{"X": 0.6473990678787231, "Y": 0.3152434527873993}, {"X": 0.7052822709083557, "Y": 0.31524375081062317}, {"X": 0.7052822113037109, "Y": 0.3264700770378113}, {"X": 0.6473990082740784, "Y": 0.3264698088169098}]}, "Id": "248bf508-b2b7-4e89-87db-f591d8541a9a"}, {"BlockType": "WORD", "Confidence": 99.64524841308594, "Text": "Date(s)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057007383555173874, "Height": 0.014226020313799381, "Left": 0.7099784016609192, "Top": 0.315216600894928}, "Polygon": [{"X": 0.7099785208702087, "Y": 0.315216600894928}, {"X": 0.7669858336448669, "Y": 0.31521689891815186}, {"X": 0.7669856548309326, "Y": 0.3294426202774048}, {"X": 0.7099784016609192, "Y": 0.3294423222541809}]}, "Id": "f8c7aa34-f4e9-446f-8735-0295784a8b4d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05213546007871628, "Height": 0.009195451624691486, "Left": 0.8372529745101929, "Top": 0.3162197470664978}, "Polygon": [{"X": 0.8372530937194824, "Y": 0.3162197470664978}, {"X": 0.8893884420394897, "Y": 0.3162200152873993}, {"X": 0.8893883228302002, "Y": 0.3254151940345764}, {"X": 0.8372529745101929, "Y": 0.3254149258136749}]}, "Id": "84151c6a-164a-456f-ae81-b4ccfaee0a3f"}, {"BlockType": "WORD", "Confidence": 98.66789245605469, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004502392373979092, "Height": 0.008554420433938503, "Left": 0.203502357006073, "Top": 0.33984750509262085}, "Polygon": [{"X": 0.203502357006073, "Y": 0.33984750509262085}, {"X": 0.20800472795963287, "Y": 0.33984753489494324}, {"X": 0.20800475776195526, "Y": 0.3484019339084625}, {"X": 0.20350238680839539, "Y": 0.34840190410614014}]}, "Id": "65b9f867-2bda-42d8-a4a9-9d1d9423b1df"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01775801181793213, "Height": 0.008810431696474552, "Left": 0.21913716197013855, "Top": 0.3396839499473572}, "Polygon": [{"X": 0.21913716197013855, "Y": 0.3396839499473572}, {"X": 0.23689515888690948, "Y": 0.33968403935432434}, {"X": 0.23689517378807068, "Y": 0.34849438071250916}, {"X": 0.21913719177246094, "Y": 0.348494291305542}]}, "Id": "b69f5c4a-f7e7-46da-9cd9-419120f99a89"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Solutions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05589953064918518, "Height": 0.009035466238856316, "Left": 0.24040257930755615, "Top": 0.33949172496795654}, "Polygon": [{"X": 0.24040257930755615, "Y": 0.33949172496795654}, {"X": 0.29630210995674133, "Y": 0.3394920229911804}, {"X": 0.29630210995674133, "Y": 0.348527193069458}, {"X": 0.24040259420871735, "Y": 0.3485269248485565}]}, "Id": "1507824c-8ae6-4ef2-9e86-cfe8adb100fc"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011645566672086716, "Height": 0.007300637196749449, "Left": 0.299785852432251, "Top": 0.34105217456817627}, "Polygon": [{"X": 0.299785852432251, "Y": 0.34105217456817627}, {"X": 0.3114314377307892, "Y": 0.34105223417282104}, {"X": 0.3114314377307892, "Y": 0.3483528196811676}, {"X": 0.299785852432251, "Y": 0.3483527600765228}]}, "Id": "141af7e9-25d5-4c55-a069-0f9b983db6fe"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Wisconsin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06258770078420639, "Height": 0.008889812976121902, "Left": 0.31487637758255005, "Top": 0.3396013677120209}, "Polygon": [{"X": 0.31487637758255005, "Y": 0.3396013677120209}, {"X": 0.37746408581733704, "Y": 0.33960166573524475}, {"X": 0.37746405601501465, "Y": 0.3484911620616913}, {"X": 0.31487637758255005, "Y": 0.3484908640384674}]}, "Id": "d34437fa-4042-4169-93dd-875ee38c35e1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Aluminum", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06413617730140686, "Height": 0.008815388195216656, "Left": 0.3804420232772827, "Top": 0.33963218331336975}, "Polygon": [{"X": 0.3804420530796051, "Y": 0.33963218331336975}, {"X": 0.4445782005786896, "Y": 0.339632511138916}, {"X": 0.4445781707763672, "Y": 0.3484475612640381}, {"X": 0.3804420232772827, "Y": 0.3484472632408142}]}, "Id": "53b6c31d-c75a-4a05-b992-5085385310cb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Foundry", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05030744522809982, "Height": 0.010883817449212074, "Left": 0.4481128454208374, "Top": 0.33973264694213867}, "Polygon": [{"X": 0.4481128752231598, "Y": 0.33973264694213867}, {"X": 0.4984202980995178, "Y": 0.3397328853607178}, {"X": 0.49842023849487305, "Y": 0.350616455078125}, {"X": 0.4481128454208374, "Y": 0.3506162166595459}]}, "Id": "51d2b073-d15b-441b-a797-17fc6e90cf5b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "9/10/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0600881353020668, "Height": 0.0091851269826293, "Left": 0.6342009902000427, "Top": 0.33947068452835083}, "Polygon": [{"X": 0.6342010498046875, "Y": 0.33947068452835083}, {"X": 0.6942890882492065, "Y": 0.3394709825515747}, {"X": 0.6942890286445618, "Y": 0.3486558198928833}, {"X": 0.6342009902000427, "Y": 0.3486555218696594}]}, "Id": "e9e3d66d-f766-4067-bf7c-308f1acb2f75"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "50.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032778121531009674, "Height": 0.008877232670783997, "Left": 0.8943625688552856, "Top": 0.3397272825241089}, "Polygon": [{"X": 0.8943626880645752, "Y": 0.3397272825241089}, {"X": 0.9271406531333923, "Y": 0.3397274315357208}, {"X": 0.9271405339241028, "Y": 0.3486045002937317}, {"X": 0.8943625688552856, "Y": 0.34860435128211975}]}, "Id": "8f75a4b9-f069-46cd-ba36-93916cb3a163"}, {"BlockType": "WORD", "Confidence": 98.6670913696289, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004544499330222607, "Height": 0.008379020728170872, "Left": 0.20341965556144714, "Top": 0.35320529341697693}, "Polygon": [{"X": 0.20341965556144714, "Y": 0.35320529341697693}, {"X": 0.20796413719654083, "Y": 0.3532053232192993}, {"X": 0.20796415209770203, "Y": 0.36158430576324463}, {"X": 0.20341967046260834, "Y": 0.36158427596092224}]}, "Id": "a5aca0a6-3a12-40dc-9fd7-7dcddecf36d8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025557028129696846, "Height": 0.008718389086425304, "Left": 0.21926525235176086, "Top": 0.35283157229423523}, "Polygon": [{"X": 0.21926525235176086, "Y": 0.35283157229423523}, {"X": 0.24482226371765137, "Y": 0.3528316915035248}, {"X": 0.24482227861881256, "Y": 0.361549973487854}, {"X": 0.21926526725292206, "Y": 0.36154985427856445}]}, "Id": "ae1203f7-7804-42ad-8b81-681cec83c93c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "from", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028511999174952507, "Height": 0.008783893659710884, "Left": 0.24881668388843536, "Top": 0.3529762923717499}, "Polygon": [{"X": 0.24881668388843536, "Y": 0.3529762923717499}, {"X": 0.2773286700248718, "Y": 0.3529764413833618}, {"X": 0.2773286700248718, "Y": 0.3617601990699768}, {"X": 0.24881668388843536, "Y": 0.36176005005836487}]}, "Id": "ece8f185-9ecf-40ba-a733-38fd0f83d306"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Wisconsin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06229362636804581, "Height": 0.008881905116140842, "Left": 0.28008705377578735, "Top": 0.3528512120246887}, "Polygon": [{"X": 0.28008705377578735, "Y": 0.3528512120246887}, {"X": 0.34238067269325256, "Y": 0.3528515100479126}, {"X": 0.34238067269325256, "Y": 0.3617331087589264}, {"X": 0.28008705377578735, "Y": 0.3617328107357025}]}, "Id": "02199c6d-726d-4934-b4de-82b415f88ee8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Aluminum", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06401003152132034, "Height": 0.008672649040818214, "Left": 0.3459339439868927, "Top": 0.35295775532722473}, "Polygon": [{"X": 0.3459339439868927, "Y": 0.35295775532722473}, {"X": 0.40994396805763245, "Y": 0.352958083152771}, {"X": 0.40994396805763245, "Y": 0.3616304099559784}, {"X": 0.3459339439868927, "Y": 0.3616301119327545}]}, "Id": "84ad60ec-062d-4ed6-b414-3a317a334694"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Foundry", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05061929672956467, "Height": 0.010986690409481525, "Left": 0.41316676139831543, "Top": 0.3529842495918274}, "Polygon": [{"X": 0.4131667912006378, "Y": 0.3529842495918274}, {"X": 0.4637860655784607, "Y": 0.3529845178127289}, {"X": 0.4637860357761383, "Y": 0.36397096514701843}, {"X": 0.41316676139831543, "Y": 0.36397072672843933}]}, "Id": "afde1440-bf72-4702-99db-fd2c4f265df9"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01151647511869669, "Height": 0.007513056043535471, "Left": 0.46649497747421265, "Top": 0.3542053699493408}, "Polygon": [{"X": 0.46649497747421265, "Y": 0.3542053699493408}, {"X": 0.47801142930984497, "Y": 0.3542053997516632}, {"X": 0.47801142930984497, "Y": 0.36171841621398926}, {"X": 0.46649497747421265, "Y": 0.3617183566093445}]}, "Id": "a5134070-0f2c-4460-926f-fb7be286fb3f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04424532130360603, "Height": 0.008641650900244713, "Left": 0.4821823537349701, "Top": 0.35310161113739014}, "Polygon": [{"X": 0.4821823835372925, "Y": 0.35310161113739014}, {"X": 0.526427686214447, "Y": 0.35310181975364685}, {"X": 0.5264276266098022, "Y": 0.3617432713508606}, {"X": 0.4821823537349701, "Y": 0.3617430627346039}]}, "Id": "5347601c-e976-45ac-a5ac-332fc673c374"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "9/10/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060070522129535675, "Height": 0.009245275519788265, "Left": 0.6342395544052124, "Top": 0.35291606187820435}, "Polygon": [{"X": 0.6342396140098572, "Y": 0.35291606187820435}, {"X": 0.6943100690841675, "Y": 0.35291633009910583}, {"X": 0.6943100094795227, "Y": 0.3621613383293152}, {"X": 0.6342395544052124, "Y": 0.3621610403060913}]}, "Id": "7318c7aa-9c5a-40b3-aa2e-7de0196f9a5b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "125.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03972916677594185, "Height": 0.00911482609808445, "Left": 0.8877460956573486, "Top": 0.352885365486145}, "Polygon": [{"X": 0.8877462148666382, "Y": 0.352885365486145}, {"X": 0.9274752736091614, "Y": 0.35288554430007935}, {"X": 0.9274751543998718, "Y": 0.3620001971721649}, {"X": 0.8877460956573486, "Y": 0.3619999885559082}]}, "Id": "d66b4f91-63b7-4107-8d1a-eb8219867e48"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0455128476023674, "Height": 0.010990474373102188, "Left": 0.6518933773040771, "Top": 0.7359299063682556}, "Polygon": [{"X": 0.6518934369087219, "Y": 0.7359299063682556}, {"X": 0.6974061727523804, "Y": 0.7359299659729004}, {"X": 0.6974061131477356, "Y": 0.7469203472137451}, {"X": 0.6518933773040771, "Y": 0.7469202876091003}]}, "Id": "34250e05-0d95-44a7-968a-6c11d8cef37e"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "$175.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04787103459239006, "Height": 0.009521720930933952, "Left": 0.887804388999939, "Top": 0.7365929484367371}, "Polygon": [{"X": 0.8878045082092285, "Y": 0.7365929484367371}, {"X": 0.9356754422187805, "Y": 0.7365930676460266}, {"X": 0.9356752634048462, "Y": 0.7461146712303162}, {"X": 0.887804388999939, "Y": 0.7461146116256714}]}, "Id": "09e161e8-7c3c-4861-b590-9c2df0f03656"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Remit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03596451133489609, "Height": 0.00888094026595354, "Left": 0.24514393508434296, "Top": 0.8183161020278931}, "Polygon": [{"X": 0.24514393508434296, "Y": 0.8183161020278931}, {"X": 0.28110843896865845, "Y": 0.8183161616325378}, {"X": 0.28110843896865845, "Y": 0.8271970152854919}, {"X": 0.24514394998550415, "Y": 0.8271970152854919}]}, "Id": "c887cc42-a422-42a5-b3b5-77d48f9188ac"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050726186484098434, "Height": 0.009610110893845558, "Left": 0.28396573662757874, "Top": 0.8195189833641052}, "Polygon": [{"X": 0.28396573662757874, "Y": 0.8195189833641052}, {"X": 0.3346919119358063, "Y": 0.81951904296875}, {"X": 0.3346919119358063, "Y": 0.8291290998458862}, {"X": 0.28396573662757874, "Y": 0.8291290998458862}]}, "Id": "200b5d03-09ca-4381-b821-811ecc9de053"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011545284651219845, "Height": 0.007341914810240269, "Left": 0.33781734108924866, "Top": 0.8199107050895691}, "Polygon": [{"X": 0.33781734108924866, "Y": 0.8199107050895691}, {"X": 0.34936264157295227, "Y": 0.8199107050895691}, {"X": 0.34936264157295227, "Y": 0.8272526264190674}, {"X": 0.33781734108924866, "Y": 0.8272526264190674}]}, "Id": "fb01a3b2-0e9f-4190-a127-fb7ac4c38caf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030347229912877083, "Height": 0.00817179400473833, "Left": 0.35334762930870056, "Top": 0.8185480833053589}, "Polygon": [{"X": 0.35334765911102295, "Y": 0.8185480833053589}, {"X": 0.3836948573589325, "Y": 0.8185480833053589}, {"X": 0.3836948573589325, "Y": 0.8267198801040649}, {"X": 0.35334762930870056, "Y": 0.8267198204994202}]}, "Id": "19cfd0e0-2f6d-4194-a8a8-3714d188f7c3"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Logistics,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05697057023644447, "Height": 0.01138330902904272, "Left": 0.3873894214630127, "Top": 0.8182297348976135}, "Polygon": [{"X": 0.3873894214630127, "Y": 0.8182297348976135}, {"X": 0.44435998797416687, "Y": 0.8182297945022583}, {"X": 0.4443599581718445, "Y": 0.8296130299568176}, {"X": 0.3873894214630127, "Y": 0.8296129703521729}]}, "Id": "beb11027-ccc8-43c5-8bd9-ba93a6ff6b3a"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019093817099928856, "Height": 0.008520684204995632, "Left": 0.4481995403766632, "Top": 0.8185440897941589}, "Polygon": [{"X": 0.4481995403766632, "Y": 0.8185440897941589}, {"X": 0.4672933518886566, "Y": 0.8185441493988037}, {"X": 0.46729332208633423, "Y": 0.8270648121833801}, {"X": 0.4481995403766632, "Y": 0.8270647525787354}]}, "Id": "0a782cb9-9483-4359-91e6-77cecceb46bb"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02473784051835537, "Height": 0.009027443826198578, "Left": 0.47098827362060547, "Top": 0.818337619304657}, "Polygon": [{"X": 0.47098830342292786, "Y": 0.818337619304657}, {"X": 0.4957261085510254, "Y": 0.818337619304657}, {"X": 0.495726078748703, "Y": 0.8273650407791138}, {"X": 0.47098827362060547, "Y": 0.8273650407791138}]}, "Id": "93d87391-da8f-4636-b855-230573a6122a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2041", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028357334434986115, "Height": 0.008735847659409046, "Left": 0.49889495968818665, "Top": 0.8185027241706848}, "Polygon": [{"X": 0.49889498949050903, "Y": 0.8185027241706848}, {"X": 0.5272523164749146, "Y": 0.8185027837753296}, {"X": 0.5272522568702698, "Y": 0.8272385597229004}, {"X": 0.49889495968818665, "Y": 0.8272385597229004}]}, "Id": "474b41b1-94a0-4c7d-a0e6-2adec0e31363"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Manitowoc,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06981772929430008, "Height": 0.010114588774740696, "Left": 0.5326436161994934, "Top": 0.818399965763092}, "Polygon": [{"X": 0.5326436161994934, "Y": 0.818399965763092}, {"X": 0.6024613380432129, "Y": 0.8184000253677368}, {"X": 0.6024612784385681, "Y": 0.8285145163536072}, {"X": 0.5326436161994934, "Y": 0.8285144567489624}]}, "Id": "ba49fb66-31db-46e5-8a28-4a43f3083eb6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019119562581181526, "Height": 0.008538827300071716, "Left": 0.6069706678390503, "Top": 0.8185486197471619}, "Polygon": [{"X": 0.6069707274436951, "Y": 0.8185486197471619}, {"X": 0.6260902285575867, "Y": 0.8185486197471619}, {"X": 0.6260901689529419, "Y": 0.82708740234375}, {"X": 0.6069706678390503, "Y": 0.82708740234375}]}, "Id": "6c60a7e5-115e-4c5d-9580-12efcc91df55"}, {"BlockType": "WORD", "Confidence": 99.00909423828125, "Text": "54221-2041.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07496039569377899, "Height": 0.008720257319509983, "Left": 0.6333862543106079, "Top": 0.8182984590530396}, "Polygon": [{"X": 0.6333863139152527, "Y": 0.8182984590530396}, {"X": 0.7083466649055481, "Y": 0.8182985782623291}, {"X": 0.7083466053009033, "Y": 0.8270187377929688}, {"X": 0.6333862543106079, "Y": 0.8270186185836792}]}, "Id": "580030ef-0a53-40d7-81fa-8b67c4367643"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03742114454507828, "Height": 0.008668763563036919, "Left": 0.7163828611373901, "Top": 0.8183749318122864}, "Polygon": [{"X": 0.7163829207420349, "Y": 0.8183749318122864}, {"X": 0.7538039684295654, "Y": 0.8183749318122864}, {"X": 0.7538039088249207, "Y": 0.8270436525344849}, {"X": 0.7163828611373901, "Y": 0.8270436525344849}]}, "Id": "eb064131-960b-4a78-88a0-ae07e89b2e16"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "contact", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04285208880901337, "Height": 0.007159110624343157, "Left": 0.1489267498254776, "Top": 0.8333569765090942}, "Polygon": [{"X": 0.1489267498254776, "Y": 0.8333569765090942}, {"X": 0.19177880883216858, "Y": 0.8333569765090942}, {"X": 0.19177883863449097, "Y": 0.8405160903930664}, {"X": 0.1489267647266388, "Y": 0.8405160307884216}]}, "Id": "a653accf-fd93-46dc-85f4-7c3ba4fdbac5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02002432383596897, "Height": 0.006009379401803017, "Left": 0.19513952732086182, "Top": 0.8346947431564331}, "Polygon": [{"X": 0.19513952732086182, "Y": 0.8346947431564331}, {"X": 0.21516384184360504, "Y": 0.8346948027610779}, {"X": 0.21516385674476624, "Y": 0.8407041430473328}, {"X": 0.1951395571231842, "Y": 0.8407041430473328}]}, "Id": "c360084b-efa1-4bcb-ab01-f8735c0467b6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "office", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03411092981696129, "Height": 0.008713641203939915, "Left": 0.21863482892513275, "Top": 0.8315320611000061}, "Polygon": [{"X": 0.21863482892513275, "Y": 0.8315320611000061}, {"X": 0.25274574756622314, "Y": 0.8315320611000061}, {"X": 0.25274577736854553, "Y": 0.8402456641197205}, {"X": 0.21863485872745514, "Y": 0.8402456641197205}]}, "Id": "27144268-548d-4c95-bf69-87d72ff2d0bb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026188157498836517, "Height": 0.008805375546216965, "Left": 0.25590312480926514, "Top": 0.8315039277076721}, "Polygon": [{"X": 0.25590312480926514, "Y": 0.8315039277076721}, {"X": 0.28209125995635986, "Y": 0.8315039277076721}, {"X": 0.28209125995635986, "Y": 0.8403093218803406}, {"X": 0.25590312480926514, "Y": 0.8403092622756958}]}, "Id": "c54d95cf-9bf3-4d22-8c6f-48c0c0170464"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021182484924793243, "Height": 0.008988614194095135, "Left": 0.285796195268631, "Top": 0.8342936038970947}, "Polygon": [{"X": 0.285796195268631, "Y": 0.8342936038970947}, {"X": 0.30697867274284363, "Y": 0.8342936038970947}, {"X": 0.30697867274284363, "Y": 0.8432822227478027}, {"X": 0.285796195268631, "Y": 0.843282163143158}]}, "Id": "ae44c170-cbe8-43cd-9c0f-d6caae48060b"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "questions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05595194548368454, "Height": 0.010981723666191101, "Left": 0.3102107048034668, "Top": 0.8317986726760864}, "Polygon": [{"X": 0.3102107048034668, "Y": 0.8317986726760864}, {"X": 0.36616265773773193, "Y": 0.8317987322807312}, {"X": 0.36616262793540955, "Y": 0.8427804112434387}, {"X": 0.3102107048034668, "Y": 0.842780351638794}]}, "Id": "380da5dd-6408-4889-a4e0-e8c7d04b6dea"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "regarding", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05640849843621254, "Height": 0.01126102451235056, "Left": 0.36984339356422424, "Top": 0.8314745426177979}, "Polygon": [{"X": 0.36984339356422424, "Y": 0.8314745426177979}, {"X": 0.4262518882751465, "Y": 0.8314746022224426}, {"X": 0.4262518584728241, "Y": 0.8427355885505676}, {"X": 0.36984339356422424, "Y": 0.8427355289459229}]}, "Id": "2174b987-2c93-45a8-918e-225ca00ab31d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021521007642149925, "Height": 0.008802753873169422, "Left": 0.42950868606567383, "Top": 0.8315526843070984}, "Polygon": [{"X": 0.4295087158679962, "Y": 0.8315526843070984}, {"X": 0.4510296881198883, "Y": 0.8315527439117432}, {"X": 0.4510296583175659, "Y": 0.8403554558753967}, {"X": 0.42950868606567383, "Y": 0.8403554558753967}]}, "Id": "92bb5e05-a613-4e93-bb66-c6deba2b9982"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04291142150759697, "Height": 0.008728611283004284, "Left": 0.4549671709537506, "Top": 0.8315370082855225}, "Polygon": [{"X": 0.454967200756073, "Y": 0.8315370082855225}, {"X": 0.4978786110877991, "Y": 0.8315370678901672}, {"X": 0.4978785812854767, "Y": 0.8402656316757202}, {"X": 0.4549671709537506, "Y": 0.8402655720710754}]}, "Id": "b1f550fc-203b-4835-9f77-ec09380646c9"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011075013317167759, "Height": 0.007483791094273329, "Left": 0.5017386078834534, "Top": 0.8332392573356628}, "Polygon": [{"X": 0.5017386674880981, "Y": 0.8332392573356628}, {"X": 0.5128136277198792, "Y": 0.8332392573356628}, {"X": 0.5128136277198792, "Y": 0.8407230377197266}, {"X": 0.5017386078834534, "Y": 0.8407230377197266}]}, "Id": "0127ff0f-ace8-47e7-a37d-8e762f486e60"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "920-686-7797", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08326150476932526, "Height": 0.009409691207110882, "Left": 0.5160424113273621, "Top": 0.8315550088882446}, "Polygon": [{"X": 0.5160424113273621, "Y": 0.8315550088882446}, {"X": 0.5993039011955261, "Y": 0.8315550684928894}, {"X": 0.5993038415908813, "Y": 0.840964674949646}, {"X": 0.5160424113273621, "Y": 0.8409646153450012}]}, "Id": "d7e20a78-b546-43f4-8e09-7c71ecf51ced"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013006000779569149, "Height": 0.005523601081222296, "Left": 0.6038719415664673, "Top": 0.8347492218017578}, "Polygon": [{"X": 0.6038719415664673, "Y": 0.8347492218017578}, {"X": 0.6168779134750366, "Y": 0.8347492218017578}, {"X": 0.6168779134750366, "Y": 0.840272843837738}, {"X": 0.6038719415664673, "Y": 0.8402727842330933}]}, "Id": "46de2088-bcdb-46b1-a823-2299fd810744"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "e-mail", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036794859915971756, "Height": 0.008928691036999226, "Left": 0.6200114488601685, "Top": 0.8315479755401611}, "Polygon": [{"X": 0.6200115084648132, "Y": 0.8315479755401611}, {"X": 0.6568062901496887, "Y": 0.8315480351448059}, {"X": 0.656806230545044, "Y": 0.8404766917228699}, {"X": 0.6200114488601685, "Y": 0.8404766321182251}]}, "Id": "79697c44-e6c7-4d46-bcda-059e966e0add"}, {"BlockType": "WORD", "Confidence": 94.19104766845703, "Text": "<EMAIL>.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1874779611825943, "Height": 0.011267958208918571, "Left": 0.6607118844985962, "Top": 0.8314340710639954}, "Polygon": [{"X": 0.660711944103241, "Y": 0.8314340710639954}, {"X": 0.8481898307800293, "Y": 0.8314342498779297}, {"X": 0.8481897115707397, "Y": 0.8427020311355591}, {"X": 0.6607118844985962, "Y": 0.8427018523216248}]}, "Id": "6fa4532f-bc52-413e-bcee-b7bfaab45509"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06433092057704926, "Height": 0.016505271196365356, "Left": 0.40850830078125, "Top": 0.8671021461486816}, "Polygon": [{"X": 0.4085083305835724, "Y": 0.8671021461486816}, {"X": 0.47283920645713806, "Y": 0.8671022057533264}, {"X": 0.4728391766548157, "Y": 0.8836074471473694}, {"X": 0.40850830078125, "Y": 0.8836073875427246}]}, "Id": "e822adb7-843d-4725-a781-4ae61efd83d3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11119548976421356, "Height": 0.021204303950071335, "Left": 0.48003315925598145, "Top": 0.8670413494110107}, "Polygon": [{"X": 0.4800332188606262, "Y": 0.8670413494110107}, {"X": 0.5912286639213562, "Y": 0.8670414090156555}, {"X": 0.5912285447120667, "Y": 0.8882456421852112}, {"X": 0.48003315925598145, "Y": 0.8882455825805664}]}, "Id": "612ef28c-9c77-41cd-95d8-b8bcec9f241a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Thank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05056442692875862, "Height": 0.011776996776461601, "Left": 0.384990394115448, "Top": 0.8939880728721619}, "Polygon": [{"X": 0.3849904239177704, "Y": 0.8939880728721619}, {"X": 0.4355548322200775, "Y": 0.8939881324768066}, {"X": 0.4355548024177551, "Y": 0.9057650566101074}, {"X": 0.384990394115448, "Y": 0.9057650566101074}]}, "Id": "262b5238-1c8a-4c16-889a-a5c145f16e51"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "you", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029228713363409042, "Height": 0.011208275333046913, "Left": 0.43965068459510803, "Top": 0.8976007699966431}, "Polygon": [{"X": 0.43965068459510803, "Y": 0.8976007699966431}, {"X": 0.468879371881485, "Y": 0.8976007699966431}, {"X": 0.4688793420791626, "Y": 0.9088090062141418}, {"X": 0.43965068459510803, "Y": 0.9088090062141418}]}, "Id": "642797f8-ef3a-4175-8be8-04764f9510d9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02272530272603035, "Height": 0.011620513163506985, "Left": 0.47395020723342896, "Top": 0.8939355611801147}, "Polygon": [{"X": 0.47395026683807373, "Y": 0.8939355611801147}, {"X": 0.4966755211353302, "Y": 0.8939355611801147}, {"X": 0.4966754615306854, "Y": 0.9055560231208801}, {"X": 0.47395020723342896, "Y": 0.9055560231208801}]}, "Id": "3213ec89-6f90-443f-9a91-43b1b51fe341"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03638813644647598, "Height": 0.011246289126574993, "Left": 0.500806987285614, "Top": 0.897659420967102}, "Polygon": [{"X": 0.5008070468902588, "Y": 0.897659420967102}, {"X": 0.5371951460838318, "Y": 0.897659420967102}, {"X": 0.537195086479187, "Y": 0.9089056849479675}, {"X": 0.500806987285614, "Y": 0.9089056849479675}]}, "Id": "9ef495b9-9a20-4ef9-aa21-2fde5f76458a"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "business!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0716262236237526, "Height": 0.011526910588145256, "Left": 0.5409953594207764, "Top": 0.8939923048019409}, "Polygon": [{"X": 0.5409954190254211, "Y": 0.8939923048019409}, {"X": 0.6126216053962708, "Y": 0.8939923644065857}, {"X": 0.6126214861869812, "Y": 0.9055192470550537}, {"X": 0.5409953594207764, "Y": 0.9055191874504089}]}, "Id": "e3a5c834-0fa5-4cd0-8f4a-d664537fde0a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "edc87e23-52d5-4cbc-a9e0-a4ea51af864b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "edc87e23-52d5-4cbc-a9e0-a4ea51af864b", "content-type": "application/x-amz-json-1.1", "content-length": "62332", "date": "Wed, 24 Sep 2025 12:59:42 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "14e5bc5d-d5de-4ba7-b37c-ec978bcab50c", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:59:44 GMT", "content-type": "application/json", "content-length": "617", "connection": "keep-alive", "x-amzn-requestid": "14e5bc5d-d5de-4ba7-b37c-ec978bcab50c"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify page 1. The content shows \"Invoice\" header, \"Invoice # 80494\", \"Bill To\", \"Terms Net 30\". No HS codes or customs. So it's a carrier invoice. Not commercial because no customs. So doc_type: invoice. Return array with one object: page_no 1, doc_type \"invoice\". Use function."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"invoice\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2730, "outputTokens": 93, "totalTokens": 2823}, "metrics": {"latencyMs": 652}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/a7b907d0_IGQ6ARKKVIX04G5ZQDJ5.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nRush Logistics\nInvoice\nPO Box 2041\nManitowoc, WI 54221-2041\nDate:\n9/10/2025\nInvoice # 80494\nBill To:\nWisconsin Aluminum Foundry\n838 S 16th Street\nManitowoc, WI 54221-0246\nTerms\nNet 30\nQuantity\nDescription\nService Date(s)\nAmount\n1\nPT Solutions to Wisconsin Aluminum Foundry\n9/10/2025\n50.00\n1\nload from Wisconsin Aluminum Foundry to Stecker\n9/10/2025\n125.00\nTotal:\n$175.00\nRemit payment to Rush Logistics, PO Box 2041 Manitowoc, WI 54221-2041. Please\ncontact our office with any questions regarding this invoice at 920-686-7797 or e-mail <EMAIL>.\nRush Logistics\nThank you for your business!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}