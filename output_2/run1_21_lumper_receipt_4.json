{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "c4f7c7fb-57d8-4661-9091-d554f8ab9a76", "Relationships": [{"Type": "CHILD", "Ids": ["8188c943-9b57-47e9-8cf3-cdec080f37e1", "5e43898b-e519-443d-85c4-bfa0cffa71bd", "fb3ffb16-b8c2-49f3-aa7f-6371d6e065d3", "c7789953-c122-40b9-a6b5-1b0973b6fa46", "760ec472-ea7b-4966-b883-49023f26e4d1", "254bc1f5-65d9-474a-a297-6977edca6d97", "7d0dba4f-4533-41fc-9231-5f407042a59a", "971c4ca1-0705-4af1-ba2d-762ca2c1ad02", "34415b7b-4c96-49fc-8002-b01c7efabf84", "f42f3078-ec9d-4a6b-97e6-3db5908f52a2", "eb8227d0-87da-4825-a64d-14c8940b55f6", "84310677-af6f-4612-af4c-b893d4a627fd", "565f8784-8e5a-46e2-915d-96dc88e890db", "0964a785-d028-4765-8610-ca7f05593d12", "30bdfc78-1be9-4bba-a62c-7a172576c326", "c9ec5a46-7544-4b8b-9df6-5cf659d4c388", "6f8ed36c-3735-49d8-89df-ab7d02acc656", "f1ce1030-6769-4301-b82b-4960f245ec24", "4b3b6435-97d1-44b0-9528-173b86ce11ce", "6ab44e3d-7f14-4145-89bd-31057405a0b1", "6b1a5a03-83ed-4fb5-bae4-faa24bbc5b37", "228158bc-8fd4-4443-a9e3-7ba775f90309", "92538ced-690c-4df8-83b1-d266992563b2", "13008849-9db3-4fe9-bc4b-780bae2b42f4", "52af0f2c-f806-40a2-93af-21a7ae33dbe5", "c5d3c655-02a9-4641-ba95-b8d99a109a40", "0d8ae59a-477b-4d2c-a97a-26eda7dce4e9", "a5767faa-fbb5-4c31-9202-69340074d423", "5b6fc775-a109-402b-9bc1-844787d018f7", "d2172c0b-8ffa-4f3b-99d5-5be7675d436a", "704eff2b-274d-402f-b97e-b66fdf1ab931", "a316460a-cdef-4619-8702-54324d5af942", "1a95015f-e736-4e6c-8627-21b5a8c26e43", "8e71ac80-bf29-4662-bdd7-4e0d6ccf14da", "2d303a08-9850-44f5-86fc-3d6bbf9c26b1", "2b7a857f-32d8-41b7-9409-b403d9765952", "88c54f37-6bea-435c-8af0-6b057320f270", "320faafe-cbcb-4782-8b0a-96c0007fe4ba"]}]}, {"BlockType": "LINE", "Confidence": 99.80677795410156, "Text": "FHI, LLC", "Geometry": {"BoundingBox": {"Width": 0.11965914815664291, "Height": 0.013422587886452675, "Left": 0.440313458442688, "Top": 0.043452490121126175}, "Polygon": [{"X": 0.4403136670589447, "Y": 0.043452490121126175}, {"X": 0.5599725842475891, "Y": 0.04345250874757767}, {"X": 0.55997234582901, "Y": 0.0568750761449337}, {"X": 0.440313458442688, "Y": 0.056875042617321014}]}, "Id": "8188c943-9b57-47e9-8cf3-cdec080f37e1", "Relationships": [{"Type": "CHILD", "Ids": ["2e706fa3-afc6-44ab-b277-04fdc239a86c", "f961f0df-91e8-40c8-9713-c5d11922b454"]}]}, {"BlockType": "LINE", "Confidence": 99.96419525146484, "Text": "Fed. ID #56-2174318", "Geometry": {"BoundingBox": {"Width": 0.27822768688201904, "Height": 0.011341417208313942, "Left": 0.36109742522239685, "Top": 0.06315767019987106}, "Polygon": [{"X": 0.36109763383865356, "Y": 0.06315767019987106}, {"X": 0.6393251419067383, "Y": 0.06315776705741882}, {"X": 0.6393249034881592, "Y": 0.07449908554553986}, {"X": 0.36109742522239685, "Y": 0.07449895888566971}]}, "Id": "5e43898b-e519-443d-85c4-bfa0cffa71bd", "Relationships": [{"Type": "CHILD", "Ids": ["6c3c2b6f-2bdc-413f-874b-b9d1497a6cba", "25493e4f-dc96-4dc4-86f1-79911b9defac", "8045551c-bdb2-40a7-a33b-d3fa6d72c502"]}]}, {"BlockType": "LINE", "Confidence": 99.90101623535156, "Text": "P.O. Box 546", "Geometry": {"BoundingBox": {"Width": 0.17865242063999176, "Height": 0.01143514271825552, "Left": 0.4111614227294922, "Top": 0.08281977474689484}, "Polygon": [{"X": 0.4111616015434265, "Y": 0.08281977474689484}, {"X": 0.5898138284683228, "Y": 0.0828198716044426}, {"X": 0.5898136496543884, "Y": 0.09425491839647293}, {"X": 0.4111614227294922, "Y": 0.09425479918718338}]}, "Id": "fb3ffb16-b8c2-49f3-aa7f-6371d6e065d3", "Relationships": [{"Type": "CHILD", "Ids": ["7a2ee502-9d4a-4fd4-aab1-79ce2c122fc6", "b92d093a-93b5-40bb-820c-9c82c6a9b6ea", "ec944649-0676-4d1b-9bc0-cb2ff73a32e4"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Fuquay Varina, NC 27526", "Geometry": {"BoundingBox": {"Width": 0.3581905961036682, "Height": 0.0142056317999959, "Left": 0.32114604115486145, "Top": 0.1027875542640686}, "Polygon": [{"X": 0.32114627957344055, "Y": 0.1027875542640686}, {"X": 0.6793366074562073, "Y": 0.10278782993555069}, {"X": 0.679336428642273, "Y": 0.11699318885803223}, {"X": 0.32114604115486145, "Y": 0.11699286848306656}]}, "Id": "c7789953-c122-40b9-a6b5-1b0973b6fa46", "Relationships": [{"Type": "CHILD", "Ids": ["012367a2-1309-4125-9744-fb37e5a97a6d", "f7eb17f5-66be-4a39-b95c-fcfb19ba7c45", "c74f7c38-fa11-4c94-9561-3bc7e1269a2e", "d2ea2c49-ca01-4245-9462-ce170755b859"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "919-552-3157", "Geometry": {"BoundingBox": {"Width": 0.183644637465477, "Height": 0.011433270759880543, "Left": 0.4082373082637787, "Top": 0.12250351905822754}, "Polygon": [{"X": 0.408237487077713, "Y": 0.12250351905822754}, {"X": 0.5918819308280945, "Y": 0.12250369787216187}, {"X": 0.5918817520141602, "Y": 0.1339367926120758}, {"X": 0.4082373082637787, "Y": 0.13393659889698029}]}, "Id": "760ec472-ea7b-4966-b883-49023f26e4d1", "Relationships": [{"Type": "CHILD", "Ids": ["c388b915-2a72-4a6e-826f-dc971fbc9b24"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.06025361269712448, "Height": 0.01126458216458559, "Left": 0.06799536943435669, "Top": 0.16588950157165527}, "Polygon": [{"X": 0.06799556314945221, "Y": 0.16588950157165527}, {"X": 0.12824898958206177, "Y": 0.16588957607746124}, {"X": 0.12824879586696625, "Y": 0.177154079079628}, {"X": 0.06799536943435669, "Y": 0.17715398967266083}]}, "Id": "254bc1f5-65d9-474a-a297-6977edca6d97", "Relationships": [{"Type": "CHILD", "Ids": ["f1ed057a-3636-40af-91c3-10f7cd9e8375"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/11/25", "Geometry": {"BoundingBox": {"Width": 0.11387137323617935, "Height": 0.011411534622311592, "Left": 0.6250819563865662, "Top": 0.1658749282360077}, "Polygon": [{"X": 0.6250821352005005, "Y": 0.1658749282360077}, {"X": 0.7389532923698425, "Y": 0.16587509214878082}, {"X": 0.7389531135559082, "Y": 0.17728646099567413}, {"X": 0.6250819563865662, "Y": 0.1772862821817398}]}, "Id": "7d0dba4f-4533-41fc-9231-5f407042a59a", "Relationships": [{"Type": "CHILD", "Ids": ["9568748b-80fa-4e0c-8431-a55767b3ca94"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "RECEIPT", "Geometry": {"BoundingBox": {"Width": 0.12763401865959167, "Height": 0.01150046568363905, "Left": 0.43700599670410156, "Top": 0.20938195288181305}, "Polygon": [{"X": 0.4370061755180359, "Y": 0.20938195288181305}, {"X": 0.5646399855613708, "Y": 0.20938219130039215}, {"X": 0.5646398067474365, "Y": 0.22088241577148438}, {"X": 0.43700599670410156, "Y": 0.22088216245174408}]}, "Id": "971c4ca1-0705-4af1-ba2d-762ca2c1ad02", "Relationships": [{"Type": "CHILD", "Ids": ["b08aa7f4-139f-4e8f-876f-7c2c4b36d1f1"]}]}, {"BlockType": "LINE", "Confidence": 99.6484375, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.1295841634273529, "Height": 0.014019904658198357, "Left": 0.06801876425743103, "Top": 0.2332058995962143}, "Polygon": [{"X": 0.06801900267601013, "Y": 0.2332058995962143}, {"X": 0.19760292768478394, "Y": 0.23320616781711578}, {"X": 0.19760268926620483, "Y": 0.2472258061170578}, {"X": 0.06801876425743103, "Y": 0.24722550809383392}]}, "Id": "34415b7b-4c96-49fc-8002-b01c7efabf84", "Relationships": [{"Type": "CHILD", "Ids": ["d9921cba-1627-4824-ab8c-d32555c6ff8a", "7d2a988c-3d28-4837-9d54-c020fdaff77d"]}]}, {"BlockType": "LINE", "Confidence": 97.40911865234375, "Text": "RAI250811065739", "Geometry": {"BoundingBox": {"Width": 0.24437358975410461, "Height": 0.011593890376389027, "Left": 0.62575364112854, "Top": 0.23321114480495453}, "Polygon": [{"X": 0.6257538199424744, "Y": 0.23321114480495453}, {"X": 0.8701272010803223, "Y": 0.23321165144443512}, {"X": 0.8701270222663879, "Y": 0.24480503797531128}, {"X": 0.62575364112854, "Y": 0.2448045015335083}]}, "Id": "f42f3078-ec9d-4a6b-97e6-3db5908f52a2", "Relationships": [{"Type": "CHILD", "Ids": ["e9638deb-54c8-4a26-9f6e-6f7151acef3a"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "FACILITY:", "Geometry": {"BoundingBox": {"Width": 0.13432680070400238, "Height": 0.011322923935949802, "Left": 0.06801436841487885, "Top": 0.2610757052898407}, "Polygon": [{"X": 0.06801456212997437, "Y": 0.2610757052898407}, {"X": 0.20234116911888123, "Y": 0.2610760033130646}, {"X": 0.2023409754037857, "Y": 0.27239862084388733}, {"X": 0.06801436841487885, "Y": 0.27239829301834106}]}, "Id": "eb8227d0-87da-4825-a64d-14c8940b55f6", "Relationships": [{"Type": "CHILD", "Ids": ["3921b09b-f6c2-43b2-bd72-e18be7836533"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Ocala", "Geometry": {"BoundingBox": {"Width": 0.07582534104585648, "Height": 0.011631847359240055, "Left": 0.6253350973129272, "Top": 0.2609020471572876}, "Polygon": [{"X": 0.6253352761268616, "Y": 0.2609020471572876}, {"X": 0.7011604309082031, "Y": 0.2609022259712219}, {"X": 0.7011602520942688, "Y": 0.2725338935852051}, {"X": 0.6253350973129272, "Y": 0.27253368496894836}]}, "Id": "84310677-af6f-4612-af4c-b893d4a627fd", "Relationships": [{"Type": "CHILD", "Ids": ["4ec798a5-81c3-49f4-83de-165c5fed8181"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Department:", "Geometry": {"BoundingBox": {"Width": 0.1587502658367157, "Height": 0.013907834887504578, "Left": 0.06806491315364838, "Top": 0.28887009620666504}, "Polygon": [{"X": 0.06806515157222748, "Y": 0.28887009620666504}, {"X": 0.22681517899036407, "Y": 0.28887054324150085}, {"X": 0.22681494057178497, "Y": 0.3027779459953308}, {"X": 0.06806491315364838, "Y": 0.302777498960495}]}, "Id": "565f8784-8e5a-46e2-915d-96dc88e890db", "Relationships": [{"Type": "CHILD", "Ids": ["0a5435db-4ed0-4b27-8938-962a1b34b576"]}]}, {"BlockType": "LINE", "Confidence": 99.76164245605469, "Text": "GRO-Ocala", "Geometry": {"BoundingBox": {"Width": 0.15273724496364594, "Height": 0.011507879942655563, "Left": 0.6255061030387878, "Top": 0.2886818051338196}, "Polygon": [{"X": 0.6255062818527222, "Y": 0.2886818051338196}, {"X": 0.778243362903595, "Y": 0.288682222366333}, {"X": 0.7782431840896606, "Y": 0.30018970370292664}, {"X": 0.6255061030387878, "Y": 0.3001892864704132}]}, "Id": "0964a785-d028-4765-8610-ca7f05593d12", "Relationships": [{"Type": "CHILD", "Ids": ["42afd0be-14d1-4f3f-a924-012d25fda0b7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.06922127306461334, "Height": 0.011067783460021019, "Left": 0.06801866739988327, "Top": 0.31671464443206787}, "Polygon": [{"X": 0.0680188536643982, "Y": 0.31671464443206787}, {"X": 0.1372399479150772, "Y": 0.3167148530483246}, {"X": 0.1372397541999817, "Y": 0.32778242230415344}, {"X": 0.06801866739988327, "Y": 0.32778221368789673}]}, "Id": "30bdfc78-1be9-4bba-a62c-7a172576c326", "Relationships": [{"Type": "CHILD", "Ids": ["b2b42172-9f8c-4c8f-a87e-d8764d25d35e"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "16", "Geometry": {"BoundingBox": {"Width": 0.030719656497240067, "Height": 0.011005452834069729, "Left": 0.6267974376678467, "Top": 0.3169155418872833}, "Polygon": [{"X": 0.626797616481781, "Y": 0.3169155418872833}, {"X": 0.6575170755386353, "Y": 0.3169156312942505}, {"X": 0.6575168967247009, "Y": 0.3279210031032562}, {"X": 0.6267974376678467, "Y": 0.32792091369628906}]}, "Id": "c9ec5a46-7544-4b8b-9df6-5cf659d4c388", "Relationships": [{"Type": "CHILD", "Ids": ["7883c7d2-9446-4065-9ee1-e127fd964c0a"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Shift:", "Geometry": {"BoundingBox": {"Width": 0.06394462287425995, "Height": 0.011428557336330414, "Left": 0.06792221963405609, "Top": 0.3442031443119049}, "Polygon": [{"X": 0.06792241334915161, "Y": 0.3442031443119049}, {"X": 0.13186684250831604, "Y": 0.3442033529281616}, {"X": 0.13186664879322052, "Y": 0.3556317090988159}, {"X": 0.06792221963405609, "Y": 0.3556315004825592}]}, "Id": "6f8ed36c-3735-49d8-89df-ab7d02acc656", "Relationships": [{"Type": "CHILD", "Ids": ["8f98a35a-d539-486a-b6a8-3e8d1e2d0933"]}]}, {"BlockType": "LINE", "Confidence": 99.15995788574219, "Text": "1st Shift", "Geometry": {"BoundingBox": {"Width": 0.10468015819787979, "Height": 0.01163840014487505, "Left": 0.6268889904022217, "Top": 0.34409958124160767}, "Polygon": [{"X": 0.6268892288208008, "Y": 0.34409958124160767}, {"X": 0.7315691709518433, "Y": 0.34409990906715393}, {"X": 0.7315689921379089, "Y": 0.35573798418045044}, {"X": 0.6268889904022217, "Y": 0.3557376265525818}]}, "Id": "f1ce1030-6769-4301-b82b-4960f245ec24", "Relationships": [{"Type": "CHILD", "Ids": ["d3c955cc-7a61-4b9b-b6e4-8ac0e9139288", "9bb8f619-a5b8-4c2f-870b-017f73bec982"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "LOAD INFO:", "Geometry": {"BoundingBox": {"Width": 0.1645379513502121, "Height": 0.011415382847189903, "Left": 0.06751324981451035, "Top": 0.3917284905910492}, "Polygon": [{"X": 0.06751345098018646, "Y": 0.3917284905910492}, {"X": 0.23205120861530304, "Y": 0.39172911643981934}, {"X": 0.23205101490020752, "Y": 0.40314388275146484}, {"X": 0.06751324981451035, "Y": 0.4031432569026947}]}, "Id": "4b3b6435-97d1-44b0-9528-173b86ce11ce", "Relationships": [{"Type": "CHILD", "Ids": ["f875d1ab-e346-4e1b-a3f6-7043df35b8fc", "061345b1-900d-4435-8268-bf476fd85179"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.09611997753381729, "Height": 0.011409152299165726, "Left": 0.06708154082298279, "Top": 0.4195166230201721}, "Polygon": [{"X": 0.06708173453807831, "Y": 0.4195166230201721}, {"X": 0.16320151090621948, "Y": 0.41951701045036316}, {"X": 0.16320131719112396, "Y": 0.43092575669288635}, {"X": 0.06708154082298279, "Y": 0.4309253692626953}]}, "Id": "6ab44e3d-7f14-4145-89bd-31057405a0b1", "Relationships": [{"Type": "CHILD", "Ids": ["11cb8147-bf6b-4534-bc81-acfe8f32c73f"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "59 north", "Geometry": {"BoundingBox": {"Width": 0.10595554113388062, "Height": 0.01127858180552721, "Left": 0.6253074407577515, "Top": 0.4197206497192383}, "Polygon": [{"X": 0.6253076791763306, "Y": 0.4197206497192383}, {"X": 0.7312629818916321, "Y": 0.4197210669517517}, {"X": 0.7312628030776978, "Y": 0.430999219417572}, {"X": 0.6253074407577515, "Y": 0.4309988021850586}]}, "Id": "6b1a5a03-83ed-4fb5-bae4-faa24bbc5b37", "Relationships": [{"Type": "CHILD", "Ids": ["46ef0d49-e4a5-4b99-85de-e2a00fb0d7e5", "8a4b83b4-eb66-4d74-9d52-74787cfdf49b"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Vendor:", "Geometry": {"BoundingBox": {"Width": 0.10209459811449051, "Height": 0.01136750914156437, "Left": 0.06617559492588043, "Top": 0.4472859501838684}, "Polygon": [{"X": 0.06617578864097595, "Y": 0.4472859501838684}, {"X": 0.16827018558979034, "Y": 0.44728636741638184}, {"X": 0.16826999187469482, "Y": 0.45865345001220703}, {"X": 0.06617559492588043, "Y": 0.4586530029773712}]}, "Id": "228158bc-8fd4-4443-a9e3-7ba775f90309", "Relationships": [{"Type": "CHILD", "Ids": ["b5a6414b-9c83-4f3f-998b-f288907dacb5"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Amercare Royal", "Geometry": {"BoundingBox": {"Width": 0.21078410744667053, "Height": 0.014070700854063034, "Left": 0.6242707371711731, "Top": 0.4472483694553375}, "Polygon": [{"X": 0.6242709755897522, "Y": 0.4472483694553375}, {"X": 0.8350548148155212, "Y": 0.44724926352500916}, {"X": 0.8350545763969421, "Y": 0.46131905913352966}, {"X": 0.6242707371711731, "Y": 0.46131813526153564}]}, "Id": "92538ced-690c-4df8-83b1-d266992563b2", "Relationships": [{"Type": "CHILD", "Ids": ["8790e98b-da0d-4cfb-bb23-fd54decdc7bb", "4d5b8a89-b17b-44af-8728-657251176412"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck", "Geometry": {"BoundingBox": {"Width": 0.0743144154548645, "Height": 0.011338412761688232, "Left": 0.06626966595649719, "Top": 0.475087434053421}, "Polygon": [{"X": 0.06626986712217331, "Y": 0.475087434053421}, {"X": 0.1405840814113617, "Y": 0.4750877618789673}, {"X": 0.14058388769626617, "Y": 0.48642584681510925}, {"X": 0.06626966595649719, "Y": 0.4864254891872406}]}, "Id": "13008849-9db3-4fe9-bc4b-780bae2b42f4", "Relationships": [{"Type": "CHILD", "Ids": ["195e514c-ea7f-43c3-b8ac-b4d7ecfa5984"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "01", "Geometry": {"BoundingBox": {"Width": 0.028749730437994003, "Height": 0.011069179512560368, "Left": 0.6250150799751282, "Top": 0.47542041540145874}, "Polygon": [{"X": 0.6250152587890625, "Y": 0.47542041540145874}, {"X": 0.6537647843360901, "Y": 0.4754205644130707}, {"X": 0.6537646055221558, "Y": 0.48648959398269653}, {"X": 0.6250150799751282, "Y": 0.486489474773407}]}, "Id": "52af0f2c-f806-40a2-93af-21a7ae33dbe5", "Relationships": [{"Type": "CHILD", "Ids": ["0226ae16-b2ae-4574-8b25-205430f402c5"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Trailer", "Geometry": {"BoundingBox": {"Width": 0.08388730138540268, "Height": 0.011210412718355656, "Left": 0.06640441715717316, "Top": 0.5029498338699341}, "Polygon": [{"X": 0.06640461087226868, "Y": 0.5029498338699341}, {"X": 0.15029171109199524, "Y": 0.5029502511024475}, {"X": 0.15029151737689972, "Y": 0.5141602754592896}, {"X": 0.06640441715717316, "Y": 0.5141598582267761}]}, "Id": "c5d3c655-02a9-4641-ba95-b8d99a109a40", "Relationships": [{"Type": "CHILD", "Ids": ["ea37f520-bdf5-4f8e-bb2b-09792d4f25cf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "5250234", "Geometry": {"BoundingBox": {"Width": 0.11469906568527222, "Height": 0.011033291928470135, "Left": 0.6249483823776245, "Top": 0.5031672120094299}, "Polygon": [{"X": 0.6249485611915588, "Y": 0.5031672120094299}, {"X": 0.7396474480628967, "Y": 0.5031677484512329}, {"X": 0.7396472096443176, "Y": 0.5142005085945129}, {"X": 0.6249483823776245, "Y": 0.5141999125480652}]}, "Id": "0d8ae59a-477b-4d2c-a97a-26eda7dce4e9", "Relationships": [{"Type": "CHILD", "Ids": ["ff6e61db-c7ee-4be3-a47e-828b6304f7fc"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Amount Due", "Geometry": {"BoundingBox": {"Width": 0.16375760734081268, "Height": 0.011330433189868927, "Left": 0.06595931947231293, "Top": 0.5503145456314087}, "Polygon": [{"X": 0.06595951318740845, "Y": 0.5503145456314087}, {"X": 0.2297169268131256, "Y": 0.5503154397010803}, {"X": 0.2297167330980301, "Y": 0.561644971370697}, {"X": 0.06595931947231293, "Y": 0.5616440773010254}]}, "Id": "a5767faa-fbb5-4c31-9202-69340074d423", "Relationships": [{"Type": "CHILD", "Ids": ["f87cd479-d863-45a8-8124-7bc465c89816", "e9e539e7-35e3-49a6-be2c-7fb4dcfe93c8"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$180.00", "Geometry": {"BoundingBox": {"Width": 0.10625392198562622, "Height": 0.01354591641575098, "Left": 0.6249967813491821, "Top": 0.549399197101593}, "Polygon": [{"X": 0.6249970197677612, "Y": 0.549399197101593}, {"X": 0.7312507033348083, "Y": 0.549399733543396}, {"X": 0.7312504649162292, "Y": 0.5629451274871826}, {"X": 0.6249967813491821, "Y": 0.5629445314407349}]}, "Id": "5b6fc775-a109-402b-9bc1-844787d018f7", "Relationships": [{"Type": "CHILD", "Ids": ["c8a3e84b-73df-4c81-a79c-85cc8b99ad1f"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Convenience Fee", "Geometry": {"BoundingBox": {"Width": 0.2295553833246231, "Height": 0.011339168064296246, "Left": 0.06719447672367096, "Top": 0.5780941247940063}, "Polygon": [{"X": 0.06719467043876648, "Y": 0.5780941247940063}, {"X": 0.29674986004829407, "Y": 0.5780953764915466}, {"X": 0.29674965143203735, "Y": 0.5894333124160767}, {"X": 0.06719447672367096, "Y": 0.5894320011138916}]}, "Id": "d2172c0b-8ffa-4f3b-99d5-5be7675d436a", "Relationships": [{"Type": "CHILD", "Ids": ["86dcc641-611b-4363-92c8-cd7c873b8004", "26631ae2-4f41-4eec-8b20-520d6a0cb299"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$9.75", "Geometry": {"BoundingBox": {"Width": 0.07284560054540634, "Height": 0.013382857665419579, "Left": 0.6250905990600586, "Top": 0.5773332118988037}, "Polygon": [{"X": 0.6250907778739929, "Y": 0.5773332118988037}, {"X": 0.6979361772537231, "Y": 0.5773336291313171}, {"X": 0.697935938835144, "Y": 0.5907160639762878}, {"X": 0.6250905990600586, "Y": 0.5907156467437744}]}, "Id": "704eff2b-274d-402f-b97e-b66fdf1ab931", "Relationships": [{"Type": "CHILD", "Ids": ["74f7759c-6830-4c8e-809a-ae8a4be34232"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Electronic Payment", "Geometry": {"BoundingBox": {"Width": 0.2524924874305725, "Height": 0.013875389471650124, "Left": 0.06825123727321625, "Top": 0.6059488654136658}, "Polygon": [{"X": 0.06825147569179535, "Y": 0.6059488654136658}, {"X": 0.32074373960494995, "Y": 0.6059503555297852}, {"X": 0.32074350118637085, "Y": 0.6198242902755737}, {"X": 0.06825123727321625, "Y": 0.6198227405548096}]}, "Id": "a316460a-cdef-4619-8702-54324d5af942", "Relationships": [{"Type": "CHILD", "Ids": ["964455a9-b321-446d-a72a-3ec427b268df", "e470adf3-7ec7-4fb9-8b32-f519781631a2"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "$189.75", "Geometry": {"BoundingBox": {"Width": 0.10612017661333084, "Height": 0.013586929999291897, "Left": 0.6248946189880371, "Top": 0.6048755645751953}, "Polygon": [{"X": 0.6248947978019714, "Y": 0.6048755645751953}, {"X": 0.7310147881507874, "Y": 0.6048761606216431}, {"X": 0.7310145497322083, "Y": 0.6184625029563904}, {"X": 0.6248946189880371, "Y": 0.6184618473052979}]}, "Id": "1a95015f-e736-4e6c-8627-21b5a8c26e43", "Relationships": [{"Type": "CHILD", "Ids": ["ddc59bd4-1d5e-4c6e-89e1-aade4b03a1a0"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "BALANCE DUE", "Geometry": {"BoundingBox": {"Width": 0.2055393010377884, "Height": 0.011493567377328873, "Left": 0.06729398667812347, "Top": 0.6530996561050415}, "Polygon": [{"X": 0.06729418784379959, "Y": 0.6530996561050415}, {"X": 0.27283328771591187, "Y": 0.6531009078025818}, {"X": 0.27283310890197754, "Y": 0.6645932197570801}, {"X": 0.06729398667812347, "Y": 0.6645918488502502}]}, "Id": "8e71ac80-bf29-4662-bdd7-4e0d6ccf14da", "Relationships": [{"Type": "CHILD", "Ids": ["97a9bc96-3fa1-4213-bedf-0fefd5ab4031", "97309aac-1241-4298-b514-2c1a498648a5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$0.00", "Geometry": {"BoundingBox": {"Width": 0.0735323503613472, "Height": 0.013299154117703438, "Left": 0.6249604821205139, "Top": 0.6525976061820984}, "Polygon": [{"X": 0.624960720539093, "Y": 0.6525976061820984}, {"X": 0.6984928250312805, "Y": 0.6525980234146118}, {"X": 0.6984926462173462, "Y": 0.6658967137336731}, {"X": 0.6249604821205139, "Y": 0.6658962368965149}]}, "Id": "2d303a08-9850-44f5-86fc-3d6bbf9c26b1", "Relationships": [{"Type": "CHILD", "Ids": ["4f4a0484-23b6-4f01-b158-7167b6280afc"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "DESCRIPTION OF CHARGES", "Geometry": {"BoundingBox": {"Width": 0.4018067717552185, "Height": 0.012055947445333004, "Left": 0.06753572821617126, "Top": 0.7001860737800598}, "Polygon": [{"X": 0.06753593683242798, "Y": 0.7001860737800598}, {"X": 0.46934249997138977, "Y": 0.7001888155937195}, {"X": 0.46934229135513306, "Y": 0.712242066860199}, {"X": 0.06753572821617126, "Y": 0.7122392654418945}]}, "Id": "2b7a857f-32d8-41b7-9409-b403d9765952", "Relationships": [{"Type": "CHILD", "Ids": ["3f87a222-676b-4bb8-b5a5-9e9ad6cfa864", "d04aca46-362d-446c-9792-038b960ee1de", "2f4d77c3-bd31-4c5b-9b0f-a9afeed5aee7"]}]}, {"BlockType": "LINE", "Confidence": 99.11322021484375, "Text": "PL 21-25 <PERSON>lts", "Geometry": {"BoundingBox": {"Width": 0.22329773008823395, "Height": 0.011831068433821201, "Left": 0.06800378113985062, "Top": 0.7281111478805542}, "Polygon": [{"X": 0.06800398230552673, "Y": 0.7281111478805542}, {"X": 0.29130151867866516, "Y": 0.7281127572059631}, {"X": 0.29130131006240845, "Y": 0.7399422526359558}, {"X": 0.06800378113985062, "Y": 0.7399406433105469}]}, "Id": "88c54f37-6bea-435c-8af0-6b057320f270", "Relationships": [{"Type": "CHILD", "Ids": ["ec91ddbd-e6c5-40e8-99c2-ee8052cb8d2e", "60289590-46ae-4989-a47a-90d81f7f91b5", "81192a4f-462f-4ef0-bfc9-c8eddf0f4b4f", "2e6f2146-f189-497f-9e8e-3db24591d89c"]}]}, {"BlockType": "LINE", "Confidence": 92.44499206542969, "Text": "4511576095(Amercare Royal)", "Geometry": {"BoundingBox": {"Width": 0.39425113797187805, "Height": 0.01432619895786047, "Left": 0.0651758685708046, "Top": 0.7761422991752625}, "Polygon": [{"X": 0.0651761144399643, "Y": 0.7761422991752625}, {"X": 0.45942699909210205, "Y": 0.7761452794075012}, {"X": 0.45942676067352295, "Y": 0.7904685139656067}, {"X": 0.0651758685708046, "Y": 0.7904654145240784}]}, "Id": "320faafe-cbcb-4782-8b0a-96c0007fe4ba", "Relationships": [{"Type": "CHILD", "Ids": ["83c65edc-97ce-4378-85cd-83c692b8f1d4", "943473a2-c38b-468d-82d7-dfa5d8fe2408"]}]}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "FHI,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05395852029323578, "Height": 0.013422568328678608, "Left": 0.440313458442688, "Top": 0.043452490121126175}, "Polygon": [{"X": 0.4403136670589447, "Y": 0.043452490121126175}, {"X": 0.4942719638347626, "Y": 0.04345249757170677}, {"X": 0.49427172541618347, "Y": 0.05687505751848221}, {"X": 0.440313458442688, "Y": 0.056875042617321014}]}, "Id": "2e706fa3-afc6-44ab-b277-04fdc239a86c"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05547850951552391, "Height": 0.011234242469072342, "Left": 0.5044940710067749, "Top": 0.04356548935174942}, "Polygon": [{"X": 0.5044942498207092, "Y": 0.04356548935174942}, {"X": 0.5599725842475891, "Y": 0.043565500527620316}, {"X": 0.5599724054336548, "Y": 0.05479973182082176}, {"X": 0.5044940710067749, "Y": 0.05479971691966057}]}, "Id": "f961f0df-91e8-40c8-9713-c5d11922b454"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Fed.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05874408408999443, "Height": 0.011293645016849041, "Left": 0.36109742522239685, "Top": 0.06315767019987106}, "Polygon": [{"X": 0.36109763383865356, "Y": 0.06315767019987106}, {"X": 0.4198414981365204, "Y": 0.06315769255161285}, {"X": 0.41984131932258606, "Y": 0.07445131242275238}, {"X": 0.36109742522239685, "Y": 0.07445129007101059}]}, "Id": "6c3c2b6f-2bdc-413f-874b-b9d1497a6cba"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028080228716135025, "Height": 0.010754511691629887, "Left": 0.4295829236507416, "Top": 0.06343037635087967}, "Polygon": [{"X": 0.4295831024646759, "Y": 0.06343037635087967}, {"X": 0.4576631486415863, "Y": 0.06343038380146027}, {"X": 0.457662969827652, "Y": 0.07418488711118698}, {"X": 0.4295829236507416, "Y": 0.07418487221002579}]}, "Id": "25493e4f-dc96-4dc4-86f1-79911b9defac"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "#56-2174318", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1735733151435852, "Height": 0.01110165473073721, "Left": 0.4657517969608307, "Top": 0.06339742988348007}, "Polygon": [{"X": 0.465751975774765, "Y": 0.06339742988348007}, {"X": 0.6393251419067383, "Y": 0.06339749693870544}, {"X": 0.6393249034881592, "Y": 0.07449908554553986}, {"X": 0.4657517969608307, "Y": 0.07449900358915329}]}, "Id": "8045551c-bdb2-40a7-a33b-d3fa6d72c502"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "P.O.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05687521770596504, "Height": 0.011414367705583572, "Left": 0.4111614227294922, "Top": 0.0828404650092125}, "Polygon": [{"X": 0.4111616015434265, "Y": 0.0828404650092125}, {"X": 0.46803662180900574, "Y": 0.08284049481153488}, {"X": 0.4680364429950714, "Y": 0.09425483644008636}, {"X": 0.4111614227294922, "Y": 0.09425479918718338}]}, "Id": "7a2ee502-9d4a-4fd4-aab1-79ce2c122fc6"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0545131154358387, "Height": 0.011321538127958775, "Left": 0.4781702756881714, "Top": 0.08281981199979782}, "Polygon": [{"X": 0.4781704843044281, "Y": 0.08281981199979782}, {"X": 0.5326834321022034, "Y": 0.08281984180212021}, {"X": 0.5326831936836243, "Y": 0.09414134919643402}, {"X": 0.4781702756881714, "Y": 0.09414131194353104}]}, "Id": "b92d093a-93b5-40bb-820c-9c82c6a9b6ea"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "546", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04879835993051529, "Height": 0.01134564820677042, "Left": 0.5410154461860657, "Top": 0.08289141952991486}, "Polygon": [{"X": 0.5410156846046448, "Y": 0.08289141952991486}, {"X": 0.5898138284683228, "Y": 0.08289144933223724}, {"X": 0.5898136496543884, "Y": 0.0942370668053627}, {"X": 0.5410154461860657, "Y": 0.09423703700304031}]}, "Id": "ec944649-0676-4d1b-9bc0-cb2ff73a32e4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fuquay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1038079485297203, "Height": 0.014136921614408493, "Left": 0.32114604115486145, "Top": 0.10285604000091553}, "Polygon": [{"X": 0.32114627957344055, "Y": 0.10285604000091553}, {"X": 0.42495399713516235, "Y": 0.1028561145067215}, {"X": 0.42495375871658325, "Y": 0.11699295789003372}, {"X": 0.32114604115486145, "Y": 0.11699286848306656}]}, "Id": "012367a2-1309-4125-9744-fb37e5a97a6d"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Varina,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09751097112894058, "Height": 0.013353771530091763, "Left": 0.4326494038105011, "Top": 0.10278764367103577}, "Polygon": [{"X": 0.4326496124267578, "Y": 0.10278764367103577}, {"X": 0.5301603674888611, "Y": 0.10278771817684174}, {"X": 0.530160129070282, "Y": 0.1161414161324501}, {"X": 0.4326494038105011, "Y": 0.11614132672548294}]}, "Id": "f7eb17f5-66be-4a39-b95c-fcfb19ba7c45"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "NC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0412915013730526, "Height": 0.011302242986857891, "Left": 0.5398619174957275, "Top": 0.1028178408741951}, "Polygon": [{"X": 0.5398620963096619, "Y": 0.1028178408741951}, {"X": 0.581153392791748, "Y": 0.10281787067651749}, {"X": 0.5811532139778137, "Y": 0.11412008106708527}, {"X": 0.5398619174957275, "Y": 0.11412004381418228}]}, "Id": "c74f7c38-fa11-4c94-9561-3bc7e1269a2e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "27526", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08114208281040192, "Height": 0.01119225099682808, "Left": 0.5981945395469666, "Top": 0.10300985723733902}, "Polygon": [{"X": 0.5981947183609009, "Y": 0.10300985723733902}, {"X": 0.6793366074562073, "Y": 0.10300992429256439}, {"X": 0.679336428642273, "Y": 0.1142021119594574}, {"X": 0.5981945395469666, "Y": 0.11420203745365143}]}, "Id": "d2ea2c49-ca01-4245-9462-ce170755b859"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "919-552-3157", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.183644637465477, "Height": 0.011433270759880543, "Left": 0.4082373082637787, "Top": 0.12250351905822754}, "Polygon": [{"X": 0.408237487077713, "Y": 0.12250351905822754}, {"X": 0.5918819308280945, "Y": 0.12250369787216187}, {"X": 0.5918817520141602, "Y": 0.1339367926120758}, {"X": 0.4082373082637787, "Y": 0.13393659889698029}]}, "Id": "c388b915-2a72-4a6e-826f-dc971fbc9b24"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06025361269712448, "Height": 0.01126458216458559, "Left": 0.06799536943435669, "Top": 0.16588950157165527}, "Polygon": [{"X": 0.06799556314945221, "Y": 0.16588950157165527}, {"X": 0.12824898958206177, "Y": 0.16588957607746124}, {"X": 0.12824879586696625, "Y": 0.177154079079628}, {"X": 0.06799536943435669, "Y": 0.17715398967266083}]}, "Id": "f1ed057a-3636-40af-91c3-10f7cd9e8375"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/11/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11387137323617935, "Height": 0.011411534622311592, "Left": 0.6250819563865662, "Top": 0.1658749282360077}, "Polygon": [{"X": 0.6250821352005005, "Y": 0.1658749282360077}, {"X": 0.7389532923698425, "Y": 0.16587509214878082}, {"X": 0.7389531135559082, "Y": 0.17728646099567413}, {"X": 0.6250819563865662, "Y": 0.1772862821817398}]}, "Id": "9568748b-80fa-4e0c-8431-a55767b3ca94"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12763401865959167, "Height": 0.01150046568363905, "Left": 0.43700599670410156, "Top": 0.20938195288181305}, "Polygon": [{"X": 0.4370061755180359, "Y": 0.20938195288181305}, {"X": 0.5646399855613708, "Y": 0.20938219130039215}, {"X": 0.5646398067474365, "Y": 0.22088241577148438}, {"X": 0.43700599670410156, "Y": 0.22088216245174408}]}, "Id": "b08aa7f4-139f-4e8f-876f-7c2c4b36d1f1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09905491024255753, "Height": 0.01401983667165041, "Left": 0.06801876425743103, "Top": 0.2332058995962143}, "Polygon": [{"X": 0.06801900267601013, "Y": 0.2332058995962143}, {"X": 0.16707366704940796, "Y": 0.233206108212471}, {"X": 0.16707342863082886, "Y": 0.24722573161125183}, {"X": 0.06801876425743103, "Y": 0.24722550809383392}]}, "Id": "d9921cba-1627-4824-ab8c-d32555c6ff8a"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023377617821097374, "Height": 0.010638154111802578, "Left": 0.17422530055046082, "Top": 0.23374329507350922}, "Polygon": [{"X": 0.17422547936439514, "Y": 0.23374329507350922}, {"X": 0.19760291278362274, "Y": 0.2337433397769928}, {"X": 0.19760273396968842, "Y": 0.24438144266605377}, {"X": 0.17422530055046082, "Y": 0.2443813979625702}]}, "Id": "7d2a988c-3d28-4837-9d54-c020fdaff77d"}, {"BlockType": "WORD", "Confidence": 97.40911865234375, "Text": "RAI250811065739", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24437358975410461, "Height": 0.011593890376389027, "Left": 0.62575364112854, "Top": 0.23321114480495453}, "Polygon": [{"X": 0.6257538199424744, "Y": 0.23321114480495453}, {"X": 0.8701272010803223, "Y": 0.23321165144443512}, {"X": 0.8701270222663879, "Y": 0.24480503797531128}, {"X": 0.62575364112854, "Y": 0.2448045015335083}]}, "Id": "e9638deb-54c8-4a26-9f6e-6f7151acef3a"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "FACILITY:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13432680070400238, "Height": 0.011322923935949802, "Left": 0.06801436841487885, "Top": 0.2610757052898407}, "Polygon": [{"X": 0.06801456212997437, "Y": 0.2610757052898407}, {"X": 0.20234116911888123, "Y": 0.2610760033130646}, {"X": 0.2023409754037857, "Y": 0.27239862084388733}, {"X": 0.06801436841487885, "Y": 0.27239829301834106}]}, "Id": "3921b09b-f6c2-43b2-bd72-e18be7836533"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Ocala", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07582534104585648, "Height": 0.011631847359240055, "Left": 0.6253350973129272, "Top": 0.2609020471572876}, "Polygon": [{"X": 0.6253352761268616, "Y": 0.2609020471572876}, {"X": 0.7011604309082031, "Y": 0.2609022259712219}, {"X": 0.7011602520942688, "Y": 0.2725338935852051}, {"X": 0.6253350973129272, "Y": 0.27253368496894836}]}, "Id": "4ec798a5-81c3-49f4-83de-165c5fed8181"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Department:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1587502658367157, "Height": 0.013907834887504578, "Left": 0.06806491315364838, "Top": 0.28887009620666504}, "Polygon": [{"X": 0.06806515157222748, "Y": 0.28887009620666504}, {"X": 0.22681517899036407, "Y": 0.28887054324150085}, {"X": 0.22681494057178497, "Y": 0.3027779459953308}, {"X": 0.06806491315364838, "Y": 0.302777498960495}]}, "Id": "0a5435db-4ed0-4b27-8938-962a1b34b576"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "GRO-Ocala", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15273724496364594, "Height": 0.011507879942655563, "Left": 0.6255061030387878, "Top": 0.2886818051338196}, "Polygon": [{"X": 0.6255062818527222, "Y": 0.2886818051338196}, {"X": 0.778243362903595, "Y": 0.288682222366333}, {"X": 0.7782431840896606, "Y": 0.30018970370292664}, {"X": 0.6255061030387878, "Y": 0.3001892864704132}]}, "Id": "42afd0be-14d1-4f3f-a924-012d25fda0b7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06922127306461334, "Height": 0.011067783460021019, "Left": 0.06801866739988327, "Top": 0.31671464443206787}, "Polygon": [{"X": 0.0680188536643982, "Y": 0.31671464443206787}, {"X": 0.1372399479150772, "Y": 0.3167148530483246}, {"X": 0.1372397541999817, "Y": 0.32778242230415344}, {"X": 0.06801866739988327, "Y": 0.32778221368789673}]}, "Id": "b2b42172-9f8c-4c8f-a87e-d8764d25d35e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "16", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030719656497240067, "Height": 0.011005452834069729, "Left": 0.6267974376678467, "Top": 0.3169155418872833}, "Polygon": [{"X": 0.626797616481781, "Y": 0.3169155418872833}, {"X": 0.6575170755386353, "Y": 0.3169156312942505}, {"X": 0.6575168967247009, "Y": 0.3279210031032562}, {"X": 0.6267974376678467, "Y": 0.32792091369628906}]}, "Id": "7883c7d2-9446-4065-9ee1-e127fd964c0a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shift:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06394462287425995, "Height": 0.011428557336330414, "Left": 0.06792221963405609, "Top": 0.3442031443119049}, "Polygon": [{"X": 0.06792241334915161, "Y": 0.3442031443119049}, {"X": 0.13186684250831604, "Y": 0.3442033529281616}, {"X": 0.13186664879322052, "Y": 0.3556317090988159}, {"X": 0.06792221963405609, "Y": 0.3556315004825592}]}, "Id": "8f98a35a-d539-486a-b6a8-3e8d1e2d0933"}, {"BlockType": "WORD", "Confidence": 98.34921264648438, "Text": "1st", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03811126574873924, "Height": 0.011082427576184273, "Left": 0.6268889904022217, "Top": 0.3446553349494934}, "Polygon": [{"X": 0.6268892288208008, "Y": 0.3446553349494934}, {"X": 0.6650002598762512, "Y": 0.34465545415878296}, {"X": 0.6650000810623169, "Y": 0.3557377755641937}, {"X": 0.6268889904022217, "Y": 0.3557376265525818}]}, "Id": "d3c955cc-7a61-4b9b-b6e4-8ac0e9139288"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Shift", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05837273597717285, "Height": 0.011485032737255096, "Left": 0.6731964349746704, "Top": 0.3440997302532196}, "Polygon": [{"X": 0.6731966137886047, "Y": 0.3440997302532196}, {"X": 0.7315691709518433, "Y": 0.34409990906715393}, {"X": 0.7315689921379089, "Y": 0.3555847704410553}, {"X": 0.6731964349746704, "Y": 0.3555845618247986}]}, "Id": "9bb8f619-a5b8-4c2f-870b-017f73bec982"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07872325927019119, "Height": 0.011307629756629467, "Left": 0.06751324981451035, "Top": 0.3917284905910492}, "Polygon": [{"X": 0.06751345098018646, "Y": 0.3917284905910492}, {"X": 0.14623650908470154, "Y": 0.39172878861427307}, {"X": 0.14623631536960602, "Y": 0.4030361473560333}, {"X": 0.06751324981451035, "Y": 0.40303584933280945}]}, "Id": "f875d1ab-e346-4e1b-a3f6-7043df35b8fc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "INFO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07570252567529678, "Height": 0.011300141923129559, "Left": 0.15634867548942566, "Top": 0.3918437361717224}, "Polygon": [{"X": 0.15634886920452118, "Y": 0.3918437361717224}, {"X": 0.23205120861530304, "Y": 0.3918440341949463}, {"X": 0.23205101490020752, "Y": 0.40314388275146484}, {"X": 0.15634867548942566, "Y": 0.40314358472824097}]}, "Id": "061345b1-900d-4435-8268-bf476fd85179"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09611997753381729, "Height": 0.011409152299165726, "Left": 0.06708154082298279, "Top": 0.4195166230201721}, "Polygon": [{"X": 0.06708173453807831, "Y": 0.4195166230201721}, {"X": 0.16320151090621948, "Y": 0.41951701045036316}, {"X": 0.16320131719112396, "Y": 0.43092575669288635}, {"X": 0.06708154082298279, "Y": 0.4309253692626953}]}, "Id": "11cb8147-bf6b-4534-bc81-acfe8f32c73f"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "59", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03218729421496391, "Height": 0.011197906918823719, "Left": 0.6253074407577515, "Top": 0.4198010265827179}, "Polygon": [{"X": 0.6253076195716858, "Y": 0.4198010265827179}, {"X": 0.6574947834014893, "Y": 0.41980114579200745}, {"X": 0.6574945449829102, "Y": 0.43099892139434814}, {"X": 0.6253074407577515, "Y": 0.4309988021850586}]}, "Id": "46ef0d49-e4a5-4b99-85de-e2a00fb0d7e5"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "north", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0647185817360878, "Height": 0.011159615591168404, "Left": 0.6665444374084473, "Top": 0.4197208285331726}, "Polygon": [{"X": 0.6665446162223816, "Y": 0.4197208285331726}, {"X": 0.7312629818916321, "Y": 0.4197210669517517}, {"X": 0.7312628030776978, "Y": 0.43088042736053467}, {"X": 0.6665444374084473, "Y": 0.4308801591396332}]}, "Id": "8a4b83b4-eb66-4d74-9d52-74787cfdf49b"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Vendor:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10209459811449051, "Height": 0.01136750914156437, "Left": 0.06617559492588043, "Top": 0.4472859501838684}, "Polygon": [{"X": 0.06617578864097595, "Y": 0.4472859501838684}, {"X": 0.16827018558979034, "Y": 0.44728636741638184}, {"X": 0.16826999187469482, "Y": 0.45865345001220703}, {"X": 0.06617559492588043, "Y": 0.4586530029773712}]}, "Id": "b5a6414b-9c83-4f3f-998b-f288907dacb5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Amercare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1281566172838211, "Height": 0.01140429638326168, "Left": 0.6242707371711731, "Top": 0.44737857580184937}, "Polygon": [{"X": 0.6242709755897522, "Y": 0.44737857580184937}, {"X": 0.7524273991584778, "Y": 0.44737911224365234}, {"X": 0.7524272203445435, "Y": 0.4587828814983368}, {"X": 0.6242707371711731, "Y": 0.4587823152542114}]}, "Id": "8790e98b-da0d-4cfb-bb23-fd54decdc7bb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Royal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07290571928024292, "Height": 0.014070115983486176, "Left": 0.7621490955352783, "Top": 0.4472489356994629}, "Polygon": [{"X": 0.7621493339538574, "Y": 0.4472489356994629}, {"X": 0.8350548148155212, "Y": 0.44724926352500916}, {"X": 0.8350545763969421, "Y": 0.46131905913352966}, {"X": 0.7621490955352783, "Y": 0.4613187313079834}]}, "Id": "4d5b8a89-b17b-44af-8728-657251176412"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0743144154548645, "Height": 0.011338412761688232, "Left": 0.06626966595649719, "Top": 0.475087434053421}, "Polygon": [{"X": 0.06626986712217331, "Y": 0.475087434053421}, {"X": 0.1405840814113617, "Y": 0.4750877618789673}, {"X": 0.14058388769626617, "Y": 0.48642584681510925}, {"X": 0.06626966595649719, "Y": 0.4864254891872406}]}, "Id": "195e514c-ea7f-43c3-b8ac-b4d7ecfa5984"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "01", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028749730437994003, "Height": 0.011069179512560368, "Left": 0.6250150799751282, "Top": 0.47542041540145874}, "Polygon": [{"X": 0.6250152587890625, "Y": 0.47542041540145874}, {"X": 0.6537647843360901, "Y": 0.4754205644130707}, {"X": 0.6537646055221558, "Y": 0.48648959398269653}, {"X": 0.6250150799751282, "Y": 0.486489474773407}]}, "Id": "0226ae16-b2ae-4574-8b25-205430f402c5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08388730138540268, "Height": 0.011210412718355656, "Left": 0.06640441715717316, "Top": 0.5029498338699341}, "Polygon": [{"X": 0.06640461087226868, "Y": 0.5029498338699341}, {"X": 0.15029171109199524, "Y": 0.5029502511024475}, {"X": 0.15029151737689972, "Y": 0.5141602754592896}, {"X": 0.06640441715717316, "Y": 0.5141598582267761}]}, "Id": "ea37f520-bdf5-4f8e-bb2b-09792d4f25cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "5250234", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11469906568527222, "Height": 0.011033291928470135, "Left": 0.6249483823776245, "Top": 0.5031672120094299}, "Polygon": [{"X": 0.6249485611915588, "Y": 0.5031672120094299}, {"X": 0.7396474480628967, "Y": 0.5031677484512329}, {"X": 0.7396472096443176, "Y": 0.5142005085945129}, {"X": 0.6249483823776245, "Y": 0.5141999125480652}]}, "Id": "ff6e61db-c7ee-4be3-a47e-828b6304f7fc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10282771289348602, "Height": 0.011186067946255207, "Left": 0.06595931947231293, "Top": 0.5504586100578308}, "Polygon": [{"X": 0.06595951318740845, "Y": 0.5504586100578308}, {"X": 0.16878703236579895, "Y": 0.5504591464996338}, {"X": 0.16878683865070343, "Y": 0.5616446733474731}, {"X": 0.06595931947231293, "Y": 0.5616440773010254}]}, "Id": "f87cd479-d863-45a8-8124-7bc465c89816"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052473392337560654, "Height": 0.011303659528493881, "Left": 0.17724353075027466, "Top": 0.5503151416778564}, "Polygon": [{"X": 0.17724372446537018, "Y": 0.5503151416778564}, {"X": 0.2297169268131256, "Y": 0.5503154397010803}, {"X": 0.2297167330980301, "Y": 0.5616188049316406}, {"X": 0.17724353075027466, "Y": 0.5616185069084167}]}, "Id": "e9e539e7-35e3-49a6-be2c-7fb4dcfe93c8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$180.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10625392198562622, "Height": 0.01354591641575098, "Left": 0.6249967813491821, "Top": 0.549399197101593}, "Polygon": [{"X": 0.6249970197677612, "Y": 0.549399197101593}, {"X": 0.7312507033348083, "Y": 0.549399733543396}, {"X": 0.7312504649162292, "Y": 0.5629451274871826}, {"X": 0.6249967813491821, "Y": 0.5629445314407349}]}, "Id": "c8a3e84b-73df-4c81-a79c-85cc8b99ad1f"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17042532563209534, "Height": 0.01125436369329691, "Left": 0.06719447672367096, "Top": 0.5781688094139099}, "Polygon": [{"X": 0.06719467043876648, "Y": 0.5781688094139099}, {"X": 0.2376197874546051, "Y": 0.5781697630882263}, {"X": 0.23761960864067078, "Y": 0.5894231200218201}, {"X": 0.06719447672367096, "Y": 0.5894221663475037}]}, "Id": "86dcc641-611b-4363-92c8-cd7c873b8004"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04895894601941109, "Height": 0.01133816223591566, "Left": 0.24779091775417328, "Top": 0.5780951380729675}, "Polygon": [{"X": 0.2477911114692688, "Y": 0.5780951380729675}, {"X": 0.29674986004829407, "Y": 0.5780953764915466}, {"X": 0.29674965143203735, "Y": 0.5894333124160767}, {"X": 0.24779091775417328, "Y": 0.5894330143928528}]}, "Id": "26631ae2-4f41-4eec-8b20-520d6a0cb299"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$9.75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07284560054540634, "Height": 0.013382857665419579, "Left": 0.6250905990600586, "Top": 0.5773332118988037}, "Polygon": [{"X": 0.6250907778739929, "Y": 0.5773332118988037}, {"X": 0.6979361772537231, "Y": 0.5773336291313171}, {"X": 0.697935938835144, "Y": 0.5907160639762878}, {"X": 0.6250905990600586, "Y": 0.5907156467437744}]}, "Id": "74f7759c-6830-4c8e-809a-ae8a4be34232"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Electronic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12785638868808746, "Height": 0.01126656774431467, "Left": 0.06825127452611923, "Top": 0.6059856414794922}, "Polygon": [{"X": 0.06825147569179535, "Y": 0.6059856414794922}, {"X": 0.1961076557636261, "Y": 0.6059863567352295}, {"X": 0.19610747694969177, "Y": 0.6172521710395813}, {"X": 0.06825127452611923, "Y": 0.617251455783844}]}, "Id": "964455a9-b321-446d-a72a-3ec427b268df"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11594195663928986, "Height": 0.013874590396881104, "Left": 0.2048017829656601, "Top": 0.6059496998786926}, "Polygon": [{"X": 0.204802006483078, "Y": 0.6059496998786926}, {"X": 0.32074373960494995, "Y": 0.6059503555297852}, {"X": 0.32074350118637085, "Y": 0.6198242902755737}, {"X": 0.2048017829656601, "Y": 0.6198235750198364}]}, "Id": "e470adf3-7ec7-4fb9-8b32-f519781631a2"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "$189.75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10612017661333084, "Height": 0.013586929999291897, "Left": 0.6248946189880371, "Top": 0.6048755645751953}, "Polygon": [{"X": 0.6248947978019714, "Y": 0.6048755645751953}, {"X": 0.7310147881507874, "Y": 0.6048761606216431}, {"X": 0.7310145497322083, "Y": 0.6184625029563904}, {"X": 0.6248946189880371, "Y": 0.6184618473052979}]}, "Id": "ddc59bd4-1d5e-4c6e-89e1-aade4b03a1a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BALANCE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13549405336380005, "Height": 0.011493115685880184, "Left": 0.06729398667812347, "Top": 0.6530996561050415}, "Polygon": [{"X": 0.06729418784379959, "Y": 0.6530996561050415}, {"X": 0.20278804004192352, "Y": 0.6531004905700684}, {"X": 0.202787846326828, "Y": 0.6645927429199219}, {"X": 0.06729398667812347, "Y": 0.6645918488502502}]}, "Id": "97a9bc96-3fa1-4213-bedf-0fefd5ab4031"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DUE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059577010571956635, "Height": 0.011466583237051964, "Left": 0.21325628459453583, "Top": 0.6531088948249817}, "Polygon": [{"X": 0.21325647830963135, "Y": 0.6531088948249817}, {"X": 0.27283328771591187, "Y": 0.6531093120574951}, {"X": 0.27283310890197754, "Y": 0.6645755171775818}, {"X": 0.21325628459453583, "Y": 0.6645750999450684}]}, "Id": "97309aac-1241-4298-b514-2c1a498648a5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0735323503613472, "Height": 0.013299154117703438, "Left": 0.6249604821205139, "Top": 0.6525976061820984}, "Polygon": [{"X": 0.624960720539093, "Y": 0.6525976061820984}, {"X": 0.6984928250312805, "Y": 0.6525980234146118}, {"X": 0.6984926462173462, "Y": 0.6658967137336731}, {"X": 0.6249604821205139, "Y": 0.6658962368965149}]}, "Id": "4f4a0484-23b6-4f01-b158-7167b6280afc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DESCRIPTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1993838995695114, "Height": 0.012054544873535633, "Left": 0.06753572821617126, "Top": 0.7001860737800598}, "Polygon": [{"X": 0.06753593683242798, "Y": 0.7001860737800598}, {"X": 0.2669196128845215, "Y": 0.7001874446868896}, {"X": 0.26691940426826477, "Y": 0.7122406363487244}, {"X": 0.06753572821617126, "Y": 0.7122392654418945}]}, "Id": "3f87a222-676b-4bb8-b5a5-9e9ad6cfa864"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040287502110004425, "Height": 0.011627068743109703, "Left": 0.27631476521492004, "Top": 0.7002607583999634}, "Polygon": [{"X": 0.27631497383117676, "Y": 0.7002607583999634}, {"X": 0.31660225987434387, "Y": 0.7002609968185425}, {"X": 0.31660208106040955, "Y": 0.7118878364562988}, {"X": 0.27631476521492004, "Y": 0.711887538433075}]}, "Id": "d04aca46-362d-446c-9792-038b960ee1de"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CHARGES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14449745416641235, "Height": 0.0117886271327734, "Left": 0.3248450458049774, "Top": 0.7003851532936096}, "Polygon": [{"X": 0.32484525442123413, "Y": 0.7003851532936096}, {"X": 0.46934249997138977, "Y": 0.7003861665725708}, {"X": 0.46934232115745544, "Y": 0.7121738195419312}, {"X": 0.3248450458049774, "Y": 0.71217280626297}]}, "Id": "2f4d77c3-bd31-4c5b-9b0f-a9afeed5aee7"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "PL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03499974310398102, "Height": 0.010978816077113152, "Left": 0.06800378859043121, "Top": 0.7284688949584961}, "Polygon": [{"X": 0.06800398230552673, "Y": 0.7284688949584961}, {"X": 0.10300353169441223, "Y": 0.7284691333770752}, {"X": 0.10300334542989731, "Y": 0.7394477128982544}, {"X": 0.06800378859043121, "Y": 0.7394474744796753}]}, "Id": "ec91ddbd-e6c5-40e8-99c2-ee8052cb8d2e"}, {"BlockType": "WORD", "Confidence": 99.52069091796875, "Text": "21-25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07484465837478638, "Height": 0.011266269721090794, "Left": 0.11100166290998459, "Top": 0.728356122970581}, "Polygon": [{"X": 0.11100185662508011, "Y": 0.728356122970581}, {"X": 0.18584631383419037, "Y": 0.728356659412384}, {"X": 0.18584612011909485, "Y": 0.7396223545074463}, {"X": 0.11100166290998459, "Y": 0.7396218180656433}]}, "Id": "60289590-46ae-4989-a47a-90d81f7f91b5"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "Fin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038659870624542236, "Height": 0.011147629469633102, "Left": 0.19569280743598938, "Top": 0.7284335494041443}, "Polygon": [{"X": 0.1956930011510849, "Y": 0.7284335494041443}, {"X": 0.23435267806053162, "Y": 0.7284338474273682}, {"X": 0.2343524843454361, "Y": 0.7395811676979065}, {"X": 0.19569280743598938, "Y": 0.7395809292793274}]}, "Id": "81192a4f-462f-4ef0-bfc9-c8eddf0f4b4f"}, {"BlockType": "WORD", "Confidence": 97.51972961425781, "Text": "Plts", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04666166380047798, "Height": 0.011829815804958344, "Left": 0.24463984370231628, "Top": 0.7281123995780945}, "Polygon": [{"X": 0.244640052318573, "Y": 0.7281123995780945}, {"X": 0.29130151867866516, "Y": 0.7281127572059631}, {"X": 0.29130131006240845, "Y": 0.7399422526359558}, {"X": 0.24463984370231628, "Y": 0.7399418950080872}]}, "Id": "2e6f2146-f189-497f-9e8e-3db24591d89c"}, {"BlockType": "WORD", "Confidence": 84.9192886352539, "Text": "4511576095(Amercare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.30175310373306274, "Height": 0.014096897095441818, "Left": 0.0651758685708046, "Top": 0.7761422991752625}, "Polygon": [{"X": 0.0651761144399643, "Y": 0.7761422991752625}, {"X": 0.36692899465560913, "Y": 0.7761445641517639}, {"X": 0.36692875623703003, "Y": 0.790239155292511}, {"X": 0.0651758685708046, "Y": 0.7902368307113647}]}, "Id": "83c65edc-97ce-4378-85cd-83c692b8f1d4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Royal)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08259562402963638, "Height": 0.014263940043747425, "Left": 0.37683138251304626, "Top": 0.776204526424408}, "Polygon": [{"X": 0.37683162093162537, "Y": 0.776204526424408}, {"X": 0.45942699909210205, "Y": 0.7762051820755005}, {"X": 0.45942676067352295, "Y": 0.7904685139656067}, {"X": 0.37683138251304626, "Y": 0.7904678583145142}]}, "Id": "943473a2-c38b-468d-82d7-dfa5d8fe2408"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "0f767624-4b11-4e5f-895d-ea8d171b9b86", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "0f767624-4b11-4e5f-895d-ea8d171b9b86", "content-type": "application/x-amz-json-1.1", "content-length": "52113", "date": "Thu, 18 Sep 2025 10:33:57 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "77fe9243-f97b-4f66-ae61-64c29c92ff76", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:34:00 GMT", "content-type": "application/json", "content-length": "1571", "connection": "keep-alive", "x-amzn-requestid": "77fe9243-f97b-4f66-ae61-64c29c92ff76"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have one page content. We need to classify. It looks like a receipt from FHI, LLC. The page header says \"RECEIPT\". There's a receipt number, facility, department, door, shift, load info: Carrier, Vendor, Truck, Trailer, Amount Due, convenience fee, electronic payment, balance due. So no invoice, but it's a receipt for a load. The vendor is \"Amercare Royal\" and the carrier is \"59 north\". It could be a \"lumper receipt\" or a \"clear to pay\" or \"receipt\" type. The \"Receipt\" is presumably a lumper receipt? Let's see lumper receipt: \"Invoice or Receipt for services provided (loading/unloading labor).\" Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This receipt has door number, amount due, convenience fee, etc. That's consistent with lumper receipt. No mention of \"PO\" but maybe the lumper receipt can refer to \"PO\" but not necessary. It also says \"Receipt #:\" RA... The doc type is \"lumper_receipt\". Also there's no \"invoice\" because it doesn't have invoice header. So we classify as lumper_receipt. There's no page continuation. So we output one object for page 1."}}}, {"toolUse": {"toolUseId": "tooluse_8xBIdFQZTs6T_kng63KvhA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2320, "outputTokens": 312, "totalTokens": 2632}, "metrics": {"latencyMs": 1425}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/36116464_21_lumper_receipt_4.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nFHI, LLC\nFed. ID #56-2174318\nP.O. Box 546\nFuquay Varina, NC 27526\n919-552-3157\nDate\n08/11/25\nRECEIPT\nReceipt #:\nRAI250811065739\nFACILITY:\nOcala\nDepartment:\nGRO-Ocala\nDoor:\n16\nShift:\n1st Shift\nLOAD INFO:\nCarrier:\n59 north\nVendor:\nAmercare Royal\nTruck\n01\nTrailer\n5250234\nAmount Due\n$180.00\nConvenience Fee\n$9.75\nElectronic Payment\n$189.75\nBALANCE DUE\n$0.00\nDESCRIPTION OF CHARGES\nPL 21-25 Fin Plts\n4511576095(Amercare Royal)\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}