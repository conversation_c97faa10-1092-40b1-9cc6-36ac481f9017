#!/usr/bin/env python3
"""
Script to extract token information from JSON files and create a CSV report.
Reads JSON files from the output folder and extracts inputTokens, outputTokens, and totalTokens.
Creates a CSV file with hyperlinks to the original files.
"""

import json
import csv
import os
import glob
from pathlib import Path


def extract_token_data(json_file_path):
    """
    Extract token data from a JSON file.
    
    Args:
        json_file_path (str): Path to the JSON file
        
    Returns:
        dict: Dictionary containing token data or None if not found
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
            
        # Look for usage data in the JSON structure
        usage_data = None
        
        # Check if usage data exists at the root level
        if 'usage' in data:
            usage_data = data['usage']
        else:
            # Search recursively for usage data
            def find_usage(obj):
                if isinstance(obj, dict):
                    if 'usage' in obj:
                        return obj['usage']
                    for value in obj.values():
                        result = find_usage(value)
                        if result:
                            return result
                elif isinstance(obj, list):
                    for item in obj:
                        result = find_usage(item)
                        if result:
                            return result
                return None
            
            usage_data = find_usage(data)
        
        if usage_data and all(key in usage_data for key in ['inputTokens', 'outputTokens', 'totalTokens']):
            return {
                'inputTokens': usage_data['inputTokens'],
                'outputTokens': usage_data['outputTokens'],
                'totalTokens': usage_data['totalTokens']
            }
        else:
            print(f"Warning: Token data not found in {json_file_path}")
            return None
            
    except Exception as e:
        print(f"Error processing {json_file_path}: {str(e)}")
        return None


def create_csv_report(output_folder, csv_output_path):
    """
    Create a CSV report with token data from all JSON files.
    
    Args:
        output_folder (str): Path to the folder containing JSON files
        csv_output_path (str): Path for the output CSV file
    """
    # Get all JSON files in the output folder
    json_files = glob.glob(os.path.join(output_folder, "*.json"))
    json_files.sort()  # Sort files alphabetically
    
    # Prepare data for CSV
    csv_data = []
    
    for json_file in json_files:
        filename = os.path.basename(json_file)
        file_path = os.path.abspath(json_file)
        
        # Extract token data
        token_data = extract_token_data(json_file)
        
        if token_data:
            # Create hyperlink formula for Excel/LibreOffice
            hyperlink = f'=HYPERLINK("file:///{file_path.replace(os.sep, "/")}", "{filename}")'
            
            csv_data.append({
                'File Name (Hyperlink)': hyperlink,
                'Input Tokens': token_data['inputTokens'],
                'Output Tokens': token_data['outputTokens'],
                'Total Tokens': token_data['totalTokens']
            })
        else:
            # Add entry with N/A values if token data not found
            hyperlink = f'=HYPERLINK("file:///{file_path.replace(os.sep, "/")}", "{filename}")'
            csv_data.append({
                'File Name (Hyperlink)': hyperlink,
                'Input Tokens': 'N/A',
                'Output Tokens': 'N/A',
                'Total Tokens': 'N/A'
            })
    
    # Write CSV file
    if csv_data:
        with open(csv_output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['File Name (Hyperlink)', 'Input Tokens', 'Output Tokens', 'Total Tokens']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            writer.writerows(csv_data)
        
        print(f"CSV report created successfully: {csv_output_path}")
        print(f"Total files processed: {len(json_files)}")
        print(f"Files with token data: {len([d for d in csv_data if d['Input Tokens'] != 'N/A'])}")
    else:
        print("No data to write to CSV file.")


def main():
    """Main function to run the token extraction and CSV creation."""
    # Define paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)  # Go up one level from Augment_test
    output_folder = os.path.join(project_root, "output")
    csv_output_path = os.path.join(current_dir, "token_analysis_report.csv")
    
    # Check if output folder exists
    if not os.path.exists(output_folder):
        print(f"Error: Output folder not found at {output_folder}")
        return
    
    print(f"Processing JSON files from: {output_folder}")
    print(f"CSV output will be saved to: {csv_output_path}")
    
    # Create the CSV report
    create_csv_report(output_folder, csv_output_path)


if __name__ == "__main__":
    main()
