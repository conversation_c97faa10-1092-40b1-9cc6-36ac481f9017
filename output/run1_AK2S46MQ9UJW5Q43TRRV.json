{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 1.675972089287825e-05}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.7894020857056603e-05, "Y": 1.0}]}, "Id": "939e1454-571e-4d65-ab43-98c1cb65e6b8", "Relationships": [{"Type": "CHILD", "Ids": ["1f337f10-7b14-408c-9372-32448a22585c", "0aa36bac-e229-4bab-a433-fbf0b9811aef", "47f88957-ccd0-409a-a678-d039d902d579", "e0845117-1a07-4705-a355-885e728ee530", "db76cf52-399f-4b8b-8c45-2b5b952d3c36", "e1188519-8f5a-4a24-8953-e01b2fac763c", "21d847e7-2ba4-4327-8147-1ad73e9126ab", "d1f27eb9-e67e-433a-abf6-3ca3f62421b7", "c0482445-8875-4af9-a89c-eab2712a5a63", "56b36c0c-5821-4943-938c-88c3e7f93574", "e05d3a06-d250-415c-bbea-bb95c1b781aa", "44078c6c-7f2d-4273-8f8e-44f3480a4ef2", "a1e44271-4b34-42a0-8d24-2af625744d61", "4ee7237b-71c3-4328-8260-60ca1c4f4948", "90c43f4a-aea2-4ab7-b241-369375e2822f", "********-5b7d-4f7a-a800-89f1f47f0dc2", "35b305d7-7b1d-42f9-b757-17f5cd69c276", "5461db34-de7a-4e55-b50c-90cc40505524", "2ef2c41d-8230-4d53-bbb6-fea4ca510ca0", "73a09ba4-72cc-4776-92b2-fb2125f15e03", "7ead7dc6-bf53-48c3-9628-9340b8a6d8c5", "f0391bfb-ba45-4943-92e1-adaa2b359235"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "WEIGHED ON A FAIRBANKS SCALE", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.051155999302864075}, "Polygon": [{"X": 0.*****************, "Y": 0.054760370403528214}, {"X": 0.***************, "Y": 0.051155999302864075}, {"X": 0.***************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "1f337f10-7b14-408c-9372-32448a22585c", "Relationships": [{"Type": "CHILD", "Ids": ["0ee876d4-b541-457c-83b9-86b2832f4afd", "1c708b02-cbd7-46be-9032-b9b5d403f6d4", "35e057a7-4df1-45b9-85b6-e5e49d01a12f", "5cc868d3-c62b-4900-b8ff-c0017b44f60c", "a08c8456-261b-4bd6-8239-0ac1f31349ee"]}]}, {"BlockType": "LINE", "Confidence": 53.***************, "Text": "NAME Restrictions L.L.K.", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.9275404810905457, "Y": 0.*****************}, {"X": 0.9277777075767517, "Y": 0.26759058237075806}, {"X": 0.29679080843925476, "Y": 0.2702781856060028}]}, "Id": "0aa36bac-e229-4bab-a433-fbf0b9811aef", "Relationships": [{"Type": "CHILD", "Ids": ["e22e8140-b219-4cda-b197-e2abeb1e1b32", "4e99b22c-be1c-4eb6-8330-d25d0f0a12fb", "601b9fd6-175e-4fb8-8e9d-a9c1196f3d21"]}]}, {"BlockType": "LINE", "Confidence": 99.0832290649414, "Text": "CUSTOMER'S", "Geometry": {"BoundingBox": {"Width": 0.21217221021652222, "Height": 0.02780269645154476, "Left": 0.0769718736410141, "Top": 0.2508905529975891}, "Polygon": [{"X": 0.0769718736410141, "Y": 0.2517939507961273}, {"X": 0.28904256224632263, "Y": 0.2508905529975891}, {"X": 0.2891440987586975, "Y": 0.27779000997543335}, {"X": 0.07707617431879044, "Y": 0.27869322896003723}]}, "Id": "47f88957-ccd0-409a-a678-d039d902d579", "Relationships": [{"Type": "CHILD", "Ids": ["7e885a4a-9c91-4b50-86ba-7d59dc6ffb0b"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "ADDRESS", "Geometry": {"BoundingBox": {"Width": 0.15492947399616241, "Height": 0.023753739893436432, "Left": 0.0794006735086441, "Top": 0.29004183411598206}, "Polygon": [{"X": 0.0794006735086441, "Y": 0.290701299905777}, {"X": 0.23424236476421356, "Y": 0.29004183411598206}, {"X": 0.23433014750480652, "Y": 0.3131362199783325}, {"X": 0.07949019223451614, "Y": 0.3137955665588379}]}, "Id": "e0845117-1a07-4705-a355-885e728ee530", "Relationships": [{"Type": "CHILD", "Ids": ["caa11e05-685a-4961-b19d-6aadf7a2edc5"]}]}, {"BlockType": "LINE", "Confidence": 99.09378814697266, "Text": "COMMODITY", "Geometry": {"BoundingBox": {"Width": 0.19837404787540436, "Height": 0.02418862096965313, "Left": 0.08304250240325928, "Top": 0.3248714208602905}, "Polygon": [{"X": 0.08304250240325928, "Y": 0.32571569085121155}, {"X": 0.28132835030555725, "Y": 0.3248714208602905}, {"X": 0.28141656517982483, "Y": 0.34821587800979614}, {"X": 0.08313295245170593, "Y": 0.3490600287914276}]}, "Id": "db76cf52-399f-4b8b-8c45-2b5b952d3c36", "Relationships": [{"Type": "CHILD", "Ids": ["a019f19b-be27-4a83-9950-59657ef36dba"]}]}, {"BlockType": "LINE", "Confidence": 99.77379608154297, "Text": "CARRIER", "Geometry": {"BoundingBox": {"Width": 0.14326931536197662, "Height": 0.02209315076470375, "Left": 0.08548152446746826, "Top": 0.36211368441581726}, "Polygon": [{"X": 0.08548152446746826, "Y": 0.3627232015132904}, {"X": 0.22866912186145782, "Y": 0.36211368441581726}, {"X": 0.22875083982944489, "Y": 0.38359737396240234}, {"X": 0.0855647400021553, "Y": 0.3842068314552307}]}, "Id": "e1188519-8f5a-4a24-8953-e01b2fac763c", "Relationships": [{"Type": "CHILD", "Ids": ["92d8562f-9e90-4c24-8fa8-0b0cf8040bfd"]}]}, {"BlockType": "LINE", "Confidence": 87.68873596191406, "Text": "#MExp.", "Geometry": {"BoundingBox": {"Width": 0.23825673758983612, "Height": 0.053189925849437714, "Left": 0.5919123888015747, "Top": 0.3307412564754486}, "Polygon": [{"X": 0.5919123888015747, "Y": 0.3317548930644989}, {"X": 0.8299858570098877, "Y": 0.3307412564754486}, {"X": 0.830169141292572, "Y": 0.3829179108142853}, {"X": 0.5921016931533813, "Y": 0.3839311897754669}]}, "Id": "21d847e7-2ba4-4327-8147-1ad73e9126ab", "Relationships": [{"Type": "CHILD", "Ids": ["5277f247-aa09-4c8c-8c9b-b52c26f0e848"]}]}, {"BlockType": "LINE", "Confidence": 91.64476013183594, "Text": "INBOUND 76380 1b 404566", "Geometry": {"BoundingBox": {"Width": 0.5587156414985657, "Height": 0.03718375414609909, "Left": 0.3716720640659332, "Top": 0.3705885112285614}, "Polygon": [{"X": 0.3716720640659332, "Y": 0.3729662299156189}, {"X": 0.9302671551704407, "Y": 0.3705885112285614}, {"X": 0.9303877353668213, "Y": 0.40539512038230896}, {"X": 0.37180206179618835, "Y": 0.4077722728252411}]}, "Id": "d1f27eb9-e67e-433a-abf6-3ca3f62421b7", "Relationships": [{"Type": "CHILD", "Ids": ["dd1761a7-b587-4fa1-bb2f-c4e9dbb15014", "42edde82-1810-4735-bf3a-7dbd85dd3cb4", "6ae1f199-cf3c-4673-bc87-014413c09f0e", "23f3eb0a-4a17-48ac-b871-7646aa31155e"]}]}, {"BlockType": "LINE", "Confidence": 96.10796356201172, "Text": "LOOP ID 55", "Geometry": {"BoundingBox": {"Width": 0.17299076914787292, "Height": 0.022324658930301666, "Left": 0.369505375623703, "Top": 0.40575194358825684}, "Polygon": [{"X": 0.369505375623703, "Y": 0.4064877927303314}, {"X": 0.5424172878265381, "Y": 0.40575194358825684}, {"X": 0.5424961447715759, "Y": 0.4273408353328705}, {"X": 0.3695860207080841, "Y": 0.4280765950679779}]}, "Id": "c0482445-8875-4af9-a89c-eab2712a5a63", "Relationships": [{"Type": "CHILD", "Ids": ["8da5e391-651f-4d62-8c9e-db9906bc7d6a", "98e792f4-9980-4adc-a427-b47f6c6ac75f", "be823e48-cfad-4b9e-8ded-f2900340137e"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "INBOUND DATE", "Geometry": {"BoundingBox": {"Width": 0.23303453624248505, "Height": 0.020502859726548195, "Left": 0.09393095970153809, "Top": 0.47061046957969666}, "Polygon": [{"X": 0.09393095970153809, "Y": 0.4716014862060547}, {"X": 0.3268921971321106, "Y": 0.47061046957969666}, {"X": 0.32696548104286194, "Y": 0.4901224672794342}, {"X": 0.09400646388530731, "Y": 0.4911133348941803}]}, "Id": "56b36c0c-5821-4943-938c-88c3e7f93574", "Relationships": [{"Type": "CHILD", "Ids": ["4fd12d17-f8d5-45cb-b8a6-f908d4346048", "57abe0c4-1380-4cee-8850-04e562bc9100"]}]}, {"BlockType": "LINE", "Confidence": 85.910400390625, "Text": "9-16-25 TIME", "Geometry": {"BoundingBox": {"Width": 0.23104889690876007, "Height": 0.021739352494478226, "Left": 0.43888014554977417, "Top": 0.4640854299068451}, "Polygon": [{"X": 0.43888014554977417, "Y": 0.4650680124759674}, {"X": 0.6698545217514038, "Y": 0.4640854299068451}, {"X": 0.6699290871620178, "Y": 0.48484233021736145}, {"X": 0.43895700573921204, "Y": 0.4858247935771942}]}, "Id": "e05d3a06-d250-415c-bbea-bb95c1b781aa", "Relationships": [{"Type": "CHILD", "Ids": ["21457314-c249-404c-ae13-e3100f180b76", "b5696ade-4860-4f16-90e7-90478ad86408"]}]}, {"BlockType": "LINE", "Confidence": 93.86399841308594, "Text": "7:39AM", "Geometry": {"BoundingBox": {"Width": 0.10891736298799515, "Height": 0.021476007997989655, "Left": 0.7654374241828918, "Top": 0.45966875553131104}, "Polygon": [{"X": 0.7654374241828918, "Y": 0.46013179421424866}, {"X": 0.8742814660072327, "Y": 0.45966875553131104}, {"X": 0.8743547797203064, "Y": 0.48068177700042725}, {"X": 0.7655119299888611, "Y": 0.4811447560787201}]}, "Id": "44078c6c-7f2d-4273-8f8e-44f3480a4ef2", "Relationships": [{"Type": "CHILD", "Ids": ["1e955bf4-1617-44fa-bba9-9dd4186ef46a"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "OUTBOUND DATE", "Geometry": {"BoundingBox": {"Width": 0.267316073179245, "Height": 0.020721253007650375, "Left": 0.09636086225509644, "Top": 0.5058368444442749}, "Polygon": [{"X": 0.09636086225509644, "Y": 0.506973385810852}, {"X": 0.36360371112823486, "Y": 0.5058368444442749}, {"X": 0.36367693543434143, "Y": 0.525421679019928}, {"X": 0.09643662720918655, "Y": 0.5265581011772156}]}, "Id": "a1e44271-4b34-42a0-8d24-2af625744d61", "Relationships": [{"Type": "CHILD", "Ids": ["ec66e3d2-830a-4753-9f2d-73b8804818c3", "7152c196-1339-4213-820f-0897f197c6d1"]}]}, {"BlockType": "LINE", "Confidence": 99.85759735107422, "Text": "9-18-25 TIME", "Geometry": {"BoundingBox": {"Width": 0.22990314662456512, "Height": 0.02298722229897976, "Left": 0.43850356340408325, "Top": 0.5008900761604309}, "Polygon": [{"X": 0.43850356340408325, "Y": 0.5018675327301025}, {"X": 0.6683276891708374, "Y": 0.5008900761604309}, {"X": 0.6684067249298096, "Y": 0.5228999853134155}, {"X": 0.4385850429534912, "Y": 0.5238772630691528}]}, "Id": "4ee7237b-71c3-4328-8260-60ca1c4f4948", "Relationships": [{"Type": "CHILD", "Ids": ["f53457c2-d6bf-4c17-baf1-fb6941f2dabc", "105ab249-1935-47d5-8b78-430842591416"]}]}, {"BlockType": "LINE", "Confidence": 99.36622619628906, "Text": "9:25AM", "Geometry": {"BoundingBox": {"Width": 0.10725177079439163, "Height": 0.020691225305199623, "Left": 0.7624420523643494, "Top": 0.4986754357814789}, "Polygon": [{"X": 0.7624420523643494, "Y": 0.4991312623023987}, {"X": 0.8696231245994568, "Y": 0.4986754357814789}, {"X": 0.8696938157081604, "Y": 0.5189108848571777}, {"X": 0.7625138163566589, "Y": 0.5193666219711304}]}, "Id": "90c43f4a-aea2-4ab7-b241-369375e2822f", "Relationships": [{"Type": "CHILD", "Ids": ["e2c09684-3524-4c1a-978a-c7a2c7c5c7f4"]}]}, {"BlockType": "LINE", "Confidence": 19.002012252807617, "Text": "IE NETES", "Geometry": {"BoundingBox": {"Width": 0.22207511961460114, "Height": 0.059549182653427124, "Left": 0.5980767011642456, "Top": 0.5556908845901489}, "Polygon": [{"X": 0.5980767011642456, "Y": 0.5566341876983643}, {"X": 0.8199456930160522, "Y": 0.5556908845901489}, {"X": 0.8201518654823303, "Y": 0.6142971515655518}, {"X": 0.5982891917228699, "Y": 0.6152400970458984}]}, "Id": "********-5b7d-4f7a-a800-89f1f47f0dc2", "Relationships": [{"Type": "CHILD", "Ids": ["e8aa803d-cb01-47d2-8355-6141cd1fefb1", "46ba7b3d-06f9-421e-a357-ee4d61639864"]}]}, {"BlockType": "LINE", "Confidence": 99.35639953613281, "Text": "LOOP ID 55", "Geometry": {"BoundingBox": {"Width": 0.16707688570022583, "Height": 0.020099209621548653, "Left": 0.37362122535705566, "Top": 0.6512823104858398}, "Polygon": [{"X": 0.37362122535705566, "Y": 0.6519919037818909}, {"X": 0.5406273007392883, "Y": 0.6512823104858398}, {"X": 0.5406981110572815, "Y": 0.6706719994544983}, {"X": 0.37369364500045776, "Y": 0.6713814735412598}]}, "Id": "35b305d7-7b1d-42f9-b757-17f5cd69c276", "Relationships": [{"Type": "CHILD", "Ids": ["5a269ef9-fc12-4d84-87e5-f82c94f6b17d", "640f059b-0b0e-494c-9179-0613dfd23bf9", "c07b7112-59f5-43af-bf1c-42901c74c993"]}]}, {"BlockType": "LINE", "Confidence": 99.91171264648438, "Text": "DRIVER ON", "Geometry": {"BoundingBox": {"Width": 0.16747787594795227, "Height": 0.017383672297000885, "Left": 0.11037233471870422, "Top": 0.6992537975311279}, "Polygon": [{"X": 0.11037233471870422, "Y": 0.6999649405479431}, {"X": 0.2777871787548065, "Y": 0.6992537975311279}, {"X": 0.2778502106666565, "Y": 0.7159264087677002}, {"X": 0.11043672263622284, "Y": 0.7166374921798706}]}, "Id": "5461db34-de7a-4e55-b50c-90cc40505524", "Relationships": [{"Type": "CHILD", "Ids": ["453e5bdc-a714-41f5-bad3-7228ce3e3b5f", "9d2bb027-e84a-4b8d-bba9-1f122b616429"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "OFF", "Geometry": {"BoundingBox": {"Width": 0.06269090622663498, "Height": 0.016186533495783806, "Left": 0.5325045585632324, "Top": 0.6950268745422363}, "Polygon": [{"X": 0.5325045585632324, "Y": 0.6952929496765137}, {"X": 0.5951377153396606, "Y": 0.6950268745422363}, {"X": 0.595195472240448, "Y": 0.7109473943710327}, {"X": 0.532562792301178, "Y": 0.7112134099006653}]}, "Id": "2ef2c41d-8230-4d53-bbb6-fea4ca510ca0", "Relationships": [{"Type": "CHILD", "Ids": ["16b76ebf-3411-4c01-91ab-0d415678e451"]}]}, {"BlockType": "LINE", "Confidence": 64.19742584228516, "Text": "SHIPPER Je", "Geometry": {"BoundingBox": {"Width": 0.5282754302024841, "Height": 0.0893167033791542, "Left": 0.11577090620994568, "Top": 0.7855818867683411}, "Polygon": [{"X": 0.11577090620994568, "Y": 0.7878233194351196}, {"X": 0.6437325477600098, "Y": 0.7855818867683411}, {"X": 0.6440463662147522, "Y": 0.8726585507392883}, {"X": 0.11610697954893112, "Y": 0.8748986124992371}]}, "Id": "73a09ba4-72cc-4776-92b2-fb2125f15e03", "Relationships": [{"Type": "CHILD", "Ids": ["a245da6f-7207-406a-9d49-5eed84f79b2b", "0215cdb1-9b67-4368-ac78-1211e31d57e3"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "WEIGHER", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "7ead7dc6-bf53-48c3-9628-9340b8a6d8c5", "Relationships": [{"Type": "CHILD", "Ids": ["c8bb7832-2b30-4c06-9f0d-************"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "FAIRBANKS SCALE CAT. 16288", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.015475750900804996, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "f0391bfb-ba45-4943-92e1-adaa2b359235", "Relationships": [{"Type": "CHILD", "Ids": ["35792c69-87bd-4e05-a02c-b439f7f6a69f", "f452ac51-93c8-4aaf-a622-e9dc16f11b33", "0c7f73bf-e8a5-49c7-b18f-7116b71342d4", "4ccc10ee-cc9b-45a4-b951-0e2fa1553ce8"]}]}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "WEIGHED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.*********1549263}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*********1549263}, {"X": 0.3091033101081848, "Y": 0.11045519262552261}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "0ee876d4-b541-457c-83b9-86b2832f4afd"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "ON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07125014066696167, "Height": 0.02945803664624691, "Left": 0.32073119282722473, "Top": 0.07224397361278534}, "Polygon": [{"X": 0.32073119282722473, "Y": 0.07254737615585327}, {"X": 0.39187273383140564, "Y": 0.07224397361278534}, {"X": 0.3919813334941864, "Y": 0.10139867663383484}, {"X": 0.3208407759666443, "Y": 0.1017020121216774}]}, "Id": "1c708b02-cbd7-46be-9032-b9b5d403f6d4"}, {"BlockType": "WORD", "Confidence": 99.5322494506836, "Text": "A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.026934998109936714, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "35e057a7-4df1-45b9-85b6-e5e49d01a12f"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "FAIRBANKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.***************}]}, "Id": "5cc868d3-c62b-4900-b8ff-c0017b44f60c"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.****************, "Top": 0.051155999302864075}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.***************, "Y": 0.051155999302864075}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "a08c8456-261b-4bd6-8239-0ac1f31349ee"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "NAME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09644277393817902, "Height": 0.022096438333392143, "Left": 0.2967073321342468, "Top": 0.24772855639457703}, "Polygon": [{"X": 0.2967073321342468, "Y": 0.24813905358314514}, {"X": 0.393069326877594, "Y": 0.24772855639457703}, {"X": 0.39315009117126465, "Y": 0.2694145441055298}, {"X": 0.29678910970687866, "Y": 0.2698250114917755}]}, "Id": "e22e8140-b219-4cda-b197-e2abeb1e1b32"}, {"BlockType": "WORD", "Confidence": 20.059789657592773, "Text": "Restrictions", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.28809723258018494, "Height": 0.06968706846237183, "Left": 0.40897858142852783, "Top": 0.20011219382286072}, "Polygon": [{"X": 0.40897858142852783, "Y": 0.20133879780769348}, {"X": 0.6968309283256531, "Y": 0.20011219382286072}, {"X": 0.6970757842063904, "Y": 0.26857322454452515}, {"X": 0.4092330038547516, "Y": 0.26979926228523254}]}, "Id": "4e99b22c-be1c-4eb6-8330-d25d0f0a12fb"}, {"BlockType": "WORD", "Confidence": 40.50621795654297, "Text": "L.L.K.", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1991441249847412, "Height": 0.05002044513821602, "Left": 0.7285908460617065, "Top": 0.20608772337436676}, "Polygon": [{"X": 0.7285908460617065, "Y": 0.2069355696439743}, {"X": 0.9275646209716797, "Y": 0.20608772337436676}, {"X": 0.9277349710464478, "Y": 0.2552606165409088}, {"X": 0.7287660241127014, "Y": 0.2561081647872925}]}, "Id": "601b9fd6-175e-4fb8-8e9d-a9c1196f3d21"}, {"BlockType": "WORD", "Confidence": 99.0832290649414, "Text": "CUSTOMER'S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21217221021652222, "Height": 0.02780269645154476, "Left": 0.0769718736410141, "Top": 0.2508905529975891}, "Polygon": [{"X": 0.0769718736410141, "Y": 0.2517939507961273}, {"X": 0.28904256224632263, "Y": 0.2508905529975891}, {"X": 0.2891440987586975, "Y": 0.27779000997543335}, {"X": 0.07707617431879044, "Y": 0.27869322896003723}]}, "Id": "7e885a4a-9c91-4b50-86ba-7d59dc6ffb0b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "ADDRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15492947399616241, "Height": 0.023753739893436432, "Left": 0.0794006735086441, "Top": 0.29004183411598206}, "Polygon": [{"X": 0.0794006735086441, "Y": 0.290701299905777}, {"X": 0.23424236476421356, "Y": 0.29004183411598206}, {"X": 0.23433014750480652, "Y": 0.3131362199783325}, {"X": 0.07949019223451614, "Y": 0.3137955665588379}]}, "Id": "caa11e05-685a-4961-b19d-6aadf7a2edc5"}, {"BlockType": "WORD", "Confidence": 99.09378814697266, "Text": "COMMODITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19837404787540436, "Height": 0.02418862096965313, "Left": 0.08304250240325928, "Top": 0.3248714208602905}, "Polygon": [{"X": 0.08304250240325928, "Y": 0.32571569085121155}, {"X": 0.28132835030555725, "Y": 0.3248714208602905}, {"X": 0.28141656517982483, "Y": 0.34821587800979614}, {"X": 0.08313295245170593, "Y": 0.3490600287914276}]}, "Id": "a019f19b-be27-4a83-9950-59657ef36dba"}, {"BlockType": "WORD", "Confidence": 99.77379608154297, "Text": "CARRIER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14326931536197662, "Height": 0.02209315076470375, "Left": 0.08548152446746826, "Top": 0.36211368441581726}, "Polygon": [{"X": 0.08548152446746826, "Y": 0.3627232015132904}, {"X": 0.22866912186145782, "Y": 0.36211368441581726}, {"X": 0.22875083982944489, "Y": 0.38359737396240234}, {"X": 0.0855647400021553, "Y": 0.3842068314552307}]}, "Id": "92d8562f-9e90-4c24-8fa8-0b0cf8040bfd"}, {"BlockType": "WORD", "Confidence": 87.68873596191406, "Text": "#MExp.", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.23825673758983612, "Height": 0.053189925849437714, "Left": 0.5919123888015747, "Top": 0.3307412564754486}, "Polygon": [{"X": 0.5919123888015747, "Y": 0.3317548930644989}, {"X": 0.8299858570098877, "Y": 0.3307412564754486}, {"X": 0.830169141292572, "Y": 0.3829179108142853}, {"X": 0.5921016931533813, "Y": 0.3839311897754669}]}, "Id": "5277f247-aa09-4c8c-8c9b-b52c26f0e848"}, {"BlockType": "WORD", "Confidence": 88.73185729980469, "Text": "INBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11890461295843124, "Height": 0.020274030044674873, "Left": 0.37172722816467285, "Top": 0.3872321546077728}, "Polygon": [{"X": 0.37172722816467285, "Y": 0.3877379298210144}, {"X": 0.4905591607093811, "Y": 0.3872321546077728}, {"X": 0.4906318485736847, "Y": 0.40700048208236694}, {"X": 0.3718010485172272, "Y": 0.40750619769096375}]}, "Id": "dd1761a7-b587-4fa1-bb2f-c4e9dbb15014"}, {"BlockType": "WORD", "Confidence": 99.53782653808594, "Text": "76380", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08831580728292465, "Height": 0.020596303045749664, "Left": 0.5417365431785583, "Top": 0.38471338152885437}, "Polygon": [{"X": 0.5417365431785583, "Y": 0.3850889503955841}, {"X": 0.6299793720245361, "Y": 0.38471338152885437}, {"X": 0.630052387714386, "Y": 0.4049341380596161}, {"X": 0.5418104529380798, "Y": 0.40530967712402344}]}, "Id": "42edde82-1810-4735-bf3a-7dbd85dd3cb4"}, {"BlockType": "WORD", "Confidence": 94.04834747314453, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036136094480752945, "Height": 0.021828187629580498, "Left": 0.6478595733642578, "Top": 0.38243988156318665}, "Polygon": [{"X": 0.6478595733642578, "Y": 0.3825933635234833}, {"X": 0.6839179992675781, "Y": 0.38243988156318665}, {"X": 0.6839956641197205, "Y": 0.40411463379859924}, {"X": 0.6479375958442688, "Y": 0.4042680859565735}]}, "Id": "6ae1f199-cf3c-4673-bc87-014413c09f0e"}, {"BlockType": "WORD", "Confidence": 84.26100158691406, "Text": "404566", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.20479467511177063, "Height": 0.03567761555314064, "Left": 0.7255930304527283, "Top": 0.3705885112285614}, "Polygon": [{"X": 0.7255930304527283, "Y": 0.3714597225189209}, {"X": 0.9302671551704407, "Y": 0.3705885112285614}, {"X": 0.9303877353668213, "Y": 0.40539512038230896}, {"X": 0.7257170677185059, "Y": 0.40626612305641174}]}, "Id": "23f3eb0a-4a17-48ac-b871-7646aa31155e"}, {"BlockType": "WORD", "Confidence": 93.93714141845703, "Text": "LOOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06983579695224762, "Height": 0.020241497084498405, "Left": 0.36951151490211487, "Top": 0.40783509612083435}, "Polygon": [{"X": 0.36951151490211487, "Y": 0.40813198685646057}, {"X": 0.439273476600647, "Y": 0.40783509612083435}, {"X": 0.4393473267555237, "Y": 0.42777976393699646}, {"X": 0.3695860207080841, "Y": 0.4280765950679779}]}, "Id": "8da5e391-651f-4d62-8c9e-db9906bc7d6a"}, {"BlockType": "WORD", "Confidence": 96.15892028808594, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034149736166000366, "Height": 0.0204426608979702, "Left": 0.4577747583389282, "Top": 0.4068845212459564}, "Polygon": [{"X": 0.4577747583389282, "Y": 0.40702950954437256}, {"X": 0.4918498694896698, "Y": 0.4068845212459564}, {"X": 0.4919244945049286, "Y": 0.4271821677684784}, {"X": 0.4578497111797333, "Y": 0.42732715606689453}]}, "Id": "98e792f4-9980-4adc-a427-b47f6c6ac75f"}, {"BlockType": "WORD", "Confidence": 98.22784423828125, "Text": "55", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03636094182729721, "Height": 0.019868463277816772, "Left": 0.5061283707618713, "Top": 0.40575194358825684}, "Polygon": [{"X": 0.5061283707618713, "Y": 0.4059063792228699}, {"X": 0.5424172878265381, "Y": 0.40575194358825684}, {"X": 0.5424892902374268, "Y": 0.42546600103378296}, {"X": 0.5062006711959839, "Y": 0.4256204068660736}]}, "Id": "be823e48-cfad-4b9e-8ded-f2900340137e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "INBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1448439210653305, "Height": 0.018939638510346413, "Left": 0.09393555670976639, "Top": 0.47217369079589844}, "Polygon": [{"X": 0.09393555670976639, "Y": 0.47278955578804016}, {"X": 0.23870986700057983, "Y": 0.47217369079589844}, {"X": 0.2387794852256775, "Y": 0.49049755930900574}, {"X": 0.09400646388530731, "Y": 0.4911133348941803}]}, "Id": "4fd12d17-f8d5-45cb-b8a6-f908d4346048"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07936108857393265, "Height": 0.017544113099575043, "Left": 0.24759574234485626, "Top": 0.47061046957969666}, "Polygon": [{"X": 0.24759574234485626, "Y": 0.470947802066803}, {"X": 0.3268921971321106, "Y": 0.47061046957969666}, {"X": 0.3269568383693695, "Y": 0.48781728744506836}, {"X": 0.2476610541343689, "Y": 0.4881545901298523}]}, "Id": "57abe0c4-1380-4cee-8850-04e562bc9100"}, {"BlockType": "WORD", "Confidence": 71.82079315185547, "Text": "9-16-25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12108170986175537, "Height": 0.021271536126732826, "Left": 0.43888014554977417, "Top": 0.46455323696136475}, "Polygon": [{"X": 0.43888014554977417, "Y": 0.4650680124759674}, {"X": 0.5598862171173096, "Y": 0.46455323696136475}, {"X": 0.5599618554115295, "Y": 0.48531007766723633}, {"X": 0.43895700573921204, "Y": 0.4858247935771942}]}, "Id": "21457314-c249-404c-ae13-e3100f180b76"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07722266763448715, "Height": 0.017674855887889862, "Left": 0.5926954746246338, "Top": 0.4644555151462555}, "Polygon": [{"X": 0.5926954746246338, "Y": 0.46478378772735596}, {"X": 0.6698558330535889, "Y": 0.4644555151462555}, {"X": 0.6699181199073792, "Y": 0.48180216550827026}, {"X": 0.5927584171295166, "Y": 0.48213037848472595}]}, "Id": "b5696ade-4860-4f16-90e7-90478ad86408"}, {"BlockType": "WORD", "Confidence": 93.86399841308594, "Text": "7:39AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10891736298799515, "Height": 0.021476007997989655, "Left": 0.7654374241828918, "Top": 0.45966875553131104}, "Polygon": [{"X": 0.7654374241828918, "Y": 0.46013179421424866}, {"X": 0.8742814660072327, "Y": 0.45966875553131104}, {"X": 0.8743547797203064, "Y": 0.48068177700042725}, {"X": 0.7655119299888611, "Y": 0.4811447560787201}]}, "Id": "1e955bf4-1617-44fa-bba9-9dd4186ef46a"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "OUTBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17971916496753693, "Height": 0.01928766258060932, "Left": 0.09636496752500534, "Top": 0.5072703957557678}, "Polygon": [{"X": 0.09636496752500534, "Y": 0.5080344676971436}, {"X": 0.2760140895843506, "Y": 0.5072703957557678}, {"X": 0.27608412504196167, "Y": 0.5257941484451294}, {"X": 0.09643662720918655, "Y": 0.5265581011772156}]}, "Id": "ec66e3d2-830a-4753-9f2d-73b8804818c3"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07902541011571884, "Height": 0.01753833331167698, "Left": 0.28464260697364807, "Top": 0.5058368444442749}, "Polygon": [{"X": 0.28464260697364807, "Y": 0.5061726570129395}, {"X": 0.36360371112823486, "Y": 0.5058368444442749}, {"X": 0.3636680245399475, "Y": 0.5230394005775452}, {"X": 0.28470757603645325, "Y": 0.5233751535415649}]}, "Id": "7152c196-1339-4213-820f-0897f197c6d1"}, {"BlockType": "WORD", "Confidence": 99.72496795654297, "Text": "9-18-25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12021157890558243, "Height": 0.02038779854774475, "Left": 0.438511461019516, "Top": 0.5034894943237305}, "Polygon": [{"X": 0.438511461019516, "Y": 0.5040004253387451}, {"X": 0.5586506128311157, "Y": 0.5034894943237305}, {"X": 0.5587230324745178, "Y": 0.523366391658783}, {"X": 0.4385850429534912, "Y": 0.5238772630691528}]}, "Id": "f53457c2-d6bf-4c17-baf1-fb6941f2dabc"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TIME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07614395022392273, "Height": 0.01732075773179531, "Left": 0.5922447443008423, "Top": 0.5008900761604309}, "Polygon": [{"X": 0.5922447443008423, "Y": 0.5012136697769165}, {"X": 0.6683276891708374, "Y": 0.5008900761604309}, {"X": 0.6683887243270874, "Y": 0.51788729429245}, {"X": 0.5923064351081848, "Y": 0.5182108283042908}]}, "Id": "105ab249-1935-47d5-8b78-430842591416"}, {"BlockType": "WORD", "Confidence": 99.36622619628906, "Text": "9:25AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10725177079439163, "Height": 0.020691225305199623, "Left": 0.7624420523643494, "Top": 0.4986754357814789}, "Polygon": [{"X": 0.7624420523643494, "Y": 0.4991312623023987}, {"X": 0.8696231245994568, "Y": 0.4986754357814789}, {"X": 0.8696938157081604, "Y": 0.5189108848571777}, {"X": 0.7625138163566589, "Y": 0.5193666219711304}]}, "Id": "e2c09684-3524-4c1a-978a-c7a2c7c5c7f4"}, {"BlockType": "WORD", "Confidence": 22.39776039123535, "Text": "IE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05365265905857086, "Height": 0.05754554644227028, "Left": 0.5980814099311829, "Top": 0.5576945543289185}, "Polygon": [{"X": 0.5980814099311829, "Y": 0.5579217672348022}, {"X": 0.6515277028083801, "Y": 0.5576945543289185}, {"X": 0.6517340540885925, "Y": 0.6150129437446594}, {"X": 0.5982891917228699, "Y": 0.6152400970458984}]}, "Id": "e8aa803d-cb01-47d2-8355-6141cd1fefb1"}, {"BlockType": "WORD", "Confidence": 15.606266021728516, "Text": "NETES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1436760276556015, "Height": 0.0579805001616478, "Left": 0.676471471786499, "Top": 0.5556908845901489}, "Polygon": [{"X": 0.676471471786499, "Y": 0.5563008785247803}, {"X": 0.8199456930160522, "Y": 0.5556908845901489}, {"X": 0.8201475143432617, "Y": 0.6130616664886475}, {"X": 0.6766772866249084, "Y": 0.6136713624000549}]}, "Id": "46ba7b3d-06f9-421e-a357-ee4d61639864"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "LOOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06997174769639969, "Height": 0.018855538219213486, "Left": 0.373624324798584, "Top": 0.6525259613990784}, "Polygon": [{"X": 0.373624324798584, "Y": 0.6528229713439941}, {"X": 0.4435274004936218, "Y": 0.6525259613990784}, {"X": 0.4435960650444031, "Y": 0.6710845232009888}, {"X": 0.37369364500045776, "Y": 0.6713814735412598}]}, "Id": "5a269ef9-fc12-4d84-87e5-f82c94f6b17d"}, {"BlockType": "WORD", "Confidence": 99.53044891357422, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03286577761173248, "Height": 0.018615126609802246, "Left": 0.4588856101036072, "Top": 0.6523148417472839}, "Polygon": [{"X": 0.4588856101036072, "Y": 0.6524541974067688}, {"X": 0.4916834533214569, "Y": 0.6523148417472839}, {"X": 0.49175140261650085, "Y": 0.6707906126976013}, {"X": 0.458953857421875, "Y": 0.6709299683570862}]}, "Id": "640f059b-0b0e-494c-9179-0613dfd23bf9"}, {"BlockType": "WORD", "Confidence": 98.84705352783203, "Text": "55", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03482988849282265, "Height": 0.018667234107851982, "Left": 0.5058650374412537, "Top": 0.6512823104858398}, "Polygon": [{"X": 0.5058650374412537, "Y": 0.6514300107955933}, {"X": 0.5406273007392883, "Y": 0.6512823104858398}, {"X": 0.5406949520111084, "Y": 0.6698018312454224}, {"X": 0.5059330463409424, "Y": 0.6699495315551758}]}, "Id": "c07b7112-59f5-43af-bf1c-42901c74c993"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "DRIVER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11447014659643173, "Height": 0.01644943654537201, "Left": 0.11037507653236389, "Top": 0.7001880407333374}, "Polygon": [{"X": 0.11037507653236389, "Y": 0.7006740570068359}, {"X": 0.2247844636440277, "Y": 0.7001880407333374}, {"X": 0.22484523057937622, "Y": 0.7161515951156616}, {"X": 0.11043672263622284, "Y": 0.7166374921798706}]}, "Id": "453e5bdc-a714-41f5-bad3-7228ce3e3b5f"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "ON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04562987759709358, "Height": 0.01601373217999935, "Left": 0.23221711814403534, "Top": 0.6992537975311279}, "Polygon": [{"X": 0.23221711814403534, "Y": 0.6994473934173584}, {"X": 0.2777871787548065, "Y": 0.6992537975311279}, {"X": 0.2778469920158386, "Y": 0.7150740027427673}, {"X": 0.2322772741317749, "Y": 0.715267539024353}]}, "Id": "9d2bb027-e84a-4b8d-bba9-1f122b616429"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06269090622663498, "Height": 0.016186533495783806, "Left": 0.5325045585632324, "Top": 0.6950268745422363}, "Polygon": [{"X": 0.5325045585632324, "Y": 0.6952929496765137}, {"X": 0.5951377153396606, "Y": 0.6950268745422363}, {"X": 0.595195472240448, "Y": 0.7109473943710327}, {"X": 0.532562792301178, "Y": 0.7112134099006653}]}, "Id": "16b76ebf-3411-4c01-91ab-0d415678e451"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "SHIPPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1319163590669632, "Height": 0.017822271212935448, "Left": 0.11595365405082703, "Top": 0.8346143960952759}, "Polygon": [{"X": 0.11595365405082703, "Y": 0.835174024105072}, {"X": 0.24780449271202087, "Y": 0.8346143960952759}, {"X": 0.24787001311779022, "Y": 0.8518771529197693}, {"X": 0.11602028459310532, "Y": 0.8524366617202759}]}, "Id": "a245da6f-7207-406a-9d49-5eed84f79b2b"}, {"BlockType": "WORD", "Confidence": 28.48274040222168, "Text": "Je", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.462805837392807, "Height": 0.0890389233827591, "Left": 0.181240513920784, "Top": 0.7855818867683411}, "Polygon": [{"X": 0.181240513920784, "Y": 0.7875453233718872}, {"X": 0.6437325477600098, "Y": 0.7855818867683411}, {"X": 0.6440463662147522, "Y": 0.8726585507392883}, {"X": 0.18157382309436798, "Y": 0.874620795249939}]}, "Id": "0215cdb1-9b67-4368-ac78-1211e31d57e3"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "WEIGHER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "c8bb7832-2b30-4c06-9f0d-************"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "FAIRBANKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.014316725544631481, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "35792c69-87bd-4e05-a02c-b439f7f6a69f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.013370881788432598, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "f452ac51-93c8-4aaf-a622-e9dc16f11b33"}, {"BlockType": "WORD", "Confidence": 99.*********11719, "Text": "CAT.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05164675787091255, "Height": 0.013436364009976387, "Left": 0.7607194185256958, "Top": 0.9181668162345886}, "Polygon": [{"X": 0.7607194185256958, "Y": 0.9183857440948486}, {"X": 0.8123196363449097, "Y": 0.9181668162345886}, {"X": 0.8123661875724792, "Y": 0.9313843250274658}, {"X": 0.760766327381134, "Y": 0.931603193283081}]}, "Id": "0c7f73bf-e8a5-49c7-b18f-7116b71342d4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "16288", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06940919905900955, "Height": 0.013425040990114212, "Left": 0.8244966268539429, "Top": 0.918586254119873}, "Polygon": [{"X": 0.8244966268539429, "Y": 0.9188804626464844}, {"X": 0.8938601016998291, "Y": 0.918586254119873}, {"X": 0.8939058184623718, "Y": 0.9317170977592468}, {"X": 0.8245428204536438, "Y": 0.9320113062858582}]}, "Id": "4ccc10ee-cc9b-45a4-b951-0e2fa1553ce8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "13876d93-4ddf-41d8-b0ce-fad3f3205350", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "13876d93-4ddf-41d8-b0ce-fad3f3205350", "content-type": "application/x-amz-json-1.1", "content-length": "35435", "date": "Wed, 24 Sep 2025 12:46:11 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "30c4817a-9e68-49d5-9f51-52c1c5ed8b36", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:13 GMT", "content-type": "application/json", "content-length": "691", "connection": "keep-alive", "x-amzn-requestid": "30c4817a-9e68-49d5-9f51-52c1c5ed8b36"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have single page. Text indicates \"WEIGHED ON A FAIRBANKS SCALE\" and contains weight-related keywords: WEIGHED, SCALE, etc. Also contains \"Gross\", \"Net\" maybe not present but weight context. The page is likely a Scale Ticket. So doc_type \"scale_ticket\". Page 1. No continuation indicator. So output array with one object: page_no 1, doc_type: \"scale_ticket\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2644, "outputTokens": 107, "totalTokens": 2751}, "metrics": {"latencyMs": 738}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/4cdc58a8_AK2S46MQ9UJW5Q43TRRV.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nWEIG<PERSON>ED ON A FAIRBANKS SCALE\nNAME Restrictions L.L.K.\nCUSTOMER'S\nADDRESS\nCOMMODITY\nCARRIER\n#MExp.\nINBOUND 76380 1b 404566\nLOOP ID 55\nINBOUND DATE\n9-16-25 TIME\n7:39AM\nOUTBOUND DATE\n9-18-25 TIME\n9:25AM\nIE NETES\nLOOP ID 55\nDRIVER ON\nOFF\nSHIPPER Je\nWEIGHER\nFAIRBANKS SCALE CAT. 16288\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}