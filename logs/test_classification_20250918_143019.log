2025-09-18 14:30:19,822 - INFO - Logging initialized. Log file: logs/test_classification_20250918_143019.log
2025-09-18 14:30:19,823 - INFO - 📁 Found 11 files to process
2025-09-18 14:30:19,823 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:30:19,823 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:30:19,823 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:30:19,823 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:30:19,823 - INFO - ⬆️ [14:30:19] Uploading: 8_log_1.pdf
2025-09-18 14:30:21,702 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_1.pdf -> s3://document-extraction-logistically/temp/29968f83_8_log_1.pdf
2025-09-18 14:30:21,702 - INFO - 🔍 [14:30:21] Starting classification: 8_log_1.pdf
2025-09-18 14:30:21,703 - INFO - ⬆️ [14:30:21] Uploading: 8_log_10.jpeg
2025-09-18 14:30:21,704 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:21,726 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:21,732 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/29968f83_8_log_1.pdf
2025-09-18 14:30:21,733 - INFO - Processing PDF from S3...
2025-09-18 14:30:21,733 - INFO - Downloading PDF from S3 to /tmp/tmpbt4d_nwk/29968f83_8_log_1.pdf
2025-09-18 14:30:23,180 - INFO - Splitting PDF into individual pages...
2025-09-18 14:30:23,182 - INFO - Splitting PDF 29968f83_8_log_1 into 3 pages
2025-09-18 14:30:23,187 - INFO - Split PDF into 3 pages
2025-09-18 14:30:23,187 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:30:23,187 - INFO - Expected pages: [1, 2, 3]
2025-09-18 14:30:23,991 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_10.jpeg -> s3://document-extraction-logistically/temp/4afc3411_8_log_10.jpeg
2025-09-18 14:30:23,992 - INFO - 🔍 [14:30:23] Starting classification: 8_log_10.jpeg
2025-09-18 14:30:23,992 - INFO - ⬆️ [14:30:23] Uploading: 8_log_11.jpg
2025-09-18 14:30:23,994 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:24,009 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:24,012 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4afc3411_8_log_10.jpeg
2025-09-18 14:30:24,012 - INFO - Processing image from S3...
2025-09-18 14:30:25,924 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_11.jpg -> s3://document-extraction-logistically/temp/b22484a2_8_log_11.jpg
2025-09-18 14:30:25,924 - INFO - 🔍 [14:30:25] Starting classification: 8_log_11.jpg
2025-09-18 14:30:25,925 - INFO - ⬆️ [14:30:25] Uploading: 8_log_2.jpeg
2025-09-18 14:30:25,928 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:25,946 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:25,951 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b22484a2_8_log_11.jpg
2025-09-18 14:30:25,951 - INFO - Processing image from S3...
2025-09-18 14:30:27,049 - INFO - Page 3: Extracted 804 characters, 46 lines from 29968f83_8_log_1_5498f618_page_003.pdf
2025-09-18 14:30:27,049 - INFO - Successfully processed page 3
2025-09-18 14:30:27,275 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_2.jpeg -> s3://document-extraction-logistically/temp/306cdafb_8_log_2.jpeg
2025-09-18 14:30:27,275 - INFO - 🔍 [14:30:27] Starting classification: 8_log_2.jpeg
2025-09-18 14:30:27,276 - INFO - ⬆️ [14:30:27] Uploading: 8_log_3.jpeg
2025-09-18 14:30:27,278 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:27,295 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:27,299 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/306cdafb_8_log_2.jpeg
2025-09-18 14:30:27,299 - INFO - Processing image from S3...
2025-09-18 14:30:28,218 - INFO - Page 1: Extracted 535 characters, 49 lines from 29968f83_8_log_1_5498f618_page_001.pdf
2025-09-18 14:30:28,218 - INFO - Successfully processed page 1
2025-09-18 14:30:28,292 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_3.jpeg -> s3://document-extraction-logistically/temp/a9d888ca_8_log_3.jpeg
2025-09-18 14:30:28,292 - INFO - 🔍 [14:30:28] Starting classification: 8_log_3.jpeg
2025-09-18 14:30:28,292 - INFO - ⬆️ [14:30:28] Uploading: 8_log_4.jpeg
2025-09-18 14:30:28,293 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:28,305 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:28,308 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a9d888ca_8_log_3.jpeg
2025-09-18 14:30:28,308 - INFO - Processing image from S3...
2025-09-18 14:30:28,603 - INFO - S3 Image temp/4afc3411_8_log_10.jpeg: Extracted 526 characters, 56 lines
2025-09-18 14:30:28,604 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:28,604 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:29,269 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_4.jpeg -> s3://document-extraction-logistically/temp/fec6f403_8_log_4.jpeg
2025-09-18 14:30:29,269 - INFO - 🔍 [14:30:29] Starting classification: 8_log_4.jpeg
2025-09-18 14:30:29,270 - INFO - ⬆️ [14:30:29] Uploading: 8_log_5.png
2025-09-18 14:30:29,272 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:29,293 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:29,306 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fec6f403_8_log_4.jpeg
2025-09-18 14:30:29,310 - INFO - Page 2: Extracted 492 characters, 33 lines from 29968f83_8_log_1_5498f618_page_002.pdf
2025-09-18 14:30:29,311 - INFO - Processing image from S3...
2025-09-18 14:30:29,311 - INFO - Successfully processed page 2
2025-09-18 14:30:29,317 - INFO - Combined 3 pages into final text
2025-09-18 14:30:29,318 - INFO - Text validation for 29968f83_8_log_1: 1886 characters, 3 pages
2025-09-18 14:30:29,319 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:29,319 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:30,093 - INFO - S3 Image temp/b22484a2_8_log_11.jpg: Extracted 1060 characters, 100 lines
2025-09-18 14:30:30,093 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:30,093 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:30,672 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_5.png -> s3://document-extraction-logistically/temp/168b7bbb_8_log_5.png
2025-09-18 14:30:30,672 - INFO - 🔍 [14:30:30] Starting classification: 8_log_5.png
2025-09-18 14:30:30,673 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:30,674 - INFO - ⬆️ [14:30:30] Uploading: 8_log_6.jpeg
2025-09-18 14:30:30,691 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:30,695 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/168b7bbb_8_log_5.png
2025-09-18 14:30:30,695 - INFO - Processing image from S3...
2025-09-18 14:30:31,023 - INFO - S3 Image temp/a9d888ca_8_log_3.jpeg: Extracted 261 characters, 24 lines
2025-09-18 14:30:31,023 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:31,023 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:31,381 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4afc3411_8_log_10.jpeg
2025-09-18 14:30:31,706 - INFO - S3 Image temp/306cdafb_8_log_2.jpeg: Extracted 485 characters, 49 lines
2025-09-18 14:30:31,706 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:31,706 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:32,294 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_6.jpeg -> s3://document-extraction-logistically/temp/90100115_8_log_6.jpeg
2025-09-18 14:30:32,294 - INFO - 🔍 [14:30:32] Starting classification: 8_log_6.jpeg
2025-09-18 14:30:32,295 - INFO - ⬆️ [14:30:32] Uploading: 8_log_7.pdf
2025-09-18 14:30:32,297 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:32,314 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:32,317 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/90100115_8_log_6.jpeg
2025-09-18 14:30:32,317 - INFO - Processing image from S3...
2025-09-18 14:30:32,329 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/29968f83_8_log_1.pdf
2025-09-18 14:30:32,638 - INFO - S3 Image temp/fec6f403_8_log_4.jpeg: Extracted 469 characters, 51 lines
2025-09-18 14:30:32,638 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:32,639 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:32,699 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b22484a2_8_log_11.jpg
2025-09-18 14:30:32,951 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_7.pdf -> s3://document-extraction-logistically/temp/7749dae0_8_log_7.pdf
2025-09-18 14:30:32,951 - INFO - 🔍 [14:30:32] Starting classification: 8_log_7.pdf
2025-09-18 14:30:32,951 - INFO - ⬆️ [14:30:32] Uploading: 8_log_8.jpeg
2025-09-18 14:30:32,952 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:32,962 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:32,967 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7749dae0_8_log_7.pdf
2025-09-18 14:30:32,967 - INFO - Processing PDF from S3...
2025-09-18 14:30:32,967 - INFO - Downloading PDF from S3 to /tmp/tmpk2goqunr/7749dae0_8_log_7.pdf
2025-09-18 14:30:33,158 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a9d888ca_8_log_3.jpeg
2025-09-18 14:30:33,675 - INFO - S3 Image temp/168b7bbb_8_log_5.png: Extracted 328 characters, 39 lines
2025-09-18 14:30:33,675 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:33,675 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:34,411 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/306cdafb_8_log_2.jpeg
2025-09-18 14:30:34,512 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_8.jpeg -> s3://document-extraction-logistically/temp/2d5960ff_8_log_8.jpeg
2025-09-18 14:30:34,512 - INFO - 🔍 [14:30:34] Starting classification: 8_log_8.jpeg
2025-09-18 14:30:34,513 - INFO - ⬆️ [14:30:34] Uploading: 8_log_9.jpeg
2025-09-18 14:30:34,515 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:34,531 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:34,536 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2d5960ff_8_log_8.jpeg
2025-09-18 14:30:34,536 - INFO - Processing image from S3...
2025-09-18 14:30:35,434 - INFO - Splitting PDF into individual pages...
2025-09-18 14:30:35,435 - INFO - Splitting PDF 7749dae0_8_log_7 into 1 pages
2025-09-18 14:30:35,439 - INFO - Split PDF into 1 pages
2025-09-18 14:30:35,439 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:30:35,439 - INFO - Expected pages: [1]
2025-09-18 14:30:35,446 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fec6f403_8_log_4.jpeg
2025-09-18 14:30:35,823 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_9.jpeg -> s3://document-extraction-logistically/temp/c3f4fb51_8_log_9.jpeg
2025-09-18 14:30:35,824 - INFO - 🔍 [14:30:35] Starting classification: 8_log_9.jpeg
2025-09-18 14:30:35,825 - INFO - Initializing TextractProcessor...
2025-09-18 14:30:35,852 - INFO - Initializing BedrockProcessor...
2025-09-18 14:30:35,859 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c3f4fb51_8_log_9.jpeg
2025-09-18 14:30:35,865 - INFO - Processing image from S3...
2025-09-18 14:30:35,872 - INFO - 

8_log_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-18 14:30:35,872 - INFO - 

✓ Saved result: output/run1_8_log_10.json
2025-09-18 14:30:36,175 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4afc3411_8_log_10.jpeg
2025-09-18 14:30:36,207 - INFO - 

8_log_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:30:36,207 - INFO - 

✓ Saved result: output/run1_8_log_1.json
2025-09-18 14:30:36,411 - INFO - S3 Image temp/90100115_8_log_6.jpeg: Extracted 548 characters, 53 lines
2025-09-18 14:30:36,412 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:36,412 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:36,517 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/29968f83_8_log_1.pdf
2025-09-18 14:30:36,543 - INFO - 

8_log_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-18 14:30:36,543 - INFO - 

✓ Saved result: output/run1_8_log_11.json
2025-09-18 14:30:36,824 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/168b7bbb_8_log_5.png
2025-09-18 14:30:36,845 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b22484a2_8_log_11.jpg
2025-09-18 14:30:36,856 - INFO - 

8_log_3.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-18 14:30:36,856 - INFO - 

✓ Saved result: output/run1_8_log_3.json
2025-09-18 14:30:37,159 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a9d888ca_8_log_3.jpeg
2025-09-18 14:30:37,174 - INFO - 

8_log_2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:30:37,175 - INFO - 

✓ Saved result: output/run1_8_log_2.json
2025-09-18 14:30:37,478 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/306cdafb_8_log_2.jpeg
2025-09-18 14:30:37,495 - INFO - 

8_log_4.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:30:37,496 - INFO - 

✓ Saved result: output/run1_8_log_4.json
2025-09-18 14:30:37,800 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fec6f403_8_log_4.jpeg
2025-09-18 14:30:37,817 - INFO - 

8_log_5.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:30:37,817 - INFO - 

✓ Saved result: output/run1_8_log_5.json
2025-09-18 14:30:38,113 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/168b7bbb_8_log_5.png
2025-09-18 14:30:38,325 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/90100115_8_log_6.jpeg
2025-09-18 14:30:38,334 - INFO - 

8_log_6.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:30:38,334 - INFO - 

✓ Saved result: output/run1_8_log_6.json
2025-09-18 14:30:38,638 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/90100115_8_log_6.jpeg
2025-09-18 14:30:38,734 - INFO - S3 Image temp/2d5960ff_8_log_8.jpeg: Extracted 416 characters, 42 lines
2025-09-18 14:30:38,735 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:38,735 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:39,967 - INFO - S3 Image temp/c3f4fb51_8_log_9.jpeg: Extracted 448 characters, 40 lines
2025-09-18 14:30:39,967 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:39,967 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:40,823 - INFO - Page 1: Extracted 519 characters, 34 lines from 7749dae0_8_log_7_c4c6acfb_page_001.pdf
2025-09-18 14:30:40,823 - INFO - Successfully processed page 1
2025-09-18 14:30:40,823 - INFO - Combined 1 pages into final text
2025-09-18 14:30:40,823 - INFO - Text validation for 7749dae0_8_log_7: 536 characters, 1 pages
2025-09-18 14:30:40,823 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:30:40,823 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:30:40,988 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2d5960ff_8_log_8.jpeg
2025-09-18 14:30:41,008 - INFO - 

8_log_8.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-18 14:30:41,008 - INFO - 

✓ Saved result: output/run1_8_log_8.json
2025-09-18 14:30:41,312 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2d5960ff_8_log_8.jpeg
2025-09-18 14:30:42,309 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c3f4fb51_8_log_9.jpeg
2025-09-18 14:30:42,327 - INFO - 

8_log_9.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:30:42,327 - INFO - 

✓ Saved result: output/run1_8_log_9.json
2025-09-18 14:30:42,630 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c3f4fb51_8_log_9.jpeg
2025-09-18 14:30:42,949 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7749dae0_8_log_7.pdf
2025-09-18 14:30:42,966 - INFO - 

8_log_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-18 14:30:42,966 - INFO - 

✓ Saved result: output/run1_8_log_7.json
2025-09-18 14:30:43,268 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7749dae0_8_log_7.pdf
2025-09-18 14:30:43,269 - INFO - 
📊 Processing Summary:
2025-09-18 14:30:43,269 - INFO -    Total files: 11
2025-09-18 14:30:43,269 - INFO -    Successful: 11
2025-09-18 14:30:43,269 - INFO -    Failed: 0
2025-09-18 14:30:43,269 - INFO -    Duration: 23.45 seconds
2025-09-18 14:30:43,269 - INFO -    Output directory: output
2025-09-18 14:30:43,269 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:30:43,270 - INFO -    📄 8_log_1.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_10.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_11.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_3.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_4.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_5.png: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_6.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_7.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_8.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-18 14:30:43,270 - INFO -    📄 8_log_9.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:30:43,271 - INFO - 
============================================================================================================================================
2025-09-18 14:30:43,271 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:30:43,271 - INFO - ============================================================================================================================================
2025-09-18 14:30:43,271 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:30:43,271 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:30:43,271 - INFO - 8_log_1.pdf                                        1      other                run1_8_log_1.json                                 
2025-09-18 14:30:43,271 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_1.pdf
2025-09-18 14:30:43,272 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 14:30:43,272 - INFO - 
2025-09-18 14:30:43,272 - INFO - 8_log_1.pdf                                        2      other                run1_8_log_1.json                                 
2025-09-18 14:30:43,272 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_1.pdf
2025-09-18 14:30:43,272 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 14:30:43,272 - INFO - 
2025-09-18 14:30:43,272 - INFO - 8_log_1.pdf                                        3      other                run1_8_log_1.json                                 
2025-09-18 14:30:43,272 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_1.pdf
2025-09-18 14:30:43,272 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 14:30:43,272 - INFO - 
2025-09-18 14:30:43,272 - INFO - 8_log_10.jpeg                                      1      log                  run1_8_log_10.json                                
2025-09-18 14:30:43,272 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_10.jpeg
2025-09-18 14:30:43,272 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_10.json
2025-09-18 14:30:43,272 - INFO - 
2025-09-18 14:30:43,272 - INFO - 8_log_11.jpg                                       1      log                  run1_8_log_11.json                                
2025-09-18 14:30:43,272 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_11.jpg
2025-09-18 14:30:43,272 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_11.json
2025-09-18 14:30:43,272 - INFO - 
2025-09-18 14:30:43,273 - INFO - 8_log_2.jpeg                                       1      other                run1_8_log_2.json                                 
2025-09-18 14:30:43,273 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_2.jpeg
2025-09-18 14:30:43,273 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_2.json
2025-09-18 14:30:43,273 - INFO - 
2025-09-18 14:30:43,273 - INFO - 8_log_3.jpeg                                       1      log                  run1_8_log_3.json                                 
2025-09-18 14:30:43,273 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_3.jpeg
2025-09-18 14:30:43,273 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_3.json
2025-09-18 14:30:43,273 - INFO - 
2025-09-18 14:30:43,273 - INFO - 8_log_4.jpeg                                       1      other                run1_8_log_4.json                                 
2025-09-18 14:30:43,273 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_4.jpeg
2025-09-18 14:30:43,273 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_4.json
2025-09-18 14:30:43,273 - INFO - 
2025-09-18 14:30:43,273 - INFO - 8_log_5.png                                        1      other                run1_8_log_5.json                                 
2025-09-18 14:30:43,273 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_5.png
2025-09-18 14:30:43,273 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_5.json
2025-09-18 14:30:43,273 - INFO - 
2025-09-18 14:30:43,273 - INFO - 8_log_6.jpeg                                       1      other                run1_8_log_6.json                                 
2025-09-18 14:30:43,273 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_6.jpeg
2025-09-18 14:30:43,273 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_6.json
2025-09-18 14:30:43,274 - INFO - 
2025-09-18 14:30:43,274 - INFO - 8_log_7.pdf                                        1      coa                  run1_8_log_7.json                                 
2025-09-18 14:30:43,274 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_7.pdf
2025-09-18 14:30:43,274 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_7.json
2025-09-18 14:30:43,274 - INFO - 
2025-09-18 14:30:43,274 - INFO - 8_log_8.jpeg                                       1      log                  run1_8_log_8.json                                 
2025-09-18 14:30:43,274 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_8.jpeg
2025-09-18 14:30:43,274 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_8.json
2025-09-18 14:30:43,274 - INFO - 
2025-09-18 14:30:43,274 - INFO - 8_log_9.jpeg                                       1      other                run1_8_log_9.json                                 
2025-09-18 14:30:43,274 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_9.jpeg
2025-09-18 14:30:43,274 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_9.json
2025-09-18 14:30:43,274 - INFO - 
2025-09-18 14:30:43,274 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:30:43,274 - INFO - Total entries: 13
2025-09-18 14:30:43,274 - INFO - ============================================================================================================================================
2025-09-18 14:30:43,274 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:30:43,275 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:30:43,275 - INFO -   1. 8_log_1.pdf                         Page 1   → other           | run1_8_log_1.json
2025-09-18 14:30:43,275 - INFO -   2. 8_log_1.pdf                         Page 2   → other           | run1_8_log_1.json
2025-09-18 14:30:43,275 - INFO -   3. 8_log_1.pdf                         Page 3   → other           | run1_8_log_1.json
2025-09-18 14:30:43,275 - INFO -   4. 8_log_10.jpeg                       Page 1   → log             | run1_8_log_10.json
2025-09-18 14:30:43,275 - INFO -   5. 8_log_11.jpg                        Page 1   → log             | run1_8_log_11.json
2025-09-18 14:30:43,275 - INFO -   6. 8_log_2.jpeg                        Page 1   → other           | run1_8_log_2.json
2025-09-18 14:30:43,275 - INFO -   7. 8_log_3.jpeg                        Page 1   → log             | run1_8_log_3.json
2025-09-18 14:30:43,275 - INFO -   8. 8_log_4.jpeg                        Page 1   → other           | run1_8_log_4.json
2025-09-18 14:30:43,275 - INFO -   9. 8_log_5.png                         Page 1   → other           | run1_8_log_5.json
2025-09-18 14:30:43,275 - INFO -  10. 8_log_6.jpeg                        Page 1   → other           | run1_8_log_6.json
2025-09-18 14:30:43,275 - INFO -  11. 8_log_7.pdf                         Page 1   → coa             | run1_8_log_7.json
2025-09-18 14:30:43,275 - INFO -  12. 8_log_8.jpeg                        Page 1   → log             | run1_8_log_8.json
2025-09-18 14:30:43,275 - INFO -  13. 8_log_9.jpeg                        Page 1   → other           | run1_8_log_9.json
2025-09-18 14:30:43,275 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:30:43,276 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 23.445769, 'processed_files': [{'filename': '8_log_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_1.pdf'}, {'filename': '8_log_10.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_10.jpeg'}, {'filename': '8_log_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_11.jpg'}, {'filename': '8_log_2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_2.jpeg'}, {'filename': '8_log_3.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_3.jpeg'}, {'filename': '8_log_4.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_4.jpeg'}, {'filename': '8_log_5.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_5.png'}, {'filename': '8_log_6.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_6.jpeg'}, {'filename': '8_log_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_7.pdf'}, {'filename': '8_log_8.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_8.jpeg'}, {'filename': '8_log_9.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/8_log/8_log_9.jpeg'}]}
