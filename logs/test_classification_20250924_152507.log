2025-09-24 15:25:07,259 - INFO - Logging initialized. Log file: logs/test_classification_20250924_152507.log
2025-09-24 15:25:07,259 - INFO - 📁 Found 15 files to process
2025-09-24 15:25:07,259 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:25:07,259 - INFO - 🚀 Processing 15 files in FORCED PARALLEL MODE...
2025-09-24 15:25:07,259 - INFO - 🚀 Creating 15 parallel tasks...
2025-09-24 15:25:07,260 - INFO - 🚀 All 15 tasks created - executing in parallel...
2025-09-24 15:25:07,260 - INFO - ⬆️ [15:25:07] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:10,522 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/7d35d840_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:10,522 - INFO - 🔍 [15:25:10] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:10,523 - INFO - ⬆️ [15:25:10] Uploading: DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:10,524 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:10,546 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:10,555 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7d35d840_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:10,555 - INFO - Processing PDF from S3...
2025-09-24 15:25:10,556 - INFO - Downloading PDF from S3 to /tmp/tmpbad7iaeg/7d35d840_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:11,201 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/5399da55_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:11,201 - INFO - 🔍 [15:25:11] Starting classification: DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:11,202 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:11,203 - INFO - ⬆️ [15:25:11] Uploading: DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:11,220 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:11,224 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5399da55_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:11,224 - INFO - Processing PDF from S3...
2025-09-24 15:25:11,225 - INFO - Downloading PDF from S3 to /tmp/tmpkzov_3nu/5399da55_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:11,815 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/092b7638_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:11,815 - INFO - 🔍 [15:25:11] Starting classification: DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:11,816 - INFO - ⬆️ [15:25:11] Uploading: HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:11,818 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:11,836 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:11,840 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/092b7638_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:11,840 - INFO - Processing PDF from S3...
2025-09-24 15:25:11,841 - INFO - Downloading PDF from S3 to /tmp/tmp5i330gv_/092b7638_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:12,429 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/75be8d09_HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:12,429 - INFO - 🔍 [15:25:12] Starting classification: HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:12,430 - INFO - ⬆️ [15:25:12] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:12,433 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:12,456 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:12,460 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/75be8d09_HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:12,461 - INFO - Processing PDF from S3...
2025-09-24 15:25:12,461 - INFO - Downloading PDF from S3 to /tmp/tmpvhpb05xg/75be8d09_HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:13,006 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/4dc61a5f_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:13,006 - INFO - 🔍 [15:25:13] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:13,007 - INFO - ⬆️ [15:25:13] Uploading: K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:13,009 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:13,030 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:13,034 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4dc61a5f_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:13,035 - INFO - Processing PDF from S3...
2025-09-24 15:25:13,035 - INFO - Downloading PDF from S3 to /tmp/tmpl93fc89f/4dc61a5f_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:13,041 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:25:13,041 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:13,043 - INFO - Splitting PDF 7d35d840_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:25:13,047 - INFO - Split PDF into 1 pages
2025-09-24 15:25:13,047 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:13,047 - INFO - Expected pages: [1]
2025-09-24 15:25:13,166 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:25:13,166 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:13,166 - INFO - Splitting PDF 5399da55_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 15:25:13,167 - INFO - Split PDF into 1 pages
2025-09-24 15:25:13,167 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:13,167 - INFO - Expected pages: [1]
2025-09-24 15:25:13,375 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:13,376 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:13,379 - INFO - Splitting PDF 092b7638_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 15:25:13,383 - INFO - Split PDF into 1 pages
2025-09-24 15:25:13,383 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:13,383 - INFO - Expected pages: [1]
2025-09-24 15:25:13,658 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/838ea519_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:13,659 - INFO - 🔍 [15:25:13] Starting classification: K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:13,659 - INFO - ⬆️ [15:25:13] Uploading: OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:13,661 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:13,680 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:13,684 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/838ea519_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:13,685 - INFO - Processing PDF from S3...
2025-09-24 15:25:13,685 - INFO - Downloading PDF from S3 to /tmp/tmpbqqm_v5v/838ea519_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:14,268 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:25:14,269 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:14,270 - INFO - Splitting PDF 75be8d09_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 15:25:14,273 - INFO - Split PDF into 1 pages
2025-09-24 15:25:14,273 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:14,273 - INFO - Expected pages: [1]
2025-09-24 15:25:14,324 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:14,324 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:14,328 - INFO - Splitting PDF 4dc61a5f_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 15:25:14,330 - INFO - Split PDF into 1 pages
2025-09-24 15:25:14,331 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:14,331 - INFO - Expected pages: [1]
2025-09-24 15:25:14,572 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/934eda92_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:14,573 - INFO - 🔍 [15:25:14] Starting classification: OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:14,573 - INFO - ⬆️ [15:25:14] Uploading: R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:14,574 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:14,591 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:14,637 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/934eda92_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:14,637 - INFO - Processing PDF from S3...
2025-09-24 15:25:14,637 - INFO - Downloading PDF from S3 to /tmp/tmpboqggqlk/934eda92_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:15,089 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:15,089 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:15,092 - INFO - Splitting PDF 838ea519_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 15:25:15,093 - INFO - Split PDF into 1 pages
2025-09-24 15:25:15,093 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:15,093 - INFO - Expected pages: [1]
2025-09-24 15:25:15,166 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/b394b607_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:15,167 - INFO - 🔍 [15:25:15] Starting classification: R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:15,168 - INFO - ⬆️ [15:25:15] Uploading: RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:15,175 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:15,189 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:15,193 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b394b607_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:15,194 - INFO - Processing PDF from S3...
2025-09-24 15:25:15,194 - INFO - Downloading PDF from S3 to /tmp/tmprpj5wz78/b394b607_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:15,773 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/02ac488d_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:15,773 - INFO - 🔍 [15:25:15] Starting classification: RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:15,773 - INFO - ⬆️ [15:25:15] Uploading: WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:15,775 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:15,790 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:15,792 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/02ac488d_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:15,792 - INFO - Processing PDF from S3...
2025-09-24 15:25:15,793 - INFO - Downloading PDF from S3 to /tmp/tmp0rutdez9/02ac488d_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:16,415 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/869d8689_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:16,415 - INFO - 🔍 [15:25:16] Starting classification: WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:16,416 - INFO - ⬆️ [15:25:16] Uploading: XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:16,419 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:16,445 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:16,446 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:16,448 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:16,450 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/869d8689_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:16,450 - INFO - Processing PDF from S3...
2025-09-24 15:25:16,451 - INFO - Splitting PDF b394b607_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 15:25:16,451 - INFO - Downloading PDF from S3 to /tmp/tmpd0l2ju5t/869d8689_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:16,462 - INFO - Split PDF into 2 pages
2025-09-24 15:25:16,462 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:16,462 - INFO - Expected pages: [1, 2]
2025-09-24 15:25:16,998 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/d72f63d7_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:16,999 - INFO - 🔍 [15:25:16] Starting classification: XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:17,000 - INFO - ⬆️ [15:25:17] Uploading: Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:17,002 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:17,022 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:17,026 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d72f63d7_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:17,026 - INFO - Processing PDF from S3...
2025-09-24 15:25:17,026 - INFO - Downloading PDF from S3 to /tmp/tmpogze0g7z/d72f63d7_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:17,243 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 15:25:17,244 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:17,246 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 15:25:17,247 - INFO - Splitting PDF 934eda92_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 15:25:17,278 - INFO - Split PDF into 2 pages
2025-09-24 15:25:17,278 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:17,278 - INFO - Expected pages: [1, 2]
2025-09-24 15:25:17,296 - INFO - Page 1: Extracted 626 characters, 49 lines from 092b7638_DFY1VDZWR7NBDLJV02G2_7fc36031_page_001.pdf
2025-09-24 15:25:17,297 - INFO - Successfully processed page 1
2025-09-24 15:25:17,298 - INFO - Combined 1 pages into final text
2025-09-24 15:25:17,298 - INFO - Text validation for 092b7638_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 15:25:17,298 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:17,298 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:17,589 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/Y82AJRDQU1FCXFHREDEA.pdf -> s3://document-extraction-logistically/temp/9e595113_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:17,589 - INFO - 🔍 [15:25:17] Starting classification: Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:17,590 - INFO - ⬆️ [15:25:17] Uploading: nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:17,590 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:17,606 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:17,609 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9e595113_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:17,609 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:25:17,609 - INFO - Processing PDF from S3...
2025-09-24 15:25:17,610 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:17,610 - INFO - Downloading PDF from S3 to /tmp/tmpw2ed_j7d/9e595113_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:17,618 - INFO - Splitting PDF 02ac488d_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 15:25:17,619 - INFO - Split PDF into 1 pages
2025-09-24 15:25:17,619 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:17,619 - INFO - Expected pages: [1]
2025-09-24 15:25:17,649 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:17,649 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:17,650 - INFO - Splitting PDF 869d8689_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 15:25:17,652 - INFO - Split PDF into 1 pages
2025-09-24 15:25:17,652 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:17,652 - INFO - Expected pages: [1]
2025-09-24 15:25:17,967 - INFO - Page 1: Extracted 589 characters, 36 lines from 4dc61a5f_I_QHD3LC0DU6S8O2YVVS60_8f0b4f70_page_001.pdf
2025-09-24 15:25:17,967 - INFO - Successfully processed page 1
2025-09-24 15:25:17,967 - INFO - Combined 1 pages into final text
2025-09-24 15:25:17,967 - INFO - Text validation for 4dc61a5f_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 15:25:17,967 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:17,967 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:18,040 - INFO - Page 1: Extracted 732 characters, 59 lines from 5399da55_DCY7SLNMWUXIENOREHQF_eced3b8f_page_001.pdf
2025-09-24 15:25:18,041 - INFO - Successfully processed page 1
2025-09-24 15:25:18,042 - INFO - Combined 1 pages into final text
2025-09-24 15:25:18,042 - INFO - Text validation for 5399da55_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 15:25:18,042 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:18,042 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:18,251 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_D5KCCMIJXGREC9Q81E0H.pdf -> s3://document-extraction-logistically/temp/c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:18,251 - INFO - 🔍 [15:25:18] Starting classification: nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:18,251 - INFO - ⬆️ [15:25:18] Uploading: nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:18,252 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:18,267 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:18,272 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:18,273 - INFO - Processing PDF from S3...
2025-09-24 15:25:18,273 - INFO - Downloading PDF from S3 to /tmp/tmpfex8p00l/c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:18,428 - INFO - Page 1: Extracted 517 characters, 31 lines from 75be8d09_HFPAXYL947DH59AB12FL_bb669c14_page_001.pdf
2025-09-24 15:25:18,428 - INFO - Successfully processed page 1
2025-09-24 15:25:18,428 - INFO - Combined 1 pages into final text
2025-09-24 15:25:18,429 - INFO - Text validation for 75be8d09_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 15:25:18,429 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:18,429 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:18,860 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/c687a310_nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:18,861 - INFO - 🔍 [15:25:18] Starting classification: nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:18,862 - INFO - ⬆️ [15:25:18] Uploading: nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:18,864 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:18,881 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:18,886 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c687a310_nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:18,886 - INFO - Processing PDF from S3...
2025-09-24 15:25:18,887 - INFO - Downloading PDF from S3 to /tmp/tmp1587p74h/c687a310_nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:18,893 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:25:18,893 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:18,894 - INFO - Splitting PDF d72f63d7_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 15:25:18,903 - INFO - Split PDF into 1 pages
2025-09-24 15:25:18,903 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:18,904 - INFO - Expected pages: [1]
2025-09-24 15:25:19,074 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:19,074 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:19,075 - INFO - Splitting PDF 9e595113_Y82AJRDQU1FCXFHREDEA into 1 pages
2025-09-24 15:25:19,075 - INFO - Split PDF into 1 pages
2025-09-24 15:25:19,075 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:19,075 - INFO - Expected pages: [1]
2025-09-24 15:25:19,446 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:19,447 - INFO - 🔍 [15:25:19] Starting classification: nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:19,448 - INFO - Initializing TextractProcessor...
2025-09-24 15:25:19,460 - INFO - Initializing BedrockProcessor...
2025-09-24 15:25:19,463 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:19,463 - INFO - Processing PDF from S3...
2025-09-24 15:25:19,464 - INFO - Downloading PDF from S3 to /tmp/tmptgy_waag/306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:19,621 - INFO - Page 1: Extracted 939 characters, 64 lines from 7d35d840_A34CDFDJ66EDOZEKZWJL_d5355672_page_001.pdf
2025-09-24 15:25:19,621 - INFO - Successfully processed page 1
2025-09-24 15:25:19,621 - INFO - Combined 1 pages into final text
2025-09-24 15:25:19,622 - INFO - Text validation for 7d35d840_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:25:19,622 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:19,622 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:19,880 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:25:19,880 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:19,881 - INFO - Splitting PDF c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H into 1 pages
2025-09-24 15:25:19,884 - INFO - Split PDF into 1 pages
2025-09-24 15:25:19,884 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:19,884 - INFO - Expected pages: [1]
2025-09-24 15:25:20,011 - INFO - Page 1: Extracted 802 characters, 30 lines from 838ea519_K9VSARJOKAIZHNJ5RBDT_4365d661_page_001.pdf
2025-09-24 15:25:20,012 - INFO - Successfully processed page 1
2025-09-24 15:25:20,012 - INFO - Combined 1 pages into final text
2025-09-24 15:25:20,012 - INFO - Text validation for 838ea519_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 15:25:20,012 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:20,012 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:20,023 - INFO - Page 2: Extracted 913 characters, 56 lines from b394b607_R1V0MO844PBLWNEAUETU_63c4a7c1_page_002.pdf
2025-09-24 15:25:20,023 - INFO - Successfully processed page 2
2025-09-24 15:25:20,374 - INFO - Page 1: Extracted 1511 characters, 86 lines from b394b607_R1V0MO844PBLWNEAUETU_63c4a7c1_page_001.pdf
2025-09-24 15:25:20,374 - INFO - Successfully processed page 1
2025-09-24 15:25:20,374 - INFO - Combined 2 pages into final text
2025-09-24 15:25:20,375 - INFO - Text validation for b394b607_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 15:25:20,375 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:20,375 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:20,448 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 15:25:20,449 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:20,450 - INFO - Splitting PDF c687a310_nmfc_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 15:25:20,453 - INFO - Split PDF into 2 pages
2025-09-24 15:25:20,453 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:20,453 - INFO - Expected pages: [1, 2]
2025-09-24 15:25:20,484 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '27b3c283-a9d0-4a46-97ad-38588377ef38', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:20 GMT', 'content-type': 'application/json', 'content-length': '704', 'connection': 'keep-alive', 'x-amzn-requestid': '27b3c283-a9d0-4a46-97ad-38588377ef38'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The header: "CUBE MEASUREMENT CERTIFICATE". That\'s likely a cube measurement certificate, which is an inspection certificate type. The definitions: inspection_cert - Inspection Certificate, Cube Measurement certificate. So doc_type: inspection_cert. No continuation. So return array with one object: page_no:1, doc_type: "inspection_cert". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2618, 'outputTokens': 97, 'totalTokens': 2715}, 'metrics': {'latencyMs': 585}}
2025-09-24 15:25:20,485 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4dc61a5f_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:20,508 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 15:25:20,509 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 15:25:20,797 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '42aaf444-1564-49c1-ad3c-9a0a696f362a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:20 GMT', 'content-type': 'application/json', 'content-length': '2362', 'connection': 'keep-alive', 'x-amzn-requestid': '42aaf444-1564-49c1-ad3c-9a0a696f362a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page, but the content indicates "Weight & Inspection", "CERTIFIED WEIGHT CERTIFICATE", "Weight Certificate", and "Page 3/3". So it\'s a multi-page document? The content shows "Page 3/3" at the bottom. It likely continues previous pages not shown. But the instruction: if page starts with "continued" or pagination like "page 2 of 3", etc., we treat as continuation and assign same doc_type as previous page. But we have only this page. There\'s no previous page in the input; we assume it\'s the first page? Wait the page shows "Page 3/3". That indicates page 3 of 3. That suggests this is the last page. But we have no content from pages 1 and 2. But we must still classify this page. It likely is part of a multi-page document. We should assign doc_type based on the content. Keywords: "Weight & Inspection", "CERTIFIED WEIGHT CERTIFICATE", etc. The doc_type is weight_and_inspection_cert, or possibly nmfc_cert if it includes NMFC keywords. The text shows no NMFC mention. So weight_and_inspection_cert.\n\nBut we must check if it\'s a continuation: page shows "Page 3/3". That indicates it\'s a multi-page doc. But we have no previous page. So we cannot assign previous doc_type. But we can still classify this page as weight_and_inspection_cert. There\'s no "continued" on top. So we proceed with that.\n\nWe need to use the tool classify_logistics_doc_type with documents array: each object includes page_no and doc_type. Since only one page, page_no 1? Wait we may treat page number as the actual page number in the PDF. The content says "Page 3/3". But the input says <page1> which indicates it\'s the first page of our extraction? The user gave <page1> with content. That might be page 1. Even though content says Page 3/3. But we should match the input\'s page number: it\'s page 1. So we output page_no: 1, doc_type: weight_and_inspection_cert.\n\nThus call function with array of one object.\n\n'}}}, {'toolUse': {'toolUseId': 'tooluse_o8Mh9t5PTAaBHEdQUCoA5A', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2685, 'outputTokens': 483, 'totalTokens': 3168}, 'metrics': {'latencyMs': 2225}}
2025-09-24 15:25:20,797 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/092b7638_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:20,802 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4dc61a5f_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:20,815 - INFO - 

DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:20,815 - INFO - 

✓ Saved result: output/run1_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 15:25:21,099 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fb68e217-b228-43c5-91e3-6f69ac18ac95', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:20 GMT', 'content-type': 'application/json', 'content-length': '1392', 'connection': 'keep-alive', 'x-amzn-requestid': 'fb68e217-b228-43c5-91e3-6f69ac18ac95'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Determine doc type. The page shows "Freight CERTIFICATE" and "DIMENSION/CUBE". It lists weight, dimensions. This looks like a weight and inspection certificate? It seems like a certificate for freight, with dimensions, weight. The spec says weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I. This page has "Freight CERTIFICATE" and dimension details. So likely weight_and_inspection_cert.\n\nBut check nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." No mention of correction or NMFC. So not nmfc_cert.\n\nThus doc_type should be weight_and_inspection_cert. Page 1.\n\nWe must output using the tool classify_logistics_doc_type with documents array: page_no:1, doc_type:"weight_and_inspection_cert".\n\nMake sure to use the correct enum name weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2606, 'outputTokens': 242, 'totalTokens': 2848}, 'metrics': {'latencyMs': 1125}}
2025-09-24 15:25:21,100 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/092b7638_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:21,101 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/75be8d09_HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:21,109 - INFO - 

HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:21,110 - INFO - 

✓ Saved result: output/run1_HFPAXYL947DH59AB12FL.json
2025-09-24 15:25:21,440 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/75be8d09_HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:21,492 - INFO - Page 1: Extracted 580 characters, 48 lines from 869d8689_WRKSHW76B3QUG47QWR75_3ff6a097_page_001.pdf
2025-09-24 15:25:21,493 - INFO - Successfully processed page 1
2025-09-24 15:25:21,494 - INFO - Combined 1 pages into final text
2025-09-24 15:25:21,494 - INFO - Text validation for 869d8689_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 15:25:21,495 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:21,496 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:21,496 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:25:21,497 - INFO - Splitting PDF into individual pages...
2025-09-24 15:25:21,506 - INFO - Splitting PDF 306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 15:25:21,510 - INFO - Split PDF into 3 pages
2025-09-24 15:25:21,510 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:25:21,510 - INFO - Expected pages: [1, 2, 3]
2025-09-24 15:25:21,806 - INFO - Page 1: Extracted 443 characters, 72 lines from 02ac488d_RUDVGETVRZO7XX6YNW7I_6cb76465_page_001.pdf
2025-09-24 15:25:21,806 - INFO - Successfully processed page 1
2025-09-24 15:25:21,806 - INFO - Combined 1 pages into final text
2025-09-24 15:25:21,806 - INFO - Text validation for 02ac488d_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 15:25:21,807 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:21,807 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:21,974 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'aa904e6e-9e6f-4fd9-8b48-8de820028360', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:21 GMT', 'content-type': 'application/json', 'content-length': '1376', 'connection': 'keep-alive', 'x-amzn-requestid': 'aa904e6e-9e6f-4fd9-8b48-8de820028360'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The content: "Inspection Certificate" appears at top. It has dimensions, weight, etc. So likely "inspection_cert" or "weight_and_inspection_cert". Need to check if it\'s weight and inspection certificate. It includes weight details and dimensions. Does it mention NMFC? No NMFC codes. So weight_and_inspection_cert. The page indicates "Inspection Certificate". It\'s a certificate of measurement. It has weight and dimensions. The instruction: "weight_and_inspection_cert - Weight and Inspection Certificate. Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". That matches. There\'s no mention of NMFC, so not nmfc_cert. So doc_type = weight_and_inspection_cert. Page 1 only. We must return array of objects? The tool expects an array of documents with page_no and doc_type. For each page, exactly one object. So output: [{"page_no":1,"doc_type":"weight_and_inspection_cert"}]. Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 242, 'totalTokens': 3068}, 'metrics': {'latencyMs': 1118}}
2025-09-24 15:25:21,975 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7d35d840_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:21,989 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:21,989 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:25:22,141 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '10a21947-6dc2-4f9c-9966-4155ff312af5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:22 GMT', 'content-type': 'application/json', 'content-length': '2825', 'connection': 'keep-alive', 'x-amzn-requestid': '10a21947-6dc2-4f9c-9966-4155ff312af5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page content. We need to classify.\n\nPage: FedEx weight validation certificate. Contains "WEIGHT VALIDATION CERTIFICATE" header. Contains weight details, total actual verified weight, etc. This is likely a "scale_ticket" or "weight_and_inspection_cert". The definition: scale ticket has weight keywords. But also weight_and_inspection_cert is for weight certificate. Let\'s check: The page is a "WEIGHT VALIDATION CERTIFICATE" which is basically a weight and inspection? But it has "Scale" and actual weight. It includes measurement. It is not a certificate of analysis for weight? It is a weight validation certificate from FedEx Freight. The key fields/structure: weight, date, location. It may be more like a "scale_ticket" because it mentions "Scale operator". But weight_and_inspection_cert is for weight and inspection certificate from authorities. This looks like a FedEx internal weight validation. Might be considered a "scale_ticket" or "weight_and_inspection_cert". The classification guidelines: If weight and inspection certificate has weight mentioned, then weight_and_inspection_cert. But also there is nmfc_cert if correction. But not here. There\'s no mention of NMFC codes. So likely weight_and_inspection_cert. However, definition: "Scale Ticket — Weight record from a scale for vehicle/load (used for billing/compliance). Keywords: Gross, Tare, Net, Weighed At, etc." This page contains "Total Actual Verified Weight" and "Scale Operator". It doesn\'t mention Gross/Tare/Net. But still refers to weight. But "Weight Validation Certificate" is more like a certificate. So weight_and_inspection_cert seems more appropriate. Let\'s check the difference: weight_and_inspection_cert is a certificate confirming weight, not necessarily a scale ticket. It says "Weight and Inspection Certificate" used by authorities. But this is a FedEx internal certificate. It might still fit weight_and_inspection_cert. The guidelines say: For weight_and_inspection_cert: if weight mentioned, classify as weight_and_inspection_cert; if weight and inspection certificate, classify as weight_and_inspection_cert. There is no mention of scale ticket. But scale ticket also has weight. Which to choose? The page header says "WEIGHT VALIDATION CERTIFICATE". That matches weight_and_inspection_cert more than scale_ticket. So choose weight_and_inspection_cert.\n\nThus output: array with single object page_no:1, doc_type: "weight_and_inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2718, 'outputTokens': 529, 'totalTokens': 3247}, 'metrics': {'latencyMs': 2177}}
2025-09-24 15:25:22,142 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5399da55_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:22,276 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7d35d840_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:22,290 - INFO - 

DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:22,290 - INFO - 

✓ Saved result: output/run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 15:25:22,651 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5399da55_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:22,698 - INFO - Page 2: Extracted 540 characters, 29 lines from 934eda92_OR9EL08KIKNQPZ3UV3HH_f7662897_page_002.pdf
2025-09-24 15:25:22,698 - INFO - Successfully processed page 2
2025-09-24 15:25:22,765 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '16629e49-8bbf-4cdb-a049-3448cf662500', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:22 GMT', 'content-type': 'application/json', 'content-length': '1478', 'connection': 'keep-alive', 'x-amzn-requestid': '16629e49-8bbf-4cdb-a049-3448cf662500'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be a "Weight and Research Program" page, with terms like "Weight and Research Program", "Weight and Research Section", "We certify that this shipment has been inspected and/or reweighed". This looks like a Weight and Inspection Certificate or a Weight and Inspection Certificate (W&I). We have to decide between weight_and_inspection_cert or nmfc_cert. The description says: "We certify that this shipment has been inspected and/or reweighed" and "Weight and Research Section". That could be a Weight and Inspection Certificate. Also the page includes "Original Correction". It says "Original Correction" maybe it\'s a corrected certificate. But the key is that it includes weight and inspection. No mention of NMFC code or corrections. So likely weight_and_inspection_cert.\n\nThus page1: weight_and_inspection_cert. page2: same.\n\nCheck for "Continued"? page2 is continuing page1.\n\nSo output array of two objects: page_no 1 and doc_type weight_and_inspection_cert; page_no 2 same.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3302, 'outputTokens': 261, 'totalTokens': 3563}, 'metrics': {'latencyMs': 1250}}
2025-09-24 15:25:22,766 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b394b607_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:22,857 - INFO - 

R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:22,857 - INFO - 

✓ Saved result: output/run1_R1V0MO844PBLWNEAUETU.json
2025-09-24 15:25:23,025 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b1ef2f6a-7c8e-41c8-bb04-a75e3e251773', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:22 GMT', 'content-type': 'application/json', 'content-length': '2310', 'connection': 'keep-alive', 'x-amzn-requestid': 'b1ef2f6a-7c8e-41c8-bb04-a75e3e251773'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine doc type. The page is a weight certificate. Keywords: WEIGHT CERTIFICATE, WEIGHT CERTIFICATION, scale, weight, lbs. So likely scale_ticket or weight_and_inspection_cert. Let\'s see definitions.\n\nscale_ticket: weight record from a scale for vehicle/load used for billing/compliance. Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, date, time, Ticket no. This page has weight, date, weighs. So likely scale_ticket.\n\nBut weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width but not rechecked. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. It also says check if nmfc_cert. But this page is weight certificate, not inspection. It doesn\'t have "Inspection" or "Certificate of Analysis". It says "WEIGHT CERTIFICATE". That might be weight_and_inspection_cert? But weight_and_inspection_cert is for weight certificates, but the classification says if weight and inspection cert has weight mentioned in lbs or tons. That might fit. But let\'s see difference: scale_ticket is more about scale reading for billing. This is a weight certificate from a service center, "WEIGHT CERTIFICATE" with mention of legal for trade. That seems like a weight certificate for compliance, maybe weight_and_inspection_cert. But the difference: weight_and_inspection_cert is a certificate confirming weight. Yes. That matches. The content: "WEIGHT CERTIFICATION" and "We certify that the mentioned shipment has been weighed and that the correct weight as stated is the true and accurate weight of the shipment." That is a certification. So weight_and_inspection_cert seems appropriate.\n\nCheck nmfc_cert: It requires NMFC code, correction, etc. Not present. So weight_and_inspection_cert.\n\nThus output page 1 with doc_type weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2672, 'outputTokens': 426, 'totalTokens': 3098}, 'metrics': {'latencyMs': 1819}}
2025-09-24 15:25:23,025 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/838ea519_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:23,142 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b394b607_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:23,150 - INFO - 

K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:23,150 - INFO - 

✓ Saved result: output/run1_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 15:25:23,471 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/838ea519_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:23,647 - INFO - Page 1: Extracted 528 characters, 31 lines from d72f63d7_XCJLXZK140FUS8020ZAG_8c6c229f_page_001.pdf
2025-09-24 15:25:23,647 - INFO - Successfully processed page 1
2025-09-24 15:25:23,648 - INFO - Combined 1 pages into final text
2025-09-24 15:25:23,648 - INFO - Text validation for d72f63d7_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 15:25:23,648 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:23,648 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:23,785 - INFO - Page 1: Extracted 1120 characters, 87 lines from 934eda92_OR9EL08KIKNQPZ3UV3HH_f7662897_page_001.pdf
2025-09-24 15:25:23,785 - INFO - Successfully processed page 1
2025-09-24 15:25:23,786 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '80bd4dcb-b857-46cc-b0e6-566d1038a44b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:23 GMT', 'content-type': 'application/json', 'content-length': '788', 'connection': 'keep-alive', 'x-amzn-requestid': '80bd4dcb-b857-46cc-b0e6-566d1038a44b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF page. We need to classify each page. There\'s only page1. The content is a NMFC CLASSIFICATION CERTIFICATE. It says "NMFC CLASSIFICATION CERTIFICATE", "NMFCC". It has "INSPECTED BY: ...". It\'s basically a NMFC certificate. According to the enum, that is "nmfc_cert". We need to output using the tool classify_logistics_doc_type with documents array containing one object: page_no: 1, doc_type: "nmfc_cert". Ensure we output exactly that tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2713, 'outputTokens': 133, 'totalTokens': 2846}, 'metrics': {'latencyMs': 830}}
2025-09-24 15:25:23,786 - INFO - Combined 2 pages into final text
2025-09-24 15:25:23,786 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/02ac488d_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:23,787 - INFO - Text validation for 934eda92_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 15:25:23,794 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:23,797 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:23,812 - INFO - 

RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 15:25:23,812 - INFO - 

✓ Saved result: output/run1_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 15:25:24,102 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/02ac488d_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:24,261 - INFO - Page 1: Extracted 840 characters, 77 lines from c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H_8c1178bd_page_001.pdf
2025-09-24 15:25:24,262 - INFO - Successfully processed page 1
2025-09-24 15:25:24,262 - INFO - Combined 1 pages into final text
2025-09-24 15:25:24,262 - INFO - Text validation for c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H: 857 characters, 1 pages
2025-09-24 15:25:24,262 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:24,262 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:24,296 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '816089fb-2aeb-4fc2-917e-50ab3a85c410', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:24 GMT', 'content-type': 'application/json', 'content-length': '1967', 'connection': 'keep-alive', 'x-amzn-requestid': '816089fb-2aeb-4fc2-917e-50ab3a85c410'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We must classify. The content shows: "Weight and Research Linear Certificate" "CERTIFIED ORIGINAL Linear Certification" "We certify that the above shipment has been measured and that the linear feet as stated is the true and accurate length of the shipment". There\'s "W&R #", "Pro #", "Date of Correction", "Linear Feet", "Description: SKIDS", dimensions, "Cu Ft", "Pounds". This looks like a Weight and Inspection Certificate? The header says "Weight and Research Linear Certificate". It\'s a linear measurement. The description: "Linear Certificate" "CERTIFIED ORIGINAL" "We certify that the above shipment has been measured". That matches weight_and_inspection_cert or inspection_cert. But it specifically says "Linear Certificate". It\'s a certificate of weight? It says "Linear Certificate" but includes weight, cubic feet, density. It says "Weight and Research". The question: is this "weight_and_inspection_cert" or "inspection_cert"? The guidelines: "weight_and_inspection_cert - Weight and Inspection Certificate: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". This page contains weight (7294 pounds), dimensions, linear feet. So likely weight_and_inspection_cert. Also, "Linear Certificate" could be a special type of weight and inspection certificate. No mention of NMFC code or correction. It has "Date of Correction: 11/04/22" but not correction info. So "weight_and_inspection_cert".\n\nThus we output one object: page_no: 1, doc_type: "weight_and_inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2640, 'outputTokens': 357, 'totalTokens': 2997}, 'metrics': {'latencyMs': 1564}}
2025-09-24 15:25:24,297 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/869d8689_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:24,317 - INFO - 

WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:24,317 - INFO - 

✓ Saved result: output/run1_WRKSHW76B3QUG47QWR75.json
2025-09-24 15:25:24,325 - INFO - Page 1: Extracted 854 characters, 69 lines from c687a310_nmfc_DH0JZ2JWDGRHD26BX74C_bc2037a1_page_001.pdf
2025-09-24 15:25:24,325 - INFO - Successfully processed page 1
2025-09-24 15:25:24,365 - INFO - Page 2: Extracted 764 characters, 54 lines from c687a310_nmfc_DH0JZ2JWDGRHD26BX74C_bc2037a1_page_002.pdf
2025-09-24 15:25:24,366 - INFO - Successfully processed page 2
2025-09-24 15:25:24,366 - INFO - Combined 2 pages into final text
2025-09-24 15:25:24,366 - INFO - Text validation for c687a310_nmfc_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 15:25:24,366 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:24,366 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:24,608 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/869d8689_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:24,773 - INFO - Page 1: Extracted 992 characters, 60 lines from 9e595113_Y82AJRDQU1FCXFHREDEA_d594d8cd_page_001.pdf
2025-09-24 15:25:24,773 - INFO - Successfully processed page 1
2025-09-24 15:25:24,774 - INFO - Combined 1 pages into final text
2025-09-24 15:25:24,774 - INFO - Text validation for 9e595113_Y82AJRDQU1FCXFHREDEA: 1009 characters, 1 pages
2025-09-24 15:25:24,774 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:24,774 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:25,925 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2460e745-16c0-46db-af96-e6669be2cab3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:25 GMT', 'content-type': 'application/json', 'content-length': '1238', 'connection': 'keep-alive', 'x-amzn-requestid': '2460e745-16c0-46db-af96-e6669be2cab3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page present. It\'s a Freight Certificate with dimensions, weight, etc. This appears to be a weight and inspection certificate? The definition: weight_and_inspection_cert – certificate confirming weight, possibly with length, width, height. This page contains weight 280 lbs, dimensions, and a note. It doesn\'t explicitly mention "Certificate of Analysis" but it\'s a "Freight Certificate" for ABF. Likely it\'s a weight and inspection certificate. But we need to check nmfc_cert: If weight_and_inspection_cert has keywords mentioned in nmfc_cert, classify nmfc_cert. But no mention of NMFC Code or class. So weight_and_inspection_cert is appropriate. So doc_type: weight_and_inspection_cert.\n\nWe output tool call classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "weight_and_inspection_cert".\n\nLet\'s produce JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2613, 'outputTokens': 209, 'totalTokens': 2822}, 'metrics': {'latencyMs': 1111}}
2025-09-24 15:25:25,926 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d72f63d7_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:25,941 - INFO - 

XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:25,941 - INFO - 

✓ Saved result: output/run1_XCJLXZK140FUS8020ZAG.json
2025-09-24 15:25:26,227 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d72f63d7_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:26,352 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '85992cbe-2e2f-47a6-ab2a-ea4655a49f77', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:26 GMT', 'content-type': 'application/json', 'content-length': '1453', 'connection': 'keep-alive', 'x-amzn-requestid': '85992cbe-2e2f-47a6-ab2a-ea4655a49f77'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. They contain "WEIGHT & RESEARCH CERTIFICATE" and "WR" etc. It\'s a weight and inspection certificate? Actually definition: "Weight and Inspection Certificate" is "weight_and_inspection_cert" category. The pages include weight details: weight, Wgt#1=470 Wgt#2=375, "W & R CODES", "WEIGHT & RESEARCH CERTIFICATE". It appears to be "weight_and_inspection_cert".\n\nCheck if it has nmfc_cert? The nmfc_cert definition: "NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual". Not sure if this includes "WEIGHT & RESEARCH CERTIFICATE" maybe it\'s a weight & research certificate, not nmfc. There\'s no mention of NMFC Code, class#, etc. So classify as weight_and_inspection_cert.\n\nBoth pages are part of same document: page 1 and page 2. They are continuation. Should we assign same doc_type to both. Yes.\n\nReturn JSON via tool call. Format: array of objects with page_no and doc_type. Output exactly using tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3124, 'outputTokens': 273, 'totalTokens': 3397}, 'metrics': {'latencyMs': 1354}}
2025-09-24 15:25:26,352 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/934eda92_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:26,380 - INFO - 

OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:26,380 - INFO - 

✓ Saved result: output/run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 15:25:26,507 - INFO - Page 1: Extracted 980 characters, 76 lines from 306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N_d3a0ae78_page_001.pdf
2025-09-24 15:25:26,507 - INFO - Successfully processed page 1
2025-09-24 15:25:26,583 - INFO - Page 3: Extracted 850 characters, 59 lines from 306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N_d3a0ae78_page_003.pdf
2025-09-24 15:25:26,583 - INFO - Successfully processed page 3
2025-09-24 15:25:26,736 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/934eda92_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:26,835 - INFO - Page 2: Extracted 850 characters, 59 lines from 306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N_d3a0ae78_page_002.pdf
2025-09-24 15:25:26,835 - INFO - Successfully processed page 2
2025-09-24 15:25:26,836 - INFO - Combined 3 pages into final text
2025-09-24 15:25:26,836 - INFO - Text validation for 306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 15:25:26,836 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:25:26,837 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:25:27,212 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '40bd89bf-9ebe-45d5-996f-3cae1aa9c4e8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:27 GMT', 'content-type': 'application/json', 'content-length': '1773', 'connection': 'keep-alive', 'x-amzn-requestid': '40bd89bf-9ebe-45d5-996f-3cae1aa9c4e8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need classify. Let\'s analyze content.\n\nPage includes:\n\n- NMFC Classification Certificate\n- Pro Number, location etc.\n- Items: 4 pcs, weight 4120? Actually weight 4120. Then in inspection detail weight 92.5? Wait weight 92.5 for each? That seems inconsistent. But anyway.\n\n- "Original Detail" and "Inspection Detail" sections. It says "NMFC# 156830 S6". "Correct NMFC/Sub:" etc. This looks like a weight and inspection certificate (W&I). Also mentions "NMFC Classification Certificate" at top.\n\nWe also have note about "Correct NMFC/Sub:" maybe indicates correction? But the description says "Correct NMFC/Sub:" then shows 156830 S6. That might be the corrected class. The content includes "NMFC Classification Certificate". That matches the "nmfc_cert" type. The doc likely is an NMFC classification certificate.\n\nCheck guidelines: weight_and_inspection_cert: check if weight and inspection certificate has weight mention in lbs or tons; if no weight, classify as inspection_cert. Here weight is 92.5 maybe weight per piece? Also "RATED AS WEIGHT OF 5000" maybe weight 5000 lbs.\n\nAlso nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." It says "Correct NMFC/Sub:" indicates correction. So classification should be nmfc_cert.\n\nThus doc_type: nmfc_cert.\n\nReturn JSON array with single object: page_no 1, doc_type: "nmfc_cert".\n\nWe must use function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2797, 'outputTokens': 354, 'totalTokens': 3151}, 'metrics': {'latencyMs': 1698}}
2025-09-24 15:25:27,212 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:27,224 - INFO - 

nmfc_D5KCCMIJXGREC9Q81E0H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 15:25:27,224 - INFO - 

✓ Saved result: output/run1_nmfc_D5KCCMIJXGREC9Q81E0H.json
2025-09-24 15:25:27,558 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c5da5ec8_nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:27,560 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b7ebc98e-a267-4472-ab45-3563334d5cc5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:27 GMT', 'content-type': 'application/json', 'content-length': '1961', 'connection': 'keep-alive', 'x-amzn-requestid': 'b7ebc98e-a267-4472-ab45-3563334d5cc5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The document is an "Inspection Certificate" from Estes, with weight, density, etc. This seems like a "inspection_cert" (Inspection Certificate) or perhaps "weight_and_inspection_cert". Let\'s analyze: The header says "ESTES INSPECTION CERTIFICATE". It also mentions "DATE OF CORRECTION", "ISSUING TERMINAL", "PRO NUMBER", "SHIPMENT DATE", shipping info, etc. It has weight and dimensions. There\'s mention of "WEIGHT (LBS)" and "CLASS". Also "AS FOUND UPON EXAMINATION: ARTICLES AND DESCRIPTION". This is a certificate of inspection. It may be a "weight_and_inspection_cert" if it includes weight. It includes weight numbers. The guidelines: "weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Note: strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert. Also, strictly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classification inspection_cert."\n\nThus, this certificate has weight, so it is weight_and_inspection_cert. It is not nmfc_cert, no mention of NMFC. So doc_type = weight_and_inspection_cert.\n\nWe output one object for page 1: {page_no:1, doc_type:"weight_and_inspection_cert"}.\n\nWe must use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2805, 'outputTokens': 371, 'totalTokens': 3176}, 'metrics': {'latencyMs': 1606}}
2025-09-24 15:25:27,561 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9e595113_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:27,581 - INFO - 

Y82AJRDQU1FCXFHREDEA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:25:27,581 - INFO - 

✓ Saved result: output/run1_Y82AJRDQU1FCXFHREDEA.json
2025-09-24 15:25:27,890 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9e595113_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:28,454 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e2df5e9b-e41f-4033-ae71-0c575ed1e96a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:28 GMT', 'content-type': 'application/json', 'content-length': '3294', 'connection': 'keep-alive', 'x-amzn-requestid': 'e2df5e9b-e41f-4033-ae71-0c575ed1e96a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a two-page document. Both pages appear to be parts of the same Inspection Correction Notice. The text repeats. There\'s no "continued" indicator. But it\'s identical content. It may be a duplicate. But the instruction: If a page is part of a multi-page single document, each page still gets the same doc_type. Should classify as "inspection_cert" or "weight_and_inspection_cert" or "nmfc_cert"? We need to determine correct type.\n\nLet\'s analyze details.\n\nWe have "Inspection Correction Notice". This indicates a correction notice for NMFC? There\'s "NMFC" columns, "Before", "After". The page includes "Inspection Correction Notice". NMFC codes, etc. The heading "Inspection Correction Notice" suggests NMFC Correction? The description: "This will certify that this shipment has been (Inspected/Dimensioned/Weighed)..." The keywords: "Inspection Correction Notice", "NMFC", "Correction Details", "Applied Rules and Accessorial". This seems like a "nmfc_cert" as it\'s a correction notice. The spec says: nmfc_cert is a NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: "As described and As found or Original and inspection or Corrected class or Correction information". This document seems to be a correction notice related to NMFC. It includes "Inspection Correction Notice" and NMFC codes. So doc_type likely nmfc_cert.\n\nBut we need to check weight_and_inspection_cert vs nmfc_cert. The spec: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. The note: Strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert. That means if the doc has nmfc_cert keywords, we classify as nmfc_cert. This doc definitely has NMFC and "Inspection Correction Notice". So nmfc_cert.\n\nThus both pages are same type nmfc_cert. We also need to consider the continuation rule. The second page does not have "continued" but it\'s identical. However, we have to classify each page. Since it\'s same doc, same type. So output two objects: page_no 1 and 2, both doc_type "nmfc_cert".\n\nWe must use the tool classify_logistics_doc_type. Provide documents array with objects. The function expects documents array of items with page_no and doc_type.\n\nWe must output a single JSON object: { "documents": [ ... ] } ?\n\nLet\'s confirm tool signature: classify_logistics_doc_type takes argument: { documents: [ { page_no: number, doc_type: ... } ] }. So we need to call the tool with that. Ensure we don\'t add any extra text. Just tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2966, 'outputTokens': 644, 'totalTokens': 3610}, 'metrics': {'latencyMs': 2856}}
2025-09-24 15:25:28,454 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c687a310_nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:28,475 - INFO - 

nmfc_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 15:25:28,476 - INFO - 

✓ Saved result: output/run1_nmfc_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 15:25:28,762 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c687a310_nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:31,568 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9ca99c02-6941-4329-ae00-2cfc28799bbf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:55:31 GMT', 'content-type': 'application/json', 'content-length': '3911', 'connection': 'keep-alive', 'x-amzn-requestid': '9ca99c02-6941-4329-ae00-2cfc28799bbf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have three pages, each looks like an inspection correction notice. The first page has some details: "Inspection Correction Notice", "PRO Number: 67708130", "Inspection photos are available..." etc. It also has "NMFC to 189800-4". The document type seems to be a "Inspection Correction Notice" which is a type of "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert"? Let\'s see the definitions: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Inspection certificate: other than weight and inspection certificate which doesn\'t have weight mentioned. nmfc_cert: NMFC classification certificate or correction notice or weight & inspection certificate but strictly with Inspected against original or Corrected against Actual.\n\nThe definition: nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Also optional NMFC Code, class #. Our page has "NMFC to 189800-4" and "Correction Details". Also "Inspection Correction Notice" header. So this is likely nmfc_cert.\n\nThe question: each page must be classified. It seems page1 has more details including "Before" and "After" columns, "Count", "Description", "Item", "Sub", "Weight", "Class", etc. It also has "Total" row. This page also contains "NMFC to 189800-4". That indicates a correction notice. So likely nmfc_cert. Page2 is similar but missing some columns like "Before" and "After" maybe? Actually page2 seems to have only the header and some fields, but not the full tables? Let\'s examine page2 content: It has same header, but the table seems missing some columns. It has "Freight Detail Piece Commodity NMFC Before After Count Description Item Sub Weight Class Weight Class" but maybe incomplete. Actually page2 shows "Correction Details Applied Rules and Accessorial Dimensions Pieces Length Width Height Weight Pieces Length Width Height Weight Average Density Cube 5.23 38.24". It seems similar but maybe some missing columns. Page3 similar to page2. All three pages are the same content but page1 has more detail. The tool expects classification per page. So we need to output an array of objects: page_no and doc_type for each page. All pages likely same type: nmfc_cert. But we need to check if page2 and page3 are continuation of page1. They have "Inspection Correction Notice" header each, but might be separate pages of same document. The instruction: If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page). So yes, we classify each page individually with same doc_type if they belong to same document. We need to examine if page2 and page3 are continuation: They start with header "ACT M/M/E DHE www.aaacooper.com ...", not "continued". But they are part of same document (inspection correction notice). So we can classify each page as nmfc_cert.\n\nThus output: array of 3 objects: page_no: 1 doc_type: nmfc_cert; page_no 2 doc_type: nmfc_cert; page_no 3 doc_type: nmfc_cert.\n\nWe must use the tool classify_logistics_doc_type with input: documents: [{page_no:1, doc_type:"nmfc_cert"}, ...]. Output as JSON? The tool expects to be called. We\'ll call the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3326, 'outputTokens': 801, 'totalTokens': 4127}, 'metrics': {'latencyMs': 3528}}
2025-09-24 15:25:31,568 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:31,610 - INFO - 

nmfc_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 3,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 15:25:31,610 - INFO - 

✓ Saved result: output/run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,913 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/306b6bac_nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:31,915 - INFO - 
📊 Processing Summary:
2025-09-24 15:25:31,915 - INFO -    Total files: 15
2025-09-24 15:25:31,915 - INFO -    Successful: 15
2025-09-24 15:25:31,915 - INFO -    Failed: 0
2025-09-24 15:25:31,915 - INFO -    Duration: 24.66 seconds
2025-09-24 15:25:31,915 - INFO -    Output directory: output
2025-09-24 15:25:31,915 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:25:31,915 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,915 - INFO -    📄 DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,915 - INFO -    📄 DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 Y82AJRDQU1FCXFHREDEA.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 nmfc_D5KCCMIJXGREC9Q81E0H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 nmfc_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 15:25:31,916 - INFO -    📄 nmfc_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}
2025-09-24 15:25:31,917 - INFO - 
============================================================================================================================================
2025-09-24 15:25:31,917 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:25:31,917 - INFO - ============================================================================================================================================
2025-09-24 15:25:31,917 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:25:31,917 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:25:31,918 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:25:31,918 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:25:31,918 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:25:31,918 - INFO - 
2025-09-24 15:25:31,918 - INFO - DCY7SLNMWUXIENOREHQF.pdf                           1      weight_and_inspect... run1_DCY7SLNMWUXIENOREHQF.json                    
2025-09-24 15:25:31,918 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:25:31,918 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 15:25:31,918 - INFO - 
2025-09-24 15:25:31,918 - INFO - DFY1VDZWR7NBDLJV02G2.pdf                           1      weight_and_inspect... run1_DFY1VDZWR7NBDLJV02G2.json                    
2025-09-24 15:25:31,918 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 15:25:31,918 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 15:25:31,918 - INFO - 
2025-09-24 15:25:31,918 - INFO - HFPAXYL947DH59AB12FL.pdf                           1      weight_and_inspect... run1_HFPAXYL947DH59AB12FL.json                    
2025-09-24 15:25:31,918 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/HFPAXYL947DH59AB12FL.pdf
2025-09-24 15:25:31,918 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HFPAXYL947DH59AB12FL.json
2025-09-24 15:25:31,918 - INFO - 
2025-09-24 15:25:31,918 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      inspection_cert      run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 15:25:31,918 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - K9VSARJOKAIZHNJ5RBDT.pdf                           1      weight_and_inspect... run1_K9VSARJOKAIZHNJ5RBDT.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - OR9EL08KIKNQPZ3UV3HH.pdf                           1      weight_and_inspect... run1_OR9EL08KIKNQPZ3UV3HH.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - OR9EL08KIKNQPZ3UV3HH.pdf                           2      weight_and_inspect... run1_OR9EL08KIKNQPZ3UV3HH.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - R1V0MO844PBLWNEAUETU.pdf                           1      weight_and_inspect... run1_R1V0MO844PBLWNEAUETU.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_R1V0MO844PBLWNEAUETU.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - R1V0MO844PBLWNEAUETU.pdf                           2      weight_and_inspect... run1_R1V0MO844PBLWNEAUETU.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_R1V0MO844PBLWNEAUETU.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - RUDVGETVRZO7XX6YNW7I.pdf                           1      nmfc_cert            run1_RUDVGETVRZO7XX6YNW7I.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 15:25:31,919 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 15:25:31,919 - INFO - 
2025-09-24 15:25:31,919 - INFO - WRKSHW76B3QUG47QWR75.pdf                           1      weight_and_inspect... run1_WRKSHW76B3QUG47QWR75.json                    
2025-09-24 15:25:31,919 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/WRKSHW76B3QUG47QWR75.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WRKSHW76B3QUG47QWR75.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - XCJLXZK140FUS8020ZAG.pdf                           1      weight_and_inspect... run1_XCJLXZK140FUS8020ZAG.json                    
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/XCJLXZK140FUS8020ZAG.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XCJLXZK140FUS8020ZAG.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - Y82AJRDQU1FCXFHREDEA.pdf                           1      weight_and_inspect... run1_Y82AJRDQU1FCXFHREDEA.json                    
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y82AJRDQU1FCXFHREDEA.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - nmfc_D5KCCMIJXGREC9Q81E0H.pdf                      1      nmfc_cert            run1_nmfc_D5KCCMIJXGREC9Q81E0H.json               
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_nmfc_D5KCCMIJXGREC9Q81E0H.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - nmfc_DH0JZ2JWDGRHD26BX74C.pdf                      1      nmfc_cert            run1_nmfc_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_nmfc_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - nmfc_DH0JZ2JWDGRHD26BX74C.pdf                      2      nmfc_cert            run1_nmfc_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_nmfc_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - nmfc_NJ4WSZ8BUQAW48V6403N.pdf                      1      nmfc_cert            run1_nmfc_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - nmfc_NJ4WSZ8BUQAW48V6403N.pdf                      2      nmfc_cert            run1_nmfc_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - nmfc_NJ4WSZ8BUQAW48V6403N.pdf                      3      nmfc_cert            run1_nmfc_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 15:25:31,920 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 15:25:31,920 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,920 - INFO - 
2025-09-24 15:25:31,920 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:25:31,920 - INFO - Total entries: 20
2025-09-24 15:25:31,920 - INFO - ============================================================================================================================================
2025-09-24 15:25:31,920 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:25:31,920 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:25:31,920 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:25:31,920 - INFO -   2. DCY7SLNMWUXIENOREHQF.pdf            Page 1   → weight_and_inspection_cert | run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 15:25:31,920 - INFO -   3. DFY1VDZWR7NBDLJV02G2.pdf            Page 1   → weight_and_inspection_cert | run1_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 15:25:31,920 - INFO -   4. HFPAXYL947DH59AB12FL.pdf            Page 1   → weight_and_inspection_cert | run1_HFPAXYL947DH59AB12FL.json
2025-09-24 15:25:31,921 - INFO -   5. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 15:25:31,921 - INFO -   6. K9VSARJOKAIZHNJ5RBDT.pdf            Page 1   → weight_and_inspection_cert | run1_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 15:25:31,921 - INFO -   7. OR9EL08KIKNQPZ3UV3HH.pdf            Page 1   → weight_and_inspection_cert | run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 15:25:31,921 - INFO -   8. OR9EL08KIKNQPZ3UV3HH.pdf            Page 2   → weight_and_inspection_cert | run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 15:25:31,921 - INFO -   9. R1V0MO844PBLWNEAUETU.pdf            Page 1   → weight_and_inspection_cert | run1_R1V0MO844PBLWNEAUETU.json
2025-09-24 15:25:31,921 - INFO -  10. R1V0MO844PBLWNEAUETU.pdf            Page 2   → weight_and_inspection_cert | run1_R1V0MO844PBLWNEAUETU.json
2025-09-24 15:25:31,921 - INFO -  11. RUDVGETVRZO7XX6YNW7I.pdf            Page 1   → nmfc_cert       | run1_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 15:25:31,921 - INFO -  12. WRKSHW76B3QUG47QWR75.pdf            Page 1   → weight_and_inspection_cert | run1_WRKSHW76B3QUG47QWR75.json
2025-09-24 15:25:31,921 - INFO -  13. XCJLXZK140FUS8020ZAG.pdf            Page 1   → weight_and_inspection_cert | run1_XCJLXZK140FUS8020ZAG.json
2025-09-24 15:25:31,921 - INFO -  14. Y82AJRDQU1FCXFHREDEA.pdf            Page 1   → weight_and_inspection_cert | run1_Y82AJRDQU1FCXFHREDEA.json
2025-09-24 15:25:31,921 - INFO -  15. nmfc_D5KCCMIJXGREC9Q81E0H.pdf       Page 1   → nmfc_cert       | run1_nmfc_D5KCCMIJXGREC9Q81E0H.json
2025-09-24 15:25:31,921 - INFO -  16. nmfc_DH0JZ2JWDGRHD26BX74C.pdf       Page 1   → nmfc_cert       | run1_nmfc_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 15:25:31,921 - INFO -  17. nmfc_DH0JZ2JWDGRHD26BX74C.pdf       Page 2   → nmfc_cert       | run1_nmfc_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 15:25:31,921 - INFO -  18. nmfc_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → nmfc_cert       | run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,921 - INFO -  19. nmfc_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → nmfc_cert       | run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,921 - INFO -  20. nmfc_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → nmfc_cert       | run1_nmfc_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 15:25:31,921 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:25:31,921 - INFO - 
✅ Test completed: {'total_files': 15, 'processed': 15, 'failed': 0, 'errors': [], 'duration_seconds': 24.655057, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}, {'filename': 'DCY7SLNMWUXIENOREHQF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf'}, {'filename': 'DFY1VDZWR7NBDLJV02G2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/DFY1VDZWR7NBDLJV02G2.pdf'}, {'filename': 'HFPAXYL947DH59AB12FL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/HFPAXYL947DH59AB12FL.pdf'}, {'filename': 'I_QHD3LC0DU6S8O2YVVS60.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf'}, {'filename': 'K9VSARJOKAIZHNJ5RBDT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/K9VSARJOKAIZHNJ5RBDT.pdf'}, {'filename': 'OR9EL08KIKNQPZ3UV3HH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf'}, {'filename': 'R1V0MO844PBLWNEAUETU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf'}, {'filename': 'RUDVGETVRZO7XX6YNW7I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/RUDVGETVRZO7XX6YNW7I.pdf'}, {'filename': 'WRKSHW76B3QUG47QWR75.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/WRKSHW76B3QUG47QWR75.pdf'}, {'filename': 'XCJLXZK140FUS8020ZAG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/XCJLXZK140FUS8020ZAG.pdf'}, {'filename': 'Y82AJRDQU1FCXFHREDEA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/Y82AJRDQU1FCXFHREDEA.pdf'}, {'filename': 'nmfc_D5KCCMIJXGREC9Q81E0H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_D5KCCMIJXGREC9Q81E0H.pdf'}, {'filename': 'nmfc_DH0JZ2JWDGRHD26BX74C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_DH0JZ2JWDGRHD26BX74C.pdf'}, {'filename': 'nmfc_NJ4WSZ8BUQAW48V6403N.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}, {'page_no': 3, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/weight_and_inspection_cert/nmfc_NJ4WSZ8BUQAW48V6403N.pdf'}]}
