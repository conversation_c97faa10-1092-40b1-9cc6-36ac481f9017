{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 1.2146754215791589e-06}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.422983359589125e-06, "Y": 1.0}]}, "Id": "c38931de-5d0d-4e6a-bbac-440e0f51c95e", "Relationships": [{"Type": "CHILD", "Ids": ["4648f156-af37-4e74-8b45-df03fe91e75b", "3fb5a490-335b-4c3c-960c-b64f5cf78727", "015cbf98-e39b-4e42-95fa-038481854e7c", "7043fedd-4366-42d6-a5af-cd8ab3628228", "fd802519-f1d0-4b54-a93d-4922006a5106", "16f5b12c-ded8-40b3-9830-19a948bee918", "848a35ff-39cd-498e-b1db-93a2007c6df8", "9f2d9b1f-7480-451b-8aea-a819fa713ae8", "1db87410-55be-4ce8-90da-c2885aa920f5", "0f839311-7160-4b80-bd6c-3e23ebcbba66", "3f4985e1-ffbe-4131-8915-3cd3d6dbb177", "11fd45d8-3281-4efc-aa18-1041ad23af2d", "175ed67b-4dcf-4f43-ae5d-0a69af9b6fff", "5a6b5a67-5efc-4f64-8257-19b72f7804f5", "46f2206c-7b36-45b1-86fb-0acc8d720c4b", "12c39e16-5be0-45c1-803b-41070589a29a", "a755c9de-e51b-4b82-9fba-402935f756f9", "c5172c2a-d2c4-49d3-b804-b54026cbee46", "2eeb0eab-5e55-43d9-895b-7131ee416169", "49ee4c93-65d9-4029-b468-4dd0102144fd", "f45d9907-3ac9-46d5-a4c0-1c8235315d18", "2e89b9e7-b53c-4622-9c73-df981e0603ae", "d43c3fb9-e305-4225-8d5b-88e1e2610f8d", "aff0b799-83df-4b8e-8c67-f21171b0c758", "45761c86-c3b6-49dc-8957-4fac818a2188", "f999fae5-78ba-41a7-b49c-021fc236b7df", "3d760302-5859-48bf-ba75-3c621b58c004", "f4bf89ec-3094-41c5-8f52-08fc9439548b", "7c647389-921d-48fd-9ad2-09db0e6e95a4", "f8a31a8f-6d13-4518-91f6-46e478185a5e", "530d7d7f-b506-434c-94c3-b60db2fd62ef"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "La Fama Foods, Inc.", "Geometry": {"BoundingBox": {"Width": 0.155225932598114, "Height": 0.016877708956599236, "Left": 0.058230701833963394, "Top": 0.0358361080288887}, "Polygon": [{"X": 0.058230701833963394, "Y": 0.036011554300785065}, {"X": 0.2134387195110321, "Y": 0.0358361080288887}, {"X": 0.2134566307067871, "Y": 0.05253860726952553}, {"X": 0.05824832245707512, "Y": 0.05271381512284279}]}, "Id": "4648f156-af37-4e74-8b45-df03fe91e75b", "Relationships": [{"Type": "CHILD", "Ids": ["04f5dbd8-9100-4d2c-906e-13189b82554d", "5d31826b-88d8-4f47-af19-acaa8ec2418d", "00cfe0db-fc58-4270-901a-98809faa4997", "64615979-d124-4279-9295-de89af25ec0f"]}]}, {"BlockType": "LINE", "Confidence": 99.47674560546875, "Text": "Packing Slip", "Geometry": {"BoundingBox": {"Width": 0.20476484298706055, "Height": 0.03264576569199562, "Left": 0.7223048806190491, "Top": 0.02207297831773758}, "Polygon": [{"X": 0.7223048806190491, "Y": 0.022304650396108627}, {"X": 0.9270323514938354, "Y": 0.02207297831773758}, {"X": 0.9270697236061096, "Y": 0.0544876866042614}, {"X": 0.7223414778709412, "Y": 0.0547187440097332}]}, "Id": "3fb5a490-335b-4c3c-960c-b64f5cf78727", "Relationships": [{"Type": "CHILD", "Ids": ["92ce1614-9e11-4397-9d18-ae4c909c977b", "63271a86-1f23-4b77-880b-3b49544374b7"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.032103195786476135, "Height": 0.009185844101011753, "Left": 0.7511487007141113, "Top": 0.059449903666973114}, "Polygon": [{"X": 0.7511487007141113, "Y": 0.059486109763383865}, {"X": 0.7832415103912354, "Y": 0.059449903666973114}, {"X": 0.7832518815994263, "Y": 0.06859957426786423}, {"X": 0.7511590719223022, "Y": 0.06863574683666229}]}, "Id": "015cbf98-e39b-4e42-95fa-038481854e7c", "Relationships": [{"Type": "CHILD", "Ids": ["b1ab9849-a8eb-468a-80bc-107e10d582c5"]}]}, {"BlockType": "LINE", "Confidence": 99.32557678222656, "Text": "S.O. No.", "Geometry": {"BoundingBox": {"Width": 0.05733350291848183, "Height": 0.011028975248336792, "Left": 0.8516311645507812, "Top": 0.06113944947719574}, "Polygon": [{"X": 0.8516311645507812, "Y": 0.061204105615615845}, {"X": 0.9089520573616028, "Y": 0.06113944947719574}, {"X": 0.9089646935462952, "Y": 0.0721038281917572}, {"X": 0.8516437411308289, "Y": 0.07216842472553253}]}, "Id": "7043fedd-4366-42d6-a5af-cd8ab3628228", "Relationships": [{"Type": "CHILD", "Ids": ["559e129c-62a1-4aed-8b9c-657db9808af1", "8817a3fe-10bb-4e3a-b07c-68f5541e80be"]}]}, {"BlockType": "LINE", "Confidence": 99.93522644042969, "Text": "7566 US Hwy 259 North", "Geometry": {"BoundingBox": {"Width": 0.19080911576747894, "Height": 0.020698703825473785, "Left": 0.05990779399871826, "Top": 0.07883812487125397}, "Polygon": [{"X": 0.05990779399871826, "Y": 0.07905302941799164}, {"X": 0.2506948709487915, "Y": 0.07883812487125397}, {"X": 0.250716894865036, "Y": 0.09932228922843933}, {"X": 0.05992940813302994, "Y": 0.09953682869672775}]}, "Id": "fd802519-f1d0-4b54-a93d-4922006a5106", "Relationships": [{"Type": "CHILD", "Ids": ["9784255f-d2e7-4fb2-a334-8c2b42ec486d", "500605ee-24ce-4f9f-891d-d4577ad20ddb", "1327eeff-28d0-4312-bcb4-5a2b21230fdf", "9bb589bc-e0fc-440b-93ee-0167f436660f", "ddfe9711-83ad-4af2-9d4d-96e2908c3fb9"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "5/7/2025", "Geometry": {"BoundingBox": {"Width": 0.05458875000476837, "Height": 0.00940095167607069, "Left": 0.742076575756073, "Top": 0.08625839650630951}, "Polygon": [{"X": 0.742076575756073, "Y": 0.08631983399391174}, {"X": 0.7966547012329102, "Y": 0.08625839650630951}, {"X": 0.7966653108596802, "Y": 0.09559796005487442}, {"X": 0.7420871257781982, "Y": 0.09565935283899307}]}, "Id": "16f5b12c-ded8-40b3-9830-19a948bee918", "Relationships": [{"Type": "CHILD", "Ids": ["3c6ac8fd-5592-49b9-a0f3-4245d921f6d5"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "9447", "Geometry": {"BoundingBox": {"Width": 0.031048158183693886, "Height": 0.0094019565731287, "Left": 0.8687058091163635, "Top": 0.08655086159706116}, "Polygon": [{"X": 0.8687058091163635, "Y": 0.08658579736948013}, {"X": 0.8997431993484497, "Y": 0.08655086159706116}, {"X": 0.899753987789154, "Y": 0.09591791033744812}, {"X": 0.8687165379524231, "Y": 0.09595281630754471}]}, "Id": "848a35ff-39cd-498e-b1db-93a2007c6df8", "Relationships": [{"Type": "CHILD", "Ids": ["63df8e10-a56b-4778-95cc-47351affdcf6"]}]}, {"BlockType": "LINE", "Confidence": 99.2332992553711, "Text": "Ship To", "Geometry": {"BoundingBox": {"Width": 0.0530698336660862, "Height": 0.014249851927161217, "Left": 0.5458984375, "Top": 0.14088226854801178}, "Polygon": [{"X": 0.5458984375, "Y": 0.14094170928001404}, {"X": 0.5989524126052856, "Y": 0.14088226854801178}, {"X": 0.5989682674407959, "Y": 0.15507273375988007}, {"X": 0.5459141731262207, "Y": 0.15513211488723755}]}, "Id": "9f2d9b1f-7480-451b-8aea-a819fa713ae8", "Relationships": [{"Type": "CHILD", "Ids": ["2aae019e-ae06-4cde-8e97-7ded10bde8cb", "69bbfa2e-6f00-4e22-8680-db83061867c1"]}]}, {"BlockType": "LINE", "Confidence": 99.7448959350586, "Text": "COMMISSARY WAREHOUSE", "Geometry": {"BoundingBox": {"Width": 0.20447805523872375, "Height": 0.01971619203686714, "Left": 0.5318998098373413, "Top": 0.16050034761428833}, "Polygon": [{"X": 0.5318998098373413, "Y": 0.1607290804386139}, {"X": 0.7363558411598206, "Y": 0.16050034761428833}, {"X": 0.7363778352737427, "Y": 0.17998817563056946}, {"X": 0.53192138671875, "Y": 0.18021653592586517}]}, "Id": "1db87410-55be-4ce8-90da-c2885aa920f5", "Relationships": [{"Type": "CHILD", "Ids": ["0d874f19-b60b-438b-a8f5-996c83494b49", "674d7d28-919e-4f2e-94f7-2b396dca3e93"]}]}, {"BlockType": "LINE", "Confidence": 99.79676818847656, "Text": "1621 HIGHWAY 75 NORTH", "Geometry": {"BoundingBox": {"Width": 0.1863696426153183, "Height": 0.019300583750009537, "Left": 0.5321668982505798, "Top": 0.17448702454566956}, "Polygon": [{"X": 0.5321668982505798, "Y": 0.17469525337219238}, {"X": 0.7185149788856506, "Y": 0.17448702454566956}, {"X": 0.7185365557670593, "Y": 0.19357970356941223}, {"X": 0.5321880578994751, "Y": 0.1937876045703888}]}, "Id": "0f839311-7160-4b80-bd6c-3e23ebcbba66", "Relationships": [{"Type": "CHILD", "Ids": ["6e52173d-33bc-4eab-8f9f-934320a5cd15", "c8920e12-e7f4-48bb-be4f-b5ae1596fe22", "526a78ae-afa0-4012-a759-be3e159915f3", "63b776ed-135c-4783-84cd-8ea6b6e05bc2"]}]}, {"BlockType": "LINE", "Confidence": 99.71546173095703, "Text": "HUNTSVILLE, TX 77340", "Geometry": {"BoundingBox": {"Width": 0.16706280410289764, "Height": 0.01910005882382393, "Left": 0.5334128737449646, "Top": 0.18849608302116394}, "Polygon": [{"X": 0.5334128737449646, "Y": 0.18868252635002136}, {"X": 0.7004544138908386, "Y": 0.18849608302116394}, {"X": 0.7004756927490234, "Y": 0.20740997791290283}, {"X": 0.5334338545799255, "Y": 0.20759613811969757}]}, "Id": "3f4985e1-ffbe-4131-8915-3cd3d6dbb177", "Relationships": [{"Type": "CHILD", "Ids": ["fdfc1e9a-ce0d-42b6-8b99-9356a3a21819", "352426cc-3b0e-442c-926e-834a98806e67", "8b8a9d22-1e99-4f29-b3f2-dc8f18a576dd"]}]}, {"BlockType": "LINE", "Confidence": 99.8046875, "Text": "P.O. No.", "Geometry": {"BoundingBox": {"Width": 0.05693945661187172, "Height": 0.010069436393678188, "Left": 0.2796013653278351, "Top": 0.32655981183052063}, "Polygon": [{"X": 0.2796013653278351, "Y": 0.3266226053237915}, {"X": 0.3365299701690674, "Y": 0.32655981183052063}, {"X": 0.3365408480167389, "Y": 0.3365664780139923}, {"X": 0.2796121835708618, "Y": 0.3366292417049408}]}, "Id": "11fd45d8-3281-4efc-aa18-1041ad23af2d", "Relationships": [{"Type": "CHILD", "Ids": ["2cbf5d5c-7fc3-426f-bcf8-901bd49c84c1", "1c8e0fd4-597a-4bf1-94a7-8225284e576a"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Ship Date", "Geometry": {"BoundingBox": {"Width": 0.06733401864767075, "Height": 0.01281500793993473, "Left": 0.40722447633743286, "Top": 0.3223007321357727}, "Polygon": [{"X": 0.40722447633743286, "Y": 0.32237502932548523}, {"X": 0.4745444655418396, "Y": 0.3223007321357727}, {"X": 0.4745585024356842, "Y": 0.33504149317741394}, {"X": 0.4072384238243103, "Y": 0.3351157307624817}]}, "Id": "175ed67b-4dcf-4f43-ae5d-0a69af9b6fff", "Relationships": [{"Type": "CHILD", "Ids": ["f5e5e0d8-77d5-4134-9d2d-11f1af711fd1", "bcdcd69a-c348-4292-9ee1-4ef430c68b94"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Ship Via", "Geometry": {"BoundingBox": {"Width": 0.057199668139219284, "Height": 0.01355661079287529, "Left": 0.5341155529022217, "Top": 0.317913681268692}, "Polygon": [{"X": 0.5341155529022217, "Y": 0.31797683238983154}, {"X": 0.5913001894950867, "Y": 0.317913681268692}, {"X": 0.5913152694702148, "Y": 0.33140721917152405}, {"X": 0.5341305136680603, "Y": 0.3314703106880188}]}, "Id": "5a6b5a67-5efc-4f64-8257-19b72f7804f5", "Relationships": [{"Type": "CHILD", "Ids": ["4d4c62e6-68f2-4677-a5e7-a18a75f9e125", "741f166c-d889-47d2-9374-043a8ab8b9d8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "FOB", "Geometry": {"BoundingBox": {"Width": 0.02991212159395218, "Height": 0.010686157271265984, "Left": 0.6970155239105225, "Top": 0.3188648819923401}, "Polygon": [{"X": 0.6970155239105225, "Y": 0.31889790296554565}, {"X": 0.7269155979156494, "Y": 0.3188648819923401}, {"X": 0.726927638053894, "Y": 0.32951807975769043}, {"X": 0.6970275044441223, "Y": 0.3295510411262512}]}, "Id": "46f2206c-7b36-45b1-86fb-0acc8d720c4b", "Relationships": [{"Type": "CHILD", "Ids": ["70f71121-0e7a-4529-8a7f-0d32208fe0f2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Project", "Geometry": {"BoundingBox": {"Width": 0.04877810552716255, "Height": 0.012779688462615013, "Left": 0.8520395755767822, "Top": 0.320295125246048}, "Polygon": [{"X": 0.8520395755767822, "Y": 0.32034894824028015}, {"X": 0.9008030295372009, "Y": 0.320295125246048}, {"X": 0.9008176326751709, "Y": 0.33302104473114014}, {"X": 0.8520541191101074, "Y": 0.33307480812072754}]}, "Id": "12c39e16-5be0-45c1-803b-41070589a29a", "Relationships": [{"Type": "CHILD", "Ids": ["d0cfbcca-f703-4fb1-b870-244279ed624f"]}]}, {"BlockType": "LINE", "Confidence": 99.59243774414062, "Text": "ED083441/25107218", "Geometry": {"BoundingBox": {"Width": 0.130945086479187, "Height": 0.011779462918639183, "Left": 0.2423161417245865, "Top": 0.35456016659736633}, "Polygon": [{"X": 0.2423161417245865, "Y": 0.3547042906284332}, {"X": 0.37324854731559753, "Y": 0.35456016659736633}, {"X": 0.3732612431049347, "Y": 0.3661956489086151}, {"X": 0.24232865869998932, "Y": 0.36633965373039246}]}, "Id": "a755c9de-e51b-4b82-9fba-402935f756f9", "Relationships": [{"Type": "CHILD", "Ids": ["bfcfbea2-7607-4ff4-b40d-5ea1806a429c"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "5/8/2025", "Geometry": {"BoundingBox": {"Width": 0.05449913069605827, "Height": 0.010287459939718246, "Left": 0.4136713147163391, "Top": 0.3519383370876312}, "Polygon": [{"X": 0.4136713147163391, "Y": 0.35199832916259766}, {"X": 0.46815916895866394, "Y": 0.3519383370876312}, {"X": 0.4681704342365265, "Y": 0.3621658682823181}, {"X": 0.4136824905872345, "Y": 0.36222580075263977}]}, "Id": "c5172c2a-d2c4-49d3-b804-b54026cbee46", "Relationships": [{"Type": "CHILD", "Ids": ["c2cdf042-ffb4-45f8-b5d0-853dcb0a7ed2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.076120525598526, "Height": 0.011910404078662395, "Left": 0.3357919156551361, "Top": 0.3829190731048584}, "Polygon": [{"X": 0.3357919156551361, "Y": 0.3830026686191559}, {"X": 0.41189950704574585, "Y": 0.3829190731048584}, {"X": 0.4119124412536621, "Y": 0.39474600553512573}, {"X": 0.3358047604560852, "Y": 0.39482948184013367}]}, "Id": "2eeb0eab-5e55-43d9-895b-7131ee416169", "Relationships": [{"Type": "CHILD", "Ids": ["d8e55374-0ad6-4489-b88b-da2237d676e1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipped", "Geometry": {"BoundingBox": {"Width": 0.056103724986314774, "Height": 0.013063081540167332, "Left": 0.6132440567016602, "Top": 0.3784119784832001}, "Polygon": [{"X": 0.6132440567016602, "Y": 0.37847357988357544}, {"X": 0.6693332195281982, "Y": 0.3784119784832001}, {"X": 0.6693478226661682, "Y": 0.39141350984573364}, {"X": 0.6132586002349854, "Y": 0.39147505164146423}]}, "Id": "49ee4c93-65d9-4029-b468-4dd0102144fd", "Relationships": [{"Type": "CHILD", "Ids": ["6b47b3fb-b356-42b8-961c-95eb3f4602f2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Ordered", "Geometry": {"BoundingBox": {"Width": 0.05541332811117172, "Height": 0.010248161852359772, "Left": 0.7556688189506531, "Top": 0.38107600808143616}, "Polygon": [{"X": 0.7556688189506531, "Y": 0.38113686442375183}, {"X": 0.8110705614089966, "Y": 0.38107600808143616}, {"X": 0.811082124710083, "Y": 0.3912633955478668}, {"X": 0.7556803226470947, "Y": 0.3913241922855377}]}, "Id": "f45d9907-3ac9-46d5-a4c0-1c8235315d18", "Relationships": [{"Type": "CHILD", "Ids": ["2590f3c3-e18a-40d6-b28e-da671a176902"]}]}, {"BlockType": "LINE", "Confidence": 99.83318328857422, "Text": "U/M", "Geometry": {"BoundingBox": {"Width": 0.028358038514852524, "Height": 0.010318269021809101, "Left": 0.8911812901496887, "Top": 0.3806443512439728}, "Polygon": [{"X": 0.8911812901496887, "Y": 0.3806754946708679}, {"X": 0.9195274710655212, "Y": 0.3806443512439728}, {"X": 0.9195392727851868, "Y": 0.39093151688575745}, {"X": 0.8911930918693542, "Y": 0.3909626305103302}]}, "Id": "2e89b9e7-b53c-4622-9c73-df981e0603ae", "Relationships": [{"Type": "CHILD", "Ids": ["4dfc1794-7f5c-4254-bd65-c2337585fd65"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.028371093794703484, "Height": 0.009529687464237213, "Left": 0.09963473677635193, "Top": 0.388615220785141}, "Polygon": [{"X": 0.09963473677635193, "Y": 0.38864636421203613}, {"X": 0.12799572944641113, "Y": 0.388615220785141}, {"X": 0.12800583243370056, "Y": 0.39811381697654724}, {"X": 0.09964480251073837, "Y": 0.39814493060112}]}, "Id": "d43c3fb9-e305-4225-8d5b-88e1e2610f8d", "Relationships": [{"Type": "CHILD", "Ids": ["1250fe89-9b5f-4b3c-a34c-a62a50b9081f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "352750", "Geometry": {"BoundingBox": {"Width": 0.044132642447948456, "Height": 0.0101799750700593, "Left": 0.04988868907094002, "Top": 0.413134902715683}, "Polygon": [{"X": 0.04988868907094002, "Y": 0.4131832420825958}, {"X": 0.0940105989575386, "Y": 0.413134902715683}, {"X": 0.09402132779359818, "Y": 0.42326658964157104}, {"X": 0.04989936947822571, "Y": 0.4233148694038391}]}, "Id": "aff0b799-83df-4b8e-8c67-f21171b0c758", "Relationships": [{"Type": "CHILD", "Ids": ["60ef6cc8-19ef-4783-8390-23a8944ec81e"]}]}, {"BlockType": "LINE", "Confidence": 99.52777862548828, "Text": "BARBCO TORTILLA CHIPS 12/16OZ 48/CS/PALLET", "Geometry": {"BoundingBox": {"Width": 0.34966161847114563, "Height": 0.014349089004099369, "Left": 0.18764354288578033, "Top": 0.40530407428741455}, "Polygon": [{"X": 0.18764354288578033, "Y": 0.4056873023509979}, {"X": 0.5372896790504456, "Y": 0.40530407428741455}, {"X": 0.5373051762580872, "Y": 0.419270396232605}, {"X": 0.18765847384929657, "Y": 0.41965317726135254}]}, "Id": "45761c86-c3b6-49dc-8957-4fac818a2188", "Relationships": [{"Type": "CHILD", "Ids": ["10b502a9-7877-444c-8e0d-e50bc2de1c6c", "1ae6fcae-e15a-41a8-a24f-224ec82c3140", "f9499429-31a2-4308-b66c-7f33d9c1fe99", "f72eca05-2f24-4cb0-8476-ac67897b0e5b", "44971ec6-2df8-4b65-bd96-f56ab3840e30"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "1,440", "Geometry": {"BoundingBox": {"Width": 0.033815279603004456, "Height": 0.010908486321568489, "Left": 0.8168557286262512, "Top": 0.4065384566783905}, "Polygon": [{"X": 0.8168557286262512, "Y": 0.4065755009651184}, {"X": 0.8506585359573364, "Y": 0.4065384566783905}, {"X": 0.8506709933280945, "Y": 0.4174099266529083}, {"X": 0.8168680667877197, "Y": 0.41744694113731384}]}, "Id": "f999fae5-78ba-41a7-b49c-021fc236b7df", "Relationships": [{"Type": "CHILD", "Ids": ["3603a28e-c5dc-4c41-855a-3df8d501b325"]}]}, {"BlockType": "LINE", "Confidence": 99.03359985351562, "Text": "ED083441/25107218", "Geometry": {"BoundingBox": {"Width": 0.1305546909570694, "Height": 0.01180802658200264, "Left": 0.18786683678627014, "Top": 0.449667751789093}, "Polygon": [{"X": 0.18786683678627014, "Y": 0.44981029629707336}, {"X": 0.31840887665748596, "Y": 0.449667751789093}, {"X": 0.31842151284217834, "Y": 0.46133336424827576}, {"X": 0.1878793090581894, "Y": 0.46147575974464417}]}, "Id": "3d760302-5859-48bf-ba75-3c621b58c004", "Relationships": [{"Type": "CHILD", "Ids": ["89e11793-48ae-481b-9c3d-8a4cc850946c"]}]}, {"BlockType": "LINE", "Confidence": 45.151268005371094, "Text": "Means", "Geometry": {"BoundingBox": {"Width": 0.16072411835193634, "Height": 0.06985219568014145, "Left": 0.7801271677017212, "Top": 0.430331289768219}, "Polygon": [{"X": 0.7801271677017212, "Y": 0.43050697445869446}, {"X": 0.9407709240913391, "Y": 0.430331289768219}, {"X": 0.9408512711524963, "Y": 0.5000088214874268}, {"X": 0.7802062630653381, "Y": 0.5001834630966187}]}, "Id": "f4bf89ec-3094-41c5-8f52-08fc9439548b", "Relationships": [{"Type": "CHILD", "Ids": ["a7ea371d-f8a4-4df3-82a5-5e3f76c7fc51"]}]}, {"BlockType": "LINE", "Confidence": 46.332706451416016, "Text": "52/3/9", "Geometry": {"BoundingBox": {"Width": 0.1523926556110382, "Height": 0.05980904772877693, "Left": 0.7873420119285583, "Top": 0.48763224482536316}, "Polygon": [{"X": 0.7873420119285583, "Y": 0.4877980351448059}, {"X": 0.9396659135818481, "Y": 0.48763224482536316}, {"X": 0.9397346377372742, "Y": 0.5472763776779175}, {"X": 0.787409782409668, "Y": 0.547441303730011}]}, "Id": "7c647389-921d-48fd-9ad2-09db0e6e95a4", "Relationships": [{"Type": "CHILD", "Ids": ["6dca9582-27e7-4236-b727-2eb0cd5ebe1a"]}]}, {"BlockType": "LINE", "Confidence": 87.40812683105469, "Text": "TRAiler # 2642", "Geometry": {"BoundingBox": {"Width": 0.24488507211208344, "Height": 0.09163658320903778, "Left": 0.26951441168785095, "Top": 0.7261245846748352}, "Polygon": [{"X": 0.26951441168785095, "Y": 0.7263855934143066}, {"X": 0.5142984390258789, "Y": 0.7261245846748352}, {"X": 0.5143994688987732, "Y": 0.8175022602081299}, {"X": 0.26961296796798706, "Y": 0.8177611827850342}]}, "Id": "f8a31a8f-6d13-4518-91f6-46e478185a5e", "Relationships": [{"Type": "CHILD", "Ids": ["b96f7179-9ff1-45fa-9652-b0aa38edd5e4", "037d101f-6659-4b83-8aab-2a8259139ab4", "d954590a-bb93-4bbb-8f26-f8e1dac4b671"]}]}, {"BlockType": "LINE", "Confidence": 87.81635284423828, "Text": "seal # E21784888", "Geometry": {"BoundingBox": {"Width": 0.2893807590007782, "Height": 0.0794324204325676, "Left": 0.30424341559410095, "Top": 0.7671695947647095}, "Polygon": [{"X": 0.30424341559410095, "Y": 0.7674769759178162}, {"X": 0.5935359597206116, "Y": 0.7671695947647095}, {"X": 0.5936241745948792, "Y": 0.8462968468666077}, {"X": 0.3043290674686432, "Y": 0.8466020226478577}]}, "Id": "530d7d7f-b506-434c-94c3-b60db2fd62ef", "Relationships": [{"Type": "CHILD", "Ids": ["bfde324a-98f8-4591-a365-33b8dc19e38f", "28c4dc76-3a3a-4f28-b850-36131b7d92fc", "c71748b7-d555-4a31-9a71-31aa43f664dc"]}]}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "La", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02044418454170227, "Height": 0.011891503818333149, "Left": 0.05823580175638199, "Top": 0.040822312235832214}, "Polygon": [{"X": 0.05823580175638199, "Y": 0.04084539785981178}, {"X": 0.07866743952035904, "Y": 0.040822312235832214}, {"X": 0.07867998629808426, "Y": 0.05269075185060501}, {"X": 0.05824832245707512, "Y": 0.05271381512284279}]}, "Id": "04f5dbd8-9100-4d2c-906e-13189b82554d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Fama", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043272197246551514, "Height": 0.01195492222905159, "Left": 0.08236884325742722, "Top": 0.03965885937213898}, "Polygon": [{"X": 0.08236884325742722, "Y": 0.03970774635672569}, {"X": 0.12562839686870575, "Y": 0.03965885937213898}, {"X": 0.12564104795455933, "Y": 0.05156494304537773}, {"X": 0.08238143473863602, "Y": 0.05161378160119057}]}, "Id": "5d31826b-88d8-4f47-af19-acaa8ec2418d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Foods,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051562123000621796, "Height": 0.013812694698572159, "Left": 0.12934349477291107, "Top": 0.03684850409626961}, "Polygon": [{"X": 0.12934349477291107, "Y": 0.03690676763653755}, {"X": 0.18089091777801514, "Y": 0.03684850409626961}, {"X": 0.18090561032295227, "Y": 0.050602998584508896}, {"X": 0.12935811281204224, "Y": 0.050661198794841766}]}, "Id": "00cfe0db-fc58-4270-901a-98809faa4997"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028121698647737503, "Height": 0.011606828309595585, "Left": 0.18532943725585938, "Top": 0.0358361080288887}, "Polygon": [{"X": 0.18532943725585938, "Y": 0.035867881029844284}, {"X": 0.2134387195110321, "Y": 0.0358361080288887}, {"X": 0.21345113217830658, "Y": 0.04741119220852852}, {"X": 0.18534180521965027, "Y": 0.04744293540716171}]}, "Id": "64615979-d124-4279-9295-de89af25ec0f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13308987021446228, "Height": 0.027205247431993484, "Left": 0.7223048806190491, "Top": 0.022154079750180244}, "Polygon": [{"X": 0.7223048806190491, "Y": 0.022304650396108627}, {"X": 0.8553637862205505, "Y": 0.022154079750180244}, {"X": 0.855394721031189, "Y": 0.04920909181237221}, {"X": 0.7223353981971741, "Y": 0.04935932904481888}]}, "Id": "92ce1614-9e11-4397-9d18-ae4c909c977b"}, {"BlockType": "WORD", "Confidence": 98.98278045654297, "Text": "Slip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062194082885980606, "Height": 0.024012602865695953, "Left": 0.8648756146430969, "Top": 0.030545245856046677}, "Polygon": [{"X": 0.8648756146430969, "Y": 0.03061554580926895}, {"X": 0.9270421266555786, "Y": 0.030545245856046677}, {"X": 0.9270697236061096, "Y": 0.0544876866042614}, {"X": 0.8649030327796936, "Y": 0.05455784872174263}]}, "Id": "63271a86-1f23-4b77-880b-3b49544374b7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032103195786476135, "Height": 0.009185844101011753, "Left": 0.7511487007141113, "Top": 0.059449903666973114}, "Polygon": [{"X": 0.7511487007141113, "Y": 0.059486109763383865}, {"X": 0.7832415103912354, "Y": 0.059449903666973114}, {"X": 0.7832518815994263, "Y": 0.06859957426786423}, {"X": 0.7511590719223022, "Y": 0.06863574683666229}]}, "Id": "b1ab9849-a8eb-468a-80bc-107e10d582c5"}, {"BlockType": "WORD", "Confidence": 98.85781860351562, "Text": "S.O.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02973199635744095, "Height": 0.009948259219527245, "Left": 0.8516311645507812, "Top": 0.061170581728219986}, "Polygon": [{"X": 0.8516311645507812, "Y": 0.061204105615615845}, {"X": 0.8813518285751343, "Y": 0.061170581728219986}, {"X": 0.8813631534576416, "Y": 0.07108534127473831}, {"X": 0.8516424894332886, "Y": 0.07111883908510208}]}, "Id": "559e129c-62a1-4aed-8b9c-657db9808af1"}, {"BlockType": "WORD", "Confidence": 99.79332733154297, "Text": "No.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023189758881926537, "Height": 0.009443229995667934, "Left": 0.8857749104499817, "Top": 0.06268671900033951}, "Polygon": [{"X": 0.8857749104499817, "Y": 0.06271285563707352}, {"X": 0.908953845500946, "Y": 0.06268671900033951}, {"X": 0.9089646935462952, "Y": 0.0721038281917572}, {"X": 0.885785698890686, "Y": 0.07212994992733002}]}, "Id": "8817a3fe-10bb-4e3a-b07c-68f5541e80be"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "7566", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03829710930585861, "Height": 0.013337871059775352, "Left": 0.05991537868976593, "Top": 0.08619895577430725}, "Polygon": [{"X": 0.05991537868976593, "Y": 0.08624205738306046}, {"X": 0.09819839894771576, "Y": 0.08619895577430725}, {"X": 0.09821248799562454, "Y": 0.09949377924203873}, {"X": 0.05992940813302994, "Y": 0.09953682869672775}]}, "Id": "9784255f-d2e7-4fb2-a334-8c2b42ec486d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "US", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024239128455519676, "Height": 0.012306634336709976, "Left": 0.10230592638254166, "Top": 0.08549626916646957}, "Polygon": [{"X": 0.10230592638254166, "Y": 0.08552354574203491}, {"X": 0.12653200328350067, "Y": 0.08549626916646957}, {"X": 0.12654505670070648, "Y": 0.0977756604552269}, {"X": 0.10231894254684448, "Y": 0.09780290722846985}]}, "Id": "500605ee-24ce-4f9f-891d-d4577ad20ddb"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Hwy", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03606602922081947, "Height": 0.012932161800563335, "Left": 0.13103726506233215, "Top": 0.08401086181402206}, "Polygon": [{"X": 0.13103726506233215, "Y": 0.0840514525771141}, {"X": 0.1670895367860794, "Y": 0.08401086181402206}, {"X": 0.16710329055786133, "Y": 0.09690247476100922}, {"X": 0.1310509741306305, "Y": 0.09694302827119827}]}, "Id": "1327eeff-28d0-4312-bcb4-5a2b21230fdf"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "259", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0293430108577013, "Height": 0.01283202599734068, "Left": 0.1702917516231537, "Top": 0.07970357686281204}, "Polygon": [{"X": 0.1702917516231537, "Y": 0.0797366052865982}, {"X": 0.1996210664510727, "Y": 0.07970357686281204}, {"X": 0.19963477551937103, "Y": 0.09250260144472122}, {"X": 0.17030541598796844, "Y": 0.092535600066185}]}, "Id": "9bb589bc-e0fc-440b-93ee-0167f436660f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "North", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04710863158106804, "Height": 0.012104558758437634, "Left": 0.20359919965267181, "Top": 0.07883812487125397}, "Polygon": [{"X": 0.20359919965267181, "Y": 0.07889117300510406}, {"X": 0.2506948709487915, "Y": 0.07883812487125397}, {"X": 0.25070783495903015, "Y": 0.09088968485593796}, {"X": 0.20361210405826569, "Y": 0.09094268083572388}]}, "Id": "ddfe9711-83ad-4af2-9d4d-96e2908c3fb9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "5/7/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05458875000476837, "Height": 0.00940095167607069, "Left": 0.742076575756073, "Top": 0.08625839650630951}, "Polygon": [{"X": 0.742076575756073, "Y": 0.08631983399391174}, {"X": 0.7966547012329102, "Y": 0.08625839650630951}, {"X": 0.7966653108596802, "Y": 0.09559796005487442}, {"X": 0.7420871257781982, "Y": 0.09565935283899307}]}, "Id": "3c6ac8fd-5592-49b9-a0f3-4245d921f6d5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "9447", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031048158183693886, "Height": 0.0094019565731287, "Left": 0.8687058091163635, "Top": 0.08655086159706116}, "Polygon": [{"X": 0.8687058091163635, "Y": 0.08658579736948013}, {"X": 0.8997431993484497, "Y": 0.08655086159706116}, {"X": 0.899753987789154, "Y": 0.09591791033744812}, {"X": 0.8687165379524231, "Y": 0.09595281630754471}]}, "Id": "63df8e10-a56b-4778-95cc-47351affdcf6"}, {"BlockType": "WORD", "Confidence": 98.51622772216797, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031018061563372612, "Height": 0.013630463741719723, "Left": 0.5458990931510925, "Top": 0.14150165021419525}, "Polygon": [{"X": 0.5458990931510925, "Y": 0.1415363848209381}, {"X": 0.5769020318984985, "Y": 0.14150165021419525}, {"X": 0.5769171714782715, "Y": 0.15509741008281708}, {"X": 0.5459141731262207, "Y": 0.15513211488723755}]}, "Id": "2aae019e-ae06-4cde-8e97-7ded10bde8cb"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01892998442053795, "Height": 0.009788205847144127, "Left": 0.5800333619117737, "Top": 0.14088226854801178}, "Polygon": [{"X": 0.5800333619117737, "Y": 0.14090345799922943}, {"X": 0.5989524126052856, "Y": 0.14088226854801178}, {"X": 0.5989633202552795, "Y": 0.1506492793560028}, {"X": 0.5800442099571228, "Y": 0.15067046880722046}]}, "Id": "69bbfa2e-6f00-4e22-8680-db83061867c1"}, {"BlockType": "WORD", "Confidence": 99.51908874511719, "Text": "COMMISSARY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10404125601053238, "Height": 0.01933813840150833, "Left": 0.5319001078605652, "Top": 0.16087840497493744}, "Polygon": [{"X": 0.5319001078605652, "Y": 0.1609947681427002}, {"X": 0.6359198093414307, "Y": 0.16087840497493744}, {"X": 0.6359413266181946, "Y": 0.18010035157203674}, {"X": 0.53192138671875, "Y": 0.18021653592586517}]}, "Id": "0d874f19-b60b-438b-a8f5-996c83494b49"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WAREHOUSE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09741970896720886, "Height": 0.010987448506057262, "Left": 0.6389484405517578, "Top": 0.16050034761428833}, "Polygon": [{"X": 0.6389484405517578, "Y": 0.16060931980609894}, {"X": 0.7363558411598206, "Y": 0.16050034761428833}, {"X": 0.7363681197166443, "Y": 0.17137891054153442}, {"X": 0.638960599899292, "Y": 0.17148779332637787}]}, "Id": "674d7d28-919e-4f2e-94f7-2b396dca3e93"}, {"BlockType": "WORD", "Confidence": 99.63628387451172, "Text": "1621", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029602691531181335, "Height": 0.0134657584130764, "Left": 0.5321731567382812, "Top": 0.1803218424320221}, "Polygon": [{"X": 0.5321731567382812, "Y": 0.18035489320755005}, {"X": 0.5617609024047852, "Y": 0.1803218424320221}, {"X": 0.5617758631706238, "Y": 0.19375459849834442}, {"X": 0.5321880578994751, "Y": 0.1937876045703888}]}, "Id": "6e52173d-33bc-4eab-8f9f-934320a5cd15"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "HIGHWAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07593536376953125, "Height": 0.012560483999550343, "Left": 0.5665880441665649, "Top": 0.17585763335227966}, "Polygon": [{"X": 0.5665880441665649, "Y": 0.17594246566295624}, {"X": 0.6425094604492188, "Y": 0.17585763335227966}, {"X": 0.6425234079360962, "Y": 0.18833337724208832}, {"X": 0.5666019320487976, "Y": 0.18841812014579773}]}, "Id": "c8920e12-e7f4-48bb-be4f-b5ae1596fe22"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01528243999928236, "Height": 0.00965083483606577, "Left": 0.6460040211677551, "Top": 0.17536145448684692}, "Polygon": [{"X": 0.6460040211677551, "Y": 0.17537853121757507}, {"X": 0.6612756252288818, "Y": 0.17536145448684692}, {"X": 0.661286473274231, "Y": 0.18499524891376495}, {"X": 0.6460148096084595, "Y": 0.18501229584217072}]}, "Id": "526a78ae-afa0-4012-a759-be3e159915f3"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "NORTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05341261625289917, "Height": 0.010081377811729908, "Left": 0.6651136875152588, "Top": 0.17448702454566956}, "Polygon": [{"X": 0.6651136875152588, "Y": 0.17454670369625092}, {"X": 0.7185149788856506, "Y": 0.17448702454566956}, {"X": 0.718526303768158, "Y": 0.1845087707042694}, {"X": 0.6651249527931213, "Y": 0.1845684051513672}]}, "Id": "63b776ed-135c-4783-84cd-8ea6b6e05bc2"}, {"BlockType": "WORD", "Confidence": 99.33195495605469, "Text": "HUNTSVILLE,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09861409664154053, "Height": 0.01898692175745964, "Left": 0.5334129333496094, "Top": 0.18860921263694763}, "Polygon": [{"X": 0.5334129333496094, "Y": 0.1887192577123642}, {"X": 0.6320059299468994, "Y": 0.18860921263694763}, {"X": 0.6320270299911499, "Y": 0.20748625695705414}, {"X": 0.5334338545799255, "Y": 0.20759613811969757}]}, "Id": "fdfc1e9a-ce0d-42b6-8b99-9356a3a21819"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021282615140080452, "Height": 0.009873166680335999, "Left": 0.6354315280914307, "Top": 0.18900930881500244}, "Polygon": [{"X": 0.6354315280914307, "Y": 0.18903306126594543}, {"X": 0.6567031145095825, "Y": 0.18900930881500244}, {"X": 0.656714141368866, "Y": 0.19885875284671783}, {"X": 0.6354425549507141, "Y": 0.19888247549533844}]}, "Id": "352426cc-3b0e-442c-926e-834a98806e67"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "77340", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03903298079967499, "Height": 0.010101716965436935, "Left": 0.6614327430725098, "Top": 0.18849608302116394}, "Polygon": [{"X": 0.6614327430725098, "Y": 0.18853962421417236}, {"X": 0.7004544138908386, "Y": 0.18849608302116394}, {"X": 0.700465738773346, "Y": 0.19855427742004395}, {"X": 0.6614440083503723, "Y": 0.19859778881072998}]}, "Id": "8b8a9d22-1e99-4f29-b3f2-dc8f18a576dd"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "P.O.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028993019834160805, "Height": 0.00979524664580822, "Left": 0.2796016335487366, "Top": 0.3268339931964874}, "Polygon": [{"X": 0.2796016335487366, "Y": 0.3268659710884094}, {"X": 0.3085840940475464, "Y": 0.3268339931964874}, {"X": 0.308594673871994, "Y": 0.3365972936153412}, {"X": 0.2796121835708618, "Y": 0.3366292417049408}]}, "Id": "2cbf5d5c-7fc3-426f-bcf8-901bd49c84c1"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "No.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022575443610548973, "Height": 0.009460359811782837, "Left": 0.3139647841453552, "Top": 0.32655981183052063}, "Polygon": [{"X": 0.3139647841453552, "Y": 0.32658469676971436}, {"X": 0.3365299701690674, "Y": 0.32655981183052063}, {"X": 0.33654022216796875, "Y": 0.33599528670310974}, {"X": 0.3139750063419342, "Y": 0.33602017164230347}]}, "Id": "1c8e0fd4-597a-4bf1-94a7-8225284e576a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030319800600409508, "Height": 0.011881669983267784, "Left": 0.40722545981407166, "Top": 0.32323405146598816}, "Polygon": [{"X": 0.40722545981407166, "Y": 0.32326751947402954}, {"X": 0.4375322759151459, "Y": 0.32323405146598816}, {"X": 0.4375452697277069, "Y": 0.3350823223590851}, {"X": 0.4072384238243103, "Y": 0.3351157307624817}]}, "Id": "f5e5e0d8-77d5-4134-9d2d-11f1af711fd1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032353475689888, "Height": 0.00994432158768177, "Left": 0.4422019124031067, "Top": 0.3223007321357727}, "Polygon": [{"X": 0.4422019124031067, "Y": 0.32233643531799316}, {"X": 0.4745444655418396, "Y": 0.3223007321357727}, {"X": 0.4745553731918335, "Y": 0.33220937848091125}, {"X": 0.4422127902507782, "Y": 0.3322450518608093}]}, "Id": "bcdcd69a-c348-4292-9ee1-4ef430c68b94"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02963687852025032, "Height": 0.012338628992438316, "Left": 0.5341169238090515, "Top": 0.31913167238235474}, "Polygon": [{"X": 0.5341169238090515, "Y": 0.31916436553001404}, {"X": 0.563740074634552, "Y": 0.31913167238235474}, {"X": 0.5637537837028503, "Y": 0.3314376175403595}, {"X": 0.5341305136680603, "Y": 0.3314703106880188}]}, "Id": "4d4c62e6-68f2-4677-a5e7-a18a75f9e125"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Via", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023025184869766235, "Height": 0.01021419744938612, "Left": 0.5682863593101501, "Top": 0.317913681268692}, "Polygon": [{"X": 0.5682863593101501, "Y": 0.3179391026496887}, {"X": 0.5913001894950867, "Y": 0.317913681268692}, {"X": 0.5913115739822388, "Y": 0.3281024992465973}, {"X": 0.5682976841926575, "Y": 0.3281278908252716}]}, "Id": "741f166c-d889-47d2-9374-043a8ab8b9d8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FOB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02991212159395218, "Height": 0.010686157271265984, "Left": 0.6970155239105225, "Top": 0.3188648819923401}, "Polygon": [{"X": 0.6970155239105225, "Y": 0.31889790296554565}, {"X": 0.7269155979156494, "Y": 0.3188648819923401}, {"X": 0.726927638053894, "Y": 0.32951807975769043}, {"X": 0.6970275044441223, "Y": 0.3295510411262512}]}, "Id": "70f71121-0e7a-4529-8a7f-0d32208fe0f2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Project", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04877810552716255, "Height": 0.012779688462615013, "Left": 0.8520395755767822, "Top": 0.320295125246048}, "Polygon": [{"X": 0.8520395755767822, "Y": 0.32034894824028015}, {"X": 0.9008030295372009, "Y": 0.320295125246048}, {"X": 0.9008176326751709, "Y": 0.33302104473114014}, {"X": 0.8520541191101074, "Y": 0.33307480812072754}]}, "Id": "d0cfbcca-f703-4fb1-b870-244279ed624f"}, {"BlockType": "WORD", "Confidence": 99.59243774414062, "Text": "ED083441/25107218", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.130945086479187, "Height": 0.011779462918639183, "Left": 0.2423161417245865, "Top": 0.35456016659736633}, "Polygon": [{"X": 0.2423161417245865, "Y": 0.3547042906284332}, {"X": 0.37324854731559753, "Y": 0.35456016659736633}, {"X": 0.3732612431049347, "Y": 0.3661956489086151}, {"X": 0.24232865869998932, "Y": 0.36633965373039246}]}, "Id": "bfcfbea2-7607-4ff4-b40d-5ea1806a429c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "5/8/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05449913069605827, "Height": 0.010287459939718246, "Left": 0.4136713147163391, "Top": 0.3519383370876312}, "Polygon": [{"X": 0.4136713147163391, "Y": 0.35199832916259766}, {"X": 0.46815916895866394, "Y": 0.3519383370876312}, {"X": 0.4681704342365265, "Y": 0.3621658682823181}, {"X": 0.4136824905872345, "Y": 0.36222580075263977}]}, "Id": "c2cdf042-ffb4-45f8-b5d0-853dcb0a7ed2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.076120525598526, "Height": 0.011910404078662395, "Left": 0.3357919156551361, "Top": 0.3829190731048584}, "Polygon": [{"X": 0.3357919156551361, "Y": 0.3830026686191559}, {"X": 0.41189950704574585, "Y": 0.3829190731048584}, {"X": 0.4119124412536621, "Y": 0.39474600553512573}, {"X": 0.3358047604560852, "Y": 0.39482948184013367}]}, "Id": "d8e55374-0ad6-4489-b88b-da2237d676e1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056103724986314774, "Height": 0.013063081540167332, "Left": 0.6132440567016602, "Top": 0.3784119784832001}, "Polygon": [{"X": 0.6132440567016602, "Y": 0.37847357988357544}, {"X": 0.6693332195281982, "Y": 0.3784119784832001}, {"X": 0.6693478226661682, "Y": 0.39141350984573364}, {"X": 0.6132586002349854, "Y": 0.39147505164146423}]}, "Id": "6b47b3fb-b356-42b8-961c-95eb3f4602f2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ordered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05541332811117172, "Height": 0.010248161852359772, "Left": 0.7556688189506531, "Top": 0.38107600808143616}, "Polygon": [{"X": 0.7556688189506531, "Y": 0.38113686442375183}, {"X": 0.8110705614089966, "Y": 0.38107600808143616}, {"X": 0.811082124710083, "Y": 0.3912633955478668}, {"X": 0.7556803226470947, "Y": 0.3913241922855377}]}, "Id": "2590f3c3-e18a-40d6-b28e-da671a176902"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "U/M", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028358038514852524, "Height": 0.010318269021809101, "Left": 0.8911812901496887, "Top": 0.3806443512439728}, "Polygon": [{"X": 0.8911812901496887, "Y": 0.3806754946708679}, {"X": 0.9195274710655212, "Y": 0.3806443512439728}, {"X": 0.9195392727851868, "Y": 0.39093151688575745}, {"X": 0.8911930918693542, "Y": 0.3909626305103302}]}, "Id": "4dfc1794-7f5c-4254-bd65-c2337585fd65"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028371093794703484, "Height": 0.009529687464237213, "Left": 0.09963473677635193, "Top": 0.388615220785141}, "Polygon": [{"X": 0.09963473677635193, "Y": 0.38864636421203613}, {"X": 0.12799572944641113, "Y": 0.388615220785141}, {"X": 0.12800583243370056, "Y": 0.39811381697654724}, {"X": 0.09964480251073837, "Y": 0.39814493060112}]}, "Id": "1250fe89-9b5f-4b3c-a34c-a62a50b9081f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "352750", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044132642447948456, "Height": 0.0101799750700593, "Left": 0.04988868907094002, "Top": 0.413134902715683}, "Polygon": [{"X": 0.04988868907094002, "Y": 0.4131832420825958}, {"X": 0.0940105989575386, "Y": 0.413134902715683}, {"X": 0.09402132779359818, "Y": 0.42326658964157104}, {"X": 0.04989936947822571, "Y": 0.4233148694038391}]}, "Id": "60ef6cc8-19ef-4783-8390-23a8944ec81e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "BARBCO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06082434207201004, "Height": 0.010317707434296608, "Left": 0.1876475065946579, "Top": 0.4093354642391205}, "Polygon": [{"X": 0.1876475065946579, "Y": 0.40940210223197937}, {"X": 0.2484608143568039, "Y": 0.4093354642391205}, {"X": 0.24847185611724854, "Y": 0.4195865988731384}, {"X": 0.18765847384929657, "Y": 0.41965317726135254}]}, "Id": "10b502a9-7877-444c-8e0d-e50bc2de1c6c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "TORTILLA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0738556757569313, "Height": 0.009685322642326355, "Left": 0.25228291749954224, "Top": 0.40850940346717834}, "Polygon": [{"X": 0.25228291749954224, "Y": 0.40859031677246094}, {"X": 0.32612818479537964, "Y": 0.40850940346717834}, {"X": 0.32613858580589294, "Y": 0.4181138575077057}, {"X": 0.25229325890541077, "Y": 0.4181947112083435}]}, "Id": "1ae6fcae-e15a-41a8-a24f-224ec82c3140"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "CHIPS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04346350207924843, "Height": 0.010183707810938358, "Left": 0.3298986852169037, "Top": 0.40738698840141296}, "Polygon": [{"X": 0.3298986852169037, "Y": 0.4074346125125885}, {"X": 0.373351126909256, "Y": 0.40738698840141296}, {"X": 0.3733621835708618, "Y": 0.4175231456756592}, {"X": 0.32990968227386475, "Y": 0.41757071018218994}]}, "Id": "f9499429-31a2-4308-b66c-7f33d9c1fe99"}, {"BlockType": "WORD", "Confidence": 98.4875259399414, "Text": "12/16OZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054445646703243256, "Height": 0.010396960191428661, "Left": 0.3787694275379181, "Top": 0.40613603591918945}, "Polygon": [{"X": 0.3787694275379181, "Y": 0.4061957001686096}, {"X": 0.43320372700691223, "Y": 0.40613603591918945}, {"X": 0.43321505188941956, "Y": 0.416473388671875}, {"X": 0.37878069281578064, "Y": 0.4165330231189728}]}, "Id": "f72eca05-2f24-4cb0-8476-ac67897b0e5b"}, {"BlockType": "WORD", "Confidence": 99.28890228271484, "Text": "48/CS/PALLET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10021147131919861, "Height": 0.01048698928207159, "Left": 0.4370897114276886, "Top": 0.40530407428741455}, "Polygon": [{"X": 0.4370897114276886, "Y": 0.4054138958454132}, {"X": 0.5372896790504456, "Y": 0.40530407428741455}, {"X": 0.5373011827468872, "Y": 0.4156813323497772}, {"X": 0.4371010959148407, "Y": 0.4157910645008087}]}, "Id": "44971ec6-2df8-4b65-bd96-f56ab3840e30"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "1,440", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033815279603004456, "Height": 0.010908486321568489, "Left": 0.8168557286262512, "Top": 0.4065384566783905}, "Polygon": [{"X": 0.8168557286262512, "Y": 0.4065755009651184}, {"X": 0.8506585359573364, "Y": 0.4065384566783905}, {"X": 0.8506709933280945, "Y": 0.4174099266529083}, {"X": 0.8168680667877197, "Y": 0.41744694113731384}]}, "Id": "3603a28e-c5dc-4c41-855a-3df8d501b325"}, {"BlockType": "WORD", "Confidence": 99.03359985351562, "Text": "ED083441/25107218", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1305546909570694, "Height": 0.01180802658200264, "Left": 0.18786683678627014, "Top": 0.449667751789093}, "Polygon": [{"X": 0.18786683678627014, "Y": 0.44981029629707336}, {"X": 0.31840887665748596, "Y": 0.449667751789093}, {"X": 0.31842151284217834, "Y": 0.46133336424827576}, {"X": 0.1878793090581894, "Y": 0.46147575974464417}]}, "Id": "89e11793-48ae-481b-9c3d-8a4cc850946c"}, {"BlockType": "WORD", "Confidence": 45.151268005371094, "Text": "Means", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16072411835193634, "Height": 0.06985219568014145, "Left": 0.7801271677017212, "Top": 0.430331289768219}, "Polygon": [{"X": 0.7801271677017212, "Y": 0.43050697445869446}, {"X": 0.9407709240913391, "Y": 0.430331289768219}, {"X": 0.9408512711524963, "Y": 0.5000088214874268}, {"X": 0.7802062630653381, "Y": 0.5001834630966187}]}, "Id": "a7ea371d-f8a4-4df3-82a5-5e3f76c7fc51"}, {"BlockType": "WORD", "Confidence": 46.332706451416016, "Text": "52/3/9", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1523926556110382, "Height": 0.05980904772877693, "Left": 0.7873420119285583, "Top": 0.48763224482536316}, "Polygon": [{"X": 0.9397346377372742, "Y": 0.5472763776779175}, {"X": 0.787409782409668, "Y": 0.547441303730011}, {"X": 0.7873420119285583, "Y": 0.4877980351448059}, {"X": 0.9396659135818481, "Y": 0.48763224482536316}]}, "Id": "6dca9582-27e7-4236-b727-2eb0cd5ebe1a"}, {"BlockType": "WORD", "Confidence": 87.32402038574219, "Text": "TRAiler", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09781334549188614, "Height": 0.04799335449934006, "Left": 0.2695613205432892, "Top": 0.7697678208351135}, "Polygon": [{"X": 0.2695613205432892, "Y": 0.7698716521263123}, {"X": 0.36732247471809387, "Y": 0.7697678208351135}, {"X": 0.3673746585845947, "Y": 0.8176577687263489}, {"X": 0.26961296796798706, "Y": 0.8177611827850342}]}, "Id": "b96f7179-9ff1-45fa-9652-b0aa38edd5e4"}, {"BlockType": "WORD", "Confidence": 98.1495132446289, "Text": "#", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.039104003459215164, "Height": 0.025221943855285645, "Left": 0.374286413192749, "Top": 0.7609838247299194}, "Polygon": [{"X": 0.374286413192749, "Y": 0.7610253691673279}, {"X": 0.41336286067962646, "Y": 0.7609838247299194}, {"X": 0.4133903980255127, "Y": 0.7861643433570862}, {"X": 0.3743138611316681, "Y": 0.7862057685852051}]}, "Id": "037d101f-6659-4b83-8aab-2a8259139ab4"}, {"BlockType": "WORD", "Confidence": 76.75083923339844, "Text": "2642", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10199680924415588, "Height": 0.05181959643959999, "Left": 0.412358820438385, "Top": 0.7261245846748352}, "Polygon": [{"X": 0.412358820438385, "Y": 0.7262333035469055}, {"X": 0.5142984390258789, "Y": 0.7261245846748352}, {"X": 0.5143555998802185, "Y": 0.7778359651565552}, {"X": 0.41241541504859924, "Y": 0.7779442071914673}]}, "Id": "d954590a-bb93-4bbb-8f26-f8e1dac4b671"}, {"BlockType": "WORD", "Confidence": 82.13468933105469, "Text": "seal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.064420685172081, "Height": 0.02772608771920204, "Left": 0.30429914593696594, "Top": 0.8188759684562683}, "Polygon": [{"X": 0.30429914593696594, "Y": 0.8189440369606018}, {"X": 0.3686896860599518, "Y": 0.8188759684562683}, {"X": 0.36871981620788574, "Y": 0.8465341329574585}, {"X": 0.3043290674686432, "Y": 0.8466020226478577}]}, "Id": "bfde324a-98f8-4591-a365-33b8dc19e38f"}, {"BlockType": "WORD", "Confidence": 95.576171875, "Text": "#", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.028844939544796944, "Height": 0.02514839544892311, "Left": 0.40002715587615967, "Top": 0.8131113648414612}, "Polygon": [{"X": 0.40002715587615967, "Y": 0.8131418228149414}, {"X": 0.4288445711135864, "Y": 0.8131113648414612}, {"X": 0.42887207865715027, "Y": 0.8382292985916138}, {"X": 0.40005460381507874, "Y": 0.838259756565094}]}, "Id": "28c4dc76-3a3a-4f28-b850-36131b7d92fc"}, {"BlockType": "WORD", "Confidence": 85.73819732666016, "Text": "E21784888", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17692720890045166, "Height": 0.06806433945894241, "Left": 0.41668441891670227, "Top": 0.7671695947647095}, "Polygon": [{"X": 0.41668441891670227, "Y": 0.7673575282096863}, {"X": 0.5935359597206116, "Y": 0.7671695947647095}, {"X": 0.5936116576194763, "Y": 0.83504718542099}, {"X": 0.4167587459087372, "Y": 0.8352339863777161}]}, "Id": "c71748b7-d555-4a31-9a71-31aa43f664dc"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "455177fb-e966-45cd-a818-b430273fee4d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "455177fb-e966-45cd-a818-b430273fee4d", "content-type": "application/x-amz-json-1.1", "content-length": "46945", "date": "Thu, 18 Sep 2025 08:50:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "661793a0-3fbf-458a-888c-efcca804d6b2", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:50:41 GMT", "content-type": "application/json", "content-length": "1131", "connection": "keep-alive", "x-amzn-requestid": "661793a0-3fbf-458a-888c-efcca804d6b2"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page that says \"Packing Slip\". This is likely a \"pack_list\" because \"Packing Slip\" is a packing list. The keywords: \"Packing Slip\" is a header. The doc_type is pack_list. So we return classification for page 1 as pack_list.\n\nWe need to use the tool classify_logistics_doc_type with array of objects containing page_no and doc_type. Since there's only one page, we produce that.\n\nWe must use exactly one object describing that page. The format: { \"documents\": [ {page_no:1, doc_type:\"pack_list\"} ] }? Wait: The function signature expects a JSON with \"documents\" array. So we should call the function with { documents: [ { page_no:1, doc_type:\"pack_list\"} ] }.\n\nWe should not output any extra text."}}}, {"toolUse": {"toolUseId": "tooluse_YggOfTR2Qgm_9kjrIYQ_Jw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2281, "outputTokens": 209, "totalTokens": 2490}, "metrics": {"latencyMs": 993}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/79cc93f2_13_pack_list_8.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nLa Fama Foods, Inc.\nPacking Slip\nDate\nS.O. No.\n7566 US Hwy 259 North\n5/7/2025\n9447\nShip To\nCOMMISSARY WAREHOUSE\n1621 HIGHWAY 75 NORTH\nHUNTSVILLE, TX 77340\nP.O. No.\nShip Date\nShip Via\nFOB\nProject\nED083441/25107218\n5/8/2025\nDescription\nShipped\nOrdered\nU/M\nItem\n352750\nBARBCO TORTILLA CHIPS 12/16OZ 48/CS/PALLET\n1,440\nED083441/25107218\nMeans\n52/3/9\nTRAiler # 2642\nseal # E21784888\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}