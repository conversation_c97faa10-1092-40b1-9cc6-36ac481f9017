2025-09-24 00:18:19,784 - INFO - Logging initialized. Log file: logs/test_classification_20250924_001819.log
2025-09-24 00:18:19,785 - INFO - 📁 Found 8 files to process
2025-09-24 00:18:19,785 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:18:19,785 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 00:18:19,785 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 00:18:19,785 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 00:18:19,786 - INFO - ⬆️ [00:18:19] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:21,221 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/819a4ecb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:21,222 - INFO - 🔍 [00:18:21] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:21,224 - INFO - ⬆️ [00:18:21] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:21,230 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:21,266 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:21,273 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/819a4ecb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:21,274 - INFO - Processing PDF from S3...
2025-09-24 00:18:21,274 - INFO - Downloading PDF from S3 to /tmp/tmpa7dvb20v/819a4ecb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:22,750 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:18:22,750 - INFO - Splitting PDF into individual pages...
2025-09-24 00:18:22,754 - INFO - Splitting PDF 819a4ecb_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 00:18:22,769 - INFO - Split PDF into 1 pages
2025-09-24 00:18:22,769 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:18:22,769 - INFO - Expected pages: [1]
2025-09-24 00:18:25,883 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/2f400393_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:25,883 - INFO - 🔍 [00:18:25] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:25,884 - INFO - ⬆️ [00:18:25] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:25,886 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:25,938 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:25,944 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f400393_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:25,945 - INFO - Processing image from S3...
2025-09-24 00:18:26,799 - INFO - Page 1: Extracted 1260 characters, 84 lines from 819a4ecb_BQJUG5URFR2GH9ECWFV4_6b274a1d_page_001.pdf
2025-09-24 00:18:26,799 - INFO - Successfully processed page 1
2025-09-24 00:18:26,800 - INFO - Combined 1 pages into final text
2025-09-24 00:18:26,800 - INFO - Text validation for 819a4ecb_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 00:18:26,801 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:26,801 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:26,857 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/051b4a32_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:26,858 - INFO - 🔍 [00:18:26] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:26,860 - INFO - ⬆️ [00:18:26] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:26,863 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:26,908 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:26,916 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/051b4a32_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:26,917 - INFO - Processing PDF from S3...
2025-09-24 00:18:26,917 - INFO - Downloading PDF from S3 to /tmp/tmpm9zt5nem/051b4a32_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:27,521 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/ad63c3a0_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:27,522 - INFO - 🔍 [00:18:27] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:27,525 - INFO - ⬆️ [00:18:27] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:27,526 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:27,576 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:27,583 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ad63c3a0_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:27,584 - INFO - Processing PDF from S3...
2025-09-24 00:18:27,584 - INFO - Downloading PDF from S3 to /tmp/tmpexb_xf7z/ad63c3a0_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:28,804 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/819a4ecb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:29,220 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:18:29,221 - INFO - Splitting PDF into individual pages...
2025-09-24 00:18:29,223 - INFO - Splitting PDF 051b4a32_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 00:18:29,234 - INFO - Split PDF into 1 pages
2025-09-24 00:18:29,234 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:18:29,234 - INFO - Expected pages: [1]
2025-09-24 00:18:29,444 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:18:29,445 - INFO - Splitting PDF into individual pages...
2025-09-24 00:18:29,450 - INFO - Splitting PDF ad63c3a0_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 00:18:29,472 - INFO - Split PDF into 7 pages
2025-09-24 00:18:29,472 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:18:29,472 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 00:18:30,271 - INFO - S3 Image temp/2f400393_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 00:18:30,271 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:30,271 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:32,207 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/e7ff2cf9_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:32,208 - INFO - 🔍 [00:18:32] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:32,209 - INFO - ⬆️ [00:18:32] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:32,211 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:32,245 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:32,251 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e7ff2cf9_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:32,251 - INFO - Processing image from S3...
2025-09-24 00:18:33,013 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2f400393_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:33,649 - INFO - Page 2: Extracted 1821 characters, 105 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_002.pdf
2025-09-24 00:18:33,649 - INFO - Successfully processed page 2
2025-09-24 00:18:33,879 - INFO - Page 1: Extracted 1731 characters, 110 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_001.pdf
2025-09-24 00:18:33,880 - INFO - Successfully processed page 1
2025-09-24 00:18:34,242 - INFO - Page 5: Extracted 2059 characters, 131 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_005.pdf
2025-09-24 00:18:34,249 - INFO - Page 1: Extracted 519 characters, 34 lines from 051b4a32_KE7TCH9TPQZFVA5CZ3HT_db0c79fc_page_001.pdf
2025-09-24 00:18:34,249 - INFO - Successfully processed page 5
2025-09-24 00:18:34,249 - INFO - Successfully processed page 1
2025-09-24 00:18:34,249 - INFO - Combined 1 pages into final text
2025-09-24 00:18:34,250 - INFO - Text validation for 051b4a32_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 00:18:34,250 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:34,250 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:34,306 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/fb58eae5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:34,306 - INFO - 🔍 [00:18:34] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:34,309 - INFO - ⬆️ [00:18:34] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:34,313 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:34,368 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:34,373 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fb58eae5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:34,373 - INFO - Processing image from S3...
2025-09-24 00:18:34,500 - INFO - Page 6: Extracted 1973 characters, 129 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_006.pdf
2025-09-24 00:18:34,501 - INFO - Successfully processed page 6
2025-09-24 00:18:34,956 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/09e5bbd4_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:34,957 - INFO - 🔍 [00:18:34] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:34,959 - INFO - ⬆️ [00:18:34] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:34,960 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:35,000 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:35,006 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/09e5bbd4_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:35,007 - INFO - Processing image from S3...
2025-09-24 00:18:36,018 - INFO - Page 3: Extracted 2265 characters, 147 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_003.pdf
2025-09-24 00:18:36,018 - INFO - Successfully processed page 3
2025-09-24 00:18:36,251 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/ecd34d71_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:36,252 - INFO - 🔍 [00:18:36] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:36,255 - INFO - Initializing TextractProcessor...
2025-09-24 00:18:36,317 - INFO - Initializing BedrockProcessor...
2025-09-24 00:18:36,339 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ecd34d71_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:36,347 - INFO - Processing image from S3...
2025-09-24 00:18:36,368 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/051b4a32_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:36,788 - INFO - Page 7: Extracted 417 characters, 27 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_007.pdf
2025-09-24 00:18:36,795 - INFO - Successfully processed page 7
2025-09-24 00:18:36,817 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:36,817 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:18:37,141 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/819a4ecb_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:37,217 - INFO - S3 Image temp/09e5bbd4_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 00:18:37,225 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:37,231 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:37,318 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:37,319 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:18:37,620 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f400393_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:37,809 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:37,809 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:18:38,110 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/051b4a32_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:38,288 - INFO - Page 4: Extracted 2242 characters, 148 lines from ad63c3a0_O2IU5G77LYNTYE0RP1TI_1bcc4d2d_page_004.pdf
2025-09-24 00:18:38,288 - INFO - Successfully processed page 4
2025-09-24 00:18:38,289 - INFO - Combined 7 pages into final text
2025-09-24 00:18:38,290 - INFO - Text validation for ad63c3a0_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 00:18:38,290 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:38,291 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:38,653 - INFO - S3 Image temp/ecd34d71_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 00:18:38,653 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:38,653 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:39,137 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/09e5bbd4_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:39,165 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:39,165 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:18:39,428 - INFO - S3 Image temp/fb58eae5_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 00:18:39,428 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:39,429 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:39,479 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/09e5bbd4_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:40,899 - INFO - S3 Image temp/e7ff2cf9_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 00:18:40,899 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:18:40,899 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:18:40,993 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ad63c3a0_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:41,959 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ecd34d71_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:43,139 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e7ff2cf9_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:43,733 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fb58eae5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:44,756 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:44,756 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:45,070 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ad63c3a0_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:45,097 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:45,098 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:18:45,503 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ecd34d71_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:46,711 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:46,711 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:18:47,010 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e7ff2cf9_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:47,314 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:18:47,314 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:18:47,640 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fb58eae5_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:47,641 - INFO - 
📊 Processing Summary:
2025-09-24 00:18:47,642 - INFO -    Total files: 8
2025-09-24 00:18:47,642 - INFO -    Successful: 8
2025-09-24 00:18:47,642 - INFO -    Failed: 0
2025-09-24 00:18:47,642 - INFO -    Duration: 27.86 seconds
2025-09-24 00:18:47,642 - INFO -    Output directory: output
2025-09-24 00:18:47,642 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:18:47,643 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,643 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,643 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,643 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-24 00:18:47,644 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,644 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,644 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,644 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:18:47,645 - INFO - 
============================================================================================================================================
2025-09-24 00:18:47,645 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:18:47,645 - INFO - ============================================================================================================================================
2025-09-24 00:18:47,646 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:18:47,646 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:18:47,646 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 00:18:47,646 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:18:47,646 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:18:47,646 - INFO - 
2025-09-24 00:18:47,646 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 00:18:47,647 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:18:47,647 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:18:47,647 - INFO - 
2025-09-24 00:18:47,647 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-24 00:18:47,647 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:18:47,647 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:18:47,647 - INFO - 
2025-09-24 00:18:47,647 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,647 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,648 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,648 - INFO - 
2025-09-24 00:18:47,648 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,648 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,648 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,648 - INFO - 
2025-09-24 00:18:47,648 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,648 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,648 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,648 - INFO - 
2025-09-24 00:18:47,648 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,649 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,649 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,649 - INFO - 
2025-09-24 00:18:47,649 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,649 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,649 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,649 - INFO - 
2025-09-24 00:18:47,649 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,649 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,649 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,649 - INFO - 
2025-09-24 00:18:47,650 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:18:47,650 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:18:47,650 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,650 - INFO - 
2025-09-24 00:18:47,650 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 00:18:47,650 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:18:47,650 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:18:47,650 - INFO - 
2025-09-24 00:18:47,650 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 00:18:47,650 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:18:47,650 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:18:47,651 - INFO - 
2025-09-24 00:18:47,651 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 00:18:47,651 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:18:47,651 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:18:47,651 - INFO - 
2025-09-24 00:18:47,651 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      log                  run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 00:18:47,651 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:18:47,651 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:18:47,651 - INFO - 
2025-09-24 00:18:47,651 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:18:47,652 - INFO - Total entries: 14
2025-09-24 00:18:47,652 - INFO - ============================================================================================================================================
2025-09-24 00:18:47,652 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:18:47,652 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:18:47,652 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:18:47,652 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:18:47,652 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:18:47,652 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,652 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,652 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,652 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,653 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,653 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,653 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:18:47,653 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:18:47,653 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:18:47,653 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:18:47,653 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → log             | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:18:47,653 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:20:38,348 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 27.855636, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
