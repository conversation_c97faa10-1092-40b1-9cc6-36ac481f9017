2025-09-24 00:21:08,262 - INFO - Logging initialized. Log file: logs/test_classification_20250924_002108.log
2025-09-24 00:21:08,263 - INFO - 📁 Found 8 files to process
2025-09-24 00:21:08,263 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:21:08,263 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 00:21:08,264 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 00:21:08,264 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 00:21:08,264 - INFO - ⬆️ [00:21:08] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:09,994 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/882d1380_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:09,995 - INFO - 🔍 [00:21:09] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:09,996 - INFO - ⬆️ [00:21:09] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:09,998 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:10,036 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:10,044 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/882d1380_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:10,044 - INFO - Processing PDF from S3...
2025-09-24 00:21:10,044 - INFO - Downloading PDF from S3 to /tmp/tmpapzz4so2/882d1380_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:11,411 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:21:11,412 - INFO - Splitting PDF into individual pages...
2025-09-24 00:21:11,416 - INFO - Splitting PDF 882d1380_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 00:21:11,427 - INFO - Split PDF into 1 pages
2025-09-24 00:21:11,427 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:21:11,427 - INFO - Expected pages: [1]
2025-09-24 00:21:15,401 - INFO - Page 1: Extracted 1260 characters, 84 lines from 882d1380_BQJUG5URFR2GH9ECWFV4_e00041b5_page_001.pdf
2025-09-24 00:21:15,402 - INFO - Successfully processed page 1
2025-09-24 00:21:15,402 - INFO - Combined 1 pages into final text
2025-09-24 00:21:15,403 - INFO - Text validation for 882d1380_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 00:21:15,403 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:15,403 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:16,008 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/ba2f5554_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:16,009 - INFO - 🔍 [00:21:16] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:16,011 - INFO - ⬆️ [00:21:16] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:16,012 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:16,038 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:16,047 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba2f5554_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:16,053 - INFO - Processing image from S3...
2025-09-24 00:21:17,031 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/91406166_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:17,031 - INFO - 🔍 [00:21:17] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:17,033 - INFO - ⬆️ [00:21:17] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:17,034 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:17,081 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:17,086 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/91406166_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:17,087 - INFO - Processing PDF from S3...
2025-09-24 00:21:17,087 - INFO - Downloading PDF from S3 to /tmp/tmp2gq8gd1x/91406166_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:17,340 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/882d1380_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:17,793 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/42280a5d_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:17,794 - INFO - 🔍 [00:21:17] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:17,797 - INFO - ⬆️ [00:21:17] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:17,800 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:17,843 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:17,849 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/42280a5d_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:17,849 - INFO - Processing PDF from S3...
2025-09-24 00:21:17,849 - INFO - Downloading PDF from S3 to /tmp/tmpdui8u8d5/42280a5d_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:19,221 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:21:19,222 - INFO - Splitting PDF into individual pages...
2025-09-24 00:21:19,226 - INFO - Splitting PDF 91406166_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 00:21:19,237 - INFO - Split PDF into 1 pages
2025-09-24 00:21:19,238 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:21:19,238 - INFO - Expected pages: [1]
2025-09-24 00:21:19,660 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:21:19,660 - INFO - Splitting PDF into individual pages...
2025-09-24 00:21:19,666 - INFO - Splitting PDF 42280a5d_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 00:21:19,695 - INFO - Split PDF into 7 pages
2025-09-24 00:21:19,696 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:21:19,696 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 00:21:20,685 - INFO - S3 Image temp/ba2f5554_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 00:21:20,686 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:20,687 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:22,357 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/bf489a57_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:22,357 - INFO - 🔍 [00:21:22] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:22,359 - INFO - ⬆️ [00:21:22] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:22,360 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:22,394 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:22,408 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf489a57_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:22,409 - INFO - Processing image from S3...
2025-09-24 00:21:22,752 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ba2f5554_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:23,765 - INFO - Page 1: Extracted 519 characters, 34 lines from 91406166_KE7TCH9TPQZFVA5CZ3HT_cc183595_page_001.pdf
2025-09-24 00:21:23,765 - INFO - Successfully processed page 1
2025-09-24 00:21:23,766 - INFO - Combined 1 pages into final text
2025-09-24 00:21:23,766 - INFO - Text validation for 91406166_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 00:21:23,766 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:23,767 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:24,096 - INFO - Page 2: Extracted 1821 characters, 105 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_002.pdf
2025-09-24 00:21:24,097 - INFO - Successfully processed page 2
2025-09-24 00:21:24,215 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/36f9a269_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:24,215 - INFO - 🔍 [00:21:24] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:24,217 - INFO - ⬆️ [00:21:24] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:24,220 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:24,268 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:24,285 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36f9a269_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:24,286 - INFO - Processing image from S3...
2025-09-24 00:21:24,324 - INFO - Page 4: Extracted 2242 characters, 148 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_004.pdf
2025-09-24 00:21:24,324 - INFO - Successfully processed page 4
2025-09-24 00:21:24,571 - INFO - Page 5: Extracted 2059 characters, 131 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_005.pdf
2025-09-24 00:21:24,572 - INFO - Successfully processed page 5
2025-09-24 00:21:24,734 - INFO - Page 1: Extracted 1731 characters, 110 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_001.pdf
2025-09-24 00:21:24,735 - INFO - Successfully processed page 1
2025-09-24 00:21:24,929 - INFO - Page 3: Extracted 2265 characters, 147 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_003.pdf
2025-09-24 00:21:24,929 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/ff34c395_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:24,929 - INFO - 🔍 [00:21:24] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:24,930 - INFO - Successfully processed page 3
2025-09-24 00:21:24,930 - INFO - ⬆️ [00:21:24] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:24,931 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:24,946 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:24,956 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ff34c395_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:24,957 - INFO - Processing image from S3...
2025-09-24 00:21:25,347 - INFO - Page 6: Extracted 1973 characters, 129 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_006.pdf
2025-09-24 00:21:25,347 - INFO - Successfully processed page 6
2025-09-24 00:21:25,941 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/2ab93517_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:25,942 - INFO - 🔍 [00:21:25] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:25,945 - INFO - Initializing TextractProcessor...
2025-09-24 00:21:25,999 - INFO - Initializing BedrockProcessor...
2025-09-24 00:21:26,020 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2ab93517_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:26,030 - INFO - Processing image from S3...
2025-09-24 00:21:26,463 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:26,463 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:21:26,671 - INFO - Page 7: Extracted 417 characters, 27 lines from 42280a5d_O2IU5G77LYNTYE0RP1TI_8e639d24_page_007.pdf
2025-09-24 00:21:26,673 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/91406166_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:26,674 - INFO - Successfully processed page 7
2025-09-24 00:21:26,678 - INFO - Combined 7 pages into final text
2025-09-24 00:21:26,680 - INFO - Text validation for 42280a5d_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 00:21:26,680 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:26,681 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:26,722 - INFO - S3 Image temp/ff34c395_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 00:21:26,723 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:26,723 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:26,755 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/882d1380_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:26,918 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:26,918 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:21:27,209 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba2f5554_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:27,396 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:27,396 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:21:27,691 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/91406166_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:27,832 - INFO - S3 Image temp/bf489a57_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 00:21:27,832 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:27,832 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:28,106 - INFO - S3 Image temp/2ab93517_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 00:21:28,108 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:28,108 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:28,424 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ff34c395_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:28,451 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:28,451 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:21:28,656 - INFO - S3 Image temp/36f9a269_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 00:21:28,656 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:21:28,656 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:21:28,772 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ff34c395_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:29,730 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf489a57_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:29,823 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/42280a5d_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:30,170 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2ab93517_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:30,991 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36f9a269_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:30,996 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:30,996 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:21:31,367 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf489a57_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:35,044 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:35,044 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:35,358 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/42280a5d_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:35,387 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:35,387 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:21:35,800 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2ab93517_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:36,060 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 00:21:36,060 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:21:36,386 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36f9a269_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:36,387 - INFO - 
📊 Processing Summary:
2025-09-24 00:21:36,388 - INFO -    Total files: 8
2025-09-24 00:21:36,388 - INFO -    Successful: 8
2025-09-24 00:21:36,388 - INFO -    Failed: 0
2025-09-24 00:21:36,388 - INFO -    Duration: 28.12 seconds
2025-09-24 00:21:36,388 - INFO -    Output directory: output
2025-09-24 00:21:36,388 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:21:36,388 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,389 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 00:21:36,390 - INFO - 
============================================================================================================================================
2025-09-24 00:21:36,391 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:21:36,391 - INFO - ============================================================================================================================================
2025-09-24 00:21:36,391 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:21:36,391 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:21:36,391 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 00:21:36,391 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 00:21:36,391 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:21:36,391 - INFO - 
2025-09-24 00:21:36,391 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 00:21:36,392 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 00:21:36,392 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:21:36,392 - INFO - 
2025-09-24 00:21:36,392 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      log                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-24 00:21:36,392 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 00:21:36,392 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:21:36,392 - INFO - 
2025-09-24 00:21:36,392 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,393 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,393 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,393 - INFO - 
2025-09-24 00:21:36,393 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,393 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,393 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,393 - INFO - 
2025-09-24 00:21:36,393 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,393 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,393 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,394 - INFO - 
2025-09-24 00:21:36,394 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,394 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,394 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,394 - INFO - 
2025-09-24 00:21:36,394 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,394 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,394 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,394 - INFO - 
2025-09-24 00:21:36,394 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,394 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,395 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,395 - INFO - 
2025-09-24 00:21:36,395 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-24 00:21:36,395 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 00:21:36,395 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,395 - INFO - 
2025-09-24 00:21:36,395 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 00:21:36,395 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 00:21:36,395 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:21:36,395 - INFO - 
2025-09-24 00:21:36,395 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 00:21:36,395 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 00:21:36,396 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:21:36,396 - INFO - 
2025-09-24 00:21:36,396 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 00:21:36,396 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 00:21:36,396 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:21:36,396 - INFO - 
2025-09-24 00:21:36,396 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      log                  run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 00:21:36,396 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 00:21:36,396 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:21:36,396 - INFO - 
2025-09-24 00:21:36,396 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:21:36,396 - INFO - Total entries: 14
2025-09-24 00:21:36,397 - INFO - ============================================================================================================================================
2025-09-24 00:21:36,397 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:21:36,397 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:21:36,397 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 00:21:36,397 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 00:21:36,397 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → log             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-24 00:21:36,397 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,397 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,397 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,397 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,398 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,398 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,398 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-24 00:21:36,398 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 00:21:36,398 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 00:21:36,398 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 00:21:36,398 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → log             | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 00:21:36,398 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:21:36,398 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 28.123776, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
