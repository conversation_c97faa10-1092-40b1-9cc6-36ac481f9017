{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 6.21251743382345e-08, "Y": 1.3097367457248765e-07}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "25b3a096-4635-43fb-842f-77ffa5957b5c", "Relationships": [{"Type": "CHILD", "Ids": ["2a1dd5cf-5085-4f4d-b52c-ef1730d0be58", "071adf60-ece8-4a8b-92e4-32dd25a528bf", "a7e5e42f-7bc5-46cf-b932-cdaea0e4df65", "84523329-6300-4352-aed0-04b64545b7c1", "3a7e7b27-8520-41ca-b1ff-8dfc9b8f259e", "be7baeb3-4fe6-4594-bf81-174fde632137", "5da9f147-6a8b-4cdf-bdd2-b4968b3b4532", "830125ef-0368-4844-ac84-7a29021b54ab", "c4a4721e-4e04-4f03-b9d5-3896160c015b", "d8bfce04-df0e-45be-ac00-8380d4b7ee37", "935b3cd3-d67b-4975-a18b-ecfc30395384", "35afcbbd-8514-4476-b65f-67b20b71e8b5", "9b316c79-2f84-451b-8bae-f4d6b1213f7c", "ce2e1496-2d29-4c36-8665-5134311d77ea", "7c7e0a2b-a9b7-47a8-84ba-454d4a3cfb2a", "a7073d5c-ba5a-4211-b047-b98ebed9d0f3", "a6439f1c-8576-414e-862e-3cf90af8db78", "67c7ba3b-55af-4d2a-bb5c-aab82ad656f4", "b62bea9a-7aee-41d4-ae66-b3d67c9186ed", "6352e4a7-1348-4e2e-9322-dacf29ea69a8", "602316d2-2e20-4303-99f6-2b1bcb558ee7", "eb60328a-8a46-40a5-a66c-0078b219029a", "9732ae21-2b82-4088-b9e9-37195fee774e", "f5967840-1279-44ef-8b80-bf4fd522508f", "e4456382-87cc-417b-bb9d-57ff17d92cc2", "732e157f-ba54-4cca-b8d9-446bb01811d5", "6b271ff8-ae90-4f41-8c92-c1100a1f8216", "68ee6c39-2575-49a0-aa12-12080c2e6c29", "9a0b5632-9450-42df-8051-d6359145e89b", "ac17d78d-913a-41a3-8b18-bc45016721c4", "9efc46c4-5ed7-449e-a61a-693a7188f663", "38b94c1f-a25b-4a8d-b9c0-74becc584a05", "301773d8-9d1f-47fb-a394-2744e58f3513", "0bd1a80a-4bb3-4ca6-b382-81ac8b97b8a5", "125efda1-508f-4856-8f74-4c9fcf94f9d1", "fbb291f5-0ec2-4c4f-b669-783539ca3f29", "5952386a-6c19-4fc1-b212-e650582d431d", "63d5e4c4-459a-44c9-8089-77f72563d647", "9903d8e0-1c42-40da-86a9-489d182f511f", "64f0f5fd-fda3-42c8-b327-aee732d6bb0a", "edf35bb1-2d4b-485e-b718-759f6f3b2cdf", "3ed02998-ff51-44c3-a8bb-a1c02b8d79d1", "c22f0b1c-55e9-4da7-a5ce-f3f06f015e5e", "0ec88894-7cfa-454a-9c29-874b41c153d3", "88feb2b1-1f87-4e32-91da-30290a2da82f", "162a6661-6d2a-4ac3-8f00-598d8d4a9b2f", "33fd6998-b10e-4503-ba30-d1c1a35d9fb4", "cfc8dae0-b0a5-4a88-ab8b-b0ace02f18b1", "607f5af1-0b91-452c-a16c-fd9a6e180ddd"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Central Transport", "Geometry": {"BoundingBox": {"Width": 0.1148308515548706, "Height": 0.01157828513532877, "Left": 0.05718902871012688, "Top": 0.05624642223119736}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.17201988399028778, "Y": 0.05624642223119736}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.05718902871012688, "Y": 0.06782470643520355}]}, "Id": "2a1dd5cf-5085-4f4d-b52c-ef1730d0be58", "Relationships": [{"Type": "CHILD", "Ids": ["19729f2a-ee28-41df-962d-da77a0b9bd7e", "29748f3a-8ba6-4450-8aed-6f540a6e0888"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "08/31/2025", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "071adf60-ece8-4a8b-92e4-32dd25a528bf", "Relationships": [{"Type": "CHILD", "Ids": ["4666e96f-287e-4b6d-b8e4-d663ddb00677"]}]}, {"BlockType": "LINE", "Confidence": 99.89218139648438, "Text": "PRO#: 227407699", "Geometry": {"BoundingBox": {"Width": 0.12172986567020416, "Height": 0.009408773854374886, "Left": 0.057394713163375854, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07145162671804428}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08085812628269196}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "a7e5e42f-7bc5-46cf-b932-cdaea0e4df65", "Relationships": [{"Type": "CHILD", "Ids": ["04d37094-9420-4f09-ae3f-0478757fe966", "c2230e77-9e6d-4645-8a92-f4326eda6903"]}]}, {"BlockType": "LINE", "Confidence": 99.89476776123047, "Text": "Weight & Inspection", "Geometry": {"BoundingBox": {"Width": 0.13100199401378632, "Height": 0.011712157167494297, "Left": 0.8079518675804138, "Top": 0.07136138528585434}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.9389538764953613, "Y": 0.07136138528585434}, {"X": 0.938953697681427, "Y": 0.08307230472564697}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "84523329-6300-4352-aed0-04b64545b7c1", "Relationships": [{"Type": "CHILD", "Ids": ["499f0269-3d20-41d8-88b8-bdbaba5246c8", "eaabf4d8-edc9-4c33-921c-836c1401ed1e", "1a192e8c-c9a7-44a1-ac59-b34f9b57fe4c"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "CENTRAL TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.16294315457344055, "Height": 0.013988837599754333, "Left": 0.4383789896965027, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19188834726810455}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.4383789896965027, "Y": 0.20587536692619324}]}, "Id": "3a7e7b27-8520-41ca-b1ff-8dfc9b8f259e", "Relationships": [{"Type": "CHILD", "Ids": ["7b4654ab-c55a-43dd-95bc-6cb604cc6eb2", "49d8a183-2068-4842-ac19-c973832b64f7"]}]}, {"BlockType": "LINE", "Confidence": 99.88745880126953, "Text": "12225 Stephens Road", "Geometry": {"BoundingBox": {"Width": 0.12882336974143982, "Height": 0.01064684335142374, "Left": 0.4399399757385254, "Top": 0.2119910717010498}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.211992546916008}, {"X": 0.5687633752822876, "Y": 0.2119910717010498}, {"X": 0.568763256072998, "Y": 0.2226364016532898}, {"X": 0.4399399757385254, "Y": 0.22263790667057037}]}, "Id": "be7baeb3-4fe6-4594-bf81-174fde632137", "Relationships": [{"Type": "CHILD", "Ids": ["dc13bcfe-a7ea-45ba-870b-3dd6acf1cb56", "6ba85bab-2113-4890-bc3f-65260e914a51", "7f6a4ab8-4ad1-4654-8446-839e30715798"]}]}, {"BlockType": "LINE", "Confidence": 99.85464477539062, "Text": "Warren, MI 48089", "Geometry": {"BoundingBox": {"Width": 0.113776296377182, "Height": 0.010304750874638557, "Left": 0.44664397835731506, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.2256883829832077}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23599044978618622}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "5da9f147-6a8b-4cdf-bdd2-b4968b3b4532", "Relationships": [{"Type": "CHILD", "Ids": ["0ab33433-86cd-4082-ba59-ad7fa669676c", "366c9513-e22f-4925-9b53-39b4f3ffc9e9", "db243ad3-38e9-4dee-ac88-b7c568a8a25f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CERTIFIED WEIGHT CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.2301705777645111, "Height": 0.009217815473675728, "Left": 0.39221125841140747, "Top": 0.2900317907333374}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.2900347113609314}, {"X": 0.622381865978241, "Y": 0.2900317907333374}, {"X": 0.6223817467689514, "Y": 0.2992466390132904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "830125ef-0368-4844-ac84-7a29021b54ab", "Relationships": [{"Type": "CHILD", "Ids": ["13ea3e70-b86e-4827-aa0d-13e8250695d9", "ca3c8fd2-4c10-4adb-b595-5f3f8b58d02d", "5dbea9cd-663b-4d71-80cd-c115c08c54ff"]}]}, {"BlockType": "LINE", "Confidence": 99.87091827392578, "Text": "Pro Number: 227407699", "Geometry": {"BoundingBox": {"Width": 0.17372579872608185, "Height": 0.009269524365663528, "Left": 0.15433162450790405, "Top": 0.31617191433906555}, "Polygon": [{"X": 0.15433168411254883, "Y": 0.3161742091178894}, {"X": 0.3280574083328247, "Y": 0.31617191433906555}, {"X": 0.32805734872817993, "Y": 0.3254391551017761}, {"X": 0.15433162450790405, "Y": 0.3254414498806}]}, "Id": "c4a4721e-4e04-4f03-b9d5-3896160c015b", "Relationships": [{"Type": "CHILD", "Ids": ["f6371e2a-8072-48d7-9f8b-f2f9fa9af7cd", "a9f92a37-8752-401b-8bfa-17031e4d5b33", "94d3c781-bf81-4c00-a2ae-e8ad23bf445f"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Bill of Lading", "Geometry": {"BoundingBox": {"Width": 0.08661317825317383, "Height": 0.011761363595724106, "Left": 0.6965249180793762, "Top": 0.3158475160598755}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3158486485481262}, {"X": 0.78313809633255, "Y": 0.3158475160598755}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.6965249180793762, "Y": 0.3276088535785675}]}, "Id": "d8bfce04-df0e-45be-ac00-8380d4b7ee37", "Relationships": [{"Type": "CHILD", "Ids": ["5a9c3fe8-55c2-4b2c-b9c1-9989184b70db", "d43ba40b-ff78-4281-9262-8a1c10f53158", "7dcb046a-6b81-4642-8ed2-92d76fe52b6c"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Date: 08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.1218271404504776, "Height": 0.01059777196496725, "Left": 0.15446539223194122, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3290797770023346}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.15446539223194122, "Y": 0.3396759331226349}]}, "Id": "935b3cd3-d67b-4975-a18b-ecfc30395384", "Relationships": [{"Type": "CHILD", "Ids": ["a7757afb-54ed-47a4-9c7f-ed509b93144a", "cae670ee-d495-448e-9470-048e8c608c91"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "DIAMOND ENVELOPE", "Geometry": {"BoundingBox": {"Width": 0.10091961175203323, "Height": 0.006404740270227194, "Left": 0.16249734163284302, "Top": 0.3682035207748413}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.26341694593429565, "Y": 0.3682035207748413}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.16249734163284302, "Y": 0.37460827827453613}]}, "Id": "35afcbbd-8514-4476-b65f-67b20b71e8b5", "Relationships": [{"Type": "CHILD", "Ids": ["35c00d41-6e56-4882-b320-1f203a013c09", "fde64f7d-e1c6-4df1-8539-d25014aab62a"]}]}, {"BlockType": "LINE", "Confidence": 99.74091339111328, "Text": "Weight Certificate", "Geometry": {"BoundingBox": {"Width": 0.08476640284061432, "Height": 0.008311820216476917, "Left": 0.7232305407524109, "Top": 0.3676687777042389}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.807996928691864, "Y": 0.3676687777042389}, {"X": 0.8079968094825745, "Y": 0.37597939372062683}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "9b316c79-2f84-451b-8bae-f4d6b1213f7c", "Relationships": [{"Type": "CHILD", "Ids": ["e7676441-2e8e-42a6-842c-560b6d0a27a9", "35aa5048-d4e8-49ad-9d75-200da893b0f0"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "2270 WHITE OAK CIR", "Geometry": {"BoundingBox": {"Width": 0.10405416041612625, "Height": 0.006522432900965214, "Left": 0.16246169805526733, "Top": 0.37667709589004517}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.37667855620384216}, {"X": 0.266515851020813, "Y": 0.37667709589004517}, {"X": 0.2665157914161682, "Y": 0.3831980526447296}, {"X": 0.16246169805526733, "Y": 0.3831995129585266}]}, "Id": "ce2e1496-2d29-4c36-8665-5134311d77ea", "Relationships": [{"Type": "CHILD", "Ids": ["77bab07d-7923-4ea5-8e9c-ee82770b78c8", "c378c395-c822-4fa7-b17c-78229b585ed4", "a2bd88de-26c5-4742-8288-2aebfa26d84e", "07fcff54-e3cb-405e-9e97-7c1f215858ca"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "We Certify that this shipment", "Geometry": {"BoundingBox": {"Width": 0.13997326791286469, "Height": 0.008207225240767002, "Left": 0.7073788642883301, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37618815898895264}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.3843914270401001}, {"X": 0.7073788642883301, "Y": 0.3843934237957001}]}, "Id": "7c7e0a2b-a9b7-47a8-84ba-454d4a3cfb2a", "Relationships": [{"Type": "CHILD", "Ids": ["4e06f2cc-f97a-467f-aad2-2fa92d466be4", "5291df57-eb7e-4f29-a7a9-6eaaa940f744", "69fc1461-9667-40fe-a4c6-0844a3f6e73d", "22966f6e-498d-4aca-bffe-5e513a3789f8", "62f456a0-4fa2-48e7-9f0e-872c9b3fa6bb"]}]}, {"BlockType": "LINE", "Confidence": 99.71878814697266, "Text": "AURORA, IL 60502", "Geometry": {"BoundingBox": {"Width": 0.09084655344486237, "Height": 0.007452928461134434, "Left": 0.16181287169456482, "Top": 0.3850322663784027}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.3850335478782654}, {"X": 0.2526594400405884, "Y": 0.3850322663784027}, {"X": 0.2526593804359436, "Y": 0.3924838900566101}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "a7073d5c-ba5a-4211-b047-b98ebed9d0f3", "Relationships": [{"Type": "CHILD", "Ids": ["91c27651-b3d0-41ea-aa0e-50185697985c", "1e4ffd35-d86f-4fc2-aca3-6b356473423b", "98dd186c-26cd-40e9-ae7e-763d5462a2b1"]}]}, {"BlockType": "LINE", "Confidence": 99.95428466796875, "Text": "has been reweighed and that", "Geometry": {"BoundingBox": {"Width": 0.1379849761724472, "Height": 0.008062225766479969, "Left": 0.7077588438987732, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.384868860244751}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3929271399974823}, {"X": 0.7077588438987732, "Y": 0.3929291069507599}]}, "Id": "a6439f1c-8576-414e-862e-3cf90af8db78", "Relationships": [{"Type": "CHILD", "Ids": ["ce8a02c3-cd45-4b2b-a20d-c90b6c8fdf19", "7876da22-217c-4693-a81a-cfb70340b9ee", "291536d7-b05c-4790-aa32-4e7dd73b1f10", "fd72623f-b613-453b-8e9d-d9aeaa8ac8fd", "c6f413fb-f656-4413-b94f-5bb129b908cd"]}]}, {"BlockType": "LINE", "Confidence": 99.98030853271484, "Text": "the corrected weight as stated", "Geometry": {"BoundingBox": {"Width": 0.14533431828022003, "Height": 0.008183035999536514, "Left": 0.707314670085907, "Top": 0.39328068494796753}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39328277111053467}, {"X": 0.8526489734649658, "Y": 0.39328068494796753}, {"X": 0.8526488542556763, "Y": 0.4014616012573242}, {"X": 0.707314670085907, "Y": 0.40146371722221375}]}, "Id": "67c7ba3b-55af-4d2a-bb5c-aab82ad656f4", "Relationships": [{"Type": "CHILD", "Ids": ["cc89cec7-856c-4775-92d4-6467ed2ea890", "2d090b8c-926c-487f-a300-2b5a1ff9dc58", "8f40f5e6-6e8f-4bba-b763-86542dcd88c8", "b49bce36-4ff6-4ada-b393-45df791ea399", "84ea21f2-96a1-4458-8897-4292e564066b"]}]}, {"BlockType": "LINE", "Confidence": 99.91458129882812, "Text": "is the true and accurate", "Geometry": {"BoundingBox": {"Width": 0.11372467130422592, "Height": 0.00710035115480423, "Left": 0.7076213955879211, "Top": 0.40173888206481934}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.8213460445404053, "Y": 0.40173888206481934}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7076213955879211, "Y": 0.40883922576904297}]}, "Id": "b62bea9a-7aee-41d4-ae66-b3d67c9186ed", "Relationships": [{"Type": "CHILD", "Ids": ["d6eec01b-fc4f-46f1-9d8e-8f4af649cbf2", "5e7ae650-abf0-49b2-a70c-c0b9bef25193", "b3b80a89-dc79-47f6-a146-f305297338ce", "6cfe383d-eb78-45d2-8642-4e3d613407d0", "e9fde885-479d-4313-9a12-f9ba272d148c"]}]}, {"BlockType": "LINE", "Confidence": 99.90074920654297, "Text": "Declared Weight: 1148", "Geometry": {"BoundingBox": {"Width": 0.10963373631238937, "Height": 0.008119854144752026, "Left": 0.16241343319416046, "Top": 0.41033288836479187}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.4103344976902008}, {"X": 0.27204716205596924, "Y": 0.41033288836479187}, {"X": 0.27204710245132446, "Y": 0.41845113039016724}, {"X": 0.16241343319416046, "Y": 0.41845273971557617}]}, "Id": "6352e4a7-1348-4e2e-9322-dacf29ea69a8", "Relationships": [{"Type": "CHILD", "Ids": ["31ff43d7-9ed8-4bb3-a64d-5cf49f7502f3", "5c9c8fe4-8c7d-42c3-8c98-8f9ea59bfccc", "c7b808ff-33a6-4824-abdc-f01e3aa399ef"]}]}, {"BlockType": "LINE", "Confidence": 99.9280014038086, "Text": "weight of the shipment", "Geometry": {"BoundingBox": {"Width": 0.1111871749162674, "Height": 0.008168041706085205, "Left": 0.7069318294525146, "Top": 0.4102623462677002}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102639853954315}, {"X": 0.8181189894676208, "Y": 0.4102623462677002}, {"X": 0.8181188702583313, "Y": 0.4184287488460541}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "602316d2-2e20-4303-99f6-2b1bcb558ee7", "Relationships": [{"Type": "CHILD", "Ids": ["0cc47460-cc6f-4885-91d3-710602189bc7", "c8e66a46-6f95-48d6-8de4-382205877943", "c47cc260-6798-452d-9420-dea8abe653db", "8606bf63-fff0-4441-a598-33a3553cc347"]}]}, {"BlockType": "LINE", "Confidence": 99.86766815185547, "Text": "Corrected Weight: 1425", "Geometry": {"BoundingBox": {"Width": 0.11429888010025024, "Height": 0.008122419007122517, "Left": 0.16247409582138062, "Top": 0.41887912154197693}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.27677297592163086, "Y": 0.41887912154197693}, {"X": 0.2767729163169861, "Y": 0.4269998371601105}, {"X": 0.16247409582138062, "Y": 0.4270015358924866}]}, "Id": "eb60328a-8a46-40a5-a66c-0078b219029a", "Relationships": [{"Type": "CHILD", "Ids": ["43364c00-8c0c-4af8-8ebc-eba830147d54", "cad5a82a-fa4c-4aac-8123-c6657ecb3913", "63475d3e-b0dc-4247-b7c9-e31e4f2648bd"]}]}, {"BlockType": "LINE", "Confidence": 99.7120132446289, "Text": "HU", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "9732ae21-2b82-4088-b9e9-37195fee774e", "Relationships": [{"Type": "CHILD", "Ids": ["0925f427-8a7a-46ad-ad52-55851896dbc7"]}]}, {"BlockType": "LINE", "Confidence": 99.89058685302734, "Text": "<PERSON>an Date", "Geometry": {"BoundingBox": {"Width": 0.0600147508084774, "Height": 0.0071591404266655445, "Left": 0.19829589128494263, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45388174057006836}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.19829589128494263, "Y": 0.4610399603843689}]}, "Id": "f5967840-1279-44ef-8b80-bf4fd522508f", "Relationships": [{"Type": "CHILD", "Ids": ["d5ad37f8-de42-45a3-9dd4-31559f0cd2cd", "1fc8a4ea-58eb-4a5c-91d0-29a6b7aaf59f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "e4456382-87cc-417b-bb9d-57ff17d92cc2", "Relationships": [{"Type": "CHILD", "Ids": ["eda01ca0-4c97-42ed-8d22-6f999e1d957c"]}]}, {"BlockType": "LINE", "Confidence": 99.78605651855469, "Text": "Weight Terminal", "Geometry": {"BoundingBox": {"Width": 0.09505319595336914, "Height": 0.009380130097270012, "Left": 0.3337138891220093, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.45351457595825195}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876699566841125, "Y": 0.4628917872905731}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "732e157f-ba54-4cca-b8d9-446bb01811d5", "Relationships": [{"Type": "CHILD", "Ids": ["e7db72cf-f1ec-46c2-9a40-7adc09eaf7a0", "bdf913a5-7d02-47ff-9830-7d3fd0d20951"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "Pro Number", "Geometry": {"BoundingBox": {"Width": 0.0677749365568161, "Height": 0.007399872411042452, "Left": 0.46933484077453613, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.45365455746650696}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.4610523581504822}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "6b271ff8-ae90-4f41-8c92-c1100a1f8216", "Relationships": [{"Type": "CHILD", "Ids": ["89e40596-a7d1-45d0-8b48-0bf2deb9b3d3", "c6764da8-2ba7-4695-8bc1-b411953293e2"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "33136199", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "68ee6c39-2575-49a0-aa12-12080c2e6c29", "Relationships": [{"Type": "CHILD", "Ids": ["c77d73aa-4a80-4039-92e0-8104e5dda6af"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "9a0b5632-9450-42df-8051-d6359145e89b", "Relationships": [{"Type": "CHILD", "Ids": ["f1fac649-6c59-46f0-be1d-fd907ccc50ba"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "00:02 880", "Geometry": {"BoundingBox": {"Width": 0.06711716204881668, "Height": 0.007216433063149452, "Left": 0.3340480327606201, "Top": 0.4650701880455017}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.4011651873588562, "Y": 0.4650701880455017}, {"X": 0.4011651277542114, "Y": 0.47228556871414185}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "ac17d78d-913a-41a3-8b18-bc45016721c4", "Relationships": [{"Type": "CHILD", "Ids": ["0c0cea16-ccac-48c9-8e9a-2c4a3d3ea70b", "a5ebb5d6-c2e3-4955-b897-8782370e9428"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "9efc46c4-5ed7-449e-a61a-693a7188f663", "Relationships": [{"Type": "CHILD", "Ids": ["0b7b1b23-8994-401c-aee5-74a9bae9d41a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "38b94c1f-a25b-4a8d-b9c0-74becc584a05", "Relationships": [{"Type": "CHILD", "Ids": ["a3148665-38b4-4550-9334-83645bdbd502"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "33136200", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "301773d8-9d1f-47fb-a394-2744e58f3513", "Relationships": [{"Type": "CHILD", "Ids": ["c0f8fc78-db07-438d-b3e7-46826e757813"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "0bd1a80a-4bb3-4ca6-b382-81ac8b97b8a5", "Relationships": [{"Type": "CHILD", "Ids": ["ab8eb615-aba9-46bd-9ee0-9599227bb13b"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "00:03", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "125efda1-508f-4856-8f74-4c9fcf94f9d1", "Relationships": [{"Type": "CHILD", "Ids": ["b1978ee6-9ca9-49ca-b8ce-7d78ac920234"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "545", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "fbb291f5-0ec2-4c4f-b669-783539ca3f29", "Relationships": [{"Type": "CHILD", "Ids": ["049efd94-db41-40bd-b9fc-fc885c0bbbcf"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "5952386a-6c19-4fc1-b212-e650582d431d", "Relationships": [{"Type": "CHILD", "Ids": ["bb4dc24f-458e-4fd6-b3d1-9ccdfacfe61d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "63d5e4c4-459a-44c9-8089-77f72563d647", "Relationships": [{"Type": "CHILD", "Ids": ["8f0d9fdd-f05d-43d9-b197-cd71ecef6454"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "9903d8e0-1c42-40da-86a9-489d182f511f", "Relationships": [{"Type": "CHILD", "Ids": ["3cd20075-ed98-4e9e-b0c1-cd998b649f06"]}]}, {"BlockType": "LINE", "Confidence": 99.68511199951172, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "64f0f5fd-fda3-42c8-b327-aee732d6bb0a", "Relationships": [{"Type": "CHILD", "Ids": ["324d0501-43c8-4422-ba51-505b53b168c2"]}]}, {"BlockType": "LINE", "Confidence": 99.79252624511719, "Text": "5", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "edf35bb1-2d4b-485e-b718-759f6f3b2cdf", "Relationships": [{"Type": "CHILD", "Ids": ["a2cb18e4-146f-488e-b589-329fa02038ae"]}]}, {"BlockType": "LINE", "Confidence": 99.71281433105469, "Text": "6", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "3ed02998-ff51-44c3-a8bb-a1c02b8d79d1", "Relationships": [{"Type": "CHILD", "Ids": ["82eca2be-2b3f-4587-81ff-400fd48c08a2"]}]}, {"BlockType": "LINE", "Confidence": 99.88201141357422, "Text": "7", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "c22f0b1c-55e9-4da7-a5ce-f3f06f015e5e", "Relationships": [{"Type": "CHILD", "Ids": ["f1a22762-748a-4b38-9073-b4c0da55554b"]}]}, {"BlockType": "LINE", "Confidence": 99.67375183105469, "Text": "8", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "0ec88894-7cfa-454a-9c29-874b41c153d3", "Relationships": [{"Type": "CHILD", "Ids": ["bef46dc1-4fd8-4aa5-91b9-909467bb25b1"]}]}, {"BlockType": "LINE", "Confidence": 99.60299682617188, "Text": "9", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "88feb2b1-1f87-4e32-91da-30290a2da82f", "Relationships": [{"Type": "CHILD", "Ids": ["0ef88f35-a13e-450b-815b-531ba2e50bfd"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "10", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "162a6661-6d2a-4ac3-8f00-598d8d4a9b2f", "Relationships": [{"Type": "CHILD", "Ids": ["0abb7a82-b132-4bfb-8b43-3b06a4b56e7b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "33fd6998-b10e-4503-ba30-d1c1a35d9fb4", "Relationships": [{"Type": "CHILD", "Ids": ["d2d10a35-e66a-476c-810f-2ced6183baba"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1425", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "cfc8dae0-b0a5-4a88-ab8b-b0ace02f18b1", "Relationships": [{"Type": "CHILD", "Ids": ["5d0631b6-0eaf-4216-a673-4a4c145b2a2c"]}]}, {"BlockType": "LINE", "Confidence": 99.75875091552734, "Text": "Page 3/3", "Geometry": {"BoundingBox": {"Width": 0.05226879566907883, "Height": 0.010070810094475746, "Left": 0.8169759511947632, "Top": 0.9619084596633911}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8692447543144226, "Y": 0.9619084596633911}, {"X": 0.8692446351051331, "Y": 0.9719780087471008}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "607f5af1-0b91-452c-a16c-fd9a6e180ddd", "Relationships": [{"Type": "CHILD", "Ids": ["250fd587-9c09-4bde-b30a-2500ef08cdb5", "083d245f-e50e-48a2-b021-9e2dc380c6f4"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Central", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047523383051157, "Height": 0.009486476890742779, "Left": 0.05718903988599777, "Top": 0.05624702572822571}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.10471241921186447, "Y": 0.05624702572822571}, {"X": 0.10471236705780029, "Y": 0.06573306769132614}, {"X": 0.05718903988599777, "Y": 0.06573350727558136}]}, "Id": "19729f2a-ee28-41df-962d-da77a0b9bd7e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transport", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06371095031499863, "Height": 0.011445809155702591, "Left": 0.10830892622470856, "Top": 0.056378427892923355}, "Polygon": [{"X": 0.10830899327993393, "Y": 0.05637900158762932}, {"X": 0.17201988399028778, "Y": 0.056378427892923355}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.10830892622470856, "Y": 0.06782423704862595}]}, "Id": "29748f3a-8ba6-4450-8aed-6f540a6e0888"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "08/31/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "4666e96f-287e-4b6d-b8e4-d663ddb00677"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "PRO#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043377768248319626, "Height": 0.009395409375429153, "Left": 0.057394713163375854, "Top": 0.07146386057138443}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07146426290273666}, {"X": 0.10077248513698578, "Y": 0.07146386057138443}, {"X": 0.1007724329829216, "Y": 0.08085886389017105}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "04d37094-9420-4f09-ae3f-0478757fe966"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07391535490751266, "Height": 0.009347072802484035, "Left": 0.10520923137664795, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.10520928353071213, "Y": 0.07145117968320847}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08079687505960464}, {"X": 0.10520923137664795, "Y": 0.08079757541418076}]}, "Id": "c2230e77-9e6d-4645-8a92-f4326eda6903"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04729338735342026, "Height": 0.011711383238434792, "Left": 0.8079518675804138, "Top": 0.07136216014623642}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.8552452921867371, "Y": 0.07136216014623642}, {"X": 0.8552451133728027, "Y": 0.08307309448719025}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "499f0269-3d20-41d8-88b8-bdbaba5246c8"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010260049253702164, "Height": 0.00927706342190504, "Left": 0.8587119579315186, "Top": 0.07156898826360703}, "Polygon": [{"X": 0.8587120771408081, "Y": 0.07156908512115479}, {"X": 0.8689720034599304, "Y": 0.07156898826360703}, {"X": 0.8689718842506409, "Y": 0.08084595203399658}, {"X": 0.8587119579315186, "Y": 0.08084604889154434}]}, "Id": "eaabf4d8-edc9-4c33-921c-836c1401ed1e"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06620460003614426, "Height": 0.011348029598593712, "Left": 0.8727492690086365, "Top": 0.07158870249986649}, "Polygon": [{"X": 0.8727494478225708, "Y": 0.07158931344747543}, {"X": 0.9389538764953613, "Y": 0.07158870249986649}, {"X": 0.938953697681427, "Y": 0.0829361081123352}, {"X": 0.8727492690086365, "Y": 0.08293673396110535}]}, "Id": "1a192e8c-c9a7-44a1-ac59-b34f9b57fe4c"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CENTRAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0677325576543808, "Height": 0.01386458333581686, "Left": 0.4383789896965027, "Top": 0.19190871715545654}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19190946221351624}, {"X": 0.5061115622520447, "Y": 0.19190871715545654}, {"X": 0.5061113834381104, "Y": 0.2057725191116333}, {"X": 0.4383789896965027, "Y": 0.20577329397201538}]}, "Id": "7b4654ab-c55a-43dd-95bc-6cb604cc6eb2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09249085932970047, "Height": 0.013988036662340164, "Left": 0.508831262588501, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.5088314414024353, "Y": 0.19188755750656128}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.508831262588501, "Y": 0.20587456226348877}]}, "Id": "49d8a183-2068-4842-ac19-c973832b64f7"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "12225", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035280805081129074, "Height": 0.00855326559394598, "Left": 0.4399400055408478, "Top": 0.2120484709739685}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.21204887330532074}, {"X": 0.47522079944610596, "Y": 0.2120484709739685}, {"X": 0.4752207100391388, "Y": 0.22060133516788483}, {"X": 0.4399400055408478, "Y": 0.22060173749923706}]}, "Id": "dc13bcfe-a7ea-45ba-870b-3dd6acf1cb56"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05482405051589012, "Height": 0.010645987465977669, "Left": 0.47985780239105225, "Top": 0.21199145913124084}, "Polygon": [{"X": 0.4798579216003418, "Y": 0.211992084980011}, {"X": 0.5346818566322327, "Y": 0.21199145913124084}, {"X": 0.5346817374229431, "Y": 0.22263680398464203}, {"X": 0.47985780239105225, "Y": 0.22263744473457336}]}, "Id": "6ba85bab-2113-4890-bc3f-65260e914a51"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02949291281402111, "Height": 0.008774659596383572, "Left": 0.5392704606056213, "Top": 0.2120930254459381}, "Polygon": [{"X": 0.5392705202102661, "Y": 0.21209336817264557}, {"X": 0.5687633752822876, "Y": 0.2120930254459381}, {"X": 0.568763256072998, "Y": 0.2208673506975174}, {"X": 0.5392704606056213, "Y": 0.22086769342422485}]}, "Id": "7f6a4ab8-4ad1-4654-8446-839e30715798"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04800622910261154, "Height": 0.010209893807768822, "Left": 0.44664397835731506, "Top": 0.2257819026708603}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.22578246891498566}, {"X": 0.4946502149105072, "Y": 0.2257819026708603}, {"X": 0.49465009570121765, "Y": 0.2359912246465683}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "0ab33433-86cd-4082-ba59-ad7fa669676c"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0159319918602705, "Height": 0.00851416401565075, "Left": 0.49934086203575134, "Top": 0.22570796310901642}, "Polygon": [{"X": 0.4993409514427185, "Y": 0.22570815682411194}, {"X": 0.515272855758667, "Y": 0.22570796310901642}, {"X": 0.5152727365493774, "Y": 0.234221950173378}, {"X": 0.49934086203575134, "Y": 0.23422212898731232}]}, "Id": "366c9513-e22f-4925-9b53-39b4f3ffc9e9"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "48089", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036994144320487976, "Height": 0.008614152669906616, "Left": 0.5234261155128479, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.5234262347221375, "Y": 0.22568747401237488}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23430076241493225}, {"X": 0.5234261155128479, "Y": 0.23430119454860687}]}, "Id": "db243ad3-38e9-4dee-ac88-b7c568a8a25f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFIED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07389842718839645, "Height": 0.008876699954271317, "Left": 0.39221125841140747, "Top": 0.29037290811538696}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.290373831987381}, {"X": 0.4661096930503845, "Y": 0.29037290811538696}, {"X": 0.46610960364341736, "Y": 0.2992486357688904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "13ea3e70-b86e-4827-aa0d-13e8250695d9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05728071182966232, "Height": 0.009197513572871685, "Left": 0.47053226828575134, "Top": 0.2900329828262329}, "Polygon": [{"X": 0.4705323576927185, "Y": 0.2900336980819702}, {"X": 0.5278129577636719, "Y": 0.2900329828262329}, {"X": 0.5278128981590271, "Y": 0.29922977089881897}, {"X": 0.47053226828575134, "Y": 0.2992304861545563}]}, "Id": "ca3c8fd2-4c10-4adb-b595-5f3f8b58d02d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08967792242765427, "Height": 0.008884044364094734, "Left": 0.5327039361000061, "Top": 0.2903496026992798}, "Polygon": [{"X": 0.5327039957046509, "Y": 0.2903507351875305}, {"X": 0.622381865978241, "Y": 0.2903496026992798}, {"X": 0.6223817467689514, "Y": 0.29923248291015625}, {"X": 0.5327039361000061, "Y": 0.29923364520072937}]}, "Id": "5dbea9cd-663b-4d71-80cd-c115c08c54ff"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021388905122876167, "Height": 0.008751227520406246, "Left": 0.15433162450790405, "Top": 0.3165864050388336}, "Polygon": [{"X": 0.15433166921138763, "Y": 0.3165866732597351}, {"X": 0.17572052776813507, "Y": 0.3165864050388336}, {"X": 0.1757204681634903, "Y": 0.32533735036849976}, {"X": 0.15433162450790405, "Y": 0.32533761858940125}]}, "Id": "f6371e2a-8072-48d7-9f8b-f2f9fa9af7cd"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05895177647471428, "Height": 0.009268013760447502, "Left": 0.18463827669620514, "Top": 0.3161730170249939}, "Polygon": [{"X": 0.18463833630084991, "Y": 0.316173791885376}, {"X": 0.24359004199504852, "Y": 0.3161730170249939}, {"X": 0.24358998239040375, "Y": 0.32544025778770447}, {"X": 0.18463827669620514, "Y": 0.32544103264808655}]}, "Id": "a9f92a37-8752-401b-8bfa-17031e4d5b33"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07552546262741089, "Height": 0.009020472876727581, "Left": 0.2525319457054138, "Top": 0.31638628244400024}, "Polygon": [{"X": 0.252532035112381, "Y": 0.3163872957229614}, {"X": 0.3280574083328247, "Y": 0.31638628244400024}, {"X": 0.32805734872817993, "Y": 0.3254057765007019}, {"X": 0.2525319457054138, "Y": 0.3254067599773407}]}, "Id": "94d3c781-bf81-4c00-a2ae-e8ad23bf445f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01808108016848564, "Height": 0.009077761322259903, "Left": 0.6965249180793762, "Top": 0.3161868751049042}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3161871135234833}, {"X": 0.714605987071991, "Y": 0.3161868751049042}, {"X": 0.7146058678627014, "Y": 0.3252643942832947}, {"X": 0.6965249180793762, "Y": 0.3252646327018738}]}, "Id": "5a9c3fe8-55c2-4b2c-b9c1-9989184b70db"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014402875676751137, "Height": 0.009438899345695972, "Left": 0.7203461527824402, "Top": 0.31584814190864563}, "Polygon": [{"X": 0.7203463315963745, "Y": 0.31584832072257996}, {"X": 0.7347490787506104, "Y": 0.31584814190864563}, {"X": 0.734748899936676, "Y": 0.3252868354320526}, {"X": 0.7203461527824402, "Y": 0.3252870440483093}]}, "Id": "d43ba40b-ff78-4281-9262-8a1c10f53158"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Lading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04320087283849716, "Height": 0.011417851783335209, "Left": 0.7399371862411499, "Top": 0.3161904215812683}, "Polygon": [{"X": 0.7399373650550842, "Y": 0.31619101762771606}, {"X": 0.78313809633255, "Y": 0.3161904215812683}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.7399371862411499, "Y": 0.32760828733444214}]}, "Id": "7dcb046a-6b81-4642-8ed2-92d76fe52b6c"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03578847274184227, "Height": 0.008917699567973614, "Left": 0.15446539223194122, "Top": 0.3295671343803406}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3295676112174988}, {"X": 0.1902538686990738, "Y": 0.3295671343803406}, {"X": 0.19025380909442902, "Y": 0.33848437666893005}, {"X": 0.15446539223194122, "Y": 0.33848485350608826}]}, "Id": "a7757afb-54ed-47a4-9c7f-ed509b93144a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07925864309072495, "Height": 0.010597198270261288, "Left": 0.19703388214111328, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.19703395664691925, "Y": 0.32907921075820923}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.19703388214111328, "Y": 0.33967533707618713}]}, "Id": "cae670ee-d495-448e-9470-048e8c608c91"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DIAMOND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04778068885207176, "Height": 0.006365248467773199, "Left": 0.16249734163284302, "Top": 0.368204265832901}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.21027801930904388, "Y": 0.368204265832901}, {"X": 0.2102779746055603, "Y": 0.3745688498020172}, {"X": 0.16249734163284302, "Y": 0.37456953525543213}]}, "Id": "35c00d41-6e56-4882-b320-1f203a013c09"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "ENVELOPE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010327696800232, "Height": 0.00638725608587265, "Left": 0.21331366896629333, "Top": 0.3682202994823456}, "Polygon": [{"X": 0.21331371366977692, "Y": 0.3682210147380829}, {"X": 0.26341694593429565, "Y": 0.3682202994823456}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.21331366896629333, "Y": 0.37460756301879883}]}, "Id": "fde64f7d-e1c6-4df1-8539-d25014aab62a"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03317679092288017, "Height": 0.008311101235449314, "Left": 0.7232305407524109, "Top": 0.3676694929599762}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.7564073204994202, "Y": 0.3676694929599762}, {"X": 0.7564072012901306, "Y": 0.3759801387786865}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "e7676441-2e8e-42a6-842c-560b6d0a27a9"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04864736273884773, "Height": 0.006783640012145042, "Left": 0.7593495845794678, "Top": 0.3677155077457428}, "Polygon": [{"X": 0.7593496441841125, "Y": 0.3677161931991577}, {"X": 0.807996928691864, "Y": 0.3677155077457428}, {"X": 0.8079968094825745, "Y": 0.3744984567165375}, {"X": 0.7593495845794678, "Y": 0.3744991421699524}]}, "Id": "35aa5048-d4e8-49ad-9d75-200da893b0f0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2270", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02383694052696228, "Height": 0.006322705652564764, "Left": 0.16246169805526733, "Top": 0.37672942876815796}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.3767297565937042}, {"X": 0.18629863858222961, "Y": 0.37672942876815796}, {"X": 0.18629859387874603, "Y": 0.3830517828464508}, {"X": 0.16246169805526733, "Y": 0.38305214047431946}]}, "Id": "77bab07d-7923-4ea5-8e9c-ee82770b78c8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WHITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032461635768413544, "Height": 0.006380218081176281, "Left": 0.18896152079105377, "Top": 0.3766777217388153}, "Polygon": [{"X": 0.18896156549453735, "Y": 0.3766781687736511}, {"X": 0.22142314910888672, "Y": 0.3766777217388153}, {"X": 0.22142310440540314, "Y": 0.38305747509002686}, {"X": 0.18896152079105377, "Y": 0.38305795192718506}]}, "Id": "c378c395-c822-4fa7-b17c-78229b585ed4"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "OAK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02111927978694439, "Height": 0.006493091583251953, "Left": 0.22455410659313202, "Top": 0.3767055571079254}, "Polygon": [{"X": 0.2245541661977768, "Y": 0.3767058551311493}, {"X": 0.24567338824272156, "Y": 0.3767055571079254}, {"X": 0.24567334353923798, "Y": 0.3831983506679535}, {"X": 0.22455410659313202, "Y": 0.38319864869117737}]}, "Id": "a2bd88de-26c5-4742-8288-2aebfa26d84e"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "CIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017902487888932228, "Height": 0.00622687628492713, "Left": 0.2486133575439453, "Top": 0.3768872916698456}, "Polygon": [{"X": 0.2486134171485901, "Y": 0.3768875300884247}, {"X": 0.266515851020813, "Y": 0.3768872916698456}, {"X": 0.2665157914161682, "Y": 0.38311392068862915}, {"X": 0.2486133575439453, "Y": 0.38311415910720825}]}, "Id": "07fcff54-e3cb-405e-9e97-7c1f215858ca"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "We", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014652658253908157, "Height": 0.006238427013158798, "Left": 0.7073789238929749, "Top": 0.37680166959762573}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37680187821388245}, {"X": 0.7220315337181091, "Y": 0.37680166959762573}, {"X": 0.7220314741134644, "Y": 0.3830398917198181}, {"X": 0.7073789238929749, "Y": 0.38304010033607483}]}, "Id": "4e06f2cc-f97a-467f-aad2-2fa92d466be4"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03157906234264374, "Height": 0.008171954192221165, "Left": 0.7253687977790833, "Top": 0.3762212097644806}, "Polygon": [{"X": 0.7253689169883728, "Y": 0.3762216567993164}, {"X": 0.7569478750228882, "Y": 0.3762212097644806}, {"X": 0.7569477558135986, "Y": 0.38439270853996277}, {"X": 0.7253687977790833, "Y": 0.3843931555747986}]}, "Id": "5291df57-eb7e-4f29-a7a9-6eaaa940f744"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019612392410635948, "Height": 0.006630921270698309, "Left": 0.7600220441818237, "Top": 0.37648865580558777}, "Polygon": [{"X": 0.7600221633911133, "Y": 0.37648892402648926}, {"X": 0.7796344757080078, "Y": 0.37648865580558777}, {"X": 0.7796343564987183, "Y": 0.38311928510665894}, {"X": 0.7600220441818237, "Y": 0.3831195831298828}]}, "Id": "69fc1461-9667-40fe-a4c6-0844a3f6e73d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0170767679810524, "Height": 0.00658020656555891, "Left": 0.7818982601165771, "Top": 0.37639084458351135}, "Polygon": [{"X": 0.7818983793258667, "Y": 0.37639108300209045}, {"X": 0.7989750504493713, "Y": 0.37639084458351135}, {"X": 0.7989749312400818, "Y": 0.38297080993652344}, {"X": 0.7818982601165771, "Y": 0.38297104835510254}]}, "Id": "22966f6e-498d-4aca-bffe-5e513a3789f8"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044950954616069794, "Height": 0.008148849941790104, "Left": 0.8024011850357056, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.8024013042449951, "Y": 0.3761868178844452}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.38433441519737244}, {"X": 0.8024011850357056, "Y": 0.3843350410461426}]}, "Id": "62f456a0-4fa2-48e7-9f0e-872c9b3fa6bb"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "AURORA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04494357109069824, "Height": 0.0072541264817118645, "Left": 0.16181287169456482, "Top": 0.385231077671051}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.38523170351982117}, {"X": 0.20675644278526306, "Y": 0.385231077671051}, {"X": 0.2067563831806183, "Y": 0.39248454570770264}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "91c27651-b3d0-41ea-aa0e-50185697985c"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009762809611856937, "Height": 0.006594124715775251, "Left": 0.21053430438041687, "Top": 0.3850327432155609}, "Polygon": [{"X": 0.21053434908390045, "Y": 0.38503286242485046}, {"X": 0.22029711306095123, "Y": 0.3850327432155609}, {"X": 0.22029706835746765, "Y": 0.3916267156600952}, {"X": 0.21053430438041687, "Y": 0.39162686467170715}]}, "Id": "1e4ffd35-d86f-4fc2-aca3-6b356473423b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "60502", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029458606615662575, "Height": 0.006240761373192072, "Left": 0.22320081293582916, "Top": 0.38528716564178467}, "Polygon": [{"X": 0.22320085763931274, "Y": 0.3852875828742981}, {"X": 0.252659410238266, "Y": 0.38528716564178467}, {"X": 0.2526593804359436, "Y": 0.3915275037288666}, {"X": 0.22320081293582916, "Y": 0.39152792096138}]}, "Id": "98dd186c-26cd-40e9-ae7e-763d5462a2b1"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "has", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016709955409169197, "Height": 0.006623478606343269, "Left": 0.7077588438987732, "Top": 0.38500329852104187}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.38500353693962097}, {"X": 0.7244688272476196, "Y": 0.38500329852104187}, {"X": 0.7244687080383301, "Y": 0.3916265368461609}, {"X": 0.7077588438987732, "Y": 0.39162677526474}]}, "Id": "ce8a02c3-cd45-4b2b-a20d-c90b6c8fdf19"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023208731785416603, "Height": 0.006726184394210577, "Left": 0.7273632884025574, "Top": 0.3849447965621948}, "Polygon": [{"X": 0.7273633480072021, "Y": 0.3849451243877411}, {"X": 0.7505720257759094, "Y": 0.3849447965621948}, {"X": 0.7505719065666199, "Y": 0.3916706442832947}, {"X": 0.7273632884025574, "Y": 0.39167100191116333}]}, "Id": "7876da22-217c-4693-a81a-cfb70340b9ee"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "reweighed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010595545172691, "Height": 0.00796002522110939, "Left": 0.75407475233078, "Top": 0.38496842980384827}, "Polygon": [{"X": 0.7540748715400696, "Y": 0.38496914505958557}, {"X": 0.8041807413101196, "Y": 0.38496842980384827}, {"X": 0.8041806221008301, "Y": 0.39292773604393005}, {"X": 0.75407475233078, "Y": 0.39292845129966736}]}, "Id": "291536d7-b05c-4790-aa32-4e7dd73b1f10"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01778884045779705, "Height": 0.006604108493775129, "Left": 0.806389570236206, "Top": 0.3849635720252991}, "Polygon": [{"X": 0.8063896894454956, "Y": 0.38496384024620056}, {"X": 0.8241783976554871, "Y": 0.3849635720252991}, {"X": 0.8241783380508423, "Y": 0.3915674388408661}, {"X": 0.806389570236206, "Y": 0.3915676772594452}]}, "Id": "fd72623f-b613-453b-8e9d-d9aeaa8ac8fd"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018977243453264236, "Height": 0.006806911434978247, "Left": 0.8267665505409241, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.8267666697502136, "Y": 0.3848671615123749}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3916735351085663}, {"X": 0.8267665505409241, "Y": 0.3916738033294678}]}, "Id": "c6f413fb-f656-4413-b94f-5bb129b908cd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015432396903634071, "Height": 0.006619512569159269, "Left": 0.707314670085907, "Top": 0.39335307478904724}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39335328340530396}, {"X": 0.7227470874786377, "Y": 0.39335307478904724}, {"X": 0.7227469682693481, "Y": 0.3999723792076111}, {"X": 0.707314670085907, "Y": 0.3999725878238678}]}, "Id": "cc89cec7-856c-4775-92d4-6467ed2ea890"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045688241720199585, "Height": 0.006469257641583681, "Left": 0.7260426878929138, "Top": 0.3935489356517792}, "Polygon": [{"X": 0.7260427474975586, "Y": 0.3935495913028717}, {"X": 0.771730899810791, "Y": 0.3935489356517792}, {"X": 0.7717308402061462, "Y": 0.40001752972602844}, {"X": 0.7260426878929138, "Y": 0.40001818537712097}]}, "Id": "2d090b8c-926c-487f-a300-2b5a1ff9dc58"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031995803117752075, "Height": 0.008181402459740639, "Left": 0.7740893959999084, "Top": 0.39328134059906006}, "Polygon": [{"X": 0.774089515209198, "Y": 0.3932817876338959}, {"X": 0.8060852289199829, "Y": 0.39328134059906006}, {"X": 0.8060850501060486, "Y": 0.40146228671073914}, {"X": 0.7740893959999084, "Y": 0.40146273374557495}]}, "Id": "8f40f5e6-6e8f-4bba-b763-86542dcd88c8"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010669945739209652, "Height": 0.004868826363235712, "Left": 0.8092235326766968, "Top": 0.394987553358078}, "Polygon": [{"X": 0.8092235922813416, "Y": 0.39498770236968994}, {"X": 0.819893479347229, "Y": 0.394987553358078}, {"X": 0.8198934197425842, "Y": 0.39985620975494385}, {"X": 0.8092235326766968, "Y": 0.3998563587665558}]}, "Id": "b49bce36-4ff6-4ada-b393-45df791ea399"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029793880879878998, "Height": 0.006427379325032234, "Left": 0.8228550553321838, "Top": 0.39359018206596375}, "Polygon": [{"X": 0.8228551745414734, "Y": 0.39359062910079956}, {"X": 0.8526489734649658, "Y": 0.39359018206596375}, {"X": 0.8526488542556763, "Y": 0.4000171422958374}, {"X": 0.8228550553321838, "Y": 0.40001755952835083}]}, "Id": "84ea21f2-96a1-4458-8897-4292e564066b"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007551476359367371, "Height": 0.0070420317351818085, "Left": 0.7076213955879211, "Top": 0.4017404317855835}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.7151728868484497, "Y": 0.4017404317855835}, {"X": 0.7151727676391602, "Y": 0.40878233313560486}, {"X": 0.7076213955879211, "Y": 0.4087824523448944}]}, "Id": "d6eec01b-fc4f-46f1-9d8e-8f4af649cbf2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015589390881359577, "Height": 0.006525528617203236, "Left": 0.717993974685669, "Top": 0.40196681022644043}, "Polygon": [{"X": 0.7179940938949585, "Y": 0.40196701884269714}, {"X": 0.733583390712738, "Y": 0.40196681022644043}, {"X": 0.7335832715034485, "Y": 0.4084920883178711}, {"X": 0.717993974685669, "Y": 0.4084923267364502}]}, "Id": "5e7ae650-abf0-49b2-a70c-c0b9bef25193"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "true", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019698061048984528, "Height": 0.00635173823684454, "Left": 0.7363592386245728, "Top": 0.40228596329689026}, "Polygon": [{"X": 0.7363593578338623, "Y": 0.40228626132011414}, {"X": 0.7560573220252991, "Y": 0.40228596329689026}, {"X": 0.7560572028160095, "Y": 0.4086374044418335}, {"X": 0.7363592386245728, "Y": 0.4086377024650574}]}, "Id": "b3b80a89-dc79-47f6-a146-f305297338ce"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01766594685614109, "Height": 0.006636554840952158, "Left": 0.7591797113418579, "Top": 0.4019549787044525}, "Polygon": [{"X": 0.7591797709465027, "Y": 0.401955246925354}, {"X": 0.7768456339836121, "Y": 0.4019549787044525}, {"X": 0.7768455743789673, "Y": 0.40859127044677734}, {"X": 0.7591797113418579, "Y": 0.40859153866767883}]}, "Id": "6cfe383d-eb78-45d2-8642-4e3d613407d0"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "accurate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0413362979888916, "Height": 0.0063436212949454784, "Left": 0.7800097465515137, "Top": 0.40249454975128174}, "Polygon": [{"X": 0.7800098657608032, "Y": 0.4024951756000519}, {"X": 0.8213460445404053, "Y": 0.40249454975128174}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7800097465515137, "Y": 0.4088381826877594}]}, "Id": "e9fde885-479d-4313-9a12-f9ba272d148c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Declared", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04313170164823532, "Height": 0.0064966813661158085, "Left": 0.16241343319416046, "Top": 0.4106127619743347}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.41061341762542725}, {"X": 0.20554512739181519, "Y": 0.4106127619743347}, {"X": 0.2055450826883316, "Y": 0.41710883378982544}, {"X": 0.16241343319416046, "Y": 0.4171094596385956}]}, "Id": "31ff43d7-9ed8-4bb3-a64d-5cf49f7502f3"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03596487268805504, "Height": 0.008118771947920322, "Left": 0.20843079686164856, "Top": 0.4103333055973053}, "Polygon": [{"X": 0.20843085646629333, "Y": 0.4103338122367859}, {"X": 0.2443956732749939, "Y": 0.4103333055973053}, {"X": 0.24439561367034912, "Y": 0.41845154762268066}, {"X": 0.20843079686164856, "Y": 0.41845208406448364}]}, "Id": "5c9c8fe4-8c7d-42c3-8c98-8f9ea59bfccc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1148", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023435501381754875, "Height": 0.006363362539559603, "Left": 0.2486116588115692, "Top": 0.4108577370643616}, "Polygon": [{"X": 0.2486117035150528, "Y": 0.4108580946922302}, {"X": 0.27204716205596924, "Y": 0.4108577370643616}, {"X": 0.27204710245132446, "Y": 0.4172207713127136}, {"X": 0.2486116588115692, "Y": 0.4172210991382599}]}, "Id": "c7b808ff-33a6-4824-abdc-f01e3aa399ef"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03254435956478119, "Height": 0.008135346695780754, "Left": 0.7069318294525146, "Top": 0.4102950394153595}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102955162525177}, {"X": 0.739476203918457, "Y": 0.4102950394153595}, {"X": 0.7394760847091675, "Y": 0.4184299111366272}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "0cc47460-cc6f-4885-91d3-710602189bc7"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009893991984426975, "Height": 0.006705030333250761, "Left": 0.7423079609870911, "Top": 0.410263329744339}, "Polygon": [{"X": 0.7423080205917358, "Y": 0.4102634787559509}, {"X": 0.7522019147872925, "Y": 0.410263329744339}, {"X": 0.7522018551826477, "Y": 0.4169681966304779}, {"X": 0.7423079609870911, "Y": 0.41696834564208984}]}, "Id": "c8e66a46-6f95-48d6-8de4-382205877943"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015859805047512054, "Height": 0.0065754735842347145, "Left": 0.7543373107910156, "Top": 0.4106237292289734}, "Polygon": [{"X": 0.7543374300003052, "Y": 0.4106239676475525}, {"X": 0.7701971530914307, "Y": 0.4106237292289734}, {"X": 0.7701970338821411, "Y": 0.4171989858150482}, {"X": 0.7543373107910156, "Y": 0.4171992242336273}]}, "Id": "c47cc260-6798-452d-9420-dea8abe653db"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04508283734321594, "Height": 0.007687367964535952, "Left": 0.7730361223220825, "Top": 0.41048917174339294}, "Polygon": [{"X": 0.7730362415313721, "Y": 0.4104898273944855}, {"X": 0.8181189894676208, "Y": 0.41048917174339294}, {"X": 0.8181188702583313, "Y": 0.4181758761405945}, {"X": 0.7730361223220825, "Y": 0.418176531791687}]}, "Id": "8606bf63-fff0-4441-a598-33a3553cc347"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04748039320111275, "Height": 0.006810094229876995, "Left": 0.16247409582138062, "Top": 0.4188801050186157}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.20995448529720306, "Y": 0.4188801050186157}, {"X": 0.20995444059371948, "Y": 0.4256894886493683}, {"X": 0.16247409582138062, "Y": 0.4256902039051056}]}, "Id": "43364c00-8c0c-4af8-8ebc-eba830147d54"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036091309040784836, "Height": 0.008014409802854061, "Left": 0.21280618011951447, "Top": 0.41898638010025024}, "Polygon": [{"X": 0.21280623972415924, "Y": 0.4189869165420532}, {"X": 0.2488974928855896, "Y": 0.41898638010025024}, {"X": 0.24889743328094482, "Y": 0.4270002543926239}, {"X": 0.21280618011951447, "Y": 0.4270007908344269}]}, "Id": "cad5a82a-fa4c-4aac-8123-c6657ecb3913"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023578299209475517, "Height": 0.006388806272298098, "Left": 0.253194659948349, "Top": 0.419284462928772}, "Polygon": [{"X": 0.2531947195529938, "Y": 0.41928479075431824}, {"X": 0.27677297592163086, "Y": 0.419284462928772}, {"X": 0.2767729163169861, "Y": 0.4256729185581207}, {"X": 0.253194659948349, "Y": 0.425673246383667}]}, "Id": "63475d3e-b0dc-4247-b7c9-e31e4f2648bd"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "HU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "0925f427-8a7a-46ad-ad52-55851896dbc7"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027246743440628052, "Height": 0.007028465624898672, "Left": 0.19829589128494263, "Top": 0.4539552330970764}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45395565032958984}, {"X": 0.22554263472557068, "Y": 0.4539552330970764}, {"X": 0.2255425900220871, "Y": 0.4609832763671875}, {"X": 0.19829589128494263, "Y": 0.4609836935997009}]}, "Id": "d5ad37f8-de42-45a3-9dd4-31559f0cd2cd"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02633608691394329, "Height": 0.007158621214330196, "Left": 0.2319745570421219, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.23197461664676666, "Y": 0.4538812041282654}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.2319745570421219, "Y": 0.4610394239425659}]}, "Id": "1fc8a4ea-58eb-4a5c-91d0-29a6b7aaf59f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "eda01ca0-4c97-42ed-8d22-6f999e1d957c"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040659863501787186, "Height": 0.009368103928864002, "Left": 0.3337138891220093, "Top": 0.4535251557826996}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.4535257816314697}, {"X": 0.37437376379966736, "Y": 0.4535251557826996}, {"X": 0.3743736743927002, "Y": 0.4628926217556}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "e7db72cf-f1ec-46c2-9a40-7adc09eaf7a0"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0507369227707386, "Height": 0.007401087787002325, "Left": 0.3780301809310913, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.3780302405357361, "Y": 0.4535139203071594}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876702547073364, "Y": 0.46091344952583313}, {"X": 0.3780301809310913, "Y": 0.4609142243862152}]}, "Id": "bdf913a5-7d02-47ff-9830-7d3fd0d20951"}, {"BlockType": "WORD", "Confidence": 99.7217788696289, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01893853209912777, "Height": 0.007110124453902245, "Left": 0.46933484077453613, "Top": 0.45394328236579895}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.4539435803890228}, {"X": 0.48827338218688965, "Y": 0.45394328236579895}, {"X": 0.4882732927799225, "Y": 0.46105310320854187}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "89e40596-a7d1-45d0-8b48-0bf2deb9b3d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044754479080438614, "Height": 0.00717869633808732, "Left": 0.4923553168773651, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4923553764820099, "Y": 0.4536542296409607}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.46083155274391174}, {"X": 0.4923553168773651, "Y": 0.46083223819732666}]}, "Id": "c6764da8-2ba7-4695-8bc1-b411953293e2"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "33136199", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "c77d73aa-4a80-4039-92e0-8104e5dda6af"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "f1fac649-6c59-46f0-be1d-fd907ccc50ba"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03399926424026489, "Height": 0.007215920835733414, "Left": 0.3340480327606201, "Top": 0.4650706946849823}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.368047297000885, "Y": 0.4650706946849823}, {"X": 0.36804723739624023, "Y": 0.4722861051559448}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "0c0cea16-ccac-48c9-8e9a-2c4a3d3ea70b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "880", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02212643437087536, "Height": 0.006782433483749628, "Left": 0.3790387511253357, "Top": 0.46533575654029846}, "Polygon": [{"X": 0.37903881072998047, "Y": 0.4653360843658447}, {"X": 0.4011651873588562, "Y": 0.46533575654029846}, {"X": 0.4011651277542114, "Y": 0.4721178412437439}, {"X": 0.3790387511253357, "Y": 0.47211816906929016}]}, "Id": "a5ebb5d6-c2e3-4955-b897-8782370e9428"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "0b7b1b23-8994-401c-aee5-74a9bae9d41a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "a3148665-38b4-4550-9334-83645bdbd502"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "33136200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "c0f8fc78-db07-438d-b3e7-46826e757813"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "ab8eb615-aba9-46bd-9ee0-9599227bb13b"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "b1978ee6-9ca9-49ca-b8ce-7d78ac920234"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "049efd94-db41-40bd-b9fc-fc885c0bbbcf"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "bb4dc24f-458e-4fd6-b3d1-9ccdfacfe61d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "8f0d9fdd-f05d-43d9-b197-cd71ecef6454"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "3cd20075-ed98-4e9e-b0c1-cd998b649f06"}, {"BlockType": "WORD", "Confidence": 99.68511199951172, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "324d0501-43c8-4422-ba51-505b53b168c2"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "a2cb18e4-146f-488e-b589-329fa02038ae"}, {"BlockType": "WORD", "Confidence": 99.71281433105469, "Text": "6", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "82eca2be-2b3f-4587-81ff-400fd48c08a2"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "7", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "f1a22762-748a-4b38-9073-b4c0da55554b"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "bef46dc1-4fd8-4aa5-91b9-909467bb25b1"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "0ef88f35-a13e-450b-815b-531ba2e50bfd"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "0abb7a82-b132-4bfb-8b43-3b06a4b56e7b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "d2d10a35-e66a-476c-810f-2ced6183baba"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "5d0631b6-0eaf-4216-a673-4a4c145b2a2c"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030416401103138924, "Height": 0.010070300661027431, "Left": 0.8169759511947632, "Top": 0.9619089365005493}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8473923802375793, "Y": 0.9619089365005493}, {"X": 0.847392201423645, "Y": 0.9719785451889038}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "250fd587-9c09-4bde-b30a-2500ef08cdb5"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "3/3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01798633299767971, "Height": 0.008184125646948814, "Left": 0.8512584567070007, "Top": 0.9621924757957458}, "Polygon": [{"X": 0.8512585759162903, "Y": 0.9621928930282593}, {"X": 0.8692447543144226, "Y": 0.9621924757957458}, {"X": 0.8692446351051331, "Y": 0.970376193523407}, {"X": 0.8512584567070007, "Y": 0.9703766107559204}]}, "Id": "083d245f-e50e-48a2-b021-9e2dc380c6f4"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "f6c5afc9-f275-48e0-b2d7-137f157fc6e9", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "f6c5afc9-f275-48e0-b2d7-137f157fc6e9", "content-type": "application/x-amz-json-1.1", "content-length": "78383", "date": "Wed, 24 Sep 2025 09:55:16 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "42aaf444-1564-49c1-ad3c-9a0a696f362a", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:55:20 GMT", "content-type": "application/json", "content-length": "2362", "connection": "keep-alive", "x-amzn-requestid": "42aaf444-1564-49c1-ad3c-9a0a696f362a"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page, but the content indicates \"Weight & Inspection\", \"CERTIFIED WEIGHT CERTIFICATE\", \"Weight Certificate\", and \"Page 3/3\". So it's a multi-page document? The content shows \"Page 3/3\" at the bottom. It likely continues previous pages not shown. But the instruction: if page starts with \"continued\" or pagination like \"page 2 of 3\", etc., we treat as continuation and assign same doc_type as previous page. But we have only this page. There's no previous page in the input; we assume it's the first page? Wait the page shows \"Page 3/3\". That indicates page 3 of 3. That suggests this is the last page. But we have no content from pages 1 and 2. But we must still classify this page. It likely is part of a multi-page document. We should assign doc_type based on the content. Keywords: \"Weight & Inspection\", \"CERTIFIED WEIGHT CERTIFICATE\", etc. The doc_type is weight_and_inspection_cert, or possibly nmfc_cert if it includes NMFC keywords. The text shows no NMFC mention. So weight_and_inspection_cert.\n\nBut we must check if it's a continuation: page shows \"Page 3/3\". That indicates it's a multi-page doc. But we have no previous page. So we cannot assign previous doc_type. But we can still classify this page as weight_and_inspection_cert. There's no \"continued\" on top. So we proceed with that.\n\nWe need to use the tool classify_logistics_doc_type with documents array: each object includes page_no and doc_type. Since only one page, page_no 1? Wait we may treat page number as the actual page number in the PDF. The content says \"Page 3/3\". But the input says <page1> which indicates it's the first page of our extraction? The user gave <page1> with content. That might be page 1. Even though content says Page 3/3. But we should match the input's page number: it's page 1. So we output page_no: 1, doc_type: weight_and_inspection_cert.\n\nThus call function with array of one object.\n\n"}}}, {"toolUse": {"toolUseId": "tooluse_o8Mh9t5PTAaBHEdQUCoA5A", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2685, "outputTokens": 483, "totalTokens": 3168}, "metrics": {"latencyMs": 2225}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/092b7638_DFY1VDZWR7NBDLJV02G2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCentral Transport\n08/31/2025\nPRO#: 227407699\nWeight & Inspection\nCENTRAL TRANSPORT\n12225 Stephens Road\nWarren, MI 48089\nCERTIFIED WEIGHT CERTIFICATE\nPro Number: 227407699\nBill of Lading\nDate: 08/26/2025\nDIAMOND ENVELOPE\nWeight Certificate\n2270 WHITE OAK CIR\nWe Certify that this shipment\nAURORA, IL 60502\nhas been reweighed and that\nthe corrected weight as stated\nis the true and accurate\nDeclared Weight: 1148\nweight of the shipment\nCorrected Weight: 1425\nHU\nScan Date\nTime\nWeight Terminal\nPro Number\n33136199\n08/26/2025\n00:02 880\n463\n227407699\n33136200\n08/26/2025\n00:03\n545\n463\n227407699\n3\n4\n5\n6\n7\n8\n9\n10\nTotal\n1425\nPage 3/3\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}