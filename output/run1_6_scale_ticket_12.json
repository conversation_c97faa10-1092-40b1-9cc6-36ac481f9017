{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9766510725021362, "Height": 0.8728974461555481, "Left": 0.007446985691785812, "Top": 0.12311111390590668}, "Polygon": [{"X": 0.007446985691785812, "Y": 0.12311111390590668}, {"X": 0.9840980768203735, "Y": 0.12478368729352951}, {"X": 0.9484036564826965, "Y": 0.994350790977478}, {"X": 0.07567737251520157, "Y": 0.996008574962616}]}, "Id": "e49730b9-1a32-4b33-abab-807cfe07c172", "Relationships": [{"Type": "CHILD", "Ids": ["863c2ad7-3609-49d5-84ae-68feb2c0cb1e", "520ceb82-3413-4225-9755-a2c2a98d0e33", "ab63a020-2651-48fb-8ff6-849b1ae1d89d", "51c59400-e9fb-4af2-bb80-c3f2c1a8b113", "9570fa0e-6b29-49c0-8274-361fe8a344ba", "cba77f81-3804-4863-9a88-82c050a805a6", "297f7771-f8c7-45c0-b6fd-f2450bd02000", "54534e78-9bbb-4e4e-82f7-f17339ec22a3", "dbae27c3-235d-4ef0-bc5b-453b8c1d2909", "9a3ac6e2-fde0-45b4-927a-5ea01104f35f", "1f110c33-60c3-410a-ae0c-b7101ab646fe", "3611dc0e-ff28-4d76-9eff-b72969587319", "7cbb41b8-6f30-4802-8c75-4100e0263f7a", "ef513507-52de-44c6-974e-eab5177390d7", "07551c12-4c89-47a1-b027-ed5000ac8829", "7f67b48b-e119-4e97-a469-1df801d38577", "c543e1dc-5d4a-422e-8a99-1e7722f5adac", "89277a24-74d8-4d07-a7ad-5c1b7ceab0c5", "49ab9368-598a-4840-b2e2-938aaaaa9a98", "b2764422-cebb-4558-bae4-7576d29bbc00", "980d7589-db03-4cff-98b0-ff46332220fd", "9010c1d2-286f-46cf-a9d2-3f9a7cc895e4", "c9826c1c-c49b-4ad9-a6ec-1d0e83753661", "9b4f37c3-40d7-425f-9986-21d7a652c1ab", "7e36f1b6-3745-4832-9c3c-b2df2b7468f0", "b6fc96d7-7f1b-448d-a9c6-b2c46063441c", "b4473684-1c8b-43d9-94cf-8351dcfb2e61", "fdb364bd-39c9-4654-b5c9-9a1f8102e044", "6a6990f9-9eb9-40ce-8690-500e789ce60f", "f632da9d-3dca-48c8-a9b2-3e50dee631cf", "fe895aab-8f1a-4e96-8089-89ca6775659e", "5213388c-2736-4d51-b0fa-9fc8cc748ee7"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "ND Paper", "Geometry": {"BoundingBox": {"Width": 0.08518323302268982, "Height": 0.015519311651587486, "Left": 0.45058363676071167, "Top": 0.1604292094707489}, "Polygon": [{"X": 0.45058363676071167, "Y": 0.1604292094707489}, {"X": 0.5355550050735474, "Y": 0.16056185960769653}, {"X": 0.5357668995857239, "Y": 0.17594853043556213}, {"X": 0.45095595717430115, "Y": 0.17582154273986816}]}, "Id": "863c2ad7-3609-49d5-84ae-68feb2c0cb1e", "Relationships": [{"Type": "CHILD", "Ids": ["bc2a3b9a-eb2b-461f-9216-19e24cc7932c", "49c3f045-e442-43d5-9776-de36d82d42a8"]}]}, {"BlockType": "LINE", "Confidence": 99.423828125, "Text": "ND", "Geometry": {"BoundingBox": {"Width": 0.02990260347723961, "Height": 0.013275125995278358, "Left": 0.7404574155807495, "Top": 0.16789719462394714}, "Polygon": [{"X": 0.7406080365180969, "Y": 0.16789719462394714}, {"X": 0.770359992980957, "Y": 0.16794276237487793}, {"X": 0.7701610922813416, "Y": 0.18117231130599976}, {"X": 0.7404574155807495, "Y": 0.18112844228744507}]}, "Id": "520ceb82-3413-4225-9755-a2c2a98d0e33", "Relationships": [{"Type": "CHILD", "Ids": ["8dcfb9ff-0821-437b-8f01-d659bcb0ef49"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "ND PAPER", "Geometry": {"BoundingBox": {"Width": 0.1480330228805542, "Height": 0.02084861882030964, "Left": 0.7906321883201599, "Top": 0.16887229681015015}, "Polygon": [{"X": 0.7909947037696838, "Y": 0.16887229681015015}, {"X": 0.9386651515960693, "Y": 0.16909795999526978}, {"X": 0.9379290342330933, "Y": 0.18972091376781464}, {"X": 0.7906321883201599, "Y": 0.18950843811035156}]}, "Id": "ab63a020-2651-48fb-8ff6-849b1ae1d89d", "Relationships": [{"Type": "CHILD", "Ids": ["1a7a0897-d553-49f5-82d6-1edfbaeee53f", "561d69b8-fd05-460f-b904-0fc6f0f4361c"]}]}, {"BlockType": "LINE", "Confidence": 99.81365966796875, "Text": "Biron Scale(Biron)", "Geometry": {"BoundingBox": {"Width": 0.16410256922245026, "Height": 0.01594742015004158, "Left": 0.4102652966976166, "Top": 0.18389669060707092}, "Polygon": [{"X": 0.4102652966976166, "Y": 0.18389669060707092}, {"X": 0.574225664138794, "Y": 0.18413664400577545}, {"X": 0.574367880821228, "Y": 0.1998441070318222}, {"X": 0.41072455048561096, "Y": 0.19961528480052948}]}, "Id": "51c59400-e9fb-4af2-bb80-c3f2c1a8b113", "Relationships": [{"Type": "CHILD", "Ids": ["8a0249e6-3af3-47b0-ac0e-e283c77c6579", "f3e3b3d6-2ae7-4685-b444-cce8495b6a63"]}]}, {"BlockType": "LINE", "Confidence": 99.65182495117188, "Text": "29237", "Geometry": {"BoundingBox": {"Width": 0.052346121519804, "Height": 0.010391316376626492, "Left": 0.09444499015808105, "Top": 0.2069253921508789}, "Polygon": [{"X": 0.09444499015808105, "Y": 0.2069253921508789}, {"X": 0.1461525857448578, "Y": 0.20699603855609894}, {"X": 0.14679111540317535, "Y": 0.21731671690940857}, {"X": 0.09514947980642319, "Y": 0.2172483652830124}]}, "Id": "9570fa0e-6b29-49c0-8274-361fe8a344ba", "Relationships": [{"Type": "CHILD", "Ids": ["ac1b66f0-fef6-40a5-8c63-62c13c1ba506"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Source", "Geometry": {"BoundingBox": {"Width": 0.05155888944864273, "Height": 0.010144300758838654, "Left": 0.48987117409706116, "Top": 0.21292546391487122}, "Polygon": [{"X": 0.48987117409706116, "Y": 0.21292546391487122}, {"X": 0.5412975549697876, "Y": 0.21299457550048828}, {"X": 0.5414300560951233, "Y": 0.22306977212429047}, {"X": 0.49006763100624084, "Y": 0.22300291061401367}]}, "Id": "cba77f81-3804-4863-9a88-82c050a805a6", "Relationships": [{"Type": "CHILD", "Ids": ["e446fa6c-b1ef-43f6-8d43-ed9b6eaf68d5"]}]}, {"BlockType": "LINE", "Confidence": 95.44319152832031, "Text": "VIM_AURO_IL_RATHBONE(VIM RECYCLERS L P)", "Geometry": {"BoundingBox": {"Width": 0.36873096227645874, "Height": 0.013234325684607029, "Left": 0.5853182673454285, "Top": 0.2123555988073349}, "Polygon": [{"X": 0.5853182673454285, "Y": 0.2123555988073349}, {"X": 0.9540492296218872, "Y": 0.2128521054983139}, {"X": 0.9535679221153259, "Y": 0.22558991611003876}, {"X": 0.5854167342185974, "Y": 0.2251136749982834}]}, "Id": "297f7771-f8c7-45c0-b6fd-f2450bd02000", "Relationships": [{"Type": "CHILD", "Ids": ["594926ed-6071-4f52-8c4a-dc8883526a3f", "01fc7d2b-e0ee-47d8-a10a-7ef46309217b", "330c182b-9565-4afb-8d76-042de53384df", "9bad2606-a5f2-4b33-81f3-1fc56a02bc44"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Contract", "Geometry": {"BoundingBox": {"Width": 0.06782763451337814, "Height": 0.010410954244434834, "Left": 0.05415438115596771, "Top": 0.2281905561685562}, "Polygon": [{"X": 0.05415438115596771, "Y": 0.2281905561685562}, {"X": 0.12130981683731079, "Y": 0.22827637195587158}, {"X": 0.12198201566934586, "Y": 0.23860150575637817}, {"X": 0.05491252616047859, "Y": 0.23851865530014038}]}, "Id": "54534e78-9bbb-4e4e-82f7-f17339ec22a3", "Relationships": [{"Type": "CHILD", "Ids": ["cd22f923-780d-4129-b518-b22dd773937c"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "018539 (HARVESTER)", "Geometry": {"BoundingBox": {"Width": 0.16440071165561676, "Height": 0.013715875335037708, "Left": 0.17100811004638672, "Top": 0.22856122255325317}, "Polygon": [{"X": 0.17100811004638672, "Y": 0.22856122255325317}, {"X": 0.3348861336708069, "Y": 0.22877050936222076}, {"X": 0.3354088366031647, "Y": 0.2422771006822586}, {"X": 0.1718050241470337, "Y": 0.2420773059129715}]}, "Id": "dbae27c3-235d-4ef0-bc5b-453b8c1d2909", "Relationships": [{"Type": "CHILD", "Ids": ["978a01dd-588a-4c70-a38d-3fe8cec8e016", "abbc59d5-bb68-415a-afc4-c2d707f3e354"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Destination", "Geometry": {"BoundingBox": {"Width": 0.08242760598659515, "Height": 0.010222110897302628, "Left": 0.4900279939174652, "Top": 0.23050884902477264}, "Polygon": [{"X": 0.4900279939174652, "Y": 0.23050884902477264}, {"X": 0.5723611116409302, "Y": 0.23061347007751465}, {"X": 0.5724555850028992, "Y": 0.24073095619678497}, {"X": 0.49022555351257324, "Y": 0.2406299114227295}]}, "Id": "9a3ac6e2-fde0-45b4-927a-5ea01104f35f", "Relationships": [{"Type": "CHILD", "Ids": ["b2f1ac02-f8ce-4c15-8b55-38a767c477a6"]}]}, {"BlockType": "LINE", "Confidence": 99.04546356201172, "Text": "BIRON(BIRON MILL)", "Geometry": {"BoundingBox": {"Width": 0.14656004309654236, "Height": 0.012445851229131222, "Left": 0.5865309238433838, "Top": 0.23018206655979156}, "Polygon": [{"X": 0.5865309238433838, "Y": 0.23018206655979156}, {"X": 0.7330909967422485, "Y": 0.23036858439445496}, {"X": 0.7329617738723755, "Y": 0.24262791872024536}, {"X": 0.5866239666938782, "Y": 0.24244913458824158}]}, "Id": "1f110c33-60c3-410a-ae0c-b7101ab646fe", "Relationships": [{"Type": "CHILD", "Ids": ["55d3a188-4f90-446f-90ce-55f9729026e5", "de2b3dc9-479e-4bb8-af9f-cc6b84052ba3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.04372882843017578, "Height": 0.009893613867461681, "Left": 0.4899614155292511, "Top": 0.264846533536911}, "Polygon": [{"X": 0.4899614155292511, "Y": 0.264846533536911}, {"X": 0.533550500869751, "Y": 0.2648957371711731}, {"X": 0.5336902737617493, "Y": 0.27474015951156616}, {"X": 0.49015453457832336, "Y": 0.2746928036212921}]}, "Id": "3611dc0e-ff28-4d76-9eff-b72969587319", "Relationships": [{"Type": "CHILD", "Ids": ["702ecbcf-5c32-4354-8340-90fe8ccbcb21"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "ZACH", "Geometry": {"BoundingBox": {"Width": 0.041206926107406616, "Height": 0.009904018603265285, "Left": 0.5842521786689758, "Top": 0.2646086812019348}, "Polygon": [{"X": 0.5842521786689758, "Y": 0.2646086812019348}, {"X": 0.6254316568374634, "Y": 0.264655202627182}, {"X": 0.6254591345787048, "Y": 0.27451270818710327}, {"X": 0.5843300819396973, "Y": 0.27446791529655457}]}, "Id": "7cbb41b8-6f30-4802-8c75-4100e0263f7a", "Relationships": [{"Type": "CHILD", "Ids": ["1b1557e4-0a1e-4eb8-87b4-fbe23496c46a"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Trailer 25", "Geometry": {"BoundingBox": {"Width": 0.08767609298229218, "Height": 0.010436716489493847, "Left": 0.7972438335418701, "Top": 0.26532596349716187}, "Polygon": [{"X": 0.797435998916626, "Y": 0.26532596349716187}, {"X": 0.8849199414253235, "Y": 0.2654246389865875}, {"X": 0.8846154808998108, "Y": 0.275762677192688}, {"X": 0.7972438335418701, "Y": 0.2756679058074951}]}, "Id": "ef513507-52de-44c6-974e-eab5177390d7", "Relationships": [{"Type": "CHILD", "Ids": ["07a3ea70-7507-46ea-a509-1b5c43f1b8c7", "486b9bc9-87cc-4b6e-adf6-9ab2cd594fec"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "BOL", "Geometry": {"BoundingBox": {"Width": 0.03428299352526665, "Height": 0.009871742688119411, "Left": 0.061953410506248474, "Top": 0.28120702505111694}, "Polygon": [{"X": 0.061953410506248474, "Y": 0.28120702505111694}, {"X": 0.09556028991937637, "Y": 0.28124257922172546}, {"X": 0.09623640030622482, "Y": 0.29107874631881714}, {"X": 0.0626707598567009, "Y": 0.29104459285736084}]}, "Id": "07551c12-4c89-47a1-b027-ed5000ac8829", "Relationships": [{"Type": "CHILD", "Ids": ["b95d6a44-914b-4a06-bbb9-c8e31fbbd223"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "Trip Ticket BIR0029913", "Geometry": {"BoundingBox": {"Width": 0.18440625071525574, "Height": 0.012281139381229877, "Left": 0.0632418617606163, "Top": 0.2997751832008362}, "Polygon": [{"X": 0.0632418617606163, "Y": 0.2997751832008362}, {"X": 0.24704307317733765, "Y": 0.29995566606521606}, {"X": 0.24764810502529144, "Y": 0.3120563328266144}, {"X": 0.06412503123283386, "Y": 0.3118853271007538}]}, "Id": "7f67b48b-e119-4e97-a469-1df801d38577", "Relationships": [{"Type": "CHILD", "Ids": ["a6864da5-8671-45e4-bdd2-723e0ce840ed", "91b88152-b864-4fe4-a516-fededb810a18", "61e5dadf-b284-4260-b969-e5d6592e4491"]}]}, {"BlockType": "LINE", "Confidence": 99.69567108154297, "Text": "Product OCC 11D", "Geometry": {"BoundingBox": {"Width": 0.1493159383535385, "Height": 0.010122493840754032, "Left": 0.48711439967155457, "Top": 0.30141153931617737}, "Polygon": [{"X": 0.48711439967155457, "Y": 0.30141153931617737}, {"X": 0.6364160776138306, "Y": 0.30155736207962036}, {"X": 0.6364303231239319, "Y": 0.31153401732444763}, {"X": 0.4873146116733551, "Y": 0.3113945424556732}]}, "Id": "c543e1dc-5d4a-422e-8a99-1e7722f5adac", "Relationships": [{"Type": "CHILD", "Ids": ["281181fa-4de4-4c3e-9539-a6751e5dfa4f", "e389f052-7238-4dfa-9788-e73fc8e10b61", "4fa27978-8d77-4f82-bf02-b5f36e467888"]}]}, {"BlockType": "LINE", "Confidence": 99.7325439453125, "Text": "Load # BIR-29237", "Geometry": {"BoundingBox": {"Width": 0.17965318262577057, "Height": 0.01046895980834961, "Left": 0.06639739871025085, "Top": 0.31877580285072327}, "Polygon": [{"X": 0.06639739871025085, "Y": 0.31877580285072327}, {"X": 0.24553203582763672, "Y": 0.31893759965896606}, {"X": 0.24605058133602142, "Y": 0.3292447626590729}, {"X": 0.06714736670255661, "Y": 0.32909080386161804}]}, "Id": "89277a24-74d8-4d07-a7ad-5c1b7ceab0c5", "Relationships": [{"Type": "CHILD", "Ids": ["1c934792-6425-4f0f-aef1-fb59b159cb09", "183fc869-eee1-4a9a-8ba8-42613fc52a8a", "ed90b4de-5579-49af-bc83-683bbba08175"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Tons", "Geometry": {"BoundingBox": {"Width": 0.03704778477549553, "Height": 0.008786933496594429, "Left": 0.19690771400928497, "Top": 0.41303563117980957}, "Polygon": [{"X": 0.19690771400928497, "Y": 0.41303563117980957}, {"X": 0.2334957867860794, "Y": 0.4130544066429138}, {"X": 0.2339555025100708, "Y": 0.42182254791259766}, {"X": 0.19740809500217438, "Y": 0.42180511355400085}]}, "Id": "49ab9368-598a-4840-b2e2-938aaaaa9a98", "Relationships": [{"Type": "CHILD", "Ids": ["1cb682de-9502-44e5-816f-53e0cea9da74"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "LB", "Geometry": {"BoundingBox": {"Width": 0.01923038810491562, "Height": 0.008599769324064255, "Left": 0.28898170590400696, "Top": 0.412490576505661}, "Polygon": [{"X": 0.28898170590400696, "Y": 0.412490576505661}, {"X": 0.30784255266189575, "Y": 0.4125002920627594}, {"X": 0.3082121014595032, "Y": 0.42109033465385437}, {"X": 0.2893717885017395, "Y": 0.4210813045501709}]}, "Id": "b2764422-cebb-4558-bae4-7576d29bbc00", "Relationships": [{"Type": "CHILD", "Ids": ["ea932908-62d3-490f-8055-14360839f4a3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Gross", "Geometry": {"BoundingBox": {"Width": 0.044618599116802216, "Height": 0.009044081903994083, "Left": 0.0799609050154686, "Top": 0.42931294441223145}, "Polygon": [{"X": 0.0799609050154686, "Y": 0.42931294441223145}, {"X": 0.12398006021976471, "Y": 0.42933258414268494}, {"X": 0.12457950413227081, "Y": 0.43835702538490295}, {"X": 0.08061084151268005, "Y": 0.43833908438682556}]}, "Id": "980d7589-db03-4cff-98b0-ff46332220fd", "Relationships": [{"Type": "CHILD", "Ids": ["8668eb8c-8b0d-44a0-b6fc-f9992a555b03"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "35.84", "Geometry": {"BoundingBox": {"Width": 0.037736766040325165, "Height": 0.009339381940662861, "Left": 0.19664505124092102, "Top": 0.4276314675807953}, "Polygon": [{"X": 0.19664505124092102, "Y": 0.4276314675807953}, {"X": 0.23389260470867157, "Y": 0.4276483356952667}, {"X": 0.23438182473182678, "Y": 0.4369708299636841}, {"X": 0.19717837870121002, "Y": 0.43695542216300964}]}, "Id": "9010c1d2-286f-46cf-a9d2-3f9a7cc895e4", "Relationships": [{"Type": "CHILD", "Ids": ["fa8e9bcd-1945-41eb-8baa-be5523072519"]}]}, {"BlockType": "LINE", "Confidence": 99.43219757080078, "Text": "71,680", "Geometry": {"BoundingBox": {"Width": 0.04509070888161659, "Height": 0.010107634589076042, "Left": 0.26583510637283325, "Top": 0.42668265104293823}, "Polygon": [{"X": 0.26583510637283325, "Y": 0.42668265104293823}, {"X": 0.31049445271492004, "Y": 0.4267030656337738}, {"X": 0.31092581152915955, "Y": 0.4367902874946594}, {"X": 0.26632362604141235, "Y": 0.43677178025245667}]}, "Id": "c9826c1c-c49b-4ad9-a6ec-1d0e83753661", "Relationships": [{"Type": "CHILD", "Ids": ["4011a2fd-a390-4bfb-8e76-21c44221858e"]}]}, {"BlockType": "LINE", "Confidence": 99.75042724609375, "Text": "Date In 11/12/22 5:04:25", "Geometry": {"BoundingBox": {"Width": 0.1774349957704544, "Height": 0.009217634797096252, "Left": 0.33633458614349365, "Top": 0.4265131652355194}, "Polygon": [{"X": 0.33633458614349365, "Y": 0.4265131652355194}, {"X": 0.5136141180992126, "Y": 0.4265943467617035}, {"X": 0.5137695670127869, "Y": 0.43573081493377686}, {"X": 0.3366956114768982, "Y": 0.43565642833709717}]}, "Id": "9b4f37c3-40d7-425f-9986-21d7a652c1ab", "Relationships": [{"Type": "CHILD", "Ids": ["db16a7b7-7252-4e77-9b83-4e2259e951a9", "43a63fa4-9b1e-4684-92a7-a50d7ff17596", "15d360c6-ccab-46c7-85db-27944a6b683d", "d6120988-a704-4309-bc85-74491f5b4334"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.033447351306676865, "Height": 0.00884980522096157, "Left": 0.08127229660749435, "Top": 0.4434557855129242}, "Polygon": [{"X": 0.08127229660749435, "Y": 0.4434557855129242}, {"X": 0.11412052810192108, "Y": 0.44346851110458374}, {"X": 0.11471964418888092, "Y": 0.4523055851459503}, {"X": 0.08190837502479553, "Y": 0.45229408144950867}]}, "Id": "7e36f1b6-3745-4832-9c3c-b2df2b7468f0", "Relationships": [{"Type": "CHILD", "Ids": ["6ce5334d-e359-4a98-9d28-e4fd838fbc17"]}]}, {"BlockType": "LINE", "Confidence": 98.61547088623047, "Text": "14.73", "Geometry": {"BoundingBox": {"Width": 0.036457352340221405, "Height": 0.009038951247930527, "Left": 0.19872742891311646, "Top": 0.4415554106235504}, "Polygon": [{"X": 0.19872742891311646, "Y": 0.4415554106235504}, {"X": 0.23471128940582275, "Y": 0.44156962633132935}, {"X": 0.23518477380275726, "Y": 0.45059436559677124}, {"X": 0.1992422342300415, "Y": 0.45058149099349976}]}, "Id": "b6fc96d7-7f1b-448d-a9c6-b2c46063441c", "Relationships": [{"Type": "CHILD", "Ids": ["0dbba2df-bf7e-4ca6-90b1-7e557b0c1a1b"]}]}, {"BlockType": "LINE", "Confidence": 99.4834213256836, "Text": "29,460", "Geometry": {"BoundingBox": {"Width": 0.04507197067141533, "Height": 0.010247650556266308, "Left": 0.26654961705207825, "Top": 0.4401445686817169}, "Polygon": [{"X": 0.26654961705207825, "Y": 0.4401445686817169}, {"X": 0.3111843168735504, "Y": 0.4401624798774719}, {"X": 0.31162160634994507, "Y": 0.4503922164440155}, {"X": 0.2670449912548065, "Y": 0.4503762125968933}]}, "Id": "b4473684-1c8b-43d9-94cf-8351dcfb2e61", "Relationships": [{"Type": "CHILD", "Ids": ["b5f6698b-615b-48e9-89fe-b280c3e1ce1f"]}]}, {"BlockType": "LINE", "Confidence": 99.64300537109375, "Text": "Date Out 11/12/22 6:01:11", "Geometry": {"BoundingBox": {"Width": 0.17491741478443146, "Height": 0.009114904329180717, "Left": 0.3371296525001526, "Top": 0.44093209505081177}, "Polygon": [{"X": 0.3371296525001526, "Y": 0.44093209505081177}, {"X": 0.5118908882141113, "Y": 0.44100168347358704}, {"X": 0.5120470523834229, "Y": 0.45004701614379883}, {"X": 0.33748680353164673, "Y": 0.449984073638916}]}, "Id": "fdb364bd-39c9-4654-b5c9-9a1f8102e044", "Relationships": [{"Type": "CHILD", "Ids": ["dab757cf-6f84-4c42-97ab-d83be56da9c7", "ded84ea7-5cf3-4f29-97e7-365b86d6a991", "83fd8a7e-0d14-4f4e-a05d-83eb2d4f534f", "b55f84d0-834b-4de3-8527-4248ee8cd11b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Net", "Geometry": {"BoundingBox": {"Width": 0.025261199101805687, "Height": 0.008836966007947922, "Left": 0.08513647317886353, "Top": 0.45781683921813965}, "Polygon": [{"X": 0.08513647317886353, "Y": 0.45781683921813965}, {"X": 0.10979314893484116, "Y": 0.4578249156475067}, {"X": 0.11039766669273376, "Y": 0.46665382385253906}, {"X": 0.08576875925064087, "Y": 0.46664664149284363}]}, "Id": "6a6990f9-9eb9-40ce-8690-500e789ce60f", "Relationships": [{"Type": "CHILD", "Ids": ["68d2a972-a8e7-4337-9222-7b31e36556ca"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "21.11", "Geometry": {"BoundingBox": {"Width": 0.03577348217368126, "Height": 0.00929650291800499, "Left": 0.19888412952423096, "Top": 0.4557640850543976}, "Polygon": [{"X": 0.19888412952423096, "Y": 0.4557640850543976}, {"X": 0.23416896164417267, "Y": 0.4557759463787079}, {"X": 0.23465761542320251, "Y": 0.4650605618953705}, {"X": 0.19941453635692596, "Y": 0.46505007147789}]}, "Id": "f632da9d-3dca-48c8-a9b2-3e50dee631cf", "Relationships": [{"Type": "CHILD", "Ids": ["fbfd119e-e920-4dd7-adbd-c17660395365"]}]}, {"BlockType": "LINE", "Confidence": 98.37631225585938, "Text": "42,220", "Geometry": {"BoundingBox": {"Width": 0.04485674947500229, "Height": 0.010043726302683353, "Left": 0.2685806453227997, "Top": 0.4542286694049835}, "Polygon": [{"X": 0.2685806453227997, "Y": 0.4542286694049835}, {"X": 0.3130102753639221, "Y": 0.45424389839172363}, {"X": 0.3134373724460602, "Y": 0.4642724096775055}, {"X": 0.2690645158290863, "Y": 0.4642590284347534}]}, "Id": "fe895aab-8f1a-4e96-8089-89ca6775659e", "Relationships": [{"Type": "CHILD", "Ids": ["4972206c-4823-4042-917d-4a54287988c7"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Buyer: 018539", "Geometry": {"BoundingBox": {"Width": 0.10016725212335587, "Height": 0.012063593603670597, "Left": 0.12300576269626617, "Top": 0.9131163954734802}, "Polygon": [{"X": 0.12300576269626617, "Y": 0.9132713079452515}, {"X": 0.22248902916908264, "Y": 0.9131163954734802}, {"X": 0.22317302227020264, "Y": 0.9250203967094421}, {"X": 0.12385006994009018, "Y": 0.9251799583435059}]}, "Id": "5213388c-2736-4d51-b0fa-9fc8cc748ee7", "Relationships": [{"Type": "CHILD", "Ids": ["a0d6f738-9e78-4839-a6f8-e44de5978bc3", "6778986d-9ee6-40c9-86b9-4050b3da8c30"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026659179478883743, "Height": 0.01222879346460104, "Left": 0.45058363676071167, "Top": 0.1604292094707489}, "Polygon": [{"X": 0.45058363676071167, "Y": 0.1604292094707489}, {"X": 0.47698748111724854, "Y": 0.16047042608261108}, {"X": 0.4772428274154663, "Y": 0.1726580113172531}, {"X": 0.45087847113609314, "Y": 0.17261818051338196}]}, "Id": "bc2a3b9a-eb2b-461f-9216-19e24cc7932c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Paper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05250462144613266, "Height": 0.015348393470048904, "Left": 0.48326224088668823, "Top": 0.16060012578964233}, "Polygon": [{"X": 0.48326224088668823, "Y": 0.16060012578964233}, {"X": 0.5355566740036011, "Y": 0.16068173944950104}, {"X": 0.5357668995857239, "Y": 0.17594853043556213}, {"X": 0.4835704267024994, "Y": 0.1758703738451004}]}, "Id": "49c3f045-e442-43d5-9776-de36d82d42a8"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02990260347723961, "Height": 0.013275125995278358, "Left": 0.7404574155807495, "Top": 0.16789719462394714}, "Polygon": [{"X": 0.7406080365180969, "Y": 0.16789719462394714}, {"X": 0.770359992980957, "Y": 0.16794276237487793}, {"X": 0.7701610922813416, "Y": 0.18117231130599976}, {"X": 0.7404574155807495, "Y": 0.18112844228744507}]}, "Id": "8dcfb9ff-0821-437b-8f01-d659bcb0ef49"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04538422077894211, "Height": 0.020492563024163246, "Left": 0.7906321883201599, "Top": 0.16908065974712372}, "Polygon": [{"X": 0.7909910678863525, "Y": 0.16908065974712372}, {"X": 0.8360163569450378, "Y": 0.16914942860603333}, {"X": 0.8355447053909302, "Y": 0.18957321345806122}, {"X": 0.7906321883201599, "Y": 0.18950843811035156}]}, "Id": "1a7a0897-d553-49f5-82d6-1edfbaeee53f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PAPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09426240622997284, "Height": 0.020721299573779106, "Left": 0.8444027900695801, "Top": 0.16895467042922974}, "Polygon": [{"X": 0.8449006676673889, "Y": 0.16895467042922974}, {"X": 0.9386651515960693, "Y": 0.16909795999526978}, {"X": 0.9379306435585022, "Y": 0.189675971865654}, {"X": 0.8444027900695801, "Y": 0.1895410418510437}]}, "Id": "561d69b8-fd05-460f-b904-0fc6f0f4361c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Biron", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04825744032859802, "Height": 0.012302858754992485, "Left": 0.4102652966976166, "Top": 0.18389669060707092}, "Polygon": [{"X": 0.4102652966976166, "Y": 0.18389669060707092}, {"X": 0.45823752880096436, "Y": 0.18396690487861633}, {"X": 0.4585227370262146, "Y": 0.19619955122470856}, {"X": 0.410622775554657, "Y": 0.1961318850517273}]}, "Id": "8a0249e6-3af3-47b0-ac0e-e283c77c6579"}, {"BlockType": "WORD", "Confidence": 99.63707733154297, "Text": "Scale(Biron)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11026767641305923, "Height": 0.015573716722428799, "Left": 0.4641002118587494, "Top": 0.18427039682865143}, "Polygon": [{"X": 0.4641002118587494, "Y": 0.18427039682865143}, {"X": 0.5742283463478088, "Y": 0.18443143367767334}, {"X": 0.574367880821228, "Y": 0.1998441070318222}, {"X": 0.4644486606121063, "Y": 0.19969040155410767}]}, "Id": "f3e3b3d6-2ae7-4685-b444-cce8495b6a63"}, {"BlockType": "WORD", "Confidence": 99.65182495117188, "Text": "29237", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052346121519804, "Height": 0.010391316376626492, "Left": 0.09444499015808105, "Top": 0.2069253921508789}, "Polygon": [{"X": 0.09444499015808105, "Y": 0.2069253921508789}, {"X": 0.1461525857448578, "Y": 0.20699603855609894}, {"X": 0.14679111540317535, "Y": 0.21731671690940857}, {"X": 0.09514947980642319, "Y": 0.2172483652830124}]}, "Id": "ac1b66f0-fef6-40a5-8c63-62c13c1ba506"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Source", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05155888944864273, "Height": 0.010144300758838654, "Left": 0.48987117409706116, "Top": 0.21292546391487122}, "Polygon": [{"X": 0.48987117409706116, "Y": 0.21292546391487122}, {"X": 0.5412975549697876, "Y": 0.21299457550048828}, {"X": 0.5414300560951233, "Y": 0.22306977212429047}, {"X": 0.49006763100624084, "Y": 0.22300291061401367}]}, "Id": "e446fa6c-b1ef-43f6-8d43-ed9b6eaf68d5"}, {"BlockType": "WORD", "Confidence": 86.52662658691406, "Text": "VIM_AURO_IL_RATHBONE(VIM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23381686210632324, "Height": 0.012600977905094624, "Left": 0.5853195190429688, "Top": 0.2125166654586792}, "Polygon": [{"X": 0.5853195190429688, "Y": 0.2125166654586792}, {"X": 0.819136381149292, "Y": 0.21283134818077087}, {"X": 0.8188766837120056, "Y": 0.22511763870716095}, {"X": 0.5854144096374512, "Y": 0.22481533885002136}]}, "Id": "594926ed-6071-4f52-8c4a-dc8883526a3f"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "RECYCLERS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09725333005189896, "Height": 0.010460834950208664, "Left": 0.8233739137649536, "Top": 0.21267645061016083}, "Polygon": [{"X": 0.8235980868339539, "Y": 0.21267645061016083}, {"X": 0.920627236366272, "Y": 0.21280710399150848}, {"X": 0.9202795624732971, "Y": 0.2231372743844986}, {"X": 0.8233739137649536, "Y": 0.22301095724105835}]}, "Id": "01fc7d2b-e0ee-47d8-a10a-7ef46309217b"}, {"BlockType": "WORD", "Confidence": 96.6954345703125, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008291245438158512, "Height": 0.009886415675282478, "Left": 0.9256346821784973, "Top": 0.2131815403699875}, "Polygon": [{"X": 0.925973653793335, "Y": 0.2131815403699875}, {"X": 0.9339259266853333, "Y": 0.21319223940372467}, {"X": 0.9335772395133972, "Y": 0.22306795418262482}, {"X": 0.9256346821784973, "Y": 0.2230575978755951}]}, "Id": "330c182b-9565-4afb-8d76-042de53384df"}, {"BlockType": "WORD", "Confidence": 98.81437683105469, "Text": "P)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015779193490743637, "Height": 0.012676806189119816, "Left": 0.938266932964325, "Top": 0.21291311085224152}, "Polygon": [{"X": 0.9387212991714478, "Y": 0.21291311085224152}, {"X": 0.9540461301803589, "Y": 0.212933748960495}, {"X": 0.9535679221153259, "Y": 0.22558991611003876}, {"X": 0.938266932964325, "Y": 0.22557012736797333}]}, "Id": "9bad2606-a5f2-4b33-81f3-1fc56a02bc44"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Contract", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06782763451337814, "Height": 0.010410954244434834, "Left": 0.05415438115596771, "Top": 0.2281905561685562}, "Polygon": [{"X": 0.05415438115596771, "Y": 0.2281905561685562}, {"X": 0.12130981683731079, "Y": 0.22827637195587158}, {"X": 0.12198201566934586, "Y": 0.23860150575637817}, {"X": 0.05491252616047859, "Y": 0.23851865530014038}]}, "Id": "cd22f923-780d-4129-b518-b22dd773937c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "018539", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05310574546456337, "Height": 0.0102850878611207, "Left": 0.17100811004638672, "Top": 0.22856122255325317}, "Polygon": [{"X": 0.17100811004638672, "Y": 0.22856122255325317}, {"X": 0.2235778123140335, "Y": 0.22862835228443146}, {"X": 0.2241138517856598, "Y": 0.2388463020324707}, {"X": 0.17161069810390472, "Y": 0.23878148198127747}]}, "Id": "978a01dd-588a-4c70-a38d-3fe8cec8e016"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "(HARVESTER)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10708698630332947, "Height": 0.01322147250175476, "Left": 0.228321835398674, "Top": 0.22905562818050385}, "Polygon": [{"X": 0.228321835398674, "Y": 0.22905562818050385}, {"X": 0.33490243554115295, "Y": 0.22919155657291412}, {"X": 0.3354088366031647, "Y": 0.2422771006822586}, {"X": 0.229000985622406, "Y": 0.24214714765548706}]}, "Id": "abbc59d5-bb68-415a-afc4-c2d707f3e354"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Destination", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08242760598659515, "Height": 0.010222110897302628, "Left": 0.4900279939174652, "Top": 0.23050884902477264}, "Polygon": [{"X": 0.4900279939174652, "Y": 0.23050884902477264}, {"X": 0.5723611116409302, "Y": 0.23061347007751465}, {"X": 0.5724555850028992, "Y": 0.24073095619678497}, {"X": 0.49022555351257324, "Y": 0.2406299114227295}]}, "Id": "b2f1ac02-f8ce-4c15-8b55-38a767c477a6"}, {"BlockType": "WORD", "Confidence": 99.09757232666016, "Text": "BIRON(BIRON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10186135768890381, "Height": 0.01239132322371006, "Left": 0.5865309238433838, "Top": 0.23018206655979156}, "Polygon": [{"X": 0.5865309238433838, "Y": 0.23018206655979156}, {"X": 0.6883922815322876, "Y": 0.23031170666217804}, {"X": 0.6883308291435242, "Y": 0.24257339537143707}, {"X": 0.5866239666938782, "Y": 0.24244913458824158}]}, "Id": "55d3a188-4f90-446f-90ce-55f9729026e5"}, {"BlockType": "WORD", "Confidence": 98.99333953857422, "Text": "MILL)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03981335461139679, "Height": 0.012134931981563568, "Left": 0.6932766437530518, "Top": 0.23041406273841858}, "Polygon": [{"X": 0.6933445930480957, "Y": 0.23041406273841858}, {"X": 0.7330899834632874, "Y": 0.2304646223783493}, {"X": 0.7329626083374023, "Y": 0.24254898726940155}, {"X": 0.6932766437530518, "Y": 0.24250048398971558}]}, "Id": "de2b3dc9-479e-4bb8-af9f-cc6b84052ba3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04372882843017578, "Height": 0.009893613867461681, "Left": 0.4899614155292511, "Top": 0.264846533536911}, "Polygon": [{"X": 0.4899614155292511, "Y": 0.264846533536911}, {"X": 0.533550500869751, "Y": 0.2648957371711731}, {"X": 0.5336902737617493, "Y": 0.27474015951156616}, {"X": 0.49015453457832336, "Y": 0.2746928036212921}]}, "Id": "702ecbcf-5c32-4354-8340-90fe8ccbcb21"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ZACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041206926107406616, "Height": 0.009904018603265285, "Left": 0.5842521786689758, "Top": 0.2646086812019348}, "Polygon": [{"X": 0.5842521786689758, "Y": 0.2646086812019348}, {"X": 0.6254316568374634, "Y": 0.264655202627182}, {"X": 0.6254591345787048, "Y": 0.27451270818710327}, {"X": 0.5843300819396973, "Y": 0.27446791529655457}]}, "Id": "1b1557e4-0a1e-4eb8-87b4-fbe23496c46a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048826348036527634, "Height": 0.009960582479834557, "Left": 0.7972518801689148, "Top": 0.26532596349716187}, "Polygon": [{"X": 0.797435998916626, "Y": 0.26532596349716187}, {"X": 0.8460782170295715, "Y": 0.2653808295726776}, {"X": 0.8458343744277954, "Y": 0.27528655529022217}, {"X": 0.7972518801689148, "Y": 0.27523374557495117}]}, "Id": "07a3ea70-7507-46ea-a509-1b5c43f1b8c7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017802845686674118, "Height": 0.009657745249569416, "Left": 0.867096483707428, "Top": 0.26610493659973145}, "Polygon": [{"X": 0.8673592805862427, "Y": 0.26610493659973145}, {"X": 0.8848993182182312, "Y": 0.2661246657371521}, {"X": 0.8846154808998108, "Y": 0.275762677192688}, {"X": 0.867096483707428, "Y": 0.275743693113327}]}, "Id": "486b9bc9-87cc-4b6e-adf6-9ab2cd594fec"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BOL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03428299352526665, "Height": 0.009871742688119411, "Left": 0.061953410506248474, "Top": 0.28120702505111694}, "Polygon": [{"X": 0.061953410506248474, "Y": 0.28120702505111694}, {"X": 0.09556028991937637, "Y": 0.28124257922172546}, {"X": 0.09623640030622482, "Y": 0.29107874631881714}, {"X": 0.0626707598567009, "Y": 0.29104459285736084}]}, "Id": "b95d6a44-914b-4a06-bbb9-c8e31fbbd223"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031658727675676346, "Height": 0.012118825688958168, "Left": 0.0632433220744133, "Top": 0.2997951805591583}, "Polygon": [{"X": 0.0632433220744133, "Y": 0.2997951805591583}, {"X": 0.09406693279743195, "Y": 0.29982542991638184}, {"X": 0.09490204602479935, "Y": 0.31191399693489075}, {"X": 0.06412503123283386, "Y": 0.3118853271007538}]}, "Id": "a6864da5-8671-45e4-bdd2-723e0ce840ed"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04733036831021309, "Height": 0.009726800955832005, "Left": 0.09793845564126968, "Top": 0.29988035559654236}, "Polygon": [{"X": 0.09793845564126968, "Y": 0.29988035559654236}, {"X": 0.14466112852096558, "Y": 0.299926221370697}, {"X": 0.14526882767677307, "Y": 0.3096071481704712}, {"X": 0.09860270470380783, "Y": 0.30956321954727173}]}, "Id": "91b88152-b864-4fe4-a516-fededb810a18"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "BIR0029913", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08603393286466599, "Height": 0.009621217846870422, "Left": 0.16148599982261658, "Top": 0.29987165331840515}, "Polygon": [{"X": 0.16148599982261658, "Y": 0.29987165331840515}, {"X": 0.24704307317733765, "Y": 0.29995566606521606}, {"X": 0.24751994013786316, "Y": 0.3094928562641144}, {"X": 0.16206486523151398, "Y": 0.3094123303890228}]}, "Id": "61e5dadf-b284-4260-b969-e5d6592e4491"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05677327513694763, "Height": 0.009598532691597939, "Left": 0.48712316155433655, "Top": 0.3018489480018616}, "Polygon": [{"X": 0.48712316155433655, "Y": 0.3018489480018616}, {"X": 0.5437725186347961, "Y": 0.30190417170524597}, {"X": 0.5438964366912842, "Y": 0.31144747138023376}, {"X": 0.4873146116733551, "Y": 0.3113945424556732}]}, "Id": "281181fa-4de4-4c3e-9539-a6751e5dfa4f"}, {"BlockType": "WORD", "Confidence": 99.76243591308594, "Text": "OCC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03348549082875252, "Height": 0.009797235019505024, "Left": 0.5719872117042542, "Top": 0.30149444937705994}, "Polygon": [{"X": 0.5719872117042542, "Y": 0.30149444937705994}, {"X": 0.6054210066795349, "Y": 0.30152708292007446}, {"X": 0.6054726839065552, "Y": 0.3112916648387909}, {"X": 0.5720796585083008, "Y": 0.3112604022026062}]}, "Id": "e389f052-7238-4dfa-9788-e73fc8e10b61"}, {"BlockType": "WORD", "Confidence": 99.32457733154297, "Text": "11D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026230843737721443, "Height": 0.00942426547408104, "Left": 0.6101989150047302, "Top": 0.30171459913253784}, "Polygon": [{"X": 0.6101989150047302, "Y": 0.30171459913253784}, {"X": 0.6364163756370544, "Y": 0.3017401695251465}, {"X": 0.6364297866821289, "Y": 0.3111388683319092}, {"X": 0.6102430820465088, "Y": 0.3111143112182617}]}, "Id": "4fa27978-8d77-4f82-bf02-b5f36e467888"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04161025211215019, "Height": 0.010350166819989681, "Left": 0.06639739871025085, "Top": 0.31877580285072327}, "Polygon": [{"X": 0.06639739871025085, "Y": 0.31877580285072327}, {"X": 0.10731057077646255, "Y": 0.318812757730484}, {"X": 0.10800765454769135, "Y": 0.3291259706020355}, {"X": 0.06714736670255661, "Y": 0.32909080386161804}]}, "Id": "1c934792-6425-4f0f-aef1-fb59b159cb09"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011285370215773582, "Height": 0.010232038795948029, "Left": 0.11215411126613617, "Top": 0.3188563287258148}, "Polygon": [{"X": 0.11215411126613617, "Y": 0.3188563287258148}, {"X": 0.12276829034090042, "Y": 0.31886592507362366}, {"X": 0.1234394758939743, "Y": 0.32908838987350464}, {"X": 0.11283890157938004, "Y": 0.3290792405605316}]}, "Id": "183fc869-eee1-4a9a-8ba8-42613fc52a8a"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "BIR-29237", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0824415534734726, "Height": 0.010115228593349457, "Left": 0.16360194981098175, "Top": 0.31898897886276245}, "Polygon": [{"X": 0.16360194981098175, "Y": 0.31898897886276245}, {"X": 0.24553833901882172, "Y": 0.3190629482269287}, {"X": 0.24604350328445435, "Y": 0.3291042149066925}, {"X": 0.16421020030975342, "Y": 0.3290337324142456}]}, "Id": "ed90b4de-5579-49af-bc83-683bbba08175"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tons", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03704778477549553, "Height": 0.008786933496594429, "Left": 0.19690771400928497, "Top": 0.41303563117980957}, "Polygon": [{"X": 0.19690771400928497, "Y": 0.41303563117980957}, {"X": 0.2334957867860794, "Y": 0.4130544066429138}, {"X": 0.2339555025100708, "Y": 0.42182254791259766}, {"X": 0.19740809500217438, "Y": 0.42180511355400085}]}, "Id": "1cb682de-9502-44e5-816f-53e0cea9da74"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01923038810491562, "Height": 0.008599769324064255, "Left": 0.28898170590400696, "Top": 0.412490576505661}, "Polygon": [{"X": 0.28898170590400696, "Y": 0.412490576505661}, {"X": 0.30784255266189575, "Y": 0.4125002920627594}, {"X": 0.3082121014595032, "Y": 0.42109033465385437}, {"X": 0.2893717885017395, "Y": 0.4210813045501709}]}, "Id": "ea932908-62d3-490f-8055-14360839f4a3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044618599116802216, "Height": 0.009044081903994083, "Left": 0.0799609050154686, "Top": 0.42931294441223145}, "Polygon": [{"X": 0.0799609050154686, "Y": 0.42931294441223145}, {"X": 0.12398006021976471, "Y": 0.42933258414268494}, {"X": 0.12457950413227081, "Y": 0.43835702538490295}, {"X": 0.08061084151268005, "Y": 0.43833908438682556}]}, "Id": "8668eb8c-8b0d-44a0-b6fc-f9992a555b03"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "35.84", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037736766040325165, "Height": 0.009339381940662861, "Left": 0.19664505124092102, "Top": 0.4276314675807953}, "Polygon": [{"X": 0.19664505124092102, "Y": 0.4276314675807953}, {"X": 0.23389260470867157, "Y": 0.4276483356952667}, {"X": 0.23438182473182678, "Y": 0.4369708299636841}, {"X": 0.19717837870121002, "Y": 0.43695542216300964}]}, "Id": "fa8e9bcd-1945-41eb-8baa-be5523072519"}, {"BlockType": "WORD", "Confidence": 99.43219757080078, "Text": "71,680", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04509070888161659, "Height": 0.010107634589076042, "Left": 0.26583510637283325, "Top": 0.42668265104293823}, "Polygon": [{"X": 0.26583510637283325, "Y": 0.42668265104293823}, {"X": 0.31049445271492004, "Y": 0.4267030656337738}, {"X": 0.31092581152915955, "Y": 0.4367902874946594}, {"X": 0.26632362604141235, "Y": 0.43677178025245667}]}, "Id": "4011a2fd-a390-4bfb-8e76-21c44221858e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031077608466148376, "Height": 0.008758628740906715, "Left": 0.33635029196739197, "Top": 0.42691072821617126}, "Polygon": [{"X": 0.33635029196739197, "Y": 0.42691072821617126}, {"X": 0.3671167194843292, "Y": 0.42692476511001587}, {"X": 0.36742788553237915, "Y": 0.43566933274269104}, {"X": 0.3366956114768982, "Y": 0.43565642833709717}]}, "Id": "db16a7b7-7252-4e77-9b83-4e2259e951a9"}, {"BlockType": "WORD", "Confidence": 99.40369415283203, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012098483741283417, "Height": 0.008479786105453968, "Left": 0.37176141142845154, "Top": 0.4271773099899292}, "Polygon": [{"X": 0.37176141142845154, "Y": 0.4271773099899292}, {"X": 0.38357600569725037, "Y": 0.42718270421028137}, {"X": 0.38385990262031555, "Y": 0.4356571137905121}, {"X": 0.37205803394317627, "Y": 0.43565213680267334}]}, "Id": "43a63fa4-9b1e-4684-92a7-a50d7ff17596"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "11/12/22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054841749370098114, "Height": 0.008945059031248093, "Left": 0.4073033332824707, "Top": 0.42659834027290344}, "Polygon": [{"X": 0.4073033332824707, "Y": 0.42659834027290344}, {"X": 0.4619348645210266, "Y": 0.4266233444213867}, {"X": 0.4621450901031494, "Y": 0.435543417930603}, {"X": 0.4075753688812256, "Y": 0.4355204403400421}]}, "Id": "15d360c6-ccab-46c7-85db-27944a6b683d"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "5:04:25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04732612147927284, "Height": 0.008812706917524338, "Left": 0.46643757820129395, "Top": 0.4265727400779724}, "Polygon": [{"X": 0.46643757820129395, "Y": 0.4265727400779724}, {"X": 0.5136141180992126, "Y": 0.4265943467617035}, {"X": 0.5137637257575989, "Y": 0.43538546562194824}, {"X": 0.46663978695869446, "Y": 0.43536558747291565}]}, "Id": "d6120988-a704-4309-bc85-74491f5b4334"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033447351306676865, "Height": 0.00884980522096157, "Left": 0.08127229660749435, "Top": 0.4434557855129242}, "Polygon": [{"X": 0.08127229660749435, "Y": 0.4434557855129242}, {"X": 0.11412052810192108, "Y": 0.44346851110458374}, {"X": 0.11471964418888092, "Y": 0.4523055851459503}, {"X": 0.08190837502479553, "Y": 0.45229408144950867}]}, "Id": "6ce5334d-e359-4a98-9d28-e4fd838fbc17"}, {"BlockType": "WORD", "Confidence": 98.61547088623047, "Text": "14.73", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036457352340221405, "Height": 0.009038951247930527, "Left": 0.19872742891311646, "Top": 0.4415554106235504}, "Polygon": [{"X": 0.19872742891311646, "Y": 0.4415554106235504}, {"X": 0.23471128940582275, "Y": 0.44156962633132935}, {"X": 0.23518477380275726, "Y": 0.45059436559677124}, {"X": 0.1992422342300415, "Y": 0.45058149099349976}]}, "Id": "0dbba2df-bf7e-4ca6-90b1-7e557b0c1a1b"}, {"BlockType": "WORD", "Confidence": 99.4834213256836, "Text": "29,460", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04507197067141533, "Height": 0.010247650556266308, "Left": 0.26654961705207825, "Top": 0.4401445686817169}, "Polygon": [{"X": 0.26654961705207825, "Y": 0.4401445686817169}, {"X": 0.3111843168735504, "Y": 0.4401624798774719}, {"X": 0.31162160634994507, "Y": 0.4503922164440155}, {"X": 0.2670449912548065, "Y": 0.4503762125968933}]}, "Id": "b5f6698b-615b-48e9-89fe-b280c3e1ce1f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030842306092381477, "Height": 0.008500377647578716, "Left": 0.3371424674987793, "Top": 0.44125688076019287}, "Polygon": [{"X": 0.3371424674987793, "Y": 0.44125688076019287}, {"X": 0.3676827847957611, "Y": 0.44126901030540466}, {"X": 0.3679847717285156, "Y": 0.44975727796554565}, {"X": 0.3374774158000946, "Y": 0.4497462511062622}]}, "Id": "dab757cf-6f84-4c42-97ab-d83be56da9c7"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024996893480420113, "Height": 0.008438130840659142, "Left": 0.37211477756500244, "Top": 0.44156742095947266}, "Polygon": [{"X": 0.37211477756500244, "Y": 0.44156742095947266}, {"X": 0.3968430161476135, "Y": 0.4415772259235382}, {"X": 0.3971116840839386, "Y": 0.45000556111335754}, {"X": 0.3724099397659302, "Y": 0.4499966502189636}]}, "Id": "ded84ea7-5cf3-4f29-97e7-365b86d6a991"}, {"BlockType": "WORD", "Confidence": 99.22293090820312, "Text": "11/12/22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054538991302251816, "Height": 0.008570189587771893, "Left": 0.40754878520965576, "Top": 0.441051185131073}, "Polygon": [{"X": 0.40754878520965576, "Y": 0.441051185131073}, {"X": 0.46188589930534363, "Y": 0.44107282161712646}, {"X": 0.4620877802371979, "Y": 0.44962137937545776}, {"X": 0.40780970454216003, "Y": 0.4496017098426819}]}, "Id": "83fd8a7e-0d14-4f4e-a05d-83eb2d4f534f"}, {"BlockType": "WORD", "Confidence": 99.39871215820312, "Text": "6:01:11", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04598197340965271, "Height": 0.008739511482417583, "Left": 0.46605950593948364, "Top": 0.44098344445228577}, "Polygon": [{"X": 0.46605950593948364, "Y": 0.44098344445228577}, {"X": 0.5118908882141113, "Y": 0.44100168347358704}, {"X": 0.512041449546814, "Y": 0.44972294569015503}, {"X": 0.4662608802318573, "Y": 0.44970637559890747}]}, "Id": "b55f84d0-834b-4de3-8527-4248ee8cd11b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025261199101805687, "Height": 0.008836966007947922, "Left": 0.08513647317886353, "Top": 0.45781683921813965}, "Polygon": [{"X": 0.08513647317886353, "Y": 0.45781683921813965}, {"X": 0.10979314893484116, "Y": 0.4578249156475067}, {"X": 0.11039766669273376, "Y": 0.46665382385253906}, {"X": 0.08576875925064087, "Y": 0.46664664149284363}]}, "Id": "68d2a972-a8e7-4337-9222-7b31e36556ca"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "21.11", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03577348217368126, "Height": 0.00929650291800499, "Left": 0.19888412952423096, "Top": 0.4557640850543976}, "Polygon": [{"X": 0.19888412952423096, "Y": 0.4557640850543976}, {"X": 0.23416896164417267, "Y": 0.4557759463787079}, {"X": 0.23465761542320251, "Y": 0.4650605618953705}, {"X": 0.19941453635692596, "Y": 0.46505007147789}]}, "Id": "fbfd119e-e920-4dd7-adbd-c17660395365"}, {"BlockType": "WORD", "Confidence": 98.37631225585938, "Text": "42,220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04485674947500229, "Height": 0.010043726302683353, "Left": 0.2685806453227997, "Top": 0.4542286694049835}, "Polygon": [{"X": 0.2685806453227997, "Y": 0.4542286694049835}, {"X": 0.3130102753639221, "Y": 0.45424389839172363}, {"X": 0.3134373724460602, "Y": 0.4642724096775055}, {"X": 0.2690645158290863, "Y": 0.4642590284347534}]}, "Id": "4972206c-4823-4042-917d-4a54287988c7"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Buyer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04075351357460022, "Height": 0.010942909866571426, "Left": 0.12300576269626617, "Top": 0.9132089614868164}, "Polygon": [{"X": 0.12300576269626617, "Y": 0.9132713079452515}, {"X": 0.16304683685302734, "Y": 0.9132089614868164}, {"X": 0.1637592762708664, "Y": 0.9240877628326416}, {"X": 0.12377718091011047, "Y": 0.9241518378257751}]}, "Id": "a0d6f738-9e78-4839-a6f8-e44de5978bc3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "018539", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04323843494057655, "Height": 0.01007478404790163, "Left": 0.1799345761537552, "Top": 0.9150140285491943}, "Polygon": [{"X": 0.1799345761537552, "Y": 0.9150808453559875}, {"X": 0.22259806096553802, "Y": 0.9150140285491943}, {"X": 0.22317302227020264, "Y": 0.9250203967094421}, {"X": 0.18056732416152954, "Y": 0.9250888228416443}]}, "Id": "6778986d-9ee6-40c9-86b9-4050b3da8c30"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "f9d3a57e-470a-4acc-af0f-2bea0e26596b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "f9d3a57e-470a-4acc-af0f-2bea0e26596b", "content-type": "application/x-amz-json-1.1", "content-length": "45567", "date": "Wed, 24 Sep 2025 13:38:55 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "861e28d3-7929-4a57-b63c-355b644a21d1", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:38:57 GMT", "content-type": "application/json", "content-length": "817", "connection": "keep-alive", "x-amzn-requestid": "861e28d3-7929-4a57-b63c-355b644a21d1"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only one page? The user provided <page1> content. It's a scale ticket? Let's examine: contains \"BOL\", \"Trip Ticket\", \"Load #\", \"Gross\", \"Tare\", \"Net\", \"Date In\", \"Date Out\", weight units, driver, etc. This matches scale ticket: weight-related keywords: Gross, Tare, Net, Weighed At (Date In/Out), Truck, Driver. So doc_type = scale_ticket.\n\nThus output JSON with documents array containing one object: page_no 1, doc_type \"scale_ticket\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2754, "outputTokens": 142, "totalTokens": 2896}, "metrics": {"latencyMs": 851}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7dc4bad1_6_scale_ticket_12.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nND Paper\nND\nND PAPER\nBiron Scale(Biron)\n29237\nSource\nVIM_AURO_IL_RATHBONE(VIM RECYCLERS L P)\nContract\n018539 (HARVESTER)\nDestination\nBIRON(BIRON MILL)\nDriver\nZACH\nTrailer 25\nBOL\nTrip Ticket BIR0029913\nProduct OCC 11D\nLoad # BIR-29237\nTons\nLB\nGross\n35.84\n71,680\nDate In 11/12/22 5:04:25\nTare\n14.73\n29,460\nDate Out 11/12/22 6:01:11\nNet\n21.11\n42,220\nBuyer: 018539\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}