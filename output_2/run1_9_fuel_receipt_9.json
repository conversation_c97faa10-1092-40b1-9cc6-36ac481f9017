{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.43699803948402405, "Height": 0.47319862246513367, "Left": 0.4110710024833679, "Top": 0.3180362284183502}, "Polygon": [{"X": 0.45013222098350525, "Y": 0.3180362284183502}, {"X": 0.8480690717697144, "Y": 0.33713674545288086}, {"X": 0.8110170364379883, "Y": 0.7912348508834839}, {"X": 0.4110710024833679, "Y": 0.7579219937324524}]}, "Id": "0a389ecb-948e-4123-8ac2-b10164bd2663", "Relationships": [{"Type": "CHILD", "Ids": ["cfd72ee4-84e5-45f6-954f-38de554794d7", "748644a3-4a7f-460c-b2ab-a3cba0f23c30", "abbf72b2-cdd8-4e1e-a107-3dc823cc02c5", "15a0c459-e4e6-47b0-a855-76e73136ed4e", "cdeaee8f-c2ce-4d8f-866b-417b03522c30", "afda62d1-b888-4db6-b3ac-d900fa8ce1ee", "c0a3e137-b496-4412-80f3-6525205f4694", "87b453d1-b3a7-4979-9cd3-249a349f2577", "2d616b48-b6b9-4b97-9448-a8ef81620cb6", "098aede2-a550-4808-aada-2c9a923f944d", "bc9735b7-faef-4ad3-8d9a-15430e446f30", "c85b6f22-b9dc-4292-b72f-666ba221cc14", "9c81b664-4b77-4ebd-943f-cadd7a7e6d43", "358beaa7-46f8-4f97-9a71-677240164e3d", "2b4c7591-c808-4958-8fd9-34e6d82e0fa6", "4119a187-7ae0-42bd-a115-efb262e23ea8", "878c5e35-20b1-48c8-a0af-92ac2c81ec2f", "9ef58aff-2a7c-4d18-b3ce-cb6f01ad7a4a", "b5c483ec-79b0-4951-98b5-06da259d0b47", "e4b1d9ae-2cd0-43a4-8004-eaa2424656f9", "cbbf0f37-bdba-432b-bac5-75aa671e2b9b", "eed3f2a6-60b6-4a04-aeb2-a0a536e3e009", "4a96c15a-1c31-4f65-bb02-d2eaf071a2b7", "77812cd0-eba9-4788-9058-d96947c5101f"]}]}, {"BlockType": "LINE", "Confidence": 85.58922576904297, "Text": "5311 OLD HIGHWAY T8", "Geometry": {"BoundingBox": {"Width": 0.2211560159921646, "Height": 0.027752960100769997, "Left": 0.5179152488708496, "Top": 0.3216841220855713}, "Polygon": [{"X": 0.5193952322006226, "Y": 0.3216841220855713}, {"X": 0.739071249961853, "Y": 0.3322339355945587}, {"X": 0.7376336455345154, "Y": 0.34943708777427673}, {"X": 0.5179152488708496, "Y": 0.33858972787857056}]}, "Id": "cfd72ee4-84e5-45f6-954f-38de554794d7", "Relationships": [{"Type": "CHILD", "Ids": ["6904a494-58c1-4587-9e95-45db701e1393", "4348f6b6-b05b-442a-8335-bc6f3ef956e8", "8c48c423-3b51-482a-845e-ce9d924e44fa", "42ff19c8-ddc7-4932-9a77-99ffe6048efd"]}]}, {"BlockType": "LINE", "Confidence": 99.16673278808594, "Text": "STEVENS POINT, WI", "Geometry": {"BoundingBox": {"Width": 0.19744393229484558, "Height": 0.029537882655858994, "Left": 0.5287465453147888, "Top": 0.33602163195610046}, "Polygon": [{"X": 0.5304595232009888, "Y": 0.33602163195610046}, {"X": 0.726190447807312, "Y": 0.34563538432121277}, {"X": 0.7245212197303772, "Y": 0.36555951833724976}, {"X": 0.5287465453147888, "Y": 0.3556383550167084}]}, "Id": "748644a3-4a7f-460c-b2ab-a3cba0f23c30", "Relationships": [{"Type": "CHILD", "Ids": ["028e6f06-f312-4923-bee2-74989f5e83ec", "3da4869c-b5fd-40a4-9aa1-576b6ff557a0", "3ae2f18f-fdac-4d74-89e1-975d87247022"]}]}, {"BlockType": "LINE", "Confidence": 99.83567810058594, "Text": "Invoice# 0098652", "Geometry": {"BoundingBox": {"Width": 0.1954675018787384, "Height": 0.027269961312413216, "Left": 0.5166807770729065, "Top": 0.3711033761501312}, "Polygon": [{"X": 0.518161952495575, "Y": 0.3711033761501312}, {"X": 0.7121482491493225, "Y": 0.3811798095703125}, {"X": 0.7107043862342834, "Y": 0.3983733355998993}, {"X": 0.5166807770729065, "Y": 0.38803353905677795}]}, "Id": "abbf72b2-cdd8-4e1e-a107-3dc823cc02c5", "Relationships": [{"Type": "CHILD", "Ids": ["82960e3a-a23c-44bb-8c77-601f131f293c", "bcd58e1c-88d3-4725-ad1e-8bcc0887ed0c"]}]}, {"BlockType": "LINE", "Confidence": 98.6428451538086, "Text": "Trans # 0900-8652", "Geometry": {"BoundingBox": {"Width": 0.22267583012580872, "Height": 0.028230909258127213, "Left": 0.5119580626487732, "Top": 0.3875694274902344}, "Polygon": [{"X": 0.5133726596832275, "Y": 0.3875694274902344}, {"X": 0.7346339225769043, "Y": 0.3993557095527649}, {"X": 0.7332600355148315, "Y": 0.4158003628253937}, {"X": 0.5119580626487732, "Y": 0.4037271738052368}]}, "Id": "15a0c459-e4e6-47b0-a855-76e73136ed4e", "Relationships": [{"Type": "CHILD", "Ids": ["2d44314e-d29f-4ca1-8cfc-2f835b656641", "f512f6fe-ef36-42df-a701-5d2d7f0a104a", "5c5c5eff-df50-420a-b271-00697de10fb9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.045277729630470276, "Height": 0.013291515409946442, "Left": 0.511269748210907, "Top": 0.40863460302352905}, "Polygon": [{"X": 0.5122166275978088, "Y": 0.40863460302352905}, {"X": 0.5565474629402161, "Y": 0.4110702574253082}, {"X": 0.5556058883666992, "Y": 0.4219261109828949}, {"X": 0.511269748210907, "Y": 0.4194519519805908}]}, "Id": "cdeaee8f-c2ce-4d8f-866b-417b03522c30", "Relationships": [{"Type": "CHILD", "Ids": ["2ddc9ce9-8569-43ea-ae25-88974ee8fc51"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "08/22/2025", "Geometry": {"BoundingBox": {"Width": 0.12028734385967255, "Height": 0.02333524078130722, "Left": 0.6257559657096863, "Top": 0.41011282801628113}, "Polygon": [{"X": 0.6271825432777405, "Y": 0.41011282801628113}, {"X": 0.74604332447052, "Y": 0.41659823060035706}, {"X": 0.7446394562721252, "Y": 0.43344807624816895}, {"X": 0.6257559657096863, "Y": 0.4268048405647278}]}, "Id": "afda62d1-b888-4db6-b3ac-d900fa8ce1ee", "Relationships": [{"Type": "CHILD", "Ids": ["5b3568b7-db80-4c93-83ae-e73bb3fa8ac7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.046290166676044464, "Height": 0.013878915458917618, "Left": 0.508510410785675, "Top": 0.42406120896339417}, "Polygon": [{"X": 0.5094992518424988, "Y": 0.42406120896339417}, {"X": 0.5548006296157837, "Y": 0.42660611867904663}, {"X": 0.5538175106048584, "Y": 0.4379401206970215}, {"X": 0.508510410785675, "Y": 0.4353541135787964}]}, "Id": "c0a3e137-b496-4412-80f3-6525205f4694", "Relationships": [{"Type": "CHILD", "Ids": ["20555b92-876f-4041-8172-42d3b3083042"]}]}, {"BlockType": "LINE", "Confidence": 99.72945404052734, "Text": "12:06 PM", "Geometry": {"BoundingBox": {"Width": 0.0950842797756195, "Height": 0.01823015697300434, "Left": 0.6249213218688965, "Top": 0.4285840392112732}, "Polygon": [{"X": 0.6260210871696472, "Y": 0.4285840392112732}, {"X": 0.7200056314468384, "Y": 0.43384894728660583}, {"X": 0.7189196944236755, "Y": 0.4468141794204712}, {"X": 0.6249213218688965, "Y": 0.4414530396461487}]}, "Id": "87b453d1-b3a7-4979-9cd3-249a349f2577", "Relationships": [{"Type": "CHILD", "Ids": ["bcabf323-6b4b-4b64-b060-e6827b46559a", "5ef6895e-8090-41bc-ba1a-73b8bd535fb4"]}]}, {"BlockType": "LINE", "Confidence": 99.92732238769531, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.2373655140399933, "Height": 0.029696494340896606, "Left": 0.5050460696220398, "Top": 0.4547618627548218}, "Polygon": [{"X": 0.5064091086387634, "Y": 0.4547618627548218}, {"X": 0.7424116134643555, "Y": 0.4685977101325989}, {"X": 0.7410903573036194, "Y": 0.4844583570957184}, {"X": 0.5050460696220398, "Y": 0.470327228307724}]}, "Id": "2d616b48-b6b9-4b97-9448-a8ef81620cb6", "Relationships": [{"Type": "CHILD", "Ids": ["e2e525f4-dfe7-46ad-99ca-a39132876b60", "4fd27ff7-3dd8-4a5e-8e01-3e3cdb1b1f1a", "355a89e7-4936-4872-b044-df5711816cee"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "02", "Geometry": {"BoundingBox": {"Width": 0.02292364276945591, "Height": 0.012063010595738888, "Left": 0.5153754353523254, "Top": 0.4727410078048706}, "Polygon": [{"X": 0.5163123607635498, "Y": 0.4727410078048706}, {"X": 0.5382990837097168, "Y": 0.4740602970123291}, {"X": 0.5373647212982178, "Y": 0.4848040044307709}, {"X": 0.5153754353523254, "Y": 0.4834657907485962}]}, "Id": "098aede2-a550-4808-aada-2c9a923f944d", "Relationships": [{"Type": "CHILD", "Ids": ["5b2bc2cd-3e58-4a6d-8c87-02fa882dcd60"]}]}, {"BlockType": "LINE", "Confidence": 99.08561706542969, "Text": "35.705 3.499", "Geometry": {"BoundingBox": {"Width": 0.1556733399629593, "Height": 0.021092651411890984, "Left": 0.5857092142105103, "Top": 0.4771426320075989}, "Polygon": [{"X": 0.5867134928703308, "Y": 0.4771426320075989}, {"X": 0.7413825392723083, "Y": 0.48642560839653015}, {"X": 0.7403988838195801, "Y": 0.498235285282135}, {"X": 0.5857092142105103, "Y": 0.48880815505981445}]}, "Id": "bc9735b7-faef-4ad3-8d9a-15430e446f30", "Relationships": [{"Type": "CHILD", "Ids": ["60716d81-cb5c-4cd4-a345-9308d221021f", "02c73597-0a11-4e9c-ae42-1e9d99c7999e"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.08353032171726227, "Height": 0.01727416180074215, "Left": 0.4994858503341675, "Top": 0.5036824941635132}, "Polygon": [{"X": 0.5005402565002441, "Y": 0.5036824941635132}, {"X": 0.5830161571502686, "Y": 0.508840024471283}, {"X": 0.5819728374481201, "Y": 0.5209566950798035}, {"X": 0.4994858503341675, "Y": 0.5157192349433899}]}, "Id": "c85b6f22-b9dc-4292-b72f-666ba221cc14", "Relationships": [{"Type": "CHILD", "Ids": ["3e44dc98-fc51-401b-8cf3-24885edf91b1"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.07307938486337662, "Height": 0.01544715091586113, "Left": 0.6674906015396118, "Top": 0.5162259340286255}, "Polygon": [{"X": 0.668408989906311, "Y": 0.5162259340286255}, {"X": 0.7405699491500854, "Y": 0.5207499861717224}, {"X": 0.7396605610847473, "Y": 0.5316730737686157}, {"X": 0.6674906015396118, "Y": 0.5270867943763733}]}, "Id": "9c81b664-4b77-4ebd-943f-cadd7a7e6d43", "Relationships": [{"Type": "CHILD", "Ids": ["1cded4bc-1cfe-49a1-aa84-6e63d2358715"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.047476235777139664, "Height": 0.014200584962964058, "Left": 0.4977375566959381, "Top": 0.5205289721488953}, "Polygon": [{"X": 0.49871769547462463, "Y": 0.5205289721488953}, {"X": 0.5452137589454651, "Y": 0.523499071598053}, {"X": 0.5442394018173218, "Y": 0.534729540348053}, {"X": 0.4977375566959381, "Y": 0.5317174792289734}]}, "Id": "358beaa7-46f8-4f97-9a71-677240164e3d", "Relationships": [{"Type": "CHILD", "Ids": ["6c9ff3d6-fe50-4889-9104-a7b0d1b2c26d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "124.93", "Geometry": {"BoundingBox": {"Width": 0.07357583940029144, "Height": 0.015956813469529152, "Left": 0.6666455864906311, "Top": 0.5328824520111084}, "Polygon": [{"X": 0.6675962209701538, "Y": 0.5328824520111084}, {"X": 0.7402214407920837, "Y": 0.5375306010246277}, {"X": 0.739280104637146, "Y": 0.548839271068573}, {"X": 0.6666455864906311, "Y": 0.5441262125968933}]}, "Id": "2b4c7591-c808-4958-8fd9-34e6d82e0fa6", "Relationships": [{"Type": "CHILD", "Ids": ["3f9e4716-a5d5-4cfe-bb96-5fb626a63767"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.062072593718767166, "Height": 0.015767522156238556, "Left": 0.49231427907943726, "Top": 0.5547561645507812}, "Polygon": [{"X": 0.49333471059799194, "Y": 0.5547561645507812}, {"X": 0.5543868541717529, "Y": 0.5588237047195435}, {"X": 0.5533742904663086, "Y": 0.5705236792564392}, {"X": 0.49231427907943726, "Y": 0.5663989186286926}]}, "Id": "4119a187-7ae0-42bd-a115-efb262e23ea8", "Relationships": [{"Type": "CHILD", "Ids": ["4370e84a-ba80-4322-9f49-31a951325073"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "124.93", "Geometry": {"BoundingBox": {"Width": 0.07373392581939697, "Height": 0.016633005812764168, "Left": 0.6530899405479431, "Top": 0.5669887065887451}, "Polygon": [{"X": 0.6540822386741638, "Y": 0.5669887065887451}, {"X": 0.7268238663673401, "Y": 0.571843683719635}, {"X": 0.725841224193573, "Y": 0.5836216807365417}, {"X": 0.6530899405479431, "Y": 0.5786989331245422}]}, "Id": "878c5e35-20b1-48c8-a0af-92ac2c81ec2f", "Relationships": [{"Type": "CHILD", "Ids": ["a52b0d17-f90c-4b6d-b8e4-6d2f43afd5ea"]}]}, {"BlockType": "LINE", "Confidence": 99.92080688476562, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.23541370034217834, "Height": 0.03044433519244194, "Left": 0.****************, "Top": 0.5909909009933472}, "Polygon": [{"X": 0.****************, "Y": 0.5909909009933472}, {"X": 0.7244367599487305, "Y": 0.6072713732719421}, {"X": 0.7232552170753479, "Y": 0.621435284614563}, {"X": 0.****************, "Y": 0.6048920750617981}]}, "Id": "9ef58aff-2a7c-4d18-b3ce-cb6f01ad7a4a", "Relationships": [{"Type": "CHILD", "Ids": ["dd8f9cd8-1a2e-49f5-8ee7-af023c13f44e", "4e1a9d94-bcbb-4e88-98dd-0caf4421a9a6", "06b87db6-9827-4ef6-9e81-9154df8e5fb7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.11035536229610443, "Height": 0.01969192735850811, "Left": 0.*****************, "Top": 0.6093183159828186}, "Polygon": [{"X": 0.***************, "Y": 0.6093183159828186}, {"X": 0.5974206328392029, "Y": 0.6170781850814819}, {"X": 0.5963982939720154, "Y": 0.6290102601051331}, {"X": 0.*****************, "Y": 0.621146023273468}]}, "Id": "b5c483ec-79b0-4951-98b5-06da259d0b47", "Relationships": [{"Type": "CHILD", "Ids": ["264253ea-3ebf-41bc-a55e-64c1564ec7d2", "3a7141af-d13e-4687-a877-ca6f20c175fd"]}]}, {"BlockType": "LINE", "Confidence": 90.525146484375, "Text": "1591", "Geometry": {"BoundingBox": {"Width": 0.042111292481422424, "Height": 0.015403363853693008, "Left": 0.6622132062911987, "Top": 0.6409195065498352}, "Polygon": [{"X": 0.6632594466209412, "Y": 0.6409195065498352}, {"X": 0.7043245434761047, "Y": 0.6438961029052734}, {"X": 0.7032841444015503, "Y": 0.****************}, {"X": 0.6622132062911987, "Y": 0.****************}]}, "Id": "e4b1d9ae-2cd0-43a4-8004-eaa2424656f9", "Relationships": [{"Type": "CHILD", "Ids": ["c0c1a0ad-305c-47a5-a670-62383d847a19"]}]}, {"BlockType": "LINE", "Confidence": 99.92636108398438, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.07465733587741852, "Height": 0.01769021525979042, "Left": 0.48249632120132446, "Top": 0.6453866362571716}, "Polygon": [{"X": 0.4835643470287323, "Y": 0.6453866362571716}, {"X": 0.5571536421775818, "Y": 0.6508229970932007}, {"X": 0.556095540523529, "Y": 0.6630768179893494}, {"X": 0.48249632120132446, "Y": 0.6575680375099182}]}, "Id": "cbbf0f37-bdba-432b-bac5-75aa671e2b9b", "Relationships": [{"Type": "CHILD", "Ids": ["c1a7346d-31de-4f2d-b7be-2604e2ae12a1", "68cab67c-10d7-4bfc-af9d-204f34f34435"]}]}, {"BlockType": "LINE", "Confidence": 99.7120132446289, "Text": "P18446", "Geometry": {"BoundingBox": {"Width": 0.07571911066770554, "Height": 0.01879880763590336, "Left": 0.6550054550170898, "Top": 0.6588646769523621}, "Polygon": [{"X": 0.6561221480369568, "Y": 0.6588646769523621}, {"X": 0.730724573135376, "Y": 0.6643801331520081}, {"X": 0.7296191453933716, "Y": 0.6776635050773621}, {"X": 0.6550054550170898, "Y": 0.6720694899559021}]}, "Id": "eed3f2a6-60b6-4a04-aeb2-a0a536e3e009", "Relationships": [{"Type": "CHILD", "Ids": ["f56bd413-b698-4193-b882-519312dbe91e"]}]}, {"BlockType": "LINE", "Confidence": 99.37728881835938, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.22293303906917572, "Height": 0.03040454536676407, "Left": 0.4802818298339844, "Top": 0.6633877754211426}, "Polygon": [{"X": 0.4814622402191162, "Y": 0.6633877754211426}, {"X": 0.7032148838043213, "Y": 0.68008953332901}, {"X": 0.7020680904388428, "Y": 0.6937923431396484}, {"X": 0.4802818298339844, "Y": 0.6768492460250854}]}, "Id": "4a96c15a-1c31-4f65-bb02-d2eaf071a2b7", "Relationships": [{"Type": "CHILD", "Ids": ["32c6452a-9416-4204-8d87-7c758f1aa36d", "90f94ba3-ae4a-40e9-87f9-acfd64992b50"]}]}, {"BlockType": "LINE", "Confidence": 99.7513427734375, "Text": "AUTH REF#: P18446", "Geometry": {"BoundingBox": {"Width": 0.21054652333259583, "Height": 0.029575109481811523, "Left": 0.4902564585208893, "Top": 0.681959867477417}, "Polygon": [{"X": 0.4914185404777527, "Y": 0.681959867477417}, {"X": 0.7008029818534851, "Y": 0.6980262994766235}, {"X": 0.6996722221374512, "Y": 0.7115349769592285}, {"X": 0.4902564585208893, "Y": 0.695243775844574}]}, "Id": "77812cd0-eba9-4788-9058-d96947c5101f", "Relationships": [{"Type": "CHILD", "Ids": ["f9d1506b-0071-48ca-bce4-04108d776fc0", "2f765ae3-eb02-48f0-be4e-f7ea0be3a03e", "0253aa9d-cc94-4e3e-a101-058bd3ebf002"]}]}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "5311", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04255405813455582, "Height": 0.01577027328312397, "Left": 0.5179152488708496, "Top": 0.3248612880706787}, "Polygon": [{"X": 0.5191170573234558, "Y": 0.3248612880706787}, {"X": 0.5604692697525024, "Y": 0.32685765624046326}, {"X": 0.5592737793922424, "Y": 0.3406315743923187}, {"X": 0.5179152488708496, "Y": 0.33858972787857056}]}, "Id": "6904a494-58c1-4587-9e95-45db701e1393"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "OLD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03496100753545761, "Height": 0.014050940051674843, "Left": 0.5731338858604431, "Top": 0.3258807063102722}, "Polygon": [{"X": 0.5742058157920837, "Y": 0.3258807063102722}, {"X": 0.6080948710441589, "Y": 0.32751238346099854}, {"X": 0.6070275902748108, "Y": 0.3399316668510437}, {"X": 0.5731338858604431, "Y": 0.3382664918899536}]}, "Id": "4348f6b6-b05b-442a-8335-bc6f3ef956e8"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "HIGHWAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08277822285890579, "Height": 0.016024811193346977, "Left": 0.6192010045051575, "Top": 0.3265267312526703}, "Polygon": [{"X": 0.6202314496040344, "Y": 0.3265267312526703}, {"X": 0.7019792795181274, "Y": 0.3304526209831238}, {"X": 0.7009600400924683, "Y": 0.3425515294075012}, {"X": 0.6192010045051575, "Y": 0.3385475277900696}]}, "Id": "8c48c423-3b51-482a-845e-ce9d924e44fa"}, {"BlockType": "WORD", "Confidence": 42.719032287597656, "Text": "T8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0244025606662035, "Height": 0.008593457750976086, "Left": 0.7146686911582947, "Top": 0.3310920298099518}, "Polygon": [{"X": 0.7152934074401855, "Y": 0.3310920298099518}, {"X": 0.739071249961853, "Y": 0.3322339355945587}, {"X": 0.7384485602378845, "Y": 0.33968546986579895}, {"X": 0.7146686911582947, "Y": 0.3385296165943146}]}, "Id": "42ff19c8-ddc7-4932-9a77-99ffe6048efd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "STEVENS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0786067321896553, "Height": 0.01847897283732891, "Left": 0.5287465453147888, "Top": 0.34107911586761475}, "Polygon": [{"X": 0.5300178527832031, "Y": 0.34107911586761475}, {"X": 0.6073532700538635, "Y": 0.3449086844921112}, {"X": 0.6060945987701416, "Y": 0.3595580756664276}, {"X": 0.5287465453147888, "Y": 0.3556383550167084}]}, "Id": "028e6f06-f312-4923-bee2-74989f5e83ec"}, {"BlockType": "WORD", "Confidence": 97.5693588256836, "Text": "POINT,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0680825337767601, "Height": 0.018188169226050377, "Left": 0.6186973452568054, "Top": 0.3423875868320465}, "Polygon": [{"X": 0.6199672222137451, "Y": 0.3423875868320465}, {"X": 0.6867799162864685, "Y": 0.3456795811653137}, {"X": 0.6855213642120361, "Y": 0.36057576537132263}, {"X": 0.6186973452568054, "Y": 0.3572050631046295}]}, "Id": "3da4869c-b5fd-40a4-9aa1-576b6ff557a0"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025757797062397003, "Height": 0.013400251045823097, "Left": 0.7004326581954956, "Top": 0.34442055225372314}, "Polygon": [{"X": 0.7014570236206055, "Y": 0.34442055225372314}, {"X": 0.726190447807312, "Y": 0.34563538432121277}, {"X": 0.7251695990562439, "Y": 0.3578208088874817}, {"X": 0.7004326581954956, "Y": 0.3565821945667267}]}, "Id": "3ae2f18f-fdac-4d74-89e1-975d87247022"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08984801918268204, "Height": 0.017460569739341736, "Left": 0.5166807770729065, "Top": 0.37530243396759033}, "Polygon": [{"X": 0.5177945494651794, "Y": 0.37530243396759033}, {"X": 0.6065287590026855, "Y": 0.37994128465652466}, {"X": 0.605427622795105, "Y": 0.3927629888057709}, {"X": 0.5166807770729065, "Y": 0.38803353905677795}]}, "Id": "82960e3a-a23c-44bb-8c77-601f131f293c"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "0098652", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08371987193822861, "Height": 0.018509257584810257, "Left": 0.6284283995628357, "Top": 0.3768938183784485}, "Polygon": [{"X": 0.6296361088752747, "Y": 0.3768938183784485}, {"X": 0.7121482491493225, "Y": 0.3811798095703125}, {"X": 0.7109538316726685, "Y": 0.39540305733680725}, {"X": 0.6284283995628357, "Y": 0.3910243809223175}]}, "Id": "bcd58e1c-88d3-4725-ad1e-8bcc0887ed0c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05800884589552879, "Height": 0.014057558961212635, "Left": 0.5119580626487732, "Top": 0.39278241991996765}, "Polygon": [{"X": 0.5129162669181824, "Y": 0.39278241991996765}, {"X": 0.5699669122695923, "Y": 0.39584508538246155}, {"X": 0.5690156817436218, "Y": 0.4068399667739868}, {"X": 0.5119580626487732, "Y": 0.4037271738052368}]}, "Id": "2d44314e-d29f-4ca1-8cfc-2f835b656641"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012853985652327538, "Height": 0.011650900356471539, "Left": 0.5920920968055725, "Top": 0.39644333720207214}, "Polygon": [{"X": 0.593039333820343, "Y": 0.39644333720207214}, {"X": 0.6049460768699646, "Y": 0.3970819413661957}, {"X": 0.6040002703666687, "Y": 0.40809425711631775}, {"X": 0.5920920968055725, "Y": 0.4074452221393585}]}, "Id": "f512f6fe-ef36-42df-a701-5d2d7f0a104a"}, {"BlockType": "WORD", "Confidence": 96.06525421142578, "Text": "0900-8652", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10701987147331238, "Height": 0.0201546773314476, "Left": 0.6276140213012695, "Top": 0.39372047781944275}, "Polygon": [{"X": 0.6288444995880127, "Y": 0.39372047781944275}, {"X": 0.7346339225769043, "Y": 0.3993557095527649}, {"X": 0.7334208488464355, "Y": 0.41387513279914856}, {"X": 0.6276140213012695, "Y": 0.40811875462532043}]}, "Id": "5c5c5eff-df50-420a-b271-00697de10fb9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045277729630470276, "Height": 0.013291515409946442, "Left": 0.511269748210907, "Top": 0.40863460302352905}, "Polygon": [{"X": 0.5122166275978088, "Y": 0.40863460302352905}, {"X": 0.5565474629402161, "Y": 0.4110702574253082}, {"X": 0.5556058883666992, "Y": 0.4219261109828949}, {"X": 0.511269748210907, "Y": 0.4194519519805908}]}, "Id": "2ddc9ce9-8569-43ea-ae25-88974ee8fc51"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "08/22/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12028734385967255, "Height": 0.02333524078130722, "Left": 0.6257559657096863, "Top": 0.41011282801628113}, "Polygon": [{"X": 0.6271825432777405, "Y": 0.41011282801628113}, {"X": 0.74604332447052, "Y": 0.41659823060035706}, {"X": 0.7446394562721252, "Y": 0.43344807624816895}, {"X": 0.6257559657096863, "Y": 0.4268048405647278}]}, "Id": "5b3568b7-db80-4c93-83ae-e73bb3fa8ac7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046290166676044464, "Height": 0.013878915458917618, "Left": 0.508510410785675, "Top": 0.42406120896339417}, "Polygon": [{"X": 0.5094992518424988, "Y": 0.42406120896339417}, {"X": 0.5548006296157837, "Y": 0.42660611867904663}, {"X": 0.5538175106048584, "Y": 0.4379401206970215}, {"X": 0.508510410785675, "Y": 0.4353541135787964}]}, "Id": "20555b92-876f-4041-8172-42d3b3083042"}, {"BlockType": "WORD", "Confidence": 99.52726745605469, "Text": "12:06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059261612594127655, "Height": 0.015356076881289482, "Left": 0.6249213218688965, "Top": 0.4294187128543854}, "Polygon": [{"X": 0.6259497404098511, "Y": 0.4294187128543854}, {"X": 0.6841829419136047, "Y": 0.4326847195625305}, {"X": 0.68316251039505, "Y": 0.4447748064994812}, {"X": 0.6249213218688965, "Y": 0.4414530396461487}]}, "Id": "bcabf323-6b4b-4b64-b060-e6827b46559a"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024019241333007812, "Height": 0.012604470364749432, "Left": 0.6959863901138306, "Top": 0.4325566589832306}, "Polygon": [{"X": 0.696936845779419, "Y": 0.4325566589832306}, {"X": 0.7200056314468384, "Y": 0.43384894728660583}, {"X": 0.7190581560134888, "Y": 0.4451611340045929}, {"X": 0.6959863901138306, "Y": 0.44384822249412537}]}, "Id": "5ef6895e-8090-41bc-ba1a-73b8bd535fb4"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046521540731191635, "Height": 0.017172368243336678, "Left": 0.5050460696220398, "Top": 0.4558645486831665}, "Polygon": [{"X": 0.5063125491142273, "Y": 0.4558645486831665}, {"X": 0.5515676140785217, "Y": 0.45852163434028625}, {"X": 0.5503084063529968, "Y": 0.47303691506385803}, {"X": 0.5050460696220398, "Y": 0.470327228307724}]}, "Id": "e2e525f4-dfe7-46ad-99ca-a39132876b60"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09413015842437744, "Height": 0.01984756626188755, "Left": 0.5635553598403931, "Top": 0.4593297243118286}, "Polygon": [{"X": 0.5647913217544556, "Y": 0.4593297243118286}, {"X": 0.6576855182647705, "Y": 0.4647841155529022}, {"X": 0.6564645171165466, "Y": 0.4791772663593292}, {"X": 0.5635553598403931, "Y": 0.47361668944358826}]}, "Id": "4fd27ff7-3dd8-4a5e-8e01-3e3cdb1b1f1a"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06071773171424866, "Height": 0.015817925333976746, "Left": 0.6816938519477844, "Top": 0.4650987386703491}, "Polygon": [{"X": 0.6827284693717957, "Y": 0.4650987386703491}, {"X": 0.7424116134643555, "Y": 0.4685977101325989}, {"X": 0.7413854002952576, "Y": 0.4809166491031647}, {"X": 0.6816938519477844, "Y": 0.47735968232154846}]}, "Id": "355a89e7-4936-4872-b044-df5711816cee"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02292364276945591, "Height": 0.012063010595738888, "Left": 0.5153754353523254, "Top": 0.4727410078048706}, "Polygon": [{"X": 0.5163123607635498, "Y": 0.4727410078048706}, {"X": 0.5382990837097168, "Y": 0.4740602970123291}, {"X": 0.5373647212982178, "Y": 0.4848040044307709}, {"X": 0.5153754353523254, "Y": 0.4834657907485962}]}, "Id": "5b2bc2cd-3e58-4a6d-8c87-02fa882dcd60"}, {"BlockType": "WORD", "Confidence": 98.71871185302734, "Text": "35.705", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07069551199674606, "Height": 0.015143468044698238, "Left": 0.5857092142105103, "Top": 0.477916419506073}, "Polygon": [{"X": 0.5866468548774719, "Y": 0.477916419506073}, {"X": 0.6564047336578369, "Y": 0.4821074306964874}, {"X": 0.6554756760597229, "Y": 0.4930598735809326}, {"X": 0.5857092142105103, "Y": 0.48880815505981445}]}, "Id": "60716d81-cb5c-4cd4-a345-9308d221021f"}, {"BlockType": "WORD", "Confidence": 99.45252227783203, "Text": "3.499", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06030720844864845, "Height": 0.01492949016392231, "Left": 0.6810753345489502, "Top": 0.4828633666038513}, "Polygon": [{"X": 0.6820298433303833, "Y": 0.4828633666038513}, {"X": 0.7413825392723083, "Y": 0.48642560839653015}, {"X": 0.7404357194900513, "Y": 0.4977928400039673}, {"X": 0.6810753345489502, "Y": 0.49417734146118164}]}, "Id": "02c73597-0a11-4e9c-ae42-1e9d99c7999e"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08353032171726227, "Height": 0.01727416180074215, "Left": 0.4994858503341675, "Top": 0.5036824941635132}, "Polygon": [{"X": 0.5005402565002441, "Y": 0.5036824941635132}, {"X": 0.5830161571502686, "Y": 0.508840024471283}, {"X": 0.5819728374481201, "Y": 0.5209566950798035}, {"X": 0.4994858503341675, "Y": 0.5157192349433899}]}, "Id": "3e44dc98-fc51-401b-8cf3-24885edf91b1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07307938486337662, "Height": 0.01544715091586113, "Left": 0.6674906015396118, "Top": 0.5162259340286255}, "Polygon": [{"X": 0.668408989906311, "Y": 0.5162259340286255}, {"X": 0.7405699491500854, "Y": 0.5207499861717224}, {"X": 0.7396605610847473, "Y": 0.5316730737686157}, {"X": 0.6674906015396118, "Y": 0.5270867943763733}]}, "Id": "1cded4bc-1cfe-49a1-aa84-6e63d2358715"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047476235777139664, "Height": 0.014200584962964058, "Left": 0.4977375566959381, "Top": 0.5205289721488953}, "Polygon": [{"X": 0.49871769547462463, "Y": 0.5205289721488953}, {"X": 0.5452137589454651, "Y": 0.523499071598053}, {"X": 0.5442394018173218, "Y": 0.534729540348053}, {"X": 0.4977375566959381, "Y": 0.5317174792289734}]}, "Id": "6c9ff3d6-fe50-4889-9104-a7b0d1b2c26d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "124.93", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07357583940029144, "Height": 0.015956813469529152, "Left": 0.6666455864906311, "Top": 0.5328824520111084}, "Polygon": [{"X": 0.6675962209701538, "Y": 0.5328824520111084}, {"X": 0.7402214407920837, "Y": 0.5375306010246277}, {"X": 0.739280104637146, "Y": 0.548839271068573}, {"X": 0.6666455864906311, "Y": 0.5441262125968933}]}, "Id": "3f9e4716-a5d5-4cfe-bb96-5fb626a63767"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062072593718767166, "Height": 0.015767522156238556, "Left": 0.49231427907943726, "Top": 0.5547561645507812}, "Polygon": [{"X": 0.49333471059799194, "Y": 0.5547561645507812}, {"X": 0.5543868541717529, "Y": 0.5588237047195435}, {"X": 0.5533742904663086, "Y": 0.5705236792564392}, {"X": 0.49231427907943726, "Y": 0.5663989186286926}]}, "Id": "4370e84a-ba80-4322-9f49-31a951325073"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "124.93", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07373392581939697, "Height": 0.016633005812764168, "Left": 0.6530899405479431, "Top": 0.5669887065887451}, "Polygon": [{"X": 0.6540822386741638, "Y": 0.5669887065887451}, {"X": 0.7268238663673401, "Y": 0.571843683719635}, {"X": 0.725841224193573, "Y": 0.5836216807365417}, {"X": 0.6530899405479431, "Y": 0.5786989331245422}]}, "Id": "a52b0d17-f90c-4b6d-b8e4-6d2f43afd5ea"}, {"BlockType": "WORD", "Confidence": 99.76243591308594, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09874750673770905, "Height": 0.020790452137589455, "Left": 0.****************, "Top": 0.5909909009933472}, "Polygon": [{"X": 0.****************, "Y": 0.5909909009933472}, {"X": 0.5877705812454224, "Y": 0.5977708101272583}, {"X": 0.5865672826766968, "Y": 0.6117813587188721}, {"X": 0.****************, "Y": 0.6048920750617981}]}, "Id": "dd8f9cd8-1a2e-49f5-8ee7-af023c13f44e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05001310631632805, "Height": 0.015127147547900677, "Left": 0.5991912484169006, "Top": 0.5991681218147278}, "Polygon": [{"X": 0.600191056728363, "Y": 0.5991681218147278}, {"X": 0.6492043733596802, "Y": 0.6025773882865906}, {"X": 0.6482110023498535, "Y": 0.614295244216919}, {"X": 0.5991912484169006, "Y": 0.6108402013778687}]}, "Id": "4e1a9d94-bcbb-4e88-98dd-0caf4421a9a6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06236898899078369, "Height": 0.01671978272497654, "Left": 0.662011444568634, "Top": 0.6036802530288696}, "Polygon": [{"X": 0.6630589962005615, "Y": 0.6036802530288696}, {"X": 0.7243804335594177, "Y": 0.6079463958740234}, {"X": 0.7233415842056274, "Y": 0.620400071144104}, {"X": 0.662011444568634, "Y": 0.6160734295845032}]}, "Id": "06b87db6-9827-4ef6-9e81-9154df8e5fb7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06238090619444847, "Height": 0.016240717843174934, "Left": 0.*****************, "Top": 0.6093183159828186}, "Polygon": [{"X": 0.***************, "Y": 0.6093183159828186}, {"X": 0.549446165561676, "Y": 0.6136727333068848}, {"X": 0.****************, "Y": 0.6255590319633484}, {"X": 0.*****************, "Y": 0.621146023273468}]}, "Id": "264253ea-3ebf-41bc-a55e-64c1564ec7d2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034906819462776184, "Height": 0.01415269635617733, "Left": 0.5625125765800476, "Top": 0.6146863698959351}, "Polygon": [{"X": 0.5635234713554382, "Y": 0.6146863698959351}, {"X": 0.5974193811416626, "Y": 0.6170924305915833}, {"X": 0.5964129567146301, "Y": 0.6288390159606934}, {"X": 0.5625125765800476, "Y": 0.6264010667800903}]}, "Id": "3a7141af-d13e-4687-a877-ca6f20c175fd"}, {"BlockType": "WORD", "Confidence": 90.525146484375, "Text": "1591", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042111292481422424, "Height": 0.015403363853693008, "Left": 0.6622132062911987, "Top": 0.6409195065498352}, "Polygon": [{"X": 0.6632594466209412, "Y": 0.6409195065498352}, {"X": 0.7043245434761047, "Y": 0.6438961029052734}, {"X": 0.7032841444015503, "Y": 0.****************}, {"X": 0.6622132062911987, "Y": 0.****************}]}, "Id": "c0c1a0ad-305c-47a5-a670-62383d847a19"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049030665308237076, "Height": 0.015415823087096214, "Left": 0.48249632120132446, "Top": 0.6457449793815613}, "Polygon": [{"X": 0.48353293538093567, "Y": 0.6457449793815613}, {"X": 0.5315269827842712, "Y": 0.6492918729782104}, {"X": 0.5304965972900391, "Y": 0.6611608266830444}, {"X": 0.48249632120132446, "Y": 0.6575680375099182}]}, "Id": "c1a7346d-31de-4f2d-b7be-2604e2ae12a1"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01425084937363863, "Height": 0.012783830985426903, "Left": 0.5429027676582336, "Top": 0.6498456597328186}, "Polygon": [{"X": 0.5439240336418152, "Y": 0.6498456597328186}, {"X": 0.5571536421775818, "Y": 0.6508229970932007}, {"X": 0.5561341643333435, "Y": 0.6626294851303101}, {"X": 0.5429027676582336, "Y": 0.6616396307945251}]}, "Id": "68cab67c-10d7-4bfc-af9d-204f34f34435"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "P18446", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07571911066770554, "Height": 0.01879880763590336, "Left": 0.6550054550170898, "Top": 0.6588646769523621}, "Polygon": [{"X": 0.6561221480369568, "Y": 0.6588646769523621}, {"X": 0.730724573135376, "Y": 0.6643801331520081}, {"X": 0.7296191453933716, "Y": 0.6776635050773621}, {"X": 0.6550054550170898, "Y": 0.6720694899559021}]}, "Id": "f56bd413-b698-4193-b882-519312dbe91e"}, {"BlockType": "WORD", "Confidence": 98.75458526611328, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10725177824497223, "Height": 0.02074923925101757, "Left": 0.48035329580307007, "Top": 0.6633877754211426}, "Polygon": [{"X": 0.4814622402191162, "Y": 0.6633877754211426}, {"X": 0.5876050591468811, "Y": 0.6713821291923523}, {"X": 0.5865110754966736, "Y": 0.6841370463371277}, {"X": 0.48035329580307007, "Y": 0.676034152507782}]}, "Id": "32c6452a-9416-4204-8d87-7c758f1aa36d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10027208179235458, "Height": 0.020940497517585754, "Left": 0.6029235124588013, "Top": 0.6728518009185791}, "Polygon": [{"X": 0.604066014289856, "Y": 0.6728518009185791}, {"X": 0.7031956315040588, "Y": 0.6803197860717773}, {"X": 0.7020680904388428, "Y": 0.6937923431396484}, {"X": 0.6029235124588013, "Y": 0.6862183213233948}]}, "Id": "90f94ba3-ae4a-40e9-87f9-acfd64992b50"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04885348305106163, "Height": 0.015045066364109516, "Left": 0.4903776943683624, "Top": 0.6825313568115234}, "Polygon": [{"X": 0.49136853218078613, "Y": 0.6825313568115234}, {"X": 0.5392311811447144, "Y": 0.6862061619758606}, {"X": 0.5382463335990906, "Y": 0.6975764632225037}, {"X": 0.4903776943683624, "Y": 0.6938579082489014}]}, "Id": "f9d1506b-0071-48ca-bce4-04108d776fc0"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05825022980570793, "Height": 0.016583185642957687, "Left": 0.5517262816429138, "Top": 0.686667799949646}, "Polygon": [{"X": 0.5527746677398682, "Y": 0.686667799949646}, {"X": 0.6099765300750732, "Y": 0.691057026386261}, {"X": 0.6089358329772949, "Y": 0.7032510042190552}, {"X": 0.5517262816429138, "Y": 0.6988059878349304}]}, "Id": "2f765ae3-eb02-48f0-be4e-f7ea0be3a03e"}, {"BlockType": "WORD", "Confidence": 99.5322494506836, "Text": "P18446", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07514884322881699, "Height": 0.018006866797804832, "Left": 0.625554084777832, "Top": 0.6935280561447144}, "Polygon": [{"X": 0.6265950798988342, "Y": 0.6935280561447144}, {"X": 0.7007029056549072, "Y": 0.6992214918136597}, {"X": 0.6996722221374512, "Y": 0.7115349769592285}, {"X": 0.625554084777832, "Y": 0.7057690620422363}]}, "Id": "0253aa9d-cc94-4e3e-a101-058bd3ebf002"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "ec26b8a9-f747-4a33-adc8-689a76424965", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "ec26b8a9-f747-4a33-adc8-689a76424965", "content-type": "application/x-amz-json-1.1", "content-length": "35316", "date": "Thu, 18 Sep 2025 08:51:29 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "80b5af6b-8be2-4f32-adf0-2cdb7a439cf4", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:32 GMT", "content-type": "application/json", "content-length": "1440", "connection": "keep-alive", "x-amzn-requestid": "80b5af6b-8be2-4f32-adf0-2cdb7a439cf4"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user has provided a single page, presumably a fuel receipt or invoice for fuel. Let's analyze the content:\n\n- It shows \"Invoice# 0098652\", \"Trans # 0900-8652\", \"Date 08/22/2025\", \"Time 12:06 PM\", \"Pump Quantity Price\", \"02 35.705 3.499\", \"Product DSL2\", \"Amount 124.93\". Then \"TOTAL 124.93\". \"NBS/KWIK TRIP FLEET CARD# ICR 1591 AUTH # P18446 RESPONSE: APPROVED AUTH REF#: P18446\".\n\nThis looks like a fuel receipt, i.e., a fuel purchase receipt. The presence of \"Pump Quantity Price\" and \"Product DSL2\" indicates a fuel product. \"NBS/KWIK TRIP FLEET\" looks like a fuel card company. \"CARD# ICR 1591\" indicates a card number. \"AUTH #\" etc. So this is a fuel purchase receipt. That matches the \"fuel_receipt\" doc_type.\n\nWe must output a JSON object via the function call: classify_logistics_doc_type with an array containing this page. The page number is 1. So we call the tool with \"documents\": [ { page_no: 1, doc_type: \"fuel_receipt\" } ].\n\nWe must not add anything else. Let's do that."}}}, {"toolUse": {"toolUseId": "tooluse_8qj1x2rdT06t4UiG6gAmCQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2233, "outputTokens": 330, "totalTokens": 2563}, "metrics": {"latencyMs": 1482}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/ce7c808e_9_fuel_receipt_9.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n5311 OLD HIGHWAY T8\nSTEVENS POINT, WI\nInvoice# 0098652\nTrans # 0900-8652\nDate\n08/22/2025\nTime\n12:06 PM\nPump Quantity Price\n02\n35.705 3.499\nProduct\nAmount\nDSL2\n124.93\nTOTAL\n124.93\nNBS/KWIK TRIP FLEET\nCARD# ICR\n1591\nAUTH #\nP18446\nRESPONSE: APPROVED\nAUTH REF#: P18446\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}