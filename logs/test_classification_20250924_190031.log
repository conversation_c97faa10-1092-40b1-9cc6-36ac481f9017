2025-09-24 19:00:31,105 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190031.log
2025-09-24 19:00:31,105 - INFO - 📁 Found 12 files to process
2025-09-24 19:00:31,105 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:00:31,105 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 19:00:31,105 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 19:00:31,105 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 19:00:31,105 - INFO - ⬆️ [19:00:31] Uploading: BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:32,825 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf -> s3://document-extraction-logistically/temp/7766ba31_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:32,825 - INFO - 🔍 [19:00:32] Starting classification: BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:32,826 - INFO - ⬆️ [19:00:32] Uploading: E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:00:32,826 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:32,850 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:32,858 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7766ba31_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:32,858 - INFO - Processing PDF from S3...
2025-09-24 19:00:32,858 - INFO - Downloading PDF from S3 to /tmp/tmp17m3fj1j/7766ba31_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:34,154 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:00:34,154 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:34,155 - INFO - Splitting PDF 7766ba31_BJYFRYT0V8O045HGDXJ6 into 1 pages
2025-09-24 19:00:34,163 - INFO - Split PDF into 1 pages
2025-09-24 19:00:34,163 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:34,163 - INFO - Expected pages: [1]
2025-09-24 19:00:37,627 - INFO - Page 1: Extracted 395 characters, 28 lines from 7766ba31_BJYFRYT0V8O045HGDXJ6_3a3cd914_page_001.pdf
2025-09-24 19:00:37,627 - INFO - Successfully processed page 1
2025-09-24 19:00:37,628 - INFO - Combined 1 pages into final text
2025-09-24 19:00:37,628 - INFO - Text validation for 7766ba31_BJYFRYT0V8O045HGDXJ6: 412 characters, 1 pages
2025-09-24 19:00:37,628 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:37,628 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:38,148 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg -> s3://document-extraction-logistically/temp/3664413d_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:00:38,148 - INFO - 🔍 [19:00:38] Starting classification: E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:00:38,149 - INFO - ⬆️ [19:00:38] Uploading: EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:38,150 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:38,173 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:38,180 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3664413d_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:00:38,181 - INFO - Processing image from S3...
2025-09-24 19:00:38,764 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf -> s3://document-extraction-logistically/temp/807ed646_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:38,764 - INFO - 🔍 [19:00:38] Starting classification: EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:38,765 - INFO - ⬆️ [19:00:38] Uploading: GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:38,767 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:38,785 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:38,790 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/807ed646_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:38,790 - INFO - Processing PDF from S3...
2025-09-24 19:00:38,791 - INFO - Downloading PDF from S3 to /tmp/tmp5r4cmru2/807ed646_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:39,395 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf -> s3://document-extraction-logistically/temp/57bc3959_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:39,395 - INFO - 🔍 [19:00:39] Starting classification: GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:39,396 - INFO - ⬆️ [19:00:39] Uploading: HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:00:39,398 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:39,410 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:39,413 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/57bc3959_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:39,413 - INFO - Processing PDF from S3...
2025-09-24 19:00:39,414 - INFO - Downloading PDF from S3 to /tmp/tmp_jz5nco6/57bc3959_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:39,704 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f8b9b732-642b-43b5-afe6-08f3937a3208', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:39 GMT', 'content-type': 'application/json', 'content-length': '1009', 'connection': 'keep-alive', 'x-amzn-requestid': 'f8b9b732-642b-43b5-afe6-08f3937a3208'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page extracted. It\'s a "J1 GATE RECEIPT" with header: "J1 GATE RECEIPT". The page shows "Out-Gate". That indicates maybe an outgate document. The description is an "Outgate" record. According to classification, outgate: "Outgate Document: Record of vehicle/container exiting a facility (gate-out/release)." Keywords: Outgate, Departed, Gate Out, Time Out, Release, Exit Time. This page definitely has Out-Gate. So classify as "outgate".\n\nAlso note it\'s a multi-page? Only page 1 shown. It\'s a single page. So output JSON with page_no 1, doc_type "outgate".\n\nWe must use tool call classify_logistics_doc_type. Let\'s produce that call with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2731, 'outputTokens': 185, 'totalTokens': 2916}, 'metrics': {'latencyMs': 938}}
2025-09-24 19:00:39,704 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7766ba31_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:39,994 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:00:39,994 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:39,995 - INFO - Splitting PDF 807ed646_EKMN29XJBZVFPLOYTKU7 into 1 pages
2025-09-24 19:00:39,997 - INFO - Split PDF into 1 pages
2025-09-24 19:00:39,997 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:39,997 - INFO - Expected pages: [1]
2025-09-24 19:00:40,096 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg -> s3://document-extraction-logistically/temp/344e5e16_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:00:40,097 - INFO - 🔍 [19:00:40] Starting classification: HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:00:40,098 - INFO - ⬆️ [19:00:40] Uploading: OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:00:40,104 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:40,118 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:40,123 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/344e5e16_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:00:40,123 - INFO - Processing image from S3...
2025-09-24 19:00:40,914 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf -> s3://document-extraction-logistically/temp/839e891d_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:00:40,914 - INFO - 🔍 [19:00:40] Starting classification: OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:00:40,915 - INFO - ⬆️ [19:00:40] Uploading: T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:00:40,917 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:40,938 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:40,942 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/839e891d_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:00:40,942 - INFO - Processing PDF from S3...
2025-09-24 19:00:40,943 - INFO - Downloading PDF from S3 to /tmp/tmpbdx3i14r/839e891d_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:00:41,013 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:00:41,014 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:41,015 - INFO - Splitting PDF 57bc3959_GHXKMFASS6CGN362R5BW into 1 pages
2025-09-24 19:00:41,021 - INFO - Split PDF into 1 pages
2025-09-24 19:00:41,022 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:41,022 - INFO - Expected pages: [1]
2025-09-24 19:00:41,616 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png -> s3://document-extraction-logistically/temp/2d5e2ca0_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:00:41,617 - INFO - 🔍 [19:00:41] Starting classification: T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:00:41,617 - INFO - ⬆️ [19:00:41] Uploading: UGM225037492OGWQHUHR.png
2025-09-24 19:00:41,623 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:41,638 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:41,676 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2d5e2ca0_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:00:41,676 - INFO - Processing image from S3...
2025-09-24 19:00:42,244 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png -> s3://document-extraction-logistically/temp/6d41ce24_UGM225037492OGWQHUHR.png
2025-09-24 19:00:42,244 - INFO - 🔍 [19:00:42] Starting classification: UGM225037492OGWQHUHR.png
2025-09-24 19:00:42,245 - INFO - ⬆️ [19:00:42] Uploading: VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:42,245 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:42,256 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:42,265 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6d41ce24_UGM225037492OGWQHUHR.png
2025-09-24 19:00:42,265 - INFO - Processing image from S3...
2025-09-24 19:00:42,930 - INFO - S3 Image temp/3664413d_E88AUSUP8065SEI9GD5Q.jpg: Extracted 516 characters, 26 lines
2025-09-24 19:00:42,930 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:42,931 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:42,938 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf -> s3://document-extraction-logistically/temp/f129c4b7_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:42,939 - INFO - 🔍 [19:00:42] Starting classification: VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:42,939 - INFO - ⬆️ [19:00:42] Uploading: WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:00:42,941 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:42,959 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:42,964 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f129c4b7_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:42,965 - INFO - Processing PDF from S3...
2025-09-24 19:00:42,965 - INFO - Downloading PDF from S3 to /tmp/tmp13gpi29u/f129c4b7_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:44,026 - INFO - S3 Image temp/2d5e2ca0_T5U48WX7H3FNSZPBDBLZ.png: Extracted 199 characters, 25 lines
2025-09-24 19:00:44,026 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:44,026 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:44,108 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf -> s3://document-extraction-logistically/temp/6ba263b5_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:00:44,108 - INFO - 🔍 [19:00:44] Starting classification: WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:00:44,109 - INFO - ⬆️ [19:00:44] Uploading: YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:44,110 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:44,131 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:44,135 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ba263b5_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:00:44,135 - INFO - Processing PDF from S3...
2025-09-24 19:00:44,135 - INFO - Downloading PDF from S3 to /tmp/tmpsv0oxhp6/6ba263b5_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:00:44,791 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf -> s3://document-extraction-logistically/temp/a4b5cc5d_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:44,791 - INFO - 🔍 [19:00:44] Starting classification: YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:44,792 - INFO - ⬆️ [19:00:44] Uploading: ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:44,795 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:44,817 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:44,820 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a4b5cc5d_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:44,820 - INFO - Processing PDF from S3...
2025-09-24 19:00:44,820 - INFO - Downloading PDF from S3 to /tmp/tmp46adpk0s/a4b5cc5d_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:45,220 - INFO - Page 1: Extracted 404 characters, 28 lines from 57bc3959_GHXKMFASS6CGN362R5BW_f6b98a2f_page_001.pdf
2025-09-24 19:00:45,220 - INFO - Successfully processed page 1
2025-09-24 19:00:45,221 - INFO - Combined 1 pages into final text
2025-09-24 19:00:45,221 - INFO - Text validation for 57bc3959_GHXKMFASS6CGN362R5BW: 421 characters, 1 pages
2025-09-24 19:00:45,221 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:45,221 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:45,338 - INFO - S3 Image temp/6d41ce24_UGM225037492OGWQHUHR.png: Extracted 223 characters, 18 lines
2025-09-24 19:00:45,338 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:45,338 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:45,351 - INFO - Page 1: Extracted 525 characters, 25 lines from 807ed646_EKMN29XJBZVFPLOYTKU7_e8c1c297_page_001.pdf
2025-09-24 19:00:45,351 - INFO - Successfully processed page 1
2025-09-24 19:00:45,351 - INFO - Combined 1 pages into final text
2025-09-24 19:00:45,351 - INFO - Text validation for 807ed646_EKMN29XJBZVFPLOYTKU7: 542 characters, 1 pages
2025-09-24 19:00:45,351 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:45,351 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:45,578 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:00:45,579 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:45,580 - INFO - Splitting PDF f129c4b7_VY0AAD98JWYCZUAHKROB into 1 pages
2025-09-24 19:00:45,582 - INFO - Split PDF into 1 pages
2025-09-24 19:00:45,582 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:45,582 - INFO - Expected pages: [1]
2025-09-24 19:00:45,639 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8934fbd5-d576-4675-948d-b5a17cb9de72', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:45 GMT', 'content-type': 'application/json', 'content-length': '1723', 'connection': 'keep-alive', 'x-amzn-requestid': '8934fbd5-d576-4675-948d-b5a17cb9de72'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. Let\'s analyze content.\n\nIt\'s some terminal document. HUSKY Terminal And Stevedoring, Inc. YANG MING LINE #776055 Lane : 45 FULL-OUT IMPORT-OUT EIR. Container info, vessel, etc. Looks like an Import-OUT EIR, likely a "Export Inward Receipt"? EIR stands for Export Inward Receipt? Actually "EIR" is "Export Inward Receipt" used in India. This is a customs document? But check categories: customs_doc, combined_carrier_documents, etc. The keywords: Container, Gross Weight, Seal intact, etc. There\'s no explicit header like "Bill of Lading". It\'s an "EIR" which is a customs document, but not exactly "Customs Doc" category? Within the provided categories, "customs_doc" refers to general customs paperwork like declarations, certificates, etc. This page seems to be a customs "Export Inward Receipt" for container shipping, which is a customs document. So likely "customs_doc".\n\nCheck if any other category: "scale_ticket"? It has weight, but also container info. But scale ticket focuses on weight measurements; a scale ticket might include gross weight. But this page also includes vessel, etc. "scale_ticket" is weight record from a scale for vehicle/load. It may not include container details maybe. "customs_doc" seems more appropriate.\n\nThus assign doc_type: customs_doc.\n\nReturn JSON with page_no 1, doc_type "customs_doc".\n\nYes.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2780, 'outputTokens': 335, 'totalTokens': 3115}, 'metrics': {'latencyMs': 1614}}
2025-09-24 19:00:45,639 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3664413d_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:00:45,702 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf -> s3://document-extraction-logistically/temp/8274c635_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:45,702 - INFO - 🔍 [19:00:45] Starting classification: ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:45,703 - INFO - Initializing TextractProcessor...
2025-09-24 19:00:45,716 - INFO - Initializing BedrockProcessor...
2025-09-24 19:00:45,722 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8274c635_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:45,724 - INFO - Processing PDF from S3...
2025-09-24 19:00:45,725 - INFO - Downloading PDF from S3 to /tmp/tmpy9256_ht/8274c635_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:45,726 - INFO - 

BJYFRYT0V8O045HGDXJ6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:45,732 - INFO - 

✓ Saved result: output/run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 19:00:46,047 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7766ba31_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:00:46,055 - INFO - 

E88AUSUP8065SEI9GD5Q.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 19:00:46,055 - INFO - 

✓ Saved result: output/run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 19:00:46,340 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3664413d_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:00:46,482 - INFO - S3 Image temp/344e5e16_HZNQ971O2V9MGG5MU1T3.jpeg: Extracted 655 characters, 33 lines
2025-09-24 19:00:46,482 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:46,482 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:46,504 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:00:46,504 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:46,506 - INFO - Splitting PDF a4b5cc5d_YASUN3YXXTIFXRHDIKNF into 1 pages
2025-09-24 19:00:46,509 - INFO - Split PDF into 1 pages
2025-09-24 19:00:46,510 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:46,510 - INFO - Expected pages: [1]
2025-09-24 19:00:46,702 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd401d876-6eaa-4e31-972f-1cab3e57a16a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:46 GMT', 'content-type': 'application/json', 'content-length': '1455', 'connection': 'keep-alive', 'x-amzn-requestid': 'd401d876-6eaa-4e31-972f-1cab3e57a16a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content. We need to parse and classify. The page shows "License", "Activity", "Container", "Chassis", "Genset", "Line", "Cat", "Weight (MT)", "Sz/Tp", "Vessel/Voyage", "Date", "Time In", "Time Out", and some data: D12713DWA, FULL OUT, MEDU4189050, OWN, MSC, Import, 23, 40DR, MSCLUCI/524A, 07/16/2025, 9:54 AM, 10:44 AM.\n\nThis looks like an Ingate or Outgate? "Time In" and "Time Out" suggests a gate log. Also "License", "Activity", "Container" etc. It might be a gate log.\n\nWe see "FULL OUT" maybe indicates an outgate event. The presence of "Time In" and "Time Out" and "Vessel/Voyage" indicates a gate record. "Activity" could be "FULL OUT". So likely "outgate". The doc type is outgate. Check list: outgate definition: Record of vehicle/container exiting facility, timestamps, release stamp, exit time. Keys: Time Out, Release, Exit Time. This page has Time In and Time Out. Also "FULL OUT" maybe indicates outgate. So classify as outgate.\n\nThus output: page 1, doc_type "outgate".\n\nWe need to output using the tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2678, 'outputTokens': 333, 'totalTokens': 3011}, 'metrics': {'latencyMs': 1514}}
2025-09-24 19:00:46,702 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2d5e2ca0_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:00:46,710 - INFO - 

T5U48WX7H3FNSZPBDBLZ.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:46,710 - INFO - 

✓ Saved result: output/run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 19:00:46,990 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2d5e2ca0_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:00:47,074 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b6b8ea92-057e-4699-b39b-6669e6c66023', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:46 GMT', 'content-type': 'application/json', 'content-length': '625', 'connection': 'keep-alive', 'x-amzn-requestid': 'b6b8ea92-057e-4699-b39b-6669e6c66023'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have single page. We need to classify. The content indicates "OUT-GATE", "Equipment Inspection Record", "Outgate: STPAUL MN 07/28/2025 1530", "Release ID", "Gross WGT", etc. This matches "outgate" definition. So doc_type: outgate. Output JSON with documents list containing one object page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2788, 'outputTokens': 99, 'totalTokens': 2887}, 'metrics': {'latencyMs': 645}}
2025-09-24 19:00:47,074 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/807ed646_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:47,091 - INFO - 

EKMN29XJBZVFPLOYTKU7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:47,091 - INFO - 

✓ Saved result: output/run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 19:00:47,350 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c103c82f-5cd9-447e-921b-a9935ed2a362', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:47 GMT', 'content-type': 'application/json', 'content-length': '1058', 'connection': 'keep-alive', 'x-amzn-requestid': 'c103c82f-5cd9-447e-921b-a9935ed2a362'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. The content: "J1 GATE RECEIPT" "Ingate/outgate?" It says "J1 RECEIPT DETAILS" "Activity", "Associated Load or Seal(s)", "Booking #", "Transportation Company", "Date & Time", "Equipment", "Empty & Driver", "Out-Gate". It appears to be an Outgate receipt. The heading says "J1 GATE RECEIPT" and "Out-Gate". So doc type "outgate". Let\'s confirm: outgate document - record of vehicle/container exiting a facility. Keywords: Outgate, Departed, Gate Out, Time Out, Release, Exit Time. This page includes "Out-Gate". So yes. So output:\n\n{\n "documents": [\n   {"page_no": 1, "doc_type": "outgate"}\n ]\n}\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2735, 'outputTokens': 211, 'totalTokens': 2946}, 'metrics': {'latencyMs': 998}}
2025-09-24 19:00:47,351 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/57bc3959_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:47,374 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/807ed646_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:00:47,387 - INFO - 

GHXKMFASS6CGN362R5BW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:47,388 - INFO - 

✓ Saved result: output/run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 19:00:47,677 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/57bc3959_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:00:47,721 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5c343fc3-7ec7-4231-bb79-6352ff964656', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:47 GMT', 'content-type': 'application/json', 'content-length': '1248', 'connection': 'keep-alive', 'x-amzn-requestid': '5c343fc3-7ec7-4231-bb79-6352ff964656'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page document. We need to classify document type.\n\nText: "Georgia Ports Authority, Savan Deliver Import Container 3501361 EVE:EVERGREEN SHIPPING AGENCY DRJK 01-Mar-2023 06:39:43 Truck ID Container EGHU9787630 Size/Type 40_DRY_9.5 Chassis 592919 Booking Vessel Temp Seals EMCNXN0202"\n\nThis looks like a container delivery/arrival document. Possibly a container arrival notice or gate log. Which categories? "ingate" or "outgate". The page contains "Truck ID", "Container", "Seals", "Booking". Maybe a gate-in document (ingate). The presence of "Georgia Ports Authority" suggests a port gate. "Deliver Import Container" suggests it\'s an import container arrival. Ingate documents record vehicle/container entering a facility. So likely ingate.\n\nCheck if any other categories: "log" could be a log of activity. But ingate is more precise.\n\nThus doc_type: ingate.\n\nReturn JSON accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2673, 'outputTokens': 237, 'totalTokens': 2910}, 'metrics': {'latencyMs': 1238}}
2025-09-24 19:00:47,721 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6d41ce24_UGM225037492OGWQHUHR.png
2025-09-24 19:00:47,732 - INFO - 

UGM225037492OGWQHUHR.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 19:00:47,733 - INFO - 

✓ Saved result: output/run1_UGM225037492OGWQHUHR.json
2025-09-24 19:00:48,081 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6d41ce24_UGM225037492OGWQHUHR.png
2025-09-24 19:00:48,289 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:00:48,289 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:48,290 - INFO - Splitting PDF 8274c635_ZAJMVPYOM22ETECQRW3L into 1 pages
2025-09-24 19:00:48,292 - INFO - Split PDF into 1 pages
2025-09-24 19:00:48,292 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:48,292 - INFO - Expected pages: [1]
2025-09-24 19:00:49,721 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '87569f47-d495-41ea-9487-ce25528a9f5c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:49 GMT', 'content-type': 'application/json', 'content-length': '1959', 'connection': 'keep-alive', 'x-amzn-requestid': '87569f47-d495-41ea-9487-ce25528a9f5c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to determine the document type for each page. We have only page 1 content. We need to classify.\n\nLet\'s read page content: "Interchange Receipt (J1)", "BNSF Railway Company", "Equipment Inspection Record", "YMMU 405514 LOAD", "FLAT 1234", "Outgate: LOGPARCHI IL 07/07/2025 2315", "Patron: YANGMINAMERI", "Trucker: DPBN IC To:", "License: 5177", "Seal: YMAT715808", "Cust: YANGMINMARLI", "Release ID: 6555CC", "Gross WGT: 51762", "Unit cannot return empty w/out waybill", "Damage:", "PHOTOGRAPHIC INSPECTION - Equipment defects are captured on recorded images.", "Vessel Nme: YM UNANIMITY", "Nbr: 080E Bkg:", "Intermodal Safe Container Certification", "Cargo Weight: 43162 lbs", "Certify Party: YANG MING NO", "Certify Date: 06/23/2025", "Commodity: PLATES NON-C", "Conversion Party: BNSF RAILWAY CO", "Conversion Date: 07/07/2025"\n\nThis looks like a container load outgate inspection record. Could be an outgate document. The keywords: "Outgate:", "Equipment Inspection Record", "Release ID", "Gross WGT", "Seal", "Unit cannot return empty w/out waybill". This could be an outgate or ingate type? It appears to be an "Outgate" document: record of vehicle/container exiting a facility. Indeed the Outgate: LOGPARCHI IL 07/07/2025 2315 indicates outgate time. So doc_type likely outgate.\n\nCheck that outgate definition: Keywords: Outgate, Departed, Gate Out, Time Out, Release, Exit Time. We have Outgate, Release ID, etc. So yes outgate.\n\nThus we output:\n\n{\n  "documents": [\n    { "page_no": 1, "doc_type": "outgate" }\n  ]\n}\n\nWe must use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2817, 'outputTokens': 478, 'totalTokens': 3295}, 'metrics': {'latencyMs': 2069}}
2025-09-24 19:00:49,721 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/344e5e16_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:00:49,737 - INFO - 

HZNQ971O2V9MGG5MU1T3.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:49,738 - INFO - 

✓ Saved result: output/run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 19:00:50,026 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/344e5e16_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:00:50,817 - INFO - Page 1: Extracted 840 characters, 41 lines from f129c4b7_VY0AAD98JWYCZUAHKROB_f6b1955b_page_001.pdf
2025-09-24 19:00:50,818 - INFO - Successfully processed page 1
2025-09-24 19:00:50,818 - INFO - Combined 1 pages into final text
2025-09-24 19:00:50,818 - INFO - Text validation for f129c4b7_VY0AAD98JWYCZUAHKROB: 857 characters, 1 pages
2025-09-24 19:00:50,819 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:50,819 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:50,911 - INFO - Page 1: Extracted 486 characters, 22 lines from a4b5cc5d_YASUN3YXXTIFXRHDIKNF_c08f95c0_page_001.pdf
2025-09-24 19:00:50,912 - INFO - Successfully processed page 1
2025-09-24 19:00:50,912 - INFO - Combined 1 pages into final text
2025-09-24 19:00:50,912 - INFO - Text validation for a4b5cc5d_YASUN3YXXTIFXRHDIKNF: 503 characters, 1 pages
2025-09-24 19:00:50,913 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:50,913 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:51,112 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:00:51,112 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:51,114 - INFO - Splitting PDF 839e891d_OO7629V25ZJ22FHL0PAA into 2 pages
2025-09-24 19:00:51,128 - INFO - Split PDF into 2 pages
2025-09-24 19:00:51,128 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:51,128 - INFO - Expected pages: [1, 2]
2025-09-24 19:00:53,513 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5aea5b79-2d5f-416b-8de6-ef7560d15ca4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:53 GMT', 'content-type': 'application/json', 'content-length': '1627', 'connection': 'keep-alive', 'x-amzn-requestid': '5aea5b79-2d5f-416b-8de6-ef7560d15ca4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given: <page1> content. We need to determine doc_type. Let\'s parse content. It seems to be an approval form for container reuse. Keywords: "APPROVED", "MAERSK", "Reuse empty container", "Container number", "Export booking ref.", "Reuse date", "Import location", "Export location", "Price", "SCAC", "COMMENT". This looks like a container approval form, perhaps internal. It doesn\'t match any of defined types. Let\'s review: invoice, comm_invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, other, tender_from_cust, so_confirmation, ingate, outgate.\n\nThis page: no invoice, no BOL, no POD, no rate confirmation. It\'s not a purchase order. It\'s not a log. It is maybe a "clear to pay"? No. It\'s maybe a "combined_carrier_documents"? No, only one header. So classify as "other". Also check for continuation: It\'s page 1, no "continued". So output page 1 doc_type: other.\n\nReturn format exactly: JSON with documents array containing object page_no:1, doc_type:"other".\n\nLet\'s do that.'}}}, {'text': '{\n    "documents": [\n        {\n            "page_no": 1,\n            "doc_type": "other"\n        }\n    ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2855, 'outputTokens': 325, 'totalTokens': 3180}, 'metrics': {'latencyMs': 1515}}
2025-09-24 19:00:53,513 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f129c4b7_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:53,529 - INFO - 

VY0AAD98JWYCZUAHKROB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 19:00:53,529 - INFO - 

✓ Saved result: output/run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 19:00:53,806 - INFO - Page 1: Extracted 1557 characters, 33 lines from 8274c635_ZAJMVPYOM22ETECQRW3L_92c8f86d_page_001.pdf
2025-09-24 19:00:53,806 - INFO - Successfully processed page 1
2025-09-24 19:00:53,806 - INFO - Combined 1 pages into final text
2025-09-24 19:00:53,806 - INFO - Text validation for 8274c635_ZAJMVPYOM22ETECQRW3L: 1574 characters, 1 pages
2025-09-24 19:00:53,807 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:53,807 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:53,812 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f129c4b7_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:00:54,634 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:00:54,634 - INFO - Splitting PDF into individual pages...
2025-09-24 19:00:54,635 - INFO - Splitting PDF 6ba263b5_WRP4157FNOQ52T6661B4 into 1 pages
2025-09-24 19:00:54,636 - INFO - Split PDF into 1 pages
2025-09-24 19:00:54,636 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:00:54,636 - INFO - Expected pages: [1]
2025-09-24 19:00:54,989 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ac46a359-b50f-4ab1-bb18-ac841b143d3f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:54 GMT', 'content-type': 'application/json', 'content-length': '3119', 'connection': 'keep-alive', 'x-amzn-requestid': 'ac46a359-b50f-4ab1-bb18-ac841b143d3f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify the document type. Let\'s analyze content. It\'s a PNCT (maybe "Production Notification Control Ticket" or something). Contains "PICKUP 566367", "LOAD OUT", "CONTAINER: FCIU9733694", "CARGO WT: 25792 LB", "SEALS: EU28454579". It looks like a gate-in/out or shipping manifest. But we have to check categories: ingate, outgate, bol, pod, etc.\n\nLook for keywords: "PICKUP" "LOAD OUT" "CONTAINER" "SIZE/TYPE" "CARGO WT" "SEALS" "RELEASE". This looks like an "ingate" or "outgate" document? There is an "INGATE" doc type: has keywords "Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In". Outgate: "Outgate, Departed, Gate Out, Time Out, Release, Exit Time". This page includes "RELEASE:" maybe indicates release. There\'s also "Welcome to PNCT. Please follow safety rules and regulations." PNCT might be "Port of New Caledonia Terminal"? Not sure.\n\nHowever "PICKUP" and "LOAD OUT" suggests a shipping or loading. But the presence of container number, cargo weight, seals indicates a customs/gate document. "RELEASE:" seems like release of container.\n\nGiven the options, the best fit is "ingate" or "outgate". But which? Let\'s examine: It says "RELEASE:" maybe indicates release from container for release. But ingate would be entry. Outgate would be exit. The presence of "PICKUP" might mean the load is being picked up. "LOAD OUT" might be load out. That might be a "loading" operation, maybe an outgate.\n\nAlso "PICKUP 566367" could refer to a truck pickup number. "LOAD OUT" might be the load out of container. However, we don\'t see "Time In" or "Entry Time". We don\'t see "Date" or "Time". There\'s no date. The page seems short.\n\nGiven the categories, I think "ingate" or "outgate" would be plausible. Which is more appropriate? The presence of "RELEASE" maybe indicates release of the container, i.e., outgate. But I\'d consider "ingate" because it\'s the gate in process.\n\nBut let\'s analyze: The page starts with "PNCT". Could be "Port of New Caledonia Terminal" - maybe "Port of New Caledonia Terminal" uses PNCT term for "Port Notification and Cargo Tracking". Not sure.\n\nThe best guess: This is a "ingate" or "outgate" document. The presence of "RELEASE:" suggests a release of container from the facility, which might happen at outgate. Ingate would be entry. The page says "Pick FCIU9733694 from GRID BGR (F1903A)". That might be a pick from a grid for loading. So it\'s a loading instruction. That might be part of a gate-out. But the presence of "RELEASE:" maybe the container is being released to the truck. So I think it\'s outgate.\n\nThus assign doc_type "outgate". Ensure we output the JSON with page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2777, 'outputTokens': 715, 'totalTokens': 3492}, 'metrics': {'latencyMs': 2931}}
2025-09-24 19:00:54,989 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a4b5cc5d_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:54,996 - INFO - 

YASUN3YXXTIFXRHDIKNF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:54,996 - INFO - 

✓ Saved result: output/run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 19:00:55,226 - INFO - Page 2: Extracted 457 characters, 4 lines from 839e891d_OO7629V25ZJ22FHL0PAA_c5f270ca_page_002.pdf
2025-09-24 19:00:55,227 - INFO - Successfully processed page 2
2025-09-24 19:00:55,294 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a4b5cc5d_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:00:55,406 - INFO - Page 1: Extracted 669 characters, 29 lines from 839e891d_OO7629V25ZJ22FHL0PAA_c5f270ca_page_001.pdf
2025-09-24 19:00:55,406 - INFO - Successfully processed page 1
2025-09-24 19:00:55,407 - INFO - Combined 2 pages into final text
2025-09-24 19:00:55,407 - INFO - Text validation for 839e891d_OO7629V25ZJ22FHL0PAA: 1162 characters, 2 pages
2025-09-24 19:00:55,408 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:00:55,408 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:00:56,099 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3539e1f7-9cf8-4640-8961-1361a51a43e5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:55 GMT', 'content-type': 'application/json', 'content-length': '1048', 'connection': 'keep-alive', 'x-amzn-requestid': '3539e1f7-9cf8-4640-8961-1361a51a43e5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. It seems like an ingate or outgate? Let\'s read: "Intermodal Gate Information - HAMU 249602 06/24/2025 11:47". Equipment ID: HAMU 249602, Terminal: NASHVILLE, Associated Equipment ID: TSXZ 474797. Date and Time. L/E Status: Load. Activity: Departure. So this is departure record. This is likely an ingate/outgate. Since Activity: Departure, that\'s more like outgate (vehicle exiting). But ingate would be arrival, entry. This says departure. So outgate. So page type: outgate. Confirm presence of "Outgate" keywords? Not explicitly, but Activity: Departure. It has "Equipment Information" and "Event Information". It\'s a gate record. So outgate. We need to produce JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2976, 'outputTokens': 201, 'totalTokens': 3177}, 'metrics': {'latencyMs': 1129}}
2025-09-24 19:00:56,099 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8274c635_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:56,129 - INFO - 

ZAJMVPYOM22ETECQRW3L.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:00:56,129 - INFO - 

✓ Saved result: output/run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 19:00:56,405 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8274c635_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:00:58,220 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b8eb07e2-1389-4758-9dbf-bb76d5fb1b07', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:30:58 GMT', 'content-type': 'application/json', 'content-length': '1595', 'connection': 'keep-alive', 'x-amzn-requestid': 'b8eb07e2-1389-4758-9dbf-bb76d5fb1b07'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 contains text about equipment, carrier, shipper, driver, load, seal, etc. It says "OUTGATE"? Actually says "DEPARTED" and "THIS TERMINAL IS EQUIPPED..." The content resembles an outgate receipt? Actually the header shows "Outlook" maybe email. The phrase "DEPARTED : 06/11/2025 13:20 ZS042 GLOBAL 4 IL" indicates a departure time. It has "CARRIER", "SHIPPER", "DRIVER", "LOAD", "SEAL", "CHECKED BY". This looks like an outgate or ingate? It says "DAMAGE IS NOT REPORTED ON INGATE OR OUTGATE RECEIPTS." So the document is an ingate/outgate receipt. It could be an outgate receipt. The type category for outgate is "outgate". The page 1 likely is an outgate document. Page 2 is just email disclaimer. Should we classify page 2 as continuation? The instruction: If a page is not from any categories, classify as other. But check if it is continuation of previous page. It doesn\'t start with "continued" etc. So page 2 is separate. It\'s an email disclaimer, not a logistics document. So classify as other. So output two entries.\n\nThus produce JSON with documents array, page_no 1 outgate, page_no 2 other.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "outgate"\n    },\n    {\n      "page_no": 2,\n      "doc_type": "other"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 349, 'totalTokens': 3277}, 'metrics': {'latencyMs': 1701}}
2025-09-24 19:00:58,220 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/839e891d_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:00:58,243 - INFO - 

OO7629V25ZJ22FHL0PAA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 19:00:58,243 - INFO - 

✓ Saved result: output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:00:58,523 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/839e891d_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:01:00,897 - INFO - Page 1: Extracted 1574 characters, 82 lines from 6ba263b5_WRP4157FNOQ52T6661B4_bad0dd15_page_001.pdf
2025-09-24 19:01:00,898 - INFO - Successfully processed page 1
2025-09-24 19:01:00,898 - INFO - Combined 1 pages into final text
2025-09-24 19:01:00,898 - INFO - Text validation for 6ba263b5_WRP4157FNOQ52T6661B4: 1591 characters, 1 pages
2025-09-24 19:01:00,898 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:01:00,898 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:01:02,930 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '27d328f7-a920-466f-aff6-6565ba420a54', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:31:02 GMT', 'content-type': 'application/json', 'content-length': '762', 'connection': 'keep-alive', 'x-amzn-requestid': '27d328f7-a920-466f-aff6-6565ba420a54'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We must classify each page; only one page. The content is an outgate/ingate? The page shows "GATE OUT" and "EIR Number", "Truck In", "Truck Out", container info, etc. It\'s a gate out record. The keywords for outgate: "Outgate", "Gate Out", "Time Out", "Release". This page clearly is an outgate document. So doc_type "outgate". Also includes "GATE OUT" heading. So page1 => outgate.\n\nReturn format JSON with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3156, 'outputTokens': 131, 'totalTokens': 3287}, 'metrics': {'latencyMs': 834}}
2025-09-24 19:01:02,931 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6ba263b5_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:01:02,961 - INFO - 

WRP4157FNOQ52T6661B4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:01:02,961 - INFO - 

✓ Saved result: output/run1_WRP4157FNOQ52T6661B4.json
2025-09-24 19:01:03,338 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ba263b5_WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:01:03,339 - INFO - 
📊 Processing Summary:
2025-09-24 19:01:03,339 - INFO -    Total files: 12
2025-09-24 19:01:03,339 - INFO -    Successful: 12
2025-09-24 19:01:03,340 - INFO -    Failed: 0
2025-09-24 19:01:03,340 - INFO -    Duration: 32.23 seconds
2025-09-24 19:01:03,340 - INFO -    Output directory: output
2025-09-24 19:01:03,340 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:01:03,340 - INFO -    📄 BJYFRYT0V8O045HGDXJ6.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,340 - INFO -    📄 E88AUSUP8065SEI9GD5Q.jpg: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 19:01:03,340 - INFO -    📄 EKMN29XJBZVFPLOYTKU7.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,340 - INFO -    📄 GHXKMFASS6CGN362R5BW.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,340 - INFO -    📄 HZNQ971O2V9MGG5MU1T3.jpeg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,340 - INFO -    📄 OO7629V25ZJ22FHL0PAA.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 19:01:03,340 - INFO -    📄 T5U48WX7H3FNSZPBDBLZ.png: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,340 - INFO -    📄 UGM225037492OGWQHUHR.png: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 19:01:03,341 - INFO -    📄 VY0AAD98JWYCZUAHKROB.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 19:01:03,341 - INFO -    📄 WRP4157FNOQ52T6661B4.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,341 - INFO -    📄 YASUN3YXXTIFXRHDIKNF.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,341 - INFO -    📄 ZAJMVPYOM22ETECQRW3L.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:01:03,341 - INFO - 
============================================================================================================================================
2025-09-24 19:01:03,341 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:01:03,342 - INFO - ============================================================================================================================================
2025-09-24 19:01:03,342 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:01:03,342 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:01:03,342 - INFO - BJYFRYT0V8O045HGDXJ6.pdf                           1      outgate                                  run1_BJYFRYT0V8O045HGDXJ6.json                                                  
2025-09-24 19:01:03,342 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 19:01:03,342 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 19:01:03,342 - INFO - 
2025-09-24 19:01:03,342 - INFO - E88AUSUP8065SEI9GD5Q.jpg                           1      customs_doc                              run1_E88AUSUP8065SEI9GD5Q.json                                                  
2025-09-24 19:01:03,342 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 19:01:03,342 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 19:01:03,342 - INFO - 
2025-09-24 19:01:03,342 - INFO - EKMN29XJBZVFPLOYTKU7.pdf                           1      outgate                                  run1_EKMN29XJBZVFPLOYTKU7.json                                                  
2025-09-24 19:01:03,342 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 19:01:03,342 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 19:01:03,342 - INFO - 
2025-09-24 19:01:03,342 - INFO - GHXKMFASS6CGN362R5BW.pdf                           1      outgate                                  run1_GHXKMFASS6CGN362R5BW.json                                                  
2025-09-24 19:01:03,342 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf
2025-09-24 19:01:03,342 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 19:01:03,342 - INFO - 
2025-09-24 19:01:03,342 - INFO - HZNQ971O2V9MGG5MU1T3.jpeg                          1      outgate                                  run1_HZNQ971O2V9MGG5MU1T3.json                                                  
2025-09-24 19:01:03,342 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 19:01:03,342 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 19:01:03,343 - INFO - 
2025-09-24 19:01:03,343 - INFO - OO7629V25ZJ22FHL0PAA.pdf                           1      outgate                                  run1_OO7629V25ZJ22FHL0PAA.json                                                  
2025-09-24 19:01:03,343 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:01:03,343 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:01:03,343 - INFO - 
2025-09-24 19:01:03,343 - INFO - OO7629V25ZJ22FHL0PAA.pdf                           2      other                                    run1_OO7629V25ZJ22FHL0PAA.json                                                  
2025-09-24 19:01:03,343 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 19:01:03,343 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:01:03,343 - INFO - 
2025-09-24 19:01:03,343 - INFO - T5U48WX7H3FNSZPBDBLZ.png                           1      outgate                                  run1_T5U48WX7H3FNSZPBDBLZ.json                                                  
2025-09-24 19:01:03,343 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 19:01:03,343 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 19:01:03,343 - INFO - 
2025-09-24 19:01:03,343 - INFO - UGM225037492OGWQHUHR.png                           1      ingate                                   run1_UGM225037492OGWQHUHR.json                                                  
2025-09-24 19:01:03,343 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png
2025-09-24 19:01:03,343 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UGM225037492OGWQHUHR.json
2025-09-24 19:01:03,343 - INFO - 
2025-09-24 19:01:03,343 - INFO - VY0AAD98JWYCZUAHKROB.pdf                           1      other                                    run1_VY0AAD98JWYCZUAHKROB.json                                                  
2025-09-24 19:01:03,343 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 19:01:03,343 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 19:01:03,343 - INFO - 
2025-09-24 19:01:03,343 - INFO - WRP4157FNOQ52T6661B4.pdf                           1      outgate                                  run1_WRP4157FNOQ52T6661B4.json                                                  
2025-09-24 19:01:03,343 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf
2025-09-24 19:01:03,343 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WRP4157FNOQ52T6661B4.json
2025-09-24 19:01:03,344 - INFO - 
2025-09-24 19:01:03,344 - INFO - YASUN3YXXTIFXRHDIKNF.pdf                           1      outgate                                  run1_YASUN3YXXTIFXRHDIKNF.json                                                  
2025-09-24 19:01:03,344 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 19:01:03,344 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 19:01:03,344 - INFO - 
2025-09-24 19:01:03,344 - INFO - ZAJMVPYOM22ETECQRW3L.pdf                           1      outgate                                  run1_ZAJMVPYOM22ETECQRW3L.json                                                  
2025-09-24 19:01:03,344 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 19:01:03,344 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 19:01:03,344 - INFO - 
2025-09-24 19:01:03,344 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:01:03,344 - INFO - Total entries: 13
2025-09-24 19:01:03,344 - INFO - ============================================================================================================================================
2025-09-24 19:01:03,344 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:01:03,344 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:01:03,344 - INFO -   1. BJYFRYT0V8O045HGDXJ6.pdf            Page 1   → outgate         | run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 19:01:03,344 - INFO -   2. E88AUSUP8065SEI9GD5Q.jpg            Page 1   → customs_doc     | run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 19:01:03,344 - INFO -   3. EKMN29XJBZVFPLOYTKU7.pdf            Page 1   → outgate         | run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 19:01:03,344 - INFO -   4. GHXKMFASS6CGN362R5BW.pdf            Page 1   → outgate         | run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 19:01:03,344 - INFO -   5. HZNQ971O2V9MGG5MU1T3.jpeg           Page 1   → outgate         | run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 19:01:03,344 - INFO -   6. OO7629V25ZJ22FHL0PAA.pdf            Page 1   → outgate         | run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:01:03,344 - INFO -   7. OO7629V25ZJ22FHL0PAA.pdf            Page 2   → other           | run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 19:01:03,344 - INFO -   8. T5U48WX7H3FNSZPBDBLZ.png            Page 1   → outgate         | run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 19:01:03,345 - INFO -   9. UGM225037492OGWQHUHR.png            Page 1   → ingate          | run1_UGM225037492OGWQHUHR.json
2025-09-24 19:01:03,345 - INFO -  10. VY0AAD98JWYCZUAHKROB.pdf            Page 1   → other           | run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 19:01:03,345 - INFO -  11. WRP4157FNOQ52T6661B4.pdf            Page 1   → outgate         | run1_WRP4157FNOQ52T6661B4.json
2025-09-24 19:01:03,345 - INFO -  12. YASUN3YXXTIFXRHDIKNF.pdf            Page 1   → outgate         | run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 19:01:03,345 - INFO -  13. ZAJMVPYOM22ETECQRW3L.pdf            Page 1   → outgate         | run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 19:01:03,345 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:01:03,345 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 32.234012, 'processed_files': [{'filename': 'BJYFRYT0V8O045HGDXJ6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf'}, {'filename': 'E88AUSUP8065SEI9GD5Q.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg'}, {'filename': 'EKMN29XJBZVFPLOYTKU7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf'}, {'filename': 'GHXKMFASS6CGN362R5BW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf'}, {'filename': 'HZNQ971O2V9MGG5MU1T3.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg'}, {'filename': 'OO7629V25ZJ22FHL0PAA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf'}, {'filename': 'T5U48WX7H3FNSZPBDBLZ.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png'}, {'filename': 'UGM225037492OGWQHUHR.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png'}, {'filename': 'VY0AAD98JWYCZUAHKROB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf'}, {'filename': 'WRP4157FNOQ52T6661B4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf'}, {'filename': 'YASUN3YXXTIFXRHDIKNF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf'}, {'filename': 'ZAJMVPYOM22ETECQRW3L.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf'}]}
