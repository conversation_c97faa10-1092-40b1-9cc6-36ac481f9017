2025-09-24 18:57:50,038 - INFO - Logging initialized. Log file: logs/test_classification_20250924_185750.log
2025-09-24 18:57:50,038 - INFO - 📁 Found 9 files to process
2025-09-24 18:57:50,038 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:57:50,038 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 18:57:50,038 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 18:57:50,038 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 18:57:50,038 - INFO - ⬆️ [18:57:50] Uploading: AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:57:52,153 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AJ17T27ABDSGCYL1YI7Q.jpg -> s3://document-extraction-logistically/temp/57f7b44d_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:57:52,154 - INFO - 🔍 [18:57:52] Starting classification: AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:57:52,154 - INFO - ⬆️ [18:57:52] Uploading: AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:57:52,155 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:52,166 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:52,171 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/57f7b44d_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:57:52,172 - INFO - Processing image from S3...
2025-09-24 18:57:53,051 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AYZVGH1N2J32RSF72XVM.jpg -> s3://document-extraction-logistically/temp/88627afb_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:57:53,051 - INFO - 🔍 [18:57:53] Starting classification: AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:57:53,052 - INFO - ⬆️ [18:57:53] Uploading: HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:57:53,054 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:53,078 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:53,083 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/88627afb_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:57:53,083 - INFO - Processing image from S3...
2025-09-24 18:57:54,424 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/HGPDC5MO1QA1H930SDUI.jpeg -> s3://document-extraction-logistically/temp/707bbe5b_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:57:54,424 - INFO - 🔍 [18:57:54] Starting classification: HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:57:54,426 - INFO - ⬆️ [18:57:54] Uploading: IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:57:54,427 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:54,444 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:54,446 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/707bbe5b_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:57:54,446 - INFO - Processing image from S3...
2025-09-24 18:57:55,019 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf -> s3://document-extraction-logistically/temp/c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:57:55,020 - INFO - 🔍 [18:57:55] Starting classification: IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:57:55,021 - INFO - ⬆️ [18:57:55] Uploading: N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:57:55,022 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:55,042 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:55,045 - INFO - S3 Image temp/57f7b44d_AJ17T27ABDSGCYL1YI7Q.jpg: Extracted 862 characters, 21 lines
2025-09-24 18:57:55,047 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:57:55,047 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:57:55,050 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:57:55,053 - INFO - Processing PDF from S3...
2025-09-24 18:57:55,054 - INFO - Downloading PDF from S3 to /tmp/tmpemyrrpv8/c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:57:55,674 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/N3E7OS7WU4GL5CNGZVGC.pdf -> s3://document-extraction-logistically/temp/fca258b7_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:57:55,675 - INFO - 🔍 [18:57:55] Starting classification: N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:57:55,676 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:55,676 - INFO - ⬆️ [18:57:55] Uploading: NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:57:55,693 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:55,696 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fca258b7_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:57:55,696 - INFO - Processing PDF from S3...
2025-09-24 18:57:55,696 - INFO - Downloading PDF from S3 to /tmp/tmpvolghm3d/fca258b7_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:57:56,318 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/NZ0W72MP1PN7WP5Z6E33.pdf -> s3://document-extraction-logistically/temp/37b860af_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:57:56,319 - INFO - 🔍 [18:57:56] Starting classification: NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:57:56,319 - INFO - ⬆️ [18:57:56] Uploading: OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:57:56,322 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:56,335 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:56,343 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/37b860af_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:57:56,344 - INFO - Processing PDF from S3...
2025-09-24 18:57:56,344 - INFO - Downloading PDF from S3 to /tmp/tmptb_8kp2_/37b860af_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:57:56,721 - INFO - S3 Image temp/88627afb_AYZVGH1N2J32RSF72XVM.jpg: Extracted 790 characters, 18 lines
2025-09-24 18:57:56,722 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:57:56,722 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:57:56,873 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:57:56,874 - INFO - Splitting PDF into individual pages...
2025-09-24 18:57:56,876 - INFO - Splitting PDF c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR into 2 pages
2025-09-24 18:57:56,921 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OQ5GJFQKACWXOAGHOFVQ.pdf -> s3://document-extraction-logistically/temp/6c93fe2d_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:57:56,921 - INFO - 🔍 [18:57:56] Starting classification: OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:57:56,922 - INFO - Split PDF into 2 pages
2025-09-24 18:57:56,922 - INFO - ⬆️ [18:57:56] Uploading: OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:57:56,922 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:57:56,922 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:56,923 - INFO - Expected pages: [1, 2]
2025-09-24 18:57:56,938 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:56,943 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4e6ef928-4a4f-4b67-a526-e105e07f77b0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:27:56 GMT', 'content-type': 'application/json', 'content-length': '601', 'connection': 'keep-alive', 'x-amzn-requestid': '4e6ef928-4a4f-4b67-a526-e105e07f77b0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to decide doc_type. The content includes "TSL Ingate: OMA" and "ActivityDate" and "Inspector" and "Carrier". This looks like an Ingate Document. Keywords: "Ingate". So doc_type = ingate. Return JSON with page 1 ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2820, 'outputTokens': 92, 'totalTokens': 2912}, 'metrics': {'latencyMs': 652}}
2025-09-24 18:57:56,944 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6c93fe2d_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:57:56,945 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/57f7b44d_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:57:56,945 - INFO - Processing PDF from S3...
2025-09-24 18:57:56,946 - INFO - Downloading PDF from S3 to /tmp/tmpfzd72dlp/6c93fe2d_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:57:57,565 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OSWHB56ZEDCANKML3UKZ.pdf -> s3://document-extraction-logistically/temp/1b622ad4_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:57:57,567 - INFO - 🔍 [18:57:57] Starting classification: OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:57:57,568 - INFO - ⬆️ [18:57:57] Uploading: WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:57:57,569 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:57,570 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:57:57,579 - INFO - Splitting PDF into individual pages...
2025-09-24 18:57:57,587 - INFO - Splitting PDF fca258b7_N3E7OS7WU4GL5CNGZVGC into 1 pages
2025-09-24 18:57:57,590 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:57,595 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1b622ad4_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:57:57,596 - INFO - Processing PDF from S3...
2025-09-24 18:57:57,596 - INFO - Downloading PDF from S3 to /tmp/tmpznypvze8/1b622ad4_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:57:57,600 - INFO - Split PDF into 1 pages
2025-09-24 18:57:57,600 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:57:57,600 - INFO - Expected pages: [1]
2025-09-24 18:57:57,647 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:57:57,647 - INFO - Splitting PDF into individual pages...
2025-09-24 18:57:57,648 - INFO - Splitting PDF 37b860af_NZ0W72MP1PN7WP5Z6E33 into 1 pages
2025-09-24 18:57:57,656 - INFO - Split PDF into 1 pages
2025-09-24 18:57:57,656 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:57:57,656 - INFO - Expected pages: [1]
2025-09-24 18:57:57,834 - INFO - S3 Image temp/707bbe5b_HGPDC5MO1QA1H930SDUI.jpeg: Extracted 560 characters, 28 lines
2025-09-24 18:57:57,834 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:57:57,834 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:57:58,184 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/WQ9YLCCRP1EJOP7S8615.png -> s3://document-extraction-logistically/temp/ea4850ea_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:57:58,184 - INFO - 🔍 [18:57:58] Starting classification: WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:57:58,185 - INFO - Initializing TextractProcessor...
2025-09-24 18:57:58,191 - INFO - Initializing BedrockProcessor...
2025-09-24 18:57:58,193 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ea4850ea_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:57:58,194 - INFO - Processing image from S3...
2025-09-24 18:57:58,204 - INFO - 

AJ17T27ABDSGCYL1YI7Q.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:57:58,205 - INFO - 

✓ Saved result: output/run1_AJ17T27ABDSGCYL1YI7Q.json
2025-09-24 18:57:58,488 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/57f7b44d_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:57:58,507 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bd0e4989-dbe0-45d2-a8c9-9bb7fc32a17f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:27:58 GMT', 'content-type': 'application/json', 'content-length': '709', 'connection': 'keep-alive', 'x-amzn-requestid': 'bd0e4989-dbe0-45d2-a8c9-9bb7fc32a17f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. The content is an Ingate document. Keywords: "TSL Ingate", "ActivityDate", "Carrier Inspector", "I hereby certify that ... inspection ...". It looks like an ingate document. So doc_type: ingate. No continuation. Output accordingly. Use tool call.\n\n'}}}, {'toolUse': {'toolUseId': 'tooluse_m5cGvw9rTQaOlAnmQUTeLg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2790, 'outputTokens': 88, 'totalTokens': 2878}, 'metrics': {'latencyMs': 593}}
2025-09-24 18:57:58,507 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/88627afb_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:57:58,530 - INFO - 

AYZVGH1N2J32RSF72XVM.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:57:58,531 - INFO - 

✓ Saved result: output/run1_AYZVGH1N2J32RSF72XVM.json
2025-09-24 18:57:58,778 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:57:58,778 - INFO - Splitting PDF into individual pages...
2025-09-24 18:57:58,779 - INFO - Splitting PDF 6c93fe2d_OQ5GJFQKACWXOAGHOFVQ into 1 pages
2025-09-24 18:57:58,790 - INFO - Split PDF into 1 pages
2025-09-24 18:57:58,790 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:57:58,790 - INFO - Expected pages: [1]
2025-09-24 18:57:58,823 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/88627afb_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:57:59,426 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:57:59,426 - INFO - Splitting PDF into individual pages...
2025-09-24 18:57:59,428 - INFO - Splitting PDF 1b622ad4_OSWHB56ZEDCANKML3UKZ into 1 pages
2025-09-24 18:57:59,431 - INFO - Split PDF into 1 pages
2025-09-24 18:57:59,431 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:57:59,431 - INFO - Expected pages: [1]
2025-09-24 18:57:59,666 - INFO - Page 2: Extracted 116 characters, 5 lines from c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR_0ca65408_page_002.pdf
2025-09-24 18:57:59,666 - INFO - Successfully processed page 2
2025-09-24 18:57:59,690 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '806125a5-7278-42a7-9a1f-3f538c7d6b9d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:27:59 GMT', 'content-type': 'application/json', 'content-length': '633', 'connection': 'keep-alive', 'x-amzn-requestid': '806125a5-7278-42a7-9a1f-3f538c7d6b9d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page. Identify document type. The page appears to be an Ingate document: "IN-GATE" heading, "Equipment Inspection Record". It says "Ingate: LOGPARCHI IL". So it\'s an ingate record. So doc_type: ingate. Output JSON with documents list.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "ingate"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2789, 'outputTokens': 100, 'totalTokens': 2889}, 'metrics': {'latencyMs': 631}}
2025-09-24 18:57:59,691 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/707bbe5b_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:57:59,710 - INFO - 

HGPDC5MO1QA1H930SDUI.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:57:59,710 - INFO - 

✓ Saved result: output/run1_HGPDC5MO1QA1H930SDUI.json
2025-09-24 18:58:00,008 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/707bbe5b_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:01,005 - INFO - Page 1: Extracted 375 characters, 27 lines from 37b860af_NZ0W72MP1PN7WP5Z6E33_0fe01420_page_001.pdf
2025-09-24 18:58:01,006 - INFO - Successfully processed page 1
2025-09-24 18:58:01,006 - INFO - Combined 1 pages into final text
2025-09-24 18:58:01,007 - INFO - Text validation for 37b860af_NZ0W72MP1PN7WP5Z6E33: 392 characters, 1 pages
2025-09-24 18:58:01,007 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:01,008 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:01,233 - INFO - S3 Image temp/ea4850ea_WQ9YLCCRP1EJOP7S8615.png: Extracted 410 characters, 24 lines
2025-09-24 18:58:01,233 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:01,233 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:01,907 - INFO - Page 1: Extracted 412 characters, 29 lines from c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR_0ca65408_page_001.pdf
2025-09-24 18:58:01,907 - INFO - Successfully processed page 1
2025-09-24 18:58:01,907 - INFO - Combined 2 pages into final text
2025-09-24 18:58:01,907 - INFO - Text validation for c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR: 564 characters, 2 pages
2025-09-24 18:58:01,908 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:01,908 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:02,449 - INFO - Page 1: Extracted 1573 characters, 33 lines from fca258b7_N3E7OS7WU4GL5CNGZVGC_7a66b90f_page_001.pdf
2025-09-24 18:58:02,449 - INFO - Successfully processed page 1
2025-09-24 18:58:02,450 - INFO - Combined 1 pages into final text
2025-09-24 18:58:02,450 - INFO - Text validation for fca258b7_N3E7OS7WU4GL5CNGZVGC: 1590 characters, 1 pages
2025-09-24 18:58:02,450 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:02,450 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:02,960 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5a261d9d-39b3-4e34-aada-aae6ab7f53dd', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:02 GMT', 'content-type': 'application/json', 'content-length': '640', 'connection': 'keep-alive', 'x-amzn-requestid': '5a261d9d-39b3-4e34-aada-aae6ab7f53dd'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content: "J1 GATE RECEIPT" header. Keywords: "In-Gate". This looks like an ingate document. Ingate page likely has headings like "J1 GATE RECEIPT" and info about equipment, empty & driver, in-gate. So likely doc_type "ingate". There is no other doc types. So output JSON with page 1 ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2722, 'outputTokens': 107, 'totalTokens': 2829}, 'metrics': {'latencyMs': 697}}
2025-09-24 18:58:02,961 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/37b860af_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:02,976 - INFO - 

NZ0W72MP1PN7WP5Z6E33.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:02,976 - INFO - 

✓ Saved result: output/run1_NZ0W72MP1PN7WP5Z6E33.json
2025-09-24 18:58:03,105 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '097f4d16-6e6c-46b8-bd9e-0f2c3804c2c5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:02 GMT', 'content-type': 'application/json', 'content-length': '632', 'connection': 'keep-alive', 'x-amzn-requestid': '097f4d16-6e6c-46b8-bd9e-0f2c3804c2c5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1 provided. The content looks like an Ingate document. It lists "In Date", "In Time", "In Condition", "In Empty", etc. This is typical of ingate. So doc_type = ingate.\n\nWe need to output JSON with documents array. Should include page_no 1 and doc_type ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2751, 'outputTokens': 96, 'totalTokens': 2847}, 'metrics': {'latencyMs': 654}}
2025-09-24 18:58:03,106 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ea4850ea_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:03,279 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/37b860af_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:03,291 - INFO - 

WQ9YLCCRP1EJOP7S8615.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:03,291 - INFO - 

✓ Saved result: output/run1_WQ9YLCCRP1EJOP7S8615.json
2025-09-24 18:58:03,580 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ea4850ea_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:03,714 - INFO - Page 1: Extracted 759 characters, 65 lines from 6c93fe2d_OQ5GJFQKACWXOAGHOFVQ_984fae4f_page_001.pdf
2025-09-24 18:58:03,714 - INFO - Successfully processed page 1
2025-09-24 18:58:03,715 - INFO - Combined 1 pages into final text
2025-09-24 18:58:03,715 - INFO - Text validation for 6c93fe2d_OQ5GJFQKACWXOAGHOFVQ: 776 characters, 1 pages
2025-09-24 18:58:03,716 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:03,716 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:04,079 - INFO - Page 1: Extracted 763 characters, 65 lines from 1b622ad4_OSWHB56ZEDCANKML3UKZ_b17cc032_page_001.pdf
2025-09-24 18:58:04,079 - INFO - Successfully processed page 1
2025-09-24 18:58:04,079 - INFO - Combined 1 pages into final text
2025-09-24 18:58:04,080 - INFO - Text validation for 1b622ad4_OSWHB56ZEDCANKML3UKZ: 780 characters, 1 pages
2025-09-24 18:58:04,080 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:04,080 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:04,670 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '84ce8268-f914-4e31-982b-53f8877b61be', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:04 GMT', 'content-type': 'application/json', 'content-length': '1328', 'connection': 'keep-alive', 'x-amzn-requestid': '84ce8268-f914-4e31-982b-53f8877b61be'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF content: page1 shows a website screenshot of ShipCSX "Shipment Lookup Results". This seems like an online portal page. Not a logistics document? Could be an ingate or outgate? Actually it\'s a portal showing status: IN-GATE (1). This might be an "ingate" document? Since it shows equipment ingated. But it\'s a portal snapshot. The document type for ingate: "Equipment In-Gated" etc. The page shows "In-Gate Readiness" and "Equipment In-Gated". So likely ingate. But maybe it\'s a portal dashboard; but the classification likely ingate. There\'s no other hint. It\'s a page with "1/2" and "2/2" pagination at bottom. So each page is part of a multi-page document. The content shows a search results page. It\'s not a standard ingate form? But the keywords: "IN-GATE", "Equipment In-Gated" are present. So classify as ingate. Page 1: ingate. Page 2: same content, just next page, so same type ingate.\n\nThus output documents array with 2 entries.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2804, 'outputTokens': 267, 'totalTokens': 3071}, 'metrics': {'latencyMs': 1412}}
2025-09-24 18:58:04,671 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:04,692 - INFO - 

IOL4A3Q1RJX8A4MOQXBR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        },
        {
            "page_no": 2,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:04,692 - INFO - 

✓ Saved result: output/run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:04,780 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '55aa6f56-a294-4f39-bc8c-7ca791aa7020', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:04 GMT', 'content-type': 'application/json', 'content-length': '1088', 'connection': 'keep-alive', 'x-amzn-requestid': '55aa6f56-a294-4f39-bc8c-7ca791aa7020'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have only page1 content. This looks like an email with gate information. Keywords: "Intermodal Gate Information", "Equipment Information", etc. This is likely an ingate or outgate document? It says "Intermodal Gate Information" and "Equipment ID" and "Arrival" activity. There\'s "Arrival". Likely this is an ingate document: record of vehicle/container entering a facility. Keywords: "Ingate", "Arrival", "Equipment ID", "Terminal". So doc_type should be ingate.\n\nCheck if any other type matches: "ingate" is defined: Record of vehicle/container entering a facility (gate-in). Yes.\n\nThus we output:\n\n{\n "documents": [\n   {\n     "page_no": 1,\n     "doc_type": "ingate"\n   }\n ]\n}\n\nEnsure format.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2968, 'outputTokens': 192, 'totalTokens': 3160}, 'metrics': {'latencyMs': 1071}}
2025-09-24 18:58:04,781 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fca258b7_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:05,013 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c6f2d8e7_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:05,037 - INFO - 

N3E7OS7WU4GL5CNGZVGC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:05,037 - INFO - 

✓ Saved result: output/run1_N3E7OS7WU4GL5CNGZVGC.json
2025-09-24 18:58:05,363 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fca258b7_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:05,875 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '30cef2fb-7df9-4244-916a-396ee1c7c4c5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:05 GMT', 'content-type': 'application/json', 'content-length': '873', 'connection': 'keep-alive', 'x-amzn-requestid': '30cef2fb-7df9-4244-916a-396ee1c7c4c5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to decide doc_type. The page contains many details: "Gate In Interchange", "Ingate Document", "Truck# 2730", "Inspected By: JESUS", "Carrier: DESERT EAGLE EXPRESS INC", date, etc. This looks like an ingate document. So doc_type: ingate.\n\nCheck keywords: "Ingate", "Gate In", "Truck", "Inspected", "Carrier". Yes matches ingate. So output JSON with documents array containing one object: page_no: 1, doc_type: "ingate".\n\nReturn via tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2879, 'outputTokens': 158, 'totalTokens': 3037}, 'metrics': {'latencyMs': 853}}
2025-09-24 18:58:05,875 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6c93fe2d_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:05,890 - INFO - 

OQ5GJFQKACWXOAGHOFVQ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:05,890 - INFO - 

✓ Saved result: output/run1_OQ5GJFQKACWXOAGHOFVQ.json
2025-09-24 18:58:06,079 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fc5195ed-1dea-4ffe-8041-77466b3980e5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:05 GMT', 'content-type': 'application/json', 'content-length': '703', 'connection': 'keep-alive', 'x-amzn-requestid': 'fc5195ed-1dea-4ffe-8041-77466b3980e5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content looks like an ingate document: "Gate In Interchange" and "Ingate Document" keywords. We need to classify as ingate. Check for keywords: "Gate In", "Ingate Document", "Equipment In-Gated", "Arrival Activity". Yes. So doc_type "ingate". Output must be JSON with documents array containing one object with page_no: 1 and doc_type: "ingate".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2881, 'outputTokens': 111, 'totalTokens': 2992}, 'metrics': {'latencyMs': 750}}
2025-09-24 18:58:06,079 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1b622ad4_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:06,182 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6c93fe2d_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:06,194 - INFO - 

OSWHB56ZEDCANKML3UKZ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:06,195 - INFO - 

✓ Saved result: output/run1_OSWHB56ZEDCANKML3UKZ.json
2025-09-24 18:58:06,490 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1b622ad4_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:06,491 - INFO - 
📊 Processing Summary:
2025-09-24 18:58:06,491 - INFO -    Total files: 9
2025-09-24 18:58:06,492 - INFO -    Successful: 9
2025-09-24 18:58:06,492 - INFO -    Failed: 0
2025-09-24 18:58:06,492 - INFO -    Duration: 16.45 seconds
2025-09-24 18:58:06,492 - INFO -    Output directory: output
2025-09-24 18:58:06,492 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:58:06,492 - INFO -    📄 AJ17T27ABDSGCYL1YI7Q.jpg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,492 - INFO -    📄 AYZVGH1N2J32RSF72XVM.jpg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,493 - INFO -    📄 HGPDC5MO1QA1H930SDUI.jpeg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,493 - INFO -    📄 IOL4A3Q1RJX8A4MOQXBR.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"ingate"}]}
2025-09-24 18:58:06,493 - INFO -    📄 N3E7OS7WU4GL5CNGZVGC.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,493 - INFO -    📄 NZ0W72MP1PN7WP5Z6E33.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,493 - INFO -    📄 OQ5GJFQKACWXOAGHOFVQ.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,493 - INFO -    📄 OSWHB56ZEDCANKML3UKZ.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,494 - INFO -    📄 WQ9YLCCRP1EJOP7S8615.png: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:06,494 - INFO - 
============================================================================================================================================
2025-09-24 18:58:06,495 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:58:06,495 - INFO - ============================================================================================================================================
2025-09-24 18:58:06,495 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:58:06,495 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:58:06,495 - INFO - AJ17T27ABDSGCYL1YI7Q.jpg                           1      ingate                                   run1_AJ17T27ABDSGCYL1YI7Q.json                                                  
2025-09-24 18:58:06,495 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:06,495 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AJ17T27ABDSGCYL1YI7Q.json
2025-09-24 18:58:06,495 - INFO - 
2025-09-24 18:58:06,495 - INFO - AYZVGH1N2J32RSF72XVM.jpg                           1      ingate                                   run1_AYZVGH1N2J32RSF72XVM.json                                                  
2025-09-24 18:58:06,496 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:06,496 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYZVGH1N2J32RSF72XVM.json
2025-09-24 18:58:06,496 - INFO - 
2025-09-24 18:58:06,496 - INFO - HGPDC5MO1QA1H930SDUI.jpeg                          1      ingate                                   run1_HGPDC5MO1QA1H930SDUI.json                                                  
2025-09-24 18:58:06,496 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:06,496 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HGPDC5MO1QA1H930SDUI.json
2025-09-24 18:58:06,496 - INFO - 
2025-09-24 18:58:06,496 - INFO - IOL4A3Q1RJX8A4MOQXBR.pdf                           1      ingate                                   run1_IOL4A3Q1RJX8A4MOQXBR.json                                                  
2025-09-24 18:58:06,496 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:06,496 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:06,496 - INFO - 
2025-09-24 18:58:06,496 - INFO - IOL4A3Q1RJX8A4MOQXBR.pdf                           2      ingate                                   run1_IOL4A3Q1RJX8A4MOQXBR.json                                                  
2025-09-24 18:58:06,497 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:06,497 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:06,497 - INFO - 
2025-09-24 18:58:06,497 - INFO - N3E7OS7WU4GL5CNGZVGC.pdf                           1      ingate                                   run1_N3E7OS7WU4GL5CNGZVGC.json                                                  
2025-09-24 18:58:06,497 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:06,497 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N3E7OS7WU4GL5CNGZVGC.json
2025-09-24 18:58:06,497 - INFO - 
2025-09-24 18:58:06,497 - INFO - NZ0W72MP1PN7WP5Z6E33.pdf                           1      ingate                                   run1_NZ0W72MP1PN7WP5Z6E33.json                                                  
2025-09-24 18:58:06,497 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:06,497 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NZ0W72MP1PN7WP5Z6E33.json
2025-09-24 18:58:06,497 - INFO - 
2025-09-24 18:58:06,497 - INFO - OQ5GJFQKACWXOAGHOFVQ.pdf                           1      ingate                                   run1_OQ5GJFQKACWXOAGHOFVQ.json                                                  
2025-09-24 18:58:06,497 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:06,497 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OQ5GJFQKACWXOAGHOFVQ.json
2025-09-24 18:58:06,498 - INFO - 
2025-09-24 18:58:06,498 - INFO - OSWHB56ZEDCANKML3UKZ.pdf                           1      ingate                                   run1_OSWHB56ZEDCANKML3UKZ.json                                                  
2025-09-24 18:58:06,498 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:06,498 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OSWHB56ZEDCANKML3UKZ.json
2025-09-24 18:58:06,499 - INFO - 
2025-09-24 18:58:06,499 - INFO - WQ9YLCCRP1EJOP7S8615.png                           1      ingate                                   run1_WQ9YLCCRP1EJOP7S8615.json                                                  
2025-09-24 18:58:06,499 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:06,499 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WQ9YLCCRP1EJOP7S8615.json
2025-09-24 18:58:06,499 - INFO - 
2025-09-24 18:58:06,499 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:58:06,499 - INFO - Total entries: 10
2025-09-24 18:58:06,500 - INFO - ============================================================================================================================================
2025-09-24 18:58:06,500 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:58:06,500 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:58:06,500 - INFO -   1. AJ17T27ABDSGCYL1YI7Q.jpg            Page 1   → ingate          | run1_AJ17T27ABDSGCYL1YI7Q.json
2025-09-24 18:58:06,500 - INFO -   2. AYZVGH1N2J32RSF72XVM.jpg            Page 1   → ingate          | run1_AYZVGH1N2J32RSF72XVM.json
2025-09-24 18:58:06,501 - INFO -   3. HGPDC5MO1QA1H930SDUI.jpeg           Page 1   → ingate          | run1_HGPDC5MO1QA1H930SDUI.json
2025-09-24 18:58:06,501 - INFO -   4. IOL4A3Q1RJX8A4MOQXBR.pdf            Page 1   → ingate          | run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:06,501 - INFO -   5. IOL4A3Q1RJX8A4MOQXBR.pdf            Page 2   → ingate          | run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:06,501 - INFO -   6. N3E7OS7WU4GL5CNGZVGC.pdf            Page 1   → ingate          | run1_N3E7OS7WU4GL5CNGZVGC.json
2025-09-24 18:58:06,501 - INFO -   7. NZ0W72MP1PN7WP5Z6E33.pdf            Page 1   → ingate          | run1_NZ0W72MP1PN7WP5Z6E33.json
2025-09-24 18:58:06,502 - INFO -   8. OQ5GJFQKACWXOAGHOFVQ.pdf            Page 1   → ingate          | run1_OQ5GJFQKACWXOAGHOFVQ.json
2025-09-24 18:58:06,502 - INFO -   9. OSWHB56ZEDCANKML3UKZ.pdf            Page 1   → ingate          | run1_OSWHB56ZEDCANKML3UKZ.json
2025-09-24 18:58:06,502 - INFO -  10. WQ9YLCCRP1EJOP7S8615.png            Page 1   → ingate          | run1_WQ9YLCCRP1EJOP7S8615.json
2025-09-24 18:58:06,502 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:58:06,503 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 16.452877, 'processed_files': [{'filename': 'AJ17T27ABDSGCYL1YI7Q.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AJ17T27ABDSGCYL1YI7Q.jpg'}, {'filename': 'AYZVGH1N2J32RSF72XVM.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AYZVGH1N2J32RSF72XVM.jpg'}, {'filename': 'HGPDC5MO1QA1H930SDUI.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/HGPDC5MO1QA1H930SDUI.jpeg'}, {'filename': 'IOL4A3Q1RJX8A4MOQXBR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}, {'page_no': 2, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf'}, {'filename': 'N3E7OS7WU4GL5CNGZVGC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/N3E7OS7WU4GL5CNGZVGC.pdf'}, {'filename': 'NZ0W72MP1PN7WP5Z6E33.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/NZ0W72MP1PN7WP5Z6E33.pdf'}, {'filename': 'OQ5GJFQKACWXOAGHOFVQ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OQ5GJFQKACWXOAGHOFVQ.pdf'}, {'filename': 'OSWHB56ZEDCANKML3UKZ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OSWHB56ZEDCANKML3UKZ.pdf'}, {'filename': 'WQ9YLCCRP1EJOP7S8615.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/WQ9YLCCRP1EJOP7S8615.png'}]}
