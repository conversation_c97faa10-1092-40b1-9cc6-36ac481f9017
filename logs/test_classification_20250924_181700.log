2025-09-24 18:17:00,829 - INFO - Logging initialized. Log file: logs/test_classification_20250924_181700.log
2025-09-24 18:17:00,829 - INFO - 📁 Found 10 files to process
2025-09-24 18:17:00,829 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:17:00,829 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 18:17:00,829 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 18:17:00,829 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 18:17:00,829 - INFO - ⬆️ [18:17:00] Uploading: BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:04,877 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/BBWPIXK6JXYXQJO2PEFR.jpg -> s3://document-extraction-logistically/temp/52567568_BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:04,878 - INFO - 🔍 [18:17:04] Starting classification: BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:04,879 - INFO - ⬆️ [18:17:04] Uploading: C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:04,881 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:04,898 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:04,904 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/52567568_BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:04,904 - INFO - Processing image from S3...
2025-09-24 18:17:06,662 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/C8WLD4UA74U7PBVTPBW4.jpg -> s3://document-extraction-logistically/temp/4a8079b9_C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:06,662 - INFO - 🔍 [18:17:06] Starting classification: C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:06,663 - INFO - ⬆️ [18:17:06] Uploading: DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:06,665 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:06,680 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:06,684 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4a8079b9_C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:06,685 - INFO - Processing image from S3...
2025-09-24 18:17:07,582 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/DDRWUNS7W350XYVX3TFS.jpg -> s3://document-extraction-logistically/temp/2f18e57f_DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:07,583 - INFO - 🔍 [18:17:07] Starting classification: DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:07,584 - INFO - ⬆️ [18:17:07] Uploading: ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:07,584 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:07,608 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:07,611 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f18e57f_DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:07,611 - INFO - Processing image from S3...
2025-09-24 18:17:08,347 - INFO - S3 Image temp/52567568_BBWPIXK6JXYXQJO2PEFR.jpg: Extracted 540 characters, 49 lines
2025-09-24 18:17:08,348 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:08,348 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:08,863 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/ECFAS69KVA24ZYZZIU14.jpg -> s3://document-extraction-logistically/temp/bfce0b37_ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:08,863 - INFO - 🔍 [18:17:08] Starting classification: ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:08,864 - INFO - ⬆️ [18:17:08] Uploading: F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:08,866 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:08,882 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:08,887 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bfce0b37_ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:08,888 - INFO - Processing image from S3...
2025-09-24 18:17:10,191 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/F6NB931XUOJY2MJRN8VT.jpg -> s3://document-extraction-logistically/temp/d7ecf83e_F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:10,191 - INFO - 🔍 [18:17:10] Starting classification: F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:10,192 - INFO - ⬆️ [18:17:10] Uploading: H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:10,195 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:10,218 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:10,222 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d7ecf83e_F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:10,223 - INFO - Processing image from S3...
2025-09-24 18:17:10,645 - INFO - S3 Image temp/4a8079b9_C8WLD4UA74U7PBVTPBW4.jpg: Extracted 704 characters, 30 lines
2025-09-24 18:17:10,646 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:10,646 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:11,384 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cc352944-d54b-4595-b4aa-586e50c74994', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:11 GMT', 'content-type': 'application/json', 'content-length': '1687', 'connection': 'keep-alive', 'x-amzn-requestid': 'cc352944-d54b-4595-b4aa-586e50c74994'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to analyze content. The text shows:\n\n- Company: AMERILUX TRANSPORTATION\n- INVOICE# 07717\n- details: "TripNumber 2 VehicleID 282663 HubOdometer 200029"\n- "Received: Fleet One OTR 520.61"\n- "Pump: 18 Gallons: 133.868 Price/ Gal: 3.889 Subtotal 520.61 Sales Tax 0.00 Total 520.61"\n- "Loves #395"\n- "Type: SALE (ORIGINAL)"\n- "Qty Name Price Total" \n- "DIESEL 520.61" maybe a line item.\n\nThis appears to be a fuel receipt, as it\'s about diesel, gallons, price per gallon. However, it also has INVOICE# 07717 and TripNumber, VehicleID, etc. But the main focus is fuel purchase. The keywords: Fuel, Diesel, Pump, Gallons, Price/ Gal. It also says "Fuel Receipt" implicitly? The page has "Received: Fleet One OTR" maybe indicates receipt. Could be "fuel_receipt". The guidelines say: fuel_receipt: Receipt for fuel purchase. Keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. Yes.\n\nThus doc_type likely "fuel_receipt".\n\nWe should check if it\'s a continuation. No. Just page1. So output: page 1 with doc_type fuel_receipt.\n\nWe need to output via tool call classify_logistics_doc_type. The input requires array of documents objects each with page_no and doc_type. So:\n\n{\n "documents": [\n  { "page_no": 1, "doc_type": "fuel_receipt" }\n ]\n}\n\nThus call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2736, 'outputTokens': 394, 'totalTokens': 3130}, 'metrics': {'latencyMs': 1856}}
2025-09-24 18:17:11,385 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/52567568_BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:11,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/H0ZEPTKWB941MNO7G87A.jpeg -> s3://document-extraction-logistically/temp/e435e2c7_H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:11,475 - INFO - 🔍 [18:17:11] Starting classification: H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:11,476 - INFO - ⬆️ [18:17:11] Uploading: JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:11,478 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:11,496 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:11,501 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e435e2c7_H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:11,501 - INFO - Processing image from S3...
2025-09-24 18:17:11,904 - INFO - S3 Image temp/2f18e57f_DDRWUNS7W350XYVX3TFS.jpg: Extracted 524 characters, 40 lines
2025-09-24 18:17:11,904 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:11,904 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:12,696 - INFO - S3 Image temp/bfce0b37_ECFAS69KVA24ZYZZIU14.jpg: Extracted 433 characters, 25 lines
2025-09-24 18:17:12,697 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:12,697 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:12,749 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/JGVB6JHHAF3QE0JSDX2E.jpeg -> s3://document-extraction-logistically/temp/93f85419_JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:12,749 - INFO - 🔍 [18:17:12] Starting classification: JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:12,750 - INFO - ⬆️ [18:17:12] Uploading: OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:12,752 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:12,766 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:12,821 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/93f85419_JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:12,822 - INFO - Processing image from S3...
2025-09-24 18:17:13,291 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8a110b1b-f748-4feb-90e1-037c96f7709e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:13 GMT', 'content-type': 'application/json', 'content-length': '1286', 'connection': 'keep-alive', 'x-amzn-requestid': '8a110b1b-f748-4feb-90e1-037c96f7709e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have only one page. It looks like a fuel receipt? Let\'s examine. The text contains: "KWIK TRIP", "Pump", "Products Reefer Quantity Unit Cost", "Total", "Sales tax", "Signature", "Invoice #: 0090760", "Truck Number: 3482", "Merchant Number", "Card Number", "Billing Company: NBS / KWIK TRIP FLEET". It includes "Fuel", "Diesel", "N82.836" maybe gallons. It looks like a fuel receipt. Yes, the definition: Fuel Receipt: keywords Fuel, Diesel, Pump #, gallons, etc. So doc_type: fuel_receipt. But also includes "Invoice #: 0090760" maybe also a fuel receipt.\n\nLet\'s double-check: The page includes "KWIK TRIP 1281 /106 N. ROYAL AVENUE ABELGIUM. WI 53004/(262) 988-7024". This is a pump location. The page includes "Products Reefer Quantity Unit Cost". So it\'s a fuel receipt.\n\nThus output: page_no 1, doc_type "fuel_receipt". Use tool.\n\n'}}}, {'toolUse': {'toolUseId': 'tooluse_QLsevV2tQki8ucx5ytMJng', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2774, 'outputTokens': 259, 'totalTokens': 3033}, 'metrics': {'latencyMs': 1364}}
2025-09-24 18:17:13,291 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4a8079b9_C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:14,043 - INFO - S3 Image temp/d7ecf83e_F6NB931XUOJY2MJRN8VT.jpg: Extracted 819 characters, 41 lines
2025-09-24 18:17:14,044 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:14,044 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:14,115 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '155d58f3-68b8-4509-b4c8-b1083efba6ae', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:13 GMT', 'content-type': 'application/json', 'content-length': '885', 'connection': 'keep-alive', 'x-amzn-requestid': '155d58f3-68b8-4509-b4c8-b1083efba6ae'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify doc type. The page content: "KWIK TRIP #1267 ... Invoice# 0098760". It seems a fuel receipt: has pump quantity, price, product, amount, totals, merchant number, card number, etc. It is a fuel receipt for a fuel purchase. So doc_type: fuel_receipt. Confirm keywords: Fuel? It mentions "KWIK TRIP", "Invoice#", "Pump Quantity Price", "Product", "Total", "Merchant Number", "Card#". So yes fuel receipt. Output via function call. Need array of documents: one object with page_no 1 and doc_type "fuel_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2738, 'outputTokens': 160, 'totalTokens': 2898}, 'metrics': {'latencyMs': 911}}
2025-09-24 18:17:14,117 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2f18e57f_DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:14,239 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/OAJAO8BDLQUAQPD81DVE.jpeg -> s3://document-extraction-logistically/temp/939deb05_OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:14,239 - INFO - 🔍 [18:17:14] Starting classification: OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:14,240 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:14,240 - INFO - ⬆️ [18:17:14] Uploading: R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:14,252 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:14,261 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/939deb05_OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:14,261 - INFO - Processing image from S3...
2025-09-24 18:17:14,385 - INFO - S3 Image temp/e435e2c7_H0ZEPTKWB941MNO7G87A.jpeg: Extracted 301 characters, 28 lines
2025-09-24 18:17:14,386 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:14,386 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:14,648 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd0d1896d-5ce5-4424-88ef-4bebfc65f5e0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:14 GMT', 'content-type': 'application/json', 'content-length': '885', 'connection': 'keep-alive', 'x-amzn-requestid': 'd0d1896d-5ce5-4424-88ef-4bebfc65f5e0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content looks like a fuel receipt: It shows "Pump", "Products", "Diesel", "Total", "Invoice #", "Truck Number", "Billing Company", etc. So it\'s a fuel receipt. According to definitions, fuel_receipt keywords: Fuel, Diesel, Pump, gallons, etc. This page has Diesel, Pump, etc. So classify as fuel_receipt. No continuation signals. So output: array with one object: page_no:1 doc_type:"fuel_receipt". Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_yu0EuyXhRwSHP4N6NApYdw', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2697, 'outputTokens': 135, 'totalTokens': 2832}, 'metrics': {'latencyMs': 762}}
2025-09-24 18:17:14,649 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bfce0b37_ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:15,704 - INFO - S3 Image temp/93f85419_JGVB6JHHAF3QE0JSDX2E.jpeg: Extracted 318 characters, 28 lines
2025-09-24 18:17:15,704 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:15,705 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:15,878 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/R3202DZX6JMMABCF7GU8.jpg -> s3://document-extraction-logistically/temp/0daf4da1_R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:15,879 - INFO - 🔍 [18:17:15] Starting classification: R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:15,880 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:15,880 - INFO - ⬆️ [18:17:15] Uploading: VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:15,893 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:15,902 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0daf4da1_R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:15,902 - INFO - Processing image from S3...
2025-09-24 18:17:16,356 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7cf4f948-27b6-4e4c-b354-9be9d9afb8e2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:16 GMT', 'content-type': 'application/json', 'content-length': '1142', 'connection': 'keep-alive', 'x-amzn-requestid': '7cf4f948-27b6-4e4c-b354-9be9d9afb8e2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a page of a fuel receipt or a fuel purchase receipt. Contains "Fuel", "Diesel", "Pump", "Truck Number", "Card Number", "Billing Company: NBS / KWIK TRIP FLEET", "Invoice #", "Total", etc. Keywords: Fuel, Diesel, Pump, Card Number, etc. This matches fuel_receipt.\n\nCheck if any other doc type possible: It could be a fuel receipt. It\'s not an invoice, because it\'s a fuel receipt. There is no "fuel receipt" keyword but we have "Fuel", "Diesel", "Pump". The definition says Fuel Receipt includes Fuel, Diesel, Pump, Gallons, Ltrs, Fuel Receipt. So fuel_receipt.\n\nThus output single object for page1: {page_no: 1, doc_type: "fuel_receipt"}.\n\nWe need to use the tool classify_logistics_doc_type. Provide array of documents, with each object having page_no and doc_type.\n\nThus call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2809, 'outputTokens': 222, 'totalTokens': 3031}, 'metrics': {'latencyMs': 1100}}
2025-09-24 18:17:16,356 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d7ecf83e_F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:16,521 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2288cd4b-786a-4aee-9708-34a505b0586b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:16 GMT', 'content-type': 'application/json', 'content-length': '834', 'connection': 'keep-alive', 'x-amzn-requestid': '2288cd4b-786a-4aee-9708-34a505b0586b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content appears to be a fuel receipt maybe? It has fields: KWIK TRIP #209, address, Invoice#, Date, Time, Pump Quantity Price, etc. "NBS/KWIK TRIP FLEET CARD# ICR AUTH # P50332 RESPONSE: APPROVED AUTH REF#: P50332". Likely a fuel receipt. Keywords: Fuel, Pump, Quantity, Price. No explicit "Fuel Receipt" but typical fuel receipt content. The doc type should be fuel_receipt. There\'s no other type that matches better. So output: page_no 1, doc_type fuel_receipt. Use the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2665, 'outputTokens': 155, 'totalTokens': 2820}, 'metrics': {'latencyMs': 876}}
2025-09-24 18:17:16,521 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e435e2c7_H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:17,257 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/VAMKEOJNCLNVRBTZBF3S.jpeg -> s3://document-extraction-logistically/temp/eb1eae57_VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:17,257 - INFO - 🔍 [18:17:17] Starting classification: VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:17,259 - INFO - Initializing TextractProcessor...
2025-09-24 18:17:17,279 - INFO - Initializing BedrockProcessor...
2025-09-24 18:17:17,291 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb1eae57_VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:17,298 - INFO - Processing image from S3...
2025-09-24 18:17:17,307 - INFO - 

BBWPIXK6JXYXQJO2PEFR.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:17,307 - INFO - 

✓ Saved result: output/run1_BBWPIXK6JXYXQJO2PEFR.json
2025-09-24 18:17:17,616 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/52567568_BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:17,634 - INFO - 

C8WLD4UA74U7PBVTPBW4.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:17,634 - INFO - 

✓ Saved result: output/run1_C8WLD4UA74U7PBVTPBW4.json
2025-09-24 18:17:17,727 - INFO - S3 Image temp/939deb05_OAJAO8BDLQUAQPD81DVE.jpeg: Extracted 311 characters, 29 lines
2025-09-24 18:17:17,728 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:17,729 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8747d8ef-ced4-4b86-befc-a6bc43806458', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:17 GMT', 'content-type': 'application/json', 'content-length': '800', 'connection': 'keep-alive', 'x-amzn-requestid': '8747d8ef-ced4-4b86-befc-a6bc43806458'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. Let\'s read. It says "Invoice# 0090428". Keywords: Invoice. It looks like fuel receipt? It shows pump quantity, price, product DSL2 etc. "NBS/KWIK TRIP FLEET CARD# ICR AUTH ... RESPONSE: APPROVED AUTH REF#" That looks like fuel receipt: Fuel, Diesel, Pump #? There is no pump number but pump quantity price. It\'s a fuel receipt. So doc_type: fuel_receipt.\n\nNo continuation. So output one object: page_no 1, doc_type "fuel_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2669, 'outputTokens': 144, 'totalTokens': 2813}, 'metrics': {'latencyMs': 800}}
2025-09-24 18:17:17,729 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:17,730 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/93f85419_JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:17,925 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4a8079b9_C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:17,948 - INFO - 

DDRWUNS7W350XYVX3TFS.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:17,949 - INFO - 

✓ Saved result: output/run1_DDRWUNS7W350XYVX3TFS.json
2025-09-24 18:17:18,279 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f18e57f_DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:18,287 - INFO - 

ECFAS69KVA24ZYZZIU14.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:18,287 - INFO - 

✓ Saved result: output/run1_ECFAS69KVA24ZYZZIU14.json
2025-09-24 18:17:18,584 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bfce0b37_ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:18,603 - INFO - 

F6NB931XUOJY2MJRN8VT.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:18,603 - INFO - 

✓ Saved result: output/run1_F6NB931XUOJY2MJRN8VT.json
2025-09-24 18:17:18,904 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d7ecf83e_F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:18,914 - INFO - 

H0ZEPTKWB941MNO7G87A.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:18,914 - INFO - 

✓ Saved result: output/run1_H0ZEPTKWB941MNO7G87A.json
2025-09-24 18:17:19,223 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e435e2c7_H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:19,234 - INFO - 

JGVB6JHHAF3QE0JSDX2E.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:19,234 - INFO - 

✓ Saved result: output/run1_JGVB6JHHAF3QE0JSDX2E.json
2025-09-24 18:17:19,363 - INFO - S3 Image temp/0daf4da1_R3202DZX6JMMABCF7GU8.jpg: Extracted 501 characters, 35 lines
2025-09-24 18:17:19,363 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:19,363 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:19,562 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/93f85419_JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:19,853 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4a9d31e1-1c04-4ac7-a96f-c96488ef0b69', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:19 GMT', 'content-type': 'application/json', 'content-length': '887', 'connection': 'keep-alive', 'x-amzn-requestid': '4a9d31e1-1c04-4ac7-a96f-c96488ef0b69'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to use tool classify_logistics_doc_type. Determine doc_type.\n\nThe content seems like a fuel receipt? It says "KWIK TRIP #674", address, "Invoice# 0091264", Pump Quantity Price, amounts, totals, card number, auth. That looks like a fuel receipt from a fuel station (KWIK TRIP). So doc_type should be fuel_receipt. Confirm: keywords include Fuel, Diesel? It says Pump Quantity Price, maybe fuel purchase. So yes fuel_receipt.\n\nReturn array with single object: page_no 1, doc_type "fuel_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2670, 'outputTokens': 154, 'totalTokens': 2824}, 'metrics': {'latencyMs': 903}}
2025-09-24 18:17:19,853 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/939deb05_OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:19,864 - INFO - 

OAJAO8BDLQUAQPD81DVE.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:19,864 - INFO - 

✓ Saved result: output/run1_OAJAO8BDLQUAQPD81DVE.json
2025-09-24 18:17:20,163 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/939deb05_OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:20,678 - INFO - S3 Image temp/eb1eae57_VAMKEOJNCLNVRBTZBF3S.jpeg: Extracted 296 characters, 29 lines
2025-09-24 18:17:20,678 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:17:20,678 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:17:21,509 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c4aa3cc6-ec09-4e18-ad8b-757f8b7fc420', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:21 GMT', 'content-type': 'application/json', 'content-length': '770', 'connection': 'keep-alive', 'x-amzn-requestid': 'c4aa3cc6-ec09-4e18-ad8b-757f8b7fc420'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to identify doc type.\n\nContent: Fuel receipt. Keywords: Fuel, Diesel, Pump#, Gallons, Fuel Receipt. So doc_type = fuel_receipt.\n\nWe must output via tool classify_logistics_doc_type with array of objects. Only one page.\n\nLet\'s ensure correct JSON format. The function expects:\n\n{\n  documents: [\n    {\n      page_no: number,\n      doc_type: "...",\n    }\n  ]\n}\n\nReturn that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2726, 'outputTokens': 121, 'totalTokens': 2847}, 'metrics': {'latencyMs': 768}}
2025-09-24 18:17:21,510 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0daf4da1_R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:21,523 - INFO - 

R3202DZX6JMMABCF7GU8.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:21,523 - INFO - 

✓ Saved result: output/run1_R3202DZX6JMMABCF7GU8.json
2025-09-24 18:17:21,832 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0daf4da1_R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:23,178 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7848d987-6d0a-4d11-91f1-95e5941f28ec', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:47:22 GMT', 'content-type': 'application/json', 'content-length': '1346', 'connection': 'keep-alive', 'x-amzn-requestid': '7848d987-6d0a-4d11-91f1-95e5941f28ec'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to determine doc_type.\n\nContent: "KWIK TRIP #674 ... Invoice# 0091491 ... Pump Quantity Price ... DSL2 ... DEFBULK ... Total 541.97 ... CARD# ICR 1591 AUTH # P32191 RESPONSE: APPROVED AUTH REF#: P32191"\n\nThis looks like a Fuel Receipt: fuel purchase at pump, maybe "Fuel Receipt" with pump quantity price. Keywords: Fuel, Diesel, Pump, Gallons, etc. There\'s "Pump Quantity Price" and "Response: Approved" indicating fuel purchase authorization. So classification: fuel_receipt.\n\nCheck definitions: fuel_receipt keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. We have Pump. Also "AUTH" typical of fuel receipts.\n\nNo invoice header. It\'s more like a fuel receipt rather than invoice. So doc_type: fuel_receipt.\n\nReturn using tool classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "fuel_receipt".\n\nMake sure use correct enum names: "fuel_receipt" is in the list.\n\nSo produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2667, 'outputTokens': 263, 'totalTokens': 2930}, 'metrics': {'latencyMs': 1188}}
2025-09-24 18:17:23,179 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb1eae57_VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:23,199 - INFO - 

VAMKEOJNCLNVRBTZBF3S.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-24 18:17:23,199 - INFO - 

✓ Saved result: output/run1_VAMKEOJNCLNVRBTZBF3S.json
2025-09-24 18:17:23,537 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb1eae57_VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:23,538 - INFO - 
📊 Processing Summary:
2025-09-24 18:17:23,538 - INFO -    Total files: 10
2025-09-24 18:17:23,538 - INFO -    Successful: 10
2025-09-24 18:17:23,538 - INFO -    Failed: 0
2025-09-24 18:17:23,538 - INFO -    Duration: 22.71 seconds
2025-09-24 18:17:23,538 - INFO -    Output directory: output
2025-09-24 18:17:23,538 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:17:23,539 - INFO -    📄 BBWPIXK6JXYXQJO2PEFR.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 C8WLD4UA74U7PBVTPBW4.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 DDRWUNS7W350XYVX3TFS.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 ECFAS69KVA24ZYZZIU14.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 F6NB931XUOJY2MJRN8VT.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 H0ZEPTKWB941MNO7G87A.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 JGVB6JHHAF3QE0JSDX2E.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 OAJAO8BDLQUAQPD81DVE.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 R3202DZX6JMMABCF7GU8.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,539 - INFO -    📄 VAMKEOJNCLNVRBTZBF3S.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-24 18:17:23,540 - INFO - 
============================================================================================================================================
2025-09-24 18:17:23,540 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:17:23,540 - INFO - ============================================================================================================================================
2025-09-24 18:17:23,540 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:17:23,540 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:17:23,540 - INFO - BBWPIXK6JXYXQJO2PEFR.jpg                           1      fuel_receipt                             run1_BBWPIXK6JXYXQJO2PEFR.json                                                  
2025-09-24 18:17:23,540 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/BBWPIXK6JXYXQJO2PEFR.jpg
2025-09-24 18:17:23,540 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BBWPIXK6JXYXQJO2PEFR.json
2025-09-24 18:17:23,540 - INFO - 
2025-09-24 18:17:23,540 - INFO - C8WLD4UA74U7PBVTPBW4.jpg                           1      fuel_receipt                             run1_C8WLD4UA74U7PBVTPBW4.json                                                  
2025-09-24 18:17:23,541 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/C8WLD4UA74U7PBVTPBW4.jpg
2025-09-24 18:17:23,541 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C8WLD4UA74U7PBVTPBW4.json
2025-09-24 18:17:23,541 - INFO - 
2025-09-24 18:17:23,541 - INFO - DDRWUNS7W350XYVX3TFS.jpg                           1      fuel_receipt                             run1_DDRWUNS7W350XYVX3TFS.json                                                  
2025-09-24 18:17:23,541 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/DDRWUNS7W350XYVX3TFS.jpg
2025-09-24 18:17:23,541 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DDRWUNS7W350XYVX3TFS.json
2025-09-24 18:17:23,541 - INFO - 
2025-09-24 18:17:23,541 - INFO - ECFAS69KVA24ZYZZIU14.jpg                           1      fuel_receipt                             run1_ECFAS69KVA24ZYZZIU14.json                                                  
2025-09-24 18:17:23,541 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/ECFAS69KVA24ZYZZIU14.jpg
2025-09-24 18:17:23,541 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ECFAS69KVA24ZYZZIU14.json
2025-09-24 18:17:23,541 - INFO - 
2025-09-24 18:17:23,541 - INFO - F6NB931XUOJY2MJRN8VT.jpg                           1      fuel_receipt                             run1_F6NB931XUOJY2MJRN8VT.json                                                  
2025-09-24 18:17:23,541 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/F6NB931XUOJY2MJRN8VT.jpg
2025-09-24 18:17:23,541 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_F6NB931XUOJY2MJRN8VT.json
2025-09-24 18:17:23,541 - INFO - 
2025-09-24 18:17:23,541 - INFO - H0ZEPTKWB941MNO7G87A.jpeg                          1      fuel_receipt                             run1_H0ZEPTKWB941MNO7G87A.json                                                  
2025-09-24 18:17:23,542 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/H0ZEPTKWB941MNO7G87A.jpeg
2025-09-24 18:17:23,542 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_H0ZEPTKWB941MNO7G87A.json
2025-09-24 18:17:23,542 - INFO - 
2025-09-24 18:17:23,542 - INFO - JGVB6JHHAF3QE0JSDX2E.jpeg                          1      fuel_receipt                             run1_JGVB6JHHAF3QE0JSDX2E.json                                                  
2025-09-24 18:17:23,542 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/JGVB6JHHAF3QE0JSDX2E.jpeg
2025-09-24 18:17:23,542 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_JGVB6JHHAF3QE0JSDX2E.json
2025-09-24 18:17:23,542 - INFO - 
2025-09-24 18:17:23,542 - INFO - OAJAO8BDLQUAQPD81DVE.jpeg                          1      fuel_receipt                             run1_OAJAO8BDLQUAQPD81DVE.json                                                  
2025-09-24 18:17:23,542 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/OAJAO8BDLQUAQPD81DVE.jpeg
2025-09-24 18:17:23,542 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OAJAO8BDLQUAQPD81DVE.json
2025-09-24 18:17:23,542 - INFO - 
2025-09-24 18:17:23,542 - INFO - R3202DZX6JMMABCF7GU8.jpg                           1      fuel_receipt                             run1_R3202DZX6JMMABCF7GU8.json                                                  
2025-09-24 18:17:23,542 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/R3202DZX6JMMABCF7GU8.jpg
2025-09-24 18:17:23,542 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_R3202DZX6JMMABCF7GU8.json
2025-09-24 18:17:23,542 - INFO - 
2025-09-24 18:17:23,542 - INFO - VAMKEOJNCLNVRBTZBF3S.jpeg                          1      fuel_receipt                             run1_VAMKEOJNCLNVRBTZBF3S.json                                                  
2025-09-24 18:17:23,542 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/VAMKEOJNCLNVRBTZBF3S.jpeg
2025-09-24 18:17:23,542 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VAMKEOJNCLNVRBTZBF3S.json
2025-09-24 18:17:23,543 - INFO - 
2025-09-24 18:17:23,543 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:17:23,543 - INFO - Total entries: 10
2025-09-24 18:17:23,543 - INFO - ============================================================================================================================================
2025-09-24 18:17:23,543 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:17:23,543 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:17:23,543 - INFO -   1. BBWPIXK6JXYXQJO2PEFR.jpg            Page 1   → fuel_receipt    | run1_BBWPIXK6JXYXQJO2PEFR.json
2025-09-24 18:17:23,543 - INFO -   2. C8WLD4UA74U7PBVTPBW4.jpg            Page 1   → fuel_receipt    | run1_C8WLD4UA74U7PBVTPBW4.json
2025-09-24 18:17:23,543 - INFO -   3. DDRWUNS7W350XYVX3TFS.jpg            Page 1   → fuel_receipt    | run1_DDRWUNS7W350XYVX3TFS.json
2025-09-24 18:17:23,543 - INFO -   4. ECFAS69KVA24ZYZZIU14.jpg            Page 1   → fuel_receipt    | run1_ECFAS69KVA24ZYZZIU14.json
2025-09-24 18:17:23,543 - INFO -   5. F6NB931XUOJY2MJRN8VT.jpg            Page 1   → fuel_receipt    | run1_F6NB931XUOJY2MJRN8VT.json
2025-09-24 18:17:23,543 - INFO -   6. H0ZEPTKWB941MNO7G87A.jpeg           Page 1   → fuel_receipt    | run1_H0ZEPTKWB941MNO7G87A.json
2025-09-24 18:17:23,543 - INFO -   7. JGVB6JHHAF3QE0JSDX2E.jpeg           Page 1   → fuel_receipt    | run1_JGVB6JHHAF3QE0JSDX2E.json
2025-09-24 18:17:23,543 - INFO -   8. OAJAO8BDLQUAQPD81DVE.jpeg           Page 1   → fuel_receipt    | run1_OAJAO8BDLQUAQPD81DVE.json
2025-09-24 18:17:23,543 - INFO -   9. R3202DZX6JMMABCF7GU8.jpg            Page 1   → fuel_receipt    | run1_R3202DZX6JMMABCF7GU8.json
2025-09-24 18:17:23,543 - INFO -  10. VAMKEOJNCLNVRBTZBF3S.jpeg           Page 1   → fuel_receipt    | run1_VAMKEOJNCLNVRBTZBF3S.json
2025-09-24 18:17:23,543 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:17:23,544 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 22.708294, 'processed_files': [{'filename': 'BBWPIXK6JXYXQJO2PEFR.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/BBWPIXK6JXYXQJO2PEFR.jpg'}, {'filename': 'C8WLD4UA74U7PBVTPBW4.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/C8WLD4UA74U7PBVTPBW4.jpg'}, {'filename': 'DDRWUNS7W350XYVX3TFS.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/DDRWUNS7W350XYVX3TFS.jpg'}, {'filename': 'ECFAS69KVA24ZYZZIU14.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/ECFAS69KVA24ZYZZIU14.jpg'}, {'filename': 'F6NB931XUOJY2MJRN8VT.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/F6NB931XUOJY2MJRN8VT.jpg'}, {'filename': 'H0ZEPTKWB941MNO7G87A.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/H0ZEPTKWB941MNO7G87A.jpeg'}, {'filename': 'JGVB6JHHAF3QE0JSDX2E.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/JGVB6JHHAF3QE0JSDX2E.jpeg'}, {'filename': 'OAJAO8BDLQUAQPD81DVE.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/OAJAO8BDLQUAQPD81DVE.jpeg'}, {'filename': 'R3202DZX6JMMABCF7GU8.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/R3202DZX6JMMABCF7GU8.jpg'}, {'filename': 'VAMKEOJNCLNVRBTZBF3S.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/fuel_receipt/VAMKEOJNCLNVRBTZBF3S.jpeg'}]}
