{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 8.176731353159994e-05, "Y": 0.0}, {"X": 1.0, "Y": 0.0003668544813990593}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "10c3494f-314d-44bc-9870-6ca98ff829d7", "Relationships": [{"Type": "CHILD", "Ids": ["74135278-49ea-4ade-a69a-74e77ab7c7dd", "fd5ce7ae-0594-43c7-91f5-40a02be049b6", "86cd41a4-1f6c-4326-9904-5ad44f4f2b03", "185fee6b-b138-4ca3-b33f-3dd982a15591", "856cdc57-6667-4961-9d80-0dc31d4a42e1", "51a5e09f-673b-4444-9e2b-59a1258f49e6", "b3b49c77-c89a-44d5-bfd8-635dc1f580c8", "c5ef189b-6fde-4ec4-9cc8-c5c4e9564197", "412b8f32-7352-44a7-9dc2-2e1164a257ba", "64fcc142-67a2-405a-b4e2-eb56875eb8f4", "afcf573e-b38f-4173-9174-7101f6fdc42b", "9c57b827-7982-4fc4-84b3-8d9f9fc5e1f2", "57cd5b71-9b2b-46e9-b73a-de0d8b0735d9", "b763de9f-7341-447f-83b5-0a20d9345437", "b5817ae8-62cc-4a31-b500-c43e0ec84cd3", "c0b278ba-3b13-41b6-a123-8346b2be94a3", "9cabe2ea-31b2-4474-bb74-ecdd673b41d9", "5df413c8-f1a2-4f9b-bc5c-3976ef5bac7e", "3acfa525-083d-441b-bdea-eb528ef706a5", "50fb009a-f18b-40e5-8526-9006f09a8162", "30f10c6a-58ee-4683-8264-e091b681a0b6", "c74b6ffe-14bb-4ed4-8a29-342ecbb203da", "6642a534-f733-4bbd-ab24-252f31899dc2", "baae50d3-6692-43d3-97b8-80cf05471570", "12692ed0-82b4-4dce-92eb-49e954e1e5d3", "86783a21-61db-4d01-a630-df81c494b726", "41f63533-5d3f-45cc-bf3d-0fe244f6bf30", "ed6d04bc-f7b0-4523-a6a8-5ebad1a65b2e", "a63493bb-b3ba-4c5b-bf7c-45a61c25b84d", "9185871f-4200-4733-b019-bc42ddf7110f", "603b29e4-d86d-4de9-a1e5-f196bf4abc77", "a1f8a01c-37a6-45a6-8668-44d71b529568", "209c9749-891e-4124-9765-43dea775ea4b", "7f9eb294-5f0c-4fca-9e36-7d915872e415", "bb12771b-0901-44ae-8ca8-c55e67458e01", "7bdfd4ea-17f9-45e8-83dd-4b2419ede82d", "2f0277e7-eb08-4281-9503-7c21274677db", "b3e6f777-96b1-405f-ac9b-140da10c8974", "ba1bb116-0c8d-4964-9770-904234130548", "f37011db-cbf2-4a8f-af65-45377b77f8a1", "eb0f3fd0-ce81-4a9f-a68b-02169f4bf6e7", "90bbdadc-9f89-4981-8e74-84d6735a12f1"]}]}, {"BlockType": "LINE", "Confidence": 44.808074951171875, "Text": "ised", "Geometry": {"BoundingBox": {"Width": 0.031261805444955826, "Height": 0.010969812050461769, "Left": 0.10575716197490692, "Top": 0.0461379736661911}, "Polygon": [{"X": 0.10586394369602203, "Y": 0.0461379736661911}, {"X": 0.13701896369457245, "Y": 0.04640067368745804}, {"X": 0.13691236078739166, "Y": 0.05710778385400772}, {"X": 0.10575716197490692, "Y": 0.05684506893157959}]}, "Id": "74135278-49ea-4ade-a69a-74e77ab7c7dd", "Relationships": [{"Type": "CHILD", "Ids": ["d659153d-fda6-4aaf-b3b7-b13363dbef99"]}]}, {"BlockType": "LINE", "Confidence": 78.62544250488281, "Text": "Number", "Geometry": {"BoundingBox": {"Width": 0.04923126474022865, "Height": 0.010663959197700024, "Left": 0.13983242213726044, "Top": 0.04649730026721954}, "Polygon": [{"X": 0.1399344652891159, "Y": 0.04649730026721954}, {"X": 0.1890636831521988, "Y": 0.046911563724279404}, {"X": 0.1889619082212448, "Y": 0.05716126039624214}, {"X": 0.13983242213726044, "Y": 0.05674697831273079}]}, "Id": "fd5ce7ae-0594-43c7-91f5-40a02be049b6", "Relationships": [{"Type": "CHILD", "Ids": ["45663b83-5e70-459c-88a5-3cbff0e6d5f0"]}]}, {"BlockType": "LINE", "Confidence": 47.67478561401367, "Text": "20312", "Geometry": {"BoundingBox": {"Width": 0.04043268412351608, "Height": 0.01061754859983921, "Left": 0.10436731576919556, "Top": 0.059050269424915314}, "Polygon": [{"X": 0.10446982085704803, "Y": 0.059050269424915314}, {"X": 0.14480000734329224, "Y": 0.0593903549015522}, {"X": 0.14469771087169647, "Y": 0.06966781616210938}, {"X": 0.10436731576919556, "Y": 0.069327712059021}]}, "Id": "86cd41a4-1f6c-4326-9904-5ad44f4f2b03", "Relationships": [{"Type": "CHILD", "Ids": ["264fd5f0-a945-4d39-86da-3c7e9613b0c2"]}]}, {"BlockType": "LINE", "Confidence": 98.73664855957031, "Text": "Saw 8787849", "Geometry": {"BoundingBox": {"Width": 0.4440680742263794, "Height": 0.05609671771526337, "Left": 0.43814972043037415, "Top": 0.03474485129117966}, "Polygon": [{"X": 0.4386628568172455, "Y": 0.03474485129117966}, {"X": 0.8822177648544312, "Y": 0.038484733551740646}, {"X": 0.8817166090011597, "Y": 0.09084156900644302}, {"X": 0.43814972043037415, "Y": 0.08710075914859772}]}, "Id": "185fee6b-b138-4ca3-b33f-3dd982a15591", "Relationships": [{"Type": "CHILD", "Ids": ["f665bf13-b1f7-48b5-b473-86eece6b242e", "c7789e52-eb83-44db-9355-c429a75b0ebf"]}]}, {"BlockType": "LINE", "Confidence": 9.859843254089355, "Text": "rermatic", "Geometry": {"BoundingBox": {"Width": 0.09043282270431519, "Height": 0.017054790630936623, "Left": 0.4480265974998474, "Top": 0.08612104505300522}, "Polygon": [{"X": 0.4481862187385559, "Y": 0.08612104505300522}, {"X": 0.5384594202041626, "Y": 0.08688236027956009}, {"X": 0.5383005738258362, "Y": 0.10317584127187729}, {"X": 0.4480265974998474, "Y": 0.10241446644067764}]}, "Id": "856cdc57-6667-4961-9d80-0dc31d4a42e1", "Relationships": [{"Type": "CHILD", "Ids": ["2af97122-fd76-4184-ba45-1918496b5ba3"]}]}, {"BlockType": "LINE", "Confidence": 15.084552764892578, "Text": "AMAMA", "Geometry": {"BoundingBox": {"Width": 0.2227204442024231, "Height": 0.03463365137577057, "Left": 0.4022594392299652, "Top": 0.12737968564033508}, "Polygon": [{"X": 0.4025810956954956, "Y": 0.12737968564033508}, {"X": 0.6249799132347107, "Y": 0.12925560772418976}, {"X": 0.6246619820594788, "Y": 0.16201333701610565}, {"X": 0.4022594392299652, "Y": 0.1601371467113495}]}, "Id": "51a5e09f-673b-4444-9e2b-59a1258f49e6", "Relationships": [{"Type": "CHILD", "Ids": ["f1d89985-b92a-48ce-84fd-a0d73dc7f99c"]}]}, {"BlockType": "LINE", "Confidence": 97.9833984375, "Text": "SAWYER NURSERY.", "Geometry": {"BoundingBox": {"Width": 0.4305690824985504, "Height": 0.028306514024734497, "Left": 0.29669126868247986, "Top": 0.17423385381698608}, "Polygon": [{"X": 0.29693490266799927, "Y": 0.17423385381698608}, {"X": 0.7272603511810303, "Y": 0.17786432802677155}, {"X": 0.7270221710205078, "Y": 0.20254036784172058}, {"X": 0.29669126868247986, "Y": 0.1989094614982605}]}, "Id": "b3b49c77-c89a-44d5-bfd8-635dc1f580c8", "Relationships": [{"Type": "CHILD", "Ids": ["b6a6726d-09cb-47fc-a680-29f805566f3c", "251f6629-77db-4953-8d44-f38a73d404d2"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "Truckload Check-in Form", "Geometry": {"BoundingBox": {"Width": 0.3433934152126312, "Height": 0.024294231086969376, "Left": 0.347440242767334, "Top": 0.2073768526315689}, "Polygon": [{"X": 0.3476509749889374, "Y": 0.2073768526315689}, {"X": 0.6908336877822876, "Y": 0.21027255058288574}, {"X": 0.6906267404556274, "Y": 0.23167107999324799}, {"X": 0.347440242767334, "Y": 0.22877509891986847}]}, "Id": "c5ef189b-6fde-4ec4-9cc8-c5c4e9564197", "Relationships": [{"Type": "CHILD", "Ids": ["3a3dbf41-6b2b-4a51-a100-9f3aad136d2d", "96074713-ee59-4614-9b1e-af7a821679c8", "f6fa651c-340b-470c-8a4e-73b45fa2b8ef"]}]}, {"BlockType": "LINE", "Confidence": 99.62850952148438, "Text": "Clear-to-Pay Voucher", "Geometry": {"BoundingBox": {"Width": 0.2911575138568878, "Height": 0.026722079142928123, "Left": 0.3744858503341675, "Top": 0.2366248071193695}, "Polygon": [{"X": 0.37472444772720337, "Y": 0.2366248071193695}, {"X": 0.6656433343887329, "Y": 0.2390798032283783}, {"X": 0.6654083728790283, "Y": 0.2633468806743622}, {"X": 0.3744858503341675, "Y": 0.2608915865421295}]}, "Id": "412b8f32-7352-44a7-9dc2-2e1164a257ba", "Relationships": [{"Type": "CHILD", "Ids": ["fab1f256-5461-4d5c-a940-0dcc1bd10869", "d8097052-db15-42e9-8502-c3e1a7a057dd"]}]}, {"BlockType": "LINE", "Confidence": 99.44908905029297, "Text": "Load # 20512 AL", "Geometry": {"BoundingBox": {"Width": 0.2437153160572052, "Height": 0.022141728550195694, "Left": 0.11143584549427032, "Top": 0.26829713582992554}, "Polygon": [{"X": 0.11163607984781265, "Y": 0.26829713582992554}, {"X": 0.35515114665031433, "Y": 0.27035239338874817}, {"X": 0.35495343804359436, "Y": 0.29043886065483093}, {"X": 0.11143584549427032, "Y": 0.288383424282074}]}, "Id": "64fcc142-67a2-405a-b4e2-eb56875eb8f4", "Relationships": [{"Type": "CHILD", "Ids": ["e7cb34f4-4db1-4500-8ae4-bf277626cf35", "9e23d76b-cadc-438f-9440-d0a61221680e", "ee57ddbe-af26-40db-8a9b-a6198f538851", "0e0e535e-4561-439f-9d81-06f6a9dba9d8"]}]}, {"BlockType": "LINE", "Confidence": 99.19403839111328, "Text": "Broker PE", "Geometry": {"BoundingBox": {"Width": 0.1488107144832611, "Height": 0.03895469382405281, "Left": 0.5778282284736633, "Top": 0.2608273923397064}, "Polygon": [{"X": 0.5781950354576111, "Y": 0.2608273923397064}, {"X": 0.7266389727592468, "Y": 0.26208019256591797}, {"X": 0.7262750864028931, "Y": 0.2997820973396301}, {"X": 0.5778282284736633, "Y": 0.2985290586948395}]}, "Id": "afcf573e-b38f-4173-9174-7101f6fdc42b", "Relationships": [{"Type": "CHILD", "Ids": ["2a50dfdd-b9f9-4098-8601-78aab7830240", "04ee649d-0da3-4f17-bb35-10ab9bdae0c4"]}]}, {"BlockType": "LINE", "Confidence": 97.58211517333984, "Text": "Date Processed 4/24/24", "Geometry": {"BoundingBox": {"Width": 0.2413576990365982, "Height": 0.02630828134715557, "Left": 0.11070937663316727, "Top": 0.30120667815208435}, "Polygon": [{"X": 0.11095134913921356, "Y": 0.30120667815208435}, {"X": 0.3520670533180237, "Y": 0.3032419979572296}, {"X": 0.35182809829711914, "Y": 0.32751497626304626}, {"X": 0.11070937663316727, "Y": 0.3254794478416443}]}, "Id": "9c57b827-7982-4fc4-84b3-8d9f9fc5e1f2", "Relationships": [{"Type": "CHILD", "Ids": ["06f9c79f-ca11-4262-a76e-69acd1d4ca01", "756ba2b8-9cfd-465f-aee3-602a6031172b", "93e40ebe-d7fd-4cb4-bd4a-04ea3cb71c9d"]}]}, {"BlockType": "LINE", "Confidence": 88.55894470214844, "Text": "Load Cost $ 4656.00", "Geometry": {"BoundingBox": {"Width": 0.24968014657497406, "Height": 0.03604061156511307, "Left": 0.57630854845047, "Top": 0.31578779220581055}, "Polygon": [{"X": 0.5766386985778809, "Y": 0.31578779220581055}, {"X": 0.8259887099266052, "Y": 0.3178927004337311}, {"X": 0.8256629109382629, "Y": 0.351828396320343}, {"X": 0.57630854845047, "Y": 0.3497231900691986}]}, "Id": "57cd5b71-9b2b-46e9-b73a-de0d8b0735d9", "Relationships": [{"Type": "CHILD", "Ids": ["d16ba020-267b-4553-8cd7-1789b5b3beca", "3f8c23a8-3848-4bff-9ad2-e8b5320580a5", "d96209db-f870-4b0c-997d-3574ae26507c"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "<PERSON><PERSON> Returned", "Geometry": {"BoundingBox": {"Width": 0.1254054754972458, "Height": 0.013602907769382, "Left": 0.11451879888772964, "Top": 0.3455684781074524}, "Polygon": [{"X": 0.11464383453130722, "Y": 0.3455684781074524}, {"X": 0.23992426693439484, "Y": 0.3466261923313141}, {"X": 0.23980005085468292, "Y": 0.35917139053344727}, {"X": 0.11451879888772964, "Y": 0.3581136167049408}]}, "Id": "b763de9f-7341-447f-83b5-0a20d9345437", "Relationships": [{"Type": "CHILD", "Ids": ["cae1b69e-6d82-49c5-87b8-877206382581", "280c98a3-a094-4942-843f-bb613c60ec57"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "50", "Geometry": {"BoundingBox": {"Width": 0.031743600964546204, "Height": 0.020916307345032692, "Left": 0.2871854305267334, "Top": 0.3405229449272156}, "Polygon": [{"X": 0.2873893976211548, "Y": 0.3405229449272156}, {"X": 0.3189290463924408, "Y": 0.34078919887542725}, {"X": 0.3187254071235657, "Y": 0.36143922805786133}, {"X": 0.2871854305267334, "Y": 0.36117294430732727}]}, "Id": "b5817ae8-62cc-4a31-b500-c43e0ec84cd3", "Relationships": [{"Type": "CHILD", "Ids": ["8a30ac81-812b-424b-9926-6a78dd7aa715"]}]}, {"BlockType": "LINE", "Confidence": 99.44535827636719, "Text": "Surcharge + $", "Geometry": {"BoundingBox": {"Width": 0.12023698538541794, "Height": 0.016149714589118958, "Left": 0.5697786808013916, "Top": 0.37015679478645325}, "Polygon": [{"X": 0.5699259638786316, "Y": 0.37015679478645325}, {"X": 0.6900156736373901, "Y": 0.3711707890033722}, {"X": 0.6898692846298218, "Y": 0.3863065242767334}, {"X": 0.5697786808013916, "Y": 0.3852924704551697}]}, "Id": "c0b278ba-3b13-41b6-a123-8346b2be94a3", "Relationships": [{"Type": "CHILD", "Ids": ["7d8d6d6b-e3f8-427d-a9a9-63dc5cf53802", "51b39baa-071f-4b1d-8275-6e54d0c5de39", "9d8857b7-693b-4600-8acb-a1d89a12b995"]}]}, {"BlockType": "LINE", "Confidence": 83.90121459960938, "Text": "50 XS 400 = $ 200.00 $", "Geometry": {"BoundingBox": {"Width": 0.3584596812725067, "Height": 0.03389798477292061, "Left": 0.1197221577167511, "Top": 0.39851802587509155}, "Polygon": [{"X": 0.12002976983785629, "Y": 0.39851802587509155}, {"X": 0.4781818389892578, "Y": 0.40154245495796204}, {"X": 0.47787991166114807, "Y": 0.43241599202156067}, {"X": 0.1197221577167511, "Y": 0.42939111590385437}]}, "Id": "9cabe2ea-31b2-4474-bb74-ecdd673b41d9", "Relationships": [{"Type": "CHILD", "Ids": ["0c1b24a5-b16c-4aeb-b1dc-25e59486dba6", "fd076ade-2557-4039-bc95-a3f3ee6d3194", "a9d45455-61c4-4ab6-b0d9-c78396ff067f", "8712aad7-d888-402e-adc1-68cdb7ca490a", "371a712d-0fdd-4696-96aa-076bbe5ab94f", "287e7f3f-7567-4327-8011-b916c21ba804"]}]}, {"BlockType": "LINE", "Confidence": 96.40910339355469, "Text": "Deductions - $", "Geometry": {"BoundingBox": {"Width": 0.1252211481332779, "Height": 0.014502836391329765, "Left": 0.5695889592170715, "Top": 0.40438494086265564}, "Polygon": [{"X": 0.5697198510169983, "Y": 0.40438494086265564}, {"X": 0.694810152053833, "Y": 0.4054412841796875}, {"X": 0.6946801543235779, "Y": 0.41888776421546936}, {"X": 0.5695889592170715, "Y": 0.4178313612937927}]}, "Id": "5df413c8-f1a2-4f9b-bc5c-3976ef5bac7e", "Relationships": [{"Type": "CHILD", "Ids": ["85f95671-9c76-4401-b56f-e9e8df64b54a", "72b2273c-5481-4a93-bdc0-1eb5a3525813", "be11e52a-a70f-483f-98ff-cf9082a4dde1"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "RACKS", "Geometry": {"BoundingBox": {"Width": 0.06484325975179672, "Height": 0.012793132103979588, "Left": 0.10823947936296463, "Top": 0.43237680196762085}, "Polygon": [{"X": 0.10836157202720642, "Y": 0.43237680196762085}, {"X": 0.17308273911476135, "Y": 0.43292340636253357}, {"X": 0.1729610562324524, "Y": 0.44516992568969727}, {"X": 0.10823947936296463, "Y": 0.44462326169013977}]}, "Id": "3acfa525-083d-441b-bdea-eb528ef706a5", "Relationships": [{"Type": "CHILD", "Ids": ["c5186e8d-30bc-4301-9264-0a3aa3b2d67a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "AMOUNT", "Geometry": {"BoundingBox": {"Width": 0.08737591654062271, "Height": 0.012461502104997635, "Left": 0.22350087761878967, "Top": 0.43420320749282837}, "Polygon": [{"X": 0.2236170619726181, "Y": 0.43420320749282837}, {"X": 0.3108767867088318, "Y": 0.4349401891231537}, {"X": 0.31076112389564514, "Y": 0.4466647207736969}, {"X": 0.22350087761878967, "Y": 0.4459277093410492}]}, "Id": "50fb009a-f18b-40e5-8526-9006f09a8162", "Relationships": [{"Type": "CHILD", "Ids": ["a83022b1-ca59-4a98-b605-b9b74c830a3f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06549657881259918, "Height": 0.012967427261173725, "Left": 0.35954028367996216, "Top": 0.43573158979415894}, "Polygon": [{"X": 0.3596624433994293, "Y": 0.43573158979415894}, {"X": 0.42503687739372253, "Y": 0.43628373742103577}, {"X": 0.4249151051044464, "Y": 0.4486989974975586}, {"X": 0.35954028367996216, "Y": 0.44814684987068176}]}, "Id": "30f10c6a-58ee-4683-8264-e091b681a0b6", "Relationships": [{"Type": "CHILD", "Ids": ["91adda1d-07b9-4d7a-91fe-c0f675bc2847"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.047812674194574356, "Height": 0.012288112193346024, "Left": 0.572535514831543, "Top": 0.4565528333187103}, "Polygon": [{"X": 0.5726511478424072, "Y": 0.4565528333187103}, {"X": 0.6203481554985046, "Y": 0.4569557011127472}, {"X": 0.6202328205108643, "Y": 0.46884095668792725}, {"X": 0.572535514831543, "Y": 0.468438059091568}]}, "Id": "c74b6ffe-14bb-4ed4-8a29-342ecbb203da", "Relationships": [{"Type": "CHILD", "Ids": ["e706d58d-02c7-4203-a35b-b575a18a28a0"]}]}, {"BlockType": "LINE", "Confidence": 87.03682708740234, "Text": "+ $ 200.00 $", "Geometry": {"BoundingBox": {"Width": 0.14663276076316833, "Height": 0.03254038840532303, "Left": 0.6742575168609619, "Top": 0.4392107129096985}, "Polygon": [{"X": 0.6745604872703552, "Y": 0.4392107129096985}, {"X": 0.8208903074264526, "Y": 0.4404466152191162}, {"X": 0.8205897212028503, "Y": 0.4717510938644409}, {"X": 0.6742575168609619, "Y": 0.47051504254341125}]}, "Id": "6642a534-f733-4bbd-ab24-252f31899dc2", "Relationships": [{"Type": "CHILD", "Ids": ["9d5b7445-7270-427e-b0f6-f70ae70db846", "b89efedc-54d7-409d-8063-4d59b4877601", "c7127cdd-43c9-44b4-8c24-856b34042046"]}]}, {"BlockType": "LINE", "Confidence": 99.9657211303711, "Text": "(Rack checks will now be sent to the", "Geometry": {"BoundingBox": {"Width": 0.2912270128726959, "Height": 0.01638752967119217, "Left": 0.5717729926109314, "Top": 0.4735109806060791}, "Polygon": [{"X": 0.5719085335731506, "Y": 0.4735109806060791}, {"X": 0.8630000352859497, "Y": 0.4759698510169983}, {"X": 0.8628665804862976, "Y": 0.4898985028266907}, {"X": 0.5717729926109314, "Y": 0.48743945360183716}]}, "Id": "baae50d3-6692-43d3-97b8-80cf05471570", "Relationships": [{"Type": "CHILD", "Ids": ["69662071-2d26-4882-baf9-7e928935a626", "02631925-5166-403f-b175-50e03a5a12a1", "ccc69e47-886d-496f-b616-f58896ca65f8", "25bd2357-c1b6-45d2-bb01-7528bd048190", "4afea55a-1e0d-4a33-9b99-bb6fcafa7c2f", "51910822-06b2-4f77-aa6b-e4b3707c59e4", "7462d8cc-5d20-4f12-b2eb-c16e4e30f330", "2d098b4e-3984-4563-ab57-8797b0772b5f"]}]}, {"BlockType": "LINE", "Confidence": 99.73313903808594, "Text": "broker.)", "Geometry": {"BoundingBox": {"Width": 0.06250183284282684, "Height": 0.015037843957543373, "Left": 0.5825040936470032, "Top": 0.49061843752861023}, "Polygon": [{"X": 0.5826451778411865, "Y": 0.49061843752861023}, {"X": 0.6450058817863464, "Y": 0.4911452531814575}, {"X": 0.6448652744293213, "Y": 0.5056563019752502}, {"X": 0.5825040936470032, "Y": 0.5051294565200806}]}, "Id": "12692ed0-82b4-4dce-92eb-49e954e1e5d3", "Relationships": [{"Type": "CHILD", "Ids": ["dde1a1d4-cd29-41f1-9d9f-b34265d12ff8"]}]}, {"BlockType": "LINE", "Confidence": 79.7765884399414, "Text": "Net Cost $ 4854.00", "Geometry": {"BoundingBox": {"Width": 0.2093755155801773, "Height": 0.03271022066473961, "Left": 0.5720324516296387, "Top": 0.508317232131958}, "Polygon": [{"X": 0.5723335146903992, "Y": 0.508317232131958}, {"X": 0.7814079523086548, "Y": 0.5100835561752319}, {"X": 0.7811102271080017, "Y": 0.5410274267196655}, {"X": 0.5720324516296387, "Y": 0.5392608642578125}]}, "Id": "86783a21-61db-4d01-a630-df81c494b726", "Relationships": [{"Type": "CHILD", "Ids": ["f43f54b7-fd1f-4af3-a35b-aa51c1315f69", "148345ba-6ae5-466d-92dd-0aac94fc275d", "5d34c0fe-bcdb-4461-8f25-eca23a3365fc"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "Billable Shortages:", "Geometry": {"BoundingBox": {"Width": 0.2500888407230377, "Height": 0.025903411209583282, "Left": 0.10592713952064514, "Top": 0.5559420585632324}, "Polygon": [{"X": 0.1061643436551094, "Y": 0.5559420585632324}, {"X": 0.35601598024368286, "Y": 0.5580533146858215}, {"X": 0.35578182339668274, "Y": 0.5818454623222351}, {"X": 0.10592713952064514, "Y": 0.5797339081764221}]}, "Id": "41f63533-5d3f-45cc-bf3d-0fe244f6bf30", "Relationships": [{"Type": "CHILD", "Ids": ["29886fe1-b451-45ff-9b87-bc362693916f", "a2c4b2f7-6590-415c-9e1b-91c13613611b"]}]}, {"BlockType": "LINE", "Confidence": 92.40353393554688, "Text": "(equipment missing with insufficient documentation)", "Geometry": {"BoundingBox": {"Width": 0.28270769119262695, "Height": 0.013149256817996502, "Left": 0.10404670983552933, "Top": 0.5808007121086121}, "Polygon": [{"X": 0.10415400564670563, "Y": 0.5808007121086121}, {"X": 0.3867543935775757, "Y": 0.5831889510154724}, {"X": 0.38664865493774414, "Y": 0.5939499735832214}, {"X": 0.10404670983552933, "Y": 0.5915615558624268}]}, "Id": "ed6d04bc-f7b0-4523-a6a8-5ebad1a65b2e", "Relationships": [{"Type": "CHILD", "Ids": ["6582dd6c-4e4f-4c59-9679-ff4ac159e654", "dee77e89-206a-4f02-b749-99fa5d2fa6d4", "67152ebe-f92b-4a1a-a824-62957909261a", "348a2008-3cdd-4a45-83c1-4002eb5b25bb", "84e4a80c-d476-4b32-af11-a4c59b36bdda"]}]}, {"BlockType": "LINE", "Confidence": 95.265869140625, "Text": "Racks: ( ) X $250.00 = $", "Geometry": {"BoundingBox": {"Width": 0.20140041410923004, "Height": 0.016143424436450005, "Left": 0.10486248880624771, "Top": 0.604655921459198}, "Polygon": [{"X": 0.10500647872686386, "Y": 0.604655921459198}, {"X": 0.30626291036605835, "Y": 0.6063569188117981}, {"X": 0.3061203956604004, "Y": 0.6207993626594543}, {"X": 0.10486248880624771, "Y": 0.6190982460975647}]}, "Id": "a63493bb-b3ba-4c5b-bf7c-45a61c25b84d", "Relationships": [{"Type": "CHILD", "Ids": ["68999652-6e94-41c0-8c00-db69606271ff", "0ea2f1f3-e1b5-4bd2-9d7d-80aa484f53c3", "dc7a6d89-7d8c-4311-a675-55a66cf02a5e", "3623160d-018b-4097-9f3c-6ac6f3669772", "ccb03689-226e-410f-9c1f-5d083330a4bf", "bbdac656-74bc-453a-9b23-f126a8ca752d", "d869d15f-be8e-466d-abb3-49f7fa59651c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Deductions", "Geometry": {"BoundingBox": {"Width": 0.13783451914787292, "Height": 0.01354855578392744, "Left": 0.5884703397750854, "Top": 0.6110802888870239}, "Polygon": [{"X": 0.5885907411575317, "Y": 0.6110802888870239}, {"X": 0.726304829120636, "Y": 0.6122442483901978}, {"X": 0.7261853218078613, "Y": 0.6246289014816284}, {"X": 0.5884703397750854, "Y": 0.623464822769165}]}, "Id": "9185871f-4200-4733-b019-bc42ddf7110f", "Relationships": [{"Type": "CHILD", "Ids": ["c3e65ad0-f978-47e4-9429-1926d692ff89", "1d068852-3f52-4090-8914-8db1bfb3e3f6"]}]}, {"BlockType": "LINE", "Confidence": 99.83119201660156, "Text": "For Load:", "Geometry": {"BoundingBox": {"Width": 0.07777468860149384, "Height": 0.012831196188926697, "Left": 0.5899719595909119, "Top": 0.6285871863365173}, "Polygon": [{"X": 0.5900903344154358, "Y": 0.6285871863365173}, {"X": 0.6677466630935669, "Y": 0.6292435526847839}, {"X": 0.667628824710846, "Y": 0.6414183974266052}, {"X": 0.5899719595909119, "Y": 0.6407619118690491}]}, "Id": "603b29e4-d86d-4de9-a1e5-f196bf4abc77", "Relationships": [{"Type": "CHILD", "Ids": ["de4c6ff5-d8de-4b1d-a70d-36be8289ec1b", "aa33e8ef-964d-4ec7-a163-73cf6489ca2f"]}]}, {"BlockType": "LINE", "Confidence": 95.522216796875, "Text": "Load Locks: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2364782840013504, "Height": 0.01653158850967884, "Left": 0.10456527024507523, "Top": 0.639309823513031}, "Polygon": [{"X": 0.1047101765871048, "Y": 0.639309823513031}, {"X": 0.3410435616970062, "Y": 0.6413075923919678}, {"X": 0.3409004211425781, "Y": 0.6558414697647095}, {"X": 0.10456527024507523, "Y": 0.6538435220718384}]}, "Id": "a1f8a01c-37a6-45a6-8668-44d71b529568", "Relationships": [{"Type": "CHILD", "Ids": ["8f4e2dbf-8b11-4725-9051-ff06f7e94ecb", "664f54c6-c244-470f-b055-ae1942670270", "b17c10da-ec92-45f0-99a1-13aa1cfa99c8", "530ea461-91e0-4eb3-9b9b-0270ca814664", "ef42b157-6389-4bb5-8e0d-69419e65c1f2", "aa3013fb-aa1a-46e4-97ff-526e14f301c5", "94e01274-0eab-403f-8cc8-deb1c1e5c8c7", "497d2bbd-f7d8-49ed-a713-b37e1ef46ca1"]}]}, {"BlockType": "LINE", "Confidence": 99.64604187011719, "Text": "$", "Geometry": {"BoundingBox": {"Width": 0.00989188626408577, "Height": 0.012164317071437836, "Left": 0.5883252024650574, "Top": 0.6635552048683167}, "Polygon": [{"X": 0.5884426236152649, "Y": 0.6635552048683167}, {"X": 0.5982170701026917, "Y": 0.6636378169059753}, {"X": 0.5980997085571289, "Y": 0.6757194995880127}, {"X": 0.5883252024650574, "Y": 0.675636887550354}]}, "Id": "209c9749-891e-4124-9765-43dea775ea4b", "Relationships": [{"Type": "CHILD", "Ids": ["8796b9ce-c6a2-40f4-9db3-d21ddb13afef"]}]}, {"BlockType": "LINE", "Confidence": 95.42293548583984, "Text": "Pallet Jack: ( ) X $500.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23803147673606873, "Height": 0.01742284558713436, "Left": 0.1036008894443512, "Top": 0.6740297079086304}, "Polygon": [{"X": 0.10375455021858215, "Y": 0.6740297079086304}, {"X": 0.3416323661804199, "Y": 0.6760408282279968}, {"X": 0.3414805829524994, "Y": 0.6914525628089905}, {"X": 0.1036008894443512, "Y": 0.6894412636756897}]}, "Id": "7f9eb294-5f0c-4fca-9e36-7d915872e415", "Relationships": [{"Type": "CHILD", "Ids": ["96a6dc66-197f-4313-905f-363492d18fdf", "73de3879-c9df-401d-958a-d40d4434ad58", "30bd98bd-5ec0-40ee-b593-875040fe5e6e", "ee034438-1c08-463f-873c-75d7cf605bcf", "46d9a578-5427-4e41-9881-5214c64e730a", "4543ad53-7041-48bf-90bb-95f8ebe46e9c", "473ef599-88f7-4754-a903-c0777ef583bb", "b2457eaf-118b-47e9-96f6-7ae44b50a414"]}]}, {"BlockType": "LINE", "Confidence": 94.22455596923828, "Text": "Load Straps: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23765522241592407, "Height": 0.016998767852783203, "Left": 0.10327975451946259, "Top": 0.7085973620414734}, "Polygon": [{"X": 0.10342922061681747, "Y": 0.7085973620414734}, {"X": 0.34093496203422546, "Y": 0.7106056213378906}, {"X": 0.3407873511314392, "Y": 0.7255961298942566}, {"X": 0.10327975451946259, "Y": 0.7235877513885498}]}, "Id": "bb12771b-0901-44ae-8ca8-c55e67458e01", "Relationships": [{"Type": "CHILD", "Ids": ["e112fb35-cb9f-4b0e-9703-437730018223", "b903c6e4-4700-43c7-a674-8002b696f888", "7786443c-1958-49c3-9123-cb3a907b1856", "63fceacb-fe63-442f-b6b6-193c8f92258f", "58d1873f-948f-4aca-8ef9-394c10da202a", "869728ed-d2b2-4b19-860d-c67af8c5812a", "599f2373-cc4a-4a6b-925c-6c795cda64d5", "8f4d9756-b0ab-40f1-a6a5-1f9191076936"]}]}, {"BlockType": "LINE", "Confidence": 99.82864379882812, "Text": "Paperwork & other Fines $", "Geometry": {"BoundingBox": {"Width": 0.21454201638698578, "Height": 0.01686844415962696, "Left": 0.10152444988489151, "Top": 0.743242621421814}, "Polygon": [{"X": 0.10167457163333893, "Y": 0.743242621421814}, {"X": 0.3160664737224579, "Y": 0.7450557351112366}, {"X": 0.3159179985523224, "Y": 0.7601110935211182}, {"X": 0.10152444988489151, "Y": 0.758297860622406}]}, "Id": "7bdfd4ea-17f9-45e8-83dd-4b2419ede82d", "Relationships": [{"Type": "CHILD", "Ids": ["d40ce2b5-dc5f-445c-9ae6-eb7702f6e88d", "0d787c4b-0fea-4b0b-9b8c-095459385fb9", "d024a138-437e-42fc-9cfe-7f9ab3f7be14", "ed170926-93c9-4df3-82ed-e616b02e0588", "5e0ff1b0-fca7-47a5-9553-f8afc5f6cb80"]}]}, {"BlockType": "LINE", "Confidence": 99.02084350585938, "Text": "(see attached documentation)", "Geometry": {"BoundingBox": {"Width": 0.23224662244319916, "Height": 0.016425391659140587, "Left": 0.1010967493057251, "Top": 0.7609055638313293}, "Polygon": [{"X": 0.10124096274375916, "Y": 0.7609055638313293}, {"X": 0.33334335684776306, "Y": 0.7628685235977173}, {"X": 0.3332009017467499, "Y": 0.7773309350013733}, {"X": 0.1010967493057251, "Y": 0.7753678560256958}]}, "Id": "2f0277e7-eb08-4281-9503-7c21274677db", "Relationships": [{"Type": "CHILD", "Ids": ["77ee4335-882f-4a2e-b8ea-f4e805696b5e", "6ba9b897-7f79-4a91-bdac-8a2d543749d8", "7e1ad536-d5b5-4b1f-baf7-b2edbc3f1603"]}]}, {"BlockType": "LINE", "Confidence": 99.53672790527344, "Text": "Manager A<PERSON><PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.1542186141014099, "Height": 0.016113869845867157, "Left": 0.5317144393920898, "Top": 0.767128050327301}, "Polygon": [{"X": 0.5318588614463806, "Y": 0.767128050327301}, {"X": 0.6859330534934998, "Y": 0.7684311270713806}, {"X": 0.6857898235321045, "Y": 0.7832419276237488}, {"X": 0.5317144393920898, "Y": 0.7819387316703796}]}, "Id": "b3e6f777-96b1-405f-ac9b-140da10c8974", "Relationships": [{"Type": "CHILD", "Ids": ["1df55d78-bda3-4759-98f5-75d83d033d93", "5a373a68-7d4f-4a4b-9361-09e4c1cbcc7a"]}]}, {"BlockType": "LINE", "Confidence": 84.3504867553711, "Text": "my", "Geometry": {"BoundingBox": {"Width": 0.08269514888525009, "Height": 0.07104548066854477, "Left": 0.7548659443855286, "Top": 0.7374851107597351}, "Polygon": [{"X": 0.7555437684059143, "Y": 0.7374851107597351}, {"X": 0.8375611305236816, "Y": 0.7381786704063416}, {"X": 0.8368862867355347, "Y": 0.8085305690765381}, {"X": 0.7548659443855286, "Y": 0.8078367710113525}]}, "Id": "ba1bb116-0c8d-4964-9770-904234130548", "Relationships": [{"Type": "CHILD", "Ids": ["0fff1b61-3331-474b-a1c9-cedbc460fb53"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "Notes:", "Geometry": {"BoundingBox": {"Width": 0.05096597597002983, "Height": 0.01281474158167839, "Left": 0.09995508193969727, "Top": 0.7950825691223145}, "Polygon": [{"X": 0.10007858276367188, "Y": 0.7950825691223145}, {"X": 0.1509210616350174, "Y": 0.7955126166343689}, {"X": 0.15079788863658905, "Y": 0.8078973293304443}, {"X": 0.09995508193969727, "Y": 0.8074672222137451}]}, "Id": "f37011db-cbf2-4a8f-af65-45377b77f8a1", "Relationships": [{"Type": "CHILD", "Ids": ["bffa2240-09f2-4673-a6f5-6886a41baafc"]}]}, {"BlockType": "LINE", "Confidence": 97.32125854492188, "Text": "Any fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468", "Geometry": {"BoundingBox": {"Width": 0.7664570808410645, "Height": 0.02274540811777115, "Left": 0.09865261614322662, "Top": 0.8466292023658752}, "Polygon": [{"X": 0.09881478548049927, "Y": 0.8466292023658752}, {"X": 0.8651096820831299, "Y": 0.8531123995780945}, {"X": 0.8649539351463318, "Y": 0.8693746328353882}, {"X": 0.09865261614322662, "Y": 0.862890899181366}]}, "Id": "eb0f3fd0-ce81-4a9f-a68b-02169f4bf6e7", "Relationships": [{"Type": "CHILD", "Ids": ["02127f37-c30d-41c0-9ea9-7a278740f353", "b9e2a8b9-42dc-4a53-a831-5996f08545f2", "1ca19027-f0d9-4a65-ab29-329f855aaeba", "adca94e7-ccc8-4d7c-9149-a2f5c5f5ed67", "25c9d1e9-1ab2-49f7-bda6-b134749d7511", "de23f3f3-ea80-49e2-97c3-08563774b1cf", "9f89fa24-6081-49ae-b035-8366838e16bb", "5e404964-098a-4417-b5ea-865d728c4ad8", "a086aebe-58d7-450a-9f37-e8fe3b6f39dc", "3d7e015a-fc4c-4549-9dc3-e8a390c29f7c", "6ea9cca7-192e-43a3-9004-fa1e08016585", "b8dc19fa-8f80-4535-a5d6-f1fdeab61976", "fd469ee0-1b1c-4676-afde-191135c1e2eb", "961c2de2-ddc5-4057-83a5-7f3c7a490d59", "0ce5e2e0-1f10-4af6-a074-b3014dd78c24", "8b559cf6-25f5-461d-861a-75e12fbd327f", "0243d3cd-bff8-4330-9754-2dd7e16da081", "6307042e-c3b5-4ae0-b17a-4b9abdb9ea46", "d1412fde-1b52-47fc-972b-6d8fd7220d74", "eb98772a-3f59-49ca-a178-f99f69d59d0d"]}]}, {"BlockType": "LINE", "Confidence": 88.76853942871094, "Text": "R", "Geometry": {"BoundingBox": {"Width": 0.027656713500618935, "Height": 0.021608801558613777, "Left": 0.9385234713554382, "Top": 0.9465957283973694}, "Polygon": [{"X": 0.9387273788452148, "Y": 0.9465957283973694}, {"X": 0.9661802053451538, "Y": 0.9468280673027039}, {"X": 0.9659765958786011, "Y": 0.9682044982910156}, {"X": 0.9385234713554382, "Y": 0.9679721593856812}]}, "Id": "90bbdadc-9f89-4981-8e74-84d6735a12f1", "Relationships": [{"Type": "CHILD", "Ids": ["65c1bfd7-fe01-4cef-a8a9-21a3278c56db"]}]}, {"BlockType": "WORD", "Confidence": 44.808074951171875, "Text": "ised", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031261805444955826, "Height": 0.010969812050461769, "Left": 0.10575716197490692, "Top": 0.0461379736661911}, "Polygon": [{"X": 0.10586394369602203, "Y": 0.0461379736661911}, {"X": 0.13701896369457245, "Y": 0.04640067368745804}, {"X": 0.13691236078739166, "Y": 0.05710778385400772}, {"X": 0.10575716197490692, "Y": 0.05684506893157959}]}, "Id": "d659153d-fda6-4aaf-b3b7-b13363dbef99"}, {"BlockType": "WORD", "Confidence": 78.62544250488281, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04923126474022865, "Height": 0.010663959197700024, "Left": 0.13983242213726044, "Top": 0.04649730026721954}, "Polygon": [{"X": 0.1399344652891159, "Y": 0.04649730026721954}, {"X": 0.1890636831521988, "Y": 0.046911563724279404}, {"X": 0.1889619082212448, "Y": 0.05716126039624214}, {"X": 0.13983242213726044, "Y": 0.05674697831273079}]}, "Id": "45663b83-5e70-459c-88a5-3cbff0e6d5f0"}, {"BlockType": "WORD", "Confidence": 47.67478561401367, "Text": "20312", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04043268412351608, "Height": 0.01061754859983921, "Left": 0.10436731576919556, "Top": 0.059050269424915314}, "Polygon": [{"X": 0.10446982085704803, "Y": 0.059050269424915314}, {"X": 0.14480000734329224, "Y": 0.0593903549015522}, {"X": 0.14469771087169647, "Y": 0.06966781616210938}, {"X": 0.10436731576919556, "Y": 0.069327712059021}]}, "Id": "264fd5f0-a945-4d39-86da-3c7e9613b0c2"}, {"BlockType": "WORD", "Confidence": 97.56198120117188, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12352358549833298, "Height": 0.046337731182575226, "Left": 0.4382188618183136, "Top": 0.03474485129117966}, "Polygon": [{"X": 0.4386628568172455, "Y": 0.03474485129117966}, {"X": 0.5617424249649048, "Y": 0.03578260913491249}, {"X": 0.5613013505935669, "Y": 0.08108258247375488}, {"X": 0.4382188618183136, "Y": 0.08004459738731384}]}, "Id": "f665bf13-b1f7-48b5-b473-86eece6b242e"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "8787849", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.29426971077919006, "Height": 0.0491531565785408, "Left": 0.5878937244415283, "Top": 0.04168840870261192}, "Polygon": [{"X": 0.5883475542068481, "Y": 0.04168840870261192}, {"X": 0.882163405418396, "Y": 0.04416580870747566}, {"X": 0.8817166090011597, "Y": 0.09084156900644302}, {"X": 0.5878937244415283, "Y": 0.08836361765861511}]}, "Id": "c7789e52-eb83-44db-9355-c429a75b0ebf"}, {"BlockType": "WORD", "Confidence": 9.859843254089355, "Text": "rermatic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09043282270431519, "Height": 0.017054790630936623, "Left": 0.4480265974998474, "Top": 0.08612104505300522}, "Polygon": [{"X": 0.4481862187385559, "Y": 0.08612104505300522}, {"X": 0.5384594202041626, "Y": 0.08688236027956009}, {"X": 0.5383005738258362, "Y": 0.10317584127187729}, {"X": 0.4480265974998474, "Y": 0.10241446644067764}]}, "Id": "2af97122-fd76-4184-ba45-1918496b5ba3"}, {"BlockType": "WORD", "Confidence": 15.084552764892578, "Text": "AMAMA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2227204442024231, "Height": 0.03463365137577057, "Left": 0.4022594392299652, "Top": 0.12737968564033508}, "Polygon": [{"X": 0.4025810956954956, "Y": 0.12737968564033508}, {"X": 0.6249799132347107, "Y": 0.12925560772418976}, {"X": 0.6246619820594788, "Y": 0.16201333701610565}, {"X": 0.4022594392299652, "Y": 0.1601371467113495}]}, "Id": "f1d89985-b92a-48ce-84fd-a0d73dc7f99c"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "SAWYER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18494632840156555, "Height": 0.02501562424004078, "Left": 0.2967032790184021, "Top": 0.17423385381698608}, "Polygon": [{"X": 0.29693490266799927, "Y": 0.17423385381698608}, {"X": 0.48164960741996765, "Y": 0.17579221725463867}, {"X": 0.48142024874687195, "Y": 0.1992494761943817}, {"X": 0.2967032790184021, "Y": 0.1976909339427948}]}, "Id": "b6a6726d-09cb-47fc-a680-29f805566f3c"}, {"BlockType": "WORD", "Confidence": 96.05548095703125, "Text": "NURSERY.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22050274908542633, "Height": 0.02516096457839012, "Left": 0.5067443251609802, "Top": 0.17737939953804016}, "Polygon": [{"X": 0.5069718956947327, "Y": 0.17737939953804016}, {"X": 0.7272470593452454, "Y": 0.17923778295516968}, {"X": 0.7270221710205078, "Y": 0.20254036784172058}, {"X": 0.5067443251609802, "Y": 0.20068177580833435}]}, "Id": "251f6629-77db-4953-8d44-f38a73d404d2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truckload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13608801364898682, "Height": 0.020663820207118988, "Left": 0.34745877981185913, "Top": 0.2073768526315689}, "Polygon": [{"X": 0.3476509749889374, "Y": 0.2073768526315689}, {"X": 0.48354679346084595, "Y": 0.20852351188659668}, {"X": 0.48335596919059753, "Y": 0.2280406802892685}, {"X": 0.34745877981185913, "Y": 0.22689391672611237}]}, "Id": "3a3dbf41-6b2b-4a51-a100-9f3aad136d2d"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "Check-in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12025604397058487, "Height": 0.020623862743377686, "Left": 0.4925466477870941, "Top": 0.20970825850963593}, "Polygon": [{"X": 0.492738276720047, "Y": 0.20970825850963593}, {"X": 0.6128026843070984, "Y": 0.21072132885456085}, {"X": 0.612612247467041, "Y": 0.2303321212530136}, {"X": 0.4925466477870941, "Y": 0.22931894659996033}]}, "Id": "96074713-ee59-4614-9b1e-af7a821679c8"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Form", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06964471936225891, "Height": 0.019365625455975533, "Left": 0.6211636066436768, "Top": 0.2123054563999176}, "Polygon": [{"X": 0.6213458776473999, "Y": 0.2123054563999176}, {"X": 0.6908083558082581, "Y": 0.2128915786743164}, {"X": 0.6906267404556274, "Y": 0.23167107999324799}, {"X": 0.6211636066436768, "Y": 0.2310849130153656}]}, "Id": "f6fa651c-340b-470c-8a4e-73b45fa2b8ef"}, {"BlockType": "WORD", "Confidence": 99.25701904296875, "Text": "Clear-to-Pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16858001053333282, "Height": 0.025687556713819504, "Left": 0.3744858503341675, "Top": 0.2366248071193695}, "Polygon": [{"X": 0.37472444772720337, "Y": 0.2366248071193695}, {"X": 0.5430658459663391, "Y": 0.23804539442062378}, {"X": 0.5428293347358704, "Y": 0.2623123526573181}, {"X": 0.3744858503341675, "Y": 0.2608915865421295}]}, "Id": "fab1f256-5461-4d5c-a940-0dcc1bd10869"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Voucher", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11406207084655762, "Height": 0.020176580175757408, "Left": 0.5515662431716919, "Top": 0.23967085778713226}, "Polygon": [{"X": 0.5517534613609314, "Y": 0.23967085778713226}, {"X": 0.6656283140182495, "Y": 0.2406318336725235}, {"X": 0.6654422283172607, "Y": 0.2598474323749542}, {"X": 0.5515662431716919, "Y": 0.2588863670825958}]}, "Id": "d8097052-db15-42e9-8502-c3e1a7a057dd"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03959387540817261, "Height": 0.012682847678661346, "Left": 0.11143584549427032, "Top": 0.2760337293148041}, "Polygon": [{"X": 0.1115589588880539, "Y": 0.2760337293148041}, {"X": 0.15102972090244293, "Y": 0.27636685967445374}, {"X": 0.15090686082839966, "Y": 0.288716584444046}, {"X": 0.11143584549427032, "Y": 0.288383424282074}]}, "Id": "e7cb34f4-4db1-4500-8ae4-bf277626cf35"}, {"BlockType": "WORD", "Confidence": 98.54332733154297, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010674598626792431, "Height": 0.011867338791489601, "Left": 0.15583984553813934, "Top": 0.2768767178058624}, "Polygon": [{"X": 0.15595699846744537, "Y": 0.2768767178058624}, {"X": 0.16651445627212524, "Y": 0.27696582674980164}, {"X": 0.16639737784862518, "Y": 0.2887440621852875}, {"X": 0.15583984553813934, "Y": 0.28865495324134827}]}, "Id": "9e23d76b-cadc-438f-9440-d0a61221680e"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "20512", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09362539649009705, "Height": 0.019584888592362404, "Left": 0.185643270611763, "Top": 0.2689233422279358}, "Polygon": [{"X": 0.18582992255687714, "Y": 0.2689233422279358}, {"X": 0.27926868200302124, "Y": 0.269711971282959}, {"X": 0.27908292412757874, "Y": 0.28850823640823364}, {"X": 0.185643270611763, "Y": 0.2877195477485657}]}, "Id": "ee57ddbe-af26-40db-8a9b-a6198f538851"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "AL", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04235772788524628, "Height": 0.016717085614800453, "Left": 0.3127909004688263, "Top": 0.2702547609806061}, "Polygon": [{"X": 0.31295228004455566, "Y": 0.2702547609806061}, {"X": 0.3551486134529114, "Y": 0.27061089873313904}, {"X": 0.35498756170272827, "Y": 0.2869718670845032}, {"X": 0.3127909004688263, "Y": 0.2866156995296478}]}, "Id": "0e0e535e-4561-439f-9d81-06f6a9dba9d8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Broker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05525553598999977, "Height": 0.012510714121162891, "Left": 0.5778552293777466, "Top": 0.283710777759552}, "Polygon": [{"X": 0.577972412109375, "Y": 0.283710777759552}, {"X": 0.633110761642456, "Y": 0.28417617082595825}, {"X": 0.6329939365386963, "Y": 0.2962214946746826}, {"X": 0.5778552293777466, "Y": 0.295756071805954}]}, "Id": "2a50dfdd-b9f9-4098-8601-78aab7830240"}, {"BlockType": "WORD", "Confidence": 98.39784240722656, "Text": "PE", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.049515288323163986, "Height": 0.0381166972219944, "Left": 0.6771236658096313, "Top": 0.26166537404060364}, "Polygon": [{"X": 0.6774885058403015, "Y": 0.26166537404060364}, {"X": 0.7266389727592468, "Y": 0.26208019256591797}, {"X": 0.7262750864028931, "Y": 0.2997820973396301}, {"X": 0.6771236658096313, "Y": 0.29936718940734863}]}, "Id": "04ee649d-0da3-4f17-bb35-10ab9bdae0c4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03694378212094307, "Height": 0.012404896318912506, "Left": 0.11073420941829681, "Top": 0.3108941614627838}, "Polygon": [{"X": 0.11085477471351624, "Y": 0.3108941614627838}, {"X": 0.14767798781394958, "Y": 0.3112049996852875}, {"X": 0.14755766093730927, "Y": 0.3232990503311157}, {"X": 0.11073420941829681, "Y": 0.3229881823062897}]}, "Id": "06f9c79f-ca11-4262-a76e-69acd1d4ca01"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Processed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07816613465547562, "Height": 0.012986875139176846, "Left": 0.15278933942317963, "Top": 0.3114513158798218}, "Polygon": [{"X": 0.152911975979805, "Y": 0.3114513158798218}, {"X": 0.23095548152923584, "Y": 0.31211012601852417}, {"X": 0.23083335161209106, "Y": 0.324438214302063}, {"X": 0.15278933942317963, "Y": 0.3237793743610382}]}, "Id": "756ba2b8-9cfd-465f-aee3-602a6031172b"}, {"BlockType": "WORD", "Confidence": 92.79595947265625, "Text": "4/24/24", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10495380312204361, "Height": 0.025156889110803604, "Left": 0.24711325764656067, "Top": 0.30235809087753296}, "Polygon": [{"X": 0.24735352396965027, "Y": 0.30235809087753296}, {"X": 0.3520670533180237, "Y": 0.3032419979572296}, {"X": 0.35182809829711914, "Y": 0.32751497626304626}, {"X": 0.24711325764656067, "Y": 0.32663097977638245}]}, "Id": "93e40ebe-d7fd-4cb4-bd4a-04ea3cb71c9d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03947858884930611, "Height": 0.012178152799606323, "Left": 0.576332151889801, "Top": 0.3354485332965851}, "Polygon": [{"X": 0.5764474272727966, "Y": 0.3354485332965851}, {"X": 0.615810751914978, "Y": 0.3357808589935303}, {"X": 0.6156957745552063, "Y": 0.3476266860961914}, {"X": 0.576332151889801, "Y": 0.3472943603992462}]}, "Id": "d16ba020-267b-4553-8cd7-1789b5b3beca"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03626213222742081, "Height": 0.012029241770505905, "Left": 0.6213395595550537, "Top": 0.33627596497535706}, "Polygon": [{"X": 0.6214533448219299, "Y": 0.33627596497535706}, {"X": 0.6576016545295715, "Y": 0.33658114075660706}, {"X": 0.6574881076812744, "Y": 0.34830519556999207}, {"X": 0.6213395595550537, "Y": 0.34800001978874207}]}, "Id": "3f8c23a8-3848-4bff-9ad2-e8b5320580a5"}, {"BlockType": "WORD", "Confidence": 65.74517822265625, "Text": "$ 4656.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13964271545410156, "Height": 0.035111743956804276, "Left": 0.6863459348678589, "Top": 0.31671667098999023}, "Polygon": [{"X": 0.686674177646637, "Y": 0.31671667098999023}, {"X": 0.8259887099266052, "Y": 0.3178927004337311}, {"X": 0.8256629109382629, "Y": 0.351828396320343}, {"X": 0.6863459348678589, "Y": 0.35065218806266785}]}, "Id": "d96209db-f870-4b0c-997d-3574ae26507c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04830773547291756, "Height": 0.012604407966136932, "Left": 0.11452226340770721, "Top": 0.3455684781074524}, "Polygon": [{"X": 0.11464383453130722, "Y": 0.3455684781074524}, {"X": 0.16282999515533447, "Y": 0.3459753096103668}, {"X": 0.16270872950553894, "Y": 0.3581728935241699}, {"X": 0.11452226340770721, "Y": 0.3577660322189331}]}, "Id": "cae1b69e-6d82-49c5-87b8-877206382581"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Returned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07205530256032944, "Height": 0.012749112211167812, "Left": 0.16786496341228485, "Top": 0.3464222550392151}, "Polygon": [{"X": 0.16798564791679382, "Y": 0.3464222550392151}, {"X": 0.23992027342319489, "Y": 0.34702959656715393}, {"X": 0.23980005085468292, "Y": 0.35917139053344727}, {"X": 0.16786496341228485, "Y": 0.35856401920318604}]}, "Id": "280c98a3-a094-4942-843f-bb613c60ec57"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "50", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.031743600964546204, "Height": 0.020916307345032692, "Left": 0.2871854305267334, "Top": 0.3405229449272156}, "Polygon": [{"X": 0.2873893976211548, "Y": 0.3405229449272156}, {"X": 0.3189290463924408, "Y": 0.34078919887542725}, {"X": 0.3187254071235657, "Y": 0.36143922805786133}, {"X": 0.2871854305267334, "Y": 0.36117294430732727}]}, "Id": "8a30ac81-812b-424b-9926-6a78dd7aa715"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Surcharge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08090711385011673, "Height": 0.015817610546946526, "Left": 0.5697786808013916, "Top": 0.37015679478645325}, "Polygon": [{"X": 0.5699259638786316, "Y": 0.37015679478645325}, {"X": 0.6506857872009277, "Y": 0.3708387017250061}, {"X": 0.6505391001701355, "Y": 0.3859744071960449}, {"X": 0.5697786808013916, "Y": 0.3852924704551697}]}, "Id": "7d8d6d6b-e3f8-427d-a9a9-63dc5cf53802"}, {"BlockType": "WORD", "Confidence": 98.53216552734375, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011310290545225143, "Height": 0.008834979496896267, "Left": 0.6595655083656311, "Top": 0.37287694215774536}, "Polygon": [{"X": 0.6596502065658569, "Y": 0.37287694215774536}, {"X": 0.6708758473396301, "Y": 0.3729717433452606}, {"X": 0.6707912087440491, "Y": 0.3817119300365448}, {"X": 0.6595655083656311, "Y": 0.38161712884902954}]}, "Id": "51b39baa-071f-4b1d-8275-6e54d0c5de39"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009443068876862526, "Height": 0.012892809696495533, "Left": 0.680570125579834, "Top": 0.3713472783565521}, "Polygon": [{"X": 0.6806941032409668, "Y": 0.3713472783565521}, {"X": 0.6900131702423096, "Y": 0.371425986289978}, {"X": 0.6898892521858215, "Y": 0.3842400908470154}, {"X": 0.680570125579834, "Y": 0.38416141271591187}]}, "Id": "9d8857b7-693b-4600-8acb-a1d89a12b995"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "50", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.039894066751003265, "Height": 0.021256636828184128, "Left": 0.11973623931407928, "Top": 0.40705645084381104}, "Polygon": [{"X": 0.11994469910860062, "Y": 0.40705645084381104}, {"X": 0.15963031351566315, "Y": 0.40739157795906067}, {"X": 0.15942227840423584, "Y": 0.42831307649612427}, {"X": 0.11973623931407928, "Y": 0.42797791957855225}]}, "Id": "0c1b24a5-b16c-4aeb-b1dc-25e59486dba6"}, {"BlockType": "WORD", "Confidence": 70.73409271240234, "Text": "XS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02883724682033062, "Height": 0.0130018824711442, "Left": 0.19235488772392273, "Top": 0.4158196449279785}, "Polygon": [{"X": 0.19248154759407043, "Y": 0.4158196449279785}, {"X": 0.2211921364068985, "Y": 0.4160621166229248}, {"X": 0.2210656702518463, "Y": 0.42882153391838074}, {"X": 0.19235488772392273, "Y": 0.42857906222343445}]}, "Id": "fd076ade-2557-4039-bc95-a3f3ee6d3194"}, {"BlockType": "WORD", "Confidence": 93.72469329833984, "Text": "400", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06531798839569092, "Height": 0.030666891485452652, "Left": 0.23187479376792908, "Top": 0.39946502447128296}, "Polygon": [{"X": 0.2321731448173523, "Y": 0.39946502447128296}, {"X": 0.29719278216362, "Y": 0.40001407265663147}, {"X": 0.29689544439315796, "Y": 0.4301319122314453}, {"X": 0.23187479376792908, "Y": 0.42958277463912964}]}, "Id": "a9d45455-61c4-4ab6-b0d9-c78396ff067f"}, {"BlockType": "WORD", "Confidence": 95.30033874511719, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011597763746976852, "Height": 0.004955346696078777, "Left": 0.3354306221008301, "Top": 0.42163753509521484}, "Polygon": [{"X": 0.3354784846305847, "Y": 0.42163753509521484}, {"X": 0.3470284044742584, "Y": 0.42173507809638977}, {"X": 0.34698057174682617, "Y": 0.4265928864479065}, {"X": 0.3354306221008301, "Y": 0.42649534344673157}]}, "Id": "8712aad7-d888-402e-adc1-68cdb7ca490a"}, {"BlockType": "WORD", "Confidence": 60.569793701171875, "Text": "$ 200.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12999685108661652, "Height": 0.026528028771281242, "Left": 0.34813177585601807, "Top": 0.4058879613876343}, "Polygon": [{"X": 0.3483821749687195, "Y": 0.4058879613876343}, {"X": 0.4781286120414734, "Y": 0.4069836437702179}, {"X": 0.47787991166114807, "Y": 0.43241599202156067}, {"X": 0.34813177585601807, "Y": 0.4313201904296875}]}, "Id": "371a712d-0fdd-4696-96aa-076bbe5ab94f"}, {"BlockType": "WORD", "Confidence": 83.273681640625, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009718945249915123, "Height": 0.012128965929150581, "Left": 0.35140717029571533, "Top": 0.418287068605423}, "Polygon": [{"X": 0.35152578353881836, "Y": 0.418287068605423}, {"X": 0.3611261248588562, "Y": 0.4183681309223175}, {"X": 0.36100757122039795, "Y": 0.4304160475730896}, {"X": 0.35140717029571533, "Y": 0.4303349554538727}]}, "Id": "287e7f3f-7567-4327-8011-b916c21ba804"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09014411270618439, "Height": 0.013494400307536125, "Left": 0.5695958733558655, "Top": 0.40438494086265564}, "Polygon": [{"X": 0.5697198510169983, "Y": 0.40438494086265564}, {"X": 0.6597400307655334, "Y": 0.40514513850212097}, {"X": 0.6596167087554932, "Y": 0.4178793430328369}, {"X": 0.5695958733558655, "Y": 0.4171191155910492}]}, "Id": "85f95671-9c76-4401-b56f-e9e8df64b54a"}, {"BlockType": "WORD", "Confidence": 89.5007553100586, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008627125062048435, "Height": 0.005947134457528591, "Left": 0.667620062828064, "Top": 0.4105254113674164}, "Polygon": [{"X": 0.6676769256591797, "Y": 0.4105254113674164}, {"X": 0.6762471795082092, "Y": 0.4105978012084961}, {"X": 0.6761903762817383, "Y": 0.41647255420684814}, {"X": 0.667620062828064, "Y": 0.41640016436576843}]}, "Id": "72b2273c-5481-4a93-bdc0-1eb5a3525813"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009697353467345238, "Height": 0.012710858136415482, "Left": 0.6851049065589905, "Top": 0.40617692470550537}, "Polygon": [{"X": 0.6852270364761353, "Y": 0.40617692470550537}, {"X": 0.6948022246360779, "Y": 0.4062577784061432}, {"X": 0.6946801543235779, "Y": 0.41888776421546936}, {"X": 0.6851049065589905, "Y": 0.41880691051483154}]}, "Id": "be11e52a-a70f-483f-98ff-cf9082a4dde1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RACKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06484325975179672, "Height": 0.012793132103979588, "Left": 0.10823947936296463, "Top": 0.43237680196762085}, "Polygon": [{"X": 0.10836157202720642, "Y": 0.43237680196762085}, {"X": 0.17308273911476135, "Y": 0.43292340636253357}, {"X": 0.1729610562324524, "Y": 0.44516992568969727}, {"X": 0.10823947936296463, "Y": 0.44462326169013977}]}, "Id": "c5186e8d-30bc-4301-9264-0a3aa3b2d67a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AMOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08737591654062271, "Height": 0.012461502104997635, "Left": 0.22350087761878967, "Top": 0.43420320749282837}, "Polygon": [{"X": 0.2236170619726181, "Y": 0.43420320749282837}, {"X": 0.3108767867088318, "Y": 0.4349401891231537}, {"X": 0.31076112389564514, "Y": 0.4466647207736969}, {"X": 0.22350087761878967, "Y": 0.4459277093410492}]}, "Id": "a83022b1-ca59-4a98-b605-b9b74c830a3f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06549657881259918, "Height": 0.012967427261173725, "Left": 0.35954028367996216, "Top": 0.43573158979415894}, "Polygon": [{"X": 0.3596624433994293, "Y": 0.43573158979415894}, {"X": 0.42503687739372253, "Y": 0.43628373742103577}, {"X": 0.4249151051044464, "Y": 0.4486989974975586}, {"X": 0.35954028367996216, "Y": 0.44814684987068176}]}, "Id": "91adda1d-07b9-4d7a-91fe-c0f675bc2847"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047812674194574356, "Height": 0.012288112193346024, "Left": 0.572535514831543, "Top": 0.4565528333187103}, "Polygon": [{"X": 0.5726511478424072, "Y": 0.4565528333187103}, {"X": 0.6203481554985046, "Y": 0.4569557011127472}, {"X": 0.6202328205108643, "Y": 0.46884095668792725}, {"X": 0.572535514831543, "Y": 0.468438059091568}]}, "Id": "e706d58d-02c7-4203-a35b-b575a18a28a0"}, {"BlockType": "WORD", "Confidence": 93.98377227783203, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010897397063672543, "Height": 0.008511217311024666, "Left": 0.6742812395095825, "Top": 0.4596458077430725}, "Polygon": [{"X": 0.6743627190589905, "Y": 0.4596458077430725}, {"X": 0.6851786375045776, "Y": 0.4597371518611908}, {"X": 0.6850971579551697, "Y": 0.468157023191452}, {"X": 0.6742812395095825, "Y": 0.46806564927101135}]}, "Id": "9d5b7445-7270-427e-b0f6-f70ae70db846"}, {"BlockType": "WORD", "Confidence": 76.78571319580078, "Text": "$ 200.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14153391122817993, "Height": 0.03249732777476311, "Left": 0.6793563961982727, "Top": 0.4392537772655487}, "Polygon": [{"X": 0.6796592473983765, "Y": 0.4392537772655487}, {"X": 0.8208903074264526, "Y": 0.4404466152191162}, {"X": 0.8205897212028503, "Y": 0.4717510938644409}, {"X": 0.6793563961982727, "Y": 0.4705581068992615}]}, "Id": "b89efedc-54d7-409d-8063-4d59b4877601"}, {"BlockType": "WORD", "Confidence": 90.34100341796875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009888794273138046, "Height": 0.01297382265329361, "Left": 0.6902868747711182, "Top": 0.45775043964385986}, "Polygon": [{"X": 0.6904115080833435, "Y": 0.45775043964385986}, {"X": 0.7001757025718689, "Y": 0.4578329026699066}, {"X": 0.7000510692596436, "Y": 0.4707242548465729}, {"X": 0.6902868747711182, "Y": 0.47064176201820374}]}, "Id": "c7127cdd-43c9-44b4-8c24-856b34042046"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "(<PERSON>ck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04667580500245094, "Height": 0.014321642927825451, "Left": 0.5717729926109314, "Top": 0.4735109806060791}, "Polygon": [{"X": 0.5719085335731506, "Y": 0.4735109806060791}, {"X": 0.618448793888092, "Y": 0.4739041030406952}, {"X": 0.6183136105537415, "Y": 0.48783260583877563}, {"X": 0.5717729926109314, "Y": 0.48743945360183716}]}, "Id": "69662071-2d26-4882-baf9-7e928935a626"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05373091623187065, "Height": 0.01251245941966772, "Left": 0.6236979365348816, "Top": 0.4742681682109833}, "Polygon": [{"X": 0.6238149404525757, "Y": 0.4742681682109833}, {"X": 0.6774288415908813, "Y": 0.4747210741043091}, {"X": 0.6773121356964111, "Y": 0.48678064346313477}, {"X": 0.6236979365348816, "Y": 0.48632773756980896}]}, "Id": "02631925-5166-403f-b175-50e03a5a12a1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0304441899061203, "Height": 0.01218500267714262, "Left": 0.6825226545333862, "Top": 0.4752015769481659}, "Polygon": [{"X": 0.6826380491256714, "Y": 0.4752015769481659}, {"X": 0.7129668593406677, "Y": 0.4754577875137329}, {"X": 0.7128516435623169, "Y": 0.4873865842819214}, {"X": 0.6825226545333862, "Y": 0.48713037371635437}]}, "Id": "ccc69e47-886d-496f-b616-f58896ca65f8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "now", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03435515612363815, "Height": 0.009064777754247189, "Left": 0.7181994318962097, "Top": 0.4788331389427185}, "Polygon": [{"X": 0.7182841300964355, "Y": 0.4788331389427185}, {"X": 0.7525545954704285, "Y": 0.4791226089000702}, {"X": 0.7524700164794922, "Y": 0.4878979027271271}, {"X": 0.7181994318962097, "Y": 0.487608402967453}]}, "Id": "25bd2357-c1b6-45d2-bb01-7528bd048190"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019356178119778633, "Height": 0.012090357020497322, "Left": 0.7571365833282471, "Top": 0.4758404493331909}, "Polygon": [{"X": 0.757251501083374, "Y": 0.4758404493331909}, {"X": 0.7764927744865417, "Y": 0.4760029911994934}, {"X": 0.7763779759407043, "Y": 0.4879308044910431}, {"X": 0.7571365833282471, "Y": 0.4877682626247406}]}, "Id": "4afea55a-1e0d-4a33-9b99-bb6fcafa7c2f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03199541196227074, "Height": 0.010060843080282211, "Left": 0.7811588048934937, "Top": 0.47856977581977844}, "Polygon": [{"X": 0.7812530398368835, "Y": 0.47856977581977844}, {"X": 0.8131542205810547, "Y": 0.47883927822113037}, {"X": 0.8130601644515991, "Y": 0.48863062262535095}, {"X": 0.7811588048934937, "Y": 0.4883611500263214}]}, "Id": "51910822-06b2-4f77-aa6b-e4b3707c59e4"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01521226391196251, "Height": 0.010155361145734787, "Left": 0.8179539442062378, "Top": 0.4788110554218292}, "Polygon": [{"X": 0.81805020570755, "Y": 0.4788110554218292}, {"X": 0.8331661820411682, "Y": 0.4789387285709381}, {"X": 0.8330699801445007, "Y": 0.4889664053916931}, {"X": 0.8179539442062378, "Y": 0.48883873224258423}]}, "Id": "7462d8cc-5d20-4f12-b2eb-c16e4e30f330"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024819791316986084, "Height": 0.012420302256941795, "Left": 0.8381670713424683, "Top": 0.4771336615085602}, "Polygon": [{"X": 0.8382841944694519, "Y": 0.4771336615085602}, {"X": 0.8629868626594543, "Y": 0.4773423373699188}, {"X": 0.8628698587417603, "Y": 0.48955395817756653}, {"X": 0.8381670713424683, "Y": 0.4893452823162079}]}, "Id": "2d098b4e-3984-4563-ab57-8797b0772b5f"}, {"BlockType": "WORD", "Confidence": 99.73313903808594, "Text": "broker.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06250183284282684, "Height": 0.015037843957543373, "Left": 0.5825040936470032, "Top": 0.49061843752861023}, "Polygon": [{"X": 0.5826451778411865, "Y": 0.49061843752861023}, {"X": 0.6450058817863464, "Y": 0.4911452531814575}, {"X": 0.6448652744293213, "Y": 0.5056563019752502}, {"X": 0.5825040936470032, "Y": 0.5051294565200806}]}, "Id": "dde1a1d4-cd29-41f1-9d9f-b34265d12ff8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027950389310717583, "Height": 0.01207671593874693, "Left": 0.5720527768135071, "Top": 0.5253317356109619}, "Polygon": [{"X": 0.5721679925918579, "Y": 0.5253317356109619}, {"X": 0.600003182888031, "Y": 0.5255669355392456}, {"X": 0.5998881459236145, "Y": 0.5374084711074829}, {"X": 0.5720527768135071, "Y": 0.5371732711791992}]}, "Id": "f43f54b7-fd1f-4af3-a35b-aa51c1315f69"}, {"BlockType": "WORD", "Confidence": 99.71600341796875, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03617120534181595, "Height": 0.011157971806824207, "Left": 0.6053768396377563, "Top": 0.5259755253791809}, "Polygon": [{"X": 0.605482280254364, "Y": 0.5259755253791809}, {"X": 0.6415480375289917, "Y": 0.5262802243232727}, {"X": 0.6414428353309631, "Y": 0.537133514881134}, {"X": 0.6053768396377563, "Y": 0.5368287563323975}]}, "Id": "148345ba-6ae5-466d-92dd-0aac94fc275d"}, {"BlockType": "WORD", "Confidence": 39.68291473388672, "Text": "$ 4854.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13661383092403412, "Height": 0.03209551423788071, "Left": 0.6447941660881042, "Top": 0.5089319348335266}, "Polygon": [{"X": 0.645094096660614, "Y": 0.5089319348335266}, {"X": 0.7814079523086548, "Y": 0.5100835561752319}, {"X": 0.7811102271080017, "Y": 0.5410274267196655}, {"X": 0.6447941660881042, "Y": 0.5398756265640259}]}, "Id": "5d34c0fe-bcdb-4461-8f25-eca23a3365fc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10355740040540695, "Height": 0.01955421082675457, "Left": 0.1059780940413475, "Top": 0.5559420585632324}, "Polygon": [{"X": 0.1061643436551094, "Y": 0.5559420585632324}, {"X": 0.20953549444675446, "Y": 0.5568155646324158}, {"X": 0.20935024321079254, "Y": 0.575496256351471}, {"X": 0.1059780940413475, "Y": 0.5746226906776428}]}, "Id": "29886fe1-b451-45ff-9b87-bc362693916f"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Shortages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13708415627479553, "Height": 0.024862993508577347, "Left": 0.21893097460269928, "Top": 0.5569824576377869}, "Polygon": [{"X": 0.21916595101356506, "Y": 0.5569824576377869}, {"X": 0.356015145778656, "Y": 0.5581388473510742}, {"X": 0.35578182339668274, "Y": 0.5818454623222351}, {"X": 0.21893097460269928, "Y": 0.5806888937950134}]}, "Id": "a2c4b2f7-6590-415c-9e1b-91c13613611b"}, {"BlockType": "WORD", "Confidence": 85.46177673339844, "Text": "(equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06154025346040726, "Height": 0.011230607517063618, "Left": 0.10404670983552933, "Top": 0.5808501839637756}, "Polygon": [{"X": 0.10415351390838623, "Y": 0.5808501839637756}, {"X": 0.16558696329593658, "Y": 0.5813693404197693}, {"X": 0.16548049449920654, "Y": 0.5920807719230652}, {"X": 0.10404670983552933, "Y": 0.5915615558624268}]}, "Id": "6582dd6c-4e4f-4c59-9679-ff4ac159e654"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "missing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04149790480732918, "Height": 0.010733485221862793, "Left": 0.16838079690933228, "Top": 0.5816287994384766}, "Polygon": [{"X": 0.16848398745059967, "Y": 0.5816287994384766}, {"X": 0.20987869799137115, "Y": 0.5819786190986633}, {"X": 0.20977573096752167, "Y": 0.5923622846603394}, {"X": 0.16838079690933228, "Y": 0.5920124053955078}]}, "Id": "dee77e89-206a-4f02-b749-99fa5d2fa6d4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02425273507833481, "Height": 0.008899704553186893, "Left": 0.21272872388362885, "Top": 0.5817189812660217}, "Polygon": [{"X": 0.21281494200229645, "Y": 0.5817189812660217}, {"X": 0.23698146641254425, "Y": 0.581923246383667}, {"X": 0.236895352602005, "Y": 0.5906187295913696}, {"X": 0.21272872388362885, "Y": 0.5904144644737244}]}, "Id": "67152ebe-f92b-4a1a-a824-62957909261a"}, {"BlockType": "WORD", "Confidence": 91.59040832519531, "Text": "insufficient", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06047479435801506, "Height": 0.009547614492475986, "Left": 0.23987561464309692, "Top": 0.582025945186615}, "Polygon": [{"X": 0.23996508121490479, "Y": 0.582025945186615}, {"X": 0.3003503978252411, "Y": 0.5825362801551819}, {"X": 0.3002611994743347, "Y": 0.5915735960006714}, {"X": 0.23987561464309692, "Y": 0.5910632610321045}]}, "Id": "348a2008-3cdd-4a45-83c1-4002eb5b25bb"}, {"BlockType": "WORD", "Confidence": 85.04364776611328, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08374757319688797, "Height": 0.009992646053433418, "Left": 0.30299919843673706, "Top": 0.5832592844963074}, "Polygon": [{"X": 0.30309081077575684, "Y": 0.5832592844963074}, {"X": 0.38674676418304443, "Y": 0.5839663147926331}, {"X": 0.3866555392742157, "Y": 0.5932519435882568}, {"X": 0.30299919843673706, "Y": 0.5925449132919312}]}, "Id": "84e4a80c-d476-4b32-af11-a4c59b36bdda"}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "Racks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053011197596788406, "Height": 0.012904644943773746, "Left": 0.10488227754831314, "Top": 0.604655921459198}, "Polygon": [{"X": 0.10500647872686386, "Y": 0.604655921459198}, {"X": 0.15789346396923065, "Y": 0.6051029562950134}, {"X": 0.1577696055173874, "Y": 0.6175605654716492}, {"X": 0.10488227754831314, "Y": 0.6171135902404785}]}, "Id": "68999652-6e94-41c0-8c00-db69606271ff"}, {"BlockType": "WORD", "Confidence": 95.11559295654297, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005946775432676077, "Height": 0.013814562931656837, "Left": 0.16330699622631073, "Top": 0.6058267951011658}, "Polygon": [{"X": 0.16344383358955383, "Y": 0.6058267951011658}, {"X": 0.16925378143787384, "Y": 0.6058759093284607}, {"X": 0.16911698877811432, "Y": 0.6196413636207581}, {"X": 0.16330699622631073, "Y": 0.6195922493934631}]}, "Id": "0ea2f1f3-e1b5-4bd2-9d7d-80aa484f53c3"}, {"BlockType": "WORD", "Confidence": 95.51598358154297, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005889044608920813, "Height": 0.0140731455758214, "Left": 0.1849711537361145, "Top": 0.6056995987892151}, "Polygon": [{"X": 0.185110405087471, "Y": 0.6056995987892151}, {"X": 0.19086019694805145, "Y": 0.605748176574707}, {"X": 0.19072099030017853, "Y": 0.619772732257843}, {"X": 0.1849711537361145, "Y": 0.6197241544723511}]}, "Id": "dc7a6d89-7d8c-4311-a675-55a66cf02a5e"}, {"BlockType": "WORD", "Confidence": 85.90939331054688, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009451748803257942, "Height": 0.008538397029042244, "Left": 0.1961943507194519, "Top": 0.6091700196266174}, "Polygon": [{"X": 0.19627830386161804, "Y": 0.6091700196266174}, {"X": 0.2056460976600647, "Y": 0.6092491745948792}, {"X": 0.20556220412254333, "Y": 0.6177084445953369}, {"X": 0.1961943507194519, "Y": 0.6176292300224304}]}, "Id": "3623160d-018b-4097-9f3c-6ac6f3669772"}, {"BlockType": "WORD", "Confidence": 97.15660858154297, "Text": "$250.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0648001879453659, "Height": 0.0128966448828578, "Left": 0.21098797023296356, "Top": 0.6061049699783325}, "Polygon": [{"X": 0.2111104279756546, "Y": 0.6061049699783325}, {"X": 0.27578815817832947, "Y": 0.6066516041755676}, {"X": 0.27566611766815186, "Y": 0.6190016269683838}, {"X": 0.21098797023296356, "Y": 0.6184549331665039}]}, "Id": "ccb03689-226e-410f-9c1f-5d083330a4bf"}, {"BlockType": "WORD", "Confidence": 96.2509994506836, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011233472265303135, "Height": 0.0053304764442145824, "Left": 0.28049686551094055, "Top": 0.6102778315544128}, "Polygon": [{"X": 0.2805485725402832, "Y": 0.6102778315544128}, {"X": 0.2917303144931793, "Y": 0.6103723049163818}, {"X": 0.29167863726615906, "Y": 0.615608274936676}, {"X": 0.28049686551094055, "Y": 0.615513801574707}]}, "Id": "bbdac656-74bc-453a-9b23-f126a8ca752d"}, {"BlockType": "WORD", "Confidence": 97.15740966796875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009807301685214043, "Height": 0.012637851759791374, "Left": 0.2964484691619873, "Top": 0.6069991588592529}, "Polygon": [{"X": 0.29657241702079773, "Y": 0.6069991588592529}, {"X": 0.3062557578086853, "Y": 0.6070809960365295}, {"X": 0.30613186955451965, "Y": 0.6196370124816895}, {"X": 0.2964484691619873, "Y": 0.6195551753044128}]}, "Id": "d869d15f-be8e-466d-abb3-49f7fa59651c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041543148458004, "Height": 0.012451651506125927, "Left": 0.5884730815887451, "Top": 0.6110802888870239}, "Polygon": [{"X": 0.5885907411575317, "Y": 0.6110802888870239}, {"X": 0.6300162076950073, "Y": 0.6114304661750793}, {"X": 0.6298988461494446, "Y": 0.6235319972038269}, {"X": 0.5884730815887451, "Y": 0.6231818199157715}]}, "Id": "c3e65ad0-f978-47e4-9429-1926d692ff89"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09021972119808197, "Height": 0.01276236679404974, "Left": 0.6360814571380615, "Top": 0.6118665337562561}, "Polygon": [{"X": 0.6361978054046631, "Y": 0.6118665337562561}, {"X": 0.7263011336326599, "Y": 0.6126280426979065}, {"X": 0.7261853218078613, "Y": 0.6246289014816284}, {"X": 0.6360814571380615, "Y": 0.6238672733306885}]}, "Id": "1d068852-3f52-4090-8914-8db1bfb3e3f6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027993513271212578, "Height": 0.012008106335997581, "Left": 0.589975893497467, "Top": 0.6285871863365173}, "Polygon": [{"X": 0.5900903344154358, "Y": 0.6285871863365173}, {"X": 0.6179693937301636, "Y": 0.6288228034973145}, {"X": 0.6178551316261292, "Y": 0.6405952572822571}, {"X": 0.589975893497467, "Y": 0.64035964012146}]}, "Id": "de4c6ff5-d8de-4b1d-a70d-36be8289ec1b"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "Load:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04524943232536316, "Height": 0.01231857854872942, "Left": 0.6224949359893799, "Top": 0.6290997862815857}, "Polygon": [{"X": 0.6226107478141785, "Y": 0.6290997862815857}, {"X": 0.6677443385124207, "Y": 0.629481315612793}, {"X": 0.667628824710846, "Y": 0.6414183974266052}, {"X": 0.6224949359893799, "Y": 0.641036868095398}]}, "Id": "aa33e8ef-964d-4ec7-a163-73cf6489ca2f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03970906138420105, "Height": 0.012369414791464806, "Left": 0.1045900210738182, "Top": 0.639326274394989}, "Polygon": [{"X": 0.10471001267433167, "Y": 0.639326274394989}, {"X": 0.14429907500743866, "Y": 0.6396609544754028}, {"X": 0.1441793292760849, "Y": 0.651695728302002}, {"X": 0.1045900210738182, "Y": 0.6513610482215881}]}, "Id": "8f4e2dbf-8b11-4725-9051-ff06f7e94ecb"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "Locks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05227024480700493, "Height": 0.013028940185904503, "Left": 0.15001986920833588, "Top": 0.6396939158439636}, "Polygon": [{"X": 0.15014506876468658, "Y": 0.6396939158439636}, {"X": 0.2022901028394699, "Y": 0.6401346921920776}, {"X": 0.20216523110866547, "Y": 0.6527228355407715}, {"X": 0.15001986920833588, "Y": 0.6522820591926575}]}, "Id": "664f54c6-c244-470f-b055-ae1942670270"}, {"BlockType": "WORD", "Confidence": 91.95172882080078, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005875525996088982, "Height": 0.013713774271309376, "Left": 0.20845770835876465, "Top": 0.6408931612968445}, "Polygon": [{"X": 0.2085932195186615, "Y": 0.6408931612968445}, {"X": 0.21433323621749878, "Y": 0.6409416794776917}, {"X": 0.21419775485992432, "Y": 0.6546069383621216}, {"X": 0.20845770835876465, "Y": 0.6545584201812744}]}, "Id": "b17c10da-ec92-45f0-99a1-13aa1cfa99c8"}, {"BlockType": "WORD", "Confidence": 95.57298278808594, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005867570638656616, "Height": 0.013659294694662094, "Left": 0.2301086038351059, "Top": 0.641294002532959}, "Polygon": [{"X": 0.23024342954158783, "Y": 0.641294002532959}, {"X": 0.2359761744737625, "Y": 0.6413424611091614}, {"X": 0.23584139347076416, "Y": 0.6549533009529114}, {"X": 0.2301086038351059, "Y": 0.654904842376709}]}, "Id": "530ea461-91e0-4eb3-9b9b-0270ca814664"}, {"BlockType": "WORD", "Confidence": 89.60160064697266, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008994589559733868, "Height": 0.008463992737233639, "Left": 0.241428941488266, "Top": 0.64460289478302}, "Polygon": [{"X": 0.2415119856595993, "Y": 0.64460289478302}, {"X": 0.25042352080345154, "Y": 0.6446782350540161}, {"X": 0.2503405213356018, "Y": 0.653066873550415}, {"X": 0.241428941488266, "Y": 0.652991533279419}]}, "Id": "ef42b157-6389-4bb5-8e0d-69419e65c1f2"}, {"BlockType": "WORD", "Confidence": 97.02806091308594, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05486908555030823, "Height": 0.01286278199404478, "Left": 0.2560127377510071, "Top": 0.6413679122924805}, "Polygon": [{"X": 0.25613540410995483, "Y": 0.6413679122924805}, {"X": 0.3108818233013153, "Y": 0.6418307423591614}, {"X": 0.3107595145702362, "Y": 0.6542307138442993}, {"X": 0.2560127377510071, "Y": 0.6537678837776184}]}, "Id": "aa3013fb-aa1a-46e4-97ff-526e14f301c5"}, {"BlockType": "WORD", "Confidence": 96.55213165283203, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010595500469207764, "Height": 0.005115726497024298, "Left": 0.3161352872848511, "Top": 0.6458781361579895}, "Polygon": [{"X": 0.3161848485469818, "Y": 0.6458781361579895}, {"X": 0.32673078775405884, "Y": 0.6459672451019287}, {"X": 0.3266812562942505, "Y": 0.650993824005127}, {"X": 0.3161352872848511, "Y": 0.6509047150611877}]}, "Id": "94e01274-0eab-403f-8cc8-deb1c1e5c8c7"}, {"BlockType": "WORD", "Confidence": 93.70874786376953, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009382465854287148, "Height": 0.012627250514924526, "Left": 0.33165380358695984, "Top": 0.6419695019721985}, "Polygon": [{"X": 0.3317774534225464, "Y": 0.6419695019721985}, {"X": 0.34103626012802124, "Y": 0.6420477628707886}, {"X": 0.34091266989707947, "Y": 0.654596745967865}, {"X": 0.33165380358695984, "Y": 0.6545184850692749}]}, "Id": "497d2bbd-f7d8-49ed-a713-b37e1ef46ca1"}, {"BlockType": "WORD", "Confidence": 99.64604187011719, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00989188626408577, "Height": 0.012164317071437836, "Left": 0.5883252024650574, "Top": 0.6635552048683167}, "Polygon": [{"X": 0.5884426236152649, "Y": 0.6635552048683167}, {"X": 0.5982170701026917, "Y": 0.6636378169059753}, {"X": 0.5980997085571289, "Y": 0.6757194995880127}, {"X": 0.5883252024650574, "Y": 0.675636887550354}]}, "Id": "8796b9ce-c6a2-40f4-9db3-d21ddb13afef"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04497359320521355, "Height": 0.012954344041645527, "Left": 0.10362917184829712, "Top": 0.6740297079086304}, "Polygon": [{"X": 0.10375455021858215, "Y": 0.6740297079086304}, {"X": 0.14860276877880096, "Y": 0.6744088530540466}, {"X": 0.1484776735305786, "Y": 0.6869840621948242}, {"X": 0.10362917184829712, "Y": 0.6866048574447632}]}, "Id": "96a6dc66-197f-4313-905f-363492d18fdf"}, {"BlockType": "WORD", "Confidence": 99.73394012451172, "Text": "<PERSON>:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0400344580411911, "Height": 0.012413877062499523, "Left": 0.15281309187412262, "Top": 0.6749809384346008}, "Polygon": [{"X": 0.15293319523334503, "Y": 0.6749809384346008}, {"X": 0.19284754991531372, "Y": 0.6753183603286743}, {"X": 0.1927276998758316, "Y": 0.6873947978019714}, {"X": 0.15281309187412262, "Y": 0.6870573163032532}]}, "Id": "73de3879-c9df-401d-958a-d40d4434ad58"}, {"BlockType": "WORD", "Confidence": 93.62065887451172, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005501094274222851, "Height": 0.013865155167877674, "Left": 0.19905009865760803, "Top": 0.6760560274124146}, "Polygon": [{"X": 0.19918721914291382, "Y": 0.6760560274124146}, {"X": 0.20455119013786316, "Y": 0.6761013865470886}, {"X": 0.20441411435604095, "Y": 0.6899212002754211}, {"X": 0.19905009865760803, "Y": 0.6898758411407471}]}, "Id": "30bd98bd-5ec0-40ee-b593-875040fe5e6e"}, {"BlockType": "WORD", "Confidence": 94.32478332519531, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006392985582351685, "Height": 0.014311769977211952, "Left": 0.22035659849643707, "Top": 0.6761695146560669}, "Polygon": [{"X": 0.22049790620803833, "Y": 0.6761695146560669}, {"X": 0.22674958407878876, "Y": 0.6762223839759827}, {"X": 0.22660832107067108, "Y": 0.6904813051223755}, {"X": 0.22035659849643707, "Y": 0.6904284358024597}]}, "Id": "ee034438-1c08-463f-873c-75d7cf605bcf"}, {"BlockType": "WORD", "Confidence": 88.4948959350586, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009388966485857964, "Height": 0.008584313094615936, "Left": 0.23172476887702942, "Top": 0.6794682145118713}, "Polygon": [{"X": 0.23180902004241943, "Y": 0.6794682145118713}, {"X": 0.24111373722553253, "Y": 0.6795468926429749}, {"X": 0.2410295307636261, "Y": 0.6880525350570679}, {"X": 0.23172476887702942, "Y": 0.6879738569259644}]}, "Id": "46d9a578-5427-4e41-9881-5214c64e730a"}, {"BlockType": "WORD", "Confidence": 96.61052703857422, "Text": "$500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06502419710159302, "Height": 0.012970604933798313, "Left": 0.2465778887271881, "Top": 0.6761258244514465}, "Polygon": [{"X": 0.24670082330703735, "Y": 0.6761258244514465}, {"X": 0.31160208582878113, "Y": 0.676674485206604}, {"X": 0.3114795684814453, "Y": 0.6890963912010193}, {"X": 0.2465778887271881, "Y": 0.688547670841217}]}, "Id": "4543ad53-7041-48bf-90bb-95f8ebe46e9c"}, {"BlockType": "WORD", "Confidence": 95.65370178222656, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010579811409115791, "Height": 0.004921437241137028, "Left": 0.3166060149669647, "Top": 0.6810029149055481}, "Polygon": [{"X": 0.31665366888046265, "Y": 0.6810029149055481}, {"X": 0.32718583941459656, "Y": 0.6810919642448425}, {"X": 0.32713818550109863, "Y": 0.6859243512153625}, {"X": 0.3166060149669647, "Y": 0.6858353018760681}]}, "Id": "473ef599-88f7-4754-a903-c0777ef583bb"}, {"BlockType": "WORD", "Confidence": 95.02391815185547, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009583690203726292, "Height": 0.012360446155071259, "Left": 0.33203545212745667, "Top": 0.6773024797439575}, "Polygon": [{"X": 0.3321564495563507, "Y": 0.6773024797439575}, {"X": 0.3416191637516022, "Y": 0.6773824691772461}, {"X": 0.3414982259273529, "Y": 0.6896629333496094}, {"X": 0.33203545212745667, "Y": 0.689582884311676}]}, "Id": "b2457eaf-118b-47e9-96f6-7ae44b50a414"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03943980112671852, "Height": 0.012249135412275791, "Left": 0.10331040620803833, "Top": 0.7085973620414734}, "Polygon": [{"X": 0.10342922061681747, "Y": 0.7085973620414734}, {"X": 0.14275020360946655, "Y": 0.7089298367500305}, {"X": 0.1426316201686859, "Y": 0.7208464741706848}, {"X": 0.10331040620803833, "Y": 0.7205139994621277}]}, "Id": "e112fb35-cb9f-4b0e-9703-437730018223"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Straps:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054432183504104614, "Height": 0.01537061482667923, "Left": 0.1481698602437973, "Top": 0.7090557217597961}, "Polygon": [{"X": 0.14831820130348206, "Y": 0.7090557217597961}, {"X": 0.20260204374790192, "Y": 0.7095147371292114}, {"X": 0.20245413482189178, "Y": 0.7244263887405396}, {"X": 0.1481698602437973, "Y": 0.7239673137664795}]}, "Id": "b903c6e4-4700-43c7-a674-8002b696f888"}, {"BlockType": "WORD", "Confidence": 93.35778045654297, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0057264105416834354, "Height": 0.014234128408133984, "Left": 0.20831938087940216, "Top": 0.7102115154266357}, "Polygon": [{"X": 0.20846007764339447, "Y": 0.7102115154266357}, {"X": 0.21404579281806946, "Y": 0.7102587819099426}, {"X": 0.21390515565872192, "Y": 0.724445641040802}, {"X": 0.20831938087940216, "Y": 0.7243984341621399}]}, "Id": "7786443c-1958-49c3-9123-cb3a907b1856"}, {"BlockType": "WORD", "Confidence": 96.12962341308594, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005679596681147814, "Height": 0.013412185944616795, "Left": 0.23004721105098724, "Top": 0.7108877301216125}, "Polygon": [{"X": 0.2301795929670334, "Y": 0.7108877301216125}, {"X": 0.23572680354118347, "Y": 0.7109346389770508}, {"X": 0.23559445142745972, "Y": 0.7242999076843262}, {"X": 0.23004721105098724, "Y": 0.7242529988288879}]}, "Id": "63fceacb-fe63-442f-b6b6-193c8f92258f"}, {"BlockType": "WORD", "Confidence": 84.33534240722656, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008921630680561066, "Height": 0.008703217841684818, "Left": 0.2411629557609558, "Top": 0.7136701941490173}, "Polygon": [{"X": 0.24124838411808014, "Y": 0.7136701941490173}, {"X": 0.2500845789909363, "Y": 0.7137448787689209}, {"X": 0.24999921023845673, "Y": 0.722373366355896}, {"X": 0.2411629557609558, "Y": 0.7222986817359924}]}, "Id": "58d1873f-948f-4aca-8ef9-394c10da202a"}, {"BlockType": "WORD", "Confidence": 97.86053466796875, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05493612587451935, "Height": 0.012829480692744255, "Left": 0.2558470368385315, "Top": 0.7106748223304749}, "Polygon": [{"X": 0.2559693455696106, "Y": 0.7106748223304749}, {"X": 0.31078314781188965, "Y": 0.7111383080482483}, {"X": 0.3106611669063568, "Y": 0.7235043048858643}, {"X": 0.2558470368385315, "Y": 0.7230408191680908}]}, "Id": "869728ed-d2b2-4b19-860d-c67af8c5812a"}, {"BlockType": "WORD", "Confidence": 83.69678497314453, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010477712377905846, "Height": 0.005078716669231653, "Left": 0.3157948851585388, "Top": 0.7147446870803833}, "Polygon": [{"X": 0.3158440887928009, "Y": 0.7147446870803833}, {"X": 0.326272577047348, "Y": 0.7148328423500061}, {"X": 0.3262234032154083, "Y": 0.71982342004776}, {"X": 0.3157948851585388, "Y": 0.7197352051734924}]}, "Id": "599f2373-cc4a-4a6b-925c-6c795cda64d5"}, {"BlockType": "WORD", "Confidence": 98.564453125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009454022161662579, "Height": 0.012614774517714977, "Left": 0.3314706087112427, "Top": 0.7115774750709534}, "Polygon": [{"X": 0.3315941095352173, "Y": 0.7115774750709534}, {"X": 0.34092462062835693, "Y": 0.711656391620636}, {"X": 0.3408011794090271, "Y": 0.7241922616958618}, {"X": 0.3314706087112427, "Y": 0.7241133451461792}]}, "Id": "8f4d9756-b0ab-40f1-a6a5-1f9191076936"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Paperwork", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08531297743320465, "Height": 0.01577550172805786, "Left": 0.10152444988489151, "Top": 0.743242621421814}, "Polygon": [{"X": 0.10167457163333893, "Y": 0.743242621421814}, {"X": 0.18683743476867676, "Y": 0.743962824344635}, {"X": 0.18668796122074127, "Y": 0.7590181231498718}, {"X": 0.10152444988489151, "Y": 0.758297860622406}]}, "Id": "d40ce2b5-dc5f-445c-9ae6-eb7702f6e88d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0148877939209342, "Height": 0.0120146619156003, "Left": 0.19222067296504974, "Top": 0.7445847392082214}, "Polygon": [{"X": 0.19233867526054382, "Y": 0.7445847392082214}, {"X": 0.20710846781730652, "Y": 0.7447096705436707}, {"X": 0.2069905549287796, "Y": 0.7565994262695312}, {"X": 0.19222067296504974, "Y": 0.756474494934082}]}, "Id": "0d787c4b-0fea-4b0b-9b8c-095459385fb9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "other", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04173537343740463, "Height": 0.012275359593331814, "Left": 0.2122514545917511, "Top": 0.7448063492774963}, "Polygon": [{"X": 0.2123696655035019, "Y": 0.7448063492774963}, {"X": 0.25398683547973633, "Y": 0.745158314704895}, {"X": 0.25386887788772583, "Y": 0.7570816874504089}, {"X": 0.2122514545917511, "Y": 0.7567297220230103}]}, "Id": "d024a138-437e-42fc-9cfe-7f9ab3f7be14"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "Fines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04281152784824371, "Height": 0.012313313782215118, "Left": 0.25874584913253784, "Top": 0.7453969120979309}, "Polygon": [{"X": 0.25886404514312744, "Y": 0.7453969120979309}, {"X": 0.30155736207962036, "Y": 0.7457579970359802}, {"X": 0.30143943428993225, "Y": 0.7577102780342102}, {"X": 0.25874584913253784, "Y": 0.7573491930961609}]}, "Id": "ed170926-93c9-4df3-82ed-e616b02e0588"}, {"BlockType": "WORD", "Confidence": 99.41824340820312, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009752875193953514, "Height": 0.011625644750893116, "Left": 0.30630168318748474, "Top": 0.7461805939674377}, "Polygon": [{"X": 0.3064155876636505, "Y": 0.7461805939674377}, {"X": 0.3160545527935028, "Y": 0.7462621331214905}, {"X": 0.3159407377243042, "Y": 0.7578062415122986}, {"X": 0.30630168318748474, "Y": 0.7577247619628906}]}, "Id": "5e0ff1b0-fca7-47a5-9553-f8afc5f6cb80"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "(see", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032079290598630905, "Height": 0.013407059945166111, "Left": 0.1011076346039772, "Top": 0.7611396312713623}, "Polygon": [{"X": 0.10123863071203232, "Y": 0.7611396312713623}, {"X": 0.1331869214773178, "Y": 0.7614098191261292}, {"X": 0.1330561488866806, "Y": 0.7745466828346252}, {"X": 0.1011076346039772, "Y": 0.7742764353752136}]}, "Id": "77ee4335-882f-4a2e-b8ea-f4e805696b5e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06586690992116928, "Height": 0.012548301368951797, "Left": 0.13760218024253845, "Top": 0.7612140774726868}, "Polygon": [{"X": 0.13772153854370117, "Y": 0.7612140774726868}, {"X": 0.20346909761428833, "Y": 0.7617701292037964}, {"X": 0.20335014164447784, "Y": 0.7737624049186707}, {"X": 0.13760218024253845, "Y": 0.7732062935829163}]}, "Id": "6ba9b897-7f79-4a91-bdac-8a2d543749d8"}, {"BlockType": "WORD", "Confidence": 97.22038269042969, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12526585161685944, "Height": 0.015378037467598915, "Left": 0.2080761045217514, "Top": 0.7619529366493225}, "Polygon": [{"X": 0.20821811258792877, "Y": 0.7619529366493225}, {"X": 0.33334195613861084, "Y": 0.7630111575126648}, {"X": 0.3332009017467499, "Y": 0.7773309350013733}, {"X": 0.2080761045217514, "Y": 0.7762726545333862}]}, "Id": "7e1ad536-d5b5-4b1f-baf7-b2edbc3f1603"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Manager", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07078222185373306, "Height": 0.015036674216389656, "Left": 0.5317180752754211, "Top": 0.767128050327301}, "Polygon": [{"X": 0.5318588614463806, "Y": 0.767128050327301}, {"X": 0.6025002598762512, "Y": 0.7677254676818848}, {"X": 0.6023600101470947, "Y": 0.7821646928787231}, {"X": 0.5317180752754211, "Y": 0.7815672159194946}]}, "Id": "1df55d78-bda3-4759-98f5-75d83d033d93"}, {"BlockType": "WORD", "Confidence": 99.0832290649414, "Text": "Approval:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07944656908512115, "Height": 0.014587892219424248, "Left": 0.6064778566360474, "Top": 0.7686540484428406}, "Polygon": [{"X": 0.6066129803657532, "Y": 0.7686540484428406}, {"X": 0.6859244108200073, "Y": 0.769324779510498}, {"X": 0.6857898235321045, "Y": 0.7832419276237488}, {"X": 0.6064778566360474, "Y": 0.7825710773468018}]}, "Id": "5a373a68-7d4f-4a4b-9361-09e4c1cbcc7a"}, {"BlockType": "WORD", "Confidence": 84.3504867553711, "Text": "my", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08269514888525009, "Height": 0.07104548066854477, "Left": 0.7548659443855286, "Top": 0.7374851107597351}, "Polygon": [{"X": 0.7555437684059143, "Y": 0.7374851107597351}, {"X": 0.8375611305236816, "Y": 0.7381786704063416}, {"X": 0.8368862867355347, "Y": 0.8085305690765381}, {"X": 0.7548659443855286, "Y": 0.8078367710113525}]}, "Id": "0fff1b61-3331-474b-a1c9-cedbc460fb53"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Notes:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05096597597002983, "Height": 0.01281474158167839, "Left": 0.09995508193969727, "Top": 0.7950825691223145}, "Polygon": [{"X": 0.10007858276367188, "Y": 0.7950825691223145}, {"X": 0.1509210616350174, "Y": 0.7955126166343689}, {"X": 0.15079788863658905, "Y": 0.8078973293304443}, {"X": 0.09995508193969727, "Y": 0.8074672222137451}]}, "Id": "bffa2240-09f2-4673-a6f5-6886a41baafc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02891189604997635, "Height": 0.01229770202189684, "Left": 0.09869194775819778, "Top": 0.8468928337097168}, "Polygon": [{"X": 0.09881215542554855, "Y": 0.8468928337097168}, {"X": 0.12760384380817413, "Y": 0.8471364378929138}, {"X": 0.1274838149547577, "Y": 0.8591905236244202}, {"X": 0.09869194775819778, "Y": 0.8589469194412231}]}, "Id": "02127f37-c30d-41c0-9ea9-7a278740f353"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "fine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02577262371778488, "Height": 0.010174371302127838, "Left": 0.13171708583831787, "Top": 0.8469083905220032}, "Polygon": [{"X": 0.13181622326374054, "Y": 0.8469083905220032}, {"X": 0.15748971700668335, "Y": 0.8471255898475647}, {"X": 0.15739071369171143, "Y": 0.8570827841758728}, {"X": 0.13171708583831787, "Y": 0.8568655848503113}]}, "Id": "b9e2a8b9-42dc-4a53-a831-5996f08545f2"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021556124091148376, "Height": 0.0090047437697649, "Left": 0.16120196878910065, "Top": 0.848614513874054}, "Polygon": [{"X": 0.16128967702388763, "Y": 0.848614513874054}, {"X": 0.18275809288024902, "Y": 0.8487961888313293}, {"X": 0.1826704889535904, "Y": 0.8576192855834961}, {"X": 0.16120196878910065, "Y": 0.8574376702308655}]}, "Id": "1ca19027-f0d9-4a65-ab29-329f855aaeba"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "appealed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05902313068509102, "Height": 0.013062063604593277, "Left": 0.18642163276672363, "Top": 0.8479506969451904}, "Polygon": [{"X": 0.18654634058475494, "Y": 0.8479506969451904}, {"X": 0.24544475972652435, "Y": 0.8484489917755127}, {"X": 0.2453204095363617, "Y": 0.8610127568244934}, {"X": 0.18642163276672363, "Y": 0.8605144023895264}]}, "Id": "adca94e7-ccc8-4d7c-9149-a2f5c5f5ed67"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041931234300136566, "Height": 0.010614411905407906, "Left": 0.24992050230503082, "Top": 0.848617672920227}, "Polygon": [{"X": 0.2500220239162445, "Y": 0.848617672920227}, {"X": 0.2918517291545868, "Y": 0.8489715456962585}, {"X": 0.291750431060791, "Y": 0.8592320680618286}, {"X": 0.24992050230503082, "Y": 0.8588781356811523}]}, "Id": "25c9d1e9-1ab2-49f7-bda6-b134749d7511"}, {"BlockType": "WORD", "Confidence": 99.64445495605469, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007679763715714216, "Height": 0.009997515007853508, "Left": 0.2959761619567871, "Top": 0.849516749382019}, "Polygon": [{"X": 0.2960742115974426, "Y": 0.849516749382019}, {"X": 0.3036559224128723, "Y": 0.8495808839797974}, {"X": 0.3035579025745392, "Y": 0.8595142364501953}, {"X": 0.2959761619567871, "Y": 0.859450101852417}]}, "Id": "de23f3f3-ea80-49e2-97c3-08563774b1cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05628621578216553, "Height": 0.010727998800575733, "Left": 0.30829307436943054, "Top": 0.8493846654891968}, "Polygon": [{"X": 0.308394193649292, "Y": 0.8493846654891968}, {"X": 0.36457929015159607, "Y": 0.8498600125312805}, {"X": 0.3644784390926361, "Y": 0.8601126670837402}, {"X": 0.30829307436943054, "Y": 0.8596373200416565}]}, "Id": "9f89fa24-6081-49ae-b035-8366838e16bb"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03047446720302105, "Height": 0.012751161120831966, "Left": 0.36876487731933594, "Top": 0.8502707481384277}, "Polygon": [{"X": 0.368887722492218, "Y": 0.8502707481384277}, {"X": 0.39923933148384094, "Y": 0.8505275249481201}, {"X": 0.3991166949272156, "Y": 0.8630219101905823}, {"X": 0.36876487731933594, "Y": 0.8627650737762451}]}, "Id": "5e404964-098a-4417-b5ea-865d728c4ad8"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010642750188708305, "Height": 0.01043852511793375, "Left": 0.40378904342651367, "Top": 0.8505248427391052}, "Polygon": [{"X": 0.40389060974121094, "Y": 0.8505248427391052}, {"X": 0.41443178057670593, "Y": 0.850614070892334}, {"X": 0.41433027386665344, "Y": 0.8609634041786194}, {"X": 0.40378904342651367, "Y": 0.8608741760253906}]}, "Id": "a086aebe-58d7-450a-9f37-e8fe3b6f39dc"}, {"BlockType": "WORD", "Confidence": 80.25968933105469, "Text": "final.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03359198942780495, "Height": 0.011505044996738434, "Left": 0.4185010492801666, "Top": 0.8505959510803223}, "Polygon": [{"X": 0.4186111092567444, "Y": 0.8505959510803223}, {"X": 0.4520930349826813, "Y": 0.8508792519569397}, {"X": 0.45198318362236023, "Y": 0.8621010184288025}, {"X": 0.4185010492801666, "Y": 0.8618177175521851}]}, "Id": "3d7e015a-fc4c-4549-9dc3-e8a390c29f7c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Appeals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05387725308537483, "Height": 0.013078308664262295, "Left": 0.4569679796695709, "Top": 0.8517383933067322}, "Polygon": [{"X": 0.4570915400981903, "Y": 0.8517383933067322}, {"X": 0.5108452439308167, "Y": 0.8521931767463684}, {"X": 0.5107220411300659, "Y": 0.8648166656494141}, {"X": 0.4569679796695709, "Y": 0.8643618822097778}]}, "Id": "6ea9cca7-192e-43a3-9004-fa1e08016585"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0284785907715559, "Height": 0.010167604312300682, "Left": 0.5149418115615845, "Top": 0.8552235960960388}, "Polygon": [{"X": 0.5150386691093445, "Y": 0.8552235960960388}, {"X": 0.5434204339981079, "Y": 0.8554637432098389}, {"X": 0.5433236956596375, "Y": 0.865391194820404}, {"X": 0.5149418115615845, "Y": 0.865151047706604}]}, "Id": "b8dc19fa-8f80-4535-a5d6-f1fdeab61976"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01598433405160904, "Height": 0.010280044749379158, "Left": 0.5474386215209961, "Top": 0.852927565574646}, "Polygon": [{"X": 0.5475374460220337, "Y": 0.852927565574646}, {"X": 0.5634229183197021, "Y": 0.8530619740486145}, {"X": 0.5633242130279541, "Y": 0.8632075786590576}, {"X": 0.5474386215209961, "Y": 0.8630731701850891}]}, "Id": "fd469ee0-1b1c-4676-afde-191135c1e2eb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03594948351383209, "Height": 0.010069978423416615, "Left": 0.5673677921295166, "Top": 0.8536140322685242}, "Polygon": [{"X": 0.5674628615379333, "Y": 0.8536140322685242}, {"X": 0.6033172607421875, "Y": 0.8539174199104309}, {"X": 0.6032224297523499, "Y": 0.8636840581893921}, {"X": 0.5673677921295166, "Y": 0.8633806705474854}]}, "Id": "961c2de2-ddc5-4057-83a5-7f3c7a490d59"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012236404232680798, "Height": 0.010197954252362251, "Left": 0.6075226664543152, "Top": 0.8537178039550781}, "Polygon": [{"X": 0.6076206564903259, "Y": 0.8537178039550781}, {"X": 0.6197590231895447, "Y": 0.8538205027580261}, {"X": 0.6196610927581787, "Y": 0.863915741443634}, {"X": 0.6075226664543152, "Y": 0.863813042640686}]}, "Id": "0ce5e2e0-1f10-4af6-a074-b3014dd78c24"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "writing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047958455979824066, "Height": 0.013181766495108604, "Left": 0.6240595579147339, "Top": 0.8542489409446716}, "Polygon": [{"X": 0.6241835355758667, "Y": 0.8542489409446716}, {"X": 0.6720180511474609, "Y": 0.8546536564826965}, {"X": 0.671894371509552, "Y": 0.8674306869506836}, {"X": 0.6240595579147339, "Y": 0.8670259714126587}]}, "Id": "8b559cf6-25f5-461d-861a-75e12fbd327f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02373308502137661, "Height": 0.009837266057729721, "Left": 0.6756114363670349, "Top": 0.8552027940750122}, "Polygon": [{"X": 0.6757046580314636, "Y": 0.8552027940750122}, {"X": 0.6993445158004761, "Y": 0.8554027676582336}, {"X": 0.6992513537406921, "Y": 0.8650400638580322}, {"X": 0.6756114363670349, "Y": 0.864840030670166}]}, "Id": "0243d3cd-bff8-4330-9754-2dd7e16da081"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "faxed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036675769835710526, "Height": 0.0112231420353055, "Left": 0.7039867639541626, "Top": 0.8547061681747437}, "Polygon": [{"X": 0.7040922045707703, "Y": 0.8547061681747437}, {"X": 0.7406625151634216, "Y": 0.8550155758857727}, {"X": 0.7405573129653931, "Y": 0.8659293055534363}, {"X": 0.7039867639541626, "Y": 0.8656198978424072}]}, "Id": "6307042e-c3b5-4ae0-b17a-4b9abdb9ea46"}, {"BlockType": "WORD", "Confidence": 99.69328308105469, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01321241445839405, "Height": 0.008608415722846985, "Left": 0.744418740272522, "Top": 0.8570097088813782}, "Polygon": [{"X": 0.7445006966590881, "Y": 0.8570097088813782}, {"X": 0.7576311826705933, "Y": 0.8571207523345947}, {"X": 0.7575493454933167, "Y": 0.865618109703064}, {"X": 0.744418740272522, "Y": 0.8655070066452026}]}, "Id": "d1412fde-1b52-47fc-972b-6d8fd7220d74"}, {"BlockType": "WORD", "Confidence": 68.82573699951172, "Text": "(616)669-7468", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1039067879319191, "Height": 0.013489997945725918, "Left": 0.7611679434776306, "Top": 0.8558846116065979}, "Polygon": [{"X": 0.7612894177436829, "Y": 0.8558846116065979}, {"X": 0.8650746941566467, "Y": 0.856762707233429}, {"X": 0.8649539351463318, "Y": 0.8693746328353882}, {"X": 0.7611679434776306, "Y": 0.8684964776039124}]}, "Id": "eb98772a-3f59-49ca-a178-f99f69d59d0d"}, {"BlockType": "WORD", "Confidence": 88.76853942871094, "Text": "R", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.027656713500618935, "Height": 0.021608801558613777, "Left": 0.9385234713554382, "Top": 0.9465957283973694}, "Polygon": [{"X": 0.9387273788452148, "Y": 0.9465957283973694}, {"X": 0.9661802053451538, "Y": 0.9468280673027039}, {"X": 0.9659765958786011, "Y": 0.9682044982910156}, {"X": 0.9385234713554382, "Y": 0.9679721593856812}]}, "Id": "65c1bfd7-fe01-4cef-a8a9-21a3278c56db"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "35613f49-4af5-47ac-97b0-c37517d1c32d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "35613f49-4af5-47ac-97b0-c37517d1c32d", "content-type": "application/x-amz-json-1.1", "content-length": "93116", "date": "Wed, 24 Sep 2025 12:42:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "d6f76039-c530-457f-8983-29b60168e86c", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:42:41 GMT", "content-type": "application/json", "content-length": "996", "connection": "keep-alive", "x-amzn-requestid": "d6f76039-c530-457f-8983-29b60168e86c"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to classify. The page shows \"Truckload Check-in Form\" and \"Clear-to-Pay Voucher\". It contains Load #, broker, date processed, load cost, rack returns, deductions, net cost, manager approval. The keywords: \"Clear-to-Pay Voucher\". So doc type likely clear_to_pay. Also has check-in form, but it's more a voucher for payment. The categories: clear_to_pay defined as approval for payment or ACH/Wire/Check transfer request or approval. This page appears to be a clearance to pay. So doc_type = clear_to_pay. No continuation. So we output array with one object: page_no 1, doc_type \"clear_to_pay\". Need to use tool classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"clear_to_pay\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2781, "outputTokens": 176, "totalTokens": 2957}, "metrics": {"latencyMs": 1035}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/05a905f7_NMWG7N543A7USM9VG3WI.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nised\nNumber\n20312\nSaw 8787849\nrermatic\nAMAMA\nSAWYER NURSERY.\nTruckload Check-in Form\nClear-to-Pay Voucher\nLoad # 20512 AL\nBroker PE\nDate Processed 4/24/24\nLoad Cost $ 4656.00\nRacks Returned\n50\nSurcharge + $\n50 XS 400 = $ 200.00 $\nDeductions - $\nRACKS\nAMOUNT\nTOTAL\nRacks\n+ $ 200.00 $\n(Rack checks will now be sent to the\nbroker.)\nNet Cost $ 4854.00\nBillable Shortages:\n(equipment missing with insufficient documentation)\nRacks: ( ) X $250.00 = $\nTotal Deductions\nFor Load:\nLoad Locks: ( ) X $25.00 = $\n$\nPallet Jack: ( ) X $500.00 = $\nLoad Straps: ( ) X $25.00 = $\nPaperwork & other Fines $\n(see attached documentation)\nManager Approval:\nmy\nNotes:\nAny fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468\nR\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}