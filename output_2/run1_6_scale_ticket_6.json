{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.8208631277084351, "Height": 0.9898151159286499, "Left": 0.0691436156630516, "Top": 0.0}, "Polygon": [{"X": 0.16014917194843292, "Y": 0.0}, {"X": 0.8900067806243896, "Y": 0.02273818850517273}, {"X": 0.8011518120765686, "Y": 0.9898151159286499}, {"X": 0.0691436156630516, "Y": 0.9546272158622742}]}, "Id": "3e9a99d2-da9c-45a4-bcf3-d0d499501956", "Relationships": [{"Type": "CHILD", "Ids": ["0e601599-f46d-45c3-901c-d3921febf157", "bcf4c5ff-73a6-43ab-9c85-e280daa31b60", "d836c344-bdad-4b1e-88a9-a5bfdd7bc6c3", "84996a38-006f-4857-b6b8-6322c972fc36", "0599b3e8-1fd0-4a14-8999-3105ebd07695", "5239136e-bce4-48d0-80d0-819c858c00bb", "91a9384b-7b1a-4729-9e3e-c8c813cea0ec", "29780d6a-ee71-4ab0-bafb-1a95658feb0b", "53f0dd0f-a448-4944-a957-87a9c19e2639", "0dc2ed4f-67d2-4a15-9015-6294ae08d90d", "7b760371-db56-4d5e-acaa-3094a7cbacc5", "08124a99-b3aa-433b-ae3b-167b402dd25f", "a492720d-4274-4e76-b94a-0426e5c42e9c", "f37ff13b-baeb-4676-b570-4ea0a38c3dd6", "a87dc616-5237-4da9-a117-8874941595d5", "ffee8707-982b-4814-8afd-571708687f55", "112c75d0-30ee-448a-89b1-eeee96dcfc9e", "f2e36057-6e50-480b-9569-ecb8b2801385", "96eeb836-b5c4-48e7-9443-812934b07b86", "be75d4eb-2933-4b7f-a78e-0e2cc15626db", "ba42e69b-8b7b-4220-aece-e66e394f0e8d", "96eb0cfb-4b98-4585-9f8e-fe8693a35b77", "924f888b-0342-4c3c-8fe4-2c4ac6b2ea1c", "069157c7-b48b-4958-b086-86ccbaaa522b", "cebc626c-f0b4-42c2-b06e-e3ce64ce1e4c", "8dd54458-4b7b-46f2-b268-f4f7cefde098", "e8fb3e93-3e24-4f28-bca4-69906df78fca", "ea2d7233-3f29-48d7-a90a-77a81686d038", "817aeff6-2aac-4b57-8c93-5a41596278e4", "db0133f6-61b2-44ec-96af-bf742c9e94d1", "5c46e33d-b8e7-42d7-a064-302d92dff0db", "8f4c7365-1b56-4f6c-a53c-71137d9c609a", "507fefb5-ee82-4c4b-bc49-c4ef6a383cbd", "72bfef42-210d-41bb-b942-dc0f5f1cb752", "9db17d3d-cec7-4a16-9295-bc53f7718015", "85677e44-e02e-44a3-be76-0508215f57df"]}]}, {"BlockType": "LINE", "Confidence": 23.811384201049805, "Text": "save THATStuff:", "Geometry": {"BoundingBox": {"Width": 0.30701184272766113, "Height": 0.07606030255556107, "Left": 0.19098401069641113, "Top": 0.03825943171977997}, "Polygon": [{"X": 0.19683627784252167, "Y": 0.03825943171977997}, {"X": 0.49799585342407227, "Y": 0.05210593342781067}, {"X": 0.49220067262649536, "Y": 0.11431973427534103}, {"X": 0.19098401069641113, "Y": 0.10042793303728104}]}, "Id": "0e601599-f46d-45c3-901c-d3921febf157", "Relationships": [{"Type": "CHILD", "Ids": ["f9fcd16f-d3c0-45b3-a8c5-2d719b142f19"]}]}, {"BlockType": "LINE", "Confidence": 99.2344970703125, "Text": "Scale Ticket #", "Geometry": {"BoundingBox": {"Width": 0.1155368909239769, "Height": 0.021604269742965698, "Left": 0.5133717656135559, "Top": 0.06735227257013321}, "Polygon": [{"X": 0.5148941874504089, "Y": 0.06735227257013321}, {"X": 0.6289086937904358, "Y": 0.07259809225797653}, {"X": 0.6273919939994812, "Y": 0.08895654231309891}, {"X": 0.5133717656135559, "Y": 0.08370621502399445}]}, "Id": "bcf4c5ff-73a6-43ab-9c85-e280daa31b60", "Relationships": [{"Type": "CHILD", "Ids": ["6eadbbff-d504-4745-a3f5-b7d15050d2d0", "b0dc9ca1-2d4d-4f0c-b6ff-26d4d10fa557", "99745e1b-570d-44ea-9854-a22a61a79f97"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "33505", "Geometry": {"BoundingBox": {"Width": 0.10950683802366257, "Height": 0.02962634153664112, "Left": 0.6814160346984863, "Top": 0.06591832637786865}, "Polygon": [{"X": 0.6837007403373718, "Y": 0.06591832637786865}, {"X": 0.7909228801727295, "Y": 0.07084938883781433}, {"X": 0.7886462211608887, "Y": 0.09554466605186462}, {"X": 0.6814160346984863, "Y": 0.09060720354318619}]}, "Id": "d836c344-bdad-4b1e-88a9-a5bfdd7bc6c3", "Relationships": [{"Type": "CHILD", "Ids": ["8cb7c579-2b51-4e76-8134-422bcc6e36d3"]}]}, {"BlockType": "LINE", "Confidence": 99.50494384765625, "Text": "One goal. Zero waste.", "Geometry": {"BoundingBox": {"Width": 0.1897258311510086, "Height": 0.034807004034519196, "Left": 0.25079041719436646, "Top": 0.09583594650030136}, "Polygon": [{"X": 0.2532478868961334, "Y": 0.09583594650030136}, {"X": 0.44051626324653625, "Y": 0.10446939617395401}, {"X": 0.4380737245082855, "Y": 0.13064295053482056}, {"X": 0.25079041719436646, "Y": 0.1219976544380188}]}, "Id": "84996a38-006f-4857-b6b8-6322c972fc36", "Relationships": [{"Type": "CHILD", "Ids": ["01e2e352-c7f2-4fd4-bf1d-fa6c23b802cc", "69f1d576-c131-4ef6-bca6-e52f5af825e4", "479149d5-4e27-4b93-9a33-d11c10ca2e0e", "0ea8cb68-feaf-4c66-a026-7d46084c1c98"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "Date 11/1/22", "Geometry": {"BoundingBox": {"Width": 0.2463722676038742, "Height": 0.053785499185323715, "Left": 0.5830399394035339, "Top": 0.10616394877433777}, "Polygon": [{"X": 0.5869938731193542, "Y": 0.10616394877433777}, {"X": 0.8294121623039246, "Y": 0.11733714491128922}, {"X": 0.8254897594451904, "Y": 0.15994945168495178}, {"X": 0.5830399394035339, "Y": 0.14875128865242004}]}, "Id": "0599b3e8-1fd0-4a14-8999-3105ebd07695", "Relationships": [{"Type": "CHILD", "Ids": ["ec3c406a-0907-4906-9756-48133856ca3e", "b2cef5d9-dd9d-424e-9fbb-2e3a25ab9ca8"]}]}, {"BlockType": "LINE", "Confidence": 99.21337127685547, "Text": "Customer:", "Geometry": {"BoundingBox": {"Width": 0.08563662320375443, "Height": 0.020685570314526558, "Left": 0.16973777115345, "Top": 0.15110334753990173}, "Polygon": [{"X": 0.17131967842578888, "Y": 0.15110334753990173}, {"X": 0.25537440180778503, "Y": 0.15498970448970795}, {"X": 0.25379678606987, "Y": 0.17178893089294434}, {"X": 0.16973777115345, "Y": 0.16789916157722473}]}, "Id": "5239136e-bce4-48d0-80d0-819c858c00bb", "Relationships": [{"Type": "CHILD", "Ids": ["78fd5579-b091-4a41-b9b8-0467bebd274a"]}]}, {"BlockType": "LINE", "Confidence": 80.79918670654297, "Text": "Earth Farthwatch watch", "Geometry": {"BoundingBox": {"Width": 0.3540515601634979, "Height": 0.06748820841312408, "Left": 0.3168354332447052, "Top": 0.14473871886730194}, "Polygon": [{"X": 0.32164278626441956, "Y": 0.14473871886730194}, {"X": 0.6708869934082031, "Y": 0.1608758270740509}, {"X": 0.6661342978477478, "Y": 0.21222692728042603}, {"X": 0.3168354332447052, "Y": 0.19604647159576416}]}, "Id": "91a9384b-7b1a-4729-9e3e-c8c813cea0ec", "Relationships": [{"Type": "CHILD", "Ids": ["00b7266a-a53b-4960-8de2-140d590298f8", "2f750378-f04e-40ed-92d8-57fe09a320a8", "d5170ebd-b119-4737-8a54-78a2b0170fa7"]}]}, {"BlockType": "LINE", "Confidence": 99.57688903808594, "Text": "Driver:", "Geometry": {"BoundingBox": {"Width": 0.05560598894953728, "Height": 0.019140271469950676, "Left": 0.16501140594482422, "Top": 0.20402459800243378}, "Polygon": [{"X": 0.16657796502113342, "Y": 0.20402459800243378}, {"X": 0.2206173986196518, "Y": 0.2065296769142151}, {"X": 0.21905358135700226, "Y": 0.2231648713350296}, {"X": 0.16501140594482422, "Y": 0.220657616853714}]}, "Id": "29780d6a-ee71-4ab0-bafb-1a95658feb0b", "Relationships": [{"Type": "CHILD", "Ids": ["fa8e0612-7a53-4e1e-a0e7-9eef29848b87"]}]}, {"BlockType": "LINE", "Confidence": 99.70145416259766, "Text": "Property Preservation", "Geometry": {"BoundingBox": {"Width": 0.5360442996025085, "Height": 0.08574112504720688, "Left": 0.28148728609085083, "Top": 0.18859267234802246}, "Polygon": [{"X": 0.2872185707092285, "Y": 0.18859267234802246}, {"X": 0.8175315260887146, "Y": 0.21315087378025055}, {"X": 0.8118991255760193, "Y": 0.27433380484580994}, {"X": 0.28148728609085083, "Y": 0.24969716370105743}]}, "Id": "53f0dd0f-a448-4944-a957-87a9c19e2639", "Relationships": [{"Type": "CHILD", "Ids": ["20d80793-bf68-473b-923d-fe0cbaddf0b8", "2cb4de53-9186-4189-a6c6-61f6129ae2f3"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "Route:", "Geometry": {"BoundingBox": {"Width": 0.05523088574409485, "Height": 0.019010048359632492, "Left": 0.16024087369441986, "Top": 0.2567090392112732}, "Polygon": [{"X": 0.16179613769054413, "Y": 0.2567090392112732}, {"X": 0.2154717594385147, "Y": 0.25920370221138}, {"X": 0.21391919255256653, "Y": 0.2757190763950348}, {"X": 0.16024087369441986, "Y": 0.27322226762771606}]}, "Id": "0dc2ed4f-67d2-4a15-9015-6294ae08d90d", "Relationships": [{"Type": "CHILD", "Ids": ["b39c7996-6d28-4d62-9c38-170f7601499c"]}]}, {"BlockType": "LINE", "Confidence": 84.2790756225586, "Text": "Truck: F2 /TLI", "Geometry": {"BoundingBox": {"Width": 0.2823660373687744, "Height": 0.05970204249024391, "Left": 0.4672570526599884, "Top": 0.25801485776901245}, "Polygon": [{"X": 0.4716136157512665, "Y": 0.25801485776901245}, {"X": 0.7496231198310852, "Y": 0.2709275484085083}, {"X": 0.7453061938285828, "Y": 0.31771692633628845}, {"X": 0.4672570526599884, "Y": 0.3047727644443512}]}, "Id": "7b760371-db56-4d5e-acaa-3094a7cbacc5", "Relationships": [{"Type": "CHILD", "Ids": ["c456f714-865c-4f35-b714-49f0960dcd0b", "7cddd201-619a-4279-b5d2-83d73750afa6", "638ff975-8bb0-46eb-9e5e-fc96e2f73df6"]}]}, {"BlockType": "LINE", "Confidence": 99.697265625, "Text": "Qty", "Geometry": {"BoundingBox": {"Width": 0.03200531750917435, "Height": 0.019973745569586754, "Left": 0.1677817851305008, "Top": 0.2826336920261383}, "Polygon": [{"X": 0.16952961683273315, "Y": 0.2826336920261383}, {"X": 0.19978711009025574, "Y": 0.28404170274734497}, {"X": 0.19804097712039948, "Y": 0.3026074469089508}, {"X": 0.1677817851305008, "Y": 0.3011980652809143}]}, "Id": "08124a99-b3aa-433b-ae3b-167b402dd25f", "Relationships": [{"Type": "CHILD", "Ids": ["a8a98c27-d46b-4739-80b7-e0b5995e1375"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.09368007630109787, "Height": 0.023432215675711632, "Left": 0.290900856256485, "Top": 0.2905772030353546}, "Polygon": [{"X": 0.2926962375640869, "Y": 0.2905772030353546}, {"X": 0.38458094000816345, "Y": 0.29485347867012024}, {"X": 0.3827908933162689, "Y": 0.3140093982219696}, {"X": 0.290900856256485, "Y": 0.30972886085510254}]}, "Id": "a492720d-4274-4e76-b94a-0426e5c42e9c", "Relationships": [{"Type": "CHILD", "Ids": ["1cd6cd2e-7956-475a-a74d-4f325b6fc6c0"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Weight", "Geometry": {"BoundingBox": {"Width": 0.0601443387567997, "Height": 0.021270908415317535, "Left": 0.6144177317619324, "Top": 0.3114752769470215}, "Polygon": [{"X": 0.616136908531189, "Y": 0.3114752769470215}, {"X": 0.6745620369911194, "Y": 0.3141951262950897}, {"X": 0.6728461980819702, "Y": 0.3327461779117584}, {"X": 0.6144177317619324, "Y": 0.33002370595932007}]}, "Id": "f37ff13b-baeb-4676-b570-4ea0a38c3dd6", "Relationships": [{"Type": "CHILD", "Ids": ["190824f9-16da-4b62-b1a6-062c384205f4"]}]}, {"BlockType": "LINE", "Confidence": 99.13006591796875, "Text": "28", "Geometry": {"BoundingBox": {"Width": 0.049481261521577835, "Height": 0.0388055220246315, "Left": 0.14929543435573578, "Top": 0.30834054946899414}, "Polygon": [{"X": 0.15274836122989655, "Y": 0.30834054946899414}, {"X": 0.19877669215202332, "Y": 0.31048521399497986}, {"X": 0.19532889127731323, "Y": 0.34714606404304504}, {"X": 0.14929543435573578, "Y": 0.3449973165988922}]}, "Id": "a87dc616-5237-4da9-a117-8874941595d5", "Relationships": [{"Type": "CHILD", "Ids": ["09553e4b-70e1-4fe8-833d-7dd7aefa99a9"]}]}, {"BlockType": "LINE", "Confidence": 84.52178192138672, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.25882452726364136, "Height": 0.05677437037229538, "Left": 0.20224052667617798, "Top": 0.31662917137145996}, "Polygon": [{"X": 0.206460103392601, "Y": 0.31662917137145996}, {"X": 0.46106505393981934, "Y": 0.32849574089050293}, {"X": 0.4568803012371063, "Y": 0.37340351939201355}, {"X": 0.20224052667617798, "Y": 0.3615092635154724}]}, "Id": "ffee8707-982b-4814-8afd-571708687f55", "Relationships": [{"Type": "CHILD", "Ids": ["6a0ce97a-2717-4b25-8676-36a8c5756bcb", "7902b6a7-93fd-4429-9fef-700d75bd0bbb"]}]}, {"BlockType": "LINE", "Confidence": 95.62789154052734, "Text": "ID 33505", "Geometry": {"BoundingBox": {"Width": 0.08518383651971817, "Height": 0.020854784175753593, "Left": 0.44337838888168335, "Top": 0.4133395552635193}, "Polygon": [{"X": 0.44495725631713867, "Y": 0.4133395552635193}, {"X": 0.5285622477531433, "Y": 0.41725242137908936}, {"X": 0.5269876718521118, "Y": 0.43419432640075684}, {"X": 0.44337838888168335, "Y": 0.4302780330181122}]}, "Id": "112c75d0-30ee-448a-89b1-eeee96dcfc9e", "Relationships": [{"Type": "CHILD", "Ids": ["e6f8125f-3ab5-42cb-b5a6-ae19f2c7c27d", "4910c451-b2cf-49fd-b57b-862b3a052dd8"]}]}, {"BlockType": "LINE", "Confidence": 99.56791687011719, "Text": "GROSS", "Geometry": {"BoundingBox": {"Width": 0.05537910386919975, "Height": 0.019032184034585953, "Left": 0.43797382712364197, "Top": 0.4490640163421631}, "Polygon": [{"X": 0.4395124912261963, "Y": 0.4490640163421631}, {"X": 0.4933529198169708, "Y": 0.4515882432460785}, {"X": 0.4918169677257538, "Y": 0.46809619665145874}, {"X": 0.43797382712364197, "Y": 0.4655698239803314}]}, "Id": "f2e36057-6e50-480b-9569-ecb8b2801385", "Relationships": [{"Type": "CHILD", "Ids": ["4b097862-aa7d-4cab-a556-0a255d31939c"]}]}, {"BlockType": "LINE", "Confidence": 97.12571716308594, "Text": "32660 lb INBOUND", "Geometry": {"BoundingBox": {"Width": 0.17239457368850708, "Height": 0.026339177042245865, "Left": 0.5326597094535828, "Top": 0.45597630739212036}, "Polygon": [{"X": 0.5343624949455261, "Y": 0.45597630739212036}, {"X": 0.7050542831420898, "Y": 0.46397989988327026}, {"X": 0.7033609747886658, "Y": 0.4823154807090759}, {"X": 0.5326597094535828, "Y": 0.47430431842803955}]}, "Id": "96eeb836-b5c4-48e7-9443-812934b07b86", "Relationships": [{"Type": "CHILD", "Ids": ["bb36fa66-5e6b-4d79-99cf-7f29b9b2d54d", "5026f52c-8de2-4a48-b495-fe3a37f03c00", "ec737940-6197-4708-acda-6050f71adb12"]}]}, {"BlockType": "LINE", "Confidence": 99.21067810058594, "Text": "11/01/2022 03:48PM", "Geometry": {"BoundingBox": {"Width": 0.19177702069282532, "Height": 0.02876386046409607, "Left": 0.43680450320243835, "Top": 0.4853757917881012}, "Polygon": [{"X": 0.4386533200740814, "Y": 0.4853757917881012}, {"X": 0.6285815238952637, "Y": 0.49429595470428467}, {"X": 0.6267442107200623, "Y": 0.5141396522521973}, {"X": 0.43680450320243835, "Y": 0.5052103400230408}]}, "Id": "be75d4eb-2933-4b7f-a78e-0e2cc15626db", "Relationships": [{"Type": "CHILD", "Ids": ["a7af72b1-daaf-439f-a440-7a6bf761c8a9", "56b94f80-6823-40d0-81ae-59a93efe24f9"]}]}, {"BlockType": "LINE", "Confidence": 79.1161117553711, "Text": "B", "Geometry": {"BoundingBox": {"Width": 0.020614156499505043, "Height": 0.015697767958045006, "Left": 0.42649751901626587, "Top": 0.5985692739486694}, "Polygon": [{"X": 0.42787599563598633, "Y": 0.5985692739486694}, {"X": 0.44711166620254517, "Y": 0.5994776487350464}, {"X": 0.44573405385017395, "Y": 0.6142670512199402}, {"X": 0.42649751901626587, "Y": 0.6133579611778259}]}, "Id": "ba42e69b-8b7b-4220-aece-e66e394f0e8d", "Relationships": [{"Type": "CHILD", "Ids": ["7ae0fe44-5cff-4ab4-bcef-9aa6c611344c"]}]}, {"BlockType": "LINE", "Confidence": 99.79173278808594, "Text": "33505", "Geometry": {"BoundingBox": {"Width": 0.05541754141449928, "Height": 0.019239595159888268, "Left": 0.4553203582763672, "Top": 0.6005008816719055}, "Polygon": [{"X": 0.456874817609787, "Y": 0.6005008816719055}, {"X": 0.5107378959655762, "Y": 0.6030445694923401}, {"X": 0.5091861486434937, "Y": 0.6197404861450195}, {"X": 0.4553203582763672, "Y": 0.6171945929527283}]}, "Id": "96eb0cfb-4b98-4585-9f8e-fe8693a35b77", "Relationships": [{"Type": "CHILD", "Ids": ["71f3534b-9aa9-4d70-b748-d86690df979c"]}]}, {"BlockType": "LINE", "Confidence": 98.26988983154297, "Text": "GROSS", "Geometry": {"BoundingBox": {"Width": 0.05582673102617264, "Height": 0.01825777254998684, "Left": 0.4196403920650482, "Top": 0.6341406106948853}, "Polygon": [{"X": 0.42110249400138855, "Y": 0.6341406106948853}, {"X": 0.47546711564064026, "Y": 0.636712372303009}, {"X": 0.47400760650634766, "Y": 0.652398407459259}, {"X": 0.4196403920650482, "Y": 0.6498246192932129}]}, "Id": "924f888b-0342-4c3c-8fe4-2c4ac6b2ea1c", "Relationships": [{"Type": "CHILD", "Ids": ["9808eea1-0665-4bfe-8a86-fa2a969caba8"]}]}, {"BlockType": "LINE", "Confidence": 88.61995697021484, "Text": "74000 lb", "Geometry": {"BoundingBox": {"Width": 0.08751638978719711, "Height": 0.02154361829161644, "Left": 0.5147263407707214, "Top": 0.641097366809845}, "Polygon": [{"X": 0.5163500905036926, "Y": 0.641097366809845}, {"X": 0.6022427678108215, "Y": 0.6451610326766968}, {"X": 0.6006235480308533, "Y": 0.6626409888267517}, {"X": 0.5147263407707214, "Y": 0.6585736870765686}]}, "Id": "069157c7-b48b-4958-b086-86ccbaaa522b", "Relationships": [{"Type": "CHILD", "Ids": ["64623f8c-0df3-4813-aae6-4f53d5f92e47", "8402d061-a3a1-4a79-80f1-c835cb800dbc"]}]}, {"BlockType": "LINE", "Confidence": 92.52279663085938, "Text": "1101A", "Geometry": {"BoundingBox": {"Width": 0.1563563197851181, "Height": 0.040600456297397614, "Left": 0.18255515396595, "Top": 0.6339708566665649}, "Polygon": [{"X": 0.18568839132785797, "Y": 0.6339708566665649}, {"X": 0.3389114737510681, "Y": 0.6412228941917419}, {"X": 0.33579379320144653, "Y": 0.6745712757110596}, {"X": 0.18255515396595, "Y": 0.6673068404197693}]}, "Id": "cebc626c-f0b4-42c2-b06e-e3ce64ce1e4c", "Relationships": [{"Type": "CHILD", "Ids": ["a4be73c9-86ac-43bf-9831-eb86e389284e"]}]}, {"BlockType": "LINE", "Confidence": 99.01228332519531, "Text": "TARE", "Geometry": {"BoundingBox": {"Width": 0.044566377997398376, "Height": 0.018297841772437096, "Left": 0.41869932413101196, "Top": 0.6517813801765442}, "Polygon": [{"X": 0.4202148914337158, "Y": 0.6517813801765442}, {"X": 0.46326568722724915, "Y": 0.6538196206092834}, {"X": 0.4617522358894348, "Y": 0.670079231262207}, {"X": 0.41869932413101196, "Y": 0.6680392622947693}]}, "Id": "8dd54458-4b7b-46f2-b268-f4f7cefde098", "Relationships": [{"Type": "CHILD", "Ids": ["c7484d5f-01bf-40c4-b039-72fef6596eb6"]}]}, {"BlockType": "LINE", "Confidence": 92.48464965820312, "Text": "32660 lb RECALLED", "Geometry": {"BoundingBox": {"Width": 0.18348495662212372, "Height": 0.028544653207063675, "Left": 0.5126543045043945, "Top": 0.6593790650367737}, "Polygon": [{"X": 0.5145065188407898, "Y": 0.6593790650367737}, {"X": 0.6961392760276794, "Y": 0.6679797768592834}, {"X": 0.6942980885505676, "Y": 0.6879236698150635}, {"X": 0.5126543045043945, "Y": 0.6793141961097717}]}, "Id": "e8fb3e93-3e24-4f28-bca4-69906df78fca", "Relationships": [{"Type": "CHILD", "Ids": ["bf606849-fa29-479e-9759-166a4278698d", "c0626839-d5ee-4483-8777-4a954b58e1b7", "c6900043-7bbd-47dc-b458-9843c6c73ac3"]}]}, {"BlockType": "LINE", "Confidence": 89.20340728759766, "Text": "NET", "Geometry": {"BoundingBox": {"Width": 0.03476808965206146, "Height": 0.018012573942542076, "Left": 0.4158450961112976, "Top": 0.669256329536438}, "Polygon": [{"X": 0.4173773527145386, "Y": 0.669256329536438}, {"X": 0.4506131708621979, "Y": 0.6708312034606934}, {"X": 0.4490825831890106, "Y": 0.6872689127922058}, {"X": 0.4158450961112976, "Y": 0.6856926679611206}]}, "Id": "ea2d7233-3f29-48d7-a90a-77a81686d038", "Relationships": [{"Type": "CHILD", "Ids": ["fe58cb29-38d1-467d-9eb2-4a092d800932"]}]}, {"BlockType": "LINE", "Confidence": 96.64879608154297, "Text": "41340 lb", "Geometry": {"BoundingBox": {"Width": 0.08843634277582169, "Height": 0.021091865375638008, "Left": 0.5114326477050781, "Top": 0.6780960559844971}, "Polygon": [{"X": 0.5130094885826111, "Y": 0.6780960559844971}, {"X": 0.5998690128326416, "Y": 0.682212769985199}, {"X": 0.5982966423034668, "Y": 0.6991879343986511}, {"X": 0.5114326477050781, "Y": 0.6950676441192627}]}, "Id": "817aeff6-2aac-4b57-8c93-5a41596278e4", "Relationships": [{"Type": "CHILD", "Ids": ["63d3abab-94f9-4890-9056-9b28c67130c2", "ad07a0d7-7860-4fc7-96c0-a8d72e45721e"]}]}, {"BlockType": "LINE", "Confidence": 99.9462890625, "Text": "Driver Signature", "Geometry": {"BoundingBox": {"Width": 0.11986224353313446, "Height": 0.022911077365279198, "Left": 0.11586029082536697, "Top": 0.7004227042198181}, "Polygon": [{"X": 0.11748898774385452, "Y": 0.7004227042198181}, {"X": 0.23572254180908203, "Y": 0.7060374021530151}, {"X": 0.23410005867481232, "Y": 0.7233337759971619}, {"X": 0.11586029082536697, "Y": 0.7177141308784485}]}, "Id": "db0133f6-61b2-44ec-96af-bf742c9e94d1", "Relationships": [{"Type": "CHILD", "Ids": ["8370056a-5e6b-4027-876c-17371732ecfd", "5f7d1661-eb73-4a63-956f-85f7de22b1d7"]}]}, {"BlockType": "LINE", "Confidence": 96.139892578125, "Text": "11/01/2022 05:12PM", "Geometry": {"BoundingBox": {"Width": 0.1914355456829071, "Height": 0.03068915568292141, "Left": 0.41582155227661133, "Top": 0.7059049606323242}, "Polygon": [{"X": 0.41784313321113586, "Y": 0.7059049606323242}, {"X": 0.607257068157196, "Y": 0.7148961424827576}, {"X": 0.6052480340003967, "Y": 0.7365941405296326}, {"X": 0.41582155227661133, "Y": 0.7275930047035217}]}, "Id": "5c46e33d-b8e7-42d7-a064-302d92dff0db", "Relationships": [{"Type": "CHILD", "Ids": ["aef386ab-8151-4f0b-ba0d-6258cfd556eb", "175459d1-87f3-43bc-a69f-3a8e42092c48"]}]}, {"BlockType": "LINE", "Confidence": 99.64285278320312, "Text": "Weighmaster Signature", "Geometry": {"BoundingBox": {"Width": 0.17173925042152405, "Height": 0.026387743651866913, "Left": 0.10823334008455276, "Top": 0.7791344523429871}, "Polygon": [{"X": 0.10995476692914963, "Y": 0.7791344523429871}, {"X": 0.2799725830554962, "Y": 0.7872388362884521}, {"X": 0.27826058864593506, "Y": 0.8055222034454346}, {"X": 0.10823334008455276, "Y": 0.7974103093147278}]}, "Id": "8f4c7365-1b56-4f6c-a53c-71137d9c609a", "Relationships": [{"Type": "CHILD", "Ids": ["5e267ddf-c288-4ab3-b14e-c533b678df30", "709d46a0-57d8-496b-acfd-774b86e7d22a"]}]}, {"BlockType": "LINE", "Confidence": 23.572425842285156, "Text": "175", "Geometry": {"BoundingBox": {"Width": 0.2000887542963028, "Height": 0.09589014947414398, "Left": 0.11242979019880295, "Top": 0.8044121265411377}, "Polygon": [{"X": 0.12059159576892853, "Y": 0.8044121265411377}, {"X": 0.31251853704452515, "Y": 0.8135716319084167}, {"X": 0.3044073283672333, "Y": 0.9003022313117981}, {"X": 0.11242979019880295, "Y": 0.8911024332046509}]}, "Id": "507fefb5-ee82-4c4b-bc49-c4ef6a383cbd", "Relationships": [{"Type": "CHILD", "Ids": ["25d93753-67e7-469b-bbc3-d096c050a897"]}]}, {"BlockType": "LINE", "Confidence": 99.619140625, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06282179802656174, "Height": 0.018645158037543297, "Left": 0.449160099029541, "Top": 0.8481684327125549}, "Polygon": [{"X": 0.45062217116355896, "Y": 0.8481684327125549}, {"X": 0.5119819045066833, "Y": 0.8511006236076355}, {"X": 0.5105227828025818, "Y": 0.866813600063324}, {"X": 0.449160099029541, "Y": 0.8638790249824524}]}, "Id": "72bfef42-210d-41bb-b942-dc0f5f1cb752", "Relationships": [{"Type": "CHILD", "Ids": ["dc7b8cd9-d583-47dd-a0cb-ded36553a5cf"]}]}, {"BlockType": "LINE", "Confidence": 99.82492065429688, "Text": "200 Terminal Street, Charlestown, MA 02129", "Geometry": {"BoundingBox": {"Width": 0.40328511595726013, "Height": 0.03838320076465607, "Left": 0.24132902920246124, "Top": 0.8936685919761658}, "Polygon": [{"X": 0.24312174320220947, "Y": 0.8936685919761658}, {"X": 0.6446141004562378, "Y": 0.9129053354263306}, {"X": 0.6428447961807251, "Y": 0.9320517778396606}, {"X": 0.24132902920246124, "Y": 0.9127964377403259}]}, "Id": "9db17d3d-cec7-4a16-9295-bc53f7718015", "Relationships": [{"Type": "CHILD", "Ids": ["195f6436-06f9-403a-88a0-b99ba47bd036", "40a5fbfa-cd9e-4c7f-a8bc-92cc3ac38c33", "f6bb4ba8-00be-475c-9f7c-a0437dbe4880", "3c72f507-e2be-43a2-8710-6e1e0158f869", "97080a09-a3cc-4c0d-916f-dc7fb3e53b14", "8081e040-492f-41f8-bdde-95e9273c90ad"]}]}, {"BlockType": "LINE", "Confidence": 95.21100616455078, "Text": "T 617.241.9998 F 617.241-0294 www.savethatstuff.com", "Geometry": {"BoundingBox": {"Width": 0.5077518820762634, "Height": 0.044467974454164505, "Left": 0.18648779392242432, "Top": 0.9129101037979126}, "Polygon": [{"X": 0.18838270008563995, "Y": 0.9129101037979126}, {"X": 0.6942396759986877, "Y": 0.9371724128723145}, {"X": 0.6923758387565613, "Y": 0.957378089427948}, {"X": 0.18648779392242432, "Y": 0.9330910444259644}]}, "Id": "85677e44-e02e-44a3-be76-0508215f57df", "Relationships": [{"Type": "CHILD", "Ids": ["2f2bca58-b897-4491-a832-37472d771849", "27319648-5548-4dc3-bcd0-af83d28d9ce4", "d960a52c-b43b-4c4e-945b-d608537d911d", "140bfe86-2443-48ee-921d-b3c8b90910e0"]}]}, {"BlockType": "WORD", "Confidence": 23.811384201049805, "Text": "save THATStuff:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.30701184272766113, "Height": 0.07606030255556107, "Left": 0.19098401069641113, "Top": 0.03825943171977997}, "Polygon": [{"X": 0.19683627784252167, "Y": 0.03825943171977997}, {"X": 0.49799585342407227, "Y": 0.05210593342781067}, {"X": 0.49220067262649536, "Y": 0.11431973427534103}, {"X": 0.19098401069641113, "Y": 0.10042793303728104}]}, "Id": "f9fcd16f-d3c0-45b3-a8c5-2d719b142f19"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04813927412033081, "Height": 0.01762903295457363, "Left": 0.5134532451629639, "Top": 0.06735227257013321}, "Polygon": [{"X": 0.5148941874504089, "Y": 0.06735227257013321}, {"X": 0.5615925192832947, "Y": 0.0695008710026741}, {"X": 0.5601538419723511, "Y": 0.08498130738735199}, {"X": 0.5134532451629639, "Y": 0.08283095806837082}]}, "Id": "6eadbbff-d504-4745-a3f5-b7d15050d2d0"}, {"BlockType": "WORD", "Confidence": 99.37340545654297, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05061114579439163, "Height": 0.018027817830443382, "Left": 0.563836932182312, "Top": 0.07026534527540207}, "Polygon": [{"X": 0.5653018355369568, "Y": 0.07026534527540207}, {"X": 0.614448070526123, "Y": 0.07252663373947144}, {"X": 0.6129854917526245, "Y": 0.08829315751791}, {"X": 0.563836932182312, "Y": 0.08602999895811081}]}, "Id": "b0dc9ca1-2d4d-4f0c-b6ff-26d4d10fa557"}, {"BlockType": "WORD", "Confidence": 98.3984375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013311341404914856, "Height": 0.01538959238678217, "Left": 0.6154653429985046, "Top": 0.0734725072979927}, "Polygon": [{"X": 0.6168418526649475, "Y": 0.0734725072979927}, {"X": 0.6287766695022583, "Y": 0.07402167469263077}, {"X": 0.6274007558822632, "Y": 0.0888620987534523}, {"X": 0.6154653429985046, "Y": 0.08831251412630081}]}, "Id": "99745e1b-570d-44ea-9854-a22a61a79f97"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "33505", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10950683802366257, "Height": 0.02962634153664112, "Left": 0.6814160346984863, "Top": 0.06591832637786865}, "Polygon": [{"X": 0.6837007403373718, "Y": 0.06591832637786865}, {"X": 0.7909228801727295, "Y": 0.07084938883781433}, {"X": 0.7886462211608887, "Y": 0.09554466605186462}, {"X": 0.6814160346984863, "Y": 0.09060720354318619}]}, "Id": "8cb7c579-2b51-4e76-8134-422bcc6e36d3"}, {"BlockType": "WORD", "Confidence": 99.75347137451172, "Text": "One", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03878830745816231, "Height": 0.01571197807788849, "Left": 0.25079041719436646, "Top": 0.10801564902067184}, "Polygon": [{"X": 0.2521038055419922, "Y": 0.10801564902067184}, {"X": 0.28957873582839966, "Y": 0.1097443625330925}, {"X": 0.2882669270038605, "Y": 0.12372762709856033}, {"X": 0.25079041719436646, "Y": 0.1219976544380188}]}, "Id": "01e2e352-c7f2-4fd4-bf1d-fa6c23b802cc"}, {"BlockType": "WORD", "Confidence": 99.52327728271484, "Text": "goal.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04412870481610298, "Height": 0.019412172958254814, "Left": 0.29127973318099976, "Top": 0.10557115077972412}, "Polygon": [{"X": 0.2929166257381439, "Y": 0.10557115077972412}, {"X": 0.33540841937065125, "Y": 0.1075308695435524}, {"X": 0.33377379179000854, "Y": 0.12498331815004349}, {"X": 0.29127973318099976, "Y": 0.12302181124687195}]}, "Id": "69f1d576-c131-4ef6-bca6-e52f5af825e4"}, {"BlockType": "WORD", "Confidence": 99.56632995605469, "Text": "Zero", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044220007956027985, "Height": 0.015894819051027298, "Left": 0.33778759837150574, "Top": 0.1051926389336586}, "Polygon": [{"X": 0.3390906751155853, "Y": 0.1051926389336586}, {"X": 0.3820075988769531, "Y": 0.10717172175645828}, {"X": 0.3807063102722168, "Y": 0.1210874617099762}, {"X": 0.33778759837150574, "Y": 0.11910693347454071}]}, "Id": "479149d5-4e27-4b93-9a33-d11c10ca2e0e"}, {"BlockType": "WORD", "Confidence": 99.17669677734375, "Text": "waste.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05713636055588722, "Height": 0.019464217126369476, "Left": 0.38337990641593933, "Top": 0.10190814733505249}, "Polygon": [{"X": 0.3849601447582245, "Y": 0.10190814733505249}, {"X": 0.44051626324653625, "Y": 0.10446939617395401}, {"X": 0.43893885612487793, "Y": 0.12137237191200256}, {"X": 0.38337990641593933, "Y": 0.11880885064601898}]}, "Id": "0ea8cb68-feaf-4c66-a026-7d46084c1c98"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0395708866417408, "Height": 0.018087835982441902, "Left": 0.5838230848312378, "Top": 0.12398533523082733}, "Polygon": [{"X": 0.5853392481803894, "Y": 0.12398533523082733}, {"X": 0.6233939528465271, "Y": 0.12574084103107452}, {"X": 0.6218796372413635, "Y": 0.14207316935062408}, {"X": 0.5838230848312378, "Y": 0.1403161585330963}]}, "Id": "ec3c406a-0907-4906-9756-48133856ca3e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "11/1/22", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1913796067237854, "Height": 0.051251187920570374, "Left": 0.6380326151847839, "Top": 0.10869826376438141}, "Polygon": [{"X": 0.6419793963432312, "Y": 0.10869826376438141}, {"X": 0.8294121623039246, "Y": 0.11733714491128922}, {"X": 0.8254897594451904, "Y": 0.15994945168495178}, {"X": 0.6380326151847839, "Y": 0.15129126608371735}]}, "Id": "b2cef5d9-dd9d-424e-9fbb-2e3a25ab9ca8"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "Customer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08563662320375443, "Height": 0.020685570314526558, "Left": 0.16973777115345, "Top": 0.15110334753990173}, "Polygon": [{"X": 0.17131967842578888, "Y": 0.15110334753990173}, {"X": 0.25537440180778503, "Y": 0.15498970448970795}, {"X": 0.25379678606987, "Y": 0.17178893089294434}, {"X": 0.16973777115345, "Y": 0.16789916157722473}]}, "Id": "78fd5579-b091-4a41-b9b8-0467bebd274a"}, {"BlockType": "WORD", "Confidence": 93.67606353759766, "Text": "Earth", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.15434452891349792, "Height": 0.053915999829769135, "Left": 0.3172253668308258, "Top": 0.14491409063339233}, "Polygon": [{"X": 0.32162633538246155, "Y": 0.14491409063339233}, {"X": 0.47156989574432373, "Y": 0.151842400431633}, {"X": 0.4671904146671295, "Y": 0.19883008301258087}, {"X": 0.3172253668308258, "Y": 0.19188472628593445}]}, "Id": "00b7266a-a53b-4960-8de2-140d590298f8"}, {"BlockType": "WORD", "Confidence": 55.994300842285156, "Text": "Farthwatch", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.35359644889831543, "Height": 0.06723982840776443, "Left": 0.3172905445098877, "Top": 0.14475876092910767}, "Polygon": [{"X": 0.3220764398574829, "Y": 0.14475876092910767}, {"X": 0.6708869934082031, "Y": 0.1608758270740509}, {"X": 0.6661554574966431, "Y": 0.2119985967874527}, {"X": 0.3172905445098877, "Y": 0.19583842158317566}]}, "Id": "2f750378-f04e-40ed-92d8-57fe09a320a8"}, {"BlockType": "WORD", "Confidence": 92.72720336914062, "Text": "watch", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1875910609960556, "Height": 0.04690735787153244, "Left": 0.48127803206443787, "Top": 0.16528162360191345}, "Polygon": [{"X": 0.4848536252975464, "Y": 0.16528162360191345}, {"X": 0.6688690781593323, "Y": 0.1737896203994751}, {"X": 0.6653150320053101, "Y": 0.2121889740228653}, {"X": 0.48127803206443787, "Y": 0.2036638855934143}]}, "Id": "d5170ebd-b119-4737-8a54-78a2b0170fa7"}, {"BlockType": "WORD", "Confidence": 99.57688903808594, "Text": "Driver:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05560598894953728, "Height": 0.019140271469950676, "Left": 0.16501140594482422, "Top": 0.20402459800243378}, "Polygon": [{"X": 0.16657796502113342, "Y": 0.20402459800243378}, {"X": 0.2206173986196518, "Y": 0.2065296769142151}, {"X": 0.21905358135700226, "Y": 0.2231648713350296}, {"X": 0.16501140594482422, "Y": 0.220657616853714}]}, "Id": "fa8e0612-7a53-4e1e-a0e7-9eef29848b87"}, {"BlockType": "WORD", "Confidence": 99.43219757080078, "Text": "Property", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.2320249229669571, "Height": 0.07161736488342285, "Left": 0.28148728609085083, "Top": 0.18859267234802246}, "Polygon": [{"X": 0.2872185707092285, "Y": 0.18859267234802246}, {"X": 0.5135121941566467, "Y": 0.19907207787036896}, {"X": 0.5078230500221252, "Y": 0.2602100372314453}, {"X": 0.28148728609085083, "Y": 0.24969716370105743}]}, "Id": "20d80793-bf68-473b-923d-fe0cbaddf0b8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Preservation", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.26474860310554504, "Height": 0.062119968235492706, "Left": 0.5523871183395386, "Top": 0.2054028958082199}, "Polygon": [{"X": 0.5570364594459534, "Y": 0.2054028958082199}, {"X": 0.817135751247406, "Y": 0.217450350522995}, {"X": 0.8125261068344116, "Y": 0.2675228714942932}, {"X": 0.5523871183395386, "Y": 0.2554439306259155}]}, "Id": "2cb4de53-9186-4189-a6c6-61f6129ae2f3"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Route:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05523088574409485, "Height": 0.019010048359632492, "Left": 0.16024087369441986, "Top": 0.2567090392112732}, "Polygon": [{"X": 0.16179613769054413, "Y": 0.2567090392112732}, {"X": 0.2154717594385147, "Y": 0.25920370221138}, {"X": 0.21391919255256653, "Y": 0.2757190763950348}, {"X": 0.16024087369441986, "Y": 0.27322226762771606}]}, "Id": "b39c7996-6d28-4d62-9c38-170f7601499c"}, {"BlockType": "WORD", "Confidence": 80.92095947265625, "Text": "Truck:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057485513389110565, "Height": 0.01877380535006523, "Left": 0.4683537185192108, "Top": 0.2768334746360779}, "Polygon": [{"X": 0.4698602259159088, "Y": 0.2768334746360779}, {"X": 0.5258392095565796, "Y": 0.279435932636261}, {"X": 0.5243354439735413, "Y": 0.2956072986125946}, {"X": 0.4683537185192108, "Y": 0.2930026650428772}]}, "Id": "c456f714-865c-4f35-b714-49f0960dcd0b"}, {"BlockType": "WORD", "Confidence": 91.09693908691406, "Text": "F2", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05405388027429581, "Height": 0.03658954054117203, "Left": 0.539619505405426, "Top": 0.26808252930641174}, "Polygon": [{"X": 0.5428000092506409, "Y": 0.26808252930641174}, {"X": 0.5936734080314636, "Y": 0.27044621109962463}, {"X": 0.5904982089996338, "Y": 0.3046720623970032}, {"X": 0.539619505405426, "Y": 0.3023041784763336}]}, "Id": "7cddd201-619a-4279-b5d2-83d73750afa6"}, {"BlockType": "WORD", "Confidence": 80.8193130493164, "Text": "/TLI", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17033909261226654, "Height": 0.05449947342276573, "Left": 0.5792840123176575, "Top": 0.2632174491882324}, "Polygon": [{"X": 0.5836246013641357, "Y": 0.2632174491882324}, {"X": 0.7496231198310852, "Y": 0.2709275484085083}, {"X": 0.7453061938285828, "Y": 0.31771692633628845}, {"X": 0.5792840123176575, "Y": 0.30998802185058594}]}, "Id": "638ff975-8bb0-46eb-9e5e-fc96e2f73df6"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "Qty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03200531750917435, "Height": 0.019973745569586754, "Left": 0.1677817851305008, "Top": 0.2826336920261383}, "Polygon": [{"X": 0.16952961683273315, "Y": 0.2826336920261383}, {"X": 0.19978711009025574, "Y": 0.28404170274734497}, {"X": 0.19804097712039948, "Y": 0.3026074469089508}, {"X": 0.1677817851305008, "Y": 0.3011980652809143}]}, "Id": "a8a98c27-d46b-4739-80b7-e0b5995e1375"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09368007630109787, "Height": 0.023432215675711632, "Left": 0.290900856256485, "Top": 0.2905772030353546}, "Polygon": [{"X": 0.2926962375640869, "Y": 0.2905772030353546}, {"X": 0.38458094000816345, "Y": 0.29485347867012024}, {"X": 0.3827908933162689, "Y": 0.3140093982219696}, {"X": 0.290900856256485, "Y": 0.30972886085510254}]}, "Id": "1cd6cd2e-7956-475a-a74d-4f325b6fc6c0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0601443387567997, "Height": 0.021270908415317535, "Left": 0.6144177317619324, "Top": 0.3114752769470215}, "Polygon": [{"X": 0.616136908531189, "Y": 0.3114752769470215}, {"X": 0.6745620369911194, "Y": 0.3141951262950897}, {"X": 0.6728461980819702, "Y": 0.3327461779117584}, {"X": 0.6144177317619324, "Y": 0.33002370595932007}]}, "Id": "190824f9-16da-4b62-b1a6-062c384205f4"}, {"BlockType": "WORD", "Confidence": 99.13006591796875, "Text": "28", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.049481261521577835, "Height": 0.0388055220246315, "Left": 0.14929543435573578, "Top": 0.30834054946899414}, "Polygon": [{"X": 0.15274836122989655, "Y": 0.30834054946899414}, {"X": 0.19877669215202332, "Y": 0.31048521399497986}, {"X": 0.19532889127731323, "Y": 0.34714606404304504}, {"X": 0.14929543435573578, "Y": 0.3449973165988922}]}, "Id": "09553e4b-70e1-4fe8-833d-7dd7aefa99a9"}, {"BlockType": "WORD", "Confidence": 77.59964752197266, "Text": "Teash", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13604560494422913, "Height": 0.05103856325149536, "Left": 0.20224052667617798, "Top": 0.31662917137145996}, "Polygon": [{"X": 0.206460103392601, "Y": 0.31662917137145996}, {"X": 0.3382861316204071, "Y": 0.322773277759552}, {"X": 0.33408457040786743, "Y": 0.3676677346229553}, {"X": 0.20224052667617798, "Y": 0.3615092635154724}]}, "Id": "6a0ce97a-2717-4b25-8676-36a8c5756bcb"}, {"BlockType": "WORD", "Confidence": 91.44392395019531, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10667473077774048, "Height": 0.042726315557956696, "Left": 0.3539275527000427, "Top": 0.32865414023399353}, "Polygon": [{"X": 0.3574729263782501, "Y": 0.32865414023399353}, {"X": 0.4606022834777832, "Y": 0.3334619700908661}, {"X": 0.4570688009262085, "Y": 0.371380478143692}, {"X": 0.3539275527000427, "Y": 0.36656320095062256}]}, "Id": "7902b6a7-93fd-4429-9fef-700d75bd0bbb"}, {"BlockType": "WORD", "Confidence": 91.40386199951172, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021386919543147087, "Height": 0.015685316175222397, "Left": 0.44358256459236145, "Top": 0.4133395552635193}, "Polygon": [{"X": 0.44495725631713867, "Y": 0.4133395552635193}, {"X": 0.4649694859981537, "Y": 0.4142761528491974}, {"X": 0.4635956883430481, "Y": 0.4290248453617096}, {"X": 0.44358256459236145, "Y": 0.4280875325202942}]}, "Id": "e6f8125f-3ab5-42cb-b5a6-ae19f2c7c27d"}, {"BlockType": "WORD", "Confidence": 99.85192108154297, "Text": "33505", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056154537945985794, "Height": 0.019309768453240395, "Left": 0.4723902940750122, "Top": 0.4148845672607422}, "Polygon": [{"X": 0.47395023703575134, "Y": 0.4148845672607422}, {"X": 0.5285448431968689, "Y": 0.417439728975296}, {"X": 0.5269876718521118, "Y": 0.43419432640075684}, {"X": 0.4723902940750122, "Y": 0.4316369593143463}]}, "Id": "4910c451-b2cf-49fd-b57b-862b3a052dd8"}, {"BlockType": "WORD", "Confidence": 99.56791687011719, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05537910386919975, "Height": 0.019032184034585953, "Left": 0.43797382712364197, "Top": 0.4490640163421631}, "Polygon": [{"X": 0.4395124912261963, "Y": 0.4490640163421631}, {"X": 0.4933529198169708, "Y": 0.4515882432460785}, {"X": 0.4918169677257538, "Y": 0.46809619665145874}, {"X": 0.43797382712364197, "Y": 0.4655698239803314}]}, "Id": "4b097862-aa7d-4cab-a556-0a255d31939c"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "32660", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05577570199966431, "Height": 0.018358854576945305, "Left": 0.5328935980796814, "Top": 0.45597630739212036}, "Polygon": [{"X": 0.5343624949455261, "Y": 0.45597630739212036}, {"X": 0.5886693000793457, "Y": 0.4585227072238922}, {"X": 0.5872030258178711, "Y": 0.4743351638317108}, {"X": 0.5328935980796814, "Y": 0.4717867076396942}]}, "Id": "bb36fa66-5e6b-4d79-99cf-7f29b9b2d54d"}, {"BlockType": "WORD", "Confidence": 91.62528228759766, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02172081731259823, "Height": 0.016292551532387733, "Left": 0.5989563465118408, "Top": 0.4605039656162262}, "Polygon": [{"X": 0.6003782749176025, "Y": 0.4605039656162262}, {"X": 0.6206771731376648, "Y": 0.4614558517932892}, {"X": 0.6192562580108643, "Y": 0.47679653763771057}, {"X": 0.5989563465118408, "Y": 0.4758439064025879}]}, "Id": "5026f52c-8de2-4a48-b495-fe3a37f03c00"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "INBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07516255229711533, "Height": 0.019667135551571846, "Left": 0.6296956539154053, "Top": 0.4626483619213104}, "Polygon": [{"X": 0.6311965584754944, "Y": 0.4626483619213104}, {"X": 0.7048582434654236, "Y": 0.466102659702301}, {"X": 0.7033609747886658, "Y": 0.4823154807090759}, {"X": 0.6296956539154053, "Y": 0.4788583219051361}]}, "Id": "ec737940-6197-4708-acda-6050f71adb12"}, {"BlockType": "WORD", "Confidence": 99.67613983154297, "Text": "11/01/2022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10681542009115219, "Height": 0.023151393979787827, "Left": 0.4369559586048126, "Top": 0.4853757917881012}, "Polygon": [{"X": 0.4386533200740814, "Y": 0.4853757917881012}, {"X": 0.5437713861465454, "Y": 0.49031275510787964}, {"X": 0.5420798659324646, "Y": 0.5085271596908569}, {"X": 0.4369559586048126, "Y": 0.5035855770111084}]}, "Id": "a7af72b1-daaf-439f-a440-7a6bf761c8a9"}, {"BlockType": "WORD", "Confidence": 98.7452163696289, "Text": "03:48PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07837523519992828, "Height": 0.021066565066576004, "Left": 0.5499857068061829, "Top": 0.49307307600975037}, "Polygon": [{"X": 0.5516065359115601, "Y": 0.49307307600975037}, {"X": 0.6283609867095947, "Y": 0.49667835235595703}, {"X": 0.6267442107200623, "Y": 0.5141396522521973}, {"X": 0.5499857068061829, "Y": 0.5105311274528503}]}, "Id": "56b94f80-6823-40d0-81ae-59a93efe24f9"}, {"BlockType": "WORD", "Confidence": 79.1161117553711, "Text": "B", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020614156499505043, "Height": 0.015697767958045006, "Left": 0.42649751901626587, "Top": 0.5985692739486694}, "Polygon": [{"X": 0.44711166620254517, "Y": 0.5994776487350464}, {"X": 0.44573405385017395, "Y": 0.6142670512199402}, {"X": 0.42649751901626587, "Y": 0.6133579611778259}, {"X": 0.42787599563598633, "Y": 0.5985692739486694}]}, "Id": "7ae0fe44-5cff-4ab4-bcef-9aa6c611344c"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "33505", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05541754141449928, "Height": 0.019239595159888268, "Left": 0.4553203582763672, "Top": 0.6005008816719055}, "Polygon": [{"X": 0.456874817609787, "Y": 0.6005008816719055}, {"X": 0.5107378959655762, "Y": 0.6030445694923401}, {"X": 0.5091861486434937, "Y": 0.6197404861450195}, {"X": 0.4553203582763672, "Y": 0.6171945929527283}]}, "Id": "71f3534b-9aa9-4d70-b748-d86690df979c"}, {"BlockType": "WORD", "Confidence": 98.26988983154297, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05582673102617264, "Height": 0.01825777254998684, "Left": 0.4196403920650482, "Top": 0.6341406106948853}, "Polygon": [{"X": 0.42110249400138855, "Y": 0.6341406106948853}, {"X": 0.47546711564064026, "Y": 0.636712372303009}, {"X": 0.47400760650634766, "Y": 0.652398407459259}, {"X": 0.4196403920650482, "Y": 0.6498246192932129}]}, "Id": "9808eea1-0665-4bfe-8a86-fa2a969caba8"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "74000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0561499260365963, "Height": 0.018877770751714706, "Left": 0.5148365497589111, "Top": 0.641097366809845}, "Polygon": [{"X": 0.5163500905036926, "Y": 0.641097366809845}, {"X": 0.5709864497184753, "Y": 0.6436822414398193}, {"X": 0.5694755911827087, "Y": 0.6599751114845276}, {"X": 0.5148365497589111, "Y": 0.6573880910873413}]}, "Id": "64623f8c-0df3-4813-aae6-4f53d5f92e47"}, {"BlockType": "WORD", "Confidence": 77.32780456542969, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021543344482779503, "Height": 0.016620201990008354, "Left": 0.5805317163467407, "Top": 0.646020770072937}, "Polygon": [{"X": 0.5819841623306274, "Y": 0.646020770072937}, {"X": 0.6020750403404236, "Y": 0.6469714045524597}, {"X": 0.6006235480308533, "Y": 0.6626409888267517}, {"X": 0.5805317163467407, "Y": 0.6616896390914917}]}, "Id": "8402d061-a3a1-4a79-80f1-c835cb800dbc"}, {"BlockType": "WORD", "Confidence": 92.52279663085938, "Text": "1101A", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1563563197851181, "Height": 0.040600456297397614, "Left": 0.18255515396595, "Top": 0.6339708566665649}, "Polygon": [{"X": 0.18568839132785797, "Y": 0.6339708566665649}, {"X": 0.3389114737510681, "Y": 0.6412228941917419}, {"X": 0.33579379320144653, "Y": 0.6745712757110596}, {"X": 0.18255515396595, "Y": 0.6673068404197693}]}, "Id": "a4be73c9-86ac-43bf-9831-eb86e389284e"}, {"BlockType": "WORD", "Confidence": 99.01228332519531, "Text": "TARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044566377997398376, "Height": 0.018297841772437096, "Left": 0.41869932413101196, "Top": 0.6517813801765442}, "Polygon": [{"X": 0.4202148914337158, "Y": 0.6517813801765442}, {"X": 0.46326568722724915, "Y": 0.6538196206092834}, {"X": 0.4617522358894348, "Y": 0.670079231262207}, {"X": 0.41869932413101196, "Y": 0.6680392622947693}]}, "Id": "c7484d5f-01bf-40c4-b039-72fef6596eb6"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "32660", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05586429685354233, "Height": 0.018709978088736534, "Left": 0.5130075216293335, "Top": 0.6593790650367737}, "Polygon": [{"X": 0.5145065188407898, "Y": 0.6593790650367737}, {"X": 0.5688718557357788, "Y": 0.6619533896446228}, {"X": 0.5673755407333374, "Y": 0.6780890226364136}, {"X": 0.5130075216293335, "Y": 0.6755125522613525}]}, "Id": "bf606849-fa29-479e-9759-166a4278698d"}, {"BlockType": "WORD", "Confidence": 77.65166473388672, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022011063992977142, "Height": 0.01650421880185604, "Left": 0.578853189945221, "Top": 0.6642244458198547}, "Polygon": [{"X": 0.5802927017211914, "Y": 0.6642244458198547}, {"X": 0.6008642315864563, "Y": 0.6651986837387085}, {"X": 0.599425733089447, "Y": 0.6807286739349365}, {"X": 0.578853189945221, "Y": 0.6797537207603455}]}, "Id": "c0626839-d5ee-4483-8777-4a954b58e1b7"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "RECALLED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08852174133062363, "Height": 0.02128244750201702, "Left": 0.6073610186576843, "Top": 0.6666412353515625}, "Polygon": [{"X": 0.6089502573013306, "Y": 0.6666412353515625}, {"X": 0.6958827972412109, "Y": 0.6707582473754883}, {"X": 0.6942980885505676, "Y": 0.6879236698150635}, {"X": 0.6073610186576843, "Y": 0.6838030815124512}]}, "Id": "c6900043-7bbd-47dc-b458-9843c6c73ac3"}, {"BlockType": "WORD", "Confidence": 89.20340728759766, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03476808965206146, "Height": 0.018012573942542076, "Left": 0.4158450961112976, "Top": 0.669256329536438}, "Polygon": [{"X": 0.4173773527145386, "Y": 0.669256329536438}, {"X": 0.4506131708621979, "Y": 0.6708312034606934}, {"X": 0.4490825831890106, "Y": 0.6872689127922058}, {"X": 0.4158450961112976, "Y": 0.6856926679611206}]}, "Id": "fe58cb29-38d1-467d-9eb2-4a092d800932"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "41340", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0559622086584568, "Height": 0.018231097608804703, "Left": 0.5115558505058289, "Top": 0.6780960559844971}, "Polygon": [{"X": 0.5130094885826111, "Y": 0.6780960559844971}, {"X": 0.5675180554389954, "Y": 0.6806795001029968}, {"X": 0.5660669803619385, "Y": 0.6963271498680115}, {"X": 0.5115558505058289, "Y": 0.6937416791915894}]}, "Id": "63d3abab-94f9-4890-9056-9b28c67130c2"}, {"BlockType": "WORD", "Confidence": 93.46520233154297, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0224910918623209, "Height": 0.016755376011133194, "Left": 0.577265202999115, "Top": 0.6824325323104858}, "Polygon": [{"X": 0.5787258744239807, "Y": 0.6824325323104858}, {"X": 0.5997563004493713, "Y": 0.6834293603897095}, {"X": 0.5982966423034668, "Y": 0.6991879343986511}, {"X": 0.577265202999115, "Y": 0.6981903314590454}]}, "Id": "ad07a0d7-7860-4fc7-96c0-a8d72e45721e"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04730019345879555, "Height": 0.01611313968896866, "Left": 0.11617712676525116, "Top": 0.7004227042198181}, "Polygon": [{"X": 0.11748898774385452, "Y": 0.7004227042198181}, {"X": 0.1634773164987564, "Y": 0.7026066184043884}, {"X": 0.16216740012168884, "Y": 0.7165358662605286}, {"X": 0.11617712676525116, "Y": 0.7143504023551941}]}, "Id": "8370056a-5e6b-4027-876c-17371732ecfd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07129188627004623, "Height": 0.020437078550457954, "Left": 0.16441486775875092, "Top": 0.7028967142105103}, "Polygon": [{"X": 0.16602517664432526, "Y": 0.7028967142105103}, {"X": 0.23570674657821655, "Y": 0.7062057852745056}, {"X": 0.23410005867481232, "Y": 0.7233337759971619}, {"X": 0.16441486775875092, "Y": 0.7200218439102173}]}, "Id": "5f7d1661-eb73-4a63-956f-85f7de22b1d7"}, {"BlockType": "WORD", "Confidence": 94.90094757080078, "Text": "11/01/2022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10617637634277344, "Height": 0.023991774767637253, "Left": 0.4160692095756531, "Top": 0.7059049606323242}, "Polygon": [{"X": 0.41784313321113586, "Y": 0.7059049606323242}, {"X": 0.5222455859184265, "Y": 0.7108607888221741}, {"X": 0.5204777121543884, "Y": 0.7298967242240906}, {"X": 0.4160692095756531, "Y": 0.7249361276626587}]}, "Id": "aef386ab-8151-4f0b-ba0d-6258cfd556eb"}, {"BlockType": "WORD", "Confidence": 97.37882995605469, "Text": "05:12PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07887106388807297, "Height": 0.021816598251461983, "Left": 0.5280576944351196, "Top": 0.7147775292396545}, "Polygon": [{"X": 0.5297427177429199, "Y": 0.7147775292396545}, {"X": 0.6069287657737732, "Y": 0.7184420228004456}, {"X": 0.6052480340003967, "Y": 0.7365941405296326}, {"X": 0.5280576944351196, "Y": 0.7329261898994446}]}, "Id": "175459d1-87f3-43bc-a69f-3a8e42092c48"}, {"BlockType": "WORD", "Confidence": 99.33533477783203, "Text": "<PERSON>gh<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09687568247318268, "Height": 0.021393226459622383, "Left": 0.1083679124712944, "Top": 0.7791344523429871}, "Polygon": [{"X": 0.10995476692914963, "Y": 0.7791344523429871}, {"X": 0.20524360239505768, "Y": 0.7836766242980957}, {"X": 0.20366162061691284, "Y": 0.8005276918411255}, {"X": 0.1083679124712944, "Y": 0.7959815859794617}]}, "Id": "5e267ddf-c288-4ab3-b14e-c533b678df30"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07218287885189056, "Height": 0.02042008563876152, "Left": 0.20767472684383392, "Top": 0.785102128982544}, "Polygon": [{"X": 0.20927537977695465, "Y": 0.785102128982544}, {"X": 0.2798576056957245, "Y": 0.7884668111801147}, {"X": 0.27826058864593506, "Y": 0.8055222034454346}, {"X": 0.20767472684383392, "Y": 0.8021546006202698}]}, "Id": "709d46a0-57d8-496b-acfd-774b86e7d22a"}, {"BlockType": "WORD", "Confidence": 23.572425842285156, "Text": "175", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2000887542963028, "Height": 0.09589014947414398, "Left": 0.11242979019880295, "Top": 0.8044121265411377}, "Polygon": [{"X": 0.12059159576892853, "Y": 0.8044121265411377}, {"X": 0.31251853704452515, "Y": 0.8135716319084167}, {"X": 0.3044073283672333, "Y": 0.9003022313117981}, {"X": 0.11242979019880295, "Y": 0.8911024332046509}]}, "Id": "25d93753-67e7-469b-bbc3-d096c050a897"}, {"BlockType": "WORD", "Confidence": 99.619140625, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06282179802656174, "Height": 0.018645158037543297, "Left": 0.449160099029541, "Top": 0.8481684327125549}, "Polygon": [{"X": 0.45062217116355896, "Y": 0.8481684327125549}, {"X": 0.5119819045066833, "Y": 0.8511006236076355}, {"X": 0.5105227828025818, "Y": 0.866813600063324}, {"X": 0.449160099029541, "Y": 0.8638790249824524}]}, "Id": "dc7b8cd9-d583-47dd-a0cb-ded36553a5cf"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035761427134275436, "Height": 0.015208255499601364, "Left": 0.24185137450695038, "Top": 0.8936685919761658}, "Polygon": [{"X": 0.24312174320220947, "Y": 0.8936685919761658}, {"X": 0.2776128053665161, "Y": 0.8953211307525635}, {"X": 0.27634385228157043, "Y": 0.9088768362998962}, {"X": 0.24185137450695038, "Y": 0.907223105430603}]}, "Id": "195f6436-06f9-403a-88a0-b99ba47bd036"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0812196359038353, "Height": 0.01784113049507141, "Left": 0.28005194664001465, "Top": 0.8957430720329285}, "Polygon": [{"X": 0.28136321902275085, "Y": 0.8957430720329285}, {"X": 0.36127156019210815, "Y": 0.8995717763900757}, {"X": 0.35996368527412415, "Y": 0.9135842323303223}, {"X": 0.28005194664001465, "Y": 0.9097527861595154}]}, "Id": "40a5fbfa-cd9e-4c7f-a8bc-92cc3ac38c33"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0608426071703434, "Height": 0.019201437011361122, "Left": 0.3638366460800171, "Top": 0.9008350968360901}, "Polygon": [{"X": 0.36536312103271484, "Y": 0.9008350968360901}, {"X": 0.4246792495250702, "Y": 0.9036772847175598}, {"X": 0.4231557250022888, "Y": 0.9200365543365479}, {"X": 0.3638366460800171, "Y": 0.9171919822692871}]}, "Id": "f6bb4ba8-00be-475c-9f7c-a0437dbe4880"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "Charlestown,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1193779855966568, "Height": 0.023172542452812195, "Left": 0.42735761404037476, "Top": 0.9041922092437744}, "Polygon": [{"X": 0.4289894998073578, "Y": 0.9041922092437744}, {"X": 0.5467355847358704, "Y": 0.9098342061042786}, {"X": 0.5451099872589111, "Y": 0.9273647665977478}, {"X": 0.42735761404037476, "Y": 0.9217177033424377}]}, "Id": "3c72f507-e2be-43a2-8710-6e1e0158f869"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "MA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03266782686114311, "Height": 0.015321288257837296, "Left": 0.5507274270057678, "Top": 0.9120723605155945}, "Polygon": [{"X": 0.5520083904266357, "Y": 0.9120723605155945}, {"X": 0.58339524269104, "Y": 0.9135764837265015}, {"X": 0.582115650177002, "Y": 0.9273936152458191}, {"X": 0.5507274270057678, "Y": 0.9258884787559509}]}, "Id": "97080a09-a3cc-4c0d-916f-dc7fb3e53b14"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "02129", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05833428353071213, "Height": 0.016817444935441017, "Left": 0.5858791470527649, "Top": 0.9145083427429199}, "Polygon": [{"X": 0.5871831178665161, "Y": 0.9145083427429199}, {"X": 0.6442134380340576, "Y": 0.9172413945198059}, {"X": 0.6429118514060974, "Y": 0.9313257932662964}, {"X": 0.5858791470527649, "Y": 0.9285907745361328}]}, "Id": "8081e040-492f-41f8-bdde-95e9273c90ad"}, {"BlockType": "WORD", "Confidence": 97.53826141357422, "Text": "T", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013692655600607395, "Height": 0.013424202799797058, "Left": 0.18717850744724274, "Top": 0.9129101037979126}, "Polygon": [{"X": 0.18838270008563995, "Y": 0.9129101037979126}, {"X": 0.20087115466594696, "Y": 0.9135090708732605}, {"X": 0.19966745376586914, "Y": 0.9263343214988708}, {"X": 0.18717850744724274, "Y": 0.9257349371910095}]}, "Id": "2f2bca58-b897-4491-a832-37472d771849"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "617.241.9998", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12523344159126282, "Height": 0.020338790491223335, "Left": 0.2028692662715912, "Top": 0.914078950881958}, "Polygon": [{"X": 0.20421992242336273, "Y": 0.914078950881958}, {"X": 0.328102707862854, "Y": 0.9200208187103271}, {"X": 0.3267574608325958, "Y": 0.934417724609375}, {"X": 0.2028692662715912, "Y": 0.9284715056419373}]}, "Id": "27319648-5548-4dc3-bcd0-af83d28d9ce4"}, {"BlockType": "WORD", "Confidence": 84.90194702148438, "Text": "F 617.241-0294", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1397259384393692, "Height": 0.02251829393208027, "Left": 0.33633142709732056, "Top": 0.9210979342460632}, "Polygon": [{"X": 0.33781492710113525, "Y": 0.9210979342460632}, {"X": 0.47605735063552856, "Y": 0.9277287125587463}, {"X": 0.4745805263519287, "Y": 0.9436162114143372}, {"X": 0.33633142709732056, "Y": 0.9369800686836243}]}, "Id": "d960a52c-b43b-4c4e-945b-d608537d911d"}, {"BlockType": "WORD", "Confidence": 98.68861389160156, "Text": "www.savethatstuff.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21551135182380676, "Height": 0.02647426910698414, "Left": 0.47835955023765564, "Top": 0.9309037923812866}, "Polygon": [{"X": 0.479865163564682, "Y": 0.9309037923812866}, {"X": 0.6938709020614624, "Y": 0.9411700367927551}, {"X": 0.6923758387565613, "Y": 0.957378089427948}, {"X": 0.47835955023765564, "Y": 0.9471034407615662}]}, "Id": "140bfe86-2443-48ee-921d-b3c8b90910e0"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "cc20d9e0-fabd-408a-abb9-8c4cfa7fdb91", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "cc20d9e0-fabd-408a-abb9-8c4cfa7fdb91", "content-type": "application/x-amz-json-1.1", "content-length": "54579", "date": "Thu, 18 Sep 2025 07:48:13 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c6cfcc80-8b47-4574-a676-523176bdb9ae", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:48:15 GMT", "content-type": "application/json", "content-length": "628", "connection": "keep-alive", "x-amzn-requestid": "c6cfcc80-8b47-4574-a676-523176bdb9ae"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We must classify each page. Only one page provided. We need to check keywords. It says \"Scale Ticket # 33505\" and contains weight info. That matches scale_ticket. So output doc_type scale_ticket for page 1. Use tool call."}}}, {"toolUse": {"toolUseId": "tooluse_Mfsy0sL5RkmKdEhPALq1iw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2265, "outputTokens": 90, "totalTokens": 2355}, "metrics": {"latencyMs": 594}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/b691a8f5_6_scale_ticket_6.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nsave THATStuff:\nScale Ticket #\n33505\nOne goal. Zero waste.\nDate 11/1/22\nCustomer:\nEarth Farthwatch watch\nDriver:\nProperty Preservation\nRoute:\nTruck: F2 /TLI\nQty\nDescription\nWeight\n28\nTeash Bales\nID 33505\nGROSS\n32660 lb INBOUND\n11/01/2022 03:48PM\nB\n33505\nGROSS\n74000 lb\n1101A\nTARE\n32660 lb RECALLED\nNET\n41340 lb\nDriver Signature\n11/01/2022 05:12PM\nWeighmaster Signature\n175\nTOTAL\n200 Terminal Street, Charlestown, MA 02129\nT 617.241.9998 F 617.241-0294 www.savethatstuff.com\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}