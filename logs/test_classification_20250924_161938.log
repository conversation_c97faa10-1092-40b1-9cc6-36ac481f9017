2025-09-24 16:19:38,151 - INFO - Logging initialized. Log file: logs/test_classification_20250924_161938.log
2025-09-24 16:19:38,151 - INFO - 📁 Found 12 files to process
2025-09-24 16:19:38,151 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 16:19:38,151 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 16:19:38,151 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 16:19:38,151 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 16:19:38,151 - INFO - ⬆️ [16:19:38] Uploading: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:40,347 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf -> s3://document-extraction-logistically/temp/76b3354f_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:40,347 - INFO - 🔍 [16:19:40] Starting classification: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:40,348 - INFO - ⬆️ [16:19:40] Uploading: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:40,350 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:40,371 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:40,376 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/76b3354f_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:40,376 - INFO - Processing PDF from S3...
2025-09-24 16:19:40,377 - INFO - Downloading PDF from S3 to /tmp/tmp_bjby4ry/76b3354f_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:42,433 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:19:42,433 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:42,436 - INFO - Splitting PDF 76b3354f_AYEA5J1NILYPMWA7PN4V into 2 pages
2025-09-24 16:19:42,442 - INFO - Split PDF into 2 pages
2025-09-24 16:19:42,442 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:42,442 - INFO - Expected pages: [1, 2]
2025-09-24 16:19:44,698 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf -> s3://document-extraction-logistically/temp/b8a77023_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:44,698 - INFO - 🔍 [16:19:44] Starting classification: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:44,699 - INFO - ⬆️ [16:19:44] Uploading: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:44,700 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:44,718 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:44,723 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b8a77023_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:44,723 - INFO - Processing PDF from S3...
2025-09-24 16:19:44,724 - INFO - Downloading PDF from S3 to /tmp/tmpfdt35dy2/b8a77023_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:45,054 - INFO - Page 2: Extracted 216 characters, 11 lines from 76b3354f_AYEA5J1NILYPMWA7PN4V_9b8081a0_page_002.pdf
2025-09-24 16:19:45,055 - INFO - Successfully processed page 2
2025-09-24 16:19:47,481 - INFO - Page 1: Extracted 2153 characters, 114 lines from 76b3354f_AYEA5J1NILYPMWA7PN4V_9b8081a0_page_001.pdf
2025-09-24 16:19:47,483 - INFO - Successfully processed page 1
2025-09-24 16:19:47,483 - INFO - Combined 2 pages into final text
2025-09-24 16:19:47,483 - INFO - Text validation for 76b3354f_AYEA5J1NILYPMWA7PN4V: 2405 characters, 2 pages
2025-09-24 16:19:47,484 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:47,484 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:19:47,489 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:19:47,489 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:47,492 - INFO - Splitting PDF b8a77023_C3I3XLR18U7J6P1N2LZR into 1 pages
2025-09-24 16:19:47,603 - INFO - Split PDF into 1 pages
2025-09-24 16:19:47,603 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:47,603 - INFO - Expected pages: [1]
2025-09-24 16:19:47,967 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf -> s3://document-extraction-logistically/temp/7463f939_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:47,967 - INFO - 🔍 [16:19:47] Starting classification: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:47,968 - INFO - ⬆️ [16:19:47] Uploading: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:19:47,968 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:47,984 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:47,989 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7463f939_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:47,989 - INFO - Processing PDF from S3...
2025-09-24 16:19:47,989 - INFO - Downloading PDF from S3 to /tmp/tmpxkmk5l3t/7463f939_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:49,632 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf -> s3://document-extraction-logistically/temp/20fd1f4f_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:19:49,633 - INFO - 🔍 [16:19:49] Starting classification: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:19:49,633 - INFO - ⬆️ [16:19:49] Uploading: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:19:49,633 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:49,641 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:49,643 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/20fd1f4f_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:19:49,643 - INFO - Processing PDF from S3...
2025-09-24 16:19:49,644 - INFO - Downloading PDF from S3 to /tmp/tmp75y7p6pa/20fd1f4f_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:19:50,286 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '15d60056-2197-4289-805d-186753c7b9ea', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:49:50 GMT', 'content-type': 'application/json', 'content-length': '1540', 'connection': 'keep-alive', 'x-amzn-requestid': '15d60056-2197-4289-805d-186753c7b9ea'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document is a Sea Freight FCL Delivery Order. The classification list includes "delivery order"? Not explicitly. But maybe "delivery order" is a type of "delivery order" which might be considered a "bill of lading" or "delivery order"? The definitions: bol: Bill of Lading. This has "Sea Freight FCL Delivery Order". It\'s not explicitly a Bill of Lading. However, it includes "Ebl - Express Bill of Lading" and "House Bill of Lading". So it references BOLs. It\'s a delivery order, maybe a shipping order. The best classification: "bol" or maybe "pack_list"? But it\'s a delivery order for FCL (full container load). The doc type likely is "bol" because it\'s a transport document. The doc includes "Ebl" etc. The page 1 includes "Sea Freight FCL Delivery Order" and contains shipment details. So classify as bol.\n\nWe must output using tool classify_logistics_doc_type with array of objects for each page. Must include page_no and doc_type.\n\nWe need to ensure we use correct doc_type values from defined list. "bol" is available. So output:\n\n[ {page_no:1, doc_type:"bol"}, {page_no:2, doc_type:"bol"} ]\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3281, 'outputTokens': 303, 'totalTokens': 3584}, 'metrics': {'latencyMs': 1499}}
2025-09-24 16:19:50,287 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/76b3354f_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:50,593 - INFO - Downloaded PDF size: 1.4 MB
2025-09-24 16:19:50,594 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:50,595 - INFO - Splitting PDF 7463f939_C94JBR3RYYOOM5J2PFUM into 1 pages
2025-09-24 16:19:50,597 - INFO - Split PDF into 1 pages
2025-09-24 16:19:50,597 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:50,598 - INFO - Expected pages: [1]
2025-09-24 16:19:50,630 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg -> s3://document-extraction-logistically/temp/c2178af6_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:19:50,631 - INFO - 🔍 [16:19:50] Starting classification: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:19:50,631 - INFO - ⬆️ [16:19:50] Uploading: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:19:50,633 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:50,651 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:50,656 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c2178af6_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:19:50,657 - INFO - Processing image from S3...
2025-09-24 16:19:51,263 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf -> s3://document-extraction-logistically/temp/f4bf283b_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:19:51,264 - INFO - 🔍 [16:19:51] Starting classification: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:19:51,265 - INFO - ⬆️ [16:19:51] Uploading: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:19:51,266 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:51,286 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:51,291 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f4bf283b_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:19:51,291 - INFO - Processing PDF from S3...
2025-09-24 16:19:51,291 - INFO - Downloading PDF from S3 to /tmp/tmpfmd8abrv/f4bf283b_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:19:51,887 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf -> s3://document-extraction-logistically/temp/ded4e03c_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:19:51,887 - INFO - 🔍 [16:19:51] Starting classification: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:19:51,888 - INFO - ⬆️ [16:19:51] Uploading: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:19:51,890 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:51,912 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:51,916 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ded4e03c_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:19:51,916 - INFO - Processing PDF from S3...
2025-09-24 16:19:51,916 - INFO - Downloading PDF from S3 to /tmp/tmpm64awf0m/ded4e03c_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:19:52,218 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 16:19:52,218 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:52,219 - INFO - Splitting PDF 20fd1f4f_DY3D94HTH1ZH420GMDO6 into 1 pages
2025-09-24 16:19:52,222 - INFO - Split PDF into 1 pages
2025-09-24 16:19:52,222 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:52,222 - INFO - Expected pages: [1]
2025-09-24 16:19:53,066 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:19:53,067 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:53,068 - INFO - Splitting PDF f4bf283b_GJ7Z12W1O0CQO9E9H39H into 2 pages
2025-09-24 16:19:53,079 - INFO - Split PDF into 2 pages
2025-09-24 16:19:53,079 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:53,079 - INFO - Expected pages: [1, 2]
2025-09-24 16:19:53,133 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf -> s3://document-extraction-logistically/temp/86e8027f_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:19:53,133 - INFO - 🔍 [16:19:53] Starting classification: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:19:53,134 - INFO - ⬆️ [16:19:53] Uploading: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:19:53,136 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:53,158 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:53,161 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/86e8027f_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:19:53,161 - INFO - Processing PDF from S3...
2025-09-24 16:19:53,162 - INFO - Downloading PDF from S3 to /tmp/tmpmdzudtax/86e8027f_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:19:53,722 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf -> s3://document-extraction-logistically/temp/543fb1ef_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:19:53,722 - INFO - 🔍 [16:19:53] Starting classification: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:19:53,723 - INFO - ⬆️ [16:19:53] Uploading: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:19:53,725 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:53,749 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:53,754 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/543fb1ef_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:19:53,754 - INFO - Processing PDF from S3...
2025-09-24 16:19:53,755 - INFO - Downloading PDF from S3 to /tmp/tmp1wy21p66/543fb1ef_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:19:53,772 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:19:53,772 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:53,772 - INFO - Splitting PDF ded4e03c_N9XYNNZR54XFORX6RGH2 into 2 pages
2025-09-24 16:19:53,774 - INFO - Split PDF into 2 pages
2025-09-24 16:19:53,774 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:53,774 - INFO - Expected pages: [1, 2]
2025-09-24 16:19:54,233 - INFO - Page 1: Extracted 1847 characters, 114 lines from b8a77023_C3I3XLR18U7J6P1N2LZR_0450604d_page_001.pdf
2025-09-24 16:19:54,233 - INFO - Successfully processed page 1
2025-09-24 16:19:54,233 - INFO - Combined 1 pages into final text
2025-09-24 16:19:54,233 - INFO - Text validation for b8a77023_C3I3XLR18U7J6P1N2LZR: 1864 characters, 1 pages
2025-09-24 16:19:54,234 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:54,234 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:19:54,313 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf -> s3://document-extraction-logistically/temp/5ec81893_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:19:54,313 - INFO - 🔍 [16:19:54] Starting classification: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:19:54,314 - INFO - ⬆️ [16:19:54] Uploading: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:19:54,317 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:54,333 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:54,336 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ec81893_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:19:54,337 - INFO - Processing PDF from S3...
2025-09-24 16:19:54,337 - INFO - Downloading PDF from S3 to /tmp/tmppt028r8_/5ec81893_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:19:54,626 - INFO - S3 Image temp/c2178af6_G7K3JPMT2OXZWEI7RRNQ.jpg: Extracted 2237 characters, 96 lines
2025-09-24 16:19:54,626 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:54,626 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:19:55,640 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:19:55,640 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:55,642 - INFO - Splitting PDF 543fb1ef_ODBS2NBTU6S3GF9JVE6V into 1 pages
2025-09-24 16:19:55,657 - INFO - Split PDF into 1 pages
2025-09-24 16:19:55,657 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:55,657 - INFO - Expected pages: [1]
2025-09-24 16:19:55,868 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:19:55,868 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:55,869 - INFO - Splitting PDF 86e8027f_NFG5QQXEN21TEZZ9SYPW into 2 pages
2025-09-24 16:19:55,875 - INFO - Split PDF into 2 pages
2025-09-24 16:19:55,875 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:55,875 - INFO - Expected pages: [1, 2]
2025-09-24 16:19:55,894 - INFO - Page 2: Extracted 0 characters, 0 lines from f4bf283b_GJ7Z12W1O0CQO9E9H39H_887ef94b_page_002.pdf
2025-09-24 16:19:55,895 - INFO - Successfully processed page 2
2025-09-24 16:19:55,926 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '27a41b7a-a468-4a7b-bfd2-60b3ef1f1935', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:49:55 GMT', 'content-type': 'application/json', 'content-length': '549', 'connection': 'keep-alive', 'x-amzn-requestid': '27a41b7a-a468-4a7b-bfd2-60b3ef1f1935'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only page1. It has "Bill of Lading" header. So doc_type = bol. No continuation. Output JSON with documents array. Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_dINDu2B8RGyWH1OSQgr3LA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3060, 'outputTokens': 55, 'totalTokens': 3115}, 'metrics': {'latencyMs': 523}}
2025-09-24 16:19:55,926 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b8a77023_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:56,186 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:19:56,187 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:56,188 - INFO - Splitting PDF 5ec81893_SMX8DOTQ89U191SMP0TO into 1 pages
2025-09-24 16:19:56,189 - INFO - Split PDF into 1 pages
2025-09-24 16:19:56,189 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:56,190 - INFO - Expected pages: [1]
2025-09-24 16:19:56,393 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg -> s3://document-extraction-logistically/temp/fcd2c6ff_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:19:56,394 - INFO - 🔍 [16:19:56] Starting classification: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:19:56,394 - INFO - ⬆️ [16:19:56] Uploading: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:19:56,395 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:56,410 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:56,413 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fcd2c6ff_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:19:56,414 - INFO - Processing image from S3...
2025-09-24 16:19:56,660 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '24c1bc7c-a2db-4485-8bc2-98f556d9792c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:49:56 GMT', 'content-type': 'application/json', 'content-length': '768', 'connection': 'keep-alive', 'x-amzn-requestid': '24c1bc7c-a2db-4485-8bc2-98f556d9792c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to decide doc_type. The content: "DELIVERY ORDER" header. This is likely a Bill of Lading or Delivery Order. In definitions: bol is Bill of Lading or Delivery Order. The header says DELIVERY ORDER. So this is a bol. Not an invoice. It\'s a delivery order, a type of bill of lading. So doc_type = bol.\n\nWe must output via tool call. Provide array with single object: page_no:1, doc_type:"bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3226, 'outputTokens': 129, 'totalTokens': 3355}, 'metrics': {'latencyMs': 837}}
2025-09-24 16:19:56,661 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c2178af6_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:19:56,972 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf -> s3://document-extraction-logistically/temp/e3f46bf0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:19:56,972 - INFO - 🔍 [16:19:56] Starting classification: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:19:56,973 - INFO - Initializing TextractProcessor...
2025-09-24 16:19:57,032 - INFO - Initializing BedrockProcessor...
2025-09-24 16:19:57,034 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e3f46bf0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:19:57,035 - INFO - Processing PDF from S3...
2025-09-24 16:19:57,035 - INFO - Downloading PDF from S3 to /tmp/tmph3070b5f/e3f46bf0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:19:57,055 - INFO - 

AYEA5J1NILYPMWA7PN4V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:19:57,056 - INFO - 

✓ Saved result: output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:19:57,223 - INFO - Page 1: Extracted 3353 characters, 164 lines from 7463f939_C94JBR3RYYOOM5J2PFUM_f85d2b3c_page_001.pdf
2025-09-24 16:19:57,224 - INFO - Successfully processed page 1
2025-09-24 16:19:57,224 - INFO - Combined 1 pages into final text
2025-09-24 16:19:57,224 - INFO - Text validation for 7463f939_C94JBR3RYYOOM5J2PFUM: 3370 characters, 1 pages
2025-09-24 16:19:57,224 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:57,224 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:19:57,334 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/76b3354f_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:19:57,353 - INFO - 

C3I3XLR18U7J6P1N2LZR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:19:57,353 - INFO - 

✓ Saved result: output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:19:57,625 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b8a77023_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:19:57,644 - INFO - 

G7K3JPMT2OXZWEI7RRNQ.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:19:57,644 - INFO - 

✓ Saved result: output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:19:57,918 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c2178af6_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:19:58,596 - INFO - Page 1: Extracted 1185 characters, 52 lines from 20fd1f4f_DY3D94HTH1ZH420GMDO6_37697e56_page_001.pdf
2025-09-24 16:19:58,596 - INFO - Successfully processed page 1
2025-09-24 16:19:58,597 - INFO - Combined 1 pages into final text
2025-09-24 16:19:58,597 - INFO - Text validation for 20fd1f4f_DY3D94HTH1ZH420GMDO6: 1202 characters, 1 pages
2025-09-24 16:19:58,598 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:58,598 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:19:58,668 - INFO - Page 1: Extracted 4034 characters, 142 lines from f4bf283b_GJ7Z12W1O0CQO9E9H39H_887ef94b_page_001.pdf
2025-09-24 16:19:58,669 - INFO - Successfully processed page 1
2025-09-24 16:19:58,669 - INFO - Combined 2 pages into final text
2025-09-24 16:19:58,669 - INFO - Text validation for f4bf283b_GJ7Z12W1O0CQO9E9H39H: 4069 characters, 2 pages
2025-09-24 16:19:58,669 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:58,669 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:19:58,718 - INFO - Page 2: Extracted 1932 characters, 48 lines from ded4e03c_N9XYNNZR54XFORX6RGH2_b0a42110_page_002.pdf
2025-09-24 16:19:58,718 - INFO - Successfully processed page 2
2025-09-24 16:19:59,012 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 16:19:59,012 - INFO - Splitting PDF into individual pages...
2025-09-24 16:19:59,013 - INFO - Splitting PDF e3f46bf0_Z10BLMPIZ96XLUZ5NUPF into 1 pages
2025-09-24 16:19:59,078 - INFO - Split PDF into 1 pages
2025-09-24 16:19:59,078 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:19:59,078 - INFO - Expected pages: [1]
2025-09-24 16:19:59,384 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '082f3ac6-6c05-497b-ad84-b01094e53243', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:49:59 GMT', 'content-type': 'application/json', 'content-length': '1077', 'connection': 'keep-alive', 'x-amzn-requestid': '082f3ac6-6c05-497b-ad84-b01094e53243'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1 provided. We need to determine doc_type. Let\'s examine. The content looks like an Air Waybill for air freight. Keywords: Air Waybill, Shipper, Consignee, declared value, etc. This is a commercial air waybill, a type of Bill of Lading for air transport. According to definitions, this is a "bol" (Bill of Lading). Air Waybill is a type of BOL. So doc_type: bol. Also might be considered commercial invoice? But it\'s an Air Waybill, not an invoice. So bol.\n\nCheck if it\'s continuation? No.\n\nThus output: page_no 1, doc_type bol.\n\nWe must call classify_logistics_doc_type with documents array: [{page_no:1, doc_type:"bol"}]'}}}, {'toolUse': {'toolUseId': 'tooluse_T1XM6s1TTqO8XWaWoBpECg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3445, 'outputTokens': 184, 'totalTokens': 3629}, 'metrics': {'latencyMs': 936}}
2025-09-24 16:19:59,384 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7463f939_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:59,438 - INFO - 

C94JBR3RYYOOM5J2PFUM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:19:59,438 - INFO - 

✓ Saved result: output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:19:59,718 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7463f939_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:19:59,744 - INFO - Page 1: Extracted 1616 characters, 146 lines from ded4e03c_N9XYNNZR54XFORX6RGH2_b0a42110_page_001.pdf
2025-09-24 16:19:59,744 - INFO - Successfully processed page 1
2025-09-24 16:19:59,744 - INFO - Combined 2 pages into final text
2025-09-24 16:19:59,744 - INFO - Text validation for ded4e03c_N9XYNNZR54XFORX6RGH2: 3584 characters, 2 pages
2025-09-24 16:19:59,744 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:19:59,744 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:20:00,106 - INFO - S3 Image temp/fcd2c6ff_Y7P2IPB0S079FZE11EEA.jpg: Extracted 821 characters, 52 lines
2025-09-24 16:20:00,106 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:20:00,106 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:20:00,386 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '183802cb-7827-4a08-a518-43b683d75115', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:00 GMT', 'content-type': 'application/json', 'content-length': '630', 'connection': 'keep-alive', 'x-amzn-requestid': '183802cb-7827-4a08-a518-43b683d75115'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. It contains a Bill of Lading. Keywords: "BILL OF LADING". So doc_type = bol. No continuation. So output an array of one object: page_no 1, doc_type "bol". Let\'s use the function.'}}}, {'toolUse': {'toolUseId': 'tooluse_7E92fB6KTYCBScQMgVXSKA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2789, 'outputTokens': 79, 'totalTokens': 2868}, 'metrics': {'latencyMs': 623}}
2025-09-24 16:20:00,386 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/20fd1f4f_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:20:00,417 - INFO - 

DY3D94HTH1ZH420GMDO6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:20:00,417 - INFO - 

✓ Saved result: output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:20:00,672 - INFO - Page 1: Extracted 2057 characters, 76 lines from 543fb1ef_ODBS2NBTU6S3GF9JVE6V_7f85a3ae_page_001.pdf
2025-09-24 16:20:00,672 - INFO - Successfully processed page 1
2025-09-24 16:20:00,672 - INFO - Combined 1 pages into final text
2025-09-24 16:20:00,672 - INFO - Text validation for 543fb1ef_ODBS2NBTU6S3GF9JVE6V: 2074 characters, 1 pages
2025-09-24 16:20:00,672 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:20:00,672 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:20:00,692 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/20fd1f4f_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:20:01,070 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ed592c9f-3242-4628-ab7c-7031136f639a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:00 GMT', 'content-type': 'application/json', 'content-length': '1148', 'connection': 'keep-alive', 'x-amzn-requestid': 'ed592c9f-3242-4628-ab7c-7031136f639a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 appears to be a Bill of Lading. Keywords: "Conocimiento de embarque recto" (Spanish for Bill of Lading), "BILL OF LADING #", etc. So doc_type: bol. Page2 is empty? It says <page2> </page2>. Probably empty page, but still part of same document? The instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." So page2 is part of same Bill of Lading. However, we need to check if page 2 indicates continuation. It is empty; but likely continuation. There\'s no content. Probably still same doc_type. The tool expects array of objects: each with page_no and doc_type. So we output:\n\n[{page_no:1, doc_type:"bol"}, {page_no:2, doc_type:"bol"}].\n\nWe must use the tool in JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3585, 'outputTokens': 231, 'totalTokens': 3816}, 'metrics': {'latencyMs': 1213}}
2025-09-24 16:20:01,070 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f4bf283b_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:20:01,103 - INFO - 

GJ7Z12W1O0CQO9E9H39H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:20:01,103 - INFO - 

✓ Saved result: output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:20:01,380 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f4bf283b_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:20:01,737 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ca1e67fe-5750-4953-962f-757a6c5d58ff', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:01 GMT', 'content-type': 'application/json', 'content-length': '725', 'connection': 'keep-alive', 'x-amzn-requestid': 'ca1e67fe-5750-4953-962f-757a6c5d58ff'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages content. The first page clearly shows "BILL OF LADING" header, with details. That is a BOL. So doc_type = "bol". The second page seems to be the continuation of the BOL. It includes signatures, etc. It is part of the same BOL. So page2 also bol. No other different doc. So output two objects: page_no 1 doc_type bol, page_no 2 doc_type bol. Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3557, 'outputTokens': 127, 'totalTokens': 3684}, 'metrics': {'latencyMs': 705}}
2025-09-24 16:20:01,737 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ded4e03c_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:20:01,760 - INFO - Page 2: Extracted 1028 characters, 92 lines from 86e8027f_NFG5QQXEN21TEZZ9SYPW_ba2144b0_page_002.pdf
2025-09-24 16:20:01,761 - INFO - Successfully processed page 2
2025-09-24 16:20:01,781 - INFO - 

N9XYNNZR54XFORX6RGH2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:20:01,782 - INFO - 

✓ Saved result: output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:20:02,033 - INFO - Page 1: Extracted 2916 characters, 126 lines from 5ec81893_SMX8DOTQ89U191SMP0TO_d5f541d6_page_001.pdf
2025-09-24 16:20:02,033 - INFO - Successfully processed page 1
2025-09-24 16:20:02,033 - INFO - Combined 1 pages into final text
2025-09-24 16:20:02,034 - INFO - Text validation for 5ec81893_SMX8DOTQ89U191SMP0TO: 2933 characters, 1 pages
2025-09-24 16:20:02,034 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:20:02,034 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:20:02,060 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ded4e03c_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:20:02,087 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ffc97b66-b5d6-4478-8399-fc8e7d0e4d6c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:01 GMT', 'content-type': 'application/json', 'content-length': '774', 'connection': 'keep-alive', 'x-amzn-requestid': 'ffc97b66-b5d6-4478-8399-fc8e7d0e4d6c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to output via the tool classify_logistics_doc_type. We need to produce array of objects: each has page_no and doc_type. The doc_type should be one of the enumerated. For page1, we see "BILL OF LADING" header. So doc_type should be "bol". Also we check if it\'s a continuation? It doesn\'t start with "continued". So just bol.\n\nReturn JSON array: [{page_no:1, doc_type:"bol"}].\n\nMake tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2759, 'outputTokens': 131, 'totalTokens': 2890}, 'metrics': {'latencyMs': 781}}
2025-09-24 16:20:02,087 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fcd2c6ff_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:20:02,097 - INFO - 

Y7P2IPB0S079FZE11EEA.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:20:02,097 - INFO - 

✓ Saved result: output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:20:02,243 - INFO - Page 1: Extracted 1621 characters, 166 lines from 86e8027f_NFG5QQXEN21TEZZ9SYPW_ba2144b0_page_001.pdf
2025-09-24 16:20:02,243 - INFO - Successfully processed page 1
2025-09-24 16:20:02,244 - INFO - Combined 2 pages into final text
2025-09-24 16:20:02,244 - INFO - Text validation for 86e8027f_NFG5QQXEN21TEZZ9SYPW: 2685 characters, 2 pages
2025-09-24 16:20:02,244 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:20:02,245 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:20:02,391 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fcd2c6ff_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:20:03,867 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a9e45ff0-f629-4de5-952a-426ea42930f6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:03 GMT', 'content-type': 'application/json', 'content-length': '570', 'connection': 'keep-alive', 'x-amzn-requestid': 'a9e45ff0-f629-4de5-952a-426ea42930f6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify. The text includes "VICs Bill of Lading - Order", "Bill Of Lading Number", "SHIP FROM", "SHIP TO", etc. This is a Bill of Lading. So doc_type: bol. Return array with one object: page_no 1, doc_type "bol". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3258, 'outputTokens': 90, 'totalTokens': 3348}, 'metrics': {'latencyMs': 615}}
2025-09-24 16:20:03,868 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ec81893_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:20:03,904 - INFO - 

SMX8DOTQ89U191SMP0TO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:20:03,904 - INFO - 

✓ Saved result: output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:20:04,214 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ec81893_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:20:04,218 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cce863e9-5343-4d2c-ba94-f554dc72f756', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:04 GMT', 'content-type': 'application/json', 'content-length': '2539', 'connection': 'keep-alive', 'x-amzn-requestid': 'cce863e9-5343-4d2c-ba94-f554dc72f756'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Need to identify document type. The page content: "MIDWEST" etc. It\'s a Load Confirmation. The description says "Load Confirmation # 1536427" and mentions "Load Confirmation". This is likely a "tender_from_cust" or maybe "load confirmation"? Not in list. The list includes tender_from_cust for Load Tender from Customer. But this is a Load Confirmation document, more like a "Load Confirmation" from carrier to customer? Let\'s analyze keywords: "Load Confirmation" appears, but not "Load Tender". The doc type list: "tender_from_cust" is for load tender from customer. This is not a tender. It\'s a confirmation of load. It could be "so_confirmation"? That\'s sales order confirmation. This is not a sales order. "po" is purchase order. Not that. "bol" Bill of Lading? No. "pod" proof of delivery? No. "rate_confirmation" maybe? The page includes "Agreed Rate" and "Print Name Signature Date" to confirm. It says "In order to receive payment for this load, this confirmation must be signed and returned" and "Load Confirmation # must be referenced on your invoice." This is more like a "rate confirmation" because it confirms the rate and terms? The definition of rate_confirmation: "Agreement from a carrier confirming rate/terms for a specific load." That seems to match: "In order to receive payment for this load, this confirmation must be signed and returned." It also includes "Agreed Rate" etc. So likely "rate_confirmation". But also "tender_from_cust" includes "Load Tender" not "Load Confirmation". So "rate_confirmation" seems correct.\n\nCheck if any other type matches: "combined_carrier_documents" no, only one doc. "invoice" no invoice. "comm_invoice" no. "po" no. "cust_rate_confirmation" for customer rate confirmation, not relevant. "clear_to_pay" no. "scale_ticket" no. "log" no. "fuel_receipt" no. "pack_list" no. "customs_doc" no. "weight_and_inspection_cert" no. "inspection_cert" no. "nmfc_cert" no. "other" maybe but we can assign rate_confirmation.\n\nThus output: page 1 with doc_type "rate_confirmation". Provide JSON array with one object. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3046, 'outputTokens': 520, 'totalTokens': 3566}, 'metrics': {'latencyMs': 2331}}
2025-09-24 16:20:04,219 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/543fb1ef_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:20:04,250 - INFO - 

ODBS2NBTU6S3GF9JVE6V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 16:20:04,250 - INFO - 

✓ Saved result: output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:20:04,431 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '917afcf7-314c-4b78-80a5-b8fe600c0088', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:04 GMT', 'content-type': 'application/json', 'content-length': '977', 'connection': 'keep-alive', 'x-amzn-requestid': '917afcf7-314c-4b78-80a5-b8fe600c0088'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. They look like a packing list (PKG LIST) with details. It\'s a multi-page document: Page 1 and Page 2. Likely the document type is \'pack_list\'. Check keywords: "PKG LIST" appears. Also "CONTAINER SUMMARY" etc. So pack_list.\n\nWe must output via the tool: classify_logistics_doc_type with documents array of objects each page. Provide page_no and doc_type. For page 2, check if continuation: page 2 has similar header. Yes it\'s continuation of same packing list. So same doc_type.\n\nThus output: documents: [{page_no:1, doc_type:"pack_list"}, {page_no:2, doc_type:"pack_list"}]. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3738, 'outputTokens': 183, 'totalTokens': 3921}, 'metrics': {'latencyMs': 1050}}
2025-09-24 16:20:04,431 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/86e8027f_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:20:04,546 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/543fb1ef_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:20:04,574 - INFO - 

NFG5QQXEN21TEZZ9SYPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:20:04,574 - INFO - 

✓ Saved result: output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:20:04,891 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/86e8027f_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:20:05,237 - INFO - Page 1: Extracted 3550 characters, 185 lines from e3f46bf0_Z10BLMPIZ96XLUZ5NUPF_99fac099_page_001.pdf
2025-09-24 16:20:05,237 - INFO - Successfully processed page 1
2025-09-24 16:20:05,237 - INFO - Combined 1 pages into final text
2025-09-24 16:20:05,238 - INFO - Text validation for e3f46bf0_Z10BLMPIZ96XLUZ5NUPF: 3567 characters, 1 pages
2025-09-24 16:20:05,238 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:20:05,238 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:20:07,248 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e7636ca3-b263-4991-894f-cdcf2107967e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:50:07 GMT', 'content-type': 'application/json', 'content-length': '659', 'connection': 'keep-alive', 'x-amzn-requestid': 'e7636ca3-b263-4991-894f-cdcf2107967e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have only page1. We need to classify. This appears to be a Bill of Lading. Contains header "BILL OF LADING" and details. So doc_type: bol. No continuation markers. So output a JSON array? The tool requires a single call with documents array. Each element has page_no and doc_type. Since only one page, need one object.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3730, 'outputTokens': 99, 'totalTokens': 3829}, 'metrics': {'latencyMs': 730}}
2025-09-24 16:20:07,248 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e3f46bf0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:20:07,294 - INFO - 

Z10BLMPIZ96XLUZ5NUPF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:20:07,294 - INFO - 

✓ Saved result: output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:20:07,607 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e3f46bf0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:20:07,609 - INFO - 
📊 Processing Summary:
2025-09-24 16:20:07,609 - INFO -    Total files: 12
2025-09-24 16:20:07,610 - INFO -    Successful: 12
2025-09-24 16:20:07,610 - INFO -    Failed: 0
2025-09-24 16:20:07,610 - INFO -    Duration: 29.46 seconds
2025-09-24 16:20:07,610 - INFO -    Output directory: output
2025-09-24 16:20:07,610 - INFO - 
📋 Successfully Processed Files:
2025-09-24 16:20:07,610 - INFO -    📄 AYEA5J1NILYPMWA7PN4V.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 C3I3XLR18U7J6P1N2LZR.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 C94JBR3RYYOOM5J2PFUM.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 DY3D94HTH1ZH420GMDO6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 G7K3JPMT2OXZWEI7RRNQ.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 GJ7Z12W1O0CQO9E9H39H.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 N9XYNNZR54XFORX6RGH2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:20:07,610 - INFO -    📄 NFG5QQXEN21TEZZ9SYPW.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-24 16:20:07,611 - INFO -    📄 ODBS2NBTU6S3GF9JVE6V.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-24 16:20:07,611 - INFO -    📄 SMX8DOTQ89U191SMP0TO.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,611 - INFO -    📄 Y7P2IPB0S079FZE11EEA.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,611 - INFO -    📄 Z10BLMPIZ96XLUZ5NUPF.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:20:07,611 - INFO - 
============================================================================================================================================
2025-09-24 16:20:07,611 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 16:20:07,612 - INFO - ============================================================================================================================================
2025-09-24 16:20:07,612 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 16:20:07,612 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:20:07,612 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           1      bol                                      run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:20:07,612 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:20:07,612 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:20:07,612 - INFO - 
2025-09-24 16:20:07,612 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           2      bol                                      run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:20:07,612 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:20:07,612 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:20:07,612 - INFO - 
2025-09-24 16:20:07,612 - INFO - C3I3XLR18U7J6P1N2LZR.pdf                           1      bol                                      run1_C3I3XLR18U7J6P1N2LZR.json                                                  
2025-09-24 16:20:07,612 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:20:07,612 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:20:07,612 - INFO - 
2025-09-24 16:20:07,612 - INFO - C94JBR3RYYOOM5J2PFUM.pdf                           1      bol                                      run1_C94JBR3RYYOOM5J2PFUM.json                                                  
2025-09-24 16:20:07,612 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:20:07,612 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:20:07,613 - INFO - 
2025-09-24 16:20:07,613 - INFO - DY3D94HTH1ZH420GMDO6.pdf                           1      bol                                      run1_DY3D94HTH1ZH420GMDO6.json                                                  
2025-09-24 16:20:07,613 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:20:07,613 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:20:07,613 - INFO - 
2025-09-24 16:20:07,613 - INFO - G7K3JPMT2OXZWEI7RRNQ.jpg                           1      bol                                      run1_G7K3JPMT2OXZWEI7RRNQ.json                                                  
2025-09-24 16:20:07,613 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:20:07,613 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:20:07,613 - INFO - 
2025-09-24 16:20:07,613 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           1      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:20:07,613 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:20:07,613 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:20:07,613 - INFO - 
2025-09-24 16:20:07,613 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           2      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:20:07,613 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:20:07,613 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:20:07,614 - INFO - 
2025-09-24 16:20:07,614 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           1      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:20:07,614 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:20:07,614 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:20:07,614 - INFO - 
2025-09-24 16:20:07,614 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           2      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:20:07,614 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:20:07,614 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:20:07,614 - INFO - 
2025-09-24 16:20:07,614 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           1      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:20:07,614 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:20:07,614 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:20:07,614 - INFO - 
2025-09-24 16:20:07,614 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           2      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:20:07,614 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:20:07,614 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:20:07,614 - INFO - 
2025-09-24 16:20:07,614 - INFO - ODBS2NBTU6S3GF9JVE6V.pdf                           1      rate_confirmation                        run1_ODBS2NBTU6S3GF9JVE6V.json                                                  
2025-09-24 16:20:07,614 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:20:07,614 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:20:07,614 - INFO - 
2025-09-24 16:20:07,614 - INFO - SMX8DOTQ89U191SMP0TO.pdf                           1      bol                                      run1_SMX8DOTQ89U191SMP0TO.json                                                  
2025-09-24 16:20:07,614 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:20:07,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:20:07,615 - INFO - 
2025-09-24 16:20:07,615 - INFO - Y7P2IPB0S079FZE11EEA.jpg                           1      bol                                      run1_Y7P2IPB0S079FZE11EEA.json                                                  
2025-09-24 16:20:07,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:20:07,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:20:07,615 - INFO - 
2025-09-24 16:20:07,615 - INFO - Z10BLMPIZ96XLUZ5NUPF.pdf                           1      bol                                      run1_Z10BLMPIZ96XLUZ5NUPF.json                                                  
2025-09-24 16:20:07,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:20:07,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:20:07,615 - INFO - 
2025-09-24 16:20:07,615 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:20:07,615 - INFO - Total entries: 16
2025-09-24 16:20:07,615 - INFO - ============================================================================================================================================
2025-09-24 16:20:07,615 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 16:20:07,615 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:20:07,615 - INFO -   1. AYEA5J1NILYPMWA7PN4V.pdf            Page 1   → bol             | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:20:07,615 - INFO -   2. AYEA5J1NILYPMWA7PN4V.pdf            Page 2   → bol             | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:20:07,615 - INFO -   3. C3I3XLR18U7J6P1N2LZR.pdf            Page 1   → bol             | run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:20:07,615 - INFO -   4. C94JBR3RYYOOM5J2PFUM.pdf            Page 1   → bol             | run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:20:07,615 - INFO -   5. DY3D94HTH1ZH420GMDO6.pdf            Page 1   → bol             | run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:20:07,615 - INFO -   6. G7K3JPMT2OXZWEI7RRNQ.jpg            Page 1   → bol             | run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:20:07,615 - INFO -   7. GJ7Z12W1O0CQO9E9H39H.pdf            Page 1   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:20:07,615 - INFO -   8. GJ7Z12W1O0CQO9E9H39H.pdf            Page 2   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:20:07,616 - INFO -   9. N9XYNNZR54XFORX6RGH2.pdf            Page 1   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:20:07,616 - INFO -  10. N9XYNNZR54XFORX6RGH2.pdf            Page 2   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:20:07,616 - INFO -  11. NFG5QQXEN21TEZZ9SYPW.pdf            Page 1   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:20:07,616 - INFO -  12. NFG5QQXEN21TEZZ9SYPW.pdf            Page 2   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:20:07,616 - INFO -  13. ODBS2NBTU6S3GF9JVE6V.pdf            Page 1   → rate_confirmation | run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:20:07,616 - INFO -  14. SMX8DOTQ89U191SMP0TO.pdf            Page 1   → bol             | run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:20:07,616 - INFO -  15. Y7P2IPB0S079FZE11EEA.jpg            Page 1   → bol             | run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:20:07,616 - INFO -  16. Z10BLMPIZ96XLUZ5NUPF.pdf            Page 1   → bol             | run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:20:07,616 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:20:07,616 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 29.458095, 'processed_files': [{'filename': 'AYEA5J1NILYPMWA7PN4V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf'}, {'filename': 'C3I3XLR18U7J6P1N2LZR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf'}, {'filename': 'C94JBR3RYYOOM5J2PFUM.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf'}, {'filename': 'DY3D94HTH1ZH420GMDO6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf'}, {'filename': 'G7K3JPMT2OXZWEI7RRNQ.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg'}, {'filename': 'GJ7Z12W1O0CQO9E9H39H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf'}, {'filename': 'N9XYNNZR54XFORX6RGH2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf'}, {'filename': 'NFG5QQXEN21TEZZ9SYPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf'}, {'filename': 'ODBS2NBTU6S3GF9JVE6V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf'}, {'filename': 'SMX8DOTQ89U191SMP0TO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf'}, {'filename': 'Y7P2IPB0S079FZE11EEA.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg'}, {'filename': 'Z10BLMPIZ96XLUZ5NUPF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf'}]}
