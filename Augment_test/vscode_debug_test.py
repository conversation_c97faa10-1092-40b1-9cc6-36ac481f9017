#!/usr/bin/env python3
"""
VS Code Debug Integration Test

This script tests the VS Code debug configuration by simulating
the debugging workflow and verifying all components work together.
"""

import sys
import os
import json
from pathlib import Path


def test_vscode_configuration():
    """Test VS Code configuration files."""
    print("🧪 Testing VS Code Configuration Files")
    print("=" * 50)
    
    workspace_root = Path(__file__).parent.parent
    vscode_dir = workspace_root / ".vscode"
    
    # Check if .vscode directory exists
    if vscode_dir.exists():
        print(f"✅ .vscode directory exists: {vscode_dir}")
    else:
        print(f"❌ .vscode directory missing: {vscode_dir}")
        return False
    
    # Check launch.json
    launch_json = vscode_dir / "launch.json"
    if launch_json.exists():
        print(f"✅ launch.json exists: {launch_json}")
        try:
            with open(launch_json, 'r') as f:
                launch_config = json.load(f)
            
            configurations = launch_config.get('configurations', [])
            print(f"✅ Found {len(configurations)} debug configurations:")
            for i, config in enumerate(configurations, 1):
                name = config.get('name', 'Unknown')
                program = config.get('program', 'Unknown')
                print(f"   {i}. {name}")
                print(f"      Program: {program}")
                
                # Check if program file exists
                if program.startswith('${workspaceFolder}'):
                    actual_program = program.replace('${workspaceFolder}', str(workspace_root))
                    if os.path.exists(actual_program):
                        print(f"      ✅ Program file exists")
                    else:
                        print(f"      ❌ Program file missing: {actual_program}")
                
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in launch.json: {e}")
            return False
    else:
        print(f"❌ launch.json missing: {launch_json}")
        return False
    
    # Check settings.json
    settings_json = vscode_dir / "settings.json"
    if settings_json.exists():
        print(f"✅ settings.json exists: {settings_json}")
        try:
            with open(settings_json, 'r') as f:
                settings = json.load(f)
            
            # Check key settings
            python_path = settings.get('python.defaultInterpreterPath', '')
            if python_path:
                print(f"✅ Python interpreter configured: {python_path}")
            
            extra_paths = settings.get('python.analysis.extraPaths', [])
            if extra_paths:
                print(f"✅ Extra paths configured: {len(extra_paths)} paths")
                for path in extra_paths:
                    print(f"      - {path}")
            
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in settings.json: {e}")
    else:
        print(f"⚠ settings.json missing (optional): {settings_json}")
    
    # Check tasks.json
    tasks_json = vscode_dir / "tasks.json"
    if tasks_json.exists():
        print(f"✅ tasks.json exists: {tasks_json}")
    else:
        print(f"⚠ tasks.json missing (optional): {tasks_json}")
    
    return True


def test_python_environment():
    """Test Python environment configuration."""
    print("\n🧪 Testing Python Environment")
    print("=" * 50)
    
    workspace_root = Path(__file__).parent.parent
    
    # Check virtual environment
    venv_python = workspace_root / "venv" / "bin" / "python"
    if venv_python.exists():
        print(f"✅ Virtual environment Python exists: {venv_python}")
    else:
        print(f"❌ Virtual environment Python missing: {venv_python}")
    
    # Check .env file
    env_file = workspace_root / ".env"
    if env_file.exists():
        print(f"✅ .env file exists: {env_file}")
        
        # Check for required environment variables
        required_vars = [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'S3_BUCKET_NAME'
        ]
        
        with open(env_file, 'r') as f:
            env_content = f.read()
        
        for var in required_vars:
            if var in env_content:
                print(f"✅ {var} found in .env")
            else:
                print(f"⚠ {var} not found in .env")
    else:
        print(f"⚠ .env file missing: {env_file}")
    
    # Check PYTHONPATH
    pythonpath = os.getenv('PYTHONPATH', '')
    if pythonpath:
        print(f"✅ PYTHONPATH set: {pythonpath}")
        paths = pythonpath.split(':')
        for path in paths:
            if os.path.exists(path):
                print(f"   ✅ {path}")
            else:
                print(f"   ❌ {path} (missing)")
    else:
        print(f"⚠ PYTHONPATH not set")


def test_debug_script_features():
    """Test the enhanced debug features in test_classification.py."""
    print("\n🧪 Testing Debug Script Features")
    print("=" * 50)
    
    workspace_root = Path(__file__).parent.parent
    test_script = workspace_root / "scripts" / "test_classification.py"
    
    if not test_script.exists():
        print(f"❌ test_classification.py not found: {test_script}")
        return False
    
    # Read the script and check for debug features
    with open(test_script, 'r') as f:
        script_content = f.read()
    
    debug_features = [
        ('import traceback', 'Traceback import for error details'),
        ('import pdb', 'PDB import for debugging'),
        ('--debug', 'Debug command line flag'),
        ('--debug-on-start', 'Debug on start command line flag'),
        ('DEBUG_ON_ERROR', 'Debug on error environment variable'),
        ('pdb.post_mortem()', 'Post-mortem debugging'),
        ('pdb.set_trace()', 'Manual breakpoint setting'),
        ('traceback.format_exc()', 'Full traceback logging'),
    ]
    
    for feature, description in debug_features:
        if feature in script_content:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    return True


def generate_debug_usage_examples():
    """Generate usage examples for debugging."""
    print("\n🧪 Debug Usage Examples")
    print("=" * 50)
    
    workspace_root = Path(__file__).parent.parent
    
    examples = [
        {
            "title": "Basic Debug Run",
            "command": f"cd {workspace_root} && python scripts/test_classification.py verified_docs/log --max-files 2 --debug",
            "description": "Run with debug mode enabled for error handling"
        },
        {
            "title": "Debug from Start",
            "command": f"cd {workspace_root} && python scripts/test_classification.py verified_docs/log --max-files 1 --debug-on-start",
            "description": "Start debugger immediately at the beginning"
        },
        {
            "title": "Environment Debug",
            "command": f"cd {workspace_root} && DEBUG_ON_ERROR=true python scripts/test_classification.py verified_docs/log --max-files 1",
            "description": "Use environment variable to enable debug on error"
        },
        {
            "title": "VS Code Debug",
            "command": "Open VS Code → Run and Debug (Ctrl+Shift+D) → Select configuration → F5",
            "description": "Use VS Code integrated debugger"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   Command: {example['command']}")
        print(f"   Description: {example['description']}")
    
    print(f"\n📋 VS Code Debug Configurations Available:")
    print(f"   1. Debug test_classification.py (Default Test)")
    print(f"   2. Debug test_classification.py (Custom Folder)")
    print(f"   3. Debug test_classification.py (Step Into All Code)")
    print(f"   4. Debug test_classification.py (Prompt for Args)")
    print(f"   5. Debug External Classification Module")


def main():
    """Run all VS Code debug integration tests."""
    print("🚀 VS Code Debug Integration Test")
    print("=" * 60)
    
    success = True
    
    # Test 1: VS Code configuration
    if not test_vscode_configuration():
        success = False
    
    # Test 2: Python environment
    test_python_environment()
    
    # Test 3: Debug script features
    if not test_debug_script_features():
        success = False
    
    # Test 4: Generate usage examples
    generate_debug_usage_examples()
    
    print(f"\n🎉 VS Code Debug Integration Test {'✅ PASSED' if success else '❌ FAILED'}")
    
    if success:
        print(f"\n🎯 Ready to Debug!")
        print(f"1. Open VS Code in the workspace root")
        print(f"2. Open scripts/test_classification.py")
        print(f"3. Set breakpoints where you want to pause")
        print(f"4. Go to Run and Debug (Ctrl+Shift+D)")
        print(f"5. Select a debug configuration")
        print(f"6. Press F5 to start debugging")
        print(f"\n🐛 Debug Features:")
        print(f"   - Post-mortem debugging on errors")
        print(f"   - Step into external libraries")
        print(f"   - Full traceback logging")
        print(f"   - Environment variable control")
        print(f"   - Command line debug flags")
    else:
        print(f"\n❌ Some issues found. Please check the configuration.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
