{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 1.2162026052919828e-07}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 4.356759745860472e-06, "Y": 1.0}]}, "Id": "c989a745-3cd9-4dbf-9992-499b2128deb1", "Relationships": [{"Type": "CHILD", "Ids": ["8bf75f78-7ce3-46e8-8464-6f8c40b6d68e", "43e157d9-afb3-40a2-8e20-820660350703", "390617f0-df9c-4f42-b02f-daf23c5dceab", "0bb464ea-81f7-45f8-a03f-f5e4c804deae", "7947e0eb-466e-4936-946c-07bc0e8e84cb", "486552ec-4a1a-49da-b0f7-8ed7a9c0f416", "0bcccc67-6376-44de-b5aa-16b0fd1676f2", "3354bb17-0bb9-4714-8f5c-a1e530e4c4d9", "2a5fdf89-efd6-4ec4-8650-f530a8c795ee", "2c35d7f0-7072-443f-8e10-7961e0cfbfd3", "c7086648-0309-424c-b35e-c959fa9b1517", "5bdf9214-1946-479d-82cc-0cf3c8946c96", "6b259548-4523-4820-a1c7-2edc5153a087", "095b4725-c8fe-4bce-9b99-e2e643640fee", "5c132227-f6b2-437e-8767-fa86ca56c2ee", "bdcf7e9c-827c-47a8-a3fe-a9d0e22848c3", "99b60ca4-a1a0-4247-97f3-7e3ead773b93", "22cb90d5-568b-4cec-b631-42280d98da50", "10dc86dc-2f9b-48e9-b890-76a89ca8c963", "fd3890dd-72c4-45f0-9772-607645d13ce9", "cd665268-222f-482f-bd88-9fe806d325bf", "a82cbcd6-9fae-48cc-9cae-1d79b3c04fb6", "80da039c-288f-4083-a9c2-960131b0974c", "5832d56b-60b2-438d-8abd-48b8d6d91934", "a2f2a5c9-2d13-4568-8872-78b4f7e5ce4f", "73e89f41-aa40-4d94-87df-ff55d787507b", "b15b6a53-28a1-4ed2-9b78-dae93a898cee", "1f2e613a-27b6-4081-b14b-7c2222e13556"]}]}, {"BlockType": "LINE", "Confidence": 95.46237182617188, "Text": "*1396215*", "Geometry": {"BoundingBox": {"Width": 0.1704884022474289, "Height": 0.015603389590978622, "Left": 0.04773757606744766, "Top": 0.0969940796494484}, "Polygon": [{"X": 0.04773757606744766, "Y": 0.09702679514884949}, {"X": 0.21822115778923035, "Y": 0.0969940796494484}, {"X": 0.21822598576545715, "Y": 0.11256484687328339}, {"X": 0.04774254187941551, "Y": 0.11259747296571732}]}, "Id": "8bf75f78-7ce3-46e8-8464-6f8c40b6d68e", "Relationships": [{"Type": "CHILD", "Ids": ["d14e67fa-8cf5-4465-b295-fb4bbf1ec891"]}]}, {"BlockType": "LINE", "Confidence": 91.93267822265625, "Text": "<PERSON><PERSON> of New England", "Geometry": {"BoundingBox": {"Width": 0.5538964867591858, "Height": 0.026583870872855186, "Left": 0.047698572278022766, "Top": 0.1185869425535202}, "Polygon": [{"X": 0.047698572278022766, "Y": 0.1186928004026413}, {"X": 0.6015873551368713, "Y": 0.1185869425535202}, {"X": 0.6015950441360474, "Y": 0.14506550133228302}, {"X": 0.04770701378583908, "Y": 0.14517080783843994}]}, "Id": "43e157d9-afb3-40a2-8e20-820660350703", "Relationships": [{"Type": "CHILD", "Ids": ["fb9762f7-2a1b-42d0-a94e-82f0795b5edc", "f8152715-a9c1-4111-a112-3f3ade6e13c3", "54c69eec-48d9-403d-b0af-ff3e7baa88d4", "1ab4c0c0-a2c3-4b45-8c40-0e6a1f049a46", "ce58af77-5909-44d2-a3ba-e1ff2231d1c6"]}]}, {"BlockType": "LINE", "Confidence": 97.76284790039062, "Text": "A Division of Casella Waste Systems Inc", "Geometry": {"BoundingBox": {"Width": 0.6245645880699158, "Height": 0.02683311142027378, "Left": 0.04951220005750656, "Top": 0.14704176783561707}, "Polygon": [{"X": 0.04951220005750656, "Y": 0.14716047048568726}, {"X": 0.6740691065788269, "Y": 0.14704176783561707}, {"X": 0.6740767955780029, "Y": 0.17375679314136505}, {"X": 0.04952071234583855, "Y": 0.1738748848438263}]}, "Id": "390617f0-df9c-4f42-b02f-daf23c5dceab", "Relationships": [{"Type": "CHILD", "Ids": ["165a28e4-d5ab-49ce-85ae-378f7ecdc7de", "d92137a7-8ba4-4c9a-bd4a-0b43d57935a5", "320cdb43-0866-46f0-a86a-8d2496976629", "bdcee8d8-018f-42ab-b5f9-737ba06a8450", "8b4ec1b9-887b-4772-9880-c0b00e2f30d0", "4ca0b2cb-e5c0-46ee-837f-70fb4a108183", "27882d60-fe82-4689-87ab-b49b7e6d7b17"]}]}, {"BlockType": "LINE", "Confidence": 97.62062072753906, "Text": "617-242-7746P 14 SCALE HOUSE617-242-3649F", "Geometry": {"BoundingBox": {"Width": 0.7569723129272461, "Height": 0.03181067481637001, "Left": 0.05091793090105057, "Top": 0.17318367958068848}, "Polygon": [{"X": 0.05091793090105057, "Y": 0.17332682013511658}, {"X": 0.8078813552856445, "Y": 0.17318367958068848}, {"X": 0.8078902363777161, "Y": 0.2048521190881729}, {"X": 0.0509280189871788, "Y": 0.2049943506717682}]}, "Id": "0bb464ea-81f7-45f8-a03f-f5e4c804deae", "Relationships": [{"Type": "CHILD", "Ids": ["d5e5459b-8d6c-4ccd-9453-576183d90e92", "f0594b68-a223-403c-8f33-c1c164ec93ad", "fbc3ae32-bd4e-42ca-8d94-903a996348c5", "d5132983-8b35-47e2-8e73-c6e02f37be76"]}]}, {"BlockType": "LINE", "Confidence": 91.19622802734375, "Text": "BUNKER HILL INDI STRIAL PARK", "Geometry": {"BoundingBox": {"Width": 0.5935463905334473, "Height": 0.026765553280711174, "Left": 0.09675182402133942, "Top": 0.16174879670143127}, "Polygon": [{"X": 0.09675182402133942, "Y": 0.16186127066612244}, {"X": 0.6902905702590942, "Y": 0.16174879670143127}, {"X": 0.6902981996536255, "Y": 0.1884024441242218}, {"X": 0.09676025062799454, "Y": 0.1885143518447876}]}, "Id": "7947e0eb-466e-4936-946c-07bc0e8e84cb", "Relationships": [{"Type": "CHILD", "Ids": ["484af211-8bd0-4a04-b2e1-c4263debdaa7", "b4016f0b-41c2-41c3-914b-b1a84185600f", "2c3edfb9-e035-4ac0-a770-6edc2d4d1f80", "680dba49-06a2-44b2-9318-e5a4ab6c05cf", "ae1ab9a0-78c3-4efd-ab64-41369a6f7900"]}]}, {"BlockType": "LINE", "Confidence": 81.25817108154297, "Text": "Ticket: 1396215", "Geometry": {"BoundingBox": {"Width": 0.2447197437286377, "Height": 0.01703384704887867, "Left": 0.051434461027383804, "Top": 0.25808456540107727}, "Polygon": [{"X": 0.051434461027383804, "Y": 0.2581300735473633}, {"X": 0.29614901542663574, "Y": 0.25808456540107727}, {"X": 0.2961542010307312, "Y": 0.2750730514526367}, {"X": 0.05143987387418747, "Y": 0.2751184105873108}]}, "Id": "486552ec-4a1a-49da-b0f7-8ed7a9c0f416", "Relationships": [{"Type": "CHILD", "Ids": ["2ff81379-5df9-463c-9bfd-338ba2f20b7c", "cd9bc403-66f3-4d45-8bfc-e2b90d67a64e"]}]}, {"BlockType": "LINE", "Confidence": 93.076171875, "Text": "Date: 9/11/2025", "Geometry": {"BoundingBox": {"Width": 0.2404455989599228, "Height": 0.01675555855035782, "Left": 0.07257307320833206, "Top": 0.27657032012939453}, "Polygon": [{"X": 0.07257307320833206, "Y": 0.27661484479904175}, {"X": 0.31301358342170715, "Y": 0.27657032012939453}, {"X": 0.31301867961883545, "Y": 0.2932814657688141}, {"X": 0.07257837802171707, "Y": 0.29332587122917175}]}, "Id": "0bcccc67-6376-44de-b5aa-16b0fd1676f2", "Relationships": [{"Type": "CHILD", "Ids": ["c80d4b76-1961-46ca-a3ad-1ce4be18b946", "f5c8b580-7a98-4a72-bb79-7d08db60b1e1"]}]}, {"BlockType": "LINE", "Confidence": 60.32418441772461, "Text": "Time 16:09:33- - 16:38:36", "Geometry": {"BoundingBox": {"Width": 0.3925778269767761, "Height": 0.019650569185614586, "Left": 0.07050830870866776, "Top": 0.2914958596229553}, "Polygon": [{"X": 0.07050830870866776, "Y": 0.2915683686733246}, {"X": 0.4630803167819977, "Y": 0.2914958596229553}, {"X": 0.46308615803718567, "Y": 0.3110741972923279}, {"X": 0.07051452249288559, "Y": 0.31114643812179565}]}, "Id": "3354bb17-0bb9-4714-8f5c-a1e530e4c4d9", "Relationships": [{"Type": "CHILD", "Ids": ["4a22a4fd-978f-4e57-8ecb-d95b977760fb", "2f2f9329-bf7e-4f53-9c7b-1ffda46f34f2", "0bdde6e5-31a1-449b-87ac-d64ac4f6ccdb", "7bfc882d-0cd3-49d8-a14d-2cc92a31c2aa"]}]}, {"BlockType": "LINE", "Confidence": 96.55054473876953, "Text": "Customer KO00508 EARTHWATCH WASTE SYSTEM", "Geometry": {"BoundingBox": {"Width": 0.8730989694595337, "Height": 0.028469689190387726, "Left": 0.08091893047094345, "Top": 0.3173295557498932}, "Polygon": [{"X": 0.08091893047094345, "Y": 0.31748998165130615}, {"X": 0.9540101885795593, "Y": 0.3173295557498932}, {"X": 0.9540178775787354, "Y": 0.34563973546028137}, {"X": 0.08092790842056274, "Y": 0.3457992672920227}]}, "Id": "2a5fdf89-efd6-4ec4-8650-f530a8c795ee", "Relationships": [{"Type": "CHILD", "Ids": ["1a580479-9a9a-47f8-98bb-a7ecbdebd46f", "9d7e9c3c-be96-4dfc-b280-22d4e6ca6c02", "7caa9daf-575c-469c-b370-9a9db8921ec6", "3c7519dd-4f02-4e65-b3ae-2cdde478d86b", "d2ae28c0-2521-484a-94b3-3a48a6245217"]}]}, {"BlockType": "LINE", "Confidence": 82.18919372558594, "Text": "Truck: 2310", "Geometry": {"BoundingBox": {"Width": 0.18585823476314545, "Height": 0.014042462222278118, "Left": 0.10743274539709091, "Top": 0.34808340668678284}, "Polygon": [{"X": 0.10743274539709091, "Y": 0.3481173515319824}, {"X": 0.29328668117523193, "Y": 0.34808340668678284}, {"X": 0.29329097270965576, "Y": 0.3620920181274414}, {"X": 0.10743717104196548, "Y": 0.36212587356567383}]}, "Id": "2c35d7f0-7072-443f-8e10-7961e0cfbfd3", "Relationships": [{"Type": "CHILD", "Ids": ["15b0c485-4c1b-4d39-b813-04148a8b275e", "7e9e3a4a-db5d-4ef8-a251-5a07874b6842"]}]}, {"BlockType": "LINE", "Confidence": 86.3173599243164, "Text": "Trailer: TA205286", "Geometry": {"BoundingBox": {"Width": 0.2790874242782593, "Height": 0.015513141639530659, "Left": 0.08946771919727325, "Top": 0.364566445350647}, "Polygon": [{"X": 0.08946771919727325, "Y": 0.3646172285079956}, {"X": 0.36855047941207886, "Y": 0.364566445350647}, {"X": 0.36855512857437134, "Y": 0.38002896308898926}, {"X": 0.08947261422872543, "Y": 0.38007959723472595}]}, "Id": "c7086648-0309-424c-b35e-c959fa9b1517", "Relationships": [{"Type": "CHILD", "Ids": ["2297ef15-4231-485d-ae38-a40b48985a55", "a856f536-393a-4a75-9edd-6661fee5f785"]}]}, {"BlockType": "LINE", "Confidence": 91.74983978271484, "Text": "Destination: A/A", "Geometry": {"BoundingBox": {"Width": 0.2548648715019226, "Height": 0.014473997056484222, "Left": 0.05428561195731163, "Top": 0.38369259238243103}, "Polygon": [{"X": 0.05428561195731163, "Y": 0.38373878598213196}, {"X": 0.30914607644081116, "Y": 0.38369259238243103}, {"X": 0.30915048718452454, "Y": 0.3981205224990845}, {"X": 0.054290205240249634, "Y": 0.39816656708717346}]}, "Id": "5bdf9214-1946-479d-82cc-0cf3c8946c96", "Relationships": [{"Type": "CHILD", "Ids": ["4c7d403d-2291-433b-b7ff-e83d609679ce", "be406965-4c8c-4853-9706-59759f63a4bb"]}]}, {"BlockType": "LINE", "Confidence": 87.87518310546875, "Text": "P.O.: EARTH091125-3", "Geometry": {"BoundingBox": {"Width": 0.3502492904663086, "Height": 0.017489586025476456, "Left": 0.12097957730293274, "Top": 0.39790067076683044}, "Polygon": [{"X": 0.12097957730293274, "Y": 0.3979640007019043}, {"X": 0.47122371196746826, "Y": 0.39790067076683044}, {"X": 0.47122886776924133, "Y": 0.41532719135284424}, {"X": 0.12098506838083267, "Y": 0.4153902530670166}]}, "Id": "6b259548-4523-4820-a1c7-2edc5153a087", "Relationships": [{"Type": "CHILD", "Ids": ["5579e6c3-9dc2-4b1d-805e-cce775c9cb4e", "e2d23ee7-d2db-4b1a-a8cb-137b1c78a1b3"]}]}, {"BlockType": "LINE", "Confidence": 85.47064208984375, "Text": "D.O.: 2522607", "Geometry": {"BoundingBox": {"Width": 0.22168892621994019, "Height": 0.01426390465348959, "Left": 0.12101923674345016, "Top": 0.4182223975658417}, "Polygon": [{"X": 0.12101923674345016, "Y": 0.4182623028755188}, {"X": 0.34270384907722473, "Y": 0.4182223975658417}, {"X": 0.34270817041397095, "Y": 0.43244650959968567}, {"X": 0.12102371454238892, "Y": 0.43248632550239563}]}, "Id": "095b4725-c8fe-4bce-9b99-e2e643640fee", "Relationships": [{"Type": "CHILD", "Ids": ["8db672a9-7541-4dc1-9be2-84cebdac6ce9", "78429672-3f16-445a-a227-ca8db2eb5146"]}]}, {"BlockType": "LINE", "Confidence": 86.55911254882812, "Text": "Seal: 531829", "Geometry": {"BoundingBox": {"Width": 0.19505658745765686, "Height": 0.012919862754642963, "Left": 0.12026123702526093, "Top": 0.4368709921836853}, "Polygon": [{"X": 0.12026123702526093, "Y": 0.43690598011016846}, {"X": 0.3153139054775238, "Y": 0.4368709921836853}, {"X": 0.315317839384079, "Y": 0.4497559666633606}, {"X": 0.12026529759168625, "Y": 0.4497908651828766}]}, "Id": "5c132227-f6b2-437e-8767-fa86ca56c2ee", "Relationships": [{"Type": "CHILD", "Ids": ["1e9a522a-a8fa-41f8-a64c-2ac527844870", "3a4c1104-46e0-44c3-b74f-350d8f8fca94"]}]}, {"BlockType": "LINE", "Confidence": 98.62630462646484, "Text": "Comment F7 TRANSPORTATION", "Geometry": {"BoundingBox": {"Width": 0.5378577709197998, "Height": 0.018440820276737213, "Left": 0.09199188649654388, "Top": 0.4663918614387512}, "Polygon": [{"X": 0.09199188649654388, "Y": 0.46648773550987244}, {"X": 0.6298443675041199, "Y": 0.4663918614387512}, {"X": 0.6298496723175049, "Y": 0.48473718762397766}, {"X": 0.09199769794940948, "Y": 0.48483267426490784}]}, "Id": "bdcf7e9c-827c-47a8-a3fe-a9d0e22848c3", "Relationships": [{"Type": "CHILD", "Ids": ["32a37da1-1efd-4db5-8dd6-58a20c432c60", "3e9ecb16-6f79-48d5-8f4d-4e5973deef4e", "c6590da8-129c-4f3d-b0f4-596bf7e5aaf4"]}]}, {"BlockType": "LINE", "Confidence": 89.41077423095703, "Text": "Gross 74220 L Out Scale 3", "Geometry": {"BoundingBox": {"Width": 0.4329214096069336, "Height": 0.015293482691049576, "Left": 0.06448246538639069, "Top": 0.5340837240219116}, "Polygon": [{"X": 0.06448246538639069, "Y": 0.534159779548645}, {"X": 0.49739935994148254, "Y": 0.5340837240219116}, {"X": 0.4974038600921631, "Y": 0.5493013858795166}, {"X": 0.06448730826377869, "Y": 0.5493772029876709}]}, "Id": "99b60ca4-a1a0-4247-97f3-7e3ead773b93", "Relationships": [{"Type": "CHILD", "Ids": ["69fd9624-bb33-420b-8761-64a6d82b12b6", "ba43bcf3-ad9e-4508-8e6a-e1d0e1e65a9b", "089b8ff2-ab96-4216-940f-c280594de11c", "b3efeb71-f119-49b4-a598-8c703020598f", "d63ab35d-8bb5-43db-888d-456db6e151fe", "aa903de1-8e9b-4fc3-83fe-6e4d7f1a5324"]}]}, {"BlockType": "LINE", "Confidence": 87.43901062011719, "Text": "Tare: 32580 L In Scale 3", "Geometry": {"BoundingBox": {"Width": 0.3972502052783966, "Height": 0.014897219836711884, "Left": 0.07268761098384857, "Top": 0.5523481369018555}, "Polygon": [{"X": 0.07268761098384857, "Y": 0.5524176359176636}, {"X": 0.469933420419693, "Y": 0.5523481369018555}, {"X": 0.46993783116340637, "Y": 0.5671760439872742}, {"X": 0.07269231975078583, "Y": 0.567245364189148}]}, "Id": "22cb90d5-568b-4cec-b631-42280d98da50", "Relationships": [{"Type": "CHILD", "Ids": ["63033217-0786-4f7d-a1f0-73469f7c10d2", "bcc6d0f0-8e12-41be-8009-125fcc65025d", "f7a4746c-3a3c-4c78-8867-b2dd5145e037", "da8bc33f-dbc3-48dd-af40-86a4b0e666b4", "33ca4df4-bf82-4612-ab38-889b4c6784f6", "90043291-53e5-4d1e-be67-c4aa0c9a9fc5"]}]}, {"BlockType": "LINE", "Confidence": 71.19586181640625, "Text": "Net: 41610 L", "Geometry": {"BoundingBox": {"Width": 0.2188889980316162, "Height": 0.012491908855736256, "Left": 0.08476102352142334, "Top": 0.571865975856781}, "Polygon": [{"X": 0.08476102352142334, "Y": 0.5719041228294373}, {"X": 0.3036462068557739, "Y": 0.571865975856781}, {"X": 0.30365002155303955, "Y": 0.5843198299407959}, {"X": 0.08476496487855911, "Y": 0.5843579173088074}]}, "Id": "10dc86dc-2f9b-48e9-b890-76a89ca8c963", "Relationships": [{"Type": "CHILD", "Ids": ["b9ab7e54-3888-4313-8c50-f9843f1943a2", "468bb750-e5ae-4495-80a1-15730f3fc7db", "27e26aa2-4495-4e5e-b1c3-c74dc2d83aca"]}]}, {"BlockType": "LINE", "Confidence": 74.48023223876953, "Text": "<PERSON><PERSON> 20 82", "Geometry": {"BoundingBox": {"Width": 0.1726113110780716, "Height": 0.011742501519620419, "Left": 0.07357224822044373, "Top": 0.5995688438415527}, "Polygon": [{"X": 0.07357224822044373, "Y": 0.59959876537323}, {"X": 0.24617993831634521, "Y": 0.5995688438415527}, {"X": 0.24618355929851532, "Y": 0.6112815141677856}, {"X": 0.07357596606016159, "Y": 0.6113113760948181}]}, "Id": "fd3890dd-72c4-45f0-9772-607645d13ce9", "Relationships": [{"Type": "CHILD", "Ids": ["3ce708f6-4d8d-4938-acae-7eacec5d2bc2", "6ba64846-33a3-4196-bb11-254a6a3878fe"]}]}, {"BlockType": "LINE", "Confidence": 99.85816192626953, "Text": "Materials & Services", "Geometry": {"BoundingBox": {"Width": 0.31052568554878235, "Height": 0.012038568034768105, "Left": 0.05718228220939636, "Top": 0.6348950862884521}, "Polygon": [{"X": 0.05718228220939636, "Y": 0.6349484920501709}, {"X": 0.3677043318748474, "Y": 0.6348950862884521}, {"X": 0.3677079677581787, "Y": 0.6468803882598877}, {"X": 0.05718609690666199, "Y": 0.6469336748123169}]}, "Id": "cd665268-222f-482f-bd88-9fe806d325bf", "Relationships": [{"Type": "CHILD", "Ids": ["dc761b53-b07c-4872-8700-346de3f85dd7", "a674d842-c334-4923-9a65-188cd9c3b867", "22d04c55-bf14-487e-9e71-49ff5a69ebc9"]}]}, {"BlockType": "LINE", "Confidence": 96.24176025390625, "Text": "Origin NA/NON APPLICABLE", "Geometry": {"BoundingBox": {"Width": 0.49961113929748535, "Height": 0.016323287039995193, "Left": 0.09829284250736237, "Top": 0.6775823831558228}, "Polygon": [{"X": 0.09829284250736237, "Y": 0.677667498588562}, {"X": 0.5978992581367493, "Y": 0.6775823831558228}, {"X": 0.5979040265083313, "Y": 0.6938208341598511}, {"X": 0.09829797595739365, "Y": 0.6939056515693665}]}, "Id": "a82cbcd6-9fae-48cc-9cae-1d79b3c04fb6", "Relationships": [{"Type": "CHILD", "Ids": ["77fb40a5-ffe6-46d9-a676-1820fd46cf2d", "57c9d8af-17b6-409e-911d-77d830a10fc7", "70e2c644-bd4d-491f-b142-77ba96ea559b"]}]}, {"BlockType": "LINE", "Confidence": 98.691650390625, "Text": "Material 539-1/OBGN MIX COMMODITIES GL", "Geometry": {"BoundingBox": {"Width": 0.7752476334571838, "Height": 0.01648397371172905, "Left": 0.07980994135141373, "Top": 0.6934472918510437}, "Polygon": [{"X": 0.07980994135141373, "Y": 0.6935789585113525}, {"X": 0.8550530076026917, "Y": 0.6934472918510437}, {"X": 0.8550575375556946, "Y": 0.709800124168396}, {"X": 0.07981512695550919, "Y": 0.7099312543869019}]}, "Id": "80da039c-288f-4083-a9c2-960131b0974c", "Relationships": [{"Type": "CHILD", "Ids": ["ae86fbf7-23df-42d5-8491-f1e40583a9a0", "6891423d-2f70-48bd-8738-4764ff484c8a", "9eae3721-922b-46b9-bfa7-290cd28ba<PERSON>", "7d578744-15cc-4ff5-a981-dbfeaf255420", "0490ebb5-eef3-4f00-9adf-d1644222e547"]}]}, {"BlockType": "LINE", "Confidence": 94.25435638427734, "Text": "Quantity: 20.82 Ton", "Geometry": {"BoundingBox": {"Width": 0.30195024609565735, "Height": 0.014244554564356804, "Left": 0.07737119495868683, "Top": 0.7152701616287231}, "Polygon": [{"X": 0.07737119495868683, "Y": 0.7153211832046509}, {"X": 0.37931716442108154, "Y": 0.7152701616287231}, {"X": 0.37932145595550537, "Y": 0.7294638156890869}, {"X": 0.07737570255994797, "Y": 0.7295147180557251}]}, "Id": "5832d56b-60b2-438d-8abd-48b8d6d91934", "Relationships": [{"Type": "CHILD", "Ids": ["d6459b95-f7be-4e18-819b-5b8b21a0c8d5", "5305586b-bae8-46b3-a342-6dcc854d8f95", "cd40a537-47c5-40f5-baa6-4d953032533c"]}]}, {"BlockType": "LINE", "Confidence": 82.49710845947266, "Text": "Weighmaster TOM", "Geometry": {"BoundingBox": {"Width": 0.2989441156387329, "Height": 0.012809333391487598, "Left": 0.039394132792949677, "Top": 0.831294059753418}, "Polygon": [{"X": 0.039394132792949677, "Y": 0.8313432931900024}, {"X": 0.3383343815803528, "Y": 0.831294059753418}, {"X": 0.3383382558822632, "Y": 0.8440542817115784}, {"X": 0.039398204535245895, "Y": 0.8441033363342285}]}, "Id": "a2f2a5c9-2d13-4568-8872-78b4f7e5ce4f", "Relationships": [{"Type": "CHILD", "Ids": ["c4deb86a-d9e4-4d94-bcd8-158a0125c54e", "57b09756-b564-4c4f-960d-0a20c5da16c6"]}]}, {"BlockType": "LINE", "Confidence": 87.79316711425781, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.10485194623470306, "Height": 0.010378104634582996, "Left": 0.08489537239074707, "Top": 0.8594906330108643}, "Polygon": [{"X": 0.08489537239074707, "Y": 0.8595077991485596}, {"X": 0.18974409997463226, "Y": 0.8594906330108643}, {"X": 0.18974731862545013, "Y": 0.8698516488075256}, {"X": 0.08489865809679031, "Y": 0.8698687553405762}]}, "Id": "73e89f41-aa40-4d94-87df-ff55d787507b", "Relationships": [{"Type": "CHILD", "Ids": ["6c35cb7e-f50d-4ceb-a0df-d4342a8cda13"]}]}, {"BlockType": "LINE", "Confidence": 97.58558654785156, "Text": "By signing above, I declare that I did NOT", "Geometry": {"BoundingBox": {"Width": 0.831017255783081, "Height": 0.015430995263159275, "Left": 0.043029770255088806, "Top": 0.9077516794204712}, "Polygon": [{"X": 0.043029770255088806, "Y": 0.9078862071037292}, {"X": 0.874042809009552, "Y": 0.9077516794204712}, {"X": 0.874047040939331, "Y": 0.9230486154556274}, {"X": 0.04303465038537979, "Y": 0.9231826663017273}]}, "Id": "b15b6a53-28a1-4ed2-9b78-dae93a898cee", "Relationships": [{"Type": "CHILD", "Ids": ["96be0a66-60f2-4c29-b43b-adbb10f70ee6", "341f57d0-cf94-45d0-9d4f-3f41ebd3022e", "f34f167f-175b-48fc-b6fb-409d6ea139fa", "57f7a3d9-8ca7-4e39-b670-33f782ba438d", "09499b07-a402-41c5-8966-e7a6f80b07b9", "5c4cab62-3093-4774-9211-6c357ebbd305", "1efbf01f-b454-4bad-afd2-9f9af9684be3", "0ac35125-8e43-4bad-8845-d80c67ae6ec7", "f3d26336-43d7-4f52-980e-d378c55dfcbc"]}]}, {"BlockType": "LINE", "Confidence": 99.95057678222656, "Text": "deposit any PROHIBITED WASTES", "Geometry": {"BoundingBox": {"Width": 0.6675469279289246, "Height": 0.015200219117105007, "Left": 0.04303944110870361, "Top": 0.9276224970817566}, "Polygon": [{"X": 0.04303944110870361, "Y": 0.9277300834655762}, {"X": 0.7105820775032043, "Y": 0.9276224970817566}, {"X": 0.7105863690376282, "Y": 0.9427155256271362}, {"X": 0.043044254183769226, "Y": 0.9428226947784424}]}, "Id": "1f2e613a-27b6-4081-b14b-7c2222e13556", "Relationships": [{"Type": "CHILD", "Ids": ["91a4fa4a-9f9e-41ea-96cc-3c11656b20d6", "6ef02f75-219f-4e9c-9629-db82cb31dbb6", "9fc159a4-6100-478d-b9ad-6164803c7690", "31cee2b2-6590-4550-a4c4-feb40c7c7985"]}]}, {"BlockType": "WORD", "Confidence": 95.46237182617188, "Text": "*1396215*", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1704884022474289, "Height": 0.015603389590978622, "Left": 0.04773757606744766, "Top": 0.0969940796494484}, "Polygon": [{"X": 0.04773757606744766, "Y": 0.09702679514884949}, {"X": 0.21822115778923035, "Y": 0.0969940796494484}, {"X": 0.21822598576545715, "Y": 0.11256484687328339}, {"X": 0.04774254187941551, "Y": 0.11259747296571732}]}, "Id": "d14e67fa-8cf5-4465-b295-fb4bbf1ec891"}, {"BlockType": "WORD", "Confidence": 99.32398223876953, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11136167496442795, "Height": 0.013199065811932087, "Left": 0.047702811658382416, "Top": 0.13197174668312073}, "Polygon": [{"X": 0.047702811658382416, "Y": 0.13199298083782196}, {"X": 0.15906035900115967, "Y": 0.13197174668312073}, {"X": 0.15906448662281036, "Y": 0.14514964818954468}, {"X": 0.04770701378583908, "Y": 0.14517080783843994}]}, "Id": "fb9762f7-2a1b-42d0-a94e-82f0795b5edc"}, {"BlockType": "WORD", "Confidence": 99.4385757446289, "Text": "Recycling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15164822340011597, "Height": 0.016559628769755363, "Left": 0.16907909512519836, "Top": 0.12664330005645752}, "Polygon": [{"X": 0.16907909512519836, "Y": 0.12667223811149597}, {"X": 0.3207222819328308, "Y": 0.12664330005645752}, {"X": 0.32072731852531433, "Y": 0.14317408204078674}, {"X": 0.16908426582813263, "Y": 0.14320293068885803}]}, "Id": "f8152715-a9c1-4111-a112-3f3ade6e13c3"}, {"BlockType": "WORD", "Confidence": 61.13759231567383, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04812043905258179, "Height": 0.011499886400997639, "Left": 0.32886210083961487, "Top": 0.12371489405632019}, "Polygon": [{"X": 0.32886210083961487, "Y": 0.1237240806221962}, {"X": 0.3769790530204773, "Y": 0.12371489405632019}, {"X": 0.37698253989219666, "Y": 0.13520561158657074}, {"X": 0.32886558771133423, "Y": 0.13521479070186615}]}, "Id": "54c69eec-48d9-403d-b0af-ff3e7baa88d4"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "New", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07265004515647888, "Height": 0.011802464723587036, "Left": 0.3893321454524994, "Top": 0.12268688529729843}, "Polygon": [{"X": 0.3893321454524994, "Y": 0.1227007582783699}, {"X": 0.4619787037372589, "Y": 0.12268688529729843}, {"X": 0.46198219060897827, "Y": 0.13447551429271698}, {"X": 0.3893357217311859, "Y": 0.13448935747146606}]}, "Id": "1ab4c0c0-a2c3-4b45-8c40-0e6a1f049a46"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "England", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12928925454616547, "Height": 0.01586015708744526, "Left": 0.47230270504951477, "Top": 0.1185869425535202}, "Polygon": [{"X": 0.47230270504951477, "Y": 0.1186116486787796}, {"X": 0.6015873551368713, "Y": 0.1185869425535202}, {"X": 0.601591944694519, "Y": 0.13442246615886688}, {"X": 0.472307413816452, "Y": 0.1344470977783203}]}, "Id": "ce58af77-5909-44d2-a3ba-e1ff2231d1c6"}, {"BlockType": "WORD", "Confidence": 97.87567901611328, "Text": "A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027235141023993492, "Height": 0.010602327063679695, "Left": 0.04951733723282814, "Top": 0.16327254474163055}, "Polygon": [{"X": 0.04951733723282814, "Y": 0.16327771544456482}, {"X": 0.07674911618232727, "Y": 0.16327254474163055}, {"X": 0.07675247639417648, "Y": 0.17386972904205322}, {"X": 0.04952071234583855, "Y": 0.1738748848438263}]}, "Id": "165a28e4-d5ab-49ce-85ae-378f7ecdc7de"}, {"BlockType": "WORD", "Confidence": 99.69328308105469, "Text": "Division", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12684889137744904, "Height": 0.014011518098413944, "Left": 0.08346560597419739, "Top": 0.15942637622356415}, "Polygon": [{"X": 0.08346560597419739, "Y": 0.15945042669773102}, {"X": 0.21031014621257782, "Y": 0.15942637622356415}, {"X": 0.21031449735164642, "Y": 0.17341391742229462}, {"X": 0.08347003906965256, "Y": 0.17343789339065552}]}, "Id": "d92137a7-8ba4-4c9a-bd4a-0b43d57935a5"}, {"BlockType": "WORD", "Confidence": 95.234375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04066740721464157, "Height": 0.012027828022837639, "Left": 0.2184504270553589, "Top": 0.15634596347808838}, "Polygon": [{"X": 0.2184504270553589, "Y": 0.1563536822795868}, {"X": 0.259114146232605, "Y": 0.15634596347808838}, {"X": 0.25911784172058105, "Y": 0.16836610436439514}, {"X": 0.21845415234565735, "Y": 0.16837379336357117}]}, "Id": "320cdb43-0866-46f0-a86a-8d2496976629"}, {"BlockType": "WORD", "Confidence": 95.39002990722656, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11552844196557999, "Height": 0.014199156314134598, "Left": 0.25586262345314026, "Top": 0.1529059261083603}, "Polygon": [{"X": 0.25586262345314026, "Y": 0.15292786061763763}, {"X": 0.3713867962360382, "Y": 0.1529059261083603}, {"X": 0.37139105796813965, "Y": 0.16708321869373322}, {"X": 0.25586700439453125, "Y": 0.1671050786972046}]}, "Id": "bdcee8d8-018f-42ab-b5f9-737ba06a8450"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Waste", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09694699198007584, "Height": 0.011248557828366756, "Left": 0.38041725754737854, "Top": 0.15178699791431427}, "Polygon": [{"X": 0.38041725754737854, "Y": 0.15180540084838867}, {"X": 0.47736093401908875, "Y": 0.15178699791431427}, {"X": 0.4773642420768738, "Y": 0.1630171835422516}, {"X": 0.38042065501213074, "Y": 0.1630355566740036}]}, "Id": "8b4ec1b9-887b-4772-9880-c0b00e2f30d0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Systems", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1292765736579895, "Height": 0.013618847355246544, "Left": 0.48574063181877136, "Top": 0.14982442557811737}, "Polygon": [{"X": 0.48574063181877136, "Y": 0.14984898269176483}, {"X": 0.6150132417678833, "Y": 0.14982442557811737}, {"X": 0.6150171756744385, "Y": 0.16341878473758698}, {"X": 0.4857446551322937, "Y": 0.16344328224658966}]}, "Id": "4ca0b2cb-e5c0-46ee-837f-70fb4a108183"}, {"BlockType": "WORD", "Confidence": 96.49394226074219, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04906120151281357, "Height": 0.01081859041005373, "Left": 0.6250110268592834, "Top": 0.14704176783561707}, "Polygon": [{"X": 0.6250110268592834, "Y": 0.14705109596252441}, {"X": 0.6740691065788269, "Y": 0.14704176783561707}, {"X": 0.6740722060203552, "Y": 0.15785105526447296}, {"X": 0.6250141263008118, "Y": 0.15786035358905792}]}, "Id": "27882d60-fe82-4689-87ab-b49b7e6d7b17"}, {"BlockType": "WORD", "Confidence": 99.16454315185547, "Text": "617-242-7746P", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23501062393188477, "Height": 0.017958926036953926, "Left": 0.05092231184244156, "Top": 0.1870354264974594}, "Polygon": [{"X": 0.05092231184244156, "Y": 0.18707974255084991}, {"X": 0.2859274446964264, "Y": 0.1870354264974594}, {"X": 0.2859329283237457, "Y": 0.20495019853115082}, {"X": 0.0509280189871788, "Y": 0.2049943506717682}]}, "Id": "d5e5459b-8d6c-4ccd-9453-576183d90e92"}, {"BlockType": "WORD", "Confidence": 92.59367370605469, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03234270215034485, "Height": 0.010986105538904667, "Left": 0.05498191714286804, "Top": 0.17840057611465454}, "Polygon": [{"X": 0.05498191714286804, "Y": 0.17840668559074402}, {"X": 0.08732113987207413, "Y": 0.17840057611465454}, {"X": 0.08732461929321289, "Y": 0.18938058614730835}, {"X": 0.054985411465168, "Y": 0.18938668072223663}]}, "Id": "f0594b68-a223-403c-8f33-c1c164ec93ad"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12232229113578796, "Height": 0.013147302903234959, "Left": 0.3011344373226166, "Top": 0.18345050513744354}, "Polygon": [{"X": 0.3011344373226166, "Y": 0.1834735870361328}, {"X": 0.42345279455184937, "Y": 0.18345050513744354}, {"X": 0.42345672845840454, "Y": 0.19657479226589203}, {"X": 0.3011384606361389, "Y": 0.19659781455993652}]}, "Id": "fbc3ae32-bd4e-42ca-8d94-903a996348c5"}, {"BlockType": "WORD", "Confidence": 98.81298065185547, "Text": "HOUSE617-242-3649F", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.37507155537605286, "Height": 0.021930675953626633, "Left": 0.43281593918800354, "Top": 0.17318367958068848}, "Polygon": [{"X": 0.43281593918800354, "Y": 0.1732546091079712}, {"X": 0.8078813552856445, "Y": 0.17318367958068848}, {"X": 0.8078874945640564, "Y": 0.19504374265670776}, {"X": 0.43282246589660645, "Y": 0.1951143592596054}]}, "Id": "d5132983-8b35-47e2-8e73-c6e02f37be76"}, {"BlockType": "WORD", "Confidence": 98.85582733154297, "Text": "BUNKER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15348489582538605, "Height": 0.01586984656751156, "Left": 0.0967552438378334, "Top": 0.1726444959640503}, "Polygon": [{"X": 0.0967552438378334, "Y": 0.1726735234260559}, {"X": 0.25023525953292847, "Y": 0.1726444959640503}, {"X": 0.25024011731147766, "Y": 0.18848541378974915}, {"X": 0.09676025062799454, "Y": 0.1885143518447876}]}, "Id": "484af211-8bd0-4a04-b2e1-c4263debdaa7"}, {"BlockType": "WORD", "Confidence": 69.88540649414062, "Text": "HILL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0875997319817543, "Height": 0.013173178769648075, "Left": 0.2586900293827057, "Top": 0.16950903832912445}, "Polygon": [{"X": 0.2586900293827057, "Y": 0.169525608420372}, {"X": 0.34628579020500183, "Y": 0.16950903832912445}, {"X": 0.3462897837162018, "Y": 0.18266569077968597}, {"X": 0.2586940824985504, "Y": 0.18268221616744995}]}, "Id": "b4016f0b-41c2-41c3-914b-b1a84185600f"}, {"BlockType": "WORD", "Confidence": 93.21907043457031, "Text": "INDI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07881230115890503, "Height": 0.012568207457661629, "Left": 0.35445770621299744, "Top": 0.16752184927463531}, "Polygon": [{"X": 0.35445770621299744, "Y": 0.16753676533699036}, {"X": 0.4332662522792816, "Y": 0.16752184927463531}, {"X": 0.43327000737190247, "Y": 0.18007518351078033}, {"X": 0.3544614911079407, "Y": 0.1800900548696518}]}, "Id": "2c3edfb9-e035-4ac0-a770-6edc2d4d1f80"}, {"BlockType": "WORD", "Confidence": 94.22592163085938, "Text": "STRIAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14109553396701813, "Height": 0.013897120952606201, "Left": 0.44032302498817444, "Top": 0.16430367529392242}, "Polygon": [{"X": 0.44032302498817444, "Y": 0.1643304079771042}, {"X": 0.581414520740509, "Y": 0.16430367529392242}, {"X": 0.5814185738563538, "Y": 0.17817415297031403}, {"X": 0.44032716751098633, "Y": 0.17820079624652863}]}, "Id": "680dba49-06a2-44b2-9318-e5a4ab6c05cf"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "PARK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1007356345653534, "Height": 0.012568891979753971, "Left": 0.5895585417747498, "Top": 0.16174879670143127}, "Polygon": [{"X": 0.5895585417747498, "Y": 0.1617678850889206}, {"X": 0.6902905702590942, "Y": 0.16174879670143127}, {"X": 0.6902942061424255, "Y": 0.17429864406585693}, {"X": 0.589562177658081, "Y": 0.17431768774986267}]}, "Id": "ae1ab9a0-78c3-4efd-ab64-41369a6f7900"}, {"BlockType": "WORD", "Confidence": 66.4502944946289, "Text": "Ticket:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10293057560920715, "Height": 0.011964306235313416, "Left": 0.05143606662750244, "Top": 0.2631540894508362}, "Polygon": [{"X": 0.05143606662750244, "Y": 0.2631732225418091}, {"X": 0.15436290204524994, "Y": 0.2631540894508362}, {"X": 0.1543666422367096, "Y": 0.27509933710098267}, {"X": 0.05143987387418747, "Y": 0.2751184105873108}]}, "Id": "2ff81379-5df9-463c-9bfd-338ba2f20b7c"}, {"BlockType": "WORD", "Confidence": 96.06604766845703, "Text": "1396215", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12766118347644806, "Height": 0.014289136044681072, "Left": 0.16849219799041748, "Top": 0.25808456540107727}, "Polygon": [{"X": 0.16849219799041748, "Y": 0.2581082880496979}, {"X": 0.29614901542663574, "Y": 0.25808456540107727}, {"X": 0.29615336656570435, "Y": 0.2723500430583954}, {"X": 0.16849665343761444, "Y": 0.2723737061023712}]}, "Id": "cd9bc403-66f3-4d45-8bfc-e2b90d67a64e"}, {"BlockType": "WORD", "Confidence": 86.28009796142578, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07850227504968643, "Height": 0.011185333132743835, "Left": 0.07257483154535294, "Top": 0.2821405231952667}, "Polygon": [{"X": 0.07257483154535294, "Y": 0.2821550667285919}, {"X": 0.1510736048221588, "Y": 0.2821405231952667}, {"X": 0.15107710659503937, "Y": 0.29331138730049133}, {"X": 0.07257837802171707, "Y": 0.29332587122917175}]}, "Id": "c80d4b76-1961-46ca-a3ad-1ce4be18b946"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "9/11/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15181897580623627, "Height": 0.014361269772052765, "Left": 0.16119897365570068, "Top": 0.27657032012939453}, "Polygon": [{"X": 0.16119897365570068, "Y": 0.27659842371940613}, {"X": 0.31301358342170715, "Y": 0.27657032012939453}, {"X": 0.31301796436309814, "Y": 0.2909035384654999}, {"X": 0.16120345890522003, "Y": 0.2909315824508667}]}, "Id": "f5c8b580-7a98-4a72-bb79-7d08db60b1e1"}, {"BlockType": "WORD", "Confidence": 65.04463958740234, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08486906439065933, "Height": 0.010812900960445404, "Left": 0.070511095225811, "Top": 0.30033352971076965}, "Polygon": [{"X": 0.070511095225811, "Y": 0.3003491759300232}, {"X": 0.15537677705287933, "Y": 0.30033352971076965}, {"X": 0.15538015961647034, "Y": 0.3111308217048645}, {"X": 0.07051452249288559, "Y": 0.31114643812179565}]}, "Id": "4a22a4fd-978f-4e57-8ecb-d95b977760fb"}, {"BlockType": "WORD", "Confidence": 45.41852951049805, "Text": "16:09:33-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14659060537815094, "Height": 0.014458929188549519, "Left": 0.1698533594608307, "Top": 0.29529598355293274}, "Polygon": [{"X": 0.1698533594608307, "Y": 0.29532304406166077}, {"X": 0.31643956899642944, "Y": 0.29529598355293274}, {"X": 0.3164439797401428, "Y": 0.3097279369831085}, {"X": 0.16985787451267242, "Y": 0.3097549080848694}]}, "Id": "2f2f9329-bf7e-4f53-9c7b-1ffda46f34f2"}, {"BlockType": "WORD", "Confidence": 56.03466033935547, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012737799435853958, "Height": 0.003187996568158269, "Left": 0.3066624104976654, "Top": 0.29967090487480164}, "Polygon": [{"X": 0.3066624104976654, "Y": 0.29967325925827026}, {"X": 0.31939923763275146, "Y": 0.29967090487480164}, {"X": 0.31940022110939026, "Y": 0.30285656452178955}, {"X": 0.3066633939743042, "Y": 0.3028588891029358}]}, "Id": "0bdde6e5-31a1-449b-87ac-d64ac4f6ccdb"}, {"BlockType": "WORD", "Confidence": 74.79890441894531, "Text": "16:38:36", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13161252439022064, "Height": 0.013628635555505753, "Left": 0.33147183060646057, "Top": 0.2914958596229553}, "Polygon": [{"X": 0.33147183060646057, "Y": 0.2915201783180237}, {"X": 0.4630803167819977, "Y": 0.2914958596229553}, {"X": 0.4630843698978424, "Y": 0.3051002621650696}, {"X": 0.33147597312927246, "Y": 0.3051244914531708}]}, "Id": "7bfc882d-0cd3-49d8-a14d-2cc92a31c2aa"}, {"BlockType": "WORD", "Confidence": 98.2176742553711, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15226972103118896, "Height": 0.011233951896429062, "Left": 0.08092435449361801, "Top": 0.33456531167030334}, "Polygon": [{"X": 0.08092435449361801, "Y": 0.33459317684173584}, {"X": 0.2331906110048294, "Y": 0.33456531167030334}, {"X": 0.23319408297538757, "Y": 0.3457714319229126}, {"X": 0.08092790842056274, "Y": 0.3457992672920227}]}, "Id": "1a580479-9a9a-47f8-98bb-a7ecbdebd46f"}, {"BlockType": "WORD", "Confidence": 84.95156860351562, "Text": "KO00508", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14873461425304413, "Height": 0.013604005798697472, "Left": 0.24667823314666748, "Top": 0.32883045077323914}, "Polygon": [{"X": 0.24667823314666748, "Y": 0.3288577198982239}, {"X": 0.3954087495803833, "Y": 0.32883045077323914}, {"X": 0.3954128324985504, "Y": 0.3424072563648224}, {"X": 0.24668242037296295, "Y": 0.34243446588516235}]}, "Id": "9d7e9c3c-be96-4dfc-b280-22d4e6ca6c02"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "EARTHWATCH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2552402913570404, "Height": 0.01576828397810459, "Left": 0.40649712085723877, "Top": 0.3227531313896179}, "Polygon": [{"X": 0.40649712085723877, "Y": 0.3227999806404114}, {"X": 0.6617329120635986, "Y": 0.3227531313896179}, {"X": 0.6617374420166016, "Y": 0.33847472071647644}, {"X": 0.406501829624176, "Y": 0.33852142095565796}]}, "Id": "7caa9daf-575c-469c-b370-9a9db8921ec6"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "WASTE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12665072083473206, "Height": 0.012213218957185745, "Left": 0.6716046333312988, "Top": 0.32060086727142334}, "Polygon": [{"X": 0.6716046333312988, "Y": 0.32062414288520813}, {"X": 0.7982519268989563, "Y": 0.32060086727142334}, {"X": 0.7982553839683533, "Y": 0.33279091119766235}, {"X": 0.6716081500053406, "Y": 0.33281409740448}]}, "Id": "3c7519dd-4f02-4e65-b3ae-2cdde478d86b"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "SYSTEM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14621280133724213, "Height": 0.01344281155616045, "Left": 0.8078010082244873, "Top": 0.3173295557498932}, "Polygon": [{"X": 0.8078010082244873, "Y": 0.3173564374446869}, {"X": 0.9540101885795593, "Y": 0.3173295557498932}, {"X": 0.9540138244628906, "Y": 0.3307455778121948}, {"X": 0.8078047633171082, "Y": 0.33077237010002136}]}, "Id": "d2ae28c0-2521-484a-94b3-3a48a6245217"}, {"BlockType": "WORD", "Confidence": 64.5264663696289, "Text": "Truck:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09752752631902695, "Height": 0.012532751075923443, "Left": 0.10743322223424911, "Top": 0.3495931029319763}, "Polygon": [{"X": 0.10743322223424911, "Y": 0.34961092472076416}, {"X": 0.20495685935020447, "Y": 0.3495931029319763}, {"X": 0.20496074855327606, "Y": 0.36210811138153076}, {"X": 0.10743717104196548, "Y": 0.36212587356567383}]}, "Id": "15b0c485-4c1b-4d39-b813-04148a8b275e"}, {"BlockType": "WORD", "Confidence": 99.85192108154297, "Text": "2310", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07648108154535294, "Height": 0.011868435889482498, "Left": 0.2168092280626297, "Top": 0.34808340668678284}, "Polygon": [{"X": 0.2168092280626297, "Y": 0.3480973541736603}, {"X": 0.29328668117523193, "Y": 0.34808340668678284}, {"X": 0.29329031705856323, "Y": 0.3599379062652588}, {"X": 0.21681290864944458, "Y": 0.35995182394981384}]}, "Id": "7e9e3a4a-db5d-4ef8-a251-5a07874b6842"}, {"BlockType": "WORD", "Confidence": 73.60391235351562, "Text": "Trailer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10835587233304977, "Height": 0.012296836823225021, "Left": 0.08946873247623444, "Top": 0.36778274178504944}, "Polygon": [{"X": 0.08946873247623444, "Y": 0.3678024709224701}, {"X": 0.197820782661438, "Y": 0.36778274178504944}, {"X": 0.1978246122598648, "Y": 0.3800599277019501}, {"X": 0.08947261422872543, "Y": 0.38007959723472595}]}, "Id": "2297ef15-4231-485d-ae38-a40b48985a55"}, {"BlockType": "WORD", "Confidence": 99.03080749511719, "Text": "TA205286", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16136504709720612, "Height": 0.013286203145980835, "Left": 0.2071894258260727, "Top": 0.364566445350647}, "Polygon": [{"X": 0.2071894258260727, "Y": 0.36459583044052124}, {"X": 0.36855047941207886, "Y": 0.364566445350647}, {"X": 0.3685544729232788, "Y": 0.3778233528137207}, {"X": 0.2071935534477234, "Y": 0.3778526484966278}]}, "Id": "a856f536-393a-4a75-9edd-6661fee5f785"}, {"BlockType": "WORD", "Confidence": 83.98696899414062, "Text": "Destination:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17954324185848236, "Height": 0.01279785018414259, "Left": 0.05428614094853401, "Top": 0.3853687345981598}, "Polygon": [{"X": 0.05428614094853401, "Y": 0.38540127873420715}, {"X": 0.2338254302740097, "Y": 0.3853687345981598}, {"X": 0.23382937908172607, "Y": 0.39813411235809326}, {"X": 0.054290205240249634, "Y": 0.39816656708717346}]}, "Id": "4c7d403d-2291-433b-b7ff-e83d609679ce"}, {"BlockType": "WORD", "Confidence": 99.51271057128906, "Text": "A/A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06497061252593994, "Height": 0.010799797251820564, "Left": 0.24417875707149506, "Top": 0.38369259238243103}, "Polygon": [{"X": 0.24417875707149506, "Y": 0.38370436429977417}, {"X": 0.30914607644081116, "Y": 0.38369259238243103}, {"X": 0.3091493546962738, "Y": 0.3944806158542633}, {"X": 0.24418209493160248, "Y": 0.39449238777160645}]}, "Id": "be406965-4c8c-4853-9706-59759f63a4bb"}, {"BlockType": "WORD", "Confidence": 76.93658447265625, "Text": "P.O.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07340990006923676, "Height": 0.011497760191559792, "Left": 0.12098145484924316, "Top": 0.40389251708984375}, "Polygon": [{"X": 0.12098145484924316, "Y": 0.4039057493209839}, {"X": 0.1943877786397934, "Y": 0.40389251708984375}, {"X": 0.19439135491847992, "Y": 0.41537705063819885}, {"X": 0.12098506838083267, "Y": 0.4153902530670166}]}, "Id": "5579e6c3-9dc2-4b1d-805e-cce775c9cb4e"}, {"BlockType": "WORD", "Confidence": 98.81378173828125, "Text": "EARTH091125-3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.26365575194358826, "Height": 0.01600044220685959, "Left": 0.20757269859313965, "Top": 0.39790067076683044}, "Polygon": [{"X": 0.20757269859313965, "Y": 0.39794832468032837}, {"X": 0.47122371196746826, "Y": 0.39790067076683044}, {"X": 0.4712284505367279, "Y": 0.41385361552238464}, {"X": 0.2075776606798172, "Y": 0.41390112042427063}]}, "Id": "e2d23ee7-d2db-4b1a-a8cb-137b1c78a1b3"}, {"BlockType": "WORD", "Confidence": 71.41561889648438, "Text": "D.O.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07800767570734024, "Height": 0.011114491149783134, "Left": 0.12102022022008896, "Top": 0.42137181758880615}, "Polygon": [{"X": 0.12102022022008896, "Y": 0.42138585448265076}, {"X": 0.19902443885803223, "Y": 0.42137181758880615}, {"X": 0.1990278959274292, "Y": 0.4324723184108734}, {"X": 0.12102371454238892, "Y": 0.43248632550239563}]}, "Id": "8db672a9-7541-4dc1-9be2-84cebdac6ce9"}, {"BlockType": "WORD", "Confidence": 99.52567291259766, "Text": "2522607", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13148970901966095, "Height": 0.012938071973621845, "Left": 0.21121805906295776, "Top": 0.4182223975658417}, "Polygon": [{"X": 0.21121805906295776, "Y": 0.4182460904121399}, {"X": 0.34270384907722473, "Y": 0.4182223975658417}, {"X": 0.3427077531814575, "Y": 0.4311368763446808}, {"X": 0.2112220674753189, "Y": 0.43116047978401184}]}, "Id": "78429672-3f16-445a-a227-ca8db2eb5146"}, {"BlockType": "WORD", "Confidence": 73.34522247314453, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07106103003025055, "Height": 0.011079669930040836, "Left": 0.12026181071996689, "Top": 0.4387111961841583}, "Polygon": [{"X": 0.12026181071996689, "Y": 0.43872392177581787}, {"X": 0.19131939113140106, "Y": 0.4387111961841583}, {"X": 0.19132283329963684, "Y": 0.44977813959121704}, {"X": 0.12026529759168625, "Y": 0.4497908651828766}]}, "Id": "1e9a522a-a8fa-41f8-a64c-2ac527844870"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "531829", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11346403509378433, "Height": 0.011794660240411758, "Left": 0.2018534541130066, "Top": 0.4368709921836853}, "Polygon": [{"X": 0.2018534541130066, "Y": 0.4368913471698761}, {"X": 0.3153139054775238, "Y": 0.4368709921836853}, {"X": 0.3153174817562103, "Y": 0.44864535331726074}, {"X": 0.20185711979866028, "Y": 0.44866564869880676}]}, "Id": "3a4c1104-46e0-44c3-b74f-350d8f8fca94"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Comment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1485685110092163, "Height": 0.0110622001811862, "Left": 0.09199420362710953, "Top": 0.47377046942710876}, "Polygon": [{"X": 0.09199420362710953, "Y": 0.47379693388938904}, {"X": 0.24055930972099304, "Y": 0.47377046942710876}, {"X": 0.24056272208690643, "Y": 0.4848062992095947}, {"X": 0.09199769794940948, "Y": 0.48483267426490784}]}, "Id": "32a37da1-1efd-4db5-8dd6-58a20c432c60"}, {"BlockType": "WORD", "Confidence": 96.05708312988281, "Text": "F7", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03718245029449463, "Height": 0.010786241851747036, "Left": 0.25931036472320557, "Top": 0.47199463844299316}, "Polygon": [{"X": 0.25931036472320557, "Y": 0.47200125455856323}, {"X": 0.29648953676223755, "Y": 0.47199463844299316}, {"X": 0.2964928150177002, "Y": 0.4827742874622345}, {"X": 0.259313702583313, "Y": 0.4827808737754822}]}, "Id": "3e9ecb16-6f79-48d5-8f4d-4e5973deef4e"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "TRANSPORTATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.321415513753891, "Height": 0.016156256198883057, "Left": 0.308433473110199, "Top": 0.4663918614387512}, "Polygon": [{"X": 0.308433473110199, "Y": 0.46644914150238037}, {"X": 0.6298443675041199, "Y": 0.4663918614387512}, {"X": 0.6298490166664124, "Y": 0.48249101638793945}, {"X": 0.30843839049339294, "Y": 0.4825481176376343}]}, "Id": "c6590da8-129c-4f3d-b0f4-596bf7e5aaf4"}, {"BlockType": "WORD", "Confidence": 65.45220947265625, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09781596809625626, "Height": 0.010270101018249989, "Left": 0.06448404490947723, "Top": 0.5391071438789368}, "Polygon": [{"X": 0.06448404490947723, "Y": 0.5391243100166321}, {"X": 0.16229680180549622, "Y": 0.5391071438789368}, {"X": 0.1623000204563141, "Y": 0.5493600964546204}, {"X": 0.06448730826377869, "Y": 0.5493772029876709}]}, "Id": "69fd9624-bb33-420b-8761-64a6d82b12b6"}, {"BlockType": "WORD", "Confidence": 99.14640045166016, "Text": "74220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09448087960481644, "Height": 0.010984930209815502, "Left": 0.1887616217136383, "Top": 0.5369237661361694}, "Polygon": [{"X": 0.1887616217136383, "Y": 0.536940336227417}, {"X": 0.28323912620544434, "Y": 0.5369237661361694}, {"X": 0.28324249386787415, "Y": 0.547892153263092}, {"X": 0.1887650340795517, "Y": 0.5479087233543396}]}, "Id": "ba43bcf3-ad9e-4508-8e6a-e1d0e1e65a9b"}, {"BlockType": "WORD", "Confidence": 75.72444915771484, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022283457219600677, "Height": 0.01025855727493763, "Left": 0.2928921580314636, "Top": 0.5363619923591614}, "Polygon": [{"X": 0.2928921580314636, "Y": 0.5363658666610718}, {"X": 0.3151724636554718, "Y": 0.5363619923591614}, {"X": 0.3151755928993225, "Y": 0.5466166138648987}, {"X": 0.29289528727531433, "Y": 0.5466205477714539}]}, "Id": "089b8ff2-ab96-4216-940f-c280594de11c"}, {"BlockType": "WORD", "Confidence": 98.0518569946289, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05688681825995445, "Height": 0.009837186895310879, "Left": 0.32257747650146484, "Top": 0.5358949899673462}, "Polygon": [{"X": 0.32257747650146484, "Y": 0.5359049439430237}, {"X": 0.3794613182544708, "Y": 0.5358949899673462}, {"X": 0.3794642984867096, "Y": 0.5457221865653992}, {"X": 0.322580486536026, "Y": 0.5457321405410767}]}, "Id": "b3efeb71-f119-49b4-a598-8c703020598f"}, {"BlockType": "WORD", "Confidence": 99.07744598388672, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08266042917966843, "Height": 0.01102580688893795, "Left": 0.3880646824836731, "Top": 0.5341578125953674}, "Polygon": [{"X": 0.3880646824836731, "Y": 0.5341723561286926}, {"X": 0.4707218408584595, "Y": 0.5341578125953674}, {"X": 0.4707251191139221, "Y": 0.5451691150665283}, {"X": 0.38806799054145813, "Y": 0.5451835989952087}]}, "Id": "d63ab35d-8bb5-43db-888d-456db6e151fe"}, {"BlockType": "WORD", "Confidence": 99.01228332519531, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01732744835317135, "Height": 0.00985655002295971, "Left": 0.48007482290267944, "Top": 0.5340837240219116}, "Polygon": [{"X": 0.48007482290267944, "Y": 0.5340867638587952}, {"X": 0.49739935994148254, "Y": 0.5340837240219116}, {"X": 0.49740228056907654, "Y": 0.5439372658729553}, {"X": 0.48007774353027344, "Y": 0.5439403057098389}]}, "Id": "aa903de1-8e9b-4fc3-83fe-6e4d7f1a5324"}, {"BlockType": "WORD", "Confidence": 66.55851745605469, "Text": "Tare:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07577366381883621, "Height": 0.010350932367146015, "Left": 0.07268904149532318, "Top": 0.5568944215774536}, "Polygon": [{"X": 0.07268904149532318, "Y": 0.5569076538085938}, {"X": 0.14845946431159973, "Y": 0.5568944215774536}, {"X": 0.14846271276474, "Y": 0.5672321319580078}, {"X": 0.07269231975078583, "Y": 0.567245364189148}]}, "Id": "63033217-0786-4f7d-a1f0-73469f7c10d2"}, {"BlockType": "WORD", "Confidence": 99.76483154296875, "Text": "32580", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09391710162162781, "Height": 0.010838693007826805, "Left": 0.1779927760362625, "Top": 0.555105447769165}, "Polygon": [{"X": 0.1779927760362625, "Y": 0.5551218390464783}, {"X": 0.2719065546989441, "Y": 0.555105447769165}, {"X": 0.2719098627567291, "Y": 0.5659277439117432}, {"X": 0.17799614369869232, "Y": 0.5659441351890564}]}, "Id": "bcc6d0f0-8e12-41be-8009-125fcc65025d"}, {"BlockType": "WORD", "Confidence": 67.33916473388672, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022642403841018677, "Height": 0.010440237820148468, "Left": 0.28155216574668884, "Top": 0.5543373823165894}, "Polygon": [{"X": 0.28155216574668884, "Y": 0.5543413758277893}, {"X": 0.30419138073921204, "Y": 0.5543373823165894}, {"X": 0.3041945695877075, "Y": 0.564773678779602}, {"X": 0.2815553545951843, "Y": 0.5647776126861572}]}, "Id": "f7a4746c-3a3c-4c78-8867-b2dd5145e037"}, {"BlockType": "WORD", "Confidence": 92.73297882080078, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030307572335004807, "Height": 0.010362552478909492, "Left": 0.3137374222278595, "Top": 0.5542315244674683}, "Polygon": [{"X": 0.3137374222278595, "Y": 0.5542368292808533}, {"X": 0.3440418541431427, "Y": 0.5542315244674683}, {"X": 0.3440449833869934, "Y": 0.5645887851715088}, {"X": 0.3137405812740326, "Y": 0.5645940899848938}]}, "Id": "da8bc33f-dbc3-48dd-af40-86a4b0e666b4"}, {"BlockType": "WORD", "Confidence": 99.49398040771484, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08166191726922989, "Height": 0.010792817920446396, "Left": 0.3615814447402954, "Top": 0.552642285823822}, "Polygon": [{"X": 0.3615814447402954, "Y": 0.5526565909385681}, {"X": 0.44324013590812683, "Y": 0.552642285823822}, {"X": 0.4432433545589447, "Y": 0.563420832157135}, {"X": 0.36158472299575806, "Y": 0.5634350776672363}]}, "Id": "33ca4df4-bf82-4612-ab38-889b4c6784f6"}, {"BlockType": "WORD", "Confidence": 98.74462127685547, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017691204324364662, "Height": 0.01007125899195671, "Left": 0.45224520564079285, "Top": 0.5523481369018555}, "Polygon": [{"X": 0.45224520564079285, "Y": 0.5523512363433838}, {"X": 0.469933420419693, "Y": 0.5523481369018555}, {"X": 0.46993640065193176, "Y": 0.5624163150787354}, {"X": 0.452248215675354, "Y": 0.5624193549156189}]}, "Id": "90043291-53e5-4d1e-be67-c4aa0c9a9fc5"}, {"BlockType": "WORD", "Confidence": 61.05787658691406, "Text": "Net:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06132415309548378, "Height": 0.01030606497079134, "Left": 0.08476170152425766, "Top": 0.5740518569946289}, "Polygon": [{"X": 0.08476170152425766, "Y": 0.5740625262260437}, {"X": 0.14608262479305267, "Y": 0.5740518569946289}, {"X": 0.14608585834503174, "Y": 0.5843472480773926}, {"X": 0.08476496487855911, "Y": 0.5843579173088074}]}, "Id": "b9ab7e54-3888-4313-8c50-f9843f1943a2"}, {"BlockType": "WORD", "Confidence": 95.29834747314453, "Text": "41610", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0942867174744606, "Height": 0.010967640206217766, "Left": 0.17679274082183838, "Top": 0.572367787361145}, "Polygon": [{"X": 0.17679274082183838, "Y": 0.572384238243103}, {"X": 0.2710760831832886, "Y": 0.572367787361145}, {"X": 0.2710794508457184, "Y": 0.5833190679550171}, {"X": 0.17679616808891296, "Y": 0.5833354592323303}]}, "Id": "468bb750-e5ae-4495-80a1-15730f3fc7db"}, {"BlockType": "WORD", "Confidence": 57.23134231567383, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022608557716012, "Height": 0.010500526987016201, "Left": 0.28104087710380554, "Top": 0.571865975856781}, "Polygon": [{"X": 0.28104087710380554, "Y": 0.5718699097633362}, {"X": 0.3036462068557739, "Y": 0.571865975856781}, {"X": 0.3036494255065918, "Y": 0.5823625922203064}, {"X": 0.2810440957546234, "Y": 0.5823665261268616}]}, "Id": "27e26aa2-4495-4e5e-b1c3-c74dc2d83aca"}, {"BlockType": "WORD", "Confidence": 84.88420867919922, "Text": "Tons", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07631862163543701, "Height": 0.010226476937532425, "Left": 0.07357272505760193, "Top": 0.6010848879814148}, "Polygon": [{"X": 0.07357272505760193, "Y": 0.6010981202125549}, {"X": 0.14988814294338226, "Y": 0.6010848879814148}, {"X": 0.14989134669303894, "Y": 0.611298143863678}, {"X": 0.07357596606016159, "Y": 0.6113113760948181}]}, "Id": "3ce708f6-4d8d-4938-acae-7eacec5d2bc2"}, {"BlockType": "WORD", "Confidence": 64.07624816894531, "Text": "20 82", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08234918117523193, "Height": 0.010594401508569717, "Left": 0.16383403539657593, "Top": 0.5995688438415527}, "Polygon": [{"X": 0.16383403539657593, "Y": 0.599583089351654}, {"X": 0.24617993831634521, "Y": 0.5995688438415527}, {"X": 0.24618321657180786, "Y": 0.6101490259170532}, {"X": 0.16383734345436096, "Y": 0.6101632714271545}]}, "Id": "6ba64846-33a3-4196-bb11-254a6a3878fe"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "Materials", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1383904218673706, "Height": 0.01131709385663271, "Left": 0.05718250572681427, "Top": 0.6356165409088135}, "Polygon": [{"X": 0.05718250572681427, "Y": 0.6356403827667236}, {"X": 0.19556941092014313, "Y": 0.6356165409088135}, {"X": 0.19557291269302368, "Y": 0.6469098925590515}, {"X": 0.05718609690666199, "Y": 0.6469336748123169}]}, "Id": "dc761b53-b07c-4872-8700-346de3f85dd7"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028859294950962067, "Height": 0.00983611959964037, "Left": 0.20458856225013733, "Top": 0.6353234648704529}, "Polygon": [{"X": 0.20458856225013733, "Y": 0.635328471660614}, {"X": 0.23344480991363525, "Y": 0.6353234648704529}, {"X": 0.2334478497505188, "Y": 0.6451546549797058}, {"X": 0.20459161698818207, "Y": 0.6451596021652222}]}, "Id": "a674d842-c334-4923-9a65-188cd9c3b867"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Services", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12504273653030396, "Height": 0.009604871273040771, "Left": 0.24266450107097626, "Top": 0.6348950862884521}, "Polygon": [{"X": 0.24266450107097626, "Y": 0.6349166035652161}, {"X": 0.3677043318748474, "Y": 0.6348950862884521}, {"X": 0.3677072525024414, "Y": 0.6444784998893738}, {"X": 0.24266746640205383, "Y": 0.6444999575614929}]}, "Id": "22d04c55-bf14-487e-9e71-49ff5a69ebc9"}, {"BlockType": "WORD", "Confidence": 89.03858947753906, "Text": "Origin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09999844431877136, "Height": 0.012347315438091755, "Left": 0.09829407930374146, "Top": 0.68155837059021}, "Polygon": [{"X": 0.09829407930374146, "Y": 0.681575357913971}, {"X": 0.1982886791229248, "Y": 0.68155837059021}, {"X": 0.19829252362251282, "Y": 0.6938886642456055}, {"X": 0.09829797595739365, "Y": 0.6939056515693665}]}, "Id": "77fb40a5-ffe6-46d9-a676-1820fd46cf2d"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "NA/NON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14233733713626862, "Height": 0.011419788934290409, "Left": 0.21126946806907654, "Top": 0.679401159286499}, "Polygon": [{"X": 0.21126946806907654, "Y": 0.6794254183769226}, {"X": 0.3536033630371094, "Y": 0.679401159286499}, {"X": 0.35360682010650635, "Y": 0.6907967925071716}, {"X": 0.21127301454544067, "Y": 0.6908209323883057}]}, "Id": "57c9d8af-17b6-409e-911d-77d830a10fc7"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "APPLICABLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23545733094215393, "Height": 0.012170079164206982, "Left": 0.3624454736709595, "Top": 0.6775823831558228}, "Polygon": [{"X": 0.3624454736709595, "Y": 0.6776224970817566}, {"X": 0.5978992581367493, "Y": 0.6775823831558228}, {"X": 0.597902774810791, "Y": 0.6897124648094177}, {"X": 0.36244913935661316, "Y": 0.689752459526062}]}, "Id": "70e2c644-bd4d-491f-b142-77ba96ea559b"}, {"BlockType": "WORD", "Confidence": 97.93785858154297, "Text": "Material", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12939821183681488, "Height": 0.011219917796552181, "Left": 0.07981157302856445, "Top": 0.6987113356590271}, "Polygon": [{"X": 0.07981157302856445, "Y": 0.6987333297729492}, {"X": 0.20920631289482117, "Y": 0.6987113356590271}, {"X": 0.20920978486537933, "Y": 0.7099093794822693}, {"X": 0.07981512695550919, "Y": 0.7099312543869019}]}, "Id": "ae86fbf7-23df-42d5-8491-f1e40583a9a0"}, {"BlockType": "WORD", "Confidence": 96.6725082397461, "Text": "539-1/OBGN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20343850553035736, "Height": 0.01117002684623003, "Left": 0.22449123859405518, "Top": 0.6970627307891846}, "Polygon": [{"X": 0.22449123859405518, "Y": 0.6970972418785095}, {"X": 0.4279263913631439, "Y": 0.6970627307891846}, {"X": 0.42792972922325134, "Y": 0.7081983089447021}, {"X": 0.22449469566345215, "Y": 0.7082327604293823}]}, "Id": "6891423d-2f70-48bd-8738-4764ff484c8a"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "MIX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0736803263425827, "Height": 0.010658695362508297, "Left": 0.43899255990982056, "Top": 0.696028470993042}, "Polygon": [{"X": 0.43899255990982056, "Y": 0.6960409879684448}, {"X": 0.5126697421073914, "Y": 0.696028470993042}, {"X": 0.5126729011535645, "Y": 0.7066746950149536}, {"X": 0.43899571895599365, "Y": 0.7066871523857117}]}, "Id": "9eae3721-922b-46b9-bfa7-290cd28ba<PERSON>"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "COMMODITIES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2707447111606598, "Height": 0.011474749073386192, "Left": 0.5205036997795105, "Top": 0.6942678093910217}, "Polygon": [{"X": 0.5205036997795105, "Y": 0.6943137645721436}, {"X": 0.79124516248703, "Y": 0.6942678093910217}, {"X": 0.7912483811378479, "Y": 0.705696702003479}, {"X": 0.5205070376396179, "Y": 0.7057425379753113}]}, "Id": "7d578744-15cc-4ff5-a981-dbfeaf255420"}, {"BlockType": "WORD", "Confidence": 99.37181091308594, "Text": "GL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05321763455867767, "Height": 0.01032671146094799, "Left": 0.801838219165802, "Top": 0.6934472918510437}, "Polygon": [{"X": 0.801838219165802, "Y": 0.6934563517570496}, {"X": 0.8550530076026917, "Y": 0.6934472918510437}, {"X": 0.8550558686256409, "Y": 0.7037649750709534}, {"X": 0.801841139793396, "Y": 0.7037740349769592}]}, "Id": "0490ebb5-eef3-4f00-9adf-d1644222e547"}, {"BlockType": "WORD", "Confidence": 88.17223358154297, "Text": "Quantity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13706299662590027, "Height": 0.011971472762525082, "Left": 0.07737191021442413, "Top": 0.7175432443618774}, "Polygon": [{"X": 0.07737191021442413, "Y": 0.7175663709640503}, {"X": 0.21443119645118713, "Y": 0.7175432443618774}, {"X": 0.2144349068403244, "Y": 0.729491651058197}, {"X": 0.07737570255994797, "Y": 0.7295147180557251}]}, "Id": "d6459b95-f7be-4e18-819b-5b8b21a0c8d5"}, {"BlockType": "WORD", "Confidence": 94.9073257446289, "Text": "20.82", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08343541622161865, "Height": 0.010423454456031322, "Left": 0.22776754200458527, "Top": 0.715904951095581}, "Polygon": [{"X": 0.22776754200458527, "Y": 0.7159190773963928}, {"X": 0.31119978427886963, "Y": 0.715904951095581}, {"X": 0.3112029731273651, "Y": 0.7263143658638}, {"X": 0.22777076065540314, "Y": 0.726328432559967}]}, "Id": "5305586b-bae8-46b3-a342-6dcc854d8f95"}, {"BlockType": "WORD", "Confidence": 99.68350982666016, "Text": "Ton", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05892069265246391, "Height": 0.0102972611784935, "Left": 0.32039958238601685, "Top": 0.7152701616287231}, "Polygon": [{"X": 0.32039958238601685, "Y": 0.7152801156044006}, {"X": 0.37931716442108154, "Y": 0.7152701616287231}, {"X": 0.37932026386260986, "Y": 0.7255575060844421}, {"X": 0.32040271162986755, "Y": 0.7255674004554749}]}, "Id": "cd40a537-47c5-40f5-baa6-4d953032533c"}, {"BlockType": "WORD", "Confidence": 65.16262817382812, "Text": "<PERSON>gh<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20449165999889374, "Height": 0.012793776579201221, "Left": 0.039394132792949677, "Top": 0.8313096165657043}, "Polygon": [{"X": 0.039394132792949677, "Y": 0.8313432931900024}, {"X": 0.24388185143470764, "Y": 0.8313096165657043}, {"X": 0.243885800242424, "Y": 0.84406977891922}, {"X": 0.039398204535245895, "Y": 0.8441033363342285}]}, "Id": "c4deb86a-d9e4-4d94-bcd8-158a0125c54e"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "TOM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08470317721366882, "Height": 0.010214433073997498, "Left": 0.25363439321517944, "Top": 0.831631064414978}, "Polygon": [{"X": 0.25363439321517944, "Y": 0.8316450119018555}, {"X": 0.33833447098731995, "Y": 0.831631064414978}, {"X": 0.33833757042884827, "Y": 0.841831624507904}, {"X": 0.25363755226135254, "Y": 0.8418455123901367}]}, "Id": "57b09756-b564-4c4f-960d-0a20c5da16c6"}, {"BlockType": "WORD", "Confidence": 87.79316711425781, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10485194623470306, "Height": 0.010378104634582996, "Left": 0.08489537239074707, "Top": 0.8594906330108643}, "Polygon": [{"X": 0.08489537239074707, "Y": 0.8595077991485596}, {"X": 0.18974409997463226, "Y": 0.8594906330108643}, {"X": 0.18974731862545013, "Y": 0.8698516488075256}, {"X": 0.08489865809679031, "Y": 0.8698687553405762}]}, "Id": "6c35cb7e-f50d-4ceb-a0df-d4342a8cda13"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045392245054244995, "Height": 0.014711531810462475, "Left": 0.04302996024489403, "Top": 0.9084711670875549}, "Polygon": [{"X": 0.04302996024489403, "Y": 0.9084784984588623}, {"X": 0.08841755241155624, "Y": 0.9084711670875549}, {"X": 0.08842220157384872, "Y": 0.9231753349304199}, {"X": 0.04303465038537979, "Y": 0.9231826663017273}]}, "Id": "96be0a66-60f2-4c29-b43b-adbb10f70ee6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "signing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13700665533542633, "Height": 0.015251388773322105, "Left": 0.10057543218135834, "Top": 0.9078547358512878}, "Polygon": [{"X": 0.10057543218135834, "Y": 0.9078769087791443}, {"X": 0.2375773787498474, "Y": 0.9078547358512878}, {"X": 0.23758208751678467, "Y": 0.923084020614624}, {"X": 0.10058024525642395, "Y": 0.9231060743331909}]}, "Id": "341f57d0-cf94-45d0-9d4f-3f41ebd3022e"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "above,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1310214251279831, "Height": 0.014221318997442722, "Left": 0.25106629729270935, "Top": 0.9082092046737671}, "Polygon": [{"X": 0.25106629729270935, "Y": 0.9082304239273071}, {"X": 0.3820834457874298, "Y": 0.9082092046737671}, {"X": 0.38208770751953125, "Y": 0.9224094152450562}, {"X": 0.25107067823410034, "Y": 0.9224305152893066}]}, "Id": "f34f167f-175b-48fc-b6fb-409d6ea139fa"}, {"BlockType": "WORD", "Confidence": 86.96369171142578, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008357249200344086, "Height": 0.011414271779358387, "Left": 0.39813485741615295, "Top": 0.9087066054344177}, "Polygon": [{"X": 0.39813485741615295, "Y": 0.9087079763412476}, {"X": 0.40648865699768066, "Y": 0.9087066054344177}, {"X": 0.40649208426475525, "Y": 0.9201195240020752}, {"X": 0.39813828468322754, "Y": 0.920120894908905}]}, "Id": "57f7a3d9-8ca7-4e39-b670-33f782ba438d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "declare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14889495074748993, "Height": 0.012784846127033234, "Left": 0.4201691150665283, "Top": 0.9083247184753418}, "Polygon": [{"X": 0.4201691150665283, "Y": 0.908348798751831}, {"X": 0.5690603256225586, "Y": 0.9083247184753418}, {"X": 0.5690640807151794, "Y": 0.92108553647995}, {"X": 0.42017292976379395, "Y": 0.9211095571517944}]}, "Id": "09499b07-a402-41c5-8966-e7a6f80b07b9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08393356204032898, "Height": 0.012578150257468224, "Left": 0.5823377966880798, "Top": 0.9089012742042542}, "Polygon": [{"X": 0.5823377966880798, "Y": 0.9089148640632629}, {"X": 0.6662677526473999, "Y": 0.9089012742042542}, {"X": 0.6662713289260864, "Y": 0.9214658737182617}, {"X": 0.5823414325714111, "Y": 0.9214794635772705}]}, "Id": "5c4cab62-3093-4774-9211-6c357ebbd305"}, {"BlockType": "WORD", "Confidence": 91.90290069580078, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009102698415517807, "Height": 0.0117486035451293, "Left": 0.6795914769172668, "Top": 0.9097269773483276}, "Polygon": [{"X": 0.6795914769172668, "Y": 0.909728467464447}, {"X": 0.6886907815933228, "Y": 0.9097269773483276}, {"X": 0.6886941194534302, "Y": 0.9214740991592407}, {"X": 0.6795948147773743, "Y": 0.9214755892753601}]}, "Id": "1efbf01f-b454-4bad-afd2-9f9af9684be3"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "did", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06298284977674484, "Height": 0.01300761103630066, "Left": 0.7032620906829834, "Top": 0.9089268445968628}, "Polygon": [{"X": 0.7032620906829834, "Y": 0.9089370369911194}, {"X": 0.7662412524223328, "Y": 0.9089268445968628}, {"X": 0.7662449479103088, "Y": 0.9219242930412292}, {"X": 0.7032657861709595, "Y": 0.9219344258308411}]}, "Id": "0ac35125-8e43-4bad-8845-d80c67ae6ec7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "NOT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09143085777759552, "Height": 0.012833938002586365, "Left": 0.7826160192489624, "Top": 0.9097749590873718}, "Polygon": [{"X": 0.7826160192489624, "Y": 0.9097897410392761}, {"X": 0.874043345451355, "Y": 0.9097749590873718}, {"X": 0.8740469217300415, "Y": 0.9225941896438599}, {"X": 0.7826196551322937, "Y": 0.9226089119911194}]}, "Id": "f3d26336-43d7-4f52-980e-d378c55dfcbc"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "deposit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14404788613319397, "Height": 0.015115861780941486, "Left": 0.04303944110870361, "Top": 0.9277068376541138}, "Polygon": [{"X": 0.04303944110870361, "Y": 0.9277300834655762}, {"X": 0.18708263337612152, "Y": 0.9277068376541138}, {"X": 0.18708732724189758, "Y": 0.9427995681762695}, {"X": 0.043044254183769226, "Y": 0.9428226947784424}]}, "Id": "91a4fa4a-9f9e-41ea-96cc-3c11656b20d6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06903129816055298, "Height": 0.011681457981467247, "Left": 0.19885766506195068, "Top": 0.9309154152870178}, "Polygon": [{"X": 0.19885766506195068, "Y": 0.930926501750946}, {"X": 0.26788538694381714, "Y": 0.9309154152870178}, {"X": 0.26788896322250366, "Y": 0.9425857663154602}, {"X": 0.1988612860441208, "Y": 0.9425968527793884}]}, "Id": "6ef02f75-219f-4e9c-9629-db82cb31dbb6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PROHIBITED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24629426002502441, "Height": 0.012482093647122383, "Left": 0.2817607820034027, "Top": 0.9284123778343201}, "Polygon": [{"X": 0.2817607820034027, "Y": 0.9284520745277405}, {"X": 0.5280513763427734, "Y": 0.9284123778343201}, {"X": 0.5280550122261047, "Y": 0.9408549070358276}, {"X": 0.28176459670066833, "Y": 0.9408944845199585}]}, "Id": "9fc159a4-6100-478d-b9ad-6164803c7690"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "WASTES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1706537902355194, "Height": 0.012139743193984032, "Left": 0.5399323105812073, "Top": 0.9296246767044067}, "Polygon": [{"X": 0.5399323105812073, "Y": 0.9296521544456482}, {"X": 0.7105826139450073, "Y": 0.9296246767044067}, {"X": 0.7105860710144043, "Y": 0.9417369961738586}, {"X": 0.539935827255249, "Y": 0.9417644143104553}]}, "Id": "31cee2b2-6590-4550-a4c4-feb40c7c7985"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "de4573f6-4c65-4c6d-94fb-7bd15c33978a", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "de4573f6-4c65-4c6d-94fb-7bd15c33978a", "content-type": "application/x-amz-json-1.1", "content-length": "66030", "date": "Wed, 24 Sep 2025 12:46:08 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "7e70e876-b94d-4983-b726-50db353bf9e1", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:10 GMT", "content-type": "application/json", "content-length": "744", "connection": "keep-alive", "x-amzn-requestid": "7e70e876-b94d-4983-b726-50db353bf9e1"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have one page. It's a scale ticket. It contains scale info: Ticket number, Date, Time, Truck, Trailer, Destination, O, D, Seal, Gross, Tare, Net, Tons, etc. So doc_type: scale_ticket. It's not a continuation, just first page. So output a list with one object: page_no 1, doc_type \"scale_ticket\". Use tool classify_logistics_doc_type."}}}, {"toolUse": {"toolUseId": "tooluse_qkcDBiflR7e3mrr3fN5Y0g", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2766, "outputTokens": 106, "totalTokens": 2872}, "metrics": {"latencyMs": 760}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/dafccb79_F4RZ1UDKLCELJG8T6R9G.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n*1396215*\nCasella Recycling of New England\nA Division of Casella Waste Systems Inc\n617-242-7746P 14 SCALE HOUSE617-242-3649F\nBUNKER HILL INDI STRIAL PARK\nTicket: 1396215\nDate: 9/11/2025\nTime 16:09:33- - 16:38:36\nCustomer KO00508 EARTHWATCH WASTE SYSTEM\nTruck: 2310\nTrailer: TA205286\nDestination: A/A\nP.O.: EARTH091125-3\nD.O.: 2522607\nSeal: 531829\nComment F7 TRANSPORTATION\nGross 74220 L Out Scale 3\nTare: 32580 L In Scale 3\nNet: 41610 L\nTons 20 82\nMaterials & Services\nOrigin NA/NON APPLICABLE\nMaterial 539-1/OBGN MIX COMMODITIES GL\nQuantity: 20.82 Ton\nWeighmaster TOM\nDriver\nBy signing above, I declare that I did NOT\ndeposit any PROHIBITED WASTES\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}