#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create an HTML version of the token analysis report for better viewing.
"""

import csv
import os
from pathlib import Path


def create_html_report():
    """Create an HTML version of the CSV report."""
    csv_file = "/home/<USER>/Desktop/test_ligistically/Augment_test/token_analysis_report.csv"
    html_file = "/home/<USER>/Desktop/test_ligistically/Augment_test/token_analysis_report.html"
    
    if not os.path.exists(csv_file):
        print(f"Error: CSV file not found at {csv_file}")
        return
    
    # Read CSV data
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        rows = list(reader)
    
    # Create HTML content
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token Analysis Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .summary {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #e8f4f8;
        }
        .number {
            text-align: right;
        }
        a {
            color: #2196F3;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-box {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Token Analysis Report</h1>
        
        <div class="summary">
            <h3>Summary Statistics</h3>
            <div class="stats">
                <div class="stat-box">
                    <div class="stat-number">146</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">409,917</div>
                    <div class="stat-label">Total Tokens</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">376,356</div>
                    <div class="stat-label">Input Tokens</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">33,561</div>
                    <div class="stat-label">Output Tokens</div>
                </div>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>File Name</th>
                    <th>Input Tokens</th>
                    <th>Output Tokens</th>
                    <th>Total Tokens</th>
                </tr>
            </thead>
            <tbody>
"""
    
    # Add table rows
    for row in rows:
        # Extract filename from hyperlink
        filename = row['File Name (Hyperlink)']
        if '"' in filename:
            # Extract filename from hyperlink formula
            parts = filename.split('"')
            if len(parts) >= 4:
                file_path = parts[1].replace('file:///', '')
                filename_only = parts[3]
                link = f'<a href="file:///{file_path}" title="Open {filename_only}">{filename_only}</a>'
            else:
                link = filename
        else:
            link = filename
        
        html_content += f"""
                <tr>
                    <td>{link}</td>
                    <td class="number">{row['Input Tokens']}</td>
                    <td class="number">{row['Output Tokens']}</td>
                    <td class="number">{row['Total Tokens']}</td>
                </tr>"""
    
    html_content += """
            </tbody>
        </table>
        
        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
            Generated from JSON files in the output folder
        </div>
    </div>
</body>
</html>"""
    
    # Write HTML file
    with open(html_file, 'w', encoding='utf-8') as file:
        file.write(html_content)
    
    print(f"HTML report created successfully: {html_file}")
    print("You can open this file in any web browser to view the report with clickable links.")


if __name__ == "__main__":
    create_html_report()
