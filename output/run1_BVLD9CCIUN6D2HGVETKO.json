{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 1.2732678023041899e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "c0f2a2c0-31d1-4a48-a0d1-bfab2e951dee", "Relationships": [{"Type": "CHILD", "Ids": ["6450181b-b4ef-48e1-a9be-44f9883ec7cd", "c99df32a-affd-4462-95bc-1c71e9c9e3d7", "2dbec490-cfb9-4761-95d9-7fc006678db6", "830c5d7a-f0c0-49ff-aa9b-07ac31b7b66b", "85ac39c1-d00c-4637-b8a1-9c49cfad4c80", "8edd139b-6840-4009-976e-70b59068bff8", "385409b1-f182-474b-9394-5f11e53fc29e", "05ddd83c-98a8-4a68-80ce-43dce502d8e5", "dad79517-177b-40d3-91c3-6de5da3e7e6a", "64fb4cf3-8c9d-4284-a571-a6643dd581a3", "dbda231a-40e1-4906-9232-9198edb58eb1", "5069b119-12b2-4ca7-b59f-2707bda51f3f", "ca2af10c-4e99-4a81-9ecd-3f805f227acb", "0583f2d5-d7c5-4c36-be0c-0ae489e9d9dd", "efcca45c-f543-4ac4-b5f2-6407e0d02bcb", "7ad75b6c-c91d-4e91-a8c7-4ece132ade7e", "4411a700-77a3-423b-b2f9-a1013f1205bf", "a025f164-d746-4289-9c58-5c771e74df10", "bfbafb4f-76ed-43e5-8379-66a77036d2bd", "afaac9ad-b90a-4150-808e-607ccbdd936d", "24871f40-0a78-472c-a079-9bc7a59c1459", "5b716f94-ccf9-40fe-a0c9-068c89184c4c", "4bf17fb4-6615-48db-aa36-f8ae42958920", "39f23e09-ff65-42b3-9dbd-c29e5bbf5694", "fdb091eb-3495-4f6a-b359-4ef162996816", "38eda110-dff0-4b73-8305-f9b799e5befa", "4f123b6e-7c99-433b-98df-1bc0ca0a3610", "911f5c7c-d61a-4a84-ae7e-a8470757eeb9"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "06/26/2024", "Geometry": {"BoundingBox": {"Width": 0.09022097289562225, "Height": 0.011148692108690739, "Left": 0.7480676770210266, "Top": 0.06161301210522652}, "Polygon": [{"X": 0.7480676770210266, "Y": 0.06161351501941681}, {"X": 0.8382886648178101, "Y": 0.06161301210522652}, {"X": 0.8382886648178101, "Y": 0.07276121526956558}, {"X": 0.7480676770210266, "Y": 0.07276170700788498}]}, "Id": "6450181b-b4ef-48e1-a9be-44f9883ec7cd", "Relationships": [{"Type": "CHILD", "Ids": ["0f7b9974-b01f-4374-b030-391c4837c1eb"]}]}, {"BlockType": "LINE", "Confidence": 98.55887603759766, "Text": "SprintPay", "Geometry": {"BoundingBox": {"Width": 0.27212727069854736, "Height": 0.04931158944964409, "Left": 0.12613733112812042, "Top": 0.06634211540222168}, "Polygon": [{"X": 0.12613733112812042, "Y": 0.06634361296892166}, {"X": 0.3982645273208618, "Y": 0.06634211540222168}, {"X": 0.3982645869255066, "Y": 0.11565239727497101}, {"X": 0.12613743543624878, "Y": 0.11565370112657547}]}, "Id": "c99df32a-affd-4462-95bc-1c71e9c9e3d7", "Relationships": [{"Type": "CHILD", "Ids": ["c12d5cc5-6cd5-4f1f-8d7d-812b773867ed"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "Powered by", "Geometry": {"BoundingBox": {"Width": 0.05359883978962898, "Height": 0.008025945164263248, "Left": 0.33838003873825073, "Top": 0.12135694921016693}, "Polygon": [{"X": 0.33838003873825073, "Y": 0.12135720252990723}, {"X": 0.3919788599014282, "Y": 0.12135694921016693}, {"X": 0.3919788599014282, "Y": 0.1293826401233673}, {"X": 0.33838003873825073, "Y": 0.1293828934431076}]}, "Id": "2dbec490-cfb9-4761-95d9-7fc006678db6", "Relationships": [{"Type": "CHILD", "Ids": ["dc8c97c9-d199-40b4-9de3-74ee9a27959f", "88ae8c39-d857-4c9d-9156-0a54ab0acec4"]}]}, {"BlockType": "LINE", "Confidence": 99.716796875, "Text": "cargosprint", "Geometry": {"BoundingBox": {"Width": 0.13479118049144745, "Height": 0.01950577273964882, "Left": 0.2571486532688141, "Top": 0.12971654534339905}, "Polygon": [{"X": 0.2571486532688141, "Y": 0.1297171711921692}, {"X": 0.39193978905677795, "Y": 0.12971654534339905}, {"X": 0.39193981885910034, "Y": 0.1492217481136322}, {"X": 0.2571486830711365, "Y": 0.14922232925891876}]}, "Id": "830c5d7a-f0c0-49ff-aa9b-07ac31b7b66b", "Relationships": [{"Type": "CHILD", "Ids": ["47252c28-c297-4146-9fe8-21998e905de8"]}]}, {"BlockType": "LINE", "Confidence": 99.76971435546875, "Text": "Reference #", "Geometry": {"BoundingBox": {"Width": 0.10225515812635422, "Height": 0.011058438569307327, "Left": 0.7458059191703796, "Top": 0.1428101360797882}, "Polygon": [{"X": 0.7458059191703796, "Y": 0.14281058311462402}, {"X": 0.8480610847473145, "Y": 0.1428101360797882}, {"X": 0.8480610847473145, "Y": 0.15386813879013062}, {"X": 0.7458059191703796, "Y": 0.15386857092380524}]}, "Id": "85ac39c1-d00c-4637-b8a1-9c49cfad4c80", "Relationships": [{"Type": "CHILD", "Ids": ["3c69a78c-85fd-4e6b-aac8-6226cdd13257", "ca4e059d-72b5-42f5-a157-1380098c5a15"]}]}, {"BlockType": "LINE", "Confidence": 99.775390625, "Text": "R-14067232", "Geometry": {"BoundingBox": {"Width": 0.09777538478374481, "Height": 0.011019112542271614, "Left": 0.7488589286804199, "Top": 0.1614106446504593}, "Polygon": [{"X": 0.7488589286804199, "Y": 0.16141106188297272}, {"X": 0.8466343283653259, "Y": 0.1614106446504593}, {"X": 0.8466343283653259, "Y": 0.17242936789989471}, {"X": 0.7488589286804199, "Y": 0.17242977023124695}]}, "Id": "8edd139b-6840-4009-976e-70b59068bff8", "Relationships": [{"Type": "CHILD", "Ids": ["026a8037-9752-4c79-8b7d-e99befd66c5a"]}]}, {"BlockType": "LINE", "Confidence": 99.81099700927734, "Text": "ACH PAYMENT NOTIFICATION", "Geometry": {"BoundingBox": {"Width": 0.3356643617153168, "Height": 0.013525167480111122, "Left": 0.12659500539302826, "Top": 0.1927025020122528}, "Polygon": [{"X": 0.12659500539302826, "Y": 0.1927037537097931}, {"X": 0.46225935220718384, "Y": 0.1927025020122528}, {"X": 0.4622593820095062, "Y": 0.20622649788856506}, {"X": 0.12659503519535065, "Y": 0.20622767508029938}]}, "Id": "385409b1-f182-474b-9394-5f11e53fc29e", "Relationships": [{"Type": "CHILD", "Ids": ["1022fe63-44ad-4e43-93ae-e06746629552", "0dac2647-b236-4bbb-b788-99cd306c38e3", "c17774da-4ac9-4f53-9f3b-d769ed681f9f"]}]}, {"BlockType": "LINE", "Confidence": 99.90187072753906, "Text": "Dear UPS SCS BUF DNMI", "Geometry": {"BoundingBox": {"Width": 0.2389840930700302, "Height": 0.015146461315453053, "Left": 0.35570210218429565, "Top": 0.24431344866752625}, "Polygon": [{"X": 0.35570210218429565, "Y": 0.24431414902210236}, {"X": 0.5946862101554871, "Y": 0.24431344866752625}, {"X": 0.5946862101554871, "Y": 0.2594592571258545}, {"X": 0.35570213198661804, "Y": 0.259459912776947}]}, "Id": "05ddd83c-98a8-4a68-80ce-43dce502d8e5", "Relationships": [{"Type": "CHILD", "Ids": ["4b42d34d-c7dd-4da6-be5f-7732a2cbcb9e", "b95a8ef0-c6c9-48de-b716-2dcf47c7c205", "3968ef54-7a79-407a-87ea-120c88bab860", "f83d3470-6ec2-4e47-ae55-db99f73de976", "1405a416-0ae5-40bf-9bf0-22962cce30fa"]}]}, {"BlockType": "LINE", "Confidence": 99.86539459228516, "Text": "An APPROVED ACH from CargoSprint is being sent to Sunjin Shipping (U.S.A.), Inc.", "Geometry": {"BoundingBox": {"Width": 0.75434410572052, "Height": 0.016217052936553955, "Left": 0.13621346652507782, "Top": 0.2899135649204254}, "Polygon": [{"X": 0.13621346652507782, "Y": 0.2899152934551239}, {"X": 0.8905575275421143, "Y": 0.2899135649204254}, {"X": 0.8905575275421143, "Y": 0.3061290681362152}, {"X": 0.1362134963274002, "Y": 0.30613061785697937}]}, "Id": "dad79517-177b-40d3-91c3-6de5da3e7e6a", "Relationships": [{"Type": "CHILD", "Ids": ["7e6b0bf7-9e2e-4063-b703-138c56b26108", "ee56dd8b-1ff9-4085-94b1-c559ed9ddbe5", "fc26c256-f9bd-4c2f-b5ac-a2db4198a86f", "bc25318f-1730-4a06-976e-0e8ca4cd27a8", "260a86af-8a85-47d5-adf0-bcbf72a0d073", "ab48cc2e-edbe-46ee-8eb5-f6ddb353038c", "ca901989-768c-4caa-a6f2-d511d681450d", "c3dd8f37-d74f-42af-b3f7-cc73d26a45ac", "1ddcde3e-9ac3-4cdf-b7cd-df4809161147", "0b304490-695f-456a-b883-6476c639319f", "14846f53-8c3a-4c1c-8368-33fd4fe6cf3b", "cd2f7d98-c686-4e6e-97c1-8f532b4ed994", "d3c69df9-5471-4755-aed5-5390c4f5813f"]}]}, {"BlockType": "LINE", "Confidence": 99.95389556884766, "Text": "ACH PAYMENTS DETAILS", "Geometry": {"BoundingBox": {"Width": 0.22517545521259308, "Height": 0.010729601606726646, "Left": 0.08407315611839294, "Top": 0.4085387587547302}, "Polygon": [{"X": 0.08407315611839294, "Y": 0.4085388779640198}, {"X": 0.30924859642982483, "Y": 0.4085387587547302}, {"X": 0.3092486262321472, "Y": 0.41926825046539307}, {"X": 0.08407318592071533, "Y": 0.41926833987236023}]}, "Id": "64fb4cf3-8c9d-4284-a571-a6643dd581a3", "Relationships": [{"Type": "CHILD", "Ids": ["baff31c5-af36-4cd7-8de4-aef0c89dc863", "d8a8566b-327f-4073-8369-c209108a1d02", "1e5e94ba-c3b2-4bc3-87ce-4cea8503c5cd"]}]}, {"BlockType": "LINE", "Confidence": 99.91702270507812, "Text": "ACH Confirmation Number:", "Geometry": {"BoundingBox": {"Width": 0.21845152974128723, "Height": 0.011246479116380215, "Left": 0.05231497436761856, "Top": 0.4372398853302002}, "Polygon": [{"X": 0.05231497436761856, "Y": 0.4372399151325226}, {"X": 0.2707664668560028, "Y": 0.4372398853302002}, {"X": 0.2707664966583252, "Y": 0.4484863579273224}, {"X": 0.05231500044465065, "Y": 0.4484863579273224}]}, "Id": "dbda231a-40e1-4906-9232-9198edb58eb1", "Relationships": [{"Type": "CHILD", "Ids": ["7c300fd7-09f0-4541-ac33-eaf97bb7d4ce", "8bbc3fa4-1fcf-4126-90e6-a1a29946c691", "d635eadd-79cf-4a67-a054-4432c88c4d1f"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Estimated Delivery Date:", "Geometry": {"BoundingBox": {"Width": 0.19629479944705963, "Height": 0.013817238621413708, "Left": 0.051670484244823456, "Top": 0.4640454351902008}, "Polygon": [{"X": 0.051670484244823456, "Y": 0.4640454351902008}, {"X": 0.2479652613401413, "Y": 0.4640454649925232}, {"X": 0.24796529114246368, "Y": 0.4778626561164856}, {"X": 0.05167051777243614, "Y": 0.4778625965118408}]}, "Id": "5069b119-12b2-4ca7-b59f-2707bda51f3f", "Relationships": [{"Type": "CHILD", "Ids": ["5ef321f9-1475-48e3-a56d-f5a256e631b3", "5ce96fe1-7018-4cd4-8109-9250e7236331", "770f18df-0d9b-4b8a-9942-b63c5359bb1a"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Customer Reference", "Geometry": {"BoundingBox": {"Width": 0.17575876414775848, "Height": 0.010952231474220753, "Left": 0.054063692688941956, "Top": 0.5054327845573425}, "Polygon": [{"X": 0.054063692688941956, "Y": 0.5054327845573425}, {"X": 0.22982244193553925, "Y": 0.5054329037666321}, {"X": 0.22982245683670044, "Y": 0.516385018825531}, {"X": 0.054063718765974045, "Y": 0.5163848400115967}]}, "Id": "ca2af10c-4e99-4a81-9ecd-3f805f227acb", "Relationships": [{"Type": "CHILD", "Ids": ["e48fa13c-e562-450d-b7f0-d9f907d0610c", "973d6415-f092-408f-a121-354dd4f229bb"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "MAWB#", "Geometry": {"BoundingBox": {"Width": 0.06798364222049713, "Height": 0.010604520328342915, "Left": 0.3236246705055237, "Top": 0.5057075023651123}, "Polygon": [{"X": 0.3236246705055237, "Y": 0.5057075023651123}, {"X": 0.3916082978248596, "Y": 0.5057075619697571}, {"X": 0.391608327627182, "Y": 0.5163120031356812}, {"X": 0.32362470030784607, "Y": 0.5163119435310364}]}, "Id": "0583f2d5-d7c5-4c36-be0c-0ae489e9d9dd", "Relationships": [{"Type": "CHILD", "Ids": ["c93a905a-f013-4fb1-a358-fd60753e7b88"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "HAWB#", "Geometry": {"BoundingBox": {"Width": 0.06580107659101486, "Height": 0.010516959242522717, "Left": 0.5618124008178711, "Top": 0.5057505965232849}, "Polygon": [{"X": 0.5618124008178711, "Y": 0.5057505965232849}, {"X": 0.6276134848594666, "Y": 0.5057506561279297}, {"X": 0.6276134848594666, "Y": 0.5162675976753235}, {"X": 0.5618124604225159, "Y": 0.5162675380706787}]}, "Id": "efcca45c-f543-4ac4-b5f2-6407e0d02bcb", "Relationships": [{"Type": "CHILD", "Ids": ["7ae453b7-50fa-4ecb-9cd7-c68854b33487"]}]}, {"BlockType": "LINE", "Confidence": 99.8175048828125, "Text": "Payment Issued To", "Geometry": {"BoundingBox": {"Width": 0.1636238396167755, "Height": 0.013624480925500393, "Left": 0.7600513696670532, "Top": 0.5054172277450562}, "Polygon": [{"X": 0.7600513696670532, "Y": 0.5054172277450562}, {"X": 0.9236751794815063, "Y": 0.5054173469543457}, {"X": 0.9236751794815063, "Y": 0.5190417170524597}, {"X": 0.7600513696670532, "Y": 0.5190415382385254}]}, "Id": "7ad75b6c-c91d-4e91-a8c7-4ece132ade7e", "Relationships": [{"Type": "CHILD", "Ids": ["5d7f4e14-4db2-4ed3-8ae7-e897ebd4059b", "af1b58ba-c9b5-447f-95db-7a3441289a3c", "d54ef632-c833-4204-a7fc-3d478270293f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "60013893193", "Geometry": {"BoundingBox": {"Width": 0.10981425642967224, "Height": 0.011013022623956203, "Left": 0.06048790365457535, "Top": 0.532802164554596}, "Polygon": [{"X": 0.06048790365457535, "Y": 0.532802164554596}, {"X": 0.1703021377325058, "Y": 0.5328023433685303}, {"X": 0.170302152633667, "Y": 0.5438151955604553}, {"X": 0.06048792973160744, "Y": 0.5438150763511658}]}, "Id": "4411a700-77a3-423b-b2f9-a1013f1205bf", "Relationships": [{"Type": "CHILD", "Ids": ["f38d92d3-cbfb-409c-82ac-885794e7eae4"]}]}, {"BlockType": "LINE", "Confidence": 98.6756591796875, "Text": "LAI12068", "Geometry": {"BoundingBox": {"Width": 0.07627617567777634, "Height": 0.011167123913764954, "Left": 0.32074615359306335, "Top": 0.5325800776481628}, "Polygon": [{"X": 0.32074615359306335, "Y": 0.5325800776481628}, {"X": 0.3970223069190979, "Y": 0.5325801968574524}, {"X": 0.3970223367214203, "Y": 0.5437471866607666}, {"X": 0.32074615359306335, "Y": 0.5437471270561218}]}, "Id": "a025f164-d746-4289-9c58-5c771e74df10", "Relationships": [{"Type": "CHILD", "Ids": ["c02b1293-6e11-4e22-b7ed-4358978dd73c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "60013893193", "Geometry": {"BoundingBox": {"Width": 0.10947991162538528, "Height": 0.010947811417281628, "Left": 0.555738091468811, "Top": 0.5328004360198975}, "Polygon": [{"X": 0.555738091468811, "Y": 0.5328004360198975}, {"X": 0.6652179956436157, "Y": 0.532800555229187}, {"X": 0.6652179956436157, "Y": 0.5437481999397278}, {"X": 0.555738091468811, "Y": 0.5437480807304382}]}, "Id": "bfbafb4f-76ed-43e5-8379-66a77036d2bd", "Relationships": [{"Type": "CHILD", "Ids": ["e22f221d-e79a-41f2-8541-516011382675"]}]}, {"BlockType": "LINE", "Confidence": 99.86793518066406, "Text": "Sunjin Shipping (U.", "Geometry": {"BoundingBox": {"Width": 0.15248574316501617, "Height": 0.014172070659697056, "Left": 0.7174237966537476, "Top": 0.5322420001029968}, "Polygon": [{"X": 0.7174237966537476, "Y": 0.5322420001029968}, {"X": 0.8699095249176025, "Y": 0.5322421789169312}, {"X": 0.8699095249176025, "Y": 0.5464140772819519}, {"X": 0.7174237966537476, "Y": 0.5464138388633728}]}, "Id": "afaac9ad-b90a-4150-808e-607ccbdd936d", "Relationships": [{"Type": "CHILD", "Ids": ["1c0878fc-1844-43e6-8fd2-a77830c9a7cb", "cde38e50-6457-425d-a8a4-7a9988807539", "b9a2ec2f-8869-4bc2-9e9c-aa69edfc9192"]}]}, {"BlockType": "LINE", "Confidence": 99.4032974243164, "Text": "S.A.), Inc.", "Geometry": {"BoundingBox": {"Width": 0.07772669196128845, "Height": 0.013515302911400795, "Left": 0.7175715565681458, "Top": 0.5463775992393494}, "Polygon": [{"X": 0.7175715565681458, "Y": 0.5463775992393494}, {"X": 0.7952982783317566, "Y": 0.5463776588439941}, {"X": 0.7952982783317566, "Y": 0.5598928928375244}, {"X": 0.7175715565681458, "Y": 0.5598927736282349}]}, "Id": "24871f40-0a78-472c-a079-9bc7a59c1459", "Relationships": [{"Type": "CHILD", "Ids": ["dec75f39-6352-4f97-bcae-5292ee81dc31", "e9e76ac7-deaf-4644-890f-b6d75df1717b"]}]}, {"BlockType": "LINE", "Confidence": 99.84130859375, "Text": "Payment Amount 210.00 USD", "Geometry": {"BoundingBox": {"Width": 0.2616802752017975, "Height": 0.01382230781018734, "Left": 0.6623545289039612, "Top": 0.6182228922843933}, "Polygon": [{"X": 0.6623545289039612, "Y": 0.6182228922843933}, {"X": 0.924034833908081, "Y": 0.6182235479354858}, {"X": 0.924034833908081, "Y": 0.6320452094078064}, {"X": 0.6623545289039612, "Y": 0.6320445537567139}]}, "Id": "5b716f94-ccf9-40fe-a0c9-068c89184c4c", "Relationships": [{"Type": "CHILD", "Ids": ["2dcddfeb-27f4-4ff6-a61d-4a5e06d15108", "5a66013d-46a3-4063-97f6-998183c702ba", "5edf25c8-cf85-46eb-8b17-f551c26a5ba8", "16924205-9ab6-4f6e-8151-d9c4f45ddb34"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Thank You,", "Geometry": {"BoundingBox": {"Width": 0.09955903887748718, "Height": 0.013731211423873901, "Left": 0.05084272101521492, "Top": 0.7195131182670593}, "Polygon": [{"X": 0.05084272101521492, "Y": 0.7195131182670593}, {"X": 0.15040172636508942, "Y": 0.719513475894928}, {"X": 0.1504017561674118, "Y": 0.7332442998886108}, {"X": 0.05084275081753731, "Y": 0.7332439422607422}]}, "Id": "4bf17fb4-6615-48db-aa36-f8ae42958920", "Relationships": [{"Type": "CHILD", "Ids": ["e201496b-23cc-4bcc-8f51-0bd32494ae6e", "2d18ff7c-1f40-492d-ad5a-a269a1ada0f9"]}]}, {"BlockType": "LINE", "Confidence": 99.85192108154297, "Text": "CargoSprint", "Geometry": {"BoundingBox": {"Width": 0.10420162230730057, "Height": 0.013982153497636318, "Left": 0.05128539353609085, "Top": 0.7374577522277832}, "Polygon": [{"X": 0.05128539353609085, "Y": 0.7374577522277832}, {"X": 0.15548698604106903, "Y": 0.7374581694602966}, {"X": 0.15548701584339142, "Y": 0.7514398694038391}, {"X": 0.05128542706370354, "Y": 0.7514394521713257}]}, "Id": "39f23e09-ff65-42b3-9dbd-c29e5bbf5694", "Relationships": [{"Type": "CHILD", "Ids": ["88c8628f-b3ee-4bdd-884c-0a5d6f8d4b1d"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "<EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.18427836894989014, "Height": 0.011629106476902962, "Left": 0.408019095659256, "Top": 0.9102886915206909}, "Polygon": [{"X": 0.408019095659256, "Y": 0.9102886915206909}, {"X": 0.5922974348068237, "Y": 0.9102898836135864}, {"X": 0.5922974348068237, "Y": 0.9219177961349487}, {"X": 0.408019095659256, "Y": 0.9219165444374084}]}, "Id": "fdb091eb-3495-4f6a-b359-4ef162996816", "Relationships": [{"Type": "CHILD", "Ids": ["cb7b3669-9290-484d-bc3d-48d5bbd96a56"]}]}, {"BlockType": "LINE", "Confidence": 97.15149688720703, "Text": "CargoSprint LLC I 21 Eastbrook Bend Suite # 202", "Geometry": {"BoundingBox": {"Width": 0.3683550953865051, "Height": 0.012926776893436909, "Left": 0.3125801980495453, "Top": 0.9292173385620117}, "Polygon": [{"X": 0.3125801980495453, "Y": 0.9292173385620117}, {"X": 0.6809353232383728, "Y": 0.9292198419570923}, {"X": 0.6809353232383728, "Y": 0.9421440958976746}, {"X": 0.3125802278518677, "Y": 0.9421414732933044}]}, "Id": "38eda110-dff0-4b73-8305-f9b799e5befa", "Relationships": [{"Type": "CHILD", "Ids": ["073c0fe0-d4d5-48e1-81dd-4acf0cbb68ce", "8e6f4200-e941-4d6a-9583-720d0a7cc24c", "ad2f10eb-c11f-4f5a-8165-044934cbde92", "61ec4a60-cde8-4a0d-9ce5-4c27fe4ce4b1", "b858c5d5-bd1f-4d63-ac3c-954f3c629c5a", "d4ebc6d3-00fe-46d2-9ca2-3b4e7cfa58d9", "206d7faa-03fe-42bb-853c-2b21ed05cfcb", "7ca3b009-5731-4538-bb92-f17d6ea2f42b", "823c71f1-c665-4904-8334-3952eeb5a598"]}]}, {"BlockType": "LINE", "Confidence": 67.87548065185547, "Text": "I", "Geometry": {"BoundingBox": {"Width": 0.0029623745940625668, "Height": 0.012838868424296379, "Left": 0.6862294673919678, "Top": 0.9293338060379028}, "Polygon": [{"X": 0.6862294673919678, "Y": 0.9293338060379028}, {"X": 0.6891918182373047, "Y": 0.9293338060379028}, {"X": 0.6891918182373047, "Y": 0.942172646522522}, {"X": 0.6862294673919678, "Y": 0.942172646522522}]}, "Id": "4f123b6e-7c99-433b-98df-1bc0ca0a3610", "Relationships": [{"Type": "CHILD", "Ids": ["4d313f91-3a3d-45eb-9ae1-926d1cc2007f"]}]}, {"BlockType": "LINE", "Confidence": 97.08534240722656, "Text": "Peachtree City, GA 30269 I P. 251-272-9247", "Geometry": {"BoundingBox": {"Width": 0.321940541267395, "Height": 0.013073463924229145, "Left": 0.3381504714488983, "Top": 0.9443967342376709}, "Polygon": [{"X": 0.3381504714488983, "Y": 0.9443967342376709}, {"X": 0.660090982913971, "Y": 0.9443989992141724}, {"X": 0.660090982913971, "Y": 0.957470178604126}, {"X": 0.3381505012512207, "Y": 0.9574678540229797}]}, "Id": "911f5c7c-d61a-4a84-ae7e-a8470757eeb9", "Relationships": [{"Type": "CHILD", "Ids": ["5b72fe44-ede9-4e51-9aed-6f33790bc9e7", "aa61596b-2d8f-4c95-b60f-3d93c779b928", "f8738ee4-7538-409d-833f-cb98fc4cbd8f", "6fe2bca3-df39-41e9-8bc8-47198b5e1653", "8f96964e-48bf-4d43-a43e-b5ea84ff6840", "75dd470c-bec1-4781-8b02-3107c01e55ad", "2ad1e2ca-1a24-4bd7-aeee-bf7daf29e0e4"]}]}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "06/26/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09022097289562225, "Height": 0.011148692108690739, "Left": 0.7480676770210266, "Top": 0.06161301210522652}, "Polygon": [{"X": 0.7480676770210266, "Y": 0.06161351501941681}, {"X": 0.8382886648178101, "Y": 0.06161301210522652}, {"X": 0.8382886648178101, "Y": 0.07276121526956558}, {"X": 0.7480676770210266, "Y": 0.07276170700788498}]}, "Id": "0f7b9974-b01f-4374-b030-391c4837c1eb"}, {"BlockType": "WORD", "Confidence": 98.55887603759766, "Text": "SprintPay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.27212727069854736, "Height": 0.04931158944964409, "Left": 0.12613733112812042, "Top": 0.06634211540222168}, "Polygon": [{"X": 0.12613733112812042, "Y": 0.06634361296892166}, {"X": 0.3982645273208618, "Y": 0.06634211540222168}, {"X": 0.3982645869255066, "Y": 0.11565239727497101}, {"X": 0.12613743543624878, "Y": 0.11565370112657547}]}, "Id": "c12d5cc5-6cd5-4f1f-8d7d-812b773867ed"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "Powered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039466407150030136, "Height": 0.006622813176363707, "Left": 0.33838003873825073, "Top": 0.12151313573122025}, "Polygon": [{"X": 0.33838003873825073, "Y": 0.12151332199573517}, {"X": 0.3778464198112488, "Y": 0.12151313573122025}, {"X": 0.3778464198112488, "Y": 0.1281357705593109}, {"X": 0.33838003873825073, "Y": 0.12813594937324524}]}, "Id": "dc8c97c9-d199-40b4-9de3-74ee9a27959f"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011142514646053314, "Height": 0.00802574958652258, "Left": 0.3808363676071167, "Top": 0.12135694921016693}, "Polygon": [{"X": 0.3808363676071167, "Y": 0.12135700136423111}, {"X": 0.3919788599014282, "Y": 0.12135694921016693}, {"X": 0.3919788599014282, "Y": 0.1293826401233673}, {"X": 0.3808363676071167, "Y": 0.12938269972801208}]}, "Id": "88ae8c39-d857-4c9d-9156-0a54ab0acec4"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "cargosprint", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13479118049144745, "Height": 0.01950577273964882, "Left": 0.2571486532688141, "Top": 0.12971654534339905}, "Polygon": [{"X": 0.2571486532688141, "Y": 0.1297171711921692}, {"X": 0.39193978905677795, "Y": 0.12971654534339905}, {"X": 0.39193981885910034, "Y": 0.1492217481136322}, {"X": 0.2571486830711365, "Y": 0.14922232925891876}]}, "Id": "47252c28-c297-4146-9fe8-21998e905de8"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Reference", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08664140105247498, "Height": 0.011058369651436806, "Left": 0.7458059191703796, "Top": 0.14281019568443298}, "Polygon": [{"X": 0.7458059191703796, "Y": 0.14281058311462402}, {"X": 0.832447350025177, "Y": 0.14281019568443298}, {"X": 0.832447350025177, "Y": 0.1538681983947754}, {"X": 0.7458059191703796, "Y": 0.15386857092380524}]}, "Id": "3c69a78c-85fd-4e6b-aac8-6226cdd13257"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010661869309842587, "Height": 0.01021706871688366, "Left": 0.8373992443084717, "Top": 0.1434200257062912}, "Polygon": [{"X": 0.8373992443084717, "Y": 0.14342007040977478}, {"X": 0.8480610847473145, "Y": 0.1434200257062912}, {"X": 0.8480610847473145, "Y": 0.15363705158233643}, {"X": 0.8373992443084717, "Y": 0.15363709628582}]}, "Id": "ca4e059d-72b5-42f5-a157-1380098c5a15"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "R-14067232", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09777538478374481, "Height": 0.011019112542271614, "Left": 0.7488589286804199, "Top": 0.1614106446504593}, "Polygon": [{"X": 0.7488589286804199, "Y": 0.16141106188297272}, {"X": 0.8466343283653259, "Y": 0.1614106446504593}, {"X": 0.8466343283653259, "Y": 0.17242936789989471}, {"X": 0.7488589286804199, "Y": 0.17242977023124695}]}, "Id": "026a8037-9752-4c79-8b7d-e99befd66c5a"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "ACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04866885393857956, "Height": 0.013495756313204765, "Left": 0.12659500539302826, "Top": 0.19270357489585876}, "Polygon": [{"X": 0.12659500539302826, "Y": 0.1927037537097931}, {"X": 0.17526382207870483, "Y": 0.19270357489585876}, {"X": 0.17526385188102722, "Y": 0.20619915425777435}, {"X": 0.12659503519535065, "Y": 0.20619931817054749}]}, "Id": "1022fe63-44ad-4e43-93ae-e06746629552"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PAYMENT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1116209402680397, "Height": 0.013470886275172234, "Left": 0.18320243060588837, "Top": 0.19275659322738647}, "Polygon": [{"X": 0.18320243060588837, "Y": 0.1927570104598999}, {"X": 0.2948233485221863, "Y": 0.19275659322738647}, {"X": 0.29482337832450867, "Y": 0.20622707903385162}, {"X": 0.18320246040821075, "Y": 0.20622748136520386}]}, "Id": "0dac2647-b236-4bbb-b788-99cd306c38e3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NOTIFICATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1609552651643753, "Height": 0.013380993157625198, "Left": 0.3013041019439697, "Top": 0.19281326234340668}, "Polygon": [{"X": 0.3013041019439697, "Y": 0.19281385838985443}, {"X": 0.46225935220718384, "Y": 0.19281326234340668}, {"X": 0.4622593820095062, "Y": 0.2061936855316162}, {"X": 0.3013041317462921, "Y": 0.20619425177574158}]}, "Id": "c17774da-4ac9-4f53-9f3b-d769ed681f9f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Dear", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03986407816410065, "Height": 0.010534010827541351, "Left": 0.35570210218429565, "Top": 0.24892589449882507}, "Polygon": [{"X": 0.35570210218429565, "Y": 0.24892601370811462}, {"X": 0.3955661654472351, "Y": 0.24892589449882507}, {"X": 0.3955661952495575, "Y": 0.25945979356765747}, {"X": 0.35570213198661804, "Y": 0.259459912776947}]}, "Id": "4b42d34d-c7dd-4da6-be5f-7732a2cbcb9e"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "UPS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03633899614214897, "Height": 0.011615986004471779, "Left": 0.42029666900634766, "Top": 0.24451732635498047}, "Polygon": [{"X": 0.42029666900634766, "Y": 0.24451744556427002}, {"X": 0.45663565397262573, "Y": 0.24451732635498047}, {"X": 0.45663565397262573, "Y": 0.25613322854042053}, {"X": 0.42029666900634766, "Y": 0.2561333179473877}]}, "Id": "b95a8ef0-c6c9-48de-b716-2dcf47c7c205"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "SCS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0363462008535862, "Height": 0.011637159623205662, "Left": 0.462344765663147, "Top": 0.24431373178958893}, "Polygon": [{"X": 0.462344765663147, "Y": 0.24431383609771729}, {"X": 0.49869096279144287, "Y": 0.24431373178958893}, {"X": 0.49869096279144287, "Y": 0.25595077872276306}, {"X": 0.46234479546546936, "Y": 0.2559508979320526}]}, "Id": "3968ef54-7a79-407a-87ea-120c88bab860"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "BUF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035498760640621185, "Height": 0.011475365608930588, "Left": 0.5094575881958008, "Top": 0.24454458057880402}, "Polygon": [{"X": 0.5094575881958008, "Y": 0.24454468488693237}, {"X": 0.544956386089325, "Y": 0.24454458057880402}, {"X": 0.544956386089325, "Y": 0.25601986050605774}, {"X": 0.5094576478004456, "Y": 0.2560199499130249}]}, "Id": "f83d3470-6ec2-4e47-ae55-db99f73de976"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "DNMI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04442715272307396, "Height": 0.010976062156260014, "Left": 0.5502590537071228, "Top": 0.24474580585956573}, "Polygon": [{"X": 0.5502590537071228, "Y": 0.24474593997001648}, {"X": 0.5946862101554871, "Y": 0.24474580585956573}, {"X": 0.5946862101554871, "Y": 0.2557217478752136}, {"X": 0.5502590537071228, "Y": 0.2557218670845032}]}, "Id": "1405a416-0ae5-40bf-9bf0-22962cce30fa"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "An", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02345428243279457, "Height": 0.010738306678831577, "Left": 0.13621346652507782, "Top": 0.2926386594772339}, "Polygon": [{"X": 0.13621346652507782, "Y": 0.29263871908187866}, {"X": 0.1596677303314209, "Y": 0.2926386594772339}, {"X": 0.1596677452325821, "Y": 0.3033769130706787}, {"X": 0.1362134963274002, "Y": 0.3033769726753235}]}, "Id": "7e6b0bf7-9e2e-4063-b703-138c56b26108"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12776021659374237, "Height": 0.013125667348504066, "Left": 0.16519507765769958, "Top": 0.29056698083877563}, "Polygon": [{"X": 0.16519507765769958, "Y": 0.2905672788619995}, {"X": 0.29295527935028076, "Y": 0.29056698083877563}, {"X": 0.29295527935028076, "Y": 0.30369237065315247}, {"X": 0.16519510746002197, "Y": 0.30369263887405396}]}, "Id": "ee56dd8b-1ff9-4085-94b1-c559ed9ddbe5"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "ACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048682667315006256, "Height": 0.013027659617364407, "Left": 0.29983511567115784, "Top": 0.29051896929740906}, "Polygon": [{"X": 0.29983511567115784, "Y": 0.2905190587043762}, {"X": 0.3485177755355835, "Y": 0.29051896929740906}, {"X": 0.3485177755355835, "Y": 0.3035465180873871}, {"X": 0.2998351454734802, "Y": 0.30354660749435425}]}, "Id": "fc26c256-f9bd-4c2f-b5ac-a2db4198a86f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "from", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03971005603671074, "Height": 0.010990804061293602, "Left": 0.3588307499885559, "Top": 0.2921532094478607}, "Polygon": [{"X": 0.3588307499885559, "Y": 0.2921532988548279}, {"X": 0.39854079484939575, "Y": 0.2921532094478607}, {"X": 0.39854082465171814, "Y": 0.3031439185142517}, {"X": 0.3588307797908783, "Y": 0.30314400792121887}]}, "Id": "bc25318f-1730-4a06-976e-0e8ca4cd27a8"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "CargoSprint", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10409511625766754, "Height": 0.013931065797805786, "Left": 0.40464556217193604, "Top": 0.2921989858150482}, "Polygon": [{"X": 0.40464556217193604, "Y": 0.2921992242336273}, {"X": 0.5087406635284424, "Y": 0.2921989858150482}, {"X": 0.5087406635284424, "Y": 0.3061298429965973}, {"X": 0.4046455919742584, "Y": 0.306130051612854}]}, "Id": "260a86af-8a85-47d5-adf0-bcbf72a0d073"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01426741760224104, "Height": 0.01105420757085085, "Left": 0.5138974785804749, "Top": 0.29209110140800476}, "Polygon": [{"X": 0.5138974785804749, "Y": 0.29209113121032715}, {"X": 0.5281648635864258, "Y": 0.29209110140800476}, {"X": 0.5281648635864258, "Y": 0.30314528942108154}, {"X": 0.5138974785804749, "Y": 0.30314531922340393}]}, "Id": "ab48cc2e-edbe-46ee-8eb5-f6ddb353038c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "being", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04691169410943985, "Height": 0.014073527418076992, "Left": 0.5341104865074158, "Top": 0.29193711280822754}, "Polygon": [{"X": 0.5341104865074158, "Y": 0.2919372022151947}, {"X": 0.5810222029685974, "Y": 0.29193711280822754}, {"X": 0.5810222029685974, "Y": 0.30601054430007935}, {"X": 0.5341105461120605, "Y": 0.3060106337070465}]}, "Id": "ca901989-768c-4caa-a6f2-d511d681450d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03700614720582962, "Height": 0.009925547055900097, "Left": 0.5869336724281311, "Top": 0.2933085858821869}, "Polygon": [{"X": 0.5869336724281311, "Y": 0.29330867528915405}, {"X": 0.6239398121833801, "Y": 0.2933085858821869}, {"X": 0.6239398717880249, "Y": 0.3032340705394745}, {"X": 0.5869337320327759, "Y": 0.30323413014411926}]}, "Id": "c3dd8f37-d74f-42af-b3f7-cc73d26a45ac"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016878684982657433, "Height": 0.00999382883310318, "Left": 0.6286209225654602, "Top": 0.29315662384033203}, "Polygon": [{"X": 0.6286209225654602, "Y": 0.2931566834449768}, {"X": 0.645499587059021, "Y": 0.29315662384033203}, {"X": 0.645499587059021, "Y": 0.3031504452228546}, {"X": 0.6286209225654602, "Y": 0.303150475025177}]}, "Id": "1ddcde3e-9ac3-4cdf-b7cd-df4809161147"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Sunjin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0489959716796875, "Height": 0.014104656875133514, "Left": 0.6587896347045898, "Top": 0.2899172306060791}, "Polygon": [{"X": 0.6587896347045898, "Y": 0.28991734981536865}, {"X": 0.7077856063842773, "Y": 0.2899172306060791}, {"X": 0.7077856063842773, "Y": 0.30402180552482605}, {"X": 0.6587896347045898, "Y": 0.3040218949317932}]}, "Id": "0b304490-695f-456a-b883-6476c639319f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06941191107034683, "Height": 0.014049682766199112, "Left": 0.7136812806129456, "Top": 0.2899138033390045}, "Polygon": [{"X": 0.7136812806129456, "Y": 0.28991398215293884}, {"X": 0.7830931544303894, "Y": 0.2899138033390045}, {"X": 0.7830931544303894, "Y": 0.3039633631706238}, {"X": 0.7136812806129456, "Y": 0.30396348237991333}]}, "Id": "14846f53-8c3a-4c1c-8368-33fd4fe6cf3b"}, {"BlockType": "WORD", "Confidence": 98.80421447753906, "Text": "(U.S.A.),", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06780357658863068, "Height": 0.01376346219331026, "Left": 0.7888087034225464, "Top": 0.29010698199272156}, "Polygon": [{"X": 0.7888087034225464, "Y": 0.2901071310043335}, {"X": 0.8566122651100159, "Y": 0.29010698199272156}, {"X": 0.8566122651100159, "Y": 0.303870290517807}, {"X": 0.7888087034225464, "Y": 0.30387043952941895}]}, "Id": "cd2f7d98-c686-4e6e-97c1-8f532b4ed994"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02781505510210991, "Height": 0.011088262312114239, "Left": 0.8627424836158752, "Top": 0.29019683599472046}, "Polygon": [{"X": 0.8627424836158752, "Y": 0.29019689559936523}, {"X": 0.8905575275421143, "Y": 0.29019683599472046}, {"X": 0.8905575275421143, "Y": 0.3012850284576416}, {"X": 0.8627424836158752, "Y": 0.3012850880622864}]}, "Id": "d3c69df9-5471-4755-aed5-5390c4f5813f"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "ACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038051068782806396, "Height": 0.010713737457990646, "Left": 0.08407315611839294, "Top": 0.4085546135902405}, "Polygon": [{"X": 0.08407315611839294, "Y": 0.40855464339256287}, {"X": 0.12212420254945755, "Y": 0.4085546135902405}, {"X": 0.12212422490119934, "Y": 0.41926833987236023}, {"X": 0.08407318592071533, "Y": 0.41926833987236023}]}, "Id": "baff31c5-af36-4cd7-8de4-aef0c89dc863"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PAYMENTS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09872972220182419, "Height": 0.010695951990783215, "Left": 0.1286013275384903, "Top": 0.40854358673095703}, "Polygon": [{"X": 0.1286013275384903, "Y": 0.4085436463356018}, {"X": 0.2273310273885727, "Y": 0.40854358673095703}, {"X": 0.2273310422897339, "Y": 0.41923952102661133}, {"X": 0.12860135734081268, "Y": 0.4192395508289337}]}, "Id": "d8a8566b-327f-4073-8369-c209108a1d02"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DETAILS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07579735666513443, "Height": 0.010641004890203476, "Left": 0.2334512621164322, "Top": 0.4085387587547302}, "Polygon": [{"X": 0.2334512621164322, "Y": 0.4085387885570526}, {"X": 0.30924859642982483, "Y": 0.4085387587547302}, {"X": 0.3092486262321472, "Y": 0.4191797077655792}, {"X": 0.23345127701759338, "Y": 0.419179767370224}]}, "Id": "1e5e94ba-c3b2-4bc3-87ce-4cea8503c5cd"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "ACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03758442401885986, "Height": 0.010739765129983425, "Left": 0.05231497436761856, "Top": 0.4377465844154358}, "Polygon": [{"X": 0.05231497436761856, "Y": 0.4377465844154358}, {"X": 0.08989937603473663, "Y": 0.4377465844154358}, {"X": 0.08989939838647842, "Y": 0.4484863579273224}, {"X": 0.05231500044465065, "Y": 0.4484863579273224}]}, "Id": "7c300fd7-09f0-4541-ac33-eaf97bb7d4ce"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Confirmation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10111148655414581, "Height": 0.010987303219735622, "Left": 0.09609407931566238, "Top": 0.4372398853302002}, "Polygon": [{"X": 0.09609407931566238, "Y": 0.4372399151325226}, {"X": 0.1972055435180664, "Y": 0.4372398853302002}, {"X": 0.1972055733203888, "Y": 0.448227196931839}, {"X": 0.09609410166740417, "Y": 0.448227196931839}]}, "Id": "8bbc3fa4-1fcf-4126-90e6-a1a29946c691"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06702159345149994, "Height": 0.010926909744739532, "Left": 0.20374490320682526, "Top": 0.43735620379447937}, "Polygon": [{"X": 0.20374490320682526, "Y": 0.43735620379447937}, {"X": 0.2707664668560028, "Y": 0.43735620379447937}, {"X": 0.2707664966583252, "Y": 0.4482831060886383}, {"X": 0.20374491810798645, "Y": 0.4482831060886383}]}, "Id": "d635eadd-79cf-4a67-a054-4432c88c4d1f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Estimated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07894719392061234, "Height": 0.01120645273476839, "Left": 0.051670484244823456, "Top": 0.4640454351902008}, "Polygon": [{"X": 0.051670484244823456, "Y": 0.4640454351902008}, {"X": 0.1306176632642746, "Y": 0.4640454351902008}, {"X": 0.1306176781654358, "Y": 0.4752518832683563}, {"X": 0.051670514047145844, "Y": 0.47525185346603394}]}, "Id": "5ef321f9-1475-48e3-a56d-f5a256e631b3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Delivery", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.064275823533535, "Height": 0.01374792493879795, "Left": 0.1369875967502594, "Top": 0.4641147255897522}, "Polygon": [{"X": 0.1369875967502594, "Y": 0.4641147255897522}, {"X": 0.2012633979320526, "Y": 0.4641147255897522}, {"X": 0.201263427734375, "Y": 0.4778626561164856}, {"X": 0.1369876265525818, "Y": 0.4778626263141632}]}, "Id": "5ce96fe1-7018-4cd4-8109-9250e7236331"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040864404290914536, "Height": 0.0106777623295784, "Left": 0.20710088312625885, "Top": 0.46456223726272583}, "Polygon": [{"X": 0.20710088312625885, "Y": 0.46456223726272583}, {"X": 0.2479652613401413, "Y": 0.46456223726272583}, {"X": 0.2479652762413025, "Y": 0.47523999214172363}, {"X": 0.20710089802742004, "Y": 0.47523999214172363}]}, "Id": "770f18df-0d9b-4b8a-9942-b63c5359bb1a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08429759740829468, "Height": 0.0107585983350873, "Left": 0.054063692688941956, "Top": 0.5056175589561462}, "Polygon": [{"X": 0.054063692688941956, "Y": 0.5056175589561462}, {"X": 0.13836126029491425, "Y": 0.505617618560791}, {"X": 0.13836129009723663, "Y": 0.5163761377334595}, {"X": 0.054063718765974045, "Y": 0.5163760781288147}]}, "Id": "e48fa13c-e562-450d-b7f0-d9f907d0610c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Reference", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0861312672495842, "Height": 0.010952160693705082, "Left": 0.14369118213653564, "Top": 0.5054328441619873}, "Polygon": [{"X": 0.14369118213653564, "Y": 0.5054328441619873}, {"X": 0.22982244193553925, "Y": 0.5054329037666321}, {"X": 0.22982245683670044, "Y": 0.516385018825531}, {"X": 0.14369121193885803, "Y": 0.5163848996162415}]}, "Id": "973d6415-f092-408f-a121-354dd4f229bb"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "MAWB#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06798364222049713, "Height": 0.010604520328342915, "Left": 0.3236246705055237, "Top": 0.5057075023651123}, "Polygon": [{"X": 0.3236246705055237, "Y": 0.5057075023651123}, {"X": 0.3916082978248596, "Y": 0.5057075619697571}, {"X": 0.391608327627182, "Y": 0.5163120031356812}, {"X": 0.32362470030784607, "Y": 0.5163119435310364}]}, "Id": "c93a905a-f013-4fb1-a358-fd60753e7b88"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "HAWB#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06580107659101486, "Height": 0.010516959242522717, "Left": 0.5618124008178711, "Top": 0.5057505965232849}, "Polygon": [{"X": 0.5618124008178711, "Y": 0.5057505965232849}, {"X": 0.6276134848594666, "Y": 0.5057506561279297}, {"X": 0.6276134848594666, "Y": 0.5162675976753235}, {"X": 0.5618124604225159, "Y": 0.5162675380706787}]}, "Id": "7ae453b7-50fa-4ecb-9cd7-c68854b33487"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07473722100257874, "Height": 0.013264731504023075, "Left": 0.7600513696670532, "Top": 0.5057768821716309}, "Polygon": [{"X": 0.7600513696670532, "Y": 0.5057768821716309}, {"X": 0.8347886204719543, "Y": 0.5057769417762756}, {"X": 0.8347886204719543, "Y": 0.5190416574478149}, {"X": 0.7600513696670532, "Y": 0.5190415382385254}]}, "Id": "5d7f4e14-4db2-4ed3-8ae7-e897ebd4059b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Issued", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05603610724210739, "Height": 0.01103076059371233, "Left": 0.84117591381073, "Top": 0.5054172873497009}, "Polygon": [{"X": 0.84117591381073, "Y": 0.5054172873497009}, {"X": 0.8972119688987732, "Y": 0.5054173469543457}, {"X": 0.8972119688987732, "Y": 0.5164480805397034}, {"X": 0.84117591381073, "Y": 0.5164480209350586}]}, "Id": "af1b58ba-c9b5-447f-95db-7a3441289a3c"}, {"BlockType": "WORD", "Confidence": 99.49238586425781, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02175282873213291, "Height": 0.010916032828390598, "Left": 0.9019224047660828, "Top": 0.5056563019752502}, "Polygon": [{"X": 0.9019224047660828, "Y": 0.5056563019752502}, {"X": 0.9236751794815063, "Y": 0.505656361579895}, {"X": 0.9236751794815063, "Y": 0.5165723562240601}, {"X": 0.9019224047660828, "Y": 0.5165723562240601}]}, "Id": "d54ef632-c833-4204-a7fc-3d478270293f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "60013893193", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10981425642967224, "Height": 0.011013022623956203, "Left": 0.06048790365457535, "Top": 0.532802164554596}, "Polygon": [{"X": 0.06048790365457535, "Y": 0.532802164554596}, {"X": 0.1703021377325058, "Y": 0.5328023433685303}, {"X": 0.170302152633667, "Y": 0.5438151955604553}, {"X": 0.06048792973160744, "Y": 0.5438150763511658}]}, "Id": "f38d92d3-cbfb-409c-82ac-885794e7eae4"}, {"BlockType": "WORD", "Confidence": 98.6756591796875, "Text": "LAI12068", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07627617567777634, "Height": 0.011167123913764954, "Left": 0.32074615359306335, "Top": 0.5325800776481628}, "Polygon": [{"X": 0.32074615359306335, "Y": 0.5325800776481628}, {"X": 0.3970223069190979, "Y": 0.5325801968574524}, {"X": 0.3970223367214203, "Y": 0.5437471866607666}, {"X": 0.32074615359306335, "Y": 0.5437471270561218}]}, "Id": "c02b1293-6e11-4e22-b7ed-4358978dd73c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "60013893193", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10947991162538528, "Height": 0.010947811417281628, "Left": 0.555738091468811, "Top": 0.5328004360198975}, "Polygon": [{"X": 0.555738091468811, "Y": 0.5328004360198975}, {"X": 0.6652179956436157, "Y": 0.532800555229187}, {"X": 0.6652179956436157, "Y": 0.5437481999397278}, {"X": 0.555738091468811, "Y": 0.5437480807304382}]}, "Id": "e22f221d-e79a-41f2-8541-516011382675"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Sunjin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04915739223361015, "Height": 0.013648229651153088, "Left": 0.7174237966537476, "Top": 0.5323279500007629}, "Polygon": [{"X": 0.7174237966537476, "Y": 0.5323279500007629}, {"X": 0.7665811777114868, "Y": 0.5323280096054077}, {"X": 0.7665811777114868, "Y": 0.5459761619567871}, {"X": 0.7174237966537476, "Y": 0.5459761023521423}]}, "Id": "1c0878fc-1844-43e6-8fd2-a77830c9a7cb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06921446323394775, "Height": 0.014145062305033207, "Left": 0.7726812958717346, "Top": 0.5322689414024353}, "Polygon": [{"X": 0.7726812958717346, "Y": 0.5322689414024353}, {"X": 0.8418956995010376, "Y": 0.5322690606117249}, {"X": 0.8418956995010376, "Y": 0.5464140176773071}, {"X": 0.7726812958717346, "Y": 0.5464138984680176}]}, "Id": "cde38e50-6457-425d-a8a4-7a9988807539"}, {"BlockType": "WORD", "Confidence": 99.6436538696289, "Text": "(U.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02259385958313942, "Height": 0.01388157531619072, "Left": 0.8473156690597534, "Top": 0.5322421193122864}, "Polygon": [{"X": 0.8473156690597534, "Y": 0.5322421193122864}, {"X": 0.8699095249176025, "Y": 0.5322421789169312}, {"X": 0.8699095249176025, "Y": 0.5461236834526062}, {"X": 0.8473156690597534, "Y": 0.5461236834526062}]}, "Id": "b9a2ec2f-8869-4bc2-9e9c-aa69edfc9192"}, {"BlockType": "WORD", "Confidence": 98.84565734863281, "Text": "S.A.),", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04391137510538101, "Height": 0.013515249826014042, "Left": 0.7175715565681458, "Top": 0.5463775992393494}, "Polygon": [{"X": 0.7175715565681458, "Y": 0.5463775992393494}, {"X": 0.7614829540252686, "Y": 0.5463776588439941}, {"X": 0.7614829540252686, "Y": 0.5598928332328796}, {"X": 0.7175715565681458, "Y": 0.5598927736282349}]}, "Id": "dec75f39-6352-4f97-bcae-5292ee81dc31"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027109188959002495, "Height": 0.01096739899367094, "Left": 0.7681890726089478, "Top": 0.5467419624328613}, "Polygon": [{"X": 0.7681890726089478, "Y": 0.5467419624328613}, {"X": 0.7952982783317566, "Y": 0.5467420220375061}, {"X": 0.7952982783317566, "Y": 0.5577093958854675}, {"X": 0.7681890726089478, "Y": 0.5577093362808228}]}, "Id": "e9e76ac7-deaf-4644-890f-b6d75df1717b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.074835404753685, "Height": 0.013257091864943504, "Left": 0.6623545289039612, "Top": 0.6187876462936401}, "Polygon": [{"X": 0.6623545289039612, "Y": 0.6187876462936401}, {"X": 0.7371899485588074, "Y": 0.6187878251075745}, {"X": 0.7371899485588074, "Y": 0.6320447325706482}, {"X": 0.6623545289039612, "Y": 0.6320445537567139}]}, "Id": "2dcddfeb-27f4-4ff6-a61d-4a5e06d15108"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0682583600282669, "Height": 0.010950346477329731, "Left": 0.7418630719184875, "Top": 0.6184596419334412}, "Polygon": [{"X": 0.7418630719184875, "Y": 0.6184596419334412}, {"X": 0.810121476650238, "Y": 0.6184598207473755}, {"X": 0.810121476650238, "Y": 0.6294100284576416}, {"X": 0.7418630719184875, "Y": 0.6294098496437073}]}, "Id": "5a66013d-46a3-4063-97f6-998183c702ba"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "210.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05555420368909836, "Height": 0.010955700650811195, "Left": 0.8266494274139404, "Top": 0.6184457540512085}, "Polygon": [{"X": 0.8266494274139404, "Y": 0.6184457540512085}, {"X": 0.8822036385536194, "Y": 0.618445873260498}, {"X": 0.8822036385536194, "Y": 0.629401445388794}, {"X": 0.8266494274139404, "Y": 0.6294013261795044}]}, "Id": "5edf25c8-cf85-46eb-8b17-f551c26a5ba8"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "USD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03704957291483879, "Height": 0.011250237934291363, "Left": 0.8869852423667908, "Top": 0.6182234287261963}, "Polygon": [{"X": 0.8869852423667908, "Y": 0.6182234287261963}, {"X": 0.924034833908081, "Y": 0.6182235479354858}, {"X": 0.924034833908081, "Y": 0.6294736862182617}, {"X": 0.8869852423667908, "Y": 0.6294735670089722}]}, "Id": "16924205-9ab6-4f6e-8151-d9c4f45ddb34"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Thank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05515533685684204, "Height": 0.011968317441642284, "Left": 0.05084272101521492, "Top": 0.7195131182670593}, "Polygon": [{"X": 0.05084272101521492, "Y": 0.7195131182670593}, {"X": 0.10599803179502487, "Y": 0.7195132970809937}, {"X": 0.10599805414676666, "Y": 0.7314814329147339}, {"X": 0.05084274709224701, "Y": 0.7314811944961548}]}, "Id": "e201496b-23cc-4bcc-8f51-0bd32494ae6e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "You,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03917962685227394, "Height": 0.01340461615473032, "Left": 0.11122213304042816, "Top": 0.7198396921157837}, "Polygon": [{"X": 0.11122213304042816, "Y": 0.7198396921157837}, {"X": 0.15040172636508942, "Y": 0.719839870929718}, {"X": 0.1504017561674118, "Y": 0.7332442998886108}, {"X": 0.11122216284275055, "Y": 0.7332441806793213}]}, "Id": "2d18ff7c-1f40-492d-ad5a-a269a1ada0f9"}, {"BlockType": "WORD", "Confidence": 99.85192108154297, "Text": "CargoSprint", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10420162230730057, "Height": 0.013982153497636318, "Left": 0.05128539353609085, "Top": 0.7374577522277832}, "Polygon": [{"X": 0.05128539353609085, "Y": 0.7374577522277832}, {"X": 0.15548698604106903, "Y": 0.7374581694602966}, {"X": 0.15548701584339142, "Y": 0.7514398694038391}, {"X": 0.05128542706370354, "Y": 0.7514394521713257}]}, "Id": "88c8628f-b3ee-4bdd-884c-0a5d6f8d4b1d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18427836894989014, "Height": 0.011629106476902962, "Left": 0.408019095659256, "Top": 0.9102886915206909}, "Polygon": [{"X": 0.408019095659256, "Y": 0.9102886915206909}, {"X": 0.5922974348068237, "Y": 0.9102898836135864}, {"X": 0.5922974348068237, "Y": 0.9219177961349487}, {"X": 0.408019095659256, "Y": 0.9219165444374084}]}, "Id": "cb7b3669-9290-484d-bc3d-48d5bbd96a56"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "CargoSprint", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09462716430425644, "Height": 0.01244315505027771, "Left": 0.3125801980495453, "Top": 0.9296990036964417}, "Polygon": [{"X": 0.3125801980495453, "Y": 0.9296990036964417}, {"X": 0.40720734000205994, "Y": 0.9296996593475342}, {"X": 0.4072073698043823, "Y": 0.9421421885490417}, {"X": 0.3125802278518677, "Y": 0.9421414732933044}]}, "Id": "073c0fe0-d4d5-48e1-81dd-4acf0cbb68ce"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03098989836871624, "Height": 0.009580755606293678, "Left": 0.41173985600471497, "Top": 0.9298844933509827}, "Polygon": [{"X": 0.41173985600471497, "Y": 0.9298844933509827}, {"X": 0.44272974133491516, "Y": 0.929884672164917}, {"X": 0.44272974133491516, "Y": 0.9394652247428894}, {"X": 0.41173985600471497, "Y": 0.9394650459289551}]}, "Id": "8e6f4200-e941-4d6a-9583-720d0a7cc24c"}, {"BlockType": "WORD", "Confidence": 76.14218139648438, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.002909362781792879, "Height": 0.012768324464559555, "Left": 0.44816961884498596, "Top": 0.9293532967567444}, "Polygon": [{"X": 0.44816961884498596, "Y": 0.9293532967567444}, {"X": 0.45107898116111755, "Y": 0.9293532967567444}, {"X": 0.45107898116111755, "Y": 0.9421216249465942}, {"X": 0.44816964864730835, "Y": 0.9421216249465942}]}, "Id": "ad2f10eb-c11f-4f5a-8165-044934cbde92"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "21", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017097951844334602, "Height": 0.009315112605690956, "Left": 0.4563502371311188, "Top": 0.929902195930481}, "Polygon": [{"X": 0.4563502371311188, "Y": 0.929902195930481}, {"X": 0.47344815731048584, "Y": 0.9299023151397705}, {"X": 0.4734481871128082, "Y": 0.9392173290252686}, {"X": 0.4563502371311188, "Y": 0.939217209815979}]}, "Id": "61ec4a60-cde8-4a0d-9ce5-4c27fe4ce4b1"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Eastbrook", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0729835033416748, "Height": 0.01007390208542347, "Left": 0.47973817586898804, "Top": 0.9294095039367676}, "Polygon": [{"X": 0.47973817586898804, "Y": 0.9294095039367676}, {"X": 0.5527216792106628, "Y": 0.9294100403785706}, {"X": 0.5527216792106628, "Y": 0.9394834041595459}, {"X": 0.4797382056713104, "Y": 0.9394829273223877}]}, "Id": "b858c5d5-bd1f-4d63-ac3c-954f3c629c5a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Bend", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03695167601108551, "Height": 0.010411013849079609, "Left": 0.5573161840438843, "Top": 0.9292190074920654}, "Polygon": [{"X": 0.5573161840438843, "Y": 0.9292190074920654}, {"X": 0.5942678451538086, "Y": 0.9292192459106445}, {"X": 0.5942679047584534, "Y": 0.9396300315856934}, {"X": 0.557316243648529, "Y": 0.9396297335624695}]}, "Id": "d4ebc6d3-00fe-46d2-9ca2-3b4e7cfa58d9"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Suite", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035967882722616196, "Height": 0.009808514267206192, "Left": 0.5996514558792114, "Top": 0.9296239018440247}, "Polygon": [{"X": 0.5996514558792114, "Y": 0.9296239018440247}, {"X": 0.6356193423271179, "Y": 0.9296241402626038}, {"X": 0.6356193423271179, "Y": 0.9394323825836182}, {"X": 0.5996514558792114, "Y": 0.9394321441650391}]}, "Id": "206d7faa-03fe-42bb-853c-2b21ed05cfcb"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0097538186237216, "Height": 0.009195741266012192, "Left": 0.6398441195487976, "Top": 0.9300737977027893}, "Polygon": [{"X": 0.6398441195487976, "Y": 0.9300737977027893}, {"X": 0.6495978832244873, "Y": 0.9300738573074341}, {"X": 0.6495978832244873, "Y": 0.9392695426940918}, {"X": 0.6398441195487976, "Y": 0.939269483089447}]}, "Id": "7ca3b009-5731-4538-bb92-f17d6ea2f42b"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "202", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02668190374970436, "Height": 0.009532972238957882, "Left": 0.6542534232139587, "Top": 0.9299210906028748}, "Polygon": [{"X": 0.6542534232139587, "Y": 0.9299210906028748}, {"X": 0.6809353232383728, "Y": 0.9299212694168091}, {"X": 0.6809353232383728, "Y": 0.9394540786743164}, {"X": 0.6542534232139587, "Y": 0.9394538402557373}]}, "Id": "823c71f1-c665-4904-8334-3952eeb5a598"}, {"BlockType": "WORD", "Confidence": 67.87548065185547, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0029623745940625668, "Height": 0.012838868424296379, "Left": 0.6862294673919678, "Top": 0.9293338060379028}, "Polygon": [{"X": 0.6862294673919678, "Y": 0.9293338060379028}, {"X": 0.6891918182373047, "Y": 0.9293338060379028}, {"X": 0.6891918182373047, "Y": 0.942172646522522}, {"X": 0.6862294673919678, "Y": 0.942172646522522}]}, "Id": "4d313f91-3a3d-45eb-9ae1-926d1cc2007f"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Peachtree", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07325856387615204, "Height": 0.010113788768649101, "Left": 0.3381504714488983, "Top": 0.944695770740509}, "Polygon": [{"X": 0.3381504714488983, "Y": 0.944695770740509}, {"X": 0.41140902042388916, "Y": 0.9446962475776672}, {"X": 0.41140905022621155, "Y": 0.9548095464706421}, {"X": 0.3381505012512207, "Y": 0.9548090100288391}]}, "Id": "5b72fe44-ede9-4e51-9aed-6f33790bc9e7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "City,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03182116150856018, "Height": 0.012547343969345093, "Left": 0.416175901889801, "Top": 0.9448624849319458}, "Polygon": [{"X": 0.416175901889801, "Y": 0.9448624849319458}, {"X": 0.4479970633983612, "Y": 0.9448626637458801}, {"X": 0.4479970633983612, "Y": 0.9574097990989685}, {"X": 0.4161759316921234, "Y": 0.9574095606803894}]}, "Id": "aa61596b-2d8f-4c95-b60f-3d93c779b928"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0236803125590086, "Height": 0.009652026928961277, "Left": 0.45353031158447266, "Top": 0.9450360536575317}, "Polygon": [{"X": 0.45353031158447266, "Y": 0.9450360536575317}, {"X": 0.4772106111049652, "Y": 0.9450362324714661}, {"X": 0.4772106111049652, "Y": 0.9546880722045898}, {"X": 0.45353031158447266, "Y": 0.9546878933906555}]}, "Id": "f8738ee4-7538-409d-833f-cb98fc4cbd8f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "30269", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04498758912086487, "Height": 0.009679783135652542, "Left": 0.4812043011188507, "Top": 0.9450798630714417}, "Polygon": [{"X": 0.4812043011188507, "Y": 0.9450798630714417}, {"X": 0.5261918902397156, "Y": 0.9450802206993103}, {"X": 0.5261918902397156, "Y": 0.9547596573829651}, {"X": 0.4812043309211731, "Y": 0.9547593593597412}]}, "Id": "6fe2bca3-df39-41e9-8bc8-47198b5e1653"}, {"BlockType": "WORD", "Confidence": 81.23764038085938, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0029343392234295607, "Height": 0.01307117659598589, "Left": 0.5315163135528564, "Top": 0.9443981051445007}, "Polygon": [{"X": 0.5315163135528564, "Y": 0.9443981051445007}, {"X": 0.5344506502151489, "Y": 0.9443981647491455}, {"X": 0.5344506502151489, "Y": 0.9574692845344543}, {"X": 0.5315163135528564, "Y": 0.9574692845344543}]}, "Id": "8f96964e-48bf-4d43-a43e-b5ea84ff6840"}, {"BlockType": "WORD", "Confidence": 98.84307098388672, "Text": "P.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013797608204185963, "Height": 0.00930744968354702, "Left": 0.5403568744659424, "Top": 0.945232629776001}, "Polygon": [{"X": 0.5403568744659424, "Y": 0.945232629776001}, {"X": 0.5541545152664185, "Y": 0.9452326893806458}, {"X": 0.5541545152664185, "Y": 0.9545400738716125}, {"X": 0.5403569340705872, "Y": 0.954539954662323}]}, "Id": "75dd470c-bec1-4781-8b02-3107c01e55ad"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "251-272-9247", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10041751712560654, "Height": 0.009668554179370403, "Left": 0.5596734881401062, "Top": 0.9449645280838013}, "Polygon": [{"X": 0.5596734881401062, "Y": 0.9449645280838013}, {"X": 0.660090982913971, "Y": 0.9449652433395386}, {"X": 0.660090982913971, "Y": 0.9546330571174622}, {"X": 0.5596734881401062, "Y": 0.9546323418617249}]}, "Id": "2ad1e2ca-1a24-4bd7-aeee-bf7daf29e0e4"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "3addee07-c7b0-41c0-9d13-1ec2ea196b8e", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "3addee07-c7b0-41c0-9d13-1ec2ea196b8e", "content-type": "application/x-amz-json-1.1", "content-length": "55624", "date": "Wed, 24 Sep 2025 12:42:30 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "276c21c3-2422-4d4b-9d5d-b99233a2380e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:42:33 GMT", "content-type": "application/json", "content-length": "983", "connection": "keep-alive", "x-amzn-requestid": "276c21c3-2422-4d4b-9d5d-b99233a2380e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's just one page. It's an ACH Payment Notification. Keywords: \"ACH PAYMENT NOTIFICATION\", \"Approved ACH\", \"Payment Amount\". This matches clear_to_pay definition: \"Clear to Pay\" – Approval for payment or ACH/Wire/Check transfer request or approval. So doc_type should be \"clear_to_pay\". There's no continuation. So we return JSON with array of objects: one page.\n\nWe must use the tool classify_logistics_doc_type. The tool expects an object with documents: an array of objects each with page_no and doc_type. So we call the function with documents: [{page_no:1, doc_type:\"clear_to_pay\"}].\n\nLet's produce that."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"clear_to_pay\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2695, "outputTokens": 163, "totalTokens": 2858}, "metrics": {"latencyMs": 913}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/202529a4_BVLD9CCIUN6D2HGVETKO.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n06/26/2024\nSprintPay\nPowered by\ncargosprint\nReference #\nR-14067232\nACH PAYMENT NOTIFICATION\nDear UPS SCS BUF DNMI\nAn APPROVED ACH from CargoSprint is being sent to Sunjin Shipping (U.S.A.), Inc.\nACH PAYMENTS DETAILS\nACH Confirmation Number:\nEstimated Delivery Date:\nCustomer Reference\nMAWB#\nHAWB#\nPayment Issued To\n60013893193\nLAI12068\n60013893193\nSunjin Shipping (U.\nS.A.), Inc.\nPayment Amount 210.00 USD\nThank You,\nCargoSprint\n<EMAIL>\nCargoSprint LLC I 21 Eastbrook Bend Suite # 202\nI\nPeachtree City, GA 30269 I P. 251-272-9247\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}