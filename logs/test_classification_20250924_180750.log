2025-09-24 18:07:50,221 - INFO - Logging initialized. Log file: logs/test_classification_20250924_180750.log
2025-09-24 18:07:50,221 - INFO - 📁 Found 9 files to process
2025-09-24 18:07:50,221 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:07:50,221 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 18:07:50,221 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 18:07:50,221 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 18:07:50,221 - INFO - ⬆️ [18:07:50] Uploading: BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:07:52,527 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/BVLD9CCIUN6D2HGVETKO.pdf -> s3://document-extraction-logistically/temp/60ceda95_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:07:52,528 - INFO - 🔍 [18:07:52] Starting classification: BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:07:52,528 - INFO - ⬆️ [18:07:52] Uploading: ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:07:52,530 - INFO - Initializing TextractProcessor...
2025-09-24 18:07:52,553 - INFO - Initializing BedrockProcessor...
2025-09-24 18:07:52,562 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/60ceda95_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:07:52,563 - INFO - Processing PDF from S3...
2025-09-24 18:07:52,563 - INFO - Downloading PDF from S3 to /tmp/tmpdyo1nup_/60ceda95_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:07:54,531 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:07:54,531 - INFO - Splitting PDF into individual pages...
2025-09-24 18:07:54,532 - INFO - Splitting PDF 60ceda95_BVLD9CCIUN6D2HGVETKO into 1 pages
2025-09-24 18:07:54,534 - INFO - Split PDF into 1 pages
2025-09-24 18:07:54,535 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:07:54,535 - INFO - Expected pages: [1]
2025-09-24 18:07:56,489 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ILOJODH5ADT10MHGK0TI.pdf -> s3://document-extraction-logistically/temp/cb46c52e_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:07:56,489 - INFO - 🔍 [18:07:56] Starting classification: ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:07:56,489 - INFO - ⬆️ [18:07:56] Uploading: NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:07:56,490 - INFO - Initializing TextractProcessor...
2025-09-24 18:07:56,501 - INFO - Initializing BedrockProcessor...
2025-09-24 18:07:56,505 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cb46c52e_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:07:56,505 - INFO - Processing PDF from S3...
2025-09-24 18:07:56,506 - INFO - Downloading PDF from S3 to /tmp/tmp27t89vn5/cb46c52e_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:07:58,878 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf -> s3://document-extraction-logistically/temp/9e316748_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:07:58,878 - INFO - 🔍 [18:07:58] Starting classification: NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:07:58,879 - INFO - ⬆️ [18:07:58] Uploading: NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:07:58,879 - INFO - Initializing TextractProcessor...
2025-09-24 18:07:58,893 - INFO - Initializing BedrockProcessor...
2025-09-24 18:07:58,897 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9e316748_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:07:58,898 - INFO - Processing PDF from S3...
2025-09-24 18:07:58,898 - INFO - Downloading PDF from S3 to /tmp/tmpk21xbzhg/9e316748_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:07:59,575 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 18:07:59,575 - INFO - Splitting PDF into individual pages...
2025-09-24 18:07:59,576 - WARNING - /Prev=0 in the trailer - assuming there is no previous xref table
2025-09-24 18:07:59,577 - INFO - Splitting PDF cb46c52e_ILOJODH5ADT10MHGK0TI into 1 pages
2025-09-24 18:07:59,580 - INFO - Split PDF into 1 pages
2025-09-24 18:07:59,580 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:07:59,581 - INFO - Expected pages: [1]
2025-09-24 18:07:59,741 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMWG7N543A7USM9VG3WI.pdf -> s3://document-extraction-logistically/temp/a8e2383a_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:07:59,741 - INFO - 🔍 [18:07:59] Starting classification: NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:07:59,742 - INFO - ⬆️ [18:07:59] Uploading: QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:07:59,744 - INFO - Initializing TextractProcessor...
2025-09-24 18:07:59,757 - INFO - Initializing BedrockProcessor...
2025-09-24 18:07:59,762 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a8e2383a_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:07:59,762 - INFO - Processing PDF from S3...
2025-09-24 18:07:59,763 - INFO - Downloading PDF from S3 to /tmp/tmphyxeynfn/a8e2383a_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:08:00,124 - INFO - Page 1: Extracted 545 characters, 28 lines from 60ceda95_BVLD9CCIUN6D2HGVETKO_6e357013_page_001.pdf
2025-09-24 18:08:00,125 - INFO - Successfully processed page 1
2025-09-24 18:08:00,125 - INFO - Combined 1 pages into final text
2025-09-24 18:08:00,125 - INFO - Text validation for 60ceda95_BVLD9CCIUN6D2HGVETKO: 562 characters, 1 pages
2025-09-24 18:08:00,125 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:00,125 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:01,320 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/QW3BG1TZMWHOTTZKGA8Y.pdf -> s3://document-extraction-logistically/temp/5cefcf39_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:08:01,320 - INFO - 🔍 [18:08:01] Starting classification: QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:08:01,321 - INFO - ⬆️ [18:08:01] Uploading: ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:01,322 - INFO - Initializing TextractProcessor...
2025-09-24 18:08:01,340 - INFO - Initializing BedrockProcessor...
2025-09-24 18:08:01,346 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5cefcf39_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:08:01,347 - INFO - Processing PDF from S3...
2025-09-24 18:08:01,347 - INFO - Downloading PDF from S3 to /tmp/tmphfq1sm0c/5cefcf39_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:08:01,355 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 18:08:01,355 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:01,357 - INFO - Splitting PDF 9e316748_NMAGOA3H1CROTNXQ4GR8 into 7 pages
2025-09-24 18:08:01,386 - INFO - Split PDF into 7 pages
2025-09-24 18:08:01,386 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:01,386 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 18:08:01,572 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:08:01,572 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:01,573 - INFO - Splitting PDF a8e2383a_NMWG7N543A7USM9VG3WI into 1 pages
2025-09-24 18:08:01,574 - INFO - Split PDF into 1 pages
2025-09-24 18:08:01,574 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:01,574 - INFO - Expected pages: [1]
2025-09-24 18:08:01,993 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ULZ4VHKA9IH82VWCSF3C.pdf -> s3://document-extraction-logistically/temp/c28fd8df_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:01,993 - INFO - 🔍 [18:08:01] Starting classification: ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:01,993 - INFO - ⬆️ [18:08:01] Uploading: W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:08:01,994 - INFO - Initializing TextractProcessor...
2025-09-24 18:08:02,005 - INFO - Initializing BedrockProcessor...
2025-09-24 18:08:02,009 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c28fd8df_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:02,009 - INFO - Processing PDF from S3...
2025-09-24 18:08:02,010 - INFO - Downloading PDF from S3 to /tmp/tmpifawdmxu/c28fd8df_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:02,540 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '43338806-b9c5-424c-b11a-192563a5d96f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:38:02 GMT', 'content-type': 'application/json', 'content-length': '1243', 'connection': 'keep-alive', 'x-amzn-requestid': '43338806-b9c5-424c-b11a-192563a5d96f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page 1 provided. It\'s an ACH payment notification. Keywords: "ACH PAYMENT NOTIFICATION", "Approved ACH", "payment amount". This matches doc_type: clear_to_pay? Wait clear_to_pay definition: Authorization indicating invoice approved for payment. ACH or Wire or Check transfer request or approval. Document or over an email. Keywords: Clear to Pay, Approved for Payment, Payment Authorization, Clear to Pay Stamp. This page says "ACH PAYMENT NOTIFICATION" and "An APPROVED ACH from CargoSprint is being sent to Sunjin Shipping". So it\'s a payment notification, basically a clear to pay. There is no invoice. So doc_type=clear_to_pay. We\'ll output the tool call with documents array: {page_no:1, doc_type:"clear_to_pay"}.\n\nBe careful that the tool expects an array of documents: each object has page_no and doc_type. Should be array of one element. Provide JSON accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2699, 'outputTokens': 215, 'totalTokens': 2914}, 'metrics': {'latencyMs': 1144}}
2025-09-24 18:08:02,541 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/60ceda95_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:08:02,646 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ (1).pdf -> s3://document-extraction-logistically/temp/018e86e0_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:08:02,647 - INFO - 🔍 [18:08:02] Starting classification: W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:08:02,648 - INFO - ⬆️ [18:08:02] Uploading: W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:08:02,650 - INFO - Initializing TextractProcessor...
2025-09-24 18:08:02,671 - INFO - Initializing BedrockProcessor...
2025-09-24 18:08:02,676 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/018e86e0_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:08:02,676 - INFO - Processing PDF from S3...
2025-09-24 18:08:02,677 - INFO - Downloading PDF from S3 to /tmp/tmp1y4kv3o3/018e86e0_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:08:03,336 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ.pdf -> s3://document-extraction-logistically/temp/2c877a6d_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:08:03,337 - INFO - 🔍 [18:08:03] Starting classification: W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:08:03,338 - INFO - ⬆️ [18:08:03] Uploading: Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:08:03,340 - INFO - Initializing TextractProcessor...
2025-09-24 18:08:03,366 - INFO - Initializing BedrockProcessor...
2025-09-24 18:08:03,370 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2c877a6d_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:08:03,370 - INFO - Processing PDF from S3...
2025-09-24 18:08:03,371 - INFO - Downloading PDF from S3 to /tmp/tmp_gcdqujl/2c877a6d_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:08:03,743 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:08:03,744 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:03,744 - INFO - Splitting PDF c28fd8df_ULZ4VHKA9IH82VWCSF3C into 1 pages
2025-09-24 18:08:03,746 - INFO - Split PDF into 1 pages
2025-09-24 18:08:03,746 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:03,746 - INFO - Expected pages: [1]
2025-09-24 18:08:03,954 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 18:08:03,954 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:03,957 - INFO - Splitting PDF 5cefcf39_QW3BG1TZMWHOTTZKGA8Y into 2 pages
2025-09-24 18:08:03,980 - INFO - Split PDF into 2 pages
2025-09-24 18:08:03,981 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:03,981 - INFO - Expected pages: [1, 2]
2025-09-24 18:08:04,035 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/Z7I9W27YVP7F6SPWV8UK.pdf -> s3://document-extraction-logistically/temp/59b70115_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:08:04,035 - INFO - 🔍 [18:08:04] Starting classification: Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:08:04,036 - INFO - Initializing TextractProcessor...
2025-09-24 18:08:04,048 - INFO - Initializing BedrockProcessor...
2025-09-24 18:08:04,053 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/59b70115_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:08:04,054 - INFO - Processing PDF from S3...
2025-09-24 18:08:04,058 - INFO - Downloading PDF from S3 to /tmp/tmpxm2fgd3s/59b70115_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:08:04,068 - INFO - 

BVLD9CCIUN6D2HGVETKO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:08:04,068 - INFO - 

✓ Saved result: output/run1_BVLD9CCIUN6D2HGVETKO.json
2025-09-24 18:08:04,349 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/60ceda95_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:08:04,697 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:08:04,697 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:04,698 - INFO - Splitting PDF 018e86e0_W7K89V5JE3EN9H4SOSTZ (1) into 1 pages
2025-09-24 18:08:04,700 - INFO - Split PDF into 1 pages
2025-09-24 18:08:04,700 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:04,700 - INFO - Expected pages: [1]
2025-09-24 18:08:05,211 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:08:05,211 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:05,212 - INFO - Splitting PDF 2c877a6d_W7K89V5JE3EN9H4SOSTZ into 1 pages
2025-09-24 18:08:05,213 - INFO - Split PDF into 1 pages
2025-09-24 18:08:05,213 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:05,213 - INFO - Expected pages: [1]
2025-09-24 18:08:06,170 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:08:06,170 - INFO - Splitting PDF into individual pages...
2025-09-24 18:08:06,171 - INFO - Splitting PDF 59b70115_Z7I9W27YVP7F6SPWV8UK into 1 pages
2025-09-24 18:08:06,173 - INFO - Split PDF into 1 pages
2025-09-24 18:08:06,173 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:08:06,173 - INFO - Expected pages: [1]
2025-09-24 18:08:06,415 - INFO - Page 1: Extracted 1392 characters, 59 lines from cb46c52e_ILOJODH5ADT10MHGK0TI_93c86a6d_page_001.pdf
2025-09-24 18:08:06,415 - INFO - Successfully processed page 1
2025-09-24 18:08:06,416 - INFO - Combined 1 pages into final text
2025-09-24 18:08:06,416 - INFO - Text validation for cb46c52e_ILOJODH5ADT10MHGK0TI: 1409 characters, 1 pages
2025-09-24 18:08:06,416 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:06,416 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:06,778 - INFO - Page 5: Extracted 1454 characters, 40 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_005.pdf
2025-09-24 18:08:06,779 - INFO - Successfully processed page 5
2025-09-24 18:08:06,995 - INFO - Page 3: Extracted 1672 characters, 51 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_003.pdf
2025-09-24 18:08:06,995 - INFO - Successfully processed page 3
2025-09-24 18:08:07,039 - INFO - Page 1: Extracted 1186 characters, 37 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_001.pdf
2025-09-24 18:08:07,039 - INFO - Successfully processed page 1
2025-09-24 18:08:07,368 - INFO - Page 2: Extracted 1897 characters, 42 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_002.pdf
2025-09-24 18:08:07,369 - INFO - Successfully processed page 2
2025-09-24 18:08:07,528 - INFO - Page 4: Extracted 1535 characters, 35 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_004.pdf
2025-09-24 18:08:07,529 - INFO - Successfully processed page 4
2025-09-24 18:08:08,160 - INFO - Page 1: Extracted 761 characters, 42 lines from a8e2383a_NMWG7N543A7USM9VG3WI_d5d94efe_page_001.pdf
2025-09-24 18:08:08,161 - INFO - Successfully processed page 1
2025-09-24 18:08:08,161 - INFO - Combined 1 pages into final text
2025-09-24 18:08:08,161 - INFO - Text validation for a8e2383a_NMWG7N543A7USM9VG3WI: 778 characters, 1 pages
2025-09-24 18:08:08,162 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:08,162 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:08,253 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0af5c536-20cf-411c-8199-b0959d5b9855', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:38:08 GMT', 'content-type': 'application/json', 'content-length': '751', 'connection': 'keep-alive', 'x-amzn-requestid': '0af5c536-20cf-411c-8199-b0959d5b9855'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. The content: "HTLF Bank Outgoing Wire Transfer Request" etc. This looks like a bank wire transfer request form. Not a logistics document. No relevant categories match. Should be classified as "other". There\'s no continuation indicator. So output with page_no 1, doc_type "other". Use the tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_WJvvri5pQeeQny-hS9w2tQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2888, 'outputTokens': 93, 'totalTokens': 2981}, 'metrics': {'latencyMs': 676}}
2025-09-24 18:08:08,253 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cb46c52e_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:08:08,269 - INFO - 

ILOJODH5ADT10MHGK0TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:08:08,269 - INFO - 

✓ Saved result: output/run1_ILOJODH5ADT10MHGK0TI.json
2025-09-24 18:08:08,577 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cb46c52e_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:08:08,651 - INFO - Page 6: Extracted 1677 characters, 38 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_006.pdf
2025-09-24 18:08:08,652 - INFO - Successfully processed page 6
2025-09-24 18:08:09,030 - INFO - Page 1: Extracted 1065 characters, 60 lines from c28fd8df_ULZ4VHKA9IH82VWCSF3C_f3d41998_page_001.pdf
2025-09-24 18:08:09,030 - INFO - Successfully processed page 1
2025-09-24 18:08:09,030 - INFO - Combined 1 pages into final text
2025-09-24 18:08:09,030 - INFO - Text validation for c28fd8df_ULZ4VHKA9IH82VWCSF3C: 1082 characters, 1 pages
2025-09-24 18:08:09,031 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:09,031 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:09,319 - INFO - Page 2: Extracted 335 characters, 15 lines from 5cefcf39_QW3BG1TZMWHOTTZKGA8Y_ba065aca_page_002.pdf
2025-09-24 18:08:09,319 - INFO - Successfully processed page 2
2025-09-24 18:08:09,894 - INFO - Page 1: Extracted 732 characters, 34 lines from 018e86e0_W7K89V5JE3EN9H4SOSTZ (1)_212db5eb_page_001.pdf
2025-09-24 18:08:09,894 - INFO - Successfully processed page 1
2025-09-24 18:08:09,894 - INFO - Combined 1 pages into final text
2025-09-24 18:08:09,895 - INFO - Text validation for 018e86e0_W7K89V5JE3EN9H4SOSTZ (1): 749 characters, 1 pages
2025-09-24 18:08:09,895 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:09,895 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:10,048 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '25869fe8-3629-453f-85c9-1a9c66270331', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:38:09 GMT', 'content-type': 'application/json', 'content-length': '677', 'connection': 'keep-alive', 'x-amzn-requestid': '25869fe8-3629-453f-85c9-1a9c66270331'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page labeled <page1>. Need to identify doc_type. Text shows: "Truckload Check-in Form" "Clear-to-Pay Voucher". It includes fields like Load Cost, Net Cost. This looks like a Clear to Pay voucher. The doc_type "clear_to_pay". So output array with one object: page_no 1, doc_type "clear_to_pay". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2785, 'outputTokens': 106, 'totalTokens': 2891}, 'metrics': {'latencyMs': 716}}
2025-09-24 18:08:10,048 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a8e2383a_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:08:10,060 - INFO - 

NMWG7N543A7USM9VG3WI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:08:10,060 - INFO - 

✓ Saved result: output/run1_NMWG7N543A7USM9VG3WI.json
2025-09-24 18:08:10,343 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a8e2383a_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:08:10,389 - INFO - Page 1: Extracted 1700 characters, 39 lines from 5cefcf39_QW3BG1TZMWHOTTZKGA8Y_ba065aca_page_001.pdf
2025-09-24 18:08:10,390 - INFO - Successfully processed page 1
2025-09-24 18:08:10,390 - INFO - Combined 2 pages into final text
2025-09-24 18:08:10,390 - INFO - Text validation for 5cefcf39_QW3BG1TZMWHOTTZKGA8Y: 2071 characters, 2 pages
2025-09-24 18:08:10,391 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:10,391 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:10,400 - INFO - Page 7: Extracted 500 characters, 15 lines from 9e316748_NMAGOA3H1CROTNXQ4GR8_c5d8d981_page_007.pdf
2025-09-24 18:08:10,400 - INFO - Successfully processed page 7
2025-09-24 18:08:10,401 - INFO - Combined 7 pages into final text
2025-09-24 18:08:10,401 - INFO - Text validation for 9e316748_NMAGOA3H1CROTNXQ4GR8: 10052 characters, 7 pages
2025-09-24 18:08:10,402 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:10,402 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:11,238 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '209c90eb-eb69-4668-922a-b5680414fad6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:38:11 GMT', 'content-type': 'application/json', 'content-length': '1245', 'connection': 'keep-alive', 'x-amzn-requestid': '209c90eb-eb69-4668-922a-b5680414fad6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content appears to be a "Clear to Pay" or "Approval Confirmation for PayCargo Invoice". It says "Approval Confirmation for PayCargo Invoice". The document type? It is an approval confirmation, with status: Approved, Approval Date. This looks like a "clear_to_pay" document: Authorization indicating invoice is approved for payment. The keywords: Approval Confirmation, Approved, Approval Date. Not exactly "Clear to Pay" but similar. The spec: clear_to_pay: Authorization indicating invoice is approved for payment. Keywords: Clear to Pay, Approved for Payment, Payment Authorization, Clear to Pay Stamp. It may fit clear_to_pay. There\'s no "Clear to Pay" but "Approval Confirmation". Might still be clear_to_pay. But is there any other? It\'s not invoice itself. It\'s just a confirmation of payment. So classify as "clear_to_pay". Output: page_no 1, doc_type "clear_to_pay".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2859, 'outputTokens': 213, 'totalTokens': 3072}, 'metrics': {'latencyMs': 1062}}
2025-09-24 18:08:11,239 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c28fd8df_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:11,267 - INFO - 

ULZ4VHKA9IH82VWCSF3C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:08:11,267 - INFO - 

✓ Saved result: output/run1_ULZ4VHKA9IH82VWCSF3C.json
2025-09-24 18:08:11,499 - INFO - Page 1: Extracted 732 characters, 34 lines from 2c877a6d_W7K89V5JE3EN9H4SOSTZ_d5748a06_page_001.pdf
2025-09-24 18:08:11,499 - INFO - Successfully processed page 1
2025-09-24 18:08:11,500 - INFO - Combined 1 pages into final text
2025-09-24 18:08:11,500 - INFO - Text validation for 2c877a6d_W7K89V5JE3EN9H4SOSTZ: 749 characters, 1 pages
2025-09-24 18:08:11,500 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:11,500 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:11,578 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c28fd8df_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:08:11,628 - INFO - Page 1: Extracted 736 characters, 39 lines from 59b70115_Z7I9W27YVP7F6SPWV8UK_11adcf53_page_001.pdf
2025-09-24 18:08:11,628 - INFO - Successfully processed page 1
2025-09-24 18:08:11,628 - INFO - Combined 1 pages into final text
2025-09-24 18:08:11,629 - INFO - Text validation for 59b70115_Z7I9W27YVP7F6SPWV8UK: 753 characters, 1 pages
2025-09-24 18:08:11,629 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:08:11,629 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:08:12,143 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '57270deb-7a74-454d-941e-fcf25954bd7b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:38:12 GMT', 'content-type': 'application/json', 'content-length': '964', 'connection': 'keep-alive', 'x-amzn-requestid': '57270deb-7a74-454d-941e-fcf25954bd7b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. It\'s a "Truckload Check-in Form" with "Clear-to-Pay Voucher" and "Load Cost". Keywords: "Clear-to-Pay Voucher", "Load Cost", "Broker PE", "Racks Returned", etc. This looks like a Clear-to-Pay document. The definition: "Clear to Pay" is an authorization indicating invoice is approved for payment. The text includes "Clear-to-Pay Voucher". So doc_type should be "clear_to_pay". We must output via function call classify_logistics_doc_type with documents array having one object: page_no 1, doc_type "clear_to_pay". Ensure correct enum exactly "clear_to_pay". Done.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2776, 'outputTokens': 166, 'totalTokens': 2942}, 'metrics': {'latencyMs': 1002}}
2025-09-24 18:08:12,143 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/018e86e0_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:08:12,166 - INFO - 

W7K89V5JE3EN9H4SOSTZ (1).pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:08:12,167 - INFO - 

✓ Saved result: output/run1_W7K89V5JE3EN9H4SOSTZ (1).json
2025-09-24 18:08:12,489 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/018e86e0_W7K89V5JE3EN9H4SOSTZ (1).pdf
