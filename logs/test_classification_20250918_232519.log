2025-09-18 23:25:19,650 - INFO - Logging initialized. Log file: logs/test_classification_20250918_232519.log
2025-09-18 23:25:19,659 - INFO - 📁 Found 57 files to process
2025-09-18 23:25:19,660 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 23:25:19,660 - INFO - 🚀 Processing 57 files in FORCED PARALLEL MODE...
2025-09-18 23:25:19,660 - INFO - 🚀 Creating 57 parallel tasks...
2025-09-18 23:25:19,660 - INFO - 🚀 All 57 tasks created - executing in parallel...
2025-09-18 23:25:19,661 - INFO - ⬆️ [23:25:19] Uploading: 10_invoice_1.pdf
2025-09-18 23:25:21,261 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/b94e46b6_10_invoice_1.pdf
2025-09-18 23:25:21,261 - INFO - 🔍 [23:25:21] Starting classification: 10_invoice_1.pdf
2025-09-18 23:25:21,262 - INFO - ⬆️ [23:25:21] Uploading: 10_invoice_10.pdf
2025-09-18 23:25:21,265 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:21,300 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:21,310 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b94e46b6_10_invoice_1.pdf
2025-09-18 23:25:21,310 - INFO - Processing PDF from S3...
2025-09-18 23:25:21,311 - INFO - Downloading PDF from S3 to /tmp/tmptfa0r2hj/b94e46b6_10_invoice_1.pdf
2025-09-18 23:25:21,855 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_10.pdf -> s3://document-extraction-logistically/temp/b937c795_10_invoice_10.pdf
2025-09-18 23:25:21,855 - INFO - 🔍 [23:25:21] Starting classification: 10_invoice_10.pdf
2025-09-18 23:25:21,856 - INFO - ⬆️ [23:25:21] Uploading: 10_invoice_2.pdf
2025-09-18 23:25:21,858 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:21,876 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:21,880 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b937c795_10_invoice_10.pdf
2025-09-18 23:25:21,880 - INFO - Processing PDF from S3...
2025-09-18 23:25:21,880 - INFO - Downloading PDF from S3 to /tmp/tmp9892v3di/b937c795_10_invoice_10.pdf
2025-09-18 23:25:22,844 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:22,845 - INFO - Splitting PDF b94e46b6_10_invoice_1 into 1 pages
2025-09-18 23:25:22,848 - INFO - Split PDF into 1 pages
2025-09-18 23:25:22,848 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:22,848 - INFO - Expected pages: [1]
2025-09-18 23:25:23,120 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:23,121 - INFO - Splitting PDF b937c795_10_invoice_10 into 1 pages
2025-09-18 23:25:23,123 - INFO - Split PDF into 1 pages
2025-09-18 23:25:23,123 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:23,123 - INFO - Expected pages: [1]
2025-09-18 23:25:23,374 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_2.pdf -> s3://document-extraction-logistically/temp/13212ef8_10_invoice_2.pdf
2025-09-18 23:25:23,374 - INFO - 🔍 [23:25:23] Starting classification: 10_invoice_2.pdf
2025-09-18 23:25:23,374 - INFO - ⬆️ [23:25:23] Uploading: 12_combined_carrier_documents_1.pdf
2025-09-18 23:25:23,375 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:23,383 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:23,385 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/13212ef8_10_invoice_2.pdf
2025-09-18 23:25:23,386 - INFO - Processing PDF from S3...
2025-09-18 23:25:23,386 - INFO - Downloading PDF from S3 to /tmp/tmpptvmk_yw/13212ef8_10_invoice_2.pdf
2025-09-18 23:25:24,028 - INFO - ✓ Uploaded: input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf -> s3://document-extraction-logistically/temp/cd2341fe_12_combined_carrier_documents_1.pdf
2025-09-18 23:25:24,028 - INFO - 🔍 [23:25:24] Starting classification: 12_combined_carrier_documents_1.pdf
2025-09-18 23:25:24,030 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:24,030 - INFO - ⬆️ [23:25:24] Uploading: 12_combined_carrier_documents_2.pdf
2025-09-18 23:25:24,046 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:24,057 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cd2341fe_12_combined_carrier_documents_1.pdf
2025-09-18 23:25:24,057 - INFO - Processing PDF from S3...
2025-09-18 23:25:24,058 - INFO - Downloading PDF from S3 to /tmp/tmpn7snzo5e/cd2341fe_12_combined_carrier_documents_1.pdf
2025-09-18 23:25:24,629 - INFO - ✓ Uploaded: input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf -> s3://document-extraction-logistically/temp/3f25d56e_12_combined_carrier_documents_2.pdf
2025-09-18 23:25:24,629 - INFO - 🔍 [23:25:24] Starting classification: 12_combined_carrier_documents_2.pdf
2025-09-18 23:25:24,630 - INFO - ⬆️ [23:25:24] Uploading: 12_combined_carrier_documents_3.pdf
2025-09-18 23:25:24,631 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:24,652 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:24,657 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3f25d56e_12_combined_carrier_documents_2.pdf
2025-09-18 23:25:24,657 - INFO - Processing PDF from S3...
2025-09-18 23:25:24,658 - INFO - Downloading PDF from S3 to /tmp/tmpy7e18ag1/3f25d56e_12_combined_carrier_documents_2.pdf
2025-09-18 23:25:25,236 - INFO - ✓ Uploaded: input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf -> s3://document-extraction-logistically/temp/ae716473_12_combined_carrier_documents_3.pdf
2025-09-18 23:25:25,236 - INFO - 🔍 [23:25:25] Starting classification: 12_combined_carrier_documents_3.pdf
2025-09-18 23:25:25,238 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:25,238 - INFO - ⬆️ [23:25:25] Uploading: 13_pack_list_1.pdf
2025-09-18 23:25:25,258 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:25,267 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ae716473_12_combined_carrier_documents_3.pdf
2025-09-18 23:25:25,267 - INFO - Processing PDF from S3...
2025-09-18 23:25:25,268 - INFO - Downloading PDF from S3 to /tmp/tmpo44w4_fr/ae716473_12_combined_carrier_documents_3.pdf
2025-09-18 23:25:25,682 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:25,686 - INFO - Splitting PDF 13212ef8_10_invoice_2 into 1 pages
2025-09-18 23:25:25,741 - INFO - Split PDF into 1 pages
2025-09-18 23:25:25,742 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:25,742 - INFO - Expected pages: [1]
2025-09-18 23:25:25,876 - INFO - ✓ Uploaded: input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf -> s3://document-extraction-logistically/temp/c75bd811_13_pack_list_1.pdf
2025-09-18 23:25:25,877 - INFO - 🔍 [23:25:25] Starting classification: 13_pack_list_1.pdf
2025-09-18 23:25:25,878 - INFO - ⬆️ [23:25:25] Uploading: 13_pack_list_10.pdf
2025-09-18 23:25:25,880 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:25,903 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:25,908 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c75bd811_13_pack_list_1.pdf
2025-09-18 23:25:25,908 - INFO - Processing PDF from S3...
2025-09-18 23:25:25,909 - INFO - Downloading PDF from S3 to /tmp/tmpg2j_izha/c75bd811_13_pack_list_1.pdf
2025-09-18 23:25:26,117 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:26,118 - INFO - Splitting PDF cd2341fe_12_combined_carrier_documents_1 into 2 pages
2025-09-18 23:25:26,120 - INFO - Split PDF into 2 pages
2025-09-18 23:25:26,120 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:26,120 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:26,390 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:26,391 - INFO - Splitting PDF 3f25d56e_12_combined_carrier_documents_2 into 1 pages
2025-09-18 23:25:26,412 - INFO - Split PDF into 1 pages
2025-09-18 23:25:26,412 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:26,412 - INFO - Expected pages: [1]
2025-09-18 23:25:26,678 - INFO - Page 1: Extracted 659 characters, 47 lines from b937c795_10_invoice_10_7d6714cb_page_001.pdf
2025-09-18 23:25:26,679 - INFO - Successfully processed page 1
2025-09-18 23:25:26,679 - INFO - Combined 1 pages into final text
2025-09-18 23:25:26,679 - INFO - Text validation for b937c795_10_invoice_10: 676 characters, 1 pages
2025-09-18 23:25:26,680 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:26,680 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:27,056 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:27,057 - INFO - Splitting PDF ae716473_12_combined_carrier_documents_3 into 1 pages
2025-09-18 23:25:27,069 - INFO - Split PDF into 1 pages
2025-09-18 23:25:27,069 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:27,069 - INFO - Expected pages: [1]
2025-09-18 23:25:27,386 - INFO - ✓ Uploaded: input_data_3_per_cat/13_pack_list/13_pack_list_10.pdf -> s3://document-extraction-logistically/temp/8355a64b_13_pack_list_10.pdf
2025-09-18 23:25:27,386 - INFO - 🔍 [23:25:27] Starting classification: 13_pack_list_10.pdf
2025-09-18 23:25:27,387 - INFO - ⬆️ [23:25:27] Uploading: 13_pack_list_2.PDF
2025-09-18 23:25:27,388 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:27,405 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:27,409 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8355a64b_13_pack_list_10.pdf
2025-09-18 23:25:27,409 - INFO - Processing PDF from S3...
2025-09-18 23:25:27,409 - INFO - Downloading PDF from S3 to /tmp/tmpw_n1k_l4/8355a64b_13_pack_list_10.pdf
2025-09-18 23:25:27,685 - INFO - Page 1: Extracted 1418 characters, 97 lines from b94e46b6_10_invoice_1_66eb492a_page_001.pdf
2025-09-18 23:25:27,685 - INFO - Successfully processed page 1
2025-09-18 23:25:27,686 - INFO - Combined 1 pages into final text
2025-09-18 23:25:27,686 - INFO - Text validation for b94e46b6_10_invoice_1: 1435 characters, 1 pages
2025-09-18 23:25:27,686 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:27,686 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:28,061 - INFO - ✓ Uploaded: input_data_3_per_cat/13_pack_list/13_pack_list_2.PDF -> s3://document-extraction-logistically/temp/a5ee8741_13_pack_list_2.PDF
2025-09-18 23:25:28,061 - INFO - 🔍 [23:25:28] Starting classification: 13_pack_list_2.PDF
2025-09-18 23:25:28,061 - INFO - ⬆️ [23:25:28] Uploading: 14_po_1.pdf
2025-09-18 23:25:28,061 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:28,069 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:28,071 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a5ee8741_13_pack_list_2.PDF
2025-09-18 23:25:28,071 - INFO - Processing PDF from S3...
2025-09-18 23:25:28,071 - INFO - Downloading PDF from S3 to /tmp/tmpimp_ga0m/a5ee8741_13_pack_list_2.PDF
2025-09-18 23:25:29,030 - INFO - ✓ Uploaded: input_data_3_per_cat/14_po/14_po_1.pdf -> s3://document-extraction-logistically/temp/cfecd9ed_14_po_1.pdf
2025-09-18 23:25:29,031 - INFO - 🔍 [23:25:29] Starting classification: 14_po_1.pdf
2025-09-18 23:25:29,032 - INFO - ⬆️ [23:25:29] Uploading: 14_po_2.pdf
2025-09-18 23:25:29,032 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:29,049 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:29,063 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cfecd9ed_14_po_1.pdf
2025-09-18 23:25:29,064 - INFO - Processing PDF from S3...
2025-09-18 23:25:29,064 - INFO - Downloading PDF from S3 to /tmp/tmp3q0y1sd2/cfecd9ed_14_po_1.pdf
2025-09-18 23:25:29,113 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b937c795_10_invoice_10.pdf
2025-09-18 23:25:29,439 - INFO - Page 2: Extracted 313 characters, 9 lines from cd2341fe_12_combined_carrier_documents_1_dd77ae99_page_002.pdf
2025-09-18 23:25:29,439 - INFO - Successfully processed page 2
2025-09-18 23:25:29,679 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b94e46b6_10_invoice_1.pdf
2025-09-18 23:25:29,717 - INFO - ✓ Uploaded: input_data_3_per_cat/14_po/14_po_2.pdf -> s3://document-extraction-logistically/temp/8c8c9078_14_po_2.pdf
2025-09-18 23:25:29,718 - INFO - 🔍 [23:25:29] Starting classification: 14_po_2.pdf
2025-09-18 23:25:29,719 - INFO - ⬆️ [23:25:29] Uploading: 14_po_3.pdf
2025-09-18 23:25:29,719 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:29,738 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:29,743 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8c8c9078_14_po_2.pdf
2025-09-18 23:25:29,743 - INFO - Processing PDF from S3...
2025-09-18 23:25:29,744 - INFO - Downloading PDF from S3 to /tmp/tmpywwv02lq/8c8c9078_14_po_2.pdf
2025-09-18 23:25:30,185 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:30,186 - INFO - Splitting PDF a5ee8741_13_pack_list_2 into 1 pages
2025-09-18 23:25:30,196 - INFO - Split PDF into 1 pages
2025-09-18 23:25:30,196 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:30,196 - INFO - Expected pages: [1]
2025-09-18 23:25:30,326 - INFO - ✓ Uploaded: input_data_3_per_cat/14_po/14_po_3.pdf -> s3://document-extraction-logistically/temp/bd4c27a5_14_po_3.pdf
2025-09-18 23:25:30,326 - INFO - 🔍 [23:25:30] Starting classification: 14_po_3.pdf
2025-09-18 23:25:30,327 - INFO - ⬆️ [23:25:30] Uploading: 15_comm_invoice_1.pdf
2025-09-18 23:25:30,328 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:30,343 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:30,353 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bd4c27a5_14_po_3.pdf
2025-09-18 23:25:30,354 - INFO - Processing PDF from S3...
2025-09-18 23:25:30,355 - INFO - Downloading PDF from S3 to /tmp/tmp6_qam12c/bd4c27a5_14_po_3.pdf
2025-09-18 23:25:30,362 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:30,373 - INFO - Splitting PDF c75bd811_13_pack_list_1 into 2 pages
2025-09-18 23:25:30,387 - INFO - Split PDF into 2 pages
2025-09-18 23:25:30,387 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:30,387 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:30,710 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:30,711 - INFO - Splitting PDF 8355a64b_13_pack_list_10 into 1 pages
2025-09-18 23:25:30,713 - INFO - Split PDF into 1 pages
2025-09-18 23:25:30,713 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:30,713 - INFO - Expected pages: [1]
2025-09-18 23:25:30,934 - INFO - ✓ Uploaded: input_data_3_per_cat/15_comm_invoice/15_comm_invoice_1.pdf -> s3://document-extraction-logistically/temp/5e604368_15_comm_invoice_1.pdf
2025-09-18 23:25:30,934 - INFO - 🔍 [23:25:30] Starting classification: 15_comm_invoice_1.pdf
2025-09-18 23:25:30,935 - INFO - ⬆️ [23:25:30] Uploading: 15_comm_invoice_2.pdf
2025-09-18 23:25:30,935 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:30,945 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:30,948 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5e604368_15_comm_invoice_1.pdf
2025-09-18 23:25:30,948 - INFO - Processing PDF from S3...
2025-09-18 23:25:30,948 - INFO - Downloading PDF from S3 to /tmp/tmpznf3yve7/5e604368_15_comm_invoice_1.pdf
2025-09-18 23:25:31,537 - INFO - ✓ Uploaded: input_data_3_per_cat/15_comm_invoice/15_comm_invoice_2.pdf -> s3://document-extraction-logistically/temp/a333ba95_15_comm_invoice_2.pdf
2025-09-18 23:25:31,538 - INFO - 🔍 [23:25:31] Starting classification: 15_comm_invoice_2.pdf
2025-09-18 23:25:31,539 - INFO - ⬆️ [23:25:31] Uploading: 16_customs_doc_1.pdf
2025-09-18 23:25:31,540 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:31,556 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:31,561 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a333ba95_15_comm_invoice_2.pdf
2025-09-18 23:25:31,561 - INFO - Processing PDF from S3...
2025-09-18 23:25:31,561 - INFO - Downloading PDF from S3 to /tmp/tmpbngrtup8/a333ba95_15_comm_invoice_2.pdf
2025-09-18 23:25:31,611 - INFO - Page 1: Extracted 940 characters, 39 lines from 13212ef8_10_invoice_2_5373931c_page_001.pdf
2025-09-18 23:25:31,611 - INFO - Successfully processed page 1
2025-09-18 23:25:31,612 - INFO - Combined 1 pages into final text
2025-09-18 23:25:31,612 - INFO - Text validation for 13212ef8_10_invoice_2: 957 characters, 1 pages
2025-09-18 23:25:31,613 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:31,613 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:31,843 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:31,844 - INFO - Splitting PDF 8c8c9078_14_po_2 into 1 pages
2025-09-18 23:25:31,860 - INFO - Split PDF into 1 pages
2025-09-18 23:25:31,860 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:31,860 - INFO - Expected pages: [1]
2025-09-18 23:25:31,975 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:31,976 - INFO - Splitting PDF bd4c27a5_14_po_3 into 2 pages
2025-09-18 23:25:31,992 - INFO - Split PDF into 2 pages
2025-09-18 23:25:31,992 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:31,992 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:32,114 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:32,115 - INFO - Splitting PDF cfecd9ed_14_po_1 into 1 pages
2025-09-18 23:25:32,158 - INFO - ✓ Uploaded: input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf -> s3://document-extraction-logistically/temp/2e892777_16_customs_doc_1.pdf
2025-09-18 23:25:32,159 - INFO - 🔍 [23:25:32] Starting classification: 16_customs_doc_1.pdf
2025-09-18 23:25:32,160 - INFO - ⬆️ [23:25:32] Uploading: 16_customs_doc_2.tiff
2025-09-18 23:25:32,162 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:32,171 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:32,174 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2e892777_16_customs_doc_1.pdf
2025-09-18 23:25:32,174 - INFO - Split PDF into 1 pages
2025-09-18 23:25:32,174 - INFO - Processing PDF from S3...
2025-09-18 23:25:32,174 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:32,174 - INFO - Expected pages: [1]
2025-09-18 23:25:32,175 - INFO - Downloading PDF from S3 to /tmp/tmpij2n52lb/2e892777_16_customs_doc_1.pdf
2025-09-18 23:25:32,419 - INFO - Page 1: Extracted 1231 characters, 84 lines from cd2341fe_12_combined_carrier_documents_1_dd77ae99_page_001.pdf
2025-09-18 23:25:32,419 - INFO - Successfully processed page 1
2025-09-18 23:25:32,419 - INFO - Combined 2 pages into final text
2025-09-18 23:25:32,419 - INFO - Text validation for cd2341fe_12_combined_carrier_documents_1: 1580 characters, 2 pages
2025-09-18 23:25:32,419 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:32,419 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:32,490 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:32,491 - INFO - Splitting PDF 5e604368_15_comm_invoice_1 into 1 pages
2025-09-18 23:25:32,494 - INFO - Split PDF into 1 pages
2025-09-18 23:25:32,494 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:32,495 - INFO - Expected pages: [1]
2025-09-18 23:25:32,771 - INFO - ✓ Uploaded: input_data_3_per_cat/16_customs_doc/16_customs_doc_2.tiff -> s3://document-extraction-logistically/temp/2e531567_16_customs_doc_2.tiff
2025-09-18 23:25:32,771 - INFO - 🔍 [23:25:32] Starting classification: 16_customs_doc_2.tiff
2025-09-18 23:25:32,772 - INFO - ⬆️ [23:25:32] Uploading: 16_customs_doc_3.pdf
2025-09-18 23:25:32,773 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:32,782 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:32,784 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2e531567_16_customs_doc_2.tiff
2025-09-18 23:25:32,784 - INFO - Processing image from S3...
2025-09-18 23:25:33,205 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:33,207 - INFO - Splitting PDF a333ba95_15_comm_invoice_2 into 1 pages
2025-09-18 23:25:33,210 - INFO - Split PDF into 1 pages
2025-09-18 23:25:33,210 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:33,210 - INFO - Expected pages: [1]
2025-09-18 23:25:33,389 - INFO - ✓ Uploaded: input_data_3_per_cat/16_customs_doc/16_customs_doc_3.pdf -> s3://document-extraction-logistically/temp/927c24eb_16_customs_doc_3.pdf
2025-09-18 23:25:33,390 - INFO - 🔍 [23:25:33] Starting classification: 16_customs_doc_3.pdf
2025-09-18 23:25:33,391 - INFO - ⬆️ [23:25:33] Uploading: 17_nmfc_cert_1.jpg
2025-09-18 23:25:33,393 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:33,408 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:33,414 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/927c24eb_16_customs_doc_3.pdf
2025-09-18 23:25:33,414 - INFO - Processing PDF from S3...
2025-09-18 23:25:33,415 - INFO - Downloading PDF from S3 to /tmp/tmpde7ndb8f/927c24eb_16_customs_doc_3.pdf
2025-09-18 23:25:33,590 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/13212ef8_10_invoice_2.pdf
2025-09-18 23:25:34,004 - INFO - ✓ Uploaded: input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_1.jpg -> s3://document-extraction-logistically/temp/d0468672_17_nmfc_cert_1.jpg
2025-09-18 23:25:34,004 - INFO - 🔍 [23:25:34] Starting classification: 17_nmfc_cert_1.jpg
2025-09-18 23:25:34,006 - INFO - ⬆️ [23:25:34] Uploading: 17_nmfc_cert_2.pdf
2025-09-18 23:25:34,006 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:34,021 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:34,025 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d0468672_17_nmfc_cert_1.jpg
2025-09-18 23:25:34,026 - INFO - Processing image from S3...
2025-09-18 23:25:34,337 - INFO - Page 1: Extracted 4024 characters, 186 lines from ae716473_12_combined_carrier_documents_3_009ba940_page_001.pdf
2025-09-18 23:25:34,337 - INFO - Successfully processed page 1
2025-09-18 23:25:34,338 - INFO - Combined 1 pages into final text
2025-09-18 23:25:34,338 - INFO - Text validation for ae716473_12_combined_carrier_documents_3: 4041 characters, 1 pages
2025-09-18 23:25:34,339 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:34,339 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:34,469 - WARNING - Sync method failed for S3 image temp/2e531567_16_customs_doc_2.tiff, trying async method: An error occurred (UnsupportedDocumentException) when calling the DetectDocumentText operation: Request has unsupported document format
2025-09-18 23:25:34,493 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:34,494 - INFO - Splitting PDF 2e892777_16_customs_doc_1 into 3 pages
2025-09-18 23:25:34,503 - INFO - Split PDF into 3 pages
2025-09-18 23:25:34,503 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:34,503 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:25:34,642 - INFO - Page 1: Extracted 4164 characters, 222 lines from 3f25d56e_12_combined_carrier_documents_2_c27dab8f_page_001.pdf
2025-09-18 23:25:34,642 - INFO - Successfully processed page 1
2025-09-18 23:25:34,642 - INFO - Combined 1 pages into final text
2025-09-18 23:25:34,642 - INFO - Text validation for 3f25d56e_12_combined_carrier_documents_2: 4181 characters, 1 pages
2025-09-18 23:25:34,643 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:34,643 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:34,645 - INFO - ✓ Uploaded: input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf -> s3://document-extraction-logistically/temp/85a9af5c_17_nmfc_cert_2.pdf
2025-09-18 23:25:34,646 - INFO - 🔍 [23:25:34] Starting classification: 17_nmfc_cert_2.pdf
2025-09-18 23:25:34,646 - INFO - ⬆️ [23:25:34] Uploading: 19_coa_1.pdf
2025-09-18 23:25:34,649 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:34,657 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:34,659 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/85a9af5c_17_nmfc_cert_2.pdf
2025-09-18 23:25:34,659 - INFO - Processing PDF from S3...
2025-09-18 23:25:34,659 - INFO - Downloading PDF from S3 to /tmp/tmpuxdimeuo/85a9af5c_17_nmfc_cert_2.pdf
2025-09-18 23:25:35,253 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:35,255 - INFO - Splitting PDF 927c24eb_16_customs_doc_3 into 1 pages
2025-09-18 23:25:35,257 - INFO - Split PDF into 1 pages
2025-09-18 23:25:35,257 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:35,257 - INFO - Expected pages: [1]
2025-09-18 23:25:35,603 - INFO - ✓ Uploaded: input_data_3_per_cat/19_coa/19_coa_1.pdf -> s3://document-extraction-logistically/temp/a319cd0f_19_coa_1.pdf
2025-09-18 23:25:35,606 - INFO - 🔍 [23:25:35] Starting classification: 19_coa_1.pdf
2025-09-18 23:25:35,607 - INFO - ⬆️ [23:25:35] Uploading: 19_coa_2.pdf
2025-09-18 23:25:35,608 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:35,618 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:35,621 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a319cd0f_19_coa_1.pdf
2025-09-18 23:25:35,621 - INFO - Processing PDF from S3...
2025-09-18 23:25:35,622 - INFO - Downloading PDF from S3 to /tmp/tmpcblrw32p/a319cd0f_19_coa_1.pdf
2025-09-18 23:25:35,921 - INFO - Started async Textract job 91891c7325dc9e33f3ebf29b86ed99581384175b977d361bafd3abc2cc249d1a for S3 image temp/2e531567_16_customs_doc_2.tiff
2025-09-18 23:25:36,004 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cd2341fe_12_combined_carrier_documents_1.pdf
2025-09-18 23:25:36,110 - INFO - Page 2: Extracted 276 characters, 20 lines from c75bd811_13_pack_list_1_39986833_page_002.pdf
2025-09-18 23:25:36,110 - INFO - Successfully processed page 2
2025-09-18 23:25:36,170 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:36,172 - INFO - Splitting PDF 85a9af5c_17_nmfc_cert_2 into 2 pages
2025-09-18 23:25:36,177 - INFO - Split PDF into 2 pages
2025-09-18 23:25:36,177 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:36,177 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:36,216 - INFO -     Poll #1 - Status: IN_PROGRESS (Elapsed: 0s)
2025-09-18 23:25:36,217 - INFO - ✓ Uploaded: input_data_3_per_cat/19_coa/19_coa_2.pdf -> s3://document-extraction-logistically/temp/6eced563_19_coa_2.pdf
2025-09-18 23:25:36,217 - INFO - 🔍 [23:25:36] Starting classification: 19_coa_2.pdf
2025-09-18 23:25:36,218 - INFO - ⬆️ [23:25:36] Uploading: 19_coa_3.pdf
2025-09-18 23:25:36,219 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:36,250 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:36,256 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6eced563_19_coa_2.pdf
2025-09-18 23:25:36,256 - INFO - Processing PDF from S3...
2025-09-18 23:25:36,257 - INFO - Downloading PDF from S3 to /tmp/tmpisoasmzy/6eced563_19_coa_2.pdf
2025-09-18 23:25:36,418 - INFO - Page 1: Extracted 1069 characters, 112 lines from 8355a64b_13_pack_list_10_dd74c4bc_page_001.pdf
2025-09-18 23:25:36,418 - INFO - Successfully processed page 1
2025-09-18 23:25:36,418 - INFO - Combined 1 pages into final text
2025-09-18 23:25:36,419 - INFO - Text validation for 8355a64b_13_pack_list_10: 1086 characters, 1 pages
2025-09-18 23:25:36,419 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:36,419 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:36,500 - INFO - Page 1: Extracted 1156 characters, 65 lines from c75bd811_13_pack_list_1_39986833_page_001.pdf
2025-09-18 23:25:36,501 - INFO - Successfully processed page 1
2025-09-18 23:25:36,501 - INFO - Combined 2 pages into final text
2025-09-18 23:25:36,501 - INFO - Text validation for c75bd811_13_pack_list_1: 1468 characters, 2 pages
2025-09-18 23:25:36,502 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:36,502 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:36,604 - INFO - Page 2: Extracted 669 characters, 16 lines from bd4c27a5_14_po_3_543aa7c2_page_002.pdf
2025-09-18 23:25:36,604 - INFO - Successfully processed page 2
2025-09-18 23:25:36,663 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3f25d56e_12_combined_carrier_documents_2.pdf
2025-09-18 23:25:36,788 - INFO - Page 1: Extracted 1774 characters, 128 lines from a5ee8741_13_pack_list_2_03ba0963_page_001.pdf
2025-09-18 23:25:36,789 - INFO - Successfully processed page 1
2025-09-18 23:25:36,789 - INFO - Combined 1 pages into final text
2025-09-18 23:25:36,789 - INFO - Text validation for a5ee8741_13_pack_list_2: 1791 characters, 1 pages
2025-09-18 23:25:36,789 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:36,789 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:36,863 - INFO - Page 1: Extracted 1432 characters, 83 lines from bd4c27a5_14_po_3_543aa7c2_page_001.pdf
2025-09-18 23:25:36,863 - INFO - Successfully processed page 1
2025-09-18 23:25:36,863 - INFO - Combined 2 pages into final text
2025-09-18 23:25:36,864 - INFO - Text validation for bd4c27a5_14_po_3: 2137 characters, 2 pages
2025-09-18 23:25:36,864 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:36,864 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:36,869 - INFO - ✓ Uploaded: input_data_3_per_cat/19_coa/19_coa_3.pdf -> s3://document-extraction-logistically/temp/4ca1210a_19_coa_3.pdf
2025-09-18 23:25:36,869 - INFO - 🔍 [23:25:36] Starting classification: 19_coa_3.pdf
2025-09-18 23:25:36,869 - INFO - ⬆️ [23:25:36] Uploading: 1_bol_1.pdf
2025-09-18 23:25:36,871 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:36,880 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:36,883 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4ca1210a_19_coa_3.pdf
2025-09-18 23:25:36,883 - INFO - Processing PDF from S3...
2025-09-18 23:25:36,883 - INFO - Downloading PDF from S3 to /tmp/tmpbstvid_e/4ca1210a_19_coa_3.pdf
2025-09-18 23:25:36,954 - INFO - S3 Image temp/d0468672_17_nmfc_cert_1.jpg: Extracted 825 characters, 77 lines
2025-09-18 23:25:36,954 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:36,954 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:37,032 - INFO - Page 1: Extracted 1011 characters, 51 lines from 5e604368_15_comm_invoice_1_db7f7c7c_page_001.pdf
2025-09-18 23:25:37,033 - INFO - Successfully processed page 1
2025-09-18 23:25:37,033 - INFO - Combined 1 pages into final text
2025-09-18 23:25:37,033 - INFO - Text validation for 5e604368_15_comm_invoice_1: 1028 characters, 1 pages
2025-09-18 23:25:37,033 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:37,033 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:37,398 - INFO - Page 1: Extracted 813 characters, 71 lines from 8c8c9078_14_po_2_410575f6_page_001.pdf
2025-09-18 23:25:37,399 - INFO - Successfully processed page 1
2025-09-18 23:25:37,399 - INFO - Combined 1 pages into final text
2025-09-18 23:25:37,399 - INFO - Text validation for 8c8c9078_14_po_2: 830 characters, 1 pages
2025-09-18 23:25:37,399 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:37,399 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:37,470 - INFO - ✓ Uploaded: input_data_3_per_cat/1_bol/1_bol_1.pdf -> s3://document-extraction-logistically/temp/250e7c4a_1_bol_1.pdf
2025-09-18 23:25:37,470 - INFO - 🔍 [23:25:37] Starting classification: 1_bol_1.pdf
2025-09-18 23:25:37,471 - INFO - ⬆️ [23:25:37] Uploading: 1_bol_10.pdf
2025-09-18 23:25:37,477 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:37,492 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:37,498 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/250e7c4a_1_bol_1.pdf
2025-09-18 23:25:37,498 - INFO - Processing PDF from S3...
2025-09-18 23:25:37,498 - INFO - Downloading PDF from S3 to /tmp/tmpufahw8h_/250e7c4a_1_bol_1.pdf
2025-09-18 23:25:38,062 - INFO - Page 1: Extracted 1011 characters, 51 lines from a333ba95_15_comm_invoice_2_d84d341d_page_001.pdf
2025-09-18 23:25:38,062 - INFO - Successfully processed page 1
2025-09-18 23:25:38,062 - INFO - Combined 1 pages into final text
2025-09-18 23:25:38,063 - INFO - Text validation for a333ba95_15_comm_invoice_2: 1028 characters, 1 pages
2025-09-18 23:25:38,063 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:38,063 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:38,104 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8355a64b_13_pack_list_10.pdf
2025-09-18 23:25:38,178 - INFO - ✓ Uploaded: input_data_3_per_cat/1_bol/1_bol_10.pdf -> s3://document-extraction-logistically/temp/c2030da7_1_bol_10.pdf
2025-09-18 23:25:38,179 - INFO - 🔍 [23:25:38] Starting classification: 1_bol_10.pdf
2025-09-18 23:25:38,179 - INFO - ⬆️ [23:25:38] Uploading: 1_bol_11.pdf
2025-09-18 23:25:38,179 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:38,197 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:38,202 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c2030da7_1_bol_10.pdf
2025-09-18 23:25:38,202 - INFO - Processing PDF from S3...
2025-09-18 23:25:38,203 - INFO - Downloading PDF from S3 to /tmp/tmpe2s4vi7v/c2030da7_1_bol_10.pdf
2025-09-18 23:25:38,240 - INFO - Page 1: Extracted 690 characters, 57 lines from cfecd9ed_14_po_1_4b2b588e_page_001.pdf
2025-09-18 23:25:38,240 - INFO - Successfully processed page 1
2025-09-18 23:25:38,241 - INFO - Combined 1 pages into final text
2025-09-18 23:25:38,241 - INFO - Text validation for cfecd9ed_14_po_1: 707 characters, 1 pages
2025-09-18 23:25:38,242 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:38,242 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:38,385 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:38,386 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:38,387 - INFO - Splitting PDF a319cd0f_19_coa_1 into 1 pages
2025-09-18 23:25:38,389 - INFO - Splitting PDF 6eced563_19_coa_2 into 1 pages
2025-09-18 23:25:38,394 - INFO - Split PDF into 1 pages
2025-09-18 23:25:38,396 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:38,396 - INFO - Expected pages: [1]
2025-09-18 23:25:38,398 - INFO - Split PDF into 1 pages
2025-09-18 23:25:38,410 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:38,410 - INFO - Expected pages: [1]
2025-09-18 23:25:38,518 - INFO -     Poll #2 - Status: IN_PROGRESS (Elapsed: 2s)
2025-09-18 23:25:38,603 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c75bd811_13_pack_list_1.pdf
2025-09-18 23:25:38,654 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bd4c27a5_14_po_3.pdf
2025-09-18 23:25:38,796 - INFO - Page 3: Extracted 1410 characters, 53 lines from 2e892777_16_customs_doc_1_606dd8cb_page_003.pdf
2025-09-18 23:25:38,797 - INFO - Successfully processed page 3
2025-09-18 23:25:38,899 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ae716473_12_combined_carrier_documents_3.pdf
2025-09-18 23:25:39,028 - INFO - ✓ Uploaded: input_data_3_per_cat/1_bol/1_bol_11.pdf -> s3://document-extraction-logistically/temp/afb33513_1_bol_11.pdf
2025-09-18 23:25:39,029 - INFO - 🔍 [23:25:39] Starting classification: 1_bol_11.pdf
2025-09-18 23:25:39,030 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:39,030 - INFO - ⬆️ [23:25:39] Uploading: 20_tender_from_cust_1.pdf
2025-09-18 23:25:39,049 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:39,056 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/afb33513_1_bol_11.pdf
2025-09-18 23:25:39,056 - INFO - Processing PDF from S3...
2025-09-18 23:25:39,057 - INFO - Downloading PDF from S3 to /tmp/tmp9e3dv4g6/afb33513_1_bol_11.pdf
2025-09-18 23:25:39,081 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d0468672_17_nmfc_cert_1.jpg
2025-09-18 23:25:39,096 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:39,097 - INFO - Splitting PDF 250e7c4a_1_bol_1 into 1 pages
2025-09-18 23:25:39,099 - INFO - Split PDF into 1 pages
2025-09-18 23:25:39,099 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:39,099 - INFO - Expected pages: [1]
2025-09-18 23:25:39,203 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5e604368_15_comm_invoice_1.pdf
2025-09-18 23:25:39,225 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:39,227 - INFO - Splitting PDF 4ca1210a_19_coa_3 into 1 pages
2025-09-18 23:25:39,244 - INFO - Split PDF into 1 pages
2025-09-18 23:25:39,245 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:39,245 - INFO - Expected pages: [1]
2025-09-18 23:25:39,389 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8c8c9078_14_po_2.pdf
2025-09-18 23:25:39,622 - INFO - ✓ Uploaded: input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf -> s3://document-extraction-logistically/temp/938bbf10_20_tender_from_cust_1.pdf
2025-09-18 23:25:39,622 - INFO - 🔍 [23:25:39] Starting classification: 20_tender_from_cust_1.pdf
2025-09-18 23:25:39,622 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:39,625 - INFO - ⬆️ [23:25:39] Uploading: 20_tender_from_cust_2.pdf
2025-09-18 23:25:39,630 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:39,636 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/938bbf10_20_tender_from_cust_1.pdf
2025-09-18 23:25:39,636 - INFO - Processing PDF from S3...
2025-09-18 23:25:39,636 - INFO - Downloading PDF from S3 to /tmp/tmpenxcin51/938bbf10_20_tender_from_cust_1.pdf
2025-09-18 23:25:39,755 - INFO - Page 1: Extracted 2057 characters, 116 lines from 2e892777_16_customs_doc_1_606dd8cb_page_001.pdf
2025-09-18 23:25:39,755 - INFO - Successfully processed page 1
2025-09-18 23:25:39,926 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cfecd9ed_14_po_1.pdf
2025-09-18 23:25:40,006 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a333ba95_15_comm_invoice_2.pdf
2025-09-18 23:25:40,255 - INFO - ✓ Uploaded: input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf -> s3://document-extraction-logistically/temp/fa4f6401_20_tender_from_cust_2.pdf
2025-09-18 23:25:40,255 - INFO - 🔍 [23:25:40] Starting classification: 20_tender_from_cust_2.pdf
2025-09-18 23:25:40,256 - INFO - ⬆️ [23:25:40] Uploading: 20_tender_from_cust_3.pdf
2025-09-18 23:25:40,261 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:40,277 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:40,284 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fa4f6401_20_tender_from_cust_2.pdf
2025-09-18 23:25:40,285 - INFO - Processing PDF from S3...
2025-09-18 23:25:40,285 - INFO - Downloading PDF from S3 to /tmp/tmpaihugohm/fa4f6401_20_tender_from_cust_2.pdf
2025-09-18 23:25:40,811 - INFO -     Poll #3 - Status: IN_PROGRESS (Elapsed: 4s)
2025-09-18 23:25:40,862 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a5ee8741_13_pack_list_2.PDF
2025-09-18 23:25:40,972 - INFO - Page 2: Extracted 485 characters, 29 lines from 85a9af5c_17_nmfc_cert_2_974beca2_page_002.pdf
2025-09-18 23:25:40,973 - INFO - ✓ Uploaded: input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf -> s3://document-extraction-logistically/temp/1977e502_20_tender_from_cust_3.pdf
2025-09-18 23:25:40,973 - INFO - Successfully processed page 2
2025-09-18 23:25:40,974 - INFO - 🔍 [23:25:40] Starting classification: 20_tender_from_cust_3.pdf
2025-09-18 23:25:40,975 - INFO - ⬆️ [23:25:40] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 23:25:40,976 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:40,978 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:40,988 - INFO - Splitting PDF 938bbf10_20_tender_from_cust_1 into 2 pages
2025-09-18 23:25:40,992 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:40,997 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1977e502_20_tender_from_cust_3.pdf
2025-09-18 23:25:40,997 - INFO - Processing PDF from S3...
2025-09-18 23:25:40,999 - INFO - Downloading PDF from S3 to /tmp/tmpdfqq16rl/1977e502_20_tender_from_cust_3.pdf
2025-09-18 23:25:41,008 - INFO - Split PDF into 2 pages
2025-09-18 23:25:41,008 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:41,009 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:41,462 - INFO - Page 1: Extracted 787 characters, 61 lines from 85a9af5c_17_nmfc_cert_2_974beca2_page_001.pdf
2025-09-18 23:25:41,462 - INFO - Successfully processed page 1
2025-09-18 23:25:41,463 - INFO - Combined 2 pages into final text
2025-09-18 23:25:41,463 - INFO - Text validation for 85a9af5c_17_nmfc_cert_2: 1308 characters, 2 pages
2025-09-18 23:25:41,463 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:41,463 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:41,582 - INFO - ✓ Uploaded: input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/0f85970a_21_lumper_receipt_1.jpeg
2025-09-18 23:25:41,582 - INFO - 🔍 [23:25:41] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 23:25:41,583 - INFO - ⬆️ [23:25:41] Uploading: 21_lumper_receipt_10.png
2025-09-18 23:25:41,585 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:41,601 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:41,612 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0f85970a_21_lumper_receipt_1.jpeg
2025-09-18 23:25:41,613 - INFO - Processing image from S3...
2025-09-18 23:25:41,613 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:41,618 - INFO - Splitting PDF fa4f6401_20_tender_from_cust_2 into 2 pages
2025-09-18 23:25:41,621 - INFO - Split PDF into 2 pages
2025-09-18 23:25:41,621 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:41,621 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:42,009 - INFO - Page 1: Extracted 4011 characters, 246 lines from 927c24eb_16_customs_doc_3_de421700_page_001.pdf
2025-09-18 23:25:42,025 - INFO - Successfully processed page 1
2025-09-18 23:25:42,029 - INFO - Page 2: Extracted 3834 characters, 261 lines from 2e892777_16_customs_doc_1_606dd8cb_page_002.pdf
2025-09-18 23:25:42,030 - INFO - Combined 1 pages into final text
2025-09-18 23:25:42,030 - INFO - Text validation for 927c24eb_16_customs_doc_3: 4028 characters, 1 pages
2025-09-18 23:25:42,030 - INFO - Successfully processed page 2
2025-09-18 23:25:42,031 - INFO - Combined 3 pages into final text
2025-09-18 23:25:42,031 - INFO - Text validation for 2e892777_16_customs_doc_1: 7356 characters, 3 pages
2025-09-18 23:25:42,031 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:42,031 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:42,034 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:42,034 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:42,102 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:42,103 - INFO - Splitting PDF c2030da7_1_bol_10 into 1 pages
2025-09-18 23:25:42,105 - INFO - Split PDF into 1 pages
2025-09-18 23:25:42,105 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:42,105 - INFO - Expected pages: [1]
2025-09-18 23:25:42,193 - INFO - ✓ Uploaded: input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/106b3c72_21_lumper_receipt_10.png
2025-09-18 23:25:42,193 - INFO - 🔍 [23:25:42] Starting classification: 21_lumper_receipt_10.png
2025-09-18 23:25:42,194 - INFO - ⬆️ [23:25:42] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 23:25:42,194 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:42,214 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:42,218 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/106b3c72_21_lumper_receipt_10.png
2025-09-18 23:25:42,218 - INFO - Processing image from S3...
2025-09-18 23:25:42,450 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:42,452 - INFO - Splitting PDF afb33513_1_bol_11 into 1 pages
2025-09-18 23:25:42,453 - INFO - Split PDF into 1 pages
2025-09-18 23:25:42,453 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:42,453 - INFO - Expected pages: [1]
2025-09-18 23:25:43,141 - INFO - ✓ Uploaded: input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/68590842_21_lumper_receipt_11.jpeg
2025-09-18 23:25:43,141 - INFO - 🔍 [23:25:43] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 23:25:43,142 - INFO - ⬆️ [23:25:43] Uploading: 22_so_confirmation_1.pdf
2025-09-18 23:25:43,148 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:43,163 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:43,167 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/68590842_21_lumper_receipt_11.jpeg
2025-09-18 23:25:43,168 - INFO - Processing image from S3...
2025-09-18 23:25:43,323 - INFO - Page 2: Extracted 29 characters, 2 lines from 938bbf10_20_tender_from_cust_1_8aeb8923_page_002.pdf
2025-09-18 23:25:43,324 - INFO - Successfully processed page 2
2025-09-18 23:25:43,523 - INFO - Page 1: Extracted 590 characters, 28 lines from 6eced563_19_coa_2_f82fc88d_page_001.pdf
2025-09-18 23:25:43,524 - INFO - Successfully processed page 1
2025-09-18 23:25:43,524 - INFO - Combined 1 pages into final text
2025-09-18 23:25:43,524 - INFO - Text validation for 6eced563_19_coa_2: 607 characters, 1 pages
2025-09-18 23:25:43,524 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:43,524 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:43,756 - INFO - ✓ Uploaded: input_data_3_per_cat/22_so_confirmation/22_so_confirmation_1.pdf -> s3://document-extraction-logistically/temp/ad9c1fcd_22_so_confirmation_1.pdf
2025-09-18 23:25:43,757 - INFO - 🔍 [23:25:43] Starting classification: 22_so_confirmation_1.pdf
2025-09-18 23:25:43,758 - INFO - ⬆️ [23:25:43] Uploading: 22_so_confirmation_2.pdf
2025-09-18 23:25:43,760 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:43,782 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:43,789 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ad9c1fcd_22_so_confirmation_1.pdf
2025-09-18 23:25:43,789 - INFO - Processing PDF from S3...
2025-09-18 23:25:43,790 - INFO - Downloading PDF from S3 to /tmp/tmp0kz8v3hz/ad9c1fcd_22_so_confirmation_1.pdf
2025-09-18 23:25:44,027 - INFO - Page 1: Extracted 879 characters, 30 lines from 4ca1210a_19_coa_3_2c50e5e7_page_001.pdf
2025-09-18 23:25:44,027 - INFO - Successfully processed page 1
2025-09-18 23:25:44,028 - INFO - Combined 1 pages into final text
2025-09-18 23:25:44,028 - INFO - Text validation for 4ca1210a_19_coa_3: 896 characters, 1 pages
2025-09-18 23:25:44,028 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:44,028 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:44,106 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:44,109 - INFO - Splitting PDF 1977e502_20_tender_from_cust_3 into 3 pages
2025-09-18 23:25:44,133 - INFO - Split PDF into 3 pages
2025-09-18 23:25:44,134 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:44,134 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:25:44,295 - INFO - S3 Image temp/0f85970a_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 23:25:44,296 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:44,296 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:44,406 - INFO - ✓ Uploaded: input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf -> s3://document-extraction-logistically/temp/fcb80f8e_22_so_confirmation_2.pdf
2025-09-18 23:25:44,406 - INFO - 🔍 [23:25:44] Starting classification: 22_so_confirmation_2.pdf
2025-09-18 23:25:44,407 - INFO - ⬆️ [23:25:44] Uploading: 22_so_confirmation_3.pdf
2025-09-18 23:25:44,408 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:44,420 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:44,422 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fcb80f8e_22_so_confirmation_2.pdf
2025-09-18 23:25:44,423 - INFO - Processing PDF from S3...
2025-09-18 23:25:44,423 - INFO - Downloading PDF from S3 to /tmp/tmph_yhjoy8/fcb80f8e_22_so_confirmation_2.pdf
2025-09-18 23:25:44,633 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/927c24eb_16_customs_doc_3.pdf
2025-09-18 23:25:44,888 - INFO - Page 2: Extracted 29 characters, 2 lines from fa4f6401_20_tender_from_cust_2_bd7445f1_page_002.pdf
2025-09-18 23:25:44,888 - INFO - Successfully processed page 2
2025-09-18 23:25:45,055 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/85a9af5c_17_nmfc_cert_2.pdf
2025-09-18 23:25:45,057 - INFO - ✓ Uploaded: input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf -> s3://document-extraction-logistically/temp/9218e851_22_so_confirmation_3.pdf
2025-09-18 23:25:45,057 - INFO - 🔍 [23:25:45] Starting classification: 22_so_confirmation_3.pdf
2025-09-18 23:25:45,057 - INFO - ⬆️ [23:25:45] Uploading: 25_ingate_1.pdf
2025-09-18 23:25:45,057 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:45,068 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:45,073 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9218e851_22_so_confirmation_3.pdf
2025-09-18 23:25:45,074 - INFO - Processing PDF from S3...
2025-09-18 23:25:45,074 - INFO - Downloading PDF from S3 to /tmp/tmp61zj7r76/9218e851_22_so_confirmation_3.pdf
2025-09-18 23:25:45,250 - INFO - Page 1: Extracted 517 characters, 56 lines from a319cd0f_19_coa_1_cce3e0b5_page_001.pdf
2025-09-18 23:25:45,250 - INFO - Successfully processed page 1
2025-09-18 23:25:45,250 - INFO - Combined 1 pages into final text
2025-09-18 23:25:45,251 - INFO - Text validation for a319cd0f_19_coa_1: 534 characters, 1 pages
2025-09-18 23:25:45,251 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:45,251 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:45,316 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:45,318 - INFO - Splitting PDF ad9c1fcd_22_so_confirmation_1 into 1 pages
2025-09-18 23:25:45,333 - INFO - Split PDF into 1 pages
2025-09-18 23:25:45,333 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:45,333 - INFO - Expected pages: [1]
2025-09-18 23:25:45,530 - INFO - Page 1: Extracted 1262 characters, 73 lines from fa4f6401_20_tender_from_cust_2_bd7445f1_page_001.pdf
2025-09-18 23:25:45,531 - INFO - Successfully processed page 1
2025-09-18 23:25:45,531 - INFO - Combined 2 pages into final text
2025-09-18 23:25:45,531 - INFO - Text validation for fa4f6401_20_tender_from_cust_2: 1327 characters, 2 pages
2025-09-18 23:25:45,531 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:45,531 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:45,689 - INFO - ✓ Uploaded: input_data_3_per_cat/25_ingate/25_ingate_1.pdf -> s3://document-extraction-logistically/temp/66da01b7_25_ingate_1.pdf
2025-09-18 23:25:45,689 - INFO - 🔍 [23:25:45] Starting classification: 25_ingate_1.pdf
2025-09-18 23:25:45,690 - INFO - ⬆️ [23:25:45] Uploading: 25_ingate_10.png
2025-09-18 23:25:45,690 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:45,698 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:45,706 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/66da01b7_25_ingate_1.pdf
2025-09-18 23:25:45,716 - INFO - Processing PDF from S3...
2025-09-18 23:25:45,718 - INFO - Page 1: Extracted 2681 characters, 83 lines from 250e7c4a_1_bol_1_3d583e96_page_001.pdf
2025-09-18 23:25:45,719 - INFO - Downloading PDF from S3 to /tmp/tmpyk5jyuw1/66da01b7_25_ingate_1.pdf
2025-09-18 23:25:45,719 - INFO - Successfully processed page 1
2025-09-18 23:25:45,722 - INFO - Combined 1 pages into final text
2025-09-18 23:25:45,722 - INFO - Text validation for 250e7c4a_1_bol_1: 2698 characters, 1 pages
2025-09-18 23:25:45,722 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:45,722 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:45,832 - INFO - S3 Image temp/106b3c72_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 23:25:45,832 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:45,832 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:45,990 - INFO -     Poll #4 - Status: SUCCEEDED (Elapsed: 6s)
2025-09-18 23:25:45,990 - INFO - Async method succeeded for S3 image temp/2e531567_16_customs_doc_2.tiff
2025-09-18 23:25:45,991 - INFO - S3 Image temp/2e531567_16_customs_doc_2.tiff: Extracted 4903 characters, 186 lines
2025-09-18 23:25:45,991 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:45,991 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:46,050 - INFO - Page 1: Extracted 1339 characters, 75 lines from 938bbf10_20_tender_from_cust_1_8aeb8923_page_001.pdf
2025-09-18 23:25:46,050 - INFO - Successfully processed page 1
2025-09-18 23:25:46,051 - INFO - Combined 2 pages into final text
2025-09-18 23:25:46,051 - INFO - Text validation for 938bbf10_20_tender_from_cust_1: 1404 characters, 2 pages
2025-09-18 23:25:46,051 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:46,052 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:46,061 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4ca1210a_19_coa_3.pdf
2025-09-18 23:25:46,147 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:46,149 - INFO - Splitting PDF fcb80f8e_22_so_confirmation_2 into 4 pages
2025-09-18 23:25:46,160 - INFO - Split PDF into 4 pages
2025-09-18 23:25:46,160 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:46,161 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-18 23:25:46,407 - INFO - ✓ Uploaded: input_data_3_per_cat/25_ingate/25_ingate_10.png -> s3://document-extraction-logistically/temp/9f80e50e_25_ingate_10.png
2025-09-18 23:25:46,407 - INFO - 🔍 [23:25:46] Starting classification: 25_ingate_10.png
2025-09-18 23:25:46,407 - INFO - ⬆️ [23:25:46] Uploading: 25_ingate_2.pdf
2025-09-18 23:25:46,408 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:46,418 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:46,420 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9f80e50e_25_ingate_10.png
2025-09-18 23:25:46,420 - INFO - Processing image from S3...
2025-09-18 23:25:46,422 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2e892777_16_customs_doc_1.pdf
2025-09-18 23:25:46,598 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0f85970a_21_lumper_receipt_1.jpeg
2025-09-18 23:25:47,009 - INFO - ✓ Uploaded: input_data_3_per_cat/25_ingate/25_ingate_2.pdf -> s3://document-extraction-logistically/temp/b54031a0_25_ingate_2.pdf
2025-09-18 23:25:47,009 - INFO - 🔍 [23:25:47] Starting classification: 25_ingate_2.pdf
2025-09-18 23:25:47,010 - INFO - ⬆️ [23:25:47] Uploading: 26_outgate_1.pdf
2025-09-18 23:25:47,011 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:47,031 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:47,044 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b54031a0_25_ingate_2.pdf
2025-09-18 23:25:47,046 - INFO - S3 Image temp/68590842_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 23:25:47,047 - INFO - Processing PDF from S3...
2025-09-18 23:25:47,047 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:47,048 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:47,048 - INFO - Downloading PDF from S3 to /tmp/tmpu3bxd03h/b54031a0_25_ingate_2.pdf
2025-09-18 23:25:47,334 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:47,334 - INFO - Splitting PDF 9218e851_22_so_confirmation_3 into 2 pages
2025-09-18 23:25:47,349 - INFO - Split PDF into 2 pages
2025-09-18 23:25:47,349 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:47,349 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:47,460 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a319cd0f_19_coa_1.pdf
2025-09-18 23:25:47,501 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fa4f6401_20_tender_from_cust_2.pdf
2025-09-18 23:25:47,621 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/250e7c4a_1_bol_1.pdf
2025-09-18 23:25:47,770 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6eced563_19_coa_2.pdf
2025-09-18 23:25:47,778 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:47,779 - INFO - Splitting PDF 66da01b7_25_ingate_1 into 2 pages
2025-09-18 23:25:47,789 - INFO - Split PDF into 2 pages
2025-09-18 23:25:47,789 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:47,789 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:47,848 - INFO - Page 2: Extracted 229 characters, 10 lines from 1977e502_20_tender_from_cust_3_f3a72da0_page_002.pdf
2025-09-18 23:25:47,849 - INFO - Successfully processed page 2
2025-09-18 23:25:47,891 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/938bbf10_20_tender_from_cust_1.pdf
2025-09-18 23:25:47,948 - INFO - ✓ Uploaded: input_data_3_per_cat/26_outgate/26_outgate_1.pdf -> s3://document-extraction-logistically/temp/7004fbb1_26_outgate_1.pdf
2025-09-18 23:25:47,948 - INFO - 🔍 [23:25:47] Starting classification: 26_outgate_1.pdf
2025-09-18 23:25:47,949 - INFO - ⬆️ [23:25:47] Uploading: 26_outgate_10.pdf
2025-09-18 23:25:47,949 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:47,965 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:47,968 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7004fbb1_26_outgate_1.pdf
2025-09-18 23:25:47,968 - INFO - Processing PDF from S3...
2025-09-18 23:25:47,968 - INFO - Downloading PDF from S3 to /tmp/tmp0g4uhaep/7004fbb1_26_outgate_1.pdf
2025-09-18 23:25:47,999 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2e531567_16_customs_doc_2.tiff
2025-09-18 23:25:48,408 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/106b3c72_21_lumper_receipt_10.png
2025-09-18 23:25:48,547 - INFO - Page 1: Extracted 1081 characters, 102 lines from afb33513_1_bol_11_bea8d867_page_001.pdf
2025-09-18 23:25:48,547 - INFO - Successfully processed page 1
2025-09-18 23:25:48,547 - INFO - Combined 1 pages into final text
2025-09-18 23:25:48,547 - INFO - Text validation for afb33513_1_bol_11: 1098 characters, 1 pages
2025-09-18 23:25:48,547 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:48,547 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:48,580 - INFO - ✓ Uploaded: input_data_3_per_cat/26_outgate/26_outgate_10.pdf -> s3://document-extraction-logistically/temp/7efbe813_26_outgate_10.pdf
2025-09-18 23:25:48,581 - INFO - 🔍 [23:25:48] Starting classification: 26_outgate_10.pdf
2025-09-18 23:25:48,582 - INFO - ⬆️ [23:25:48] Uploading: 26_outgate_11.pdf
2025-09-18 23:25:48,584 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:48,605 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:48,608 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7efbe813_26_outgate_10.pdf
2025-09-18 23:25:48,608 - INFO - Processing PDF from S3...
2025-09-18 23:25:48,608 - INFO - Downloading PDF from S3 to /tmp/tmpwjowvkkl/7efbe813_26_outgate_10.pdf
2025-09-18 23:25:48,846 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:48,848 - INFO - Splitting PDF b54031a0_25_ingate_2 into 1 pages
2025-09-18 23:25:48,862 - INFO - Split PDF into 1 pages
2025-09-18 23:25:48,862 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:48,862 - INFO - Expected pages: [1]
2025-09-18 23:25:49,245 - INFO - ✓ Uploaded: input_data_3_per_cat/26_outgate/26_outgate_11.pdf -> s3://document-extraction-logistically/temp/6622e8d5_26_outgate_11.pdf
2025-09-18 23:25:49,245 - INFO - 🔍 [23:25:49] Starting classification: 26_outgate_11.pdf
2025-09-18 23:25:49,245 - INFO - ⬆️ [23:25:49] Uploading: 2_pod_1.pdf
2025-09-18 23:25:49,246 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:49,319 - INFO - S3 Image temp/9f80e50e_25_ingate_10.png: Extracted 558 characters, 43 lines
2025-09-18 23:25:49,321 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:49,321 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:49,326 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:49,328 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6622e8d5_26_outgate_11.pdf
2025-09-18 23:25:49,328 - INFO - Processing PDF from S3...
2025-09-18 23:25:49,328 - INFO - Downloading PDF from S3 to /tmp/tmpso59yxcn/6622e8d5_26_outgate_11.pdf
2025-09-18 23:25:49,400 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/68590842_21_lumper_receipt_11.jpeg
2025-09-18 23:25:49,467 - INFO - Page 1: Extracted 2091 characters, 153 lines from 1977e502_20_tender_from_cust_3_f3a72da0_page_001.pdf
2025-09-18 23:25:49,468 - INFO - Successfully processed page 1
2025-09-18 23:25:49,521 - INFO - Page 1: Extracted 950 characters, 59 lines from ad9c1fcd_22_so_confirmation_1_0e664e46_page_001.pdf
2025-09-18 23:25:49,523 - INFO - Page 1: Extracted 2034 characters, 111 lines from c2030da7_1_bol_10_29e1d9f4_page_001.pdf
2025-09-18 23:25:49,524 - INFO - Successfully processed page 1
2025-09-18 23:25:49,525 - INFO - Successfully processed page 1
2025-09-18 23:25:49,525 - INFO - Combined 1 pages into final text
2025-09-18 23:25:49,525 - INFO - Text validation for ad9c1fcd_22_so_confirmation_1: 967 characters, 1 pages
2025-09-18 23:25:49,525 - INFO - Combined 1 pages into final text
2025-09-18 23:25:49,526 - INFO - Text validation for c2030da7_1_bol_10: 2051 characters, 1 pages
2025-09-18 23:25:49,526 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:49,526 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:49,526 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:49,526 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:50,229 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/aeba0c89_2_pod_1.pdf
2025-09-18 23:25:50,229 - INFO - 🔍 [23:25:50] Starting classification: 2_pod_1.pdf
2025-09-18 23:25:50,231 - INFO - ⬆️ [23:25:50] Uploading: 2_pod_10.pdf
2025-09-18 23:25:50,237 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:50,256 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:50,260 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/aeba0c89_2_pod_1.pdf
2025-09-18 23:25:50,261 - INFO - Processing PDF from S3...
2025-09-18 23:25:50,261 - INFO - Downloading PDF from S3 to /tmp/tmp8tv5hbm7/aeba0c89_2_pod_1.pdf
2025-09-18 23:25:50,605 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/afb33513_1_bol_11.pdf
2025-09-18 23:25:50,730 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:50,731 - INFO - Splitting PDF 7efbe813_26_outgate_10 into 1 pages
2025-09-18 23:25:50,732 - INFO - Split PDF into 1 pages
2025-09-18 23:25:50,732 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:50,732 - INFO - Expected pages: [1]
2025-09-18 23:25:50,837 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_10.pdf -> s3://document-extraction-logistically/temp/65cc422f_2_pod_10.pdf
2025-09-18 23:25:50,837 - INFO - 🔍 [23:25:50] Starting classification: 2_pod_10.pdf
2025-09-18 23:25:50,838 - INFO - ⬆️ [23:25:50] Uploading: 2_pod_11.pdf
2025-09-18 23:25:50,839 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:50,859 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:50,869 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/65cc422f_2_pod_10.pdf
2025-09-18 23:25:50,869 - INFO - Processing PDF from S3...
2025-09-18 23:25:50,870 - INFO - Downloading PDF from S3 to /tmp/tmpuh6lh8p5/65cc422f_2_pod_10.pdf
2025-09-18 23:25:51,173 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c2030da7_1_bol_10.pdf
2025-09-18 23:25:51,318 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9f80e50e_25_ingate_10.png
2025-09-18 23:25:51,522 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_11.pdf -> s3://document-extraction-logistically/temp/24b3411f_2_pod_11.pdf
2025-09-18 23:25:51,523 - INFO - 🔍 [23:25:51] Starting classification: 2_pod_11.pdf
2025-09-18 23:25:51,524 - INFO - ⬆️ [23:25:51] Uploading: 3_rate_confirmation_1.pdf
2025-09-18 23:25:51,525 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:51,544 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:51,549 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/24b3411f_2_pod_11.pdf
2025-09-18 23:25:51,550 - INFO - Processing PDF from S3...
2025-09-18 23:25:51,550 - INFO - Downloading PDF from S3 to /tmp/tmp0v48emoo/24b3411f_2_pod_11.pdf
2025-09-18 23:25:51,709 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ad9c1fcd_22_so_confirmation_1.pdf
2025-09-18 23:25:52,063 - INFO - Page 1: Extracted 605 characters, 27 lines from 66da01b7_25_ingate_1_0f727851_page_001.pdf
2025-09-18 23:25:52,063 - INFO - Successfully processed page 1
2025-09-18 23:25:52,304 - INFO - ✓ Uploaded: input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_1.pdf -> s3://document-extraction-logistically/temp/722807fd_3_rate_confirmation_1.pdf
2025-09-18 23:25:52,304 - INFO - 🔍 [23:25:52] Starting classification: 3_rate_confirmation_1.pdf
2025-09-18 23:25:52,305 - INFO - ⬆️ [23:25:52] Uploading: 3_rate_confirmation_2.pdf
2025-09-18 23:25:52,307 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:52,326 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:52,329 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/722807fd_3_rate_confirmation_1.pdf
2025-09-18 23:25:52,329 - INFO - Processing PDF from S3...
2025-09-18 23:25:52,330 - INFO - Downloading PDF from S3 to /tmp/tmp88kudst9/722807fd_3_rate_confirmation_1.pdf
2025-09-18 23:25:52,401 - INFO - Page 2: Extracted 538 characters, 7 lines from 66da01b7_25_ingate_1_0f727851_page_002.pdf
2025-09-18 23:25:52,402 - INFO - Successfully processed page 2
2025-09-18 23:25:52,427 - INFO - Combined 2 pages into final text
2025-09-18 23:25:52,433 - INFO - Text validation for 66da01b7_25_ingate_1: 1179 characters, 2 pages
2025-09-18 23:25:52,435 - INFO - Page 3: Extracted 6732 characters, 63 lines from fcb80f8e_22_so_confirmation_2_056e71a9_page_003.pdf
2025-09-18 23:25:52,436 - INFO - Successfully processed page 3
2025-09-18 23:25:52,437 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:52,437 - INFO - Splitting PDF 65cc422f_2_pod_10 into 1 pages
2025-09-18 23:25:52,438 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:52,438 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:52,441 - INFO - Split PDF into 1 pages
2025-09-18 23:25:52,441 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:52,441 - INFO - Expected pages: [1]
2025-09-18 23:25:52,479 - INFO - Page 1: Extracted 1006 characters, 66 lines from 9218e851_22_so_confirmation_3_b10164d9_page_001.pdf
2025-09-18 23:25:52,479 - INFO - Successfully processed page 1
2025-09-18 23:25:52,821 - INFO - Page 2: Extracted 2902 characters, 61 lines from 9218e851_22_so_confirmation_3_b10164d9_page_002.pdf
2025-09-18 23:25:52,821 - INFO - Successfully processed page 2
2025-09-18 23:25:52,821 - INFO - Combined 2 pages into final text
2025-09-18 23:25:52,822 - INFO - Text validation for 9218e851_22_so_confirmation_3: 3944 characters, 2 pages
2025-09-18 23:25:52,822 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:52,822 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:52,964 - INFO - Page 3: Extracted 6281 characters, 47 lines from 1977e502_20_tender_from_cust_3_f3a72da0_page_003.pdf
2025-09-18 23:25:52,965 - INFO - Successfully processed page 3
2025-09-18 23:25:52,965 - INFO - Combined 3 pages into final text
2025-09-18 23:25:52,965 - INFO - Text validation for 1977e502_20_tender_from_cust_3: 8656 characters, 3 pages
2025-09-18 23:25:52,965 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:52,965 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:53,024 - INFO - ✓ Uploaded: input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_2.pdf -> s3://document-extraction-logistically/temp/9b035f4f_3_rate_confirmation_2.pdf
2025-09-18 23:25:53,025 - INFO - 🔍 [23:25:53] Starting classification: 3_rate_confirmation_2.pdf
2025-09-18 23:25:53,026 - INFO - ⬆️ [23:25:53] Uploading: 3_rate_confirmation_3.pdf
2025-09-18 23:25:53,028 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:53,047 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:53,052 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9b035f4f_3_rate_confirmation_2.pdf
2025-09-18 23:25:53,052 - INFO - Processing PDF from S3...
2025-09-18 23:25:53,053 - INFO - Downloading PDF from S3 to /tmp/tmpjx_ex071/9b035f4f_3_rate_confirmation_2.pdf
2025-09-18 23:25:53,228 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:53,230 - INFO - Splitting PDF aeba0c89_2_pod_1 into 3 pages
2025-09-18 23:25:53,234 - INFO - Split PDF into 3 pages
2025-09-18 23:25:53,234 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:53,234 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:25:53,401 - INFO - Page 2: Extracted 6360 characters, 63 lines from fcb80f8e_22_so_confirmation_2_056e71a9_page_002.pdf
2025-09-18 23:25:53,402 - INFO - Successfully processed page 2
2025-09-18 23:25:53,603 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:53,605 - INFO - Splitting PDF 24b3411f_2_pod_11 into 1 pages
2025-09-18 23:25:53,608 - INFO - Split PDF into 1 pages
2025-09-18 23:25:53,608 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:53,609 - INFO - Expected pages: [1]
2025-09-18 23:25:53,660 - INFO - Page 1: Extracted 2650 characters, 102 lines from fcb80f8e_22_so_confirmation_2_056e71a9_page_001.pdf
2025-09-18 23:25:53,660 - INFO - Successfully processed page 1
2025-09-18 23:25:53,697 - INFO - ✓ Uploaded: input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf -> s3://document-extraction-logistically/temp/e634e69b_3_rate_confirmation_3.pdf
2025-09-18 23:25:53,697 - INFO - 🔍 [23:25:53] Starting classification: 3_rate_confirmation_3.pdf
2025-09-18 23:25:53,698 - INFO - ⬆️ [23:25:53] Uploading: 5_clear_to_pay_1.pdf
2025-09-18 23:25:53,698 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:53,711 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:53,714 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e634e69b_3_rate_confirmation_3.pdf
2025-09-18 23:25:53,715 - INFO - Processing PDF from S3...
2025-09-18 23:25:53,715 - INFO - Downloading PDF from S3 to /tmp/tmpnxo8xeej/e634e69b_3_rate_confirmation_3.pdf
2025-09-18 23:25:53,754 - INFO - Page 1: Extracted 852 characters, 22 lines from b54031a0_25_ingate_2_7f492d42_page_001.pdf
2025-09-18 23:25:53,755 - INFO - Successfully processed page 1
2025-09-18 23:25:53,755 - INFO - Combined 1 pages into final text
2025-09-18 23:25:53,755 - INFO - Text validation for b54031a0_25_ingate_2: 869 characters, 1 pages
2025-09-18 23:25:53,755 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:53,755 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:53,841 - INFO - Page 4: Extracted 5052 characters, 47 lines from fcb80f8e_22_so_confirmation_2_056e71a9_page_004.pdf
2025-09-18 23:25:53,841 - INFO - Successfully processed page 4
2025-09-18 23:25:53,841 - INFO - Combined 4 pages into final text
2025-09-18 23:25:53,842 - INFO - Text validation for fcb80f8e_22_so_confirmation_2: 20868 characters, 4 pages
2025-09-18 23:25:53,842 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:53,842 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:54,147 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:54,148 - INFO - Splitting PDF 6622e8d5_26_outgate_11 into 1 pages
2025-09-18 23:25:54,149 - INFO - Split PDF into 1 pages
2025-09-18 23:25:54,149 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:54,149 - INFO - Expected pages: [1]
2025-09-18 23:25:54,415 - INFO - ✓ Uploaded: input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_1.pdf -> s3://document-extraction-logistically/temp/52eb333a_5_clear_to_pay_1.pdf
2025-09-18 23:25:54,415 - INFO - 🔍 [23:25:54] Starting classification: 5_clear_to_pay_1.pdf
2025-09-18 23:25:54,415 - INFO - ⬆️ [23:25:54] Uploading: 5_clear_to_pay_2.pdf
2025-09-18 23:25:54,416 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:54,424 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:54,428 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/52eb333a_5_clear_to_pay_1.pdf
2025-09-18 23:25:54,428 - INFO - Processing PDF from S3...
2025-09-18 23:25:54,429 - INFO - Downloading PDF from S3 to /tmp/tmp0o_pt2m_/52eb333a_5_clear_to_pay_1.pdf
2025-09-18 23:25:54,616 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:54,620 - INFO - Splitting PDF 7004fbb1_26_outgate_1 into 2 pages
2025-09-18 23:25:54,728 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:54,730 - INFO - Splitting PDF 722807fd_3_rate_confirmation_1 into 1 pages
2025-09-18 23:25:54,733 - INFO - Split PDF into 1 pages
2025-09-18 23:25:54,734 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:54,734 - INFO - Expected pages: [1]
2025-09-18 23:25:54,761 - INFO - Split PDF into 2 pages
2025-09-18 23:25:54,761 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:54,761 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:55,078 - INFO - ✓ Uploaded: input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_2.pdf -> s3://document-extraction-logistically/temp/3bab47c9_5_clear_to_pay_2.pdf
2025-09-18 23:25:55,078 - INFO - 🔍 [23:25:55] Starting classification: 5_clear_to_pay_2.pdf
2025-09-18 23:25:55,079 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:55,079 - INFO - ⬆️ [23:25:55] Uploading: 6_scale_ticket_1.pdf
2025-09-18 23:25:55,094 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:55,097 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3bab47c9_5_clear_to_pay_2.pdf
2025-09-18 23:25:55,097 - INFO - Processing PDF from S3...
2025-09-18 23:25:55,097 - INFO - Downloading PDF from S3 to /tmp/tmp3o0k1bl_/3bab47c9_5_clear_to_pay_2.pdf
2025-09-18 23:25:55,227 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1977e502_20_tender_from_cust_3.pdf
2025-09-18 23:25:55,399 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:55,400 - INFO - Splitting PDF 9b035f4f_3_rate_confirmation_2 into 1 pages
2025-09-18 23:25:55,405 - INFO - Split PDF into 1 pages
2025-09-18 23:25:55,405 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:55,405 - INFO - Expected pages: [1]
2025-09-18 23:25:55,822 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/c2d1a484_6_scale_ticket_1.pdf
2025-09-18 23:25:55,822 - INFO - 🔍 [23:25:55] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 23:25:55,824 - INFO - ⬆️ [23:25:55] Uploading: 6_scale_ticket_10.png
2025-09-18 23:25:55,825 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:55,832 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:55,845 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:55,849 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c2d1a484_6_scale_ticket_1.pdf
2025-09-18 23:25:55,849 - INFO - Splitting PDF e634e69b_3_rate_confirmation_3 into 2 pages
2025-09-18 23:25:55,849 - INFO - Processing PDF from S3...
2025-09-18 23:25:55,850 - INFO - Downloading PDF from S3 to /tmp/tmp3824fz0g/c2d1a484_6_scale_ticket_1.pdf
2025-09-18 23:25:55,868 - INFO - Split PDF into 2 pages
2025-09-18 23:25:55,868 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:55,868 - INFO - Expected pages: [1, 2]
2025-09-18 23:25:55,951 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9218e851_22_so_confirmation_3.pdf
2025-09-18 23:25:56,080 - INFO - Page 1: Extracted 851 characters, 49 lines from 7efbe813_26_outgate_10_834dd4d5_page_001.pdf
2025-09-18 23:25:56,081 - INFO - Successfully processed page 1
2025-09-18 23:25:56,081 - INFO - Combined 1 pages into final text
2025-09-18 23:25:56,081 - INFO - Text validation for 7efbe813_26_outgate_10: 868 characters, 1 pages
2025-09-18 23:25:56,081 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:56,081 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:56,110 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b54031a0_25_ingate_2.pdf
2025-09-18 23:25:56,697 - INFO - Page 1: Extracted 961 characters, 60 lines from 65cc422f_2_pod_10_9f73d17b_page_001.pdf
2025-09-18 23:25:56,697 - INFO - Successfully processed page 1
2025-09-18 23:25:56,698 - INFO - Combined 1 pages into final text
2025-09-18 23:25:56,698 - INFO - Text validation for 65cc422f_2_pod_10: 978 characters, 1 pages
2025-09-18 23:25:56,698 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:56,698 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:56,854 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:56,855 - INFO - Splitting PDF 52eb333a_5_clear_to_pay_1 into 1 pages
2025-09-18 23:25:56,859 - INFO - Split PDF into 1 pages
2025-09-18 23:25:56,859 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:56,859 - INFO - Expected pages: [1]
2025-09-18 23:25:56,875 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:56,876 - INFO - Splitting PDF 3bab47c9_5_clear_to_pay_2 into 1 pages
2025-09-18 23:25:56,879 - INFO - Split PDF into 1 pages
2025-09-18 23:25:56,879 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:56,879 - INFO - Expected pages: [1]
2025-09-18 23:25:56,906 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/484081c3_6_scale_ticket_10.png
2025-09-18 23:25:56,907 - INFO - 🔍 [23:25:56] Starting classification: 6_scale_ticket_10.png
2025-09-18 23:25:56,908 - INFO - ⬆️ [23:25:56] Uploading: 6_scale_ticket_11.jpg
2025-09-18 23:25:56,910 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:56,939 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:56,942 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/484081c3_6_scale_ticket_10.png
2025-09-18 23:25:56,942 - INFO - Processing image from S3...
2025-09-18 23:25:57,093 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/66da01b7_25_ingate_1.pdf
2025-09-18 23:25:57,869 - INFO - Page 3: Extracted 441 characters, 20 lines from aeba0c89_2_pod_1_4d269aa3_page_003.pdf
2025-09-18 23:25:57,870 - INFO - Successfully processed page 3
2025-09-18 23:25:58,150 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fcb80f8e_22_so_confirmation_2.pdf
2025-09-18 23:25:58,156 - INFO - Splitting PDF into individual pages...
2025-09-18 23:25:58,160 - INFO - Splitting PDF c2d1a484_6_scale_ticket_1 into 1 pages
2025-09-18 23:25:58,162 - INFO - Split PDF into 1 pages
2025-09-18 23:25:58,162 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:25:58,162 - INFO - Expected pages: [1]
2025-09-18 23:25:58,164 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7efbe813_26_outgate_10.pdf
2025-09-18 23:25:58,462 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg -> s3://document-extraction-logistically/temp/78b15fe0_6_scale_ticket_11.jpg
2025-09-18 23:25:58,462 - INFO - 🔍 [23:25:58] Starting classification: 6_scale_ticket_11.jpg
2025-09-18 23:25:58,463 - INFO - ⬆️ [23:25:58] Uploading: 8_log_1.pdf
2025-09-18 23:25:58,466 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:58,485 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:58,490 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/78b15fe0_6_scale_ticket_11.jpg
2025-09-18 23:25:58,490 - INFO - Processing image from S3...
2025-09-18 23:25:59,101 - INFO - ✓ Uploaded: input_data_3_per_cat/8_log/8_log_1.pdf -> s3://document-extraction-logistically/temp/bd078963_8_log_1.pdf
2025-09-18 23:25:59,102 - INFO - 🔍 [23:25:59] Starting classification: 8_log_1.pdf
2025-09-18 23:25:59,103 - INFO - ⬆️ [23:25:59] Uploading: 8_log_10.jpeg
2025-09-18 23:25:59,108 - INFO - Initializing TextractProcessor...
2025-09-18 23:25:59,125 - INFO - Initializing BedrockProcessor...
2025-09-18 23:25:59,133 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bd078963_8_log_1.pdf
2025-09-18 23:25:59,133 - INFO - Processing PDF from S3...
2025-09-18 23:25:59,134 - INFO - Downloading PDF from S3 to /tmp/tmpyl53lj9d/bd078963_8_log_1.pdf
2025-09-18 23:25:59,161 - INFO - Page 1: Extracted 797 characters, 46 lines from 24b3411f_2_pod_11_8d6f8e2c_page_001.pdf
2025-09-18 23:25:59,169 - INFO - Page 1: Extracted 838 characters, 47 lines from 6622e8d5_26_outgate_11_48268a87_page_001.pdf
2025-09-18 23:25:59,169 - INFO - Successfully processed page 1
2025-09-18 23:25:59,170 - INFO - Successfully processed page 1
2025-09-18 23:25:59,170 - INFO - Combined 1 pages into final text
2025-09-18 23:25:59,171 - INFO - Text validation for 6622e8d5_26_outgate_11: 855 characters, 1 pages
2025-09-18 23:25:59,171 - INFO - Combined 1 pages into final text
2025-09-18 23:25:59,171 - INFO - Text validation for 24b3411f_2_pod_11: 814 characters, 1 pages
2025-09-18 23:25:59,172 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:59,172 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:59,172 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:59,172 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:59,431 - INFO - Page 1: Extracted 1597 characters, 78 lines from aeba0c89_2_pod_1_4d269aa3_page_001.pdf
2025-09-18 23:25:59,432 - INFO - Successfully processed page 1
2025-09-18 23:25:59,598 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/65cc422f_2_pod_10.pdf
2025-09-18 23:25:59,650 - INFO - Page 2: Extracted 1895 characters, 65 lines from aeba0c89_2_pod_1_4d269aa3_page_002.pdf
2025-09-18 23:25:59,650 - INFO - Successfully processed page 2
2025-09-18 23:25:59,651 - INFO - Combined 3 pages into final text
2025-09-18 23:25:59,651 - INFO - Text validation for aeba0c89_2_pod_1: 3988 characters, 3 pages
2025-09-18 23:25:59,652 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:25:59,652 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:25:59,910 - INFO - Page 2: Extracted 510 characters, 15 lines from e634e69b_3_rate_confirmation_3_5f04aa4a_page_002.pdf
2025-09-18 23:25:59,910 - INFO - Successfully processed page 2
2025-09-18 23:26:00,574 - INFO - S3 Image temp/484081c3_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 23:26:00,574 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:00,574 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:00,772 - INFO - Page 1: Extracted 1245 characters, 64 lines from e634e69b_3_rate_confirmation_3_5f04aa4a_page_001.pdf
2025-09-18 23:26:00,773 - INFO - Successfully processed page 1
2025-09-18 23:26:00,773 - INFO - Combined 2 pages into final text
2025-09-18 23:26:00,773 - INFO - Text validation for e634e69b_3_rate_confirmation_3: 1791 characters, 2 pages
2025-09-18 23:26:00,774 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:00,774 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:00,950 - INFO - ✓ Uploaded: input_data_3_per_cat/8_log/8_log_10.jpeg -> s3://document-extraction-logistically/temp/ebdccbae_8_log_10.jpeg
2025-09-18 23:26:00,951 - INFO - 🔍 [23:26:00] Starting classification: 8_log_10.jpeg
2025-09-18 23:26:00,951 - INFO - ⬆️ [23:26:00] Uploading: 8_log_11.jpg
2025-09-18 23:26:00,952 - INFO - Initializing TextractProcessor...
2025-09-18 23:26:00,960 - INFO - Initializing BedrockProcessor...
2025-09-18 23:26:00,962 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ebdccbae_8_log_10.jpeg
2025-09-18 23:26:00,962 - INFO - Processing image from S3...
2025-09-18 23:26:01,214 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6622e8d5_26_outgate_11.pdf
2025-09-18 23:26:01,219 - INFO - Page 1: Extracted 1259 characters, 67 lines from 9b035f4f_3_rate_confirmation_2_0ab6a3e7_page_001.pdf
2025-09-18 23:26:01,228 - INFO - Successfully processed page 1
2025-09-18 23:26:01,230 - INFO - Page 1: Extracted 721 characters, 35 lines from 3bab47c9_5_clear_to_pay_2_006acbed_page_001.pdf
2025-09-18 23:26:01,231 - INFO - Combined 1 pages into final text
2025-09-18 23:26:01,231 - INFO - Successfully processed page 1
2025-09-18 23:26:01,232 - INFO - Text validation for 9b035f4f_3_rate_confirmation_2: 1276 characters, 1 pages
2025-09-18 23:26:01,232 - INFO - Combined 1 pages into final text
2025-09-18 23:26:01,232 - INFO - Text validation for 3bab47c9_5_clear_to_pay_2: 738 characters, 1 pages
2025-09-18 23:26:01,233 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:01,233 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:01,236 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:01,236 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:01,550 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/24b3411f_2_pod_11.pdf
2025-09-18 23:26:01,606 - INFO - Page 2: Extracted 269 characters, 15 lines from 7004fbb1_26_outgate_1_2bad6c7b_page_002.pdf
2025-09-18 23:26:01,606 - INFO - Successfully processed page 2
2025-09-18 23:26:01,845 - INFO - S3 Image temp/78b15fe0_6_scale_ticket_11.jpg: Extracted 361 characters, 32 lines
2025-09-18 23:26:01,846 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:01,846 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:02,261 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/484081c3_6_scale_ticket_10.png
2025-09-18 23:26:02,389 - INFO - Page 1: Extracted 749 characters, 42 lines from 52eb333a_5_clear_to_pay_1_e6868751_page_001.pdf
2025-09-18 23:26:02,389 - INFO - Successfully processed page 1
2025-09-18 23:26:02,390 - INFO - Combined 1 pages into final text
2025-09-18 23:26:02,390 - INFO - Text validation for 52eb333a_5_clear_to_pay_1: 766 characters, 1 pages
2025-09-18 23:26:02,391 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:02,391 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:02,432 - INFO - Page 1: Extracted 1211 characters, 66 lines from 722807fd_3_rate_confirmation_1_5d8946b9_page_001.pdf
2025-09-18 23:26:02,433 - INFO - Successfully processed page 1
2025-09-18 23:26:02,434 - INFO - Splitting PDF into individual pages...
2025-09-18 23:26:02,434 - INFO - Combined 1 pages into final text
2025-09-18 23:26:02,434 - INFO - Text validation for 722807fd_3_rate_confirmation_1: 1228 characters, 1 pages
2025-09-18 23:26:02,435 - INFO - Splitting PDF bd078963_8_log_1 into 3 pages
2025-09-18 23:26:02,436 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:02,437 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:02,440 - INFO - Split PDF into 3 pages
2025-09-18 23:26:02,440 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:26:02,440 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:26:02,594 - INFO - Page 1: Extracted 558 characters, 30 lines from 7004fbb1_26_outgate_1_2bad6c7b_page_001.pdf
2025-09-18 23:26:02,595 - INFO - Successfully processed page 1
2025-09-18 23:26:02,595 - INFO - Combined 2 pages into final text
2025-09-18 23:26:02,596 - INFO - Text validation for 7004fbb1_26_outgate_1: 863 characters, 2 pages
2025-09-18 23:26:02,596 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:02,596 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:03,059 - INFO - ✓ Uploaded: input_data_3_per_cat/8_log/8_log_11.jpg -> s3://document-extraction-logistically/temp/1b38bb37_8_log_11.jpg
2025-09-18 23:26:03,060 - INFO - 🔍 [23:26:03] Starting classification: 8_log_11.jpg
2025-09-18 23:26:03,061 - INFO - ⬆️ [23:26:03] Uploading: 9_fuel_receipt_1.png
2025-09-18 23:26:03,068 - INFO - Initializing TextractProcessor...
2025-09-18 23:26:03,086 - INFO - Initializing BedrockProcessor...
2025-09-18 23:26:03,093 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e634e69b_3_rate_confirmation_3.pdf
2025-09-18 23:26:03,096 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1b38bb37_8_log_11.jpg
2025-09-18 23:26:03,097 - INFO - Processing image from S3...
2025-09-18 23:26:03,112 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/aeba0c89_2_pod_1.pdf
2025-09-18 23:26:03,244 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3bab47c9_5_clear_to_pay_2.pdf
2025-09-18 23:26:03,369 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9b035f4f_3_rate_confirmation_2.pdf
2025-09-18 23:26:03,568 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/78b15fe0_6_scale_ticket_11.jpg
2025-09-18 23:26:03,661 - INFO - ✓ Uploaded: input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_1.png -> s3://document-extraction-logistically/temp/f61247c0_9_fuel_receipt_1.png
2025-09-18 23:26:03,661 - INFO - 🔍 [23:26:03] Starting classification: 9_fuel_receipt_1.png
2025-09-18 23:26:03,662 - INFO - ⬆️ [23:26:03] Uploading: 9_fuel_receipt_10.jpeg
2025-09-18 23:26:03,664 - INFO - Initializing TextractProcessor...
2025-09-18 23:26:03,685 - INFO - Initializing BedrockProcessor...
2025-09-18 23:26:03,689 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f61247c0_9_fuel_receipt_1.png
2025-09-18 23:26:03,689 - INFO - Processing image from S3...
2025-09-18 23:26:03,892 - INFO - Page 1: Extracted 1382 characters, 62 lines from c2d1a484_6_scale_ticket_1_87e222e3_page_001.pdf
2025-09-18 23:26:03,892 - INFO - Successfully processed page 1
2025-09-18 23:26:03,892 - INFO - Combined 1 pages into final text
2025-09-18 23:26:03,893 - INFO - Text validation for c2d1a484_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 23:26:03,893 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:03,893 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:04,230 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/722807fd_3_rate_confirmation_1.pdf
2025-09-18 23:26:04,414 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/52eb333a_5_clear_to_pay_1.pdf
2025-09-18 23:26:04,863 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7004fbb1_26_outgate_1.pdf
2025-09-18 23:26:05,277 - INFO - S3 Image temp/ebdccbae_8_log_10.jpeg: Extracted 526 characters, 56 lines
2025-09-18 23:26:05,277 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:05,278 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:05,614 - INFO - ✓ Uploaded: input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_10.jpeg -> s3://document-extraction-logistically/temp/756463b8_9_fuel_receipt_10.jpeg
2025-09-18 23:26:05,614 - INFO - 🔍 [23:26:05] Starting classification: 9_fuel_receipt_10.jpeg
2025-09-18 23:26:05,615 - INFO - ⬆️ [23:26:05] Uploading: 9_fuel_receipt_11.pdf
2025-09-18 23:26:05,618 - INFO - Initializing TextractProcessor...
2025-09-18 23:26:05,635 - INFO - Initializing BedrockProcessor...
2025-09-18 23:26:05,641 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/756463b8_9_fuel_receipt_10.jpeg
2025-09-18 23:26:05,641 - INFO - Processing image from S3...
2025-09-18 23:26:05,813 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c2d1a484_6_scale_ticket_1.pdf
2025-09-18 23:26:06,035 - INFO - Page 2: Extracted 492 characters, 33 lines from bd078963_8_log_1_f6b40fef_page_002.pdf
2025-09-18 23:26:06,036 - INFO - Successfully processed page 2
2025-09-18 23:26:06,633 - INFO - Page 3: Extracted 804 characters, 46 lines from bd078963_8_log_1_f6b40fef_page_003.pdf
2025-09-18 23:26:06,634 - INFO - Successfully processed page 3
2025-09-18 23:26:06,956 - INFO - Page 1: Extracted 535 characters, 49 lines from bd078963_8_log_1_f6b40fef_page_001.pdf
2025-09-18 23:26:06,956 - INFO - Successfully processed page 1
2025-09-18 23:26:06,956 - INFO - Combined 3 pages into final text
2025-09-18 23:26:06,957 - INFO - Text validation for bd078963_8_log_1: 1886 characters, 3 pages
2025-09-18 23:26:06,957 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:06,957 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:07,091 - INFO - S3 Image temp/f61247c0_9_fuel_receipt_1.png: Extracted 517 characters, 46 lines
2025-09-18 23:26:07,091 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:07,091 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:07,576 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ebdccbae_8_log_10.jpeg
2025-09-18 23:26:07,606 - INFO - S3 Image temp/1b38bb37_8_log_11.jpg: Extracted 1060 characters, 100 lines
2025-09-18 23:26:07,607 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:07,607 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:07,821 - INFO - ✓ Uploaded: input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_11.pdf -> s3://document-extraction-logistically/temp/86a475b4_9_fuel_receipt_11.pdf
2025-09-18 23:26:07,821 - INFO - 🔍 [23:26:07] Starting classification: 9_fuel_receipt_11.pdf
2025-09-18 23:26:07,824 - INFO - Initializing TextractProcessor...
2025-09-18 23:26:07,844 - INFO - Initializing BedrockProcessor...
2025-09-18 23:26:07,852 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/86a475b4_9_fuel_receipt_11.pdf
2025-09-18 23:26:07,852 - INFO - Processing PDF from S3...
2025-09-18 23:26:07,853 - INFO - Downloading PDF from S3 to /tmp/tmp9etw7p32/86a475b4_9_fuel_receipt_11.pdf
2025-09-18 23:26:07,873 - INFO - 

10_invoice_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:26:07,873 - INFO - 

✓ Saved result: output/run1_10_invoice_10.json
2025-09-18 23:26:08,171 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b937c795_10_invoice_10.pdf
2025-09-18 23:26:08,191 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:26:08,191 - INFO - 

✓ Saved result: output/run1_10_invoice_1.json
2025-09-18 23:26:08,483 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b94e46b6_10_invoice_1.pdf
2025-09-18 23:26:08,503 - INFO - 

10_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:26:08,503 - INFO - 

✓ Saved result: output/run1_10_invoice_2.json
2025-09-18 23:26:08,795 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/13212ef8_10_invoice_2.pdf
2025-09-18 23:26:08,812 - INFO - 

12_combined_carrier_documents_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 2,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 23:26:08,812 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_1.json
2025-09-18 23:26:08,942 - INFO - S3 Image temp/756463b8_9_fuel_receipt_10.jpeg: Extracted 318 characters, 28 lines
2025-09-18 23:26:08,942 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:08,942 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:09,112 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cd2341fe_12_combined_carrier_documents_1.pdf
2025-09-18 23:26:09,157 - INFO - 

12_combined_carrier_documents_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:26:09,157 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_2.json
2025-09-18 23:26:09,457 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3f25d56e_12_combined_carrier_documents_2.pdf
2025-09-18 23:26:09,481 - INFO - 

13_pack_list_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 23:26:09,481 - INFO - 

✓ Saved result: output/run1_13_pack_list_10.json
2025-09-18 23:26:09,773 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8355a64b_13_pack_list_10.pdf
2025-09-18 23:26:09,790 - INFO - 

13_pack_list_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 23:26:09,790 - INFO - 

✓ Saved result: output/run1_13_pack_list_1.json
2025-09-18 23:26:10,089 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c75bd811_13_pack_list_1.pdf
2025-09-18 23:26:10,128 - INFO - 

14_po_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-18 23:26:10,128 - INFO - 

✓ Saved result: output/run1_14_po_3.json
2025-09-18 23:26:10,144 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1b38bb37_8_log_11.jpg
2025-09-18 23:26:10,194 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f61247c0_9_fuel_receipt_1.png
2025-09-18 23:26:10,431 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bd4c27a5_14_po_3.pdf
2025-09-18 23:26:10,474 - INFO - 

12_combined_carrier_documents_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 23:26:10,474 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_3.json
2025-09-18 23:26:10,742 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/756463b8_9_fuel_receipt_10.jpeg
2025-09-18 23:26:10,773 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ae716473_12_combined_carrier_documents_3.pdf
2025-09-18 23:26:10,821 - INFO - 

17_nmfc_cert_1.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-18 23:26:10,822 - INFO - 

✓ Saved result: output/run1_17_nmfc_cert_1.json
2025-09-18 23:26:11,122 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d0468672_17_nmfc_cert_1.jpg
2025-09-18 23:26:11,134 - INFO - 

15_comm_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-18 23:26:11,134 - INFO - 

✓ Saved result: output/run1_15_comm_invoice_1.json
2025-09-18 23:26:11,172 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bd078963_8_log_1.pdf
2025-09-18 23:26:11,434 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5e604368_15_comm_invoice_1.pdf
2025-09-18 23:26:11,445 - INFO - 

14_po_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 23:26:11,445 - INFO - 

✓ Saved result: output/run1_14_po_2.json
2025-09-18 23:26:11,747 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8c8c9078_14_po_2.pdf
2025-09-18 23:26:11,758 - INFO - 

14_po_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 23:26:11,758 - INFO - 

✓ Saved result: output/run1_14_po_1.json
2025-09-18 23:26:12,054 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cfecd9ed_14_po_1.pdf
2025-09-18 23:26:12,074 - INFO - 

15_comm_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-18 23:26:12,075 - INFO - 

✓ Saved result: output/run1_15_comm_invoice_2.json
2025-09-18 23:26:12,368 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a333ba95_15_comm_invoice_2.pdf
2025-09-18 23:26:12,399 - INFO - 

13_pack_list_2.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:26:12,399 - INFO - 

✓ Saved result: output/run1_13_pack_list_2.json
2025-09-18 23:26:12,703 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a5ee8741_13_pack_list_2.PDF
2025-09-18 23:26:12,758 - INFO - 

16_customs_doc_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 23:26:12,758 - INFO - 

✓ Saved result: output/run1_16_customs_doc_3.json
2025-09-18 23:26:13,058 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/927c24eb_16_customs_doc_3.pdf
2025-09-18 23:26:13,084 - INFO - 

17_nmfc_cert_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:26:13,084 - INFO - 

✓ Saved result: output/run1_17_nmfc_cert_2.json
2025-09-18 23:26:13,386 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/85a9af5c_17_nmfc_cert_2.pdf
2025-09-18 23:26:13,405 - INFO - 

19_coa_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:26:13,405 - INFO - 

✓ Saved result: output/run1_19_coa_3.json
2025-09-18 23:26:13,700 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4ca1210a_19_coa_3.pdf
2025-09-18 23:26:13,793 - INFO - 

16_customs_doc_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 23:26:13,794 - INFO - 

✓ Saved result: output/run1_16_customs_doc_1.json
2025-09-18 23:26:14,099 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2e892777_16_customs_doc_1.pdf
2025-09-18 23:26:14,113 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 23:26:14,113 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 23:26:14,413 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0f85970a_21_lumper_receipt_1.jpeg
2025-09-18 23:26:14,433 - INFO - 

19_coa_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:26:14,433 - INFO - 

✓ Saved result: output/run1_19_coa_1.json
2025-09-18 23:26:14,737 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a319cd0f_19_coa_1.pdf
2025-09-18 23:26:14,760 - INFO - 

20_tender_from_cust_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 23:26:14,760 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_2.json
2025-09-18 23:26:15,056 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fa4f6401_20_tender_from_cust_2.pdf
2025-09-18 23:26:15,092 - INFO - 

1_bol_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:26:15,092 - INFO - 

✓ Saved result: output/run1_1_bol_1.json
2025-09-18 23:26:15,394 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/250e7c4a_1_bol_1.pdf
2025-09-18 23:26:15,409 - INFO - 

19_coa_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:26:15,409 - INFO - 

✓ Saved result: output/run1_19_coa_2.json
2025-09-18 23:26:15,698 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6eced563_19_coa_2.pdf
2025-09-18 23:26:15,720 - INFO - 

20_tender_from_cust_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 23:26:15,721 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_1.json
2025-09-18 23:26:16,021 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/938bbf10_20_tender_from_cust_1.pdf
2025-09-18 23:26:16,076 - INFO - 

16_customs_doc_2.tiff

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 23:26:16,076 - INFO - 

✓ Saved result: output/run1_16_customs_doc_2.json
2025-09-18 23:26:16,369 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2e531567_16_customs_doc_2.tiff
2025-09-18 23:26:16,376 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:26:16,376 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 23:26:16,675 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/106b3c72_21_lumper_receipt_10.png
2025-09-18 23:26:16,682 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 23:26:16,682 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 23:26:16,789 - INFO - Splitting PDF into individual pages...
2025-09-18 23:26:16,790 - INFO - Splitting PDF 86a475b4_9_fuel_receipt_11 into 1 pages
2025-09-18 23:26:16,795 - INFO - Split PDF into 1 pages
2025-09-18 23:26:16,795 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:26:16,795 - INFO - Expected pages: [1]
2025-09-18 23:26:16,981 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/68590842_21_lumper_receipt_11.jpeg
2025-09-18 23:26:16,997 - INFO - 

1_bol_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:26:16,997 - INFO - 

✓ Saved result: output/run1_1_bol_11.json
2025-09-18 23:26:17,296 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/afb33513_1_bol_11.pdf
2025-09-18 23:26:17,337 - INFO - 

1_bol_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:26:17,337 - INFO - 

✓ Saved result: output/run1_1_bol_10.json
2025-09-18 23:26:17,637 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c2030da7_1_bol_10.pdf
2025-09-18 23:26:17,648 - INFO - 

25_ingate_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-18 23:26:17,649 - INFO - 

✓ Saved result: output/run1_25_ingate_10.json
2025-09-18 23:26:17,945 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9f80e50e_25_ingate_10.png
2025-09-18 23:26:17,960 - INFO - 

22_so_confirmation_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-18 23:26:17,960 - INFO - 

✓ Saved result: output/run1_22_so_confirmation_1.json
2025-09-18 23:26:18,264 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ad9c1fcd_22_so_confirmation_1.pdf
2025-09-18 23:26:18,339 - INFO - 

20_tender_from_cust_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 3,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 23:26:18,339 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_3.json
2025-09-18 23:26:18,637 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1977e502_20_tender_from_cust_3.pdf
2025-09-18 23:26:18,680 - INFO - 

22_so_confirmation_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "po_confirmation"
        }
    ]
}
2025-09-18 23:26:18,680 - INFO - 

✓ Saved result: output/run1_22_so_confirmation_3.json
2025-09-18 23:26:18,980 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9218e851_22_so_confirmation_3.pdf
2025-09-18 23:26:18,995 - INFO - 

25_ingate_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-18 23:26:18,995 - INFO - 

✓ Saved result: output/run1_25_ingate_2.json
2025-09-18 23:26:19,297 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b54031a0_25_ingate_2.pdf
2025-09-18 23:26:19,310 - INFO - 

25_ingate_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        },
        {
            "page_no": 2,
            "doc_type": "ingate"
        }
    ]
}
2025-09-18 23:26:19,310 - INFO - 

✓ Saved result: output/run1_25_ingate_1.json
2025-09-18 23:26:19,610 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/66da01b7_25_ingate_1.pdf
2025-09-18 23:26:19,756 - INFO - 

22_so_confirmation_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "so_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "so_confirmation"
        },
        {
            "page_no": 4,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-18 23:26:19,756 - INFO - 

✓ Saved result: output/run1_22_so_confirmation_2.json
2025-09-18 23:26:20,050 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fcb80f8e_22_so_confirmation_2.pdf
2025-09-18 23:26:20,064 - INFO - 

26_outgate_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 23:26:20,064 - INFO - 

✓ Saved result: output/run1_26_outgate_10.json
2025-09-18 23:26:20,365 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7efbe813_26_outgate_10.pdf
2025-09-18 23:26:20,385 - INFO - 

2_pod_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 23:26:20,385 - INFO - 

✓ Saved result: output/run1_2_pod_10.json
2025-09-18 23:26:20,678 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/65cc422f_2_pod_10.pdf
2025-09-18 23:26:20,690 - INFO - 

26_outgate_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 23:26:20,690 - INFO - 

✓ Saved result: output/run1_26_outgate_11.json
2025-09-18 23:26:20,991 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6622e8d5_26_outgate_11.pdf
2025-09-18 23:26:21,010 - INFO - 

2_pod_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 23:26:21,010 - INFO - 

✓ Saved result: output/run1_2_pod_11.json
2025-09-18 23:26:21,312 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/24b3411f_2_pod_11.pdf
2025-09-18 23:26:21,320 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:26:21,320 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_10.json
2025-09-18 23:26:21,620 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/484081c3_6_scale_ticket_10.png
2025-09-18 23:26:21,643 - INFO - 

3_rate_confirmation_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 23:26:21,643 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_3.json
2025-09-18 23:26:21,948 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e634e69b_3_rate_confirmation_3.pdf
2025-09-18 23:26:21,987 - INFO - 

2_pod_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        },
        {
            "page_no": 3,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 23:26:21,987 - INFO - 

✓ Saved result: output/run1_2_pod_1.json
2025-09-18 23:26:22,947 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/aeba0c89_2_pod_1.pdf
2025-09-18 23:26:22,956 - INFO - 

5_clear_to_pay_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-18 23:26:22,956 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_2.json
2025-09-18 23:26:23,244 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3bab47c9_5_clear_to_pay_2.pdf
2025-09-18 23:26:23,256 - INFO - 

3_rate_confirmation_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 23:26:23,256 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_2.json
2025-09-18 23:26:23,597 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9b035f4f_3_rate_confirmation_2.pdf
2025-09-18 23:26:23,611 - INFO - 

6_scale_ticket_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:26:23,611 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_11.json
2025-09-18 23:26:23,908 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/78b15fe0_6_scale_ticket_11.jpg
2025-09-18 23:26:23,936 - INFO - 

3_rate_confirmation_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 23:26:23,936 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_1.json
2025-09-18 23:26:24,318 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/722807fd_3_rate_confirmation_1.pdf
2025-09-18 23:26:24,330 - INFO - 

5_clear_to_pay_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-18 23:26:24,330 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_1.json
2025-09-18 23:26:24,623 - INFO - Page 1: Extracted 402 characters, 39 lines from 86a475b4_9_fuel_receipt_11_5b328af4_page_001.pdf
2025-09-18 23:26:24,623 - INFO - Successfully processed page 1
2025-09-18 23:26:24,624 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/52eb333a_5_clear_to_pay_1.pdf
2025-09-18 23:26:24,624 - INFO - Combined 1 pages into final text
2025-09-18 23:26:24,624 - INFO - Text validation for 86a475b4_9_fuel_receipt_11: 419 characters, 1 pages
2025-09-18 23:26:24,629 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:26:24,629 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:26:24,645 - INFO - 

26_outgate_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 23:26:24,645 - INFO - 

✓ Saved result: output/run1_26_outgate_1.json
2025-09-18 23:26:25,026 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7004fbb1_26_outgate_1.pdf
2025-09-18 23:26:25,049 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:26:25,049 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_1.json
2025-09-18 23:26:25,336 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c2d1a484_6_scale_ticket_1.pdf
2025-09-18 23:26:25,349 - INFO - 

8_log_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:26:25,349 - INFO - 

✓ Saved result: output/run1_8_log_10.json
2025-09-18 23:26:25,644 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ebdccbae_8_log_10.jpeg
2025-09-18 23:26:25,676 - INFO - 

8_log_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-18 23:26:25,676 - INFO - 

✓ Saved result: output/run1_8_log_11.json
2025-09-18 23:26:26,049 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1b38bb37_8_log_11.jpg
2025-09-18 23:26:26,066 - INFO - 

9_fuel_receipt_1.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 23:26:26,066 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_1.json
2025-09-18 23:26:26,356 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f61247c0_9_fuel_receipt_1.png
2025-09-18 23:26:26,363 - INFO - 

9_fuel_receipt_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 23:26:26,363 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_10.json
2025-09-18 23:26:26,667 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/756463b8_9_fuel_receipt_10.jpeg
2025-09-18 23:26:26,696 - INFO - 

8_log_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:26:26,696 - INFO - 

✓ Saved result: output/run1_8_log_1.json
2025-09-18 23:26:27,073 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bd078963_8_log_1.pdf
2025-09-18 23:26:27,587 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/86a475b4_9_fuel_receipt_11.pdf
2025-09-18 23:26:27,601 - INFO - 

9_fuel_receipt_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 23:26:27,601 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_11.json
2025-09-18 23:26:27,897 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/86a475b4_9_fuel_receipt_11.pdf
2025-09-18 23:26:27,898 - INFO - 
📊 Processing Summary:
2025-09-18 23:26:27,899 - INFO -    Total files: 57
2025-09-18 23:26:27,899 - INFO -    Successful: 57
2025-09-18 23:26:27,899 - INFO -    Failed: 0
2025-09-18 23:26:27,899 - INFO -    Duration: 68.24 seconds
2025-09-18 23:26:27,899 - INFO -    Output directory: output
2025-09-18 23:26:27,899 - INFO - 
📋 Successfully Processed Files:
2025-09-18 23:26:27,899 - INFO -    📄 10_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:26:27,899 - INFO -    📄 10_invoice_10.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:26:27,899 - INFO -    📄 10_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:26:27,899 - INFO -    📄 12_combined_carrier_documents_1.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"combined_carrier_documents"}]}
2025-09-18 23:26:27,900 - INFO -    📄 12_combined_carrier_documents_2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:26:27,900 - INFO -    📄 12_combined_carrier_documents_3.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 23:26:27,900 - INFO -    📄 13_pack_list_1.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-18 23:26:27,900 - INFO -    📄 13_pack_list_10.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 23:26:27,900 - INFO -    📄 13_pack_list_2.PDF: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:26:27,900 - INFO -    📄 14_po_1.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 23:26:27,900 - INFO -    📄 14_po_2.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 23:26:27,900 - INFO -    📄 14_po_3.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-18 23:26:27,900 - INFO -    📄 15_comm_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-18 23:26:27,900 - INFO -    📄 15_comm_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-18 23:26:27,900 - INFO -    📄 16_customs_doc_1.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"customs_doc"}]}
2025-09-18 23:26:27,900 - INFO -    📄 16_customs_doc_2.tiff: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 23:26:27,901 - INFO -    📄 16_customs_doc_3.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 23:26:27,901 - INFO -    📄 17_nmfc_cert_1.jpg: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-18 23:26:27,901 - INFO -    📄 17_nmfc_cert_2.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"scale_ticket"}]}
2025-09-18 23:26:27,901 - INFO -    📄 19_coa_1.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 23:26:27,901 - INFO -    📄 19_coa_2.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 23:26:27,901 - INFO -    📄 19_coa_3.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:26:27,901 - INFO -    📄 1_bol_1.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:26:27,901 - INFO -    📄 1_bol_10.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:26:27,901 - INFO -    📄 1_bol_11.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:26:27,901 - INFO -    📄 20_tender_from_cust_1.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-18 23:26:27,901 - INFO -    📄 20_tender_from_cust_2.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-18 23:26:27,901 - INFO -    📄 20_tender_from_cust_3.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"},{"page_no":3,"doc_type":"tender_from_cust"}]}
2025-09-18 23:26:27,901 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 23:26:27,901 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:26:27,902 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 23:26:27,902 - INFO -    📄 22_so_confirmation_1.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-18 23:26:27,902 - INFO -    📄 22_so_confirmation_2.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"},{"page_no":2,"doc_type":"so_confirmation"},{"page_no":3,"doc_type":"so_confirmation"},{"page_no":4,"doc_type":"so_confirmation"}]}
2025-09-18 23:26:27,902 - INFO -    📄 22_so_confirmation_3.pdf: {"documents":[{"page_no":1,"doc_type":"po_confirmation"},{"page_no":2,"doc_type":"po_confirmation"}]}
2025-09-18 23:26:27,902 - INFO -    📄 25_ingate_1.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"ingate"}]}
2025-09-18 23:26:27,902 - INFO -    📄 25_ingate_10.png: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-18 23:26:27,902 - INFO -    📄 25_ingate_2.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-18 23:26:27,902 - INFO -    📄 26_outgate_1.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"outgate"}]}
2025-09-18 23:26:27,902 - INFO -    📄 26_outgate_10.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 23:26:27,902 - INFO -    📄 26_outgate_11.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 23:26:27,902 - INFO -    📄 2_pod_1.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"},{"page_no":3,"doc_type":"pack_list"}]}
2025-09-18 23:26:27,902 - INFO -    📄 2_pod_10.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 23:26:27,902 - INFO -    📄 2_pod_11.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 23:26:27,902 - INFO -    📄 3_rate_confirmation_1.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-18 23:26:27,902 - INFO -    📄 3_rate_confirmation_2.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-18 23:26:27,902 - INFO -    📄 3_rate_confirmation_3.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-18 23:26:27,902 - INFO -    📄 5_clear_to_pay_1.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-18 23:26:27,902 - INFO -    📄 5_clear_to_pay_2.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-18 23:26:27,902 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:26:27,902 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:26:27,902 - INFO -    📄 6_scale_ticket_11.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:26:27,902 - INFO -    📄 8_log_1.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"}]}
2025-09-18 23:26:27,903 - INFO -    📄 8_log_10.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 23:26:27,903 - INFO -    📄 8_log_11.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-18 23:26:27,903 - INFO -    📄 9_fuel_receipt_1.png: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 23:26:27,903 - INFO -    📄 9_fuel_receipt_10.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 23:26:27,903 - INFO -    📄 9_fuel_receipt_11.pdf: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 23:26:27,903 - INFO - 
============================================================================================================================================
2025-09-18 23:26:27,904 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 23:26:27,904 - INFO - ============================================================================================================================================
2025-09-18 23:26:27,904 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 23:26:27,904 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 23:26:27,904 - INFO - 10_invoice_1.pdf                                   1      invoice              run1_10_invoice_1.json                            
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_1.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 10_invoice_10.pdf                                  1      invoice              run1_10_invoice_10.json                           
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_10.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_10.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 10_invoice_2.pdf                                   1      invoice              run1_10_invoice_2.json                            
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_2.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_2.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 12_combined_carrier_documents_1.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_1.json         
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_1.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 12_combined_carrier_documents_1.pdf                2      combined_carrier_d... run1_12_combined_carrier_documents_1.json         
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_1.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 12_combined_carrier_documents_2.pdf                1      bol                  run1_12_combined_carrier_documents_2.json         
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_2.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 12_combined_carrier_documents_3.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_3.json         
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_3.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 13_pack_list_1.pdf                                 1      pack_list            run1_13_pack_list_1.json                          
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_1.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 13_pack_list_1.pdf                                 2      pack_list            run1_13_pack_list_1.json                          
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_1.json
2025-09-18 23:26:27,904 - INFO - 
2025-09-18 23:26:27,904 - INFO - 13_pack_list_10.pdf                                1      pack_list            run1_13_pack_list_10.json                         
2025-09-18 23:26:27,904 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_10.pdf
2025-09-18 23:26:27,904 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_10.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 13_pack_list_2.PDF                                 1      bol                  run1_13_pack_list_2.json                          
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_2.PDF
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_2.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 14_po_1.pdf                                        1      po                   run1_14_po_1.json                                 
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_1.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_1.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 14_po_2.pdf                                        1      po                   run1_14_po_2.json                                 
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_2.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_2.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 14_po_3.pdf                                        1      po                   run1_14_po_3.json                                 
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_3.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_3.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 14_po_3.pdf                                        2      po                   run1_14_po_3.json                                 
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_3.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_3.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 15_comm_invoice_1.pdf                              1      comm_invoice         run1_15_comm_invoice_1.json                       
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/15_comm_invoice/15_comm_invoice_1.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_15_comm_invoice_1.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 15_comm_invoice_2.pdf                              1      comm_invoice         run1_15_comm_invoice_2.json                       
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/15_comm_invoice/15_comm_invoice_2.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_15_comm_invoice_2.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 16_customs_doc_1.pdf                               1      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 16_customs_doc_1.pdf                               2      invoice              run1_16_customs_doc_1.json                        
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 16_customs_doc_1.pdf                               3      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 16_customs_doc_2.tiff                              1      customs_doc          run1_16_customs_doc_2.json                        
2025-09-18 23:26:27,905 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_2.tiff
2025-09-18 23:26:27,905 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_2.json
2025-09-18 23:26:27,905 - INFO - 
2025-09-18 23:26:27,905 - INFO - 16_customs_doc_3.pdf                               1      customs_doc          run1_16_customs_doc_3.json                        
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_3.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_3.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 17_nmfc_cert_1.jpg                                 1      nmfc_cert            run1_17_nmfc_cert_1.json                          
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_1.jpg
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_17_nmfc_cert_1.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 17_nmfc_cert_2.pdf                                 1      other                run1_17_nmfc_cert_2.json                          
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_17_nmfc_cert_2.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 17_nmfc_cert_2.pdf                                 2      scale_ticket         run1_17_nmfc_cert_2.json                          
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_17_nmfc_cert_2.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 19_coa_1.pdf                                       1      other                run1_19_coa_1.json                                
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/19_coa/19_coa_1.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_19_coa_1.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 19_coa_2.pdf                                       1      other                run1_19_coa_2.json                                
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/19_coa/19_coa_2.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_19_coa_2.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 19_coa_3.pdf                                       1      scale_ticket         run1_19_coa_3.json                                
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/19_coa/19_coa_3.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_19_coa_3.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 1_bol_1.pdf                                        1      bol                  run1_1_bol_1.json                                 
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_1.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_1_bol_1.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 1_bol_10.pdf                                       1      bol                  run1_1_bol_10.json                                
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_10.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_1_bol_10.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 1_bol_11.pdf                                       1      bol                  run1_1_bol_11.json                                
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_11.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_1_bol_11.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 20_tender_from_cust_1.pdf                          1      tender_from_cust     run1_20_tender_from_cust_1.json                   
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_1.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 20_tender_from_cust_1.pdf                          2      tender_from_cust     run1_20_tender_from_cust_1.json                   
2025-09-18 23:26:27,906 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf
2025-09-18 23:26:27,906 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_1.json
2025-09-18 23:26:27,906 - INFO - 
2025-09-18 23:26:27,906 - INFO - 20_tender_from_cust_2.pdf                          1      tender_from_cust     run1_20_tender_from_cust_2.json                   
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_2.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 20_tender_from_cust_2.pdf                          2      tender_from_cust     run1_20_tender_from_cust_2.json                   
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_2.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 20_tender_from_cust_3.pdf                          1      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 20_tender_from_cust_3.pdf                          2      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 20_tender_from_cust_3.pdf                          3      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 21_lumper_receipt_10.png                           1      invoice              run1_21_lumper_receipt_10.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 22_so_confirmation_1.pdf                           1      so_confirmation      run1_22_so_confirmation_1.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_1.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_1.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 22_so_confirmation_2.pdf                           1      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 22_so_confirmation_2.pdf                           2      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 22_so_confirmation_2.pdf                           3      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:26:27,907 - INFO - 
2025-09-18 23:26:27,907 - INFO - 22_so_confirmation_2.pdf                           4      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:26:27,907 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:26:27,907 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 22_so_confirmation_3.pdf                           1      po_confirmation      run1_22_so_confirmation_3.json                    
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_3.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 22_so_confirmation_3.pdf                           2      po_confirmation      run1_22_so_confirmation_3.json                    
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_3.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 25_ingate_1.pdf                                    1      ingate               run1_25_ingate_1.json                             
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_1.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 25_ingate_1.pdf                                    2      ingate               run1_25_ingate_1.json                             
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_1.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 25_ingate_10.png                                   1      ingate               run1_25_ingate_10.json                            
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_10.png
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_10.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 25_ingate_2.pdf                                    1      ingate               run1_25_ingate_2.json                             
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_2.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_2.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 26_outgate_1.pdf                                   1      outgate              run1_26_outgate_1.json                            
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_1.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 26_outgate_1.pdf                                   2      outgate              run1_26_outgate_1.json                            
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_1.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 26_outgate_10.pdf                                  1      outgate              run1_26_outgate_10.json                           
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_10.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_10.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 26_outgate_11.pdf                                  1      outgate              run1_26_outgate_11.json                           
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_11.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_11.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 2_pod_1.pdf                                        1      pack_list            run1_2_pod_1.json                                 
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_1.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 2_pod_1.pdf                                        2      pack_list            run1_2_pod_1.json                                 
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_1.json
2025-09-18 23:26:27,908 - INFO - 
2025-09-18 23:26:27,908 - INFO - 2_pod_1.pdf                                        3      pack_list            run1_2_pod_1.json                                 
2025-09-18 23:26:27,908 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 23:26:27,908 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_1.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 2_pod_10.pdf                                       1      pod                  run1_2_pod_10.json                                
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_10.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_10.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 2_pod_11.pdf                                       1      pod                  run1_2_pod_11.json                                
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_11.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_11.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 3_rate_confirmation_1.pdf                          1      rate_confirmation    run1_3_rate_confirmation_1.json                   
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_1.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_1.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 3_rate_confirmation_2.pdf                          1      rate_confirmation    run1_3_rate_confirmation_2.json                   
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_2.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_2.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 3_rate_confirmation_3.pdf                          1      rate_confirmation    run1_3_rate_confirmation_3.json                   
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_3.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 3_rate_confirmation_3.pdf                          2      rate_confirmation    run1_3_rate_confirmation_3.json                   
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_3.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 5_clear_to_pay_1.pdf                               1      clear_to_pay         run1_5_clear_to_pay_1.json                        
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_1.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_5_clear_to_pay_1.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 5_clear_to_pay_2.pdf                               1      clear_to_pay         run1_5_clear_to_pay_2.json                        
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_2.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_5_clear_to_pay_2.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 6_scale_ticket_1.pdf                               1      scale_ticket         run1_6_scale_ticket_1.json                        
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_6_scale_ticket_1.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 6_scale_ticket_10.png                              1      scale_ticket         run1_6_scale_ticket_10.json                       
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_6_scale_ticket_10.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 6_scale_ticket_11.jpg                              1      scale_ticket         run1_6_scale_ticket_11.json                       
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_6_scale_ticket_11.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,909 - INFO - 8_log_1.pdf                                        1      other                run1_8_log_1.json                                 
2025-09-18 23:26:27,909 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_1.pdf
2025-09-18 23:26:27,909 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 23:26:27,909 - INFO - 
2025-09-18 23:26:27,910 - INFO - 8_log_1.pdf                                        2      other                run1_8_log_1.json                                 
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_1.pdf
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - 8_log_1.pdf                                        3      other                run1_8_log_1.json                                 
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_1.pdf
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - 8_log_10.jpeg                                      1      other                run1_8_log_10.json                                
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_10.jpeg
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_10.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - 8_log_11.jpg                                       1      log                  run1_8_log_11.json                                
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_11.jpg
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_11.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - 9_fuel_receipt_1.png                               1      fuel_receipt         run1_9_fuel_receipt_1.json                        
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_1.png
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_1.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - 9_fuel_receipt_10.jpeg                             1      fuel_receipt         run1_9_fuel_receipt_10.json                       
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_10.jpeg
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_10.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - 9_fuel_receipt_11.pdf                              1      fuel_receipt         run1_9_fuel_receipt_11.json                       
2025-09-18 23:26:27,910 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_11.pdf
2025-09-18 23:26:27,910 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_11.json
2025-09-18 23:26:27,910 - INFO - 
2025-09-18 23:26:27,910 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 23:26:27,910 - INFO - Total entries: 78
2025-09-18 23:26:27,910 - INFO - ============================================================================================================================================
2025-09-18 23:26:27,910 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 23:26:27,910 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 23:26:27,910 - INFO -   1. 10_invoice_1.pdf                    Page 1   → invoice         | run1_10_invoice_1.json
2025-09-18 23:26:27,910 - INFO -   2. 10_invoice_10.pdf                   Page 1   → invoice         | run1_10_invoice_10.json
2025-09-18 23:26:27,910 - INFO -   3. 10_invoice_2.pdf                    Page 1   → invoice         | run1_10_invoice_2.json
2025-09-18 23:26:27,910 - INFO -   4. 12_combined_carrier_documents_1.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_1.json
2025-09-18 23:26:27,910 - INFO -   5. 12_combined_carrier_documents_1.pdf Page 2   → combined_carrier_documents | run1_12_combined_carrier_documents_1.json
2025-09-18 23:26:27,910 - INFO -   6. 12_combined_carrier_documents_2.pdf Page 1   → bol             | run1_12_combined_carrier_documents_2.json
2025-09-18 23:26:27,910 - INFO -   7. 12_combined_carrier_documents_3.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_3.json
2025-09-18 23:26:27,910 - INFO -   8. 13_pack_list_1.pdf                  Page 1   → pack_list       | run1_13_pack_list_1.json
2025-09-18 23:26:27,910 - INFO -   9. 13_pack_list_1.pdf                  Page 2   → pack_list       | run1_13_pack_list_1.json
2025-09-18 23:26:27,910 - INFO -  10. 13_pack_list_10.pdf                 Page 1   → pack_list       | run1_13_pack_list_10.json
2025-09-18 23:26:27,910 - INFO -  11. 13_pack_list_2.PDF                  Page 1   → bol             | run1_13_pack_list_2.json
2025-09-18 23:26:27,911 - INFO -  12. 14_po_1.pdf                         Page 1   → po              | run1_14_po_1.json
2025-09-18 23:26:27,911 - INFO -  13. 14_po_2.pdf                         Page 1   → po              | run1_14_po_2.json
2025-09-18 23:26:27,911 - INFO -  14. 14_po_3.pdf                         Page 1   → po              | run1_14_po_3.json
2025-09-18 23:26:27,911 - INFO -  15. 14_po_3.pdf                         Page 2   → po              | run1_14_po_3.json
2025-09-18 23:26:27,911 - INFO -  16. 15_comm_invoice_1.pdf               Page 1   → comm_invoice    | run1_15_comm_invoice_1.json
2025-09-18 23:26:27,911 - INFO -  17. 15_comm_invoice_2.pdf               Page 1   → comm_invoice    | run1_15_comm_invoice_2.json
2025-09-18 23:26:27,911 - INFO -  18. 16_customs_doc_1.pdf                Page 1   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 23:26:27,911 - INFO -  19. 16_customs_doc_1.pdf                Page 2   → invoice         | run1_16_customs_doc_1.json
2025-09-18 23:26:27,911 - INFO -  20. 16_customs_doc_1.pdf                Page 3   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 23:26:27,911 - INFO -  21. 16_customs_doc_2.tiff               Page 1   → customs_doc     | run1_16_customs_doc_2.json
2025-09-18 23:26:27,911 - INFO -  22. 16_customs_doc_3.pdf                Page 1   → customs_doc     | run1_16_customs_doc_3.json
2025-09-18 23:26:27,911 - INFO -  23. 17_nmfc_cert_1.jpg                  Page 1   → nmfc_cert       | run1_17_nmfc_cert_1.json
2025-09-18 23:26:27,911 - INFO -  24. 17_nmfc_cert_2.pdf                  Page 1   → other           | run1_17_nmfc_cert_2.json
2025-09-18 23:26:27,911 - INFO -  25. 17_nmfc_cert_2.pdf                  Page 2   → scale_ticket    | run1_17_nmfc_cert_2.json
2025-09-18 23:26:27,911 - INFO -  26. 19_coa_1.pdf                        Page 1   → other           | run1_19_coa_1.json
2025-09-18 23:26:27,911 - INFO -  27. 19_coa_2.pdf                        Page 1   → other           | run1_19_coa_2.json
2025-09-18 23:26:27,911 - INFO -  28. 19_coa_3.pdf                        Page 1   → scale_ticket    | run1_19_coa_3.json
2025-09-18 23:26:27,911 - INFO -  29. 1_bol_1.pdf                         Page 1   → bol             | run1_1_bol_1.json
2025-09-18 23:26:27,911 - INFO -  30. 1_bol_10.pdf                        Page 1   → bol             | run1_1_bol_10.json
2025-09-18 23:26:27,911 - INFO -  31. 1_bol_11.pdf                        Page 1   → bol             | run1_1_bol_11.json
2025-09-18 23:26:27,911 - INFO -  32. 20_tender_from_cust_1.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_1.json
2025-09-18 23:26:27,911 - INFO -  33. 20_tender_from_cust_1.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_1.json
2025-09-18 23:26:27,911 - INFO -  34. 20_tender_from_cust_2.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_2.json
2025-09-18 23:26:27,911 - INFO -  35. 20_tender_from_cust_2.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_2.json
2025-09-18 23:26:27,911 - INFO -  36. 20_tender_from_cust_3.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 23:26:27,911 - INFO -  37. 20_tender_from_cust_3.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 23:26:27,911 - INFO -  38. 20_tender_from_cust_3.pdf           Page 3   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 23:26:27,911 - INFO -  39. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 23:26:27,911 - INFO -  40. 21_lumper_receipt_10.png            Page 1   → invoice         | run1_21_lumper_receipt_10.json
2025-09-18 23:26:27,911 - INFO -  41. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 23:26:27,911 - INFO -  42. 22_so_confirmation_1.pdf            Page 1   → so_confirmation | run1_22_so_confirmation_1.json
2025-09-18 23:26:27,911 - INFO -  43. 22_so_confirmation_2.pdf            Page 1   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:26:27,911 - INFO -  44. 22_so_confirmation_2.pdf            Page 2   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:26:27,911 - INFO -  45. 22_so_confirmation_2.pdf            Page 3   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:26:27,911 - INFO -  46. 22_so_confirmation_2.pdf            Page 4   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:26:27,911 - INFO -  47. 22_so_confirmation_3.pdf            Page 1   → po_confirmation | run1_22_so_confirmation_3.json
2025-09-18 23:26:27,911 - INFO -  48. 22_so_confirmation_3.pdf            Page 2   → po_confirmation | run1_22_so_confirmation_3.json
2025-09-18 23:26:27,911 - INFO -  49. 25_ingate_1.pdf                     Page 1   → ingate          | run1_25_ingate_1.json
2025-09-18 23:26:27,911 - INFO -  50. 25_ingate_1.pdf                     Page 2   → ingate          | run1_25_ingate_1.json
2025-09-18 23:26:27,911 - INFO -  51. 25_ingate_10.png                    Page 1   → ingate          | run1_25_ingate_10.json
2025-09-18 23:26:27,911 - INFO -  52. 25_ingate_2.pdf                     Page 1   → ingate          | run1_25_ingate_2.json
2025-09-18 23:26:27,911 - INFO -  53. 26_outgate_1.pdf                    Page 1   → outgate         | run1_26_outgate_1.json
2025-09-18 23:26:27,911 - INFO -  54. 26_outgate_1.pdf                    Page 2   → outgate         | run1_26_outgate_1.json
2025-09-18 23:26:27,911 - INFO -  55. 26_outgate_10.pdf                   Page 1   → outgate         | run1_26_outgate_10.json
2025-09-18 23:26:27,912 - INFO -  56. 26_outgate_11.pdf                   Page 1   → outgate         | run1_26_outgate_11.json
2025-09-18 23:26:27,912 - INFO -  57. 2_pod_1.pdf                         Page 1   → pack_list       | run1_2_pod_1.json
2025-09-18 23:26:27,912 - INFO -  58. 2_pod_1.pdf                         Page 2   → pack_list       | run1_2_pod_1.json
2025-09-18 23:26:27,912 - INFO -  59. 2_pod_1.pdf                         Page 3   → pack_list       | run1_2_pod_1.json
2025-09-18 23:26:27,912 - INFO -  60. 2_pod_10.pdf                        Page 1   → pod             | run1_2_pod_10.json
2025-09-18 23:26:27,912 - INFO -  61. 2_pod_11.pdf                        Page 1   → pod             | run1_2_pod_11.json
2025-09-18 23:26:27,912 - INFO -  62. 3_rate_confirmation_1.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_1.json
2025-09-18 23:26:27,912 - INFO -  63. 3_rate_confirmation_2.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_2.json
2025-09-18 23:26:27,912 - INFO -  64. 3_rate_confirmation_3.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_3.json
2025-09-18 23:26:27,912 - INFO -  65. 3_rate_confirmation_3.pdf           Page 2   → rate_confirmation | run1_3_rate_confirmation_3.json
2025-09-18 23:26:27,912 - INFO -  66. 5_clear_to_pay_1.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_1.json
2025-09-18 23:26:27,912 - INFO -  67. 5_clear_to_pay_2.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_2.json
2025-09-18 23:26:27,912 - INFO -  68. 6_scale_ticket_1.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_1.json
2025-09-18 23:26:27,912 - INFO -  69. 6_scale_ticket_10.png               Page 1   → scale_ticket    | run1_6_scale_ticket_10.json
2025-09-18 23:26:27,912 - INFO -  70. 6_scale_ticket_11.jpg               Page 1   → scale_ticket    | run1_6_scale_ticket_11.json
2025-09-18 23:26:27,912 - INFO -  71. 8_log_1.pdf                         Page 1   → other           | run1_8_log_1.json
2025-09-18 23:26:27,929 - INFO -  72. 8_log_1.pdf                         Page 2   → other           | run1_8_log_1.json
2025-09-18 23:26:27,929 - INFO -  73. 8_log_1.pdf                         Page 3   → other           | run1_8_log_1.json
2025-09-18 23:26:27,929 - INFO -  74. 8_log_10.jpeg                       Page 1   → other           | run1_8_log_10.json
2025-09-18 23:26:27,930 - INFO -  75. 8_log_11.jpg                        Page 1   → log             | run1_8_log_11.json
2025-09-18 23:26:27,930 - INFO -  76. 9_fuel_receipt_1.png                Page 1   → fuel_receipt    | run1_9_fuel_receipt_1.json
2025-09-18 23:26:27,930 - INFO -  77. 9_fuel_receipt_10.jpeg              Page 1   → fuel_receipt    | run1_9_fuel_receipt_10.json
2025-09-18 23:26:27,930 - INFO -  78. 9_fuel_receipt_11.pdf               Page 1   → fuel_receipt    | run1_9_fuel_receipt_11.json
2025-09-18 23:26:27,930 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 23:26:27,931 - INFO - 
✅ Test completed: {'total_files': 57, 'processed': 57, 'failed': 0, 'errors': [], 'duration_seconds': 68.238415, 'processed_files': [{'filename': '10_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_1.pdf'}, {'filename': '10_invoice_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_10.pdf'}, {'filename': '10_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_2.pdf'}, {'filename': '12_combined_carrier_documents_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}, {'page_no': 2, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf'}, {'filename': '12_combined_carrier_documents_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf'}, {'filename': '12_combined_carrier_documents_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf'}, {'filename': '13_pack_list_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf'}, {'filename': '13_pack_list_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_3_per_cat/13_pack_list/13_pack_list_10.pdf'}, {'filename': '13_pack_list_2.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/13_pack_list/13_pack_list_2.PDF'}, {'filename': '14_po_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_3_per_cat/14_po/14_po_1.pdf'}, {'filename': '14_po_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_3_per_cat/14_po/14_po_2.pdf'}, {'filename': '14_po_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': 'input_data_3_per_cat/14_po/14_po_3.pdf'}, {'filename': '15_comm_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': 'input_data_3_per_cat/15_comm_invoice/15_comm_invoice_1.pdf'}, {'filename': '15_comm_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': 'input_data_3_per_cat/15_comm_invoice/15_comm_invoice_2.pdf'}, {'filename': '16_customs_doc_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'customs_doc'}]}, 'file_path': 'input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf'}, {'filename': '16_customs_doc_2.tiff', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': 'input_data_3_per_cat/16_customs_doc/16_customs_doc_2.tiff'}, {'filename': '16_customs_doc_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': 'input_data_3_per_cat/16_customs_doc/16_customs_doc_3.pdf'}, {'filename': '17_nmfc_cert_1.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': 'input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_1.jpg'}, {'filename': '17_nmfc_cert_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf'}, {'filename': '19_coa_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/19_coa/19_coa_1.pdf'}, {'filename': '19_coa_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/19_coa/19_coa_2.pdf'}, {'filename': '19_coa_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/19_coa/19_coa_3.pdf'}, {'filename': '1_bol_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/1_bol/1_bol_1.pdf'}, {'filename': '1_bol_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/1_bol/1_bol_10.pdf'}, {'filename': '1_bol_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/1_bol/1_bol_11.pdf'}, {'filename': '20_tender_from_cust_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf'}, {'filename': '20_tender_from_cust_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf'}, {'filename': '20_tender_from_cust_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}, {'page_no': 3, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf'}, {'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '22_so_confirmation_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': 'input_data_3_per_cat/22_so_confirmation/22_so_confirmation_1.pdf'}, {'filename': '22_so_confirmation_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}, {'page_no': 2, 'doc_type': 'so_confirmation'}, {'page_no': 3, 'doc_type': 'so_confirmation'}, {'page_no': 4, 'doc_type': 'so_confirmation'}]}, 'file_path': 'input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf'}, {'filename': '22_so_confirmation_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po_confirmation'}, {'page_no': 2, 'doc_type': 'po_confirmation'}]}, 'file_path': 'input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf'}, {'filename': '25_ingate_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}, {'page_no': 2, 'doc_type': 'ingate'}]}, 'file_path': 'input_data_3_per_cat/25_ingate/25_ingate_1.pdf'}, {'filename': '25_ingate_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': 'input_data_3_per_cat/25_ingate/25_ingate_10.png'}, {'filename': '25_ingate_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': 'input_data_3_per_cat/25_ingate/25_ingate_2.pdf'}, {'filename': '26_outgate_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_3_per_cat/26_outgate/26_outgate_1.pdf'}, {'filename': '26_outgate_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_3_per_cat/26_outgate/26_outgate_10.pdf'}, {'filename': '26_outgate_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_3_per_cat/26_outgate/26_outgate_11.pdf'}, {'filename': '2_pod_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}, {'page_no': 3, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_3_per_cat/2_pod/2_pod_1.pdf'}, {'filename': '2_pod_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_3_per_cat/2_pod/2_pod_10.pdf'}, {'filename': '2_pod_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_3_per_cat/2_pod/2_pod_11.pdf'}, {'filename': '3_rate_confirmation_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_1.pdf'}, {'filename': '3_rate_confirmation_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_2.pdf'}, {'filename': '3_rate_confirmation_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf'}, {'filename': '5_clear_to_pay_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': 'input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_1.pdf'}, {'filename': '5_clear_to_pay_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': 'input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_2.pdf'}, {'filename': '6_scale_ticket_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf'}, {'filename': '6_scale_ticket_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png'}, {'filename': '6_scale_ticket_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg'}, {'filename': '8_log_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/8_log/8_log_1.pdf'}, {'filename': '8_log_10.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/8_log/8_log_10.jpeg'}, {'filename': '8_log_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': 'input_data_3_per_cat/8_log/8_log_11.jpg'}, {'filename': '9_fuel_receipt_1.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_1.png'}, {'filename': '9_fuel_receipt_10.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_10.jpeg'}, {'filename': '9_fuel_receipt_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_11.pdf'}]}
