2025-09-24 17:47:20,071 - INFO - Logging initialized. Log file: logs/test_classification_20250924_174720.log
2025-09-24 17:47:20,071 - INFO - 📁 Found 16 files to process
2025-09-24 17:47:20,071 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:47:20,071 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-24 17:47:20,071 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-24 17:47:20,071 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-24 17:47:20,071 - INFO - ⬆️ [17:47:20] Uploading: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:22,261 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg -> s3://document-extraction-logistically/temp/d8fed948_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:22,261 - INFO - 🔍 [17:47:22] Starting classification: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:22,261 - INFO - ⬆️ [17:47:22] Uploading: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:22,266 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:22,283 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:22,293 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d8fed948_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:22,293 - INFO - Processing image from S3...
2025-09-24 17:47:23,596 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf -> s3://document-extraction-logistically/temp/eca22e37_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:23,597 - INFO - 🔍 [17:47:23] Starting classification: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:23,598 - INFO - ⬆️ [17:47:23] Uploading: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:23,599 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:23,631 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:23,637 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eca22e37_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:23,637 - INFO - Processing PDF from S3...
2025-09-24 17:47:23,638 - INFO - Downloading PDF from S3 to /tmp/tmp2fhxd8jg/eca22e37_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:24,858 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf -> s3://document-extraction-logistically/temp/2533db44_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:24,859 - INFO - 🔍 [17:47:24] Starting classification: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:24,860 - INFO - ⬆️ [17:47:24] Uploading: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:24,861 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:24,882 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:24,886 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2533db44_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:24,886 - INFO - Processing PDF from S3...
2025-09-24 17:47:24,886 - INFO - Downloading PDF from S3 to /tmp/tmp7o6tmv2s/2533db44_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:25,723 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:47:25,723 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:25,728 - INFO - Splitting PDF eca22e37_GKYUUU2YTZ1YTPGXHO51 into 3 pages
2025-09-24 17:47:25,751 - INFO - Split PDF into 3 pages
2025-09-24 17:47:25,752 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:25,752 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:47:25,978 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf -> s3://document-extraction-logistically/temp/f36a85e9_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:25,979 - INFO - 🔍 [17:47:25] Starting classification: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:25,979 - INFO - ⬆️ [17:47:25] Uploading: S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:25,981 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:25,997 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:26,000 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f36a85e9_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:26,001 - INFO - Processing PDF from S3...
2025-09-24 17:47:26,001 - INFO - Downloading PDF from S3 to /tmp/tmpadrb798g/f36a85e9_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:26,248 - INFO - S3 Image temp/d8fed948_AOYIL346IKT06LKO6D2R.jpg: Extracted 882 characters, 69 lines
2025-09-24 17:47:26,248 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:26,248 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:26,616 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf -> s3://document-extraction-logistically/temp/1220196c_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:26,616 - INFO - 🔍 [17:47:26] Starting classification: S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:26,617 - INFO - ⬆️ [17:47:26] Uploading: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:26,617 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:26,637 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:26,643 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1220196c_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:26,643 - INFO - Processing PDF from S3...
2025-09-24 17:47:26,644 - INFO - Downloading PDF from S3 to /tmp/tmp6qahhfwd/1220196c_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:26,844 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:47:26,845 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:26,846 - INFO - Splitting PDF 2533db44_I6IM526GOVKAWH0B5WKE into 1 pages
2025-09-24 17:47:26,848 - INFO - Split PDF into 1 pages
2025-09-24 17:47:26,848 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:26,848 - INFO - Expected pages: [1]
2025-09-24 17:47:27,259 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf -> s3://document-extraction-logistically/temp/3f4d5c8a_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:27,260 - INFO - 🔍 [17:47:27] Starting classification: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:27,260 - INFO - ⬆️ [17:47:27] Uploading: bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:27,261 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:27,269 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:27,272 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3f4d5c8a_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:27,272 - INFO - Processing PDF from S3...
2025-09-24 17:47:27,272 - INFO - Downloading PDF from S3 to /tmp/tmp3crbrkx9/3f4d5c8a_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:27,974 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:47:27,974 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:27,975 - WARNING - incorrect startxref pointer(1)
2025-09-24 17:47:27,978 - INFO - Splitting PDF f36a85e9_KK3PKFLS4ROTEZ7LOMMD into 1 pages
2025-09-24 17:47:27,988 - INFO - Split PDF into 1 pages
2025-09-24 17:47:27,988 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:27,988 - INFO - Expected pages: [1]
2025-09-24 17:47:28,238 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 17:47:28,238 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:28,239 - INFO - Splitting PDF 1220196c_S92OW12RYFF5G3READPW into 1 pages
2025-09-24 17:47:28,240 - INFO - Split PDF into 1 pages
2025-09-24 17:47:28,240 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:28,240 - INFO - Expected pages: [1]
2025-09-24 17:47:28,517 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 17:47:28,517 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:28,519 - INFO - Splitting PDF 3f4d5c8a_TQSZJLFFGGKB4NFN12RD into 1 pages
2025-09-24 17:47:28,521 - INFO - Split PDF into 1 pages
2025-09-24 17:47:28,521 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:28,521 - INFO - Expected pages: [1]
2025-09-24 17:47:29,277 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8e34ed7c-4213-473d-ae6a-d81bb86963e0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:29 GMT', 'content-type': 'application/json', 'content-length': '1003', 'connection': 'keep-alive', 'x-amzn-requestid': '8e34ed7c-4213-473d-ae6a-d81bb86963e0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page of a delivery receipt. We need to classify the doc type. The page contains "Delivery Receipt" header. Also contains "Bill of Lading Number", "Purchase Order Number", "Shipper", "Consignee". Likely a proof of delivery? The header says Delivery Receipt; but we need to map to categories. The categories include pod (Proof of Delivery), ingate, outgate, etc. Delivery Receipt could be POD. It has "Receiver Name", "Signature", "Time in", "Time Out". That matches POD. So doc_type = pod.\n\nWe need to output using the tool classify_logistics_doc_type with documents array containing object: page_no:1, doc_type:"pod".\n\nWe need to output exactly that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2815, 'outputTokens': 172, 'totalTokens': 2987}, 'metrics': {'latencyMs': 985}}
2025-09-24 17:47:29,278 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d8fed948_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:29,650 - INFO - Page 1: Extracted 643 characters, 34 lines from eca22e37_GKYUUU2YTZ1YTPGXHO51_ad6ea9bc_page_001.pdf
2025-09-24 17:47:29,650 - INFO - Successfully processed page 1
2025-09-24 17:47:30,606 - INFO - Page 3: Extracted 1566 characters, 104 lines from eca22e37_GKYUUU2YTZ1YTPGXHO51_ad6ea9bc_page_003.pdf
2025-09-24 17:47:30,607 - INFO - Successfully processed page 3
2025-09-24 17:47:31,091 - INFO - Page 2: Extracted 1426 characters, 86 lines from eca22e37_GKYUUU2YTZ1YTPGXHO51_ad6ea9bc_page_002.pdf
2025-09-24 17:47:31,092 - INFO - Successfully processed page 2
2025-09-24 17:47:31,092 - INFO - Combined 3 pages into final text
2025-09-24 17:47:31,092 - INFO - Text validation for eca22e37_GKYUUU2YTZ1YTPGXHO51: 3690 characters, 3 pages
2025-09-24 17:47:31,093 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:31,093 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:31,230 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf -> s3://document-extraction-logistically/temp/41e50c08_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:31,230 - INFO - 🔍 [17:47:31] Starting classification: bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:31,231 - INFO - ⬆️ [17:47:31] Uploading: bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:31,231 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:31,248 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:31,252 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/41e50c08_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:31,252 - INFO - Processing PDF from S3...
2025-09-24 17:47:31,253 - INFO - Downloading PDF from S3 to /tmp/tmp7wyw3y4l/41e50c08_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:32,553 - INFO - Page 1: Extracted 1921 characters, 143 lines from 2533db44_I6IM526GOVKAWH0B5WKE_bf9ca831_page_001.pdf
2025-09-24 17:47:32,553 - INFO - Successfully processed page 1
2025-09-24 17:47:32,553 - INFO - Combined 1 pages into final text
2025-09-24 17:47:32,553 - INFO - Text validation for 2533db44_I6IM526GOVKAWH0B5WKE: 1938 characters, 1 pages
2025-09-24 17:47:32,553 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:32,553 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:32,772 - INFO - Page 1: Extracted 1312 characters, 65 lines from 3f4d5c8a_TQSZJLFFGGKB4NFN12RD_7ae2f088_page_001.pdf
2025-09-24 17:47:32,772 - INFO - Successfully processed page 1
2025-09-24 17:47:32,773 - INFO - Combined 1 pages into final text
2025-09-24 17:47:32,773 - INFO - Text validation for 3f4d5c8a_TQSZJLFFGGKB4NFN12RD: 1329 characters, 1 pages
2025-09-24 17:47:32,773 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:32,773 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:33,060 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf -> s3://document-extraction-logistically/temp/20c41725_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:33,061 - INFO - 🔍 [17:47:33] Starting classification: bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:33,061 - INFO - ⬆️ [17:47:33] Uploading: bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:33,064 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:33,079 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:33,083 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/20c41725_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:33,083 - INFO - Processing PDF from S3...
2025-09-24 17:47:33,083 - INFO - Downloading PDF from S3 to /tmp/tmpm1wcv67d/20c41725_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:33,144 - INFO - Page 1: Extracted 1453 characters, 86 lines from f36a85e9_KK3PKFLS4ROTEZ7LOMMD_1e771160_page_001.pdf
2025-09-24 17:47:33,144 - INFO - Successfully processed page 1
2025-09-24 17:47:33,144 - INFO - Combined 1 pages into final text
2025-09-24 17:47:33,144 - INFO - Text validation for f36a85e9_KK3PKFLS4ROTEZ7LOMMD: 1470 characters, 1 pages
2025-09-24 17:47:33,145 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:33,145 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:33,406 - INFO - Page 1: Extracted 1576 characters, 111 lines from 1220196c_S92OW12RYFF5G3READPW_68195b65_page_001.pdf
2025-09-24 17:47:33,407 - INFO - Successfully processed page 1
2025-09-24 17:47:33,407 - INFO - Combined 1 pages into final text
2025-09-24 17:47:33,407 - INFO - Text validation for 1220196c_S92OW12RYFF5G3READPW: 1593 characters, 1 pages
2025-09-24 17:47:33,408 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:33,408 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:33,706 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf -> s3://document-extraction-logistically/temp/925ad8cf_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:33,707 - INFO - 🔍 [17:47:33] Starting classification: bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:33,707 - INFO - ⬆️ [17:47:33] Uploading: bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:33,709 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:33,725 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:33,733 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/925ad8cf_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:33,733 - INFO - Processing PDF from S3...
2025-09-24 17:47:33,733 - INFO - Downloading PDF from S3 to /tmp/tmplvtaip5o/925ad8cf_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:34,621 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf -> s3://document-extraction-logistically/temp/01a86c59_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:34,621 - INFO - 🔍 [17:47:34] Starting classification: bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:34,622 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:34,624 - INFO - ⬆️ [17:47:34] Uploading: bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:34,630 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b1ca496f-c869-4782-b193-2749b6b5729f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:34 GMT', 'content-type': 'application/json', 'content-length': '2295', 'connection': 'keep-alive', 'x-amzn-requestid': 'b1ca496f-c869-4782-b193-2749b6b5729f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Let\'s parse content.\n\nPage1: Title: FedEx proof-of-delivery. Contains "proof-of-delivery" heading. Also includes signature area, delivered status. So doc type: pod (Proof of Delivery). Also includes tracking number etc. So page1: pod.\n\nPage2: Looks like delivery receipt (Freight Bill). It contains Bill of Lading, shipping info, charges, etc. Could be invoice? But it\'s freight bill. The header says "DELIVERY RECEIPT Freight Freight Bill 6435169926 R0". This looks like an invoice-like. Might be called "invoice" (carrier invoice). But also there is "Freight Bill" which is basically invoice. Also includes charges. So doc type: invoice. However we need to differentiate between invoice vs comm_invoice, lumper_receipt. No HS code, no customs. No lumper. So invoice.\n\nBut also there\'s a "Freight Bill" which might be considered "invoice". Yes.\n\nPage3: Another delivery receipt page, but page 1 of 2. Also similar to page2. It\'s same type. So invoice.\n\nBut we need to check if page2 is continuation of page1? Page2 starts with "Page 2 of 2" and is a separate document? It might be part of a different document. Page2 appears to be a page of a freight bill, page 2 of 2. Page3 is page 1 of 2 again. So page3 is a separate document. So each page should be classified individually.\n\nThus:\n\nPage1: pod\nPage2: invoice\nPage3: invoice\n\nBut check if page2/3 might be "combined_carrier_documents"? They contain multiple headers? Page2 has "DELIVERY RECEIPT Freight Freight Bill" and maybe includes BOL details. But combined_carrier_documents is if multiple distinct document headers on one page. Page2 has only one header. So not combined.\n\nThus final output: array of objects with page_no and doc_type.\n\nWe need to call function classify_logistics_doc_type with list of documents. Provide array of objects. Ensure output format correct.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3808, 'outputTokens': 481, 'totalTokens': 4289}, 'metrics': {'latencyMs': 2279}}
2025-09-24 17:47:34,633 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:34,635 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eca22e37_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:34,644 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/01a86c59_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:34,645 - INFO - Processing PDF from S3...
2025-09-24 17:47:34,648 - INFO - Downloading PDF from S3 to /tmp/tmpifsg0kjr/01a86c59_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:34,797 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b08afece-5f2a-4baa-b8ea-9f525b930960', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:34 GMT', 'content-type': 'application/json', 'content-length': '824', 'connection': 'keep-alive', 'x-amzn-requestid': 'b08afece-5f2a-4baa-b8ea-9f525b930960'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page PDF. Need to classify. The content shows "DELIVERY RECEIPT" heading, also "RECEIVED BY", "Delivery Date", "Driver Name", "Manifest Number", etc. This seems like a Proof of Delivery (POD) type document. The heading "DELIVERY RECEIPT" could be a POD. It includes signature? Not shown. But typical POD includes "Received By" and "Delivered". So doc_type should be pod. There\'s no separate invoice or other. So output: one object for page 1 with doc_type "pod". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2946, 'outputTokens': 141, 'totalTokens': 3087}, 'metrics': {'latencyMs': 823}}
2025-09-24 17:47:34,797 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3f4d5c8a_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:34,801 - INFO - Downloaded PDF size: 1.8 MB
2025-09-24 17:47:34,801 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:34,802 - INFO - Splitting PDF 41e50c08_bol_DV1B199A0TJGUQ3FETIU into 3 pages
2025-09-24 17:47:34,830 - INFO - Split PDF into 3 pages
2025-09-24 17:47:34,831 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:34,831 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:47:35,124 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c336c413-9132-402e-9c6d-7775c3c0c3a2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:35 GMT', 'content-type': 'application/json', 'content-length': '830', 'connection': 'keep-alive', 'x-amzn-requestid': 'c336c413-9132-402e-9c6d-7775c3c0c3a2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Must output via function. Determine doc type. The content looks like a Delivery Receipt or Proof of Delivery. It says "Delivery Receipt" heading. Also includes signature, driver notes, etc. This matches POD or delivery receipt. In the definitions, POD: "Proof of Delivery" includes signatures, dates, etc. Delivery Receipt is similar. The heading "Delivery Receipt" suggests a POD. Use doc_type "pod". Output array with one entry: page_no 1, doc_type "pod".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3016, 'outputTokens': 129, 'totalTokens': 3145}, 'metrics': {'latencyMs': 818}}
2025-09-24 17:47:35,124 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f36a85e9_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:35,246 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '74aa5dcf-04f6-4661-883e-f100acc91467', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:35 GMT', 'content-type': 'application/json', 'content-length': '1468', 'connection': 'keep-alive', 'x-amzn-requestid': '74aa5dcf-04f6-4661-883e-f100acc91467'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only one page present: page1. We need to analyze its content. It looks like a delivery receipt or a shipment receipt. It contains "Receipt" header. It has fields like Origin, Destination, Shipper BL or GBL Number, Consignee, etc. It has P.O. #, etc. It also has "Receiver\'s Signature" and "ID". It seems like a delivery receipt, possibly a POD (Proof of Delivery) document. Because it includes delivered items, quantity, weight, conditions, signatures, and so on. It is likely a POD. The header says "RECEIPT". But "Proof of Delivery" is often called "POD" or "Delivery Receipt". So classify as pod. Let\'s confirm: POD typical keywords: "Proof of Delivery", "Delivery Ticket", "Delivered", "Received by", "Signature". Yes we have "Receiver\'s Signature" "ID". Also "Delivered" maybe not explicitly but "RECEIPT". It has "Delivery" in header. So pod. So output for page 1: doc_type: pod.\n\nWe must use the tool `classify_logistics_doc_type` with input: documents: array of objects with page_no and doc_type. For one page, page_no = 1, doc_type = "pod".\n\nReturn JSON accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3222, 'outputTokens': 296, 'totalTokens': 3518}, 'metrics': {'latencyMs': 1509}}
2025-09-24 17:47:35,247 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2533db44_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:35,760 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:47:35,761 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:35,762 - INFO - Splitting PDF 925ad8cf_bol_IJW9IJUOROYY1SWO0B17 into 1 pages
2025-09-24 17:47:35,765 - INFO - Split PDF into 1 pages
2025-09-24 17:47:35,765 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:35,765 - INFO - Expected pages: [1]
2025-09-24 17:47:35,774 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 17:47:35,776 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:35,780 - INFO - Splitting PDF 20c41725_bol_HEMTH4BHCWXWE044I0SK into 2 pages
2025-09-24 17:47:35,787 - INFO - Split PDF into 2 pages
2025-09-24 17:47:35,788 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:35,788 - INFO - Expected pages: [1, 2]
2025-09-24 17:47:35,956 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf -> s3://document-extraction-logistically/temp/70d89084_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:35,957 - INFO - 🔍 [17:47:35] Starting classification: bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:35,958 - INFO - ⬆️ [17:47:35] Uploading: bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:35,960 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:35,981 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:35,985 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/70d89084_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:35,985 - INFO - Processing PDF from S3...
2025-09-24 17:47:35,985 - INFO - Downloading PDF from S3 to /tmp/tmpdbnzj4k0/70d89084_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:37,020 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:47:37,021 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:37,023 - INFO - Splitting PDF 01a86c59_bol_K88AFHJU4HIQUZYTMOF4 into 1 pages
2025-09-24 17:47:37,028 - INFO - Split PDF into 1 pages
2025-09-24 17:47:37,028 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:37,028 - INFO - Expected pages: [1]
2025-09-24 17:47:37,240 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'da6c9afc-3e8e-4b40-a69a-14c2fc6f8729', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:37 GMT', 'content-type': 'application/json', 'content-length': '2027', 'connection': 'keep-alive', 'x-amzn-requestid': 'da6c9afc-3e8e-4b40-a69a-14c2fc6f8729'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. We have many terms. This looks like a freight bill - a Delivery receipt, with freight bill number, origin, destination, etc. It includes weight and inspection review. It seems like a "Delivery Receipt" but from SEFL. In the list, we have doc_type options: invoice, comm_invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, other, tender_from_cust, so_confirmation, ingate, outgate.\n\nThis page includes weight and inspection review: "A Weight & Inspection review has resulted in the following change". That\'s a weight and inspection certificate? But it\'s a freight bill receipt, not a certificate. However, the keywords for weight_and_inspection_cert: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. This page seems to be a freight bill with a weight & inspection review notice. Not sure if that qualifies as weight_and_inspection_cert. It is more like a freight bill. The doc type "invoice" with carrier invoice. It includes "C/LINE AMOUNT" and "TOTAL COLLECT" etc. So it\'s an invoice. There\'s no HS code, so not comm_invoice. It\'s not a lumper receipt. So likely "invoice".\n\nLet\'s check: The page includes "SEFL FREIGHT BILL NUMBER", "Receipt", "SEFL AMOUNT", "C/LINE AMOUNT", "TOTAL COLLECT", "Payment of freight charges due". That matches "invoice" definition. So doc_type: invoice.\n\nWe need to output JSON via tool. Format: {documents: [{page_no:1, doc_type:"invoice"}]}.\n\nUse tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3108, 'outputTokens': 401, 'totalTokens': 3509}, 'metrics': {'latencyMs': 1855}}
2025-09-24 17:47:37,240 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1220196c_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:38,665 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 17:47:38,666 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:38,667 - INFO - Splitting PDF 70d89084_bol_P2EZG64EJI98NT69TSAL into 1 pages
2025-09-24 17:47:38,671 - INFO - Split PDF into 1 pages
2025-09-24 17:47:38,671 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:38,671 - INFO - Expected pages: [1]
2025-09-24 17:47:38,818 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf -> s3://document-extraction-logistically/temp/cccb6d1e_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:38,818 - INFO - 🔍 [17:47:38] Starting classification: bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:38,819 - INFO - ⬆️ [17:47:38] Uploading: bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:38,819 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:38,839 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:38,842 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cccb6d1e_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:38,842 - INFO - Processing PDF from S3...
2025-09-24 17:47:38,843 - INFO - Downloading PDF from S3 to /tmp/tmpwmke867i/cccb6d1e_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:40,089 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf -> s3://document-extraction-logistically/temp/75f6b010_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:40,089 - INFO - 🔍 [17:47:40] Starting classification: bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:40,090 - INFO - ⬆️ [17:47:40] Uploading: pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:40,091 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:40,169 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:40,173 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/75f6b010_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:40,173 - INFO - Processing PDF from S3...
2025-09-24 17:47:40,174 - INFO - Downloading PDF from S3 to /tmp/tmpjznm7dv0/75f6b010_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:40,796 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg -> s3://document-extraction-logistically/temp/ee5f04fe_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:40,797 - INFO - 🔍 [17:47:40] Starting classification: pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:40,797 - INFO - ⬆️ [17:47:40] Uploading: pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:40,804 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:40,820 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:40,824 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ee5f04fe_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:40,825 - INFO - Processing image from S3...
2025-09-24 17:47:41,446 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg -> s3://document-extraction-logistically/temp/4dd51aaf_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:41,447 - INFO - 🔍 [17:47:41] Starting classification: pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:41,448 - INFO - ⬆️ [17:47:41] Uploading: pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:41,450 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:41,471 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:41,479 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4dd51aaf_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:41,479 - INFO - Processing image from S3...
2025-09-24 17:47:41,502 - INFO - Page 2: Extracted 2112 characters, 162 lines from 41e50c08_bol_DV1B199A0TJGUQ3FETIU_00e0a5a0_page_002.pdf
2025-09-24 17:47:41,504 - INFO - Successfully processed page 2
2025-09-24 17:47:41,655 - INFO - Page 1: Extracted 2979 characters, 110 lines from 41e50c08_bol_DV1B199A0TJGUQ3FETIU_00e0a5a0_page_001.pdf
2025-09-24 17:47:41,655 - INFO - Successfully processed page 1
2025-09-24 17:47:41,790 - INFO - Page 1: Extracted 3736 characters, 122 lines from 925ad8cf_bol_IJW9IJUOROYY1SWO0B17_9300f8f9_page_001.pdf
2025-09-24 17:47:41,790 - INFO - Successfully processed page 1
2025-09-24 17:47:41,796 - INFO - Combined 1 pages into final text
2025-09-24 17:47:41,796 - INFO - Text validation for 925ad8cf_bol_IJW9IJUOROYY1SWO0B17: 3753 characters, 1 pages
2025-09-24 17:47:41,802 - INFO - Page 3: Extracted 990 characters, 86 lines from 41e50c08_bol_DV1B199A0TJGUQ3FETIU_00e0a5a0_page_003.pdf
2025-09-24 17:47:41,802 - INFO - Successfully processed page 3
2025-09-24 17:47:41,803 - INFO - Combined 3 pages into final text
2025-09-24 17:47:41,803 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:41,803 - INFO - Text validation for 41e50c08_bol_DV1B199A0TJGUQ3FETIU: 6135 characters, 3 pages
2025-09-24 17:47:41,803 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:41,806 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:41,806 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:42,097 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_YC2ROS9NUB31VO0ABEV5.jpeg -> s3://document-extraction-logistically/temp/7be80c6d_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:42,098 - INFO - 🔍 [17:47:42] Starting classification: pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:42,099 - INFO - Initializing TextractProcessor...
2025-09-24 17:47:42,119 - INFO - Initializing BedrockProcessor...
2025-09-24 17:47:42,130 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7be80c6d_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:42,132 - INFO - Processing image from S3...
2025-09-24 17:47:42,147 - INFO - 

AOYIL346IKT06LKO6D2R.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:47:42,148 - INFO - 

✓ Saved result: output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:47:42,188 - INFO - Page 1: Extracted 3181 characters, 88 lines from 20c41725_bol_HEMTH4BHCWXWE044I0SK_ab2a1f99_page_001.pdf
2025-09-24 17:47:42,188 - INFO - Successfully processed page 1
2025-09-24 17:47:42,291 - INFO - Downloaded PDF size: 3.3 MB
2025-09-24 17:47:42,291 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:42,292 - INFO - Splitting PDF cccb6d1e_bol_PK5AIZFCER7DUFL15N01 into 1 pages
2025-09-24 17:47:42,298 - INFO - Split PDF into 1 pages
2025-09-24 17:47:42,299 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:42,299 - INFO - Expected pages: [1]
2025-09-24 17:47:42,448 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d8fed948_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:42,494 - INFO - 

GKYUUU2YTZ1YTPGXHO51.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:47:42,494 - INFO - 

✓ Saved result: output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:42,644 - INFO - Page 2: Extracted 3230 characters, 90 lines from 20c41725_bol_HEMTH4BHCWXWE044I0SK_ab2a1f99_page_002.pdf
2025-09-24 17:47:42,644 - INFO - Successfully processed page 2
2025-09-24 17:47:42,645 - INFO - Combined 2 pages into final text
2025-09-24 17:47:42,645 - INFO - Text validation for 20c41725_bol_HEMTH4BHCWXWE044I0SK: 6447 characters, 2 pages
2025-09-24 17:47:42,646 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:42,646 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:42,817 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eca22e37_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:42,840 - INFO - 

TQSZJLFFGGKB4NFN12RD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:47:42,840 - INFO - 

✓ Saved result: output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:47:42,941 - INFO - Downloaded PDF size: 1.3 MB
2025-09-24 17:47:42,942 - INFO - Splitting PDF into individual pages...
2025-09-24 17:47:42,943 - INFO - Splitting PDF 75f6b010_bol_RCNPMJFRSZTXQWBGLB9R into 1 pages
2025-09-24 17:47:42,948 - INFO - Split PDF into 1 pages
2025-09-24 17:47:42,949 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:47:42,949 - INFO - Expected pages: [1]
2025-09-24 17:47:43,043 - INFO - Page 1: Extracted 2991 characters, 109 lines from 01a86c59_bol_K88AFHJU4HIQUZYTMOF4_b08025a0_page_001.pdf
2025-09-24 17:47:43,045 - INFO - Successfully processed page 1
2025-09-24 17:47:43,045 - INFO - Combined 1 pages into final text
2025-09-24 17:47:43,046 - INFO - Text validation for 01a86c59_bol_K88AFHJU4HIQUZYTMOF4: 3008 characters, 1 pages
2025-09-24 17:47:43,046 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:43,047 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:43,131 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3f4d5c8a_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:43,146 - INFO - 

KK3PKFLS4ROTEZ7LOMMD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:47:43,146 - INFO - 

✓ Saved result: output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:47:43,441 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f36a85e9_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:43,460 - INFO - 

I6IM526GOVKAWH0B5WKE.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:47:43,460 - INFO - 

✓ Saved result: output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:47:43,780 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2533db44_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:43,807 - INFO - 

S92OW12RYFF5G3READPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:47:43,808 - INFO - 

✓ Saved result: output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:47:43,829 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a6584762-0faf-4af4-9ffe-47bb8925d6a4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:43 GMT', 'content-type': 'application/json', 'content-length': '817', 'connection': 'keep-alive', 'x-amzn-requestid': 'a6584762-0faf-4af4-9ffe-47bb8925d6a4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page provided. Need to classify it. It\'s likely a Waybill document. But in the enum list, there isn\'t a "Waybill" type. But we have "bol" for Bill of Lading. Waybill is a type of BOL. It includes shipping details, pickup, delivery, etc. So likely "bol". The page shows "WAYBILL" header, "Waybill # : O-52467", etc. So yes, it\'s a bill of lading. So classify as bol. It\'s a single page, no continuation. So output single object with page_no 1 and doc_type "bol". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3532, 'outputTokens': 152, 'totalTokens': 3684}, 'metrics': {'latencyMs': 854}}
2025-09-24 17:47:43,829 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/925ad8cf_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:43,895 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ce228f02-a491-4d6d-8bcb-c5cc82a4d2e4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:43 GMT', 'content-type': 'application/json', 'content-length': '789', 'connection': 'keep-alive', 'x-amzn-requestid': 'ce228f02-a491-4d6d-8bcb-c5cc82a4d2e4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. It\'s a multi-page document. The pages 1-3 appear to be BOLs or freight bill. There\'s "BILL OF LADING", page 1 of 1, then page 2 and 3 show details. It\'s a Bill of Lading (BOL). So doc_type "bol". All pages same type. No continuation signals. So output 3 objects with page_no 1,2,3, doc_type "bol".\n\nWe must call the function classify_logistics_doc_type with array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4626, 'outputTokens': 149, 'totalTokens': 4775}, 'metrics': {'latencyMs': 879}}
2025-09-24 17:47:43,895 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/41e50c08_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:44,053 - INFO - S3 Image temp/ee5f04fe_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg: Extracted 515 characters, 39 lines
2025-09-24 17:47:44,053 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:44,053 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:44,094 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1220196c_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:44,131 - INFO - 

bol_IJW9IJUOROYY1SWO0B17.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:44,132 - INFO - 

✓ Saved result: output/run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:47:44,386 - INFO - S3 Image temp/4dd51aaf_pack_list_P6RAGXD27RW4QUXN96TH.jpeg: Extracted 456 characters, 31 lines
2025-09-24 17:47:44,386 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:44,386 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:44,421 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/925ad8cf_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:44,481 - INFO - 

bol_DV1B199A0TJGUQ3FETIU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        },
        {
            "page_no": 3,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:44,481 - INFO - 

✓ Saved result: output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:44,757 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '36c489e5-db60-40da-a24a-34c57b7b3cba', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:44 GMT', 'content-type': 'application/json', 'content-length': '564', 'connection': 'keep-alive', 'x-amzn-requestid': '36c489e5-db60-40da-a24a-34c57b7b3cba'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. It\'s a Bill of Lading. So doc_type: bol. No continuation. Just one page. Output array with one object: page_no 1, doc_type "bol". Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_UhUHdIx6SyqKkwzjHcRy3A', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3289, 'outputTokens': 63, 'totalTokens': 3352}, 'metrics': {'latencyMs': 499}}
2025-09-24 17:47:44,757 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/01a86c59_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:44,794 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/41e50c08_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:44,821 - INFO - 

bol_K88AFHJU4HIQUZYTMOF4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:44,821 - INFO - 

✓ Saved result: output/run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:47:44,838 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fb648be8-e4e9-4173-9ec2-bb2641074445', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:44 GMT', 'content-type': 'application/json', 'content-length': '863', 'connection': 'keep-alive', 'x-amzn-requestid': 'fb648be8-e4e9-4173-9ec2-bb2641074445'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Both pages appear to be the same type: Bill of Lading, "UNIFROM STRAIGHT BILL OF LADING" "Bill of Lading NO 873960". So doc_type = bol. Check for continuation: The page 2 is same content repeated; likely continuation? But both are separate pages of same document. We need to output for each page: page_no and doc_type. No other pages. So output an array with two objects: page_no 1 bol, page_no 2 bol.\n\nWe need to call the tool classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4139, 'outputTokens': 154, 'totalTokens': 4293}, 'metrics': {'latencyMs': 898}}
2025-09-24 17:47:44,838 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/20c41725_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:45,082 - INFO - Page 1: Extracted 1608 characters, 74 lines from 70d89084_bol_P2EZG64EJI98NT69TSAL_54aeadaa_page_001.pdf
2025-09-24 17:47:45,083 - INFO - Successfully processed page 1
2025-09-24 17:47:45,083 - INFO - Combined 1 pages into final text
2025-09-24 17:47:45,083 - INFO - Text validation for 70d89084_bol_P2EZG64EJI98NT69TSAL: 1625 characters, 1 pages
2025-09-24 17:47:45,083 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:45,083 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:45,110 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/01a86c59_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:45,174 - INFO - 

bol_HEMTH4BHCWXWE044I0SK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:45,174 - INFO - 

✓ Saved result: output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:47:45,262 - INFO - S3 Image temp/7be80c6d_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg: Extracted 454 characters, 31 lines
2025-09-24 17:47:45,262 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:45,262 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:45,459 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/20c41725_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:46,016 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f137cdf9-afdf-4c01-a66d-b1e50958e83e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:45 GMT', 'content-type': 'application/json', 'content-length': '873', 'connection': 'keep-alive', 'x-amzn-requestid': 'f137cdf9-afdf-4c01-a66d-b1e50958e83e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. The content shows "Packing Slip" header. This appears to be a packing list / pack list. It lists items, quantities, shipping, driver, inspector, received by. The doc type likely "pack_list". The keyword "Packing Slip" is typical for pack list. So output: page_no 1, doc_type "pack_list".\n\nWe must use tool: classify_logistics_doc_type with array of documents. Provide object with page_no and doc_type. So call.\n\n'}}}, {'toolUse': {'toolUseId': 'tooluse_nRGFKBlpQk6wYjJuYOwCGw', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2697, 'outputTokens': 125, 'totalTokens': 2822}, 'metrics': {'latencyMs': 811}}
2025-09-24 17:47:46,017 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ee5f04fe_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:46,037 - INFO - 

pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:47:46,038 - INFO - 

✓ Saved result: output/run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:47:46,046 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2592f0f1-c270-4ba5-9605-3a71ca6e051a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:45 GMT', 'content-type': 'application/json', 'content-length': '598', 'connection': 'keep-alive', 'x-amzn-requestid': '2592f0f1-c270-4ba5-9605-3a71ca6e051a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. It seems to be a packing slip / packing list. The keywords: "Packing Slip", "Ship To", "Purchase Order", "Order Quantity", "Ship Quantity". That\'s a packing list. So doc_type: pack_list. No continuation. Output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2661, 'outputTokens': 83, 'totalTokens': 2744}, 'metrics': {'latencyMs': 529}}
2025-09-24 17:47:46,046 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4dd51aaf_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:46,326 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ee5f04fe_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:46,339 - INFO - 

pack_list_P6RAGXD27RW4QUXN96TH.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:47:46,340 - INFO - 

✓ Saved result: output/run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:47:46,640 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4dd51aaf_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:46,914 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b58c3ed5-af6c-4aed-8ef9-44d6a074ad92', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:46 GMT', 'content-type': 'application/json', 'content-length': '622', 'connection': 'keep-alive', 'x-amzn-requestid': 'b58c3ed5-af6c-4aed-8ef9-44d6a074ad92'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': "We need to classify each page. There's only page1. We must output via tool classify_logistics_doc_type with documents array containing one object for page1. Determine doc_type. The page is a Straight Bill of Lading - Short Form. So doc_type: bol. There's no mention of continuation. So output bol. Provide JSON."}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2968, 'outputTokens': 85, 'totalTokens': 3053}, 'metrics': {'latencyMs': 639}}
2025-09-24 17:47:46,914 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/70d89084_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:46,940 - INFO - 

bol_P2EZG64EJI98NT69TSAL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:46,940 - INFO - 

✓ Saved result: output/run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:47:47,117 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4f4fa26c-55fb-40b9-aa55-b47faa8d3891', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:46 GMT', 'content-type': 'application/json', 'content-length': '653', 'connection': 'keep-alive', 'x-amzn-requestid': '4f4fa26c-55fb-40b9-aa55-b47faa8d3891'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It appears to be a packing slip (Packing Slip). The keywords: "Packing Slip" is a header. The doc type list includes pack_list. So classify as pack_list. Must output via tool. Use array of documents: Each page has an object with page_no and doc_type. So return: [{page_no:1, doc_type:"pack_list"}]. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2662, 'outputTokens': 101, 'totalTokens': 2763}, 'metrics': {'latencyMs': 600}}
2025-09-24 17:47:47,117 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7be80c6d_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:47,239 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/70d89084_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:47,253 - INFO - 

pack_list_YC2ROS9NUB31VO0ABEV5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:47:47,254 - INFO - 

✓ Saved result: output/run1_pack_list_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:47:47,629 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7be80c6d_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:51,227 - INFO - Page 1: Extracted 2992 characters, 114 lines from 75f6b010_bol_RCNPMJFRSZTXQWBGLB9R_03b73d8b_page_001.pdf
2025-09-24 17:47:51,227 - INFO - Successfully processed page 1
2025-09-24 17:47:51,228 - INFO - Combined 1 pages into final text
2025-09-24 17:47:51,228 - INFO - Text validation for 75f6b010_bol_RCNPMJFRSZTXQWBGLB9R: 3009 characters, 1 pages
2025-09-24 17:47:51,228 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:51,228 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:52,236 - INFO - Page 1: Extracted 3032 characters, 118 lines from cccb6d1e_bol_PK5AIZFCER7DUFL15N01_96e7e78e_page_001.pdf
2025-09-24 17:47:52,236 - INFO - Successfully processed page 1
2025-09-24 17:47:52,236 - INFO - Combined 1 pages into final text
2025-09-24 17:47:52,237 - INFO - Text validation for cccb6d1e_bol_PK5AIZFCER7DUFL15N01: 3049 characters, 1 pages
2025-09-24 17:47:52,238 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:47:52,238 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:47:53,058 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '78b0e212-fceb-424c-8275-ea9b4b52359d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:52 GMT', 'content-type': 'application/json', 'content-length': '545', 'connection': 'keep-alive', 'x-amzn-requestid': '78b0e212-fceb-424c-8275-ea9b4b52359d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this single page. It\'s a Bill of Lading (BOL). The header says "BILL OF LADING". So doc_type = "bol". It\'s a single page. No continuation. So output array with one object: page_no 1, doc_type "bol". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3289, 'outputTokens': 80, 'totalTokens': 3369}, 'metrics': {'latencyMs': 620}}
2025-09-24 17:47:53,059 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/75f6b010_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:53,104 - INFO - 

bol_RCNPMJFRSZTXQWBGLB9R.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:53,105 - INFO - 

✓ Saved result: output/run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:47:53,395 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/75f6b010_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:54,076 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0d66b3a3-6df9-49fa-923f-9d11453d129b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:17:53 GMT', 'content-type': 'application/json', 'content-length': '563', 'connection': 'keep-alive', 'x-amzn-requestid': '0d66b3a3-6df9-49fa-923f-9d11453d129b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have single page. Need to classify. Content shows "BILL OF LADING" header, with BOL # etc. So doc_type = bol. Output needs to use tool classify_logistics_doc_type with documents array. Provide single object for page 1: page_no 1, doc_type "bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3307, 'outputTokens': 81, 'totalTokens': 3388}, 'metrics': {'latencyMs': 578}}
2025-09-24 17:47:54,077 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cccb6d1e_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:54,106 - INFO - 

bol_PK5AIZFCER7DUFL15N01.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:47:54,106 - INFO - 

✓ Saved result: output/run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:47:54,414 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cccb6d1e_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:54,415 - INFO - 
📊 Processing Summary:
2025-09-24 17:47:54,415 - INFO -    Total files: 16
2025-09-24 17:47:54,415 - INFO -    Successful: 16
2025-09-24 17:47:54,415 - INFO -    Failed: 0
2025-09-24 17:47:54,416 - INFO -    Duration: 34.34 seconds
2025-09-24 17:47:54,416 - INFO -    Output directory: output
2025-09-24 17:47:54,416 - INFO - 
📋 Successfully Processed Files:
2025-09-24 17:47:54,416 - INFO -    📄 AOYIL346IKT06LKO6D2R.jpg: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:47:54,416 - INFO -    📄 GKYUUU2YTZ1YTPGXHO51.pdf: {"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 17:47:54,416 - INFO -    📄 I6IM526GOVKAWH0B5WKE.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:47:54,416 - INFO -    📄 KK3PKFLS4ROTEZ7LOMMD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:47:54,416 - INFO -    📄 S92OW12RYFF5G3READPW.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 17:47:54,416 - INFO -    📄 TQSZJLFFGGKB4NFN12RD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_DV1B199A0TJGUQ3FETIU.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"bol"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_HEMTH4BHCWXWE044I0SK.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_IJW9IJUOROYY1SWO0B17.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_K88AFHJU4HIQUZYTMOF4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_P2EZG64EJI98NT69TSAL.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_PK5AIZFCER7DUFL15N01.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:47:54,416 - INFO -    📄 bol_RCNPMJFRSZTXQWBGLB9R.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:47:54,417 - INFO -    📄 pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:47:54,417 - INFO -    📄 pack_list_P6RAGXD27RW4QUXN96TH.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:47:54,417 - INFO -    📄 pack_list_YC2ROS9NUB31VO0ABEV5.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:47:54,417 - INFO - 
============================================================================================================================================
2025-09-24 17:47:54,417 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 17:47:54,417 - INFO - ============================================================================================================================================
2025-09-24 17:47:54,417 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 17:47:54,417 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:47:54,418 - INFO - AOYIL346IKT06LKO6D2R.jpg                           1      pod                                      run1_AOYIL346IKT06LKO6D2R.json                                                  
2025-09-24 17:47:54,418 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:47:54,418 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:47:54,418 - INFO - 
2025-09-24 17:47:54,418 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           1      pod                                      run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:47:54,418 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:54,418 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:54,418 - INFO - 
2025-09-24 17:47:54,418 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           2      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:47:54,418 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:54,418 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:54,418 - INFO - 
2025-09-24 17:47:54,418 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           3      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:47:54,418 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:47:54,418 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:54,418 - INFO - 
2025-09-24 17:47:54,418 - INFO - I6IM526GOVKAWH0B5WKE.pdf                           1      pod                                      run1_I6IM526GOVKAWH0B5WKE.json                                                  
2025-09-24 17:47:54,418 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:47:54,418 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:47:54,418 - INFO - 
2025-09-24 17:47:54,418 - INFO - KK3PKFLS4ROTEZ7LOMMD.pdf                           1      pod                                      run1_KK3PKFLS4ROTEZ7LOMMD.json                                                  
2025-09-24 17:47:54,418 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:47:54,419 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:47:54,419 - INFO - 
2025-09-24 17:47:54,419 - INFO - S92OW12RYFF5G3READPW.pdf                           1      invoice                                  run1_S92OW12RYFF5G3READPW.json                                                  
2025-09-24 17:47:54,419 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf
2025-09-24 17:47:54,419 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:47:54,419 - INFO - 
2025-09-24 17:47:54,419 - INFO - TQSZJLFFGGKB4NFN12RD.pdf                           1      pod                                      run1_TQSZJLFFGGKB4NFN12RD.json                                                  
2025-09-24 17:47:54,419 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:47:54,419 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:47:54,419 - INFO - 
2025-09-24 17:47:54,419 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       1      bol                                      run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:47:54,419 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:54,419 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:54,419 - INFO - 
2025-09-24 17:47:54,419 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       2      bol                                      run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:47:54,419 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:54,419 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:54,419 - INFO - 
2025-09-24 17:47:54,419 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       3      bol                                      run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:47:54,419 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:47:54,419 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:54,419 - INFO - 
2025-09-24 17:47:54,419 - INFO - bol_HEMTH4BHCWXWE044I0SK.pdf                       1      bol                                      run1_bol_HEMTH4BHCWXWE044I0SK.json                                              
2025-09-24 17:47:54,420 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:54,420 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:47:54,420 - INFO - 
2025-09-24 17:47:54,420 - INFO - bol_HEMTH4BHCWXWE044I0SK.pdf                       2      bol                                      run1_bol_HEMTH4BHCWXWE044I0SK.json                                              
2025-09-24 17:47:54,420 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:47:54,420 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:47:54,420 - INFO - 
2025-09-24 17:47:54,420 - INFO - bol_IJW9IJUOROYY1SWO0B17.pdf                       1      bol                                      run1_bol_IJW9IJUOROYY1SWO0B17.json                                              
2025-09-24 17:47:54,420 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:47:54,420 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:47:54,420 - INFO - 
2025-09-24 17:47:54,420 - INFO - bol_K88AFHJU4HIQUZYTMOF4.pdf                       1      bol                                      run1_bol_K88AFHJU4HIQUZYTMOF4.json                                              
2025-09-24 17:47:54,420 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:47:54,420 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:47:54,420 - INFO - 
2025-09-24 17:47:54,420 - INFO - bol_P2EZG64EJI98NT69TSAL.pdf                       1      bol                                      run1_bol_P2EZG64EJI98NT69TSAL.json                                              
2025-09-24 17:47:54,420 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:47:54,420 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:47:54,420 - INFO - 
2025-09-24 17:47:54,420 - INFO - bol_PK5AIZFCER7DUFL15N01.pdf                       1      bol                                      run1_bol_PK5AIZFCER7DUFL15N01.json                                              
2025-09-24 17:47:54,420 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:47:54,420 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:47:54,420 - INFO - 
2025-09-24 17:47:54,421 - INFO - bol_RCNPMJFRSZTXQWBGLB9R.pdf                       1      bol                                      run1_bol_RCNPMJFRSZTXQWBGLB9R.json                                              
2025-09-24 17:47:54,421 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:47:54,421 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:47:54,421 - INFO - 
2025-09-24 17:47:54,421 - INFO - pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg                1      pack_list                                run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json                                        
2025-09-24 17:47:54,421 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:47:54,421 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:47:54,421 - INFO - 
2025-09-24 17:47:54,421 - INFO - pack_list_P6RAGXD27RW4QUXN96TH.jpeg                1      pack_list                                run1_pack_list_P6RAGXD27RW4QUXN96TH.json                                        
2025-09-24 17:47:54,421 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:47:54,421 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:47:54,421 - INFO - 
2025-09-24 17:47:54,421 - INFO - pack_list_YC2ROS9NUB31VO0ABEV5.jpeg                1      pack_list                                run1_pack_list_YC2ROS9NUB31VO0ABEV5.json                                        
2025-09-24 17:47:54,421 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:47:54,421 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:47:54,421 - INFO - 
2025-09-24 17:47:54,421 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:47:54,421 - INFO - Total entries: 21
2025-09-24 17:47:54,421 - INFO - ============================================================================================================================================
2025-09-24 17:47:54,421 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 17:47:54,421 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:47:54,421 - INFO -   1. AOYIL346IKT06LKO6D2R.jpg            Page 1   → pod             | run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:47:54,422 - INFO -   2. GKYUUU2YTZ1YTPGXHO51.pdf            Page 1   → pod             | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:54,422 - INFO -   3. GKYUUU2YTZ1YTPGXHO51.pdf            Page 2   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:54,422 - INFO -   4. GKYUUU2YTZ1YTPGXHO51.pdf            Page 3   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:47:54,422 - INFO -   5. I6IM526GOVKAWH0B5WKE.pdf            Page 1   → pod             | run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:47:54,422 - INFO -   6. KK3PKFLS4ROTEZ7LOMMD.pdf            Page 1   → pod             | run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:47:54,422 - INFO -   7. S92OW12RYFF5G3READPW.pdf            Page 1   → invoice         | run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:47:54,422 - INFO -   8. TQSZJLFFGGKB4NFN12RD.pdf            Page 1   → pod             | run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:47:54,422 - INFO -   9. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 1   → bol             | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:54,422 - INFO -  10. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 2   → bol             | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:54,422 - INFO -  11. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 3   → bol             | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:47:54,422 - INFO -  12. bol_HEMTH4BHCWXWE044I0SK.pdf        Page 1   → bol             | run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:47:54,422 - INFO -  13. bol_HEMTH4BHCWXWE044I0SK.pdf        Page 2   → bol             | run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:47:54,422 - INFO -  14. bol_IJW9IJUOROYY1SWO0B17.pdf        Page 1   → bol             | run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:47:54,422 - INFO -  15. bol_K88AFHJU4HIQUZYTMOF4.pdf        Page 1   → bol             | run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:47:54,422 - INFO -  16. bol_P2EZG64EJI98NT69TSAL.pdf        Page 1   → bol             | run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:47:54,422 - INFO -  17. bol_PK5AIZFCER7DUFL15N01.pdf        Page 1   → bol             | run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:47:54,422 - INFO -  18. bol_RCNPMJFRSZTXQWBGLB9R.pdf        Page 1   → bol             | run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:47:54,422 - INFO -  19. pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg Page 1   → pack_list       | run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:47:54,423 - INFO -  20. pack_list_P6RAGXD27RW4QUXN96TH.jpeg Page 1   → pack_list       | run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:47:54,423 - INFO -  21. pack_list_YC2ROS9NUB31VO0ABEV5.jpeg Page 1   → pack_list       | run1_pack_list_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:47:54,423 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:47:54,423 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 34.34382, 'processed_files': [{'filename': 'AOYIL346IKT06LKO6D2R.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg'}, {'filename': 'GKYUUU2YTZ1YTPGXHO51.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf'}, {'filename': 'I6IM526GOVKAWH0B5WKE.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf'}, {'filename': 'KK3PKFLS4ROTEZ7LOMMD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf'}, {'filename': 'S92OW12RYFF5G3READPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf'}, {'filename': 'TQSZJLFFGGKB4NFN12RD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf'}, {'filename': 'bol_DV1B199A0TJGUQ3FETIU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}, {'page_no': 3, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf'}, {'filename': 'bol_HEMTH4BHCWXWE044I0SK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf'}, {'filename': 'bol_IJW9IJUOROYY1SWO0B17.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf'}, {'filename': 'bol_K88AFHJU4HIQUZYTMOF4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf'}, {'filename': 'bol_P2EZG64EJI98NT69TSAL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf'}, {'filename': 'bol_PK5AIZFCER7DUFL15N01.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf'}, {'filename': 'bol_RCNPMJFRSZTXQWBGLB9R.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf'}, {'filename': 'pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg'}, {'filename': 'pack_list_P6RAGXD27RW4QUXN96TH.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg'}, {'filename': 'pack_list_YC2ROS9NUB31VO0ABEV5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_YC2ROS9NUB31VO0ABEV5.jpeg'}]}
