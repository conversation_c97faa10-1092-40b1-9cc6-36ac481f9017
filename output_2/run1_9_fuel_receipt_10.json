{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.5926582217216492, "Height": 0.8873576521873474, "Left": 0.1707426905632019, "Top": 0.04072597995400429}, "Polygon": [{"X": 0.1707426905632019, "Y": 0.05763465538620949}, {"X": 0.7307333946228027, "Y": 0.04072597995400429}, {"X": 0.7634009122848511, "Y": 0.9198020100593567}, {"X": 0.24584701657295227, "Y": 0.9280836582183838}]}, "Id": "0fba713d-4eb1-42d2-a5a1-77bbcafe85e5", "Relationships": [{"Type": "CHILD", "Ids": ["e7a10c7c-064c-42cf-8801-1fddc9ab7c1c", "4cb8048e-9f63-4e96-b59e-7ab86b00f474", "cfaed669-9e4c-4af7-823b-97ac5420f496", "18ed8cc5-c32d-4e24-a01f-259c8a87d405", "fb05a569-3bde-4906-938c-f26947c5c956", "d58a0f40-85c3-449d-a0c1-28eb05429bc9", "68da365f-d0c7-48fd-9d7c-ef4fb3fd9246", "e113a36d-e1c5-45b8-9f68-f4e26f6114d3", "ac5d79cb-d4b5-47b6-8a00-2aca601c88a5", "36b1adfe-62ea-47ee-b2f5-f6856a66c389", "05f2df83-b70c-4539-ad1e-78969eebb4ba", "8e416247-751a-420b-84bd-15358290fe95", "def8ef39-de3e-4023-a163-96b5c013727e", "66ac4b27-9d0d-492b-9a36-99ebb504099f", "8f2da4df-601c-4be4-88cf-281b383513f0", "a3952cbf-3125-47c6-b7c6-c2475c028172", "a1c9dd4e-86c2-4493-a501-fb0e6abd7a2c", "22d2a428-ed42-4091-8f13-79089df9c5a4", "21f03dcd-905f-4bab-a37a-fb305397f6db", "b05f0707-f0d7-4ef4-a8f9-8426b04e742b", "5c5f282b-a0c1-41e6-b0d5-5c6d6df004e7", "d52662c9-753b-416c-8c48-b00e25b96ba2", "838c04f8-af16-454c-8402-6b62f6a9c4f1", "971a0de0-b0b8-4938-907b-6f0b7a78f646", "1bbe27a3-b613-4835-aed5-70310849c08f", "5128ae2a-dcc3-48b6-8a50-d80b1aff8fd7", "f579d429-a4c5-4f72-bbd7-475c4364945d", "914abba4-a2ec-40eb-ac5f-3e36fc827f7b"]}]}, {"BlockType": "LINE", "Confidence": 99.9606704711914, "Text": "KWIK TRIP #885", "Geometry": {"BoundingBox": {"Width": 0.25113609433174133, "Height": 0.02544017694890499, "Left": 0.32543274760246277, "Top": 0.2852621376514435}, "Polygon": [{"X": 0.32543274760246277, "Y": 0.29184484481811523}, {"X": 0.5755866169929504, "Y": 0.2852621376514435}, {"X": 0.5765688419342041, "Y": 0.3042072355747223}, {"X": 0.32683295011520386, "Y": 0.31070229411125183}]}, "Id": "e7a10c7c-064c-42cf-8801-1fddc9ab7c1c", "Relationships": [{"Type": "CHILD", "Ids": ["c0986da5-7696-4a31-a638-ca9268a811c8", "841821ee-05e6-4614-a70c-a771adc0a7a5", "2f40203c-41e4-418c-8964-048c334321c6"]}]}, {"BlockType": "LINE", "Confidence": 99.74456787109375, "Text": "301 ELDERBERRY RD", "Geometry": {"BoundingBox": {"Width": 0.30412527918815613, "Height": 0.02690006233751774, "Left": 0.29117438197135925, "Top": 0.3115958273410797}, "Polygon": [{"X": 0.29117438197135925, "Y": 0.31944191455841064}, {"X": 0.5943360924720764, "Y": 0.3115958273410797}, {"X": 0.5952996611595154, "Y": 0.3307570219039917}, {"X": 0.2926512360572815, "Y": 0.3384959101676941}]}, "Id": "4cb8048e-9f63-4e96-b59e-7ab86b00f474", "Relationships": [{"Type": "CHILD", "Ids": ["fba18272-ed82-489d-aa45-b0d4be4ebb31", "a3c201bd-2b52-4021-99de-7ee5af612bfd", "3fc5860a-7b9d-4659-83be-bd56e07505cb"]}]}, {"BlockType": "LINE", "Confidence": 97.86870574951172, "Text": "ABBOTSFORD, WI", "Geometry": {"BoundingBox": {"Width": 0.24763083457946777, "Height": 0.028828956186771393, "Left": 0.33071932196617126, "Top": 0.33910566568374634}, "Polygon": [{"X": 0.33071932196617126, "Y": 0.3453761637210846}, {"X": 0.5771728157997131, "Y": 0.33910566568374634}, {"X": 0.5783501267433167, "Y": 0.3617669641971588}, {"X": 0.3323916792869568, "Y": 0.36793461441993713}]}, "Id": "cfaed669-9e4c-4af7-823b-97ac5420f496", "Relationships": [{"Type": "CHILD", "Ids": ["32b9ecb5-9154-4a9b-9553-5ab6425bb4cc", "1618af62-38c1-4b06-b822-b3fd11152168"]}]}, {"BlockType": "LINE", "Confidence": 99.16334533691406, "Text": "Invoice# 0090428", "Geometry": {"BoundingBox": {"Width": 0.2971690595149994, "Height": 0.02539757825434208, "Left": 0.28321340680122375, "Top": 0.3920554220676422}, "Polygon": [{"X": 0.28321340680122375, "Y": 0.3993382453918457}, {"X": 0.579435408115387, "Y": 0.3920554220676422}, {"X": 0.5803824663162231, "Y": 0.41026926040649414}, {"X": 0.2846406102180481, "Y": 0.41745302081108093}]}, "Id": "18ed8cc5-c32d-4e24-a01f-259c8a87d405", "Relationships": [{"Type": "CHILD", "Ids": ["2723ea3d-c6c9-453a-abcc-932c52da148c", "4f435023-f13d-4e36-9ebb-d5c65e54a484"]}]}, {"BlockType": "LINE", "Confidence": 97.32415008544922, "Text": "Trans # 0900-0428", "Geometry": {"BoundingBox": {"Width": 0.33541831374168396, "Height": 0.026269588619470596, "Left": 0.28146713972091675, "Top": 0.4168721139431}, "Polygon": [{"X": 0.28146713972091675, "Y": 0.4249575436115265}, {"X": 0.6159924864768982, "Y": 0.4168721139431}, {"X": 0.6168854236602783, "Y": 0.4351683259010315}, {"X": 0.2829059958457947, "Y": 0.4431416988372803}]}, "Id": "fb05a569-3bde-4906-938c-f26947c5c956", "Relationships": [{"Type": "CHILD", "Ids": ["867fe586-a2bf-4879-a432-686d63d052bb", "799b1cf2-0674-4339-bec2-15d24343d83e", "bbcff072-baab-4253-9199-19300b65d4f7"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.07014358788728714, "Height": 0.017915531992912292, "Left": 0.2867220342159271, "Top": 0.45018455386161804}, "Polygon": [{"X": 0.2867220342159271, "Y": 0.45182108879089355}, {"X": 0.35568270087242126, "Y": 0.45018455386161804}, {"X": 0.35686561465263367, "Y": 0.4664842188358307}, {"X": 0.288005530834198, "Y": 0.46810010075569153}]}, "Id": "d58a0f40-85c3-449d-a0c1-28eb05429bc9", "Relationships": [{"Type": "CHILD", "Ids": ["b427fac6-3006-4505-8124-0f876bd8634e"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "09/04/2025", "Geometry": {"BoundingBox": {"Width": 0.17514896392822266, "Height": 0.023946523666381836, "Left": 0.461292564868927, "Top": 0.4423026144504547}, "Polygon": [{"X": 0.461292564868927, "Y": 0.44644036889076233}, {"X": 0.6355046033859253, "Y": 0.4423026144504547}, {"X": 0.6364415287971497, "Y": 0.462174654006958}, {"X": 0.4625399112701416, "Y": 0.46624913811683655}]}, "Id": "68da365f-d0c7-48fd-9d7c-ef4fb3fd9246", "Relationships": [{"Type": "CHILD", "Ids": ["9c6b0467-fadb-4a0c-ac70-6c654275ccf8"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.07113468647003174, "Height": 0.01896274834871292, "Left": 0.28756481409072876, "Top": 0.4740091860294342}, "Polygon": [{"X": 0.28756481409072876, "Y": 0.47564032673835754}, {"X": 0.3574400842189789, "Y": 0.4740091860294342}, {"X": 0.3586995005607605, "Y": 0.491362988948822}, {"X": 0.2889329195022583, "Y": 0.49297192692756653}]}, "Id": "e113a36d-e1c5-45b8-9f68-f4e26f6114d3", "Relationships": [{"Type": "CHILD", "Ids": ["be02a3c0-fc83-4122-96b9-12b590e7f5da"]}]}, {"BlockType": "LINE", "Confidence": 99.44136810302734, "Text": "08:08 AM", "Geometry": {"BoundingBox": {"Width": 0.13824301958084106, "Height": 0.02024688944220543, "Left": 0.46299076080322266, "Top": 0.4692375957965851}, "Polygon": [{"X": 0.46299076080322266, "Y": 0.4724425971508026}, {"X": 0.6003717184066772, "Y": 0.4692375957965851}, {"X": 0.6012337803840637, "Y": 0.4863223433494568}, {"X": 0.4640637934207916, "Y": 0.4894844889640808}]}, "Id": "ac5d79cb-d4b5-47b6-8a00-2aca601c88a5", "Relationships": [{"Type": "CHILD", "Ids": ["e24ab6d9-c95c-440e-8a7c-5f29e44ea664", "9ea11095-c1a7-4af1-a02d-8e6472bd92a7"]}]}, {"BlockType": "LINE", "Confidence": 99.9669189453125, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.34652596712112427, "Height": 0.02957173064351082, "Left": 0.2929080128669739, "Top": 0.518296480178833}, "Polygon": [{"X": 0.2929080128669739, "Y": 0.5260776281356812}, {"X": 0.6383987069129944, "Y": 0.518296480178833}, {"X": 0.6394339799880981, "Y": 0.5402246713638306}, {"X": 0.2946254014968872, "Y": 0.5478681921958923}]}, "Id": "36b1adfe-62ea-47ee-b2f5-f6856a66c389", "Relationships": [{"Type": "CHILD", "Ids": ["76bf3a83-dbed-4239-bbfa-287b6294043d", "3dd6cc54-6acc-469a-8069-f6addeea8fd2", "025a7b09-7818-4751-9950-9d97a8b55912"]}]}, {"BlockType": "LINE", "Confidence": 99.75186920166016, "Text": "02", "Geometry": {"BoundingBox": {"Width": 0.034514572471380234, "Height": 0.017023252323269844, "Left": 0.3122924864292145, "Top": 0.5506505966186523}, "Polygon": [{"X": 0.3122924864292145, "Y": 0.5513867735862732}, {"X": 0.34559839963912964, "Y": 0.5506505966186523}, {"X": 0.346807062625885, "Y": 0.5669475197792053}, {"X": 0.31355020403862, "Y": 0.5676738619804382}]}, "Id": "05f2df83-b70c-4539-ad1e-78969eebb4ba", "Relationships": [{"Type": "CHILD", "Ids": ["835423b9-6f73-4143-930a-3542ae364395"]}]}, {"BlockType": "LINE", "Confidence": 99.61196899414062, "Text": "149.572 3.599", "Geometry": {"BoundingBox": {"Width": 0.2420390546321869, "Height": 0.023944765329360962, "Left": 0.39827269315719604, "Top": 0.5442020297050476}, "Polygon": [{"X": 0.39827269315719604, "Y": 0.5495323538780212}, {"X": 0.6394287347793579, "Y": 0.5442020297050476}, {"X": 0.6403117775917053, "Y": 0.5628982782363892}, {"X": 0.39956334233283997, "Y": 0.5681467652320862}]}, "Id": "8e416247-751a-420b-84bd-15358290fe95", "Relationships": [{"Type": "CHILD", "Ids": ["6cb63771-534e-4cd9-a479-9bfa30c92c34", "d850ce03-284d-44ff-a84e-dcfbd87ca2da"]}]}, {"BlockType": "LINE", "Confidence": 99.294677734375, "Text": "02", "Geometry": {"BoundingBox": {"Width": 0.03390832617878914, "Height": 0.01696549355983734, "Left": 0.31385740637779236, "Top": 0.5752984285354614}, "Polygon": [{"X": 0.31385740637779236, "Y": 0.576008141040802}, {"X": 0.3465580940246582, "Y": 0.5752984285354614}, {"X": 0.34776571393013, "Y": 0.591563880443573}, {"X": 0.3151131868362427, "Y": 0.59226393699646}]}, "Id": "def8ef39-de3e-4023-a163-96b5c013727e", "Relationships": [{"Type": "CHILD", "Ids": ["b6c7f2e8-0e9c-4d69-b039-ea02d19c1d26"]}]}, {"BlockType": "LINE", "Confidence": 99.71161651611328, "Text": "8.438 3.849", "Geometry": {"BoundingBox": {"Width": 0.20833256840705872, "Height": 0.023093970492482185, "Left": 0.4333781898021698, "Top": 0.569692075252533}, "Polygon": [{"X": 0.4333781898021698, "Y": 0.5741918683052063}, {"X": 0.6408295631408691, "Y": 0.569692075252533}, {"X": 0.6417107582092285, "Y": 0.5883564352989197}, {"X": 0.43461042642593384, "Y": 0.5927860736846924}]}, "Id": "66ac4b27-9d0d-492b-9a36-99ebb504099f", "Relationships": [{"Type": "CHILD", "Ids": ["a1180fb4-e247-4727-9171-6411af7a69bb", "5aaf6660-159d-4ede-bdac-0504ddae561e"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.12095757573843002, "Height": 0.020266683772206306, "Left": 0.2957591414451599, "Top": 0.6235003471374512}, "Polygon": [{"X": 0.2957591414451599, "Y": 0.6260026097297668}, {"X": 0.4155022203922272, "Y": 0.6235003471374512}, {"X": 0.41671672463417053, "Y": 0.641303300857544}, {"X": 0.29716745018959045, "Y": 0.6437669992446899}]}, "Id": "8f2da4df-601c-4be4-88cf-281b383513f0", "Relationships": [{"Type": "CHILD", "Ids": ["c97294ae-99ab-4dae-bb56-0c6e2b389f4d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.10595392435789108, "Height": 0.02124728448688984, "Left": 0.5384883880615234, "Top": 0.6216651201248169}, "Polygon": [{"X": 0.5384883880615234, "Y": 0.623855471611023}, {"X": 0.6435413360595703, "Y": 0.6216651201248169}, {"X": 0.6444423198699951, "Y": 0.6407582759857178}, {"X": 0.5395724177360535, "Y": 0.6429123878479004}]}, "Id": "a3952cbf-3125-47c6-b7c6-c2475c028172", "Relationships": [{"Type": "CHILD", "Ids": ["229bbf75-93cd-4f43-bdf3-a6f5d1f79c6e"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.06754791736602783, "Height": 0.019055282697081566, "Left": 0.2968209981918335, "Top": 0.6503100395202637}, "Polygon": [{"X": 0.2968209981918335, "Y": 0.6516668200492859}, {"X": 0.3630712628364563, "Y": 0.6503100395202637}, {"X": 0.36436891555786133, "Y": 0.6680297255516052}, {"X": 0.29822567105293274, "Y": 0.6693652868270874}]}, "Id": "a1c9dd4e-86c2-4493-a501-fb0e6abd7a2c", "Relationships": [{"Type": "CHILD", "Ids": ["9acc73a1-7647-4a6f-8ca7-67923c558242"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "538.31", "Geometry": {"BoundingBox": {"Width": 0.10277938097715378, "Height": 0.02156108245253563, "Left": 0.538869321346283, "Top": 0.6479736566543579}, "Polygon": [{"X": 0.538869321346283, "Y": 0.6500540375709534}, {"X": 0.6407204270362854, "Y": 0.6479736566543579}, {"X": 0.6416487097740173, "Y": 0.6674901843070984}, {"X": 0.5399793982505798, "Y": 0.6695347428321838}]}, "Id": "22d2a428-ed42-4091-8f13-79089df9c5a4", "Relationships": [{"Type": "CHILD", "Ids": ["0c36bb0c-374e-4354-a687-013326829308"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "DEFBULK", "Geometry": {"BoundingBox": {"Width": 0.11752024292945862, "Height": 0.022814631462097168, "Left": 0.2989172041416168, "Top": 0.67391037940979}, "Polygon": [{"X": 0.2989172041416168, "Y": 0.6762418746948242}, {"X": 0.41502973437309265, "Y": 0.67391037940979}, {"X": 0.41643744707107544, "Y": 0.6944364309310913}, {"X": 0.30054259300231934, "Y": 0.6967250108718872}]}, "Id": "21f03dcd-905f-4bab-a37a-fb305397f6db", "Relationships": [{"Type": "CHILD", "Ids": ["4139f4ca-7848-4603-981c-c5665145fe5e"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "32.48", "Geometry": {"BoundingBox": {"Width": 0.08876445144414902, "Height": 0.020797444507479668, "Left": 0.5578718781471252, "Top": 0.6749652624130249}, "Polygon": [{"X": 0.5578718781471252, "Y": 0.6767215132713318}, {"X": 0.6457358598709106, "Y": 0.6749652624130249}, {"X": 0.6466363072395325, "Y": 0.6940366625785828}, {"X": 0.5589261054992676, "Y": 0.6957627534866333}]}, "Id": "b05f0707-f0d7-4ef4-a8f9-8426b04e742b", "Relationships": [{"Type": "CHILD", "Ids": ["0bb91965-b39a-4ea7-a1aa-295754896a31"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.08512358367443085, "Height": 0.023070454597473145, "Left": 0.3004342317581177, "Top": 0.7228091359138489}, "Polygon": [{"X": 0.3004342317581177, "Y": 0.7244219779968262}, {"X": 0.384015291929245, "Y": 0.7228091359138489}, {"X": 0.38555780053138733, "Y": 0.7442991137504578}, {"X": 0.30214154720306396, "Y": 0.745879590511322}]}, "Id": "5c5f282b-a0c1-41e6-b0d5-5c6d6df004e7", "Relationships": [{"Type": "CHILD", "Ids": ["87e0cba9-0b68-4b45-90a2-7964eaec2180"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "570.79", "Geometry": {"BoundingBox": {"Width": 0.10691002756357193, "Height": 0.021360550075769424, "Left": 0.5237612128257751, "Top": 0.7279072999954224}, "Polygon": [{"X": 0.5237612128257751, "Y": 0.7299351096153259}, {"X": 0.629723310470581, "Y": 0.7279072999954224}, {"X": 0.6306712627410889, "Y": 0.7472767233848572}, {"X": 0.5248982906341553, "Y": 0.7492678165435791}]}, "Id": "d52662c9-753b-416c-8c48-b00e25b96ba2", "Relationships": [{"Type": "CHILD", "Ids": ["a84d24ea-1b7d-40f1-80b2-eaf0165ec483"]}]}, {"BlockType": "LINE", "Confidence": 99.84487915039062, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.3271760642528534, "Height": 0.03820156306028366, "Left": 0.30573830008506775, "Top": 0.7674157023429871}, "Polygon": [{"X": 0.30573830008506775, "Y": 0.7734386324882507}, {"X": 0.6313292980194092, "Y": 0.7674157023429871}, {"X": 0.6329143643379211, "Y": 0.7997822165489197}, {"X": 0.3082944452762604, "Y": 0.8056172728538513}]}, "Id": "838c04f8-af16-454c-8402-6b62f6a9c4f1", "Relationships": [{"Type": "CHILD", "Ids": ["669afb6c-c29a-4cdb-8839-b57231cf9063", "bbce58e3-1145-46b4-8f2b-d5154a8a60bc", "b05a254b-5d08-4721-9f5a-34feb2f1ca18"]}]}, {"BlockType": "LINE", "Confidence": 99.51231384277344, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.15150725841522217, "Height": 0.02605641447007656, "Left": 0.30749499797821045, "Top": 0.7942987680435181}, "Polygon": [{"X": 0.30749499797821045, "Y": 0.7970156073570251}, {"X": 0.****************, "Y": 0.7942987680435181}, {"X": 0.***************, "Y": 0.8177011013031006}, {"X": 0.30934926867485046, "Y": 0.820355236530304}]}, "Id": "971a0de0-b0b8-4938-907b-6f0b7a78f646", "Relationships": [{"Type": "CHILD", "Ids": ["e44b9764-285f-4b81-b95f-e1cc7344f79d", "483e6d3b-57b8-466f-8d09-2681a04c25b9"]}]}, {"BlockType": "LINE", "Confidence": 89.1661376953125, "Text": "AUTH XXXXXXXXXXXXXX1591 #", "Geometry": {"BoundingBox": {"Width": 0.30474790930747986, "Height": 0.0472322441637516, "Left": 0.30853280425071716, "Top": 0.8178847432136536}, "Polygon": [{"X": 0.30853280425071716, "Y": 0.8232372403144836}, {"X": 0.611129105091095, "Y": 0.8178847432136536}, {"X": 0.613280713558197, "Y": 0.8599907159805298}, {"X": 0.31186413764953613, "Y": 0.8651169538497925}]}, "Id": "1bbe27a3-b613-4835-aed5-70310849c08f", "Relationships": [{"Type": "CHILD", "Ids": ["a089571b-1c63-4cd8-aff0-258e1c138a78", "599fff97-81d4-446c-bfdd-111ee1862a56", "1070d44e-6b59-487e-86b0-240bc59f2b15"]}]}, {"BlockType": "LINE", "Confidence": 99.27435302734375, "Text": "P43347", "Geometry": {"BoundingBox": {"Width": 0.10632408410310745, "Height": 0.02323223650455475, "Left": 0.5468494892120361, "Top": 0.8510990738868713}, "Polygon": [{"X": 0.5468494892120361, "Y": 0.8529040813446045}, {"X": 0.6521560549736023, "Y": 0.8510990738868713}, {"X": 0.653173565864563, "Y": 0.8725664615631104}, {"X": 0.5480777025222778, "Y": 0.8743313550949097}]}, "Id": "5128ae2a-dcc3-48b6-8a50-d80b1aff8fd7", "Relationships": [{"Type": "CHILD", "Ids": ["ca8797c1-7354-4df1-9df2-9e7c487d0417"]}]}, {"BlockType": "LINE", "Confidence": 99.28292846679688, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.3078773617744446, "Height": 0.03294716030359268, "Left": 0.31098857522010803, "Top": 0.8651414513587952}, "Polygon": [{"X": 0.31098857522010803, "Y": 0.8703276515007019}, {"X": 0.6174498796463013, "Y": 0.8651414513587952}, {"X": 0.618865966796875, "Y": 0.8930538892745972}, {"X": 0.3132002055644989, "Y": 0.8980886340141296}]}, "Id": "f579d429-a4c5-4f72-bbd7-475c4364945d", "Relationships": [{"Type": "CHILD", "Ids": ["5f24e4fb-7328-48f4-8915-5a9a80df1b75", "4e76d18f-48c0-4784-a73a-6f7db7bc3cc3"]}]}, {"BlockType": "LINE", "Confidence": 99.15125274658203, "Text": "AUTH REF#: P43347", "Geometry": {"BoundingBox": {"Width": 0.2907390892505646, "Height": 0.02386900596320629, "Left": 0.3285296857357025, "Top": 0.8913964033126831}, "Polygon": [{"X": 0.3285296857357025, "Y": 0.8961769938468933}, {"X": 0.6182945370674133, "Y": 0.8913964033126831}, {"X": 0.6192687749862671, "Y": 0.9105831384658813}, {"X": 0.3300224542617798, "Y": 0.9152653813362122}]}, "Id": "914abba4-a2ec-40eb-ac5f-3e36fc827f7b", "Relationships": [{"Type": "CHILD", "Ids": ["2be40a93-c4f2-44a6-95ae-22243cfb987a", "9e12ecf4-f5c7-477d-9e56-a05e28bedac2", "83bf143c-894f-40a0-b274-c04a83ae0785"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0728396400809288, "Height": 0.020167239010334015, "Left": 0.32543274760246277, "Top": 0.2899607717990875}, "Polygon": [{"X": 0.32543274760246277, "Y": 0.29184484481811523}, {"X": 0.39703047275543213, "Y": 0.2899607717990875}, {"X": 0.39827239513397217, "Y": 0.3082682490348816}, {"X": 0.32679030299186707, "Y": 0.31012800335884094}]}, "Id": "c0986da5-7696-4a31-a638-ca9268a811c8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07295102626085281, "Height": 0.019732752814888954, "Left": 0.41538217663764954, "Top": 0.2883845865726471}, "Polygon": [{"X": 0.41538217663764954, "Y": 0.2902752459049225}, {"X": 0.48726558685302734, "Y": 0.2883845865726471}, {"X": 0.48833319544792175, "Y": 0.3062504827976227}, {"X": 0.4165632724761963, "Y": 0.3081173300743103}]}, "Id": "841821ee-05e6-4614-a70c-a771adc0a7a5"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "#885", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07129879295825958, "Height": 0.020048540085554123, "Left": 0.5052700638771057, "Top": 0.2859854996204376}, "Polygon": [{"X": 0.5052700638771057, "Y": 0.2878360152244568}, {"X": 0.5756241679191589, "Y": 0.2859854996204376}, {"X": 0.5765688419342041, "Y": 0.3042072355747223}, {"X": 0.5063281655311584, "Y": 0.30603402853012085}]}, "Id": "2f40203c-41e4-418c-8964-048c334321c6"}, {"BlockType": "WORD", "Confidence": 99.24346160888672, "Text": "301", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049803100526332855, "Height": 0.0187807809561491, "Left": 0.291244238615036, "Top": 0.31908807158470154}, "Polygon": [{"X": 0.291244238615036, "Y": 0.32034310698509216}, {"X": 0.33976420760154724, "Y": 0.31908807158470154}, {"X": 0.34104734659194946, "Y": 0.33662959933280945}, {"X": 0.29260262846946716, "Y": 0.3378688395023346}]}, "Id": "fba18272-ed82-489d-aa45-b0d4be4ebb31"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ELDERBERRY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17868490517139435, "Height": 0.022614171728491783, "Left": 0.36450082063674927, "Top": 0.314011812210083}, "Polygon": [{"X": 0.36450082063674927, "Y": 0.31860753893852234}, {"X": 0.5421919822692871, "Y": 0.314011812210083}, {"X": 0.5431857705116272, "Y": 0.3320896029472351}, {"X": 0.3657788336277008, "Y": 0.33662599325180054}]}, "Id": "a3c201bd-2b52-4021-99de-7ee5af612bfd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03465947508811951, "Height": 0.018493331968784332, "Left": 0.5605632066726685, "Top": 0.3115958273410797}, "Polygon": [{"X": 0.5605632066726685, "Y": 0.3124698996543884}, {"X": 0.5943360924720764, "Y": 0.3115958273410797}, {"X": 0.5952226519584656, "Y": 0.3292261064052582}, {"X": 0.5615025758743286, "Y": 0.33008918166160583}]}, "Id": "3fc5860a-7b9d-4659-83be-bd56e07505cb"}, {"BlockType": "WORD", "Confidence": 95.84642028808594, "Text": "ABBOTSFORD,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1908775120973587, "Height": 0.026314755901694298, "Left": 0.33079829812049866, "Top": 0.3416198492050171}, "Polygon": [{"X": 0.33079829812049866, "Y": 0.34644174575805664}, {"X": 0.5204451084136963, "Y": 0.3416198492050171}, {"X": 0.5216758251190186, "Y": 0.36318811774253845}, {"X": 0.3323916792869568, "Y": 0.36793461441993713}]}, "Id": "32b9ecb5-9154-4a9b-9553-5ab6425bb4cc"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03620430827140808, "Height": 0.018207356333732605, "Left": 0.5418683886528015, "Top": 0.33910566568374634}, "Polygon": [{"X": 0.5418683886528015, "Y": 0.3400039076805115}, {"X": 0.5771728157997131, "Y": 0.33910566568374634}, {"X": 0.5780726671218872, "Y": 0.35642603039741516}, {"X": 0.5428225994110107, "Y": 0.35731300711631775}]}, "Id": "1618af62-38c1-4b06-b822-b3fd11152168"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1413845270872116, "Height": 0.021430909633636475, "Left": 0.2832236886024475, "Top": 0.39602211117744446}, "Polygon": [{"X": 0.2832236886024475, "Y": 0.3994685411453247}, {"X": 0.4234163165092468, "Y": 0.39602211117744446}, {"X": 0.4246082007884979, "Y": 0.4140531122684479}, {"X": 0.2846406102180481, "Y": 0.41745302081108093}]}, "Id": "2723ea3d-c6c9-453a-abcc-932c52da148c"}, {"BlockType": "WORD", "Confidence": 98.91402435302734, "Text": "0090428", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12174264341592789, "Height": 0.020273543894290924, "Left": 0.4585944712162018, "Top": 0.3920554220676422}, "Polygon": [{"X": 0.4585944712162018, "Y": 0.39502638578414917}, {"X": 0.579435408115387, "Y": 0.3920554220676422}, {"X": 0.5803371071815491, "Y": 0.40939652919769287}, {"X": 0.4596831500530243, "Y": 0.4123289883136749}]}, "Id": "4f435023-f13d-4e36-9ebb-d5c65e54a484"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09070645272731781, "Height": 0.018802402541041374, "Left": 0.28158923983573914, "Top": 0.42433929443359375}, "Polygon": [{"X": 0.28158923983573914, "Y": 0.4265007972717285}, {"X": 0.3711121380329132, "Y": 0.42433929443359375}, {"X": 0.37229570746421814, "Y": 0.4410076141357422}, {"X": 0.2829059958457947, "Y": 0.4431416988372803}]}, "Id": "867fe586-a2bf-4879-a432-686d63d052bb"}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019688142463564873, "Height": 0.017062127590179443, "Left": 0.4066304564476013, "Top": 0.4224865734577179}, "Polygon": [{"X": 0.4066304564476013, "Y": 0.4229355752468109}, {"X": 0.4252201318740845, "Y": 0.4224865734577179}, {"X": 0.42631858587265015, "Y": 0.439105361700058}, {"X": 0.4077565371990204, "Y": 0.43954870104789734}]}, "Id": "799b1cf2-0674-4339-bec2-15d24343d83e"}, {"BlockType": "WORD", "Confidence": 92.26622772216797, "Text": "0900-0428", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15638358891010284, "Height": 0.021180393174290657, "Left": 0.4604615569114685, "Top": 0.4168721139431}, "Polygon": [{"X": 0.4604615569114685, "Y": 0.4206312596797943}, {"X": 0.6159924864768982, "Y": 0.4168721139431}, {"X": 0.6168451309204102, "Y": 0.4343430995941162}, {"X": 0.4615573287010193, "Y": 0.4380525052547455}]}, "Id": "bbcff072-baab-4253-9199-19300b65d4f7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07014358788728714, "Height": 0.017915531992912292, "Left": 0.2867220342159271, "Top": 0.45018455386161804}, "Polygon": [{"X": 0.2867220342159271, "Y": 0.45182108879089355}, {"X": 0.35568270087242126, "Y": 0.45018455386161804}, {"X": 0.35686561465263367, "Y": 0.4664842188358307}, {"X": 0.288005530834198, "Y": 0.46810010075569153}]}, "Id": "b427fac6-3006-4505-8124-0f876bd8634e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "09/04/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17514896392822266, "Height": 0.023946523666381836, "Left": 0.461292564868927, "Top": 0.4423026144504547}, "Polygon": [{"X": 0.461292564868927, "Y": 0.44644036889076233}, {"X": 0.6355046033859253, "Y": 0.4423026144504547}, {"X": 0.6364415287971497, "Y": 0.462174654006958}, {"X": 0.4625399112701416, "Y": 0.46624913811683655}]}, "Id": "9c6b0467-fadb-4a0c-ac70-6c654275ccf8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07113468647003174, "Height": 0.01896274834871292, "Left": 0.28756481409072876, "Top": 0.4740091860294342}, "Polygon": [{"X": 0.28756481409072876, "Y": 0.47564032673835754}, {"X": 0.3574400842189789, "Y": 0.4740091860294342}, {"X": 0.3586995005607605, "Y": 0.491362988948822}, {"X": 0.2889329195022583, "Y": 0.49297192692756653}]}, "Id": "be02a3c0-fc83-4122-96b9-12b590e7f5da"}, {"BlockType": "WORD", "Confidence": 99.08860778808594, "Text": "08:08", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08514676988124847, "Height": 0.018957875669002533, "Left": 0.46299076080322266, "Top": 0.4704781472682953}, "Polygon": [{"X": 0.46299076080322266, "Y": 0.4724425971508026}, {"X": 0.5471963882446289, "Y": 0.4704781472682953}, {"X": 0.5481375455856323, "Y": 0.4874977469444275}, {"X": 0.4640607535839081, "Y": 0.4894360303878784}]}, "Id": "e24ab6d9-c95c-440e-8a7c-5f29e44ea664"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03516669198870659, "Height": 0.017613589763641357, "Left": 0.566067099571228, "Top": 0.4694986939430237}, "Polygon": [{"X": 0.566067099571228, "Y": 0.4702991545200348}, {"X": 0.6003848910331726, "Y": 0.4694986939430237}, {"X": 0.6012337803840637, "Y": 0.4863223433494568}, {"X": 0.5669679641723633, "Y": 0.48711228370666504}]}, "Id": "9ea11095-c1a7-4af1-a02d-8e6472bd92a7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06928970664739609, "Height": 0.02189805917441845, "Left": 0.2929811477661133, "Top": 0.5254793167114258}, "Polygon": [{"X": 0.2929811477661133, "Y": 0.5270054936408997}, {"X": 0.36078986525535583, "Y": 0.5254793167114258}, {"X": 0.36227086186408997, "Y": 0.5458764433860779}, {"X": 0.294586718082428, "Y": 0.547377347946167}]}, "Id": "76bf3a83-dbed-4239-bbfa-287b6294043d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13658875226974487, "Height": 0.024872688576579094, "Left": 0.3799929916858673, "Top": 0.5210688710212708}, "Polygon": [{"X": 0.3799929916858673, "Y": 0.5241163372993469}, {"X": 0.5153025388717651, "Y": 0.5210688710212708}, {"X": 0.5165817141532898, "Y": 0.5429480075836182}, {"X": 0.38153916597366333, "Y": 0.5459415316581726}]}, "Id": "3dd6cc54-6acc-469a-8069-f6addeea8fd2"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08820993453264236, "Height": 0.020148668438196182, "Left": 0.5510919094085693, "Top": 0.5192146301269531}, "Polygon": [{"X": 0.5510919094085693, "Y": 0.5211806297302246}, {"X": 0.6384420394897461, "Y": 0.5192146301269531}, {"X": 0.6393018364906311, "Y": 0.5374261736869812}, {"X": 0.5520955920219421, "Y": 0.5393632650375366}]}, "Id": "025a7b09-7818-4751-9950-9d97a8b55912"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034514572471380234, "Height": 0.017023252323269844, "Left": 0.3122924864292145, "Top": 0.5506505966186523}, "Polygon": [{"X": 0.3122924864292145, "Y": 0.5513867735862732}, {"X": 0.34559839963912964, "Y": 0.5506505966186523}, {"X": 0.346807062625885, "Y": 0.5669475197792053}, {"X": 0.31355020403862, "Y": 0.5676738619804382}]}, "Id": "835423b9-6f73-4143-930a-3542ae364395"}, {"BlockType": "WORD", "Confidence": 99.5322494506836, "Text": "149.572", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11918476223945618, "Height": 0.01948815956711769, "Left": 0.39827269315719604, "Top": 0.5469198226928711}, "Polygon": [{"X": 0.39827269315719604, "Y": 0.5495323538780212}, {"X": 0.5164681673049927, "Y": 0.5469198226928711}, {"X": 0.5174574851989746, "Y": 0.5638318657875061}, {"X": 0.39944276213645935, "Y": 0.5664079785346985}]}, "Id": "6cb63771-534e-4cd9-a479-9bfa30c92c34"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "3.599", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08709605783224106, "Height": 0.01931963674724102, "Left": 0.5532156825065613, "Top": 0.5454564690589905}, "Polygon": [{"X": 0.5532156825065613, "Y": 0.5473616123199463}, {"X": 0.6394879817962646, "Y": 0.5454564690589905}, {"X": 0.6403117775917053, "Y": 0.5628982782363892}, {"X": 0.554175853729248, "Y": 0.5647761225700378}]}, "Id": "d850ce03-284d-44ff-a84e-dcfbd87ca2da"}, {"BlockType": "WORD", "Confidence": 99.294677734375, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03390832617878914, "Height": 0.01696549355983734, "Left": 0.31385740637779236, "Top": 0.5752984285354614}, "Polygon": [{"X": 0.31385740637779236, "Y": 0.576008141040802}, {"X": 0.3465580940246582, "Y": 0.5752984285354614}, {"X": 0.34776571393013, "Y": 0.591563880443573}, {"X": 0.3151131868362427, "Y": 0.59226393699646}]}, "Id": "b6c7f2e8-0e9c-4d69-b039-ea02d19c1d26"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "8.438", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08478030562400818, "Height": 0.018663089722394943, "Left": 0.4333781898021698, "Top": 0.5723743438720703}, "Polygon": [{"X": 0.4333781898021698, "Y": 0.5741918683052063}, {"X": 0.5171703696250916, "Y": 0.5723743438720703}, {"X": 0.518158495426178, "Y": 0.589245617389679}, {"X": 0.4344945549964905, "Y": 0.5910374522209167}]}, "Id": "a1180fb4-e247-4727-9171-6411af7a69bb"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "3.849", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0875019058585167, "Height": 0.01945466548204422, "Left": 0.5542088747024536, "Top": 0.5707525610923767}, "Polygon": [{"X": 0.5542088747024536, "Y": 0.5726310610771179}, {"X": 0.6408796310424805, "Y": 0.5707525610923767}, {"X": 0.6417107582092285, "Y": 0.5883564352989197}, {"X": 0.5551785826683044, "Y": 0.5902072787284851}]}, "Id": "5aaf6660-159d-4ede-bdac-0504ddae561e"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12095757573843002, "Height": 0.020266683772206306, "Left": 0.2957591414451599, "Top": 0.6235003471374512}, "Polygon": [{"X": 0.2957591414451599, "Y": 0.6260026097297668}, {"X": 0.4155022203922272, "Y": 0.6235003471374512}, {"X": 0.41671672463417053, "Y": 0.641303300857544}, {"X": 0.29716745018959045, "Y": 0.6437669992446899}]}, "Id": "c97294ae-99ab-4dae-bb56-0c6e2b389f4d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10595392435789108, "Height": 0.02124728448688984, "Left": 0.5384883880615234, "Top": 0.6216651201248169}, "Polygon": [{"X": 0.5384883880615234, "Y": 0.623855471611023}, {"X": 0.6435413360595703, "Y": 0.6216651201248169}, {"X": 0.6444423198699951, "Y": 0.6407582759857178}, {"X": 0.5395724177360535, "Y": 0.6429123878479004}]}, "Id": "229bbf75-93cd-4f43-bdf3-a6f5d1f79c6e"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06754791736602783, "Height": 0.019055282697081566, "Left": 0.2968209981918335, "Top": 0.6503100395202637}, "Polygon": [{"X": 0.2968209981918335, "Y": 0.6516668200492859}, {"X": 0.3630712628364563, "Y": 0.6503100395202637}, {"X": 0.36436891555786133, "Y": 0.6680297255516052}, {"X": 0.29822567105293274, "Y": 0.6693652868270874}]}, "Id": "9acc73a1-7647-4a6f-8ca7-67923c558242"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "538.31", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10277938097715378, "Height": 0.02156108245253563, "Left": 0.538869321346283, "Top": 0.6479736566543579}, "Polygon": [{"X": 0.538869321346283, "Y": 0.6500540375709534}, {"X": 0.6407204270362854, "Y": 0.6479736566543579}, {"X": 0.6416487097740173, "Y": 0.6674901843070984}, {"X": 0.5399793982505798, "Y": 0.6695347428321838}]}, "Id": "0c36bb0c-374e-4354-a687-013326829308"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "DEFBULK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11752024292945862, "Height": 0.022814631462097168, "Left": 0.2989172041416168, "Top": 0.67391037940979}, "Polygon": [{"X": 0.2989172041416168, "Y": 0.6762418746948242}, {"X": 0.41502973437309265, "Y": 0.67391037940979}, {"X": 0.41643744707107544, "Y": 0.6944364309310913}, {"X": 0.30054259300231934, "Y": 0.6967250108718872}]}, "Id": "4139f4ca-7848-4603-981c-c5665145fe5e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "32.48", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08876445144414902, "Height": 0.020797444507479668, "Left": 0.5578718781471252, "Top": 0.6749652624130249}, "Polygon": [{"X": 0.5578718781471252, "Y": 0.6767215132713318}, {"X": 0.6457358598709106, "Y": 0.6749652624130249}, {"X": 0.6466363072395325, "Y": 0.6940366625785828}, {"X": 0.5589261054992676, "Y": 0.6957627534866333}]}, "Id": "0bb91965-b39a-4ea7-a1aa-295754896a31"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08512358367443085, "Height": 0.023070454597473145, "Left": 0.3004342317581177, "Top": 0.7228091359138489}, "Polygon": [{"X": 0.3004342317581177, "Y": 0.7244219779968262}, {"X": 0.384015291929245, "Y": 0.7228091359138489}, {"X": 0.38555780053138733, "Y": 0.7442991137504578}, {"X": 0.30214154720306396, "Y": 0.745879590511322}]}, "Id": "87e0cba9-0b68-4b45-90a2-7964eaec2180"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "570.79", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10691002756357193, "Height": 0.021360550075769424, "Left": 0.5237612128257751, "Top": 0.7279072999954224}, "Polygon": [{"X": 0.5237612128257751, "Y": 0.7299351096153259}, {"X": 0.629723310470581, "Y": 0.7279072999954224}, {"X": 0.6306712627410889, "Y": 0.7472767233848572}, {"X": 0.5248982906341553, "Y": 0.7492678165435791}]}, "Id": "a84d24ea-1b7d-40f1-80b2-eaf0165ec483"}, {"BlockType": "WORD", "Confidence": 99.6534194946289, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1348254382610321, "Height": 0.02625359781086445, "Left": 0.30573830008506775, "Top": 0.7709740996360779}, "Polygon": [{"X": 0.30573830008506775, "Y": 0.7734386324882507}, {"X": 0.43896687030792236, "Y": 0.7709740996360779}, {"X": 0.44056373834609985, "Y": 0.7948200702667236}, {"X": 0.30762800574302673, "Y": 0.797227680683136}]}, "Id": "669afb6c-c29a-4cdb-8839-b57231cf9063"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07151112705469131, "Height": 0.021657897159457207, "Left": 0.45412424206733704, "Top": 0.7772495746612549}, "Polygon": [{"X": 0.45412424206733704, "Y": 0.7785412669181824}, {"X": 0.5244320034980774, "Y": 0.7772495746612549}, {"X": 0.5256353616714478, "Y": 0.7976414561271667}, {"X": 0.45546019077301025, "Y": 0.7989075183868408}]}, "Id": "bbce58e3-1145-46b4-8f2b-d5154a8a60bc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08991967886686325, "Height": 0.02151842601597309, "Left": 0.****************, "Top": 0.7798596024513245}, "Polygon": [{"X": 0.****************, "Y": 0.7814870476722717}, {"X": 0.6319386959075928, "Y": 0.7798596024513245}, {"X": 0.6329143643379211, "Y": 0.7997822165489197}, {"X": 0.****************, "Y": 0.8013780117034912}]}, "Id": "b05a254b-5d08-4721-9f5a-34feb2f1ca18"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08566982299089432, "Height": 0.022020848467946053, "Left": 0.30749499797821045, "Top": 0.7954903244972229}, "Polygon": [{"X": 0.30749499797821045, "Y": 0.7970156073570251}, {"X": 0.3916962146759033, "Y": 0.7954903244972229}, {"X": 0.39316484332084656, "Y": 0.8160167932510376}, {"X": 0.30912330746650696, "Y": 0.8175112009048462}]}, "Id": "e44b9764-285f-4b81-b95f-e1cc7344f79d"}, {"BlockType": "WORD", "Confidence": 99.0539321899414, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04944458603858948, "Height": 0.018490159884095192, "Left": 0.****************, "Top": 0.8000659942626953}, "Polygon": [{"X": 0.****************, "Y": 0.8009362816810608}, {"X": 0.*****************, "Y": 0.8000659942626953}, {"X": 0.***************, "Y": 0.8177011013031006}, {"X": 0.*****************, "Y": 0.8185561299324036}]}, "Id": "483e6d3b-57b8-466f-8d09-2681a04c25b9"}, {"BlockType": "WORD", "Confidence": 99.13444519042969, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06727472692728043, "Height": 0.019055193290114403, "Left": 0.3103656470775604, "Top": 0.8451370000839233}, "Polygon": [{"X": 0.3103656470775604, "Y": 0.8462790250778198}, {"X": 0.3763252794742584, "Y": 0.8451370000839233}, {"X": 0.37764039635658264, "Y": 0.8630712628364563}, {"X": 0.3117905557155609, "Y": 0.8641921877861023}]}, "Id": "a089571b-1c63-4cd8-aff0-258e1c138a78"}, {"BlockType": "WORD", "Confidence": 68.64955139160156, "Text": "XXXXXXXXXXXXXX1591", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.303211510181427, "Height": 0.0338040255010128, "Left": 0.30938008427619934, "Top": 0.8178847432136536}, "Polygon": [{"X": 0.30938008427619934, "Y": 0.823222279548645}, {"X": 0.611129105091095, "Y": 0.8178847432136536}, {"X": 0.612591564655304, "Y": 0.8465047478675842}, {"X": 0.3116421699523926, "Y": 0.8516887426376343}]}, "Id": "599fff97-81d4-446c-bfdd-111ee1862a56"}, {"BlockType": "WORD", "Confidence": 99.71440124511719, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019831467419862747, "Height": 0.01619359292089939, "Left": 0.39279109239578247, "Top": 0.8475276827812195}, "Polygon": [{"X": 0.39279109239578247, "Y": 0.8478508591651917}, {"X": 0.411510705947876, "Y": 0.8475276827812195}, {"X": 0.4126225411891937, "Y": 0.8634033799171448}, {"X": 0.3939305245876312, "Y": 0.8637212514877319}]}, "Id": "1070d44e-6b59-487e-86b0-240bc59f2b15"}, {"BlockType": "WORD", "Confidence": 99.27435302734375, "Text": "P43347", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10632408410310745, "Height": 0.02323223650455475, "Left": 0.5468494892120361, "Top": 0.8510990738868713}, "Polygon": [{"X": 0.5468494892120361, "Y": 0.8529040813446045}, {"X": 0.6521560549736023, "Y": 0.8510990738868713}, {"X": 0.653173565864563, "Y": 0.8725664615631104}, {"X": 0.5480777025222778, "Y": 0.8743313550949097}]}, "Id": "ca8797c1-7354-4df1-9df2-9e7c487d0417"}, {"BlockType": "WORD", "Confidence": 98.59513854980469, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14789055287837982, "Height": 0.02358957752585411, "Left": 0.31098857522010803, "Top": 0.8678485155105591}, "Polygon": [{"X": 0.31098857522010803, "Y": 0.8703276515007019}, {"X": 0.45748576521873474, "Y": 0.8678485155105591}, {"X": 0.45887911319732666, "Y": 0.8890140056610107}, {"X": 0.31267037987709045, "Y": 0.8914380669593811}]}, "Id": "5f24e4fb-7328-48f4-8915-5a9a80df1b75"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1389981359243393, "Height": 0.02288604900240898, "Left": 0.4798678159713745, "Top": 0.8724357485771179}, "Polygon": [{"X": 0.4798678159713745, "Y": 0.874754011631012}, {"X": 0.6178199648857117, "Y": 0.8724357485771179}, {"X": 0.618865966796875, "Y": 0.8930538892745972}, {"X": 0.48117923736572266, "Y": 0.895321786403656}]}, "Id": "4e76d18f-48c0-4784-a73a-6f7db7bc3cc3"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06814540177583694, "Height": 0.01866125501692295, "Left": 0.3285296857357025, "Top": 0.8950735926628113}, "Polygon": [{"X": 0.3285296857357025, "Y": 0.8961769938468933}, {"X": 0.3954117000102997, "Y": 0.8950735926628113}, {"X": 0.39667508006095886, "Y": 0.9126522541046143}, {"X": 0.3299027681350708, "Y": 0.9137348532676697}]}, "Id": "2be40a93-c4f2-44a6-95ae-22243cfb987a"}, {"BlockType": "WORD", "Confidence": 99.40290832519531, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08096472173929214, "Height": 0.018532928079366684, "Left": 0.41358914971351624, "Top": 0.8953601717948914}, "Polygon": [{"X": 0.41358914971351624, "Y": 0.8966756463050842}, {"X": 0.493474543094635, "Y": 0.8953601717948914}, {"X": 0.4945538640022278, "Y": 0.9126020073890686}, {"X": 0.4147971570491791, "Y": 0.9138931035995483}]}, "Id": "9e12ecf4-f5c7-477d-9e56-a05e28bedac2"}, {"BlockType": "WORD", "Confidence": 98.14035034179688, "Text": "P43347", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10328538715839386, "Height": 0.015946967527270317, "Left": 0.5159591436386108, "Top": 0.8958176970481873}, "Polygon": [{"X": 0.5159591436386108, "Y": 0.8975024223327637}, {"X": 0.6185190081596375, "Y": 0.8958176970481873}, {"X": 0.6192445755004883, "Y": 0.91010582447052}, {"X": 0.5168218016624451, "Y": 0.9117646217346191}]}, "Id": "83bf143c-894f-40a0-b274-c04a83ae0785"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "ed7bc0cb-1c5b-4019-b4d6-ff8e755a9eca", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "ed7bc0cb-1c5b-4019-b4d6-ff8e755a9eca", "content-type": "application/x-amz-json-1.1", "content-length": "40723", "date": "Thu, 18 Sep 2025 08:51:08 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "479934d8-d597-444d-8b06-c929449b5c6b", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:12 GMT", "content-type": "application/json", "content-length": "971", "connection": "keep-alive", "x-amzn-requestid": "479934d8-d597-444d-8b06-c929449b5c6b"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have page 1 content. This looks like a fuel receipt (fuel purchase). Keywords: Pump Quantity Price, product, amount, etc. It has \"Invoice#\". But likely a fuel receipt. It is not a commercial invoice, not a bill of lading, not a POD. Fuel receipt fits: Fuel, Pump #, gallons, price. The text has \"Pump Quantity Price\" which indicates fuel. The doc type should be \"fuel_receipt\".\n\nWe need to return a function call to classify_logistics_doc_type with documents array containing one object: page_no: 1, doc_type: \"fuel_receipt\".\n\nWe should use the tool."}}}, {"toolUse": {"toolUseId": "tooluse_x-jOp-MDSh6spt8XrV7cFA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2260, "outputTokens": 173, "totalTokens": 2433}, "metrics": {"latencyMs": 783}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/508cb8c8_9_fuel_receipt_10.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP #885\n301 ELDERBERRY RD\nABBOTSFORD, WI\nInvoice# 0090428\nTrans # 0900-0428\nDate\n09/04/2025\nTime\n08:08 AM\nPump Quantity Price\n02\n149.572 3.599\n02\n8.438 3.849\nProduct\nAmount\nDSL2\n538.31\nDEFBULK\n32.48\nTOTAL\n570.79\nNBS/KWIK TRIP FLEET\nCARD# ICR\nAUTH XXXXXXXXXXXXXX1591 #\nP43347\nRESPONSE: APPROVED\nAUTH REF#: P43347\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}