2025-09-24 18:59:05,778 - INFO - Logging initialized. Log file: logs/test_classification_20250924_185905.log
2025-09-24 18:59:05,779 - INFO - 📁 Found 12 files to process
2025-09-24 18:59:05,779 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:59:05,779 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 18:59:05,779 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 18:59:05,779 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 18:59:05,779 - INFO - ⬆️ [18:59:05] Uploading: BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:07,345 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf -> s3://document-extraction-logistically/temp/5ebfe4b8_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:07,345 - INFO - 🔍 [18:59:07] Starting classification: BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:07,345 - INFO - ⬆️ [18:59:07] Uploading: E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:07,351 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:07,372 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:07,383 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ebfe4b8_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:07,383 - INFO - Processing PDF from S3...
2025-09-24 18:59:07,384 - INFO - Downloading PDF from S3 to /tmp/tmp0y0b7055/5ebfe4b8_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:08,649 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:59:08,650 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:08,651 - INFO - Splitting PDF 5ebfe4b8_BJYFRYT0V8O045HGDXJ6 into 1 pages
2025-09-24 18:59:08,658 - INFO - Split PDF into 1 pages
2025-09-24 18:59:08,658 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:08,658 - INFO - Expected pages: [1]
2025-09-24 18:59:09,602 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg -> s3://document-extraction-logistically/temp/4bc051cd_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:09,602 - INFO - 🔍 [18:59:09] Starting classification: E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:09,602 - INFO - ⬆️ [18:59:09] Uploading: EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:09,603 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:09,614 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:09,618 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4bc051cd_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:09,618 - INFO - Processing image from S3...
2025-09-24 18:59:10,209 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf -> s3://document-extraction-logistically/temp/dca3178a_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:10,209 - INFO - 🔍 [18:59:10] Starting classification: EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:10,210 - INFO - ⬆️ [18:59:10] Uploading: GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:10,211 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:10,226 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:10,230 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dca3178a_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:10,230 - INFO - Processing PDF from S3...
2025-09-24 18:59:10,231 - INFO - Downloading PDF from S3 to /tmp/tmptj862acu/dca3178a_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:10,830 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf -> s3://document-extraction-logistically/temp/0857f006_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:10,830 - INFO - 🔍 [18:59:10] Starting classification: GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:10,831 - INFO - ⬆️ [18:59:10] Uploading: HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:10,832 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:10,848 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:10,852 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0857f006_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:10,852 - INFO - Processing PDF from S3...
2025-09-24 18:59:10,852 - INFO - Downloading PDF from S3 to /tmp/tmp1gfo5fou/0857f006_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:11,436 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:59:11,436 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:11,437 - INFO - Splitting PDF dca3178a_EKMN29XJBZVFPLOYTKU7 into 1 pages
2025-09-24 18:59:11,438 - INFO - Split PDF into 1 pages
2025-09-24 18:59:11,438 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:11,438 - INFO - Expected pages: [1]
2025-09-24 18:59:11,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg -> s3://document-extraction-logistically/temp/eb5905a3_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:11,476 - INFO - 🔍 [18:59:11] Starting classification: HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:11,476 - INFO - ⬆️ [18:59:11] Uploading: OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:11,478 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:11,498 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:11,502 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb5905a3_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:11,503 - INFO - Processing image from S3...
2025-09-24 18:59:12,100 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf -> s3://document-extraction-logistically/temp/cff4ef22_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:12,101 - INFO - 🔍 [18:59:12] Starting classification: OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:12,101 - INFO - ⬆️ [18:59:12] Uploading: T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:12,103 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:12,119 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:12,126 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cff4ef22_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:12,127 - INFO - Processing PDF from S3...
2025-09-24 18:59:12,127 - INFO - Downloading PDF from S3 to /tmp/tmpobt79suc/cff4ef22_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:12,384 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:59:12,385 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:12,386 - INFO - Splitting PDF 0857f006_GHXKMFASS6CGN362R5BW into 1 pages
2025-09-24 18:59:12,394 - INFO - Split PDF into 1 pages
2025-09-24 18:59:12,395 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:12,395 - INFO - Expected pages: [1]
2025-09-24 18:59:12,719 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png -> s3://document-extraction-logistically/temp/4c3f470b_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:12,719 - INFO - 🔍 [18:59:12] Starting classification: T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:12,719 - INFO - ⬆️ [18:59:12] Uploading: UGM225037492OGWQHUHR.png
2025-09-24 18:59:12,720 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:12,727 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:12,729 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4c3f470b_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:12,730 - INFO - Processing image from S3...
2025-09-24 18:59:12,859 - INFO - S3 Image temp/4bc051cd_E88AUSUP8065SEI9GD5Q.jpg: Extracted 516 characters, 26 lines
2025-09-24 18:59:12,859 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:12,859 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:13,249 - INFO - Page 1: Extracted 395 characters, 28 lines from 5ebfe4b8_BJYFRYT0V8O045HGDXJ6_7118dd6f_page_001.pdf
2025-09-24 18:59:13,249 - INFO - Successfully processed page 1
2025-09-24 18:59:13,250 - INFO - Combined 1 pages into final text
2025-09-24 18:59:13,250 - INFO - Text validation for 5ebfe4b8_BJYFRYT0V8O045HGDXJ6: 412 characters, 1 pages
2025-09-24 18:59:13,250 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:13,250 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:13,312 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png -> s3://document-extraction-logistically/temp/d56648b2_UGM225037492OGWQHUHR.png
2025-09-24 18:59:13,312 - INFO - 🔍 [18:59:13] Starting classification: UGM225037492OGWQHUHR.png
2025-09-24 18:59:13,313 - INFO - ⬆️ [18:59:13] Uploading: VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:13,313 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:13,327 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:13,334 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d56648b2_UGM225037492OGWQHUHR.png
2025-09-24 18:59:13,334 - INFO - Processing image from S3...
2025-09-24 18:59:13,833 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:59:13,834 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:13,835 - INFO - Splitting PDF cff4ef22_OO7629V25ZJ22FHL0PAA into 2 pages
2025-09-24 18:59:13,849 - INFO - Split PDF into 2 pages
2025-09-24 18:59:13,849 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:13,849 - INFO - Expected pages: [1, 2]
2025-09-24 18:59:13,971 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf -> s3://document-extraction-logistically/temp/f36fc34f_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:13,971 - INFO - 🔍 [18:59:13] Starting classification: VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:13,972 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:13,976 - INFO - ⬆️ [18:59:13] Uploading: WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:13,990 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:13,996 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f36fc34f_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:13,996 - INFO - Processing PDF from S3...
2025-09-24 18:59:13,996 - INFO - Downloading PDF from S3 to /tmp/tmpdgs8_5h8/f36fc34f_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:14,595 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf -> s3://document-extraction-logistically/temp/08ca72dc_WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:14,596 - INFO - 🔍 [18:59:14] Starting classification: WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:14,596 - INFO - ⬆️ [18:59:14] Uploading: YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:14,598 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:14,613 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:14,617 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/08ca72dc_WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:14,617 - INFO - Processing PDF from S3...
2025-09-24 18:59:14,618 - INFO - Downloading PDF from S3 to /tmp/tmp5wyxgrpo/08ca72dc_WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:14,935 - INFO - Page 1: Extracted 525 characters, 25 lines from dca3178a_EKMN29XJBZVFPLOYTKU7_0955d459_page_001.pdf
2025-09-24 18:59:14,935 - INFO - Successfully processed page 1
2025-09-24 18:59:14,935 - INFO - Combined 1 pages into final text
2025-09-24 18:59:14,936 - INFO - Text validation for dca3178a_EKMN29XJBZVFPLOYTKU7: 542 characters, 1 pages
2025-09-24 18:59:14,936 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:14,936 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:15,196 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf -> s3://document-extraction-logistically/temp/764cee2a_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:15,197 - INFO - 🔍 [18:59:15] Starting classification: YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:15,198 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:15,198 - INFO - ⬆️ [18:59:15] Uploading: ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:15,208 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:15,216 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/764cee2a_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:15,217 - INFO - Processing PDF from S3...
2025-09-24 18:59:15,218 - INFO - Downloading PDF from S3 to /tmp/tmpppqon486/764cee2a_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:15,491 - INFO - S3 Image temp/d56648b2_UGM225037492OGWQHUHR.png: Extracted 223 characters, 18 lines
2025-09-24 18:59:15,491 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:15,491 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:15,519 - INFO - S3 Image temp/4c3f470b_T5U48WX7H3FNSZPBDBLZ.png: Extracted 199 characters, 25 lines
2025-09-24 18:59:15,520 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:15,520 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:15,609 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '283d08cd-071b-487d-b279-9bf0c3e981a3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:15 GMT', 'content-type': 'application/json', 'content-length': '1199', 'connection': 'keep-alive', 'x-amzn-requestid': '283d08cd-071b-487d-b279-9bf0c3e981a3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It seems to be a Gate Receipt (Ingate or Outgate?). Title: "J1 GATE RECEIPT" and text includes "Out-Gate". It\'s an "Ingate" or "Outgate" document. The header says "J1 GATE RECEIPT" and mentions "Out-Gate" under "Empty & Driver". The page includes "Equipment", "Booking #", "Transportation Company". This is likely an Outgate. But we need to see if it\'s Ingate or Outgate. The header says "J1 GATE RECEIPT". The details mention "Out-Gate". It could be an Outgate receipt. The document type "outgate" (Outgate Document). The structure: Records of vehicle/container exiting a facility. So classify as "outgate".\n\nWe must match the doc_type to one of the enum values. "outgate" is the enum. So output:\n\n{\n "documents": [\n   { "page_no": 1, "doc_type": "outgate" }\n ]\n}\n\nWe must use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2731, 'outputTokens': 246, 'totalTokens': 2977}, 'metrics': {'latencyMs': 1267}}
2025-09-24 18:59:15,610 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ebfe4b8_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:15,723 - INFO - S3 Image temp/eb5905a3_HZNQ971O2V9MGG5MU1T3.jpeg: Extracted 655 characters, 33 lines
2025-09-24 18:59:15,723 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:15,723 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:15,799 - INFO - Page 1: Extracted 404 characters, 28 lines from 0857f006_GHXKMFASS6CGN362R5BW_e795be52_page_001.pdf
2025-09-24 18:59:15,799 - INFO - Successfully processed page 1
2025-09-24 18:59:15,800 - INFO - Combined 1 pages into final text
2025-09-24 18:59:15,800 - INFO - Text validation for 0857f006_GHXKMFASS6CGN362R5BW: 421 characters, 1 pages
2025-09-24 18:59:15,800 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:15,800 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:15,846 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf -> s3://document-extraction-logistically/temp/4faeaf6e_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:15,846 - INFO - 🔍 [18:59:15] Starting classification: ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:15,846 - INFO - Initializing TextractProcessor...
2025-09-24 18:59:15,854 - INFO - Initializing BedrockProcessor...
2025-09-24 18:59:15,856 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4faeaf6e_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:15,857 - INFO - Processing PDF from S3...
2025-09-24 18:59:15,858 - INFO - Downloading PDF from S3 to /tmp/tmp36j8ksdl/4faeaf6e_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:15,862 - INFO - 

BJYFRYT0V8O045HGDXJ6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:15,863 - INFO - 

✓ Saved result: output/run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 18:59:16,158 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ebfe4b8_BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:16,583 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b1b32aa0-3743-4eae-9d42-7838c9f2b6d6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:16 GMT', 'content-type': 'application/json', 'content-length': '567', 'connection': 'keep-alive', 'x-amzn-requestid': 'b1b32aa0-3743-4eae-9d42-7838c9f2b6d6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. It\'s an Outgate document. The header says "OUT-GATE" and "Equipment Inspection Record". Contains "Outgate:" and other details. So doc_type should be outgate. No continuation indicators. Output format: JSON with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}] }'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2788, 'outputTokens': 73, 'totalTokens': 2861}, 'metrics': {'latencyMs': 528}}
2025-09-24 18:59:16,583 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dca3178a_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:16,604 - INFO - 

EKMN29XJBZVFPLOYTKU7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:16,604 - INFO - 

✓ Saved result: output/run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 18:59:16,896 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dca3178a_EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:17,558 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:59:17,559 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:17,560 - INFO - Splitting PDF f36fc34f_VY0AAD98JWYCZUAHKROB into 1 pages
2025-09-24 18:59:17,562 - INFO - Split PDF into 1 pages
2025-09-24 18:59:17,562 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:17,563 - INFO - Expected pages: [1]
2025-09-24 18:59:17,573 - INFO - Page 2: Extracted 457 characters, 4 lines from cff4ef22_OO7629V25ZJ22FHL0PAA_ba9bc306_page_002.pdf
2025-09-24 18:59:17,573 - INFO - Successfully processed page 2
2025-09-24 18:59:17,651 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4698b8a3-4c60-48c6-90f7-32cf29373df4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:17 GMT', 'content-type': 'application/json', 'content-length': '714', 'connection': 'keep-alive', 'x-amzn-requestid': '4698b8a3-4c60-48c6-90f7-32cf29373df4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF page content. Determine doc type. The text includes "Outgate:" and "Equipment Inspection Record", "Interchange Receipt". "Outgate: LOGPARCHI IL 07/07/2025 2315". This looks like an outgate document. Outgate record. The doc type is outgate. Ensure no other doc types. Keywords: Outgate, Release ID, etc. So classify as outgate. Output JSON with page_no 1, doc_type "outgate".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2817, 'outputTokens': 122, 'totalTokens': 2939}, 'metrics': {'latencyMs': 797}}
2025-09-24 18:59:17,651 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb5905a3_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:17,669 - INFO - 

HZNQ971O2V9MGG5MU1T3.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:17,670 - INFO - 

✓ Saved result: output/run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 18:59:17,763 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'afc9bf13-2d46-4972-9583-d120177a7905', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:17 GMT', 'content-type': 'application/json', 'content-length': '1109', 'connection': 'keep-alive', 'x-amzn-requestid': 'afc9bf13-2d46-4972-9583-d120177a7905'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page input: page1 content. We need to decide doc_type. The content appears to be some kind of container arrival note. "Georgia Ports Authority, Savannah Deliver Import Container". Contains "Truck ID", "Container", "Booking", "Vessel", "Temp", "Seals". This looks like an ingate document? Ingate Document: Record of vehicle/container entering a facility. Keywords: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In. This has "Truck ID" and "Container" and "Seals". Likely ingate.\n\nAlternatively could be an outgate? But it\'s arrival. So ingate. There\'s no "Outgate" markers. So assign doc_type: ingate.\n\nThus output JSON with documents array single entry page_no 1, doc_type "ingate".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2673, 'outputTokens': 200, 'totalTokens': 2873}, 'metrics': {'latencyMs': 1059}}
2025-09-24 18:59:17,764 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d56648b2_UGM225037492OGWQHUHR.png
2025-09-24 18:59:17,838 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 18:59:17,838 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:17,839 - INFO - Splitting PDF 08ca72dc_WRP4157FNOQ52T6661B4 into 1 pages
2025-09-24 18:59:17,843 - INFO - Split PDF into 1 pages
2025-09-24 18:59:17,843 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:17,843 - INFO - Expected pages: [1]
2025-09-24 18:59:17,862 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:59:17,863 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:17,866 - INFO - Splitting PDF 4faeaf6e_ZAJMVPYOM22ETECQRW3L into 1 pages
2025-09-24 18:59:17,869 - INFO - Split PDF into 1 pages
2025-09-24 18:59:17,869 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:17,869 - INFO - Expected pages: [1]
2025-09-24 18:59:17,968 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb5905a3_HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:17,977 - INFO - 

UGM225037492OGWQHUHR.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:59:17,977 - INFO - 

✓ Saved result: output/run1_UGM225037492OGWQHUHR.json
2025-09-24 18:59:18,131 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e04ad54f-4506-4a74-be5a-74d05fab5270', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:17 GMT', 'content-type': 'application/json', 'content-length': '1489', 'connection': 'keep-alive', 'x-amzn-requestid': 'e04ad54f-4506-4a74-be5a-74d05fab5270'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1 content. We need to decide document type. The content looks like a gate log: contains Time In, Time Out, Vessel/Voyage, Container, Chassis, Genset, etc. That\'s typical of a gate log or ingate/outgate log. The header says "License Activity Container Chassis Genset Line Cat Weight (MT) Sz/Tp Vessel/Voyage Date Time In Time Out". This looks like an ingate/outgate log. There\'s no explicit header like "Ingate", but the fields "Time In" and "Time Out" suggest gate log. The relevant categories: ingate, outgate, log. Which one? Since it includes both Time In and Time Out, could be a combined gate log? Could be "log" type. According to definition, "log" includes driver log, logbook, activity record, etc. Ingate/outgate logs are separate categories: ingate: record of vehicle/container entering. outgate: exit. But this page contains both. It might be a "Combined Carier Documents"? No, it\'s a log. So doc_type: log. Also check if any other type: The page is a gate log. So yes log. We\'ll output one document object with page_no 1.\n\nReturn JSON.'}}}, {'text': '{\n    "documents": [\n        {\n            "page_no": 1,\n            "doc_type": "log"\n        }\n    ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2678, 'outputTokens': 302, 'totalTokens': 2980}, 'metrics': {'latencyMs': 1493}}
2025-09-24 18:59:18,132 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4c3f470b_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:18,274 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d56648b2_UGM225037492OGWQHUHR.png
2025-09-24 18:59:18,285 - INFO - 

T5U48WX7H3FNSZPBDBLZ.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 18:59:18,285 - INFO - 

✓ Saved result: output/run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 18:59:18,415 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0cc3fbb5-2147-420b-b880-10e5e43388b3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:18 GMT', 'content-type': 'application/json', 'content-length': '1276', 'connection': 'keep-alive', 'x-amzn-requestid': '0cc3fbb5-2147-420b-b880-10e5e43388b3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content. We need to classify the type. Let\'s read it.\n\nTitle: "J1 GATE RECEIPT" and "J1 RECEIPT DETAILS". It\'s a gate receipt, likely an ingate or outgate? The text says "Out-Gate" in the details. It\'s a gate receipt for a vehicle entering or exiting. The phrase "Out-Gate" suggests the vehicle is leaving (outgate). We see "Equipment Empty & Driver Out-Gate". This looks like an outgate receipt. The header "J1 GATE RECEIPT" likely indicates a gate receipt. The categories: ingate, outgate. We need to decide which. The page includes "Out-Gate" field, so it\'s an outgate. So doc_type: outgate.\n\nWe need to output JSON with documents array. Page number 1. Output format exactly.\n\nThus:\n\n{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "outgate"\n    }\n  ]\n}\n\nCheck if there are any continuation words. No. So that\'s it.'}}}, {'text': '{\n    "documents": [\n        {\n            "page_no": 1,\n            "doc_type": "outgate"\n        }\n    ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2735, 'outputTokens': 260, 'totalTokens': 2995}, 'metrics': {'latencyMs': 1196}}
2025-09-24 18:59:18,415 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0857f006_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:18,581 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4c3f470b_T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:18,591 - INFO - 

GHXKMFASS6CGN362R5BW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:18,591 - INFO - 

✓ Saved result: output/run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 18:59:18,708 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e03b0992-e5ed-4ee7-ad05-994468a546ef', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:18 GMT', 'content-type': 'application/json', 'content-length': '4635', 'connection': 'keep-alive', 'x-amzn-requestid': 'e03b0992-e5ed-4ee7-ad05-994468a546ef'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to identify doc_type.\n\nWe read the page. It contains a container info, with details like container number, size, vessel, etc. This looks like an Export Import Declaration? The header says "FULL-OUT" and "** IMPORT-OUT EIR **". EIR could be "Export Import Report"? It seems like a driver\'s log or gate-out. But the content includes container, vehicle, driver license, etc. This might be an "ingate" or "outgate" document. Let\'s analyze.\n\nWe have "Trucking Co: (CQBH) CARDINAL LOGISTICS 1 LLC", "Truck License: 86266RP", "Truck Tag: 86266RP", "SEALINTACT", "CONTAINER GROSS WEIGHT(LBS): 48281 Exit with OWN chassis". The heading: "FULL-OUT" suggests that the container is being fully out of the terminal. "IMPORT-OUT EIR". Possibly a "Export Import Report" which is a customs document? But the keywords for customs_doc include "Customs, Declaration, Import/Export Declaration, Customs Broker". This page has "IMPORT-OUT EIR" which might be a customs document. But the description for customs_doc says general customs paperwork. This could be a EIR (Export Import Report). In shipping, EIR is a "Export Import Report" used by U.S. and Canadian customs. So likely customs_doc.\n\nBut we should check if any other doc type fits: "ingate" vs "outgate". "outgate" includes keywords: Outgate, Departed, Gate Out, Time Out, Release, Exit Time. This page has "EXIT with OWN chassis" but no explicit "Outgate" word. But it is about container exit. Also includes a "Driver License" etc. Could be a "ingate/outgate" document for truck. The term "ingate" and "outgate" used for container port operations. The content includes "Container: TLLU6009676" etc. It seems like a record of container exit. So maybe outgate. But the doc type "outgate" defined as record of vehicle/container exiting a facility (gate-out/release). Keywords include Outgate, Departed, Gate Out, Time Out, Release, Exit Time. Our page does not have those explicit words, but has "FULL-OUT" and "EXIT with OWN chassis". "FULL-OUT" might be a flag that container is full out of the terminal. Potentially this is a "ingate" or "outgate" doc. Which one? It may be an "ingate" if the container is entering the terminal. But the content says "Exit with OWN chassis". So it\'s exit. So outgate.\n\nBut also "EIR" might be a customs doc.\n\nLet\'s examine the difference: customs_doc is for customs paperwork. This page looks like a driver log for terminal. It may not be a customs doc. But "IMPORT-OUT EIR" might be a customs report. However, the EIR is a "Export Import Report" that is a customs document used for container movement. But the page includes details of container, vessel, driver, truck, etc. It could be a "combined_carrier_documents" but only one header. "combined_carrier_documents" would show multiple distinct doc headers on same page. Not the case.\n\nThus the best classification likely "ingate" or "outgate". Which one? Let\'s search for keywords: "Exit with OWN chassis" indicates exit. So outgate. The page has no "Outgate" word, but "Full-out" can be considered. But the doc type "outgate" requires "Outgate, Departed, Gate Out, Time Out, Release, Exit Time". It has "Exit". So maybe qualifies. Also it includes truck license, driver license, etc. So could be outgate.\n\nAlternatively, "ingate" doc type includes "Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In". No such keywords. So outgate is more likely.\n\nThus we classify page1 as "outgate".\n\nBut we should double-check whether "outgate" is correct. The page includes "CONTAINER GROSS WEIGHT(LBS): 48281". That might be used for weight records. But weight ticket keywords include Gross, Tare, Net, Weighed At, etc. There is "CONTAINER GROSS WEIGHT(LBS): 48281" but weight ticket might include "Ticket no." etc. No. So not scale_ticket.\n\nThus choose outgate.\n\nWe also need to consider if the page is a continuation. Only one page. So no previous page. Not a continuation.\n\nThus output:\n\n{\n "documents": [\n   {"page_no": 1, "doc_type": "outgate"}\n ]\n}\n\nCheck that doc_type is exactly one of allowed strings. "outgate" is allowed.\n\nThus produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2780, 'outputTokens': 1052, 'totalTokens': 3832}, 'metrics': {'latencyMs': 4607}}
2025-09-24 18:59:18,708 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4bc051cd_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:18,884 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0857f006_GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:18,901 - INFO - 

E88AUSUP8065SEI9GD5Q.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:18,901 - INFO - 

✓ Saved result: output/run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 18:59:18,911 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:59:18,912 - INFO - Splitting PDF into individual pages...
2025-09-24 18:59:18,913 - INFO - Splitting PDF 764cee2a_YASUN3YXXTIFXRHDIKNF into 1 pages
2025-09-24 18:59:18,916 - INFO - Split PDF into 1 pages
2025-09-24 18:59:18,917 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:59:18,917 - INFO - Expected pages: [1]
2025-09-24 18:59:18,934 - INFO - Page 1: Extracted 669 characters, 29 lines from cff4ef22_OO7629V25ZJ22FHL0PAA_ba9bc306_page_001.pdf
2025-09-24 18:59:18,935 - INFO - Successfully processed page 1
2025-09-24 18:59:18,935 - INFO - Combined 2 pages into final text
2025-09-24 18:59:18,935 - INFO - Text validation for cff4ef22_OO7629V25ZJ22FHL0PAA: 1162 characters, 2 pages
2025-09-24 18:59:18,936 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:18,936 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:19,190 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4bc051cd_E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:21,598 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ab8fb096-9eb1-4eae-9497-fa856c0c677c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:21 GMT', 'content-type': 'application/json', 'content-length': '1607', 'connection': 'keep-alive', 'x-amzn-requestid': 'ab8fb096-9eb1-4eae-9497-fa856c0c677c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Page1 looks like an ingate or outgate receipt? It has "DEPARTED" and "OUTGATE"? It says "DEPARTED: 06/11/2025 13:20 ZS042 GLOBAL 4 IL". There\'s driver info, seal number, load weight. It says "THIS TERMINAL IS EQUIPPED WITH AN AUTOMATED GATE SYSTEM. DAMAGE IS NOT REPORTED ON INGATE OR OUTGATE RECEIPTS." That suggests the page is an ingate or outgate? But the header "DEPARTED" suggests it\'s outgate? However, it also says "INGATE OR OUTGATE" but not clear which. The page is an email from UPGo, likely an in-gate or out-gate receipt. The content: "DEPARTED" indicates departure from facility. That is outgate. The type is "outgate" or "ingate"? The doc has "DEPARTED" indicating leaving. So it\'s outgate. The doc type "outgate".\n\nPage2 is just email confidentiality notice. It\'s not a document. Should it be "other" or same as previous? It is continuation? The page content is not a logistics document, no keywords. It\'s just email footnote. We should classify as "other". But check continuation rule: The page starts with "This email and any attachments may contain..." It\'s not a continuation of previous page. So we can classify as "other".\n\nThus output two pages: page 1 outgate, page 2 other.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 347, 'totalTokens': 3275}, 'metrics': {'latencyMs': 1522}}
2025-09-24 18:59:21,598 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cff4ef22_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:21,621 - INFO - 

OO7629V25ZJ22FHL0PAA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:59:21,621 - INFO - 

✓ Saved result: output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 18:59:21,822 - INFO - Page 1: Extracted 840 characters, 41 lines from f36fc34f_VY0AAD98JWYCZUAHKROB_d905df1f_page_001.pdf
2025-09-24 18:59:21,822 - INFO - Successfully processed page 1
2025-09-24 18:59:21,823 - INFO - Combined 1 pages into final text
2025-09-24 18:59:21,823 - INFO - Text validation for f36fc34f_VY0AAD98JWYCZUAHKROB: 857 characters, 1 pages
2025-09-24 18:59:21,823 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:21,823 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:21,960 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cff4ef22_OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:22,404 - INFO - Page 1: Extracted 486 characters, 22 lines from 764cee2a_YASUN3YXXTIFXRHDIKNF_bb3315ac_page_001.pdf
2025-09-24 18:59:22,405 - INFO - Successfully processed page 1
2025-09-24 18:59:22,405 - INFO - Combined 1 pages into final text
2025-09-24 18:59:22,405 - INFO - Text validation for 764cee2a_YASUN3YXXTIFXRHDIKNF: 503 characters, 1 pages
2025-09-24 18:59:22,406 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:22,406 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:23,079 - INFO - Page 1: Extracted 1557 characters, 33 lines from 4faeaf6e_ZAJMVPYOM22ETECQRW3L_c7fcb1c5_page_001.pdf
2025-09-24 18:59:23,080 - INFO - Successfully processed page 1
2025-09-24 18:59:23,080 - INFO - Combined 1 pages into final text
2025-09-24 18:59:23,080 - INFO - Text validation for 4faeaf6e_ZAJMVPYOM22ETECQRW3L: 1574 characters, 1 pages
2025-09-24 18:59:23,081 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:23,081 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:24,228 - INFO - Page 1: Extracted 1574 characters, 82 lines from 08ca72dc_WRP4157FNOQ52T6661B4_590d1b41_page_001.pdf
2025-09-24 18:59:24,229 - INFO - Successfully processed page 1
2025-09-24 18:59:24,229 - INFO - Combined 1 pages into final text
2025-09-24 18:59:24,229 - INFO - Text validation for 08ca72dc_WRP4157FNOQ52T6661B4: 1591 characters, 1 pages
2025-09-24 18:59:24,230 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:59:24,230 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:59:25,201 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'aaa76e24-2dc8-4a18-9e53-084856c7d43f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:25 GMT', 'content-type': 'application/json', 'content-length': '1629', 'connection': 'keep-alive', 'x-amzn-requestid': 'aaa76e24-2dc8-4a18-9e53-084856c7d43f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. Let\'s examine keywords.\n\nPage text: PNCT, PICKUP 566367, MSC: LOAD OUT, SXIY: SKYLINE EXPRESS, ... CONTAINER: FCIU9733694, SIZE/TYPE: 40 GP 96, CARD ID: 231466, CHASSIS, etc. This looks like a container pickup or load out card, maybe a gate card for container release. Could be ingate or outgate? It says "Pick FCIU9733694 from GRID BGR (F1903A)" and "SAVE THIS CARD TO EXIT THE TERMINAL." That seems like a container gate card for pick up. Might be ingate? Actually ingate is entry of vehicle/container into facility. This seems like a card used to pick up a container from a gate, maybe an outgate card. The wording "exit the terminal" suggests the container is leaving the terminal, so it\'s an outgate document. The "INGATE" would be for arrival, "OUTGATE" for departure. The page has "PICKUP 566367" and "LOAD OUT". So it\'s probably an outgate card. So doc_type: outgate.\n\nCheck if any other match: "ingate" vs "outgate". There\'s an "OUTGATE" in the definitions, but doc type names: ingate, outgate. So we classify as "outgate".\n\nThus we output JSON: documents: [{ page_no: 1, doc_type: "outgate" }].\n\nWe must use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_gO_W3DtoTmqjPXOz0B2-PQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2777, 'outputTokens': 348, 'totalTokens': 3125}, 'metrics': {'latencyMs': 1578}}
2025-09-24 18:59:25,201 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/764cee2a_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:25,215 - INFO - 

YASUN3YXXTIFXRHDIKNF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:25,215 - INFO - 

✓ Saved result: output/run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 18:59:25,300 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6f08517a-284c-4e22-a2cb-338e793bda43', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:25 GMT', 'content-type': 'application/json', 'content-length': '2280', 'connection': 'keep-alive', 'x-amzn-requestid': '6f08517a-284c-4e22-a2cb-338e793bda43'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The user provided only page1 of a PDF. We must output a JSON with documents array containing object for page1. We need to examine the page content for document type. Let\'s parse the content.\n\nThe text:\n\n```\nAPPROVED\nMAERSK\nReuse empty container\nContainer number\nExport booking ref.\nReuse date\nCAAU8485442\n257637187\n09/12/2025\nImport location\nExport location\nPrice\nIllinois\nIllinois\nUSD30\nRef. client\nImport delivery date\nSCAC (import)\nSCAC (export)\n--\n--\nSSRT\nSSRT\nCOMMENT\nFor North America Carriers, please ensure that if you have previously requested empty rail billing for this\napproved container to cancel the request for empty rail billing. Please cancel empty rail billing by\ncontacting <EMAIL>\nRequest history\n09/12/2025 09:38 AM\nApproved\n09/12/2025 09:38 AM\nOverrule submitted\n09/12/2025 09:06 AM\nDeclined\n09/12/2025 09:06 AM Overrule submitted\n09/12/2025 07:45 AM\nDeclined\n09/12/2025 07:45 AM\nPending\n09/12/2025 07:45 AM <NAME_EMAIL> at Super Star Freight\nGenerated by Avantida 09/12/2025 10:25 AM\n```\n\nWe need to determine the document type. This looks like a container booking approval or something regarding reuse empty container. It is likely an approval/dispatch note. Not an invoice, BOL, POD, etc. Maybe "clear_to_pay"? No. It\'s like an approval of a container reuse. Might be a "clear to pay" or "approval" but not in list. Could be "other". But check if there is a known type: maybe this is an "approval" for empty container reuse. None of the defined types. So classify as "other". But check if a known type like "po"? No. "purchase order" - no. "rate_confirmation" - no. "bol" - no. "pod" - no. "log" - no. "scale_ticket" - no. "fuel_receipt" - no. "cust_rate_confirmation" - no. "clear_to_pay" - no. "combined_carrier_documents" - maybe not.\n\nTherefore classify as "other". Output JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2855, 'outputTokens': 545, 'totalTokens': 3400}, 'metrics': {'latencyMs': 2286}}
2025-09-24 18:59:25,300 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f36fc34f_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:25,545 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/764cee2a_YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:25,570 - INFO - 

VY0AAD98JWYCZUAHKROB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:59:25,571 - INFO - 

✓ Saved result: output/run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 18:59:25,647 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3b942a38-0e2a-4830-bd26-88f81bd4dbf2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:25 GMT', 'content-type': 'application/json', 'content-length': '1676', 'connection': 'keep-alive', 'x-amzn-requestid': '3b942a38-0e2a-4830-bd26-88f81bd4dbf2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page from the user. We need to classify the page. The text is about "Intermodal Gate Information" with details like Equipment ID, Terminal, etc. This looks like an ingate or outgate document. It says "Activity: Departure" and "L/E Status: Load" and "Event Information". The keywords: "Equipment Information", "Event Information", "Equipment ID", "Terminal", "Associated Equipment ID", "Date and Time", "L/E Status", "Activity: Departure", "Driver Information", "Seal Number", "Booking Number". It seems to be a gate-out document. Let\'s see definitions: ingate: "Record of vehicle/container entering a facility (gate-in). Keywords: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In". outgate: "Record of vehicle/container exiting a facility (gate-out/release). Keywords: Outgate, Departed, Gate Out, Time Out, Release, Exit Time".\n\nThis page states "Activity: Departure", which is departure from the facility, i.e., outgate. So doc_type likely outgate.\n\nThe text is an email summary of gate information. It might also be ingate if it\'s arrival. But "Departure" indicates outgate. So we classify as outgate.\n\nWe need to output JSON with "documents": array of objects with page_no 1 and doc_type "outgate". Ensure we use the tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2976, 'outputTokens': 322, 'totalTokens': 3298}, 'metrics': {'latencyMs': 1432}}
2025-09-24 18:59:25,647 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4faeaf6e_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:25,899 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f36fc34f_VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:25,936 - INFO - 

ZAJMVPYOM22ETECQRW3L.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:25,936 - INFO - 

✓ Saved result: output/run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 18:59:26,224 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4faeaf6e_ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:26,570 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'de7271d1-d554-477d-aa3a-36208855990a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:29:26 GMT', 'content-type': 'application/json', 'content-length': '1118', 'connection': 'keep-alive', 'x-amzn-requestid': 'de7271d1-d554-477d-aa3a-36208855990a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to decide doc_type. Let\'s analyze keywords: The document appears to be a Gate Out (Outgate) document for a container. It has "GATE OUT" and "EIR Number", "Truck In", "Truck Out", "Container", "Type", "Tare", "Payload", "ISO Code", "Vessel", "Move Type", "Depot - Customer", "Customer". Also it includes "EIR Chassis", "Chassis", "Truck Out In" etc. This appears to be an Outgate document. The relevant keywords: "GATE OUT". The spec: outgate includes "Outgate, Departed, Gate Out, Time Out, Release, Exit Time". So we should classify as "outgate".\n\nWe must output JSON with "documents" array with one object: page_no: 1, doc_type: "outgate".\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3156, 'outputTokens': 220, 'totalTokens': 3376}, 'metrics': {'latencyMs': 1036}}
2025-09-24 18:59:26,570 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/08ca72dc_WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:26,605 - INFO - 

WRP4157FNOQ52T6661B4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 18:59:26,605 - INFO - 

✓ Saved result: output/run1_WRP4157FNOQ52T6661B4.json
2025-09-24 18:59:26,895 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/08ca72dc_WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:26,895 - INFO - 
📊 Processing Summary:
2025-09-24 18:59:26,896 - INFO -    Total files: 12
2025-09-24 18:59:26,896 - INFO -    Successful: 12
2025-09-24 18:59:26,896 - INFO -    Failed: 0
2025-09-24 18:59:26,896 - INFO -    Duration: 21.12 seconds
2025-09-24 18:59:26,896 - INFO -    Output directory: output
2025-09-24 18:59:26,896 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:59:26,896 - INFO -    📄 BJYFRYT0V8O045HGDXJ6.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 E88AUSUP8065SEI9GD5Q.jpg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 EKMN29XJBZVFPLOYTKU7.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 GHXKMFASS6CGN362R5BW.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 HZNQ971O2V9MGG5MU1T3.jpeg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 OO7629V25ZJ22FHL0PAA.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 18:59:26,896 - INFO -    📄 T5U48WX7H3FNSZPBDBLZ.png: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 18:59:26,896 - INFO -    📄 UGM225037492OGWQHUHR.png: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 VY0AAD98JWYCZUAHKROB.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 18:59:26,896 - INFO -    📄 WRP4157FNOQ52T6661B4.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 YASUN3YXXTIFXRHDIKNF.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,896 - INFO -    📄 ZAJMVPYOM22ETECQRW3L.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 18:59:26,897 - INFO - 
============================================================================================================================================
2025-09-24 18:59:26,897 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:59:26,897 - INFO - ============================================================================================================================================
2025-09-24 18:59:26,897 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:59:26,897 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:59:26,897 - INFO - BJYFRYT0V8O045HGDXJ6.pdf                           1      outgate                                  run1_BJYFRYT0V8O045HGDXJ6.json                                                  
2025-09-24 18:59:26,897 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf
2025-09-24 18:59:26,897 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 18:59:26,897 - INFO - 
2025-09-24 18:59:26,897 - INFO - E88AUSUP8065SEI9GD5Q.jpg                           1      outgate                                  run1_E88AUSUP8065SEI9GD5Q.json                                                  
2025-09-24 18:59:26,897 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg
2025-09-24 18:59:26,897 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 18:59:26,897 - INFO - 
2025-09-24 18:59:26,897 - INFO - EKMN29XJBZVFPLOYTKU7.pdf                           1      outgate                                  run1_EKMN29XJBZVFPLOYTKU7.json                                                  
2025-09-24 18:59:26,897 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf
2025-09-24 18:59:26,897 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 18:59:26,897 - INFO - 
2025-09-24 18:59:26,898 - INFO - GHXKMFASS6CGN362R5BW.pdf                           1      outgate                                  run1_GHXKMFASS6CGN362R5BW.json                                                  
2025-09-24 18:59:26,898 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf
2025-09-24 18:59:26,898 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 18:59:26,898 - INFO - 
2025-09-24 18:59:26,898 - INFO - HZNQ971O2V9MGG5MU1T3.jpeg                          1      outgate                                  run1_HZNQ971O2V9MGG5MU1T3.json                                                  
2025-09-24 18:59:26,898 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg
2025-09-24 18:59:26,898 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 18:59:26,898 - INFO - 
2025-09-24 18:59:26,898 - INFO - OO7629V25ZJ22FHL0PAA.pdf                           1      outgate                                  run1_OO7629V25ZJ22FHL0PAA.json                                                  
2025-09-24 18:59:26,898 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:26,898 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 18:59:26,898 - INFO - 
2025-09-24 18:59:26,898 - INFO - OO7629V25ZJ22FHL0PAA.pdf                           2      other                                    run1_OO7629V25ZJ22FHL0PAA.json                                                  
2025-09-24 18:59:26,898 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf
2025-09-24 18:59:26,898 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 18:59:26,898 - INFO - 
2025-09-24 18:59:26,898 - INFO - T5U48WX7H3FNSZPBDBLZ.png                           1      log                                      run1_T5U48WX7H3FNSZPBDBLZ.json                                                  
2025-09-24 18:59:26,898 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png
2025-09-24 18:59:26,898 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 18:59:26,898 - INFO - 
2025-09-24 18:59:26,898 - INFO - UGM225037492OGWQHUHR.png                           1      ingate                                   run1_UGM225037492OGWQHUHR.json                                                  
2025-09-24 18:59:26,898 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png
2025-09-24 18:59:26,898 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UGM225037492OGWQHUHR.json
2025-09-24 18:59:26,899 - INFO - 
2025-09-24 18:59:26,899 - INFO - VY0AAD98JWYCZUAHKROB.pdf                           1      other                                    run1_VY0AAD98JWYCZUAHKROB.json                                                  
2025-09-24 18:59:26,899 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf
2025-09-24 18:59:26,899 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 18:59:26,899 - INFO - 
2025-09-24 18:59:26,899 - INFO - WRP4157FNOQ52T6661B4.pdf                           1      outgate                                  run1_WRP4157FNOQ52T6661B4.json                                                  
2025-09-24 18:59:26,899 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf
2025-09-24 18:59:26,899 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WRP4157FNOQ52T6661B4.json
2025-09-24 18:59:26,899 - INFO - 
2025-09-24 18:59:26,899 - INFO - YASUN3YXXTIFXRHDIKNF.pdf                           1      outgate                                  run1_YASUN3YXXTIFXRHDIKNF.json                                                  
2025-09-24 18:59:26,899 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf
2025-09-24 18:59:26,899 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 18:59:26,899 - INFO - 
2025-09-24 18:59:26,899 - INFO - ZAJMVPYOM22ETECQRW3L.pdf                           1      outgate                                  run1_ZAJMVPYOM22ETECQRW3L.json                                                  
2025-09-24 18:59:26,899 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf
2025-09-24 18:59:26,899 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 18:59:26,899 - INFO - 
2025-09-24 18:59:26,899 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:59:26,899 - INFO - Total entries: 13
2025-09-24 18:59:26,899 - INFO - ============================================================================================================================================
2025-09-24 18:59:26,899 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:59:26,899 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:59:26,899 - INFO -   1. BJYFRYT0V8O045HGDXJ6.pdf            Page 1   → outgate         | run1_BJYFRYT0V8O045HGDXJ6.json
2025-09-24 18:59:26,899 - INFO -   2. E88AUSUP8065SEI9GD5Q.jpg            Page 1   → outgate         | run1_E88AUSUP8065SEI9GD5Q.json
2025-09-24 18:59:26,900 - INFO -   3. EKMN29XJBZVFPLOYTKU7.pdf            Page 1   → outgate         | run1_EKMN29XJBZVFPLOYTKU7.json
2025-09-24 18:59:26,900 - INFO -   4. GHXKMFASS6CGN362R5BW.pdf            Page 1   → outgate         | run1_GHXKMFASS6CGN362R5BW.json
2025-09-24 18:59:26,900 - INFO -   5. HZNQ971O2V9MGG5MU1T3.jpeg           Page 1   → outgate         | run1_HZNQ971O2V9MGG5MU1T3.json
2025-09-24 18:59:26,900 - INFO -   6. OO7629V25ZJ22FHL0PAA.pdf            Page 1   → outgate         | run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 18:59:26,900 - INFO -   7. OO7629V25ZJ22FHL0PAA.pdf            Page 2   → other           | run1_OO7629V25ZJ22FHL0PAA.json
2025-09-24 18:59:26,900 - INFO -   8. T5U48WX7H3FNSZPBDBLZ.png            Page 1   → log             | run1_T5U48WX7H3FNSZPBDBLZ.json
2025-09-24 18:59:26,900 - INFO -   9. UGM225037492OGWQHUHR.png            Page 1   → ingate          | run1_UGM225037492OGWQHUHR.json
2025-09-24 18:59:26,900 - INFO -  10. VY0AAD98JWYCZUAHKROB.pdf            Page 1   → other           | run1_VY0AAD98JWYCZUAHKROB.json
2025-09-24 18:59:26,900 - INFO -  11. WRP4157FNOQ52T6661B4.pdf            Page 1   → outgate         | run1_WRP4157FNOQ52T6661B4.json
2025-09-24 18:59:26,900 - INFO -  12. YASUN3YXXTIFXRHDIKNF.pdf            Page 1   → outgate         | run1_YASUN3YXXTIFXRHDIKNF.json
2025-09-24 18:59:26,900 - INFO -  13. ZAJMVPYOM22ETECQRW3L.pdf            Page 1   → outgate         | run1_ZAJMVPYOM22ETECQRW3L.json
2025-09-24 18:59:26,900 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:59:26,900 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 21.116407, 'processed_files': [{'filename': 'BJYFRYT0V8O045HGDXJ6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/BJYFRYT0V8O045HGDXJ6.pdf'}, {'filename': 'E88AUSUP8065SEI9GD5Q.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/E88AUSUP8065SEI9GD5Q.jpg'}, {'filename': 'EKMN29XJBZVFPLOYTKU7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/EKMN29XJBZVFPLOYTKU7.pdf'}, {'filename': 'GHXKMFASS6CGN362R5BW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/GHXKMFASS6CGN362R5BW.pdf'}, {'filename': 'HZNQ971O2V9MGG5MU1T3.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/HZNQ971O2V9MGG5MU1T3.jpeg'}, {'filename': 'OO7629V25ZJ22FHL0PAA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/OO7629V25ZJ22FHL0PAA.pdf'}, {'filename': 'T5U48WX7H3FNSZPBDBLZ.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/T5U48WX7H3FNSZPBDBLZ.png'}, {'filename': 'UGM225037492OGWQHUHR.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/UGM225037492OGWQHUHR.png'}, {'filename': 'VY0AAD98JWYCZUAHKROB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/VY0AAD98JWYCZUAHKROB.pdf'}, {'filename': 'WRP4157FNOQ52T6661B4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/WRP4157FNOQ52T6661B4.pdf'}, {'filename': 'YASUN3YXXTIFXRHDIKNF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/YASUN3YXXTIFXRHDIKNF.pdf'}, {'filename': 'ZAJMVPYOM22ETECQRW3L.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/outgate/ZAJMVPYOM22ETECQRW3L.pdf'}]}
