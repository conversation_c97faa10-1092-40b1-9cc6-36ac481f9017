2025-09-24 18:29:29,719 - INFO - Logging initialized. Log file: logs/test_classification_20250924_182929.log
2025-09-24 18:29:29,719 - INFO - 📁 Found 18 files to process
2025-09-24 18:29:29,719 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:29:29,719 - INFO - 🚀 Processing 18 files in FORCED PARALLEL MODE...
2025-09-24 18:29:29,719 - INFO - 🚀 Creating 18 parallel tasks...
2025-09-24 18:29:29,719 - INFO - 🚀 All 18 tasks created - executing in parallel...
2025-09-24 18:29:29,719 - INFO - ⬆️ [18:29:29] Uploading: CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:32,209 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf -> s3://document-extraction-logistically/temp/162d0f2d_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:32,209 - INFO - 🔍 [18:29:32] Starting classification: CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:32,210 - INFO - ⬆️ [18:29:32] Uploading: CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:32,212 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:32,232 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:32,238 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/162d0f2d_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:32,238 - INFO - Processing PDF from S3...
2025-09-24 18:29:32,238 - INFO - Downloading PDF from S3 to /tmp/tmpdjv6ihom/162d0f2d_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:32,858 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CMGYRKEPEEGIJHOFEAQ9.pdf -> s3://document-extraction-logistically/temp/c2a71800_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:32,859 - INFO - 🔍 [18:29:32] Starting classification: CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:32,859 - INFO - ⬆️ [18:29:32] Uploading: DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:32,862 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:32,879 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:32,882 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c2a71800_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:32,882 - INFO - Processing PDF from S3...
2025-09-24 18:29:32,882 - INFO - Downloading PDF from S3 to /tmp/tmpi1is0917/c2a71800_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:34,275 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DKI9846PWRKYUQA0DQ4A.pdf -> s3://document-extraction-logistically/temp/600b7610_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:34,276 - INFO - 🔍 [18:29:34] Starting classification: DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:34,277 - INFO - ⬆️ [18:29:34] Uploading: DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:34,279 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:34,293 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:34,297 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/600b7610_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:34,297 - INFO - Processing PDF from S3...
2025-09-24 18:29:34,298 - INFO - Downloading PDF from S3 to /tmp/tmpsz1bjuu6/600b7610_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:34,432 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:29:34,432 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:34,432 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:29:34,433 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:34,434 - INFO - Splitting PDF c2a71800_CMGYRKEPEEGIJHOFEAQ9 into 1 pages
2025-09-24 18:29:34,437 - INFO - Splitting PDF 162d0f2d_CHU18SQU5Q2XCX57ABTP into 2 pages
2025-09-24 18:29:34,451 - INFO - Split PDF into 1 pages
2025-09-24 18:29:34,452 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:34,452 - INFO - Expected pages: [1]
2025-09-24 18:29:34,456 - INFO - Split PDF into 2 pages
2025-09-24 18:29:34,456 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:34,456 - INFO - Expected pages: [1, 2]
2025-09-24 18:29:34,905 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DTOGYRLS96ZKXQOO6EY5.pdf -> s3://document-extraction-logistically/temp/693bf5a9_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:34,905 - INFO - 🔍 [18:29:34] Starting classification: DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:34,905 - INFO - ⬆️ [18:29:34] Uploading: EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:29:34,905 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:34,917 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:34,920 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/693bf5a9_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:34,920 - INFO - Processing PDF from S3...
2025-09-24 18:29:34,920 - INFO - Downloading PDF from S3 to /tmp/tmprvin7dat/693bf5a9_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:36,135 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf -> s3://document-extraction-logistically/temp/97159a6b_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:29:36,135 - INFO - 🔍 [18:29:36] Starting classification: EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:29:36,136 - INFO - ⬆️ [18:29:36] Uploading: ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:36,136 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:36,159 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:36,163 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/97159a6b_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:29:36,163 - INFO - Processing PDF from S3...
2025-09-24 18:29:36,163 - INFO - Downloading PDF from S3 to /tmp/tmpn2gmzewf/97159a6b_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:29:36,517 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:29:36,517 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:36,518 - INFO - Splitting PDF 693bf5a9_DTOGYRLS96ZKXQOO6EY5 into 1 pages
2025-09-24 18:29:36,520 - INFO - Split PDF into 1 pages
2025-09-24 18:29:36,520 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:36,520 - INFO - Expected pages: [1]
2025-09-24 18:29:36,754 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 18:29:36,754 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:36,756 - INFO - Splitting PDF 600b7610_DKI9846PWRKYUQA0DQ4A into 1 pages
2025-09-24 18:29:36,757 - INFO - Split PDF into 1 pages
2025-09-24 18:29:36,757 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:36,757 - INFO - Expected pages: [1]
2025-09-24 18:29:36,766 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf -> s3://document-extraction-logistically/temp/dcb99c2c_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:36,766 - INFO - 🔍 [18:29:36] Starting classification: ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:36,767 - INFO - ⬆️ [18:29:36] Uploading: IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:36,767 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:36,787 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:36,791 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dcb99c2c_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:36,791 - INFO - Processing PDF from S3...
2025-09-24 18:29:36,791 - INFO - Downloading PDF from S3 to /tmp/tmp_ta2i018/dcb99c2c_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:37,364 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/IGQ6ARKKVIX04G5ZQDJ5.pdf -> s3://document-extraction-logistically/temp/a7b907d0_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:37,364 - INFO - 🔍 [18:29:37] Starting classification: IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:37,365 - INFO - ⬆️ [18:29:37] Uploading: J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:37,366 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:37,390 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:37,426 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a7b907d0_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:37,427 - INFO - Processing PDF from S3...
2025-09-24 18:29:37,427 - INFO - Downloading PDF from S3 to /tmp/tmpnahmlt4p/a7b907d0_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:37,846 - INFO - Page 2: Extracted 301 characters, 16 lines from 162d0f2d_CHU18SQU5Q2XCX57ABTP_f12ba564_page_002.pdf
2025-09-24 18:29:37,847 - INFO - Successfully processed page 2
2025-09-24 18:29:38,006 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf -> s3://document-extraction-logistically/temp/6bb91139_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:38,007 - INFO - 🔍 [18:29:38] Starting classification: J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:38,007 - INFO - ⬆️ [18:29:38] Uploading: MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:38,010 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:38,025 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:38,031 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6bb91139_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:38,031 - INFO - Processing PDF from S3...
2025-09-24 18:29:38,031 - INFO - Downloading PDF from S3 to /tmp/tmpcoxralv6/6bb91139_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:38,661 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf -> s3://document-extraction-logistically/temp/200caffe_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:38,661 - INFO - 🔍 [18:29:38] Starting classification: MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:38,663 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:38,667 - INFO - ⬆️ [18:29:38] Uploading: NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:38,681 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:38,685 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/200caffe_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:38,685 - INFO - Processing PDF from S3...
2025-09-24 18:29:38,685 - INFO - Downloading PDF from S3 to /tmp/tmpnqzglbop/200caffe_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:38,972 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:29:38,972 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:38,973 - INFO - Splitting PDF a7b907d0_IGQ6ARKKVIX04G5ZQDJ5 into 1 pages
2025-09-24 18:29:38,979 - INFO - Split PDF into 1 pages
2025-09-24 18:29:38,979 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:38,979 - INFO - Expected pages: [1]
2025-09-24 18:29:39,216 - INFO - Page 1: Extracted 671 characters, 43 lines from c2a71800_CMGYRKEPEEGIJHOFEAQ9_bf09db4a_page_001.pdf
2025-09-24 18:29:39,217 - INFO - Successfully processed page 1
2025-09-24 18:29:39,218 - INFO - Combined 1 pages into final text
2025-09-24 18:29:39,218 - INFO - Text validation for c2a71800_CMGYRKEPEEGIJHOFEAQ9: 688 characters, 1 pages
2025-09-24 18:29:39,218 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:39,218 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:39,309 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf -> s3://document-extraction-logistically/temp/4851d379_NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:39,309 - INFO - 🔍 [18:29:39] Starting classification: NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:39,310 - INFO - ⬆️ [18:29:39] Uploading: SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:39,312 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:39,336 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:39,345 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4851d379_NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:39,345 - INFO - Processing PDF from S3...
2025-09-24 18:29:39,347 - INFO - Downloading PDF from S3 to /tmp/tmpo8w7qu1j/4851d379_NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:39,357 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 18:29:39,358 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:39,359 - INFO - Splitting PDF dcb99c2c_ESRNPUZ2WJ48Y3NTAX21 into 2 pages
2025-09-24 18:29:39,362 - INFO - Split PDF into 2 pages
2025-09-24 18:29:39,362 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:39,362 - INFO - Expected pages: [1, 2]
2025-09-24 18:29:39,481 - INFO - Page 1: Extracted 1169 characters, 61 lines from 162d0f2d_CHU18SQU5Q2XCX57ABTP_f12ba564_page_001.pdf
2025-09-24 18:29:39,482 - INFO - Successfully processed page 1
2025-09-24 18:29:39,482 - INFO - Combined 2 pages into final text
2025-09-24 18:29:39,483 - INFO - Text validation for 162d0f2d_CHU18SQU5Q2XCX57ABTP: 1506 characters, 2 pages
2025-09-24 18:29:39,483 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:39,483 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:39,903 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf -> s3://document-extraction-logistically/temp/91ae3348_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:39,903 - INFO - 🔍 [18:29:39] Starting classification: SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:39,903 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:39,903 - INFO - ⬆️ [18:29:39] Uploading: TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:39,909 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:39,913 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/91ae3348_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:39,913 - INFO - Processing PDF from S3...
2025-09-24 18:29:39,913 - INFO - Downloading PDF from S3 to /tmp/tmp2u87k37n/91ae3348_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:40,473 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TAIJ9HY2DEXLZTHT5SXH.pdf -> s3://document-extraction-logistically/temp/5c31ca93_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:40,473 - INFO - 🔍 [18:29:40] Starting classification: TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:40,474 - INFO - ⬆️ [18:29:40] Uploading: TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:40,475 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:40,501 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:40,505 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5c31ca93_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:40,505 - INFO - Processing PDF from S3...
2025-09-24 18:29:40,505 - INFO - Downloading PDF from S3 to /tmp/tmpmx7zorur/5c31ca93_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:40,760 - INFO - Page 1: Extracted 808 characters, 53 lines from 693bf5a9_DTOGYRLS96ZKXQOO6EY5_bf2f74db_page_001.pdf
2025-09-24 18:29:40,761 - INFO - Successfully processed page 1
2025-09-24 18:29:40,762 - INFO - Combined 1 pages into final text
2025-09-24 18:29:40,763 - INFO - Text validation for 693bf5a9_DTOGYRLS96ZKXQOO6EY5: 825 characters, 1 pages
2025-09-24 18:29:40,763 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:40,764 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:40,985 - INFO - Downloaded PDF size: 1.2 MB
2025-09-24 18:29:40,986 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:40,989 - INFO - Splitting PDF 6bb91139_J2HEKQMZPE7HUYJF14PH into 10 pages
2025-09-24 18:29:41,007 - INFO - Split PDF into 10 pages
2025-09-24 18:29:41,007 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:41,007 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-09-24 18:29:41,083 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf -> s3://document-extraction-logistically/temp/e5b7e833_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:41,083 - INFO - 🔍 [18:29:41] Starting classification: TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:41,084 - INFO - ⬆️ [18:29:41] Uploading: UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:41,085 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:41,097 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:41,101 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e5b7e833_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:41,101 - INFO - Processing PDF from S3...
2025-09-24 18:29:41,102 - INFO - Downloading PDF from S3 to /tmp/tmp2agvr_wf/e5b7e833_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:41,159 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7c5c01e3-1dfc-44d3-b58b-9518543fce48', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:41 GMT', 'content-type': 'application/json', 'content-length': '749', 'connection': 'keep-alive', 'x-amzn-requestid': '7c5c01e3-1dfc-44d3-b58b-9518543fce48'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to output a single object with page_no: 1, doc_type.\n\nWe need to decide if this is invoice or comm_invoice or lumper_receipt or other. The document contains "UA TERM Invoice #0393" and appears to be a carrier invoice. It has PO Box? There\'s no HS code, no customs. It\'s an invoice. Not lumper because no lumper-specific. So classify as invoice. Output via tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2778, 'outputTokens': 120, 'totalTokens': 2898}, 'metrics': {'latencyMs': 717}}
2025-09-24 18:29:41,160 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c2a71800_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:41,327 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 18:29:41,327 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:41,330 - INFO - Splitting PDF 200caffe_MVSX79LFMEJ1OI57DGBO into 2 pages
2025-09-24 18:29:41,337 - INFO - Split PDF into 2 pages
2025-09-24 18:29:41,337 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:41,337 - INFO - Expected pages: [1, 2]
2025-09-24 18:29:41,707 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf -> s3://document-extraction-logistically/temp/d4951f12_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:41,707 - INFO - 🔍 [18:29:41] Starting classification: UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:41,707 - INFO - ⬆️ [18:29:41] Uploading: US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:41,708 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:41,718 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:41,719 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '98a70c87-ad44-4a53-b270-3da5477bb6a4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:41 GMT', 'content-type': 'application/json', 'content-length': '1086', 'connection': 'keep-alive', 'x-amzn-requestid': '98a70c87-ad44-4a53-b270-3da5477bb6a4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have two pages. Page1 is invoice. It has "Invoice Number" and "Print Date", etc. It\'s a carrier invoice. No HS codes, no commercial details. So doc_type: invoice.\n\nPage2 continues with charges. It starts with "Charge Type". The page likely is continuation of same invoice. No indication of continuation page? It doesn\'t start with "continued", but it\'s part of same document. So same doc_type invoice. The instructions: If page is part of multi-page single document: each page still gets same doc_type. So page2 also invoice. So output array with two objects.\n\nReturn via tool call classify_logistics_doc_type with documents array. Each element: page_no, doc_type.\n\nLet\'s produce.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3063, 'outputTokens': 183, 'totalTokens': 3246}, 'metrics': {'latencyMs': 1027}}
2025-09-24 18:29:41,721 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/162d0f2d_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:41,722 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d4951f12_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:41,724 - INFO - Processing PDF from S3...
2025-09-24 18:29:41,726 - INFO - Downloading PDF from S3 to /tmp/tmprxp4wtl7/d4951f12_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:41,978 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:29:41,979 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:41,982 - INFO - Splitting PDF 5c31ca93_TAIJ9HY2DEXLZTHT5SXH into 1 pages
2025-09-24 18:29:41,998 - INFO - Split PDF into 1 pages
2025-09-24 18:29:41,999 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:41,999 - INFO - Expected pages: [1]
2025-09-24 18:29:42,267 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 18:29:42,268 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:42,270 - INFO - Splitting PDF 91ae3348_SPKHMNJUMC10ZF1SJUVA into 2 pages
2025-09-24 18:29:42,272 - INFO - Split PDF into 2 pages
2025-09-24 18:29:42,272 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:42,272 - INFO - Expected pages: [1, 2]
2025-09-24 18:29:42,346 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf -> s3://document-extraction-logistically/temp/f643b150_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:42,347 - INFO - 🔍 [18:29:42] Starting classification: US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:42,347 - INFO - ⬆️ [18:29:42] Uploading: W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:42,349 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:42,368 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:42,379 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f643b150_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:42,380 - INFO - Processing PDF from S3...
2025-09-24 18:29:42,381 - INFO - Downloading PDF from S3 to /tmp/tmpb07yw9td/f643b150_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:42,525 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '67cb1b57-1915-43b3-8439-2df064a266d9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:42 GMT', 'content-type': 'application/json', 'content-length': '629', 'connection': 'keep-alive', 'x-amzn-requestid': '67cb1b57-1915-43b3-8439-2df064a266d9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page 1. It clearly is an invoice. Check if it\'s commercial invoice? It doesn\'t show HS code, international shipping. It\'s a domestic transport invoice. So doc_type: invoice. Output JSON via tool call.\n\nWe must output exactly one object: page_no:1 doc_type:"invoice".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2814, 'outputTokens': 85, 'totalTokens': 2899}, 'metrics': {'latencyMs': 587}}
2025-09-24 18:29:42,525 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/693bf5a9_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:42,604 - INFO - Downloaded PDF size: 2.0 MB
2025-09-24 18:29:42,605 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:42,606 - INFO - Splitting PDF 4851d379_NTI160L8QH452446HMQV into 4 pages
2025-09-24 18:29:42,612 - INFO - Split PDF into 4 pages
2025-09-24 18:29:42,612 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:42,612 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 18:29:42,950 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/W1P39VXY7XRKDJG29JXQ.pdf -> s3://document-extraction-logistically/temp/5b2d1806_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:42,951 - INFO - 🔍 [18:29:42] Starting classification: W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:42,951 - INFO - ⬆️ [18:29:42] Uploading: ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:42,952 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:42,968 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:42,978 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5b2d1806_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:42,979 - INFO - Processing PDF from S3...
2025-09-24 18:29:42,980 - INFO - Downloading PDF from S3 to /tmp/tmptkj3gibw/5b2d1806_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:43,028 - INFO - Page 1: Extracted 808 characters, 63 lines from 600b7610_DKI9846PWRKYUQA0DQ4A_60a0acbc_page_001.pdf
2025-09-24 18:29:43,028 - INFO - Successfully processed page 1
2025-09-24 18:29:43,028 - INFO - Combined 1 pages into final text
2025-09-24 18:29:43,029 - INFO - Text validation for 600b7610_DKI9846PWRKYUQA0DQ4A: 825 characters, 1 pages
2025-09-24 18:29:43,029 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:43,029 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:43,106 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:29:43,106 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:43,108 - INFO - Splitting PDF e5b7e833_TWW70AYZ4HHN0G7UPMM7 into 3 pages
2025-09-24 18:29:43,164 - INFO - Page 1: Extracted 609 characters, 31 lines from a7b907d0_IGQ6ARKKVIX04G5ZQDJ5_733408da_page_001.pdf
2025-09-24 18:29:43,166 - INFO - Successfully processed page 1
2025-09-24 18:29:43,166 - INFO - Combined 1 pages into final text
2025-09-24 18:29:43,167 - INFO - Text validation for a7b907d0_IGQ6ARKKVIX04G5ZQDJ5: 626 characters, 1 pages
2025-09-24 18:29:43,171 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:43,171 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:43,177 - INFO - Split PDF into 3 pages
2025-09-24 18:29:43,177 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:43,177 - INFO - Expected pages: [1, 2, 3]
2025-09-24 18:29:43,547 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZNZPCNCYXXVUIEOEH7SH.pdf -> s3://document-extraction-logistically/temp/f83c2fb7_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:43,548 - INFO - 🔍 [18:29:43] Starting classification: ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:43,548 - INFO - ⬆️ [18:29:43] Uploading: ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:29:43,551 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:43,561 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:43,564 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f83c2fb7_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:43,564 - INFO - Processing PDF from S3...
2025-09-24 18:29:43,564 - INFO - Downloading PDF from S3 to /tmp/tmp4_xuiosu/f83c2fb7_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:43,723 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:29:43,723 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:43,725 - INFO - Splitting PDF d4951f12_UMKAW947G6NUUYO725CC into 2 pages
2025-09-24 18:29:43,730 - INFO - Split PDF into 2 pages
2025-09-24 18:29:43,730 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:43,730 - INFO - Expected pages: [1, 2]
2025-09-24 18:29:44,149 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf -> s3://document-extraction-logistically/temp/a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:29:44,150 - INFO - 🔍 [18:29:44] Starting classification: ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:29:44,151 - INFO - Initializing TextractProcessor...
2025-09-24 18:29:44,168 - INFO - Initializing BedrockProcessor...
2025-09-24 18:29:44,175 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:29:44,175 - INFO - Processing PDF from S3...
2025-09-24 18:29:44,176 - INFO - Downloading PDF from S3 to /tmp/tmprxv4v2am/a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:29:44,197 - INFO - 

CMGYRKEPEEGIJHOFEAQ9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:44,197 - INFO - 

✓ Saved result: output/run1_CMGYRKEPEEGIJHOFEAQ9.json
2025-09-24 18:29:44,479 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c2a71800_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:29:44,482 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:29:44,482 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:44,483 - INFO - Splitting PDF 5b2d1806_W1P39VXY7XRKDJG29JXQ into 1 pages
2025-09-24 18:29:44,493 - INFO - Split PDF into 1 pages
2025-09-24 18:29:44,496 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:44,497 - INFO - Expected pages: [1]
2025-09-24 18:29:44,533 - INFO - 

CHU18SQU5Q2XCX57ABTP.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:44,533 - INFO - 

✓ Saved result: output/run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:29:44,559 - INFO - Page 6: Extracted 28 characters, 8 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_006.pdf
2025-09-24 18:29:44,559 - INFO - Successfully processed page 6
2025-09-24 18:29:44,815 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/162d0f2d_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:29:44,832 - INFO - 

DTOGYRLS96ZKXQOO6EY5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:44,832 - INFO - 

✓ Saved result: output/run1_DTOGYRLS96ZKXQOO6EY5.json
2025-09-24 18:29:44,877 - INFO - Page 1: Extracted 1120 characters, 59 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_001.pdf
2025-09-24 18:29:44,877 - INFO - Successfully processed page 1
2025-09-24 18:29:44,944 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2d53123e-1c20-43b7-826a-c467fe11180b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:44 GMT', 'content-type': 'application/json', 'content-length': '638', 'connection': 'keep-alive', 'x-amzn-requestid': '2d53123e-1c20-43b7-826a-c467fe11180b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s an invoice. The text includes "Bill to:", "Invoice ID:", "Due Date:", etc. No commercial info like HS code. So should be \'invoice\'. We must output via tool call classify_logistics_doc_type with documents array containing object: page_no 1, doc_type "invoice".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2838, 'outputTokens': 90, 'totalTokens': 2928}, 'metrics': {'latencyMs': 593}}
2025-09-24 18:29:44,944 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/600b7610_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:44,964 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 18:29:44,964 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:44,965 - INFO - Splitting PDF f643b150_US3F65B5JD70IE5R8PG9 into 5 pages
2025-09-24 18:29:44,973 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '14e5bc5d-d5de-4ba7-b37c-ec978bcab50c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:44 GMT', 'content-type': 'application/json', 'content-length': '617', 'connection': 'keep-alive', 'x-amzn-requestid': '14e5bc5d-d5de-4ba7-b37c-ec978bcab50c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The content shows "Invoice" header, "Invoice # 80494", "Bill To", "Terms Net 30". No HS codes or customs. So it\'s a carrier invoice. Not commercial because no customs. So doc_type: invoice. Return array with one object: page_no 1, doc_type "invoice". Use function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2730, 'outputTokens': 93, 'totalTokens': 2823}, 'metrics': {'latencyMs': 652}}
2025-09-24 18:29:44,973 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a7b907d0_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:44,994 - INFO - Split PDF into 5 pages
2025-09-24 18:29:44,994 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:44,994 - INFO - Expected pages: [1, 2, 3, 4, 5]
2025-09-24 18:29:45,016 - INFO - Page 5: Extracted 0 characters, 0 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_005.pdf
2025-09-24 18:29:45,016 - INFO - Successfully processed page 5
2025-09-24 18:29:45,108 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/693bf5a9_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:29:45,128 - INFO - 

DKI9846PWRKYUQA0DQ4A.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:45,128 - INFO - 

✓ Saved result: output/run1_DKI9846PWRKYUQA0DQ4A.json
2025-09-24 18:29:45,382 - INFO - Page 1: Extracted 888 characters, 70 lines from dcb99c2c_ESRNPUZ2WJ48Y3NTAX21_5e46a555_page_001.pdf
2025-09-24 18:29:45,382 - INFO - Successfully processed page 1
2025-09-24 18:29:45,409 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/600b7610_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:29:45,419 - INFO - 

IGQ6ARKKVIX04G5ZQDJ5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:45,420 - INFO - 

✓ Saved result: output/run1_IGQ6ARKKVIX04G5ZQDJ5.json
2025-09-24 18:29:45,583 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:29:45,584 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:45,584 - INFO - Splitting PDF f83c2fb7_ZNZPCNCYXXVUIEOEH7SH into 1 pages
2025-09-24 18:29:45,586 - INFO - Split PDF into 1 pages
2025-09-24 18:29:45,586 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:45,586 - INFO - Expected pages: [1]
2025-09-24 18:29:45,700 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a7b907d0_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:29:45,930 - INFO - Page 3: Extracted 835 characters, 38 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_003.pdf
2025-09-24 18:29:45,930 - INFO - Successfully processed page 3
2025-09-24 18:29:45,981 - INFO - Downloaded PDF size: 1.2 MB
2025-09-24 18:29:45,982 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:45,986 - INFO - Splitting PDF 97159a6b_EJJM2MNOTED9686H34VA into 7 pages
2025-09-24 18:29:46,006 - INFO - Split PDF into 7 pages
2025-09-24 18:29:46,006 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:46,006 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 18:29:46,299 - INFO - Page 2: Extracted 3067 characters, 129 lines from dcb99c2c_ESRNPUZ2WJ48Y3NTAX21_5e46a555_page_002.pdf
2025-09-24 18:29:46,299 - INFO - Successfully processed page 2
2025-09-24 18:29:46,299 - INFO - Combined 2 pages into final text
2025-09-24 18:29:46,299 - INFO - Text validation for dcb99c2c_ESRNPUZ2WJ48Y3NTAX21: 3991 characters, 2 pages
2025-09-24 18:29:46,300 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:46,300 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:46,352 - INFO - Page 2: Extracted 2306 characters, 44 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_002.pdf
2025-09-24 18:29:46,354 - INFO - Successfully processed page 2
2025-09-24 18:29:46,368 - INFO - Page 1: Extracted 779 characters, 52 lines from 5c31ca93_TAIJ9HY2DEXLZTHT5SXH_eb0c23d4_page_001.pdf
2025-09-24 18:29:46,394 - INFO - Page 4: Extracted 2909 characters, 106 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_004.pdf
2025-09-24 18:29:46,394 - INFO - Successfully processed page 1
2025-09-24 18:29:46,395 - INFO - Successfully processed page 4
2025-09-24 18:29:46,395 - INFO - Combined 1 pages into final text
2025-09-24 18:29:46,395 - INFO - Text validation for 5c31ca93_TAIJ9HY2DEXLZTHT5SXH: 796 characters, 1 pages
2025-09-24 18:29:46,396 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:46,396 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:46,640 - INFO - Page 1: Extracted 1031 characters, 65 lines from 200caffe_MVSX79LFMEJ1OI57DGBO_8289a639_page_001.pdf
2025-09-24 18:29:46,640 - INFO - Successfully processed page 1
2025-09-24 18:29:46,720 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 18:29:46,720 - INFO - Splitting PDF into individual pages...
2025-09-24 18:29:46,723 - INFO - Splitting PDF a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON into 10 pages
2025-09-24 18:29:46,756 - INFO - Split PDF into 10 pages
2025-09-24 18:29:46,756 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:29:46,756 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-09-24 18:29:47,020 - INFO - Page 7: Extracted 51 characters, 10 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_007.pdf
2025-09-24 18:29:47,021 - INFO - Successfully processed page 7
2025-09-24 18:29:47,748 - INFO - Page 1: Extracted 835 characters, 66 lines from e5b7e833_TWW70AYZ4HHN0G7UPMM7_a28aa619_page_001.pdf
2025-09-24 18:29:47,748 - INFO - Successfully processed page 1
2025-09-24 18:29:47,805 - INFO - Page 3: Extracted 884 characters, 71 lines from e5b7e833_TWW70AYZ4HHN0G7UPMM7_a28aa619_page_003.pdf
2025-09-24 18:29:47,805 - INFO - Successfully processed page 3
2025-09-24 18:29:47,854 - INFO - Page 1: Extracted 734 characters, 58 lines from 91ae3348_SPKHMNJUMC10ZF1SJUVA_8238d24a_page_001.pdf
2025-09-24 18:29:47,854 - INFO - Successfully processed page 1
2025-09-24 18:29:48,257 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b0376c6f-c24e-4a9d-8f09-8958fb4942d3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:48 GMT', 'content-type': 'application/json', 'content-length': '668', 'connection': 'keep-alive', 'x-amzn-requestid': 'b0376c6f-c24e-4a9d-8f09-8958fb4942d3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content is an "Invoice". It contains "Invoice" header, invoice number, "BILL TO", "LOAD DETAILS", "RATES AND CHARGES", "Total Rate". No HS Code or customs info. So it\'s a carrier invoice. So doc_type "invoice". It\'s page 1. No continuation. So output: {page_no:1, doc_type:"invoice"}.\n\nWe must use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2810, 'outputTokens': 109, 'totalTokens': 2919}, 'metrics': {'latencyMs': 692}}
2025-09-24 18:29:48,257 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5c31ca93_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:48,276 - INFO - 

TAIJ9HY2DEXLZTHT5SXH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:48,276 - INFO - 

✓ Saved result: output/run1_TAIJ9HY2DEXLZTHT5SXH.json
2025-09-24 18:29:48,320 - INFO - Page 3: Extracted 1176 characters, 65 lines from 4851d379_NTI160L8QH452446HMQV_c14a12c2_page_003.pdf
2025-09-24 18:29:48,321 - INFO - Successfully processed page 3
2025-09-24 18:29:48,375 - INFO - Page 1: Extracted 623 characters, 35 lines from 5b2d1806_W1P39VXY7XRKDJG29JXQ_40cf285a_page_001.pdf
2025-09-24 18:29:48,376 - INFO - Successfully processed page 1
2025-09-24 18:29:48,376 - INFO - Combined 1 pages into final text
2025-09-24 18:29:48,377 - INFO - Text validation for 5b2d1806_W1P39VXY7XRKDJG29JXQ: 640 characters, 1 pages
2025-09-24 18:29:48,377 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:48,377 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:48,581 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5c31ca93_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:29:48,654 - INFO - Page 9: Extracted 2002 characters, 30 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_009.pdf
2025-09-24 18:29:48,654 - INFO - Successfully processed page 9
2025-09-24 18:29:48,690 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4c295448-3fdd-4279-b73d-89c57b01f50b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:48 GMT', 'content-type': 'application/json', 'content-length': '1166', 'connection': 'keep-alive', 'x-amzn-requestid': '4c295448-3fdd-4279-b73d-89c57b01f50b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Need to classify each page.\n\nPage1: It contains an "Invoice" type document. It has remittance stub, invoice date, amount due, freight bill no, bill to etc. It\'s a carrier invoice. Not comm_invoice because no HS codes, etc. So doc_type: invoice.\n\nPage2: It\'s a Bill of Lading. It has "BILL OF LADING" header, BOL #, shipper, consignee, etc. So doc_type: bol.\n\nCheck for continuation: Page1 doesn\'t have "continued" or page numbering. Page2 has "PAGE 1 of 1". So not continuation. So each page gets its own doc_type.\n\nReturn array of objects with page_no and doc_type.\n\nWe must use tool classify_logistics_doc_type with the array of documents. The tool expects:\n\n{\n  documents: [\n    { page_no: 1, doc_type: ... },\n    { page_no: 2, doc_type: ... }\n  ]\n}\n\nReturn that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3714, 'outputTokens': 236, 'totalTokens': 3950}, 'metrics': {'latencyMs': 1194}}
2025-09-24 18:29:48,690 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dcb99c2c_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:48,742 - INFO - 

ESRNPUZ2WJ48Y3NTAX21.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:29:48,742 - INFO - 

✓ Saved result: output/run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:29:48,775 - INFO - Page 2: Extracted 2000 characters, 30 lines from 4851d379_NTI160L8QH452446HMQV_c14a12c2_page_002.pdf
2025-09-24 18:29:48,776 - INFO - Successfully processed page 2
2025-09-24 18:29:48,875 - INFO - Page 2: Extracted 3706 characters, 111 lines from 200caffe_MVSX79LFMEJ1OI57DGBO_8289a639_page_002.pdf
2025-09-24 18:29:48,876 - INFO - Successfully processed page 2
2025-09-24 18:29:48,876 - INFO - Combined 2 pages into final text
2025-09-24 18:29:48,876 - INFO - Text validation for 200caffe_MVSX79LFMEJ1OI57DGBO: 4773 characters, 2 pages
2025-09-24 18:29:48,876 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:48,877 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:49,004 - INFO - Page 1: Extracted 771 characters, 36 lines from 4851d379_NTI160L8QH452446HMQV_c14a12c2_page_001.pdf
2025-09-24 18:29:49,004 - INFO - Successfully processed page 1
2025-09-24 18:29:49,007 - INFO - Page 5: Extracted 117 characters, 3 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_005.pdf
2025-09-24 18:29:49,007 - INFO - Successfully processed page 5
2025-09-24 18:29:49,031 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dcb99c2c_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:29:49,033 - INFO - Page 2: Extracted 2945 characters, 113 lines from 91ae3348_SPKHMNJUMC10ZF1SJUVA_8238d24a_page_002.pdf
2025-09-24 18:29:49,034 - INFO - Successfully processed page 2
2025-09-24 18:29:49,034 - INFO - Combined 2 pages into final text
2025-09-24 18:29:49,035 - INFO - Text validation for 91ae3348_SPKHMNJUMC10ZF1SJUVA: 3715 characters, 2 pages
2025-09-24 18:29:49,035 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:49,035 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:49,215 - INFO - Page 8: Extracted 1189 characters, 66 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_008.pdf
2025-09-24 18:29:49,215 - INFO - Successfully processed page 8
2025-09-24 18:29:49,468 - INFO - Page 1: Extracted 759 characters, 40 lines from f643b150_US3F65B5JD70IE5R8PG9_b75d3819_page_001.pdf
2025-09-24 18:29:49,468 - INFO - Successfully processed page 1
2025-09-24 18:29:49,511 - INFO - Page 3: Extracted 560 characters, 33 lines from f643b150_US3F65B5JD70IE5R8PG9_b75d3819_page_003.pdf
2025-09-24 18:29:49,512 - INFO - Successfully processed page 3
2025-09-24 18:29:49,733 - INFO - Page 4: Extracted 3498 characters, 100 lines from 4851d379_NTI160L8QH452446HMQV_c14a12c2_page_004.pdf
2025-09-24 18:29:49,734 - INFO - Successfully processed page 4
2025-09-24 18:29:49,734 - INFO - Combined 4 pages into final text
2025-09-24 18:29:49,734 - INFO - Text validation for 4851d379_NTI160L8QH452446HMQV: 7519 characters, 4 pages
2025-09-24 18:29:49,735 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:49,735 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:49,971 - INFO - Page 4: Extracted 1636 characters, 75 lines from f643b150_US3F65B5JD70IE5R8PG9_b75d3819_page_004.pdf
2025-09-24 18:29:49,971 - INFO - Successfully processed page 4
2025-09-24 18:29:50,057 - INFO - Page 10: Extracted 1044 characters, 32 lines from 6bb91139_J2HEKQMZPE7HUYJF14PH_23617b10_page_010.pdf
2025-09-24 18:29:50,057 - INFO - Successfully processed page 10
2025-09-24 18:29:50,058 - INFO - Combined 10 pages into final text
2025-09-24 18:29:50,059 - INFO - Text validation for 6bb91139_J2HEKQMZPE7HUYJF14PH: 11673 characters, 10 pages
2025-09-24 18:29:50,060 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:50,060 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:50,088 - INFO - Page 4: Extracted 443 characters, 22 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_004.pdf
2025-09-24 18:29:50,088 - INFO - Successfully processed page 4
2025-09-24 18:29:50,172 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f743b3c2-59ce-42f8-b3b7-10feab6cdc09', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:50 GMT', 'content-type': 'application/json', 'content-length': '683', 'connection': 'keep-alive', 'x-amzn-requestid': 'f743b3c2-59ce-42f8-b3b7-10feab6cdc09'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content. We need to classify doc_type. The text shows "INVOICE" header, with Invoice #, Bill To, etc. No HS, customs, etc. So it\'s an invoice. It\'s a carrier invoice, not commercial. So doc_type: "invoice". Output must be via tool call classify_logistics_doc_type. Provide array of objects for each page. Only page1. Output JSON accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2765, 'outputTokens': 104, 'totalTokens': 2869}, 'metrics': {'latencyMs': 648}}
2025-09-24 18:29:50,173 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5b2d1806_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:50,194 - INFO - 

W1P39VXY7XRKDJG29JXQ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:50,194 - INFO - 

✓ Saved result: output/run1_W1P39VXY7XRKDJG29JXQ.json
2025-09-24 18:29:50,276 - INFO - Page 2: Extracted 4442 characters, 192 lines from d4951f12_UMKAW947G6NUUYO725CC_7dbcd128_page_002.pdf
2025-09-24 18:29:50,276 - INFO - Successfully processed page 2
2025-09-24 18:29:50,355 - INFO - Page 1: Extracted 4110 characters, 187 lines from d4951f12_UMKAW947G6NUUYO725CC_7dbcd128_page_001.pdf
2025-09-24 18:29:50,355 - INFO - Successfully processed page 1
2025-09-24 18:29:50,355 - INFO - Combined 2 pages into final text
2025-09-24 18:29:50,355 - INFO - Text validation for d4951f12_UMKAW947G6NUUYO725CC: 8588 characters, 2 pages
2025-09-24 18:29:50,356 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:50,356 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:50,449 - INFO - Page 2: Extracted 535 characters, 40 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_002.pdf
2025-09-24 18:29:50,450 - INFO - Successfully processed page 2
2025-09-24 18:29:50,478 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5b2d1806_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:29:50,673 - INFO - Page 2: Extracted 3342 characters, 95 lines from e5b7e833_TWW70AYZ4HHN0G7UPMM7_a28aa619_page_002.pdf
2025-09-24 18:29:50,673 - INFO - Successfully processed page 2
2025-09-24 18:29:50,674 - INFO - Combined 3 pages into final text
2025-09-24 18:29:50,674 - INFO - Text validation for e5b7e833_TWW70AYZ4HHN0G7UPMM7: 5116 characters, 3 pages
2025-09-24 18:29:50,674 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:50,674 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:50,772 - INFO - Page 4: Extracted 502 characters, 36 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_004.pdf
2025-09-24 18:29:50,772 - INFO - Successfully processed page 4
2025-09-24 18:29:50,974 - INFO - Page 1: Extracted 1119 characters, 42 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_001.pdf
2025-09-24 18:29:50,975 - INFO - Successfully processed page 1
2025-09-24 18:29:51,073 - INFO - Page 5: Extracted 1435 characters, 22 lines from f643b150_US3F65B5JD70IE5R8PG9_b75d3819_page_005.pdf
2025-09-24 18:29:51,074 - INFO - Successfully processed page 5
2025-09-24 18:29:51,187 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3c18874c-1f2a-4030-8481-d7632b472fc5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:51 GMT', 'content-type': 'application/json', 'content-length': '1032', 'connection': 'keep-alive', 'x-amzn-requestid': '3c18874c-1f2a-4030-8481-d7632b472fc5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have two pages: page1 and page2.\n\nPage1: Text shows "INVOICE / FACTURE TRA0036104" and many invoice details. So doc_type: invoice. It\'s a carrier invoice. There\'s no HS code or customs. So classify as invoice.\n\nPage2: This page has "STRAIGHT BILL OF LADING ORIGINAL - NOT NEGOTIABLE" and details of BOL. So doc_type: bol.\n\nWe need to output via tool call classify_logistics_doc_type with documents array containing objects for each page.\n\nWe need to ensure exactly one per page, ascending page order. Provide page_no and doc_type.\n\nSo:\n\n[{page_no:1, doc_type:"invoice"}, {page_no:2, doc_type:"bol"}]\n\nWe must use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3935, 'outputTokens': 193, 'totalTokens': 4128}, 'metrics': {'latencyMs': 1124}}
2025-09-24 18:29:51,187 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/200caffe_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:51,245 - INFO - 

MVSX79LFMEJ1OI57DGBO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:29:51,246 - INFO - 

✓ Saved result: output/run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:29:51,264 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2dd8750c-d7bc-476e-a784-e914fb153e1d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:51 GMT', 'content-type': 'application/json', 'content-length': '942', 'connection': 'keep-alive', 'x-amzn-requestid': '2dd8750c-d7bc-476e-a784-e914fb153e1d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages of content. Let\'s identify type.\n\nPage 1: shows "INVOICE" header. It\'s a carrier invoice. Contains Invoice details, billed to, etc. It\'s a carrier invoice. No HS code, no customs, no lumper. So doc_type: invoice.\n\nPage 2: shows "BILL OF LADING" header. So doc_type: bol.\n\nThus we need to return an array of objects: each page with page_no and doc_type. Output via the tool classify_logistics_doc_type.\n\nWe must use tool. Provide JSON accordingly.\n\nLet\'s do: documents: [{page_no:1, doc_type:"invoice"}, {page_no:2, doc_type:"bol"}].\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3605, 'outputTokens': 175, 'totalTokens': 3780}, 'metrics': {'latencyMs': 1025}}
2025-09-24 18:29:51,264 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/91ae3348_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:51,353 - INFO - Page 3: Extracted 1520 characters, 38 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_003.pdf
2025-09-24 18:29:51,353 - INFO - Successfully processed page 3
2025-09-24 18:29:51,373 - INFO - Page 1: Extracted 644 characters, 46 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_001.pdf
2025-09-24 18:29:51,387 - INFO - Page 1: Extracted 1118 characters, 71 lines from f83c2fb7_ZNZPCNCYXXVUIEOEH7SH_772b753d_page_001.pdf
2025-09-24 18:29:51,387 - INFO - Successfully processed page 1
2025-09-24 18:29:51,387 - INFO - Successfully processed page 1
2025-09-24 18:29:51,391 - INFO - Combined 1 pages into final text
2025-09-24 18:29:51,391 - INFO - Text validation for f83c2fb7_ZNZPCNCYXXVUIEOEH7SH: 1135 characters, 1 pages
2025-09-24 18:29:51,392 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:51,392 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:51,533 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/200caffe_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:29:51,542 - INFO - Page 2: Extracted 2306 characters, 55 lines from f643b150_US3F65B5JD70IE5R8PG9_b75d3819_page_002.pdf
2025-09-24 18:29:51,543 - INFO - Successfully processed page 2
2025-09-24 18:29:51,545 - INFO - Combined 5 pages into final text
2025-09-24 18:29:51,547 - INFO - Text validation for f643b150_US3F65B5JD70IE5R8PG9: 6789 characters, 5 pages
2025-09-24 18:29:51,564 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:51,565 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:51,596 - INFO - 

SPKHMNJUMC10ZF1SJUVA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:29:51,596 - INFO - 

✓ Saved result: output/run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:29:51,852 - INFO - Page 6: Extracted 1476 characters, 75 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_006.pdf
2025-09-24 18:29:51,852 - INFO - Successfully processed page 6
2025-09-24 18:29:51,878 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/91ae3348_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:29:52,905 - INFO - Page 2: Extracted 1831 characters, 39 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_002.pdf
2025-09-24 18:29:52,906 - INFO - Successfully processed page 2
2025-09-24 18:29:52,909 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '91fcce97-df19-4a24-831c-22fe5e450baf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:52 GMT', 'content-type': 'application/json', 'content-length': '2071', 'connection': 'keep-alive', 'x-amzn-requestid': '91fcce97-df19-4a24-831c-22fe5e450baf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. We have 4 pages.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing each page object.\n\nWe need decide doc_type for each page.\n\nPage1: Looks like an invoice: has Invoice #, Bill to, amount, etc. It\'s a carrier invoice. No HS codes. So doc_type: invoice.\n\nPage2: Looks like continuation of page1? Actually page2 contains payment breakdown, terms, signatures, shipping instructions & rate confirmation. It\'s part of same invoice document? It says "Shipping Instructions & Rate Confirmation" and page 2 of 2. So it may be part of the same invoice? The earlier page1 is invoice. Page2 includes shipping instructions and rate confirmation. It might be a continuation of the invoice. We should classify each page individually. The content still has invoice info: payment, terms, signatures. Also shipping instructions. But doc_type should be same as previous page if continuation. The first page had invoice. Page2 starts with "Payment: Line Haul..." That indicates same invoice. So page2 should be invoice.\n\nPage3: Shipping Instructions & Rate Confirmation with details, order, pickup, delivery, weight. This is a rate confirmation. It has "Shipping Instructions & Rate Confirmation" header. So doc_type: rate_confirmation. Is it continuation? It says Page 1 of 2. So page3 is part of a rate confirmation document separate from invoice. So doc_type: rate_confirmation.\n\nPage4: Straight Bill of Lading short form. So doc_type: bol.\n\nThus outputs: page1 invoice, page2 invoice, page3 rate_confirmation, page4 bol.\n\nWe must output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"rate_confirmation"},{"page_no":4,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4563, 'outputTokens': 394, 'totalTokens': 4957}, 'metrics': {'latencyMs': 1938}}
2025-09-24 18:29:52,909 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4851d379_NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:52,940 - INFO - Page 7: Extracted 1440 characters, 24 lines from 97159a6b_EJJM2MNOTED9686H34VA_b79fb758_page_007.pdf
2025-09-24 18:29:52,943 - INFO - Successfully processed page 7
2025-09-24 18:29:52,955 - INFO - Combined 7 pages into final text
2025-09-24 18:29:52,956 - INFO - Text validation for 97159a6b_EJJM2MNOTED9686H34VA: 8077 characters, 7 pages
2025-09-24 18:29:52,961 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:29:52,962 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:29:53,002 - INFO - 

NTI160L8QH452446HMQV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:29:53,002 - INFO - 

✓ Saved result: output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:29:53,161 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9cd4db41-85ca-42d6-96f7-33b6a8383f8b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:53 GMT', 'content-type': 'application/json', 'content-length': '579', 'connection': 'keep-alive', 'x-amzn-requestid': '9cd4db41-85ca-42d6-96f7-33b6a8383f8b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. The text clearly shows "INVOICE" header, invoice number, invoice date, etc. There\'s no HS codes, customs details. So it\'s a carrier invoice, not commercial invoice. So doc_type = "invoice". Output using tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2922, 'outputTokens': 77, 'totalTokens': 2999}, 'metrics': {'latencyMs': 608}}
2025-09-24 18:29:53,161 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f83c2fb7_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:53,268 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a35230f4-735e-4abe-8884-68ca5ec869af', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:53 GMT', 'content-type': 'application/json', 'content-length': '1617', 'connection': 'keep-alive', 'x-amzn-requestid': 'a35230f4-735e-4abe-8884-68ca5ec869af'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Page1 appears to contain original invoice and bill of lading. Many headers: "ORIGINAL INVOICE", "Total Amount Due", "BILL OF LADING". This is a combined page: includes invoice and BOL. According to definitions, combined_carrier_documents: page containing multiple carrier documents bundled together. Keywords: multiple distinct document headers on one page, Bundle, Combined. Page1 has both invoice and BOL. So doc_type combined_carrier_documents.\n\nPage2 appears to be Delivery Receipt / Proof of Delivery. Contains "Delivery Receipt", "PRO-NUMBER", "Signature", etc. It\'s a POD. The header says "Delivery Receipt". So doc_type pod. It also includes BOL again but it is separate page. POD is a separate document. So page2 is pod.\n\nCheck continuation: Page1 ends with "Page 1/2". So page1 is page1 of 2. Page2 says "Page 2/2". So continuation but each page is part of different document? Page1 includes invoice and BOL; page2 includes delivery receipt. They are separate documents but part of same shipment. They are separate documents. So classify page1 as combined_carrier_documents. Page2 as pod. No continuation classification needed. Output each page with doc_type.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4953, 'outputTokens': 304, 'totalTokens': 5257}, 'metrics': {'latencyMs': 1650}}
2025-09-24 18:29:53,280 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d4951f12_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:53,293 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4851d379_NTI160L8QH452446HMQV.pdf
2025-09-24 18:29:53,304 - INFO - Page 5: Extracted 3946 characters, 133 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_005.pdf
2025-09-24 18:29:53,306 - INFO - Successfully processed page 5
2025-09-24 18:29:53,320 - INFO - 

ZNZPCNCYXXVUIEOEH7SH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:29:53,320 - INFO - 

✓ Saved result: output/run1_ZNZPCNCYXXVUIEOEH7SH.json
2025-09-24 18:29:53,397 - INFO - Page 3: Extracted 3655 characters, 106 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_003.pdf
2025-09-24 18:29:53,398 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '669734e6-9bee-4ed0-a235-dd73f64d3d4c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:53 GMT', 'content-type': 'application/json', 'content-length': '1387', 'connection': 'keep-alive', 'x-amzn-requestid': '669734e6-9bee-4ed0-a235-dd73f64d3d4c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We look at content.\n\nPage1: Contains "Freight Bill" header, "THIS IS YOUR INVOICE". Has invoice style, with "Please Pay $138.26". Also has BOL etc. It\'s a carrier invoice. It has "Bill of Lading: 873131". It includes invoice, due amount. So doc_type: invoice. It also has BOL but overall invoice. Not commercial invoice since no HS codes etc. So invoice.\n\nPage2: Looks like a Bill of Lading. Contains "UNIFORM STRAIGHT BILL OF LADING" and "Bill of Lading NO 873131". So doc_type: bol.\n\nPage3: "Delivery Receipt" header. "Delivery Receipt" is proof of delivery? It includes "Freight Bill No." but also "Delivery Receipt". It has sign driver etc. This is a POD? It has driver signature, date. So doc_type: pod.\n\nCheck if page3 is continuation? No. So separate.\n\nThus we output 3 objects.\n\nNeed to use tool classify_logistics_doc_type with array of objects: each page with page_no and doc_type.\n\nWe\'ll produce JSON array with objects.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4053, 'outputTokens': 282, 'totalTokens': 4335}, 'metrics': {'latencyMs': 1463}}
2025-09-24 18:29:53,398 - INFO - Successfully processed page 3
2025-09-24 18:29:53,399 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e5b7e833_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:53,461 - INFO - Page 6: Extracted 3053 characters, 112 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_006.pdf
2025-09-24 18:29:53,461 - INFO - Successfully processed page 6
2025-09-24 18:29:53,604 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f83c2fb7_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:29:53,689 - INFO - 

UMKAW947G6NUUYO725CC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 2,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 18:29:53,689 - INFO - 

✓ Saved result: output/run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:29:53,969 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d4951f12_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:29:54,021 - INFO - 

TWW70AYZ4HHN0G7UPMM7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        },
        {
            "page_no": 3,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 18:29:54,021 - INFO - 

✓ Saved result: output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:29:54,235 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1960a10b-26a2-4b2d-84b1-6536425fc6f8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:54 GMT', 'content-type': 'application/json', 'content-length': '3183', 'connection': 'keep-alive', 'x-amzn-requestid': '1960a10b-26a2-4b2d-84b1-6536425fc6f8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have pages 1-10 (page5-7 are blankish). Should classify each page.\n\nPage1: Invoice. Contains "Invoice #", "Bill To", etc. No HS codes. So type invoice.\n\nPage2: Notice of Assignment and Change of Payee letter. It\'s not a logistics doc; but might be considered other. It\'s a letter, not invoice. So other.\n\nPage3: BOL (Bill of Lading). It has BOL #: title. So bol.\n\nPage4: Another BOL page. Also bol. It has BOL #. So bol.\n\nPage5: empty. Should we classify as continuation? It\'s blank page. We check if continuation: no header. It\'s empty. According to rules: if not in categories and continuation of previous page? Does it start with "continued"? No. So we classify as other? But blank page likely part of same document but blank. But likely blank page that is part of BOL document. But we need to decide. The rule: "If document is not from any of categories mentioned, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign same doc_type as previous page." Blank page likely continuation of BOL. But does it have any indicator? No. But we might treat as other? The instruction: "If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page." So we need to determine if it\'s continuation. The page has no indicator but it\'s just blank. However, it is part of BOL but we can\'t be sure. But we can treat as continuation because BOL likely spans multiple pages and page5 shows nothing. We might decide to label as bol. But the rule: only if page starts with "continued" or pagination etc. But blank page doesn\'t show that. So classification: other.\n\nPage6: "9  if 01 / THE LIFE Line YOUR" Looks like some junk. Not any doc. So other.\n\nPage7: random characters. Other.\n\nPage8-9: Shipping Instructions & Rate Confirmation. This is rate_confirmation. It has "Shipping Instructions & Rate Confirmation". So rate_confirmation. It\'s a two-page document. Page8 and page9 both part of rate confirmation. So doc_type rate_confirmation for both.\n\nPage10: Notice of Release from factoring. Not a logistics doc. So other.\n\nThus list: page1 invoice, page2 other, page3 bol, page4 bol, page5 other, page6 other, page7 other, page8 rate_confirmation, page9 rate_confirmation, page10 other.\n\nReturn JSON array objects via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"},{"page_no":8,"doc_type":"rate_confirmation"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 5873, 'outputTokens': 684, 'totalTokens': 6557}, 'metrics': {'latencyMs': 2943}}
2025-09-24 18:29:54,235 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6bb91139_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:54,301 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e5b7e833_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:29:54,401 - INFO - 

J2HEKQMZPE7HUYJF14PH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "bol"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        },
        {
            "page_no": 5,
            "doc_type": "other"
        },
        {
            "page_no": 6,
            "doc_type": "other"
        },
        {
            "page_no": 7,
            "doc_type": "other"
        },
        {
            "page_no": 8,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 9,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 10,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:29:54,401 - INFO - 

✓ Saved result: output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:29:54,731 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6bb91139_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:29:57,371 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a6c18b73-c27f-44cc-b93e-3da8ff273803', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:54 GMT', 'content-type': 'application/json', 'content-length': '2670', 'connection': 'keep-alive', 'x-amzn-requestid': 'a6c18b73-c27f-44cc-b93e-3da8ff273803'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page with doc_type. We have 5 pages. Determine type.\n\nPage1: Looks like an invoice statement with "INV DATE", "INV #", "INV AMOUNT". It\'s a carrier invoice? Contains "PAYABLE TO", "NOTICE OF ASSIGNMENT". Keywords: Invoice, INV #, INV AMOUNT. Not commercial invoice because no HS codes etc. So type: invoice.\n\nPage2: It\'s a letter about assignment of payments, not invoice. It\'s a notice of assignment. Could be \'clear_to_pay\'? Actually it\'s an assignment letter, giving payment instructions. Not a payment authorization. It\'s more of a letter. Probably \'other\'? But maybe \'clear_to_pay\' is for approval for payment. This is a notice of assignment, telling where to send payments. Might be \'clear_to_pay\'? The doc has "Clear to Pay" stamp? It doesn\'t show. But includes instructions for ACH. It says "All payments must be sent...". That\'s more of a payment instruction letter. There\'s no payment authorization. I think \'other\'. But maybe \'clear_to_pay\' is an approval for payment, but this is a letter to instruct payment. Might still be \'clear_to_pay\'? The definition: Clear to Pay: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email. Keywords: "Clear to Pay", "Approved for Payment", "Payment Authorization". This doc doesn\'t have those. So \'other\'.\n\nPage3: This looks like an invoice again. It has "Invoice #", "Invoice Date", "Total Rate $2,200.00". So invoice.\n\nPage4: Shipping Instructions & Rate Confirmation. That\'s a rate confirmation. So doc_type: rate_confirmation. The page includes "Shipping Instructions & Rate Confirmation". So yes.\n\nPage5: Also part of rate confirmation. It\'s still the same document. So page5 should be same type: rate_confirmation.\n\nCheck if continuation: page4 says Page 1 of 2. page5 says Page 2 of 2. So indeed continuation. So both rate_confirmation.\n\nNow check if page2 maybe continuation? It is separate letter. Not continuation of page1. So classification stands.\n\nThus we output array of objects: each with page_no and doc_type.\n\nWe must call function classify_logistics_doc_type with documents array. Let\'s craft.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"rate_confirmation"},{"page_no":5,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4482, 'outputTokens': 539, 'totalTokens': 5021}, 'metrics': {'latencyMs': 2261}}
2025-09-24 18:29:57,371 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f643b150_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:57,432 - INFO - 

US3F65B5JD70IE5R8PG9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        },
        {
            "page_no": 4,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 5,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 18:29:57,432 - INFO - 

✓ Saved result: output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:29:57,741 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f643b150_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:29:58,874 - INFO - Page 10: Extracted 1217 characters, 39 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_010.pdf
2025-09-24 18:29:58,874 - INFO - Successfully processed page 10
2025-09-24 18:29:59,434 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6ce2fe57-1433-420a-b8be-0caa8518668c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:59:57 GMT', 'content-type': 'application/json', 'content-length': '3155', 'connection': 'keep-alive', 'x-amzn-requestid': '6ce2fe57-1433-420a-b8be-0caa8518668c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Pages 1-7.\n\nWe must check if page is continuation of previous page? Page1 says "Continued on next page". So page 1 continues on page2? Wait page1 says "Continued on next page" which indicates page1 is part of a multi-page document. So page1 and page2 are continuation. The content: page1 shows invoice header, page2 shows notice of assignment. The document appears to be an invoice. But page2 is assignment notice. The whole set likely is an invoice with assignment notice. So classify as invoice? But we need to consider doc type classification: invoice vs combined_carrier_documents or maybe assignment notice is separate. But the tool expects that each page gets a doc_type. However, rule: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." So if page1 and page2 are part of the same document, we classify both as invoice. But page2 content is assignment notice, but it\'s part of the invoice. So still invoice. So page1: invoice. page2: invoice.\n\nPage3: looks similar to page1 but without "continued". It is invoice page. So invoice.\n\nPage4: also invoice page. So invoice.\n\nPage5: just pickup/delivery info. That looks like a simple location list, maybe part of invoice? But it\'s a short page with pickup/delivery. It might be part of the invoice too. But we treat it as part of same document. So invoice.\n\nPage6: Shipping Instructions & Rate Confirmation. That is a Rate Confirmation. It\'s a separate document? But it\'s part of the same PDF? Yes. But we need to decide if this is part of the invoice or separate. The content shows "Shipping Instructions & Rate Confirmation", includes order, pickup date, etc. That is a rate confirmation. So maybe it\'s a separate document. But the same PDF contains both invoice and rate confirmation. According to rule: "If a page is part of a multi-page single document: each page still gets the same doc_type". But here we have two distinct document types in same PDF. But we should classify each page individually. So page6: rate_confirmation. Page7: continuation of page6? Page7 says "Page 2 of 2" so yes, page6 and page7 are continuation of same rate confirmation. So page6 and page7 both rate_confirmation.\n\nThus classification:\n\nPage1: invoice\nPage2: invoice\nPage3: invoice\nPage4: invoice\nPage5: invoice\nPage6: rate_confirmation\nPage7: rate_confirmation\n\nCheck if any other types? Maybe page5 is just pickup/delivery info but still invoice content. So invoice.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"invoice"},{"page_no":5,"doc_type":"invoice"},{"page_no":6,"doc_type":"rate_confirmation"},{"page_no":7,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4785, 'outputTokens': 655, 'totalTokens': 5440}, 'metrics': {'latencyMs': 2948}}
2025-09-24 18:29:59,434 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/97159a6b_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:29:59,515 - INFO - 

EJJM2MNOTED9686H34VA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        },
        {
            "page_no": 4,
            "doc_type": "invoice"
        },
        {
            "page_no": 5,
            "doc_type": "invoice"
        },
        {
            "page_no": 6,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 7,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 18:29:59,516 - INFO - 

✓ Saved result: output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:29:59,809 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/97159a6b_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:03,396 - INFO - Page 9: Extracted 1541 characters, 74 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_009.pdf
2025-09-24 18:30:03,396 - INFO - Successfully processed page 9
2025-09-24 18:30:05,371 - INFO - Page 8: Extracted 3132 characters, 115 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_008.pdf
2025-09-24 18:30:05,371 - INFO - Successfully processed page 8
2025-09-24 18:30:07,182 - INFO - Page 7: Extracted 3086 characters, 113 lines from a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON_c4d5dc2a_page_007.pdf
2025-09-24 18:30:07,182 - INFO - Successfully processed page 7
2025-09-24 18:30:07,183 - INFO - Combined 10 pages into final text
2025-09-24 18:30:07,183 - INFO - Text validation for a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON: 21501 characters, 10 pages
2025-09-24 18:30:07,184 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:30:07,184 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:30:13,206 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd51bc0e2-c33c-4b87-9e5c-188fb58c2325', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:00:13 GMT', 'content-type': 'application/json', 'content-length': '4692', 'connection': 'keep-alive', 'x-amzn-requestid': 'd51bc0e2-c33c-4b87-9e5c-188fb58c2325'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Let\'s identify. Page1: Invoice (carrier invoice). Keywords: Invoice, Invoice #, Amount Due. So doc_type invoice.\n\nPage2: Freight Bill. Contains "FREIGHT BILL" header. That is a Bill of Lading? Actually Freight Bill is a type of invoice? Wait Freight Bill is like a billing document. Could be invoice or "bol"? It has "FREIGHT BILL" maybe a type of invoice. The system says doc_type enum includes invoice, comm_invoice, lumper_receipt, bol, pod, etc. Freight Bill is maybe a "bol" but it\'s more of a bill. However "Freight Bill" is often an invoice for freight charges. But we have a header "FREIGHT BILL". I think it\'s a "bol"?? Wait bol is Bill of Lading, a transport document. Freight Bill is a billing document. It likely is an invoice. But we already have page1 invoice. Page2 might be part of same invoice? It includes "FREIGHT BILL" and shows charges. It might be a second page of same invoice. But doc_type might be invoice. Let\'s examine: Page1 is Invoice. Page2: FREIGHT BILL header, shows charges, total $509.52. Similar to page1. So likely still invoice. But maybe it\'s a combined page? We need to decide. The doc_type "invoice" applies. I\'d classify page2 as invoice.\n\nPage3: Bill of Lading - Short Form. So doc_type bol. It\'s a BOL.\n\nPage4: Also Freight Bill? It has "FREIGHT BILL" header again. Similar to page2. Might be part of same invoice? But separate BOL? Let\'s check content: It lists "SHIPPERS NO.", "Consignee". This looks like BOL. But header says FREIGHT BILL. However includes details of BOL like "TRAILER NO". Might be a BOL as well. We need to look at keywords. It has "FREIGHT BILL" header but also "SHIPPERS NO." and "Consignee". It\'s a BOL. I think doc_type bol.\n\nPage5: Straight Bill Of Lading - Short Form. So bol.\n\nPage6-8: BILL OF LADING pages 1-3. So bol.\n\nPage9-10: Shipping Instructions & Rate Confirmation. That\'s a rate confirmation. So doc_type rate_confirmation.\n\nNow we must check continuation: Page2 may be continuation of page1? It starts with "ORIGINAL" and "FREIGHT BILL". Does page1 end with "Thank you for your business." Then page2 starts with "ORIGINAL". That might be a separate document. But perhaps page2 is a combined document containing invoice and freight bill? However we treat each page individually. But rule: If page starts with "continued" etc. We need to check if continuation. Page2 doesn\'t start with "continued". But it starts with "ORIGINAL". So it\'s not a continuation. So page2 is separate doc_type invoice. But we need to decide if it\'s invoice or bol. Let\'s see if it contains "Bill of Lading" text? No. It says "FREIGHT BILL". That is an invoice. So doc_type invoice.\n\nPage4: starts with "ORIGINAL" and "FREIGHT BILL". So similar to page2. So invoice.\n\nBut page4 also includes "SHIPPERS NO." etc. Maybe still invoice. But likely a freight bill. I\'d classify as invoice.\n\nThus mapping:\n\nPage1: invoice\nPage2: invoice\nPage3: bol\nPage4: invoice? Wait page4 has BOL-like details but header says FREIGHT BILL. Maybe it\'s a BOL. But "FREIGHT BILL" might be a type of BOL? Let\'s see doc_type "bol" is Bill of Lading. The page includes "PRO. NO." and "TRAILER NO." and "SHIPPERS NO." and "Consignee". That\'s typical of a BOL. Usually BOL includes "BOL #". But here "FREIGHT BILL". Could be a BOL. I\'ll classify page4 as bol to capture BOL.\n\nBut we must be consistent: page3 is BOL. page4 also BOL. page5 BOL. page6-8 BOL.\n\nPage9-10: rate_confirmation.\n\nDouble-check page2 vs page4: page2 includes "P.O. NO." "Consignee" "Destination" "Shipper". It also has "Total Charges". The header "FREIGHT BILL". That is a freight bill but also could be BOL. The difference: BOL usually includes "BOL #", "Ship Date". page2 doesn\'t show BOL #. It\'s more a freight bill. So invoice.\n\nThus final classification list.\n\nWe need to output JSON with array of objects with page_no and doc_type. Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"bol"},{"page_no":7,"doc_type":"bol"},{"page_no":8,"doc_type":"bol"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 8366, 'outputTokens': 1125, 'totalTokens': 9491}, 'metrics': {'latencyMs': 4710}}
2025-09-24 18:30:13,206 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:13,369 - INFO - 

ZT8R6RF8DXQOQ3Q4N7ON.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "bol"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        },
        {
            "page_no": 5,
            "doc_type": "bol"
        },
        {
            "page_no": 6,
            "doc_type": "bol"
        },
        {
            "page_no": 7,
            "doc_type": "bol"
        },
        {
            "page_no": 8,
            "doc_type": "bol"
        },
        {
            "page_no": 9,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 10,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 18:30:13,369 - INFO - 

✓ Saved result: output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,647 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a1c86e45_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,651 - INFO - 
📊 Processing Summary:
2025-09-24 18:30:14,651 - INFO -    Total files: 18
2025-09-24 18:30:14,651 - INFO -    Successful: 18
2025-09-24 18:30:14,652 - INFO -    Failed: 0
2025-09-24 18:30:14,652 - INFO -    Duration: 44.93 seconds
2025-09-24 18:30:14,652 - INFO -    Output directory: output
2025-09-24 18:30:14,652 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:30:14,652 - INFO -    📄 CHU18SQU5Q2XCX57ABTP.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"}]}
2025-09-24 18:30:14,652 - INFO -    📄 CMGYRKEPEEGIJHOFEAQ9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,652 - INFO -    📄 DKI9846PWRKYUQA0DQ4A.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,652 - INFO -    📄 DTOGYRLS96ZKXQOO6EY5.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,652 - INFO -    📄 EJJM2MNOTED9686H34VA.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"invoice"},{"page_no":5,"doc_type":"invoice"},{"page_no":6,"doc_type":"rate_confirmation"},{"page_no":7,"doc_type":"rate_confirmation"}]}
2025-09-24 18:30:14,652 - INFO -    📄 ESRNPUZ2WJ48Y3NTAX21.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:30:14,652 - INFO -    📄 IGQ6ARKKVIX04G5ZQDJ5.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,652 - INFO -    📄 J2HEKQMZPE7HUYJF14PH.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"},{"page_no":8,"doc_type":"rate_confirmation"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"other"}]}
2025-09-24 18:30:14,652 - INFO -    📄 MVSX79LFMEJ1OI57DGBO.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:30:14,652 - INFO -    📄 NTI160L8QH452446HMQV.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"rate_confirmation"},{"page_no":4,"doc_type":"bol"}]}
2025-09-24 18:30:14,652 - INFO -    📄 SPKHMNJUMC10ZF1SJUVA.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:30:14,652 - INFO -    📄 TAIJ9HY2DEXLZTHT5SXH.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,653 - INFO -    📄 TWW70AYZ4HHN0G7UPMM7.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"pod"}]}
2025-09-24 18:30:14,653 - INFO -    📄 UMKAW947G6NUUYO725CC.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"pod"}]}
2025-09-24 18:30:14,653 - INFO -    📄 US3F65B5JD70IE5R8PG9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"rate_confirmation"},{"page_no":5,"doc_type":"rate_confirmation"}]}
2025-09-24 18:30:14,653 - INFO -    📄 W1P39VXY7XRKDJG29JXQ.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,653 - INFO -    📄 ZNZPCNCYXXVUIEOEH7SH.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:30:14,653 - INFO -    📄 ZT8R6RF8DXQOQ3Q4N7ON.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"bol"},{"page_no":7,"doc_type":"bol"},{"page_no":8,"doc_type":"bol"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"rate_confirmation"}]}
2025-09-24 18:30:14,654 - INFO - 
============================================================================================================================================
2025-09-24 18:30:14,654 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:30:14,654 - INFO - ============================================================================================================================================
2025-09-24 18:30:14,654 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:30:14,654 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:30:14,654 - INFO - CHU18SQU5Q2XCX57ABTP.pdf                           1      invoice                                  run1_CHU18SQU5Q2XCX57ABTP.json                                                  
2025-09-24 18:30:14,654 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:30:14,654 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:30:14,654 - INFO - 
2025-09-24 18:30:14,654 - INFO - CHU18SQU5Q2XCX57ABTP.pdf                           2      invoice                                  run1_CHU18SQU5Q2XCX57ABTP.json                                                  
2025-09-24 18:30:14,654 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:30:14,654 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:30:14,654 - INFO - 
2025-09-24 18:30:14,654 - INFO - CMGYRKEPEEGIJHOFEAQ9.pdf                           1      invoice                                  run1_CMGYRKEPEEGIJHOFEAQ9.json                                                  
2025-09-24 18:30:14,654 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:30:14,654 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CMGYRKEPEEGIJHOFEAQ9.json
2025-09-24 18:30:14,655 - INFO - 
2025-09-24 18:30:14,655 - INFO - DKI9846PWRKYUQA0DQ4A.pdf                           1      invoice                                  run1_DKI9846PWRKYUQA0DQ4A.json                                                  
2025-09-24 18:30:14,655 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:30:14,655 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DKI9846PWRKYUQA0DQ4A.json
2025-09-24 18:30:14,655 - INFO - 
2025-09-24 18:30:14,655 - INFO - DTOGYRLS96ZKXQOO6EY5.pdf                           1      invoice                                  run1_DTOGYRLS96ZKXQOO6EY5.json                                                  
2025-09-24 18:30:14,655 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:30:14,655 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTOGYRLS96ZKXQOO6EY5.json
2025-09-24 18:30:14,655 - INFO - 
2025-09-24 18:30:14,655 - INFO - EJJM2MNOTED9686H34VA.pdf                           1      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,655 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,655 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,655 - INFO - 
2025-09-24 18:30:14,655 - INFO - EJJM2MNOTED9686H34VA.pdf                           2      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,655 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,655 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,655 - INFO - 
2025-09-24 18:30:14,655 - INFO - EJJM2MNOTED9686H34VA.pdf                           3      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,656 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,656 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,656 - INFO - 
2025-09-24 18:30:14,656 - INFO - EJJM2MNOTED9686H34VA.pdf                           4      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,656 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,656 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,656 - INFO - 
2025-09-24 18:30:14,656 - INFO - EJJM2MNOTED9686H34VA.pdf                           5      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,656 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,656 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,656 - INFO - 
2025-09-24 18:30:14,656 - INFO - EJJM2MNOTED9686H34VA.pdf                           6      rate_confirmation                        run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,656 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,656 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,656 - INFO - 
2025-09-24 18:30:14,656 - INFO - EJJM2MNOTED9686H34VA.pdf                           7      rate_confirmation                        run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:30:14,656 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:30:14,656 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,656 - INFO - 
2025-09-24 18:30:14,656 - INFO - ESRNPUZ2WJ48Y3NTAX21.pdf                           1      invoice                                  run1_ESRNPUZ2WJ48Y3NTAX21.json                                                  
2025-09-24 18:30:14,656 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:30:14,657 - INFO - 
2025-09-24 18:30:14,657 - INFO - ESRNPUZ2WJ48Y3NTAX21.pdf                           2      bol                                      run1_ESRNPUZ2WJ48Y3NTAX21.json                                                  
2025-09-24 18:30:14,657 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:30:14,657 - INFO - 
2025-09-24 18:30:14,657 - INFO - IGQ6ARKKVIX04G5ZQDJ5.pdf                           1      invoice                                  run1_IGQ6ARKKVIX04G5ZQDJ5.json                                                  
2025-09-24 18:30:14,657 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IGQ6ARKKVIX04G5ZQDJ5.json
2025-09-24 18:30:14,657 - INFO - 
2025-09-24 18:30:14,657 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           1      invoice                                  run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,657 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,657 - INFO - 
2025-09-24 18:30:14,657 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           2      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,657 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,657 - INFO - 
2025-09-24 18:30:14,657 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           3      bol                                      run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,657 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,657 - INFO - 
2025-09-24 18:30:14,657 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           4      bol                                      run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,657 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,657 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,658 - INFO - 
2025-09-24 18:30:14,658 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           5      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,658 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,658 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,658 - INFO - 
2025-09-24 18:30:14,658 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           6      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,658 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,658 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,658 - INFO - 
2025-09-24 18:30:14,658 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           7      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,658 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,658 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,658 - INFO - 
2025-09-24 18:30:14,658 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           8      rate_confirmation                        run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,658 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,658 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,658 - INFO - 
2025-09-24 18:30:14,658 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           9      rate_confirmation                        run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,658 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,658 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,658 - INFO - 
2025-09-24 18:30:14,659 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           10     other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,659 - INFO - 
2025-09-24 18:30:14,659 - INFO - MVSX79LFMEJ1OI57DGBO.pdf                           1      invoice                                  run1_MVSX79LFMEJ1OI57DGBO.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:30:14,659 - INFO - 
2025-09-24 18:30:14,659 - INFO - MVSX79LFMEJ1OI57DGBO.pdf                           2      bol                                      run1_MVSX79LFMEJ1OI57DGBO.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:30:14,659 - INFO - 
2025-09-24 18:30:14,659 - INFO - NTI160L8QH452446HMQV.pdf                           1      invoice                                  run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,659 - INFO - 
2025-09-24 18:30:14,659 - INFO - NTI160L8QH452446HMQV.pdf                           2      invoice                                  run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,659 - INFO - 
2025-09-24 18:30:14,659 - INFO - NTI160L8QH452446HMQV.pdf                           3      rate_confirmation                        run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,659 - INFO - 
2025-09-24 18:30:14,659 - INFO - NTI160L8QH452446HMQV.pdf                           4      bol                                      run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:30:14,659 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:30:14,659 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,660 - INFO - 
2025-09-24 18:30:14,660 - INFO - SPKHMNJUMC10ZF1SJUVA.pdf                           1      invoice                                  run1_SPKHMNJUMC10ZF1SJUVA.json                                                  
2025-09-24 18:30:14,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:30:14,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:30:14,660 - INFO - 
2025-09-24 18:30:14,660 - INFO - SPKHMNJUMC10ZF1SJUVA.pdf                           2      bol                                      run1_SPKHMNJUMC10ZF1SJUVA.json                                                  
2025-09-24 18:30:14,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:30:14,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:30:14,660 - INFO - 
2025-09-24 18:30:14,660 - INFO - TAIJ9HY2DEXLZTHT5SXH.pdf                           1      invoice                                  run1_TAIJ9HY2DEXLZTHT5SXH.json                                                  
2025-09-24 18:30:14,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:30:14,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TAIJ9HY2DEXLZTHT5SXH.json
2025-09-24 18:30:14,660 - INFO - 
2025-09-24 18:30:14,660 - INFO - TWW70AYZ4HHN0G7UPMM7.pdf                           1      invoice                                  run1_TWW70AYZ4HHN0G7UPMM7.json                                                  
2025-09-24 18:30:14,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:30:14,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:30:14,660 - INFO - 
2025-09-24 18:30:14,660 - INFO - TWW70AYZ4HHN0G7UPMM7.pdf                           2      bol                                      run1_TWW70AYZ4HHN0G7UPMM7.json                                                  
2025-09-24 18:30:14,660 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:30:14,660 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:30:14,660 - INFO - 
2025-09-24 18:30:14,660 - INFO - TWW70AYZ4HHN0G7UPMM7.pdf                           3      pod                                      run1_TWW70AYZ4HHN0G7UPMM7.json                                                  
2025-09-24 18:30:14,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:30:14,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:30:14,661 - INFO - 
2025-09-24 18:30:14,661 - INFO - UMKAW947G6NUUYO725CC.pdf                           1      combined_carrier_d...                    run1_UMKAW947G6NUUYO725CC.json                                                  
2025-09-24 18:30:14,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:30:14,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:30:14,661 - INFO - 
2025-09-24 18:30:14,661 - INFO - UMKAW947G6NUUYO725CC.pdf                           2      pod                                      run1_UMKAW947G6NUUYO725CC.json                                                  
2025-09-24 18:30:14,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:30:14,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:30:14,661 - INFO - 
2025-09-24 18:30:14,661 - INFO - US3F65B5JD70IE5R8PG9.pdf                           1      invoice                                  run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:30:14,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:30:14,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,661 - INFO - 
2025-09-24 18:30:14,661 - INFO - US3F65B5JD70IE5R8PG9.pdf                           2      other                                    run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:30:14,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:30:14,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,661 - INFO - 
2025-09-24 18:30:14,661 - INFO - US3F65B5JD70IE5R8PG9.pdf                           3      invoice                                  run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:30:14,661 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:30:14,661 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,661 - INFO - 
2025-09-24 18:30:14,662 - INFO - US3F65B5JD70IE5R8PG9.pdf                           4      rate_confirmation                        run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:30:14,662 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:30:14,662 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,662 - INFO - 
2025-09-24 18:30:14,662 - INFO - US3F65B5JD70IE5R8PG9.pdf                           5      rate_confirmation                        run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:30:14,662 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:30:14,662 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,662 - INFO - 
2025-09-24 18:30:14,662 - INFO - W1P39VXY7XRKDJG29JXQ.pdf                           1      invoice                                  run1_W1P39VXY7XRKDJG29JXQ.json                                                  
2025-09-24 18:30:14,662 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:30:14,662 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W1P39VXY7XRKDJG29JXQ.json
2025-09-24 18:30:14,662 - INFO - 
2025-09-24 18:30:14,662 - INFO - ZNZPCNCYXXVUIEOEH7SH.pdf                           1      invoice                                  run1_ZNZPCNCYXXVUIEOEH7SH.json                                                  
2025-09-24 18:30:14,662 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:30:14,662 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZNZPCNCYXXVUIEOEH7SH.json
2025-09-24 18:30:14,662 - INFO - 
2025-09-24 18:30:14,662 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           1      invoice                                  run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,662 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,662 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,662 - INFO - 
2025-09-24 18:30:14,662 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           2      invoice                                  run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,663 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,663 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,663 - INFO - 
2025-09-24 18:30:14,663 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           3      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,663 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,663 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,663 - INFO - 
2025-09-24 18:30:14,663 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           4      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,663 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,663 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,663 - INFO - 
2025-09-24 18:30:14,663 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           5      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,663 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,663 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,663 - INFO - 
2025-09-24 18:30:14,663 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           6      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,663 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,663 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,663 - INFO - 
2025-09-24 18:30:14,663 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           7      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,663 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,663 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,663 - INFO - 
2025-09-24 18:30:14,664 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           8      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,664 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,664 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,664 - INFO - 
2025-09-24 18:30:14,664 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           9      rate_confirmation                        run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,664 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,664 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,664 - INFO - 
2025-09-24 18:30:14,664 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           10     rate_confirmation                        run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:30:14,664 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:30:14,664 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,664 - INFO - 
2025-09-24 18:30:14,664 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:30:14,664 - INFO - Total entries: 56
2025-09-24 18:30:14,664 - INFO - ============================================================================================================================================
2025-09-24 18:30:14,664 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:30:14,664 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:30:14,664 - INFO -   1. CHU18SQU5Q2XCX57ABTP.pdf            Page 1   → invoice         | run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:30:14,664 - INFO -   2. CHU18SQU5Q2XCX57ABTP.pdf            Page 2   → invoice         | run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:30:14,664 - INFO -   3. CMGYRKEPEEGIJHOFEAQ9.pdf            Page 1   → invoice         | run1_CMGYRKEPEEGIJHOFEAQ9.json
2025-09-24 18:30:14,664 - INFO -   4. DKI9846PWRKYUQA0DQ4A.pdf            Page 1   → invoice         | run1_DKI9846PWRKYUQA0DQ4A.json
2025-09-24 18:30:14,664 - INFO -   5. DTOGYRLS96ZKXQOO6EY5.pdf            Page 1   → invoice         | run1_DTOGYRLS96ZKXQOO6EY5.json
2025-09-24 18:30:14,664 - INFO -   6. EJJM2MNOTED9686H34VA.pdf            Page 1   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,664 - INFO -   7. EJJM2MNOTED9686H34VA.pdf            Page 2   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,664 - INFO -   8. EJJM2MNOTED9686H34VA.pdf            Page 3   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,664 - INFO -   9. EJJM2MNOTED9686H34VA.pdf            Page 4   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,664 - INFO -  10. EJJM2MNOTED9686H34VA.pdf            Page 5   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,665 - INFO -  11. EJJM2MNOTED9686H34VA.pdf            Page 6   → rate_confirmation | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,665 - INFO -  12. EJJM2MNOTED9686H34VA.pdf            Page 7   → rate_confirmation | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:30:14,665 - INFO -  13. ESRNPUZ2WJ48Y3NTAX21.pdf            Page 1   → invoice         | run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:30:14,665 - INFO -  14. ESRNPUZ2WJ48Y3NTAX21.pdf            Page 2   → bol             | run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:30:14,665 - INFO -  15. IGQ6ARKKVIX04G5ZQDJ5.pdf            Page 1   → invoice         | run1_IGQ6ARKKVIX04G5ZQDJ5.json
2025-09-24 18:30:14,665 - INFO -  16. J2HEKQMZPE7HUYJF14PH.pdf            Page 1   → invoice         | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  17. J2HEKQMZPE7HUYJF14PH.pdf            Page 2   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  18. J2HEKQMZPE7HUYJF14PH.pdf            Page 3   → bol             | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  19. J2HEKQMZPE7HUYJF14PH.pdf            Page 4   → bol             | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  20. J2HEKQMZPE7HUYJF14PH.pdf            Page 5   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  21. J2HEKQMZPE7HUYJF14PH.pdf            Page 6   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  22. J2HEKQMZPE7HUYJF14PH.pdf            Page 7   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  23. J2HEKQMZPE7HUYJF14PH.pdf            Page 8   → rate_confirmation | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  24. J2HEKQMZPE7HUYJF14PH.pdf            Page 9   → rate_confirmation | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  25. J2HEKQMZPE7HUYJF14PH.pdf            Page 10  → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:30:14,665 - INFO -  26. MVSX79LFMEJ1OI57DGBO.pdf            Page 1   → invoice         | run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:30:14,665 - INFO -  27. MVSX79LFMEJ1OI57DGBO.pdf            Page 2   → bol             | run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:30:14,666 - INFO -  28. NTI160L8QH452446HMQV.pdf            Page 1   → invoice         | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,666 - INFO -  29. NTI160L8QH452446HMQV.pdf            Page 2   → invoice         | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,666 - INFO -  30. NTI160L8QH452446HMQV.pdf            Page 3   → rate_confirmation | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,666 - INFO -  31. NTI160L8QH452446HMQV.pdf            Page 4   → bol             | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:30:14,666 - INFO -  32. SPKHMNJUMC10ZF1SJUVA.pdf            Page 1   → invoice         | run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:30:14,666 - INFO -  33. SPKHMNJUMC10ZF1SJUVA.pdf            Page 2   → bol             | run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:30:14,666 - INFO -  34. TAIJ9HY2DEXLZTHT5SXH.pdf            Page 1   → invoice         | run1_TAIJ9HY2DEXLZTHT5SXH.json
2025-09-24 18:30:14,666 - INFO -  35. TWW70AYZ4HHN0G7UPMM7.pdf            Page 1   → invoice         | run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:30:14,666 - INFO -  36. TWW70AYZ4HHN0G7UPMM7.pdf            Page 2   → bol             | run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:30:14,666 - INFO -  37. TWW70AYZ4HHN0G7UPMM7.pdf            Page 3   → pod             | run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:30:14,666 - INFO -  38. UMKAW947G6NUUYO725CC.pdf            Page 1   → combined_carrier_documents | run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:30:14,666 - INFO -  39. UMKAW947G6NUUYO725CC.pdf            Page 2   → pod             | run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:30:14,666 - INFO -  40. US3F65B5JD70IE5R8PG9.pdf            Page 1   → invoice         | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,666 - INFO -  41. US3F65B5JD70IE5R8PG9.pdf            Page 2   → other           | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,667 - INFO -  42. US3F65B5JD70IE5R8PG9.pdf            Page 3   → invoice         | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,667 - INFO -  43. US3F65B5JD70IE5R8PG9.pdf            Page 4   → rate_confirmation | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,667 - INFO -  44. US3F65B5JD70IE5R8PG9.pdf            Page 5   → rate_confirmation | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:30:14,667 - INFO -  45. W1P39VXY7XRKDJG29JXQ.pdf            Page 1   → invoice         | run1_W1P39VXY7XRKDJG29JXQ.json
2025-09-24 18:30:14,667 - INFO -  46. ZNZPCNCYXXVUIEOEH7SH.pdf            Page 1   → invoice         | run1_ZNZPCNCYXXVUIEOEH7SH.json
2025-09-24 18:30:14,667 - INFO -  47. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 1   → invoice         | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  48. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 2   → invoice         | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  49. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 3   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  50. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 4   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  51. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 5   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  52. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 6   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  53. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 7   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  54. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 8   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  55. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 9   → rate_confirmation | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO -  56. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 10  → rate_confirmation | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:30:14,667 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:30:14,668 - INFO - 
✅ Test completed: {'total_files': 18, 'processed': 18, 'failed': 0, 'errors': [], 'duration_seconds': 44.93199, 'processed_files': [{'filename': 'CHU18SQU5Q2XCX57ABTP.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf'}, {'filename': 'CMGYRKEPEEGIJHOFEAQ9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CMGYRKEPEEGIJHOFEAQ9.pdf'}, {'filename': 'DKI9846PWRKYUQA0DQ4A.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DKI9846PWRKYUQA0DQ4A.pdf'}, {'filename': 'DTOGYRLS96ZKXQOO6EY5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DTOGYRLS96ZKXQOO6EY5.pdf'}, {'filename': 'EJJM2MNOTED9686H34VA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}, {'page_no': 4, 'doc_type': 'invoice'}, {'page_no': 5, 'doc_type': 'invoice'}, {'page_no': 6, 'doc_type': 'rate_confirmation'}, {'page_no': 7, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf'}, {'filename': 'ESRNPUZ2WJ48Y3NTAX21.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf'}, {'filename': 'IGQ6ARKKVIX04G5ZQDJ5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/IGQ6ARKKVIX04G5ZQDJ5.pdf'}, {'filename': 'J2HEKQMZPE7HUYJF14PH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'bol'}, {'page_no': 4, 'doc_type': 'bol'}, {'page_no': 5, 'doc_type': 'other'}, {'page_no': 6, 'doc_type': 'other'}, {'page_no': 7, 'doc_type': 'other'}, {'page_no': 8, 'doc_type': 'rate_confirmation'}, {'page_no': 9, 'doc_type': 'rate_confirmation'}, {'page_no': 10, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf'}, {'filename': 'MVSX79LFMEJ1OI57DGBO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf'}, {'filename': 'NTI160L8QH452446HMQV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}, {'page_no': 4, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf'}, {'filename': 'SPKHMNJUMC10ZF1SJUVA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf'}, {'filename': 'TAIJ9HY2DEXLZTHT5SXH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TAIJ9HY2DEXLZTHT5SXH.pdf'}, {'filename': 'TWW70AYZ4HHN0G7UPMM7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}, {'page_no': 3, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf'}, {'filename': 'UMKAW947G6NUUYO725CC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}, {'page_no': 2, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf'}, {'filename': 'US3F65B5JD70IE5R8PG9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'invoice'}, {'page_no': 4, 'doc_type': 'rate_confirmation'}, {'page_no': 5, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf'}, {'filename': 'W1P39VXY7XRKDJG29JXQ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/W1P39VXY7XRKDJG29JXQ.pdf'}, {'filename': 'ZNZPCNCYXXVUIEOEH7SH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZNZPCNCYXXVUIEOEH7SH.pdf'}, {'filename': 'ZT8R6RF8DXQOQ3Q4N7ON.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'bol'}, {'page_no': 4, 'doc_type': 'bol'}, {'page_no': 5, 'doc_type': 'bol'}, {'page_no': 6, 'doc_type': 'bol'}, {'page_no': 7, 'doc_type': 'bol'}, {'page_no': 8, 'doc_type': 'bol'}, {'page_no': 9, 'doc_type': 'rate_confirmation'}, {'page_no': 10, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf'}]}
