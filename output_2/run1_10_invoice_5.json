{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 7.290168468898628e-09, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "bc1e7570-86d5-47aa-8925-e6f3137f90a5", "Relationships": [{"Type": "CHILD", "Ids": ["ec5180f9-ce62-4f0b-9dc6-55b9c2935ee5", "ccffcb45-56b3-4c5b-931f-1e282cfde984", "ed2f7061-f65b-42aa-b104-edd6036ebb4f", "6c22e4d1-cc58-4398-9011-e5d6ceca9f2f", "03c6fcb7-4dbd-423a-8307-e3de6182e222", "af75beb1-9f28-4e64-a14c-e6f2874c533b", "c2c5747b-492d-451a-ae23-743657fc4f3c", "5463d54a-eba9-4e3b-9e63-0f92544bed8a", "cb0383c6-68ae-4243-90d8-a6f268cb32c4", "45c81f68-8a20-46cf-9147-f44d8ff4e88a", "31a694b1-c064-4dde-bc76-af4452d43602", "79861188-f34f-42e3-a76e-2ec166810b6a", "9c79bf71-c027-442d-80a6-65e090803389", "9caf88fc-7462-48bb-862c-bffadf95af5a", "d5e3283b-5641-440f-96f2-1e466d0bdaea", "a7c11db1-d598-434e-874e-b3b8a3595e9a", "8af92bc7-c43f-4b5e-a260-c1d2b46ac6e9", "65146849-97b4-4ca8-9dc5-e7d45fc3b109", "ae77a550-3967-414e-b77f-24fcdb276790", "ccb0fd68-0b5a-4704-8b1e-6e6eadbb9b93", "0ea03aa5-5fa6-4794-8406-a2c9e521a5cd", "7ef413ae-96b7-4100-a4fc-bc72f2182575", "3bc526ec-ad59-4923-9872-ae35c60adad8", "41a5fc51-d6a5-43d6-bc33-a76e30190f8a", "b40ddc70-2bd1-448a-8a71-89796c3927de", "6a063b7d-2c0f-4c65-ad86-0ac9b03402b8", "8c3257bc-a310-451b-9b68-0213b493c94f", "03ef41a5-6045-4f94-80cb-1bbb8d6b0906", "9fc04d87-0e69-4e6a-b051-b628426f8380", "0b91e18f-31ca-47bb-9b65-dcac028a2073", "36d712b2-f78a-4f92-b3fb-b6be051aa65d", "c512c90b-bc98-4e1d-9754-2475f4c8a8fa", "939b0355-cc59-4ed7-adee-218dde3f6841", "e30008a9-de4d-4f10-96bd-f98a1e21e9fd", "e4ef48d5-d932-4a2d-9012-dd937126f301", "8e9bdee1-6c8b-4002-825d-eef120a7a4c4", "351783f9-e820-4d13-91a4-1049909331b9", "1b71e923-ddc6-4479-bfa5-28b6ec12cbaf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Invoice", "Geometry": {"BoundingBox": {"Width": 0.12167340517044067, "Height": 0.02087762951850891, "Left": 0.570727527141571, "Top": 0.08082494884729385}, "Polygon": [{"X": 0.5707277655601501, "Y": 0.08082494884729385}, {"X": 0.6924009323120117, "Y": 0.08082568645477295}, {"X": 0.6924007534980774, "Y": 0.10170257836580276}, {"X": 0.570727527141571, "Y": 0.10170190781354904}]}, "Id": "ec5180f9-ce62-4f0b-9dc6-55b9c2935ee5", "Relationships": [{"Type": "CHILD", "Ids": ["dd4c3b5e-802f-409f-b190-cbc1bee8bdf6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date:", "Geometry": {"BoundingBox": {"Width": 0.03961382433772087, "Height": 0.010407843627035618, "Left": 0.5694351196289062, "Top": 0.14273084700107574}, "Polygon": [{"X": 0.569435179233551, "Y": 0.14273084700107574}, {"X": 0.6090489029884338, "Y": 0.14273101091384888}, {"X": 0.6090488433837891, "Y": 0.1531386822462082}, {"X": 0.5694351196289062, "Y": 0.15313851833343506}]}, "Id": "ccffcb45-56b3-4c5b-931f-1e282cfde984", "Relationships": [{"Type": "CHILD", "Ids": ["aa8b08a1-e466-4f4e-9b72-77e53ba17272"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "08/06/2025", "Geometry": {"BoundingBox": {"Width": 0.0869469866156578, "Height": 0.0107219023630023, "Left": 0.735404908657074, "Top": 0.14261667430400848}, "Polygon": [{"X": 0.7354050278663635, "Y": 0.14261667430400848}, {"X": 0.82235187292099, "Y": 0.14261704683303833}, {"X": 0.8223518133163452, "Y": 0.15333856642246246}, {"X": 0.735404908657074, "Y": 0.153338223695755}]}, "Id": "ed2f7061-f65b-42aa-b104-edd6036ebb4f", "Relationships": [{"Type": "CHILD", "Ids": ["0167f879-7d2c-4524-a25f-8e517504d03c"]}]}, {"BlockType": "LINE", "Confidence": 99.29615783691406, "Text": "E & R FREIGHT TRUCKING LLC", "Geometry": {"BoundingBox": {"Width": 0.2536512315273285, "Height": 0.010301666334271431, "Left": 0.09881647676229477, "Top": 0.1601887047290802}, "Polygon": [{"X": 0.09881659597158432, "Y": 0.1601887047290802}, {"X": 0.35246771574020386, "Y": 0.1601896733045578}, {"X": 0.3524675965309143, "Y": 0.17049036920070648}, {"X": 0.09881647676229477, "Y": 0.17048947513103485}]}, "Id": "6c22e4d1-cc58-4398-9011-e5d6ceca9f2f", "Relationships": [{"Type": "CHILD", "Ids": ["1f725dfe-a894-46f6-8815-9282cdea2288", "1e9e1fa9-7463-4c1a-8b75-168e140ffcc9", "9e9af256-e781-41e8-99f2-630cd61f866a", "46eb9c3c-767b-4302-8dde-354c46a70297", "e90189a2-1f7d-4331-b713-3011188351ae"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "Invoice No.:", "Geometry": {"BoundingBox": {"Width": 0.08959117531776428, "Height": 0.010506520047783852, "Left": 0.5699952244758606, "Top": 0.1578761637210846}, "Polygon": [{"X": 0.5699953436851501, "Y": 0.1578761637210846}, {"X": 0.6595863699913025, "Y": 0.15787650644779205}, {"X": 0.6595863103866577, "Y": 0.1683826893568039}, {"X": 0.5699952244758606, "Y": 0.16838236153125763}]}, "Id": "03c6fcb7-4dbd-423a-8307-e3de6182e222", "Relationships": [{"Type": "CHILD", "Ids": ["b456833c-d9bb-4236-b7ac-831f00c91d8f", "4a59925b-b3b3-41d8-b93c-a5ed704b5a71"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "A11214", "Geometry": {"BoundingBox": {"Width": 0.0599195659160614, "Height": 0.010388251394033432, "Left": 0.7353599071502686, "Top": 0.1579800248146057}, "Polygon": [{"X": 0.7353599667549133, "Y": 0.1579800248146057}, {"X": 0.7952794432640076, "Y": 0.15798024833202362}, {"X": 0.7952793836593628, "Y": 0.16836827993392944}, {"X": 0.7353599071502686, "Y": 0.16836805641651154}]}, "Id": "af75beb1-9f28-4e64-a14c-e6f2874c533b", "Relationships": [{"Type": "CHILD", "Ids": ["bd2bac15-b25d-4daa-b902-55d2b8e23c91"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Due Date:", "Geometry": {"BoundingBox": {"Width": 0.07640311866998672, "Height": 0.010631147772073746, "Left": 0.5696079134941101, "Top": 0.1729619801044464}, "Polygon": [{"X": 0.5696080327033997, "Y": 0.1729619801044464}, {"X": 0.6460110545158386, "Y": 0.1729622483253479}, {"X": 0.6460109353065491, "Y": 0.18359312415122986}, {"X": 0.5696079134941101, "Y": 0.18359288573265076}]}, "Id": "c2c5747b-492d-451a-ae23-743657fc4f3c", "Relationships": [{"Type": "CHILD", "Ids": ["9de86974-0aef-44a0-b68f-ffd8608659a0", "765ef62e-e67d-4d57-91f5-134822d30aad"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "08/08/2025", "Geometry": {"BoundingBox": {"Width": 0.08707542717456818, "Height": 0.01049283891916275, "Left": 0.7353776693344116, "Top": 0.17313140630722046}, "Polygon": [{"X": 0.7353777289390564, "Y": 0.17313140630722046}, {"X": 0.8224530816078186, "Y": 0.17313170433044434}, {"X": 0.8224530220031738, "Y": 0.1836242526769638}, {"X": 0.7353776693344116, "Y": 0.18362396955490112}]}, "Id": "5463d54a-eba9-4e3b-9e63-0f92544bed8a", "Relationships": [{"Type": "CHILD", "Ids": ["db722a8b-0686-445c-8f60-41d1395376bb"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "245 3rd Street", "Geometry": {"BoundingBox": {"Width": 0.11064233630895615, "Height": 0.010622906498610973, "Left": 0.09807826578617096, "Top": 0.18173374235630035}, "Polygon": [{"X": 0.09807838499546051, "Y": 0.18173374235630035}, {"X": 0.2087206095457077, "Y": 0.181734099984169}, {"X": 0.20872049033641815, "Y": 0.1923566609621048}, {"X": 0.09807826578617096, "Y": 0.19235633313655853}]}, "Id": "cb0383c6-68ae-4243-90d8-a6f268cb32c4", "Relationships": [{"Type": "CHILD", "Ids": ["0298188e-f1d7-4713-9381-2ff78fb170bd", "e8c14b5b-22cf-4fea-8e5f-60f8a278b33e", "5ab3093f-f220-4092-8a20-77ff61133c76"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Salesperson:", "Geometry": {"BoundingBox": {"Width": 0.09946642816066742, "Height": 0.013233810663223267, "Left": 0.5694169998168945, "Top": 0.18805326521396637}, "Polygon": [{"X": 0.5694171190261841, "Y": 0.18805326521396637}, {"X": 0.6688833832740784, "Y": 0.18805356323719025}, {"X": 0.6688832640647888, "Y": 0.20128707587718964}, {"X": 0.5694169998168945, "Y": 0.20128680765628815}]}, "Id": "45c81f68-8a20-46cf-9147-f44d8ff4e88a", "Relationships": [{"Type": "CHILD", "Ids": ["a1164289-eb68-4d85-b11f-5dd75d547bf9"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "ELVIS RODRIGUEZ", "Geometry": {"BoundingBox": {"Width": 0.15751181542873383, "Height": 0.010727894492447376, "Left": 0.7362565398216248, "Top": 0.188052698969841}, "Polygon": [{"X": 0.7362565994262695, "Y": 0.188052698969841}, {"X": 0.8937683701515198, "Y": 0.1880531758069992}, {"X": 0.8937682509422302, "Y": 0.1987805962562561}, {"X": 0.7362565398216248, "Y": 0.19878017902374268}]}, "Id": "31a694b1-c064-4dde-bc76-af4452d43602", "Relationships": [{"Type": "CHILD", "Ids": ["56907202-6f1d-4968-bbe6-c4c9f6a71b88", "5e28069c-8af5-4abe-b4f0-0258c31726ca"]}]}, {"BlockType": "LINE", "Confidence": 99.94439697265625, "Text": "Passaic, NJ 07055", "Geometry": {"BoundingBox": {"Width": 0.1440732479095459, "Height": 0.012275338172912598, "Left": 0.09900268167257309, "Top": 0.1971578598022461}, "Polygon": [{"X": 0.09900282323360443, "Y": 0.1971578598022461}, {"X": 0.24307593703269958, "Y": 0.19715824723243713}, {"X": 0.24307580292224884, "Y": 0.2094331979751587}, {"X": 0.09900268167257309, "Y": 0.20943285524845123}]}, "Id": "79861188-f34f-42e3-a76e-2ec166810b6a", "Relationships": [{"Type": "CHILD", "Ids": ["ed9496d6-be9a-4b81-bec5-2bba757e9961", "05a4a889-da89-420a-882d-130d59e6f126", "3bc0d825-72af-4b6f-b6d2-aa01222c767d"]}]}, {"BlockType": "LINE", "Confidence": 97.52890014648438, "Text": "Customer PO No: EST7AC11496960", "Geometry": {"BoundingBox": {"Width": 0.3114384114742279, "Height": 0.010794981382787228, "Left": 0.5690957903862, "Top": 0.20320521295070648}, "Polygon": [{"X": 0.5690959095954895, "Y": 0.20320521295070648}, {"X": 0.8805341720581055, "Y": 0.20320601761341095}, {"X": 0.8805341124534607, "Y": 0.21400019526481628}, {"X": 0.5690957903862, "Y": 0.21399949491024017}]}, "Id": "9c79bf71-c027-442d-80a6-65e090803389", "Relationships": [{"Type": "CHILD", "Ids": ["e1b531d8-68d2-40ea-bbbc-a09e981e7b45", "cce81d44-1f43-41e2-ab3d-be37063043f0", "558fee53-f48b-45b2-943b-2d40c021b40d", "5a7cdf55-cfc6-4d05-8e93-842ae8d1fd09"]}]}, {"BlockType": "LINE", "Confidence": 98.6462631225586, "Text": "Tel: (973)756-6070", "Geometry": {"BoundingBox": {"Width": 0.1482320874929428, "Height": 0.013361869379878044, "Left": 0.09787817299365997, "Top": 0.21253220736980438}, "Polygon": [{"X": 0.09787832200527191, "Y": 0.21253220736980438}, {"X": 0.24611026048660278, "Y": 0.21253255009651184}, {"X": 0.24611011147499084, "Y": 0.22589407861232758}, {"X": 0.09787817299365997, "Y": 0.2258937805891037}]}, "Id": "9caf88fc-7462-48bb-862c-bffadf95af5a", "Relationships": [{"Type": "CHILD", "Ids": ["326b396a-1fc6-4128-98c4-93cac3fff885", "5f65af25-175f-4684-832e-449488ca2dc2"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "MC NUMBER:", "Geometry": {"BoundingBox": {"Width": 0.11019738018512726, "Height": 0.010656899772584438, "Left": 0.5694883465766907, "Top": 0.21842966973781586}, "Polygon": [{"X": 0.5694884061813354, "Y": 0.21842966973781586}, {"X": 0.6796857118606567, "Y": 0.21842989325523376}, {"X": 0.6796855926513672, "Y": 0.22908656299114227}, {"X": 0.5694883465766907, "Y": 0.22908636927604675}]}, "Id": "d5e3283b-5641-440f-96f2-1e466d0bdaea", "Relationships": [{"Type": "CHILD", "Ids": ["b49d66e0-bc2a-4f2a-9cf9-2d2ab5d23b9d", "1d9b749a-b8f9-4c77-88c2-3496b2a0e2e4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "014348", "Geometry": {"BoundingBox": {"Width": 0.05787849426269531, "Height": 0.010337166488170624, "Left": 0.7355576753616333, "Top": 0.21879903972148895}, "Polygon": [{"X": 0.7355577945709229, "Y": 0.21879903972148895}, {"X": 0.7934361696243286, "Y": 0.2187991589307785}, {"X": 0.7934361100196838, "Y": 0.22913619875907898}, {"X": 0.7355576753616333, "Y": 0.22913609445095062}]}, "Id": "a7c11db1-d598-434e-874e-b3b8a3595e9a", "Relationships": [{"Type": "CHILD", "Ids": ["a316f9cc-d4a6-41d4-92d7-d14c6c05690b"]}]}, {"BlockType": "LINE", "Confidence": 93.01966857910156, "Text": "Email: <EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.22741426527500153, "Height": 0.013414734974503517, "Left": 0.09889272600412369, "Top": 0.22805200517177582}, "Polygon": [{"X": 0.09889287501573563, "Y": 0.22805200517177582}, {"X": 0.3263069987297058, "Y": 0.22805243730545044}, {"X": 0.32630684971809387, "Y": 0.24146674573421478}, {"X": 0.09889272600412369, "Y": 0.24146641790866852}]}, "Id": "8af92bc7-c43f-4b5e-a260-c1d2b46ac6e9", "Relationships": [{"Type": "CHILD", "Ids": ["878cb075-739c-4908-8fc8-86acb7bd170d", "367473dd-ffd6-48a8-8b14-56f2daecab7e"]}]}, {"BlockType": "LINE", "Confidence": 99.9206771850586, "Text": "Bill To:", "Geometry": {"BoundingBox": {"Width": 0.055333249270915985, "Height": 0.010226291604340076, "Left": 0.09872958064079285, "Top": 0.257277250289917}, "Polygon": [{"X": 0.0987296998500824, "Y": 0.257277250289917}, {"X": 0.15406283736228943, "Y": 0.25727730989456177}, {"X": 0.15406271815299988, "Y": 0.2675035297870636}, {"X": 0.09872958064079285, "Y": 0.2675034999847412}]}, "Id": "65146849-97b4-4ca8-9dc5-e7d45fc3b109", "Relationships": [{"Type": "CHILD", "Ids": ["1e71ecde-1ff3-44fb-88b7-83e11cf0f8d0", "0ebb41da-5ef8-4d5a-92e2-fedda584e978"]}]}, {"BlockType": "LINE", "Confidence": 99.89802551269531, "Text": "IFREIGHT LOGISTICS LLC", "Geometry": {"BoundingBox": {"Width": 0.2142128050327301, "Height": 0.010733366943895817, "Left": 0.09942610561847687, "Top": 0.27270084619522095}, "Polygon": [{"X": 0.09942622482776642, "Y": 0.27270084619522095}, {"X": 0.3136388957500458, "Y": 0.2727009654045105}, {"X": 0.3136388063430786, "Y": 0.2834342122077942}, {"X": 0.09942610561847687, "Y": 0.2834341526031494}]}, "Id": "ae77a550-3967-414e-b77f-24fcdb276790", "Relationships": [{"Type": "CHILD", "Ids": ["df82a3ae-fce4-41d7-a30c-7bdb27596917", "2eab84a9-1b53-4b94-a518-037732cb969c", "67962b83-12c7-4d96-8ef4-cba1b2feea65"]}]}, {"BlockType": "LINE", "Confidence": 99.44255828857422, "Text": "MC-1064155", "Geometry": {"BoundingBox": {"Width": 0.09975029528141022, "Height": 0.010740476660430431, "Left": 0.09857416152954102, "Top": 0.2880399823188782}, "Polygon": [{"X": 0.09857428073883057, "Y": 0.2880399823188782}, {"X": 0.19832445681095123, "Y": 0.2880399823188782}, {"X": 0.19832433760166168, "Y": 0.2987804412841797}, {"X": 0.09857416152954102, "Y": 0.2987804412841797}]}, "Id": "ccb0fd68-0b5a-4704-8b1e-6e6eadbb9b93", "Relationships": [{"Type": "CHILD", "Ids": ["0b347a4f-0492-4756-a02a-7ac11a6ba017"]}]}, {"BlockType": "LINE", "Confidence": 99.96077728271484, "Text": "131 GARY WAY STE 4", "Geometry": {"BoundingBox": {"Width": 0.17994198203086853, "Height": 0.010902275331318378, "Left": 0.09915262460708618, "Top": 0.30333617329597473}, "Polygon": [{"X": 0.09915274381637573, "Y": 0.3033362329006195}, {"X": 0.2790946066379547, "Y": 0.30333617329597473}, {"X": 0.27909448742866516, "Y": 0.3142383396625519}, {"X": 0.09915262460708618, "Y": 0.31423845887184143}]}, "Id": "0ea03aa5-5fa6-4794-8406-a2c9e521a5cd", "Relationships": [{"Type": "CHILD", "Ids": ["095aef13-6e21-477c-a661-150aca8919ed", "8eda9c57-8c20-4a9f-ae59-fc950aa07044", "3803e195-a90f-45d5-a160-532695d975d2", "a34286d0-4659-4eb9-86d1-c5325fcee382", "79c5f0f3-6933-4836-a200-ba4107eed7ff"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "RONKONKOMA, NY 11779", "Geometry": {"BoundingBox": {"Width": 0.22340112924575806, "Height": 0.012374251149594784, "Left": 0.09885594248771667, "Top": 0.3188675343990326}, "Polygon": [{"X": 0.09885608404874802, "Y": 0.3188677132129669}, {"X": 0.32225707173347473, "Y": 0.3188675343990326}, {"X": 0.3222569525241852, "Y": 0.33124151825904846}, {"X": 0.09885594248771667, "Y": 0.33124178647994995}]}, "Id": "7ef413ae-96b7-4100-a4fc-bc72f2182575", "Relationships": [{"Type": "CHILD", "Ids": ["564520ac-1c57-4375-ae39-9685a03b85c9", "aefe44c7-32ab-46d4-a7dd-fd45afcff471", "6b5709a5-ec9e-4a4e-a11d-e0bf000d119f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Qty", "Geometry": {"BoundingBox": {"Width": 0.027530808001756668, "Height": 0.012871194630861282, "Left": 0.11765674501657486, "Top": 0.35762766003608704}, "Polygon": [{"X": 0.1176568865776062, "Y": 0.3576276898384094}, {"X": 0.14518755674362183, "Y": 0.35762766003608704}, {"X": 0.1451874077320099, "Y": 0.37049877643585205}, {"X": 0.11765674501657486, "Y": 0.3704988360404968}]}, "Id": "3bc526ec-ad59-4923-9872-ae35c60adad8", "Relationships": [{"Type": "CHILD", "Ids": ["dcf49b17-1549-4667-855e-f2156f236b5d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.03335360065102577, "Height": 0.010078689083456993, "Left": 0.16240282356739044, "Top": 0.35757073760032654}, "Polygon": [{"X": 0.16240294277668, "Y": 0.3575707972049713}, {"X": 0.1957564353942871, "Y": 0.35757073760032654}, {"X": 0.19575631618499756, "Y": 0.3676493465900421}, {"X": 0.16240282356739044, "Y": 0.3676494359970093}]}, "Id": "41a5fc51-d6a5-43d6-bc33-a76e30190f8a", "Relationships": [{"Type": "CHILD", "Ids": ["7c20780e-0e3e-4d38-8910-0b07af45a4e4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.09157400578260422, "Height": 0.012896859087049961, "Left": 0.2767729163169861, "Top": 0.35746270418167114}, "Polygon": [{"X": 0.27677303552627563, "Y": 0.35746288299560547}, {"X": 0.3683468997478485, "Y": 0.35746270418167114}, {"X": 0.36834678053855896, "Y": 0.3703593611717224}, {"X": 0.2767729163169861, "Y": 0.3703595697879791}]}, "Id": "b40ddc70-2bd1-448a-8a71-89796c3927de", "Relationships": [{"Type": "CHILD", "Ids": ["a857bb16-30bb-49e4-a236-6760e78c76d7"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Unit Price", "Geometry": {"BoundingBox": {"Width": 0.07752160727977753, "Height": 0.010303791612386703, "Left": 0.6950205564498901, "Top": 0.3572513461112976}, "Polygon": [{"X": 0.6950206756591797, "Y": 0.35725149512290955}, {"X": 0.7725421786308289, "Y": 0.3572513461112976}, {"X": 0.7725421190261841, "Y": 0.3675549626350403}, {"X": 0.6950205564498901, "Y": 0.3675551414489746}]}, "Id": "6a063b7d-2c0f-4c65-ad86-0ac9b03402b8", "Relationships": [{"Type": "CHILD", "Ids": ["40422d47-6022-4e3a-b1f0-11a4ee8d4d29", "ba30f4f6-aab7-4a95-ada2-31b65df58c0b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.03970661759376526, "Height": 0.010305868461728096, "Left": 0.8631364703178406, "Top": 0.3574539124965668}, "Polygon": [{"X": 0.8631365299224854, "Y": 0.35745400190353394}, {"X": 0.9028431177139282, "Y": 0.3574539124965668}, {"X": 0.9028429985046387, "Y": 0.36775970458984375}, {"X": 0.8631364703178406, "Y": 0.3677597939968109}]}, "Id": "8c3257bc-a310-451b-9b68-0213b493c94f", "Relationships": [{"Type": "CHILD", "Ids": ["146b7625-c757-4d96-8306-6579632f6a92"]}]}, {"BlockType": "LINE", "Confidence": 99.71520233154297, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.005175105296075344, "Height": 0.009204517118632793, "Left": 0.13780032098293304, "Top": 0.3854466676712036}, "Polygon": [{"X": 0.1378004252910614, "Y": 0.385446697473526}, {"X": 0.14297541975975037, "Y": 0.3854466676712036}, {"X": 0.1429753303527832, "Y": 0.3946511745452881}, {"X": 0.13780032098293304, "Y": 0.3946512043476105}]}, "Id": "03ef41a5-6045-4f94-80cb-1bbb8d6b0906", "Relationships": [{"Type": "CHILD", "Ids": ["310beebe-baf3-4905-b7ee-d89bed99346b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "SERVICE", "Geometry": {"BoundingBox": {"Width": 0.06785181909799576, "Height": 0.009501578286290169, "Left": 0.16241887211799622, "Top": 0.3851204514503479}, "Polygon": [{"X": 0.16241897642612457, "Y": 0.3851206302642822}, {"X": 0.23027068376541138, "Y": 0.3851204514503479}, {"X": 0.23027057945728302, "Y": 0.3946218192577362}, {"X": 0.16241887211799622, "Y": 0.3946220278739929}]}, "Id": "9fc04d87-0e69-4e6a-b051-b628426f8380", "Relationships": [{"Type": "CHILD", "Ids": ["a339d592-1ebe-42bb-bcfe-9aee2b2a6ee4"]}]}, {"BlockType": "LINE", "Confidence": 98.03517150878906, "Text": "LINE HAUL (LOAD # EST7AC11496960) - QUICK", "Geometry": {"BoundingBox": {"Width": 0.3523698151111603, "Height": 0.01194754894822836, "Left": 0.27670949697494507, "Top": 0.38501879572868347}, "Polygon": [{"X": 0.276709645986557, "Y": 0.3850197196006775}, {"X": 0.629079282283783, "Y": 0.38501879572868347}, {"X": 0.6290792226791382, "Y": 0.39696526527404785}, {"X": 0.27670949697494507, "Y": 0.3969663381576538}]}, "Id": "0b91e18f-31ca-47bb-9b65-dcac028a2073", "Relationships": [{"Type": "CHILD", "Ids": ["37c349db-e50d-4e6f-84ac-0a8e70a8a38c", "8940ce60-4a38-416e-ae55-cb8761eae43a", "63b1763b-7430-4e69-b467-0aa7282cc175", "8ef1abb1-5d5f-4394-b26f-0c6d8f3cb663", "17b9eb64-8ef7-43de-aee7-390bd61d87d9", "9d9b458e-2fbf-4bdf-8780-880ae0ba89ad", "09a540eb-b78c-44ea-93b2-38bdd2feb7db"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$1,050.00", "Geometry": {"BoundingBox": {"Width": 0.06959298253059387, "Height": 0.011823841370642185, "Left": 0.6986832022666931, "Top": 0.38457176089286804}, "Polygon": [{"X": 0.6986833214759827, "Y": 0.38457196950912476}, {"X": 0.7682762145996094, "Y": 0.38457176089286804}, {"X": 0.7682760953903198, "Y": 0.39639541506767273}, {"X": 0.6986832022666931, "Y": 0.39639562368392944}]}, "Id": "36d712b2-f78a-4f92-b3fb-b6be051aa65d", "Relationships": [{"Type": "CHILD", "Ids": ["c33af7be-5c7d-4c9c-8993-b36cce5ff041"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$1,050.00", "Geometry": {"BoundingBox": {"Width": 0.07011725008487701, "Height": 0.01196206919848919, "Left": 0.8291334509849548, "Top": 0.38445863127708435}, "Polygon": [{"X": 0.8291335701942444, "Y": 0.3844588100910187}, {"X": 0.8992506861686707, "Y": 0.38445863127708435}, {"X": 0.8992505669593811, "Y": 0.3964204788208008}, {"X": 0.8291334509849548, "Y": 0.3964207172393799}]}, "Id": "c512c90b-bc98-4e1d-9754-2475f4c8a8fa", "Relationships": [{"Type": "CHILD", "Ids": ["b2c5d940-fae1-4f83-a06a-cf28b73b21be"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "PAY", "Geometry": {"BoundingBox": {"Width": 0.030923612415790558, "Height": 0.009453551843762398, "Left": 0.2770519256591797, "Top": 0.3992142677307129}, "Polygon": [{"X": 0.27705201506614685, "Y": 0.39921435713768005}, {"X": 0.30797553062438965, "Y": 0.3992142677307129}, {"X": 0.3079754412174225, "Y": 0.4086677134037018}, {"X": 0.2770519256591797, "Y": 0.40866783261299133}]}, "Id": "939b0355-cc59-4ed7-adee-218dde3f6841", "Relationships": [{"Type": "CHILD", "Ids": ["b7e01022-8ba4-4cba-8c47-4090e34731c8"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Total $1,050.00", "Geometry": {"BoundingBox": {"Width": 0.12140583246946335, "Height": 0.011619827710092068, "Left": 0.7819064259529114, "Top": 0.7935773730278015}, "Polygon": [{"X": 0.7819065451622009, "Y": 0.7935791611671448}, {"X": 0.9033122658729553, "Y": 0.7935773730278015}, {"X": 0.9033122062683105, "Y": 0.8051953911781311}, {"X": 0.7819064259529114, "Y": 0.8051972389221191}]}, "Id": "e30008a9-de4d-4f10-96bd-f98a1e21e9fd", "Relationships": [{"Type": "CHILD", "Ids": ["7170ce92-d027-47e4-955a-3fe2e839d7f9", "71703f2f-2efa-4d9d-ba62-fb90eafbc768"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Balance Due $1,050.00", "Geometry": {"BoundingBox": {"Width": 0.1754833161830902, "Height": 0.01169351302087307, "Left": 0.728143572807312, "Top": 0.8140668869018555}, "Polygon": [{"X": 0.7281436324119568, "Y": 0.8140695691108704}, {"X": 0.9036268591880798, "Y": 0.8140668869018555}, {"X": 0.9036267399787903, "Y": 0.8257577419281006}, {"X": 0.728143572807312, "Y": 0.8257604241371155}]}, "Id": "e4ef48d5-d932-4a2d-9012-dd937126f301", "Relationships": [{"Type": "CHILD", "Ids": ["af8f0e9d-5891-4193-98f7-2d5594d70aad", "f2ec3dd6-6e0f-4bda-8f42-0ce9e9889aa7", "d1c47251-26d7-4c56-956d-1391384b219d"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "NOTE: QUICK PAY", "Geometry": {"BoundingBox": {"Width": 0.15050877630710602, "Height": 0.010195014998316765, "Left": 0.088814876973629, "Top": 0.8526560664176941}, "Polygon": [{"X": 0.08881499618291855, "Y": 0.8526585102081299}, {"X": 0.23932364583015442, "Y": 0.8526560664176941}, {"X": 0.23932354152202606, "Y": 0.8628486394882202}, {"X": 0.088814876973629, "Y": 0.862851083278656}]}, "Id": "8e9bdee1-6c8b-4002-825d-eef120a7a4c4", "Relationships": [{"Type": "CHILD", "Ids": ["0d3277dd-af3e-4f76-a904-4fe6c45de78d", "b009bc28-9da3-4222-90de-025e0018a254", "f73d0e9c-9aef-41c1-8e38-b533d0b9121b"]}]}, {"BlockType": "LINE", "Confidence": 99.91835021972656, "Text": "TERM: NET 2", "Geometry": {"BoundingBox": {"Width": 0.10587365925312042, "Height": 0.010234801098704338, "Left": 0.08829949051141739, "Top": 0.8677678108215332}, "Polygon": [{"X": 0.08829960227012634, "Y": 0.8677695393562317}, {"X": 0.1941731572151184, "Y": 0.8677678108215332}, {"X": 0.19417303800582886, "Y": 0.878000795841217}, {"X": 0.08829949051141739, "Y": 0.8780025839805603}]}, "Id": "351783f9-e820-4d13-91a4-1049909331b9", "Relationships": [{"Type": "CHILD", "Ids": ["40c2beef-73ed-4b78-8293-b21be16d2590", "0f6904ba-6be9-4e39-ab71-016d4a282bd7", "7e516b0c-f593-4d7e-b0fb-494f70536416"]}]}, {"BlockType": "LINE", "Confidence": 99.98405456542969, "Text": "Thank you for your business.", "Geometry": {"BoundingBox": {"Width": 0.23503901064395905, "Height": 0.012859463691711426, "Left": 0.08801937848329544, "Top": 0.9008802175521851}, "Polygon": [{"X": 0.08801952749490738, "Y": 0.9008843898773193}, {"X": 0.3230583965778351, "Y": 0.9008802175521851}, {"X": 0.32305827736854553, "Y": 0.9137355089187622}, {"X": 0.08801937848329544, "Y": 0.9137396812438965}]}, "Id": "1b71e923-ddc6-4479-bfa5-28b6ec12cbaf", "Relationships": [{"Type": "CHILD", "Ids": ["6faaea46-d5bb-49c0-bc22-f683537a6e70", "b511a42f-b7b0-4c59-b389-ba99b53c85d9", "0f736946-6104-4ec4-b1f2-8c2d8d4de380", "ff2cff07-af4a-4146-9e50-b7f6cbf0e9a9", "fac06bf8-ba87-42ef-a1d4-868cce33da0f"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12167340517044067, "Height": 0.02087762951850891, "Left": 0.570727527141571, "Top": 0.08082494884729385}, "Polygon": [{"X": 0.5707277655601501, "Y": 0.08082494884729385}, {"X": 0.6924009323120117, "Y": 0.08082568645477295}, {"X": 0.6924007534980774, "Y": 0.10170257836580276}, {"X": 0.570727527141571, "Y": 0.10170190781354904}]}, "Id": "dd4c3b5e-802f-409f-b190-cbc1bee8bdf6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03961382433772087, "Height": 0.010407843627035618, "Left": 0.5694351196289062, "Top": 0.14273084700107574}, "Polygon": [{"X": 0.569435179233551, "Y": 0.14273084700107574}, {"X": 0.6090489029884338, "Y": 0.14273101091384888}, {"X": 0.6090488433837891, "Y": 0.1531386822462082}, {"X": 0.5694351196289062, "Y": 0.15313851833343506}]}, "Id": "aa8b08a1-e466-4f4e-9b72-77e53ba17272"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "08/06/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0869469866156578, "Height": 0.0107219023630023, "Left": 0.735404908657074, "Top": 0.14261667430400848}, "Polygon": [{"X": 0.7354050278663635, "Y": 0.14261667430400848}, {"X": 0.82235187292099, "Y": 0.14261704683303833}, {"X": 0.8223518133163452, "Y": 0.15333856642246246}, {"X": 0.735404908657074, "Y": 0.153338223695755}]}, "Id": "0167f879-7d2c-4524-a25f-8e517504d03c"}, {"BlockType": "WORD", "Confidence": 96.93080139160156, "Text": "E &", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02718140371143818, "Height": 0.010170584544539452, "Left": 0.09881647676229477, "Top": 0.16027916967868805}, "Polygon": [{"X": 0.09881658852100372, "Y": 0.16027916967868805}, {"X": 0.1259978860616684, "Y": 0.1602792739868164}, {"X": 0.12599776685237885, "Y": 0.17044974863529205}, {"X": 0.09881647676229477, "Y": 0.1704496592283249}]}, "Id": "1f725dfe-a894-46f6-8815-9282cdea2288"}, {"BlockType": "WORD", "Confidence": 99.61834716796875, "Text": "R", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010780456475913525, "Height": 0.00980012584477663, "Left": 0.13175442814826965, "Top": 0.16047494113445282}, "Polygon": [{"X": 0.131754532456398, "Y": 0.16047494113445282}, {"X": 0.14253488183021545, "Y": 0.1604749858379364}, {"X": 0.1425347626209259, "Y": 0.17027506232261658}, {"X": 0.13175442814826965, "Y": 0.1702750325202942}]}, "Id": "1e9e1fa9-7463-4c1a-8b75-168e140ffcc9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07369081676006317, "Height": 0.010173858143389225, "Left": 0.1485963612794876, "Top": 0.16029353439807892}, "Polygon": [{"X": 0.14859648048877716, "Y": 0.16029353439807892}, {"X": 0.22228717803955078, "Y": 0.1602938175201416}, {"X": 0.22228707373142242, "Y": 0.17046739161014557}, {"X": 0.1485963612794876, "Y": 0.17046712338924408}]}, "Id": "9e9af256-e781-41e8-99f2-630cd61f866a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRUCKING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08894116431474686, "Height": 0.01024693250656128, "Left": 0.22630873322486877, "Top": 0.16024330258369446}, "Polygon": [{"X": 0.22630883753299713, "Y": 0.16024330258369446}, {"X": 0.31524989008903503, "Y": 0.16024364531040192}, {"X": 0.3152497708797455, "Y": 0.17049023509025574}, {"X": 0.22630873322486877, "Y": 0.17048992216587067}]}, "Id": "46eb9c3c-767b-4302-8dde-354c46a70297"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03130340948700905, "Height": 0.010280415415763855, "Left": 0.3211643099784851, "Top": 0.16018955409526825}, "Polygon": [{"X": 0.32116442918777466, "Y": 0.16018955409526825}, {"X": 0.35246771574020386, "Y": 0.1601896733045578}, {"X": 0.3524675965309143, "Y": 0.1704699695110321}, {"X": 0.3211643099784851, "Y": 0.17046985030174255}]}, "Id": "e90189a2-1f7d-4331-b713-3011188351ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05358553305268288, "Height": 0.010472077876329422, "Left": 0.5699952244758606, "Top": 0.15791048109531403}, "Polygon": [{"X": 0.5699953436851501, "Y": 0.15791048109531403}, {"X": 0.6235807538032532, "Y": 0.15791068971157074}, {"X": 0.6235806345939636, "Y": 0.16838255524635315}, {"X": 0.5699952244758606, "Y": 0.16838236153125763}]}, "Id": "b456833c-d9bb-4236-b7ac-831f00c91d8f"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "No.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030164562165737152, "Height": 0.010463682934641838, "Left": 0.6294218301773071, "Top": 0.1578764021396637}, "Polygon": [{"X": 0.6294219493865967, "Y": 0.1578764021396637}, {"X": 0.6595863699913025, "Y": 0.15787650644779205}, {"X": 0.6595863103866577, "Y": 0.1683400720357895}, {"X": 0.6294218301773071, "Y": 0.16833996772766113}]}, "Id": "4a59925b-b3b3-41d8-b93c-a5ed704b5a71"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "A11214", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0599195659160614, "Height": 0.010388251394033432, "Left": 0.7353599071502686, "Top": 0.1579800248146057}, "Polygon": [{"X": 0.7353599667549133, "Y": 0.1579800248146057}, {"X": 0.7952794432640076, "Y": 0.15798024833202362}, {"X": 0.7952793836593628, "Y": 0.16836827993392944}, {"X": 0.7353599071502686, "Y": 0.16836805641651154}]}, "Id": "bd2bac15-b25d-4daa-b902-55d2b8e23c91"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03075318969786167, "Height": 0.010608515702188015, "Left": 0.5696079134941101, "Top": 0.1729619801044464}, "Polygon": [{"X": 0.5696080327033997, "Y": 0.1729619801044464}, {"X": 0.6003611087799072, "Y": 0.17296208441257477}, {"X": 0.6003609895706177, "Y": 0.1835704892873764}, {"X": 0.5696079134941101, "Y": 0.18357039988040924}]}, "Id": "9de86974-0aef-44a0-b68f-ffd8608659a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039996057748794556, "Height": 0.01054224744439125, "Left": 0.6060149669647217, "Top": 0.1730508804321289}, "Polygon": [{"X": 0.6060150861740112, "Y": 0.1730508804321289}, {"X": 0.6460110545158386, "Y": 0.17305101454257965}, {"X": 0.6460109353065491, "Y": 0.18359312415122986}, {"X": 0.6060149669647217, "Y": 0.1835930049419403}]}, "Id": "765ef62e-e67d-4d57-91f5-134822d30aad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "08/08/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08707542717456818, "Height": 0.01049283891916275, "Left": 0.7353776693344116, "Top": 0.17313140630722046}, "Polygon": [{"X": 0.7353777289390564, "Y": 0.17313140630722046}, {"X": 0.8224530816078186, "Y": 0.17313170433044434}, {"X": 0.8224530220031738, "Y": 0.1836242526769638}, {"X": 0.7353776693344116, "Y": 0.18362396955490112}]}, "Id": "db722a8b-0686-445c-8f60-41d1395376bb"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "245", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02888154424726963, "Height": 0.010248676873743534, "Left": 0.09807826578617096, "Top": 0.182036355137825}, "Polygon": [{"X": 0.09807838499546051, "Y": 0.182036355137825}, {"X": 0.12695981562137604, "Y": 0.18203644454479218}, {"X": 0.1269596964120865, "Y": 0.19228503108024597}, {"X": 0.09807826578617096, "Y": 0.1922849416732788}]}, "Id": "0298188e-f1d7-4713-9381-2ff78fb170bd"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "3rd", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02413778565824032, "Height": 0.01051164511591196, "Left": 0.13239610195159912, "Top": 0.1818326711654663}, "Polygon": [{"X": 0.13239622116088867, "Y": 0.1818326711654663}, {"X": 0.156533882021904, "Y": 0.18183274567127228}, {"X": 0.15653377771377563, "Y": 0.1923443078994751}, {"X": 0.13239610195159912, "Y": 0.19234424829483032}]}, "Id": "e8c14b5b-22cf-4fea-8e5f-60f8a278b33e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04599791765213013, "Height": 0.010622699744999409, "Left": 0.16272269189357758, "Top": 0.18173395097255707}, "Polygon": [{"X": 0.16272279620170593, "Y": 0.18173395097255707}, {"X": 0.2087206095457077, "Y": 0.181734099984169}, {"X": 0.20872049033641815, "Y": 0.1923566609621048}, {"X": 0.16272269189357758, "Y": 0.19235652685165405}]}, "Id": "5ab3093f-f220-4092-8a20-77ff61133c76"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Salesperson:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09946642816066742, "Height": 0.013233810663223267, "Left": 0.5694169998168945, "Top": 0.18805326521396637}, "Polygon": [{"X": 0.5694171190261841, "Y": 0.18805326521396637}, {"X": 0.6688833832740784, "Y": 0.18805356323719025}, {"X": 0.6688832640647888, "Y": 0.20128707587718964}, {"X": 0.5694169998168945, "Y": 0.20128680765628815}]}, "Id": "a1164289-eb68-4d85-b11f-5dd75d547bf9"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "ELVIS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04812509939074516, "Height": 0.010727599263191223, "Left": 0.7362565398216248, "Top": 0.188052698969841}, "Polygon": [{"X": 0.7362565994262695, "Y": 0.188052698969841}, {"X": 0.784381628036499, "Y": 0.18805284798145294}, {"X": 0.7843815088272095, "Y": 0.19878029823303223}, {"X": 0.7362565398216248, "Y": 0.19878017902374268}]}, "Id": "56907202-6f1d-4968-bbe6-c4c9f6a71b88"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RODRIGUEZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1031753197312355, "Height": 0.010538743808865547, "Left": 0.7905930280685425, "Top": 0.18812905251979828}, "Polygon": [{"X": 0.7905930876731873, "Y": 0.18812905251979828}, {"X": 0.8937683701515198, "Y": 0.18812935054302216}, {"X": 0.8937682509422302, "Y": 0.19866779446601868}, {"X": 0.7905930280685425, "Y": 0.198667511343956}]}, "Id": "5e28069c-8af5-4abe-b4f0-0258c31726ca"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Passaic,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06406234949827194, "Height": 0.012275146320462227, "Left": 0.09900268167257309, "Top": 0.1971578598022461}, "Polygon": [{"X": 0.09900282323360443, "Y": 0.1971578598022461}, {"X": 0.16306503117084503, "Y": 0.19715803861618042}, {"X": 0.1630648970603943, "Y": 0.20943300426006317}, {"X": 0.09900268167257309, "Y": 0.20943285524845123}]}, "Id": "ed9496d6-be9a-4b81-bec5-2bba757e9961"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "NJ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01992557942867279, "Height": 0.010463643819093704, "Left": 0.16955050826072693, "Top": 0.19730858504772186}, "Polygon": [{"X": 0.16955062747001648, "Y": 0.19730858504772186}, {"X": 0.18947608768939972, "Y": 0.19730862975120544}, {"X": 0.18947598338127136, "Y": 0.20777222514152527}, {"X": 0.16955050826072693, "Y": 0.2077721655368805}]}, "Id": "05a4a889-da89-420a-882d-130d59e6f126"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "07055", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04797137528657913, "Height": 0.010309596545994282, "Left": 0.19510455429553986, "Top": 0.1975516676902771}, "Polygon": [{"X": 0.1951046586036682, "Y": 0.1975516676902771}, {"X": 0.2430759221315384, "Y": 0.19755180180072784}, {"X": 0.24307581782341003, "Y": 0.2078612744808197}, {"X": 0.19510455429553986, "Y": 0.20786115527153015}]}, "Id": "3bc0d825-72af-4b6f-b6d2-aa01222c767d"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07564177364110947, "Height": 0.010717049241065979, "Left": 0.5690957903862, "Top": 0.20320521295070648}, "Polygon": [{"X": 0.5690959095954895, "Y": 0.20320521295070648}, {"X": 0.6447375416755676, "Y": 0.203205406665802}, {"X": 0.6447374820709229, "Y": 0.21392226219177246}, {"X": 0.5690957903862, "Y": 0.21392209827899933}]}, "Id": "e1b531d8-68d2-40ea-bbbc-a09e981e7b45"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02413369156420231, "Height": 0.010629598051309586, "Left": 0.6499495506286621, "Top": 0.20329344272613525}, "Polygon": [{"X": 0.6499496698379517, "Y": 0.20329344272613525}, {"X": 0.6740832328796387, "Y": 0.20329350233078003}, {"X": 0.6740831136703491, "Y": 0.21392303705215454}, {"X": 0.6499495506286621, "Y": 0.21392299234867096}]}, "Id": "cce81d44-1f43-41e2-ab3d-be37063043f0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025272369384765625, "Height": 0.01035679318010807, "Left": 0.6798948645591736, "Top": 0.20345111191272736}, "Polygon": [{"X": 0.6798949837684631, "Y": 0.20345111191272736}, {"X": 0.7051672339439392, "Y": 0.20345117151737213}, {"X": 0.7051671743392944, "Y": 0.21380789577960968}, {"X": 0.6798948645591736, "Y": 0.2138078361749649}]}, "Id": "558fee53-f48b-45b2-943b-2d40c021b40d"}, {"BlockType": "WORD", "Confidence": 90.28479766845703, "Text": "EST7AC11496960", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14441096782684326, "Height": 0.010664604604244232, "Left": 0.7361232042312622, "Top": 0.20333558320999146}, "Polygon": [{"X": 0.7361233234405518, "Y": 0.20333558320999146}, {"X": 0.8805341720581055, "Y": 0.2033359557390213}, {"X": 0.8805341124534607, "Y": 0.21400019526481628}, {"X": 0.7361232042312622, "Y": 0.21399986743927002}]}, "Id": "5a7cdf55-cfc6-4d05-8e93-842ae8d1fd09"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Tel:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02800901234149933, "Height": 0.010552267543971539, "Left": 0.09787820279598236, "Top": 0.21276409924030304}, "Polygon": [{"X": 0.09787832200527191, "Y": 0.21276409924030304}, {"X": 0.1258872151374817, "Y": 0.21276415884494781}, {"X": 0.12588709592819214, "Y": 0.22331637144088745}, {"X": 0.09787820279598236, "Y": 0.22331631183624268}]}, "Id": "326b396a-1fc6-4128-98c4-93cac3fff885"}, {"BlockType": "WORD", "Confidence": 97.30229187011719, "Text": "(973)756-6070", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11341776698827744, "Height": 0.013361789286136627, "Left": 0.13269248604774475, "Top": 0.21253228187561035}, "Polygon": [{"X": 0.1326926350593567, "Y": 0.21253228187561035}, {"X": 0.24611026048660278, "Y": 0.21253255009651184}, {"X": 0.24611011147499084, "Y": 0.22589407861232758}, {"X": 0.13269248604774475, "Y": 0.22589385509490967}]}, "Id": "5f65af25-175f-4684-832e-449488ca2dc2"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "MC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025869842618703842, "Height": 0.01062326692044735, "Left": 0.5694883465766907, "Top": 0.21845674514770508}, "Polygon": [{"X": 0.5694884061813354, "Y": 0.21845674514770508}, {"X": 0.5953581929206848, "Y": 0.21845680475234985}, {"X": 0.5953580737113953, "Y": 0.22908002138137817}, {"X": 0.5694883465766907, "Y": 0.2290799766778946}]}, "Id": "b49d66e0-bc2a-4f2a-9cf9-2d2ab5d23b9d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "NUMBER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07852422446012497, "Height": 0.010656831786036491, "Left": 0.6011614799499512, "Top": 0.21842972934246063}, "Polygon": [{"X": 0.6011615991592407, "Y": 0.21842972934246063}, {"X": 0.6796857118606567, "Y": 0.21842989325523376}, {"X": 0.6796855926513672, "Y": 0.22908656299114227}, {"X": 0.6011614799499512, "Y": 0.22908641397953033}]}, "Id": "1d9b749a-b8f9-4c77-88c2-3496b2a0e2e4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "014348", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05787849426269531, "Height": 0.010337166488170624, "Left": 0.7355576753616333, "Top": 0.21879903972148895}, "Polygon": [{"X": 0.7355577945709229, "Y": 0.21879903972148895}, {"X": 0.7934361696243286, "Y": 0.2187991589307785}, {"X": 0.7934361100196838, "Y": 0.22913619875907898}, {"X": 0.7355576753616333, "Y": 0.22913609445095062}]}, "Id": "a316f9cc-d4a6-41d4-92d7-d14c6c05690b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Email:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046673733741045, "Height": 0.010611550882458687, "Left": 0.09889275580644608, "Top": 0.2281789481639862}, "Polygon": [{"X": 0.09889287501573563, "Y": 0.2281789481639862}, {"X": 0.14556649327278137, "Y": 0.22817903757095337}, {"X": 0.14556637406349182, "Y": 0.23879049718379974}, {"X": 0.09889275580644608, "Y": 0.23879042267799377}]}, "Id": "878cb075-739c-4908-8fc8-86acb7bd170d"}, {"BlockType": "WORD", "Confidence": 86.0792007446289, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17498154938220978, "Height": 0.013414638116955757, "Left": 0.15132543444633484, "Top": 0.22805210947990417}, "Polygon": [{"X": 0.15132558345794678, "Y": 0.22805210947990417}, {"X": 0.3263069987297058, "Y": 0.22805243730545044}, {"X": 0.32630684971809387, "Y": 0.24146674573421478}, {"X": 0.15132543444633484, "Y": 0.2414664924144745}]}, "Id": "367473dd-ffd6-48a8-8b14-56f2daecab7e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024953050538897514, "Height": 0.010195965878665447, "Left": 0.09872958064079285, "Top": 0.257277250289917}, "Polygon": [{"X": 0.0987296998500824, "Y": 0.257277250289917}, {"X": 0.12368263304233551, "Y": 0.2572772800922394}, {"X": 0.12368252128362656, "Y": 0.2674732208251953}, {"X": 0.09872958064079285, "Y": 0.2674731910228729}]}, "Id": "1e71ecde-1ff3-44fb-88b7-83e11cf0f8d0"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02545570395886898, "Height": 0.009999414905905724, "Left": 0.1286071240901947, "Top": 0.25750410556793213}, "Polygon": [{"X": 0.12860724329948425, "Y": 0.25750410556793213}, {"X": 0.15406282246112823, "Y": 0.2575041353702545}, {"X": 0.15406271815299988, "Y": 0.2675035297870636}, {"X": 0.1286071240901947, "Y": 0.2675034999847412}]}, "Id": "0ebb41da-5ef8-4d5a-92e2-fedda584e978"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "IFREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08001116663217545, "Height": 0.010622513480484486, "Left": 0.09942610561847687, "Top": 0.2728116512298584}, "Polygon": [{"X": 0.09942622482776642, "Y": 0.2728116512298584}, {"X": 0.1794372797012329, "Y": 0.2728117108345032}, {"X": 0.17943716049194336, "Y": 0.2834341824054718}, {"X": 0.09942610561847687, "Y": 0.2834341526031494}]}, "Id": "df82a3ae-fce4-41d7-a30c-7bdb27596917"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "LOGISTICS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09218605607748032, "Height": 0.01073050033301115, "Left": 0.1846422404050827, "Top": 0.2727009057998657}, "Polygon": [{"X": 0.18464235961437225, "Y": 0.2727009057998657}, {"X": 0.2768282890319824, "Y": 0.2727009356021881}, {"X": 0.27682816982269287, "Y": 0.28343141078948975}, {"X": 0.1846422404050827, "Y": 0.28343138098716736}]}, "Id": "2eab84a9-1b53-4b94-a518-037732cb969c"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03080955147743225, "Height": 0.01070330385118723, "Left": 0.2828293442726135, "Top": 0.27270832657814026}, "Polygon": [{"X": 0.2828294634819031, "Y": 0.27270832657814026}, {"X": 0.3136388957500458, "Y": 0.27270832657814026}, {"X": 0.3136388063430786, "Y": 0.2834116220474243}, {"X": 0.2828293442726135, "Y": 0.2834116220474243}]}, "Id": "67962b83-12c7-4d96-8ef4-cba1b2feea65"}, {"BlockType": "WORD", "Confidence": 99.44255828857422, "Text": "MC-1064155", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09975029528141022, "Height": 0.010740476660430431, "Left": 0.09857416152954102, "Top": 0.2880399823188782}, "Polygon": [{"X": 0.09857428073883057, "Y": 0.2880399823188782}, {"X": 0.19832445681095123, "Y": 0.2880399823188782}, {"X": 0.19832433760166168, "Y": 0.2987804412841797}, {"X": 0.09857416152954102, "Y": 0.2987804412841797}]}, "Id": "0b347a4f-0492-4756-a02a-7ac11a6ba017"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "131", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025231337174773216, "Height": 0.010622890666127205, "Left": 0.09915262460708618, "Top": 0.3036155700683594}, "Polygon": [{"X": 0.09915274381637573, "Y": 0.3036155700683594}, {"X": 0.12438395619392395, "Y": 0.3036155700683594}, {"X": 0.1243838369846344, "Y": 0.31423842906951904}, {"X": 0.09915262460708618, "Y": 0.31423845887184143}]}, "Id": "095aef13-6e21-477c-a661-150aca8919ed"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GARY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048595793545246124, "Height": 0.01055755652487278, "Left": 0.1324709951877594, "Top": 0.3034360408782959}, "Polygon": [{"X": 0.13247111439704895, "Y": 0.3034360706806183}, {"X": 0.18106679618358612, "Y": 0.3034360408782959}, {"X": 0.18106667697429657, "Y": 0.31399357318878174}, {"X": 0.1324709951877594, "Y": 0.3139936029911041}]}, "Id": "8eda9c57-8c20-4a9f-ae59-fc950aa07044"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03969285637140274, "Height": 0.010540400631725788, "Left": 0.18597270548343658, "Top": 0.3033800721168518}, "Polygon": [{"X": 0.18597282469272614, "Y": 0.3033801019191742}, {"X": 0.22566556930541992, "Y": 0.3033800721168518}, {"X": 0.22566545009613037, "Y": 0.3139204680919647}, {"X": 0.18597270548343658, "Y": 0.3139204680919647}]}, "Id": "3803e195-a90f-45d5-a160-532695d975d2"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "STE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032633114606142044, "Height": 0.010685435496270657, "Left": 0.23124757409095764, "Top": 0.30333617329597473}, "Polygon": [{"X": 0.231247678399086, "Y": 0.3033362030982971}, {"X": 0.2638806700706482, "Y": 0.30333617329597473}, {"X": 0.26388058066368103, "Y": 0.31402158737182617}, {"X": 0.23124757409095764, "Y": 0.31402161717414856}]}, "Id": "a34286d0-4659-4eb9-86d1-c5325fcee382"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009632454253733158, "Height": 0.010002130642533302, "Left": 0.26946213841438293, "Top": 0.30375155806541443}, "Polygon": [{"X": 0.2694622576236725, "Y": 0.3037515878677368}, {"X": 0.2790946066379547, "Y": 0.30375155806541443}, {"X": 0.27909448742866516, "Y": 0.3137536942958832}, {"X": 0.26946213841438293, "Y": 0.3137536942958832}]}, "Id": "79c5f0f3-6933-4836-a200-ba4107eed7ff"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "RONKONKOMA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1306026726961136, "Height": 0.01233705785125494, "Left": 0.09885594248771667, "Top": 0.31890472769737244}, "Polygon": [{"X": 0.09885607659816742, "Y": 0.3189048171043396}, {"X": 0.22945861518383026, "Y": 0.31890472769737244}, {"X": 0.22945848107337952, "Y": 0.331241637468338}, {"X": 0.09885594248771667, "Y": 0.33124178647994995}]}, "Id": "564520ac-1c57-4375-ae39-9685a03b85c9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023219482973217964, "Height": 0.010540696792304516, "Left": 0.23655442893505096, "Top": 0.318867564201355}, "Polygon": [{"X": 0.23655454814434052, "Y": 0.31886759400367737}, {"X": 0.2597739100456238, "Y": 0.318867564201355}, {"X": 0.25977379083633423, "Y": 0.3294082581996918}, {"X": 0.23655442893505096, "Y": 0.3294082581996918}]}, "Id": "aefe44c7-32ab-46d4-a7dd-fd45afcff471"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "11779", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046518877148628235, "Height": 0.010358062572777271, "Left": 0.2757381796836853, "Top": 0.3191644847393036}, "Polygon": [{"X": 0.27573829889297485, "Y": 0.319164514541626}, {"X": 0.32225707173347473, "Y": 0.3191644847393036}, {"X": 0.3222569525241852, "Y": 0.3295224905014038}, {"X": 0.2757381796836853, "Y": 0.3295225501060486}]}, "Id": "6b5709a5-ec9e-4a4e-a11d-e0bf000d119f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Qty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027530808001756668, "Height": 0.012871194630861282, "Left": 0.11765674501657486, "Top": 0.35762766003608704}, "Polygon": [{"X": 0.1176568865776062, "Y": 0.3576276898384094}, {"X": 0.14518755674362183, "Y": 0.35762766003608704}, {"X": 0.1451874077320099, "Y": 0.37049877643585205}, {"X": 0.11765674501657486, "Y": 0.3704988360404968}]}, "Id": "dcf49b17-1549-4667-855e-f2156f236b5d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03335360065102577, "Height": 0.010078689083456993, "Left": 0.16240282356739044, "Top": 0.35757073760032654}, "Polygon": [{"X": 0.16240294277668, "Y": 0.3575707972049713}, {"X": 0.1957564353942871, "Y": 0.35757073760032654}, {"X": 0.19575631618499756, "Y": 0.3676493465900421}, {"X": 0.16240282356739044, "Y": 0.3676494359970093}]}, "Id": "7c20780e-0e3e-4d38-8910-0b07af45a4e4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09157400578260422, "Height": 0.012896859087049961, "Left": 0.2767729163169861, "Top": 0.35746270418167114}, "Polygon": [{"X": 0.27677303552627563, "Y": 0.35746288299560547}, {"X": 0.3683468997478485, "Y": 0.35746270418167114}, {"X": 0.36834678053855896, "Y": 0.3703593611717224}, {"X": 0.2767729163169861, "Y": 0.3703595697879791}]}, "Id": "a857bb16-30bb-49e4-a236-6760e78c76d7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03169097378849983, "Height": 0.010302240028977394, "Left": 0.6950205564498901, "Top": 0.35725143551826477}, "Polygon": [{"X": 0.6950206756591797, "Y": 0.35725149512290955}, {"X": 0.7267115712165833, "Y": 0.35725143551826477}, {"X": 0.7267114520072937, "Y": 0.36755359172821045}, {"X": 0.6950205564498901, "Y": 0.3675536811351776}]}, "Id": "40422d47-6022-4e3a-b1f0-11a4ee8d4d29"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04011659324169159, "Height": 0.010128316469490528, "Left": 0.7324255704879761, "Top": 0.3574267327785492}, "Polygon": [{"X": 0.7324256896972656, "Y": 0.35742682218551636}, {"X": 0.7725421786308289, "Y": 0.3574267327785492}, {"X": 0.7725421190261841, "Y": 0.3675549626350403}, {"X": 0.7324255704879761, "Y": 0.36755505204200745}]}, "Id": "ba30f4f6-aab7-4a95-ada2-31b65df58c0b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03970661759376526, "Height": 0.010305868461728096, "Left": 0.8631364703178406, "Top": 0.3574539124965668}, "Polygon": [{"X": 0.8631365299224854, "Y": 0.35745400190353394}, {"X": 0.9028431177139282, "Y": 0.3574539124965668}, {"X": 0.9028429985046387, "Y": 0.36775970458984375}, {"X": 0.8631364703178406, "Y": 0.3677597939968109}]}, "Id": "146b7625-c757-4d96-8306-6579632f6a92"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005175105296075344, "Height": 0.009204517118632793, "Left": 0.13780032098293304, "Top": 0.3854466676712036}, "Polygon": [{"X": 0.1378004252910614, "Y": 0.385446697473526}, {"X": 0.14297541975975037, "Y": 0.3854466676712036}, {"X": 0.1429753303527832, "Y": 0.3946511745452881}, {"X": 0.13780032098293304, "Y": 0.3946512043476105}]}, "Id": "310beebe-baf3-4905-b7ee-d89bed99346b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SERVICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06785181909799576, "Height": 0.009501578286290169, "Left": 0.16241887211799622, "Top": 0.3851204514503479}, "Polygon": [{"X": 0.16241897642612457, "Y": 0.3851206302642822}, {"X": 0.23027068376541138, "Y": 0.3851204514503479}, {"X": 0.23027057945728302, "Y": 0.3946218192577362}, {"X": 0.16241887211799622, "Y": 0.3946220278739929}]}, "Id": "a339d592-1ebe-42bb-bcfe-9aee2b2a6ee4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LINE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03386561945080757, "Height": 0.009515498764812946, "Left": 0.27670952677726746, "Top": 0.38518837094306946}, "Polygon": [{"X": 0.276709645986557, "Y": 0.3851884603500366}, {"X": 0.3105751574039459, "Y": 0.38518837094306946}, {"X": 0.31057506799697876, "Y": 0.39470374584198}, {"X": 0.27670952677726746, "Y": 0.39470386505126953}]}, "Id": "37c349db-e50d-4e6f-84ac-0a8e70a8a38c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "HAUL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04145300015807152, "Height": 0.009524340741336346, "Left": 0.31637582182884216, "Top": 0.3851979970932007}, "Polygon": [{"X": 0.3163759112358093, "Y": 0.38519811630249023}, {"X": 0.357828825712204, "Y": 0.3851979970932007}, {"X": 0.3578287363052368, "Y": 0.39472222328186035}, {"X": 0.31637582182884216, "Y": 0.3947223424911499}]}, "Id": "8940ce60-4a38-416e-ae55-cb8761eae43a"}, {"BlockType": "WORD", "Confidence": 99.453125, "Text": "(LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04702621325850487, "Height": 0.0119223203510046, "Left": 0.36297380924224854, "Top": 0.38504377007484436}, "Polygon": [{"X": 0.3629739284515381, "Y": 0.3850438892841339}, {"X": 0.4100000262260437, "Y": 0.38504377007484436}, {"X": 0.40999990701675415, "Y": 0.39696595072746277}, {"X": 0.36297380924224854, "Y": 0.3969660699367523}]}, "Id": "63b1763b-7430-4e69-b467-0aa7282cc175"}, {"BlockType": "WORD", "Confidence": 99.619140625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008981353603303432, "Height": 0.008928803727030754, "Left": 0.4146868884563446, "Top": 0.3855556547641754}, "Polygon": [{"X": 0.41468697786331177, "Y": 0.3855556845664978}, {"X": 0.42366823554039, "Y": 0.3855556547641754}, {"X": 0.42366814613342285, "Y": 0.39448443055152893}, {"X": 0.4146868884563446, "Y": 0.3944844603538513}]}, "Id": "8ef1abb1-5d5f-4394-b26f-0c6d8f3cb663"}, {"BlockType": "WORD", "Confidence": 98.69160461425781, "Text": "EST7AC11496960)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13556820154190063, "Height": 0.01193429809063673, "Left": 0.4287722408771515, "Top": 0.3850189745426178}, "Polygon": [{"X": 0.42877236008644104, "Y": 0.38501933217048645}, {"X": 0.5643404722213745, "Y": 0.3850189745426178}, {"X": 0.564340353012085, "Y": 0.3969528377056122}, {"X": 0.4287722408771515, "Y": 0.3969532549381256}]}, "Id": "17b9eb64-8ef7-43de-aee7-390bd61d87d9"}, {"BlockType": "WORD", "Confidence": 88.62882995605469, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00510774040594697, "Height": 0.0020305002108216286, "Left": 0.5696238279342651, "Top": 0.389867901802063}, "Polygon": [{"X": 0.5696238875389099, "Y": 0.389867901802063}, {"X": 0.5747315883636475, "Y": 0.389867901802063}, {"X": 0.5747315883636475, "Y": 0.39189839363098145}, {"X": 0.5696238279342651, "Y": 0.39189839363098145}]}, "Id": "9d9b458e-2fbf-4bdf-8780-880ae0ba89ad"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "QUICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049571212381124496, "Height": 0.009663783945143223, "Left": 0.5795081257820129, "Top": 0.3851432204246521}, "Polygon": [{"X": 0.5795081853866577, "Y": 0.38514336943626404}, {"X": 0.629079282283783, "Y": 0.3851432204246521}, {"X": 0.6290792226791382, "Y": 0.3948068618774414}, {"X": 0.5795081257820129, "Y": 0.39480701088905334}]}, "Id": "09a540eb-b78c-44ea-93b2-38bdd2feb7db"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$1,050.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06959298253059387, "Height": 0.011823841370642185, "Left": 0.6986832022666931, "Top": 0.38457176089286804}, "Polygon": [{"X": 0.6986833214759827, "Y": 0.38457196950912476}, {"X": 0.7682762145996094, "Y": 0.38457176089286804}, {"X": 0.7682760953903198, "Y": 0.39639541506767273}, {"X": 0.6986832022666931, "Y": 0.39639562368392944}]}, "Id": "c33af7be-5c7d-4c9c-8993-b36cce5ff041"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$1,050.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07011725008487701, "Height": 0.01196206919848919, "Left": 0.8291334509849548, "Top": 0.38445863127708435}, "Polygon": [{"X": 0.8291335701942444, "Y": 0.3844588100910187}, {"X": 0.8992506861686707, "Y": 0.38445863127708435}, {"X": 0.8992505669593811, "Y": 0.3964204788208008}, {"X": 0.8291334509849548, "Y": 0.3964207172393799}]}, "Id": "b2c5d940-fae1-4f83-a06a-cf28b73b21be"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030923612415790558, "Height": 0.009453551843762398, "Left": 0.2770519256591797, "Top": 0.3992142677307129}, "Polygon": [{"X": 0.27705201506614685, "Y": 0.39921435713768005}, {"X": 0.30797553062438965, "Y": 0.3992142677307129}, {"X": 0.3079754412174225, "Y": 0.4086677134037018}, {"X": 0.2770519256591797, "Y": 0.40866783261299133}]}, "Id": "b7e01022-8ba4-4cba-8c47-4090e34731c8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03625214844942093, "Height": 0.009116031229496002, "Left": 0.7819064855575562, "Top": 0.7942805886268616}, "Polygon": [{"X": 0.7819065451622009, "Y": 0.7942811250686646}, {"X": 0.8181586265563965, "Y": 0.7942805886268616}, {"X": 0.8181585073471069, "Y": 0.8033961057662964}, {"X": 0.7819064855575562, "Y": 0.8033966422080994}]}, "Id": "7170ce92-d027-47e4-955a-3fe2e839d7f9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$1,050.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06743524223566055, "Height": 0.011619028635323048, "Left": 0.8358770608901978, "Top": 0.7935773730278015}, "Polygon": [{"X": 0.8358771204948425, "Y": 0.7935783863067627}, {"X": 0.9033122658729553, "Y": 0.7935773730278015}, {"X": 0.9033122062683105, "Y": 0.8051953911781311}, {"X": 0.8358770608901978, "Y": 0.8051964044570923}]}, "Id": "71703f2f-2efa-4d9d-ba62-fb90eafbc768"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Balance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05703721195459366, "Height": 0.009046207182109356, "Left": 0.728143572807312, "Top": 0.814724862575531}, "Polygon": [{"X": 0.7281436324119568, "Y": 0.8147256970405579}, {"X": 0.7851807475090027, "Y": 0.814724862575531}, {"X": 0.7851806879043579, "Y": 0.8237701654434204}, {"X": 0.728143572807312, "Y": 0.823771059513092}]}, "Id": "af8f0e9d-5891-4193-98f7-2d5594d70aad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027895381674170494, "Height": 0.00944774504750967, "Left": 0.7907469868659973, "Top": 0.8146428465843201}, "Polygon": [{"X": 0.7907471060752869, "Y": 0.8146432638168335}, {"X": 0.8186423778533936, "Y": 0.8146428465843201}, {"X": 0.8186423182487488, "Y": 0.8240901827812195}, {"X": 0.7907469868659973, "Y": 0.8240906000137329}]}, "Id": "f2ec3dd6-6e0f-4bda-8f42-0ce9e9889aa7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$1,050.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06767306476831436, "Height": 0.01169185247272253, "Left": 0.8359537720680237, "Top": 0.8140668869018555}, "Polygon": [{"X": 0.8359538912773132, "Y": 0.8140679001808167}, {"X": 0.9036268591880798, "Y": 0.8140668869018555}, {"X": 0.9036267399787903, "Y": 0.8257577419281006}, {"X": 0.8359537720680237, "Y": 0.8257587552070618}]}, "Id": "d1c47251-26d7-4c56-956d-1391384b219d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NOTE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05067303776741028, "Height": 0.00991847924888134, "Left": 0.088814876973629, "Top": 0.8527775406837463}, "Polygon": [{"X": 0.08881499618291855, "Y": 0.8527783751487732}, {"X": 0.13948792219161987, "Y": 0.8527775406837463}, {"X": 0.13948780298233032, "Y": 0.8626951575279236}, {"X": 0.088814876973629, "Y": 0.8626959919929504}]}, "Id": "0d3277dd-af3e-4f76-a904-4fe6c45de78d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "QUICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05413668230175972, "Height": 0.010006359778344631, "Left": 0.14593206346035004, "Top": 0.8526974320411682}, "Polygon": [{"X": 0.1459321677684784, "Y": 0.8526982665061951}, {"X": 0.20006874203681946, "Y": 0.8526974320411682}, {"X": 0.2000686377286911, "Y": 0.8627029061317444}, {"X": 0.14593206346035004, "Y": 0.862703800201416}]}, "Id": "b009bc28-9da3-4222-90de-025e0018a254"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03403477743268013, "Height": 0.010193096473813057, "Left": 0.2052888721227646, "Top": 0.8526560664176941}, "Polygon": [{"X": 0.20528899133205414, "Y": 0.8526566028594971}, {"X": 0.23932364583015442, "Y": 0.8526560664176941}, {"X": 0.23932354152202606, "Y": 0.8628486394882202}, {"X": 0.2052888721227646, "Y": 0.8628491759300232}]}, "Id": "f73d0e9c-9aef-41c1-8e38-b533d0b9121b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TERM:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05226214975118637, "Height": 0.010163979604840279, "Left": 0.08829949051141739, "Top": 0.8678386211395264}, "Polygon": [{"X": 0.08829960227012634, "Y": 0.8678394556045532}, {"X": 0.14056164026260376, "Y": 0.8678386211395264}, {"X": 0.1405615210533142, "Y": 0.8780016899108887}, {"X": 0.08829949051141739, "Y": 0.8780025839805603}]}, "Id": "40c2beef-73ed-4b78-8293-b21be16d2590"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033396124839782715, "Height": 0.010148588567972183, "Left": 0.14692415297031403, "Top": 0.8677680492401123}, "Polygon": [{"X": 0.14692427217960358, "Y": 0.8677685856819153}, {"X": 0.18032027781009674, "Y": 0.8677680492401123}, {"X": 0.1803201586008072, "Y": 0.8779160380363464}, {"X": 0.14692415297031403, "Y": 0.8779166340827942}]}, "Id": "0f6904ba-6be9-4e39-ab71-016d4a282bd7"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009130005724728107, "Height": 0.009533206932246685, "Left": 0.18504314124584198, "Top": 0.8682498931884766}, "Polygon": [{"X": 0.18504324555397034, "Y": 0.8682500720024109}, {"X": 0.19417314231395721, "Y": 0.8682498931884766}, {"X": 0.19417303800582886, "Y": 0.877782940864563}, {"X": 0.18504314124584198, "Y": 0.8777831196784973}]}, "Id": "7e516b0c-f593-4d7e-b0fb-494f70536416"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Thank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05014265701174736, "Height": 0.009764598682522774, "Left": 0.08801941573619843, "Top": 0.9010410308837891}, "Polygon": [{"X": 0.08801952749490738, "Y": 0.9010418653488159}, {"X": 0.13816207647323608, "Y": 0.9010410308837891}, {"X": 0.13816195726394653, "Y": 0.9108046889305115}, {"X": 0.08801941573619843, "Y": 0.9108055830001831}]}, "Id": "6faaea46-d5bb-49c0-bc22-f683537a6e70"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "you", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029734119772911072, "Height": 0.010472898371517658, "Left": 0.14246754348278046, "Top": 0.9032124876976013}, "Polygon": [{"X": 0.14246766269207, "Y": 0.9032130241394043}, {"X": 0.17220166325569153, "Y": 0.9032124876976013}, {"X": 0.17220154404640198, "Y": 0.9136848449707031}, {"X": 0.14246754348278046, "Y": 0.9136853814125061}]}, "Id": "b511a42f-b7b0-4c59-b389-ba99b53c85d9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02281980775296688, "Height": 0.009839397855103016, "Left": 0.17700134217739105, "Top": 0.9008824229240417}, "Polygon": [{"X": 0.1770014464855194, "Y": 0.9008827805519104}, {"X": 0.19982114434242249, "Y": 0.9008824229240417}, {"X": 0.19982104003429413, "Y": 0.9107213616371155}, {"X": 0.17700134217739105, "Y": 0.9107217788696289}]}, "Id": "0f736946-6104-4ec4-b1f2-8c2d8d4de380"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03684619069099426, "Height": 0.010543016716837883, "Left": 0.20440518856048584, "Top": 0.9031946063041687}, "Polygon": [{"X": 0.2044052928686142, "Y": 0.9031952619552612}, {"X": 0.2412513643503189, "Y": 0.9031946063041687}, {"X": 0.24125126004219055, "Y": 0.9137369394302368}, {"X": 0.20440518856048584, "Y": 0.9137375950813293}]}, "Id": "ff2cff07-af4a-4146-9e50-b7f6cbf0e9a9"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "business.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07682079821825027, "Height": 0.009928574785590172, "Left": 0.2462375909090042, "Top": 0.9009554386138916}, "Polygon": [{"X": 0.24623769521713257, "Y": 0.9009568095207214}, {"X": 0.3230583965778351, "Y": 0.9009554386138916}, {"X": 0.3230583071708679, "Y": 0.9108826518058777}, {"X": 0.2462375909090042, "Y": 0.9108840227127075}]}, "Id": "fac06bf8-ba87-42ef-a1d4-868cce33da0f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "f371b368-bed4-4a27-8640-c9f8e3466d9d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "f371b368-bed4-4a27-8640-c9f8e3466d9d", "content-type": "application/x-amz-json-1.1", "content-length": "62867", "date": "Thu, 18 Sep 2025 09:49:56 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "8c585813-d0e5-4967-adb4-0a12b0e4d561", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 09:49:58 GMT", "content-type": "application/json", "content-length": "685", "connection": "keep-alive", "x-amzn-requestid": "8c585813-d0e5-4967-adb4-0a12b0e4d561"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It's an invoice. The content shows \"Invoice\" header, \"Invoice No.\" etc. It's a carrier invoice. Not lumper receipt. So doc_type = \"invoice\". Output via tool call classify_logistics_doc_type with documents array of object {page_no:1, doc_type:\"invoice\"}."}}}, {"toolUse": {"toolUseId": "tooluse_hngVPjYIScSv6AMhLz01dg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2367, "outputTokens": 104, "totalTokens": 2471}, "metrics": {"latencyMs": 703}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/2c72b0ad_10_invoice_5.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Initial pallets, finished pallets, carrier, Total amount, PO, Door number, Date-Time, service charge, \n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nInvoice\nDate:\n08/06/2025\nE & R FREIGHT TRUCKING LLC\nInvoice No.:\nA11214\nDue Date:\n08/08/2025\n245 3rd Street\nSalesperson:\nELVIS RODRIGUEZ\nPassaic, NJ 07055\nCustomer PO No: EST7AC11496960\nTel: (973)756-6070\nMC NUMBER:\n014348\nEmail: <EMAIL>\nBill To:\nIFREIGHT LOGISTICS LLC\nMC-1064155\n131 GARY WAY STE 4\nRONKONKOMA, NY 11779\nQty\nItem\nDescription\nUnit Price\nTotal\n1\nSERVICE\nLINE HAUL (LOAD # EST7AC11496960) - QUICK\n$1,050.00\n$1,050.00\nPAY\nTotal $1,050.00\nBalance Due $1,050.00\nNOTE: QUICK PAY\nTERM: NET 2\nThank you for your business.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}