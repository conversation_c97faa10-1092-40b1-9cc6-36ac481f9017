{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.00017843868408817798, "Y": 0.0}, {"X": 1.0, "Y": 4.873228681390174e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "b0b31cd2-da89-479b-9518-9dd04644f56b", "Relationships": [{"Type": "CHILD", "Ids": ["2d7e0806-5791-47ed-a87f-09f1d770f925", "a3f282b8-d53b-41bf-8e5c-4ecf1b4027e4", "06f2be31-5c64-4a33-a52f-e6e69801a112", "b12319b9-cbeb-4597-8963-5c36a2db869f", "219a1264-32a3-4167-896d-d8357197b4b0", "69aba4cb-4831-4bed-aef4-694aab4771d2", "2577ad4c-003c-426c-9748-0da2bc789314", "b3ddd4c0-3b0b-449a-8ffe-1b9938dee391", "2e998d39-b16e-4e4c-a603-744839854a9d", "a5a465d8-9a66-4173-85ab-d810b2192288", "64fe4339-5058-4e5c-b32a-0cbf8f7e2fb7", "6eb2184c-a57a-4cdc-a838-af6e0eff74a6", "8aa30021-a4d1-4c97-8c36-5007a744fe1c", "fd1eb06a-b9b6-40d0-ae21-8ca9dfc8667c", "014849ce-97f0-41b9-9097-825b823df0fe", "598cb24f-1375-47cc-ab09-bca174aa2c5d", "b5733b67-7584-41fd-9ad9-05e4951aacde", "200d0052-29cc-49cb-8c26-c1e0d24134bb", "c23bdb30-1901-4234-b626-d1114666a413", "753f7c63-003e-4ba7-af99-dbbfa0f27e46", "c0a543df-28c3-4a2c-8990-e003deeae61a", "93117e5d-3b0d-4971-8777-cb482d42814a", "5c75ead5-ff57-4ad5-8e1c-7e98862baeaa", "9aae576b-aab0-4d8d-a2a1-e490af4189b3", "d0e355f8-4f45-45d0-92f3-26b664345fc2", "265e75fa-7d2b-4d33-aeb3-9d587dd6eb77", "7b3ca631-3494-4489-bb7d-914ce884fc51", "df725878-cbf4-4913-8de4-7559d54ecbba", "4a948f41-e688-430d-8c9b-258df946084b", "6568fcb5-5009-4eb1-a891-c92b601bb4ad", "c1b2ce98-dddd-4ec2-863f-3fbf055f5f11"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Packing Slip", "Geometry": {"BoundingBox": {"Width": 0.26136067509651184, "Height": 0.039459820836782455, "Left": 0.6808469295501709, "Top": 0.052786462008953094}, "Polygon": [{"X": 0.6809285283088684, "Y": 0.052786462008953094}, {"X": 0.9422076344490051, "Y": 0.0534658208489418}, {"X": 0.9421724081039429, "Y": 0.09224627912044525}, {"X": 0.6808469295501709, "Y": 0.09157577157020569}]}, "Id": "2d7e0806-5791-47ed-a87f-09f1d770f925", "Relationships": [{"Type": "CHILD", "Ids": ["74c7d0e9-50e0-4f94-9cc2-970beaa5ae62", "779c54ae-3b58-4fce-bc7d-37405a6b7e72"]}]}, {"BlockType": "LINE", "Confidence": 99.74569702148438, "Text": "FAB-MASTERS, INC.", "Geometry": {"BoundingBox": {"Width": 0.35247427225112915, "Height": 0.026176445186138153, "Left": 0.12047037482261658, "Top": 0.07846589386463165}, "Polygon": [{"X": 0.1205882802605629, "Y": 0.07846589386463165}, {"X": 0.4729446470737457, "Y": 0.07937360554933548}, {"X": 0.472867488861084, "Y": 0.1046423390507698}, {"X": 0.12047037482261658, "Y": 0.10374239832162857}]}, "Id": "a3f282b8-d53b-41bf-8e5c-4ecf1b4027e4", "Relationships": [{"Type": "CHILD", "Ids": ["0208a4bd-f056-4414-a98c-8c3c58958671", "1d3bfffe-706f-491d-9e79-c939455a4eef"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "42012", "Geometry": {"BoundingBox": {"Width": 0.04037594422698021, "Height": 0.010435985401272774, "Left": 0.8973315358161926, "Top": 0.09746186435222626}, "Polygon": [{"X": 0.8973430395126343, "Y": 0.09746186435222626}, {"X": 0.9377074837684631, "Y": 0.09756524115800858}, {"X": 0.9376978874206543, "Y": 0.10789785534143448}, {"X": 0.8973315358161926, "Y": 0.10779484361410141}]}, "Id": "06f2be31-5c64-4a33-a52f-e6e69801a112", "Relationships": [{"Type": "CHILD", "Ids": ["2d30b99e-c251-482d-8cb6-95bfac1daf9d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.06330995261669159, "Height": 0.008922736160457134, "Left": 0.11239873617887497, "Top": 0.16472658514976501}, "Polygon": [{"X": 0.11243993043899536, "Y": 0.16472658514976501}, {"X": 0.17570868134498596, "Y": 0.16488473117351532}, {"X": 0.17567002773284912, "Y": 0.17364931106567383}, {"X": 0.11239873617887497, "Y": 0.17349164187908173}]}, "Id": "b12319b9-cbeb-4597-8963-5c36a2db869f", "Relationships": [{"Type": "CHILD", "Ids": ["27adc5aa-bedd-4658-a5e5-739d23cba6f3"]}]}, {"BlockType": "LINE", "Confidence": 99.63203430175781, "Text": "901 Joliet St", "Geometry": {"BoundingBox": {"Width": 0.08679516613483429, "Height": 0.009445054456591606, "Left": 0.32855257391929626, "Top": 0.16259372234344482}, "Polygon": [{"X": 0.3285868465900421, "Y": 0.16259372234344482}, {"X": 0.41534772515296936, "Y": 0.1628108024597168}, {"X": 0.4153171479701996, "Y": 0.17203877866268158}, {"X": 0.32855257391929626, "Y": 0.17182238399982452}]}, "Id": "219a1264-32a3-4167-896d-d8357197b4b0", "Relationships": [{"Type": "CHILD", "Ids": ["383395e9-f88a-4a5b-b000-05a3489bc244", "31a6eaf1-f55c-418e-80dd-68421801967e", "d46714cc-e2be-4b64-8f36-12126431e71c"]}]}, {"BlockType": "LINE", "Confidence": 99.96318054199219, "Text": "Ship To: RexCon LLC", "Geometry": {"BoundingBox": {"Width": 0.15023700892925262, "Height": 0.011295393109321594, "Left": 0.575625479221344, "Top": 0.16070900857448578}, "Polygon": [{"X": 0.5756536722183228, "Y": 0.16070900857448578}, {"X": 0.7258625030517578, "Y": 0.16108518838882446}, {"X": 0.725841760635376, "Y": 0.17200440168380737}, {"X": 0.575625479221344, "Y": 0.1716296523809433}]}, "Id": "69aba4cb-4831-4bed-aef4-694aab4771d2", "Relationships": [{"Type": "CHILD", "Ids": ["13628c4f-5d92-4e65-a4d7-73cc4bea6042", "7ba849b5-f46e-4bad-93de-455083baf3c5", "02bd36f8-9e17-4119-be06-29973744779c", "bc11c5d5-eb50-414e-b73d-c72faf6a7332"]}]}, {"BlockType": "LINE", "Confidence": 99.95362854003906, "Text": "Janesville, WI 53548", "Geometry": {"BoundingBox": {"Width": 0.14505845308303833, "Height": 0.012035026215016842, "Left": 0.3281242847442627, "Top": 0.1785234957933426}, "Polygon": [{"X": 0.3281676471233368, "Y": 0.1785234957933426}, {"X": 0.473182737827301, "Y": 0.17888431251049042}, {"X": 0.47314709424972534, "Y": 0.190558522939682}, {"X": 0.3281242847442627, "Y": 0.19019919633865356}]}, "Id": "2577ad4c-003c-426c-9748-0da2bc789314", "Relationships": [{"Type": "CHILD", "Ids": ["18aa6c2d-205f-4cae-9a3e-b7ec9c9af84c", "1caa4616-1761-4e22-9082-5eea1070a537", "b25b703b-bb0d-4777-8c2f-21250c0046ce"]}]}, {"BlockType": "LINE", "Confidence": 99.9704360961914, "Text": "2841 Whiting Rd.", "Geometry": {"BoundingBox": {"Width": 0.12696145474910736, "Height": 0.01191281620413065, "Left": 0.6385650038719177, "Top": 0.17896737158298492}, "Polygon": [{"X": 0.6385916471481323, "Y": 0.17896737158298492}, {"X": 0.7655264735221863, "Y": 0.1792832463979721}, {"X": 0.7655065655708313, "Y": 0.1908801943063736}, {"X": 0.6385650038719177, "Y": 0.19056561589241028}]}, "Id": "b3ddd4c0-3b0b-449a-8ffe-1b9938dee391", "Relationships": [{"Type": "CHILD", "Ids": ["38d60e99-3128-480a-ad6a-2a1276b86784", "14eea680-44c3-43d2-a165-3fee914d0ff6", "e6da11d8-887e-4e28-a738-c31e04bd582d"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "Phone: ************", "Geometry": {"BoundingBox": {"Width": 0.1431792974472046, "Height": 0.011225566267967224, "Left": 0.32856205105781555, "Top": 0.19547609984874725}, "Polygon": [{"X": 0.3286024034023285, "Y": 0.19547609984874725}, {"X": 0.47174134850502014, "Y": 0.1958300918340683}, {"X": 0.47170811891555786, "Y": 0.20670166611671448}, {"X": 0.32856205105781555, "Y": 0.20634903013706207}]}, "Id": "2e998d39-b16e-4e4c-a603-744839854a9d", "Relationships": [{"Type": "CHILD", "Ids": ["5a06880c-bdc2-4e7a-8ac5-d89b77afffa3", "604a947a-8a2a-4b01-a141-eed9e4641e1b"]}]}, {"BlockType": "LINE", "Confidence": 99.89775848388672, "Text": "Burlington, WI 53105", "Geometry": {"BoundingBox": {"Width": 0.15894368290901184, "Height": 0.01148543506860733, "Left": 0.6385923027992249, "Top": 0.19599668681621552}, "Polygon": [{"X": 0.638617753982544, "Y": 0.19599668681621552}, {"X": 0.7975360155105591, "Y": 0.19638973474502563}, {"X": 0.7975186109542847, "Y": 0.20748211443424225}, {"X": 0.6385923027992249, "Y": 0.2070906013250351}]}, "Id": "a5a465d8-9a66-4173-85ab-d810b2192288", "Relationships": [{"Type": "CHILD", "Ids": ["69a8768a-8ca2-48d8-ad62-5fa489098b17", "9d594d8e-6507-4bbf-a24d-8ca5d66da493", "f18d0a51-08ee-4024-8dd9-ca0521eda3b2"]}]}, {"BlockType": "LINE", "Confidence": 99.9462890625, "Text": "Fax: ************", "Geometry": {"BoundingBox": {"Width": 0.12499264627695084, "Height": 0.010854333639144897, "Left": 0.3284149467945099, "Top": 0.21317294239997864}, "Polygon": [{"X": 0.3284541070461273, "Y": 0.21317294239997864}, {"X": 0.4534075856208801, "Y": 0.21347999572753906}, {"X": 0.453374445438385, "Y": 0.22402727603912354}, {"X": 0.3284149467945099, "Y": 0.22372135519981384}]}, "Id": "64fe4339-5058-4e5c-b32a-0cbf8f7e2fb7", "Relationships": [{"Type": "CHILD", "Ids": ["e31ecc68-27a9-492e-a614-c1c4582214a7", "5f3067b2-271a-4d3e-b35d-87a2390b4aef"]}]}, {"BlockType": "LINE", "Confidence": 99.9509048461914, "Text": "Ship Date: 9/19/2025", "Geometry": {"BoundingBox": {"Width": 0.16434402763843536, "Height": 0.011561024934053421, "Left": 0.10755109041929245, "Top": 0.2482149600982666}, "Polygon": [{"X": 0.10760378837585449, "Y": 0.2482149600982666}, {"X": 0.2718951106071472, "Y": 0.24861352145671844}, {"X": 0.2718507945537567, "Y": 0.2597759962081909}, {"X": 0.10755109041929245, "Y": 0.2593790292739868}]}, "Id": "6eb2184c-a57a-4cdc-a838-af6e0eff74a6", "Relationships": [{"Type": "CHILD", "Ids": ["6e3d1274-f9a6-4353-a30a-0adb0acbe832", "5b028ec9-5d40-4c7e-a15b-f57bf05a59b1", "ade89c44-6de3-46d1-ab3a-9a5b339077e4"]}]}, {"BlockType": "LINE", "Confidence": 99.97369384765625, "Text": "Sales Person: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.13517194986343384, "Height": 0.010339627973735332, "Left": 0.10670533031225204, "Top": 0.2653314769268036}, "Polygon": [{"X": 0.10675264149904251, "Y": 0.2653314769268036}, {"X": 0.24187727272510529, "Y": 0.2656572163105011}, {"X": 0.24183614552021027, "Y": 0.2756710946559906}, {"X": 0.10670533031225204, "Y": 0.2753465175628662}]}, "Id": "8aa30021-a4d1-4c97-8c36-5007a744fe1c", "Relationships": [{"Type": "CHILD", "Ids": ["19ec7e29-1e1a-4c99-884c-175a177e4dab", "f872737f-7f1b-4051-b00e-9dbf70a799ef", "7acc6655-3329-4013-b351-68199b742bc8"]}]}, {"BlockType": "LINE", "Confidence": 99.92080688476562, "Text": "Bill To: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10591679811477661, "Height": 0.009451000019907951, "Left": 0.576612651348114, "Top": 0.2646380066871643}, "Polygon": [{"X": 0.5766363739967346, "Y": 0.2646380066871643}, {"X": 0.6825294494628906, "Y": 0.26489347219467163}, {"X": 0.6825101971626282, "Y": 0.2740890085697174}, {"X": 0.576612651348114, "Y": 0.27383437752723694}]}, "Id": "fd1eb06a-b9b6-40d0-ae21-8ca9dfc8667c", "Relationships": [{"Type": "CHILD", "Ids": ["86864dfe-c28b-4a0e-94c0-fb4295d0dee2", "e98d2d12-feb9-4d94-b04f-fac083f4f7fe", "9a457d57-177e-4d8c-b6b2-4eba397c3297"]}]}, {"BlockType": "LINE", "Confidence": 99.63501739501953, "Text": "Purchase Order: PUR3165-104676", "Geometry": {"BoundingBox": {"Width": 0.25457754731178284, "Height": 0.010307539254426956, "Left": 0.10637982189655304, "Top": 0.2826036214828491}, "Polygon": [{"X": 0.10642565786838531, "Y": 0.2826036214828491}, {"X": 0.36095738410949707, "Y": 0.283213347196579}, {"X": 0.36092284321784973, "Y": 0.2929111421108246}, {"X": 0.10637982189655304, "Y": 0.29230356216430664}]}, "Id": "014849ce-97f0-41b9-9097-825b823df0fe", "Relationships": [{"Type": "CHILD", "Ids": ["4778bdf8-d6fc-4e11-a415-934423ed3a3f", "088fdde3-39eb-48aa-9fd5-6fa1e5ebcd87", "31e97294-701c-46ff-b73e-19cde2ed8b44"]}]}, {"BlockType": "LINE", "Confidence": 99.92054748535156, "Text": "Shipped Via: CPU", "Geometry": {"BoundingBox": {"Width": 0.14274202287197113, "Height": 0.012437555938959122, "Left": 0.10497268289327621, "Top": 0.2987845838069916}, "Polygon": [{"X": 0.10502992570400238, "Y": 0.2987845838069916}, {"X": 0.24771471321582794, "Y": 0.2991243600845337}, {"X": 0.24766536056995392, "Y": 0.3112221360206604}, {"X": 0.10497268289327621, "Y": 0.31088387966156006}]}, "Id": "598cb24f-1375-47cc-ab09-bca174aa2c5d", "Relationships": [{"Type": "CHILD", "Ids": ["13664a15-e97c-4c46-860e-e6c8b7b8d66a", "b3f447c1-d517-4483-9170-37d2d9a0c185", "1161b73e-7158-4b72-8dab-4fbd8a022cf0"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Part Number", "Geometry": {"BoundingBox": {"Width": 0.09470200538635254, "Height": 0.009515979327261448, "Left": 0.37956324219703674, "Top": 0.3789333999156952}, "Polygon": [{"X": 0.379595547914505, "Y": 0.3789333999156952}, {"X": 0.4742652475833893, "Y": 0.37915217876434326}, {"X": 0.47423696517944336, "Y": 0.38844940066337585}, {"X": 0.37956324219703674, "Y": 0.38823139667510986}]}, "Id": "b5733b67-7584-41fd-9ad9-05e4951aacde", "Relationships": [{"Type": "CHILD", "Ids": ["9c08655e-2e00-4e94-b8ad-c8f69f8441c4", "76d69b9c-0df9-4e0e-a360-d713f20cdf4c"]}]}, {"BlockType": "LINE", "Confidence": 99.88162231445312, "Text": "Our Job#", "Geometry": {"BoundingBox": {"Width": 0.0679376870393753, "Height": 0.009050609543919563, "Left": 0.5881668329238892, "Top": 0.3791378438472748}, "Polygon": [{"X": 0.5881892442703247, "Y": 0.3791378438472748}, {"X": 0.6561045050621033, "Y": 0.37929481267929077}, {"X": 0.6560848355293274, "Y": 0.3881884515285492}, {"X": 0.5881668329238892, "Y": 0.3880320191383362}]}, "Id": "200d0052-29cc-49cb-8c26-c1e0d24134bb", "Relationships": [{"Type": "CHILD", "Ids": ["782efc73-6d83-40a4-ad2c-14708db3dfc7", "181ade28-22b3-459f-afc0-09bc9e6e4090"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Order Quantity", "Geometry": {"BoundingBox": {"Width": 0.1118588000535965, "Height": 0.011047624051570892, "Left": 0.6913531422615051, "Top": 0.3789621591567993}, "Polygon": [{"X": 0.6913752555847168, "Y": 0.3789621591567993}, {"X": 0.8032119274139404, "Y": 0.3792206645011902}, {"X": 0.8031952977180481, "Y": 0.3900097608566284}, {"X": 0.6913531422615051, "Y": 0.3897522985935211}]}, "Id": "c23bdb30-1901-4234-b626-d1114666a413", "Relationships": [{"Type": "CHILD", "Ids": ["26df60a2-df81-4125-8f71-961c848a789d", "ad4d3758-2b0e-4e53-a131-942db73683e8"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Ship Quantity", "Geometry": {"BoundingBox": {"Width": 0.10044257342815399, "Height": 0.01170393917709589, "Left": 0.8478410840034485, "Top": 0.37937313318252563}, "Polygon": [{"X": 0.8478564023971558, "Y": 0.37937313318252563}, {"X": 0.9482836127281189, "Y": 0.3796052634716034}, {"X": 0.9482735395431519, "Y": 0.39107707142829895}, {"X": 0.8478410840034485, "Y": 0.3908459544181824}]}, "Id": "753f7c63-003e-4ba7-af99-dbbfa0f27e46", "Relationships": [{"Type": "CHILD", "Ids": ["40cf3d5a-dbb8-4b81-8f3a-39ee3d88bba4", "52b389d9-6f11-4802-bc56-34d0469d1618"]}]}, {"BlockType": "LINE", "Confidence": 95.62699127197266, "Text": "524-14659-86, RV R09", "Geometry": {"BoundingBox": {"Width": 0.1679878681898117, "Height": 0.01207063440233469, "Left": 0.10479520261287689, "Top": 0.40216901898384094}, "Polygon": [{"X": 0.10485048592090607, "Y": 0.40216901898384094}, {"X": 0.2727830708026886, "Y": 0.4025535583496094}, {"X": 0.2727367579936981, "Y": 0.41423964500427246}, {"X": 0.10479520261287689, "Y": 0.4138568341732025}]}, "Id": "c0a543df-28c3-4a2c-8990-e003deeae61a", "Relationships": [{"Type": "CHILD", "Ids": ["9b42468a-0357-4c87-90a5-da1e74504d26", "5ad3030e-3061-4b20-9c39-d84a91d1bdd7", "6e049317-a28b-48c9-846a-38073a6a744e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "41828", "Geometry": {"BoundingBox": {"Width": 0.046993888914585114, "Height": 0.009862704202532768, "Left": 0.5897408127784729, "Top": 0.4028176963329315}, "Polygon": [{"X": 0.5897653698921204, "Y": 0.4028176963329315}, {"X": 0.636734664440155, "Y": 0.4029252529144287}, {"X": 0.6367122530937195, "Y": 0.41268038749694824}, {"X": 0.5897408127784729, "Y": 0.4125732183456421}]}, "Id": "93117e5d-3b0d-4971-8777-cb482d42814a", "Relationships": [{"Type": "CHILD", "Ids": ["6d72e6c4-2edd-4756-b2bb-8d7b3dc7b770"]}]}, {"BlockType": "LINE", "Confidence": 99.77300262451172, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008943737484514713, "Height": 0.009359687566757202, "Left": 0.8110321164131165, "Top": 0.4027754068374634}, "Polygon": [{"X": 0.8110461831092834, "Y": 0.4027754068374634}, {"X": 0.8199758529663086, "Y": 0.40279585123062134}, {"X": 0.819962203502655, "Y": 0.4121350944042206}, {"X": 0.8110321164131165, "Y": 0.4121147096157074}]}, "Id": "5c75ead5-ff57-4ad5-8e1c-7e98862baeaa", "Relationships": [{"Type": "CHILD", "Ids": ["65b2630b-15d6-4d30-bc7a-631794d96788"]}]}, {"BlockType": "LINE", "Confidence": 99.59642028808594, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.009217207320034504, "Height": 0.009451779536902905, "Left": 0.9613184332847595, "Top": 0.4038422405719757}, "Polygon": [{"X": 0.9613261818885803, "Y": 0.4038422405719757}, {"X": 0.9705356955528259, "Y": 0.4038633406162262}, {"X": 0.9705283641815186, "Y": 0.4132940173149109}, {"X": 0.9613184332847595, "Y": 0.41327300667762756}]}, "Id": "9aae576b-aab0-4d8d-a2a1-e490af4189b3", "Relationships": [{"Type": "CHILD", "Ids": ["89cacee6-c7d1-4fc9-9ab3-6c246df64ae0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipping", "Geometry": {"BoundingBox": {"Width": 0.06560664623975754, "Height": 0.01266777515411377, "Left": 0.591485857963562, "Top": 0.6460992693901062}, "Polygon": [{"X": 0.5915172100067139, "Y": 0.6460992693901062}, {"X": 0.6570925116539001, "Y": 0.6462352871894836}, {"X": 0.6570648550987244, "Y": 0.65876704454422}, {"X": 0.591485857963562, "Y": 0.6586316823959351}]}, "Id": "d0e355f8-4f45-45d0-92f3-26b664345fc2", "Relationships": [{"Type": "CHILD", "Ids": ["d7ec0c1f-cb97-4e06-a5ab-f1593288405c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.0478743277490139, "Height": 0.01007128320634365, "Left": 0.7296315431594849, "Top": 0.6469079256057739}, "Polygon": [{"X": 0.7296502590179443, "Y": 0.6469079256057739}, {"X": 0.7775058746337891, "Y": 0.6470072269439697}, {"X": 0.7774893641471863, "Y": 0.6569792032241821}, {"X": 0.7296315431594849, "Y": 0.6568803787231445}]}, "Id": "265e75fa-7d2b-4d33-aeb3-9d587dd6eb77", "Relationships": [{"Type": "CHILD", "Ids": ["01a7f5c0-4d01-4fc4-89d9-902ef1bdc324"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Inspector", "Geometry": {"BoundingBox": {"Width": 0.07415719330310822, "Height": 0.012204466387629509, "Left": 0.874270498752594, "Top": 0.6469952464103699}, "Polygon": [{"X": 0.8742851614952087, "Y": 0.6469952464103699}, {"X": 0.948427677154541, "Y": 0.6471490263938904}, {"X": 0.9484171271324158, "Y": 0.6591997146606445}, {"X": 0.874270498752594, "Y": 0.6590466499328613}]}, "Id": "7b3ca631-3494-4489-bb7d-914ce884fc51", "Relationships": [{"Type": "CHILD", "Ids": ["7ad2d390-1575-4843-8f36-52c225e59c7b"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Shipment visually", "Geometry": {"BoundingBox": {"Width": 0.14085103571414948, "Height": 0.013296600431203842, "Left": 0.09481432288885117, "Top": 0.6666083335876465}, "Polygon": [{"X": 0.09487637132406235, "Y": 0.6666083335876465}, {"X": 0.23566536605358124, "Y": 0.6668977737426758}, {"X": 0.2356116771697998, "Y": 0.6799048781394958}, {"X": 0.09481432288885117, "Y": 0.6796170473098755}]}, "Id": "df725878-cbf4-4913-8de4-7559d54ecbba", "Relationships": [{"Type": "CHILD", "Ids": ["8e092da2-9764-43ea-9979-d147dad518a1", "2f62dede-96b9-47a0-a089-9f28c084b332"]}]}, {"BlockType": "LINE", "Confidence": 99.97342681884766, "Text": "checked for damage", "Geometry": {"BoundingBox": {"Width": 0.16142921149730682, "Height": 0.013201344758272171, "Left": 0.09309090673923492, "Top": 0.6825749278068542}, "Polygon": [{"X": 0.09315241128206253, "Y": 0.6825749278068542}, {"X": 0.25452011823654175, "Y": 0.6829044222831726}, {"X": 0.2544681131839752, "Y": 0.6957762837409973}, {"X": 0.09309090673923492, "Y": 0.695448637008667}]}, "Id": "4a948f41-e688-430d-8c9b-258df946084b", "Relationships": [{"Type": "CHILD", "Ids": ["91dd76e9-5889-41d5-9350-bf2c72f68ce6", "101ee804-6ae0-492e-b504-2f6b738f1dc4", "eb2ab65a-c18b-48d8-9596-f86a0761fa94"]}]}, {"BlockType": "LINE", "Confidence": 93.50969696044922, "Text": "Received by: <PERSON>er", "Geometry": {"BoundingBox": {"Width": 0.5097424387931824, "Height": 0.08230233937501907, "Left": 0.43519699573516846, "Top": 0.7668606042861938}, "Polygon": [{"X": 0.4354585111141205, "Y": 0.7668606042861938}, {"X": 0.9449394345283508, "Y": 0.7678631544113159}, {"X": 0.9448668956756592, "Y": 0.8491629362106323}, {"X": 0.43519699573516846, "Y": 0.8481966853141785}]}, "Id": "6568fcb5-5009-4eb1-a891-c92b601bb4ad", "Relationships": [{"Type": "CHILD", "Ids": ["ae7a271d-0db0-477d-8476-44425d7fc9d6", "df35dd0a-8127-4067-9dd7-d19568beb151", "2a01a141-624a-4a1c-9611-952079f013c8", "c5ebab91-d165-4b2e-b8be-9ca82827edf2"]}]}, {"BlockType": "LINE", "Confidence": 21.26624298095703, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.0922727882862091, "Height": 0.05186423659324646, "Left": 0.4071168005466461, "Top": 0.851246178150177}, "Polygon": [{"X": 0.4072895646095276, "Y": 0.851246178150177}, {"X": 0.4993895888328552, "Y": 0.851420521736145}, {"X": 0.499238520860672, "Y": 0.9031103849411011}, {"X": 0.4071168005466461, "Y": 0.9029402136802673}]}, "Id": "c1b2ce98-dddd-4ec2-863f-3fbf055f5f11", "Relationships": [{"Type": "CHILD", "Ids": ["b389b1a6-4254-47ac-8294-e53984a5edab"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1689765900373459, "Height": 0.035109467804431915, "Left": 0.6808556318283081, "Top": 0.052786462008953094}, "Polygon": [{"X": 0.6809285283088684, "Y": 0.052786462008953094}, {"X": 0.8498321771621704, "Y": 0.05322563275694847}, {"X": 0.8497860431671143, "Y": 0.08789592981338501}, {"X": 0.6808556318283081, "Y": 0.08746187388896942}]}, "Id": "74c7d0e9-50e0-4f94-9cc2-970beaa5ae62"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Slip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07837880402803421, "Height": 0.03086075186729431, "Left": 0.8638214468955994, "Top": 0.06138553097844124}, "Polygon": [{"X": 0.8638602495193481, "Y": 0.06138553097844124}, {"X": 0.942200243473053, "Y": 0.06158865988254547}, {"X": 0.9421724081039429, "Y": 0.09224627912044525}, {"X": 0.8638214468955994, "Y": 0.09204524755477905}]}, "Id": "779c54ae-3b58-4fce-bc7d-37405a6b7e72"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "FAB-MASTERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2623155415058136, "Height": 0.024625690653920174, "Left": 0.12047037482261658, "Top": 0.07978639006614685}, "Polygon": [{"X": 0.12058212608098984, "Y": 0.07978639006614685}, {"X": 0.3827859163284302, "Y": 0.08046155422925949}, {"X": 0.38270291686058044, "Y": 0.10441207885742188}, {"X": 0.12047037482261658, "Y": 0.10374239832162857}]}, "Id": "0208a4bd-f056-4414-a98c-8c3c58958671"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07766615599393845, "Height": 0.02002326399087906, "Left": 0.3952784836292267, "Top": 0.07917370647192001}, "Polygon": [{"X": 0.3953460454940796, "Y": 0.07917370647192001}, {"X": 0.4729446470737457, "Y": 0.07937360554933548}, {"X": 0.4728841185569763, "Y": 0.09919697046279907}, {"X": 0.3952784836292267, "Y": 0.09899841248989105}]}, "Id": "1d3bfffe-706f-491d-9e79-c939455a4eef"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "42012", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04037594422698021, "Height": 0.010435985401272774, "Left": 0.8973315358161926, "Top": 0.09746186435222626}, "Polygon": [{"X": 0.8973430395126343, "Y": 0.09746186435222626}, {"X": 0.9377074837684631, "Y": 0.09756524115800858}, {"X": 0.9376978874206543, "Y": 0.10789785534143448}, {"X": 0.8973315358161926, "Y": 0.10779484361410141}]}, "Id": "2d30b99e-c251-482d-8cb6-95bfac1daf9d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06330995261669159, "Height": 0.008922736160457134, "Left": 0.11239873617887497, "Top": 0.16472658514976501}, "Polygon": [{"X": 0.11243993043899536, "Y": 0.16472658514976501}, {"X": 0.17570868134498596, "Y": 0.16488473117351532}, {"X": 0.17567002773284912, "Y": 0.17364931106567383}, {"X": 0.11239873617887497, "Y": 0.17349164187908173}]}, "Id": "27adc5aa-bedd-4658-a5e5-739d23cba6f3"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "901", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023015441372990608, "Height": 0.009012045338749886, "Left": 0.3285527229309082, "Top": 0.1628258377313614}, "Polygon": [{"X": 0.32858598232269287, "Y": 0.1628258377313614}, {"X": 0.35156816244125366, "Y": 0.16288334131240845}, {"X": 0.3515358567237854, "Y": 0.17183788120746613}, {"X": 0.3285527229309082, "Y": 0.1717805713415146}]}, "Id": "383395e9-f88a-4a5b-b000-05a3489bc244"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03944811969995499, "Height": 0.009310156106948853, "Left": 0.3569476306438446, "Top": 0.16268134117126465}, "Polygon": [{"X": 0.3569806218147278, "Y": 0.16268134117126465}, {"X": 0.396395742893219, "Y": 0.16277997195720673}, {"X": 0.3963644206523895, "Y": 0.1719914972782135}, {"X": 0.3569476306438446, "Y": 0.1718931943178177}]}, "Id": "31a6eaf1-f55c-418e-80dd-68421801967e"}, {"BlockType": "WORD", "Confidence": 99.04415893554688, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01541330385953188, "Height": 0.009151531383395195, "Left": 0.39993444085121155, "Top": 0.1627723127603531}, "Polygon": [{"X": 0.3999652862548828, "Y": 0.1627723127603531}, {"X": 0.41534772515296936, "Y": 0.1628108024597168}, {"X": 0.4153175354003906, "Y": 0.17192384600639343}, {"X": 0.39993444085121155, "Y": 0.17188549041748047}]}, "Id": "d46714cc-e2be-4b64-8f36-12126431e71c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03255024924874306, "Height": 0.01100178249180317, "Left": 0.575625479221344, "Top": 0.16070900857448578}, "Polygon": [{"X": 0.5756536722183228, "Y": 0.16070900857448578}, {"X": 0.6081756949424744, "Y": 0.16079045832157135}, {"X": 0.6081491112709045, "Y": 0.1717107892036438}, {"X": 0.575625479221344, "Y": 0.1716296523809433}]}, "Id": "13628c4f-5d92-4e65-a4d7-73cc4bea6042"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022760502994060516, "Height": 0.00868274737149477, "Left": 0.6121966242790222, "Top": 0.1615486443042755}, "Polygon": [{"X": 0.6122174859046936, "Y": 0.1615486443042755}, {"X": 0.6349571347236633, "Y": 0.161605566740036}, {"X": 0.6349372267723083, "Y": 0.1702313870191574}, {"X": 0.6121966242790222, "Y": 0.17017462849617004}]}, "Id": "7ba849b5-f46e-4bad-93de-455083baf3c5"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "RexCon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05459103360772133, "Height": 0.009367226622998714, "Left": 0.6403268575668335, "Top": 0.16194283962249756}, "Polygon": [{"X": 0.640347957611084, "Y": 0.16194283962249756}, {"X": 0.6949178576469421, "Y": 0.16207945346832275}, {"X": 0.6948990821838379, "Y": 0.17131006717681885}, {"X": 0.6403268575668335, "Y": 0.17117388546466827}]}, "Id": "02bd36f8-9e17-4119-be06-29973744779c"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025961054489016533, "Height": 0.00858833733946085, "Left": 0.699897289276123, "Top": 0.16319656372070312}, "Polygon": [{"X": 0.6999144554138184, "Y": 0.16319656372070312}, {"X": 0.7258583307266235, "Y": 0.16326148808002472}, {"X": 0.7258421778678894, "Y": 0.1717848926782608}, {"X": 0.699897289276123, "Y": 0.17172016203403473}]}, "Id": "bc11c5d5-eb50-414e-b73d-c72faf6a7332"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Janesville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07406198978424072, "Height": 0.010711563751101494, "Left": 0.3281242847442627, "Top": 0.179671049118042}, "Polygon": [{"X": 0.32816338539123535, "Y": 0.179671049118042}, {"X": 0.4021862745285034, "Y": 0.17985515296459198}, {"X": 0.4021507203578949, "Y": 0.19038261473178864}, {"X": 0.3281242847442627, "Y": 0.19019919633865356}]}, "Id": "18aa6c2d-205f-4cae-9a3e-b7ec9c9af84c"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018393654376268387, "Height": 0.008811972104012966, "Left": 0.4062047302722931, "Top": 0.17956870794296265}, "Polygon": [{"X": 0.40623417496681213, "Y": 0.17956870794296265}, {"X": 0.4245983958244324, "Y": 0.17961439490318298}, {"X": 0.424569696187973, "Y": 0.18838068842887878}, {"X": 0.4062047302722931, "Y": 0.18833515048027039}]}, "Id": "1caa4616-1761-4e22-9082-5eea1070a537"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "53548", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04041653499007225, "Height": 0.009336201474070549, "Left": 0.4327661991119385, "Top": 0.1787838190793991}, "Polygon": [{"X": 0.43279609084129333, "Y": 0.1787838190793991}, {"X": 0.473182737827301, "Y": 0.17888431251049042}, {"X": 0.47315454483032227, "Y": 0.1881200224161148}, {"X": 0.4327661991119385, "Y": 0.18801987171173096}]}, "Id": "b25b703b-bb0d-4777-8c2f-21250c0046ce"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2841", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03655419871211052, "Height": 0.00892441626638174, "Left": 0.6385713815689087, "Top": 0.17896737158298492}, "Polygon": [{"X": 0.6385916471481323, "Y": 0.17896737158298492}, {"X": 0.6751255393028259, "Y": 0.1790582835674286}, {"X": 0.6751067638397217, "Y": 0.18789179623126984}, {"X": 0.6385713815689087, "Y": 0.18780116736888885}]}, "Id": "38d60e99-3128-480a-ad6a-2a1276b86784"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05775893107056618, "Height": 0.011405164375901222, "Left": 0.6796811819076538, "Top": 0.17940542101860046}, "Polygon": [{"X": 0.6797049045562744, "Y": 0.17940542101860046}, {"X": 0.7374401092529297, "Y": 0.17954908311367035}, {"X": 0.7374193668365479, "Y": 0.19081059098243713}, {"X": 0.6796811819076538, "Y": 0.1906675100326538}]}, "Id": "14eea680-44c3-43d2-a165-3fee914d0ff6"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023475781083106995, "Height": 0.008775699883699417, "Left": 0.7420483231544495, "Top": 0.180594339966774}, "Polygon": [{"X": 0.7420642375946045, "Y": 0.180594339966774}, {"X": 0.7655240893363953, "Y": 0.1806526780128479}, {"X": 0.7655091881752014, "Y": 0.1893700361251831}, {"X": 0.7420483231544495, "Y": 0.18931186199188232}]}, "Id": "e6da11d8-887e-4e28-a738-c31e04bd582d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04874064773321152, "Height": 0.009335720911622047, "Left": 0.32856205105781555, "Top": 0.19713330268859863}, "Polygon": [{"X": 0.3285962641239166, "Y": 0.19713330268859863}, {"X": 0.37730270624160767, "Y": 0.19725368916988373}, {"X": 0.37727054953575134, "Y": 0.20646901428699493}, {"X": 0.32856205105781555, "Y": 0.20634903013706207}]}, "Id": "5a06880c-bdc2-4e7a-8ac5-d89b77afffa3"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0899064913392067, "Height": 0.010756189934909344, "Left": 0.38183486461639404, "Top": 0.19560784101486206}, "Polygon": [{"X": 0.38187140226364136, "Y": 0.19560784101486206}, {"X": 0.47174134850502014, "Y": 0.1958300918340683}, {"X": 0.47170913219451904, "Y": 0.20636402070522308}, {"X": 0.38183486461639404, "Y": 0.2061426043510437}]}, "Id": "604a947a-8a2a-4b01-a141-eed9e4641e1b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Burlington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08174992352724075, "Height": 0.011295259930193424, "Left": 0.6385923027992249, "Top": 0.19599668681621552}, "Polygon": [{"X": 0.638617753982544, "Y": 0.19599668681621552}, {"X": 0.720342218875885, "Y": 0.19619880616664886}, {"X": 0.7203209400177002, "Y": 0.20729194581508636}, {"X": 0.6385923027992249, "Y": 0.2070906013250351}]}, "Id": "69a8768a-8ca2-48d8-ad62-5fa489098b17"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02232258953154087, "Height": 0.008608299307525158, "Left": 0.7246359586715698, "Top": 0.19736430048942566}, "Polygon": [{"X": 0.7246522307395935, "Y": 0.19736430048942566}, {"X": 0.7469585537910461, "Y": 0.1974194496870041}, {"X": 0.7469431161880493, "Y": 0.2059725970029831}, {"X": 0.7246359586715698, "Y": 0.205917626619339}]}, "Id": "9d594d8e-6507-4bbf-a24d-8ca5d66da493"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "53105", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045800186693668365, "Height": 0.00923668872565031, "Left": 0.7517336010932922, "Top": 0.19770413637161255}, "Polygon": [{"X": 0.7517498135566711, "Y": 0.19770413637161255}, {"X": 0.7975337505340576, "Y": 0.19781731069087982}, {"X": 0.7975194454193115, "Y": 0.20694082975387573}, {"X": 0.7517336010932922, "Y": 0.2068280130624771}]}, "Id": "f18d0a51-08ee-4024-8dd9-ca0521eda3b2"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Fax:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02897687442600727, "Height": 0.009142367169260979, "Left": 0.3284149467945099, "Top": 0.21464985609054565}, "Polygon": [{"X": 0.328448623418808, "Y": 0.21464985609054565}, {"X": 0.3573918044567108, "Y": 0.2147209346294403}, {"X": 0.3573593497276306, "Y": 0.2237922102212906}, {"X": 0.3284149467945099, "Y": 0.22372135519981384}]}, "Id": "e31ecc68-27a9-492e-a614-c1c4582214a7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09110303223133087, "Height": 0.010557482950389385, "Left": 0.36230456829071045, "Top": 0.21325621008872986}, "Polygon": [{"X": 0.3623413145542145, "Y": 0.21325621008872986}, {"X": 0.4534075856208801, "Y": 0.21347999572753906}, {"X": 0.4533751308917999, "Y": 0.22381369769573212}, {"X": 0.36230456829071045, "Y": 0.22359073162078857}]}, "Id": "5f3067b2-271a-4d3e-b35d-87a2390b4aef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03285035490989685, "Height": 0.010675131343305111, "Left": 0.10755377262830734, "Top": 0.2482149600982666}, "Polygon": [{"X": 0.10760378837585449, "Y": 0.2482149600982666}, {"X": 0.1404041200876236, "Y": 0.24829453229904175}, {"X": 0.1403556913137436, "Y": 0.2588900923728943}, {"X": 0.10755377262830734, "Y": 0.258810818195343}]}, "Id": "6e3d1274-f9a6-4353-a30a-0adb0acbe832"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038840021938085556, "Height": 0.008894362486898899, "Left": 0.14513275027275085, "Top": 0.24872159957885742}, "Polygon": [{"X": 0.14517277479171753, "Y": 0.24872159957885742}, {"X": 0.18397276103496552, "Y": 0.24881571531295776}, {"X": 0.1839343011379242, "Y": 0.25761595368385315}, {"X": 0.14513275027275085, "Y": 0.2575221359729767}]}, "Id": "5b028ec9-5d40-4c7e-a15b-f57bf05a59b1"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "9/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0831928700208664, "Height": 0.010508754290640354, "Left": 0.18869884312152863, "Top": 0.24926723539829254}, "Polygon": [{"X": 0.1887436807155609, "Y": 0.24926723539829254}, {"X": 0.271891713142395, "Y": 0.2494688779115677}, {"X": 0.2718507945537567, "Y": 0.2597759962081909}, {"X": 0.18869884312152863, "Y": 0.2595750689506531}]}, "Id": "ade89c44-6de3-46d1-ab3a-9a5b339077e4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0398874394595623, "Height": 0.008961455896496773, "Left": 0.1067107617855072, "Top": 0.2653314769268036}, "Polygon": [{"X": 0.10675264149904251, "Y": 0.2653314769268036}, {"X": 0.1465982049703598, "Y": 0.26542752981185913}, {"X": 0.14655794203281403, "Y": 0.274292916059494}, {"X": 0.1067107617855072, "Y": 0.27419716119766235}]}, "Id": "19ec7e29-1e1a-4c99-884c-175a177e4dab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Person:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05532466992735863, "Height": 0.009042423218488693, "Left": 0.15121117234230042, "Top": 0.2660205364227295}, "Polygon": [{"X": 0.15125145018100739, "Y": 0.2660205364227295}, {"X": 0.20653584599494934, "Y": 0.2661537826061249}, {"X": 0.20649781823158264, "Y": 0.2750629782676697}, {"X": 0.15121117234230042, "Y": 0.2749301493167877}]}, "Id": "f872737f-7f1b-4051-b00e-9dbf70a799ef"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02637927047908306, "Height": 0.008643696084618568, "Left": 0.2154921144247055, "Top": 0.2670274078845978}, "Polygon": [{"X": 0.21552838385105133, "Y": 0.2670274078845978}, {"X": 0.24187138676643372, "Y": 0.2670908570289612}, {"X": 0.24183614552021027, "Y": 0.2756710946559906}, {"X": 0.2154921144247055, "Y": 0.2756078243255615}]}, "Id": "7acc6655-3329-4013-b351-68199b742bc8"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022836260497570038, "Height": 0.00885586254298687, "Left": 0.5766136646270752, "Top": 0.2646380066871643}, "Polygon": [{"X": 0.5766363739967346, "Y": 0.2646380066871643}, {"X": 0.5994499325752258, "Y": 0.264693021774292}, {"X": 0.5994281768798828, "Y": 0.27349385619163513}, {"X": 0.5766136646270752, "Y": 0.2734389901161194}]}, "Id": "86864dfe-c28b-4a0e-94c0-fb4295d0dee2"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023690026253461838, "Height": 0.008764742873609066, "Left": 0.6036970615386963, "Top": 0.2648926377296448}, "Polygon": [{"X": 0.6037184000015259, "Y": 0.2648926377296448}, {"X": 0.6273870468139648, "Y": 0.2649497389793396}, {"X": 0.6273666620254517, "Y": 0.2736573815345764}, {"X": 0.6036970615386963, "Y": 0.2736004590988159}]}, "Id": "e98d2d12-feb9-4d94-b04f-fac083f4f7fe"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Astec", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04152856767177582, "Height": 0.008957063779234886, "Left": 0.6410002112388611, "Top": 0.2651319205760956}, "Polygon": [{"X": 0.6410204172134399, "Y": 0.2651319205760956}, {"X": 0.6825287342071533, "Y": 0.26523205637931824}, {"X": 0.6825101971626282, "Y": 0.2740890085697174}, {"X": 0.6410002112388611, "Y": 0.273989200592041}]}, "Id": "9a457d57-177e-4d8c-b6b2-4eba397c3297"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06828074157238007, "Height": 0.009425880387425423, "Left": 0.10638188570737839, "Top": 0.2826036214828491}, "Polygon": [{"X": 0.10642565786838531, "Y": 0.2826036214828491}, {"X": 0.17466263473033905, "Y": 0.28276705741882324}, {"X": 0.17462174594402313, "Y": 0.2920295000076294}, {"X": 0.10638188570737839, "Y": 0.29186657071113586}]}, "Id": "4778bdf8-d6fc-4e11-a415-934423ed3a3f"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Order:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04662464186549187, "Height": 0.008999510668218136, "Left": 0.17875780165195465, "Top": 0.2835880219936371}, "Polygon": [{"X": 0.17879685759544373, "Y": 0.2835880219936371}, {"X": 0.22538243234157562, "Y": 0.28369957208633423}, {"X": 0.22534526884555817, "Y": 0.2925875186920166}, {"X": 0.17875780165195465, "Y": 0.2924763262271881}]}, "Id": "088fdde3-39eb-48aa-9fd5-6fa1e5ebcd87"}, {"BlockType": "WORD", "Confidence": 99.0344009399414, "Text": "PUR3165-104676", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13020989298820496, "Height": 0.009075426496565342, "Left": 0.23074418306350708, "Top": 0.28382834792137146}, "Polygon": [{"X": 0.23078061640262604, "Y": 0.28382834792137146}, {"X": 0.36095407605171204, "Y": 0.28414005041122437}, {"X": 0.3609228730201721, "Y": 0.29290375113487244}, {"X": 0.23074418306350708, "Y": 0.2925930321216583}]}, "Id": "31e97294-701c-46ff-b73e-19cde2ed8b44"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06724026054143906, "Height": 0.012258567847311497, "Left": 0.10497268289327621, "Top": 0.2987845838069916}, "Polygon": [{"X": 0.10502992570400238, "Y": 0.2987845838069916}, {"X": 0.17221294343471527, "Y": 0.2989445626735687}, {"X": 0.17215941846370697, "Y": 0.3110431432723999}, {"X": 0.10497268289327621, "Y": 0.31088387966156006}]}, "Id": "13664a15-e97c-4c46-860e-e6c8b7b8d66a"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Via:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03150191530585289, "Height": 0.00976593792438507, "Left": 0.17712347209453583, "Top": 0.3000074326992035}, "Polygon": [{"X": 0.1771661341190338, "Y": 0.3000074326992035}, {"X": 0.20862539112567902, "Y": 0.3000822961330414}, {"X": 0.20858411490917206, "Y": 0.30977335572242737}, {"X": 0.17712347209453583, "Y": 0.30969876050949097}]}, "Id": "b3f447c1-d517-4483-9170-37d2d9a0c185"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "CPU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03359362110495567, "Height": 0.009667626582086086, "Left": 0.2141152322292328, "Top": 0.30047887563705444}, "Polygon": [{"X": 0.21415582299232483, "Y": 0.30047887563705444}, {"X": 0.24770885705947876, "Y": 0.3005587160587311}, {"X": 0.2476697415113449, "Y": 0.3101465106010437}, {"X": 0.2141152322292328, "Y": 0.31006693840026855}]}, "Id": "1161b73e-7158-4b72-8dab-4fbd8a022cf0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Part", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031287893652915955, "Height": 0.009028171189129353, "Left": 0.37956324219703674, "Top": 0.37927520275115967}, "Polygon": [{"X": 0.3795943856239319, "Y": 0.37927520275115967}, {"X": 0.4108511507511139, "Y": 0.37934741377830505}, {"X": 0.4108212888240814, "Y": 0.38830336928367615}, {"X": 0.37956324219703674, "Y": 0.38823139667510986}]}, "Id": "9c08655e-2e00-4e94-b8ad-c8f69f8441c4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058614280074834824, "Height": 0.009141802787780762, "Left": 0.41565096378326416, "Top": 0.37901681661605835}, "Polygon": [{"X": 0.41568079590797424, "Y": 0.37901681661605835}, {"X": 0.4742652475833893, "Y": 0.37915217876434326}, {"X": 0.4742378294467926, "Y": 0.3881585896015167}, {"X": 0.41565096378326416, "Y": 0.3880236744880676}]}, "Id": "76d69b9c-0df9-4e0e-a360-d713f20cdf4c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027228470891714096, "Height": 0.00860066432505846, "Left": 0.5881668329238892, "Top": 0.3794940114021301}, "Polygon": [{"X": 0.5881883502006531, "Y": 0.3794940114021301}, {"X": 0.6153952479362488, "Y": 0.37955689430236816}, {"X": 0.6153748035430908, "Y": 0.3880946934223175}, {"X": 0.5881668329238892, "Y": 0.3880320191383362}]}, "Id": "782efc73-6d83-40a4-ad2c-14708db3dfc7"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "Job#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03687393292784691, "Height": 0.008967381902039051, "Left": 0.6192305684089661, "Top": 0.37920963764190674}, "Polygon": [{"X": 0.6192517280578613, "Y": 0.37920963764190674}, {"X": 0.6561045050621033, "Y": 0.37929481267929077}, {"X": 0.6560848355293274, "Y": 0.3881770074367523}, {"X": 0.6192305684089661, "Y": 0.38809213042259216}]}, "Id": "181ade28-22b3-459f-afc0-09bc9e6e4090"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04321245849132538, "Height": 0.009138467721641064, "Left": 0.6913567185401917, "Top": 0.3789621591567993}, "Polygon": [{"X": 0.6913752555847168, "Y": 0.3789621591567993}, {"X": 0.7345691919326782, "Y": 0.3790619969367981}, {"X": 0.7345523834228516, "Y": 0.38810062408447266}, {"X": 0.6913567185401917, "Y": 0.38800111413002014}]}, "Id": "26df60a2-df81-4125-8f71-961c848a789d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06442111730575562, "Height": 0.010814959183335304, "Left": 0.7387906312942505, "Top": 0.37919479608535767}, "Polygon": [{"X": 0.7388101816177368, "Y": 0.37919479608535767}, {"X": 0.8032117486000061, "Y": 0.3793436586856842}, {"X": 0.8031952977180481, "Y": 0.3900097608566284}, {"X": 0.7387906312942505, "Y": 0.389861524105072}]}, "Id": "ad4d3758-2b0e-4e53-a131-942db73683e8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03306126594543457, "Height": 0.010801351629197598, "Left": 0.8478420376777649, "Top": 0.37937313318252563}, "Polygon": [{"X": 0.8478564023971558, "Y": 0.37937313318252563}, {"X": 0.8809033036231995, "Y": 0.3794495165348053}, {"X": 0.8808906078338623, "Y": 0.3901744782924652}, {"X": 0.8478420376777649, "Y": 0.3900984227657318}]}, "Id": "40cf3d5a-dbb8-4b81-8f3a-39ee3d88bba4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06307115405797958, "Height": 0.01136680506169796, "Left": 0.885212242603302, "Top": 0.37971025705337524}, "Polygon": [{"X": 0.8852253556251526, "Y": 0.37971025705337524}, {"X": 0.9482834339141846, "Y": 0.37985602021217346}, {"X": 0.9482735395431519, "Y": 0.39107707142829895}, {"X": 0.885212242603302, "Y": 0.39093196392059326}]}, "Id": "52b389d9-6f11-4802-bc56-34d0469d1618"}, {"BlockType": "WORD", "Confidence": 98.82493591308594, "Text": "524-14659-86,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1136588305234909, "Height": 0.011946788057684898, "Left": 0.10479520261287689, "Top": 0.40216901898384094}, "Polygon": [{"X": 0.10485048592090607, "Y": 0.40216901898384094}, {"X": 0.2184540331363678, "Y": 0.40242916345596313}, {"X": 0.2184048295021057, "Y": 0.4141158163547516}, {"X": 0.10479520261287689, "Y": 0.4138568341732025}]}, "Id": "9b42468a-0357-4c87-90a5-da1e74504d26"}, {"BlockType": "WORD", "Confidence": 92.00115966796875, "Text": "RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020761407911777496, "Height": 0.009812340140342712, "Left": 0.21850335597991943, "Top": 0.4030020534992218}, "Polygon": [{"X": 0.21854446828365326, "Y": 0.4030020534992218}, {"X": 0.23926475644111633, "Y": 0.4030494689941406}, {"X": 0.23922456800937653, "Y": 0.4128143787384033}, {"X": 0.21850335597991943, "Y": 0.41276711225509644}]}, "Id": "5ad3030e-3061-4b20-9c39-d84a91d1bdd7"}, {"BlockType": "WORD", "Confidence": 96.05488586425781, "Text": "R09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029052339494228363, "Height": 0.009638422168791294, "Left": 0.2437281757593155, "Top": 0.4031352996826172}, "Polygon": [{"X": 0.2437673807144165, "Y": 0.4031352996826172}, {"X": 0.27278050780296326, "Y": 0.403201699256897}, {"X": 0.2727425694465637, "Y": 0.4127737283706665}, {"X": 0.2437281757593155, "Y": 0.41270753741264343}]}, "Id": "6e049317-a28b-48c9-846a-38073a6a744e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "41828", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046993888914585114, "Height": 0.009862704202532768, "Left": 0.5897408127784729, "Top": 0.4028176963329315}, "Polygon": [{"X": 0.5897653698921204, "Y": 0.4028176963329315}, {"X": 0.636734664440155, "Y": 0.4029252529144287}, {"X": 0.6367122530937195, "Y": 0.41268038749694824}, {"X": 0.5897408127784729, "Y": 0.4125732183456421}]}, "Id": "6d72e6c4-2edd-4756-b2bb-8d7b3dc7b770"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008943737484514713, "Height": 0.009359687566757202, "Left": 0.8110321164131165, "Top": 0.4027754068374634}, "Polygon": [{"X": 0.8110461831092834, "Y": 0.4027754068374634}, {"X": 0.8199758529663086, "Y": 0.40279585123062134}, {"X": 0.819962203502655, "Y": 0.4121350944042206}, {"X": 0.8110321164131165, "Y": 0.4121147096157074}]}, "Id": "65b2630b-15d6-4d30-bc7a-631794d96788"}, {"BlockType": "WORD", "Confidence": 99.59642028808594, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009217207320034504, "Height": 0.009451779536902905, "Left": 0.9613184332847595, "Top": 0.4038422405719757}, "Polygon": [{"X": 0.9613261818885803, "Y": 0.4038422405719757}, {"X": 0.9705356955528259, "Y": 0.4038633406162262}, {"X": 0.9705283641815186, "Y": 0.4132940173149109}, {"X": 0.9613184332847595, "Y": 0.41327300667762756}]}, "Id": "89cacee6-c7d1-4fc9-9ab3-6c246df64ae0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06560664623975754, "Height": 0.01266777515411377, "Left": 0.591485857963562, "Top": 0.6460992693901062}, "Polygon": [{"X": 0.5915172100067139, "Y": 0.6460992693901062}, {"X": 0.6570925116539001, "Y": 0.6462352871894836}, {"X": 0.6570648550987244, "Y": 0.65876704454422}, {"X": 0.591485857963562, "Y": 0.6586316823959351}]}, "Id": "d7ec0c1f-cb97-4e06-a5ab-f1593288405c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0478743277490139, "Height": 0.01007128320634365, "Left": 0.7296315431594849, "Top": 0.6469079256057739}, "Polygon": [{"X": 0.7296502590179443, "Y": 0.6469079256057739}, {"X": 0.7775058746337891, "Y": 0.6470072269439697}, {"X": 0.7774893641471863, "Y": 0.6569792032241821}, {"X": 0.7296315431594849, "Y": 0.6568803787231445}]}, "Id": "01a7f5c0-4d01-4fc4-89d9-902ef1bdc324"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07415719330310822, "Height": 0.012204466387629509, "Left": 0.874270498752594, "Top": 0.6469952464103699}, "Polygon": [{"X": 0.8742851614952087, "Y": 0.6469952464103699}, {"X": 0.948427677154541, "Y": 0.6471490263938904}, {"X": 0.9484171271324158, "Y": 0.6591997146606445}, {"X": 0.874270498752594, "Y": 0.6590466499328613}]}, "Id": "7ad2d390-1575-4843-8f36-52c225e59c7b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07682637870311737, "Height": 0.012818841263651848, "Left": 0.09481597691774368, "Top": 0.6666083335876465}, "Polygon": [{"X": 0.09487637132406235, "Y": 0.6666083335876465}, {"X": 0.17164236307144165, "Y": 0.6667661070823669}, {"X": 0.17158640921115875, "Y": 0.6794271469116211}, {"X": 0.09481597691774368, "Y": 0.6792701482772827}]}, "Id": "8e092da2-9764-43ea-9979-d147dad518a1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "visually", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0592908076941967, "Height": 0.01260433904826641, "Left": 0.17637239396572113, "Top": 0.6673005819320679}, "Polygon": [{"X": 0.17642728984355927, "Y": 0.6673005819320679}, {"X": 0.23566319048404694, "Y": 0.6674222946166992}, {"X": 0.2356116771697998, "Y": 0.6799048781394958}, {"X": 0.17637239396572113, "Y": 0.679783821105957}]}, "Id": "2f62dede-96b9-47a0-a089-9f28c084b332"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0652301087975502, "Height": 0.010491804219782352, "Left": 0.09310292452573776, "Top": 0.6825749278068542}, "Polygon": [{"X": 0.09315241128206253, "Y": 0.6825749278068542}, {"X": 0.15833303332328796, "Y": 0.6827080249786377}, {"X": 0.15828663110733032, "Y": 0.6930667757987976}, {"X": 0.09310292452573776, "Y": 0.6929342746734619}]}, "Id": "91dd76e9-5889-41d5-9350-bf2c72f68ce6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023500947281718254, "Height": 0.009997658431529999, "Left": 0.1634407341480255, "Top": 0.6830585598945618}, "Polygon": [{"X": 0.1634850800037384, "Y": 0.6830585598945618}, {"X": 0.18694168329238892, "Y": 0.6831064224243164}, {"X": 0.18689841032028198, "Y": 0.6930562257766724}, {"X": 0.1634407341480255, "Y": 0.693008542060852}]}, "Id": "101ee804-6ae0-492e-b504-2f6b738f1dc4"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06333280354738235, "Height": 0.01261040847748518, "Left": 0.19118574261665344, "Top": 0.6831659078598022}, "Polygon": [{"X": 0.19123978912830353, "Y": 0.6831659078598022}, {"X": 0.2545185387134552, "Y": 0.6832950711250305}, {"X": 0.2544681131839752, "Y": 0.6957762837409973}, {"X": 0.19118574261665344, "Y": 0.6956478357315063}]}, "Id": "eb2ab65a-c18b-48d8-9596-f86a0761fa94"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12093807011842728, "Height": 0.01908222958445549, "Left": 0.4353081285953522, "Top": 0.794779896736145}, "Polygon": [{"X": 0.43536874651908875, "Y": 0.794779896736145}, {"X": 0.5562462210655212, "Y": 0.7950147390365601}, {"X": 0.5561959743499756, "Y": 0.8138620853424072}, {"X": 0.4353081285953522, "Y": 0.8136292099952698}]}, "Id": "ae7a271d-0db0-477d-8476-44425d7fc9d6"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "by:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040596868842840195, "Height": 0.022788910195231438, "Left": 0.5690933465957642, "Top": 0.7951502203941345}, "Polygon": [{"X": 0.5691524744033813, "Y": 0.7951502203941345}, {"X": 0.6096901893615723, "Y": 0.7952289581298828}, {"X": 0.6096352338790894, "Y": 0.8179391026496887}, {"X": 0.5690933465957642, "Y": 0.8178611397743225}]}, "Id": "df35dd0a-8127-4067-9dd7-d19568beb151"}, {"BlockType": "WORD", "Confidence": 98.19097137451172, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16197578608989716, "Height": 0.050526831299066544, "Left": 0.6227415800094604, "Top": 0.7672293782234192}, "Polygon": [{"X": 0.6228601336479187, "Y": 0.7672293782234192}, {"X": 0.7847173810005188, "Y": 0.7675478458404541}, {"X": 0.7846358418464661, "Y": 0.817756175994873}, {"X": 0.6227415800094604, "Y": 0.8174448013305664}]}, "Id": "2a01a141-624a-4a1c-9611-952079f013c8"}, {"BlockType": "WORD", "Confidence": 76.1561279296875, "Text": "parker", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1511339545249939, "Height": 0.06136317178606987, "Left": 0.79378741979599, "Top": 0.7877997756004333}, "Polygon": [{"X": 0.7938839793205261, "Y": 0.7877997756004333}, {"X": 0.9449213743209839, "Y": 0.7880942821502686}, {"X": 0.9448668956756592, "Y": 0.8491629362106323}, {"X": 0.79378741979599, "Y": 0.8488765358924866}]}, "Id": "c5ebab91-d165-4b2e-b8be-9ca82827edf2"}, {"BlockType": "WORD", "Confidence": 21.26624298095703, "Text": "<PERSON><PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0922727882862091, "Height": 0.05186423659324646, "Left": 0.4071168005466461, "Top": 0.851246178150177}, "Polygon": [{"X": 0.4072895646095276, "Y": 0.851246178150177}, {"X": 0.4993895888328552, "Y": 0.851420521736145}, {"X": 0.499238520860672, "Y": 0.9031103849411011}, {"X": 0.4071168005466461, "Y": 0.9029402136802673}]}, "Id": "b389b1a6-4254-47ac-8294-e53984a5edab"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b2f6064e-cc94-46f6-a257-7af70a6ac68f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b2f6064e-cc94-46f6-a257-7af70a6ac68f", "content-type": "application/x-amz-json-1.1", "content-length": "52050", "date": "Wed, 24 Sep 2025 12:17:43 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "2592f0f1-c270-4ba5-9605-3a71ca6e051a", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:17:45 GMT", "content-type": "application/json", "content-length": "598", "connection": "keep-alive", "x-amzn-requestid": "2592f0f1-c270-4ba5-9605-3a71ca6e051a"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. It seems to be a packing slip / packing list. The keywords: \"Packing Slip\", \"Ship To\", \"Purchase Order\", \"Order Quantity\", \"Ship Quantity\". That's a packing list. So doc_type: pack_list. No continuation. Output via tool call."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"pack_list\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2661, "outputTokens": 83, "totalTokens": 2744}, "metrics": {"latencyMs": 529}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/4dd51aaf_pack_list_P6RAGXD27RW4QUXN96TH.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nPacking Slip\nFAB-MASTERS, INC.\n42012\nAddress:\n901 Joliet St\nShip To: RexCon LLC\nJanesville, WI 53548\n2841 Whiting Rd.\nPhone: ************\nBurlington, WI 53105\nFax: ************\nShip Date: 9/19/2025\nSales Person: Zac\nBill To: Astec\nPurchase Order: PUR3165-104676\nShipped Via: CPU\nPart Number\nOur Job#\nOrder Quantity\nShip Quantity\n524-14659-86, RV R09\n41828\n2\n2\nShipping\nDriver\nInspector\nShipment visually\nchecked for damage\nReceived by: <PERSON> parker\nV<PERSON>\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}