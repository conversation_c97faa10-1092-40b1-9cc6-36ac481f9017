{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.00017843868408817798, "Y": 0.0}, {"X": 1.0, "Y": 4.873228681390174e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "d857783f-61aa-4559-a531-1c872734082c", "Relationships": [{"Type": "CHILD", "Ids": ["4e933211-7535-4e2c-baf7-61cf6db652bd", "4dd50d0d-a4d2-4ccc-b295-cab4d775b878", "b56a10e3-5852-4ac3-abc0-c0f8620fed13", "19c065aa-3675-41aa-9f49-178a19ed3bd0", "8ef8d766-ad78-4930-bfd8-fbcdb3f34b17", "222c41ed-b236-42cf-9923-864d4d02e767", "c830a62d-7f63-449d-9b63-e402f90bc73d", "8aabd79b-47c5-4603-971d-43daa9fbe08d", "0cc4ef2b-aaa4-4a21-9484-424e247f4ad8", "be47c497-3ef4-4dee-9621-cf36a3ff2927", "0cf46509-64fd-4350-8d47-f314e03b4165", "3b522461-1539-4760-a7b4-0044aa249bdb", "e9209370-715e-49fe-80ef-557dd6163616", "3dd36d1a-9143-4d94-9918-25b012950b1f", "6d175224-d982-4d19-94e6-ea393dc03fde", "f919f341-eec9-43e5-9035-97cae32f57de", "f35b2ed5-2a7f-4bd8-b7b9-66638314016f", "d20ac083-3689-49d8-8945-2114c1746342", "f309d523-fc24-48a4-9ad9-a2eb760ed604", "ffd29b98-833d-4882-b34f-9c5cd35ad1da", "d5b7bd8d-71ff-4a34-9990-b6982ae14b58", "05d09c89-edf1-4bbb-96e3-074c5027704e", "4a790db0-da05-45e1-a93a-19d628b25aa1", "f53ef82b-7c1f-4288-aba3-7fa5e9dc5107", "f73206be-9056-4d5c-bab3-e028f58b8a84", "d8700927-c80a-4a91-b1c3-3c3db4be9284", "159665b6-41c4-4191-8dc4-4fde928228f0", "d96cdac5-d52a-41c8-aba5-8d31f50b9821", "60ada26f-17bc-4009-8f1c-b0d9e9162605", "63492e50-51ff-467b-84ac-45ff266ce1e2", "65402547-ccb3-4b3c-8cf2-f6f86fe31dd8"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Packing Slip", "Geometry": {"BoundingBox": {"Width": 0.26136067509651184, "Height": 0.039459820836782455, "Left": 0.6808469295501709, "Top": 0.052786462008953094}, "Polygon": [{"X": 0.6809285283088684, "Y": 0.052786462008953094}, {"X": 0.9422076344490051, "Y": 0.0534658208489418}, {"X": 0.9421724081039429, "Y": 0.09224627912044525}, {"X": 0.6808469295501709, "Y": 0.09157577157020569}]}, "Id": "4e933211-7535-4e2c-baf7-61cf6db652bd", "Relationships": [{"Type": "CHILD", "Ids": ["75c9e599-9670-46c3-8afd-00f53ab26af1", "0a2d5392-44bf-4711-b0bb-d306e836cab8"]}]}, {"BlockType": "LINE", "Confidence": 99.74569702148438, "Text": "FAB-MASTERS, INC.", "Geometry": {"BoundingBox": {"Width": 0.35247427225112915, "Height": 0.026176445186138153, "Left": 0.12047037482261658, "Top": 0.07846589386463165}, "Polygon": [{"X": 0.1205882802605629, "Y": 0.07846589386463165}, {"X": 0.4729446470737457, "Y": 0.07937360554933548}, {"X": 0.472867488861084, "Y": 0.1046423390507698}, {"X": 0.12047037482261658, "Y": 0.10374239832162857}]}, "Id": "4dd50d0d-a4d2-4ccc-b295-cab4d775b878", "Relationships": [{"Type": "CHILD", "Ids": ["0f827117-4e99-4c02-9373-c64f3dd041b3", "0c21a75e-69ac-4ca6-9a9d-1a27ffbe44f9"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "42012", "Geometry": {"BoundingBox": {"Width": 0.04037594422698021, "Height": 0.010435985401272774, "Left": 0.8973315358161926, "Top": 0.09746186435222626}, "Polygon": [{"X": 0.8973430395126343, "Y": 0.09746186435222626}, {"X": 0.9377074837684631, "Y": 0.09756524115800858}, {"X": 0.9376978874206543, "Y": 0.10789785534143448}, {"X": 0.8973315358161926, "Y": 0.10779484361410141}]}, "Id": "b56a10e3-5852-4ac3-abc0-c0f8620fed13", "Relationships": [{"Type": "CHILD", "Ids": ["32ffb1f3-0cf7-4dee-b16e-93ddb186153a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.06330995261669159, "Height": 0.008922736160457134, "Left": 0.11239873617887497, "Top": 0.16472658514976501}, "Polygon": [{"X": 0.11243993043899536, "Y": 0.16472658514976501}, {"X": 0.17570868134498596, "Y": 0.16488473117351532}, {"X": 0.17567002773284912, "Y": 0.17364931106567383}, {"X": 0.11239873617887497, "Y": 0.17349164187908173}]}, "Id": "19c065aa-3675-41aa-9f49-178a19ed3bd0", "Relationships": [{"Type": "CHILD", "Ids": ["9e30b0dd-eb61-4d6f-bec0-618841dba698"]}]}, {"BlockType": "LINE", "Confidence": 99.63203430175781, "Text": "901 Joliet St", "Geometry": {"BoundingBox": {"Width": 0.08679516613483429, "Height": 0.009445054456591606, "Left": 0.32855257391929626, "Top": 0.16259372234344482}, "Polygon": [{"X": 0.3285868465900421, "Y": 0.16259372234344482}, {"X": 0.41534772515296936, "Y": 0.1628108024597168}, {"X": 0.4153171479701996, "Y": 0.17203877866268158}, {"X": 0.32855257391929626, "Y": 0.17182238399982452}]}, "Id": "8ef8d766-ad78-4930-bfd8-fbcdb3f34b17", "Relationships": [{"Type": "CHILD", "Ids": ["0d49af57-ded7-41b3-97ab-70382bf03b32", "e3164b85-b579-4c1f-b635-ed6b73210b02", "98d2e2c9-8e3d-4547-9359-dd55687e4ccf"]}]}, {"BlockType": "LINE", "Confidence": 99.96318054199219, "Text": "Ship To: RexCon LLC", "Geometry": {"BoundingBox": {"Width": 0.15023700892925262, "Height": 0.011295393109321594, "Left": 0.575625479221344, "Top": 0.16070900857448578}, "Polygon": [{"X": 0.5756536722183228, "Y": 0.16070900857448578}, {"X": 0.7258625030517578, "Y": 0.16108518838882446}, {"X": 0.725841760635376, "Y": 0.17200440168380737}, {"X": 0.575625479221344, "Y": 0.1716296523809433}]}, "Id": "222c41ed-b236-42cf-9923-864d4d02e767", "Relationships": [{"Type": "CHILD", "Ids": ["94394445-7a05-4abf-be35-86a5b3ddc618", "4167c31e-2986-4b0b-ac5a-19a74fbfae13", "549b53d7-0b2b-4e94-b82b-d4402d20ff57", "4f135850-aaba-45ea-8f54-b32717d11bcb"]}]}, {"BlockType": "LINE", "Confidence": 99.95362854003906, "Text": "Janesville, WI 53548", "Geometry": {"BoundingBox": {"Width": 0.14505845308303833, "Height": 0.012035026215016842, "Left": 0.3281242847442627, "Top": 0.1785234957933426}, "Polygon": [{"X": 0.3281676471233368, "Y": 0.1785234957933426}, {"X": 0.473182737827301, "Y": 0.17888431251049042}, {"X": 0.47314709424972534, "Y": 0.190558522939682}, {"X": 0.3281242847442627, "Y": 0.19019919633865356}]}, "Id": "c830a62d-7f63-449d-9b63-e402f90bc73d", "Relationships": [{"Type": "CHILD", "Ids": ["aa90d8c9-87dd-4b0b-9b2e-c322eecec6e3", "ad05be90-e288-452a-bf90-3e143e3e47b5", "9f5bf03a-465a-4e25-84a2-a70f5e3164e7"]}]}, {"BlockType": "LINE", "Confidence": 99.9704360961914, "Text": "2841 Whiting Rd.", "Geometry": {"BoundingBox": {"Width": 0.12696145474910736, "Height": 0.01191281620413065, "Left": 0.6385650038719177, "Top": 0.17896737158298492}, "Polygon": [{"X": 0.6385916471481323, "Y": 0.17896737158298492}, {"X": 0.7655264735221863, "Y": 0.1792832463979721}, {"X": 0.7655065655708313, "Y": 0.1908801943063736}, {"X": 0.6385650038719177, "Y": 0.19056561589241028}]}, "Id": "8aabd79b-47c5-4603-971d-43daa9fbe08d", "Relationships": [{"Type": "CHILD", "Ids": ["faa3e476-f8b7-4eff-be69-eb8d681ea726", "dd41d377-adc6-471f-999a-b23556dd125d", "83309972-5852-41ff-a573-5e8a124d0643"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "Phone: ************", "Geometry": {"BoundingBox": {"Width": 0.1431792974472046, "Height": 0.011225566267967224, "Left": 0.32856205105781555, "Top": 0.19547609984874725}, "Polygon": [{"X": 0.3286024034023285, "Y": 0.19547609984874725}, {"X": 0.47174134850502014, "Y": 0.1958300918340683}, {"X": 0.47170811891555786, "Y": 0.20670166611671448}, {"X": 0.32856205105781555, "Y": 0.20634903013706207}]}, "Id": "0cc4ef2b-aaa4-4a21-9484-424e247f4ad8", "Relationships": [{"Type": "CHILD", "Ids": ["184e3e2c-c9e3-4d51-9f58-561072c451b1", "e9e01a08-3276-4b94-a28d-dc24cd7bae33"]}]}, {"BlockType": "LINE", "Confidence": 99.89775848388672, "Text": "Burlington, WI 53105", "Geometry": {"BoundingBox": {"Width": 0.15894368290901184, "Height": 0.01148543506860733, "Left": 0.6385923027992249, "Top": 0.19599668681621552}, "Polygon": [{"X": 0.638617753982544, "Y": 0.19599668681621552}, {"X": 0.7975360155105591, "Y": 0.19638973474502563}, {"X": 0.7975186109542847, "Y": 0.20748211443424225}, {"X": 0.6385923027992249, "Y": 0.2070906013250351}]}, "Id": "be47c497-3ef4-4dee-9621-cf36a3ff2927", "Relationships": [{"Type": "CHILD", "Ids": ["3f76b40b-9a3d-4f58-b901-00d73c1cabd9", "2e96db70-3a96-4e5f-ad48-cafc12dd5ea3", "146478b1-371e-4b0b-bde5-37445951d70f"]}]}, {"BlockType": "LINE", "Confidence": 99.9462890625, "Text": "Fax: ************", "Geometry": {"BoundingBox": {"Width": 0.12499264627695084, "Height": 0.010854333639144897, "Left": 0.3284149467945099, "Top": 0.21317294239997864}, "Polygon": [{"X": 0.3284541070461273, "Y": 0.21317294239997864}, {"X": 0.4534075856208801, "Y": 0.21347999572753906}, {"X": 0.453374445438385, "Y": 0.22402727603912354}, {"X": 0.3284149467945099, "Y": 0.22372135519981384}]}, "Id": "0cf46509-64fd-4350-8d47-f314e03b4165", "Relationships": [{"Type": "CHILD", "Ids": ["242f091b-a0f1-45ca-a428-df9ab2a50597", "3fdf965c-551e-4569-baf6-f6c36ad8716a"]}]}, {"BlockType": "LINE", "Confidence": 99.9509048461914, "Text": "Ship Date: 9/19/2025", "Geometry": {"BoundingBox": {"Width": 0.16434402763843536, "Height": 0.011561024934053421, "Left": 0.10755109041929245, "Top": 0.2482149600982666}, "Polygon": [{"X": 0.10760378837585449, "Y": 0.2482149600982666}, {"X": 0.2718951106071472, "Y": 0.24861352145671844}, {"X": 0.2718507945537567, "Y": 0.2597759962081909}, {"X": 0.10755109041929245, "Y": 0.2593790292739868}]}, "Id": "3b522461-1539-4760-a7b4-0044aa249bdb", "Relationships": [{"Type": "CHILD", "Ids": ["fc5ec9a5-f753-487b-9c86-8fa707c3b5a4", "589247aa-4332-4ace-b9bd-283ed6c43918", "a893e377-d1dc-4eb8-8ced-f0641c04bfaa"]}]}, {"BlockType": "LINE", "Confidence": 99.97369384765625, "Text": "Sales Person: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.13517194986343384, "Height": 0.010339627973735332, "Left": 0.10670533031225204, "Top": 0.2653314769268036}, "Polygon": [{"X": 0.10675264149904251, "Y": 0.2653314769268036}, {"X": 0.24187727272510529, "Y": 0.2656572163105011}, {"X": 0.24183614552021027, "Y": 0.2756710946559906}, {"X": 0.10670533031225204, "Y": 0.2753465175628662}]}, "Id": "e9209370-715e-49fe-80ef-557dd6163616", "Relationships": [{"Type": "CHILD", "Ids": ["d65683b0-5441-48b1-aad2-44fb05fee235", "1090ffa9-0a7a-4c44-82e5-f168b1b6595d", "8d695476-0126-4149-b93c-e702fdc9de01"]}]}, {"BlockType": "LINE", "Confidence": 99.92080688476562, "Text": "Bill To: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10591679811477661, "Height": 0.009451000019907951, "Left": 0.576612651348114, "Top": 0.2646380066871643}, "Polygon": [{"X": 0.5766363739967346, "Y": 0.2646380066871643}, {"X": 0.6825294494628906, "Y": 0.26489347219467163}, {"X": 0.6825101971626282, "Y": 0.2740890085697174}, {"X": 0.576612651348114, "Y": 0.27383437752723694}]}, "Id": "3dd36d1a-9143-4d94-9918-25b012950b1f", "Relationships": [{"Type": "CHILD", "Ids": ["26aec6d1-5039-4123-b801-e7d535063e59", "27bbf141-ba04-49c2-b5f9-02a27b3d6e04", "e4c2efc5-aa72-4157-8ac1-04e1214b6db8"]}]}, {"BlockType": "LINE", "Confidence": 99.63501739501953, "Text": "Purchase Order: PUR3165-104676", "Geometry": {"BoundingBox": {"Width": 0.25457754731178284, "Height": 0.010307539254426956, "Left": 0.10637982189655304, "Top": 0.2826036214828491}, "Polygon": [{"X": 0.10642565786838531, "Y": 0.2826036214828491}, {"X": 0.36095738410949707, "Y": 0.283213347196579}, {"X": 0.36092284321784973, "Y": 0.2929111421108246}, {"X": 0.10637982189655304, "Y": 0.29230356216430664}]}, "Id": "6d175224-d982-4d19-94e6-ea393dc03fde", "Relationships": [{"Type": "CHILD", "Ids": ["fc41305e-9232-4b42-aa5a-46145f70d761", "0cff052f-33e8-4aff-8b73-a0ed5070be60", "584ac328-9c09-4f47-976b-006f343cc12b"]}]}, {"BlockType": "LINE", "Confidence": 99.92054748535156, "Text": "Shipped Via: CPU", "Geometry": {"BoundingBox": {"Width": 0.14274202287197113, "Height": 0.012437555938959122, "Left": 0.10497268289327621, "Top": 0.2987845838069916}, "Polygon": [{"X": 0.10502992570400238, "Y": 0.2987845838069916}, {"X": 0.24771471321582794, "Y": 0.2991243600845337}, {"X": 0.24766536056995392, "Y": 0.3112221360206604}, {"X": 0.10497268289327621, "Y": 0.31088387966156006}]}, "Id": "f919f341-eec9-43e5-9035-97cae32f57de", "Relationships": [{"Type": "CHILD", "Ids": ["d813c7ac-2103-4d29-9d2a-3cd9cc4f0fdc", "b460eb1b-8cda-4cd1-a79f-8f5ba01658a7", "e8181856-6a5c-473f-8fc2-ab1189e7776a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Part Number", "Geometry": {"BoundingBox": {"Width": 0.09470200538635254, "Height": 0.009515979327261448, "Left": 0.37956324219703674, "Top": 0.3789333999156952}, "Polygon": [{"X": 0.379595547914505, "Y": 0.3789333999156952}, {"X": 0.4742652475833893, "Y": 0.37915217876434326}, {"X": 0.47423696517944336, "Y": 0.38844940066337585}, {"X": 0.37956324219703674, "Y": 0.38823139667510986}]}, "Id": "f35b2ed5-2a7f-4bd8-b7b9-66638314016f", "Relationships": [{"Type": "CHILD", "Ids": ["e4546b0e-984d-4f30-9981-242d6a6ff4b0", "7e59a78c-706f-4894-ae97-6fc9ada6aaaf"]}]}, {"BlockType": "LINE", "Confidence": 99.88162231445312, "Text": "Our Job#", "Geometry": {"BoundingBox": {"Width": 0.0679376870393753, "Height": 0.009050609543919563, "Left": 0.5881668329238892, "Top": 0.3791378438472748}, "Polygon": [{"X": 0.5881892442703247, "Y": 0.3791378438472748}, {"X": 0.6561045050621033, "Y": 0.37929481267929077}, {"X": 0.6560848355293274, "Y": 0.3881884515285492}, {"X": 0.5881668329238892, "Y": 0.3880320191383362}]}, "Id": "d20ac083-3689-49d8-8945-2114c1746342", "Relationships": [{"Type": "CHILD", "Ids": ["d1a935c4-21c3-4471-b3cb-db01ca5f68c9", "d7ca2f16-5711-4dd0-b08c-12d5ba2c588e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Order Quantity", "Geometry": {"BoundingBox": {"Width": 0.1118588000535965, "Height": 0.011047624051570892, "Left": 0.6913531422615051, "Top": 0.3789621591567993}, "Polygon": [{"X": 0.6913752555847168, "Y": 0.3789621591567993}, {"X": 0.8032119274139404, "Y": 0.3792206645011902}, {"X": 0.8031952977180481, "Y": 0.3900097608566284}, {"X": 0.6913531422615051, "Y": 0.3897522985935211}]}, "Id": "f309d523-fc24-48a4-9ad9-a2eb760ed604", "Relationships": [{"Type": "CHILD", "Ids": ["973e8f24-26ff-4d88-8ded-a4291f41b834", "7b334858-5e15-4e42-a5dd-1492a9ecbcab"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Ship Quantity", "Geometry": {"BoundingBox": {"Width": 0.10044257342815399, "Height": 0.01170393917709589, "Left": 0.8478410840034485, "Top": 0.37937313318252563}, "Polygon": [{"X": 0.8478564023971558, "Y": 0.37937313318252563}, {"X": 0.9482836127281189, "Y": 0.3796052634716034}, {"X": 0.9482735395431519, "Y": 0.39107707142829895}, {"X": 0.8478410840034485, "Y": 0.3908459544181824}]}, "Id": "ffd29b98-833d-4882-b34f-9c5cd35ad1da", "Relationships": [{"Type": "CHILD", "Ids": ["1e597897-8fbd-49d6-b90f-d9fc564b5c4f", "8c2d7ca3-86a1-4daf-8082-3e4e1a98a65a"]}]}, {"BlockType": "LINE", "Confidence": 95.62699127197266, "Text": "524-14659-86, RV R09", "Geometry": {"BoundingBox": {"Width": 0.1679878681898117, "Height": 0.01207063440233469, "Left": 0.10479520261287689, "Top": 0.40216901898384094}, "Polygon": [{"X": 0.10485048592090607, "Y": 0.40216901898384094}, {"X": 0.2727830708026886, "Y": 0.4025535583496094}, {"X": 0.2727367579936981, "Y": 0.41423964500427246}, {"X": 0.10479520261287689, "Y": 0.4138568341732025}]}, "Id": "d5b7bd8d-71ff-4a34-9990-b6982ae14b58", "Relationships": [{"Type": "CHILD", "Ids": ["f3a83355-0e28-4876-8de3-d4856c84827f", "18b90c44-94db-44f5-a773-24ac32484e43", "36d609ec-eb31-4bb7-a98f-ef31b2ddbe5f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "41828", "Geometry": {"BoundingBox": {"Width": 0.046993888914585114, "Height": 0.009862704202532768, "Left": 0.5897408127784729, "Top": 0.4028176963329315}, "Polygon": [{"X": 0.5897653698921204, "Y": 0.4028176963329315}, {"X": 0.636734664440155, "Y": 0.4029252529144287}, {"X": 0.6367122530937195, "Y": 0.41268038749694824}, {"X": 0.5897408127784729, "Y": 0.4125732183456421}]}, "Id": "05d09c89-edf1-4bbb-96e3-074c5027704e", "Relationships": [{"Type": "CHILD", "Ids": ["0874f2fd-d12c-4e97-91d9-d3a834678a80"]}]}, {"BlockType": "LINE", "Confidence": 99.77300262451172, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008943737484514713, "Height": 0.009359687566757202, "Left": 0.8110321164131165, "Top": 0.4027754068374634}, "Polygon": [{"X": 0.8110461831092834, "Y": 0.4027754068374634}, {"X": 0.8199758529663086, "Y": 0.40279585123062134}, {"X": 0.819962203502655, "Y": 0.4121350944042206}, {"X": 0.8110321164131165, "Y": 0.4121147096157074}]}, "Id": "4a790db0-da05-45e1-a93a-19d628b25aa1", "Relationships": [{"Type": "CHILD", "Ids": ["059ee6e5-0832-4fe6-af9b-2b2a74b681d3"]}]}, {"BlockType": "LINE", "Confidence": 99.59642028808594, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.009217207320034504, "Height": 0.009451779536902905, "Left": 0.9613184332847595, "Top": 0.4038422405719757}, "Polygon": [{"X": 0.9613261818885803, "Y": 0.4038422405719757}, {"X": 0.9705356955528259, "Y": 0.4038633406162262}, {"X": 0.9705283641815186, "Y": 0.4132940173149109}, {"X": 0.9613184332847595, "Y": 0.41327300667762756}]}, "Id": "f53ef82b-7c1f-4288-aba3-7fa5e9dc5107", "Relationships": [{"Type": "CHILD", "Ids": ["1e28791c-3af5-41e8-ba21-659ae9a78511"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipping", "Geometry": {"BoundingBox": {"Width": 0.06560664623975754, "Height": 0.01266777515411377, "Left": 0.591485857963562, "Top": 0.6460992693901062}, "Polygon": [{"X": 0.5915172100067139, "Y": 0.6460992693901062}, {"X": 0.6570925116539001, "Y": 0.6462352871894836}, {"X": 0.6570648550987244, "Y": 0.65876704454422}, {"X": 0.591485857963562, "Y": 0.6586316823959351}]}, "Id": "f73206be-9056-4d5c-bab3-e028f58b8a84", "Relationships": [{"Type": "CHILD", "Ids": ["61b538e6-f876-4604-b040-2f542723852c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.0478743277490139, "Height": 0.01007128320634365, "Left": 0.7296315431594849, "Top": 0.6469079256057739}, "Polygon": [{"X": 0.7296502590179443, "Y": 0.6469079256057739}, {"X": 0.7775058746337891, "Y": 0.6470072269439697}, {"X": 0.7774893641471863, "Y": 0.6569792032241821}, {"X": 0.7296315431594849, "Y": 0.6568803787231445}]}, "Id": "d8700927-c80a-4a91-b1c3-3c3db4be9284", "Relationships": [{"Type": "CHILD", "Ids": ["70f9b208-6288-4ae8-a066-5eb5625683f8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Inspector", "Geometry": {"BoundingBox": {"Width": 0.07415719330310822, "Height": 0.012204466387629509, "Left": 0.874270498752594, "Top": 0.6469952464103699}, "Polygon": [{"X": 0.8742851614952087, "Y": 0.6469952464103699}, {"X": 0.948427677154541, "Y": 0.6471490263938904}, {"X": 0.9484171271324158, "Y": 0.6591997146606445}, {"X": 0.874270498752594, "Y": 0.6590466499328613}]}, "Id": "159665b6-41c4-4191-8dc4-4fde928228f0", "Relationships": [{"Type": "CHILD", "Ids": ["bdc829bd-2c5a-44e0-bf22-63d39b99db09"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Shipment visually", "Geometry": {"BoundingBox": {"Width": 0.14085103571414948, "Height": 0.013296600431203842, "Left": 0.09481432288885117, "Top": 0.6666083335876465}, "Polygon": [{"X": 0.09487637132406235, "Y": 0.6666083335876465}, {"X": 0.23566536605358124, "Y": 0.6668977737426758}, {"X": 0.2356116771697998, "Y": 0.6799048781394958}, {"X": 0.09481432288885117, "Y": 0.6796170473098755}]}, "Id": "d96cdac5-d52a-41c8-aba5-8d31f50b9821", "Relationships": [{"Type": "CHILD", "Ids": ["ca176a89-86de-4d9a-88b7-de970b3e691e", "66bf67ef-e12f-4bf0-bfbb-3726ecc99e4b"]}]}, {"BlockType": "LINE", "Confidence": 99.97342681884766, "Text": "checked for damage", "Geometry": {"BoundingBox": {"Width": 0.16142921149730682, "Height": 0.013201344758272171, "Left": 0.09309090673923492, "Top": 0.6825749278068542}, "Polygon": [{"X": 0.09315241128206253, "Y": 0.6825749278068542}, {"X": 0.25452011823654175, "Y": 0.6829044222831726}, {"X": 0.2544681131839752, "Y": 0.6957762837409973}, {"X": 0.09309090673923492, "Y": 0.695448637008667}]}, "Id": "60ada26f-17bc-4009-8f1c-b0d9e9162605", "Relationships": [{"Type": "CHILD", "Ids": ["d2298ea8-e612-4c4a-b66a-12eb22ca297c", "f24ff579-f4f1-40d3-afad-f8cf2531e14b", "5c5986a2-379e-4242-80d9-22a3033ef867"]}]}, {"BlockType": "LINE", "Confidence": 93.50969696044922, "Text": "Received by: <PERSON>er", "Geometry": {"BoundingBox": {"Width": 0.5097424387931824, "Height": 0.08230233937501907, "Left": 0.43519699573516846, "Top": 0.7668606042861938}, "Polygon": [{"X": 0.4354585111141205, "Y": 0.7668606042861938}, {"X": 0.9449394345283508, "Y": 0.7678631544113159}, {"X": 0.9448668956756592, "Y": 0.8491629362106323}, {"X": 0.43519699573516846, "Y": 0.8481966853141785}]}, "Id": "63492e50-51ff-467b-84ac-45ff266ce1e2", "Relationships": [{"Type": "CHILD", "Ids": ["88c3e522-ef9c-417e-a87c-fa10fb76a982", "466bc72c-6c31-4d57-bacd-3fede90b21d3", "5b87a981-cb40-4c4c-b8b3-3338b9fdf16a", "edcfc353-ef71-4217-87f7-7c37560aa82c"]}]}, {"BlockType": "LINE", "Confidence": 21.26624298095703, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.0922727882862091, "Height": 0.05186423659324646, "Left": 0.4071168005466461, "Top": 0.851246178150177}, "Polygon": [{"X": 0.4072895646095276, "Y": 0.851246178150177}, {"X": 0.4993895888328552, "Y": 0.851420521736145}, {"X": 0.499238520860672, "Y": 0.9031103849411011}, {"X": 0.4071168005466461, "Y": 0.9029402136802673}]}, "Id": "65402547-ccb3-4b3c-8cf2-f6f86fe31dd8", "Relationships": [{"Type": "CHILD", "Ids": ["cfae085b-bc00-43c4-899a-2b68819a0120"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1689765900373459, "Height": 0.035109467804431915, "Left": 0.6808556318283081, "Top": 0.052786462008953094}, "Polygon": [{"X": 0.6809285283088684, "Y": 0.052786462008953094}, {"X": 0.8498321771621704, "Y": 0.05322563275694847}, {"X": 0.8497860431671143, "Y": 0.08789592981338501}, {"X": 0.6808556318283081, "Y": 0.08746187388896942}]}, "Id": "75c9e599-9670-46c3-8afd-00f53ab26af1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Slip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07837880402803421, "Height": 0.03086075186729431, "Left": 0.8638214468955994, "Top": 0.06138553097844124}, "Polygon": [{"X": 0.8638602495193481, "Y": 0.06138553097844124}, {"X": 0.942200243473053, "Y": 0.06158865988254547}, {"X": 0.9421724081039429, "Y": 0.09224627912044525}, {"X": 0.8638214468955994, "Y": 0.09204524755477905}]}, "Id": "0a2d5392-44bf-4711-b0bb-d306e836cab8"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "FAB-MASTERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2623155415058136, "Height": 0.024625690653920174, "Left": 0.12047037482261658, "Top": 0.07978639006614685}, "Polygon": [{"X": 0.12058212608098984, "Y": 0.07978639006614685}, {"X": 0.3827859163284302, "Y": 0.08046155422925949}, {"X": 0.38270291686058044, "Y": 0.10441207885742188}, {"X": 0.12047037482261658, "Y": 0.10374239832162857}]}, "Id": "0f827117-4e99-4c02-9373-c64f3dd041b3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07766615599393845, "Height": 0.02002326399087906, "Left": 0.3952784836292267, "Top": 0.07917370647192001}, "Polygon": [{"X": 0.3953460454940796, "Y": 0.07917370647192001}, {"X": 0.4729446470737457, "Y": 0.07937360554933548}, {"X": 0.4728841185569763, "Y": 0.09919697046279907}, {"X": 0.3952784836292267, "Y": 0.09899841248989105}]}, "Id": "0c21a75e-69ac-4ca6-9a9d-1a27ffbe44f9"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "42012", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04037594422698021, "Height": 0.010435985401272774, "Left": 0.8973315358161926, "Top": 0.09746186435222626}, "Polygon": [{"X": 0.8973430395126343, "Y": 0.09746186435222626}, {"X": 0.9377074837684631, "Y": 0.09756524115800858}, {"X": 0.9376978874206543, "Y": 0.10789785534143448}, {"X": 0.8973315358161926, "Y": 0.10779484361410141}]}, "Id": "32ffb1f3-0cf7-4dee-b16e-93ddb186153a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06330995261669159, "Height": 0.008922736160457134, "Left": 0.11239873617887497, "Top": 0.16472658514976501}, "Polygon": [{"X": 0.11243993043899536, "Y": 0.16472658514976501}, {"X": 0.17570868134498596, "Y": 0.16488473117351532}, {"X": 0.17567002773284912, "Y": 0.17364931106567383}, {"X": 0.11239873617887497, "Y": 0.17349164187908173}]}, "Id": "9e30b0dd-eb61-4d6f-bec0-618841dba698"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "901", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023015441372990608, "Height": 0.009012045338749886, "Left": 0.3285527229309082, "Top": 0.1628258377313614}, "Polygon": [{"X": 0.32858598232269287, "Y": 0.1628258377313614}, {"X": 0.35156816244125366, "Y": 0.16288334131240845}, {"X": 0.3515358567237854, "Y": 0.17183788120746613}, {"X": 0.3285527229309082, "Y": 0.1717805713415146}]}, "Id": "0d49af57-ded7-41b3-97ab-70382bf03b32"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03944811969995499, "Height": 0.009310156106948853, "Left": 0.3569476306438446, "Top": 0.16268134117126465}, "Polygon": [{"X": 0.3569806218147278, "Y": 0.16268134117126465}, {"X": 0.396395742893219, "Y": 0.16277997195720673}, {"X": 0.3963644206523895, "Y": 0.1719914972782135}, {"X": 0.3569476306438446, "Y": 0.1718931943178177}]}, "Id": "e3164b85-b579-4c1f-b635-ed6b73210b02"}, {"BlockType": "WORD", "Confidence": 99.04415893554688, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01541330385953188, "Height": 0.009151531383395195, "Left": 0.39993444085121155, "Top": 0.1627723127603531}, "Polygon": [{"X": 0.3999652862548828, "Y": 0.1627723127603531}, {"X": 0.41534772515296936, "Y": 0.1628108024597168}, {"X": 0.4153175354003906, "Y": 0.17192384600639343}, {"X": 0.39993444085121155, "Y": 0.17188549041748047}]}, "Id": "98d2e2c9-8e3d-4547-9359-dd55687e4ccf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03255024924874306, "Height": 0.01100178249180317, "Left": 0.575625479221344, "Top": 0.16070900857448578}, "Polygon": [{"X": 0.5756536722183228, "Y": 0.16070900857448578}, {"X": 0.6081756949424744, "Y": 0.16079045832157135}, {"X": 0.6081491112709045, "Y": 0.1717107892036438}, {"X": 0.575625479221344, "Y": 0.1716296523809433}]}, "Id": "94394445-7a05-4abf-be35-86a5b3ddc618"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022760502994060516, "Height": 0.00868274737149477, "Left": 0.6121966242790222, "Top": 0.1615486443042755}, "Polygon": [{"X": 0.6122174859046936, "Y": 0.1615486443042755}, {"X": 0.6349571347236633, "Y": 0.161605566740036}, {"X": 0.6349372267723083, "Y": 0.1702313870191574}, {"X": 0.6121966242790222, "Y": 0.17017462849617004}]}, "Id": "4167c31e-2986-4b0b-ac5a-19a74fbfae13"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "RexCon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05459103360772133, "Height": 0.009367226622998714, "Left": 0.6403268575668335, "Top": 0.16194283962249756}, "Polygon": [{"X": 0.640347957611084, "Y": 0.16194283962249756}, {"X": 0.6949178576469421, "Y": 0.16207945346832275}, {"X": 0.6948990821838379, "Y": 0.17131006717681885}, {"X": 0.6403268575668335, "Y": 0.17117388546466827}]}, "Id": "549b53d7-0b2b-4e94-b82b-d4402d20ff57"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025961054489016533, "Height": 0.00858833733946085, "Left": 0.699897289276123, "Top": 0.16319656372070312}, "Polygon": [{"X": 0.6999144554138184, "Y": 0.16319656372070312}, {"X": 0.7258583307266235, "Y": 0.16326148808002472}, {"X": 0.7258421778678894, "Y": 0.1717848926782608}, {"X": 0.699897289276123, "Y": 0.17172016203403473}]}, "Id": "4f135850-aaba-45ea-8f54-b32717d11bcb"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Janesville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07406198978424072, "Height": 0.010711563751101494, "Left": 0.3281242847442627, "Top": 0.179671049118042}, "Polygon": [{"X": 0.32816338539123535, "Y": 0.179671049118042}, {"X": 0.4021862745285034, "Y": 0.17985515296459198}, {"X": 0.4021507203578949, "Y": 0.19038261473178864}, {"X": 0.3281242847442627, "Y": 0.19019919633865356}]}, "Id": "aa90d8c9-87dd-4b0b-9b2e-c322eecec6e3"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018393654376268387, "Height": 0.008811972104012966, "Left": 0.4062047302722931, "Top": 0.17956870794296265}, "Polygon": [{"X": 0.40623417496681213, "Y": 0.17956870794296265}, {"X": 0.4245983958244324, "Y": 0.17961439490318298}, {"X": 0.424569696187973, "Y": 0.18838068842887878}, {"X": 0.4062047302722931, "Y": 0.18833515048027039}]}, "Id": "ad05be90-e288-452a-bf90-3e143e3e47b5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "53548", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04041653499007225, "Height": 0.009336201474070549, "Left": 0.4327661991119385, "Top": 0.1787838190793991}, "Polygon": [{"X": 0.43279609084129333, "Y": 0.1787838190793991}, {"X": 0.473182737827301, "Y": 0.17888431251049042}, {"X": 0.47315454483032227, "Y": 0.1881200224161148}, {"X": 0.4327661991119385, "Y": 0.18801987171173096}]}, "Id": "9f5bf03a-465a-4e25-84a2-a70f5e3164e7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2841", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03655419871211052, "Height": 0.00892441626638174, "Left": 0.6385713815689087, "Top": 0.17896737158298492}, "Polygon": [{"X": 0.6385916471481323, "Y": 0.17896737158298492}, {"X": 0.6751255393028259, "Y": 0.1790582835674286}, {"X": 0.6751067638397217, "Y": 0.18789179623126984}, {"X": 0.6385713815689087, "Y": 0.18780116736888885}]}, "Id": "faa3e476-f8b7-4eff-be69-eb8d681ea726"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05775893107056618, "Height": 0.011405164375901222, "Left": 0.6796811819076538, "Top": 0.17940542101860046}, "Polygon": [{"X": 0.6797049045562744, "Y": 0.17940542101860046}, {"X": 0.7374401092529297, "Y": 0.17954908311367035}, {"X": 0.7374193668365479, "Y": 0.19081059098243713}, {"X": 0.6796811819076538, "Y": 0.1906675100326538}]}, "Id": "dd41d377-adc6-471f-999a-b23556dd125d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023475781083106995, "Height": 0.008775699883699417, "Left": 0.7420483231544495, "Top": 0.180594339966774}, "Polygon": [{"X": 0.7420642375946045, "Y": 0.180594339966774}, {"X": 0.7655240893363953, "Y": 0.1806526780128479}, {"X": 0.7655091881752014, "Y": 0.1893700361251831}, {"X": 0.7420483231544495, "Y": 0.18931186199188232}]}, "Id": "83309972-5852-41ff-a573-5e8a124d0643"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04874064773321152, "Height": 0.009335720911622047, "Left": 0.32856205105781555, "Top": 0.19713330268859863}, "Polygon": [{"X": 0.3285962641239166, "Y": 0.19713330268859863}, {"X": 0.37730270624160767, "Y": 0.19725368916988373}, {"X": 0.37727054953575134, "Y": 0.20646901428699493}, {"X": 0.32856205105781555, "Y": 0.20634903013706207}]}, "Id": "184e3e2c-c9e3-4d51-9f58-561072c451b1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0899064913392067, "Height": 0.010756189934909344, "Left": 0.38183486461639404, "Top": 0.19560784101486206}, "Polygon": [{"X": 0.38187140226364136, "Y": 0.19560784101486206}, {"X": 0.47174134850502014, "Y": 0.1958300918340683}, {"X": 0.47170913219451904, "Y": 0.20636402070522308}, {"X": 0.38183486461639404, "Y": 0.2061426043510437}]}, "Id": "e9e01a08-3276-4b94-a28d-dc24cd7bae33"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Burlington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08174992352724075, "Height": 0.011295259930193424, "Left": 0.6385923027992249, "Top": 0.19599668681621552}, "Polygon": [{"X": 0.638617753982544, "Y": 0.19599668681621552}, {"X": 0.720342218875885, "Y": 0.19619880616664886}, {"X": 0.7203209400177002, "Y": 0.20729194581508636}, {"X": 0.6385923027992249, "Y": 0.2070906013250351}]}, "Id": "3f76b40b-9a3d-4f58-b901-00d73c1cabd9"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02232258953154087, "Height": 0.008608299307525158, "Left": 0.7246359586715698, "Top": 0.19736430048942566}, "Polygon": [{"X": 0.7246522307395935, "Y": 0.19736430048942566}, {"X": 0.7469585537910461, "Y": 0.1974194496870041}, {"X": 0.7469431161880493, "Y": 0.2059725970029831}, {"X": 0.7246359586715698, "Y": 0.205917626619339}]}, "Id": "2e96db70-3a96-4e5f-ad48-cafc12dd5ea3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "53105", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045800186693668365, "Height": 0.00923668872565031, "Left": 0.7517336010932922, "Top": 0.19770413637161255}, "Polygon": [{"X": 0.7517498135566711, "Y": 0.19770413637161255}, {"X": 0.7975337505340576, "Y": 0.19781731069087982}, {"X": 0.7975194454193115, "Y": 0.20694082975387573}, {"X": 0.7517336010932922, "Y": 0.2068280130624771}]}, "Id": "146478b1-371e-4b0b-bde5-37445951d70f"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Fax:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02897687442600727, "Height": 0.009142367169260979, "Left": 0.3284149467945099, "Top": 0.21464985609054565}, "Polygon": [{"X": 0.328448623418808, "Y": 0.21464985609054565}, {"X": 0.3573918044567108, "Y": 0.2147209346294403}, {"X": 0.3573593497276306, "Y": 0.2237922102212906}, {"X": 0.3284149467945099, "Y": 0.22372135519981384}]}, "Id": "242f091b-a0f1-45ca-a428-df9ab2a50597"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09110303223133087, "Height": 0.010557482950389385, "Left": 0.36230456829071045, "Top": 0.21325621008872986}, "Polygon": [{"X": 0.3623413145542145, "Y": 0.21325621008872986}, {"X": 0.4534075856208801, "Y": 0.21347999572753906}, {"X": 0.4533751308917999, "Y": 0.22381369769573212}, {"X": 0.36230456829071045, "Y": 0.22359073162078857}]}, "Id": "3fdf965c-551e-4569-baf6-f6c36ad8716a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03285035490989685, "Height": 0.010675131343305111, "Left": 0.10755377262830734, "Top": 0.2482149600982666}, "Polygon": [{"X": 0.10760378837585449, "Y": 0.2482149600982666}, {"X": 0.1404041200876236, "Y": 0.24829453229904175}, {"X": 0.1403556913137436, "Y": 0.2588900923728943}, {"X": 0.10755377262830734, "Y": 0.258810818195343}]}, "Id": "fc5ec9a5-f753-487b-9c86-8fa707c3b5a4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038840021938085556, "Height": 0.008894362486898899, "Left": 0.14513275027275085, "Top": 0.24872159957885742}, "Polygon": [{"X": 0.14517277479171753, "Y": 0.24872159957885742}, {"X": 0.18397276103496552, "Y": 0.24881571531295776}, {"X": 0.1839343011379242, "Y": 0.25761595368385315}, {"X": 0.14513275027275085, "Y": 0.2575221359729767}]}, "Id": "589247aa-4332-4ace-b9bd-283ed6c43918"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "9/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0831928700208664, "Height": 0.010508754290640354, "Left": 0.18869884312152863, "Top": 0.24926723539829254}, "Polygon": [{"X": 0.1887436807155609, "Y": 0.24926723539829254}, {"X": 0.271891713142395, "Y": 0.2494688779115677}, {"X": 0.2718507945537567, "Y": 0.2597759962081909}, {"X": 0.18869884312152863, "Y": 0.2595750689506531}]}, "Id": "a893e377-d1dc-4eb8-8ced-f0641c04bfaa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0398874394595623, "Height": 0.008961455896496773, "Left": 0.1067107617855072, "Top": 0.2653314769268036}, "Polygon": [{"X": 0.10675264149904251, "Y": 0.2653314769268036}, {"X": 0.1465982049703598, "Y": 0.26542752981185913}, {"X": 0.14655794203281403, "Y": 0.274292916059494}, {"X": 0.1067107617855072, "Y": 0.27419716119766235}]}, "Id": "d65683b0-5441-48b1-aad2-44fb05fee235"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Person:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05532466992735863, "Height": 0.009042423218488693, "Left": 0.15121117234230042, "Top": 0.2660205364227295}, "Polygon": [{"X": 0.15125145018100739, "Y": 0.2660205364227295}, {"X": 0.20653584599494934, "Y": 0.2661537826061249}, {"X": 0.20649781823158264, "Y": 0.2750629782676697}, {"X": 0.15121117234230042, "Y": 0.2749301493167877}]}, "Id": "1090ffa9-0a7a-4c44-82e5-f168b1b6595d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02637927047908306, "Height": 0.008643696084618568, "Left": 0.2154921144247055, "Top": 0.2670274078845978}, "Polygon": [{"X": 0.21552838385105133, "Y": 0.2670274078845978}, {"X": 0.24187138676643372, "Y": 0.2670908570289612}, {"X": 0.24183614552021027, "Y": 0.2756710946559906}, {"X": 0.2154921144247055, "Y": 0.2756078243255615}]}, "Id": "8d695476-0126-4149-b93c-e702fdc9de01"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022836260497570038, "Height": 0.00885586254298687, "Left": 0.5766136646270752, "Top": 0.2646380066871643}, "Polygon": [{"X": 0.5766363739967346, "Y": 0.2646380066871643}, {"X": 0.5994499325752258, "Y": 0.264693021774292}, {"X": 0.5994281768798828, "Y": 0.27349385619163513}, {"X": 0.5766136646270752, "Y": 0.2734389901161194}]}, "Id": "26aec6d1-5039-4123-b801-e7d535063e59"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023690026253461838, "Height": 0.008764742873609066, "Left": 0.6036970615386963, "Top": 0.2648926377296448}, "Polygon": [{"X": 0.6037184000015259, "Y": 0.2648926377296448}, {"X": 0.6273870468139648, "Y": 0.2649497389793396}, {"X": 0.6273666620254517, "Y": 0.2736573815345764}, {"X": 0.6036970615386963, "Y": 0.2736004590988159}]}, "Id": "27bbf141-ba04-49c2-b5f9-02a27b3d6e04"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Astec", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04152856767177582, "Height": 0.008957063779234886, "Left": 0.6410002112388611, "Top": 0.2651319205760956}, "Polygon": [{"X": 0.6410204172134399, "Y": 0.2651319205760956}, {"X": 0.6825287342071533, "Y": 0.26523205637931824}, {"X": 0.6825101971626282, "Y": 0.2740890085697174}, {"X": 0.6410002112388611, "Y": 0.273989200592041}]}, "Id": "e4c2efc5-aa72-4157-8ac1-04e1214b6db8"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06828074157238007, "Height": 0.009425880387425423, "Left": 0.10638188570737839, "Top": 0.2826036214828491}, "Polygon": [{"X": 0.10642565786838531, "Y": 0.2826036214828491}, {"X": 0.17466263473033905, "Y": 0.28276705741882324}, {"X": 0.17462174594402313, "Y": 0.2920295000076294}, {"X": 0.10638188570737839, "Y": 0.29186657071113586}]}, "Id": "fc41305e-9232-4b42-aa5a-46145f70d761"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Order:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04662464186549187, "Height": 0.008999510668218136, "Left": 0.17875780165195465, "Top": 0.2835880219936371}, "Polygon": [{"X": 0.17879685759544373, "Y": 0.2835880219936371}, {"X": 0.22538243234157562, "Y": 0.28369957208633423}, {"X": 0.22534526884555817, "Y": 0.2925875186920166}, {"X": 0.17875780165195465, "Y": 0.2924763262271881}]}, "Id": "0cff052f-33e8-4aff-8b73-a0ed5070be60"}, {"BlockType": "WORD", "Confidence": 99.0344009399414, "Text": "PUR3165-104676", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13020989298820496, "Height": 0.009075426496565342, "Left": 0.23074418306350708, "Top": 0.28382834792137146}, "Polygon": [{"X": 0.23078061640262604, "Y": 0.28382834792137146}, {"X": 0.36095407605171204, "Y": 0.28414005041122437}, {"X": 0.3609228730201721, "Y": 0.29290375113487244}, {"X": 0.23074418306350708, "Y": 0.2925930321216583}]}, "Id": "584ac328-9c09-4f47-976b-006f343cc12b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06724026054143906, "Height": 0.012258567847311497, "Left": 0.10497268289327621, "Top": 0.2987845838069916}, "Polygon": [{"X": 0.10502992570400238, "Y": 0.2987845838069916}, {"X": 0.17221294343471527, "Y": 0.2989445626735687}, {"X": 0.17215941846370697, "Y": 0.3110431432723999}, {"X": 0.10497268289327621, "Y": 0.31088387966156006}]}, "Id": "d813c7ac-2103-4d29-9d2a-3cd9cc4f0fdc"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Via:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03150191530585289, "Height": 0.00976593792438507, "Left": 0.17712347209453583, "Top": 0.3000074326992035}, "Polygon": [{"X": 0.1771661341190338, "Y": 0.3000074326992035}, {"X": 0.20862539112567902, "Y": 0.3000822961330414}, {"X": 0.20858411490917206, "Y": 0.30977335572242737}, {"X": 0.17712347209453583, "Y": 0.30969876050949097}]}, "Id": "b460eb1b-8cda-4cd1-a79f-8f5ba01658a7"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "CPU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03359362110495567, "Height": 0.009667626582086086, "Left": 0.2141152322292328, "Top": 0.30047887563705444}, "Polygon": [{"X": 0.21415582299232483, "Y": 0.30047887563705444}, {"X": 0.24770885705947876, "Y": 0.3005587160587311}, {"X": 0.2476697415113449, "Y": 0.3101465106010437}, {"X": 0.2141152322292328, "Y": 0.31006693840026855}]}, "Id": "e8181856-6a5c-473f-8fc2-ab1189e7776a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Part", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031287893652915955, "Height": 0.009028171189129353, "Left": 0.37956324219703674, "Top": 0.37927520275115967}, "Polygon": [{"X": 0.3795943856239319, "Y": 0.37927520275115967}, {"X": 0.4108511507511139, "Y": 0.37934741377830505}, {"X": 0.4108212888240814, "Y": 0.38830336928367615}, {"X": 0.37956324219703674, "Y": 0.38823139667510986}]}, "Id": "e4546b0e-984d-4f30-9981-242d6a6ff4b0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058614280074834824, "Height": 0.009141802787780762, "Left": 0.41565096378326416, "Top": 0.37901681661605835}, "Polygon": [{"X": 0.41568079590797424, "Y": 0.37901681661605835}, {"X": 0.4742652475833893, "Y": 0.37915217876434326}, {"X": 0.4742378294467926, "Y": 0.3881585896015167}, {"X": 0.41565096378326416, "Y": 0.3880236744880676}]}, "Id": "7e59a78c-706f-4894-ae97-6fc9ada6aaaf"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027228470891714096, "Height": 0.00860066432505846, "Left": 0.5881668329238892, "Top": 0.3794940114021301}, "Polygon": [{"X": 0.5881883502006531, "Y": 0.3794940114021301}, {"X": 0.6153952479362488, "Y": 0.37955689430236816}, {"X": 0.6153748035430908, "Y": 0.3880946934223175}, {"X": 0.5881668329238892, "Y": 0.3880320191383362}]}, "Id": "d1a935c4-21c3-4471-b3cb-db01ca5f68c9"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "Job#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03687393292784691, "Height": 0.008967381902039051, "Left": 0.6192305684089661, "Top": 0.37920963764190674}, "Polygon": [{"X": 0.6192517280578613, "Y": 0.37920963764190674}, {"X": 0.6561045050621033, "Y": 0.37929481267929077}, {"X": 0.6560848355293274, "Y": 0.3881770074367523}, {"X": 0.6192305684089661, "Y": 0.38809213042259216}]}, "Id": "d7ca2f16-5711-4dd0-b08c-12d5ba2c588e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04321245849132538, "Height": 0.009138467721641064, "Left": 0.6913567185401917, "Top": 0.3789621591567993}, "Polygon": [{"X": 0.6913752555847168, "Y": 0.3789621591567993}, {"X": 0.7345691919326782, "Y": 0.3790619969367981}, {"X": 0.7345523834228516, "Y": 0.38810062408447266}, {"X": 0.6913567185401917, "Y": 0.38800111413002014}]}, "Id": "973e8f24-26ff-4d88-8ded-a4291f41b834"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06442111730575562, "Height": 0.010814959183335304, "Left": 0.7387906312942505, "Top": 0.37919479608535767}, "Polygon": [{"X": 0.7388101816177368, "Y": 0.37919479608535767}, {"X": 0.8032117486000061, "Y": 0.3793436586856842}, {"X": 0.8031952977180481, "Y": 0.3900097608566284}, {"X": 0.7387906312942505, "Y": 0.389861524105072}]}, "Id": "7b334858-5e15-4e42-a5dd-1492a9ecbcab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03306126594543457, "Height": 0.010801351629197598, "Left": 0.8478420376777649, "Top": 0.37937313318252563}, "Polygon": [{"X": 0.8478564023971558, "Y": 0.37937313318252563}, {"X": 0.8809033036231995, "Y": 0.3794495165348053}, {"X": 0.8808906078338623, "Y": 0.3901744782924652}, {"X": 0.8478420376777649, "Y": 0.3900984227657318}]}, "Id": "1e597897-8fbd-49d6-b90f-d9fc564b5c4f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06307115405797958, "Height": 0.01136680506169796, "Left": 0.885212242603302, "Top": 0.37971025705337524}, "Polygon": [{"X": 0.8852253556251526, "Y": 0.37971025705337524}, {"X": 0.9482834339141846, "Y": 0.37985602021217346}, {"X": 0.9482735395431519, "Y": 0.39107707142829895}, {"X": 0.885212242603302, "Y": 0.39093196392059326}]}, "Id": "8c2d7ca3-86a1-4daf-8082-3e4e1a98a65a"}, {"BlockType": "WORD", "Confidence": 98.82493591308594, "Text": "524-14659-86,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1136588305234909, "Height": 0.011946788057684898, "Left": 0.10479520261287689, "Top": 0.40216901898384094}, "Polygon": [{"X": 0.10485048592090607, "Y": 0.40216901898384094}, {"X": 0.2184540331363678, "Y": 0.40242916345596313}, {"X": 0.2184048295021057, "Y": 0.4141158163547516}, {"X": 0.10479520261287689, "Y": 0.4138568341732025}]}, "Id": "f3a83355-0e28-4876-8de3-d4856c84827f"}, {"BlockType": "WORD", "Confidence": 92.00115966796875, "Text": "RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020761407911777496, "Height": 0.009812340140342712, "Left": 0.21850335597991943, "Top": 0.4030020534992218}, "Polygon": [{"X": 0.21854446828365326, "Y": 0.4030020534992218}, {"X": 0.23926475644111633, "Y": 0.4030494689941406}, {"X": 0.23922456800937653, "Y": 0.4128143787384033}, {"X": 0.21850335597991943, "Y": 0.41276711225509644}]}, "Id": "18b90c44-94db-44f5-a773-24ac32484e43"}, {"BlockType": "WORD", "Confidence": 96.05488586425781, "Text": "R09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029052339494228363, "Height": 0.009638422168791294, "Left": 0.2437281757593155, "Top": 0.4031352996826172}, "Polygon": [{"X": 0.2437673807144165, "Y": 0.4031352996826172}, {"X": 0.27278050780296326, "Y": 0.403201699256897}, {"X": 0.2727425694465637, "Y": 0.4127737283706665}, {"X": 0.2437281757593155, "Y": 0.41270753741264343}]}, "Id": "36d609ec-eb31-4bb7-a98f-ef31b2ddbe5f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "41828", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046993888914585114, "Height": 0.009862704202532768, "Left": 0.5897408127784729, "Top": 0.4028176963329315}, "Polygon": [{"X": 0.5897653698921204, "Y": 0.4028176963329315}, {"X": 0.636734664440155, "Y": 0.4029252529144287}, {"X": 0.6367122530937195, "Y": 0.41268038749694824}, {"X": 0.5897408127784729, "Y": 0.4125732183456421}]}, "Id": "0874f2fd-d12c-4e97-91d9-d3a834678a80"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008943737484514713, "Height": 0.009359687566757202, "Left": 0.8110321164131165, "Top": 0.4027754068374634}, "Polygon": [{"X": 0.8110461831092834, "Y": 0.4027754068374634}, {"X": 0.8199758529663086, "Y": 0.40279585123062134}, {"X": 0.819962203502655, "Y": 0.4121350944042206}, {"X": 0.8110321164131165, "Y": 0.4121147096157074}]}, "Id": "059ee6e5-0832-4fe6-af9b-2b2a74b681d3"}, {"BlockType": "WORD", "Confidence": 99.59642028808594, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009217207320034504, "Height": 0.009451779536902905, "Left": 0.9613184332847595, "Top": 0.4038422405719757}, "Polygon": [{"X": 0.9613261818885803, "Y": 0.4038422405719757}, {"X": 0.9705356955528259, "Y": 0.4038633406162262}, {"X": 0.9705283641815186, "Y": 0.4132940173149109}, {"X": 0.9613184332847595, "Y": 0.41327300667762756}]}, "Id": "1e28791c-3af5-41e8-ba21-659ae9a78511"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06560664623975754, "Height": 0.01266777515411377, "Left": 0.591485857963562, "Top": 0.6460992693901062}, "Polygon": [{"X": 0.5915172100067139, "Y": 0.6460992693901062}, {"X": 0.6570925116539001, "Y": 0.6462352871894836}, {"X": 0.6570648550987244, "Y": 0.65876704454422}, {"X": 0.591485857963562, "Y": 0.6586316823959351}]}, "Id": "61b538e6-f876-4604-b040-2f542723852c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0478743277490139, "Height": 0.01007128320634365, "Left": 0.7296315431594849, "Top": 0.6469079256057739}, "Polygon": [{"X": 0.7296502590179443, "Y": 0.6469079256057739}, {"X": 0.7775058746337891, "Y": 0.6470072269439697}, {"X": 0.7774893641471863, "Y": 0.6569792032241821}, {"X": 0.7296315431594849, "Y": 0.6568803787231445}]}, "Id": "70f9b208-6288-4ae8-a066-5eb5625683f8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07415719330310822, "Height": 0.012204466387629509, "Left": 0.874270498752594, "Top": 0.6469952464103699}, "Polygon": [{"X": 0.8742851614952087, "Y": 0.6469952464103699}, {"X": 0.948427677154541, "Y": 0.6471490263938904}, {"X": 0.9484171271324158, "Y": 0.6591997146606445}, {"X": 0.874270498752594, "Y": 0.6590466499328613}]}, "Id": "bdc829bd-2c5a-44e0-bf22-63d39b99db09"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07682637870311737, "Height": 0.012818841263651848, "Left": 0.09481597691774368, "Top": 0.6666083335876465}, "Polygon": [{"X": 0.09487637132406235, "Y": 0.6666083335876465}, {"X": 0.17164236307144165, "Y": 0.6667661070823669}, {"X": 0.17158640921115875, "Y": 0.6794271469116211}, {"X": 0.09481597691774368, "Y": 0.6792701482772827}]}, "Id": "ca176a89-86de-4d9a-88b7-de970b3e691e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "visually", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0592908076941967, "Height": 0.01260433904826641, "Left": 0.17637239396572113, "Top": 0.6673005819320679}, "Polygon": [{"X": 0.17642728984355927, "Y": 0.6673005819320679}, {"X": 0.23566319048404694, "Y": 0.6674222946166992}, {"X": 0.2356116771697998, "Y": 0.6799048781394958}, {"X": 0.17637239396572113, "Y": 0.679783821105957}]}, "Id": "66bf67ef-e12f-4bf0-bfbb-3726ecc99e4b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0652301087975502, "Height": 0.010491804219782352, "Left": 0.09310292452573776, "Top": 0.6825749278068542}, "Polygon": [{"X": 0.09315241128206253, "Y": 0.6825749278068542}, {"X": 0.15833303332328796, "Y": 0.6827080249786377}, {"X": 0.15828663110733032, "Y": 0.6930667757987976}, {"X": 0.09310292452573776, "Y": 0.6929342746734619}]}, "Id": "d2298ea8-e612-4c4a-b66a-12eb22ca297c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023500947281718254, "Height": 0.009997658431529999, "Left": 0.1634407341480255, "Top": 0.6830585598945618}, "Polygon": [{"X": 0.1634850800037384, "Y": 0.6830585598945618}, {"X": 0.18694168329238892, "Y": 0.6831064224243164}, {"X": 0.18689841032028198, "Y": 0.6930562257766724}, {"X": 0.1634407341480255, "Y": 0.693008542060852}]}, "Id": "f24ff579-f4f1-40d3-afad-f8cf2531e14b"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06333280354738235, "Height": 0.01261040847748518, "Left": 0.19118574261665344, "Top": 0.6831659078598022}, "Polygon": [{"X": 0.19123978912830353, "Y": 0.6831659078598022}, {"X": 0.2545185387134552, "Y": 0.6832950711250305}, {"X": 0.2544681131839752, "Y": 0.6957762837409973}, {"X": 0.19118574261665344, "Y": 0.6956478357315063}]}, "Id": "5c5986a2-379e-4242-80d9-22a3033ef867"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12093807011842728, "Height": 0.01908222958445549, "Left": 0.4353081285953522, "Top": 0.794779896736145}, "Polygon": [{"X": 0.43536874651908875, "Y": 0.794779896736145}, {"X": 0.5562462210655212, "Y": 0.7950147390365601}, {"X": 0.5561959743499756, "Y": 0.8138620853424072}, {"X": 0.4353081285953522, "Y": 0.8136292099952698}]}, "Id": "88c3e522-ef9c-417e-a87c-fa10fb76a982"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "by:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040596868842840195, "Height": 0.022788910195231438, "Left": 0.5690933465957642, "Top": 0.7951502203941345}, "Polygon": [{"X": 0.5691524744033813, "Y": 0.7951502203941345}, {"X": 0.6096901893615723, "Y": 0.7952289581298828}, {"X": 0.6096352338790894, "Y": 0.8179391026496887}, {"X": 0.5690933465957642, "Y": 0.8178611397743225}]}, "Id": "466bc72c-6c31-4d57-bacd-3fede90b21d3"}, {"BlockType": "WORD", "Confidence": 98.19097137451172, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16197578608989716, "Height": 0.050526831299066544, "Left": 0.6227415800094604, "Top": 0.7672293782234192}, "Polygon": [{"X": 0.6228601336479187, "Y": 0.7672293782234192}, {"X": 0.7847173810005188, "Y": 0.7675478458404541}, {"X": 0.7846358418464661, "Y": 0.817756175994873}, {"X": 0.6227415800094604, "Y": 0.8174448013305664}]}, "Id": "5b87a981-cb40-4c4c-b8b3-3338b9fdf16a"}, {"BlockType": "WORD", "Confidence": 76.1561279296875, "Text": "parker", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1511339545249939, "Height": 0.06136317178606987, "Left": 0.79378741979599, "Top": 0.7877997756004333}, "Polygon": [{"X": 0.7938839793205261, "Y": 0.7877997756004333}, {"X": 0.9449213743209839, "Y": 0.7880942821502686}, {"X": 0.9448668956756592, "Y": 0.8491629362106323}, {"X": 0.79378741979599, "Y": 0.8488765358924866}]}, "Id": "edcfc353-ef71-4217-87f7-7c37560aa82c"}, {"BlockType": "WORD", "Confidence": 21.26624298095703, "Text": "<PERSON><PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0922727882862091, "Height": 0.05186423659324646, "Left": 0.4071168005466461, "Top": 0.851246178150177}, "Polygon": [{"X": 0.4072895646095276, "Y": 0.851246178150177}, {"X": 0.4993895888328552, "Y": 0.851420521736145}, {"X": 0.499238520860672, "Y": 0.9031103849411011}, {"X": 0.4071168005466461, "Y": 0.9029402136802673}]}, "Id": "cfae085b-bc00-43c4-899a-2b68819a0120"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c8b36115-3885-4520-9a7c-655aa3fb9533", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c8b36115-3885-4520-9a7c-655aa3fb9533", "content-type": "application/x-amz-json-1.1", "content-length": "52050", "date": "Wed, 24 Sep 2025 10:52:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "13dc98d2-a61f-4d5e-9e45-bf7000baaecd", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 10:52:41 GMT", "content-type": "application/json", "content-length": "815", "connection": "keep-alive", "x-amzn-requestid": "13dc98d2-a61f-4d5e-9e45-bf7000baaecd"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The page has \"Packing Slip\" and \"Purchase Order\". This seems like a packing list? The header says \"Packing Slip\". The definition for pack_list: \"Packing List, Pack List, Contents, Qty, Item Description\". This appears to be a packing slip, which is similar to packing list. So doc_type likely pack_list. There's no BOL etc. So classify as pack_list.\n\nBut what about invoice? No invoice. So pack_list. It's a single page. So output array with one object: page_no 1, doc_type \"pack_list\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"pack_list\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2626, "outputTokens": 133, "totalTokens": 2759}, "metrics": {"latencyMs": 707}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/e281475f_P6RAGXD27RW4QUXN96TH.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nPacking Slip\nFAB-MASTERS, INC.\n42012\nAddress:\n901 Joliet St\nShip To: RexCon LLC\nJanesville, WI 53548\n2841 Whiting Rd.\nPhone: ************\nBurlington, WI 53105\nFax: ************\nShip Date: 9/19/2025\nSales Person: Zac\nBill To: Astec\nPurchase Order: PUR3165-104676\nShipped Via: CPU\nPart Number\nOur Job#\nOrder Quantity\nShip Quantity\n524-14659-86, RV R09\n41828\n2\n2\nShipping\nDriver\nInspector\nShipment visually\nchecked for damage\nReceived by: <PERSON> parker\nV<PERSON>\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}