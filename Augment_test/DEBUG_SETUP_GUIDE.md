# VS Code Debug Setup Guide for test_classification.py

This guide explains how to set up and use VS Code debugging for `test_classification.py` so that breakpoints work in all files, including external dependencies.

## 🎯 Quick Start

1. **Open VS Code** in the workspace root (`/home/<USER>/Documents/repositories/test_logistically`)
2. **Open the file** `scripts/test_classification.py`
3. **Set breakpoints** by clicking in the left margin (red dots will appear)
4. **Press F5** or go to **Run and Debug (Ctrl+Shift+D)**
5. **Select a debug configuration** from the dropdown
6. **Start debugging** - the code will stop at your breakpoints!

## 🔧 Configuration Details

### Debug Configurations Available

The `.vscode/launch.json` file contains these debug configurations:

1. **Debug test_classification.py (Default Test)**
   - Runs the default test (no command line args)
   - Uses built-in test folder and settings

2. **Debug test_classification.py (Custom Folder)**
   - Processes files from `verified_docs_v1/weight_and_inspection_cert`
   - Limits to 2 files for quick testing
   - Enables debug mode

3. **Debug test_classification.py (Step Into All Code)**
   - Starts with debugger immediately (`stopOnEntry: true`)
   - Enables `--debug-on-start` flag
   - Perfect for stepping through from the beginning

4. **Debug test_classification.py (Single File Quick Test)**
   - Processes only 1 file for fastest testing
   - Outputs to `Augment_test/debug_output`
   - Ideal for quick debugging sessions

5. **Debug External Classification Module**
   - Directly debugs the external classification module
   - Uses the external repository's Python environment

### Key Debug Settings

All configurations include these important settings:

```json
{
    "justMyCode": false,           // Allows stepping into external libraries
    "subProcess": true,            // Debugs subprocesses
    "showReturnValue": true,       // Shows function return values
    "redirectOutput": true,        // Captures all output
    "pathMappings": [              // Maps external code paths
        {
            "localRoot": "${workspaceFolder}",
            "remoteRoot": "${workspaceFolder}"
        },
        {
            "localRoot": "/home/<USER>/Documents/repositories/document-data-extraction",
            "remoteRoot": "/home/<USER>/Documents/repositories/document-data-extraction"
        }
    ]
}
```

## 🐛 Setting Breakpoints in External Files

### In External Repository Files

1. **Open the external file** in VS Code:
   ```
   /home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py
   ```

2. **Set breakpoints** in the external file by clicking in the left margin

3. **Start debugging** `test_classification.py` - it will stop at external breakpoints too!

### In Standard Library Files

1. **Enable "justMyCode": false** (already configured)
2. **Step into** standard library functions using F11
3. **Set breakpoints** in any Python standard library file

### In Third-Party Packages

1. **Navigate to** the package source (e.g., in `venv/lib/python3.x/site-packages/`)
2. **Set breakpoints** in package files
3. **Debug normally** - breakpoints will be hit

## 🚀 Debugging Workflow

### Basic Debugging

1. **Set breakpoints** in `test_classification.py`:
   ```python
   # Line 228: Before classification call
   result = loop.run_until_complete(llm_classification(s3_uri))
   
   # Line 276: In process_single_file
   result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
   
   # Line 453: In parallel processing
   results = await asyncio.gather(*tasks, return_exceptions=True)
   ```

2. **Set breakpoints** in external files:
   ```python
   # In /home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py
   # At the main function entry point
   async def main(s3_uri: str) -> Dict:
   ```

3. **Start debugging** with F5

### Advanced Debugging

1. **Use conditional breakpoints**:
   - Right-click on breakpoint → Add Condition
   - Example: `filename.endswith('.pdf')`

2. **Use logpoints**:
   - Right-click in margin → Add Logpoint
   - Example: `Processing file: {filename}`

3. **Debug on error**:
   - The script automatically enters debugger on errors when `DEBUG_ON_ERROR=true`
   - Use `--debug` flag or set environment variable

## 🔍 Debug Features in test_classification.py

The script includes built-in debugging features:

### Command Line Debug Flags

```bash
# Enable debug mode (post-mortem on errors)
python scripts/test_classification.py folder_path --debug

# Start debugger immediately
python scripts/test_classification.py folder_path --debug-on-start

# Combine both
python scripts/test_classification.py folder_path --debug --debug-on-start
```

### Environment Variable Control

```bash
# Enable debug on error via environment variable
DEBUG_ON_ERROR=true python scripts/test_classification.py folder_path
```

### Automatic Error Debugging

When an error occurs and debug mode is enabled:
1. **Full traceback** is logged
2. **Debug info** is stored
3. **Debugger starts** automatically at the error location
4. **Error context** is preserved for inspection

## 🛠️ Troubleshooting

### Breakpoints Not Hit

1. **Check "justMyCode" setting** - should be `false`
2. **Verify pathMappings** in launch.json
3. **Ensure correct Python interpreter** is selected
4. **Check PYTHONPATH** includes external repository

### External Files Not Found

1. **Verify external repository path**:
   ```
   /home/<USER>/Documents/repositories/document-data-extraction
   ```

2. **Check pathMappings** in launch.json
3. **Open external files** in VS Code workspace

### Debug Console Issues

1. **Use integrated terminal** (`"console": "integratedTerminal"`)
2. **Check redirectOutput** setting
3. **Verify environment variables** are loaded

## 📝 Testing the Setup

Run the debug test script to verify everything works:

```bash
cd /home/<USER>/Documents/repositories/test_logistically
python Augment_test/vscode_debug_test.py
```

This will check:
- VS Code configuration files
- Python environment setup
- Debug script features
- Path mappings

## 🎉 Success Indicators

You'll know debugging is working correctly when:

1. ✅ **Breakpoints turn red** (not gray) when set
2. ✅ **Code stops** at breakpoints in local files
3. ✅ **Code stops** at breakpoints in external files
4. ✅ **Variables panel** shows all local and global variables
5. ✅ **Call stack** shows the complete execution path
6. ✅ **Step Into (F11)** works for external functions
7. ✅ **Debug console** allows variable inspection

## 🔗 Quick Reference

| Action | Shortcut | Description |
|--------|----------|-------------|
| Start Debugging | F5 | Start selected debug configuration |
| Step Over | F10 | Execute current line |
| Step Into | F11 | Step into function calls |
| Step Out | Shift+F11 | Step out of current function |
| Continue | F5 | Continue execution |
| Stop | Shift+F5 | Stop debugging |
| Restart | Ctrl+Shift+F5 | Restart debugging session |

Happy debugging! 🐛✨
