{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9998514652252197, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 0.9998514652252197}, {"X": 0.0, "Y": 0.9997634887695312}]}, "Id": "6136e4b2-0956-4efb-8be1-b13862060b23", "Relationships": [{"Type": "CHILD", "Ids": ["7e0721ab-4067-44b8-9b98-dcf56002a359", "8640347b-81f4-4d54-8d49-b3ba65334f16", "efc5b0a6-3474-43ca-946e-aaa4769bbfd2", "94434c2f-c6ba-4035-b47a-81c7dfda7c5c", "32d1a0fa-177b-4966-9729-683559b68b70", "259f52c8-2cf0-45ca-af4f-64e8bee726b2", "3c8d9097-c7fd-4380-a144-91b7c3be086e", "a39e6b69-2cbf-476a-9e4e-6ff5d9fcf7f2", "babb1ec2-f9ee-4416-9f82-8e3e938dde12", "b9aef48a-1698-4b60-88e4-816af9fe1f5c", "4c5437ed-1ef7-4de2-a3a8-723b9e9d7ba8", "8e128f68-0782-4f4b-9e47-d49757d560db", "64578cec-74c1-4bd7-8fe6-f3fac1586275", "e2db7648-9f26-47fa-9654-7ad2624d6b5d", "c0c51a51-2e16-48dc-a016-2057b8cccba4", "8ec307ab-570c-4271-93b1-6fc73a462279", "704f6c2c-2783-42b7-90b0-91120f17a827", "08b77307-0a0c-4c45-990b-2f10843f4d78", "13cd9cdb-bffa-436f-bf38-3ab2ea06cc13", "7a20fdb0-34c9-49bd-be20-fd8535ecea35", "18d5dd29-eaf5-4b14-b4ac-ef79b9324fd2", "7659eaa4-dc81-4162-895e-bd680aa89d7e", "24e0f4a8-2c49-4be9-ab2e-38c359cd147d", "b222041b-316e-4036-ad1e-adf9dd450038", "49acf2d5-e3f9-41c3-a69d-2b1bdf73eccc", "edfd22e6-d7d8-4b25-b233-b66e92e49540", "4c8258dc-76fa-428c-bb7d-357b001df2fb", "619e6199-1cd7-443a-88f9-05068fd1a302", "8062d79c-461a-4b35-8cfd-e18ac7f2f594", "efbc7531-49b5-4045-8221-18306387ade5", "0473f464-5893-4a9d-9b84-405c000f084e", "b4f4ec21-05d3-40d9-9aae-e24c7cbef2d2", "59656940-625f-462d-bcb2-1e056978439e", "6cbf4903-d4c0-486a-862e-9ada30b25808", "a02fc653-43aa-4109-8821-0461d3f06f5e", "fc4067ed-516b-4290-8fef-c33f9ec77a10", "3d67211c-e8b0-47a4-83ff-00da848bbc12", "e435ebb7-5cf5-4de2-8eb1-61e78ce0777e", "3c45f871-05bb-4015-a240-7c2e0a1ac4b2", "5ac960b4-0b48-45e8-8140-4dc9d84c01d0", "037380c8-4098-46c8-9f49-f0243db74a26", "4420838d-f113-47bd-81b6-e6bc73ce8424", "e2de8db2-1d5d-4660-84f7-98862e243068", "de1bee00-3066-4276-bb44-60cf0b4917de", "8172ec8c-e70a-4e72-8b9f-b7930462138d", "bab0e592-1edc-410c-b930-47d13dc9948d", "2b51f627-9ef4-4c5a-9c94-b2f554d834a7", "f20a5863-0a5b-4bae-a217-a21d5fee4ddc", "e9876790-59e2-4ddb-a2d8-65663fff953c", "d7f9e4f8-cf3d-4e94-8479-8f62e84d07e0", "1d3ef4e9-ac1d-4eb7-abb1-c95ad6b9ebc9", "64f23679-720f-4fea-9493-d03946a64082", "7b312e39-660e-46df-83f5-b803a1e6ad97", "539f8819-f417-47de-8158-8f1263652be0"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Capstone Logistics", "Geometry": {"BoundingBox": {"Width": 0.31937849521636963, "Height": 0.027991890907287598, "Left": 0.06309226155281067, "Top": 0.011519120074808598}, "Polygon": [{"X": 0.06309226155281067, "Y": 0.011519120074808598}, {"X": 0.3824707567691803, "Y": 0.011547164991497993}, {"X": 0.3824702203273773, "Y": 0.03951101005077362}, {"X": 0.06309276074171066, "Y": 0.0394829660654068}]}, "Id": "7e0721ab-4067-44b8-9b98-dcf56002a359", "Relationships": [{"Type": "CHILD", "Ids": ["ed018a7e-de89-41b7-97a9-10229e82d0d9", "93ca4c16-1249-4d5b-8fd1-5b22a135fa41"]}]}, {"BlockType": "LINE", "Confidence": 99.96907806396484, "Text": "30 TECHNOLOGY PKWY SOUTH SUITE 200", "Geometry": {"BoundingBox": {"Width": 0.33041805028915405, "Height": 0.009215794503688812, "Left": 0.05747958645224571, "Top": 0.05732746794819832}, "Polygon": [{"X": 0.05747958645224571, "Y": 0.05732746794819832}, {"X": 0.38789764046669006, "Y": 0.05735648050904274}, {"X": 0.38789746165275574, "Y": 0.06654325872659683}, {"X": 0.05747975781559944, "Y": 0.06651424616575241}]}, "Id": "8640347b-81f4-4d54-8d49-b3ba65334f16", "Relationships": [{"Type": "CHILD", "Ids": ["40e8df56-1e41-4be6-800b-7399bc9a07b6", "90580b58-fb16-4c1f-a66b-3a300cb342bd", "4799f45a-a02e-497a-bb8f-c5807fe01918", "a61e9615-b82f-42bd-8100-0da61b688d61", "e2c0001c-9323-47a5-96b0-2162653136bb", "96511955-2e5c-46fa-9521-60eb3eb924a3"]}]}, {"BlockType": "LINE", "Confidence": 99.89586639404297, "Text": "PEACHTREE CORNERS, GA 30092", "Geometry": {"BoundingBox": {"Width": 0.2574659585952759, "Height": 0.010520954616367817, "Left": 0.09355613589286804, "Top": 0.07110230624675751}, "Polygon": [{"X": 0.09355613589286804, "Y": 0.07110230624675751}, {"X": 0.3510220944881439, "Y": 0.07112491875886917}, {"X": 0.3510219156742096, "Y": 0.08162326365709305}, {"X": 0.09355628490447998, "Y": 0.08160065114498138}]}, "Id": "efc5b0a6-3474-43ca-946e-aaa4769bbfd2", "Relationships": [{"Type": "CHILD", "Ids": ["0ce298c7-d9b8-4d94-a11f-8ba327995b1f", "9c653d3d-6dd5-4e1b-b562-c94de78c2ba3", "9713cd8e-36a0-46c6-ad06-75760e8c6135", "9fa8dcb0-2f8f-407c-a502-4a8b4a68e9aa"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "770-414-1929", "Geometry": {"BoundingBox": {"Width": 0.09458013623952866, "Height": 0.009043813683092594, "Left": 0.17519186437129974, "Top": 0.08475837856531143}, "Polygon": [{"X": 0.17519186437129974, "Y": 0.08475837856531143}, {"X": 0.2697719931602478, "Y": 0.0847666785120964}, {"X": 0.269771933555603, "Y": 0.09380219131708145}, {"X": 0.17519189417362213, "Y": 0.09379388391971588}]}, "Id": "94434c2f-c6ba-4035-b47a-81c7dfda7c5c", "Relationships": [{"Type": "CHILD", "Ids": ["7f2860c5-d981-423f-b569-a1277abaea03"]}]}, {"BlockType": "LINE", "Confidence": 99.609375, "Text": "FED ID# 45-3087555", "Geometry": {"BoundingBox": {"Width": 0.14676444232463837, "Height": 0.009119506925344467, "Left": 0.1490323692560196, "Top": 0.09834431856870651}, "Polygon": [{"X": 0.1490323692560196, "Y": 0.09834431856870651}, {"X": 0.29579681158065796, "Y": 0.09835720807313919}, {"X": 0.2957967221736908, "Y": 0.10746382176876068}, {"X": 0.14903244376182556, "Y": 0.1074509397149086}]}, "Id": "32d1a0fa-177b-4966-9729-683559b68b70", "Relationships": [{"Type": "CHILD", "Ids": ["52609d7d-3ce9-4244-b0ad-25818c1e46da", "59813745-1026-494f-92db-fc42042871a9", "c04fe3c9-57f2-4287-b2c8-74b7d1291c20"]}]}, {"BlockType": "LINE", "Confidence": 99.88037109375, "Text": "09:05:24 September 23, 2025", "Geometry": {"BoundingBox": {"Width": 0.2964562773704529, "Height": 0.017005562782287598, "Left": 0.07393396645784378, "Top": 0.12794309854507446}, "Polygon": [{"X": 0.07393396645784378, "Y": 0.12794309854507446}, {"X": 0.37039023637771606, "Y": 0.1279691457748413}, {"X": 0.3703899383544922, "Y": 0.14494867622852325}, {"X": 0.07393424957990646, "Y": 0.1449226289987564}]}, "Id": "259f52c8-2cf0-45ca-af4f-64e8bee726b2", "Relationships": [{"Type": "CHILD", "Ids": ["bc7c198e-9d40-4b9f-9d18-dd781ba40c7a", "a4686c6f-50ef-44e3-b2ef-7a5b5bb5639c", "0b099298-8092-4204-98fa-0ec72296d21a", "e404fb45-0112-4341-b152-7e3281ebc0de"]}]}, {"BlockType": "LINE", "Confidence": 99.5703125, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.06799831986427307, "Height": 0.011643113568425179, "Left": 0.008144558407366276, "Top": 0.16233031451702118}, "Polygon": [{"X": 0.008144558407366276, "Y": 0.16233031451702118}, {"X": 0.07614269107580185, "Y": 0.1623362898826599}, {"X": 0.07614287734031677, "Y": 0.1739734262228012}, {"X": 0.008144841529428959, "Y": 0.17396745085716248}]}, "Id": "3c8d9097-c7fd-4380-a144-91b7c3be086e", "Relationships": [{"Type": "CHILD", "Ids": ["2e0121c6-ceb6-4622-8f65-1d827c58facb", "6e579934-5526-44c3-b538-dee240804f85"]}]}, {"BlockType": "LINE", "Confidence": 99.81365966796875, "Text": "abdded47-6a4d-", "Geometry": {"BoundingBox": {"Width": 0.10865724086761475, "Height": 0.009154762141406536, "Left": 0.2184745967388153, "Top": 0.16248664259910583}, "Polygon": [{"X": 0.2184745967388153, "Y": 0.16248664259910583}, {"X": 0.32713183760643005, "Y": 0.1624961793422699}, {"X": 0.3271317183971405, "Y": 0.17164140939712524}, {"X": 0.2184745967388153, "Y": 0.1716318577528}]}, "Id": "a39e6b69-2cbf-476a-9e4e-6ff5d9fcf7f2", "Relationships": [{"Type": "CHILD", "Ids": ["c0f43ab3-a7f9-48ee-bd9b-1753f6e87e7f"]}]}, {"BlockType": "LINE", "Confidence": 99.294677734375, "Text": "4102-abec-f09cd3c12da9", "Geometry": {"BoundingBox": {"Width": 0.1703822910785675, "Height": 0.009194445796310902, "Left": 0.21817068755626678, "Top": 0.17590057849884033}, "Polygon": [{"X": 0.21817068755626678, "Y": 0.17590057849884033}, {"X": 0.3885529935359955, "Y": 0.17591553926467896}, {"X": 0.38855278491973877, "Y": 0.18509501218795776}, {"X": 0.21817068755626678, "Y": 0.18508005142211914}]}, "Id": "babb1ec2-f9ee-4416-9f82-8e3e938dde12", "Relationships": [{"Type": "CHILD", "Ids": ["14caf153-3a71-4ed2-b013-4d84d99486d0"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Location:", "Geometry": {"BoundingBox": {"Width": 0.0636940747499466, "Height": 0.009039170108735561, "Left": 0.008177836425602436, "Top": 0.19818806648254395}, "Polygon": [{"X": 0.008177836425602436, "Y": 0.19818806648254395}, {"X": 0.07187175750732422, "Y": 0.19819365441799164}, {"X": 0.07187191396951675, "Y": 0.20722723007202148}, {"X": 0.008178056217730045, "Y": 0.2072216421365738}]}, "Id": "b9aef48a-1698-4b60-88e4-816af9fe1f5c", "Relationships": [{"Type": "CHILD", "Ids": ["432a4d48-9ad2-41ea-8c54-a1866b603555"]}]}, {"BlockType": "LINE", "Confidence": 99.96745300292969, "Text": "US FOODS CINCINNATI", "Geometry": {"BoundingBox": {"Width": 0.1834840029478073, "Height": 0.008916270919144154, "Left": 0.21799910068511963, "Top": 0.1982455551624298}, "Polygon": [{"X": 0.21799910068511963, "Y": 0.1982455551624298}, {"X": 0.40148308873176575, "Y": 0.19826167821884155}, {"X": 0.4014829099178314, "Y": 0.2071618288755417}, {"X": 0.21799910068511963, "Y": 0.20714570581912994}]}, "Id": "4c5437ed-1ef7-4de2-a3a8-723b9e9d7ba8", "Relationships": [{"Type": "CHILD", "Ids": ["b3f23c74-4c6f-41c0-ad00-8dbdaea4dfcb", "0740c253-1ab3-46cf-b622-33750841138d", "e9ddb359-2711-46e6-91de-d7ccbefac24f"]}]}, {"BlockType": "LINE", "Confidence": 98.83131408691406, "Text": "OH 3W", "Geometry": {"BoundingBox": {"Width": 0.052872948348522186, "Height": 0.009001893922686577, "Left": 0.21843016147613525, "Top": 0.2116624116897583}, "Polygon": [{"X": 0.21843016147613525, "Y": 0.2116624116897583}, {"X": 0.27130311727523804, "Y": 0.21166706085205078}, {"X": 0.27130305767059326, "Y": 0.22066430747509003}, {"X": 0.21843016147613525, "Y": 0.22065967321395874}]}, "Id": "8e128f68-0782-4f4b-9e47-d49757d560db", "Relationships": [{"Type": "CHILD", "Ids": ["614fcf0c-4c7d-4a68-8bf6-4df2d9e5a43f", "57bf7d2f-1674-45e9-9fbe-0e0021766a3d"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "Work Date:", "Geometry": {"BoundingBox": {"Width": 0.07788897305727005, "Height": 0.009003616869449615, "Left": 0.008077682927250862, "Top": 0.2338096797466278}, "Polygon": [{"X": 0.008077682927250862, "Y": 0.2338096797466278}, {"X": 0.08596652001142502, "Y": 0.23381651937961578}, {"X": 0.08596666157245636, "Y": 0.24281328916549683}, {"X": 0.008077901788055897, "Y": 0.24280644953250885}]}, "Id": "64578cec-74c1-4bd7-8fe6-f3fac1586275", "Relationships": [{"Type": "CHILD", "Ids": ["5059340b-15d6-4cbe-9d45-252539999c61", "6499a536-7a26-47e4-8909-a44c07a732e4"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "2025-09-23", "Geometry": {"BoundingBox": {"Width": 0.07772710919380188, "Height": 0.009051868692040443, "Left": 0.21829789876937866, "Top": 0.23381932079792023}, "Polygon": [{"X": 0.21829789876937866, "Y": 0.23381932079792023}, {"X": 0.29602500796318054, "Y": 0.233826145529747}, {"X": 0.2960249185562134, "Y": 0.24287119507789612}, {"X": 0.21829789876937866, "Y": 0.24286437034606934}]}, "Id": "e2db7648-9f26-47fa-9654-7ad2624d6b5d", "Relationships": [{"Type": "CHILD", "Ids": ["40ef23bc-4dd5-480d-a7b6-b0662c1d3129"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Bill Code:", "Geometry": {"BoundingBox": {"Width": 0.06840651482343674, "Height": 0.008882906287908554, "Left": 0.008503729477524757, "Top": 0.2514336109161377}, "Polygon": [{"X": 0.008503729477524757, "Y": 0.2514336109161377}, {"X": 0.076910100877285, "Y": 0.25143963098526}, {"X": 0.07691024243831635, "Y": 0.26031652092933655}, {"X": 0.008503945544362068, "Y": 0.2603105306625366}]}, "Id": "c0c51a51-2e16-48dc-a016-2057b8cccba4", "Relationships": [{"Type": "CHILD", "Ids": ["2ac9f60f-d48f-49b9-b630-fdcd172a79d5", "da5a510f-f90f-4d75-8665-3ec2fb50e2fe"]}]}, {"BlockType": "LINE", "Confidence": 99.47206115722656, "Text": "RCOD30315", "Geometry": {"BoundingBox": {"Width": 0.0883929431438446, "Height": 0.008946668356657028, "Left": 0.2182169109582901, "Top": 0.25157976150512695}, "Polygon": [{"X": 0.2182169258594513, "Y": 0.25157976150512695}, {"X": 0.3066098690032959, "Y": 0.25158753991127014}, {"X": 0.30660974979400635, "Y": 0.2605264186859131}, {"X": 0.2182169109582901, "Y": 0.2605186700820923}]}, "Id": "8ec307ab-570c-4271-93b1-6fc73a462279", "Relationships": [{"Type": "CHILD", "Ids": ["77b51f0c-9548-4518-a2dd-da238c5847f0"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.051022496074438095, "Height": 0.00882819015532732, "Left": 0.008738881908357143, "Top": 0.2694484293460846}, "Polygon": [{"X": 0.008738881908357143, "Y": 0.2694484293460846}, {"X": 0.05976121872663498, "Y": 0.26945292949676514}, {"X": 0.05976137891411781, "Y": 0.27827662229537964}, {"X": 0.008739096112549305, "Y": 0.2782721519470215}]}, "Id": "704f6c2c-2783-42b7-90b0-91120f17a827", "Relationships": [{"Type": "CHILD", "Ids": ["9cb3f979-b910-4ba0-9871-94601acf85d6"]}]}, {"BlockType": "LINE", "Confidence": 99.96419525146484, "Text": "CARRIER DOES NOT", "Geometry": {"BoundingBox": {"Width": 0.15931545197963715, "Height": 0.00876295194029808, "Left": 0.2185031622648239, "Top": 0.2695634365081787}, "Polygon": [{"X": 0.2185031622648239, "Y": 0.2695634365081787}, {"X": 0.37781861424446106, "Y": 0.26957741379737854}, {"X": 0.37781843543052673, "Y": 0.2783263921737671}, {"X": 0.2185031622648239, "Y": 0.2783123850822449}]}, "Id": "08b77307-0a0c-4c45-990b-2f10843f4d78", "Relationships": [{"Type": "CHILD", "Ids": ["6f07339a-4bb5-4ada-b96a-390188d9af64", "a2e14ece-70e3-425e-9f97-3d1465d1a646", "e65a023d-a30d-4090-ba99-691c4cd32dab"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock:", "Geometry": {"BoundingBox": {"Width": 0.04023122414946556, "Height": 0.009022913873195648, "Left": 0.008435364812612534, "Top": 0.2870439291000366}, "Polygon": [{"X": 0.008435364812612534, "Y": 0.2870439291000366}, {"X": 0.04866641387343407, "Y": 0.28704744577407837}, {"X": 0.048666588962078094, "Y": 0.2960668206214905}, {"X": 0.008435584604740143, "Y": 0.29606330394744873}]}, "Id": "13cd9cdb-bffa-436f-bf38-3ab2ea06cc13", "Relationships": [{"Type": "CHILD", "Ids": ["3cd8fc0e-a362-4efa-a23c-ebb8819ede44"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRY", "Geometry": {"BoundingBox": {"Width": 0.03517632186412811, "Height": 0.008905908092856407, "Left": 0.21844042837619781, "Top": 0.2871910333633423}, "Polygon": [{"X": 0.218440443277359, "Y": 0.2871910333633423}, {"X": 0.2536167502403259, "Y": 0.2871941328048706}, {"X": 0.25361672043800354, "Y": 0.29609695076942444}, {"X": 0.21844042837619781, "Y": 0.2960938513278961}]}, "Id": "7a20fdb0-34c9-49bd-be20-fd8535ecea35", "Relationships": [{"Type": "CHILD", "Ids": ["54356673-1ffa-4ca9-8c71-287cc44c19e5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.03814519941806793, "Height": 0.008638055063784122, "Left": 0.00849840883165598, "Top": 0.3053167164325714}, "Polygon": [{"X": 0.00849840883165598, "Y": 0.3053167164325714}, {"X": 0.046643439680337906, "Y": 0.3053200840950012}, {"X": 0.04664360731840134, "Y": 0.31395477056503296}, {"X": 0.008498618379235268, "Y": 0.31395143270492554}]}, "Id": "18d5dd29-eaf5-4b14-b4ac-ef79b9324fd2", "Relationships": [{"Type": "CHILD", "Ids": ["fd49e368-8c3c-481c-9654-caea11e73023"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "29", "Geometry": {"BoundingBox": {"Width": 0.016295304521918297, "Height": 0.008822520263493061, "Left": 0.21841122210025787, "Top": 0.305290549993515}, "Polygon": [{"X": 0.21841123700141907, "Y": 0.305290549993515}, {"X": 0.23470653593540192, "Y": 0.3052919805049896}, {"X": 0.23470650613307953, "Y": 0.3141130805015564}, {"X": 0.21841122210025787, "Y": 0.3141116499900818}]}, "Id": "7659eaa4-dc81-4162-895e-bd680aa89d7e", "Relationships": [{"Type": "CHILD", "Ids": ["4cb999a8-33a1-426a-b1db-d580903b74c8"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Purchase Orders", "Geometry": {"BoundingBox": {"Width": 0.11049387603998184, "Height": 0.009032007306814194, "Left": 0.008284796960651875, "Top": 0.3297984302043915}, "Polygon": [{"X": 0.008284796960651875, "Y": 0.3297984302043915}, {"X": 0.11877857148647308, "Y": 0.3298081159591675}, {"X": 0.11877866834402084, "Y": 0.33883044123649597}, {"X": 0.008285016752779484, "Y": 0.3388207256793976}]}, "Id": "24e0f4a8-2c49-4be9-ab2e-38c359cd147d", "Relationships": [{"Type": "CHILD", "Ids": ["9af848f2-485f-4c9f-ac1f-7e5edd852f3d", "a8259de6-1b51-442a-9f69-b748c29735c8"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.05086766183376312, "Height": 0.008900132030248642, "Left": 0.21836696565151215, "Top": 0.32995927333831787}, "Polygon": [{"X": 0.21836698055267334, "Y": 0.32995927333831787}, {"X": 0.26923462748527527, "Y": 0.329963743686676}, {"X": 0.2692345678806305, "Y": 0.3388594090938568}, {"X": 0.21836696565151215, "Y": 0.33885493874549866}]}, "Id": "b222041b-316e-4036-ad1e-adf9dd450038", "Relationships": [{"Type": "CHILD", "Ids": ["3a2ca354-e852-4d4d-96e3-5f68ef798e1e"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "7163773W", "Geometry": {"BoundingBox": {"Width": 0.07479508221149445, "Height": 0.009473801590502262, "Left": 0.008294785395264626, "Top": 0.34770074486732483}, "Polygon": [{"X": 0.008294785395264626, "Y": 0.34770074486732483}, {"X": 0.08308972418308258, "Y": 0.3477073013782501}, {"X": 0.08308986574411392, "Y": 0.3571745455265045}, {"X": 0.008295015431940556, "Y": 0.35716795921325684}]}, "Id": "49acf2d5-e3f9-41c3-a69d-2b1bdf73eccc", "Relationships": [{"Type": "CHILD", "Ids": ["40799fbf-0ab3-47d5-bf82-a39369800fd0"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "TEAM", "Geometry": {"BoundingBox": {"Width": 0.04763469845056534, "Height": 0.009195343591272831, "Left": 0.21811336278915405, "Top": 0.34773558378219604}, "Polygon": [{"X": 0.21811336278915405, "Y": 0.34773558378219604}, {"X": 0.2657480537891388, "Y": 0.3477397561073303}, {"X": 0.265747994184494, "Y": 0.3569309115409851}, {"X": 0.21811336278915405, "Y": 0.35692673921585083}]}, "Id": "edfd22e6-d7d8-4b25-b233-b66e92e49540", "Relationships": [{"Type": "CHILD", "Ids": ["ac66231d-1256-4dea-a825-022fdf2a6605"]}]}, {"BlockType": "LINE", "Confidence": 99.5338363647461, "Text": "THREE-REDISTRIBUTION", "Geometry": {"BoundingBox": {"Width": 0.19881923496723175, "Height": 0.008900116197764874, "Left": 0.21797184646129608, "Top": 0.36122196912765503}, "Polygon": [{"X": 0.21797186136245728, "Y": 0.36122196912765503}, {"X": 0.41679108142852783, "Y": 0.3612394332885742}, {"X": 0.4167908728122711, "Y": 0.37012210488319397}, {"X": 0.21797184646129608, "Y": 0.3701046407222748}]}, "Id": "4c8258dc-76fa-428c-bb7d-357b001df2fb", "Relationships": [{"Type": "CHILD", "Ids": ["3a754fb0-3cb8-464e-8985-23dba3f43230"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "Total Finished Pallets:", "Geometry": {"BoundingBox": {"Width": 0.15023799240589142, "Height": 0.008861630223691463, "Left": 0.008324678055942059, "Top": 0.39029932022094727}, "Polygon": [{"X": 0.008324678055942059, "Y": 0.39029932022094727}, {"X": 0.15856261551380157, "Y": 0.390312522649765}, {"X": 0.15856267511844635, "Y": 0.3991609513759613}, {"X": 0.008324893191456795, "Y": 0.39914774894714355}]}, "Id": "619e6199-1cd7-443a-88f9-05068fd1a302", "Relationships": [{"Type": "CHILD", "Ids": ["e5b3c197-f403-40be-92bf-ae2bea003168", "d7f1c97f-fafe-4021-a892-82f2556e7ec7", "660f5aaf-d3a3-482c-a9f4-210766079e95"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "100", "Geometry": {"BoundingBox": {"Width": 0.023614907637238503, "Height": 0.008653867058455944, "Left": 0.21992357075214386, "Top": 0.39045608043670654}, "Polygon": [{"X": 0.21992357075214386, "Y": 0.39045608043670654}, {"X": 0.24353846907615662, "Y": 0.3904581665992737}, {"X": 0.24353843927383423, "Y": 0.39910995960235596}, {"X": 0.21992357075214386, "Y": 0.3991078734397888}]}, "Id": "8062d79c-461a-4b35-8cfd-e18ac7f2f594", "Relationships": [{"Type": "CHILD", "Ids": ["a8806d57-7a7a-4803-a721-96fc38960454"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "Total Case Count:", "Geometry": {"BoundingBox": {"Width": 0.12125390768051147, "Height": 0.009163206443190575, "Left": 0.008370183408260345, "Top": 0.4081292748451233}, "Polygon": [{"X": 0.008370183408260345, "Y": 0.4081292748451233}, {"X": 0.12962399423122406, "Y": 0.4081399142742157}, {"X": 0.12962409853935242, "Y": 0.4172924757003784}, {"X": 0.008370405994355679, "Y": 0.4172818064689636}]}, "Id": "efbc7531-49b5-4045-8221-18306387ade5", "Relationships": [{"Type": "CHILD", "Ids": ["bad4013d-299a-4601-92bc-2862113089dc", "ceb256fd-d387-441c-98d6-66225c10e852", "ce92891c-08d7-4084-8b77-697ff8229416"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "2205", "Geometry": {"BoundingBox": {"Width": 0.0334165133535862, "Height": 0.008977863006293774, "Left": 0.2182658314704895, "Top": 0.4084344804286957}, "Polygon": [{"X": 0.2182658463716507, "Y": 0.4084344804286957}, {"X": 0.2516823410987854, "Y": 0.4084374010562897}, {"X": 0.251682311296463, "Y": 0.41741234064102173}, {"X": 0.2182658314704895, "Y": 0.41740942001342773}]}, "Id": "0473f464-5893-4a9d-9b84-405c000f084e", "Relationships": [{"Type": "CHILD", "Ids": ["a490a81d-120d-4f4d-8b2b-12d888612f4c"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Total Weight:", "Geometry": {"BoundingBox": {"Width": 0.09259630739688873, "Height": 0.011444908566772938, "Left": 0.008394207805395126, "Top": 0.4258168041706085}, "Polygon": [{"X": 0.008394207805395126, "Y": 0.4258168041706085}, {"X": 0.10099036246538162, "Y": 0.42582494020462036}, {"X": 0.10099051147699356, "Y": 0.437261700630188}, {"X": 0.008394485339522362, "Y": 0.43725356459617615}]}, "Id": "b4f4ec21-05d3-40d9-9aae-e24c7cbef2d2", "Relationships": [{"Type": "CHILD", "Ids": ["6be9f59a-8867-485c-9492-7cd776c45f71", "96fb0a77-f822-4c86-ab7a-f30a003e11f4"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "28205.00", "Geometry": {"BoundingBox": {"Width": 0.06289466470479965, "Height": 0.008721854537725449, "Left": 0.21847432851791382, "Top": 0.4260772168636322}, "Polygon": [{"X": 0.21847432851791382, "Y": 0.4260772168636322}, {"X": 0.28136900067329407, "Y": 0.4260827302932739}, {"X": 0.2813689112663269, "Y": 0.43479904532432556}, {"X": 0.21847432851791382, "Y": 0.43479353189468384}]}, "Id": "59656940-625f-462d-bcb2-1e056978439e", "Relationships": [{"Type": "CHILD", "Ids": ["3b44c7a8-d192-4df0-884a-17988ace349d"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "Trailer Number:", "Geometry": {"BoundingBox": {"Width": 0.10896769165992737, "Height": 0.008999854326248169, "Left": 0.008273606188595295, "Top": 0.443850040435791}, "Polygon": [{"X": 0.008273606188595295, "Y": 0.443850040435791}, {"X": 0.11724119633436203, "Y": 0.44385963678359985}, {"X": 0.11724130064249039, "Y": 0.4528498947620392}, {"X": 0.00827382504940033, "Y": 0.45284032821655273}]}, "Id": "6cbf4903-d4c0-486a-862e-9ada30b25808", "Relationships": [{"Type": "CHILD", "Ids": ["69331bad-e95a-47c4-9737-3dcfc0baab4f", "fbdc2a0c-0a2c-4378-8c15-498e71988c85"]}]}, {"BlockType": "LINE", "Confidence": 99.37281036376953, "Text": "0", "Geometry": {"BoundingBox": {"Width": 0.0082298768684268, "Height": 0.00876916665583849, "Left": 0.21841850876808167, "Top": 0.44403672218322754}, "Polygon": [{"X": 0.21841850876808167, "Y": 0.44403672218322754}, {"X": 0.22664839029312134, "Y": 0.44403746724128723}, {"X": 0.22664837539196014, "Y": 0.45280590653419495}, {"X": 0.21841850876808167, "Y": 0.45280516147613525}]}, "Id": "a02fc653-43aa-4109-8821-0461d3f06f5e", "Relationships": [{"Type": "CHILD", "Ids": ["958c663b-22b2-45ba-97d2-2a517c1fb5aa"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Tractor Number:", "Geometry": {"BoundingBox": {"Width": 0.11247771233320236, "Height": 0.009101415984332561, "Left": 0.00833654124289751, "Top": 0.4614793360233307}, "Polygon": [{"X": 0.00833654124289751, "Y": 0.4614793360233307}, {"X": 0.12081415206193924, "Y": 0.4614892303943634}, {"X": 0.1208142563700676, "Y": 0.4705807566642761}, {"X": 0.008336762897670269, "Y": 0.4705708622932434}]}, "Id": "fc4067ed-516b-4290-8fef-c33f9ec77a10", "Relationships": [{"Type": "CHILD", "Ids": ["7404e377-caba-41c5-8b6e-86e767018b6b", "a478f1fd-121f-4a8f-a739-35842c68cedb"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "BOL:", "Geometry": {"BoundingBox": {"Width": 0.037187885493040085, "Height": 0.008783808909356594, "Left": 0.008399550803005695, "Top": 0.47972339391708374}, "Polygon": [{"X": 0.008399550803005695, "Y": 0.47972339391708374}, {"X": 0.04558726027607918, "Y": 0.479726642370224}, {"X": 0.045587435364723206, "Y": 0.4885071814060211}, {"X": 0.008399764075875282, "Y": 0.48850393295288086}]}, "Id": "3d67211c-e8b0-47a4-83ff-00da848bbc12", "Relationships": [{"Type": "CHILD", "Ids": ["75f4218d-b196-44fd-93a7-d39fa7032d71"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Comments:", "Geometry": {"BoundingBox": {"Width": 0.07617875933647156, "Height": 0.008975650183856487, "Left": 0.008568806573748589, "Top": 0.4973137676715851}, "Polygon": [{"X": 0.008568806573748589, "Y": 0.4973137676715851}, {"X": 0.08474742621183395, "Y": 0.4973204731941223}, {"X": 0.0847475603222847, "Y": 0.5062894225120544}, {"X": 0.008569024503231049, "Y": 0.5062827467918396}]}, "Id": "e435ebb7-5cf5-4de2-8eb1-61e78ce0777e", "Relationships": [{"Type": "CHILD", "Ids": ["7576cb2f-9358-4327-8757-bde6700b8b95"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Canned Comments:", "Geometry": {"BoundingBox": {"Width": 0.1316455453634262, "Height": 0.008715253323316574, "Left": 0.008641708642244339, "Top": 0.5153520107269287}, "Polygon": [{"X": 0.008641708642244339, "Y": 0.5153520107269287}, {"X": 0.14028719067573547, "Y": 0.5153635740280151}, {"X": 0.14028726518154144, "Y": 0.5240672826766968}, {"X": 0.008641920052468777, "Y": 0.5240557193756104}]}, "Id": "3c45f871-05bb-4015-a240-7c2e0a1ac4b2", "Relationships": [{"Type": "CHILD", "Ids": ["f1f68f58-231f-4328-87d2-1f0749dcec47", "d4c9b1be-5577-4e82-b46e-47ead8ef9004"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Unloaders:", "Geometry": {"BoundingBox": {"Width": 0.07250294834375381, "Height": 0.008964789099991322, "Left": 0.00836679246276617, "Top": 0.5327576398849487}, "Polygon": [{"X": 0.00836679246276617, "Y": 0.5327576398849487}, {"X": 0.08086960017681122, "Y": 0.5327640175819397}, {"X": 0.08086974173784256, "Y": 0.5417224168777466}, {"X": 0.00836701039224863, "Y": 0.5417160987854004}]}, "Id": "5ac960b4-0b48-45e8-8140-4dc9d84c01d0", "Relationships": [{"Type": "CHILD", "Ids": ["0a79759c-d9e7-47d0-839d-a311d454b2e8"]}]}, {"BlockType": "LINE", "Confidence": 98.97560119628906, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004625690635293722, "Height": 0.008851837366819382, "Left": 0.2201656699180603, "Top": 0.5329271554946899}, "Polygon": [{"X": 0.2201656848192215, "Y": 0.5329271554946899}, {"X": 0.22479136288166046, "Y": 0.5329275131225586}, {"X": 0.22479136288166046, "Y": 0.5417789816856384}, {"X": 0.2201656699180603, "Y": 0.541778564453125}]}, "Id": "037380c8-4098-46c8-9f49-f0243db74a26", "Relationships": [{"Type": "CHILD", "Ids": ["d64792b1-8122-4da0-9113-02d6a52e177a"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Base Charge:", "Geometry": {"BoundingBox": {"Width": 0.08902029693126678, "Height": 0.011187843047082424, "Left": 0.008465991355478764, "Top": 0.5578882694244385}, "Polygon": [{"X": 0.008465991355478764, "Y": 0.5578882694244385}, {"X": 0.09748613834381104, "Y": 0.5578961372375488}, {"X": 0.09748628735542297, "Y": 0.5690761208534241}, {"X": 0.008466262370347977, "Y": 0.5690683126449585}]}, "Id": "4420838d-f113-47bd-81b6-e6bc73ce8424", "Relationships": [{"Type": "CHILD", "Ids": ["69708e93-95a4-4955-8890-8ba6ffe78410", "66c14e35-d2e9-4547-b0f6-9dc95f9c227b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "500.00", "Geometry": {"BoundingBox": {"Width": 0.045998167246580124, "Height": 0.009090442210435867, "Left": 0.2187076359987259, "Top": 0.5579280853271484}, "Polygon": [{"X": 0.21870765089988708, "Y": 0.5579280853271484}, {"X": 0.2647058069705963, "Y": 0.5579321384429932}, {"X": 0.26470574736595154, "Y": 0.5670185089111328}, {"X": 0.2187076359987259, "Y": 0.5670145153999329}]}, "Id": "e2de8db2-1d5d-4660-84f7-98862e243068", "Relationships": [{"Type": "CHILD", "Ids": ["380a4348-4fc2-404f-ba63-05ad045dfe96"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Convenience Fee:", "Geometry": {"BoundingBox": {"Width": 0.11973482370376587, "Height": 0.0088790412992239, "Left": 0.008586077019572258, "Top": 0.5756323337554932}, "Polygon": [{"X": 0.008586077019572258, "Y": 0.5756323337554932}, {"X": 0.1283208131790161, "Y": 0.5756428837776184}, {"X": 0.12832090258598328, "Y": 0.584511399269104}, {"X": 0.008586292155086994, "Y": 0.5845009088516235}]}, "Id": "de1bee00-3066-4276-bb44-60cf0b4917de", "Relationships": [{"Type": "CHILD", "Ids": ["81f3edcd-99a5-469f-968e-09ed9dedd68b", "41abe567-a251-48dd-af9d-9a748c2ed3eb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "10.00", "Geometry": {"BoundingBox": {"Width": 0.03615653142333031, "Height": 0.008849347941577435, "Left": 0.21982061862945557, "Top": 0.5757320523262024}, "Polygon": [{"X": 0.21982063353061676, "Y": 0.5757320523262024}, {"X": 0.25597715377807617, "Y": 0.5757352113723755}, {"X": 0.2559771239757538, "Y": 0.5845813751220703}, {"X": 0.21982061862945557, "Y": 0.5845782160758972}]}, "Id": "8172ec8c-e70a-4e72-8b9f-b7930462138d", "Relationships": [{"Type": "CHILD", "Ids": ["edd92db1-bae7-44da-9b96-16c802a0154b"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Total Cost:", "Geometry": {"BoundingBox": {"Width": 0.07426508516073227, "Height": 0.009049003943800926, "Left": 0.008350272662937641, "Top": 0.5934895277023315}, "Polygon": [{"X": 0.008350272662937641, "Y": 0.5934895277023315}, {"X": 0.08261521905660629, "Y": 0.5934960246086121}, {"X": 0.08261536061763763, "Y": 0.602538526058197}, {"X": 0.008350493386387825, "Y": 0.6025319695472717}]}, "Id": "bab0e592-1edc-410c-b930-47d13dc9948d", "Relationships": [{"Type": "CHILD", "Ids": ["9a2e239d-6570-4754-b8ba-5537040f001b", "add9c73f-9dfe-4d35-b105-6fb66f8d6184"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "510.00", "Geometry": {"BoundingBox": {"Width": 0.04594118893146515, "Height": 0.009339669719338417, "Left": 0.21869735419750214, "Top": 0.5934710502624512}, "Polygon": [{"X": 0.21869735419750214, "Y": 0.5934710502624512}, {"X": 0.2646385431289673, "Y": 0.5934751033782959}, {"X": 0.2646384835243225, "Y": 0.6028107404708862}, {"X": 0.21869735419750214, "Y": 0.6028066873550415}]}, "Id": "2b51f627-9ef4-4c5a-9c94-b2f554d834a7", "Relationships": [{"Type": "CHILD", "Ids": ["f416c611-a877-4390-9a75-2f25142e6ac1"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payments:", "Geometry": {"BoundingBox": {"Width": 0.06896629184484482, "Height": 0.011080441065132618, "Left": 0.008281613700091839, "Top": 0.6185397505760193}, "Polygon": [{"X": 0.008281613700091839, "Y": 0.6185397505760193}, {"X": 0.0772477313876152, "Y": 0.6185457706451416}, {"X": 0.07724791020154953, "Y": 0.6296201944351196}, {"X": 0.008281882852315903, "Y": 0.6296141147613525}]}, "Id": "f20a5863-0a5b-4bae-a217-a21d5fee4ddc", "Relationships": [{"Type": "CHILD", "Ids": ["e9294fab-270d-4a2f-a242-f4b4abfbe32e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.05589422583580017, "Height": 0.0086582712829113, "Left": 0.21814681589603424, "Top": 0.6186829209327698}, "Polygon": [{"X": 0.21814683079719543, "Y": 0.6186829209327698}, {"X": 0.2740410566329956, "Y": 0.6186878681182861}, {"X": 0.27404099702835083, "Y": 0.6273412108421326}, {"X": 0.21814681589603424, "Y": 0.627336323261261}]}, "Id": "e9876790-59e2-4ddb-a2d8-65663fff953c", "Relationships": [{"Type": "CHILD", "Ids": ["993fd254-27d9-4653-97c7-29b8baab8769"]}]}, {"BlockType": "LINE", "Confidence": 99.13365173339844, "Text": "CapstonePay-", "Geometry": {"BoundingBox": {"Width": 0.09230265766382217, "Height": 0.010882600210607052, "Left": 0.042539387941360474, "Top": 0.6365899443626404}, "Polygon": [{"X": 0.042539387941360474, "Y": 0.6365899443626404}, {"X": 0.1348419487476349, "Y": 0.6365980505943298}, {"X": 0.13484203815460205, "Y": 0.6474725604057312}, {"X": 0.04253961145877838, "Y": 0.6474644541740417}]}, "Id": "d7f9e4f8-cf3d-4e94-8479-8f62e84d07e0", "Relationships": [{"Type": "CHILD", "Ids": ["ce132509-f195-42d9-a566-55e55143b8ae"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$510.00", "Geometry": {"BoundingBox": {"Width": 0.05417800322175026, "Height": 0.009784932248294353, "Left": 0.2186148315668106, "Top": 0.635902464389801}, "Polygon": [{"X": 0.2186148315668106, "Y": 0.635902464389801}, {"X": 0.27279284596443176, "Y": 0.6359072327613831}, {"X": 0.2727927565574646, "Y": 0.6456874012947083}, {"X": 0.2186148315668106, "Y": 0.6456826329231262}]}, "Id": "1d3ef4e9-ac1d-4eb7-abb1-c95ad6b9ebc9", "Relationships": [{"Type": "CHILD", "Ids": ["97d2a602-9072-485a-991d-1e120bc5d140"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "38054139", "Geometry": {"BoundingBox": {"Width": 0.06640410423278809, "Height": 0.009031868539750576, "Left": 0.04249372333288193, "Top": 0.6500594615936279}, "Polygon": [{"X": 0.04249372333288193, "Y": 0.6500594615936279}, {"X": 0.10889771580696106, "Y": 0.6500653028488159}, {"X": 0.10889782756567001, "Y": 0.6590913534164429}, {"X": 0.04249390587210655, "Y": 0.6590855121612549}]}, "Id": "64f23679-720f-4fea-9493-d03946a64082", "Relationships": [{"Type": "CHILD", "Ids": ["44678263-ab62-4eae-bedf-8da8764dda96"]}]}, {"BlockType": "LINE", "Confidence": 99.9109115600586, "Text": "Total Payments", "Geometry": {"BoundingBox": {"Width": 0.10481719672679901, "Height": 0.011563497595489025, "Left": 0.042015042155981064, "Top": 0.6717744469642639}, "Polygon": [{"X": 0.042015042155981064, "Y": 0.6717744469642639}, {"X": 0.1468321532011032, "Y": 0.6717836856842041}, {"X": 0.14683224260807037, "Y": 0.683337926864624}, {"X": 0.04201527684926987, "Y": 0.6833287477493286}]}, "Id": "7b312e39-660e-46df-83f5-b803a1e6ad97", "Relationships": [{"Type": "CHILD", "Ids": ["8285bf77-350e-4dfd-81fa-6c674d5bfb37", "a29818dc-abfc-4b58-ba6c-2e849165e63d"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$510.00", "Geometry": {"BoundingBox": {"Width": 0.05435352772474289, "Height": 0.010139241814613342, "Left": 0.2184671312570572, "Top": 0.6713500618934631}, "Polygon": [{"X": 0.21846714615821838, "Y": 0.6713500618934631}, {"X": 0.2728206515312195, "Y": 0.6713548302650452}, {"X": 0.2728205919265747, "Y": 0.6814892888069153}, {"X": 0.2184671312570572, "Y": 0.6814845204353333}]}, "Id": "539f8819-f417-47de-8158-8f1263652be0", "Relationships": [{"Type": "CHILD", "Ids": ["91c205db-07ec-4311-a3f6-b39936afe3d0"]}]}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Capstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1551251858472824, "Height": 0.027581969276070595, "Left": 0.06309226900339127, "Top": 0.011914619244635105}, "Polygon": [{"X": 0.06309226900339127, "Y": 0.011914619244635105}, {"X": 0.21821746230125427, "Y": 0.011928240768611431}, {"X": 0.21821744740009308, "Y": 0.039496589452028275}, {"X": 0.06309276074171066, "Y": 0.0394829660654068}]}, "Id": "ed018a7e-de89-41b7-97a9-10229e82d0d9"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15388168394565582, "Height": 0.027913054451346397, "Left": 0.22858908772468567, "Top": 0.011533652432262897}, "Polygon": [{"X": 0.22858913242816925, "Y": 0.011533652432262897}, {"X": 0.3824707567691803, "Y": 0.011547164991497993}, {"X": 0.3824702203273773, "Y": 0.03944670781493187}, {"X": 0.22858908772468567, "Y": 0.03943319246172905}]}, "Id": "93ca4c16-1249-4d5b-8fd1-5b22a135fa41"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016235334798693657, "Height": 0.009175027720630169, "Left": 0.05747958645224571, "Top": 0.05734064429998398}, "Polygon": [{"X": 0.05747958645224571, "Y": 0.05734064429998398}, {"X": 0.07371477037668228, "Y": 0.05734206736087799}, {"X": 0.07371491938829422, "Y": 0.06651566922664642}, {"X": 0.05747975781559944, "Y": 0.06651424616575241}]}, "Id": "40e8df56-1e41-4be6-800b-7399bc9a07b6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TECHNOLOGY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1145934984087944, "Height": 0.008914460428059101, "Left": 0.0780453160405159, "Top": 0.05743260309100151}, "Polygon": [{"X": 0.0780453160405159, "Y": 0.05743260309100151}, {"X": 0.19263878464698792, "Y": 0.057442665100097656}, {"X": 0.1926388144493103, "Y": 0.06634706258773804}, {"X": 0.07804545760154724, "Y": 0.06633699685335159}]}, "Id": "90580b58-fb16-4c1f-a66b-3a300cb342bd"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PKWY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049338776618242264, "Height": 0.009018132463097572, "Left": 0.19693146646022797, "Top": 0.05736788362264633}, "Polygon": [{"X": 0.19693146646022797, "Y": 0.05736788362264633}, {"X": 0.24627025425434113, "Y": 0.05737221613526344}, {"X": 0.24627020955085754, "Y": 0.06638601422309875}, {"X": 0.19693149626255035, "Y": 0.06638168543577194}]}, "Id": "4799f45a-a02e-497a-bb8f-c5807fe01918"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SOUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05558796599507332, "Height": 0.009103185497224331, "Left": 0.25106310844421387, "Top": 0.05738111957907677}, "Polygon": [{"X": 0.25106316804885864, "Y": 0.05738111957907677}, {"X": 0.3066510856151581, "Y": 0.05738600343465805}, {"X": 0.3066509962081909, "Y": 0.06648430973291397}, {"X": 0.25106310844421387, "Y": 0.06647942215204239}]}, "Id": "a61e9615-b82f-42bd-8100-0da61b688d61"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "SUITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04716572165489197, "Height": 0.009018135257065296, "Left": 0.31135451793670654, "Top": 0.05740287899971008}, "Polygon": [{"X": 0.3113546073436737, "Y": 0.05740287899971008}, {"X": 0.3585202395915985, "Y": 0.05740702152252197}, {"X": 0.3585200905799866, "Y": 0.0664210170507431}, {"X": 0.31135451793670654, "Y": 0.06641687452793121}]}, "Id": "e2c0001c-9323-47a5-96b0-2162653136bb"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024977300316095352, "Height": 0.0089932382106781, "Left": 0.362920343875885, "Top": 0.057354290038347244}, "Polygon": [{"X": 0.36292052268981934, "Y": 0.057354290038347244}, {"X": 0.38789764046669006, "Y": 0.05735648050904274}, {"X": 0.38789746165275574, "Y": 0.06634752452373505}, {"X": 0.362920343875885, "Y": 0.06634533405303955}]}, "Id": "96511955-2e5c-46fa-9521-60eb3eb924a3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PEACHTREE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09717186540365219, "Height": 0.00875189807265997, "Left": 0.09355613589286804, "Top": 0.07120215892791748}, "Polygon": [{"X": 0.09355613589286804, "Y": 0.07120215892791748}, {"X": 0.19072797894477844, "Y": 0.07121068984270096}, {"X": 0.19072799384593964, "Y": 0.07995405793190002}, {"X": 0.09355626255273819, "Y": 0.07994551956653595}]}, "Id": "0ce298c7-d9b8-4d94-a11f-8ba327995b1f"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "CORNERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08078249543905258, "Height": 0.01044042594730854, "Left": 0.19546596705913544, "Top": 0.07117626816034317}, "Polygon": [{"X": 0.19546596705913544, "Y": 0.07117626816034317}, {"X": 0.2762484550476074, "Y": 0.07118336111307144}, {"X": 0.27624839544296265, "Y": 0.08161669969558716}, {"X": 0.19546598196029663, "Y": 0.08160959929227829}]}, "Id": "9c653d3d-6dd5-4e1b-b562-c94de78c2ba3"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024020560085773468, "Height": 0.008762350305914879, "Left": 0.28115928173065186, "Top": 0.07120873779058456}, "Polygon": [{"X": 0.28115934133529663, "Y": 0.07120873779058456}, {"X": 0.3051798343658447, "Y": 0.0712108463048935}, {"X": 0.30517974495887756, "Y": 0.07997108995914459}, {"X": 0.28115928173065186, "Y": 0.07996898144483566}]}, "Id": "9713cd8e-36a0-46c6-ad06-75760e8c6135"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "30092", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041400227695703506, "Height": 0.008878943510353565, "Left": 0.3096218705177307, "Top": 0.07112128287553787}, "Polygon": [{"X": 0.3096219599246979, "Y": 0.07112128287553787}, {"X": 0.3510220944881439, "Y": 0.07112491875886917}, {"X": 0.351021945476532, "Y": 0.08000022917985916}, {"X": 0.3096218705177307, "Y": 0.07999659329652786}]}, "Id": "9fa8dcb0-2f8f-407c-a502-4a8b4a68e9aa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "770-414-1929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09458013623952866, "Height": 0.009043813683092594, "Left": 0.17519186437129974, "Top": 0.08475837856531143}, "Polygon": [{"X": 0.17519186437129974, "Y": 0.08475837856531143}, {"X": 0.2697719931602478, "Y": 0.0847666785120964}, {"X": 0.269771933555603, "Y": 0.09380219131708145}, {"X": 0.17519189417362213, "Y": 0.09379388391971588}]}, "Id": "7f2860c5-d981-423f-b569-a1277abaea03"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03112718090415001, "Height": 0.008794553577899933, "Left": 0.1490323841571808, "Top": 0.09849487245082855}, "Polygon": [{"X": 0.1490323841571808, "Y": 0.09849487245082855}, {"X": 0.1801595240831375, "Y": 0.09849760681390762}, {"X": 0.1801595538854599, "Y": 0.10728942602872849}, {"X": 0.14903244376182556, "Y": 0.10728669166564941}]}, "Id": "52609d7d-3ce9-4244-b0ad-25818c1e46da"}, {"BlockType": "WORD", "Confidence": 99.091796875, "Text": "ID#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026228446513414383, "Height": 0.00882361177355051, "Left": 0.1850374937057495, "Top": 0.09843906760215759}, "Polygon": [{"X": 0.1850374937057495, "Y": 0.09843906760215759}, {"X": 0.2112659364938736, "Y": 0.09844136983156204}, {"X": 0.2112659364938736, "Y": 0.10726267844438553}, {"X": 0.1850375235080719, "Y": 0.10726037621498108}]}, "Id": "59813745-1026-494f-92db-fc42042871a9"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "45-3087555", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08112061023712158, "Height": 0.009113742038607597, "Left": 0.21467620134353638, "Top": 0.09835008531808853}, "Polygon": [{"X": 0.21467620134353638, "Y": 0.09835008531808853}, {"X": 0.29579681158065796, "Y": 0.09835720807313919}, {"X": 0.2957967221736908, "Y": 0.10746382176876068}, {"X": 0.21467620134353638, "Y": 0.10745669901371002}]}, "Id": "c04fe3c9-57f2-4287-b2c8-74b7d1291c20"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "09:05:24", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08921484649181366, "Height": 0.01320365909487009, "Left": 0.07393397390842438, "Top": 0.12812981009483337}, "Polygon": [{"X": 0.07393397390842438, "Y": 0.12812981009483337}, {"X": 0.16314873099327087, "Y": 0.12813764810562134}, {"X": 0.16314882040023804, "Y": 0.1413334757089615}, {"X": 0.07393418997526169, "Y": 0.14132563769817352}]}, "Id": "bc7c198e-9d40-4b9f-9d18-dd781ba40c7a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "September", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10756350308656693, "Height": 0.016988974064588547, "Left": 0.17025162279605865, "Top": 0.12795156240463257}, "Polygon": [{"X": 0.17025162279605865, "Y": 0.12795156240463257}, {"X": 0.2778151333332062, "Y": 0.12796100974082947}, {"X": 0.2778150141239166, "Y": 0.1449405401945114}, {"X": 0.17025171220302582, "Y": 0.14493109285831451}]}, "Id": "a4686c6f-50ef-44e3-b2ef-7a5b5bb5639c"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "23,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03044874407351017, "Height": 0.015525024384260178, "Left": 0.28364279866218567, "Top": 0.128265380859375}, "Polygon": [{"X": 0.2836429178714752, "Y": 0.128265380859375}, {"X": 0.3140915334224701, "Y": 0.1282680481672287}, {"X": 0.31409135460853577, "Y": 0.14379039406776428}, {"X": 0.28364279866218567, "Y": 0.14378772675991058}]}, "Id": "0b099298-8092-4204-98fa-0ec72296d21a"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04892079904675484, "Height": 0.01266996469348669, "Left": 0.32146942615509033, "Top": 0.1283702254295349}, "Polygon": [{"X": 0.32146960496902466, "Y": 0.1283702254295349}, {"X": 0.37039023637771606, "Y": 0.12837451696395874}, {"X": 0.37038999795913696, "Y": 0.14104019105434418}, {"X": 0.32146942615509033, "Y": 0.14103588461875916}]}, "Id": "e404fb45-0112-4341-b152-7e3281ebc0de"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051816441118717194, "Height": 0.011641692370176315, "Left": 0.008144558407366276, "Top": 0.16233031451702118}, "Polygon": [{"X": 0.008144558407366276, "Y": 0.16233031451702118}, {"X": 0.05996078625321388, "Y": 0.1623348593711853}, {"X": 0.059960998594760895, "Y": 0.1739720106124878}, {"X": 0.008144841529428959, "Y": 0.17396745085716248}]}, "Id": "2e0121c6-ceb6-4622-8f65-1d827c58facb"}, {"BlockType": "WORD", "Confidence": 99.1796875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012420771643519402, "Height": 0.008702797815203667, "Left": 0.06372206658124924, "Top": 0.16273245215415955}, "Polygon": [{"X": 0.06372206658124924, "Y": 0.16273245215415955}, {"X": 0.07614269852638245, "Y": 0.1627335399389267}, {"X": 0.07614284008741379, "Y": 0.17143525183200836}, {"X": 0.06372222304344177, "Y": 0.1714341640472412}]}, "Id": "6e579934-5526-44c3-b538-dee240804f85"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "abdded47-6a4d-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10865724086761475, "Height": 0.009154762141406536, "Left": 0.2184745967388153, "Top": 0.16248664259910583}, "Polygon": [{"X": 0.2184745967388153, "Y": 0.16248664259910583}, {"X": 0.32713183760643005, "Y": 0.1624961793422699}, {"X": 0.3271317183971405, "Y": 0.17164140939712524}, {"X": 0.2184745967388153, "Y": 0.1716318577528}]}, "Id": "c0f43ab3-a7f9-48ee-bd9b-1753f6e87e7f"}, {"BlockType": "WORD", "Confidence": 99.294677734375, "Text": "4102-abec-f09cd3c12da9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1703822910785675, "Height": 0.009194445796310902, "Left": 0.21817068755626678, "Top": 0.17590057849884033}, "Polygon": [{"X": 0.21817068755626678, "Y": 0.17590057849884033}, {"X": 0.3885529935359955, "Y": 0.17591553926467896}, {"X": 0.38855278491973877, "Y": 0.18509501218795776}, {"X": 0.21817068755626678, "Y": 0.18508005142211914}]}, "Id": "14caf153-3a71-4ed2-b013-4d84d99486d0"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0636940747499466, "Height": 0.009039170108735561, "Left": 0.008177836425602436, "Top": 0.19818806648254395}, "Polygon": [{"X": 0.008177836425602436, "Y": 0.19818806648254395}, {"X": 0.07187175750732422, "Y": 0.19819365441799164}, {"X": 0.07187191396951675, "Y": 0.20722723007202148}, {"X": 0.008178056217730045, "Y": 0.2072216421365738}]}, "Id": "432a4d48-9ad2-41ea-8c54-a1866b603555"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "US", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021481581032276154, "Height": 0.008898398838937283, "Left": 0.21799910068511963, "Top": 0.1982455551624298}, "Polygon": [{"X": 0.21799910068511963, "Y": 0.1982455551624298}, {"X": 0.23948067426681519, "Y": 0.19824744760990143}, {"X": 0.2394806444644928, "Y": 0.20714394748210907}, {"X": 0.21799910068511963, "Y": 0.20714206993579865}]}, "Id": "b3f23c74-4c6f-41c0-ad00-8dbdaea4dfcb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FOODS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054770536720752716, "Height": 0.008692866191267967, "Left": 0.24397772550582886, "Top": 0.19835585355758667}, "Polygon": [{"X": 0.24397775530815125, "Y": 0.19835585355758667}, {"X": 0.298748254776001, "Y": 0.19836066663265228}, {"X": 0.2987481653690338, "Y": 0.2070487141609192}, {"X": 0.24397772550582886, "Y": 0.20704390108585358}]}, "Id": "0740c253-1ab3-46cf-b622-33750841138d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CINCINNATI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09810129553079605, "Height": 0.008859732188284397, "Left": 0.3033818006515503, "Top": 0.19830209016799927}, "Polygon": [{"X": 0.30338189005851746, "Y": 0.19830209016799927}, {"X": 0.40148308873176575, "Y": 0.1983107179403305}, {"X": 0.4014829099178314, "Y": 0.2071618288755417}, {"X": 0.3033818006515503, "Y": 0.20715321600437164}]}, "Id": "e9ddb359-2711-46e6-91de-d7ccbefac24f"}, {"BlockType": "WORD", "Confidence": 98.24198913574219, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02400314249098301, "Height": 0.008698788471519947, "Left": 0.21843016147613525, "Top": 0.2116934061050415}, "Polygon": [{"X": 0.21843016147613525, "Y": 0.2116934061050415}, {"X": 0.2424333095550537, "Y": 0.21169550716876984}, {"X": 0.24243327975273132, "Y": 0.22039219737052917}, {"X": 0.21843016147613525, "Y": 0.22039008140563965}]}, "Id": "614fcf0c-4c7d-4a68-8bf6-4df2d9e5a43f"}, {"BlockType": "WORD", "Confidence": 99.42063903808594, "Text": "3W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024466564878821373, "Height": 0.008999398909509182, "Left": 0.24683654308319092, "Top": 0.21166491508483887}, "Polygon": [{"X": 0.2468365728855133, "Y": 0.21166491508483887}, {"X": 0.27130311727523804, "Y": 0.21166706085205078}, {"X": 0.27130305767059326, "Y": 0.22066430747509003}, {"X": 0.24683654308319092, "Y": 0.2206621617078781}]}, "Id": "57bf7d2f-1674-45e9-9fbe-0e0021766a3d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03901597857475281, "Height": 0.009000202640891075, "Left": 0.008077682927250862, "Top": 0.2338096797466278}, "Polygon": [{"X": 0.008077682927250862, "Y": 0.2338096797466278}, {"X": 0.04709348455071449, "Y": 0.2338131070137024}, {"X": 0.04709366336464882, "Y": 0.24280987679958344}, {"X": 0.008077901788055897, "Y": 0.24280644953250885}]}, "Id": "5059340b-15d6-4cbe-9d45-252539999c61"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035172801464796066, "Height": 0.008727754466235638, "Left": 0.050793856382369995, "Top": 0.233934223651886}, "Polygon": [{"X": 0.050793856382369995, "Y": 0.233934223651886}, {"X": 0.08596652746200562, "Y": 0.2339373081922531}, {"X": 0.08596665412187576, "Y": 0.2426619827747345}, {"X": 0.050794024020433426, "Y": 0.24265888333320618}]}, "Id": "6499a536-7a26-47e4-8909-a44c07a732e4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2025-09-23", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07772710919380188, "Height": 0.009051868692040443, "Left": 0.21829789876937866, "Top": 0.23381932079792023}, "Polygon": [{"X": 0.21829789876937866, "Y": 0.23381932079792023}, {"X": 0.29602500796318054, "Y": 0.233826145529747}, {"X": 0.2960249185562134, "Y": 0.24287119507789612}, {"X": 0.21829789876937866, "Y": 0.24286437034606934}]}, "Id": "40ef23bc-4dd5-480d-a7b6-b0662c1d3129"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02486225962638855, "Height": 0.008879081346094608, "Left": 0.008503729477524757, "Top": 0.2514336109161377}, "Polygon": [{"X": 0.008503729477524757, "Y": 0.2514336109161377}, {"X": 0.033365800976753235, "Y": 0.2514358162879944}, {"X": 0.03336598724126816, "Y": 0.2603127062320709}, {"X": 0.008503945544362068, "Y": 0.2603105306625366}]}, "Id": "2ac9f60f-d48f-49b9-b630-fdcd172a79d5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03885840252041817, "Height": 0.008838558569550514, "Left": 0.03805183991789818, "Top": 0.25147342681884766}, "Polygon": [{"X": 0.03805183991789818, "Y": 0.25147342681884766}, {"X": 0.076910100877285, "Y": 0.25147685408592224}, {"X": 0.07691024243831635, "Y": 0.2603119909763336}, {"X": 0.0380520224571228, "Y": 0.2603085935115814}]}, "Id": "da5a510f-f90f-4d75-8665-3ec2fb50e2fe"}, {"BlockType": "WORD", "Confidence": 99.47206115722656, "Text": "RCOD30315", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0883929431438446, "Height": 0.008946668356657028, "Left": 0.2182169109582901, "Top": 0.25157976150512695}, "Polygon": [{"X": 0.2182169258594513, "Y": 0.25157976150512695}, {"X": 0.3066098690032959, "Y": 0.25158753991127014}, {"X": 0.30660974979400635, "Y": 0.2605264186859131}, {"X": 0.2182169109582901, "Y": 0.2605186700820923}]}, "Id": "77b51f0c-9548-4518-a2dd-da238c5847f0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051022496074438095, "Height": 0.00882819015532732, "Left": 0.008738881908357143, "Top": 0.2694484293460846}, "Polygon": [{"X": 0.008738881908357143, "Y": 0.2694484293460846}, {"X": 0.05976121872663498, "Y": 0.26945292949676514}, {"X": 0.05976137891411781, "Y": 0.27827662229537964}, {"X": 0.008739096112549305, "Y": 0.2782721519470215}]}, "Id": "9cb3f979-b910-4ba0-9871-94601acf85d6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARRIER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07279305160045624, "Height": 0.008750996552407742, "Left": 0.2185031622648239, "Top": 0.2695677876472473}, "Polygon": [{"X": 0.2185031622648239, "Y": 0.2695677876472473}, {"X": 0.29129621386528015, "Y": 0.26957419514656067}, {"X": 0.291296124458313, "Y": 0.2783187925815582}, {"X": 0.2185031622648239, "Y": 0.2783123850822449}]}, "Id": "6f07339a-4bb5-4ada-b96a-390188d9af64"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "DOES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04320422559976578, "Height": 0.008643845096230507, "Left": 0.2954668402671814, "Top": 0.26961252093315125}, "Polygon": [{"X": 0.29546692967414856, "Y": 0.26961252093315125}, {"X": 0.3386710584163666, "Y": 0.2696163058280945}, {"X": 0.338670939207077, "Y": 0.278256356716156}, {"X": 0.2954668402671814, "Y": 0.27825257182121277}]}, "Id": "a2e14ece-70e3-425e-9f97-3d1465d1a646"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "NOT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034703806042671204, "Height": 0.008747345767915249, "Left": 0.34311482310295105, "Top": 0.269574373960495}, "Polygon": [{"X": 0.3431149423122406, "Y": 0.269574373960495}, {"X": 0.37781861424446106, "Y": 0.26957741379737854}, {"X": 0.37781843543052673, "Y": 0.2783217132091522}, {"X": 0.34311482310295105, "Y": 0.2783186733722687}]}, "Id": "e65a023d-a30d-4090-ba99-691c4cd32dab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04023122414946556, "Height": 0.009022913873195648, "Left": 0.008435364812612534, "Top": 0.2870439291000366}, "Polygon": [{"X": 0.008435364812612534, "Y": 0.2870439291000366}, {"X": 0.04866641387343407, "Y": 0.28704744577407837}, {"X": 0.048666588962078094, "Y": 0.2960668206214905}, {"X": 0.008435584604740143, "Y": 0.29606330394744873}]}, "Id": "3cd8fc0e-a362-4efa-a23c-ebb8819ede44"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03517632186412811, "Height": 0.008905908092856407, "Left": 0.21844042837619781, "Top": 0.2871910333633423}, "Polygon": [{"X": 0.218440443277359, "Y": 0.2871910333633423}, {"X": 0.2536167502403259, "Y": 0.2871941328048706}, {"X": 0.25361672043800354, "Y": 0.29609695076942444}, {"X": 0.21844042837619781, "Y": 0.2960938513278961}]}, "Id": "54356673-1ffa-4ca9-8c71-287cc44c19e5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03814519941806793, "Height": 0.008638055063784122, "Left": 0.00849840883165598, "Top": 0.3053167164325714}, "Polygon": [{"X": 0.00849840883165598, "Y": 0.3053167164325714}, {"X": 0.046643439680337906, "Y": 0.3053200840950012}, {"X": 0.04664360731840134, "Y": 0.31395477056503296}, {"X": 0.008498618379235268, "Y": 0.31395143270492554}]}, "Id": "fd49e368-8c3c-481c-9654-caea11e73023"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "29", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016295304521918297, "Height": 0.008822520263493061, "Left": 0.21841122210025787, "Top": 0.305290549993515}, "Polygon": [{"X": 0.21841123700141907, "Y": 0.305290549993515}, {"X": 0.23470653593540192, "Y": 0.3052919805049896}, {"X": 0.23470650613307953, "Y": 0.3141130805015564}, {"X": 0.21841122210025787, "Y": 0.3141116499900818}]}, "Id": "4cb999a8-33a1-426a-b1db-d580903b74c8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06073806807398796, "Height": 0.00897988025099039, "Left": 0.008284796960651875, "Top": 0.3297984302043915}, "Polygon": [{"X": 0.008284796960651875, "Y": 0.3297984302043915}, {"X": 0.06902270764112473, "Y": 0.3298037648200989}, {"X": 0.06902286410331726, "Y": 0.3387783169746399}, {"X": 0.008285014890134335, "Y": 0.3387729823589325}]}, "Id": "9af848f2-485f-4c9f-ac1f-7e5edd852f3d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Orders", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04543781280517578, "Height": 0.008932978846132755, "Left": 0.07334085553884506, "Top": 0.3298974633216858}, "Polygon": [{"X": 0.07334085553884506, "Y": 0.3298974633216858}, {"X": 0.11877857148647308, "Y": 0.32990145683288574}, {"X": 0.11877866834402084, "Y": 0.33883044123649597}, {"X": 0.073341004550457, "Y": 0.338826447725296}]}, "Id": "a8259de6-1b51-442a-9f69-b748c29735c8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05086766183376312, "Height": 0.008900132030248642, "Left": 0.21836696565151215, "Top": 0.32995927333831787}, "Polygon": [{"X": 0.21836698055267334, "Y": 0.32995927333831787}, {"X": 0.26923462748527527, "Y": 0.329963743686676}, {"X": 0.2692345678806305, "Y": 0.3388594090938568}, {"X": 0.21836696565151215, "Y": 0.33885493874549866}]}, "Id": "3a2ca354-e852-4d4d-96e3-5f68ef798e1e"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "7163773W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07479508221149445, "Height": 0.009473801590502262, "Left": 0.008294785395264626, "Top": 0.34770074486732483}, "Polygon": [{"X": 0.008294785395264626, "Y": 0.34770074486732483}, {"X": 0.08308972418308258, "Y": 0.3477073013782501}, {"X": 0.08308986574411392, "Y": 0.3571745455265045}, {"X": 0.008295015431940556, "Y": 0.35716795921325684}]}, "Id": "40799fbf-0ab3-47d5-bf82-a39369800fd0"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "TEAM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04763469845056534, "Height": 0.009195343591272831, "Left": 0.21811336278915405, "Top": 0.34773558378219604}, "Polygon": [{"X": 0.21811336278915405, "Y": 0.34773558378219604}, {"X": 0.2657480537891388, "Y": 0.3477397561073303}, {"X": 0.265747994184494, "Y": 0.3569309115409851}, {"X": 0.21811336278915405, "Y": 0.35692673921585083}]}, "Id": "ac66231d-1256-4dea-a825-022fdf2a6605"}, {"BlockType": "WORD", "Confidence": 99.5338363647461, "Text": "THREE-REDISTRIBUTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19881923496723175, "Height": 0.008900116197764874, "Left": 0.21797184646129608, "Top": 0.36122196912765503}, "Polygon": [{"X": 0.21797186136245728, "Y": 0.36122196912765503}, {"X": 0.41679108142852783, "Y": 0.3612394332885742}, {"X": 0.4167908728122711, "Y": 0.37012210488319397}, {"X": 0.21797184646129608, "Y": 0.3701046407222748}]}, "Id": "3a754fb0-3cb8-464e-8985-23dba3f43230"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03520705923438072, "Height": 0.00883104745298624, "Left": 0.008324678055942059, "Top": 0.3903197944164276}, "Polygon": [{"X": 0.008324678055942059, "Y": 0.3903197944164276}, {"X": 0.04353155940771103, "Y": 0.39032289385795593}, {"X": 0.04353173449635506, "Y": 0.3991508483886719}, {"X": 0.008324893191456795, "Y": 0.39914774894714355}]}, "Id": "e5b3c197-f403-40be-92bf-ae2bea003168"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Finished", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05805591121315956, "Height": 0.00875859148800373, "Left": 0.04801841452717781, "Top": 0.39034825563430786}, "Polygon": [{"X": 0.04801841452717781, "Y": 0.39034825563430786}, {"X": 0.10607421398162842, "Y": 0.39035335183143616}, {"X": 0.10607432574033737, "Y": 0.39910683035850525}, {"X": 0.04801858589053154, "Y": 0.39910173416137695}]}, "Id": "d7f1c97f-fafe-4021-a892-82f2556e7ec7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04835456982254982, "Height": 0.008763561956584454, "Left": 0.11020810902118683, "Top": 0.39030829071998596}, "Polygon": [{"X": 0.11020810902118683, "Y": 0.39030829071998596}, {"X": 0.15856261551380157, "Y": 0.390312522649765}, {"X": 0.15856267511844635, "Y": 0.3990718424320221}, {"X": 0.11020821332931519, "Y": 0.39906758069992065}]}, "Id": "660f5aaf-d3a3-482c-a9f4-210766079e95"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "100", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023614907637238503, "Height": 0.008653867058455944, "Left": 0.21992357075214386, "Top": 0.39045608043670654}, "Polygon": [{"X": 0.21992357075214386, "Y": 0.39045608043670654}, {"X": 0.24353846907615662, "Y": 0.3904581665992737}, {"X": 0.24353843927383423, "Y": 0.39910995960235596}, {"X": 0.21992357075214386, "Y": 0.3991078734397888}]}, "Id": "a8806d57-7a7a-4803-a721-96fc38960454"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035141658037900925, "Height": 0.009155641309916973, "Left": 0.008370183408260345, "Top": 0.4081292748451233}, "Polygon": [{"X": 0.008370183408260345, "Y": 0.4081292748451233}, {"X": 0.04351165518164635, "Y": 0.4081323444843292}, {"X": 0.04351184144616127, "Y": 0.41728490591049194}, {"X": 0.008370405994355679, "Y": 0.4172818064689636}]}, "Id": "bad4013d-299a-4601-92bc-2862113089dc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032438524067401886, "Height": 0.008577700704336166, "Left": 0.048213496804237366, "Top": 0.40852266550064087}, "Polygon": [{"X": 0.048213496804237366, "Y": 0.40852266550064087}, {"X": 0.08065188676118851, "Y": 0.4085255265235901}, {"X": 0.08065202087163925, "Y": 0.41710036993026733}, {"X": 0.048213664442300797, "Y": 0.4170975089073181}]}, "Id": "ceb256fd-d387-441c-98d6-66225c10e852"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Count:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04446539655327797, "Height": 0.00864988099783659, "Left": 0.08515869081020355, "Top": 0.4085511267185211}, "Polygon": [{"X": 0.08515869081020355, "Y": 0.4085511267185211}, {"X": 0.12962400913238525, "Y": 0.4085550308227539}, {"X": 0.12962408363819122, "Y": 0.4172009825706482}, {"X": 0.0851588249206543, "Y": 0.4171970784664154}]}, "Id": "ce92891c-08d7-4084-8b77-697ff8229416"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2205", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0334165133535862, "Height": 0.008977863006293774, "Left": 0.2182658314704895, "Top": 0.4084344804286957}, "Polygon": [{"X": 0.2182658463716507, "Y": 0.4084344804286957}, {"X": 0.2516823410987854, "Y": 0.4084374010562897}, {"X": 0.251682311296463, "Y": 0.41741234064102173}, {"X": 0.2182658314704895, "Y": 0.41740942001342773}]}, "Id": "a490a81d-120d-4f4d-8b2b-12d888612f4c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035187747329473495, "Height": 0.0089301448315382, "Left": 0.00839421059936285, "Top": 0.4259450137615204}, "Polygon": [{"X": 0.00839421059936285, "Y": 0.4259450137615204}, {"X": 0.043581780046224594, "Y": 0.4259480834007263}, {"X": 0.04358195886015892, "Y": 0.43487516045570374}, {"X": 0.008394427597522736, "Y": 0.4348720610141754}]}, "Id": "6be9f59a-8867-485c-9492-7cd776c45f71"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05301310867071152, "Height": 0.011441431939601898, "Left": 0.04797740653157234, "Top": 0.4258202612400055}, "Polygon": [{"X": 0.04797740653157234, "Y": 0.4258202612400055}, {"X": 0.10099036246538162, "Y": 0.42582494020462036}, {"X": 0.10099051147699356, "Y": 0.437261700630188}, {"X": 0.04797763004899025, "Y": 0.4372570514678955}]}, "Id": "96fb0a77-f822-4c86-ab7a-f30a003e11f4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "28205.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06289466470479965, "Height": 0.008721854537725449, "Left": 0.21847432851791382, "Top": 0.4260772168636322}, "Polygon": [{"X": 0.21847432851791382, "Y": 0.4260772168636322}, {"X": 0.28136900067329407, "Y": 0.4260827302932739}, {"X": 0.2813689112663269, "Y": 0.43479904532432556}, {"X": 0.21847432851791382, "Y": 0.43479353189468384}]}, "Id": "3b44c7a8-d192-4df0-884a-17988ace349d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04636069014668465, "Height": 0.00881620030850172, "Left": 0.008273606188595295, "Top": 0.443850040435791}, "Polygon": [{"X": 0.008273606188595295, "Y": 0.443850040435791}, {"X": 0.054634127765893936, "Y": 0.44385412335395813}, {"X": 0.05463429540395737, "Y": 0.4526662528514862}, {"X": 0.008273820392787457, "Y": 0.4526621699333191}]}, "Id": "69331bad-e95a-47c4-9737-3dcfc0baab4f"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05934840440750122, "Height": 0.008965328335762024, "Left": 0.05789289250969887, "Top": 0.44388458132743835}, "Polygon": [{"X": 0.05789289250969887, "Y": 0.44388458132743835}, {"X": 0.11724119633436203, "Y": 0.4438897967338562}, {"X": 0.11724130064249039, "Y": 0.4528498947620392}, {"X": 0.057893056422472, "Y": 0.45284467935562134}]}, "Id": "fbdc2a0c-0a2c-4378-8c15-498e71988c85"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0082298768684268, "Height": 0.00876916665583849, "Left": 0.21841850876808167, "Top": 0.44403672218322754}, "Polygon": [{"X": 0.21841850876808167, "Y": 0.44403672218322754}, {"X": 0.22664839029312134, "Y": 0.44403746724128723}, {"X": 0.22664837539196014, "Y": 0.45280590653419495}, {"X": 0.21841850876808167, "Y": 0.45280516147613525}]}, "Id": "958c663b-22b2-45ba-97d2-2a517c1fb5aa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05014510452747345, "Height": 0.008696966804564, "Left": 0.008336549624800682, "Top": 0.4618043005466461}, "Polygon": [{"X": 0.008336549624800682, "Y": 0.4618043005466461}, {"X": 0.05848149582743645, "Y": 0.4618087112903595}, {"X": 0.05848165601491928, "Y": 0.47050127387046814}, {"X": 0.00833676103502512, "Y": 0.47049686312675476}]}, "Id": "7404e377-caba-41c5-8b6e-86e767018b6b"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05901551991701126, "Height": 0.009096719324588776, "Left": 0.061798736453056335, "Top": 0.46148404479026794}, "Polygon": [{"X": 0.061798736453056335, "Y": 0.46148404479026794}, {"X": 0.12081415206193924, "Y": 0.4614892303943634}, {"X": 0.1208142563700676, "Y": 0.4705807566642761}, {"X": 0.06179890036582947, "Y": 0.47057557106018066}]}, "Id": "a478f1fd-121f-4a8f-a739-35842c68cedb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BOL:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037187885493040085, "Height": 0.008783808909356594, "Left": 0.008399550803005695, "Top": 0.47972339391708374}, "Polygon": [{"X": 0.008399550803005695, "Y": 0.47972339391708374}, {"X": 0.04558726027607918, "Y": 0.479726642370224}, {"X": 0.045587435364723206, "Y": 0.4885071814060211}, {"X": 0.008399764075875282, "Y": 0.48850393295288086}]}, "Id": "75f4218d-b196-44fd-93a7-d39fa7032d71"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07617875933647156, "Height": 0.008975650183856487, "Left": 0.008568806573748589, "Top": 0.4973137676715851}, "Polygon": [{"X": 0.008568806573748589, "Y": 0.4973137676715851}, {"X": 0.08474742621183395, "Y": 0.4973204731941223}, {"X": 0.0847475603222847, "Y": 0.5062894225120544}, {"X": 0.008569024503231049, "Y": 0.5062827467918396}]}, "Id": "7576cb2f-9358-4327-8757-bde6700b8b95"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Canned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05118166655302048, "Height": 0.008575181476771832, "Left": 0.008641708642244339, "Top": 0.5153520107269287}, "Polygon": [{"X": 0.008641708642244339, "Y": 0.5153520107269287}, {"X": 0.05982321873307228, "Y": 0.5153565406799316}, {"X": 0.059823375195264816, "Y": 0.5239272117614746}, {"X": 0.008641916327178478, "Y": 0.5239227414131165}]}, "Id": "f1f68f58-231f-4328-87d2-1f0749dcec47"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.076230488717556, "Height": 0.008626519702374935, "Left": 0.06405677646398544, "Top": 0.5154407620429993}, "Polygon": [{"X": 0.06405677646398544, "Y": 0.5154407620429993}, {"X": 0.14028719067573547, "Y": 0.5154474377632141}, {"X": 0.14028726518154144, "Y": 0.5240672826766968}, {"X": 0.06405692547559738, "Y": 0.5240606069564819}]}, "Id": "d4c9b1be-5577-4e82-b46e-47ead8ef9004"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Unloaders:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07250294834375381, "Height": 0.008964789099991322, "Left": 0.00836679246276617, "Top": 0.5327576398849487}, "Polygon": [{"X": 0.00836679246276617, "Y": 0.5327576398849487}, {"X": 0.08086960017681122, "Y": 0.5327640175819397}, {"X": 0.08086974173784256, "Y": 0.5417224168777466}, {"X": 0.00836701039224863, "Y": 0.5417160987854004}]}, "Id": "0a79759c-d9e7-47d0-839d-a311d454b2e8"}, {"BlockType": "WORD", "Confidence": 98.97560119628906, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004625690635293722, "Height": 0.008851837366819382, "Left": 0.2201656699180603, "Top": 0.5329271554946899}, "Polygon": [{"X": 0.2201656848192215, "Y": 0.5329271554946899}, {"X": 0.22479136288166046, "Y": 0.5329275131225586}, {"X": 0.22479136288166046, "Y": 0.5417789816856384}, {"X": 0.2201656699180603, "Y": 0.541778564453125}]}, "Id": "d64792b1-8122-4da0-9113-02d6a52e177a"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Base", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03268372267484665, "Height": 0.008804013021290302, "Left": 0.008465995080769062, "Top": 0.5580689907073975}, "Polygon": [{"X": 0.008465995080769062, "Y": 0.5580689907073975}, {"X": 0.04114954173564911, "Y": 0.5580718517303467}, {"X": 0.041149720549583435, "Y": 0.5668729543685913}, {"X": 0.008466209284961224, "Y": 0.5668700933456421}]}, "Id": "69708e93-95a4-4955-8890-8ba6ffe78410"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05203695595264435, "Height": 0.01118459366261959, "Left": 0.045449335128068924, "Top": 0.5578915476799011}, "Polygon": [{"X": 0.045449335128068924, "Y": 0.5578915476799011}, {"X": 0.09748613834381104, "Y": 0.5578961372375488}, {"X": 0.09748628735542297, "Y": 0.5690761208534241}, {"X": 0.04544955492019653, "Y": 0.5690715312957764}]}, "Id": "66c14e35-d2e9-4547-b0f6-9dc95f9c227b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045998167246580124, "Height": 0.009090442210435867, "Left": 0.2187076359987259, "Top": 0.5579280853271484}, "Polygon": [{"X": 0.21870765089988708, "Y": 0.5579280853271484}, {"X": 0.2647058069705963, "Y": 0.5579321384429932}, {"X": 0.26470574736595154, "Y": 0.5670185089111328}, {"X": 0.2187076359987259, "Y": 0.5670145153999329}]}, "Id": "380a4348-4fc2-404f-ba63-05ad045dfe96"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08766293525695801, "Height": 0.00882114190608263, "Left": 0.008586077950894833, "Top": 0.5756874680519104}, "Polygon": [{"X": 0.008586077950894833, "Y": 0.5756874680519104}, {"X": 0.09624888747930527, "Y": 0.5756951570510864}, {"X": 0.09624900668859482, "Y": 0.5845085978507996}, {"X": 0.008586292155086994, "Y": 0.5845009088516235}]}, "Id": "81f3edcd-99a5-469f-968e-09ed9dedd68b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027837002649903297, "Height": 0.008743951097130775, "Left": 0.10048389434814453, "Top": 0.5756404399871826}, "Polygon": [{"X": 0.10048389434814453, "Y": 0.5756404399871826}, {"X": 0.1283208131790161, "Y": 0.5756428837776184}, {"X": 0.12832090258598328, "Y": 0.5843843817710876}, {"X": 0.10048401355743408, "Y": 0.5843819379806519}]}, "Id": "41abe567-a251-48dd-af9d-9a748c2ed3eb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03615653142333031, "Height": 0.008849347941577435, "Left": 0.21982061862945557, "Top": 0.5757320523262024}, "Polygon": [{"X": 0.21982063353061676, "Y": 0.5757320523262024}, {"X": 0.25597715377807617, "Y": 0.5757352113723755}, {"X": 0.2559771239757538, "Y": 0.5845813751220703}, {"X": 0.21982061862945557, "Y": 0.5845782160758972}]}, "Id": "edd92db1-bae7-44da-9b96-16c802a0154b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03500073775649071, "Height": 0.00904555432498455, "Left": 0.008350272662937641, "Top": 0.5934895277023315}, "Polygon": [{"X": 0.008350272662937641, "Y": 0.5934895277023315}, {"X": 0.04335082694888115, "Y": 0.5934925675392151}, {"X": 0.043351009488105774, "Y": 0.6025350689888}, {"X": 0.008350493386387825, "Y": 0.6025319695472717}]}, "Id": "9a2e239d-6570-4754-b8ba-5537040f001b"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Cost:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03452085331082344, "Height": 0.008638531900942326, "Left": 0.0480944998562336, "Top": 0.5937337279319763}, "Polygon": [{"X": 0.0480944998562336, "Y": 0.5937337279319763}, {"X": 0.08261521905660629, "Y": 0.5937367677688599}, {"X": 0.08261535316705704, "Y": 0.6023722290992737}, {"X": 0.048094671219587326, "Y": 0.6023691892623901}]}, "Id": "add9c73f-9dfe-4d35-b105-6fb66f8d6184"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "510.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04594118893146515, "Height": 0.009339669719338417, "Left": 0.21869735419750214, "Top": 0.5934710502624512}, "Polygon": [{"X": 0.21869735419750214, "Y": 0.5934710502624512}, {"X": 0.2646385431289673, "Y": 0.5934751033782959}, {"X": 0.2646384835243225, "Y": 0.6028107404708862}, {"X": 0.21869735419750214, "Y": 0.6028066873550415}]}, "Id": "f416c611-a877-4390-9a75-2f25142e6ac1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06896629184484482, "Height": 0.011080441065132618, "Left": 0.008281613700091839, "Top": 0.6185397505760193}, "Polygon": [{"X": 0.008281613700091839, "Y": 0.6185397505760193}, {"X": 0.0772477313876152, "Y": 0.6185457706451416}, {"X": 0.07724791020154953, "Y": 0.6296201944351196}, {"X": 0.008281882852315903, "Y": 0.6296141147613525}]}, "Id": "e9294fab-270d-4a2f-a242-f4b4abfbe32e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05589422583580017, "Height": 0.0086582712829113, "Left": 0.21814681589603424, "Top": 0.6186829209327698}, "Polygon": [{"X": 0.21814683079719543, "Y": 0.6186829209327698}, {"X": 0.2740410566329956, "Y": 0.6186878681182861}, {"X": 0.27404099702835083, "Y": 0.6273412108421326}, {"X": 0.21814681589603424, "Y": 0.627336323261261}]}, "Id": "993fd254-27d9-4653-97c7-29b8baab8769"}, {"BlockType": "WORD", "Confidence": 99.13365173339844, "Text": "CapstonePay-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09230265766382217, "Height": 0.010882600210607052, "Left": 0.042539387941360474, "Top": 0.6365899443626404}, "Polygon": [{"X": 0.042539387941360474, "Y": 0.6365899443626404}, {"X": 0.1348419487476349, "Y": 0.6365980505943298}, {"X": 0.13484203815460205, "Y": 0.6474725604057312}, {"X": 0.04253961145877838, "Y": 0.6474644541740417}]}, "Id": "ce132509-f195-42d9-a566-55e55143b8ae"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$510.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05417800322175026, "Height": 0.009784932248294353, "Left": 0.2186148315668106, "Top": 0.635902464389801}, "Polygon": [{"X": 0.2186148315668106, "Y": 0.635902464389801}, {"X": 0.27279284596443176, "Y": 0.6359072327613831}, {"X": 0.2727927565574646, "Y": 0.6456874012947083}, {"X": 0.2186148315668106, "Y": 0.6456826329231262}]}, "Id": "97d2a602-9072-485a-991d-1e120bc5d140"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "38054139", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06640410423278809, "Height": 0.009031868539750576, "Left": 0.04249372333288193, "Top": 0.6500594615936279}, "Polygon": [{"X": 0.04249372333288193, "Y": 0.6500594615936279}, {"X": 0.10889771580696106, "Y": 0.6500653028488159}, {"X": 0.10889782756567001, "Y": 0.6590913534164429}, {"X": 0.04249390587210655, "Y": 0.6590855121612549}]}, "Id": "44678263-ab62-4eae-bedf-8da8764dda96"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035178374499082565, "Height": 0.009283226914703846, "Left": 0.042015042155981064, "Top": 0.6717744469642639}, "Polygon": [{"X": 0.042015042155981064, "Y": 0.6717744469642639}, {"X": 0.07719326764345169, "Y": 0.6717775464057922}, {"X": 0.07719341665506363, "Y": 0.6810576915740967}, {"X": 0.04201522842049599, "Y": 0.6810545921325684}]}, "Id": "8285bf77-350e-4dfd-81fa-6c674d5bfb37"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Payments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06523586809635162, "Height": 0.01136481761932373, "Left": 0.08159636706113815, "Top": 0.6719731092453003}, "Polygon": [{"X": 0.08159636706113815, "Y": 0.6719731092453003}, {"X": 0.1468321532011032, "Y": 0.6719788908958435}, {"X": 0.14683224260807037, "Y": 0.683337926864624}, {"X": 0.08159654587507248, "Y": 0.6833322048187256}]}, "Id": "a29818dc-abfc-4b58-ba6c-2e849165e63d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$510.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05435352772474289, "Height": 0.010139241814613342, "Left": 0.2184671312570572, "Top": 0.6713500618934631}, "Polygon": [{"X": 0.21846714615821838, "Y": 0.6713500618934631}, {"X": 0.2728206515312195, "Y": 0.6713548302650452}, {"X": 0.2728205919265747, "Y": 0.6814892888069153}, {"X": 0.2184671312570572, "Y": 0.6814845204353333}]}, "Id": "91c205db-07ec-4311-a3f6-b39936afe3d0"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "91a65e95-5a34-476f-8c7d-2aa60ccf492b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "91a65e95-5a34-476f-8c7d-2aa60ccf492b", "content-type": "application/x-amz-json-1.1", "content-length": "75721", "date": "Wed, 24 Sep 2025 13:26:51 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "b53b984d-9c1b-428f-b35c-defebc11c4f7", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:53 GMT", "content-type": "application/json", "content-length": "818", "connection": "keep-alive", "x-amzn-requestid": "b53b984d-9c1b-428f-b35c-defebc11c4f7"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have single page. Need to classify. Let's examine content. It seems to be a receipt or invoice. Contains \"Receipt #\", \"Bill Code\", \"Carrier\", \"Total Finished Pallets\", \"Total Weight\", \"Base Charge\", \"Convenience Fee\", \"Total Cost\". Also \"Payments: Amount CapstonePay-\". This appears to be a carrier invoice or fee receipt. No HS codes, no commercial invoice. So likely \"invoice\". It is a carrier invoice. So doc_type: \"invoice\". Output JSON with documents array with page_no 1."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"invoice\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2834, "outputTokens": 132, "totalTokens": 2966}, "metrics": {"latencyMs": 808}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/b7440965_OLQ7TIWW6EVTC6BXA1II.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nCapstone Logistics\n30 TECHNOLOGY PKWY SOUTH SUITE 200\nPEACHTREE CORNERS, GA 30092\n770-414-1929\nFED ID# 45-3087555\n09:05:24 September 23, 2025\nReceipt #:\nabdded47-6a4d-\n4102-abec-f09cd3c12da9\nLocation:\nUS FOODS CINCINNATI\nOH 3W\nWork Date:\n2025-09-23\nBill Code:\nRCOD30315\nCarrier:\nCARRIER DOES NOT\nDock:\nDRY\nDoor:\n29\nPurchase Orders\nVendor\n7163773W\nTEAM\nTHREE-REDISTRIBUTION\nTotal Finished Pallets:\n100\nTotal Case Count:\n2205\nTotal Weight:\n28205.00\nTrailer Number:\n0\nTractor Number:\nBOL:\nComments:\nCanned Comments:\nUnloaders:\n1\nBase Charge:\n500.00\nConvenience Fee:\n10.00\nTotal Cost:\n510.00\nPayments:\nAmount\nCapstonePay-\n$510.00\n38054139\nTotal Payments\n$510.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}