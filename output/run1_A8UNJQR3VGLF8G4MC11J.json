{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 3.117942526387196e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.4163790140742094e-08, "Y": 1.0}]}, "Id": "de349ffc-bca9-457b-9ba7-7e44071ff1d8", "Relationships": [{"Type": "CHILD", "Ids": ["36762a49-2327-44b4-8f55-0b67913a3458", "6b27b2f4-720c-41a6-82e4-abae141ee4e8", "99624f99-a14b-418c-8d07-beb276fe803e", "af8755e5-17a6-477e-87f6-cd28d4c9b5e6", "6dcd296d-784b-4f3c-b51e-47e9e2262889", "e12ec02a-87b6-4e87-a577-c81d5b797e4f", "7117dcb8-144e-439d-b571-ad9d303f737d", "ad2eec81-5ff2-4a23-8785-bb9ab7cdc3fe", "7558e303-cbe3-4aca-9179-be37b75138cb", "c4e16217-b093-4825-9011-a3cd699bbfdd", "611bece6-4827-4217-9747-e12094970b68", "e9b1000d-1513-4190-aaac-55082a255897", "e58cf7e8-840b-4714-bf32-faefab02e830", "34a2fbb3-c0ca-4f63-9cc3-76ef3d53e909", "c84879b6-830a-4d3c-9975-df3f2c564f0d", "d6c8b24a-92d2-46a1-897f-3384bb614dfa", "0f93b7c7-d4c2-4c92-b8fc-bad577defa31", "c6fd9a03-9967-4fe6-aa99-e1be45081d5f", "3e8dd845-4878-46ae-80bd-e076e904a974", "688aad1e-8671-459c-8983-1d3d4f401b04", "232e1f2b-0eae-40e4-a4ba-cdb5bf8ec933", "ec6f9b22-52fb-44c0-b4c6-0678555c476d", "d201fd5b-42bb-4b25-8c84-369111522152", "0a3660f2-51b0-476b-b95c-f5c08993ec65", "7c42ef0b-7265-4c52-a34a-e451f9bf52dc", "8f12a570-0d4e-43ce-b94f-4cd30e360d7d", "7a878d1b-0057-4a91-a36d-75678eead012", "e19dd942-025f-4939-ad0d-52b84b11710c", "cc5158d5-f898-48ff-b0ec-33560d75e13d", "3bd84d3d-d8c3-46e5-becb-65d064059ee3", "01b992df-d199-4b67-a184-2e516a4c71b0", "db80dc5e-e7ac-4a64-aa09-d3b92cf0ac87", "384330c2-df09-45ba-886a-8303a2403716"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "22", "Geometry": {"BoundingBox": {"Width": 0.018967419862747192, "Height": 0.01908302865922451, "Left": 0.538722813129425, "Top": 0.06924674659967422}, "Polygon": [{"X": 0.5387228727340698, "Y": 0.06924697011709213}, {"X": 0.5576902627944946, "Y": 0.06924674659967422}, {"X": 0.5576902031898499, "Y": 0.08832956105470657}, {"X": 0.538722813129425, "Y": 0.08832977712154388}]}, "Id": "36762a49-2327-44b4-8f55-0b67913a3458", "Relationships": [{"Type": "CHILD", "Ids": ["f04dce72-5e57-4ff3-aba9-13534aedec09"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Quincy", "Geometry": {"BoundingBox": {"Width": 0.12330450117588043, "Height": 0.04742755740880966, "Left": 0.14421379566192627, "Top": 0.10360948741436005}, "Polygon": [{"X": 0.14421379566192627, "Y": 0.10361088067293167}, {"X": 0.2675180435180664, "Y": 0.10360948741436005}, {"X": 0.2675183117389679, "Y": 0.15103568136692047}, {"X": 0.1442141830921173, "Y": 0.1510370522737503}]}, "Id": "6b27b2f4-720c-41a6-82e4-abae141ee4e8", "Relationships": [{"Type": "CHILD", "Ids": ["0985de4b-ccbc-4cfb-8d88-681c96c5b25f"]}]}, {"BlockType": "LINE", "Confidence": 98.68463134765625, "Text": "RECYCLE", "Geometry": {"BoundingBox": {"Width": 0.09603939205408096, "Height": 0.015556512400507927, "Left": 0.15010212361812592, "Top": 0.15007789433002472}, "Polygon": [{"X": 0.15010212361812592, "Y": 0.15007895231246948}, {"X": 0.24614141881465912, "Y": 0.15007789433002472}, {"X": 0.24614152312278748, "Y": 0.16563335061073303}, {"X": 0.15010225772857666, "Y": 0.1656344085931778}]}, "Id": "99624f99-a14b-418c-8d07-beb276fe803e", "Relationships": [{"Type": "CHILD", "Ids": ["49b2b2d8-1ad2-4e72-954a-91b0677032ee"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "ELECTRONIC SCALE TICKET", "Geometry": {"BoundingBox": {"Width": 0.3649231791496277, "Height": 0.029771119356155396, "Left": 0.08056754618883133, "Top": 0.18123281002044678}, "Polygon": [{"X": 0.08056754618883133, "Y": 0.18123675882816315}, {"X": 0.4454907178878784, "Y": 0.18123281002044678}, {"X": 0.4454907476902008, "Y": 0.21100005507469177}, {"X": 0.08056783676147461, "Y": 0.21100392937660217}]}, "Id": "af8755e5-17a6-477e-87f6-cd28d4c9b5e6", "Relationships": [{"Type": "CHILD", "Ids": ["e76a8cd2-eb92-45fc-afbd-6f8a0a273d45", "07588b6a-f557-4452-984b-1e243418f1f2", "893535d6-e854-4195-a24e-2370ce7c7467"]}]}, {"BlockType": "LINE", "Confidence": 60.13054275512695, "Text": "ID4", "Geometry": {"BoundingBox": {"Width": 0.02343960665166378, "Height": 0.021165531128644943, "Left": 0.6646358370780945, "Top": 0.23285114765167236}, "Polygon": [{"X": 0.6646358966827393, "Y": 0.23285140097141266}, {"X": 0.6880754232406616, "Y": 0.23285114765167236}, {"X": 0.6880753636360168, "Y": 0.2540164291858673}, {"X": 0.6646358370780945, "Y": 0.2540166974067688}]}, "Id": "6dcd296d-784b-4f3c-b51e-47e9e2262889", "Relationships": [{"Type": "CHILD", "Ids": ["287f0aa1-d6c6-49a6-b2d4-6244d087709f"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "DATE", "Geometry": {"BoundingBox": {"Width": 0.03444308787584305, "Height": 0.017727505415678024, "Left": 0.08247493952512741, "Top": 0.24582962691783905}, "Polygon": [{"X": 0.08247493952512741, "Y": 0.2458299845457077}, {"X": 0.11691787093877792, "Y": 0.24582962691783905}, {"X": 0.11691802740097046, "Y": 0.2635567784309387}, {"X": 0.08247511088848114, "Y": 0.2635571360588074}]}, "Id": "e12ec02a-87b6-4e87-a577-c81d5b797e4f", "Relationships": [{"Type": "CHILD", "Ids": ["a3373e2f-77e8-4b0d-bf63-cee28822199d"]}]}, {"BlockType": "LINE", "Confidence": 90.04055786132812, "Text": "GROSS 60180 1b INBOUND", "Geometry": {"BoundingBox": {"Width": 0.22251108288764954, "Height": 0.02169174887239933, "Left": 0.6617772579193115, "Top": 0.2865135967731476}, "Polygon": [{"X": 0.6617773771286011, "Y": 0.28651586174964905}, {"X": 0.8842883706092834, "Y": 0.2865135967731476}, {"X": 0.8842881917953491, "Y": 0.3082031309604645}, {"X": 0.6617772579193115, "Y": 0.30820533633232117}]}, "Id": "7117dcb8-144e-439d-b571-ad9d303f737d", "Relationships": [{"Type": "CHILD", "Ids": ["5e1fa2a8-d546-4cff-adec-7a490cfdb484", "143de3fb-6804-4e8c-a740-4617a7b49a2b", "f361c013-8d71-410b-8acb-1abeca17a630", "223d1253-c97d-471c-b603-a0c7f9f7a822"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "COMPANY", "Geometry": {"BoundingBox": {"Width": 0.06590182334184647, "Height": 0.017093542963266373, "Left": 0.0825219452381134, "Top": 0.30718129873275757}, "Polygon": [{"X": 0.0825219452381134, "Y": 0.3071819543838501}, {"X": 0.14842364192008972, "Y": 0.30718129873275757}, {"X": 0.14842377603054047, "Y": 0.3242741823196411}, {"X": 0.08252211660146713, "Y": 0.32427483797073364}]}, "Id": "ad2eec81-5ff2-4a23-8785-bb9ab7cdc3fe", "Relationships": [{"Type": "CHILD", "Ids": ["4aad23b7-6562-4017-86dc-f3815eecc4c8"]}]}, {"BlockType": "LINE", "Confidence": 84.05442810058594, "Text": "BLOOMINGTON OFFSET PROCESS INC. BL DOMINGTON n. 61702 59924 37273112-DAR", "Geometry": {"BoundingBox": {"Width": 0.012625621631741524, "Height": 0.5742081999778748, "Left": 0.06183503940701485, "Top": 0.21489307284355164}, "Polygon": [{"X": 0.06183503940701485, "Y": 0.21489320695400238}, {"X": 0.07445498555898666, "Y": 0.21489307284355164}, {"X": 0.07446066290140152, "Y": 0.7891011834144592}, {"X": 0.06184088811278343, "Y": 0.789101243019104}]}, "Id": "7558e303-cbe3-4aca-9179-be37b75138cb", "Relationships": [{"Type": "CHILD", "Ids": ["87432d2a-e0f3-4442-9d55-3cee1f35494e", "4ad2b3b5-b6f2-457e-a894-93250959d601", "75be5056-3b5e-4008-ad37-eeb84dfae546", "7737106e-eca2-41a8-8d7c-03c278a0796e", "8422ff5c-e719-4727-a9e2-346052df8f98", "82bc95e8-4981-475b-ba85-6d84add07e29", "0c6438c4-5609-4255-9174-74f4982074c5", "20757bda-6110-4fc0-9073-e6467333e6d0", "e29f1f5a-f763-4315-83ac-20ee87595217", "24d8c873-bd18-4f74-9734-79c9a1f51845"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "NAME", "Geometry": {"BoundingBox": {"Width": 0.0379115529358387, "Height": 0.017611879855394363, "Left": 0.0826258510351181, "Top": 0.33744269609451294}, "Polygon": [{"X": 0.0826258510351181, "Y": 0.3374430537223816}, {"X": 0.12053725123405457, "Y": 0.33744269609451294}, {"X": 0.1205374076962471, "Y": 0.35505419969558716}, {"X": 0.08262602239847183, "Y": 0.3550545573234558}]}, "Id": "c4e16217-b093-4825-9011-a3cd699bbfdd", "Relationships": [{"Type": "CHILD", "Ids": ["fa5afd83-5799-4d1e-8b78-11af2f9fb471"]}]}, {"BlockType": "LINE", "Confidence": 95.95463562011719, "Text": "Plastic Ingenuity", "Geometry": {"BoundingBox": {"Width": 0.2065964639186859, "Height": 0.06845436990261078, "Left": 0.15947984158992767, "Top": 0.31567177176475525}, "Polygon": [{"X": 0.15947984158992767, "Y": 0.3156737983226776}, {"X": 0.3660760819911957, "Y": 0.31567177176475525}, {"X": 0.3660762906074524, "Y": 0.38412418961524963}, {"X": 0.15948039293289185, "Y": 0.38412612676620483}]}, "Id": "611bece6-4827-4217-9747-e12094970b68", "Relationships": [{"Type": "CHILD", "Ids": ["663b7976-1ed5-4d61-850a-4647a630a6a3", "fbe37555-e588-4370-8e6f-bf87da2282f6"]}]}, {"BlockType": "LINE", "Confidence": 99.8718490600586, "Text": "09/04/2025 09:57AM", "Geometry": {"BoundingBox": {"Width": 0.16021327674388885, "Height": 0.021401297301054, "Left": 0.6627776622772217, "Top": 0.339263379573822}, "Polygon": [{"X": 0.6627777218818665, "Y": 0.3392649292945862}, {"X": 0.822990894317627, "Y": 0.339263379573822}, {"X": 0.8229907751083374, "Y": 0.36066311597824097}, {"X": 0.6627776622772217, "Y": 0.3606646656990051}]}, "Id": "e9b1000d-1513-4190-aaac-55082a255897", "Relationships": [{"Type": "CHILD", "Ids": ["674c21d0-6b86-4f5b-a33d-b199acecfd24", "9d6beb4a-5500-4dcd-9208-7e588f6f97f9"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "RIMAS#", "Geometry": {"BoundingBox": {"Width": 0.04912838712334633, "Height": 0.017279261723160744, "Left": 0.08313275128602982, "Top": 0.3991844654083252}, "Polygon": [{"X": 0.08313275128602982, "Y": 0.3991849422454834}, {"X": 0.1322609931230545, "Y": 0.3991844654083252}, {"X": 0.13226114213466644, "Y": 0.41646328568458557}, {"X": 0.08313291519880295, "Y": 0.4164637327194214}]}, "Id": "e58cf7e8-840b-4714-bf32-faefab02e830", "Relationships": [{"Type": "CHILD", "Ids": ["df84c020-af1f-4b31-8990-432a353ad691"]}]}, {"BlockType": "LINE", "Confidence": 99.79173278808594, "Text": "1892858", "Geometry": {"BoundingBox": {"Width": 0.11805611103773117, "Height": 0.033073727041482925, "Left": 0.1890472173690796, "Top": 0.3867896497249603}, "Polygon": [{"X": 0.1890472173690796, "Y": 0.38679075241088867}, {"X": 0.3071031868457794, "Y": 0.3867896497249603}, {"X": 0.30710333585739136, "Y": 0.4198622703552246}, {"X": 0.1890474557876587, "Y": 0.41986337304115295}]}, "Id": "34a2fbb3-c0ca-4f63-9cc3-76ef3d53e909", "Relationships": [{"Type": "CHILD", "Ids": ["d38ac475-1180-45fc-907c-b139e99ba99f"]}]}, {"BlockType": "LINE", "Confidence": 99.19413757324219, "Text": "ID 4", "Geometry": {"BoundingBox": {"Width": 0.03326082229614258, "Height": 0.021144641563296318, "Left": 0.6562516093254089, "Top": 0.44292309880256653}, "Polygon": [{"X": 0.6562516689300537, "Y": 0.4429233968257904}, {"X": 0.6895124316215515, "Y": 0.44292309880256653}, {"X": 0.6895123720169067, "Y": 0.4640674591064453}, {"X": 0.6562516093254089, "Y": 0.4640677571296692}]}, "Id": "c84879b6-830a-4d3c-9975-df3f2c564f0d", "Relationships": [{"Type": "CHILD", "Ids": ["96c74056-a924-4563-a9cb-40a1f24d0813", "58a88a21-3f20-4763-b816-d38a8c36df8a"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "COMMODITY", "Geometry": {"BoundingBox": {"Width": 0.07999587059020996, "Height": 0.017448028549551964, "Left": 0.08290845900774002, "Top": 0.4604684114456177}, "Polygon": [{"X": 0.08290845900774002, "Y": 0.460469126701355}, {"X": 0.16290420293807983, "Y": 0.4604684114456177}, {"X": 0.16290433704853058, "Y": 0.4779157340526581}, {"X": 0.08290863037109375, "Y": 0.477916419506073}]}, "Id": "d6c8b24a-92d2-46a1-897f-3384bb614dfa", "Relationships": [{"Type": "CHILD", "Ids": ["4616a966-8ab2-4bda-b995-a5876b1885ee"]}]}, {"BlockType": "LINE", "Confidence": 96.66234588623047, "Text": "B", "Geometry": {"BoundingBox": {"Width": 0.008700113743543625, "Height": 0.01692778244614601, "Left": 0.48729366064071655, "Top": 0.46145206689834595}, "Polygon": [{"X": 0.48729366064071655, "Y": 0.4614521563053131}, {"X": 0.4959937632083893, "Y": 0.46145206689834595}, {"X": 0.49599379301071167, "Y": 0.4783797860145569}, {"X": 0.48729366064071655, "Y": 0.47837984561920166}]}, "Id": "0f93b7c7-d4c2-4c92-b8fc-bad577defa31", "Relationships": [{"Type": "CHILD", "Ids": ["cff2e4d2-c97f-4024-99f5-4f1a1fedc334"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "GROSS", "Geometry": {"BoundingBox": {"Width": 0.044641293585300446, "Height": 0.0213495884090662, "Left": 0.661774218082428, "Top": 0.4962230920791626}, "Polygon": [{"X": 0.6617743372917175, "Y": 0.49622347950935364}, {"X": 0.7064155340194702, "Y": 0.4962230920791626}, {"X": 0.7064154148101807, "Y": 0.517572283744812}, {"X": 0.661774218082428, "Y": 0.5175727009773254}]}, "Id": "c6fd9a03-9967-4fe6-aa99-e1be45081d5f", "Relationships": [{"Type": "CHILD", "Ids": ["583975b2-f98a-474f-acb3-9fe5a64d8448"]}]}, {"BlockType": "LINE", "Confidence": 89.27827453613281, "Text": "60180 1b RECALLED", "Geometry": {"BoundingBox": {"Width": 0.15185494720935822, "Height": 0.021945511922240257, "Left": 0.7419872879981995, "Top": 0.49645286798477173}, "Polygon": [{"X": 0.741987407207489, "Y": 0.4964542090892792}, {"X": 0.8938422203063965, "Y": 0.49645286798477173}, {"X": 0.8938420414924622, "Y": 0.5183970928192139}, {"X": 0.7419872879981995, "Y": 0.5183984041213989}]}, "Id": "3e8dd845-4878-46ae-80bd-e076e904a974", "Relationships": [{"Type": "CHILD", "Ids": ["12273af6-508b-4a30-b3e7-ef816e474486", "0ed1dd17-53a6-4e25-a309-14fccc07a576", "022cbccf-8ff0-4b59-8f35-e10c1dffb037"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TRUCKING", "Geometry": {"BoundingBox": {"Width": 0.06788383424282074, "Height": 0.01697067730128765, "Left": 0.08264128863811493, "Top": 0.5222025513648987}, "Polygon": [{"X": 0.08264128863811493, "Y": 0.5222030878067017}, {"X": 0.15052497386932373, "Y": 0.5222025513648987}, {"X": 0.15052512288093567, "Y": 0.5391726493835449}, {"X": 0.08264145255088806, "Y": 0.5391731858253479}]}, "Id": "688aad1e-8671-459c-8983-1d3d4f401b04", "Relationships": [{"Type": "CHILD", "Ids": ["cb5cbbf4-c388-4fd6-8948-e0e6eac9dc98"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "TARE", "Geometry": {"BoundingBox": {"Width": 0.033814139664173126, "Height": 0.020649179816246033, "Left": 0.6638299822807312, "Top": 0.5225731730461121}, "Polygon": [{"X": 0.6638301014900208, "Y": 0.5225734710693359}, {"X": 0.6976441144943237, "Y": 0.5225731730461121}, {"X": 0.697644054889679, "Y": 0.5432220697402954}, {"X": 0.6638299822807312, "Y": 0.5432223081588745}]}, "Id": "232e1f2b-0eae-40e4-a4ba-cdb5bf8ec933", "Relationships": [{"Type": "CHILD", "Ids": ["48b535c9-fb0f-437c-9d57-ba2af3e886ca"]}]}, {"BlockType": "LINE", "Confidence": 81.29683685302734, "Text": "29580 1b", "Geometry": {"BoundingBox": {"Width": 0.06902147829532623, "Height": 0.021497424*********, "Left": 0.743660032749176, "Top": 0.5223353505134583}, "Polygon": [{"X": 0.7436601519584656, "Y": 0.522335946559906}, {"X": 0.8126814961433411, "Y": 0.5223353505134583}, {"X": 0.8126813173294067, "Y": 0.5438321828842163}, {"X": 0.743660032749176, "Y": 0.5438327789306641}]}, "Id": "ec6f9b22-52fb-44c0-b4c6-0678555c476d", "Relationships": [{"Type": "CHILD", "Ids": ["29e0419e-87fc-45e5-95d4-f6708db7170a", "f2ca0314-7c50-4768-a42d-e40a5646d2f6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "COMPANY", "Geometry": {"BoundingBox": {"Width": 0.06463994085788727, "Height": 0.017091646790504456, "Left": 0.0831184834241867, "Top": 0.5529906153678894}, "Polygon": [{"X": 0.0831184834241867, "Y": 0.5529911518096924}, {"X": 0.14775827527046204, "Y": 0.5529906153678894}, {"X": 0.14775842428207397, "Y": 0.5700817108154297}, {"X": 0.08311864733695984, "Y": 0.5700822472572327}]}, "Id": "d201fd5b-42bb-4b25-8c84-369111522152", "Relationships": [{"Type": "CHILD", "Ids": ["04efda53-1023-4154-b0db-d833da8a67eb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "NET", "Geometry": {"BoundingBox": {"Width": 0.02612069994211197, "Height": 0.02123800851404667, "Left": 0.6625823974609375, "Top": 0.5493202805519104}, "Polygon": [{"X": 0.6625824570655823, "Y": 0.5493205189704895}, {"X": 0.6887030601501465, "Y": 0.5493202805519104}, {"X": 0.6887030005455017, "Y": 0.5705580711364746}, {"X": 0.6625823974609375, "Y": 0.5705583095550537}]}, "Id": "0a3660f2-51b0-476b-b95c-f5c08993ec65", "Relationships": [{"Type": "CHILD", "Ids": ["9a7787d2-208c-476f-8216-dfa19c778224"]}]}, {"BlockType": "LINE", "Confidence": 91.9112777709961, "Text": "30600 1b", "Geometry": {"BoundingBox": {"Width": 0.07066726684570312, "Height": 0.021801279857754707, "Left": 0.7421797513961792, "Top": 0.5491365790367126}, "Polygon": [{"X": 0.7421798706054688, "Y": 0.5491371750831604}, {"X": 0.8128470182418823, "Y": 0.5491365790367126}, {"X": 0.8128468990325928, "Y": 0.5709372758865356}, {"X": 0.7421797513961792, "Y": 0.5709378123283386}]}, "Id": "7c42ef0b-7265-4c52-a34a-e451f9bf52dc", "Relationships": [{"Type": "CHILD", "Ids": ["15e70a2d-f5e9-4125-97c3-e45ef76df1ad", "ed5436d5-d5ea-48b4-9ace-d27439278c12"]}]}, {"BlockType": "LINE", "Confidence": 99.64059448242188, "Text": "TRUCK # 026", "Geometry": {"BoundingBox": {"Width": 0.1300773322582245, "Height": 0.03612230345606804, "Left": 0.0827522873878479, "Top": 0.5962823033332825}, "Polygon": [{"X": 0.0827522873878479, "Y": 0.5962833762168884}, {"X": 0.2128293812274933, "Y": 0.5962823033332825}, {"X": 0.2128296196460724, "Y": 0.6324036121368408}, {"X": 0.08275263756513596, "Y": 0.632404625415802}]}, "Id": "8f12a570-0d4e-43ce-b94f-4cd30e360d7d", "Relationships": [{"Type": "CHILD", "Ids": ["e2a1f71f-3838-4c8a-afba-b1ae4eddf725", "59dc7ad2-c150-450f-9dc8-ac80ffcf5975", "a60f9336-8934-437b-8ccd-f2ba879560a4"]}]}, {"BlockType": "LINE", "Confidence": 99.57410430908203, "Text": "09/04/2025 11:18AM", "Geometry": {"BoundingBox": {"Width": 0.15926772356033325, "Height": 0.02236035466194153, "Left": 0.6635473370552063, "Top": 0.6006414294242859}, "Polygon": [{"X": 0.6635473966598511, "Y": 0.6006426811218262}, {"X": 0.8228150606155396, "Y": 0.6006414294242859}, {"X": 0.8228148818016052, "Y": 0.6230005025863647}, {"X": 0.6635473370552063, "Y": 0.623001754283905}]}, "Id": "7a878d1b-0057-4a91-a36d-75678eead012", "Relationships": [{"Type": "CHILD", "Ids": ["1c9a4dfb-1bdc-4756-8bbc-db7780e620fa", "8b805bb6-3f77-4d8d-a591-5e5125a38d77"]}]}, {"BlockType": "LINE", "Confidence": 99.66690826416016, "Text": "TRAILER # 2435", "Geometry": {"BoundingBox": {"Width": 0.14458173513412476, "Height": 0.03575264662504196, "Left": 0.08178097009658813, "Top": 0.6597878932952881}, "Polygon": [{"X": 0.08178097009658813, "Y": 0.6597890257835388}, {"X": 0.22636249661445618, "Y": 0.6597878932952881}, {"X": 0.22636272013187408, "Y": 0.6955394744873047}, {"X": 0.08178132027387619, "Y": 0.6955405473709106}]}, "Id": "e19dd942-025f-4939-ad0d-52b84b11710c", "Relationships": [{"Type": "CHILD", "Ids": ["59c26dc0-4cac-450e-9a82-eb93391baf08", "1c4188fe-4c69-4061-9938-6e082d90a892", "c6bd390c-ffb9-47d1-8ee4-538131e00fab"]}]}, {"BlockType": "LINE", "Confidence": 99.638671875, "Text": "RELEASE #", "Geometry": {"BoundingBox": {"Width": 0.07101314514875412, "Height": 0.018450187519192696, "Left": 0.0818968266248703, "Top": 0.7382897138595581}, "Polygon": [{"X": 0.0818968266248703, "Y": 0.7382902503013611}, {"X": 0.15290983021259308, "Y": 0.7382897138595581}, {"X": 0.15290997922420502, "Y": 0.7567394375801086}, {"X": 0.08189700543880463, "Y": 0.7567399144172668}]}, "Id": "cc5158d5-f898-48ff-b0ec-33560d75e13d", "Relationships": [{"Type": "CHILD", "Ids": ["970d1ec0-f88d-489b-ba74-e3a0f1be75ba", "6e0f52f6-7b9f-4815-bf35-0c4100e4fb80"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRIVER", "Geometry": {"BoundingBox": {"Width": 0.04842720553278923, "Height": 0.017517179250717163, "Left": 0.08027521520853043, "Top": 0.8014766573905945}, "Polygon": [{"X": 0.08027521520853043, "Y": 0.8014769554138184}, {"X": 0.12870226800441742, "Y": 0.8014766573905945}, {"X": 0.12870241701602936, "Y": 0.8189935088157654}, {"X": 0.08027538657188416, "Y": 0.8189938068389893}]}, "Id": "3bd84d3d-d8c3-46e5-becb-65d064059ee3", "Relationships": [{"Type": "CHILD", "Ids": ["d6ee558d-b0db-4a17-ba9a-2fe24d54a880"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "ON", "Geometry": {"BoundingBox": {"Width": 0.01833987608551979, "Height": 0.016976920887827873, "Left": 0.16106098890304565, "Top": 0.8004869222640991}, "Polygon": [{"X": 0.16106098890304565, "Y": 0.8004870414733887}, {"X": 0.1794007420539856, "Y": 0.8004869222640991}, {"X": 0.17940087616443634, "Y": 0.8174636960029602}, {"X": 0.1610611230134964, "Y": 0.8174638152122498}]}, "Id": "01b992df-d199-4b67-a184-2e516a4c71b0", "Relationships": [{"Type": "CHILD", "Ids": ["af92926f-905a-4c9f-9f3b-d60450435969"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "OFF", "Geometry": {"BoundingBox": {"Width": 0.024782298132777214, "Height": 0.016880076378583908, "Left": 0.20802272856235504, "Top": 0.7995903491973877}, "Polygon": [{"X": 0.20802272856235504, "Y": 0.799590528011322}, {"X": 0.23280492424964905, "Y": 0.7995903491973877}, {"X": 0.2328050285577774, "Y": 0.8164702653884888}, {"X": 0.2080228477716446, "Y": 0.8164704442024231}]}, "Id": "db80dc5e-e7ac-4a64-aa09-d3b92cf0ac87", "Relationships": [{"Type": "CHILD", "Ids": ["27723438-8437-40c7-83d4-cb31d8c20bd6"]}]}, {"BlockType": "LINE", "Confidence": 99.3212890625, "Text": "SEAL #", "Geometry": {"BoundingBox": {"Width": 0.04512716829776764, "Height": 0.01836753822863102, "Left": 0.07902687042951584, "Top": 0.8628989458084106}, "Polygon": [{"X": 0.07902687042951584, "Y": 0.8628992438316345}, {"X": 0.12415388226509094, "Y": 0.8628989458084106}, {"X": 0.12415403872728348, "Y": 0.8812662363052368}, {"X": 0.07902704924345016, "Y": 0.8812664747238159}]}, "Id": "384330c2-df09-45ba-886a-8303a2403716", "Relationships": [{"Type": "CHILD", "Ids": ["3634914d-5e2c-4fe8-8ce4-e760a1296da7", "c62fe3d7-14d8-4498-bfa8-e8ee18f92f1a"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018967419862747192, "Height": 0.01908302865922451, "Left": 0.538722813129425, "Top": 0.06924674659967422}, "Polygon": [{"X": 0.5387228727340698, "Y": 0.06924697011709213}, {"X": 0.5576902627944946, "Y": 0.06924674659967422}, {"X": 0.5576902031898499, "Y": 0.08832956105470657}, {"X": 0.538722813129425, "Y": 0.08832977712154388}]}, "Id": "f04dce72-5e57-4ff3-aba9-13534aedec09"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quincy", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12330450117588043, "Height": 0.04742755740880966, "Left": 0.14421379566192627, "Top": 0.10360948741436005}, "Polygon": [{"X": 0.14421379566192627, "Y": 0.10361088067293167}, {"X": 0.2675180435180664, "Y": 0.10360948741436005}, {"X": 0.2675183117389679, "Y": 0.15103568136692047}, {"X": 0.1442141830921173, "Y": 0.1510370522737503}]}, "Id": "0985de4b-ccbc-4cfb-8d88-681c96c5b25f"}, {"BlockType": "WORD", "Confidence": 98.68463134765625, "Text": "RECYCLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09603939205408096, "Height": 0.015556512400507927, "Left": 0.15010212361812592, "Top": 0.15007789433002472}, "Polygon": [{"X": 0.15010212361812592, "Y": 0.15007895231246948}, {"X": 0.24614141881465912, "Y": 0.15007789433002472}, {"X": 0.24614152312278748, "Y": 0.16563335061073303}, {"X": 0.15010225772857666, "Y": 0.1656344085931778}]}, "Id": "49b2b2d8-1ad2-4e72-954a-91b0677032ee"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ELECTRONIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1716722548007965, "Height": 0.0291511919349432, "Left": 0.08056755363941193, "Top": 0.18185274302959442}, "Polygon": [{"X": 0.08056755363941193, "Y": 0.18185459077358246}, {"X": 0.2522396445274353, "Y": 0.18185274302959442}, {"X": 0.25223982334136963, "Y": 0.21100211143493652}, {"X": 0.08056783676147461, "Y": 0.21100392937660217}]}, "Id": "e76a8cd2-eb92-45fc-afbd-6f8a0a273d45"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08368564397096634, "Height": 0.028646042570471764, "Left": 0.26122137904167175, "Top": 0.18162062764167786}, "Polygon": [{"X": 0.26122137904167175, "Y": 0.18162153661251068}, {"X": 0.34490692615509033, "Y": 0.18162062764167786}, {"X": 0.3449070155620575, "Y": 0.21026578545570374}, {"X": 0.2612215280532837, "Y": 0.21026666462421417}]}, "Id": "07588b6a-f557-4452-984b-1e243418f1f2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TICKET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09328862279653549, "Height": 0.029714388772845268, "Left": 0.3522021174430847, "Top": 0.18123281002044678}, "Polygon": [{"X": 0.3522021174430847, "Y": 0.18123382329940796}, {"X": 0.4454907178878784, "Y": 0.18123281002044678}, {"X": 0.4454907476902008, "Y": 0.2109462171792984}, {"X": 0.3522022068500519, "Y": 0.2109472006559372}]}, "Id": "893535d6-e854-4195-a24e-2370ce7c7467"}, {"BlockType": "WORD", "Confidence": 60.13054275512695, "Text": "ID4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02343960665166378, "Height": 0.021165531128644943, "Left": 0.6646358370780945, "Top": 0.23285114765167236}, "Polygon": [{"X": 0.6646358966827393, "Y": 0.23285140097141266}, {"X": 0.6880754232406616, "Y": 0.23285114765167236}, {"X": 0.6880753636360168, "Y": 0.2540164291858673}, {"X": 0.6646358370780945, "Y": 0.2540166974067688}]}, "Id": "287f0aa1-d6c6-49a6-b2d4-6244d087709f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03444308787584305, "Height": 0.017727505415678024, "Left": 0.08247493952512741, "Top": 0.24582962691783905}, "Polygon": [{"X": 0.08247493952512741, "Y": 0.2458299845457077}, {"X": 0.11691787093877792, "Y": 0.24582962691783905}, {"X": 0.11691802740097046, "Y": 0.2635567784309387}, {"X": 0.08247511088848114, "Y": 0.2635571360588074}]}, "Id": "a3373e2f-77e8-4b0d-bf63-cee28822199d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0441555418074131, "Height": 0.021491484716534615, "Left": 0.6617772579193115, "Top": 0.2867138683795929}, "Polygon": [{"X": 0.6617773771286011, "Y": 0.2867143154144287}, {"X": 0.7059328556060791, "Y": 0.2867138683795929}, {"X": 0.7059327363967896, "Y": 0.30820491909980774}, {"X": 0.6617772579193115, "Y": 0.30820533633232117}]}, "Id": "5e1fa2a8-d546-4cff-adec-7a490cfdb484"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "60180", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04538433253765106, "Height": 0.02127273939549923, "Left": 0.7412632703781128, "Top": 0.2868461012840271}, "Polygon": [{"X": 0.7412633895874023, "Y": 0.2868465483188629}, {"X": 0.786647617816925, "Y": 0.2868461012840271}, {"X": 0.7866474986076355, "Y": 0.30811837315559387}, {"X": 0.7412632703781128, "Y": 0.3081188499927521}]}, "Id": "143de3fb-6804-4e8c-a740-4617a7b49a2b"}, {"BlockType": "WORD", "Confidence": 61.13759231567383, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015218589454889297, "Height": 0.021057073026895523, "Left": 0.7969700694084167, "Top": 0.28686627745628357}, "Polygon": [{"X": 0.7969701886177063, "Y": 0.2868664562702179}, {"X": 0.8121886253356934, "Y": 0.28686627745628357}, {"X": 0.8121885061264038, "Y": 0.30792319774627686}, {"X": 0.7969700694084167, "Y": 0.3079233765602112}]}, "Id": "f361c013-8d71-410b-8acb-1abeca17a630"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "INBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060575202107429504, "Height": 0.0213850699365139, "Left": 0.8237131834030151, "Top": 0.2865135967731476}, "Polygon": [{"X": 0.8237133026123047, "Y": 0.2865142226219177}, {"X": 0.8842883706092834, "Y": 0.2865135967731476}, {"X": 0.8842881917953491, "Y": 0.30789807438850403}, {"X": 0.8237131834030151, "Y": 0.3078986704349518}]}, "Id": "223d1253-c97d-471c-b603-a0c7f9f7a822"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "COMPANY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06590182334184647, "Height": 0.017093542963266373, "Left": 0.0825219452381134, "Top": 0.30718129873275757}, "Polygon": [{"X": 0.0825219452381134, "Y": 0.3071819543838501}, {"X": 0.14842364192008972, "Y": 0.30718129873275757}, {"X": 0.14842377603054047, "Y": 0.3242741823196411}, {"X": 0.08252211660146713, "Y": 0.32427483797073364}]}, "Id": "4aad23b7-6562-4017-86dc-f3815eecc4c8"}, {"BlockType": "WORD", "Confidence": 96.26016235351562, "Text": "BLOOMINGTON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009709554724395275, "Height": 0.1004626527428627, "Left": 0.061839863657951355, "Top": 0.6886385679244995}, "Polygon": [{"X": 0.06184088811278343, "Y": 0.789101243019104}, {"X": 0.061839863657951355, "Y": 0.6886386871337891}, {"X": 0.07154841721057892, "Y": 0.6886385679244995}, {"X": 0.0715494155883789, "Y": 0.7891011834144592}]}, "Id": "87432d2a-e0f3-4442-9d55-3cee1f35494e"}, {"BlockType": "WORD", "Confidence": 97.37483978271484, "Text": "OFFSET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008918877691030502, "Height": 0.05263913422822952, "Left": 0.06331271678209305, "Top": 0.6329457759857178}, "Polygon": [{"X": 0.06331325322389603, "Y": 0.6855849027633667}, {"X": 0.06331271678209305, "Y": 0.6329458355903625}, {"X": 0.07223106920719147, "Y": 0.6329457759857178}, {"X": 0.07223159819841385, "Y": 0.6855848431587219}]}, "Id": "4ad2b3b5-b6f2-457e-a894-93250959d601"}, {"BlockType": "WORD", "Confidence": 99.37420654296875, "Text": "PROCESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009310329332947731, "Height": 0.06541983038187027, "Left": 0.06346362829208374, "Top": 0.5637057423591614}, "Polygon": [{"X": 0.06346429139375687, "Y": 0.6291255950927734}, {"X": 0.06346362829208374, "Y": 0.5637058615684509}, {"X": 0.07277330756187439, "Y": 0.5637057423591614}, {"X": 0.07277395576238632, "Y": 0.6291255354881287}]}, "Id": "75be5056-3b5e-4008-ad37-eeb84dfae546"}, {"BlockType": "WORD", "Confidence": 63.735050201416016, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008470118045806885, "Height": 0.02460726536810398, "Left": 0.06405004113912582, "Top": 0.5353220701217651}, "Polygon": [{"X": 0.06405028700828552, "Y": 0.559929370880127}, {"X": 0.06405004113912582, "Y": 0.5353221893310547}, {"X": 0.07251991331577301, "Y": 0.5353220701217651}, {"X": 0.07252015918493271, "Y": 0.5599293112754822}]}, "Id": "7737106e-eca2-41a8-8d7c-03c278a0796e"}, {"BlockType": "WORD", "Confidence": 77.25984191894531, "Text": "BL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008572131395339966, "Height": 0.01369546726346016, "Left": 0.06406809389591217, "Top": 0.5062434077262878}, "Polygon": [{"X": 0.06406823545694351, "Y": 0.5199388861656189}, {"X": 0.06406809389591217, "Y": 0.5062434673309326}, {"X": 0.07264009118080139, "Y": 0.5062434077262878}, {"X": 0.07264022529125214, "Y": 0.5199388265609741}]}, "Id": "8422ff5c-e719-4727-a9e2-346052df8f98"}, {"BlockType": "WORD", "Confidence": 64.94558715820312, "Text": "DOMINGTON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011020279489457607, "Height": 0.08417176455259323, "Left": 0.0634375661611557, "Top": 0.420576274394989}, "Polygon": [{"X": 0.06343842297792435, "Y": 0.5047480463981628}, {"X": 0.0634375661611557, "Y": 0.42057639360427856}, {"X": 0.07445701956748962, "Y": 0.420576274394989}, {"X": 0.07445784658193588, "Y": 0.5047479271888733}]}, "Id": "82bc95e8-4981-475b-ba85-6d84add07e29"}, {"BlockType": "WORD", "Confidence": 67.69271850585938, "Text": "n.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008621121756732464, "Height": 0.01101189386099577, "Left": 0.06417479366064072, "Top": 0.4066143035888672}, "Polygon": [{"X": 0.06417490541934967, "Y": 0.41762620210647583}, {"X": 0.06417479366064072, "Y": 0.40661439299583435}, {"X": 0.0727958083152771, "Y": 0.4066143035888672}, {"X": 0.07279592007398605, "Y": 0.41762614250183105}]}, "Id": "0c6438c4-5609-4255-9174-74f4982074c5"}, {"BlockType": "WORD", "Confidence": 94.25163269042969, "Text": "61702", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008736998774111271, "Height": 0.03804825246334076, "Left": 0.06359684467315674, "Top": 0.3657069802284241}, "Polygon": [{"X": 0.06359723210334778, "Y": 0.40375521779060364}, {"X": 0.06359684467315674, "Y": 0.36570706963539124}, {"X": 0.07233346998691559, "Y": 0.3657069802284241}, {"X": 0.07233384251594543, "Y": 0.40375515818595886}]}, "Id": "20757bda-6110-4fc0-9073-e6467333e6d0"}, {"BlockType": "WORD", "Confidence": 89.52347564697266, "Text": "59924", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008589291013777256, "Height": 0.037294652312994, "Left": 0.0634133443236351, "Top": 0.31751543283462524}, "Polygon": [{"X": 0.06341372430324554, "Y": 0.35481008887290955}, {"X": 0.0634133443236351, "Y": 0.3175155222415924}, {"X": 0.07200226187705994, "Y": 0.31751543283462524}, {"X": 0.07200263440608978, "Y": 0.3548099994659424}]}, "Id": "e29f1f5a-f763-4315-83ac-20ee87595217"}, {"BlockType": "WORD", "Confidence": 90.12675476074219, "Text": "37273112-DAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010440616868436337, "Height": 0.0923895612359047, "Left": 0.062114838510751724, "Top": 0.21489308774471283}, "Polygon": [{"X": 0.062115781009197235, "Y": 0.3072826564311981}, {"X": 0.062114838510751724, "Y": 0.21489320695400238}, {"X": 0.07255453616380692, "Y": 0.21489308774471283}, {"X": 0.07255546003580093, "Y": 0.30728253722190857}]}, "Id": "24d8c873-bd18-4f74-9734-79c9a1f51845"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "NAME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0379115529358387, "Height": 0.017611879855394363, "Left": 0.0826258510351181, "Top": 0.33744269609451294}, "Polygon": [{"X": 0.0826258510351181, "Y": 0.3374430537223816}, {"X": 0.12053725123405457, "Y": 0.33744269609451294}, {"X": 0.1205374076962471, "Y": 0.35505419969558716}, {"X": 0.08262602239847183, "Y": 0.3550545573234558}]}, "Id": "fa5afd83-5799-4d1e-8b78-11af2f9fb471"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Plastic", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08250419795513153, "Height": 0.04157533124089241, "Left": 0.15947984158992767, "Top": 0.31567299365997314}, "Polygon": [{"X": 0.15947984158992767, "Y": 0.3156737983226776}, {"X": 0.2419838011264801, "Y": 0.31567299365997314}, {"X": 0.2419840395450592, "Y": 0.357247531414032}, {"X": 0.15948016941547394, "Y": 0.35724833607673645}]}, "Id": "663b7976-1ed5-4d61-850a-4647a630a6a3"}, {"BlockType": "WORD", "Confidence": 92.05815887451172, "Text": "Ingenuity", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.111542247235775, "Height": 0.061622925102710724, "Left": 0.2545340657234192, "Top": 0.3225023150444031}, "Polygon": [{"X": 0.2545340657234192, "Y": 0.3225034177303314}, {"X": 0.36607611179351807, "Y": 0.3225023150444031}, {"X": 0.3660762906074524, "Y": 0.38412418961524963}, {"X": 0.25453439354896545, "Y": 0.3841252326965332}]}, "Id": "fbe37555-e588-4370-8e6f-bf87da2282f6"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "09/04/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08970598131418228, "Height": 0.021400609984993935, "Left": 0.6627776622772217, "Top": 0.33926406502723694}, "Polygon": [{"X": 0.6627777218818665, "Y": 0.3392649292945862}, {"X": 0.752483606338501, "Y": 0.33926406502723694}, {"X": 0.7524834871292114, "Y": 0.3606638014316559}, {"X": 0.6627776622772217, "Y": 0.3606646656990051}]}, "Id": "674c21d0-6b86-4f5b-a33d-b199acecfd24"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "09:57AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06248831748962402, "Height": 0.02093462273478508, "Left": 0.7605025768280029, "Top": 0.33939000964164734}, "Polygon": [{"X": 0.7605026960372925, "Y": 0.3393906354904175}, {"X": 0.822990894317627, "Y": 0.33939000964164734}, {"X": 0.8229907751083374, "Y": 0.36032402515411377}, {"X": 0.7605025768280029, "Y": 0.3603246510028839}]}, "Id": "9d6beb4a-5500-4dcd-9208-7e588f6f97f9"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "RIMAS#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04912838712334633, "Height": 0.017279261723160744, "Left": 0.08313275128602982, "Top": 0.3991844654083252}, "Polygon": [{"X": 0.08313275128602982, "Y": 0.3991849422454834}, {"X": 0.1322609931230545, "Y": 0.3991844654083252}, {"X": 0.13226114213466644, "Y": 0.41646328568458557}, {"X": 0.08313291519880295, "Y": 0.4164637327194214}]}, "Id": "df84c020-af1f-4b31-8990-432a353ad691"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "1892858", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11805611103773117, "Height": 0.033073727041482925, "Left": 0.1890472173690796, "Top": 0.3867896497249603}, "Polygon": [{"X": 0.1890472173690796, "Y": 0.38679075241088867}, {"X": 0.3071031868457794, "Y": 0.3867896497249603}, {"X": 0.30710333585739136, "Y": 0.4198622703552246}, {"X": 0.1890474557876587, "Y": 0.41986337304115295}]}, "Id": "d38ac475-1180-45fc-907c-b139e99ba99f"}, {"BlockType": "WORD", "Confidence": 98.69180297851562, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014361937530338764, "Height": 0.02114447019994259, "Left": 0.6562516093254089, "Top": 0.44292327761650085}, "Polygon": [{"X": 0.6562516689300537, "Y": 0.4429233968257904}, {"X": 0.6706135272979736, "Y": 0.44292327761650085}, {"X": 0.6706134676933289, "Y": 0.46406760811805725}, {"X": 0.6562516093254089, "Y": 0.4640677571296692}]}, "Id": "96c74056-a924-4563-a9cb-40a1f24d0813"}, {"BlockType": "WORD", "Confidence": 99.69647216796875, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008753139525651932, "Height": 0.020019102841615677, "Left": 0.6807593107223511, "Top": 0.44351470470428467}, "Polygon": [{"X": 0.6807593703269958, "Y": 0.44351479411125183}, {"X": 0.6895124316215515, "Y": 0.44351470470428467}, {"X": 0.6895123720169067, "Y": 0.4635337293148041}, {"X": 0.6807593107223511, "Y": 0.46353381872177124}]}, "Id": "58a88a21-3f20-4763-b816-d38a8c36df8a"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "COMMODITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07999587059020996, "Height": 0.017448028549551964, "Left": 0.08290845900774002, "Top": 0.4604684114456177}, "Polygon": [{"X": 0.08290845900774002, "Y": 0.460469126701355}, {"X": 0.16290420293807983, "Y": 0.4604684114456177}, {"X": 0.16290433704853058, "Y": 0.4779157340526581}, {"X": 0.08290863037109375, "Y": 0.477916419506073}]}, "Id": "4616a966-8ab2-4bda-b995-a5876b1885ee"}, {"BlockType": "WORD", "Confidence": 96.66234588623047, "Text": "B", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008700113743543625, "Height": 0.01692778244614601, "Left": 0.48729366064071655, "Top": 0.46145206689834595}, "Polygon": [{"X": 0.48729366064071655, "Y": 0.4614521563053131}, {"X": 0.4959937632083893, "Y": 0.46145206689834595}, {"X": 0.49599379301071167, "Y": 0.4783797860145569}, {"X": 0.48729366064071655, "Y": 0.47837984561920166}]}, "Id": "cff2e4d2-c97f-4024-99f5-4f1a1fedc334"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044641293585300446, "Height": 0.0213495884090662, "Left": 0.661774218082428, "Top": 0.4962230920791626}, "Polygon": [{"X": 0.6617743372917175, "Y": 0.49622347950935364}, {"X": 0.7064155340194702, "Y": 0.4962230920791626}, {"X": 0.7064154148101807, "Y": 0.517572283744812}, {"X": 0.661774218082428, "Y": 0.5175727009773254}]}, "Id": "583975b2-f98a-474f-acb3-9fe5a64d8448"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "60180", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04396059736609459, "Height": 0.020961027592420578, "Left": 0.7419872879981995, "Top": 0.49664467573165894}, "Polygon": [{"X": 0.741987407207489, "Y": 0.49664506316185}, {"X": 0.7859479188919067, "Y": 0.49664467573165894}, {"X": 0.7859477400779724, "Y": 0.5176053047180176}, {"X": 0.7419872879981995, "Y": 0.517605721950531}]}, "Id": "12273af6-508b-4a30-b3e7-ef816e474486"}, {"BlockType": "WORD", "Confidence": 68.16266632080078, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01587410271167755, "Height": 0.021499885246157646, "Left": 0.7973678112030029, "Top": 0.49645358324050903}, "Polygon": [{"X": 0.7973679900169373, "Y": 0.4964537024497986}, {"X": 0.8132418990135193, "Y": 0.49645358324050903}, {"X": 0.8132417798042297, "Y": 0.5179533362388611}, {"X": 0.7973678112030029, "Y": 0.5179534554481506}]}, "Id": "0ed1dd17-53a6-4e25-a309-14fccc07a576"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "RECALLED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07208454608917236, "Height": 0.021297655999660492, "Left": 0.8217576742172241, "Top": 0.4971000552177429}, "Polygon": [{"X": 0.8217578530311584, "Y": 0.49710068106651306}, {"X": 0.8938422203063965, "Y": 0.4971000552177429}, {"X": 0.8938420414924622, "Y": 0.5183970928192139}, {"X": 0.8217576742172241, "Y": 0.5183976888656616}]}, "Id": "022cbccf-8ff0-4b59-8f35-e10c1dffb037"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRUCKING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06788383424282074, "Height": 0.01697067730128765, "Left": 0.08264128863811493, "Top": 0.5222025513648987}, "Polygon": [{"X": 0.08264128863811493, "Y": 0.5222030878067017}, {"X": 0.15052497386932373, "Y": 0.5222025513648987}, {"X": 0.15052512288093567, "Y": 0.5391726493835449}, {"X": 0.08264145255088806, "Y": 0.5391731858253479}]}, "Id": "cb5cbbf4-c388-4fd6-8948-e0e6eac9dc98"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "TARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033814139664173126, "Height": 0.020649179816246033, "Left": 0.6638299822807312, "Top": 0.5225731730461121}, "Polygon": [{"X": 0.6638301014900208, "Y": 0.5225734710693359}, {"X": 0.6976441144943237, "Y": 0.5225731730461121}, {"X": 0.697644054889679, "Y": 0.5432220697402954}, {"X": 0.6638299822807312, "Y": 0.5432223081588745}]}, "Id": "48b535c9-fb0f-437c-9d57-ba2af3e886ca"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "29580", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04378245398402214, "Height": 0.0214972086250782, "Left": 0.743660032749176, "Top": 0.5223355889320374}, "Polygon": [{"X": 0.7436601519584656, "Y": 0.522335946559906}, {"X": 0.7874424457550049, "Y": 0.5223355889320374}, {"X": 0.7874423265457153, "Y": 0.5438324213027954}, {"X": 0.743660032749176, "Y": 0.5438327789306641}]}, "Id": "29e0419e-87fc-45e5-95d4-f6708db7170a"}, {"BlockType": "WORD", "Confidence": 62.86212921142578, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014441953040659428, "Height": 0.021454738453030586, "Left": 0.798239529132843, "Top": 0.5223546624183655}, "Polygon": [{"X": 0.7982397079467773, "Y": 0.522354781627655}, {"X": 0.8126814961433411, "Y": 0.5223546624183655}, {"X": 0.8126813173294067, "Y": 0.5438092350959778}, {"X": 0.798239529132843, "Y": 0.5438093543052673}]}, "Id": "f2ca0314-7c50-4768-a42d-e40a5646d2f6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "COMPANY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06463994085788727, "Height": 0.017091646790504456, "Left": 0.0831184834241867, "Top": 0.5529906153678894}, "Polygon": [{"X": 0.0831184834241867, "Y": 0.5529911518096924}, {"X": 0.14775827527046204, "Y": 0.5529906153678894}, {"X": 0.14775842428207397, "Y": 0.5700817108154297}, {"X": 0.08311864733695984, "Y": 0.5700822472572327}]}, "Id": "04efda53-1023-4154-b0db-d833da8a67eb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02612069994211197, "Height": 0.02123800851404667, "Left": 0.6625823974609375, "Top": 0.5493202805519104}, "Polygon": [{"X": 0.6625824570655823, "Y": 0.5493205189704895}, {"X": 0.6887030601501465, "Y": 0.5493202805519104}, {"X": 0.6887030005455017, "Y": 0.5705580711364746}, {"X": 0.6625823974609375, "Y": 0.5705583095550537}]}, "Id": "9a7787d2-208c-476f-8216-dfa19c778224"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "30600", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04386334866285324, "Height": 0.02173515222966671, "Left": 0.7421797513961792, "Top": 0.549136757850647}, "Polygon": [{"X": 0.7421798706054688, "Y": 0.5491371750831604}, {"X": 0.786043107509613, "Y": 0.549136757850647}, {"X": 0.7860429883003235, "Y": 0.5708715915679932}, {"X": 0.7421797513961792, "Y": 0.5708719491958618}]}, "Id": "15e70a2d-f5e9-4125-97c3-e45ef76df1ad"}, {"BlockType": "WORD", "Confidence": 84.03081512451172, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015725839883089066, "Height": 0.021758543327450752, "Left": 0.7971211671829224, "Top": 0.5491788387298584}, "Polygon": [{"X": 0.7971213459968567, "Y": 0.549178957939148}, {"X": 0.8128470182418823, "Y": 0.5491788387298584}, {"X": 0.8128468990325928, "Y": 0.5709372758865356}, {"X": 0.7971211671829224, "Y": 0.5709373950958252}]}, "Id": "ed5436d5-d5ea-48b4-9ace-d27439278c12"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "TRUCK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04454620182514191, "Height": 0.01741913892328739, "Left": 0.08275246620178223, "Top": 0.614985466003418}, "Polygon": [{"X": 0.08275246620178223, "Y": 0.6149858236312866}, {"X": 0.1272985190153122, "Y": 0.614985466003418}, {"X": 0.12729866802692413, "Y": 0.6324042677879333}, {"X": 0.08275263756513596, "Y": 0.632404625415802}]}, "Id": "e2a1f71f-3838-4c8a-afba-b1ae4eddf725"}, {"BlockType": "WORD", "Confidence": 99.22772216796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0077025932259857655, "Height": 0.01824304461479187, "Left": 0.1309896856546402, "Top": 0.6140062212944031}, "Polygon": [{"X": 0.1309896856546402, "Y": 0.6140062808990479}, {"X": 0.13869212567806244, "Y": 0.6140062212944031}, {"X": 0.13869227468967438, "Y": 0.6322492361068726}, {"X": 0.13098984956741333, "Y": 0.6322492957115173}]}, "Id": "59dc7ad2-c150-450f-9dc8-ac80ffcf5975"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "026", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04344183951616287, "Height": 0.034464430063962936, "Left": 0.16938777267932892, "Top": 0.5962823033332825}, "Polygon": [{"X": 0.16938777267932892, "Y": 0.5962826609611511}, {"X": 0.2128293812274933, "Y": 0.5962823033332825}, {"X": 0.2128296047449112, "Y": 0.6307464241981506}, {"X": 0.1693880409002304, "Y": 0.6307467222213745}]}, "Id": "a60f9336-8934-437b-8ccd-f2ba879560a4"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "09/04/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08902787417173386, "Height": 0.021967710927128792, "Left": 0.6635473370552063, "Top": 0.6006419658660889}, "Polygon": [{"X": 0.6635473966598511, "Y": 0.6006426811218262}, {"X": 0.7525752186775208, "Y": 0.6006419658660889}, {"X": 0.7525750994682312, "Y": 0.62260901927948}, {"X": 0.6635473370552063, "Y": 0.6226096749305725}]}, "Id": "1c9a4dfb-1bdc-4756-8bbc-db7780e620fa"}, {"BlockType": "WORD", "Confidence": 99.45651245117188, "Text": "11:18AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06080310791730881, "Height": 0.02133173868060112, "Left": 0.7620119452476501, "Top": 0.6016692519187927}, "Polygon": [{"X": 0.7620120644569397, "Y": 0.6016697287559509}, {"X": 0.8228150606155396, "Y": 0.6016692519187927}, {"X": 0.8228148818016052, "Y": 0.6230005025863647}, {"X": 0.7620119452476501, "Y": 0.623000979423523}]}, "Id": "8b805bb6-3f77-4d8d-a591-5e5125a38d77"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "TRAILER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055566009134054184, "Height": 0.017678387463092804, "Left": 0.08178114145994186, "Top": 0.677188515663147}, "Polygon": [{"X": 0.08178114145994186, "Y": 0.6771889328956604}, {"X": 0.1373469978570938, "Y": 0.677188515663147}, {"X": 0.13734714686870575, "Y": 0.6948664784431458}, {"X": 0.0817813128232956, "Y": 0.6948668956756592}]}, "Id": "59c26dc0-4cac-450e-9a82-eb93391baf08"}, {"BlockType": "WORD", "Confidence": 99.22772216796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007443005684763193, "Height": 0.017611265182495117, "Left": 0.1410481333732605, "Top": 0.6763334274291992}, "Polygon": [{"X": 0.1410481333732605, "Y": 0.676333487033844}, {"X": 0.1484909951686859, "Y": 0.6763334274291992}, {"X": 0.14849114418029785, "Y": 0.6939446330070496}, {"X": 0.14104828238487244, "Y": 0.6939446926116943}]}, "Id": "1c4188fe-4c69-4061-9938-6e082d90a892"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "2435", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.052430782467126846, "Height": 0.035751961171627045, "Left": 0.17393192648887634, "Top": 0.6597878932952881}, "Polygon": [{"X": 0.17393192648887634, "Y": 0.6597883105278015}, {"X": 0.22636249661445618, "Y": 0.6597878932952881}, {"X": 0.22636272013187408, "Y": 0.6955394744873047}, {"X": 0.17393220961093903, "Y": 0.6955398917198181}]}, "Id": "c6bd390c-ffb9-47d1-8ee4-538131e00fab"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "RELEASE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059427812695503235, "Height": 0.017536280676722527, "Left": 0.0818968340754509, "Top": 0.7392036318778992}, "Polygon": [{"X": 0.0818968340754509, "Y": 0.7392040491104126}, {"X": 0.1413245052099228, "Y": 0.7392036318778992}, {"X": 0.14132465422153473, "Y": 0.7567394971847534}, {"X": 0.08189700543880463, "Y": 0.7567399144172668}]}, "Id": "970d1ec0-f88d-489b-ba74-e3a0f1be75ba"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0074634673073887825, "Height": 0.017608100548386574, "Left": 0.14544649422168732, "Top": 0.7382897138595581}, "Polygon": [{"X": 0.14544649422168732, "Y": 0.7382897734642029}, {"X": 0.15290983021259308, "Y": 0.7382897138595581}, {"X": 0.15290996432304382, "Y": 0.7558977603912354}, {"X": 0.14544664323329926, "Y": 0.7558978199958801}]}, "Id": "6e0f52f6-7b9f-4815-bf35-0c4100e4fb80"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRIVER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04842720553278923, "Height": 0.017517179250717163, "Left": 0.08027521520853043, "Top": 0.8014766573905945}, "Polygon": [{"X": 0.08027521520853043, "Y": 0.8014769554138184}, {"X": 0.12870226800441742, "Y": 0.8014766573905945}, {"X": 0.12870241701602936, "Y": 0.8189935088157654}, {"X": 0.08027538657188416, "Y": 0.8189938068389893}]}, "Id": "d6ee558d-b0db-4a17-ba9a-2fe24d54a880"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01833987608551979, "Height": 0.016976920887827873, "Left": 0.16106098890304565, "Top": 0.8004869222640991}, "Polygon": [{"X": 0.16106098890304565, "Y": 0.8004870414733887}, {"X": 0.1794007420539856, "Y": 0.8004869222640991}, {"X": 0.17940087616443634, "Y": 0.8174636960029602}, {"X": 0.1610611230134964, "Y": 0.8174638152122498}]}, "Id": "af92926f-905a-4c9f-9f3b-d60450435969"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024782298132777214, "Height": 0.016880076378583908, "Left": 0.20802272856235504, "Top": 0.7995903491973877}, "Polygon": [{"X": 0.20802272856235504, "Y": 0.799590528011322}, {"X": 0.23280492424964905, "Y": 0.7995903491973877}, {"X": 0.2328050285577774, "Y": 0.8164702653884888}, {"X": 0.2080228477716446, "Y": 0.8164704442024231}]}, "Id": "27723438-8437-40c7-83d4-cb31d8c20bd6"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "SEAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03465325012803078, "Height": 0.017342299222946167, "Left": 0.07902688533067703, "Top": 0.8639242053031921}, "Polygon": [{"X": 0.07902688533067703, "Y": 0.8639243841171265}, {"X": 0.11367997527122498, "Y": 0.8639242053031921}, {"X": 0.11368013173341751, "Y": 0.8812662959098816}, {"X": 0.07902704924345016, "Y": 0.8812664747238159}]}, "Id": "3634914d-5e2c-4fe8-8ce4-e760a1296da7"}, {"BlockType": "WORD", "Confidence": 98.75, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007659516762942076, "Height": 0.017943376675248146, "Left": 0.11649452149868011, "Top": 0.8628989458084106}, "Polygon": [{"X": 0.11649452149868011, "Y": 0.8628990054130554}, {"X": 0.12415388226509094, "Y": 0.8628989458084106}, {"X": 0.12415403872728348, "Y": 0.8808422684669495}, {"X": 0.11649467796087265, "Y": 0.8808423280715942}]}, "Id": "c62fe3d7-14d8-4498-bfa8-e8ee18f92f1a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "6d7d7402-793f-4068-8023-254f768d5c42", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "6d7d7402-793f-4068-8023-254f768d5c42", "content-type": "application/x-amz-json-1.1", "content-length": "49512", "date": "Wed, 24 Sep 2025 12:46:10 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a8943259-35dd-408e-a563-b529d3f862a7", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:14 GMT", "content-type": "application/json", "content-length": "636", "connection": "keep-alive", "x-amzn-requestid": "a8943259-35dd-408e-a563-b529d3f862a7"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. The page content: \"ELECTRONIC SCALE TICKET\" and weight information: GROSS, TARE, NET, dates, truck, trailer, seal. So that matches scale_ticket definition. We should output object with page_no 1 and doc_type \"scale_ticket\". Use tool call classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2698, "outputTokens": 94, "totalTokens": 2792}, "metrics": {"latencyMs": 640}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/6d58c97b_A8UNJQR3VGLF8G4MC11J.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n22\nQuincy\nRECYCLE\nELECTRONIC SCALE TICKET\nID4\nDATE\nGROSS 60180 1b INBOUND\nCOMPANY\nBLOOMINGTON OFFSET PROCESS INC. BL DOMINGTON n. 61702 59924 37273112-DAR\nNAME\nPlastic Ingenuity\n09/04/2025 09:57AM\nRIMAS#\n1892858\nID 4\nCOMMODITY\nB\nGROSS\n60180 1b RECALLED\nTRUCKING\nTARE\n29580 1b\nCOMPANY\nNET\n30600 1b\nTRUCK # 026\n09/04/2025 11:18AM\nTRAILER # 2435\nRELEASE #\nDRIVER\nON\nOFF\nSEAL #\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}