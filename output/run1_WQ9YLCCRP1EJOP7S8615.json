{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "07c7b266-dfe3-4f33-bec2-3ac809ad995a", "Relationships": [{"Type": "CHILD", "Ids": ["443b54bf-49bf-460d-830f-3e5c553ad069", "d5cd3734-36a2-4cda-8ce1-8399ac48bf3d", "9a960996-3364-461e-a0fc-709ef3e0cb9c", "ef465d2a-7646-4945-a1f8-0cac18ca9712", "4453bb4f-24a6-4c83-8d54-1e5b5a9c62ce", "eb45cac0-d331-4b79-b439-8f8d12e9b3fa", "2ee93a83-559b-4403-b1d4-0172a790cf39", "fc4b078d-1ee3-4c6b-85d6-c189b90a9032", "811a423c-da50-4fd7-b73c-dcbfb5ffe5ae", "7104d99f-4a80-4f7c-81c4-34b1296c21f8", "f6f8f9e1-e742-4069-9764-dd88193725f7", "98d85ef7-e7c6-47b3-9b4e-b417ee92f4f8", "16bfbf36-7843-408e-8b17-662fe83aa7a2", "31c6acd7-425e-4416-a6e1-0ed6d8010e43", "d428a75e-becf-4519-b470-43c00432176b", "16b35257-65d8-4c06-8bb6-b05a6849295e", "d3b3cca8-a928-46af-9860-753f1d44975a", "c036985f-9b69-4571-b7b2-033f6b828f75", "9ddcdfc5-624c-4898-a5a6-41346ecdc105", "387ca3b0-34cc-472c-9311-6f3c9f4a5d61", "61987f2a-f18f-4a74-9df8-eb5b3f17585a", "608adb8e-7071-42dc-a10e-249aba402dbc", "0fb119fe-06d8-4e73-8e70-508cf27b62c6", "6fa4054c-f578-447a-9f46-27566196bc7c"]}]}, {"BlockType": "LINE", "Confidence": 86.16895294189453, "Text": "8/29/25, 8:44 AM", "Geometry": {"BoundingBox": {"Width": 0.07431413978338242, "Height": 0.011384090408682823, "Left": 0.02310476265847683, "Top": 0.01561495941132307}, "Polygon": [{"X": 0.023104770109057426, "Y": 0.01561495941132307}, {"X": 0.0974189043045044, "Y": 0.015616017393767834}, {"X": 0.0974188968539238, "Y": 0.02699904888868332}, {"X": 0.02310476265847683, "Y": 0.026998033747076988}]}, "Id": "443b54bf-49bf-460d-830f-3e5c553ad069", "Relationships": [{"Type": "CHILD", "Ids": ["67c5d3c4-93ef-4a98-8f19-4007d19f5b1c", "9225b6b0-9121-4d60-a74d-76547b3dfb83", "a38fa6e9-2f09-4b5f-a9cc-58ba454e0bac"]}]}, {"BlockType": "LINE", "Confidence": 96.11905670166016, "Text": "J1", "Geometry": {"BoundingBox": {"Width": 0.010329744778573513, "Height": 0.010095990262925625, "Left": 0.5571965575218201, "Top": 0.015727343037724495}, "Polygon": [{"X": 0.5571966171264648, "Y": 0.015727343037724495}, {"X": 0.5675263404846191, "Y": 0.015727490186691284}, {"X": 0.5675262808799744, "Y": 0.025823332369327545}, {"X": 0.5571965575218201, "Y": 0.025823190808296204}]}, "Id": "d5cd3734-36a2-4cda-8ce1-8399ac48bf3d", "Relationships": [{"Type": "CHILD", "Ids": ["3ec13c9e-0ca9-470d-aec9-dcd96b547517"]}]}, {"BlockType": "LINE", "Confidence": 94.67733764648438, "Text": "Con Global", "Geometry": {"BoundingBox": {"Width": 0.6781650185585022, "Height": 0.11373972147703171, "Left": 0.2713283598423004, "Top": 0.06493331491947174}, "Polygon": [{"X": 0.27132850885391235, "Y": 0.06493331491947174}, {"X": 0.9494933485984802, "Y": 0.06494130194187164}, {"X": 0.9494929313659668, "Y": 0.17867304384708405}, {"X": 0.2713283598423004, "Y": 0.17866890132427216}]}, "Id": "9a960996-3364-461e-a0fc-709ef3e0cb9c", "Relationships": [{"Type": "CHILD", "Ids": ["c649981b-d635-4eb6-8a90-1f01b7136392", "7bbcbe5f-00da-4bef-8960-39e631135897"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "TCNU1638696", "Geometry": {"BoundingBox": {"Width": 0.16356204450130463, "Height": 0.024553902447223663, "Left": 0.42639920115470886, "Top": 0.2439866065979004}, "Polygon": [{"X": 0.42639926075935364, "Y": 0.2439866065979004}, {"X": 0.5899612307548523, "Y": 0.2439870685338974}, {"X": 0.5899611711502075, "Y": 0.26854050159454346}, {"X": 0.42639920115470886, "Y": 0.26854023337364197}]}, "Id": "ef465d2a-7646-4945-a1f8-0cac18ca9712", "Relationships": [{"Type": "CHILD", "Ids": ["bee78306-755c-4e97-bc32-222ed93b1c3e"]}]}, {"BlockType": "LINE", "Confidence": 99.66647338867188, "Text": "ConGlobal Industries", "Geometry": {"BoundingBox": {"Width": 0.13474023342132568, "Height": 0.015268665738403797, "Left": 0.4407184422016144, "Top": 0.2917872965335846}, "Polygon": [{"X": 0.44071847200393677, "Y": 0.2917872965335846}, {"X": 0.5754587054252625, "Y": 0.29178735613822937}, {"X": 0.5754586458206177, "Y": 0.30705592036247253}, {"X": 0.4407184422016144, "Y": 0.3070559799671173}]}, "Id": "4453bb4f-24a6-4c83-8d54-1e5b5a9c62ce", "Relationships": [{"Type": "CHILD", "Ids": ["e6e8ef9d-6225-4e7d-bca1-147bb4ac0078", "b6a17e9e-a609-46e9-a6dc-f6ff2ebbb83f"]}]}, {"BlockType": "LINE", "Confidence": 99.96392822265625, "Text": "3201 CenterPoint Way", "Geometry": {"BoundingBox": {"Width": 0.14340052008628845, "Height": 0.018990706652402878, "Left": 0.4370809495449066, "Top": 0.3183431923389435}, "Polygon": [{"X": 0.4370810091495514, "Y": 0.31834331154823303}, {"X": 0.5804814696311951, "Y": 0.3183431923389435}, {"X": 0.5804814100265503, "Y": 0.337333619594574}, {"X": 0.4370809495449066, "Y": 0.33733388781547546}]}, "Id": "eb45cac0-d331-4b79-b439-8f8d12e9b3fa", "Relationships": [{"Type": "CHILD", "Ids": ["ab2e65ea-7207-400d-864f-093d42b2d8dc", "04d80909-b2f1-47db-b8aa-dc25d1675354", "308485f4-e5ae-4548-a65a-e3026654bf42"]}]}, {"BlockType": "LINE", "Confidence": 99.91104888916016, "Text": "Joliet, IL 60436", "Geometry": {"BoundingBox": {"Width": 0.09604102373123169, "Height": 0.01692650094628334, "Left": 0.460102379322052, "Top": 0.3441251516342163}, "Polygon": [{"X": 0.4601024091243744, "Y": 0.344125360250473}, {"X": 0.5561434030532837, "Y": 0.3441251516342163}, {"X": 0.5561433434486389, "Y": 0.3610513508319855}, {"X": 0.460102379322052, "Y": 0.36105164885520935}]}, "Id": "2ee93a83-559b-4403-b1d4-0172a790cf39", "Relationships": [{"Type": "CHILD", "Ids": ["11db33ad-2594-48f4-a016-40b9d64ce611", "3b0b5b97-6ad0-4ed5-a462-70d884eb753b", "36a8adce-a77b-460e-963e-47d957adf856"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "(708)-929-0342", "Geometry": {"BoundingBox": {"Width": 0.09915607422590256, "Height": 0.017738452181220055, "Left": 0.4588090479373932, "Top": 0.36992281675338745}, "Polygon": [{"X": 0.4588090777397156, "Y": 0.3699231743812561}, {"X": 0.557965099811554, "Y": 0.36992281675338745}, {"X": 0.557965099811554, "Y": 0.38766083121299744}, {"X": 0.4588090479373932, "Y": 0.38766127824783325}]}, "Id": "fc4b078d-1ee3-4c6b-85d6-c189b90a9032", "Relationships": [{"Type": "CHILD", "Ids": ["89cbb7d3-5683-463b-8739-b882169117e8"]}]}, {"BlockType": "LINE", "Confidence": 99.71121215820312, "Text": "Customer: CMA", "Geometry": {"BoundingBox": {"Width": 0.10691270232200623, "Height": 0.014965521171689034, "Left": 0.09640277922153473, "Top": 0.4219761788845062}, "Polygon": [{"X": 0.09640279412269592, "Y": 0.42197683453559875}, {"X": 0.20331548154354095, "Y": 0.4219761788845062}, {"X": 0.20331545174121857, "Y": 0.4369409680366516}, {"X": 0.09640277922153473, "Y": 0.4369416832923889}]}, "Id": "811a423c-da50-4fd7-b73c-dcbfb5ffe5ae", "Relationships": [{"Type": "CHILD", "Ids": ["1c537b80-3731-44dd-b5c8-5575d3e9c249", "69a979c6-25f8-4b1f-8451-08d9d4606c61"]}]}, {"BlockType": "LINE", "Confidence": 91.89423370361328, "Text": "Depot: CHI09", "Geometry": {"BoundingBox": {"Width": 0.08938866853713989, "Height": 0.018675772473216057, "Left": 0.09701725840568542, "Top": 0.4564722776412964}, "Polygon": [{"X": 0.09701727330684662, "Y": 0.4564729928970337}, {"X": 0.18640592694282532, "Y": 0.4564722776412964}, {"X": 0.18640589714050293, "Y": 0.4751472771167755}, {"X": 0.09701725840568542, "Y": 0.4751480519771576}]}, "Id": "7104d99f-4a80-4f7c-81c4-34b1296c21f8", "Relationships": [{"Type": "CHILD", "Ids": ["7bab894d-8520-44f4-832c-b3311916b271", "e8bf2ee0-7607-4a73-bde0-0beb58fe89ca"]}]}, {"BlockType": "LINE", "Confidence": 99.62186431884766, "Text": "Size: 40 H", "Geometry": {"BoundingBox": {"Width": 0.06590547412633896, "Height": 0.015334805473685265, "Left": 0.09638277441263199, "Top": 0.4915553629398346}, "Polygon": [{"X": 0.09638278931379318, "Y": 0.49155598878860474}, {"X": 0.16228824853897095, "Y": 0.4915553629398346}, {"X": 0.16228823363780975, "Y": 0.5068894624710083}, {"X": 0.09638277441263199, "Y": 0.5068901777267456}]}, "Id": "f6f8f9e1-e742-4069-9764-dd88193725f7", "Relationships": [{"Type": "CHILD", "Ids": ["8534aff4-99cd-479e-8376-5869749618f0", "34203a9d-9af4-426c-bb90-f5fa969ba1e1", "8bc5b316-95c7-49cf-8334-bac8938552f3"]}]}, {"BlockType": "LINE", "Confidence": 99.9473876953125, "Text": "Equipment Type: CON", "Geometry": {"BoundingBox": {"Width": 0.1500462144613266, "Height": 0.01881583221256733, "Left": 0.0968511551618576, "Top": 0.5255729556083679}, "Polygon": [{"X": 0.0968511775135994, "Y": 0.5255746245384216}, {"X": 0.2468973696231842, "Y": 0.5255729556083679}, {"X": 0.24689733982086182, "Y": 0.5443869829177856}, {"X": 0.0968511551618576, "Y": 0.5443887710571289}]}, "Id": "98d85ef7-e7c6-47b3-9b4e-b417ee92f4f8", "Relationships": [{"Type": "CHILD", "Ids": ["8293de5a-5811-4ef5-afd2-72b25727f559", "417821d6-e006-4df3-9c73-bbcf8da7bfe5", "f90f163a-7aaf-4b9f-b919-f817a2056c80"]}]}, {"BlockType": "LINE", "Confidence": 99.60871124267578, "Text": "In Date: 2025-08-28", "Geometry": {"BoundingBox": {"Width": 0.1325182318687439, "Height": 0.014916643500328064, "Left": 0.09695605933666229, "Top": 0.5608450174331665}, "Polygon": [{"X": 0.09695607423782349, "Y": 0.560846745967865}, {"X": 0.2294742912054062, "Y": 0.5608450174331665}, {"X": 0.2294742614030838, "Y": 0.5757598280906677}, {"X": 0.09695605933666229, "Y": 0.5757616758346558}]}, "Id": "16bfbf36-7843-408e-8b17-662fe83aa7a2", "Relationships": [{"Type": "CHILD", "Ids": ["2ed6247f-8aa5-4aab-9787-0d0da9431ced", "75caf39b-0d26-43ee-b745-d7492520ecbb", "6ce74597-7b70-486a-b4a3-84f7718e6b6d"]}]}, {"BlockType": "LINE", "Confidence": 99.35626220703125, "Text": "In Time: 11:35", "Geometry": {"BoundingBox": {"Width": 0.09421038627624512, "Height": 0.015165434218943119, "Left": 0.09688542783260345, "Top": 0.5955797433853149}, "Polygon": [{"X": 0.09688544273376465, "Y": 0.5955811142921448}, {"X": 0.19109581410884857, "Y": 0.5955797433853149}, {"X": 0.19109579920768738, "Y": 0.6107437014579773}, {"X": 0.09688542783260345, "Y": 0.6107451319694519}]}, "Id": "31c6acd7-425e-4416-a6e1-0ed6d8010e43", "Relationships": [{"Type": "CHILD", "Ids": ["f99d6d35-9eb8-44fa-a463-359041558537", "eba597c3-21c3-429d-a835-0c04fce2e578", "21db4da8-6884-4a0e-a1ff-41ae2a48740d"]}]}, {"BlockType": "LINE", "Confidence": 99.18354034423828, "Text": "In Condition: AV", "Geometry": {"BoundingBox": {"Width": 0.11166032403707504, "Height": 0.01573333330452442, "Left": 0.09689242392778397, "Top": 0.6291576623916626}, "Polygon": [{"X": 0.09689243882894516, "Y": 0.6291595101356506}, {"X": 0.208552747964859, "Y": 0.6291576623916626}, {"X": 0.20855271816253662, "Y": 0.6448891162872314}, {"X": 0.09689242392778397, "Y": 0.6448910236358643}]}, "Id": "d428a75e-becf-4519-b470-43c00432176b", "Relationships": [{"Type": "CHILD", "Ids": ["dbbbca9b-d620-4908-ba5d-b08bd112ad80", "489be8b1-ca28-46db-bf04-03d586f954f1", "c7ccefd7-4ddd-453d-b809-b3e9c383b808"]}]}, {"BlockType": "LINE", "Confidence": 99.47883605957031, "Text": "In Empty: True", "Geometry": {"BoundingBox": {"Width": 0.09694287180900574, "Height": 0.018491104245185852, "Left": 0.09687966108322144, "Top": 0.6649655699729919}, "Polygon": [{"X": 0.09687967598438263, "Y": 0.6649673581123352}, {"X": 0.19382253289222717, "Y": 0.6649655699729919}, {"X": 0.19382250308990479, "Y": 0.6834548711776733}, {"X": 0.09687966108322144, "Y": 0.6834567189216614}]}, "Id": "16b35257-65d8-4c06-8bb6-b05a6849295e", "Relationships": [{"Type": "CHILD", "Ids": ["c5d2a50a-f7d7-478c-b9e8-9e0b7d58c0bb", "788127a3-3b45-4156-945b-8a4bc444b668", "1b01f3b4-f174-436f-8a6d-1a20fd2fbaae"]}]}, {"BlockType": "LINE", "Confidence": 99.97017669677734, "Text": "EIR Num: **********", "Geometry": {"BoundingBox": {"Width": 0.1466113179922104, "Height": 0.014951014891266823, "Left": 0.09685231000185013, "Top": 0.6989008784294128}, "Polygon": [{"X": 0.09685232490301132, "Y": 0.6989037990570068}, {"X": 0.2434636354446411, "Y": 0.6989008784294128}, {"X": 0.24346360564231873, "Y": 0.713848888874054}, {"X": 0.09685231000185013, "Y": 0.7138519287109375}]}, "Id": "d3b3cca8-a928-46af-9860-753f1d44975a", "Relationships": [{"Type": "CHILD", "Ids": ["8f486db2-06c6-4603-b888-25f0958045e9", "a0458bb6-de8f-4192-84c9-615cc6abe2b7", "61f2a907-f1d6-4b92-ad3d-f4e73d3654d8"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Type: SINGLE", "Geometry": {"BoundingBox": {"Width": 0.0884980708360672, "Height": 0.019050389528274536, "Left": 0.09600945562124252, "Top": 0.7337602972984314}, "Polygon": [{"X": 0.09600947797298431, "Y": 0.7337622046470642}, {"X": 0.18450753390789032, "Y": 0.7337602972984314}, {"X": 0.18450750410556793, "Y": 0.752808690071106}, {"X": 0.09600945562124252, "Y": 0.7528107166290283}]}, "Id": "c036985f-9b69-4571-b7b2-033f6b828f75", "Relationships": [{"Type": "CHILD", "Ids": ["c6cbfe8b-5a93-4185-8409-dbbc0a838501", "eb348afe-8f79-476d-86b5-491c3b2da8cd"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "SCAC Code: MVKT", "Geometry": {"BoundingBox": {"Width": 0.12245579063892365, "Height": 0.015054838731884956, "Left": 0.09649067372083664, "Top": 0.7686945199966431}, "Polygon": [{"X": 0.09649068862199783, "Y": 0.7686973810195923}, {"X": 0.2189464569091797, "Y": 0.7686945199966431}, {"X": 0.2189464420080185, "Y": 0.7837464213371277}, {"X": 0.09649067372083664, "Y": 0.7837493419647217}]}, "Id": "9ddcdfc5-624c-4898-a5a6-41346ecdc105", "Relationships": [{"Type": "CHILD", "Ids": ["3ae92138-3612-44a8-86ba-7523e62cef14", "cb2965cb-d927-4a76-9dda-263036b9be19", "3335cde3-6f78-4826-bc2d-8d4d4d5e83c9"]}]}, {"BlockType": "LINE", "Confidence": 99.22854614257812, "Text": "Trucking Co. Name: M VK TRAN", "Geometry": {"BoundingBox": {"Width": 0.21830706298351288, "Height": 0.019147051498293877, "Left": 0.09613632410764694, "Top": 0.802950918674469}, "Polygon": [{"X": 0.09613634645938873, "Y": 0.8029563426971436}, {"X": 0.3144433796405792, "Y": 0.802950918674469}, {"X": 0.31444334983825684, "Y": 0.8220922946929932}, {"X": 0.09613632410764694, "Y": 0.8220979571342468}]}, "Id": "387ca3b0-34cc-472c-9311-6f3c9f4a5d61", "Relationships": [{"Type": "CHILD", "Ids": ["01e886fa-fa8b-4668-a677-2facd9cd407e", "cc3160f4-fed2-4ed4-b150-2d5705f34a71", "36c190e0-9195-4b22-943a-31cd94ffbd8a", "f1998886-cc94-4ebe-9f8f-cd9147274193", "2e3413a6-bfec-4360-937d-bdaf75f08a80", "81210304-6eb7-4b12-a883-a13a582ea6d6"]}]}, {"BlockType": "LINE", "Confidence": 99.86819458007812, "Text": "Driver chz: NEDZ400975", "Geometry": {"BoundingBox": {"Width": 0.16101659834384918, "Height": 0.01534046046435833, "Left": 0.09692832827568054, "Top": 0.8376855254173279}, "Polygon": [{"X": 0.09692834317684174, "Y": 0.8376898169517517}, {"X": 0.2579449415206909, "Y": 0.8376855254173279}, {"X": 0.25794491171836853, "Y": 0.8530215620994568}, {"X": 0.09692832827568054, "Y": 0.8530259728431702}]}, "Id": "61987f2a-f18f-4a74-9df8-eb5b3f17585a", "Relationships": [{"Type": "CHILD", "Ids": ["befe8569-30ba-45ff-81c8-69269f6105fe", "ed2542cf-57ca-462c-8a32-090dd7d02d1b", "b5d2cebc-16b4-4757-854d-034128fbc6be"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Driver License: None None", "Geometry": {"BoundingBox": {"Width": 0.17906887829303741, "Height": 0.01486943382769823, "Left": 0.09687989205121994, "Top": 0.8725695013999939}, "Polygon": [{"X": 0.09687990695238113, "Y": 0.8725746273994446}, {"X": 0.27594876289367676, "Y": 0.8725695013999939}, {"X": 0.27594873309135437, "Y": 0.8874337077140808}, {"X": 0.09687989205121994, "Y": 0.887438952922821}]}, "Id": "608adb8e-7071-42dc-a10e-249aba402dbc", "Relationships": [{"Type": "CHILD", "Ids": ["a0015890-f6d6-4c6a-b0bd-300b6e4210d9", "39ac6224-070e-4c35-b0ca-aeaa7f2e5539", "3611f416-8888-4a40-8531-027c791d156e", "2e5d42ee-5c6c-4424-afb3-9a5fbb6090db"]}]}, {"BlockType": "LINE", "Confidence": 99.83971405029297, "Text": "Tractor License: XXX IL", "Geometry": {"BoundingBox": {"Width": 0.1519937813282013, "Height": 0.014847089536488056, "Left": 0.09618541598320007, "Top": 0.906980037689209}, "Polygon": [{"X": 0.09618542343378067, "Y": 0.9069846272468567}, {"X": 0.24817919731140137, "Y": 0.906980037689209}, {"X": 0.24817916750907898, "Y": 0.9218224287033081}, {"X": 0.09618541598320007, "Y": 0.9218271374702454}]}, "Id": "0fb119fe-06d8-4e73-8e70-508cf27b62c6", "Relationships": [{"Type": "CHILD", "Ids": ["7b34b216-782f-432a-a192-793663e43dd8", "2c676e5c-74f2-46e3-8ee5-26351eb8db77", "e50cb371-885e-4ccd-8cae-d3511d234dd5", "af563b96-6c7f-46b7-b087-f25adc513955"]}]}, {"BlockType": "LINE", "Confidence": 99.95389556884766, "Text": "Driver Name: 8183", "Geometry": {"BoundingBox": {"Width": 0.12636837363243103, "Height": 0.014968160539865494, "Left": 0.0969071015715599, "Top": 0.9417892694473267}, "Polygon": [{"X": 0.0969071164727211, "Y": 0.9417933225631714}, {"X": 0.22327546775341034, "Y": 0.9417892694473267}, {"X": 0.22327545285224915, "Y": 0.9567533135414124}, {"X": 0.0969071015715599, "Y": 0.9567574262619019}]}, "Id": "6fa4054c-f578-447a-9f46-27566196bc7c", "Relationships": [{"Type": "CHILD", "Ids": ["81d5dc18-d030-4744-9d2a-b3556b4a7f5d", "b581473b-a5bb-480c-a5e1-1c2b27e01210", "c38659fc-b64b-4bc0-a5f6-48671f023698"]}]}, {"BlockType": "WORD", "Confidence": 64.13265228271484, "Text": "8/29/25,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03552082180976868, "Height": 0.011331590823829174, "Left": 0.02310476265847683, "Top": 0.01566692814230919}, "Polygon": [{"X": 0.023104770109057426, "Y": 0.01566692814230919}, {"X": 0.05862558260560036, "Y": 0.01566743291914463}, {"X": 0.05862557142972946, "Y": 0.02699851803481579}, {"X": 0.02310476265847683, "Y": 0.026998033747076988}]}, "Id": "67c5d3c4-93ef-4a98-8f19-4007d19f5b1c"}, {"BlockType": "WORD", "Confidence": 95.9765625, "Text": "8:44", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01939098723232746, "Height": 0.009746355935931206, "Left": 0.06136542558670044, "Top": 0.01595340296626091}, "Polygon": [{"X": 0.061365433037281036, "Y": 0.01595340296626091}, {"X": 0.08075641095638275, "Y": 0.015953678637742996}, {"X": 0.08075640350580215, "Y": 0.025699758902192116}, {"X": 0.06136542558670044, "Y": 0.025699492543935776}]}, "Id": "9225b6b0-9121-4d60-a74d-76547b3dfb83"}, {"BlockType": "WORD", "Confidence": 98.39764404296875, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015306005254387856, "Height": 0.010139800608158112, "Left": 0.08211290091276169, "Top": 0.015615799464285374}, "Polygon": [{"X": 0.08211290836334229, "Y": 0.015615799464285374}, {"X": 0.0974189043045044, "Y": 0.015616017393767834}, {"X": 0.0974188968539238, "Y": 0.02575559914112091}, {"X": 0.08211290091276169, "Y": 0.025755390524864197}]}, "Id": "a38fa6e9-2f09-4b5f-a9cc-58ba454e0bac"}, {"BlockType": "WORD", "Confidence": 96.11905670166016, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010329744778573513, "Height": 0.010095990262925625, "Left": 0.5571965575218201, "Top": 0.015727343037724495}, "Polygon": [{"X": 0.5571966171264648, "Y": 0.015727343037724495}, {"X": 0.5675263404846191, "Y": 0.015727490186691284}, {"X": 0.5675262808799744, "Y": 0.025823332369327545}, {"X": 0.5571965575218201, "Y": 0.025823190808296204}]}, "Id": "3ec13c9e-0ca9-470d-aec9-dcd96b547517"}, {"BlockType": "WORD", "Confidence": 93.68083953857422, "Text": "Con", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2519247531890869, "Height": 0.11373712122440338, "Left": 0.2713283598423004, "Top": 0.06493331491947174}, "Polygon": [{"X": 0.27132850885391235, "Y": 0.06493331491947174}, {"X": 0.5232530832290649, "Y": 0.06493628025054932}, {"X": 0.5232528448104858, "Y": 0.17867043614387512}, {"X": 0.2713283598423004, "Y": 0.17866890132427216}]}, "Id": "c649981b-d635-4eb6-8a90-1f01b7136392"}, {"BlockType": "WORD", "Confidence": 95.673828125, "Text": "Global", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.39433881640434265, "Height": 0.11104237288236618, "Left": 0.5551545023918152, "Top": 0.06642217189073563}, "Polygon": [{"X": 0.5551548004150391, "Y": 0.06642217189073563}, {"X": 0.9494933485984802, "Y": 0.06642678380012512}, {"X": 0.9494929313659668, "Y": 0.1774645447731018}, {"X": 0.5551545023918152, "Y": 0.1774621158838272}]}, "Id": "7bbcbe5f-00da-4bef-8960-39e631135897"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TCNU1638696", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16356204450130463, "Height": 0.024553902447223663, "Left": 0.42639920115470886, "Top": 0.2439866065979004}, "Polygon": [{"X": 0.42639926075935364, "Y": 0.2439866065979004}, {"X": 0.5899612307548523, "Y": 0.2439870685338974}, {"X": 0.5899611711502075, "Y": 0.26854050159454346}, {"X": 0.42639920115470886, "Y": 0.26854023337364197}]}, "Id": "bee78306-755c-4e97-bc32-222ed93b1c3e"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "ConGlobal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0680614784359932, "Height": 0.015153052285313606, "Left": 0.4407184422016144, "Top": 0.2917872965335846}, "Polygon": [{"X": 0.44071847200393677, "Y": 0.2917872965335846}, {"X": 0.5087799429893494, "Y": 0.291787326335907}, {"X": 0.5087798833847046, "Y": 0.30694034695625305}, {"X": 0.4407184422016144, "Y": 0.30694034695625305}]}, "Id": "e6e8ef9d-6225-4e7d-bca1-147bb4ac0078"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Industries", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06308567523956299, "Height": 0.014989862218499184, "Left": 0.5123730301856995, "Top": 0.2920660972595215}, "Polygon": [{"X": 0.5123730301856995, "Y": 0.2920660972595215}, {"X": 0.5754587054252625, "Y": 0.2920660972595215}, {"X": 0.5754586458206177, "Y": 0.30705592036247253}, {"X": 0.5123730301856995, "Y": 0.3070559501647949}]}, "Id": "b6a17e9e-a609-46e9-a6dc-f6ff2ebbb83f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "3201", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03007114864885807, "Height": 0.015027589164674282, "Left": 0.437080979347229, "Top": 0.31842875480651855}, "Polygon": [{"X": 0.4370810091495514, "Y": 0.31842878460884094}, {"X": 0.46715211868286133, "Y": 0.31842875480651855}, {"X": 0.46715208888053894, "Y": 0.33345627784729004}, {"X": 0.437080979347229, "Y": 0.3334563374519348}]}, "Id": "ab2e65ea-7207-400d-864f-093d42b2d8dc"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "CenterPoint", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07584209740161896, "Height": 0.015223706141114235, "Left": 0.4723437428474426, "Top": 0.31834322214126587}, "Polygon": [{"X": 0.472343772649765, "Y": 0.31834328174591064}, {"X": 0.5481858253479004, "Y": 0.31834322214126587}, {"X": 0.5481858253479004, "Y": 0.3335667848587036}, {"X": 0.4723437428474426, "Y": 0.33356693387031555}]}, "Id": "04d80909-b2f1-47db-b8aa-dc25d1675354"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Way", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028950046747922897, "Height": 0.018339814618229866, "Left": 0.5515314340591431, "Top": 0.31899386644363403}, "Polygon": [{"X": 0.5515314936637878, "Y": 0.3189938962459564}, {"X": 0.5804814696311951, "Y": 0.31899386644363403}, {"X": 0.5804814100265503, "Y": 0.337333619594574}, {"X": 0.5515314340591431, "Y": 0.33733367919921875}]}, "Id": "308485f4-e5ae-4548-a65a-e3026654bf42"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "<PERSON><PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03730226680636406, "Height": 0.016560116782784462, "Left": 0.460102379322052, "Top": 0.34449154138565063}, "Polygon": [{"X": 0.4601024091243744, "Y": 0.3444916009902954}, {"X": 0.49740466475486755, "Y": 0.34449154138565063}, {"X": 0.4974046051502228, "Y": 0.3610515296459198}, {"X": 0.460102379322052, "Y": 0.36105164885520935}]}, "Id": "11db33ad-2594-48f4-a016-40b9d64ce611"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011431319639086723, "Height": 0.014161327853798866, "Left": 0.5012599229812622, "Top": 0.3447484076023102}, "Polygon": [{"X": 0.5012599229812622, "Y": 0.34474843740463257}, {"X": 0.5126912593841553, "Y": 0.3447484076023102}, {"X": 0.5126911997795105, "Y": 0.3589097261428833}, {"X": 0.5012599229812622, "Y": 0.3589097559452057}]}, "Id": "3b0b5b97-6ad0-4ed5-a462-70d884eb753b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "60436", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040231142193078995, "Height": 0.015150848776102066, "Left": 0.5159122943878174, "Top": 0.3441251516342163}, "Polygon": [{"X": 0.5159122943878174, "Y": 0.34412524104118347}, {"X": 0.5561434030532837, "Y": 0.3441251516342163}, {"X": 0.5561434030532837, "Y": 0.3592758774757385}, {"X": 0.5159122943878174, "Y": 0.3592759966850281}]}, "Id": "36a8adce-a77b-460e-963e-47d957adf856"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "(708)-929-0342", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09915607422590256, "Height": 0.017738452181220055, "Left": 0.4588090479373932, "Top": 0.36992281675338745}, "Polygon": [{"X": 0.4588090777397156, "Y": 0.3699231743812561}, {"X": 0.557965099811554, "Y": 0.36992281675338745}, {"X": 0.557965099811554, "Y": 0.38766083121299744}, {"X": 0.4588090479373932, "Y": 0.38766127824783325}]}, "Id": "89cbb7d3-5683-463b-8739-b882169117e8"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "Customer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0709676668047905, "Height": 0.014965305104851723, "Left": 0.09640277922153473, "Top": 0.42197638750076294}, "Polygon": [{"X": 0.09640279412269592, "Y": 0.42197683453559875}, {"X": 0.16737045347690582, "Y": 0.42197638750076294}, {"X": 0.16737042367458344, "Y": 0.4369412064552307}, {"X": 0.09640277922153473, "Y": 0.4369416832923889}]}, "Id": "1c537b80-3731-44dd-b5c8-5575d3e9c249"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "CMA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03214616701006889, "Height": 0.014719044789671898, "Left": 0.17116931080818176, "Top": 0.4220096170902252}, "Polygon": [{"X": 0.17116932570934296, "Y": 0.42200982570648193}, {"X": 0.20331548154354095, "Y": 0.4220096170902252}, {"X": 0.20331546664237976, "Y": 0.43672844767570496}, {"X": 0.17116931080818176, "Y": 0.43672868609428406}]}, "Id": "69a979c6-25f8-4b1f-8451-08d9d4606c61"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Depot:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04606041684746742, "Height": 0.017935335636138916, "Left": 0.09701725840568542, "Top": 0.4572127163410187}, "Polygon": [{"X": 0.09701727330684662, "Y": 0.45721307396888733}, {"X": 0.14307767152786255, "Y": 0.4572127163410187}, {"X": 0.14307765662670135, "Y": 0.47514766454696655}, {"X": 0.09701725840568542, "Y": 0.4751480519771576}]}, "Id": "7bab894d-8520-44f4-832c-b3311916b271"}, {"BlockType": "WORD", "Confidence": 83.86817932128906, "Text": "CHI09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03941986709833145, "Height": 0.015520830638706684, "Left": 0.14698606729507446, "Top": 0.4564722776412964}, "Polygon": [{"X": 0.14698608219623566, "Y": 0.45647260546684265}, {"X": 0.18640592694282532, "Y": 0.4564722776412964}, {"X": 0.18640591204166412, "Y": 0.4719927906990051}, {"X": 0.14698606729507446, "Y": 0.4719931185245514}]}, "Id": "e8bf2ee0-7607-4a73-bde0-0beb58fe89ca"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Size:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03150627017021179, "Height": 0.015334478579461575, "Left": 0.09638277441263199, "Top": 0.49155569076538086}, "Polygon": [{"X": 0.09638278931379318, "Y": 0.49155598878860474}, {"X": 0.12788905203342438, "Y": 0.49155569076538086}, {"X": 0.12788903713226318, "Y": 0.506889820098877}, {"X": 0.09638277441263199, "Y": 0.5068901777267456}]}, "Id": "8534aff4-99cd-479e-8376-5869749618f0"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016739392653107643, "Height": 0.014608132652938366, "Left": 0.13144591450691223, "Top": 0.4919971227645874}, "Polygon": [{"X": 0.13144592940807343, "Y": 0.49199727177619934}, {"X": 0.14818529784679413, "Y": 0.4919971227645874}, {"X": 0.14818528294563293, "Y": 0.5066050887107849}, {"X": 0.13144591450691223, "Y": 0.5066052675247192}]}, "Id": "34203a9d-9af4-426c-bb90-f5fa969ba1e1"}, {"BlockType": "WORD", "Confidence": 99.12926483154297, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010467316955327988, "Height": 0.014321173541247845, "Left": 0.15182092785835266, "Top": 0.4919947683811188}, "Polygon": [{"X": 0.15182095766067505, "Y": 0.49199485778808594}, {"X": 0.16228824853897095, "Y": 0.4919947683811188}, {"X": 0.16228823363780975, "Y": 0.5063158273696899}, {"X": 0.15182092785835266, "Y": 0.5063159465789795}]}, "Id": "8bc5b316-95c7-49cf-8334-bac8938552f3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07489606738090515, "Height": 0.0184011310338974, "Left": 0.0968511551618576, "Top": 0.5259506106376648}, "Polygon": [{"X": 0.0968511700630188, "Y": 0.5259514451026917}, {"X": 0.17174722254276276, "Y": 0.5259506106376648}, {"X": 0.17174720764160156, "Y": 0.5443508625030518}, {"X": 0.0968511551618576, "Y": 0.5443517565727234}]}, "Id": "8293de5a-5811-4ef5-afd2-72b25727f559"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03729592263698578, "Height": 0.018189195543527603, "Left": 0.17500390112400055, "Top": 0.5261986255645752}, "Polygon": [{"X": 0.17500391602516174, "Y": 0.5261990427970886}, {"X": 0.21229982376098633, "Y": 0.5261986255645752}, {"X": 0.21229979395866394, "Y": 0.5443874001502991}, {"X": 0.17500390112400055, "Y": 0.5443878173828125}]}, "Id": "417821d6-e006-4df3-9c73-bbcf8da7bfe5"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "CON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030693598091602325, "Height": 0.015324775129556656, "Left": 0.21620377898216248, "Top": 0.5255729556083679}, "Polygon": [{"X": 0.21620379388332367, "Y": 0.5255733132362366}, {"X": 0.2468973696231842, "Y": 0.5255729556083679}, {"X": 0.246897354722023, "Y": 0.5408973693847656}, {"X": 0.21620377898216248, "Y": 0.5408977270126343}]}, "Id": "f90f163a-7aaf-4b9f-b919-f817a2056c80"}, {"BlockType": "WORD", "Confidence": 98.9343490600586, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013210747390985489, "Height": 0.01406038273125887, "Left": 0.09695605933666229, "Top": 0.5612995028495789}, "Polygon": [{"X": 0.09695607423782349, "Y": 0.5612996816635132}, {"X": 0.11016681045293808, "Y": 0.5612995028495789}, {"X": 0.11016679555177689, "Y": 0.5753597021102905}, {"X": 0.09695605933666229, "Y": 0.5753598809242249}]}, "Id": "2ed6247f-8aa5-4aab-9787-0d0da9431ced"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03606557846069336, "Height": 0.014629350043833256, "Left": 0.11431948840618134, "Top": 0.5611320734024048}, "Polygon": [{"X": 0.11431950330734253, "Y": 0.561132550239563}, {"X": 0.1503850668668747, "Y": 0.5611320734024048}, {"X": 0.1503850519657135, "Y": 0.5757609009742737}, {"X": 0.11431948840618134, "Y": 0.5757614374160767}]}, "Id": "75caf39b-0d26-43ee-b745-d7492520ecbb"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2025-08-28", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07503922283649445, "Height": 0.014825711026787758, "Left": 0.15443506836891174, "Top": 0.5608450174331665}, "Polygon": [{"X": 0.15443508327007294, "Y": 0.5608459711074829}, {"X": 0.2294742912054062, "Y": 0.5608450174331665}, {"X": 0.2294742614030838, "Y": 0.5756697058677673}, {"X": 0.15443506836891174, "Y": 0.5756707191467285}]}, "Id": "6ce74597-7b70-486a-b4a3-84f7718e6b6d"}, {"BlockType": "WORD", "Confidence": 98.16725158691406, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013298774138092995, "Height": 0.014181774109601974, "Left": 0.09688542783260345, "Top": 0.5961147546768188}, "Polygon": [{"X": 0.09688544273376465, "Y": 0.5961149334907532}, {"X": 0.1101842001080513, "Y": 0.5961147546768188}, {"X": 0.1101841926574707, "Y": 0.6102963089942932}, {"X": 0.09688542783260345, "Y": 0.6102964878082275}]}, "Id": "f99d6d35-9eb8-44fa-a463-359041558537"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038657356053590775, "Height": 0.014868215657770634, "Left": 0.11351072788238525, "Top": 0.5955802798271179}, "Polygon": [{"X": 0.11351074278354645, "Y": 0.5955808758735657}, {"X": 0.15216808021068573, "Y": 0.5955802798271179}, {"X": 0.15216806530952454, "Y": 0.6104479432106018}, {"X": 0.11351072788238525, "Y": 0.6104485392570496}]}, "Id": "eba597c3-21c3-429d-a835-0c04fce2e578"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "11:35", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03399203345179558, "Height": 0.015139220282435417, "Left": 0.1571037769317627, "Top": 0.5956050157546997}, "Polygon": [{"X": 0.1571037918329239, "Y": 0.5956054925918579}, {"X": 0.19109581410884857, "Y": 0.5956050157546997}, {"X": 0.19109579920768738, "Y": 0.6107437014579773}, {"X": 0.1571037769317627, "Y": 0.6107442378997803}]}, "Id": "21db4da8-6884-4a0e-a1ff-41ae2a48740d"}, {"BlockType": "WORD", "Confidence": 97.95819091796875, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013307853601872921, "Height": 0.014247954823076725, "Left": 0.09689242392778397, "Top": 0.6300983428955078}, "Polygon": [{"X": 0.09689243882894516, "Y": 0.6300985217094421}, {"X": 0.11020027846097946, "Y": 0.6300983428955078}, {"X": 0.11020026355981827, "Y": 0.6443460583686829}, {"X": 0.09689242392778397, "Y": 0.644346296787262}]}, "Id": "dbbbca9b-d620-4908-ba5d-b08bd112ad80"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Condition:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07219397276639938, "Height": 0.015732673928141594, "Left": 0.11407623440027237, "Top": 0.6291580200195312}, "Polygon": [{"X": 0.11407624930143356, "Y": 0.6291592121124268}, {"X": 0.18627020716667175, "Y": 0.6291580200195312}, {"X": 0.18627019226551056, "Y": 0.6448894739151001}, {"X": 0.11407623440027237, "Y": 0.6448907256126404}]}, "Id": "489be8b1-ca28-46db-bf04-03d586f954f1"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "AV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018771154806017876, "Height": 0.014109344221651554, "Left": 0.18978159129619598, "Top": 0.62986820936203}, "Polygon": [{"X": 0.18978160619735718, "Y": 0.6298685669898987}, {"X": 0.208552747964859, "Y": 0.62986820936203}, {"X": 0.20855271816253662, "Y": 0.6439772248268127}, {"X": 0.18978159129619598, "Y": 0.6439775824546814}]}, "Id": "c7ccefd7-4ddd-453d-b809-b3e9c383b808"}, {"BlockType": "WORD", "Confidence": 98.48612976074219, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013347098603844643, "Height": 0.014234015718102455, "Left": 0.09687966108322144, "Top": 0.6652145981788635}, "Polygon": [{"X": 0.09687967598438263, "Y": 0.6652148365974426}, {"X": 0.11022675782442093, "Y": 0.6652145981788635}, {"X": 0.11022674292325974, "Y": 0.6794483661651611}, {"X": 0.09687966108322144, "Y": 0.6794486045837402}]}, "Id": "c5d2a50a-f7d7-478c-b9e8-9e0b7d58c0bb"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Empty:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04803098738193512, "Height": 0.018264465034008026, "Left": 0.11425724625587463, "Top": 0.6651918888092041}, "Polygon": [{"X": 0.11425726860761642, "Y": 0.6651927828788757}, {"X": 0.16228823363780975, "Y": 0.6651918888092041}, {"X": 0.16228821873664856, "Y": 0.6834554672241211}, {"X": 0.11425724625587463, "Y": 0.6834563612937927}]}, "Id": "788127a3-3b45-4156-945b-8a4bc444b668"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "True", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028359882533550262, "Height": 0.014850704930722713, "Left": 0.1654626429080963, "Top": 0.6649655699729919}, "Polygon": [{"X": 0.1654626578092575, "Y": 0.6649661064147949}, {"X": 0.19382253289222717, "Y": 0.6649655699729919}, {"X": 0.19382250308990479, "Y": 0.6798157691955566}, {"X": 0.1654626429080963, "Y": 0.6798163056373596}]}, "Id": "1b01f3b4-f174-436f-8a6d-1a20fd2fbaae"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "EIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02219017967581749, "Height": 0.014502346515655518, "Left": 0.09685231000185013, "Top": 0.6990015506744385}, "Polygon": [{"X": 0.09685232490301132, "Y": 0.6990019679069519}, {"X": 0.11904248595237732, "Y": 0.6990015506744385}, {"X": 0.11904247105121613, "Y": 0.7135034203529358}, {"X": 0.09685231000185013, "Y": 0.713503897190094}]}, "Id": "8f486db2-06c6-4603-b888-25f0958045e9"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Num:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03759131208062172, "Height": 0.014627902768552303, "Left": 0.12291581183671951, "Top": 0.6992234587669373}, "Polygon": [{"X": 0.1229158267378807, "Y": 0.6992242336273193}, {"X": 0.16050712764263153, "Y": 0.6992234587669373}, {"X": 0.16050709784030914, "Y": 0.7138506174087524}, {"X": 0.12291581183671951, "Y": 0.7138513922691345}]}, "Id": "a0458bb6-de8f-4192-84c9-615cc6abe2b7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07779879122972488, "Height": 0.014860418625175953, "Left": 0.16566483676433563, "Top": 0.6989008784294128}, "Polygon": [{"X": 0.16566485166549683, "Y": 0.698902428150177}, {"X": 0.2434636354446411, "Y": 0.6989008784294128}, {"X": 0.24346360564231873, "Y": 0.71375972032547}, {"X": 0.16566483676433563, "Y": 0.7137613296508789}]}, "Id": "61f2a907-f1d6-4b92-ad3d-f4e73d3654d8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037570152431726456, "Height": 0.01827198639512062, "Left": 0.09600945562124252, "Top": 0.7345386743545532}, "Polygon": [{"X": 0.09600947797298431, "Y": 0.7345395088195801}, {"X": 0.13357961177825928, "Y": 0.7345386743545532}, {"X": 0.1335795819759369, "Y": 0.7528098225593567}, {"X": 0.09600945562124252, "Y": 0.7528107166290283}]}, "Id": "c6cbfe8b-5a93-4185-8409-dbbc0a838501"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "SINGLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04703164100646973, "Height": 0.014926142990589142, "Left": 0.1374758929014206, "Top": 0.7337602972984314}, "Polygon": [{"X": 0.1374759078025818, "Y": 0.7337613105773926}, {"X": 0.18450753390789032, "Y": 0.7337602972984314}, {"X": 0.18450751900672913, "Y": 0.7486854195594788}, {"X": 0.1374758929014206, "Y": 0.7486864328384399}]}, "Id": "eb348afe-8f79-476d-86b5-491c3b2da8cd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SCAC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03730336204171181, "Height": 0.014934342354536057, "Left": 0.09649067372083664, "Top": 0.768703818321228}, "Polygon": [{"X": 0.09649068862199783, "Y": 0.7687047123908997}, {"X": 0.13379403948783875, "Y": 0.768703818321228}, {"X": 0.13379402458667755, "Y": 0.783637285232544}, {"X": 0.09649067372083664, "Y": 0.7836381793022156}]}, "Id": "3ae92138-3612-44a8-86ba-7523e62cef14"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03907950222492218, "Height": 0.015052864328026772, "Left": 0.13731993734836578, "Top": 0.7686954736709595}, "Polygon": [{"X": 0.13731995224952698, "Y": 0.7686964273452759}, {"X": 0.17639943957328796, "Y": 0.7686954736709595}, {"X": 0.17639942467212677, "Y": 0.7837474346160889}, {"X": 0.13731993734836578, "Y": 0.7837483882904053}]}, "Id": "cb2965cb-d927-4a76-9dda-263036b9be19"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "MVKT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03834515064954758, "Height": 0.014705895446240902, "Left": 0.1806013137102127, "Top": 0.7687454223632812}, "Polygon": [{"X": 0.1806013286113739, "Y": 0.7687463164329529}, {"X": 0.2189464569091797, "Y": 0.7687454223632812}, {"X": 0.2189464420080185, "Y": 0.7834504246711731}, {"X": 0.1806013137102127, "Y": 0.7834513187408447}]}, "Id": "3335cde3-6f78-4826-bc2d-8d4d4d5e83c9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trucking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05984876677393913, "Height": 0.019143084064126015, "Left": 0.09613632410764694, "Top": 0.8029548525810242}, "Polygon": [{"X": 0.09613634645938873, "Y": 0.8029563426971436}, {"X": 0.15598508715629578, "Y": 0.8029548525810242}, {"X": 0.15598507225513458, "Y": 0.8220964074134827}, {"X": 0.09613632410764694, "Y": 0.8220979571342468}]}, "Id": "01e886fa-fa8b-4668-a677-2facd9cd407e"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Co.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021950215101242065, "Height": 0.014520964585244656, "Left": 0.1601271629333496, "Top": 0.8033039569854736}, "Polygon": [{"X": 0.1601271778345108, "Y": 0.8033044934272766}, {"X": 0.18207737803459167, "Y": 0.8033039569854736}, {"X": 0.18207736313343048, "Y": 0.8178243637084961}, {"X": 0.1601271629333496, "Y": 0.8178249001502991}]}, "Id": "cc3160f4-fed2-4ed4-b150-2d5705f34a71"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04443788155913353, "Height": 0.014450447633862495, "Left": 0.1864856779575348, "Top": 0.8034176826477051}, "Polygon": [{"X": 0.18648569285869598, "Y": 0.8034188151359558}, {"X": 0.23092356324195862, "Y": 0.8034176826477051}, {"X": 0.23092353343963623, "Y": 0.8178669810295105}, {"X": 0.1864856779575348, "Y": 0.8178681135177612}]}, "Id": "36c190e0-9195-4b22-943a-31cd94ffbd8a"}, {"BlockType": "WORD", "Confidence": 98.05584716796875, "Text": "M", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012777398340404034, "Height": 0.013237633742392063, "Left": 0.2352246344089508, "Top": 0.8039471507072449}, "Polygon": [{"X": 0.235224649310112, "Y": 0.8039474487304688}, {"X": 0.2480020374059677, "Y": 0.8039471507072449}, {"X": 0.24800200760364532, "Y": 0.8171844482421875}, {"X": 0.2352246344089508, "Y": 0.8171847462654114}]}, "Id": "f1998886-cc94-4ebe-9f8f-cd9147274193"}, {"BlockType": "WORD", "Confidence": 97.8077163696289, "Text": "VK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02337983250617981, "Height": 0.014569645747542381, "Left": 0.2512612044811249, "Top": 0.8033627271652222}, "Polygon": [{"X": 0.25126123428344727, "Y": 0.8033633232116699}, {"X": 0.2746410369873047, "Y": 0.8033627271652222}, {"X": 0.2746410071849823, "Y": 0.8179317712783813}, {"X": 0.2512612044811249, "Y": 0.8179323673248291}]}, "Id": "2e3413a6-bfec-4360-937d-bdaf75f08a80"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "TRAN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037248872220516205, "Height": 0.014191721566021442, "Left": 0.2771945297718048, "Top": 0.8033499717712402}, "Polygon": [{"X": 0.2771945297718048, "Y": 0.8033508658409119}, {"X": 0.3144433796405792, "Y": 0.8033499717712402}, {"X": 0.31444334983825684, "Y": 0.81754070520401}, {"X": 0.2771945297718048, "Y": 0.8175416588783264}]}, "Id": "81210304-6eb7-4b12-a883-a13a582ea6d6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042756322771310806, "Height": 0.014468585141003132, "Left": 0.09692832827568054, "Top": 0.8381280303001404}, "Polygon": [{"X": 0.09692834317684174, "Y": 0.8381291627883911}, {"X": 0.13968464732170105, "Y": 0.8381280303001404}, {"X": 0.13968463242053986, "Y": 0.8525954484939575}, {"X": 0.09692832827568054, "Y": 0.852596640586853}]}, "Id": "befe8569-30ba-45ff-81c8-69269f6105fe"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "chz:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026985056698322296, "Height": 0.01533683855086565, "Left": 0.14301183819770813, "Top": 0.8376878499984741}, "Polygon": [{"X": 0.14301185309886932, "Y": 0.8376885652542114}, {"X": 0.16999688744544983, "Y": 0.8376878499984741}, {"X": 0.16999687254428864, "Y": 0.8530239462852478}, {"X": 0.14301183819770813, "Y": 0.8530247211456299}]}, "Id": "ed2542cf-57ca-462c-8a32-090dd7d02d1b"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "NEDZ400975", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08423156291246414, "Height": 0.014538177289068699, "Left": 0.17371337115764618, "Top": 0.8380955457687378}, "Polygon": [{"X": 0.17371338605880737, "Y": 0.8380978107452393}, {"X": 0.2579449415206909, "Y": 0.8380955457687378}, {"X": 0.25794491171836853, "Y": 0.8526313900947571}, {"X": 0.17371337115764618, "Y": 0.8526337146759033}]}, "Id": "b5d2cebc-16b4-4757-854d-034128fbc6be"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04269495978951454, "Height": 0.01469513215124607, "Left": 0.09687989205121994, "Top": 0.8725733757019043}, "Polygon": [{"X": 0.09687990695238113, "Y": 0.8725746273994446}, {"X": 0.13957485556602478, "Y": 0.8725733757019043}, {"X": 0.1395748406648636, "Y": 0.8872672915458679}, {"X": 0.09687989205121994, "Y": 0.8872685432434082}]}, "Id": "a0015890-f6d6-4c6a-b0bd-300b6e4210d9"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05354993790388107, "Height": 0.014720134437084198, "Left": 0.14324504137039185, "Top": 0.8727174401283264}, "Polygon": [{"X": 0.14324505627155304, "Y": 0.8727189898490906}, {"X": 0.19679498672485352, "Y": 0.8727174401283264}, {"X": 0.19679497182369232, "Y": 0.887436032295227}, {"X": 0.14324504137039185, "Y": 0.8874375820159912}]}, "Id": "39ac6224-070e-4c35-b0ca-aeaa7f2e5539"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "None", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03542915731668472, "Height": 0.014207440428435802, "Left": 0.20096558332443237, "Top": 0.873155415058136}, "Polygon": [{"X": 0.20096561312675476, "Y": 0.8731564283370972}, {"X": 0.2363947480916977, "Y": 0.873155415058136}, {"X": 0.2363947331905365, "Y": 0.8873618245124817}, {"X": 0.20096558332443237, "Y": 0.8873628377914429}]}, "Id": "3611f416-8888-4a40-8531-027c791d156e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "None", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035335708409547806, "Height": 0.014456088654696941, "Left": 0.24061305820941925, "Top": 0.8729196190834045}, "Polygon": [{"X": 0.24061307311058044, "Y": 0.8729206323623657}, {"X": 0.27594876289367676, "Y": 0.8729196190834045}, {"X": 0.27594873309135437, "Y": 0.8873746991157532}, {"X": 0.24061305820941925, "Y": 0.8873757123947144}]}, "Id": "2e5d42ee-5c6c-4424-afb3-9a5fbb6090db"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04987197369337082, "Height": 0.014559755101799965, "Left": 0.09618541598320007, "Top": 0.9070679545402527}, "Polygon": [{"X": 0.09618542343378067, "Y": 0.9070694446563721}, {"X": 0.1460573822259903, "Y": 0.9070679545402527}, {"X": 0.1460573673248291, "Y": 0.9216261506080627}, {"X": 0.09618541598320007, "Y": 0.9216277003288269}]}, "Id": "7b34b216-782f-432a-a192-793663e43dd8"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05362009257078171, "Height": 0.014623169787228107, "Left": 0.14946725964546204, "Top": 0.9071133136749268}, "Polygon": [{"X": 0.14946727454662323, "Y": 0.9071149230003357}, {"X": 0.20308735966682434, "Y": 0.9071133136749268}, {"X": 0.20308732986450195, "Y": 0.9217348098754883}, {"X": 0.14946725964546204, "Y": 0.921736478805542}]}, "Id": "2c676e5c-74f2-46e3-8ee5-26351eb8db77"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "XXX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027262400835752487, "Height": 0.01438137423247099, "Left": 0.20634780824184418, "Top": 0.9069805145263672}, "Polygon": [{"X": 0.20634783804416656, "Y": 0.9069812893867493}, {"X": 0.23361021280288696, "Y": 0.9069805145263672}, {"X": 0.23361019790172577, "Y": 0.9213610291481018}, {"X": 0.20634780824184418, "Y": 0.9213618636131287}]}, "Id": "e50cb371-885e-4ccd-8cae-d3511d234dd5"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011082510463893414, "Height": 0.0143526466563344, "Left": 0.23709668219089508, "Top": 0.9074701070785522}, "Polygon": [{"X": 0.23709669709205627, "Y": 0.9074704647064209}, {"X": 0.24817919731140137, "Y": 0.9074701070785522}, {"X": 0.24817916750907898, "Y": 0.9218224287033081}, {"X": 0.23709668219089508, "Y": 0.9218227863311768}]}, "Id": "af563b96-6c7f-46b7-b087-f25adc513955"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04269811138510704, "Height": 0.01458730548620224, "Left": 0.0969071015715599, "Top": 0.9417919516563416}, "Polygon": [{"X": 0.0969071164727211, "Y": 0.9417933225631714}, {"X": 0.13960520923137665, "Y": 0.9417919516563416}, {"X": 0.13960519433021545, "Y": 0.9563778638839722}, {"X": 0.0969071015715599, "Y": 0.956379234790802}]}, "Id": "81d5dc18-d030-4744-9d2a-b3556b4a7f5d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04461721330881119, "Height": 0.014377564191818237, "Left": 0.14328265190124512, "Top": 0.942277193069458}, "Polygon": [{"X": 0.1432826668024063, "Y": 0.9422786235809326}, {"X": 0.1878998726606369, "Y": 0.942277193069458}, {"X": 0.18789984285831451, "Y": 0.9566532969474792}, {"X": 0.14328265190124512, "Y": 0.9566547870635986}]}, "Id": "b581473b-a5bb-480c-a5e1-1c2b27e01210"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "8183", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03159976005554199, "Height": 0.014792048372328281, "Left": 0.19167570769786835, "Top": 0.9419622421264648}, "Polygon": [{"X": 0.19167573750019073, "Y": 0.941963255405426}, {"X": 0.22327546775341034, "Y": 0.9419622421264648}, {"X": 0.22327545285224915, "Y": 0.9567533135414124}, {"X": 0.19167570769786835, "Y": 0.9567543268203735}]}, "Id": "c38659fc-b64b-4bc0-a5f6-48671f023698"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "6cc6d89b-38ac-4fff-b956-b51b8acbf5b2", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "6cc6d89b-38ac-4fff-b956-b51b8acbf5b2", "content-type": "application/x-amz-json-1.1", "content-length": "47482", "date": "Wed, 24 Sep 2025 13:28:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "92a57846-48e3-4988-880e-c98c7d112ea2", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:28:41 GMT", "content-type": "application/json", "content-length": "918", "connection": "keep-alive", "x-amzn-requestid": "92a57846-48e3-4988-880e-c98c7d112ea2"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page text. Need classify. Let's identify keywords: \"In Date\", \"In Time\", \"In Condition\", \"In Empty\", \"EIR Num\", \"SCAC Code\", \"Trucking Co. Name\", \"Driver\", etc. This looks like an \"ingate\" document: record of vehicle entering facility. Keywords: \"Equipment In-Gated\" but we see \"In Date\", \"In Time\", \"In Condition\". Also \"Depot\". So likely ingate.\n\nCheck other doc types: bol, pod, etc. No mention of Bill of Lading. No sign of receipt or invoice. Not a scale ticket: no weight. Not a log: no times beyond entry. So ingate.\n\nThus output page 1 as ingate."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"ingate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2751, "outputTokens": 174, "totalTokens": 2925}, "metrics": {"latencyMs": 909}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/6ab8df01_WQ9YLCCRP1EJOP7S8615.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n8/29/25, 8:44 AM\nJ1\nCon Global\nTCNU1638696\nConGlobal Industries\n3201 CenterPoint Way\nJoliet, IL 60436\n(708)-929-0342\nCustomer: CMA\nDepot: CHI09\nSize: 40 H\nEquipment Type: CON\nIn Date: 2025-08-28\nIn Time: 11:35\nIn Condition: AV\nIn Empty: True\nEIR Num: **********\nType: SINGLE\nSCAC Code: MVKT\nTrucking Co. Name: M VK TRAN\nDriver chz: NEDZ400975\nDriver License: None None\nTractor License: XXX IL\nDriver Name: 8183\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}