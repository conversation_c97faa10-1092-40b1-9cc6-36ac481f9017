2025-09-24 17:55:51,016 - INFO - Logging initialized. Log file: logs/test_classification_20250924_175551.log
2025-09-24 17:55:51,016 - INFO - 📁 Found 9 files to process
2025-09-24 17:55:51,017 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:55:51,017 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 17:55:51,017 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 17:55:51,017 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 17:55:51,017 - INFO - ⬆️ [17:55:51] Uploading: BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:55:52,905 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/BVLD9CCIUN6D2HGVETKO.pdf -> s3://document-extraction-logistically/temp/70f11c37_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:55:52,905 - INFO - 🔍 [17:55:52] Starting classification: BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:55:52,906 - INFO - ⬆️ [17:55:52] Uploading: ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:55:52,908 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:52,938 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:52,963 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/70f11c37_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:55:52,964 - INFO - Processing PDF from S3...
2025-09-24 17:55:52,966 - INFO - Downloading PDF from S3 to /tmp/tmpsirmb34j/70f11c37_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:55:54,652 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ILOJODH5ADT10MHGK0TI.pdf -> s3://document-extraction-logistically/temp/fe215fe4_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:55:54,653 - INFO - 🔍 [17:55:54] Starting classification: ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:55:54,654 - INFO - ⬆️ [17:55:54] Uploading: NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 17:55:54,654 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:54,672 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:54,679 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fe215fe4_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:55:54,680 - INFO - Processing PDF from S3...
2025-09-24 17:55:54,680 - INFO - Downloading PDF from S3 to /tmp/tmpbqjb6czb/fe215fe4_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:55:54,793 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:55:54,793 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:54,794 - INFO - Splitting PDF 70f11c37_BVLD9CCIUN6D2HGVETKO into 1 pages
2025-09-24 17:55:54,797 - INFO - Split PDF into 1 pages
2025-09-24 17:55:54,797 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:54,797 - INFO - Expected pages: [1]
2025-09-24 17:55:55,369 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf -> s3://document-extraction-logistically/temp/97fc811d_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 17:55:55,369 - INFO - 🔍 [17:55:55] Starting classification: NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 17:55:55,370 - INFO - ⬆️ [17:55:55] Uploading: NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:55:55,371 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:55,399 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:55,407 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/97fc811d_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 17:55:55,407 - INFO - Processing PDF from S3...
2025-09-24 17:55:55,408 - INFO - Downloading PDF from S3 to /tmp/tmpc48kxaky/97fc811d_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 17:55:56,016 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMWG7N543A7USM9VG3WI.pdf -> s3://document-extraction-logistically/temp/92786cba_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:55:56,016 - INFO - 🔍 [17:55:56] Starting classification: NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:55:56,017 - INFO - ⬆️ [17:55:56] Uploading: QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:55:56,020 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:56,047 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:56,050 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/92786cba_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:55:56,050 - INFO - Processing PDF from S3...
2025-09-24 17:55:56,051 - INFO - Downloading PDF from S3 to /tmp/tmpycau4yab/92786cba_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:55:56,697 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/QW3BG1TZMWHOTTZKGA8Y.pdf -> s3://document-extraction-logistically/temp/8e4d6c87_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:55:56,697 - INFO - 🔍 [17:55:56] Starting classification: QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:55:56,698 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:56,699 - INFO - ⬆️ [17:55:56] Uploading: ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:55:56,726 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:56,733 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8e4d6c87_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:55:56,733 - INFO - Processing PDF from S3...
2025-09-24 17:55:56,733 - INFO - Downloading PDF from S3 to /tmp/tmp0o2d0uns/8e4d6c87_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:55:57,340 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 17:55:57,341 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:57,342 - WARNING - /Prev=0 in the trailer - assuming there is no previous xref table
2025-09-24 17:55:57,343 - INFO - Splitting PDF fe215fe4_ILOJODH5ADT10MHGK0TI into 1 pages
2025-09-24 17:55:57,347 - INFO - Split PDF into 1 pages
2025-09-24 17:55:57,347 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:57,347 - INFO - Expected pages: [1]
2025-09-24 17:55:57,363 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ULZ4VHKA9IH82VWCSF3C.pdf -> s3://document-extraction-logistically/temp/4e5e76c6_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:55:57,364 - INFO - 🔍 [17:55:57] Starting classification: ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:55:57,364 - INFO - ⬆️ [17:55:57] Uploading: W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:55:57,365 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:57,377 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:57,380 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4e5e76c6_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:55:57,381 - INFO - Processing PDF from S3...
2025-09-24 17:55:57,381 - INFO - Downloading PDF from S3 to /tmp/tmpi8uigx85/4e5e76c6_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:55:57,992 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ (1).pdf -> s3://document-extraction-logistically/temp/9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:55:57,992 - INFO - 🔍 [17:55:57] Starting classification: W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:55:57,993 - INFO - ⬆️ [17:55:57] Uploading: W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:55:57,994 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:58,014 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:58,017 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:55:58,017 - INFO - Processing PDF from S3...
2025-09-24 17:55:58,017 - INFO - Downloading PDF from S3 to /tmp/tmpkpgmlsvn/9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:55:58,095 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 17:55:58,095 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:58,098 - INFO - Splitting PDF 97fc811d_NMAGOA3H1CROTNXQ4GR8 into 7 pages
2025-09-24 17:55:58,135 - INFO - Split PDF into 7 pages
2025-09-24 17:55:58,135 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:58,135 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 17:55:58,593 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ.pdf -> s3://document-extraction-logistically/temp/b43da5d8_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:55:58,594 - INFO - 🔍 [17:55:58] Starting classification: W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:55:58,595 - INFO - ⬆️ [17:55:58] Uploading: Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:55:58,595 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:58,618 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:58,624 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b43da5d8_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:55:58,624 - INFO - Processing PDF from S3...
2025-09-24 17:55:58,625 - INFO - Downloading PDF from S3 to /tmp/tmpg6h1wl3q/b43da5d8_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:55:58,717 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:55:58,717 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:58,718 - INFO - Splitting PDF 92786cba_NMWG7N543A7USM9VG3WI into 1 pages
2025-09-24 17:55:58,721 - INFO - Split PDF into 1 pages
2025-09-24 17:55:58,721 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:58,721 - INFO - Expected pages: [1]
2025-09-24 17:55:59,112 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 17:55:59,113 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:59,114 - INFO - Splitting PDF 8e4d6c87_QW3BG1TZMWHOTTZKGA8Y into 2 pages
2025-09-24 17:55:59,170 - INFO - Split PDF into 2 pages
2025-09-24 17:55:59,170 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:59,170 - INFO - Expected pages: [1, 2]
2025-09-24 17:55:59,252 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/Z7I9W27YVP7F6SPWV8UK.pdf -> s3://document-extraction-logistically/temp/7237ae51_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:55:59,252 - INFO - 🔍 [17:55:59] Starting classification: Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:55:59,254 - INFO - Initializing TextractProcessor...
2025-09-24 17:55:59,270 - INFO - Initializing BedrockProcessor...
2025-09-24 17:55:59,273 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7237ae51_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:55:59,274 - INFO - Processing PDF from S3...
2025-09-24 17:55:59,274 - INFO - Downloading PDF from S3 to /tmp/tmp7_zlmc6k/7237ae51_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:55:59,274 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:55:59,279 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:59,280 - INFO - Splitting PDF 4e5e76c6_ULZ4VHKA9IH82VWCSF3C into 1 pages
2025-09-24 17:55:59,282 - INFO - Split PDF into 1 pages
2025-09-24 17:55:59,282 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:59,283 - INFO - Expected pages: [1]
2025-09-24 17:55:59,876 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:55:59,876 - INFO - Splitting PDF into individual pages...
2025-09-24 17:55:59,877 - INFO - Splitting PDF 9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1) into 1 pages
2025-09-24 17:55:59,882 - INFO - Split PDF into 1 pages
2025-09-24 17:55:59,882 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:55:59,882 - INFO - Expected pages: [1]
2025-09-24 17:56:00,733 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:56:00,734 - INFO - Splitting PDF into individual pages...
2025-09-24 17:56:00,736 - INFO - Splitting PDF b43da5d8_W7K89V5JE3EN9H4SOSTZ into 1 pages
2025-09-24 17:56:00,739 - INFO - Split PDF into 1 pages
2025-09-24 17:56:00,739 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:56:00,740 - INFO - Expected pages: [1]
2025-09-24 17:56:01,028 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:56:01,029 - INFO - Splitting PDF into individual pages...
2025-09-24 17:56:01,030 - INFO - Splitting PDF 7237ae51_Z7I9W27YVP7F6SPWV8UK into 1 pages
2025-09-24 17:56:01,033 - INFO - Split PDF into 1 pages
2025-09-24 17:56:01,033 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:56:01,034 - INFO - Expected pages: [1]
2025-09-24 17:56:04,503 - INFO - Page 1: Extracted 545 characters, 28 lines from 70f11c37_BVLD9CCIUN6D2HGVETKO_350d8de2_page_001.pdf
2025-09-24 17:56:04,503 - INFO - Successfully processed page 1
2025-09-24 17:56:04,504 - INFO - Combined 1 pages into final text
2025-09-24 17:56:04,504 - INFO - Text validation for 70f11c37_BVLD9CCIUN6D2HGVETKO: 562 characters, 1 pages
2025-09-24 17:56:04,505 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:04,505 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:05,017 - INFO - Page 1: Extracted 1065 characters, 60 lines from 4e5e76c6_ULZ4VHKA9IH82VWCSF3C_48e4f3d6_page_001.pdf
2025-09-24 17:56:05,018 - INFO - Successfully processed page 1
2025-09-24 17:56:05,018 - INFO - Combined 1 pages into final text
2025-09-24 17:56:05,018 - INFO - Text validation for 4e5e76c6_ULZ4VHKA9IH82VWCSF3C: 1082 characters, 1 pages
2025-09-24 17:56:05,019 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:05,019 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:05,361 - INFO - Page 1: Extracted 1700 characters, 39 lines from 8e4d6c87_QW3BG1TZMWHOTTZKGA8Y_5b885e79_page_001.pdf
2025-09-24 17:56:05,362 - INFO - Successfully processed page 1
2025-09-24 17:56:05,465 - INFO - Page 1: Extracted 732 characters, 34 lines from b43da5d8_W7K89V5JE3EN9H4SOSTZ_b4929ca7_page_001.pdf
2025-09-24 17:56:05,478 - INFO - Page 1: Extracted 732 characters, 34 lines from 9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1)_21f49b1e_page_001.pdf
2025-09-24 17:56:05,478 - INFO - Successfully processed page 1
2025-09-24 17:56:05,484 - INFO - Successfully processed page 1
2025-09-24 17:56:05,488 - INFO - Page 1: Extracted 761 characters, 42 lines from 92786cba_NMWG7N543A7USM9VG3WI_9f2d6ace_page_001.pdf
2025-09-24 17:56:05,488 - INFO - Combined 1 pages into final text
2025-09-24 17:56:05,488 - INFO - Combined 1 pages into final text
2025-09-24 17:56:05,489 - INFO - Successfully processed page 1
2025-09-24 17:56:05,489 - INFO - Text validation for b43da5d8_W7K89V5JE3EN9H4SOSTZ: 749 characters, 1 pages
2025-09-24 17:56:05,489 - INFO - Text validation for 9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1): 749 characters, 1 pages
2025-09-24 17:56:05,489 - INFO - Combined 1 pages into final text
2025-09-24 17:56:05,489 - INFO - Text validation for 92786cba_NMWG7N543A7USM9VG3WI: 778 characters, 1 pages
2025-09-24 17:56:05,490 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:05,490 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:05,490 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:05,490 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:05,490 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:05,492 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:05,588 - INFO - Page 2: Extracted 335 characters, 15 lines from 8e4d6c87_QW3BG1TZMWHOTTZKGA8Y_5b885e79_page_002.pdf
2025-09-24 17:56:05,588 - INFO - Successfully processed page 2
2025-09-24 17:56:05,589 - INFO - Combined 2 pages into final text
2025-09-24 17:56:05,589 - INFO - Text validation for 8e4d6c87_QW3BG1TZMWHOTTZKGA8Y: 2071 characters, 2 pages
2025-09-24 17:56:05,590 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:05,590 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:05,607 - INFO - Page 4: Extracted 1535 characters, 35 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_004.pdf
2025-09-24 17:56:05,608 - INFO - Successfully processed page 4
2025-09-24 17:56:05,695 - INFO - Page 6: Extracted 1677 characters, 38 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_006.pdf
2025-09-24 17:56:05,696 - INFO - Successfully processed page 6
2025-09-24 17:56:05,759 - INFO - Page 1: Extracted 1186 characters, 37 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_001.pdf
2025-09-24 17:56:05,760 - INFO - Successfully processed page 1
2025-09-24 17:56:05,834 - INFO - Page 3: Extracted 1672 characters, 51 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_003.pdf
2025-09-24 17:56:05,834 - INFO - Successfully processed page 3
2025-09-24 17:56:05,862 - INFO - Page 5: Extracted 1454 characters, 40 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_005.pdf
2025-09-24 17:56:05,863 - INFO - Successfully processed page 5
2025-09-24 17:56:06,171 - INFO - Page 2: Extracted 1897 characters, 42 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_002.pdf
2025-09-24 17:56:06,171 - INFO - Successfully processed page 2
2025-09-24 17:56:06,535 - INFO - Page 1: Extracted 736 characters, 39 lines from 7237ae51_Z7I9W27YVP7F6SPWV8UK_c8160d22_page_001.pdf
2025-09-24 17:56:06,536 - INFO - Successfully processed page 1
2025-09-24 17:56:06,536 - INFO - Combined 1 pages into final text
2025-09-24 17:56:06,536 - INFO - Text validation for 7237ae51_Z7I9W27YVP7F6SPWV8UK: 753 characters, 1 pages
2025-09-24 17:56:06,537 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:06,537 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:07,069 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c967b6c6-daf3-4a82-9b21-f23962037f72', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:06 GMT', 'content-type': 'application/json', 'content-length': '1479', 'connection': 'keep-alive', 'x-amzn-requestid': 'c967b6c6-daf3-4a82-9b21-f23962037f72'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s an ACH Payment Notification. Does it match any category? The categories are logistics documents: invoice, comm_invoice, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, tender_from_cust, so_confirmation, ingate, outgate. This appears to be a payment notification, not a standard invoice. It might be "clear_to_pay"? That is an authorization indicating invoice is approved for payment. But our page says "ACH PAYMENT NOTIFICATION" and "Approved ACH from CargoSprint is being sent to Sunjin Shipping." That indicates payment is being sent. It\'s not a "clear to pay" stamp but a notification of payment. It seems like a payment notice. None of the categories exactly match. Could be "other". There\'s no explicit doc type header like "Invoice". It has Payment Amount, etc. But it\'s not a standard invoice. So classify as other. Return classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "other".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2677, 'outputTokens': 266, 'totalTokens': 2943}, 'metrics': {'latencyMs': 1319}}
2025-09-24 17:56:07,069 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/70f11c37_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:56:07,078 - INFO - 

BVLD9CCIUN6D2HGVETKO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 17:56:07,078 - INFO - 

✓ Saved result: output/run1_BVLD9CCIUN6D2HGVETKO.json
2025-09-24 17:56:07,327 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '33d73ea9-b414-4405-9b7b-6131e7322d04', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:07 GMT', 'content-type': 'application/json', 'content-length': '666', 'connection': 'keep-alive', 'x-amzn-requestid': '33d73ea9-b414-4405-9b7b-6131e7322d04'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify page 1. Text indicates "Truckload Check-in Form Clear-to-Pay Voucher". Contains "Clear-to-Pay Voucher". Also includes "Load Cost", "Net Cost", "Manager Approval". This looks like a Clear to Pay document. So doc_type: clear_to_pay. There\'s no indication it\'s a continuation. So output page 1 with doc_type clear_to_pay.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}] }'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2754, 'outputTokens': 99, 'totalTokens': 2853}, 'metrics': {'latencyMs': 646}}
2025-09-24 17:56:07,327 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:56:07,403 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5d8cf66e-4a8e-4192-8c30-e7053f510822', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:07 GMT', 'content-type': 'application/json', 'content-length': '735', 'connection': 'keep-alive', 'x-amzn-requestid': '5d8cf66e-4a8e-4192-8c30-e7053f510822'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have one page. Text shows "Truckload Check-in Form" and "Clear-to-Pay Voucher". That seems a clear-to-pay document. It is a payment authorization. Keywords: "Clear-to-Pay Voucher". So doc_type: clear_to_pay.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "clear_to_pay".\n\nReturn JSON via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2754, 'outputTokens': 114, 'totalTokens': 2868}, 'metrics': {'latencyMs': 649}}
2025-09-24 17:56:07,403 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b43da5d8_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:56:07,406 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '81780f12-616e-48ae-aeb7-d7f70f90fe2b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:07 GMT', 'content-type': 'application/json', 'content-length': '681', 'connection': 'keep-alive', 'x-amzn-requestid': '81780f12-616e-48ae-aeb7-d7f70f90fe2b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The text indicates "Truckload Check-in Form" and "Clear-to-Pay Voucher". The keywords: "Clear-to-Pay Voucher" and "Clear-to-Pay" appears. So doc_type should be clear_to_pay. The page number? Not necessary. We just output array with objects. It\'s single page. Output: [{"page_no":1,"doc_type":"clear_to_pay"}] via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2763, 'outputTokens': 110, 'totalTokens': 2873}, 'metrics': {'latencyMs': 733}}
2025-09-24 17:56:07,408 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/92786cba_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:56:07,876 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a0d1e1e3-e3ab-4ff4-be43-d0fc155109f2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:07 GMT', 'content-type': 'application/json', 'content-length': '1728', 'connection': 'keep-alive', 'x-amzn-requestid': 'a0d1e1e3-e3ab-4ff4-be43-d0fc155109f2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. We need to examine the text. It\'s an "Approval Confirmation for PayCargo Invoice 08AIR44777" with Payment Details, Total, etc. It contains terms: "Invoice", "Amount", "Total". It seems like a carrier invoice? But it\'s a payment approval confirmation. The heading says Approval Confirmation for PayCargo Invoice. Could be a "clear to pay" or "invoice"? There is "Approved" status. But it\'s not a standard invoice (no line items, no bill to/from). It seems more like an approval confirmation for an invoice. The categories: "clear_to_pay" is for authorization indicating invoice is approved for payment. That matches "Approved" status. There\'s "Approval Confirmation" heading. There\'s "Approval Date", "Status Approved". So it likely is a Clear to Pay document. The doc type "clear_to_pay" is for authorization indicating invoice is approved for payment. This page seems to be a clearance approval for a PayCargo invoice. So classify as clear_to_pay.\n\nCheck if any other categories: invoice? It says "Invoice" type. But it\'s not a typical invoice. It\'s a payment approval. So "clear_to_pay" seems appropriate.\n\nNow output with tool call. The tool expects an array of objects with page_no and doc_type. Only one page. So:\n\n{\n  documents: [\n    { page_no: 1, doc_type: "clear_to_pay" }\n  ]\n}\n\nLet\'s produce that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2837, 'outputTokens': 325, 'totalTokens': 3162}, 'metrics': {'latencyMs': 1591}}
2025-09-24 17:56:07,877 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4e5e76c6_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:56:07,886 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3d709f5d-3c71-4856-9c19-0a54689eb099', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:07 GMT', 'content-type': 'application/json', 'content-length': '1131', 'connection': 'keep-alive', 'x-amzn-requestid': '3d709f5d-3c71-4856-9c19-0a54689eb099'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Content: appears to be email correspondence. Not a logistics document like invoice or BOL. It\'s just an email. So classification: this is not part of a recognized doc type. Likely "other". But we need to check if page2 is continuation of page1. Page2 content: "6/20/24, 8:59 AM Mail - Antoine Oda - Outlook ... 2/2". The page ends with "2/2". That suggests page1 had "1/2" at end. So page1 is first half of a two-page email. So page2 is continuation of same email. For classification we assign same doc_type to both pages. Since email is not a recognized logistic document, we classify as "other".\n\nThus output: two objects with page_no 1 and 2, doc_type "other". Ensure strict format: use tool classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3226, 'outputTokens': 219, 'totalTokens': 3445}, 'metrics': {'latencyMs': 1107}}
2025-09-24 17:56:07,887 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8e4d6c87_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:56:08,147 - INFO - Page 1: Extracted 1392 characters, 59 lines from fe215fe4_ILOJODH5ADT10MHGK0TI_e72e21b9_page_001.pdf
2025-09-24 17:56:08,147 - INFO - Successfully processed page 1
2025-09-24 17:56:08,147 - INFO - Combined 1 pages into final text
2025-09-24 17:56:08,147 - INFO - Text validation for fe215fe4_ILOJODH5ADT10MHGK0TI: 1409 characters, 1 pages
2025-09-24 17:56:08,148 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:08,148 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:08,234 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/70f11c37_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 17:56:08,263 - INFO - 

W7K89V5JE3EN9H4SOSTZ (1).pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 17:56:08,263 - INFO - 

✓ Saved result: output/run1_W7K89V5JE3EN9H4SOSTZ (1).json
2025-09-24 17:56:08,558 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9e0ff25e_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 17:56:08,566 - INFO - 

W7K89V5JE3EN9H4SOSTZ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 17:56:08,566 - INFO - 

✓ Saved result: output/run1_W7K89V5JE3EN9H4SOSTZ.json
2025-09-24 17:56:08,850 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b43da5d8_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 17:56:08,853 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bf0352f0-a1f7-4a84-96f9-bd7cafb4d65d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:08 GMT', 'content-type': 'application/json', 'content-length': '1024', 'connection': 'keep-alive', 'x-amzn-requestid': 'bf0352f0-a1f7-4a84-96f9-bd7cafb4d65d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to determine its doc_type. The text: "Truckload Check-in Form Clear-to-Pay Voucher". The document includes "Clear-to-Pay Voucher" header. So likely "clear_to_pay". The doc_type "clear_to_pay" is defined: Authorization indicating invoice is approved for payment. Keywords: "Clear to Pay", "Approved for Payment", "Payment Authorization", "Clear to Pay Stamp". This page includes "Clear-to-Pay Voucher". So doc_type is clear_to_pay. Also check for continuing? No. So output array with one object.\n\nWe must use tool classify_logistics_doc_type with documents array containing object with page_no and doc_type. So call function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2758, 'outputTokens': 171, 'totalTokens': 2929}, 'metrics': {'latencyMs': 931}}
2025-09-24 17:56:08,855 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7237ae51_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:56:08,880 - INFO - 

NMWG7N543A7USM9VG3WI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 17:56:08,880 - INFO - 

✓ Saved result: output/run1_NMWG7N543A7USM9VG3WI.json
2025-09-24 17:56:09,132 - INFO - Page 7: Extracted 500 characters, 15 lines from 97fc811d_NMAGOA3H1CROTNXQ4GR8_16949762_page_007.pdf
2025-09-24 17:56:09,133 - INFO - Successfully processed page 7
2025-09-24 17:56:09,133 - INFO - Combined 7 pages into final text
2025-09-24 17:56:09,134 - INFO - Text validation for 97fc811d_NMAGOA3H1CROTNXQ4GR8: 10052 characters, 7 pages
2025-09-24 17:56:09,136 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:56:09,136 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:56:09,192 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/92786cba_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 17:56:09,216 - INFO - 

ULZ4VHKA9IH82VWCSF3C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 17:56:09,216 - INFO - 

✓ Saved result: output/run1_ULZ4VHKA9IH82VWCSF3C.json
2025-09-24 17:56:09,513 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4e5e76c6_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 17:56:09,531 - INFO - 

QW3BG1TZMWHOTTZKGA8Y.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 17:56:09,531 - INFO - 

✓ Saved result: output/run1_QW3BG1TZMWHOTTZKGA8Y.json
2025-09-24 17:56:09,832 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8e4d6c87_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 17:56:09,853 - INFO - 

Z7I9W27YVP7F6SPWV8UK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 17:56:09,853 - INFO - 

✓ Saved result: output/run1_Z7I9W27YVP7F6SPWV8UK.json
2025-09-24 17:56:10,102 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a3de79e7-44ad-4dbb-9973-72f578ded7ff', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:09 GMT', 'content-type': 'application/json', 'content-length': '696', 'connection': 'keep-alive', 'x-amzn-requestid': 'a3de79e7-44ad-4dbb-9973-72f578ded7ff'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page document. It\'s a bank wire transfer request form. It is not any of the logistics document types described. So we should classify as "other". The instructions say: "If a page is part of a multi-page single document, each page still gets the same doc_type." Here only one page.\n\nThus we return tool call with documents array: page_no:1, doc_type:"other".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2866, 'outputTokens': 101, 'totalTokens': 2967}, 'metrics': {'latencyMs': 697}}
2025-09-24 17:56:10,103 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fe215fe4_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:56:10,161 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7237ae51_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 17:56:10,185 - INFO - 

ILOJODH5ADT10MHGK0TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 17:56:10,185 - INFO - 

✓ Saved result: output/run1_ILOJODH5ADT10MHGK0TI.json
2025-09-24 17:56:10,479 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fe215fe4_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 17:56:12,037 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dc0e5df5-c6a9-400e-9700-ec282117a146', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:26:11 GMT', 'content-type': 'application/json', 'content-length': '1771', 'connection': 'keep-alive', 'x-amzn-requestid': 'dc0e5df5-c6a9-400e-9700-ec282117a146'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 7 pages. They are email exchanges, not documents like invoices. Likely classification is "other" for each page, but maybe some pages contain a bill of lading attachment. But the content is email text. The classification requires each page assigned a type. We need to check if any page contains a document like "bill of lading" text. Page5 includes "OBOL" and "BOL". But the actual BOL is likely an attachment not shown. The page text includes "Here is the OBOL that was given to the partnership carrier. I see the BOL states to bill the third party below but has no address to bill on this." But no actual BOL content. So likely "other". But check page6: "This message originated outside of ... Please see the attached bill of lading". So attachments are referenced but not content. So pages are email body. So classify as "other".\n\nBut check page1: It\'s an email with subject and attachments. No actual invoice. So "other".\n\nThus output array with 7 objects, each page_no and doc_type "other".\n\nBut we need to consider continuation: no continuation. So each page gets its own doc type.\n\nWe must call the function classify_logistics_doc_type with documents array.\n\nLet\'s produce JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"},{"page_no":4,"doc_type":"other"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 5601, 'outputTokens': 342, 'totalTokens': 5943}, 'metrics': {'latencyMs': 1620}}
2025-09-24 17:56:12,038 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/97fc811d_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 17:56:12,120 - INFO - 

NMAGOA3H1CROTNXQ4GR8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        },
        {
            "page_no": 4,
            "doc_type": "other"
        },
        {
            "page_no": 5,
            "doc_type": "other"
        },
        {
            "page_no": 6,
            "doc_type": "other"
        },
        {
            "page_no": 7,
            "doc_type": "other"
        }
    ]
}
2025-09-24 17:56:12,120 - INFO - 

✓ Saved result: output/run1_NMAGOA3H1CROTNXQ4GR8.json
