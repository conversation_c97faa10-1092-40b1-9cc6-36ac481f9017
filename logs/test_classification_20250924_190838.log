2025-09-24 19:08:38,969 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190838.log
2025-09-24 19:08:38,970 - INFO - 📁 Found 16 files to process
2025-09-24 19:08:38,970 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:08:38,970 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-24 19:08:38,970 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-24 19:08:38,970 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-24 19:08:38,970 - INFO - ⬆️ [19:08:38] Uploading: 6_scale_ticket_1.pdf
2025-09-24 19:08:42,043 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/4767d62e_6_scale_ticket_1.pdf
2025-09-24 19:08:42,043 - INFO - 🔍 [19:08:42] Starting classification: 6_scale_ticket_1.pdf
2025-09-24 19:08:42,044 - INFO - ⬆️ [19:08:42] Uploading: 6_scale_ticket_10.png
2025-09-24 19:08:42,045 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:42,056 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:42,067 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4767d62e_6_scale_ticket_1.pdf
2025-09-24 19:08:42,068 - INFO - Processing PDF from S3...
2025-09-24 19:08:42,068 - INFO - Downloading PDF from S3 to /tmp/tmpqfl8vh0u/4767d62e_6_scale_ticket_1.pdf
2025-09-24 19:08:44,898 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:08:44,898 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:44,901 - INFO - Splitting PDF 4767d62e_6_scale_ticket_1 into 1 pages
2025-09-24 19:08:44,904 - INFO - Split PDF into 1 pages
2025-09-24 19:08:44,904 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:44,904 - INFO - Expected pages: [1]
2025-09-24 19:08:45,876 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/0da44b1d_6_scale_ticket_10.png
2025-09-24 19:08:45,876 - INFO - 🔍 [19:08:45] Starting classification: 6_scale_ticket_10.png
2025-09-24 19:08:45,877 - INFO - ⬆️ [19:08:45] Uploading: 6_scale_ticket_11.jpg
2025-09-24 19:08:45,878 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:45,892 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:45,896 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0da44b1d_6_scale_ticket_10.png
2025-09-24 19:08:45,896 - INFO - Processing image from S3...
2025-09-24 19:08:49,756 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg -> s3://document-extraction-logistically/temp/09476ade_6_scale_ticket_11.jpg
2025-09-24 19:08:49,756 - INFO - 🔍 [19:08:49] Starting classification: 6_scale_ticket_11.jpg
2025-09-24 19:08:49,757 - INFO - ⬆️ [19:08:49] Uploading: 6_scale_ticket_12.jpg
2025-09-24 19:08:49,759 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:49,774 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:49,778 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/09476ade_6_scale_ticket_11.jpg
2025-09-24 19:08:49,778 - INFO - Processing image from S3...
2025-09-24 19:08:50,563 - INFO - S3 Image temp/0da44b1d_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-24 19:08:50,563 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:50,563 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:51,168 - INFO - Page 1: Extracted 1382 characters, 62 lines from 4767d62e_6_scale_ticket_1_b06c99c1_page_001.pdf
2025-09-24 19:08:51,168 - INFO - Successfully processed page 1
2025-09-24 19:08:51,168 - INFO - Combined 1 pages into final text
2025-09-24 19:08:51,168 - INFO - Text validation for 4767d62e_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-24 19:08:51,169 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:51,169 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:52,261 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg -> s3://document-extraction-logistically/temp/7dc4bad1_6_scale_ticket_12.jpg
2025-09-24 19:08:52,262 - INFO - 🔍 [19:08:52] Starting classification: 6_scale_ticket_12.jpg
2025-09-24 19:08:52,262 - INFO - ⬆️ [19:08:52] Uploading: 6_scale_ticket_13.jpg
2025-09-24 19:08:52,264 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:52,280 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:52,284 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7dc4bad1_6_scale_ticket_12.jpg
2025-09-24 19:08:52,284 - INFO - Processing image from S3...
2025-09-24 19:08:52,371 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '69e9d51f-e94e-4c26-bd37-69c200f84383', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:52 GMT', 'content-type': 'application/json', 'content-length': '574', 'connection': 'keep-alive', 'x-amzn-requestid': '69e9d51f-e94e-4c26-bd37-69c200f84383'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It looks like a Scale Ticket. Contains weight data, LBS. GROSS, NET, etc. So doc_type "scale_ticket". No continuation signals. So output page 1 as scale_ticket.\n\nReturn JSON with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2867, 'outputTokens': 77, 'totalTokens': 2944}, 'metrics': {'latencyMs': 550}}
2025-09-24 19:08:52,371 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0da44b1d_6_scale_ticket_10.png
2025-09-24 19:08:53,009 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg -> s3://document-extraction-logistically/temp/9018a6f7_6_scale_ticket_13.jpg
2025-09-24 19:08:53,009 - INFO - 🔍 [19:08:53] Starting classification: 6_scale_ticket_13.jpg
2025-09-24 19:08:53,010 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:53,010 - INFO - ⬆️ [19:08:53] Uploading: 6_scale_ticket_14.jpg
2025-09-24 19:08:53,022 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:53,028 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9018a6f7_6_scale_ticket_13.jpg
2025-09-24 19:08:53,028 - INFO - Processing image from S3...
2025-09-24 19:08:53,202 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'db2812c3-92f0-41fb-8fdc-10346d0faf1e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:53 GMT', 'content-type': 'application/json', 'content-length': '754', 'connection': 'keep-alive', 'x-amzn-requestid': 'db2812c3-92f0-41fb-8fdc-10346d0faf1e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It has scale data: Gross, Tare, Net, Tons, etc. It\'s a weight record. Contains "Gross: 73860", "Tare: 33120", "Net: 40740". It also has a ticket number, date, time. It\'s likely a Scale Ticket. Also has "Ticket:" and "Date:" etc. So doc_type: scale_ticket. No continuation indication. Single page.\n\nThus return JSON with documents array containing one object: page_no: 1, doc_type: "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3151, 'outputTokens': 133, 'totalTokens': 3284}, 'metrics': {'latencyMs': 718}}
2025-09-24 19:08:53,202 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4767d62e_6_scale_ticket_1.pdf
2025-09-24 19:08:54,112 - INFO - S3 Image temp/09476ade_6_scale_ticket_11.jpg: Extracted 361 characters, 32 lines
2025-09-24 19:08:54,113 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:54,113 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:55,135 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg -> s3://document-extraction-logistically/temp/a9e7d940_6_scale_ticket_14.jpg
2025-09-24 19:08:55,135 - INFO - 🔍 [19:08:55] Starting classification: 6_scale_ticket_14.jpg
2025-09-24 19:08:55,136 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:55,137 - INFO - ⬆️ [19:08:55] Uploading: 6_scale_ticket_15.pdf
2025-09-24 19:08:55,141 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:55,144 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a9e7d940_6_scale_ticket_14.jpg
2025-09-24 19:08:55,144 - INFO - Processing image from S3...
2025-09-24 19:08:55,759 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf -> s3://document-extraction-logistically/temp/345ebd0c_6_scale_ticket_15.pdf
2025-09-24 19:08:55,759 - INFO - 🔍 [19:08:55] Starting classification: 6_scale_ticket_15.pdf
2025-09-24 19:08:55,760 - INFO - ⬆️ [19:08:55] Uploading: 6_scale_ticket_16.pdf
2025-09-24 19:08:55,762 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:55,813 - INFO - S3 Image temp/7dc4bad1_6_scale_ticket_12.jpg: Extracted 360 characters, 32 lines
2025-09-24 19:08:55,815 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:55,815 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:55,818 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:55,821 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/345ebd0c_6_scale_ticket_15.pdf
2025-09-24 19:08:55,821 - INFO - Processing PDF from S3...
2025-09-24 19:08:55,821 - INFO - Downloading PDF from S3 to /tmp/tmpbf9qhmpr/345ebd0c_6_scale_ticket_15.pdf
2025-09-24 19:08:56,314 - INFO - S3 Image temp/9018a6f7_6_scale_ticket_13.jpg: Extracted 904 characters, 46 lines
2025-09-24 19:08:56,314 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:56,314 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:56,467 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3d0c61ab-2186-4a40-81dd-a9884428233e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:56 GMT', 'content-type': 'application/json', 'content-length': '1008', 'connection': 'keep-alive', 'x-amzn-requestid': '3d0c61ab-2186-4a40-81dd-a9884428233e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page: page1. The content seems like a scale ticket: includes Gross, Tare, Net, weight in tons and pounds, date in, date out, driver, trailer, load number, etc. Keywords: Gross, Tare, Net, Weighed At, Ticket no? There is "Trip Ticket BIR0029912". So it\'s a Scale Ticket. According to definition, a scale ticket includes weight-related keywords: Gross, Tare, Net, etc. So doc_type = "scale_ticket". Must output JSON with documents array with one object: page_no:1 doc_type:"scale_ticket".\n\nCheck if any other types? There\'s BOL mentioned but it\'s part of header maybe. But the main content is weight. So scale_ticket.\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2754, 'outputTokens': 183, 'totalTokens': 2937}, 'metrics': {'latencyMs': 997}}
2025-09-24 19:08:56,467 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/09476ade_6_scale_ticket_11.jpg
2025-09-24 19:08:56,615 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf -> s3://document-extraction-logistically/temp/35520a0b_6_scale_ticket_16.pdf
2025-09-24 19:08:56,615 - INFO - 🔍 [19:08:56] Starting classification: 6_scale_ticket_16.pdf
2025-09-24 19:08:56,616 - INFO - ⬆️ [19:08:56] Uploading: 6_scale_ticket_2.pdf
2025-09-24 19:08:56,617 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:56,631 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:56,637 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/35520a0b_6_scale_ticket_16.pdf
2025-09-24 19:08:56,637 - INFO - Processing PDF from S3...
2025-09-24 19:08:56,638 - INFO - Downloading PDF from S3 to /tmp/tmp2k08okju/35520a0b_6_scale_ticket_16.pdf
2025-09-24 19:08:57,396 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf -> s3://document-extraction-logistically/temp/4a242f4d_6_scale_ticket_2.pdf
2025-09-24 19:08:57,396 - INFO - 🔍 [19:08:57] Starting classification: 6_scale_ticket_2.pdf
2025-09-24 19:08:57,397 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:57,397 - INFO - ⬆️ [19:08:57] Uploading: 6_scale_ticket_3.pdf
2025-09-24 19:08:57,409 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:57,416 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4a242f4d_6_scale_ticket_2.pdf
2025-09-24 19:08:57,417 - INFO - Processing PDF from S3...
2025-09-24 19:08:57,417 - INFO - Downloading PDF from S3 to /tmp/tmpe270h17x/4a242f4d_6_scale_ticket_2.pdf
2025-09-24 19:08:57,954 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:08:57,954 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:57,955 - INFO - Splitting PDF 345ebd0c_6_scale_ticket_15 into 1 pages
2025-09-24 19:08:57,958 - INFO - Split PDF into 1 pages
2025-09-24 19:08:57,958 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:57,958 - INFO - Expected pages: [1]
2025-09-24 19:08:57,987 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '861e28d3-7929-4a57-b63c-355b644a21d1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:57 GMT', 'content-type': 'application/json', 'content-length': '817', 'connection': 'keep-alive', 'x-amzn-requestid': '861e28d3-7929-4a57-b63c-355b644a21d1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page? The user provided <page1> content. It\'s a scale ticket? Let\'s examine: contains "BOL", "Trip Ticket", "Load #", "Gross", "Tare", "Net", "Date In", "Date Out", weight units, driver, etc. This matches scale ticket: weight-related keywords: Gross, Tare, Net, Weighed At (Date In/Out), Truck, Driver. So doc_type = scale_ticket.\n\nThus output JSON with documents array containing one object: page_no 1, doc_type "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2754, 'outputTokens': 142, 'totalTokens': 2896}, 'metrics': {'latencyMs': 851}}
2025-09-24 19:08:57,988 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7dc4bad1_6_scale_ticket_12.jpg
2025-09-24 19:08:58,019 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf -> s3://document-extraction-logistically/temp/ba1cd5ca_6_scale_ticket_3.pdf
2025-09-24 19:08:58,019 - INFO - 🔍 [19:08:58] Starting classification: 6_scale_ticket_3.pdf
2025-09-24 19:08:58,020 - INFO - ⬆️ [19:08:58] Uploading: 6_scale_ticket_4.pdf
2025-09-24 19:08:58,022 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:58,043 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:58,047 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba1cd5ca_6_scale_ticket_3.pdf
2025-09-24 19:08:58,047 - INFO - Processing PDF from S3...
2025-09-24 19:08:58,048 - INFO - Downloading PDF from S3 to /tmp/tmppr9ibxhu/ba1cd5ca_6_scale_ticket_3.pdf
2025-09-24 19:08:58,547 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2417da7d-840e-4293-b429-5dd40f79383e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:58 GMT', 'content-type': 'application/json', 'content-length': '977', 'connection': 'keep-alive', 'x-amzn-requestid': '2417da7d-840e-4293-b429-5dd40f79383e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The page contains scale ticket info: "Gross", "Tare", "Net", "Weight", "Scale", "Tons". Also includes Bill of Lading header. But main focus is scale ticket: weight record from a scale. So doc_type likely "scale_ticket". It also has BOL header but that might be included as part of scale ticket page. According to definitions, if page contains weight-related keywords like Gross, Tare, Net, Weighed At, etc, classify as scale_ticket. So assign "scale_ticket". Only one page. So output JSON with documents array containing page_no 1, doc_type "scale_ticket".'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "scale_ticket"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2963, 'outputTokens': 173, 'totalTokens': 3136}, 'metrics': {'latencyMs': 968}}
2025-09-24 19:08:58,547 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9018a6f7_6_scale_ticket_13.jpg
2025-09-24 19:08:58,704 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf -> s3://document-extraction-logistically/temp/1c67ee71_6_scale_ticket_4.pdf
2025-09-24 19:08:58,705 - INFO - 🔍 [19:08:58] Starting classification: 6_scale_ticket_4.pdf
2025-09-24 19:08:58,709 - INFO - ⬆️ [19:08:58] Uploading: 6_scale_ticket_5.pdf
2025-09-24 19:08:58,710 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:58,730 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:58,733 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1c67ee71_6_scale_ticket_4.pdf
2025-09-24 19:08:58,733 - INFO - Processing PDF from S3...
2025-09-24 19:08:58,733 - INFO - Downloading PDF from S3 to /tmp/tmp3jhix0h_/1c67ee71_6_scale_ticket_4.pdf
2025-09-24 19:08:59,371 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf -> s3://document-extraction-logistically/temp/d1490326_6_scale_ticket_5.pdf
2025-09-24 19:08:59,371 - INFO - 🔍 [19:08:59] Starting classification: 6_scale_ticket_5.pdf
2025-09-24 19:08:59,372 - INFO - ⬆️ [19:08:59] Uploading: 6_scale_ticket_6.pdf
2025-09-24 19:08:59,373 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:59,388 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:59,395 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d1490326_6_scale_ticket_5.pdf
2025-09-24 19:08:59,396 - INFO - Processing PDF from S3...
2025-09-24 19:08:59,397 - INFO - Downloading PDF from S3 to /tmp/tmpsea3mick/d1490326_6_scale_ticket_5.pdf
2025-09-24 19:08:59,482 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 19:08:59,483 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:59,484 - INFO - Splitting PDF 35520a0b_6_scale_ticket_16 into 1 pages
2025-09-24 19:08:59,490 - INFO - Split PDF into 1 pages
2025-09-24 19:08:59,490 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:59,490 - INFO - Expected pages: [1]
2025-09-24 19:08:59,502 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:08:59,502 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:59,504 - INFO - Splitting PDF 4a242f4d_6_scale_ticket_2 into 1 pages
2025-09-24 19:08:59,506 - INFO - Split PDF into 1 pages
2025-09-24 19:08:59,506 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:59,506 - INFO - Expected pages: [1]
2025-09-24 19:08:59,857 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:08:59,857 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:59,860 - INFO - Splitting PDF ba1cd5ca_6_scale_ticket_3 into 1 pages
2025-09-24 19:08:59,862 - INFO - Split PDF into 1 pages
2025-09-24 19:08:59,862 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:59,862 - INFO - Expected pages: [1]
2025-09-24 19:09:00,012 - INFO - S3 Image temp/a9e7d940_6_scale_ticket_14.jpg: Extracted 489 characters, 64 lines
2025-09-24 19:09:00,012 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:00,012 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:00,179 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf -> s3://document-extraction-logistically/temp/61b66182_6_scale_ticket_6.pdf
2025-09-24 19:09:00,179 - INFO - 🔍 [19:09:00] Starting classification: 6_scale_ticket_6.pdf
2025-09-24 19:09:00,180 - INFO - ⬆️ [19:09:00] Uploading: 6_scale_ticket_7.pdf
2025-09-24 19:09:00,181 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:00,201 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:00,206 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/61b66182_6_scale_ticket_6.pdf
2025-09-24 19:09:00,206 - INFO - Processing PDF from S3...
2025-09-24 19:09:00,206 - INFO - Downloading PDF from S3 to /tmp/tmpckktw8_l/61b66182_6_scale_ticket_6.pdf
2025-09-24 19:09:00,852 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf -> s3://document-extraction-logistically/temp/c4585511_6_scale_ticket_7.pdf
2025-09-24 19:09:00,853 - INFO - 🔍 [19:09:00] Starting classification: 6_scale_ticket_7.pdf
2025-09-24 19:09:00,853 - INFO - ⬆️ [19:09:00] Uploading: 6_scale_ticket_8.pdf
2025-09-24 19:09:00,855 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:00,875 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:00,881 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c4585511_6_scale_ticket_7.pdf
2025-09-24 19:09:00,881 - INFO - Processing PDF from S3...
2025-09-24 19:09:00,881 - INFO - Downloading PDF from S3 to /tmp/tmp5x8r7kcu/c4585511_6_scale_ticket_7.pdf
2025-09-24 19:09:00,917 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 19:09:00,917 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:00,920 - INFO - Splitting PDF 1c67ee71_6_scale_ticket_4 into 1 pages
2025-09-24 19:09:00,922 - INFO - Split PDF into 1 pages
2025-09-24 19:09:00,922 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:00,922 - INFO - Expected pages: [1]
2025-09-24 19:09:01,527 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf -> s3://document-extraction-logistically/temp/9b1a2efc_6_scale_ticket_8.pdf
2025-09-24 19:09:01,527 - INFO - 🔍 [19:09:01] Starting classification: 6_scale_ticket_8.pdf
2025-09-24 19:09:01,528 - INFO - ⬆️ [19:09:01] Uploading: 6_scale_ticket_9.pdf
2025-09-24 19:09:01,534 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:01,544 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:01,550 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9b1a2efc_6_scale_ticket_8.pdf
2025-09-24 19:09:01,550 - INFO - Processing PDF from S3...
2025-09-24 19:09:01,550 - INFO - Downloading PDF from S3 to /tmp/tmp1zqc801k/9b1a2efc_6_scale_ticket_8.pdf
2025-09-24 19:09:01,759 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:09:01,759 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:01,760 - INFO - Splitting PDF d1490326_6_scale_ticket_5 into 1 pages
2025-09-24 19:09:01,761 - INFO - Split PDF into 1 pages
2025-09-24 19:09:01,761 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:01,761 - INFO - Expected pages: [1]
2025-09-24 19:09:02,163 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '91f134e5-7195-4304-a244-310379d6328b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:01 GMT', 'content-type': 'application/json', 'content-length': '803', 'connection': 'keep-alive', 'x-amzn-requestid': '91f134e5-7195-4304-a244-310379d6328b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We must classify each page. Only one page provided: page1. Need to decide doc_type. Text is garbled but includes "GROSS 32980 lb INBOUND" "TARE 32980 1b RECALLED" "NET 45040 1b". That resembles a scale ticket (weight record). Also contains "GROSS", "TARE", "NET", weight units lb, 1b maybe lbs? Presence of weight keywords indicates scale_ticket. There\'s no other header. So classify as scale_ticket.\n\nThus output: {documents: [{page_no:1, doc_type:"scale_ticket"}]}'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2858, 'outputTokens': 145, 'totalTokens': 3003}, 'metrics': {'latencyMs': 860}}
2025-09-24 19:09:02,163 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a9e7d940_6_scale_ticket_14.jpg
2025-09-24 19:09:02,166 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf -> s3://document-extraction-logistically/temp/d699e8a4_6_scale_ticket_9.pdf
2025-09-24 19:09:02,166 - INFO - 🔍 [19:09:02] Starting classification: 6_scale_ticket_9.pdf
2025-09-24 19:09:02,166 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:02,175 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:02,178 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d699e8a4_6_scale_ticket_9.pdf
2025-09-24 19:09:02,178 - INFO - Processing PDF from S3...
2025-09-24 19:09:02,179 - INFO - Downloading PDF from S3 to /tmp/tmp8ahw1v6r/d699e8a4_6_scale_ticket_9.pdf
2025-09-24 19:09:02,194 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:02,194 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_10.json
2025-09-24 19:09:02,482 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0da44b1d_6_scale_ticket_10.png
2025-09-24 19:09:02,505 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:02,505 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_1.json
2025-09-24 19:09:02,809 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4767d62e_6_scale_ticket_1.pdf
2025-09-24 19:09:02,819 - INFO - 

6_scale_ticket_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:02,819 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_11.json
2025-09-24 19:09:03,056 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:09:03,056 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:03,058 - INFO - Splitting PDF c4585511_6_scale_ticket_7 into 1 pages
2025-09-24 19:09:03,059 - INFO - Split PDF into 1 pages
2025-09-24 19:09:03,059 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:03,060 - INFO - Expected pages: [1]
2025-09-24 19:09:03,104 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/09476ade_6_scale_ticket_11.jpg
2025-09-24 19:09:03,130 - INFO - Page 1: Extracted 678 characters, 29 lines from 345ebd0c_6_scale_ticket_15_fa1fcf6c_page_001.pdf
2025-09-24 19:09:03,132 - INFO - 

6_scale_ticket_12.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:03,132 - INFO - Successfully processed page 1
2025-09-24 19:09:03,133 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_12.json
2025-09-24 19:09:03,133 - INFO - Combined 1 pages into final text
2025-09-24 19:09:03,133 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 19:09:03,137 - INFO - Text validation for 345ebd0c_6_scale_ticket_15: 695 characters, 1 pages
2025-09-24 19:09:03,138 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:03,144 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:03,145 - INFO - Splitting PDF 61b66182_6_scale_ticket_6 into 1 pages
2025-09-24 19:09:03,145 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:03,149 - INFO - Split PDF into 1 pages
2025-09-24 19:09:03,149 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:03,149 - INFO - Expected pages: [1]
2025-09-24 19:09:03,424 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7dc4bad1_6_scale_ticket_12.jpg
2025-09-24 19:09:03,448 - INFO - 

6_scale_ticket_13.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:03,449 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_13.json
2025-09-24 19:09:03,707 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:09:03,707 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:03,709 - INFO - Splitting PDF 9b1a2efc_6_scale_ticket_8 into 1 pages
2025-09-24 19:09:03,710 - INFO - Split PDF into 1 pages
2025-09-24 19:09:03,711 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:03,711 - INFO - Expected pages: [1]
2025-09-24 19:09:03,744 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9018a6f7_6_scale_ticket_13.jpg
2025-09-24 19:09:03,763 - INFO - 

6_scale_ticket_14.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:03,763 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_14.json
2025-09-24 19:09:03,983 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:09:03,983 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:03,984 - INFO - Splitting PDF d699e8a4_6_scale_ticket_9 into 1 pages
2025-09-24 19:09:03,986 - INFO - Split PDF into 1 pages
2025-09-24 19:09:03,986 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:03,986 - INFO - Expected pages: [1]
2025-09-24 19:09:04,044 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a9e7d940_6_scale_ticket_14.jpg
2025-09-24 19:09:05,224 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '585bf681-2aec-4edd-ba44-988ce87f653b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:05 GMT', 'content-type': 'application/json', 'content-length': '829', 'connection': 'keep-alive', 'x-amzn-requestid': '585bf681-2aec-4edd-ba44-988ce87f653b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page provided. Determine document type. The content includes "Ticket", "Scale", "Gross", "Tare", "Net", "Tons", "Weighmaster". Also "Ticket 1242095", "Date", "Time", "Truck", "Trailer", "Destination", "P.O.", "D.O.", "Seal". Likely a Scale Ticket. It includes weight info. So doc_type = "scale_ticket". Ensure no other candidate. It\'s not invoice, not pod, etc. It\'s a scale ticket. So output JSON with one object page_no 1 scale_ticket.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2839, 'outputTokens': 146, 'totalTokens': 2985}, 'metrics': {'latencyMs': 859}}
2025-09-24 19:09:05,224 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/345ebd0c_6_scale_ticket_15.pdf
2025-09-24 19:09:05,243 - INFO - 

6_scale_ticket_15.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:05,243 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_15.json
2025-09-24 19:09:05,532 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/345ebd0c_6_scale_ticket_15.pdf
2025-09-24 19:09:05,656 - INFO - Page 1: Extracted 1041 characters, 72 lines from 4a242f4d_6_scale_ticket_2_a84ee512_page_001.pdf
2025-09-24 19:09:05,657 - INFO - Successfully processed page 1
2025-09-24 19:09:05,657 - INFO - Combined 1 pages into final text
2025-09-24 19:09:05,657 - INFO - Text validation for 4a242f4d_6_scale_ticket_2: 1058 characters, 1 pages
2025-09-24 19:09:05,658 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:05,658 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:05,672 - INFO - Page 1: Extracted 702 characters, 33 lines from ba1cd5ca_6_scale_ticket_3_812fac1a_page_001.pdf
2025-09-24 19:09:05,672 - INFO - Successfully processed page 1
2025-09-24 19:09:05,673 - INFO - Combined 1 pages into final text
2025-09-24 19:09:05,673 - INFO - Text validation for ba1cd5ca_6_scale_ticket_3: 719 characters, 1 pages
2025-09-24 19:09:05,673 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:05,673 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:06,215 - INFO - Page 1: Extracted 2006 characters, 95 lines from 35520a0b_6_scale_ticket_16_5bbc51a4_page_001.pdf
2025-09-24 19:09:06,215 - INFO - Successfully processed page 1
2025-09-24 19:09:06,215 - INFO - Combined 1 pages into final text
2025-09-24 19:09:06,215 - INFO - Text validation for 35520a0b_6_scale_ticket_16: 2023 characters, 1 pages
2025-09-24 19:09:06,216 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:06,216 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:06,920 - INFO - Page 1: Extracted 421 characters, 38 lines from 1c67ee71_6_scale_ticket_4_3d568e87_page_001.pdf
2025-09-24 19:09:06,920 - INFO - Successfully processed page 1
2025-09-24 19:09:06,920 - INFO - Combined 1 pages into final text
2025-09-24 19:09:06,920 - INFO - Text validation for 1c67ee71_6_scale_ticket_4: 438 characters, 1 pages
2025-09-24 19:09:06,921 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:06,921 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:07,247 - INFO - Page 1: Extracted 679 characters, 29 lines from d1490326_6_scale_ticket_5_e0beb03c_page_001.pdf
2025-09-24 19:09:07,247 - INFO - Successfully processed page 1
2025-09-24 19:09:07,247 - INFO - Combined 1 pages into final text
2025-09-24 19:09:07,247 - INFO - Text validation for d1490326_6_scale_ticket_5: 696 characters, 1 pages
2025-09-24 19:09:07,247 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:07,247 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:08,073 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7affe8c1-0657-4a42-b94a-bb61889b822f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:07 GMT', 'content-type': 'application/json', 'content-length': '1084', 'connection': 'keep-alive', 'x-amzn-requestid': '7affe8c1-0657-4a42-b94a-bb61889b822f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this page. It\'s a scale ticket? The page includes gross, tare, net, weight, tons, etc. It says "Ticket: 524100", "Date: 11/2/2022", "Time", "Truck", "Truck Type", "P.O.", "D.O.", "Gross", "Tare", "Net", "Tons". It also has "Materials & Services", "Quantity", "Amount". And "By signing above, I declare that I did NOT deposit any PROHIBITED WASTES". This looks like a "Scale Ticket" for a waste recycling operation. The presence of weight fields (Gross, Tare, Net, Tons) indicates "scale_ticket". The doc also includes "Ticket". It might be a "Scale Ticket" indeed. There\'s no invoice, no BOL. So classify as scale_ticket.\n\nReturn JSON with documents: array with page_no 1 and doc_type "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2877, 'outputTokens': 215, 'totalTokens': 3092}, 'metrics': {'latencyMs': 1134}}
2025-09-24 19:09:08,073 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ba1cd5ca_6_scale_ticket_3.pdf
2025-09-24 19:09:08,095 - INFO - 

6_scale_ticket_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:08,095 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_3.json
2025-09-24 19:09:08,114 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4c92bdf3-2e41-4a34-9966-76bf31393747', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:07 GMT', 'content-type': 'application/json', 'content-length': '1309', 'connection': 'keep-alive', 'x-amzn-requestid': '4c92bdf3-2e41-4a34-9966-76bf31393747'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify.\n\nWe need to read the content: It contains "SCALE IN GROSS WEIGHT", "NET TONS", "BILL OF LADING", "INVOICE", etc. It has weight info, gross weight, net weight, etc. Also "BILL OF LADING" header present. There is also "INVOICE" block. The description: "WEIGHMASTER CUSTOMER" "DATE/TIME IN" etc. This page looks like a scale ticket: includes weight details and maybe also a BOL. But we need to classify the page type.\n\nWe have doc types: scale_ticket, bol, invoice, combined_carrier_documents etc.\n\nIt might be a Scale Ticket. It has weight info, gross, tare, net weight, etc. It also has BOL header. But the presence of weight keywords indicates scale ticket. According to instruction: "A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords ... then it is a scale ticket." So this page is a scale ticket.\n\nThus doc_type: scale_ticket. Page number: 1.\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2957, 'outputTokens': 256, 'totalTokens': 3213}, 'metrics': {'latencyMs': 1223}}
2025-09-24 19:09:08,114 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4a242f4d_6_scale_ticket_2.pdf
2025-09-24 19:09:08,389 - INFO - Page 1: Extracted 731 characters, 33 lines from d699e8a4_6_scale_ticket_9_c7898b27_page_001.pdf
2025-09-24 19:09:08,390 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba1cd5ca_6_scale_ticket_3.pdf
2025-09-24 19:09:08,390 - INFO - Successfully processed page 1
2025-09-24 19:09:08,391 - INFO - Combined 1 pages into final text
2025-09-24 19:09:08,391 - INFO - Text validation for d699e8a4_6_scale_ticket_9: 748 characters, 1 pages
2025-09-24 19:09:08,397 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:08,398 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:08,420 - INFO - 

6_scale_ticket_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:08,420 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_2.json
2025-09-24 19:09:08,724 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4a242f4d_6_scale_ticket_2.pdf
2025-09-24 19:09:08,784 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1698ca42-9da5-4fd6-a3fd-b14fdbd445dd', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:08 GMT', 'content-type': 'application/json', 'content-length': '619', 'connection': 'keep-alive', 'x-amzn-requestid': '1698ca42-9da5-4fd6-a3fd-b14fdbd445dd'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. The content: "ELECTRONIC SCALE TICKET" header. Keywords: "WEIGHER", "GROSS", "TARE", "NET". So classification: scale_ticket. No continuation indicator. So output JSON with documents array containing object page_no:1, doc_type:"scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2785, 'outputTokens': 90, 'totalTokens': 2875}, 'metrics': {'latencyMs': 545}}
2025-09-24 19:09:08,784 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1c67ee71_6_scale_ticket_4.pdf
2025-09-24 19:09:08,805 - INFO - 

6_scale_ticket_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:08,805 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_4.json
2025-09-24 19:09:09,093 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1c67ee71_6_scale_ticket_4.pdf
2025-09-24 19:09:09,095 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd55d508a-41da-4e5e-bd61-98a85be717ad', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:08 GMT', 'content-type': 'application/json', 'content-length': '1815', 'connection': 'keep-alive', 'x-amzn-requestid': 'd55d508a-41da-4e5e-bd61-98a85be717ad'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. This looks like a Solid Waste Manifest, including scale data, weigh, origin, etc. Is it a "scale_ticket"? It contains weight details, gross, tare, net, scale numbers. It also has "Ticket: 1247291", "Load #", "Date", "Time In/Out". This could be a "scale_ticket" or maybe a "log" (activity record). But the definition: "Scale Ticket" has weight keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, Ticket no. Yes. This page seems to be a scale ticket (weighing record). It also has a manifest; but the presence of "Ticket: 1247291" and weight suggests a scale ticket.\n\nThe definitions also mention that scale ticket may contain Bill of Lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket. So we should classify as "scale_ticket".\n\nCheck if any other type could apply. It\'s not invoice or comm_invoice. It\'s not BOL. It\'s not POD. It\'s not rate_confirmation. It\'s not fuel_receipt. It\'s not log? But logs might include activity records, but the weigh data is a key indicator for scale_ticket. So choose scale_ticket.\n\nThus output:\n\n{\n "documents": [\n   { "page_no": 1, "doc_type": "scale_ticket" }\n ]\n}\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_H68J-xlCRxOdKb5f4GLylg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3228, 'outputTokens': 360, 'totalTokens': 3588}, 'metrics': {'latencyMs': 1679}}
2025-09-24 19:09:09,096 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/35520a0b_6_scale_ticket_16.pdf
2025-09-24 19:09:09,136 - INFO - 

6_scale_ticket_16.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:09,136 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_16.json
2025-09-24 19:09:09,450 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/35520a0b_6_scale_ticket_16.pdf
2025-09-24 19:09:09,453 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a5393b96-2ade-473e-9ae2-8d45b6596e5d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:09 GMT', 'content-type': 'application/json', 'content-length': '1032', 'connection': 'keep-alive', 'x-amzn-requestid': 'a5393b96-2ade-473e-9ae2-8d45b6596e5d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We must output JSON with documents array containing object for page 1. Determine doc_type.\n\nText: "******** Reprinted Ticket ********" and contains Gross, Tare, Net, Tons. This looks like a scale ticket. Keywords: Gross, Tare, Net, Weighmaster, Driver. So it\'s scale ticket. So doc_type: "scale_ticket".\n\nCheck if any other relevant header: "Reprinted Ticket". Not invoice, no invoice info. So scale_ticket.\n\nWe should output:\n\n{\n "documents": [\n   {"page_no": 1, "doc_type": "scale_ticket"}\n ]\n}\n\nWe must use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_b0ohW19rQlS3_CBTGB0ppg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2842, 'outputTokens': 163, 'totalTokens': 3005}, 'metrics': {'latencyMs': 920}}
2025-09-24 19:09:09,453 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d1490326_6_scale_ticket_5.pdf
2025-09-24 19:09:09,471 - INFO - 

6_scale_ticket_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:09,471 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_5.json
2025-09-24 19:09:09,533 - INFO - Page 1: Extracted 473 characters, 36 lines from 61b66182_6_scale_ticket_6_71844bb3_page_001.pdf
2025-09-24 19:09:09,534 - INFO - Successfully processed page 1
2025-09-24 19:09:09,534 - INFO - Combined 1 pages into final text
2025-09-24 19:09:09,534 - INFO - Text validation for 61b66182_6_scale_ticket_6: 490 characters, 1 pages
2025-09-24 19:09:09,535 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:09,535 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:09,577 - INFO - Page 1: Extracted 622 characters, 27 lines from 9b1a2efc_6_scale_ticket_8_64b668f7_page_001.pdf
2025-09-24 19:09:09,578 - INFO - Successfully processed page 1
2025-09-24 19:09:09,579 - INFO - Combined 1 pages into final text
2025-09-24 19:09:09,579 - INFO - Text validation for 9b1a2efc_6_scale_ticket_8: 639 characters, 1 pages
2025-09-24 19:09:09,579 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:09,579 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:09,755 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d1490326_6_scale_ticket_5.pdf
2025-09-24 19:09:10,417 - INFO - Page 1: Extracted 1020 characters, 58 lines from c4585511_6_scale_ticket_7_eccbf62f_page_001.pdf
2025-09-24 19:09:10,418 - INFO - Successfully processed page 1
2025-09-24 19:09:10,419 - INFO - Combined 1 pages into final text
2025-09-24 19:09:10,419 - INFO - Text validation for c4585511_6_scale_ticket_7: 1037 characters, 1 pages
2025-09-24 19:09:10,419 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:10,419 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:10,805 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '97a97053-8a8b-477f-a887-b93704c874e1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:10 GMT', 'content-type': 'application/json', 'content-length': '1159', 'connection': 'keep-alive', 'x-amzn-requestid': '97a97053-8a8b-477f-a887-b93704c874e1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content seems to be a "Reprinted Ticket" with weight details. It includes Gross, Tare, Net, Tons. There\'s also "Manifest", "P.O.", "D.O." (Delivery Order?), maybe "D.O." stands for Delivery Order. It looks like a scale ticket: weight record. It includes Gross, Tare, Net, Tons. So likely "scale_ticket".\n\nCheck definitions: scale_ticket: weight record from a scale for vehicle/load (used for billing/compliance). Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. This matches: Gross, Tare, Net, Tons, time, date, ticket number. So "scale_ticket" is correct.\n\nThus output one page with doc_type "scale_ticket". Format as JSON with "documents" array.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "scale_ticket"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2882, 'outputTokens': 227, 'totalTokens': 3109}, 'metrics': {'latencyMs': 1158}}
2025-09-24 19:09:10,805 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d699e8a4_6_scale_ticket_9.pdf
2025-09-24 19:09:10,814 - INFO - 

6_scale_ticket_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:10,814 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_9.json
2025-09-24 19:09:11,111 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d699e8a4_6_scale_ticket_9.pdf
2025-09-24 19:09:11,386 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c1547e7c-da4e-4fa4-987c-2d49419c3a8c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:11 GMT', 'content-type': 'application/json', 'content-length': '561', 'connection': 'keep-alive', 'x-amzn-requestid': 'c1547e7c-da4e-4fa4-987c-2d49419c3a8c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need classify. Text includes "Scale Ticket #", weight fields "Gross", "Tare", "Net", etc. So it\'s a scale ticket. Document type: scale_ticket.\n\nReturn JSON with one object page_no 1 doc_type "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2769, 'outputTokens': 75, 'totalTokens': 2844}, 'metrics': {'latencyMs': 556}}
2025-09-24 19:09:11,387 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/61b66182_6_scale_ticket_6.pdf
2025-09-24 19:09:11,403 - INFO - 

6_scale_ticket_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:11,403 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_6.json
2025-09-24 19:09:11,659 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7aeb1a4e-527e-4c7d-8dce-a2f6470fa29c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:11 GMT', 'content-type': 'application/json', 'content-length': '809', 'connection': 'keep-alive', 'x-amzn-requestid': '7aeb1a4e-527e-4c7d-8dce-a2f6470fa29c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': "We need to classify each page. There is only page1 provided. We must output JSON with documents array containing one object: page_no:1, doc_type: ?\n\nWe need to determine if this page is a Scale Ticket. Let's see keywords: Gross, Tare, Net, Tons, Weighmaster, etc. Yes weight-related. So scale_ticket.\n\nAlso check if any other document type overlapping? Not invoice, BOL, etc. It's a weight record. So classify as scale_ticket.\n\nReturn JSON via tool call classify_logistics_doc_type."}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2831, 'outputTokens': 130, 'totalTokens': 2961}, 'metrics': {'latencyMs': 821}}
2025-09-24 19:09:11,659 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9b1a2efc_6_scale_ticket_8.pdf
2025-09-24 19:09:11,710 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/61b66182_6_scale_ticket_6.pdf
2025-09-24 19:09:11,730 - INFO - 

6_scale_ticket_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:11,730 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_8.json
2025-09-24 19:09:12,075 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9b1a2efc_6_scale_ticket_8.pdf
2025-09-24 19:09:12,247 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f32be77a-44b5-441c-9a27-7f8e14ecd61c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:12 GMT', 'content-type': 'application/json', 'content-length': '650', 'connection': 'keep-alive', 'x-amzn-requestid': 'f32be77a-44b5-441c-9a27-7f8e14ecd61c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The text is about a scale ticket. Keywords: Scale, Gross, Tare, Net, Ticket number, Date, Time, Truck/License, Customer, etc. So doc_type is "scale_ticket" according to definitions. It\'s a single page, no continuation. So output documents array with one entry: page_no: 1, doc_type: "scale_ticket".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2960, 'outputTokens': 100, 'totalTokens': 3060}, 'metrics': {'latencyMs': 603}}
2025-09-24 19:09:12,247 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c4585511_6_scale_ticket_7.pdf
2025-09-24 19:09:12,271 - INFO - 

6_scale_ticket_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 19:09:12,271 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_7.json
2025-09-24 19:09:12,588 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c4585511_6_scale_ticket_7.pdf
2025-09-24 19:09:12,589 - INFO - 
📊 Processing Summary:
2025-09-24 19:09:12,589 - INFO -    Total files: 16
2025-09-24 19:09:12,590 - INFO -    Successful: 16
2025-09-24 19:09:12,590 - INFO -    Failed: 0
2025-09-24 19:09:12,590 - INFO -    Duration: 33.62 seconds
2025-09-24 19:09:12,590 - INFO -    Output directory: output
2025-09-24 19:09:12,590 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:09:12,590 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,590 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,590 - INFO -    📄 6_scale_ticket_11.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,590 - INFO -    📄 6_scale_ticket_12.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,590 - INFO -    📄 6_scale_ticket_13.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,590 - INFO -    📄 6_scale_ticket_14.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_15.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_16.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_2.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_3.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_4.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_5.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_6.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_7.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_8.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,591 - INFO -    📄 6_scale_ticket_9.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 19:09:12,592 - INFO - 
============================================================================================================================================
2025-09-24 19:09:12,592 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:09:12,592 - INFO - ============================================================================================================================================
2025-09-24 19:09:12,592 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:09:12,592 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:09:12,592 - INFO - 6_scale_ticket_1.pdf                               1      scale_ticket                             run1_6_scale_ticket_1.json                                                      
2025-09-24 19:09:12,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-24 19:09:12,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_1.json
2025-09-24 19:09:12,593 - INFO - 
2025-09-24 19:09:12,593 - INFO - 6_scale_ticket_10.png                              1      scale_ticket                             run1_6_scale_ticket_10.json                                                     
2025-09-24 19:09:12,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png
2025-09-24 19:09:12,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_10.json
2025-09-24 19:09:12,593 - INFO - 
2025-09-24 19:09:12,593 - INFO - 6_scale_ticket_11.jpg                              1      scale_ticket                             run1_6_scale_ticket_11.json                                                     
2025-09-24 19:09:12,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg
2025-09-24 19:09:12,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_11.json
2025-09-24 19:09:12,593 - INFO - 
2025-09-24 19:09:12,593 - INFO - 6_scale_ticket_12.jpg                              1      scale_ticket                             run1_6_scale_ticket_12.json                                                     
2025-09-24 19:09:12,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg
2025-09-24 19:09:12,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_12.json
2025-09-24 19:09:12,593 - INFO - 
2025-09-24 19:09:12,593 - INFO - 6_scale_ticket_13.jpg                              1      scale_ticket                             run1_6_scale_ticket_13.json                                                     
2025-09-24 19:09:12,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg
2025-09-24 19:09:12,594 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_13.json
2025-09-24 19:09:12,594 - INFO - 
2025-09-24 19:09:12,594 - INFO - 6_scale_ticket_14.jpg                              1      scale_ticket                             run1_6_scale_ticket_14.json                                                     
2025-09-24 19:09:12,594 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg
2025-09-24 19:09:12,594 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_14.json
2025-09-24 19:09:12,594 - INFO - 
2025-09-24 19:09:12,594 - INFO - 6_scale_ticket_15.pdf                              1      scale_ticket                             run1_6_scale_ticket_15.json                                                     
2025-09-24 19:09:12,594 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf
2025-09-24 19:09:12,594 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_15.json
2025-09-24 19:09:12,594 - INFO - 
2025-09-24 19:09:12,594 - INFO - 6_scale_ticket_16.pdf                              1      scale_ticket                             run1_6_scale_ticket_16.json                                                     
2025-09-24 19:09:12,594 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf
2025-09-24 19:09:12,594 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_16.json
2025-09-24 19:09:12,594 - INFO - 
2025-09-24 19:09:12,594 - INFO - 6_scale_ticket_2.pdf                               1      scale_ticket                             run1_6_scale_ticket_2.json                                                      
2025-09-24 19:09:12,594 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf
2025-09-24 19:09:12,594 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_2.json
2025-09-24 19:09:12,594 - INFO - 
2025-09-24 19:09:12,594 - INFO - 6_scale_ticket_3.pdf                               1      scale_ticket                             run1_6_scale_ticket_3.json                                                      
2025-09-24 19:09:12,595 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf
2025-09-24 19:09:12,595 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_3.json
2025-09-24 19:09:12,595 - INFO - 
2025-09-24 19:09:12,595 - INFO - 6_scale_ticket_4.pdf                               1      scale_ticket                             run1_6_scale_ticket_4.json                                                      
2025-09-24 19:09:12,595 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf
2025-09-24 19:09:12,595 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_4.json
2025-09-24 19:09:12,595 - INFO - 
2025-09-24 19:09:12,595 - INFO - 6_scale_ticket_5.pdf                               1      scale_ticket                             run1_6_scale_ticket_5.json                                                      
2025-09-24 19:09:12,595 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf
2025-09-24 19:09:12,595 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_5.json
2025-09-24 19:09:12,595 - INFO - 
2025-09-24 19:09:12,595 - INFO - 6_scale_ticket_6.pdf                               1      scale_ticket                             run1_6_scale_ticket_6.json                                                      
2025-09-24 19:09:12,595 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf
2025-09-24 19:09:12,595 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_6.json
2025-09-24 19:09:12,595 - INFO - 
2025-09-24 19:09:12,595 - INFO - 6_scale_ticket_7.pdf                               1      scale_ticket                             run1_6_scale_ticket_7.json                                                      
2025-09-24 19:09:12,595 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf
2025-09-24 19:09:12,595 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_7.json
2025-09-24 19:09:12,595 - INFO - 
2025-09-24 19:09:12,596 - INFO - 6_scale_ticket_8.pdf                               1      scale_ticket                             run1_6_scale_ticket_8.json                                                      
2025-09-24 19:09:12,596 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf
2025-09-24 19:09:12,596 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_8.json
2025-09-24 19:09:12,596 - INFO - 
2025-09-24 19:09:12,596 - INFO - 6_scale_ticket_9.pdf                               1      scale_ticket                             run1_6_scale_ticket_9.json                                                      
2025-09-24 19:09:12,596 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf
2025-09-24 19:09:12,596 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_6_scale_ticket_9.json
2025-09-24 19:09:12,596 - INFO - 
2025-09-24 19:09:12,596 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:09:12,596 - INFO - Total entries: 16
2025-09-24 19:09:12,596 - INFO - ============================================================================================================================================
2025-09-24 19:09:12,596 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:09:12,596 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:09:12,596 - INFO -   1. 6_scale_ticket_1.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_1.json
2025-09-24 19:09:12,596 - INFO -   2. 6_scale_ticket_10.png               Page 1   → scale_ticket    | run1_6_scale_ticket_10.json
2025-09-24 19:09:12,596 - INFO -   3. 6_scale_ticket_11.jpg               Page 1   → scale_ticket    | run1_6_scale_ticket_11.json
2025-09-24 19:09:12,596 - INFO -   4. 6_scale_ticket_12.jpg               Page 1   → scale_ticket    | run1_6_scale_ticket_12.json
2025-09-24 19:09:12,596 - INFO -   5. 6_scale_ticket_13.jpg               Page 1   → scale_ticket    | run1_6_scale_ticket_13.json
2025-09-24 19:09:12,596 - INFO -   6. 6_scale_ticket_14.jpg               Page 1   → scale_ticket    | run1_6_scale_ticket_14.json
2025-09-24 19:09:12,596 - INFO -   7. 6_scale_ticket_15.pdf               Page 1   → scale_ticket    | run1_6_scale_ticket_15.json
2025-09-24 19:09:12,596 - INFO -   8. 6_scale_ticket_16.pdf               Page 1   → scale_ticket    | run1_6_scale_ticket_16.json
2025-09-24 19:09:12,597 - INFO -   9. 6_scale_ticket_2.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_2.json
2025-09-24 19:09:12,597 - INFO -  10. 6_scale_ticket_3.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_3.json
2025-09-24 19:09:12,597 - INFO -  11. 6_scale_ticket_4.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_4.json
2025-09-24 19:09:12,597 - INFO -  12. 6_scale_ticket_5.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_5.json
2025-09-24 19:09:12,597 - INFO -  13. 6_scale_ticket_6.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_6.json
2025-09-24 19:09:12,597 - INFO -  14. 6_scale_ticket_7.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_7.json
2025-09-24 19:09:12,597 - INFO -  15. 6_scale_ticket_8.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_8.json
2025-09-24 19:09:12,597 - INFO -  16. 6_scale_ticket_9.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_9.json
2025-09-24 19:09:12,597 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:09:12,597 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 33.619316, 'processed_files': [{'filename': '6_scale_ticket_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf'}, {'filename': '6_scale_ticket_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png'}, {'filename': '6_scale_ticket_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg'}, {'filename': '6_scale_ticket_12.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg'}, {'filename': '6_scale_ticket_13.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg'}, {'filename': '6_scale_ticket_14.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg'}, {'filename': '6_scale_ticket_15.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf'}, {'filename': '6_scale_ticket_16.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf'}, {'filename': '6_scale_ticket_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf'}, {'filename': '6_scale_ticket_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf'}, {'filename': '6_scale_ticket_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf'}, {'filename': '6_scale_ticket_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf'}, {'filename': '6_scale_ticket_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf'}, {'filename': '6_scale_ticket_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf'}, {'filename': '6_scale_ticket_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf'}, {'filename': '6_scale_ticket_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf'}]}
