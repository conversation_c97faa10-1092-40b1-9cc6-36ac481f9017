2025-09-23 17:18:04,290 - INFO - Logging initialized. Log file: logs/test_classification_20250923_171804.log
2025-09-23 17:18:04,290 - INFO - 📁 Found 1 files to process
2025-09-23 17:18:04,290 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 17:18:04,290 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 17:18:04,290 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 17:18:04,290 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 17:18:04,290 - INFO - ⬆️ [17:18:04] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:18:08,802 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/a1647995_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:18:08,803 - INFO - 🔍 [17:18:08] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:18:08,804 - INFO - Initializing TextractProcessor...
2025-09-23 17:18:08,820 - INFO - Initializing BedrockProcessor...
2025-09-23 17:18:08,825 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a1647995_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:18:08,825 - INFO - Processing PDF from S3...
2025-09-23 17:18:08,825 - INFO - Downloading PDF from S3 to /tmp/tmpd_phba1v/a1647995_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:18:11,279 - INFO - Downloaded PDF size: 1.1 MB
2025-09-23 17:18:11,280 - INFO - Splitting PDF into individual pages...
2025-09-23 17:18:11,283 - INFO - Splitting PDF a1647995_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 17:18:11,291 - INFO - Split PDF into 7 pages
2025-09-23 17:18:11,292 - INFO - Processing pages with Textract in parallel...
2025-09-23 17:18:11,292 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 17:18:16,092 - INFO - Page 3: Extracted 929 characters, 64 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_003.pdf
2025-09-23 17:18:16,093 - INFO - Successfully processed page 3
2025-09-23 17:18:16,416 - INFO - Page 4: Extracted 1151 characters, 63 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_004.pdf
2025-09-23 17:18:16,417 - INFO - Successfully processed page 4
2025-09-23 17:18:16,426 - INFO - Page 2: Extracted 1174 characters, 29 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_002.pdf
2025-09-23 17:18:16,426 - INFO - Successfully processed page 2
2025-09-23 17:18:17,578 - INFO - Page 1: Extracted 1168 characters, 45 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_001.pdf
2025-09-23 17:18:17,578 - INFO - Successfully processed page 1
2025-09-23 17:18:18,069 - INFO - Page 5: Extracted 2022 characters, 27 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_005.pdf
2025-09-23 17:18:18,069 - INFO - Successfully processed page 5
2025-09-23 17:18:23,370 - INFO - Page 7: Extracted 788 characters, 55 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_007.pdf
2025-09-23 17:18:23,371 - INFO - Successfully processed page 7
2025-09-23 17:18:24,105 - INFO - Page 6: Extracted 4159 characters, 115 lines from a1647995_Purchase_Order_VRSD0E9045781_b30f8fc6_page_006.pdf
2025-09-23 17:18:24,105 - INFO - Successfully processed page 6
2025-09-23 17:18:24,105 - INFO - Combined 7 pages into final text
2025-09-23 17:18:24,106 - INFO - Text validation for a1647995_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 17:18:24,106 - INFO - Analyzing document types with Bedrock...
2025-09-23 17:18:24,107 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 17:18:30,432 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-23 17:18:30,433 - ERROR - Processing failed for s3://document-extraction-logistically/temp/a1647995_Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:18:30,435 - ERROR - ✗ Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:18:32,086 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a1647995_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:18:32,086 - INFO - 
📊 Processing Summary:
2025-09-23 17:18:32,086 - INFO -    Total files: 1
2025-09-23 17:18:32,086 - INFO -    Successful: 0
2025-09-23 17:18:32,086 - INFO -    Failed: 1
2025-09-23 17:18:32,086 - INFO -    Duration: 27.80 seconds
2025-09-23 17:18:32,087 - INFO -    Output directory: output
2025-09-23 17:18:32,087 - ERROR - 
❌ Errors:
2025-09-23 17:18:32,087 - ERROR -    Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:18:32,087 - INFO - 
📋 No files were successfully processed.
2025-09-23 17:18:32,087 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 27.795828, 'processed_files': []}
