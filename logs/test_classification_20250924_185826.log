2025-09-24 18:58:26,723 - INFO - Logging initialized. Log file: logs/test_classification_20250924_185826.log
2025-09-24 18:58:26,723 - INFO - 📁 Found 9 files to process
2025-09-24 18:58:26,723 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:58:26,723 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 18:58:26,723 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 18:58:26,723 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 18:58:26,723 - INFO - ⬆️ [18:58:26] Uploading: AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:29,041 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AJ17T27ABDSGCYL1YI7Q.jpg -> s3://document-extraction-logistically/temp/f1c4c021_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:29,041 - INFO - 🔍 [18:58:29] Starting classification: AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:29,041 - INFO - ⬆️ [18:58:29] Uploading: AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:29,043 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:29,062 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:29,069 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f1c4c021_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:29,069 - INFO - Processing image from S3...
2025-09-24 18:58:30,221 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AYZVGH1N2J32RSF72XVM.jpg -> s3://document-extraction-logistically/temp/a32a3b56_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:30,222 - INFO - 🔍 [18:58:30] Starting classification: AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:30,223 - INFO - ⬆️ [18:58:30] Uploading: HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:30,225 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:30,238 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:30,243 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a32a3b56_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:30,243 - INFO - Processing image from S3...
2025-09-24 18:58:32,274 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/HGPDC5MO1QA1H930SDUI.jpeg -> s3://document-extraction-logistically/temp/543ced16_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:32,274 - INFO - 🔍 [18:58:32] Starting classification: HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:32,275 - INFO - ⬆️ [18:58:32] Uploading: IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:32,278 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:32,299 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:32,306 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/543ced16_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:32,306 - INFO - Processing image from S3...
2025-09-24 18:58:32,563 - INFO - S3 Image temp/f1c4c021_AJ17T27ABDSGCYL1YI7Q.jpg: Extracted 862 characters, 21 lines
2025-09-24 18:58:32,564 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:32,564 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:33,197 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf -> s3://document-extraction-logistically/temp/26aa4666_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:33,198 - INFO - 🔍 [18:58:33] Starting classification: IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:33,199 - INFO - ⬆️ [18:58:33] Uploading: N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:33,199 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:33,232 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:33,243 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/26aa4666_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:33,243 - INFO - Processing PDF from S3...
2025-09-24 18:58:33,245 - INFO - Downloading PDF from S3 to /tmp/tmppsetdv68/26aa4666_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:34,086 - INFO - S3 Image temp/a32a3b56_AYZVGH1N2J32RSF72XVM.jpg: Extracted 790 characters, 18 lines
2025-09-24 18:58:34,088 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:34,088 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:34,093 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/N3E7OS7WU4GL5CNGZVGC.pdf -> s3://document-extraction-logistically/temp/5b33d449_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:34,093 - INFO - 🔍 [18:58:34] Starting classification: N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:34,094 - INFO - ⬆️ [18:58:34] Uploading: NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:34,094 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:34,114 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:34,117 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5b33d449_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:34,117 - INFO - Processing PDF from S3...
2025-09-24 18:58:34,117 - INFO - Downloading PDF from S3 to /tmp/tmprhhyesds/5b33d449_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:34,518 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '40d69375-4b48-4273-8833-93a905ac8c33', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:34 GMT', 'content-type': 'application/json', 'content-length': '680', 'connection': 'keep-alive', 'x-amzn-requestid': '40d69375-4b48-4273-8833-93a905ac8c33'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We read content. It\'s an ingate? The header: "TSL Ingate: OMA". Contains "ActivityDate", "Inspector", "Carrier CALHOUN TRUCK LINES". It\'s an ingate document. So doc_type: ingate.\n\nCheck if any continuation? No. It\'s page1. So output page_no 1 and doc_type ingate.\n\nReturn JSON with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2820, 'outputTokens': 111, 'totalTokens': 2931}, 'metrics': {'latencyMs': 764}}
2025-09-24 18:58:34,519 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f1c4c021_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:34,752 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/NZ0W72MP1PN7WP5Z6E33.pdf -> s3://document-extraction-logistically/temp/816390f2_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:34,753 - INFO - 🔍 [18:58:34] Starting classification: NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:34,753 - INFO - ⬆️ [18:58:34] Uploading: OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:34,753 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:34,767 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:34,770 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/816390f2_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:34,770 - INFO - Processing PDF from S3...
2025-09-24 18:58:34,770 - INFO - Downloading PDF from S3 to /tmp/tmptmdyk8a6/816390f2_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:35,409 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OQ5GJFQKACWXOAGHOFVQ.pdf -> s3://document-extraction-logistically/temp/7a14f3b9_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:35,410 - INFO - 🔍 [18:58:35] Starting classification: OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:35,411 - INFO - ⬆️ [18:58:35] Uploading: OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:35,411 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:58:35,411 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:35,417 - INFO - Splitting PDF into individual pages...
2025-09-24 18:58:35,435 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:35,466 - INFO - Splitting PDF 26aa4666_IOL4A3Q1RJX8A4MOQXBR into 2 pages
2025-09-24 18:58:35,468 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7a14f3b9_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:35,468 - INFO - Processing PDF from S3...
2025-09-24 18:58:35,469 - INFO - Downloading PDF from S3 to /tmp/tmp74z2brck/7a14f3b9_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:35,487 - INFO - Split PDF into 2 pages
2025-09-24 18:58:35,487 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:58:35,487 - INFO - Expected pages: [1, 2]
2025-09-24 18:58:36,086 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OSWHB56ZEDCANKML3UKZ.pdf -> s3://document-extraction-logistically/temp/0b4d71cd_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:36,086 - INFO - 🔍 [18:58:36] Starting classification: OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:36,087 - INFO - ⬆️ [18:58:36] Uploading: WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:36,088 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:36,112 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7e749e6a-bc72-45a0-a520-f87f0b976820', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:35 GMT', 'content-type': 'application/json', 'content-length': '747', 'connection': 'keep-alive', 'x-amzn-requestid': '7e749e6a-bc72-45a0-a520-f87f0b976820'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to examine the page. It shows "TSL Ingate: OMA" "ActivityDate:..." "Carrier Inspector..." So it\'s an Ingate document. The description: "Ingate Document: Record of vehicle/container entering a facility (gate-in)." The keywords: Ingate, Equipment In-Gated, Arrival Activity, Time In, etc. So doc_type is "ingate". Output JSON with page_no 1 and doc_type ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2790, 'outputTokens': 123, 'totalTokens': 2913}, 'metrics': {'latencyMs': 790}}
2025-09-24 18:58:36,116 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:36,117 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a32a3b56_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:36,122 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0b4d71cd_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:36,126 - INFO - Processing PDF from S3...
2025-09-24 18:58:36,127 - INFO - Downloading PDF from S3 to /tmp/tmpge_86dwt/0b4d71cd_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:36,257 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:58:36,258 - INFO - Splitting PDF into individual pages...
2025-09-24 18:58:36,259 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:58:36,259 - INFO - Splitting PDF into individual pages...
2025-09-24 18:58:36,260 - INFO - Splitting PDF 5b33d449_N3E7OS7WU4GL5CNGZVGC into 1 pages
2025-09-24 18:58:36,264 - INFO - Splitting PDF 816390f2_NZ0W72MP1PN7WP5Z6E33 into 1 pages
2025-09-24 18:58:36,278 - INFO - Split PDF into 1 pages
2025-09-24 18:58:36,278 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:58:36,278 - INFO - Expected pages: [1]
2025-09-24 18:58:36,280 - INFO - Split PDF into 1 pages
2025-09-24 18:58:36,285 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:58:36,286 - INFO - Expected pages: [1]
2025-09-24 18:58:36,985 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/WQ9YLCCRP1EJOP7S8615.png -> s3://document-extraction-logistically/temp/6ab8df01_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:36,986 - INFO - 🔍 [18:58:36] Starting classification: WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:36,988 - INFO - Initializing TextractProcessor...
2025-09-24 18:58:37,009 - INFO - Initializing BedrockProcessor...
2025-09-24 18:58:37,017 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ab8df01_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:37,018 - INFO - Processing image from S3...
2025-09-24 18:58:37,032 - INFO - 

AJ17T27ABDSGCYL1YI7Q.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:37,032 - INFO - 

✓ Saved result: output/run1_AJ17T27ABDSGCYL1YI7Q.json
2025-09-24 18:58:37,202 - INFO - S3 Image temp/543ced16_HGPDC5MO1QA1H930SDUI.jpeg: Extracted 560 characters, 28 lines
2025-09-24 18:58:37,202 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:37,202 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:37,321 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f1c4c021_AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:37,333 - INFO - 

AYZVGH1N2J32RSF72XVM.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:37,334 - INFO - 

✓ Saved result: output/run1_AYZVGH1N2J32RSF72XVM.json
2025-09-24 18:58:37,617 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a32a3b56_AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:37,782 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:58:37,783 - INFO - Splitting PDF into individual pages...
2025-09-24 18:58:37,784 - INFO - Splitting PDF 7a14f3b9_OQ5GJFQKACWXOAGHOFVQ into 1 pages
2025-09-24 18:58:37,792 - INFO - Split PDF into 1 pages
2025-09-24 18:58:37,792 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:58:37,792 - INFO - Expected pages: [1]
2025-09-24 18:58:37,957 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:58:37,957 - INFO - Splitting PDF into individual pages...
2025-09-24 18:58:37,959 - INFO - Splitting PDF 0b4d71cd_OSWHB56ZEDCANKML3UKZ into 1 pages
2025-09-24 18:58:37,961 - INFO - Split PDF into 1 pages
2025-09-24 18:58:37,961 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:58:37,961 - INFO - Expected pages: [1]
2025-09-24 18:58:38,140 - INFO - Page 2: Extracted 116 characters, 5 lines from 26aa4666_IOL4A3Q1RJX8A4MOQXBR_17509c7f_page_002.pdf
2025-09-24 18:58:38,141 - INFO - Successfully processed page 2
2025-09-24 18:58:39,098 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ea28930c-3d52-4624-a7d8-d4499ee06ddc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:38 GMT', 'content-type': 'application/json', 'content-length': '557', 'connection': 'keep-alive', 'x-amzn-requestid': 'ea28930c-3d52-4624-a7d8-d4499ee06ddc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Keywords: "IN-GATE", "Equipment Inspection Record", "Ingate:". So likely ingate document. So doc_type "ingate". Output JSON with one entry.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "ingate"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2789, 'outputTokens': 80, 'totalTokens': 2869}, 'metrics': {'latencyMs': 602}}
2025-09-24 18:58:39,099 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/543ced16_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:39,121 - INFO - 

HGPDC5MO1QA1H930SDUI.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:39,122 - INFO - 

✓ Saved result: output/run1_HGPDC5MO1QA1H930SDUI.json
2025-09-24 18:58:39,409 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/543ced16_HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:39,551 - INFO - Page 1: Extracted 412 characters, 29 lines from 26aa4666_IOL4A3Q1RJX8A4MOQXBR_17509c7f_page_001.pdf
2025-09-24 18:58:39,552 - INFO - Successfully processed page 1
2025-09-24 18:58:39,552 - INFO - Combined 2 pages into final text
2025-09-24 18:58:39,552 - INFO - Text validation for 26aa4666_IOL4A3Q1RJX8A4MOQXBR: 564 characters, 2 pages
2025-09-24 18:58:39,553 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:39,553 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:39,566 - INFO - Page 1: Extracted 375 characters, 27 lines from 816390f2_NZ0W72MP1PN7WP5Z6E33_c2095495_page_001.pdf
2025-09-24 18:58:39,566 - INFO - Successfully processed page 1
2025-09-24 18:58:39,566 - INFO - Combined 1 pages into final text
2025-09-24 18:58:39,567 - INFO - Text validation for 816390f2_NZ0W72MP1PN7WP5Z6E33: 392 characters, 1 pages
2025-09-24 18:58:39,567 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:39,567 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:39,618 - INFO - S3 Image temp/6ab8df01_WQ9YLCCRP1EJOP7S8615.png: Extracted 410 characters, 24 lines
2025-09-24 18:58:39,618 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:39,618 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:41,005 - INFO - Page 1: Extracted 1573 characters, 33 lines from 5b33d449_N3E7OS7WU4GL5CNGZVGC_7491b508_page_001.pdf
2025-09-24 18:58:41,005 - INFO - Successfully processed page 1
2025-09-24 18:58:41,005 - INFO - Combined 1 pages into final text
2025-09-24 18:58:41,006 - INFO - Text validation for 5b33d449_N3E7OS7WU4GL5CNGZVGC: 1590 characters, 1 pages
2025-09-24 18:58:41,006 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:41,006 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:41,513 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fbd0faa9-6b90-4af9-acb7-23fb3e16ee1b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:41 GMT', 'content-type': 'application/json', 'content-length': '822', 'connection': 'keep-alive', 'x-amzn-requestid': 'fbd0faa9-6b90-4af9-acb7-23fb3e16ee1b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1 content. It\'s a gate receipt. This is likely an ingate document? The header says "J1 GATE RECEIPT" and details include In-Gate. This matches ingate document. According to definitions: Ingates: Record of vehicle/container entering a facility. Keywords: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In. This page has In-Gate. So doc_type: ingate. So we produce JSON with documents array having page_no 1, doc_type ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2722, 'outputTokens': 140, 'totalTokens': 2862}, 'metrics': {'latencyMs': 753}}
2025-09-24 18:58:41,514 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/816390f2_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:41,528 - INFO - 

NZ0W72MP1PN7WP5Z6E33.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:41,529 - INFO - 

✓ Saved result: output/run1_NZ0W72MP1PN7WP5Z6E33.json
2025-09-24 18:58:41,777 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '92a57846-48e3-4988-880e-c98c7d112ea2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:41 GMT', 'content-type': 'application/json', 'content-length': '918', 'connection': 'keep-alive', 'x-amzn-requestid': '92a57846-48e3-4988-880e-c98c7d112ea2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page text. Need classify. Let\'s identify keywords: "In Date", "In Time", "In Condition", "In Empty", "EIR Num", "SCAC Code", "Trucking Co. Name", "Driver", etc. This looks like an "ingate" document: record of vehicle entering facility. Keywords: "Equipment In-Gated" but we see "In Date", "In Time", "In Condition". Also "Depot". So likely ingate.\n\nCheck other doc types: bol, pod, etc. No mention of Bill of Lading. No sign of receipt or invoice. Not a scale ticket: no weight. Not a log: no times beyond entry. So ingate.\n\nThus output page 1 as ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2751, 'outputTokens': 174, 'totalTokens': 2925}, 'metrics': {'latencyMs': 909}}
2025-09-24 18:58:41,777 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6ab8df01_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:41,819 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/816390f2_NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:41,835 - INFO - 

WQ9YLCCRP1EJOP7S8615.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:41,836 - INFO - 

✓ Saved result: output/run1_WQ9YLCCRP1EJOP7S8615.json
2025-09-24 18:58:42,126 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ab8df01_WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:42,444 - INFO - Page 1: Extracted 763 characters, 65 lines from 0b4d71cd_OSWHB56ZEDCANKML3UKZ_56a6b566_page_001.pdf
2025-09-24 18:58:42,445 - INFO - Successfully processed page 1
2025-09-24 18:58:42,445 - INFO - Combined 1 pages into final text
2025-09-24 18:58:42,445 - INFO - Text validation for 0b4d71cd_OSWHB56ZEDCANKML3UKZ: 780 characters, 1 pages
2025-09-24 18:58:42,446 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:42,446 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:42,521 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5e005706-e8b8-4680-9aee-2eec7de70078', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:42 GMT', 'content-type': 'application/json', 'content-length': '1772', 'connection': 'keep-alive', 'x-amzn-requestid': '5e005706-e8b8-4680-9aee-2eec7de70078'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page of the PDF. The user gave two pages with text. The text is a web page from ShipCSX showing "Shipment Lookup Results" with some status: IN-GATE (1). Likely this is an ingate document? The page shows "Equipment In-Gated" and "Gate Window" etc. It appears to be an in-gate record. The content is a web page that shows status of a shipment: "IN-GATE (1)". This looks like an ingate document. According to definitions, ingate: record of vehicle/container entering a facility, with equipment in-gated, arrival activity, etc. The page includes "Equipment In-Gated" and "In-Gate Readiness". So I\'d classify as ingate.\n\nBut also the page is a "Shipment Lookup Results" page, not necessarily a document type? But we need to classify. There\'s no explicit header of "Ingate" though. But keywords "Equipment In-Gated" suggests ingate. There\'s no "Outgate". So ingate.\n\nWe have two pages: page 1 and page 2. Page 2 is just footer "2/2" and copyright. It is part of same document. So page 2 continues page 1. The instruction: if page starts with "continued", or pagination like "page 2 of 3". It doesn\'t start with "continued" but has "2/2". It is a continuation. So assign same doc_type as previous page: ingate.\n\nThus output two entries: page_no 1 ingate, page_no 2 ingate.\n\nCheck if doc_type "ingate" is correct. Yes.\n\nReturn JSON with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2804, 'outputTokens': 378, 'totalTokens': 3182}, 'metrics': {'latencyMs': 1810}}
2025-09-24 18:58:42,522 - INFO - Page 1: Extracted 759 characters, 65 lines from 7a14f3b9_OQ5GJFQKACWXOAGHOFVQ_54c6748e_page_001.pdf
2025-09-24 18:58:42,522 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/26aa4666_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:42,522 - INFO - Successfully processed page 1
2025-09-24 18:58:42,524 - INFO - Combined 1 pages into final text
2025-09-24 18:58:42,526 - INFO - Text validation for 7a14f3b9_OQ5GJFQKACWXOAGHOFVQ: 776 characters, 1 pages
2025-09-24 18:58:42,528 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:58:42,530 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:58:42,538 - INFO - 

IOL4A3Q1RJX8A4MOQXBR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        },
        {
            "page_no": 2,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:42,538 - INFO - 

✓ Saved result: output/run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:42,817 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/26aa4666_IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:43,396 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '57f923a6-0ae0-478a-a18e-1d9c136bcb5f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:43 GMT', 'content-type': 'application/json', 'content-length': '1103', 'connection': 'keep-alive', 'x-amzn-requestid': '57f923a6-0ae0-478a-a18e-1d9c136bcb5f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The content looks like an email dispatch with Intermodal Gate Information. The document type is likely "ingate" or "outgate" depending on context. It says "Intermodal Gate Information MRKU 329129 08/29/2025 22:46" and contains Equipment ID, Terminal, Arrival, etc. Also "Event Information" and "Arrival". This looks like an ingate record: vehicle entering facility. There\'s no departure. So doc_type "ingate".\n\nCheck keywords: "Equipment Information", "Event Information", "Arrival", "InGate" is not explicitly but similar. The keywords for ingate: Equipment In-Gated, Arrival, Time In, etc. So ingate fits.\n\nWe must return JSON with documents array with one object: page_no 1, doc_type ingate.\n\nLet\'s produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2968, 'outputTokens': 199, 'totalTokens': 3167}, 'metrics': {'latencyMs': 1180}}
2025-09-24 18:58:43,397 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5b33d449_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:43,411 - INFO - 

N3E7OS7WU4GL5CNGZVGC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:43,411 - INFO - 

✓ Saved result: output/run1_N3E7OS7WU4GL5CNGZVGC.json
2025-09-24 18:58:43,733 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5b33d449_N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:44,572 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c2933a58-c0dd-4460-8a12-48a0ccb11f0b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:44 GMT', 'content-type': 'application/json', 'content-length': '649', 'connection': 'keep-alive', 'x-amzn-requestid': 'c2933a58-c0dd-4460-8a12-48a0ccb11f0b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page provided. Need detect doc type. This looks like an Ingate document: "Gate In Interchange", "Ingate Document", "Equipment In-Gated". Contains truck number, time in, inspection, etc. So doc_type: ingate. No continuation. Output JSON with documents array with page_no 1 and doc_type ingate.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2879, 'outputTokens': 96, 'totalTokens': 2975}, 'metrics': {'latencyMs': 632}}
2025-09-24 18:58:44,573 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7a14f3b9_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:44,599 - INFO - 

OQ5GJFQKACWXOAGHOFVQ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:44,599 - INFO - 

✓ Saved result: output/run1_OQ5GJFQKACWXOAGHOFVQ.json
2025-09-24 18:58:44,890 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7a14f3b9_OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:45,199 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1e88a588-450f-42ab-8d44-adef1988800f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:28:44 GMT', 'content-type': 'application/json', 'content-length': '771', 'connection': 'keep-alive', 'x-amzn-requestid': '1e88a588-450f-42ab-8d44-adef1988800f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The page seems to be an ingate document: "Gate In Interchange", "Ingate Document" keywords: "Gate In", "Ingate", "Arrival Activity", "Time In", "Truck In". It lists container details, inspection, etc. So doc_type: ingate.\n\nWe must output JSON with documents array containing one object with page_no 1, doc_type "ingate".\n\nCheck if any other type: The page is clearly an ingate. So output.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2881, 'outputTokens': 127, 'totalTokens': 3008}, 'metrics': {'latencyMs': 809}}
2025-09-24 18:58:45,200 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0b4d71cd_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:45,222 - INFO - 

OSWHB56ZEDCANKML3UKZ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 18:58:45,222 - INFO - 

✓ Saved result: output/run1_OSWHB56ZEDCANKML3UKZ.json
2025-09-24 18:58:45,505 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0b4d71cd_OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:45,507 - INFO - 
📊 Processing Summary:
2025-09-24 18:58:45,507 - INFO -    Total files: 9
2025-09-24 18:58:45,507 - INFO -    Successful: 9
2025-09-24 18:58:45,507 - INFO -    Failed: 0
2025-09-24 18:58:45,507 - INFO -    Duration: 18.78 seconds
2025-09-24 18:58:45,507 - INFO -    Output directory: output
2025-09-24 18:58:45,507 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:58:45,507 - INFO -    📄 AJ17T27ABDSGCYL1YI7Q.jpg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,507 - INFO -    📄 AYZVGH1N2J32RSF72XVM.jpg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 HGPDC5MO1QA1H930SDUI.jpeg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 IOL4A3Q1RJX8A4MOQXBR.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 N3E7OS7WU4GL5CNGZVGC.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 NZ0W72MP1PN7WP5Z6E33.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 OQ5GJFQKACWXOAGHOFVQ.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 OSWHB56ZEDCANKML3UKZ.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,508 - INFO -    📄 WQ9YLCCRP1EJOP7S8615.png: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 18:58:45,509 - INFO - 
============================================================================================================================================
2025-09-24 18:58:45,509 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:58:45,509 - INFO - ============================================================================================================================================
2025-09-24 18:58:45,509 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:58:45,509 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:58:45,509 - INFO - AJ17T27ABDSGCYL1YI7Q.jpg                           1      ingate                                   run1_AJ17T27ABDSGCYL1YI7Q.json                                                  
2025-09-24 18:58:45,509 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AJ17T27ABDSGCYL1YI7Q.jpg
2025-09-24 18:58:45,509 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AJ17T27ABDSGCYL1YI7Q.json
2025-09-24 18:58:45,510 - INFO - 
2025-09-24 18:58:45,510 - INFO - AYZVGH1N2J32RSF72XVM.jpg                           1      ingate                                   run1_AYZVGH1N2J32RSF72XVM.json                                                  
2025-09-24 18:58:45,510 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AYZVGH1N2J32RSF72XVM.jpg
2025-09-24 18:58:45,510 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYZVGH1N2J32RSF72XVM.json
2025-09-24 18:58:45,510 - INFO - 
2025-09-24 18:58:45,510 - INFO - HGPDC5MO1QA1H930SDUI.jpeg                          1      ingate                                   run1_HGPDC5MO1QA1H930SDUI.json                                                  
2025-09-24 18:58:45,510 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/HGPDC5MO1QA1H930SDUI.jpeg
2025-09-24 18:58:45,510 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HGPDC5MO1QA1H930SDUI.json
2025-09-24 18:58:45,510 - INFO - 
2025-09-24 18:58:45,510 - INFO - IOL4A3Q1RJX8A4MOQXBR.pdf                           1      ingate                                   run1_IOL4A3Q1RJX8A4MOQXBR.json                                                  
2025-09-24 18:58:45,510 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:45,510 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:45,510 - INFO - 
2025-09-24 18:58:45,510 - INFO - IOL4A3Q1RJX8A4MOQXBR.pdf                           2      ingate                                   run1_IOL4A3Q1RJX8A4MOQXBR.json                                                  
2025-09-24 18:58:45,511 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf
2025-09-24 18:58:45,511 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:45,511 - INFO - 
2025-09-24 18:58:45,511 - INFO - N3E7OS7WU4GL5CNGZVGC.pdf                           1      ingate                                   run1_N3E7OS7WU4GL5CNGZVGC.json                                                  
2025-09-24 18:58:45,511 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/N3E7OS7WU4GL5CNGZVGC.pdf
2025-09-24 18:58:45,511 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N3E7OS7WU4GL5CNGZVGC.json
2025-09-24 18:58:45,511 - INFO - 
2025-09-24 18:58:45,511 - INFO - NZ0W72MP1PN7WP5Z6E33.pdf                           1      ingate                                   run1_NZ0W72MP1PN7WP5Z6E33.json                                                  
2025-09-24 18:58:45,511 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/NZ0W72MP1PN7WP5Z6E33.pdf
2025-09-24 18:58:45,511 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NZ0W72MP1PN7WP5Z6E33.json
2025-09-24 18:58:45,511 - INFO - 
2025-09-24 18:58:45,511 - INFO - OQ5GJFQKACWXOAGHOFVQ.pdf                           1      ingate                                   run1_OQ5GJFQKACWXOAGHOFVQ.json                                                  
2025-09-24 18:58:45,511 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OQ5GJFQKACWXOAGHOFVQ.pdf
2025-09-24 18:58:45,511 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OQ5GJFQKACWXOAGHOFVQ.json
2025-09-24 18:58:45,511 - INFO - 
2025-09-24 18:58:45,511 - INFO - OSWHB56ZEDCANKML3UKZ.pdf                           1      ingate                                   run1_OSWHB56ZEDCANKML3UKZ.json                                                  
2025-09-24 18:58:45,511 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OSWHB56ZEDCANKML3UKZ.pdf
2025-09-24 18:58:45,512 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OSWHB56ZEDCANKML3UKZ.json
2025-09-24 18:58:45,512 - INFO - 
2025-09-24 18:58:45,512 - INFO - WQ9YLCCRP1EJOP7S8615.png                           1      ingate                                   run1_WQ9YLCCRP1EJOP7S8615.json                                                  
2025-09-24 18:58:45,512 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/WQ9YLCCRP1EJOP7S8615.png
2025-09-24 18:58:45,512 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WQ9YLCCRP1EJOP7S8615.json
2025-09-24 18:58:45,512 - INFO - 
2025-09-24 18:58:45,512 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:58:45,512 - INFO - Total entries: 10
2025-09-24 18:58:45,512 - INFO - ============================================================================================================================================
2025-09-24 18:58:45,512 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:58:45,512 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:58:45,512 - INFO -   1. AJ17T27ABDSGCYL1YI7Q.jpg            Page 1   → ingate          | run1_AJ17T27ABDSGCYL1YI7Q.json
2025-09-24 18:58:45,512 - INFO -   2. AYZVGH1N2J32RSF72XVM.jpg            Page 1   → ingate          | run1_AYZVGH1N2J32RSF72XVM.json
2025-09-24 18:58:45,512 - INFO -   3. HGPDC5MO1QA1H930SDUI.jpeg           Page 1   → ingate          | run1_HGPDC5MO1QA1H930SDUI.json
2025-09-24 18:58:45,512 - INFO -   4. IOL4A3Q1RJX8A4MOQXBR.pdf            Page 1   → ingate          | run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:45,512 - INFO -   5. IOL4A3Q1RJX8A4MOQXBR.pdf            Page 2   → ingate          | run1_IOL4A3Q1RJX8A4MOQXBR.json
2025-09-24 18:58:45,513 - INFO -   6. N3E7OS7WU4GL5CNGZVGC.pdf            Page 1   → ingate          | run1_N3E7OS7WU4GL5CNGZVGC.json
2025-09-24 18:58:45,513 - INFO -   7. NZ0W72MP1PN7WP5Z6E33.pdf            Page 1   → ingate          | run1_NZ0W72MP1PN7WP5Z6E33.json
2025-09-24 18:58:45,513 - INFO -   8. OQ5GJFQKACWXOAGHOFVQ.pdf            Page 1   → ingate          | run1_OQ5GJFQKACWXOAGHOFVQ.json
2025-09-24 18:58:45,513 - INFO -   9. OSWHB56ZEDCANKML3UKZ.pdf            Page 1   → ingate          | run1_OSWHB56ZEDCANKML3UKZ.json
2025-09-24 18:58:45,513 - INFO -  10. WQ9YLCCRP1EJOP7S8615.png            Page 1   → ingate          | run1_WQ9YLCCRP1EJOP7S8615.json
2025-09-24 18:58:45,513 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:58:45,513 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 18.783222, 'processed_files': [{'filename': 'AJ17T27ABDSGCYL1YI7Q.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AJ17T27ABDSGCYL1YI7Q.jpg'}, {'filename': 'AYZVGH1N2J32RSF72XVM.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/AYZVGH1N2J32RSF72XVM.jpg'}, {'filename': 'HGPDC5MO1QA1H930SDUI.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/HGPDC5MO1QA1H930SDUI.jpeg'}, {'filename': 'IOL4A3Q1RJX8A4MOQXBR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}, {'page_no': 2, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/IOL4A3Q1RJX8A4MOQXBR.pdf'}, {'filename': 'N3E7OS7WU4GL5CNGZVGC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/N3E7OS7WU4GL5CNGZVGC.pdf'}, {'filename': 'NZ0W72MP1PN7WP5Z6E33.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/NZ0W72MP1PN7WP5Z6E33.pdf'}, {'filename': 'OQ5GJFQKACWXOAGHOFVQ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OQ5GJFQKACWXOAGHOFVQ.pdf'}, {'filename': 'OSWHB56ZEDCANKML3UKZ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/OSWHB56ZEDCANKML3UKZ.pdf'}, {'filename': 'WQ9YLCCRP1EJOP7S8615.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/ingate/WQ9YLCCRP1EJOP7S8615.png'}]}
