2025-09-24 18:47:14,976 - INFO - Logging initialized. Log file: logs/test_classification_20250924_184714.log
2025-09-24 18:47:14,976 - INFO - 📁 Found 10 files to process
2025-09-24 18:47:14,976 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:47:14,976 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 18:47:14,976 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 18:47:14,976 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 18:47:14,976 - INFO - ⬆️ [18:47:14] Uploading: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:17,053 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf -> s3://document-extraction-logistically/temp/c1d3c70c_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:17,053 - INFO - 🔍 [18:47:17] Starting classification: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:17,054 - INFO - ⬆️ [18:47:17] Uploading: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:17,056 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:17,075 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:17,080 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c1d3c70c_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:17,080 - INFO - Processing PDF from S3...
2025-09-24 18:47:17,080 - INFO - Downloading PDF from S3 to /tmp/tmpeamnc5dy/c1d3c70c_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:18,381 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf -> s3://document-extraction-logistically/temp/098ec02c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:18,381 - INFO - 🔍 [18:47:18] Starting classification: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:18,382 - INFO - ⬆️ [18:47:18] Uploading: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:18,382 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:18,397 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:18,400 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/098ec02c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:18,401 - INFO - Processing PDF from S3...
2025-09-24 18:47:18,401 - INFO - Downloading PDF from S3 to /tmp/tmp4m48tyrb/098ec02c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:18,715 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:47:18,715 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:18,717 - INFO - Splitting PDF c1d3c70c_G0H0K1LRWDOG7LXAKKQ7 into 1 pages
2025-09-24 18:47:18,720 - INFO - Split PDF into 1 pages
2025-09-24 18:47:18,720 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:18,720 - INFO - Expected pages: [1]
2025-09-24 18:47:18,997 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf -> s3://document-extraction-logistically/temp/e85947b9_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:18,997 - INFO - 🔍 [18:47:18] Starting classification: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:18,998 - INFO - ⬆️ [18:47:18] Uploading: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:18,999 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:19,014 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:19,019 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e85947b9_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:19,020 - INFO - Processing PDF from S3...
2025-09-24 18:47:19,020 - INFO - Downloading PDF from S3 to /tmp/tmpzm0w4odc/e85947b9_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:19,612 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf -> s3://document-extraction-logistically/temp/66b344b0_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:19,613 - INFO - 🔍 [18:47:19] Starting classification: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:19,614 - INFO - ⬆️ [18:47:19] Uploading: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:19,615 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:19,646 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:19,650 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/66b344b0_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:19,650 - INFO - Processing PDF from S3...
2025-09-24 18:47:19,651 - INFO - Downloading PDF from S3 to /tmp/tmp2pwh0blt/66b344b0_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:20,155 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:47:20,156 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:20,157 - INFO - Splitting PDF 098ec02c_GKNF55W2CF2JR6EBXMPX into 1 pages
2025-09-24 18:47:20,162 - INFO - Split PDF into 1 pages
2025-09-24 18:47:20,162 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:20,162 - INFO - Expected pages: [1]
2025-09-24 18:47:20,596 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:47:20,596 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:20,598 - INFO - Splitting PDF e85947b9_KDFG6JJAZV41YZP4TEGN into 1 pages
2025-09-24 18:47:20,603 - INFO - Split PDF into 1 pages
2025-09-24 18:47:20,603 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:20,603 - INFO - Expected pages: [1]
2025-09-24 18:47:20,738 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf -> s3://document-extraction-logistically/temp/c8cb8012_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:20,738 - INFO - 🔍 [18:47:20] Starting classification: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:20,739 - INFO - ⬆️ [18:47:20] Uploading: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:20,744 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:20,756 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:20,759 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c8cb8012_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:20,760 - INFO - Processing PDF from S3...
2025-09-24 18:47:20,760 - INFO - Downloading PDF from S3 to /tmp/tmpqpopi0sq/c8cb8012_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:20,899 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:47:20,899 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:20,900 - INFO - Splitting PDF 66b344b0_MMNWUYGBLL1K5KSJBNOT into 1 pages
2025-09-24 18:47:20,901 - INFO - Split PDF into 1 pages
2025-09-24 18:47:20,901 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:20,902 - INFO - Expected pages: [1]
2025-09-24 18:47:21,392 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf -> s3://document-extraction-logistically/temp/5b4a6312_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:21,392 - INFO - 🔍 [18:47:21] Starting classification: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:21,393 - INFO - ⬆️ [18:47:21] Uploading: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:21,393 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:21,399 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:21,403 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5b4a6312_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:21,404 - INFO - Processing PDF from S3...
2025-09-24 18:47:21,404 - INFO - Downloading PDF from S3 to /tmp/tmpr_ab9_2w/5b4a6312_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:22,043 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf -> s3://document-extraction-logistically/temp/8a5a34e5_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:22,043 - INFO - 🔍 [18:47:22] Starting classification: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:22,044 - INFO - ⬆️ [18:47:22] Uploading: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:22,046 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:22,097 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:22,101 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8a5a34e5_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:22,101 - INFO - Processing PDF from S3...
2025-09-24 18:47:22,101 - INFO - Downloading PDF from S3 to /tmp/tmp3m2fgtzv/8a5a34e5_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:22,620 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:47:22,620 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:22,622 - INFO - Splitting PDF 5b4a6312_O1YJBQBLYAU6D0SDDKAU into 1 pages
2025-09-24 18:47:22,624 - INFO - Split PDF into 1 pages
2025-09-24 18:47:22,624 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:47:22,624 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:22,624 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:22,625 - INFO - Expected pages: [1]
2025-09-24 18:47:22,629 - INFO - Splitting PDF c8cb8012_NFMA1926AJ8R3TDZQALU into 1 pages
2025-09-24 18:47:22,631 - INFO - Split PDF into 1 pages
2025-09-24 18:47:22,632 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:22,632 - INFO - Expected pages: [1]
2025-09-24 18:47:22,691 - INFO - Page 1: Extracted 667 characters, 55 lines from c1d3c70c_G0H0K1LRWDOG7LXAKKQ7_ffdbc43c_page_001.pdf
2025-09-24 18:47:22,691 - INFO - Successfully processed page 1
2025-09-24 18:47:22,692 - INFO - Combined 1 pages into final text
2025-09-24 18:47:22,692 - INFO - Text validation for c1d3c70c_G0H0K1LRWDOG7LXAKKQ7: 684 characters, 1 pages
2025-09-24 18:47:22,693 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:22,693 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:22,921 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf -> s3://document-extraction-logistically/temp/7147fc62_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:22,922 - INFO - 🔍 [18:47:22] Starting classification: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:22,923 - INFO - ⬆️ [18:47:22] Uploading: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:22,925 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:22,943 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:22,946 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7147fc62_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:22,946 - INFO - Processing PDF from S3...
2025-09-24 18:47:22,947 - INFO - Downloading PDF from S3 to /tmp/tmpnswlzbrg/7147fc62_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:23,606 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:47:23,607 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:23,609 - INFO - Splitting PDF 8a5a34e5_OLQ7TIWW6EVTC6BXA1II into 1 pages
2025-09-24 18:47:23,614 - INFO - Split PDF into 1 pages
2025-09-24 18:47:23,614 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:23,614 - INFO - Expected pages: [1]
2025-09-24 18:47:24,122 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg -> s3://document-extraction-logistically/temp/d8fd259d_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:24,123 - INFO - 🔍 [18:47:24] Starting classification: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:24,124 - INFO - ⬆️ [18:47:24] Uploading: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:24,126 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:24,144 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:24,150 - INFO - Page 1: Extracted 372 characters, 28 lines from 66b344b0_MMNWUYGBLL1K5KSJBNOT_0618595c_page_001.pdf
2025-09-24 18:47:24,153 - INFO - Successfully processed page 1
2025-09-24 18:47:24,155 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d8fd259d_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:24,155 - INFO - Processing image from S3...
2025-09-24 18:47:24,155 - INFO - Combined 1 pages into final text
2025-09-24 18:47:24,159 - INFO - Text validation for 66b344b0_MMNWUYGBLL1K5KSJBNOT: 389 characters, 1 pages
2025-09-24 18:47:24,159 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:24,159 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:24,255 - INFO - Page 1: Extracted 493 characters, 42 lines from 098ec02c_GKNF55W2CF2JR6EBXMPX_be7de2c4_page_001.pdf
2025-09-24 18:47:24,255 - INFO - Successfully processed page 1
2025-09-24 18:47:24,255 - INFO - Combined 1 pages into final text
2025-09-24 18:47:24,256 - INFO - Text validation for 098ec02c_GKNF55W2CF2JR6EBXMPX: 510 characters, 1 pages
2025-09-24 18:47:24,256 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:24,256 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:24,466 - INFO - Page 1: Extracted 695 characters, 58 lines from e85947b9_KDFG6JJAZV41YZP4TEGN_f0b008b6_page_001.pdf
2025-09-24 18:47:24,466 - INFO - Successfully processed page 1
2025-09-24 18:47:24,467 - INFO - Combined 1 pages into final text
2025-09-24 18:47:24,467 - INFO - Text validation for e85947b9_KDFG6JJAZV41YZP4TEGN: 712 characters, 1 pages
2025-09-24 18:47:24,467 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:24,467 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:24,662 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:47:24,662 - INFO - Splitting PDF into individual pages...
2025-09-24 18:47:24,663 - INFO - Splitting PDF 7147fc62_V3BCW6BW9XVKKO6WY2YJ into 1 pages
2025-09-24 18:47:24,665 - INFO - Split PDF into 1 pages
2025-09-24 18:47:24,665 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:47:24,665 - INFO - Expected pages: [1]
2025-09-24 18:47:24,818 - INFO - Page 1: Extracted 311 characters, 32 lines from 5b4a6312_O1YJBQBLYAU6D0SDDKAU_7da1d4ba_page_001.pdf
2025-09-24 18:47:24,818 - INFO - Successfully processed page 1
2025-09-24 18:47:24,818 - INFO - Combined 1 pages into final text
2025-09-24 18:47:24,818 - INFO - Text validation for 5b4a6312_O1YJBQBLYAU6D0SDDKAU: 328 characters, 1 pages
2025-09-24 18:47:24,818 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:24,818 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:25,102 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg -> s3://document-extraction-logistically/temp/86848a79_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:25,103 - INFO - 🔍 [18:47:25] Starting classification: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:25,104 - INFO - Initializing TextractProcessor...
2025-09-24 18:47:25,113 - INFO - Initializing BedrockProcessor...
2025-09-24 18:47:25,115 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/86848a79_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:25,116 - INFO - Processing image from S3...
2025-09-24 18:47:26,123 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fcd5f268-bf3e-433e-9157-02870bfd10df', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:25 GMT', 'content-type': 'application/json', 'content-length': '2492', 'connection': 'keep-alive', 'x-amzn-requestid': 'fcd5f268-bf3e-433e-9157-02870bfd10df'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page, labeled <page1>. We need to classify the document type. Let\'s look at content.\n\nIt says "Capstone Logistics", address, phone, FED ID, date/time, Receipt #, Location, Work Date, Bill Code, Carrier: BLUE STAR, Dock, Door, Purchase Orders Vendor, etc. It lists "Total Initial Pallets", "Total Finished Pallets", "Total Case Count", "Total Weight", Trailer Number, Tractor Number, BOL, Comments, etc. Base Charge, Convenience Fee, Total Cost, Payments, Amount.\n\nThis looks like a "bill of lading" or "invoice"? It has BOL, but it\'s more like a receipt for loading/unloading, with charges, base charge, convenience fee, etc. This might be a "lumper receipt" because it has "Unloaders" and "Base Charge", "Convenience Fee", "Total Cost". However, lumper receipt typically has PO, Door number, Date-Time, Initial pallets, finished pallets, service charge, etc. This page has "Unloaders: 2", charges. There\'s no explicit "PO" but there is "Purchase Orders" vendor. Could be a "bill of lading"? But contains charges, payments. Might be an invoice from carrier for the load. But there is no HS codes or customs. So not comm_invoice. It\'s not a "combined_carrier_documents" because single page. It\'s not a "log" or "pack_list". It\'s not a "rate_confirmation". So likely "invoice" or "lumper_receipt". Let\'s differentiate: lumper receipt: "If invoice/receipt has HS/HTS code, Country of Origin, terms of sale etc then it is comm_invoice; otherwise, it is invoice." For lumper receipt: "If has some of keywords similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, then it is lumper_receipt; otherwise, it is invoice." This page has Door number, date/time, initial pallets, finished pallets, carrier, service charge, total amount. So it\'s lumper_receipt.\n\nThus doc_type: lumper_receipt. page_no: 1.\n\nReturn an array of objects? The tool expects documents array with objects. So we pass single object: {page_no:1, doc_type:"lumper_receipt"}.\n\nLet\'s call tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_v23gPK0dSx6LQLk_C6qSGQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2786, 'outputTokens': 513, 'totalTokens': 3299}, 'metrics': {'latencyMs': 2181}}
2025-09-24 18:47:26,124 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c1d3c70c_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:26,151 - INFO - 

G0H0K1LRWDOG7LXAKKQ7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:26,152 - INFO - 

✓ Saved result: output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:47:26,441 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c1d3c70c_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:26,724 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3d787f2f-8f7d-4852-aa5f-946b7c78cc1a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:26 GMT', 'content-type': 'application/json', 'content-length': '1143', 'connection': 'keep-alive', 'x-amzn-requestid': '3d787f2f-8f7d-4852-aa5f-946b7c78cc1a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Need to determine doc_type. Let\'s analyze content. The page seems to be a receipt? It has fields like "Receipt #", "Bill Code", "Carrier", "Purchase Orders", "Total Initial Pallets", "Total Finished Pallets", "Total Case Count", "Trailer Number", "Base Charge", "Convenience Fee", "Total Cost", "Payments", "CapstonePay". This looks like a "Carrier Invoice" or "Invoice" type. Is it a commercial invoice? It doesn\'t mention HS code, Customs, etc. So likely "invoice". It\'s not a packing list. There\'s no "Bill of Lading" header. There\'s no "Proof of Delivery" with signature. There\'s no weight. So invoice seems right.\n\nThus doc_type: "invoice". For page1, output object with page_no:1 and doc_type: "invoice". Ensure correct format.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2801, 'outputTokens': 207, 'totalTokens': 3008}, 'metrics': {'latencyMs': 1113}}
2025-09-24 18:47:26,727 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd7eb5b7b-c5f1-47f0-9586-cafc7a0308ed', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:26 GMT', 'content-type': 'application/json', 'content-length': '1337', 'connection': 'keep-alive', 'x-amzn-requestid': 'd7eb5b7b-c5f1-47f0-9586-cafc7a0308ed'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The content looks like a receipt? It has BOL Number, PO #, Driver Name, Amount, convenience fee, grand total. Might be a lumper receipt? The description says lumper receipt includes PO, door number, date-time, initial pallets, finished pallets, carrier, service charge, total amount. This page has BOL Number, PO #, driver name, amounts. No door number. It has payment method, visa. Could be a lumper receipt, but could also be a "invoice"? But invoice usually includes carrier billing address, shipment ID, line charges, total due. This has an amount and grand total. Could be a lumper receipt. The header "Relay Sale# 8000" maybe indicates a sale. The presence of "BOL Number" suggests a BOL. But the page says "Receipt Details" and "Payment Method". Looks like a payment receipt for a load. That matches lumper receipt. So doc_type = lumper_receipt.\n\nWe have only one page. So output objects array with one object: page_no:1, doc_type:"lumper_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2674, 'outputTokens': 259, 'totalTokens': 2933}, 'metrics': {'latencyMs': 1338}}
2025-09-24 18:47:26,727 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e85947b9_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:26,728 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/66b344b0_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:26,749 - INFO - 

KDFG6JJAZV41YZP4TEGN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:47:26,749 - INFO - 

✓ Saved result: output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:47:27,030 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e85947b9_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:27,040 - INFO - 

MMNWUYGBLL1K5KSJBNOT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:27,040 - INFO - 

✓ Saved result: output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:47:27,188 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '312bb9d2-297e-4503-9dd1-af69e66c4916', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:26 GMT', 'content-type': 'application/json', 'content-length': '1134', 'connection': 'keep-alive', 'x-amzn-requestid': '312bb9d2-297e-4503-9dd1-af69e66c4916'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Text indicates: "Receipt#", "Invoice #", "Activity: Total Unloading", "Fee Amount: $275.00", "Convenience Fee: $9.62", "Total: $284.62". It also shows PO#, Carrier, Door. This looks like a carrier invoice (or lumper receipt). The instructions: For invoice: Bill issued by a carrier for goods being transported. For lumper receipt: invoice for services loaded/unloaded. The text includes "Total Unloading" and "Convenience Fee". That suggests lumper receipt. The rule: if invoice/receipt has HS/HTS code, etc. it\'s comm_invoice. Not present. If has door number, Date-Time, initial pallets, finished pallets, carrier, service charge, total amount => lumper_receipt. Yes. So classify as lumper_receipt. Output must be JSON via tool call. Only one page object. Let\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2660, 'outputTokens': 216, 'totalTokens': 2876}, 'metrics': {'latencyMs': 1137}}
2025-09-24 18:47:27,188 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5b4a6312_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:27,370 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/66b344b0_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:27,381 - INFO - 

O1YJBQBLYAU6D0SDDKAU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:27,381 - INFO - 

✓ Saved result: output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:47:27,548 - INFO - S3 Image temp/d8fd259d_Y9K6Z2GSAP496UQLOOGU.jpeg: Extracted 542 characters, 30 lines
2025-09-24 18:47:27,548 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:27,548 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:27,695 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5b4a6312_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:27,752 - INFO - S3 Image temp/86848a79_ZOBA55ELUA2GRE3UJM3I.jpg: Extracted 709 characters, 59 lines
2025-09-24 18:47:27,753 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:27,753 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:28,125 - INFO - Page 1: Extracted 657 characters, 54 lines from 8a5a34e5_OLQ7TIWW6EVTC6BXA1II_e36c429d_page_001.pdf
2025-09-24 18:47:28,125 - INFO - Successfully processed page 1
2025-09-24 18:47:28,126 - INFO - Combined 1 pages into final text
2025-09-24 18:47:28,126 - INFO - Text validation for 8a5a34e5_OLQ7TIWW6EVTC6BXA1II: 674 characters, 1 pages
2025-09-24 18:47:28,126 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:28,126 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:28,307 - INFO - Page 1: Extracted 343 characters, 28 lines from 7147fc62_V3BCW6BW9XVKKO6WY2YJ_fb284e01_page_001.pdf
2025-09-24 18:47:28,307 - INFO - Successfully processed page 1
2025-09-24 18:47:28,307 - INFO - Combined 1 pages into final text
2025-09-24 18:47:28,307 - INFO - Text validation for 7147fc62_V3BCW6BW9XVKKO6WY2YJ: 360 characters, 1 pages
2025-09-24 18:47:28,307 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:28,307 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:28,673 - INFO - Page 1: Extracted 460 characters, 36 lines from c8cb8012_NFMA1926AJ8R3TDZQALU_e6a31324_page_001.pdf
2025-09-24 18:47:28,673 - INFO - Successfully processed page 1
2025-09-24 18:47:28,674 - INFO - Combined 1 pages into final text
2025-09-24 18:47:28,674 - INFO - Text validation for c8cb8012_NFMA1926AJ8R3TDZQALU: 477 characters, 1 pages
2025-09-24 18:47:28,674 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:47:28,674 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:47:30,055 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2172f771-c8f1-4880-a87e-ebae15d50384', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:29 GMT', 'content-type': 'application/json', 'content-length': '4949', 'connection': 'keep-alive', 'x-amzn-requestid': '2172f771-c8f1-4880-a87e-ebae15d50384'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content: It\'s a delivery receipt? Let\'s examine keywords. It\'s IDSH, Innovative Distribution Services, address, phone, Fed ID, Status, Manager, Date, Truck, Trailer, Door, Carrier: Modus Logistics, PO(s): 2644663, Vendor(s): AMERCAREROYAL LLC, Subtotal, Convenience Fee, Total Charge, Billing Type: Relay Checkout, Payment type, Driver Name, Driver Signature.\n\nThis seems like a "Relay Checkout" maybe a "Relay" document: likely a "Relay checkout" is a type of "Delivery Receipt" or "Proof of Delivery"? But there is no explicit "Proof of Delivery" header. There is a Delivery? There\'s a Driver Name and Signature. It is related to a driver. The context: IDSH, Innovative Distribution Services. Could be a "Liftgate" or "Delivery ticket". But the tags: "Relay Checkout" suggests a "Relay" system where a driver picks up a load at a relay location. The document includes driver signature. That could be a POD. The page includes "PO(s): 2644663". It might be a POD? But we need to choose from enumerated types: invoice, comm_invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, comm_invoice, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, other, tender_from_cust, so_confirmation, ingate, outgate.\n\nWe need to assign doc_type.\n\nLet\'s see if it matches "pod" (Proof of Delivery). POD usually includes "Proof of Delivery", "Delivery Ticket", Received by, Delivered, Delivery Receipt, Date Received, delivery address, signatures, condition remarks. This document does not have "Proof of Delivery" explicitly. But it does have "Driver Name" and "Driver Signature". There\'s "Total Charge", "Billing Type: Relay Checkout". It\'s a payment receipt for a relay. Might be a "lumper receipt"? Lumper receipt includes PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This has PO, Door, Date, Carrier, Total Charge, Payment type. Could be lumper receipt. But lumper receipt is for services like loading/unloading labor. This may be a "Relay Checkout" for a load at a relay location; could be a "relay checkout" which is like a "relay receipt" maybe a type of "lumper receipt"? The lumper receipt is a separate document for loading/unloading labor, but this includes total charge, convenience fee. It might be a "relay checkout" that charges the driver for the relay. Let\'s search definitions: lumper receipt: "Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount". This page has PO, Door, Date, Carrier, Total Charge. Lacks pallets but may not be critical. It\'s a receipt for the relay checkout. So lumper receipt seems plausible.\n\nBut lumper receipt is a type of invoice. But invoice vs lumper receipt: "If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), then it is comm_invoice; otherwise, if door number, date-time, etc. qualifies lumper_receipt; otherwise invoice." This page has door number, date, PO. So lumper receipt.\n\nCheck if it\'s a "combined_carrier_documents": The page only shows one set of headings, not multiple. So not combined.\n\nCheck if "invoice": it\'s a billing of $260, with subtotal, convenience fee, etc. That is like a carrier invoice. But the presence of driver signature and driver name might indicate it\'s a relay checkout receipt, not a typical invoice. But the definition says invoice: "Bill issued by a carrier for goods being transported." This page shows a bill? It shows "Total Charge", "Billing Type: Relay Checkout", "Relay Payment Type: credit_card". It\'s a billing to the driver. It\'s a receipt, maybe a "relay checkout receipt" which is a type of invoice. But lumper receipt is specifically for labor. This includes a fee for relay. So lumper receipt.\n\nGiven the guidelines: "If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), then it is comm_invoice; otherwise, if it has door number, Date-Time, initial pallets, finished pallets, carrier, service charge, total amount, then it is lumper_receipt." This has door number, Date, carrier, total amount, but no pallets. So lumper_receipt.\n\nThus we should classify doc_type as "lumper_receipt".\n\nReturn JSON: array with object for page 1: page_no 1, doc_type "lumper_receipt".\n\nWe must use tool call. So produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2716, 'outputTokens': 1069, 'totalTokens': 3785}, 'metrics': {'latencyMs': 4617}}
2025-09-24 18:47:30,055 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/098ec02c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:30,063 - INFO - 

GKNF55W2CF2JR6EBXMPX.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:30,063 - INFO - 

✓ Saved result: output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:47:30,363 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/098ec02c_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:30,449 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f1e2d7b7-a80b-472d-92e5-51900a591da5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:30 GMT', 'content-type': 'application/json', 'content-length': '1280', 'connection': 'keep-alive', 'x-amzn-requestid': 'f1e2d7b7-a80b-472d-92e5-51900a591da5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with receipt info. We need to classify the document type. Let\'s read: Capstone Logistics, receipt #, work date, bill code, carrier, dock, door, purchase orders, vendor, etc. It has "Total Finished Pallets", "Total Case Count", "Total Weight", "Trailer Number", "Tractor Number", "BOL", "Base Charge" etc. It looks like a "Receipt" from a carrier? Could be a "Clear to Pay" or "Invoice"? It mentions "Payments" and "Total Cost" and "CapstonePay". It seems it\'s a receipt of charges from a carrier. It might be an "invoice" from a carrier. The doc says "Receipt" and lists charges. This seems like a carrier invoice.\n\nKey fields: Bill Code, Carrier, Base Charge, Convenience Fee, Total Cost, Payments. That matches invoice type. So doc_type = "invoice".\n\nWe also have page 1 only. So we output an array with one object: {page_no:1, doc_type:"invoice"}.\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2783, 'outputTokens': 248, 'totalTokens': 3031}, 'metrics': {'latencyMs': 1183}}
2025-09-24 18:47:30,449 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8a5a34e5_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:30,469 - INFO - 

OLQ7TIWW6EVTC6BXA1II.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:47:30,469 - INFO - 

✓ Saved result: output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:47:30,708 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'df96cc36-af2a-4280-871f-6fe528649f33', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:30 GMT', 'content-type': 'application/json', 'content-length': '1170', 'connection': 'keep-alive', 'x-amzn-requestid': 'df96cc36-af2a-4280-871f-6fe528649f33'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to analyze the content. The page shows "Receipt number 128521", "CAPE STONE LUMPERS, LLC", "GENERAL RECEIPT", date, time, cashier, customer name, items, quantity, price, subtotal, total, payment method. This looks like a pay receipt for a lumpers service. According to definitions, "lumper_receipt" is an invoice or receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. The page has "Item(s)" "Quantity" "Price" "Subtotal" etc. This is a receipt of a lumpers service. So doc_type = lumper_receipt. It\'s a single page.\n\nWe must output via classify_logistics_doc_type tool. Provide documents array with one object: page_no: 1, doc_type: "lumper_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2671, 'outputTokens': 223, 'totalTokens': 2894}, 'metrics': {'latencyMs': 1205}}
2025-09-24 18:47:30,708 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7147fc62_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:30,757 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8a5a34e5_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:30,771 - INFO - 

V3BCW6BW9XVKKO6WY2YJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:30,772 - INFO - 

✓ Saved result: output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:47:31,070 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7147fc62_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:31,490 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a363bd5a-5254-47a8-b5fa-49ee7b2598fb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:31 GMT', 'content-type': 'application/json', 'content-length': '1246', 'connection': 'keep-alive', 'x-amzn-requestid': 'a363bd5a-5254-47a8-b5fa-49ee7b2598fb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. The text seems like a receipt, showing charges, total due. It\'s a carrier receipt. The keywords: "RECEIPT#", "Total Due", "Payment", "Carrier", "PO". It seems like a lumper receipt? The definition: lumper_receipt: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. Yes matches lumper. Also invoice category: but difference: invoice vs comm_invoice: if no HS code etc. lumper receipt has door number, date/time, pallets, service charge. So classify as lumper_receipt.\n\nWe must use the tool \'classify_logistics_doc_type\' with documents array containing page_no and doc_type. So output:\n\n{\n  "documents": [\n    { "page_no": 1, "doc_type": "lumper_receipt" }\n  ]\n}\n\nBut tool expects JSON output: we call function.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2765, 'outputTokens': 236, 'totalTokens': 3001}, 'metrics': {'latencyMs': 2706}}
2025-09-24 18:47:31,491 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d8fd259d_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:31,507 - INFO - 

Y9K6Z2GSAP496UQLOOGU.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:31,507 - INFO - 

✓ Saved result: output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:47:31,797 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d8fd259d_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:32,107 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3d4dda69-284f-4ac9-9ad8-5c75beac5d83', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:31 GMT', 'content-type': 'application/json', 'content-length': '3271', 'connection': 'keep-alive', 'x-amzn-requestid': '3d4dda69-284f-4ac9-9ad8-5c75beac5d83'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. We need to determine doc_type. The content seems like a receipt or invoice for a freight service. Let\'s see keywords: "Receipt #", "Purchase Orders", "Total Initial Pallets", "Total Finished Pallets", "Base Charge", "Convenience Fee", "Total Cost", "Payments", "Amount". This looks like a freight bill or invoice. Is it a commercial invoice? It doesn\'t have HS code, customs info. It appears to be a carrier invoice for a freight. So doc_type = invoice.\n\nCheck if any other type: "lumper receipt"? That would include "PO", "Door number", "Date-Time", "Initial pallets", "finished pallets", "carrier", "service charge", "total amount". This page has many of those. It includes "Purchase Orders", "Total Initial Pallets", "Total Finished Pallets", "Base Charge", "Convenience Fee", "Total Cost", "Payments". It also has a "Receipt #". Is this a lumper receipt? The instructions in definition: If has PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, then lumper_receipt. But also "invoice" has keywords: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc. This page does not show "Invoice" label. It shows "Receipt #". It\'s not clearly "invoice". The lumper receipt description: "Invoice or Receipt for services provided (loading/unloading labor)." This page has "Base Charge" and "Convenience Fee" and "Total Cost". Could be a lumper receipt. But the page also has "Purchase Orders", "Total Initial Pallets", "Total Finished Pallets", "Total Case Count". That seems freight bill. The lumper receipt description: "If has PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount." This page has PO, Door number (Door:4), Date-Time (08:32:49 September 23, 2025), Initial pallets, finished pallets, carrier (FLAWLESS), service charge (Convenience Fee?), total amount (Total Cost 360). So qualifies for lumper receipt.\n\nBut lumper receipt is a subset of invoice? The instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type." There\'s only one page. We need to decide between invoice and lumper_receipt.\n\nCheck earlier note: "If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice." That would be invoice. But lumper receipt is more specific: "If has PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount then it is lumper_receipt; otherwise, it is invoice." So indeed lumper receipt is a special classification within invoice. So we should classify as lumper_receipt.\n\nThus output: page_no 1, doc_type "lumper_receipt".\n\nWe must call tool classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2800, 'outputTokens': 693, 'totalTokens': 3493}, 'metrics': {'latencyMs': 3102}}
2025-09-24 18:47:32,107 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/86848a79_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:32,127 - INFO - 

ZOBA55ELUA2GRE3UJM3I.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:32,127 - INFO - 

✓ Saved result: output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:47:32,514 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/86848a79_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:33,232 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0e237ba9-1bc2-4b05-bdf1-acbe4d62ed0e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:17:33 GMT', 'content-type': 'application/json', 'content-length': '3717', 'connection': 'keep-alive', 'x-amzn-requestid': '0e237ba9-1bc2-4b05-bdf1-acbe4d62ed0e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to decide doc_type. The text: "Express Freight Handlers, Inc." "PO No:" "Vendor Name:" etc. The mention "PO No:" suggests purchase order? But also "Load No:" "Truck No:" etc. There\'s "Payment Type: Relay". This looks like a "Purchase Order" or a "Lumper Receipt"? The mention "PO No:" is a field. But also "Load No:" "Driver Name". The content could be a "Purchase Order" or a "Lumper Receipt". The instructions: lumper_receipt: "If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), then it is comm_invoice; otherwise, it is invoice." But lumper_receipt: "PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount." This page has PO No, Door No, Date, Driver Name, Payment Type, etc. It might be a lumper receipt? But lumper receipts are for loading/unloading labor. The page shows "PO No", "Qty", "Weight", "Charge", "Fee", "Add\'l Info". But also the header "Express Freight Handlers, Inc." The title is not "Lumper Receipt". Does it include "Service Charge"? It has "Charge: $140.0", "Fee: $7.7". That\'s similar. The presence of "PO No" indicates that it\'s referencing a purchase order. But it might be a "Purchase Order" itself? However, Purchase Order usually has items list. This page has "Qty", "Weight", "Charge", "Fee". That looks like a receipt of services, with charges. It might be a "Lumper Receipt" indeed. But lumper receipts are receipts for services provided (loading/unloading labor). They mention PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. We have PO No, Door No, Date, Driver Name, Payment Type, etc. Not initial/final pallets. But it\'s close enough? The presence of "Charge" and "Fee" indicates charges. The doc type could be "lumper_receipt" or "invoice". Invoice would be for goods being transported. But this page says "LINDAR FORMING" which might be the vendor name. The context: Express Freight Handlers, Inc. "Warehouse: BUNZL CINCINNATI". Possibly this is a "Purchase Order" for a load? But there is "Load No". Could be a "PO" from the carrier to the vendor. But the presence of "PO No:" and "Vendor Name:" suggests the receiving party. But the page also has "Qty: 293800" "Weight: 932" "Charge: $140.0" "Fee: $7.7". That seems like a "Purchase Order" line items? But there is no total. It\'s a single line. Usually PO has items with description. This seems more like a "Lumper Receipt" (bill of lading? no). But lumper receipts reference "service charge".\n\nGiven the criteria: lumper_receipt: "If the invoice/receipt has PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount." We have PO No, Door No, Date, Carrier (MFS TRANSPORT), but we don\'t have initial/final pallets. We have Charge and Fee. Total amount? Not explicit, but maybe Charge + Fee = total. So qualifies. So classify as lumper_receipt.\n\nAlternatively, could be "invoice" because it\'s a bill for goods? But "invoice" is for goods being transported, not services. This is about loading/unloading. So lumper_receipt likely.\n\nThus output page 1 with doc_type "lumper_receipt".\n\nWe must use the tool to output JSON array of objects.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2717, 'outputTokens': 848, 'totalTokens': 3565}, 'metrics': {'latencyMs': 3414}}
2025-09-24 18:47:33,232 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c8cb8012_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:33,254 - INFO - 

NFMA1926AJ8R3TDZQALU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:47:33,254 - INFO - 

✓ Saved result: output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:47:33,541 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c8cb8012_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:33,541 - INFO - 
📊 Processing Summary:
2025-09-24 18:47:33,541 - INFO -    Total files: 10
2025-09-24 18:47:33,542 - INFO -    Successful: 10
2025-09-24 18:47:33,542 - INFO -    Failed: 0
2025-09-24 18:47:33,542 - INFO -    Duration: 18.56 seconds
2025-09-24 18:47:33,542 - INFO -    Output directory: output
2025-09-24 18:47:33,542 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:47:33,542 - INFO -    📄 G0H0K1LRWDOG7LXAKKQ7.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 GKNF55W2CF2JR6EBXMPX.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 KDFG6JJAZV41YZP4TEGN.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:47:33,542 - INFO -    📄 MMNWUYGBLL1K5KSJBNOT.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 NFMA1926AJ8R3TDZQALU.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 O1YJBQBLYAU6D0SDDKAU.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 OLQ7TIWW6EVTC6BXA1II.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:47:33,542 - INFO -    📄 V3BCW6BW9XVKKO6WY2YJ.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 Y9K6Z2GSAP496UQLOOGU.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,542 - INFO -    📄 ZOBA55ELUA2GRE3UJM3I.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:47:33,543 - INFO - 
============================================================================================================================================
2025-09-24 18:47:33,543 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:47:33,543 - INFO - ============================================================================================================================================
2025-09-24 18:47:33,543 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:47:33,543 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:47:33,543 - INFO - G0H0K1LRWDOG7LXAKKQ7.pdf                           1      lumper_receipt                           run1_G0H0K1LRWDOG7LXAKKQ7.json                                                  
2025-09-24 18:47:33,543 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:47:33,543 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:47:33,543 - INFO - 
2025-09-24 18:47:33,544 - INFO - GKNF55W2CF2JR6EBXMPX.pdf                           1      lumper_receipt                           run1_GKNF55W2CF2JR6EBXMPX.json                                                  
2025-09-24 18:47:33,544 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:47:33,544 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:47:33,544 - INFO - 
2025-09-24 18:47:33,544 - INFO - KDFG6JJAZV41YZP4TEGN.pdf                           1      invoice                                  run1_KDFG6JJAZV41YZP4TEGN.json                                                  
2025-09-24 18:47:33,544 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:47:33,544 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:47:33,544 - INFO - 
2025-09-24 18:47:33,544 - INFO - MMNWUYGBLL1K5KSJBNOT.pdf                           1      lumper_receipt                           run1_MMNWUYGBLL1K5KSJBNOT.json                                                  
2025-09-24 18:47:33,544 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:47:33,544 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:47:33,544 - INFO - 
2025-09-24 18:47:33,544 - INFO - NFMA1926AJ8R3TDZQALU.pdf                           1      lumper_receipt                           run1_NFMA1926AJ8R3TDZQALU.json                                                  
2025-09-24 18:47:33,544 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:47:33,544 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:47:33,544 - INFO - 
2025-09-24 18:47:33,544 - INFO - O1YJBQBLYAU6D0SDDKAU.pdf                           1      lumper_receipt                           run1_O1YJBQBLYAU6D0SDDKAU.json                                                  
2025-09-24 18:47:33,544 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:47:33,544 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:47:33,544 - INFO - 
2025-09-24 18:47:33,544 - INFO - OLQ7TIWW6EVTC6BXA1II.pdf                           1      invoice                                  run1_OLQ7TIWW6EVTC6BXA1II.json                                                  
2025-09-24 18:47:33,545 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:47:33,545 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:47:33,545 - INFO - 
2025-09-24 18:47:33,545 - INFO - V3BCW6BW9XVKKO6WY2YJ.pdf                           1      lumper_receipt                           run1_V3BCW6BW9XVKKO6WY2YJ.json                                                  
2025-09-24 18:47:33,545 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:47:33,545 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:47:33,545 - INFO - 
2025-09-24 18:47:33,545 - INFO - Y9K6Z2GSAP496UQLOOGU.jpeg                          1      lumper_receipt                           run1_Y9K6Z2GSAP496UQLOOGU.json                                                  
2025-09-24 18:47:33,545 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:47:33,545 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:47:33,545 - INFO - 
2025-09-24 18:47:33,545 - INFO - ZOBA55ELUA2GRE3UJM3I.jpg                           1      lumper_receipt                           run1_ZOBA55ELUA2GRE3UJM3I.json                                                  
2025-09-24 18:47:33,545 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:47:33,545 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:47:33,545 - INFO - 
2025-09-24 18:47:33,545 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:47:33,545 - INFO - Total entries: 10
2025-09-24 18:47:33,545 - INFO - ============================================================================================================================================
2025-09-24 18:47:33,545 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:47:33,545 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:47:33,545 - INFO -   1. G0H0K1LRWDOG7LXAKKQ7.pdf            Page 1   → lumper_receipt  | run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:47:33,545 - INFO -   2. GKNF55W2CF2JR6EBXMPX.pdf            Page 1   → lumper_receipt  | run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:47:33,545 - INFO -   3. KDFG6JJAZV41YZP4TEGN.pdf            Page 1   → invoice         | run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:47:33,546 - INFO -   4. MMNWUYGBLL1K5KSJBNOT.pdf            Page 1   → lumper_receipt  | run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:47:33,546 - INFO -   5. NFMA1926AJ8R3TDZQALU.pdf            Page 1   → lumper_receipt  | run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:47:33,546 - INFO -   6. O1YJBQBLYAU6D0SDDKAU.pdf            Page 1   → lumper_receipt  | run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:47:33,546 - INFO -   7. OLQ7TIWW6EVTC6BXA1II.pdf            Page 1   → invoice         | run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:47:33,546 - INFO -   8. V3BCW6BW9XVKKO6WY2YJ.pdf            Page 1   → lumper_receipt  | run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:47:33,546 - INFO -   9. Y9K6Z2GSAP496UQLOOGU.jpeg           Page 1   → lumper_receipt  | run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:47:33,546 - INFO -  10. ZOBA55ELUA2GRE3UJM3I.jpg            Page 1   → lumper_receipt  | run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:47:33,546 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:47:33,546 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 18.564978, 'processed_files': [{'filename': 'G0H0K1LRWDOG7LXAKKQ7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf'}, {'filename': 'GKNF55W2CF2JR6EBXMPX.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf'}, {'filename': 'KDFG6JJAZV41YZP4TEGN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf'}, {'filename': 'MMNWUYGBLL1K5KSJBNOT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf'}, {'filename': 'NFMA1926AJ8R3TDZQALU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf'}, {'filename': 'O1YJBQBLYAU6D0SDDKAU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf'}, {'filename': 'OLQ7TIWW6EVTC6BXA1II.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf'}, {'filename': 'V3BCW6BW9XVKKO6WY2YJ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf'}, {'filename': 'Y9K6Z2GSAP496UQLOOGU.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg'}, {'filename': 'ZOBA55ELUA2GRE3UJM3I.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg'}]}
