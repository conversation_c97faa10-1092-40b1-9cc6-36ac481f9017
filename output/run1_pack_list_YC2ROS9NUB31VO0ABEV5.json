{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.8905228671428631e-06, "Y": 0.0}, {"X": 1.0, "Y": 1.0289252713846508e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "ea54c8b3-82f1-430d-80ea-b53be08f2f2f", "Relationships": [{"Type": "CHILD", "Ids": ["3e1c549b-ecd0-4a41-8548-984e485bf186", "ea5b596a-5df1-46dd-89ee-987dff9d15ab", "baff01de-8cb6-4ba5-aef4-d5bbda39c129", "cd619fc8-70f7-44a9-a7c1-b6456ed0b8a4", "7d95bea6-3bef-4cca-98c1-7ed14ac1a116", "275afcd0-1901-407c-a685-bf1adef24e77", "729a0deb-5010-42a3-8f7b-db898aa42b14", "ca3d004e-c560-4d54-87b7-9b79803b5be4", "e08c97c5-2be4-44ab-a944-311fa5cbaaf9", "8d487044-05d2-4952-a63a-2a1a7e83b7ae", "b4af73c7-ff10-4071-8a5e-3ccd4b410a5f", "dfe599a7-968a-4626-832e-755210a0e0a9", "cd14644f-95f0-4260-81ab-030b392d6011", "cfb01b20-8557-4cf1-a998-b9e1801c9b14", "f800eae2-f8fa-4df9-bad4-248f9de20f35", "ece4a9ac-94d2-4447-8c73-d5de289f882a", "bd4cdf02-098d-440e-ac94-4f0a3a6b460c", "cebd6f68-bbb6-4fbe-b344-dd35019e5e61", "c4b10925-960c-43ad-b825-2bfd837a635d", "d69d5d89-7858-42e9-b75f-8f8e175f6deb", "18aa3270-11be-402c-9af3-77e00d2cd485", "e02580e1-6446-4ef1-b56a-d048481c267d", "e5a8ce57-a696-48fb-ba02-1c3eb8e470f9", "63727379-77f0-4ea8-a26b-fad1e9783caa", "ce174f2e-9207-450c-8e85-48858bff6467", "408ff044-c475-44f9-a74c-7baa92307218", "56d034be-30d3-45fd-9022-987066534cc2", "3adb76ff-f28a-4fd8-82cb-0e6004efb2ac", "8294b66f-27af-48fe-ba2d-7ae23ebaed40", "8b2fb88d-8a32-4991-8443-4507ec556d77", "a0ff14a5-26f2-4215-a415-03cab47c81d8"]}]}, {"BlockType": "LINE", "Confidence": 99.86288452148438, "Text": "FAB-MASTERS, INC.", "Geometry": {"BoundingBox": {"Width": 0.33993303775787354, "Height": 0.027209147810935974, "Left": 0.11321188509464264, "Top": 0.06286675482988358}, "Polygon": [{"X": 0.11329008638858795, "Y": 0.06286675482988358}, {"X": 0.453144907951355, "Y": 0.0630674734711647}, {"X": 0.4530876874923706, "Y": 0.09007590264081955}, {"X": 0.11321188509464264, "Y": 0.08987520635128021}]}, "Id": "3e1c549b-ecd0-4a41-8548-984e485bf186", "Relationships": [{"Type": "CHILD", "Ids": ["619b8d1d-98e1-4935-a89c-2b8036ef4e8d", "bb3808aa-489a-4fd0-b176-762e8ede4061"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Packing Slip", "Geometry": {"BoundingBox": {"Width": 0.25006216764450073, "Height": 0.03609446808695793, "Left": 0.6520259976387024, "Top": 0.04386517405509949}, "Polygon": [{"X": 0.6520857810974121, "Y": 0.04386517405509949}, {"X": 0.9020881652832031, "Y": 0.04401284083724022}, {"X": 0.9020488262176514, "Y": 0.07995964586734772}, {"X": 0.6520259976387024, "Y": 0.07981199771165848}]}, "Id": "ea5b596a-5df1-46dd-89ee-987dff9d15ab", "Relationships": [{"Type": "CHILD", "Ids": ["5675665f-5cbf-497a-90e4-72015e70eee9", "3532bbf3-9950-4dd3-a4e0-e716c41961e3"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "42011", "Geometry": {"BoundingBox": {"Width": 0.03689040616154671, "Height": 0.00999403651803732, "Left": 0.8584620952606201, "Top": 0.08601761609315872}, "Polygon": [{"X": 0.8584739565849304, "Y": 0.08601761609315872}, {"X": 0.8953524827957153, "Y": 0.08603939414024353}, {"X": 0.8953414559364319, "Y": 0.09601165354251862}, {"X": 0.8584620952606201, "Y": 0.09598987549543381}]}, "Id": "baff01de-8cb6-4ba5-aef4-d5bbda39c129", "Relationships": [{"Type": "CHILD", "Ids": ["1706fe50-48e8-45d5-b670-de0a4b260c2c"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.06147180125117302, "Height": 0.009599120356142521, "Left": 0.10660583525896072, "Top": 0.14761248230934143}, "Polygon": [{"X": 0.10663367062807083, "Y": 0.14761248230934143}, {"X": 0.16807764768600464, "Y": 0.14764876663684845}, {"X": 0.16805115342140198, "Y": 0.15721160173416138}, {"X": 0.10660583525896072, "Y": 0.15717533230781555}]}, "Id": "cd619fc8-70f7-44a9-a7c1-b6456ed0b8a4", "Relationships": [{"Type": "CHILD", "Ids": ["b226e301-0619-4724-b883-57c022ad93c9"]}]}, {"BlockType": "LINE", "Confidence": 99.67919921875, "Text": "901 Joliet St", "Geometry": {"BoundingBox": {"Width": 0.08280424773693085, "Height": 0.009933996014297009, "Left": 0.3148934245109558, "Top": 0.14928953349590302}, "Polygon": [{"X": 0.3149174749851227, "Y": 0.14928953349590302}, {"X": 0.39769765734672546, "Y": 0.14933840930461884}, {"X": 0.39767545461654663, "Y": 0.1592235267162323}, {"X": 0.3148934245109558, "Y": 0.15917466580867767}]}, "Id": "7d95bea6-3bef-4cca-98c1-7ed14ac1a116", "Relationships": [{"Type": "CHILD", "Ids": ["53ada454-e707-45bd-9496-a882689b9206", "36c0ae82-cb9b-4876-9c6f-26b165047534", "4ddc452b-6e0f-4ea2-bdf8-363d613670f4"]}]}, {"BlockType": "LINE", "Confidence": 99.9133529663086, "Text": "Ship To: RexCon LLC", "Geometry": {"BoundingBox": {"Width": 0.14269210398197174, "Height": 0.011826923117041588, "Left": 0.5503053069114685, "Top": 0.1514817178249359}, "Polygon": [{"X": 0.5503275394439697, "Y": 0.1514817178249359}, {"X": 0.692997395992279, "Y": 0.15156593918800354}, {"X": 0.6929789781570435, "Y": 0.16330863535404205}, {"X": 0.5503053069114685, "Y": 0.16322441399097443}]}, "Id": "275afcd0-1901-407c-a685-bf1adef24e77", "Relationships": [{"Type": "CHILD", "Ids": ["c2729f79-2643-40e4-8b72-0cc9ced00d3c", "37ca1e71-4a14-4745-9773-97691ed6e415", "bcaabd12-92fc-4df5-9cb4-e5653177a8f9", "b0fde4d5-0b3c-48b0-89e1-6b64db576e90"]}]}, {"BlockType": "LINE", "Confidence": 99.94386291503906, "Text": "Janesville, WI 53548", "Geometry": {"BoundingBox": {"Width": 0.1387762427330017, "Height": 0.011427939869463444, "Left": 0.31422558426856995, "Top": 0.16664530336856842}, "Polygon": [{"X": 0.3142532408237457, "Y": 0.16664530336856842}, {"X": 0.45300182700157166, "Y": 0.1667272001504898}, {"X": 0.4529778063297272, "Y": 0.1780732423067093}, {"X": 0.31422558426856995, "Y": 0.1779913455247879}]}, "Id": "729a0deb-5010-42a3-8f7b-db898aa42b14", "Relationships": [{"Type": "CHILD", "Ids": ["5c8fcf26-b2e4-4f8c-b0e5-da411e920c73", "51efe406-f96a-440b-b11e-536adbd63da1", "63e454d1-61bf-4d5e-9cd8-2b2786a6ade2"]}]}, {"BlockType": "LINE", "Confidence": 99.92080688476562, "Text": "2841 Whiting Rd.", "Geometry": {"BoundingBox": {"Width": 0.12023106217384338, "Height": 0.012425738386809826, "Left": 0.6099357604980469, "Top": 0.1702669858932495}, "Polygon": [{"X": 0.6099575161933899, "Y": 0.1702669858932495}, {"X": 0.7301667928695679, "Y": 0.17033794522285461}, {"X": 0.730148434638977, "Y": 0.1826927214860916}, {"X": 0.6099357604980469, "Y": 0.1826217770576477}]}, "Id": "ca3d004e-c560-4d54-87b7-9b79803b5be4", "Relationships": [{"Type": "CHILD", "Ids": ["e2081feb-cab1-4e8d-9ad4-d8873e856ffe", "cd29c88f-7545-4d86-8a6b-c78c0e95ddd3", "0fb31973-6a09-4a61-9103-279c0265b99e"]}]}, {"BlockType": "LINE", "Confidence": 99.91619873046875, "Text": "Phone: ************", "Geometry": {"BoundingBox": {"Width": 0.13726486265659332, "Height": 0.010705206543207169, "Left": 0.31468331813812256, "Top": 0.18416982889175415}, "Polygon": [{"X": 0.3147091567516327, "Y": 0.18416982889175415}, {"X": 0.4519481658935547, "Y": 0.1842508316040039}, {"X": 0.4519256353378296, "Y": 0.19487503170967102}, {"X": 0.31468331813812256, "Y": 0.19479402899742126}]}, "Id": "e08c97c5-2be4-44ab-a944-311fa5cbaaf9", "Relationships": [{"Type": "CHILD", "Ids": ["487a19fc-0dae-412e-8566-8d769d73f959", "6607fde2-4ee9-49e9-9237-88d28b2faf8e"]}]}, {"BlockType": "LINE", "Confidence": 99.87470245361328, "Text": "Burlington, WI 53105", "Geometry": {"BoundingBox": {"Width": 0.14988215267658234, "Height": 0.011947562918066978, "Left": 0.6102584004402161, "Top": 0.1879032999277115}, "Polygon": [{"X": 0.6102792620658875, "Y": 0.1879032999277115}, {"X": 0.7601405382156372, "Y": 0.18799175322055817}, {"X": 0.7601237297058105, "Y": 0.19985085725784302}, {"X": 0.6102584004402161, "Y": 0.19976241886615753}]}, "Id": "8d487044-05d2-4952-a63a-2a1a7e83b7ae", "Relationships": [{"Type": "CHILD", "Ids": ["d1edbb71-604a-48ef-989f-1b843b7cb14f", "a4cb8618-2c96-491d-9c6f-75d2f8271056", "e8e3dc4f-a6e2-4269-a753-c8f7fec80080"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Fax: ************", "Geometry": {"BoundingBox": {"Width": 0.12015782296657562, "Height": 0.010407835245132446, "Left": 0.3143838346004486, "Top": 0.2019696831703186}, "Polygon": [{"X": 0.3144090175628662, "Y": 0.2019696831703186}, {"X": 0.4345416724681854, "Y": 0.20204058289527893}, {"X": 0.43451932072639465, "Y": 0.21237751841545105}, {"X": 0.3143838346004486, "Y": 0.21230661869049072}]}, "Id": "b4af73c7-ff10-4071-8a5e-3ccd4b410a5f", "Relationships": [{"Type": "CHILD", "Ids": ["b3053f68-e694-4c17-8d0f-1f765aa069a2", "89b785ad-3312-4714-b087-9c900b40f126"]}]}, {"BlockType": "LINE", "Confidence": 99.95416259765625, "Text": "Ship Date: 9/19/2025", "Geometry": {"BoundingBox": {"Width": 0.15725833177566528, "Height": 0.012729189358651638, "Left": 0.10295334458351135, "Top": 0.23474736511707306}, "Polygon": [{"X": 0.10299021750688553, "Y": 0.23474736511707306}, {"X": 0.26021167635917664, "Y": 0.23484013974666595}, {"X": 0.260179340839386, "Y": 0.24747654795646667}, {"X": 0.10295334458351135, "Y": 0.24738378822803497}]}, "Id": "dfe599a7-968a-4626-832e-755210a0e0a9", "Relationships": [{"Type": "CHILD", "Ids": ["c3ae452c-e1e0-40c0-ad8a-aafd64a4cc79", "867bcb01-3faf-4c32-a68b-5e91ed65851c", "df32f4e1-a6d3-4397-94e8-4fd34017fe3a"]}]}, {"BlockType": "LINE", "Confidence": 99.9574203491211, "Text": "Sales Person: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.1292000561952591, "Height": 0.011380664072930813, "Left": 0.10228630900382996, "Top": 0.25242120027542114}, "Polygon": [{"X": 0.10231930762529373, "Y": 0.25242120027542114}, {"X": 0.23148636519908905, "Y": 0.2524974048137665}, {"X": 0.2314566969871521, "Y": 0.2638018727302551}, {"X": 0.10228630900382996, "Y": 0.2637256383895874}]}, "Id": "cd14644f-95f0-4260-81ab-030b392d6011", "Relationships": [{"Type": "CHILD", "Ids": ["56bb508e-6021-4e76-a301-7fa94171521a", "cbfdaa48-b97e-4195-a46d-c7d135c5895a", "5c0633eb-a180-4c60-a52d-40e14150ec90"]}]}, {"BlockType": "LINE", "Confidence": 99.90154266357422, "Text": "Bill To: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.09988313913345337, "Height": 0.010135631076991558, "Left": 0.550470769405365, "Top": 0.2580232620239258}, "Polygon": [{"X": 0.5504899024963379, "Y": 0.2580232620239258}, {"X": 0.6503539681434631, "Y": 0.25808218121528625}, {"X": 0.6503371596336365, "Y": 0.268158882856369}, {"X": 0.550470769405365, "Y": 0.26809996366500854}]}, "Id": "cfb01b20-8557-4cf1-a998-b9e1801c9b14", "Relationships": [{"Type": "CHILD", "Ids": ["1b95adbf-9a3b-46c1-bd37-d519030b4e7d", "6a095bc1-497a-4d80-b626-491e4ca5d8f8", "8f5e7bb2-166b-4dcd-803c-4f2c9ea95780"]}]}, {"BlockType": "LINE", "Confidence": 99.86494445800781, "Text": "Purchase Order: PUR3165-105166", "Geometry": {"BoundingBox": {"Width": 0.24311040341854095, "Height": 0.012326186522841454, "Left": 0.10205425322055817, "Top": 0.27028441429138184}, "Polygon": [{"X": 0.10208982974290848, "Y": 0.27028441429138184}, {"X": 0.3451646566390991, "Y": 0.2704278230667114}, {"X": 0.3451358675956726, "Y": 0.28261059522628784}, {"X": 0.10205425322055817, "Y": 0.28246718645095825}]}, "Id": "f800eae2-f8fa-4df9-bad4-248f9de20f35", "Relationships": [{"Type": "CHILD", "Ids": ["69d59a08-0ff0-411d-abc0-993bb99c8b5e", "7d9072a0-f87e-47d8-b35c-7755abb6bcdd", "e04dc943-cce5-4c60-99c7-74d6c65f6651"]}]}, {"BlockType": "LINE", "Confidence": 99.86819458007812, "Text": "Shipped Via: CPU", "Geometry": {"BoundingBox": {"Width": 0.13529877364635468, "Height": 0.01282179169356823, "Left": 0.10153787583112717, "Top": 0.2868586480617523}, "Polygon": [{"X": 0.1015750914812088, "Y": 0.2868586480617523}, {"X": 0.23683665692806244, "Y": 0.2869384288787842}, {"X": 0.23680338263511658, "Y": 0.2996804416179657}, {"X": 0.10153787583112717, "Y": 0.29960063099861145}]}, "Id": "ece4a9ac-94d2-4447-8c73-d5de289f882a", "Relationships": [{"Type": "CHILD", "Ids": ["d8708772-83c4-4fa3-8211-2bb88a65ffff", "4dee4c33-bbc8-4178-8721-a86f05635bfd", "c751ae1b-979a-4612-9742-07c0e6684303"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Part Number", "Geometry": {"BoundingBox": {"Width": 0.09081481397151947, "Height": 0.009804426692426205, "Left": 0.3630725145339966, "Top": 0.372030645608902}, "Polygon": [{"X": 0.36309516429901123, "Y": 0.372030645608902}, {"X": 0.45388731360435486, "Y": 0.3720841705799103}, {"X": 0.4538666903972626, "Y": 0.38183507323265076}, {"X": 0.3630725145339966, "Y": 0.38178154826164246}]}, "Id": "bd4cdf02-098d-440e-ac94-4f0a3a6b460c", "Relationships": [{"Type": "CHILD", "Ids": ["9dceaf07-c6ee-463c-bfb1-5367e729d2cf", "bbce17d2-ec97-4185-adca-4242bd413669"]}]}, {"BlockType": "LINE", "Confidence": 99.75386810302734, "Text": "Our Job#", "Geometry": {"BoundingBox": {"Width": 0.06422566622495651, "Height": 0.009322538040578365, "Left": 0.5602496862411499, "Top": 0.3742678463459015}, "Polygon": [{"X": 0.5602670311927795, "Y": 0.3742678463459015}, {"X": 0.6244753003120422, "Y": 0.37430569529533386}, {"X": 0.6244592666625977, "Y": 0.38359037041664124}, {"X": 0.5602496862411499, "Y": 0.38355252146720886}]}, "Id": "cebd6f68-bbb6-4fbe-b344-dd35019e5e61", "Relationships": [{"Type": "CHILD", "Ids": ["7a6cf97f-ebb3-420f-8f50-c1ac50d73948", "850e3089-21db-4974-88c1-4dd7e96f048b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Order Quantity", "Geometry": {"BoundingBox": {"Width": 0.10627743601799011, "Height": 0.011450564488768578, "Left": 0.6574920415878296, "Top": 0.3742363154888153}, "Polygon": [{"X": 0.6575108766555786, "Y": 0.3742363154888153}, {"X": 0.7637695074081421, "Y": 0.37429898977279663}, {"X": 0.7637534737586975, "Y": 0.3856869041919708}, {"X": 0.6574920415878296, "Y": 0.3856242299079895}]}, "Id": "c4b10925-960c-43ad-b825-2bfd837a635d", "Relationships": [{"Type": "CHILD", "Ids": ["ddb4044c-aa43-450f-94de-f8e4357ad4ca", "5b28e5c1-02ae-4b67-b361-bd7e79cc6e3b"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Ship Quantity", "Geometry": {"BoundingBox": {"Width": 0.09705446660518646, "Height": 0.011477812193334103, "Left": 0.8052487373352051, "Top": 0.3749060034751892}, "Polygon": [{"X": 0.8052636981010437, "Y": 0.3749060034751892}, {"X": 0.902303159236908, "Y": 0.3749632239341736}, {"X": 0.9022907018661499, "Y": 0.3863838315010071}, {"X": 0.8052487373352051, "Y": 0.3863266110420227}]}, "Id": "d69d5d89-7858-42e9-b75f-8f8e175f6deb", "Relationships": [{"Type": "CHILD", "Ids": ["102c6169-cdf1-4345-9e1a-ddeda6788ce9", "ab6a2b7e-fcf1-4fe1-952c-5046867afc33"]}]}, {"BlockType": "LINE", "Confidence": 96.53559112548828, "Text": "544-15008-80,RV R05", "Geometry": {"BoundingBox": {"Width": 0.15945522487163544, "Height": 0.012986520305275917, "Left": 0.10191446542739868, "Top": 0.3925946056842804}, "Polygon": [{"X": 0.10195209830999374, "Y": 0.3925946056842804}, {"X": 0.2613696753978729, "Y": 0.3926886022090912}, {"X": 0.2613367438316345, "Y": 0.40558114647865295}, {"X": 0.10191446542739868, "Y": 0.40548714995384216}]}, "Id": "18aa3270-11be-402c-9af3-77e00d2cd485", "Relationships": [{"Type": "CHILD", "Ids": ["4540615f-5b2b-499a-96d4-5a2211108381", "ec9ec213-327b-4402-8295-d5d0e1c8f076"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "41941", "Geometry": {"BoundingBox": {"Width": 0.044650498777627945, "Height": 0.009661072865128517, "Left": 0.5616632103919983, "Top": 0.39821141958236694}, "Polygon": [{"X": 0.5616812109947205, "Y": 0.39821141958236694}, {"X": 0.6063137054443359, "Y": 0.3982377350330353}, {"X": 0.6062966585159302, "Y": 0.4078724980354309}, {"X": 0.5616632103919983, "Y": 0.4078461825847626}]}, "Id": "e02580e1-6446-4ef1-b56a-d048481c267d", "Relationships": [{"Type": "CHILD", "Ids": ["c57c87e5-9458-40fa-b7c6-731ec158e771"]}]}, {"BlockType": "LINE", "Confidence": 99.25801086425781, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.008198490366339684, "Height": 0.009699119254946709, "Left": 0.769997775554657, "Top": 0.39847108721733093}, "Polygon": [{"X": 0.770011305809021, "Y": 0.39847108721733093}, {"X": 0.7781962752342224, "Y": 0.39847591519355774}, {"X": 0.7781829237937927, "Y": 0.408170223236084}, {"X": 0.769997775554657, "Y": 0.4081653952598572}]}, "Id": "e5a8ce57-a696-48fb-ba02-1c3eb8e470f9", "Relationships": [{"Type": "CHILD", "Ids": ["64b68979-17f7-4e35-a2a6-70c81bfcc3b2"]}]}, {"BlockType": "LINE", "Confidence": 98.92279052734375, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008932854048907757, "Height": 0.00949004665017128, "Left": 0.9128589630126953, "Top": 0.3990378975868225}, "Polygon": [{"X": 0.9128690958023071, "Y": 0.3990378975868225}, {"X": 0.9217917919158936, "Y": 0.39904317259788513}, {"X": 0.9217818975448608, "Y": 0.4085279405117035}, {"X": 0.9128589630126953, "Y": 0.40852269530296326}]}, "Id": "63727379-77f0-4ea8-a26b-fad1e9783caa", "Relationships": [{"Type": "CHILD", "Ids": ["693e955b-4d39-4b71-988a-a0d09c0baa95"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipping", "Geometry": {"BoundingBox": {"Width": 0.062230851501226425, "Height": 0.012969802133738995, "Left": 0.5624654293060303, "Top": 0.6395642757415771}, "Polygon": [{"X": 0.5624895691871643, "Y": 0.6395642757415771}, {"X": 0.6246963143348694, "Y": 0.6396008729934692}, {"X": 0.6246740221977234, "Y": 0.6525340676307678}, {"X": 0.5624654293060303, "Y": 0.6524974703788757}]}, "Id": "ce174f2e-9207-450c-8e85-48858bff6467", "Relationships": [{"Type": "CHILD", "Ids": ["c28ef1f8-b7f5-4a8d-82ef-259531ccbc3b"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.045537348836660385, "Height": 0.010100631043314934, "Left": 0.6931411027908325, "Top": 0.6402238011360168}, "Polygon": [{"X": 0.693156898021698, "Y": 0.6402238011360168}, {"X": 0.7386784553527832, "Y": 0.640250563621521}, {"X": 0.7386637330055237, "Y": 0.6503244042396545}, {"X": 0.6931411027908325, "Y": 0.6502976417541504}]}, "Id": "408ff044-c475-44f9-a74c-7baa92307218", "Relationships": [{"Type": "CHILD", "Ids": ["0e618486-2a8e-4e14-b7fe-55f6059c13b0"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Inspector", "Geometry": {"BoundingBox": {"Width": 0.07192175835371017, "Height": 0.012350550852715969, "Left": 0.8308875560760498, "Top": 0.6396564841270447}, "Polygon": [{"X": 0.8309029936790466, "Y": 0.6396564841270447}, {"X": 0.9028093218803406, "Y": 0.6396988034248352}, {"X": 0.9027959108352661, "Y": 0.6520070433616638}, {"X": 0.8308875560760498, "Y": 0.6519647240638733}]}, "Id": "56d034be-30d3-45fd-9022-987066534cc2", "Relationships": [{"Type": "CHILD", "Ids": ["684bf7bc-85a7-4af8-9403-f2a7a734c0cc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipment visually", "Geometry": {"BoundingBox": {"Width": 0.1329101324081421, "Height": 0.01392030157148838, "Left": 0.09316544234752655, "Top": 0.6584966778755188}, "Polygon": [{"X": 0.09320610761642456, "Y": 0.6584966778755188}, {"X": 0.22607557475566864, "Y": 0.6585748791694641}, {"X": 0.2260391116142273, "Y": 0.6724169850349426}, {"X": 0.09316544234752655, "Y": 0.6723387241363525}]}, "Id": "3adb76ff-f28a-4fd8-82cb-0e6004efb2ac", "Relationships": [{"Type": "CHILD", "Ids": ["3c610b04-0cd1-4599-aa31-16093f4ce1c8", "caa29f2a-63aa-435e-8f59-599c5e06e932"]}]}, {"BlockType": "LINE", "Confidence": 99.97694396972656, "Text": "checked for damage", "Geometry": {"BoundingBox": {"Width": 0.15215478837490082, "Height": 0.013631144538521767, "Left": 0.09157972782850266, "Top": 0.6746164560317993}, "Polygon": [{"X": 0.09161955118179321, "Y": 0.6746164560317993}, {"X": 0.24373450875282288, "Y": 0.6747060418128967}, {"X": 0.24369938671588898, "Y": 0.6882476210594177}, {"X": 0.09157972782850266, "Y": 0.6881580948829651}]}, "Id": "8294b66f-27af-48fe-ba2d-7ae23ebaed40", "Relationships": [{"Type": "CHILD", "Ids": ["97374853-fa3a-4e01-90bf-78c7e2d58e08", "df54b9f7-c18f-4258-b664-bb88e4543b90", "cf0eb646-e5ad-41f5-aa44-1b7a5afef9d1"]}]}, {"BlockType": "LINE", "Confidence": 80.82644653320312, "Text": "Received by: <PERSON><PERSON>er", "Geometry": {"BoundingBox": {"Width": 0.5162212252616882, "Height": 0.07867902517318726, "Left": 0.41354772448539734, "Top": 0.7561668753623962}, "Polygon": [{"X": 0.41372060775756836, "Y": 0.7561668753623962}, {"X": 0.929768979549408, "Y": 0.7564705610275269}, {"X": 0.9296883940696716, "Y": 0.8348459005355835}, {"X": 0.41354772448539734, "Y": 0.8345422744750977}]}, "Id": "8b2fb88d-8a32-4991-8443-4507ec556d77", "Relationships": [{"Type": "CHILD", "Ids": ["95d94b5a-45f5-4e1f-8316-199983eb4c57", "f52faa98-eb81-4055-8bbe-3242273388ee", "058b1ae1-c269-47c4-afd6-6fcd74900fa8", "34852e6e-3a44-4b99-9263-2d4fc1c8808b"]}]}, {"BlockType": "LINE", "Confidence": 41.93239974975586, "Text": "<PERSON><PERSON>.", "Geometry": {"BoundingBox": {"Width": 0.06566952913999557, "Height": 0.03334707021713257, "Left": 0.45996540784835815, "Top": 0.8548255562782288}, "Polygon": [{"X": 0.4600353538990021, "Y": 0.8548255562782288}, {"X": 0.5256349444389343, "Y": 0.8548641204833984}, {"X": 0.5255699753761292, "Y": 0.8881726264953613}, {"X": 0.45996540784835815, "Y": 0.8881340026855469}]}, "Id": "a0ff14a5-26f2-4215-a415-03cab47c81d8", "Relationships": [{"Type": "CHILD", "Ids": ["5d58292c-be49-46c6-b0c5-11247f7ca744"]}]}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "FAB-MASTERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25461193919181824, "Height": 0.027158761397004128, "Left": 0.11321188509464264, "Top": 0.06286675482988358}, "Polygon": [{"X": 0.11329008638858795, "Y": 0.06286675482988358}, {"X": 0.3678238093852997, "Y": 0.06301708519458771}, {"X": 0.3677613139152527, "Y": 0.09002552181482315}, {"X": 0.11321188509464264, "Y": 0.08987520635128021}]}, "Id": "619b8d1d-98e1-4935-a89c-2b8036ef4e8d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07342520356178284, "Height": 0.01989101432263851, "Left": 0.37971195578575134, "Top": 0.06667652726173401}, "Polygon": [{"X": 0.3797573447227478, "Y": 0.06667652726173401}, {"X": 0.4531371593475342, "Y": 0.06671986728906631}, {"X": 0.45309510827064514, "Y": 0.08656754344701767}, {"X": 0.37971195578575134, "Y": 0.08652421087026596}]}, "Id": "bb3808aa-489a-4fd0-b176-762e8ede4061"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16039395332336426, "Height": 0.03314083442091942, "Left": 0.6520308256149292, "Top": 0.04386517405509949}, "Polygon": [{"X": 0.6520857810974121, "Y": 0.04386517405509949}, {"X": 0.8124247789382935, "Y": 0.043959878385066986}, {"X": 0.8123818635940552, "Y": 0.07700600475072861}, {"X": 0.6520308256149292, "Y": 0.0769113153219223}]}, "Id": "5675665f-5cbf-497a-90e4-72015e70eee9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Slip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07538188993930817, "Height": 0.030219251289963722, "Left": 0.8266999125480652, "Top": 0.049740396440029144}, "Polygon": [{"X": 0.8267381191253662, "Y": 0.049740396440029144}, {"X": 0.9020818471908569, "Y": 0.04978489503264427}, {"X": 0.9020488262176514, "Y": 0.07995964586734772}, {"X": 0.8266999125480652, "Y": 0.07991515100002289}]}, "Id": "3532bbf3-9950-4dd3-a4e0-e716c41961e3"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "42011", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03689040616154671, "Height": 0.00999403651803732, "Left": 0.8584620952606201, "Top": 0.08601761609315872}, "Polygon": [{"X": 0.8584739565849304, "Y": 0.08601761609315872}, {"X": 0.8953524827957153, "Y": 0.08603939414024353}, {"X": 0.8953414559364319, "Y": 0.09601165354251862}, {"X": 0.8584620952606201, "Y": 0.09598987549543381}]}, "Id": "1706fe50-48e8-45d5-b670-de0a4b260c2c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06147180125117302, "Height": 0.009599120356142521, "Left": 0.10660583525896072, "Top": 0.14761248230934143}, "Polygon": [{"X": 0.10663367062807083, "Y": 0.14761248230934143}, {"X": 0.16807764768600464, "Y": 0.14764876663684845}, {"X": 0.16805115342140198, "Y": 0.15721160173416138}, {"X": 0.10660583525896072, "Y": 0.15717533230781555}]}, "Id": "b226e301-0619-4724-b883-57c022ad93c9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "901", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02191382274031639, "Height": 0.009212459437549114, "Left": 0.3148946464061737, "Top": 0.1494624763727188}, "Polygon": [{"X": 0.31491705775260925, "Y": 0.1494624763727188}, {"X": 0.3368084728717804, "Y": 0.14947539567947388}, {"X": 0.33678653836250305, "Y": 0.1586749255657196}, {"X": 0.3148946464061737, "Y": 0.15866200625896454}]}, "Id": "53ada454-e707-45bd-9496-a882689b9206"}, {"BlockType": "WORD", "Confidence": 99.51271057128906, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03781925514340401, "Height": 0.009719750843942165, "Left": 0.3419208228588104, "Top": 0.14930549263954163}, "Polygon": [{"X": 0.3419438302516937, "Y": 0.14930549263954163}, {"X": 0.37974005937576294, "Y": 0.14932779967784882}, {"X": 0.3797178864479065, "Y": 0.15902523696422577}, {"X": 0.3419208228588104, "Y": 0.15900292992591858}]}, "Id": "36c0ae82-cb9b-4876-9c6f-26b165047534"}, {"BlockType": "WORD", "Confidence": 99.55416870117188, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014665024355053902, "Height": 0.00956039410084486, "Left": 0.3830318748950958, "Top": 0.14966313540935516}, "Polygon": [{"X": 0.38305366039276123, "Y": 0.14966313540935516}, {"X": 0.39769691228866577, "Y": 0.1496717780828476}, {"X": 0.39767545461654663, "Y": 0.1592235267162323}, {"X": 0.3830318748950958, "Y": 0.15921488404273987}]}, "Id": "4ddc452b-6e0f-4ea2-bdf8-363d613670f4"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030968068167567253, "Height": 0.011332421563565731, "Left": 0.5503060817718506, "Top": 0.1514817178249359}, "Polygon": [{"X": 0.5503275394439697, "Y": 0.1514817178249359}, {"X": 0.581274151802063, "Y": 0.15149997174739838}, {"X": 0.5812535285949707, "Y": 0.16281412541866302}, {"X": 0.5503060817718506, "Y": 0.16279587149620056}]}, "Id": "c2729f79-2643-40e4-8b72-0cc9ced00d3c"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021762769669294357, "Height": 0.00916788075119257, "Left": 0.5852570533752441, "Top": 0.15222156047821045}, "Polygon": [{"X": 0.5852736830711365, "Y": 0.15222156047821045}, {"X": 0.6070197820663452, "Y": 0.15223439037799835}, {"X": 0.6070036292076111, "Y": 0.16138944029808044}, {"X": 0.5852570533752441, "Y": 0.16137659549713135}]}, "Id": "37ca1e71-4a14-4745-9773-97691ed6e415"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "RexCon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051604464650154114, "Height": 0.010137093253433704, "Left": 0.6119340062141418, "Top": 0.15263457596302032}, "Polygon": [{"X": 0.6119517683982849, "Y": 0.15263457596302032}, {"X": 0.6635384559631348, "Y": 0.15266501903533936}, {"X": 0.6635218858718872, "Y": 0.16277165710926056}, {"X": 0.6119340062141418, "Y": 0.16274121403694153}]}, "Id": "bcaabd12-92fc-4df5-9cb4-e5653177a8f9"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02445872128009796, "Height": 0.009189593605697155, "Left": 0.6685346364974976, "Top": 0.15411904454231262}, "Polygon": [{"X": 0.6685495376586914, "Y": 0.15411904454231262}, {"X": 0.6929933428764343, "Y": 0.15413346886634827}, {"X": 0.6929789781570435, "Y": 0.16330863535404205}, {"X": 0.6685346364974976, "Y": 0.1632942110300064}]}, "Id": "b0fde4d5-0b3c-48b0-89e1-6b64db576e90"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Janesville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07120312005281448, "Height": 0.011388055048882961, "Left": 0.31422558426856995, "Top": 0.16664530336856842}, "Polygon": [{"X": 0.3142532408237457, "Y": 0.16664530336856842}, {"X": 0.38542869687080383, "Y": 0.16668730974197388}, {"X": 0.3854029178619385, "Y": 0.17803335189819336}, {"X": 0.31422558426856995, "Y": 0.1779913455247879}]}, "Id": "5c8fcf26-b2e4-4f8c-b0e5-da411e920c73"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017223767936229706, "Height": 0.009423796087503433, "Left": 0.38907280564308167, "Top": 0.16724513471126556}, "Polygon": [{"X": 0.38909414410591125, "Y": 0.16724513471126556}, {"X": 0.40629658102989197, "Y": 0.16725528240203857}, {"X": 0.4062756299972534, "Y": 0.1766689270734787}, {"X": 0.38907280564308167, "Y": 0.1766587793827057}]}, "Id": "51efe406-f96a-440b-b11e-536adbd63da1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "53548", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03841789439320564, "Height": 0.009335821494460106, "Left": 0.4145815372467041, "Top": 0.1678440272808075}, "Polygon": [{"X": 0.4146020710468292, "Y": 0.1678440272808075}, {"X": 0.45299941301345825, "Y": 0.16786669194698334}, {"X": 0.4529796838760376, "Y": 0.17717985808849335}, {"X": 0.4145815372467041, "Y": 0.1771571934223175}]}, "Id": "63e454d1-61bf-4d5e-9cd8-2b2786a6ade2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2841", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431665152311325, "Height": 0.009499778971076012, "Left": 0.6099408268928528, "Top": 0.1702669858932495}, "Polygon": [{"X": 0.6099575161933899, "Y": 0.1702669858932495}, {"X": 0.6442574858665466, "Y": 0.17028723657131195}, {"X": 0.6442415118217468, "Y": 0.17976675927639008}, {"X": 0.6099408268928528, "Y": 0.17974652349948883}]}, "Id": "e2081feb-cab1-4e8d-9ad4-d8873e856ffe"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054159946739673615, "Height": 0.01186362374573946, "Left": 0.649296224117279, "Top": 0.17081333696842194}, "Polygon": [{"X": 0.6493160128593445, "Y": 0.17081333696842194}, {"X": 0.7034561634063721, "Y": 0.17084528505802155}, {"X": 0.703437864780426, "Y": 0.18267695605754852}, {"X": 0.649296224117279, "Y": 0.1826450079679489}]}, "Id": "cd29c88f-7545-4d86-8a6b-c78c0e95ddd3"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021797074005007744, "Height": 0.009300634264945984, "Left": 0.708367109298706, "Top": 0.1720736175775528}, "Polygon": [{"X": 0.7083814144134521, "Y": 0.1720736175775528}, {"X": 0.7301642298698425, "Y": 0.17208647727966309}, {"X": 0.7301504015922546, "Y": 0.18137425184249878}, {"X": 0.708367109298706, "Y": 0.1813613921403885}]}, "Id": "0fb31973-6a09-4a61-9103-279c0265b99e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04665335640311241, "Height": 0.00989560317248106, "Left": 0.3146851360797882, "Top": 0.18416982889175415}, "Polygon": [{"X": 0.3147091567516327, "Y": 0.18416982889175415}, {"X": 0.3613384962081909, "Y": 0.1841973513364792}, {"X": 0.36131551861763, "Y": 0.19406543672084808}, {"X": 0.3146851360797882, "Y": 0.19403791427612305}]}, "Id": "487a19fc-0dae-412e-8566-8d769d73f959"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08615048974752426, "Height": 0.010124246589839458, "Left": 0.3657965064048767, "Top": 0.1847507804632187}, "Polygon": [{"X": 0.3658198416233063, "Y": 0.1847507804632187}, {"X": 0.45194700360298157, "Y": 0.1848016232252121}, {"X": 0.4519256353378296, "Y": 0.19487503170967102}, {"X": 0.3657965064048767, "Y": 0.1948242038488388}]}, "Id": "6607fde2-4ee9-49e9-9237-88d28b2faf8e"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Burlington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07691449671983719, "Height": 0.011904497630894184, "Left": 0.6102584004402161, "Top": 0.1879032999277115}, "Polygon": [{"X": 0.6102792620658875, "Y": 0.1879032999277115}, {"X": 0.6871728897094727, "Y": 0.18794868886470795}, {"X": 0.6871541142463684, "Y": 0.1998077929019928}, {"X": 0.6102584004402161, "Y": 0.19976241886615753}]}, "Id": "d1edbb71-604a-48ef-989f-1b843b7cb14f"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02112223580479622, "Height": 0.008839434944093227, "Left": 0.6916007399559021, "Top": 0.189245343208313}, "Polygon": [{"X": 0.6916146278381348, "Y": 0.189245343208313}, {"X": 0.7127229571342468, "Y": 0.18925781548023224}, {"X": 0.7127094864845276, "Y": 0.1980847865343094}, {"X": 0.6916007399559021, "Y": 0.19807232916355133}]}, "Id": "a4cb8618-2c96-491d-9c6f-75d2f8271056"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "53105", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04315675422549248, "Height": 0.009744483046233654, "Left": 0.7169814109802246, "Top": 0.18964140117168427}, "Polygon": [{"X": 0.7169961333274841, "Y": 0.18964140117168427}, {"X": 0.7601381540298462, "Y": 0.18966685235500336}, {"X": 0.7601243853569031, "Y": 0.1993858814239502}, {"X": 0.7169814109802246, "Y": 0.1993604153394699}]}, "Id": "e8e3dc4f-a6e2-4269-a753-c8f7fec80080"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Fax:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027891559526324272, "Height": 0.00959622859954834, "Left": 0.31438568234443665, "Top": 0.2019696831703186}, "Polygon": [{"X": 0.3144090175628662, "Y": 0.2019696831703186}, {"X": 0.34227725863456726, "Y": 0.2019861340522766}, {"X": 0.34225451946258545, "Y": 0.21156591176986694}, {"X": 0.31438568234443665, "Y": 0.21154946088790894}]}, "Id": "b3053f68-e694-4c17-8d0f-1f765aa069a2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08795750141143799, "Height": 0.010044928640127182, "Left": 0.34658342599868774, "Top": 0.20233258605003357}, "Polygon": [{"X": 0.3466070294380188, "Y": 0.20233258605003357}, {"X": 0.43454092741012573, "Y": 0.20238448679447174}, {"X": 0.43451932072639465, "Y": 0.21237751841545105}, {"X": 0.34658342599868774, "Y": 0.21232561767101288}]}, "Id": "89b785ad-3312-4714-b087-9c900b40f126"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031334079802036285, "Height": 0.011169755831360817, "Left": 0.10295768082141876, "Top": 0.23474736511707306}, "Polygon": [{"X": 0.10299021750688553, "Y": 0.23474736511707306}, {"X": 0.13429175317287445, "Y": 0.23476584255695343}, {"X": 0.13426001369953156, "Y": 0.24591712653636932}, {"X": 0.10295768082141876, "Y": 0.24589864909648895}]}, "Id": "c3ae452c-e1e0-40c0-ad8a-aafd64a4cc79"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03710061311721802, "Height": 0.009330346249043941, "Left": 0.1387639343738556, "Top": 0.2354821264743805}, "Polygon": [{"X": 0.1387903392314911, "Y": 0.2354821264743805}, {"X": 0.1758645474910736, "Y": 0.23550400137901306}, {"X": 0.17583893239498138, "Y": 0.244812473654747}, {"X": 0.1387639343738556, "Y": 0.24479059875011444}]}, "Id": "867bcb01-3faf-4c32-a68b-5e91ed65851c"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "9/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0794830173254013, "Height": 0.011299424804747105, "Left": 0.1807251125574112, "Top": 0.23617713153362274}, "Polygon": [{"X": 0.18075594305992126, "Y": 0.23617713153362274}, {"X": 0.2602081298828125, "Y": 0.2362240105867386}, {"X": 0.260179340839386, "Y": 0.24747654795646667}, {"X": 0.1807251125574112, "Y": 0.24742966890335083}]}, "Id": "df32f4e1-a6d3-4397-94e8-4fd34017fe3a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037951819598674774, "Height": 0.00942244566977024, "Left": 0.10229186713695526, "Top": 0.25242120027542114}, "Polygon": [{"X": 0.10231930762529373, "Y": 0.25242120027542114}, {"X": 0.14024369418621063, "Y": 0.2524435818195343}, {"X": 0.14021706581115723, "Y": 0.26184365153312683}, {"X": 0.10229186713695526, "Y": 0.26182126998901367}]}, "Id": "56bb508e-6021-4e76-a301-7fa94171521a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Person:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052747249603271484, "Height": 0.009582189843058586, "Left": 0.14461135864257812, "Top": 0.25340214371681213}, "Polygon": [{"X": 0.1446383148431778, "Y": 0.25340214371681213}, {"X": 0.1973586082458496, "Y": 0.2534332573413849}, {"X": 0.19733279943466187, "Y": 0.26298433542251587}, {"X": 0.14461135864257812, "Y": 0.2629532516002655}]}, "Id": "cbfdaa48-b97e-4195-a46d-c7d135c5895a"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025287607684731483, "Height": 0.009228254668414593, "Left": 0.20619326829910278, "Top": 0.25457361340522766}, "Polygon": [{"X": 0.20621797442436218, "Y": 0.25457361340522766}, {"X": 0.23148088157176971, "Y": 0.2545885145664215}, {"X": 0.2314566969871521, "Y": 0.2638018727302551}, {"X": 0.20619326829910278, "Y": 0.2637869417667389}]}, "Id": "5c0633eb-a180-4c60-a52d-40e14150ec90"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02159924991428852, "Height": 0.00919167511165142, "Left": 0.5504724979400635, "Top": 0.2580232620239258}, "Polygon": [{"X": 0.5504899024963379, "Y": 0.2580232620239258}, {"X": 0.5720717310905457, "Y": 0.2580359876155853}, {"X": 0.5720548033714294, "Y": 0.26721492409706116}, {"X": 0.5504724979400635, "Y": 0.2672021985054016}]}, "Id": "1b95adbf-9a3b-46c1-bd37-d519030b4e7d"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021527890115976334, "Height": 0.00893675908446312, "Left": 0.5763189196586609, "Top": 0.2585461437702179}, "Polygon": [{"X": 0.5763353109359741, "Y": 0.2585461437702179}, {"X": 0.5978468060493469, "Y": 0.25855883955955505}, {"X": 0.5978308916091919, "Y": 0.2674829065799713}, {"X": 0.5763189196586609, "Y": 0.26747021079063416}]}, "Id": "6a095bc1-497a-4d80-b626-491e4ca5d8f8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Astec", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03922554850578308, "Height": 0.009219682775437832, "Left": 0.6111268997192383, "Top": 0.2589392066001892}, "Polygon": [{"X": 0.6111431121826172, "Y": 0.2589392066001892}, {"X": 0.6503524780273438, "Y": 0.25896233320236206}, {"X": 0.6503371596336365, "Y": 0.268158882856369}, {"X": 0.6111268997192383, "Y": 0.26813575625419617}]}, "Id": "8f5e7bb2-166b-4dcd-803c-4f2c9ea95780"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06499185413122177, "Height": 0.00997052900493145, "Left": 0.10206082463264465, "Top": 0.27028441429138184}, "Polygon": [{"X": 0.10208982974290848, "Y": 0.27028441429138184}, {"X": 0.16705268621444702, "Y": 0.2703227400779724}, {"X": 0.16702516376972198, "Y": 0.28025493025779724}, {"X": 0.10206082463264465, "Y": 0.28021660447120667}]}, "Id": "69d59a08-0ff0-411d-abc0-993bb99c8b5e"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Order:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04470785707235336, "Height": 0.009890219196677208, "Left": 0.1707945019006729, "Top": 0.27139654755592346}, "Polygon": [{"X": 0.17082175612449646, "Y": 0.27139654755592346}, {"X": 0.21550235152244568, "Y": 0.2714228928089142}, {"X": 0.2154761105775833, "Y": 0.2812867760658264}, {"X": 0.1707945019006729, "Y": 0.2812604010105133}]}, "Id": "7d9072a0-f87e-47d8-b35c-7755abb6bcdd"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "PUR3165-105166", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12467621266841888, "Height": 0.009997377172112465, "Left": 0.22048310935497284, "Top": 0.2726132273674011}, "Polygon": [{"X": 0.2205093950033188, "Y": 0.2726132273674011}, {"X": 0.3451593220233917, "Y": 0.27268674969673157}, {"X": 0.3451358675956726, "Y": 0.28261059522628784}, {"X": 0.22048310935497284, "Y": 0.2825370728969574}]}, "Id": "e04dc943-cce5-4c60-99c7-74d6c65f6651"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06332710385322571, "Height": 0.012779337354004383, "Left": 0.10153787583112717, "Top": 0.2868586480617523}, "Polygon": [{"X": 0.1015750914812088, "Y": 0.2868586480617523}, {"X": 0.16486498713493347, "Y": 0.2868959903717041}, {"X": 0.16482961177825928, "Y": 0.29963797330856323}, {"X": 0.10153787583112717, "Y": 0.29960063099861145}]}, "Id": "d8708772-83c4-4fa3-8211-2bb88a65ffff"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "Via:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030755573883652687, "Height": 0.01056718360632658, "Left": 0.16930922865867615, "Top": 0.2882201671600342}, "Polygon": [{"X": 0.1693384051322937, "Y": 0.2882201671600342}, {"X": 0.20006480813026428, "Y": 0.2882382869720459}, {"X": 0.20003636181354523, "Y": 0.29878735542297363}, {"X": 0.16930922865867615, "Y": 0.2987692356109619}]}, "Id": "4dee4c33-bbc8-4178-8721-a86f05635bfd"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "CPU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03173026442527771, "Height": 0.01002749428153038, "Left": 0.20510059595108032, "Top": 0.28914347290992737}, "Polygon": [{"X": 0.20512744784355164, "Y": 0.28914347290992737}, {"X": 0.23683084547519684, "Y": 0.28916215896606445}, {"X": 0.23680470883846283, "Y": 0.29917094111442566}, {"X": 0.20510059595108032, "Y": 0.2991522550582886}]}, "Id": "c751ae1b-979a-4612-9742-07c0e6684303"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Part", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029847480356693268, "Height": 0.009277384728193283, "Left": 0.3630736470222473, "Top": 0.372030645608902}, "Polygon": [{"X": 0.36309516429901123, "Y": 0.372030645608902}, {"X": 0.3929211497306824, "Y": 0.3720482289791107}, {"X": 0.3929002583026886, "Y": 0.38130804896354675}, {"X": 0.3630736470222473, "Y": 0.3812904357910156}]}, "Id": "9dceaf07-c6ee-463c-bfb1-5367e729d2cf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056627023965120316, "Height": 0.009593135677278042, "Left": 0.3972598910331726, "Top": 0.37224194407463074}, "Polygon": [{"X": 0.39728134870529175, "Y": 0.37224194407463074}, {"X": 0.4538869261741638, "Y": 0.37227532267570496}, {"X": 0.4538666903972626, "Y": 0.38183507323265076}, {"X": 0.3972598910331726, "Y": 0.38180169463157654}]}, "Id": "bbce17d2-ec97-4185-adca-4242bd413669"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026230327785015106, "Height": 0.009221863932907581, "Left": 0.5602498054504395, "Top": 0.3742678463459015}, "Polygon": [{"X": 0.5602670311927795, "Y": 0.3742678463459015}, {"X": 0.5864801406860352, "Y": 0.3742832839488983}, {"X": 0.586463451385498, "Y": 0.3834896981716156}, {"X": 0.5602498054504395, "Y": 0.3834742605686188}]}, "Id": "7a6cf97f-ebb3-420f-8f50-c1ac50d73948"}, {"BlockType": "WORD", "Confidence": 99.55655670166016, "Text": "Job#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03522883728146553, "Height": 0.009288681671023369, "Left": 0.5892464518547058, "Top": 0.3743017017841339}, "Polygon": [{"X": 0.5892632007598877, "Y": 0.3743017017841339}, {"X": 0.6244753003120422, "Y": 0.37432244420051575}, {"X": 0.6244592666625977, "Y": 0.38359037041664124}, {"X": 0.5892464518547058, "Y": 0.3835696280002594}]}, "Id": "850e3089-21db-4974-88c1-4dd7e96f048b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041065096855163574, "Height": 0.009469482116401196, "Left": 0.6574952602386475, "Top": 0.3742363154888153}, "Polygon": [{"X": 0.6575108766555786, "Y": 0.3742363154888153}, {"X": 0.698560357093811, "Y": 0.3742605447769165}, {"X": 0.6985456347465515, "Y": 0.3837057948112488}, {"X": 0.6574952602386475, "Y": 0.38368159532546997}]}, "Id": "ddb4044c-aa43-450f-94de-f8e4357ad4ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06214268133044243, "Height": 0.010940417647361755, "Left": 0.7016261219978333, "Top": 0.3747464716434479}, "Polygon": [{"X": 0.7016430497169495, "Y": 0.3747464716434479}, {"X": 0.7637687921524048, "Y": 0.37478309869766235}, {"X": 0.7637534737586975, "Y": 0.3856869041919708}, {"X": 0.7016261219978333, "Y": 0.38565024733543396}]}, "Id": "5b28e5c1-02ae-4b67-b361-bd7e79cc6e3b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031075704842805862, "Height": 0.011119081638753414, "Left": 0.8052491545677185, "Top": 0.3749060034751892}, "Polygon": [{"X": 0.8052636981010437, "Y": 0.3749060034751892}, {"X": 0.8363248109817505, "Y": 0.37492433190345764}, {"X": 0.8363110423088074, "Y": 0.3860251009464264}, {"X": 0.8052491545677185, "Y": 0.38600677251815796}]}, "Id": "102c6169-cdf1-4345-9e1a-ddeda6788ce9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06207212060689926, "Height": 0.011285703629255295, "Left": 0.8402308821678162, "Top": 0.3750981092453003}, "Polygon": [{"X": 0.8402447700500488, "Y": 0.3750981092453003}, {"X": 0.9023029804229736, "Y": 0.3751347064971924}, {"X": 0.9022907018661499, "Y": 0.3863838315010071}, {"X": 0.8402308821678162, "Y": 0.386347234249115}]}, "Id": "ab6a2b7e-fcf1-4fe1-952c-5046867afc33"}, {"BlockType": "WORD", "Confidence": 98.50187683105469, "Text": "544-15008-80,RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1274154633283615, "Height": 0.012967631220817566, "Left": 0.10191446542739868, "Top": 0.3925946056842804}, "Polygon": [{"X": 0.10195209830999374, "Y": 0.3925946056842804}, {"X": 0.2293299287557602, "Y": 0.3926697075366974}, {"X": 0.22929604351520538, "Y": 0.40556225180625916}, {"X": 0.10191446542739868, "Y": 0.40548714995384216}]}, "Id": "4540615f-5b2b-499a-96d4-5a2211108381"}, {"BlockType": "WORD", "Confidence": 94.5693130493164, "Text": "R05", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027508599683642387, "Height": 0.0100110974162817, "Left": 0.23385684192180634, "Top": 0.3943328261375427}, "Polygon": [{"X": 0.23388300836086273, "Y": 0.3943328261375427}, {"X": 0.26136544346809387, "Y": 0.39434900879859924}, {"X": 0.2613399028778076, "Y": 0.4043439030647278}, {"X": 0.23385684192180634, "Y": 0.40432772040367126}]}, "Id": "ec9ec213-327b-4402-8295-d5d0e1c8f076"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "41941", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044650498777627945, "Height": 0.009661072865128517, "Left": 0.5616632103919983, "Top": 0.39821141958236694}, "Polygon": [{"X": 0.5616812109947205, "Y": 0.39821141958236694}, {"X": 0.6063137054443359, "Y": 0.3982377350330353}, {"X": 0.6062966585159302, "Y": 0.4078724980354309}, {"X": 0.5616632103919983, "Y": 0.4078461825847626}]}, "Id": "c57c87e5-9458-40fa-b7c6-731ec158e771"}, {"BlockType": "WORD", "Confidence": 99.25801086425781, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008198490366339684, "Height": 0.009699119254946709, "Left": 0.769997775554657, "Top": 0.39847108721733093}, "Polygon": [{"X": 0.770011305809021, "Y": 0.39847108721733093}, {"X": 0.7781962752342224, "Y": 0.39847591519355774}, {"X": 0.7781829237937927, "Y": 0.408170223236084}, {"X": 0.769997775554657, "Y": 0.4081653952598572}]}, "Id": "64b68979-17f7-4e35-a2a6-70c81bfcc3b2"}, {"BlockType": "WORD", "Confidence": 98.92279052734375, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008932854048907757, "Height": 0.00949004665017128, "Left": 0.9128589630126953, "Top": 0.3990378975868225}, "Polygon": [{"X": 0.9128690958023071, "Y": 0.3990378975868225}, {"X": 0.9217917919158936, "Y": 0.39904317259788513}, {"X": 0.9217818975448608, "Y": 0.4085279405117035}, {"X": 0.9128589630126953, "Y": 0.40852269530296326}]}, "Id": "693e955b-4d39-4b71-988a-a0d09c0baa95"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062230851501226425, "Height": 0.012969802133738995, "Left": 0.5624654293060303, "Top": 0.6395642757415771}, "Polygon": [{"X": 0.5624895691871643, "Y": 0.6395642757415771}, {"X": 0.6246963143348694, "Y": 0.6396008729934692}, {"X": 0.6246740221977234, "Y": 0.6525340676307678}, {"X": 0.5624654293060303, "Y": 0.6524974703788757}]}, "Id": "c28ef1f8-b7f5-4a8d-82ef-259531ccbc3b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045537348836660385, "Height": 0.010100631043314934, "Left": 0.6931411027908325, "Top": 0.6402238011360168}, "Polygon": [{"X": 0.693156898021698, "Y": 0.6402238011360168}, {"X": 0.7386784553527832, "Y": 0.640250563621521}, {"X": 0.7386637330055237, "Y": 0.6503244042396545}, {"X": 0.6931411027908325, "Y": 0.6502976417541504}]}, "Id": "0e618486-2a8e-4e14-b7fe-55f6059c13b0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07192175835371017, "Height": 0.012350550852715969, "Left": 0.8308875560760498, "Top": 0.6396564841270447}, "Polygon": [{"X": 0.8309029936790466, "Y": 0.6396564841270447}, {"X": 0.9028093218803406, "Y": 0.6396988034248352}, {"X": 0.9027959108352661, "Y": 0.6520070433616638}, {"X": 0.8308875560760498, "Y": 0.6519647240638733}]}, "Id": "684bf7bc-85a7-4af8-9403-f2a7a734c0cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0726637989282608, "Height": 0.013042994774878025, "Left": 0.09316791594028473, "Top": 0.6584966778755188}, "Polygon": [{"X": 0.09320610761642456, "Y": 0.6584966778755188}, {"X": 0.16583171486854553, "Y": 0.6585394144058228}, {"X": 0.1657956838607788, "Y": 0.6715396642684937}, {"X": 0.09316791594028473, "Y": 0.6714968681335449}]}, "Id": "3c610b04-0cd1-4599-aa31-16093f4ce1c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "visually", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055904921144247055, "Height": 0.012892266735434532, "Left": 0.17016807198524475, "Top": 0.65952467918396}, "Polygon": [{"X": 0.1702035814523697, "Y": 0.65952467918396}, {"X": 0.2260729819536209, "Y": 0.659557580947876}, {"X": 0.2260391116142273, "Y": 0.6724169850349426}, {"X": 0.17016807198524475, "Y": 0.6723840832710266}]}, "Id": "caa29f2a-63aa-435e-8f59-599c5e06e932"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0614500418305397, "Height": 0.010700305923819542, "Left": 0.09158818423748016, "Top": 0.6746164560317993}, "Polygon": [{"X": 0.09161955118179321, "Y": 0.6746164560317993}, {"X": 0.15303823351860046, "Y": 0.674652636051178}, {"X": 0.1530083566904068, "Y": 0.685316801071167}, {"X": 0.09158818423748016, "Y": 0.6852806210517883}]}, "Id": "97374853-fa3a-4e01-90bf-78c7e2d58e08"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021979479119181633, "Height": 0.0105592617765069, "Left": 0.1578899323940277, "Top": 0.6751007437705994}, "Polygon": [{"X": 0.15791936218738556, "Y": 0.6751007437705994}, {"X": 0.1798694133758545, "Y": 0.6751136183738708}, {"X": 0.17984052002429962, "Y": 0.6856600046157837}, {"X": 0.1578899323940277, "Y": 0.6856470704078674}]}, "Id": "df54b9f7-c18f-4258-b664-bb88e4543b90"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05976700037717819, "Height": 0.012816310860216618, "Left": 0.1839655488729477, "Top": 0.6754313111305237}, "Polygon": [{"X": 0.1840004324913025, "Y": 0.6754313111305237}, {"X": 0.2437325417995453, "Y": 0.6754664778709412}, {"X": 0.24369938671588898, "Y": 0.6882476210594177}, {"X": 0.1839655488729477, "Y": 0.6882124543190002}]}, "Id": "cf0eb646-e5ad-41f5-aa44-1b7a5afef9d1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11427921801805496, "Height": 0.01879861205816269, "Left": 0.41360947489738464, "Top": 0.7878174185752869}, "Polygon": [{"X": 0.413650780916214, "Y": 0.7878174185752869}, {"X": 0.5278887152671814, "Y": 0.7878846526145935}, {"X": 0.5278522372245789, "Y": 0.8066160678863525}, {"X": 0.41360947489738464, "Y": 0.8065488338470459}]}, "Id": "95d94b5a-45f5-4e1f-8316-199983eb4c57"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "by:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03770871087908745, "Height": 0.022683974355459213, "Left": 0.5403268933296204, "Top": 0.7884222269058228}, "Polygon": [{"X": 0.5403702855110168, "Y": 0.7884222269058228}, {"X": 0.5780355930328369, "Y": 0.7884443402290344}, {"X": 0.5779941082000732, "Y": 0.8111062049865723}, {"X": 0.5403268933296204, "Y": 0.8110840320587158}]}, "Id": "f52faa98-eb81-4055-8bbe-3242273388ee"}, {"BlockType": "WORD", "Confidence": 45.79261016845703, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16860760748386383, "Height": 0.0589708536863327, "Left": 0.5980849862098694, "Top": 0.7562754154205322}, "Polygon": [{"X": 0.5981900691986084, "Y": 0.7562754154205322}, {"X": 0.766692578792572, "Y": 0.7563745975494385}, {"X": 0.7666101455688477, "Y": 0.8152462840080261}, {"X": 0.5980849862098694, "Y": 0.8151471018791199}]}, "Id": "058b1ae1-c269-47c4-afd6-6fcd74900fa8"}, {"BlockType": "WORD", "Confidence": 77.80193328857422, "Text": "parker", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1535376012325287, "Height": 0.05337739363312721, "Left": 0.7762055397033691, "Top": 0.7814685106277466}, "Polygon": [{"X": 0.7762790322303772, "Y": 0.7814685106277466}, {"X": 0.9297431707382202, "Y": 0.7815588116645813}, {"X": 0.9296883940696716, "Y": 0.8348459005355835}, {"X": 0.7762055397033691, "Y": 0.8347555994987488}]}, "Id": "34852e6e-3a44-4b99-9263-2d4fc1c8808b"}, {"BlockType": "WORD", "Confidence": 41.93239974975586, "Text": "<PERSON><PERSON>.", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06566952913999557, "Height": 0.03334707021713257, "Left": 0.45996540784835815, "Top": 0.8548255562782288}, "Polygon": [{"X": 0.4600353538990021, "Y": 0.8548255562782288}, {"X": 0.5256349444389343, "Y": 0.8548641204833984}, {"X": 0.5255699753761292, "Y": 0.8881726264953613}, {"X": 0.45996540784835815, "Y": 0.8881340026855469}]}, "Id": "5d58292c-be49-46c6-b0c5-11247f7ca744"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c570487b-187f-46d5-9632-50dc6ad2c07f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c570487b-187f-46d5-9632-50dc6ad2c07f", "content-type": "application/x-amz-json-1.1", "content-length": "51650", "date": "Wed, 24 Sep 2025 12:17:44 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "4f4fa26c-55fb-40b9-aa55-b47faa8d3891", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:17:46 GMT", "content-type": "application/json", "content-length": "653", "connection": "keep-alive", "x-amzn-requestid": "4f4fa26c-55fb-40b9-aa55-b47faa8d3891"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It appears to be a packing slip (Packing Slip). The keywords: \"Packing Slip\" is a header. The doc type list includes pack_list. So classify as pack_list. Must output via tool. Use array of documents: Each page has an object with page_no and doc_type. So return: [{page_no:1, doc_type:\"pack_list\"}]. Use tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"pack_list\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2662, "outputTokens": 101, "totalTokens": 2763}, "metrics": {"latencyMs": 600}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7be80c6d_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nFAB-MASTERS, INC.\nPacking Slip\n42011\nAddress:\n901 Joliet St\nShip To: RexCon LLC\nJanesville, WI 53548\n2841 Whiting Rd.\nPhone: ************\nBurlington, WI 53105\nFax: ************\nShip Date: 9/19/2025\nSales Person: Zac\nBill To: Astec\nPurchase Order: PUR3165-105166\nShipped Via: CPU\nPart Number\nOur Job#\nOrder Quantity\nShip Quantity\n544-15008-80,RV R05\n41941\n3\n2\nShipping\nDriver\nInspector\nShipment visually\nchecked for damage\nReceived by: <PERSON><PERSON> parker\n<PERSON>.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}