{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 8.443100441013485e-09, "Y": 0.0}, {"X": 1.0, "Y": 1.4430595385306333e-08}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "03a65ff8-228a-4825-ab38-117f432966e6", "Relationships": [{"Type": "CHILD", "Ids": ["72b2f14d-3cfa-470e-8f3c-900a86c4c556", "76728b71-8856-498f-bdf3-efa8a01d4d27", "e4136702-8bc8-4e6b-aaa3-9fd35972e6bf", "216687bc-83f9-46d6-8d00-fe1e187881b2", "aed0d079-44b6-4bc1-a360-20cb1e6d6961", "0f8f8be2-6796-4963-906d-a64ab2bdc023", "68a53d75-1430-4c8e-a8e8-532222b3994f", "cfa58247-c606-4e6b-81e1-b79262a99371", "59c3304a-7cf0-4a82-b316-e0d7904590aa", "40e782a3-ebc7-447b-ba7e-73b4c10db64a", "52f374fd-5c01-40fb-9959-640cec72e595", "bf2acc03-e714-4c50-b7bb-49d89c74262c", "97fc9478-90d7-431c-8520-ba86b3c234ca", "af74c846-f892-46f7-9660-ca917d0a67d1", "7c8907b1-06d7-4365-ae4b-6918c200ecca", "766419b1-2a9f-424a-97fa-7787fe440504", "c63c959b-97f0-45a1-82e0-f3c959d6236f", "a1f2c27c-34f9-4a79-ae56-855b0ec4e875", "89d4fda7-3680-4614-a8f3-dc7a9bd3933b", "e80f029f-6efd-4fd3-b396-ab8d9695b5bb", "4ece0ff9-43fa-4377-b275-b441b622f207", "917f7f2b-0d23-44c1-a331-c816b42f1f8f", "60598ef8-5541-43d2-a173-750d512191d2", "f61b54c9-7b77-4ab2-a1d9-8e949f826efa", "6e929d17-4bb4-4e5b-a20a-4f649adad6ca", "c1c9fe07-7eff-4dd3-a7b8-cb07cb89bcb8", "518fd9cd-181e-4240-a450-119579e99401"]}]}, {"BlockType": "LINE", "Confidence": 54.910118103027344, "Text": "[csx", "Geometry": {"BoundingBox": {"Width": 0.09535503387451172, "Height": 0.03282533958554268, "Left": 0.031824901700019836, "Top": 0.023787105455994606}, "Polygon": [{"X": 0.03182515501976013, "Y": 0.023787105455994606}, {"X": 0.12717993557453156, "Y": 0.02378726936876774}, {"X": 0.12717963755130768, "Y": 0.056612446904182434}, {"X": 0.031824901700019836, "Y": 0.056612279266119}]}, "Id": "72b2f14d-3cfa-470e-8f3c-900a86c4c556", "Relationships": [{"Type": "CHILD", "Ids": ["94d0e229-8239-4b62-989b-d2f18784ad9b"]}]}, {"BlockType": "LINE", "Confidence": 92.38540649414062, "Text": "]", "Geometry": {"BoundingBox": {"Width": 0.012019729241728783, "Height": 0.03246896713972092, "Left": 0.14009550213813782, "Top": 0.023222247138619423}, "Polygon": [{"X": 0.1400958001613617, "Y": 0.023222247138619423}, {"X": 0.15211524069309235, "Y": 0.023222267627716064}, {"X": 0.15211492776870728, "Y": 0.05569121614098549}, {"X": 0.14009550213813782, "Y": 0.0556911937892437}]}, "Id": "76728b71-8856-498f-bdf3-efa8a01d4d27", "Relationships": [{"Type": "CHILD", "Ids": ["facaab1d-6aa5-40a4-85fa-9e7b2064c041"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "J1 GATE RECEIPT", "Geometry": {"BoundingBox": {"Width": 0.18699020147323608, "Height": 0.01288246177136898, "Left": 0.17484207451343536, "Top": 0.02686494030058384}, "Polygon": [{"X": 0.17484219372272491, "Y": 0.02686494030058384}, {"X": 0.36183226108551025, "Y": 0.026865262538194656}, {"X": 0.3618321120738983, "Y": 0.03974740207195282}, {"X": 0.17484207451343536, "Y": 0.039747077971696854}]}, "Id": "e4136702-8bc8-4e6b-aaa3-9fd35972e6bf", "Relationships": [{"Type": "CHILD", "Ids": ["2996641d-29a9-424d-a89f-ad7c3b7158d0", "47f20a54-3811-4c51-9e09-5154908ce38c", "d8337a9d-77a7-4577-b2d7-fbc19747661a"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Chicago 59th St, IL", "Geometry": {"BoundingBox": {"Width": 0.1694081574678421, "Height": 0.014382818713784218, "Left": 0.7970151901245117, "Top": 0.026063768193125725}, "Polygon": [{"X": 0.7970154881477356, "Y": 0.026063768193125725}, {"X": 0.9664233326911926, "Y": 0.026064058765769005}, {"X": 0.9664230942726135, "Y": 0.04044658690690994}, {"X": 0.7970151901245117, "Y": 0.040446292608976364}]}, "Id": "216687bc-83f9-46d6-8d00-fe1e187881b2", "Relationships": [{"Type": "CHILD", "Ids": ["3e45a868-062a-4545-9c50-788716c58bca", "526d7f25-b432-4838-a8bb-c6f65bd2823c", "54da3221-8243-4d10-a99b-ed3f5694de79", "91c7d8fb-5c60-4548-b70f-ea3177b68f75"]}]}, {"BlockType": "LINE", "Confidence": 99.91395568847656, "Text": "For questions call, 1-877-SHIPCSX", "Geometry": {"BoundingBox": {"Width": 0.2036072462797165, "Height": 0.009712126106023788, "Left": 0.1751377135515213, "Top": 0.048189010471105576}, "Polygon": [{"X": 0.17513780295848846, "Y": 0.048189010471105576}, {"X": 0.3787449598312378, "Y": 0.04818936809897423}, {"X": 0.37874484062194824, "Y": 0.057901136577129364}, {"X": 0.1751377135515213, "Y": 0.05790077894926071}]}, "Id": "aed0d079-44b6-4bc1-a360-20cb1e6d6961", "Relationships": [{"Type": "CHILD", "Ids": ["0cad3f72-a879-4092-ba5b-39c52cb016e5", "af6e2432-1098-40f9-9597-493c49b96b5f", "c909f4e6-da79-4d70-83b4-2a58dcb1c349", "f76d0633-bedb-4ded-9946-b3a1e33b6eb3"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "CAAU 848544", "Geometry": {"BoundingBox": {"Width": 0.12634918093681335, "Height": 0.011072934605181217, "Left": 0.8398926854133606, "Top": 0.053294144570827484}, "Polygon": [{"X": 0.8398929238319397, "Y": 0.053294144570827484}, {"X": 0.9662418961524963, "Y": 0.05329436436295509}, {"X": 0.9662416577339172, "Y": 0.06436707824468613}, {"X": 0.8398926854133606, "Y": 0.06436685472726822}]}, "Id": "0f8f8be2-6796-4963-906d-a64ab2bdc023", "Relationships": [{"Type": "CHILD", "Ids": ["961b31fb-d513-4cab-85c9-f51932a1a944", "b1303d05-fb2e-4455-b8be-392ff46b9aab"]}]}, {"BlockType": "LINE", "Confidence": 99.56871795654297, "Text": "PAGE 1", "Geometry": {"BoundingBox": {"Width": 0.041497498750686646, "Height": 0.007715877145528793, "Left": 0.03386988118290901, "Top": 0.0755307674407959}, "Polygon": [{"X": 0.03386994078755379, "Y": 0.0755307674407959}, {"X": 0.07536737620830536, "Y": 0.07553084194660187}, {"X": 0.07536731660366058, "Y": 0.0832466408610344}, {"X": 0.03386988118290901, "Y": 0.08324656635522842}]}, "Id": "68a53d75-1430-4c8e-a8e8-532222b3994f", "Relationships": [{"Type": "CHILD", "Ids": ["5fa47250-3e1d-4aeb-9a52-713529396fea", "5b78ea01-536e-42bc-b794-e59ad4bd169a"]}]}, {"BlockType": "LINE", "Confidence": 99.47066497802734, "Text": "040L 0000W 0000H", "Geometry": {"BoundingBox": {"Width": 0.11731115728616714, "Height": 0.007844305597245693, "Left": 0.848124086856842, "Top": 0.07143959403038025}, "Polygon": [{"X": 0.8481242656707764, "Y": 0.07143959403038025}, {"X": 0.965435266494751, "Y": 0.07143980264663696}, {"X": 0.9654350876808167, "Y": 0.07928390055894852}, {"X": 0.848124086856842, "Y": 0.0792836844921112}]}, "Id": "cfa58247-c606-4e6b-81e1-b79262a99371", "Relationships": [{"Type": "CHILD", "Ids": ["68fa787a-c599-40ab-9d01-eac3eab07cbb", "d9cd0933-1fb9-4487-8e53-bed37b76fc14", "ab216b48-2abb-4dd9-9142-1617e7ddb4be"]}]}, {"BlockType": "LINE", "Confidence": 99.94384765625, "Text": "J1 RECEIPT DETAILS (1)", "Geometry": {"BoundingBox": {"Width": 0.20092515647411346, "Height": 0.013477513566613197, "Left": 0.39906343817710876, "Top": 0.1204431802034378}, "Polygon": [{"X": 0.3990635871887207, "Y": 0.1204431802034378}, {"X": 0.599988579750061, "Y": 0.12044354528188705}, {"X": 0.5999884009361267, "Y": 0.13392068445682526}, {"X": 0.39906343817710876, "Y": 0.1339203119277954}]}, "Id": "59c3304a-7cf0-4a82-b316-e0d7904590aa", "Relationships": [{"Type": "CHILD", "Ids": ["12a15c6d-d573-4235-9d86-0b0e84673e41", "1440314c-6383-4423-ad8b-28bd711b7672", "e346a9fb-9383-4eff-a90c-cee1715de0f3", "e9bba2e6-51db-4737-8e2e-86d7fb9d6098"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.05096088722348213, "Height": 0.010738005861639977, "Left": 0.040108758956193924, "Top": 0.15501035749912262}, "Polygon": [{"X": 0.04010884463787079, "Y": 0.15501035749912262}, {"X": 0.09106964617967606, "Y": 0.15501044690608978}, {"X": 0.09106955677270889, "Y": 0.16574835777282715}, {"X": 0.040108758956193924, "Y": 0.16574826836585999}]}, "Id": "40e782a3-ebc7-447b-ba7e-73b4c10db64a", "Relationships": [{"Type": "CHILD", "Ids": ["291e1570-76f7-449b-a104-7afd0d4f45ea"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Associated", "Geometry": {"BoundingBox": {"Width": 0.07549204677343369, "Height": 0.008709115907549858, "Left": 0.1880568265914917, "Top": 0.15493714809417725}, "Polygon": [{"X": 0.18805690109729767, "Y": 0.15493714809417725}, {"X": 0.263548880815506, "Y": 0.154937282204628}, {"X": 0.26354876160621643, "Y": 0.16364626586437225}, {"X": 0.1880568265914917, "Y": 0.16364611685276031}]}, "Id": "52f374fd-5c01-40fb-9959-640cec72e595", "Relationships": [{"Type": "CHILD", "Ids": ["1c352dd1-93cd-4c56-85ee-b14376011227"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Load or", "Geometry": {"BoundingBox": {"Width": 0.05105169862508774, "Height": 0.008713520132005215, "Left": 0.48433107137680054, "Top": 0.1548941433429718}, "Polygon": [{"X": 0.4843311905860901, "Y": 0.1548941433429718}, {"X": 0.5353827476501465, "Y": 0.15489423274993896}, {"X": 0.5353826284408569, "Y": 0.163607656955719}, {"X": 0.48433107137680054, "Y": 0.16360756754875183}]}, "Id": "bf2acc03-e714-4c50-b7bb-49d89c74262c", "Relationships": [{"Type": "CHILD", "Ids": ["ca28a256-9ff8-4ee8-b7ce-a07c74d280eb", "2eb225a1-71a0-437d-9d2b-fdb97b6a8065"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Seal(s)", "Geometry": {"BoundingBox": {"Width": 0.04689733311533928, "Height": 0.010966391302645206, "Left": 0.335964173078537, "Top": 0.16060176491737366}, "Polygon": [{"X": 0.33596429228782654, "Y": 0.16060176491737366}, {"X": 0.38286149501800537, "Y": 0.16060185432434082}, {"X": 0.38286134600639343, "Y": 0.1715681552886963}, {"X": 0.335964173078537, "Y": 0.17156806588172913}]}, "Id": "97fc9478-90d7-431c-8520-ba86b3c234ca", "Relationships": [{"Type": "CHILD", "Ids": ["404caf53-f606-4175-bd3a-ed57d612cf1a"]}]}, {"BlockType": "LINE", "Confidence": 99.8388671875, "Text": "Booking #", "Geometry": {"BoundingBox": {"Width": 0.06793901324272156, "Height": 0.01083412580192089, "Left": 0.5986597537994385, "Top": 0.16086126863956451}, "Polygon": [{"X": 0.598659873008728, "Y": 0.16086126863956451}, {"X": 0.6665987372398376, "Y": 0.16086138784885406}, {"X": 0.6665985584259033, "Y": 0.17169539630413055}, {"X": 0.5986597537994385, "Y": 0.1716952621936798}]}, "Id": "af74c846-f892-46f7-9660-ca917d0a67d1", "Relationships": [{"Type": "CHILD", "Ids": ["2f156154-14e4-4fb0-8c29-35f185f32a65", "2613f6ab-c15a-4777-8980-cc03befdaab4"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Transportation Company", "Geometry": {"BoundingBox": {"Width": 0.16861556470394135, "Height": 0.010935764759778976, "Left": 0.7454329133033752, "Top": 0.1550092250108719}, "Polygon": [{"X": 0.7454330921173096, "Y": 0.1550092250108719}, {"X": 0.914048433303833, "Y": 0.15500955283641815}, {"X": 0.9140482544898987, "Y": 0.16594499349594116}, {"X": 0.7454329133033752, "Y": 0.1659446805715561}]}, "Id": "7c8907b1-06d7-4365-ae4b-6918c200ecca", "Relationships": [{"Type": "CHILD", "Ids": ["a367c4ce-1a43-4a1b-9771-9d9689f5f318", "f7d00ebf-4a44-47ad-a714-f9210ec7095f"]}]}, {"BlockType": "LINE", "Confidence": 99.89583587646484, "Text": "Date & Time", "Geometry": {"BoundingBox": {"Width": 0.0824541300535202, "Height": 0.0086395014077425, "Left": 0.0405488945543766, "Top": 0.1675979495048523}, "Polygon": [{"X": 0.040548961609601974, "Y": 0.1675979495048523}, {"X": 0.1230030208826065, "Y": 0.16759809851646423}, {"X": 0.12300294637680054, "Y": 0.17623744904994965}, {"X": 0.0405488945543766, "Y": 0.1762372851371765}]}, "Id": "766419b1-2a9f-424a-97fa-7787fe440504", "Relationships": [{"Type": "CHILD", "Ids": ["80a9cef3-8f65-447c-91b6-1ba4b30de99c", "61878c23-a0a2-4d5b-ac21-c43bc37a51dc", "ccf84e86-125b-4d21-9ae8-884f76364f0e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Equipment", "Geometry": {"BoundingBox": {"Width": 0.072161965072155, "Height": 0.01068528275936842, "Left": 0.18866486847400665, "Top": 0.16757214069366455}, "Polygon": [{"X": 0.188664972782135, "Y": 0.16757214069366455}, {"X": 0.26082682609558105, "Y": 0.1675722748041153}, {"X": 0.2608267068862915, "Y": 0.17825742065906525}, {"X": 0.18866486847400665, "Y": 0.1782572865486145}]}, "Id": "c63c959b-97f0-45a1-82e0-f3c959d6236f", "Relationships": [{"Type": "CHILD", "Ids": ["a2b93ecf-1026-4ad8-a38b-4d40a5e4402d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Empty", "Geometry": {"BoundingBox": {"Width": 0.04207408428192139, "Height": 0.010703271254897118, "Left": 0.4844282865524292, "Top": 0.16764196753501892}, "Polygon": [{"X": 0.48442843556404114, "Y": 0.16764196753501892}, {"X": 0.5265023708343506, "Y": 0.16764205694198608}, {"X": 0.5265021920204163, "Y": 0.17834524810314178}, {"X": 0.4844282865524292, "Y": 0.17834515869617462}]}, "Id": "a1f2c27c-34f9-4a79-ae56-855b0ec4e875", "Relationships": [{"Type": "CHILD", "Ids": ["3302ad56-6bf0-4818-8e34-b44ea3157a81"]}]}, {"BlockType": "LINE", "Confidence": 99.8681640625, "Text": "& Driver", "Geometry": {"BoundingBox": {"Width": 0.05341443419456482, "Height": 0.008540176786482334, "Left": 0.7460697293281555, "Top": 0.1676451712846756}, "Polygon": [{"X": 0.7460699081420898, "Y": 0.1676451712846756}, {"X": 0.7994841933250427, "Y": 0.16764527559280396}, {"X": 0.7994840145111084, "Y": 0.17618535459041595}, {"X": 0.7460697293281555, "Y": 0.1761852502822876}]}, "Id": "89d4fda7-3680-4614-a8f3-dc7a9bd3933b", "Relationships": [{"Type": "CHILD", "Ids": ["bcdc73c8-2593-4d1b-96c5-a1ca29d1388f", "f3a6674c-58e3-4d27-ad0f-de6be7250fd2"]}]}, {"BlockType": "LINE", "Confidence": 99.08482360839844, "Text": "In-Gate", "Geometry": {"BoundingBox": {"Width": 0.042406387627124786, "Height": 0.007871822454035282, "Left": 0.04064808785915375, "Top": 0.2000231146812439}, "Polygon": [{"X": 0.04064815118908882, "Y": 0.2000231146812439}, {"X": 0.08305447548627853, "Y": 0.20002318918704987}, {"X": 0.08305440843105316, "Y": 0.2078949362039566}, {"X": 0.04064808785915375, "Y": 0.20789484679698944}]}, "Id": "e80f029f-6efd-4fd3-b396-ab8d9695b5bb", "Relationships": [{"Type": "CHILD", "Ids": ["e084531d-98af-4906-a336-95bb36b07d4b"]}]}, {"BlockType": "LINE", "Confidence": 93.87456512451172, "Text": "---", "Geometry": {"BoundingBox": {"Width": 0.011696472764015198, "Height": 0.0018027451587840915, "Left": 0.18781355023384094, "Top": 0.20353761315345764}, "Polygon": [{"X": 0.18781356513500214, "Y": 0.20353761315345764}, {"X": 0.19951002299785614, "Y": 0.20353764295578003}, {"X": 0.19950999319553375, "Y": 0.20534037053585052}, {"X": 0.18781355023384094, "Y": 0.20534034073352814}]}, "Id": "4ece0ff9-43fa-4377-b275-b441b622f207", "Relationships": [{"Type": "CHILD", "Ids": ["bd2c2be0-0728-433c-acd3-4bd200b43546"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "992249", "Geometry": {"BoundingBox": {"Width": 0.045010410249233246, "Height": 0.007626181002706289, "Left": 0.33596450090408325, "Top": 0.2002248466014862}, "Polygon": [{"X": 0.3359645903110504, "Y": 0.2002248466014862}, {"X": 0.3809749186038971, "Y": 0.20022492110729218}, {"X": 0.38097482919692993, "Y": 0.20785102248191833}, {"X": 0.33596450090408325, "Y": 0.20785093307495117}]}, "Id": "917f7f2b-0d23-44c1-a331-c816b42f1f8f", "Relationships": [{"Type": "CHILD", "Ids": ["ca971632-be0c-4a3a-800d-d7140b303876"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Load", "Geometry": {"BoundingBox": {"Width": 0.029252607375383377, "Height": 0.00789414718747139, "Left": 0.4841039180755615, "Top": 0.19995591044425964}, "Polygon": [{"X": 0.4841040372848511, "Y": 0.19995591044425964}, {"X": 0.5133565068244934, "Y": 0.19995597004890442}, {"X": 0.5133564472198486, "Y": 0.20785005390644073}, {"X": 0.4841039180755615, "Y": 0.20784999430179596}]}, "Id": "60598ef8-5541-43d2-a173-750d512191d2", "Relationships": [{"Type": "CHILD", "Ids": ["9d0d6e91-648b-47f3-bb05-64921d328806"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "257637187", "Geometry": {"BoundingBox": {"Width": 0.06748058646917343, "Height": 0.007971092127263546, "Left": 0.5981258749961853, "Top": 0.19998814165592194}, "Polygon": [{"X": 0.5981259942054749, "Y": 0.19998814165592194}, {"X": 0.6656064391136169, "Y": 0.19998827576637268}, {"X": 0.6656063199043274, "Y": 0.20795923471450806}, {"X": 0.5981258749961853, "Y": 0.2079591006040573}]}, "Id": "f61b54c9-7b77-4ab2-a1d9-8e949f826efa", "Relationships": [{"Type": "CHILD", "Ids": ["46d37599-791f-4c90-b52e-833c2d00b891"]}]}, {"BlockType": "LINE", "Confidence": 99.95639038085938, "Text": "Super Star Freight Inc (SSRT)", "Geometry": {"BoundingBox": {"Width": 0.17193034291267395, "Height": 0.009916342794895172, "Left": 0.7459123134613037, "Top": 0.19974938035011292}, "Polygon": [{"X": 0.745912492275238, "Y": 0.19974938035011292}, {"X": 0.9178426265716553, "Y": 0.19974970817565918}, {"X": 0.917842447757721, "Y": 0.2096657156944275}, {"X": 0.7459123134613037, "Y": 0.20966538786888123}]}, "Id": "6e929d17-4bb4-4e5b-a20a-4f649adad6ca", "Relationships": [{"Type": "CHILD", "Ids": ["6c8ea994-aa15-451d-bca1-76f0ebd40baf", "060eb301-1d4a-4da2-8fa3-1f6e4c41f989", "42ac4e9b-6239-4910-b891-1188696eb64c", "27d7cead-f4d9-4cb8-afc7-d05ad2bbbc61", "a179f152-013b-4903-ae81-503344889662"]}]}, {"BlockType": "LINE", "Confidence": 98.8818359375, "Text": "09/14/25 22:45", "Geometry": {"BoundingBox": {"Width": 0.10015656799077988, "Height": 0.007902156561613083, "Left": 0.04037685692310333, "Top": 0.21113993227481842}, "Polygon": [{"X": 0.040376920253038406, "Y": 0.21113993227481842}, {"X": 0.1405334323644638, "Y": 0.21114012598991394}, {"X": 0.14053335785865784, "Y": 0.2190420925617218}, {"X": 0.04037685692310333, "Y": 0.21904189884662628}]}, "Id": "c1c9fe07-7eff-4dd3-a7b8-cb07cb89bcb8", "Relationships": [{"Type": "CHILD", "Ids": ["397af9b0-43dc-4ff7-b192-1935239c73ad", "9e7180f6-6766-4938-8a2d-871b87a6959d"]}]}, {"BlockType": "LINE", "Confidence": 99.98028564453125, "Text": "Damage is captured on recorded images at AGS locations", "Geometry": {"BoundingBox": {"Width": 0.3432479798793793, "Height": 0.009978629648685455, "Left": 0.033968277275562286, "Top": 0.2621561884880066}, "Polygon": [{"X": 0.033968355506658554, "Y": 0.2621561884880066}, {"X": 0.37721627950668335, "Y": 0.2621568739414215}, {"X": 0.3772161304950714, "Y": 0.27213481068611145}, {"X": 0.033968277275562286, "Y": 0.27213412523269653}]}, "Id": "518fd9cd-181e-4240-a450-119579e99401", "Relationships": [{"Type": "CHILD", "Ids": ["785bb5ba-7398-46c9-b32a-1438bd97b417", "12425b08-4154-42ad-bc34-bd221e58b8ac", "9a4b76c0-e218-47a6-a338-9822822a22de", "4c9d4374-8f56-4b63-b35a-4585f45bae05", "31645e5f-987f-473a-9e6f-6d4534b37ac6", "87273651-7fc8-4b5e-8814-ff6d58475cb8", "f44de41a-d724-4f2a-92ea-38e09a38e70f", "eab0228d-bf6c-4a4d-b6cd-3b8a03ab8353", "f93ca30f-8dc5-4948-9dfe-568bca88bab2"]}]}, {"BlockType": "WORD", "Confidence": 54.910118103027344, "Text": "[csx", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09535503387451172, "Height": 0.03282533958554268, "Left": 0.031824901700019836, "Top": 0.023787105455994606}, "Polygon": [{"X": 0.03182515501976013, "Y": 0.023787105455994606}, {"X": 0.12717993557453156, "Y": 0.02378726936876774}, {"X": 0.12717963755130768, "Y": 0.056612446904182434}, {"X": 0.031824901700019836, "Y": 0.056612279266119}]}, "Id": "94d0e229-8239-4b62-989b-d2f18784ad9b"}, {"BlockType": "WORD", "Confidence": 92.38540649414062, "Text": "]", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012019729241728783, "Height": 0.03246896713972092, "Left": 0.14009550213813782, "Top": 0.023222247138619423}, "Polygon": [{"X": 0.1400958001613617, "Y": 0.023222247138619423}, {"X": 0.15211524069309235, "Y": 0.023222267627716064}, {"X": 0.15211492776870728, "Y": 0.05569121614098549}, {"X": 0.14009550213813782, "Y": 0.0556911937892437}]}, "Id": "facaab1d-6aa5-40a4-85fa-9e7b2064c041"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022250859066843987, "Height": 0.012600227259099483, "Left": 0.17484207451343536, "Top": 0.027136625722050667}, "Polygon": [{"X": 0.17484219372272491, "Y": 0.027136625722050667}, {"X": 0.1970929205417633, "Y": 0.02713666297495365}, {"X": 0.19709280133247375, "Y": 0.039736852049827576}, {"X": 0.17484207451343536, "Y": 0.03973681479692459}]}, "Id": "2996641d-29a9-424d-a89f-ad7c3b7158d0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "GATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05615678057074547, "Height": 0.012882234528660774, "Left": 0.20747575163841248, "Top": 0.026864996179938316}, "Polygon": [{"X": 0.20747588574886322, "Y": 0.026864996179938316}, {"X": 0.26363253593444824, "Y": 0.026865093037486076}, {"X": 0.2636323869228363, "Y": 0.03974723070859909}, {"X": 0.20747575163841248, "Y": 0.03974713385105133}]}, "Id": "47f20a54-3811-4c51-9e09-5154908ce38c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09105192124843597, "Height": 0.012747475877404213, "Left": 0.2707803547382355, "Top": 0.026962827891111374}, "Polygon": [{"X": 0.2707805037498474, "Y": 0.026962827891111374}, {"X": 0.36183226108551025, "Y": 0.02696298435330391}, {"X": 0.3618321120738983, "Y": 0.039710305631160736}, {"X": 0.2707803547382355, "Y": 0.0397101454436779}]}, "Id": "d8337a9d-77a7-4577-b2d7-fbc19747661a"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.073374904692173, "Height": 0.014382652007043362, "Left": 0.7970151901245117, "Top": 0.026063768193125725}, "Polygon": [{"X": 0.7970154881477356, "Y": 0.026063768193125725}, {"X": 0.8703901171684265, "Y": 0.026063892990350723}, {"X": 0.8703898191452026, "Y": 0.04044641926884651}, {"X": 0.7970151901245117, "Y": 0.040446292608976364}]}, "Id": "3e45a868-062a-4545-9c50-788716c58bca"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "59th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0388624370098114, "Height": 0.011596607975661755, "Left": 0.8764660358428955, "Top": 0.026105280965566635}, "Polygon": [{"X": 0.8764662146568298, "Y": 0.026105280965566635}, {"X": 0.9153284430503845, "Y": 0.026105348020792007}, {"X": 0.9153282046318054, "Y": 0.037701889872550964}, {"X": 0.8764660358428955, "Y": 0.03770182281732559}]}, "Id": "526d7f25-b432-4838-a8bb-c6f65bd2823c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "St,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023172875866293907, "Height": 0.013029825873672962, "Left": 0.9211851954460144, "Top": 0.026520419865846634}, "Polygon": [{"X": 0.9211854934692383, "Y": 0.026520419865846634}, {"X": 0.9443581104278564, "Y": 0.026520458981394768}, {"X": 0.9443578124046326, "Y": 0.03955024480819702}, {"X": 0.9211851954460144, "Y": 0.03955020383000374}]}, "Id": "54da3221-8243-4d10-a99b-ed3f5694de79"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016314242035150528, "Height": 0.011089161038398743, "Left": 0.9501091241836548, "Top": 0.026474475860595703}, "Polygon": [{"X": 0.9501093626022339, "Y": 0.026474475860595703}, {"X": 0.9664233326911926, "Y": 0.02647450380027294}, {"X": 0.9664231538772583, "Y": 0.037563636898994446}, {"X": 0.9501091241836548, "Y": 0.03756360709667206}]}, "Id": "91c7d8fb-5c60-4548-b70f-ea3177b68f75"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019155556336045265, "Height": 0.007620855234563351, "Left": 0.1751377284526825, "Top": 0.04849462956190109}, "Polygon": [{"X": 0.17513780295848846, "Y": 0.04849462956190109}, {"X": 0.1942932903766632, "Y": 0.04849466308951378}, {"X": 0.19429321587085724, "Y": 0.05611548572778702}, {"X": 0.1751377284526825, "Y": 0.05611545220017433}]}, "Id": "0cad3f72-a879-4092-ba5b-39c52cb016e5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "questions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058482930064201355, "Height": 0.009372275322675705, "Left": 0.19747982919216156, "Top": 0.04852864518761635}, "Polygon": [{"X": 0.19747991859912872, "Y": 0.04852864518761635}, {"X": 0.2559627592563629, "Y": 0.048528749495744705}, {"X": 0.25596264004707336, "Y": 0.05790092051029205}, {"X": 0.19747982919216156, "Y": 0.057900819927453995}]}, "Id": "af6e2432-1098-40f9-9597-493c49b96b5f"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "call,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023277178406715393, "Height": 0.008919766172766685, "Left": 0.25967177748680115, "Top": 0.048189159482717514}, "Polygon": [{"X": 0.2596718668937683, "Y": 0.048189159482717514}, {"X": 0.28294894099235535, "Y": 0.0481892004609108}, {"X": 0.2829488515853882, "Y": 0.05710892379283905}, {"X": 0.25967177748680115, "Y": 0.05710888281464577}]}, "Id": "c909f4e6-da79-4d70-83b4-2a58dcb1c349"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "1-877-SHIPCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09132489562034607, "Height": 0.007753887679427862, "Left": 0.2874200642108917, "Top": 0.04831361398100853}, "Polygon": [{"X": 0.2874201536178589, "Y": 0.04831361398100853}, {"X": 0.3787449598312378, "Y": 0.048313774168491364}, {"X": 0.37874487042427063, "Y": 0.05606750398874283}, {"X": 0.2874200642108917, "Y": 0.056067343801259995}]}, "Id": "f76d0633-bedb-4ded-9946-b3a1e33b6eb3"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "CAAU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05218280106782913, "Height": 0.010948793031275272, "Left": 0.8398926854133606, "Top": 0.05341815575957298}, "Polygon": [{"X": 0.8398929238319397, "Y": 0.05341815575957298}, {"X": 0.8920754790306091, "Y": 0.053418245166540146}, {"X": 0.8920753002166748, "Y": 0.06436695158481598}, {"X": 0.8398926854133606, "Y": 0.06436685472726822}]}, "Id": "961b31fb-d513-4cab-85c9-f51932a1a944"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "848544", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0682435855269432, "Height": 0.011024048551917076, "Left": 0.8979982733726501, "Top": 0.05329424515366554}, "Polygon": [{"X": 0.8979985117912292, "Y": 0.05329424515366554}, {"X": 0.9662418961524963, "Y": 0.05329436436295509}, {"X": 0.9662416577339172, "Y": 0.06431829184293747}, {"X": 0.8979982733726501, "Y": 0.06431817263364792}]}, "Id": "b1303d05-fb2e-4455-b8be-392ff46b9aab"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "PAGE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03270893916487694, "Height": 0.007715861313045025, "Left": 0.03386988118290901, "Top": 0.0755307674407959}, "Polygon": [{"X": 0.03386994078755379, "Y": 0.0755307674407959}, {"X": 0.06657882034778595, "Y": 0.07553081959486008}, {"X": 0.06657875329256058, "Y": 0.0832466259598732}, {"X": 0.03386988118290901, "Y": 0.08324656635522842}]}, "Id": "5fa47250-3e1d-4aeb-9a52-713529396fea"}, {"BlockType": "WORD", "Confidence": 99.21556091308594, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0047321743331849575, "Height": 0.007291862275451422, "Left": 0.07063520699739456, "Top": 0.0755414068698883}, "Polygon": [{"X": 0.07063526660203934, "Y": 0.0755414068698883}, {"X": 0.07536737620830536, "Y": 0.0755414143204689}, {"X": 0.07536731660366058, "Y": 0.08283326774835587}, {"X": 0.07063520699739456, "Y": 0.08283326029777527}]}, "Id": "5b78ea01-536e-42bc-b794-e59ad4bd169a"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "040L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02938176319003105, "Height": 0.007844148203730583, "Left": 0.848124086856842, "Top": 0.07143959403038025}, "Polygon": [{"X": 0.8481242656707764, "Y": 0.07143959403038025}, {"X": 0.8775058388710022, "Y": 0.07143964618444443}, {"X": 0.8775057196617126, "Y": 0.07928373664617538}, {"X": 0.848124086856842, "Y": 0.0792836844921112}]}, "Id": "68fa787a-c599-40ab-9d01-eac3eab07cbb"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "0000W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04269392788410187, "Height": 0.007623759098351002, "Left": 0.8810332417488098, "Top": 0.07148055732250214}, "Polygon": [{"X": 0.8810333609580994, "Y": 0.07148055732250214}, {"X": 0.9237271547317505, "Y": 0.0714806318283081}, {"X": 0.9237269759178162, "Y": 0.07910431921482086}, {"X": 0.8810332417488098, "Y": 0.0791042372584343}]}, "Id": "d9cd0933-1fb9-4487-8e53-bed37b76fc14"}, {"BlockType": "WORD", "Confidence": 98.88632202148438, "Text": "0000H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03943580016493797, "Height": 0.0076247816905379295, "Left": 0.9259994626045227, "Top": 0.07152915745973587}, "Polygon": [{"X": 0.9259995818138123, "Y": 0.07152915745973587}, {"X": 0.965435266494751, "Y": 0.07152922451496124}, {"X": 0.9654350876808167, "Y": 0.07915394008159637}, {"X": 0.9259994626045227, "Y": 0.0791538655757904}]}, "Id": "ab216b48-2abb-4dd9-9142-1617e7ddb4be"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01771370694041252, "Height": 0.010263518430292606, "Left": 0.39906346797943115, "Top": 0.12117244303226471}, "Polygon": [{"X": 0.3990635871887207, "Y": 0.12117244303226471}, {"X": 0.4167771637439728, "Y": 0.1211724728345871}, {"X": 0.4167770445346832, "Y": 0.13143596053123474}, {"X": 0.39906346797943115, "Y": 0.13143593072891235}]}, "Id": "12a15c6d-d573-4235-9d86-0b0e84673e41"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07282408326864243, "Height": 0.010429337620735168, "Left": 0.42497000098228455, "Top": 0.12107077240943909}, "Polygon": [{"X": 0.4249701499938965, "Y": 0.12107077240943909}, {"X": 0.49779409170150757, "Y": 0.12107090651988983}, {"X": 0.49779394268989563, "Y": 0.13150011003017426}, {"X": 0.42497000098228455, "Y": 0.1314999759197235}]}, "Id": "1440314c-6383-4423-ad8b-28bd711b7672"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "DETAILS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07103079557418823, "Height": 0.01046853419393301, "Left": 0.5017183423042297, "Top": 0.12104357033967972}, "Polygon": [{"X": 0.5017185211181641, "Y": 0.12104357033967972}, {"X": 0.572749137878418, "Y": 0.12104369699954987}, {"X": 0.5727489590644836, "Y": 0.1315121054649353}, {"X": 0.5017183423042297, "Y": 0.13151197135448456}]}, "Id": "e346a9fb-9383-4eff-a90c-cee1715de0f3"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "(1)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02207273617386818, "Height": 0.013477184809744358, "Left": 0.5779158473014832, "Top": 0.12044350802898407}, "Polygon": [{"X": 0.5779160261154175, "Y": 0.12044350802898407}, {"X": 0.599988579750061, "Y": 0.12044354528188705}, {"X": 0.5999884009361267, "Y": 0.13392068445682526}, {"X": 0.5779158473014832, "Y": 0.13392065465450287}]}, "Id": "e9bba2e6-51db-4737-8e2e-86d7fb9d6098"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05096088722348213, "Height": 0.010738005861639977, "Left": 0.040108758956193924, "Top": 0.15501035749912262}, "Polygon": [{"X": 0.04010884463787079, "Y": 0.15501035749912262}, {"X": 0.09106964617967606, "Y": 0.15501044690608978}, {"X": 0.09106955677270889, "Y": 0.16574835777282715}, {"X": 0.040108758956193924, "Y": 0.16574826836585999}]}, "Id": "291e1570-76f7-449b-a104-7afd0d4f45ea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Associated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07549204677343369, "Height": 0.008709115907549858, "Left": 0.1880568265914917, "Top": 0.15493714809417725}, "Polygon": [{"X": 0.18805690109729767, "Y": 0.15493714809417725}, {"X": 0.263548880815506, "Y": 0.154937282204628}, {"X": 0.26354876160621643, "Y": 0.16364626586437225}, {"X": 0.1880568265914917, "Y": 0.16364611685276031}]}, "Id": "1c352dd1-93cd-4c56-85ee-b14376011227"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03274143859744072, "Height": 0.008713485673069954, "Left": 0.48433107137680054, "Top": 0.1548941433429718}, "Polygon": [{"X": 0.4843311905860901, "Y": 0.1548941433429718}, {"X": 0.5170724987983704, "Y": 0.15489420294761658}, {"X": 0.5170723795890808, "Y": 0.1636076271533966}, {"X": 0.48433107137680054, "Y": 0.16360756754875183}]}, "Id": "ca28a256-9ff8-4ee8-b7ce-a07c74d280eb"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01424717903137207, "Height": 0.006496458780020475, "Left": 0.5211355686187744, "Top": 0.15705838799476624}, "Polygon": [{"X": 0.5211356282234192, "Y": 0.15705838799476624}, {"X": 0.5353827476501465, "Y": 0.15705841779708862}, {"X": 0.5353826284408569, "Y": 0.163554847240448}, {"X": 0.5211355686187744, "Y": 0.1635548174381256}]}, "Id": "2eb225a1-71a0-437d-9d2b-fdb97b6a8065"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Seal(s)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04689733311533928, "Height": 0.010966391302645206, "Left": 0.335964173078537, "Top": 0.16060176491737366}, "Polygon": [{"X": 0.33596429228782654, "Y": 0.16060176491737366}, {"X": 0.38286149501800537, "Y": 0.16060185432434082}, {"X": 0.38286134600639343, "Y": 0.1715681552886963}, {"X": 0.335964173078537, "Y": 0.17156806588172913}]}, "Id": "404caf53-f606-4175-bd3a-ed57d612cf1a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05500639230012894, "Height": 0.01083410158753395, "Left": 0.5986597537994385, "Top": 0.16086126863956451}, "Polygon": [{"X": 0.598659873008728, "Y": 0.16086126863956451}, {"X": 0.6536661386489868, "Y": 0.16086137294769287}, {"X": 0.6536659598350525, "Y": 0.17169536650180817}, {"X": 0.5986597537994385, "Y": 0.1716952621936798}]}, "Id": "2f156154-14e4-4fb0-8c29-35f185f32a65"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008963494561612606, "Height": 0.008356933481991291, "Left": 0.6576352715492249, "Top": 0.16101482510566711}, "Polygon": [{"X": 0.6576353907585144, "Y": 0.16101482510566711}, {"X": 0.6665987372398376, "Y": 0.1610148549079895}, {"X": 0.6665986180305481, "Y": 0.16937176883220673}, {"X": 0.6576352715492249, "Y": 0.16937175393104553}]}, "Id": "2613f6ab-c15a-4777-8980-cc03befdaab4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transportation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10028257220983505, "Height": 0.010708343237638474, "Left": 0.7454329133033752, "Top": 0.1550092250108719}, "Polygon": [{"X": 0.7454330921173096, "Y": 0.1550092250108719}, {"X": 0.8457154631614685, "Y": 0.1550094187259674}, {"X": 0.8457152843475342, "Y": 0.16571757197380066}, {"X": 0.7454329133033752, "Y": 0.16571737825870514}]}, "Id": "a367c4ce-1a43-4a1b-9771-9d9689f5f318"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06433576345443726, "Height": 0.010914025828242302, "Left": 0.8497126698493958, "Top": 0.1550309658050537}, "Polygon": [{"X": 0.8497129082679749, "Y": 0.1550309658050537}, {"X": 0.914048433303833, "Y": 0.15503108501434326}, {"X": 0.9140482544898987, "Y": 0.16594499349594116}, {"X": 0.8497126698493958, "Y": 0.1659448742866516}]}, "Id": "f7d00ebf-4a44-47ad-a714-f9210ec7095f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0309385284781456, "Height": 0.008527456782758236, "Left": 0.0405488945543766, "Top": 0.16760984063148499}, "Polygon": [{"X": 0.040548961609601974, "Y": 0.16760984063148499}, {"X": 0.0714874193072319, "Y": 0.16760990023612976}, {"X": 0.07148735225200653, "Y": 0.1761372983455658}, {"X": 0.0405488945543766, "Y": 0.17613723874092102}]}, "Id": "80a9cef3-8f65-447c-91b6-1ba4b30de99c"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009538947604596615, "Height": 0.008443210273981094, "Left": 0.07557249814271927, "Top": 0.16775034368038177}, "Polygon": [{"X": 0.07557256519794464, "Y": 0.16775034368038177}, {"X": 0.08511144667863846, "Y": 0.16775035858154297}, {"X": 0.08511137217283249, "Y": 0.17619355022907257}, {"X": 0.07557249814271927, "Y": 0.17619353532791138}]}, "Id": "61878c23-a0a2-4d5b-ac21-c43bc37a51dc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0345044881105423, "Height": 0.008639411069452763, "Left": 0.0884985402226448, "Top": 0.16759803891181946}, "Polygon": [{"X": 0.08849860727787018, "Y": 0.16759803891181946}, {"X": 0.1230030208826065, "Y": 0.16759809851646423}, {"X": 0.12300294637680054, "Y": 0.17623744904994965}, {"X": 0.0884985402226448, "Y": 0.17623737454414368}]}, "Id": "ccf84e86-125b-4d21-9ae8-884f76364f0e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.072161965072155, "Height": 0.01068528275936842, "Left": 0.18866486847400665, "Top": 0.16757214069366455}, "Polygon": [{"X": 0.188664972782135, "Y": 0.16757214069366455}, {"X": 0.26082682609558105, "Y": 0.1675722748041153}, {"X": 0.2608267068862915, "Y": 0.17825742065906525}, {"X": 0.18866486847400665, "Y": 0.1782572865486145}]}, "Id": "a2b93ecf-1026-4ad8-a38b-4d40a5e4402d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04207408428192139, "Height": 0.010703271254897118, "Left": 0.4844282865524292, "Top": 0.16764196753501892}, "Polygon": [{"X": 0.48442843556404114, "Y": 0.16764196753501892}, {"X": 0.5265023708343506, "Y": 0.16764205694198608}, {"X": 0.5265021920204163, "Y": 0.17834524810314178}, {"X": 0.4844282865524292, "Y": 0.17834515869617462}]}, "Id": "3302ad56-6bf0-4818-8e34-b44ea3157a81"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009345156140625477, "Height": 0.008425632491707802, "Left": 0.7460697293281555, "Top": 0.16775964200496674}, "Polygon": [{"X": 0.7460699081420898, "Y": 0.16775964200496674}, {"X": 0.7554149031639099, "Y": 0.16775965690612793}, {"X": 0.7554147243499756, "Y": 0.1761852651834488}, {"X": 0.7460697293281555, "Y": 0.1761852502822876}]}, "Id": "bcdc73c8-2593-4d1b-96c5-a1ca29d1388f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0396997407078743, "Height": 0.008417101576924324, "Left": 0.759784460067749, "Top": 0.16764520108699799}, "Polygon": [{"X": 0.7597845792770386, "Y": 0.16764520108699799}, {"X": 0.7994841933250427, "Y": 0.16764527559280396}, {"X": 0.7994840145111084, "Y": 0.17606230080127716}, {"X": 0.759784460067749, "Y": 0.1760622262954712}]}, "Id": "f3a6674c-58e3-4d27-ad0f-de6be7250fd2"}, {"BlockType": "WORD", "Confidence": 99.08482360839844, "Text": "In-Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042406387627124786, "Height": 0.007871822454035282, "Left": 0.04064808785915375, "Top": 0.2000231146812439}, "Polygon": [{"X": 0.04064815118908882, "Y": 0.2000231146812439}, {"X": 0.08305447548627853, "Y": 0.20002318918704987}, {"X": 0.08305440843105316, "Y": 0.2078949362039566}, {"X": 0.04064808785915375, "Y": 0.20789484679698944}]}, "Id": "e084531d-98af-4906-a336-95bb36b07d4b"}, {"BlockType": "WORD", "Confidence": 93.87456512451172, "Text": "---", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011696472764015198, "Height": 0.0018027451587840915, "Left": 0.18781355023384094, "Top": 0.20353761315345764}, "Polygon": [{"X": 0.18781356513500214, "Y": 0.20353761315345764}, {"X": 0.19951002299785614, "Y": 0.20353764295578003}, {"X": 0.19950999319553375, "Y": 0.20534037053585052}, {"X": 0.18781355023384094, "Y": 0.20534034073352814}]}, "Id": "bd2c2be0-0728-433c-acd3-4bd200b43546"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "992249", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045010410249233246, "Height": 0.007626181002706289, "Left": 0.33596450090408325, "Top": 0.2002248466014862}, "Polygon": [{"X": 0.3359645903110504, "Y": 0.2002248466014862}, {"X": 0.3809749186038971, "Y": 0.20022492110729218}, {"X": 0.38097482919692993, "Y": 0.20785102248191833}, {"X": 0.33596450090408325, "Y": 0.20785093307495117}]}, "Id": "ca971632-be0c-4a3a-800d-d7140b303876"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029252607375383377, "Height": 0.00789414718747139, "Left": 0.4841039180755615, "Top": 0.19995591044425964}, "Polygon": [{"X": 0.4841040372848511, "Y": 0.19995591044425964}, {"X": 0.5133565068244934, "Y": 0.19995597004890442}, {"X": 0.5133564472198486, "Y": 0.20785005390644073}, {"X": 0.4841039180755615, "Y": 0.20784999430179596}]}, "Id": "9d0d6e91-648b-47f3-bb05-64921d328806"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "257637187", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06748058646917343, "Height": 0.007971092127263546, "Left": 0.5981258749961853, "Top": 0.19998814165592194}, "Polygon": [{"X": 0.5981259942054749, "Y": 0.19998814165592194}, {"X": 0.6656064391136169, "Y": 0.19998827576637268}, {"X": 0.6656063199043274, "Y": 0.20795923471450806}, {"X": 0.5981258749961853, "Y": 0.2079591006040573}]}, "Id": "46d37599-791f-4c90-b52e-833c2d00b891"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Super", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0347423255443573, "Height": 0.009556353092193604, "Left": 0.7459123134613037, "Top": 0.2001090943813324}, "Polygon": [{"X": 0.745912492275238, "Y": 0.2001090943813324}, {"X": 0.7806546092033386, "Y": 0.20010916888713837}, {"X": 0.7806544303894043, "Y": 0.209665447473526}, {"X": 0.7459123134613037, "Y": 0.20966538786888123}]}, "Id": "6c8ea994-aa15-451d-bca1-76f0ebd40baf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Star", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024255167692899704, "Height": 0.00776199484243989, "Left": 0.7838038206100464, "Top": 0.2001497745513916}, "Polygon": [{"X": 0.7838039994239807, "Y": 0.2001497745513916}, {"X": 0.80805903673172, "Y": 0.20014981925487518}, {"X": 0.8080588579177856, "Y": 0.20791177451610565}, {"X": 0.7838038206100464, "Y": 0.20791171491146088}]}, "Id": "060eb301-1d4a-4da2-8fa3-1f6e4c41f989"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04086284711956978, "Height": 0.009786934591829777, "Left": 0.8117110729217529, "Top": 0.1998596340417862}, "Polygon": [{"X": 0.8117112517356873, "Y": 0.1998596340417862}, {"X": 0.8525739312171936, "Y": 0.19985970854759216}, {"X": 0.8525736927986145, "Y": 0.2096465677022934}, {"X": 0.8117110729217529, "Y": 0.20964647829532623}]}, "Id": "42ac4e9b-6239-4910-b891-1188696eb64c"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017696421593427658, "Height": 0.007783474400639534, "Left": 0.8564241528511047, "Top": 0.2000444531440735}, "Polygon": [{"X": 0.8564243316650391, "Y": 0.2000444531440735}, {"X": 0.8741205930709839, "Y": 0.20004448294639587}, {"X": 0.8741204142570496, "Y": 0.20782792568206787}, {"X": 0.8564241528511047, "Y": 0.20782789587974548}]}, "Id": "27d7cead-f4d9-4cb8-afc7-d05ad2bbbc61"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "(SSRT)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04005033150315285, "Height": 0.009757504798471928, "Left": 0.8777922987937927, "Top": 0.1997496336698532}, "Polygon": [{"X": 0.877792477607727, "Y": 0.1997496336698532}, {"X": 0.9178426265716553, "Y": 0.19974970817565918}, {"X": 0.917842447757721, "Y": 0.20950713753700256}, {"X": 0.8777922987937927, "Y": 0.2095070630311966}]}, "Id": "a179f152-013b-4903-ae81-503344889662"}, {"BlockType": "WORD", "Confidence": 98.4375, "Text": "09/14/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05670871213078499, "Height": 0.007889147847890854, "Left": 0.04037685692310333, "Top": 0.21115286648273468}, "Polygon": [{"X": 0.040376920253038406, "Y": 0.21115286648273468}, {"X": 0.09708557277917862, "Y": 0.21115297079086304}, {"X": 0.09708550572395325, "Y": 0.21904200315475464}, {"X": 0.04037685692310333, "Y": 0.21904189884662628}]}, "Id": "397af9b0-43dc-4ff7-b192-1935239c73ad"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "22:45", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03350597620010376, "Height": 0.007842150516808033, "Left": 0.10702744871377945, "Top": 0.21114006638526917}, "Polygon": [{"X": 0.10702751576900482, "Y": 0.21114006638526917}, {"X": 0.1405334323644638, "Y": 0.21114012598991394}, {"X": 0.14053335785865784, "Y": 0.21898221969604492}, {"X": 0.10702744871377945, "Y": 0.21898214519023895}]}, "Id": "9e7180f6-6766-4938-8a2d-871b87a6959d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04939406365156174, "Height": 0.009533383883535862, "Left": 0.033968277275562286, "Top": 0.26251304149627686}, "Polygon": [{"X": 0.033968351781368256, "Y": 0.26251304149627686}, {"X": 0.08336234092712402, "Y": 0.2625131607055664}, {"X": 0.08336225897073746, "Y": 0.27204644680023193}, {"X": 0.033968277275562286, "Y": 0.2720463275909424}]}, "Id": "785bb5ba-7398-46c9-b32a-1438bd97b417"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009721673093736172, "Height": 0.007772509939968586, "Left": 0.0870392769575119, "Top": 0.2624870240688324}, "Polygon": [{"X": 0.08703934401273727, "Y": 0.2624870240688324}, {"X": 0.09676095098257065, "Y": 0.2624870538711548}, {"X": 0.09676088392734528, "Y": 0.2702595293521881}, {"X": 0.0870392769575119, "Y": 0.2702595293521881}]}, "Id": "12425b08-4154-42ad-bc34-bd221e58b8ac"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0522870309650898, "Height": 0.009664314799010754, "Left": 0.10038687288761139, "Top": 0.2623896300792694}, "Polygon": [{"X": 0.10038696229457855, "Y": 0.2623896300792694}, {"X": 0.1526739001274109, "Y": 0.26238974928855896}, {"X": 0.15267381072044373, "Y": 0.27205395698547363}, {"X": 0.10038687288761139, "Y": 0.2720538377761841}]}, "Id": "9a4b76c0-e218-47a6-a338-9822822a22de"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014701366424560547, "Height": 0.005897307302802801, "Left": 0.15649311244487762, "Top": 0.2643209397792816}, "Polygon": [{"X": 0.1564931720495224, "Y": 0.2643209397792816}, {"X": 0.17119447886943817, "Y": 0.264320969581604}, {"X": 0.1711944192647934, "Y": 0.27021825313568115}, {"X": 0.15649311244487762, "Y": 0.27021822333335876}]}, "Id": "4c9d4374-8f56-4b63-b35a-4585f45bae05"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05255569890141487, "Height": 0.00817271787673235, "Left": 0.17488472163677216, "Top": 0.2621564567089081}, "Polygon": [{"X": 0.17488479614257812, "Y": 0.2621564567089081}, {"X": 0.22744041681289673, "Y": 0.26215657591819763}, {"X": 0.22744032740592957, "Y": 0.27032917737960815}, {"X": 0.17488472163677216, "Y": 0.270329087972641}]}, "Id": "31645e5f-987f-473a-9e6f-6d4534b37ac6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043601904064416885, "Height": 0.009665112942457199, "Left": 0.2312048375606537, "Top": 0.26246950030326843}, "Polygon": [{"X": 0.23120494186878204, "Y": 0.26246950030326843}, {"X": 0.2748067378997803, "Y": 0.2624695897102356}, {"X": 0.2748066186904907, "Y": 0.27213460206985474}, {"X": 0.2312048375606537, "Y": 0.2721345126628876}]}, "Id": "87273651-7fc8-4b5e-8814-ff6d58475cb8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011729191988706589, "Height": 0.007200827822089195, "Left": 0.2784792482852936, "Top": 0.2631247043609619}, "Polygon": [{"X": 0.27847933769226074, "Y": 0.2631247043609619}, {"X": 0.2902084290981293, "Y": 0.2631247341632843}, {"X": 0.2902083694934845, "Y": 0.27032551169395447}, {"X": 0.2784792482852936, "Y": 0.27032551169395447}]}, "Id": "f44de41a-d724-4f2a-92ea-38e09a38e70f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "AGS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02593986503779888, "Height": 0.007731006480753422, "Left": 0.29312318563461304, "Top": 0.2625380754470825}, "Polygon": [{"X": 0.2931232750415802, "Y": 0.2625380754470825}, {"X": 0.3190630376338959, "Y": 0.2625381350517273}, {"X": 0.3190629482269287, "Y": 0.27026909589767456}, {"X": 0.29312318563461304, "Y": 0.2702690362930298}]}, "Id": "eab0228d-bf6c-4a4d-b6cd-3b8a03ab8353"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "locations", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05443013459444046, "Height": 0.008072794415056705, "Left": 0.3227861225605011, "Top": 0.2622717618942261}, "Polygon": [{"X": 0.32278624176979065, "Y": 0.2622717618942261}, {"X": 0.37721627950668335, "Y": 0.2622718811035156}, {"X": 0.3772161602973938, "Y": 0.2703445553779602}, {"X": 0.3227861225605011, "Y": 0.27034446597099304}]}, "Id": "f93ca30f-8dc5-4948-9dfe-568bca88bab2"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "fc057be7-84fd-4cba-bb41-219fef1cb957", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "fc057be7-84fd-4cba-bb41-219fef1cb957", "content-type": "application/x-amz-json-1.1", "content-length": "46395", "date": "Wed, 24 Sep 2025 13:28:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "fbd0faa9-6b90-4af9-acb7-23fb3e16ee1b", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:28:41 GMT", "content-type": "application/json", "content-length": "822", "connection": "keep-alive", "x-amzn-requestid": "fbd0faa9-6b90-4af9-acb7-23fb3e16ee1b"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1 content. It's a gate receipt. This is likely an ingate document? The header says \"J1 GATE RECEIPT\" and details include In-Gate. This matches ingate document. According to definitions: Ingates: Record of vehicle/container entering a facility. Keywords: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In. This page has In-Gate. So doc_type: ingate. So we produce JSON with documents array having page_no 1, doc_type ingate."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"ingate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2722, "outputTokens": 140, "totalTokens": 2862}, "metrics": {"latencyMs": 753}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/816390f2_NZ0W72MP1PN7WP5Z6E33.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n[csx\n]\nJ1 GATE RECEIPT\nChicago 59th St, IL\nFor questions call, 1-877-SHIPCSX\nCAAU 848544\nPAGE 1\n040L 0000W 0000H\nJ1 RECEIPT DETAILS (1)\nActivity\nAssociated\nLoad or\nSeal(s)\nBooking #\nTransportation Company\nDate & Time\nEquipment\nEmpty\n& Driver\nIn-Gate\n---\n992249\nLoad\n257637187\nSuper Star Freight Inc (SSRT)\n09/14/25 22:45\nDamage is captured on recorded images at AGS locations\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}