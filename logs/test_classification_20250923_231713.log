2025-09-23 23:17:13,989 - INFO - Logging initialized. Log file: logs/test_classification_20250923_231713.log
2025-09-23 23:17:13,989 - INFO - 📁 Found 8 files to process
2025-09-23 23:17:13,989 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 23:17:13,989 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-23 23:17:13,989 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-23 23:17:13,989 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-23 23:17:13,989 - INFO - ⬆️ [23:17:13] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:15,619 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/bb5ec611_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:15,619 - INFO - 🔍 [23:17:15] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:15,620 - INFO - ⬆️ [23:17:15] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:15,621 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:15,647 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:15,654 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bb5ec611_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:15,655 - INFO - Processing PDF from S3...
2025-09-23 23:17:15,655 - INFO - Downloading PDF from S3 to /tmp/tmprbzayedn/bb5ec611_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:16,998 - INFO - Splitting PDF into individual pages...
2025-09-23 23:17:16,999 - INFO - Splitting PDF bb5ec611_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-23 23:17:17,007 - INFO - Split PDF into 1 pages
2025-09-23 23:17:17,007 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:17:17,007 - INFO - Expected pages: [1]
2025-09-23 23:17:21,040 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/6864a849_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:21,040 - INFO - 🔍 [23:17:21] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:21,041 - INFO - ⬆️ [23:17:21] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:21,043 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:21,057 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:21,061 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6864a849_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:21,062 - INFO - Processing image from S3...
2025-09-23 23:17:21,349 - INFO - Page 1: Extracted 1260 characters, 84 lines from bb5ec611_BQJUG5URFR2GH9ECWFV4_769732f0_page_001.pdf
2025-09-23 23:17:21,349 - INFO - Successfully processed page 1
2025-09-23 23:17:21,350 - INFO - Combined 1 pages into final text
2025-09-23 23:17:21,350 - INFO - Text validation for bb5ec611_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-23 23:17:21,350 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:21,350 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:21,350 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:21,350 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:21,350 - INFO - tool_config is None: False
2025-09-23 23:17:21,350 - INFO - tool_config is truthy: True
2025-09-23 23:17:21,350 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:21,350 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:21,350 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:21,351 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:21,956 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:21,957 - INFO - 🔍 [23:17:21] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:21,959 - INFO - ⬆️ [23:17:21] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:21,960 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:21,974 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:21,978 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:21,978 - INFO - Processing PDF from S3...
2025-09-23 23:17:21,978 - INFO - Downloading PDF from S3 to /tmp/tmp4wcf1pli/7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:22,638 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/10679d23_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:22,638 - INFO - 🔍 [23:17:22] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:22,639 - INFO - ⬆️ [23:17:22] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:22,640 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:22,661 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:22,666 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/10679d23_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:22,666 - INFO - Processing PDF from S3...
2025-09-23 23:17:22,666 - INFO - Downloading PDF from S3 to /tmp/tmpudyjhjcp/10679d23_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:23,308 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bb5ec611_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:24,527 - INFO - Splitting PDF into individual pages...
2025-09-23 23:17:24,531 - INFO - Splitting PDF 10679d23_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-23 23:17:24,544 - INFO - Split PDF into 7 pages
2025-09-23 23:17:24,544 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:17:24,544 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 23:17:25,197 - INFO - S3 Image temp/6864a849_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-23 23:17:25,197 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:25,198 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:25,198 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:25,198 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:25,198 - INFO - tool_config is None: False
2025-09-23 23:17:25,198 - INFO - tool_config is truthy: True
2025-09-23 23:17:25,198 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:25,198 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:25,198 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:25,198 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:26,826 - INFO - Splitting PDF into individual pages...
2025-09-23 23:17:26,828 - INFO - Splitting PDF 7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-23 23:17:26,832 - INFO - Split PDF into 1 pages
2025-09-23 23:17:26,832 - INFO - Processing pages with Textract in parallel...
2025-09-23 23:17:26,832 - INFO - Expected pages: [1]
2025-09-23 23:17:27,185 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/710ae907_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:27,185 - INFO - 🔍 [23:17:27] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:27,186 - INFO - ⬆️ [23:17:27] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:27,187 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:27,205 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:27,210 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/710ae907_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:27,210 - INFO - Processing image from S3...
2025-09-23 23:17:28,790 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6864a849_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:28,870 - INFO - Page 2: Extracted 1821 characters, 105 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_002.pdf
2025-09-23 23:17:28,871 - INFO - Successfully processed page 2
2025-09-23 23:17:29,055 - INFO - Page 1: Extracted 1731 characters, 110 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_001.pdf
2025-09-23 23:17:29,055 - INFO - Successfully processed page 1
2025-09-23 23:17:29,283 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/2d139614_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:29,284 - INFO - 🔍 [23:17:29] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:29,284 - INFO - ⬆️ [23:17:29] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:29,284 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:29,309 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:29,318 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2d139614_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:29,318 - INFO - Processing image from S3...
2025-09-23 23:17:29,354 - INFO - Page 5: Extracted 2059 characters, 131 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_005.pdf
2025-09-23 23:17:29,354 - INFO - Successfully processed page 5
2025-09-23 23:17:29,606 - INFO - Page 6: Extracted 1973 characters, 129 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_006.pdf
2025-09-23 23:17:29,607 - INFO - Successfully processed page 6
2025-09-23 23:17:29,689 - INFO - Page 3: Extracted 2265 characters, 147 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_003.pdf
2025-09-23 23:17:29,689 - INFO - Successfully processed page 3
2025-09-23 23:17:29,900 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/eb0305e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:29,901 - INFO - 🔍 [23:17:29] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:29,901 - INFO - ⬆️ [23:17:29] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:29,904 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:29,918 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:29,923 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb0305e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:29,924 - INFO - Processing image from S3...
2025-09-23 23:17:31,094 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/bf30c808_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:31,095 - INFO - 🔍 [23:17:31] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:31,097 - INFO - Initializing TextractProcessor...
2025-09-23 23:17:31,132 - INFO - Initializing BedrockProcessor...
2025-09-23 23:17:31,141 - INFO - Page 4: Extracted 2242 characters, 148 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_004.pdf
2025-09-23 23:17:31,147 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf30c808_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:31,149 - INFO - Successfully processed page 4
2025-09-23 23:17:31,150 - INFO - Processing image from S3...
2025-09-23 23:17:31,175 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:17:31,175 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:17:31,457 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bb5ec611_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:31,463 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:17:31,463 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:17:31,751 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6864a849_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:32,200 - INFO - S3 Image temp/eb0305e3_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-23 23:17:32,200 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:32,201 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:32,201 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:32,201 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:32,201 - INFO - tool_config is None: False
2025-09-23 23:17:32,201 - INFO - tool_config is truthy: True
2025-09-23 23:17:32,201 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:32,201 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:32,201 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:32,201 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:32,427 - INFO - S3 Image temp/710ae907_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-23 23:17:32,427 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:32,427 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:32,427 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:32,427 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:32,427 - INFO - tool_config is None: False
2025-09-23 23:17:32,427 - INFO - tool_config is truthy: True
2025-09-23 23:17:32,427 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:32,427 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:32,427 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:32,427 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:32,653 - INFO - Page 7: Extracted 417 characters, 27 lines from 10679d23_O2IU5G77LYNTYE0RP1TI_32726408_page_007.pdf
2025-09-23 23:17:32,653 - INFO - Successfully processed page 7
2025-09-23 23:17:32,654 - INFO - Combined 7 pages into final text
2025-09-23 23:17:32,655 - INFO - Text validation for 10679d23_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-23 23:17:32,656 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:32,656 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:32,656 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:32,656 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:32,656 - INFO - tool_config is None: False
2025-09-23 23:17:32,656 - INFO - tool_config is truthy: True
2025-09-23 23:17:32,656 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:32,656 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:32,656 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:32,656 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:33,508 - INFO - Page 1: Extracted 519 characters, 34 lines from 7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT_e2bbaf44_page_001.pdf
2025-09-23 23:17:33,508 - INFO - Successfully processed page 1
2025-09-23 23:17:33,508 - INFO - Combined 1 pages into final text
2025-09-23 23:17:33,509 - INFO - Text validation for 7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-23 23:17:33,509 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:33,509 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:33,509 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:33,509 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:33,509 - INFO - tool_config is None: False
2025-09-23 23:17:33,509 - INFO - tool_config is truthy: True
2025-09-23 23:17:33,509 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:33,510 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:33,510 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:33,510 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:33,725 - INFO - S3 Image temp/2d139614_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-23 23:17:33,725 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:33,725 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:33,725 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:33,725 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:33,725 - INFO - tool_config is None: False
2025-09-23 23:17:33,725 - INFO - tool_config is truthy: True
2025-09-23 23:17:33,725 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:33,725 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:33,725 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:33,725 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:34,295 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb0305e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:34,299 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:17:34,299 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:17:34,322 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/710ae907_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:34,353 - INFO - S3 Image temp/bf30c808_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-23 23:17:34,353 - INFO - Analyzing document types with Bedrock...
2025-09-23 23:17:34,353 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 23:17:34,353 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 23:17:34,353 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 23:17:34,353 - INFO - tool_config is None: False
2025-09-23 23:17:34,353 - INFO - tool_config is truthy: True
2025-09-23 23:17:34,353 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 23:17:34,354 - INFO - toolConfig keys: ['tools']
2025-09-23 23:17:34,354 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 23:17:34,354 - INFO - === END BEDROCK DEBUG ===
2025-09-23 23:17:34,586 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb0305e3_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:34,632 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:17:34,633 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:17:34,922 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/710ae907_PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:35,069 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/10679d23_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:35,189 - INFO - 

O2IU5G77LYNTYE0RP1TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        },
        {
            "page_no": 4,
            "doc_type": "log"
        },
        {
            "page_no": 5,
            "doc_type": "log"
        },
        {
            "page_no": 6,
            "doc_type": "log"
        },
        {
            "page_no": 7,
            "doc_type": "log"
        }
    ]
}
2025-09-23 23:17:35,189 - INFO - 

✓ Saved result: output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:35,479 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/10679d23_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:36,128 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2d139614_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:36,152 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:17:36,152 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:17:36,154 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:36,422 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf30c808_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:36,503 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2d139614_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:36,517 - INFO - 

KE7TCH9TPQZFVA5CZ3HT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-23 23:17:36,517 - INFO - 

✓ Saved result: output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:17:36,798 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7b7b0c4e_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:36,805 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-23 23:17:36,806 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:17:37,095 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf30c808_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:37,096 - INFO - 
📊 Processing Summary:
2025-09-23 23:17:37,096 - INFO -    Total files: 8
2025-09-23 23:17:37,096 - INFO -    Successful: 8
2025-09-23 23:17:37,096 - INFO -    Failed: 0
2025-09-23 23:17:37,096 - INFO -    Duration: 23.11 seconds
2025-09-23 23:17:37,096 - INFO -    Output directory: output
2025-09-23 23:17:37,096 - INFO - 
📋 Successfully Processed Files:
2025-09-23 23:17:37,097 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:17:37,097 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:17:37,097 - INFO -    📄 KE7TCH9TPQZFVA5CZ3HT.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-23 23:17:37,097 - INFO -    📄 O2IU5G77LYNTYE0RP1TI.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"},{"page_no":4,"doc_type":"log"},{"page_no":5,"doc_type":"log"},{"page_no":6,"doc_type":"log"},{"page_no":7,"doc_type":"log"}]}
2025-09-23 23:17:37,097 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-23 23:17:37,097 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:17:37,097 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:17:37,097 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-23 23:17:37,098 - INFO - 
============================================================================================================================================
2025-09-23 23:17:37,098 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 23:17:37,098 - INFO - ============================================================================================================================================
2025-09-23 23:17:37,098 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 23:17:37,098 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:17:37,098 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-23 23:17:37,098 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-23 23:17:37,098 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:17:37,098 - INFO - 
2025-09-23 23:17:37,099 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-23 23:17:37,099 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-23 23:17:37,099 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:17:37,099 - INFO - 
2025-09-23 23:17:37,099 - INFO - KE7TCH9TPQZFVA5CZ3HT.pdf                           1      coa                  run1_KE7TCH9TPQZFVA5CZ3HT.json                    
2025-09-23 23:17:37,099 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-23 23:17:37,099 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:17:37,099 - INFO - 
2025-09-23 23:17:37,099 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           1      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,099 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,099 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,099 - INFO - 
2025-09-23 23:17:37,099 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           2      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,099 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,099 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,100 - INFO - 
2025-09-23 23:17:37,100 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           3      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,100 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,100 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,100 - INFO - 
2025-09-23 23:17:37,100 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           4      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,100 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,100 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,100 - INFO - 
2025-09-23 23:17:37,100 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           5      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,100 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,100 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,100 - INFO - 
2025-09-23 23:17:37,100 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           6      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,100 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,100 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,100 - INFO - 
2025-09-23 23:17:37,100 - INFO - O2IU5G77LYNTYE0RP1TI.pdf                           7      log                  run1_O2IU5G77LYNTYE0RP1TI.json                    
2025-09-23 23:17:37,101 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf
2025-09-23 23:17:37,101 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,101 - INFO - 
2025-09-23 23:17:37,101 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-23 23:17:37,101 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-23 23:17:37,101 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:17:37,101 - INFO - 
2025-09-23 23:17:37,101 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      other                run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-23 23:17:37,101 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-23 23:17:37,101 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:17:37,101 - INFO - 
2025-09-23 23:17:37,101 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      other                run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-23 23:17:37,101 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-23 23:17:37,101 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:17:37,101 - INFO - 
2025-09-23 23:17:37,101 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-23 23:17:37,101 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-23 23:17:37,101 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:17:37,101 - INFO - 
2025-09-23 23:17:37,102 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 23:17:37,102 - INFO - Total entries: 14
2025-09-23 23:17:37,102 - INFO - ============================================================================================================================================
2025-09-23 23:17:37,102 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 23:17:37,102 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:17:37,102 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-23 23:17:37,102 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-23 23:17:37,102 - INFO -   3. KE7TCH9TPQZFVA5CZ3HT.pdf            Page 1   → coa             | run1_KE7TCH9TPQZFVA5CZ3HT.json
2025-09-23 23:17:37,102 - INFO -   4. O2IU5G77LYNTYE0RP1TI.pdf            Page 1   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -   5. O2IU5G77LYNTYE0RP1TI.pdf            Page 2   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -   6. O2IU5G77LYNTYE0RP1TI.pdf            Page 3   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -   7. O2IU5G77LYNTYE0RP1TI.pdf            Page 4   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -   8. O2IU5G77LYNTYE0RP1TI.pdf            Page 5   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -   9. O2IU5G77LYNTYE0RP1TI.pdf            Page 6   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -  10. O2IU5G77LYNTYE0RP1TI.pdf            Page 7   → log             | run1_O2IU5G77LYNTYE0RP1TI.json
2025-09-23 23:17:37,102 - INFO -  11. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-23 23:17:37,102 - INFO -  12. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → other           | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-23 23:17:37,103 - INFO -  13. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → other           | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-23 23:17:37,103 - INFO -  14. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-23 23:17:37,103 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 23:17:37,103 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 23.106503, 'processed_files': [{'filename': 'BQJUG5URFR2GH9ECWFV4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/BQJUG5URFR2GH9ECWFV4.pdf'}, {'filename': 'DTA5S55B66Z5U1H1GDK5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/DTA5S55B66Z5U1H1GDK5.jpeg'}, {'filename': 'KE7TCH9TPQZFVA5CZ3HT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/KE7TCH9TPQZFVA5CZ3HT.pdf'}, {'filename': 'O2IU5G77LYNTYE0RP1TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}, {'page_no': 2, 'doc_type': 'log'}, {'page_no': 3, 'doc_type': 'log'}, {'page_no': 4, 'doc_type': 'log'}, {'page_no': 5, 'doc_type': 'log'}, {'page_no': 6, 'doc_type': 'log'}, {'page_no': 7, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/O2IU5G77LYNTYE0RP1TI.pdf'}, {'filename': 'PEI6APOK17E5E1C2H2TN.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/PEI6APOK17E5E1C2H2TN.jpg'}, {'filename': 'QRYUPA9PAG4E9QPW5OT2.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/QRYUPA9PAG4E9QPW5OT2.jpeg'}, {'filename': 'RCXF8W06HPYJQHAQ0N3S.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/RCXF8W06HPYJQHAQ0N3S.jpg'}, {'filename': 'WKJ8LEUD5SSNRVRB1YYW.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/log/WKJ8LEUD5SSNRVRB1YYW.jpg'}]}
