{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}, {"page_no": 2, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "6762c65f-e151-429c-a077-20ad35f2a3e3", "Relationships": [{"Type": "CHILD", "Ids": ["343325c5-79e4-41b8-bf1c-88ce24ea0f0a", "9923e8cc-470a-4085-af55-af132755cadb", "fe888265-f97b-4ec3-bb3f-4a5ee8aa0f30", "6a9e4589-d473-452e-8583-d588b4a327ab", "688566f3-712e-4d4a-909a-6afb6aad0816", "115b22c0-dc4f-4554-bffe-6b90e35f46da", "d777ed7d-3a33-43e7-9afa-f9a21dfe5e6d", "dca0f761-be05-43d6-9685-7d2e6654e68f", "7af1cdbb-f30b-45b5-acd0-18e4b5bc23a6", "54c86519-3b8c-4fb8-bbf9-6ad1ed86f8c9", "8e70bb9a-5668-4e0e-9b05-73f88894145d", "0c2d22df-792d-495c-8b0d-19310cc1e2e2", "60a15564-8a49-45bb-a0f3-1eec637a471b", "07ac635b-3020-440a-9629-f8103af013e3", "88094897-26f2-4f3f-8c1e-375b3d784651", "16f89742-1278-4901-907e-7204eb94e155", "0b4bc020-4517-40d7-9335-e294bb8c22b7", "6a881daa-1dcc-4bb3-924f-6f16621e4879", "555a723a-096e-411d-9069-b2b967ee9338", "b8d18b2d-ed23-411c-a9c9-cac9e7255d24", "00b27d36-05a1-43fe-bea3-9f65f1e976f1", "cee68393-47a8-492a-8159-5f3efdeaed18", "aea890bc-6cf1-4c69-bf7b-7720e2f610fa", "3383640d-91c4-4a82-a0b2-e08a7d2df6d5", "aea0b398-f1b3-4d41-b9a0-dad9d1e5f7b4", "4cef4cd9-7926-4616-93ae-667a17230161", "8e80f32c-61bb-44ab-abff-1be43e0b829e", "9ae56429-323e-4563-93f6-22a0eb9ea0b0", "40e79aba-be6f-4b0e-bac2-cddcc671d775"]}]}, {"BlockType": "LINE", "Confidence": 99.29488372802734, "Text": "8/22/25, 4:25 PM", "Geometry": {"BoundingBox": {"Width": 0.09901738911867142, "Height": 0.00943772029131651, "Left": 0.03925308585166931, "Top": 0.020746659487485886}, "Polygon": [{"X": 0.0392531156539917, "Y": 0.02074672095477581}, {"X": 0.13827048242092133, "Y": 0.020746659487485886}, {"X": 0.13827043771743774, "Y": 0.030184321105480194}, {"X": 0.03925308585166931, "Y": 0.03018438071012497}]}, "Id": "343325c5-79e4-41b8-bf1c-88ce24ea0f0a", "Relationships": [{"Type": "CHILD", "Ids": ["0bd2fc66-e7f0-4800-a104-374fe47d244c", "88ef6a47-eaab-4877-be59-8dcd4843aa08", "096937bc-8c84-48da-8159-2b59b6abb26e"]}]}, {"BlockType": "LINE", "Confidence": 99.814453125, "Text": "ShipCSX", "Geometry": {"BoundingBox": {"Width": 0.053457677364349365, "Height": 0.010314098559319973, "Left": 0.5477864742279053, "Top": 0.02053697779774666}, "Polygon": [{"X": 0.54778653383255, "Y": 0.020537011325359344}, {"X": 0.6012441515922546, "Y": 0.02053697779774666}, {"X": 0.6012440919876099, "Y": 0.03085104562342167}, {"X": 0.5477864742279053, "Y": 0.030851077288389206}]}, "Id": "9923e8cc-470a-4085-af55-af132755cadb", "Relationships": [{"Type": "CHILD", "Ids": ["87bd40eb-c6ce-42dd-8eeb-e9a49fb8b908"]}]}, {"BlockType": "LINE", "Confidence": 65.52462005615234, "Text": "ShipCSX ############ III.", "Geometry": {"BoundingBox": {"Width": 0.19251520931720734, "Height": 0.0339469388127327, "Left": 0.06585811078548431, "Top": 0.051732417196035385}, "Polygon": [{"X": 0.06585822999477386, "Y": 0.051732517778873444}, {"X": 0.25837332010269165, "Y": 0.051732417196035385}, {"X": 0.2583731710910797, "Y": 0.08567927032709122}, {"X": 0.06585811078548431, "Y": 0.08567935228347778}]}, "Id": "fe888265-f97b-4ec3-bb3f-4a5ee8aa0f30", "Relationships": [{"Type": "CHILD", "Ids": ["6d056e84-5564-42df-8653-0f8da6137a37", "c40085cf-330d-4de8-a5a5-e8c4fb1c3a3b", "a2ce9100-be99-4401-ae08-aefac0c2c036"]}]}, {"BlockType": "LINE", "Confidence": 99.9083251953125, "Text": "Login to ShipCSX", "Geometry": {"BoundingBox": {"Width": 0.12718118727207184, "Height": 0.012999488972127438, "Left": 0.5085984468460083, "Top": 0.0646568238735199}, "Polygon": [{"X": 0.5085985064506531, "Y": 0.06465688347816467}, {"X": 0.635779619216919, "Y": 0.0646568238735199}, {"X": 0.6357795000076294, "Y": 0.07765625417232513}, {"X": 0.5085984468460083, "Y": 0.07765631377696991}]}, "Id": "6a9e4589-d473-452e-8583-d588b4a327ab", "Relationships": [{"Type": "CHILD", "Ids": ["a82407f6-bfc4-4193-99d4-a8c2f7254628", "0496045e-7b79-4178-8907-649b5c45c447", "a6b61cbd-207d-412c-b8c0-03d1e1d4b42f"]}]}, {"BlockType": "LINE", "Confidence": 99.83318328857422, "Text": "INTERMODAL", "Geometry": {"BoundingBox": {"Width": 0.11169197410345078, "Height": 0.010523735545575619, "Left": 0.06729956716299057, "Top": 0.12056335061788559}, "Polygon": [{"X": 0.06729960441589355, "Y": 0.12056338787078857}, {"X": 0.17899154126644135, "Y": 0.12056335061788559}, {"X": 0.17899149656295776, "Y": 0.1310870498418808}, {"X": 0.06729956716299057, "Y": 0.13108707964420319}]}, "Id": "688566f3-712e-4d4a-909a-6afb6aad0816", "Relationships": [{"Type": "CHILD", "Ids": ["17f9bf74-0611-4a42-8cd9-6951fdf9216e"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "CARLOAD", "Geometry": {"BoundingBox": {"Width": 0.07976388186216354, "Height": 0.01023051980882883, "Left": 0.212533637881279, "Top": 0.12074360251426697}, "Polygon": [{"X": 0.21253368258476257, "Y": 0.12074362486600876}, {"X": 0.29229751229286194, "Y": 0.12074360251426697}, {"X": 0.29229745268821716, "Y": 0.13097409904003143}, {"X": 0.212533637881279, "Y": 0.13097412884235382}]}, "Id": "115b22c0-dc4f-4554-bffe-6b90e35f46da", "Relationships": [{"Type": "CHILD", "Ids": ["42f53452-5256-46db-b947-33c04e3f39d0"]}]}, {"BlockType": "LINE", "Confidence": 99.93856811523438, "Text": "New Customer? Start here", "Geometry": {"BoundingBox": {"Width": 0.19794225692749023, "Height": 0.010598283261060715, "Left": 0.7371698617935181, "Top": 0.12056905776262283}, "Polygon": [{"X": 0.7371699213981628, "Y": 0.12056911736726761}, {"X": 0.9351121187210083, "Y": 0.12056905776262283}, {"X": 0.9351120591163635, "Y": 0.13116727769374847}, {"X": 0.7371698617935181, "Y": 0.13116733729839325}]}, "Id": "d777ed7d-3a33-43e7-9afa-f9a21dfe5e6d", "Relationships": [{"Type": "CHILD", "Ids": ["cc196a39-2b8a-4bda-9484-08f1298f0b71", "ab1870c8-4696-4c32-89c9-ed0daaf089f9", "d3113927-20bf-4b1d-8478-1fa4ad097c75", "619c5a4b-8f2d-40ee-9632-ddfb1d19aa67"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Shipment Lookup Results", "Geometry": {"BoundingBox": {"Width": 0.24812689423561096, "Height": 0.01653239130973816, "Left": 0.0663376972079277, "Top": 0.17545028030872345}, "Polygon": [{"X": 0.06633775681257248, "Y": 0.17545032501220703}, {"X": 0.31446459889411926, "Y": 0.17545028030872345}, {"X": 0.3144645094871521, "Y": 0.19198265671730042}, {"X": 0.0663376972079277, "Y": 0.1919826716184616}]}, "Id": "dca0f761-be05-43d6-9685-7d2e6654e68f", "Relationships": [{"Type": "CHILD", "Ids": ["db23f0d3-fae4-417a-9cdc-068635bcdf93", "d5c3adee-09f1-44d7-ab9b-424d55a1fb5a", "f7cc625a-efb2-44ab-9160-61b26b6bd98e"]}]}, {"BlockType": "LINE", "Confidence": 99.85906219482422, "Text": "As of 16:20 on 08/22/25", "Geometry": {"BoundingBox": {"Width": 0.158379465341568, "Height": 0.009470001794397831, "Left": 0.3312145173549652, "Top": 0.17966310679912567}, "Polygon": [{"X": 0.3312145471572876, "Y": 0.17966313660144806}, {"X": 0.4895939826965332, "Y": 0.17966310679912567}, {"X": 0.4895939230918884, "Y": 0.18913309276103973}, {"X": 0.3312145173549652, "Y": 0.18913310766220093}]}, "Id": "7af1cdbb-f30b-45b5-acd0-18e4b5bc23a6", "Relationships": [{"Type": "CHILD", "Ids": ["0dd61fe5-776f-4ec3-9209-ff2e7cd44f35", "0c6429d5-23bb-4093-886d-01b250137cab", "348716a9-0add-4d78-af10-bb8763e6b916", "a957a737-7fe8-424d-896d-685968f07281", "b26f8cf7-1f92-456b-b7ae-7300e9d58d96"]}]}, {"BlockType": "LINE", "Confidence": 99.76203918457031, "Text": "Modify Search", "Geometry": {"BoundingBox": {"Width": 0.10737447440624237, "Height": 0.012897240929305553, "Left": 0.8269309997558594, "Top": 0.17731256783008575}, "Polygon": [{"X": 0.8269310593605042, "Y": 0.17731258273124695}, {"X": 0.9343054294586182, "Y": 0.17731256783008575}, {"X": 0.9343053102493286, "Y": 0.1902097910642624}, {"X": 0.8269309997558594, "Y": 0.19020980596542358}]}, "Id": "54c86519-3b8c-4fb8-bbf9-6ad1ed86f8c9", "Relationships": [{"Type": "CHILD", "Ids": ["a3045a97-bbdd-4a02-a26a-8dce54b0551a", "c3e4c91b-c2d1-45c1-9d59-ab45c3a06edf"]}]}, {"BlockType": "LINE", "Confidence": 99.9574203491211, "Text": "Chicago 59th St", "Geometry": {"BoundingBox": {"Width": 0.13510820269584656, "Height": 0.014874709770083427, "Left": 0.06531623750925064, "Top": 0.23167023062705994}, "Polygon": [{"X": 0.06531628966331482, "Y": 0.23167023062705994}, {"X": 0.2004244327545166, "Y": 0.23167023062705994}, {"X": 0.20042437314987183, "Y": 0.2465449422597885}, {"X": 0.06531623750925064, "Y": 0.24654492735862732}]}, "Id": "8e70bb9a-5668-4e0e-9b05-73f88894145d", "Relationships": [{"Type": "CHILD", "Ids": ["9a7bf731-9e33-4d52-8687-15a0264691ea", "e4dd4b79-ef1d-4267-a009-31a63fbecd7a", "2b282364-5cb6-4432-a155-45b7ad4bba93"]}]}, {"BlockType": "LINE", "Confidence": 85.7959976196289, "Text": "<", "Geometry": {"BoundingBox": {"Width": 0.016306869685649872, "Height": 0.007808193564414978, "Left": 0.9105649590492249, "Top": 0.23384323716163635}, "Polygon": [{"X": 0.9105650186538696, "Y": 0.23384323716163635}, {"X": 0.9268718361854553, "Y": 0.23384323716163635}, {"X": 0.9268717169761658, "Y": 0.24165143072605133}, {"X": 0.9105649590492249, "Y": 0.24165143072605133}]}, "Id": "0c2d22df-792d-495c-8b0d-19310cc1e2e2", "Relationships": [{"Type": "CHILD", "Ids": ["e607d37b-3766-48e6-b771-009e65c7fd0f"]}]}, {"BlockType": "LINE", "Confidence": 96.92343139648438, "Text": "NOTIFIED (0)", "Geometry": {"BoundingBox": {"Width": 0.0873735100030899, "Height": 0.010835348628461361, "Left": 0.126900777220726, "Top": 0.27612459659576416}, "Polygon": [{"X": 0.1269008219242096, "Y": 0.27612459659576416}, {"X": 0.21427428722381592, "Y": 0.27612462639808655}, {"X": 0.21427424252033234, "Y": 0.2869599461555481}, {"X": 0.126900777220726, "Y": 0.2869599461555481}]}, "Id": "60a15564-8a49-45bb-a0f3-1eec637a471b", "Relationships": [{"Type": "CHILD", "Ids": ["d5aa17b4-08ed-486a-8441-b7b05e8dac1f", "9a83575b-d49c-40c0-8630-ab3d49aac6d8"]}]}, {"BlockType": "LINE", "Confidence": 98.16356658935547, "Text": "ENROUTE (0)", "Geometry": {"BoundingBox": {"Width": 0.08763321489095688, "Height": 0.010869486257433891, "Left": 0.3543371856212616, "Top": 0.27611371874809265}, "Polygon": [{"X": 0.35433724522590637, "Y": 0.27611371874809265}, {"X": 0.4419704079627991, "Y": 0.27611371874809265}, {"X": 0.4419703483581543, "Y": 0.2869831919670105}, {"X": 0.3543371856212616, "Y": 0.2869831919670105}]}, "Id": "07ac635b-3020-440a-9629-f8103af013e3", "Relationships": [{"Type": "CHILD", "Ids": ["d4ef1738-8481-4488-91e4-af6bee5cc8ed", "159533a8-87e2-4346-bd93-4a2354357436"]}]}, {"BlockType": "LINE", "Confidence": 99.30136108398438, "Text": "IN-GATE (1)", "Geometry": {"BoundingBox": {"Width": 0.0776836946606636, "Height": 0.011174974963068962, "Left": 0.580407977104187, "Top": 0.2758520543575287}, "Polygon": [{"X": 0.5804080367088318, "Y": 0.2758520543575287}, {"X": 0.65809166431427, "Y": 0.2758520841598511}, {"X": 0.6580916047096252, "Y": 0.2870270311832428}, {"X": 0.580407977104187, "Y": 0.2870270311832428}]}, "Id": "88094897-26f2-4f3f-8c1e-375b3d784651", "Relationships": [{"Type": "CHILD", "Ids": ["a4f3a5e8-2a34-4339-833d-3b879bd9e384", "09d02474-6a61-4591-8b46-3493632d8201"]}]}, {"BlockType": "LINE", "Confidence": 95.25171661376953, "Text": "OTHERS (0)", "Geometry": {"BoundingBox": {"Width": 0.07648663967847824, "Height": 0.010808178223669529, "Left": 0.7974938750267029, "Top": 0.2760707437992096}, "Polygon": [{"X": 0.7974939346313477, "Y": 0.2760707437992096}, {"X": 0.8739804625511169, "Y": 0.276070773601532}, {"X": 0.8739804029464722, "Y": 0.28687894344329834}, {"X": 0.7974938750267029, "Y": 0.28687891364097595}]}, "Id": "16f89742-1278-4901-907e-7204eb94e155", "Relationships": [{"Type": "CHILD", "Ids": ["af5bbe0f-4204-4866-9688-98cf22942a23", "68461a6f-e8ee-4242-b043-af930<PERSON><PERSON><PERSON>e"]}]}, {"BlockType": "LINE", "Confidence": 99.8779296875, "Text": "HASU 505072", "Geometry": {"BoundingBox": {"Width": 0.11712640523910522, "Height": 0.01132781058549881, "Left": 0.4422950744628906, "Top": 0.32347923517227173}, "Polygon": [{"X": 0.4422951340675354, "Y": 0.32347923517227173}, {"X": 0.5594214797019958, "Y": 0.3234792649745941}, {"X": 0.5594214200973511, "Y": 0.33480703830718994}, {"X": 0.4422950744628906, "Y": 0.33480700850486755}]}, "Id": "0b4bc020-4517-40d7-9335-e294bb8c22b7", "Relationships": [{"Type": "CHILD", "Ids": ["f22e72da-a203-4f5d-aca7-cd1f225a1dc0", "c0753250-9ef9-43db-8750-30736503212b"]}]}, {"BlockType": "LINE", "Confidence": 98.36236572265625, "Text": "Load / Empty", "Geometry": {"BoundingBox": {"Width": 0.09021560847759247, "Height": 0.011911830864846706, "Left": 0.0681370198726654, "Top": 0.35532116889953613}, "Polygon": [{"X": 0.06813706457614899, "Y": 0.35532116889953613}, {"X": 0.15835262835025787, "Y": 0.3553212285041809}, {"X": 0.1583525836467743, "Y": 0.367233008146286}, {"X": 0.0681370198726654, "Y": 0.3672329783439636}]}, "Id": "6a881daa-1dcc-4bb3-924f-6f16621e4879", "Relationships": [{"Type": "CHILD", "Ids": ["1f1e98ce-312a-42ea-9b95-2d5fa99ef820", "b3362086-de0c-49bc-aa80-4b7140fa052c", "289c7681-81bd-4e4e-8f39-78c82114ad71"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Load", "Geometry": {"BoundingBox": {"Width": 0.03347822278738022, "Height": 0.009506034664809704, "Left": 0.899960458278656, "Top": 0.35537996888160706}, "Polygon": [{"X": 0.8999605774879456, "Y": 0.35537996888160706}, {"X": 0.9334387183189392, "Y": 0.35537996888160706}, {"X": 0.9334385991096497, "Y": 0.36488598585128784}, {"X": 0.899960458278656, "Y": 0.36488598585128784}]}, "Id": "555a723a-096e-411d-9069-b2b967ee9338", "Relationships": [{"Type": "CHILD", "Ids": ["f4e6b144-119a-4f43-9327-743922df4978"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "Waybill Date", "Geometry": {"BoundingBox": {"Width": 0.09054514765739441, "Height": 0.011959458701312542, "Left": 0.0666683167219162, "Top": 0.3854420781135559}, "Polygon": [{"X": 0.06666836142539978, "Y": 0.3854420781135559}, {"X": 0.1572134643793106, "Y": 0.3854421377182007}, {"X": 0.15721341967582703, "Y": 0.3974015414714813}, {"X": 0.0666683167219162, "Y": 0.39740148186683655}]}, "Id": "b8d18b2d-ed23-411c-a9c9-cac9e7255d24", "Relationships": [{"Type": "CHILD", "Ids": ["d4d9a172-a27a-42f8-88bb-b55278da20aa", "7e045a3b-fd6b-423d-9032-69d059280975"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/20/25", "Geometry": {"BoundingBox": {"Width": 0.0679243952035904, "Height": 0.01076925452798605, "Left": 0.8654285073280334, "Top": 0.385328471660614}, "Polygon": [{"X": 0.8654285669326782, "Y": 0.385328471660614}, {"X": 0.9333528876304626, "Y": 0.3853285014629364}, {"X": 0.9333527684211731, "Y": 0.39609771966934204}, {"X": 0.8654285073280334, "Y": 0.39609768986701965}]}, "Id": "00b27d36-05a1-43fe-bea3-9f65f1e976f1", "Relationships": [{"Type": "CHILD", "Ids": ["2703cdd2-5e76-494a-abfe-1e173b286428"]}]}, {"BlockType": "LINE", "Confidence": 99.64285278320312, "Text": "In-Gate Readiness", "Geometry": {"BoundingBox": {"Width": 0.12601153552532196, "Height": 0.01015146542340517, "Left": 0.06827356666326523, "Top": 0.4157252609729767}, "Polygon": [{"X": 0.06827360391616821, "Y": 0.4157252609729767}, {"X": 0.19428510963916779, "Y": 0.41572532057762146}, {"X": 0.1942850649356842, "Y": 0.4258767068386078}, {"X": 0.06827356666326523, "Y": 0.425876647233963}]}, "Id": "cee68393-47a8-492a-8159-5f3efdeaed18", "Relationships": [{"Type": "CHILD", "Ids": ["fc2b369a-fa8c-4f69-9607-7897001cb060", "542fc299-fe59-4992-8c29-5c0266ba5b41"]}]}, {"BlockType": "LINE", "Confidence": 99.87712860107422, "Text": "Equipment In-Gated", "Geometry": {"BoundingBox": {"Width": 0.14320775866508484, "Height": 0.011954067274928093, "Left": 0.7901227474212646, "Top": 0.41568708419799805}, "Polygon": [{"X": 0.7901228070259094, "Y": 0.41568708419799805}, {"X": 0.9333304762840271, "Y": 0.4156871736049652}, {"X": 0.9333304166793823, "Y": 0.4276411533355713}, {"X": 0.7901227474212646, "Y": 0.4276410639286041}]}, "Id": "aea890bc-6cf1-4c69-bf7b-7720e2f610fa", "Relationships": [{"Type": "CHILD", "Ids": ["07cd2e9c-5147-48cc-ba6b-1b09818a70b8", "a0871c1f-4ea2-4ba5-bfd8-bcb9a488bf9a"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Gate Window", "Geometry": {"BoundingBox": {"Width": 0.0965833067893982, "Height": 0.010245559737086296, "Left": 0.06750138103961945, "Top": 0.4477134644985199}, "Polygon": [{"X": 0.06750141829252243, "Y": 0.4477134644985199}, {"X": 0.16408468782901764, "Y": 0.4477135241031647}, {"X": 0.16408464312553406, "Y": 0.45795902609825134}, {"X": 0.06750138103961945, "Y": 0.4579589366912842}]}, "Id": "3383640d-91c4-4a82-a0b2-e08a7d2df6d5", "Relationships": [{"Type": "CHILD", "Ids": ["e2553674-f888-4f74-8b63-ad3722f670c5", "e35e268b-e052-458e-8aa6-60a963ed2bb0"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "N/A", "Geometry": {"BoundingBox": {"Width": 0.028114764019846916, "Height": 0.010347459465265274, "Left": 0.9058139324188232, "Top": 0.4478679299354553}, "Polygon": [{"X": 0.9058140516281128, "Y": 0.4478679299354553}, {"X": 0.9339287281036377, "Y": 0.4478679597377777}, {"X": 0.9339286088943481, "Y": 0.4582153856754303}, {"X": 0.9058139324188232, "Y": 0.4582153558731079}]}, "Id": "aea0b398-f1b3-4d41-b9a0-dad9d1e5f7b4", "Relationships": [{"Type": "CHILD", "Ids": ["320c62af-7b8f-4b44-a838-b3fb2bb409a4"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "CSX.com", "Geometry": {"BoundingBox": {"Width": 0.06645089387893677, "Height": 0.0101262042298913, "Left": 0.42832493782043457, "Top": 0.9258226156234741}, "Polygon": [{"X": 0.42832499742507935, "Y": 0.9258226156234741}, {"X": 0.49477583169937134, "Y": 0.9258227348327637}, {"X": 0.49477577209472656, "Y": 0.9359487891197205}, {"X": 0.42832493782043457, "Y": 0.9359486699104309}]}, "Id": "4cef4cd9-7926-4616-93ae-667a17230161", "Relationships": [{"Type": "CHILD", "Ids": ["e130cf13-f81a-4bfc-b222-3252c9b92a95"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Help", "Geometry": {"BoundingBox": {"Width": 0.03468989580869675, "Height": 0.012940299697220325, "Left": 0.5377088189125061, "Top": 0.9257529973983765}, "Polygon": [{"X": 0.5377089381217957, "Y": 0.9257529973983765}, {"X": 0.5723987221717834, "Y": 0.9257530570030212}, {"X": 0.5723986625671387, "Y": 0.9386932849884033}, {"X": 0.5377088189125061, "Y": 0.9386932253837585}]}, "Id": "8e80f32c-61bb-44ab-abff-1be43e0b829e", "Relationships": [{"Type": "CHILD", "Ids": ["f29e2ecb-063d-4b36-9e66-bf5f274577cb"]}]}, {"BlockType": "LINE", "Confidence": 80.5516586303711, "Text": "https://next.shipcsx.com/#/shipment/lookup/results", "Geometry": {"BoundingBox": {"Width": 0.291151762008667, "Height": 0.010319159366190434, "Left": 0.03901178762316704, "Top": 0.9709151983261108}, "Polygon": [{"X": 0.039011821150779724, "Y": 0.9709151983261108}, {"X": 0.33016353845596313, "Y": 0.9709158539772034}, {"X": 0.33016347885131836, "Y": 0.9812343716621399}, {"X": 0.03901178762316704, "Y": 0.9812336564064026}]}, "Id": "9ae56429-323e-4563-93f6-22a0eb9ea0b0", "Relationships": [{"Type": "CHILD", "Ids": ["a509739b-debe-44b3-aab9-3b9a7e200c66"]}]}, {"BlockType": "LINE", "Confidence": 99.55815887451172, "Text": "1/2", "Geometry": {"BoundingBox": {"Width": 0.017750058323144913, "Height": 0.008312360383570194, "Left": 0.9430518746376038, "Top": 0.9714582562446594}, "Polygon": [{"X": 0.9430519342422485, "Y": 0.9714582562446594}, {"X": 0.9608019590377808, "Y": 0.9714583158493042}, {"X": 0.9608018398284912, "Y": 0.9797706604003906}, {"X": 0.9430518746376038, "Y": 0.9797706007957458}]}, "Id": "40e79aba-be6f-4b0e-bac2-cddcc671d775", "Relationships": [{"Type": "CHILD", "Ids": ["89fc33c4-ef58-4ea9-bfad-752f9ee7ecb2"]}]}, {"BlockType": "WORD", "Confidence": 98.63739776611328, "Text": "8/22/25,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04647859185934067, "Height": 0.009373999200761318, "Left": 0.03925308585166931, "Top": 0.020810380578041077}, "Polygon": [{"X": 0.0392531156539917, "Y": 0.020810408517718315}, {"X": 0.08573167771100998, "Y": 0.020810380578041077}, {"X": 0.085731640458107, "Y": 0.03018435277044773}, {"X": 0.03925308585166931, "Y": 0.03018438071012497}]}, "Id": "0bd2fc66-e7f0-4800-a104-374fe47d244c"}, {"BlockType": "WORD", "Confidence": 99.59881591796875, "Text": "4:25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025932729244232178, "Height": 0.008374422788619995, "Left": 0.08952663093805313, "Top": 0.020789118483662605}, "Polygon": [{"X": 0.08952666074037552, "Y": 0.02078913524746895}, {"X": 0.11545936018228531, "Y": 0.020789118483662605}, {"X": 0.11545933037996292, "Y": 0.029163526371121407}, {"X": 0.08952663093805313, "Y": 0.0291635412722826}]}, "Id": "88ef6a47-eaab-4877-be59-8dcd4843aa08"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0188374612480402, "Height": 0.00828541163355112, "Left": 0.11943301558494568, "Top": 0.020746659487485886}, "Polygon": [{"X": 0.11943304538726807, "Y": 0.02074667066335678}, {"X": 0.13827048242092133, "Y": 0.020746659487485886}, {"X": 0.13827043771743774, "Y": 0.029032059013843536}, {"X": 0.11943301558494568, "Y": 0.029032070189714432}]}, "Id": "096937bc-8c84-48da-8159-2b59b6abb26e"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "ShipCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053457677364349365, "Height": 0.010314098559319973, "Left": 0.5477864742279053, "Top": 0.02053697779774666}, "Polygon": [{"X": 0.54778653383255, "Y": 0.020537011325359344}, {"X": 0.6012441515922546, "Y": 0.02053697779774666}, {"X": 0.6012440919876099, "Y": 0.03085104562342167}, {"X": 0.5477864742279053, "Y": 0.030851077288389206}]}, "Id": "87bd40eb-c6ce-42dd-8eeb-e9a49fb8b908"}, {"BlockType": "WORD", "Confidence": 93.3155288696289, "Text": "ShipCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19251520931720734, "Height": 0.031673774123191833, "Left": 0.06585811823606491, "Top": 0.051732417196035385}, "Polygon": [{"X": 0.06585822999477386, "Y": 0.051732517778873444}, {"X": 0.25837332010269165, "Y": 0.051732417196035385}, {"X": 0.2583731710910797, "Y": 0.08340610563755035}, {"X": 0.06585811823606491, "Y": 0.08340618759393692}]}, "Id": "6d056e84-5564-42df-8653-0f8da6137a37"}, {"BlockType": "WORD", "Confidence": 64.45252990722656, "Text": "############", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05844074860215187, "Height": 0.005455313716083765, "Left": 0.06702394783496857, "Top": 0.0801699161529541}, "Polygon": [{"X": 0.06702396273612976, "Y": 0.08016993850469589}, {"X": 0.12546469271183014, "Y": 0.0801699161529541}, {"X": 0.12546467781066895, "Y": 0.08562520146369934}, {"X": 0.06702394783496857, "Y": 0.08562523126602173}]}, "Id": "c40085cf-330d-4de8-a5a5-e8c4fb1c3a3b"}, {"BlockType": "WORD", "Confidence": 38.80580520629883, "Text": "III.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025815652683377266, "Height": 0.00567787978798151, "Left": 0.13529612123966217, "Top": 0.08000144362449646}, "Polygon": [{"X": 0.13529615104198456, "Y": 0.08000145852565765}, {"X": 0.16111178696155548, "Y": 0.08000144362449646}, {"X": 0.1611117571592331, "Y": 0.0856793150305748}, {"X": 0.13529612123966217, "Y": 0.0856793224811554}]}, "Id": "a2ce9100-be99-4401-ae08-aefac0c2c036"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03944868966937065, "Height": 0.012749997898936272, "Left": 0.5085984468460083, "Top": 0.06490631401538849}, "Polygon": [{"X": 0.5085985064506531, "Y": 0.06490633636713028}, {"X": 0.5480471253395081, "Y": 0.06490631401538849}, {"X": 0.5480470061302185, "Y": 0.07765629887580872}, {"X": 0.5085984468460083, "Y": 0.07765631377696991}]}, "Id": "a82407f6-bfc4-4193-99d4-a8c2f7254628"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01547942403703928, "Height": 0.00947629101574421, "Left": 0.5521012544631958, "Top": 0.06580178439617157}, "Polygon": [{"X": 0.5521013140678406, "Y": 0.06580179184675217}, {"X": 0.5675806999206543, "Y": 0.06580178439617157}, {"X": 0.5675806403160095, "Y": 0.07527806609869003}, {"X": 0.5521012544631958, "Y": 0.07527807354927063}]}, "Id": "0496045e-7b79-4178-8907-649b5c45c447"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "ShipCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06451187282800674, "Height": 0.01285382267087698, "Left": 0.5712677240371704, "Top": 0.0646568238735199}, "Polygon": [{"X": 0.57126784324646, "Y": 0.06465685367584229}, {"X": 0.635779619216919, "Y": 0.0646568238735199}, {"X": 0.6357795000076294, "Y": 0.07751061767339706}, {"X": 0.5712677240371704, "Y": 0.07751064747571945}]}, "Id": "a6b61cbd-207d-412c-b8c0-03d1e1d4b42f"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "INTERMODAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11169197410345078, "Height": 0.010523735545575619, "Left": 0.06729956716299057, "Top": 0.12056335061788559}, "Polygon": [{"X": 0.06729960441589355, "Y": 0.12056338787078857}, {"X": 0.17899154126644135, "Y": 0.12056335061788559}, {"X": 0.17899149656295776, "Y": 0.1310870498418808}, {"X": 0.06729956716299057, "Y": 0.13108707964420319}]}, "Id": "17f9bf74-0611-4a42-8cd9-6951fdf9216e"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "CARLOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07976388186216354, "Height": 0.01023051980882883, "Left": 0.212533637881279, "Top": 0.12074360251426697}, "Polygon": [{"X": 0.21253368258476257, "Y": 0.12074362486600876}, {"X": 0.29229751229286194, "Y": 0.12074360251426697}, {"X": 0.29229745268821716, "Y": 0.13097409904003143}, {"X": 0.212533637881279, "Y": 0.13097412884235382}]}, "Id": "42f53452-5256-46db-b947-33c04e3f39d0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "New", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03458040952682495, "Height": 0.010393439792096615, "Left": 0.7371698617935181, "Top": 0.12070293724536896}, "Polygon": [{"X": 0.7371699213981628, "Y": 0.12070295214653015}, {"X": 0.771750271320343, "Y": 0.12070293724536896}, {"X": 0.7717502117156982, "Y": 0.13109637796878815}, {"X": 0.7371698617935181, "Y": 0.13109637796878815}]}, "Id": "cc196a39-2b8a-4bda-9484-08f1298f0b71"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Customer?", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08059045672416687, "Height": 0.010598246939480305, "Left": 0.7751535177230835, "Top": 0.12056908011436462}, "Polygon": [{"X": 0.7751535773277283, "Y": 0.12056910246610641}, {"X": 0.855743944644928, "Y": 0.12056908011436462}, {"X": 0.8557438850402832, "Y": 0.13116730749607086}, {"X": 0.7751535177230835, "Y": 0.13116732239723206}]}, "Id": "ab1870c8-4696-4c32-89c9-ed0daaf089f9"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Start", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0376124233007431, "Height": 0.010173989459872246, "Left": 0.8594823479652405, "Top": 0.12083130329847336}, "Polygon": [{"X": 0.85948246717453, "Y": 0.12083131074905396}, {"X": 0.8970947861671448, "Y": 0.12083130329847336}, {"X": 0.8970946669578552, "Y": 0.13100528717041016}, {"X": 0.8594823479652405, "Y": 0.13100528717041016}]}, "Id": "d3113927-20bf-4b1d-8478-1fa4ad097c75"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "here", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0338725745677948, "Height": 0.010347886942327023, "Left": 0.9012395739555359, "Top": 0.12062808871269226}, "Polygon": [{"X": 0.9012396335601807, "Y": 0.12062809616327286}, {"X": 0.9351121187210083, "Y": 0.12062808871269226}, {"X": 0.9351120591163635, "Y": 0.13097596168518066}, {"X": 0.9012395739555359, "Y": 0.13097597658634186}]}, "Id": "619c5a4b-8f2d-40ee-9632-ddfb1d19aa67"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09385927021503448, "Height": 0.016521621495485306, "Left": 0.0663376972079277, "Top": 0.17545031011104584}, "Polygon": [{"X": 0.06633775681257248, "Y": 0.17545032501220703}, {"X": 0.1601969599723816, "Y": 0.17545031011104584}, {"X": 0.16019690036773682, "Y": 0.19197192788124084}, {"X": 0.0663376972079277, "Y": 0.19197192788124084}]}, "Id": "db23f0d3-fae4-417a-9cdc-068635bcdf93"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Lookup", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07226472347974777, "Height": 0.01632762886583805, "Left": 0.16555270552635193, "Top": 0.1756550371646881}, "Polygon": [{"X": 0.1655527800321579, "Y": 0.1756550520658493}, {"X": 0.2378174364566803, "Y": 0.1756550371646881}, {"X": 0.23781736195087433, "Y": 0.19198265671730042}, {"X": 0.16555270552635193, "Y": 0.1919826716184616}]}, "Id": "d5c3adee-09f1-44d7-ab9b-424d55a1fb5a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Results", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07127586007118225, "Height": 0.013488233089447021, "Left": 0.243188738822937, "Top": 0.17565244436264038}, "Polygon": [{"X": 0.2431887984275818, "Y": 0.17565244436264038}, {"X": 0.31446459889411926, "Y": 0.17565244436264038}, {"X": 0.3144645392894745, "Y": 0.1891406625509262}, {"X": 0.243188738822937, "Y": 0.1891406774520874}]}, "Id": "f7cc625a-efb2-44ab-9160-61b26b6bd98e"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "As", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017303358763456345, "Height": 0.00903573352843523, "Left": 0.3312145173549652, "Top": 0.18000130355358124}, "Polygon": [{"X": 0.3312145471572876, "Y": 0.18000130355358124}, {"X": 0.34851786494255066, "Y": 0.18000130355358124}, {"X": 0.34851783514022827, "Y": 0.189037024974823}, {"X": 0.3312145173549652, "Y": 0.1890370398759842}]}, "Id": "0dd61fe5-776f-4ec3-9209-ff2e7cd44f35"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015243617817759514, "Height": 0.009469983167946339, "Left": 0.35117870569229126, "Top": 0.17966313660144806}, "Polygon": [{"X": 0.35117876529693604, "Y": 0.17966313660144806}, {"X": 0.3664223253726959, "Y": 0.17966313660144806}, {"X": 0.36642226576805115, "Y": 0.18913310766220093}, {"X": 0.35117870569229126, "Y": 0.18913310766220093}]}, "Id": "0c6429d5-23bb-4093-886d-01b250137cab"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "16:20", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03760083019733429, "Height": 0.009254093281924725, "Left": 0.3683059811592102, "Top": 0.1798340529203415}, "Polygon": [{"X": 0.368306040763855, "Y": 0.17983406782150269}, {"X": 0.4059067964553833, "Y": 0.1798340529203415}, {"X": 0.4059067666530609, "Y": 0.1890881508588791}, {"X": 0.3683059811592102, "Y": 0.1890881508588791}]}, "Id": "348716a9-0add-4d78-af10-bb8763e6b916"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016561632975935936, "Height": 0.006885145325213671, "Left": 0.40785345435142517, "Top": 0.18206609785556793}, "Polygon": [{"X": 0.40785351395606995, "Y": 0.18206609785556793}, {"X": 0.42441511154174805, "Y": 0.18206609785556793}, {"X": 0.42441505193710327, "Y": 0.18895123898983002}, {"X": 0.40785345435142517, "Y": 0.18895123898983002}]}, "Id": "a957a737-7fe8-424d-896d-685968f07281"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "08/22/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062342461198568344, "Height": 0.009273025207221508, "Left": 0.42725151777267456, "Top": 0.17983824014663696}, "Polygon": [{"X": 0.42725157737731934, "Y": 0.17983825504779816}, {"X": 0.4895939826965332, "Y": 0.17983824014663696}, {"X": 0.4895939230918884, "Y": 0.18911126255989075}, {"X": 0.42725151777267456, "Y": 0.18911126255989075}]}, "Id": "b26f8cf7-1f92-456b-b7ae-7300e9d58d96"}, {"BlockType": "WORD", "Confidence": 99.59243774414062, "Text": "Modify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052877429872751236, "Height": 0.012897233478724957, "Left": 0.8269309997558594, "Top": 0.17731256783008575}, "Polygon": [{"X": 0.8269310593605042, "Y": 0.17731258273124695}, {"X": 0.8798084259033203, "Y": 0.17731256783008575}, {"X": 0.8798083066940308, "Y": 0.1902097910642624}, {"X": 0.8269309997558594, "Y": 0.19020980596542358}]}, "Id": "a3045a97-bbdd-4a02-a26a-8dce54b0551a"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Search", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05062456056475639, "Height": 0.010786633938550949, "Left": 0.8836808800697327, "Top": 0.17745600640773773}, "Polygon": [{"X": 0.8836809992790222, "Y": 0.17745600640773773}, {"X": 0.9343054294586182, "Y": 0.17745600640773773}, {"X": 0.9343053698539734, "Y": 0.18824262917041779}, {"X": 0.8836808800697327, "Y": 0.18824264407157898}]}, "Id": "c3e4c91b-c2d1-45c1-9d59-ab45c3a06edf"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0684114545583725, "Height": 0.014874705113470554, "Left": 0.06531623750925064, "Top": 0.23167023062705994}, "Polygon": [{"X": 0.06531628966331482, "Y": 0.23167023062705994}, {"X": 0.13372769951820374, "Y": 0.23167023062705994}, {"X": 0.13372763991355896, "Y": 0.2465449422597885}, {"X": 0.06531623750925064, "Y": 0.24654492735862732}]}, "Id": "9a7bf731-9e33-4d52-8687-15a0264691ea"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "59th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039604268968105316, "Height": 0.01213853433728218, "Left": 0.13897067308425903, "Top": 0.231786847114563}, "Polygon": [{"X": 0.1389707326889038, "Y": 0.231786847114563}, {"X": 0.17857494950294495, "Y": 0.231786847114563}, {"X": 0.17857488989830017, "Y": 0.24392537772655487}, {"X": 0.13897067308425903, "Y": 0.24392537772655487}]}, "Id": "e4dd4b79-ef1d-4267-a009-31a63fbecd7a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01715724729001522, "Height": 0.011908924207091331, "Left": 0.18326719105243683, "Top": 0.23196986317634583}, "Polygon": [{"X": 0.1832672357559204, "Y": 0.23196986317634583}, {"X": 0.2004244327545166, "Y": 0.23196986317634583}, {"X": 0.20042438805103302, "Y": 0.2438787817955017}, {"X": 0.18326719105243683, "Y": 0.2438787817955017}]}, "Id": "2b282364-5cb6-4432-a155-45b7ad4bba93"}, {"BlockType": "WORD", "Confidence": 85.7959976196289, "Text": "<", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016306869685649872, "Height": 0.007808193564414978, "Left": 0.9105649590492249, "Top": 0.23384323716163635}, "Polygon": [{"X": 0.9105649590492249, "Y": 0.24165143072605133}, {"X": 0.9105650186538696, "Y": 0.23384323716163635}, {"X": 0.9268718361854553, "Y": 0.23384323716163635}, {"X": 0.9268717169761658, "Y": 0.24165143072605133}]}, "Id": "e607d37b-3766-48e6-b771-009e65c7fd0f"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "NOTIFIED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06730309873819351, "Height": 0.009062694385647774, "Left": 0.126900777220726, "Top": 0.2766331434249878}, "Polygon": [{"X": 0.1269008219242096, "Y": 0.2766331434249878}, {"X": 0.19420388340950012, "Y": 0.2766331434249878}, {"X": 0.19420383870601654, "Y": 0.2856958210468292}, {"X": 0.126900777220726, "Y": 0.2856958210468292}]}, "Id": "d5aa17b4-08ed-486a-8441-b7b05e8dac1f"}, {"BlockType": "WORD", "Confidence": 93.91521453857422, "Text": "(0)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016676226630806923, "Height": 0.010835337452590466, "Left": 0.19759805500507355, "Top": 0.27612462639808655}, "Polygon": [{"X": 0.19759809970855713, "Y": 0.27612462639808655}, {"X": 0.21427428722381592, "Y": 0.27612462639808655}, {"X": 0.21427424252033234, "Y": 0.2869599461555481}, {"X": 0.19759805500507355, "Y": 0.2869599461555481}]}, "Id": "9a83575b-d49c-40c0-8630-ab3d49aac6d8"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "ENROUTE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06728222221136093, "Height": 0.00901841465383768, "Left": 0.3543371856212616, "Top": 0.2766253650188446}, "Polygon": [{"X": 0.35433724522590637, "Y": 0.2766253650188446}, {"X": 0.4216194152832031, "Y": 0.276625394821167}, {"X": 0.42161935567855835, "Y": 0.2856437861919403}, {"X": 0.3543371856212616, "Y": 0.2856437861919403}]}, "Id": "d4ef1738-8481-4488-91e4-af6bee5cc8ed"}, {"BlockType": "WORD", "Confidence": 96.40525817871094, "Text": "(0)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016506435349583626, "Height": 0.010869475081562996, "Left": 0.4254639744758606, "Top": 0.27611371874809265}, "Polygon": [{"X": 0.42546403408050537, "Y": 0.27611371874809265}, {"X": 0.4419704079627991, "Y": 0.27611371874809265}, {"X": 0.4419703483581543, "Y": 0.2869831919670105}, {"X": 0.4254639744758606, "Y": 0.2869831919670105}]}, "Id": "159533a8-87e2-4346-bd93-4a2354357436"}, {"BlockType": "WORD", "Confidence": 99.22772216796875, "Text": "IN-GATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057638004422187805, "Height": 0.009276400320231915, "Left": 0.580407977104187, "Top": 0.2764384150505066}, "Polygon": [{"X": 0.5804080367088318, "Y": 0.2764384150505066}, {"X": 0.6380459666252136, "Y": 0.2764384150505066}, {"X": 0.6380459070205688, "Y": 0.2857148051261902}, {"X": 0.580407977104187, "Y": 0.2857148051261902}]}, "Id": "a4f3a5e8-2a34-4339-833d-3b879bd9e384"}, {"BlockType": "WORD", "Confidence": 99.375, "Text": "(1)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016842128708958626, "Height": 0.011174964718520641, "Left": 0.6412495374679565, "Top": 0.2758520543575287}, "Polygon": [{"X": 0.6412495970726013, "Y": 0.2758520543575287}, {"X": 0.65809166431427, "Y": 0.2758520841598511}, {"X": 0.6580916047096252, "Y": 0.2870270311832428}, {"X": 0.6412495374679565, "Y": 0.2870270311832428}]}, "Id": "09d02474-6a61-4591-8b46-3493632d8201"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "OTHERS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05664097145199776, "Height": 0.009093218483030796, "Left": 0.7974938750267029, "Top": 0.27660784125328064}, "Polygon": [{"X": 0.7974939346313477, "Y": 0.27660784125328064}, {"X": 0.8541348576545715, "Y": 0.27660784125328064}, {"X": 0.854134738445282, "Y": 0.28570106625556946}, {"X": 0.7974938750267029, "Y": 0.28570103645324707}]}, "Id": "af5bbe0f-4204-4866-9688-98cf22942a23"}, {"BlockType": "WORD", "Confidence": 90.74757385253906, "Text": "(0)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01640961691737175, "Height": 0.010808168910443783, "Left": 0.8575708866119385, "Top": 0.276070773601532}, "Polygon": [{"X": 0.8575709462165833, "Y": 0.276070773601532}, {"X": 0.8739804625511169, "Y": 0.276070773601532}, {"X": 0.8739804029464722, "Y": 0.28687894344329834}, {"X": 0.8575708866119385, "Y": 0.28687894344329834}]}, "Id": "68461a6f-e8ee-4242-b043-af930<PERSON><PERSON><PERSON>e"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "HASU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04875635355710983, "Height": 0.011119241826236248, "Left": 0.4422950744628906, "Top": 0.32347923517227173}, "Polygon": [{"X": 0.4422951340675354, "Y": 0.32347923517227173}, {"X": 0.49105143547058105, "Y": 0.32347923517227173}, {"X": 0.4910513758659363, "Y": 0.33459848165512085}, {"X": 0.4422950744628906, "Y": 0.33459845185279846}]}, "Id": "f22e72da-a203-4f5d-aca7-cd1f225a1dc0"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "505072", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06384257972240448, "Height": 0.011254568584263325, "Left": 0.49557891488075256, "Top": 0.3235524594783783}, "Polygon": [{"X": 0.49557897448539734, "Y": 0.3235524594783783}, {"X": 0.5594214797019958, "Y": 0.3235524892807007}, {"X": 0.5594214200973511, "Y": 0.33480703830718994}, {"X": 0.49557891488075256, "Y": 0.33480700850486755}]}, "Id": "c0753250-9ef9-43db-8750-30736503212b"}, {"BlockType": "WORD", "Confidence": 98.310546875, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032550472766160965, "Height": 0.009635529480874538, "Left": 0.068137027323246, "Top": 0.35532116889953613}, "Polygon": [{"X": 0.06813706457614899, "Y": 0.35532116889953613}, {"X": 0.10068750381469727, "Y": 0.3553211987018585}, {"X": 0.10068746656179428, "Y": 0.36495670676231384}, {"X": 0.068137027323246, "Y": 0.36495670676231384}]}, "Id": "1f1e98ce-312a-42ea-9b95-2d5fa99ef820"}, {"BlockType": "WORD", "Confidence": 97.80193328857422, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006441786419600248, "Height": 0.010217257775366306, "Left": 0.10387995839118958, "Top": 0.35532450675964355}, "Polygon": [{"X": 0.10387999564409256, "Y": 0.35532450675964355}, {"X": 0.11032174527645111, "Y": 0.35532450675964355}, {"X": 0.11032170802354813, "Y": 0.3655417561531067}, {"X": 0.10387995839118958, "Y": 0.3655417561531067}]}, "Id": "b3362086-de0c-49bc-aa80-4b7140fa052c"}, {"BlockType": "WORD", "Confidence": 98.974609375, "Text": "Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044416967779397964, "Height": 0.011723476462066174, "Left": 0.11393566429615021, "Top": 0.35550954937934875}, "Polygon": [{"X": 0.11393570899963379, "Y": 0.35550954937934875}, {"X": 0.15835262835025787, "Y": 0.35550954937934875}, {"X": 0.1583525836467743, "Y": 0.367233008146286}, {"X": 0.11393566429615021, "Y": 0.367233008146286}]}, "Id": "289c7681-81bd-4e4e-8f39-78c82114ad71"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03347822278738022, "Height": 0.009506034664809704, "Left": 0.899960458278656, "Top": 0.35537996888160706}, "Polygon": [{"X": 0.8999605774879456, "Y": 0.35537996888160706}, {"X": 0.9334387183189392, "Y": 0.35537996888160706}, {"X": 0.9334385991096497, "Y": 0.36488598585128784}, {"X": 0.899960458278656, "Y": 0.36488598585128784}]}, "Id": "f4e6b144-119a-4f43-9327-743922df4978"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Waybill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05286629498004913, "Height": 0.0119594382122159, "Left": 0.0666683167219162, "Top": 0.3854420781135559}, "Polygon": [{"X": 0.06666836142539978, "Y": 0.3854420781135559}, {"X": 0.11953461170196533, "Y": 0.3854421079158783}, {"X": 0.11953456699848175, "Y": 0.39740151166915894}, {"X": 0.0666683167219162, "Y": 0.39740148186683655}]}, "Id": "d4d9a172-a27a-42f8-88bb-b55278da20aa"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0332687646150589, "Height": 0.009637485258281231, "Left": 0.12394469976425171, "Top": 0.3856765329837799}, "Polygon": [{"X": 0.1239447370171547, "Y": 0.3856765329837799}, {"X": 0.1572134643793106, "Y": 0.3856765329837799}, {"X": 0.15721341967582703, "Y": 0.3953140079975128}, {"X": 0.12394469976425171, "Y": 0.39531397819519043}]}, "Id": "7e045a3b-fd6b-423d-9032-69d059280975"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/20/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0679243952035904, "Height": 0.01076925452798605, "Left": 0.8654285073280334, "Top": 0.385328471660614}, "Polygon": [{"X": 0.8654285669326782, "Y": 0.385328471660614}, {"X": 0.9333528876304626, "Y": 0.3853285014629364}, {"X": 0.9333527684211731, "Y": 0.39609771966934204}, {"X": 0.8654285073280334, "Y": 0.39609768986701965}]}, "Id": "2703cdd2-5e76-494a-abfe-1e173b286428"}, {"BlockType": "WORD", "Confidence": 99.31501007080078, "Text": "In-Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05215134844183922, "Height": 0.010091382078826427, "Left": 0.06827356666326523, "Top": 0.4157852828502655}, "Polygon": [{"X": 0.06827360391616821, "Y": 0.4157852828502655}, {"X": 0.12042491137981415, "Y": 0.4157853126525879}, {"X": 0.12042487412691116, "Y": 0.4258766770362854}, {"X": 0.06827356666326523, "Y": 0.425876647233963}]}, "Id": "fc2b369a-fa8c-4f69-9607-7897001cb060"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Readiness", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06946374475955963, "Height": 0.010045927949249744, "Left": 0.12482136487960815, "Top": 0.4157252907752991}, "Polygon": [{"X": 0.12482140213251114, "Y": 0.4157252907752991}, {"X": 0.19428510963916779, "Y": 0.41572532057762146}, {"X": 0.1942850649356842, "Y": 0.42577120661735535}, {"X": 0.12482136487960815, "Y": 0.42577117681503296}]}, "Id": "542fc299-fe59-4992-8c29-5c0266ba5b41"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07767278701066971, "Height": 0.011695720255374908, "Left": 0.7901227474212646, "Top": 0.4159453809261322}, "Polygon": [{"X": 0.7901228070259094, "Y": 0.4159453809261322}, {"X": 0.8677955269813538, "Y": 0.415945440530777}, {"X": 0.8677954077720642, "Y": 0.4276411235332489}, {"X": 0.7901227474212646, "Y": 0.4276410639286041}]}, "Id": "07cd2e9c-5147-48cc-ba6b-1b09818a70b8"}, {"BlockType": "WORD", "Confidence": 99.75426483154297, "Text": "In-Gated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06242068111896515, "Height": 0.010149171575903893, "Left": 0.8709098100662231, "Top": 0.4156871438026428}, "Polygon": [{"X": 0.8709098696708679, "Y": 0.4156871438026428}, {"X": 0.9333304762840271, "Y": 0.4156871736049652}, {"X": 0.9333304166793823, "Y": 0.4258362948894501}, {"X": 0.8709098100662231, "Y": 0.4258362650871277}]}, "Id": "a0871c1f-4ea2-4ba5-bfd8-bcb9a488bf9a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03372543305158615, "Height": 0.009969339706003666, "Left": 0.06750138103961945, "Top": 0.44798964262008667}, "Polygon": [{"X": 0.06750141829252243, "Y": 0.44798964262008667}, {"X": 0.1012268140912056, "Y": 0.44798964262008667}, {"X": 0.10122677683830261, "Y": 0.45795896649360657}, {"X": 0.06750138103961945, "Y": 0.4579589366912842}]}, "Id": "e2553674-f888-4f74-8b63-ad3722f670c5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Window", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059550244361162186, "Height": 0.010109380818903446, "Left": 0.10453444719314575, "Top": 0.4477134943008423}, "Polygon": [{"X": 0.10453448444604874, "Y": 0.4477134943008423}, {"X": 0.16408468782901764, "Y": 0.4477135241031647}, {"X": 0.16408464312553406, "Y": 0.45782285928726196}, {"X": 0.10453444719314575, "Y": 0.4578228294849396}]}, "Id": "e35e268b-e052-458e-8aa6-60a963ed2bb0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "N/A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028114764019846916, "Height": 0.010347459465265274, "Left": 0.9058139324188232, "Top": 0.4478679299354553}, "Polygon": [{"X": 0.9058140516281128, "Y": 0.4478679299354553}, {"X": 0.9339287281036377, "Y": 0.4478679597377777}, {"X": 0.9339286088943481, "Y": 0.4582153856754303}, {"X": 0.9058139324188232, "Y": 0.4582153558731079}]}, "Id": "320c62af-7b8f-4b44-a838-b3fb2bb409a4"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CSX.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06645089387893677, "Height": 0.0101262042298913, "Left": 0.42832493782043457, "Top": 0.9258226156234741}, "Polygon": [{"X": 0.42832499742507935, "Y": 0.9258226156234741}, {"X": 0.49477583169937134, "Y": 0.9258227348327637}, {"X": 0.49477577209472656, "Y": 0.9359487891197205}, {"X": 0.42832493782043457, "Y": 0.9359486699104309}]}, "Id": "e130cf13-f81a-4bfc-b222-3252c9b92a95"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Help", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03468989580869675, "Height": 0.012940299697220325, "Left": 0.5377088189125061, "Top": 0.9257529973983765}, "Polygon": [{"X": 0.5377089381217957, "Y": 0.9257529973983765}, {"X": 0.5723987221717834, "Y": 0.9257530570030212}, {"X": 0.5723986625671387, "Y": 0.9386932849884033}, {"X": 0.5377088189125061, "Y": 0.9386932253837585}]}, "Id": "f29e2ecb-063d-4b36-9e66-bf5f274577cb"}, {"BlockType": "WORD", "Confidence": 80.5516586303711, "Text": "https://next.shipcsx.com/#/shipment/lookup/results", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.291151762008667, "Height": 0.010319159366190434, "Left": 0.03901178762316704, "Top": 0.9709151983261108}, "Polygon": [{"X": 0.039011821150779724, "Y": 0.9709151983261108}, {"X": 0.33016353845596313, "Y": 0.9709158539772034}, {"X": 0.33016347885131836, "Y": 0.9812343716621399}, {"X": 0.03901178762316704, "Y": 0.9812336564064026}]}, "Id": "a509739b-debe-44b3-aab9-3b9a7e200c66"}, {"BlockType": "WORD", "Confidence": 99.55815887451172, "Text": "1/2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017750058323144913, "Height": 0.008312360383570194, "Left": 0.9430518746376038, "Top": 0.9714582562446594}, "Polygon": [{"X": 0.9430519342422485, "Y": 0.9714582562446594}, {"X": 0.9608019590377808, "Y": 0.9714583158493042}, {"X": 0.9608018398284912, "Y": 0.9797706604003906}, {"X": 0.9430518746376038, "Y": 0.9797706007957458}]}, "Id": "89fc33c4-ef58-4ea9-bfad-752f9ee7ecb2"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "827db084-e936-4b3b-baa2-428ac1f2fb79", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "827db084-e936-4b3b-baa2-428ac1f2fb79", "content-type": "application/x-amz-json-1.1", "content-length": "46005", "date": "Wed, 24 Sep 2025 13:28:38 GMT"}, "RetryAttempts": 0}}, {"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.0532905037052842e-07, "Y": 0.0}, {"X": 1.0, "Y": 0.00017039099475368857}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "0d4f7016-3bf2-4c9d-8edb-abcac449a14a", "Relationships": [{"Type": "CHILD", "Ids": ["a370f813-c3fe-4837-a039-2dbfd60bd945", "f75bd46b-bdf3-44e2-b3e1-1d6ee72d38a0", "55555ad1-bdf1-4aa2-85f8-5cb12fa6bbec", "42c637ce-eb65-4530-a840-66aa3b554f19", "62b737c7-a437-444d-86c3-4557c43d549f"]}]}, {"BlockType": "LINE", "Confidence": 99.54386901855469, "Text": "8/22/25, 4:25 PM", "Geometry": {"BoundingBox": {"Width": 0.09865929186344147, "Height": 0.00924752838909626, "Left": 0.03938569501042366, "Top": 0.020886437967419624}, "Polygon": [{"X": 0.03938991576433182, "Y": 0.020886437967419624}, {"X": 0.13804498314857483, "Y": 0.020907334983348846}, {"X": 0.1380409449338913, "Y": 0.030133966356515884}, {"X": 0.03938569501042366, "Y": 0.030113285407423973}]}, "Id": "a370f813-c3fe-4837-a039-2dbfd60bd945", "Relationships": [{"Type": "CHILD", "Ids": ["be580183-a5cb-49cd-8d78-7f797b5fb99b", "a24387b0-848d-4eff-98a6-a871899403a3", "72b6ed46-ec90-4b85-a20e-1ec3eff0b8e8"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "ShipCSX", "Geometry": {"BoundingBox": {"Width": 0.05301423370838165, "Height": 0.010020153596997261, "Left": 0.5481066107749939, "Top": 0.020708218216896057}, "Polygon": [{"X": 0.5481101870536804, "Y": 0.020708218216896057}, {"X": 0.6011208295822144, "Y": 0.02071944996714592}, {"X": 0.6011173725128174, "Y": 0.030728371813893318}, {"X": 0.5481066107749939, "Y": 0.030717266723513603}]}, "Id": "f75bd46b-bdf3-44e2-b3e1-1d6ee72d38a0", "Relationships": [{"Type": "CHILD", "Ids": ["80f3a091-f70a-4ce8-8771-8cc3fc62409f"]}]}, {"BlockType": "LINE", "Confidence": 99.4212417602539, "Text": "© 2025 CSX Technology, Inc. v.4.58.0", "Geometry": {"BoundingBox": {"Width": 0.23459367454051971, "Height": 0.011001206934452057, "Left": 0.3833168148994446, "Top": 0.0409192331135273}, "Polygon": [{"X": 0.383321076631546, "Y": 0.0409192331135273}, {"X": 0.6179105043411255, "Y": 0.04096781089901924}, {"X": 0.6179066896438599, "Y": 0.051920440047979355}, {"X": 0.3833168148994446, "Y": 0.05187247321009636}]}, "Id": "55555ad1-bdf1-4aa2-85f8-5cb12fa6bbec", "Relationships": [{"Type": "CHILD", "Ids": ["cc1d5e75-0bc8-46b6-89d4-b5a8e5c1e3a9", "03811510-81a6-4014-a1d4-321a1a8b4842", "78433ad4-255b-42f8-bac8-de39c6400c32", "ae4e792c-4e31-4565-ac7a-6a66aedcfa69", "d99cc5fe-882e-45a4-ad82-0c417ba9a287", "a439d7d0-ab04-4bd2-8029-91684b644b6b"]}]}, {"BlockType": "LINE", "Confidence": 64.20758819580078, "Text": "https://next.shipcsx.com/#/shipment/lookup/results", "Geometry": {"BoundingBox": {"Width": 0.2918328046798706, "Height": 0.009638886898756027, "Left": 0.038935527205467224, "Top": 0.971490740776062}, "Polygon": [{"X": 0.0389399379491806, "Y": 0.9714948534965515}, {"X": 0.330768346786499, "Y": 0.971490740776062}, {"X": 0.3307644724845886, "Y": 0.9811248779296875}, {"X": 0.038935527205467224, "Y": 0.9811296463012695}]}, "Id": "42c637ce-eb65-4530-a840-66aa3b554f19", "Relationships": [{"Type": "CHILD", "Ids": ["577704c9-570e-4004-a33c-0e60f47d6e6d"]}]}, {"BlockType": "LINE", "Confidence": 99.20599365234375, "Text": "2/2", "Geometry": {"BoundingBox": {"Width": 0.0180229339748621, "Height": 0.008418356999754906, "Left": 0.9427365660667419, "Top": 0.9715057015419006}, "Polygon": [{"X": 0.9427388906478882, "Y": 0.9715059995651245}, {"X": 0.9607595205307007, "Y": 0.9715057015419006}, {"X": 0.9607571959495544, "Y": 0.9799237847328186}, {"X": 0.9427365660667419, "Y": 0.9799240827560425}]}, "Id": "62b737c7-a437-444d-86c3-4557c43d549f", "Relationships": [{"Type": "CHILD", "Ids": ["f4ee1845-c265-4622-a66f-7b72776187d8"]}]}, {"BlockType": "WORD", "Confidence": 98.8855209350586, "Text": "8/22/25,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0462830625474453, "Height": 0.009199700318276882, "Left": 0.03938569501042366, "Top": 0.02092328667640686}, "Polygon": [{"X": 0.039389900863170624, "Y": 0.02092328667640686}, {"X": 0.08566875755786896, "Y": 0.020933087915182114}, {"X": 0.08566463738679886, "Y": 0.030122986063361168}, {"X": 0.03938569501042366, "Y": 0.030113285407423973}]}, "Id": "be580183-a5cb-49cd-8d78-7f797b5fb99b"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "4:25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025672493502497673, "Height": 0.008177596144378185, "Left": 0.08973775804042816, "Top": 0.02089710347354412}, "Polygon": [{"X": 0.08974142372608185, "Y": 0.02089710347354412}, {"X": 0.11541025340557098, "Y": 0.020902540534734726}, {"X": 0.11540663987398148, "Y": 0.02907469868659973}, {"X": 0.08973775804042816, "Y": 0.029069311916828156}]}, "Id": "a24387b0-848d-4eff-98a6-a871899403a3"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018308179453015327, "Height": 0.007977066561579704, "Left": 0.11973680555820465, "Top": 0.020913219079375267}, "Polygon": [{"X": 0.119740329682827, "Y": 0.020913219079375267}, {"X": 0.13804498314857483, "Y": 0.020917095243930817}, {"X": 0.13804148137569427, "Y": 0.02889028564095497}, {"X": 0.11973680555820465, "Y": 0.028886443004012108}]}, "Id": "72b6ed46-ec90-4b85-a20e-1ec3eff0b8e8"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "ShipCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05301423370838165, "Height": 0.010020153596997261, "Left": 0.5481066107749939, "Top": 0.020708218216896057}, "Polygon": [{"X": 0.5481101870536804, "Y": 0.020708218216896057}, {"X": 0.6011208295822144, "Y": 0.02071944996714592}, {"X": 0.6011173725128174, "Y": 0.030728371813893318}, {"X": 0.5481066107749939, "Y": 0.030717266723513603}]}, "Id": "80f3a091-f70a-4ce8-8771-8cc3fc62409f"}, {"BlockType": "WORD", "Confidence": 97.69073486328125, "Text": "©", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011332494206726551, "Height": 0.008525182493031025, "Left": 0.3833177089691162, "Top": 0.04101433604955673}, "Polygon": [{"X": 0.38332104682922363, "Y": 0.04101433604955673}, {"X": 0.3946502208709717, "Y": 0.041016679257154465}, {"X": 0.39464691281318665, "Y": 0.04953951761126518}, {"X": 0.3833177089691162, "Y": 0.04953719303011894}]}, "Id": "cc1d5e75-0bc8-46b6-89d4-b5a8e5c1e3a9"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03319397196173668, "Height": 0.008717290125787258, "Left": 0.39794406294822693, "Top": 0.040984537452459335}, "Polygon": [{"X": 0.39794743061065674, "Y": 0.040984537452459335}, {"X": 0.4311380386352539, "Y": 0.04099141061306}, {"X": 0.4311347007751465, "Y": 0.04970182850956917}, {"X": 0.39794406294822693, "Y": 0.04969502240419388}]}, "Id": "03811510-81a6-4014-a1d4-321a1a8b4842"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026776427403092384, "Height": 0.008494473062455654, "Left": 0.43509846925735474, "Top": 0.04110545665025711}, "Polygon": [{"X": 0.4351016879081726, "Y": 0.04110545665025711}, {"X": 0.46187490224838257, "Y": 0.04111099988222122}, {"X": 0.4618717133998871, "Y": 0.04959993064403534}, {"X": 0.43509846925735474, "Y": 0.049594443291425705}]}, "Id": "78433ad4-255b-42f8-bac8-de39c6400c32"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Technology,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07589278370141983, "Height": 0.010968547314405441, "Left": 0.4641435742378235, "Top": 0.04093597084283829}, "Polygon": [{"X": 0.464147686958313, "Y": 0.04093597084283829}, {"X": 0.5400363802909851, "Y": 0.0409516841173172}, {"X": 0.5400324463844299, "Y": 0.05190451815724373}, {"X": 0.4641435742378235, "Y": 0.051889002323150635}]}, "Id": "ae4e792c-4e31-4565-ac7a-6a66aedcfa69"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020793424919247627, "Height": 0.008480670861899853, "Left": 0.5440199375152588, "Top": 0.0411558598279953}, "Polygon": [{"X": 0.5440229773521423, "Y": 0.0411558598279953}, {"X": 0.5648133754730225, "Y": 0.041160162538290024}, {"X": 0.5648103952407837, "Y": 0.04963652789592743}, {"X": 0.5440199375152588, "Y": 0.04963226616382599}]}, "Id": "d99cc5fe-882e-45a4-ad82-0c417ba9a287"}, {"BlockType": "WORD", "Confidence": 99.0344009399414, "Text": "v.4.58.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04942131042480469, "Height": 0.008709566667675972, "Left": 0.568489134311676, "Top": 0.04104037582874298}, "Polygon": [{"X": 0.5684922337532043, "Y": 0.04104037582874298}, {"X": 0.6179104447364807, "Y": 0.04105060547590256}, {"X": 0.6179074645042419, "Y": 0.049749940633773804}, {"X": 0.568489134311676, "Y": 0.049739811569452286}]}, "Id": "a439d7d0-ab04-4bd2-8029-91684b644b6b"}, {"BlockType": "WORD", "Confidence": 64.20758819580078, "Text": "https://next.shipcsx.com/#/shipment/lookup/results", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2918328046798706, "Height": 0.009638886898756027, "Left": 0.038935527205467224, "Top": 0.971490740776062}, "Polygon": [{"X": 0.0389399379491806, "Y": 0.9714948534965515}, {"X": 0.330768346786499, "Y": 0.971490740776062}, {"X": 0.3307644724845886, "Y": 0.9811248779296875}, {"X": 0.038935527205467224, "Y": 0.9811296463012695}]}, "Id": "577704c9-570e-4004-a33c-0e60f47d6e6d"}, {"BlockType": "WORD", "Confidence": 99.20599365234375, "Text": "2/2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0180229339748621, "Height": 0.008418356999754906, "Left": 0.9427365660667419, "Top": 0.9715057015419006}, "Polygon": [{"X": 0.9427388906478882, "Y": 0.9715059995651245}, {"X": 0.9607595205307007, "Y": 0.9715057015419006}, {"X": 0.9607571959495544, "Y": 0.9799237847328186}, {"X": 0.9427365660667419, "Y": 0.9799240827560425}]}, "Id": "f4ee1845-c265-4622-a66f-7b72776187d8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "6ac19f18-cc25-4f75-8203-18c9f986719d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "6ac19f18-cc25-4f75-8203-18c9f986719d", "content-type": "application/x-amz-json-1.1", "content-length": "9426", "date": "Wed, 24 Sep 2025 13:28:37 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "5e005706-e8b8-4680-9aee-2eec7de70078", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:28:42 GMT", "content-type": "application/json", "content-length": "1772", "connection": "keep-alive", "x-amzn-requestid": "5e005706-e8b8-4680-9aee-2eec7de70078"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page of the PDF. The user gave two pages with text. The text is a web page from ShipCSX showing \"Shipment Lookup Results\" with some status: IN-GATE (1). Likely this is an ingate document? The page shows \"Equipment In-Gated\" and \"Gate Window\" etc. It appears to be an in-gate record. The content is a web page that shows status of a shipment: \"IN-GATE (1)\". This looks like an ingate document. According to definitions, ingate: record of vehicle/container entering a facility, with equipment in-gated, arrival activity, etc. The page includes \"Equipment In-Gated\" and \"In-Gate Readiness\". So I'd classify as ingate.\n\nBut also the page is a \"Shipment Lookup Results\" page, not necessarily a document type? But we need to classify. There's no explicit header of \"Ingate\" though. But keywords \"Equipment In-Gated\" suggests ingate. There's no \"Outgate\". So ingate.\n\nWe have two pages: page 1 and page 2. Page 2 is just footer \"2/2\" and copyright. It is part of same document. So page 2 continues page 1. The instruction: if page starts with \"continued\", or pagination like \"page 2 of 3\". It doesn't start with \"continued\" but has \"2/2\". It is a continuation. So assign same doc_type as previous page: ingate.\n\nThus output two entries: page_no 1 ingate, page_no 2 ingate.\n\nCheck if doc_type \"ingate\" is correct. Yes.\n\nReturn JSON with documents array.\n\n"}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"ingate\"},{\"page_no\":2,\"doc_type\":\"ingate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2804, "outputTokens": 378, "totalTokens": 3182}, "metrics": {"latencyMs": 1810}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/26aa4666_IOL4A3Q1RJX8A4MOQXBR.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n8/22/25, 4:25 PM\nShipCSX\nShipCSX ############ III.\nLogin to ShipCSX\nINTERMODAL\nCARLOAD\nNew Customer? Start here\nShipment Lookup Results\nAs of 16:20 on 08/22/25\nModify Search\nChicago 59th St\n<\nNOTIFIED (0)\nENROUTE (0)\nIN-GATE (1)\nOTHERS (0)\nHASU 505072\nLoad / Empty\nLoad\nWaybill Date\n08/20/25\nIn-Gate Readiness\nEquipment In-Gated\nGate Window\nN/A\nCSX.com\nHelp\nhttps://next.shipcsx.com/#/shipment/lookup/results\n1/2\n</page1>\n\n<page2>\n8/22/25, 4:25 PM\nShipCSX\n© 2025 CSX Technology, Inc. v.4.58.0\nhttps://next.shipcsx.com/#/shipment/lookup/results\n2/2\n</page2>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}