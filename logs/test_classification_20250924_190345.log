2025-09-24 19:03:45,298 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190345.log
2025-09-24 19:03:45,298 - INFO - 📁 Found 11 files to process
2025-09-24 19:03:45,298 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:03:45,298 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-24 19:03:45,298 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-24 19:03:45,298 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-24 19:03:45,298 - INFO - ⬆️ [19:03:45] Uploading: 1_bol_1.pdf
2025-09-24 19:03:47,001 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_1.pdf -> s3://document-extraction-logistically/temp/e49e0abd_1_bol_1.pdf
2025-09-24 19:03:47,001 - INFO - 🔍 [19:03:47] Starting classification: 1_bol_1.pdf
2025-09-24 19:03:47,001 - INFO - ⬆️ [19:03:47] Uploading: 1_bol_10.pdf
2025-09-24 19:03:47,002 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:47,021 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:47,029 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e49e0abd_1_bol_1.pdf
2025-09-24 19:03:47,030 - INFO - Processing PDF from S3...
2025-09-24 19:03:47,030 - INFO - Downloading PDF from S3 to /tmp/tmp7fqik08g/e49e0abd_1_bol_1.pdf
2025-09-24 19:03:48,456 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_10.pdf -> s3://document-extraction-logistically/temp/757f9587_1_bol_10.pdf
2025-09-24 19:03:48,457 - INFO - 🔍 [19:03:48] Starting classification: 1_bol_10.pdf
2025-09-24 19:03:48,458 - INFO - ⬆️ [19:03:48] Uploading: 1_bol_11.pdf
2025-09-24 19:03:48,461 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:48,483 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:48,486 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/757f9587_1_bol_10.pdf
2025-09-24 19:03:48,487 - INFO - Processing PDF from S3...
2025-09-24 19:03:48,487 - INFO - Downloading PDF from S3 to /tmp/tmpy3ohog_3/757f9587_1_bol_10.pdf
2025-09-24 19:03:48,548 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:03:48,548 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:48,550 - INFO - Splitting PDF e49e0abd_1_bol_1 into 1 pages
2025-09-24 19:03:48,553 - INFO - Split PDF into 1 pages
2025-09-24 19:03:48,553 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:48,553 - INFO - Expected pages: [1]
2025-09-24 19:03:49,130 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_11.pdf -> s3://document-extraction-logistically/temp/c2190251_1_bol_11.pdf
2025-09-24 19:03:49,130 - INFO - 🔍 [19:03:49] Starting classification: 1_bol_11.pdf
2025-09-24 19:03:49,131 - INFO - ⬆️ [19:03:49] Uploading: 1_bol_2.pdf
2025-09-24 19:03:49,133 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:49,155 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:49,158 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c2190251_1_bol_11.pdf
2025-09-24 19:03:49,158 - INFO - Processing PDF from S3...
2025-09-24 19:03:49,159 - INFO - Downloading PDF from S3 to /tmp/tmpfq0e2r3i/c2190251_1_bol_11.pdf
2025-09-24 19:03:49,745 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_2.pdf -> s3://document-extraction-logistically/temp/b59f54f7_1_bol_2.pdf
2025-09-24 19:03:49,745 - INFO - 🔍 [19:03:49] Starting classification: 1_bol_2.pdf
2025-09-24 19:03:49,747 - INFO - ⬆️ [19:03:49] Uploading: 1_bol_3.pdf
2025-09-24 19:03:49,750 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:49,766 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:49,769 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b59f54f7_1_bol_2.pdf
2025-09-24 19:03:49,769 - INFO - Processing PDF from S3...
2025-09-24 19:03:49,769 - INFO - Downloading PDF from S3 to /tmp/tmp9sdp8ccv/b59f54f7_1_bol_2.pdf
2025-09-24 19:03:50,419 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_3.pdf -> s3://document-extraction-logistically/temp/a43e9629_1_bol_3.pdf
2025-09-24 19:03:50,420 - INFO - 🔍 [19:03:50] Starting classification: 1_bol_3.pdf
2025-09-24 19:03:50,420 - INFO - ⬆️ [19:03:50] Uploading: 1_bol_4.pdf
2025-09-24 19:03:50,421 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:50,444 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:50,448 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a43e9629_1_bol_3.pdf
2025-09-24 19:03:50,448 - INFO - Processing PDF from S3...
2025-09-24 19:03:50,449 - INFO - Downloading PDF from S3 to /tmp/tmpz6inwqao/a43e9629_1_bol_3.pdf
2025-09-24 19:03:50,922 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 19:03:50,922 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:50,922 - INFO - Splitting PDF 757f9587_1_bol_10 into 1 pages
2025-09-24 19:03:50,923 - INFO - Split PDF into 1 pages
2025-09-24 19:03:50,923 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:50,923 - INFO - Expected pages: [1]
2025-09-24 19:03:51,043 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_4.pdf -> s3://document-extraction-logistically/temp/4fcf6c48_1_bol_4.pdf
2025-09-24 19:03:51,043 - INFO - 🔍 [19:03:51] Starting classification: 1_bol_4.pdf
2025-09-24 19:03:51,044 - INFO - ⬆️ [19:03:51] Uploading: 1_bol_5.pdf
2025-09-24 19:03:51,046 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:51,063 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:51,066 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4fcf6c48_1_bol_4.pdf
2025-09-24 19:03:51,067 - INFO - Processing PDF from S3...
2025-09-24 19:03:51,067 - INFO - Downloading PDF from S3 to /tmp/tmpz7n_cywj/4fcf6c48_1_bol_4.pdf
2025-09-24 19:03:51,418 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 19:03:51,418 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:51,419 - INFO - Splitting PDF c2190251_1_bol_11 into 1 pages
2025-09-24 19:03:51,421 - INFO - Split PDF into 1 pages
2025-09-24 19:03:51,421 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:51,421 - INFO - Expected pages: [1]
2025-09-24 19:03:51,651 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_5.pdf -> s3://document-extraction-logistically/temp/e9648917_1_bol_5.pdf
2025-09-24 19:03:51,652 - INFO - 🔍 [19:03:51] Starting classification: 1_bol_5.pdf
2025-09-24 19:03:51,652 - INFO - ⬆️ [19:03:51] Uploading: 1_bol_6.pdf
2025-09-24 19:03:51,652 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:51,667 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:51,706 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e9648917_1_bol_5.pdf
2025-09-24 19:03:51,707 - INFO - Processing PDF from S3...
2025-09-24 19:03:51,707 - INFO - Downloading PDF from S3 to /tmp/tmp7ei1k0q3/e9648917_1_bol_5.pdf
2025-09-24 19:03:51,798 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:03:51,799 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:51,799 - INFO - Splitting PDF b59f54f7_1_bol_2 into 1 pages
2025-09-24 19:03:51,800 - INFO - Split PDF into 1 pages
2025-09-24 19:03:51,800 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:51,800 - INFO - Expected pages: [1]
2025-09-24 19:03:52,272 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_6.pdf -> s3://document-extraction-logistically/temp/4a63fcde_1_bol_6.pdf
2025-09-24 19:03:52,273 - INFO - 🔍 [19:03:52] Starting classification: 1_bol_6.pdf
2025-09-24 19:03:52,274 - INFO - ⬆️ [19:03:52] Uploading: 1_bol_7.pdf
2025-09-24 19:03:52,274 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:52,290 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:52,298 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4a63fcde_1_bol_6.pdf
2025-09-24 19:03:52,299 - INFO - Processing PDF from S3...
2025-09-24 19:03:52,300 - INFO - Downloading PDF from S3 to /tmp/tmp7s0oc_wj/4a63fcde_1_bol_6.pdf
2025-09-24 19:03:52,828 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 19:03:52,828 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:52,830 - INFO - Splitting PDF a43e9629_1_bol_3 into 1 pages
2025-09-24 19:03:52,834 - INFO - Split PDF into 1 pages
2025-09-24 19:03:52,834 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:52,834 - INFO - Expected pages: [1]
2025-09-24 19:03:52,918 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_7.pdf -> s3://document-extraction-logistically/temp/3b5414c5_1_bol_7.pdf
2025-09-24 19:03:52,918 - INFO - 🔍 [19:03:52] Starting classification: 1_bol_7.pdf
2025-09-24 19:03:52,919 - INFO - ⬆️ [19:03:52] Uploading: 1_bol_8.pdf
2025-09-24 19:03:52,926 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:52,937 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:52,941 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3b5414c5_1_bol_7.pdf
2025-09-24 19:03:52,941 - INFO - Processing PDF from S3...
2025-09-24 19:03:52,941 - INFO - Downloading PDF from S3 to /tmp/tmpaarjfi86/3b5414c5_1_bol_7.pdf
2025-09-24 19:03:52,950 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:03:52,950 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:52,952 - INFO - Splitting PDF 4fcf6c48_1_bol_4 into 1 pages
2025-09-24 19:03:52,955 - INFO - Split PDF into 1 pages
2025-09-24 19:03:52,955 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:52,955 - INFO - Expected pages: [1]
2025-09-24 19:03:53,530 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:03:53,530 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:53,532 - INFO - Splitting PDF e9648917_1_bol_5 into 1 pages
2025-09-24 19:03:53,534 - INFO - Split PDF into 1 pages
2025-09-24 19:03:53,535 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:53,535 - INFO - Expected pages: [1]
2025-09-24 19:03:53,543 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_8.pdf -> s3://document-extraction-logistically/temp/fecee747_1_bol_8.pdf
2025-09-24 19:03:53,543 - INFO - 🔍 [19:03:53] Starting classification: 1_bol_8.pdf
2025-09-24 19:03:53,544 - INFO - ⬆️ [19:03:53] Uploading: 1_bol_9.pdf
2025-09-24 19:03:53,552 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:53,566 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:53,568 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fecee747_1_bol_8.pdf
2025-09-24 19:03:53,569 - INFO - Processing PDF from S3...
2025-09-24 19:03:53,569 - INFO - Downloading PDF from S3 to /tmp/tmpo98j7sma/fecee747_1_bol_8.pdf
2025-09-24 19:03:54,126 - INFO - Page 1: Extracted 2681 characters, 83 lines from e49e0abd_1_bol_1_da447bce_page_001.pdf
2025-09-24 19:03:54,126 - INFO - Successfully processed page 1
2025-09-24 19:03:54,127 - INFO - Combined 1 pages into final text
2025-09-24 19:03:54,127 - INFO - Text validation for e49e0abd_1_bol_1: 2698 characters, 1 pages
2025-09-24 19:03:54,127 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:54,127 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:54,192 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_9.pdf -> s3://document-extraction-logistically/temp/8ebeb8f7_1_bol_9.pdf
2025-09-24 19:03:54,192 - INFO - 🔍 [19:03:54] Starting classification: 1_bol_9.pdf
2025-09-24 19:03:54,194 - INFO - Initializing TextractProcessor...
2025-09-24 19:03:54,209 - INFO - Initializing BedrockProcessor...
2025-09-24 19:03:54,214 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8ebeb8f7_1_bol_9.pdf
2025-09-24 19:03:54,214 - INFO - Processing PDF from S3...
2025-09-24 19:03:54,214 - INFO - Downloading PDF from S3 to /tmp/tmptujqy_r1/8ebeb8f7_1_bol_9.pdf
2025-09-24 19:03:54,660 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:03:54,660 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:54,662 - INFO - Splitting PDF 4a63fcde_1_bol_6 into 1 pages
2025-09-24 19:03:54,664 - INFO - Split PDF into 1 pages
2025-09-24 19:03:54,664 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:54,664 - INFO - Expected pages: [1]
2025-09-24 19:03:55,496 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 19:03:55,496 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:55,499 - INFO - Splitting PDF 3b5414c5_1_bol_7 into 1 pages
2025-09-24 19:03:55,503 - INFO - Split PDF into 1 pages
2025-09-24 19:03:55,503 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:55,503 - INFO - Expected pages: [1]
2025-09-24 19:03:56,155 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 19:03:56,155 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:56,156 - INFO - Splitting PDF fecee747_1_bol_8 into 1 pages
2025-09-24 19:03:56,158 - INFO - Split PDF into 1 pages
2025-09-24 19:03:56,158 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:56,158 - INFO - Expected pages: [1]
2025-09-24 19:03:56,240 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fdfae067-a581-47df-912f-6d3e66e497b7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:33:56 GMT', 'content-type': 'application/json', 'content-length': '867', 'connection': 'keep-alive', 'x-amzn-requestid': 'fdfae067-a581-47df-912f-6d3e66e497b7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page is provided: page1. We need to output JSON with documents list containing that page.\n\nWe must decide doc_type.\n\nThe page content looks like a Bill of Lading (BOL). It has "Bill Of Lading: (Short Form)". So doc_type: bol. There\'s no other conflicting type. It\'s not invoice, etc. So bol.\n\nWe need to output JSON with documents array of one object: page_no: 1, doc_type: "bol".\n\nWe need to use tool classify_logistics_doc_type. So we must call the function with the documents array.\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3334, 'outputTokens': 149, 'totalTokens': 3483}, 'metrics': {'latencyMs': 900}}
2025-09-24 19:03:56,240 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e49e0abd_1_bol_1.pdf
2025-09-24 19:03:56,285 - INFO - 

1_bol_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:03:56,285 - INFO - 

✓ Saved result: output/run1_1_bol_1.json
2025-09-24 19:03:56,446 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 19:03:56,446 - INFO - Splitting PDF into individual pages...
2025-09-24 19:03:56,447 - INFO - Splitting PDF 8ebeb8f7_1_bol_9 into 1 pages
2025-09-24 19:03:56,451 - INFO - Split PDF into 1 pages
2025-09-24 19:03:56,451 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:03:56,451 - INFO - Expected pages: [1]
2025-09-24 19:03:56,586 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e49e0abd_1_bol_1.pdf
2025-09-24 19:03:57,426 - INFO - Page 1: Extracted 2034 characters, 111 lines from 757f9587_1_bol_10_f59a53f4_page_001.pdf
2025-09-24 19:03:57,426 - INFO - Successfully processed page 1
2025-09-24 19:03:57,426 - INFO - Combined 1 pages into final text
2025-09-24 19:03:57,427 - INFO - Text validation for 757f9587_1_bol_10: 2051 characters, 1 pages
2025-09-24 19:03:57,427 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:57,427 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:58,018 - INFO - Page 1: Extracted 1081 characters, 102 lines from c2190251_1_bol_11_39b5572e_page_001.pdf
2025-09-24 19:03:58,018 - INFO - Successfully processed page 1
2025-09-24 19:03:58,018 - INFO - Combined 1 pages into final text
2025-09-24 19:03:58,018 - INFO - Text validation for c2190251_1_bol_11: 1098 characters, 1 pages
2025-09-24 19:03:58,019 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:58,019 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:58,276 - INFO - Page 1: Extracted 1008 characters, 67 lines from 4fcf6c48_1_bol_4_5e2b20df_page_001.pdf
2025-09-24 19:03:58,276 - INFO - Successfully processed page 1
2025-09-24 19:03:58,276 - INFO - Combined 1 pages into final text
2025-09-24 19:03:58,277 - INFO - Text validation for 4fcf6c48_1_bol_4: 1025 characters, 1 pages
2025-09-24 19:03:58,277 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:58,277 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:58,718 - INFO - Page 1: Extracted 1026 characters, 65 lines from e9648917_1_bol_5_b2017765_page_001.pdf
2025-09-24 19:03:58,718 - INFO - Successfully processed page 1
2025-09-24 19:03:58,718 - INFO - Combined 1 pages into final text
2025-09-24 19:03:58,719 - INFO - Text validation for e9648917_1_bol_5: 1043 characters, 1 pages
2025-09-24 19:03:58,719 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:58,719 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:58,884 - INFO - Page 1: Extracted 2981 characters, 114 lines from b59f54f7_1_bol_2_d6143d9f_page_001.pdf
2025-09-24 19:03:58,884 - INFO - Successfully processed page 1
2025-09-24 19:03:58,885 - INFO - Combined 1 pages into final text
2025-09-24 19:03:58,885 - INFO - Text validation for b59f54f7_1_bol_2: 2998 characters, 1 pages
2025-09-24 19:03:58,885 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:58,885 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:58,944 - INFO - Page 1: Extracted 3577 characters, 94 lines from a43e9629_1_bol_3_b3632dfb_page_001.pdf
2025-09-24 19:03:58,944 - INFO - Successfully processed page 1
2025-09-24 19:03:58,944 - INFO - Combined 1 pages into final text
2025-09-24 19:03:58,944 - INFO - Text validation for a43e9629_1_bol_3: 3594 characters, 1 pages
2025-09-24 19:03:58,945 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:03:58,945 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:03:59,165 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd2fd89d6-2413-41fc-a545-15d9a6a290d1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:33:58 GMT', 'content-type': 'application/json', 'content-length': '666', 'connection': 'keep-alive', 'x-amzn-requestid': 'd2fd89d6-2413-41fc-a545-15d9a6a290d1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s a "STRAIGHT BILL OF LADING" with header "STRAIGHT BILL OF LADING". So doc_type = bol. Need to output JSON with documents array with one object: page_no 1, doc_type "bol". Use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_yLy2u2h_T4KdWr5-8Kmg7Q', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3412, 'outputTokens': 84, 'totalTokens': 3496}, 'metrics': {'latencyMs': 614}}
2025-09-24 19:03:59,166 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/757f9587_1_bol_10.pdf
2025-09-24 19:03:59,199 - INFO - 

1_bol_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:03:59,199 - INFO - 

✓ Saved result: output/run1_1_bol_10.json
2025-09-24 19:03:59,513 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/757f9587_1_bol_10.pdf
2025-09-24 19:03:59,831 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '97d94ca6-baed-4bfe-a618-df3dfd4d693f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:33:59 GMT', 'content-type': 'application/json', 'content-length': '674', 'connection': 'keep-alive', 'x-amzn-requestid': '97d94ca6-baed-4bfe-a618-df3dfd4d693f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content looks like a Bill of Lading. It has "Bill of Lading" header, ship from, ship to, trailer, etc. So doc_type = "bol". There\'s no continuation. So output: {documents: [{page_no:1, doc_type:"bol"}]}. Use function.'}}}, {'toolUse': {'toolUseId': 'tooluse_ugdwhcfHSO2TnGaz_yd3xw', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3002, 'outputTokens': 92, 'totalTokens': 3094}, 'metrics': {'latencyMs': 653}}
2025-09-24 19:03:59,831 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c2190251_1_bol_11.pdf
2025-09-24 19:03:59,861 - INFO - 

1_bol_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:03:59,861 - INFO - 

✓ Saved result: output/run1_1_bol_11.json
2025-09-24 19:03:59,948 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cdfcd454-6d0d-4629-a90a-3d08b29ddb6e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:33:59 GMT', 'content-type': 'application/json', 'content-length': '481', 'connection': 'keep-alive', 'x-amzn-requestid': 'cdfcd454-6d0d-4629-a90a-3d08b29ddb6e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. This page contains "STRAIGHT BILL OF LADING" header. So doc_type = bol. No continuation. So output single object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2920, 'outputTokens': 56, 'totalTokens': 2976}, 'metrics': {'latencyMs': 477}}
2025-09-24 19:03:59,949 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4fcf6c48_1_bol_4.pdf
2025-09-24 19:04:00,161 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c2190251_1_bol_11.pdf
2025-09-24 19:04:00,195 - INFO - 

1_bol_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:00,196 - INFO - 

✓ Saved result: output/run1_1_bol_4.json
2025-09-24 19:04:00,391 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '675120b5-e8b4-4d89-a4e5-8681a0b79fe6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:00 GMT', 'content-type': 'application/json', 'content-length': '567', 'connection': 'keep-alive', 'x-amzn-requestid': '675120b5-e8b4-4d89-a4e5-8681a0b79fe6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. There is only page1 text. It\'s a Bill of Lading. The header shows "STRAIGHT BILL OF LADING". So doc_type: bol. No continuation. Output JSON with documents array.'}}}, {'text': '{\n    "documents": [\n        {\n            "page_no": 1,\n            "doc_type": "bol"\n        }\n    ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2920, 'outputTokens': 77, 'totalTokens': 2997}, 'metrics': {'latencyMs': 481}}
2025-09-24 19:04:00,391 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e9648917_1_bol_5.pdf
2025-09-24 19:04:00,477 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4fcf6c48_1_bol_4.pdf
2025-09-24 19:04:00,505 - INFO - 

1_bol_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:00,505 - INFO - 

✓ Saved result: output/run1_1_bol_5.json
2025-09-24 19:04:00,817 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e9648917_1_bol_5.pdf
2025-09-24 19:04:01,315 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f791774d-335a-4064-8e2e-dbb6529d2328', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:00 GMT', 'content-type': 'application/json', 'content-length': '556', 'connection': 'keep-alive', 'x-amzn-requestid': 'f791774d-335a-4064-8e2e-dbb6529d2328'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. It looks like a Bill of Lading. The header: "STRAIGHT BILL OF LADING - SHORT FORM". So doc_type: bol. No continuation or multi-page. So output JSON with documents array with page_no 1 bol.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3507, 'outputTokens': 77, 'totalTokens': 3584}, 'metrics': {'latencyMs': 582}}
2025-09-24 19:04:01,316 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a43e9629_1_bol_3.pdf
2025-09-24 19:04:01,361 - INFO - 

1_bol_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:01,361 - INFO - 

✓ Saved result: output/run1_1_bol_3.json
2025-09-24 19:04:01,548 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '81e35111-c764-462f-a7aa-12b7bff35b54', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:00 GMT', 'content-type': 'application/json', 'content-length': '661', 'connection': 'keep-alive', 'x-amzn-requestid': '81e35111-c764-462f-a7aa-12b7bff35b54'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have only one page. It\'s a Bill of Lading. The text contains "BILL OF LADING". So doc_type = bol. There\'s no continuation indicator. So output:\n\n{\n "documents":[{"page_no":1,"doc_type":"bol"}]\n}\n\nWe must call the tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_tfBORQ2ISHSEA5l0Kt9Nwg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3389, 'outputTokens': 82, 'totalTokens': 3471}, 'metrics': {'latencyMs': 646}}
2025-09-24 19:04:01,548 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b59f54f7_1_bol_2.pdf
2025-09-24 19:04:01,640 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a43e9629_1_bol_3.pdf
2025-09-24 19:04:01,688 - INFO - 

1_bol_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:01,688 - INFO - 

✓ Saved result: output/run1_1_bol_2.json
2025-09-24 19:04:02,017 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b59f54f7_1_bol_2.pdf
2025-09-24 19:04:13,164 - INFO - Page 1: Extracted 2964 characters, 117 lines from 4a63fcde_1_bol_6_ad01454c_page_001.pdf
2025-09-24 19:04:13,165 - INFO - Successfully processed page 1
2025-09-24 19:04:13,165 - INFO - Combined 1 pages into final text
2025-09-24 19:04:13,165 - INFO - Text validation for 4a63fcde_1_bol_6: 2981 characters, 1 pages
2025-09-24 19:04:13,165 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:04:13,165 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:04:14,098 - INFO - Page 1: Extracted 4332 characters, 118 lines from 3b5414c5_1_bol_7_197c94be_page_001.pdf
2025-09-24 19:04:14,098 - INFO - Successfully processed page 1
2025-09-24 19:04:14,098 - INFO - Combined 1 pages into final text
2025-09-24 19:04:14,098 - INFO - Text validation for 3b5414c5_1_bol_7: 4349 characters, 1 pages
2025-09-24 19:04:14,099 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:04:14,099 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:04:15,224 - INFO - Page 1: Extracted 4253 characters, 172 lines from 8ebeb8f7_1_bol_9_96b6b89b_page_001.pdf
2025-09-24 19:04:15,224 - INFO - Successfully processed page 1
2025-09-24 19:04:15,224 - INFO - Combined 1 pages into final text
2025-09-24 19:04:15,225 - INFO - Text validation for 8ebeb8f7_1_bol_9: 4270 characters, 1 pages
2025-09-24 19:04:15,225 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:04:15,225 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:04:15,249 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '00dead3e-b612-4086-8b3a-bd9821b68078', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:15 GMT', 'content-type': 'application/json', 'content-length': '455', 'connection': 'keep-alive', 'x-amzn-requestid': '00dead3e-b612-4086-8b3a-bd9821b68078'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It\'s a Bill of Lading. So doc_type should be "bol". There\'s no continuation. So output with page_no 1 and doc_type bol.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3378, 'outputTokens': 54, 'totalTokens': 3432}, 'metrics': {'latencyMs': 898}}
2025-09-24 19:04:15,249 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4a63fcde_1_bol_6.pdf
2025-09-24 19:04:15,334 - INFO - 

1_bol_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:15,334 - INFO - 

✓ Saved result: output/run1_1_bol_6.json
2025-09-24 19:04:15,849 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0c5c7775-2ffe-4fd1-bdb1-bbeedd2ca008', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:15 GMT', 'content-type': 'application/json', 'content-length': '568', 'connection': 'keep-alive', 'x-amzn-requestid': '0c5c7775-2ffe-4fd1-bdb1-bbeedd2ca008'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content is a "Straight Bill of Lading" with header "STRAIGHT BILL OF LADING-SHORT FORM-ORIGINAL-NOT NEGOTIABLE". This matches bol (Bill of Lading). So doc_type should be "bol". No continuation header. So output one page.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3624, 'outputTokens': 82, 'totalTokens': 3706}, 'metrics': {'latencyMs': 586}}
2025-09-24 19:04:15,850 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3b5414c5_1_bol_7.pdf
2025-09-24 19:04:16,225 - INFO - Page 1: Extracted 4799 characters, 118 lines from fecee747_1_bol_8_39e0e860_page_001.pdf
2025-09-24 19:04:16,226 - INFO - Successfully processed page 1
2025-09-24 19:04:16,226 - INFO - Combined 1 pages into final text
2025-09-24 19:04:16,226 - INFO - Text validation for fecee747_1_bol_8: 4816 characters, 1 pages
2025-09-24 19:04:16,226 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:04:16,227 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:04:16,375 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4a63fcde_1_bol_6.pdf
2025-09-24 19:04:16,434 - INFO - 

1_bol_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:16,434 - INFO - 

✓ Saved result: output/run1_1_bol_7.json
2025-09-24 19:04:16,723 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3b5414c5_1_bol_7.pdf
2025-09-24 19:04:17,058 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '537f47e5-b4b0-48cc-b99b-a0e87915f114', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:16 GMT', 'content-type': 'application/json', 'content-length': '550', 'connection': 'keep-alive', 'x-amzn-requestid': '537f47e5-b4b0-48cc-b99b-a0e87915f114'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It contains "Straight Bill of Lading" header. This is a Bill of Lading. According to classification, "bol" is Bill of Lading. So output page 1 as bol. The tool requires a JSON with documents array. Let\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3775, 'outputTokens': 76, 'totalTokens': 3851}, 'metrics': {'latencyMs': 551}}
2025-09-24 19:04:17,059 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8ebeb8f7_1_bol_9.pdf
2025-09-24 19:04:17,111 - INFO - 

1_bol_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:17,111 - INFO - 

✓ Saved result: output/run1_1_bol_9.json
2025-09-24 19:04:17,495 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8ebeb8f7_1_bol_9.pdf
2025-09-24 19:04:18,417 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f9edc0fc-d6ca-4da7-af0c-cc89fda4f2a8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:34:18 GMT', 'content-type': 'application/json', 'content-length': '662', 'connection': 'keep-alive', 'x-amzn-requestid': 'f9edc0fc-d6ca-4da7-af0c-cc89fda4f2a8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 content provided. Need to determine doc_type. The header says "STRAIGHT BILL OF LADING - SHORT FORM". So it\'s a Bill of Lading. According to definitions, that is bol. So doc_type "bol". Output JSON with documents array containing page 1 bol.\n\nWe need to output via tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3747, 'outputTokens': 96, 'totalTokens': 3843}, 'metrics': {'latencyMs': 837}}
2025-09-24 19:04:18,418 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fecee747_1_bol_8.pdf
2025-09-24 19:04:18,473 - INFO - 

1_bol_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:04:18,473 - INFO - 

✓ Saved result: output/run1_1_bol_8.json
2025-09-24 19:04:18,764 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fecee747_1_bol_8.pdf
2025-09-24 19:04:18,766 - INFO - 
📊 Processing Summary:
2025-09-24 19:04:18,766 - INFO -    Total files: 11
2025-09-24 19:04:18,766 - INFO -    Successful: 11
2025-09-24 19:04:18,766 - INFO -    Failed: 0
2025-09-24 19:04:18,766 - INFO -    Duration: 33.47 seconds
2025-09-24 19:04:18,766 - INFO -    Output directory: output
2025-09-24 19:04:18,766 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:04:18,766 - INFO -    📄 1_bol_1.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_10.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_11.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_3.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_5.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_7.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_8.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,767 - INFO -    📄 1_bol_9.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:04:18,768 - INFO - 
============================================================================================================================================
2025-09-24 19:04:18,768 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:04:18,768 - INFO - ============================================================================================================================================
2025-09-24 19:04:18,768 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:04:18,768 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:04:18,768 - INFO - 1_bol_1.pdf                                        1      bol                                      run1_1_bol_1.json                                                               
2025-09-24 19:04:18,768 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_1.pdf
2025-09-24 19:04:18,768 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_1.json
2025-09-24 19:04:18,768 - INFO - 
2025-09-24 19:04:18,768 - INFO - 1_bol_10.pdf                                       1      bol                                      run1_1_bol_10.json                                                              
2025-09-24 19:04:18,768 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_10.pdf
2025-09-24 19:04:18,768 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_10.json
2025-09-24 19:04:18,768 - INFO - 
2025-09-24 19:04:18,768 - INFO - 1_bol_11.pdf                                       1      bol                                      run1_1_bol_11.json                                                              
2025-09-24 19:04:18,768 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_11.pdf
2025-09-24 19:04:18,768 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_11.json
2025-09-24 19:04:18,768 - INFO - 
2025-09-24 19:04:18,768 - INFO - 1_bol_2.pdf                                        1      bol                                      run1_1_bol_2.json                                                               
2025-09-24 19:04:18,769 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_2.pdf
2025-09-24 19:04:18,769 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_2.json
2025-09-24 19:04:18,769 - INFO - 
2025-09-24 19:04:18,769 - INFO - 1_bol_3.pdf                                        1      bol                                      run1_1_bol_3.json                                                               
2025-09-24 19:04:18,769 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_3.pdf
2025-09-24 19:04:18,769 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_3.json
2025-09-24 19:04:18,769 - INFO - 
2025-09-24 19:04:18,769 - INFO - 1_bol_4.pdf                                        1      bol                                      run1_1_bol_4.json                                                               
2025-09-24 19:04:18,769 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_4.pdf
2025-09-24 19:04:18,769 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_4.json
2025-09-24 19:04:18,769 - INFO - 
2025-09-24 19:04:18,769 - INFO - 1_bol_5.pdf                                        1      bol                                      run1_1_bol_5.json                                                               
2025-09-24 19:04:18,769 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_5.pdf
2025-09-24 19:04:18,769 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_5.json
2025-09-24 19:04:18,769 - INFO - 
2025-09-24 19:04:18,769 - INFO - 1_bol_6.pdf                                        1      bol                                      run1_1_bol_6.json                                                               
2025-09-24 19:04:18,769 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_6.pdf
2025-09-24 19:04:18,769 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_6.json
2025-09-24 19:04:18,769 - INFO - 
2025-09-24 19:04:18,769 - INFO - 1_bol_7.pdf                                        1      bol                                      run1_1_bol_7.json                                                               
2025-09-24 19:04:18,769 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_7.pdf
2025-09-24 19:04:18,769 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_7.json
2025-09-24 19:04:18,769 - INFO - 
2025-09-24 19:04:18,770 - INFO - 1_bol_8.pdf                                        1      bol                                      run1_1_bol_8.json                                                               
2025-09-24 19:04:18,770 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_8.pdf
2025-09-24 19:04:18,770 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_8.json
2025-09-24 19:04:18,770 - INFO - 
2025-09-24 19:04:18,770 - INFO - 1_bol_9.pdf                                        1      bol                                      run1_1_bol_9.json                                                               
2025-09-24 19:04:18,770 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_9.pdf
2025-09-24 19:04:18,770 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_1_bol_9.json
2025-09-24 19:04:18,770 - INFO - 
2025-09-24 19:04:18,770 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:04:18,770 - INFO - Total entries: 11
2025-09-24 19:04:18,770 - INFO - ============================================================================================================================================
2025-09-24 19:04:18,770 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:04:18,770 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:04:18,770 - INFO -   1. 1_bol_1.pdf                         Page 1   → bol             | run1_1_bol_1.json
2025-09-24 19:04:18,770 - INFO -   2. 1_bol_10.pdf                        Page 1   → bol             | run1_1_bol_10.json
2025-09-24 19:04:18,770 - INFO -   3. 1_bol_11.pdf                        Page 1   → bol             | run1_1_bol_11.json
2025-09-24 19:04:18,770 - INFO -   4. 1_bol_2.pdf                         Page 1   → bol             | run1_1_bol_2.json
2025-09-24 19:04:18,770 - INFO -   5. 1_bol_3.pdf                         Page 1   → bol             | run1_1_bol_3.json
2025-09-24 19:04:18,770 - INFO -   6. 1_bol_4.pdf                         Page 1   → bol             | run1_1_bol_4.json
2025-09-24 19:04:18,770 - INFO -   7. 1_bol_5.pdf                         Page 1   → bol             | run1_1_bol_5.json
2025-09-24 19:04:18,770 - INFO -   8. 1_bol_6.pdf                         Page 1   → bol             | run1_1_bol_6.json
2025-09-24 19:04:18,770 - INFO -   9. 1_bol_7.pdf                         Page 1   → bol             | run1_1_bol_7.json
2025-09-24 19:04:18,771 - INFO -  10. 1_bol_8.pdf                         Page 1   → bol             | run1_1_bol_8.json
2025-09-24 19:04:18,771 - INFO -  11. 1_bol_9.pdf                         Page 1   → bol             | run1_1_bol_9.json
2025-09-24 19:04:18,771 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:04:18,771 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 33.467582, 'processed_files': [{'filename': '1_bol_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_1.pdf'}, {'filename': '1_bol_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_10.pdf'}, {'filename': '1_bol_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_11.pdf'}, {'filename': '1_bol_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_2.pdf'}, {'filename': '1_bol_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_3.pdf'}, {'filename': '1_bol_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_4.pdf'}, {'filename': '1_bol_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_5.pdf'}, {'filename': '1_bol_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_6.pdf'}, {'filename': '1_bol_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_7.pdf'}, {'filename': '1_bol_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_8.pdf'}, {'filename': '1_bol_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/1_bol/1_bol_9.pdf'}]}
