{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 3.019411209947975e-08, "Y": 1.0}]}, "Id": "e5d488d0-9174-4858-8fbb-7ab70753ab81", "Relationships": [{"Type": "CHILD", "Ids": ["2cc818f1-acd3-4b1c-8fca-ededf846e2b2", "f4671afb-6a61-4a8f-8dbc-8d58b9ce9db1", "de67e6ad-3651-4c52-bbe6-4d4ec69e3884", "52e0ff00-eea4-4f9e-a869-12ed570a9e35", "60fbd7d2-9b9e-4699-ad44-0c1a15b76bd4", "6552ea20-d89a-4a9b-b440-00cc020df246", "ba18e8c4-043b-4a79-8c35-ec985a7693d2", "a6108ae0-b2d3-4e94-a542-c36fa3f8a8da", "0a2ae40f-4b49-40e7-8b4c-8bebfdccd1b5", "ce25f9c7-988f-4c7b-920d-f2ec8a12fe9c", "e3ff3b60-4630-4e28-a2ad-b70adba73fa9", "42db42a0-3b87-4983-89b7-3a5a4b0ba37e", "a9c988a1-e4ef-4f14-bcc0-242bad260052", "d6316548-0994-43ea-a1b2-767e3bb85005", "92cf946b-5272-4bcb-b605-d027cf8c4dae", "0c9746ae-94d3-4724-9e79-d5c4f12ed809", "d389443a-2582-4933-b0d9-3d8dc8506b71", "e1590e3b-1d4a-4deb-9459-d8ecb2f40e13", "71fba577-f0c0-4f26-8ec4-cc24ef72fcca", "22183e5d-50df-4e7f-8a16-f64fab935650", "8616117e-a1fb-4c2d-b2fc-5d4610edc14e", "cf3c38b3-4473-4e2a-a1c3-e9c2d172a666", "aa4b2bf5-f82d-481d-ada4-70a7c1efe89c", "62b92028-b427-4ad3-80a8-ed8eeb08ba66", "f7429f6a-f040-4857-b1e7-e16cfcfd6e16", "d4b23d99-eb13-4ec1-b83e-7124142e99e6", "d36fcba8-5520-48f1-be34-11e910701f1f", "10c6f9ca-8511-454d-8c2f-ffc32ecb13a7", "b018fd85-8ba4-4b4b-90fd-866f279217ac", "50e9d659-d6d0-4fed-95b2-7badc251ecd6", "d286626c-7625-4bcf-9ade-8785a74b2992", "c1c6a66f-cabd-4889-b2aa-6285992c8c57", "a2669a1a-2f1e-4c3e-9650-0cff04b015c3", "bfe7f248-de48-47bd-8c83-57ff86f1822a", "11e5fe76-9e6d-4542-bc17-f96925400bc6"]}]}, {"BlockType": "LINE", "Confidence": 99.96717834472656, "Text": "P.A.N. Trucking LLC", "Geometry": {"BoundingBox": {"Width": 0.18159207701683044, "Height": 0.014668827876448631, "Left": 0.268857479095459, "Top": 0.12844780087471008}, "Polygon": [{"X": 0.268857479095459, "Y": 0.12844780087471008}, {"X": 0.4504494071006775, "Y": 0.12845046818256378}, {"X": 0.45044955611228943, "Y": 0.14311662316322327}, {"X": 0.26885759830474854, "Y": 0.14311400055885315}]}, "Id": "2cc818f1-acd3-4b1c-8fca-ededf846e2b2", "Relationships": [{"Type": "CHILD", "Ids": ["a51cee1f-80e4-4d61-bcd1-879f577dacbf", "8f194aa2-4f32-48bf-a89f-d69fd70ffde9", "a4149833-a1ac-424b-b978-d1bc214c7e80"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "6490 Evelyn Street", "Geometry": {"BoundingBox": {"Width": 0.14696729183197021, "Height": 0.013516505248844624, "Left": 0.2709336578845978, "Top": 0.14497771859169006}, "Polygon": [{"X": 0.2709336578845978, "Y": 0.14497771859169006}, {"X": 0.41790083050727844, "Y": 0.1449798345565796}, {"X": 0.417900949716568, "Y": 0.15849421918392181}, {"X": 0.27093374729156494, "Y": 0.15849213302135468}]}, "Id": "f4671afb-6a61-4a8f-8dbc-8d58b9ce9db1", "Relationships": [{"Type": "CHILD", "Ids": ["4acc6276-479d-45cb-814a-3d7ded2d6c94", "caaee1da-b899-45de-b9ab-fc27c437f241", "fcd53c0d-cfab-4f88-85d6-f930fb7cbafb"]}]}, {"BlockType": "LINE", "Confidence": 99.92080688476562, "Text": "Harrisburg, PA 17111", "Geometry": {"BoundingBox": {"Width": 0.16398492455482483, "Height": 0.013548914343118668, "Left": 0.27168262004852295, "Top": 0.16032426059246063}, "Polygon": [{"X": 0.27168262004852295, "Y": 0.16032426059246063}, {"X": 0.4356674253940582, "Y": 0.16032658517360687}, {"X": 0.4356675446033478, "Y": 0.173873171210289}, {"X": 0.2716827094554901, "Y": 0.17387089133262634}]}, "Id": "de67e6ad-3651-4c52-bbe6-4d4ec69e3884", "Relationships": [{"Type": "CHILD", "Ids": ["2b243f30-7cfd-4cb3-907b-25fa3efcfdb9", "cfc98b17-a993-4935-a48a-b176b30c6691", "f439e541-0179-48cf-adda-32042312948d"]}]}, {"BlockType": "LINE", "Confidence": 99.44660949707031, "Text": "Phone: (*************", "Geometry": {"BoundingBox": {"Width": 0.17534294724464417, "Height": 0.013545926660299301, "Left": 0.27289873361587524, "Top": 0.17556431889533997}, "Polygon": [{"X": 0.27289873361587524, "Y": 0.17556431889533997}, {"X": 0.44824156165122986, "Y": 0.17556674778461456}, {"X": 0.4482416808605194, "Y": 0.18911023437976837}, {"X": 0.2728988230228424, "Y": 0.18910785019397736}]}, "Id": "52e0ff00-eea4-4f9e-a869-12ed570a9e35", "Relationships": [{"Type": "CHILD", "Ids": ["e35bece4-bb93-47d9-b9c5-495743bba296", "807335fd-e5e2-40f8-bbe4-32358d95e875", "bffcd515-0614-45dd-8d66-84b949d2f104"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "INVOICE", "Geometry": {"BoundingBox": {"Width": 0.14468902349472046, "Height": 0.01951207034289837, "Left": 0.13199232518672943, "Top": 0.26982709765434265}, "Polygon": [{"X": 0.13199232518672943, "Y": 0.26982709765434265}, {"X": 0.27668121457099915, "Y": 0.2698288857936859}, {"X": 0.2766813337802887, "Y": 0.28933918476104736}, {"X": 0.1319924294948578, "Y": 0.2893374562263489}]}, "Id": "60fbd7d2-9b9e-4699-ad44-0c1a15b76bd4", "Relationships": [{"Type": "CHILD", "Ids": ["94261aa7-3d62-4e30-9fdb-dadbefd3ff7e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Bill To:", "Geometry": {"BoundingBox": {"Width": 0.05224059894680977, "Height": 0.010350522585213184, "Left": 0.1314866840839386, "Top": 0.3265421688556671}, "Polygon": [{"X": 0.1314866840839386, "Y": 0.3265421688556671}, {"X": 0.1837272197008133, "Y": 0.32654276490211487}, {"X": 0.18372727930545807, "Y": 0.336892694234848}, {"X": 0.13148674368858337, "Y": 0.33689212799072266}]}, "Id": "6552ea20-d89a-4a9b-b440-00cc020df246", "Relationships": [{"Type": "CHILD", "Ids": ["99528572-c13e-4671-9987-d14f268caa0d", "c88815c1-efa7-4b4f-b144-6d66d0bda174"]}]}, {"BlockType": "LINE", "Confidence": 99.89583587646484, "Text": "Invoice #: 919", "Geometry": {"BoundingBox": {"Width": 0.10314679145812988, "Height": 0.010406868532299995, "Left": 0.6051945090293884, "Top": 0.32602202892303467}, "Polygon": [{"X": 0.6051945090293884, "Y": 0.32602202892303467}, {"X": 0.708341121673584, "Y": 0.3260231912136078}, {"X": 0.7083412408828735, "Y": 0.3364289104938507}, {"X": 0.6051945686340332, "Y": 0.3364277482032776}]}, "Id": "ba18e8c4-043b-4a79-8c35-ec985a7693d2", "Relationships": [{"Type": "CHILD", "Ids": ["1cba96ee-fa35-42a2-be7a-d72b73f3aecb", "4b12da56-375d-46b7-8fce-6e528417aa77", "f1ff6387-9096-4d4b-8546-f006ca93bb40"]}]}, {"BlockType": "LINE", "Confidence": 97.32939910888672, "Text": "iFreight", "Geometry": {"BoundingBox": {"Width": 0.05262390524148941, "Height": 0.012564047239720821, "Left": 0.13156910240650177, "Top": 0.34086117148399353}, "Polygon": [{"X": 0.13156910240650177, "Y": 0.34086117148399353}, {"X": 0.1841929405927658, "Y": 0.3408617377281189}, {"X": 0.18419301509857178, "Y": 0.35342520475387573}, {"X": 0.13156917691230774, "Y": 0.35342463850975037}]}, "Id": "a6108ae0-b2d3-4e94-a542-c36fa3f8a8da", "Relationships": [{"Type": "CHILD", "Ids": ["776bb1f6-9772-4ce3-bbba-6dab7e3cf77e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Date: 9/19/2025", "Geometry": {"BoundingBox": {"Width": 0.1136901006102562, "Height": 0.010367173701524734, "Left": 0.6047427654266357, "Top": 0.3401624858379364}, "Polygon": [{"X": 0.6047427654266357, "Y": 0.3401624858379364}, {"X": 0.7184327244758606, "Y": 0.3401637375354767}, {"X": 0.7184328436851501, "Y": 0.35052967071533203}, {"X": 0.6047428846359253, "Y": 0.35052841901779175}]}, "Id": "0a2ae40f-4b49-40e7-8b4c-8bebfdccd1b5", "Relationships": [{"Type": "CHILD", "Ids": ["987b81fe-1ce2-4539-b29f-4a064752f163", "880d007d-dde3-4193-93ea-50fe959bdcf3"]}]}, {"BlockType": "LINE", "Confidence": 99.94710540771484, "Text": "131 <PERSON>, Unit 4", "Geometry": {"BoundingBox": {"Width": 0.1498349905014038, "Height": 0.012735310941934586, "Left": 0.13194872438907623, "Top": 0.3547472059726715}, "Polygon": [{"X": 0.13194872438907623, "Y": 0.3547472059726715}, {"X": 0.2817836105823517, "Y": 0.35474881529808044}, {"X": 0.28178369998931885, "Y": 0.3674825131893158}, {"X": 0.131948783993721, "Y": 0.36748093366622925}]}, "Id": "ce25f9c7-988f-4c7b-920d-f2ec8a12fe9c", "Relationships": [{"Type": "CHILD", "Ids": ["3adc6cea-0790-4b7e-bed9-7e438ff3e2ea", "00cd9db1-dbd2-4739-b108-2b104f4e267f", "29de3cef-e38f-49b8-a021-c2f8d4fe7c5b", "84ce5d82-515e-4a6c-91d0-9ee8ca30f34c", "ca3b1576-f06a-428d-876e-a4e8c7b46069"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "Load #: EST7AC11780784", "Geometry": {"BoundingBox": {"Width": 0.19138047099113464, "Height": 0.010673191398382187, "Left": 0.606505811214447, "Top": 0.35397273302078247}, "Polygon": [{"X": 0.606505811214447, "Y": 0.35397273302078247}, {"X": 0.7978861331939697, "Y": 0.3539747893810272}, {"X": 0.797886312007904, "Y": 0.36464592814445496}, {"X": 0.6065059304237366, "Y": 0.3646439015865326}]}, "Id": "e3ff3b60-4630-4e28-a2ad-b70adba73fa9", "Relationships": [{"Type": "CHILD", "Ids": ["41987681-1d2b-4a52-a5e1-d231b68e2038", "0b497a37-9217-429d-aa1e-74154216a68e", "b46aefa3-17ec-4454-93b0-24bf3595006a"]}]}, {"BlockType": "LINE", "Confidence": 99.90699005126953, "Text": "Ronkonkoma, NY 11779", "Geometry": {"BoundingBox": {"Width": 0.17113029956817627, "Height": 0.01211647130548954, "Left": 0.13153396546840668, "Top": 0.3685999810695648}, "Polygon": [{"X": 0.13153396546840668, "Y": 0.3685999810695648}, {"X": 0.3026641607284546, "Y": 0.3686017692089081}, {"X": 0.30266425013542175, "Y": 0.3807164430618286}, {"X": 0.13153403997421265, "Y": 0.38071468472480774}]}, "Id": "42db42a0-3b87-4983-89b7-3a5a4b0ba37e", "Relationships": [{"Type": "CHILD", "Ids": ["6d983561-971d-4f2a-889b-81552ce3f3e2", "621e6196-9b28-4a63-8afe-7c085f54e2a3", "c0d6cc1c-a13c-4126-84d3-dbaf5960733e"]}]}, {"BlockType": "LINE", "Confidence": 99.96663665771484, "Text": "Term: 3 Day Quick pay", "Geometry": {"BoundingBox": {"Width": 0.16412657499313354, "Height": 0.012870825827121735, "Left": 0.6049593091011047, "Top": 0.36804452538490295}, "Polygon": [{"X": 0.6049593091011047, "Y": 0.36804452538490295}, {"X": 0.769085705280304, "Y": 0.36804625391960144}, {"X": 0.7690858840942383, "Y": 0.3809153437614441}, {"X": 0.6049594283103943, "Y": 0.380913645029068}]}, "Id": "a9c988a1-e4ef-4f14-bcc0-242bad260052", "Relationships": [{"Type": "CHILD", "Ids": ["d18a6b06-58ef-4eb2-ac90-e51b9ab3f377", "b1552624-3cf4-4c36-b4c1-a2b71d65bdb4", "3ea78e7d-60d8-4596-a582-2d92824f93c5", "e77ac07d-1679-4fbf-bd2e-ab3331789cfe", "ed5cf182-fee5-4342-8689-b21401455ccc"]}]}, {"BlockType": "LINE", "Confidence": 99.92060089111328, "Text": "From: UPS MDT, Middletown, PA", "Geometry": {"BoundingBox": {"Width": 0.23775893449783325, "Height": 0.011680308729410172, "Left": 0.13109973073005676, "Top": 0.4254966974258423}, "Polygon": [{"X": 0.13109973073005676, "Y": 0.4254966974258423}, {"X": 0.36885857582092285, "Y": 0.42549896240234375}, {"X": 0.36885866522789, "Y": 0.43717700242996216}, {"X": 0.13109980523586273, "Y": 0.4371747672557831}]}, "Id": "d6316548-0994-43ea-a1b2-767e3bb85005", "Relationships": [{"Type": "CHILD", "Ids": ["9367f337-44e3-4810-9c5c-9a232da9f392", "a5da8117-047c-42d9-90cd-4bdb0e132f01", "4360bedb-00d9-4bad-8a2f-dc29d94decc7", "613d8df2-1758-43dd-a7e1-d45cd50cf1ee", "3fa5435a-5526-4c9c-9033-544dab53b6e9"]}]}, {"BlockType": "LINE", "Confidence": 99.86328125, "Text": "Truck #: 685", "Geometry": {"BoundingBox": {"Width": 0.08771657943725586, "Height": 0.010300821624696255, "Left": 0.6011584997177124, "Top": 0.42518866062164307}, "Polygon": [{"X": 0.6011584997177124, "Y": 0.42518866062164307}, {"X": 0.6888749599456787, "Y": 0.4251894950866699}, {"X": 0.6888750791549683, "Y": 0.4354894757270813}, {"X": 0.601158618927002, "Y": 0.43548867106437683}]}, "Id": "92cf946b-5272-4bcb-b605-d027cf8c4dae", "Relationships": [{"Type": "CHILD", "Ids": ["db35608f-d994-4d09-84fc-3d581e515196", "1caf5662-61b4-42b6-9a9f-c6aed3fd24c2", "50d13208-813f-436c-b45d-2367ebaf55ae"]}]}, {"BlockType": "LINE", "Confidence": 99.53916931152344, "Text": "To: Amazon, Breinigsville, PA", "Geometry": {"BoundingBox": {"Width": 0.20996138453483582, "Height": 0.012613467872142792, "Left": 0.13069012761116028, "Top": 0.43959611654281616}, "Polygon": [{"X": 0.13069012761116028, "Y": 0.43959611654281616}, {"X": 0.34065142273902893, "Y": 0.43959805369377136}, {"X": 0.3406515121459961, "Y": 0.45220956206321716}, {"X": 0.13069020211696625, "Y": 0.45220765471458435}]}, "Id": "0c9746ae-94d3-4724-9e79-d5c4f12ed809", "Relationships": [{"Type": "CHILD", "Ids": ["c06465ae-6925-4c91-af8b-5de14dedd029", "c3f7ada5-3e04-4c2b-b1b7-599f6b5f549d", "19242cc1-e504-4578-93d3-ef710e6be18d", "e4d296e5-e1a8-4d66-b67b-edeae5b0ad29"]}]}, {"BlockType": "LINE", "Confidence": 99.84049224853516, "Text": "Trailer #: 117", "Geometry": {"BoundingBox": {"Width": 0.09326766431331635, "Height": 0.010397485457360744, "Left": 0.602616012096405, "Top": 0.43910515308380127}, "Polygon": [{"X": 0.602616012096405, "Y": 0.43910515308380127}, {"X": 0.6958835124969482, "Y": 0.4391060173511505}, {"X": 0.6958836317062378, "Y": 0.4495026171207428}, {"X": 0.6026160717010498, "Y": 0.44950178265571594}]}, "Id": "d389443a-2582-4933-b0d9-3d8dc8506b71", "Relationships": [{"Type": "CHILD", "Ids": ["19308c2b-e6f8-487a-8986-94b207d2387a", "b0cc6c79-9ecb-4161-ac22-295dac61f08d", "e60a51c3-0eb8-45d9-9cee-9c0fb14d677e"]}]}, {"BlockType": "LINE", "Confidence": 99.8907241821289, "Text": "Back to: UPS MDT, Middletown, PA", "Geometry": {"BoundingBox": {"Width": 0.2555370628833771, "Height": 0.011883960105478764, "Left": 0.13129977881908417, "Top": 0.45346179604530334}, "Polygon": [{"X": 0.13129977881908417, "Y": 0.45346179604530334}, {"X": 0.3868367671966553, "Y": 0.4534641206264496}, {"X": 0.38683685660362244, "Y": 0.4653457701206207}, {"X": 0.13129985332489014, "Y": 0.46534350514411926}]}, "Id": "e1590e3b-1d4a-4deb-9459-d8ecb2f40e13", "Relationships": [{"Type": "CHILD", "Ids": ["459b0361-5fbd-40fa-a8bf-5c11f010e4dd", "b9c05b95-1089-4fc5-95de-514a02d09a16", "06c22235-50b6-49aa-9f35-0d79ac9fd683", "3b98e983-d5a8-4e1b-99e5-fd57045876f0", "67240046-27a2-465a-b82e-9c078f27f45e", "a705cdf2-1f96-47ee-967c-6794e9930fa1"]}]}, {"BlockType": "LINE", "Confidence": 99.74430084228516, "Text": "Driver: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.194793701171875, "Height": 0.012744942680001259, "Left": 0.6017146110534668, "Top": 0.4531106650829315}, "Polygon": [{"X": 0.6017146110534668, "Y": 0.4531106650829315}, {"X": 0.7965081930160522, "Y": 0.4531124234199524}, {"X": 0.7965083122253418, "Y": 0.46585559844970703}, {"X": 0.6017147898674011, "Y": 0.46585386991500854}]}, "Id": "71fba577-f0c0-4f26-8ec4-cc24ef72fcca", "Relationships": [{"Type": "CHILD", "Ids": ["697c546b-129b-474c-bef0-5141d952fb2a", "4275a83c-f401-4607-b74a-989171d2baf2", "e2dbd44c-5d68-47ca-ac21-a1062b51975f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.06524452567100525, "Height": 0.013433807529509068, "Left": 0.08595970273017883, "Top": 0.4988410770893097}, "Polygon": [{"X": 0.08595970273017883, "Y": 0.4988410770893097}, {"X": 0.1512041538953781, "Y": 0.49884161353111267}, {"X": 0.15120422840118408, "Y": 0.5122748613357544}, {"X": 0.0859597697854042, "Y": 0.5122743844985962}]}, "Id": "22183e5d-50df-4e7f-8a16-f64fab935650", "Relationships": [{"Type": "CHILD", "Ids": ["a4abb3dd-1f9d-4f7f-8c6c-e778ea3b3db9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Rate", "Geometry": {"BoundingBox": {"Width": 0.038705598562955856, "Height": 0.010720547288656235, "Left": 0.43075835704803467, "Top": 0.4989771544933319}, "Polygon": [{"X": 0.43075835704803467, "Y": 0.4989771544933319}, {"X": 0.46946385502815247, "Y": 0.4989774823188782}, {"X": 0.469463974237442, "Y": 0.509697675704956}, {"X": 0.43075844645500183, "Y": 0.5096973776817322}]}, "Id": "8616117e-a1fb-4c2d-b2fc-5d4610edc14e", "Relationships": [{"Type": "CHILD", "Ids": ["2d3eb02b-af53-4aba-8c93-0219dd01e1d5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.06877375394105911, "Height": 0.010745047591626644, "Left": 0.6637752652168274, "Top": 0.4988587200641632}, "Polygon": [{"X": 0.6637752652168274, "Y": 0.4988587200641632}, {"X": 0.7325488924980164, "Y": 0.4988592863082886}, {"X": 0.7325490117073059, "Y": 0.5096037983894348}, {"X": 0.6637753844261169, "Y": 0.5096032023429871}]}, "Id": "cf3c38b3-4473-4e2a-a1c3-e9c2d172a666", "Relationships": [{"Type": "CHILD", "Ids": ["b84fc8f9-e750-41c0-a5ad-04dc4c794e84"]}]}, {"BlockType": "LINE", "Confidence": 99.98779296875, "Text": "Load Delivered on 09/19/2025", "Geometry": {"BoundingBox": {"Width": 0.2430180460214615, "Height": 0.011312432587146759, "Left": 0.08713557571172714, "Top": 0.5306065082550049}, "Polygon": [{"X": 0.08713557571172714, "Y": 0.5306065082550049}, {"X": 0.33015352487564087, "Y": 0.5306083559989929}, {"X": 0.33015361428260803, "Y": 0.541918933391571}, {"X": 0.08713562786579132, "Y": 0.541917085647583}]}, "Id": "aa4b2bf5-f82d-481d-ada4-70a7c1efe89c", "Relationships": [{"Type": "CHILD", "Ids": ["bdcb9cca-4a5c-4b4e-b666-9402fbe2360a", "79e476d9-75a9-4e33-ad8a-2bb9f6bd17cd", "f3bd3079-4e8f-4ad0-8fd5-4ecdb0d02ad2", "0f67c1f8-7ab3-4605-927f-7be9d863543f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$575.00", "Geometry": {"BoundingBox": {"Width": 0.06598949432373047, "Height": 0.013470015488564968, "Left": 0.4300934970378876, "Top": 0.5292885303497314}, "Polygon": [{"X": 0.4300934970378876, "Y": 0.5292885303497314}, {"X": 0.4960828721523285, "Y": 0.5292890667915344}, {"X": 0.49608299136161804, "Y": 0.542758584022522}, {"X": 0.4300936162471771, "Y": 0.542758047580719}]}, "Id": "62b92028-b427-4ad3-80a8-ed8eeb08ba66", "Relationships": [{"Type": "CHILD", "Ids": ["62a0afc7-d9bd-4b78-9785-5f43323a84a4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$575.00", "Geometry": {"BoundingBox": {"Width": 0.0654326006770134, "Height": 0.013348333537578583, "Left": 0.6645368337631226, "Top": 0.529354989528656}, "Polygon": [{"X": 0.6645368337631226, "Y": 0.529354989528656}, {"X": 0.729969322681427, "Y": 0.5293554663658142}, {"X": 0.7299694418907166, "Y": 0.5427033305168152}, {"X": 0.6645370125770569, "Y": 0.5427027940750122}]}, "Id": "f7429f6a-f040-4857-b1e7-e16cfcfd6e16", "Relationships": [{"Type": "CHILD", "Ids": ["47adfa1a-96b0-4a3e-a80e-449b2ea2918c"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Return Load", "Geometry": {"BoundingBox": {"Width": 0.09758786112070084, "Height": 0.011116213165223598, "Left": 0.08706098794937134, "Top": 0.5770986080169678}, "Polygon": [{"X": 0.08706098794937134, "Y": 0.5770986080169678}, {"X": 0.1846487820148468, "Y": 0.5770992636680603}, {"X": 0.18464884161949158, "Y": 0.5882148146629333}, {"X": 0.08706104010343552, "Y": 0.5882141590118408}]}, "Id": "d4b23d99-eb13-4ec1-b83e-7124142e99e6", "Relationships": [{"Type": "CHILD", "Ids": ["880f68cd-b6f8-4cd0-a708-f2c07ea66cde", "d9369852-7780-4d64-8407-b3b73d7a125c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$425.00", "Geometry": {"BoundingBox": {"Width": 0.06590573489665985, "Height": 0.013091142289340496, "Left": 0.4300442039966583, "Top": 0.5761727690696716}, "Polygon": [{"X": 0.4300442039966583, "Y": 0.5761727690696716}, {"X": 0.49594980478286743, "Y": 0.5761732459068298}, {"X": 0.49594995379447937, "Y": 0.589263916015625}, {"X": 0.4300443232059479, "Y": 0.5892634987831116}]}, "Id": "d36fcba8-5520-48f1-be34-11e910701f1f", "Relationships": [{"Type": "CHILD", "Ids": ["6a4ec617-2f89-4a09-8214-7a7804db8d38"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$425.00", "Geometry": {"BoundingBox": {"Width": 0.06591783463954926, "Height": 0.013283252716064453, "Left": 0.6643721461296082, "Top": 0.5759472846984863}, "Polygon": [{"X": 0.6643721461296082, "Y": 0.5759472846984863}, {"X": 0.7302898168563843, "Y": 0.5759477615356445}, {"X": 0.7302899956703186, "Y": 0.5892305374145508}, {"X": 0.6643722653388977, "Y": 0.5892301201820374}]}, "Id": "10c6f9ca-8511-454d-8c2f-ffc32ecb13a7", "Relationships": [{"Type": "CHILD", "Ids": ["64eccf24-e1df-4c39-b0d6-caa0c600f19d"]}]}, {"BlockType": "LINE", "Confidence": 99.99755859375, "Text": "Make checks payable to:", "Geometry": {"BoundingBox": {"Width": 0.18305180966854095, "Height": 0.01302613690495491, "Left": 0.13079184293746948, "Top": 0.6752540469169617}, "Polygon": [{"X": 0.13079184293746948, "Y": 0.6752540469169617}, {"X": 0.3138435482978821, "Y": 0.6752550005912781}, {"X": 0.31384363770484924, "Y": 0.6882801651954651}, {"X": 0.13079191744327545, "Y": 0.6882792711257935}]}, "Id": "b018fd85-8ba4-4b4b-90fd-866f279217ac", "Relationships": [{"Type": "CHILD", "Ids": ["7e0f59cd-2391-4cdb-9ddd-8ebc009be5f6", "1623d093-ef6f-44fa-9e7e-a902f8a75d54", "0e536795-abf5-4dd3-b00c-ebaf2f621284", "4c115bf6-a29c-47d5-a3b6-0f0575c35edb"]}]}, {"BlockType": "LINE", "Confidence": 99.88082122802734, "Text": "Total: $1,000.00", "Geometry": {"BoundingBox": {"Width": 0.11509702354669571, "Height": 0.012594176456332207, "Left": 0.6600432395935059, "Top": 0.674380898475647}, "Polygon": [{"X": 0.6600432395935059, "Y": 0.674380898475647}, {"X": 0.775140106678009, "Y": 0.6743814945220947}, {"X": 0.7751402854919434, "Y": 0.6869750618934631}, {"X": 0.6600434184074402, "Y": 0.6869745254516602}]}, "Id": "50e9d659-d6d0-4fed-95b2-7badc251ecd6", "Relationships": [{"Type": "CHILD", "Ids": ["eb05c5f5-c69c-47e1-a8c7-2c61bb5d02ba", "dd23c105-533f-44cd-b160-cd7a9605cb1c"]}]}, {"BlockType": "LINE", "Confidence": 99.93383026123047, "Text": "P.A.N. Trucking LLC", "Geometry": {"BoundingBox": {"Width": 0.14311224222183228, "Height": 0.012754042632877827, "Left": 0.13092750310897827, "Top": 0.6892338991165161}, "Polygon": [{"X": 0.13092750310897827, "Y": 0.6892338991165161}, {"X": 0.2740396559238434, "Y": 0.6892346143722534}, {"X": 0.27403974533081055, "Y": 0.7019879221916199}, {"X": 0.13092757761478424, "Y": 0.7019872665405273}]}, "Id": "d286626c-7625-4bcf-9ade-8785a74b2992", "Relationships": [{"Type": "CHILD", "Ids": ["0a9d4bce-613f-4ec0-a860-9cfe3dedd6b3", "026effe7-3a88-4f95-a41c-f6cbfe0a3df6", "dd148c08-278b-44c5-a91b-5f3b4f8d3b61"]}]}, {"BlockType": "LINE", "Confidence": 99.98371887207031, "Text": "Balance Due: $1,000.00", "Geometry": {"BoundingBox": {"Width": 0.16912202537059784, "Height": 0.012504221871495247, "Left": 0.6078585386276245, "Top": 0.6883215308189392}, "Polygon": [{"X": 0.6078585386276245, "Y": 0.6883215308189392}, {"X": 0.776980459690094, "Y": 0.6883224248886108}, {"X": 0.7769805788993835, "Y": 0.7008257508277893}, {"X": 0.6078587174415588, "Y": 0.7008249759674072}]}, "Id": "c1c6a66f-cabd-4889-b2aa-6285992c8c57", "Relationships": [{"Type": "CHILD", "Ids": ["9a1e3d23-9e08-40b3-a20b-a9131db5e84d", "090cc648-c93b-45dd-9320-1fcfddc5580d", "8d8b4392-3d18-43e6-9bc3-ad328509b61a"]}]}, {"BlockType": "LINE", "Confidence": 99.97694396972656, "Text": "6490 Evelyn Street", "Geometry": {"BoundingBox": {"Width": 0.13365918397903442, "Height": 0.012731092050671577, "Left": 0.13048531115055084, "Top": 0.7034430503845215}, "Polygon": [{"X": 0.13048531115055084, "Y": 0.7034430503845215}, {"X": 0.2641443908214569, "Y": 0.703443706035614}, {"X": 0.2641444802284241, "Y": 0.7161741852760315}, {"X": 0.1304853856563568, "Y": 0.7161735892295837}]}, "Id": "a2669a1a-2f1e-4c3e-9650-0cff04b015c3", "Relationships": [{"Type": "CHILD", "Ids": ["82cd65eb-017c-48fb-b56e-8772bddffd96", "a8069538-ca72-4e79-9923-5c355013d9cf", "fe05e6f7-02f9-4508-9fe0-033f9f19cfde"]}]}, {"BlockType": "LINE", "Confidence": 99.7944564819336, "Text": "Harrisburg, PA 17111", "Geometry": {"BoundingBox": {"Width": 0.14843550324440002, "Height": 0.012945232912898064, "Left": 0.13105478882789612, "Top": 0.7171671390533447}, "Polygon": [{"X": 0.13105478882789612, "Y": 0.7171671390533447}, {"X": 0.279490202665329, "Y": 0.717167854309082}, {"X": 0.27949029207229614, "Y": 0.7301123738288879}, {"X": 0.1310548484325409, "Y": 0.7301117777824402}]}, "Id": "bfe7f248-de48-47bd-8c83-57ff86f1822a", "Relationships": [{"Type": "CHILD", "Ids": ["057eb588-4f3e-4051-9c3e-f8ebf148ec70", "c7fdd4c6-24fc-4064-b80b-94b777f297fe", "fb02fac4-8502-41af-a27d-8297f5db89f0"]}]}, {"BlockType": "LINE", "Confidence": 99.97412872314453, "Text": "Thank You for Your Business!", "Geometry": {"BoundingBox": {"Width": 0.25138309597969055, "Height": 0.012164457701146603, "Left": 0.3668988049030304, "Top": 0.8089209198951721}, "Polygon": [{"X": 0.3668988049030304, "Y": 0.8089209198951721}, {"X": 0.6182817816734314, "Y": 0.8089216351509094}, {"X": 0.618281900882721, "Y": 0.8210853338241577}, {"X": 0.36689889430999756, "Y": 0.8210846781730652}]}, "Id": "11e5fe76-9e6d-4542-bc17-f96925400bc6", "Relationships": [{"Type": "CHILD", "Ids": ["b0f6f006-a1c5-47cd-af7f-66712d0e70e9", "54601c93-7acc-4a98-851e-ba8ef3e5e547", "48898753-550e-4105-8469-57ac97cc95ac", "daba3f33-e9fe-4dee-b6f4-d91777d1bd60", "2520f820-6ed6-47d1-848a-96880ea5dc7d"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "P.A.N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054770976305007935, "Height": 0.011905391700565815, "Left": 0.268857479095459, "Top": 0.1287471503019333}, "Polygon": [{"X": 0.268857479095459, "Y": 0.1287471503019333}, {"X": 0.***********349976, "Y": 0.12874795496463776}, {"X": 0.3236284554004669, "Y": 0.14065253734588623}, {"X": 0.26885756850242615, "Y": 0.14065174758434296}]}, "Id": "a51cee1f-80e4-4d61-bcd1-879f577dacbf"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Trucking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08003603667020798, "Height": 0.01448386162519455, "Left": 0.32888153195381165, "Top": 0.12863217294216156}, "Polygon": [{"X": 0.32888153195381165, "Y": 0.12863217294216156}, {"X": 0.4089174270629883, "Y": 0.12863335013389587}, {"X": 0.40891754627227783, "Y": 0.1431160271167755}, {"X": 0.3288816213607788, "Y": 0.1431148648262024}]}, "Id": "8f194aa2-4f32-48bf-a89f-d69fd70ffde9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03538306802511215, "Height": 0.011663930490612984, "Left": 0.4150664508342743, "Top": 0.128449946641922}, "Polygon": [{"X": 0.4150664508342743, "Y": 0.128449946641922}, {"X": 0.4504494071006775, "Y": 0.12845046818256378}, {"X": 0.45044952630996704, "Y": 0.14011387526988983}, {"X": 0.41506657004356384, "Y": 0.14011336863040924}]}, "Id": "a4149833-a1ac-424b-b978-d1bc214c7e80"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "6490", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0386522077023983, "Height": 0.01071576401591301, "Left": 0.2709336578845978, "Top": 0.14529664814472198}, "Polygon": [{"X": 0.2709336578845978, "Y": 0.14529664814472198}, {"X": 0.3095857799053192, "Y": 0.14529721438884735}, {"X": 0.3095858693122864, "Y": 0.1560124158859253}, {"X": 0.27093371748924255, "Y": 0.15601186454296112}]}, "Id": "4acc6276-479d-45cb-814a-3d7ded2d6c94"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0510808564722538, "Height": 0.013397336937487125, "Left": 0.3147944509983063, "Top": 0.14509615302085876}, "Polygon": [{"X": 0.3147944509983063, "Y": 0.14509615302085876}, {"X": 0.3658752143383026, "Y": 0.14509688317775726}, {"X": 0.3658753037452698, "Y": 0.15849348902702332}, {"X": 0.31479454040527344, "Y": 0.15849275887012482}]}, "Id": "caaee1da-b899-45de-b9ab-fc27c437f241"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0468873605132103, "Height": 0.010857236571609974, "Left": 0.3710135519504547, "Top": 0.14497916400432587}, "Polygon": [{"X": 0.3710135519504547, "Y": 0.14497916400432587}, {"X": 0.41790083050727844, "Y": 0.1449798345565796}, {"X": 0.4179009199142456, "Y": 0.15583640336990356}, {"X": 0.3710136413574219, "Y": 0.15583573281764984}]}, "Id": "fcd53c0d-cfab-4f88-85d6-f930fb7cbafb"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Harrisburg,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08451944589614868, "Height": 0.013297122903168201, "Left": 0.27168262004852295, "Top": 0.16057494282722473}, "Polygon": [{"X": 0.27168262004852295, "Y": 0.16057494282722473}, {"X": 0.3562019467353821, "Y": 0.16057614982128143}, {"X": 0.35620206594467163, "Y": 0.17387206852436066}, {"X": 0.2716827094554901, "Y": 0.17387089133262634}]}, "Id": "2b243f30-7cfd-4cb3-907b-25fa3efcfdb9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023034263402223587, "Height": 0.010722807608544827, "Left": 0.36172106862068176, "Top": 0.1604495644569397}, "Polygon": [{"X": 0.36172106862068176, "Y": 0.1604495644569397}, {"X": 0.3847552537918091, "Y": 0.16044989228248596}, {"X": 0.38475534319877625, "Y": 0.1711723804473877}, {"X": 0.3617211580276489, "Y": 0.17117205262184143}]}, "Id": "cfc98b17-a993-4935-a48a-b176b30c6691"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "17111", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04524192959070206, "Height": 0.01096292957663536, "Left": 0.39042559266090393, "Top": 0.16032594442367554}, "Polygon": [{"X": 0.39042559266090393, "Y": 0.16032594442367554}, {"X": 0.4356674253940582, "Y": 0.16032658517360687}, {"X": 0.4356675148010254, "Y": 0.1712888777256012}, {"X": 0.3904256820678711, "Y": 0.17128823697566986}]}, "Id": "f439e541-0179-48cf-adda-32042312948d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053376149386167526, "Height": 0.010935977101325989, "Left": 0.27289873361587524, "Top": 0.1757192760705948}, "Polygon": [{"X": 0.27289873361587524, "Y": 0.1757192760705948}, {"X": 0.3262748122215271, "Y": 0.17572002112865448}, {"X": 0.3262748718261719, "Y": 0.18665525317192078}, {"X": 0.2728988230228424, "Y": 0.18665452301502228}]}, "Id": "e35bece4-bb93-47d9-b9c5-495743bba296"}, {"BlockType": "WORD", "Confidence": 99.0234375, "Text": "(717)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039470091462135315, "Height": 0.013364308513700962, "Left": 0.3323224186897278, "Top": 0.175744891166687}, "Polygon": [{"X": 0.3323224186897278, "Y": 0.175744891166687}, {"X": 0.37179240584373474, "Y": 0.17574544250965118}, {"X": 0.3717925250530243, "Y": 0.1891091912984848}, {"X": 0.33232253789901733, "Y": 0.18910865485668182}]}, "Id": "807335fd-e5e2-40f8-bbe4-32358d95e875"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "561-8001", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07103316485881805, "Height": 0.010964691638946533, "Left": 0.37720850110054016, "Top": 0.17556576430797577}, "Polygon": [{"X": 0.37720850110054016, "Y": 0.17556576430797577}, {"X": 0.44824156165122986, "Y": 0.17556674778461456}, {"X": 0.448241651058197, "Y": 0.1865304559469223}, {"X": 0.3772085905075073, "Y": 0.1865294873714447}]}, "Id": "bffcd515-0614-45dd-8d66-84b949d2f104"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "INVOICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14468902349472046, "Height": 0.01951207034289837, "Left": 0.13199232518672943, "Top": 0.26982709765434265}, "Polygon": [{"X": 0.13199232518672943, "Y": 0.26982709765434265}, {"X": 0.27668121457099915, "Y": 0.2698288857936859}, {"X": 0.2766813337802887, "Y": 0.28933918476104736}, {"X": 0.1319924294948578, "Y": 0.2893374562263489}]}, "Id": "94261aa7-3d62-4e30-9fdb-dadbefd3ff7e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023680511862039566, "Height": 0.01035020500421524, "Left": 0.1314866840839386, "Top": 0.3265421688556671}, "Polygon": [{"X": 0.1314866840839386, "Y": 0.3265421688556671}, {"X": 0.1551671326160431, "Y": 0.3265424370765686}, {"X": 0.15516719222068787, "Y": 0.33689239621162415}, {"X": 0.13148674368858337, "Y": 0.33689212799072266}]}, "Id": "99528572-c13e-4671-9987-d14f268caa0d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02412872016429901, "Height": 0.010077283717691898, "Left": 0.15959855914115906, "Top": 0.32669177651405334}, "Polygon": [{"X": 0.15959855914115906, "Y": 0.32669177651405334}, {"X": 0.1837272197008133, "Y": 0.32669204473495483}, {"X": 0.18372727930545807, "Y": 0.33676907420158386}, {"X": 0.15959861874580383, "Y": 0.3367688059806824}]}, "Id": "c88815c1-efa7-4b4f-b144-6d66d0bda174"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05399066209793091, "Height": 0.010271084494888783, "Left": 0.6051945090293884, "Top": 0.32615727186203003}, "Polygon": [{"X": 0.6051945090293884, "Y": 0.32615727186203003}, {"X": 0.6591850519180298, "Y": 0.32615789771080017}, {"X": 0.6591851711273193, "Y": 0.33642837405204773}, {"X": 0.6051945686340332, "Y": 0.3364277482032776}]}, "Id": "1cba96ee-fa35-42a2-be7a-d72b73f3aecb"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014546183869242668, "Height": 0.01039624772965908, "Left": 0.6626684069633484, "Top": 0.3260226845741272}, "Polygon": [{"X": 0.6626684069633484, "Y": 0.3260226845741272}, {"X": 0.6772144436836243, "Y": 0.3260228633880615}, {"X": 0.6772145628929138, "Y": 0.33641892671585083}, {"X": 0.6626685261726379, "Y": 0.3364187777042389}]}, "Id": "4b12da56-375d-46b7-8fce-6e528417aa77"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "919", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026014890521764755, "Height": 0.01018383726477623, "Left": 0.6823263764381409, "Top": 0.3262014091014862}, "Polygon": [{"X": 0.6823263764381409, "Y": 0.3262014091014862}, {"X": 0.708341121673584, "Y": 0.3262017071247101}, {"X": 0.7083412408828735, "Y": 0.33638525009155273}, {"X": 0.6823264956474304, "Y": 0.33638495206832886}]}, "Id": "f1ff6387-9096-4d4b-8546-f006ca93bb40"}, {"BlockType": "WORD", "Confidence": 97.32939910888672, "Text": "iFreight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05262390524148941, "Height": 0.012564047239720821, "Left": 0.13156910240650177, "Top": 0.34086117148399353}, "Polygon": [{"X": 0.13156910240650177, "Y": 0.34086117148399353}, {"X": 0.1841929405927658, "Y": 0.3408617377281189}, {"X": 0.18419301509857178, "Y": 0.35342520475387573}, {"X": 0.13156917691230774, "Y": 0.35342463850975037}]}, "Id": "776bb1f6-9772-4ce3-bbba-6dab7e3cf77e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03778807818889618, "Height": 0.010191770270466805, "Left": 0.6047427654266357, "Top": 0.3403370678424835}, "Polygon": [{"X": 0.6047427654266357, "Y": 0.3403370678424835}, {"X": 0.6425307393074036, "Y": 0.34033748507499695}, {"X": 0.6425308585166931, "Y": 0.3505288362503052}, {"X": 0.6047428846359253, "Y": 0.35052841901779175}]}, "Id": "987b81fe-1ce2-4539-b29f-4a064752f163"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "9/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07080510258674622, "Height": 0.010079697705805302, "Left": 0.6476277709007263, "Top": 0.3401629626750946}, "Polygon": [{"X": 0.6476277709007263, "Y": 0.3401629626750946}, {"X": 0.7184327244758606, "Y": 0.3401637375354767}, {"X": 0.7184328436851501, "Y": 0.3502426743507385}, {"X": 0.6476278901100159, "Y": 0.35024189949035645}]}, "Id": "880d007d-dde3-4193-93ea-50fe959bdcf3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "131", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02331540361046791, "Height": 0.01003602147102356, "Left": 0.13194872438907623, "Top": 0.3547472059726715}, "Polygon": [{"X": 0.13194872438907623, "Y": 0.3547472059726715}, {"X": 0.15526406466960907, "Y": 0.354747474193573}, {"X": 0.15526412427425385, "Y": 0.36478322744369507}, {"X": 0.13194876909255981, "Y": 0.36478298902511597}]}, "Id": "3adc6cea-0790-4b7e-bed9-7e438ff3e2ea"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033783696591854095, "Height": 0.012679493054747581, "Left": 0.16190756857395172, "Top": 0.35478711128234863}, "Polygon": [{"X": 0.16190756857395172, "Y": 0.35478711128234863}, {"X": 0.19569118320941925, "Y": 0.3547874689102173}, {"X": 0.19569125771522522, "Y": 0.36746659874916077}, {"X": 0.1619076430797577, "Y": 0.3674662411212921}]}, "Id": "00cd9db1-dbd2-4739-b108-2b104f4e267f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Way,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035561494529247284, "Height": 0.012526823207736015, "Left": 0.1997544914484024, "Top": 0.35495519638061523}, "Polygon": [{"X": 0.1997544914484024, "Y": 0.35495519638061523}, {"X": 0.23531590402126312, "Y": 0.3549555838108063}, {"X": 0.23531599342823029, "Y": 0.3674820363521576}, {"X": 0.19975458085536957, "Y": 0.36748164892196655}]}, "Id": "29de3cef-e38f-49b8-a021-c2f8d4fe7c5b"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028225919231772423, "Height": 0.010149252600967884, "Left": 0.24090784788131714, "Top": 0.3547782599925995}, "Polygon": [{"X": 0.24090784788131714, "Y": 0.3547782599925995}, {"X": 0.26913368701934814, "Y": 0.35477855801582336}, {"X": 0.2691337764263153, "Y": 0.3649275004863739}, {"X": 0.24090790748596191, "Y": 0.36492720246315}]}, "Id": "84ce5d82-515e-4a6c-91d0-9ee8ca30f34c"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009407791309058666, "Height": 0.009685590863227844, "Left": 0.2723758816719055, "Top": 0.3551722764968872}, "Polygon": [{"X": 0.2723758816719055, "Y": 0.3551722764968872}, {"X": 0.2817836105823517, "Y": 0.35517239570617676}, {"X": 0.28178369998931885, "Y": 0.36485788226127625}, {"X": 0.2723759710788727, "Y": 0.3648577928543091}]}, "Id": "ca3b1576-f06a-428d-876e-a4e8c7b46069"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03656439855694771, "Height": 0.010060827247798443, "Left": 0.606505811214447, "Top": 0.3543272912502289}, "Polygon": [{"X": 0.606505811214447, "Y": 0.3543272912502289}, {"X": 0.6430701017379761, "Y": 0.3543277084827423}, {"X": 0.6430702209472656, "Y": 0.3643881380558014}, {"X": 0.6065059304237366, "Y": 0.36438775062561035}]}, "Id": "41987681-1d2b-4a52-a5e1-d231b68e2038"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014264890924096107, "Height": 0.010493875481188297, "Left": 0.6469877362251282, "Top": 0.35415059328079224}, "Polygon": [{"X": 0.6469877362251282, "Y": 0.35415059328079224}, {"X": 0.661252498626709, "Y": 0.3541507422924042}, {"X": 0.6612526178359985, "Y": 0.36464446783065796}, {"X": 0.6469878554344177, "Y": 0.364644318819046}]}, "Id": "0b497a37-9217-429d-aa1e-74154216a68e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "EST7AC11780784", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1312474012374878, "Height": 0.010370193980634212, "Left": 0.6666388511657715, "Top": 0.353973388671875}, "Polygon": [{"X": 0.6666388511657715, "Y": 0.353973388671875}, {"X": 0.7978861331939697, "Y": 0.3539747893810272}, {"X": 0.7978862524032593, "Y": 0.3643435835838318}, {"X": 0.666638970375061, "Y": 0.36434218287467957}]}, "Id": "b46aefa3-17ec-4454-93b0-24bf3595006a"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "<PERSON><PERSON><PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09574141353368759, "Height": 0.011668597348034382, "Left": 0.13153396546840668, "Top": 0.369047075510025}, "Polygon": [{"X": 0.13153396546840668, "Y": 0.369047075510025}, {"X": 0.2272752970457077, "Y": 0.3690480887889862}, {"X": 0.22727538645267487, "Y": 0.38071566820144653}, {"X": 0.13153403997421265, "Y": 0.38071468472480774}]}, "Id": "6d983561-971d-4f2a-889b-81552ce3f3e2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02170742303133011, "Height": 0.01044465322047472, "Left": 0.2329569011926651, "Top": 0.3686010539531708}, "Polygon": [{"X": 0.2329569011926651, "Y": 0.3686010539531708}, {"X": 0.25466427206993103, "Y": 0.3686012625694275}, {"X": 0.2546643316745758, "Y": 0.379045695066452}, {"X": 0.23295697569847107, "Y": 0.3790454566478729}]}, "Id": "621e6196-9b28-4a63-8afe-7c085f54e2a3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "11779", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04274125397205353, "Height": 0.010105435736477375, "Left": 0.25992298126220703, "Top": 0.3689056634902954}, "Polygon": [{"X": 0.25992298126220703, "Y": 0.3689056634902954}, {"X": 0.3026641607284546, "Y": 0.3689061105251312}, {"X": 0.30266425013542175, "Y": 0.3790110945701599}, {"X": 0.2599230706691742, "Y": 0.3790106475353241}]}, "Id": "c0d6cc1c-a13c-4126-84d3-dbaf5960733e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Term:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04296939820051193, "Height": 0.010260540060698986, "Left": 0.6049593091011047, "Top": 0.36851534247398376}, "Polygon": [{"X": 0.6049593091011047, "Y": 0.36851534247398376}, {"X": 0.6479285955429077, "Y": 0.3685157895088196}, {"X": 0.6479287147521973, "Y": 0.37877586483955383}, {"X": 0.6049594283103943, "Y": 0.378775417804718}]}, "Id": "d18a6b06-58ef-4eb2-ac90-e51b9ab3f377"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008632569573819637, "Height": 0.009976429864764214, "Left": 0.6534436345100403, "Top": 0.36841195821762085}, "Polygon": [{"X": 0.6534436345100403, "Y": 0.36841195821762085}, {"X": 0.6620760560035706, "Y": 0.368412047624588}, {"X": 0.6620761752128601, "Y": 0.3783884048461914}, {"X": 0.6534437537193298, "Y": 0.37838831543922424}]}, "Id": "b1552624-3cf4-4c36-b4c1-a2b71d65bdb4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Day", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02777007780969143, "Height": 0.012614067643880844, "Left": 0.6670742630958557, "Top": 0.3682119846343994}, "Polygon": [{"X": 0.6670742630958557, "Y": 0.3682119846343994}, {"X": 0.6948441863059998, "Y": 0.3682122528553009}, {"X": 0.6948443651199341, "Y": 0.38082605600357056}, {"X": 0.6670743823051453, "Y": 0.3808257579803467}]}, "Id": "3ea78e7d-60d8-4596-a582-2d92824f93c5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quick", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04030711576342583, "Height": 0.01044329721480608, "Left": 0.6988762021064758, "Top": 0.36804550886154175}, "Polygon": [{"X": 0.6988762021064758, "Y": 0.36804550886154175}, {"X": 0.7391831874847412, "Y": 0.3680459260940552}, {"X": 0.7391833066940308, "Y": 0.37848880887031555}, {"X": 0.6988763213157654, "Y": 0.3784883916378021}]}, "Id": "e77ac07d-1679-4fbf-bd2e-ab3331789cfe"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025430239737033844, "Height": 0.010476185008883476, "Left": 0.7436556220054626, "Top": 0.3704391419887543}, "Polygon": [{"X": 0.7436556220054626, "Y": 0.3704391419887543}, {"X": 0.769085705280304, "Y": 0.37043941020965576}, {"X": 0.7690858840942383, "Y": 0.3809153437614441}, {"X": 0.7436557412147522, "Y": 0.3809150755405426}]}, "Id": "ed5cf182-fee5-4342-8689-b21401455ccc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "From:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04384590685367584, "Height": 0.01024008821696043, "Left": 0.13109973073005676, "Top": 0.4254966974258423}, "Polygon": [{"X": 0.13109973073005676, "Y": 0.4254966974258423}, {"X": 0.17494557797908783, "Y": 0.4254971146583557}, {"X": 0.1749456375837326, "Y": 0.4357367753982544}, {"X": 0.13109979033470154, "Y": 0.43573638796806335}]}, "Id": "9367f337-44e3-4810-9c5c-9a232da9f392"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "UPS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03169004246592522, "Height": 0.010128069669008255, "Left": 0.1802145391702652, "Top": 0.4255739450454712}, "Polygon": [{"X": 0.1802145391702652, "Y": 0.4255739450454712}, {"X": 0.21190452575683594, "Y": 0.42557424306869507}, {"X": 0.2119045853614807, "Y": 0.43570199608802795}, {"X": 0.18021459877490997, "Y": 0.4357016980648041}]}, "Id": "a5da8117-047c-42d9-90cd-4bdb0e132f01"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "MDT,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03740079700946808, "Height": 0.011335483752191067, "Left": 0.2165621966123581, "Top": 0.4257607161998749}, "Polygon": [{"X": 0.2165621966123581, "Y": 0.4257607161998749}, {"X": 0.253962904214859, "Y": 0.42576107382774353}, {"X": 0.25396299362182617, "Y": 0.43709617853164673}, {"X": 0.21656227111816406, "Y": 0.43709585070610046}]}, "Id": "4360bedb-00d9-4bad-8a2f-dc29d94decc7"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Middletown,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08311814069747925, "Height": 0.011502016335725784, "Left": 0.2596862018108368, "Top": 0.4256747364997864}, "Polygon": [{"X": 0.2596862018108368, "Y": 0.4256747364997864}, {"X": 0.3428042531013489, "Y": 0.42567554116249084}, {"X": 0.34280434250831604, "Y": 0.43717676401138306}, {"X": 0.25968629121780396, "Y": 0.437175989151001}]}, "Id": "613d8df2-1758-43dd-a7e1-d45cd50cf1ee"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020724989473819733, "Height": 0.009990927763283253, "Left": 0.3481336534023285, "Top": 0.4256795346736908}, "Polygon": [{"X": 0.3481336534023285, "Y": 0.4256795346736908}, {"X": 0.36885857582092285, "Y": 0.4256797432899475}, {"X": 0.36885866522789, "Y": 0.4356704652309418}, {"X": 0.34813374280929565, "Y": 0.43567028641700745}]}, "Id": "3fa5435a-5526-4c9c-9033-544dab53b6e9"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039924729615449905, "Height": 0.010204637423157692, "Left": 0.6011584997177124, "Top": 0.42528441548347473}, "Polygon": [{"X": 0.6011584997177124, "Y": 0.42528441548347473}, {"X": 0.6410831212997437, "Y": 0.4252847731113434}, {"X": 0.6410832405090332, "Y": 0.4354890286922455}, {"X": 0.601158618927002, "Y": 0.43548867106437683}]}, "Id": "db35608f-d994-4d09-84fc-3d581e515196"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012989589013159275, "Height": 0.010049702599644661, "Left": 0.6443216800689697, "Top": 0.4251890778541565}, "Polygon": [{"X": 0.6443216800689697, "Y": 0.4251890778541565}, {"X": 0.657311201095581, "Y": 0.42518919706344604}, {"X": 0.6573112607002258, "Y": 0.435238778591156}, {"X": 0.6443217992782593, "Y": 0.43523865938186646}]}, "Id": "1caf5662-61b4-42b6-9a9f-c6aed3fd24c2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "685", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026632942259311676, "Height": 0.010031592100858688, "Left": 0.6622421145439148, "Top": 0.4252631962299347}, "Polygon": [{"X": 0.6622421145439148, "Y": 0.4252631962299347}, {"X": 0.6888749599456787, "Y": 0.4252634644508362}, {"X": 0.6888750791549683, "Y": 0.4352948069572449}, {"X": 0.6622422337532043, "Y": 0.4352945387363434}]}, "Id": "50d13208-813f-436c-b45d-2367ebaf55ae"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024183494970202446, "Height": 0.010185032151639462, "Left": 0.13069012761116028, "Top": 0.43959611654281616}, "Polygon": [{"X": 0.13069012761116028, "Y": 0.43959611654281616}, {"X": 0.1548735648393631, "Y": 0.4395963251590729}, {"X": 0.15487362444400787, "Y": 0.4497811496257782}, {"X": 0.13069018721580505, "Y": 0.4497809112071991}]}, "Id": "c06465ae-6925-4c91-af8b-5de14dedd029"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Amazon,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062377505004405975, "Height": 0.011639614589512348, "Left": 0.15904466807842255, "Top": 0.4396768808364868}, "Polygon": [{"X": 0.15904466807842255, "Y": 0.4396768808364868}, {"X": 0.22142210602760315, "Y": 0.43967747688293457}, {"X": 0.22142218053340912, "Y": 0.4513165056705475}, {"X": 0.15904474258422852, "Y": 0.4513159394264221}]}, "Id": "c3f7ada5-3e04-4c2b-b1b7-599f6b5f549d"}, {"BlockType": "WORD", "Confidence": 98.60491180419922, "Text": "Breinigsville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08740317076444626, "Height": 0.012502633966505527, "Left": 0.2264872044324875, "Top": 0.4397067129611969}, "Polygon": [{"X": 0.2264872044324875, "Y": 0.4397067129611969}, {"X": 0.313890278339386, "Y": 0.43970751762390137}, {"X": 0.31389036774635315, "Y": 0.45220932364463806}, {"X": 0.22648729383945465, "Y": 0.452208548784256}]}, "Id": "19242cc1-e504-4578-93d3-ef710e6be18d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021307485178112984, "Height": 0.01003949623554945, "Left": 0.31934401392936707, "Top": 0.4396953582763672}, "Polygon": [{"X": 0.31934401392936707, "Y": 0.4396953582763672}, {"X": 0.34065142273902893, "Y": 0.4396955370903015}, {"X": 0.3406514823436737, "Y": 0.4497348368167877}, {"X": 0.31934407353401184, "Y": 0.4497346580028534}]}, "Id": "e4d296e5-e1a8-4d66-b67b-edeae5b0ad29"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045630961656570435, "Height": 0.010274368338286877, "Left": 0.602616012096405, "Top": 0.439227819442749}, "Polygon": [{"X": 0.602616012096405, "Y": 0.439227819442749}, {"X": 0.6482468247413635, "Y": 0.43922823667526245}, {"X": 0.6482469439506531, "Y": 0.44950219988822937}, {"X": 0.6026160717010498, "Y": 0.44950178265571594}]}, "Id": "19308c2b-e6f8-487a-8986-94b207d2387a"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013246117159724236, "Height": 0.009971226565539837, "Left": 0.6513245105743408, "Top": 0.4393344819545746}, "Polygon": [{"X": 0.6513245105743408, "Y": 0.4393344819545746}, {"X": 0.6645705103874207, "Y": 0.43933460116386414}, {"X": 0.6645706295967102, "Y": 0.4493056833744049}, {"X": 0.6513246297836304, "Y": 0.44930556416511536}]}, "Id": "b0cc6c79-9ecb-4161-ac22-295dac61f08d"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "117", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025479117408394814, "Height": 0.010083107277750969, "Left": 0.6704045534133911, "Top": 0.4391057789325714}, "Polygon": [{"X": 0.6704045534133911, "Y": 0.4391057789325714}, {"X": 0.6958835124969482, "Y": 0.4391060173511505}, {"X": 0.6958836317062378, "Y": 0.44918888807296753}, {"X": 0.6704046726226807, "Y": 0.4491886496543884}]}, "Id": "e60a51c3-0eb8-45d9-9cee-9c0fb14d677e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Back", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03753902018070221, "Height": 0.010347558185458183, "Left": 0.13129977881908417, "Top": 0.45346179604530334}, "Polygon": [{"X": 0.13129977881908417, "Y": 0.45346179604530334}, {"X": 0.1688387393951416, "Y": 0.453462153673172}, {"X": 0.16883879899978638, "Y": 0.46380937099456787}, {"X": 0.13129983842372894, "Y": 0.4638090431690216}]}, "Id": "459b0361-5fbd-40fa-a8bf-5c11f010e4dd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019916793331503868, "Height": 0.009417872875928879, "Left": 0.17222601175308228, "Top": 0.454088419675827}, "Polygon": [{"X": 0.17222601175308228, "Y": 0.454088419675827}, {"X": 0.19214275479316711, "Y": 0.45408859848976135}, {"X": 0.1921428143978119, "Y": 0.463506281375885}, {"X": 0.17222607135772705, "Y": 0.4635061025619507}]}, "Id": "b9c05b95-1089-4fc5-95de-514a02d09a16"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UPS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03200377896428108, "Height": 0.010313509032130241, "Left": 0.19741016626358032, "Top": 0.45351433753967285}, "Polygon": [{"X": 0.19741016626358032, "Y": 0.45351433753967285}, {"X": 0.22941388189792633, "Y": 0.45351463556289673}, {"X": 0.2294139415025711, "Y": 0.46382784843444824}, {"X": 0.1974102258682251, "Y": 0.46382758021354675}]}, "Id": "06c22235-50b6-49aa-9f35-0d79ac9fd683"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "MDT,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03746176138520241, "Height": 0.011474921368062496, "Left": 0.23430433869361877, "Top": 0.4538698196411133}, "Polygon": [{"X": 0.23430433869361877, "Y": 0.4538698196411133}, {"X": 0.2717660069465637, "Y": 0.45387017726898193}, {"X": 0.2717660963535309, "Y": 0.46534475684165955}, {"X": 0.23430441319942474, "Y": 0.4653444290161133}]}, "Id": "3b98e983-d5a8-4e1b-99e5-fd57045876f0"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "Middletown,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08275360614061356, "Height": 0.011666909791529179, "Left": 0.27707812190055847, "Top": 0.45353028178215027}, "Polygon": [{"X": 0.27707812190055847, "Y": 0.45353028178215027}, {"X": 0.35983163118362427, "Y": 0.45353105664253235}, {"X": 0.35983172059059143, "Y": 0.46519720554351807}, {"X": 0.27707821130752563, "Y": 0.4651964604854584}]}, "Id": "67240046-27a2-465a-b82e-9c078f27f45e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021252643316984177, "Height": 0.010082143358886242, "Left": 0.36558419466018677, "Top": 0.45354393124580383}, "Polygon": [{"X": 0.36558419466018677, "Y": 0.45354393124580383}, {"X": 0.3868367671966553, "Y": 0.45354411005973816}, {"X": 0.38683685660362244, "Y": 0.46362605690956116}, {"X": 0.36558428406715393, "Y": 0.46362587809562683}]}, "Id": "a705cdf2-1f96-47ee-967c-6794e9930fa1"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Driver:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045212388038635254, "Height": 0.010303663089871407, "Left": 0.6017146110534668, "Top": 0.4531705677509308}, "Polygon": [{"X": 0.6017146110534668, "Y": 0.4531705677509308}, {"X": 0.6469269394874573, "Y": 0.4531709849834442}, {"X": 0.646926999092102, "Y": 0.46347424387931824}, {"X": 0.6017147302627563, "Y": 0.4634738266468048}]}, "Id": "697c546b-129b-474c-bef0-5141d952fb2a"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056853681802749634, "Height": 0.01042204350233078, "Left": 0.6515215039253235, "Top": 0.45311111211776733}, "Polygon": [{"X": 0.6515215039253235, "Y": 0.45311111211776733}, {"X": 0.708375096321106, "Y": 0.4531116187572479}, {"X": 0.7083752155303955, "Y": 0.4635331630706787}, {"X": 0.651521623134613, "Y": 0.4635326564311981}]}, "Id": "4275a83c-f401-4607-b74a-989171d2baf2"}, {"BlockType": "WORD", "Confidence": 99.40210723876953, "Text": "Panfilovskiy", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08287347853183746, "Height": 0.012658814899623394, "Left": 0.7136348485946655, "Top": 0.45319679379463196}, "Polygon": [{"X": 0.7136348485946655, "Y": 0.45319679379463196}, {"X": 0.7965081930160522, "Y": 0.45319753885269165}, {"X": 0.7965083122253418, "Y": 0.46585559844970703}, {"X": 0.7136350274085999, "Y": 0.46585485339164734}]}, "Id": "e2dbd44c-5d68-47ca-ac21-a1062b51975f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06524452567100525, "Height": 0.013433807529509068, "Left": 0.08595970273017883, "Top": 0.4988410770893097}, "Polygon": [{"X": 0.08595970273017883, "Y": 0.4988410770893097}, {"X": 0.1512041538953781, "Y": 0.49884161353111267}, {"X": 0.15120422840118408, "Y": 0.5122748613357544}, {"X": 0.0859597697854042, "Y": 0.5122743844985962}]}, "Id": "a4abb3dd-1f9d-4f7f-8c6c-e778ea3b3db9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038705598562955856, "Height": 0.010720547288656235, "Left": 0.43075835704803467, "Top": 0.4989771544933319}, "Polygon": [{"X": 0.43075835704803467, "Y": 0.4989771544933319}, {"X": 0.46946385502815247, "Y": 0.4989774823188782}, {"X": 0.469463974237442, "Y": 0.509697675704956}, {"X": 0.43075844645500183, "Y": 0.5096973776817322}]}, "Id": "2d3eb02b-af53-4aba-8c93-0219dd01e1d5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06877375394105911, "Height": 0.010745047591626644, "Left": 0.6637752652168274, "Top": 0.4988587200641632}, "Polygon": [{"X": 0.6637752652168274, "Y": 0.4988587200641632}, {"X": 0.7325488924980164, "Y": 0.4988592863082886}, {"X": 0.7325490117073059, "Y": 0.5096037983894348}, {"X": 0.6637753844261169, "Y": 0.5096032023429871}]}, "Id": "b84fc8f9-e750-41c0-a5ad-04dc4c794e84"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038686420768499374, "Height": 0.011059868149459362, "Left": 0.08713557571172714, "Top": 0.5306283831596375}, "Polygon": [{"X": 0.08713557571172714, "Y": 0.5306283831596375}, {"X": 0.12582193315029144, "Y": 0.5306286811828613}, {"X": 0.12582199275493622, "Y": 0.5416882634162903}, {"X": 0.08713562786579132, "Y": 0.5416879653930664}]}, "Id": "bdcb9cca-4a5c-4b4e-b666-9402fbe2360a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Delivered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07625257968902588, "Height": 0.011171632446348667, "Left": 0.13118883967399597, "Top": 0.5306068658828735}, "Polygon": [{"X": 0.13118883967399597, "Y": 0.5306068658828735}, {"X": 0.20744135975837708, "Y": 0.5306074619293213}, {"X": 0.20744143426418304, "Y": 0.5417785048484802}, {"X": 0.13118889927864075, "Y": 0.5417779088020325}]}, "Id": "79e476d9-75a9-4e33-ad8a-2bb9f6bd17cd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019813690334558487, "Height": 0.008644025772809982, "Left": 0.21233659982681274, "Top": 0.5332741737365723}, "Polygon": [{"X": 0.21233659982681274, "Y": 0.5332741737365723}, {"X": 0.23215024173259735, "Y": 0.5332742929458618}, {"X": 0.23215030133724213, "Y": 0.5419182181358337}, {"X": 0.21233665943145752, "Y": 0.5419180393218994}]}, "Id": "f3bd3079-4e8f-4ad0-8fd5-4ecdb0d02ad2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "09/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09328129142522812, "Height": 0.010954827070236206, "Left": 0.2368723303079605, "Top": 0.530929446220398}, "Polygon": [{"X": 0.2368723303079605, "Y": 0.530929446220398}, {"X": 0.33015352487564087, "Y": 0.5309301614761353}, {"X": 0.33015361428260803, "Y": 0.5418842434883118}, {"X": 0.23687240481376648, "Y": 0.5418835282325745}]}, "Id": "0f67c1f8-7ab3-4605-927f-7be9d863543f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$575.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06598949432373047, "Height": 0.013470015488564968, "Left": 0.4300934970378876, "Top": 0.5292885303497314}, "Polygon": [{"X": 0.4300934970378876, "Y": 0.5292885303497314}, {"X": 0.4960828721523285, "Y": 0.5292890667915344}, {"X": 0.49608299136161804, "Y": 0.542758584022522}, {"X": 0.4300936162471771, "Y": 0.542758047580719}]}, "Id": "62a0afc7-d9bd-4b78-9785-5f43323a84a4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$575.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0654326006770134, "Height": 0.013348333537578583, "Left": 0.6645368337631226, "Top": 0.529354989528656}, "Polygon": [{"X": 0.6645368337631226, "Y": 0.529354989528656}, {"X": 0.729969322681427, "Y": 0.5293554663658142}, {"X": 0.7299694418907166, "Y": 0.5427033305168152}, {"X": 0.6645370125770569, "Y": 0.5427027940750122}]}, "Id": "47adfa1a-96b0-4a3e-a80e-449b2ea2918c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Return", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053440459072589874, "Height": 0.010844865813851357, "Left": 0.08706098794937134, "Top": 0.5770986080169678}, "Polygon": [{"X": 0.08706098794937134, "Y": 0.5770986080169678}, {"X": 0.14050137996673584, "Y": 0.5770989656448364}, {"X": 0.14050143957138062, "Y": 0.587943434715271}, {"X": 0.08706104010343552, "Y": 0.5879430770874023}]}, "Id": "880f68cd-b6f8-4cd0-a708-f2c07ea66cde"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038665320724248886, "Height": 0.010976718738675117, "Left": 0.1459835320711136, "Top": 0.5772380828857422}, "Polygon": [{"X": 0.1459835320711136, "Y": 0.5772380828857422}, {"X": 0.1846487820148468, "Y": 0.5772383213043213}, {"X": 0.18464884161949158, "Y": 0.5882148146629333}, {"X": 0.14598359167575836, "Y": 0.5882145166397095}]}, "Id": "d9369852-7780-4d64-8407-b3b73d7a125c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$425.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06590573489665985, "Height": 0.013091142289340496, "Left": 0.4300442039966583, "Top": 0.5761727690696716}, "Polygon": [{"X": 0.4300442039966583, "Y": 0.5761727690696716}, {"X": 0.49594980478286743, "Y": 0.5761732459068298}, {"X": 0.49594995379447937, "Y": 0.589263916015625}, {"X": 0.4300443232059479, "Y": 0.5892634987831116}]}, "Id": "6a4ec617-2f89-4a09-8214-7a7804db8d38"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$425.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06591783463954926, "Height": 0.013283252716064453, "Left": 0.6643721461296082, "Top": 0.5759472846984863}, "Polygon": [{"X": 0.6643721461296082, "Y": 0.5759472846984863}, {"X": 0.7302898168563843, "Y": 0.5759477615356445}, {"X": 0.7302899956703186, "Y": 0.5892305374145508}, {"X": 0.6643722653388977, "Y": 0.5892301201820374}]}, "Id": "64eccf24-e1df-4c39-b0d6-caa0c600f19d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Make", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03930268436670303, "Height": 0.010145539417862892, "Left": 0.13079184293746948, "Top": 0.6752930283546448}, "Polygon": [{"X": 0.13079184293746948, "Y": 0.6752930283546448}, {"X": 0.17009447515010834, "Y": 0.6752932071685791}, {"X": 0.1700945347547531, "Y": 0.6854385733604431}, {"X": 0.13079190254211426, "Y": 0.685438334941864}]}, "Id": "7e0f59cd-2391-4cdb-9ddd-8ebc009be5f6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05320468172430992, "Height": 0.010407534427940845, "Left": 0.17453314363956451, "Top": 0.6752542853355408}, "Polygon": [{"X": 0.17453314363956451, "Y": 0.6752542853355408}, {"X": 0.22773775458335876, "Y": 0.6752545833587646}, {"X": 0.22773781418800354, "Y": 0.685661792755127}, {"X": 0.1745332032442093, "Y": 0.6856615543365479}]}, "Id": "1623d093-ef6f-44fa-9e7e-a902f8a75d54"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "payable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05814969912171364, "Height": 0.012845820747315884, "Left": 0.2326178252696991, "Top": 0.6754342317581177}, "Polygon": [{"X": 0.2326178252696991, "Y": 0.6754342317581177}, {"X": 0.2907674312591553, "Y": 0.6754345893859863}, {"X": 0.29076752066612244, "Y": 0.6882801055908203}, {"X": 0.23261791467666626, "Y": 0.6882798075675964}]}, "Id": "0e536795-abf5-4dd3-b00c-ebaf2f621284"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "to:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01914600282907486, "Height": 0.009619060903787613, "Left": 0.2946976125240326, "Top": 0.6757460236549377}, "Polygon": [{"X": 0.2946976125240326, "Y": 0.6757460236549377}, {"X": 0.3138435482978821, "Y": 0.6757461428642273}, {"X": 0.31384363770484924, "Y": 0.6853650808334351}, {"X": 0.29469770193099976, "Y": 0.6853650212287903}]}, "Id": "4c115bf6-a29c-47d5-a3b6-0f0575c35edb"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Total:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03928915783762932, "Height": 0.010246636345982552, "Left": 0.6600432395935059, "Top": 0.6750242710113525}, "Polygon": [{"X": 0.6600432395935059, "Y": 0.6750242710113525}, {"X": 0.6993322968482971, "Y": 0.6750245094299316}, {"X": 0.6993324160575867, "Y": 0.6852709054946899}, {"X": 0.6600433588027954, "Y": 0.6852707266807556}]}, "Id": "eb05c5f5-c69c-47e1-a8c7-2c61bb5d02ba"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$1,000.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07111591100692749, "Height": 0.012593945488333702, "Left": 0.7040243744850159, "Top": 0.6743811368942261}, "Polygon": [{"X": 0.7040243744850159, "Y": 0.6743811368942261}, {"X": 0.775140106678009, "Y": 0.6743814945220947}, {"X": 0.7751402854919434, "Y": 0.6869750618934631}, {"X": 0.7040244936943054, "Y": 0.6869747042655945}]}, "Id": "dd23c105-533f-44cd-b160-cd7a9605cb1c"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "P.A.N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044880494475364685, "Height": 0.010299177840352058, "Left": 0.13092750310897827, "Top": 0.6892338991165161}, "Polygon": [{"X": 0.13092750310897827, "Y": 0.6892338991165161}, {"X": 0.17580793797969818, "Y": 0.6892340779304504}, {"X": 0.17580799758434296, "Y": 0.6995330452919006}, {"X": 0.13092756271362305, "Y": 0.6995328664779663}]}, "Id": "0a9d4bce-613f-4ec0-a860-9cfe3dedd6b3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trucking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06056283041834831, "Height": 0.012521215714514256, "Left": 0.18036550283432007, "Top": 0.6894665360450745}, "Polygon": [{"X": 0.18036550283432007, "Y": 0.6894665360450745}, {"X": 0.24092824757099152, "Y": 0.6894668340682983}, {"X": 0.24092833697795868, "Y": 0.7019877433776855}, {"X": 0.18036557734012604, "Y": 0.7019874453544617}]}, "Id": "026effe7-3a88-4f95-a41c-f6cbfe0a3df6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028201935812830925, "Height": 0.010181929916143417, "Left": 0.24583780765533447, "Top": 0.6892668604850769}, "Polygon": [{"X": 0.24583780765533447, "Y": 0.6892668604850769}, {"X": 0.2740396559238434, "Y": 0.6892669796943665}, {"X": 0.27403974533081055, "Y": 0.6994487643241882}, {"X": 0.24583786725997925, "Y": 0.6994486451148987}]}, "Id": "dd148c08-278b-44c5-a91b-5f3b4f8d3b61"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Balance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05666383355855942, "Height": 0.009866057895123959, "Left": 0.6078585982322693, "Top": 0.6892792582511902}, "Polygon": [{"X": 0.6078585982322693, "Y": 0.6892792582511902}, {"X": 0.6645222902297974, "Y": 0.6892794966697693}, {"X": 0.6645224094390869, "Y": 0.6991453170776367}, {"X": 0.6078586578369141, "Y": 0.6991450190544128}]}, "Id": "9a1e3d23-9e08-40b3-a20b-a9131db5e84d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Due:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03214672580361366, "Height": 0.009879035875201225, "Left": 0.6692668795585632, "Top": 0.6890569925308228}, "Polygon": [{"X": 0.6692668795585632, "Y": 0.6890569925308228}, {"X": 0.7014135122299194, "Y": 0.6890571117401123}, {"X": 0.701413631439209, "Y": 0.6989359855651855}, {"X": 0.6692669987678528, "Y": 0.698935866355896}]}, "Id": "090cc648-c93b-45dd-9320-1fcfddc5580d"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "$1,000.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07076315581798553, "Height": 0.01250373013317585, "Left": 0.7062174677848816, "Top": 0.6883220672607422}, "Polygon": [{"X": 0.7062174677848816, "Y": 0.6883220672607422}, {"X": 0.776980459690094, "Y": 0.6883224248886108}, {"X": 0.7769805788993835, "Y": 0.7008257508277893}, {"X": 0.7062175869941711, "Y": 0.7008254528045654}]}, "Id": "8d8b4392-3d18-43e6-9bc3-ad328509b61a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "6490", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03505415469408035, "Height": 0.009877997450530529, "Left": 0.13048531115055084, "Top": 0.703585684299469}, "Polygon": [{"X": 0.13048531115055084, "Y": 0.703585684299469}, {"X": 0.16553941369056702, "Y": 0.7035858631134033}, {"X": 0.1655394583940506, "Y": 0.7134637236595154}, {"X": 0.13048537075519562, "Y": 0.713463544845581}]}, "Id": "82cd65eb-017c-48fb-b56e-8772bddffd96"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04670681059360504, "Height": 0.012730690650641918, "Left": 0.17046916484832764, "Top": 0.7034432888031006}, "Polygon": [{"X": 0.17046916484832764, "Y": 0.7034432888031006}, {"X": 0.21717588603496552, "Y": 0.7034434676170349}, {"X": 0.21717597544193268, "Y": 0.7161739468574524}, {"X": 0.1704692393541336, "Y": 0.7161737680435181}]}, "Id": "a8069538-ca72-4e79-9923-5c355013d9cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Street", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042115215212106705, "Height": 0.010090978816151619, "Left": 0.22202925384044647, "Top": 0.7035495042800903}, "Polygon": [{"X": 0.22202925384044647, "Y": 0.7035495042800903}, {"X": 0.2641443908214569, "Y": 0.7035496830940247}, {"X": 0.2641444802284241, "Y": 0.7136404514312744}, {"X": 0.22202932834625244, "Y": 0.7136402726173401}]}, "Id": "fe05e6f7-02f9-4508-9fe0-033f9f19cfde"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "Harrisburg,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07651811093091965, "Height": 0.01294492557644844, "Left": 0.13105478882789612, "Top": 0.7171671390533447}, "Polygon": [{"X": 0.13105478882789612, "Y": 0.7171671390533447}, {"X": 0.2075728178024292, "Y": 0.7171674966812134}, {"X": 0.20757289230823517, "Y": 0.7301120758056641}, {"X": 0.1310548484325409, "Y": 0.7301117777824402}]}, "Id": "057eb588-4f3e-4051-9c3e-f8ebf148ec70"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02108193375170231, "Height": 0.0099472776055336, "Left": 0.21334214508533478, "Top": 0.7177132964134216}, "Polygon": [{"X": 0.21334214508533478, "Y": 0.7177132964134216}, {"X": 0.23442402482032776, "Y": 0.7177134156227112}, {"X": 0.23442408442497253, "Y": 0.727660596370697}, {"X": 0.21334221959114075, "Y": 0.7276604771614075}]}, "Id": "c7fdd4c6-24fc-4064-b80b-94b777f297fe"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "17111", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039943769574165344, "Height": 0.010138502344489098, "Left": 0.2395465075969696, "Top": 0.7175029516220093}, "Polygon": [{"X": 0.2395465075969696, "Y": 0.7175029516220093}, {"X": 0.279490202665329, "Y": 0.7175031304359436}, {"X": 0.27949026226997375, "Y": 0.7276414632797241}, {"X": 0.23954656720161438, "Y": 0.7276412844657898}]}, "Id": "fb02fac4-8502-41af-a27d-8297f5db89f0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Thank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053030598908662796, "Height": 0.011909275315701962, "Left": 0.3668988049030304, "Top": 0.8091707229614258}, "Polygon": [{"X": 0.3668988049030304, "Y": 0.8091707229614258}, {"X": 0.41992929577827454, "Y": 0.8091709017753601}, {"X": 0.4199294149875641, "Y": 0.8210800290107727}, {"X": 0.36689889430999756, "Y": 0.8210798501968384}]}, "Id": "b0f6f006-a1c5-47cd-af7f-66712d0e70e9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "You", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03362162783741951, "Height": 0.01185483019798994, "Left": 0.4244356155395508, "Top": 0.8091590404510498}, "Polygon": [{"X": 0.4244356155395508, "Y": 0.8091590404510498}, {"X": 0.45805713534355164, "Y": 0.8091591000556946}, {"X": 0.4580572247505188, "Y": 0.821013867855072}, {"X": 0.42443570494651794, "Y": 0.8210137486457825}]}, "Id": "54601c93-7acc-4a98-851e-ba8ef3e5e547"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023224739357829094, "Height": 0.012103009037673473, "Left": 0.4633760452270508, "Top": 0.8089820146560669}, "Polygon": [{"X": 0.4633760452270508, "Y": 0.8089820146560669}, {"X": 0.4866006672382355, "Y": 0.8089820742607117}, {"X": 0.486600786447525, "Y": 0.8210849761962891}, {"X": 0.46337613463401794, "Y": 0.8210849165916443}]}, "Id": "48898753-550e-4105-8469-57ac97cc95ac"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04054402932524681, "Height": 0.01184115931391716, "Left": 0.4909999668598175, "Top": 0.8091040849685669}, "Polygon": [{"X": 0.4909999668598175, "Y": 0.8091040849685669}, {"X": 0.5315439105033875, "Y": 0.8091042041778564}, {"X": 0.531544029712677, "Y": 0.8209452629089355}, {"X": 0.49100008606910706, "Y": 0.820945143699646}]}, "Id": "daba3f33-e9fe-4dee-b6f4-d91777d1bd60"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Business!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0810040831565857, "Height": 0.012118862010538578, "Left": 0.5372778177261353, "Top": 0.8089213967323303}, "Polygon": [{"X": 0.5372778177261353, "Y": 0.8089213967323303}, {"X": 0.6182817816734314, "Y": 0.8089216351509094}, {"X": 0.618281900882721, "Y": 0.8210402727127075}, {"X": 0.5372779369354248, "Y": 0.8210400342941284}]}, "Id": "2520f820-6ed6-47d1-848a-96880ea5dc7d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d32a0998-9c1b-4d5c-9ccd-faf4f566c64f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d32a0998-9c1b-4d5c-9ccd-faf4f566c64f", "content-type": "application/x-amz-json-1.1", "content-length": "68933", "date": "Wed, 24 Sep 2025 12:59:47 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "f743b3c2-59ce-42f8-b3b7-10feab6cdc09", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:59:50 GMT", "content-type": "application/json", "content-length": "683", "connection": "keep-alive", "x-amzn-requestid": "f743b3c2-59ce-42f8-b3b7-10feab6cdc09"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page content. We need to classify doc_type. The text shows \"INVOICE\" header, with Invoice #, Bill To, etc. No HS, customs, etc. So it's an invoice. It's a carrier invoice, not commercial. So doc_type: \"invoice\". Output must be via tool call classify_logistics_doc_type. Provide array of objects for each page. Only page1. Output JSON accordingly."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"invoice\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2765, "outputTokens": 104, "totalTokens": 2869}, "metrics": {"latencyMs": 648}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/5b2d1806_W1P39VXY7XRKDJG29JXQ.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nP.A.N. Trucking LLC\n6490 Evelyn Street\nHarrisburg, PA 17111\nPhone: (*************\nINVOICE\nBill To:\nInvoice #: 919\niFreight\nDate: 9/19/2025\n131 Gary Way, Unit 4\nLoad #: EST7AC11780784\nRonkonkoma, NY 11779\nTerm: 3 Day Quick pay\nFrom: UPS MDT, Middletown, PA\nTruck #: 685\nTo: Amazon, Breinigsville, PA\nTrailer #: 117\nBack to: UPS MDT, Middletown, PA\nDriver: <PERSON><PERSON>\nActivity\nRate\nAmount\nLoad Delivered on 09/19/2025\n$575.00\n$575.00\nReturn Load\n$425.00\n$425.00\nMake checks payable to:\nTotal: $1,000.00\nP.A.N. Trucking LLC\nBalance Due: $1,000.00\n6490 Evelyn Street\nHarrisburg, PA 17111\nThank You for Your Business!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}