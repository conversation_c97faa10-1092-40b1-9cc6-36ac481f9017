{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9579706788063049, "Height": 0.8127232193946838, "Left": 0.042029350996017456, "Top": 0.18727681040763855}, "Polygon": [{"X": 0.07137355208396912, "Y": 0.19274047017097473}, {"X": 0.9341785311698914, "Y": 0.18727681040763855}, {"X": 1.0, "Y": 0.968345582485199}, {"X": 0.042029350996017456, "Y": 1.0}]}, "Id": "fcc5073e-bc1d-4598-8efb-51d4d987f6e0", "Relationships": [{"Type": "CHILD", "Ids": ["53079956-0a99-41e0-a561-ed5300efdbe0", "6963a826-8b11-4936-a420-85007d52fe86", "335c8e73-01f4-4483-b4ec-3f1e62d0d936", "b0029e6d-f168-4219-8d72-fe93ecd39298", "be7f1ee6-1bdd-40d4-9706-3a820a7fdf73", "43d53913-ff65-4eff-98fa-abe484ef6375", "0a53f522-85b2-4b87-9cdc-01f057117eb0", "1f732b3e-96c6-43f0-ab6d-3df493201a7b", "7f9193ca-d1d6-4d7c-a3ac-5688a32e67c9", "b6ae853b-0fd1-481a-af81-53bd0ea002d6", "92d07b25-df55-4cb3-b2b0-d6171e80e4b6", "1df9c0f7-faf7-467c-a2fa-cdb3907a6799", "36ee3846-92cb-4af7-a555-daaf50a81fad", "614e130f-6cc4-476e-b271-01d27ee4475a", "8c8a4277-9b13-4c26-a86b-c474e36f0158", "e2c49d15-16a5-4859-b436-dffc7f697457", "8c3ffc6f-0b8e-440c-a77e-738da6c56dfb", "ed994463-25f1-441a-ad33-9ffd8faf74fe", "1b0bfed7-9d2f-4385-8074-c18b9101b4fd", "08a63e3c-3d02-4b18-8f25-22f1d00f42de", "e01c8e91-fd0f-49b9-8e05-798f430fcb8a", "cc610281-b2a4-483f-b645-d0d42685a14b", "0a679443-0f0e-4dd9-b0a7-e295689993c3", "6f84a64f-8191-4874-bbd7-205236f6b996", "bc45eea9-0104-4e48-8625-b458cd44b467", "253ed676-3919-4ae4-8116-c8fb68c5e09e", "ca3eea60-502b-43e2-a220-000f7d3fe23b", "292b5b9c-c027-47cf-983c-9b1e6970d879", "36148bdf-c045-4508-817d-18c0b86f9ec6", "34cb3c9d-c881-4492-be0e-5eecca44d972"]}]}, {"BlockType": "LINE", "Confidence": 94.45445251464844, "Text": "KWIK TRIP 1281 /106 N. ROYAL AVENUE ABELGIUM. WI 53004/(262) 988-7024", "Geometry": {"BoundingBox": {"Width": 0.7081819176673889, "Height": 0.02934407815337181, "Left": 0.0961918905377388, "Top": 0.2260054051876068}, "Polygon": [{"X": 0.09696412086486816, "Y": 0.2314014881849289}, {"X": 0.8026425242424011, "Y": 0.2260054051876068}, {"X": 0.8043738007545471, "Y": 0.2493603229522705}, {"X": 0.0961918905377388, "Y": 0.2553494870662689}]}, "Id": "53079956-0a99-41e0-a561-ed5300efdbe0", "Relationships": [{"Type": "CHILD", "Ids": ["6305e982-efee-42a3-9b5c-9678f781549d", "60cb8bb5-807c-44cc-9953-ae6f2e7c580f", "b45cc05c-ef50-4d0c-b1a4-15b0baa86f33", "9dee8351-a3cd-4904-9396-deaedf808d4a", "58a6446a-1481-44c3-8a9a-158901264098", "40744896-9840-47a2-a29e-54086e3f79b2", "23134533-4d32-461f-a2b9-943912d7394e", "5732d3ec-4e2b-4ccc-b767-9f541d4b5d59", "73125346-d990-4d16-b69b-9e4748ce0758", "7bdf9286-884d-4ac2-87de-85742454249c", "ef2c4980-b066-4716-b5d8-5ec8064d0646"]}]}, {"BlockType": "LINE", "Confidence": 98.92051696777344, "Text": "Number: 0900-0760 05/12/2025 Station: 9", "Geometry": {"BoundingBox": {"Width": 0.4264146089553833, "Height": 0.022711986675858498, "Left": 0.09531233459711075, "Top": 0.25491419434547424}, "Polygon": [{"X": 0.09592772275209427, "Y": 0.2585563361644745}, {"X": 0.5211343765258789, "Y": 0.25491419434547424}, {"X": 0.5217269659042358, "Y": 0.2736985385417938}, {"X": 0.09531233459711075, "Y": 0.2776261866092682}]}, "Id": "6963a826-8b11-4936-a420-85007d52fe86", "Relationships": [{"Type": "CHILD", "Ids": ["8318d390-e58f-41c9-b1a4-a84ba397ebbf", "1300e6c9-7d54-4cfe-b21a-33fd8e73e874", "bd73647e-ee8f-417a-905d-58f05d03edf9", "88a74add-bcec-41b3-83ce-edc1eeaa4c33", "31f5ed81-29bb-478e-9912-90d6d301d38f"]}]}, {"BlockType": "LINE", "Confidence": 99.12236785888672, "Text": "Response: APPROVED AUTH REF#: P00174", "Geometry": {"BoundingBox": {"Width": 0.37614139914512634, "Height": 0.021576235070824623, "Left": 0.09551398456096649, "Top": 0.3017629086971283}, "Polygon": [{"X": 0.09608286619186401, "Y": 0.30557340383529663}, {"X": 0.4712366461753845, "Y": 0.3017629086971283}, {"X": 0.47165536880493164, "Y": 0.31929248571395874}, {"X": 0.09551398456096649, "Y": 0.3233391344547272}]}, "Id": "335c8e73-01f4-4483-b4ec-3f1e62d0d936", "Relationships": [{"Type": "CHILD", "Ids": ["15635cf1-a8c8-4689-8fdd-6272c7859797", "ea3961ec-0b4a-425a-89a0-b3053279e877", "7a6940ed-fb29-44bf-bbff-ede03970e117", "0011e896-937d-49f9-9af0-494bad660133", "974f5395-c01b-4354-b8e8-7191da4c2641"]}]}, {"BlockType": "LINE", "Confidence": 96.2119369506836, "Text": "LOYALTY Response: APPROVED. You earned 1.64 Fleet points! Drink Credit Earned! E", "Geometry": {"BoundingBox": {"Width": 0.828021228313446, "Height": 0.03583764657378197, "Left": 0.09382016956806183, "Top": 0.3349702060222626}, "Polygon": [{"X": 0.09466539323329926, "Y": 0.3444330394268036}, {"X": 0.9195305109024048, "Y": 0.3349702060222626}, {"X": 0.9218414425849915, "Y": 0.36056986451148987}, {"X": 0.09382016956806183, "Y": 0.37080782651901245}]}, "Id": "b0029e6d-f168-4219-8d72-fe93ecd39298", "Relationships": [{"Type": "CHILD", "Ids": ["44674e40-2d63-4e76-a1c6-4c03ba8cc18a", "a601d64f-7189-4eb9-b482-1c6f7aa53646", "7b99b851-90d7-47fc-aa84-6d9be5a5fdfb", "d9c994ac-f39e-462c-8512-74bce0deded0", "e9bf57e1-8a3d-4a7f-9498-20e1efdb6437", "512c7f03-2809-49cb-8630-fdd62300cc34", "6bd989cf-47a5-4203-9932-5cba1a1efe6c", "a46397fa-9ce5-49da-b330-a7d441bbc543", "8a3cee00-ef61-4b3f-b5c9-09824c19f919", "ee8e5ae6-31ec-462d-90e6-8c2e60e75410", "59b1f576-7dba-49ec-9a9f-bd7599873443", "a41491e0-28df-43b6-bf43-405eec56a274"]}]}, {"BlockType": "LINE", "Confidence": 92.69766998291016, "Text": "xpires in 24 Hours. Shower C", "Geometry": {"BoundingBox": {"Width": 0.29650232195854187, "Height": 0.021822204813361168, "Left": 0.09540756046772003, "Top": 0.3702833950519562}, "Polygon": [{"X": 0.0959826409816742, "Y": 0.3739716410636902}, {"X": 0.3916962146759033, "Y": 0.3702833950519562}, {"X": 0.3919098675251007, "Y": 0.38822558522224426}, {"X": 0.09540756046772003, "Y": 0.3921056091785431}]}, "Id": "be7f1ee6-1bdd-40d4-9706-3a820a7fdf73", "Relationships": [{"Type": "CHILD", "Ids": ["56f2603d-3e70-4d40-aa96-ce8d1e631706", "133ebcb3-0ddc-48f2-9f3c-154ff8893413", "d8a52fd9-dfc5-48e7-9ebb-c4ec33d053f0", "d27ff9e9-156d-4d79-8c32-993de9f9993a", "7c97a49c-8f8b-49a5-a385-e2ad1e5939c0", "d63767bc-fca7-407c-bb72-02a5bc7f5552"]}]}, {"BlockType": "LINE", "Confidence": 99.81126403808594, "Text": "Pump", "Geometry": {"BoundingBox": {"Width": 0.044174861162900925, "Height": 0.016635917127132416, "Left": 0.09379877150058746, "Top": 0.4213407337665558}, "Polygon": [{"X": 0.09430718421936035, "Y": 0.42195624113082886}, {"X": 0.1379736363887787, "Y": 0.4213407337665558}, {"X": 0.13756826519966125, "Y": 0.43733593821525574}, {"X": 0.09379877150058746, "Y": 0.4379766285419464}]}, "Id": "43d53913-ff65-4eff-98fa-abe484ef6375", "Relationships": [{"Type": "CHILD", "Ids": ["dbd906ae-262d-4add-b5a9-390715c81e11"]}]}, {"BlockType": "LINE", "Confidence": 99.80963134765625, "Text": "Products Reefer Quantity Unit Cost", "Geometry": {"BoundingBox": {"Width": 0.40388429164886475, "Height": 0.02530001476407051, "Left": 0.30780139565467834, "Top": 0.4056236445903778}, "Polygon": [{"X": 0.30781063437461853, "Y": 0.4111940860748291}, {"X": 0.7105453014373779, "Y": 0.4056236445903778}, {"X": 0.7116856575012207, "Y": 0.4250656068325043}, {"X": 0.30780139565467834, "Y": 0.4309236407279968}]}, "Id": "0a53f522-85b2-4b87-9cdc-01f057117eb0", "Relationships": [{"Type": "CHILD", "Ids": ["b5af164b-2afa-4390-9fe4-bc1bdcd726b8", "bebd9c40-848b-4d96-a9d7-133eaa6ab35f", "066f8d1b-2eef-419f-bd58-b95018f9cf54", "ad25e323-058e-4618-946b-3c8e9b28a988", "e06c5632-2ef1-4cbd-953e-6db5b1d8e8c7"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.04852968081831932, "Height": 0.015722179785370827, "Left": 0.7737860083580017, "Top": 0.40308552980422974}, "Polygon": [{"X": 0.7737860083580017, "Y": 0.4037395417690277}, {"X": 0.8211886286735535, "Y": 0.40308552980422974}, {"X": 0.8223156929016113, "Y": 0.41812747716903687}, {"X": 0.7748099565505981, "Y": 0.418807715177536}]}, "Id": "1f732b3e-96c6-43f0-ab6d-3df493201a7b", "Relationships": [{"Type": "CHILD", "Ids": ["63e36399-c51b-44a7-acd2-b4eddb112067"]}]}, {"BlockType": "LINE", "Confidence": 60.259883880615234, "Text": "#2", "Geometry": {"BoundingBox": {"Width": 0.0334499254822731, "Height": 0.016636015847325325, "Left": 0.09460008889436722, "Top": 0.4444902241230011}, "Polygon": [{"X": 0.09510887414216995, "Y": 0.4449802339076996}, {"X": 0.12805001437664032, "Y": 0.4444902241230011}, {"X": 0.12761934101581573, "Y": 0.4606170058250427}, {"X": 0.09460008889436722, "Y": 0.4611262381076813}]}, "Id": "7f9193ca-d1d6-4d7c-a3ac-5688a32e67c9", "Relationships": [{"Type": "CHILD", "Ids": ["ed62dbd8-0a8d-4141-b984-57e9c07cb069"]}]}, {"BlockType": "LINE", "Confidence": 99.73881530761719, "Text": "Diesel 2", "Geometry": {"BoundingBox": {"Width": 0.0836011990904808, "Height": 0.016604673117399216, "Left": 0.3084762394428253, "Top": 0.43725526332855225}, "Polygon": [{"X": 0.308481901884079, "Y": 0.43848666548728943}, {"X": 0.3918962776660919, "Y": 0.43725526332855225}, {"X": 0.3920774459838867, "Y": 0.4525819718837738}, {"X": 0.3084762394428253, "Y": 0.45385992527008057}]}, "Id": "b6ae853b-0fd1-481a-af81-53bd0ea002d6", "Relationships": [{"Type": "CHILD", "Ids": ["e6b935ba-a2b3-4efe-b6b6-4daf6709a5d5", "19edf889-a340-4f3f-ae28-8ad82f7d567e"]}]}, {"BlockType": "LINE", "Confidence": 99.71360778808594, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.009917051531374454, "Height": 0.014264615252614021, "Left": 0.44316932559013367, "Top": 0.43468478322029114}, "Polygon": [{"X": 0.44316932559013367, "Y": 0.4348263144493103}, {"X": 0.45279374718666077, "Y": 0.43468478322029114}, {"X": 0.45308637619018555, "Y": 0.4488029181957245}, {"X": 0.4434421956539154, "Y": 0.44894939661026}]}, "Id": "92d07b25-df55-4cb3-b2b0-d6171e80e4b6", "Relationships": [{"Type": "CHILD", "Ids": ["d2201fe4-6fe0-4a57-9571-52d053ba9b3c"]}]}, {"BlockType": "LINE", "Confidence": 99.27515411376953, "Text": "82.836", "Geometry": {"BoundingBox": {"Width": 0.06126062944531441, "Height": 0.01540230680257082, "Left": 0.5368056297302246, "Top": 0.43075624108314514}, "Polygon": [{"X": 0.5368056297302246, "Y": 0.43164440989494324}, {"X": 0.5974594354629517, "Y": 0.43075624108314514}, {"X": 0.5980662107467651, "Y": 0.4452382028102875}, {"X": 0.5372849106788635, "Y": 0.4461585581302643}]}, "Id": "1df9c0f7-faf7-467c-a2fa-cdb3907a6799", "Relationships": [{"Type": "CHILD", "Ids": ["3467b664-ac13-4c37-b4f4-75caec81a230"]}]}, {"BlockType": "LINE", "Confidence": 97.13169860839844, "Text": "3.099", "Geometry": {"BoundingBox": {"Width": 0.0529136061668396, "Height": 0.016413262113928795, "Left": 0.6600504517555237, "Top": 0.42859768867492676}, "Polygon": [{"X": 0.6600504517555237, "Y": 0.42935821413993835}, {"X": 0.7120473980903625, "Y": 0.42859768867492676}, {"X": 0.7129640579223633, "Y": 0.44422051310539246}, {"X": 0.6608497500419617, "Y": 0.4450109302997589}]}, "Id": "36ee3846-92cb-4af7-a555-daaf50a81fad", "Relationships": [{"Type": "CHILD", "Ids": ["70305e82-acb7-4026-956c-decfceed4363"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "256.71", "Geometry": {"BoundingBox": {"Width": 0.060373060405254364, "Height": 0.016392944380640984, "Left": 0.7646402716636658, "Top": 0.4256192147731781}, "Polygon": [{"X": 0.7646402716636658, "Y": 0.42648249864578247}, {"X": 0.8238500952720642, "Y": 0.4256192147731781}, {"X": 0.825013279914856, "Y": 0.44111502170562744}, {"X": 0.7656713128089905, "Y": 0.44201216101646423}]}, "Id": "614e130f-6cc4-476e-b271-01d27ee4475a", "Relationships": [{"Type": "CHILD", "Ids": ["505a087f-cc83-4476-8c11-f7b4314963f3"]}]}, {"BlockType": "LINE", "Confidence": 92.76566314697266, "Text": "subtotal =", "Geometry": {"BoundingBox": {"Width": 0.10390456020832062, "Height": 0.018456652760505676, "Left": 0.223989799618721, "Top": 0.484774112701416}, "Polygon": [{"X": 0.22420087456703186, "Y": 0.48646900057792664}, {"X": 0.3278535306453705, "Y": 0.484774112701416}, {"X": 0.3278943598270416, "Y": 0.5014725923538208}, {"X": 0.223989799618721, "Y": 0.5032308101654053}]}, "Id": "8c8a4277-9b13-4c26-a86b-c474e36f0158", "Relationships": [{"Type": "CHILD", "Ids": ["8b5a9c9f-5a5a-4654-b002-9da6b0f83dc5", "ea41242e-4b17-45a0-a06b-bbbc6f258325"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "256.71", "Geometry": {"BoundingBox": {"Width": 0.06095096096396446, "Height": 0.016783230006694794, "Left": 0.3704836964607239, "Top": 0.48004716634750366}, "Polygon": [{"X": 0.3704836964607239, "Y": 0.48103299736976624}, {"X": 0.4311596155166626, "Y": 0.48004716634750366}, {"X": 0.43143466114997864, "Y": 0.49580949544906616}, {"X": 0.37062013149261475, "Y": 0.49683037400245667}]}, "Id": "e2c49d15-16a5-4859-b436-dffc7f697457", "Relationships": [{"Type": "CHILD", "Ids": ["ed777540-a53d-4ca8-9477-968fd4659d2a"]}]}, {"BlockType": "LINE", "Confidence": 99.01539611816406, "Text": "sales tax =", "Geometry": {"BoundingBox": {"Width": 0.11570610851049423, "Height": 0.01881137490272522, "Left": 0.2131168395280838, "Top": 0.5301539301872253}, "Polygon": [{"X": 0.2133525013923645, "Y": 0.5322201251983643}, {"X": 0.3287802040576935, "Y": 0.5301539301872253}, {"X": 0.32882294058799744, "Y": 0.5468283295631409}, {"X": 0.2131168395280838, "Y": 0.5489652752876282}]}, "Id": "8c3ffc6f-0b8e-440c-a77e-738da6c56dfb", "Relationships": [{"Type": "CHILD", "Ids": ["9780c0a2-bf61-41cb-8e90-31624416f6f1", "03dd048d-89a0-4457-82dc-57d6eb3ab51e", "94cb0143-231a-426a-80c5-5aeda46d8886"]}]}, {"BlockType": "LINE", "Confidence": 95.8448257446289, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.0414731428027153, "Height": 0.015611699782311916, "Left": 0.3917703926563263, "Top": 0.5255355834960938}, "Polygon": [{"X": 0.3917703926563263, "Y": 0.5262694358825684}, {"X": 0.43298211693763733, "Y": 0.5255355834960938}, {"X": 0.4332435429096222, "Y": 0.5403909087181091}, {"X": 0.39194372296333313, "Y": 0.5411472916603088}]}, "Id": "ed994463-25f1-441a-ad33-9ffd8faf74fe", "Relationships": [{"Type": "CHILD", "Ids": ["e2c67726-156a-4903-b1bb-152e19557e97"]}]}, {"BlockType": "LINE", "Confidence": 98.03641510009766, "Text": "TOTAL =", "Geometry": {"BoundingBox": {"Width": 0.0733472928404808, "Height": 0.01710735633969307, "Left": 0.2544335424900055, "Top": 0.5536593198776245}, "Polygon": [{"X": 0.2545611560344696, "Y": 0.5550279021263123}, {"X": 0.3277430832386017, "Y": 0.5536593198776245}, {"X": 0.3277808427810669, "Y": 0.5693556666374207}, {"X": 0.2544335424900055, "Y": 0.5707666277885437}]}, "Id": "1b0bfed7-9d2f-4385-8074-c18b9101b4fd", "Relationships": [{"Type": "CHILD", "Ids": ["991807f9-8142-44ef-9605-62c6b2b1ae2e", "11f2f472-da17-457d-8bcb-3c70bd5b5044"]}]}, {"BlockType": "LINE", "Confidence": 99.0086898803711, "Text": "256.71 Signature:", "Geometry": {"BoundingBox": {"Width": 0.18867535889148712, "Height": 0.022974682971835136, "Left": 0.3695487976074219, "Top": 0.5447131395339966}, "Polygon": [{"X": 0.3695487976074219, "Y": 0.5481987595558167}, {"X": 0.5575376749038696, "Y": 0.5447131395339966}, {"X": 0.5582241415977478, "Y": 0.5640669465065002}, {"X": 0.36971285939216614, "Y": 0.5676878094673157}]}, "Id": "08a63e3c-3d02-4b18-8f25-22f1d00f42de", "Relationships": [{"Type": "CHILD", "Ids": ["a4722ec7-9149-430f-bb77-b4d07d5cc8fc", "47fa57ce-f852-4675-9225-b181a6cd573f"]}]}, {"BlockType": "LINE", "Confidence": 85.6510238647461, "Text": "Duways I agree to pay total amount", "Geometry": {"BoundingBox": {"Width": 0.31072431802749634, "Height": 0.10635557770729065, "Left": 0.5906084179878235, "Top": 0.49600380659103394}, "Polygon": [{"X": 0.5906084179878235, "Y": 0.5011619329452515}, {"X": 0.8928975462913513, "Y": 0.49600380659103394}, {"X": 0.901332676410675, "Y": 0.5960581302642822}, {"X": 0.5947048664093018, "Y": 0.602359414100647}]}, "Id": "e01c8e91-fd0f-49b9-8e05-798f430fcb8a", "Relationships": [{"Type": "CHILD", "Ids": ["0a632481-e059-4cd7-ab3a-e1fb5d00ec7d", "22fe7761-771b-4b21-94ab-8189dd012ec9", "37d9cc75-111d-4782-9a9e-18083472f24c", "7eb3e988-589b-4c96-bd20-74a99a772435", "49054f2b-8bfd-4341-a899-856ae39cd9da", "23a503e6-0d92-479e-823e-98b775d983eb", "3ac506d4-05ec-4fd4-acb7-4632b3ca1d36"]}]}, {"BlockType": "LINE", "Confidence": 87.11467742919922, "Text": "according to card ssuer agreement.", "Geometry": {"BoundingBox": {"Width": 0.3676043748855591, "Height": 0.02732042968273163, "Left": 0.5721151828765869, "Top": 0.5830371379852295}, "Polygon": [{"X": 0.5721151828765869, "Y": 0.5903988480567932}, {"X": 0.9379537105560303, "Y": 0.5830371379852295}, {"X": 0.9397196173667908, "Y": 0.602723240852356}, {"X": 0.572860062122345, "Y": 0.6103575825691223}]}, "Id": "cc610281-b2a4-483f-b645-d0d42685a14b", "Relationships": [{"Type": "CHILD", "Ids": ["307a0b0b-46dc-46ca-9761-d6b0e31ef77e", "783b8292-8570-4a73-a99d-c6ebaaad8a36", "f546528d-c3de-4eb9-bbc2-be4631cbe804", "3b239721-f532-425c-934b-4b3a10ce512f", "9c7bbe03-c871-4272-86ee-acab8d141bbc"]}]}, {"BlockType": "LINE", "Confidence": 99.05824279785156, "Text": "Salesperson ID: AUTO/SMARTFUEL", "Geometry": {"BoundingBox": {"Width": 0.3206421434879303, "Height": 0.031380604952573776, "Left": 0.13517440855503082, "Top": 0.6424707770347595}, "Polygon": [{"X": 0.13578051328659058, "Y": 0.6494452953338623}, {"X": 0.4553227424621582, "Y": 0.6424707770347595}, {"X": 0.4558165371417999, "Y": 0.6665877103805542}, {"X": 0.13517440855503082, "Y": 0.6738513708114624}]}, "Id": "0a679443-0f0e-4dd9-b0a7-e295689993c3", "Relationships": [{"Type": "CHILD", "Ids": ["13ca361e-15f6-4403-9705-9b64a085a1b7", "815c288c-b5bf-4470-b8c7-153d1355e30b", "dd939183-544b-437e-933d-69649771af18"]}]}, {"BlockType": "LINE", "Confidence": 98.96451568603516, "Text": "Invoice #: 0090760", "Geometry": {"BoundingBox": {"Width": 0.1894308477640152, "Height": 0.02072538249194622, "Left": 0.6188575029373169, "Top": 0.6331351399421692}, "Polygon": [{"X": 0.6188575029373169, "Y": 0.637233555316925}, {"X": 0.8071247935295105, "Y": 0.6331351399421692}, {"X": 0.8082883358001709, "Y": 0.6496443748474121}, {"X": 0.6195842623710632, "Y": 0.6538605690002441}]}, "Id": "6f84a64f-8191-4874-bbd7-205236f6b996", "Relationships": [{"Type": "CHILD", "Ids": ["baf637c5-bfaf-4d54-b2ea-b5828f29e344", "6d3cdca1-326c-4521-be38-207a7b74fa4f", "e14d6195-7b99-4fea-9099-d0fc8c9014c6"]}]}, {"BlockType": "LINE", "Confidence": 99.73127746582031, "Text": "Truck Number: 3482", "Geometry": {"BoundingBox": {"Width": 0.19197483360767365, "Height": 0.02324061095714569, "Left": 0.15612870454788208, "Top": 0.6714648008346558}, "Polygon": [{"X": 0.156540647149086, "Y": 0.6758180260658264}, {"X": 0.3480055034160614, "Y": 0.6714648008346558}, {"X": 0.3481035530567169, "Y": 0.6902176737785339}, {"X": 0.15612870454788208, "Y": 0.6947054266929626}]}, "Id": "bc45eea9-0104-4e48-8625-b458cd44b467", "Relationships": [{"Type": "CHILD", "Ids": ["e3c08a41-3250-408f-9224-b4576599ed76", "9e520eaa-e7fc-4447-ba4e-8f33eaedfc6a", "4e96c056-3823-4761-8948-70780fd2f253"]}]}, {"BlockType": "LINE", "Confidence": 97.93135070800781, "Text": "Merchant Number: *******2817", "Geometry": {"BoundingBox": {"Width": 0.29932719469070435, "Height": 0.02363916113972664, "Left": 0.****************, "Top": 0.656462550163269}, "Polygon": [{"X": 0.****************, "Y": 0.663202166557312}, {"X": 0.8505393266677856, "Y": 0.656462550163269}, {"X": 0.8518158793449402, "Y": 0.6731725931167603}, {"X": 0.****************, "Y": 0.6801016926765442}]}, "Id": "253ed676-3919-4ae4-8116-c8fb68c5e09e", "Relationships": [{"Type": "CHILD", "Ids": ["63f4c3d4-64ce-41fa-9631-607e872d70c7", "e81a64c3-0080-48a1-8c12-b460f17f29c3", "a24b92fc-28ab-4a76-9a72-7023bff5e62d"]}]}, {"BlockType": "LINE", "Confidence": 77.**************, "Text": "Card Number: xxxxxxxxxxxxxx1773 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.39882469177246094, "Height": 0.030908165499567986, "Left": 0.166154146194458, "Top": 0.6880123615264893}, "Polygon": [{"X": 0.16659273207187653, "Y": 0.697346568107605}, {"X": 0.5642201900482178, "Y": 0.6880123615264893}, {"X": 0.564978837966919, "Y": 0.7092664241790771}, {"X": 0.166154146194458, "Y": 0.7189205288887024}]}, "Id": "ca3eea60-502b-43e2-a220-000f7d3fe23b", "Relationships": [{"Type": "CHILD", "Ids": ["e8551b15-502f-4ad2-a80a-5fb13169cd49", "1f569418-fa4c-438e-9f67-35ae7c52cce8", "bf93b0b8-93d8-4bd4-8be4-88becfbb48b8", "430a3141-64d2-48aa-831d-6aaf8a2d5317"]}]}, {"BlockType": "LINE", "Confidence": 91.67366027832031, "Text": "Loyalty Card#: xxxxxxxxxxxx8997 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.034095458686351776, "Left": 0.14253173768520355, "Top": 0.7134917974472046}, "Polygon": [{"X": 0.14310576021671295, "Y": 0.7232123017311096}, {"X": 0.****************, "Y": 0.7134917974472046}, {"X": 0.****************, "Y": 0.7375027537345886}, {"X": 0.14253173768520355, "Y": 0.747587263584137}]}, "Id": "292b5b9c-c027-47cf-983c-9b1e6970d879", "Relationships": [{"Type": "CHILD", "Ids": ["f0fddf9a-8453-4810-ad5f-c7b883ec49dd", "5fa40049-1be5-4619-9da2-0e8b69b64079", "fd562186-5d55-43a0-8886-b219ddabf0f1", "494e9625-e8f9-44a8-863f-77ba8ea1109f"]}]}, {"BlockType": "LINE", "Confidence": 98.79586791992188, "Text": "Billing Company: NBS / KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.03371965512633324, "Left": 0.08830035477876663, "Top": 0.7646784782409668}, "Polygon": [{"X": 0.0890108272433281, "Y": 0.775413453578949}, {"X": 0.5010209083557129, "Y": 0.7646784782409668}, {"X": 0.5016205310821533, "Y": 0.7873077988624573}, {"X": 0.08830035477876663, "Y": 0.7983981370925903}]}, "Id": "36148bdf-c045-4508-817d-18c0b86f9ec6", "Relationships": [{"Type": "CHILD", "Ids": ["9d760565-6fa9-4b7c-9b2e-b8ad9777701a", "1d0a8d7f-cd38-42bd-910d-d37790ae99be", "8779d21c-6403-4076-839c-b63834624961", "23e53adf-d40f-431e-8e5b-caafb4f6d574", "53bcf094-0992-4592-a6d9-b4c515ba9d3a", "dfa76917-63f5-4689-9725-64536c958deb", "89016775-b7cf-46c3-ae7f-c4005cb4b3ee"]}]}, {"BlockType": "LINE", "Confidence": 90.70870208740234, "Text": "THANK YOU! - THANK YOU!", "Geometry": {"BoundingBox": {"Width": 0.2916978895664215, "Height": 0.02673567645251751, "Left": 0.08559971302747726, "Top": 0.8185780644416809}, "Polygon": [{"X": 0.0861794501543045, "Y": 0.8266626596450806}, {"X": 0.3771287798881531, "Y": 0.8185780644416809}, {"X": 0.37729761004447937, "Y": 0.837024450302124}, {"X": 0.08559971302747726, "Y": 0.8453137874603271}]}, "Id": "34cb3c9d-c881-4492-be0e-5eecca44d972", "Relationships": [{"Type": "CHILD", "Ids": ["f22d0603-65c9-4ef4-9d65-6c4e09871fd9", "a6639712-e797-4bfd-8585-64e68adc1833", "b77a26df-522a-45dc-9aef-dc5bda95566b", "10de27e0-c7e1-4f6d-8dc8-c6080d91a266", "331d1fe6-a024-4c8b-b0ab-eccebf3df591"]}]}, {"BlockType": "WORD", "Confidence": 98.8464584350586, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04154516011476517, "Height": 0.015977757051587105, "Left": 0.09632165729999542, "Top": 0.23534734547138214}, "Polygon": [{"X": 0.09682656824588776, "Y": 0.23566709458827972}, {"X": 0.1378668248653412, "Y": 0.23534734547138214}, {"X": 0.13745923340320587, "Y": 0.25098279118537903}, {"X": 0.09632165729999542, "Y": 0.2513251006603241}]}, "Id": "6305e982-efee-42a3-9b5c-9678f781549d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042238540947437286, "Height": 0.015556016936898232, "Left": 0.14728929102420807, "Top": 0.23454035818576813}, "Polygon": [{"X": 0.1476638913154602, "Y": 0.2348659485578537}, {"X": 0.18952783942222595, "Y": 0.23454035818576813}, {"X": 0.189249649643898, "Y": 0.2497483789920807}, {"X": 0.14728929102420807, "Y": 0.2500963807106018}]}, "Id": "60cb8bb5-807c-44cc-9953-ae6f2e7c580f"}, {"BlockType": "WORD", "Confidence": 98.3826904296875, "Text": "1281", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0412188284099102, "Height": 0.016410166397690773, "Left": 0.20187370479106903, "Top": 0.235290989279747}, "Polygon": [{"X": 0.20213742554187775, "Y": 0.23561112582683563}, {"X": 0.24309253692626953, "Y": 0.235290989279747}, {"X": 0.24292825162410736, "Y": 0.2513577938079834}, {"X": 0.20187370479106903, "Y": 0.25170114636421204}]}, "Id": "b45cc05c-ef50-4d0c-b1a4-15b0baa86f33"}, {"BlockType": "WORD", "Confidence": 82.62934875488281, "Text": "/106", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0399223193526268, "Height": 0.015350538305938244, "Left": 0.2569865584373474, "Top": 0.23863908648490906}, "Polygon": [{"X": 0.2571084797382355, "Y": 0.2389553189277649}, {"X": 0.2969088852405548, "Y": 0.23863908648490906}, {"X": 0.29687702655792236, "Y": 0.25365227460861206}, {"X": 0.2569865584373474, "Y": 0.25398963689804077}]}, "Id": "9dee8351-a3cd-4904-9396-deaedf808d4a"}, {"BlockType": "WORD", "Confidence": 97.72879791259766, "Text": "N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017780518159270287, "Height": 0.014088022522628307, "Left": 0.30783331394195557, "Top": 0.23912936449050903}, "Polygon": [{"X": 0.307839959859848, "Y": 0.23927077651023865}, {"X": 0.32558324933052063, "Y": 0.23912936449050903}, {"X": 0.3256138265132904, "Y": 0.2530672252178192}, {"X": 0.30783331394195557, "Y": 0.2532173693180084}]}, "Id": "58a6446a-1481-44c3-8a9a-158901264098"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "ROYAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05023320019245148, "Height": 0.01481151394546032, "Left": 0.3387475907802582, "Top": 0.23827534914016724}, "Polygon": [{"X": 0.3387475907802582, "Y": 0.23867376148700714}, {"X": 0.3888123035430908, "Y": 0.23827534914016724}, {"X": 0.38898077607154846, "Y": 0.25266289710998535}, {"X": 0.33880776166915894, "Y": 0.2530868649482727}]}, "Id": "40744896-9840-47a2-a29e-54086e3f79b2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AVENUE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06052936613559723, "Height": 0.01573888771235943, "Left": 0.3998250365257263, "Top": 0.2361535131931305}, "Polygon": [{"X": 0.3998250365257263, "Y": 0.23662927746772766}, {"X": 0.4600127339363098, "Y": 0.2361535131931305}, {"X": 0.46035438776016235, "Y": 0.25138404965400696}, {"X": 0.40002912282943726, "Y": 0.25189241766929626}]}, "Id": "23134533-4d32-461f-a2b9-943912d7394e"}, {"BlockType": "WORD", "Confidence": 77.14505004882812, "Text": "ABELGIUM.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08724050968885422, "Height": 0.017676498740911484, "Left": 0.47195759415626526, "Top": 0.2327091544866562}, "Polygon": [{"X": 0.47195759415626526, "Y": 0.2333858162164688}, {"X": 0.558566153049469, "Y": 0.2327091544866562}, {"X": 0.5591980814933777, "Y": 0.24965663254261017}, {"X": 0.4723697006702423, "Y": 0.2503856420516968}]}, "Id": "5732d3ec-4e2b-4ccc-b767-9f541d4b5d59"}, {"BlockType": "WORD", "Confidence": 99.56472778320312, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020399080589413643, "Height": 0.01476062461733818, "Left": 0.5714011192321777, "Top": 0.23140186071395874}, "Polygon": [{"X": 0.5714011192321777, "Y": 0.23155571520328522}, {"X": 0.5911840796470642, "Y": 0.23140186071395874}, {"X": 0.5918002128601074, "Y": 0.2459983378648758}, {"X": 0.5719741582870483, "Y": 0.24616248905658722}]}, "Id": "73125346-d990-4d16-b69b-9e4748ce0758"}, {"BlockType": "WORD", "Confidence": 93.94790649414062, "Text": "53004/(262)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11088307201862335, "Height": 0.018568148836493492, "Left": 0.6025761365890503, "Top": 0.22677277028560638}, "Polygon": [{"X": 0.6025761365890503, "Y": 0.22761276364326477}, {"X": 0.7123906016349792, "Y": 0.22677277028560638}, {"X": 0.7134591937065125, "Y": 0.24443145096302032}, {"X": 0.603355348110199, "Y": 0.24534091353416443}]}, "Id": "7bdf9286-884d-4ac2-87de-85742454249c"}, {"BlockType": "WORD", "Confidence": 91.00128173828125, "Text": "988-7024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07907254993915558, "Height": 0.016289053484797478, "Left": 0.7247300744056702, "Top": 0.2260054051876068}, "Polygon": [{"X": 0.7247300744056702, "Y": 0.22660118341445923}, {"X": 0.8026425242424011, "Y": 0.2260054051876068}, {"X": 0.8038026094436646, "Y": 0.2416549026966095}, {"X": 0.7257089614868164, "Y": 0.24229446053504944}]}, "Id": "ef2c4980-b066-4716-b5d8-5ec8064d0646"}, {"BlockType": "WORD", "Confidence": 98.81477355957031, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07139934599399567, "Height": 0.016085531562566757, "Left": 0.0954282358288765, "Top": 0.2579490542411804}, "Polygon": [{"X": 0.09592772275209427, "Y": 0.2585563361644745}, {"X": 0.16682758927345276, "Y": 0.2579490542411804}, {"X": 0.16649356484413147, "Y": 0.2733886241912842}, {"X": 0.0954282358288765, "Y": 0.2740345597267151}]}, "Id": "8318d390-e58f-41c9-b1a4-a84ba397ebbf"}, {"BlockType": "WORD", "Confidence": 98.58258819580078, "Text": "0900-0760", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0974552184343338, "Height": 0.019454030320048332, "Left": 0.18000440299510956, "Top": 0.2570430040359497}, "Polygon": [{"X": 0.18036943674087524, "Y": 0.2578747868537903}, {"X": 0.27745962142944336, "Y": 0.2570430040359497}, {"X": 0.27736619114875793, "Y": 0.27560141682624817}, {"X": 0.18000440299510956, "Y": 0.2764970362186432}]}, "Id": "1300e6c9-7d54-4cfe-b21a-33fd8e73e874"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "05/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10294143855571747, "Height": 0.016636677086353302, "Left": 0.29808223247528076, "Top": 0.2591218054294586}, "Polygon": [{"X": 0.2981126606464386, "Y": 0.2600126564502716}, {"X": 0.4008122682571411, "Y": 0.2591218054294586}, {"X": 0.4010236859321594, "Y": 0.2748103141784668}, {"X": 0.29808223247528076, "Y": 0.2757585048675537}]}, "Id": "bd73647e-ee8f-417a-905d-58f05d03edf9"}, {"BlockType": "WORD", "Confidence": 98.87416076660156, "Text": "Station:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0785166546702385, "Height": 0.016846299171447754, "Left": 0.4207010567188263, "Top": 0.2573269307613373}, "Polygon": [{"X": 0.4207010567188263, "Y": 0.25800150632858276}, {"X": 0.4987632930278778, "Y": 0.2573269307613373}, {"X": 0.4992177188396454, "Y": 0.2734536826610565}, {"X": 0.4209672510623932, "Y": 0.27417322993278503}]}, "Id": "88a74add-bcec-41b3-83ce-edc1eeaa4c33"}, {"BlockType": "WORD", "Confidence": 98.3603744506836, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009762313216924667, "Height": 0.014239287935197353, "Left": 0.5118802189826965, "Top": 0.2568690776824951}, "Polygon": [{"X": 0.5118802189826965, "Y": 0.2569494843482971}, {"X": 0.5211960673332214, "Y": 0.2568690776824951}, {"X": 0.5216425657272339, "Y": 0.2710232436656952}, {"X": 0.5123071074485779, "Y": 0.27110835909843445}]}, "Id": "31f5ed81-29bb-478e-9912-90d6d301d38f"}, {"BlockType": "WORD", "Confidence": 98.45603942871094, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09401880949735641, "Height": 0.01815842092037201, "Left": 0.09553181380033493, "Top": 0.3046240210533142}, "Polygon": [{"X": 0.09608286619186401, "Y": 0.30557340383529663}, {"X": 0.18955062329769135, "Y": 0.3046240210533142}, {"X": 0.1892402023077011, "Y": 0.32177606225013733}, {"X": 0.09553181380033493, "Y": 0.3227824568748474}]}, "Id": "15635cf1-a8c8-4689-8fdd-6272c7859797"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08527715504169464, "Height": 0.017350738868117332, "Left": 0.2024974524974823, "Top": 0.3048374354839325}, "Polygon": [{"X": 0.20276327431201935, "Y": 0.30570441484451294}, {"X": 0.28777462244033813, "Y": 0.3048374354839325}, {"X": 0.2877177298069, "Y": 0.3212713301181793}, {"X": 0.2024974524974823, "Y": 0.3221881687641144}]}, "Id": "ea3961ec-0b4a-425a-89a0-b3053279e877"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04195493087172508, "Height": 0.015387801453471184, "Left": 0.298327773809433, "Top": 0.30522412061691284}, "Polygon": [{"X": 0.29835593700408936, "Y": 0.30565235018730164}, {"X": 0.3402176797389984, "Y": 0.30522412061691284}, {"X": 0.34028270840644836, "Y": 0.3201613128185272}, {"X": 0.298327773809433, "Y": 0.3206119239330292}]}, "Id": "7a6940ed-fb29-44bf-bbff-ede03970e117"}, {"BlockType": "WORD", "Confidence": 98.68522644042969, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049082718789577484, "Height": 0.015234915539622307, "Left": 0.3500320315361023, "Top": 0.3041122555732727}, "Polygon": [{"X": 0.3500320315361023, "Y": 0.30461153388023376}, {"X": 0.39892202615737915, "Y": 0.3041122555732727}, {"X": 0.3991147577762604, "Y": 0.31882214546203613}, {"X": 0.3501177430152893, "Y": 0.31934717297554016}]}, "Id": "0011e896-937d-49f9-9af0-494bad660133"}, {"BlockType": "WORD", "Confidence": 98.59912872314453, "Text": "P00174", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06027667969465256, "Height": 0.015157327987253666, "Left": 0.41132718324661255, "Top": 0.30262085795402527}, "Polygon": [{"X": 0.41132718324661255, "Y": 0.3032313287258148}, {"X": 0.47125712037086487, "Y": 0.30262085795402527}, {"X": 0.4716038703918457, "Y": 0.3171364963054657}, {"X": 0.41154471039772034, "Y": 0.31777817010879517}]}, "Id": "974f5395-c01b-4354-b8e8-7191da4c2641"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "LOYALTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07581927627325058, "Height": 0.016870804131031036, "Left": 0.09395384788513184, "Top": 0.34976550936698914}, "Polygon": [{"X": 0.09446631371974945, "Y": 0.3506452739238739}, {"X": 0.169773131608963, "Y": 0.34976550936698914}, {"X": 0.16943974792957306, "Y": 0.36571362614631653}, {"X": 0.09395384788513184, "Y": 0.3666363060474396}]}, "Id": "44674e40-2d63-4e76-a1c6-4c03ba8cc18a"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09407173842191696, "Height": 0.01975017599761486, "Left": 0.18069584667682648, "Top": 0.34998348355293274}, "Polygon": [{"X": 0.1810545027256012, "Y": 0.3510828912258148}, {"X": 0.27476757764816284, "Y": 0.34998348355293274}, {"X": 0.274667888879776, "Y": 0.36857175827026367}, {"X": 0.18069584667682648, "Y": 0.36973366141319275}]}, "Id": "a601d64f-7189-4eb9-b482-1c6f7aa53646"}, {"BlockType": "WORD", "Confidence": 62.3626823425293, "Text": "APPROVED.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09042812883853912, "Height": 0.017612427473068237, "Left": 0.28779420256614685, "Top": 0.34810784459114075}, "Polygon": [{"X": 0.287850946187973, "Y": 0.34916403889656067}, {"X": 0.3780584931373596, "Y": 0.34810784459114075}, {"X": 0.37822234630584717, "Y": 0.3646104633808136}, {"X": 0.28779420256614685, "Y": 0.365720272064209}]}, "Id": "7b99b851-90d7-47fc-aa84-6d9be5a5fdfb"}, {"BlockType": "WORD", "Confidence": 99.47206115722656, "Text": "You", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03068654052913189, "Height": 0.014257971197366714, "Left": 0.3904471695423126, "Top": 0.34763169288635254}, "Polygon": [{"X": 0.3904471695423126, "Y": 0.3479883670806885}, {"X": 0.42090773582458496, "Y": 0.34763169288635254}, {"X": 0.42113369703292847, "Y": 0.3615177571773529}, {"X": 0.3906106650829315, "Y": 0.36188966035842896}]}, "Id": "d9c994ac-f39e-462c-8512-74bce0deded0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "earned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06053835153579712, "Height": 0.016425181180238724, "Left": 0.4319142997264862, "Top": 0.3444778621196747}, "Polygon": [{"X": 0.4319142997264862, "Y": 0.34517702460289}, {"X": 0.49203184247016907, "Y": 0.3444778621196747}, {"X": 0.4924526512622833, "Y": 0.36016979813575745}, {"X": 0.4321959614753723, "Y": 0.3609030544757843}]}, "Id": "e9bf57e1-8a3d-4a7f-9498-20e1efdb6437"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "1.64", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039510250091552734, "Height": 0.01562935672700405, "Left": 0.5043970942497253, "Top": 0.3426555395126343}, "Polygon": [{"X": 0.5043970942497253, "Y": 0.34310734272003174}, {"X": 0.5433855056762695, "Y": 0.3426555395126343}, {"X": 0.5439073443412781, "Y": 0.35781174898147583}, {"X": 0.5048320293426514, "Y": 0.3582848906517029}]}, "Id": "512c7f03-2809-49cb-8630-fdd62300cc34"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05115122348070145, "Height": 0.01593867316842079, "Left": 0.5542978644371033, "Top": 0.3416648805141449}, "Polygon": [{"X": 0.5542978644371033, "Y": 0.34224939346313477}, {"X": 0.6047818660736084, "Y": 0.3416648805141449}, {"X": 0.6054490804672241, "Y": 0.3569910228252411}, {"X": 0.5548514127731323, "Y": 0.3576035499572754}]}, "Id": "6bd989cf-47a5-4203-9932-5cba1a1efe6c"}, {"BlockType": "WORD", "Confidence": 98.87496185302734, "Text": "points!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06908669322729111, "Height": 0.019123349338769913, "Left": 0.6161471605300903, "Top": 0.3385480046272278}, "Polygon": [{"X": 0.6161471605300903, "Y": 0.3393310010433197}, {"X": 0.6842214465141296, "Y": 0.3385480046272278}, {"X": 0.6852338314056396, "Y": 0.35684308409690857}, {"X": 0.616976797580719, "Y": 0.3576713502407074}]}, "Id": "a46397fa-9ce5-49da-b330-a7d441bbc543"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Drink", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05066246911883354, "Height": 0.015069972723722458, "Left": 0.6986590027809143, "Top": 0.3369392454624176}, "Polygon": [{"X": 0.6986590027809143, "Y": 0.3375096619129181}, {"X": 0.7483826279640198, "Y": 0.3369392454624176}, {"X": 0.749321460723877, "Y": 0.3514125645160675}, {"X": 0.6994924545288086, "Y": 0.35200920701026917}]}, "Id": "8a3cee00-ef61-4b3f-b5c9-09824c19f919"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "Credit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060239166021347046, "Height": 0.015454284846782684, "Left": 0.7596796154975891, "Top": 0.3364548683166504}, "Polygon": [{"X": 0.7596796154975891, "Y": 0.33713388442993164}, {"X": 0.818808376789093, "Y": 0.3364548683166504}, {"X": 0.8199187517166138, "Y": 0.35119834542274475}, {"X": 0.7606626749038696, "Y": 0.35190916061401367}]}, "Id": "ee8e5ae6-31ec-462d-90e6-8c2e60e75410"}, {"BlockType": "WORD", "Confidence": 98.97421264648438, "Text": "Earned!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.071216881275177, "Height": 0.01606774888932705, "Left": 0.8289059996604919, "Top": 0.335499107837677}, "Polygon": [{"X": 0.8289059996604919, "Y": 0.3363015949726105}, {"X": 0.8987953662872314, "Y": 0.335499107837677}, {"X": 0.900122880935669, "Y": 0.3507254421710968}, {"X": 0.8300785422325134, "Y": 0.3515668511390686}]}, "Id": "59b1f576-7dba-49ec-9a9f-bd7599873443"}, {"BlockType": "WORD", "Confidence": 96.1804428100586, "Text": "E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0114152692258358, "Height": 0.014525342732667923, "Left": 0.9094154834747314, "Top": 0.3349702060222626}, "Polygon": [{"X": 0.9094154834747314, "Y": 0.33508622646331787}, {"X": 0.9195305109024048, "Y": 0.3349702060222626}, {"X": 0.9208307862281799, "Y": 0.3493741750717163}, {"X": 0.9106945991516113, "Y": 0.349495530128479}]}, "Id": "a41491e0-28df-43b6-bf43-405eec56a274"}, {"BlockType": "WORD", "Confidence": 98.85462951660156, "Text": "xpires", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0636158213019371, "Height": 0.018899695947766304, "Left": 0.09540756046772003, "Top": 0.37320590019226074}, "Polygon": [{"X": 0.09598198533058167, "Y": 0.3739922344684601}, {"X": 0.15902338922023773, "Y": 0.37320590019226074}, {"X": 0.1586182415485382, "Y": 0.3912784159183502}, {"X": 0.09540756046772003, "Y": 0.3921056091785431}]}, "Id": "56f2603d-3e70-4d40-aa96-ce8d1e631706"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020006123930215836, "Height": 0.015744805335998535, "Left": 0.17140117287635803, "Top": 0.3730691373348236}, "Polygon": [{"X": 0.17171959578990936, "Y": 0.37331488728523254}, {"X": 0.19140729308128357, "Y": 0.3730691373348236}, {"X": 0.19113406538963318, "Y": 0.3885572552680969}, {"X": 0.17140117287635803, "Y": 0.38881394267082214}]}, "Id": "133ebcb3-0ddc-48f2-9f3c-154ff8893413"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "24", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021311022341251373, "Height": 0.01551031693816185, "Left": 0.20355954766273499, "Top": 0.373657763004303}, "Polygon": [{"X": 0.20380061864852905, "Y": 0.3739214539527893}, {"X": 0.22487057745456696, "Y": 0.373657763004303}, {"X": 0.22467702627182007, "Y": 0.3888928294181824}, {"X": 0.20355954766273499, "Y": 0.38916805386543274}]}, "Id": "d8a52fd9-dfc5-48e7-9ebb-c4ec33d053f0"}, {"BlockType": "WORD", "Confidence": 97.5791244506836, "Text": "Hours.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059102047234773636, "Height": 0.01574859954416752, "Left": 0.23609209060668945, "Top": 0.3728708028793335}, "Polygon": [{"X": 0.23625758290290833, "Y": 0.37360867857933044}, {"X": 0.295194149017334, "Y": 0.3728708028793335}, {"X": 0.2951592206954956, "Y": 0.3878497779369354}, {"X": 0.23609209060668945, "Y": 0.38861942291259766}]}, "Id": "d27ff9e9-156d-4d79-8c32-993de9f9993a"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06107744202017784, "Height": 0.015451626852154732, "Left": 0.30877432227134705, "Top": 0.37055742740631104}, "Polygon": [{"X": 0.30877912044525146, "Y": 0.3713175654411316}, {"X": 0.36972472071647644, "Y": 0.37055742740631104}, {"X": 0.3698517680168152, "Y": 0.38521668314933777}, {"X": 0.30877432227134705, "Y": 0.3860090672969818}]}, "Id": "7c97a49c-8f8b-49a5-a385-e2ad1e5939c0"}, {"BlockType": "WORD", "Confidence": 60.186744689941406, "Text": "C", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010291021317243576, "Height": 0.011034799739718437, "Left": 0.38157063722610474, "Top": 0.3732730746269226}, "Polygon": [{"X": 0.38157063722610474, "Y": 0.37340086698532104}, {"X": 0.3917318284511566, "Y": 0.3732730746269226}, {"X": 0.3918616473674774, "Y": 0.384176105260849}, {"X": 0.38168415427207947, "Y": 0.3843078911304474}]}, "Id": "d63767bc-fca7-407c-bb72-02a5bc7f5552"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044174861162900925, "Height": 0.016635917127132416, "Left": 0.09379877150058746, "Top": 0.4213407337665558}, "Polygon": [{"X": 0.09430718421936035, "Y": 0.42195624113082886}, {"X": 0.1379736363887787, "Y": 0.4213407337665558}, {"X": 0.13756826519966125, "Y": 0.43733593821525574}, {"X": 0.09379877150058746, "Y": 0.4379766285419464}]}, "Id": "dbd906ae-262d-4add-b5a9-390715c81e11"}, {"BlockType": "WORD", "Confidence": 99.56314086914062, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08289223909378052, "Height": 0.016357051208615303, "Left": 0.30780139565467834, "Top": 0.41456660628318787}, "Polygon": [{"X": 0.307808518409729, "Y": 0.41572335362434387}, {"X": 0.3905169665813446, "Y": 0.41456660628318787}, {"X": 0.39069363474845886, "Y": 0.4297213554382324}, {"X": 0.30780139565467834, "Y": 0.4309236407279968}]}, "Id": "b5af164b-2afa-4390-9fe4-bc1bdcd726b8"}, {"BlockType": "WORD", "Confidence": 99.73233795166016, "Text": "Reefer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06098833680152893, "Height": 0.016346322372555733, "Left": 0.4221758246421814, "Top": 0.4111853539943695}, "Polygon": [{"X": 0.4221758246421814, "Y": 0.41202855110168457}, {"X": 0.48277440667152405, "Y": 0.4111853539943695}, {"X": 0.4831641614437103, "Y": 0.4266543686389923}, {"X": 0.42242860794067383, "Y": 0.4275316894054413}]}, "Id": "bebd9c40-848b-4d96-a9d7-133eaa6ab35f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08136990666389465, "Height": 0.019544105976819992, "Left": 0.5152196884155273, "Top": 0.4076913595199585}, "Polygon": [{"X": 0.5152196884155273, "Y": 0.40880754590034485}, {"X": 0.5958215594291687, "Y": 0.4076913595199585}, {"X": 0.5965895652770996, "Y": 0.4260651171207428}, {"X": 0.5157718658447266, "Y": 0.4272354543209076}]}, "Id": "066f8d1b-2eef-419f-bd58-b95018f9cf54"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04131040349602699, "Height": 0.015593472868204117, "Left": 0.6167734265327454, "Top": 0.40700963139533997}, "Polygon": [{"X": 0.6167734265327454, "Y": 0.40757137537002563}, {"X": 0.6573209166526794, "Y": 0.40700963139533997}, {"X": 0.6580838561058044, "Y": 0.4220190644264221}, {"X": 0.6174479722976685, "Y": 0.4226031005382538}]}, "Id": "ad25e323-058e-4618-946b-3c8e9b28a988"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0422695092856884, "Height": 0.016035398468375206, "Left": 0.6691814064979553, "Top": 0.4056236445903778}, "Polygon": [{"X": 0.6691814064979553, "Y": 0.4061957597732544}, {"X": 0.7105453014373779, "Y": 0.4056236445903778}, {"X": 0.7114509344100952, "Y": 0.42106348276138306}, {"X": 0.6699944734573364, "Y": 0.42165902256965637}]}, "Id": "e06c5632-2ef1-4cbd-953e-6db5b1d8e8c7"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04852968081831932, "Height": 0.015722179785370827, "Left": 0.7737860083580017, "Top": 0.40308552980422974}, "Polygon": [{"X": 0.7737860083580017, "Y": 0.4037395417690277}, {"X": 0.8211886286735535, "Y": 0.40308552980422974}, {"X": 0.8223156929016113, "Y": 0.41812747716903687}, {"X": 0.7748099565505981, "Y": 0.418807715177536}]}, "Id": "63e36399-c51b-44a7-acd2-b4eddb112067"}, {"BlockType": "WORD", "Confidence": 60.259883880615234, "Text": "#2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0334499254822731, "Height": 0.016636015847325325, "Left": 0.09460008889436722, "Top": 0.4444902241230011}, "Polygon": [{"X": 0.09510887414216995, "Y": 0.4449802339076996}, {"X": 0.12805001437664032, "Y": 0.4444902241230011}, {"X": 0.12761934101581573, "Y": 0.4606170058250427}, {"X": 0.09460008889436722, "Y": 0.4611262381076813}]}, "Id": "ed62dbd8-0a8d-4141-b984-57e9c07cb069"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05972808599472046, "Height": 0.01620684750378132, "Left": 0.3084762394428253, "Top": 0.4376530945301056}, "Polygon": [{"X": 0.308481901884079, "Y": 0.4385329484939575}, {"X": 0.36807680130004883, "Y": 0.4376530945301056}, {"X": 0.3682043254375458, "Y": 0.45294690132141113}, {"X": 0.3084762394428253, "Y": 0.45385992527008057}]}, "Id": "e6b935ba-a2b3-4efe-b6b6-4daf6709a5d5"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010916424915194511, "Height": 0.013933510519564152, "Left": 0.38114261627197266, "Top": 0.43725526332855225}, "Polygon": [{"X": 0.38114261627197266, "Y": 0.4374140202999115}, {"X": 0.3918962776660919, "Y": 0.43725526332855225}, {"X": 0.3920590281486511, "Y": 0.4510246217250824}, {"X": 0.38128378987312317, "Y": 0.4511887729167938}]}, "Id": "19edf889-a340-4f3f-ae28-8ad82f7d567e"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009917051531374454, "Height": 0.014264615252614021, "Left": 0.44316932559013367, "Top": 0.43468478322029114}, "Polygon": [{"X": 0.44316932559013367, "Y": 0.4348263144493103}, {"X": 0.45279374718666077, "Y": 0.43468478322029114}, {"X": 0.45308637619018555, "Y": 0.4488029181957245}, {"X": 0.4434421956539154, "Y": 0.44894939661026}]}, "Id": "d2201fe4-6fe0-4a57-9571-52d053ba9b3c"}, {"BlockType": "WORD", "Confidence": 99.27515411376953, "Text": "82.836", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06126062944531441, "Height": 0.01540230680257082, "Left": 0.5368056297302246, "Top": 0.43075624108314514}, "Polygon": [{"X": 0.5368056297302246, "Y": 0.43164440989494324}, {"X": 0.5974594354629517, "Y": 0.43075624108314514}, {"X": 0.5980662107467651, "Y": 0.4452382028102875}, {"X": 0.5372849106788635, "Y": 0.4461585581302643}]}, "Id": "3467b664-ac13-4c37-b4f4-75caec81a230"}, {"BlockType": "WORD", "Confidence": 97.13169860839844, "Text": "3.099", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0529136061668396, "Height": 0.016413262113928795, "Left": 0.6600504517555237, "Top": 0.42859768867492676}, "Polygon": [{"X": 0.6600504517555237, "Y": 0.42935821413993835}, {"X": 0.7120473980903625, "Y": 0.42859768867492676}, {"X": 0.7129640579223633, "Y": 0.44422051310539246}, {"X": 0.6608497500419617, "Y": 0.4450109302997589}]}, "Id": "70305e82-acb7-4026-956c-decfceed4363"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "256.71", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060373060405254364, "Height": 0.016392944380640984, "Left": 0.7646402716636658, "Top": 0.4256192147731781}, "Polygon": [{"X": 0.7646402716636658, "Y": 0.42648249864578247}, {"X": 0.8238500952720642, "Y": 0.4256192147731781}, {"X": 0.825013279914856, "Y": 0.44111502170562744}, {"X": 0.7656713128089905, "Y": 0.44201216101646423}]}, "Id": "505a087f-cc83-4476-8c11-f7b4314963f3"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08136315643787384, "Height": 0.01808873750269413, "Left": 0.223989799618721, "Top": 0.4851420521736145}, "Polygon": [{"X": 0.22420087456703186, "Y": 0.48646900057792664}, {"X": 0.30535295605659485, "Y": 0.4851420521736145}, {"X": 0.3053392469882965, "Y": 0.5018542408943176}, {"X": 0.223989799618721, "Y": 0.5032308101654053}]}, "Id": "8b5a9c9f-5a5a-4654-b002-9da6b0f83dc5"}, {"BlockType": "WORD", "Confidence": 85.69973754882812, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010541156865656376, "Height": 0.007857055403292179, "Left": 0.31733742356300354, "Top": 0.48733463883399963}, "Polygon": [{"X": 0.31733742356300354, "Y": 0.4875076115131378}, {"X": 0.3278597891330719, "Y": 0.48733463883399963}, {"X": 0.32787856459617615, "Y": 0.4950157701969147}, {"X": 0.3173444867134094, "Y": 0.49519169330596924}]}, "Id": "ea41242e-4b17-45a0-a06b-bbbc6f258325"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "256.71", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06095096096396446, "Height": 0.016783230006694794, "Left": 0.3704836964607239, "Top": 0.48004716634750366}, "Polygon": [{"X": 0.3704836964607239, "Y": 0.48103299736976624}, {"X": 0.4311596155166626, "Y": 0.48004716634750366}, {"X": 0.43143466114997864, "Y": 0.49580949544906616}, {"X": 0.37062013149261475, "Y": 0.49683037400245667}]}, "Id": "ed777540-a53d-4ca8-9477-968fd4659d2a"}, {"BlockType": "WORD", "Confidence": 99.118896484375, "Text": "sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052324339747428894, "Height": 0.015864048153162003, "Left": 0.2131168395280838, "Top": 0.5331012606620789}, "Polygon": [{"X": 0.21332693099975586, "Y": 0.5340373516082764}, {"X": 0.2654411792755127, "Y": 0.5331012606620789}, {"X": 0.2653433680534363, "Y": 0.548000693321228}, {"X": 0.2131168395280838, "Y": 0.5489652752876282}]}, "Id": "9780c0a2-bf61-41cb-8e90-31624416f6f1"}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030807942152023315, "Height": 0.01577218621969223, "Left": 0.27682429552078247, "Top": 0.5305324792861938}, "Polygon": [{"X": 0.276899129152298, "Y": 0.5310826301574707}, {"X": 0.3076322376728058, "Y": 0.5305324792861938}, {"X": 0.30762484669685364, "Y": 0.5457373857498169}, {"X": 0.27682429552078247, "Y": 0.5463046431541443}]}, "Id": "03dd048d-89a0-4457-82dc-57d6eb3ab51e"}, {"BlockType": "WORD", "Confidence": 98.17223358154297, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010656846687197685, "Height": 0.00798874907195568, "Left": 0.3181498646736145, "Top": 0.5327064990997314}, "Polygon": [{"X": 0.3181498646736145, "Y": 0.5328978300094604}, {"X": 0.3287867605686188, "Y": 0.5327064990997314}, {"X": 0.32880672812461853, "Y": 0.5405008792877197}, {"X": 0.3181579113006592, "Y": 0.5406952500343323}]}, "Id": "94cb0143-231a-426a-80c5-5aeda46d8886"}, {"BlockType": "WORD", "Confidence": 95.8448257446289, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0414731428027153, "Height": 0.015611699782311916, "Left": 0.3917703926563263, "Top": 0.5255355834960938}, "Polygon": [{"X": 0.3917703926563263, "Y": 0.5262694358825684}, {"X": 0.43298211693763733, "Y": 0.5255355834960938}, {"X": 0.4332435429096222, "Y": 0.5403909087181091}, {"X": 0.39194372296333313, "Y": 0.5411472916603088}]}, "Id": "e2c67726-156a-4903-b1bb-152e19557e97"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051708854734897614, "Height": 0.016703389585018158, "Left": 0.2544335424900055, "Top": 0.5540632605552673}, "Polygon": [{"X": 0.2545611560344696, "Y": 0.5550279021263123}, {"X": 0.3061423897743225, "Y": 0.5540632605552673}, {"X": 0.30613142251968384, "Y": 0.5697721242904663}, {"X": 0.2544335424900055, "Y": 0.5707666277885437}]}, "Id": "991807f9-8142-44ef-9605-62c6b2b1ae2e"}, {"BlockType": "WORD", "Confidence": 96.12165069580078, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010762915946543217, "Height": 0.00818227231502533, "Left": 0.3170050084590912, "Top": 0.5560145378112793}, "Polygon": [{"X": 0.3170050084590912, "Y": 0.556216299533844}, {"X": 0.32774874567985535, "Y": 0.5560145378112793}, {"X": 0.327767938375473, "Y": 0.5639918446540833}, {"X": 0.31701189279556274, "Y": 0.5641968250274658}]}, "Id": "11f2f472-da17-457d-8bcb-3c70bd5b5044"}, {"BlockType": "WORD", "Confidence": 99.20679473876953, "Text": "256.71", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062373772263526917, "Height": 0.016948308795690536, "Left": 0.3695627748966217, "Top": 0.5487051010131836}, "Polygon": [{"X": 0.3695627748966217, "Y": 0.5498600602149963}, {"X": 0.4316631853580475, "Y": 0.5487051010131836}, {"X": 0.4319365322589874, "Y": 0.5644622445106506}, {"X": 0.3696957230567932, "Y": 0.5656534433364868}]}, "Id": "a4722ec7-9149-430f-bb77-b4d07d5cc8fc"}, {"BlockType": "WORD", "Confidence": 98.81059265136719, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10378039628267288, "Height": 0.021339500322937965, "Left": 0.4544437527656555, "Top": 0.5447131395339966}, "Polygon": [{"X": 0.4544437527656555, "Y": 0.5466246604919434}, {"X": 0.5575376749038696, "Y": 0.5447131395339966}, {"X": 0.5582241415977478, "Y": 0.5640669465065002}, {"X": 0.45484456419944763, "Y": 0.5660526156425476}]}, "Id": "47fa57ce-f852-4675-9225-b181a6cd573f"}, {"BlockType": "WORD", "Confidence": 20.996492385864258, "Text": "Duways", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.233414888381958, "Height": 0.10505492240190506, "Left": 0.5906084179878235, "Top": 0.4973044693470001}, "Polygon": [{"X": 0.5906084179878235, "Y": 0.5011619329452515}, {"X": 0.8166733384132385, "Y": 0.4973044693470001}, {"X": 0.8240233063697815, "Y": 0.5976468324661255}, {"X": 0.5947048664093018, "Y": 0.602359414100647}]}, "Id": "0a632481-e059-4cd7-ab3a-e1fb5d00ec7d"}, {"BlockType": "WORD", "Confidence": 97.66361999511719, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0072471448220312595, "Height": 0.014958173036575317, "Left": 0.6165135502815247, "Top": 0.5660053491592407}, "Polygon": [{"X": 0.6165135502815247, "Y": 0.5661325454711914}, {"X": 0.6230970025062561, "Y": 0.5660053491592407}, {"X": 0.6237607002258301, "Y": 0.5808327198028564}, {"X": 0.6171634197235107, "Y": 0.5809635519981384}]}, "Id": "22fe7761-771b-4b21-94ab-8189dd012ec9"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "agree", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05187453702092171, "Height": 0.01548176258802414, "Left": 0.636297345161438, "Top": 0.5687560439109802}, "Polygon": [{"X": 0.636297345161438, "Y": 0.5697501301765442}, {"X": 0.6873915791511536, "Y": 0.5687560439109802}, {"X": 0.688171923160553, "Y": 0.58321613073349}, {"X": 0.636972963809967, "Y": 0.584237813949585}]}, "Id": "37d9cc75-111d-4782-9a9e-18083472f24c"}, {"BlockType": "WORD", "Confidence": 99.7265625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02017712965607643, "Height": 0.01481548510491848, "Left": 0.6995323896408081, "Top": 0.5651310086250305}, "Polygon": [{"X": 0.6995323896408081, "Y": 0.5655051469802856}, {"X": 0.7188652157783508, "Y": 0.5651310086250305}, {"X": 0.7197095155715942, "Y": 0.5795619487762451}, {"X": 0.7003371715545654, "Y": 0.5799465179443359}]}, "Id": "7eb3e988-589b-4c96-bd20-74a99a772435"}, {"BlockType": "WORD", "Confidence": 81.89472961425781, "Text": "pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029906218871474266, "Height": 0.015622229315340519, "Left": 0.7312007546424866, "Top": 0.5677482485771179}, "Polygon": [{"X": 0.7312007546424866, "Y": 0.5683116316795349}, {"X": 0.760138213634491, "Y": 0.5677482485771179}, {"X": 0.7611069679260254, "Y": 0.582790732383728}, {"X": 0.7321080565452576, "Y": 0.5833705067634583}]}, "Id": "49054f2b-8bfd-4341-a899-856ae39cd9da"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05070798099040985, "Height": 0.01662181131541729, "Left": 0.772689700126648, "Top": 0.5625223517417908}, "Polygon": [{"X": 0.772689700126648, "Y": 0.5634803771972656}, {"X": 0.8222506642341614, "Y": 0.5625223517417908}, {"X": 0.8233976364135742, "Y": 0.5781570672988892}, {"X": 0.773727297782898, "Y": 0.5791441798210144}]}, "Id": "23a503e6-0d92-479e-823e-98b775d983eb"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06440139561891556, "Height": 0.017430143430829048, "Left": 0.8352982401847839, "Top": 0.5605130791664124}, "Polygon": [{"X": 0.8352982401847839, "Y": 0.5617303848266602}, {"X": 0.8983360528945923, "Y": 0.5605130791664124}, {"X": 0.8996996283531189, "Y": 0.5766874551773071}, {"X": 0.8365181684494019, "Y": 0.5779432058334351}]}, "Id": "3ac506d4-05ec-4fd4-acb7-4632b3ca1d36"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "according", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09577128291130066, "Height": 0.0218597874045372, "Left": 0.572115421295166, "Top": 0.5884977579116821}, "Polygon": [{"X": 0.572115421295166, "Y": 0.5904046297073364}, {"X": 0.6668751239776611, "Y": 0.5884977579116821}, {"X": 0.6678867340087891, "Y": 0.6083800792694092}, {"X": 0.572860062122345, "Y": 0.6103575825691223}]}, "Id": "307a0b0b-46dc-46ca-9761-d6b0e31ef77e"}, {"BlockType": "WORD", "Confidence": 98.515625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020248867571353912, "Height": 0.01577945053577423, "Left": 0.679896354675293, "Top": 0.5883920788764954}, "Polygon": [{"X": 0.679896354675293, "Y": 0.5887827277183533}, {"X": 0.6992915868759155, "Y": 0.5883920788764954}, {"X": 0.7001452445983887, "Y": 0.6037697196006775}, {"X": 0.6807079315185547, "Y": 0.6041715741157532}]}, "Id": "783b8292-8570-4a73-a99d-c6ebaaad8a36"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040045589208602905, "Height": 0.014926139265298843, "Left": 0.7114554047584534, "Top": 0.5886234641075134}, "Polygon": [{"X": 0.7114554047584534, "Y": 0.5894139409065247}, {"X": 0.7506139278411865, "Y": 0.5886234641075134}, {"X": 0.7515010237693787, "Y": 0.6027384400367737}, {"X": 0.7122645974159241, "Y": 0.6035496592521667}]}, "Id": "f546528d-c3de-4eb9-bbc2-be4631cbe804"}, {"BlockType": "WORD", "Confidence": 38.28224563598633, "Text": "ssuer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05801820382475853, "Height": 0.016533024609088898, "Left": 0.7671668529510498, "Top": 0.5870115756988525}, "Polygon": [{"X": 0.7671668529510498, "Y": 0.5881597399711609}, {"X": 0.8240587711334229, "Y": 0.5870115756988525}, {"X": 0.8251850605010986, "Y": 0.6023635268211365}, {"X": 0.7681702375411987, "Y": 0.6035445928573608}]}, "Id": "3b239721-f532-425c-934b-4b3a10ce512f"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "agreement.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10372557491064072, "Height": 0.021519728004932404, "Left": 0.8359677195549011, "Top": 0.5830371379852295}, "Polygon": [{"X": 0.8359677195549011, "Y": 0.585089385509491}, {"X": 0.9379537105560303, "Y": 0.5830371379852295}, {"X": 0.9396932721138, "Y": 0.6024298071861267}, {"X": 0.8374294638633728, "Y": 0.6045568585395813}]}, "Id": "9c7bbe03-c871-4272-86ee-acab8d141bbc"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "Salesperson", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11735472828149796, "Height": 0.021604439243674278, "Left": 0.13517440855503082, "Top": 0.6522469520568848}, "Polygon": [{"X": 0.13564704358577728, "Y": 0.6548193693161011}, {"X": 0.2525291442871094, "Y": 0.6522469520568848}, {"X": 0.252372145652771, "Y": 0.671196460723877}, {"X": 0.13517440855503082, "Y": 0.6738513708114624}]}, "Id": "13ca361e-15f6-4403-9705-9b64a085a1b7"}, {"BlockType": "WORD", "Confidence": 99.80229949951172, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027729492634534836, "Height": 0.016985829919576645, "Left": 0.2650356888771057, "Top": 0.6489630937576294}, "Polygon": [{"X": 0.26514214277267456, "Y": 0.6495687365531921}, {"X": 0.29276520013809204, "Y": 0.6489630937576294}, {"X": 0.2927228808403015, "Y": 0.6653263568878174}, {"X": 0.2650356888771057, "Y": 0.6659489274024963}]}, "Id": "815c288c-b5bf-4470-b8c7-153d1355e30b"}, {"BlockType": "WORD", "Confidence": 97.54085540771484, "Text": "AUTO/SMARTFUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14915429055690765, "Height": 0.021458232775330544, "Left": 0.30653926730155945, "Top": 0.6424707770347595}, "Polygon": [{"X": 0.3065507411956787, "Y": 0.645717978477478}, {"X": 0.4553227424621582, "Y": 0.6424707770347595}, {"X": 0.4556935429573059, "Y": 0.6605808734893799}, {"X": 0.30653926730155945, "Y": 0.6639289855957031}]}, "Id": "dd939183-544b-437e-933d-69649771af18"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07382714748382568, "Height": 0.017745159566402435, "Left": 0.6188650131225586, "Top": 0.6358173489570618}, "Polygon": [{"X": 0.6188650131225586, "Y": 0.6374059319496155}, {"X": 0.6918208599090576, "Y": 0.6358173489570618}, {"X": 0.6926921606063843, "Y": 0.6519295573234558}, {"X": 0.6195712089538574, "Y": 0.6535624861717224}]}, "Id": "baf637c5-bfaf-4d54-b2ea-b5828f29e344"}, {"BlockType": "WORD", "Confidence": 97.70946502685547, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020335078239440918, "Height": 0.016794145107269287, "Left": 0.7035016417503357, "Top": 0.6349691152572632}, "Polygon": [{"X": 0.7035016417503357, "Y": 0.6353909373283386}, {"X": 0.7228797078132629, "Y": 0.6349691152572632}, {"X": 0.7238367199897766, "Y": 0.6513294577598572}, {"X": 0.7044142484664917, "Y": 0.6517632603645325}]}, "Id": "6d3cdca1-326c-4521-be38-207a7b74fa4f"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "0090760", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07325225323438644, "Height": 0.017473163083195686, "Left": 0.7350360751152039, "Top": 0.6337864995002747}, "Polygon": [{"X": 0.7350360751152039, "Y": 0.635358452796936}, {"X": 0.8071706891059875, "Y": 0.6337864995002747}, {"X": 0.8082883358001709, "Y": 0.6496443748474121}, {"X": 0.7359936237335205, "Y": 0.6512596607208252}]}, "Id": "e14d6195-7b99-4fea-9099-d0fc8c9014c6"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050949838012456894, "Height": 0.017577387392520905, "Left": 0.15612870454788208, "Top": 0.6771280169487}, "Polygon": [{"X": 0.1564868986606598, "Y": 0.6782825589179993}, {"X": 0.20707854628562927, "Y": 0.6771280169487}, {"X": 0.20683805644512177, "Y": 0.6935200095176697}, {"X": 0.15612870454788208, "Y": 0.6947054266929626}]}, "Id": "e3c08a41-3250-408f-9224-b4576599ed76"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07238282263278961, "Height": 0.0178787000477314, "Left": 0.2197922021150589, "Top": 0.6742073893547058}, "Polygon": [{"X": 0.22000078856945038, "Y": 0.6758520007133484}, {"X": 0.2921750247478485, "Y": 0.6742073893547058}, {"X": 0.2921319603919983, "Y": 0.6903977394104004}, {"X": 0.2197922021150589, "Y": 0.6920860409736633}]}, "Id": "9e520eaa-e7fc-4447-ba4e-8f33eaedfc6a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "3482", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042222969233989716, "Height": 0.017155412584543228, "Left": 0.305867075920105, "Top": 0.6714648008346558}, "Polygon": [{"X": 0.30587881803512573, "Y": 0.6724225878715515}, {"X": 0.3480055034160614, "Y": 0.6714648008346558}, {"X": 0.3480900526046753, "Y": 0.6876369118690491}, {"X": 0.305867075920105, "Y": 0.6886202096939087}]}, "Id": "4e96c056-3823-4761-8948-70780fd2f253"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Merchant", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08828205615282059, "Height": 0.01877446472644806, "Left": 0.552489697933197, "Top": 0.6612536907196045}, "Polygon": [{"X": 0.552489697933197, "Y": 0.6632324457168579}, {"X": 0.6399924755096436, "Y": 0.6612536907196045}, {"X": 0.640771746635437, "Y": 0.6779940724372864}, {"X": 0.5530634522438049, "Y": 0.6800281405448914}]}, "Id": "63f4c3d4-64ce-41fa-9631-607e872d70c7"}, {"BlockType": "WORD", "Confidence": 99.0539321899414, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0743628591299057, "Height": 0.018497159704566002, "Left": 0.6495676040649414, "Top": 0.6593475937843323}, "Polygon": [{"X": 0.6495676040649414, "Y": 0.6610069870948792}, {"X": 0.7229514718055725, "Y": 0.6593475937843323}, {"X": 0.7239304184913635, "Y": 0.676138699054718}, {"X": 0.6503742337226868, "Y": 0.677844762802124}]}, "Id": "e81a64c3-0080-48a1-8c12-b460f17f29c3"}, {"BlockType": "WORD", "Confidence": 94.84913635253906, "Text": "*******2817", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11435484886169434, "Height": 0.01837533712387085, "Left": 0.7374354004859924, "Top": 0.****************}, "Polygon": [{"X": 0.7374354004859924, "Y": 0.****************}, {"X": 0.8505873680114746, "Y": 0.****************}, {"X": 0.8517902493476868, "Y": 0.672837495803833}, {"X": 0.7383898496627808, "Y": 0.6754663586616516}]}, "Id": "a24b92fc-28ab-4a76-9a72-7023bff5e62d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042354390025138855, "Height": 0.017275428399443626, "Left": 0.166154146194458, "Top": 0.7016451358795166}, "Polygon": [{"X": 0.16648513078689575, "Y": 0.7026391625404358}, {"X": 0.20850853621959686, "Y": 0.7016451358795166}, {"X": 0.20827414095401764, "Y": 0.7179009914398193}, {"X": 0.166154146194458, "Y": 0.7189205288887024}]}, "Id": "e8551b15-502f-4ad2-a80a-5fb13169cd49"}, {"BlockType": "WORD", "Confidence": 99.01406860351562, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07329893857240677, "Height": 0.01806488260626793, "Left": 0.2194986790418625, "Top": 0.6989210844039917}, "Polygon": [{"X": 0.21970854699611664, "Y": 0.7006481289863586}, {"X": 0.29279762506484985, "Y": 0.6989210844039917}, {"X": 0.2927558720111847, "Y": 0.7152143120765686}, {"X": 0.2194986790418625, "Y": 0.7169860005378723}]}, "Id": "1f569418-fa4c-438e-9f67-35ae7c52cce8"}, {"BlockType": "WORD", "Confidence": 13.152403831481934, "Text": "xxxxxxxxxxxxxx1773", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19549697637557983, "Height": 0.023813094943761826, "Left": 0.3047425448894501, "Top": 0.6896130442619324}, "Polygon": [{"X": 0.3047594726085663, "Y": 0.6941905617713928}, {"X": 0.4997316598892212, "Y": 0.6896130442619324}, {"X": 0.5002395510673523, "Y": 0.7087081074714661}, {"X": 0.3047425448894501, "Y": 0.7134261727333069}]}, "Id": "bf93b0b8-93d8-4bd4-8be4-88becfbb48b8"}, {"BlockType": "WORD", "Confidence": 97.78958129882812, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05476606637239456, "Height": 0.018497830256819725, "Left": 0.5100677609443665, "Top": 0.6880123615264893}, "Polygon": [{"X": 0.5100677609443665, "Y": 0.6892836093902588}, {"X": 0.5642201900482178, "Y": 0.6880123615264893}, {"X": 0.5648338198661804, "Y": 0.7052038311958313}, {"X": 0.5105510354042053, "Y": 0.7065101861953735}]}, "Id": "430a3141-64d2-48aa-831d-6aaf8a2d5317"}, {"BlockType": "WORD", "Confidence": 96.82178497314453, "Text": "Loyalty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07526615262031555, "Height": 0.022027242928743362, "Left": 0.14253173768520355, "Top": 0.7255600094795227}, "Polygon": [{"X": 0.14300736784934998, "Y": 0.7273905277252197}, {"X": 0.2177978903055191, "Y": 0.7255600094795227}, {"X": 0.21753467619419098, "Y": 0.7457002997398376}, {"X": 0.14253173768520355, "Y": 0.747587263584137}]}, "Id": "f0fddf9a-8453-4810-ad5f-c7b883ec49dd"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "Card#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06190283223986626, "Height": 0.01849360205233097, "Left": 0.22940978407859802, "Top": 0.7229428291320801}, "Polygon": [{"X": 0.22960367798805237, "Y": 0.7244514226913452}, {"X": 0.2913126051425934, "Y": 0.7229428291320801}, {"X": 0.2912658154964447, "Y": 0.7398885488510132}, {"X": 0.22940978407859802, "Y": 0.7414364218711853}]}, "Id": "5fa40049-1be5-4619-9da2-0e8b69b64079"}, {"BlockType": "WORD", "Confidence": 72.98609161376953, "Text": "xxxxxxxxxxxx8997", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1728898137807846, "Height": 0.022626640275120735, "Left": 0.305582195520401, "Top": 0.7156447172164917}, "Polygon": [{"X": 0.30559617280960083, "Y": 0.7198442816734314}, {"X": 0.****************, "Y": 0.7156447172164917}, {"X": 0.****************, "Y": 0.7339523434638977}, {"X": 0.305582195520401, "Y": 0.7382713556289673}]}, "Id": "fd562186-5d55-43a0-8886-b219ddabf0f1"}, {"BlockType": "WORD", "Confidence": 97.20484161376953, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054126616567373276, "Height": 0.018228448927402496, "Left": 0.4890047609806061, "Top": 0.7134917974472046}, "Polygon": [{"X": 0.4890047609806061, "Y": 0.7147955298423767}, {"X": 0.****************, "Y": 0.7134917974472046}, {"X": 0.5431313514709473, "Y": 0.7303823232650757}, {"X": 0.4894278347492218, "Y": 0.7317202687263489}]}, "Id": "494e9625-e8f9-44a8-863f-77ba8ea1109f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07384194433689117, "Height": 0.022607261314988136, "Left": 0.08830223232507706, "Top": 0.7757301330566406}, "Polygon": [{"X": 0.08894191682338715, "Y": 0.7776429653167725}, {"X": 0.16214418411254883, "Y": 0.7757301330566406}, {"X": 0.16171643137931824, "Y": 0.7963676452636719}, {"X": 0.08830223232507706, "Y": 0.7983373999595642}]}, "Id": "9d760565-6fa9-4b7c-9b2e-b8ad9777701a"}, {"BlockType": "WORD", "Confidence": 93.35399627685547, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08430074155330658, "Height": 0.022466255351901054, "Left": 0.17392383515834808, "Top": 0.7736344337463379}, "Polygon": [{"X": 0.1743096560239792, "Y": 0.7758283019065857}, {"X": 0.25822457671165466, "Y": 0.7736344337463379}, {"X": 0.258076012134552, "Y": 0.7938426733016968}, {"X": 0.17392383515834808, "Y": 0.7961006760597229}]}, "Id": "1d0a8d7f-cd38-42bd-910d-d37790ae99be"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "NBS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0323970764875412, "Height": 0.01721378229558468, "Left": 0.27222877740859985, "Top": 0.771456778049469}, "Polygon": [{"X": 0.27231699228286743, "Y": 0.7723003625869751}, {"X": 0.30462586879730225, "Y": 0.771456778049469}, {"X": 0.30461135506629944, "Y": 0.7878069281578064}, {"X": 0.27222877740859985, "Y": 0.788670539855957}]}, "Id": "8779d21c-6403-4076-839c-b63834624961"}, {"BlockType": "WORD", "Confidence": 98.73944091796875, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008634933270514011, "Height": 0.016989486292004585, "Left": 0.3167206346988678, "Top": 0.7702366709709167}, "Polygon": [{"X": 0.3167206346988678, "Y": 0.7704610824584961}, {"X": 0.32532212138175964, "Y": 0.7702366709709167}, {"X": 0.32535555958747864, "Y": 0.7869963049888611}, {"X": 0.3167339861392975, "Y": 0.787226140499115}]}, "Id": "23e53adf-d40f-431e-8e5b-caafb4f6d574"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04204249009490013, "Height": 0.017265230417251587, "Left": 0.3383336663246155, "Top": 0.7684625387191772}, "Polygon": [{"X": 0.3383336663246155, "Y": 0.7695547938346863}, {"X": 0.3802203834056854, "Y": 0.7684625387191772}, {"X": 0.3803761601448059, "Y": 0.784609854221344}, {"X": 0.33839526772499084, "Y": 0.7857277989387512}]}, "Id": "53bcf094-0992-4592-a6d9-b4c515ba9d3a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04265075922012329, "Height": 0.01712903566658497, "Left": 0.3925463855266571, "Top": 0.7669140696525574}, "Polygon": [{"X": 0.3925463855266571, "Y": 0.768018901348114}, {"X": 0.4349207580089569, "Y": 0.7669140696525574}, {"X": 0.4351971447467804, "Y": 0.78291255235672}, {"X": 0.3927285373210907, "Y": 0.7840431332588196}]}, "Id": "dfa76917-63f5-4689-9725-64536c958deb"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05535631999373436, "Height": 0.018498705700039864, "Left": 0.4461159110069275, "Top": 0.7646784782409668}, "Polygon": [{"X": 0.4461159110069275, "Y": 0.766109049320221}, {"X": 0.5010209083557129, "Y": 0.7646784782409668}, {"X": 0.5014722347259521, "Y": 0.7817110419273376}, {"X": 0.44643744826316833, "Y": 0.7831771969795227}]}, "Id": "89016775-b7cf-46c3-ae7f-c4005cb4b3ee"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05420630797743797, "Height": 0.01835770718753338, "Left": 0.08562575280666351, "Top": 0.8261182904243469}, "Polygon": [{"X": 0.08614995330572128, "Y": 0.8276116251945496}, {"X": 0.13983206450939178, "Y": 0.8261182904243469}, {"X": 0.13943374156951904, "Y": 0.8429484367370605}, {"X": 0.08562575280666351, "Y": 0.8444759845733643}]}, "Id": "f22d0603-65c9-4ef4-9d65-6c4e09871fd9"}, {"BlockType": "WORD", "Confidence": 99.314208984375, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04025306925177574, "Height": 0.019018324092030525, "Left": 0.15069349110126495, "Top": 0.8244456648826599}, "Polygon": [{"X": 0.15108953416347504, "Y": 0.8255540728569031}, {"X": 0.190946564078331, "Y": 0.8244456648826599}, {"X": 0.19064964354038239, "Y": 0.8423284888267517}, {"X": 0.15069349110126495, "Y": 0.8434639573097229}]}, "Id": "a6639712-e797-4bfd-8585-64e68adc1833"}, {"BlockType": "WORD", "Confidence": 54.69467544555664, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033761899918317795, "Height": 0.0055245766416192055, "Left": 0.2150029093027115, "Top": 0.8298890590667725}, "Polygon": [{"X": 0.21506361663341522, "Y": 0.8308343887329102}, {"X": 0.24876481294631958, "Y": 0.8298890590667725}, {"X": 0.24872547388076782, "Y": 0.8344624638557434}, {"X": 0.2150029093027115, "Y": 0.8354136347770691}]}, "Id": "b77a26df-522a-45dc-9aef-dc5bda95566b"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053430091589689255, "Height": 0.01831204816699028, "Left": 0.27111896872520447, "Top": 0.8208773136138916}, "Polygon": [{"X": 0.2712115943431854, "Y": 0.8223600387573242}, {"X": 0.3245176374912262, "Y": 0.8208773136138916}, {"X": 0.3245490491390228, "Y": 0.8376725316047668}, {"X": 0.27111896872520447, "Y": 0.839189350605011}]}, "Id": "10de27e0-c7e1-4f6d-8dc8-c6080d91a266"}, {"BlockType": "WORD", "Confidence": 99.61276245117188, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0407230518758297, "Height": 0.018743062391877174, "Left": 0.3365667164325714, "Top": 0.8185780644416809}, "Polygon": [{"X": 0.3365667164325714, "Y": 0.8197051882743835}, {"X": 0.3771287798881531, "Y": 0.8185780644416809}, {"X": 0.3772897720336914, "Y": 0.8361668586730957}, {"X": 0.3366290330886841, "Y": 0.8373211622238159}]}, "Id": "331d1fe6-a024-4c8b-b0ab-eccebf3df591"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "296c8752-6947-4e73-bcd8-6967053524a3", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "296c8752-6947-4e73-bcd8-6967053524a3", "content-type": "application/x-amz-json-1.1", "content-length": "72645", "date": "Wed, 24 Sep 2025 12:47:09 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "8a110b1b-f748-4feb-90e1-037c96f7709e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:13 GMT", "content-type": "application/json", "content-length": "1286", "connection": "keep-alive", "x-amzn-requestid": "8a110b1b-f748-4feb-90e1-037c96f7709e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have only one page. It looks like a fuel receipt? Let's examine. The text contains: \"KWIK TRIP\", \"Pump\", \"Products Reefer Quantity Unit Cost\", \"Total\", \"Sales tax\", \"Signature\", \"Invoice #: 0090760\", \"Truck Number: 3482\", \"Merchant Number\", \"Card Number\", \"Billing Company: NBS / KWIK TRIP FLEET\". It includes \"Fuel\", \"Diesel\", \"N82.836\" maybe gallons. It looks like a fuel receipt. Yes, the definition: Fuel Receipt: keywords Fuel, Diesel, Pump #, gallons, etc. So doc_type: fuel_receipt. But also includes \"Invoice #: 0090760\" maybe also a fuel receipt.\n\nLet's double-check: The page includes \"KWIK TRIP 1281 /106 N. ROYAL AVENUE ABELGIUM. WI 53004/(262) 988-7024\". This is a pump location. The page includes \"Products Reefer Quantity Unit Cost\". So it's a fuel receipt.\n\nThus output: page_no 1, doc_type \"fuel_receipt\". Use tool.\n\n"}}}, {"toolUse": {"toolUseId": "tooluse_QLsevV2tQki8ucx5ytMJng", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2774, "outputTokens": 259, "totalTokens": 3033}, "metrics": {"latencyMs": 1364}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/4a8079b9_C8WLD4UA74U7PBVTPBW4.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP 1281 /106 N. ROYAL AVENUE ABELGIUM. WI 53004/(262) 988-7024\nNumber: 0900-0760 05/12/2025 Station: 9\nResponse: APPROVED AUTH REF#: P00174\nLOYALTY Response: APPROVED. You earned 1.64 Fleet points! Drink Credit Earned! E\nxpires in 24 Hours. Shower C\nPump\nProducts Reefer Quantity Unit Cost\nTotal\n#2\nDiesel 2\nN\n82.836\n3.099\n256.71\nsubtotal =\n256.71\nsales tax =\n0.00\nTOTAL =\n256.71 Signature:\nDuways I agree to pay total amount\naccording to card ssuer agreement.\nSalesperson ID: AUTO/SMARTFUEL\nInvoice #: 0090760\nTruck Number: 3482\nMerchant Number: *******2817\nCard Number: xxxxxxxxxxxxxx1773 -ICR-\nLoyalty Card#: xxxxxxxxxxxx8997 -ICR-\nBilling Company: NBS / KWIK TRIP FLEET\nTHANK YOU! - THANK YOU!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}