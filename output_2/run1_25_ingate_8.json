{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0018942136084660888}, {"X": 0.9711273312568665, "Y": 0.0}, {"X": 1.0, "Y": 0.9619335532188416}, {"X": 0.04052659124135971, "Y": 1.0}]}, "Id": "7d81261b-2b60-4f6c-86b3-0ae6d72dee87", "Relationships": [{"Type": "CHILD", "Ids": ["f829dbd1-af6f-4ac5-a6ac-87bca042623d", "f182fd2c-9381-4e52-94b2-d7ee11c37083", "81d9ec36-fa7d-42d4-8e8b-2c6d8e7e688e", "8850a039-4eae-4627-8eec-57714a22a2d1", "c8730eeb-111f-4418-a1e1-221e17cb7e78", "59ddf98b-63bc-4117-9b37-22eb8dd03611", "392ab39e-0b0b-4517-ad0c-b4aa99ee080c", "83af6645-4228-4fc1-8a11-e334f9f6e8c4", "4ca6d336-881a-45a4-b129-f4abd2237c8e", "15f42b28-3dc9-4f23-834d-387085857460", "2e7842f3-80be-40f9-9f1b-30eb669667d3", "7fb5ed02-b749-40b5-be24-95de0c19220a", "fed15249-bab2-4c03-bb8f-f83dd2368efd", "66273828-e010-47f6-9835-487bbabea932", "65edbe91-528e-4870-bc5f-e474f8cd951b", "e2d81d36-5028-4581-9719-7e9c9de95a60", "3ab04225-affa-4257-8e54-68a7fd8bac09", "aeea21c8-ce27-44c7-af7e-83c71dfdd449", "9a3240e4-3f05-45dd-a39a-1927813603fb", "8cb6dcdd-3568-41f5-8b32-affaea0250fb", "1c7e75b5-4298-4fde-956f-da77b8bd6982", "9f09667e-e557-46cc-b55d-15e91e190590", "51dc0470-3cb9-4ba5-bb25-2e43742b3adf"]}]}, {"BlockType": "LINE", "Confidence": 89.78336334228516, "Text": "CITS ConGlobal", "Geometry": {"BoundingBox": {"Width": 0.9415886402130127, "Height": 0.13149607181549072, "Left": 0.008415731601417065, "Top": 0.0}, "Polygon": [{"X": 0.008415731601417065, "Y": 0.030197983607649803}, {"X": 0.9447632431983948, "Y": 0.0}, {"X": 0.9500043988227844, "Y": 0.06312260031700134}, {"X": 0.013926315121352673, "Y": 0.13149607181549072}]}, "Id": "f829dbd1-af6f-4ac5-a6ac-87bca042623d", "Relationships": [{"Type": "CHILD", "Ids": ["cae5bd9f-dd3f-4fea-b1a8-4802a8c25686", "e6052637-e131-4bbc-9c4e-f686cd2ab147"]}]}, {"BlockType": "LINE", "Confidence": 96.84131622314453, "Text": "OOCU8756309", "Geometry": {"BoundingBox": {"Width": 0.18352067470550537, "Height": 0.04126334562897682, "Left": 0.39462435245513916, "Top": 0.15602150559425354}, "Polygon": [{"X": 0.39462435245513916, "Y": 0.16931599378585815}, {"X": 0.5766682028770447, "Y": 0.15602150559425354}, {"X": 0.5781450271606445, "Y": 0.18399247527122498}, {"X": 0.39611566066741943, "Y": 0.19728486239910126}]}, "Id": "f182fd2c-9381-4e52-94b2-d7ee11c37083", "Relationships": [{"Type": "CHILD", "Ids": ["99050d8b-b557-407e-8dc7-a556c434931d"]}]}, {"BlockType": "LINE", "Confidence": 99.363037109375, "Text": "ConGlobal Industries", "Geometry": {"BoundingBox": {"Width": 0.14313550293445587, "Height": 0.028900614008307457, "Left": 0.41714489459991455, "Top": 0.20857778191566467}, "Polygon": [{"X": 0.41714489459991455, "Y": 0.2189578413963318}, {"X": 0.5593013763427734, "Y": 0.20857778191566467}, {"X": 0.5602803826332092, "Y": 0.2270994335412979}, {"X": 0.41813135147094727, "Y": 0.23747840523719788}]}, "Id": "81d9ec36-fa7d-42d4-8e8b-2c6d8e7e688e", "Relationships": [{"Type": "CHILD", "Ids": ["3c48337c-74af-4fdb-99f6-21354fbe08b2", "55b0886f-6bde-4843-b933-5754b0a8aa64"]}]}, {"BlockType": "LINE", "Confidence": 99.90726470947266, "Text": "465 Sawtell Avenue", "Geometry": {"BoundingBox": {"Width": 0.13366778194904327, "Height": 0.02773519977927208, "Left": 0.4234849214553833, "Top": 0.2372034341096878}, "Polygon": [{"X": 0.4234849214553833, "Y": 0.24689321219921112}, {"X": 0.5561985969543457, "Y": 0.2372034341096878}, {"X": 0.5571526885032654, "Y": 0.25524982810020447}, {"X": 0.4244458079338074, "Y": 0.264938622713089}]}, "Id": "8850a039-4eae-4627-8eec-57714a22a2d1", "Relationships": [{"Type": "CHILD", "Ids": ["ba22799e-1226-494f-9a51-fcfdf5285f8a", "a2a6c011-71e3-4c44-86b6-3df9856af3bd", "7318c77d-884f-4241-9c21-23d4c7c205ef"]}]}, {"BlockType": "LINE", "Confidence": 88.28178405761719, "Text": "Atlanta, GA 30315", "Geometry": {"BoundingBox": {"Width": 0.1240360289812088, "Height": 0.02795623615384102, "Left": 0.42963892221450806, "Top": 0.26517823338508606}, "Polygon": [{"X": 0.42963892221450806, "Y": 0.2741604745388031}, {"X": 0.552671492099762, "Y": 0.26517823338508606}, {"X": 0.5536749362945557, "Y": 0.2841532230377197}, {"X": 0.4306490123271942, "Y": 0.293134480714798}]}, "Id": "c8730eeb-111f-4418-a1e1-221e17cb7e78", "Relationships": [{"Type": "CHILD", "Ids": ["dc5b3799-8406-40d1-99bb-f1614e7cb588", "2d075d08-c825-4397-a214-05f46437ce35", "158a3c0d-ed64-4f2f-a7ac-42bb7e4bf666"]}]}, {"BlockType": "LINE", "Confidence": 99.03359985351562, "Text": "**************", "Geometry": {"BoundingBox": {"Width": 0.10650090873241425, "Height": 0.027859780937433243, "Left": 0.4406694173812866, "Top": 0.29376649856567383}, "Polygon": [{"X": 0.4406694173812866, "Y": 0.3014633357524872}, {"X": 0.5461035370826721, "Y": 0.29376649856567383}, {"X": 0.5471703410148621, "Y": 0.31393036246299744}, {"X": 0.44174227118492126, "Y": 0.32162630558013916}]}, "Id": "59ddf98b-63bc-4117-9b37-22eb8dd03611", "Relationships": [{"Type": "CHILD", "Ids": ["c821b6f8-1f07-48d5-ad99-b6fde3b57082"]}]}, {"BlockType": "LINE", "Confidence": 83.20970153808594, "Text": "Customer: OOCL", "Geometry": {"BoundingBox": {"Width": 0.12272791564464569, "Height": 0.027159372344613075, "Left": 0.06034333258867264, "Top": 0.37763211131095886}, "Polygon": [{"X": 0.06034333258867264, "Y": 0.3865180015563965}, {"X": 0.18208520114421844, "Y": 0.37763211131095886}, {"X": 0.18307125568389893, "Y": 0.395906537771225}, {"X": 0.06133570894598961, "Y": 0.4047914743423462}]}, "Id": "392ab39e-0b0b-4517-ad0c-b4aa99ee080c", "Relationships": [{"Type": "CHILD", "Ids": ["ff12ebe2-f915-4f2f-89c6-a979b7356cf3", "e366a2ba-8191-47f3-871e-d7972b8223fc"]}]}, {"BlockType": "LINE", "Confidence": 92.96556091308594, "Text": "Depot: ATL02", "Geometry": {"BoundingBox": {"Width": 0.09813614934682846, "Height": 0.029355529695749283, "Left": 0.062416739761829376, "Top": 0.41615086793899536}, "Polygon": [{"X": 0.062416739761829376, "Y": 0.42322516441345215}, {"X": 0.15934902429580688, "Y": 0.41615086793899536}, {"X": 0.16055288910865784, "Y": 0.4384329915046692}, {"X": 0.06362675875425339, "Y": 0.44550639390945435}]}, "Id": "83af6645-4228-4fc1-8a11-e334f9f6e8c4", "Relationships": [{"Type": "CHILD", "Ids": ["a9758a0b-e2dc-4b8d-8b9a-7053fe69caca", "a84a1639-c6d4-4ed2-9747-de7e92fc3228"]}]}, {"BlockType": "LINE", "Confidence": 98.08653259277344, "Text": "Size: 40 H", "Geometry": {"BoundingBox": {"Width": 0.07202330976724625, "Height": 0.023212067782878876, "Left": 0.06397383660078049, "Top": 0.4565165638923645}, "Polygon": [{"X": 0.06397383660078049, "Y": 0.4617012143135071}, {"X": 0.13502176105976105, "Y": 0.4565165638923645}, {"X": 0.13599714636802673, "Y": 0.474544495344162}, {"X": 0.06495287269353867, "Y": 0.479728639125824}]}, "Id": "4ca6d336-881a-45a4-b129-f4abd2237c8e", "Relationships": [{"Type": "CHILD", "Ids": ["4f04d8a6-71e3-4501-bd36-66f338e376e9", "70029450-2af6-434d-bf6e-b7192832d77b", "ded35761-8bde-4f48-9bb9-df0fb62e8644"]}]}, {"BlockType": "LINE", "Confidence": 99.37254333496094, "Text": "Equipment Type: CON", "Geometry": {"BoundingBox": {"Width": 0.1620723009109497, "Height": 0.03410420939326286, "Left": 0.06648243218660355, "Top": 0.48583555221557617}, "Polygon": [{"X": 0.06648243218660355, "Y": 0.4975735545158386}, {"X": 0.22735033929347992, "Y": 0.48583555221557617}, {"X": 0.22855474054813385, "Y": 0.5082032680511475}, {"X": 0.06769706308841705, "Y": 0.5199397802352905}]}, "Id": "15f42b28-3dc9-4f23-834d-387085857460", "Relationships": [{"Type": "CHILD", "Ids": ["cdd38345-000f-46cd-ac6f-e5bd2b1df56d", "5db20604-b244-4389-8fcd-42046c098eec", "f18fb634-6827-4726-be0a-865c962da103"]}]}, {"BlockType": "LINE", "Confidence": 88.49324035644531, "Text": "In Date: 2022-12-27", "Geometry": {"BoundingBox": {"Width": 0.14280050992965698, "Height": 0.028720244765281677, "Left": 0.06872639805078506, "Top": 0.524623453617096}, "Polygon": [{"X": 0.06872639805078506, "Y": 0.5349698066711426}, {"X": 0.21053650975227356, "Y": 0.524623453617096}, {"X": 0.21152691543102264, "Y": 0.5429984927177429}, {"X": 0.06972420960664749, "Y": 0.5533437132835388}]}, "Id": "2e7842f3-80be-40f9-9f1b-30eb669667d3", "Relationships": [{"Type": "CHILD", "Ids": ["0bebb5cb-c809-4a55-bb03-57b0fedd19d5", "7146be1f-acc3-400c-bef4-688ae7fb0ed7", "c8e68412-1d67-47b0-8754-e7a3b67935b9"]}]}, {"BlockType": "LINE", "Confidence": 82.38958740234375, "Text": "in Time: 12:11", "Geometry": {"BoundingBox": {"Width": 0.10075962543487549, "Height": 0.024480512365698814, "Left": 0.07084813714027405, "Top": 0.5654739141464233}, "Polygon": [{"X": 0.07084813714027405, "Y": 0.5727566480636597}, {"X": 0.17067870497703552, "Y": 0.5654739141464233}, {"X": 0.17160776257514954, "Y": 0.5826723575592041}, {"X": 0.07178207486867905, "Y": 0.5899544358253479}]}, "Id": "7fb5ed02-b749-40b5-be24-95de0c19220a", "Relationships": [{"Type": "CHILD", "Ids": ["a0b91ac2-881a-475e-aed6-7c3d2d9e5eaf", "e061144b-a91c-4a22-ad4a-187b1e8b6c07", "b9c400ba-391a-45d1-8f77-6f970091d191"]}]}, {"BlockType": "LINE", "Confidence": 92.64329528808594, "Text": "In Condition: AV", "Geometry": {"BoundingBox": {"Width": 0.12039922922849655, "Height": 0.025935670360922813, "Left": 0.07260321080684662, "Top": 0.6009951829910278}, "Polygon": [{"X": 0.07260321080684662, "Y": 0.6097096800804138}, {"X": 0.19207307696342468, "Y": 0.6009951829910278}, {"X": 0.19300243258476257, "Y": 0.6182171106338501}, {"X": 0.07353843003511429, "Y": 0.626930832862854}]}, "Id": "fed15249-bab2-4c03-bb8f-f83dd2368efd", "Relationships": [{"Type": "CHILD", "Ids": ["c71da3a6-8ea1-403f-aff4-1df6c6f804b9", "daeb3e90-51b3-4f29-86f1-89fb1f67b952", "b4ca9b52-c7b7-40cf-819b-175a0565ff1f"]}]}, {"BlockType": "LINE", "Confidence": 96.99776458740234, "Text": "In Empty: True", "Geometry": {"BoundingBox": {"Width": 0.10544859617948532, "Height": 0.028974032029509544, "Left": 0.0747087150812149, "Top": 0.6382923126220703}, "Polygon": [{"X": 0.0747087150812149, "Y": 0.6458991169929504}, {"X": 0.1790032833814621, "Y": 0.6382923126220703}, {"X": 0.18015730381011963, "Y": 0.6596604585647583}, {"X": 0.07586908340454102, "Y": 0.6672663688659668}]}, "Id": "66273828-e010-47f6-9835-487bbabea932", "Relationships": [{"Type": "CHILD", "Ids": ["e9befc8a-ce3f-416e-a4f0-cfb025a30a70", "9935aa60-6f98-4c5b-9bf4-ca903c61648c", "b84a0cab-daba-4b50-869c-8f70bfdb5b52"]}]}, {"BlockType": "LINE", "Confidence": 98.69519805908203, "Text": "EIR Num: **********", "Geometry": {"BoundingBox": {"Width": 0.1574394255876541, "Height": 0.029197044670581818, "Left": 0.07662750035524368, "Top": 0.6717352867126465}, "Polygon": [{"X": 0.07662750035524368, "Y": 0.6831471920013428}, {"X": 0.2331090122461319, "Y": 0.6717352867126465}, {"X": 0.2340669333934784, "Y": 0.6895215511322021}, {"X": 0.07759334146976471, "Y": 0.7009323239326477}]}, "Id": "65edbe91-528e-4870-bc5f-e474f8cd951b", "Relationships": [{"Type": "CHILD", "Ids": ["dfd19b4f-406a-43b9-89e9-2f07a6706ab0", "03efc0e6-f508-4c5f-a4d3-d7c6ffc31e60", "9e6e274f-f5c2-45ae-baf8-6108c362436a"]}]}, {"BlockType": "LINE", "Confidence": 99.85560607910156, "Text": "Type: SINGLE", "Geometry": {"BoundingBox": {"Width": 0.09605066478252411, "Height": 0.028307506814599037, "Left": 0.07809903472661972, "Top": 0.7137234807014465}, "Polygon": [{"X": 0.07809903472661972, "Y": 0.7206432819366455}, {"X": 0.17299394309520721, "Y": 0.7137234807014465}, {"X": 0.17414969205856323, "Y": 0.7351120710372925}, {"X": 0.07926055788993835, "Y": 0.7420309782028198}]}, "Id": "e2d81d36-5028-4581-9719-7e9c9de95a60", "Relationships": [{"Type": "CHILD", "Ids": ["54764ece-411d-4ba8-9286-f2c65d3bdf92", "6555cbd7-5e7b-42cd-b275-33d6d43292f1"]}]}, {"BlockType": "LINE", "Confidence": 95.82828521728516, "Text": "SCAC Code: HWCL", "Geometry": {"BoundingBox": {"Width": 0.13320794701576233, "Height": 0.0274136271327734, "Left": 0.0803559198975563, "Top": 0.7480894327163696}, "Polygon": [{"X": 0.0803559198975563, "Y": 0.7577321529388428}, {"X": 0.21260546147823334, "Y": 0.7480894327163696}, {"X": 0.21356387436389923, "Y": 0.7658613324165344}, {"X": 0.08132100850343704, "Y": 0.7755030989646912}]}, "Id": "3ab04225-affa-4257-8e54-68a7fd8bac09", "Relationships": [{"Type": "CHILD", "Ids": ["4d0b3f5c-9159-439e-83ab-d7cec2af94c9", "3b91bfc3-3900-4a3f-885e-dea6d49eab3d", "969f4db5-ab97-4089-bde5-dcbf5d31e08e"]}]}, {"BlockType": "LINE", "Confidence": 99.55372619628906, "Text": "Trucking Co. Name: HWC LOGIST", "Geometry": {"BoundingBox": {"Width": 0.23867374658584595, "Height": 0.038937442004680634, "Left": 0.08212066441774368, "Top": 0.7772454023361206}, "Polygon": [{"X": 0.08212066441774368, "Y": 0.7945614457130432}, {"X": 0.3196348249912262, "Y": 0.7772454023361206}, {"X": 0.32079440355300903, "Y": 0.7988688945770264}, {"X": 0.08329487591981888, "Y": 0.8161828517913818}]}, "Id": "aeea21c8-ce27-44c7-af7e-83c71dfdd449", "Relationships": [{"Type": "CHILD", "Ids": ["7dcb6663-7850-4957-8ddb-34b541747e07", "0fbfad53-74e5-43b3-ac20-0725f6a47d32", "8dfb9d57-2b83-4190-a29d-465c8c8ff0ff", "79fc0fee-858d-4b1c-81ba-9e5fa9c300e6", "b2540250-e351-4f7a-b328-f617311ceb69"]}]}, {"BlockType": "LINE", "Confidence": 98.00508880615234, "Text": "Driver chz: DCLZ400840", "Geometry": {"BoundingBox": {"Width": 0.17129972577095032, "Height": 0.030174067243933678, "Left": 0.08476737141609192, "Top": 0.8190756440162659}, "Polygon": [{"X": 0.08476737141609192, "Y": 0.8314933776855469}, {"X": 0.255111426115036, "Y": 0.8190756440162659}, {"X": 0.25606709718704224, "Y": 0.8368332982063293}, {"X": 0.08573164790868759, "Y": 0.8492497205734253}]}, "Id": "9a3240e4-3f05-45dd-a39a-1927813603fb", "Relationships": [{"Type": "CHILD", "Ids": ["594f38e1-7da6-4d98-a91c-67986924e99e", "3947efbc-7f48-4539-ac4a-e689f43f8ba9", "6cd20c1a-221c-471f-9e0a-39fb2ba74cc5"]}]}, {"BlockType": "LINE", "Confidence": 99.39722442626953, "Text": "Driver License:", "Geometry": {"BoundingBox": {"Width": 0.10793554782867432, "Height": 0.024872759357094765, "Left": 0.08676043152809143, "Top": 0.8612626194953918}, "Polygon": [{"X": 0.08676043152809143, "Y": 0.8690628409385681}, {"X": 0.1937740445137024, "Y": 0.8612626194953918}, {"X": 0.19469597935676575, "Y": 0.8783358931541443}, {"X": 0.08768758177757263, "Y": 0.8861353993415833}]}, "Id": "8cb6dcdd-3568-41f5-8b32-affaea0250fb", "Relationships": [{"Type": "CHILD", "Ids": ["1041c41c-1d07-4b4d-9307-afe16bd670d7", "e7128f77-abae-4db4-8f14-3847d57ffc10"]}]}, {"BlockType": "LINE", "Confidence": 92.71683502197266, "Text": "Tractor License: IC78JG GA", "Geometry": {"BoundingBox": {"Width": 0.19130852818489075, "Height": 0.03214774653315544, "Left": 0.0882902443408966, "Top": 0.8912960886955261}, "Polygon": [{"X": 0.0882902443408966, "Y": 0.9051675200462341}, {"X": 0.27861613035202026, "Y": 0.8912960886955261}, {"X": 0.27959877252578735, "Y": 0.9095738530158997}, {"X": 0.08928278088569641, "Y": 0.9234438538551331}]}, "Id": "1c7e75b5-4298-4fde-956f-da77b8bd6982", "Relationships": [{"Type": "CHILD", "Ids": ["f32dd21c-76fe-4205-9c02-760836948858", "0c841280-b20c-4d84-a95d-a8c429302fd3", "3749ebff-9c4e-4a52-8e1b-0a0856f9b73a", "b29f3623-c772-4674-8660-0201a87a5c6b"]}]}, {"BlockType": "LINE", "Confidence": 99.42920684814453, "Text": "Driver Name: 70169", "Geometry": {"BoundingBox": {"Width": 0.14489826560020447, "Height": 0.02829941175878048, "Left": 0.09045588225126266, "Top": 0.9320562481880188}, "Polygon": [{"X": 0.09045588225126266, "Y": 0.9425457715988159}, {"X": 0.23439423739910126, "Y": 0.9320562481880188}, {"X": 0.23535414040088654, "Y": 0.9498672485351562}, {"X": 0.09142308682203293, "Y": 0.9603556990623474}]}, "Id": "9f09667e-e557-46cc-b55d-15e91e190590", "Relationships": [{"Type": "CHILD", "Ids": ["d1195406-55da-4dec-8812-d3c88820eccd", "3f5daa4b-7a31-430c-b1d6-64788f1371c7", "212ed5e3-56c1-4e52-9596-65c282dfb29c"]}]}, {"BlockType": "LINE", "Confidence": 21.41053581237793, "Text": "-", "Geometry": {"BoundingBox": {"Width": 0.025373047217726707, "Height": 0.005397100001573563, "Left": 0.5053290128707886, "Top": 0.994342565536499}, "Polygon": [{"X": 0.5053290128707886, "Y": 0.9961774349212646}, {"X": 0.5305130481719971, "Y": 0.994342565536499}, {"X": 0.5307020545005798, "Y": 0.9979048371315002}, {"X": 0.5055182576179504, "Y": 0.9997396469116211}]}, "Id": "51dc0470-3cb9-4ba5-bb25-2e43742b3adf", "Relationships": [{"Type": "CHILD", "Ids": ["ccfa68ab-1d85-4f5e-85ee-fe5fa6c63393"]}]}, {"BlockType": "WORD", "Confidence": 88.53176879882812, "Text": "CITS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3343692123889923, "Height": 0.1253325343132019, "Left": 0.008415731601417065, "Top": 0.006163550075143576}, "Polygon": [{"X": 0.008415731601417065, "Y": 0.030197983607649803}, {"X": 0.33736899495124817, "Y": 0.006163550075143576}, {"X": 0.34278494119644165, "Y": 0.10747542232275009}, {"X": 0.013926315121352673, "Y": 0.13149607181549072}]}, "Id": "cae5bd9f-dd3f-4fea-b1a8-4802a8c25686"}, {"BlockType": "WORD", "Confidence": 91.03495788574219, "Text": "ConGlobal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.5657099485397339, "Height": 0.10161147266626358, "Left": 0.3841656744480133, "Top": 0.0}, "Polygon": [{"X": 0.3841656744480133, "Y": 0.013796668499708176}, {"X": 0.9453327655792236, "Y": 0.0}, {"X": 0.9498755931854248, "Y": 0.0606323666870594}, {"X": 0.3888484537601471, "Y": 0.10161147266626358}]}, "Id": "e6052637-e131-4bbc-9c4e-f686cd2ab147"}, {"BlockType": "WORD", "Confidence": 96.84131622314453, "Text": "OOCU8756309", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18352067470550537, "Height": 0.04126334562897682, "Left": 0.39462435245513916, "Top": 0.15602150559425354}, "Polygon": [{"X": 0.39462435245513916, "Y": 0.16931599378585815}, {"X": 0.5766682028770447, "Y": 0.15602150559425354}, {"X": 0.5781450271606445, "Y": 0.18399247527122498}, {"X": 0.39611566066741943, "Y": 0.19728486239910126}]}, "Id": "99050d8b-b557-407e-8dc7-a556c434931d"}, {"BlockType": "WORD", "Confidence": 99.17350769042969, "Text": "ConGlobal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07343252748250961, "Height": 0.023810740560293198, "Left": 0.41714489459991455, "Top": 0.21366766095161438}, "Polygon": [{"X": 0.41714489459991455, "Y": 0.2189578413963318}, {"X": 0.4895947575569153, "Y": 0.21366766095161438}, {"X": 0.4905773997306824, "Y": 0.23218877613544464}, {"X": 0.41813135147094727, "Y": 0.23747840523719788}]}, "Id": "3c48337c-74af-4fdb-99f6-21354fbe08b2"}, {"BlockType": "WORD", "Confidence": 99.55257415771484, "Text": "Industries", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0676213949918747, "Height": 0.022218171507120132, "Left": 0.49264290928840637, "Top": 0.20944738388061523}, "Polygon": [{"X": 0.49264290928840637, "Y": 0.21431802213191986}, {"X": 0.55934739112854, "Y": 0.20944738388061523}, {"X": 0.5602642893791199, "Y": 0.22679539024829865}, {"X": 0.49356314539909363, "Y": 0.23166555166244507}]}, "Id": "55b0886f-6bde-4843-b933-5754b0a8aa64"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "465", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026530995965003967, "Height": 0.018879810348153114, "Left": 0.42352208495140076, "Top": 0.24572010338306427}, "Polygon": [{"X": 0.42352208495140076, "Y": 0.24759116768836975}, {"X": 0.44914862513542175, "Y": 0.24572010338306427}, {"X": 0.45005306601524353, "Y": 0.26272904872894287}, {"X": 0.4244277775287628, "Y": 0.26459991931915283}]}, "Id": "ba22799e-1226-494f-9a51-fcfdf5285f8a"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0486566536128521, "Height": 0.021528325974941254, "Left": 0.4531535804271698, "Top": 0.2412443459033966}, "Polygon": [{"X": 0.4531535804271698, "Y": 0.2447270303964615}, {"X": 0.5008533000946045, "Y": 0.2412443459033966}, {"X": 0.5018102526664734, "Y": 0.25929033756256104}, {"X": 0.4541129469871521, "Y": 0.26277267932891846}]}, "Id": "a2a6c011-71e3-4c44-86b6-3df9856af3bd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Avenue", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0528358556330204, "Height": 0.02039325051009655, "Left": 0.5042990446090698, "Top": 0.23831304907798767}, "Polygon": [{"X": 0.5042990446090698, "Y": 0.24210664629936218}, {"X": 0.5562572479248047, "Y": 0.23831304907798767}, {"X": 0.5571349263191223, "Y": 0.2549130618572235}, {"X": 0.5051791071891785, "Y": 0.25870630145072937}]}, "Id": "7318c77d-884f-4241-9c21-23d4c7c205ef"}, {"BlockType": "WORD", "Confidence": 65.60108947753906, "Text": "Atlanta,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05356082320213318, "Height": 0.02196817845106125, "Left": 0.42968395352363586, "Top": 0.27116629481315613}, "Polygon": [{"X": 0.42968395352363586, "Y": 0.27500635385513306}, {"X": 0.482282429933548, "Y": 0.27116629481315613}, {"X": 0.48324477672576904, "Y": 0.2892948389053345}, {"X": 0.4306490123271942, "Y": 0.293134480714798}]}, "Id": "dc5b3799-8406-40d1-99bb-f1614e7cb588"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022675275802612305, "Height": 0.01943804696202278, "Left": 0.4853367209434509, "Top": 0.26850777864456177}, "Polygon": [{"X": 0.4853367209434509, "Y": 0.2700941562652588}, {"X": 0.5070655941963196, "Y": 0.26850777864456177}, {"X": 0.5080119967460632, "Y": 0.28635963797569275}, {"X": 0.4862842261791229, "Y": 0.28794583678245544}]}, "Id": "2d075d08-c825-4397-a214-05f46437ce35"}, {"BlockType": "WORD", "Confidence": 99.33294677734375, "Text": "30315", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042767561972141266, "Height": 0.020635435357689857, "Left": 0.5108575820922852, "Top": 0.26563048362731934}, "Polygon": [{"X": 0.5108575820922852, "Y": 0.26868492364883423}, {"X": 0.5526953935623169, "Y": 0.26563048362731934}, {"X": 0.5536251664161682, "Y": 0.28321176767349243}, {"X": 0.5117894411087036, "Y": 0.28626590967178345}]}, "Id": "158a3c0d-ed64-4f2f-a7ac-42bb7e4bf666"}, {"BlockType": "WORD", "Confidence": 99.03359985351562, "Text": "**************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10650090873241425, "Height": 0.027859780937433243, "Left": 0.4406694173812866, "Top": 0.29376649856567383}, "Polygon": [{"X": 0.4406694173812866, "Y": 0.3014633357524872}, {"X": 0.5461035370826721, "Y": 0.29376649856567383}, {"X": 0.5471703410148621, "Y": 0.31393036246299744}, {"X": 0.44174227118492126, "Y": 0.32162630558013916}]}, "Id": "c821b6f8-1f07-48d5-ad99-b6fde3b57082"}, {"BlockType": "WORD", "Confidence": 73.14512634277344, "Text": "Customer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07801157981157303, "Height": 0.022031160071492195, "Left": 0.06044497340917587, "Top": 0.38276031613349915}, "Polygon": [{"X": 0.06044497340917587, "Y": 0.38838955760002136}, {"X": 0.13756941258907318, "Y": 0.38276031613349915}, {"X": 0.1384565532207489, "Y": 0.39916276931762695}, {"X": 0.06133570894598961, "Y": 0.4047914743423462}]}, "Id": "ff12ebe2-f915-4f2f-89c6-a979b7356cf3"}, {"BlockType": "WORD", "Confidence": 93.27427673339844, "Text": "OOCL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04245408624410629, "Height": 0.021259650588035583, "Left": 0.14061492681503296, "Top": 0.37763211131095886}, "Polygon": [{"X": 0.14061492681503296, "Y": 0.3806590139865875}, {"X": 0.18208520114421844, "Y": 0.37763211131095886}, {"X": 0.18306902050971985, "Y": 0.39586520195007324}, {"X": 0.14160090684890747, "Y": 0.39889177680015564}]}, "Id": "e366a2ba-8191-47f3-871e-d7972b8223fc"}, {"BlockType": "WORD", "Confidence": 87.73377227783203, "Text": "Depot:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05208323150873184, "Height": 0.02398477867245674, "Left": 0.06252630054950714, "Top": 0.4215216338634491}, "Polygon": [{"X": 0.06252630054950714, "Y": 0.4252426326274872}, {"X": 0.11351201683282852, "Y": 0.4215216338634491}, {"X": 0.11460953205823898, "Y": 0.4417858123779297}, {"X": 0.06362675875425339, "Y": 0.44550639390945435}]}, "Id": "a9758a0b-e2dc-4b8d-8b9a-7053fe69caca"}, {"BlockType": "WORD", "Confidence": 98.19734954833984, "Text": "ATL02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04422476887702942, "Height": 0.021963270381093025, "Left": 0.11614054441452026, "Top": 0.41615086793899536}, "Polygon": [{"X": 0.11614054441452026, "Y": 0.4193043112754822}, {"X": 0.15934902429580688, "Y": 0.41615086793899536}, {"X": 0.16036531329154968, "Y": 0.4349610507488251}, {"X": 0.11715914309024811, "Y": 0.43811413645744324}]}, "Id": "a84a1639-c6d4-4ed2-9747-de7e92fc3228"}, {"BlockType": "WORD", "Confidence": 96.38333129882812, "Text": "Size:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03625694662332535, "Height": 0.020167313516139984, "Left": 0.0639975294470787, "Top": 0.4595613181591034}, "Polygon": [{"X": 0.0639975294470787, "Y": 0.4621375501155853}, {"X": 0.09930090606212616, "Y": 0.4595613181591034}, {"X": 0.10025447607040405, "Y": 0.47715267539024353}, {"X": 0.06495287269353867, "Y": 0.479728639125824}]}, "Id": "4f04d8a6-71e3-4501-bd36-66f338e376e9"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01974012516438961, "Height": 0.01874791644513607, "Left": 0.10227230191230774, "Top": 0.4575345516204834}, "Polygon": [{"X": 0.10227230191230774, "Y": 0.4589064121246338}, {"X": 0.12107160687446594, "Y": 0.4575345516204834}, {"X": 0.1220124289393425, "Y": 0.4749107360839844}, {"X": 0.10321405529975891, "Y": 0.4762824773788452}]}, "Id": "70029450-2af6-434d-bf6e-b7192832d77b"}, {"BlockType": "WORD", "Confidence": 98.27667236328125, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011913490481674671, "Height": 0.01496738102287054, "Left": 0.12393085658550262, "Top": 0.45756641030311584}, "Polygon": [{"X": 0.12393085658550262, "Y": 0.4583798944950104}, {"X": 0.135078564286232, "Y": 0.45756641030311584}, {"X": 0.13584434986114502, "Y": 0.4717203676700592}, {"X": 0.12469709664583206, "Y": 0.47253379225730896}]}, "Id": "ded35761-8bde-4f48-9bb9-df0fb62e8644"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08266068249940872, "Height": 0.02666379138827324, "Left": 0.0665721595287323, "Top": 0.4932759702205658}, "Polygon": [{"X": 0.0665721595287323, "Y": 0.4992256760597229}, {"X": 0.1481127291917801, "Y": 0.4932759702205658}, {"X": 0.14923283457756042, "Y": 0.5139907598495483}, {"X": 0.06769706308841705, "Y": 0.5199397802352905}]}, "Id": "cdd38345-000f-46cd-ac6f-e5bd2b1df56d"}, {"BlockType": "WORD", "Confidence": 98.2860336303711, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03997044637799263, "Height": 0.023430408909916878, "Left": 0.15084311366081238, "Top": 0.48972049355506897}, "Polygon": [{"X": 0.15084311366081238, "Y": 0.4925558865070343}, {"X": 0.1897023767232895, "Y": 0.48972049355506897}, {"X": 0.1908135563135147, "Y": 0.5103158354759216}, {"X": 0.15195657312870026, "Y": 0.5131508708000183}]}, "Id": "5db20604-b244-4389-8fcd-42046c098eec"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "CON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03547080606222153, "Height": 0.020446227863430977, "Left": 0.1928449124097824, "Top": 0.48583555221557617}, "Polygon": [{"X": 0.1928449124097824, "Y": 0.48835331201553345}, {"X": 0.22735033929347992, "Y": 0.48583555221557617}, {"X": 0.22831571102142334, "Y": 0.5037643313407898}, {"X": 0.1938120573759079, "Y": 0.5062817931175232}]}, "Id": "f18fb634-6827-4726-be0a-865c962da103"}, {"BlockType": "WORD", "Confidence": 83.3801040649414, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015593844465911388, "Height": 0.01674312725663185, "Left": 0.06885747611522675, "Top": 0.5363078117370605}, "Polygon": [{"X": 0.06885747611522675, "Y": 0.537383496761322}, {"X": 0.08360113948583603, "Y": 0.5363078117370605}, {"X": 0.08445131778717041, "Y": 0.5519753694534302}, {"X": 0.06970831006765366, "Y": 0.5530509352684021}]}, "Id": "0bebb5cb-c809-4a55-bb03-57b0fedd19d5"}, {"BlockType": "WORD", "Confidence": 82.54225158691406, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04019163176417351, "Height": 0.019711365923285484, "Left": 0.08788830786943436, "Top": 0.5322405099868774}, "Polygon": [{"X": 0.08788830786943436, "Y": 0.5351063013076782}, {"X": 0.12716792523860931, "Y": 0.5322405099868774}, {"X": 0.12807993590831757, "Y": 0.5490863919258118}, {"X": 0.08880219608545303, "Y": 0.5519518852233887}]}, "Id": "7146be1f-acc3-400c-bef4-688ae7fb0ed7"}, {"BlockType": "WORD", "Confidence": 99.55735778808594, "Text": "2022-12-27", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08096469938755035, "Height": 0.023716140538454056, "Left": 0.1305355280637741, "Top": 0.524623453617096}, "Polygon": [{"X": 0.1305355280637741, "Y": 0.5304602384567261}, {"X": 0.21053650975227356, "Y": 0.524623453617096}, {"X": 0.21150022745132446, "Y": 0.5425034165382385}, {"X": 0.13150331377983093, "Y": 0.5483396053314209}]}, "Id": "c8e68412-1d67-47b0-8754-e7a3b67935b9"}, {"BlockType": "WORD", "Confidence": 75.29834747314453, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0154091976583004, "Height": 0.01605433039367199, "Left": 0.0709550529718399, "Top": 0.5736606121063232}, "Polygon": [{"X": 0.0709550529718399, "Y": 0.5747253894805908}, {"X": 0.08555085957050323, "Y": 0.5736606121063232}, {"X": 0.0863642543554306, "Y": 0.5886502861976624}, {"X": 0.07176907360553741, "Y": 0.5897149443626404}]}, "Id": "a0b91ac2-881a-475e-aed6-7c3d2d9e5eaf"}, {"BlockType": "WORD", "Confidence": 72.019287109375, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0432903878390789, "Height": 0.019925475120544434, "Left": 0.0890192911028862, "Top": 0.5687049031257629}, "Polygon": [{"X": 0.0890192911028862, "Y": 0.5717965364456177}, {"X": 0.13139840960502625, "Y": 0.5687049031257629}, {"X": 0.1323096752166748, "Y": 0.5855391025543213}, {"X": 0.08993259072303772, "Y": 0.5886303782463074}]}, "Id": "e061144b-a91c-4a22-ad4a-187b1e8b6c07"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "12:11", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036157507449388504, "Height": 0.01952643133699894, "Left": 0.13543714582920074, "Top": 0.5654739141464233}, "Polygon": [{"X": 0.13543714582920074, "Y": 0.5680448412895203}, {"X": 0.17067870497703552, "Y": 0.5654739141464233}, {"X": 0.17159464955329895, "Y": 0.5824296474456787}, {"X": 0.13635478913784027, "Y": 0.5850003361701965}]}, "Id": "b9c400ba-391a-45d1-8f77-6f970091d191"}, {"BlockType": "WORD", "Confidence": 91.16250610351562, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015781110152602196, "Height": 0.016821036115288734, "Left": 0.0726652666926384, "Top": 0.609763503074646}, "Polygon": [{"X": 0.0726652666926384, "Y": 0.6108523607254028}, {"X": 0.08759268373250961, "Y": 0.609763503074646}, {"X": 0.08844637125730515, "Y": 0.6254957914352417}, {"X": 0.07351962476968765, "Y": 0.6265845894813538}]}, "Id": "c71da3a6-8ea1-403f-aff4-1df6c6f804b9"}, {"BlockType": "WORD", "Confidence": 86.87638854980469, "Text": "Condition:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07765523344278336, "Height": 0.02262406423687935, "Left": 0.0917401909828186, "Top": 0.6029117703437805}, "Polygon": [{"X": 0.0917401909828186, "Y": 0.6085090637207031}, {"X": 0.16847540438175201, "Y": 0.6029117703437805}, {"X": 0.16939541697502136, "Y": 0.6199390292167664}, {"X": 0.09266391396522522, "Y": 0.625535786151886}]}, "Id": "daeb3e90-51b3-4f29-86f1-89fb1f67b952"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "AV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021352818235754967, "Height": 0.01798829436302185, "Left": 0.17161042988300323, "Top": 0.6009951829910278}, "Polygon": [{"X": 0.17161042988300323, "Y": 0.6024877429008484}, {"X": 0.19207307696342468, "Y": 0.6009951829910278}, {"X": 0.19296325743198395, "Y": 0.6174909472465515}, {"X": 0.1725015640258789, "Y": 0.6189834475517273}]}, "Id": "b4ca9b52-c7b7-40cf-819b-175a0565ff1f"}, {"BlockType": "WORD", "Confidence": 91.8702163696289, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015957674011588097, "Height": 0.016970345750451088, "Left": 0.07482761144638062, "Top": 0.6469873785972595}, "Polygon": [{"X": 0.07482761144638062, "Y": 0.6480884552001953}, {"X": 0.0899241715669632, "Y": 0.6469873785972595}, {"X": 0.09078527987003326, "Y": 0.6628567576408386}, {"X": 0.0756894052028656, "Y": 0.6639577746391296}]}, "Id": "e9befc8a-ce3f-416e-a4f0-cfb025a30a70"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "Empty:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052894774824380875, "Height": 0.023516852408647537, "Left": 0.09387452155351639, "Top": 0.6423582434654236}, "Polygon": [{"X": 0.09387452155351639, "Y": 0.6461382508277893}, {"X": 0.14570146799087524, "Y": 0.6423582434654236}, {"X": 0.14676930010318756, "Y": 0.6620954871177673}, {"X": 0.0949452742934227, "Y": 0.6658750772476196}]}, "Id": "9935aa60-6f98-4c5b-9bf4-ca903c61648c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "True", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03136230632662773, "Height": 0.02013467438519001, "Left": 0.14860866963863373, "Top": 0.6382923126220703}, "Polygon": [{"X": 0.14860866963863373, "Y": 0.6405091881752014}, {"X": 0.1790032833814621, "Y": 0.6382923126220703}, {"X": 0.17997097969055176, "Y": 0.6562103629112244}, {"X": 0.14957791566848755, "Y": 0.6584270000457764}]}, "Id": "b84a0cab-daba-4b50-869c-8f70bfdb5b52"}, {"BlockType": "WORD", "Confidence": 97.65884399414062, "Text": "EIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02501433901488781, "Height": 0.018022051081061363, "Left": 0.07671020925045013, "Top": 0.6829102635383606}, "Polygon": [{"X": 0.07671020925045013, "Y": 0.6846702098846436}, {"X": 0.10084252804517746, "Y": 0.6829102635383606}, {"X": 0.1017245501279831, "Y": 0.6991725564002991}, {"X": 0.07759334146976471, "Y": 0.7009323239326477}]}, "Id": "dfd19b4f-406a-43b9-89e9-2f07a6706ab0"}, {"BlockType": "WORD", "Confidence": 98.79524230957031, "Text": "Num:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04257403314113617, "Height": 0.01947859302163124, "Left": 0.10452215373516083, "Top": 0.6792437434196472}, "Polygon": [{"X": 0.10452215373516083, "Y": 0.6822836995124817}, {"X": 0.14620672166347504, "Y": 0.6792437434196472}, {"X": 0.147096186876297, "Y": 0.6956826448440552}, {"X": 0.10541356354951859, "Y": 0.6987223625183105}]}, "Id": "03efc0e6-f508-4c5f-a4d3-d7c6ffc31e60"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08350201696157455, "Height": 0.02335430309176445, "Left": 0.1505405157804489, "Top": 0.6717352867126465}, "Polygon": [{"X": 0.1505405157804489, "Y": 0.6777568459510803}, {"X": 0.2331090122461319, "Y": 0.6717352867126465}, {"X": 0.23404254019260406, "Y": 0.6890685558319092}, {"X": 0.1514781266450882, "Y": 0.69508957862854}]}, "Id": "9e6e274f-f5c2-45ae-baf8-6108c362436a"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04183739796280861, "Height": 0.022913755849003792, "Left": 0.0781775638461113, "Top": 0.7191172242164612}, "Polygon": [{"X": 0.0781775638461113, "Y": 0.7220892310142517}, {"X": 0.1189342811703682, "Y": 0.7191172242164612}, {"X": 0.12001495808362961, "Y": 0.739059329032898}, {"X": 0.07926055788993835, "Y": 0.7420309782028198}]}, "Id": "54764ece-411d-4ba8-9286-f2c65d3bdf92"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SINGLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05171699821949005, "Height": 0.021430548280477524, "Left": 0.12223497778177261, "Top": 0.7137234807014465}, "Polygon": [{"X": 0.12223497778177261, "Y": 0.7174248695373535}, {"X": 0.17299394309520721, "Y": 0.7137234807014465}, {"X": 0.17395198345184326, "Y": 0.7314530611038208}, {"X": 0.1231955736875534, "Y": 0.7351540327072144}]}, "Id": "6555cbd7-5e7b-42cd-b275-33d6d43292f1"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "SCAC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04137331247329712, "Height": 0.019729046151041985, "Left": 0.08040980994701385, "Top": 0.7557740211486816}, "Polygon": [{"X": 0.08040980994701385, "Y": 0.7587243914604187}, {"X": 0.1208738461136818, "Y": 0.7557740211486816}, {"X": 0.12178312242031097, "Y": 0.772553026676178}, {"X": 0.08132100850343704, "Y": 0.7755030989646912}]}, "Id": "4d0b3f5c-9159-439e-83ab-d7cec2af94c9"}, {"BlockType": "WORD", "Confidence": 88.0323257446289, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043649621307849884, "Height": 0.02070334553718567, "Left": 0.12385144829750061, "Top": 0.7514474987983704}, "Polygon": [{"X": 0.12385144829750061, "Y": 0.7545607686042786}, {"X": 0.1665501445531845, "Y": 0.7514474987983704}, {"X": 0.1675010770559311, "Y": 0.7690378427505493}, {"X": 0.1248045265674591, "Y": 0.7721508145332336}]}, "Id": "3b91bfc3-3900-4a3f-885e-dea6d49eab3d"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "HWCL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04408072307705879, "Height": 0.02022533118724823, "Left": 0.1694604456424713, "Top": 0.7483615279197693}, "Polygon": [{"X": 0.1694604456424713, "Y": 0.7515084743499756}, {"X": 0.21262013912200928, "Y": 0.7483615279197693}, {"X": 0.2135411649942398, "Y": 0.7654402852058411}, {"X": 0.17038355767726898, "Y": 0.7685868740081787}]}, "Id": "969f4db5-ab97-4089-bde5-dcbf5d31e08e"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Trucking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06570286303758621, "Height": 0.02481646090745926, "Left": 0.0822029784321785, "Top": 0.7913663983345032}, "Polygon": [{"X": 0.0822029784321785, "Y": 0.7960771322250366}, {"X": 0.1468176394701004, "Y": 0.7913663983345032}, {"X": 0.1479058414697647, "Y": 0.8114726543426514}, {"X": 0.08329487591981888, "Y": 0.8161828517913818}]}, "Id": "7dcb6663-7850-4957-8ddb-34b541747e07"}, {"BlockType": "WORD", "Confidence": 99.5322494506836, "Text": "Co.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02424968220293522, "Height": 0.018596604466438293, "Left": 0.1504860669374466, "Top": 0.7886128425598145}, "Polygon": [{"X": 0.1504860669374466, "Y": 0.7903141975402832}, {"X": 0.17382264137268066, "Y": 0.7886128425598145}, {"X": 0.17473575472831726, "Y": 0.8055082559585571}, {"X": 0.15140029788017273, "Y": 0.8072094321250916}]}, "Id": "0fbfad53-74e5-43b3-ac20-0725f6a47d32"}, {"BlockType": "WORD", "Confidence": 98.84407043457031, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04928905889391899, "Height": 0.020409822463989258, "Left": 0.17787617444992065, "Top": 0.7848360538482666}, "Polygon": [{"X": 0.17787617444992065, "Y": 0.7883631587028503}, {"X": 0.22625534236431122, "Y": 0.7848360538482666}, {"X": 0.22716523706912994, "Y": 0.8017191290855408}, {"X": 0.17878840863704681, "Y": 0.8052458763122559}]}, "Id": "8dfb9d57-2b83-4190-a29d-465c8c8ff0ff"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "HWC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03715340048074722, "Height": 0.019560813903808594, "Left": 0.22971206903457642, "Top": 0.7813825607299805}, "Polygon": [{"X": 0.22971206903457642, "Y": 0.7840248942375183}, {"X": 0.2659555673599243, "Y": 0.7813825607299805}, {"X": 0.26686546206474304, "Y": 0.7983012795448303}, {"X": 0.23062370717525482, "Y": 0.8009433746337891}]}, "Id": "79fc0fee-858d-4b1c-81ba-9e5fa9c300e6"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "LOGIST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051699161529541016, "Height": 0.0209619402885437, "Left": 0.2688612937927246, "Top": 0.7772454023361206}, "Polygon": [{"X": 0.2688612937927246, "Y": 0.7809470295906067}, {"X": 0.3196348249912262, "Y": 0.7772454023361206}, {"X": 0.3205604553222656, "Y": 0.7945060133934021}, {"X": 0.2697893977165222, "Y": 0.7982073426246643}]}, "Id": "b2540250-e351-4f7a-b328-f617311ceb69"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04766985774040222, "Height": 0.01989782601594925, "Left": 0.08483625948429108, "Top": 0.8293519020080566}, "Polygon": [{"X": 0.08483625948429108, "Y": 0.832761824131012}, {"X": 0.13161291182041168, "Y": 0.8293519020080566}, {"X": 0.1325061172246933, "Y": 0.8458401560783386}, {"X": 0.08573164790868759, "Y": 0.8492497205734253}]}, "Id": "594f38e1-7da6-4d98-a91c-67986924e99e"}, {"BlockType": "WORD", "Confidence": 95.33880615234375, "Text": "chz:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029902972280979156, "Height": 0.01891418732702732, "Left": 0.1345471888780594, "Top": 0.8265808820724487}, "Polygon": [{"X": 0.1345471888780594, "Y": 0.8286944627761841}, {"X": 0.16354157030582428, "Y": 0.8265808820724487}, {"X": 0.16445015370845795, "Y": 0.8433816432952881}, {"X": 0.13545715808868408, "Y": 0.8454950451850891}]}, "Id": "3947efbc-7f48-4539-ac4a-e689f43f8ba9"}, {"BlockType": "WORD", "Confidence": 98.68622589111328, "Text": "DCLZ400840", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08956801146268845, "Height": 0.02390182949602604, "Left": 0.166482076048851, "Top": 0.8190756440162659}, "Polygon": [{"X": 0.166482076048851, "Y": 0.8255365490913391}, {"X": 0.255111426115036, "Y": 0.8190756440162659}, {"X": 0.25605008006095886, "Y": 0.8365172743797302}, {"X": 0.16742514073848724, "Y": 0.8429774641990662}]}, "Id": "6cd20c1a-221c-471f-9e0a-39fb2ba74cc5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04741043597459793, "Height": 0.020308762788772583, "Left": 0.0867687240242958, "Top": 0.8658266067504883}, "Polygon": [{"X": 0.0867687240242958, "Y": 0.8692155480384827}, {"X": 0.1332625448703766, "Y": 0.8658266067504883}, {"X": 0.13417915999889374, "Y": 0.8827467560768127}, {"X": 0.08768758177757263, "Y": 0.8861353993415833}]}, "Id": "1041c41c-1d07-4b4d-9307-afe16bd670d7"}, {"BlockType": "WORD", "Confidence": 98.80421447753906, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05831468850374222, "Height": 0.020838871598243713, "Left": 0.13635866343975067, "Top": 0.8612626194953918}, "Polygon": [{"X": 0.13635866343975067, "Y": 0.8654476404190063}, {"X": 0.1937740445137024, "Y": 0.8612626194953918}, {"X": 0.1946733593940735, "Y": 0.8779168725013733}, {"X": 0.13726070523262024, "Y": 0.8821014761924744}]}, "Id": "e7128f77-abae-4db4-8f14-3847d57ffc10"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05453137308359146, "Height": 0.020504452288150787, "Left": 0.08838152140378952, "Top": 0.9029393792152405}, "Polygon": [{"X": 0.08838152140378952, "Y": 0.9068482518196106}, {"X": 0.14201416075229645, "Y": 0.9029393792152405}, {"X": 0.14291289448738098, "Y": 0.9195353388786316}, {"X": 0.08928278088569641, "Y": 0.9234438538551331}]}, "Id": "f32dd21c-76fe-4205-9c02-760836948858"}, {"BlockType": "WORD", "Confidence": 86.318359375, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05822363123297691, "Height": 0.020854204893112183, "Left": 0.14510399103164673, "Top": 0.8980073928833008}, "Polygon": [{"X": 0.14510399103164673, "Y": 0.9021852612495422}, {"X": 0.20242740213871002, "Y": 0.8980073928833008}, {"X": 0.20332762598991394, "Y": 0.9146841764450073}, {"X": 0.14600692689418793, "Y": 0.9188616275787354}]}, "Id": "0c841280-b20c-4d84-a95d-a8c429302fd3"}, {"BlockType": "WORD", "Confidence": 84.9354248046875, "Text": "IC78JG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048284485936164856, "Height": 0.02098165638744831, "Left": 0.20578312873840332, "Top": 0.8931540250778198}, "Polygon": [{"X": 0.20578312873840332, "Y": 0.8966043591499329}, {"X": 0.25312381982803345, "Y": 0.8931540250778198}, {"X": 0.25406762957572937, "Y": 0.9106857180595398}, {"X": 0.20672930777072906, "Y": 0.9141356945037842}]}, "Id": "3749ebff-9c4e-4a52-8e1b-0a0856f9b73a"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022872326895594597, "Height": 0.017876237630844116, "Left": 0.25666743516921997, "Top": 0.8922032117843628}, "Polygon": [{"X": 0.25666743516921997, "Y": 0.8938064575195312}, {"X": 0.2786649167537689, "Y": 0.8922032117843628}, {"X": 0.2795397639274597, "Y": 0.9084763526916504}, {"X": 0.25754332542419434, "Y": 0.9100794196128845}]}, "Id": "b29f3623-c772-4674-8660-0201a87a5c6b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047843579202890396, "Height": 0.020273445174098015, "Left": 0.09050782769918442, "Top": 0.9400822520256042}, "Polygon": [{"X": 0.09050782769918442, "Y": 0.9435023069381714}, {"X": 0.13743840157985687, "Y": 0.9400822520256042}, {"X": 0.1383514106273651, "Y": 0.9569359421730042}, {"X": 0.09142308682203293, "Y": 0.9603556990623474}]}, "Id": "d1195406-55da-4dec-8812-d3c88820eccd"}, {"BlockType": "WORD", "Confidence": 98.52598571777344, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04909975454211235, "Height": 0.019835829734802246, "Left": 0.140438973903656, "Top": 0.9363414645195007}, "Polygon": [{"X": 0.140438973903656, "Y": 0.9398553371429443}, {"X": 0.18865691125392914, "Y": 0.9363414645195007}, {"X": 0.18953873217105865, "Y": 0.9526637196540833}, {"X": 0.1413230299949646, "Y": 0.956177294254303}]}, "Id": "3f5daa4b-7a31-430c-b1d6-64788f1371c7"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "70169", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04355312138795853, "Height": 0.01986936293542385, "Left": 0.19174446165561676, "Top": 0.9320562481880188}, "Polygon": [{"X": 0.19174446165561676, "Y": 0.9351643919944763}, {"X": 0.23439423739910126, "Y": 0.9320562481880188}, {"X": 0.23529759049415588, "Y": 0.9488178491592407}, {"X": 0.19264985620975494, "Y": 0.9519256353378296}]}, "Id": "212ed5e3-56c1-4e52-9596-65c282dfb29c"}, {"BlockType": "WORD", "Confidence": 21.41053581237793, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025471093133091927, "Height": 0.005657442379742861, "Left": 0.5053290128707886, "Top": 0.994342565536499}, "Polygon": [{"X": 0.5053290128707886, "Y": 0.9961774349212646}, {"X": 0.5305130481719971, "Y": 0.994342565536499}, {"X": 0.5308001041412354, "Y": 0.9997528195381165}, {"X": 0.5056164860725403, "Y": 1.0}]}, "Id": "ccfa68ab-1d85-4f5e-85ee-fe5fa6c63393"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "cf5a5301-12ad-464c-a811-91307a116d9a", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "cf5a5301-12ad-464c-a811-91307a116d9a", "content-type": "application/x-amz-json-1.1", "content-length": "43953", "date": "Thu, 18 Sep 2025 07:52:56 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "bf80a691-d61f-459c-aa8f-dd04b854ba12", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:52:59 GMT", "content-type": "application/json", "content-length": "1045", "connection": "keep-alive", "x-amzn-requestid": "bf80a691-d61f-459c-aa8f-dd04b854ba12"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The content appears like a shipping or gate-in record: \"CITS ConGlobal… In Date: 2022-12-27… In Time: 12:11… In Condition: AV… In Empty: True… EIR Num: **********… SCAC Code: HWCL… Trucking Co. Name: HWC LOGIST… Driver chz: DCLZ400840…\". This looks like an ingate document. Possibly \"Ingress Gate\" record. There is an EIR number (Electronic Information Record) which is used in loading/unloading. The keywords: \"In Date\", \"In Time\", \"In Condition\", \"In Empty\". That seems like an ingate doc. So doc_type: ingate. We'll classify page1 as ingate. No continuation. Output: list with one object."}}}, {"toolUse": {"toolUseId": "tooluse_hJCcoLcZTQyEWHtXmw9r1A", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2236, "outputTokens": 215, "totalTokens": 2451}, "metrics": {"latencyMs": 1392}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/75bd805b_25_ingate_8.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCITS ConGlobal\nOOCU8756309\nConGlobal Industries\n465 Sawtell Avenue\nAtlanta, GA 30315\n**************\nCustomer: OOCL\nDepot: ATL02\nSize: 40 H\nEquipment Type: CON\nIn Date: 2022-12-27\nin Time: 12:11\nIn Condition: AV\nIn Empty: True\nEIR Num: **********\nType: SINGLE\nSCAC Code: HWCL\nTrucking Co. Name: HWC LOGIST\nDriver chz: DCLZ400840\nDriver License:\nTractor License: IC78JG GA\nDriver Name: 70169\n-\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}