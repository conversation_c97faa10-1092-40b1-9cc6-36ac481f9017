2025-09-23 22:16:40,546 - INFO - Logging initialized. Log file: logs/test_classification_20250923_221640.log
2025-09-23 22:16:40,547 - INFO - 📁 Found 1 files to process
2025-09-23 22:16:40,547 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 22:16:40,547 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 22:16:40,547 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 22:16:40,548 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 22:16:40,549 - INFO - ⬆️ [22:16:40] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:16:46,337 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/d160e48d_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:16:46,338 - INFO - 🔍 [22:16:46] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:16:46,338 - INFO - Initializing TextractProcessor...
2025-09-23 22:16:46,350 - INFO - Initializing BedrockProcessor...
2025-09-23 22:16:46,355 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d160e48d_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:16:46,355 - INFO - Processing PDF from S3...
2025-09-23 22:16:46,356 - INFO - Downloading PDF from S3 to /tmp/tmp9iij19b9/d160e48d_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:16:51,656 - INFO - Splitting PDF into individual pages...
2025-09-23 22:16:51,660 - INFO - Splitting PDF d160e48d_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 22:16:51,668 - INFO - Split PDF into 7 pages
2025-09-23 22:16:51,668 - INFO - Processing pages with Textract in parallel...
2025-09-23 22:16:51,669 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 22:16:56,908 - INFO - Page 3: Extracted 929 characters, 64 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_003.pdf
2025-09-23 22:16:56,908 - INFO - Successfully processed page 3
2025-09-23 22:16:57,348 - INFO - Page 5: Extracted 2022 characters, 27 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_005.pdf
2025-09-23 22:16:57,348 - INFO - Successfully processed page 5
2025-09-23 22:16:57,405 - INFO - Page 2: Extracted 1174 characters, 29 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_002.pdf
2025-09-23 22:16:57,405 - INFO - Successfully processed page 2
2025-09-23 22:16:57,827 - INFO - Page 4: Extracted 1151 characters, 63 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_004.pdf
2025-09-23 22:16:57,827 - INFO - Successfully processed page 4
2025-09-23 22:16:57,948 - INFO - Page 1: Extracted 1168 characters, 45 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_001.pdf
2025-09-23 22:16:57,949 - INFO - Successfully processed page 1
2025-09-23 22:16:58,449 - INFO - Page 6: Extracted 4159 characters, 115 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_006.pdf
2025-09-23 22:16:58,449 - INFO - Successfully processed page 6
2025-09-23 22:17:01,023 - INFO - Page 7: Extracted 788 characters, 55 lines from d160e48d_Purchase_Order_VRSD0E9045781_dccdfd01_page_007.pdf
2025-09-23 22:17:01,023 - INFO - Successfully processed page 7
2025-09-23 22:17:01,024 - INFO - Combined 7 pages into final text
2025-09-23 22:17:01,024 - INFO - Text validation for d160e48d_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 22:17:01,025 - INFO - Analyzing document types with Bedrock...
2025-09-23 22:17:01,025 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 22:17:01,025 - INFO - === DEBUG: BEDROCK CALL_PARAMS ===
2025-09-23 22:17:01,025 - INFO - tool_config parameter type: <class 'dict'>
2025-09-23 22:17:01,025 - INFO - tool_config is None: False
2025-09-23 22:17:01,026 - INFO - tool_config is truthy: True
2025-09-23 22:17:01,026 - INFO - ✅ tool_config added to call_params as 'toolConfig'
2025-09-23 22:17:01,026 - INFO - toolConfig keys: ['tools']
2025-09-23 22:17:01,026 - INFO - Final call_params keys: ['modelId', 'system', 'messages', 'inferenceConfig', 'toolConfig', 'additionalModelRequestFields']
2025-09-23 22:17:01,026 - INFO - === END BEDROCK DEBUG ===
2025-09-23 22:17:06,245 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d160e48d_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:06,353 - INFO - 

Purchase_Order_VRSD0E9045781.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        },
        {
            "page_no": 4,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 5,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 6,
            "doc_type": "bol"
        },
        {
            "page_no": 7,
            "doc_type": "pod"
        }
    ]
}
2025-09-23 22:17:06,353 - INFO - 

✓ Saved result: output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,781 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d160e48d_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,782 - INFO - 
📊 Processing Summary:
2025-09-23 22:17:07,782 - INFO -    Total files: 1
2025-09-23 22:17:07,782 - INFO -    Successful: 1
2025-09-23 22:17:07,782 - INFO -    Failed: 0
2025-09-23 22:17:07,782 - INFO -    Duration: 27.23 seconds
2025-09-23 22:17:07,782 - INFO -    Output directory: output
2025-09-23 22:17:07,782 - INFO - 
📋 Successfully Processed Files:
2025-09-23 22:17:07,782 - INFO -    📄 Purchase_Order_VRSD0E9045781.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"rate_confirmation"},{"page_no":5,"doc_type":"rate_confirmation"},{"page_no":6,"doc_type":"bol"},{"page_no":7,"doc_type":"pod"}]}
2025-09-23 22:17:07,782 - INFO - 
============================================================================================================================================
2025-09-23 22:17:07,782 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-23 22:17:07,782 - INFO - ============================================================================================================================================
2025-09-23 22:17:07,782 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-23 22:17:07,782 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 22:17:07,782 - INFO - Purchase_Order_VRSD0E9045781.pdf                   1      invoice              run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,782 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - Purchase_Order_VRSD0E9045781.pdf                   2      other                run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - Purchase_Order_VRSD0E9045781.pdf                   3      invoice              run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - Purchase_Order_VRSD0E9045781.pdf                   4      rate_confirmation    run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - Purchase_Order_VRSD0E9045781.pdf                   5      rate_confirmation    run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - Purchase_Order_VRSD0E9045781.pdf                   6      bol                  run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - Purchase_Order_VRSD0E9045781.pdf                   7      pod                  run1_Purchase_Order_VRSD0E9045781.json            
2025-09-23 22:17:07,783 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/mansi/Purchase_Order_VRSD0E9045781.pdf
2025-09-23 22:17:07,783 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - 
2025-09-23 22:17:07,783 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-23 22:17:07,783 - INFO - Total entries: 7
2025-09-23 22:17:07,783 - INFO - ============================================================================================================================================
2025-09-23 22:17:07,783 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-23 22:17:07,783 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 22:17:07,783 - INFO -   1. Purchase_Order_VRSD0E9045781.pdf    Page 1   → invoice         | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO -   2. Purchase_Order_VRSD0E9045781.pdf    Page 2   → other           | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO -   3. Purchase_Order_VRSD0E9045781.pdf    Page 3   → invoice         | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO -   4. Purchase_Order_VRSD0E9045781.pdf    Page 4   → rate_confirmation | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO -   5. Purchase_Order_VRSD0E9045781.pdf    Page 5   → rate_confirmation | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO -   6. Purchase_Order_VRSD0E9045781.pdf    Page 6   → bol             | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO -   7. Purchase_Order_VRSD0E9045781.pdf    Page 7   → pod             | run1_Purchase_Order_VRSD0E9045781.json
2025-09-23 22:17:07,783 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-23 22:17:07,783 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 27.234638, 'processed_files': [{'filename': 'Purchase_Order_VRSD0E9045781.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'invoice'}, {'page_no': 4, 'doc_type': 'rate_confirmation'}, {'page_no': 5, 'doc_type': 'rate_confirmation'}, {'page_no': 6, 'doc_type': 'bol'}, {'page_no': 7, 'doc_type': 'pod'}]}, 'file_path': 'mansi/Purchase_Order_VRSD0E9045781.pdf'}]}
