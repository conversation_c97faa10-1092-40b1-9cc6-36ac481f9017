{"classification_result": {"documents": [{"page_no": 1, "doc_type": "other"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 4.450841029068897e-09}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 5.716121620480408e-08, "Y": 1.0}]}, "Id": "96e109c6-38b0-4b75-866d-abd6af437acc", "Relationships": [{"Type": "CHILD", "Ids": ["962430f3-f126-452e-832e-a99f6cc64666", "b1af698c-b0df-4b94-8b1a-c7730e34f08e", "70493ab4-14eb-4416-8c8b-0b8fdcd541ab", "f59a9546-6117-4f69-bfcf-a0f566e26840", "6c5b56a0-5d7d-4446-b8dc-28b03588d576", "1be1e994-9b2f-477f-8755-641aecfa6310", "5347b90d-f459-4e4c-9ddb-9925cf610d59", "fce40d72-a303-4b15-832d-21dcac7700bb", "445e08d4-b44d-4b3a-ba8b-4d4f5739876d", "da10c15d-5dbe-4045-9daf-7de6b92f6e2b", "cdcf4b82-c633-47b6-909c-860d5e9a9a91", "ad35f9c0-f92d-42ef-a523-c2541627f7ba", "38b58d25-f513-4c1f-9789-53b5250573bb", "6f03d33f-e41d-47c5-9a45-5537a2d06e66", "761757e3-73a7-4033-ab68-e149747b64ee", "2f475bdf-d1d6-4209-a8e7-e552f753b01b", "15e28875-62cb-493c-8c08-e7979e8ad5c0", "1d2f53b2-8116-4af7-9925-7afeb5e8a84b", "26c9d6c8-40a7-4772-8220-aae1177839e6", "a93c70c5-498d-49b3-b7f8-8a904bafc43b", "bd388590-a8d3-4bba-a56b-ad93ac310007", "81de451c-1668-4a4e-8326-112fa74307f0", "76af1dfa-c77e-4506-9b53-53ada03472d3", "c1e9de33-aabf-4228-8606-3bc0f9ee91aa", "63c0c905-4363-4b56-84f6-9ee30595db7d", "cb696188-dbc8-40e1-a8c1-361b11020392", "80401883-acfc-46ed-b541-d0f44e34cf0d", "df812042-21d9-4858-99a9-4f821655b5b2", "ec72fe95-ca78-4393-8fd1-b5dc53ab4846", "7a7fc471-3168-449d-9fa3-ac1b8ad2eb4d", "b048302f-f4bb-430d-8e77-ec5c4df2215a", "14ac2820-bf97-4a90-98e0-5f3abf417941", "61cc0972-b296-42e7-b8f8-9061f1ea1de6", "cdd9b896-2de9-4ad7-81ca-ac00ec2bc998", "e851896c-e475-447e-b850-d08b0b8d0b91", "5a0f586f-ac74-4235-aac8-242a594e8e4c", "09574352-023b-4c0e-8b74-226c73f68f49", "6208eb4d-22b3-4cf7-9cd0-7f5b06fb6585", "4107c5d9-6701-4fe3-a70f-b3c7188125a5", "7576ed3d-313d-4235-8a88-a7da19d5997c", "d32e462e-2f06-43c2-b5bb-ca6136c048ec"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "APPROVED", "Geometry": {"BoundingBox": {"Width": 0.11620873957872391, "Height": 0.013804974965751171, "Left": 0.16856591403484344, "Top": 0.05330808088183403}, "Polygon": [{"X": 0.16856594383716583, "Y": 0.053308822214603424}, {"X": 0.28477466106414795, "Y": 0.05330808088183403}, {"X": 0.2847746014595032, "Y": 0.06711231172084808}, {"X": 0.16856591403484344, "Y": 0.06711305677890778}]}, "Id": "962430f3-f126-452e-832e-a99f6cc64666", "Relationships": [{"Type": "CHILD", "Ids": ["53c490ef-cda1-4ad6-bdbe-b231a1547851"]}]}, {"BlockType": "LINE", "Confidence": 99.55655670166016, "Text": "MAERSK", "Geometry": {"BoundingBox": {"Width": 0.04685927554965019, "Height": 0.0064019979909062386, "Left": 0.098813995718956, "Top": 0.06787344813346863}, "Polygon": [{"X": 0.09881400316953659, "Y": 0.0678737461566925}, {"X": 0.14567327499389648, "Y": 0.06787344813346863}, {"X": 0.1456732600927353, "Y": 0.07427514344453812}, {"X": 0.098813995718956, "Y": 0.07427544891834259}]}, "Id": "b1af698c-b0df-4b94-8b1a-c7730e34f08e", "Relationships": [{"Type": "CHILD", "Ids": ["94dbae64-c89d-4fd5-9eeb-f57bb79ed4f2"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "Reuse empty container", "Geometry": {"BoundingBox": {"Width": 0.23427945375442505, "Height": 0.017574992030858994, "Left": 0.1926911175251007, "Top": 0.07330680638551712}, "Polygon": [{"X": 0.1926911622285843, "Y": 0.0733083114027977}, {"X": 0.42697057127952576, "Y": 0.07330680638551712}, {"X": 0.4269704818725586, "Y": 0.09088029712438583}, {"X": 0.1926911175251007, "Y": 0.09088180214166641}]}, "Id": "70493ab4-14eb-4416-8c8b-0b8fdcd541ab", "Relationships": [{"Type": "CHILD", "Ids": ["689f4147-c1e1-4c3c-bb2a-e3b2278d58c2", "8a89310e-8df1-4f8b-8735-57218013ae96", "92b4df47-b924-4ada-8a87-6bafeb4b88b7"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Container number", "Geometry": {"BoundingBox": {"Width": 0.1453874409198761, "Height": 0.01171440165489912, "Left": 0.07359862327575684, "Top": 0.11026330292224884}, "Polygon": [{"X": 0.07359863072633743, "Y": 0.11026424169540405}, {"X": 0.21898606419563293, "Y": 0.11026330292224884}, {"X": 0.21898603439331055, "Y": 0.12197676301002502}, {"X": 0.07359862327575684, "Y": 0.12197770923376083}]}, "Id": "f59a9546-6117-4f69-bfcf-a0f566e26840", "Relationships": [{"Type": "CHILD", "Ids": ["bb384df7-1441-4f40-b6dc-3c185465ce94", "b08b18df-335b-43d6-9a68-d38333561338"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Export booking ref.", "Geometry": {"BoundingBox": {"Width": 0.14948688447475433, "Height": 0.014359677210450172, "Left": 0.35321247577667236, "Top": 0.10919293761253357}, "Polygon": [{"X": 0.35321253538131714, "Y": 0.10919390618801117}, {"X": 0.5026993751525879, "Y": 0.10919293761253357}, {"X": 0.5026992559432983, "Y": 0.12355165183544159}, {"X": 0.35321247577667236, "Y": 0.12355262041091919}]}, "Id": "6c5b56a0-5d7d-4446-b8dc-28b03588d576", "Relationships": [{"Type": "CHILD", "Ids": ["ec517fce-e00d-488b-b7d6-5d9b5f416f88", "5bbae16d-f801-449c-acfc-4d1efcb6064e", "d65db233-ac0c-4bbe-89fd-53e86267ce57"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Reuse date", "Geometry": {"BoundingBox": {"Width": 0.08762527257204056, "Height": 0.011470870114862919, "Left": 0.6298614740371704, "Top": 0.10862217098474503}, "Polygon": [{"X": 0.6298615336418152, "Y": 0.10862273722887039}, {"X": 0.7174867391586304, "Y": 0.10862217098474503}, {"X": 0.7174866199493408, "Y": 0.12009247392416}, {"X": 0.6298614740371704, "Y": 0.12009304016828537}]}, "Id": "1be1e994-9b2f-477f-8755-641aecfa6310", "Relationships": [{"Type": "CHILD", "Ids": ["b039b168-9fc7-4123-a206-a0d3344f7927", "610f6130-55a8-4a99-b1d9-77f758322c4b"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "CAAU8485442", "Geometry": {"BoundingBox": {"Width": 0.130105659365654, "Height": 0.011947781778872013, "Left": 0.0735413134098053, "Top": 0.12749287486076355}, "Polygon": [{"X": 0.0735413208603859, "Y": 0.1274937242269516}, {"X": 0.2036469727754593, "Y": 0.12749287486076355}, {"X": 0.2036469429731369, "Y": 0.13943980634212494}, {"X": 0.0735413134098053, "Y": 0.139440655708313}]}, "Id": "5347b90d-f459-4e4c-9ddb-9925cf610d59", "Relationships": [{"Type": "CHILD", "Ids": ["eec15355-49f0-434f-a8e9-556518fdf026"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "257637187", "Geometry": {"BoundingBox": {"Width": 0.1009463295340538, "Height": 0.011916875839233398, "Left": 0.35220593214035034, "Top": 0.12656144797801971}, "Polygon": [{"X": 0.3522059917449951, "Y": 0.12656210362911224}, {"X": 0.45315226912498474, "Y": 0.12656144797801971}, {"X": 0.45315220952033997, "Y": 0.13847766816616058}, {"X": 0.35220593214035034, "Y": 0.1384783238172531}]}, "Id": "fce40d72-a303-4b15-832d-21dcac7700bb", "Relationships": [{"Type": "CHILD", "Ids": ["86f2ee28-9545-402a-b445-e963c436d920"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "09/12/2025", "Geometry": {"BoundingBox": {"Width": 0.10431566089391708, "Height": 0.012517169117927551, "Left": 0.6292881965637207, "Top": 0.1257018744945526}, "Polygon": [{"X": 0.6292883157730103, "Y": 0.12570254504680634}, {"X": 0.7336038947105408, "Y": 0.1257018744945526}, {"X": 0.7336037755012512, "Y": 0.13821835815906525}, {"X": 0.6292881965637207, "Y": 0.13821904361248016}]}, "Id": "445e08d4-b44d-4b3a-ba8b-4d4f5739876d", "Relationships": [{"Type": "CHILD", "Ids": ["49651fe3-cfe2-4422-a85b-4227698c5c67"]}]}, {"BlockType": "LINE", "Confidence": 99.68679809570312, "Text": "Import location", "Geometry": {"BoundingBox": {"Width": 0.12254814803600311, "Height": 0.014345572330057621, "Left": 0.0744355246424675, "Top": 0.1545090526342392}, "Polygon": [{"X": 0.07443553954362869, "Y": 0.15450985729694366}, {"X": 0.1969836801290512, "Y": 0.1545090526342392}, {"X": 0.19698365032672882, "Y": 0.16885381937026978}, {"X": 0.0744355246424675, "Y": 0.16885462403297424}]}, "Id": "da10c15d-5dbe-4045-9daf-7de6b92f6e2b", "Relationships": [{"Type": "CHILD", "Ids": ["9213f9cc-9320-46ac-abc4-bfb06feed653", "885669ee-0831-421e-b4e8-a99f2ca400de"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Export location", "Geometry": {"BoundingBox": {"Width": 0.11969556659460068, "Height": 0.014354727230966091, "Left": 0.28300461173057556, "Top": 0.15384842455387115}, "Polygon": [{"X": 0.28300467133522034, "Y": 0.15384921431541443}, {"X": 0.40270018577575684, "Y": 0.15384842455387115}, {"X": 0.4027000963687897, "Y": 0.16820237040519714}, {"X": 0.28300461173057556, "Y": 0.16820316016674042}]}, "Id": "cdcf4b82-c633-47b6-909c-860d5e9a9a91", "Relationships": [{"Type": "CHILD", "Ids": ["8871a008-e18e-40d5-bf91-01da475fc4b8", "d50edd53-a1ed-43e4-a181-8a28cd34d557"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.03896303474903107, "Height": 0.01102838758379221, "Left": 0.4933297336101532, "Top": 0.15372797846794128}, "Polygon": [{"X": 0.493329793214798, "Y": 0.15372823178768158}, {"X": 0.5322927832603455, "Y": 0.15372797846794128}, {"X": 0.5322927236557007, "Y": 0.16475610435009003}, {"X": 0.4933297336101532, "Y": 0.16475635766983032}]}, "Id": "ad35f9c0-f92d-42ef-a523-c2541627f7ba", "Relationships": [{"Type": "CHILD", "Ids": ["9bb3c7db-075e-4d9e-8f43-132f1a4886cf"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Illinois", "Geometry": {"BoundingBox": {"Width": 0.058630332350730896, "Height": 0.012309109792113304, "Left": 0.07405391335487366, "Top": 0.17137058079242706}, "Polygon": [{"X": 0.07405392080545425, "Y": 0.1713709533214569}, {"X": 0.13268424570560455, "Y": 0.17137058079242706}, {"X": 0.13268423080444336, "Y": 0.18367929756641388}, {"X": 0.07405391335487366, "Y": 0.18367968499660492}]}, "Id": "38b58d25-f513-4c1f-9789-53b5250573bb", "Relationships": [{"Type": "CHILD", "Ids": ["d1b4dcd9-3771-4b2e-9afe-ff8d6bff8767"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Illinois", "Geometry": {"BoundingBox": {"Width": 0.05793018266558647, "Height": 0.012232729233801365, "Left": 0.28273314237594604, "Top": 0.17069926857948303}, "Polygon": [{"X": 0.2827332019805908, "Y": 0.17069964110851288}, {"X": 0.340663343667984, "Y": 0.17069926857948303}, {"X": 0.34066328406333923, "Y": 0.18293161690235138}, {"X": 0.28273314237594604, "Y": 0.18293198943138123}]}, "Id": "6f03d33f-e41d-47c5-9a45-5537a2d06e66", "Relationships": [{"Type": "CHILD", "Ids": ["cd7345ae-5048-4942-ba49-5f9815828fe7"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "USD30", "Geometry": {"BoundingBox": {"Width": 0.05975371226668358, "Height": 0.011833122931420803, "Left": 0.49279913306236267, "Top": 0.1705559641122818}, "Polygon": [{"X": 0.49279922246932983, "Y": 0.17055635154247284}, {"X": 0.5525528788566589, "Y": 0.1705559641122818}, {"X": 0.5525527596473694, "Y": 0.18238869309425354}, {"X": 0.49279913306236267, "Y": 0.18238908052444458}]}, "Id": "761757e3-73a7-4033-ab68-e149747b64ee", "Relationships": [{"Type": "CHILD", "Ids": ["2d7724a1-c1a5-4252-bcfb-395336e359bc"]}]}, {"BlockType": "LINE", "Confidence": 99.95565795898438, "Text": "Ref. client", "Geometry": {"BoundingBox": {"Width": 0.07952859252691269, "Height": 0.011910787783563137, "Left": 0.07453349977731705, "Top": 0.1987835317850113}, "Polygon": [{"X": 0.07453350722789764, "Y": 0.19878405332565308}, {"X": 0.15406209230422974, "Y": 0.1987835317850113}, {"X": 0.15406206250190735, "Y": 0.21069379150867462}, {"X": 0.07453349977731705, "Y": 0.2106943130493164}]}, "Id": "2f475bdf-d1d6-4209-a8e7-e552f753b01b", "Relationships": [{"Type": "CHILD", "Ids": ["d9f940fa-c059-4b7a-9ef5-3f9adb54f07b", "536525c6-5375-482a-8d85-fa9e7a7b046e"]}]}, {"BlockType": "LINE", "Confidence": 99.70443725585938, "Text": "Import delivery date", "Geometry": {"BoundingBox": {"Width": 0.15841436386108398, "Height": 0.014439000748097897, "Left": 0.2864718735218048, "Top": 0.19800589978694916}, "Polygon": [{"X": 0.2864719331264496, "Y": 0.19800695776939392}, {"X": 0.4448862373828888, "Y": 0.19800589978694916}, {"X": 0.444886177778244, "Y": 0.21244385838508606}, {"X": 0.2864718735218048, "Y": 0.21244490146636963}]}, "Id": "15e28875-62cb-493c-8c08-e7979e8ad5c0", "Relationships": [{"Type": "CHILD", "Ids": ["5fd98ca7-958e-49b6-a33d-ba6a00f867ef", "0dde15ec-eedf-426e-8115-b8b4934802e0", "1f398382-b6f7-42e6-9f71-8eca852d50a7"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "SCAC (import)", "Geometry": {"BoundingBox": {"Width": 0.11198403686285019, "Height": 0.0144800441339612, "Left": 0.4960293471813202, "Top": 0.1972479522228241}, "Polygon": [{"X": 0.49602943658828735, "Y": 0.1972486972808838}, {"X": 0.608013391494751, "Y": 0.1972479522228241}, {"X": 0.6080132722854614, "Y": 0.21172724664211273}, {"X": 0.4960293471813202, "Y": 0.21172799170017242}]}, "Id": "1d2f53b2-8116-4af7-9925-7afeb5e8a84b", "Relationships": [{"Type": "CHILD", "Ids": ["1c49f601-d6e9-45a7-a46c-51d2dd5d032f", "5eeb0a52-3b01-4214-868d-761acce12bf2"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "SCAC (export)", "Geometry": {"BoundingBox": {"Width": 0.11077597737312317, "Height": 0.014417590573430061, "Left": 0.7047407627105713, "Top": 0.1967051923274994}, "Polygon": [{"X": 0.7047408819198608, "Y": 0.1967059224843979}, {"X": 0.8155167698860168, "Y": 0.1967051923274994}, {"X": 0.8155165910720825, "Y": 0.2111220508813858}, {"X": 0.7047407627105713, "Y": 0.2111227810382843}]}, "Id": "26c9d6c8-40a7-4772-8220-aae1177839e6", "Relationships": [{"Type": "CHILD", "Ids": ["ef4ddf7e-d949-4436-b0c5-405336c25fec", "d36572ef-a214-49c3-99c8-dc7e7dad29a9"]}]}, {"BlockType": "LINE", "Confidence": 84.50016021728516, "Text": "--", "Geometry": {"BoundingBox": {"Width": 0.015895182266831398, "Height": 0.0030037357937544584, "Left": 0.07422226667404175, "Top": 0.2214086651802063}, "Polygon": [{"X": 0.07422226667404175, "Y": 0.22140876948833466}, {"X": 0.090117447078228, "Y": 0.2214086651802063}, {"X": 0.090117447078228, "Y": 0.22441229224205017}, {"X": 0.07422226667404175, "Y": 0.22441239655017853}]}, "Id": "a93c70c5-498d-49b3-b7f8-8a904bafc43b", "Relationships": [{"Type": "CHILD", "Ids": ["eb9a344e-0e4e-448d-9f01-3ceeb721de06"]}]}, {"BlockType": "LINE", "Confidence": 84.66647338867188, "Text": "--", "Geometry": {"BoundingBox": {"Width": 0.015519197098910809, "Height": 0.0028748218901455402, "Left": 0.28593963384628296, "Top": 0.22069354355335236}, "Polygon": [{"X": 0.28593966364860535, "Y": 0.2206936478614807}, {"X": 0.30145883560180664, "Y": 0.22069354355335236}, {"X": 0.30145883560180664, "Y": 0.22356826066970825}, {"X": 0.28593963384628296, "Y": 0.2235683649778366}]}, "Id": "bd388590-a8d3-4bba-a56b-ad93ac310007", "Relationships": [{"Type": "CHILD", "Ids": ["31c56d88-1dd5-4c47-b2c2-7ce76ec1f15b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "SSRT", "Geometry": {"BoundingBox": {"Width": 0.047200072556734085, "Height": 0.01174737885594368, "Left": 0.4957720637321472, "Top": 0.214826300740242}, "Polygon": [{"X": 0.495772123336792, "Y": 0.21482661366462708}, {"X": 0.5429721474647522, "Y": 0.214826300740242}, {"X": 0.5429720282554626, "Y": 0.22657336294651031}, {"X": 0.4957720637321472, "Y": 0.22657367587089539}]}, "Id": "81de451c-1668-4a4e-8326-112fa74307f0", "Relationships": [{"Type": "CHILD", "Ids": ["89ad2434-6fdb-4339-98f6-a8cf620e3c80"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "SSRT", "Geometry": {"BoundingBox": {"Width": 0.04727667197585106, "Height": 0.011807230301201344, "Left": 0.7046666741371155, "Top": 0.21425168216228485}, "Polygon": [{"X": 0.704666793346405, "Y": 0.21425200998783112}, {"X": 0.7519433498382568, "Y": 0.21425168216228485}, {"X": 0.7519432306289673, "Y": 0.22605860233306885}, {"X": 0.7046666741371155, "Y": 0.22605891525745392}]}, "Id": "76af1dfa-c77e-4506-9b53-53ada03472d3", "Relationships": [{"Type": "CHILD", "Ids": ["32902bf4-58f3-4f3a-968b-35020fe55665"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "COMMENT", "Geometry": {"BoundingBox": {"Width": 0.08052881807088852, "Height": 0.010096578858792782, "Left": 0.07396573573350906, "Top": 0.23947414755821228}, "Polygon": [{"X": 0.07396574318408966, "Y": 0.23947468400001526}, {"X": 0.15449455380439758, "Y": 0.23947414755821228}, {"X": 0.1544945240020752, "Y": 0.24957019090652466}, {"X": 0.07396573573350906, "Y": 0.24957072734832764}]}, "Id": "c1e9de33-aabf-4228-8606-3bc0f9ee91aa", "Relationships": [{"Type": "CHILD", "Ids": ["042593a7-40d3-4d7b-8b6d-0c40dacf515d"]}]}, {"BlockType": "LINE", "Confidence": 99.96794128417969, "Text": "For North America Carriers, please ensure that if you have previously requested empty rail billing for this", "Geometry": {"BoundingBox": {"Width": 0.8297611474990845, "Height": 0.016142047941684723, "Left": 0.07479985803365707, "Top": 0.2513391971588135}, "Polygon": [{"X": 0.07479987293481827, "Y": 0.2513447701931}, {"X": 0.9045610427856445, "Y": 0.2513391971588135}, {"X": 0.9045608639717102, "Y": 0.2674756646156311}, {"X": 0.07479985803365707, "Y": 0.2674812376499176}]}, "Id": "63c0c905-4363-4b56-84f6-9ee30595db7d", "Relationships": [{"Type": "CHILD", "Ids": ["4412da1b-83b7-4e90-bafd-9c3a97d5e10b", "8faff19e-c933-42c8-9676-21dd67e7cf1f", "6c2ef378-02b6-4b59-85df-1c62566ac29c", "833112d6-a306-478a-a1f2-09a7dfc8accd", "1103074d-415b-4bcf-b0b4-9890974024d2", "dc8c76d5-f562-490a-bf80-79a774b96131", "36da340c-d2c6-4603-a8bd-a0d844612120", "7fe7501a-0af1-4a01-8856-19a22593adb0", "c30f519d-b5a2-4643-a84c-9f6f51004160", "ca44b966-1b17-4ac9-b457-824a4e010dee", "d8798224-1723-4ea6-889d-a1d8701aa1a0", "77a4677c-f485-4a19-bcfa-1adb2a213b5a", "f5bd9fb9-64fe-443a-addc-4891587f975b", "ea8ba6a2-9510-4e33-8df6-11b4d5d6a76f", "f8c40dc4-2dda-4d65-b72a-0ddfe1ac6b71", "6f3adc79-35d9-4713-998b-62161eab4428", "9454ca1b-a9bc-4a3b-9d7e-dd66cde7c27b"]}]}, {"BlockType": "LINE", "Confidence": 99.94463348388672, "Text": "approved container to cancel the request for empty rail billing. Please cancel empty rail billing by", "Geometry": {"BoundingBox": {"Width": 0.771007239818573, "Height": 0.01667114906013012, "Left": 0.07453656196594238, "Top": 0.2679276764392853}, "Polygon": [{"X": 0.07453657686710358, "Y": 0.26793286204338074}, {"X": 0.8455438017845154, "Y": 0.2679276764392853}, {"X": 0.845543622970581, "Y": 0.2845936119556427}, {"X": 0.07453656196594238, "Y": 0.28459882736206055}]}, "Id": "cb696188-dbc8-40e1-a8c1-361b11020392", "Relationships": [{"Type": "CHILD", "Ids": ["80813b0f-0674-468b-b057-38d2cfafe8d0", "d8b0c9ee-e611-4f95-b97b-e9857920d305", "f0fdf101-a8d2-4573-8950-3220f5d5ae94", "8f6177ba-1ac7-453b-824a-444ad45e03a0", "df9016a8-ceb0-404c-97af-8ca34adc104e", "4c547858-3f31-4d2e-9f66-124427f7dcc1", "eb076347-f333-46ab-a6e9-c50c332909e5", "a021d8be-1c43-4798-8907-23d1244c3d51", "273bb936-0d85-402b-b30f-7c1a48c4d967", "e6926a72-4c23-4329-9422-708a095bfb82", "776b8c07-f45c-4e0c-a62b-104c9f8cd731", "bd72262d-84e3-4894-be50-dfc6e426b144", "3fddfc56-89da-42a3-9e8f-d7e34c37aa7d", "43ee4d8f-5877-44f9-8c04-5d7e71531d0d", "df7a2199-4d2f-40c9-94a5-b008947c08d3", "ad9f5044-ad21-40da-84a1-1d25ff04bb39"]}]}, {"BlockType": "LINE", "Confidence": 96.59239959716797, "Text": "contacting <EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.3149142563343048, "Height": 0.014115175232291222, "Left": 0.07444506138563156, "Top": 0.2865573465824127}, "Polygon": [{"X": 0.07444506883621216, "Y": 0.28655949234962463}, {"X": 0.3893592953681946, "Y": 0.2865573465824127}, {"X": 0.3893592357635498, "Y": 0.3006703853607178}, {"X": 0.07444506138563156, "Y": 0.3006725311279297}]}, "Id": "80401883-acfc-46ed-b541-d0f44e34cf0d", "Relationships": [{"Type": "CHILD", "Ids": ["0b62d88c-f197-41db-8fff-e6d7f3c8716e", "9e68a8e5-bfb3-4086-b2f6-1b64d517e5e5"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Request history", "Geometry": {"BoundingBox": {"Width": 0.1259348839521408, "Height": 0.0143196489661932, "Left": 0.07499157637357712, "Top": 0.33813878893852234}, "Polygon": [{"X": 0.07499158382415771, "Y": 0.3381396532058716}, {"X": 0.20092645287513733, "Y": 0.33813878893852234}, {"X": 0.20092640817165375, "Y": 0.35245758295059204}, {"X": 0.07499157637357712, "Y": 0.3524584472179413}]}, "Id": "df812042-21d9-4858-99a9-4f821655b5b2", "Relationships": [{"Type": "CHILD", "Ids": ["b31933ad-7118-4f84-a35c-a3398cdbb8a0", "eee2e67f-733b-4bc7-82f6-3e73562bcd28"]}]}, {"BlockType": "LINE", "Confidence": 99.71353912353516, "Text": "09/12/2025 09:38 AM", "Geometry": {"BoundingBox": {"Width": 0.1765160858631134, "Height": 0.012029491364955902, "Left": 0.07459906488656998, "Top": 0.36096814274787903}, "Polygon": [{"X": 0.07459907233715057, "Y": 0.3609693646430969}, {"X": 0.2511151432991028, "Y": 0.36096814274787903}, {"X": 0.2511151134967804, "Y": 0.37299641966819763}, {"X": 0.07459906488656998, "Y": 0.3729976415634155}]}, "Id": "ec72fe95-ca78-4393-8fd1-b5dc53ab4846", "Relationships": [{"Type": "CHILD", "Ids": ["9c9bc9df-a141-484a-b8d8-2b72d35ee0db", "2b52c116-bdc5-4682-b507-7a9c5cadc8bd", "9eed9ebc-3647-49a1-a820-6c5a5fd93402"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Approved", "Geometry": {"BoundingBox": {"Width": 0.07584540545940399, "Height": 0.01422877050936222, "Left": 0.26539307832717896, "Top": 0.3601917028427124}, "Polygon": [{"X": 0.26539310812950134, "Y": 0.3601922392845154}, {"X": 0.34123846888542175, "Y": 0.3601917028427124}, {"X": 0.341238409280777, "Y": 0.3744199573993683}, {"X": 0.26539307832717896, "Y": 0.37442049384117126}]}, "Id": "7a7fc471-3168-449d-9fa3-ac1b8ad2eb4d", "Relationships": [{"Type": "CHILD", "Ids": ["0d23765e-4842-4833-a4e1-d14593a6a154"]}]}, {"BlockType": "LINE", "Confidence": 99.7067642211914, "Text": "09/12/2025 09:38 AM", "Geometry": {"BoundingBox": {"Width": 0.1764291673898697, "Height": 0.011914094910025597, "Left": 0.07463734596967697, "Top": 0.38119998574256897}, "Polygon": [{"X": 0.07463735342025757, "Y": 0.3812011778354645}, {"X": 0.25106650590896606, "Y": 0.38119998574256897}, {"X": 0.2510664761066437, "Y": 0.39311283826828003}, {"X": 0.07463734596967697, "Y": 0.3931140601634979}]}, "Id": "b048302f-f4bb-430d-8e77-ec5c4df2215a", "Relationships": [{"Type": "CHILD", "Ids": ["b6ba6d70-e305-4495-8a28-771107af590a", "ce4625af-17f8-4acf-abd8-bfc9f7f70ef3", "ee03d300-9e33-401f-88c7-1a7a03e0ec68"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON> submitted", "Geometry": {"BoundingBox": {"Width": 0.15223908424377441, "Height": 0.011713334359228611, "Left": 0.26584938168525696, "Top": 0.3801659345626831}, "Polygon": [{"X": 0.26584941148757935, "Y": 0.38016700744628906}, {"X": 0.418088436126709, "Y": 0.3801659345626831}, {"X": 0.4180883765220642, "Y": 0.391878217458725}, {"X": 0.26584938168525696, "Y": 0.39187929034233093}]}, "Id": "14ac2820-bf97-4a90-98e0-5f3abf417941", "Relationships": [{"Type": "CHILD", "Ids": ["774311a4-464b-4065-bde3-cde9d38a1388", "93d76b38-eab2-413e-88e2-18662cb548f4"]}]}, {"BlockType": "LINE", "Confidence": 99.814453125, "Text": "09/12/2025 09:06 AM", "Geometry": {"BoundingBox": {"Width": 0.17654231190681458, "Height": 0.011683995835483074, "Left": 0.07473282516002655, "Top": 0.40127265453338623}, "Polygon": [{"X": 0.07473283261060715, "Y": 0.4012739062309265}, {"X": 0.2512751519680023, "Y": 0.40127265453338623}, {"X": 0.25127509236335754, "Y": 0.4129554331302643}, {"X": 0.07473282516002655, "Y": 0.4129566550254822}]}, "Id": "61cc0972-b296-42e7-b8f8-9061f1ea1de6", "Relationships": [{"Type": "CHILD", "Ids": ["2a1b8f42-18e3-47de-a3c4-6b471575a076", "d617ddfd-c012-40f7-a376-b2ae84374f21", "83fbbdf1-ea50-4058-9abb-b11dd50daa62"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Declined", "Geometry": {"BoundingBox": {"Width": 0.068719781935215, "Height": 0.011370878666639328, "Left": 0.266343355178833, "Top": 0.40049782395362854}, "Polygon": [{"X": 0.2663433849811554, "Y": 0.40049830079078674}, {"X": 0.3350631296634674, "Y": 0.40049782395362854}, {"X": 0.335063099861145, "Y": 0.41186821460723877}, {"X": 0.266343355178833, "Y": 0.411868691444397}]}, "Id": "cdd9b896-2de9-4ad7-81ca-ac00ec2bc998", "Relationships": [{"Type": "CHILD", "Ids": ["c8d2bfdd-1489-426b-889e-757aaf4428b7"]}]}, {"BlockType": "LINE", "Confidence": 99.826171875, "Text": "09/12/2025 09:06 AM Overrule submitted", "Geometry": {"BoundingBox": {"Width": 0.34327903389930725, "Height": 0.012751282192766666, "Left": 0.074763223528862, "Top": 0.420322060585022}, "Polygon": [{"X": 0.0747632309794426, "Y": 0.4203244745731354}, {"X": 0.41804224252700806, "Y": 0.420322060585022}, {"X": 0.4180421829223633, "Y": 0.43307095766067505}, {"X": 0.074763223528862, "Y": 0.43307334184646606}]}, "Id": "e851896c-e475-447e-b850-d08b0b8d0b91", "Relationships": [{"Type": "CHILD", "Ids": ["3107b4cd-e6ad-4c94-8abc-7dc00c5888d1", "1037849f-0dae-4be1-a80a-eb070bf5abf6", "1ff02c67-9729-4be0-98c5-db842cca8015", "272cc902-6cae-4aa4-ad7a-20a207252c30", "24e6da0f-490f-4b3b-b5b3-73cb87915db9"]}]}, {"BlockType": "LINE", "Confidence": 99.63216400146484, "Text": "09/12/2025 07:45 AM", "Geometry": {"BoundingBox": {"Width": 0.1765129715204239, "Height": 0.011899442411959171, "Left": 0.07485289871692657, "Top": 0.44131848216056824}, "Polygon": [{"X": 0.07485291361808777, "Y": 0.44131970405578613}, {"X": 0.25136587023735046, "Y": 0.44131848216056824}, {"X": 0.2513658404350281, "Y": 0.45321667194366455}, {"X": 0.07485289871692657, "Y": 0.45321792364120483}]}, "Id": "5a0f586f-ac74-4235-aac8-242a594e8e4c", "Relationships": [{"Type": "CHILD", "Ids": ["c59d85c8-69ef-4137-8f4d-6c78e4fceded", "0cf5d9b9-6f42-4e99-a562-1a1a67b27d05", "a553663a-724e-4b0b-8867-29479776e5d7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Declined", "Geometry": {"BoundingBox": {"Width": 0.06848611682653427, "Height": 0.011225027963519096, "Left": 0.26654282212257385, "Top": 0.4406757056713104}, "Polygon": [{"X": 0.26654285192489624, "Y": 0.44067618250846863}, {"X": 0.3350289463996887, "Y": 0.4406757056713104}, {"X": 0.33502888679504395, "Y": 0.4519002437591553}, {"X": 0.26654282212257385, "Y": 0.4519007205963135}]}, "Id": "09574352-023b-4c0e-8b74-226c73f68f49", "Relationships": [{"Type": "CHILD", "Ids": ["1252df5b-21e2-41dc-9dc7-419391fbf622"]}]}, {"BlockType": "LINE", "Confidence": 99.67448425292969, "Text": "09/12/2025 07:45 AM", "Geometry": {"BoundingBox": {"Width": 0.1762716919183731, "Height": 0.012031280435621738, "Left": 0.07488468289375305, "Top": 0.46124061942100525}, "Polygon": [{"X": 0.07488469034433365, "Y": 0.46124187111854553}, {"X": 0.25115635991096497, "Y": 0.46124061942100525}, {"X": 0.2511563301086426, "Y": 0.4732706546783447}, {"X": 0.07488468289375305, "Y": 0.473271906375885}]}, "Id": "6208eb4d-22b3-4cf7-9cd0-7f5b06fb6585", "Relationships": [{"Type": "CHILD", "Ids": ["6c44179c-a468-40f3-b85a-621381c63e86", "1fc45e81-8306-487e-9ba7-f87ec2a7b035", "8ea3ab63-2136-4fe5-b381-23eacc822514"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Pending", "Geometry": {"BoundingBox": {"Width": 0.0647222101688385, "Height": 0.013983549550175667, "Left": 0.26665782928466797, "Top": 0.4605606496334076}, "Polygon": [{"X": 0.26665788888931274, "Y": 0.4605611264705658}, {"X": 0.33138006925582886, "Y": 0.4605606496334076}, {"X": 0.3313800096511841, "Y": 0.4745437502861023}, {"X": 0.26665782928466797, "Y": 0.4745441973209381}]}, "Id": "4107c5d9-6701-4fe3-a70f-b3c7188125a5", "Relationships": [{"Type": "CHILD", "Ids": ["e9945ad5-ba7f-4a08-ad2e-fe1a40c9a3c1"]}]}, {"BlockType": "LINE", "Confidence": 99.43807983398438, "Text": "09/12/2025 07:45 AM <NAME_EMAIL> at Super Star Freight", "Geometry": {"BoundingBox": {"Width": 0.7048412561416626, "Height": 0.015513633377850056, "Left": 0.07489025592803955, "Top": 0.479099303483963}, "Polygon": [{"X": 0.07489027082920074, "Y": 0.47910428047180176}, {"X": 0.7797315716743469, "Y": 0.479099303483963}, {"X": 0.7797313928604126, "Y": 0.49460792541503906}, {"X": 0.07489025592803955, "Y": 0.4946129322052002}]}, "Id": "7576ed3d-313d-4235-8a88-a7da19d5997c", "Relationships": [{"Type": "CHILD", "Ids": ["b47265cb-8662-40d1-92ce-b2193f6177c3", "ff88581f-8461-4359-aebb-8b40be0db098", "4cd7fd95-6e07-4441-ad85-c4b434df6cb2", "abdd5811-c899-40db-8854-df65fbbee404", "62e8c434-ab22-41c7-bf4c-0b8f2f2ac10d", "d030a0de-dcdd-4ea2-bc5f-fe00b3292346", "ed1b4d55-7a5b-4a39-8760-99f4253dc14a", "fb039530-acee-4649-b1be-9fb8129552a0", "4610b0b4-fd93-4c87-8ae8-c7207ce2c670", "2d5c831b-f6bc-4a44-910b-bae6f0c5ba31"]}]}, {"BlockType": "LINE", "Confidence": 99.67587280273438, "Text": "Generated by Avantida 09/12/2025 10:25 AM", "Geometry": {"BoundingBox": {"Width": 0.2591727077960968, "Height": 0.010636492632329464, "Left": 0.6526296138763428, "Top": 0.5259606242179871}, "Polygon": [{"X": 0.6526296734809875, "Y": 0.5259624719619751}, {"X": 0.9118022918701172, "Y": 0.5259606242179871}, {"X": 0.9118021726608276, "Y": 0.5365952253341675}, {"X": 0.6526296138763428, "Y": 0.5365970730781555}]}, "Id": "d32e462e-2f06-43c2-b5bb-ca6136c048ec", "Relationships": [{"Type": "CHILD", "Ids": ["940711aa-4165-4130-bfb5-177246751049", "a1cd1133-ce50-4f56-8542-8cf7bb276edd", "0f62561e-3ed6-4aa1-83a9-507e960893ce", "04a63c48-f7ed-47d1-b4b7-f992bc38357c", "65c7b995-3762-46b7-8b3a-35e253052c1a", "cfad9bcd-99d1-4754-82f5-fffef7385c78"]}]}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11620873957872391, "Height": 0.013804974965751171, "Left": 0.16856591403484344, "Top": 0.05330808088183403}, "Polygon": [{"X": 0.16856594383716583, "Y": 0.053308822214603424}, {"X": 0.28477466106414795, "Y": 0.05330808088183403}, {"X": 0.2847746014595032, "Y": 0.06711231172084808}, {"X": 0.16856591403484344, "Y": 0.06711305677890778}]}, "Id": "53c490ef-cda1-4ad6-bdbe-b231a1547851"}, {"BlockType": "WORD", "Confidence": 99.55655670166016, "Text": "MAERSK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04685927554965019, "Height": 0.0064019979909062386, "Left": 0.098813995718956, "Top": 0.06787344813346863}, "Polygon": [{"X": 0.09881400316953659, "Y": 0.0678737461566925}, {"X": 0.14567327499389648, "Y": 0.06787344813346863}, {"X": 0.1456732600927353, "Y": 0.07427514344453812}, {"X": 0.098813995718956, "Y": 0.07427544891834259}]}, "Id": "94dbae64-c89d-4fd5-9eeb-f57bb79ed4f2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Reuse", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06230967491865158, "Height": 0.013579947873950005, "Left": 0.1926911324262619, "Top": 0.07429537177085876}, "Polygon": [{"X": 0.1926911622285843, "Y": 0.074295774102211}, {"X": 0.2550007998943329, "Y": 0.07429537177085876}, {"X": 0.2550007700920105, "Y": 0.08787491917610168}, {"X": 0.1926911324262619, "Y": 0.08787532150745392}]}, "Id": "689f4147-c1e1-4c3c-bb2a-e3b2278d58c2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06297562271356583, "Height": 0.016108116135001183, "Left": 0.26209867000579834, "Top": 0.07477323710918427}, "Polygon": [{"X": 0.2620987296104431, "Y": 0.0747736394405365}, {"X": 0.32507428526878357, "Y": 0.07477323710918427}, {"X": 0.3250742256641388, "Y": 0.09088095277547836}, {"X": 0.26209867000579834, "Y": 0.0908813551068306}]}, "Id": "8a89310e-8df1-4f8b-8735-57218013ae96"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09520914405584335, "Height": 0.013985972851514816, "Left": 0.3317614197731018, "Top": 0.07330680638551712}, "Polygon": [{"X": 0.3317614793777466, "Y": 0.07330741733312607}, {"X": 0.42697057127952576, "Y": 0.07330680638551712}, {"X": 0.426970511674881, "Y": 0.08729217201471329}, {"X": 0.3317614197731018, "Y": 0.08729278296232224}]}, "Id": "92b4df47-b924-4ada-8a87-6bafeb4b88b7"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07831986248493195, "Height": 0.011350560002028942, "Left": 0.07359862327575684, "Top": 0.11062714457511902}, "Polygon": [{"X": 0.07359863072633743, "Y": 0.11062765121459961}, {"X": 0.15191848576068878, "Y": 0.11062714457511902}, {"X": 0.1519184708595276, "Y": 0.12197720259428024}, {"X": 0.07359862327575684, "Y": 0.12197770923376083}]}, "Id": "bb384df7-1441-4f40-b6dc-3c185465ce94"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061896733939647675, "Height": 0.011465288698673248, "Left": 0.15708932280540466, "Top": 0.11026330292224884}, "Polygon": [{"X": 0.15708935260772705, "Y": 0.11026370525360107}, {"X": 0.21898606419563293, "Y": 0.11026330292224884}, {"X": 0.21898603439331055, "Y": 0.12172819674015045}, {"X": 0.15708932280540466, "Y": 0.12172859162092209}]}, "Id": "b08b18df-335b-43d6-9a68-d38333561338"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Export", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050485435873270035, "Height": 0.013342974707484245, "Left": 0.35321247577667236, "Top": 0.11017677187919617}, "Polygon": [{"X": 0.35321253538131714, "Y": 0.11017709970474243}, {"X": 0.4036979079246521, "Y": 0.11017677187919617}, {"X": 0.4036978483200073, "Y": 0.1235194206237793}, {"X": 0.35321247577667236, "Y": 0.12351974844932556}]}, "Id": "ec517fce-e00d-488b-b7d6-5d9b5f416f88"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06381046026945114, "Height": 0.01427098736166954, "Left": 0.4089704751968384, "Top": 0.1092812716960907}, "Polygon": [{"X": 0.40897053480148315, "Y": 0.10928168147802353}, {"X": 0.4727809429168701, "Y": 0.1092812716960907}, {"X": 0.47278085350990295, "Y": 0.12355184555053711}, {"X": 0.4089704751968384, "Y": 0.12355225533246994}]}, "Id": "5bbae16d-f801-449c-acfc-4d1efcb6064e"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "ref.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024638421833515167, "Height": 0.01155487634241581, "Left": 0.47806093096733093, "Top": 0.10919293761253357}, "Polygon": [{"X": 0.4780609905719757, "Y": 0.1091931015253067}, {"X": 0.5026993751525879, "Y": 0.10919293761253357}, {"X": 0.5026992559432983, "Y": 0.1207476556301117}, {"X": 0.47806093096733093, "Y": 0.12074781954288483}]}, "Id": "d65db233-ac0c-4bbe-89fd-53e86267ce57"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Reuse", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048165369778871536, "Height": 0.010610359720885754, "Left": 0.6298614740371704, "Top": 0.10948268324136734}, "Polygon": [{"X": 0.6298615336418152, "Y": 0.10948299616575241}, {"X": 0.6780268549919128, "Y": 0.10948268324136734}, {"X": 0.6780267357826233, "Y": 0.1200927272439003}, {"X": 0.6298614740371704, "Y": 0.12009304016828537}]}, "Id": "b039b168-9fc7-4123-a206-a0d3344f7927"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034364648163318634, "Height": 0.01140773855149746, "Left": 0.6831220984458923, "Top": 0.10862217098474503}, "Polygon": [{"X": 0.6831221580505371, "Y": 0.10862239450216293}, {"X": 0.7174867391586304, "Y": 0.10862217098474503}, {"X": 0.7174866199493408, "Y": 0.12002968788146973}, {"X": 0.6831220984458923, "Y": 0.12002991139888763}]}, "Id": "610f6130-55a8-4a99-b1d9-77f758322c4b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "CAAU8485442", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.130105659365654, "Height": 0.011947781778872013, "Left": 0.0735413134098053, "Top": 0.12749287486076355}, "Polygon": [{"X": 0.0735413208603859, "Y": 0.1274937242269516}, {"X": 0.2036469727754593, "Y": 0.12749287486076355}, {"X": 0.2036469429731369, "Y": 0.13943980634212494}, {"X": 0.0735413134098053, "Y": 0.139440655708313}]}, "Id": "eec15355-49f0-434f-a8e9-556518fdf026"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "257637187", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1009463295340538, "Height": 0.011916875839233398, "Left": 0.35220593214035034, "Top": 0.12656144797801971}, "Polygon": [{"X": 0.3522059917449951, "Y": 0.12656210362911224}, {"X": 0.45315226912498474, "Y": 0.12656144797801971}, {"X": 0.45315220952033997, "Y": 0.13847766816616058}, {"X": 0.35220593214035034, "Y": 0.1384783238172531}]}, "Id": "86f2ee28-9545-402a-b445-e963c436d920"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10431566089391708, "Height": 0.012517169117927551, "Left": 0.6292881965637207, "Top": 0.1257018744945526}, "Polygon": [{"X": 0.6292883157730103, "Y": 0.12570254504680634}, {"X": 0.7336038947105408, "Y": 0.1257018744945526}, {"X": 0.7336037755012512, "Y": 0.13821835815906525}, {"X": 0.6292881965637207, "Y": 0.13821904361248016}]}, "Id": "49651fe3-cfe2-4422-a85b-4227698c5c67"}, {"BlockType": "WORD", "Confidence": 99.45252227783203, "Text": "Import", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052865058183670044, "Height": 0.013288380578160286, "Left": 0.0744355246424675, "Top": 0.1555662453174591}, "Polygon": [{"X": 0.07443553954362869, "Y": 0.15556658804416656}, {"X": 0.12730059027671814, "Y": 0.1555662453174591}, {"X": 0.12730056047439575, "Y": 0.16885428130626678}, {"X": 0.0744355246424675, "Y": 0.16885462403297424}]}, "Id": "9213f9cc-9320-46ac-abc4-bfb06feed653"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "location", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06424028426408768, "Height": 0.011445087380707264, "Left": 0.13274338841438293, "Top": 0.1545090526342392}, "Polygon": [{"X": 0.13274340331554413, "Y": 0.15450946986675262}, {"X": 0.1969836801290512, "Y": 0.1545090526342392}, {"X": 0.19698365032672882, "Y": 0.16595372557640076}, {"X": 0.13274338841438293, "Y": 0.16595414280891418}]}, "Id": "885669ee-0831-421e-b4e8-a99f2ca400de"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Export", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050811003893613815, "Height": 0.013521028682589531, "Left": 0.28300461173057556, "Top": 0.15468212962150574}, "Polygon": [{"X": 0.28300467133522034, "Y": 0.154682457447052}, {"X": 0.3338156044483185, "Y": 0.15468212962150574}, {"X": 0.3338155448436737, "Y": 0.16820281744003296}, {"X": 0.28300461173057556, "Y": 0.16820316016674042}]}, "Id": "8871a008-e18e-40d5-bf91-01da475fc4b8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "location", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0636858120560646, "Height": 0.011382038705050945, "Left": 0.33901435136795044, "Top": 0.15384842455387115}, "Polygon": [{"X": 0.3390144109725952, "Y": 0.15384884178638458}, {"X": 0.40270018577575684, "Y": 0.15384842455387115}, {"X": 0.40270012617111206, "Y": 0.16523005068302155}, {"X": 0.33901435136795044, "Y": 0.16523046791553497}]}, "Id": "d50edd53-a1ed-43e4-a181-8a28cd34d557"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03896303474903107, "Height": 0.01102838758379221, "Left": 0.4933297336101532, "Top": 0.15372797846794128}, "Polygon": [{"X": 0.493329793214798, "Y": 0.15372823178768158}, {"X": 0.5322927832603455, "Y": 0.15372797846794128}, {"X": 0.5322927236557007, "Y": 0.16475610435009003}, {"X": 0.4933297336101532, "Y": 0.16475635766983032}]}, "Id": "9bb3c7db-075e-4d9e-8f43-132f1a4886cf"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Illinois", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058630332350730896, "Height": 0.012309109792113304, "Left": 0.07405391335487366, "Top": 0.17137058079242706}, "Polygon": [{"X": 0.07405392080545425, "Y": 0.1713709533214569}, {"X": 0.13268424570560455, "Y": 0.17137058079242706}, {"X": 0.13268423080444336, "Y": 0.18367929756641388}, {"X": 0.07405391335487366, "Y": 0.18367968499660492}]}, "Id": "d1b4dcd9-3771-4b2e-9afe-ff8d6bff8767"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Illinois", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05793018266558647, "Height": 0.012232729233801365, "Left": 0.28273314237594604, "Top": 0.17069926857948303}, "Polygon": [{"X": 0.2827332019805908, "Y": 0.17069964110851288}, {"X": 0.340663343667984, "Y": 0.17069926857948303}, {"X": 0.34066328406333923, "Y": 0.18293161690235138}, {"X": 0.28273314237594604, "Y": 0.18293198943138123}]}, "Id": "cd7345ae-5048-4942-ba49-5f9815828fe7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "USD30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05975371226668358, "Height": 0.011833122931420803, "Left": 0.49279913306236267, "Top": 0.1705559641122818}, "Polygon": [{"X": 0.49279922246932983, "Y": 0.17055635154247284}, {"X": 0.5525528788566589, "Y": 0.1705559641122818}, {"X": 0.5525527596473694, "Y": 0.18238869309425354}, {"X": 0.49279913306236267, "Y": 0.18238908052444458}]}, "Id": "2d7724a1-c1a5-4252-bcfb-395336e359bc"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Ref.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029820555821061134, "Height": 0.01170838251709938, "Left": 0.07453349977731705, "Top": 0.19898593425750732}, "Polygon": [{"X": 0.07453350722789764, "Y": 0.19898612797260284}, {"X": 0.10435405373573303, "Y": 0.19898593425750732}, {"X": 0.10435404628515244, "Y": 0.2106941193342209}, {"X": 0.07453349977731705, "Y": 0.2106943130493164}]}, "Id": "d9f940fa-c059-4b7a-9ef5-3f9adb54f07b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "client", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04352858290076256, "Height": 0.011536029167473316, "Left": 0.11053350567817688, "Top": 0.1987835317850113}, "Polygon": [{"X": 0.11053352057933807, "Y": 0.19878381490707397}, {"X": 0.15406209230422974, "Y": 0.1987835317850113}, {"X": 0.15406206250190735, "Y": 0.21031926572322845}, {"X": 0.11053350567817688, "Y": 0.21031956374645233}]}, "Id": "536525c6-5375-482a-8d85-fa9e7a7b046e"}, {"BlockType": "WORD", "Confidence": 99.1531753540039, "Text": "Import", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05236244946718216, "Height": 0.01348335575312376, "Left": 0.2864718735218048, "Top": 0.1989615559577942}, "Polygon": [{"X": 0.2864719331264496, "Y": 0.19896189868450165}, {"X": 0.33883434534072876, "Y": 0.1989615559577942}, {"X": 0.338834285736084, "Y": 0.21244455873966217}, {"X": 0.2864718735218048, "Y": 0.21244490146636963}]}, "Id": "5fd98ca7-958e-49b6-a33d-ba6a00f867ef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "delivery", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06135628744959831, "Height": 0.014184626750648022, "Left": 0.34401315450668335, "Top": 0.1980210393667221}, "Polygon": [{"X": 0.3440132141113281, "Y": 0.19802144169807434}, {"X": 0.40536943078041077, "Y": 0.1980210393667221}, {"X": 0.405369371175766, "Y": 0.21220526099205017}, {"X": 0.34401315450668335, "Y": 0.2122056633234024}]}, "Id": "0dde15ec-eedf-426e-8115-b8b4934802e0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034498050808906555, "Height": 0.011368892155587673, "Left": 0.41038820147514343, "Top": 0.19800589978694916}, "Polygon": [{"X": 0.4103882610797882, "Y": 0.19800613820552826}, {"X": 0.4448862373828888, "Y": 0.19800589978694916}, {"X": 0.444886177778244, "Y": 0.2093745619058609}, {"X": 0.41038820147514343, "Y": 0.20937480032444}]}, "Id": "1f398382-b6f7-42e6-9f71-8eca852d50a7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SCAC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04342207685112953, "Height": 0.011052331887185574, "Left": 0.4960293769836426, "Top": 0.19828499853610992}, "Polygon": [{"X": 0.49602943658828735, "Y": 0.1982852816581726}, {"X": 0.5394514203071594, "Y": 0.19828499853610992}, {"X": 0.5394513607025146, "Y": 0.2093370407819748}, {"X": 0.4960293769836426, "Y": 0.20933732390403748}]}, "Id": "1c49f601-d6e9-45a7-a46c-51d2dd5d032f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "(import)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06284711509943008, "Height": 0.014479718171060085, "Left": 0.5451662540435791, "Top": 0.1972479522228241}, "Polygon": [{"X": 0.5451663732528687, "Y": 0.19724836945533752}, {"X": 0.608013391494751, "Y": 0.1972479522228241}, {"X": 0.6080132722854614, "Y": 0.21172724664211273}, {"X": 0.5451662540435791, "Y": 0.21172766387462616}]}, "Id": "5eeb0a52-3b01-4214-868d-761acce12bf2"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "SCAC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04370899498462677, "Height": 0.010786978527903557, "Left": 0.7047407627105713, "Top": 0.19780300557613373}, "Polygon": [{"X": 0.7047408819198608, "Y": 0.1978032886981964}, {"X": 0.7484498023986816, "Y": 0.19780300557613373}, {"X": 0.7484496831893921, "Y": 0.20858968794345856}, {"X": 0.7047407627105713, "Y": 0.20858998596668243}]}, "Id": "ef4ddf7e-d949-4436-b0c5-405336c25fec"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "(export)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061377666890621185, "Height": 0.014417262747883797, "Left": 0.7541390657424927, "Top": 0.1967051923274994}, "Polygon": [{"X": 0.7541391849517822, "Y": 0.19670559465885162}, {"X": 0.8155167698860168, "Y": 0.1967051923274994}, {"X": 0.8155165910720825, "Y": 0.2111220508813858}, {"X": 0.7541390657424927, "Y": 0.21112245321273804}]}, "Id": "d36572ef-a214-49c3-99c8-dc7e7dad29a9"}, {"BlockType": "WORD", "Confidence": 84.50016021728516, "Text": "--", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015895182266831398, "Height": 0.0030037357937544584, "Left": 0.07422226667404175, "Top": 0.2214086651802063}, "Polygon": [{"X": 0.07422226667404175, "Y": 0.22140876948833466}, {"X": 0.090117447078228, "Y": 0.2214086651802063}, {"X": 0.090117447078228, "Y": 0.22441229224205017}, {"X": 0.07422226667404175, "Y": 0.22441239655017853}]}, "Id": "eb9a344e-0e4e-448d-9f01-3ceeb721de06"}, {"BlockType": "WORD", "Confidence": 84.66647338867188, "Text": "--", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015519197098910809, "Height": 0.0028748218901455402, "Left": 0.28593963384628296, "Top": 0.22069354355335236}, "Polygon": [{"X": 0.28593966364860535, "Y": 0.2206936478614807}, {"X": 0.30145883560180664, "Y": 0.22069354355335236}, {"X": 0.30145883560180664, "Y": 0.22356826066970825}, {"X": 0.28593963384628296, "Y": 0.2235683649778366}]}, "Id": "31c56d88-1dd5-4c47-b2c2-7ce76ec1f15b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047200072556734085, "Height": 0.01174737885594368, "Left": 0.4957720637321472, "Top": 0.214826300740242}, "Polygon": [{"X": 0.495772123336792, "Y": 0.21482661366462708}, {"X": 0.5429721474647522, "Y": 0.214826300740242}, {"X": 0.5429720282554626, "Y": 0.22657336294651031}, {"X": 0.4957720637321472, "Y": 0.22657367587089539}]}, "Id": "89ad2434-6fdb-4339-98f6-a8cf620e3c80"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04727667197585106, "Height": 0.011807230301201344, "Left": 0.7046666741371155, "Top": 0.21425168216228485}, "Polygon": [{"X": 0.704666793346405, "Y": 0.21425200998783112}, {"X": 0.7519433498382568, "Y": 0.21425168216228485}, {"X": 0.7519432306289673, "Y": 0.22605860233306885}, {"X": 0.7046666741371155, "Y": 0.22605891525745392}]}, "Id": "32902bf4-58f3-4f3a-968b-35020fe55665"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "COMMENT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08052881807088852, "Height": 0.010096578858792782, "Left": 0.07396573573350906, "Top": 0.23947414755821228}, "Polygon": [{"X": 0.07396574318408966, "Y": 0.23947468400001526}, {"X": 0.15449455380439758, "Y": 0.23947414755821228}, {"X": 0.1544945240020752, "Y": 0.24957019090652466}, {"X": 0.07396573573350906, "Y": 0.24957072734832764}]}, "Id": "042593a7-40d3-4d7b-8b6d-0c40dacf515d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025355687364935875, "Height": 0.010764977894723415, "Left": 0.07479985803365707, "Top": 0.25491252541542053}, "Polygon": [{"X": 0.07479986548423767, "Y": 0.25491270422935486}, {"X": 0.1001555472612381, "Y": 0.25491252541542053}, {"X": 0.1001555323600769, "Y": 0.2656773328781128}, {"X": 0.07479985803365707, "Y": 0.2656775116920471}]}, "Id": "4412da1b-83b7-4e90-bafd-9c3a97d5e10b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "North", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04419611394405365, "Height": 0.010973875410854816, "Left": 0.10569699108600616, "Top": 0.2543797492980957}, "Polygon": [{"X": 0.10569699853658676, "Y": 0.2543800473213196}, {"X": 0.14989310503005981, "Y": 0.2543797492980957}, {"X": 0.14989307522773743, "Y": 0.26535332202911377}, {"X": 0.10569699108600616, "Y": 0.26535362005233765}]}, "Id": "8faff19e-c933-42c8-9676-21dd67e7cf1f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "America", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06603163480758667, "Height": 0.011307022534310818, "Left": 0.15528935194015503, "Top": 0.25403547286987305}, "Polygon": [{"X": 0.15528936684131622, "Y": 0.25403591990470886}, {"X": 0.2213209867477417, "Y": 0.25403547286987305}, {"X": 0.2213209569454193, "Y": 0.2653420567512512}, {"X": 0.15528935194015503, "Y": 0.26534250378608704}]}, "Id": "6c2ef378-02b6-4b59-85df-1c62566ac29c"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Carriers,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06613481789827347, "Height": 0.012771437875926495, "Left": 0.22711321711540222, "Top": 0.25391846895217896}, "Polygon": [{"X": 0.2271132469177246, "Y": 0.25391891598701477}, {"X": 0.2932480275630951, "Y": 0.25391846895217896}, {"X": 0.2932479679584503, "Y": 0.2666894495487213}, {"X": 0.22711321711540222, "Y": 0.26668989658355713}]}, "Id": "833112d6-a306-478a-a1f2-09a7dfc8accd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05172120779752731, "Height": 0.013842430897057056, "Left": 0.2990264594554901, "Top": 0.25363731384277344}, "Polygon": [{"X": 0.2990265190601349, "Y": 0.2536376416683197}, {"X": 0.350747674703598, "Y": 0.25363731384277344}, {"X": 0.35074761509895325, "Y": 0.26747938990592957}, {"X": 0.2990264594554901, "Y": 0.2674797475337982}]}, "Id": "1103074d-415b-4bcf-b0b4-9890974024d2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ensure", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053405001759529114, "Height": 0.008523640222847462, "Left": 0.35619106888771057, "Top": 0.2561744749546051}, "Polygon": [{"X": 0.35619112849235535, "Y": 0.25617483258247375}, {"X": 0.4095960855484009, "Y": 0.2561744749546051}, {"X": 0.4095960259437561, "Y": 0.26469776034355164}, {"X": 0.35619106888771057, "Y": 0.2646981179714203}]}, "Id": "dc8c76d5-f562-490a-bf80-79a774b96131"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03174372762441635, "Height": 0.011288999579846859, "Left": 0.41443005204200745, "Top": 0.253162145614624}, "Polygon": [{"X": 0.4144301116466522, "Y": 0.25316235423088074}, {"X": 0.4461737871170044, "Y": 0.253162145614624}, {"X": 0.4461737275123596, "Y": 0.26445093750953674}, {"X": 0.41443005204200745, "Y": 0.26445114612579346}]}, "Id": "36da340c-d2c6-4603-a8bd-a0d844612120"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "if", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01050669327378273, "Height": 0.011623119935393333, "Left": 0.4513629972934723, "Top": 0.25273898243904114}, "Polygon": [{"X": 0.45136305689811707, "Y": 0.2527390420436859}, {"X": 0.4618696868419647, "Y": 0.25273898243904114}, {"X": 0.46186962723731995, "Y": 0.26436200737953186}, {"X": 0.4513629972934723, "Y": 0.264362096786499}]}, "Id": "7fe7501a-0af1-4a01-8856-19a22593adb0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "you", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02869418077170849, "Height": 0.011144762858748436, "Left": 0.46557116508483887, "Top": 0.25595682859420776}, "Polygon": [{"X": 0.46557122468948364, "Y": 0.2559570074081421}, {"X": 0.4942653477191925, "Y": 0.25595682859420776}, {"X": 0.49426528811454773, "Y": 0.2671014070510864}, {"X": 0.46557116508483887, "Y": 0.26710158586502075}]}, "Id": "c30f519d-b5a2-4643-a84c-9f6f51004160"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "have", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036776505410671234, "Height": 0.011264284141361713, "Left": 0.5004201531410217, "Top": 0.25286978483200073}, "Polygon": [{"X": 0.5004202723503113, "Y": 0.25287002325057983}, {"X": 0.537196695804596, "Y": 0.25286978483200073}, {"X": 0.5371966361999512, "Y": 0.2641338109970093}, {"X": 0.5004201531410217, "Y": 0.2641340494155884}]}, "Id": "ca44b966-1b17-4ac9-b457-824a4e010dee"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "previously", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08125219494104385, "Height": 0.014013316482305527, "Left": 0.5425330400466919, "Top": 0.25267452001571655}, "Polygon": [{"X": 0.5425330996513367, "Y": 0.2526750862598419}, {"X": 0.6237851977348328, "Y": 0.25267452001571655}, {"X": 0.6237850785255432, "Y": 0.2666873037815094}, {"X": 0.5425330400466919, "Y": 0.2666878402233124}]}, "Id": "d8798224-1723-4ea6-889d-a1d8701aa1a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "requested", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07900632172822952, "Height": 0.014109116978943348, "Left": 0.6288719773292542, "Top": 0.2523818016052246}, "Polygon": [{"X": 0.6288720369338989, "Y": 0.2523823380470276}, {"X": 0.7078782916069031, "Y": 0.2523818016052246}, {"X": 0.7078781723976135, "Y": 0.2664903998374939}, {"X": 0.6288719773292542, "Y": 0.2664909362792969}]}, "Id": "77a4677c-f485-4a19-bcfa-1adb2a213b5a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049652546644210815, "Height": 0.013003595173358917, "Left": 0.7132872343063354, "Top": 0.2532727122306824}, "Polygon": [{"X": 0.713287353515625, "Y": 0.25327304005622864}, {"X": 0.7629397511482239, "Y": 0.2532727122306824}, {"X": 0.7629396319389343, "Y": 0.26627597212791443}, {"X": 0.7132872343063354, "Y": 0.2662763297557831}]}, "Id": "f5bd9fb9-64fe-443a-addc-4891587f975b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "rail", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024053063243627548, "Height": 0.011369963176548481, "Left": 0.7677999138832092, "Top": 0.2518576383590698}, "Polygon": [{"X": 0.7678000330924988, "Y": 0.25185781717300415}, {"X": 0.7918530106544495, "Y": 0.2518576383590698}, {"X": 0.7918528914451599, "Y": 0.2632274627685547}, {"X": 0.7677999138832092, "Y": 0.2632276117801666}]}, "Id": "ea8ba6a2-9510-4e33-8df6-11b4d5d6a76f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04531731456518173, "Height": 0.014136667363345623, "Left": 0.7973267436027527, "Top": 0.2516114413738251}, "Polygon": [{"X": 0.7973268628120422, "Y": 0.25161176919937134}, {"X": 0.8426440358161926, "Y": 0.2516114413738251}, {"X": 0.8426439166069031, "Y": 0.2657478153705597}, {"X": 0.7973267436027527, "Y": 0.26574811339378357}]}, "Id": "f8c40dc4-2dda-4d65-b72a-0ddfe1ac6b71"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02334308624267578, "Height": 0.01166919432580471, "Left": 0.8474478721618652, "Top": 0.2513394355773926}, "Polygon": [{"X": 0.8474479913711548, "Y": 0.2513395845890045}, {"X": 0.870790958404541, "Y": 0.2513394355773926}, {"X": 0.8707908391952515, "Y": 0.2630084455013275}, {"X": 0.8474478721618652, "Y": 0.26300862431526184}]}, "Id": "6f3adc79-35d9-4713-998b-62161eab4428"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02882801927626133, "Height": 0.011199748143553734, "Left": 0.8757330179214478, "Top": 0.25161653757095337}, "Polygon": [{"X": 0.8757331371307373, "Y": 0.2516167163848877}, {"X": 0.9045610427856445, "Y": 0.25161653757095337}, {"X": 0.9045608639717102, "Y": 0.26281610131263733}, {"X": 0.8757330179214478, "Y": 0.26281628012657166}]}, "Id": "9454ca1b-a9bc-4a3b-9d7e-dd66cde7c27b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "approved", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07396218180656433, "Height": 0.014254222624003887, "Left": 0.07453656196594238, "Top": 0.2703445851802826}, "Polygon": [{"X": 0.07453657686710358, "Y": 0.2703450918197632}, {"X": 0.1484987437725067, "Y": 0.2703445851802826}, {"X": 0.14849872887134552, "Y": 0.28459832072257996}, {"X": 0.07453656196594238, "Y": 0.28459882736206055}]}, "Id": "80813b0f-0674-468b-b057-38d2cfafe8d0"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07635980099439621, "Height": 0.011340507306158543, "Left": 0.15441910922527313, "Top": 0.27022650837898254}, "Polygon": [{"X": 0.15441912412643433, "Y": 0.27022701501846313}, {"X": 0.23077890276908875, "Y": 0.27022650837898254}, {"X": 0.23077887296676636, "Y": 0.2815665006637573}, {"X": 0.15441910922527313, "Y": 0.2815670073032379}]}, "Id": "d8b0c9ee-e611-4f95-b97b-e9857920d305"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0155923618003726, "Height": 0.009874357841908932, "Left": 0.2351905107498169, "Top": 0.27133986353874207}, "Polygon": [{"X": 0.23519054055213928, "Y": 0.27133995294570923}, {"X": 0.25078287720680237, "Y": 0.27133986353874207}, {"X": 0.25078284740448, "Y": 0.2812141180038452}, {"X": 0.2351905107498169, "Y": 0.2812142074108124}]}, "Id": "f0fdf101-a8d2-4573-8950-3220f5d5ae94"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "cancel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05195959284901619, "Height": 0.011276313103735447, "Left": 0.2561227083206177, "Top": 0.2698640823364258}, "Polygon": [{"X": 0.25612273812294006, "Y": 0.26986443996429443}, {"X": 0.3080822825431824, "Y": 0.2698640823364258}, {"X": 0.30808225274086, "Y": 0.2811400592327118}, {"X": 0.2561227083206177, "Y": 0.28114038705825806}]}, "Id": "8f6177ba-1ac7-453b-824a-444ad45e03a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025059551000595093, "Height": 0.01106532383710146, "Left": 0.31378254294395447, "Top": 0.26987001299858093}, "Polygon": [{"X": 0.31378260254859924, "Y": 0.26987016201019287}, {"X": 0.33884209394454956, "Y": 0.26987001299858093}, {"X": 0.3388420641422272, "Y": 0.2809351682662964}, {"X": 0.31378254294395447, "Y": 0.2809353172779083}]}, "Id": "df9016a8-ceb0-404c-97af-8ca34adc104e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "request", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06013132259249687, "Height": 0.012849929742515087, "Left": 0.3437901437282562, "Top": 0.27074119448661804}, "Polygon": [{"X": 0.343790203332901, "Y": 0.2707415819168091}, {"X": 0.4039214849472046, "Y": 0.27074119448661804}, {"X": 0.4039213955402374, "Y": 0.283590704202652}, {"X": 0.3437901437282562, "Y": 0.2835911214351654}]}, "Id": "4c547858-3f31-4d2e-9f66-124427f7dcc1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02192837744951248, "Height": 0.011723305098712444, "Left": 0.4085655212402344, "Top": 0.2689233124256134}, "Polygon": [{"X": 0.40856558084487915, "Y": 0.26892346143722534}, {"X": 0.43049389123916626, "Y": 0.2689233124256134}, {"X": 0.4304938316345215, "Y": 0.2806464731693268}, {"X": 0.4085655212402344, "Y": 0.2806466221809387}]}, "Id": "eb076347-f333-46ab-a6e9-c50c332909e5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04998540133237839, "Height": 0.01299155130982399, "Left": 0.4355328679084778, "Top": 0.2704488933086395}, "Polygon": [{"X": 0.43553292751312256, "Y": 0.2704492211341858}, {"X": 0.4855182468891144, "Y": 0.2704488933086395}, {"X": 0.4855181872844696, "Y": 0.28344011306762695}, {"X": 0.4355328679084778, "Y": 0.2834404408931732}]}, "Id": "a021d8be-1c43-4798-8907-23d1244c3d51"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "rail", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02347845584154129, "Height": 0.01140166912227869, "Left": 0.4906330704689026, "Top": 0.2690300941467285}, "Polygon": [{"X": 0.49063315987586975, "Y": 0.26903024315834045}, {"X": 0.5141115188598633, "Y": 0.2690300941467285}, {"X": 0.5141114592552185, "Y": 0.2804315984249115}, {"X": 0.4906330704689026, "Y": 0.2804317772388458}]}, "Id": "273bb936-0d85-402b-b30f-7c1a48c4d967"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "billing.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05130833387374878, "Height": 0.014344565570354462, "Left": 0.5198599696159363, "Top": 0.2687930464744568}, "Polygon": [{"X": 0.5198600888252258, "Y": 0.26879340410232544}, {"X": 0.5711683034896851, "Y": 0.2687930464744568}, {"X": 0.5711681842803955, "Y": 0.2831372618675232}, {"X": 0.5198599696159363, "Y": 0.28313761949539185}]}, "Id": "e6926a72-4c23-4329-9422-708a095bfb82"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05263634771108627, "Height": 0.011341174133121967, "Left": 0.5780179500579834, "Top": 0.26881200075149536}, "Polygon": [{"X": 0.578018069267273, "Y": 0.268812358379364}, {"X": 0.6306543350219727, "Y": 0.26881200075149536}, {"X": 0.6306542158126831, "Y": 0.2801527976989746}, {"X": 0.5780179500579834, "Y": 0.28015315532684326}]}, "Id": "776b8c07-f45c-4e0c-a62b-104c9f8cd731"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "cancel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051582325249910355, "Height": 0.011277321726083755, "Left": 0.6358032822608948, "Top": 0.2686237096786499}, "Polygon": [{"X": 0.6358034014701843, "Y": 0.26862403750419617}, {"X": 0.687385618686676, "Y": 0.2686237096786499}, {"X": 0.6873855590820312, "Y": 0.2799006700515747}, {"X": 0.6358032822608948, "Y": 0.27990102767944336}]}, "Id": "bd72262d-84e3-4894-be50-dfc6e426b144"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04932235926389694, "Height": 0.012888498604297638, "Left": 0.6927333474159241, "Top": 0.26971983909606934}, "Polygon": [{"X": 0.6927334070205688, "Y": 0.2697201669216156}, {"X": 0.7420556545257568, "Y": 0.26971983909606934}, {"X": 0.7420555353164673, "Y": 0.2826080024242401}, {"X": 0.6927333474159241, "Y": 0.2826083302497864}]}, "Id": "3fddfc56-89da-42a3-9e8f-d7e34c37aa7d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "rail", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0237987469881773, "Height": 0.011537659913301468, "Left": 0.747028648853302, "Top": 0.2681425213813782}, "Polygon": [{"X": 0.7470287680625916, "Y": 0.2681426703929901}, {"X": 0.7708273530006409, "Y": 0.2681425213813782}, {"X": 0.7708272933959961, "Y": 0.2796800136566162}, {"X": 0.747028648853302, "Y": 0.27968019247055054}]}, "Id": "43ee4d8f-5877-44f9-8c04-5d7e71531d0d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0472700260579586, "Height": 0.014191444031894207, "Left": 0.7765963673591614, "Top": 0.2679278254508972}, "Polygon": [{"X": 0.7765964865684509, "Y": 0.2679281234741211}, {"X": 0.8238663673400879, "Y": 0.2679278254508972}, {"X": 0.8238662481307983, "Y": 0.28211894631385803}, {"X": 0.7765963673591614, "Y": 0.2821192741394043}]}, "Id": "df7a2199-4d2f-40c9-94a5-b008947c08d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016971109434962273, "Height": 0.014052356593310833, "Left": 0.828572690486908, "Top": 0.26796016097068787}, "Polygon": [{"X": 0.8285728693008423, "Y": 0.2679602801799774}, {"X": 0.8455438017845154, "Y": 0.26796016097068787}, {"X": 0.8455436825752258, "Y": 0.282012403011322}, {"X": 0.828572690486908, "Y": 0.2820125222206116}]}, "Id": "ad9f5044-ad21-40da-84a1-1d25ff04bb39"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "contacting", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0855490118265152, "Height": 0.014113623648881912, "Left": 0.07444506138563156, "Top": 0.2865588963031769}, "Polygon": [{"X": 0.07444506883621216, "Y": 0.28655949234962463}, {"X": 0.15999406576156616, "Y": 0.2865588963031769}, {"X": 0.15999403595924377, "Y": 0.30067193508148193}, {"X": 0.07444506138563156, "Y": 0.3006725311279297}]}, "Id": "0b62d88c-f197-41db-8fff-e6d7f3c8716e"}, {"BlockType": "WORD", "Confidence": 93.18478393554688, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22394633293151855, "Height": 0.013529027812182903, "Left": 0.16541296243667603, "Top": 0.286626398563385}, "Polygon": [{"X": 0.1654129922389984, "Y": 0.2866278886795044}, {"X": 0.3893592953681946, "Y": 0.286626398563385}, {"X": 0.3893592357635498, "Y": 0.3001538813114166}, {"X": 0.16541296243667603, "Y": 0.3001554012298584}]}, "Id": "9e68a8e5-bfb3-4086-b2f6-1b64d517e5e5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Request", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06563617289066315, "Height": 0.013728944584727287, "Left": 0.07499157637357712, "Top": 0.33872950077056885}, "Polygon": [{"X": 0.07499158382415771, "Y": 0.33872994780540466}, {"X": 0.14062774181365967, "Y": 0.33872950077056885}, {"X": 0.14062772691249847, "Y": 0.35245800018310547}, {"X": 0.07499157637357712, "Y": 0.3524584472179413}]}, "Id": "b31933ad-7118-4f84-a35c-a3398cdbb8a0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "history", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05510270968079567, "Height": 0.013980504125356674, "Left": 0.14582374691963196, "Top": 0.33813878893852234}, "Polygon": [{"X": 0.14582376182079315, "Y": 0.3381391763687134}, {"X": 0.20092645287513733, "Y": 0.33813878893852234}, {"X": 0.20092642307281494, "Y": 0.35211893916130066}, {"X": 0.14582374691963196, "Y": 0.3521192967891693}]}, "Id": "eee2e67f-733b-4bc7-82f6-3e73562bcd28"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09509307146072388, "Height": 0.011667396873235703, "Left": 0.07459906488656998, "Top": 0.3613302409648895}, "Polygon": [{"X": 0.07459907233715057, "Y": 0.36133089661598206}, {"X": 0.16969214379787445, "Y": 0.3613302409648895}, {"X": 0.16969211399555206, "Y": 0.372996985912323}, {"X": 0.07459906488656998, "Y": 0.3729976415634155}]}, "Id": "9c9bc9df-a141-484a-b8d8-2b72d35ee0db"}, {"BlockType": "WORD", "Confidence": 99.501953125, "Text": "09:38", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04455842077732086, "Height": 0.011272257193922997, "Left": 0.17519748210906982, "Top": 0.36096838116645813}, "Polygon": [{"X": 0.17519749701023102, "Y": 0.360968679189682}, {"X": 0.21975590288639069, "Y": 0.36096838116645813}, {"X": 0.2197558730840683, "Y": 0.3722403049468994}, {"X": 0.17519748210906982, "Y": 0.3722406327724457}]}, "Id": "2b52c116-bdc5-4682-b507-7a9c5cadc8bd"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026337262243032455, "Height": 0.010689899325370789, "Left": 0.22477789223194122, "Top": 0.3612072765827179}, "Polygon": [{"X": 0.2247779220342636, "Y": 0.3612074553966522}, {"X": 0.2511151432991028, "Y": 0.3612072765827179}, {"X": 0.2511151134967804, "Y": 0.37189701199531555}, {"X": 0.22477789223194122, "Y": 0.3718971908092499}]}, "Id": "9eed9ebc-3647-49a1-a820-6c5a5fd93402"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Approved", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07584540545940399, "Height": 0.01422877050936222, "Left": 0.26539307832717896, "Top": 0.3601917028427124}, "Polygon": [{"X": 0.26539310812950134, "Y": 0.3601922392845154}, {"X": 0.34123846888542175, "Y": 0.3601917028427124}, {"X": 0.341238409280777, "Y": 0.3744199573993683}, {"X": 0.26539307832717896, "Y": 0.37442049384117126}]}, "Id": "0d23765e-4842-4833-a4e1-d14593a6a154"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09535041451454163, "Height": 0.011616840958595276, "Left": 0.07463734596967697, "Top": 0.38149723410606384}, "Polygon": [{"X": 0.07463735342025757, "Y": 0.38149788975715637}, {"X": 0.169987753033638, "Y": 0.38149723410606384}, {"X": 0.1699877381324768, "Y": 0.3931134045124054}, {"X": 0.07463734596967697, "Y": 0.3931140601634979}]}, "Id": "b6ba6d70-e305-4495-8a28-771107af590a"}, {"BlockType": "WORD", "Confidence": 99.619140625, "Text": "09:38", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04450378939509392, "Height": 0.011140330694615841, "Left": 0.17527875304222107, "Top": 0.3812001943588257}, "Polygon": [{"X": 0.17527878284454346, "Y": 0.38120049238204956}, {"X": 0.21978254616260529, "Y": 0.3812001943588257}, {"X": 0.2197825163602829, "Y": 0.39234021306037903}, {"X": 0.17527875304222107, "Y": 0.3923405110836029}]}, "Id": "ce4625af-17f8-4acf-abd8-bfc9f7f70ef3"}, {"BlockType": "WORD", "Confidence": 99.56951904296875, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02638285607099533, "Height": 0.010736378841102123, "Left": 0.22468365728855133, "Top": 0.38124334812164307}, "Polygon": [{"X": 0.22468368709087372, "Y": 0.3812435567378998}, {"X": 0.25106650590896606, "Y": 0.38124334812164307}, {"X": 0.2510664761066437, "Y": 0.39197954535484314}, {"X": 0.22468365728855133, "Y": 0.39197975397109985}]}, "Id": "ee03d300-9e33-401f-88c7-1a7a03e0ec68"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06667140871286392, "Height": 0.011266705580055714, "Left": 0.26584938168525696, "Top": 0.3806125819683075}, "Polygon": [{"X": 0.26584941148757935, "Y": 0.3806130290031433}, {"X": 0.3325207829475403, "Y": 0.3806125819683075}, {"X": 0.3325207233428955, "Y": 0.39187881350517273}, {"X": 0.26584938168525696, "Y": 0.39187929034233093}]}, "Id": "774311a4-464b-4065-bde3-cde9d38a1388"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "submitted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08007321506738663, "Height": 0.01138785108923912, "Left": 0.33801522850990295, "Top": 0.3801659345626831}, "Polygon": [{"X": 0.33801528811454773, "Y": 0.38016650080680847}, {"X": 0.418088436126709, "Y": 0.3801659345626831}, {"X": 0.4180883765220642, "Y": 0.39155325293540955}, {"X": 0.33801522850990295, "Y": 0.3915537893772125}]}, "Id": "93d76b38-eab2-413e-88e2-18662cb548f4"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09550055861473083, "Height": 0.011496477760374546, "Left": 0.07473282516002655, "Top": 0.4014601707458496}, "Polygon": [{"X": 0.07473283261060715, "Y": 0.4014608561992645}, {"X": 0.17023338377475739, "Y": 0.4014601707458496}, {"X": 0.170233353972435, "Y": 0.41295599937438965}, {"X": 0.07473282516002655, "Y": 0.4129566550254822}]}, "Id": "2a1b8f42-18e3-47de-a3c4-6b471575a076"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "09:06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044056955724954605, "Height": 0.010992160998284817, "Left": 0.17558464407920837, "Top": 0.40138009190559387}, "Polygon": [{"X": 0.17558465898036957, "Y": 0.40138041973114014}, {"X": 0.21964159607887268, "Y": 0.40138009190559387}, {"X": 0.2196415662765503, "Y": 0.412371963262558}, {"X": 0.17558464407920837, "Y": 0.41237226128578186}]}, "Id": "d617ddfd-c012-40f7-a376-b2ae84374f21"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026514772325754166, "Height": 0.010643407702445984, "Left": 0.22476036846637726, "Top": 0.40127265453338623}, "Polygon": [{"X": 0.22476039826869965, "Y": 0.40127286314964294}, {"X": 0.2512751519680023, "Y": 0.40127265453338623}, {"X": 0.25127512216567993, "Y": 0.4119158983230591}, {"X": 0.22476036846637726, "Y": 0.4119160771369934}]}, "Id": "83fbbdf1-ea50-4058-9abb-b11dd50daa62"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Declined", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.068719781935215, "Height": 0.011370878666639328, "Left": 0.266343355178833, "Top": 0.40049782395362854}, "Polygon": [{"X": 0.2663433849811554, "Y": 0.40049830079078674}, {"X": 0.3350631296634674, "Y": 0.40049782395362854}, {"X": 0.335063099861145, "Y": 0.41186821460723877}, {"X": 0.266343355178833, "Y": 0.411868691444397}]}, "Id": "c8d2bfdd-1489-426b-889e-757aaf4428b7"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09529003500938416, "Height": 0.011468429118394852, "Left": 0.074763223528862, "Top": 0.4216049313545227}, "Polygon": [{"X": 0.0747632309794426, "Y": 0.42160558700561523}, {"X": 0.17005325853824615, "Y": 0.4216049313545227}, {"X": 0.17005322873592377, "Y": 0.43307268619537354}, {"X": 0.074763223528862, "Y": 0.43307334184646606}]}, "Id": "3107b4cd-e6ad-4c94-8abc-7dc00c5888d1"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "09:06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04436309635639191, "Height": 0.011131244711577892, "Left": 0.17536325752735138, "Top": 0.4213294982910156}, "Polygon": [{"X": 0.17536327242851257, "Y": 0.4213297963142395}, {"X": 0.2197263538837433, "Y": 0.4213294982910156}, {"X": 0.2197263240814209, "Y": 0.4324604272842407}, {"X": 0.17536325752735138, "Y": 0.432460755109787}]}, "Id": "1037849f-0dae-4be1-a80a-eb070bf5abf6"}, {"BlockType": "WORD", "Confidence": 99.501953125, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026405813172459602, "Height": 0.010728554800152779, "Left": 0.22485478222370148, "Top": 0.42151325941085815}, "Polygon": [{"X": 0.22485481202602386, "Y": 0.4215134382247925}, {"X": 0.25126057863235474, "Y": 0.42151325941085815}, {"X": 0.25126054883003235, "Y": 0.43224161863327026}, {"X": 0.22485478222370148, "Y": 0.432241827249527}]}, "Id": "1ff02c67-9729-4be0-98c5-db842cca8015"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06664028763771057, "Height": 0.011020724661648273, "Left": 0.2660493850708008, "Top": 0.42089757323265076}, "Polygon": [{"X": 0.26604941487312317, "Y": 0.4208980202674866}, {"X": 0.33268967270851135, "Y": 0.42089757323265076}, {"X": 0.33268964290618896, "Y": 0.43191781640052795}, {"X": 0.2660493850708008, "Y": 0.43191829323768616}]}, "Id": "272cc902-6cae-4aa4-ad7a-20a207252c30"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "submitted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07981694489717484, "Height": 0.011442468501627445, "Left": 0.3382253050804138, "Top": 0.420322060585022}, "Polygon": [{"X": 0.3382253348827362, "Y": 0.42032262682914734}, {"X": 0.41804224252700806, "Y": 0.420322060585022}, {"X": 0.4180421829223633, "Y": 0.43176397681236267}, {"X": 0.3382253050804138, "Y": 0.43176454305648804}]}, "Id": "24e6da0f-490f-4b3b-b5b3-73cb87915db9"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0954170823097229, "Height": 0.011625545099377632, "Left": 0.07485289871692657, "Top": 0.44159236550331116}, "Polygon": [{"X": 0.07485291361808777, "Y": 0.4415930509567261}, {"X": 0.17026998102664948, "Y": 0.44159236550331116}, {"X": 0.17026996612548828, "Y": 0.4532172381877899}, {"X": 0.07485289871692657, "Y": 0.45321792364120483}]}, "Id": "c59d85c8-69ef-4137-8f4d-6c78e4fceded"}, {"BlockType": "WORD", "Confidence": 99.53125, "Text": "07:45", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04436158388853073, "Height": 0.01110877189785242, "Left": 0.17561957240104675, "Top": 0.44131869077682495}, "Polygon": [{"X": 0.17561960220336914, "Y": 0.44131898880004883}, {"X": 0.21998116374015808, "Y": 0.44131869077682495}, {"X": 0.2199811339378357, "Y": 0.4524271488189697}, {"X": 0.17561957240104675, "Y": 0.4524274468421936}]}, "Id": "0cf5d9b9-6f42-4e99-a562-1a1a67b27d05"}, {"BlockType": "WORD", "Confidence": 99.443359375, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026397401466965675, "Height": 0.010570740327239037, "Left": 0.22496847808361053, "Top": 0.441561758518219}, "Polygon": [{"X": 0.22496850788593292, "Y": 0.4415619373321533}, {"X": 0.25136587023735046, "Y": 0.441561758518219}, {"X": 0.2513658404350281, "Y": 0.45213231444358826}, {"X": 0.22496847808361053, "Y": 0.4521324932575226}]}, "Id": "a553663a-724e-4b0b-8867-29479776e5d7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Declined", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06848611682653427, "Height": 0.011225027963519096, "Left": 0.26654282212257385, "Top": 0.4406757056713104}, "Polygon": [{"X": 0.26654285192489624, "Y": 0.44067618250846863}, {"X": 0.3350289463996887, "Y": 0.4406757056713104}, {"X": 0.33502888679504395, "Y": 0.4519002437591553}, {"X": 0.26654282212257385, "Y": 0.4519007205963135}]}, "Id": "1252df5b-21e2-41dc-9dc7-419391fbf622"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09531017392873764, "Height": 0.011672147549688816, "Left": 0.07488468289375305, "Top": 0.461599737405777}, "Polygon": [{"X": 0.07488469034433365, "Y": 0.4616004228591919}, {"X": 0.1701948493719101, "Y": 0.461599737405777}, {"X": 0.1701948344707489, "Y": 0.4732712209224701}, {"X": 0.07488468289375305, "Y": 0.473271906375885}]}, "Id": "6c44179c-a468-40f3-b85a-621381c63e86"}, {"BlockType": "WORD", "Confidence": 99.560546875, "Text": "07:45", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04415346682071686, "Height": 0.01120959036052227, "Left": 0.1757238209247589, "Top": 0.46124082803726196}, "Polygon": [{"X": 0.1757238507270813, "Y": 0.4612411558628082}, {"X": 0.21987728774547577, "Y": 0.46124082803726196}, {"X": 0.21987725794315338, "Y": 0.4724501073360443}, {"X": 0.1757238209247589, "Y": 0.4724504351615906}]}, "Id": "1fc45e81-8306-487e-9ba7-f87ec2a7b035"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026057682931423187, "Height": 0.010573135688900948, "Left": 0.22509868443012238, "Top": 0.4615510106086731}, "Polygon": [{"X": 0.22509871423244476, "Y": 0.4615512192249298}, {"X": 0.25115635991096497, "Y": 0.4615510106086731}, {"X": 0.2511563301086426, "Y": 0.47212398052215576}, {"X": 0.22509868443012238, "Y": 0.4721241593360901}]}, "Id": "8ea3ab63-2136-4fe5-b381-23eacc822514"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pending", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0647222101688385, "Height": 0.013983549550175667, "Left": 0.26665782928466797, "Top": 0.4605606496334076}, "Polygon": [{"X": 0.26665788888931274, "Y": 0.4605611264705658}, {"X": 0.33138006925582886, "Y": 0.4605606496334076}, {"X": 0.3313800096511841, "Y": 0.4745437502861023}, {"X": 0.26665782928466797, "Y": 0.4745441973209381}]}, "Id": "e9945ad5-ba7f-4a08-ad2e-fe1a40c9a3c1"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09530515968799591, "Height": 0.011828041635453701, "Left": 0.07489026337862015, "Top": 0.4815858006477356}, "Polygon": [{"X": 0.07489027082920074, "Y": 0.4815864562988281}, {"X": 0.17019541561603546, "Y": 0.4815858006477356}, {"X": 0.17019540071487427, "Y": 0.49341315031051636}, {"X": 0.07489026337862015, "Y": 0.4934138357639313}]}, "Id": "b47265cb-8662-40d1-92ce-b2193f6177c3"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "07:45", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04422533139586449, "Height": 0.011106442660093307, "Left": 0.17572233080863953, "Top": 0.48152562975883484}, "Polygon": [{"X": 0.17572236061096191, "Y": 0.4815259575843811}, {"X": 0.2199476659297943, "Y": 0.48152562975883484}, {"X": 0.21994763612747192, "Y": 0.4926317632198334}, {"X": 0.17572233080863953, "Y": 0.49263209104537964}]}, "Id": "ff88581f-8461-4359-aebb-8b40be0db098"}, {"BlockType": "WORD", "Confidence": 99.26598358154297, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026118868961930275, "Height": 0.010566049255430698, "Left": 0.2250753939151764, "Top": 0.4815979599952698}, "Polygon": [{"X": 0.22507542371749878, "Y": 0.4815981388092041}, {"X": 0.2511942684650421, "Y": 0.4815979599952698}, {"X": 0.2511942386627197, "Y": 0.4921638071537018}, {"X": 0.2250753939151764, "Y": 0.4921640157699585}]}, "Id": "4cd7fd95-6e07-4441-ad85-c4b434df6cb2"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Created", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061425160616636276, "Height": 0.011414081789553165, "Left": 0.2661896049976349, "Top": 0.48071080446243286}, "Polygon": [{"X": 0.2661896347999573, "Y": 0.4807112216949463}, {"X": 0.32761475443840027, "Y": 0.48071080446243286}, {"X": 0.3276147246360779, "Y": 0.49212443828582764}, {"X": 0.2661896049976349, "Y": 0.49212488532066345}]}, "Id": "abdd5811-c899-40db-8854-df65fbbee404"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018502559512853622, "Height": 0.014211972244083881, "Left": 0.3331368863582611, "Top": 0.48039913177490234}, "Polygon": [{"X": 0.3331369459629059, "Y": 0.4803992509841919}, {"X": 0.35163944959640503, "Y": 0.48039913177490234}, {"X": 0.35163938999176025, "Y": 0.4946109652519226}, {"X": 0.3331368863582611, "Y": 0.49461108446121216}]}, "Id": "62e8c434-ab22-41c7-bf4c-0b8f2f2ac10d"}, {"BlockType": "WORD", "Confidence": 96.01482391357422, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25527679920196533, "Height": 0.014595494605600834, "Left": 0.35597965121269226, "Top": 0.47972577810287476}, "Polygon": [{"X": 0.35597971081733704, "Y": 0.4797275960445404}, {"X": 0.6112564206123352, "Y": 0.47972577810287476}, {"X": 0.6112563610076904, "Y": 0.49431946873664856}, {"X": 0.35597965121269226, "Y": 0.4943212866783142}]}, "Id": "d030a0de-dcdd-4ea2-bc5f-fe00b3292346"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015373707748949528, "Height": 0.010235002264380455, "Left": 0.6161008477210999, "Top": 0.48102226853370667}, "Polygon": [{"X": 0.6161009073257446, "Y": 0.4810223877429962}, {"X": 0.6314745545387268, "Y": 0.48102226853370667}, {"X": 0.631474494934082, "Y": 0.4912571609020233}, {"X": 0.6161008477210999, "Y": 0.49125728011131287}]}, "Id": "ed1b4d55-7a5b-4a39-8760-99f4253dc14a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Super", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045935697853565216, "Height": 0.01353093795478344, "Left": 0.6362489461898804, "Top": 0.479965478181839}, "Polygon": [{"X": 0.6362490057945251, "Y": 0.47996580600738525}, {"X": 0.682184636592865, "Y": 0.479965478181839}, {"X": 0.6821845173835754, "Y": 0.4934960901737213}, {"X": 0.6362489461898804, "Y": 0.4934964179992676}]}, "Id": "fb039530-acee-4649-b1be-9fb8129552a0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Star", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032233692705631256, "Height": 0.010796835646033287, "Left": 0.6874955296516418, "Top": 0.4798853397369385}, "Polygon": [{"X": 0.6874956488609314, "Y": 0.4798855781555176}, {"X": 0.7197292447090149, "Y": 0.4798853397369385}, {"X": 0.7197291254997253, "Y": 0.4906819462776184}, {"X": 0.6874955296516418, "Y": 0.4906821846961975}]}, "Id": "4610b0b4-fd93-4c87-8ae8-c7207ce2c670"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05441376566886902, "Height": 0.014160564169287682, "Left": 0.7253177762031555, "Top": 0.479099303483963}, "Polygon": [{"X": 0.7253178954124451, "Y": 0.47909969091415405}, {"X": 0.7797315716743469, "Y": 0.479099303483963}, {"X": 0.7797313928604126, "Y": 0.493259459733963}, {"X": 0.7253177762031555, "Y": 0.49325984716415405}]}, "Id": "2d5c831b-f6bc-4a44-910b-bae6f0c5ba31"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Generated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05880502611398697, "Height": 0.008335324935615063, "Left": 0.6526296138763428, "Top": 0.5265823006629944}, "Polygon": [{"X": 0.6526296734809875, "Y": 0.5265827178955078}, {"X": 0.7114346623420715, "Y": 0.5265823006629944}, {"X": 0.711434543132782, "Y": 0.5349171757698059}, {"X": 0.6526296138763428, "Y": 0.5349175930023193}]}, "Id": "940711aa-4165-4130-bfb5-177246751049"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013613083399832249, "Height": 0.01038037147372961, "Left": 0.7153636813163757, "Top": 0.5262162685394287}, "Polygon": [{"X": 0.7153638005256653, "Y": 0.5262163877487183}, {"X": 0.7289767861366272, "Y": 0.5262162685394287}, {"X": 0.7289766669273376, "Y": 0.5365965366363525}, {"X": 0.7153636813163757, "Y": 0.5365966558456421}]}, "Id": "a1cd1133-ce50-4f56-8542-8cf7bb276edd"}, {"BlockType": "WORD", "Confidence": 99.40449523925781, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050138089805841446, "Height": 0.008470561355352402, "Left": 0.7325917482376099, "Top": 0.5260834097862244}, "Polygon": [{"X": 0.7325918674468994, "Y": 0.526083767414093}, {"X": 0.7827298641204834, "Y": 0.5260834097862244}, {"X": 0.7827298045158386, "Y": 0.5345536470413208}, {"X": 0.7325917482376099, "Y": 0.5345540046691895}]}, "Id": "0f62561e-3ed6-4aa1-83a9-507e960893ce"}, {"BlockType": "WORD", "Confidence": 99.619140625, "Text": "09/12/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06635776162147522, "Height": 0.008280263282358646, "Left": 0.7864412069320679, "Top": 0.5261391997337341}, "Polygon": [{"X": 0.7864413261413574, "Y": 0.5261396765708923}, {"X": 0.8527989983558655, "Y": 0.5261391997337341}, {"X": 0.8527988791465759, "Y": 0.5344190001487732}, {"X": 0.7864412069320679, "Y": 0.5344194769859314}]}, "Id": "04a63c48-f7ed-47d1-b4b7-f992bc38357c"}, {"BlockType": "WORD", "Confidence": 99.56951904296875, "Text": "10:25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031016312539577484, "Height": 0.008255667984485626, "Left": 0.8587642312049866, "Top": 0.5259607434272766}, "Polygon": [{"X": 0.8587643504142761, "Y": 0.5259609818458557}, {"X": 0.889780580997467, "Y": 0.5259607434272766}, {"X": 0.8897804617881775, "Y": 0.5342162251472473}, {"X": 0.8587642312049866, "Y": 0.5342164039611816}]}, "Id": "65c7b995-3762-46b7-8b3a-35e253052c1a"}, {"BlockType": "WORD", "Confidence": 99.50115966796875, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01852567307651043, "Height": 0.007930517196655273, "Left": 0.8932766318321228, "Top": 0.5261385440826416}, "Polygon": [{"X": 0.8932767510414124, "Y": 0.5261386632919312}, {"X": 0.9118022918701172, "Y": 0.5261385440826416}, {"X": 0.9118022322654724, "Y": 0.5340689420700073}, {"X": 0.8932766318321228, "Y": 0.5340690612792969}]}, "Id": "cfad9bcd-99d1-4754-82f5-fffef7385c78"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "f327965c-87c0-42bf-b7e8-15302cd5a226", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "f327965c-87c0-42bf-b7e8-15302cd5a226", "content-type": "application/x-amz-json-1.1", "content-length": "82359", "date": "Wed, 24 Sep 2025 13:32:28 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "742cdf16-4ac4-41d5-93fc-0224b0127b68", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:31 GMT", "content-type": "application/json", "content-length": "1219", "connection": "keep-alive", "x-amzn-requestid": "742cdf16-4ac4-41d5-93fc-0224b0127b68"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only page1 given. We need to examine content. It's a document with title \"APPROVED\" \"MAERSK\" \"Reuse empty container\" etc. Looks like a container approval or request to approve reuse of empty container. The keywords: \"APPROVED\", \"Reuse empty container\", \"Container number\", \"Export booking ref.\", \"Import location\", \"Export location\", etc. Not a typical invoice, POD, BOL, etc. It appears to be a container approval/booking confirmation. Not matching any provided categories except maybe \"other\". We should check if any category matches. There's no \"invoice\", \"bol\", \"pod\", \"rate_confirmation\", etc. None. So classify as \"other\". The instructions: if page is continuation of previous page, assign same type. But it's just one page. So output:\n\n{\n \"documents\": [\n   {\"page_no\": 1, \"doc_type\": \"other\"}\n ]\n}\n\nReturn via tool call."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"other\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2858, "outputTokens": 220, "totalTokens": 3078}, "metrics": {"latencyMs": 1049}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/f0640456_VY0AAD98JWYCZUAHKROB.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nAPPROVED\nMAERSK\nReuse empty container\nContainer number\nExport booking ref.\nReuse date\nCAAU8485442\n257637187\n09/12/2025\nImport location\nExport location\nPrice\nIllinois\nIllinois\nUSD30\nRef. client\nImport delivery date\nSCAC (import)\nSCAC (export)\n--\n--\nSSRT\nSSRT\nCOMMENT\nFor North America Carriers, please ensure that if you have previously requested empty rail billing for this\napproved container to cancel the request for empty rail billing. Please cancel empty rail billing by\ncontacting <EMAIL>\nRequest history\n09/12/2025 09:38 AM\nApproved\n09/12/2025 09:38 AM\nOverrule submitted\n09/12/2025 09:06 AM\nDeclined\n09/12/2025 09:06 AM Overrule submitted\n09/12/2025 07:45 AM\nDeclined\n09/12/2025 07:45 AM\nPending\n09/12/2025 07:45 AM <NAME_EMAIL> at Super Star Freight\nGenerated by Avantida 09/12/2025 10:25 AM\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}