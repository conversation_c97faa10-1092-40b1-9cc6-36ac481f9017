#!/usr/bin/env python3
"""
Debug Test Script for test_classification.py

This script helps test the debugging configuration and demonstrates
how to use the enhanced debugging features.
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

from test_classification import FileProcessor


def test_debug_configuration():
    """Test the debug configuration setup."""
    print("🧪 Testing Debug Configuration")
    print("=" * 50)
    
    # Check if debug environment variables are set
    debug_on_error = os.getenv('DEBUG_ON_ERROR', '').lower() in ('true', '1', 'yes')
    print(f"DEBUG_ON_ERROR: {debug_on_error}")
    
    # Check Python path
    python_path = os.getenv('PYTHONPATH', '')
    print(f"PYTHONPATH: {python_path}")
    
    # Check if external repository is accessible
    external_repo = "/home/<USER>/Documents/repositories/document-data-extraction"
    external_accessible = os.path.exists(external_repo)
    print(f"External repo accessible: {external_accessible} ({external_repo})")
    
    # Check if local main.py exists
    local_main = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts', 'main.py')
    local_main_exists = os.path.exists(local_main)
    print(f"Local main.py exists: {local_main_exists} ({local_main})")
    
    print("\n✅ Debug configuration test completed")


async def test_file_processor_with_debug():
    """Test the FileProcessor with debug features."""
    print("\n🧪 Testing FileProcessor with Debug Features")
    print("=" * 50)
    
    # Enable debug mode
    os.environ['DEBUG_ON_ERROR'] = 'true'
    
    # Create a test processor
    processor = FileProcessor("Augment_test/debug_output", "Augment_test/debug_logs")
    
    # Test with a non-existent folder to trigger an error
    try:
        files = processor.get_supported_files("/non/existent/folder")
        print(f"Found files: {files}")
    except Exception as e:
        print(f"Expected error caught: {e}")
    
    # Test with an existing folder if available
    test_folder = "/home/<USER>/Documents/repositories/test_logistically/verified_docs/log"
    if os.path.exists(test_folder):
        print(f"\n📁 Testing with existing folder: {test_folder}")
        files = processor.get_supported_files(test_folder)
        print(f"Found {len(files)} files")
        if files:
            print(f"First few files: {files[:3]}")
    else:
        print(f"\n⚠ Test folder not found: {test_folder}")
    
    print("\n✅ FileProcessor debug test completed")


def test_import_debugging():
    """Test the import debugging features."""
    print("\n🧪 Testing Import Debugging")
    print("=" * 50)
    
    # Test importing the classification function
    try:
        from test_classification import llm_classification
        if llm_classification:
            print("✅ Classification function imported successfully")
            print(f"Function: {llm_classification}")
        else:
            print("❌ Classification function is None")
    except Exception as e:
        print(f"❌ Failed to import classification function: {e}")
    
    print("\n✅ Import debugging test completed")


def create_sample_test_files():
    """Create sample test files for debugging."""
    print("\n🧪 Creating Sample Test Files")
    print("=" * 50)
    
    # Create test directory
    test_dir = Path("Augment_test/sample_files")
    test_dir.mkdir(exist_ok=True)
    
    # Create a sample text file (will be ignored by the processor)
    sample_txt = test_dir / "sample.txt"
    sample_txt.write_text("This is a sample text file for testing.")
    
    # Create a sample JSON file (will be ignored by the processor)
    sample_json = test_dir / "sample.json"
    sample_json.write_text('{"test": "data"}')
    
    print(f"✅ Created sample files in: {test_dir}")
    print(f"   - {sample_txt}")
    print(f"   - {sample_json}")
    print("Note: These files will be ignored by the processor (unsupported extensions)")
    
    return str(test_dir)


async def run_debug_tests():
    """Run all debug tests."""
    print("🚀 Starting Debug Tests for test_classification.py")
    print("=" * 60)
    
    # Test 1: Debug configuration
    test_debug_configuration()
    
    # Test 2: Import debugging
    test_import_debugging()
    
    # Test 3: FileProcessor with debug
    await test_file_processor_with_debug()
    
    # Test 4: Create sample files
    sample_dir = create_sample_test_files()
    
    print(f"\n🎉 All debug tests completed!")
    print(f"\n📋 Next Steps:")
    print(f"1. Open VS Code in the workspace root")
    print(f"2. Go to Run and Debug (Ctrl+Shift+D)")
    print(f"3. Select one of the debug configurations:")
    print(f"   - 'Debug test_classification.py (Default Test)'")
    print(f"   - 'Debug test_classification.py (Custom Folder)'")
    print(f"   - 'Debug test_classification.py (Step Into All Code)'")
    print(f"4. Set breakpoints in the code where you want to debug")
    print(f"5. Press F5 to start debugging")
    print(f"\n🐛 Debug Features Available:")
    print(f"   - Use --debug flag to enable post-mortem debugging on errors")
    print(f"   - Use --debug-on-start flag to start debugger immediately")
    print(f"   - Set DEBUG_ON_ERROR=true environment variable")
    print(f"   - justMyCode is set to false to debug into external libraries")


if __name__ == "__main__":
    asyncio.run(run_debug_tests())
