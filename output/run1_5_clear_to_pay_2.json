{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 1.4989719602453988e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "c2202085-023a-4280-b27c-bef48f1d8a27", "Relationships": [{"Type": "CHILD", "Ids": ["806a9297-31d0-423f-bdb8-7f2f6e5380a7", "0db21a07-0afb-4a58-9179-e20bdebb1068", "090f7e51-4911-4b22-bad3-7011ecf124be", "1438c3f0-7d17-405b-b3ac-ebdf39160563", "8e9eb8a0-26dc-4f58-af82-03cb4af119e7", "42af4ab4-9abb-4197-b0a2-448e41c4ca3f", "a8d91fc0-1c68-49c6-b8ea-14d0646205ae", "9c2c45d9-4d8b-4d71-a1e3-3520c468c29a", "12aa0906-4ff6-4ebe-824c-a77fd567b11d", "f0f34f13-e86c-43b2-88c1-e20a82e1212e", "cc1bdbc6-5d8f-48f4-aad6-a846072cebe0", "49f935b1-52d4-42b2-87c4-5d0cae91da8c", "461537c5-0955-41a8-8a4e-db37db3a1a1a", "de7dd18e-bd3b-426c-aadf-d03b26d837aa", "24e76fc8-badc-481e-a1b6-88a9d845bbcb", "28748f64-5769-43bd-8ca9-bab2f28c2d59", "1afd78be-b521-428e-b953-0d15556f7420", "8d93282d-4033-43cf-801f-6b6459a70004", "1aa803da-fb61-437e-ba1a-9ef00b30a3fc", "cf2d7fde-f566-4f7e-bf72-56cc534056d5", "f83efbac-fa41-438b-ba15-5dfc53277c75", "439b7c3a-ef6d-45f3-afcc-3cf64a525ae7", "98e18668-2dc1-4209-b07d-e79abac69a6a", "1ef94126-c7b3-4f09-b79d-6488a715cd01", "79a51042-ead0-4289-91ed-c2a46a479025", "ced65a9f-73ab-412d-ae0c-07ac25973253", "34ac2a65-7896-49d7-8b61-a81314fee69a", "ea86647b-7e0d-4881-8554-b9b128693401", "7abd3c4a-2df0-4938-9bc5-c9b8c1f89225", "cd89bdc7-e676-4a93-b6b5-e4964a8146b2", "abd9ce2a-c0c0-4ea6-b338-37ce6096e2ba", "825cb5d5-7a43-4a00-9d7d-c98ecdd0d4bb", "8b49ed1a-e6bb-4b20-b3e8-8871a21578be", "4c9c2683-0976-4c34-bca2-500927999cae", "9c983268-4443-4999-b1ed-247196aa73c0"]}]}, {"BlockType": "LINE", "Confidence": 94.87942504882812, "Text": "Saw 7056563", "Geometry": {"BoundingBox": {"Width": 0.30521300435066223, "Height": 0.06624899059534073, "Left": 0.5896632671356201, "Top": 0.07072446495294571}, "Polygon": [{"X": 0.5896651148796082, "Y": 0.07072446495294571}, {"X": 0.89487624168396, "Y": 0.07076777517795563}, {"X": 0.8948742151260376, "Y": 0.13697345554828644}, {"X": 0.5896632671356201, "Y": 0.1369301825761795}]}, "Id": "806a9297-31d0-423f-bdb8-7f2f6e5380a7", "Relationships": [{"Type": "CHILD", "Ids": ["6c660b7b-5afb-4d9c-8370-f47afb9a8f4d", "08363343-525d-4502-88ea-80e89748e3e8"]}]}, {"BlockType": "LINE", "Confidence": 20.76201820373535, "Text": "AMBVD", "Geometry": {"BoundingBox": {"Width": 0.22545401751995087, "Height": 0.03536486625671387, "Left": 0.4016489088535309, "Top": 0.1331961750984192}, "Polygon": [{"X": 0.4016498625278473, "Y": 0.1331961750984192}, {"X": 0.6271029114723206, "Y": 0.13322813808918}, {"X": 0.6271018981933594, "Y": 0.16856104135513306}, {"X": 0.4016489088535309, "Y": 0.16852909326553345}]}, "Id": "0db21a07-0afb-4a58-9179-e20bdebb1068", "Relationships": [{"Type": "CHILD", "Ids": ["e05ca002-867f-4fe3-bf7a-72853d33f8fd"]}]}, {"BlockType": "LINE", "Confidence": 96.95153045654297, "Text": "SAWYER NURSERY.", "Geometry": {"BoundingBox": {"Width": 0.43205714225769043, "Height": 0.02347600646317005, "Left": 0.300431489944458, "Top": 0.18134063482284546}, "Polygon": [{"X": 0.30043211579322815, "Y": 0.18134063482284546}, {"X": 0.7324886322021484, "Y": 0.18140184879302979}, {"X": 0.7324879169464111, "Y": 0.20481663942337036}, {"X": 0.300431489944458, "Y": 0.20475544035434723}]}, "Id": "090f7e51-4911-4b22-bad3-7011ecf124be", "Relationships": [{"Type": "CHILD", "Ids": ["e84f30e4-5e9e-472f-bbf5-39a58aa79eec", "7bf841b6-7cae-495f-82da-997c8f4180d3"]}]}, {"BlockType": "LINE", "Confidence": 99.86573791503906, "Text": "Truckload Check-in Form", "Geometry": {"BoundingBox": {"Width": 0.34514355659484863, "Height": 0.019788047298789024, "Left": 0.3518599271774292, "Top": 0.2139761745929718}, "Polygon": [{"X": 0.3518604636192322, "Y": 0.2139761745929718}, {"X": 0.6970034837722778, "Y": 0.21402505040168762}, {"X": 0.6970028877258301, "Y": 0.23376421630382538}, {"X": 0.3518599271774292, "Y": 0.23371534049510956}]}, "Id": "1438c3f0-7d17-405b-b3ac-ebdf39160563", "Relationships": [{"Type": "CHILD", "Ids": ["6ee1255f-7cb5-4aa8-8876-e63f5cdd002b", "c23de31d-bc3b-4b29-85cd-8bbdc467f9fc", "dc1b7703-3d8e-4963-929b-01617ac8a186"]}]}, {"BlockType": "LINE", "Confidence": 99.56791687011719, "Text": "Clear-to-Pay Voucher", "Geometry": {"BoundingBox": {"Width": 0.2923758924007416, "Height": 0.02381926216185093, "Left": 0.37930452823638916, "Top": 0.24309197068214417}, "Polygon": [{"X": 0.3793051540851593, "Y": 0.24309197068214417}, {"X": 0.6716804504394531, "Y": 0.24313335120677948}, {"X": 0.6716797351837158, "Y": 0.26691123843193054}, {"X": 0.37930452823638916, "Y": 0.26686984300613403}]}, "Id": "8e9eb8a0-26dc-4f58-af82-03cb4af119e7", "Relationships": [{"Type": "CHILD", "Ids": ["fec66d73-7f1e-4e60-bb99-6ef245c81118", "891f6d99-a33d-4318-bb70-c3e0d7530365"]}]}, {"BlockType": "LINE", "Confidence": 98.12207794189453, "Text": "Load # 20081", "Geometry": {"BoundingBox": {"Width": 0.14595557749271393, "Height": 0.021700123324990273, "Left": 0.11647119373083115, "Top": 0.28144410252571106}, "Polygon": [{"X": 0.11647173017263412, "Y": 0.28144410252571106}, {"X": 0.2624267637729645, "Y": 0.28146475553512573}, {"X": 0.2624261975288391, "Y": 0.3031442165374756}, {"X": 0.11647119373083115, "Y": 0.3031235933303833}]}, "Id": "42af4ab4-9abb-4197-b0a2-448e41c4ca3f", "Relationships": [{"Type": "CHILD", "Ids": ["c33f19a9-babb-48a9-af9e-d0a48082e6d1", "5aab2ca3-b37d-4415-bfcb-b334608c3b0a", "4d1abf96-9f01-469e-9c3b-8034a2397580"]}]}, {"BlockType": "LINE", "Confidence": 99.7608413696289, "Text": "Broker", "Geometry": {"BoundingBox": {"Width": 0.05547386035323143, "Height": 0.011601464822888374, "Left": 0.5692568421363831, "Top": 0.286699503660202}, "Polygon": [{"X": 0.5692571401596069, "Y": 0.286699503660202}, {"X": 0.6247307062149048, "Y": 0.2867073714733124}, {"X": 0.6247303485870361, "Y": 0.29830098152160645}, {"X": 0.5692568421363831, "Y": 0.2982931435108185}]}, "Id": "a8d91fc0-1c68-49c6-b8ea-14d0646205ae", "Relationships": [{"Type": "CHILD", "Ids": ["bb9c2840-f0da-49ab-bbb3-139001df9bd5"]}]}, {"BlockType": "LINE", "Confidence": 98.9674301147461, "Text": "PE", "Geometry": {"BoundingBox": {"Width": 0.07020014524459839, "Height": 0.03418971225619316, "Left": 0.6797472834587097, "Top": 0.2667059600353241}, "Polygon": [{"X": 0.6797482967376709, "Y": 0.2667059600353241}, {"X": 0.7499474287033081, "Y": 0.2667158842086792}, {"X": 0.7499464154243469, "Y": 0.30089566111564636}, {"X": 0.6797472834587097, "Y": 0.30088573694229126}]}, "Id": "9c2c45d9-4d8b-4d71-a1e3-3520c468c29a", "Relationships": [{"Type": "CHILD", "Ids": ["99478d42-8361-4f73-968c-b653ff0523fa"]}]}, {"BlockType": "LINE", "Confidence": 99.85736846923828, "Text": "Date Processed 5/26/2023", "Geometry": {"BoundingBox": {"Width": 0.29935935139656067, "Height": 0.027756407856941223, "Left": 0.11656401306390762, "Top": 0.3263808786869049}, "Polygon": [{"X": 0.11656470596790314, "Y": 0.3263808786869049}, {"X": 0.4159233570098877, "Y": 0.32642319798469543}, {"X": 0.415922611951828, "Y": 0.3541373014450073}, {"X": 0.11656401306390762, "Y": 0.3540949821472168}]}, "Id": "12aa0906-4ff6-4ebe-824c-a77fd567b11d", "Relationships": [{"Type": "CHILD", "Ids": ["b01ec2d2-f509-425a-b4fa-4b59b3757467", "1971727c-29e7-4b70-a989-6c03749b7b77", "99372cc0-e16b-42cc-9160-3317514acf44"]}]}, {"BlockType": "LINE", "Confidence": 93.41092681884766, "Text": "Load Cost $1,811.50", "Geometry": {"BoundingBox": {"Width": 0.2618899941444397, "Height": 0.030339840799570084, "Left": 0.5735875964164734, "Top": 0.33014822006225586}, "Polygon": [{"X": 0.5735884308815002, "Y": 0.33014822006225586}, {"X": 0.8354775905609131, "Y": 0.33018526434898376}, {"X": 0.8354766368865967, "Y": 0.36048805713653564}, {"X": 0.5735875964164734, "Y": 0.3604510426521301}]}, "Id": "f0f34f13-e86c-43b2-88c1-e20a82e1212e", "Relationships": [{"Type": "CHILD", "Ids": ["1a23af7f-6a5a-4e85-9115-3546a1ab5068", "4d4f896f-97c2-4960-a0b2-10e3215d6da1", "7b6df8d2-2112-4a5b-95dd-ac18942e64e3"]}]}, {"BlockType": "LINE", "Confidence": 99.93463134765625, "Text": "<PERSON><PERSON> Returned 58", "Geometry": {"BoundingBox": {"Width": 0.22270430624485016, "Height": 0.019123701378703117, "Left": 0.12101997435092926, "Top": 0.36618831753730774}, "Polygon": [{"X": 0.12102044373750687, "Y": 0.36618831753730774}, {"X": 0.3437242805957794, "Y": 0.36621978878974915}, {"X": 0.34372377395629883, "Y": 0.385312020778656}, {"X": 0.12101997435092926, "Y": 0.3852805495262146}]}, "Id": "cc1bdbc6-5d8f-48f4-aad6-a846072cebe0", "Relationships": [{"Type": "CHILD", "Ids": ["3f069e46-23d5-4453-9688-5c8e2f75bea8", "4f8128f4-bf60-4e6b-8677-c7380f0db681", "30391b00-f2f5-4ef8-8eb8-1a9bc4bf399a"]}]}, {"BlockType": "LINE", "Confidence": 98.70064544677734, "Text": "Surcharge + $", "Geometry": {"BoundingBox": {"Width": 0.12142102420330048, "Height": 0.015464779920876026, "Left": 0.5784105658531189, "Top": 0.38868266344070435}, "Polygon": [{"X": 0.5784109830856323, "Y": 0.38868266344070435}, {"X": 0.6998315453529358, "Y": 0.38869979977607727}, {"X": 0.6998311281204224, "Y": 0.4041474163532257}, {"X": 0.5784105658531189, "Y": 0.4041302800178528}]}, "Id": "49f935b1-52d4-42b2-87c4-5d0cae91da8c", "Relationships": [{"Type": "CHILD", "Ids": ["f882b5bf-59f2-411d-abe2-f0e09000620f", "b05ad6f4-240a-416c-b9db-2eebf8c787cd", "afbc9227-3ecc-4d3e-9f13-48a978a8749b"]}]}, {"BlockType": "LINE", "Confidence": 82.40931701660156, "Text": "/", "Geometry": {"BoundingBox": {"Width": 0.03604384511709213, "Height": 0.007077441085129976, "Left": 0.739342212677002, "Top": 0.3872559368610382}, "Polygon": [{"X": 0.739342451095581, "Y": 0.3872559368610382}, {"X": 0.7753860354423523, "Y": 0.3872610330581665}, {"X": 0.775385856628418, "Y": 0.3943333923816681}, {"X": 0.739342212677002, "Y": 0.3943282961845398}]}, "Id": "461537c5-0955-41a8-8a4e-db37db3a1a1a", "Relationships": [{"Type": "CHILD", "Ids": ["76cb8390-63fd-4179-b929-c25bf08ae66d"]}]}, {"BlockType": "LINE", "Confidence": 96.5987777709961, "Text": "Deductions - $", "Geometry": {"BoundingBox": {"Width": 0.12572361528873444, "Height": 0.012501686811447144, "Left": 0.5792409181594849, "Top": 0.4233436584472656}, "Polygon": [{"X": 0.5792412757873535, "Y": 0.4233436584472656}, {"X": 0.7049645185470581, "Y": 0.4233614206314087}, {"X": 0.7049641609191895, "Y": 0.43584534525871277}, {"X": 0.5792409181594849, "Y": 0.4358276128768921}]}, "Id": "de7dd18e-bd3b-426c-aadf-d03b26d837aa", "Relationships": [{"Type": "CHILD", "Ids": ["c387112f-a42d-445c-9524-93cd074f2f2b", "68a8bf4b-e949-4ed2-81f7-f6b4e003fa90"]}]}, {"BlockType": "LINE", "Confidence": 94.48660278320312, "Text": "\\", "Geometry": {"BoundingBox": {"Width": 0.029602937400341034, "Height": 0.008931504562497139, "Left": 0.7403873801231384, "Top": 0.4204845726490021}, "Polygon": [{"X": 0.7403876185417175, "Y": 0.4204845726490021}, {"X": 0.7699902653694153, "Y": 0.42048874497413635}, {"X": 0.7699900269508362, "Y": 0.42941606044769287}, {"X": 0.7403873801231384, "Y": 0.4294118881225586}]}, "Id": "24e76fc8-badc-481e-a1b6-88a9d845bbcb", "Relationships": [{"Type": "CHILD", "Ids": ["55874354-5fb9-4421-aceb-51ca1e5d45a4"]}]}, {"BlockType": "LINE", "Confidence": 87.01582336425781, "Text": "58 XS 4.- = $232,-", "Geometry": {"BoundingBox": {"Width": 0.33475369215011597, "Height": 0.024369128048419952, "Left": 0.13731786608695984, "Top": 0.43149495124816895}, "Polygon": [{"X": 0.1373184770345688, "Y": 0.43149495124816895}, {"X": 0.4720715880393982, "Y": 0.43154218792915344}, {"X": 0.4720709025859833, "Y": 0.4558640718460083}, {"X": 0.13731786608695984, "Y": 0.4558168053627014}]}, "Id": "28748f64-5769-43bd-8ca9-bab2f28c2d59", "Relationships": [{"Type": "CHILD", "Ids": ["f082f622-d142-464c-991e-22e4c56b40c9", "76e63b6c-18d3-4b93-ace8-eb5653fd3503", "d20ad11f-792b-43e5-8d1c-6b5be7b9acbd", "a2a71510-e6a6-4774-a961-b69db0b66383", "60b0eeec-a906-4f8b-be6b-7385bb0ecbf4"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "RACKS AMOUNT TOTAL", "Geometry": {"BoundingBox": {"Width": 0.3177521824836731, "Height": 0.011901987716555595, "Left": 0.11665182560682297, "Top": 0.45716094970703125}, "Polygon": [{"X": 0.11665212363004684, "Y": 0.45716094970703125}, {"X": 0.43440401554107666, "Y": 0.45720580220222473}, {"X": 0.4344036877155304, "Y": 0.4690629541873932}, {"X": 0.11665182560682297, "Y": 0.4690181016921997}]}, "Id": "1afd78be-b521-428e-b953-0d15556f7420", "Relationships": [{"Type": "CHILD", "Ids": ["26dd7677-0d8d-4a02-8337-7503e6e331bf", "28117a60-2f4f-4123-acfe-844e08930599", "a880ae8a-02a0-4c44-8fc2-0549c8adb6c1"]}]}, {"BlockType": "LINE", "Confidence": 74.80265808105469, "Text": "Racks + $232,-", "Geometry": {"BoundingBox": {"Width": 0.24389666318893433, "Height": 0.02032937854528427, "Left": 0.5825254917144775, "Top": 0.46969568729400635}, "Polygon": [{"X": 0.5825260877609253, "Y": 0.46969568729400635}, {"X": 0.8264221549034119, "Y": 0.46973010897636414}, {"X": 0.8264215588569641, "Y": 0.4900250732898712}, {"X": 0.5825254917144775, "Y": 0.4899906814098358}]}, "Id": "8d93282d-4033-43cf-801f-6b6459a70004", "Relationships": [{"Type": "CHILD", "Ids": ["dd649200-8311-45a2-84d0-43b736d1720e", "048e9443-46a1-495a-a477-3bb972fa3eaf", "ba65b9e8-2ea4-4428-bced-157cb6718b8f"]}]}, {"BlockType": "LINE", "Confidence": 99.92434692382812, "Text": "(Rack checks will now be sent to the", "Geometry": {"BoundingBox": {"Width": 0.29254835844039917, "Height": 0.014317430555820465, "Left": 0.5824187994003296, "Top": 0.4914499819278717}, "Polygon": [{"X": 0.5824191570281982, "Y": 0.4914499819278717}, {"X": 0.8749671578407288, "Y": 0.49149125814437866}, {"X": 0.8749666810035706, "Y": 0.5057674050331116}, {"X": 0.5824187994003296, "Y": 0.505726158618927}]}, "Id": "1aa803da-fb61-437e-ba1a-9ef00b30a3fc", "Relationships": [{"Type": "CHILD", "Ids": ["a7581844-68c3-47a5-8750-5f2dc9628d12", "e8872f30-7484-487e-9f60-50ced91fad87", "fbb7321e-96cf-4429-bdad-ffa66409e0ca", "be840eb9-462d-485d-afa0-f039d3bc8a65", "3171a1cd-acaf-4db3-8d5a-6b2a2fbf2f09", "28045193-7e19-49b8-a6da-403472ad5d8f", "1520418f-99b5-466e-a599-c6e3aa66dc8b", "1d960130-31e0-45fe-a4d9-42e5908856e2"]}]}, {"BlockType": "LINE", "Confidence": 99.48262023925781, "Text": "broker.)", "Geometry": {"BoundingBox": {"Width": 0.06332949548959732, "Height": 0.01425098069012165, "Left": 0.5937391519546509, "Top": 0.5080398917198181}, "Polygon": [{"X": 0.5937395691871643, "Y": 0.5080398917198181}, {"X": 0.65706866979599, "Y": 0.5080488324165344}, {"X": 0.6570682525634766, "Y": 0.5222908854484558}, {"X": 0.5937391519546509, "Y": 0.5222819447517395}]}, "Id": "cf2d7fde-f566-4f7e-bf72-56cc534056d5", "Relationships": [{"Type": "CHILD", "Ids": ["848ad0d6-1c97-4281-9ebf-20a6b40cbc12"]}]}, {"BlockType": "LINE", "Confidence": 97.79057312011719, "Text": "Net Cost $2,043.50", "Geometry": {"BoundingBox": {"Width": 0.25439634919166565, "Height": 0.026055634021759033, "Left": 0.5835145115852356, "Top": 0.5375574231147766}, "Polygon": [{"X": 0.5835152268409729, "Y": 0.5375574231147766}, {"X": 0.8379108905792236, "Y": 0.5375933051109314}, {"X": 0.8379100561141968, "Y": 0.5636130571365356}, {"X": 0.5835145115852356, "Y": 0.5635772347450256}]}, "Id": "f83efbac-fa41-438b-ba15-5dfc53277c75", "Relationships": [{"Type": "CHILD", "Ids": ["cdd03a89-b312-46b0-8fd7-4942337bbe95", "23749880-7004-4f19-b60e-3a561ac97c7e", "cebde096-3583-420b-bd31-e727da1000e7"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Billable Shortages:", "Geometry": {"BoundingBox": {"Width": 0.2506729066371918, "Height": 0.022772829979658127, "Left": 0.11781423538923264, "Top": 0.5796347856521606}, "Polygon": [{"X": 0.1178147941827774, "Y": 0.5796347856521606}, {"X": 0.3684871196746826, "Y": 0.5796701312065125}, {"X": 0.36848652362823486, "Y": 0.6024076342582703}, {"X": 0.11781423538923264, "Y": 0.6023723483085632}]}, "Id": "439b7c3a-ef6d-45f3-afcc-3cf64a525ae7", "Relationships": [{"Type": "CHILD", "Ids": ["cf39cb8c-e235-4e9a-87e8-e167efd4e250", "831d9fba-6ec4-495a-8704-47ff1c7bdef7"]}]}, {"BlockType": "LINE", "Confidence": 87.95926666259766, "Text": "requipment missing with insufficient documentation", "Geometry": {"BoundingBox": {"Width": 0.2841707170009613, "Height": 0.011054752394557, "Left": 0.11641670018434525, "Top": 0.603716254234314}, "Polygon": [{"X": 0.11641696840524673, "Y": 0.603716254234314}, {"X": 0.40058740973472595, "Y": 0.6037562489509583}, {"X": 0.4005871117115021, "Y": 0.6147710084915161}, {"X": 0.11641670018434525, "Y": 0.6147310137748718}]}, "Id": "98e18668-2dc1-4209-b07d-e79abac69a6a", "Relationships": [{"Type": "CHILD", "Ids": ["93f24196-60b8-4a9f-8340-f5a312b019a6", "46302429-a199-4afb-a58c-ee5b29d04c8a", "a5c4410d-1bdf-4a9b-b5f9-3aee177aa513", "9cce9b34-81d3-469a-85b7-3b0e072083eb", "67318b5d-fb06-4a4d-a6bd-b3490bf9be04"]}]}, {"BlockType": "LINE", "Confidence": 94.10102844238281, "Text": "Racks: ( ) X $250.00 $", "Geometry": {"BoundingBox": {"Width": 0.202506422996521, "Height": 0.0137887978926301, "Left": 0.1173315942287445, "Top": 0.6283055543899536}, "Polygon": [{"X": 0.11733193695545197, "Y": 0.6283055543899536}, {"X": 0.3198380172252655, "Y": 0.628334105014801}, {"X": 0.31983765959739685, "Y": 0.6420943737030029}, {"X": 0.1173315942287445, "Y": 0.6420658826828003}]}, "Id": "1ef94126-c7b3-4f09-b79d-6488a715cd01", "Relationships": [{"Type": "CHILD", "Ids": ["4f6f0d1a-d8ff-4b89-836b-5cf6397c820a", "df3da0f0-2c34-43a1-83a8-30476fc14078", "5d083a2a-c264-488c-b97b-b1944f21c6cb", "b95f691b-a673-4a91-a054-f84a0bd700cc", "81c2fd37-fb89-4139-afcc-6ae3d76f34b7", "28fca8bd-a581-49cb-82f1-b04762f6c4bd"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Total Deductions", "Geometry": {"BoundingBox": {"Width": 0.1384696364402771, "Height": 0.011604785919189453, "Left": 0.6024941802024841, "Top": 0.6283608078956604}, "Polygon": [{"X": 0.602494478225708, "Y": 0.6283608078956604}, {"X": 0.7409638166427612, "Y": 0.628380298614502}, {"X": 0.7409634590148926, "Y": 0.6399655938148499}, {"X": 0.6024941802024841, "Y": 0.6399461030960083}]}, "Id": "79a51042-ead0-4289-91ed-c2a46a479025", "Relationships": [{"Type": "CHILD", "Ids": ["81cfb714-956b-45e1-93b2-97f2f6f1638b", "73c3f0e9-13e6-4e00-9f38-a3f10f101f95"]}]}, {"BlockType": "LINE", "Confidence": 99.73114776611328, "Text": "For Load:", "Geometry": {"BoundingBox": {"Width": 0.07765011489391327, "Height": 0.011426055803894997, "Left": 0.6047047972679138, "Top": 0.6456210613250732}, "Polygon": [{"X": 0.6047050952911377, "Y": 0.6456210613250732}, {"X": 0.6823548674583435, "Y": 0.6456320285797119}, {"X": 0.6823545694351196, "Y": 0.6570471525192261}, {"X": 0.6047047972679138, "Y": 0.6570362448692322}]}, "Id": "ced65a9f-73ab-412d-ae0c-07ac25973253", "Relationships": [{"Type": "CHILD", "Ids": ["d48de1e9-3e24-4547-b72c-2f22686d5ee1", "df2044fa-31bc-440d-acc5-3002de0f2d71"]}]}, {"BlockType": "LINE", "Confidence": 88.97608947753906, "Text": "Load Locks: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2385191172361374, "Height": 0.014859232120215893, "Left": 0.117476686835289, "Top": 0.6616898775100708}, "Polygon": [{"X": 0.11747705191373825, "Y": 0.6616898775100708}, {"X": 0.3559958040714264, "Y": 0.6617234349250793}, {"X": 0.35599541664123535, "Y": 0.6765490770339966}, {"X": 0.117476686835289, "Y": 0.676515519618988}]}, "Id": "34ac2a65-7896-49d7-8b61-a81314fee69a", "Relationships": [{"Type": "CHILD", "Ids": ["eda33a2b-7b49-43ed-88dd-64f4d65a93de", "b2b54748-91a7-4ee5-ac7c-19f2166be3fd", "cebd77ca-2f9c-4460-ab9c-7ed70f40b836", "2212a1ff-e206-4b78-9868-7b1b778d09f1", "84fd2dbd-a00f-4733-8314-594ee4d7bdb5", "238e40b4-831d-479d-ac5c-42b1f672fca0", "81eca921-f118-4fbe-914b-b9e8f91f42b9"]}]}, {"BlockType": "LINE", "Confidence": 95.77567291259766, "Text": "$", "Geometry": {"BoundingBox": {"Width": 0.010098937898874283, "Height": 0.01212747860699892, "Left": 0.603948712348938, "Top": 0.6791166067123413}, "Polygon": [{"X": 0.6039490699768066, "Y": 0.6791166067123413}, {"X": 0.614047646522522, "Y": 0.6791180372238159}, {"X": 0.6140472888946533, "Y": 0.6912440657615662}, {"X": 0.603948712348938, "Y": 0.6912426352500916}]}, "Id": "ea86647b-7e0d-4881-8554-b9b128693401", "Relationships": [{"Type": "CHILD", "Ids": ["354b61cd-1184-4686-b1f0-a9cb707b8fd7"]}]}, {"BlockType": "LINE", "Confidence": 91.79894256591797, "Text": "Pallet Jack: ( ) X $500.00 - $", "Geometry": {"BoundingBox": {"Width": 0.23895935714244843, "Height": 0.013947353698313236, "Left": 0.1181296557188034, "Top": 0.6963074207305908}, "Polygon": [{"X": 0.11812999844551086, "Y": 0.6963074207305908}, {"X": 0.35708901286125183, "Y": 0.6963410377502441}, {"X": 0.3570886552333832, "Y": 0.7102547883987427}, {"X": 0.1181296557188034, "Y": 0.7102211713790894}]}, "Id": "7abd3c4a-2df0-4938-9bc5-c9b8c1f89225", "Relationships": [{"Type": "CHILD", "Ids": ["2911ebe4-134d-4b03-9e7a-4db9aa241233", "e091975f-0109-4b63-a815-e13c76c854f1", "32fe4b32-c048-4ee6-bc7f-26feff0f5f18", "ec548534-b274-4550-a6f2-34028b27d13e", "fc12d067-5536-4450-b337-5eb1a0fdf9e5", "8fde49fd-2b78-454e-93ff-ae7806c112d2", "999faac8-5aa0-41be-bf71-1e4b538490e6", "3a23e8e0-0667-4020-9bc7-3f10f2833555"]}]}, {"BlockType": "LINE", "Confidence": 91.82164001464844, "Text": "Load Straps: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2388901710510254, "Height": 0.015205707401037216, "Left": 0.11816539615392685, "Top": 0.7303385138511658}, "Polygon": [{"X": 0.1181657686829567, "Y": 0.7303385138511658}, {"X": 0.35705557465553284, "Y": 0.7303721308708191}, {"X": 0.3570551574230194, "Y": 0.7455442547798157}, {"X": 0.11816539615392685, "Y": 0.7455106377601624}]}, "Id": "cd89bdc7-e676-4a93-b6b5-e4964a8146b2", "Relationships": [{"Type": "CHILD", "Ids": ["9e3ac13f-b3f6-410e-9b62-d0e248f49cd2", "1570c908-933a-402d-a9b5-8bb6b2679a49", "983e07ce-3e19-4f35-9b9e-208c51f82169", "4999a2b6-e0ad-4840-8528-c573b974986d", "e1155924-ed23-466d-ab07-408ef6a0da6c", "77702a43-f291-4dbc-8921-badd8410b015", "406aa1c3-7251-47dd-af57-99abab0b4859", "9d71b919-ecca-4c84-bb36-3a39495d89ba"]}]}, {"BlockType": "LINE", "Confidence": 99.73851776123047, "Text": "Paperwork & other Fines $", "Geometry": {"BoundingBox": {"Width": 0.21507374942302704, "Height": 0.014641941525042057, "Left": 0.11782500147819519, "Top": 0.7648339867591858}, "Polygon": [{"X": 0.11782536655664444, "Y": 0.7648339867591858}, {"X": 0.33289873600006104, "Y": 0.7648642063140869}, {"X": 0.33289834856987, "Y": 0.7794759273529053}, {"X": 0.11782500147819519, "Y": 0.7794457077980042}]}, "Id": "abd9ce2a-c0c0-4ea6-b338-37ce6096e2ba", "Relationships": [{"Type": "CHILD", "Ids": ["3c188cff-fe2e-4fb3-a82a-8ee932e650c6", "1b70e2ac-cf6a-4a86-9681-c443c789a1ff", "0a5b4df6-2f33-43fb-a3e1-f425da1c09ce", "84a3da4a-d37b-467d-9114-5e458a77337f", "bce7cfb4-5c00-409f-bf5a-9cd0fd3db088"]}]}, {"BlockType": "LINE", "Confidence": 99.05366516113281, "Text": "(see attached documentation)", "Geometry": {"BoundingBox": {"Width": 0.23336835205554962, "Height": 0.014204194769263268, "Left": 0.1175626888871193, "Top": 0.782246470451355}, "Polygon": [{"X": 0.11756303906440735, "Y": 0.782246470451355}, {"X": 0.3509310483932495, "Y": 0.7822792530059814}, {"X": 0.35093066096305847, "Y": 0.796450674533844}, {"X": 0.1175626888871193, "Y": 0.7964178919792175}]}, "Id": "825cb5d5-7a43-4a00-9d7d-c98ecdd0d4bb", "Relationships": [{"Type": "CHILD", "Ids": ["b172c2db-5e50-4f1c-b4d9-5de00727a999", "28737787-1083-4198-9ab9-4f278baecea8", "3b27b0a0-b54b-49cc-ac81-36a2729afbc5"]}]}, {"BlockType": "LINE", "Confidence": 99.62133026123047, "Text": "Manager A<PERSON><PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.15500731766223907, "Height": 0.014326130039989948, "Left": 0.5494952201843262, "Top": 0.7830010652542114}, "Polygon": [{"X": 0.5494956374168396, "Y": 0.7830010652542114}, {"X": 0.704502522945404, "Y": 0.7830228805541992}, {"X": 0.7045021057128906, "Y": 0.7973272204399109}, {"X": 0.5494952201843262, "Y": 0.7973054647445679}]}, "Id": "8b49ed1a-e6bb-4b20-b3e8-8871a21578be", "Relationships": [{"Type": "CHILD", "Ids": ["6a65fd37-0289-48ba-98ae-b28514cbf4de", "118a691c-fd3a-43da-9feb-989ffd542d3e"]}]}, {"BlockType": "LINE", "Confidence": 99.48181915283203, "Text": "Notes:", "Geometry": {"BoundingBox": {"Width": 0.05159206688404083, "Height": 0.012029643170535564, "Left": 0.11761604994535446, "Top": 0.8164384365081787}, "Polygon": [{"X": 0.11761634796857834, "Y": 0.8164384365081787}, {"X": 0.1692081242799759, "Y": 0.8164456486701965}, {"X": 0.16920781135559082, "Y": 0.8284680843353271}, {"X": 0.11761604994535446, "Y": 0.8284608125686646}]}, "Id": "4c9c2683-0976-4c34-bca2-500927999cae", "Relationships": [{"Type": "CHILD", "Ids": ["577cbd04-d4c1-4127-905f-e61230f56571"]}]}, {"BlockType": "LINE", "Confidence": 97.44171142578125, "Text": "Any fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468", "Geometry": {"BoundingBox": {"Width": 0.7673737406730652, "Height": 0.013018960133194923, "Left": 0.11811070144176483, "Top": 0.8672084212303162}, "Polygon": [{"X": 0.1181110218167305, "Y": 0.8672084212303162}, {"X": 0.8854844570159912, "Y": 0.8673160672187805}, {"X": 0.8854840993881226, "Y": 0.8802273869514465}, {"X": 0.11811070144176483, "Y": 0.8801197409629822}]}, "Id": "9c983268-4443-4999-b1ed-247196aa73c0", "Relationships": [{"Type": "CHILD", "Ids": ["5d8e2b88-d34e-4415-a24a-9e40f0b32775", "9fc0aabc-871d-4b46-a127-e1c65a0427af", "19817f10-14ab-4dd5-9d8f-ec8759603c39", "d8b3ff60-4090-435e-83c5-e66b89b6427d", "69c82dbb-5e6c-4353-85eb-057c47e8fd44", "73f9e09d-e123-4884-85b8-b0ffee2bcc5f", "8f6b27eb-b5a1-4130-a424-56fec3702cfa", "45d8f212-63ea-488a-b1a9-12c7f7bccb09", "2c14f80d-ab20-4122-97ab-8b0422280ae4", "c85023ba-3a60-418f-8967-4f601a155c51", "e9e6a0c5-0cad-4ff5-8ac5-8f1f2b1a6096", "6bb81cc5-0bd0-4261-9f19-61a966c0406e", "d4fe6ec8-0f97-4a5d-8e9b-6ab316c1c499", "9faea021-bd18-4b50-a045-f6ec77c6e934", "9987ef6c-e85d-4760-8062-129b074d0aa9", "08bd7dd8-a67b-4e17-b6ca-c7f668023888", "bc69d222-3e7d-4921-b915-547dab7c5e99", "05b1f1a8-bd7a-4f37-a237-c8ee1b888d4f", "507ea432-2050-40eb-b732-dfbabc19764d", "8ffa644f-c660-46c6-991a-db8ffe9bf95a"]}]}, {"BlockType": "WORD", "Confidence": 92.23353576660156, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10540323704481125, "Height": 0.04917736351490021, "Left": 0.5896637439727783, "Top": 0.07072446495294571}, "Polygon": [{"X": 0.5896651148796082, "Y": 0.07072446495294571}, {"X": 0.6950669884681702, "Y": 0.07073942571878433}, {"X": 0.6950655579566956, "Y": 0.11990182846784592}, {"X": 0.5896637439727783, "Y": 0.11988688260316849}]}, "Id": "6c660b7b-5afb-4d9c-8370-f47afb9a8f4d"}, {"BlockType": "WORD", "Confidence": 97.52531433105469, "Text": "7056563", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.19257330894470215, "Height": 0.060884229838848114, "Left": 0.7023028135299683, "Top": 0.07608922570943832}, "Polygon": [{"X": 0.7023046016693115, "Y": 0.07608922570943832}, {"X": 0.8948761224746704, "Y": 0.07611655443906784}, {"X": 0.8948742151260376, "Y": 0.13697345554828644}, {"X": 0.7023028135299683, "Y": 0.1369461566209793}]}, "Id": "08363343-525d-4502-88ea-80e89748e3e8"}, {"BlockType": "WORD", "Confidence": 20.76201820373535, "Text": "AMBVD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22545401751995087, "Height": 0.03536486625671387, "Left": 0.4016489088535309, "Top": 0.1331961750984192}, "Polygon": [{"X": 0.4016498625278473, "Y": 0.1331961750984192}, {"X": 0.6271029114723206, "Y": 0.13322813808918}, {"X": 0.6271018981933594, "Y": 0.16856104135513306}, {"X": 0.4016489088535309, "Y": 0.16852909326553345}]}, "Id": "e05ca002-867f-4fe3-bf7a-72853d33f8fd"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "SAWYER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18591628968715668, "Height": 0.023441141471266747, "Left": 0.300431489944458, "Top": 0.18134063482284546}, "Polygon": [{"X": 0.30043211579322815, "Y": 0.18134063482284546}, {"X": 0.4863477945327759, "Y": 0.18136698007583618}, {"X": 0.48634713888168335, "Y": 0.20478177070617676}, {"X": 0.300431489944458, "Y": 0.20475544035434723}]}, "Id": "e84f30e4-5e9e-472f-bbf5-39a58aa79eec"}, {"BlockType": "WORD", "Confidence": 93.95188903808594, "Text": "NURSERY.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2246761918067932, "Height": 0.02256779558956623, "Left": 0.5078124403953552, "Top": 0.18164871633052826}, "Polygon": [{"X": 0.5078130960464478, "Y": 0.18164871633052826}, {"X": 0.7324886322021484, "Y": 0.18168054521083832}, {"X": 0.7324879765510559, "Y": 0.20421651005744934}, {"X": 0.5078124403953552, "Y": 0.20418468117713928}]}, "Id": "7bf841b6-7cae-495f-82da-997c8f4180d3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Truckload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13653694093227386, "Height": 0.01950434409081936, "Left": 0.3518599271774292, "Top": 0.2139761745929718}, "Polygon": [{"X": 0.3518604636192322, "Y": 0.2139761745929718}, {"X": 0.48839688301086426, "Y": 0.21399550139904022}, {"X": 0.4883963465690613, "Y": 0.2334805130958557}, {"X": 0.3518599271774292, "Y": 0.2334611862897873}]}, "Id": "6ee1255f-7cb5-4aa8-8876-e63f5cdd002b"}, {"BlockType": "WORD", "Confidence": 99.67613983154297, "Text": "Check-in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1211833506822586, "Height": 0.019593311473727226, "Left": 0.4968012273311615, "Top": 0.21411915123462677}, "Polygon": [{"X": 0.4968017637729645, "Y": 0.21411915123462677}, {"X": 0.6179845929145813, "Y": 0.21413631737232208}, {"X": 0.6179839968681335, "Y": 0.23371246457099915}, {"X": 0.4968012273311615, "Y": 0.23369529843330383}]}, "Id": "c23de31d-bc3b-4b29-85cd-8bbdc467f9fc"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Form", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07053661346435547, "Height": 0.018371663987636566, "Left": 0.6264668107032776, "Top": 0.21539254486560822}, "Polygon": [{"X": 0.6264673471450806, "Y": 0.21539254486560822}, {"X": 0.6970034241676331, "Y": 0.2154025435447693}, {"X": 0.6970028877258301, "Y": 0.23376421630382538}, {"X": 0.6264668107032776, "Y": 0.2337542325258255}]}, "Id": "dc1b7703-3d8e-4963-929b-01617ac8a186"}, {"BlockType": "WORD", "Confidence": 99.29528045654297, "Text": "Clear-to-Pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1697853058576584, "Height": 0.023801911622285843, "Left": 0.37930452823638916, "Top": 0.24309197068214417}, "Polygon": [{"X": 0.3793051540851593, "Y": 0.24309197068214417}, {"X": 0.5490898489952087, "Y": 0.24311600625514984}, {"X": 0.5490891337394714, "Y": 0.2668938636779785}, {"X": 0.37930452823638916, "Y": 0.26686984300613403}]}, "Id": "fec66d73-7f1e-4e60-bb99-6ef245c81118"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Voucher", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11440407484769821, "Height": 0.018674347549676895, "Left": 0.5572763085365295, "Top": 0.24373221397399902}, "Polygon": [{"X": 0.5572768449783325, "Y": 0.24373221397399902}, {"X": 0.6716803908348083, "Y": 0.24374841153621674}, {"X": 0.6716798543930054, "Y": 0.2624065577983856}, {"X": 0.5572763085365295, "Y": 0.2623903751373291}]}, "Id": "891f6d99-a33d-4318-bb70-c3e0d7530365"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040198516100645065, "Height": 0.011720404028892517, "Left": 0.11647132784128189, "Top": 0.2858521640300751}, "Polygon": [{"X": 0.11647161841392517, "Y": 0.2858521640300751}, {"X": 0.15666984021663666, "Y": 0.2858578562736511}, {"X": 0.15666955709457397, "Y": 0.2975725829601288}, {"X": 0.11647132784128189, "Y": 0.29756689071655273}]}, "Id": "c33f19a9-babb-48a9-af9e-d0a48082e6d1"}, {"BlockType": "WORD", "Confidence": 95.14568328857422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010951509699225426, "Height": 0.011172923259437084, "Left": 0.1608695089817047, "Top": 0.2865135967731476}, "Polygon": [{"X": 0.1608697921037674, "Y": 0.2865135967731476}, {"X": 0.17182102799415588, "Y": 0.28651514649391174}, {"X": 0.1718207448720932, "Y": 0.29768651723861694}, {"X": 0.1608695089817047, "Y": 0.2976849675178528}]}, "Id": "5aab2ca3-b37d-4415-bfcb-b334608c3b0a"}, {"BlockType": "WORD", "Confidence": 99.33773040771484, "Text": "20081", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09039538353681564, "Height": 0.021692262962460518, "Left": 0.17203138768672943, "Top": 0.2814519703388214}, "Polygon": [{"X": 0.1720319390296936, "Y": 0.2814519703388214}, {"X": 0.2624267637729645, "Y": 0.28146475553512573}, {"X": 0.2624261975288391, "Y": 0.3031442165374756}, {"X": 0.17203138768672943, "Y": 0.30313143134117126}]}, "Id": "4d1abf96-9f01-469e-9c3b-8034a2397580"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Broker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05547386035323143, "Height": 0.011601464822888374, "Left": 0.5692568421363831, "Top": 0.286699503660202}, "Polygon": [{"X": 0.5692571401596069, "Y": 0.286699503660202}, {"X": 0.6247307062149048, "Y": 0.2867073714733124}, {"X": 0.6247303485870361, "Y": 0.29830098152160645}, {"X": 0.5692568421363831, "Y": 0.2982931435108185}]}, "Id": "bb9c2840-f0da-49ab-bbb3-139001df9bd5"}, {"BlockType": "WORD", "Confidence": 98.9674301147461, "Text": "PE", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07020014524459839, "Height": 0.03418971225619316, "Left": 0.6797472834587097, "Top": 0.2667059600353241}, "Polygon": [{"X": 0.6797482967376709, "Y": 0.2667059600353241}, {"X": 0.7499474287033081, "Y": 0.2667158842086792}, {"X": 0.7499464154243469, "Y": 0.30089566111564636}, {"X": 0.6797472834587097, "Y": 0.30088573694229126}]}, "Id": "99478d42-8361-4f73-968c-b653ff0523fa"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03751755878329277, "Height": 0.011081227101385593, "Left": 0.11656415462493896, "Top": 0.33742454648017883}, "Polygon": [{"X": 0.11656443029642105, "Y": 0.33742454648017883}, {"X": 0.15408171713352203, "Y": 0.33742985129356384}, {"X": 0.15408143401145935, "Y": 0.34850576519966125}, {"X": 0.11656415462493896, "Y": 0.34850046038627625}]}, "Id": "b01ec2d2-f509-425a-b4fa-4b59b3757467"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Processed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07984647899866104, "Height": 0.01142625417560339, "Left": 0.15821559727191925, "Top": 0.3372431993484497}, "Polygon": [{"X": 0.15821588039398193, "Y": 0.3372431993484497}, {"X": 0.2380620837211609, "Y": 0.33725449442863464}, {"X": 0.238061785697937, "Y": 0.3486694395542145}, {"X": 0.15821559727191925, "Y": 0.34865817427635193}]}, "Id": "1971727c-29e7-4b70-a989-6c03749b7b77"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "5/26/2023", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16433002054691315, "Height": 0.027737313881516457, "Left": 0.25159332156181335, "Top": 0.3263999819755554}, "Polygon": [{"X": 0.25159403681755066, "Y": 0.3263999819755554}, {"X": 0.4159233570098877, "Y": 0.32642319798469543}, {"X": 0.415922611951828, "Y": 0.3541373014450073}, {"X": 0.25159332156181335, "Y": 0.3541140556335449}]}, "Id": "99372cc0-e16b-42cc-9160-3317514acf44"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04009363427758217, "Height": 0.01228137407451868, "Left": 0.5735878944396973, "Top": 0.33741599321365356}, "Polygon": [{"X": 0.5735882520675659, "Y": 0.33741599321365356}, {"X": 0.6136815547943115, "Y": 0.3374216556549072}, {"X": 0.6136811971664429, "Y": 0.3496973514556885}, {"X": 0.5735878944396973, "Y": 0.3496916890144348}]}, "Id": "1a23af7f-6a5a-4e85-9115-3546a1ab5068"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036674950271844864, "Height": 0.011521099135279655, "Left": 0.6189399361610413, "Top": 0.33795201778411865}, "Polygon": [{"X": 0.6189402937889099, "Y": 0.33795201778411865}, {"X": 0.6556149125099182, "Y": 0.3379572033882141}, {"X": 0.6556145548820496, "Y": 0.34947311878204346}, {"X": 0.6189399361610413, "Y": 0.349467933177948}]}, "Id": "4d4f896f-97c2-4960-a0b2-10e3215d6da1"}, {"BlockType": "WORD", "Confidence": 80.361328125, "Text": "$1,811.50", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.15128836035728455, "Height": 0.03032420203089714, "Left": 0.6841892004013062, "Top": 0.3301638662815094}, "Polygon": [{"X": 0.6841900944709778, "Y": 0.3301638662815094}, {"X": 0.8354775905609131, "Y": 0.33018526434898376}, {"X": 0.8354766368865967, "Y": 0.36048805713653564}, {"X": 0.6841892004013062, "Y": 0.36046668887138367}]}, "Id": "7b6df8d2-2112-4a5b-95dd-ac18942e64e3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048991139978170395, "Height": 0.012163417413830757, "Left": 0.12102002650499344, "Top": 0.3709349036216736}, "Polygon": [{"X": 0.12102033197879791, "Y": 0.3709349036216736}, {"X": 0.17001116275787354, "Y": 0.37094181776046753}, {"X": 0.17001086473464966, "Y": 0.383098304271698}, {"X": 0.12102002650499344, "Y": 0.38309139013290405}]}, "Id": "3f069e46-23d5-4453-9688-5c8e2f75bea8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Returned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07255759090185165, "Height": 0.012376274913549423, "Left": 0.17482176423072815, "Top": 0.37095099687576294}, "Polygon": [{"X": 0.17482207715511322, "Y": 0.37095099687576294}, {"X": 0.2473793625831604, "Y": 0.3709612488746643}, {"X": 0.24737903475761414, "Y": 0.38332727551460266}, {"X": 0.17482176423072815, "Y": 0.3833170235157013}]}, "Id": "4f8128f4-bf60-4e6b-8677-c7380f0db681"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "58", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05173654481768608, "Height": 0.019099539145827293, "Left": 0.29198774695396423, "Top": 0.36621248722076416}, "Polygon": [{"X": 0.29198822379112244, "Y": 0.36621248722076416}, {"X": 0.3437242805957794, "Y": 0.36621978878974915}, {"X": 0.34372377395629883, "Y": 0.385312020778656}, {"X": 0.29198774695396423, "Y": 0.385304719209671}]}, "Id": "30391b00-f2f5-4ef8-8eb8-1a9bc4bf399a"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Surcharge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08112683147192001, "Height": 0.015305086970329285, "Left": 0.5784105658531189, "Top": 0.38883665204048157}, "Polygon": [{"X": 0.5784109830856323, "Y": 0.38883665204048157}, {"X": 0.6595373749732971, "Y": 0.38884812593460083}, {"X": 0.6595369577407837, "Y": 0.40414172410964966}, {"X": 0.5784105658531189, "Y": 0.4041302800178528}]}, "Id": "f882b5bf-59f2-411d-abe2-f0e09000620f"}, {"BlockType": "WORD", "Confidence": 96.56409454345703, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010465393774211407, "Height": 0.008246120996773243, "Left": 0.6688497066497803, "Top": 0.3911568522453308}, "Polygon": [{"X": 0.6688500046730042, "Y": 0.3911568522453308}, {"X": 0.6793151497840881, "Y": 0.3911583125591278}, {"X": 0.679314911365509, "Y": 0.3994029760360718}, {"X": 0.6688497066497803, "Y": 0.3994014859199524}]}, "Id": "b05ad6f4-240a-416c-b9db-2eebf8c787cd"}, {"BlockType": "WORD", "Confidence": 99.68669891357422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010881456546485424, "Height": 0.013133440166711807, "Left": 0.6889501214027405, "Top": 0.3886982798576355}, "Polygon": [{"X": 0.6889504790306091, "Y": 0.3886982798576355}, {"X": 0.6998315453529358, "Y": 0.38869979977607727}, {"X": 0.6998311877250671, "Y": 0.401831716299057}, {"X": 0.6889501214027405, "Y": 0.40183016657829285}]}, "Id": "afbc9227-3ecc-4d3e-9f13-48a978a8749b"}, {"BlockType": "WORD", "Confidence": 82.40931701660156, "Text": "/", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03604384511709213, "Height": 0.007077441085129976, "Left": 0.739342212677002, "Top": 0.3872559368610382}, "Polygon": [{"X": 0.7753860354423523, "Y": 0.3872610330581665}, {"X": 0.775385856628418, "Y": 0.3943333923816681}, {"X": 0.739342212677002, "Y": 0.3943282961845398}, {"X": 0.739342451095581, "Y": 0.3872559368610382}]}, "Id": "76cb8390-63fd-4179-b929-c25bf08ae66d"}, {"BlockType": "WORD", "Confidence": 96.18403625488281, "Text": "Deductions -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10682931542396545, "Height": 0.012188736349344254, "Left": 0.5792409181594849, "Top": 0.42345160245895386}, "Polygon": [{"X": 0.5792412757873535, "Y": 0.42345160245895386}, {"X": 0.6860702037811279, "Y": 0.42346668243408203}, {"X": 0.6860698461532593, "Y": 0.4356403350830078}, {"X": 0.5792409181594849, "Y": 0.43562525510787964}]}, "Id": "c387112f-a42d-445c-9524-93cd074f2f2b"}, {"BlockType": "WORD", "Confidence": 97.01351165771484, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010185823775827885, "Height": 0.01248537190258503, "Left": 0.6947786808013916, "Top": 0.4233599901199341}, "Polygon": [{"X": 0.6947790384292603, "Y": 0.4233599901199341}, {"X": 0.7049645185470581, "Y": 0.4233614206314087}, {"X": 0.7049641609191895, "Y": 0.43584534525871277}, {"X": 0.6947786808013916, "Y": 0.43584391474723816}]}, "Id": "68a8bf4b-e949-4ed2-81f7-f6b4e003fa90"}, {"BlockType": "WORD", "Confidence": 94.48660278320312, "Text": "\\", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.029602937400341034, "Height": 0.008931504562497139, "Left": 0.7403873801231384, "Top": 0.4204845726490021}, "Polygon": [{"X": 0.7699902653694153, "Y": 0.42048874497413635}, {"X": 0.7699900269508362, "Y": 0.42941606044769287}, {"X": 0.7403873801231384, "Y": 0.4294118881225586}, {"X": 0.7403876185417175, "Y": 0.4204845726490021}]}, "Id": "55874354-5fb9-4421-aceb-51ca1e5d45a4"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "58", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0425306037068367, "Height": 0.021793918684124947, "Left": 0.13731786608695984, "Top": 0.43402889370918274}, "Polygon": [{"X": 0.137318417429924, "Y": 0.43402889370918274}, {"X": 0.17984847724437714, "Y": 0.43403491377830505}, {"X": 0.17984792590141296, "Y": 0.45582282543182373}, {"X": 0.13731786608695984, "Y": 0.4558168053627014}]}, "Id": "f082f622-d142-464c-991e-22e4c56b40c9"}, {"BlockType": "WORD", "Confidence": 91.98819732666016, "Text": "XS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02960963547229767, "Height": 0.012119299732148647, "Left": 0.20099274814128876, "Top": 0.4401569366455078}, "Polygon": [{"X": 0.20099306106567383, "Y": 0.4401569366455078}, {"X": 0.23060238361358643, "Y": 0.4401611089706421}, {"X": 0.23060207068920135, "Y": 0.45227622985839844}, {"X": 0.20099274814128876, "Y": 0.45227205753326416}]}, "Id": "76e63b6c-18d3-4b93-ace8-eb5653fd3503"}, {"BlockType": "WORD", "Confidence": 77.44239807128906, "Text": "4.-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05556721240282059, "Height": 0.022199831902980804, "Left": 0.24093540012836456, "Top": 0.4315095543861389}, "Polygon": [{"X": 0.24093596637248993, "Y": 0.4315095543861389}, {"X": 0.29650261998176575, "Y": 0.43151742219924927}, {"X": 0.296502023935318, "Y": 0.4537093937397003}, {"X": 0.24093540012836456, "Y": 0.45370155572891235}]}, "Id": "d20ad11f-792b-43e5-8d1c-6b5be7b9acbd"}, {"BlockType": "WORD", "Confidence": 95.53472137451172, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011496406979858875, "Height": 0.00488631846383214, "Left": 0.3447898030281067, "Top": 0.4437050521373749}, "Polygon": [{"X": 0.34478992223739624, "Y": 0.4437050521373749}, {"X": 0.3562861979007721, "Y": 0.4437066614627838}, {"X": 0.35628607869148254, "Y": 0.4485913813114166}, {"X": 0.3447898030281067, "Y": 0.4485897421836853}]}, "Id": "a2a71510-e6a6-4774-a961-b69db0b66383"}, {"BlockType": "WORD", "Confidence": 70.3896255493164, "Text": "$232,-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11089339852333069, "Height": 0.020582130178809166, "Left": 0.36117810010910034, "Top": 0.43400660157203674}, "Polygon": [{"X": 0.3611786663532257, "Y": 0.43400660157203674}, {"X": 0.47207149863243103, "Y": 0.43402227759361267}, {"X": 0.47207093238830566, "Y": 0.45458874106407166}, {"X": 0.36117810010910034, "Y": 0.4545730948448181}]}, "Id": "60b0eeec-a906-4f8b-be6b-7385bb0ecbf4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RACKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0659169927239418, "Height": 0.011822321452200413, "Left": 0.11665183305740356, "Top": 0.45716094970703125}, "Polygon": [{"X": 0.11665212363004684, "Y": 0.45716094970703125}, {"X": 0.18256881833076477, "Y": 0.4571702480316162}, {"X": 0.1825685203075409, "Y": 0.4689832925796509}, {"X": 0.11665183305740356, "Y": 0.46897396445274353}]}, "Id": "26dd7677-0d8d-4a02-8337-7503e6e331bf"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AMOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08708254992961884, "Height": 0.01104178186506033, "Left": 0.23298074305057526, "Top": 0.4578242897987366}, "Polygon": [{"X": 0.23298102617263794, "Y": 0.4578242897987366}, {"X": 0.3200632929801941, "Y": 0.4578365683555603}, {"X": 0.3200629949569702, "Y": 0.4688660502433777}, {"X": 0.23298074305057526, "Y": 0.46885377168655396}]}, "Id": "28117a60-2f4f-4123-acfe-844e08930599"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06492549180984497, "Height": 0.011266661807894707, "Left": 0.3694784939289093, "Top": 0.45779627561569214}, "Polygon": [{"X": 0.3694787919521332, "Y": 0.45779627561569214}, {"X": 0.4344039857387543, "Y": 0.45780545473098755}, {"X": 0.4344036877155304, "Y": 0.4690629541873932}, {"X": 0.3694784939289093, "Y": 0.4690537750720978}]}, "Id": "a880ae8a-02a0-4c44-8fc2-0549c8adb6c1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04903475567698479, "Height": 0.011652568355202675, "Left": 0.5825256109237671, "Top": 0.4748230576515198}, "Polygon": [{"X": 0.582525908946991, "Y": 0.4748230576515198}, {"X": 0.6315603256225586, "Y": 0.4748299717903137}, {"X": 0.6315600275993347, "Y": 0.4864756166934967}, {"X": 0.5825256109237671, "Y": 0.48646870255470276}]}, "Id": "dd649200-8311-45a2-84d0-43b736d1720e"}, {"BlockType": "WORD", "Confidence": 86.97195434570312, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010675839148461819, "Height": 0.0072976225055754185, "Left": 0.685575544834137, "Top": 0.47717827558517456}, "Polygon": [{"X": 0.6855757236480713, "Y": 0.47717827558517456}, {"X": 0.696251392364502, "Y": 0.47717976570129395}, {"X": 0.6962511539459229, "Y": 0.48447591066360474}, {"X": 0.685575544834137, "Y": 0.48447439074516296}]}, "Id": "048e9443-46a1-495a-a477-3bb972fa3eaf"}, {"BlockType": "WORD", "Confidence": 37.43602752685547, "Text": "$232,-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13124403357505798, "Height": 0.020313480868935585, "Left": 0.6951780915260315, "Top": 0.4697116017341614}, "Polygon": [{"X": 0.6951786875724792, "Y": 0.4697116017341614}, {"X": 0.8264221549034119, "Y": 0.46973010897636414}, {"X": 0.8264215588569641, "Y": 0.4900250732898712}, {"X": 0.6951780915260315, "Y": 0.49000656604766846}]}, "Id": "ba65b9e8-2ea4-4428-bced-157cb6718b8f"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "(<PERSON>ck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04753455892205238, "Height": 0.014282872900366783, "Left": 0.5824187994003296, "Top": 0.4914499819278717}, "Polygon": [{"X": 0.5824191570281982, "Y": 0.4914499819278717}, {"X": 0.6299533247947693, "Y": 0.49145668745040894}, {"X": 0.6299529075622559, "Y": 0.5057328343391418}, {"X": 0.5824187994003296, "Y": 0.505726158618927}]}, "Id": "a7581844-68c3-47a5-8750-5f2dc9628d12"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05475565418601036, "Height": 0.012177536264061928, "Left": 0.6342670917510986, "Top": 0.49162039160728455}, "Polygon": [{"X": 0.6342674493789673, "Y": 0.49162039160728455}, {"X": 0.6890227794647217, "Y": 0.49162811040878296}, {"X": 0.689022421836853, "Y": 0.5037978887557983}, {"X": 0.6342670917510986, "Y": 0.5037901997566223}]}, "Id": "e8872f30-7484-487e-9f60-50ced91fad87"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03135434165596962, "Height": 0.01193633209913969, "Left": 0.6933801770210266, "Top": 0.4916652739048004}, "Polygon": [{"X": 0.6933805346488953, "Y": 0.4916652739048004}, {"X": 0.7247345447540283, "Y": 0.4916696846485138}, {"X": 0.7247341871261597, "Y": 0.503601610660553}, {"X": 0.6933801770210266, "Y": 0.5035971999168396}]}, "Id": "fbb7321e-96cf-4429-bdad-ffa66409e0ca"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "now", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03455721214413643, "Height": 0.008605338633060455, "Left": 0.7295389175415039, "Top": 0.49499258399009705}, "Polygon": [{"X": 0.7295392155647278, "Y": 0.49499258399009705}, {"X": 0.7640961408615112, "Y": 0.49499744176864624}, {"X": 0.7640959024429321, "Y": 0.5035979151725769}, {"X": 0.7295389175415039, "Y": 0.5035930275917053}]}, "Id": "be840eb9-462d-485d-afa0-f039d3bc8a65"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019423341378569603, "Height": 0.011654733680188656, "Left": 0.7689269781112671, "Top": 0.4918619394302368}, "Polygon": [{"X": 0.7689273357391357, "Y": 0.4918619394302368}, {"X": 0.7883503437042236, "Y": 0.4918646812438965}, {"X": 0.788349986076355, "Y": 0.503516674041748}, {"X": 0.7689269781112671, "Y": 0.5035139322280884}]}, "Id": "3171a1cd-acaf-4db3-8d5a-6b2a2fbf2f09"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032880451530218124, "Height": 0.01037353090941906, "Left": 0.7925770878791809, "Top": 0.4934443533420563}, "Polygon": [{"X": 0.7925773859024048, "Y": 0.4934443533420563}, {"X": 0.8254575729370117, "Y": 0.49344900250434875}, {"X": 0.8254572153091431, "Y": 0.5038179159164429}, {"X": 0.7925770878791809, "Y": 0.5038132667541504}]}, "Id": "28045193-7e19-49b8-a6da-403472ad5d8f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01619839482009411, "Height": 0.009988473728299141, "Left": 0.829407811164856, "Top": 0.4936181306838989}, "Polygon": [{"X": 0.8294081091880798, "Y": 0.4936181306838989}, {"X": 0.8456062078475952, "Y": 0.4936204254627228}, {"X": 0.8456059098243713, "Y": 0.5036066174507141}, {"X": 0.829407811164856, "Y": 0.5036042928695679}]}, "Id": "1520418f-99b5-466e-a599-c6e3aa66dc8b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025166315957903862, "Height": 0.01152397133409977, "Left": 0.8498008251190186, "Top": 0.4920193552970886}, "Polygon": [{"X": 0.8498011827468872, "Y": 0.4920193552970886}, {"X": 0.874967098236084, "Y": 0.49202290177345276}, {"X": 0.8749667406082153, "Y": 0.5035433173179626}, {"X": 0.8498008251190186, "Y": 0.5035398006439209}]}, "Id": "1d960130-31e0-45fe-a4d9-42e5908856e2"}, {"BlockType": "WORD", "Confidence": 99.48262023925781, "Text": "broker.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06332949548959732, "Height": 0.01425098069012165, "Left": 0.5937391519546509, "Top": 0.5080398917198181}, "Polygon": [{"X": 0.5937395691871643, "Y": 0.5080398917198181}, {"X": 0.65706866979599, "Y": 0.5080488324165344}, {"X": 0.6570682525634766, "Y": 0.5222908854484558}, {"X": 0.5937391519546509, "Y": 0.5222819447517395}]}, "Id": "848ad0d6-1c97-4281-9ebf-20a6b40cbc12"}, {"BlockType": "WORD", "Confidence": 98.69519805908203, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029091598466038704, "Height": 0.011797674931585789, "Left": 0.5835147500038147, "Top": 0.5428829789161682}, "Polygon": [{"X": 0.5835151076316833, "Y": 0.5428829789161682}, {"X": 0.6126063466072083, "Y": 0.5428870916366577}, {"X": 0.6126060485839844, "Y": 0.5546807050704956}, {"X": 0.5835147500038147, "Y": 0.5546765923500061}]}, "Id": "cdd03a89-b312-46b0-8fd7-4942337bbe95"}, {"BlockType": "WORD", "Confidence": 99.64764404296875, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03693893551826477, "Height": 0.011645348742604256, "Left": 0.6172640919685364, "Top": 0.5431277751922607}, "Polygon": [{"X": 0.617264449596405, "Y": 0.5431277751922607}, {"X": 0.6542030572891235, "Y": 0.543133020401001}, {"X": 0.6542026996612549, "Y": 0.5547731518745422}, {"X": 0.6172640919685364, "Y": 0.554767906665802}]}, "Id": "23749880-7004-4f19-b60e-3a561ac97c7e"}, {"BlockType": "WORD", "Confidence": 95.02890014648438, "Text": "$2,043.50", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17840102314949036, "Height": 0.026044920086860657, "Left": 0.6595098376274109, "Top": 0.5375681519508362}, "Polygon": [{"X": 0.659510612487793, "Y": 0.5375681519508362}, {"X": 0.8379108905792236, "Y": 0.5375933051109314}, {"X": 0.8379100561141968, "Y": 0.5636130571365356}, {"X": 0.6595098376274109, "Y": 0.5635879635810852}]}, "Id": "cebde096-3583-420b-bd31-e727da1000e7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10409253090620041, "Height": 0.01852380484342575, "Left": 0.11781433969736099, "Top": 0.5796347856521606}, "Polygon": [{"X": 0.1178147941827774, "Y": 0.5796347856521606}, {"X": 0.2219068706035614, "Y": 0.5796494483947754}, {"X": 0.2219063937664032, "Y": 0.598158597946167}, {"X": 0.11781433969736099, "Y": 0.5981439352035522}]}, "Id": "cf39cb8c-e235-4e9a-87e8-e167efd4e250"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Shortages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1369275450706482, "Height": 0.022716624662280083, "Left": 0.23155957460403442, "Top": 0.5796909928321838}, "Polygon": [{"X": 0.23156015574932098, "Y": 0.5796909928321838}, {"X": 0.3684871196746826, "Y": 0.5797103047370911}, {"X": 0.36848652362823486, "Y": 0.6024076342582703}, {"X": 0.23155957460403442, "Y": 0.602388322353363}]}, "Id": "831d9fba-6ec4-495a-8704-47ff1c7bdef7"}, {"BlockType": "WORD", "Confidence": 79.84156036376953, "Text": "requipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06127878651022911, "Height": 0.009871997870504856, "Left": 0.11641670018434525, "Top": 0.6048676371574402}, "Polygon": [{"X": 0.11641694605350494, "Y": 0.6048676371574402}, {"X": 0.17769548296928406, "Y": 0.6048762202262878}, {"X": 0.17769522964954376, "Y": 0.6147395968437195}, {"X": 0.11641670018434525, "Y": 0.6147310137748718}]}, "Id": "93f24196-60b8-4a9f-8340-f5a312b019a6"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "missing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04202340543270111, "Height": 0.010577251203358173, "Left": 0.18039287626743317, "Top": 0.6040776968002319}, "Polygon": [{"X": 0.18039314448833466, "Y": 0.6040776968002319}, {"X": 0.22241628170013428, "Y": 0.6040836572647095}, {"X": 0.2224159985780716, "Y": 0.6146549582481384}, {"X": 0.18039287626743317, "Y": 0.6146490573883057}]}, "Id": "46302429-a199-4afb-a58c-ee5b29d04c8a"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024317173287272453, "Height": 0.008349860087037086, "Left": 0.22517673671245575, "Top": 0.6040480732917786}, "Polygon": [{"X": 0.22517696022987366, "Y": 0.6040480732917786}, {"X": 0.24949391186237335, "Y": 0.6040515303611755}, {"X": 0.24949370324611664, "Y": 0.6123979687690735}, {"X": 0.22517673671245575, "Y": 0.6123945116996765}]}, "Id": "a5c4410d-1bdf-4a9b-b5f9-3aee177aa513"}, {"BlockType": "WORD", "Confidence": 96.652587890625, "Text": "insufficient", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060892704874277115, "Height": 0.008753020316362381, "Left": 0.2520337700843811, "Top": 0.6037749648094177}, "Polygon": [{"X": 0.2520339787006378, "Y": 0.6037749648094177}, {"X": 0.3129264712333679, "Y": 0.6037835478782654}, {"X": 0.3129262328147888, "Y": 0.6125279664993286}, {"X": 0.2520337700843811, "Y": 0.612519383430481}]}, "Id": "9cce9b34-81d3-469a-85b7-3b0e072083eb"}, {"BlockType": "WORD", "Confidence": 63.48931884765625, "Text": "documentation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08533047884702682, "Height": 0.008737897500395775, "Left": 0.31525692343711853, "Top": 0.6037442684173584}, "Polygon": [{"X": 0.31525716185569763, "Y": 0.6037442684173584}, {"X": 0.40058740973472595, "Y": 0.6037562489509583}, {"X": 0.40058717131614685, "Y": 0.6124821305274963}, {"X": 0.31525692343711853, "Y": 0.6124701499938965}]}, "Id": "67318b5d-fb06-4a4d-a6bd-b3490bf9be04"}, {"BlockType": "WORD", "Confidence": 98.87496185302734, "Text": "Racks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053650036454200745, "Height": 0.011379420757293701, "Left": 0.11733164638280869, "Top": 0.6286805868148804}, "Polygon": [{"X": 0.11733192950487137, "Y": 0.6286805868148804}, {"X": 0.17098169028759003, "Y": 0.6286881566047668}, {"X": 0.17098139226436615, "Y": 0.6400600075721741}, {"X": 0.11733164638280869, "Y": 0.6400524377822876}]}, "Id": "4f6f0d1a-d8ff-4b89-836b-5cf6397c820a"}, {"BlockType": "WORD", "Confidence": 89.15995788574219, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005627519451081753, "Height": 0.012094262056052685, "Left": 0.17592543363571167, "Top": 0.6299233436584473}, "Polygon": [{"X": 0.17592574656009674, "Y": 0.6299233436584473}, {"X": 0.1815529614686966, "Y": 0.6299241781234741}, {"X": 0.18155264854431152, "Y": 0.6420176029205322}, {"X": 0.17592543363571167, "Y": 0.6420168280601501}]}, "Id": "df3da0f0-2c34-43a1-83a8-30476fc14078"}, {"BlockType": "WORD", "Confidence": 96.41282653808594, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005820860154926777, "Height": 0.012711962684988976, "Left": 0.19829022884368896, "Top": 0.6293660998344421}, "Polygon": [{"X": 0.19829055666923523, "Y": 0.6293660998344421}, {"X": 0.20411109924316406, "Y": 0.629366934299469}, {"X": 0.2041107714176178, "Y": 0.6420781016349792}, {"X": 0.19829022884368896, "Y": 0.6420772671699524}]}, "Id": "5d083a2a-c264-488c-b97b-b1944f21c6cb"}, {"BlockType": "WORD", "Confidence": 92.89601135253906, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009872401133179665, "Height": 0.00855213962495327, "Left": 0.20914724469184875, "Top": 0.631386935710907}, "Polygon": [{"X": 0.20914746820926666, "Y": 0.631386935710907}, {"X": 0.21901965141296387, "Y": 0.6313883066177368}, {"X": 0.21901942789554596, "Y": 0.6399390697479248}, {"X": 0.20914724469184875, "Y": 0.639937698841095}]}, "Id": "b95f691b-a673-4a91-a054-f84a0bd700cc"}, {"BlockType": "WORD", "Confidence": 98.6328125, "Text": "$250.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06593728065490723, "Height": 0.01258140429854393, "Left": 0.2237004041671753, "Top": 0.628320574760437}, "Polygon": [{"X": 0.22370073199272156, "Y": 0.628320574760437}, {"X": 0.2896376848220825, "Y": 0.6283298134803772}, {"X": 0.28963735699653625, "Y": 0.6409019827842712}, {"X": 0.2237004041671753, "Y": 0.6408926844596863}]}, "Id": "81c2fd37-fb89-4139-afcc-6ae3d76f34b7"}, {"BlockType": "WORD", "Confidence": 88.62962341308594, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010121982544660568, "Height": 0.012218808755278587, "Left": 0.30971604585647583, "Top": 0.6285836696624756}, "Polygon": [{"X": 0.3097163438796997, "Y": 0.6285836696624756}, {"X": 0.3198380172252655, "Y": 0.6285851001739502}, {"X": 0.31983768939971924, "Y": 0.6408025026321411}, {"X": 0.30971604585647583, "Y": 0.6408010721206665}]}, "Id": "28fca8bd-a581-49cb-82f1-b04762f6c4bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04240143671631813, "Height": 0.011343386024236679, "Left": 0.6024941802024841, "Top": 0.6285437345504761}, "Polygon": [{"X": 0.602494478225708, "Y": 0.6285437345504761}, {"X": 0.644895613193512, "Y": 0.6285496950149536}, {"X": 0.6448952555656433, "Y": 0.6398870944976807}, {"X": 0.6024941802024841, "Y": 0.6398811340332031}]}, "Id": "81cfb714-956b-45e1-93b2-97f2f6f1638b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09105204045772552, "Height": 0.011598109267652035, "Left": 0.6499117612838745, "Top": 0.6283674836158752}, "Polygon": [{"X": 0.6499120593070984, "Y": 0.6283674836158752}, {"X": 0.7409638166427612, "Y": 0.628380298614502}, {"X": 0.7409634590148926, "Y": 0.6399655938148499}, {"X": 0.6499117612838745, "Y": 0.6399527788162231}]}, "Id": "73c3f0e9-13e6-4e00-9f38-a3f10f101f95"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027909638360142708, "Height": 0.011229475028812885, "Left": 0.6047047972679138, "Top": 0.645686686038971}, "Polygon": [{"X": 0.6047050952911377, "Y": 0.645686686038971}, {"X": 0.6326144337654114, "Y": 0.6456906199455261}, {"X": 0.6326140761375427, "Y": 0.6569162011146545}, {"X": 0.6047047972679138, "Y": 0.6569122672080994}]}, "Id": "d48de1e9-3e24-4547-b72c-2f22686d5ee1"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "Load:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04565737023949623, "Height": 0.011421551927924156, "Left": 0.63669753074646, "Top": 0.6456255912780762}, "Polygon": [{"X": 0.6366978287696838, "Y": 0.6456255912780762}, {"X": 0.6823548674583435, "Y": 0.6456320285797119}, {"X": 0.6823545694351196, "Y": 0.6570471525192261}, {"X": 0.63669753074646, "Y": 0.6570407152175903}]}, "Id": "df2044fa-31bc-440d-acc5-3002de0f2d71"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040737736970186234, "Height": 0.011813991703093052, "Left": 0.11747674643993378, "Top": 0.6623064875602722}, "Polygon": [{"X": 0.11747703701257706, "Y": 0.6623064875602722}, {"X": 0.1582144796848297, "Y": 0.6623122692108154}, {"X": 0.15821418166160583, "Y": 0.6741204857826233}, {"X": 0.11747674643993378, "Y": 0.6741147637367249}]}, "Id": "eda33a2b-7b49-43ed-88dd-64f4d65a93de"}, {"BlockType": "WORD", "Confidence": 98.79524230957031, "Text": "Locks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0539955273270607, "Height": 0.012356875464320183, "Left": 0.16298943758010864, "Top": 0.662104070186615}, "Polygon": [{"X": 0.16298973560333252, "Y": 0.662104070186615}, {"X": 0.21698495745658875, "Y": 0.6621116995811462}, {"X": 0.21698464453220367, "Y": 0.6744609475135803}, {"X": 0.16298943758010864, "Y": 0.6744533777236938}]}, "Id": "b2b54748-91a7-4ee5-ac7c-19f2166be3fd"}, {"BlockType": "WORD", "Confidence": 76.20376586914062, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027942171320319176, "Height": 0.014829600229859352, "Left": 0.22228194773197174, "Top": 0.6617045998573303}, "Polygon": [{"X": 0.2222823202610016, "Y": 0.6617045998573303}, {"X": 0.25022411346435547, "Y": 0.6617085337638855}, {"X": 0.25022372603416443, "Y": 0.6765342354774475}, {"X": 0.22228194773197174, "Y": 0.6765303015708923}]}, "Id": "cebd77ca-2f9c-4460-ab9c-7ed70f40b836"}, {"BlockType": "WORD", "Confidence": 79.0710678100586, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009297792799770832, "Height": 0.008624798618257046, "Left": 0.255423367023468, "Top": 0.6659112572669983}, "Polygon": [{"X": 0.25542357563972473, "Y": 0.6659112572669983}, {"X": 0.264721155166626, "Y": 0.6659125685691833}, {"X": 0.2647209167480469, "Y": 0.6745360493659973}, {"X": 0.255423367023468, "Y": 0.6745347380638123}]}, "Id": "2212a1ff-e206-4b78-9868-7b1b778d09f1"}, {"BlockType": "WORD", "Confidence": 96.89891052246094, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06112426146864891, "Height": 0.01323897484689951, "Left": 0.2642442286014557, "Top": 0.6620985269546509}, "Polygon": [{"X": 0.26424455642700195, "Y": 0.6620985269546509}, {"X": 0.3253684937953949, "Y": 0.6621071100234985}, {"X": 0.32536813616752625, "Y": 0.6753374934196472}, {"X": 0.2642442286014557, "Y": 0.6753289103507996}]}, "Id": "84fd2dbd-a00f-4733-8314-594ee4d7bdb5"}, {"BlockType": "WORD", "Confidence": 97.50338745117188, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01133705209940672, "Height": 0.004824519623070955, "Left": 0.32997235655784607, "Top": 0.6657844185829163}, "Polygon": [{"X": 0.329972505569458, "Y": 0.6657844185829163}, {"X": 0.34130942821502686, "Y": 0.6657860279083252}, {"X": 0.3413092792034149, "Y": 0.6706089377403259}, {"X": 0.32997235655784607, "Y": 0.6706073880195618}]}, "Id": "238e40b4-831d-479d-ac5c-42b1f672fca0"}, {"BlockType": "WORD", "Confidence": 74.3895492553711, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00997991394251585, "Height": 0.012337488122284412, "Left": 0.3460158705711365, "Top": 0.6621382832527161}, "Polygon": [{"X": 0.34601619839668274, "Y": 0.6621382832527161}, {"X": 0.3559958040714264, "Y": 0.6621396541595459}, {"X": 0.3559954762458801, "Y": 0.6744757294654846}, {"X": 0.3460158705711365, "Y": 0.6744743585586548}]}, "Id": "81eca921-f118-4fbe-914b-b9e8f91f42b9"}, {"BlockType": "WORD", "Confidence": 95.77567291259766, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010098937898874283, "Height": 0.01212747860699892, "Left": 0.603948712348938, "Top": 0.6791166067123413}, "Polygon": [{"X": 0.6039490699768066, "Y": 0.6791166067123413}, {"X": 0.614047646522522, "Y": 0.6791180372238159}, {"X": 0.6140472888946533, "Y": 0.6912440657615662}, {"X": 0.603948712348938, "Y": 0.6912426352500916}]}, "Id": "354b61cd-1184-4686-b1f0-a9cb707b8fd7"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044669851660728455, "Height": 0.01153523288667202, "Left": 0.11812970787286758, "Top": 0.6964622735977173}, "Polygon": [{"X": 0.11812999844551086, "Y": 0.6964622735977173}, {"X": 0.16279956698417664, "Y": 0.6964685916900635}, {"X": 0.16279926896095276, "Y": 0.7079975008964539}, {"X": 0.11812970787286758, "Y": 0.7079912424087524}]}, "Id": "2911ebe4-134d-4b03-9e7a-4db9aa241233"}, {"BlockType": "WORD", "Confidence": 99.52327728271484, "Text": "<PERSON>:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040473248809576035, "Height": 0.011523894965648651, "Left": 0.1672496348619461, "Top": 0.6963143348693848}, "Polygon": [{"X": 0.1672499179840088, "Y": 0.6963143348693848}, {"X": 0.20772287249565125, "Y": 0.6963200569152832}, {"X": 0.20772258937358856, "Y": 0.707838237285614}, {"X": 0.1672496348619461, "Y": 0.7078325152397156}]}, "Id": "e091975f-0109-4b63-a815-e13c76c854f1"}, {"BlockType": "WORD", "Confidence": 93.00482177734375, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005708469543606043, "Height": 0.013021725229918957, "Left": 0.21340321004390717, "Top": 0.6972136497497559}, "Polygon": [{"X": 0.21340353786945343, "Y": 0.6972136497497559}, {"X": 0.21911168098449707, "Y": 0.6972144842147827}, {"X": 0.2191113382577896, "Y": 0.7102353572845459}, {"X": 0.21340321004390717, "Y": 0.7102345824241638}]}, "Id": "32fe4b32-c048-4ee6-bc7f-26feff0f5f18"}, {"BlockType": "WORD", "Confidence": 93.48473358154297, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006112797185778618, "Height": 0.012515691109001637, "Left": 0.23562274873256683, "Top": 0.6970536708831787}, "Polygon": [{"X": 0.2356230616569519, "Y": 0.6970536708831787}, {"X": 0.2417355477809906, "Y": 0.6970545649528503}, {"X": 0.24173521995544434, "Y": 0.7095693945884705}, {"X": 0.23562274873256683, "Y": 0.7095685601234436}]}, "Id": "ec548534-b274-4550-a6f2-34028b27d13e"}, {"BlockType": "WORD", "Confidence": 73.59195709228516, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009887147694826126, "Height": 0.008834282867610455, "Left": 0.2465316206216812, "Top": 0.6992959976196289}, "Polygon": [{"X": 0.24653184413909912, "Y": 0.6992959976196289}, {"X": 0.25641876459121704, "Y": 0.6992973685264587}, {"X": 0.25641852617263794, "Y": 0.7081303000450134}, {"X": 0.2465316206216812, "Y": 0.7081288695335388}]}, "Id": "fc12d067-5536-4450-b337-5eb1a0fdf9e5"}, {"BlockType": "WORD", "Confidence": 97.79874420166016, "Text": "$500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0658784806728363, "Height": 0.011710491962730885, "Left": 0.2612293064594269, "Top": 0.6966966390609741}, "Polygon": [{"X": 0.26122960448265076, "Y": 0.6966966390609741}, {"X": 0.3271077871322632, "Y": 0.6967058777809143}, {"X": 0.3271074891090393, "Y": 0.7084071040153503}, {"X": 0.2612293064594269, "Y": 0.7083978652954102}]}, "Id": "8fde49fd-2b78-454e-93ff-ae7806c112d2"}, {"BlockType": "WORD", "Confidence": 79.47225952148438, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010627293027937412, "Height": 0.003068731864914298, "Left": 0.3316441774368286, "Top": 0.6993433237075806}, "Polygon": [{"X": 0.3316442668437958, "Y": 0.6993433237075806}, {"X": 0.34227147698402405, "Y": 0.6993448138237}, {"X": 0.3422713875770569, "Y": 0.7024120688438416}, {"X": 0.3316441774368286, "Y": 0.7024105191230774}]}, "Id": "999faac8-5aa0-41be-bf71-1e4b538490e6"}, {"BlockType": "WORD", "Confidence": 97.68495178222656, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010039864107966423, "Height": 0.011877928860485554, "Left": 0.34704914689064026, "Top": 0.6964814066886902}, "Polygon": [{"X": 0.3470494747161865, "Y": 0.6964814066886902}, {"X": 0.35708901286125183, "Y": 0.69648277759552}, {"X": 0.35708868503570557, "Y": 0.7083593010902405}, {"X": 0.34704914689064026, "Y": 0.7083578705787659}]}, "Id": "3a23e8e0-0667-4020-9bc7-3f10f2833555"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04021437093615532, "Height": 0.011835435405373573, "Left": 0.11816546320915222, "Top": 0.7308557033538818}, "Polygon": [{"X": 0.1181657612323761, "Y": 0.7308557033538818}, {"X": 0.15837983787059784, "Y": 0.7308613657951355}, {"X": 0.15837953984737396, "Y": 0.7426910996437073}, {"X": 0.11816546320915222, "Y": 0.7426854968070984}]}, "Id": "9e3ac13f-b3f6-410e-9b62-d0e248f49cd2"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "Straps:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05481516197323799, "Height": 0.014726093038916588, "Left": 0.16341908276081085, "Top": 0.730798602104187}, "Polygon": [{"X": 0.1634194552898407, "Y": 0.730798602104187}, {"X": 0.21823424100875854, "Y": 0.7308063507080078}, {"X": 0.2182338684797287, "Y": 0.7455247044563293}, {"X": 0.16341908276081085, "Y": 0.7455170154571533}]}, "Id": "1570c908-933a-402d-a9b5-8bb6b2679a49"}, {"BlockType": "WORD", "Confidence": 91.17486572265625, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0055338847450912, "Height": 0.012058770284056664, "Left": 0.22372227907180786, "Top": 0.7318230867385864}, "Polygon": [{"X": 0.22372259199619293, "Y": 0.7318230867385864}, {"X": 0.22925616800785065, "Y": 0.7318239212036133}, {"X": 0.22925585508346558, "Y": 0.74388188123703}, {"X": 0.22372227907180786, "Y": 0.743881106376648}]}, "Id": "983e07ce-3e19-4f35-9b9e-208c51f82169"}, {"BlockType": "WORD", "Confidence": 91.30221557617188, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005707674194127321, "Height": 0.013324182480573654, "Left": 0.24576258659362793, "Top": 0.7314085364341736}, "Polygon": [{"X": 0.2457629293203354, "Y": 0.7314085364341736}, {"X": 0.25147026777267456, "Y": 0.7314093708992004}, {"X": 0.2514699101448059, "Y": 0.7447327375411987}, {"X": 0.24576258659362793, "Y": 0.7447319030761719}]}, "Id": "4999a2b6-e0ad-4840-8528-c573b974986d"}, {"BlockType": "WORD", "Confidence": 63.433311462402344, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010432935319840908, "Height": 0.008735757321119308, "Left": 0.2562576234340668, "Top": 0.7337411642074585}, "Polygon": [{"X": 0.2562578320503235, "Y": 0.7337411642074585}, {"X": 0.26669055223464966, "Y": 0.7337426543235779}, {"X": 0.26669031381607056, "Y": 0.7424769401550293}, {"X": 0.2562576234340668, "Y": 0.7424754500389099}]}, "Id": "e1155924-ed23-466d-ab07-408ef6a0da6c"}, {"BlockType": "WORD", "Confidence": 95.45659637451172, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05581032112240791, "Height": 0.012936838902533054, "Left": 0.2712685763835907, "Top": 0.7303600311279297}, "Polygon": [{"X": 0.27126890420913696, "Y": 0.7303600311279297}, {"X": 0.3270789086818695, "Y": 0.73036789894104}, {"X": 0.32707855105400085, "Y": 0.7432968616485596}, {"X": 0.2712685763835907, "Y": 0.743289053440094}]}, "Id": "77702a43-f291-4dbc-8921-badd8410b015"}, {"BlockType": "WORD", "Confidence": 97.31046295166016, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011988215148448944, "Height": 0.004923770669847727, "Left": 0.3310393989086151, "Top": 0.7340360879898071}, "Polygon": [{"X": 0.33103954792022705, "Y": 0.7340360879898071}, {"X": 0.34302762150764465, "Y": 0.7340377569198608}, {"X": 0.3430275022983551, "Y": 0.7389598488807678}, {"X": 0.3310393989086151, "Y": 0.7389581203460693}]}, "Id": "406aa1c3-7251-47dd-af57-99abab0b4859"}, {"BlockType": "WORD", "Confidence": 96.07382202148438, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009692336432635784, "Height": 0.012331448495388031, "Left": 0.3473632335662842, "Top": 0.7304210662841797}, "Polygon": [{"X": 0.34736356139183044, "Y": 0.7304210662841797}, {"X": 0.35705557465553284, "Y": 0.7304224371910095}, {"X": 0.3570552468299866, "Y": 0.7427524924278259}, {"X": 0.3473632335662842, "Y": 0.7427511811256409}]}, "Id": "9d71b919-ecca-4c84-bb36-3a39495d89ba"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "Paperwork", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08577918261289597, "Height": 0.014093016274273396, "Left": 0.11782500147819519, "Top": 0.7653647065162659}, "Polygon": [{"X": 0.11782535165548325, "Y": 0.7653647065162659}, {"X": 0.20360419154167175, "Y": 0.7653767466545105}, {"X": 0.2036038190126419, "Y": 0.7794577479362488}, {"X": 0.11782500147819519, "Y": 0.7794457077980042}]}, "Id": "3c188cff-fe2e-4fb3-a82a-8ee932e650c6"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015143593773245811, "Height": 0.01186202559620142, "Left": 0.2085617631673813, "Top": 0.7653040289878845}, "Polygon": [{"X": 0.20856206119060516, "Y": 0.7653040289878845}, {"X": 0.22370535135269165, "Y": 0.7653061747550964}, {"X": 0.22370505332946777, "Y": 0.7771660685539246}, {"X": 0.2085617631673813, "Y": 0.7771639227867126}]}, "Id": "1b70e2ac-cf6a-4a86-9681-c443c789a1ff"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "other", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04216981679201126, "Height": 0.012150295078754425, "Left": 0.22882036864757538, "Top": 0.7650332450866699}, "Polygon": [{"X": 0.22882068157196045, "Y": 0.7650332450866699}, {"X": 0.27099019289016724, "Y": 0.7650391459465027}, {"X": 0.27098986506462097, "Y": 0.7771835327148438}, {"X": 0.22882036864757538, "Y": 0.777177631855011}]}, "Id": "0a5b4df6-2f33-43fb-a3e1-f425da1c09ce"}, {"BlockType": "WORD", "Confidence": 99.37440490722656, "Text": "Fines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0433683805167675, "Height": 0.012176922522485256, "Left": 0.27519676089286804, "Top": 0.7650023102760315}, "Polygon": [{"X": 0.2751970589160919, "Y": 0.7650023102760315}, {"X": 0.31856513023376465, "Y": 0.7650083899497986}, {"X": 0.3185648024082184, "Y": 0.7771792411804199}, {"X": 0.27519676089286804, "Y": 0.7771731019020081}]}, "Id": "84a3da4a-d37b-467d-9114-5e458a77337f"}, {"BlockType": "WORD", "Confidence": 99.5655288696289, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010133316740393639, "Height": 0.012428064830601215, "Left": 0.32276543974876404, "Top": 0.7648627758026123}, "Polygon": [{"X": 0.3227657675743103, "Y": 0.7648627758026123}, {"X": 0.33289873600006104, "Y": 0.7648642063140869}, {"X": 0.33289840817451477, "Y": 0.7772908210754395}, {"X": 0.32276543974876404, "Y": 0.7772893905639648}]}, "Id": "bce7cfb4-5c00-409f-bf5a-9cd0fd3db088"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "(see", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032408978790044785, "Height": 0.01246245950460434, "Left": 0.11756269633769989, "Top": 0.7834774255752563}, "Polygon": [{"X": 0.11756300926208496, "Y": 0.7834774255752563}, {"X": 0.14997167885303497, "Y": 0.7834819555282593}, {"X": 0.1499713659286499, "Y": 0.7959398627281189}, {"X": 0.11756269633769989, "Y": 0.795935332775116}]}, "Id": "b172c2db-5e50-4f1c-b4d9-5de00727a999"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06655557453632355, "Height": 0.011859230697154999, "Left": 0.15415357053279877, "Top": 0.7822515964508057}, "Polygon": [{"X": 0.15415386855602264, "Y": 0.7822515964508057}, {"X": 0.22070914506912231, "Y": 0.7822609543800354}, {"X": 0.22070884704589844, "Y": 0.7941108345985413}, {"X": 0.15415357053279877, "Y": 0.7941014766693115}]}, "Id": "28737787-1083-4198-9ab9-4f278baecea8"}, {"BlockType": "WORD", "Confidence": 97.39935302734375, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1256713718175888, "Height": 0.013761705718934536, "Left": 0.2252596616744995, "Top": 0.7826889753341675}, "Polygon": [{"X": 0.22526001930236816, "Y": 0.7826889753341675}, {"X": 0.3509310185909271, "Y": 0.782706618309021}, {"X": 0.35093066096305847, "Y": 0.796450674533844}, {"X": 0.2252596616744995, "Y": 0.7964330315589905}]}, "Id": "3b27b0a0-b54b-49cc-ac81-36a2729afbc5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Manager", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0711146667599678, "Height": 0.01420679409056902, "Left": 0.5494952201843262, "Top": 0.7830010652542114}, "Polygon": [{"X": 0.5494956374168396, "Y": 0.7830010652542114}, {"X": 0.6206098794937134, "Y": 0.7830110788345337}, {"X": 0.6206094622612, "Y": 0.7972078919410706}, {"X": 0.5494952201843262, "Y": 0.7971978783607483}]}, "Id": "6a65fd37-0289-48ba-98ae-b28514cbf4de"}, {"BlockType": "WORD", "Confidence": 99.24266052246094, "Text": "Approval:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08005757629871368, "Height": 0.01393220666795969, "Left": 0.6244449615478516, "Top": 0.783394992351532}, "Polygon": [{"X": 0.624445378780365, "Y": 0.783394992351532}, {"X": 0.704502522945404, "Y": 0.7834062576293945}, {"X": 0.7045021057128906, "Y": 0.7973272204399109}, {"X": 0.6244449615478516, "Y": 0.7973159551620483}]}, "Id": "118a691c-fd3a-43da-9feb-989ffd542d3e"}, {"BlockType": "WORD", "Confidence": 99.48181915283203, "Text": "Notes:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05159206688404083, "Height": 0.012029643170535564, "Left": 0.11761604994535446, "Top": 0.8164384365081787}, "Polygon": [{"X": 0.11761634796857834, "Y": 0.8164384365081787}, {"X": 0.1692081242799759, "Y": 0.8164456486701965}, {"X": 0.16920781135559082, "Y": 0.8284680843353271}, {"X": 0.11761604994535446, "Y": 0.8284608125686646}]}, "Id": "577cbd04-d4c1-4127-905f-e61230f56571"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028948944061994553, "Height": 0.011225632391870022, "Left": 0.11811071634292603, "Top": 0.8682746291160583}, "Polygon": [{"X": 0.11811099201440811, "Y": 0.8682746291160583}, {"X": 0.14705966413021088, "Y": 0.8682787418365479}, {"X": 0.1470593810081482, "Y": 0.8795002698898315}, {"X": 0.11811071634292603, "Y": 0.8794962167739868}]}, "Id": "5d8e2b88-d34e-4415-a24a-9e40f0b32775"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "fine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025950679555535316, "Height": 0.01030617393553257, "Left": 0.15092016756534576, "Top": 0.8672895431518555}, "Polygon": [{"X": 0.15092042088508606, "Y": 0.8672895431518555}, {"X": 0.17687085270881653, "Y": 0.8672931790351868}, {"X": 0.17687058448791504, "Y": 0.8775957226753235}, {"X": 0.15092016756534576, "Y": 0.8775920867919922}]}, "Id": "9fc0aabc-871d-4b46-a127-e1c65a0427af"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021968604996800423, "Height": 0.008333445526659489, "Left": 0.18015296757221222, "Top": 0.8695351481437683}, "Polygon": [{"X": 0.18015319108963013, "Y": 0.8695351481437683}, {"X": 0.2021215856075287, "Y": 0.8695382475852966}, {"X": 0.20212136209011078, "Y": 0.8778685927391052}, {"X": 0.18015296757221222, "Y": 0.8778654932975769}]}, "Id": "19817f10-14ab-4dd5-9d8f-ec8759603c39"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "appealed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05989030376076698, "Height": 0.012638813816010952, "Left": 0.20533068478107452, "Top": 0.8675015568733215}, "Polygon": [{"X": 0.2053310126066208, "Y": 0.8675015568733215}, {"X": 0.2652209997177124, "Y": 0.8675099611282349}, {"X": 0.26522067189216614, "Y": 0.8801403641700745}, {"X": 0.20533068478107452, "Y": 0.8801319599151611}]}, "Id": "d8b3ff60-4090-435e-83c5-e66b89b6427d"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04305916652083397, "Height": 0.009959128685295582, "Left": 0.26870715618133545, "Top": 0.8674629926681519}, "Polygon": [{"X": 0.26870742440223694, "Y": 0.8674629926681519}, {"X": 0.3117663264274597, "Y": 0.8674690127372742}, {"X": 0.3117660582065582, "Y": 0.8774220943450928}, {"X": 0.26870715618133545, "Y": 0.8774160742759705}]}, "Id": "69c82dbb-5e6c-4353-85eb-057c47e8fd44"}, {"BlockType": "WORD", "Confidence": 99.6436538696289, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008326851762831211, "Height": 0.009763739071786404, "Left": 0.3152005970478058, "Top": 0.8676601052284241}, "Polygon": [{"X": 0.3152008354663849, "Y": 0.8676601052284241}, {"X": 0.32352742552757263, "Y": 0.8676612377166748}, {"X": 0.32352718710899353, "Y": 0.8774238228797913}, {"X": 0.3152005970478058, "Y": 0.8774226307868958}]}, "Id": "73f9e09d-e123-4884-85b8-b0ffee2bcc5f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05749214440584183, "Height": 0.0104988319799304, "Left": 0.3272935748100281, "Top": 0.8672377467155457}, "Polygon": [{"X": 0.32729384303092957, "Y": 0.8672377467155457}, {"X": 0.3847857117652893, "Y": 0.8672458529472351}, {"X": 0.3847854435443878, "Y": 0.8777365684509277}, {"X": 0.3272935748100281, "Y": 0.8777285218238831}]}, "Id": "8f6b27eb-b5a1-4130-a424-56fec3702cfa"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03120267391204834, "Height": 0.011895028874278069, "Left": 0.38798636198043823, "Top": 0.8678565621376038}, "Polygon": [{"X": 0.3879866898059845, "Y": 0.8678565621376038}, {"X": 0.4191890358924866, "Y": 0.8678609728813171}, {"X": 0.4191887080669403, "Y": 0.8797516226768494}, {"X": 0.38798636198043823, "Y": 0.879747211933136}]}, "Id": "45d8f212-63ea-488a-b1a9-12c7f7bccb09"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011242419481277466, "Height": 0.010094129480421543, "Left": 0.4226769506931305, "Top": 0.8673338890075684}, "Polygon": [{"X": 0.422677218914032, "Y": 0.8673338890075684}, {"X": 0.43391937017440796, "Y": 0.8673354387283325}, {"X": 0.43391910195350647, "Y": 0.8774279952049255}, {"X": 0.4226769506931305, "Y": 0.8774264454841614}]}, "Id": "2c14f80d-ab20-4122-97ab-8b0422280ae4"}, {"BlockType": "WORD", "Confidence": 97.32939910888672, "Text": "final.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0344957634806633, "Height": 0.010123689658939838, "Left": 0.43758878111839294, "Top": 0.8675383925437927}, "Polygon": [{"X": 0.43758904933929443, "Y": 0.8675383925437927}, {"X": 0.47208452224731445, "Y": 0.8675432205200195}, {"X": 0.47208425402641296, "Y": 0.8776620626449585}, {"X": 0.43758878111839294, "Y": 0.8776572346687317}]}, "Id": "c85023ba-3a60-418f-8967-4f601a155c51"}, {"BlockType": "WORD", "Confidence": 99.28252410888672, "Text": "Appeals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0547621063888073, "Height": 0.012011663988232613, "Left": 0.47636422514915466, "Top": 0.8677747249603271}, "Polygon": [{"X": 0.4763645529747009, "Y": 0.8677747249603271}, {"X": 0.5311263203620911, "Y": 0.8677824139595032}, {"X": 0.5311259627342224, "Y": 0.8797863721847534}, {"X": 0.47636422514915466, "Y": 0.8797786831855774}]}, "Id": "e9e6a0c5-0cad-4ff5-8ac5-8f1f2b1a6096"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028886985033750534, "Height": 0.009423933923244476, "Left": 0.5342694520950317, "Top": 0.8703201413154602}, "Polygon": [{"X": 0.5342696905136108, "Y": 0.8703201413154602}, {"X": 0.5631564259529114, "Y": 0.8703241944313049}, {"X": 0.5631561875343323, "Y": 0.8797440528869629}, {"X": 0.5342694520950317, "Y": 0.8797399997711182}]}, "Id": "6bb81cc5-0bd0-4261-9f19-61a966c0406e"}, {"BlockType": "WORD", "Confidence": 91.2414321899414, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01660262979567051, "Height": 0.01001769583672285, "Left": 0.5666442513465881, "Top": 0.8675370216369629}, "Polygon": [{"X": 0.566644549369812, "Y": 0.8675370216369629}, {"X": 0.5832468867301941, "Y": 0.8675393462181091}, {"X": 0.5832465887069702, "Y": 0.877554714679718}, {"X": 0.5666442513465881, "Y": 0.8775523900985718}]}, "Id": "d4fe6ec8-0f97-4a5d-8e9b-6ab316c1c499"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03650062903761864, "Height": 0.009908945299685001, "Left": 0.5869418978691101, "Top": 0.8676736950874329}, "Polygon": [{"X": 0.586942195892334, "Y": 0.8676736950874329}, {"X": 0.623442530632019, "Y": 0.8676788210868835}, {"X": 0.6234422326087952, "Y": 0.8775826692581177}, {"X": 0.5869418978691101, "Y": 0.877577543258667}]}, "Id": "9faea021-bd18-4b50-a045-f6ec77c6e934"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012371562421321869, "Height": 0.010067172348499298, "Left": 0.627257764339447, "Top": 0.8675341010093689}, "Polygon": [{"X": 0.6272580027580261, "Y": 0.8675341010093689}, {"X": 0.6396293044090271, "Y": 0.8675358295440674}, {"X": 0.6396290063858032, "Y": 0.8776012659072876}, {"X": 0.627257764339447, "Y": 0.8775995373725891}]}, "Id": "9987ef6c-e85d-4760-8062-129b074d0aa9"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "writing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04823950305581093, "Height": 0.012017665430903435, "Left": 0.6436449289321899, "Top": 0.8678750991821289}, "Polygon": [{"X": 0.6436452865600586, "Y": 0.8678750991821289}, {"X": 0.6918843984603882, "Y": 0.8678818941116333}, {"X": 0.6918841004371643, "Y": 0.8798927664756775}, {"X": 0.6436449289321899, "Y": 0.8798860311508179}]}, "Id": "08bd7dd8-a67b-4e17-b6ca-c7f668023888"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02468433789908886, "Height": 0.009880824945867062, "Left": 0.6955345869064331, "Top": 0.8676871657371521}, "Polygon": [{"X": 0.695534884929657, "Y": 0.8676871657371521}, {"X": 0.7202189564704895, "Y": 0.8676906228065491}, {"X": 0.7202186584472656, "Y": 0.8775680065155029}, {"X": 0.6955345869064331, "Y": 0.8775644898414612}]}, "Id": "bc69d222-3e7d-4921-b915-547dab7c5e99"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "faxed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03719257935881615, "Height": 0.0098500931635499, "Left": 0.723811149597168, "Top": 0.8678760528564453}, "Polygon": [{"X": 0.7238114476203918, "Y": 0.8678760528564453}, {"X": 0.7610037326812744, "Y": 0.8678812384605408}, {"X": 0.7610034346580505, "Y": 0.877726137638092}, {"X": 0.723811149597168, "Y": 0.8777208924293518}]}, "Id": "05b1f1a8-bd7a-4f37-a237-c8ee1b888d4f"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013110860250890255, "Height": 0.008009781129658222, "Left": 0.7647325396537781, "Top": 0.8699139952659607}, "Polygon": [{"X": 0.7647327780723572, "Y": 0.8699139952659607}, {"X": 0.7778434157371521, "Y": 0.8699158430099487}, {"X": 0.777843177318573, "Y": 0.8779237866401672}, {"X": 0.7647325396537781, "Y": 0.8779219388961792}]}, "Id": "507ea432-2050-40eb-b732-dfbabc19764d"}, {"BlockType": "WORD", "Confidence": 63.224647521972656, "Text": "(616)669-7468", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1044774204492569, "Height": 0.011757527478039265, "Left": 0.7810070514678955, "Top": 0.8679513931274414}, "Polygon": [{"X": 0.7810073494911194, "Y": 0.8679513931274414}, {"X": 0.8854844570159912, "Y": 0.8679660558700562}, {"X": 0.8854840993881226, "Y": 0.8797088861465454}, {"X": 0.7810070514678955, "Y": 0.8796942234039307}]}, "Id": "8ffa644f-c660-46c6-991a-db8ffe9bf95a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "4f0af8c5-87e5-4f2a-be1a-9874ead386dc", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "4f0af8c5-87e5-4f2a-be1a-9874ead386dc", "content-type": "application/x-amz-json-1.1", "content-length": "84339", "date": "Wed, 24 Sep 2025 13:38:20 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "f13a4133-87d9-4b93-b5fe-b11fd140e525", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:38:23 GMT", "content-type": "application/json", "content-length": "740", "connection": "keep-alive", "x-amzn-requestid": "f13a4133-87d9-4b93-b5fe-b11fd140e525"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify page 1. The content: \"Truckload Check-in Form\", \"Clear-to-Pay Voucher\", \"Load # 20081\", \"Broker\", etc. This seems a Clear-to-Pay Voucher, which is a document type \"clear_to_pay\". The definition: Approval for payment or ACH/Wire/Check transfer request or approval. Contains \"Clear-to-Pay\". So doc_type should be \"clear_to_pay\". There's no continuation. So output page 1 as clear_to_pay."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"clear_to_pay\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2827, "outputTokens": 123, "totalTokens": 2950}, "metrics": {"latencyMs": 741}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/003b4389_5_clear_to_pay_2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nSaw 7056563\nAMBVD\nSAWYER NURSERY.\nTruckload Check-in Form\nClear-to-Pay Voucher\nLoad # 20081\nBroker\nPE\nDate Processed 5/26/2023\nLoad Cost $1,811.50\nRacks Returned 58\nSurcharge + $\n/\nDeductions - $\n\\\n58 XS 4.- = $232,-\nRACKS AMOUNT TOTAL\nRacks + $232,-\n(Rack checks will now be sent to the\nbroker.)\nNet Cost $2,043.50\nBillable Shortages:\nrequipment missing with insufficient documentation\nRacks: ( ) X $250.00 $\nTotal Deductions\nFor Load:\nLoad Locks: ( ) X $25.00 = $\n$\nPallet Jack: ( ) X $500.00 - $\nLoad Straps: ( ) X $25.00 = $\nPaperwork & other Fines $\n(see attached documentation)\nManager Approval:\nNotes:\nAny fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}