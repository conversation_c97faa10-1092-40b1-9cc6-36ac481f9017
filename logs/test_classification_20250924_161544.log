2025-09-24 16:15:44,162 - INFO - Logging initialized. Log file: logs/test_classification_20250924_161544.log
2025-09-24 16:15:44,163 - INFO - 📁 Found 12 files to process
2025-09-24 16:15:44,163 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 16:15:44,163 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 16:15:44,163 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 16:15:44,163 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 16:15:44,163 - INFO - ⬆️ [16:15:44] Uploading: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:15:46,535 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf -> s3://document-extraction-logistically/temp/90772179_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:15:46,535 - INFO - 🔍 [16:15:46] Starting classification: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:15:46,535 - INFO - ⬆️ [16:15:46] Uploading: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:15:46,537 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:46,554 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:46,559 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/90772179_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:15:46,559 - INFO - Processing PDF from S3...
2025-09-24 16:15:46,559 - INFO - Downloading PDF from S3 to /tmp/tmp21jrz0zt/90772179_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:15:48,384 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:15:48,384 - INFO - Splitting PDF into individual pages...
2025-09-24 16:15:48,387 - INFO - Splitting PDF 90772179_AYEA5J1NILYPMWA7PN4V into 2 pages
2025-09-24 16:15:48,392 - INFO - Split PDF into 2 pages
2025-09-24 16:15:48,392 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:15:48,392 - INFO - Expected pages: [1, 2]
2025-09-24 16:15:51,049 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf -> s3://document-extraction-logistically/temp/3321b1e9_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:15:51,049 - INFO - 🔍 [16:15:51] Starting classification: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:15:51,050 - INFO - ⬆️ [16:15:51] Uploading: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:15:51,052 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:51,067 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:51,073 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3321b1e9_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:15:51,073 - INFO - Processing PDF from S3...
2025-09-24 16:15:51,074 - INFO - Downloading PDF from S3 to /tmp/tmpy7ic0hhm/3321b1e9_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:15:52,549 - INFO - Page 2: Extracted 216 characters, 11 lines from 90772179_AYEA5J1NILYPMWA7PN4V_9ed2ba91_page_002.pdf
2025-09-24 16:15:52,550 - INFO - Successfully processed page 2
2025-09-24 16:15:53,646 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:15:53,646 - INFO - Splitting PDF into individual pages...
2025-09-24 16:15:53,651 - INFO - Splitting PDF 3321b1e9_C3I3XLR18U7J6P1N2LZR into 1 pages
2025-09-24 16:15:53,769 - INFO - Split PDF into 1 pages
2025-09-24 16:15:53,769 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:15:53,769 - INFO - Expected pages: [1]
2025-09-24 16:15:54,296 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf -> s3://document-extraction-logistically/temp/15eadee0_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:15:54,296 - INFO - 🔍 [16:15:54] Starting classification: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:15:54,297 - INFO - ⬆️ [16:15:54] Uploading: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:15:54,298 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:54,317 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:54,324 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15eadee0_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:15:54,324 - INFO - Processing PDF from S3...
2025-09-24 16:15:54,325 - INFO - Downloading PDF from S3 to /tmp/tmpxfvh1igr/15eadee0_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:15:54,646 - INFO - Page 1: Extracted 2153 characters, 114 lines from 90772179_AYEA5J1NILYPMWA7PN4V_9ed2ba91_page_001.pdf
2025-09-24 16:15:54,647 - INFO - Successfully processed page 1
2025-09-24 16:15:54,647 - INFO - Combined 2 pages into final text
2025-09-24 16:15:54,647 - INFO - Text validation for 90772179_AYEA5J1NILYPMWA7PN4V: 2405 characters, 2 pages
2025-09-24 16:15:54,648 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:15:54,648 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:15:55,890 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf -> s3://document-extraction-logistically/temp/d05db1aa_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:15:55,891 - INFO - 🔍 [16:15:55] Starting classification: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:15:55,892 - INFO - ⬆️ [16:15:55] Uploading: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:15:55,900 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:55,912 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:55,915 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d05db1aa_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:15:55,915 - INFO - Processing PDF from S3...
2025-09-24 16:15:55,916 - INFO - Downloading PDF from S3 to /tmp/tmpdjtjvf0h/d05db1aa_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:15:56,901 - INFO - Downloaded PDF size: 1.4 MB
2025-09-24 16:15:56,901 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg -> s3://document-extraction-logistically/temp/ff4774f9_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:15:56,901 - INFO - Splitting PDF into individual pages...
2025-09-24 16:15:56,902 - INFO - 🔍 [16:15:56] Starting classification: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:15:56,903 - INFO - ⬆️ [16:15:56] Uploading: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:15:56,904 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:56,926 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:56,926 - INFO - Splitting PDF 15eadee0_C94JBR3RYYOOM5J2PFUM into 1 pages
2025-09-24 16:15:56,931 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ff4774f9_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:15:56,931 - INFO - Processing image from S3...
2025-09-24 16:15:56,937 - INFO - Split PDF into 1 pages
2025-09-24 16:15:56,937 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:15:56,937 - INFO - Expected pages: [1]
2025-09-24 16:15:57,102 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ce85a232-fdbc-4667-8501-809fa202877e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:45:56 GMT', 'content-type': 'application/json', 'content-length': '1146', 'connection': 'keep-alive', 'x-amzn-requestid': 'ce85a232-fdbc-4667-8501-809fa202877e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page as doc_type. Page 1: It\'s a Sea Freight FCL Delivery Order. Likely a "delivery order" which is a type of "delivery order" not explicitly in the list. Delivery order may be a type of "delivery notes" but not listed. Could be considered "combined_carrier_documents" if multiple docs? But only one header: "Sea Freight FCL Delivery Order". It\'s a shipping document but not invoice, BOL, POD, rate confirmation, etc. Not in list. So classify as other. But check if page 2 is continuation: page 2 says "END OF DOCUMENT". So both pages part of same delivery order. So page 1 doc_type: other, page 2 same other. But we need to output each page object. So output array of objects: [{page_no:1, doc_type:"other"}, {page_no:2, doc_type:"other"}]. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3281, 'outputTokens': 223, 'totalTokens': 3504}, 'metrics': {'latencyMs': 1094}}
2025-09-24 16:15:57,103 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/90772179_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:15:57,591 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf -> s3://document-extraction-logistically/temp/1fcd8c16_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:15:57,591 - INFO - 🔍 [16:15:57] Starting classification: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:15:57,592 - INFO - ⬆️ [16:15:57] Uploading: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:15:57,595 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:57,610 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:57,613 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1fcd8c16_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:15:57,613 - INFO - Processing PDF from S3...
2025-09-24 16:15:57,613 - INFO - Downloading PDF from S3 to /tmp/tmpoq4bw20b/1fcd8c16_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:15:58,305 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf -> s3://document-extraction-logistically/temp/edef7d42_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:15:58,306 - INFO - 🔍 [16:15:58] Starting classification: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:15:58,307 - INFO - ⬆️ [16:15:58] Uploading: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:15:58,308 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:58,326 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:58,332 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/edef7d42_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:15:58,332 - INFO - Processing PDF from S3...
2025-09-24 16:15:58,332 - INFO - Downloading PDF from S3 to /tmp/tmpjs6vaptp/edef7d42_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:15:58,439 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 16:15:58,439 - INFO - Splitting PDF into individual pages...
2025-09-24 16:15:58,441 - INFO - Splitting PDF d05db1aa_DY3D94HTH1ZH420GMDO6 into 1 pages
2025-09-24 16:15:58,443 - INFO - Split PDF into 1 pages
2025-09-24 16:15:58,444 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:15:58,444 - INFO - Expected pages: [1]
2025-09-24 16:15:59,469 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:15:59,469 - INFO - Splitting PDF into individual pages...
2025-09-24 16:15:59,470 - INFO - Splitting PDF 1fcd8c16_GJ7Z12W1O0CQO9E9H39H into 2 pages
2025-09-24 16:15:59,484 - INFO - Split PDF into 2 pages
2025-09-24 16:15:59,484 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:15:59,484 - INFO - Expected pages: [1, 2]
2025-09-24 16:15:59,616 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf -> s3://document-extraction-logistically/temp/0d94eaff_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:15:59,617 - INFO - 🔍 [16:15:59] Starting classification: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:15:59,617 - INFO - ⬆️ [16:15:59] Uploading: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:15:59,620 - INFO - Initializing TextractProcessor...
2025-09-24 16:15:59,637 - INFO - Initializing BedrockProcessor...
2025-09-24 16:15:59,640 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0d94eaff_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:15:59,641 - INFO - Processing PDF from S3...
2025-09-24 16:15:59,641 - INFO - Downloading PDF from S3 to /tmp/tmpwin5861u/0d94eaff_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:16:00,131 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:16:00,132 - INFO - Splitting PDF into individual pages...
2025-09-24 16:16:00,134 - INFO - Splitting PDF edef7d42_N9XYNNZR54XFORX6RGH2 into 2 pages
2025-09-24 16:16:00,137 - INFO - Split PDF into 2 pages
2025-09-24 16:16:00,137 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:16:00,137 - INFO - Expected pages: [1, 2]
2025-09-24 16:16:00,236 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf -> s3://document-extraction-logistically/temp/247d5acc_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:00,236 - INFO - 🔍 [16:16:00] Starting classification: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:00,236 - INFO - ⬆️ [16:16:00] Uploading: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:00,237 - INFO - Initializing TextractProcessor...
2025-09-24 16:16:00,253 - INFO - Initializing BedrockProcessor...
2025-09-24 16:16:00,258 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/247d5acc_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:00,258 - INFO - Processing PDF from S3...
2025-09-24 16:16:00,259 - INFO - Downloading PDF from S3 to /tmp/tmpyg3hygp1/247d5acc_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:00,417 - INFO - Page 1: Extracted 1847 characters, 114 lines from 3321b1e9_C3I3XLR18U7J6P1N2LZR_58b10ae5_page_001.pdf
2025-09-24 16:16:00,417 - INFO - Successfully processed page 1
2025-09-24 16:16:00,417 - INFO - Combined 1 pages into final text
2025-09-24 16:16:00,417 - INFO - Text validation for 3321b1e9_C3I3XLR18U7J6P1N2LZR: 1864 characters, 1 pages
2025-09-24 16:16:00,418 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:00,418 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:00,692 - INFO - S3 Image temp/ff4774f9_G7K3JPMT2OXZWEI7RRNQ.jpg: Extracted 2237 characters, 96 lines
2025-09-24 16:16:00,693 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:00,693 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:00,868 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf -> s3://document-extraction-logistically/temp/60b6b2d9_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:00,869 - INFO - 🔍 [16:16:00] Starting classification: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:00,872 - INFO - ⬆️ [16:16:00] Uploading: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:00,877 - INFO - Initializing TextractProcessor...
2025-09-24 16:16:00,890 - INFO - Initializing BedrockProcessor...
2025-09-24 16:16:00,893 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/60b6b2d9_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:00,894 - INFO - Processing PDF from S3...
2025-09-24 16:16:00,894 - INFO - Downloading PDF from S3 to /tmp/tmp61wn7idd/60b6b2d9_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:02,036 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:16:02,036 - INFO - Splitting PDF into individual pages...
2025-09-24 16:16:02,037 - INFO - Splitting PDF 247d5acc_ODBS2NBTU6S3GF9JVE6V into 1 pages
2025-09-24 16:16:02,046 - INFO - Split PDF into 1 pages
2025-09-24 16:16:02,046 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:16:02,046 - INFO - Expected pages: [1]
2025-09-24 16:16:02,165 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bbeed2a3-6574-4b95-a418-37caa3f1cb0a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:02 GMT', 'content-type': 'application/json', 'content-length': '607', 'connection': 'keep-alive', 'x-amzn-requestid': 'bbeed2a3-6574-4b95-a418-37caa3f1cb0a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We must use tool classify_logistics_doc_type with input: documents array with objects page_no: 1, doc_type: "bol" presumably. Let\'s confirm: It has "Bill of Lading" header, "BL #", BOL details. So doc_type is bol. Output JSON as tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3060, 'outputTokens': 92, 'totalTokens': 3152}, 'metrics': {'latencyMs': 613}}
2025-09-24 16:16:02,166 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3321b1e9_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:16:02,190 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:16:02,190 - INFO - Splitting PDF into individual pages...
2025-09-24 16:16:02,191 - INFO - Splitting PDF 0d94eaff_NFG5QQXEN21TEZZ9SYPW into 2 pages
2025-09-24 16:16:02,197 - INFO - Split PDF into 2 pages
2025-09-24 16:16:02,197 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:16:02,197 - INFO - Expected pages: [1, 2]
2025-09-24 16:16:02,519 - INFO - Page 2: Extracted 0 characters, 0 lines from 1fcd8c16_GJ7Z12W1O0CQO9E9H39H_a3a33b12_page_002.pdf
2025-09-24 16:16:02,519 - INFO - Successfully processed page 2
2025-09-24 16:16:02,645 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:16:02,646 - INFO - Splitting PDF into individual pages...
2025-09-24 16:16:02,647 - INFO - Splitting PDF 60b6b2d9_SMX8DOTQ89U191SMP0TO into 1 pages
2025-09-24 16:16:02,649 - INFO - Split PDF into 1 pages
2025-09-24 16:16:02,649 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:16:02,649 - INFO - Expected pages: [1]
2025-09-24 16:16:02,915 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg -> s3://document-extraction-logistically/temp/61ddc0a9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:02,916 - INFO - 🔍 [16:16:02] Starting classification: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:02,916 - INFO - ⬆️ [16:16:02] Uploading: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:02,918 - INFO - Initializing TextractProcessor...
2025-09-24 16:16:02,934 - INFO - Initializing BedrockProcessor...
2025-09-24 16:16:02,940 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/61ddc0a9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:02,940 - INFO - Processing image from S3...
2025-09-24 16:16:03,090 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1765af62-d7cc-480e-b74d-a1374e5235d4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:02 GMT', 'content-type': 'application/json', 'content-length': '1171', 'connection': 'keep-alive', 'x-amzn-requestid': '1765af62-d7cc-480e-b74d-a1374e5235d4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with DELIVERY ORDER. It\'s a Delivery Order document. But not in the list. The list includes: invoice, comm_invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, other, tender_from_cust, so_confirmation, ingate, outgate.\n\nDelivery Order is not explicitly listed. It\'s a kind of shipping instruction. Could be "combined_carrier_documents"? But it\'s just a single document. Not multiple. Could be "other" as fallback.\n\nCheck if it\'s a "delivery order" is a type of "bill of lading"? No, it\'s a separate doc. Should classify as "other".\n\nThus page1: doc_type: other. No continuation. Provide JSON: [{"page_no":1,"doc_type":"other"}].'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3226, 'outputTokens': 216, 'totalTokens': 3442}, 'metrics': {'latencyMs': 1148}}
2025-09-24 16:16:03,090 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ff4774f9_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:16:03,602 - INFO - Page 1: Extracted 3353 characters, 164 lines from 15eadee0_C94JBR3RYYOOM5J2PFUM_ae77812d_page_001.pdf
2025-09-24 16:16:03,602 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf -> s3://document-extraction-logistically/temp/da640436_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:03,602 - INFO - 🔍 [16:16:03] Starting classification: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:03,602 - INFO - Successfully processed page 1
2025-09-24 16:16:03,603 - INFO - Combined 1 pages into final text
2025-09-24 16:16:03,604 - INFO - Text validation for 15eadee0_C94JBR3RYYOOM5J2PFUM: 3370 characters, 1 pages
2025-09-24 16:16:03,604 - INFO - Initializing TextractProcessor...
2025-09-24 16:16:03,615 - INFO - Initializing BedrockProcessor...
2025-09-24 16:16:03,620 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:03,620 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:03,621 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/da640436_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:03,627 - INFO - Processing PDF from S3...
2025-09-24 16:16:03,628 - INFO - Downloading PDF from S3 to /tmp/tmp10t_wxqo/da640436_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:03,669 - INFO - 

AYEA5J1NILYPMWA7PN4V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:16:03,669 - INFO - 

✓ Saved result: output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:16:03,954 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/90772179_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:16:03,988 - INFO - 

C3I3XLR18U7J6P1N2LZR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:03,988 - INFO - 

✓ Saved result: output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:16:04,193 - INFO - Page 1: Extracted 1185 characters, 52 lines from d05db1aa_DY3D94HTH1ZH420GMDO6_6b564a3f_page_001.pdf
2025-09-24 16:16:04,193 - INFO - Successfully processed page 1
2025-09-24 16:16:04,194 - INFO - Combined 1 pages into final text
2025-09-24 16:16:04,194 - INFO - Text validation for d05db1aa_DY3D94HTH1ZH420GMDO6: 1202 characters, 1 pages
2025-09-24 16:16:04,194 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:04,194 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:04,279 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3321b1e9_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:16:04,334 - INFO - Page 2: Extracted 1932 characters, 48 lines from edef7d42_N9XYNNZR54XFORX6RGH2_e5f5ec90_page_002.pdf
2025-09-24 16:16:04,337 - INFO - Successfully processed page 2
2025-09-24 16:16:04,353 - INFO - 

G7K3JPMT2OXZWEI7RRNQ.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:16:04,353 - INFO - 

✓ Saved result: output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:16:04,647 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ff4774f9_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:16:05,233 - INFO - Page 1: Extracted 4034 characters, 142 lines from 1fcd8c16_GJ7Z12W1O0CQO9E9H39H_a3a33b12_page_001.pdf
2025-09-24 16:16:05,233 - INFO - Successfully processed page 1
2025-09-24 16:16:05,233 - INFO - Combined 2 pages into final text
2025-09-24 16:16:05,234 - INFO - Text validation for 1fcd8c16_GJ7Z12W1O0CQO9E9H39H: 4069 characters, 2 pages
2025-09-24 16:16:05,234 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:05,234 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:05,593 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '27bab705-01c9-4bdb-b4bf-57c9acf252c0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:05 GMT', 'content-type': 'application/json', 'content-length': '859', 'connection': 'keep-alive', 'x-amzn-requestid': '27bab705-01c9-4bdb-b4bf-57c9acf252c0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have to classify each page. Only page1 provided. This page looks like an Air Waybill. That\'s a Bill of Lading for air shipments. The document type: bol. The page has header "Air Waybill" and details. So classify as bol.\n\nWe must output via tool classify_logistics_doc_type with array of documents, each with page_no and doc_type. Just one page.\n\nReturn JSON object? The tool expects JSON with "documents": array of objects.\n\nLet\'s produce tool call.'}}}, {'toolUse': {'toolUseId': 'tooluse_ByLYx23GQam_whczJrt16g', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3445, 'outputTokens': 119, 'totalTokens': 3564}, 'metrics': {'latencyMs': 778}}
2025-09-24 16:16:05,593 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15eadee0_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:16:05,603 - INFO - Page 1: Extracted 1616 characters, 146 lines from edef7d42_N9XYNNZR54XFORX6RGH2_e5f5ec90_page_001.pdf
2025-09-24 16:16:05,604 - INFO - Successfully processed page 1
2025-09-24 16:16:05,604 - INFO - Combined 2 pages into final text
2025-09-24 16:16:05,605 - INFO - Text validation for edef7d42_N9XYNNZR54XFORX6RGH2: 3584 characters, 2 pages
2025-09-24 16:16:05,610 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:05,613 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:05,682 - INFO - 

C94JBR3RYYOOM5J2PFUM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:05,682 - INFO - 

✓ Saved result: output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:16:05,783 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 16:16:05,783 - INFO - Splitting PDF into individual pages...
2025-09-24 16:16:05,785 - INFO - Splitting PDF da640436_Z10BLMPIZ96XLUZ5NUPF into 1 pages
2025-09-24 16:16:05,854 - INFO - Split PDF into 1 pages
2025-09-24 16:16:05,854 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:16:05,854 - INFO - Expected pages: [1]
2025-09-24 16:16:05,986 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15eadee0_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:16:06,291 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0e4bc593-e7ae-4a9a-a798-25a853000a8e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:06 GMT', 'content-type': 'application/json', 'content-length': '913', 'connection': 'keep-alive', 'x-amzn-requestid': '0e4bc593-e7ae-4a9a-a798-25a853000a8e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have only page1. We need to determine doc_type. The page has "BILL OF LADING" header. So doc_type likely "bol". It\'s a bill of lading. The question: are there any other overlapping types like combined_carrier_documents? It\'s just one BOL. So classify as bol. Should output via function call.\n\nWe need to output JSON array? The tool expects a single call with array of documents, each with page_no and doc_type. For each page, we provide. So we produce:\n\n{\n  "documents": [\n    { "page_no": 1, "doc_type": "bol" }\n  ]\n}\n\nReturn via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2789, 'outputTokens': 163, 'totalTokens': 2952}, 'metrics': {'latencyMs': 912}}
2025-09-24 16:16:06,291 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d05db1aa_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:16:06,309 - INFO - 

DY3D94HTH1ZH420GMDO6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:06,309 - INFO - 

✓ Saved result: output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:16:06,600 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d05db1aa_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:16:07,086 - INFO - Page 1: Extracted 2057 characters, 76 lines from 247d5acc_ODBS2NBTU6S3GF9JVE6V_fdad1752_page_001.pdf
2025-09-24 16:16:07,087 - INFO - Successfully processed page 1
2025-09-24 16:16:07,087 - INFO - Combined 1 pages into final text
2025-09-24 16:16:07,087 - INFO - Text validation for 247d5acc_ODBS2NBTU6S3GF9JVE6V: 2074 characters, 1 pages
2025-09-24 16:16:07,087 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:07,087 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:07,456 - INFO - S3 Image temp/61ddc0a9_Y7P2IPB0S079FZE11EEA.jpg: Extracted 821 characters, 52 lines
2025-09-24 16:16:07,456 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:07,456 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:07,642 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ff49b5b0-9759-4a22-91e7-5e4ff2da5d5d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:07 GMT', 'content-type': 'application/json', 'content-length': '1207', 'connection': 'keep-alive', 'x-amzn-requestid': 'ff49b5b0-9759-4a22-91e7-5e4ff2da5d5d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 is a Bill of Lading. It has header "Conocimiento de embarque recto - Original", "BILL OF LADING #". So doc_type: bol. Page2 is empty? It shows just <page2> </page2> no content. Likely continuation? But no content. According to instructions: For every page in the input PDF you MUST return exactly one object describing that page. If page is continuation, assign same doc_type. But page2 appears blank. It could be simply blank page. But blank page still part of same document? Possibly yes, if it\'s the second page of the Bill of Lading. It might be a blank second page. So we classify page2 as bol. There\'s no content to detect \'continued\' but page numbering? Not given. But since it\'s the second page of the same PDF, we treat as continuation. So we assign bol for page2.\n\nThus output two objects. Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3585, 'outputTokens': 230, 'totalTokens': 3815}, 'metrics': {'latencyMs': 1167}}
2025-09-24 16:16:07,642 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1fcd8c16_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:16:07,690 - INFO - 

GJ7Z12W1O0CQO9E9H39H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:07,690 - INFO - 

✓ Saved result: output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:16:07,895 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4e363513-6f55-4460-8c34-69733058eb62', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:07 GMT', 'content-type': 'application/json', 'content-length': '992', 'connection': 'keep-alive', 'x-amzn-requestid': '4e363513-6f55-4460-8c34-69733058eb62'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Need to classify each.\n\nPage1: clearly a Bill of Lading. Keywords: "BILL OF LADING", "Bill of Lading Number", etc. So doc_type: bol.\n\nPage2: continuation of BOL. Contains signatures, etc. No separate header. It\'s same document. So same type bol.\n\nCheck continuation detection: page2 doesn\'t start with "continued" but it\'s page 2 of 2 maybe. The prompt says if page starts with "continued" or pagination. But page2 still part of same doc. We should set doc_type bol for page2. No override.\n\nReturn array objects: page_no 1 bol, page_no 2 bol.\n\nUse tool classify_logistics_doc_type with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3557, 'outputTokens': 181, 'totalTokens': 3738}, 'metrics': {'latencyMs': 965}}
2025-09-24 16:16:07,896 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/edef7d42_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:16:07,979 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1fcd8c16_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:16:08,030 - INFO - 

N9XYNNZR54XFORX6RGH2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:08,030 - INFO - 

✓ Saved result: output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:16:08,140 - INFO - Page 2: Extracted 1028 characters, 92 lines from 0d94eaff_NFG5QQXEN21TEZZ9SYPW_5dc77421_page_002.pdf
2025-09-24 16:16:08,140 - INFO - Successfully processed page 2
2025-09-24 16:16:08,313 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/edef7d42_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:16:09,010 - INFO - Page 1: Extracted 2916 characters, 126 lines from 60b6b2d9_SMX8DOTQ89U191SMP0TO_a4966504_page_001.pdf
2025-09-24 16:16:09,010 - INFO - Successfully processed page 1
2025-09-24 16:16:09,010 - INFO - Combined 1 pages into final text
2025-09-24 16:16:09,010 - INFO - Text validation for 60b6b2d9_SMX8DOTQ89U191SMP0TO: 2933 characters, 1 pages
2025-09-24 16:16:09,010 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:09,010 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:09,212 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3aef79e8-b51e-47ca-ace8-e83ffb8167e3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:09 GMT', 'content-type': 'application/json', 'content-length': '618', 'connection': 'keep-alive', 'x-amzn-requestid': '3aef79e8-b51e-47ca-ace8-e83ffb8167e3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. The text includes "BILL OF LADING" header. So doc_type is bol. Check if it\'s continuation? No. So output array with one object: page_no 1, doc_type "bol". Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_dp4I5JX-SFirvUJEWsndmA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2759, 'outputTokens': 74, 'totalTokens': 2833}, 'metrics': {'latencyMs': 568}}
2025-09-24 16:16:09,213 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/61ddc0a9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:09,236 - INFO - 

Y7P2IPB0S079FZE11EEA.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:09,236 - INFO - 

✓ Saved result: output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:16:09,305 - INFO - Page 1: Extracted 1621 characters, 166 lines from 0d94eaff_NFG5QQXEN21TEZZ9SYPW_5dc77421_page_001.pdf
2025-09-24 16:16:09,305 - INFO - Successfully processed page 1
2025-09-24 16:16:09,306 - INFO - Combined 2 pages into final text
2025-09-24 16:16:09,306 - INFO - Text validation for 0d94eaff_NFG5QQXEN21TEZZ9SYPW: 2685 characters, 2 pages
2025-09-24 16:16:09,306 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:09,306 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:09,392 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '96edd6f2-1b13-474a-97a5-454adeb4682f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:09 GMT', 'content-type': 'application/json', 'content-length': '1398', 'connection': 'keep-alive', 'x-amzn-requestid': '96edd6f2-1b13-474a-97a5-454adeb4682f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page, page1. We need to classify. The page contains a "Load Confirmation #", with details of a load, rate, etc. This looks like a "Load Confirmation" which is a "Load Confirmation" from a carrier? The categories provided include "rate_confirmation" (Carrier Rate Confirmation) and "cust_rate_confirmation" (Customer Rate Confirmation). The page says "Load Confirmation # 1536427" and "In order to receive payment for this load, this confirmation must be signed and returned." It also says "This signed confirmation will fulfill requirements of a binding contract under this contract carrier authority." It also says "Agreed Rate" and "Rate Confirmation" maybe. This is a confirmation of rate and load, basically a rate confirmation. So likely "rate_confirmation". It is not a commercial invoice, not a POD, not a BOL. It is a carrier rate confirmation. So doc_type = "rate_confirmation".\n\nWe need to output JSON array of documents with page_no and doc_type. There\'s only one page. Output exactly one object. Output via tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3046, 'outputTokens': 240, 'totalTokens': 3286}, 'metrics': {'latencyMs': 1156}}
2025-09-24 16:16:09,392 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/247d5acc_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:09,540 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/61ddc0a9_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:09,567 - INFO - 

ODBS2NBTU6S3GF9JVE6V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 16:16:09,567 - INFO - 

✓ Saved result: output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:16:09,847 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/247d5acc_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:10,887 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '14bca688-1b09-45ba-a544-e82dd53618d0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:10 GMT', 'content-type': 'application/json', 'content-length': '641', 'connection': 'keep-alive', 'x-amzn-requestid': '14bca688-1b09-45ba-a544-e82dd53618d0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page input. It appears to be a Bill of Lading. Keywords: "Bill of Lading", "Ship From", "Ship To", etc. So doc_type = "bol". The page number is 1. Output array with single object. Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_gf5EGVw0S9mNl-2CbB9zag', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3258, 'outputTokens': 81, 'totalTokens': 3339}, 'metrics': {'latencyMs': 633}}
2025-09-24 16:16:10,888 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/60b6b2d9_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:10,936 - INFO - 

SMX8DOTQ89U191SMP0TO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:10,936 - INFO - 

✓ Saved result: output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:16:11,224 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/60b6b2d9_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:11,483 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8ebf161b-1c4a-4342-b8ae-be3e2d8239a0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:11 GMT', 'content-type': 'application/json', 'content-length': '992', 'connection': 'keep-alive', 'x-amzn-requestid': '8ebf161b-1c4a-4342-b8ae-be3e2d8239a0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. They appear to be a "PKG LIST" which is a packing list. Keywords: "PKG LIST", "PACKING LIST" etc. The content indicates items, quantities, weights. So doc_type likely pack_list. Let\'s confirm: No other document type keywords like invoice, BOL, etc. "PKG LIST" is the header. It\'s a single document spanning two pages. So each page gets doc_type: pack_list. Need to check if page 2 is continuation: page 2 starts with "ARIORS LLC" (typo?). It seems continuation. Page 2 also has "Page 2 of 2". So it\'s the same doc_type. So output two objects: page_no 1: pack_list; page_no 2: pack_list. Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3738, 'outputTokens': 196, 'totalTokens': 3934}, 'metrics': {'latencyMs': 983}}
2025-09-24 16:16:11,483 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0d94eaff_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:16:11,535 - INFO - 

NFG5QQXEN21TEZZ9SYPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:16:11,536 - INFO - 

✓ Saved result: output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:16:11,824 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0d94eaff_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:16:11,888 - INFO - Page 1: Extracted 3550 characters, 185 lines from da640436_Z10BLMPIZ96XLUZ5NUPF_e8b70c8c_page_001.pdf
2025-09-24 16:16:11,888 - INFO - Successfully processed page 1
2025-09-24 16:16:11,889 - INFO - Combined 1 pages into final text
2025-09-24 16:16:11,890 - INFO - Text validation for da640436_Z10BLMPIZ96XLUZ5NUPF: 3567 characters, 1 pages
2025-09-24 16:16:11,890 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:16:11,890 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:16:13,770 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3984774b-5665-4cef-9e9b-5ded685c99f5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:46:13 GMT', 'content-type': 'application/json', 'content-length': '574', 'connection': 'keep-alive', 'x-amzn-requestid': '3984774b-5665-4cef-9e9b-5ded685c99f5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. It contains a Bill of Lading header: "BILL OF LADING". Also it lists consignor, consignee, vessel, port, shipment details, etc. So doc_type = bol. There\'s no other doc. So output: one object with page_no:1, doc_type:"bol". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3730, 'outputTokens': 88, 'totalTokens': 3818}, 'metrics': {'latencyMs': 589}}
2025-09-24 16:16:13,771 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/da640436_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:13,809 - INFO - 

Z10BLMPIZ96XLUZ5NUPF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:16:13,809 - INFO - 

✓ Saved result: output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:16:14,092 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/da640436_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:14,094 - INFO - 
📊 Processing Summary:
2025-09-24 16:16:14,094 - INFO -    Total files: 12
2025-09-24 16:16:14,094 - INFO -    Successful: 12
2025-09-24 16:16:14,094 - INFO -    Failed: 0
2025-09-24 16:16:14,094 - INFO -    Duration: 29.93 seconds
2025-09-24 16:16:14,094 - INFO -    Output directory: output
2025-09-24 16:16:14,094 - INFO - 
📋 Successfully Processed Files:
2025-09-24 16:16:14,094 - INFO -    📄 AYEA5J1NILYPMWA7PN4V.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 16:16:14,094 - INFO -    📄 C3I3XLR18U7J6P1N2LZR.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:16:14,094 - INFO -    📄 C94JBR3RYYOOM5J2PFUM.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:16:14,095 - INFO -    📄 DY3D94HTH1ZH420GMDO6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:16:14,095 - INFO -    📄 G7K3JPMT2OXZWEI7RRNQ.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 16:16:14,095 - INFO -    📄 GJ7Z12W1O0CQO9E9H39H.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:16:14,095 - INFO -    📄 N9XYNNZR54XFORX6RGH2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:16:14,095 - INFO -    📄 NFG5QQXEN21TEZZ9SYPW.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-24 16:16:14,095 - INFO -    📄 ODBS2NBTU6S3GF9JVE6V.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-24 16:16:14,095 - INFO -    📄 SMX8DOTQ89U191SMP0TO.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:16:14,095 - INFO -    📄 Y7P2IPB0S079FZE11EEA.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:16:14,095 - INFO -    📄 Z10BLMPIZ96XLUZ5NUPF.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:16:14,096 - INFO - 
============================================================================================================================================
2025-09-24 16:16:14,096 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 16:16:14,096 - INFO - ============================================================================================================================================
2025-09-24 16:16:14,096 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 16:16:14,096 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:16:14,096 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           1      other                                    run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:16:14,096 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:16:14,096 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:16:14,096 - INFO - 
2025-09-24 16:16:14,096 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           2      other                                    run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:16:14,096 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:16:14,096 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:16:14,096 - INFO - 
2025-09-24 16:16:14,097 - INFO - C3I3XLR18U7J6P1N2LZR.pdf                           1      bol                                      run1_C3I3XLR18U7J6P1N2LZR.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - C94JBR3RYYOOM5J2PFUM.pdf                           1      bol                                      run1_C94JBR3RYYOOM5J2PFUM.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - DY3D94HTH1ZH420GMDO6.pdf                           1      bol                                      run1_DY3D94HTH1ZH420GMDO6.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - G7K3JPMT2OXZWEI7RRNQ.jpg                           1      other                                    run1_G7K3JPMT2OXZWEI7RRNQ.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           1      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           2      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           1      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           2      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           1      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           2      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:16:14,097 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:16:14,097 - INFO - 
2025-09-24 16:16:14,097 - INFO - ODBS2NBTU6S3GF9JVE6V.pdf                           1      rate_confirmation                        run1_ODBS2NBTU6S3GF9JVE6V.json                                                  
2025-09-24 16:16:14,097 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:16:14,098 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:16:14,098 - INFO - 
2025-09-24 16:16:14,098 - INFO - SMX8DOTQ89U191SMP0TO.pdf                           1      bol                                      run1_SMX8DOTQ89U191SMP0TO.json                                                  
2025-09-24 16:16:14,098 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:16:14,098 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:16:14,098 - INFO - 
2025-09-24 16:16:14,098 - INFO - Y7P2IPB0S079FZE11EEA.jpg                           1      bol                                      run1_Y7P2IPB0S079FZE11EEA.json                                                  
2025-09-24 16:16:14,098 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:16:14,098 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:16:14,098 - INFO - 
2025-09-24 16:16:14,098 - INFO - Z10BLMPIZ96XLUZ5NUPF.pdf                           1      bol                                      run1_Z10BLMPIZ96XLUZ5NUPF.json                                                  
2025-09-24 16:16:14,098 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:16:14,098 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:16:14,098 - INFO - 
2025-09-24 16:16:14,098 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:16:14,098 - INFO - Total entries: 16
2025-09-24 16:16:14,098 - INFO - ============================================================================================================================================
2025-09-24 16:16:14,098 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 16:16:14,098 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:16:14,098 - INFO -   1. AYEA5J1NILYPMWA7PN4V.pdf            Page 1   → other           | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:16:14,098 - INFO -   2. AYEA5J1NILYPMWA7PN4V.pdf            Page 2   → other           | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:16:14,098 - INFO -   3. C3I3XLR18U7J6P1N2LZR.pdf            Page 1   → bol             | run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:16:14,098 - INFO -   4. C94JBR3RYYOOM5J2PFUM.pdf            Page 1   → bol             | run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:16:14,098 - INFO -   5. DY3D94HTH1ZH420GMDO6.pdf            Page 1   → bol             | run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:16:14,098 - INFO -   6. G7K3JPMT2OXZWEI7RRNQ.jpg            Page 1   → other           | run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:16:14,098 - INFO -   7. GJ7Z12W1O0CQO9E9H39H.pdf            Page 1   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:16:14,098 - INFO -   8. GJ7Z12W1O0CQO9E9H39H.pdf            Page 2   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:16:14,098 - INFO -   9. N9XYNNZR54XFORX6RGH2.pdf            Page 1   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:16:14,098 - INFO -  10. N9XYNNZR54XFORX6RGH2.pdf            Page 2   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:16:14,098 - INFO -  11. NFG5QQXEN21TEZZ9SYPW.pdf            Page 1   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:16:14,098 - INFO -  12. NFG5QQXEN21TEZZ9SYPW.pdf            Page 2   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:16:14,098 - INFO -  13. ODBS2NBTU6S3GF9JVE6V.pdf            Page 1   → rate_confirmation | run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:16:14,098 - INFO -  14. SMX8DOTQ89U191SMP0TO.pdf            Page 1   → bol             | run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:16:14,098 - INFO -  15. Y7P2IPB0S079FZE11EEA.jpg            Page 1   → bol             | run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:16:14,098 - INFO -  16. Z10BLMPIZ96XLUZ5NUPF.pdf            Page 1   → bol             | run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:16:14,098 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:16:14,098 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 29.930601, 'processed_files': [{'filename': 'AYEA5J1NILYPMWA7PN4V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf'}, {'filename': 'C3I3XLR18U7J6P1N2LZR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf'}, {'filename': 'C94JBR3RYYOOM5J2PFUM.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf'}, {'filename': 'DY3D94HTH1ZH420GMDO6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf'}, {'filename': 'G7K3JPMT2OXZWEI7RRNQ.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg'}, {'filename': 'GJ7Z12W1O0CQO9E9H39H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf'}, {'filename': 'N9XYNNZR54XFORX6RGH2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf'}, {'filename': 'NFG5QQXEN21TEZZ9SYPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf'}, {'filename': 'ODBS2NBTU6S3GF9JVE6V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf'}, {'filename': 'SMX8DOTQ89U191SMP0TO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf'}, {'filename': 'Y7P2IPB0S079FZE11EEA.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg'}, {'filename': 'Z10BLMPIZ96XLUZ5NUPF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf'}]}
