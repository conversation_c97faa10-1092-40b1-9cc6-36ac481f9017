2025-09-24 15:17:47,222 - INFO - Logging initialized. Log file: logs/test_classification_20250924_151747.log
2025-09-24 15:17:47,222 - INFO - 📁 Found 1 files to process
2025-09-24 15:17:47,222 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:17:47,222 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 15:17:47,222 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 15:17:47,222 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 15:17:47,222 - INFO - ⬆️ [15:17:47] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:17:52,417 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/7e628010_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:17:52,417 - INFO - 🔍 [15:17:52] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:17:52,419 - INFO - Initializing TextractProcessor...
2025-09-24 15:17:52,435 - INFO - Initializing BedrockProcessor...
2025-09-24 15:17:52,445 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7e628010_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:17:52,445 - INFO - Processing PDF from S3...
2025-09-24 15:17:52,446 - INFO - Downloading PDF from S3 to /tmp/tmpv_6u_air/7e628010_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:17:55,014 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:17:55,014 - INFO - Splitting PDF into individual pages...
2025-09-24 15:17:55,015 - INFO - Splitting PDF 7e628010_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:17:55,018 - INFO - Split PDF into 1 pages
2025-09-24 15:17:55,018 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:17:55,018 - INFO - Expected pages: [1]
2025-09-24 15:18:02,164 - INFO - Page 1: Extracted 939 characters, 64 lines from 7e628010_A34CDFDJ66EDOZEKZWJL_9ab8c6d6_page_001.pdf
2025-09-24 15:18:02,164 - INFO - Successfully processed page 1
2025-09-24 15:18:02,164 - INFO - Combined 1 pages into final text
2025-09-24 15:18:02,165 - INFO - Text validation for 7e628010_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:18:02,165 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:18:02,165 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:18:04,908 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'faa3a846-5fb6-4934-9ad9-4b24afd7c80e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:48:04 GMT', 'content-type': 'application/json', 'content-length': '1408', 'connection': 'keep-alive', 'x-amzn-requestid': 'faa3a846-5fb6-4934-9ad9-4b24afd7c80e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The page appears to be an Inspection Certificate. The header says "Inspection Certificate". It includes weight, dimensions, measurement. That matches weight_and_inspection_cert or inspection_cert. The guidelines:\n\n- weight_and_inspection_cert: certificate of analysis, measurement certificate, weight certificate, inspection certificate, W&I. But if it has keywords mentioned in nmfc_cert, classify nmfc_cert else keep weight_and_inspection_cert.\n\n- nmfc_cert: NMFC classification certificate or correction notice. Keywords: Inspected against original or Corrected etc. Not present.\n\nThus weight_and_inspection_cert.\n\nCheck also inspection_cert: if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert.\n\nSo we classify as weight_and_inspection_cert.\n\nWe output using the tool. The tool expects a JSON object with documents array. For each page: page_no: 1, doc_type: "weight_and_inspection_cert".\n\nReturn tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 232, 'totalTokens': 3058}, 'metrics': {'latencyMs': 1205}}
2025-09-24 15:18:04,909 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7e628010_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:18:04,927 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:18:04,927 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:18:05,928 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7e628010_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:18:05,929 - INFO - 
📊 Processing Summary:
2025-09-24 15:18:05,930 - INFO -    Total files: 1
2025-09-24 15:18:05,930 - INFO -    Successful: 1
2025-09-24 15:18:05,930 - INFO -    Failed: 0
2025-09-24 15:18:05,930 - INFO -    Duration: 18.71 seconds
2025-09-24 15:18:05,930 - INFO -    Output directory: output
2025-09-24 15:18:05,930 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:18:05,930 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:18:05,931 - INFO - 
============================================================================================================================================
2025-09-24 15:18:05,931 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:18:05,931 - INFO - ============================================================================================================================================
2025-09-24 15:18:05,931 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:18:05,931 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:18:05,931 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:18:05,931 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:18:05,931 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:18:05,932 - INFO - 
2025-09-24 15:18:05,932 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:18:05,932 - INFO - Total entries: 1
2025-09-24 15:18:05,932 - INFO - ============================================================================================================================================
2025-09-24 15:18:05,932 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:18:05,932 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:18:05,932 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:18:05,932 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:18:05,932 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 18.706857, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
