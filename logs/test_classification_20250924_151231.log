2025-09-24 15:12:31,901 - INFO - Logging initialized. Log file: logs/test_classification_20250924_151231.log
2025-09-24 15:12:31,902 - INFO - 📁 Found 1 files to process
2025-09-24 15:12:31,902 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:12:31,902 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 15:12:31,903 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 15:12:31,903 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 15:12:31,903 - INFO - ⬆️ [15:12:31] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:36,410 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/8c09fb14_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:36,411 - INFO - 🔍 [15:12:36] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:36,412 - INFO - Initializing TextractProcessor...
2025-09-24 15:12:36,429 - INFO - Initializing BedrockProcessor...
2025-09-24 15:12:36,435 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8c09fb14_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:36,435 - INFO - Processing PDF from S3...
2025-09-24 15:12:36,435 - INFO - Downloading PDF from S3 to /tmp/tmptuynj94q/8c09fb14_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:38,912 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:12:38,912 - INFO - Splitting PDF into individual pages...
2025-09-24 15:12:38,914 - INFO - Splitting PDF 8c09fb14_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:12:38,917 - INFO - Split PDF into 1 pages
2025-09-24 15:12:38,917 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:12:38,917 - INFO - Expected pages: [1]
2025-09-24 15:12:45,837 - INFO - Page 1: Extracted 939 characters, 64 lines from 8c09fb14_A34CDFDJ66EDOZEKZWJL_19029e38_page_001.pdf
2025-09-24 15:12:45,837 - INFO - Successfully processed page 1
2025-09-24 15:12:45,837 - INFO - Combined 1 pages into final text
2025-09-24 15:12:45,838 - INFO - Text validation for 8c09fb14_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:12:45,838 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:12:45,838 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:12:48,625 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '98ac74f0-6cf6-4057-a2be-17b2b38ebde0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:42:48 GMT', 'content-type': 'application/json', 'content-length': '1463', 'connection': 'keep-alive', 'x-amzn-requestid': '98ac74f0-6cf6-4057-a2be-17b2b38ebde0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 provided. We must output one object with page_no 1 and doc_type. Determine type. The content: "Inspection Certificate" appears. It has weight, dimensions, "Mettler-Toledo", "Measurement Certificate". Keywords: "Inspection Certificate", "Dimensions and weights determined and certified", "Weight(Lbs)", "Density". This matches weight_and_inspection_cert or inspection_cert? Look at definitions: weight_and_inspection_cert: certificate confirming weight possibly with length, width, height but not rechecked. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I. There\'s rule: if weight_and_inspection_cert has keywords mentioned in nmfc_cert, classify as nmfc_cert else keep weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify weight_and_inspection_cert; otherwise, inspection_cert. Here it has weight Lbs and dimensions. So weight_and_inspection_cert. No NMFC keywords. So doc_type = weight_and_inspection_cert.\n\nReturn tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 247, 'totalTokens': 3073}, 'metrics': {'latencyMs': 1243}}
2025-09-24 15:12:48,626 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8c09fb14_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:48,659 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:12:48,659 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:12:50,127 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8c09fb14_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:50,128 - INFO - 
📊 Processing Summary:
2025-09-24 15:12:50,128 - INFO -    Total files: 1
2025-09-24 15:12:50,128 - INFO -    Successful: 1
2025-09-24 15:12:50,128 - INFO -    Failed: 0
2025-09-24 15:12:50,128 - INFO -    Duration: 18.23 seconds
2025-09-24 15:12:50,128 - INFO -    Output directory: output
2025-09-24 15:12:50,128 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:12:50,128 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:12:50,129 - INFO - 
============================================================================================================================================
2025-09-24 15:12:50,129 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:12:50,129 - INFO - ============================================================================================================================================
2025-09-24 15:12:50,129 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:12:50,129 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:12:50,129 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:12:50,129 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:12:50,129 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:12:50,129 - INFO - 
2025-09-24 15:12:50,129 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:12:50,129 - INFO - Total entries: 1
2025-09-24 15:12:50,129 - INFO - ============================================================================================================================================
2025-09-24 15:12:50,129 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:12:50,130 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:12:50,130 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:12:50,130 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:12:50,130 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 18.225247, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
