2025-09-24 18:48:45,873 - INFO - Logging initialized. Log file: logs/test_classification_20250924_184845.log
2025-09-24 18:48:45,873 - INFO - 📁 Found 10 files to process
2025-09-24 18:48:45,873 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:48:45,873 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 18:48:45,873 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 18:48:45,873 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 18:48:45,873 - INFO - ⬆️ [18:48:45] Uploading: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:47,915 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf -> s3://document-extraction-logistically/temp/274add08_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:47,915 - INFO - 🔍 [18:48:47] Starting classification: G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:47,916 - INFO - ⬆️ [18:48:47] Uploading: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:47,921 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:47,937 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:47,943 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/274add08_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:47,943 - INFO - Processing PDF from S3...
2025-09-24 18:48:47,944 - INFO - Downloading PDF from S3 to /tmp/tmp6imwmzzv/274add08_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:48,943 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf -> s3://document-extraction-logistically/temp/ef471e76_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:48,943 - INFO - 🔍 [18:48:48] Starting classification: GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:48,944 - INFO - ⬆️ [18:48:48] Uploading: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:48,945 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:48,960 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:48,966 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ef471e76_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:48,967 - INFO - Processing PDF from S3...
2025-09-24 18:48:48,967 - INFO - Downloading PDF from S3 to /tmp/tmp62vzex8v/ef471e76_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:49,523 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:48:49,523 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:49,526 - INFO - Splitting PDF 274add08_G0H0K1LRWDOG7LXAKKQ7 into 1 pages
2025-09-24 18:48:49,534 - INFO - Split PDF into 1 pages
2025-09-24 18:48:49,534 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:49,534 - INFO - Expected pages: [1]
2025-09-24 18:48:49,756 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf -> s3://document-extraction-logistically/temp/7fdbe9a0_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:49,756 - INFO - 🔍 [18:48:49] Starting classification: KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:49,757 - INFO - ⬆️ [18:48:49] Uploading: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:49,757 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:49,768 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:49,772 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7fdbe9a0_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:49,772 - INFO - Processing PDF from S3...
2025-09-24 18:48:49,772 - INFO - Downloading PDF from S3 to /tmp/tmpdgto0j88/7fdbe9a0_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:50,479 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf -> s3://document-extraction-logistically/temp/0479612a_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:50,479 - INFO - 🔍 [18:48:50] Starting classification: MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:50,481 - INFO - ⬆️ [18:48:50] Uploading: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:48:50,484 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:50,511 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:50,515 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0479612a_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:50,516 - INFO - Processing PDF from S3...
2025-09-24 18:48:50,516 - INFO - Downloading PDF from S3 to /tmp/tmpw94ztlnl/0479612a_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:51,007 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:48:51,008 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:51,010 - INFO - Splitting PDF ef471e76_GKNF55W2CF2JR6EBXMPX into 1 pages
2025-09-24 18:48:51,015 - INFO - Split PDF into 1 pages
2025-09-24 18:48:51,015 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:51,015 - INFO - Expected pages: [1]
2025-09-24 18:48:51,263 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf -> s3://document-extraction-logistically/temp/239533cc_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:48:51,264 - INFO - 🔍 [18:48:51] Starting classification: NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:48:51,265 - INFO - ⬆️ [18:48:51] Uploading: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:51,270 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:51,285 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:51,291 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/239533cc_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:48:51,291 - INFO - Processing PDF from S3...
2025-09-24 18:48:51,292 - INFO - Downloading PDF from S3 to /tmp/tmptll11zrj/239533cc_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:48:51,303 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:48:51,304 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:51,307 - INFO - Splitting PDF 7fdbe9a0_KDFG6JJAZV41YZP4TEGN into 1 pages
2025-09-24 18:48:51,319 - INFO - Split PDF into 1 pages
2025-09-24 18:48:51,319 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:51,320 - INFO - Expected pages: [1]
2025-09-24 18:48:51,750 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:48:51,750 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:51,751 - INFO - Splitting PDF 0479612a_MMNWUYGBLL1K5KSJBNOT into 1 pages
2025-09-24 18:48:51,753 - INFO - Split PDF into 1 pages
2025-09-24 18:48:51,753 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:51,753 - INFO - Expected pages: [1]
2025-09-24 18:48:51,869 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf -> s3://document-extraction-logistically/temp/69a8fe97_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:51,869 - INFO - 🔍 [18:48:51] Starting classification: O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:51,870 - INFO - ⬆️ [18:48:51] Uploading: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:48:51,871 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:51,889 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:51,893 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/69a8fe97_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:51,893 - INFO - Processing PDF from S3...
2025-09-24 18:48:51,893 - INFO - Downloading PDF from S3 to /tmp/tmp57u6__cb/69a8fe97_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:52,630 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf -> s3://document-extraction-logistically/temp/bd5d7fdb_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:48:52,631 - INFO - 🔍 [18:48:52] Starting classification: OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:48:52,632 - INFO - ⬆️ [18:48:52] Uploading: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:48:52,633 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:52,692 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:52,695 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bd5d7fdb_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:48:52,695 - INFO - Processing PDF from S3...
2025-09-24 18:48:52,695 - INFO - Downloading PDF from S3 to /tmp/tmpqa9cgayw/bd5d7fdb_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:48:53,105 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:48:53,105 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:53,106 - INFO - Splitting PDF 239533cc_NFMA1926AJ8R3TDZQALU into 1 pages
2025-09-24 18:48:53,108 - INFO - Split PDF into 1 pages
2025-09-24 18:48:53,108 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:53,108 - INFO - Expected pages: [1]
2025-09-24 18:48:53,211 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:48:53,211 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:53,212 - INFO - Splitting PDF 69a8fe97_O1YJBQBLYAU6D0SDDKAU into 1 pages
2025-09-24 18:48:53,213 - INFO - Split PDF into 1 pages
2025-09-24 18:48:53,213 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:53,213 - INFO - Expected pages: [1]
2025-09-24 18:48:53,398 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf -> s3://document-extraction-logistically/temp/9d59ceee_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:48:53,399 - INFO - 🔍 [18:48:53] Starting classification: V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:48:53,400 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:53,403 - INFO - ⬆️ [18:48:53] Uploading: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:48:53,410 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:53,418 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9d59ceee_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:48:53,419 - INFO - Processing PDF from S3...
2025-09-24 18:48:53,419 - INFO - Downloading PDF from S3 to /tmp/tmpzqdkae31/9d59ceee_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:48:53,837 - INFO - Page 1: Extracted 667 characters, 55 lines from 274add08_G0H0K1LRWDOG7LXAKKQ7_89569335_page_001.pdf
2025-09-24 18:48:53,837 - INFO - Successfully processed page 1
2025-09-24 18:48:53,837 - INFO - Combined 1 pages into final text
2025-09-24 18:48:53,837 - INFO - Text validation for 274add08_G0H0K1LRWDOG7LXAKKQ7: 684 characters, 1 pages
2025-09-24 18:48:53,838 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:53,838 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:54,110 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:48:54,110 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:54,111 - INFO - Splitting PDF bd5d7fdb_OLQ7TIWW6EVTC6BXA1II into 1 pages
2025-09-24 18:48:54,116 - INFO - Split PDF into 1 pages
2025-09-24 18:48:54,116 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:54,116 - INFO - Expected pages: [1]
2025-09-24 18:48:54,328 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg -> s3://document-extraction-logistically/temp/0b042fc6_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:48:54,329 - INFO - 🔍 [18:48:54] Starting classification: Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:48:54,329 - INFO - ⬆️ [18:48:54] Uploading: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:48:54,331 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:54,345 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:54,349 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0b042fc6_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:48:54,349 - INFO - Processing image from S3...
2025-09-24 18:48:55,044 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg -> s3://document-extraction-logistically/temp/53afbe6f_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:48:55,044 - INFO - 🔍 [18:48:55] Starting classification: ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:48:55,046 - INFO - Initializing TextractProcessor...
2025-09-24 18:48:55,060 - INFO - Initializing BedrockProcessor...
2025-09-24 18:48:55,063 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/53afbe6f_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:48:55,064 - INFO - Processing image from S3...
2025-09-24 18:48:55,143 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:48:55,143 - INFO - Splitting PDF into individual pages...
2025-09-24 18:48:55,144 - INFO - Splitting PDF 9d59ceee_V3BCW6BW9XVKKO6WY2YJ into 1 pages
2025-09-24 18:48:55,144 - INFO - Split PDF into 1 pages
2025-09-24 18:48:55,144 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:48:55,144 - INFO - Expected pages: [1]
2025-09-24 18:48:55,222 - INFO - Page 1: Extracted 695 characters, 58 lines from 7fdbe9a0_KDFG6JJAZV41YZP4TEGN_2c8fb443_page_001.pdf
2025-09-24 18:48:55,222 - INFO - Successfully processed page 1
2025-09-24 18:48:55,223 - INFO - Combined 1 pages into final text
2025-09-24 18:48:55,223 - INFO - Text validation for 7fdbe9a0_KDFG6JJAZV41YZP4TEGN: 712 characters, 1 pages
2025-09-24 18:48:55,223 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:55,224 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:55,270 - INFO - Page 1: Extracted 372 characters, 28 lines from 0479612a_MMNWUYGBLL1K5KSJBNOT_b2cf5536_page_001.pdf
2025-09-24 18:48:55,270 - INFO - Successfully processed page 1
2025-09-24 18:48:55,271 - INFO - Combined 1 pages into final text
2025-09-24 18:48:55,271 - INFO - Text validation for 0479612a_MMNWUYGBLL1K5KSJBNOT: 389 characters, 1 pages
2025-09-24 18:48:55,271 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:55,271 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:55,661 - INFO - Page 1: Extracted 493 characters, 42 lines from ef471e76_GKNF55W2CF2JR6EBXMPX_91ee3a72_page_001.pdf
2025-09-24 18:48:55,662 - INFO - Successfully processed page 1
2025-09-24 18:48:55,662 - INFO - Combined 1 pages into final text
2025-09-24 18:48:55,662 - INFO - Text validation for ef471e76_GKNF55W2CF2JR6EBXMPX: 510 characters, 1 pages
2025-09-24 18:48:55,662 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:55,663 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:56,115 - INFO - Page 1: Extracted 311 characters, 32 lines from 69a8fe97_O1YJBQBLYAU6D0SDDKAU_920abbfd_page_001.pdf
2025-09-24 18:48:56,115 - INFO - Successfully processed page 1
2025-09-24 18:48:56,116 - INFO - Combined 1 pages into final text
2025-09-24 18:48:56,116 - INFO - Text validation for 69a8fe97_O1YJBQBLYAU6D0SDDKAU: 328 characters, 1 pages
2025-09-24 18:48:56,116 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:56,116 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:56,401 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '66248102-2a6c-455f-a188-cc4544a8959d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:18:56 GMT', 'content-type': 'application/json', 'content-length': '1359', 'connection': 'keep-alive', 'x-amzn-requestid': '66248102-2a6c-455f-a188-cc4544a8959d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We examine content: It is a receipt, with fields: Capstone Logistics, 770-414-1929, Receipt #, Location, Work Date, Bill Code, Carrier: BLUE STAR, Dock, Door, Purchase Orders, Vendor, Totals, etc. It looks like a "Receipt" maybe a "Lumper Receipt"? It has "Base Charge", "Convenience Fee", "Total Cost", "Payments". It lists "Total Initial Pallets", "Total Finished Pallets", "Total Case Count", "Total Weight". That matches lumper receipt: includes PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. So doc_type lumper_receipt. Let\'s double-check: lumper receipt: "Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount". Yes. So classify page1 as lumper_receipt. Use tool call classify_logistics_doc_type with documents array containing object {page_no:1, doc_type:"lumper_receipt"}.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2789, 'outputTokens': 263, 'totalTokens': 3052}, 'metrics': {'latencyMs': 1349}}
2025-09-24 18:48:56,402 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/274add08_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:56,419 - INFO - 

G0H0K1LRWDOG7LXAKKQ7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:48:56,420 - INFO - 

✓ Saved result: output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:48:56,718 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/274add08_G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:48:57,584 - INFO - Page 1: Extracted 460 characters, 36 lines from 239533cc_NFMA1926AJ8R3TDZQALU_82133f21_page_001.pdf
2025-09-24 18:48:57,584 - INFO - Successfully processed page 1
2025-09-24 18:48:57,585 - INFO - Combined 1 pages into final text
2025-09-24 18:48:57,585 - INFO - Text validation for 239533cc_NFMA1926AJ8R3TDZQALU: 477 characters, 1 pages
2025-09-24 18:48:57,586 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:57,586 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:57,909 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '78227a68-0be4-47d6-ba11-07ad84fe5bd8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:18:57 GMT', 'content-type': 'application/json', 'content-length': '1737', 'connection': 'keep-alive', 'x-amzn-requestid': '78227a68-0be4-47d6-ba11-07ad84fe5bd8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have only page1 text. Need to determine doc_type. Let\'s analyze keywords: "BOL Number", "PO #", "Driver Name", "Convenience Fee", "Grand Total". It\'s a receipt? The header: "Paid By", "Payment Method visa". It seems like a payment receipt for a BOL (Bill of Lading) or a driver? It could be a "lumper receipt" (invoice for services). The definition: lumper_receipt: keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has BOL Number, PO #, Driver Name, Amount. It could be lumper receipt. Also "Relay Sale#" maybe indicates payment for a relay sale. The doc_type might be "lumper_receipt" or "invoice". The difference: invoice: Bill issued by a carrier for goods being transported. lumper_receipt: receipt for services (loading/unloading labor). This page: "Services", "Convenience Fee", "Grand Total". That looks like a service receipt. So lumper_receipt. Check if any other classification: maybe "invoice" but it\'s a receipt to a driver for service. "BOL Number" indicates the load. So likely lumper_receipt. Also presence of PO # but not a purchase order. So I think lumper_receipt.\n\nWe must output JSON via tool call classify_logistics_doc_type with documents array: page_no 1, doc_type lumper_receipt.\n\nLet\'s call.'}}}, {'toolUse': {'toolUseId': 'tooluse_YAexLDihQ-e0WV_sh5ItYg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2677, 'outputTokens': 335, 'totalTokens': 3012}, 'metrics': {'latencyMs': 1519}}
2025-09-24 18:48:57,909 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0479612a_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:57,915 - INFO - 

MMNWUYGBLL1K5KSJBNOT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:48:57,915 - INFO - 

✓ Saved result: output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:48:57,965 - INFO - S3 Image temp/0b042fc6_Y9K6Z2GSAP496UQLOOGU.jpeg: Extracted 542 characters, 30 lines
2025-09-24 18:48:57,965 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:57,965 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:58,198 - INFO - Page 1: Extracted 657 characters, 54 lines from bd5d7fdb_OLQ7TIWW6EVTC6BXA1II_9a3b44ba_page_001.pdf
2025-09-24 18:48:58,198 - INFO - Successfully processed page 1
2025-09-24 18:48:58,199 - INFO - Combined 1 pages into final text
2025-09-24 18:48:58,199 - INFO - Text validation for bd5d7fdb_OLQ7TIWW6EVTC6BXA1II: 674 characters, 1 pages
2025-09-24 18:48:58,199 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:58,199 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:58,208 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0479612a_MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:48:58,412 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8e17e8fb-7623-4069-898f-06ec5db869ce', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:18:58 GMT', 'content-type': 'application/json', 'content-length': '2020', 'connection': 'keep-alive', 'x-amzn-requestid': '8e17e8fb-7623-4069-898f-06ec5db869ce'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to decide doc_type. Let\'s analyze text.\n\nKey terms: "Receipt #:" "Bill Code:" "Carrier:" "Purchase Orders" "Total Initial Pallets" "Total Finished Pallets" "Total Case Count" "Trailer Number" "Tractor Number" "BOL:" "Add Fee" "Base Charge" "Convenience Fee" "Total Cost" "Payments". This looks like a carrier invoice or a freight bill. It includes charges, payment details. It includes BOL but no cargo details. It seems to be a carrier invoice for a shipment. No HS codes or customs details. It\'s not a commercial invoice. It\'s not a lumper receipt? Lumper receipts have door number, date-time, initial pallets, finished pallets, carrier, service charge, total amount. This page has door number? "Door: 14" yes. It has initial and finished pallets. It has service charge? Base charge, convenience fee. Total cost. Payment. So could be lumper receipt or invoice. The instructions: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.) then comm_invoice; otherwise, invoice. Also check if it has some of keywords similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, then lumper_receipt; otherwise, invoice.\n\nThis page has Door number, date-time? It has "15:20:43 September 19, 2025" is date-time. It has initial pallets, finished pallets. Carrier. Service charge? It has convenience fee, add fee. Total amount. So matches lumper receipt. So doc_type: lumper_receipt.\n\nWe must output via tool call classify_logistics_doc_type with array of documents objects: page_no: 1, doc_type: "lumper_receipt".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2804, 'outputTokens': 418, 'totalTokens': 3222}, 'metrics': {'latencyMs': 2034}}
2025-09-24 18:48:58,413 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7fdbe9a0_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:58,432 - INFO - 

KDFG6JJAZV41YZP4TEGN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:48:58,432 - INFO - 

✓ Saved result: output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:48:58,696 - INFO - S3 Image temp/53afbe6f_ZOBA55ELUA2GRE3UJM3I.jpg: Extracted 709 characters, 59 lines
2025-09-24 18:48:58,696 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:58,696 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:58,704 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0290d3b6-ee51-4f28-a1eb-6046a5f6ce0d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:18:58 GMT', 'content-type': 'application/json', 'content-length': '1563', 'connection': 'keep-alive', 'x-amzn-requestid': '0290d3b6-ee51-4f28-a1eb-6046a5f6ce0d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine doc_type. The page shows a receipt from a trucking company: "TWS of Carteret LLC... Receipt# 15323... PO# 1685260... Carrier: RAF TRUCKING ... Invoice # 162340IDJC ... Activity: Total Unloading ... Fee Amount $275.00 ... Convenience Fee $9.62 ... Total $284.62". This looks like a lumper receipt? Let\'s check lumper receipt definition: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page includes PO#, Door number, Carrier, service charges, total amount. It\'s a receipt for unloading fees. So lumper_receipt seems appropriate. Also could be invoice? But invoice typically is a carrier invoice for goods being transported, includes line charges, total due, etc. This is more like a lumper receipt - paid for unloading service. The doc type list: lumper_receipt is separate. So classify as lumper_receipt.\n\nWe need to use the tool classify_logistics_doc_type with array of objects. Each object: page_no, doc_type. So output:\n\n{\n  documents: [\n    { page_no: 1, doc_type: "lumper_receipt" }\n  ]\n}\n\nWe need to output via the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2663, 'outputTokens': 312, 'totalTokens': 2975}, 'metrics': {'latencyMs': 1446}}
2025-09-24 18:48:58,705 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/69a8fe97_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:58,737 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7fdbe9a0_KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:48:58,749 - INFO - 

O1YJBQBLYAU6D0SDDKAU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:48:58,749 - INFO - 

✓ Saved result: output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:48:59,049 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/69a8fe97_O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:48:59,173 - INFO - Page 1: Extracted 343 characters, 28 lines from 9d59ceee_V3BCW6BW9XVKKO6WY2YJ_c9ea7369_page_001.pdf
2025-09-24 18:48:59,173 - INFO - Successfully processed page 1
2025-09-24 18:48:59,174 - INFO - Combined 1 pages into final text
2025-09-24 18:48:59,174 - INFO - Text validation for 9d59ceee_V3BCW6BW9XVKKO6WY2YJ: 360 characters, 1 pages
2025-09-24 18:48:59,175 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:48:59,175 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:48:59,563 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fb9c430c-d290-4c0c-80b2-07d5b0bf1d81', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:18:59 GMT', 'content-type': 'application/json', 'content-length': '2813', 'connection': 'keep-alive', 'x-amzn-requestid': 'fb9c430c-d290-4c0c-80b2-07d5b0bf1d81'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page: page1. We need to output via classify_logistics_doc_type tool. The page content: It\'s a document with details: IDSH, innovative distribution services, address, phone, Fed ID, status, manager, date, truck, trailer, door, carrier: Modus Logistics, PO(s):2644663, vendor(s): AMERCAREROYAL LLC, subtotal, convenience fee, total charge, billing type: Relay Checkout, payment type, driver name, signature. This looks like a "Relay Checkout" receipt? It\'s a payment receipt for relay checkout. It could be an invoice? But it\'s a checkout receipt. It might be an invoice? There\'s no "Invoice" heading, but it\'s a billing type. Could be a "lumper_receipt"? But lumper receipt includes PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page doesn\'t have door number? It has door:20. It has PO(s). It has driver name, signature. It has a total charge. It looks like a "lumper receipt" or maybe an "invoice". But lumper receipt is specifically for loading/unloading labor. This is a relay checkout: a service where the carrier picks up at a store? The fee is $5.00 convenience fee. This could be a "relay checkout" invoice? There\'s no "Invoice" heading. But the content shows total charge, driver signature. Might be a "clear to pay"? No.\n\nGiven definitions: invoice: Bill issued by a carrier for goods being transported. Has Invoice, Invoice No, Amount Due. This doesn\'t have those words. lumper_receipt: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO, Door number, date? Date: 09/19/2025. It has carrier: Modus Logistics. It has total amount. It might be a lumper receipt. It also has vendor(s) and billing type: Relay Checkout. But lumper receipt is a receipt for loading/unloading. This could be considered a relay checkout receipt, which is a service. I\'d classify as lumper_receipt.\n\nBut check if it\'s a "relay checkout" type? Not in list. Might be "invoice"? But invoice requires "Invoice" heading. Not present. So lumper_receipt seems plausible. Also we don\'t have "Initial pallets" or "finished pallets". But lumper receipt includes service charge, total amount. There\'s a convenience fee, total charge. So lumper_receipt.\n\nThus output: page_no:1, doc_type: lumper_receipt.\n\nWe must call the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2719, 'outputTokens': 594, 'totalTokens': 3313}, 'metrics': {'latencyMs': 2692}}
2025-09-24 18:48:59,564 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ef471e76_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:59,578 - INFO - 

GKNF55W2CF2JR6EBXMPX.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:48:59,579 - INFO - 

✓ Saved result: output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:48:59,872 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ef471e76_GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:48:59,967 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '89345d40-8261-41ae-a30b-aae4bf68b5a2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:18:59 GMT', 'content-type': 'application/json', 'content-length': '1359', 'connection': 'keep-alive', 'x-amzn-requestid': '89345d40-8261-41ae-a30b-aae4bf68b5a2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The page contains "PO No:" "Vendor", "Load No", etc. It looks like a "Lindar Form" maybe a shipping form? The keywords: "PO No:" indicates purchase order. But the document includes load info, trailer, driver. Could be a "Purchase Order"? Usually purchase order has buyer and seller, line items. This page lists PO No and vendor details. It might be a "Purchase Order". It includes "Qty", "Weight", "Charge", etc. That suggests order details. So doc_type "po".\n\nCheck if any other matching type: "invoice" would have Invoice No. Not present. "bol" would have Bill of Lading. Not present. "pod" has Proof of Delivery. Not present. "rate_confirmation" has Carrier Rate Confirmation. No. "scale_ticket" has weight and scale; but this has weight but also PO. Scale ticket has weight, gross, tare, net. Not here. "log" maybe not. So best is "po". The page likely is a purchase order for shipping. So output single object: page_no 1, doc_type "po".\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2720, 'outputTokens': 265, 'totalTokens': 2985}, 'metrics': {'latencyMs': 1262}}
2025-09-24 18:48:59,967 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/239533cc_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:48:59,983 - INFO - 

NFMA1926AJ8R3TDZQALU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 18:48:59,983 - INFO - 

✓ Saved result: output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:49:00,227 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fe31d908-9fa9-48f1-8bb0-bda0591f2f49', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:19:00 GMT', 'content-type': 'application/json', 'content-length': '793', 'connection': 'keep-alive', 'x-amzn-requestid': 'fe31d908-9fa9-48f1-8bb0-bda0591f2f49'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Determine doc type. The page content: It has "Receipt #" header, "Bill Code", "Carrier", "Purchase Orders", "Total Finished Pallets", "Total Case Count", "Base Charge", "Total Cost", "Payments", etc. Looks like an invoice? There\'s no HS codes etc. It\'s a carrier invoice. Contains billing info, cost, payments. So doc_type: invoice. Return array with single object: page_no 1, doc_type "invoice". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2786, 'outputTokens': 128, 'totalTokens': 2914}, 'metrics': {'latencyMs': 805}}
2025-09-24 18:49:00,227 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bd5d7fdb_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:49:00,273 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/239533cc_NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:49:00,297 - INFO - 

OLQ7TIWW6EVTC6BXA1II.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:49:00,297 - INFO - 

✓ Saved result: output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:49:00,347 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3a84ea36-7afa-4d23-a73e-57d9a12e3bbf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:19:00 GMT', 'content-type': 'application/json', 'content-length': '1236', 'connection': 'keep-alive', 'x-amzn-requestid': '3a84ea36-7afa-4d23-a73e-57d9a12e3bbf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Identify type. The content: "RECEIPT#" and "Total Due". It includes "Pallets Start", "Finish", "Cases", "Load/Trip#", "Driver", "Gate Out". This looks like a receipt for haul services, maybe a lumper receipt? The guidelines: lumper_receipt: Invoice or receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO, Door number? It has Door/Dock: 21 (GRO). It has Pallets Start:14 - Finish:19. It has Total Due: $200. So fits lumper_receipt. It\'s a receipt from TW Services for trucking services. Also "RECEIPT#" is present. Not a standard invoice maybe. So classification: lumper_receipt. There\'s only one page. So output JSON with documents array containing object page_no:1 doc_type:"lumper_receipt". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2768, 'outputTokens': 244, 'totalTokens': 3012}, 'metrics': {'latencyMs': 1187}}
2025-09-24 18:49:00,348 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0b042fc6_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:49:00,595 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bd5d7fdb_OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:49:00,608 - INFO - 

Y9K6Z2GSAP496UQLOOGU.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:49:00,608 - INFO - 

✓ Saved result: output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:49:00,900 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0b042fc6_Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:49:01,089 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '22cba4e5-47e2-46f6-94e4-b4c955627ec4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:19:00 GMT', 'content-type': 'application/json', 'content-length': '1355', 'connection': 'keep-alive', 'x-amzn-requestid': '22cba4e5-47e2-46f6-94e4-b4c955627ec4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to examine content to decide doc_type. Let\'s parse: It seems like a "receipt" for loading/unloading? Terms: "Receipt #", "Purchase Orders", "Total Initial Pallets", "Total Finished Pallets", "Add Fee", "Base Charge", "Convenience Fee", "Total Cost", "Payments", "CapstonePay". This looks like a lumper receipt (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. There is PO: 4511545251. It lists "Total Initial Pallets" and "Total Finished Pallets". That matches lumper receipt definition: "If has PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount" => lumper_receipt.\n\nAlso it has "Carrier: FLAWLESS". Yes.\n\nThus we classify as lumper_receipt.\n\nWe need to output via tool call classify_logistics_doc_type with array of documents: page_no 1, doc_type "lumper_receipt".\n\nReturn exactly that JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2803, 'outputTokens': 260, 'totalTokens': 3063}, 'metrics': {'latencyMs': 1238}}
2025-09-24 18:49:01,090 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/53afbe6f_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:49:01,119 - INFO - 

ZOBA55ELUA2GRE3UJM3I.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:49:01,119 - INFO - 

✓ Saved result: output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:49:01,481 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/53afbe6f_ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:49:01,495 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '443a0cf0-5fad-4fb8-bff6-e37798a0b9c9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:19:01 GMT', 'content-type': 'application/json', 'content-length': '1123', 'connection': 'keep-alive', 'x-amzn-requestid': '443a0cf0-5fad-4fb8-bff6-e37798a0b9c9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. Text: "Receipt number 128521 CAPE STONE LUMPERS, LLC ... GENERAL RECEIPT ... CASHIER NAME ... CUSTOMER NAME RCH TRUCKING ... Item(s) ... Quantity ... Price ... 5plts ... 140.00 ... SUBTOTAL ... TOTAL ... Payment Method: Cash." This is a receipt of a lumpy service? The definition for lumper_receipt: "Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount". This looks like a lumper receipt: it is a receipt for a service (likely a lumpers service). So doc_type should be lumper_receipt. The page is a single page. So output an array with one object: page_no:1, doc_type:"lumper_receipt".\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2674, 'outputTokens': 217, 'totalTokens': 2891}, 'metrics': {'latencyMs': 1141}}
2025-09-24 18:49:01,495 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9d59ceee_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:49:01,514 - INFO - 

V3BCW6BW9XVKKO6WY2YJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-24 18:49:01,514 - INFO - 

✓ Saved result: output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:49:01,804 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9d59ceee_V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:49:01,805 - INFO - 
📊 Processing Summary:
2025-09-24 18:49:01,805 - INFO -    Total files: 10
2025-09-24 18:49:01,805 - INFO -    Successful: 10
2025-09-24 18:49:01,805 - INFO -    Failed: 0
2025-09-24 18:49:01,805 - INFO -    Duration: 15.93 seconds
2025-09-24 18:49:01,806 - INFO -    Output directory: output
2025-09-24 18:49:01,806 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:49:01,806 - INFO -    📄 G0H0K1LRWDOG7LXAKKQ7.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 GKNF55W2CF2JR6EBXMPX.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 KDFG6JJAZV41YZP4TEGN.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 MMNWUYGBLL1K5KSJBNOT.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 NFMA1926AJ8R3TDZQALU.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 18:49:01,806 - INFO -    📄 O1YJBQBLYAU6D0SDDKAU.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 OLQ7TIWW6EVTC6BXA1II.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:49:01,806 - INFO -    📄 V3BCW6BW9XVKKO6WY2YJ.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 Y9K6Z2GSAP496UQLOOGU.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,806 - INFO -    📄 ZOBA55ELUA2GRE3UJM3I.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-24 18:49:01,807 - INFO - 
============================================================================================================================================
2025-09-24 18:49:01,807 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:49:01,807 - INFO - ============================================================================================================================================
2025-09-24 18:49:01,807 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:49:01,807 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:49:01,807 - INFO - G0H0K1LRWDOG7LXAKKQ7.pdf                           1      lumper_receipt                           run1_G0H0K1LRWDOG7LXAKKQ7.json                                                  
2025-09-24 18:49:01,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf
2025-09-24 18:49:01,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:49:01,808 - INFO - 
2025-09-24 18:49:01,808 - INFO - GKNF55W2CF2JR6EBXMPX.pdf                           1      lumper_receipt                           run1_GKNF55W2CF2JR6EBXMPX.json                                                  
2025-09-24 18:49:01,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf
2025-09-24 18:49:01,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:49:01,808 - INFO - 
2025-09-24 18:49:01,808 - INFO - KDFG6JJAZV41YZP4TEGN.pdf                           1      lumper_receipt                           run1_KDFG6JJAZV41YZP4TEGN.json                                                  
2025-09-24 18:49:01,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf
2025-09-24 18:49:01,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:49:01,808 - INFO - 
2025-09-24 18:49:01,808 - INFO - MMNWUYGBLL1K5KSJBNOT.pdf                           1      lumper_receipt                           run1_MMNWUYGBLL1K5KSJBNOT.json                                                  
2025-09-24 18:49:01,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf
2025-09-24 18:49:01,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:49:01,808 - INFO - 
2025-09-24 18:49:01,808 - INFO - NFMA1926AJ8R3TDZQALU.pdf                           1      po                                       run1_NFMA1926AJ8R3TDZQALU.json                                                  
2025-09-24 18:49:01,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf
2025-09-24 18:49:01,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:49:01,809 - INFO - 
2025-09-24 18:49:01,809 - INFO - O1YJBQBLYAU6D0SDDKAU.pdf                           1      lumper_receipt                           run1_O1YJBQBLYAU6D0SDDKAU.json                                                  
2025-09-24 18:49:01,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf
2025-09-24 18:49:01,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:49:01,809 - INFO - 
2025-09-24 18:49:01,809 - INFO - OLQ7TIWW6EVTC6BXA1II.pdf                           1      invoice                                  run1_OLQ7TIWW6EVTC6BXA1II.json                                                  
2025-09-24 18:49:01,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf
2025-09-24 18:49:01,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:49:01,809 - INFO - 
2025-09-24 18:49:01,809 - INFO - V3BCW6BW9XVKKO6WY2YJ.pdf                           1      lumper_receipt                           run1_V3BCW6BW9XVKKO6WY2YJ.json                                                  
2025-09-24 18:49:01,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf
2025-09-24 18:49:01,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:49:01,809 - INFO - 
2025-09-24 18:49:01,809 - INFO - Y9K6Z2GSAP496UQLOOGU.jpeg                          1      lumper_receipt                           run1_Y9K6Z2GSAP496UQLOOGU.json                                                  
2025-09-24 18:49:01,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg
2025-09-24 18:49:01,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:49:01,809 - INFO - 
2025-09-24 18:49:01,809 - INFO - ZOBA55ELUA2GRE3UJM3I.jpg                           1      lumper_receipt                           run1_ZOBA55ELUA2GRE3UJM3I.json                                                  
2025-09-24 18:49:01,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg
2025-09-24 18:49:01,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:49:01,810 - INFO - 
2025-09-24 18:49:01,810 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:49:01,810 - INFO - Total entries: 10
2025-09-24 18:49:01,810 - INFO - ============================================================================================================================================
2025-09-24 18:49:01,810 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:49:01,810 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:49:01,810 - INFO -   1. G0H0K1LRWDOG7LXAKKQ7.pdf            Page 1   → lumper_receipt  | run1_G0H0K1LRWDOG7LXAKKQ7.json
2025-09-24 18:49:01,810 - INFO -   2. GKNF55W2CF2JR6EBXMPX.pdf            Page 1   → lumper_receipt  | run1_GKNF55W2CF2JR6EBXMPX.json
2025-09-24 18:49:01,810 - INFO -   3. KDFG6JJAZV41YZP4TEGN.pdf            Page 1   → lumper_receipt  | run1_KDFG6JJAZV41YZP4TEGN.json
2025-09-24 18:49:01,810 - INFO -   4. MMNWUYGBLL1K5KSJBNOT.pdf            Page 1   → lumper_receipt  | run1_MMNWUYGBLL1K5KSJBNOT.json
2025-09-24 18:49:01,810 - INFO -   5. NFMA1926AJ8R3TDZQALU.pdf            Page 1   → po              | run1_NFMA1926AJ8R3TDZQALU.json
2025-09-24 18:49:01,810 - INFO -   6. O1YJBQBLYAU6D0SDDKAU.pdf            Page 1   → lumper_receipt  | run1_O1YJBQBLYAU6D0SDDKAU.json
2025-09-24 18:49:01,810 - INFO -   7. OLQ7TIWW6EVTC6BXA1II.pdf            Page 1   → invoice         | run1_OLQ7TIWW6EVTC6BXA1II.json
2025-09-24 18:49:01,810 - INFO -   8. V3BCW6BW9XVKKO6WY2YJ.pdf            Page 1   → lumper_receipt  | run1_V3BCW6BW9XVKKO6WY2YJ.json
2025-09-24 18:49:01,810 - INFO -   9. Y9K6Z2GSAP496UQLOOGU.jpeg           Page 1   → lumper_receipt  | run1_Y9K6Z2GSAP496UQLOOGU.json
2025-09-24 18:49:01,810 - INFO -  10. ZOBA55ELUA2GRE3UJM3I.jpg            Page 1   → lumper_receipt  | run1_ZOBA55ELUA2GRE3UJM3I.json
2025-09-24 18:49:01,810 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:49:01,810 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 15.931615, 'processed_files': [{'filename': 'G0H0K1LRWDOG7LXAKKQ7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/G0H0K1LRWDOG7LXAKKQ7.pdf'}, {'filename': 'GKNF55W2CF2JR6EBXMPX.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/GKNF55W2CF2JR6EBXMPX.pdf'}, {'filename': 'KDFG6JJAZV41YZP4TEGN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/KDFG6JJAZV41YZP4TEGN.pdf'}, {'filename': 'MMNWUYGBLL1K5KSJBNOT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/MMNWUYGBLL1K5KSJBNOT.pdf'}, {'filename': 'NFMA1926AJ8R3TDZQALU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/NFMA1926AJ8R3TDZQALU.pdf'}, {'filename': 'O1YJBQBLYAU6D0SDDKAU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/O1YJBQBLYAU6D0SDDKAU.pdf'}, {'filename': 'OLQ7TIWW6EVTC6BXA1II.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/OLQ7TIWW6EVTC6BXA1II.pdf'}, {'filename': 'V3BCW6BW9XVKKO6WY2YJ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/V3BCW6BW9XVKKO6WY2YJ.pdf'}, {'filename': 'Y9K6Z2GSAP496UQLOOGU.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/Y9K6Z2GSAP496UQLOOGU.jpeg'}, {'filename': 'ZOBA55ELUA2GRE3UJM3I.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/lumper_receipt/ZOBA55ELUA2GRE3UJM3I.jpg'}]}
