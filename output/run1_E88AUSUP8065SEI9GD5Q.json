{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.947020947933197, "Height": 0.4108607769012451, "Left": 0.014077258296310902, "Top": 0.196654811501503}, "Polygon": [{"X": 0.042655281722545624, "Y": 0.2056763470172882}, {"X": 0.9238666296005249, "Y": 0.196654811501503}, {"X": 0.9610982537269592, "Y": 0.6008718609809875}, {"X": 0.014077258296310902, "Y": 0.6075155735015869}]}, "Id": "0608de35-1ddd-4a56-8e48-9f16b1d56afb", "Relationships": [{"Type": "CHILD", "Ids": ["40392303-a160-4fd3-8b12-4b22b6ecfd66", "0ea085b3-051c-45e5-8cc6-8ec5c3972e57", "4a4e84ac-b55a-4eee-b623-7ec7506282d7", "9e9d6647-96c4-40eb-afd8-65bb32f30c2f", "cc543604-8463-4b06-8f4d-3f2843eb6d1a", "a19db26b-6cfd-4db3-bfc5-cc3b40ca2c62", "70c4480e-a56e-43d6-abf8-fb363a6a65e4", "a427371d-858c-4498-93b6-9b2723a5715d", "8ca94953-dc54-4ca8-b8af-0f5870e68220", "176ca330-43b3-4985-9fb2-bc8642933b89", "59816a7e-712e-4741-b583-9939597e0605", "b7b74dea-34cd-4bdc-ad0d-3faaf1c2b125", "9092d5d8-38ea-4da6-bda4-baa28ad0842c", "a42625b1-1542-4f7b-b945-7675af46f101", "02d3f238-d695-4d93-b7b7-dace9e721963", "2443c788-8830-4d14-b22f-365201acc274", "18d5848d-95c3-4199-ac53-369a7c3417d9", "f3fea987-ac47-4011-832f-32a1ea74d413", "998fceea-de13-4194-a177-971c561e5e59", "93591eec-0461-4bc1-84a1-b139c0b9152f", "fd3135df-0206-465d-ab34-2d6983b4c164", "dd5e23ac-42e4-4730-891c-404b0eee8625", "764f2582-a071-4356-a246-af7902729c09", "c23af7cb-c6dd-4029-9988-a9c10703509a", "eff1680f-5fa8-484d-b46c-7a1fab6f3fff", "9a58b37a-3d92-4143-9157-6ff1f5cb6bc6"]}]}, {"BlockType": "LINE", "Confidence": 99.76311492919922, "Text": "HUSKY Terminal And Stevedoring, Inc.", "Geometry": {"BoundingBox": {"Width": 0.32871338725090027, "Height": 0.024510497227311134, "Left": 0.29418638348579407, "Top": 0.23929980397224426}, "Polygon": [{"X": 0.29470324516296387, "Y": 0.24254851043224335}, {"X": 0.6221350431442261, "Y": 0.23929980397224426}, {"X": 0.6228997707366943, "Y": 0.2606048285961151}, {"X": 0.29418638348579407, "Y": 0.26381030678749084}]}, "Id": "40392303-a160-4fd3-8b12-4b22b6ecfd66", "Relationships": [{"Type": "CHILD", "Ids": ["36f43d69-787d-49fa-bcfd-a5f3960e36b1", "768f5bf1-dd8f-44ed-9907-603d781af587", "704285de-9599-4a49-a43f-2abf77e63085", "a851751c-a8a3-4a27-a306-89270a55e6b3", "5685aa17-6b0f-4cd0-93c4-5fe213772c41"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "YANG MING LINE", "Geometry": {"BoundingBox": {"Width": 0.17220331728458405, "Height": 0.014913094229996204, "Left": 0.3551764190196991, "Top": 0.2744426131248474}, "Polygon": [{"X": 0.3553498387336731, "Y": 0.2761000990867615}, {"X": 0.5271368622779846, "Y": 0.2744426131248474}, {"X": 0.5273797512054443, "Y": 0.2877124845981598}, {"X": 0.3551764190196991, "Y": 0.28935572504997253}]}, "Id": "0ea085b3-051c-45e5-8cc6-8ec5c3972e57", "Relationships": [{"Type": "CHILD", "Ids": ["f98e32be-0099-4d59-ac13-7fdb51587ac0", "28f303eb-55d9-4c5b-855d-084dc24cf1e5", "89fb0b3c-fae0-47be-b4ed-5effa713b9f9"]}]}, {"BlockType": "LINE", "Confidence": 99.54021453857422, "Text": "#776055", "Geometry": {"BoundingBox": {"Width": 0.20642389357089996, "Height": 0.04176092520356178, "Left": 0.682270884513855, "Top": 0.2479507029056549}, "Polygon": [{"X": 0.682270884513855, "Y": 0.24994730949401855}, {"X": 0.8853437304496765, "Y": 0.2479507029056549}, {"X": 0.8886948227882385, "Y": 0.2877653241157532}, {"X": 0.6841343641281128, "Y": 0.2897116243839264}]}, "Id": "4a4e84ac-b55a-4eee-b623-7ec7506282d7", "Relationships": [{"Type": "CHILD", "Ids": ["f63d03f2-249d-476c-a08a-d297401a96e9"]}]}, {"BlockType": "LINE", "Confidence": 80.92464447021484, "Text": "Lane : 45", "Geometry": {"BoundingBox": {"Width": 0.059744153171777725, "Height": 0.015353632159531116, "Left": 0.07409865409135818, "Top": 0.3013356328010559}, "Polygon": [{"X": 0.07504545152187347, "Y": 0.3018920421600342}, {"X": 0.1338428109884262, "Y": 0.3013356328010559}, {"X": 0.13305382430553436, "Y": 0.316138356924057}, {"X": 0.07409865409135818, "Y": 0.31668928265571594}]}, "Id": "9e9d6647-96c4-40eb-afd8-65bb32f30c2f", "Relationships": [{"Type": "CHILD", "Ids": ["61e43de7-2d78-4781-a39d-9dc6646e744e", "910d0c97-4a74-42a9-97f4-ac09bbf5350c"]}]}, {"BlockType": "LINE", "Confidence": 99.75505828857422, "Text": "FULL-OUT", "Geometry": {"BoundingBox": {"Width": 0.07837951183319092, "Height": 0.014653503894805908, "Left": 0.20256216824054718, "Top": 0.29705628752708435}, "Polygon": [{"X": 0.2031288892030716, "Y": 0.2977944612503052}, {"X": 0.2809416651725769, "Y": 0.29705628752708435}, {"X": 0.28057172894477844, "Y": 0.3109784722328186}, {"X": 0.20256216824054718, "Y": 0.31170979142189026}]}, "Id": "cc543604-8463-4b06-8f4d-3f2843eb6d1a", "Relationships": [{"Type": "CHILD", "Ids": ["51773c6c-9a69-489f-bc6a-3c22d37ade10"]}]}, {"BlockType": "LINE", "Confidence": 97.08431243896484, "Text": "** IMPORT-OUT EIR **", "Geometry": {"BoundingBox": {"Width": 0.2286672443151474, "Height": 0.015421790070831776, "Left": 0.3561108410358429, "Top": 0.29186999797821045}, "Polygon": [{"X": 0.35628142952919006, "Y": 0.29403820633888245}, {"X": 0.5843974947929382, "Y": 0.29186999797821045}, {"X": 0.5847780704498291, "Y": 0.3051425814628601}, {"X": 0.3561108410358429, "Y": 0.30729177594184875}]}, "Id": "a19db26b-6cfd-4db3-bfc5-cc3b40ca2c62", "Relationships": [{"Type": "CHILD", "Ids": ["8e6b07f2-45ae-4aec-bbea-b86378e4a4c5", "0b553a82-b84b-4dbc-a282-7717914970e9", "995dcc41-7a98-4f67-a45c-85fffc2c75a2", "a11ec7a5-202f-4b04-8f94-cb3aa5c5fc2d"]}]}, {"BlockType": "LINE", "Confidence": 98.43181610107422, "Text": "2025-07-15 09:10:18", "Geometry": {"BoundingBox": {"Width": 0.17108292877674103, "Height": 0.02406400814652443, "Left": 0.7215562462806702, "Top": 0.2818846106529236}, "Polygon": [{"X": 0.7215562462806702, "Y": 0.2835021913051605}, {"X": 0.8907375335693359, "Y": 0.2818846106529236}, {"X": 0.89263916015625, "Y": 0.30435484647750854}, {"X": 0.7227625846862793, "Y": 0.3059486150741577}]}, "Id": "70c4480e-a56e-43d6-abf8-fb363a6a65e4", "Relationships": [{"Type": "CHILD", "Ids": ["a3652a40-0d4f-41d7-88a1-b8c2b55dfe1f", "1f637328-12db-4917-a19c-b6cb9e255fcc"]}]}, {"BlockType": "LINE", "Confidence": 94.86418151855469, "Text": "Container: TLLU6009676", "Geometry": {"BoundingBox": {"Width": 0.1550125628709793, "Height": 0.02676522545516491, "Left": 0.07163473218679428, "Top": 0.3182167410850525}, "Polygon": [{"X": 0.07325880229473114, "Y": 0.3196464776992798}, {"X": 0.226647287607193, "Y": 0.3182167410850525}, {"X": 0.22572638094425201, "Y": 0.34357696771621704}, {"X": 0.07163473218679428, "Y": 0.34498196840286255}]}, "Id": "a427371d-858c-4498-93b6-9b2723a5715d", "Relationships": [{"Type": "CHILD", "Ids": ["7ad41778-2cca-4867-9515-cb6ec228b5ba", "05ae17ff-ea11-434b-af09-a639273d8595"]}]}, {"BlockType": "LINE", "Confidence": 79.63777923583984, "Text": "Size/Type: 42G1", "Geometry": {"BoundingBox": {"Width": 0.10951069742441177, "Height": 0.021378370001912117, "Left": 0.27460944652557373, "Top": 0.313035249710083}, "Polygon": [{"X": 0.27517011761665344, "Y": 0.314054012298584}, {"X": 0.3841201663017273, "Y": 0.313035249710083}, {"X": 0.3839617669582367, "Y": 0.333408921957016}, {"X": 0.27460944652557373, "Y": 0.33441361784935}]}, "Id": "8ca94953-dc54-4ca8-b8af-0f5870e68220", "Relationships": [{"Type": "CHILD", "Ids": ["2b7849a5-5d2d-4fb0-8c20-0d7de0636566", "25ba9e19-f20a-416c-b74a-ec9f029ab139"]}]}, {"BlockType": "LINE", "Confidence": 93.32389831542969, "Text": "Spot: I134", "Geometry": {"BoundingBox": {"Width": 0.0795719102025032, "Height": 0.02086741104722023, "Left": 0.47080814838409424, "Top": 0.3108387291431427}, "Polygon": [{"X": 0.47080814838409424, "Y": 0.31157898902893066}, {"X": 0.5499305725097656, "Y": 0.3108387291431427}, {"X": 0.5503800511360168, "Y": 0.3309759497642517}, {"X": 0.4709683060646057, "Y": 0.33170613646507263}]}, "Id": "176ca330-43b3-4985-9fb2-bc8642933b89", "Relationships": [{"Type": "CHILD", "Ids": ["9a930509-ef67-4ddb-8ea2-defea59ca748", "2aaddd37-e234-4f6f-a071-7d559a5d2d98"]}]}, {"BlockType": "LINE", "Confidence": 97.24539184570312, "Text": "Genset :", "Geometry": {"BoundingBox": {"Width": 0.05569101497530937, "Height": 0.018480472266674042, "Left": 0.6698780655860901, "Top": 0.31163638830184937}, "Polygon": [{"X": 0.6698780655860901, "Y": 0.3121472895145416}, {"X": 0.7245980501174927, "Y": 0.31163638830184937}, {"X": 0.7255690693855286, "Y": 0.3296121656894684}, {"X": 0.670670211315155, "Y": 0.330116868019104}]}, "Id": "59816a7e-712e-4741-b583-9939597e0605", "Relationships": [{"Type": "CHILD", "Ids": ["758044af-a72f-4184-a451-2029af76c709", "5eacccbf-3f72-4b97-94f1-9b8dc4761740"]}]}, {"BlockType": "LINE", "Confidence": 97.69265747070312, "Text": "Chassis : FB", "Geometry": {"BoundingBox": {"Width": 0.080093614757061, "Height": 0.023878445848822594, "Left": 0.07033389061689377, "Top": 0.3413667678833008}, "Polygon": [{"X": 0.071818508207798, "Y": 0.3420853316783905}, {"X": 0.15042750537395477, "Y": 0.3413667678833008}, {"X": 0.14927075803279877, "Y": 0.3645382821559906}, {"X": 0.07033389061689377, "Y": 0.36524519324302673}]}, "Id": "b7b74dea-34cd-4bdc-ad0d-3faaf1c2b125", "Relationships": [{"Type": "CHILD", "Ids": ["02c20fca-03cd-4c46-8cd5-913420f2e41e", "ad826d23-5fb8-4dd1-9de0-4b74408d346d", "2c098c8a-7b1e-47c7-9889-066b51ed4b3b"]}]}, {"BlockType": "LINE", "Confidence": 72.19926452636719, "Text": "Size/Type:", "Geometry": {"BoundingBox": {"Width": 0.06991810351610184, "Height": 0.019985230639576912, "Left": 0.27351734042167664, "Top": 0.3358630836009979}, "Polygon": [{"X": 0.2740519046783447, "Y": 0.33649942278862}, {"X": 0.3434354364871979, "Y": 0.3358630836009979}, {"X": 0.3431432545185089, "Y": 0.3552205562591553}, {"X": 0.27351734042167664, "Y": 0.3558483421802521}]}, "Id": "9092d5d8-38ea-4da6-bda4-baa28ad0842c", "Relationships": [{"Type": "CHILD", "Ids": ["22d98271-4dc8-4441-b563-9442655a007c"]}]}, {"BlockType": "LINE", "Confidence": 96.64361572265625, "Text": "Spot:", "Geometry": {"BoundingBox": {"Width": 0.03524015098810196, "Height": 0.01942971535027027, "Left": 0.4704185128211975, "Top": 0.3348763883113861}, "Polygon": [{"X": 0.4704185128211975, "Y": 0.33519697189331055}, {"X": 0.5053877830505371, "Y": 0.3348763883113861}, {"X": 0.5056586861610413, "Y": 0.3539898097515106}, {"X": 0.47056859731674194, "Y": 0.35430610179901123}]}, "Id": "a42625b1-1542-4f7b-b945-7675af46f101", "Relationships": [{"Type": "CHILD", "Ids": ["8ccd49bd-bdae-425d-8538-6c374510a38e"]}]}, {"BlockType": "LINE", "Confidence": 98.51901245117188, "Text": "Vessel : YM TUTORIAL", "Geometry": {"BoundingBox": {"Width": 0.15681906044483185, "Height": 0.02502487041056156, "Left": 0.06807311624288559, "Top": 0.36608901619911194}, "Polygon": [{"X": 0.06959085911512375, "Y": 0.36747705936431885}, {"X": 0.22489216923713684, "Y": 0.36608901619911194}, {"X": 0.2240329086780548, "Y": 0.38974952697753906}, {"X": 0.06807311624288559, "Y": 0.39111390709877014}]}, "Id": "02d3f238-d695-4d93-b7b7-dace9e721963", "Relationships": [{"Type": "CHILD", "Ids": ["b2761e87-8a11-4bdd-bc75-b6dac3f5fb8a", "f5671c82-b7f5-402d-9845-919a169fc71a", "d1fb8420-b23f-4af3-a275-c01ec07b57da", "ac113bb9-a53c-4824-ac0b-256a2ff864fe"]}]}, {"BlockType": "LINE", "Confidence": 86.88270568847656, "Text": "Voyage : 018W", "Geometry": {"BoundingBox": {"Width": 0.10098588466644287, "Height": 0.020374074578285217, "Left": 0.4161613881587982, "Top": 0.36037176847457886}, "Polygon": [{"X": 0.41619908809661865, "Y": 0.36127370595932007}, {"X": 0.5168322324752808, "Y": 0.36037176847457886}, {"X": 0.5171472430229187, "Y": 0.37985649704933167}, {"X": 0.4161613881587982, "Y": 0.38074585795402527}]}, "Id": "2443c788-8830-4d14-b22f-365201acc274", "Relationships": [{"Type": "CHILD", "Ids": ["136d25c7-5724-48bf-97db-846d55a671be", "95621fc9-c853-4eff-b726-fe4bdde13b28", "8b73ac11-c23f-423b-9d2f-df70ac611e0b"]}]}, {"BlockType": "LINE", "Confidence": 91.4403305053711, "Text": "Ref. No: W226681952", "Geometry": {"BoundingBox": {"Width": 0.1526869535446167, "Height": 0.023909270763397217, "Left": 0.6708279848098755, "Top": 0.35934239625930786}, "Polygon": [{"X": 0.6708279848098755, "Y": 0.36069440841674805}, {"X": 0.8219103813171387, "Y": 0.35934239625930786}, {"X": 0.8235149383544922, "Y": 0.3819214701652527}, {"X": 0.6718176007270813, "Y": 0.3832516670227051}]}, "Id": "18d5848d-95c3-4199-ac53-369a7c3417d9", "Relationships": [{"Type": "CHILD", "Ids": ["bb83913b-35db-4143-850d-a0f3f3e3b158", "a9447174-805c-4a9d-b7c5-940af3b5305b", "a328fe43-9c69-42df-b425-10e3a2f11e20"]}]}, {"BlockType": "LINE", "Confidence": 99.61355590820312, "Text": "Vent:", "Geometry": {"BoundingBox": {"Width": 0.034325163811445236, "Height": 0.016480790451169014, "Left": 0.435265451669693, "Top": 0.38332298398017883}, "Polygon": [{"X": 0.435265451669693, "Y": 0.3836233615875244}, {"X": 0.4694673717021942, "Y": 0.38332298398017883}, {"X": 0.46959060430526733, "Y": 0.39950695633888245}, {"X": 0.43528950214385986, "Y": 0.3998037576675415}]}, "Id": "f3fea987-ac47-4011-832f-32a1ea74d413", "Relationships": [{"Type": "CHILD", "Ids": ["8299cdbd-27c8-44d1-9e73-dd7f697919bc"]}]}, {"BlockType": "LINE", "Confidence": 79.33514404296875, "Text": "<PERSON><PERSON>. Temp :", "Geometry": {"BoundingBox": {"Width": 0.059895094484090805, "Height": 0.02008686028420925, "Left": 0.0663837268948555, "Top": 0.3948802053928375}, "Polygon": [{"X": 0.06764126569032669, "Y": 0.39539116621017456}, {"X": 0.126278817653656, "Y": 0.3948802053928375}, {"X": 0.1252260059118271, "Y": 0.4144635498523712}, {"X": 0.0663837268948555, "Y": 0.41496706008911133}]}, "Id": "998fceea-de13-4194-a177-971c561e5e59", "Relationships": [{"Type": "CHILD", "Ids": ["4fd5410e-bd54-4af1-978c-b66bcb43e7c9", "569ab00c-f5ae-4648-b611-6cd3ab4c9fe6"]}]}, {"BlockType": "LINE", "Confidence": 90.06536865234375, "Text": "Driver License: 116390010495", "Geometry": {"BoundingBox": {"Width": 0.20040859282016754, "Height": 0.024978935718536377, "Left": 0.06466715037822723, "Top": 0.****************}, "Polygon": [{"X": 0.06616333872079849, "Y": 0.****************}, {"X": 0.2650757431983948, "Y": 0.****************}, {"X": 0.2644033432006836, "Y": 0.*****************}, {"X": 0.06466715037822723, "Y": 0.****************}]}, "Id": "93591eec-0461-4bc1-84a1-b139c0b9152f", "Relationships": [{"Type": "CHILD", "Ids": ["fc795bb4-4163-45ae-a864-4314023554e3", "1821cadc-78fc-4643-9138-b7c1011bde14", "aed035dd-d561-4198-a62c-59678fd0e789"]}]}, {"BlockType": "LINE", "Confidence": 94.73348236083984, "Text": "Trucking Co: (CQBH) CARDINAL LOGISTICS 1 LLC", "Geometry": {"BoundingBox": {"Width": 0.39101290702819824, "Height": 0.024473637342453003, "Left": 0.3417694866657257, "Top": 0.****************}, "Polygon": [{"X": 0.34208929538726807, "Y": 0.*****************}, {"X": 0.731630802154541, "Y": 0.****************}, {"X": 0.7327824234962463, "Y": 0.****************}, {"X": 0.3417694866657257, "Y": 0.****************}]}, "Id": "fd3135df-0206-465d-ab34-2d6983b4c164", "Relationships": [{"Type": "CHILD", "Ids": ["1308c02c-675c-44b6-a2e6-a454339be956", "cb8b4f0f-665f-4598-88f4-7a6cfd739e0b", "5f9b3bc0-8f90-4a04-87ba-2fd6ec4e3dd1", "c7bfa05a-635c-479b-84ac-e02e96c17603", "da261fdc-d672-4c61-bf13-b6e2eed5ae32", "1efbcef2-b96d-46e7-af62-2447c019b9be", "e074c3c8-1f54-4660-98ad-3242b13717af"]}]}, {"BlockType": "LINE", "Confidence": 98.79838562011719, "Text": "Truck License : 86266RP", "Geometry": {"BoundingBox": {"Width": 0.15582846105098724, "Height": 0.022957544773817062, "Left": 0.06272165477275848, "Top": 0.4355649948120117}, "Polygon": [{"X": 0.06411658227443695, "Y": 0.4368593692779541}, {"X": 0.21855011582374573, "Y": 0.4355649948120117}, {"X": 0.21774789690971375, "Y": 0.45725011825561523}, {"X": 0.06272165477275848, "Y": 0.4585225284099579}]}, "Id": "dd5e23ac-42e4-4730-891c-404b0eee8625", "Relationships": [{"Type": "CHILD", "Ids": ["b56da456-21fc-4821-8f8f-ac07d2570115", "81edd62b-9cdc-4e52-8f4d-833321b610e3", "93f8c8cb-2caf-4f9d-9f14-75a0e8bbdf57", "449bcec9-6514-47eb-b34a-1b238871178d"]}]}, {"BlockType": "LINE", "Confidence": 99.43424224853516, "Text": "Truck Tag : 86266RP", "Geometry": {"BoundingBox": {"Width": 0.1620250642299652, "Height": 0.021605176851153374, "Left": 0.34015822410583496, "Top": 0.43117406964302063}, "Polygon": [{"X": 0.34046927094459534, "Y": 0.43252983689308167}, {"X": 0.5019133687019348, "Y": 0.43117406964302063}, {"X": 0.5021832585334778, "Y": 0.4514448940753937}, {"X": 0.34015822410583496, "Y": 0.45277926325798035}]}, "Id": "764f2582-a071-4356-a246-af7902729c09", "Relationships": [{"Type": "CHILD", "Ids": ["c610a3dc-4768-4483-9eb6-f5bdeb049f04", "5993a1d7-3855-49db-af94-88692107eaba", "4622cb20-e749-44ff-8263-b4a582e5f0ca", "9d33875a-b0f4-4a97-b9c1-3edcb8544c97"]}]}, {"BlockType": "LINE", "Confidence": 97.85475158691406, "Text": "SEALINTACT", "Geometry": {"BoundingBox": {"Width": 0.07593611627817154, "Height": 0.017747240141034126, "Left": 0.05983924865722656, "Top": 0.46860942244529724}, "Polygon": [{"X": 0.06094617396593094, "Y": 0.4692172110080719}, {"X": 0.1357753574848175, "Y": 0.46860942244529724}, {"X": 0.1348942071199417, "Y": 0.48575738072395325}, {"X": 0.05983924865722656, "Y": 0.4863566756248474}]}, "Id": "c23af7cb-c6dd-4029-9988-a9c10703509a", "Relationships": [{"Type": "CHILD", "Ids": ["1d7d4084-5f5a-4854-95b1-7ddbdad2db59"]}]}, {"BlockType": "LINE", "Confidence": 96.89901733398438, "Text": "CONTAINER GROSS WEIGHT(LBS): 48281 Exit with OWN chassis", "Geometry": {"BoundingBox": {"Width": 0.37685367465019226, "Height": 0.02284768968820572, "Left": 0.058541055768728256, "Top": 0.4816625118255615}, "Polygon": [{"X": 0.059823088347911835, "Y": 0.48466622829437256}, {"X": 0.4353652894496918, "Y": 0.4816625118255615}, {"X": 0.4353947341442108, "Y": 0.5015559792518616}, {"X": 0.058541055768728256, "Y": 0.504510223865509}]}, "Id": "eff1680f-5fa8-484d-b46c-7a1fab6f3fff", "Relationships": [{"Type": "CHILD", "Ids": ["c1e012c7-80f1-4add-8c5c-83ce57598a3b", "83aeb9dd-8f37-4271-9eba-5a06ef49da9c", "e859e4b9-fc14-46c9-8557-532f8e7581c5", "82731382-c9eb-406d-bbab-ce15a790ce3d", "5341445c-c5f2-4ff3-9d00-8396ab282838", "b07d0999-8c14-437f-9f52-d68c2957266e", "5ba25f9b-07e2-423f-b6f9-e9f2a22efd65", "98e23ab6-017e-4a7a-86b1-c50be94fe7c6"]}]}, {"BlockType": "LINE", "Confidence": 98.19808959960938, "Text": "Returning MTYs should beclean and free of debris", "Geometry": {"BoundingBox": {"Width": 0.26380056142807007, "Height": 0.023739038035273552, "Left": 0.05755586549639702, "Top": 0.5014238357543945}, "Polygon": [{"X": 0.05895518511533737, "Y": 0.50348299741745}, {"X": 0.3213564157485962, "Y": 0.5014238357543945}, {"X": 0.32095399498939514, "Y": 0.5231416821479797}, {"X": 0.05755586549639702, "Y": 0.5251628756523132}]}, "Id": "9a58b37a-3d92-4143-9157-6ff1f5cb6bc6", "Relationships": [{"Type": "CHILD", "Ids": ["c00c5d0f-d791-4ce0-bf5a-e5ebbe0669f2", "0304afd2-a39a-4285-aa32-a71ebf172b5e", "ccac212c-7ce8-4299-9f34-21cc30bfc2f8", "da642a46-f9e0-4f78-b218-c49a2d16587d", "7fb8ed3a-c3eb-43a7-a09a-c6566415a615", "9741ecbe-ff88-4341-bf45-fd842be0ac0e", "aa3e18ba-a6a1-4c31-a16e-dbfd7484d399", "3bd58766-052e-484f-9d9b-5c99cb28642e"]}]}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "HUSKY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04575730860233307, "Height": 0.017680859193205833, "Left": 0.2942313551902771, "Top": 0.24427954852581024}, "Polygon": [{"X": 0.29465022683143616, "Y": 0.24472859501838684}, {"X": 0.339988648891449, "Y": 0.24427954852581024}, {"X": 0.3397131860256195, "Y": 0.2615162134170532}, {"X": 0.2942313551902771, "Y": 0.2619604170322418}]}, "Id": "36f43d69-787d-49fa-bcfd-a5f3960e36b1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07085303217172623, "Height": 0.01785522699356079, "Left": 0.34848836064338684, "Top": 0.24277231097221375}, "Polygon": [{"X": 0.348734974861145, "Y": 0.24347202479839325}, {"X": 0.41934138536453247, "Y": 0.24277231097221375}, {"X": 0.419317364692688, "Y": 0.25993531942367554}, {"X": 0.34848836064338684, "Y": 0.26062753796577454}]}, "Id": "768f5bf1-dd8f-44ed-9907-603d781af587"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "And", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02857777662575245, "Height": 0.017637554556131363, "Left": 0.429445743560791, "Top": 0.2416222095489502}, "Polygon": [{"X": 0.429445743560791, "Y": 0.2419046014547348}, {"X": 0.4579247236251831, "Y": 0.2416222095489502}, {"X": 0.4580235183238983, "Y": 0.2589804232120514}, {"X": 0.42945367097854614, "Y": 0.25925976037979126}]}, "Id": "704285de-9599-4a49-a43f-2abf77e63085"}, {"BlockType": "WORD", "Confidence": 99.25323486328125, "Text": "<PERSON><PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10996320843696594, "Height": 0.022369664162397385, "Left": 0.46586599946022034, "Top": 0.239765003323555}, "Polygon": [{"X": 0.46586599946022034, "Y": 0.2408502846956253}, {"X": 0.5752484202384949, "Y": 0.239765003323555}, {"X": 0.5758292078971863, "Y": 0.2610638439655304}, {"X": 0.4660182595252991, "Y": 0.2621346712112427}]}, "Id": "a851751c-a8a3-4a27-a306-89270a55e6b3"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03457971662282944, "Height": 0.018085191026329994, "Left": 0.5881989598274231, "Top": 0.2394794076681137}, "Polygon": [{"X": 0.5881989598274231, "Y": 0.23981612920761108}, {"X": 0.6221414804458618, "Y": 0.2394794076681137}, {"X": 0.6227787137031555, "Y": 0.25723162293434143}, {"X": 0.588725209236145, "Y": 0.25756460428237915}]}, "Id": "5685aa17-6b0f-4cd0-93c4-5fe213772c41"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "YANG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05841294676065445, "Height": 0.013067977502942085, "Left": 0.3551764190196991, "Top": 0.2762877345085144}, "Polygon": [{"X": 0.35534003376960754, "Y": 0.2768494188785553}, {"X": 0.41358935832977295, "Y": 0.2762877345085144}, {"X": 0.41355881094932556, "Y": 0.28879860043525696}, {"X": 0.3551764190196991, "Y": 0.28935572504997253}]}, "Id": "f98e32be-0099-4d59-ac13-7fdb51587ac0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "MING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054356157779693604, "Height": 0.012916474603116512, "Left": 0.4198201894760132, "Top": 0.27548345923423767}, "Polygon": [{"X": 0.4198363125324249, "Y": 0.2760064899921417}, {"X": 0.47406965494155884, "Y": 0.27548345923423767}, {"X": 0.4741763472557068, "Y": 0.28788110613822937}, {"X": 0.4198201894760132, "Y": 0.28839993476867676}]}, "Id": "28f303eb-55d9-4c5b-855d-084dc24cf1e5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LINE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04583374410867691, "Height": 0.012962604872882366, "Left": 0.48153239488601685, "Top": 0.2744426131248474}, "Polygon": [{"X": 0.48153239488601685, "Y": 0.27488264441490173}, {"X": 0.5271368622779846, "Y": 0.2744426131248474}, {"X": 0.5273661017417908, "Y": 0.28696879744529724}, {"X": 0.4816572368144989, "Y": 0.28740522265434265}]}, "Id": "89fb0b3c-fae0-47be-b4ed-5effa713b9f9"}, {"BlockType": "WORD", "Confidence": 99.54021453857422, "Text": "#776055", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20642389357089996, "Height": 0.04176092520356178, "Left": 0.682270884513855, "Top": 0.2479507029056549}, "Polygon": [{"X": 0.682270884513855, "Y": 0.24994730949401855}, {"X": 0.8853437304496765, "Y": 0.2479507029056549}, {"X": 0.8886948227882385, "Y": 0.2877653241157532}, {"X": 0.6841343641281128, "Y": 0.2897116243839264}]}, "Id": "f63d03f2-249d-476c-a08a-d297401a96e9"}, {"BlockType": "WORD", "Confidence": 61.84928894042969, "Text": "Lane :", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032235968858003616, "Height": 0.015093314461410046, "Left": 0.07409865409135818, "Top": 0.3015959560871124}, "Polygon": [{"X": 0.07504545152187347, "Y": 0.3018920421600342}, {"X": 0.1063346192240715, "Y": 0.3015959560871124}, {"X": 0.10547178238630295, "Y": 0.3163960874080658}, {"X": 0.07409865409135818, "Y": 0.31668928265571594}]}, "Id": "61e43de7-2d78-4781-a39d-9dc6646e744e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "45", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01779336854815483, "Height": 0.010533533059060574, "Left": 0.11587703227996826, "Top": 0.3045702874660492}, "Polygon": [{"X": 0.11646222323179245, "Y": 0.30473268032073975}, {"X": 0.1336704045534134, "Y": 0.3045702874660492}, {"X": 0.13311755657196045, "Y": 0.3149425685405731}, {"X": 0.11587703227996826, "Y": 0.31510382890701294}]}, "Id": "910d0c97-4a74-42a9-97f4-ac09bbf5350c"}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "FULL-OUT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07837951183319092, "Height": 0.014653503894805908, "Left": 0.20256216824054718, "Top": 0.29705628752708435}, "Polygon": [{"X": 0.2031288892030716, "Y": 0.2977944612503052}, {"X": 0.2809416651725769, "Y": 0.29705628752708435}, {"X": 0.28057172894477844, "Y": 0.3109784722328186}, {"X": 0.20256216824054718, "Y": 0.31170979142189026}]}, "Id": "51773c6c-9a69-489f-bc6a-3c22d37ade10"}, {"BlockType": "WORD", "Confidence": 98.44567108154297, "Text": "**", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020086301490664482, "Height": 0.0072919647209346294, "Left": 0.35617393255233765, "Top": 0.2950970232486725}, "Polygon": [{"X": 0.3562653362751007, "Y": 0.2952868640422821}, {"X": 0.37626025080680847, "Y": 0.2950970232486725}, {"X": 0.37619468569755554, "Y": 0.3022000193595886}, {"X": 0.35617393255233765, "Y": 0.3023889660835266}]}, "Id": "8e6b07f2-45ae-4aec-bbea-b86378e4a4c5"}, {"BlockType": "WORD", "Confidence": 96.44591522216797, "Text": "IMPORT-OUT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13484448194503784, "Height": 0.014140788465738297, "Left": 0.3834901750087738, "Top": 0.292893648147583}, "Polygon": [{"X": 0.38359174132347107, "Y": 0.29417189955711365}, {"X": 0.5181209444999695, "Y": 0.292893648147583}, {"X": 0.5183346271514893, "Y": 0.3057670593261719}, {"X": 0.3834901750087738, "Y": 0.307034432888031}]}, "Id": "0b553a82-b84b-4dbc-a282-7717914970e9"}, {"BlockType": "WORD", "Confidence": 98.134765625, "Text": "EIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03300047665834427, "Height": 0.012855656445026398, "Left": 0.5235726833343506, "Top": 0.2921372652053833}, "Polygon": [{"X": 0.5235726833343506, "Y": 0.29244810342788696}, {"X": 0.5562776327133179, "Y": 0.2921372652053833}, {"X": 0.5565731525421143, "Y": 0.3046846389770508}, {"X": 0.5237933993339539, "Y": 0.3049929141998291}]}, "Id": "995dcc41-7a98-4f67-a45c-85fffc2c75a2"}, {"BlockType": "WORD", "Confidence": 95.31090545654297, "Text": "**", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020692894235253334, "Height": 0.007627409882843494, "Left": 0.5639187097549438, "Top": 0.2919025123119354}, "Polygon": [{"X": 0.5639187097549438, "Y": 0.29209718108177185}, {"X": 0.5843983888626099, "Y": 0.2919025123119354}, {"X": 0.5846115946769714, "Y": 0.2993362247943878}, {"X": 0.5641040802001953, "Y": 0.29952993988990784}]}, "Id": "a11ec7a5-202f-4b04-8f94-cb3aa5c5fc2d"}, {"BlockType": "WORD", "Confidence": 98.42694091796875, "Text": "2025-07-15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09758621454238892, "Height": 0.017759261652827263, "Left": 0.7215562462806702, "Top": 0.2825806438922882}, "Polygon": [{"X": 0.7215562462806702, "Y": 0.2835021913051605}, {"X": 0.8179405927658081, "Y": 0.2825806438922882}, {"X": 0.8191424608230591, "Y": 0.2994284927845001}, {"X": 0.7224611043930054, "Y": 0.3003399074077606}]}, "Id": "a3652a40-0d4f-41d7-88a1-b8c2b55dfe1f"}, {"BlockType": "WORD", "Confidence": 98.43669891357422, "Text": "09:10:18", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0719946026802063, "Height": 0.017969636246562004, "Left": 0.8206445574760437, "Top": 0.28704899549484253}, "Polygon": [{"X": 0.8206445574760437, "Y": 0.2877204418182373}, {"X": 0.8911746144294739, "Y": 0.28704899549484253}, {"X": 0.89263916015625, "Y": 0.30435484647750854}, {"X": 0.8218859434127808, "Y": 0.3050186336040497}]}, "Id": "1f637328-12db-4917-a19c-b6cb9e255fcc"}, {"BlockType": "WORD", "Confidence": 90.04643249511719, "Text": "Container:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06029193475842476, "Height": 0.01911337673664093, "Left": 0.07163473218679428, "Top": 0.3258686065673828}, "Polygon": [{"X": 0.07282484322786331, "Y": 0.3264162838459015}, {"X": 0.13192667067050934, "Y": 0.3258686065673828}, {"X": 0.1309347003698349, "Y": 0.3444412648677826}, {"X": 0.07163473218679428, "Y": 0.34498196840286255}]}, "Id": "7ad41778-2cca-4867-9515-cb6ec228b5ba"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "TLLU6009676", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09262965619564056, "Height": 0.02215966396033764, "Left": 0.13401763141155243, "Top": 0.3182167410850525}, "Polygon": [{"X": 0.13514460623264313, "Y": 0.31906965374946594}, {"X": 0.226647287607193, "Y": 0.3182167410850525}, {"X": 0.22587312757968903, "Y": 0.3395358920097351}, {"X": 0.13401763141155243, "Y": 0.3403764069080353}]}, "Id": "05ae17ff-ea11-434b-af09-a639273d8595"}, {"BlockType": "WORD", "Confidence": 82.31365966796875, "Text": "Size/Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06886574625968933, "Height": 0.019589990377426147, "Left": 0.27460944652557373, "Top": 0.31482362747192383}, "Polygon": [{"X": 0.27513134479522705, "Y": 0.3154619336128235}, {"X": 0.34347519278526306, "Y": 0.31482362747192383}, {"X": 0.34318807721138, "Y": 0.3337835371494293}, {"X": 0.27460944652557373, "Y": 0.33441361784935}]}, "Id": "2b7849a5-5d2d-4fb0-8c20-0d7de0636566"}, {"BlockType": "WORD", "Confidence": 76.9618911743164, "Text": "42G1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035565122961997986, "Height": 0.017726998776197433, "Left": 0.3485550284385681, "Top": 0.313035249710083}, "Polygon": [{"X": 0.34880173206329346, "Y": 0.31336551904678345}, {"X": 0.3841201663017273, "Y": 0.313035249710083}, {"X": 0.38398489356040955, "Y": 0.3304359018802643}, {"X": 0.3485550284385681, "Y": 0.33076226711273193}]}, "Id": "25ba9e19-f20a-416c-b74a-ec9f029ab139"}, {"BlockType": "WORD", "Confidence": 88.13237762451172, "Text": "Spot:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03502748906612396, "Height": 0.01910371705889702, "Left": 0.4708188772201538, "Top": 0.3126024007797241}, "Polygon": [{"X": 0.4708188772201538, "Y": 0.31292724609375}, {"X": 0.5055783987045288, "Y": 0.3126024007797241}, {"X": 0.505846381187439, "Y": 0.33138543367385864}, {"X": 0.4709683060646057, "Y": 0.33170613646507263}]}, "Id": "9a930509-ef67-4ddb-8ea2-defea59ca748"}, {"BlockType": "WORD", "Confidence": 98.51542663574219, "Text": "I134", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037167761474847794, "Height": 0.01798878237605095, "Left": 0.5131567716598511, "Top": 0.3108387291431427}, "Polygon": [{"X": 0.5131567716598511, "Y": 0.31118276715278625}, {"X": 0.5499305725097656, "Y": 0.3108387291431427}, {"X": 0.5503244996070862, "Y": 0.3284875452518463}, {"X": 0.5134328007698059, "Y": 0.32882750034332275}]}, "Id": "2aaddd37-e234-4f6f-a071-7d559a5d2d98"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "Genset", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04273748770356178, "Height": 0.016912423074245453, "Left": 0.6698780655860901, "Top": 0.3117562234401703}, "Polygon": [{"X": 0.6698780655860901, "Y": 0.3121472895145416}, {"X": 0.7117613554000854, "Y": 0.3117562234401703}, {"X": 0.7126155495643616, "Y": 0.328281968832016}, {"X": 0.6706063747406006, "Y": 0.32866865396499634}]}, "Id": "758044af-a72f-4184-a451-2029af76c709"}, {"BlockType": "WORD", "Confidence": 94.64704132080078, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004274645820260048, "Height": 0.011884729377925396, "Left": 0.7212944626808167, "Top": 0.3177609145641327}, "Polygon": [{"X": 0.7212944626808167, "Y": 0.31779468059539795}, {"X": 0.7249288558959961, "Y": 0.3177609145641327}, {"X": 0.7255690693855286, "Y": 0.3296121656894684}, {"X": 0.7219268083572388, "Y": 0.32964566349983215}]}, "Id": "5eacccbf-3f72-4b97-94f1-9b8dc4761740"}, {"BlockType": "WORD", "Confidence": 99.57369995117188, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041430313140153885, "Height": 0.01760946586728096, "Left": 0.07033389061689377, "Top": 0.34763574600219727}, "Polygon": [{"X": 0.07143920660018921, "Y": 0.34800243377685547}, {"X": 0.11176420003175735, "Y": 0.34763574600219727}, {"X": 0.11078394204378128, "Y": 0.3648829460144043}, {"X": 0.07033389061689377, "Y": 0.36524519324302673}]}, "Id": "02c20fca-03cd-4c46-8cd5-913420f2e41e"}, {"BlockType": "WORD", "Confidence": 94.12926483154297, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0039173299446702, "Height": 0.010316475294530392, "Left": 0.12592364847660065, "Top": 0.3502488434314728}, "Polygon": [{"X": 0.12648069858551025, "Y": 0.3502793312072754}, {"X": 0.12984097003936768, "Y": 0.3502488434314728}, {"X": 0.1292901337146759, "Y": 0.36053505539894104}, {"X": 0.12592364847660065, "Y": 0.36056533455848694}]}, "Id": "ad826d23-5fb8-4dd1-9de0-4b74408d346d"}, {"BlockType": "WORD", "Confidence": 99.375, "Text": "FB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019229698926210403, "Height": 0.017697498202323914, "Left": 0.13119781017303467, "Top": 0.3413667678833008}, "Polygon": [{"X": 0.13213080167770386, "Y": 0.3415340185165405}, {"X": 0.15042750537395477, "Y": 0.3413667678833008}, {"X": 0.14955227077007294, "Y": 0.3588990569114685}, {"X": 0.13119781017303467, "Y": 0.3590642511844635}]}, "Id": "2c098c8a-7b1e-47c7-9889-066b51ed4b3b"}, {"BlockType": "WORD", "Confidence": 72.19926452636719, "Text": "Size/Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06991810351610184, "Height": 0.019985230639576912, "Left": 0.27351734042167664, "Top": 0.3358630836009979}, "Polygon": [{"X": 0.2740519046783447, "Y": 0.33649942278862}, {"X": 0.3434354364871979, "Y": 0.3358630836009979}, {"X": 0.3431432545185089, "Y": 0.3552205562591553}, {"X": 0.27351734042167664, "Y": 0.3558483421802521}]}, "Id": "22d98271-4dc8-4441-b563-9442655a007c"}, {"BlockType": "WORD", "Confidence": 96.64361572265625, "Text": "Spot:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03524015098810196, "Height": 0.01942971535027027, "Left": 0.4704185128211975, "Top": 0.3348763883113861}, "Polygon": [{"X": 0.4704185128211975, "Y": 0.33519697189331055}, {"X": 0.5053877830505371, "Y": 0.3348763883113861}, {"X": 0.5056586861610413, "Y": 0.3539898097515106}, {"X": 0.47056859731674194, "Y": 0.35430610179901123}]}, "Id": "8ccd49bd-bdae-425d-8538-6c374510a38e"}, {"BlockType": "WORD", "Confidence": 98.81556701660156, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035038311034440994, "Height": 0.017768830060958862, "Left": 0.06807311624288559, "Top": 0.3733450770378113}, "Polygon": [{"X": 0.06919471174478531, "Y": 0.3736465275287628}, {"X": 0.10311143100261688, "Y": 0.3733450770378113}, {"X": 0.10209588706493378, "Y": 0.39081627130508423}, {"X": 0.06807311624288559, "Y": 0.39111390709877014}]}, "Id": "b2761e87-8a11-4bdd-bc75-b6dac3f5fb8a"}, {"BlockType": "WORD", "Confidence": 97.58550262451172, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004132245667278767, "Height": 0.010448681190609932, "Left": 0.12370231002569199, "Top": 0.3764324188232422}, "Polygon": [{"X": 0.12426795065402985, "Y": 0.37646403908729553}, {"X": 0.12783455848693848, "Y": 0.3764324188232422}, {"X": 0.12727557122707367, "Y": 0.3868497312068939}, {"X": 0.12370231002569199, "Y": 0.38688111305236816}]}, "Id": "f5671c82-b7f5-402d-9845-919a169fc71a"}, {"BlockType": "WORD", "Confidence": 97.72380828857422, "Text": "YM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018289368599653244, "Height": 0.017421621829271317, "Left": 0.13224950432777405, "Top": 0.3701830804347992}, "Polygon": [{"X": 0.1331605315208435, "Y": 0.37033790349960327}, {"X": 0.1505388617515564, "Y": 0.3701830804347992}, {"X": 0.14968161284923553, "Y": 0.38745176792144775}, {"X": 0.13224950432777405, "Y": 0.387604683637619}]}, "Id": "d1fb8420-b23f-4af3-a275-c01ec07b57da"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "TUTORIAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06850884109735489, "Height": 0.020465614274144173, "Left": 0.15638333559036255, "Top": 0.36608901619911194}, "Polygon": [{"X": 0.15734565258026123, "Y": 0.36669275164604187}, {"X": 0.22489216923713684, "Y": 0.36608901619911194}, {"X": 0.22417055070400238, "Y": 0.38595956563949585}, {"X": 0.15638333559036255, "Y": 0.38655465841293335}]}, "Id": "ac113bb9-a53c-4824-ac0b-256a2ff864fe"}, {"BlockType": "WORD", "Confidence": 69.9962158203125, "Text": "Voyage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045702047646045685, "Height": 0.019304832443594933, "Left": 0.4161613881587982, "Top": 0.3614410161972046}, "Polygon": [{"X": 0.4161979854106903, "Y": 0.36184900999069214}, {"X": 0.46174517273902893, "Y": 0.3614410161972046}, {"X": 0.4618634283542633, "Y": 0.38034334778785706}, {"X": 0.4161613881587982, "Y": 0.38074585795402527}]}, "Id": "136d25c7-5724-48bf-97db-846d55a671be"}, {"BlockType": "WORD", "Confidence": 90.74138641357422, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0036862152628600597, "Height": 0.011055211536586285, "Left": 0.46627262234687805, "Top": 0.365707665681839}, "Polygon": [{"X": 0.46627262234687805, "Y": 0.3657397925853729}, {"X": 0.4698738157749176, "Y": 0.365707665681839}, {"X": 0.4699588418006897, "Y": 0.3767310082912445}, {"X": 0.4663504958152771, "Y": 0.37676286697387695}]}, "Id": "95621fc9-c853-4eff-b726-fe4bdde13b28"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "018W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03922033682465553, "Height": 0.01799706369638443, "Left": 0.4778973162174225, "Top": 0.36037176847457886}, "Polygon": [{"X": 0.4778973162174225, "Y": 0.3607207238674164}, {"X": 0.5168322324752808, "Y": 0.36037176847457886}, {"X": 0.5171176195144653, "Y": 0.37802428007125854}, {"X": 0.4780590236186981, "Y": 0.3783688247203827}]}, "Id": "8b73ac11-c23f-423b-9d2f-df70ac611e0b"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "Ref.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02758263796567917, "Height": 0.016983691602945328, "Left": 0.6708279848098755, "Top": 0.36045488715171814}, "Polygon": [{"X": 0.6708279848098755, "Y": 0.36069440841674805}, {"X": 0.6975952982902527, "Y": 0.36045488715171814}, {"X": 0.6984106302261353, "Y": 0.3772018849849701}, {"X": 0.6715625524520874, "Y": 0.37743857502937317}]}, "Id": "bb83913b-35db-4143-850d-a0f3f3e3b158"}, {"BlockType": "WORD", "Confidence": 74.9328384399414, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024226324632763863, "Height": 0.016871675848960876, "Left": 0.7038466930389404, "Top": 0.36116403341293335}, "Polygon": [{"X": 0.7038466930389404, "Y": 0.36137259006500244}, {"X": 0.7271730303764343, "Y": 0.36116403341293335}, {"X": 0.728073000907898, "Y": 0.3778296113014221}, {"X": 0.7046765685081482, "Y": 0.37803569436073303}]}, "Id": "a9447174-805c-4a9d-b7c5-940af3b5305b"}, {"BlockType": "WORD", "Confidence": 99.59562683105469, "Text": "W226681952", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09336530417203903, "Height": 0.022082723677158356, "Left": 0.7301496267318726, "Top": 0.3606472611427307}, "Polygon": [{"X": 0.7301496267318726, "Y": 0.3614683151245117}, {"X": 0.8220030665397644, "Y": 0.3606472611427307}, {"X": 0.8235149383544922, "Y": 0.3819214701652527}, {"X": 0.7313091158866882, "Y": 0.38273000717163086}]}, "Id": "a328fe43-9c69-42df-b425-10e3a2f11e20"}, {"BlockType": "WORD", "Confidence": 99.61355590820312, "Text": "Vent:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034325163811445236, "Height": 0.016480790451169014, "Left": 0.435265451669693, "Top": 0.38332298398017883}, "Polygon": [{"X": 0.435265451669693, "Y": 0.3836233615875244}, {"X": 0.4694673717021942, "Y": 0.38332298398017883}, {"X": 0.46959060430526733, "Y": 0.39950695633888245}, {"X": 0.43528950214385986, "Y": 0.3998037576675415}]}, "Id": "8299cdbd-27c8-44d1-9e73-dd7f697919bc"}, {"BlockType": "WORD", "Confidence": 99.51271057128906, "Text": "Req.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02256167307496071, "Height": 0.017822962254285812, "Left": 0.0663837268948555, "Top": 0.3971441090106964}, "Polygon": [{"X": 0.06751668453216553, "Y": 0.39733049273490906}, {"X": 0.0889454036951065, "Y": 0.3971441090106964}, {"X": 0.08787979930639267, "Y": 0.4147831201553345}, {"X": 0.0663837268948555, "Y": 0.41496706008911133}]}, "Id": "4fd5410e-bd54-4af1-978c-b66bcb43e7c9"}, {"BlockType": "WORD", "Confidence": 59.157569885253906, "Text": "Temp :", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033596545457839966, "Height": 0.018659254536032677, "Left": 0.09268227219581604, "Top": 0.3948802053928375}, "Polygon": [{"X": 0.09377691894769669, "Y": 0.3951634168624878}, {"X": 0.126278817653656, "Y": 0.3948802053928375}, {"X": 0.12529070675373077, "Y": 0.4132601022720337}, {"X": 0.09268227219581604, "Y": 0.41353946924209595}]}, "Id": "569ab00c-f5ae-4648-b611-6cd3ab4c9fe6"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035934366285800934, "Height": 0.015749918296933174, "Left": 0.06466715037822723, "Top": 0.4225270748138428}, "Polygon": [{"X": 0.06566043943166733, "Y": 0.42282387614250183}, {"X": 0.10060151666402817, "Y": 0.4225270748138428}, {"X": 0.09970402717590332, "Y": 0.4379837214946747}, {"X": 0.06466715037822723, "Y": 0.****************}]}, "Id": "fc795bb4-4163-45ae-a864-4314023554e3"}, {"BlockType": "WORD", "Confidence": 70.52076721191406, "Text": "License:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050766270607709885, "Height": 0.015985647216439247, "Left": 0.10245184600353241, "Top": 0.4203087091445923}, "Polygon": [{"X": 0.10334809124469757, "Y": 0.4207330048084259}, {"X": 0.1532181203365326, "Y": 0.4203087091445923}, {"X": 0.15245968103408813, "Y": 0.4358751177787781}, {"X": 0.10245184600353241, "Y": 0.4362943470478058}]}, "Id": "1821cadc-78fc-4643-9138-b7c1011bde14"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "116390010495", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10553482919931412, "Height": 0.02101953513920307, "Left": 0.15954092144966125, "Top": 0.****************}, "Polygon": [{"X": 0.16049641370773315, "Y": 0.41419291496276855}, {"X": 0.2650757431983948, "Y": 0.****************}, {"X": 0.26449474692344666, "Y": 0.43343645334243774}, {"X": 0.15954092144966125, "Y": 0.43431758880615234}]}, "Id": "aed035dd-d561-4198-a62c-59678fd0e789"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Trucking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056982506066560745, "Height": 0.01989699713885784, "Left": 0.3417776823043823, "Top": 0.41187500953674316}, "Polygon": [{"X": 0.34207138419151306, "Y": 0.41236019134521484}, {"X": 0.3987601697444916, "Y": 0.41187500953674316}, {"X": 0.3986625373363495, "Y": 0.43129396438598633}, {"X": 0.3417776823043823, "Y": 0.43177199363708496}]}, "Id": "1308c02c-675c-44b6-a2e6-a454339be956"}, {"BlockType": "WORD", "Confidence": 65.00896453857422, "Text": "Co:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02281065471470356, "Height": 0.016202272847294807, "Left": 0.40255746245384216, "Top": 0.41173526644706726}, "Polygon": [{"X": 0.4026269018650055, "Y": 0.4119299054145813}, {"X": 0.42536813020706177, "Y": 0.41173526644706726}, {"X": 0.42536357045173645, "Y": 0.4277452826499939}, {"X": 0.40255746245384216, "Y": 0.4279375374317169}]}, "Id": "cb8b4f0f-665f-4598-88f4-7a6cfd739e0b"}, {"BlockType": "WORD", "Confidence": 99.0734634399414, "Text": "(CQBH)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057666484266519547, "Height": 0.018837785348296165, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "5f9b3bc0-8f90-4a04-87ba-2fd6ec4e3dd1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARDINAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07783041894435883, "Height": 0.019455436617136, "Left": 0.5013492107391357, "Top": 0.****************}, "Polygon": [{"X": 0.5013492107391357, "Y": 0.****************}, {"X": 0.5786709785461426, "Y": 0.****************}, {"X": 0.5791796445846558, "Y": 0.*****************}, {"X": 0.5015984773635864, "Y": 0.****************}]}, "Id": "c7bfa05a-635c-479b-84ac-e02e96c17603"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "LOGISTICS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08759083598852158, "Height": 0.01965608075261116, "Left": 0.5884990096092224, "Top": 0.****************}, "Polygon": [{"X": 0.5884990096092224, "Y": 0.*****************}, {"X": 0.6752519011497498, "Y": 0.****************}, {"X": 0.6760898232460022, "Y": 0.****************}, {"X": 0.5890437364578247, "Y": 0.****************}]}, "Id": "da261fdc-d672-4c61-bf13-b6e2eed5ae32"}, {"BlockType": "WORD", "Confidence": 99.13006591796875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006978979334235191, "Height": 0.017651498317718506, "Left": 0.6858505010604858, "Top": 0.41056135296821594}, "Polygon": [{"X": 0.6858505010604858, "Y": 0.41061389446258545}, {"X": 0.6919978260993958, "Y": 0.41056135296821594}, {"X": 0.6928294897079468, "Y": 0.42816099524497986}, {"X": 0.6866627931594849, "Y": 0.42821285128593445}]}, "Id": "1efbcef2-b96d-46e7-af62-2447c019b9be"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029858572408556938, "Height": 0.018786072731018066, "Left": 0.7029238343238831, "Top": 0.41048502922058105}, "Polygon": [{"X": 0.7029238343238831, "Y": 0.41073161363601685}, {"X": 0.731774628162384, "Y": 0.41048502922058105}, {"X": 0.7327824234962463, "Y": 0.****************}, {"X": 0.7038359642028809, "Y": 0.4292711019515991}]}, "Id": "e074c3c8-1f54-4660-98ad-3242b13717af"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02956780605018139, "Height": 0.017113836482167244, "Left": 0.06272165477275848, "Top": 0.4414086937904358}, "Polygon": [{"X": 0.06380834430456161, "Y": 0.4416463375091553}, {"X": 0.09228946268558502, "Y": 0.4414086937904358}, {"X": 0.0912877693772316, "Y": 0.4582880735397339}, {"X": 0.06272165477275848, "Y": 0.4585225284099579}]}, "Id": "b56da456-21fc-4821-8f8f-ac07d2570115"}, {"BlockType": "WORD", "Confidence": 99.36443328857422, "Text": "License", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042909350246191025, "Height": 0.015935249626636505, "Left": 0.0943496897816658, "Top": 0.4410080015659332}, "Polygon": [{"X": 0.09526634961366653, "Y": 0.441358357667923}, {"X": 0.13725903630256653, "Y": 0.4410080015659332}, {"X": 0.136458158493042, "Y": 0.4565971791744232}, {"X": 0.0943496897816658, "Y": 0.45694324374198914}]}, "Id": "81edd62b-9cdc-4e52-8f4d-833321b610e3"}, {"BlockType": "WORD", "Confidence": 97.46013641357422, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004233696963638067, "Height": 0.01112935971468687, "Left": 0.1469516009092331, "Top": 0.44342875480651855}, "Polygon": [{"X": 0.1475013792514801, "Y": 0.4434594213962555}, {"X": 0.1511853039264679, "Y": 0.44342875480651855}, {"X": 0.1506427377462387, "Y": 0.45452770590782166}, {"X": 0.1469516009092331, "Y": 0.4545581042766571}]}, "Id": "93f8c8cb-2caf-4f9d-9f14-75a0e8bbdf57"}, {"BlockType": "WORD", "Confidence": 98.60491180419922, "Text": "86266RP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06145690754055977, "Height": 0.01938726007938385, "Left": 0.15709319710731506, "Top": 0.4355649948120117}, "Polygon": [{"X": 0.15799449384212494, "Y": 0.43607252836227417}, {"X": 0.21855011582374573, "Y": 0.4355649948120117}, {"X": 0.2178514003753662, "Y": 0.45445218682289124}, {"X": 0.15709319710731506, "Y": 0.45495226979255676}]}, "Id": "449bcec9-6514-47eb-b34a-1b238871178d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03564866632223129, "Height": 0.016794079914689064, "Left": 0.34020358324050903, "Top": 0.43303197622299194}, "Polygon": [{"X": 0.3404569923877716, "Y": 0.4333289861679077}, {"X": 0.3758522570133209, "Y": 0.43303197622299194}, {"X": 0.37570247054100037, "Y": 0.4495328366756439}, {"X": 0.34020358324050903, "Y": 0.44982603192329407}]}, "Id": "c610a3dc-4768-4483-9eb6-f5bdeb049f04"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Tag", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021816061809659004, "Height": 0.019414393231272697, "Left": 0.38010212779045105, "Top": 0.4330359101295471}, "Polygon": [{"X": 0.38026162981987, "Y": 0.4332175850868225}, {"X": 0.4019181728363037, "Y": 0.4330359101295471}, {"X": 0.401832640171051, "Y": 0.45227134227752686}, {"X": 0.38010212779045105, "Y": 0.45245030522346497}]}, "Id": "5993a1d7-3855-49db-af94-88692107eaba"}, {"BlockType": "WORD", "Confidence": 98.12340545654297, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.003679388202726841, "Height": 0.011189992539584637, "Left": 0.42117300629615784, "Top": 0.43748024106025696}, "Polygon": [{"X": 0.4211844503879547, "Y": 0.4375108778476715}, {"X": 0.4248524010181427, "Y": 0.43748024106025696}, {"X": 0.4248482286930084, "Y": 0.4486398696899414}, {"X": 0.42117300629615784, "Y": 0.44867023825645447}]}, "Id": "4622cb20-e749-44ff-8263-b4a582e5f0ca"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "86266RP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06819747388362885, "Height": 0.018774600699543953, "Left": 0.43395841121673584, "Top": 0.43117406964302063}, "Polygon": [{"X": 0.43395841121673584, "Y": 0.4317447245121002}, {"X": 0.5019133687019348, "Y": 0.43117406964302063}, {"X": 0.5021559000015259, "Y": 0.4493860900402069}, {"X": 0.4339810311794281, "Y": 0.44994866847991943}]}, "Id": "9d33875a-b0f4-4a97-b9c1-3edcb8544c97"}, {"BlockType": "WORD", "Confidence": 97.85475158691406, "Text": "SEALINTACT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07593611627817154, "Height": 0.017747240141034126, "Left": 0.05983924865722656, "Top": 0.46860942244529724}, "Polygon": [{"X": 0.06094617396593094, "Y": 0.4692172110080719}, {"X": 0.1357753574848175, "Y": 0.46860942244529724}, {"X": 0.1348942071199417, "Y": 0.48575738072395325}, {"X": 0.05983924865722656, "Y": 0.4863566756248474}]}, "Id": "1d7d4084-5f5a-4854-95b1-7ddbdad2db59"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "CONTAINER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06986116617918015, "Height": 0.017048422247171402, "Left": 0.05855884030461311, "Top": 0.4871865212917328}, "Polygon": [{"X": 0.05962482467293739, "Y": 0.4877350628376007}, {"X": 0.12842001020908356, "Y": 0.4871865212917328}, {"X": 0.12755317986011505, "Y": 0.5036939382553101}, {"X": 0.05855884030461311, "Y": 0.5042349696159363}]}, "Id": "c1e012c7-80f1-4add-8c5c-83ce57598a3b"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04215168207883835, "Height": 0.015815526247024536, "Left": 0.12945736944675446, "Top": 0.4861313998699188}, "Polygon": [{"X": 0.13026560842990875, "Y": 0.4864612817764282}, {"X": 0.1716090440750122, "Y": 0.4861313998699188}, {"X": 0.1709131896495819, "Y": 0.5016213059425354}, {"X": 0.12945736944675446, "Y": 0.5019469261169434}]}, "Id": "83aeb9dd-8f37-4271-9eba-5a06ef49da9c"}, {"BlockType": "WORD", "Confidence": 93.43590545654297, "Text": "WEIGHT(LBS):", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09169911593198776, "Height": 0.019571872428059578, "Left": 0.1722344011068344, "Top": 0.4840470552444458}, "Polygon": [{"X": 0.17307636141777039, "Y": 0.48477303981781006}, {"X": 0.2639335095882416, "Y": 0.4840470552444458}, {"X": 0.26339247822761536, "Y": 0.5029043555259705}, {"X": 0.1722344011068344, "Y": 0.5036189556121826}]}, "Id": "e859e4b9-fc14-46c9-8557-532f8e7581c5"}, {"BlockType": "WORD", "Confidence": 97.021484375, "Text": "48281", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03275652602314949, "Height": 0.01564900390803814, "Left": 0.26610004901885986, "Top": 0.48349225521087646}, "Polygon": [{"X": 0.26653459668159485, "Y": 0.483750581741333}, {"X": 0.29885655641555786, "Y": 0.48349225521087646}, {"X": 0.29850947856903076, "Y": 0.49888619780540466}, {"X": 0.26610004901885986, "Y": 0.49914124608039856}]}, "Id": "82731382-c9eb-406d-bbab-ce15a790ce3d"}, {"BlockType": "WORD", "Confidence": 96.78631591796875, "Text": "Exit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023651055991649628, "Height": 0.01612252928316593, "Left": 0.30081191658973694, "Top": 0.4825495481491089}, "Polygon": [{"X": 0.30116480588912964, "Y": 0.48273590207099915}, {"X": 0.3244629502296448, "Y": 0.4825495481491089}, {"X": 0.3241753578186035, "Y": 0.498488187789917}, {"X": 0.30081191658973694, "Y": 0.4986720681190491}]}, "Id": "5341445c-c5f2-4ff3-9d00-8396ab282838"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025067206472158432, "Height": 0.015804845839738846, "Left": 0.32606983184814453, "Top": 0.482534259557724}, "Polygon": [{"X": 0.3263462483882904, "Y": 0.48273250460624695}, {"X": 0.35113704204559326, "Y": 0.482534259557724}, {"X": 0.3509286642074585, "Y": 0.49814343452453613}, {"X": 0.32606983184814453, "Y": 0.49833911657333374}]}, "Id": "b07d0999-8c14-437f-9f52-d68c2957266e"}, {"BlockType": "WORD", "Confidence": 94.052734375, "Text": "OWN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035331957042217255, "Height": 0.016149211674928665, "Left": 0.3537692427635193, "Top": 0.482139527797699}, "Polygon": [{"X": 0.3539731502532959, "Y": 0.4824204742908478}, {"X": 0.38910120725631714, "Y": 0.482139527797699}, {"X": 0.3889954090118408, "Y": 0.4980114996433258}, {"X": 0.3537692427635193, "Y": 0.49828875064849854}]}, "Id": "5ba25f9b-07e2-423f-b6f9-e9f2a22efd65"}, {"BlockType": "WORD", "Confidence": 94.53125, "Text": "chassis", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04509374871850014, "Height": 0.016478028148412704, "Left": 0.39029571413993835, "Top": 0.4818693995475769}, "Polygon": [{"X": 0.3903994560241699, "Y": 0.48222899436950684}, {"X": 0.43536561727523804, "Y": 0.4818693995475769}, {"X": 0.4353894591331482, "Y": 0.4979926645755768}, {"X": 0.39029571413993835, "Y": 0.4983474314212799}]}, "Id": "98e23ab6-017e-4a7a-86b1-c50be94fe7c6"}, {"BlockType": "WORD", "Confidence": 97.34136199951172, "Text": "Returning", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04819671809673309, "Height": 0.019219567999243736, "Left": 0.05755586549639702, "Top": 0.5059432983398438}, "Polygon": [{"X": 0.05877265706658363, "Y": 0.5063109397888184}, {"X": 0.10575258731842041, "Y": 0.5059432983398438}, {"X": 0.10469064861536026, "Y": 0.5248011946678162}, {"X": 0.05755586549639702, "Y": 0.5251628756523132}]}, "Id": "c00c5d0f-d791-4ce0-bf5a-e5ebbe0669f2"}, {"BlockType": "WORD", "Confidence": 99.36224365234375, "Text": "MTYs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03389498218894005, "Height": 0.015729738399386406, "Left": 0.10660161077976227, "Top": 0.5049812197685242}, "Polygon": [{"X": 0.10746828466653824, "Y": 0.5052398443222046}, {"X": 0.14049659669399261, "Y": 0.5049812197685242}, {"X": 0.13971930742263794, "Y": 0.5204557180404663}, {"X": 0.10660161077976227, "Y": 0.5207109451293945}]}, "Id": "0304afd2-a39a-4285-aa32-a71ebf172b5e"}, {"BlockType": "WORD", "Confidence": 99.453125, "Text": "should", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03642333671450615, "Height": 0.01596985198557377, "Left": 0.14095833897590637, "Top": 0.5041663646697998}, "Polygon": [{"X": 0.14174312353134155, "Y": 0.5044455528259277}, {"X": 0.17738166451454163, "Y": 0.5041663646697998}, {"X": 0.17669475078582764, "Y": 0.5198607444763184}, {"X": 0.14095833897590637, "Y": 0.5201361775398254}]}, "Id": "ccac212c-7ce8-4299-9f34-21cc30bfc2f8"}, {"BlockType": "WORD", "Confidence": 96.8702163696289, "Text": "beclean", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04399213194847107, "Height": 0.015101710334420204, "Left": 0.17874391376972198, "Top": 0.5037275552749634}, "Polygon": [{"X": 0.17938484251499176, "Y": 0.5040672421455383}, {"X": 0.22273604571819305, "Y": 0.5037275552749634}, {"X": 0.22220714390277863, "Y": 0.5184938907623291}, {"X": 0.17874391376972198, "Y": 0.5188292860984802}]}, "Id": "da642a46-f9e0-4f78-b218-c49a2d16587d"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02081644907593727, "Height": 0.015294710174202919, "Left": 0.22393706440925598, "Top": 0.5031294226646423}, "Polygon": [{"X": 0.22447460889816284, "Y": 0.5032883882522583}, {"X": 0.24475350975990295, "Y": 0.5031294226646423}, {"X": 0.2442697137594223, "Y": 0.5182672142982483}, {"X": 0.22393706440925598, "Y": 0.5184241533279419}]}, "Id": "7fb8ed3a-c3eb-43a7-a09a-c6566415a615"}, {"BlockType": "WORD", "Confidence": 93.3203125, "Text": "free", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02362963929772377, "Height": 0.015392315573990345, "Left": 0.24665601551532745, "Top": 0.5025742053985596}, "Polygon": [{"X": 0.24713581800460815, "Y": 0.5027557611465454}, {"X": 0.2702856659889221, "Y": 0.5025742053985596}, {"X": 0.26986753940582275, "Y": 0.5177873373031616}, {"X": 0.24665601551532745, "Y": 0.5179665088653564}]}, "Id": "9741ecbe-ff88-4341-bf45-fd842be0ac0e"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01366075687110424, "Height": 0.01577802002429962, "Left": 0.27142348885536194, "Top": 0.5021104216575623}, "Polygon": [{"X": 0.27184998989105225, "Y": 0.502214252948761}, {"X": 0.28508424758911133, "Y": 0.5021104216575623}, {"X": 0.2846940755844116, "Y": 0.5177860260009766}, {"X": 0.27142348885536194, "Y": 0.5178884267807007}]}, "Id": "aa3e18ba-a6a1-4c31-a16e-dbfd7484d399"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "debris", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03656354546546936, "Height": 0.016243640333414078, "Left": 0.28479287028312683, "Top": 0.5014238357543945}, "Polygon": [{"X": 0.28518983721733093, "Y": 0.5017076730728149}, {"X": 0.3213564157485962, "Y": 0.5014238357543945}, {"X": 0.3210606276988983, "Y": 0.5173875093460083}, {"X": 0.28479287028312683, "Y": 0.5176674723625183}]}, "Id": "3bd58766-052e-484f-9d9b-5c99cb28642e"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "52345b10-9f79-4ec4-867b-11ec573c2583", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "52345b10-9f79-4ec4-867b-11ec573c2583", "content-type": "application/x-amz-json-1.1", "content-length": "55574", "date": "Wed, 24 Sep 2025 13:32:21 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "db72723e-7f20-4566-afda-8a023dafa9fa", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:25 GMT", "content-type": "application/json", "content-length": "1598", "connection": "keep-alive", "x-amzn-requestid": "db72723e-7f20-4566-afda-8a023dafa9fa"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. We need to classify. The page content: HUSKY Terminal And Stevedoring, Inc. YANG MING LINE. Looks like an Export Inward or Outbound? The header says \"FULL-OUT\" \"IMPORT-OUT EIR\". It has container info, vessel, voyage, driver license, trucking co, truck license, etc. This looks like a gate-out document, specifically an Outgate document. Let's check definitions.\n\nOutgate: Record of vehicle/container exiting a facility. Keywords: Outgate, Departed, Gate Out, Full out, etc. The page has \"FULL-OUT\" which indicates full out. Also has driver license, truck license, etc. So it's an outgate. There's also \"IMPORT-OUT EIR\". EIR stands for Export Inward Receipt? But \"IMPORT-OUT EIR\" maybe means EIR for import out? Not sure.\n\nBut we have \"FULL-OUT\" and \"CONTAINER GROSS WEIGHT(LBS)\" so it's a gate-out. Outgate doc type.\n\nWe must output JSON with documents array containing one object for page 1 with doc_type \"outgate\". Let's ensure no other classification fits better. \"scale_ticket\" would involve weight but also scale location etc. This page includes weight but also container and driver license, etc. The presence of \"FULL-OUT\" indicates outgate. Outgate doc is more likely.\n\nThus output."}}}, {"text": "{\n  \"documents\": [\n    {\n      \"page_no\": 1,\n      \"doc_type\": \"outgate\"\n    }\n  ]\n}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2783, "outputTokens": 327, "totalTokens": 3110}, "metrics": {"latencyMs": 1573}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/f484a675_E88AUSUP8065SEI9GD5Q.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nHUSKY Terminal And Stevedoring, Inc.\nYANG MING LINE\n#776055\nLane : 45\nFULL-OUT\n** IMPORT-OUT EIR **\n2025-07-15 09:10:18\nContainer: TLLU6009676\nSize/Type: 42G1\nSpot: I134\nGenset :\nChassis : FB\nSize/Type:\nSpot:\nVessel : YM TUTORIAL\nVoyage : 018W\nRef. No: W226681952\nVent:\nReq. Temp :\nDriver License: 116390010495\nTrucking Co: (CQBH) CARDINAL LOGISTICS 1 LLC\nTruck License : 86266RP\nTruck Tag : 86266RP\nSEALINTACT\nCONTAINER GROSS WEIGHT(LBS): 48281 Exit with OWN chassis\nReturning MTYs should beclean and free of debris\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}