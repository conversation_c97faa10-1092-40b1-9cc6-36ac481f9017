#!/usr/bin/env python3
"""
Test script to verify the token extraction functionality.
"""

import os
import csv
import json
from extract_token_data import extract_token_data, create_csv_report


def test_token_extraction():
    """Test the token extraction from a specific file."""
    print("Testing token extraction functionality...")
    
    # Test with the specific file mentioned by the user
    test_file = "/home/<USER>/Desktop/test_ligistically/output/run1_2_pod_2.json"
    
    if os.path.exists(test_file):
        print(f"Testing with file: {test_file}")
        
        # Extract token data
        token_data = extract_token_data(test_file)
        
        if token_data:
            print("✓ Token extraction successful!")
            print(f"  Input Tokens: {token_data['inputTokens']}")
            print(f"  Output Tokens: {token_data['outputTokens']}")
            print(f"  Total Tokens: {token_data['totalTokens']}")
            
            # Verify the expected values
            expected_values = {
                'inputTokens': 2397,
                'outputTokens': 177,
                'totalTokens': 2574
            }
            
            if token_data == expected_values:
                print("✓ Token values match expected results!")
                return True
            else:
                print("✗ Token values don't match expected results!")
                print(f"Expected: {expected_values}")
                print(f"Got: {token_data}")
                return False
        else:
            print("✗ Failed to extract token data!")
            return False
    else:
        print(f"✗ Test file not found: {test_file}")
        return False


def test_csv_creation():
    """Test CSV file creation and verify its contents."""
    print("\nTesting CSV creation...")
    
    csv_file = "/home/<USER>/Desktop/test_ligistically/Augment_test/token_analysis_report.csv"
    
    if os.path.exists(csv_file):
        print(f"✓ CSV file exists: {csv_file}")
        
        # Read and verify CSV contents
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            rows = list(reader)
            
        print(f"✓ CSV contains {len(rows)} data rows")
        
        # Check if the specific file we tested is in the CSV
        target_filename = "run1_2_pod_2.json"
        found_row = None
        
        for row in rows:
            if target_filename in row['File Name (Hyperlink)']:
                found_row = row
                break
        
        if found_row:
            print(f"✓ Found target file in CSV: {target_filename}")
            print(f"  Input Tokens: {found_row['Input Tokens']}")
            print(f"  Output Tokens: {found_row['Output Tokens']}")
            print(f"  Total Tokens: {found_row['Total Tokens']}")
            
            # Verify values
            if (found_row['Input Tokens'] == '2397' and 
                found_row['Output Tokens'] == '177' and 
                found_row['Total Tokens'] == '2574'):
                print("✓ CSV values are correct!")
                return True
            else:
                print("✗ CSV values are incorrect!")
                return False
        else:
            print(f"✗ Target file not found in CSV: {target_filename}")
            return False
    else:
        print(f"✗ CSV file not found: {csv_file}")
        return False


def main():
    """Run all tests."""
    print("=" * 50)
    print("TOKEN EXTRACTION TEST SUITE")
    print("=" * 50)
    
    test1_passed = test_token_extraction()
    test2_passed = test_csv_creation()
    
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    print(f"Token Extraction Test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"CSV Creation Test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! The solution is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    main()
