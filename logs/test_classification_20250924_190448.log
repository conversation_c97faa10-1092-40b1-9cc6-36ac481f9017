2025-09-24 19:04:48,311 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190448.log
2025-09-24 19:04:48,312 - INFO - 📁 Found 14 files to process
2025-09-24 19:04:48,312 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:04:48,312 - INFO - 🚀 Processing 14 files in FORCED PARALLEL MODE...
2025-09-24 19:04:48,312 - INFO - 🚀 Creating 14 parallel tasks...
2025-09-24 19:04:48,312 - INFO - 🚀 All 14 tasks created - executing in parallel...
2025-09-24 19:04:48,313 - INFO - ⬆️ [19:04:48] Uploading: 2_pod_1.pdf
2025-09-24 19:04:51,389 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/3b0da096_2_pod_1.pdf
2025-09-24 19:04:51,390 - INFO - 🔍 [19:04:51] Starting classification: 2_pod_1.pdf
2025-09-24 19:04:51,391 - INFO - ⬆️ [19:04:51] Uploading: 2_pod_10.pdf
2025-09-24 19:04:51,393 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:51,413 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:51,421 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3b0da096_2_pod_1.pdf
2025-09-24 19:04:51,421 - INFO - Processing PDF from S3...
2025-09-24 19:04:51,421 - INFO - Downloading PDF from S3 to /tmp/tmpur_dtqba/3b0da096_2_pod_1.pdf
2025-09-24 19:04:52,003 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_10.pdf -> s3://document-extraction-logistically/temp/380a2409_2_pod_10.pdf
2025-09-24 19:04:52,003 - INFO - 🔍 [19:04:52] Starting classification: 2_pod_10.pdf
2025-09-24 19:04:52,004 - INFO - ⬆️ [19:04:52] Uploading: 2_pod_11.pdf
2025-09-24 19:04:52,005 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:52,016 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:52,023 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/380a2409_2_pod_10.pdf
2025-09-24 19:04:52,023 - INFO - Processing PDF from S3...
2025-09-24 19:04:52,024 - INFO - Downloading PDF from S3 to /tmp/tmpgwu1f_wa/380a2409_2_pod_10.pdf
2025-09-24 19:04:52,650 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_11.pdf -> s3://document-extraction-logistically/temp/182d62f1_2_pod_11.pdf
2025-09-24 19:04:52,651 - INFO - 🔍 [19:04:52] Starting classification: 2_pod_11.pdf
2025-09-24 19:04:52,652 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:52,652 - INFO - ⬆️ [19:04:52] Uploading: 2_pod_12.pdf
2025-09-24 19:04:52,665 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:52,677 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/182d62f1_2_pod_11.pdf
2025-09-24 19:04:52,678 - INFO - Processing PDF from S3...
2025-09-24 19:04:52,679 - INFO - Downloading PDF from S3 to /tmp/tmpc71x1a_i/182d62f1_2_pod_11.pdf
2025-09-24 19:04:53,293 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_12.pdf -> s3://document-extraction-logistically/temp/4c1206c8_2_pod_12.pdf
2025-09-24 19:04:53,293 - INFO - 🔍 [19:04:53] Starting classification: 2_pod_12.pdf
2025-09-24 19:04:53,294 - INFO - ⬆️ [19:04:53] Uploading: 2_pod_13.pdf
2025-09-24 19:04:53,295 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:53,316 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:53,318 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4c1206c8_2_pod_12.pdf
2025-09-24 19:04:53,319 - INFO - Processing PDF from S3...
2025-09-24 19:04:53,319 - INFO - Downloading PDF from S3 to /tmp/tmpostfnaj8/4c1206c8_2_pod_12.pdf
2025-09-24 19:04:53,323 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:04:53,324 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:53,325 - INFO - Splitting PDF 380a2409_2_pod_10 into 1 pages
2025-09-24 19:04:53,331 - INFO - Split PDF into 1 pages
2025-09-24 19:04:53,331 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:53,332 - INFO - Expected pages: [1]
2025-09-24 19:04:53,922 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_13.pdf -> s3://document-extraction-logistically/temp/3a44acf0_2_pod_13.pdf
2025-09-24 19:04:53,922 - INFO - 🔍 [19:04:53] Starting classification: 2_pod_13.pdf
2025-09-24 19:04:53,923 - INFO - ⬆️ [19:04:53] Uploading: 2_pod_14.pdf
2025-09-24 19:04:53,924 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:53,945 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:53,950 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3a44acf0_2_pod_13.pdf
2025-09-24 19:04:53,950 - INFO - Processing PDF from S3...
2025-09-24 19:04:53,951 - INFO - Downloading PDF from S3 to /tmp/tmpalmftx2t/3a44acf0_2_pod_13.pdf
2025-09-24 19:04:54,567 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_14.pdf -> s3://document-extraction-logistically/temp/ca22ebf8_2_pod_14.pdf
2025-09-24 19:04:54,567 - INFO - 🔍 [19:04:54] Starting classification: 2_pod_14.pdf
2025-09-24 19:04:54,568 - INFO - ⬆️ [19:04:54] Uploading: 2_pod_2.pdf
2025-09-24 19:04:54,569 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:54,585 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:54,589 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ca22ebf8_2_pod_14.pdf
2025-09-24 19:04:54,589 - INFO - Processing PDF from S3...
2025-09-24 19:04:54,590 - INFO - Downloading PDF from S3 to /tmp/tmpt5x065p7/ca22ebf8_2_pod_14.pdf
2025-09-24 19:04:55,074 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:04:55,075 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:55,076 - INFO - Splitting PDF 182d62f1_2_pod_11 into 1 pages
2025-09-24 19:04:55,078 - INFO - Split PDF into 1 pages
2025-09-24 19:04:55,079 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:55,079 - INFO - Expected pages: [1]
2025-09-24 19:04:55,089 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:04:55,089 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:55,090 - INFO - Splitting PDF 4c1206c8_2_pod_12 into 1 pages
2025-09-24 19:04:55,095 - INFO - Split PDF into 1 pages
2025-09-24 19:04:55,095 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:55,095 - INFO - Expected pages: [1]
2025-09-24 19:04:55,277 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_2.pdf -> s3://document-extraction-logistically/temp/7f62e6a0_2_pod_2.pdf
2025-09-24 19:04:55,277 - INFO - 🔍 [19:04:55] Starting classification: 2_pod_2.pdf
2025-09-24 19:04:55,278 - INFO - ⬆️ [19:04:55] Uploading: 2_pod_3.pdf
2025-09-24 19:04:55,279 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:55,294 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:55,331 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7f62e6a0_2_pod_2.pdf
2025-09-24 19:04:55,331 - INFO - Processing PDF from S3...
2025-09-24 19:04:55,331 - INFO - Downloading PDF from S3 to /tmp/tmp5nhodfwk/7f62e6a0_2_pod_2.pdf
2025-09-24 19:04:55,769 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:04:55,769 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:55,769 - INFO - Splitting PDF ca22ebf8_2_pod_14 into 1 pages
2025-09-24 19:04:55,770 - INFO - Split PDF into 1 pages
2025-09-24 19:04:55,770 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:55,770 - INFO - Expected pages: [1]
2025-09-24 19:04:55,950 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_3.pdf -> s3://document-extraction-logistically/temp/f9354b1f_2_pod_3.pdf
2025-09-24 19:04:55,950 - INFO - 🔍 [19:04:55] Starting classification: 2_pod_3.pdf
2025-09-24 19:04:55,952 - INFO - ⬆️ [19:04:55] Uploading: 2_pod_4.pdf
2025-09-24 19:04:55,952 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:55,967 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:55,972 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f9354b1f_2_pod_3.pdf
2025-09-24 19:04:55,972 - INFO - Processing PDF from S3...
2025-09-24 19:04:55,972 - INFO - Downloading PDF from S3 to /tmp/tmp0_y_qsx7/f9354b1f_2_pod_3.pdf
2025-09-24 19:04:56,097 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:04:56,097 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:56,097 - INFO - Splitting PDF 3a44acf0_2_pod_13 into 1 pages
2025-09-24 19:04:56,098 - INFO - Split PDF into 1 pages
2025-09-24 19:04:56,098 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:56,098 - INFO - Expected pages: [1]
2025-09-24 19:04:56,594 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_4.pdf -> s3://document-extraction-logistically/temp/f3f502a0_2_pod_4.pdf
2025-09-24 19:04:56,595 - INFO - 🔍 [19:04:56] Starting classification: 2_pod_4.pdf
2025-09-24 19:04:56,595 - INFO - ⬆️ [19:04:56] Uploading: 2_pod_5.pdf
2025-09-24 19:04:56,596 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:56,611 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:56,615 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f3f502a0_2_pod_4.pdf
2025-09-24 19:04:56,616 - INFO - Processing PDF from S3...
2025-09-24 19:04:56,616 - INFO - Downloading PDF from S3 to /tmp/tmpcym_kodd/f3f502a0_2_pod_4.pdf
2025-09-24 19:04:57,214 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_5.pdf -> s3://document-extraction-logistically/temp/956e2d3a_2_pod_5.pdf
2025-09-24 19:04:57,215 - INFO - 🔍 [19:04:57] Starting classification: 2_pod_5.pdf
2025-09-24 19:04:57,215 - INFO - ⬆️ [19:04:57] Uploading: 2_pod_6.pdf
2025-09-24 19:04:57,216 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:57,226 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:57,229 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/956e2d3a_2_pod_5.pdf
2025-09-24 19:04:57,230 - INFO - Processing PDF from S3...
2025-09-24 19:04:57,230 - INFO - Downloading PDF from S3 to /tmp/tmp7gsvmmyc/956e2d3a_2_pod_5.pdf
2025-09-24 19:04:57,806 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_6.pdf -> s3://document-extraction-logistically/temp/0a169c06_2_pod_6.pdf
2025-09-24 19:04:57,807 - INFO - 🔍 [19:04:57] Starting classification: 2_pod_6.pdf
2025-09-24 19:04:57,807 - INFO - ⬆️ [19:04:57] Uploading: 2_pod_7.pdf
2025-09-24 19:04:57,808 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:57,831 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:57,837 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0a169c06_2_pod_6.pdf
2025-09-24 19:04:57,837 - INFO - Processing PDF from S3...
2025-09-24 19:04:57,838 - INFO - Downloading PDF from S3 to /tmp/tmptzswa_19/0a169c06_2_pod_6.pdf
2025-09-24 19:04:57,942 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 19:04:57,942 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:57,945 - INFO - Splitting PDF 7f62e6a0_2_pod_2 into 1 pages
2025-09-24 19:04:57,947 - INFO - Split PDF into 1 pages
2025-09-24 19:04:57,947 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:57,947 - INFO - Expected pages: [1]
2025-09-24 19:04:58,126 - INFO - Page 1: Extracted 961 characters, 60 lines from 380a2409_2_pod_10_b7727e8c_page_001.pdf
2025-09-24 19:04:58,127 - INFO - Successfully processed page 1
2025-09-24 19:04:58,129 - INFO - Combined 1 pages into final text
2025-09-24 19:04:58,129 - INFO - Text validation for 380a2409_2_pod_10: 978 characters, 1 pages
2025-09-24 19:04:58,129 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:04:58,129 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:04:58,283 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:04:58,283 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:58,284 - INFO - Splitting PDF f9354b1f_2_pod_3 into 1 pages
2025-09-24 19:04:58,285 - INFO - Split PDF into 1 pages
2025-09-24 19:04:58,285 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:58,285 - INFO - Expected pages: [1]
2025-09-24 19:04:58,415 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_7.pdf -> s3://document-extraction-logistically/temp/28a87bd4_2_pod_7.pdf
2025-09-24 19:04:58,416 - INFO - 🔍 [19:04:58] Starting classification: 2_pod_7.pdf
2025-09-24 19:04:58,416 - INFO - ⬆️ [19:04:58] Uploading: 2_pod_8.pdf
2025-09-24 19:04:58,418 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:58,433 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:58,437 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/28a87bd4_2_pod_7.pdf
2025-09-24 19:04:58,437 - INFO - Processing PDF from S3...
2025-09-24 19:04:58,437 - INFO - Downloading PDF from S3 to /tmp/tmp3sid8q3u/28a87bd4_2_pod_7.pdf
2025-09-24 19:04:58,555 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:04:58,555 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:58,557 - INFO - Splitting PDF f3f502a0_2_pod_4 into 1 pages
2025-09-24 19:04:58,559 - INFO - Split PDF into 1 pages
2025-09-24 19:04:58,559 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:58,560 - INFO - Expected pages: [1]
2025-09-24 19:04:58,947 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:04:58,947 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:58,947 - INFO - Splitting PDF 956e2d3a_2_pod_5 into 1 pages
2025-09-24 19:04:58,948 - INFO - Split PDF into 1 pages
2025-09-24 19:04:58,948 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:58,948 - INFO - Expected pages: [1]
2025-09-24 19:04:59,014 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_8.pdf -> s3://document-extraction-logistically/temp/1b1bc469_2_pod_8.pdf
2025-09-24 19:04:59,015 - INFO - 🔍 [19:04:59] Starting classification: 2_pod_8.pdf
2025-09-24 19:04:59,016 - INFO - ⬆️ [19:04:59] Uploading: 2_pod_9.pdf
2025-09-24 19:04:59,022 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:59,039 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:59,043 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1b1bc469_2_pod_8.pdf
2025-09-24 19:04:59,044 - INFO - Processing PDF from S3...
2025-09-24 19:04:59,044 - INFO - Downloading PDF from S3 to /tmp/tmpgry44o6z/1b1bc469_2_pod_8.pdf
2025-09-24 19:04:59,255 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:04:59,255 - INFO - Splitting PDF into individual pages...
2025-09-24 19:04:59,256 - INFO - Splitting PDF 0a169c06_2_pod_6 into 1 pages
2025-09-24 19:04:59,257 - INFO - Split PDF into 1 pages
2025-09-24 19:04:59,258 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:04:59,258 - INFO - Expected pages: [1]
2025-09-24 19:04:59,646 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_9.pdf -> s3://document-extraction-logistically/temp/edccc47b_2_pod_9.pdf
2025-09-24 19:04:59,647 - INFO - 🔍 [19:04:59] Starting classification: 2_pod_9.pdf
2025-09-24 19:04:59,648 - INFO - Initializing TextractProcessor...
2025-09-24 19:04:59,660 - INFO - Initializing BedrockProcessor...
2025-09-24 19:04:59,663 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/edccc47b_2_pod_9.pdf
2025-09-24 19:04:59,664 - INFO - Processing PDF from S3...
2025-09-24 19:04:59,664 - INFO - Downloading PDF from S3 to /tmp/tmpp4gruimd/edccc47b_2_pod_9.pdf
2025-09-24 19:05:00,468 - INFO - Page 1: Extracted 797 characters, 46 lines from 182d62f1_2_pod_11_477cf96d_page_001.pdf
2025-09-24 19:05:00,470 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fd117179-57c6-4492-88c5-7b9f57a5c388', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:00 GMT', 'content-type': 'application/json', 'content-length': '1089', 'connection': 'keep-alive', 'x-amzn-requestid': 'fd117179-57c6-4492-88c5-7b9f57a5c388'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to identify doc type. The content includes "DELIVERY RECEIPT MUST BE RETURNED" and "CONSIGNEE", "FREIGHT BILL NO.", "SHIPPER", "BILL TO". There\'s also "PO#: 3773". There\'s "RECEIVED THE ABOVE DESCRIBED PROPERTY IN" etc. This looks like a Delivery Receipt. Which doc type matches? The list doesn\'t include "delivery receipt" explicitly. But may correspond to POD (Proof of Delivery). POD includes "Proof of Delivery" header. This one says "DELIVERY RECEIPT MUST BE RETURNED". That\'s basically a POD. So classify as pod. There\'s no "Proof of Delivery" phrase but it\'s a delivery receipt. Could also be "pod". So page1 doc_type: pod.\n\nCheck for continuation: Not present. So output accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2962, 'outputTokens': 202, 'totalTokens': 3164}, 'metrics': {'latencyMs': 1151}}
2025-09-24 19:05:00,471 - INFO - Successfully processed page 1
2025-09-24 19:05:00,471 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/380a2409_2_pod_10.pdf
2025-09-24 19:05:00,472 - INFO - Combined 1 pages into final text
2025-09-24 19:05:00,472 - INFO - Text validation for 182d62f1_2_pod_11: 814 characters, 1 pages
2025-09-24 19:05:00,479 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:00,481 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:00,505 - INFO - 

2_pod_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:00,505 - INFO - 

✓ Saved result: output/run1_2_pod_10.json
2025-09-24 19:05:00,526 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 19:05:00,526 - INFO - Splitting PDF into individual pages...
2025-09-24 19:05:00,527 - INFO - Splitting PDF 28a87bd4_2_pod_7 into 1 pages
2025-09-24 19:05:00,533 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 19:05:00,533 - INFO - Split PDF into 1 pages
2025-09-24 19:05:00,533 - INFO - Splitting PDF into individual pages...
2025-09-24 19:05:00,533 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:05:00,534 - INFO - Expected pages: [1]
2025-09-24 19:05:00,534 - INFO - Splitting PDF 1b1bc469_2_pod_8 into 1 pages
2025-09-24 19:05:00,544 - INFO - Split PDF into 1 pages
2025-09-24 19:05:00,544 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:05:00,544 - INFO - Expected pages: [1]
2025-09-24 19:05:00,805 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/380a2409_2_pod_10.pdf
2025-09-24 19:05:02,678 - INFO - Page 1: Extracted 983 characters, 63 lines from ca22ebf8_2_pod_14_598a7cda_page_001.pdf
2025-09-24 19:05:02,679 - INFO - Successfully processed page 1
2025-09-24 19:05:02,679 - INFO - Combined 1 pages into final text
2025-09-24 19:05:02,679 - INFO - Text validation for ca22ebf8_2_pod_14: 1000 characters, 1 pages
2025-09-24 19:05:02,679 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:02,680 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:02,817 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e1010fe5-3713-4875-9d04-55d38d2582b6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:02 GMT', 'content-type': 'application/json', 'content-length': '746', 'connection': 'keep-alive', 'x-amzn-requestid': 'e1010fe5-3713-4875-9d04-55d38d2582b6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify the doc type. The text shows "POD" at top. It includes details: BOL Nbr, PO Nbr, Consignee, Shipper, description, weight, delivery driver name, arrival times, delivery signature. That matches Proof of Delivery. So doc_type: pod.\n\nWe must output JSON with documents array, page_no: 1, doc_type: "pod".\n\nCheck if it\'s continuation: no.\n\nReturn via tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2895, 'outputTokens': 122, 'totalTokens': 3017}, 'metrics': {'latencyMs': 806}}
2025-09-24 19:05:02,817 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/182d62f1_2_pod_11.pdf
2025-09-24 19:05:02,842 - INFO - 

2_pod_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:02,842 - INFO - 

✓ Saved result: output/run1_2_pod_11.json
2025-09-24 19:05:03,145 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/182d62f1_2_pod_11.pdf
2025-09-24 19:05:04,974 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2bd03195-3aad-4206-a716-3dba43420235', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:04 GMT', 'content-type': 'application/json', 'content-length': '1065', 'connection': 'keep-alive', 'x-amzn-requestid': '2bd03195-3aad-4206-a716-3dba43420235'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. Keywords: "DELIVERY RECEIPT", "CONSIGNEE", "FREIGHT BILL NO.", "METHOD OF PAYMENT", "SHIPPER", "BILL TO", "PO#", "DESCRIPTION OF ARTICLES", "WEIGHT", "RECEIVED THE ABOVE DESCRIBED PROPERTY". This looks like a Delivery Receipt, which is a Proof of Delivery (POD) or a Delivery Receipt? In logistics, "Delivery Receipt" is typically a POD. But we need to choose doc_type: pod. Let\'s confirm: It has "RECEIVED THE ABOVE DESCRIBED PROPERTY IN GOOD CONDITION EXCEPT AS NOTED. SIGNATURE REQUIRED- INITIALS NOT ACCEPTED". That is signature block at end, typical POD. Also includes "PO#" and "FREIGHT BILL NO." So it\'s a POD. So doc_type = pod.\n\nWe have only page 1, so output accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2975, 'outputTokens': 211, 'totalTokens': 3186}, 'metrics': {'latencyMs': 1133}}
2025-09-24 19:05:04,974 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ca22ebf8_2_pod_14.pdf
2025-09-24 19:05:04,997 - INFO - 

2_pod_14.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:04,997 - INFO - 

✓ Saved result: output/run1_2_pod_14.json
2025-09-24 19:05:05,081 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 19:05:05,082 - INFO - Splitting PDF into individual pages...
2025-09-24 19:05:05,083 - INFO - Splitting PDF 3b0da096_2_pod_1 into 3 pages
2025-09-24 19:05:05,088 - INFO - Split PDF into 3 pages
2025-09-24 19:05:05,088 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:05:05,089 - INFO - Expected pages: [1, 2, 3]
2025-09-24 19:05:05,296 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ca22ebf8_2_pod_14.pdf
2025-09-24 19:05:05,309 - INFO - Page 1: Extracted 762 characters, 53 lines from 7f62e6a0_2_pod_2_58c74cdd_page_001.pdf
2025-09-24 19:05:05,310 - INFO - Successfully processed page 1
2025-09-24 19:05:05,310 - INFO - Combined 1 pages into final text
2025-09-24 19:05:05,310 - INFO - Text validation for 7f62e6a0_2_pod_2: 779 characters, 1 pages
2025-09-24 19:05:05,311 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:05,311 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:06,557 - INFO - Page 1: Extracted 1774 characters, 120 lines from 1b1bc469_2_pod_8_8759dfdf_page_001.pdf
2025-09-24 19:05:06,557 - INFO - Successfully processed page 1
2025-09-24 19:05:06,557 - INFO - Combined 1 pages into final text
2025-09-24 19:05:06,557 - INFO - Text validation for 1b1bc469_2_pod_8: 1791 characters, 1 pages
2025-09-24 19:05:06,558 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:06,558 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:06,906 - INFO - Page 1: Extracted 1430 characters, 77 lines from 28a87bd4_2_pod_7_cb549edc_page_001.pdf
2025-09-24 19:05:06,906 - INFO - Successfully processed page 1
2025-09-24 19:05:06,907 - INFO - Combined 1 pages into final text
2025-09-24 19:05:06,907 - INFO - Text validation for 28a87bd4_2_pod_7: 1447 characters, 1 pages
2025-09-24 19:05:06,907 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:06,907 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:07,212 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c237ebc0-57bb-4f29-962e-40c74ee18dad', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:07 GMT', 'content-type': 'application/json', 'content-length': '747', 'connection': 'keep-alive', 'x-amzn-requestid': 'c237ebc0-57bb-4f29-962e-40c74ee18dad'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only page1 provided. It\'s a Delivery Ticket. It has "DELIVERY TICKET" header. Possibly it\'s a POD or Delivery Ticket. In definitions, POD: Proof of Delivery. Delivery Ticket could be a POD. The fields: Received By, Delivered By. So likely doc_type = pod. There\'s no explicit "Proof of Delivery", but delivery ticket is essentially a POD. So output page 1 as pod.\n\nReturn JSON with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2872, 'outputTokens': 113, 'totalTokens': 2985}, 'metrics': {'latencyMs': 706}}
2025-09-24 19:05:07,213 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7f62e6a0_2_pod_2.pdf
2025-09-24 19:05:07,232 - INFO - 

2_pod_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:07,233 - INFO - 

✓ Saved result: output/run1_2_pod_2.json
2025-09-24 19:05:07,517 - INFO - Page 1: Extracted 2131 characters, 138 lines from 956e2d3a_2_pod_5_145c7ea2_page_001.pdf
2025-09-24 19:05:07,517 - INFO - Successfully processed page 1
2025-09-24 19:05:07,517 - INFO - Combined 1 pages into final text
2025-09-24 19:05:07,517 - INFO - Text validation for 956e2d3a_2_pod_5: 2148 characters, 1 pages
2025-09-24 19:05:07,518 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:07,518 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:07,526 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7f62e6a0_2_pod_2.pdf
2025-09-24 19:05:07,785 - INFO - Page 1: Extracted 797 characters, 55 lines from f9354b1f_2_pod_3_04fb9f4f_page_001.pdf
2025-09-24 19:05:07,785 - INFO - Successfully processed page 1
2025-09-24 19:05:07,785 - INFO - Combined 1 pages into final text
2025-09-24 19:05:07,785 - INFO - Text validation for f9354b1f_2_pod_3: 814 characters, 1 pages
2025-09-24 19:05:07,786 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:07,786 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:07,879 - INFO - Page 1: Extracted 1484 characters, 105 lines from 4c1206c8_2_pod_12_94970ba0_page_001.pdf
2025-09-24 19:05:07,879 - INFO - Successfully processed page 1
2025-09-24 19:05:07,879 - INFO - Combined 1 pages into final text
2025-09-24 19:05:07,879 - INFO - Text validation for 4c1206c8_2_pod_12: 1501 characters, 1 pages
2025-09-24 19:05:07,879 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:07,880 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:08,052 - INFO - Page 1: Extracted 1309 characters, 74 lines from 0a169c06_2_pod_6_bfd3b64e_page_001.pdf
2025-09-24 19:05:08,052 - INFO - Successfully processed page 1
2025-09-24 19:05:08,053 - INFO - Combined 1 pages into final text
2025-09-24 19:05:08,053 - INFO - Text validation for 0a169c06_2_pod_6: 1326 characters, 1 pages
2025-09-24 19:05:08,053 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:08,054 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:08,472 - INFO - Page 1: Extracted 1837 characters, 158 lines from 3a44acf0_2_pod_13_7b7279ea_page_001.pdf
2025-09-24 19:05:08,472 - INFO - Successfully processed page 1
2025-09-24 19:05:08,472 - INFO - Combined 1 pages into final text
2025-09-24 19:05:08,472 - INFO - Text validation for 3a44acf0_2_pod_13: 1854 characters, 1 pages
2025-09-24 19:05:08,472 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:08,473 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:08,606 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f405d84c-9c04-4aa7-a7f9-68bdfce4404f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:08 GMT', 'content-type': 'application/json', 'content-length': '889', 'connection': 'keep-alive', 'x-amzn-requestid': 'f405d84c-9c04-4aa7-a7f9-68bdfce4404f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. Keywords: "DELIVERY RECEIPT", "PYLE FREIGHT", "P O NUMBER", "SHIPPER", "CONSIGNEE". Might be a Delivery Receipt. That could be a POD? Proof of Delivery typically has "Proof of Delivery". This says "DELIVERY RECEIPT". Could be POD. But "POD" doc_type is for proof of delivery. The page has a signature, date, arrival time, departure time. It\'s a delivery receipt. Likely POD. It also has "SIGNATURE: Keith E". That matches POD. So doc_type: pod.\n\nCheck if there is continuation? It\'s page 1 of 1. So just one page.\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3308, 'outputTokens': 168, 'totalTokens': 3476}, 'metrics': {'latencyMs': 866}}
2025-09-24 19:05:08,606 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1b1bc469_2_pod_8.pdf
2025-09-24 19:05:08,643 - INFO - 

2_pod_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:08,643 - INFO - 

✓ Saved result: output/run1_2_pod_8.json
2025-09-24 19:05:08,943 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1b1bc469_2_pod_8.pdf
2025-09-24 19:05:09,145 - INFO - Page 1: Extracted 1604 characters, 83 lines from f3f502a0_2_pod_4_053f7f4a_page_001.pdf
2025-09-24 19:05:09,145 - INFO - Successfully processed page 1
2025-09-24 19:05:09,145 - INFO - Combined 1 pages into final text
2025-09-24 19:05:09,145 - INFO - Text validation for f3f502a0_2_pod_4: 1621 characters, 1 pages
2025-09-24 19:05:09,145 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:09,145 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:09,949 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f88ec52c-33ef-4f52-a74d-3836e27f339a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:09 GMT', 'content-type': 'application/json', 'content-length': '835', 'connection': 'keep-alive', 'x-amzn-requestid': 'f88ec52c-33ef-4f52-a74d-3836e27f339a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. It\'s a Delivery Receipt. The header contains "Delivery Receipt". That is likely a POD (Proof of Delivery). The job: classify each page. The doc type for this page is "pod". Must return JSON with documents array containing page_no 1 and doc_type "pod".\n\nLet\'s double-check that it\'s not any other type. Delivery Receipt is typical POD. There\'s no invoice, no BOL. So yes pod.\n\nNow we need to output via the tool "classify_logistics_doc_type". So we must call the tool with the appropriate JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2973, 'outputTokens': 133, 'totalTokens': 3106}, 'metrics': {'latencyMs': 763}}
2025-09-24 19:05:09,949 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0a169c06_2_pod_6.pdf
2025-09-24 19:05:09,973 - INFO - 

2_pod_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:09,973 - INFO - 

✓ Saved result: output/run1_2_pod_6.json
2025-09-24 19:05:09,975 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c9b2138d-0990-435b-b35e-84f16bf0a4d0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:09 GMT', 'content-type': 'application/json', 'content-length': '1141', 'connection': 'keep-alive', 'x-amzn-requestid': 'c9b2138d-0990-435b-b35e-84f16bf0a4d0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify doc_type. The content: "DELIVERY TICKET PACKING". It includes "Pack List #: 22240", "see Packing List for details". It has shipping info, driver, dates, truck, weights, etc. It looks like a Delivery Ticket (proof of delivery?) But "Delivery Ticket" is not exactly "Proof of Delivery" but similar. The doc contains weights: "ALL WEIGHTS ARE REPORTED IN LB". It might be a POD or delivery ticket. The header says "DELIVERY TICKET". That matches POD? The doc type "pod" definition: proof of delivery. Keywords: "Proof of Delivery", "Delivery Ticket", "POD". So we can classify as pod. There\'s no signature lines but it\'s a Delivery Ticket. Also includes driver info, date/time, truck, etc. So doc_type = pod.\n\nReturn JSON with documents array: page_no 1, doc_type "pod".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2875, 'outputTokens': 213, 'totalTokens': 3088}, 'metrics': {'latencyMs': 1034}}
2025-09-24 19:05:09,975 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f9354b1f_2_pod_3.pdf
2025-09-24 19:05:10,126 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a07d9bd4-9efe-467d-a0ad-cdb0439deafa', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:09 GMT', 'content-type': 'application/json', 'content-length': '2359', 'connection': 'keep-alive', 'x-amzn-requestid': 'a07d9bd4-9efe-467d-a0ad-cdb0439deafa'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Determine doc_type. The content shows "DELIVERY ORDER" header. That is likely a "delivery order" which is not explicitly listed but likely corresponds to "bol" or "pod" or "delivery order"? The list includes "bol" for Bill of Lading, "pod" for Proof of Delivery, "delivery order" maybe considered "bol" or maybe "delivery order" is a type of "bol" or "delivery order" which is a carrier document. In the definitions, "bol" is Bill of Lading. Delivery order is a different type. The list does not include "delivery order" explicitly. But we might classify it as "bol" because it\'s a carrier document that describes goods and shipment. However "delivery order" is typically a document that authorizes delivery by the carrier; it\'s not the same as Bill of Lading. In absence of a dedicated category, maybe use "other". But we should check if any category matches: "delivery order" might be considered a "log" or "packing list"? Not. The header says "DELIVERY ORDER". There\'s no explicit "invoice", "bol", "pod". It includes shipment details, weight, description. It\'s a delivery order from the importer. Likely the best match is "bol" as the legal transport document. However "bol" definition includes "Bill of Lading" with shipper, consignee, etc. But delivery order is similar to a "bill of lading". In practice, a delivery order is a document that authorizes the carrier to deliver goods to the consignee. So I\'d classify as "bol" or maybe "other".\n\nGiven the instructions: "If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header." The header says "DELIVERY ORDER". But no doc_type enum for delivery order. So we must choose the most appropriate from the enum list. "bol" is the best fit. So page 1 doc_type: "bol".\n\nReturn JSON with documents array: page_no 1, doc_type "bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3136, 'outputTokens': 468, 'totalTokens': 3604}, 'metrics': {'latencyMs': 1989}}
2025-09-24 19:05:10,127 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/28a87bd4_2_pod_7.pdf
2025-09-24 19:05:10,207 - INFO - Page 3: Extracted 441 characters, 20 lines from 3b0da096_2_pod_1_6af60c24_page_003.pdf
2025-09-24 19:05:10,207 - INFO - Successfully processed page 3
2025-09-24 19:05:10,267 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:05:10,267 - INFO - Splitting PDF into individual pages...
2025-09-24 19:05:10,268 - INFO - Splitting PDF edccc47b_2_pod_9 into 1 pages
2025-09-24 19:05:10,270 - INFO - Split PDF into 1 pages
2025-09-24 19:05:10,270 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:05:10,270 - INFO - Expected pages: [1]
2025-09-24 19:05:10,313 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0a169c06_2_pod_6.pdf
2025-09-24 19:05:10,314 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1c9f3480-a26a-4f63-b6f0-992b9eba25d1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:10 GMT', 'content-type': 'application/json', 'content-length': '1559', 'connection': 'keep-alive', 'x-amzn-requestid': '1c9f3480-a26a-4f63-b6f0-992b9eba25d1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page? The user provided <page1> that contains page 2 of 2. It seems the document has two pages, but we see only page 1 content? Actually the text shows "Page 2 of 2" at top, indicating maybe this is page 2. But the content is a Delivery Receipt. It includes "DELIVERY RECEIPT" header, "Freight Bill", "Bill of Lading", etc. This is a Proof of Delivery? Actually it\'s a Delivery Receipt from FedEx. It might be a POD (Proof of Delivery). It includes "Received by:" and "Delivered by" etc. So likely doc_type is pod. The doc_type list includes "pod" for Proof of Delivery. There\'s also "delivery receipt" but that\'s POD. So page 1 is a POD.\n\nShould we consider any multi-page? The text says "Page 2 of 2". Maybe it\'s page 2. But only one page content we have. So we classify as pod. There\'s no continuation sign. So output:\n\n{documents: [{page_no: 1, doc_type: "pod"}]}\n\nBut we must output page numbers as actual. We only have 1 page. The user had <page1> but the content indicates it\'s page 2 of 2. But we don\'t know page numbers. We\'ll assume page 1. However to be safe we could output page_no: 1. The spec says actual page number. We\'ll output 1.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3338, 'outputTokens': 336, 'totalTokens': 3674}, 'metrics': {'latencyMs': 1619}}
2025-09-24 19:05:10,315 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/956e2d3a_2_pod_5.pdf
2025-09-24 19:05:10,346 - INFO - 

2_pod_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:10,347 - INFO - 

✓ Saved result: output/run1_2_pod_3.json
2025-09-24 19:05:10,390 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3c72c453-38cb-41a2-b26e-6a1fbfbb78e3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:10 GMT', 'content-type': 'application/json', 'content-length': '1073', 'connection': 'keep-alive', 'x-amzn-requestid': '3c72c453-38cb-41a2-b26e-6a1fbfbb78e3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. We need to classify. The content: appears to be a delivery receipt, with shipping details, PO, BL number, etc. It mentions "REC\'D the above In good condition etc." This looks like a "Delivery Receipt" or maybe a "Proof of Delivery". The page has "Received By" and "Time In" "Time Out" etc. It also includes details of skids delivered, damage, etc. This is typical of a Proof of Delivery (POD). The header "RECEIPT" and "Delivery" suggests a delivery receipt, but the content matches POD. The doc type is "pod" for Proof of Delivery.\n\nLet\'s double-check: The page shows "Received By" block, "Time In", "Time Out", "Received By" names etc. That matches POD. So doc_type: pod.\n\nReturn JSON with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3138, 'outputTokens': 199, 'totalTokens': 3337}, 'metrics': {'latencyMs': 1165}}
2025-09-24 19:05:10,390 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4c1206c8_2_pod_12.pdf
2025-09-24 19:05:10,647 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f9354b1f_2_pod_3.pdf
2025-09-24 19:05:10,662 - INFO - 

2_pod_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 19:05:10,662 - INFO - 

✓ Saved result: output/run1_2_pod_7.json
2025-09-24 19:05:10,782 - INFO - Page 1: Extracted 1597 characters, 78 lines from 3b0da096_2_pod_1_6af60c24_page_001.pdf
2025-09-24 19:05:10,783 - INFO - Successfully processed page 1
2025-09-24 19:05:10,998 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/28a87bd4_2_pod_7.pdf
2025-09-24 19:05:11,028 - INFO - 

2_pod_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:11,028 - INFO - 

✓ Saved result: output/run1_2_pod_5.json
2025-09-24 19:05:11,031 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '20932081-2873-4b73-bd85-4b2ef363a981', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:10 GMT', 'content-type': 'application/json', 'content-length': '1423', 'connection': 'keep-alive', 'x-amzn-requestid': '20932081-2873-4b73-bd85-4b2ef363a981'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page (page1). We need to classify the document type. The text shows "DELIVERY RECEIPT" at top. It has fields like Ship date, Pro No, Shipper, Consignee, COD Amount, etc. Also it mentions "Bottom portion to be kept by driver as proof of delivery." This seems like a Delivery Receipt, possibly a Proof of Delivery (POD). But is it a POD? POD usually has Received date, signature, condition, etc. This page includes "RECEIVED ON", "PERFORMED PLEASE INITIAL", "SIGNATURE", "BOTTOM portion to be kept by driver as proof of delivery." That is typical of a POD. However, the header says DELIVERY RECEIPT. It might be a Delivery Receipt, which is a type of POD? In the taxonomy, POD is defined as "Proof of Delivery". The header "DELIVERY RECEIPT" might be a title for a POD. In the list, "pod" is the type. There\'s no other type that matches. So we classify as pod.\n\nWe need to output using the tool classify_logistics_doc_type. Provide a docs array with page_no: 1, doc_type: "pod".\n\nAlso check if any continuation indicator: no. So we output that.\n\nLet\'s produce JSON.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3312, 'outputTokens': 284, 'totalTokens': 3596}, 'metrics': {'latencyMs': 1301}}
2025-09-24 19:05:11,031 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3a44acf0_2_pod_13.pdf
2025-09-24 19:05:11,315 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/956e2d3a_2_pod_5.pdf
2025-09-24 19:05:11,345 - INFO - 

2_pod_12.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:11,345 - INFO - 

✓ Saved result: output/run1_2_pod_12.json
2025-09-24 19:05:11,631 - INFO - Page 2: Extracted 1895 characters, 65 lines from 3b0da096_2_pod_1_6af60c24_page_002.pdf
2025-09-24 19:05:11,631 - INFO - Successfully processed page 2
2025-09-24 19:05:11,631 - INFO - Combined 3 pages into final text
2025-09-24 19:05:11,632 - INFO - Text validation for 3b0da096_2_pod_1: 3988 characters, 3 pages
2025-09-24 19:05:11,632 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:11,632 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:11,661 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4c1206c8_2_pod_12.pdf
2025-09-24 19:05:11,707 - INFO - 

2_pod_13.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:11,707 - INFO - 

✓ Saved result: output/run1_2_pod_13.json
2025-09-24 19:05:12,070 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3a44acf0_2_pod_13.pdf
2025-09-24 19:05:14,372 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b92fab05-bb12-4ad6-beaa-78db8f4e9781', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:14 GMT', 'content-type': 'application/json', 'content-length': '3871', 'connection': 'keep-alive', 'x-amzn-requestid': 'b92fab05-bb12-4ad6-beaa-78db8f4e9781'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify the doc_type. Let\'s examine content.\n\nThe text includes:\n\n- "CNW"\n- "FOR QUESTIONS, CALL ANYTIME"\n- "800-852-2282"\n- "11456458"\n- "BILL SHIPPER"\n- "COMPANY NAME (CONSIGNOR)"\n- "FULL DESCRIPTION OF CONTENTS"\n- "DATE 20-JUN-2025"\n- "ELECTRICAL COMPONENTS INTERNATIONAL"\n- "CUSTOMS REQUIRE A PRO-FORMA INVOICE FOR ALL PIECES"\n- "WEIGHT"\n- "NON-DOCUMENT SHIPMENTS"\n- "LB 12415"\n- "ROJAS DRIVE 3 600.0 KG"\n- "PICKUP FROM"\n- "DIMENSIONS 48x48x48"\n- "SPECIAL INSTRUCTIONS USA EXPORT"\n- "DECLARED ITEMS VALUED VALUE FOR OVER $2500"\n- "CUSTOMS EL PASO, TX 79928"\n- "REQUIRE FILING WITH CBP VIA AES"\n- "United States of America"\n- "SCHED B#"\n- "BILL CONSIGNEE"\n- "COMPANY NAME (CONSIGNEE)"\n- etc.\n- "REFERENCE: ITN#"\n- "ECI SOUTHFIELD"\n- "DECLARED VALUE FOR CUSTOMS PURPOSES:"\n- "SECURITY STATEMENT"\n- "SIGNATURE OF CONSIGNEE"\n- "PRINT NAME"\n- "DATE"\n- "P.O.D."\n- "LISA TURNER"\n- "4/24/25"\n- "TIME 8:21 Am 8:21 Am"\n- "Shipper\'s Signature"\n- "Date"\n- "stop"\n- "2 Skids"\n\nThis looks like a Customs Declaration or a pro forma invoice? It says "CUSTOMS REQUIRE A PRO-FORMA INVOICE FOR ALL PIECES". It includes shipping details, but also has P.O.D. and signature. The presence of "P.O.D." and signature suggests Proof of Delivery. But also includes Customs info. Maybe it\'s a POD? Let\'s see key words: "Proof of Delivery" is missing, but "P.O.D." abbreviation is present. POD usually has "Proof of Delivery" header. It has "P.O.D." and signature lines. Might be a POD for a shipment. However, it also contains customs info like declared value, shipping details. POD could include shipping details. But also "CUSTOMS REQUIRE A PRO-FORMA INVOICE FOR ALL PIECES" suggests it\'s a customs statement, maybe a commercial invoice? But no "Commercial Invoice" header. It\'s more like a "Customs Declaration" page. However, doc types defined: commercial invoice, lumper receipt, invoice, etc. There\'s no "customs declaration" type. The category "customs_doc" covers general customs paperwork. Possibly this page is a customs document. But also includes "P.O.D." indicating proof of delivery. Could be a combined document? But no distinct separate sections.\n\nWe need to pick one doc_type. "customs_doc" seems fitting: it is a customs document. It has customs details, declared value, etc. It doesn\'t contain HS code etc. It might be like a "Customs Declaration" or "Export Declaration".\n\nThus likely "customs_doc".\n\nCheck if any other type more appropriate: "comm_invoice" requires HS/HTS code, country of origin, terms of sale. Not present. So not comm_invoice.\n\n"invoice" also not present.\n\n"po" not present.\n\n"pack_list" no.\n\n"bol" not present.\n\n"pod" maybe: There is "P.O.D." signature. But doc also contains more context. Usually POD includes signature from recipient and date/time. It has that. But also includes shipping details. POD might also include the shipping details. Actually, POD often includes details like shipping info, but the main header is "Proof of Delivery". Here it\'s abbreviated "P.O.D." maybe.\n\nBut the presence of "CUSTOMS REQUIRE A PRO-FORMA INVOICE FOR ALL PIECES" might indicate it\'s a customs declaration and not a POD.\n\nThe doc type "customs_doc" seems best for a customs-related page that doesn\'t fit others.\n\nThus produce classification: page 1: customs_doc.\n\nNo continuation.\n\nThus output JSON.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3073, 'outputTokens': 910, 'totalTokens': 3983}, 'metrics': {'latencyMs': 4010}}
2025-09-24 19:05:14,373 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f3f502a0_2_pod_4.pdf
2025-09-24 19:05:14,409 - INFO - 

2_pod_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 19:05:14,410 - INFO - 

✓ Saved result: output/run1_2_pod_4.json
2025-09-24 19:05:14,709 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f3f502a0_2_pod_4.pdf
2025-09-24 19:05:15,089 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0957d4a7-8f53-4d49-9919-6f394331d9d9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:14 GMT', 'content-type': 'application/json', 'content-length': '2398', 'connection': 'keep-alive', 'x-amzn-requestid': '0957d4a7-8f53-4d49-9919-6f394331d9d9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. This is a delivery note / order invoice? The document shows Order, Delivery Note, item list, serial numbers, incoterm EXW, etc. It seems like an invoice or delivery note. The header says "Delivery Note" and "Order". Not explicitly "Invoice". Could be "invoice" type? But it\'s a delivery note, not an invoice. But the classification options: invoice, comm_invoice, bol, pod, etc. None for "delivery note". The closest is "invoice" maybe, because it\'s a bill? Actually it\'s more like order/delivery note. There\'s no payment amount. So maybe "other"? But we should treat as invoice? The definition: invoice: Bill issued by a carrier for goods being transported. This document is from CORETRAX to ROCKCLIFF ENERGY, with item list. It might be a purchase order? There\'s no PO number? There is "Cust Ref/PO" but not a numeric PO. There\'s "Cust SEU No." Not a PO. So not purchase order. Could be "invoice" for sale of goods. But still no amount due. But there may be amounts elsewhere? The document only lists items and serial numbers. No price. So maybe it\'s a delivery note, not invoice. There\'s no doc type among options that matches "delivery note". So default to "other". But check if any of the other categories apply: bol? No BOL. pod? No proof of delivery. rate_confirmation? No. clear_to_pay? No. scale_ticket? No weight. log? No. fuel_receipt? No. combined_carrier_documents? Could be multiple? No. pack_list? It lists items but not packaging details. It\'s more like a purchase order? But it\'s from CORETRAX to ROCKCLIFF, maybe it\'s a delivery note for equipment. Not a PO; it\'s a "delivery note" indicating items delivered. That is not in list. So classify as "other". But need to check continuation: page2 starts with "8" etc. Page3 continues. There is no "continued" header. But the document seems continuous. So each page same doc type. So all pages "other".\n\nThus output: documents array with page_no 1,2,3 all doc_type "other".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4488, 'outputTokens': 505, 'totalTokens': 4993}, 'metrics': {'latencyMs': 2318}}
2025-09-24 19:05:15,089 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3b0da096_2_pod_1.pdf
2025-09-24 19:05:15,143 - INFO - 

2_pod_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-24 19:05:15,143 - INFO - 

✓ Saved result: output/run1_2_pod_1.json
2025-09-24 19:05:15,428 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3b0da096_2_pod_1.pdf
2025-09-24 19:05:15,831 - INFO - Page 1: Extracted 824 characters, 53 lines from edccc47b_2_pod_9_32162d10_page_001.pdf
2025-09-24 19:05:15,832 - INFO - Successfully processed page 1
2025-09-24 19:05:15,832 - INFO - Combined 1 pages into final text
2025-09-24 19:05:15,832 - INFO - Text validation for edccc47b_2_pod_9: 841 characters, 1 pages
2025-09-24 19:05:15,832 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:05:15,833 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:05:17,906 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6a062e16-b27c-41e5-bf82-63e7b38cc69b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:35:17 GMT', 'content-type': 'application/json', 'content-length': '877', 'connection': 'keep-alive', 'x-amzn-requestid': '6a062e16-b27c-41e5-bf82-63e7b38cc69b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. We need to determine doc_type. The page header says "DELIVERY TICKET". Also includes details like Driver, Date In, Time In, Truck #, etc. Likely a delivery ticket similar to POD? The definition for POD: Proof of Delivery: "Delivery Ticket" could be a POD. POD keywords: "Proof of Delivery", "Delivery Ticket", "POD", "Received by" etc. This page has "Received By:" but no actual data. It has driver, times, etc. So likely POD. Also "Delivery Ticket" is a header. So doc_type should be pod. No continuation. So output page 1 as pod.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2899, 'outputTokens': 156, 'totalTokens': 3055}, 'metrics': {'latencyMs': 874}}
2025-09-24 19:05:17,907 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/edccc47b_2_pod_9.pdf
2025-09-24 19:05:17,923 - INFO - 

2_pod_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 19:05:17,923 - INFO - 

✓ Saved result: output/run1_2_pod_9.json
2025-09-24 19:05:18,243 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/edccc47b_2_pod_9.pdf
2025-09-24 19:05:18,243 - INFO - 
📊 Processing Summary:
2025-09-24 19:05:18,243 - INFO -    Total files: 14
2025-09-24 19:05:18,243 - INFO -    Successful: 14
2025-09-24 19:05:18,244 - INFO -    Failed: 0
2025-09-24 19:05:18,244 - INFO -    Duration: 29.93 seconds
2025-09-24 19:05:18,244 - INFO -    Output directory: output
2025-09-24 19:05:18,244 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_1.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"}]}
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_10.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_11.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_12.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_13.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_14.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,244 - INFO -    📄 2_pod_2.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_3.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_4.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_5.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_6.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_7.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_8.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,245 - INFO -    📄 2_pod_9.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 19:05:18,246 - INFO - 
============================================================================================================================================
2025-09-24 19:05:18,246 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:05:18,246 - INFO - ============================================================================================================================================
2025-09-24 19:05:18,246 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:05:18,246 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:05:18,246 - INFO - 2_pod_1.pdf                                        1      other                                    run1_2_pod_1.json                                                               
2025-09-24 19:05:18,246 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_1.pdf
2025-09-24 19:05:18,246 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_1.json
2025-09-24 19:05:18,246 - INFO - 
2025-09-24 19:05:18,246 - INFO - 2_pod_1.pdf                                        2      other                                    run1_2_pod_1.json                                                               
2025-09-24 19:05:18,246 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_1.pdf
2025-09-24 19:05:18,246 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_1.json
2025-09-24 19:05:18,246 - INFO - 
2025-09-24 19:05:18,246 - INFO - 2_pod_1.pdf                                        3      other                                    run1_2_pod_1.json                                                               
2025-09-24 19:05:18,246 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_1.pdf
2025-09-24 19:05:18,246 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_1.json
2025-09-24 19:05:18,247 - INFO - 
2025-09-24 19:05:18,247 - INFO - 2_pod_10.pdf                                       1      pod                                      run1_2_pod_10.json                                                              
2025-09-24 19:05:18,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_10.pdf
2025-09-24 19:05:18,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_10.json
2025-09-24 19:05:18,247 - INFO - 
2025-09-24 19:05:18,247 - INFO - 2_pod_11.pdf                                       1      pod                                      run1_2_pod_11.json                                                              
2025-09-24 19:05:18,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_11.pdf
2025-09-24 19:05:18,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_11.json
2025-09-24 19:05:18,247 - INFO - 
2025-09-24 19:05:18,247 - INFO - 2_pod_12.pdf                                       1      pod                                      run1_2_pod_12.json                                                              
2025-09-24 19:05:18,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_12.pdf
2025-09-24 19:05:18,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_12.json
2025-09-24 19:05:18,247 - INFO - 
2025-09-24 19:05:18,247 - INFO - 2_pod_13.pdf                                       1      pod                                      run1_2_pod_13.json                                                              
2025-09-24 19:05:18,247 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_13.pdf
2025-09-24 19:05:18,247 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_13.json
2025-09-24 19:05:18,247 - INFO - 
2025-09-24 19:05:18,248 - INFO - 2_pod_14.pdf                                       1      pod                                      run1_2_pod_14.json                                                              
2025-09-24 19:05:18,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_14.pdf
2025-09-24 19:05:18,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_14.json
2025-09-24 19:05:18,248 - INFO - 
2025-09-24 19:05:18,248 - INFO - 2_pod_2.pdf                                        1      pod                                      run1_2_pod_2.json                                                               
2025-09-24 19:05:18,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_2.pdf
2025-09-24 19:05:18,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_2.json
2025-09-24 19:05:18,248 - INFO - 
2025-09-24 19:05:18,248 - INFO - 2_pod_3.pdf                                        1      pod                                      run1_2_pod_3.json                                                               
2025-09-24 19:05:18,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_3.pdf
2025-09-24 19:05:18,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_3.json
2025-09-24 19:05:18,248 - INFO - 
2025-09-24 19:05:18,248 - INFO - 2_pod_4.pdf                                        1      customs_doc                              run1_2_pod_4.json                                                               
2025-09-24 19:05:18,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_4.pdf
2025-09-24 19:05:18,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_4.json
2025-09-24 19:05:18,248 - INFO - 
2025-09-24 19:05:18,248 - INFO - 2_pod_5.pdf                                        1      pod                                      run1_2_pod_5.json                                                               
2025-09-24 19:05:18,248 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_5.pdf
2025-09-24 19:05:18,248 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_5.json
2025-09-24 19:05:18,249 - INFO - 
2025-09-24 19:05:18,249 - INFO - 2_pod_6.pdf                                        1      pod                                      run1_2_pod_6.json                                                               
2025-09-24 19:05:18,249 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_6.pdf
2025-09-24 19:05:18,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_6.json
2025-09-24 19:05:18,249 - INFO - 
2025-09-24 19:05:18,249 - INFO - 2_pod_7.pdf                                        1      bol                                      run1_2_pod_7.json                                                               
2025-09-24 19:05:18,249 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_7.pdf
2025-09-24 19:05:18,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_7.json
2025-09-24 19:05:18,249 - INFO - 
2025-09-24 19:05:18,249 - INFO - 2_pod_8.pdf                                        1      pod                                      run1_2_pod_8.json                                                               
2025-09-24 19:05:18,249 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_8.pdf
2025-09-24 19:05:18,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_8.json
2025-09-24 19:05:18,249 - INFO - 
2025-09-24 19:05:18,249 - INFO - 2_pod_9.pdf                                        1      pod                                      run1_2_pod_9.json                                                               
2025-09-24 19:05:18,249 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_9.pdf
2025-09-24 19:05:18,249 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_2_pod_9.json
2025-09-24 19:05:18,249 - INFO - 
2025-09-24 19:05:18,250 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:05:18,250 - INFO - Total entries: 16
2025-09-24 19:05:18,250 - INFO - ============================================================================================================================================
2025-09-24 19:05:18,250 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:05:18,250 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:05:18,250 - INFO -   1. 2_pod_1.pdf                         Page 1   → other           | run1_2_pod_1.json
2025-09-24 19:05:18,250 - INFO -   2. 2_pod_1.pdf                         Page 2   → other           | run1_2_pod_1.json
2025-09-24 19:05:18,250 - INFO -   3. 2_pod_1.pdf                         Page 3   → other           | run1_2_pod_1.json
2025-09-24 19:05:18,250 - INFO -   4. 2_pod_10.pdf                        Page 1   → pod             | run1_2_pod_10.json
2025-09-24 19:05:18,250 - INFO -   5. 2_pod_11.pdf                        Page 1   → pod             | run1_2_pod_11.json
2025-09-24 19:05:18,250 - INFO -   6. 2_pod_12.pdf                        Page 1   → pod             | run1_2_pod_12.json
2025-09-24 19:05:18,250 - INFO -   7. 2_pod_13.pdf                        Page 1   → pod             | run1_2_pod_13.json
2025-09-24 19:05:18,250 - INFO -   8. 2_pod_14.pdf                        Page 1   → pod             | run1_2_pod_14.json
2025-09-24 19:05:18,250 - INFO -   9. 2_pod_2.pdf                         Page 1   → pod             | run1_2_pod_2.json
2025-09-24 19:05:18,250 - INFO -  10. 2_pod_3.pdf                         Page 1   → pod             | run1_2_pod_3.json
2025-09-24 19:05:18,251 - INFO -  11. 2_pod_4.pdf                         Page 1   → customs_doc     | run1_2_pod_4.json
2025-09-24 19:05:18,251 - INFO -  12. 2_pod_5.pdf                         Page 1   → pod             | run1_2_pod_5.json
2025-09-24 19:05:18,251 - INFO -  13. 2_pod_6.pdf                         Page 1   → pod             | run1_2_pod_6.json
2025-09-24 19:05:18,251 - INFO -  14. 2_pod_7.pdf                         Page 1   → bol             | run1_2_pod_7.json
2025-09-24 19:05:18,251 - INFO -  15. 2_pod_8.pdf                         Page 1   → pod             | run1_2_pod_8.json
2025-09-24 19:05:18,251 - INFO -  16. 2_pod_9.pdf                         Page 1   → pod             | run1_2_pod_9.json
2025-09-24 19:05:18,251 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:05:18,252 - INFO - 
✅ Test completed: {'total_files': 14, 'processed': 14, 'failed': 0, 'errors': [], 'duration_seconds': 29.930914, 'processed_files': [{'filename': '2_pod_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_1.pdf'}, {'filename': '2_pod_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_10.pdf'}, {'filename': '2_pod_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_11.pdf'}, {'filename': '2_pod_12.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_12.pdf'}, {'filename': '2_pod_13.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_13.pdf'}, {'filename': '2_pod_14.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_14.pdf'}, {'filename': '2_pod_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_2.pdf'}, {'filename': '2_pod_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_3.pdf'}, {'filename': '2_pod_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_4.pdf'}, {'filename': '2_pod_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_5.pdf'}, {'filename': '2_pod_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_6.pdf'}, {'filename': '2_pod_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_7.pdf'}, {'filename': '2_pod_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_8.pdf'}, {'filename': '2_pod_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/2_pod/2_pod_9.pdf'}]}
