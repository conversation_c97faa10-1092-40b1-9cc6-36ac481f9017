2025-09-18 23:36:34,548 - INFO - Logging initialized. Log file: logs/test_classification_20250918_233634.log
2025-09-18 23:36:34,549 - INFO - 📁 Found 57 files to process
2025-09-18 23:36:34,549 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 23:36:34,549 - INFO - 🚀 Processing 57 files in FORCED PARALLEL MODE...
2025-09-18 23:36:34,549 - INFO - 🚀 Creating 57 parallel tasks...
2025-09-18 23:36:34,549 - INFO - 🚀 All 57 tasks created - executing in parallel...
2025-09-18 23:36:34,549 - INFO - ⬆️ [23:36:34] Uploading: 10_invoice_1.pdf
2025-09-18 23:36:36,456 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/bc695a49_10_invoice_1.pdf
2025-09-18 23:36:36,457 - INFO - 🔍 [23:36:36] Starting classification: 10_invoice_1.pdf
2025-09-18 23:36:36,458 - INFO - ⬆️ [23:36:36] Uploading: 10_invoice_10.pdf
2025-09-18 23:36:36,459 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:36,492 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:36,507 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bc695a49_10_invoice_1.pdf
2025-09-18 23:36:36,507 - INFO - Processing PDF from S3...
2025-09-18 23:36:36,508 - INFO - Downloading PDF from S3 to /tmp/tmpijnxr3h_/bc695a49_10_invoice_1.pdf
2025-09-18 23:36:37,172 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_10.pdf -> s3://document-extraction-logistically/temp/83be436c_10_invoice_10.pdf
2025-09-18 23:36:37,172 - INFO - 🔍 [23:36:37] Starting classification: 10_invoice_10.pdf
2025-09-18 23:36:37,173 - INFO - ⬆️ [23:36:37] Uploading: 10_invoice_2.pdf
2025-09-18 23:36:37,176 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:37,199 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:37,202 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83be436c_10_invoice_10.pdf
2025-09-18 23:36:37,202 - INFO - Processing PDF from S3...
2025-09-18 23:36:37,202 - INFO - Downloading PDF from S3 to /tmp/tmp6p6s_wh8/83be436c_10_invoice_10.pdf
2025-09-18 23:36:38,298 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:38,301 - INFO - Splitting PDF bc695a49_10_invoice_1 into 1 pages
2025-09-18 23:36:38,309 - INFO - Split PDF into 1 pages
2025-09-18 23:36:38,309 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:38,309 - INFO - Expected pages: [1]
2025-09-18 23:36:38,707 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:38,708 - INFO - Splitting PDF 83be436c_10_invoice_10 into 1 pages
2025-09-18 23:36:38,715 - INFO - Split PDF into 1 pages
2025-09-18 23:36:38,715 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:38,715 - INFO - Expected pages: [1]
2025-09-18 23:36:39,807 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_2.pdf -> s3://document-extraction-logistically/temp/52657c04_10_invoice_2.pdf
2025-09-18 23:36:39,807 - INFO - 🔍 [23:36:39] Starting classification: 10_invoice_2.pdf
2025-09-18 23:36:39,808 - INFO - ⬆️ [23:36:39] Uploading: 12_combined_carrier_documents_1.pdf
2025-09-18 23:36:39,810 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:39,830 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:39,834 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/52657c04_10_invoice_2.pdf
2025-09-18 23:36:39,834 - INFO - Processing PDF from S3...
2025-09-18 23:36:39,834 - INFO - Downloading PDF from S3 to /tmp/tmpo21q3fnh/52657c04_10_invoice_2.pdf
2025-09-18 23:36:40,722 - INFO - ✓ Uploaded: input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf -> s3://document-extraction-logistically/temp/3423a0a8_12_combined_carrier_documents_1.pdf
2025-09-18 23:36:40,723 - INFO - 🔍 [23:36:40] Starting classification: 12_combined_carrier_documents_1.pdf
2025-09-18 23:36:40,723 - INFO - ⬆️ [23:36:40] Uploading: 12_combined_carrier_documents_2.pdf
2025-09-18 23:36:40,724 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:40,745 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:40,749 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3423a0a8_12_combined_carrier_documents_1.pdf
2025-09-18 23:36:40,749 - INFO - Processing PDF from S3...
2025-09-18 23:36:40,749 - INFO - Downloading PDF from S3 to /tmp/tmpkkks5mhz/3423a0a8_12_combined_carrier_documents_1.pdf
2025-09-18 23:36:41,387 - INFO - ✓ Uploaded: input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf -> s3://document-extraction-logistically/temp/89484609_12_combined_carrier_documents_2.pdf
2025-09-18 23:36:41,387 - INFO - 🔍 [23:36:41] Starting classification: 12_combined_carrier_documents_2.pdf
2025-09-18 23:36:41,387 - INFO - ⬆️ [23:36:41] Uploading: 12_combined_carrier_documents_3.pdf
2025-09-18 23:36:41,388 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:41,406 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:41,410 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/89484609_12_combined_carrier_documents_2.pdf
2025-09-18 23:36:41,411 - INFO - Processing PDF from S3...
2025-09-18 23:36:41,411 - INFO - Downloading PDF from S3 to /tmp/tmp1thlpzli/89484609_12_combined_carrier_documents_2.pdf
2025-09-18 23:36:42,048 - INFO - ✓ Uploaded: input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf -> s3://document-extraction-logistically/temp/c5141f30_12_combined_carrier_documents_3.pdf
2025-09-18 23:36:42,048 - INFO - 🔍 [23:36:42] Starting classification: 12_combined_carrier_documents_3.pdf
2025-09-18 23:36:42,049 - INFO - ⬆️ [23:36:42] Uploading: 13_pack_list_1.pdf
2025-09-18 23:36:42,051 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:42,072 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:42,077 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c5141f30_12_combined_carrier_documents_3.pdf
2025-09-18 23:36:42,077 - INFO - Processing PDF from S3...
2025-09-18 23:36:42,077 - INFO - Downloading PDF from S3 to /tmp/tmpzux8ju0h/c5141f30_12_combined_carrier_documents_3.pdf
2025-09-18 23:36:42,096 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:42,108 - INFO - Splitting PDF 52657c04_10_invoice_2 into 1 pages
2025-09-18 23:36:42,178 - INFO - Split PDF into 1 pages
2025-09-18 23:36:42,179 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:42,179 - INFO - Expected pages: [1]
2025-09-18 23:36:42,525 - INFO - Page 1: Extracted 1418 characters, 97 lines from bc695a49_10_invoice_1_d309ccb0_page_001.pdf
2025-09-18 23:36:42,525 - INFO - Successfully processed page 1
2025-09-18 23:36:42,526 - INFO - Combined 1 pages into final text
2025-09-18 23:36:42,526 - INFO - Text validation for bc695a49_10_invoice_1: 1435 characters, 1 pages
2025-09-18 23:36:42,526 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:42,526 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:42,764 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:42,765 - INFO - Splitting PDF 3423a0a8_12_combined_carrier_documents_1 into 2 pages
2025-09-18 23:36:42,770 - INFO - Split PDF into 2 pages
2025-09-18 23:36:42,770 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:42,770 - INFO - Expected pages: [1, 2]
2025-09-18 23:36:43,145 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:43,146 - INFO - Splitting PDF 89484609_12_combined_carrier_documents_2 into 1 pages
2025-09-18 23:36:43,159 - INFO - Split PDF into 1 pages
2025-09-18 23:36:43,159 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:43,159 - INFO - Expected pages: [1]
2025-09-18 23:36:43,202 - INFO - ✓ Uploaded: input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf -> s3://document-extraction-logistically/temp/af50118b_13_pack_list_1.pdf
2025-09-18 23:36:43,203 - INFO - 🔍 [23:36:43] Starting classification: 13_pack_list_1.pdf
2025-09-18 23:36:43,204 - INFO - ⬆️ [23:36:43] Uploading: 13_pack_list_10.pdf
2025-09-18 23:36:43,206 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:43,225 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:43,229 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/af50118b_13_pack_list_1.pdf
2025-09-18 23:36:43,230 - INFO - Processing PDF from S3...
2025-09-18 23:36:43,230 - INFO - Downloading PDF from S3 to /tmp/tmp6qn0bxfu/af50118b_13_pack_list_1.pdf
2025-09-18 23:36:43,536 - INFO - Page 1: Extracted 659 characters, 47 lines from 83be436c_10_invoice_10_b9d82146_page_001.pdf
2025-09-18 23:36:43,536 - INFO - Successfully processed page 1
2025-09-18 23:36:43,536 - INFO - Combined 1 pages into final text
2025-09-18 23:36:43,536 - INFO - Text validation for 83be436c_10_invoice_10: 676 characters, 1 pages
2025-09-18 23:36:43,536 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:43,536 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:43,909 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:43,910 - INFO - Splitting PDF c5141f30_12_combined_carrier_documents_3 into 1 pages
2025-09-18 23:36:43,921 - INFO - Split PDF into 1 pages
2025-09-18 23:36:43,921 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:43,922 - INFO - Expected pages: [1]
2025-09-18 23:36:45,037 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bc695a49_10_invoice_1.pdf
2025-09-18 23:36:45,364 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:45,367 - INFO - Splitting PDF af50118b_13_pack_list_1 into 2 pages
2025-09-18 23:36:45,383 - INFO - Split PDF into 2 pages
2025-09-18 23:36:45,383 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:45,383 - INFO - Expected pages: [1, 2]
2025-09-18 23:36:45,608 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83be436c_10_invoice_10.pdf
2025-09-18 23:36:45,851 - INFO - ✓ Uploaded: input_data_3_per_cat/13_pack_list/13_pack_list_10.pdf -> s3://document-extraction-logistically/temp/b2c0a712_13_pack_list_10.pdf
2025-09-18 23:36:45,852 - INFO - 🔍 [23:36:45] Starting classification: 13_pack_list_10.pdf
2025-09-18 23:36:45,853 - INFO - ⬆️ [23:36:45] Uploading: 13_pack_list_2.PDF
2025-09-18 23:36:45,853 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:45,871 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:45,876 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b2c0a712_13_pack_list_10.pdf
2025-09-18 23:36:45,877 - INFO - Processing PDF from S3...
2025-09-18 23:36:45,878 - INFO - Downloading PDF from S3 to /tmp/tmp12fy5715/b2c0a712_13_pack_list_10.pdf
2025-09-18 23:36:46,166 - INFO - Page 2: Extracted 313 characters, 9 lines from 3423a0a8_12_combined_carrier_documents_1_4d3cf0c3_page_002.pdf
2025-09-18 23:36:46,167 - INFO - Successfully processed page 2
2025-09-18 23:36:46,783 - INFO - ✓ Uploaded: input_data_3_per_cat/13_pack_list/13_pack_list_2.PDF -> s3://document-extraction-logistically/temp/674a809a_13_pack_list_2.PDF
2025-09-18 23:36:46,783 - INFO - 🔍 [23:36:46] Starting classification: 13_pack_list_2.PDF
2025-09-18 23:36:46,784 - INFO - ⬆️ [23:36:46] Uploading: 14_po_1.pdf
2025-09-18 23:36:46,786 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:46,808 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:46,813 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/674a809a_13_pack_list_2.PDF
2025-09-18 23:36:46,813 - INFO - Processing PDF from S3...
2025-09-18 23:36:46,814 - INFO - Downloading PDF from S3 to /tmp/tmpryseyhz2/674a809a_13_pack_list_2.PDF
2025-09-18 23:36:47,254 - INFO - Page 1: Extracted 940 characters, 39 lines from 52657c04_10_invoice_2_dbae87bf_page_001.pdf
2025-09-18 23:36:47,254 - INFO - Successfully processed page 1
2025-09-18 23:36:47,254 - INFO - Combined 1 pages into final text
2025-09-18 23:36:47,254 - INFO - Text validation for 52657c04_10_invoice_2: 957 characters, 1 pages
2025-09-18 23:36:47,255 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:47,255 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:47,844 - INFO - ✓ Uploaded: input_data_3_per_cat/14_po/14_po_1.pdf -> s3://document-extraction-logistically/temp/44ef1580_14_po_1.pdf
2025-09-18 23:36:47,845 - INFO - 🔍 [23:36:47] Starting classification: 14_po_1.pdf
2025-09-18 23:36:47,846 - INFO - ⬆️ [23:36:47] Uploading: 14_po_2.pdf
2025-09-18 23:36:47,846 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:47,871 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:47,877 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/44ef1580_14_po_1.pdf
2025-09-18 23:36:47,877 - INFO - Processing PDF from S3...
2025-09-18 23:36:47,878 - INFO - Downloading PDF from S3 to /tmp/tmpun08gq31/44ef1580_14_po_1.pdf
2025-09-18 23:36:48,177 - INFO - Page 1: Extracted 1231 characters, 84 lines from 3423a0a8_12_combined_carrier_documents_1_4d3cf0c3_page_001.pdf
2025-09-18 23:36:48,177 - INFO - Successfully processed page 1
2025-09-18 23:36:48,177 - INFO - Combined 2 pages into final text
2025-09-18 23:36:48,177 - INFO - Text validation for 3423a0a8_12_combined_carrier_documents_1: 1580 characters, 2 pages
2025-09-18 23:36:48,177 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:48,177 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:48,575 - INFO - ✓ Uploaded: input_data_3_per_cat/14_po/14_po_2.pdf -> s3://document-extraction-logistically/temp/ff680802_14_po_2.pdf
2025-09-18 23:36:48,576 - INFO - 🔍 [23:36:48] Starting classification: 14_po_2.pdf
2025-09-18 23:36:48,577 - INFO - ⬆️ [23:36:48] Uploading: 14_po_3.pdf
2025-09-18 23:36:48,578 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:48,595 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:48,599 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ff680802_14_po_2.pdf
2025-09-18 23:36:48,599 - INFO - Processing PDF from S3...
2025-09-18 23:36:48,599 - INFO - Downloading PDF from S3 to /tmp/tmp7lxr_zh7/ff680802_14_po_2.pdf
2025-09-18 23:36:48,923 - INFO - Page 1: Extracted 4164 characters, 222 lines from 89484609_12_combined_carrier_documents_2_8ebaf89d_page_001.pdf
2025-09-18 23:36:48,923 - INFO - Successfully processed page 1
2025-09-18 23:36:48,924 - INFO - Combined 1 pages into final text
2025-09-18 23:36:48,924 - INFO - Text validation for 89484609_12_combined_carrier_documents_2: 4181 characters, 1 pages
2025-09-18 23:36:48,924 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:48,924 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:49,190 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/52657c04_10_invoice_2.pdf
2025-09-18 23:36:49,226 - INFO - ✓ Uploaded: input_data_3_per_cat/14_po/14_po_3.pdf -> s3://document-extraction-logistically/temp/c822818e_14_po_3.pdf
2025-09-18 23:36:49,226 - INFO - 🔍 [23:36:49] Starting classification: 14_po_3.pdf
2025-09-18 23:36:49,226 - INFO - ⬆️ [23:36:49] Uploading: 15_comm_invoice_1.pdf
2025-09-18 23:36:49,227 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:49,236 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:49,238 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c822818e_14_po_3.pdf
2025-09-18 23:36:49,238 - INFO - Processing PDF from S3...
2025-09-18 23:36:49,238 - INFO - Downloading PDF from S3 to /tmp/tmphzwzwjv3/c822818e_14_po_3.pdf
2025-09-18 23:36:49,425 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:49,426 - INFO - Splitting PDF 674a809a_13_pack_list_2 into 1 pages
2025-09-18 23:36:49,437 - INFO - Split PDF into 1 pages
2025-09-18 23:36:49,437 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:49,437 - INFO - Expected pages: [1]
2025-09-18 23:36:49,519 - INFO - Page 2: Extracted 276 characters, 20 lines from af50118b_13_pack_list_1_97ed6764_page_002.pdf
2025-09-18 23:36:49,520 - INFO - Successfully processed page 2
2025-09-18 23:36:49,821 - INFO - Page 1: Extracted 4024 characters, 186 lines from c5141f30_12_combined_carrier_documents_3_4eeab88f_page_001.pdf
2025-09-18 23:36:49,822 - INFO - Successfully processed page 1
2025-09-18 23:36:49,822 - INFO - Combined 1 pages into final text
2025-09-18 23:36:49,822 - INFO - Text validation for c5141f30_12_combined_carrier_documents_3: 4041 characters, 1 pages
2025-09-18 23:36:49,822 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:49,822 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:49,850 - INFO - ✓ Uploaded: input_data_3_per_cat/15_comm_invoice/15_comm_invoice_1.pdf -> s3://document-extraction-logistically/temp/14386cf6_15_comm_invoice_1.pdf
2025-09-18 23:36:49,851 - INFO - 🔍 [23:36:49] Starting classification: 15_comm_invoice_1.pdf
2025-09-18 23:36:49,851 - INFO - ⬆️ [23:36:49] Uploading: 15_comm_invoice_2.pdf
2025-09-18 23:36:49,852 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:49,864 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:49,867 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/14386cf6_15_comm_invoice_1.pdf
2025-09-18 23:36:49,868 - INFO - Processing PDF from S3...
2025-09-18 23:36:49,868 - INFO - Downloading PDF from S3 to /tmp/tmpq2t2r_dc/14386cf6_15_comm_invoice_1.pdf
2025-09-18 23:36:50,384 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:50,385 - INFO - Splitting PDF 44ef1580_14_po_1 into 1 pages
2025-09-18 23:36:50,392 - INFO - Split PDF into 1 pages
2025-09-18 23:36:50,392 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:50,392 - INFO - Expected pages: [1]
2025-09-18 23:36:50,471 - INFO - ✓ Uploaded: input_data_3_per_cat/15_comm_invoice/15_comm_invoice_2.pdf -> s3://document-extraction-logistically/temp/819f64ce_15_comm_invoice_2.pdf
2025-09-18 23:36:50,472 - INFO - 🔍 [23:36:50] Starting classification: 15_comm_invoice_2.pdf
2025-09-18 23:36:50,473 - INFO - ⬆️ [23:36:50] Uploading: 16_customs_doc_1.pdf
2025-09-18 23:36:50,475 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:50,496 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:50,501 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/819f64ce_15_comm_invoice_2.pdf
2025-09-18 23:36:50,502 - INFO - Processing PDF from S3...
2025-09-18 23:36:50,502 - INFO - Downloading PDF from S3 to /tmp/tmpnexpnokn/819f64ce_15_comm_invoice_2.pdf
2025-09-18 23:36:50,939 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:50,940 - INFO - Splitting PDF ff680802_14_po_2 into 1 pages
2025-09-18 23:36:50,948 - INFO - Split PDF into 1 pages
2025-09-18 23:36:50,948 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:50,948 - INFO - Expected pages: [1]
2025-09-18 23:36:51,002 - INFO - Page 1: Extracted 1156 characters, 65 lines from af50118b_13_pack_list_1_97ed6764_page_001.pdf
2025-09-18 23:36:51,002 - INFO - Successfully processed page 1
2025-09-18 23:36:51,002 - INFO - Combined 2 pages into final text
2025-09-18 23:36:51,002 - INFO - Text validation for af50118b_13_pack_list_1: 1468 characters, 2 pages
2025-09-18 23:36:51,003 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:51,003 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:51,051 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:51,052 - INFO - Splitting PDF c822818e_14_po_3 into 2 pages
2025-09-18 23:36:51,058 - INFO - Split PDF into 2 pages
2025-09-18 23:36:51,058 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:51,058 - INFO - Expected pages: [1, 2]
2025-09-18 23:36:51,116 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3423a0a8_12_combined_carrier_documents_1.pdf
2025-09-18 23:36:51,128 - INFO - ✓ Uploaded: input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf -> s3://document-extraction-logistically/temp/1bd841e4_16_customs_doc_1.pdf
2025-09-18 23:36:51,128 - INFO - 🔍 [23:36:51] Starting classification: 16_customs_doc_1.pdf
2025-09-18 23:36:51,129 - INFO - ⬆️ [23:36:51] Uploading: 16_customs_doc_2.tiff
2025-09-18 23:36:51,131 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:51,154 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:51,158 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1bd841e4_16_customs_doc_1.pdf
2025-09-18 23:36:51,158 - INFO - Processing PDF from S3...
2025-09-18 23:36:51,159 - INFO - Downloading PDF from S3 to /tmp/tmp65wh71zl/1bd841e4_16_customs_doc_1.pdf
2025-09-18 23:36:51,310 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:51,311 - INFO - Splitting PDF 14386cf6_15_comm_invoice_1 into 1 pages
2025-09-18 23:36:51,312 - INFO - Split PDF into 1 pages
2025-09-18 23:36:51,312 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:51,312 - INFO - Expected pages: [1]
2025-09-18 23:36:51,393 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/89484609_12_combined_carrier_documents_2.pdf
2025-09-18 23:36:51,793 - INFO - ✓ Uploaded: input_data_3_per_cat/16_customs_doc/16_customs_doc_2.tiff -> s3://document-extraction-logistically/temp/650428d9_16_customs_doc_2.tiff
2025-09-18 23:36:51,793 - INFO - 🔍 [23:36:51] Starting classification: 16_customs_doc_2.tiff
2025-09-18 23:36:51,793 - INFO - ⬆️ [23:36:51] Uploading: 16_customs_doc_3.pdf
2025-09-18 23:36:51,794 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:51,814 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:51,820 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/650428d9_16_customs_doc_2.tiff
2025-09-18 23:36:51,820 - INFO - Processing image from S3...
2025-09-18 23:36:52,007 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:52,009 - INFO - Splitting PDF 819f64ce_15_comm_invoice_2 into 1 pages
2025-09-18 23:36:52,012 - INFO - Split PDF into 1 pages
2025-09-18 23:36:52,012 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:52,012 - INFO - Expected pages: [1]
2025-09-18 23:36:52,381 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c5141f30_12_combined_carrier_documents_3.pdf
2025-09-18 23:36:52,461 - INFO - ✓ Uploaded: input_data_3_per_cat/16_customs_doc/16_customs_doc_3.pdf -> s3://document-extraction-logistically/temp/485a455d_16_customs_doc_3.pdf
2025-09-18 23:36:52,461 - INFO - 🔍 [23:36:52] Starting classification: 16_customs_doc_3.pdf
2025-09-18 23:36:52,462 - INFO - ⬆️ [23:36:52] Uploading: 17_nmfc_cert_1.jpg
2025-09-18 23:36:52,464 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:52,479 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:52,486 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/485a455d_16_customs_doc_3.pdf
2025-09-18 23:36:52,487 - INFO - Processing PDF from S3...
2025-09-18 23:36:52,487 - INFO - Downloading PDF from S3 to /tmp/tmpw4yjuc58/485a455d_16_customs_doc_3.pdf
2025-09-18 23:36:52,678 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:52,681 - INFO - Splitting PDF 1bd841e4_16_customs_doc_1 into 3 pages
2025-09-18 23:36:52,697 - INFO - Split PDF into 3 pages
2025-09-18 23:36:52,697 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:52,697 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:36:53,076 - INFO - ✓ Uploaded: input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_1.jpg -> s3://document-extraction-logistically/temp/6dada468_17_nmfc_cert_1.jpg
2025-09-18 23:36:53,076 - INFO - 🔍 [23:36:53] Starting classification: 17_nmfc_cert_1.jpg
2025-09-18 23:36:53,077 - INFO - ⬆️ [23:36:53] Uploading: 17_nmfc_cert_2.pdf
2025-09-18 23:36:53,078 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:53,095 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:53,099 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6dada468_17_nmfc_cert_1.jpg
2025-09-18 23:36:53,099 - INFO - Processing image from S3...
2025-09-18 23:36:53,148 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/af50118b_13_pack_list_1.pdf
2025-09-18 23:36:53,521 - WARNING - Sync method failed for S3 image temp/650428d9_16_customs_doc_2.tiff, trying async method: An error occurred (UnsupportedDocumentException) when calling the DetectDocumentText operation: Request has unsupported document format
2025-09-18 23:36:53,723 - INFO - ✓ Uploaded: input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf -> s3://document-extraction-logistically/temp/066db343_17_nmfc_cert_2.pdf
2025-09-18 23:36:53,723 - INFO - 🔍 [23:36:53] Starting classification: 17_nmfc_cert_2.pdf
2025-09-18 23:36:53,724 - INFO - ⬆️ [23:36:53] Uploading: 19_coa_1.pdf
2025-09-18 23:36:53,724 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:53,740 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:53,743 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/066db343_17_nmfc_cert_2.pdf
2025-09-18 23:36:53,743 - INFO - Processing PDF from S3...
2025-09-18 23:36:53,743 - INFO - Downloading PDF from S3 to /tmp/tmp16e8kn1c/066db343_17_nmfc_cert_2.pdf
2025-09-18 23:36:54,542 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:54,544 - INFO - Splitting PDF 485a455d_16_customs_doc_3 into 1 pages
2025-09-18 23:36:54,547 - INFO - Split PDF into 1 pages
2025-09-18 23:36:54,547 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:54,547 - INFO - Expected pages: [1]
2025-09-18 23:36:54,838 - INFO - Page 2: Extracted 669 characters, 16 lines from c822818e_14_po_3_37de6790_page_002.pdf
2025-09-18 23:36:54,839 - INFO - Successfully processed page 2
2025-09-18 23:36:54,922 - INFO - Started async Textract job 1c246a66805bf4f76989c87f9f87c7dee708aa19f7970d324ce9bc1eca59f4f0 for S3 image temp/650428d9_16_customs_doc_2.tiff
2025-09-18 23:36:55,224 - INFO -     Poll #1 - Status: IN_PROGRESS (Elapsed: 0s)
2025-09-18 23:36:55,249 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:55,251 - INFO - Splitting PDF 066db343_17_nmfc_cert_2 into 2 pages
2025-09-18 23:36:55,255 - INFO - Split PDF into 2 pages
2025-09-18 23:36:55,255 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:55,255 - INFO - Expected pages: [1, 2]
2025-09-18 23:36:55,261 - INFO - ✓ Uploaded: input_data_3_per_cat/19_coa/19_coa_1.pdf -> s3://document-extraction-logistically/temp/52dcd349_19_coa_1.pdf
2025-09-18 23:36:55,262 - INFO - 🔍 [23:36:55] Starting classification: 19_coa_1.pdf
2025-09-18 23:36:55,262 - INFO - ⬆️ [23:36:55] Uploading: 19_coa_2.pdf
2025-09-18 23:36:55,264 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:55,277 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:55,279 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/52dcd349_19_coa_1.pdf
2025-09-18 23:36:55,280 - INFO - Processing PDF from S3...
2025-09-18 23:36:55,280 - INFO - Downloading PDF from S3 to /tmp/tmphukypaf1/52dcd349_19_coa_1.pdf
2025-09-18 23:36:55,516 - INFO - Page 1: Extracted 1011 characters, 51 lines from 14386cf6_15_comm_invoice_1_91467f48_page_001.pdf
2025-09-18 23:36:55,516 - INFO - Successfully processed page 1
2025-09-18 23:36:55,516 - INFO - Combined 1 pages into final text
2025-09-18 23:36:55,516 - INFO - Text validation for 14386cf6_15_comm_invoice_1: 1028 characters, 1 pages
2025-09-18 23:36:55,517 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:55,517 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:55,739 - INFO - Page 1: Extracted 690 characters, 57 lines from 44ef1580_14_po_1_6eb45bda_page_001.pdf
2025-09-18 23:36:55,740 - INFO - Successfully processed page 1
2025-09-18 23:36:55,740 - INFO - Combined 1 pages into final text
2025-09-18 23:36:55,741 - INFO - Text validation for 44ef1580_14_po_1: 707 characters, 1 pages
2025-09-18 23:36:55,757 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:55,758 - INFO - Page 1: Extracted 1432 characters, 83 lines from c822818e_14_po_3_37de6790_page_001.pdf
2025-09-18 23:36:55,758 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:55,760 - INFO - Successfully processed page 1
2025-09-18 23:36:55,761 - INFO - Combined 2 pages into final text
2025-09-18 23:36:55,761 - INFO - Text validation for c822818e_14_po_3: 2137 characters, 2 pages
2025-09-18 23:36:55,761 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:55,761 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:55,907 - INFO - ✓ Uploaded: input_data_3_per_cat/19_coa/19_coa_2.pdf -> s3://document-extraction-logistically/temp/24d1bf92_19_coa_2.pdf
2025-09-18 23:36:55,907 - INFO - 🔍 [23:36:55] Starting classification: 19_coa_2.pdf
2025-09-18 23:36:55,908 - INFO - ⬆️ [23:36:55] Uploading: 19_coa_3.pdf
2025-09-18 23:36:55,909 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:55,930 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:55,934 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/24d1bf92_19_coa_2.pdf
2025-09-18 23:36:55,934 - INFO - Processing PDF from S3...
2025-09-18 23:36:55,935 - INFO - Downloading PDF from S3 to /tmp/tmpcgbzmbdz/24d1bf92_19_coa_2.pdf
2025-09-18 23:36:56,208 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:56,211 - INFO - Splitting PDF b2c0a712_13_pack_list_10 into 1 pages
2025-09-18 23:36:56,212 - INFO - Split PDF into 1 pages
2025-09-18 23:36:56,213 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:56,213 - INFO - Expected pages: [1]
2025-09-18 23:36:56,476 - INFO - Page 1: Extracted 1011 characters, 51 lines from 819f64ce_15_comm_invoice_2_5ab0c036_page_001.pdf
2025-09-18 23:36:56,477 - INFO - Successfully processed page 1
2025-09-18 23:36:56,477 - INFO - Combined 1 pages into final text
2025-09-18 23:36:56,477 - INFO - Text validation for 819f64ce_15_comm_invoice_2: 1028 characters, 1 pages
2025-09-18 23:36:56,477 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:56,478 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:56,536 - INFO - ✓ Uploaded: input_data_3_per_cat/19_coa/19_coa_3.pdf -> s3://document-extraction-logistically/temp/537d7eb5_19_coa_3.pdf
2025-09-18 23:36:56,537 - INFO - 🔍 [23:36:56] Starting classification: 19_coa_3.pdf
2025-09-18 23:36:56,537 - INFO - ⬆️ [23:36:56] Uploading: 1_bol_1.pdf
2025-09-18 23:36:56,540 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:56,546 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:56,548 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/537d7eb5_19_coa_3.pdf
2025-09-18 23:36:56,548 - INFO - Processing PDF from S3...
2025-09-18 23:36:56,548 - INFO - Downloading PDF from S3 to /tmp/tmpoxmm92vg/537d7eb5_19_coa_3.pdf
2025-09-18 23:36:56,751 - INFO - S3 Image temp/6dada468_17_nmfc_cert_1.jpg: Extracted 825 characters, 77 lines
2025-09-18 23:36:56,751 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:56,752 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:56,984 - INFO - Page 3: Extracted 1410 characters, 53 lines from 1bd841e4_16_customs_doc_1_a424749c_page_003.pdf
2025-09-18 23:36:56,984 - INFO - Successfully processed page 3
2025-09-18 23:36:57,077 - INFO - Page 1: Extracted 1774 characters, 128 lines from 674a809a_13_pack_list_2_0b350654_page_001.pdf
2025-09-18 23:36:57,077 - INFO - Successfully processed page 1
2025-09-18 23:36:57,077 - INFO - Combined 1 pages into final text
2025-09-18 23:36:57,078 - INFO - Text validation for 674a809a_13_pack_list_2: 1791 characters, 1 pages
2025-09-18 23:36:57,078 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:57,078 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:57,156 - INFO - ✓ Uploaded: input_data_3_per_cat/1_bol/1_bol_1.pdf -> s3://document-extraction-logistically/temp/0fd8e9c1_1_bol_1.pdf
2025-09-18 23:36:57,157 - INFO - 🔍 [23:36:57] Starting classification: 1_bol_1.pdf
2025-09-18 23:36:57,158 - INFO - ⬆️ [23:36:57] Uploading: 1_bol_10.pdf
2025-09-18 23:36:57,160 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:57,177 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:57,181 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0fd8e9c1_1_bol_1.pdf
2025-09-18 23:36:57,182 - INFO - Processing PDF from S3...
2025-09-18 23:36:57,182 - INFO - Downloading PDF from S3 to /tmp/tmpu57_r4y3/0fd8e9c1_1_bol_1.pdf
2025-09-18 23:36:57,247 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/14386cf6_15_comm_invoice_1.pdf
2025-09-18 23:36:57,270 - INFO - Page 1: Extracted 2057 characters, 116 lines from 1bd841e4_16_customs_doc_1_a424749c_page_001.pdf
2025-09-18 23:36:57,272 - INFO - Successfully processed page 1
2025-09-18 23:36:57,376 - INFO - Page 1: Extracted 813 characters, 71 lines from ff680802_14_po_2_fbbc176c_page_001.pdf
2025-09-18 23:36:57,376 - INFO - Successfully processed page 1
2025-09-18 23:36:57,376 - INFO - Combined 1 pages into final text
2025-09-18 23:36:57,376 - INFO - Text validation for ff680802_14_po_2: 830 characters, 1 pages
2025-09-18 23:36:57,376 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:57,376 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:57,428 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/44ef1580_14_po_1.pdf
2025-09-18 23:36:57,492 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c822818e_14_po_3.pdf
2025-09-18 23:36:57,520 - INFO -     Poll #2 - Status: IN_PROGRESS (Elapsed: 2s)
2025-09-18 23:36:58,118 - INFO - ✓ Uploaded: input_data_3_per_cat/1_bol/1_bol_10.pdf -> s3://document-extraction-logistically/temp/4bdb7d4e_1_bol_10.pdf
2025-09-18 23:36:58,118 - INFO - 🔍 [23:36:58] Starting classification: 1_bol_10.pdf
2025-09-18 23:36:58,120 - INFO - ⬆️ [23:36:58] Uploading: 1_bol_11.pdf
2025-09-18 23:36:58,120 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:58,160 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:58,167 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4bdb7d4e_1_bol_10.pdf
2025-09-18 23:36:58,168 - INFO - Processing PDF from S3...
2025-09-18 23:36:58,169 - INFO - Downloading PDF from S3 to /tmp/tmpv5jaiakc/4bdb7d4e_1_bol_10.pdf
2025-09-18 23:36:58,178 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:58,179 - INFO - Splitting PDF 52dcd349_19_coa_1 into 1 pages
2025-09-18 23:36:58,182 - INFO - Split PDF into 1 pages
2025-09-18 23:36:58,182 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:58,182 - INFO - Expected pages: [1]
2025-09-18 23:36:58,218 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:58,220 - INFO - Splitting PDF 24d1bf92_19_coa_2 into 1 pages
2025-09-18 23:36:58,231 - INFO - Split PDF into 1 pages
2025-09-18 23:36:58,231 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:58,232 - INFO - Expected pages: [1]
2025-09-18 23:36:58,508 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:58,509 - INFO - Splitting PDF 537d7eb5_19_coa_3 into 1 pages
2025-09-18 23:36:58,517 - INFO - Split PDF into 1 pages
2025-09-18 23:36:58,517 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:58,517 - INFO - Expected pages: [1]
2025-09-18 23:36:58,706 - INFO - Page 2: Extracted 3834 characters, 261 lines from 1bd841e4_16_customs_doc_1_a424749c_page_002.pdf
2025-09-18 23:36:58,706 - INFO - Successfully processed page 2
2025-09-18 23:36:58,707 - INFO - Combined 3 pages into final text
2025-09-18 23:36:58,707 - INFO - Text validation for 1bd841e4_16_customs_doc_1: 7356 characters, 3 pages
2025-09-18 23:36:58,707 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:36:58,707 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:36:58,724 - INFO - Splitting PDF into individual pages...
2025-09-18 23:36:58,725 - INFO - Splitting PDF 0fd8e9c1_1_bol_1 into 1 pages
2025-09-18 23:36:58,727 - INFO - Split PDF into 1 pages
2025-09-18 23:36:58,727 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:36:58,727 - INFO - Expected pages: [1]
2025-09-18 23:36:58,886 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/819f64ce_15_comm_invoice_2.pdf
2025-09-18 23:36:59,093 - INFO - ✓ Uploaded: input_data_3_per_cat/1_bol/1_bol_11.pdf -> s3://document-extraction-logistically/temp/e762725e_1_bol_11.pdf
2025-09-18 23:36:59,094 - INFO - 🔍 [23:36:59] Starting classification: 1_bol_11.pdf
2025-09-18 23:36:59,094 - INFO - ⬆️ [23:36:59] Uploading: 20_tender_from_cust_1.pdf
2025-09-18 23:36:59,099 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:59,108 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:59,112 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e762725e_1_bol_11.pdf
2025-09-18 23:36:59,112 - INFO - Processing PDF from S3...
2025-09-18 23:36:59,119 - INFO - Downloading PDF from S3 to /tmp/tmpfwojfv7e/e762725e_1_bol_11.pdf
2025-09-18 23:36:59,124 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6dada468_17_nmfc_cert_1.jpg
2025-09-18 23:36:59,167 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ff680802_14_po_2.pdf
2025-09-18 23:36:59,797 - INFO - Page 1: Extracted 787 characters, 61 lines from 066db343_17_nmfc_cert_2_89ffc706_page_001.pdf
2025-09-18 23:36:59,798 - INFO - Successfully processed page 1
2025-09-18 23:36:59,799 - INFO - ✓ Uploaded: input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf -> s3://document-extraction-logistically/temp/c300a5a5_20_tender_from_cust_1.pdf
2025-09-18 23:36:59,800 - INFO - 🔍 [23:36:59] Starting classification: 20_tender_from_cust_1.pdf
2025-09-18 23:36:59,800 - INFO - ⬆️ [23:36:59] Uploading: 20_tender_from_cust_2.pdf
2025-09-18 23:36:59,801 - INFO - Initializing TextractProcessor...
2025-09-18 23:36:59,817 - INFO - Initializing BedrockProcessor...
2025-09-18 23:36:59,820 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c300a5a5_20_tender_from_cust_1.pdf
2025-09-18 23:36:59,820 - INFO - Processing PDF from S3...
2025-09-18 23:36:59,820 - INFO - Downloading PDF from S3 to /tmp/tmp86vw41lx/c300a5a5_20_tender_from_cust_1.pdf
2025-09-18 23:36:59,826 - INFO -     Poll #3 - Status: IN_PROGRESS (Elapsed: 4s)
2025-09-18 23:37:00,370 - INFO - Page 2: Extracted 485 characters, 29 lines from 066db343_17_nmfc_cert_2_89ffc706_page_002.pdf
2025-09-18 23:37:00,371 - INFO - Successfully processed page 2
2025-09-18 23:37:00,372 - INFO - Combined 2 pages into final text
2025-09-18 23:37:00,372 - INFO - Text validation for 066db343_17_nmfc_cert_2: 1308 characters, 2 pages
2025-09-18 23:37:00,373 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:00,373 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:00,395 - INFO - ✓ Uploaded: input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf -> s3://document-extraction-logistically/temp/6cb81e2a_20_tender_from_cust_2.pdf
2025-09-18 23:37:00,395 - INFO - 🔍 [23:37:00] Starting classification: 20_tender_from_cust_2.pdf
2025-09-18 23:37:00,396 - INFO - ⬆️ [23:37:00] Uploading: 20_tender_from_cust_3.pdf
2025-09-18 23:37:00,397 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:00,413 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:00,416 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6cb81e2a_20_tender_from_cust_2.pdf
2025-09-18 23:37:00,416 - INFO - Processing PDF from S3...
2025-09-18 23:37:00,416 - INFO - Downloading PDF from S3 to /tmp/tmpsj6g17ep/6cb81e2a_20_tender_from_cust_2.pdf
2025-09-18 23:37:00,796 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/674a809a_13_pack_list_2.PDF
2025-09-18 23:37:01,019 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:01,020 - INFO - ✓ Uploaded: input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf -> s3://document-extraction-logistically/temp/bdedd3ed_20_tender_from_cust_3.pdf
2025-09-18 23:37:01,020 - INFO - 🔍 [23:37:01] Starting classification: 20_tender_from_cust_3.pdf
2025-09-18 23:37:01,021 - INFO - ⬆️ [23:37:01] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 23:37:01,023 - INFO - Splitting PDF c300a5a5_20_tender_from_cust_1 into 2 pages
2025-09-18 23:37:01,024 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:01,053 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:01,060 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bdedd3ed_20_tender_from_cust_3.pdf
2025-09-18 23:37:01,061 - INFO - Processing PDF from S3...
2025-09-18 23:37:01,062 - INFO - Downloading PDF from S3 to /tmp/tmpvpims_zd/bdedd3ed_20_tender_from_cust_3.pdf
2025-09-18 23:37:01,062 - INFO - Split PDF into 2 pages
2025-09-18 23:37:01,070 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:01,071 - INFO - Expected pages: [1, 2]
2025-09-18 23:37:01,343 - INFO - Page 1: Extracted 1069 characters, 112 lines from b2c0a712_13_pack_list_10_a7fcb477_page_001.pdf
2025-09-18 23:37:01,345 - INFO - Successfully processed page 1
2025-09-18 23:37:01,346 - INFO - Combined 1 pages into final text
2025-09-18 23:37:01,346 - INFO - Text validation for b2c0a712_13_pack_list_10: 1086 characters, 1 pages
2025-09-18 23:37:01,346 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:01,346 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:01,631 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:01,632 - INFO - Splitting PDF 6cb81e2a_20_tender_from_cust_2 into 2 pages
2025-09-18 23:37:01,633 - INFO - Split PDF into 2 pages
2025-09-18 23:37:01,633 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:01,633 - INFO - Expected pages: [1, 2]
2025-09-18 23:37:01,687 - INFO - ✓ Uploaded: input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/669b6a63_21_lumper_receipt_1.jpeg
2025-09-18 23:37:01,687 - INFO - 🔍 [23:37:01] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 23:37:01,687 - INFO - ⬆️ [23:37:01] Uploading: 21_lumper_receipt_10.png
2025-09-18 23:37:01,688 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:01,702 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:01,705 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/669b6a63_21_lumper_receipt_1.jpeg
2025-09-18 23:37:01,705 - INFO - Processing image from S3...
2025-09-18 23:37:02,096 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:02,098 - INFO - Splitting PDF 4bdb7d4e_1_bol_10 into 1 pages
2025-09-18 23:37:02,100 - INFO - Split PDF into 1 pages
2025-09-18 23:37:02,100 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:02,100 - INFO - Expected pages: [1]
2025-09-18 23:37:02,213 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1bd841e4_16_customs_doc_1.pdf
2025-09-18 23:37:02,329 - INFO - ✓ Uploaded: input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/4f3952e5_21_lumper_receipt_10.png
2025-09-18 23:37:02,329 - INFO - 🔍 [23:37:02] Starting classification: 21_lumper_receipt_10.png
2025-09-18 23:37:02,330 - INFO - ⬆️ [23:37:02] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 23:37:02,330 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:02,349 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:02,356 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4f3952e5_21_lumper_receipt_10.png
2025-09-18 23:37:02,356 - INFO - Processing image from S3...
2025-09-18 23:37:02,874 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:02,876 - INFO - Splitting PDF bdedd3ed_20_tender_from_cust_3 into 3 pages
2025-09-18 23:37:02,891 - INFO - Split PDF into 3 pages
2025-09-18 23:37:02,891 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:02,891 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:37:03,052 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/066db343_17_nmfc_cert_2.pdf
2025-09-18 23:37:03,165 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b2c0a712_13_pack_list_10.pdf
2025-09-18 23:37:03,266 - INFO - Page 1: Extracted 590 characters, 28 lines from 24d1bf92_19_coa_2_2a61c7ea_page_001.pdf
2025-09-18 23:37:03,266 - INFO - Successfully processed page 1
2025-09-18 23:37:03,266 - INFO - Combined 1 pages into final text
2025-09-18 23:37:03,267 - INFO - Text validation for 24d1bf92_19_coa_2: 607 characters, 1 pages
2025-09-18 23:37:03,267 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:03,267 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:03,426 - INFO - Page 1: Extracted 879 characters, 30 lines from 537d7eb5_19_coa_3_935d4b7a_page_001.pdf
2025-09-18 23:37:03,426 - INFO - Successfully processed page 1
2025-09-18 23:37:03,426 - INFO - Combined 1 pages into final text
2025-09-18 23:37:03,426 - INFO - Text validation for 537d7eb5_19_coa_3: 896 characters, 1 pages
2025-09-18 23:37:03,427 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:03,427 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:03,532 - INFO - Page 2: Extracted 29 characters, 2 lines from c300a5a5_20_tender_from_cust_1_0d4c423a_page_002.pdf
2025-09-18 23:37:03,533 - INFO - Successfully processed page 2
2025-09-18 23:37:03,581 - INFO - ✓ Uploaded: input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/c87b52ed_21_lumper_receipt_11.jpeg
2025-09-18 23:37:03,581 - INFO - 🔍 [23:37:03] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 23:37:03,581 - INFO - ⬆️ [23:37:03] Uploading: 22_so_confirmation_1.pdf
2025-09-18 23:37:03,584 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:03,592 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:03,593 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c87b52ed_21_lumper_receipt_11.jpeg
2025-09-18 23:37:03,593 - INFO - Processing image from S3...
2025-09-18 23:37:03,654 - INFO -     Poll #4 - Status: SUCCEEDED (Elapsed: 6s)
2025-09-18 23:37:03,654 - INFO - Async method succeeded for S3 image temp/650428d9_16_customs_doc_2.tiff
2025-09-18 23:37:03,654 - INFO - S3 Image temp/650428d9_16_customs_doc_2.tiff: Extracted 4903 characters, 186 lines
2025-09-18 23:37:03,654 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:03,654 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:03,980 - INFO - Page 2: Extracted 29 characters, 2 lines from 6cb81e2a_20_tender_from_cust_2_8f71b55f_page_002.pdf
2025-09-18 23:37:03,980 - INFO - Successfully processed page 2
2025-09-18 23:37:04,179 - INFO - Page 1: Extracted 4011 characters, 246 lines from 485a455d_16_customs_doc_3_ce164d7a_page_001.pdf
2025-09-18 23:37:04,179 - INFO - Successfully processed page 1
2025-09-18 23:37:04,180 - INFO - Combined 1 pages into final text
2025-09-18 23:37:04,180 - INFO - Text validation for 485a455d_16_customs_doc_3: 4028 characters, 1 pages
2025-09-18 23:37:04,180 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:04,180 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:04,200 - INFO - ✓ Uploaded: input_data_3_per_cat/22_so_confirmation/22_so_confirmation_1.pdf -> s3://document-extraction-logistically/temp/f5c4300a_22_so_confirmation_1.pdf
2025-09-18 23:37:04,200 - INFO - 🔍 [23:37:04] Starting classification: 22_so_confirmation_1.pdf
2025-09-18 23:37:04,201 - INFO - ⬆️ [23:37:04] Uploading: 22_so_confirmation_2.pdf
2025-09-18 23:37:04,202 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:04,302 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:04,305 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f5c4300a_22_so_confirmation_1.pdf
2025-09-18 23:37:04,305 - INFO - Processing PDF from S3...
2025-09-18 23:37:04,305 - INFO - Downloading PDF from S3 to /tmp/tmpn5nwwmqm/f5c4300a_22_so_confirmation_1.pdf
2025-09-18 23:37:04,408 - INFO - S3 Image temp/669b6a63_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 23:37:04,409 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:04,409 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:04,455 - INFO - Page 1: Extracted 517 characters, 56 lines from 52dcd349_19_coa_1_8d97195e_page_001.pdf
2025-09-18 23:37:04,455 - INFO - Successfully processed page 1
2025-09-18 23:37:04,455 - INFO - Combined 1 pages into final text
2025-09-18 23:37:04,455 - INFO - Text validation for 52dcd349_19_coa_1: 534 characters, 1 pages
2025-09-18 23:37:04,456 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:04,456 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:04,850 - INFO - ✓ Uploaded: input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf -> s3://document-extraction-logistically/temp/ea7e920f_22_so_confirmation_2.pdf
2025-09-18 23:37:04,850 - INFO - 🔍 [23:37:04] Starting classification: 22_so_confirmation_2.pdf
2025-09-18 23:37:04,851 - INFO - ⬆️ [23:37:04] Uploading: 22_so_confirmation_3.pdf
2025-09-18 23:37:04,854 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:04,871 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:04,875 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ea7e920f_22_so_confirmation_2.pdf
2025-09-18 23:37:04,875 - INFO - Processing PDF from S3...
2025-09-18 23:37:04,875 - INFO - Downloading PDF from S3 to /tmp/tmpyj0bwogs/ea7e920f_22_so_confirmation_2.pdf
2025-09-18 23:37:04,998 - INFO - Page 1: Extracted 1339 characters, 75 lines from c300a5a5_20_tender_from_cust_1_0d4c423a_page_001.pdf
2025-09-18 23:37:04,999 - INFO - Successfully processed page 1
2025-09-18 23:37:04,999 - INFO - Combined 2 pages into final text
2025-09-18 23:37:04,999 - INFO - Text validation for c300a5a5_20_tender_from_cust_1: 1404 characters, 2 pages
2025-09-18 23:37:05,000 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:05,000 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:05,054 - INFO - S3 Image temp/4f3952e5_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 23:37:05,055 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:05,055 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:05,513 - INFO - ✓ Uploaded: input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf -> s3://document-extraction-logistically/temp/286e3749_22_so_confirmation_3.pdf
2025-09-18 23:37:05,513 - INFO - 🔍 [23:37:05] Starting classification: 22_so_confirmation_3.pdf
2025-09-18 23:37:05,514 - INFO - ⬆️ [23:37:05] Uploading: 25_ingate_1.pdf
2025-09-18 23:37:05,516 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:05,533 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:05,537 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/286e3749_22_so_confirmation_3.pdf
2025-09-18 23:37:05,537 - INFO - Processing PDF from S3...
2025-09-18 23:37:05,537 - INFO - Downloading PDF from S3 to /tmp/tmpzt00scpn/286e3749_22_so_confirmation_3.pdf
2025-09-18 23:37:05,544 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/537d7eb5_19_coa_3.pdf
2025-09-18 23:37:05,755 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:05,758 - INFO - Splitting PDF f5c4300a_22_so_confirmation_1 into 1 pages
2025-09-18 23:37:05,772 - INFO - Split PDF into 1 pages
2025-09-18 23:37:05,772 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:05,772 - INFO - Expected pages: [1]
2025-09-18 23:37:06,115 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/650428d9_16_customs_doc_2.tiff
2025-09-18 23:37:06,225 - INFO - ✓ Uploaded: input_data_3_per_cat/25_ingate/25_ingate_1.pdf -> s3://document-extraction-logistically/temp/59e55a41_25_ingate_1.pdf
2025-09-18 23:37:06,225 - INFO - 🔍 [23:37:06] Starting classification: 25_ingate_1.pdf
2025-09-18 23:37:06,226 - INFO - ⬆️ [23:37:06] Uploading: 25_ingate_10.png
2025-09-18 23:37:06,226 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:06,253 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/52dcd349_19_coa_1.pdf
2025-09-18 23:37:06,253 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:06,266 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/59e55a41_25_ingate_1.pdf
2025-09-18 23:37:06,267 - INFO - Processing PDF from S3...
2025-09-18 23:37:06,268 - INFO - Downloading PDF from S3 to /tmp/tmp2tcex1m3/59e55a41_25_ingate_1.pdf
2025-09-18 23:37:06,429 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/24d1bf92_19_coa_2.pdf
2025-09-18 23:37:06,458 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/485a455d_16_customs_doc_3.pdf
2025-09-18 23:37:06,588 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/669b6a63_21_lumper_receipt_1.jpeg
2025-09-18 23:37:06,680 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:06,682 - INFO - Splitting PDF ea7e920f_22_so_confirmation_2 into 4 pages
2025-09-18 23:37:06,691 - INFO - Split PDF into 4 pages
2025-09-18 23:37:06,691 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:06,691 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-18 23:37:06,971 - INFO - ✓ Uploaded: input_data_3_per_cat/25_ingate/25_ingate_10.png -> s3://document-extraction-logistically/temp/f234ef81_25_ingate_10.png
2025-09-18 23:37:06,971 - INFO - 🔍 [23:37:06] Starting classification: 25_ingate_10.png
2025-09-18 23:37:06,972 - INFO - ⬆️ [23:37:06] Uploading: 25_ingate_2.pdf
2025-09-18 23:37:06,973 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:06,985 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:07,004 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f234ef81_25_ingate_10.png
2025-09-18 23:37:07,005 - INFO - Processing image from S3...
2025-09-18 23:37:07,017 - INFO - Page 2: Extracted 229 characters, 10 lines from bdedd3ed_20_tender_from_cust_3_eae3e848_page_002.pdf
2025-09-18 23:37:07,018 - INFO - Successfully processed page 2
2025-09-18 23:37:07,026 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c300a5a5_20_tender_from_cust_1.pdf
2025-09-18 23:37:07,196 - INFO - S3 Image temp/c87b52ed_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 23:37:07,196 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:07,196 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:07,350 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:07,351 - INFO - Splitting PDF 286e3749_22_so_confirmation_3 into 2 pages
2025-09-18 23:37:07,356 - INFO - Split PDF into 2 pages
2025-09-18 23:37:07,356 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:07,356 - INFO - Expected pages: [1, 2]
2025-09-18 23:37:07,648 - INFO - ✓ Uploaded: input_data_3_per_cat/25_ingate/25_ingate_2.pdf -> s3://document-extraction-logistically/temp/77e6ca6a_25_ingate_2.pdf
2025-09-18 23:37:07,648 - INFO - 🔍 [23:37:07] Starting classification: 25_ingate_2.pdf
2025-09-18 23:37:07,649 - INFO - ⬆️ [23:37:07] Uploading: 26_outgate_1.pdf
2025-09-18 23:37:07,651 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:07,661 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:07,663 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/77e6ca6a_25_ingate_2.pdf
2025-09-18 23:37:07,663 - INFO - Processing PDF from S3...
2025-09-18 23:37:07,664 - INFO - Downloading PDF from S3 to /tmp/tmp4by9ef1e/77e6ca6a_25_ingate_2.pdf
2025-09-18 23:37:07,827 - INFO - Page 1: Extracted 1262 characters, 73 lines from 6cb81e2a_20_tender_from_cust_2_8f71b55f_page_001.pdf
2025-09-18 23:37:07,827 - INFO - Successfully processed page 1
2025-09-18 23:37:07,828 - INFO - Combined 2 pages into final text
2025-09-18 23:37:07,828 - INFO - Text validation for 6cb81e2a_20_tender_from_cust_2: 1327 characters, 2 pages
2025-09-18 23:37:07,828 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:07,828 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:08,001 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:08,002 - INFO - Splitting PDF 59e55a41_25_ingate_1 into 2 pages
2025-09-18 23:37:08,009 - INFO - Split PDF into 2 pages
2025-09-18 23:37:08,009 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:08,009 - INFO - Expected pages: [1, 2]
2025-09-18 23:37:08,371 - INFO - Page 1: Extracted 2681 characters, 83 lines from 0fd8e9c1_1_bol_1_f08db1c5_page_001.pdf
2025-09-18 23:37:08,372 - INFO - Successfully processed page 1
2025-09-18 23:37:08,372 - INFO - Combined 1 pages into final text
2025-09-18 23:37:08,372 - INFO - Text validation for 0fd8e9c1_1_bol_1: 2698 characters, 1 pages
2025-09-18 23:37:08,373 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:08,373 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:08,651 - INFO - Page 1: Extracted 2091 characters, 153 lines from bdedd3ed_20_tender_from_cust_3_eae3e848_page_001.pdf
2025-09-18 23:37:08,651 - INFO - Successfully processed page 1
2025-09-18 23:37:08,723 - INFO - ✓ Uploaded: input_data_3_per_cat/26_outgate/26_outgate_1.pdf -> s3://document-extraction-logistically/temp/4b191512_26_outgate_1.pdf
2025-09-18 23:37:08,723 - INFO - 🔍 [23:37:08] Starting classification: 26_outgate_1.pdf
2025-09-18 23:37:08,723 - INFO - ⬆️ [23:37:08] Uploading: 26_outgate_10.pdf
2025-09-18 23:37:08,724 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:08,735 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:08,737 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4b191512_26_outgate_1.pdf
2025-09-18 23:37:08,737 - INFO - Processing PDF from S3...
2025-09-18 23:37:08,737 - INFO - Downloading PDF from S3 to /tmp/tmpgh4k3kg5/4b191512_26_outgate_1.pdf
2025-09-18 23:37:09,024 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4f3952e5_21_lumper_receipt_10.png
2025-09-18 23:37:09,121 - INFO - Page 1: Extracted 2034 characters, 111 lines from 4bdb7d4e_1_bol_10_a2f6df1a_page_001.pdf
2025-09-18 23:37:09,121 - INFO - Successfully processed page 1
2025-09-18 23:37:09,122 - INFO - Combined 1 pages into final text
2025-09-18 23:37:09,122 - INFO - Text validation for 4bdb7d4e_1_bol_10: 2051 characters, 1 pages
2025-09-18 23:37:09,122 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:09,122 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:09,246 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:09,248 - INFO - Splitting PDF e762725e_1_bol_11 into 1 pages
2025-09-18 23:37:09,251 - INFO - Split PDF into 1 pages
2025-09-18 23:37:09,251 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:09,251 - INFO - Expected pages: [1]
2025-09-18 23:37:09,325 - INFO - Page 3: Extracted 6281 characters, 47 lines from bdedd3ed_20_tender_from_cust_3_eae3e848_page_003.pdf
2025-09-18 23:37:09,325 - INFO - Successfully processed page 3
2025-09-18 23:37:09,325 - INFO - Combined 3 pages into final text
2025-09-18 23:37:09,326 - INFO - Text validation for bdedd3ed_20_tender_from_cust_3: 8656 characters, 3 pages
2025-09-18 23:37:09,326 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:09,326 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:09,338 - INFO - ✓ Uploaded: input_data_3_per_cat/26_outgate/26_outgate_10.pdf -> s3://document-extraction-logistically/temp/15ad4a3e_26_outgate_10.pdf
2025-09-18 23:37:09,338 - INFO - 🔍 [23:37:09] Starting classification: 26_outgate_10.pdf
2025-09-18 23:37:09,339 - INFO - ⬆️ [23:37:09] Uploading: 26_outgate_11.pdf
2025-09-18 23:37:09,340 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:09,367 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:09,372 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15ad4a3e_26_outgate_10.pdf
2025-09-18 23:37:09,372 - INFO - Processing PDF from S3...
2025-09-18 23:37:09,372 - INFO - Downloading PDF from S3 to /tmp/tmpb2j6n_i7/15ad4a3e_26_outgate_10.pdf
2025-09-18 23:37:09,383 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:09,385 - INFO - Splitting PDF 77e6ca6a_25_ingate_2 into 1 pages
2025-09-18 23:37:09,404 - INFO - Split PDF into 1 pages
2025-09-18 23:37:09,405 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:09,405 - INFO - Expected pages: [1]
2025-09-18 23:37:09,628 - INFO - S3 Image temp/f234ef81_25_ingate_10.png: Extracted 558 characters, 43 lines
2025-09-18 23:37:09,628 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:09,628 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:09,859 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6cb81e2a_20_tender_from_cust_2.pdf
2025-09-18 23:37:09,981 - INFO - ✓ Uploaded: input_data_3_per_cat/26_outgate/26_outgate_11.pdf -> s3://document-extraction-logistically/temp/71e9a8bc_26_outgate_11.pdf
2025-09-18 23:37:09,981 - INFO - 🔍 [23:37:09] Starting classification: 26_outgate_11.pdf
2025-09-18 23:37:09,981 - INFO - ⬆️ [23:37:09] Uploading: 2_pod_1.pdf
2025-09-18 23:37:09,983 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:10,001 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:10,005 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/71e9a8bc_26_outgate_11.pdf
2025-09-18 23:37:10,005 - INFO - Processing PDF from S3...
2025-09-18 23:37:10,005 - INFO - Downloading PDF from S3 to /tmp/tmp054774re/71e9a8bc_26_outgate_11.pdf
2025-09-18 23:37:10,174 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0fd8e9c1_1_bol_1.pdf
2025-09-18 23:37:10,178 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c87b52ed_21_lumper_receipt_11.jpeg
2025-09-18 23:37:10,870 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4bdb7d4e_1_bol_10.pdf
2025-09-18 23:37:11,093 - INFO - Page 1: Extracted 950 characters, 59 lines from f5c4300a_22_so_confirmation_1_277a68a0_page_001.pdf
2025-09-18 23:37:11,093 - INFO - Successfully processed page 1
2025-09-18 23:37:11,094 - INFO - Combined 1 pages into final text
2025-09-18 23:37:11,094 - INFO - Text validation for f5c4300a_22_so_confirmation_1: 967 characters, 1 pages
2025-09-18 23:37:11,094 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:11,094 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:11,289 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/fa1c4490_2_pod_1.pdf
2025-09-18 23:37:11,289 - INFO - 🔍 [23:37:11] Starting classification: 2_pod_1.pdf
2025-09-18 23:37:11,291 - INFO - ⬆️ [23:37:11] Uploading: 2_pod_10.pdf
2025-09-18 23:37:11,293 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:11,310 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:11,316 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fa1c4490_2_pod_1.pdf
2025-09-18 23:37:11,316 - INFO - Processing PDF from S3...
2025-09-18 23:37:11,317 - INFO - Downloading PDF from S3 to /tmp/tmp4vn02umm/fa1c4490_2_pod_1.pdf
2025-09-18 23:37:11,456 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:11,457 - INFO - Splitting PDF 4b191512_26_outgate_1 into 2 pages
2025-09-18 23:37:11,543 - INFO - Split PDF into 2 pages
2025-09-18 23:37:11,543 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:11,543 - INFO - Expected pages: [1, 2]
2025-09-18 23:37:11,547 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bdedd3ed_20_tender_from_cust_3.pdf
2025-09-18 23:37:11,775 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f234ef81_25_ingate_10.png
2025-09-18 23:37:11,890 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_10.pdf -> s3://document-extraction-logistically/temp/2421f7fd_2_pod_10.pdf
2025-09-18 23:37:11,890 - INFO - 🔍 [23:37:11] Starting classification: 2_pod_10.pdf
2025-09-18 23:37:11,891 - INFO - ⬆️ [23:37:11] Uploading: 2_pod_11.pdf
2025-09-18 23:37:11,891 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:11,898 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:11,901 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2421f7fd_2_pod_10.pdf
2025-09-18 23:37:11,901 - INFO - Processing PDF from S3...
2025-09-18 23:37:11,901 - INFO - Downloading PDF from S3 to /tmp/tmpzy_o0bbe/2421f7fd_2_pod_10.pdf
2025-09-18 23:37:11,929 - INFO - Page 2: Extracted 538 characters, 7 lines from 59e55a41_25_ingate_1_415463c0_page_002.pdf
2025-09-18 23:37:11,929 - INFO - Successfully processed page 2
2025-09-18 23:37:12,012 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:12,015 - INFO - Splitting PDF 15ad4a3e_26_outgate_10 into 1 pages
2025-09-18 23:37:12,016 - INFO - Split PDF into 1 pages
2025-09-18 23:37:12,016 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:12,016 - INFO - Expected pages: [1]
2025-09-18 23:37:12,078 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:12,079 - INFO - Splitting PDF 71e9a8bc_26_outgate_11 into 1 pages
2025-09-18 23:37:12,079 - INFO - Split PDF into 1 pages
2025-09-18 23:37:12,079 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:12,080 - INFO - Expected pages: [1]
2025-09-18 23:37:12,349 - INFO - Page 1: Extracted 2650 characters, 102 lines from ea7e920f_22_so_confirmation_2_3745a424_page_001.pdf
2025-09-18 23:37:12,350 - INFO - Successfully processed page 1
2025-09-18 23:37:12,380 - INFO - Page 1: Extracted 605 characters, 27 lines from 59e55a41_25_ingate_1_415463c0_page_001.pdf
2025-09-18 23:37:12,380 - INFO - Successfully processed page 1
2025-09-18 23:37:12,381 - INFO - Combined 2 pages into final text
2025-09-18 23:37:12,381 - INFO - Text validation for 59e55a41_25_ingate_1: 1179 characters, 2 pages
2025-09-18 23:37:12,381 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:12,381 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:12,502 - INFO - Page 1: Extracted 1006 characters, 66 lines from 286e3749_22_so_confirmation_3_7181e635_page_001.pdf
2025-09-18 23:37:12,503 - INFO - Successfully processed page 1
2025-09-18 23:37:12,543 - INFO - Page 4: Extracted 5052 characters, 47 lines from ea7e920f_22_so_confirmation_2_3745a424_page_004.pdf
2025-09-18 23:37:12,543 - INFO - Successfully processed page 4
2025-09-18 23:37:12,591 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_11.pdf -> s3://document-extraction-logistically/temp/e14bb5f1_2_pod_11.pdf
2025-09-18 23:37:12,592 - INFO - 🔍 [23:37:12] Starting classification: 2_pod_11.pdf
2025-09-18 23:37:12,592 - INFO - ⬆️ [23:37:12] Uploading: 3_rate_confirmation_1.pdf
2025-09-18 23:37:12,593 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:12,619 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:12,622 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e14bb5f1_2_pod_11.pdf
2025-09-18 23:37:12,622 - INFO - Processing PDF from S3...
2025-09-18 23:37:12,623 - INFO - Downloading PDF from S3 to /tmp/tmpx2mn7ip2/e14bb5f1_2_pod_11.pdf
2025-09-18 23:37:13,055 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f5c4300a_22_so_confirmation_1.pdf
2025-09-18 23:37:13,093 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:13,095 - INFO - Splitting PDF 2421f7fd_2_pod_10 into 1 pages
2025-09-18 23:37:13,096 - INFO - Split PDF into 1 pages
2025-09-18 23:37:13,097 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:13,097 - INFO - Expected pages: [1]
2025-09-18 23:37:13,432 - INFO - Page 2: Extracted 6360 characters, 63 lines from ea7e920f_22_so_confirmation_2_3745a424_page_002.pdf
2025-09-18 23:37:13,432 - INFO - Successfully processed page 2
2025-09-18 23:37:13,470 - INFO - Page 2: Extracted 2902 characters, 61 lines from 286e3749_22_so_confirmation_3_7181e635_page_002.pdf
2025-09-18 23:37:13,470 - INFO - Successfully processed page 2
2025-09-18 23:37:13,471 - INFO - Combined 2 pages into final text
2025-09-18 23:37:13,471 - INFO - Text validation for 286e3749_22_so_confirmation_3: 3944 characters, 2 pages
2025-09-18 23:37:13,471 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:13,471 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:13,546 - INFO - ✓ Uploaded: input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_1.pdf -> s3://document-extraction-logistically/temp/89c072ba_3_rate_confirmation_1.pdf
2025-09-18 23:37:13,546 - INFO - 🔍 [23:37:13] Starting classification: 3_rate_confirmation_1.pdf
2025-09-18 23:37:13,547 - INFO - ⬆️ [23:37:13] Uploading: 3_rate_confirmation_2.pdf
2025-09-18 23:37:13,548 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:13,575 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:13,580 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/89c072ba_3_rate_confirmation_1.pdf
2025-09-18 23:37:13,580 - INFO - Processing PDF from S3...
2025-09-18 23:37:13,581 - INFO - Downloading PDF from S3 to /tmp/tmpvuclhgv_/89c072ba_3_rate_confirmation_1.pdf
2025-09-18 23:37:13,729 - INFO - Page 3: Extracted 6732 characters, 63 lines from ea7e920f_22_so_confirmation_2_3745a424_page_003.pdf
2025-09-18 23:37:13,730 - INFO - Successfully processed page 3
2025-09-18 23:37:13,730 - INFO - Combined 4 pages into final text
2025-09-18 23:37:13,730 - INFO - Text validation for ea7e920f_22_so_confirmation_2: 20868 characters, 4 pages
2025-09-18 23:37:13,731 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:13,731 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:13,742 - INFO - Page 1: Extracted 852 characters, 22 lines from 77e6ca6a_25_ingate_2_6076eb1d_page_001.pdf
2025-09-18 23:37:13,743 - INFO - Successfully processed page 1
2025-09-18 23:37:13,743 - INFO - Combined 1 pages into final text
2025-09-18 23:37:13,743 - INFO - Text validation for 77e6ca6a_25_ingate_2: 869 characters, 1 pages
2025-09-18 23:37:13,743 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:13,743 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:14,246 - INFO - ✓ Uploaded: input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_2.pdf -> s3://document-extraction-logistically/temp/50e43235_3_rate_confirmation_2.pdf
2025-09-18 23:37:14,246 - INFO - 🔍 [23:37:14] Starting classification: 3_rate_confirmation_2.pdf
2025-09-18 23:37:14,248 - INFO - ⬆️ [23:37:14] Uploading: 3_rate_confirmation_3.pdf
2025-09-18 23:37:14,249 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:14,271 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:14,275 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/50e43235_3_rate_confirmation_2.pdf
2025-09-18 23:37:14,275 - INFO - Processing PDF from S3...
2025-09-18 23:37:14,275 - INFO - Downloading PDF from S3 to /tmp/tmpetrxjale/50e43235_3_rate_confirmation_2.pdf
2025-09-18 23:37:14,772 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/59e55a41_25_ingate_1.pdf
2025-09-18 23:37:14,917 - INFO - ✓ Uploaded: input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf -> s3://document-extraction-logistically/temp/47e93596_3_rate_confirmation_3.pdf
2025-09-18 23:37:14,917 - INFO - 🔍 [23:37:14] Starting classification: 3_rate_confirmation_3.pdf
2025-09-18 23:37:14,918 - INFO - ⬆️ [23:37:14] Uploading: 5_clear_to_pay_1.pdf
2025-09-18 23:37:14,921 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:14,940 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:14,944 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/47e93596_3_rate_confirmation_3.pdf
2025-09-18 23:37:14,945 - INFO - Processing PDF from S3...
2025-09-18 23:37:14,945 - INFO - Downloading PDF from S3 to /tmp/tmp0hhfhhcx/47e93596_3_rate_confirmation_3.pdf
2025-09-18 23:37:15,607 - INFO - ✓ Uploaded: input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_1.pdf -> s3://document-extraction-logistically/temp/15964f60_5_clear_to_pay_1.pdf
2025-09-18 23:37:15,607 - INFO - 🔍 [23:37:15] Starting classification: 5_clear_to_pay_1.pdf
2025-09-18 23:37:15,608 - INFO - ⬆️ [23:37:15] Uploading: 5_clear_to_pay_2.pdf
2025-09-18 23:37:15,609 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:15,625 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:15,630 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15964f60_5_clear_to_pay_1.pdf
2025-09-18 23:37:15,630 - INFO - Processing PDF from S3...
2025-09-18 23:37:15,630 - INFO - Downloading PDF from S3 to /tmp/tmplm8h9wis/15964f60_5_clear_to_pay_1.pdf
2025-09-18 23:37:15,751 - INFO - Page 1: Extracted 1081 characters, 102 lines from e762725e_1_bol_11_931d34dd_page_001.pdf
2025-09-18 23:37:15,751 - INFO - Successfully processed page 1
2025-09-18 23:37:15,752 - INFO - Combined 1 pages into final text
2025-09-18 23:37:15,752 - INFO - Text validation for e762725e_1_bol_11: 1098 characters, 1 pages
2025-09-18 23:37:15,752 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:15,752 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:15,934 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/77e6ca6a_25_ingate_2.pdf
2025-09-18 23:37:16,255 - INFO - ✓ Uploaded: input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_2.pdf -> s3://document-extraction-logistically/temp/6feac388_5_clear_to_pay_2.pdf
2025-09-18 23:37:16,255 - INFO - 🔍 [23:37:16] Starting classification: 5_clear_to_pay_2.pdf
2025-09-18 23:37:16,255 - INFO - ⬆️ [23:37:16] Uploading: 6_scale_ticket_1.pdf
2025-09-18 23:37:16,256 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:16,263 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:16,265 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6feac388_5_clear_to_pay_2.pdf
2025-09-18 23:37:16,265 - INFO - Processing PDF from S3...
2025-09-18 23:37:16,265 - INFO - Downloading PDF from S3 to /tmp/tmp5lcjtyah/6feac388_5_clear_to_pay_2.pdf
2025-09-18 23:37:16,338 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:16,338 - INFO - Splitting PDF e14bb5f1_2_pod_11 into 1 pages
2025-09-18 23:37:16,339 - INFO - Split PDF into 1 pages
2025-09-18 23:37:16,339 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:16,339 - INFO - Expected pages: [1]
2025-09-18 23:37:16,630 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:16,630 - INFO - Splitting PDF 50e43235_3_rate_confirmation_2 into 1 pages
2025-09-18 23:37:16,631 - INFO - Split PDF into 1 pages
2025-09-18 23:37:16,631 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:16,631 - INFO - Expected pages: [1]
2025-09-18 23:37:16,795 - INFO - Page 1: Extracted 851 characters, 49 lines from 15ad4a3e_26_outgate_10_982c6d9a_page_001.pdf
2025-09-18 23:37:16,795 - INFO - Successfully processed page 1
2025-09-18 23:37:16,795 - INFO - Combined 1 pages into final text
2025-09-18 23:37:16,796 - INFO - Text validation for 15ad4a3e_26_outgate_10: 868 characters, 1 pages
2025-09-18 23:37:16,796 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:16,796 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:16,846 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/286e3749_22_so_confirmation_3.pdf
2025-09-18 23:37:16,917 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/354c3ea7_6_scale_ticket_1.pdf
2025-09-18 23:37:16,917 - INFO - 🔍 [23:37:16] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 23:37:16,917 - INFO - ⬆️ [23:37:16] Uploading: 6_scale_ticket_10.png
2025-09-18 23:37:16,918 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:16,926 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:16,928 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/354c3ea7_6_scale_ticket_1.pdf
2025-09-18 23:37:16,928 - INFO - Processing PDF from S3...
2025-09-18 23:37:16,928 - INFO - Downloading PDF from S3 to /tmp/tmpd0d25gfm/354c3ea7_6_scale_ticket_1.pdf
2025-09-18 23:37:16,962 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:16,964 - INFO - Splitting PDF 47e93596_3_rate_confirmation_3 into 2 pages
2025-09-18 23:37:16,974 - INFO - Split PDF into 2 pages
2025-09-18 23:37:16,974 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:16,974 - INFO - Expected pages: [1, 2]
2025-09-18 23:37:17,044 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ea7e920f_22_so_confirmation_2.pdf
2025-09-18 23:37:17,369 - INFO - Page 1: Extracted 838 characters, 47 lines from 71e9a8bc_26_outgate_11_11ae0152_page_001.pdf
2025-09-18 23:37:17,369 - INFO - Successfully processed page 1
2025-09-18 23:37:17,369 - INFO - Combined 1 pages into final text
2025-09-18 23:37:17,370 - INFO - Text validation for 71e9a8bc_26_outgate_11: 855 characters, 1 pages
2025-09-18 23:37:17,370 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:17,370 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:17,512 - INFO - Page 2: Extracted 269 characters, 15 lines from 4b191512_26_outgate_1_c5cac472_page_002.pdf
2025-09-18 23:37:17,512 - INFO - Successfully processed page 2
2025-09-18 23:37:17,736 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e762725e_1_bol_11.pdf
2025-09-18 23:37:17,905 - INFO - Page 1: Extracted 558 characters, 30 lines from 4b191512_26_outgate_1_c5cac472_page_001.pdf
2025-09-18 23:37:17,905 - INFO - Successfully processed page 1
2025-09-18 23:37:17,906 - INFO - Combined 2 pages into final text
2025-09-18 23:37:17,906 - INFO - Text validation for 4b191512_26_outgate_1: 863 characters, 2 pages
2025-09-18 23:37:17,907 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:17,907 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:17,981 - INFO - Page 1: Extracted 961 characters, 60 lines from 2421f7fd_2_pod_10_f443dcef_page_001.pdf
2025-09-18 23:37:17,981 - INFO - Successfully processed page 1
2025-09-18 23:37:17,981 - INFO - Combined 1 pages into final text
2025-09-18 23:37:17,982 - INFO - Text validation for 2421f7fd_2_pod_10: 978 characters, 1 pages
2025-09-18 23:37:17,982 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:17,982 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:18,048 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:18,049 - INFO - Splitting PDF 6feac388_5_clear_to_pay_2 into 1 pages
2025-09-18 23:37:18,051 - INFO - Split PDF into 1 pages
2025-09-18 23:37:18,051 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:18,051 - INFO - Expected pages: [1]
2025-09-18 23:37:18,061 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:18,062 - INFO - Splitting PDF 15964f60_5_clear_to_pay_1 into 1 pages
2025-09-18 23:37:18,067 - INFO - Split PDF into 1 pages
2025-09-18 23:37:18,067 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:18,067 - INFO - Expected pages: [1]
2025-09-18 23:37:18,233 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/8ac2463c_6_scale_ticket_10.png
2025-09-18 23:37:18,233 - INFO - 🔍 [23:37:18] Starting classification: 6_scale_ticket_10.png
2025-09-18 23:37:18,233 - INFO - ⬆️ [23:37:18] Uploading: 6_scale_ticket_11.jpg
2025-09-18 23:37:18,234 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:18,243 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:18,246 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8ac2463c_6_scale_ticket_10.png
2025-09-18 23:37:18,246 - INFO - Processing image from S3...
2025-09-18 23:37:18,698 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15ad4a3e_26_outgate_10.pdf
2025-09-18 23:37:19,237 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:19,241 - INFO - Splitting PDF 354c3ea7_6_scale_ticket_1 into 1 pages
2025-09-18 23:37:19,244 - INFO - Split PDF into 1 pages
2025-09-18 23:37:19,244 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:19,245 - INFO - Expected pages: [1]
2025-09-18 23:37:19,322 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/71e9a8bc_26_outgate_11.pdf
2025-09-18 23:37:20,308 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2421f7fd_2_pod_10.pdf
2025-09-18 23:37:20,338 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg -> s3://document-extraction-logistically/temp/16f952dc_6_scale_ticket_11.jpg
2025-09-18 23:37:20,338 - INFO - 🔍 [23:37:20] Starting classification: 6_scale_ticket_11.jpg
2025-09-18 23:37:20,339 - INFO - ⬆️ [23:37:20] Uploading: 8_log_1.pdf
2025-09-18 23:37:20,341 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:20,363 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:20,367 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/16f952dc_6_scale_ticket_11.jpg
2025-09-18 23:37:20,367 - INFO - Processing image from S3...
2025-09-18 23:37:20,821 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4b191512_26_outgate_1.pdf
2025-09-18 23:37:20,994 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:20,995 - INFO - Splitting PDF fa1c4490_2_pod_1 into 3 pages
2025-09-18 23:37:20,997 - INFO - Split PDF into 3 pages
2025-09-18 23:37:20,997 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:20,997 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:37:21,008 - INFO - ✓ Uploaded: input_data_3_per_cat/8_log/8_log_1.pdf -> s3://document-extraction-logistically/temp/3318b62f_8_log_1.pdf
2025-09-18 23:37:21,009 - INFO - 🔍 [23:37:21] Starting classification: 8_log_1.pdf
2025-09-18 23:37:21,009 - INFO - ⬆️ [23:37:21] Uploading: 8_log_10.jpeg
2025-09-18 23:37:21,010 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:21,023 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:21,025 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3318b62f_8_log_1.pdf
2025-09-18 23:37:21,026 - INFO - Processing PDF from S3...
2025-09-18 23:37:21,026 - INFO - Downloading PDF from S3 to /tmp/tmp3ftt6bmu/3318b62f_8_log_1.pdf
2025-09-18 23:37:21,038 - INFO - Page 2: Extracted 510 characters, 15 lines from 47e93596_3_rate_confirmation_3_4c7e39b9_page_002.pdf
2025-09-18 23:37:21,038 - INFO - Successfully processed page 2
2025-09-18 23:37:21,579 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:21,579 - INFO - Splitting PDF 89c072ba_3_rate_confirmation_1 into 1 pages
2025-09-18 23:37:21,581 - INFO - Split PDF into 1 pages
2025-09-18 23:37:21,581 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:21,581 - INFO - Expected pages: [1]
2025-09-18 23:37:21,852 - INFO - Page 1: Extracted 797 characters, 46 lines from e14bb5f1_2_pod_11_f61599a4_page_001.pdf
2025-09-18 23:37:21,852 - INFO - Successfully processed page 1
2025-09-18 23:37:21,852 - INFO - Combined 1 pages into final text
2025-09-18 23:37:21,853 - INFO - Text validation for e14bb5f1_2_pod_11: 814 characters, 1 pages
2025-09-18 23:37:21,853 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:21,853 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:22,793 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:22,794 - INFO - Splitting PDF 3318b62f_8_log_1 into 3 pages
2025-09-18 23:37:22,796 - INFO - Split PDF into 3 pages
2025-09-18 23:37:22,796 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:22,796 - INFO - Expected pages: [1, 2, 3]
2025-09-18 23:37:22,875 - INFO - Page 1: Extracted 721 characters, 35 lines from 6feac388_5_clear_to_pay_2_608a4452_page_001.pdf
2025-09-18 23:37:22,875 - INFO - Successfully processed page 1
2025-09-18 23:37:22,876 - INFO - Combined 1 pages into final text
2025-09-18 23:37:22,876 - INFO - Text validation for 6feac388_5_clear_to_pay_2: 738 characters, 1 pages
2025-09-18 23:37:22,876 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:22,876 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:23,097 - INFO - S3 Image temp/8ac2463c_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 23:37:23,098 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:23,098 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:23,162 - INFO - ✓ Uploaded: input_data_3_per_cat/8_log/8_log_10.jpeg -> s3://document-extraction-logistically/temp/c82fe36a_8_log_10.jpeg
2025-09-18 23:37:23,162 - INFO - 🔍 [23:37:23] Starting classification: 8_log_10.jpeg
2025-09-18 23:37:23,162 - INFO - ⬆️ [23:37:23] Uploading: 8_log_11.jpg
2025-09-18 23:37:23,163 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:23,170 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:23,172 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c82fe36a_8_log_10.jpeg
2025-09-18 23:37:23,172 - INFO - Processing image from S3...
2025-09-18 23:37:23,224 - INFO - Page 1: Extracted 1259 characters, 67 lines from 50e43235_3_rate_confirmation_2_2df60386_page_001.pdf
2025-09-18 23:37:23,224 - INFO - Successfully processed page 1
2025-09-18 23:37:23,225 - INFO - Combined 1 pages into final text
2025-09-18 23:37:23,225 - INFO - Text validation for 50e43235_3_rate_confirmation_2: 1276 characters, 1 pages
2025-09-18 23:37:23,225 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:23,225 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:23,312 - INFO - Page 1: Extracted 1245 characters, 64 lines from 47e93596_3_rate_confirmation_3_4c7e39b9_page_001.pdf
2025-09-18 23:37:23,312 - INFO - Successfully processed page 1
2025-09-18 23:37:23,312 - INFO - Combined 2 pages into final text
2025-09-18 23:37:23,312 - INFO - Text validation for 47e93596_3_rate_confirmation_3: 1791 characters, 2 pages
2025-09-18 23:37:23,313 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:23,313 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:23,333 - INFO - Page 1: Extracted 749 characters, 42 lines from 15964f60_5_clear_to_pay_1_4292bce3_page_001.pdf
2025-09-18 23:37:23,333 - INFO - Successfully processed page 1
2025-09-18 23:37:23,333 - INFO - Combined 1 pages into final text
2025-09-18 23:37:23,333 - INFO - Text validation for 15964f60_5_clear_to_pay_1: 766 characters, 1 pages
2025-09-18 23:37:23,333 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:23,333 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:23,617 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e14bb5f1_2_pod_11.pdf
2025-09-18 23:37:24,052 - INFO - S3 Image temp/16f952dc_6_scale_ticket_11.jpg: Extracted 361 characters, 32 lines
2025-09-18 23:37:24,052 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:24,052 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:24,760 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6feac388_5_clear_to_pay_2.pdf
2025-09-18 23:37:24,798 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8ac2463c_6_scale_ticket_10.png
2025-09-18 23:37:25,193 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/50e43235_3_rate_confirmation_2.pdf
2025-09-18 23:37:25,202 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15964f60_5_clear_to_pay_1.pdf
2025-09-18 23:37:25,449 - INFO - Page 1: Extracted 1382 characters, 62 lines from 354c3ea7_6_scale_ticket_1_5f984b2d_page_001.pdf
2025-09-18 23:37:25,450 - INFO - Successfully processed page 1
2025-09-18 23:37:25,450 - INFO - Combined 1 pages into final text
2025-09-18 23:37:25,450 - INFO - Text validation for 354c3ea7_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 23:37:25,450 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:25,450 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:25,555 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/47e93596_3_rate_confirmation_3.pdf
2025-09-18 23:37:26,177 - INFO - ✓ Uploaded: input_data_3_per_cat/8_log/8_log_11.jpg -> s3://document-extraction-logistically/temp/ad702e80_8_log_11.jpg
2025-09-18 23:37:26,178 - INFO - 🔍 [23:37:26] Starting classification: 8_log_11.jpg
2025-09-18 23:37:26,178 - INFO - ⬆️ [23:37:26] Uploading: 9_fuel_receipt_1.png
2025-09-18 23:37:26,178 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:26,280 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:26,283 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ad702e80_8_log_11.jpg
2025-09-18 23:37:26,283 - INFO - Processing image from S3...
2025-09-18 23:37:26,456 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/16f952dc_6_scale_ticket_11.jpg
2025-09-18 23:37:26,624 - INFO - Page 2: Extracted 492 characters, 33 lines from 3318b62f_8_log_1_6a5148e6_page_002.pdf
2025-09-18 23:37:26,625 - INFO - Successfully processed page 2
2025-09-18 23:37:26,707 - INFO - Page 3: Extracted 804 characters, 46 lines from 3318b62f_8_log_1_6a5148e6_page_003.pdf
2025-09-18 23:37:26,707 - INFO - Successfully processed page 3
2025-09-18 23:37:26,896 - INFO - ✓ Uploaded: input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_1.png -> s3://document-extraction-logistically/temp/cacce28e_9_fuel_receipt_1.png
2025-09-18 23:37:26,897 - INFO - 🔍 [23:37:26] Starting classification: 9_fuel_receipt_1.png
2025-09-18 23:37:26,898 - INFO - ⬆️ [23:37:26] Uploading: 9_fuel_receipt_10.jpeg
2025-09-18 23:37:26,898 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:26,918 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:26,921 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cacce28e_9_fuel_receipt_1.png
2025-09-18 23:37:26,921 - INFO - Processing image from S3...
2025-09-18 23:37:27,215 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/354c3ea7_6_scale_ticket_1.pdf
2025-09-18 23:37:27,237 - INFO - Page 1: Extracted 535 characters, 49 lines from 3318b62f_8_log_1_6a5148e6_page_001.pdf
2025-09-18 23:37:27,247 - INFO - Page 1: Extracted 1211 characters, 66 lines from 89c072ba_3_rate_confirmation_1_e8da3c0d_page_001.pdf
2025-09-18 23:37:27,248 - INFO - Successfully processed page 1
2025-09-18 23:37:27,248 - INFO - Successfully processed page 1
2025-09-18 23:37:27,250 - INFO - Combined 3 pages into final text
2025-09-18 23:37:27,250 - INFO - Combined 1 pages into final text
2025-09-18 23:37:27,251 - INFO - Text validation for 3318b62f_8_log_1: 1886 characters, 3 pages
2025-09-18 23:37:27,251 - INFO - Text validation for 89c072ba_3_rate_confirmation_1: 1228 characters, 1 pages
2025-09-18 23:37:27,262 - INFO - Page 1: Extracted 1597 characters, 78 lines from fa1c4490_2_pod_1_52976871_page_001.pdf
2025-09-18 23:37:27,263 - INFO - Successfully processed page 1
2025-09-18 23:37:27,263 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:27,263 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:27,265 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:27,265 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:27,448 - INFO - S3 Image temp/c82fe36a_8_log_10.jpeg: Extracted 526 characters, 56 lines
2025-09-18 23:37:27,449 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:27,449 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:27,764 - INFO - Page 3: Extracted 441 characters, 20 lines from fa1c4490_2_pod_1_52976871_page_003.pdf
2025-09-18 23:37:27,764 - INFO - Successfully processed page 3
2025-09-18 23:37:27,885 - INFO - Page 2: Extracted 1895 characters, 65 lines from fa1c4490_2_pod_1_52976871_page_002.pdf
2025-09-18 23:37:27,886 - INFO - Successfully processed page 2
2025-09-18 23:37:27,886 - INFO - Combined 3 pages into final text
2025-09-18 23:37:27,886 - INFO - Text validation for fa1c4490_2_pod_1: 3988 characters, 3 pages
2025-09-18 23:37:27,887 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:27,887 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:29,459 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c82fe36a_8_log_10.jpeg
2025-09-18 23:37:29,581 - INFO - ✓ Uploaded: input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_10.jpeg -> s3://document-extraction-logistically/temp/50a4f600_9_fuel_receipt_10.jpeg
2025-09-18 23:37:29,582 - INFO - 🔍 [23:37:29] Starting classification: 9_fuel_receipt_10.jpeg
2025-09-18 23:37:29,582 - INFO - ⬆️ [23:37:29] Uploading: 9_fuel_receipt_11.pdf
2025-09-18 23:37:29,582 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:29,591 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:29,596 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/50a4f600_9_fuel_receipt_10.jpeg
2025-09-18 23:37:29,596 - INFO - Processing image from S3...
2025-09-18 23:37:29,792 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3318b62f_8_log_1.pdf
2025-09-18 23:37:29,912 - INFO - S3 Image temp/cacce28e_9_fuel_receipt_1.png: Extracted 517 characters, 46 lines
2025-09-18 23:37:29,912 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:29,912 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:30,174 - INFO - S3 Image temp/ad702e80_8_log_11.jpg: Extracted 1060 characters, 100 lines
2025-09-18 23:37:30,174 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:30,174 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:31,109 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/89c072ba_3_rate_confirmation_1.pdf
2025-09-18 23:37:31,399 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cacce28e_9_fuel_receipt_1.png
2025-09-18 23:37:32,858 - INFO - ✓ Uploaded: input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_11.pdf -> s3://document-extraction-logistically/temp/ebf3bbdd_9_fuel_receipt_11.pdf
2025-09-18 23:37:32,858 - INFO - 🔍 [23:37:32] Starting classification: 9_fuel_receipt_11.pdf
2025-09-18 23:37:32,860 - INFO - Initializing TextractProcessor...
2025-09-18 23:37:32,877 - INFO - Initializing BedrockProcessor...
2025-09-18 23:37:32,883 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ebf3bbdd_9_fuel_receipt_11.pdf
2025-09-18 23:37:32,886 - INFO - Processing PDF from S3...
2025-09-18 23:37:32,888 - INFO - Downloading PDF from S3 to /tmp/tmpc8kfuk4t/ebf3bbdd_9_fuel_receipt_11.pdf
2025-09-18 23:37:32,914 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:37:32,914 - INFO - 

✓ Saved result: output/run1_10_invoice_1.json
2025-09-18 23:37:33,002 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ad702e80_8_log_11.jpg
2025-09-18 23:37:33,030 - INFO - S3 Image temp/50a4f600_9_fuel_receipt_10.jpeg: Extracted 318 characters, 28 lines
2025-09-18 23:37:33,030 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:37:33,030 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:37:33,215 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bc695a49_10_invoice_1.pdf
2025-09-18 23:37:33,226 - INFO - 

10_invoice_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:37:33,226 - INFO - 

✓ Saved result: output/run1_10_invoice_10.json
2025-09-18 23:37:33,526 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83be436c_10_invoice_10.pdf
2025-09-18 23:37:33,547 - INFO - 

10_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:37:33,547 - INFO - 

✓ Saved result: output/run1_10_invoice_2.json
2025-09-18 23:37:33,849 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/52657c04_10_invoice_2.pdf
2025-09-18 23:37:33,878 - INFO - 

12_combined_carrier_documents_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 2,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 23:37:33,878 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_1.json
2025-09-18 23:37:34,176 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3423a0a8_12_combined_carrier_documents_1.pdf
2025-09-18 23:37:34,221 - INFO - 

12_combined_carrier_documents_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:37:34,221 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_2.json
2025-09-18 23:37:34,331 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-18 23:37:34,332 - ERROR - Processing failed for s3://document-extraction-logistically/temp/fa1c4490_2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 23:37:34,524 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/89484609_12_combined_carrier_documents_2.pdf
2025-09-18 23:37:34,559 - INFO - 

12_combined_carrier_documents_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 23:37:34,559 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_3.json
2025-09-18 23:37:34,616 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/50a4f600_9_fuel_receipt_10.jpeg
2025-09-18 23:37:34,861 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c5141f30_12_combined_carrier_documents_3.pdf
2025-09-18 23:37:34,876 - INFO - 

13_pack_list_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 23:37:34,876 - INFO - 

✓ Saved result: output/run1_13_pack_list_1.json
2025-09-18 23:37:35,167 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/af50118b_13_pack_list_1.pdf
2025-09-18 23:37:35,181 - INFO - 

15_comm_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-18 23:37:35,181 - INFO - 

✓ Saved result: output/run1_15_comm_invoice_1.json
2025-09-18 23:37:35,490 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/14386cf6_15_comm_invoice_1.pdf
2025-09-18 23:37:35,501 - INFO - 

14_po_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 23:37:35,501 - INFO - 

✓ Saved result: output/run1_14_po_1.json
2025-09-18 23:37:35,802 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/44ef1580_14_po_1.pdf
2025-09-18 23:37:35,839 - INFO - 

14_po_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-18 23:37:35,840 - INFO - 

✓ Saved result: output/run1_14_po_3.json
2025-09-18 23:37:36,142 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c822818e_14_po_3.pdf
2025-09-18 23:37:36,156 - INFO - 

15_comm_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-18 23:37:36,156 - INFO - 

✓ Saved result: output/run1_15_comm_invoice_2.json
2025-09-18 23:37:36,463 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/819f64ce_15_comm_invoice_2.pdf
2025-09-18 23:37:36,488 - INFO - 

17_nmfc_cert_1.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-18 23:37:36,488 - INFO - 

✓ Saved result: output/run1_17_nmfc_cert_1.json
2025-09-18 23:37:36,809 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6dada468_17_nmfc_cert_1.jpg
2025-09-18 23:37:36,829 - INFO - 

14_po_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 23:37:36,829 - INFO - 

✓ Saved result: output/run1_14_po_2.json
2025-09-18 23:37:37,128 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ff680802_14_po_2.pdf
2025-09-18 23:37:37,157 - INFO - 

13_pack_list_2.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 23:37:37,157 - INFO - 

✓ Saved result: output/run1_13_pack_list_2.json
2025-09-18 23:37:37,461 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/674a809a_13_pack_list_2.PDF
2025-09-18 23:37:37,539 - INFO - 

16_customs_doc_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 23:37:37,539 - INFO - 

✓ Saved result: output/run1_16_customs_doc_1.json
2025-09-18 23:37:37,850 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1bd841e4_16_customs_doc_1.pdf
2025-09-18 23:37:37,877 - INFO - 

17_nmfc_cert_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:37:37,877 - INFO - 

✓ Saved result: output/run1_17_nmfc_cert_2.json
2025-09-18 23:37:38,180 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/066db343_17_nmfc_cert_2.pdf
2025-09-18 23:37:38,205 - INFO - 

13_pack_list_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 23:37:38,205 - INFO - 

✓ Saved result: output/run1_13_pack_list_10.json
2025-09-18 23:37:38,378 - INFO - Splitting PDF into individual pages...
2025-09-18 23:37:38,380 - INFO - Splitting PDF ebf3bbdd_9_fuel_receipt_11 into 1 pages
2025-09-18 23:37:38,389 - INFO - Split PDF into 1 pages
2025-09-18 23:37:38,389 - INFO - Processing pages with Textract in parallel...
2025-09-18 23:37:38,389 - INFO - Expected pages: [1]
2025-09-18 23:37:38,513 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b2c0a712_13_pack_list_10.pdf
2025-09-18 23:37:38,530 - INFO - 

19_coa_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "coa"
        }
    ]
}
2025-09-18 23:37:38,530 - INFO - 

✓ Saved result: output/run1_19_coa_3.json
2025-09-18 23:37:38,827 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/537d7eb5_19_coa_3.pdf
2025-09-18 23:37:38,877 - INFO - 

16_customs_doc_2.tiff

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 23:37:38,877 - INFO - 

✓ Saved result: output/run1_16_customs_doc_2.json
2025-09-18 23:37:39,172 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/650428d9_16_customs_doc_2.tiff
2025-09-18 23:37:39,183 - INFO - 

19_coa_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:37:39,183 - INFO - 

✓ Saved result: output/run1_19_coa_1.json
2025-09-18 23:37:39,484 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/52dcd349_19_coa_1.pdf
2025-09-18 23:37:39,495 - INFO - 

19_coa_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:37:39,495 - INFO - 

✓ Saved result: output/run1_19_coa_2.json
2025-09-18 23:37:39,798 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/24d1bf92_19_coa_2.pdf
2025-09-18 23:37:39,847 - INFO - 

16_customs_doc_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 23:37:39,847 - INFO - 

✓ Saved result: output/run1_16_customs_doc_3.json
2025-09-18 23:37:40,151 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/485a455d_16_customs_doc_3.pdf
2025-09-18 23:37:40,166 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 23:37:40,167 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 23:37:40,466 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/669b6a63_21_lumper_receipt_1.jpeg
2025-09-18 23:37:40,482 - INFO - 

20_tender_from_cust_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 23:37:40,482 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_1.json
2025-09-18 23:37:40,783 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c300a5a5_20_tender_from_cust_1.pdf
2025-09-18 23:37:40,797 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 23:37:40,798 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 23:37:41,101 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4f3952e5_21_lumper_receipt_10.png
2025-09-18 23:37:41,116 - INFO - 

20_tender_from_cust_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 23:37:41,116 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_2.json
2025-09-18 23:37:41,420 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6cb81e2a_20_tender_from_cust_2.pdf
2025-09-18 23:37:41,444 - INFO - 

1_bol_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:37:41,444 - INFO - 

✓ Saved result: output/run1_1_bol_1.json
2025-09-18 23:37:41,747 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0fd8e9c1_1_bol_1.pdf
2025-09-18 23:37:41,762 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 23:37:41,762 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 23:37:42,065 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c87b52ed_21_lumper_receipt_11.jpeg
2025-09-18 23:37:42,103 - INFO - 

1_bol_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:37:42,103 - INFO - 

✓ Saved result: output/run1_1_bol_10.json
2025-09-18 23:37:42,409 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4bdb7d4e_1_bol_10.pdf
2025-09-18 23:37:42,473 - INFO - 

20_tender_from_cust_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 3,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 23:37:42,474 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_3.json
2025-09-18 23:37:42,778 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bdedd3ed_20_tender_from_cust_3.pdf
2025-09-18 23:37:42,794 - INFO - 

25_ingate_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-18 23:37:42,794 - INFO - 

✓ Saved result: output/run1_25_ingate_10.json
2025-09-18 23:37:43,091 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f234ef81_25_ingate_10.png
2025-09-18 23:37:43,115 - INFO - 

22_so_confirmation_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-18 23:37:43,115 - INFO - 

✓ Saved result: output/run1_22_so_confirmation_1.json
2025-09-18 23:37:43,418 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f5c4300a_22_so_confirmation_1.pdf
2025-09-18 23:37:43,430 - INFO - 

25_ingate_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:37:43,431 - INFO - 

✓ Saved result: output/run1_25_ingate_1.json
2025-09-18 23:37:43,734 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/59e55a41_25_ingate_1.pdf
2025-09-18 23:37:43,743 - INFO - 

25_ingate_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-18 23:37:43,743 - INFO - 

✓ Saved result: output/run1_25_ingate_2.json
2025-09-18 23:37:44,044 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/77e6ca6a_25_ingate_2.pdf
2025-09-18 23:37:44,077 - INFO - 

22_so_confirmation_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:37:44,077 - INFO - 

✓ Saved result: output/run1_22_so_confirmation_3.json
2025-09-18 23:37:44,373 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/286e3749_22_so_confirmation_3.pdf
2025-09-18 23:37:44,524 - INFO - 

22_so_confirmation_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "so_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "so_confirmation"
        },
        {
            "page_no": 4,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-18 23:37:44,524 - INFO - 

✓ Saved result: output/run1_22_so_confirmation_2.json
2025-09-18 23:37:44,827 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ea7e920f_22_so_confirmation_2.pdf
2025-09-18 23:37:44,846 - INFO - 

1_bol_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 23:37:44,846 - INFO - 

✓ Saved result: output/run1_1_bol_11.json
2025-09-18 23:37:45,151 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e762725e_1_bol_11.pdf
2025-09-18 23:37:45,163 - INFO - 

26_outgate_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 23:37:45,163 - INFO - 

✓ Saved result: output/run1_26_outgate_10.json
2025-09-18 23:37:45,465 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15ad4a3e_26_outgate_10.pdf
2025-09-18 23:37:45,488 - INFO - 

26_outgate_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 23:37:45,488 - INFO - 

✓ Saved result: output/run1_26_outgate_11.json
2025-09-18 23:37:45,786 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/71e9a8bc_26_outgate_11.pdf
2025-09-18 23:37:45,808 - INFO - 

2_pod_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 23:37:45,808 - INFO - 

✓ Saved result: output/run1_2_pod_10.json
2025-09-18 23:37:46,116 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2421f7fd_2_pod_10.pdf
2025-09-18 23:37:46,135 - INFO - 

26_outgate_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 23:37:46,136 - INFO - 

✓ Saved result: output/run1_26_outgate_1.json
2025-09-18 23:37:46,445 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4b191512_26_outgate_1.pdf
2025-09-18 23:37:46,463 - INFO - 

2_pod_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 23:37:46,464 - INFO - 

✓ Saved result: output/run1_2_pod_11.json
2025-09-18 23:37:46,767 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e14bb5f1_2_pod_11.pdf
2025-09-18 23:37:46,787 - INFO - 

5_clear_to_pay_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-18 23:37:46,787 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_2.json
2025-09-18 23:37:47,084 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6feac388_5_clear_to_pay_2.pdf
2025-09-18 23:37:47,095 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:37:47,095 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_10.json
2025-09-18 23:37:48,020 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8ac2463c_6_scale_ticket_10.png
2025-09-18 23:37:48,049 - INFO - 

3_rate_confirmation_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 23:37:48,050 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_2.json
2025-09-18 23:37:48,347 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/50e43235_3_rate_confirmation_2.pdf
2025-09-18 23:37:48,366 - INFO - 

5_clear_to_pay_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-18 23:37:48,366 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_1.json
2025-09-18 23:37:48,657 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15964f60_5_clear_to_pay_1.pdf
2025-09-18 23:37:48,679 - INFO - 

3_rate_confirmation_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 23:37:48,679 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_3.json
2025-09-18 23:37:48,965 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/47e93596_3_rate_confirmation_3.pdf
2025-09-18 23:37:48,969 - INFO - 

6_scale_ticket_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:37:48,969 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_11.json
2025-09-18 23:37:49,251 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/16f952dc_6_scale_ticket_11.jpg
2025-09-18 23:37:49,280 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 23:37:49,280 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_1.json
2025-09-18 23:37:49,560 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/354c3ea7_6_scale_ticket_1.pdf
2025-09-18 23:37:49,572 - INFO - 

8_log_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:37:49,572 - INFO - 

✓ Saved result: output/run1_8_log_10.json
2025-09-18 23:37:49,858 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c82fe36a_8_log_10.jpeg
2025-09-18 23:37:49,894 - INFO - 

8_log_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-18 23:37:49,894 - INFO - 

✓ Saved result: output/run1_8_log_1.json
2025-09-18 23:37:50,176 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3318b62f_8_log_1.pdf
2025-09-18 23:37:50,206 - INFO - 

3_rate_confirmation_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 23:37:50,206 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_1.json
2025-09-18 23:37:50,495 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/89c072ba_3_rate_confirmation_1.pdf
2025-09-18 23:37:50,511 - INFO - 

9_fuel_receipt_1.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 23:37:50,511 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_1.json
2025-09-18 23:37:50,807 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cacce28e_9_fuel_receipt_1.png
2025-09-18 23:37:50,831 - INFO - 

8_log_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-18 23:37:50,832 - INFO - 

✓ Saved result: output/run1_8_log_11.json
2025-09-18 23:37:51,126 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ad702e80_8_log_11.jpg
2025-09-18 23:37:51,127 - ERROR - ✗ Failed to process input_data_3_per_cat/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 23:37:51,418 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fa1c4490_2_pod_1.pdf
2025-09-18 23:37:51,429 - INFO - 

9_fuel_receipt_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 23:37:51,429 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_10.json
2025-09-18 23:37:51,752 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/50a4f600_9_fuel_receipt_10.jpeg
2025-09-18 23:38:05,272 - INFO - Page 1: Extracted 402 characters, 39 lines from ebf3bbdd_9_fuel_receipt_11_7804075b_page_001.pdf
2025-09-18 23:38:05,272 - INFO - Successfully processed page 1
2025-09-18 23:38:05,272 - INFO - Combined 1 pages into final text
2025-09-18 23:38:05,272 - INFO - Text validation for ebf3bbdd_9_fuel_receipt_11: 419 characters, 1 pages
2025-09-18 23:38:05,274 - INFO - Analyzing document types with Bedrock...
2025-09-18 23:38:05,274 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 23:38:07,386 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ebf3bbdd_9_fuel_receipt_11.pdf
2025-09-18 23:38:07,406 - INFO - 

9_fuel_receipt_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 23:38:07,406 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_11.json
2025-09-18 23:38:09,243 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ebf3bbdd_9_fuel_receipt_11.pdf
2025-09-18 23:38:09,244 - INFO - 
📊 Processing Summary:
2025-09-18 23:38:09,244 - INFO -    Total files: 57
2025-09-18 23:38:09,244 - INFO -    Successful: 56
2025-09-18 23:38:09,244 - INFO -    Failed: 1
2025-09-18 23:38:09,245 - INFO -    Duration: 94.70 seconds
2025-09-18 23:38:09,245 - INFO -    Output directory: output
2025-09-18 23:38:09,245 - INFO - 
📋 Successfully Processed Files:
2025-09-18 23:38:09,245 - INFO -    📄 10_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:38:09,245 - INFO -    📄 10_invoice_10.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:38:09,245 - INFO -    📄 10_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:38:09,245 - INFO -    📄 12_combined_carrier_documents_1.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"combined_carrier_documents"}]}
2025-09-18 23:38:09,245 - INFO -    📄 12_combined_carrier_documents_2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:38:09,245 - INFO -    📄 12_combined_carrier_documents_3.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 23:38:09,245 - INFO -    📄 13_pack_list_1.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-18 23:38:09,245 - INFO -    📄 13_pack_list_10.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 23:38:09,246 - INFO -    📄 13_pack_list_2.PDF: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 23:38:09,246 - INFO -    📄 14_po_1.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 23:38:09,246 - INFO -    📄 14_po_2.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 23:38:09,246 - INFO -    📄 14_po_3.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-18 23:38:09,246 - INFO -    📄 15_comm_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-18 23:38:09,246 - INFO -    📄 15_comm_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-18 23:38:09,246 - INFO -    📄 16_customs_doc_1.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"customs_doc"}]}
2025-09-18 23:38:09,246 - INFO -    📄 16_customs_doc_2.tiff: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 23:38:09,246 - INFO -    📄 16_customs_doc_3.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 23:38:09,246 - INFO -    📄 17_nmfc_cert_1.jpg: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-18 23:38:09,246 - INFO -    📄 17_nmfc_cert_2.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"scale_ticket"}]}
2025-09-18 23:38:09,246 - INFO -    📄 19_coa_1.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 23:38:09,246 - INFO -    📄 19_coa_2.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 23:38:09,246 - INFO -    📄 19_coa_3.pdf: {"documents":[{"page_no":1,"doc_type":"coa"}]}
2025-09-18 23:38:09,247 - INFO -    📄 1_bol_1.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:38:09,247 - INFO -    📄 1_bol_10.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:38:09,247 - INFO -    📄 1_bol_11.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 23:38:09,247 - INFO -    📄 20_tender_from_cust_1.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-18 23:38:09,247 - INFO -    📄 20_tender_from_cust_2.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-18 23:38:09,247 - INFO -    📄 20_tender_from_cust_3.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"},{"page_no":3,"doc_type":"tender_from_cust"}]}
2025-09-18 23:38:09,247 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 23:38:09,247 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 23:38:09,247 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 23:38:09,247 - INFO -    📄 22_so_confirmation_1.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-18 23:38:09,247 - INFO -    📄 22_so_confirmation_2.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"},{"page_no":2,"doc_type":"so_confirmation"},{"page_no":3,"doc_type":"so_confirmation"},{"page_no":4,"doc_type":"so_confirmation"}]}
2025-09-18 23:38:09,247 - INFO -    📄 22_so_confirmation_3.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-18 23:38:09,247 - INFO -    📄 25_ingate_1.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"other"}]}
2025-09-18 23:38:09,248 - INFO -    📄 25_ingate_10.png: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-18 23:38:09,248 - INFO -    📄 25_ingate_2.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-18 23:38:09,248 - INFO -    📄 26_outgate_1.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"outgate"}]}
2025-09-18 23:38:09,248 - INFO -    📄 26_outgate_10.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 23:38:09,248 - INFO -    📄 26_outgate_11.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 23:38:09,248 - INFO -    📄 2_pod_10.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 23:38:09,248 - INFO -    📄 2_pod_11.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 23:38:09,248 - INFO -    📄 3_rate_confirmation_1.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-18 23:38:09,248 - INFO -    📄 3_rate_confirmation_2.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-18 23:38:09,248 - INFO -    📄 3_rate_confirmation_3.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-18 23:38:09,248 - INFO -    📄 5_clear_to_pay_1.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-18 23:38:09,248 - INFO -    📄 5_clear_to_pay_2.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-18 23:38:09,248 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:38:09,248 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:38:09,248 - INFO -    📄 6_scale_ticket_11.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 23:38:09,248 - INFO -    📄 8_log_1.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"}]}
2025-09-18 23:38:09,248 - INFO -    📄 8_log_10.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 23:38:09,248 - INFO -    📄 8_log_11.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-18 23:38:09,248 - INFO -    📄 9_fuel_receipt_1.png: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 23:38:09,248 - INFO -    📄 9_fuel_receipt_10.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 23:38:09,248 - INFO -    📄 9_fuel_receipt_11.pdf: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 23:38:09,248 - ERROR - 
❌ Errors:
2025-09-18 23:38:09,248 - ERROR -    Failed to process input_data_3_per_cat/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 23:38:09,249 - INFO - 
============================================================================================================================================
2025-09-18 23:38:09,250 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 23:38:09,250 - INFO - ============================================================================================================================================
2025-09-18 23:38:09,250 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 23:38:09,250 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 23:38:09,250 - INFO - 10_invoice_1.pdf                                   1      invoice              run1_10_invoice_1.json                            
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_1.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 10_invoice_10.pdf                                  1      invoice              run1_10_invoice_10.json                           
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_10.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_10.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 10_invoice_2.pdf                                   1      invoice              run1_10_invoice_2.json                            
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_2.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_2.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 12_combined_carrier_documents_1.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_1.json         
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_1.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 12_combined_carrier_documents_1.pdf                2      combined_carrier_d... run1_12_combined_carrier_documents_1.json         
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_1.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 12_combined_carrier_documents_2.pdf                1      bol                  run1_12_combined_carrier_documents_2.json         
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_2.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 12_combined_carrier_documents_3.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_3.json         
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_3.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 13_pack_list_1.pdf                                 1      pack_list            run1_13_pack_list_1.json                          
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_1.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 13_pack_list_1.pdf                                 2      pack_list            run1_13_pack_list_1.json                          
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_1.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 13_pack_list_10.pdf                                1      pack_list            run1_13_pack_list_10.json                         
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_10.pdf
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_10.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,250 - INFO - 13_pack_list_2.PDF                                 1      pack_list            run1_13_pack_list_2.json                          
2025-09-18 23:38:09,250 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/13_pack_list/13_pack_list_2.PDF
2025-09-18 23:38:09,250 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_2.json
2025-09-18 23:38:09,250 - INFO - 
2025-09-18 23:38:09,251 - INFO - 14_po_1.pdf                                        1      po                   run1_14_po_1.json                                 
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_1.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_1.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 14_po_2.pdf                                        1      po                   run1_14_po_2.json                                 
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_2.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_2.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 14_po_3.pdf                                        1      po                   run1_14_po_3.json                                 
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_3.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_3.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 14_po_3.pdf                                        2      po                   run1_14_po_3.json                                 
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/14_po/14_po_3.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_3.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 15_comm_invoice_1.pdf                              1      comm_invoice         run1_15_comm_invoice_1.json                       
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/15_comm_invoice/15_comm_invoice_1.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_15_comm_invoice_1.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 15_comm_invoice_2.pdf                              1      comm_invoice         run1_15_comm_invoice_2.json                       
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/15_comm_invoice/15_comm_invoice_2.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_15_comm_invoice_2.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 16_customs_doc_1.pdf                               1      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 16_customs_doc_1.pdf                               2      comm_invoice         run1_16_customs_doc_1.json                        
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 16_customs_doc_1.pdf                               3      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 16_customs_doc_2.tiff                              1      customs_doc          run1_16_customs_doc_2.json                        
2025-09-18 23:38:09,251 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_2.tiff
2025-09-18 23:38:09,251 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_2.json
2025-09-18 23:38:09,251 - INFO - 
2025-09-18 23:38:09,251 - INFO - 16_customs_doc_3.pdf                               1      customs_doc          run1_16_customs_doc_3.json                        
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/16_customs_doc/16_customs_doc_3.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_3.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 17_nmfc_cert_1.jpg                                 1      nmfc_cert            run1_17_nmfc_cert_1.json                          
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_1.jpg
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_17_nmfc_cert_1.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 17_nmfc_cert_2.pdf                                 1      nmfc_cert            run1_17_nmfc_cert_2.json                          
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_17_nmfc_cert_2.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 17_nmfc_cert_2.pdf                                 2      scale_ticket         run1_17_nmfc_cert_2.json                          
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_17_nmfc_cert_2.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 19_coa_1.pdf                                       1      other                run1_19_coa_1.json                                
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/19_coa/19_coa_1.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_19_coa_1.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 19_coa_2.pdf                                       1      other                run1_19_coa_2.json                                
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/19_coa/19_coa_2.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_19_coa_2.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 19_coa_3.pdf                                       1      coa                  run1_19_coa_3.json                                
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/19_coa/19_coa_3.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_19_coa_3.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 1_bol_1.pdf                                        1      bol                  run1_1_bol_1.json                                 
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_1.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_1_bol_1.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 1_bol_10.pdf                                       1      bol                  run1_1_bol_10.json                                
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_10.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_1_bol_10.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 1_bol_11.pdf                                       1      bol                  run1_1_bol_11.json                                
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_11.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_1_bol_11.json
2025-09-18 23:38:09,252 - INFO - 
2025-09-18 23:38:09,252 - INFO - 20_tender_from_cust_1.pdf                          1      tender_from_cust     run1_20_tender_from_cust_1.json                   
2025-09-18 23:38:09,252 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf
2025-09-18 23:38:09,252 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_1.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 20_tender_from_cust_1.pdf                          2      tender_from_cust     run1_20_tender_from_cust_1.json                   
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_1.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 20_tender_from_cust_2.pdf                          1      tender_from_cust     run1_20_tender_from_cust_2.json                   
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_2.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 20_tender_from_cust_2.pdf                          2      tender_from_cust     run1_20_tender_from_cust_2.json                   
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_2.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 20_tender_from_cust_3.pdf                          1      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 20_tender_from_cust_3.pdf                          2      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 20_tender_from_cust_3.pdf                          3      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 21_lumper_receipt_10.png                           1      invoice              run1_21_lumper_receipt_10.json                    
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 22_so_confirmation_1.pdf                           1      so_confirmation      run1_22_so_confirmation_1.json                    
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_1.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_1.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 22_so_confirmation_2.pdf                           1      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 22_so_confirmation_2.pdf                           2      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:38:09,253 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:38:09,253 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:38:09,253 - INFO - 
2025-09-18 23:38:09,253 - INFO - 22_so_confirmation_2.pdf                           3      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 22_so_confirmation_2.pdf                           4      so_confirmation      run1_22_so_confirmation_2.json                    
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_2.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 22_so_confirmation_3.pdf                           1      other                run1_22_so_confirmation_3.json                    
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_3.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 22_so_confirmation_3.pdf                           2      other                run1_22_so_confirmation_3.json                    
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_22_so_confirmation_3.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 25_ingate_1.pdf                                    1      ingate               run1_25_ingate_1.json                             
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_1.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_1.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 25_ingate_1.pdf                                    2      other                run1_25_ingate_1.json                             
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_1.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_1.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 25_ingate_10.png                                   1      ingate               run1_25_ingate_10.json                            
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_10.png
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_10.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 25_ingate_2.pdf                                    1      ingate               run1_25_ingate_2.json                             
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/25_ingate/25_ingate_2.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_25_ingate_2.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 26_outgate_1.pdf                                   1      outgate              run1_26_outgate_1.json                            
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_1.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_1.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 26_outgate_1.pdf                                   2      outgate              run1_26_outgate_1.json                            
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_1.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_1.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,254 - INFO - 26_outgate_10.pdf                                  1      outgate              run1_26_outgate_10.json                           
2025-09-18 23:38:09,254 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_10.pdf
2025-09-18 23:38:09,254 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_10.json
2025-09-18 23:38:09,254 - INFO - 
2025-09-18 23:38:09,255 - INFO - 26_outgate_11.pdf                                  1      outgate              run1_26_outgate_11.json                           
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/26_outgate/26_outgate_11.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_11.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 2_pod_10.pdf                                       1      pod                  run1_2_pod_10.json                                
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_10.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_10.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 2_pod_11.pdf                                       1      pod                  run1_2_pod_11.json                                
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_11.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_11.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 3_rate_confirmation_1.pdf                          1      rate_confirmation    run1_3_rate_confirmation_1.json                   
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_1.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_1.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 3_rate_confirmation_2.pdf                          1      rate_confirmation    run1_3_rate_confirmation_2.json                   
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_2.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_2.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 3_rate_confirmation_3.pdf                          1      rate_confirmation    run1_3_rate_confirmation_3.json                   
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_3.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 3_rate_confirmation_3.pdf                          2      rate_confirmation    run1_3_rate_confirmation_3.json                   
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_3_rate_confirmation_3.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 5_clear_to_pay_1.pdf                               1      clear_to_pay         run1_5_clear_to_pay_1.json                        
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_1.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_5_clear_to_pay_1.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 5_clear_to_pay_2.pdf                               1      clear_to_pay         run1_5_clear_to_pay_2.json                        
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_2.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_5_clear_to_pay_2.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 6_scale_ticket_1.pdf                               1      scale_ticket         run1_6_scale_ticket_1.json                        
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_6_scale_ticket_1.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,255 - INFO - 6_scale_ticket_10.png                              1      scale_ticket         run1_6_scale_ticket_10.json                       
2025-09-18 23:38:09,255 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png
2025-09-18 23:38:09,255 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_6_scale_ticket_10.json
2025-09-18 23:38:09,255 - INFO - 
2025-09-18 23:38:09,256 - INFO - 6_scale_ticket_11.jpg                              1      scale_ticket         run1_6_scale_ticket_11.json                       
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_6_scale_ticket_11.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 8_log_1.pdf                                        1      other                run1_8_log_1.json                                 
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_1.pdf
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 8_log_1.pdf                                        2      other                run1_8_log_1.json                                 
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_1.pdf
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 8_log_1.pdf                                        3      other                run1_8_log_1.json                                 
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_1.pdf
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_1.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 8_log_10.jpeg                                      1      other                run1_8_log_10.json                                
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_10.jpeg
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_10.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 8_log_11.jpg                                       1      log                  run1_8_log_11.json                                
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/8_log/8_log_11.jpg
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_8_log_11.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 9_fuel_receipt_1.png                               1      fuel_receipt         run1_9_fuel_receipt_1.json                        
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_1.png
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_1.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 9_fuel_receipt_10.jpeg                             1      fuel_receipt         run1_9_fuel_receipt_10.json                       
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_10.jpeg
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_10.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - 9_fuel_receipt_11.pdf                              1      fuel_receipt         run1_9_fuel_receipt_11.json                       
2025-09-18 23:38:09,256 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_11.pdf
2025-09-18 23:38:09,256 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_11.json
2025-09-18 23:38:09,256 - INFO - 
2025-09-18 23:38:09,256 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 23:38:09,256 - INFO - Total entries: 75
2025-09-18 23:38:09,256 - INFO - ============================================================================================================================================
2025-09-18 23:38:09,256 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 23:38:09,256 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 23:38:09,256 - INFO -   1. 10_invoice_1.pdf                    Page 1   → invoice         | run1_10_invoice_1.json
2025-09-18 23:38:09,257 - INFO -   2. 10_invoice_10.pdf                   Page 1   → invoice         | run1_10_invoice_10.json
2025-09-18 23:38:09,257 - INFO -   3. 10_invoice_2.pdf                    Page 1   → invoice         | run1_10_invoice_2.json
2025-09-18 23:38:09,257 - INFO -   4. 12_combined_carrier_documents_1.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_1.json
2025-09-18 23:38:09,257 - INFO -   5. 12_combined_carrier_documents_1.pdf Page 2   → combined_carrier_documents | run1_12_combined_carrier_documents_1.json
2025-09-18 23:38:09,257 - INFO -   6. 12_combined_carrier_documents_2.pdf Page 1   → bol             | run1_12_combined_carrier_documents_2.json
2025-09-18 23:38:09,257 - INFO -   7. 12_combined_carrier_documents_3.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_3.json
2025-09-18 23:38:09,257 - INFO -   8. 13_pack_list_1.pdf                  Page 1   → pack_list       | run1_13_pack_list_1.json
2025-09-18 23:38:09,257 - INFO -   9. 13_pack_list_1.pdf                  Page 2   → pack_list       | run1_13_pack_list_1.json
2025-09-18 23:38:09,257 - INFO -  10. 13_pack_list_10.pdf                 Page 1   → pack_list       | run1_13_pack_list_10.json
2025-09-18 23:38:09,257 - INFO -  11. 13_pack_list_2.PDF                  Page 1   → pack_list       | run1_13_pack_list_2.json
2025-09-18 23:38:09,257 - INFO -  12. 14_po_1.pdf                         Page 1   → po              | run1_14_po_1.json
2025-09-18 23:38:09,257 - INFO -  13. 14_po_2.pdf                         Page 1   → po              | run1_14_po_2.json
2025-09-18 23:38:09,257 - INFO -  14. 14_po_3.pdf                         Page 1   → po              | run1_14_po_3.json
2025-09-18 23:38:09,257 - INFO -  15. 14_po_3.pdf                         Page 2   → po              | run1_14_po_3.json
2025-09-18 23:38:09,257 - INFO -  16. 15_comm_invoice_1.pdf               Page 1   → comm_invoice    | run1_15_comm_invoice_1.json
2025-09-18 23:38:09,257 - INFO -  17. 15_comm_invoice_2.pdf               Page 1   → comm_invoice    | run1_15_comm_invoice_2.json
2025-09-18 23:38:09,257 - INFO -  18. 16_customs_doc_1.pdf                Page 1   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 23:38:09,257 - INFO -  19. 16_customs_doc_1.pdf                Page 2   → comm_invoice    | run1_16_customs_doc_1.json
2025-09-18 23:38:09,257 - INFO -  20. 16_customs_doc_1.pdf                Page 3   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 23:38:09,257 - INFO -  21. 16_customs_doc_2.tiff               Page 1   → customs_doc     | run1_16_customs_doc_2.json
2025-09-18 23:38:09,257 - INFO -  22. 16_customs_doc_3.pdf                Page 1   → customs_doc     | run1_16_customs_doc_3.json
2025-09-18 23:38:09,257 - INFO -  23. 17_nmfc_cert_1.jpg                  Page 1   → nmfc_cert       | run1_17_nmfc_cert_1.json
2025-09-18 23:38:09,257 - INFO -  24. 17_nmfc_cert_2.pdf                  Page 1   → nmfc_cert       | run1_17_nmfc_cert_2.json
2025-09-18 23:38:09,257 - INFO -  25. 17_nmfc_cert_2.pdf                  Page 2   → scale_ticket    | run1_17_nmfc_cert_2.json
2025-09-18 23:38:09,257 - INFO -  26. 19_coa_1.pdf                        Page 1   → other           | run1_19_coa_1.json
2025-09-18 23:38:09,257 - INFO -  27. 19_coa_2.pdf                        Page 1   → other           | run1_19_coa_2.json
2025-09-18 23:38:09,257 - INFO -  28. 19_coa_3.pdf                        Page 1   → coa             | run1_19_coa_3.json
2025-09-18 23:38:09,257 - INFO -  29. 1_bol_1.pdf                         Page 1   → bol             | run1_1_bol_1.json
2025-09-18 23:38:09,257 - INFO -  30. 1_bol_10.pdf                        Page 1   → bol             | run1_1_bol_10.json
2025-09-18 23:38:09,257 - INFO -  31. 1_bol_11.pdf                        Page 1   → bol             | run1_1_bol_11.json
2025-09-18 23:38:09,257 - INFO -  32. 20_tender_from_cust_1.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_1.json
2025-09-18 23:38:09,257 - INFO -  33. 20_tender_from_cust_1.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_1.json
2025-09-18 23:38:09,257 - INFO -  34. 20_tender_from_cust_2.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_2.json
2025-09-18 23:38:09,258 - INFO -  35. 20_tender_from_cust_2.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_2.json
2025-09-18 23:38:09,258 - INFO -  36. 20_tender_from_cust_3.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 23:38:09,258 - INFO -  37. 20_tender_from_cust_3.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 23:38:09,258 - INFO -  38. 20_tender_from_cust_3.pdf           Page 3   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 23:38:09,258 - INFO -  39. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 23:38:09,258 - INFO -  40. 21_lumper_receipt_10.png            Page 1   → invoice         | run1_21_lumper_receipt_10.json
2025-09-18 23:38:09,258 - INFO -  41. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 23:38:09,258 - INFO -  42. 22_so_confirmation_1.pdf            Page 1   → so_confirmation | run1_22_so_confirmation_1.json
2025-09-18 23:38:09,258 - INFO -  43. 22_so_confirmation_2.pdf            Page 1   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:38:09,258 - INFO -  44. 22_so_confirmation_2.pdf            Page 2   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:38:09,258 - INFO -  45. 22_so_confirmation_2.pdf            Page 3   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:38:09,258 - INFO -  46. 22_so_confirmation_2.pdf            Page 4   → so_confirmation | run1_22_so_confirmation_2.json
2025-09-18 23:38:09,258 - INFO -  47. 22_so_confirmation_3.pdf            Page 1   → other           | run1_22_so_confirmation_3.json
2025-09-18 23:38:09,258 - INFO -  48. 22_so_confirmation_3.pdf            Page 2   → other           | run1_22_so_confirmation_3.json
2025-09-18 23:38:09,258 - INFO -  49. 25_ingate_1.pdf                     Page 1   → ingate          | run1_25_ingate_1.json
2025-09-18 23:38:09,258 - INFO -  50. 25_ingate_1.pdf                     Page 2   → other           | run1_25_ingate_1.json
2025-09-18 23:38:09,258 - INFO -  51. 25_ingate_10.png                    Page 1   → ingate          | run1_25_ingate_10.json
2025-09-18 23:38:09,258 - INFO -  52. 25_ingate_2.pdf                     Page 1   → ingate          | run1_25_ingate_2.json
2025-09-18 23:38:09,258 - INFO -  53. 26_outgate_1.pdf                    Page 1   → outgate         | run1_26_outgate_1.json
2025-09-18 23:38:09,258 - INFO -  54. 26_outgate_1.pdf                    Page 2   → outgate         | run1_26_outgate_1.json
2025-09-18 23:38:09,258 - INFO -  55. 26_outgate_10.pdf                   Page 1   → outgate         | run1_26_outgate_10.json
2025-09-18 23:38:09,258 - INFO -  56. 26_outgate_11.pdf                   Page 1   → outgate         | run1_26_outgate_11.json
2025-09-18 23:38:09,258 - INFO -  57. 2_pod_10.pdf                        Page 1   → pod             | run1_2_pod_10.json
2025-09-18 23:38:09,258 - INFO -  58. 2_pod_11.pdf                        Page 1   → pod             | run1_2_pod_11.json
2025-09-18 23:38:09,258 - INFO -  59. 3_rate_confirmation_1.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_1.json
2025-09-18 23:38:09,258 - INFO -  60. 3_rate_confirmation_2.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_2.json
2025-09-18 23:38:09,258 - INFO -  61. 3_rate_confirmation_3.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_3.json
2025-09-18 23:38:09,258 - INFO -  62. 3_rate_confirmation_3.pdf           Page 2   → rate_confirmation | run1_3_rate_confirmation_3.json
2025-09-18 23:38:09,258 - INFO -  63. 5_clear_to_pay_1.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_1.json
2025-09-18 23:38:09,258 - INFO -  64. 5_clear_to_pay_2.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_2.json
2025-09-18 23:38:09,258 - INFO -  65. 6_scale_ticket_1.pdf                Page 1   → scale_ticket    | run1_6_scale_ticket_1.json
2025-09-18 23:38:09,258 - INFO -  66. 6_scale_ticket_10.png               Page 1   → scale_ticket    | run1_6_scale_ticket_10.json
2025-09-18 23:38:09,259 - INFO -  67. 6_scale_ticket_11.jpg               Page 1   → scale_ticket    | run1_6_scale_ticket_11.json
2025-09-18 23:38:09,259 - INFO -  68. 8_log_1.pdf                         Page 1   → other           | run1_8_log_1.json
2025-09-18 23:38:09,259 - INFO -  69. 8_log_1.pdf                         Page 2   → other           | run1_8_log_1.json
2025-09-18 23:38:09,259 - INFO -  70. 8_log_1.pdf                         Page 3   → other           | run1_8_log_1.json
2025-09-18 23:38:09,259 - INFO -  71. 8_log_10.jpeg                       Page 1   → other           | run1_8_log_10.json
2025-09-18 23:38:09,259 - INFO -  72. 8_log_11.jpg                        Page 1   → log             | run1_8_log_11.json
2025-09-18 23:38:09,259 - INFO -  73. 9_fuel_receipt_1.png                Page 1   → fuel_receipt    | run1_9_fuel_receipt_1.json
2025-09-18 23:38:09,259 - INFO -  74. 9_fuel_receipt_10.jpeg              Page 1   → fuel_receipt    | run1_9_fuel_receipt_10.json
2025-09-18 23:38:09,259 - INFO -  75. 9_fuel_receipt_11.pdf               Page 1   → fuel_receipt    | run1_9_fuel_receipt_11.json
2025-09-18 23:38:09,259 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 23:38:09,260 - INFO - 
✅ Test completed: {'total_files': 57, 'processed': 56, 'failed': 1, 'errors': ['Failed to process input_data_3_per_cat/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 94.695083, 'processed_files': [{'filename': '10_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_1.pdf'}, {'filename': '10_invoice_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_10.pdf'}, {'filename': '10_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_2.pdf'}, {'filename': '12_combined_carrier_documents_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}, {'page_no': 2, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf'}, {'filename': '12_combined_carrier_documents_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf'}, {'filename': '12_combined_carrier_documents_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_3_per_cat/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf'}, {'filename': '13_pack_list_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_3_per_cat/13_pack_list/13_pack_list_1.pdf'}, {'filename': '13_pack_list_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_3_per_cat/13_pack_list/13_pack_list_10.pdf'}, {'filename': '13_pack_list_2.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_3_per_cat/13_pack_list/13_pack_list_2.PDF'}, {'filename': '14_po_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_3_per_cat/14_po/14_po_1.pdf'}, {'filename': '14_po_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_3_per_cat/14_po/14_po_2.pdf'}, {'filename': '14_po_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': 'input_data_3_per_cat/14_po/14_po_3.pdf'}, {'filename': '15_comm_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': 'input_data_3_per_cat/15_comm_invoice/15_comm_invoice_1.pdf'}, {'filename': '15_comm_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': 'input_data_3_per_cat/15_comm_invoice/15_comm_invoice_2.pdf'}, {'filename': '16_customs_doc_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'customs_doc'}]}, 'file_path': 'input_data_3_per_cat/16_customs_doc/16_customs_doc_1.pdf'}, {'filename': '16_customs_doc_2.tiff', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': 'input_data_3_per_cat/16_customs_doc/16_customs_doc_2.tiff'}, {'filename': '16_customs_doc_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': 'input_data_3_per_cat/16_customs_doc/16_customs_doc_3.pdf'}, {'filename': '17_nmfc_cert_1.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': 'input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_1.jpg'}, {'filename': '17_nmfc_cert_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/17_nmfc_cert/17_nmfc_cert_2.pdf'}, {'filename': '19_coa_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/19_coa/19_coa_1.pdf'}, {'filename': '19_coa_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/19_coa/19_coa_2.pdf'}, {'filename': '19_coa_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'coa'}]}, 'file_path': 'input_data_3_per_cat/19_coa/19_coa_3.pdf'}, {'filename': '1_bol_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/1_bol/1_bol_1.pdf'}, {'filename': '1_bol_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/1_bol/1_bol_10.pdf'}, {'filename': '1_bol_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_3_per_cat/1_bol/1_bol_11.pdf'}, {'filename': '20_tender_from_cust_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_1.pdf'}, {'filename': '20_tender_from_cust_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_2.pdf'}, {'filename': '20_tender_from_cust_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}, {'page_no': 3, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_3_per_cat/20_tender_from_cust/20_tender_from_cust_3.pdf'}, {'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_3_per_cat/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '22_so_confirmation_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': 'input_data_3_per_cat/22_so_confirmation/22_so_confirmation_1.pdf'}, {'filename': '22_so_confirmation_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}, {'page_no': 2, 'doc_type': 'so_confirmation'}, {'page_no': 3, 'doc_type': 'so_confirmation'}, {'page_no': 4, 'doc_type': 'so_confirmation'}]}, 'file_path': 'input_data_3_per_cat/22_so_confirmation/22_so_confirmation_2.pdf'}, {'filename': '22_so_confirmation_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/22_so_confirmation/22_so_confirmation_3.pdf'}, {'filename': '25_ingate_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/25_ingate/25_ingate_1.pdf'}, {'filename': '25_ingate_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': 'input_data_3_per_cat/25_ingate/25_ingate_10.png'}, {'filename': '25_ingate_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}]}, 'file_path': 'input_data_3_per_cat/25_ingate/25_ingate_2.pdf'}, {'filename': '26_outgate_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_3_per_cat/26_outgate/26_outgate_1.pdf'}, {'filename': '26_outgate_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_3_per_cat/26_outgate/26_outgate_10.pdf'}, {'filename': '26_outgate_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_3_per_cat/26_outgate/26_outgate_11.pdf'}, {'filename': '2_pod_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_3_per_cat/2_pod/2_pod_10.pdf'}, {'filename': '2_pod_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_3_per_cat/2_pod/2_pod_11.pdf'}, {'filename': '3_rate_confirmation_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_1.pdf'}, {'filename': '3_rate_confirmation_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_2.pdf'}, {'filename': '3_rate_confirmation_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_3_per_cat/3_rate_confirmation/3_rate_confirmation_3.pdf'}, {'filename': '5_clear_to_pay_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': 'input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_1.pdf'}, {'filename': '5_clear_to_pay_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': 'input_data_3_per_cat/5_clear_to_pay/5_clear_to_pay_2.pdf'}, {'filename': '6_scale_ticket_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf'}, {'filename': '6_scale_ticket_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png'}, {'filename': '6_scale_ticket_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': 'input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg'}, {'filename': '8_log_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/8_log/8_log_1.pdf'}, {'filename': '8_log_10.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_3_per_cat/8_log/8_log_10.jpeg'}, {'filename': '8_log_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'log'}]}, 'file_path': 'input_data_3_per_cat/8_log/8_log_11.jpg'}, {'filename': '9_fuel_receipt_1.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_1.png'}, {'filename': '9_fuel_receipt_10.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_10.jpeg'}, {'filename': '9_fuel_receipt_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_3_per_cat/9_fuel_receipt/9_fuel_receipt_11.pdf'}]}
