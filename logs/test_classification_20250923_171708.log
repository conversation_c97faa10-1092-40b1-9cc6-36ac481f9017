2025-09-23 17:17:08,212 - INFO - Logging initialized. Log file: logs/test_classification_20250923_171708.log
2025-09-23 17:17:08,212 - INFO - 📁 Found 1 files to process
2025-09-23 17:17:08,212 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 17:17:08,212 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 17:17:08,212 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 17:17:08,212 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 17:17:08,213 - INFO - ⬆️ [17:17:08] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:17:12,358 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/1a6af42b_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:17:12,359 - INFO - 🔍 [17:17:12] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:17:12,360 - INFO - Initializing TextractProcessor...
2025-09-23 17:17:12,376 - INFO - Initializing BedrockProcessor...
2025-09-23 17:17:12,381 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1a6af42b_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:17:12,381 - INFO - Processing PDF from S3...
2025-09-23 17:17:12,382 - INFO - Downloading PDF from S3 to /tmp/tmp93lyjss4/1a6af42b_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:17:15,310 - INFO - Downloaded PDF size: 1.1 MB
2025-09-23 17:17:15,310 - INFO - Splitting PDF into individual pages...
2025-09-23 17:17:15,311 - INFO - Splitting PDF 1a6af42b_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 17:17:15,315 - INFO - Split PDF into 7 pages
2025-09-23 17:17:15,315 - INFO - Processing pages with Textract in parallel...
2025-09-23 17:17:15,315 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 17:17:20,822 - INFO - Page 1: Extracted 1168 characters, 45 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_001.pdf
2025-09-23 17:17:20,823 - INFO - Successfully processed page 1
2025-09-23 17:17:21,115 - INFO - Page 4: Extracted 1151 characters, 63 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_004.pdf
2025-09-23 17:17:21,125 - INFO - Page 3: Extracted 929 characters, 64 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_003.pdf
2025-09-23 17:17:21,131 - INFO - Successfully processed page 4
2025-09-23 17:17:21,139 - INFO - Page 5: Extracted 2022 characters, 27 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_005.pdf
2025-09-23 17:17:21,139 - INFO - Successfully processed page 3
2025-09-23 17:17:21,139 - INFO - Successfully processed page 5
2025-09-23 17:17:21,940 - INFO - Page 2: Extracted 1174 characters, 29 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_002.pdf
2025-09-23 17:17:21,940 - INFO - Successfully processed page 2
2025-09-23 17:17:22,107 - INFO - Page 6: Extracted 4159 characters, 115 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_006.pdf
2025-09-23 17:17:22,107 - INFO - Successfully processed page 6
2025-09-23 17:17:25,208 - INFO - Page 7: Extracted 788 characters, 55 lines from 1a6af42b_Purchase_Order_VRSD0E9045781_f46e6fa9_page_007.pdf
2025-09-23 17:17:25,208 - INFO - Successfully processed page 7
2025-09-23 17:17:25,209 - INFO - Combined 7 pages into final text
2025-09-23 17:17:25,210 - INFO - Text validation for 1a6af42b_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 17:17:25,210 - INFO - Analyzing document types with Bedrock...
2025-09-23 17:17:25,211 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 17:17:32,778 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-23 17:17:32,778 - ERROR - Processing failed for s3://document-extraction-logistically/temp/1a6af42b_Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:17:32,779 - ERROR - ✗ Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:17:33,699 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1a6af42b_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:17:33,699 - INFO - 
📊 Processing Summary:
2025-09-23 17:17:33,700 - INFO -    Total files: 1
2025-09-23 17:17:33,700 - INFO -    Successful: 0
2025-09-23 17:17:33,700 - INFO -    Failed: 1
2025-09-23 17:17:33,700 - INFO -    Duration: 25.49 seconds
2025-09-23 17:17:33,700 - INFO -    Output directory: output
2025-09-23 17:17:33,701 - ERROR - 
❌ Errors:
2025-09-23 17:17:33,701 - ERROR -    Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:17:33,701 - INFO - 
📋 No files were successfully processed.
2025-09-23 17:17:33,701 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 25.486906, 'processed_files': []}
