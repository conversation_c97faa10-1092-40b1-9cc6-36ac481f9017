{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9997085928916931, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 1.28812374100562e-07}, {"X": 1.0, "Y": 0.9997085928916931}, {"X": 0.0, "Y": 0.9996108412742615}]}, "Id": "bfdf4830-8c1b-40c1-b228-093e40c99c16", "Relationships": [{"Type": "CHILD", "Ids": ["a76d3e8d-e292-46a0-b360-f26d3e311c24", "a83eab6f-2820-45a2-9e54-f28576ccc91d", "c262708a-d0c5-4622-9dfd-4b49968cf83f", "0b2eaf16-3bf5-4933-bcce-54923e7c9261", "61478c08-77b5-46db-b3e0-bb5d48366f99", "db46570b-35b6-4008-ad53-777872099e00", "67de5cce-137b-4ee5-adc4-2f300dac192d", "2441f9e8-b2b7-4e33-8325-63c3d198bbe0", "30f601ce-ee1f-41a8-807b-e28c09b7da09", "0b9d319b-026b-478f-aee3-ea00f6255026", "790a9d93-4f6a-4c5e-a5f1-1df66d68c1ec", "a05f2b0a-bf0d-4c6d-93d3-b19b000489a1", "4f4b32ed-73ec-422c-833c-c40604d9ddb1", "371ffce7-d552-4882-ac79-7f4a9cb6a2d8", "89913de2-756d-4417-bac4-9c12bd6aab59", "bf3e8e9d-af55-49c3-a4bf-c2f0e32a4735", "ab18deb9-4a2b-4d51-8910-e5ebe608e774", "2842936f-3b23-4543-a676-7ad1b7bc2617", "bf93a16a-2780-4c33-aa87-9939200c4e8c", "bdb1fbe5-8dfe-4b04-b5ff-c6cbb6886936", "6a4d2e18-b2b0-4d74-94c7-bf6265ec3208", "ec750c8b-9176-48ed-9d0e-1e686a2466e2", "d154dbad-3213-43de-a375-4949c9953708", "c46453b7-f5bb-472c-9bcd-f7d3d3039848", "6d9b7867-d303-4e3c-8fc4-2bed3f2d613a", "26a73a03-d9c3-4d99-ad86-af7cad7a4b66", "d9ff2889-2bb4-460a-a46c-e1905d67b3a3", "421dc6de-8ea7-42aa-a529-55af8987fc01", "bf85255e-7393-4d4d-ae01-58465aa52294", "27895314-4b93-4a5a-9423-c456ea92bfa3", "23be0e87-4f6a-46b8-88f5-9a4b92789635", "70144070-e4ba-496d-9e85-3d2c9694584f", "43f8fc12-db0b-4208-9a92-28ebb5e13194", "1d5f7150-847e-4f11-a20a-d007bef19405", "cbf25d33-99aa-4b54-9035-0ecd2e537f60", "ed5cd09e-ae86-42e5-8bbd-749a13554f3b", "71a9d4c1-788e-4dd8-bad3-a5070a84e8be", "a6606813-c5c2-4044-af22-90e0d7e69071", "73c1cfca-93bf-46bd-9b47-93559daf98c3", "2932509c-cdb4-4fd1-8d02-112a9fff390b", "22b5886c-d2ae-40bb-98bc-e3b452c9d558", "658e558a-4330-41ef-bc4e-584c107216f7", "79ae8226-0df5-47d0-9553-49c39f821582", "b552b85e-eff9-4765-9189-6315b661ac88", "0f26e6c0-48e8-4625-a7c7-d63fb8201988", "f6d1c275-28f5-4913-a991-8923f08596b2", "e8115103-7a0a-4e18-84fb-1149d5dd03b4", "49b38e7a-40b2-408f-bc86-ce464f466330", "ce87d0a5-73e3-4444-868b-28d096fc45c5", "4606d78a-1b01-4b90-ae17-1b92c174c09c", "9d60c6d6-975e-486a-90fd-6593468f21eb", "b3b3838a-3070-43b9-9d2d-13310ab8d289", "56d358ba-e4eb-4192-80ca-33d139ed9421", "5e8765dd-f207-445f-bca7-b4832c7ed638", "dacdb74e-d945-4c22-86ef-5e9dcbbebea0"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Capstone Logistics", "Geometry": {"BoundingBox": {"Width": 0.3194742798805237, "Height": 0.02801007777452469, "Left": 0.06310328096151352, "Top": 0.011501245200634003}, "Polygon": [{"X": 0.06310328096151352, "Y": 0.011501245200634003}, {"X": 0.3825775682926178, "Y": 0.011538994498550892}, {"X": 0.38257652521133423, "Y": 0.03951132297515869}, {"X": 0.06310354173183441, "Y": 0.039473757147789}]}, "Id": "a76d3e8d-e292-46a0-b360-f26d3e311c24", "Relationships": [{"Type": "CHILD", "Ids": ["12d511ae-e4d7-4bfa-84c4-74d4a0ff6b84", "89e24c73-3664-437c-9fc0-3dd425c2392f"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "30 TECHNOLOGY PKWY SOUTH SUITE 200", "Geometry": {"BoundingBox": {"Width": 0.3306022882461548, "Height": 0.009208844974637032, "Left": 0.05751122906804085, "Top": 0.0573071613907814}, "Polygon": [{"X": 0.05751122906804085, "Y": 0.0573071613907814}, {"X": 0.38811349868774414, "Y": 0.057345911860466}, {"X": 0.3881131410598755, "Y": 0.06651600450277328}, {"X": 0.05751132220029831, "Y": 0.06647732108831406}]}, "Id": "a83eab6f-2820-45a2-9e54-f28576ccc91d", "Relationships": [{"Type": "CHILD", "Ids": ["0d5e0bcd-dc18-44fa-8df3-dd14993517da", "ad09eb8a-6c7a-4d08-bba8-3b91f1b51ced", "29312bf6-9055-4a2b-9b46-54f25f8e509d", "44621d48-afb9-4f43-bb63-a8ce908f4ca2", "f97d27a1-2e5f-4c91-8ff7-0f7bad394e4e", "177023eb-7cd3-4e04-b96c-0230d2b65d9f"]}]}, {"BlockType": "LINE", "Confidence": 99.9281997680664, "Text": "PEACHTREE CORNERS, GA 30092", "Geometry": {"BoundingBox": {"Width": 0.2575463652610779, "Height": 0.010532092303037643, "Left": 0.09364710003137589, "Top": 0.07109962403774261}, "Polygon": [{"X": 0.09364710003137589, "Y": 0.07109962403774261}, {"X": 0.35119345784187317, "Y": 0.07112973928451538}, {"X": 0.3511931300163269, "Y": 0.08163171261548996}, {"X": 0.09364715218544006, "Y": 0.08160165697336197}]}, "Id": "c262708a-d0c5-4622-9dfd-4b49968cf83f", "Relationships": [{"Type": "CHILD", "Ids": ["ad005e4a-03ba-4894-b6f7-4b7bc3413fe8", "421fde1c-d336-43c5-8c06-61c6127c4e75", "ff3f9dd0-092b-409b-afd6-0b34c983da67", "9f62f2ef-83cd-413a-a5bd-daae9e6588c2"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "770-414-1929", "Geometry": {"BoundingBox": {"Width": 0.094415083527565, "Height": 0.009030125103890896, "Left": 0.17539450526237488, "Top": 0.08480369299650192}, "Polygon": [{"X": 0.17539456486701965, "Y": 0.08480369299650192}, {"X": 0.2698096036911011, "Y": 0.08481470495462418}, {"X": 0.26980939507484436, "Y": 0.0938338190317154}, {"X": 0.17539450526237488, "Y": 0.09382282197475433}]}, "Id": "0b2eaf16-3bf5-4933-bcce-54923e7c9261", "Relationships": [{"Type": "CHILD", "Ids": ["0a4f4131-9867-4b82-97a3-81c4b1b25fa0"]}]}, {"BlockType": "LINE", "Confidence": 99.619140625, "Text": "FED ID# 45-3087555", "Geometry": {"BoundingBox": {"Width": 0.14693959057331085, "Height": 0.009184326976537704, "Left": 0.14906303584575653, "Top": 0.09834501892328262}, "Polygon": [{"X": 0.14906306564807892, "Y": 0.09834501892328262}, {"X": 0.2960026264190674, "Y": 0.09836211800575256}, {"X": 0.2960023880004883, "Y": 0.10752934217453003}, {"X": 0.14906303584575653, "Y": 0.10751227289438248}]}, "Id": "61478c08-77b5-46db-b3e0-bb5d48366f99", "Relationships": [{"Type": "CHILD", "Ids": ["acd7ceb4-68d3-4457-8abf-6309d0eb104f", "b5dce1e8-3c47-4fe7-aa2e-b5cecea7faed", "da56d70b-30a8-4621-b9c9-3ea8f899fdb3"]}]}, {"BlockType": "LINE", "Confidence": 99.736328125, "Text": "09:31:23 September 19, 2025", "Geometry": {"BoundingBox": {"Width": 0.2963535189628601, "Height": 0.017013734206557274, "Left": 0.0740216076374054, "Top": 0.12794938683509827}, "Polygon": [{"X": 0.0740216076374054, "Y": 0.12794938683509827}, {"X": 0.3703751266002655, "Y": 0.1279837042093277}, {"X": 0.37037453055381775, "Y": 0.1449631303548813}, {"X": 0.07402174174785614, "Y": 0.1449289321899414}]}, "Id": "db46570b-35b6-4008-ad53-777872099e00", "Relationships": [{"Type": "CHILD", "Ids": ["2ed16e31-79fd-4fa9-9ae2-75aefa725c9f", "5f6acdfa-1c16-45d7-99de-1f58e8197021", "0253c3ae-2364-4522-8020-d99fe3<PERSON><PERSON>da", "104904d2-0020-431b-b519-21ab8bf452bb"]}]}, {"BlockType": "LINE", "Confidence": 99.55078125, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.06806518137454987, "Height": 0.011615058407187462, "Left": 0.008174428716301918, "Top": 0.1623573750257492}, "Polygon": [{"X": 0.008174428716301918, "Y": 0.1623573750257492}, {"X": 0.07623952627182007, "Y": 0.16236521303653717}, {"X": 0.07623960822820663, "Y": 0.17397242784500122}, {"X": 0.008174629881978035, "Y": 0.17396461963653564}]}, "Id": "67de5cce-137b-4ee5-adc4-2f300dac192d", "Relationships": [{"Type": "CHILD", "Ids": ["6161fb5a-2b6f-4790-94d9-490917620910", "4577cf68-f10f-481c-885f-cca56e93bc95"]}]}, {"BlockType": "LINE", "Confidence": 99.41426086425781, "Text": "e38dd18a-43b4-", "Geometry": {"BoundingBox": {"Width": 0.10960433632135391, "Height": 0.009240793995559216, "Left": 0.21879450976848602, "Top": 0.16252686083316803}, "Polygon": [{"X": 0.21879464387893677, "Y": 0.16252686083316803}, {"X": 0.32839885354042053, "Y": 0.16253946721553802}, {"X": 0.32839858531951904, "Y": 0.17176765203475952}, {"X": 0.21879450976848602, "Y": 0.17175506055355072}]}, "Id": "2441f9e8-b2b7-4e33-8325-63c3d198bbe0", "Relationships": [{"Type": "CHILD", "Ids": ["0c5ed32d-c5ed-4f2a-af95-27985f8bc581"]}]}, {"BlockType": "LINE", "Confidence": 99.13365173339844, "Text": "4ab6-9afa-0638fb6236a1", "Geometry": {"BoundingBox": {"Width": 0.1697077602148056, "Height": 0.009227524511516094, "Left": 0.21791617572307587, "Top": 0.17583253979682922}, "Polygon": [{"X": 0.21791629493236542, "Y": 0.17583253979682922}, {"X": 0.38762393593788147, "Y": 0.17585201561450958}, {"X": 0.3876235783100128, "Y": 0.185060054063797}, {"X": 0.21791617572307587, "Y": 0.18504060804843903}]}, "Id": "30f601ce-ee1f-41a8-807b-e28c09b7da09", "Relationships": [{"Type": "CHILD", "Ids": ["0a1fb7db-1533-41a2-bed3-ea0e0f6f7e3f"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Location:", "Geometry": {"BoundingBox": {"Width": 0.06374981999397278, "Height": 0.009074743837118149, "Left": 0.008191775530576706, "Top": 0.1980976164340973}, "Polygon": [{"X": 0.008191775530576706, "Y": 0.1980976164340973}, {"X": 0.07194152474403381, "Y": 0.19810490310192108}, {"X": 0.07194159179925919, "Y": 0.20717234909534454}, {"X": 0.008191932924091816, "Y": 0.20716507732868195}]}, "Id": "0b9d319b-026b-478f-aee3-ea00f6255026", "Relationships": [{"Type": "CHILD", "Ids": ["7123427a-1465-46f4-b4da-aafa4a153de2"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "US FOODS MANASSAS VA", "Geometry": {"BoundingBox": {"Width": 0.20578333735466003, "Height": 0.008916574530303478, "Left": 0.2180284559726715, "Top": 0.1982361376285553}, "Polygon": [{"X": 0.21802857518196106, "Y": 0.1982361376285553}, {"X": 0.42381179332733154, "Y": 0.1982596516609192}, {"X": 0.4238114058971405, "Y": 0.20715270936489105}, {"X": 0.2180284559726715, "Y": 0.20712922513484955}]}, "Id": "790a9d93-4f6a-4c5e-a5f1-1df66d68c1ec", "Relationships": [{"Type": "CHILD", "Ids": ["cbf1b308-57f9-46cd-9991-57fc11bf9519", "5009e3d6-343d-4bc7-84b7-4dd94b573096", "f18b2c18-fe03-4b6a-9045-3602ed818acc", "4348d5eb-005d-435f-a6a2-53df4648706f"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "50", "Geometry": {"BoundingBox": {"Width": 0.01978478394448757, "Height": 0.008830396458506584, "Left": 0.21882697939872742, "Top": 0.211736798286438}, "Polygon": [{"X": 0.21882709860801697, "Y": 0.211736798286438}, {"X": 0.23861175775527954, "Y": 0.21173904836177826}, {"X": 0.2386116087436676, "Y": 0.22056719660758972}, {"X": 0.21882697939872742, "Y": 0.22056493163108826}]}, "Id": "a05f2b0a-bf0d-4c6d-93d3-b19b000489a1", "Relationships": [{"Type": "CHILD", "Ids": ["4e321f39-04c2-4972-9237-e4e0179c0f36"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "Work Date:", "Geometry": {"BoundingBox": {"Width": 0.07791928946971893, "Height": 0.008914979174733162, "Left": 0.00807191152125597, "Top": 0.23380158841609955}, "Polygon": [{"X": 0.00807191152125597, "Y": 0.23380158841609955}, {"X": 0.08599114418029785, "Y": 0.2338104397058487}, {"X": 0.08599119633436203, "Y": 0.24271656572818756}, {"X": 0.008072065189480782, "Y": 0.2427077293395996}]}, "Id": "4f4b32ed-73ec-422c-833c-c40604d9ddb1", "Relationships": [{"Type": "CHILD", "Ids": ["4faa2c68-cec0-4bae-a4dd-e29ea4f25edb", "87801a37-7456-48a9-82cd-cd2b23db54e6"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "2025-09-19", "Geometry": {"BoundingBox": {"Width": 0.078591488301754, "Height": 0.009237575344741344, "Left": 0.21837042272090912, "Top": 0.23373211920261383}, "Polygon": [{"X": 0.21837054193019867, "Y": 0.23373211920261383}, {"X": 0.2969619035720825, "Y": 0.23374104499816895}, {"X": 0.2969616651535034, "Y": 0.24296969175338745}, {"X": 0.21837042272090912, "Y": 0.24296079576015472}]}, "Id": "371ffce7-d552-4882-ac79-7f4a9cb6a2d8", "Relationships": [{"Type": "CHILD", "Ids": ["57a4726d-5e88-4bce-b07a-cc6d35dafa78"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Bill Code:", "Geometry": {"BoundingBox": {"Width": 0.06849740445613861, "Height": 0.00888383761048317, "Left": 0.008549812249839306, "Top": 0.2514825463294983}, "Polygon": [{"X": 0.008549812249839306, "Y": 0.2514825463294983}, {"X": 0.07704715430736542, "Y": 0.2514902949333191}, {"X": 0.07704722136259079, "Y": 0.26036638021469116}, {"X": 0.008549965918064117, "Y": 0.26035863161087036}]}, "Id": "89913de2-756d-4417-bac4-9c12bd6aab59", "Relationships": [{"Type": "CHILD", "Ids": ["b6e6dfad-a82d-4beb-ae67-fe0e969583a7", "e6a07d25-145f-4ab0-8539-5f32672a2bdb"]}]}, {"BlockType": "LINE", "Confidence": 99.0832290649414, "Text": "RCOD62049", "Geometry": {"BoundingBox": {"Width": 0.08832843601703644, "Height": 0.00907201785594225, "Left": 0.2185012251138687, "Top": 0.25153690576553345}, "Polygon": [{"X": 0.21850135922431946, "Y": 0.25153690576553345}, {"X": 0.30682966113090515, "Y": 0.2515469193458557}, {"X": 0.30682942271232605, "Y": 0.2606089413166046}, {"X": 0.2185012251138687, "Y": 0.26059895753860474}]}, "Id": "bf3e8e9d-af55-49c3-a4bf-c2f0e32a4735", "Relationships": [{"Type": "CHILD", "Ids": ["49dd35d8-dabc-40f9-8393-8780da82e467"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.05111446976661682, "Height": 0.008814351633191109, "Left": 0.008738297037780285, "Top": 0.2695052921772003}, "Polygon": [{"X": 0.008738297037780285, "Y": 0.2695052921772003}, {"X": 0.059852682054042816, "Y": 0.26951107382774353}, {"X": 0.05985276773571968, "Y": 0.27831965684890747}, {"X": 0.008738448843359947, "Y": 0.27831390500068665}]}, "Id": "ab18deb9-4a2b-4d51-8910-e5ebe608e774", "Relationships": [{"Type": "CHILD", "Ids": ["2d69b0ff-0dc3-45a3-9116-88f7c580a652"]}]}, {"BlockType": "LINE", "Confidence": 99.80917358398438, "Text": "BLUE STAR", "Geometry": {"BoundingBox": {"Width": 0.09144453704357147, "Height": 0.008854497224092484, "Left": 0.21823392808437347, "Top": 0.26949313282966614}, "Polygon": [{"X": 0.21823404729366302, "Y": 0.26949313282966614}, {"X": 0.30967846512794495, "Y": 0.2695034444332123}, {"X": 0.30967822670936584, "Y": 0.2783476412296295}, {"X": 0.21823392808437347, "Y": 0.2783373296260834}]}, "Id": "2842936f-3b23-4543-a676-7ad1b7bc2617", "Relationships": [{"Type": "CHILD", "Ids": ["c83f2d81-4af2-4656-a26a-4e53dbbc32bf", "dcacbdd8-87b1-4567-a90f-9c0205360329"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock:", "Geometry": {"BoundingBox": {"Width": 0.040206316858530045, "Height": 0.008975755423307419, "Left": 0.00850470457226038, "Top": 0.2870841920375824}, "Polygon": [{"X": 0.00850470457226038, "Y": 0.2870841920375824}, {"X": 0.04871091991662979, "Y": 0.2870887219905853}, {"X": 0.04871102049946785, "Y": 0.2960599362850189}, {"X": 0.008504859171807766, "Y": 0.2960554361343384}]}, "Id": "bf93a16a-2780-4c33-aa87-9939200c4e8c", "Relationships": [{"Type": "CHILD", "Ids": ["610a3cf6-2654-4d04-b68b-85b8ea78d493"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRY", "Geometry": {"BoundingBox": {"Width": 0.03505349159240723, "Height": 0.008740147575736046, "Left": 0.21857313811779022, "Top": 0.2872932255268097}, "Polygon": [{"X": 0.21857325732707977, "Y": 0.2872932255268097}, {"X": 0.25362661480903625, "Y": 0.28729715943336487}, {"X": 0.2536264657974243, "Y": 0.2960333824157715}, {"X": 0.21857313811779022, "Y": 0.2960294485092163}]}, "Id": "bdb1fbe5-8dfe-4b04-b5ff-c6cbb6886936", "Relationships": [{"Type": "CHILD", "Ids": ["19bf9145-391e-446c-af37-c1e19f505175"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.038239676505327225, "Height": 0.008668327704071999, "Left": 0.008474435657262802, "Top": 0.30529356002807617}, "Polygon": [{"X": 0.008474435657262802, "Y": 0.30529356002807617}, {"X": 0.04671401157975197, "Y": 0.3052978515625}, {"X": 0.04671411216259003, "Y": 0.3139618933200836}, {"X": 0.008474585600197315, "Y": 0.3139576315879822}]}, "Id": "6a4d2e18-b2b0-4d74-94c7-bf6265ec3208", "Relationships": [{"Type": "CHILD", "Ids": ["60e248cd-b233-4570-8422-5fdd91a05e11"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.007984012365341187, "Height": 0.008589389733970165, "Left": 0.21841123700141907, "Top": 0.30527740716934204}, "Polygon": [{"X": 0.21841135621070862, "Y": 0.30527740716934204}, {"X": 0.22639524936676025, "Y": 0.30527830123901367}, {"X": 0.2263951301574707, "Y": 0.3138667941093445}, {"X": 0.21841123700141907, "Y": 0.31386590003967285}]}, "Id": "ec750c8b-9176-48ed-9d0e-1e686a2466e2", "Relationships": [{"Type": "CHILD", "Ids": ["187c7ae6-0c96-4e0f-b240-02aac8f5c2d8"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "Purchase Orders", "Geometry": {"BoundingBox": {"Width": 0.11058663576841354, "Height": 0.008967917412519455, "Left": 0.008243284188210964, "Top": 0.32984480261802673}, "Polygon": [{"X": 0.008243284188210964, "Y": 0.32984480261802673}, {"X": 0.11882990598678589, "Y": 0.3298571705818176}, {"X": 0.11882991343736649, "Y": 0.3388127386569977}, {"X": 0.00824343878775835, "Y": 0.33880043029785156}]}, "Id": "d154dbad-3213-43de-a375-4949c9953708", "Relationships": [{"Type": "CHILD", "Ids": ["00e6ad27-f08f-4b19-ac12-469e1d4954eb", "a47a4785-53f7-4602-86ce-9e694e22d630"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.050862159579992294, "Height": 0.00894931796938181, "Left": 0.21848100423812866, "Top": 0.3298982083797455}, "Polygon": [{"X": 0.2184811234474182, "Y": 0.3298982083797455}, {"X": 0.26934316754341125, "Y": 0.32990390062332153}, {"X": 0.26934298872947693, "Y": 0.3388475477695465}, {"X": 0.21848100423812866, "Y": 0.33884185552597046}]}, "Id": "c46453b7-f5bb-472c-9bcd-f7d3d3039848", "Relationships": [{"Type": "CHILD", "Ids": ["10f42ed4-36da-477f-81d7-7dbba7f272c6"]}]}, {"BlockType": "LINE", "Confidence": 97.05038452148438, "Text": "71084250", "Geometry": {"BoundingBox": {"Width": 0.07003629207611084, "Height": 0.009112236090004444, "Left": 0.008559885434806347, "Top": 0.34777602553367615}, "Polygon": [{"X": 0.008559885434806347, "Y": 0.34777602553367615}, {"X": 0.07859611511230469, "Y": 0.3477838337421417}, {"X": 0.07859618216753006, "Y": 0.3568882644176483}, {"X": 0.008560041896998882, "Y": 0.3568804860115051}]}, "Id": "6d9b7867-d303-4e3c-8fc4-2bed3f2d613a", "Relationships": [{"Type": "CHILD", "Ids": ["01b31149-1398-4dab-bb44-cfdb02ab5de4"]}]}, {"BlockType": "LINE", "Confidence": 61.906288146972656, "Text": "AMERCAREROYAL-QDOBA", "Geometry": {"BoundingBox": {"Width": 0.21617792546749115, "Height": 0.011168766766786575, "Left": 0.21842798590660095, "Top": 0.3478051424026489}, "Polygon": [{"X": 0.2184281349182129, "Y": 0.3478051424026489}, {"X": 0.4346058964729309, "Y": 0.3478291928768158}, {"X": 0.4346053898334503, "Y": 0.3589739203453064}, {"X": 0.21842798590660095, "Y": 0.3589499294757843}]}, "Id": "26a73a03-d9c3-4d99-ad86-af7cad7a4b66", "Relationships": [{"Type": "CHILD", "Ids": ["5718bff1-a62a-4b1d-8dc3-26368253ce2f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Initial Pallets:", "Geometry": {"BoundingBox": {"Width": 0.13245904445648193, "Height": 0.008905753493309021, "Left": 0.008380639366805553, "Top": 0.3902607560157776}, "Polygon": [{"X": 0.008380639366805553, "Y": 0.3902607560157776}, {"X": 0.14083968102931976, "Y": 0.39027538895606995}, {"X": 0.14083965122699738, "Y": 0.3991665244102478}, {"X": 0.008380793035030365, "Y": 0.39915192127227783}]}, "Id": "d9ff2889-2bb4-460a-a46c-e1905d67b3a3", "Relationships": [{"Type": "CHILD", "Ids": ["c1f2245d-3e68-4250-90f0-5c52389c520e", "fc89567e-2036-45f3-9960-6d48c638c958", "0c43aa5c-0cc0-4d30-8693-9ce652ca39af"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "10.00", "Geometry": {"BoundingBox": {"Width": 0.03614473342895508, "Height": 0.008885457180440426, "Left": 0.21998809278011322, "Top": 0.39042699337005615}, "Polygon": [{"X": 0.21998821198940277, "Y": 0.39042699337005615}, {"X": 0.2561328113079071, "Y": 0.3904309868812561}, {"X": 0.25613266229629517, "Y": 0.39931246638298035}, {"X": 0.21998809278011322, "Y": 0.3993084728717804}]}, "Id": "421dc6de-8ea7-42aa-a529-55af8987fc01", "Relationships": [{"Type": "CHILD", "Ids": ["49d8f368-6469-4cc8-b000-a2438ea8406d"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Total Finished Pallets:", "Geometry": {"BoundingBox": {"Width": 0.15040448307991028, "Height": 0.00916322786360979, "Left": 0.008372697047889233, "Top": 0.40815576910972595}, "Polygon": [{"X": 0.008372697047889233, "Y": 0.40815576910972595}, {"X": 0.15877719223499298, "Y": 0.4081723093986511}, {"X": 0.1587771475315094, "Y": 0.41731899976730347}, {"X": 0.008372855372726917, "Y": 0.4173024892807007}]}, "Id": "bf85255e-7393-4d4d-ae01-58465aa52294", "Relationships": [{"Type": "CHILD", "Ids": ["83a7202c-3d1a-41f2-bc72-474c8217181c", "342f9f09-00f6-40ec-bc83-8be67d36f16f", "08fc31e4-5433-41cb-98f6-c1ba6a9bbd0a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "15", "Geometry": {"BoundingBox": {"Width": 0.014720850624144077, "Height": 0.008790640160441399, "Left": 0.22003307938575745, "Top": 0.40836530923843384}, "Polygon": [{"X": 0.220033198595047, "Y": 0.40836530923843384}, {"X": 0.23475393652915955, "Y": 0.4083669185638428}, {"X": 0.2347537875175476, "Y": 0.417155921459198}, {"X": 0.22003307938575745, "Y": 0.41715431213378906}]}, "Id": "27895314-4b93-4a5a-9423-c456ea92bfa3", "Relationships": [{"Type": "CHILD", "Ids": ["65d2a6cd-fd25-4694-be5d-e53425516bcb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Case Count:", "Geometry": {"BoundingBox": {"Width": 0.12136751413345337, "Height": 0.008736229501664639, "Left": 0.008366741240024567, "Top": 0.4260273873806}, "Polygon": [{"X": 0.008366741240024567, "Y": 0.4260273873806}, {"X": 0.12973424792289734, "Y": 0.4260406792163849}, {"X": 0.12973424792289734, "Y": 0.4347636103630066}, {"X": 0.008366892114281654, "Y": 0.43475034832954407}]}, "Id": "23be0e87-4f6a-46b8-88f5-9a4b92789635", "Relationships": [{"Type": "CHILD", "Ids": ["46058779-5f30-40f7-bad2-ce284d92f305", "efd1ab05-20f6-45e3-b098-eef6895ce570", "4c950f8f-25f9-4eb7-9a7a-01cf4ba44c10"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "303", "Geometry": {"BoundingBox": {"Width": 0.024199621751904488, "Height": 0.009009895846247673, "Left": 0.21846845746040344, "Top": 0.42603620886802673}, "Polygon": [{"X": 0.218468576669693, "Y": 0.42603620886802673}, {"X": 0.24266807734966278, "Y": 0.42603886127471924}, {"X": 0.24266792833805084, "Y": 0.43504610657691956}, {"X": 0.21846845746040344, "Y": 0.43504345417022705}]}, "Id": "70144070-e4ba-496d-9e85-3d2c9694584f", "Relationships": [{"Type": "CHILD", "Ids": ["b5cc68f1-875d-4cf9-93b3-cef5702ca896"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Total Weight:", "Geometry": {"BoundingBox": {"Width": 0.09273430705070496, "Height": 0.011482630856335163, "Left": 0.008352678269147873, "Top": 0.44379928708076477}, "Polygon": [{"X": 0.008352678269147873, "Y": 0.44379928708076477}, {"X": 0.10108694434165955, "Y": 0.4438094198703766}, {"X": 0.10108698904514313, "Y": 0.45528191328048706}, {"X": 0.008352876640856266, "Y": 0.45527181029319763}]}, "Id": "43f8fc12-db0b-4208-9a92-28ebb5e13194", "Relationships": [{"Type": "CHILD", "Ids": ["b39e50b9-da36-4d52-b99f-ccdf69338423", "27d71974-1cba-4b27-955c-7ffb20c59368"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "7161.00", "Geometry": {"BoundingBox": {"Width": 0.054514240473508835, "Height": 0.00910905096679926, "Left": 0.21866899728775024, "Top": 0.44386035203933716}, "Polygon": [{"X": 0.2186691164970398, "Y": 0.44386035203933716}, {"X": 0.27318325638771057, "Y": 0.4438663125038147}, {"X": 0.27318304777145386, "Y": 0.45296940207481384}, {"X": 0.21866899728775024, "Y": 0.4529634714126587}]}, "Id": "1d5f7150-847e-4f11-a20a-d007bef19405", "Relationships": [{"Type": "CHILD", "Ids": ["448fd46a-3f50-44ce-95ae-98be8630a64e"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Trailer Number:", "Geometry": {"BoundingBox": {"Width": 0.1089404821395874, "Height": 0.009055140428245068, "Left": 0.0082924235612154, "Top": 0.4615468680858612}, "Polygon": [{"X": 0.0082924235612154, "Y": 0.4615468680858612}, {"X": 0.11723289638757706, "Y": 0.4615587294101715}, {"X": 0.11723291128873825, "Y": 0.47060200572013855}, {"X": 0.008292580023407936, "Y": 0.47059014439582825}]}, "Id": "cbf25d33-99aa-4b54-9035-0ecd2e537f60", "Relationships": [{"Type": "CHILD", "Ids": ["b66d703a-c4e8-4427-845a-6616eecf3ec8", "16385d8b-9683-490a-87ac-34b412b44034"]}]}, {"BlockType": "LINE", "Confidence": 99.4190444946289, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004833530634641647, "Height": 0.008928668685257435, "Left": 0.22011533379554749, "Top": 0.4616663455963135}, "Polygon": [{"X": 0.22011545300483704, "Y": 0.4616663455963135}, {"X": 0.22494886815547943, "Y": 0.46166688203811646}, {"X": 0.2249487340450287, "Y": 0.47059503197669983}, {"X": 0.22011533379554749, "Y": 0.47059449553489685}]}, "Id": "ed5cd09e-ae86-42e5-8bbd-749a13554f3b", "Relationships": [{"Type": "CHILD", "Ids": ["e983381a-db3f-4119-a704-097b964b1207"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Tractor Number:", "Geometry": {"BoundingBox": {"Width": 0.11242939531803131, "Height": 0.009044358506798744, "Left": 0.008451895788311958, "Top": 0.47938525676727295}, "Polygon": [{"X": 0.008451895788311958, "Y": 0.47938525676727295}, {"X": 0.12088128179311752, "Y": 0.4793974757194519}, {"X": 0.12088128924369812, "Y": 0.48842963576316833}, {"X": 0.008452051319181919, "Y": 0.48841744661331177}]}, "Id": "71a9d4c1-788e-4dd8-bad3-a5070a84e8be", "Relationships": [{"Type": "CHILD", "Ids": ["b37ec81a-01a7-489c-a21b-65e7ec38cad4", "c2731534-66bb-4636-b387-b4d86b1dbcdd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "BOL:", "Geometry": {"BoundingBox": {"Width": 0.0372314378619194, "Height": 0.008752837777137756, "Left": 0.008500535041093826, "Top": 0.49740836024284363}, "Polygon": [{"X": 0.008500535041093826, "Y": 0.49740836024284363}, {"X": 0.04573187232017517, "Y": 0.49741241335868835}, {"X": 0.04573197290301323, "Y": 0.5061612129211426}, {"X": 0.008500685915350914, "Y": 0.5061572194099426}]}, "Id": "a6606813-c5c2-4044-af22-90e0d7e69071", "Relationships": [{"Type": "CHILD", "Ids": ["8da312d3-8f87-454d-b2ff-bb88a5f6a5ff"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Comments:", "Geometry": {"BoundingBox": {"Width": 0.07643184810876846, "Height": 0.00879090279340744, "Left": 0.008480163291096687, "Top": 0.5153142809867859}, "Polygon": [{"X": 0.008480163291096687, "Y": 0.5153142809867859}, {"X": 0.08491195738315582, "Y": 0.5153225064277649}, {"X": 0.08491200953722, "Y": 0.5241051912307739}, {"X": 0.008480314165353775, "Y": 0.5240969657897949}]}, "Id": "73c1cfca-93bf-46bd-9b47-93559daf98c3", "Relationships": [{"Type": "CHILD", "Ids": ["d469cdd1-0a81-412f-8c88-43b1a049ab85"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Canned Comments:", "Geometry": {"BoundingBox": {"Width": 0.131972998380661, "Height": 0.009069186635315418, "Left": 0.008612160570919514, "Top": 0.5327337384223938}, "Polygon": [{"X": 0.008612160570919514, "Y": 0.5327337384223938}, {"X": 0.14058516919612885, "Y": 0.5327479243278503}, {"X": 0.14058513939380646, "Y": 0.5418029427528381}, {"X": 0.008612316101789474, "Y": 0.5417887568473816}]}, "Id": "2932509c-cdb4-4fd1-8d02-112a9fff390b", "Relationships": [{"Type": "CHILD", "Ids": ["5d452c64-9653-4c56-88b6-e1bb4275a61c", "b9ab832a-83b5-4a12-8ebe-0d800ffa1a2a"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Unloaders:", "Geometry": {"BoundingBox": {"Width": 0.07239977270364761, "Height": 0.0089563624933362, "Left": 0.008574532344937325, "Top": 0.5506824254989624}, "Polygon": [{"X": 0.008574532344937325, "Y": 0.5506824254989624}, {"X": 0.08097424358129501, "Y": 0.5506901741027832}, {"X": 0.08097430318593979, "Y": 0.5596387982368469}, {"X": 0.008574686013162136, "Y": 0.5596310496330261}]}, "Id": "22b5886c-d2ae-40bb-98bc-e3b452c9d558", "Relationships": [{"Type": "CHILD", "Ids": ["49482aee-7ed9-4ed5-ab97-93c3e2735ecf"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.007885992527008057, "Height": 0.008779648691415787, "Left": 0.21846283972263336, "Top": 0.5508977174758911}, "Polygon": [{"X": 0.2184629589319229, "Y": 0.5508977174758911}, {"X": 0.22634883224964142, "Y": 0.550898551940918}, {"X": 0.22634869813919067, "Y": 0.5596773624420166}, {"X": 0.21846283972263336, "Y": 0.5596765279769897}]}, "Id": "658e558a-4330-41ef-bc4e-584c107216f7", "Relationships": [{"Type": "CHILD", "Ids": ["5c489910-62b6-409b-8921-e73de0e430de"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Base Charge:", "Geometry": {"BoundingBox": {"Width": 0.08906261622905731, "Height": 0.011331669054925442, "Left": 0.00844611506909132, "Top": 0.5755212306976318}, "Polygon": [{"X": 0.00844611506909132, "Y": 0.5755212306976318}, {"X": 0.09750868380069733, "Y": 0.5755307078361511}, {"X": 0.0975087359547615, "Y": 0.5868528485298157}, {"X": 0.00844631064683199, "Y": 0.5868434309959412}]}, "Id": "79ae8226-0df5-47d0-9553-49c39f821582", "Relationships": [{"Type": "CHILD", "Ids": ["973062f2-d075-4644-aec6-3ec7c0e8e18f", "ef041235-a50b-42a2-a02e-879298423251"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "180.00", "Geometry": {"BoundingBox": {"Width": 0.04470408335328102, "Height": 0.008928242139518261, "Left": 0.21997465193271637, "Top": 0.5756847858428955}, "Polygon": [{"X": 0.21997477114200592, "Y": 0.5756847858428955}, {"X": 0.2646787464618683, "Y": 0.5756895542144775}, {"X": 0.2646785378456116, "Y": 0.584613025188446}, {"X": 0.21997465193271637, "Y": 0.584608256816864}]}, "Id": "b552b85e-eff9-4765-9189-6315b661ac88", "Relationships": [{"Type": "CHILD", "Ids": ["0e5ebd05-9850-4b47-8ae8-65c7630a55b0"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Convenience Fee:", "Geometry": {"BoundingBox": {"Width": 0.11968250572681427, "Height": 0.008898916654288769, "Left": 0.008678133599460125, "Top": 0.5935677289962769}, "Polygon": [{"X": 0.008678133599460125, "Y": 0.5935677289962769}, {"X": 0.12836064398288727, "Y": 0.5935804843902588}, {"X": 0.12836064398288727, "Y": 0.6024667024612427}, {"X": 0.008678286336362362, "Y": 0.6024540066719055}]}, "Id": "0f26e6c0-48e8-4625-a7c7-d63fb8201988", "Relationships": [{"Type": "CHILD", "Ids": ["9b783468-f4f2-4d3d-a9be-8332c4d09428", "8b24c22b-8045-418f-94f4-c60497993b2d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "10.00", "Geometry": {"BoundingBox": {"Width": 0.03617306053638458, "Height": 0.008964275941252708, "Left": 0.2198769450187683, "Top": 0.5935900807380676}, "Polygon": [{"X": 0.21987706422805786, "Y": 0.5935900807380676}, {"X": 0.2560499906539917, "Y": 0.593593955039978}, {"X": 0.25604984164237976, "Y": 0.6025543808937073}, {"X": 0.2198769450187683, "Y": 0.6025505065917969}]}, "Id": "f6d1c275-28f5-4913-a991-8923f08596b2", "Relationships": [{"Type": "CHILD", "Ids": ["9fdb6301-3647-485f-bc9a-6543493d17ad"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Total Cost:", "Geometry": {"BoundingBox": {"Width": 0.07423391938209534, "Height": 0.008875059895217419, "Left": 0.008374905213713646, "Top": 0.6112791299819946}, "Polygon": [{"X": 0.008374905213713646, "Y": 0.6112791299819946}, {"X": 0.08260876685380936, "Y": 0.611286997795105}, {"X": 0.08260882645845413, "Y": 0.6201542019844055}, {"X": 0.008375057950615883, "Y": 0.6201463341712952}]}, "Id": "e8115103-7a0a-4e18-84fb-1149d5dd03b4", "Relationships": [{"Type": "CHILD", "Ids": ["32962711-166f-4840-a6b9-acbe8245b2a0", "1e255cd0-c9e6-4ed9-9c37-7c52cca83e27"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "190.00", "Geometry": {"BoundingBox": {"Width": 0.04444485157728195, "Height": 0.008918522857129574, "Left": 0.22012856602668762, "Top": 0.611238420009613}, "Polygon": [{"X": 0.22012868523597717, "Y": 0.611238420009613}, {"X": 0.26457342505455017, "Y": 0.6112431287765503}, {"X": 0.26457324624061584, "Y": 0.6201569437980652}, {"X": 0.22012856602668762, "Y": 0.6201522350311279}]}, "Id": "49b38e7a-40b2-408f-bc86-ce464f466330", "Relationships": [{"Type": "CHILD", "Ids": ["de0c25da-bc57-4cc1-89ad-3a6967a4e296"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payments:", "Geometry": {"BoundingBox": {"Width": 0.06891808658838272, "Height": 0.011261017993092537, "Left": 0.008325915783643723, "Top": 0.6364960074424744}, "Polygon": [{"X": 0.008325915783643723, "Y": 0.6364960074424744}, {"X": 0.07724392414093018, "Y": 0.636503279209137}, {"X": 0.07724400609731674, "Y": 0.6477570533752441}, {"X": 0.008326110430061817, "Y": 0.6477497816085815}]}, "Id": "ce87d0a5-73e3-4444-868b-28d096fc45c5", "Relationships": [{"Type": "CHILD", "Ids": ["8b1ccb22-8537-4ca9-a237-da3461141587"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.055809542536735535, "Height": 0.008606204763054848, "Left": 0.21831992268562317, "Top": 0.636658251285553}, "Polygon": [{"X": 0.21832004189491272, "Y": 0.636658251285553}, {"X": 0.2741294801235199, "Y": 0.636664092540741}, {"X": 0.2741292715072632, "Y": 0.6452644467353821}, {"X": 0.21831992268562317, "Y": 0.6452585458755493}]}, "Id": "4606d78a-1b01-4b90-ae17-1b92c174c09c", "Relationships": [{"Type": "CHILD", "Ids": ["643b61f0-d2e2-4aa8-8735-b95db868fcdc"]}]}, {"BlockType": "LINE", "Confidence": 98.17701721191406, "Text": "CapstonePay-", "Geometry": {"BoundingBox": {"Width": 0.09224124252796173, "Height": 0.011310219764709473, "Left": 0.04248572885990143, "Top": 0.6539745926856995}, "Polygon": [{"X": 0.04248572885990143, "Y": 0.6539745926856995}, {"X": 0.13472697138786316, "Y": 0.6539842486381531}, {"X": 0.13472695648670197, "Y": 0.6652848124504089}, {"X": 0.04248586669564247, "Y": 0.6652751564979553}]}, "Id": "9d60c6d6-975e-486a-90fd-6593468f21eb", "Relationships": [{"Type": "CHILD", "Ids": ["28ac7791-791d-4749-b3dd-40d3a7f1e95e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$190.00", "Geometry": {"BoundingBox": {"Width": 0.05447690933942795, "Height": 0.010041066445410252, "Left": 0.2184952050447464, "Top": 0.653295636177063}, "Polygon": [{"X": 0.21849533915519714, "Y": 0.653295636177063}, {"X": 0.27297210693359375, "Y": 0.6533013582229614}, {"X": 0.27297189831733704, "Y": 0.6633366942405701}, {"X": 0.2184952050447464, "Y": 0.6633310317993164}]}, "Id": "b3b3838a-3070-43b9-9d2d-13310ab8d289", "Relationships": [{"Type": "CHILD", "Ids": ["77e255a8-2fc2-46d6-912f-56d53f118bec"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "38024319", "Geometry": {"BoundingBox": {"Width": 0.06663750112056732, "Height": 0.009148575365543365, "Left": 0.0424657016992569, "Top": 0.6676889061927795}, "Polygon": [{"X": 0.0424657016992569, "Y": 0.6676889061927795}, {"X": 0.10910318046808243, "Y": 0.6676958799362183}, {"X": 0.10910320281982422, "Y": 0.6768375039100647}, {"X": 0.04246581345796585, "Y": 0.676830530166626}]}, "Id": "56d358ba-e4eb-4192-80ca-33d139ed9421", "Relationships": [{"Type": "CHILD", "Ids": ["edae444f-be26-438d-be06-ad4d500ac9c7"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "Total Payments", "Geometry": {"BoundingBox": {"Width": 0.10474986582994461, "Height": 0.011314996518194675, "Left": 0.04202926158905029, "Top": 0.6895958185195923}, "Polygon": [{"X": 0.04202926158905029, "Y": 0.6895958185195923}, {"X": 0.1467791348695755, "Y": 0.6896067261695862}, {"X": 0.14677909016609192, "Y": 0.7009108662605286}, {"X": 0.042029403150081635, "Y": 0.7008999586105347}]}, "Id": "5e8765dd-f207-445f-bca7-b4832c7ed638", "Relationships": [{"Type": "CHILD", "Ids": ["d40c21e0-0231-4778-90e8-4b2f598fb15c", "4cf20e68-5e1d-4ee8-81d8-b5a7e2b9df91"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$190.00", "Geometry": {"BoundingBox": {"Width": 0.05450601130723953, "Height": 0.009886737912893295, "Left": 0.21849341690540314, "Top": 0.6891739964485168}, "Polygon": [{"X": 0.21849355101585388, "Y": 0.6891739964485168}, {"X": 0.27299943566322327, "Y": 0.6891796588897705}, {"X": 0.27299922704696655, "Y": 0.6990606784820557}, {"X": 0.21849341690540314, "Y": 0.699055016040802}]}, "Id": "dacdb74e-d945-4c22-86ef-5e9dcbbebea0", "Relationships": [{"Type": "CHILD", "Ids": ["3ef19f81-4dc5-4d73-a7fa-efa762c68108"]}]}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Capstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15512344241142273, "Height": 0.027559420093894005, "Left": 0.06310328841209412, "Top": 0.011932577937841415}, "Polygon": [{"X": 0.06310328841209412, "Y": 0.011932577937841415}, {"X": 0.21822673082351685, "Y": 0.011950905434787273}, {"X": 0.218226358294487, "Y": 0.03949199616909027}, {"X": 0.06310354173183441, "Y": 0.039473757147789}]}, "Id": "12d511ae-e4d7-4bfa-84c4-74d4a0ff6b84"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15382856130599976, "Height": 0.027928682044148445, "Left": 0.22874902188777924, "Top": 0.011520817875862122}, "Polygon": [{"X": 0.22874943912029266, "Y": 0.011520817875862122}, {"X": 0.3825775682926178, "Y": 0.011538994498550892}, {"X": 0.38257652521133423, "Y": 0.03944949805736542}, {"X": 0.22874902188777924, "Y": 0.039431411772966385}]}, "Id": "89e24c73-3664-437c-9fc0-3dd425c2392f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016253221780061722, "Height": 0.009141773916780949, "Left": 0.05751122906804085, "Top": 0.0573374442756176}, "Polygon": [{"X": 0.05751122906804085, "Y": 0.0573374442756176}, {"X": 0.0737643837928772, "Y": 0.05733935162425041}, {"X": 0.07376445084810257, "Y": 0.06647922098636627}, {"X": 0.05751132220029831, "Y": 0.06647732108831406}]}, "Id": "0d5e0bcd-dc18-44fa-8df3-dd14993517da"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TECHNOLOGY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11458708345890045, "Height": 0.008923345245420933, "Left": 0.07809645682573318, "Top": 0.05742882192134857}, "Polygon": [{"X": 0.07809645682573318, "Y": 0.05742882192134857}, {"X": 0.19268354773521423, "Y": 0.057442255318164825}, {"X": 0.19268345832824707, "Y": 0.06635216623544693}, {"X": 0.07809651643037796, "Y": 0.06633876264095306}]}, "Id": "ad09eb8a-6c7a-4d08-bba8-3b91f1b51ced"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PKWY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04921044409275055, "Height": 0.009014305658638477, "Left": 0.19706103205680847, "Top": 0.057361308485269547}, "Polygon": [{"X": 0.19706112146377563, "Y": 0.057361308485269547}, {"X": 0.24627147614955902, "Y": 0.057367078959941864}, {"X": 0.2462713122367859, "Y": 0.06637561321258545}, {"X": 0.19706103205680847, "Y": 0.06636985391378403}]}, "Id": "29312bf6-9055-4a2b-9b46-54f25f8e509d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SOUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055525876581668854, "Height": 0.009104645811021328, "Left": 0.2511160373687744, "Top": 0.05737372115254402}, "Polygon": [{"X": 0.25111621618270874, "Y": 0.05737372115254402}, {"X": 0.30664193630218506, "Y": 0.057380229234695435}, {"X": 0.30664169788360596, "Y": 0.06647836416959763}, {"X": 0.2511160373687744, "Y": 0.06647186726331711}]}, "Id": "44621d48-afb9-4f43-bb63-a8ce908f4ca2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "SUITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04706122726202011, "Height": 0.009002028033137321, "Left": 0.31154733896255493, "Top": 0.05741901695728302}, "Polygon": [{"X": 0.3115476071834564, "Y": 0.05741901695728302}, {"X": 0.35860857367515564, "Y": 0.05742453411221504}, {"X": 0.35860827565193176, "Y": 0.06642104685306549}, {"X": 0.31154733896255493, "Y": 0.06641554087400436}]}, "Id": "f97d27a1-2e5f-4c91-8ff7-0f7bad394e4e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02504229173064232, "Height": 0.009038753807544708, "Left": 0.3630712032318115, "Top": 0.057342976331710815}, "Polygon": [{"X": 0.3630715310573578, "Y": 0.057342976331710815}, {"X": 0.38811349868774414, "Y": 0.057345911860466}, {"X": 0.3881131708621979, "Y": 0.06638173013925552}, {"X": 0.3630712032318115, "Y": 0.06637880206108093}]}, "Id": "177023eb-7cd3-4e04-b96c-0230d2b65d9f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "PEACHTREE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09714403748512268, "Height": 0.008741478435695171, "Left": 0.09364710003137589, "Top": 0.07121335715055466}, "Polygon": [{"X": 0.09364710003137589, "Y": 0.07121335715055466}, {"X": 0.19079114496707916, "Y": 0.07122471928596497}, {"X": 0.190791055560112, "Y": 0.0799548402428627}, {"X": 0.09364714473485947, "Y": 0.07994349300861359}]}, "Id": "ad005e4a-03ba-4894-b6f7-4b7bc3413fe8"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "CORNERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08093267679214478, "Height": 0.010420192964375019, "Left": 0.19552883505821228, "Top": 0.07120279967784882}, "Polygon": [{"X": 0.19552893936634064, "Y": 0.07120279967784882}, {"X": 0.27646151185035706, "Y": 0.07121226191520691}, {"X": 0.27646127343177795, "Y": 0.08162299543619156}, {"X": 0.19552883505821228, "Y": 0.08161354809999466}]}, "Id": "421fde1c-d336-43c5-8c06-61c6127c4e75"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024071456864476204, "Height": 0.008745650760829449, "Left": 0.2812844216823578, "Top": 0.07123178243637085}, "Polygon": [{"X": 0.2812846302986145, "Y": 0.07123178243637085}, {"X": 0.30535587668418884, "Y": 0.07123459875583649}, {"X": 0.30535566806793213, "Y": 0.07997743040323257}, {"X": 0.2812844216823578, "Y": 0.07997462153434753}]}, "Id": "ff3f9dd0-092b-409b-afd6-0b34c983da67"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "30092", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04149414226412773, "Height": 0.008845467120409012, "Left": 0.30969932675361633, "Top": 0.07112488150596619}, "Polygon": [{"X": 0.30969956517219543, "Y": 0.07112488150596619}, {"X": 0.35119345784187317, "Y": 0.07112973928451538}, {"X": 0.3511931896209717, "Y": 0.0799703523516655}, {"X": 0.30969932675361633, "Y": 0.0799655094742775}]}, "Id": "9f62f2ef-83cd-413a-a5bd-daae9e6588c2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "770-414-1929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.094415083527565, "Height": 0.009030125103890896, "Left": 0.17539450526237488, "Top": 0.08480369299650192}, "Polygon": [{"X": 0.17539456486701965, "Y": 0.08480369299650192}, {"X": 0.2698096036911011, "Y": 0.08481470495462418}, {"X": 0.26980939507484436, "Y": 0.0938338190317154}, {"X": 0.17539450526237488, "Y": 0.09382282197475433}]}, "Id": "0a4f4131-9867-4b82-97a3-81c4b1b25fa0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031186193227767944, "Height": 0.00878516398370266, "Left": 0.14906303584575653, "Top": 0.09850068390369415}, "Polygon": [{"X": 0.14906306564807892, "Y": 0.09850068390369415}, {"X": 0.18024922907352448, "Y": 0.09850431233644485}, {"X": 0.1802491545677185, "Y": 0.10728584975004196}, {"X": 0.14906303584575653, "Y": 0.10728222876787186}]}, "Id": "acd7ceb4-68d3-4457-8abf-6309d0eb104f"}, {"BlockType": "WORD", "Confidence": 99.111328125, "Text": "ID#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02616603672504425, "Height": 0.008870158344507217, "Left": 0.1851455271244049, "Top": 0.09841547906398773}, "Polygon": [{"X": 0.18514560163021088, "Y": 0.09841547906398773}, {"X": 0.21131156384944916, "Y": 0.09841852635145187}, {"X": 0.2113114446401596, "Y": 0.10728564113378525}, {"X": 0.1851455271244049, "Y": 0.1072826012969017}]}, "Id": "b5dce1e8-3c47-4fe7-aa2e-b5cecea7faed"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "45-3087555", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08107366412878036, "Height": 0.009176662191748619, "Left": 0.21492895483970642, "Top": 0.09835267812013626}, "Polygon": [{"X": 0.21492907404899597, "Y": 0.09835267812013626}, {"X": 0.2960026264190674, "Y": 0.09836211800575256}, {"X": 0.2960023880004883, "Y": 0.10752934217453003}, {"X": 0.21492895483970642, "Y": 0.10751992464065552}]}, "Id": "da56d70b-30a8-4621-b9c9-3ea8f899fdb3"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "09:31:23", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08756210654973984, "Height": 0.013169980607926846, "Left": 0.0740216076374054, "Top": 0.12806935608386993}, "Polygon": [{"X": 0.0740216076374054, "Y": 0.12806935608386993}, {"X": 0.16158372163772583, "Y": 0.12807948887348175}, {"X": 0.16158364713191986, "Y": 0.14123934507369995}, {"X": 0.07402171194553375, "Y": 0.14122922718524933}]}, "Id": "2ed16e31-79fd-4fa9-9ae2-75aefa725c9f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "September", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10780927538871765, "Height": 0.01699194125831127, "Left": 0.1703570932149887, "Top": 0.12796054780483246}, "Polygon": [{"X": 0.17035719752311707, "Y": 0.12796054780483246}, {"X": 0.27816635370254517, "Y": 0.1279730200767517}, {"X": 0.2781659960746765, "Y": 0.14495249092578888}, {"X": 0.1703570932149887, "Y": 0.14494004845619202}]}, "Id": "5f6acdfa-1c16-45d7-99de-1f58e8197021"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "19,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028456704691052437, "Height": 0.01582697033882141, "Left": 0.28574076294898987, "Top": 0.12805771827697754}, "Polygon": [{"X": 0.2857411503791809, "Y": 0.12805771827697754}, {"X": 0.31419748067855835, "Y": 0.12806101143360138}, {"X": 0.31419703364372253, "Y": 0.14388468861579895}, {"X": 0.28574076294898987, "Y": 0.1438813954591751}]}, "Id": "0253c3ae-2364-4522-8020-d99fe3<PERSON><PERSON>da"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048771489411592484, "Height": 0.012715727090835571, "Left": 0.3216036260128021, "Top": 0.1283423900604248}, "Polygon": [{"X": 0.3216039836406708, "Y": 0.1283423900604248}, {"X": 0.3703751266002655, "Y": 0.12834803760051727}, {"X": 0.3703746497631073, "Y": 0.14105811715126038}, {"X": 0.3216036260128021, "Y": 0.1410524845123291}]}, "Id": "104904d2-0020-431b-b519-21ab8bf452bb"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05186503380537033, "Height": 0.011613198556005955, "Left": 0.008174428716301918, "Top": 0.1623573750257492}, "Polygon": [{"X": 0.008174428716301918, "Y": 0.1623573750257492}, {"X": 0.060039352625608444, "Y": 0.16236335039138794}, {"X": 0.0600394643843174, "Y": 0.17397058010101318}, {"X": 0.008174629881978035, "Y": 0.17396461963653564}]}, "Id": "6161fb5a-2b6f-4790-94d9-490917620910"}, {"BlockType": "WORD", "Confidence": 99.140625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012516963295638561, "Height": 0.008901355788111687, "Left": 0.0637226328253746, "Top": 0.16265276074409485}, "Polygon": [{"X": 0.0637226328253746, "Y": 0.16265276074409485}, {"X": 0.07623952627182007, "Y": 0.16265420615673065}, {"X": 0.07623959332704544, "Y": 0.17155411839485168}, {"X": 0.06372271478176117, "Y": 0.17155267298221588}]}, "Id": "4577cf68-f10f-481c-885f-cca56e93bc95"}, {"BlockType": "WORD", "Confidence": 99.41426086425781, "Text": "e38dd18a-43b4-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10960433632135391, "Height": 0.009240793995559216, "Left": 0.21879450976848602, "Top": 0.16252686083316803}, "Polygon": [{"X": 0.21879464387893677, "Y": 0.16252686083316803}, {"X": 0.32839885354042053, "Y": 0.16253946721553802}, {"X": 0.32839858531951904, "Y": 0.17176765203475952}, {"X": 0.21879450976848602, "Y": 0.17175506055355072}]}, "Id": "0c5ed32d-c5ed-4f2a-af95-27985f8bc581"}, {"BlockType": "WORD", "Confidence": 99.13365173339844, "Text": "4ab6-9afa-0638fb6236a1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1697077602148056, "Height": 0.009227524511516094, "Left": 0.21791617572307587, "Top": 0.17583253979682922}, "Polygon": [{"X": 0.21791629493236542, "Y": 0.17583253979682922}, {"X": 0.38762393593788147, "Y": 0.17585201561450958}, {"X": 0.3876235783100128, "Y": 0.185060054063797}, {"X": 0.21791617572307587, "Y": 0.18504060804843903}]}, "Id": "0a1fb7db-1533-41a2-bed3-ea0e0f6f7e3f"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06374981999397278, "Height": 0.009074743837118149, "Left": 0.008191775530576706, "Top": 0.1980976164340973}, "Polygon": [{"X": 0.008191775530576706, "Y": 0.1980976164340973}, {"X": 0.07194152474403381, "Y": 0.19810490310192108}, {"X": 0.07194159179925919, "Y": 0.20717234909534454}, {"X": 0.008191932924091816, "Y": 0.20716507732868195}]}, "Id": "7123427a-1465-46f4-b4da-aafa4a153de2"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "US", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021508675068616867, "Height": 0.008879872038960457, "Left": 0.2180284559726715, "Top": 0.19825179874897003}, "Polygon": [{"X": 0.21802857518196106, "Y": 0.19825179874897003}, {"X": 0.23953713476657867, "Y": 0.19825425744056702}, {"X": 0.23953698575496674, "Y": 0.20713168382644653}, {"X": 0.2180284559726715, "Y": 0.20712922513484955}]}, "Id": "cbf1b308-57f9-46cd-9991-57fc11bf9519"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FOODS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05487559363245964, "Height": 0.008740336634218693, "Left": 0.2439509630203247, "Top": 0.19831599295139313}, "Polygon": [{"X": 0.24395111203193665, "Y": 0.19831599295139313}, {"X": 0.29882654547691345, "Y": 0.19832226634025574}, {"X": 0.29882633686065674, "Y": 0.20705632865428925}, {"X": 0.2439509630203247, "Y": 0.20705007016658783}]}, "Id": "5009e3d6-343d-4bc7-84b7-4dd94b573096"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "MANASSAS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09123145043849945, "Height": 0.008851459249854088, "Left": 0.30312681198120117, "Top": 0.1982458531856537}, "Polygon": [{"X": 0.3031270503997803, "Y": 0.1982458531856537}, {"X": 0.3943582773208618, "Y": 0.19825628399848938}, {"X": 0.39435791969299316, "Y": 0.20709732174873352}, {"X": 0.30312681198120117, "Y": 0.20708690583705902}]}, "Id": "f18b2c18-fe03-4b6a-9045-3602ed818acc"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "VA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024972878396511078, "Height": 0.008778704330325127, "Left": 0.39883890748023987, "Top": 0.19830065965652466}, "Polygon": [{"X": 0.3988392651081085, "Y": 0.19830065965652466}, {"X": 0.42381179332733154, "Y": 0.19830352067947388}, {"X": 0.4238114058971405, "Y": 0.20707936584949493}, {"X": 0.39883890748023987, "Y": 0.2070765197277069}]}, "Id": "4348d5eb-005d-435f-a6a2-53df4648706f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "50", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01978478394448757, "Height": 0.008830396458506584, "Left": 0.21882697939872742, "Top": 0.211736798286438}, "Polygon": [{"X": 0.21882709860801697, "Y": 0.211736798286438}, {"X": 0.23861175775527954, "Y": 0.21173904836177826}, {"X": 0.2386116087436676, "Y": 0.22056719660758972}, {"X": 0.21882697939872742, "Y": 0.22056493163108826}]}, "Id": "4e321f39-04c2-4972-9237-e4e0179c0f36"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03903741389513016, "Height": 0.00890678446739912, "Left": 0.00807191152125597, "Top": 0.23380158841609955}, "Polygon": [{"X": 0.00807191152125597, "Y": 0.23380158841609955}, {"X": 0.047109220176935196, "Y": 0.23380601406097412}, {"X": 0.04710932448506355, "Y": 0.24270837008953094}, {"X": 0.008072065189480782, "Y": 0.24270394444465637}]}, "Id": "4faa2c68-cec0-4bae-a4dd-e29ea4f25edb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035177960991859436, "Height": 0.008862169459462166, "Left": 0.05081323906779289, "Top": 0.23385439813137054}, "Polygon": [{"X": 0.05081323906779289, "Y": 0.23385439813137054}, {"X": 0.08599114418029785, "Y": 0.2338583916425705}, {"X": 0.08599119633436203, "Y": 0.24271656572818756}, {"X": 0.05081333592534065, "Y": 0.2427125722169876}]}, "Id": "87801a37-7456-48a9-82cd-cd2b23db54e6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2025-09-19", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.078591488301754, "Height": 0.009237575344741344, "Left": 0.21837042272090912, "Top": 0.23373211920261383}, "Polygon": [{"X": 0.21837054193019867, "Y": 0.23373211920261383}, {"X": 0.2969619035720825, "Y": 0.23374104499816895}, {"X": 0.2969616651535034, "Y": 0.24296969175338745}, {"X": 0.21837042272090912, "Y": 0.24296079576015472}]}, "Id": "57a4726d-5e88-4bce-b07a-cc6d35dafa78"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024732960388064384, "Height": 0.008878891356289387, "Left": 0.008549812249839306, "Top": 0.2514825463294983}, "Polygon": [{"X": 0.008549812249839306, "Y": 0.2514825463294983}, {"X": 0.033282652497291565, "Y": 0.25148534774780273}, {"X": 0.033282771706581116, "Y": 0.2603614330291748}, {"X": 0.008549965918064117, "Y": 0.26035863161087036}]}, "Id": "b6e6dfad-a82d-4beb-ae67-fe0e969583a7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03897375613451004, "Height": 0.008829507045447826, "Left": 0.03807346150279045, "Top": 0.25149479508399963}, "Polygon": [{"X": 0.03807346150279045, "Y": 0.25149479508399963}, {"X": 0.07704715430736542, "Y": 0.251499205827713}, {"X": 0.07704721391201019, "Y": 0.26032429933547974}, {"X": 0.038073573261499405, "Y": 0.26031988859176636}]}, "Id": "e6a07d25-145f-4ab0-8539-5f32672a2bdb"}, {"BlockType": "WORD", "Confidence": 99.0832290649414, "Text": "RCOD62049", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08832843601703644, "Height": 0.00907201785594225, "Left": 0.2185012251138687, "Top": 0.25153690576553345}, "Polygon": [{"X": 0.21850135922431946, "Y": 0.25153690576553345}, {"X": 0.30682966113090515, "Y": 0.2515469193458557}, {"X": 0.30682942271232605, "Y": 0.2606089413166046}, {"X": 0.2185012251138687, "Y": 0.26059895753860474}]}, "Id": "49dd35d8-dabc-40f9-8393-8780da82e467"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05111446976661682, "Height": 0.008814351633191109, "Left": 0.008738297037780285, "Top": 0.2695052921772003}, "Polygon": [{"X": 0.008738297037780285, "Y": 0.2695052921772003}, {"X": 0.059852682054042816, "Y": 0.26951107382774353}, {"X": 0.05985276773571968, "Y": 0.27831965684890747}, {"X": 0.008738448843359947, "Y": 0.27831390500068665}]}, "Id": "2d69b0ff-0dc3-45a3-9116-88f7c580a652"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "BLUE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0436357818543911, "Height": 0.008849112316966057, "Left": 0.21823392808437347, "Top": 0.26949313282966614}, "Polygon": [{"X": 0.21823404729366302, "Y": 0.26949313282966614}, {"X": 0.2618696987628937, "Y": 0.2694980502128601}, {"X": 0.26186951994895935, "Y": 0.27834224700927734}, {"X": 0.21823392808437347, "Y": 0.2783373296260834}]}, "Id": "c83f2d81-4af2-4656-a26a-4e53dbbc32bf"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "STAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04254882037639618, "Height": 0.008706418797373772, "Left": 0.2671296298503876, "Top": 0.26955506205558777}, "Polygon": [{"X": 0.2671298086643219, "Y": 0.26955506205558777}, {"X": 0.30967846512794495, "Y": 0.2695598602294922}, {"X": 0.30967822670936584, "Y": 0.2782614827156067}, {"X": 0.2671296298503876, "Y": 0.27825668454170227}]}, "Id": "dcacbdd8-87b1-4567-a90f-9c0205360329"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040206316858530045, "Height": 0.008975755423307419, "Left": 0.00850470457226038, "Top": 0.2870841920375824}, "Polygon": [{"X": 0.00850470457226038, "Y": 0.2870841920375824}, {"X": 0.04871091991662979, "Y": 0.2870887219905853}, {"X": 0.04871102049946785, "Y": 0.2960599362850189}, {"X": 0.008504859171807766, "Y": 0.2960554361343384}]}, "Id": "610a3cf6-2654-4d04-b68b-85b8ea78d493"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03505349159240723, "Height": 0.008740147575736046, "Left": 0.21857313811779022, "Top": 0.2872932255268097}, "Polygon": [{"X": 0.21857325732707977, "Y": 0.2872932255268097}, {"X": 0.25362661480903625, "Y": 0.28729715943336487}, {"X": 0.2536264657974243, "Y": 0.2960333824157715}, {"X": 0.21857313811779022, "Y": 0.2960294485092163}]}, "Id": "19bf9145-391e-446c-af37-c1e19f505175"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038239676505327225, "Height": 0.008668327704071999, "Left": 0.008474435657262802, "Top": 0.30529356002807617}, "Polygon": [{"X": 0.008474435657262802, "Y": 0.30529356002807617}, {"X": 0.04671401157975197, "Y": 0.3052978515625}, {"X": 0.04671411216259003, "Y": 0.3139618933200836}, {"X": 0.008474585600197315, "Y": 0.3139576315879822}]}, "Id": "60e248cd-b233-4570-8422-5fdd91a05e11"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007984012365341187, "Height": 0.008589389733970165, "Left": 0.21841123700141907, "Top": 0.30527740716934204}, "Polygon": [{"X": 0.21841135621070862, "Y": 0.30527740716934204}, {"X": 0.22639524936676025, "Y": 0.30527830123901367}, {"X": 0.2263951301574707, "Y": 0.3138667941093445}, {"X": 0.21841123700141907, "Y": 0.31386590003967285}]}, "Id": "187c7ae6-0c96-4e0f-b240-02aac8f5c2d8"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06074930727481842, "Height": 0.008962365798652172, "Left": 0.008243284188210964, "Top": 0.32984480261802673}, "Polygon": [{"X": 0.008243284188210964, "Y": 0.32984480261802673}, {"X": 0.06899251788854599, "Y": 0.32985159754753113}, {"X": 0.06899259239435196, "Y": 0.33880719542503357}, {"X": 0.00824343878775835, "Y": 0.33880043029785156}]}, "Id": "00e6ad27-f08f-4b19-ac12-469e1d4954eb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Orders", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04546928033232689, "Height": 0.008793910965323448, "Left": 0.0733606368303299, "Top": 0.32997527718544006}, "Polygon": [{"X": 0.0733606368303299, "Y": 0.32997527718544006}, {"X": 0.11882990598678589, "Y": 0.32998034358024597}, {"X": 0.11882991343736649, "Y": 0.33876916766166687}, {"X": 0.07336070388555527, "Y": 0.33876410126686096}]}, "Id": "a47a4785-53f7-4602-86ce-9e694e22d630"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050862159579992294, "Height": 0.00894931796938181, "Left": 0.21848100423812866, "Top": 0.3298982083797455}, "Polygon": [{"X": 0.2184811234474182, "Y": 0.3298982083797455}, {"X": 0.26934316754341125, "Y": 0.32990390062332153}, {"X": 0.26934298872947693, "Y": 0.3388475477695465}, {"X": 0.21848100423812866, "Y": 0.33884185552597046}]}, "Id": "10f42ed4-36da-477f-81d7-7dbba7f272c6"}, {"BlockType": "WORD", "Confidence": 97.05038452148438, "Text": "71084250", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07003629207611084, "Height": 0.009112236090004444, "Left": 0.008559885434806347, "Top": 0.34777602553367615}, "Polygon": [{"X": 0.008559885434806347, "Y": 0.34777602553367615}, {"X": 0.07859611511230469, "Y": 0.3477838337421417}, {"X": 0.07859618216753006, "Y": 0.3568882644176483}, {"X": 0.008560041896998882, "Y": 0.3568804860115051}]}, "Id": "01b31149-1398-4dab-bb44-cfdb02ab5de4"}, {"BlockType": "WORD", "Confidence": 61.906288146972656, "Text": "AMERCAREROYAL-QDOBA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21617792546749115, "Height": 0.011168766766786575, "Left": 0.21842798590660095, "Top": 0.3478051424026489}, "Polygon": [{"X": 0.2184281349182129, "Y": 0.3478051424026489}, {"X": 0.4346058964729309, "Y": 0.3478291928768158}, {"X": 0.4346053898334503, "Y": 0.3589739203453064}, {"X": 0.21842798590660095, "Y": 0.3589499294757843}]}, "Id": "5718bff1-a62a-4b1d-8dc3-26368253ce2f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035192567855119705, "Height": 0.008767543360590935, "Left": 0.008380640298128128, "Top": 0.3903328478336334}, "Polygon": [{"X": 0.008380640298128128, "Y": 0.3903328478336334}, {"X": 0.043573103845119476, "Y": 0.3903367519378662}, {"X": 0.04357320815324783, "Y": 0.3991003930568695}, {"X": 0.00838079210370779, "Y": 0.3990965187549591}]}, "Id": "c1f2245d-3e68-4250-90f0-5c52389c520e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Initial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03966712951660156, "Height": 0.008827094919979572, "Left": 0.04816026985645294, "Top": 0.3903261721134186}, "Polygon": [{"X": 0.04816026985645294, "Y": 0.3903261721134186}, {"X": 0.08782734721899033, "Y": 0.39033055305480957}, {"X": 0.0878273993730545, "Y": 0.3991532623767853}, {"X": 0.048160370439291, "Y": 0.3991489112377167}]}, "Id": "fc89567e-2036-45f3-9960-6d48c638c958"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04849182814359665, "Height": 0.008896488696336746, "Left": 0.09234784543514252, "Top": 0.39027002453804016}, "Polygon": [{"X": 0.09234784543514252, "Y": 0.39027002453804016}, {"X": 0.14083968102931976, "Y": 0.39027538895606995}, {"X": 0.14083965122699738, "Y": 0.3991665244102478}, {"X": 0.0923478901386261, "Y": 0.3991611897945404}]}, "Id": "0c43aa5c-0cc0-4d30-8693-9ce652ca39af"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03614473342895508, "Height": 0.008885457180440426, "Left": 0.21998809278011322, "Top": 0.39042699337005615}, "Polygon": [{"X": 0.21998821198940277, "Y": 0.39042699337005615}, {"X": 0.2561328113079071, "Y": 0.3904309868812561}, {"X": 0.25613266229629517, "Y": 0.39931246638298035}, {"X": 0.21998809278011322, "Y": 0.3993084728717804}]}, "Id": "49d8f368-6469-4cc8-b000-a2438ea8406d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035188499838113785, "Height": 0.009079626761376858, "Left": 0.008372697047889233, "Top": 0.40815576910972595}, "Polygon": [{"X": 0.008372697047889233, "Y": 0.40815576910972595}, {"X": 0.04356108605861664, "Y": 0.40815964341163635}, {"X": 0.04356119781732559, "Y": 0.417235404253006}, {"X": 0.008372854441404343, "Y": 0.41723155975341797}]}, "Id": "83a7202c-3d1a-41f2-bc72-474c8217181c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Finished", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05820626765489578, "Height": 0.00908230897039175, "Left": 0.04795689508318901, "Top": 0.4082309305667877}, "Polygon": [{"X": 0.04795689508318901, "Y": 0.4082309305667877}, {"X": 0.1061631366610527, "Y": 0.4082373380661011}, {"X": 0.1061631590127945, "Y": 0.41731324791908264}, {"X": 0.04795699939131737, "Y": 0.4173068404197693}]}, "Id": "342f9f09-00f6-40ec-bc83-8be67d36f16f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048744428902864456, "Height": 0.008814342319965363, "Left": 0.11003275215625763, "Top": 0.4082871377468109}, "Polygon": [{"X": 0.11003275215625763, "Y": 0.4082871377468109}, {"X": 0.1587771773338318, "Y": 0.4082925021648407}, {"X": 0.1587771475315094, "Y": 0.4171014726161957}, {"X": 0.11003277450799942, "Y": 0.4170961081981659}]}, "Id": "08fc31e4-5433-41cb-98f6-c1ba6a9bbd0a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014720850624144077, "Height": 0.008790640160441399, "Left": 0.22003307938575745, "Top": 0.40836530923843384}, "Polygon": [{"X": 0.220033198595047, "Y": 0.40836530923843384}, {"X": 0.23475393652915955, "Y": 0.4083669185638428}, {"X": 0.2347537875175476, "Y": 0.417155921459198}, {"X": 0.22003307938575745, "Y": 0.41715431213378906}]}, "Id": "65d2a6cd-fd25-4694-be5d-e53425516bcb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03507193550467491, "Height": 0.008718564175069332, "Left": 0.008366741240024567, "Top": 0.4260273873806}, "Polygon": [{"X": 0.008366741240024567, "Y": 0.4260273873806}, {"X": 0.04343856871128082, "Y": 0.426031231880188}, {"X": 0.04343867674469948, "Y": 0.4347459375858307}, {"X": 0.008366892114281654, "Y": 0.43474212288856506}]}, "Id": "46058779-5f30-40f7-bad2-ce284d92f305"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032408952713012695, "Height": 0.008633635006844997, "Left": 0.04821991175413132, "Top": 0.42612460255622864}, "Polygon": [{"X": 0.04821991175413132, "Y": 0.42612460255622864}, {"X": 0.08062880486249924, "Y": 0.4261281490325928}, {"X": 0.08062886446714401, "Y": 0.4347582459449768}, {"X": 0.04822000861167908, "Y": 0.43475469946861267}]}, "Id": "efd1ab05-20f6-45e3-b098-eef6895ce570"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Count:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0446159765124321, "Height": 0.008562750183045864, "Left": 0.08511827886104584, "Top": 0.426148384809494}, "Polygon": [{"X": 0.08511827886104584, "Y": 0.426148384809494}, {"X": 0.12973424792289734, "Y": 0.4261532723903656}, {"X": 0.12973424792289734, "Y": 0.43471112847328186}, {"X": 0.08511833101511002, "Y": 0.43470627069473267}]}, "Id": "4c950f8f-25f9-4eb7-9a7a-01cf4ba44c10"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "303", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024199621751904488, "Height": 0.009009895846247673, "Left": 0.21846845746040344, "Top": 0.42603620886802673}, "Polygon": [{"X": 0.218468576669693, "Y": 0.42603620886802673}, {"X": 0.24266807734966278, "Y": 0.42603886127471924}, {"X": 0.24266792833805084, "Y": 0.43504610657691956}, {"X": 0.21846845746040344, "Y": 0.43504345417022705}]}, "Id": "b5cc68f1-875d-4cf9-93b3-cef5702ca896"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03526851162314415, "Height": 0.008842886425554752, "Left": 0.008352680131793022, "Top": 0.4438890814781189}, "Polygon": [{"X": 0.008352680131793022, "Y": 0.4438890814781189}, {"X": 0.043621085584163666, "Y": 0.4438929259777069}, {"X": 0.04362119361758232, "Y": 0.4527319669723511}, {"X": 0.008352832868695259, "Y": 0.45272812247276306}]}, "Id": "b39e50b9-da36-4d52-b99f-ccdf69338423"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05330681800842285, "Height": 0.011478324420750141, "Left": 0.04778016731142998, "Top": 0.4438035786151886}, "Polygon": [{"X": 0.04778016731142998, "Y": 0.4438035786151886}, {"X": 0.10108694434165955, "Y": 0.4438094198703766}, {"X": 0.10108698904514313, "Y": 0.45528191328048706}, {"X": 0.04778030142188072, "Y": 0.45527610182762146}]}, "Id": "27d71974-1cba-4b27-955c-7ffb20c59368"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "7161.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054514240473508835, "Height": 0.00910905096679926, "Left": 0.21866899728775024, "Top": 0.44386035203933716}, "Polygon": [{"X": 0.2186691164970398, "Y": 0.44386035203933716}, {"X": 0.27318325638771057, "Y": 0.4438663125038147}, {"X": 0.27318304777145386, "Y": 0.45296940207481384}, {"X": 0.21866899728775024, "Y": 0.4529634714126587}]}, "Id": "448fd46a-3f50-44ce-95ae-98be8630a64e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04641878232359886, "Height": 0.008874813094735146, "Left": 0.0082924235612154, "Top": 0.4615468680858612}, "Polygon": [{"X": 0.0082924235612154, "Y": 0.4615468680858612}, {"X": 0.05471111461520195, "Y": 0.4615519046783447}, {"X": 0.05471120402216911, "Y": 0.4704216718673706}, {"X": 0.008292577229440212, "Y": 0.4704166352748871}]}, "Id": "b66d703a-c4e8-4427-845a-6616eecf3ec8"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05915113538503647, "Height": 0.008997289463877678, "Left": 0.058081772178411484, "Top": 0.4616047143936157}, "Polygon": [{"X": 0.058081772178411484, "Y": 0.4616047143936157}, {"X": 0.11723289638757706, "Y": 0.46161115169525146}, {"X": 0.11723291128873825, "Y": 0.47060200572013855}, {"X": 0.05808186158537865, "Y": 0.4705955684185028}]}, "Id": "16385d8b-9683-490a-87ac-34b412b44034"}, {"BlockType": "WORD", "Confidence": 99.4190444946289, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004833530634641647, "Height": 0.008928668685257435, "Left": 0.22011533379554749, "Top": 0.4616663455963135}, "Polygon": [{"X": 0.22011545300483704, "Y": 0.4616663455963135}, {"X": 0.22494886815547943, "Y": 0.46166688203811646}, {"X": 0.2249487340450287, "Y": 0.47059503197669983}, {"X": 0.22011533379554749, "Y": 0.47059449553489685}]}, "Id": "e983381a-db3f-4119-a704-097b964b1207"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04984522610902786, "Height": 0.00870195496827364, "Left": 0.008451900444924831, "Top": 0.4796923100948334}, "Polygon": [{"X": 0.008451900444924831, "Y": 0.4796923100948334}, {"X": 0.058297038078308105, "Y": 0.47969773411750793}, {"X": 0.05829712748527527, "Y": 0.48839426040649414}, {"X": 0.008452050387859344, "Y": 0.48838886618614197}]}, "Id": "b37ec81a-01a7-489c-a21b-65e7ec38cad4"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05915381759405136, "Height": 0.009038577787578106, "Left": 0.06172747164964676, "Top": 0.47939103841781616}, "Polygon": [{"X": 0.06172747164964676, "Y": 0.47939103841781616}, {"X": 0.12088128179311752, "Y": 0.4793974757194519}, {"X": 0.12088128924369812, "Y": 0.48842963576316833}, {"X": 0.061727557331323624, "Y": 0.488423228263855}]}, "Id": "c2731534-66bb-4636-b387-b4d86b1dbcdd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BOL:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0372314378619194, "Height": 0.008752837777137756, "Left": 0.008500535041093826, "Top": 0.49740836024284363}, "Polygon": [{"X": 0.008500535041093826, "Y": 0.49740836024284363}, {"X": 0.04573187232017517, "Y": 0.49741241335868835}, {"X": 0.04573197290301323, "Y": 0.5061612129211426}, {"X": 0.008500685915350914, "Y": 0.5061572194099426}]}, "Id": "8da312d3-8f87-454d-b2ff-bb88a5f6a5ff"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07643184810876846, "Height": 0.00879090279340744, "Left": 0.008480163291096687, "Top": 0.5153142809867859}, "Polygon": [{"X": 0.008480163291096687, "Y": 0.5153142809867859}, {"X": 0.08491195738315582, "Y": 0.5153225064277649}, {"X": 0.08491200953722, "Y": 0.5241051912307739}, {"X": 0.008480314165353775, "Y": 0.5240969657897949}]}, "Id": "d469cdd1-0a81-412f-8c88-43b1a049ab85"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Canned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051312029361724854, "Height": 0.00901059340685606, "Left": 0.008612160570919514, "Top": 0.5327337384223938}, "Polygon": [{"X": 0.008612160570919514, "Y": 0.5327337384223938}, {"X": 0.05992410331964493, "Y": 0.5327392220497131}, {"X": 0.05992418900132179, "Y": 0.5417443513870239}, {"X": 0.0086123151704669, "Y": 0.5417388081550598}]}, "Id": "5d452c64-9653-4c56-88b6-e1bb4275a61c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0762975737452507, "Height": 0.008814414031803608, "Left": 0.06428758800029755, "Top": 0.5329884886741638}, "Polygon": [{"X": 0.06428758800029755, "Y": 0.5329884886741638}, {"X": 0.14058515429496765, "Y": 0.5329967141151428}, {"X": 0.14058513939380646, "Y": 0.5418029427528381}, {"X": 0.06428766250610352, "Y": 0.5417947173118591}]}, "Id": "b9ab832a-83b5-4a12-8ebe-0d800ffa1a2a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Unloaders:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07239977270364761, "Height": 0.0089563624933362, "Left": 0.008574532344937325, "Top": 0.5506824254989624}, "Polygon": [{"X": 0.008574532344937325, "Y": 0.5506824254989624}, {"X": 0.08097424358129501, "Y": 0.5506901741027832}, {"X": 0.08097430318593979, "Y": 0.5596387982368469}, {"X": 0.008574686013162136, "Y": 0.5596310496330261}]}, "Id": "49482aee-7ed9-4ed5-ab97-93c3e2735ecf"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007885992527008057, "Height": 0.008779648691415787, "Left": 0.21846283972263336, "Top": 0.5508977174758911}, "Polygon": [{"X": 0.2184629589319229, "Y": 0.5508977174758911}, {"X": 0.22634883224964142, "Y": 0.550898551940918}, {"X": 0.22634869813919067, "Y": 0.5596773624420166}, {"X": 0.21846283972263336, "Y": 0.5596765279769897}]}, "Id": "5c489910-62b6-409b-8921-e73de0e430de"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Base", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03260719031095505, "Height": 0.008714635856449604, "Left": 0.008446118794381618, "Top": 0.5757392048835754}, "Polygon": [{"X": 0.008446118794381618, "Y": 0.5757392048835754}, {"X": 0.041053202003240585, "Y": 0.5757427215576172}, {"X": 0.04105331003665924, "Y": 0.5844538807868958}, {"X": 0.008446268737316132, "Y": 0.584450364112854}]}, "Id": "973062f2-d075-4644-aec6-3ec7c0e8e18f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05205739662051201, "Height": 0.011327727697789669, "Left": 0.0454513356089592, "Top": 0.575525164604187}, "Polygon": [{"X": 0.0454513356089592, "Y": 0.575525164604187}, {"X": 0.09750868380069733, "Y": 0.5755307078361511}, {"X": 0.0975087359547615, "Y": 0.5868528485298157}, {"X": 0.04545146971940994, "Y": 0.5868473649024963}]}, "Id": "ef041235-a50b-42a2-a02e-879298423251"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "180.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04470408335328102, "Height": 0.008928242139518261, "Left": 0.21997465193271637, "Top": 0.5756847858428955}, "Polygon": [{"X": 0.21997477114200592, "Y": 0.5756847858428955}, {"X": 0.2646787464618683, "Y": 0.5756895542144775}, {"X": 0.2646785378456116, "Y": 0.584613025188446}, {"X": 0.21997465193271637, "Y": 0.584608256816864}]}, "Id": "0e5ebd05-9850-4b47-8ae8-65c7630a55b0"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08761050552129745, "Height": 0.008865030482411385, "Left": 0.0086781345307827, "Top": 0.5935982465744019}, "Polygon": [{"X": 0.0086781345307827, "Y": 0.5935982465744019}, {"X": 0.09628859907388687, "Y": 0.5936075448989868}, {"X": 0.09628864377737045, "Y": 0.6024633049964905}, {"X": 0.008678286336362362, "Y": 0.6024540066719055}]}, "Id": "9b783468-f4f2-4d3d-a9be-8332c4d09428"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02784905768930912, "Height": 0.008888681419193745, "Left": 0.1005115807056427, "Top": 0.59357750415802}, "Polygon": [{"X": 0.1005115807056427, "Y": 0.59357750415802}, {"X": 0.12836064398288727, "Y": 0.5935804843902588}, {"X": 0.12836064398288727, "Y": 0.6024661660194397}, {"X": 0.10051161795854568, "Y": 0.6024632453918457}]}, "Id": "8b24c22b-8045-418f-94f4-c60497993b2d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03617306053638458, "Height": 0.008964275941252708, "Left": 0.2198769450187683, "Top": 0.5935900807380676}, "Polygon": [{"X": 0.21987706422805786, "Y": 0.5935900807380676}, {"X": 0.2560499906539917, "Y": 0.593593955039978}, {"X": 0.25604984164237976, "Y": 0.6025543808937073}, {"X": 0.2198769450187683, "Y": 0.6025505065917969}]}, "Id": "9fdb6301-3647-485f-bc9a-6543493d17ad"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035121217370033264, "Height": 0.008870930410921574, "Left": 0.008374905213713646, "Top": 0.6112791299819946}, "Polygon": [{"X": 0.008374905213713646, "Y": 0.6112791299819946}, {"X": 0.043496016412973404, "Y": 0.6112828254699707}, {"X": 0.04349612072110176, "Y": 0.6201500296592712}, {"X": 0.008375057950615883, "Y": 0.6201463341712952}]}, "Id": "32962711-166f-4840-a6b9-acbe8245b2a0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Cost:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03433108702301979, "Height": 0.008759633637964725, "Left": 0.04827773943543434, "Top": 0.6113823056221008}, "Polygon": [{"X": 0.04827773943543434, "Y": 0.6113823056221008}, {"X": 0.08260876685380936, "Y": 0.6113859415054321}, {"X": 0.08260882645845413, "Y": 0.6201419234275818}, {"X": 0.0482778362929821, "Y": 0.6201382875442505}]}, "Id": "1e255cd0-c9e6-4ed9-9c37-7c52cca83e27"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "190.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04444485157728195, "Height": 0.008918522857129574, "Left": 0.22012856602668762, "Top": 0.611238420009613}, "Polygon": [{"X": 0.22012868523597717, "Y": 0.611238420009613}, {"X": 0.26457342505455017, "Y": 0.6112431287765503}, {"X": 0.26457324624061584, "Y": 0.6201569437980652}, {"X": 0.22012856602668762, "Y": 0.6201522350311279}]}, "Id": "de0c25da-bc57-4cc1-89ad-3a6967a4e296"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06891808658838272, "Height": 0.011261017993092537, "Left": 0.008325915783643723, "Top": 0.6364960074424744}, "Polygon": [{"X": 0.008325915783643723, "Y": 0.6364960074424744}, {"X": 0.07724392414093018, "Y": 0.636503279209137}, {"X": 0.07724400609731674, "Y": 0.6477570533752441}, {"X": 0.008326110430061817, "Y": 0.6477497816085815}]}, "Id": "8b1ccb22-8537-4ca9-a237-da3461141587"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055809542536735535, "Height": 0.008606204763054848, "Left": 0.21831992268562317, "Top": 0.636658251285553}, "Polygon": [{"X": 0.21832004189491272, "Y": 0.636658251285553}, {"X": 0.2741294801235199, "Y": 0.636664092540741}, {"X": 0.2741292715072632, "Y": 0.6452644467353821}, {"X": 0.21831992268562317, "Y": 0.6452585458755493}]}, "Id": "643b61f0-d2e2-4aa8-8735-b95db868fcdc"}, {"BlockType": "WORD", "Confidence": 98.17701721191406, "Text": "CapstonePay-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09224124252796173, "Height": 0.011310219764709473, "Left": 0.04248572885990143, "Top": 0.6539745926856995}, "Polygon": [{"X": 0.04248572885990143, "Y": 0.6539745926856995}, {"X": 0.13472697138786316, "Y": 0.6539842486381531}, {"X": 0.13472695648670197, "Y": 0.6652848124504089}, {"X": 0.04248586669564247, "Y": 0.6652751564979553}]}, "Id": "28ac7791-791d-4749-b3dd-40d3a7f1e95e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$190.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05447690933942795, "Height": 0.010041066445410252, "Left": 0.2184952050447464, "Top": 0.653295636177063}, "Polygon": [{"X": 0.21849533915519714, "Y": 0.653295636177063}, {"X": 0.27297210693359375, "Y": 0.6533013582229614}, {"X": 0.27297189831733704, "Y": 0.6633366942405701}, {"X": 0.2184952050447464, "Y": 0.6633310317993164}]}, "Id": "77e255a8-2fc2-46d6-912f-56d53f118bec"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "38024319", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06663750112056732, "Height": 0.009148575365543365, "Left": 0.0424657016992569, "Top": 0.6676889061927795}, "Polygon": [{"X": 0.0424657016992569, "Y": 0.6676889061927795}, {"X": 0.10910318046808243, "Y": 0.6676958799362183}, {"X": 0.10910320281982422, "Y": 0.6768375039100647}, {"X": 0.04246581345796585, "Y": 0.676830530166626}]}, "Id": "edae444f-be26-438d-be06-ad4d500ac9c7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035130906850099564, "Height": 0.008968435227870941, "Left": 0.04202926158905029, "Top": 0.6895958185195923}, "Polygon": [{"X": 0.04202926158905029, "Y": 0.6895958185195923}, {"X": 0.07716010510921478, "Y": 0.6895995140075684}, {"X": 0.07716016471385956, "Y": 0.6985642910003662}, {"X": 0.04202937334775925, "Y": 0.6985605955123901}]}, "Id": "d40c21e0-0231-4778-90e8-4b2f598fb15c"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Payments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06507906317710876, "Height": 0.011268939822912216, "Left": 0.08170006424188614, "Top": 0.6896418929100037}, "Polygon": [{"X": 0.08170006424188614, "Y": 0.6896418929100037}, {"X": 0.1467791348695755, "Y": 0.6896486878395081}, {"X": 0.14677909016609192, "Y": 0.7009108662605286}, {"X": 0.08170013874769211, "Y": 0.7009040713310242}]}, "Id": "4cf20e68-5e1d-4ee8-81d8-b5a7e2b9df91"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$190.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05450601130723953, "Height": 0.009886737912893295, "Left": 0.21849341690540314, "Top": 0.6891739964485168}, "Polygon": [{"X": 0.21849355101585388, "Y": 0.6891739964485168}, {"X": 0.27299943566322327, "Y": 0.6891796588897705}, {"X": 0.27299922704696655, "Y": 0.6990606784820557}, {"X": 0.21849341690540314, "Y": 0.699055016040802}]}, "Id": "3ef19f81-4dc5-4d73-a7fa-efa762c68108"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "a9393e4b-2c20-4277-b4df-6dfdfd3967c6", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "a9393e4b-2c20-4277-b4df-6dfdfd3967c6", "content-type": "application/x-amz-json-1.1", "content-length": "77238", "date": "Wed, 24 Sep 2025 13:26:47 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ba512b2d-a002-4529-b05d-1de890a6a669", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:51 GMT", "content-type": "application/json", "content-length": "1833", "connection": "keep-alive", "x-amzn-requestid": "ba512b2d-a002-4529-b05d-1de890a6a669"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to categorize.\n\nLet's analyze the content. It appears to be a receipt, maybe a carrier receipt. It says \"Receipt #:\", \"Bill Code:\", \"Carrier: BLUE STAR\", \"Purchase Orders\", \"Total Initial Pallets\", \"Total Finished Pallets\", \"Total Case Count\", \"Total Weight\", \"Base Charge\", \"Convenience Fee\", \"Total Cost\", \"Payments\". This looks like a lumper receipt? Might be a \"lumper receipt\" for loading/unloading labor? However lumper receipts include PO, door number, date-time, initial pallets, finished pallets, carrier, service charge, total amount. This page includes many of those. It also includes \"BOL:\" but no BOL number? It has \"Comments\", \"Canned Comments\", \"Unloaders: 2\". That seems like a POD? But no signature or delivered info. It's a receipt for the carrier's charges.\n\nWe also have \"Receipt #:\" maybe a receipt from the carrier? This could be a \"lumper receipt\" used by carriers to bill for loading/unloading.\n\nThus doc_type likely lumper_receipt.\n\nCheck differences: lumper receipt is \"Invoice or Receipt for services provided (loading/unloading labor)\". It says \"PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\". This page has \"Purchase Orders\" and \"Door:2\", \"Work Date:2025-09-19\". It has \"Carrier: BLUE STAR\", \"Base Charge 180.00\", \"Convenience Fee 10.00\", \"Total Cost 190.00\". So yes, lumper receipt.\n\nThus output: page 1, doc_type: lumper_receipt."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"lumper_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2837, "outputTokens": 379, "totalTokens": 3216}, "metrics": {"latencyMs": 1808}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/72ebbd92_G0H0K1LRWDOG7LXAKKQ7.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nCapstone Logistics\n30 TECHNOLOGY PKWY SOUTH SUITE 200\nPEACHTREE CORNERS, GA 30092\n770-414-1929\nFED ID# 45-3087555\n09:31:23 September 19, 2025\nReceipt #:\ne38dd18a-43b4-\n4ab6-9afa-0638fb6236a1\nLocation:\nUS FOODS MANASSAS VA\n50\nWork Date:\n2025-09-19\nBill Code:\nRCOD62049\nCarrier:\nBLUE STAR\nDock:\nDRY\nDoor:\n2\nPurchase Orders\nVendor\n71084250\nAMERCAREROYAL-QDOBA\nTotal Initial Pallets:\n10.00\nTotal Finished Pallets:\n15\nTotal Case Count:\n303\nTotal Weight:\n7161.00\nTrailer Number:\n1\nTractor Number:\nBOL:\nComments:\nCanned Comments:\nUnloaders:\n2\nBase Charge:\n180.00\nConvenience Fee:\n10.00\nTotal Cost:\n190.00\nPayments:\nAmount\nCapstonePay-\n$190.00\n38024319\nTotal Payments\n$190.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}