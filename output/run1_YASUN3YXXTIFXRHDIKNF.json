{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.43170401453971863, "Height": 0.5889143347740173, "Left": 0.32218173146247864, "Top": 0.21769069135189056}, "Polygon": [{"X": 0.32218173146247864, "Y": 0.797556459903717}, {"X": 0.3399563133716583, "Y": 0.21769069135189056}, {"X": 0.7538857460021973, "Y": 0.23142652213573456}, {"X": 0.7325760722160339, "Y": 0.8066050410270691}]}, "Id": "b7d5f164-22fd-451f-a015-cea566e2dbcf", "Relationships": [{"Type": "CHILD", "Ids": ["03240195-3d49-46b6-b334-6a3afb332049", "dde50a6b-e713-4432-ac94-24663b2aaf6b", "3e6c78d3-fed5-4d8c-ab61-a878e843452a", "b8f9b6c2-ba5f-4c70-9748-329c0015dea1", "4f71541d-fc62-4d9a-927b-2f514b21b207", "20d19755-1047-44b2-b166-67c9638ee8a4", "60f464d4-03ba-4d5f-920b-fdda1a8f3f90", "9156b74d-ed9b-40c5-913a-e05cbb9e9095", "49806d0b-4e99-47fc-b25c-01dd14600253", "b0dfe7ed-24be-4540-b4d4-5d379c1c7e58", "a1ff69d7-05cc-4a95-82bf-67d86416f233", "6377f2d5-70cd-45a2-9615-cf96b601e970", "ebde2620-f6fa-4243-8604-6a4cb2d505d0", "1b3ef199-876e-47ae-8328-1dcb0fbc06f0", "ba18f7cc-bb0b-4f35-8a35-25112a9327ac", "6f8a4aa5-1c94-4f4e-be38-ba738cc2d49b", "9b04f611-9d4a-42f5-b507-90cb2e360bcc", "94d58053-576c-4c84-872b-04dc61fa455e", "f92759f4-f8c3-4c82-a87c-3a909d8fd0b2", "e3f85665-e437-4906-9e28-c0d6be551183", "61ddde3b-9936-4184-9eda-ea39595acd7b", "bfc3b0e3-0370-4fb2-8145-4be33d077e82"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "PNCT", "Geometry": {"BoundingBox": {"Width": 0.025146232917904854, "Height": 0.04900875315070152, "Left": 0.3346099555492401, "Top": 0.5788138508796692}, "Polygon": [{"X": 0.3346099555492401, "Y": 0.6272240281105042}, {"X": 0.3360992670059204, "Y": 0.5788138508796692}, {"X": 0.3597561717033386, "Y": 0.5794349312782288}, {"X": 0.3582497537136078, "Y": 0.6278226375579834}]}, "Id": "03240195-3d49-46b6-b334-6a3afb332049", "Relationships": [{"Type": "CHILD", "Ids": ["7d80935a-a8e3-41e3-ac19-498375da08ee"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "PICKUP 566367", "Geometry": {"BoundingBox": {"Width": 0.029812457039952278, "Height": 0.17160378396511078, "Left": 0.3377879559993744, "Top": 0.36865589022636414}, "Polygon": [{"X": 0.3377879559993744, "Y": 0.5395983457565308}, {"X": 0.34304824471473694, "Y": 0.36865589022636414}, {"X": 0.3676004111766815, "Y": 0.3693995475769043}, {"X": 0.36227768659591675, "Y": 0.5402596592903137}]}, "Id": "dde50a6b-e713-4432-ac94-24663b2aaf6b", "Relationships": [{"Type": "CHILD", "Ids": ["ea91958e-a57b-41f0-8b0a-2a1e7eae077f", "74e96f24-613b-4a72-90ab-7940e1cae171"]}]}, {"BlockType": "LINE", "Confidence": 98.20451354980469, "Text": "MSC:", "Geometry": {"BoundingBox": {"Width": 0.025167690590023994, "Height": 0.04603307321667671, "Left": 0.3714204430580139, "Top": 0.5832650661468506}, "Polygon": [{"X": 0.3714204430580139, "Y": 0.6286975145339966}, {"X": 0.3728441596031189, "Y": 0.5832650661468506}, {"X": 0.39658811688423157, "Y": 0.5838868021965027}, {"X": 0.39514830708503723, "Y": 0.6292981505393982}]}, "Id": "3e6c78d3-fed5-4d8c-ab61-a878e843452a", "Relationships": [{"Type": "CHILD", "Ids": ["c80eb38d-82aa-4418-93c5-4830f76cc6d3"]}]}, {"BlockType": "LINE", "Confidence": 99.9157943725586, "Text": "LOAD OUT", "Geometry": {"BoundingBox": {"Width": 0.015253691002726555, "Height": 0.05691348388791084, "Left": 0.3997693955898285, "Top": 0.6936008930206299}, "Polygon": [{"X": 0.3997693955898285, "Y": 0.750205397605896}, {"X": 0.4015715420246124, "Y": 0.6936008930206299}, {"X": 0.4150230884552002, "Y": 0.6939247846603394}, {"X": 0.41320955753326416, "Y": 0.7505143880844116}]}, "Id": "b8f9b6c2-ba5f-4c70-9748-329c0015dea1", "Relationships": [{"Type": "CHILD", "Ids": ["dfafe199-b0fc-439a-9317-7d78af3c96cd", "0d257fe9-02c9-435e-bc81-d7fa2d0cd1a1"]}]}, {"BlockType": "LINE", "Confidence": 99.7810287475586, "Text": "SXIY: SKYLINE EXPRESS (INLAND E 2025/06/06 13:18:00", "Geometry": {"BoundingBox": {"Width": 0.023850400000810623, "Height": 0.2915046215057373, "Left": 0.40553516149520874, "Top": 0.2729915380477905}, "Polygon": [{"X": 0.40553516149520874, "Y": 0.5641103386878967}, {"X": 0.4148029685020447, "Y": 0.2729915380477905}, {"X": 0.42938554286956787, "Y": 0.27346062660217285}, {"X": 0.42005476355552673, "Y": 0.5644961595535278}]}, "Id": "4f71541d-fc62-4d9a-927b-2f514b21b207", "Relationships": [{"Type": "CHILD", "Ids": ["030a6eba-59da-4b38-93c9-32c1acbda31b", "a7ffdc00-5666-4b33-88d8-e82a4121d478", "ada87c71-a227-4518-bf83-f0e54891edf1", "d67fc4b8-d8a0-4a77-a22a-c7584bd39a13", "a3b20ebf-b5c8-4bed-9d35-90dcac9b59da", "31cfd94b-171c-4f3b-bc51-546592abe6e0", "8cbd3913-b2a7-453f-986e-2f05aff93010"]}]}, {"BlockType": "LINE", "Confidence": 99.30325317382812, "Text": "CONTAINER: FCIU9733694", "Geometry": {"BoundingBox": {"Width": 0.02146645449101925, "Height": 0.18062946200370789, "Left": 0.420697957277298, "Top": 0.5708413124084473}, "Polygon": [{"X": 0.420697957277298, "Y": 0.751111626625061}, {"X": 0.42649611830711365, "Y": 0.5708413124084473}, {"X": 0.44216442108154297, "Y": 0.5712557435035706}, {"X": 0.43632417917251587, "Y": 0.7514707446098328}]}, "Id": "20d19755-1047-44b2-b166-67c9638ee8a4", "Relationships": [{"Type": "CHILD", "Ids": ["3f51db47-8b22-4041-a4a3-f8768c047946", "d3319287-0f34-4eb1-b3a9-818f75c5b23c"]}]}, {"BlockType": "LINE", "Confidence": 91.78950500488281, "Text": "SIZE/TYPE: 40 GP 96", "Geometry": {"BoundingBox": {"Width": 0.017835110425949097, "Height": 0.10935010761022568, "Left": 0.42646822333335876, "Top": 0.*****************}, "Polygon": [{"X": 0.42646822333335876, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "60f464d4-03ba-4d5f-920b-fdda1a8f3f90", "Relationships": [{"Type": "CHILD", "Ids": ["de65d7b9-46ea-4df7-85ef-c1e35e68dcb8", "478a1658-ae66-4e33-9758-1108a514bdba", "175f6fee-becd-48eb-8094-97c0ed88508f", "2983e5c9-4a2d-4393-b0c4-18572b250af7"]}]}, {"BlockType": "LINE", "Confidence": 99.84109497070312, "Text": "CARD ID: 231466", "Geometry": {"BoundingBox": {"Width": 0.014924447052180767, "Height": 0.08293350785970688, "Left": 0.***************, "Top": 0.2973398268222809}, "Polygon": [{"X": 0.***************, "Y": 0.37990424036979675}, {"X": 0.*****************, "Y": 0.2973398268222809}, {"X": 0.*****************, "Y": 0.29772886633872986}, {"X": 0.*****************, "Y": 0.38027334213256836}]}, "Id": "9156b74d-ed9b-40c5-913a-e05cbb9e9095", "Relationships": [{"Type": "CHILD", "Ids": ["a7b835b6-e785-474b-a757-fe2e45fd305f", "24ca37e4-497e-468d-81ff-307f7c60353d", "8b163ba9-46e7-462b-a3b9-30967715022b"]}]}, {"BlockType": "LINE", "Confidence": 95.57068634033203, "Text": "CHASSIS: AV0Z410014", "Geometry": {"BoundingBox": {"Width": 0.016160251572728157, "Height": 0.10707060992717743, "Left": 0.****************, "Top": 0.6450700163841248}, "Polygon": [{"X": 0.****************, "Y": 0.7518494129180908}, {"X": 0.*****************, "Y": 0.6450700163841248}, {"X": 0.****************, "Y": 0.6453877687454224}, {"X": 0.4525021016597748, "Y": 0.7521406412124634}]}, "Id": "49806d0b-4e99-47fc-b25c-01dd14600253", "Relationships": [{"Type": "CHILD", "Ids": ["d495f21b-a06c-4d47-8760-4da189160011", "becdc16a-39cb-4c91-8798-368a91720380"]}]}, {"BlockType": "LINE", "Confidence": 99.4052963256836, "Text": "SIZE/TYPE:", "Geometry": {"BoundingBox": {"Width": 0.014251652173697948, "Height": 0.05559337139129639, "Left": 0.4469074308872223, "Top": 0.4876033067703247}, "Polygon": [{"X": 0.4469074308872223, "Y": 0.5428606271743774}, {"X": 0.448701411485672, "Y": 0.4876033067703247}, {"X": 0.4611590802669525, "Y": 0.48795291781425476}, {"X": 0.45935484766960144, "Y": 0.5431966781616211}]}, "Id": "b0dfe7ed-24be-4540-b4d4-5d379c1c7e58", "Relationships": [{"Type": "CHILD", "Ids": ["5abd33da-02c9-4dc6-8a7c-4473d3941e94"]}]}, {"BlockType": "LINE", "Confidence": 90.46456146240234, "Text": "GENERATOR:", "Geometry": {"BoundingBox": {"Width": 0.014053573831915855, "Height": 0.05498840659856796, "Left": 0.4585486650466919, "Top": 0.6974174380302429}, "Polygon": [{"X": 0.4585486650466919, "Y": 0.7521243095397949}, {"X": 0.4603404998779297, "Y": 0.6974174380302429}, {"X": 0.4726022481918335, "Y": 0.6977121233940125}, {"X": 0.4708004295825958, "Y": 0.7524058818817139}]}, "Id": "a1ff69d7-05cc-4a95-82bf-67d86416f233", "Relationships": [{"Type": "CHILD", "Ids": ["e904beaf-4a08-43e8-9682-9cfb039b4c70"]}]}, {"BlockType": "LINE", "Confidence": 97.67185974121094, "Text": "TEMP: AIR EXCH:", "Geometry": {"BoundingBox": {"Width": 0.015518084168434143, "Height": 0.09108633548021317, "Left": 0.46533775329589844, "Top": 0.45312705636024475}, "Polygon": [{"X": 0.46533775329589844, "Y": 0.5438752770423889}, {"X": 0.46830999851226807, "Y": 0.45312705636024475}, {"X": 0.4808558523654938, "Y": 0.4534875750541687}, {"X": 0.47786667943000793, "Y": 0.5442134141921997}]}, "Id": "6377f2d5-70cd-45a2-9615-cf96b601e970", "Relationships": [{"Type": "CHILD", "Ids": ["32f5b164-e993-4cf2-a530-cba250e0d4da", "36d7b56a-1c6c-40a6-a79f-b2f73edf2b62", "5457670a-8880-4e06-b37d-f9caade9b157"]}]}, {"BlockType": "LINE", "Confidence": 99.68112182617188, "Text": "VESSEL: MVEG522R", "Geometry": {"BoundingBox": {"Width": 0.015481905080378056, "Height": 0.09055313467979431, "Left": 0.47689756751060486, "Top": 0.6626637578010559}, "Polygon": [{"X": 0.47689756751060486, "Y": 0.7529301643371582}, {"X": 0.47987988591194153, "Y": 0.6626637578010559}, {"X": 0.49237948656082153, "Y": 0.6629726886749268}, {"X": 0.4893803298473358, "Y": 0.7532169222831726}]}, "Id": "ebde2620-f6fa-4243-8604-6a4cb2d505d0", "Relationships": [{"Type": "CHILD", "Ids": ["e071f999-c726-4052-bfea-0c4c6ca7d493", "622bb9c4-4547-41eb-9569-a35840819422"]}]}, {"BlockType": "LINE", "Confidence": 84.32855987548828, "Text": "VENT:", "Geometry": {"BoundingBox": {"Width": 0.013262175023555756, "Height": 0.027749493718147278, "Left": 0.48391425609588623, "Top": 0.5167261362075806}, "Polygon": [{"X": 0.48391425609588623, "Y": 0.5441422462463379}, {"X": 0.4848201274871826, "Y": 0.5167261362075806}, {"X": 0.4971764385700226, "Y": 0.517066240310669}, {"X": 0.4962655305862427, "Y": 0.5444756150245667}]}, "Id": "1b3ef199-876e-47ae-8328-1dcb0fbc06f0", "Relationships": [{"Type": "CHILD", "Ids": ["0cf1e245-fa16-4f7f-947f-39050b493c2d"]}]}, {"BlockType": "LINE", "Confidence": 96.29100799560547, "Text": "CARGO WT: 25792 LB", "Geometry": {"BoundingBox": {"Width": 0.01593306101858616, "Height": 0.09867437928915024, "Left": 0.49518442153930664, "Top": 0.6543326377868652}, "Polygon": [{"X": 0.49518442153930664, "Y": 0.752716600894928}, {"X": 0.4984629452228546, "Y": 0.6543326377868652}, {"X": 0.5111175179481506, "Y": 0.6546474695205688}, {"X": 0.5078204274177551, "Y": 0.7530069947242737}]}, "Id": "ba18f7cc-bb0b-4f35-8a35-25112a9327ac", "Relationships": [{"Type": "CHILD", "Ids": ["fffaf3c8-8a23-4066-9234-84db75e8cf06", "faa39692-b6d3-4b5a-a6b5-cbff0e53cf3b", "59bd6164-7764-4775-b6ff-4140d86b11e3", "3ead3381-0fd7-443e-abbc-b501ec161aab"]}]}, {"BlockType": "LINE", "Confidence": 99.08402252197266, "Text": "HAZARD:", "Geometry": {"BoundingBox": {"Width": 0.013657238334417343, "Height": 0.038098182529211044, "Left": 0.5024366974830627, "Top": 0.5059131979942322}, "Polygon": [{"X": 0.5024366974830627, "Y": 0.5436766743659973}, {"X": 0.5036953091621399, "Y": 0.5059131979942322}, {"X": 0.516093909740448, "Y": 0.506257176399231}, {"X": 0.5148283839225769, "Y": 0.5440114140510559}]}, "Id": "6f8a4aa5-1c94-4f4e-be38-ba738cc2d49b", "Relationships": [{"Type": "CHILD", "Ids": ["90e0d4b9-95ed-4db7-8514-333144699607"]}]}, {"BlockType": "LINE", "Confidence": 99.82063293457031, "Text": "SEALS: EU28454579", "Geometry": {"BoundingBox": {"Width": 0.015559786930680275, "Height": 0.09529483318328857, "Left": 0.5138859748840332, "Top": 0.657780110836029}, "Polygon": [{"X": 0.5138859748840332, "Y": 0.7527910470962524}, {"X": 0.517079770565033, "Y": 0.657780110836029}, {"X": 0.5294457674026489, "Y": 0.6580870747566223}, {"X": 0.52623450756073, "Y": 0.7530749440193176}]}, "Id": "9b04f611-9d4a-42f5-b507-90cb2e360bcc", "Relationships": [{"Type": "CHILD", "Ids": ["15deb286-4f5d-4038-b5bd-bb63b213f23e", "07c17332-2906-4559-9ff4-4254172640f4"]}]}, {"BlockType": "LINE", "Confidence": 99.20360565185547, "Text": "RELEASE:", "Geometry": {"BoundingBox": {"Width": 0.01374824158847332, "Height": 0.0440969318151474, "Left": 0.5207757353782654, "Top": 0.5003316402435303}, "Polygon": [{"X": 0.5207757353782654, "Y": 0.5440971255302429}, {"X": 0.522246778011322, "Y": 0.5003316402435303}, {"X": 0.5345239639282227, "Y": 0.5006736516952515}, {"X": 0.5330448746681213, "Y": 0.5444285869598389}]}, "Id": "94d58053-576c-4c84-872b-04dc61fa455e", "Relationships": [{"Type": "CHILD", "Ids": ["1038eea9-c09e-47db-9160-20c8a629aa54"]}]}, {"BlockType": "LINE", "Confidence": 99.95376586914062, "Text": "Pick FCIU9733694 from GRID BGR (F1903A)", "Geometry": {"BoundingBox": {"Width": 0.024568185210227966, "Height": 0.246754989027977, "Left": 0.5322601795196533, "Top": 0.5066400170326233}, "Polygon": [{"X": 0.5322601795196533, "Y": 0.7530235052108765}, {"X": 0.5406127572059631, "Y": 0.5066400170326233}, {"X": 0.5568283796310425, "Y": 0.5070899128913879}, {"X": 0.5484164357185364, "Y": 0.7533950209617615}]}, "Id": "f92759f4-f8c3-4c82-a87c-3a909d8fd0b2", "Relationships": [{"Type": "CHILD", "Ids": ["31849436-41c8-4ed5-99f5-682a6064960d", "65a021c0-4a1f-4521-a40d-0620479a27ae", "49bd0fa6-dc34-4086-8017-4232a7d3f68a", "eae87a6a-c57f-4065-885f-03cb9e6a7ea3", "e8dd1392-382b-40b1-9926-8e89245ca041", "8d5fadd2-2d0e-4e9b-9f2e-001f36a4ac29"]}]}, {"BlockType": "LINE", "Confidence": 98.21083068847656, "Text": "WELCOME TO PNCT. PLEASE FOLLOW SAFETY RULES AND REGULATIONS.", "Geometry": {"BoundingBox": {"Width": 0.02507202886044979, "Height": 0.30984294414520264, "Left": 0.6614753603935242, "Top": 0.44700363278388977}, "Polygon": [{"X": 0.6614753603935242, "Y": 0.756527304649353}, {"X": 0.6725914478302002, "Y": 0.44700363278388977}, {"X": 0.6865473985671997, "Y": 0.44740793108940125}, {"X": 0.6753674745559692, "Y": 0.7568466067314148}]}, "Id": "e3f85665-e437-4906-9e28-c0d6be551183", "Relationships": [{"Type": "CHILD", "Ids": ["6e3e133a-db3d-4378-9fb8-c3109c2e9cd0", "fb41781f-d711-418e-8213-bf42fc8ef7c3", "d6332241-d5a4-46cd-a7c5-b286060ee50f", "4c0ff48e-ca44-454b-adf0-7fc7217e3880", "f0221f17-c0b9-4a25-9d55-87f8dfa3b5f8", "72d77514-cdc4-45e8-b898-ce5869952c6a", "60b9f145-8c88-4d58-90e4-6cdf173838e3", "8d7d754f-e308-48cc-9b1b-c900c1ccbddd", "b2277670-979f-4099-992f-5d682d731102"]}]}, {"BlockType": "LINE", "Confidence": 99.74710845947266, "Text": "TO CONTACT TROUBLE WINDOW: TXT MSG (888) 490-2226 WITH TICKET NUMBER.", "Geometry": {"BoundingBox": {"Width": 0.030376246199011803, "Height": 0.38018250465393066, "Left": 0.6796075701713562, "Top": 0.37636929750442505}, "Polygon": [{"X": 0.6796075701713562, "Y": 0.756171464920044}, {"X": 0.693354606628418, "Y": 0.37636929750442505}, {"X": 0.709983766078949, "Y": 0.3768739104270935}, {"X": 0.696143388748169, "Y": 0.7565518021583557}]}, "Id": "61ddde3b-9936-4184-9eda-ea39595acd7b", "Relationships": [{"Type": "CHILD", "Ids": ["160a20d0-d67a-4a8f-ac17-bd114897fbbd", "f7d73718-0ebc-4b2e-bfba-0bc5beef3685", "acfb3484-55d3-40ec-bc5f-6a28c8dd786f", "a9246129-53b7-4788-942b-54501084ace5", "7c0e7e95-2bb0-42ba-9756-9ebe59ea8ccb", "ec5d6e97-d3a0-4025-8386-12f5de4316a1", "eaa455c6-b1b4-45eb-95fc-23bd0f5e3cc2", "6cd3e489-5557-4ffd-ad06-84a108dbbbd7", "f8f213b5-c000-4fba-8937-9ecc5059890c", "498bda25-7c47-4299-8fc4-a7518ecaa68d", "db1204f3-d2b6-4ef6-9838-357599ba9f45"]}]}, {"BlockType": "LINE", "Confidence": 95.82042694091797, "Text": "SAVE THIS CARD TO EXIT THE TERMINAL.", "Geometry": {"BoundingBox": {"Width": 0.02047378197312355, "Height": 0.18006400763988495, "Left": 0.6984972357749939, "Top": 0.5766897201538086}, "Polygon": [{"X": 0.6984972357749939, "Y": 0.7564345002174377}, {"X": 0.7050560116767883, "Y": 0.5766897201538086}, {"X": 0.7189710140228271, "Y": 0.5770581960678101}, {"X": 0.7123751640319824, "Y": 0.7567537426948547}]}, "Id": "bfc3b0e3-0370-4fb2-8145-4be33d077e82", "Relationships": [{"Type": "CHILD", "Ids": ["fc7e2506-6a71-4fcd-87d3-b957e065a5f6", "a55e765f-8bdc-4cf2-846e-a606f42d24cc", "f05b0011-c553-43fa-91ad-2727238070da", "a74a3adc-5054-46ba-887b-31d5977608ef", "bbb73e35-26f4-40da-8617-3c501ddb967b", "0cfcd86c-f1d9-43c7-8986-cb1c1771a88d", "155cc09e-a769-462d-8287-eb2cebc94d1f"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PNCT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025146232917904854, "Height": 0.04900875315070152, "Left": 0.3346099555492401, "Top": 0.5788138508796692}, "Polygon": [{"X": 0.3346099555492401, "Y": 0.6272240281105042}, {"X": 0.3360992670059204, "Y": 0.5788138508796692}, {"X": 0.3597561717033386, "Y": 0.5794349312782288}, {"X": 0.3582497537136078, "Y": 0.6278226375579834}]}, "Id": "7d80935a-a8e3-41e3-ac19-498375da08ee"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PICKUP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026437046006321907, "Height": 0.07968581467866898, "Left": 0.3377879559993744, "Top": 0.46055999398231506}, "Polygon": [{"X": 0.3377879559993744, "Y": 0.5395983457565308}, {"X": 0.3402201235294342, "Y": 0.46055999398231506}, {"X": 0.36422500014305115, "Y": 0.46124470233917236}, {"X": 0.3617645502090454, "Y": 0.5402458310127258}]}, "Id": "ea91958e-a57b-41f0-8b0a-2a1e7eae077f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "566367", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026673603802919388, "Height": 0.07964227348566055, "Left": 0.34092679619789124, "Top": 0.3686652183532715}, "Polygon": [{"X": 0.34092679619789124, "Y": 0.4476107656955719}, {"X": 0.34335651993751526, "Y": 0.3686652183532715}, {"X": 0.3676004111766815, "Y": 0.3693995475769043}, {"X": 0.36514222621917725, "Y": 0.44830751419067383}]}, "Id": "74e96f24-613b-4a72-90ab-7940e1cae171"}, {"BlockType": "WORD", "Confidence": 98.20451354980469, "Text": "MSC:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025167690590023994, "Height": 0.04603307321667671, "Left": 0.3714204430580139, "Top": 0.5832650661468506}, "Polygon": [{"X": 0.3714204430580139, "Y": 0.6286975145339966}, {"X": 0.3728441596031189, "Y": 0.5832650661468506}, {"X": 0.39658811688423157, "Y": 0.5838868021965027}, {"X": 0.39514830708503723, "Y": 0.6292981505393982}]}, "Id": "c80eb38d-82aa-4418-93c5-4830f76cc6d3"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014316585846245289, "Height": 0.028985144570469856, "Left": 0.3998117446899414, "Top": 0.7215292453765869}, "Polygon": [{"X": 0.3998117446899414, "Y": 0.7502063512802124}, {"X": 0.40072476863861084, "Y": 0.7215292453765869}, {"X": 0.4141283333301544, "Y": 0.7218447923660278}, {"X": 0.41320955753326416, "Y": 0.7505143880844116}]}, "Id": "dfafe199-b0fc-439a-9317-7d78af3c96cd"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "OUT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014080283232033253, "Height": 0.021484538912773132, "Left": 0.4008976221084595, "Top": 0.6936008930206299}, "Polygon": [{"X": 0.4008976221084595, "Y": 0.7147681713104248}, {"X": 0.4015715420246124, "Y": 0.6936008930206299}, {"X": 0.41497790813446045, "Y": 0.6939237117767334}, {"X": 0.41429975628852844, "Y": 0.7150854468345642}]}, "Id": "0d257fe9-02c9-435e-bc81-d7fa2d0cd1a1"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "SXIY:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012649871408939362, "Height": 0.0270206481218338, "Left": 0.4056836664676666, "Top": 0.5374070405960083}, "Polygon": [{"X": 0.4056836664676666, "Y": 0.5641143321990967}, {"X": 0.40653395652770996, "Y": 0.5374070405960083}, {"X": 0.4183335304260254, "Y": 0.537726640701294}, {"X": 0.4174785614013672, "Y": 0.5644276738166809}]}, "Id": "030a6eba-59da-4b38-93c9-32c1acbda31b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SKYLINE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01339669432491064, "Height": 0.040143512189388275, "Left": 0.4068199098110199, "Top": 0.49138346314430237}, "Polygon": [{"X": 0.4068199098110199, "Y": 0.5311972498893738}, {"X": 0.40808752179145813, "Y": 0.49138346314430237}, {"X": 0.4202165901660919, "Y": 0.4917227029800415}, {"X": 0.41894179582595825, "Y": 0.5315269827842712}]}, "Id": "a7ffdc00-5666-4b33-88d8-e82a4121d478"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "EXPRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013586300425231457, "Height": 0.03997325152158737, "Left": 0.40819475054740906, "Top": 0.44705456495285034}, "Polygon": [{"X": 0.40819475054740906, "Y": 0.4866822063922882}, {"X": 0.40945643186569214, "Y": 0.44705456495285034}, {"X": 0.421781063079834, "Y": 0.4474097490310669}, {"X": 0.4205121099948883, "Y": 0.4870278239250183}]}, "Id": "ada87c71-a227-4518-bf83-f0e54891edf1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "(INLAND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015452689491212368, "Height": 0.038875315338373184, "Left": 0.40979477763175964, "Top": 0.4021093547344208}, "Polygon": [{"X": 0.40979477763175964, "Y": 0.4405730664730072}, {"X": 0.4110194742679596, "Y": 0.4021093547344208}, {"X": 0.425247460603714, "Y": 0.4025317132472992}, {"X": 0.4240146279335022, "Y": 0.44098466634750366}]}, "Id": "d67fc4b8-d8a0-4a77-a22a-c7584bd39a13"}, {"BlockType": "WORD", "Confidence": 99.55416870117188, "Text": "E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01241071056574583, "Height": 0.005590123124420643, "Left": 0.41083377599716187, "Top": 0.3924452066421509}, "Polygon": [{"X": 0.41083377599716187, "Y": 0.39767083525657654}, {"X": 0.41100013256073, "Y": 0.3924452066421509}, {"X": 0.42324450612068176, "Y": 0.39281097054481506}, {"X": 0.42307719588279724, "Y": 0.39803534746170044}]}, "Id": "a3b20ebf-b5c8-4bed-9d35-90dcac9b59da"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "2025/06/06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014249342493712902, "Height": 0.05752436816692352, "Left": 0.41167953610420227, "Top": 0.32281923294067383}, "Polygon": [{"X": 0.41167953610420227, "Y": 0.37996962666511536}, {"X": 0.4134991765022278, "Y": 0.32281923294067383}, {"X": 0.42592886090278625, "Y": 0.32320716977119446}, {"X": 0.4240986704826355, "Y": 0.38034358620643616}]}, "Id": "31cfd94b-171c-4f3b-bc51-546592abe6e0"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "13:18:00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013860885985195637, "Height": 0.04465767368674278, "Left": 0.41361814737319946, "Top": 0.27299875020980835}, "Polygon": [{"X": 0.41361814737319946, "Y": 0.3172667324542999}, {"X": 0.41502758860588074, "Y": 0.27299875020980835}, {"X": 0.4274790287017822, "Y": 0.27339932322502136}, {"X": 0.4260614216327667, "Y": 0.31765642762184143}]}, "Id": "8cbd3913-b2a7-453f-986e-2f05aff93010"}, {"BlockType": "WORD", "Confidence": 98.80421447753906, "Text": "CONTAINER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018228111788630486, "Height": 0.08067174255847931, "Left": 0.420697957277298, "Top": 0.6707990169525146}, "Polygon": [{"X": 0.420697957277298, "Y": 0.751111626625061}, {"X": 0.42328110337257385, "Y": 0.6707990169525146}, {"X": 0.4389260709285736, "Y": 0.6711827516555786}, {"X": 0.43632417917251587, "Y": 0.7514707446098328}]}, "Id": "3f51db47-8b22-4041-a4a3-f8768c047946"}, {"BlockType": "WORD", "Confidence": 99.80229949951172, "Text": "FCIU9733694", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018271418288350105, "Height": 0.09077263623476028, "Left": 0.42378321290016174, "Top": 0.570846438407898}, "Polygon": [{"X": 0.42378321290016174, "Y": 0.6612399220466614}, {"X": 0.42669087648391724, "Y": 0.570846438407898}, {"X": 0.4420546293258667, "Y": 0.5712528228759766}, {"X": 0.43912628293037415, "Y": 0.6616190671920776}]}, "Id": "d3319287-0f34-4eb1-b3a9-818f75c5b23c"}, {"BlockType": "WORD", "Confidence": 67.30608367919922, "Text": "SIZE/TYPE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01605754718184471, "Height": 0.0598200187087059, "Left": 0.42646822333335876, "Top": 0.487549364566803}, "Polygon": [{"X": 0.42646822333335876, "Y": 0.****************}, {"X": 0.4283792972564697, "Y": 0.487549364566803}, {"X": 0.4425257742404938, "Y": 0.48794621229171753}, {"X": 0.44060221314430237, "Y": 0.5473693609237671}]}, "Id": "de65d7b9-46ea-4df7-85ef-c1e35e68dcb8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012168725952506065, "Height": 0.01148912776261568, "Left": 0.4306541383266449, "Top": 0.46987077593803406}, "Polygon": [{"X": 0.4306541383266449, "Y": 0.48102718591690063}, {"X": 0.43101319670677185, "Y": 0.46987077593803406}, {"X": 0.4428228735923767, "Y": 0.4702061116695404}, {"X": 0.44246187806129456, "Y": 0.48135992884635925}]}, "Id": "478a1658-ae66-4e33-9758-1108a514bdba"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01278946828097105, "Height": 0.011942661367356777, "Left": 0.431011825799942, "Top": 0.4535912275314331}, "Polygon": [{"X": 0.431011825799942, "Y": 0.46518027782440186}, {"X": 0.43138477206230164, "Y": 0.4535912275314331}, {"X": 0.44380128383636475, "Y": 0.45394766330718994}, {"X": 0.4434261918067932, "Y": 0.46553388237953186}]}, "Id": "175f6fee-becd-48eb-8094-97c0ed88508f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "96", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012413336895406246, "Height": 0.011636271141469479, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.***************, "Y": 0.****************}]}, "Id": "2983e5c9-4a2d-4393-b0c4-18572b250af7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012961073778569698, "Height": 0.023251917213201523, "Left": 0.***************, "Top": 0.35702046751976013}, "Polygon": [{"X": 0.***************, "Y": 0.37990424036979675}, {"X": 0.****************, "Y": 0.35702046751976013}, {"X": 0.*****************, "Y": 0.35739409923553467}, {"X": 0.*****************, "Y": 0.38027238845825195}]}, "Id": "a7b835b6-e785-474b-a757-fe2e45fd305f"}, {"BlockType": "WORD", "Confidence": 99.53304290771484, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012484561651945114, "Height": 0.015314670279622078, "Left": 0.***************, "Top": 0.3369644284248352}, "Polygon": [{"X": 0.***************, "Y": 0.35191109776496887}, {"X": 0.****************, "Y": 0.3369644284248352}, {"X": 0.****************, "Y": 0.3373359739780426}, {"X": 0.*****************, "Y": 0.35227909684181213}]}, "Id": "24ca37e4-497e-468d-81ff-307f7c60353d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "231466", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013243108987808228, "Height": 0.033760908991098404, "Left": 0.4355831444263458, "Top": 0.29734179377555847}, "Polygon": [{"X": 0.4355831444263458, "Y": 0.3307248055934906}, {"X": 0.436657577753067, "Y": 0.29734179377555847}, {"X": 0.44882625341415405, "Y": 0.29772770404815674}, {"X": 0.44774582982063293, "Y": 0.3311026990413666}]}, "Id": "8b163ba9-46e7-462b-a3b9-30967715022b"}, {"BlockType": "WORD", "Confidence": 99.36224365234375, "Text": "CHASSIS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014109321869909763, "Height": 0.04425701126456261, "Left": 0.****************, "Top": 0.7078835964202881}, "Polygon": [{"X": 0.****************, "Y": 0.7518494129180908}, {"X": 0.44125550985336304, "Y": 0.7078835964202881}, {"X": 0.4539376199245453, "Y": 0.7081857323646545}, {"X": 0.4525021016597748, "Y": 0.7521406412124634}]}, "Id": "d495f21b-a06c-4d47-8760-4da189160011"}, {"BlockType": "WORD", "Confidence": 91.77913665771484, "Text": "AV0Z410014", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014197148382663727, "Height": 0.0576721727848053, "Left": 0.4416823387145996, "Top": 0.6450762748718262}, "Polygon": [{"X": 0.4416823387145996, "Y": 0.7024535536766052}, {"X": 0.4435451328754425, "Y": 0.6450762748718262}, {"X": 0.4558795094490051, "Y": 0.6453850269317627}, {"X": 0.454006165266037, "Y": 0.7027484774589539}]}, "Id": "becdc16a-39cb-4c91-8798-368a91720380"}, {"BlockType": "WORD", "Confidence": 99.4052963256836, "Text": "SIZE/TYPE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014251652173697948, "Height": 0.05559337139129639, "Left": 0.4469074308872223, "Top": 0.4876033067703247}, "Polygon": [{"X": 0.4469074308872223, "Y": 0.5428606271743774}, {"X": 0.448701411485672, "Y": 0.4876033067703247}, {"X": 0.4611590802669525, "Y": 0.48795291781425476}, {"X": 0.45935484766960144, "Y": 0.5431966781616211}]}, "Id": "5abd33da-02c9-4dc6-8a7c-4473d3941e94"}, {"BlockType": "WORD", "Confidence": 90.46456146240234, "Text": "GENERATOR:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014053573831915855, "Height": 0.05498840659856796, "Left": 0.4585486650466919, "Top": 0.6974174380302429}, "Polygon": [{"X": 0.4585486650466919, "Y": 0.7521243095397949}, {"X": 0.4603404998779297, "Y": 0.6974174380302429}, {"X": 0.4726022481918335, "Y": 0.6977121233940125}, {"X": 0.4708004295825958, "Y": 0.7524058818817139}]}, "Id": "e904beaf-4a08-43e8-9682-9cfb039b4c70"}, {"BlockType": "WORD", "Confidence": 93.41338348388672, "Text": "TEMP:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01328566949814558, "Height": 0.02783743292093277, "Left": 0.46533775329589844, "Top": 0.5163719654083252}, "Polygon": [{"X": 0.46533775329589844, "Y": 0.5438752770423889}, {"X": 0.4662385582923889, "Y": 0.5163719654083252}, {"X": 0.4786234200000763, "Y": 0.5167127847671509}, {"X": 0.4777175486087799, "Y": 0.544209361076355}]}, "Id": "32f5b164-e993-4cf2-a530-cba250e0d4da"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012429512105882168, "Height": 0.017573894932866096, "Left": 0.46702709794044495, "Top": 0.48439064621925354}, "Polygon": [{"X": 0.46702709794044495, "Y": 0.5016347765922546}, {"X": 0.4675919711589813, "Y": 0.48439064621925354}, {"X": 0.4794566333293915, "Y": 0.4847244620323181}, {"X": 0.47888869047164917, "Y": 0.5019645690917969}]}, "Id": "36d7b56a-1c6c-40a6-a79f-b2f73edf2b62"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "EXCH:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01320676039904356, "Height": 0.026945006102323532, "Left": 0.46764907240867615, "Top": 0.45313310623168945}, "Polygon": [{"X": 0.46764907240867615, "Y": 0.47973009943962097}, {"X": 0.46852028369903564, "Y": 0.45313310623168945}, {"X": 0.4808558523654938, "Y": 0.4534875750541687}, {"X": 0.4799797534942627, "Y": 0.4800781011581421}]}, "Id": "5457670a-8880-4e06-b37d-f9caade9b157"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "VESSEL:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013728323392570019, "Height": 0.039724238216876984, "Left": 0.47689756751060486, "Top": 0.7134912014007568}, "Polygon": [{"X": 0.47689756751060486, "Y": 0.7529301643371582}, {"X": 0.4782005846500397, "Y": 0.7134912014007568}, {"X": 0.49062588810920715, "Y": 0.7137860655784607}, {"X": 0.48931553959846497, "Y": 0.7532154321670532}]}, "Id": "e071f999-c726-4052-bfea-0c4c6ca7d493"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "MVEG522R", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013976113870739937, "Height": 0.04601170867681503, "Left": 0.47840335965156555, "Top": 0.6626646518707275}, "Polygon": [{"X": 0.47840335965156555, "Y": 0.7083794474601746}, {"X": 0.47991377115249634, "Y": 0.6626646518707275}, {"X": 0.49237948656082153, "Y": 0.6629726886749268}, {"X": 0.49086058139801025, "Y": 0.7086763381958008}]}, "Id": "622bb9c4-4547-41eb-9569-a35840819422"}, {"BlockType": "WORD", "Confidence": 84.32855987548828, "Text": "VENT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013262175023555756, "Height": 0.027749493718147278, "Left": 0.48391425609588623, "Top": 0.5167261362075806}, "Polygon": [{"X": 0.48391425609588623, "Y": 0.5441422462463379}, {"X": 0.4848201274871826, "Y": 0.5167261362075806}, {"X": 0.4971764385700226, "Y": 0.517066240310669}, {"X": 0.4962655305862427, "Y": 0.5444756150245667}]}, "Id": "0cf1e245-fa16-4f7f-947f-39050b493c2d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARGO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013429827988147736, "Height": 0.029217369854450226, "Left": 0.49518442153930664, "Top": 0.7237856388092041}, "Polygon": [{"X": 0.49518442153930664, "Y": 0.752716600894928}, {"X": 0.4961485266685486, "Y": 0.7237856388092041}, {"X": 0.5086142420768738, "Y": 0.7240790724754333}, {"X": 0.507644772529602, "Y": 0.7530030012130737}]}, "Id": "fffaf3c8-8a23-4066-9234-84db75e8cf06"}, {"BlockType": "WORD", "Confidence": 85.28280639648438, "Text": "WT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012764913029968739, "Height": 0.01607075147330761, "Left": 0.496507853269577, "Top": 0.7047086358070374}, "Polygon": [{"X": 0.496507853269577, "Y": 0.7204905152320862}, {"X": 0.49703383445739746, "Y": 0.7047086358070374}, {"X": 0.5092727541923523, "Y": 0.7050012946128845}, {"X": 0.508743941783905, "Y": 0.7207793593406677}]}, "Id": "faa39692-b6d3-4b5a-a6b5-cbff0e53cf3b"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "25792", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013199204578995705, "Height": 0.027823932468891144, "Left": 0.4973660409450531, "Top": 0.6708202958106995}, "Polygon": [{"X": 0.4973660409450531, "Y": 0.6983492374420166}, {"X": 0.4982835650444031, "Y": 0.6708202958106995}, {"X": 0.5105652809143066, "Y": 0.6711220145225525}, {"X": 0.5096427202224731, "Y": 0.6986442804336548}]}, "Id": "59bd6164-7764-4775-b6ff-4140d86b11e3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012259037233889103, "Height": 0.0113290436565876, "Left": 0.49874186515808105, "Top": 0.6543487310409546}, "Polygon": [{"X": 0.49874186515808105, "Y": 0.6653845310211182}, {"X": 0.49910974502563477, "Y": 0.6543487310409546}, {"X": 0.51100093126297, "Y": 0.6546446084976196}, {"X": 0.5106310844421387, "Y": 0.6656777858734131}]}, "Id": "3ead3381-0fd7-443e-abbc-b501ec161aab"}, {"BlockType": "WORD", "Confidence": 99.08402252197266, "Text": "HAZARD:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013657238334417343, "Height": 0.038098182529211044, "Left": 0.5024366974830627, "Top": 0.5059131979942322}, "Polygon": [{"X": 0.5024366974830627, "Y": 0.5436766743659973}, {"X": 0.5036953091621399, "Y": 0.5059131979942322}, {"X": 0.516093909740448, "Y": 0.506257176399231}, {"X": 0.5148283839225769, "Y": 0.5440114140510559}]}, "Id": "90e0d4b9-95ed-4db7-8514-333144699607"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "SEALS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013197077438235283, "Height": 0.0331355519592762, "Left": 0.5139113068580627, "Top": 0.7199339866638184}, "Polygon": [{"X": 0.5139113068580627, "Y": 0.7527916431427002}, {"X": 0.5150157809257507, "Y": 0.7199339866638184}, {"X": 0.527108371257782, "Y": 0.7202196717262268}, {"X": 0.5259979367256165, "Y": 0.7530695199966431}]}, "Id": "15deb286-4f5d-4038-b5bd-bb63b213f23e"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "EU28454579", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014252189546823502, "Height": 0.056404683738946915, "Left": 0.5151935815811157, "Top": 0.657780110836029}, "Polygon": [{"X": 0.5151935815811157, "Y": 0.7138914465904236}, {"X": 0.517079770565033, "Y": 0.657780110836029}, {"X": 0.5294457674026489, "Y": 0.6580870747566223}, {"X": 0.5275492668151855, "Y": 0.7141848206520081}]}, "Id": "07c17332-2906-4559-9ff4-4254172640f4"}, {"BlockType": "WORD", "Confidence": 99.20360565185547, "Text": "RELEASE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01374824158847332, "Height": 0.0440969318151474, "Left": 0.5207757353782654, "Top": 0.5003316402435303}, "Polygon": [{"X": 0.5207757353782654, "Y": 0.5440971255302429}, {"X": 0.522246778011322, "Y": 0.5003316402435303}, {"X": 0.5345239639282227, "Y": 0.5006736516952515}, {"X": 0.5330448746681213, "Y": 0.5444285869598389}]}, "Id": "1038eea9-c09e-47db-9160-20c8a629aa54"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pick", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014659504406154156, "Height": 0.02667977474629879, "Left": 0.5323700308799744, "Top": 0.7266626358032227}, "Polygon": [{"X": 0.5323700308799744, "Y": 0.753026008605957}, {"X": 0.5332638025283813, "Y": 0.7266626358032227}, {"X": 0.5470294952392578, "Y": 0.7269862294197083}, {"X": 0.546130359172821, "Y": 0.7533424496650696}]}, "Id": "31849436-41c8-4ed5-99f5-682a6064960d"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "FCIU9733694", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0164777934551239, "Height": 0.0722285658121109, "Left": 0.5335031151771545, "Top": 0.6493735909461975}, "Polygon": [{"X": 0.5335031151771545, "Y": 0.7212710380554199}, {"X": 0.5359407067298889, "Y": 0.6493735909461975}, {"X": 0.5499809384346008, "Y": 0.6497244834899902}, {"X": 0.5475283265113831, "Y": 0.7216021418571472}]}, "Id": "65a021c0-4a1f-4521-a40d-0620479a27ae"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "from", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01448976993560791, "Height": 0.026771053671836853, "Left": 0.5362831950187683, "Top": 0.6182958483695984}, "Polygon": [{"X": 0.5362831950187683, "Y": 0.6447260975837708}, {"X": 0.5371793508529663, "Y": 0.6182958483695984}, {"X": 0.5507729649543762, "Y": 0.6186437606811523}, {"X": 0.5498714447021484, "Y": 0.6450669169425964}]}, "Id": "49bd0fa6-dc34-4086-8017-4232a7d3f68a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "GRID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01455653551965952, "Height": 0.025955695658922195, "Left": 0.5371527075767517, "Top": 0.5876383781433105}, "Polygon": [{"X": 0.5371527075767517, "Y": 0.6132425665855408}, {"X": 0.5380207896232605, "Y": 0.5876383781433105}, {"X": 0.5517092347145081, "Y": 0.587996780872345}, {"X": 0.5508359670639038, "Y": 0.613594114780426}]}, "Id": "eae87a6a-c57f-4065-885f-03cb9e6a7ea3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BGR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01452385913580656, "Height": 0.02016674540936947, "Left": 0.5380445718765259, "Top": 0.5625924468040466}, "Polygon": [{"X": 0.5380445718765259, "Y": 0.5823951959609985}, {"X": 0.5387158989906311, "Y": 0.5625924468040466}, {"X": 0.5525684356689453, "Y": 0.5629618763923645}, {"X": 0.5518930554389954, "Y": 0.5827592015266418}]}, "Id": "e8dd1392-382b-40b1-9926-8e89245ca041"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "(F1903A)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017829306423664093, "Height": 0.04963615909218788, "Left": 0.5389990210533142, "Top": 0.5066415071487427}, "Polygon": [{"X": 0.5389990210533142, "Y": 0.5558449625968933}, {"X": 0.5406671166419983, "Y": 0.5066415071487427}, {"X": 0.5568283796310425, "Y": 0.5070899128913879}, {"X": 0.5551484823226929, "Y": 0.5562776923179626}]}, "Id": "8d5fadd2-2d0e-4e9b-9f2e-001f36a4ac29"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "WELCOME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015207146294414997, "Height": 0.03801954910159111, "Left": 0.6614872813224792, "Top": 0.7188262343406677}, "Polygon": [{"X": 0.6614872813224792, "Y": 0.7565275430679321}, {"X": 0.662841260433197, "Y": 0.7188262343406677}, {"X": 0.676694393157959, "Y": 0.7191547751426697}, {"X": 0.6753326654434204, "Y": 0.7568458318710327}]}, "Id": "6e3e133a-db3d-4378-9fb8-c3109c2e9cd0"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013458377681672573, "Height": 0.010788766667246819, "Left": 0.6633418798446655, "Top": 0.7044068574905396}, "Polygon": [{"X": 0.6633418798446655, "Y": 0.7148844003677368}, {"X": 0.6637182235717773, "Y": 0.7044068574905396}, {"X": 0.6768002510070801, "Y": 0.704720675945282}, {"X": 0.6764218807220459, "Y": 0.7151955962181091}]}, "Id": "fb41781f-d711-418e-8213-bf42fc8ef7c3"}, {"BlockType": "WORD", "Confidence": 96.56230163574219, "Text": "PNCT.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013838809914886951, "Height": 0.025340158492326736, "Left": 0.664129912853241, "Top": 0.6757680177688599}, "Polygon": [{"X": 0.664129912853241, "Y": 0.700796902179718}, {"X": 0.6650290489196777, "Y": 0.6757680177688599}, {"X": 0.6779687404632568, "Y": 0.6760856509208679}, {"X": 0.6770647764205933, "Y": 0.7011081576347351}]}, "Id": "d6332241-d5a4-46cd-a7c5-b286060ee50f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PLEASE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014478219673037529, "Height": 0.03195037692785263, "Left": 0.6652016043663025, "Top": 0.6348732113838196}, "Polygon": [{"X": 0.6652016043663025, "Y": 0.666493833065033}, {"X": 0.6663374304771423, "Y": 0.6348732113838196}, {"X": 0.679679811000824, "Y": 0.6352112293243408}, {"X": 0.6785377264022827, "Y": 0.6668235659599304}]}, "Id": "4c0ff48e-ca44-454b-adf0-7fc7217e3880"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FOLLOW", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014502028934657574, "Height": 0.03247424215078354, "Left": 0.6664314866065979, "Top": 0.5984587669372559}, "Polygon": [{"X": 0.6664314866065979, "Y": 0.6305938959121704}, {"X": 0.6675857901573181, "Y": 0.5984587669372559}, {"X": 0.6809335350990295, "Y": 0.5988063812255859}, {"X": 0.6797728538513184, "Y": 0.6309329867362976}]}, "Id": "f0221f17-c0b9-4a25-9d55-87f8dfa3b5f8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SAFETY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014968384057283401, "Height": 0.03203054890036583, "Left": 0.6673811078071594, "Top": 0.5627844333648682}, "Polygon": [{"X": 0.6673811078071594, "Y": 0.5944538712501526}, {"X": 0.6685184836387634, "Y": 0.5627844333648682}, {"X": 0.682349443435669, "Y": 0.5631541013717651}, {"X": 0.6812055706977844, "Y": 0.5948149561882019}]}, "Id": "72d77514-cdc4-45e8-b898-ce5869952c6a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "RULES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014847337268292904, "Height": 0.027048209682106972, "Left": 0.668552577495575, "Top": 0.5327890515327454}, "Polygon": [{"X": 0.668552577495575, "Y": 0.559465229511261}, {"X": 0.6695106029510498, "Y": 0.5327890515327454}, {"X": 0.6833999156951904, "Y": 0.533168375492096}, {"X": 0.6824364066123962, "Y": 0.5598372220993042}]}, "Id": "60b9f145-8c88-4d58-90e4-6cdf173838e3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "AND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013869252055883408, "Height": 0.016813594847917557, "Left": 0.6699844002723694, "Top": 0.5124939680099487}, "Polygon": [{"X": 0.6699844002723694, "Y": 0.5289440751075745}, {"X": 0.6705752611160278, "Y": 0.5124939680099487}, {"X": 0.6838536262512207, "Y": 0.5128618478775024}, {"X": 0.6832595467567444, "Y": 0.5293076038360596}]}, "Id": "8d7d754f-e308-48cc-9b1b-c900c1ccbddd"}, {"BlockType": "WORD", "Confidence": 87.72321319580078, "Text": "REGULATIONS.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016186868771910667, "Height": 0.06253442913293839, "Left": 0.6703605651855469, "Top": 0.44700366258621216}, "Polygon": [{"X": 0.6703605651855469, "Y": 0.5091509819030762}, {"X": 0.6725924611091614, "Y": 0.44700366258621216}, {"X": 0.6865473985671997, "Y": 0.44740793108940125}, {"X": 0.6843026876449585, "Y": 0.5095381140708923}]}, "Id": "b2277670-979f-4099-992f-5d682d731102"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01406662818044424, "Height": 0.011528205126523972, "Left": 0.6796875596046448, "Top": 0.7449592351913452}, "Polygon": [{"X": 0.6796875596046448, "Y": 0.756173312664032}, {"X": 0.6800934672355652, "Y": 0.7449592351913452}, {"X": 0.6937541365623474, "Y": 0.7452763915061951}, {"X": 0.6933459639549255, "Y": 0.7564874887466431}]}, "Id": "160a20d0-d67a-4a8f-ac17-bd114897fbbd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CONTACT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015530675649642944, "Height": 0.040141597390174866, "Left": 0.6802369356155396, "Top": 0.7004594206809998}, "Polygon": [{"X": 0.6802369356155396, "Y": 0.7402728199958801}, {"X": 0.6816779971122742, "Y": 0.7004594206809998}, {"X": 0.6957675814628601, "Y": 0.7007986307144165}, {"X": 0.6943181753158569, "Y": 0.7406010031700134}]}, "Id": "f7d73718-0ebc-4b2e-bfba-0bc5beef3685"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TROUBLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015400719828903675, "Height": 0.0402410589158535, "Left": 0.6819223165512085, "Top": 0.6561813950538635}, "Polygon": [{"X": 0.6819223165512085, "Y": 0.6960854530334473}, {"X": 0.6833667755126953, "Y": 0.6561813950538635}, {"X": 0.6973230242729187, "Y": 0.656529426574707}, {"X": 0.6958703398704529, "Y": 0.6964224576950073}]}, "Id": "acfb3484-55d3-40ec-bc5f-6a28c8dd786f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "WINDOW:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015500201843678951, "Height": 0.03919791802763939, "Left": 0.6834551095962524, "Top": 0.6127864122390747}, "Polygon": [{"X": 0.6834551095962524, "Y": 0.6516318321228027}, {"X": 0.6848611831665039, "Y": 0.6127864122390747}, {"X": 0.6989552974700928, "Y": 0.6131496429443359}, {"X": 0.6975411176681519, "Y": 0.6519843339920044}]}, "Id": "a9246129-53b7-4788-942b-54501084ace5"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "TXT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014549586921930313, "Height": 0.01720721647143364, "Left": 0.6851757168769836, "Top": 0.5900838971138}, "Polygon": [{"X": 0.6851757168769836, "Y": 0.6069303750991821}, {"X": 0.6857854723930359, "Y": 0.5900838971138}, {"X": 0.6997252702713013, "Y": 0.5904492735862732}, {"X": 0.6991119980812073, "Y": 0.6072911024093628}]}, "Id": "7c0e7e95-2bb0-42ba-9756-9ebe59ea8ccb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "MSG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014578763395547867, "Height": 0.017418043687939644, "Left": 0.685890793800354, "Top": 0.568580150604248}, "Polygon": [{"X": 0.685890793800354, "Y": 0.5856311321258545}, {"X": 0.6865079998970032, "Y": 0.568580150604248}, {"X": 0.7004695534706116, "Y": 0.5689519047737122}, {"X": 0.6998488306999207, "Y": 0.5859982371330261}]}, "Id": "ec5d6e97-d3a0-4025-8386-12f5de4316a1"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "(888)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017420686781406403, "Height": 0.026682527735829353, "Left": 0.6867308020591736, "Top": 0.5364207625389099}, "Polygon": [{"X": 0.6867308020591736, "Y": 0.5626630187034607}, {"X": 0.6876806616783142, "Y": 0.5364207625389099}, {"X": 0.704151451587677, "Y": 0.5368695855140686}, {"X": 0.7031951546669006, "Y": 0.5631032586097717}]}, "Id": "eaa455c6-b1b4-45eb-95fc-23bd0f5e3cc2"}, {"BlockType": "WORD", "Confidence": 98.22126007080078, "Text": "490-2226", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015491140075027943, "Height": 0.04571228474378586, "Left": 0.68803471326828, "Top": 0.4853631854057312}, "Polygon": [{"X": 0.68803471326828, "Y": 0.5306967496871948}, {"X": 0.6896757483482361, "Y": 0.4853631854057312}, {"X": 0.7035258412361145, "Y": 0.48575425148010254}, {"X": 0.7018755078315735, "Y": 0.5310754776000977}]}, "Id": "6cd3e489-5557-4ffd-ad06-84a108dbbbd7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WITH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014874638989567757, "Height": 0.023151226341724396, "Left": 0.6896262764930725, "Top": 0.4581240713596344}, "Polygon": [{"X": 0.6896262764930725, "Y": 0.48087745904922485}, {"X": 0.6904498934745789, "Y": 0.4581240713596344}, {"X": 0.7045009136199951, "Y": 0.45852822065353394}, {"X": 0.7036726474761963, "Y": 0.4812752902507782}]}, "Id": "f8f213b5-c000-4fba-8937-9ecc5059890c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TICKET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015617524273693562, "Height": 0.03499845042824745, "Left": 0.6905630826950073, "Top": 0.41891026496887207}, "Polygon": [{"X": 0.6905630826950073, "Y": 0.45349442958831787}, {"X": 0.6918148398399353, "Y": 0.41891026496887207}, {"X": 0.7061805725097656, "Y": 0.419334352016449}, {"X": 0.7049214243888855, "Y": 0.4539087116718292}]}, "Id": "498bda25-7c47-4299-8fc4-a7518ecaa68d"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "NUMBER.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01539800874888897, "Height": 0.03893120586872101, "Left": 0.6922253370285034, "Top": 0.3763773441314697}, "Polygon": [{"X": 0.6922253370285034, "Y": 0.4148942828178406}, {"X": 0.6936196088790894, "Y": 0.3763773441314697}, {"X": 0.7076233625411987, "Y": 0.3768022656440735}, {"X": 0.7062211036682129, "Y": 0.41530853509902954}]}, "Id": "db1204f3-d2b6-4ef6-9838-357599ba9f45"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SAVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014140435494482517, "Height": 0.021136805415153503, "Left": 0.6986749768257141, "Top": 0.7356094717979431}, "Polygon": [{"X": 0.6986749768257141, "Y": 0.7564386129379272}, {"X": 0.6994350552558899, "Y": 0.7356094717979431}, {"X": 0.7128154039382935, "Y": 0.7359226942062378}, {"X": 0.7120511531829834, "Y": 0.7567462921142578}]}, "Id": "fc7e2506-6a71-4fcd-87d3-b957e065a5f6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "THIS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014061512425541878, "Height": 0.020517146214842796, "Left": 0.6995881199836731, "Top": 0.7108801603317261}, "Polygon": [{"X": 0.6995881199836731, "Y": 0.7310843467712402}, {"X": 0.7003254294395447, "Y": 0.7108801603317261}, {"X": 0.7136496305465698, "Y": 0.7111983895301819}, {"X": 0.7129083275794983, "Y": 0.731397271156311}]}, "Id": "a55e765f-8bdc-4cf2-846e-a606f42d24cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014265869744122028, "Height": 0.022170543670654297, "Left": 0.7004950046539307, "Top": 0.6852173805236816}, "Polygon": [{"X": 0.7004950046539307, "Y": 0.7070653438568115}, {"X": 0.7012922763824463, "Y": 0.6852173805236816}, {"X": 0.7147608995437622, "Y": 0.6855457425117493}, {"X": 0.7139592170715332, "Y": 0.7073879241943359}]}, "Id": "f05b0011-c553-43fa-91ad-2727238070da"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013540518470108509, "Height": 0.011208312585949898, "Left": 0.7014488577842712, "Top": 0.6706629395484924}, "Polygon": [{"X": 0.7014488577842712, "Y": 0.6815499067306519}, {"X": 0.701846182346344, "Y": 0.6706629395484924}, {"X": 0.7149893641471863, "Y": 0.670987069606781}, {"X": 0.7145899534225464, "Y": 0.681871235370636}]}, "Id": "a74a3adc-5054-46ba-887b-31d5977608ef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "EXIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01440573949366808, "Height": 0.021715747192502022, "Left": 0.7020331025123596, "Top": 0.6457006931304932}, "Polygon": [{"X": 0.7020331025123596, "Y": 0.667079508304596}, {"X": 0.7028133273124695, "Y": 0.6457006931304932}, {"X": 0.7164388298988342, "Y": 0.6460433006286621}, {"X": 0.7156543135643005, "Y": 0.6674163937568665}]}, "Id": "bbb73e35-26f4-40da-8617-3c501ddb967b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "THE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014301519840955734, "Height": 0.016120929270982742, "Left": 0.7026768922805786, "Top": 0.6261142492294312}, "Polygon": [{"X": 0.7026768922805786, "Y": 0.6418890953063965}, {"X": 0.7032525539398193, "Y": 0.6261142492294312}, {"X": 0.7169784307479858, "Y": 0.6264646053314209}, {"X": 0.716399610042572, "Y": 0.6422351598739624}]}, "Id": "0cfcd86c-f1d9-43c7-8986-cb1c1771a88d"}, {"BlockType": "WORD", "Confidence": 71.22130584716797, "Text": "TERMINAL.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015194788575172424, "Height": 0.04534100368618965, "Left": 0.703574001789093, "Top": 0.5766939520835876}, "Polygon": [{"X": 0.703574001789093, "Y": 0.6216881275177002}, {"X": 0.7052159309387207, "Y": 0.5766939520835876}, {"X": 0.7187687754631042, "Y": 0.5770528316497803}, {"X": 0.7171178460121155, "Y": 0.6220349669456482}]}, "Id": "155cc09e-a769-462d-8287-eb2cebc94d1f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b67a9aef-8bb5-4af1-a1c5-d65b946af013", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b67a9aef-8bb5-4af1-a1c5-d65b946af013", "content-type": "application/x-amz-json-1.1", "content-length": "49920", "date": "Wed, 24 Sep 2025 13:32:29 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "92d2ad95-1dd1-4056-802c-dd733d05619d", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:32 GMT", "content-type": "application/json", "content-length": "1722", "connection": "keep-alive", "x-amzn-requestid": "92d2ad95-1dd1-4056-802c-dd733d05619d"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We need to determine doc type.\n\nContent: Looks like a pickup card or terminal card. Keywords: \"PNCT\", \"PICKUP\". No obvious invoice, BOL, POD, etc. Not a packing list. Could be a load card or gate card. Could be ingate document? It shows pickup info, container, etc. Might be an ingate or outgate? It says \"Welcome to PNCT\" terminal. \"Save this card to exit the terminal.\" So it's a card for exit. Might be an ingate or outgate? The content is about pickup and container. Could be a \"ingate\" document: record of vehicle entering. But the card says to exit the terminal. Could be an outgoing load card. The doc type list: ingate, outgate. The card is for exit. Likely \"outgate\" because it's for exiting terminal. But it says pickup. Or maybe it's a \"ingate\" because it shows pickup and container. But outgate typically has departure time, exit. The page says \"WELCOME TO PNCT\" \"SAVE THIS CARD TO EXIT THE TERMINAL.\" So use to exit, maybe it's a card to be used as an exit. This seems like an \"ingate\" or \"outgate\" card. Not sure. The label is \"PNCT\" maybe \"Port Terminal Card Terminal\"? Might be a \"ingate\" or \"outgate\" for container terminal. The doc type list: ingate and outgate.\n\nWe should pick one. The card is for exit. That is \"outgate\". So doc_type = outgate.\n\nReturn JSON with page_no 1, doc_type outgate."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2780, "outputTokens": 371, "totalTokens": 3151}, "metrics": {"latencyMs": 1797}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/d4d5d0cd_YASUN3YXXTIFXRHDIKNF.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nPNCT\nPICKUP 566367\nMSC:\nLOAD OUT\nSXIY: SK<PERSON><PERSON><PERSON>E EXPRESS (INLAND E 2025/06/06 13:18:00\nCONTAINER: FCIU9733694\nSIZE/TYPE: 40 GP 96\nCARD ID: 231466\nCHASSIS: AV0Z410014\nSIZE/TYPE:\nGENERATOR:\nTEMP: AIR EXCH:\nVESSEL: MVEG522R\nVENT:\nCARGO WT: 25792 LB\nHAZARD:\nSEALS: EU28454579\nRELEASE:\nPick FCIU9733694 from GRID BGR (F1903A)\nWELCOME TO PNCT. PLEASE FOLLOW SAFETY RULES AND REGULATIONS.\nTO CONTACT TROUBLE WINDOW: TXT MSG (888) 490-2226 WITH TICKET NUMBER.\nSAVE THIS CARD TO EXIT THE TERMINAL.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}