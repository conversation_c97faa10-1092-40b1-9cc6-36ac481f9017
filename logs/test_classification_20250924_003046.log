2025-09-24 00:30:46,186 - INFO - Logging initialized. Log file: logs/test_classification_20250924_003046.log
2025-09-24 00:30:46,187 - INFO - 📁 Found 9 files to process
2025-09-24 00:30:46,187 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:30:46,187 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 00:30:46,187 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 00:30:46,188 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 00:30:46,188 - INFO - ⬆️ [00:30:46] Uploading: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:48,067 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf -> s3://document-extraction-logistically/temp/1e26a804_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:48,068 - INFO - 🔍 [00:30:48] Starting classification: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:48,069 - INFO - ⬆️ [00:30:48] Uploading: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:30:48,070 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:48,114 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:48,121 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1e26a804_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:48,122 - INFO - Processing PDF from S3...
2025-09-24 00:30:48,122 - INFO - Downloading PDF from S3 to /tmp/tmp3zwey9pa/1e26a804_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:49,677 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf -> s3://document-extraction-logistically/temp/30230aa9_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:30:49,678 - INFO - 🔍 [00:30:49] Starting classification: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:30:49,679 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:30:49,681 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:49,683 - INFO - ⬆️ [00:30:49] Uploading: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:30:49,684 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:49,767 - INFO - Splitting PDF 1e26a804_CZ7K9JE7PH88JUBC19JF into 1 pages
2025-09-24 00:30:49,775 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:49,785 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/30230aa9_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:30:49,786 - INFO - Processing PDF from S3...
2025-09-24 00:30:49,787 - INFO - Downloading PDF from S3 to /tmp/tmp7k05o6bf/30230aa9_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:30:49,855 - INFO - Split PDF into 1 pages
2025-09-24 00:30:49,855 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:49,855 - INFO - Expected pages: [1]
2025-09-24 00:30:51,107 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf -> s3://document-extraction-logistically/temp/cb838ada_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:30:51,108 - INFO - 🔍 [00:30:51] Starting classification: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:30:51,131 - INFO - ⬆️ [00:30:51] Uploading: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:30:51,132 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:51,189 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:51,195 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cb838ada_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:30:51,195 - INFO - Processing PDF from S3...
2025-09-24 00:30:51,195 - INFO - Downloading PDF from S3 to /tmp/tmp25vsaxfg/cb838ada_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:30:52,021 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:30:52,022 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:52,027 - INFO - Splitting PDF 30230aa9_DGAKPGYVH59IXR7KRKZS into 4 pages
2025-09-24 00:30:52,065 - INFO - Split PDF into 4 pages
2025-09-24 00:30:52,065 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:52,065 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 00:30:52,130 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf -> s3://document-extraction-logistically/temp/916e7a41_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:30:52,130 - INFO - 🔍 [00:30:52] Starting classification: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:30:52,132 - INFO - ⬆️ [00:30:52] Uploading: GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:30:52,134 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:52,163 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:52,168 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/916e7a41_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:30:52,169 - INFO - Processing PDF from S3...
2025-09-24 00:30:52,169 - INFO - Downloading PDF from S3 to /tmp/tmphsks03jo/916e7a41_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:30:52,768 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf -> s3://document-extraction-logistically/temp/c125df7a_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:30:52,768 - INFO - 🔍 [00:30:52] Starting classification: GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:30:52,770 - INFO - ⬆️ [00:30:52] Uploading: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:30:52,773 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:52,799 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:52,805 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c125df7a_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:30:52,814 - INFO - Processing PDF from S3...
2025-09-24 00:30:52,814 - INFO - Downloading PDF from S3 to /tmp/tmpgrdu967g/c125df7a_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:30:53,478 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf -> s3://document-extraction-logistically/temp/28050897_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:30:53,480 - INFO - 🔍 [00:30:53] Starting classification: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:30:53,481 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 00:30:53,482 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:53,483 - INFO - ⬆️ [00:30:53] Uploading: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:30:53,486 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:53,504 - INFO - Splitting PDF cb838ada_ECY73YCNA7IPQSM8NAKW into 1 pages
2025-09-24 00:30:53,532 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:53,547 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/28050897_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:30:53,547 - INFO - Processing PDF from S3...
2025-09-24 00:30:53,548 - INFO - Downloading PDF from S3 to /tmp/tmp10v3b2yv/28050897_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:30:53,557 - INFO - Split PDF into 1 pages
2025-09-24 00:30:53,557 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:53,557 - INFO - Expected pages: [1]
2025-09-24 00:30:54,139 - INFO - Page 1: Extracted 1384 characters, 71 lines from 1e26a804_CZ7K9JE7PH88JUBC19JF_5319fc60_page_001.pdf
2025-09-24 00:30:54,139 - INFO - Successfully processed page 1
2025-09-24 00:30:54,140 - INFO - Combined 1 pages into final text
2025-09-24 00:30:54,140 - INFO - Text validation for 1e26a804_CZ7K9JE7PH88JUBC19JF: 1401 characters, 1 pages
2025-09-24 00:30:54,140 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:30:54,140 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:30:54,163 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 00:30:54,164 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:54,167 - INFO - Splitting PDF 916e7a41_EDYM381JJDTUB87YAAPI into 1 pages
2025-09-24 00:30:54,192 - INFO - Split PDF into 1 pages
2025-09-24 00:30:54,192 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:54,193 - INFO - Expected pages: [1]
2025-09-24 00:30:54,334 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:30:54,335 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:54,340 - INFO - Splitting PDF c125df7a_GMCKX2ERTX05S300COLL into 3 pages
2025-09-24 00:30:54,371 - INFO - Split PDF into 3 pages
2025-09-24 00:30:54,371 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:54,372 - INFO - Expected pages: [1, 2, 3]
2025-09-24 00:30:54,676 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf -> s3://document-extraction-logistically/temp/1f3e745f_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:30:54,677 - INFO - 🔍 [00:30:54] Starting classification: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:30:54,678 - INFO - ⬆️ [00:30:54] Uploading: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:30:54,679 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:54,700 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:54,714 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1f3e745f_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:30:54,714 - INFO - Processing PDF from S3...
2025-09-24 00:30:54,714 - INFO - Downloading PDF from S3 to /tmp/tmprlmdgntn/1f3e745f_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:30:55,317 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:30:55,317 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:55,325 - INFO - Splitting PDF 28050897_T51X0UC3WJL168AL2PGB into 1 pages
2025-09-24 00:30:55,351 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf -> s3://document-extraction-logistically/temp/8bc588ca_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:30:55,354 - INFO - 🔍 [00:30:55] Starting classification: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:30:55,355 - INFO - Split PDF into 1 pages
2025-09-24 00:30:55,356 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:55,357 - INFO - Expected pages: [1]
2025-09-24 00:30:55,357 - INFO - ⬆️ [00:30:55] Uploading: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:30:55,358 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:55,399 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:55,406 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8bc588ca_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:30:55,412 - INFO - Processing PDF from S3...
2025-09-24 00:30:55,412 - INFO - Downloading PDF from S3 to /tmp/tmp6ajilbbx/8bc588ca_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:30:55,984 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1e26a804_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:56,134 - INFO - Page 2: Extracted 579 characters, 42 lines from 30230aa9_DGAKPGYVH59IXR7KRKZS_0cdb52e7_page_002.pdf
2025-09-24 00:30:56,135 - INFO - Successfully processed page 2
2025-09-24 00:30:56,900 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf -> s3://document-extraction-logistically/temp/f3cca44e_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:30:56,900 - INFO - 🔍 [00:30:56] Starting classification: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:30:56,905 - INFO - Initializing TextractProcessor...
2025-09-24 00:30:56,960 - INFO - Initializing BedrockProcessor...
2025-09-24 00:30:56,969 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f3cca44e_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:30:56,978 - INFO - Processing PDF from S3...
2025-09-24 00:30:56,986 - INFO - Downloading PDF from S3 to /tmp/tmp_po9d2ty/f3cca44e_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:30:57,131 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 00:30:57,151 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:57,205 - INFO - Page 4: Extracted 3255 characters, 170 lines from 30230aa9_DGAKPGYVH59IXR7KRKZS_0cdb52e7_page_004.pdf
2025-09-24 00:30:57,221 - INFO - Successfully processed page 4
2025-09-24 00:30:57,237 - INFO - Splitting PDF 1f3e745f_TCM8BE9P052RZLZGHQIW into 1 pages
2025-09-24 00:30:57,290 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:30:57,296 - INFO - Split PDF into 1 pages
2025-09-24 00:30:57,301 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:57,311 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:57,320 - INFO - Expected pages: [1]
2025-09-24 00:30:57,351 - INFO - Page 3: Extracted 541 characters, 39 lines from 30230aa9_DGAKPGYVH59IXR7KRKZS_0cdb52e7_page_003.pdf
2025-09-24 00:30:57,378 - INFO - Successfully processed page 3
2025-09-24 00:30:57,399 - INFO - Splitting PDF 8bc588ca_V44T1WF2N7RT33JSFU5F into 1 pages
2025-09-24 00:30:57,493 - INFO - 

CZ7K9JE7PH88JUBC19JF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:30:57,493 - INFO - 

✓ Saved result: output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:30:57,498 - INFO - Split PDF into 1 pages
2025-09-24 00:30:57,499 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:57,499 - INFO - Expected pages: [1]
2025-09-24 00:30:57,784 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1e26a804_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:30:57,857 - INFO - Page 1: Extracted 2957 characters, 137 lines from 30230aa9_DGAKPGYVH59IXR7KRKZS_0cdb52e7_page_001.pdf
2025-09-24 00:30:57,857 - INFO - Successfully processed page 1
2025-09-24 00:30:57,858 - INFO - Combined 4 pages into final text
2025-09-24 00:30:57,858 - INFO - Text validation for 30230aa9_DGAKPGYVH59IXR7KRKZS: 7406 characters, 4 pages
2025-09-24 00:30:57,859 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:30:57,859 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:30:58,042 - INFO - Page 3: Extracted 526 characters, 49 lines from c125df7a_GMCKX2ERTX05S300COLL_b05a2fdc_page_003.pdf
2025-09-24 00:30:58,043 - INFO - Successfully processed page 3
2025-09-24 00:30:58,342 - INFO - Page 1: Extracted 1271 characters, 100 lines from c125df7a_GMCKX2ERTX05S300COLL_b05a2fdc_page_001.pdf
2025-09-24 00:30:58,342 - INFO - Successfully processed page 1
2025-09-24 00:30:58,458 - INFO - Page 2: Extracted 1411 characters, 134 lines from c125df7a_GMCKX2ERTX05S300COLL_b05a2fdc_page_002.pdf
2025-09-24 00:30:58,458 - INFO - Successfully processed page 2
2025-09-24 00:30:58,458 - INFO - Combined 3 pages into final text
2025-09-24 00:30:58,459 - INFO - Text validation for c125df7a_GMCKX2ERTX05S300COLL: 3263 characters, 3 pages
2025-09-24 00:30:58,459 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:30:58,459 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:30:59,009 - INFO - Page 1: Extracted 1545 characters, 72 lines from cb838ada_ECY73YCNA7IPQSM8NAKW_60a7aa6b_page_001.pdf
2025-09-24 00:30:59,010 - INFO - Successfully processed page 1
2025-09-24 00:30:59,010 - INFO - Combined 1 pages into final text
2025-09-24 00:30:59,010 - INFO - Text validation for cb838ada_ECY73YCNA7IPQSM8NAKW: 1562 characters, 1 pages
2025-09-24 00:30:59,010 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:30:59,011 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:30:59,332 - INFO - Page 1: Extracted 1163 characters, 75 lines from 916e7a41_EDYM381JJDTUB87YAAPI_09439658_page_001.pdf
2025-09-24 00:30:59,332 - INFO - Successfully processed page 1
2025-09-24 00:30:59,333 - INFO - Combined 1 pages into final text
2025-09-24 00:30:59,333 - INFO - Text validation for 916e7a41_EDYM381JJDTUB87YAAPI: 1180 characters, 1 pages
2025-09-24 00:30:59,334 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:30:59,334 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:30:59,537 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 00:30:59,537 - INFO - Splitting PDF into individual pages...
2025-09-24 00:30:59,541 - INFO - Splitting PDF f3cca44e_Z106V9IKGLMUAGC3VOK8 into 2 pages
2025-09-24 00:30:59,553 - INFO - Split PDF into 2 pages
2025-09-24 00:30:59,554 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:30:59,554 - INFO - Expected pages: [1, 2]
2025-09-24 00:31:00,437 - INFO - Page 1: Extracted 2010 characters, 210 lines from 28050897_T51X0UC3WJL168AL2PGB_db299960_page_001.pdf
2025-09-24 00:31:00,437 - INFO - Successfully processed page 1
2025-09-24 00:31:00,437 - INFO - Combined 1 pages into final text
2025-09-24 00:31:00,438 - INFO - Text validation for 28050897_T51X0UC3WJL168AL2PGB: 2027 characters, 1 pages
2025-09-24 00:31:00,438 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:31:00,438 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:31:01,016 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cb838ada_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:31:01,365 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/916e7a41_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:31:01,462 - INFO - 

ECY73YCNA7IPQSM8NAKW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:31:01,462 - INFO - 

✓ Saved result: output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:31:01,466 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c125df7a_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:31:01,796 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cb838ada_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:31:02,179 - INFO - 

EDYM381JJDTUB87YAAPI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:31:02,179 - INFO - 

✓ Saved result: output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:31:02,270 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/28050897_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:31:02,513 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/916e7a41_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:31:03,498 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-24 00:31:03,537 - ERROR - Processing failed for s3://document-extraction-logistically/temp/30230aa9_DGAKPGYVH59IXR7KRKZS.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:31:03,553 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/30230aa9_DGAKPGYVH59IXR7KRKZS.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:31:03,590 - INFO - 

GMCKX2ERTX05S300COLL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 00:31:03,590 - INFO - 

✓ Saved result: output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:03,594 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 227, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 142, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 00:31:03,800 - INFO - Page 1: Extracted 1341 characters, 86 lines from 1f3e745f_TCM8BE9P052RZLZGHQIW_ca51bf00_page_001.pdf
2025-09-24 00:31:03,821 - INFO - Successfully processed page 1
2025-09-24 00:31:03,836 - INFO - Page 1: Extracted 4288 characters, 110 lines from 8bc588ca_V44T1WF2N7RT33JSFU5F_f5b53ecb_page_001.pdf
2025-09-24 00:31:03,836 - INFO - Combined 1 pages into final text
2025-09-24 00:31:03,837 - INFO - Successfully processed page 1
2025-09-24 00:31:03,837 - INFO - Text validation for 1f3e745f_TCM8BE9P052RZLZGHQIW: 1358 characters, 1 pages
2025-09-24 00:31:03,837 - INFO - Combined 1 pages into final text
2025-09-24 00:31:03,838 - INFO - Text validation for 8bc588ca_V44T1WF2N7RT33JSFU5F: 4305 characters, 1 pages
2025-09-24 00:31:03,838 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:31:03,839 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:31:03,842 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:31:03,844 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:31:03,884 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c125df7a_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:31:04,637 - INFO - Page 2: Extracted 727 characters, 48 lines from f3cca44e_Z106V9IKGLMUAGC3VOK8_8fb6bb9e_page_002.pdf
2025-09-24 00:31:04,649 - INFO - Successfully processed page 2
2025-09-24 00:31:04,722 - INFO - 

T51X0UC3WJL168AL2PGB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:31:04,722 - INFO - 

✓ Saved result: output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:31:05,026 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/28050897_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:31:05,028 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:31:05,033 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 269, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 227, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 142, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 00:31:05,337 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/30230aa9_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:31:05,517 - INFO - Page 1: Extracted 1789 characters, 98 lines from f3cca44e_Z106V9IKGLMUAGC3VOK8_8fb6bb9e_page_001.pdf
2025-09-24 00:31:05,517 - INFO - Successfully processed page 1
2025-09-24 00:31:05,517 - INFO - Combined 2 pages into final text
2025-09-24 00:31:05,518 - INFO - Text validation for f3cca44e_Z106V9IKGLMUAGC3VOK8: 2552 characters, 2 pages
2025-09-24 00:31:05,518 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:31:05,518 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:31:05,580 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1f3e745f_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:31:06,003 - INFO - 

TCM8BE9P052RZLZGHQIW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:31:06,004 - INFO - 

✓ Saved result: output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:31:06,131 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8bc588ca_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:31:06,293 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1f3e745f_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:31:07,398 - INFO - 

V44T1WF2N7RT33JSFU5F.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:31:07,398 - INFO - 

✓ Saved result: output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:31:07,695 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8bc588ca_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:31:08,005 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f3cca44e_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:31:08,766 - INFO - 

Z106V9IKGLMUAGC3VOK8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:31:08,766 - INFO - 

✓ Saved result: output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:31:09,129 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f3cca44e_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:31:09,131 - INFO - 
📊 Processing Summary:
2025-09-24 00:31:09,131 - INFO -    Total files: 9
2025-09-24 00:31:09,131 - INFO -    Successful: 8
2025-09-24 00:31:09,131 - INFO -    Failed: 1
2025-09-24 00:31:09,131 - INFO -    Duration: 22.94 seconds
2025-09-24 00:31:09,132 - INFO -    Output directory: output
2025-09-24 00:31:09,132 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:31:09,132 - INFO -    📄 CZ7K9JE7PH88JUBC19JF.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,132 - INFO -    📄 ECY73YCNA7IPQSM8NAKW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,133 - INFO -    📄 EDYM381JJDTUB87YAAPI.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,133 - INFO -    📄 GMCKX2ERTX05S300COLL.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 00:31:09,133 - INFO -    📄 T51X0UC3WJL168AL2PGB.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,133 - INFO -    📄 TCM8BE9P052RZLZGHQIW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,133 - INFO -    📄 V44T1WF2N7RT33JSFU5F.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,134 - INFO -    📄 Z106V9IKGLMUAGC3VOK8.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"}]}
2025-09-24 00:31:09,134 - ERROR - 
❌ Errors:
2025-09-24 00:31:09,134 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf: Unexpected tool response format from Bedrock
2025-09-24 00:31:09,135 - INFO - 
============================================================================================================================================
2025-09-24 00:31:09,135 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:31:09,136 - INFO - ============================================================================================================================================
2025-09-24 00:31:09,136 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:31:09,136 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:31:09,136 - INFO - CZ7K9JE7PH88JUBC19JF.pdf                           1      comm_invoice         run1_CZ7K9JE7PH88JUBC19JF.json                    
2025-09-24 00:31:09,136 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:31:09,136 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:31:09,136 - INFO - 
2025-09-24 00:31:09,136 - INFO - ECY73YCNA7IPQSM8NAKW.pdf                           1      comm_invoice         run1_ECY73YCNA7IPQSM8NAKW.json                    
2025-09-24 00:31:09,137 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:31:09,137 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:31:09,137 - INFO - 
2025-09-24 00:31:09,137 - INFO - EDYM381JJDTUB87YAAPI.pdf                           1      comm_invoice         run1_EDYM381JJDTUB87YAAPI.json                    
2025-09-24 00:31:09,137 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:31:09,137 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:31:09,137 - INFO - 
2025-09-24 00:31:09,137 - INFO - GMCKX2ERTX05S300COLL.pdf                           1      invoice              run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:31:09,138 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:31:09,138 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:09,138 - INFO - 
2025-09-24 00:31:09,138 - INFO - GMCKX2ERTX05S300COLL.pdf                           2      invoice              run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:31:09,138 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:31:09,138 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:09,138 - INFO - 
2025-09-24 00:31:09,139 - INFO - GMCKX2ERTX05S300COLL.pdf                           3      invoice              run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:31:09,139 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:31:09,139 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:09,139 - INFO - 
2025-09-24 00:31:09,139 - INFO - T51X0UC3WJL168AL2PGB.pdf                           1      comm_invoice         run1_T51X0UC3WJL168AL2PGB.json                    
2025-09-24 00:31:09,139 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:31:09,139 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:31:09,139 - INFO - 
2025-09-24 00:31:09,140 - INFO - TCM8BE9P052RZLZGHQIW.pdf                           1      comm_invoice         run1_TCM8BE9P052RZLZGHQIW.json                    
2025-09-24 00:31:09,140 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:31:09,140 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:31:09,140 - INFO - 
2025-09-24 00:31:09,141 - INFO - V44T1WF2N7RT33JSFU5F.pdf                           1      comm_invoice         run1_V44T1WF2N7RT33JSFU5F.json                    
2025-09-24 00:31:09,141 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:31:09,141 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:31:09,142 - INFO - 
2025-09-24 00:31:09,142 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           1      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 00:31:09,142 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:31:09,142 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:31:09,142 - INFO - 
2025-09-24 00:31:09,143 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           2      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 00:31:09,143 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:31:09,143 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:31:09,144 - INFO - 
2025-09-24 00:31:09,144 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:31:09,144 - INFO - Total entries: 11
2025-09-24 00:31:09,144 - INFO - ============================================================================================================================================
2025-09-24 00:31:09,144 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:31:09,145 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:31:09,145 - INFO -   1. CZ7K9JE7PH88JUBC19JF.pdf            Page 1   → comm_invoice    | run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:31:09,146 - INFO -   2. ECY73YCNA7IPQSM8NAKW.pdf            Page 1   → comm_invoice    | run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:31:09,146 - INFO -   3. EDYM381JJDTUB87YAAPI.pdf            Page 1   → comm_invoice    | run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:31:09,146 - INFO -   4. GMCKX2ERTX05S300COLL.pdf            Page 1   → invoice         | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:09,146 - INFO -   5. GMCKX2ERTX05S300COLL.pdf            Page 2   → invoice         | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:09,146 - INFO -   6. GMCKX2ERTX05S300COLL.pdf            Page 3   → invoice         | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:31:09,147 - INFO -   7. T51X0UC3WJL168AL2PGB.pdf            Page 1   → comm_invoice    | run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:31:09,147 - INFO -   8. TCM8BE9P052RZLZGHQIW.pdf            Page 1   → comm_invoice    | run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:31:09,147 - INFO -   9. V44T1WF2N7RT33JSFU5F.pdf            Page 1   → comm_invoice    | run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:31:09,147 - INFO -  10. Z106V9IKGLMUAGC3VOK8.pdf            Page 1   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:31:09,148 - INFO -  11. Z106V9IKGLMUAGC3VOK8.pdf            Page 2   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:31:09,148 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:31:09,149 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 8, 'failed': 1, 'errors': ['Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 22.943058, 'processed_files': [{'filename': 'CZ7K9JE7PH88JUBC19JF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf'}, {'filename': 'ECY73YCNA7IPQSM8NAKW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf'}, {'filename': 'EDYM381JJDTUB87YAAPI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf'}, {'filename': 'GMCKX2ERTX05S300COLL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf'}, {'filename': 'T51X0UC3WJL168AL2PGB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf'}, {'filename': 'TCM8BE9P052RZLZGHQIW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf'}, {'filename': 'V44T1WF2N7RT33JSFU5F.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf'}, {'filename': 'Z106V9IKGLMUAGC3VOK8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf'}]}
