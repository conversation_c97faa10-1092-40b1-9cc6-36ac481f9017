{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 8.662511419288421e-08, "Y": 2.136732923929685e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "00404a17-ddc7-4d49-bd8e-df49eb0bbd35", "Relationships": [{"Type": "CHILD", "Ids": ["1338369d-1127-46d8-bf06-7ab75f13ed2b", "b775b7f0-dd33-4b66-bb97-c771a50b9440", "ff84e5e1-87ca-4e22-a635-180ad737ee0f", "c66c493b-8302-4b9c-bb3c-a2ec859845a4", "17ec182f-81b1-4183-8900-441c90b1b14c", "751199bd-9ef9-449e-9ab5-0d1c5286d527", "30d93a3c-effc-434d-af77-765c3e641e0a", "bb77112b-368e-4559-9b5b-fdfa62999389", "de0d56cf-05a8-40c9-9f3a-02cd974fdfd0", "e81c0b56-e207-4906-9743-2dde436aaef1", "86724064-ba9c-4069-a14e-bfce29c0cf11", "60cc149c-b48b-4dfd-b2fc-7dc09dbc4068", "9157dc13-d4f2-42bd-9d52-e54a33323e06", "433645fe-2f77-49bf-ab0b-d20179f14774", "f0b34dad-57fc-4928-a9ec-b175fa392702", "afee4778-dd74-4a32-a24a-989d3d3159c3", "7a3c2845-8c65-4cf9-b157-fe85180e055b", "387b8ad4-39f4-4c69-92ed-ebbad56fbb08", "d33a8c8b-ac4f-44d6-9e34-62ad5049ffac"]}]}, {"BlockType": "LINE", "Confidence": 99.51377868652344, "Text": "J1 (Gate Information)", "Geometry": {"BoundingBox": {"Width": 0.23664842545986176, "Height": 0.03277239203453064, "Left": 0.38190585374832153, "Top": 0.06617559492588043}, "Polygon": [{"X": 0.3819059431552887, "Y": 0.06617692112922668}, {"X": 0.6185542941093445, "Y": 0.06617559492588043}, {"X": 0.6185541152954102, "Y": 0.09894677251577377}, {"X": 0.38190585374832153, "Y": 0.09894798696041107}]}, "Id": "1338369d-1127-46d8-bf06-7ab75f13ed2b", "Relationships": [{"Type": "CHILD", "Ids": ["c4dfd7f8-6392-49e2-a33c-1d90c6b2d5ec", "366e7f49-cc9a-4ca7-9ae5-0227aa208586", "adc95007-fcc0-4ae9-b6e9-f65c510c2dca"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "Equipment Information", "Geometry": {"BoundingBox": {"Width": 0.19721651077270508, "Height": 0.026640158146619797, "Left": 0.23805968463420868, "Top": 0.16618400812149048}, "Polygon": [{"X": 0.23805974423885345, "Y": 0.16618484258651733}, {"X": 0.43527618050575256, "Y": 0.16618400812149048}, {"X": 0.4352760910987854, "Y": 0.1928233951330185}, {"X": 0.23805968463420868, "Y": 0.19282416999340057}]}, "Id": "b775b7f0-dd33-4b66-bb97-c771a50b9440", "Relationships": [{"Type": "CHILD", "Ids": ["75db09da-2dd0-43d9-8489-972f58253061", "0135831d-0fed-4ba4-900b-97ec853fc45e"]}]}, {"BlockType": "LINE", "Confidence": 99.71161651611328, "Text": "Event Information", "Geometry": {"BoundingBox": {"Width": 0.15331047773361206, "Height": 0.02153603918850422, "Left": 0.6626219749450684, "Top": 0.16619469225406647}, "Polygon": [{"X": 0.6626220345497131, "Y": 0.166195347905159}, {"X": 0.8159324526786804, "Y": 0.16619469225406647}, {"X": 0.8159323334693909, "Y": 0.1877301186323166}, {"X": 0.6626219749450684, "Y": 0.18773072957992554}]}, "Id": "ff84e5e1-87ca-4e22-a635-180ad737ee0f", "Relationships": [{"Type": "CHILD", "Ids": ["f69717fd-489d-414a-be7a-bc6f34158eea", "f38ef333-a796-461d-a55c-250f8dddd70f"]}]}, {"BlockType": "LINE", "Confidence": 99.66946411132812, "Text": "Equipment ID: MRSU 547108", "Geometry": {"BoundingBox": {"Width": 0.24979647994041443, "Height": 0.02644757181406021, "Left": 0.19988039135932922, "Top": 0.2380058914422989}, "Polygon": [{"X": 0.199880450963974, "Y": 0.23800671100616455}, {"X": 0.44967687129974365, "Y": 0.2380058914422989}, {"X": 0.4496767818927765, "Y": 0.26445272564888}, {"X": 0.19988039135932922, "Y": 0.2644534707069397}]}, "Id": "c66c493b-8302-4b9c-bb3c-a2ec859845a4", "Relationships": [{"Type": "CHILD", "Ids": ["193ba969-64a6-4d58-b65e-8399dd1c0266", "94920637-30c2-446e-b2a3-1f57dfe64a5d", "797da5c2-01c0-4a62-8dfe-ebb7af586ad9", "d8769a40-ec40-4c87-8e38-f60047547475"]}]}, {"BlockType": "LINE", "Confidence": 99.71076965332031, "Text": "Terminal: CHICAGO 59TH ST", "Geometry": {"BoundingBox": {"Width": 0.2507258653640747, "Height": 0.021549798548221588, "Left": 0.6226696372032166, "Top": 0.23805665969848633}, "Polygon": [{"X": 0.6226696968078613, "Y": 0.238057479262352}, {"X": 0.8733955025672913, "Y": 0.23805665969848633}, {"X": 0.8733953833580017, "Y": 0.2596057057380676}, {"X": 0.6226696372032166, "Y": 0.2596064507961273}]}, "Id": "17ec182f-81b1-4183-8900-441c90b1b14c", "Relationships": [{"Type": "CHILD", "Ids": ["baad9982-4fca-4822-bbe4-0533d09d4fa8", "97200386-2d75-4d08-a0f4-8796da3646bb", "38050a0c-b4af-4a4e-b266-9a473634abb6", "e4da5e1f-ecf6-447a-9ea3-1cc6e010fbf9"]}]}, {"BlockType": "LINE", "Confidence": 99.9052963256836, "Text": "Associated Equipment ID: SSRT 823120", "Geometry": {"BoundingBox": {"Width": 0.34718090295791626, "Height": 0.026458144187927246, "Left": 0.09720996022224426, "Top": 0.30215340852737427}, "Polygon": [{"X": 0.09721000492572784, "Y": 0.3021542429924011}, {"X": 0.4443908631801605, "Y": 0.30215340852737427}, {"X": 0.44439077377319336, "Y": 0.3286108374595642}, {"X": 0.09720996022224426, "Y": 0.3286115527153015}]}, "Id": "751199bd-9ef9-449e-9ab5-0d1c5286d527", "Relationships": [{"Type": "CHILD", "Ids": ["2c2f4986-fbb2-4e2f-aa06-50a674620b5a", "306dbba3-e0b5-4d50-b6de-5d52194222c7", "64011ae7-38d9-4349-a37f-84dcd6c34602", "c726a999-6303-4210-ae6f-ddc1d0fefc50", "13c2491c-43e2-40ac-bb27-9392af07de76"]}]}, {"BlockType": "LINE", "Confidence": 99.83354949951172, "Text": "Date and Time: 07/09/2025 19:19", "Geometry": {"BoundingBox": {"Width": 0.2787807583808899, "Height": 0.02151385135948658, "Left": 0.573593258857727, "Top": 0.3021440804004669}, "Polygon": [{"X": 0.5735933184623718, "Y": 0.30214476585388184}, {"X": 0.8523740172386169, "Y": 0.3021440804004669}, {"X": 0.8523738980293274, "Y": 0.3236573338508606}, {"X": 0.573593258857727, "Y": 0.32365792989730835}]}, "Id": "30d93a3c-effc-434d-af77-765c3e641e0a", "Relationships": [{"Type": "CHILD", "Ids": ["d08ffe20-7c54-49c2-bc28-d2f3c89bc359", "951700a0-ed26-41a5-b266-045ffe7c8e27", "f34e7bde-d1f4-4317-8223-9843064720eb", "511aebb4-b180-4e9e-b2e4-d531fd380ad9", "ea674720-eb17-456c-ae80-9202b10774bb"]}]}, {"BlockType": "LINE", "Confidence": 99.80123138427734, "Text": "L/E Status: Load", "Geometry": {"BoundingBox": {"Width": 0.14272946119308472, "Height": 0.02162611298263073, "Left": 0.2281515896320343, "Top": 0.3662303388118744}, "Polygon": [{"X": 0.22815164923667908, "Y": 0.3662305772304535}, {"X": 0.370881050825119, "Y": 0.3662303388118744}, {"X": 0.37088099122047424, "Y": 0.38785627484321594}, {"X": 0.2281515896320343, "Y": 0.38785645365715027}]}, "Id": "bb77112b-368e-4559-9b5b-fdfa62999389", "Relationships": [{"Type": "CHILD", "Ids": ["9d1dc80a-decd-4234-9bb3-12f064b622cf", "cf77d828-a85f-4b1f-ba3e-259ef528028c", "493180a4-f231-4af4-9d36-5e053590749e"]}]}, {"BlockType": "LINE", "Confidence": 99.52168273925781, "Text": "Activity: Arrival", "Geometry": {"BoundingBox": {"Width": 0.13197512924671173, "Height": 0.026995569467544556, "Left": 0.6305906176567078, "Top": 0.3660137355327606}, "Polygon": [{"X": 0.6305907368659973, "Y": 0.36601394414901733}, {"X": 0.7625657320022583, "Y": 0.3660137355327606}, {"X": 0.7625656127929688, "Y": 0.39300915598869324}, {"X": 0.6305906176567078, "Y": 0.3930093050003052}]}, "Id": "de0d56cf-05a8-40c9-9f3a-02cd974fdfd0", "Relationships": [{"Type": "CHILD", "Ids": ["110f3aec-d08b-4b39-b31c-c1c4a9416cf4", "dc82afef-e127-4d10-a835-f5fa89aa8731"]}]}, {"BlockType": "LINE", "Confidence": 99.66159057617188, "Text": "Length: 40'", "Geometry": {"BoundingBox": {"Width": 0.09901443868875504, "Height": 0.026644308120012283, "Left": 0.25577330589294434, "Top": 0.4301122725009918}, "Polygon": [{"X": 0.2557733654975891, "Y": 0.4301123321056366}, {"X": 0.3547877371311188, "Y": 0.4301122725009918}, {"X": 0.3547876477241516, "Y": 0.4567565321922302}, {"X": 0.25577330589294434, "Y": 0.456756591796875}]}, "Id": "e81c0b56-e207-4906-9743-2dde436aaef1", "Relationships": [{"Type": "CHILD", "Ids": ["062faf7f-125e-455f-b6a1-24d623fff362", "fd07e1a6-6510-411a-b898-ea1d18d5a4b7"]}]}, {"BlockType": "LINE", "Confidence": 99.77452850341797, "Text": "Drayage Co: SUPER STAR FREIGHT INC", "Geometry": {"BoundingBox": {"Width": 0.3496951162815094, "Height": 0.027304913848638535, "Left": 0.5963993668556213, "Top": 0.4299372434616089}, "Polygon": [{"X": 0.5963994860649109, "Y": 0.429937481880188}, {"X": 0.9460944533348083, "Y": 0.4299372434616089}, {"X": 0.9460943341255188, "Y": 0.45724204182624817}, {"X": 0.5963993668556213, "Y": 0.4572421610355377}]}, "Id": "86724064-ba9c-4069-a14e-bfce29c0cf11", "Relationships": [{"Type": "CHILD", "Ids": ["48462e6e-102e-437d-b631-3909e4c45b20", "0d0dedd7-659e-4b11-aeb4-de5feea69357", "d7e324e1-072d-4f5d-9c4e-a2c708aa5ffd", "0a911177-2fd0-4f24-ac9d-3f6320f7b148", "72105a88-e70e-4855-bdb4-a51c5b8ddad7", "93443b65-2d82-42e8-9243-4d6d6120b70a"]}]}, {"BlockType": "LINE", "Confidence": 99.81575012207031, "Text": "Type: CN", "Geometry": {"BoundingBox": {"Width": 0.08137796819210052, "Height": 0.02646283619105816, "Left": 0.2755587697029114, "Top": 0.49452388286590576}, "Polygon": [{"X": 0.27555882930755615, "Y": 0.49452388286590576}, {"X": 0.3569367229938507, "Y": 0.49452388286590576}, {"X": 0.35693666338920593, "Y": 0.5209867358207703}, {"X": 0.2755587697029114, "Y": 0.5209866762161255}]}, "Id": "60cc149c-b48b-4dfd-b2fc-7dc09dbc4068", "Relationships": [{"Type": "CHILD", "Ids": ["d0ee1221-6646-46f6-b72c-a4154cb351f4", "96515ba4-03e9-474b-8828-69258d9503fb"]}]}, {"BlockType": "LINE", "Confidence": 99.69108581542969, "Text": "Driver Information: CORDOVA, SALVADOR", "Geometry": {"BoundingBox": {"Width": 0.3681177496910095, "Height": 0.025090498849749565, "Left": 0.5398690104484558, "Top": 0.4944792687892914}, "Polygon": [{"X": 0.5398691296577454, "Y": 0.4944792687892914}, {"X": 0.9079867601394653, "Y": 0.49447932839393616}, {"X": 0.9079866409301758, "Y": 0.5195697546005249}, {"X": 0.5398690104484558, "Y": 0.5195695757865906}]}, "Id": "9157dc13-d4f2-42bd-9d52-e54a33323e06", "Relationships": [{"Type": "CHILD", "Ids": ["d509084e-a39a-4024-8b2f-fa559f8e6b71", "dec24b25-116c-4a9c-b863-056ba32b5400", "361f2877-b23b-4313-aba1-003996712992", "6fe608ef-64e0-4e47-84da-0434f22ad715"]}]}, {"BlockType": "LINE", "Confidence": 99.794189453125, "Text": "SCAC Code: SSRT", "Geometry": {"BoundingBox": {"Width": 0.16448892652988434, "Height": 0.021588034927845, "Left": 0.59520024061203, "Top": 0.5584951639175415}, "Polygon": [{"X": 0.5952003598213196, "Y": 0.5584951639175415}, {"X": 0.759689211845398, "Y": 0.558495283126831}, {"X": 0.7596890926361084, "Y": 0.5800831913948059}, {"X": 0.59520024061203, "Y": 0.5800829529762268}]}, "Id": "433645fe-2f77-49bf-ab0b-d20179f14774", "Relationships": [{"Type": "CHILD", "Ids": ["022e486a-e98b-4ee3-b064-1444393970bb", "4c55621d-073c-447e-bdba-0350f75b654e", "7b700ac8-182d-42fc-8b77-2cc963110dd2"]}]}, {"BlockType": "LINE", "Confidence": 99.46527862548828, "Text": "Seal Number: C992213", "Geometry": {"BoundingBox": {"Width": 0.19834813475608826, "Height": 0.021710308268666267, "Left": 0.5858680009841919, "Top": 0.6227218508720398}, "Polygon": [{"X": 0.5858680605888367, "Y": 0.6227218508720398}, {"X": 0.7842161059379578, "Y": 0.6227222084999084}, {"X": 0.784216046333313, "Y": 0.6444321870803833}, {"X": 0.5858680009841919, "Y": 0.6444317102432251}]}, "Id": "f0b34dad-57fc-4928-a9ec-b175fa392702", "Relationships": [{"Type": "CHILD", "Ids": ["e34f92ac-9c1a-4e1f-9422-353c2e05b988", "074562c0-692a-43b7-8650-f49c006ae8da", "d5099112-0ca3-4eb3-9764-f3445b08d101"]}]}, {"BlockType": "LINE", "Confidence": 99.93045043945312, "Text": "Booking Number:", "Geometry": {"BoundingBox": {"Width": 0.15101230144500732, "Height": 0.026866571977734566, "Left": 0.5510156154632568, "Top": 0.6868892908096313}, "Polygon": [{"X": 0.5510157346725464, "Y": 0.6868892908096313}, {"X": 0.7020279169082642, "Y": 0.6868897080421448}, {"X": 0.7020277976989746, "Y": 0.7137558460235596}, {"X": 0.5510156154632568, "Y": 0.7137553691864014}]}, "Id": "afee4778-dd74-4a32-a24a-989d3d3159c3", "Relationships": [{"Type": "CHILD", "Ids": ["6afcc5b4-6c4c-4244-94ab-064b46143737", "795ec87c-34f7-4c54-9915-b5fce95592c3"]}]}, {"BlockType": "LINE", "Confidence": 99.77100372314453, "Text": "Inspection Information:", "Geometry": {"BoundingBox": {"Width": 0.1998465657234192, "Height": 0.026392769068479538, "Left": 0.012817435897886753, "Top": 0.7831935882568359}, "Polygon": [{"X": 0.01281747780740261, "Y": 0.7831935882568359}, {"X": 0.21266400814056396, "Y": 0.783194363117218}, {"X": 0.212663933634758, "Y": 0.8095863461494446}, {"X": 0.012817435897886753, "Y": 0.809585452079773}]}, "Id": "7a3c2845-8c65-4cf9-b157-fe85180e055b", "Relationships": [{"Type": "CHILD", "Ids": ["7868173b-3bcb-4473-81ab-ee8fcf66ead5", "e144852a-12db-491a-86bf-87bc4575095a"]}]}, {"BlockType": "LINE", "Confidence": 99.45403289794922, "Text": "Please refer ShipCSX images for Inspection Information.", "Geometry": {"BoundingBox": {"Width": 0.45246490836143494, "Height": 0.02705548331141472, "Left": 0.013076364994049072, "Top": 0.8598491549491882}, "Polygon": [{"X": 0.013076407834887505, "Y": 0.8598491549491882}, {"X": 0.465541273355484, "Y": 0.8598514199256897}, {"X": 0.46554118394851685, "Y": 0.8869046568870544}, {"X": 0.013076364994049072, "Y": 0.8869022130966187}]}, "Id": "387b8ad4-39f4-4c69-92ed-ebbad56fbb08", "Relationships": [{"Type": "CHILD", "Ids": ["787bbea8-e4af-4a27-ba45-ed6106d1b5b2", "8fbdd823-02a6-4b7b-b96a-dc1454123297", "4020841d-a37c-4a7d-94a8-f6ef19bb8fac", "54a87883-8d09-426c-b8cf-373c572af88d", "87f746f7-56a7-455f-bb66-a64fd861ba93", "34fc38b7-a895-4150-84d5-7cd8d14b5082", "4c2fadbc-d828-47ed-87a6-87733a84f628"]}]}, {"BlockType": "LINE", "Confidence": 99.43914794921875, "Text": "Damage is captured on recorded images at AGS locations", "Geometry": {"BoundingBox": {"Width": 0.3261963129043579, "Height": 0.019096745178103447, "Left": 0.012565430253744125, "Top": 0.9215645790100098}, "Polygon": [{"X": 0.012565460056066513, "Y": 0.9215645790100098}, {"X": 0.33876174688339233, "Y": 0.9215664863586426}, {"X": 0.33876168727874756, "Y": 0.9406613111495972}, {"X": 0.012565430253744125, "Y": 0.9406593441963196}]}, "Id": "d33a8c8b-ac4f-44d6-9e34-62ad5049ffac", "Relationships": [{"Type": "CHILD", "Ids": ["72c54618-be0f-40f9-a95b-23c6dfd494f0", "4ac48529-a3fc-428f-971b-19cfaef3b44a", "a87255d5-8f8d-4cd0-9052-89d295cc217a", "e5d3cd42-aed5-4a84-b0dd-ac5090377a77", "47a24e9b-b950-40bb-933a-c2d005ad5fc3", "6eeecb5a-d8bd-4b99-82f4-7c721a9642cb", "6c769305-9ab1-4b62-bae5-128a7db32887", "8c5c1111-1c20-4dbc-8e12-1835723eaf2c", "4a95487b-6be8-49ac-a594-a1e1b79c60c1"]}]}, {"BlockType": "WORD", "Confidence": 99.43698120117188, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02378736436367035, "Height": 0.026604268699884415, "Left": 0.3819058835506439, "Top": 0.06617678701877594}, "Polygon": [{"X": 0.3819059431552887, "Y": 0.06617692112922668}, {"X": 0.4056932330131531, "Y": 0.06617678701877594}, {"X": 0.4056931436061859, "Y": 0.09278092533349991}, {"X": 0.3819058835506439, "Y": 0.09278105199337006}]}, "Id": "c4dfd7f8-6392-49e2-a33c-1d90c6b2d5ec"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "(Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05969218164682388, "Height": 0.03257240355014801, "Left": 0.41516464948654175, "Top": 0.06637541204690933}, "Polygon": [{"X": 0.4151647686958313, "Y": 0.0663757473230362}, {"X": 0.4748568534851074, "Y": 0.06637541204690933}, {"X": 0.47485673427581787, "Y": 0.09894751012325287}, {"X": 0.41516464948654175, "Y": 0.09894781559705734}]}, "Id": "366e7f49-cc9a-4ca7-9ae5-0227aa208586"}, {"BlockType": "WORD", "Confidence": 99.5322494506836, "Text": "Information)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13614054024219513, "Height": 0.03243643790483475, "Left": 0.48241373896598816, "Top": 0.06635265052318573}, "Polygon": [{"X": 0.4824138283729553, "Y": 0.06635341048240662}, {"X": 0.6185542941093445, "Y": 0.06635265052318573}, {"X": 0.6185541749000549, "Y": 0.09878838807344437}, {"X": 0.48241373896598816, "Y": 0.09878908842802048}]}, "Id": "adc95007-fcc0-4ae9-b6e9-f65c510c2dca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0936817154288292, "Height": 0.02663971669971943, "Left": 0.23805968463420868, "Top": 0.1661844551563263}, "Polygon": [{"X": 0.23805974423885345, "Y": 0.16618484258651733}, {"X": 0.3317413926124573, "Y": 0.1661844551563263}, {"X": 0.3317413330078125, "Y": 0.19282379746437073}, {"X": 0.23805968463420868, "Y": 0.19282416999340057}]}, "Id": "75db09da-2dd0-43d9-8489-972f58253061"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09850811213254929, "Height": 0.0212665107101202, "Left": 0.33676809072494507, "Top": 0.1663176715373993}, "Polygon": [{"X": 0.33676815032958984, "Y": 0.16631808876991272}, {"X": 0.43527618050575256, "Y": 0.1663176715373993}, {"X": 0.4352761209011078, "Y": 0.187583789229393}, {"X": 0.33676809072494507, "Y": 0.18758419156074524}]}, "Id": "0135831d-0fed-4ba4-900b-97ec853fc45e"}, {"BlockType": "WORD", "Confidence": 99.63229370117188, "Text": "Event", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04909685254096985, "Height": 0.02124883607029915, "Left": 0.6626219749450684, "Top": 0.16635951399803162}, "Polygon": [{"X": 0.6626220345497131, "Y": 0.16635972261428833}, {"X": 0.7117187976837158, "Y": 0.16635951399803162}, {"X": 0.711718738079071, "Y": 0.18760816752910614}, {"X": 0.6626219749450684, "Y": 0.18760836124420166}]}, "Id": "f69717fd-489d-414a-be7a-bc6f34158eea"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09923235327005386, "Height": 0.021535824984312057, "Left": 0.7167000770568848, "Top": 0.16619469225406647}, "Polygon": [{"X": 0.7167001962661743, "Y": 0.1661951094865799}, {"X": 0.8159324526786804, "Y": 0.16619469225406647}, {"X": 0.8159323334693909, "Y": 0.1877301186323166}, {"X": 0.7167000770568848, "Y": 0.18773052096366882}]}, "Id": "f38ef333-a796-461d-a55c-250f8dddd70f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09376216679811478, "Height": 0.02626725099980831, "Left": 0.19988039135932922, "Top": 0.23818621039390564}, "Polygon": [{"X": 0.199880450963974, "Y": 0.2381865233182907}, {"X": 0.2936425507068634, "Y": 0.23818621039390564}, {"X": 0.29364249110221863, "Y": 0.2644532024860382}, {"X": 0.19988039135932922, "Y": 0.2644534707069397}]}, "Id": "193ba969-64a6-4d58-b65e-8399dd1c0266"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02253282256424427, "Height": 0.020703962072730064, "Left": 0.29854562878608704, "Top": 0.23842529952526093}, "Polygon": [{"X": 0.2985456883907318, "Y": 0.2384253740310669}, {"X": 0.32107844948768616, "Y": 0.23842529952526093}, {"X": 0.3210783898830414, "Y": 0.25912919640541077}, {"X": 0.29854562878608704, "Y": 0.25912925601005554}]}, "Id": "94920637-30c2-446e-b2a3-1f57dfe64a5d"}, {"BlockType": "WORD", "Confidence": 99.37519836425781, "Text": "MRSU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05252822861075401, "Height": 0.02147011086344719, "Left": 0.3314046263694763, "Top": 0.2380061149597168}, "Polygon": [{"X": 0.3314046859741211, "Y": 0.23800627887248993}, {"X": 0.38393285870552063, "Y": 0.2380061149597168}, {"X": 0.38393276929855347, "Y": 0.25947606563568115}, {"X": 0.3314046263694763, "Y": 0.2594762146472931}]}, "Id": "797da5c2-01c0-4a62-8dfe-ebb7af586ad9"}, {"BlockType": "WORD", "Confidence": 99.43936920166016, "Text": "547108", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05999290570616722, "Height": 0.02137979306280613, "Left": 0.38968396186828613, "Top": 0.23809362947940826}, "Polygon": [{"X": 0.3896840512752533, "Y": 0.23809382319450378}, {"X": 0.44967687129974365, "Y": 0.23809362947940826}, {"X": 0.4496768116950989, "Y": 0.2594732344150543}, {"X": 0.38968396186828613, "Y": 0.25947341322898865}]}, "Id": "d8769a40-ec40-4c87-8e38-f60047547475"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Terminal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07919996231794357, "Height": 0.02154717408120632, "Left": 0.6226696372032166, "Top": 0.23805928230285645}, "Polygon": [{"X": 0.6226696968078613, "Y": 0.23805955052375793}, {"X": 0.701869547367096, "Y": 0.23805928230285645}, {"X": 0.7018694877624512, "Y": 0.2596062123775482}, {"X": 0.6226696372032166, "Y": 0.2596064507961273}]}, "Id": "baad9982-4fca-4822-bbe4-0533d09d4fa8"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "CHICAGO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08411803096532822, "Height": 0.02122432179749012, "Left": 0.7115139961242676, "Top": 0.23816825449466705}, "Polygon": [{"X": 0.7115140557289124, "Y": 0.23816853761672974}, {"X": 0.795632004737854, "Y": 0.23816825449466705}, {"X": 0.7956319451332092, "Y": 0.25939232110977173}, {"X": 0.7115139961242676, "Y": 0.2593925893306732}]}, "Id": "97200386-2d75-4d08-a0f4-8796da3646bb"}, {"BlockType": "WORD", "Confidence": 99.39632415771484, "Text": "59TH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04381012171506882, "Height": 0.021293489262461662, "Left": 0.8015055060386658, "Top": 0.23812007904052734}, "Polygon": [{"X": 0.8015056252479553, "Y": 0.23812022805213928}, {"X": 0.8453156352043152, "Y": 0.23812007904052734}, {"X": 0.8453155755996704, "Y": 0.2594134509563446}, {"X": 0.8015055060386658, "Y": 0.25941357016563416}]}, "Id": "38050a0c-b4af-4a4e-b266-9a473634abb6"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "ST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022068645805120468, "Height": 0.020876074209809303, "Left": 0.8513268232345581, "Top": 0.23805665969848633}, "Polygon": [{"X": 0.8513269424438477, "Y": 0.2380567342042923}, {"X": 0.8733955025672913, "Y": 0.23805665969848633}, {"X": 0.8733953833580017, "Y": 0.2589326798915863}, {"X": 0.8513268232345581, "Y": 0.2589327394962311}]}, "Id": "e4da5e1f-ecf6-447a-9ea3-1cc6e010fbf9"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Associated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09695702791213989, "Height": 0.021334629505872726, "Left": 0.09720996767282486, "Top": 0.3022555112838745}, "Polygon": [{"X": 0.09721000492572784, "Y": 0.3022557497024536}, {"X": 0.19416698813438416, "Y": 0.3022555112838745}, {"X": 0.19416694343090057, "Y": 0.323589950799942}, {"X": 0.09720996767282486, "Y": 0.32359015941619873}]}, "Id": "2c2f4986-fbb2-4e2f-aa06-50a674620b5a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09380272030830383, "Height": 0.026457564905285835, "Left": 0.1998104602098465, "Top": 0.3021537661552429}, "Polygon": [{"X": 0.19981051981449127, "Y": 0.302154004573822}, {"X": 0.29361316561698914, "Y": 0.3021537661552429}, {"X": 0.29361310601234436, "Y": 0.3286111354827881}, {"X": 0.1998104602098465, "Y": 0.3286113440990448}]}, "Id": "306dbba3-e0b5-4d50-b6de-5d52194222c7"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022575005888938904, "Height": 0.020715050399303436, "Left": 0.2985323667526245, "Top": 0.30257469415664673}, "Polygon": [{"X": 0.2985324263572693, "Y": 0.3025747537612915}, {"X": 0.3211073577404022, "Y": 0.30257469415664673}, {"X": 0.32110729813575745, "Y": 0.323289692401886}, {"X": 0.2985323667526245, "Y": 0.32328975200653076}]}, "Id": "64011ae7-38d9-4349-a37f-84dcd6c34602"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04816325008869171, "Height": 0.021070821210741997, "Left": 0.33137112855911255, "Top": 0.3023671805858612}, "Polygon": [{"X": 0.3313711881637573, "Y": 0.30236729979515076}, {"X": 0.37953436374664307, "Y": 0.3023671805858612}, {"X": 0.3795343041419983, "Y": 0.32343789935112}, {"X": 0.33137112855911255, "Y": 0.32343798875808716}]}, "Id": "c726a999-6303-4210-ae6f-ddc1d0fefc50"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "823120", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06058894470334053, "Height": 0.021078959107398987, "Left": 0.3838019371032715, "Top": 0.302398145198822}, "Polygon": [{"X": 0.38380199670791626, "Y": 0.30239829421043396}, {"X": 0.4443908631801605, "Y": 0.302398145198822}, {"X": 0.44439080357551575, "Y": 0.32347697019577026}, {"X": 0.3838019371032715, "Y": 0.3234771192073822}]}, "Id": "13c2491c-43e2-40ac-bb27-9392af07de76"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0383969284594059, "Height": 0.020862987264990807, "Left": 0.573593258857727, "Top": 0.3026040494441986}, "Polygon": [{"X": 0.5735933184623718, "Y": 0.30260413885116577}, {"X": 0.6119901537895203, "Y": 0.3026040494441986}, {"X": 0.6119900941848755, "Y": 0.323466956615448}, {"X": 0.573593258857727, "Y": 0.3234670162200928}]}, "Id": "d08ffe20-7c54-49c2-bc28-d2f3c89bc359"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03158092498779297, "Height": 0.020665587857365608, "Left": 0.6170498728752136, "Top": 0.30273449420928955}, "Polygon": [{"X": 0.6170499920845032, "Y": 0.3027345538139343}, {"X": 0.6486307978630066, "Y": 0.30273449420928955}, {"X": 0.6486307382583618, "Y": 0.32340002059936523}, {"X": 0.6170498728752136, "Y": 0.32340008020401}]}, "Id": "951700a0-ed26-41a5-b266-045ffe7c8e27"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04689590260386467, "Height": 0.020727967843413353, "Left": 0.6547567844390869, "Top": 0.302600234746933}, "Polygon": [{"X": 0.6547568440437317, "Y": 0.30260035395622253}, {"X": 0.7016526460647583, "Y": 0.302600234746933}, {"X": 0.7016525864601135, "Y": 0.3233281075954437}, {"X": 0.6547567844390869, "Y": 0.3233282268047333}]}, "Id": "f34e7bde-d1f4-4317-8223-9843064720eb"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "07/09/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09065771102905273, "Height": 0.021420251578092575, "Left": 0.7113749980926514, "Top": 0.30214419960975647}, "Polygon": [{"X": 0.7113750576972961, "Y": 0.30214443802833557}, {"X": 0.8020327091217041, "Y": 0.30214419960975647}, {"X": 0.8020325899124146, "Y": 0.3235642611980438}, {"X": 0.7113749980926514, "Y": 0.32356446981430054}]}, "Id": "511aebb4-b180-4e9e-b2e4-d531fd380ad9"}, {"BlockType": "WORD", "Confidence": 99.53543853759766, "Text": "19:19", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043850742280483246, "Height": 0.021177491173148155, "Left": 0.8085232973098755, "Top": 0.30247995257377625}, "Polygon": [{"X": 0.8085233569145203, "Y": 0.3024800419807434}, {"X": 0.8523740172386169, "Y": 0.30247995257377625}, {"X": 0.8523738980293274, "Y": 0.3236573338508606}, {"X": 0.8085232973098755, "Y": 0.32365742325782776}]}, "Id": "ea674720-eb17-456c-ae80-9202b10774bb"}, {"BlockType": "WORD", "Confidence": 99.67215728759766, "Text": "L/E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027293434366583824, "Height": 0.021375048905611038, "Left": 0.2281515896320343, "Top": 0.3662305474281311}, "Polygon": [{"X": 0.22815164923667908, "Y": 0.3662305772304535}, {"X": 0.25544503331184387, "Y": 0.3662305474281311}, {"X": 0.2554449737071991, "Y": 0.38760554790496826}, {"X": 0.2281515896320343, "Y": 0.38760557770729065}]}, "Id": "9d1dc80a-decd-4234-9bb3-12f064b622cf"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Status:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06081991270184517, "Height": 0.020779849961400032, "Left": 0.2607128322124481, "Top": 0.36674797534942627}, "Polygon": [{"X": 0.2607128918170929, "Y": 0.36674806475639343}, {"X": 0.3215327262878418, "Y": 0.36674797534942627}, {"X": 0.3215326964855194, "Y": 0.3875277638435364}, {"X": 0.2607128322124481, "Y": 0.38752782344818115}]}, "Id": "cf77d828-a85f-4b1f-ba3e-259ef528028c"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03937814012169838, "Height": 0.021485963836312294, "Left": 0.33150291442871094, "Top": 0.36637037992477417}, "Polygon": [{"X": 0.3315029740333557, "Y": 0.36637043952941895}, {"X": 0.370881050825119, "Y": 0.36637037992477417}, {"X": 0.37088099122047424, "Y": 0.38785627484321594}, {"X": 0.33150291442871094, "Y": 0.3878563344478607}]}, "Id": "493180a4-f231-4af4-9d36-5e053590749e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Activity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07117421925067902, "Height": 0.026995474472641945, "Left": 0.6305906176567078, "Top": 0.3660138249397278}, "Polygon": [{"X": 0.6305907368659973, "Y": 0.36601394414901733}, {"X": 0.7017648220062256, "Y": 0.3660138249397278}, {"X": 0.701764702796936, "Y": 0.393009215593338}, {"X": 0.6305906176567078, "Y": 0.3930093050003052}]}, "Id": "110f3aec-d08b-4b39-b31c-c1c4a9416cf4"}, {"BlockType": "WORD", "Confidence": 99.12308502197266, "Text": "Arrival", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05210312455892563, "Height": 0.021613147109746933, "Left": 0.7104626297950745, "Top": 0.3660990595817566}, "Polygon": [{"X": 0.7104626893997192, "Y": 0.36609914898872375}, {"X": 0.7625657320022583, "Y": 0.3660990595817566}, {"X": 0.7625656127929688, "Y": 0.38771212100982666}, {"X": 0.7104626297950745, "Y": 0.3877122104167938}]}, "Id": "dc82afef-e127-4d10-a835-f5fa89aa8731"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Length:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06569287925958633, "Height": 0.026644283905625343, "Left": 0.25577330589294434, "Top": 0.4301123023033142}, "Polygon": [{"X": 0.2557733654975891, "Y": 0.4301123321056366}, {"X": 0.3214661777019501, "Y": 0.4301123023033142}, {"X": 0.3214660882949829, "Y": 0.4567565619945526}, {"X": 0.25577330589294434, "Y": 0.456756591796875}]}, "Id": "062faf7f-125e-455f-b6a1-24d623fff362"}, {"BlockType": "WORD", "Confidence": 99.65182495117188, "Text": "40'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023910384625196457, "Height": 0.021574852988123894, "Left": 0.3308773338794708, "Top": 0.4302416741847992}, "Polygon": [{"X": 0.3308773934841156, "Y": 0.4302416741847992}, {"X": 0.3547877371311188, "Y": 0.4302416741847992}, {"X": 0.354787677526474, "Y": 0.45181649923324585}, {"X": 0.3308773338794708, "Y": 0.45181652903556824}]}, "Id": "fd07e1a6-6510-411a-b898-ea1d18d5a4b7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07046392560005188, "Height": 0.02670423500239849, "Left": 0.5963993668556213, "Top": 0.4305379092693329}, "Polygon": [{"X": 0.5963994264602661, "Y": 0.43053796887397766}, {"X": 0.6668632626533508, "Y": 0.4305379092693329}, {"X": 0.6668631434440613, "Y": 0.45724213123321533}, {"X": 0.5963993668556213, "Y": 0.4572421610355377}]}, "Id": "48462e6e-102e-437d-b631-3909e4c45b20"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Co:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029607223346829414, "Height": 0.021124547347426414, "Left": 0.6721338033676147, "Top": 0.43082326650619507}, "Polygon": [{"X": 0.6721338629722595, "Y": 0.43082326650619507}, {"X": 0.7017409801483154, "Y": 0.43082326650619507}, {"X": 0.7017409205436707, "Y": 0.45194777846336365}, {"X": 0.6721338033676147, "Y": 0.45194780826568604}]}, "Id": "0d0dedd7-659e-4b11-aeb4-de5feea69357"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "SUPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06211543083190918, "Height": 0.02145211398601532, "Left": 0.7116402983665466, "Top": 0.43037280440330505}, "Polygon": [{"X": 0.7116404175758362, "Y": 0.43037283420562744}, {"X": 0.7737557291984558, "Y": 0.43037280440330505}, {"X": 0.7737556099891663, "Y": 0.4518248736858368}, {"X": 0.7116402983665466, "Y": 0.4518249034881592}]}, "Id": "d7e324e1-072d-4f5d-9c4e-a2c708aa5ffd"}, {"BlockType": "WORD", "Confidence": 99.55257415771484, "Text": "STAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047341231256723404, "Height": 0.021986840292811394, "Left": 0.7790748476982117, "Top": 0.42993733286857605}, "Polygon": [{"X": 0.7790749669075012, "Y": 0.42993736267089844}, {"X": 0.8264160752296448, "Y": 0.42993733286857605}, {"X": 0.8264159560203552, "Y": 0.4519241452217102}, {"X": 0.7790748476982117, "Y": 0.4519241750240326}]}, "Id": "0a911177-2fd0-4f24-ac9d-3f6320f7b148"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0787467509508133, "Height": 0.021686134859919548, "Left": 0.8320455551147461, "Top": 0.43015506863594055}, "Polygon": [{"X": 0.8320456147193909, "Y": 0.4301551282405853}, {"X": 0.9107922911643982, "Y": 0.43015506863594055}, {"X": 0.9107921719551086, "Y": 0.45184117555618286}, {"X": 0.8320455551147461, "Y": 0.45184120535850525}]}, "Id": "72105a88-e70e-4855-bdb4-a51c5b8ddad7"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "INC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02996189333498478, "Height": 0.021590523421764374, "Left": 0.9161325693130493, "Top": 0.43032529950141907}, "Polygon": [{"X": 0.9161326885223389, "Y": 0.43032529950141907}, {"X": 0.9460944533348083, "Y": 0.43032529950141907}, {"X": 0.9460943341255188, "Y": 0.45191580057144165}, {"X": 0.9161325693130493, "Y": 0.45191580057144165}]}, "Id": "93443b65-2d82-42e8-9243-4d6d6120b70a"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045670438557863235, "Height": 0.02637496590614319, "Left": 0.2755587697029114, "Top": 0.4946117401123047}, "Polygon": [{"X": 0.27555882930755615, "Y": 0.4946117401123047}, {"X": 0.3212291896343231, "Y": 0.4946117401123047}, {"X": 0.32122913002967834, "Y": 0.5209866762161255}, {"X": 0.2755587697029114, "Y": 0.5209866762161255}]}, "Id": "d0ee1221-6646-46f6-b72c-a4154cb351f4"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "CN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02552149072289467, "Height": 0.02153436653316021, "Left": 0.33141523599624634, "Top": 0.49452388286590576}, "Polygon": [{"X": 0.3314152956008911, "Y": 0.49452388286590576}, {"X": 0.3569367229938507, "Y": 0.49452388286590576}, {"X": 0.35693666338920593, "Y": 0.5160582661628723}, {"X": 0.33141523599624634, "Y": 0.5160582661628723}]}, "Id": "96515ba4-03e9-474b-8828-69258d9503fb"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05245102569460869, "Height": 0.021352052688598633, "Left": 0.5398690104484558, "Top": 0.4947163462638855}, "Polygon": [{"X": 0.5398691296577454, "Y": 0.4947163462638855}, {"X": 0.5923200845718384, "Y": 0.4947163462638855}, {"X": 0.5923199653625488, "Y": 0.5160683989524841}, {"X": 0.5398690104484558, "Y": 0.5160683393478394}]}, "Id": "d509084e-a39a-4024-8b2f-fa559f8e6b71"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "Information:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10476569086313248, "Height": 0.021491041406989098, "Left": 0.5971494317054749, "Top": 0.49447929859161377}, "Polygon": [{"X": 0.5971494913101196, "Y": 0.49447929859161377}, {"X": 0.7019150853157043, "Y": 0.49447929859161377}, {"X": 0.7019150257110596, "Y": 0.5159703493118286}, {"X": 0.5971494317054749, "Y": 0.5159702897071838}]}, "Id": "dec24b25-116c-4a9c-b863-056ba32b5400"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "CORDOVA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09426624327898026, "Height": 0.025038693100214005, "Left": 0.7121608853340149, "Top": 0.4945310354232788}, "Polygon": [{"X": 0.7121609449386597, "Y": 0.4945310354232788}, {"X": 0.8064271211624146, "Y": 0.4945310354232788}, {"X": 0.806427001953125, "Y": 0.5195697546005249}, {"X": 0.7121608853340149, "Y": 0.5195696949958801}]}, "Id": "361f2877-b23b-4313-aba1-003996712992"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SALVADOR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09580917656421661, "Height": 0.021585576236248016, "Left": 0.8121775984764099, "Top": 0.4945165812969208}, "Polygon": [{"X": 0.8121777176856995, "Y": 0.4945165812969208}, {"X": 0.9079867601394653, "Y": 0.49451661109924316}, {"X": 0.9079866409301758, "Y": 0.5161021947860718}, {"X": 0.8121775984764099, "Y": 0.516102135181427}]}, "Id": "6fe608ef-64e0-4e47-84da-0434f22ad715"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "SCAC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05007446929812431, "Height": 0.02114713005721569, "Left": 0.59520024061203, "Top": 0.5587641596794128}, "Polygon": [{"X": 0.5952003598213196, "Y": 0.5587641596794128}, {"X": 0.6452747583389282, "Y": 0.5587642192840576}, {"X": 0.6452746391296387, "Y": 0.5799112915992737}, {"X": 0.59520024061203, "Y": 0.5799112319946289}]}, "Id": "022e486a-e98b-4ee3-b064-1444393970bb"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05048729106783867, "Height": 0.021587904542684555, "Left": 0.6512982845306396, "Top": 0.5584952235221863}, "Polygon": [{"X": 0.6512983441352844, "Y": 0.5584952235221863}, {"X": 0.7017855644226074, "Y": 0.5584952235221863}, {"X": 0.7017854452133179, "Y": 0.5800831317901611}, {"X": 0.6512982845306396, "Y": 0.5800830125808716}]}, "Id": "4c55621d-073c-447e-bdba-0350f75b654e"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04798269271850586, "Height": 0.02123040333390236, "Left": 0.7117065191268921, "Top": 0.5586997866630554}, "Polygon": [{"X": 0.7117065787315369, "Y": 0.5586997866630554}, {"X": 0.759689211845398, "Y": 0.5586997866630554}, {"X": 0.7596890926361084, "Y": 0.5799301862716675}, {"X": 0.7117065191268921, "Y": 0.5799301266670227}]}, "Id": "7b700ac8-182d-42fc-8b77-2cc963110dd2"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "Seal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03657514601945877, "Height": 0.021002477034926414, "Left": 0.5858680009841919, "Top": 0.6229742169380188}, "Polygon": [{"X": 0.5858680605888367, "Y": 0.6229742169380188}, {"X": 0.6224431395530701, "Y": 0.6229742765426636}, {"X": 0.6224430799484253, "Y": 0.6439766883850098}, {"X": 0.5858680009841919, "Y": 0.643976628780365}]}, "Id": "e34f92ac-9c1a-4e1f-9422-353c2e05b988"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07308968901634216, "Height": 0.021316012367606163, "Left": 0.6284182071685791, "Top": 0.6228511929512024}, "Polygon": [{"X": 0.6284183263778687, "Y": 0.6228511929512024}, {"X": 0.7015079259872437, "Y": 0.6228513121604919}, {"X": 0.7015078067779541, "Y": 0.6441672444343567}, {"X": 0.6284182071685791, "Y": 0.6441670656204224}]}, "Id": "074562c0-692a-43b7-8650-f49c006ae8da"}, {"BlockType": "WORD", "Confidence": 98.9636459350586, "Text": "C992213", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07215757668018341, "Height": 0.021710073575377464, "Left": 0.7120585441589355, "Top": 0.6227220892906189}, "Polygon": [{"X": 0.7120586633682251, "Y": 0.6227220892906189}, {"X": 0.7842161059379578, "Y": 0.6227222084999084}, {"X": 0.784216046333313, "Y": 0.6444321870803833}, {"X": 0.7120585441589355, "Y": 0.644432008266449}]}, "Id": "d5099112-0ca3-4eb3-9764-f3445b08d101"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07145730406045914, "Height": 0.02685180865228176, "Left": 0.5510156154632568, "Top": 0.6869037747383118}, "Polygon": [{"X": 0.5510157346725464, "Y": 0.6869037747383118}, {"X": 0.6224729418754578, "Y": 0.6869040131568909}, {"X": 0.6224728226661682, "Y": 0.7137556076049805}, {"X": 0.5510156154632568, "Y": 0.7137553691864014}]}, "Id": "6afcc5b4-6c4c-4244-94ab-064b46143737"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07378403842449188, "Height": 0.021678879857063293, "Left": 0.6282438635826111, "Top": 0.6868894696235657}, "Polygon": [{"X": 0.6282439827919006, "Y": 0.6868894696235657}, {"X": 0.7020279169082642, "Y": 0.6868897080421448}, {"X": 0.7020277976989746, "Y": 0.7085683941841125}, {"X": 0.6282438635826111, "Y": 0.7085681557655334}]}, "Id": "795ec87c-34f7-4c54-9915-b5fce95592c3"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08986720442771912, "Height": 0.026131214573979378, "Left": 0.012817435897886753, "Top": 0.7834546566009521}, "Polygon": [{"X": 0.012817476876080036, "Y": 0.7834546566009521}, {"X": 0.1026846393942833, "Y": 0.7834550142288208}, {"X": 0.10268458724021912, "Y": 0.8095858693122864}, {"X": 0.012817435897886753, "Y": 0.809585452079773}]}, "Id": "7868173b-3bcb-4473-81ab-ee8fcf66ead5"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Information:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10444559156894684, "Height": 0.021402135491371155, "Left": 0.10821840912103653, "Top": 0.7831939458847046}, "Polygon": [{"X": 0.10821845382452011, "Y": 0.7831939458847046}, {"X": 0.21266400814056396, "Y": 0.783194363117218}, {"X": 0.2126639485359192, "Y": 0.8045960664749146}, {"X": 0.10821840912103653, "Y": 0.8045956492424011}]}, "Id": "e144852a-12db-491a-86bf-87bc4575095a"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05440106987953186, "Height": 0.021635187789797783, "Left": 0.013076373375952244, "Top": 0.8599450588226318}, "Polygon": [{"X": 0.01307640690356493, "Y": 0.8599450588226318}, {"X": 0.06747744232416153, "Y": 0.8599453568458557}, {"X": 0.06747740507125854, "Y": 0.8815802931785583}, {"X": 0.013076373375952244, "Y": 0.8815799951553345}]}, "Id": "787bbea8-e4af-4a27-ba45-ed6106d1b5b2"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "refer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037472426891326904, "Height": 0.02133043110370636, "Left": 0.0732974261045456, "Top": 0.8601874113082886}, "Polygon": [{"X": 0.07329746335744858, "Y": 0.8601874113082886}, {"X": 0.1107698529958725, "Y": 0.8601875901222229}, {"X": 0.11076980829238892, "Y": 0.8815178275108337}, {"X": 0.0732974261045456, "Y": 0.8815176486968994}]}, "Id": "8fbdd823-02a6-4b7b-b96a-dc1454123297"}, {"BlockType": "WORD", "Confidence": 99.5557632446289, "Text": "ShipCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07371565699577332, "Height": 0.02661490999162197, "Left": 0.11534947156906128, "Top": 0.8598496913909912}, "Polygon": [{"X": 0.11534952372312546, "Y": 0.8598496913909912}, {"X": 0.1890651285648346, "Y": 0.8598500490188599}, {"X": 0.18906506896018982, "Y": 0.8864645957946777}, {"X": 0.11534947156906128, "Y": 0.8864641785621643}]}, "Id": "4020841d-a37c-4a7d-94a8-f6ef19bb8fac"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058293797075748444, "Height": 0.026721371337771416, "Left": 0.19362790882587433, "Top": 0.8601821064949036}, "Polygon": [{"X": 0.1936279684305191, "Y": 0.8601821064949036}, {"X": 0.25192171335220337, "Y": 0.8601824045181274}, {"X": 0.2519216239452362, "Y": 0.8869034647941589}, {"X": 0.19362790882587433, "Y": 0.8869031667709351}]}, "Id": "54a87883-8d09-426c-b8cf-373c572af88d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022414596751332283, "Height": 0.02112693339586258, "Left": 0.2565958499908447, "Top": 0.8602445721626282}, "Polygon": [{"X": 0.2565959095954895, "Y": 0.8602445721626282}, {"X": 0.27901044487953186, "Y": 0.8602446913719177}, {"X": 0.2790103852748871, "Y": 0.8813715577125549}, {"X": 0.2565958499908447, "Y": 0.8813714385032654}]}, "Id": "87f746f7-56a7-455f-bb66-a64fd861ba93"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0812331885099411, "Height": 0.02639886736869812, "Left": 0.283780962228775, "Top": 0.8601230978965759}, "Polygon": [{"X": 0.2837810218334198, "Y": 0.8601230978965759}, {"X": 0.36501413583755493, "Y": 0.8601235151290894}, {"X": 0.36501407623291016, "Y": 0.8865219950675964}, {"X": 0.283780962228775, "Y": 0.886521577835083}]}, "Id": "34fc38b7-a895-4150-84d5-7cd8d14b5082"}, {"BlockType": "WORD", "Confidence": 98.36575317382812, "Text": "Information.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09404540061950684, "Height": 0.021700773388147354, "Left": 0.3714958727359772, "Top": 0.8600543737411499}, "Polygon": [{"X": 0.37149596214294434, "Y": 0.8600543737411499}, {"X": 0.465541273355484, "Y": 0.8600548505783081}, {"X": 0.46554121375083923, "Y": 0.8817551732063293}, {"X": 0.3714958727359772, "Y": 0.8817546367645264}]}, "Id": "4c2fadbc-d828-47ed-87a6-87733a84f628"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "Damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04790414497256279, "Height": 0.019069869071245193, "Left": 0.012565430253744125, "Top": 0.9215897917747498}, "Polygon": [{"X": 0.012565460056066513, "Y": 0.9215897917747498}, {"X": 0.060469575226306915, "Y": 0.9215900301933289}, {"X": 0.06046954169869423, "Y": 0.9406596422195435}, {"X": 0.012565430253744125, "Y": 0.9406593441963196}]}, "Id": "72c54618-be0f-40f9-a95b-23c6dfd494f0"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009091035462915897, "Height": 0.01445034146308899, "Left": 0.06395555287599564, "Top": 0.9216806888580322}, "Polygon": [{"X": 0.06395558267831802, "Y": 0.9216806888580322}, {"X": 0.07304658740758896, "Y": 0.921680748462677}, {"X": 0.07304656505584717, "Y": 0.9361310005187988}, {"X": 0.06395555287599564, "Y": 0.936130940914154}]}, "Id": "4ac48529-a3fc-428f-971b-19cfaef3b44a"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04942091181874275, "Height": 0.018206244334578514, "Left": 0.07618879526853561, "Top": 0.9217742085456848}, "Polygon": [{"X": 0.076188825070858, "Y": 0.9217742085456848}, {"X": 0.12560971081256866, "Y": 0.9217745065689087}, {"X": 0.12560966610908508, "Y": 0.9399804472923279}, {"X": 0.07618879526853561, "Y": 0.939980149269104}]}, "Id": "a87255d5-8f8d-4cd0-9052-89d295cc217a"}, {"BlockType": "WORD", "Confidence": 99.61276245117188, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01409110613167286, "Height": 0.012114289216697216, "Left": 0.12930099666118622, "Top": 0.9243442416191101}, "Polygon": [{"X": 0.1293010264635086, "Y": 0.9243442416191101}, {"X": 0.14339211583137512, "Y": 0.9243443608283997}, {"X": 0.14339208602905273, "Y": 0.9364585280418396}, {"X": 0.12930099666118622, "Y": 0.9364584684371948}]}, "Id": "e5d3cd42-aed5-4a84-b0dd-ac5090377a77"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049946822226047516, "Height": 0.014403942972421646, "Left": 0.14699511229991913, "Top": 0.9219081997871399}, "Polygon": [{"X": 0.14699514210224152, "Y": 0.9219081997871399}, {"X": 0.19694194197654724, "Y": 0.9219084978103638}, {"X": 0.19694189727306366, "Y": 0.9363121390342712}, {"X": 0.14699511229991913, "Y": 0.9363118410110474}]}, "Id": "47a24e9b-b950-40bb-933a-c2d005ad5fc3"}, {"BlockType": "WORD", "Confidence": 98.88392639160156, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041047610342502594, "Height": 0.018277080729603767, "Left": 0.20047545433044434, "Top": 0.9218462109565735}, "Polygon": [{"X": 0.20047549903392792, "Y": 0.9218462109565735}, {"X": 0.24152307212352753, "Y": 0.9218464493751526}, {"X": 0.24152302742004395, "Y": 0.9401232600212097}, {"X": 0.20047545433044434, "Y": 0.9401230216026306}]}, "Id": "6eeecb5a-d8bd-4b99-82f4-7c721a9642cb"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011065641418099403, "Height": 0.014505922794342041, "Left": 0.24469523131847382, "Top": 0.9216125011444092}, "Polygon": [{"X": 0.2446952760219574, "Y": 0.9216125011444092}, {"X": 0.25576087832450867, "Y": 0.921612560749054}, {"X": 0.2557608485221863, "Y": 0.9361184239387512}, {"X": 0.24469523131847382, "Y": 0.9361183643341064}]}, "Id": "6c769305-9ab1-4b62-bae5-128a7db32887"}, {"BlockType": "WORD", "Confidence": 99.4834213256836, "Text": "AGS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027531780302524567, "Height": 0.014381995424628258, "Left": 0.2575889527797699, "Top": 0.9215660095214844}, "Polygon": [{"X": 0.2575889825820923, "Y": 0.9215660095214844}, {"X": 0.28512072563171387, "Y": 0.9215661883354187}, {"X": 0.2851206958293915, "Y": 0.9359480142593384}, {"X": 0.2575889527797699, "Y": 0.935947835445404}]}, "Id": "8c5c1111-1c20-4dbc-8e12-1835723eaf2c"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "locations", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050178367644548416, "Height": 0.014135636389255524, "Left": 0.288583368062973, "Top": 0.9219068884849548}, "Polygon": [{"X": 0.2885833978652954, "Y": 0.9219068884849548}, {"X": 0.33876174688339233, "Y": 0.9219071865081787}, {"X": 0.33876168727874756, "Y": 0.9360425472259521}, {"X": 0.288583368062973, "Y": 0.9360422492027283}]}, "Id": "4a95487b-6be8-49ac-a594-a1e1b79c60c1"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "cd5a5248-3645-4cdf-b5a5-1896ea69983c", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "cd5a5248-3645-4cdf-b5a5-1896ea69983c", "content-type": "application/x-amz-json-1.1", "content-length": "47044", "date": "Thu, 18 Sep 2025 07:52:58 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c25bbecc-2601-4dc7-8642-284235feaaf3", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:53:00 GMT", "content-type": "application/json", "content-length": "877", "connection": "keep-alive", "x-amzn-requestid": "c25bbecc-2601-4dc7-8642-284235feaaf3"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It appears to be an Ingate or Outgate document. Let's examine: Title \"J1 (Gate Information)\" suggests gate info. Equipment ID, Terminal, Date and Time, Activity: Arrival. So it's Ingate. There's no mention of departure. So doc_type = ingate. Also check if any other categories: It's not a bill of lading, invoice, etc. So ingate. We must output a single object for page 1: page_no 1, doc_type \"ingate\".\n\nReturn via classify_logistics_doc_type tool."}}}, {"toolUse": {"toolUseId": "tooluse_jUj1XjU8Sjya1oCM0df6BA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2228, "outputTokens": 170, "totalTokens": 2398}, "metrics": {"latencyMs": 891}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/71106c4d_25_ingate_6.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nJ1 (Gate Information)\nEquipment Information\nEvent Information\nEquipment ID: MRSU 547108\nTerminal: CH<PERSON><PERSON>O 59TH ST\nAssociated Equipment ID: SSRT 823120\nDate and Time: 07/09/2025 19:19\nL/E Status: Load\nActivity: Arrival\nLength: 40'\nDrayage Co: SUPER STAR FREIGHT INC\nType: CN\nDriver Information: CORDOVA, SALVADOR\nSCAC Code: SSRT\nSeal Number: C992213\nBooking Number:\nInspection Information:\nPlease refer ShipCSX images for Inspection Information.\nDamage is captured on recorded images at AGS locations\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}