{"cells": [{"cell_type": "code", "execution_count": 12, "id": "8fadba99", "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "import ast\n", "from typing import Any, Dict\n", "\n", "def extract_json_from_backticks(text: str) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Extract JSON from text - handles both backtick-fenced and plain JSON.\n", "    - First tries to find JSON enclosed in triple backticks (``` ... ```).\n", "    - If no backticks found, tries to parse the entire text as JSON.\n", "    - Accepts optional fence label (e.g. ```json or ```json\\n).\n", "    - Tries json.loads first, then tries extracting {...} or [...] slices,\n", "      then falls back to a best-effort ast.literal_eval after simple literal replacements.\n", "    - Never raises: returns a dict with 'working_json' True and 'data' on success,\n", "      or 'working_json' False with 'error' and the extracted 'content' on failure.\n", "\n", "    Returns:\n", "      {'working_json': True, 'data': <parsed object>}\n", "      OR\n", "      {'working_json': False, 'error': 'message', 'content': '<extracted_text (trimmed)>'}\n", "    \"\"\"\n", "    # First try to find fenced block with triple backticks\n", "    fence_pat = re.compile(r'```(?:[^\\n]*)\\n?(.*?)```', re.DOTALL)\n", "    m = fence_pat.search(text)\n", "\n", "    if m:\n", "        # Found backtick-fenced content\n", "        content = m.group(1)\n", "        stripped = content.strip()\n", "    else:\n", "        # No backticks found, try to parse the entire text as JSON\n", "        stripped = text.strip()\n", "\n", "    # 1) Try strict JSON\n", "    try:\n", "        parsed = json.loads(stripped)\n", "        return {'working_json': True, 'data': parsed}\n", "    except json.JSONDecodeError as first_err:\n", "        last_err = str(first_err)\n", "\n", "    # 2) Try to locate a JSON object/array substring within the content (first {...} or [...])\n", "    for start_ch, end_ch in (('{', '}'), ('[', ']')):\n", "        s_idx = stripped.find(start_ch)\n", "        e_idx = stripped.rfind(end_ch)\n", "        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:\n", "            candidate = stripped[s_idx:e_idx+1].strip()\n", "            try:\n", "                parsed = json.loads(candidate)\n", "                return {'working_json': True, 'data': parsed}\n", "            except json.JSONDecodeError as e:\n", "                last_err = f\"{last_err}; fallback slice JSON error: {e}\"\n", "\n", "    # 3) Best-effort Python-literal parse (handles single quotes, trailing commas sometimes)\n", "    #    Convert JS literals to Python equivalents (simple replacement).\n", "    #    This may modify inside strings in extreme cases, but it's a pragmatic fallback.\n", "    try:\n", "        alt = re.sub(r'\\bnull\\b', 'None', stripped, flags=re.IGNORECASE)\n", "        alt = re.sub(r'\\btrue\\b', 'True', alt, flags=re.IGNORECASE)\n", "        alt = re.sub(r'\\bfalse\\b', 'False', alt, flags=re.IGNORECASE)\n", "\n", "        # Attempt to isolate braces/brackets first (if present)\n", "        s_idx = alt.find('{')\n", "        e_idx = alt.rfind('}')\n", "        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:\n", "            alt_candidate = alt[s_idx:e_idx+1]\n", "        else:\n", "            s_idx = alt.find('[')\n", "            e_idx = alt.rfind(']')\n", "            alt_candidate = alt[s_idx:e_idx+1] if (s_idx != -1 and e_idx != -1 and e_idx > s_idx) else alt\n", "\n", "        parsed = ast.literal_eval(alt_candidate)\n", "        return {'working_json': True, 'data': parsed}\n", "    except Exception as e:\n", "        last_err = f\"{last_err}; ast fallback error: {e}\"\n", "\n", "    # If everything fails, return failure with debugging info (trim content for safety)\n", "    debug_content = stripped\n", "    return {\n", "        'working_json': <PERSON><PERSON><PERSON>,\n", "        'error': 'Failed to parse JSON from text; last error: ' + last_err,\n", "        'content': debug_content\n", "    }\n"]}, {"cell_type": "code", "execution_count": 23, "id": "fe5f116a", "metadata": {}, "outputs": [], "source": ["response = {'ResponseMetadata': {'RequestId': '384c8b9c-3eaa-48e7-aa88-ebf469976d78', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:03 GMT', 'content-type': 'application/json', 'content-length': '4974', 'connection': 'keep-alive', 'x-amzn-requestid': '384c8b9c-3eaa-48e7-aa88-ebf469976d78'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with two pages. Need to classify each page.\\n\\nThe content looks like a weight and research program (maybe weight and inspection certificate). The header: \"Weight and Research Program\" and terms like \"We certify that this shipment has been inspected and/or reweighed\" and \"Weight and Research Section\". Also includes \"As found upon Examination\". This likely is a \"weight_and_inspection_cert\" or \"inspection_cert\" or \"nmfc_cert\" if correction. The description includes \"Original\" and \"Correction\". It says \"Original Correction\". It shows \"Original Correction\" date. It shows \"We certify that this shipment has been inspected and/or reweighed\". That matches weight and inspection certificate. Does it mention any correction? The header says \"Original Correction\". But does it have \"Original\" or \"Correction\"? It says \"Original Correction\" maybe means original or corrected. But no explicit \"Original\" vs \"Correction\" section. The text: \"Original Correction 9/04/25\". It might be a corrected certificate. The instruction for weight_and_inspection_cert: If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert otherwise keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons classify as weight_and_inspection_cert; else inspection_cert.\\n\\nWe see weight details: \"Weight and Research Program\". The weight is indicated: \"Weights\", \"Gross\", \"Tare\" maybe? It lists \"Weights\" but not explicit weight numbers. It does show \"CUBE 22.84 UNITS 1\" and \"51.00x43.00x18.00\" and \"UPD PER ITEM 111460 DENS14.97 PCF\". It mentions \"WEIGHT\" maybe? It says \"Weight and Research Section\". It doesn\\'t show explicit weight numbers like \"Gross: 500 lbs\". But might still be weight and inspection certificate. The presence of \"Original Correction\" suggests a corrected certificate. For nmfc_cert, keywords: \"Original and inspection\" or \"Corrected class\" etc. The text says: \"As found upon Examination\" and \"We certify that this shipment has been inspected and/or reweighed\". This seems like a certificate of inspection. It doesn\\'t explicitly mention \"Original and inspection\" or \"Corrected\". But it says \"Original Correction\". That might be a combined phrase. But the actual header says \"Weight and Research Program\". The doc type likely \"weight_and_inspection_cert\".\\n\\nBut check if any nmfc_cert keywords present: \"As found upon Examination\" is part of weight_and_inspection_cert description. But nmfc_cert keywords: \"As found or Original and inspection or Corrected class or Correction information\". The phrase \"Original Correction\" might be a keyword? But the guidelines say \"If it has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert.\" The provided keywords for nmfc_cert: \"As found or Original and inspection or Corrected class or Correction information\". It says \"As found upon Examination\" appears. That phrase \"As found\" is a keyword. That might lead to nmfc_cert classification. But also weight_and_inspection_cert also has \"As found upon Examination\" as part of its description. Actually weight_and_inspection_cert description: \"If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert.\" But it also says \"If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert.\" That means if we see nmfc_cert keywords, we choose nmfc_cert. The phrase \"As found upon Examination\" is listed under weight_and_inspection_cert too? Let\\'s re-read: In weight_and_inspection_cert description: \"If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify as inspection_cert.\" Then the note: \"Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\" The nmfc_cert keywords: \"As found or Original and inspection or Corrected class or Correction information\". So \"As found\" appears in weight_and_inspection_cert as well? Actually weight_and_inspection_cert description didn\\'t mention \"As found\". But the note says if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, then classify as nmfc_cert. So if we see \"As found\" on the page, we might classify as nmfc_cert. The page has \"As found upon Examination\". That matches \"As found\" keyword. So likely nmfc_cert.\\n\\nBut the page also includes \"Original'}}}]}}, 'stopReason': 'max_tokens', 'usage': {'inputTokens': 3302, 'outputTokens': 997, 'totalTokens': 4299}, 'metrics': {'latencyMs': 4283}}"]}, {"cell_type": "code", "execution_count": 24, "id": "a37f8d31", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ResponseMetadata': {'RequestId': '384c8b9c-3eaa-48e7-aa88-ebf469976d78',\n", "  'HTTPStatusCode': 200,\n", "  'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:03 GMT',\n", "   'content-type': 'application/json',\n", "   'content-length': '4974',\n", "   'connection': 'keep-alive',\n", "   'x-amzn-requestid': '384c8b9c-3eaa-48e7-aa88-ebf469976d78'},\n", "  'RetryAttempts': 0},\n", " 'output': {'message': {'role': 'assistant',\n", "   'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with two pages. Need to classify each page.\\n\\nThe content looks like a weight and research program (maybe weight and inspection certificate). The header: \"Weight and Research Program\" and terms like \"We certify that this shipment has been inspected and/or reweighed\" and \"Weight and Research Section\". Also includes \"As found upon Examination\". This likely is a \"weight_and_inspection_cert\" or \"inspection_cert\" or \"nmfc_cert\" if correction. The description includes \"Original\" and \"Correction\". It says \"Original Correction\". It shows \"Original Correction\" date. It shows \"We certify that this shipment has been inspected and/or reweighed\". That matches weight and inspection certificate. Does it mention any correction? The header says \"Original Correction\". But does it have \"Original\" or \"Correction\"? It says \"Original Correction\" maybe means original or corrected. But no explicit \"Original\" vs \"Correction\" section. The text: \"Original Correction 9/04/25\". It might be a corrected certificate. The instruction for weight_and_inspection_cert: If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert otherwise keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons classify as weight_and_inspection_cert; else inspection_cert.\\n\\nWe see weight details: \"Weight and Research Program\". The weight is indicated: \"Weights\", \"Gross\", \"Tare\" maybe? It lists \"Weights\" but not explicit weight numbers. It does show \"CUBE 22.84 UNITS 1\" and \"51.00x43.00x18.00\" and \"UPD PER ITEM 111460 DENS14.97 PCF\". It mentions \"WEIGHT\" maybe? It says \"Weight and Research Section\". It doesn\\'t show explicit weight numbers like \"Gross: 500 lbs\". But might still be weight and inspection certificate. The presence of \"Original Correction\" suggests a corrected certificate. For nmfc_cert, keywords: \"Original and inspection\" or \"Corrected class\" etc. The text says: \"As found upon Examination\" and \"We certify that this shipment has been inspected and/or reweighed\". This seems like a certificate of inspection. It doesn\\'t explicitly mention \"Original and inspection\" or \"Corrected\". But it says \"Original Correction\". That might be a combined phrase. But the actual header says \"Weight and Research Program\". The doc type likely \"weight_and_inspection_cert\".\\n\\nBut check if any nmfc_cert keywords present: \"As found upon Examination\" is part of weight_and_inspection_cert description. But nmfc_cert keywords: \"As found or Original and inspection or Corrected class or Correction information\". The phrase \"Original Correction\" might be a keyword? But the guidelines say \"If it has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert.\" The provided keywords for nmfc_cert: \"As found or Original and inspection or Corrected class or Correction information\". It says \"As found upon Examination\" appears. That phrase \"As found\" is a keyword. That might lead to nmfc_cert classification. But also weight_and_inspection_cert also has \"As found upon Examination\" as part of its description. Actually weight_and_inspection_cert description: \"If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert.\" But it also says \"If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert.\" That means if we see nmfc_cert keywords, we choose nmfc_cert. The phrase \"As found upon Examination\" is listed under weight_and_inspection_cert too? Let\\'s re-read: In weight_and_inspection_cert description: \"If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify as inspection_cert.\" Then the note: \"Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\" The nmfc_cert keywords: \"As found or Original and inspection or Corrected class or Correction information\". So \"As found\" appears in weight_and_inspection_cert as well? Actually weight_and_inspection_cert description didn\\'t mention \"As found\". But the note says if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, then classify as nmfc_cert. So if we see \"As found\" on the page, we might classify as nmfc_cert. The page has \"As found upon Examination\". That matches \"As found\" keyword. So likely nmfc_cert.\\n\\nBut the page also includes \"Original'}}}]}},\n", " 'stopReason': 'max_tokens',\n", " 'usage': {'inputTokens': 3302, 'outputTokens': 997, 'totalTokens': 4299},\n", " 'metrics': {'latencyMs': 4283}}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 20, "id": "5146ecdd", "metadata": {}, "outputs": [], "source": ["if response['stopReason'] == 'end_turn':\n", "    if len(response['output']['message']['content']) > 1:\n", "        content_in_string = response['output']['message']['content'][1][\"text\"]\n", "    else:\n", "        content_in_string = response['output']['message']['content'][0][\"text\"]\n", "    content = extract_json_from_backticks(content_in_string)[\"data\"]\n", "    print(content)"]}, {"cell_type": "code", "execution_count": null, "id": "d62a1557", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "id": "29f6b858", "metadata": {}, "outputs": [], "source": ["response2 = {'ResponseMetadata': {'RequestId': '76698621-4387-44a3-b0c0-51c1e3ca08f1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:46 GMT', 'content-type': 'application/json', 'content-length': '4181', 'connection': 'keep-alive', 'x-amzn-requestid': '76698621-4387-44a3-b0c0-51c1e3ca08f1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a multi-page document. It appears to be a \"Weight and Research Program\" document. The content: includes \"Weight and Research Program\" header. It has weight, density, cube, etc. The description says \"We certify that this shipment has been inspected and/or reweighed ...\". It\\'s a weight & research program. It seems like a \"Weight and Inspection Certificate\" or \"Weight and Inspection Certificate\"? However the definitions: weight_and_inspection_cert is a certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Not exactly. There\\'s also nmfc_cert which is a NMFC Classification Certificate or Correction notice or Weight & inspection certificate but with \"Inspected against original or Corrected against Actual\". The document has \"As found upon Examination\" and \"We certify that this shipment has been inspected and/or reweighed\". That could be a weight and inspection certificate. The content also includes \"Cube\" measurement. So it fits weight_and_inspection_cert. The classification: weight_and_inspection_cert. But we need to check if nmfc_cert: \"If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\" What are nmfc_cert keywords? It says: \"When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords indication: As described or As found or Original and inspection or Corrected class or Correction information. Other keywords optional: NMFC Code, class #.\" The doc has \"As found upon Examination\" phrase. That might be a keyword for nmfc_cert. But does it have NMFC code? Not seeing. But \"As found upon Examination\" is in the doc. That phrase appears in the page.\\n\\nI think weight_and_inspection_cert is correct. But check nmfc_cert: \"When correction is made in weight_and_inspection_cert, nmfc_cert is issued.\" The doc says \"Original Correction\" at top. There\\'s a field \"Original Correction\" and \"We certify that this shipment has been inspected and/or reweighed\". That seems like a correction. It states \"Original Correction 9/04/25\". That might indicate a correction. The presence of \"As found upon Examination\" might be a key. But it\\'s not \"Original and inspection\" or \"Corrected class\". It doesn\\'t show NMFC code. The phrase \"As found upon Examination\" may be a keyword for nmfc_cert. The doc also says \"Original Correction\". This might indeed be a correction notice. But we don\\'t see NMFC code. The definition says \"When correction is made in weight_and_inspection_cert, nmfc_cert is issued.\" So if this is a correction, it might be nmfc_cert. But do we have evidence? There\\'s \"Original Correction\" and \"We certify ... inspected\". But the phrase \"As found upon Examination\" suggests it\\'s a \"weight & inspection certificate\"? But it\\'s possibly a correction.\\n\\nLet\\'s read the spec: weight_and_inspection_cert: \"Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\". nmfc_cert: \"When correction is made in weight_and_inspection_cert, nmfc_cert is issued.\" So if it\\'s a correction, it\\'s nmfc_cert. But we need to see if it\\'s a correction. It has \"Original Correction\" header, \"Date of Issuing\" and \"Original Correction\". The doc has \"Date of Issuing\" and \"Original Correction\". So yes, it\\'s a corrected weight & inspection certificate. The phrase \"As found upon Examination\" appears. So likely nmfc_cert.\\n\\nLet\\'s choose nmfc_cert. The doc is multi-page: page1 and page2. So both pages get same doc_type: nmfc_cert.\\n\\nWe need to output via tool call classify_logistics_doc_type with array of objects: each has page_no and doc_type. For 2 pages.\\n\\nLet\\'s produce that.'}}}, {'text': '{\"documents\":[{\"page_no\":1,\"doc_type\":\"nmfc_cert\"},{\"page_no\":2,\"doc_type\":\"nmfc_cert\"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3299, 'outputTokens': 846, 'totalTokens': 4145}, 'metrics': {'latencyMs': 3751}}"]}, {"cell_type": "code", "execution_count": null, "id": "df680760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 21, "id": "f788b517", "metadata": {}, "outputs": [], "source": ["import boto3"]}, {"cell_type": "code", "execution_count": 22, "id": "6ac7b4a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'1.40.5'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["boto3.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "e3ebc594", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "62e9e235", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "333f44e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "02a99ee5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "6c9c2fda", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<page1>\n", "FedEx®\n", "September 19, 2025\n", "®\n", "Dear Customer,\n", "The following is the proof-of-delivery for tracking number: **********\n", "Delivery Information:\n", "Status:\n", "Delivered\n", "Delivery date:\n", "Sep 5, 2025 06:50\n", "Signed for by:\n", "** DRIVER SPOTTED **\n", "Service type:\n", "FedEx Freight Economy\n", "Special Handling:\n", "Please see following page for Signature\n", "Shipping Information:\n", "Tracking number:\n", "**********\n", "Ship Date:\n", "Aug 27, 2025\n", "Weight:\n", "310.0 LB/140.74 KG\n", "Recipient:\n", "Shipper:\n", "CARDINAL HEALTH MEDICAL PRODUC\n", "RIVERSIDE LOGISTICS\n", "4551 E PHILADELPHIA ST\n", "5662 EASTPORT BLVD\n", "ONTARIO, CA, US, 91761-2316\n", "HENRICO, VA, US, 23231\n", "Purchase Order\n", "11746,**********\n", "Thank you for choosing FedEx.\n", "</page1>\n", "\n", "<page2>\n", "Page 2 of 2\n", "FedEx®\n", "DELIVERY RECEIPT\n", "Freight\n", "Freight Bill ********** R0\n", "2200 FORWARD DRIVE\n", "Ship Date 08/27/2025\n", "Bill of Lading 11746\n", "HARRISON, AR 72601\n", "P O **********\n", "Shipper Reference\n", "fedex.com 1 866 393.4585\n", "Origin RCH\n", "Destination MRL\n", "Consignee\n", "Trailer # X11232\n", "Shipper\n", "CARDINAL HEALTH MEDICAL PRODUC\n", "RIVERSIDE LOGISTICS\n", "4551 E PHILADELPHIA ST\n", "5662 EASTPORT BLVD\n", "ONTARIO\n", "HENRICO\n", "CA 91761-2316\n", "US\n", "VA 23231\n", "US\n", "Time\n", "PIECES\n", "PKG\n", "H/U\n", "HM\n", "DESCRIPTION\n", "FINT(LBS)\n", "<PERSON><PERSON>\n", "FedEx Freight Economy\n", "NMFC\n", "PCF CLASS\n", "RATE\n", "TOTAL CHARGES\n", "APPT 090525 :00SETUP090425 09 16\n", "CRYSTAL SIMMONS (*************\n", "0700 APPT\n", "APPT 263828\n", "DRIVER COPY\n", "Date\n", "L#\n", "SD ontent Subject no or to Count\n", "<PERSON><PERSON>\n", "allet\n", "or\n", "BY ACCEPTING THE SHIPMENT, YOU AGREE TO BE FULLY RESPO<PERSON><PERSON>LE FOR ANY ADDITIONAL APPL CABLE CHARGES FOR DELIVERY SERVICES RENDERED INCLUDING BUT NOT LIMITED TO DETENTION\n", "10\n", "1 PREPAID - W<PERSON>L INVOICE THIRD PARTY 310\n", "ACCESSORIAL SERVICES PERFORMED\n", "INVOICE RESPONSIBLE PARTY\n", "INSIDE DELIVERY\n", "SORT & SEGREGATE\n", "DETENTION\n", "RESIDENTIAL-LIMITED ACCESS\n", "LIFT GATE\n", "OTHERS\n", "Delv. Driver & #.\n", "+6243 0.11ame2\n", "Date\n", "9-5-25\n", "Arrive\n", "66:50\n", "<PERSON><PERSON><PERSON>\n", "97:20\n", "Customer Requirements/Appointment Instruction\n", "# of Skids\n", "# of Pcs\n", "OS&D #\n", "APPOINTMENT FROM 05 00 TO 09:00\n", "Shipment received in apparent good order with wrap intact unless otherwise noted\n", "APPT 090525 09 00SETUP090425 09:16\n", "CRYSTAL SIMMONS (*************\n", "0700 APPT\n", "Received by:\n", "APPT 263828\n", "Over\n", "Damage\n", "Exceptions\n", "Short\n", "Wrap Broken\n", "</page2>\n", "\n", "<page3>\n", "Page 1 of 2\n", "FedEx\n", "DELIVERY RECEIPT\n", "Freight\n", "Freight Bill ********** R0\n", "2200 FORWARD DRIVE\n", "Ship Date 08/27/2025\n", "Bill of Lading 11746\n", "HARRISON, AR 72601\n", "P.O **********\n", "Shipper Reference\n", "fedex.com 1.866\n", "393.4585\n", "Origin RCH\n", "Destination MRL\n", "Consignee\n", "Trailer # X11232\n", "Shipper\n", "CARDINAL HEALTH MEDICAL PRODUC\n", "RIVERSIDE LOGISTICS\n", "4551 E PHILADELPHIA ST\n", "5662 EASTPORT BLVD\n", "ONTARIO\n", "HENRICO\n", "CA 91761-2316\n", "US\n", "VA 23231\n", "US\n", "FedEx Freight Economy\n", "PIECES\n", "PKG\n", "H/U\n", "HM\n", "DESCRIPTION\n", "WT(LBS)\n", "NMFC\n", "PCF CLASS\n", "RATE\n", "TOTAL CHARGES\n", "PO# **********\n", "10\n", "OR BLUE TOWEL\n", "310\n", "150\n", "202508271216\n", "OR#\n", "APPOINTMENT DELIVERY **\n", "CALIFORNIA COMPLIANCE\n", "FUEL SURCHG LTL SHPT 32.80%\n", "DRIVER COPY\n", "603349270-108-07-74\n", "CZAR\n", "198\n", "ILS 10985\n", "** FAK RATES APPLIED **\n", "04 ZONE NUMBER\n", "PRICING ADJUSTMENT\n", "CAN NOT AUTO RATE\n", "SCHED090425 00:00 SETUP090425 07:04\n", "PEND TRAP (000)000-0000\n", "APPOINTMENT FROM 05:00 TO 09:00\n", "BY\n", "ACCEPTING\n", "THE\n", "SHIPMENT, YOU AGREE TO BE FULLY RES<PERSON><PERSON><PERSON><PERSON> FOR ANY ADDITIONAL APPL\n", "CABLE CHARGES FOR\n", "DELIVERY\n", "SERVICES\n", "RENDERED\n", "INCLUDING\n", "BUT\n", "NOT\n", "LIMITED\n", "TO\n", "DETENTION\n", "PREPAID - <PERSON><PERSON><PERSON> INVOICE THIRD PARTY\n", "ACCESSORIAL SERVICES PERFORMED.\n", "WILL INVOICE RESPONSIBLE PARTY\n", "INSIDE DELIVERY\n", "SORT & SEGREGATE\n", "DETENTION\n", "RESIDENTIAL LIMITED ACCESS\n", "LIFT GATE\n", "OTHERS\n", "Delv Driver & #\n", "Date\n", "Arrive\n", "<PERSON><PERSON><PERSON>\n", "Customer Requirements/Appointment Instruction\n", "# of Skids\n", "# of Pcs\n", "OS&D #\n", "APPOINTMENT FROM 05:00 TO 09 00\n", "Shipment received in apparent good order with wrap intact unless otherwise noted\n", "APPT 090525 :00SETUP090425 09:16\n", "CRYSTAL SIMMONS (*************\n", "0700 APPT\n", "Received by:\n", "APPT 263828\n", "Over\n", "Damage\n", "Exceptions\n", "Short\n", "Wrap Broken\n", "</page3>\n"]}], "source": ["print(\"<page1>\\nFedEx®\\nSeptember 19, 2025\\n®\\nDear Customer,\\nThe following is the proof-of-delivery for tracking number: **********\\nDelivery Information:\\nStatus:\\nDelivered\\nDelivery date:\\nSep 5, 2025 06:50\\nSigned for by:\\n** DRIVER SPOTTED **\\nService type:\\nFedEx Freight Economy\\nSpecial Handling:\\nPlease see following page for Signature\\nShipping Information:\\nTracking number:\\n**********\\nShip Date:\\nAug 27, 2025\\nWeight:\\n310.0 LB/140.74 KG\\nRecipient:\\nShipper:\\nCARDINAL HEALTH MEDICAL PRODUC\\nRIVERSIDE LOGISTICS\\n4551 E PHILADELPHIA ST\\n5662 EASTPORT BLVD\\nONTARIO, CA, US, 91761-2316\\nHENRICO, VA, US, 23231\\nPurchase Order\\n11746,**********\\nThank you for choosing FedEx.\\n</page1>\\n\\n<page2>\\nPage 2 of 2\\nFedEx®\\nDELIVERY RECEIPT\\nFreight\\nFreight Bill ********** R0\\n2200 FORWARD DRIVE\\nShip Date 08/27/2025\\nBill of Lading 11746\\nHARRISON, AR 72601\\nP O **********\\nShipper Reference\\nfedex.com 1 866 393.4585\\nOrigin RCH\\nDestination MRL\\nConsignee\\nTrailer # X11232\\nShipper\\nCARDINAL HEALTH MEDICAL PRODUC\\nRIVERSIDE LOGISTICS\\n4551 E PHILADELPHIA ST\\n5662 EASTPORT BLVD\\nONTARIO\\nHENRICO\\nCA 91761-2316\\nUS\\nVA 23231\\nUS\\nTime\\nPIECES\\nPKG\\nH/U\\nHM\\nDESCRIPTION\\nFINT(LBS)\\nTraillum\\nFedEx Freight Economy\\nNMFC\\nPCF CLASS\\nRATE\\nTOTAL CHARGES\\nAPPT 090525 :00SETUP090425 09 16\\nCRYSTAL SIMMONS (*************\\n0700 APPT\\nAPPT 263828\\nDRIVER COPY\\nDate\\nL#\\nSD ontent Subject no or to Count\\nFus\\nallet\\nor\\nBY ACCEPTING THE SHIPMENT, YOU AGREE TO BE FULLY RESPONSIBLE FOR ANY ADDITIONAL APPL CABLE CHARGES FOR DELIVERY SERVICES RENDERED INCLUDING BUT NOT LIMITED TO DETENTION\\n10\\n1 PREPAID - WILL INVOICE THIRD PARTY 310\\nACCESSORIAL SERVICES PERFORMED\\nINVOICE RESPONSIBLE PARTY\\nINSIDE DELIVERY\\nSORT & SEGREGATE\\nDETENTION\\nRESIDENTIAL-LIMITED ACCESS\\nLIFT GATE\\nOTHERS\\nDelv. Driver & #.\\n+6243 0.11ame2\\nDate\\n9-5-25\\nArrive\\n66:50\\nDepart\\n97:20\\nCustomer Requirements/Appointment Instruction\\n# of Skids\\n# of Pcs\\nOS&D #\\nAPPOINTMENT FROM 05 00 TO 09:00\\nShipment received in apparent good order with wrap intact unless otherwise noted\\nAPPT 090525 09 00SETUP090425 09:16\\nCRYSTAL SIMMONS (*************\\n0700 APPT\\nReceived by:\\nAPPT 263828\\nOver\\nDamage\\nExceptions\\nShort\\nWrap Broken\\n</page2>\\n\\n<page3>\\nPage 1 of 2\\nFedEx\\nDELIVERY RECEIPT\\nFreight\\nFreight Bill ********** R0\\n2200 FORWARD DRIVE\\nShip Date 08/27/2025\\nBill of Lading 11746\\nHARRISON, AR 72601\\nP.O **********\\nShipper Reference\\nfedex.com 1.866\\n393.4585\\nOrigin RCH\\nDestination MRL\\nConsignee\\nTrailer # X11232\\nShipper\\nCARDINAL HEALTH MEDICAL PRODUC\\nRIVERSIDE LOGISTICS\\n4551 E PHILADELPHIA ST\\n5662 EASTPORT BLVD\\nONTARIO\\nHENRICO\\nCA 91761-2316\\nUS\\nVA 23231\\nUS\\nFedEx Freight Economy\\nPIECES\\nPKG\\nH/U\\nHM\\nDESCRIPTION\\nWT(LBS)\\nNMFC\\nPCF CLASS\\nRATE\\nTOTAL CHARGES\\nPO# **********\\n10\\nOR BLUE TOWEL\\n310\\n150\\n202508271216\\nOR#\\nAPPOINTMENT DELIVERY **\\nCALIFORNIA COMPLIANCE\\nFUEL SURCHG LTL SHPT 32.80%\\nDRIVER COPY\\n603349270-108-07-74\\nCZAR\\n198\\nILS 10985\\n** FAK RATES APPLIED **\\n04 ZONE NUMBER\\nPRICING ADJUSTMENT\\nCAN NOT AUTO RATE\\nSCHED090425 00:00 SETUP090425 07:04\\nPEND TRAP (000)000-0000\\nAPPOINTMENT FROM 05:00 TO 09:00\\nBY\\nACCEPTING\\nTHE\\nSHIPMENT, YOU AGREE TO BE FULLY RESPONSIBLE FOR ANY ADDITIONAL APPL\\nCABLE CHARGES FOR\\nDELIVERY\\nSERVICES\\nRENDERED\\nINCLUDING\\nBUT\\nNOT\\nLIMITED\\nTO\\nDETENTION\\nPREPAID - WILL INVOICE THIRD PARTY\\nACCESSORIAL SERVICES PERFORMED.\\nWILL INVOICE RESPONSIBLE PARTY\\nINSIDE DELIVERY\\nSORT & SEGREGATE\\nDETENTION\\nRESIDENTIAL LIMITED ACCESS\\nLIFT GATE\\nOTHERS\\nDelv Driver & #\\nDate\\nArrive\\nDepart\\nCustomer Requirements/Appointment Instruction\\n# of Skids\\n# of Pcs\\nOS&D #\\nAPPOINTMENT FROM 05:00 TO 09 00\\nShipment received in apparent good order with wrap intact unless otherwise noted\\nAPPT 090525 :00SETUP090425 09:16\\nCRYSTAL SIMMONS (*************\\n0700 APPT\\nReceived by:\\nAPPT 263828\\nOver\\nDamage\\nExceptions\\nShort\\nWrap Broken\\n</page3>\")"]}, {"cell_type": "code", "execution_count": null, "id": "e5fc1fcc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}