{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9979555606842041, "Left": 0.0, "Top": 0.002009193878620863}, "Polygon": [{"X": 0.0, "Y": 0.0026915324851870537}, {"X": 1.0, "Y": 0.002009193878620863}, {"X": 1.0, "Y": 0.9994205832481384}, {"X": 0.0, "Y": 0.9999647736549377}]}, "Id": "07a4c7ab-cb3b-406d-96fc-1b4bf09ca85f", "Relationships": [{"Type": "CHILD", "Ids": ["0f172099-3bd4-4053-b06c-d1dc954071e6", "93d55229-2b4b-40fb-a3a3-2d00992d1bba", "eb03c627-c0c4-4fe0-92fe-2dc1039e7c2a", "9d4e860e-1bb2-41b1-96dc-f041e738fc7c", "3a77a77d-6bff-4929-89fd-a05dd4c532e4", "22ccac77-0347-47a4-956f-e51bbc2532cc", "32203c8c-b728-4623-a976-11cbb247bca8", "8aa0adec-ec0a-4b11-a856-fd27e903631d", "f71b1691-b0f2-42d1-b488-bfe1433edcfc", "f228dd6c-b85d-4d2a-8e20-19ac96321685", "6b3c477e-d425-49cd-b5b5-46799226c40b", "208b6840-9558-4cb6-887a-87e11accb3fb", "cd9e3c62-5f85-487b-b2e2-571e26806502", "8592a52c-55b8-4578-90b6-8da72131019b", "67ea44cb-2e8f-4365-95de-c183b4ceed91", "a97e83b6-91f1-4d8c-a133-30bab677ff81", "5fa04338-4a55-4e22-a0f7-8ed1c2a8e867", "fa753d4d-5fe4-4a26-a7b6-9835f26cc687", "bc46f82e-103a-4374-9a95-970c43aba6cd", "db58c23b-6bf4-43ac-8f8e-68fc068028fb", "226a5b26-41b2-4242-8b34-573d5a84be8b", "eab99398-f9d5-4ad4-b7ae-395f7804190c", "259138f8-006e-4ff7-8cbc-dd7eef06605d", "81c545c2-91a0-4cb8-810c-4bf826576686", "1bcb3b75-047c-47bb-ada4-42f5e1afcd33", "4a98fe22-19c9-4b7c-9123-c537dc7d67ea", "65c86fa9-8910-4bee-b9a2-525635faa320", "43daaf8e-6de9-49b0-a1d5-e3cc39c55146", "8ceba84b-8506-44de-9d29-7130f9f1c4b2", "6da45441-c645-4ad4-a301-b8f59d4deb4b", "3700c98a-fedd-4659-900f-aed95ea8ce19", "049bec64-6362-4885-bf40-035d73e9c14d", "2c5e29e9-7308-450c-97a4-1df447ff0c83", "d170f99d-a775-4d92-9b90-e20d3e70af1f", "ef06ef64-19a0-4387-b362-a48dc7b839c0", "96bb088f-3ba1-49b8-8686-f8a18d0feeb6", "8903746a-f565-40b1-bb31-30524b7235f0", "7cf58b4c-71a3-40e6-ba1d-83f81d3eb832", "59971cdc-1b6f-47bd-92b1-77e28b852279", "c2f37329-8848-4691-bff0-80eb44bce911", "23f5173c-28b6-466f-8d60-c1d04c8cff1d", "48d05ac0-7710-44f2-9e0c-6503b9a6c682"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "DATE: 2-Sep", "Geometry": {"BoundingBox": {"Width": 0.1343451291322708, "Height": 0.015511228702962399, "Left": 0.24907651543617249, "Top": 0.10191360116004944}, "Polygon": [{"X": 0.24907651543617249, "Y": 0.102003313601017}, {"X": 0.38340842723846436, "Y": 0.10191360116004944}, {"X": 0.3834216296672821, "Y": 0.11733540147542953}, {"X": 0.24908801913261414, "Y": 0.11742483079433441}]}, "Id": "0f172099-3bd4-4053-b06c-d1dc954071e6", "Relationships": [{"Type": "CHILD", "Ids": ["88aa663b-aee6-4456-8800-cc91b6186e66", "b6f6df53-58a6-4a83-be0e-410738a8d785"]}]}, {"BlockType": "LINE", "Confidence": 99.80794525146484, "Text": "UNIT #: 6063/283420", "Geometry": {"BoundingBox": {"Width": 0.22098317742347717, "Height": 0.01477088313549757, "Left": 0.23158307373523712, "Top": 0.13764864206314087}, "Polygon": [{"X": 0.23158307373523712, "Y": 0.1377951204776764}, {"X": 0.4525528848171234, "Y": 0.13764864206314087}, {"X": 0.4525662362575531, "Y": 0.152273491024971}, {"X": 0.2315937727689743, "Y": 0.15241952240467072}]}, "Id": "93d55229-2b4b-40fb-a3a3-2d00992d1bba", "Relationships": [{"Type": "CHILD", "Ids": ["1474a493-daef-490a-9ca4-537154d6e266", "b5119468-fc17-440e-afd6-6d60c504648f", "8181a0ea-beba-496d-a530-43b80c4ecbe2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck Ready Time:", "Geometry": {"BoundingBox": {"Width": 0.16915929317474365, "Height": 0.015419158153235912, "Left": 0.48266464471817017, "Top": 0.15561673045158386}, "Polygon": [{"X": 0.48266464471817017, "Y": 0.15572842955589294}, {"X": 0.6518074870109558, "Y": 0.15561673045158386}, {"X": 0.6518239378929138, "Y": 0.17092455923557281}, {"X": 0.48267900943756104, "Y": 0.17103590071201324}]}, "Id": "eb03c627-c0c4-4fe0-92fe-2dc1039e7c2a", "Relationships": [{"Type": "CHILD", "Ids": ["69f4e3c0-e9d8-40e1-a057-d9b158210398", "a5848684-9e6d-4195-9d8b-839a98c9ee21", "6dc3d56b-838f-4e29-9986-50f33a788797"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.1736733615398407, "Height": 0.012899169698357582, "Left": 0.22103966772556305, "Top": 0.17417529225349426}, "Polygon": [{"X": 0.22103966772556305, "Y": 0.1742895394563675}, {"X": 0.3947019577026367, "Y": 0.17417529225349426}, {"X": 0.39471304416656494, "Y": 0.18696053326129913}, {"X": 0.22104892134666443, "Y": 0.1870744675397873}]}, "Id": "9d4e860e-1bb2-41b1-96dc-f041e738fc7c", "Relationships": [{"Type": "CHILD", "Ids": ["b9d66545-49e5-4e3f-aceb-be1b13c45330", "4880a3f5-eb88-47b2-a5a4-b75f01c80f06"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.15370632708072662, "Height": 0.015318433754146099, "Left": 0.4835941791534424, "Top": 0.19150330126285553}, "Polygon": [{"X": 0.4835941791534424, "Y": 0.19160403311252594}, {"X": 0.6372842788696289, "Y": 0.19150330126285553}, {"X": 0.6373004913330078, "Y": 0.20672132074832916}, {"X": 0.4836084544658661, "Y": 0.2068217396736145}]}, "Id": "3a77a77d-6bff-4929-89fd-a05dd4c532e4", "Relationships": [{"Type": "CHILD", "Ids": ["cf567379-4aa4-4287-9c80-5f5f3956261e", "b5a4b96a-4248-4638-a8d5-c1e445a1e65f"]}]}, {"BlockType": "LINE", "Confidence": 99.94466400146484, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.24682378768920898, "Height": 0.013465484604239464, "Left": 0.12375569343566895, "Top": 0.2109547108411789}, "Polygon": [{"X": 0.12375569343566895, "Y": 0.21111582219600677}, {"X": 0.3705682158470154, "Y": 0.2109547108411789}, {"X": 0.37057948112487793, "Y": 0.22425955533981323}, {"X": 0.12376426160335541, "Y": 0.2244202047586441}]}, "Id": "22ccac77-0347-47a4-956f-e51bbc2532cc", "Relationships": [{"Type": "CHILD", "Ids": ["cb14b1cb-421a-4f67-8c7e-c97b472cc1c9", "17c355b8-2ede-40af-a4ce-1e96f7016de4", "cdce87ab-9bc9-4dcc-bcf2-66613c44ee07"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.051167819648981094, "Height": 0.012044334784150124, "Left": 0.34116190671920776, "Top": 0.24282944202423096}, "Polygon": [{"X": 0.34116190671920776, "Y": 0.24286261200904846}, {"X": 0.39231935143470764, "Y": 0.24282944202423096}, {"X": 0.39232972264289856, "Y": 0.2548407018184662}, {"X": 0.3411717712879181, "Y": 0.25487378239631653}]}, "Id": "32203c8c-b728-4623-a976-11cbb247bca8", "Relationships": [{"Type": "CHILD", "Ids": ["afcdb056-ce23-4c76-8e81-ead3065e7dcd"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.10014614462852478, "Height": 0.012233363464474678, "Left": 0.8630748391151428, "Top": 0.2528887987136841}, "Polygon": [{"X": 0.8630748391151428, "Y": 0.25295355916023254}, {"X": 0.9632047414779663, "Y": 0.2528887987136841}, {"X": 0.9632209539413452, "Y": 0.2650575637817383}, {"X": 0.8630900382995605, "Y": 0.2651221752166748}]}, "Id": "8aa0adec-ec0a-4b11-a856-fd27e903631d", "Relationships": [{"Type": "CHILD", "Ids": ["a78c3171-8340-4c8f-a76a-0f4d96477758", "f6db1ce9-a2a3-4c6d-ad1d-e39dc7cb008e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.10767072439193726, "Height": 0.01283766608685255, "Left": 0.15752527117729187, "Top": 0.26422953605651855}, "Polygon": [{"X": 0.15752527117729187, "Y": 0.2642990052700043}, {"X": 0.2651863098144531, "Y": 0.26422953605651855}, {"X": 0.2651959955692291, "Y": 0.2769978940486908}, {"X": 0.15753385424613953, "Y": 0.2770671844482422}]}, "Id": "f71b1691-b0f2-42d1-b488-bfe1433edcfc", "Relationships": [{"Type": "CHILD", "Ids": ["be665f3e-d991-430a-aa8d-cc6f9b484786"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.025493381544947624, "Height": 0.0126876225695014, "Left": 0.3544650375843048, "Top": 0.26230761408805847}, "Polygon": [{"X": 0.3544650375843048, "Y": 0.2623240649700165}, {"X": 0.37994760274887085, "Y": 0.26230761408805847}, {"X": 0.3799584209918976, "Y": 0.2749788463115692}, {"X": 0.35447558760643005, "Y": 0.27499523758888245}]}, "Id": "f228dd6c-b85d-4d2a-8e20-19ac96321685", "Relationships": [{"Type": "CHILD", "Ids": ["36355f7e-aa15-4d85-b9c0-5c9937261412"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.03618374466896057, "Height": 0.012243750505149364, "Left": 0.4246581196784973, "Top": 0.2618671655654907}, "Polygon": [{"X": 0.4246581196784973, "Y": 0.2618905007839203}, {"X": 0.4608306288719177, "Y": 0.2618671655654907}, {"X": 0.4608418643474579, "Y": 0.2740876078605652}, {"X": 0.4246689975261688, "Y": 0.27411091327667236}]}, "Id": "6b3c477e-d425-49cd-b5b5-46799226c40b", "Relationships": [{"Type": "CHILD", "Ids": ["c07496b6-c172-414d-a512-c5a6567d9020"]}]}, {"BlockType": "LINE", "Confidence": 99.91862487792969, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.15203790366649628, "Height": 0.01299773808568716, "Left": 0.5928003787994385, "Top": 0.26100343465805054}, "Polygon": [{"X": 0.5928003787994385, "Y": 0.2611016035079956}, {"X": 0.7448233962059021, "Y": 0.26100343465805054}, {"X": 0.7448382377624512, "Y": 0.2739032804965973}, {"X": 0.5928136110305786, "Y": 0.27400118112564087}]}, "Id": "208b6840-9558-4cb6-887a-87e11accb3fb", "Relationships": [{"Type": "CHILD", "Ids": ["0b87dbdb-39b1-4f6a-bb59-ddeb18d210ca", "55373c38-ac3d-4fea-bb10-f743e8c397b2", "83653f58-73dc-46e8-b8c5-cc8ce83c0191"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "1025", "Geometry": {"BoundingBox": {"Width": 0.07541435956954956, "Height": 0.01827477477490902, "Left": 0.8736101984977722, "Top": 0.28849515318870544}, "Polygon": [{"X": 0.8736101984977722, "Y": 0.28854355216026306}, {"X": 0.9490005373954773, "Y": 0.28849515318870544}, {"X": 0.9490245580673218, "Y": 0.3067217469215393}, {"X": 0.8736331462860107, "Y": 0.3067699372768402}]}, "Id": "cd9e3c62-5f85-487b-b2e2-571e26806502", "Relationships": [{"Type": "CHILD", "Ids": ["7aa49d85-d62a-40e3-b7cf-a928e0ba57ea"]}]}, {"BlockType": "LINE", "Confidence": 71.41163635253906, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.051616184413433075, "Height": 0.022746475413441658, "Left": 0.5122780203819275, "Top": 0.3041526973247528}, "Polygon": [{"X": 0.5122780203819275, "Y": 0.304185688495636}, {"X": 0.5638713836669922, "Y": 0.3041526973247528}, {"X": 0.5638942122459412, "Y": 0.3268663287162781}, {"X": 0.5122998356819153, "Y": 0.3268991708755493}]}, "Id": "8592a52c-55b8-4578-90b6-8da72131019b", "Relationships": [{"Type": "CHILD", "Ids": ["a1faafda-65fa-4a27-9a56-5ba1c025c305"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "X-Rite", "Geometry": {"BoundingBox": {"Width": 0.05952225625514984, "Height": 0.013337465934455395, "Left": 0.0916421189904213, "Top": 0.3353727161884308}, "Polygon": [{"X": 0.0916421189904213, "Y": 0.33541053533554077}, {"X": 0.1511555165052414, "Y": 0.3353727161884308}, {"X": 0.15116438269615173, "Y": 0.348672479391098}, {"X": 0.0916503369808197, "Y": 0.34871017932891846}]}, "Id": "67ea44cb-2e8f-4365-95de-c183b4ceed91", "Relationships": [{"Type": "CHILD", "Ids": ["5e6701ef-a24c-4a4a-8985-8584a4fffe4e"]}]}, {"BlockType": "LINE", "Confidence": 98.06879425048828, "Text": "X 1 BH **********", "Geometry": {"BoundingBox": {"Width": 0.18185415863990784, "Height": 0.013331868685781956, "Left": 0.4367141127586365, "Top": 0.33400195837020874}, "Polygon": [{"X": 0.4367141127586365, "Y": 0.3341175615787506}, {"X": 0.618554413318634, "Y": 0.33400195837020874}, {"X": 0.6185683012008667, "Y": 0.3472186028957367}, {"X": 0.43672600388526917, "Y": 0.3473338484764099}]}, "Id": "a97e83b6-91f1-4d8c-a133-30bab677ff81", "Relationships": [{"Type": "CHILD", "Ids": ["7bc2c6df-98cf-4730-b6ca-7eec4a6f2569", "8596cfaf-9f29-4a7d-bb9d-d47b09ee0e64", "13d12351-430d-4d60-adc8-8c5187fc81ce", "acb2d5b2-3c30-4d8a-b703-01e6c9299053"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1035", "Geometry": {"BoundingBox": {"Width": 0.07742656767368317, "Height": 0.021021690219640732, "Left": 0.8656136393547058, "Top": 0.3259267210960388}, "Polygon": [{"X": 0.8656136393547058, "Y": 0.32597601413726807}, {"X": 0.9430126547813416, "Y": 0.3259267210960388}, {"X": 0.9430401921272278, "Y": 0.3468993604183197}, {"X": 0.865639865398407, "Y": 0.34694841504096985}]}, "Id": "5fa04338-4a55-4e22-a0f7-8ed1c2a8e867", "Relationships": [{"Type": "CHILD", "Ids": ["35679376-f597-46a0-8a37-ba3b241534df"]}]}, {"BlockType": "LINE", "Confidence": 47.785396575927734, "Text": "1 SK", "Geometry": {"BoundingBox": {"Width": 0.05798918381333351, "Height": 0.021306835114955902, "Left": 0.4959995448589325, "Top": 0.43253424763679504}, "Polygon": [{"X": 0.4959995448589325, "Y": 0.43257030844688416}, {"X": 0.5539675354957581, "Y": 0.43253424763679504}, {"X": 0.5539887547492981, "Y": 0.45380520820617676}, {"X": 0.49601975083351135, "Y": 0.45384109020233154}]}, "Id": "fa753d4d-5fe4-4a26-a7b6-9835f26cc687", "Relationships": [{"Type": "CHILD", "Ids": ["938969ad-5a01-4445-a13b-f4ec56bf140f"]}]}, {"BlockType": "LINE", "Confidence": 99.80149841308594, "Text": "1250", "Geometry": {"BoundingBox": {"Width": 0.09510881453752518, "Height": 0.024248141795396805, "Left": 0.8821449875831604, "Top": 0.42209717631340027}, "Polygon": [{"X": 0.8821449875831604, "Y": 0.422156423330307}, {"X": 0.9772213101387024, "Y": 0.42209717631340027}, {"X": 0.977253794670105, "Y": 0.4462863504886627}, {"X": 0.8821755647659302, "Y": 0.4463452994823456}]}, "Id": "bc46f82e-103a-4374-9a95-970c43aba6cd", "Relationships": [{"Type": "CHILD", "Ids": ["78c30d0b-2fe5-4298-b193-8e60a49adeaf"]}]}, {"BlockType": "LINE", "Confidence": 79.89607238769531, "Text": "MKE CUSTOM", "Geometry": {"BoundingBox": {"Width": 0.13366205990314484, "Height": 0.013280344195663929, "Left": 0.09239158034324646, "Top": 0.4537929892539978}, "Polygon": [{"X": 0.09239158034324646, "Y": 0.45387572050094604}, {"X": 0.22604404389858246, "Y": 0.4537929892539978}, {"X": 0.2260536402463913, "Y": 0.4669908285140991}, {"X": 0.09239973872900009, "Y": 0.46707332134246826}]}, "Id": "db58c23b-6bf4-43ac-8f8e-68fc068028fb", "Relationships": [{"Type": "CHILD", "Ids": ["f67329d1-e302-45d9-ab77-3c7ec912aee4", "0ed56963-469a-4650-8ea2-f74368925631"]}]}, {"BlockType": "LINE", "Confidence": 90.14569091796875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.014294770546257496, "Height": 0.013029608875513077, "Left": 0.3634490966796875, "Top": 0.46200641989707947}, "Polygon": [{"X": 0.3634490966796875, "Y": 0.4620152711868286}, {"X": 0.3777327537536621, "Y": 0.46200641989707947}, {"X": 0.37774384021759033, "Y": 0.4750272333621979}, {"X": 0.3634600341320038, "Y": 0.47503605484962463}]}, "Id": "226a5b26-41b2-4242-8b34-573d5a84be8b", "Relationships": [{"Type": "CHILD", "Ids": ["f15ca76e-9e61-4602-be38-4d5f7dc6b19a"]}]}, {"BlockType": "LINE", "Confidence": 98.44667053222656, "Text": "X **********", "Geometry": {"BoundingBox": {"Width": 0.13973268866539001, "Height": 0.013086776249110699, "Left": 0.4394785165786743, "Top": 0.4614695608615875}, "Polygon": [{"X": 0.4394785165786743, "Y": 0.4615558981895447}, {"X": 0.5791980028152466, "Y": 0.4614695608615875}, {"X": 0.5792112350463867, "Y": 0.47447025775909424}, {"X": 0.43949025869369507, "Y": 0.4745563268661499}]}, "Id": "eab99398-f9d5-4ad4-b7ae-395f7804190c", "Relationships": [{"Type": "CHILD", "Ids": ["c1ba0e1f-8818-4f2d-94f8-6fa90a40587d", "8008463f-20d1-4f09-84b2-31156c6ddc45"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "MACHINING", "Geometry": {"BoundingBox": {"Width": 0.11939875781536102, "Height": 0.01331488136202097, "Left": 0.09224744141101837, "Top": 0.47421929240226746}, "Polygon": [{"X": 0.09224744141101837, "Y": 0.4742928743362427}, {"X": 0.2116367220878601, "Y": 0.47421929240226746}, {"X": 0.2116461992263794, "Y": 0.48746082186698914}, {"X": 0.09225562959909439, "Y": 0.48753419518470764}]}, "Id": "259138f8-006e-4ff7-8cbc-dd7eef06605d", "Relationships": [{"Type": "CHILD", "Ids": ["e4447cf2-2697-4262-b6f2-82997a8a758a"]}]}, {"BlockType": "LINE", "Confidence": 97.00155639648438, "Text": "1300", "Geometry": {"BoundingBox": {"Width": 0.08181269466876984, "Height": 0.02233395352959633, "Left": 0.8700106739997864, "Top": 0.4832892119884491}, "Polygon": [{"X": 0.8700106739997864, "Y": 0.48333948850631714}, {"X": 0.951793909072876, "Y": 0.4832892119884491}, {"X": 0.951823353767395, "Y": 0.5055731534957886}, {"X": 0.870038628578186, "Y": 0.5056231617927551}]}, "Id": "81c545c2-91a0-4cb8-810c-4bf826576686", "Relationships": [{"Type": "CHILD", "Ids": ["e10bc022-be97-4ade-a629-730591be4e8f"]}]}, {"BlockType": "LINE", "Confidence": 62.42087936401367, "Text": "Isk", "Geometry": {"BoundingBox": {"Width": 0.10366231948137283, "Height": 0.053209688514471054, "Left": 0.16321435570716858, "Top": 0.5355206727981567}, "Polygon": [{"X": 0.16321435570716858, "Y": 0.5355836749076843}, {"X": 0.2668362259864807, "Y": 0.5355206727981567}, {"X": 0.2668766677379608, "Y": 0.588668167591095}, {"X": 0.16325029730796814, "Y": 0.5887303948402405}]}, "Id": "1bcb3b75-047c-47bb-ada4-42f5e1afcd33", "Relationships": [{"Type": "CHILD", "Ids": ["27cd7fc0-187a-44e4-a1d8-cde369b08986"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1315", "Geometry": {"BoundingBox": {"Width": 0.0761420950293541, "Height": 0.021358123049139977, "Left": 0.8741515874862671, "Top": 0.5384814143180847}, "Polygon": [{"X": 0.8741515874862671, "Y": 0.5385276079177856}, {"X": 0.9502655863761902, "Y": 0.5384814143180847}, {"X": 0.9502937197685242, "Y": 0.5597935318946838}, {"X": 0.874178409576416, "Y": 0.5598395466804504}]}, "Id": "4a98fe22-19c9-4b7c-9123-c537dc7d67ea", "Relationships": [{"Type": "CHILD", "Ids": ["4fe6c96d-7608-4f15-9fb8-3b715f4b5211"]}]}, {"BlockType": "LINE", "Confidence": 64.61595153808594, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.05480049550533295, "Height": 0.02022732049226761, "Left": 0.5054538249969482, "Top": 0.5652329921722412}, "Polygon": [{"X": 0.5054538249969482, "Y": 0.5652660727500916}, {"X": 0.5602341294288635, "Y": 0.5652329921722412}, {"X": 0.5602543354034424, "Y": 0.5854274034500122}, {"X": 0.5054731369018555, "Y": 0.585460364818573}]}, "Id": "65c86fa9-8910-4bee-b9a2-525635faa320", "Relationships": [{"Type": "CHILD", "Ids": ["62cdd6de-dbc8-4f67-8967-cd1b6724e4c1"]}]}, {"BlockType": "LINE", "Confidence": 99.93572998046875, "Text": "Zed Engineering", "Geometry": {"BoundingBox": {"Width": 0.15843461453914642, "Height": 0.017106346786022186, "Left": 0.09143631160259247, "Top": 0.5870837569236755}, "Polygon": [{"X": 0.09143631160259247, "Y": 0.5871788859367371}, {"X": 0.24985821545124054, "Y": 0.5870837569236755}, {"X": 0.2498709261417389, "Y": 0.6040953397750854}, {"X": 0.09144681692123413, "Y": 0.6041901111602783}]}, "Id": "43daaf8e-6de9-49b0-a1d5-e3cc39c55146", "Relationships": [{"Type": "CHILD", "Ids": ["e977d7e0-a3c4-4e21-bf01-b08f1ea819b0", "1a897714-5dd3-4aa7-a85a-a763a8003a8e"]}]}, {"BlockType": "LINE", "Confidence": 92.3582992553711, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.014318541623651981, "Height": 0.013645639643073082, "Left": 0.36545634269714355, "Top": 0.5938563346862793}, "Polygon": [{"X": 0.36545634269714355, "Y": 0.593864917755127}, {"X": 0.37976324558258057, "Y": 0.5938563346862793}, {"X": 0.37977486848831177, "Y": 0.6074934005737305}, {"X": 0.3654678165912628, "Y": 0.6075019836425781}]}, "Id": "8ceba84b-8506-44de-9d29-7130f9f1c4b2", "Relationships": [{"Type": "CHILD", "Ids": ["f4b52ccc-7052-46e5-94f5-1b3053897208"]}]}, {"BlockType": "LINE", "Confidence": 95.47183990478516, "Text": "X **********", "Geometry": {"BoundingBox": {"Width": 0.14154808223247528, "Height": 0.013571676798164845, "Left": 0.4418497085571289, "Top": 0.5923745036125183}, "Polygon": [{"X": 0.4418497085571289, "Y": 0.5924594402313232}, {"X": 0.5833840370178223, "Y": 0.5923745036125183}, {"X": 0.5833978056907654, "Y": 0.6058616042137146}, {"X": 0.4418618977069855, "Y": 0.6059461832046509}]}, "Id": "6da45441-c645-4ad4-a301-b8f59d4deb4b", "Relationships": [{"Type": "CHILD", "Ids": ["fdadd850-9d33-4c32-ba20-6de1f34ec969", "7a9538d3-7637-4c9f-a21d-8929192886c5"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "(formerly Renishaw)", "Geometry": {"BoundingBox": {"Width": 0.19534368813037872, "Height": 0.020287584513425827, "Left": 0.0931345596909523, "Top": 0.606086790561676}, "Polygon": [{"X": 0.0931345596909523, "Y": 0.6062036156654358}, {"X": 0.2884625494480133, "Y": 0.606086790561676}, {"X": 0.2884782552719116, "Y": 0.626258134841919}, {"X": 0.09314704686403275, "Y": 0.6263744235038757}]}, "Id": "3700c98a-fedd-4659-900f-aed95ea8ce19", "Relationships": [{"Type": "CHILD", "Ids": ["a6219b11-4e75-49bb-a4c6-dfa67e27ecf7", "0f00b71d-7ee1-4e7c-b2d4-c58f9782bdf3"]}]}, {"BlockType": "LINE", "Confidence": 99.6314926147461, "Text": "1330", "Geometry": {"BoundingBox": {"Width": 0.07649876922369003, "Height": 0.025058606639504433, "Left": 0.8878089785575867, "Top": 0.6226698756217957}, "Polygon": [{"X": 0.8878089785575867, "Y": 0.622715413570404}, {"X": 0.9642744064331055, "Y": 0.6226698756217957}, {"X": 0.9643077254295349, "Y": 0.6476832032203674}, {"X": 0.8878406882286072, "Y": 0.6477285027503967}]}, "Id": "049bec64-6362-4885-bf40-035d73e9c14d", "Relationships": [{"Type": "CHILD", "Ids": ["8d51d768-4565-4d56-8a7d-ce33dc50e9d6"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1140", "Geometry": {"BoundingBox": {"Width": 0.07566630095243454, "Height": 0.020148571580648422, "Left": 0.8858582973480225, "Top": 0.681958794593811}, "Polygon": [{"X": 0.8858582973480225, "Y": 0.6820032000541687}, {"X": 0.9614979028701782, "Y": 0.681958794593811}, {"X": 0.9615246057510376, "Y": 0.7020631432533264}, {"X": 0.8858838081359863, "Y": 0.7021073698997498}]}, "Id": "2c5e29e9-7308-450c-97a4-1df447ff0c83", "Relationships": [{"Type": "CHILD", "Ids": ["97e2db30-0dab-4f50-9daf-9bc96b945489"]}]}, {"BlockType": "LINE", "Confidence": 97.86441802978516, "Text": "4 Bsk", "Geometry": {"BoundingBox": {"Width": 0.11494189500808716, "Height": 0.03051259182393551, "Left": 0.49482741951942444, "Top": 0.691970705986023}, "Polygon": [{"X": 0.49482741951942444, "Y": 0.6920379996299744}, {"X": 0.6097375750541687, "Y": 0.691970705986023}, {"X": 0.6097692847251892, "Y": 0.7224164009094238}, {"X": 0.4948562681674957, "Y": 0.7224832773208618}]}, "Id": "d170f99d-a775-4d92-9b90-e20d3e70af1f", "Relationships": [{"Type": "CHILD", "Ids": ["2e3a666c-b539-4e9c-ae76-f506a8e3774e", "9b36cb01-e034-48ea-a83e-f45e0c105601"]}]}, {"BlockType": "LINE", "Confidence": 89.32311248779297, "Text": "Bucher 3BSK Hydraulics", "Geometry": {"BoundingBox": {"Width": 0.18080604076385498, "Height": 0.04770384728908539, "Left": 0.09192696958780289, "Top": 0.7091639637947083}, "Polygon": [{"X": 0.09192696958780289, "Y": 0.7092694640159607}, {"X": 0.2726965546607971, "Y": 0.7091639637947083}, {"X": 0.27273300290107727, "Y": 0.7567635178565979}, {"X": 0.09195637702941895, "Y": 0.7568678259849548}]}, "Id": "ef06ef64-19a0-4387-b362-a48dc7b839c0", "Relationships": [{"Type": "CHILD", "Ids": ["cb089de9-40cc-4693-a1b9-019b0a027595", "c2ab89f9-2d69-4580-a2f9-5ce4ce50cd87", "1b52d1ed-0732-429d-9eff-84fa18626d80"]}]}, {"BlockType": "LINE", "Confidence": 89.76158905029297, "Text": "SO-2143111 ********** ********** SO=", "Geometry": {"BoundingBox": {"Width": 0.3502083122730255, "Height": 0.016775893047451973, "Left": 0.49170804023742676, "Top": 0.7192971706390381}, "Polygon": [{"X": 0.49170804023742676, "Y": 0.7195010185241699}, {"X": 0.8418959379196167, "Y": 0.7192971706390381}, {"X": 0.8419163227081299, "Y": 0.7358700037002563}, {"X": 0.4917236864566803, "Y": 0.7360730171203613}]}, "Id": "96bb088f-3ba1-49b8-8686-f8a18d0feeb6", "Relationships": [{"Type": "CHILD", "Ids": ["1fa45277-f39b-483c-a043-c85a77a65718", "2160fee1-7609-4091-ae4a-82be6fe6681b", "4eddaefc-91f6-44dd-ab1a-91b74cee3abd", "cc25cba0-03a5-4c66-adcb-17f66e19b9fe"]}]}, {"BlockType": "LINE", "Confidence": 96.41581726074219, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01211322657763958, "Height": 0.010712170973420143, "Left": 0.3677826225757599, "Top": 0.7383894920349121}, "Polygon": [{"X": 0.3677826225757599, "Y": 0.7383965253829956}, {"X": 0.3798867166042328, "Y": 0.7383894920349121}, {"X": 0.3798958659172058, "Y": 0.7490946650505066}, {"X": 0.36779165267944336, "Y": 0.7491016387939453}]}, "Id": "8903746a-f565-40b1-bb31-30524b7235f0", "Relationships": [{"Type": "CHILD", "Ids": ["92b527ff-c1bb-4e24-8d03-a8d2c7220a14"]}]}, {"BlockType": "LINE", "Confidence": 89.43878173828125, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01176295056939125, "Height": 0.011210707016289234, "Left": 0.44537925720214844, "Top": 0.7361752390861511}, "Polygon": [{"X": 0.44537925720214844, "Y": 0.7361820936203003}, {"X": 0.457131952047348, "Y": 0.7361752390861511}, {"X": 0.4571422040462494, "Y": 0.7473791837692261}, {"X": 0.44538941979408264, "Y": 0.7473859786987305}]}, "Id": "7cf58b4c-71a3-40e6-ba1d-83f81d3eb832", "Relationships": [{"Type": "CHILD", "Ids": ["99a62aa8-d84e-4fb6-92e1-ac294d3c9653"]}]}, {"BlockType": "LINE", "Confidence": 92.25824737548828, "Text": "1200", "Geometry": {"BoundingBox": {"Width": 0.0895143523812294, "Height": 0.020893357694149017, "Left": 0.8964565396308899, "Top": 0.725365161895752}, "Polygon": [{"X": 0.8964565396308899, "Y": 0.7254171967506409}, {"X": 0.9859427809715271, "Y": 0.725365161895752}, {"X": 0.9859709143638611, "Y": 0.7462067604064941}, {"X": 0.8964831233024597, "Y": 0.746258556842804}]}, "Id": "59971cdc-1b6f-47bd-92b1-77e28b852279", "Relationships": [{"Type": "CHILD", "Ids": ["ad5702dc-26ed-4fda-aaf2-d26d16ecedb3"]}]}, {"BlockType": "LINE", "Confidence": 99.10076141357422, "Text": "2143114", "Geometry": {"BoundingBox": {"Width": 0.06698443740606308, "Height": 0.012590054422616959, "Left": 0.49198946356773376, "Top": 0.7416430115699768}, "Polygon": [{"X": 0.49198946356773376, "Y": 0.7416818141937256}, {"X": 0.5589613318443298, "Y": 0.7416430115699768}, {"X": 0.5589739084243774, "Y": 0.754194438457489}, {"X": 0.49200132489204407, "Y": 0.7542330622673035}]}, "Id": "c2f37329-8848-4691-bff0-80eb44bce911", "Relationships": [{"Type": "CHILD", "Ids": ["4d7d4537-7349-40ee-90ca-6d8b6bedd5ba"]}]}, {"BlockType": "LINE", "Confidence": 97.70348358154297, "Text": "50-2142101", "Geometry": {"BoundingBox": {"Width": 0.17951054871082306, "Height": 0.022924115881323814, "Left": 0.5686962008476257, "Top": 0.7474031448364258}, "Polygon": [{"X": 0.5686962008476257, "Y": 0.7475069761276245}, {"X": 0.7481803894042969, "Y": 0.7474031448364258}, {"X": 0.7482067346572876, "Y": 0.7702240347862244}, {"X": 0.568719208240509, "Y": 0.7703272700309753}]}, "Id": "23f5173c-28b6-466f-8d60-c1d04c8cff1d", "Relationships": [{"Type": "CHILD", "Ids": ["459785f4-ba86-4d3b-b7b0-a9b673019ed6"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "GRAND RAPIDS", "Geometry": {"BoundingBox": {"Width": 0.15110214054584503, "Height": 0.016991661861538887, "Left": 0.460280179977417, "Top": 0.981703519821167}, "Polygon": [{"X": 0.460280179977417, "Y": 0.9817859530448914}, {"X": 0.6113647222518921, "Y": 0.981703519821167}, {"X": 0.6113823056221008, "Y": 0.9986130595207214}, {"X": 0.46029573678970337, "Y": 0.9986951351165771}]}, "Id": "48d05ac0-7710-44f2-9e0c-6503b9a6c682", "Relationships": [{"Type": "CHILD", "Ids": ["440b2bf1-5a57-455d-9d23-be2ebb2d3654", "0a63101b-9171-4c51-bf3c-c66568d582cc"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058128464967012405, "Height": 0.012404133565723896, "Left": 0.24907651543617249, "Top": 0.10196449607610703}, "Polygon": [{"X": 0.24907651543617249, "Y": 0.102003313601017}, {"X": 0.30719515681266785, "Y": 0.10196449607610703}, {"X": 0.3072049617767334, "Y": 0.11432991921901703}, {"X": 0.24908573925495148, "Y": 0.11436863243579865}]}, "Id": "88aa663b-aee6-4456-8800-cc91b6186e66"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "2-Sep", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05300193279981613, "Height": 0.015237096697092056, "Left": 0.33041971921920776, "Top": 0.10213357955217361}, "Polygon": [{"X": 0.33041971921920776, "Y": 0.1021689623594284}, {"X": 0.3834086060523987, "Y": 0.10213357955217361}, {"X": 0.3834216296672821, "Y": 0.11733540147542953}, {"X": 0.33043205738067627, "Y": 0.11737067252397537}]}, "Id": "b6f6df53-58a6-4a83-be0e-410738a8d785"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051272813230752945, "Height": 0.012293647043406963, "Left": 0.23158347606658936, "Top": 0.13832055032253265}, "Polygon": [{"X": 0.23158347606658936, "Y": 0.13835452497005463}, {"X": 0.2828468084335327, "Y": 0.13832055032253265}, {"X": 0.282856285572052, "Y": 0.1505803018808365}, {"X": 0.23159246146678925, "Y": 0.1506141871213913}]}, "Id": "1474a493-daef-490a-9ca4-537154d6e266"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021893227472901344, "Height": 0.012132336385548115, "Left": 0.28561145067214966, "Top": 0.1384291797876358}, "Polygon": [{"X": 0.28561145067214966, "Y": 0.13844367861747742}, {"X": 0.3074950575828552, "Y": 0.1384291797876358}, {"X": 0.30750468373298645, "Y": 0.15054704248905182}, {"X": 0.28562086820602417, "Y": 0.15056151151657104}]}, "Id": "b5119468-fc17-440e-afd6-6d60c504648f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "6063/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12212570011615753, "Height": 0.014705550856888294, "Left": 0.33044055104255676, "Top": 0.13764864206314087}, "Polygon": [{"X": 0.33044055104255676, "Y": 0.13772960007190704}, {"X": 0.4525528848171234, "Y": 0.13764864206314087}, {"X": 0.4525662362575531, "Y": 0.152273491024971}, {"X": 0.33045244216918945, "Y": 0.1523541957139969}]}, "Id": "8181a0ea-beba-496d-a530-43b80c4ecbe2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050398245453834534, "Height": 0.01245021354407072, "Left": 0.48266467452049255, "Top": 0.15572160482406616}, "Polygon": [{"X": 0.48266467452049255, "Y": 0.15575487911701202}, {"X": 0.5330507755279541, "Y": 0.15572160482406616}, {"X": 0.5330629348754883, "Y": 0.16813862323760986}, {"X": 0.48267632722854614, "Y": 0.16817182302474976}]}, "Id": "69f4e3c0-e9d8-40e1-a057-d9b158210398"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058782294392585754, "Height": 0.015346383675932884, "Left": 0.5372443795204163, "Top": 0.15565358102321625}, "Polygon": [{"X": 0.5372443795204163, "Y": 0.15569239854812622}, {"X": 0.5960108637809753, "Y": 0.15565358102321625}, {"X": 0.5960266590118408, "Y": 0.17096129059791565}, {"X": 0.5372593998908997, "Y": 0.17099997401237488}]}, "Id": "a5848684-9e6d-4195-9d8b-839a98c9ee21"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050173934549093246, "Height": 0.011968777514994144, "Left": 0.6016469597816467, "Top": 0.15613901615142822}, "Polygon": [{"X": 0.6016469597816467, "Y": 0.15617212653160095}, {"X": 0.6518080234527588, "Y": 0.15613901615142822}, {"X": 0.6518208980560303, "Y": 0.16807475686073303}, {"X": 0.6016592979431152, "Y": 0.1681077927350998}]}, "Id": "6dc3d56b-838f-4e29-9986-50f33a788797"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08015662431716919, "Height": 0.012394125573337078, "Left": 0.22103999555110931, "Top": 0.17468033730983734}, "Polygon": [{"X": 0.22103999555110931, "Y": 0.17473305761814117}, {"X": 0.30118685960769653, "Y": 0.17468033730983734}, {"X": 0.3011966049671173, "Y": 0.1870218813419342}, {"X": 0.22104892134666443, "Y": 0.1870744675397873}]}, "Id": "b9d66545-49e5-4e3f-aceb-be1b13c45330"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06346306204795837, "Height": 0.012359020300209522, "Left": 0.33124956488609314, "Top": 0.17417529225349426}, "Polygon": [{"X": 0.33124956488609314, "Y": 0.17421704530715942}, {"X": 0.3947019577026367, "Y": 0.17417529225349426}, {"X": 0.3947126269340515, "Y": 0.1864926815032959}, {"X": 0.3312595784664154, "Y": 0.1865343153476715}]}, "Id": "4880a3f5-eb88-47b2-a5a4-b75f01c80f06"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05074506625533104, "Height": 0.0126723637804389, "Left": 0.4835941791534424, "Top": 0.19157077372074127}, "Polygon": [{"X": 0.4835941791534424, "Y": 0.19160403311252594}, {"X": 0.5343268513679504, "Y": 0.19157077372074127}, {"X": 0.5343392491340637, "Y": 0.20420998334884644}, {"X": 0.4836060404777527, "Y": 0.20424313843250275}]}, "Id": "cf567379-4aa4-4287-9c80-5f5f3956261e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0987502932548523, "Height": 0.015064951963722706, "Left": 0.5385501980781555, "Top": 0.1917208731174469}, "Polygon": [{"X": 0.5385501980781555, "Y": 0.19178558886051178}, {"X": 0.637284517288208, "Y": 0.1917208731174469}, {"X": 0.6373004913330078, "Y": 0.20672132074832916}, {"X": 0.5385649800300598, "Y": 0.20678582787513733}]}, "Id": "b5a4b96a-4248-4638-a8d5-c1e445a1e65f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11919339746236801, "Height": 0.012825488112866879, "Left": 0.1237560510635376, "Top": 0.21159471571445465}, "Polygon": [{"X": 0.1237560510635376, "Y": 0.21167249977588654}, {"X": 0.2429399937391281, "Y": 0.21159471571445465}, {"X": 0.2429494559764862, "Y": 0.22434262931346893}, {"X": 0.12376426160335541, "Y": 0.2244202047586441}]}, "Id": "cb14b1cb-421a-4f67-8c7e-c97b472cc1c9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05343512445688248, "Height": 0.012469845823943615, "Left": 0.2481691688299179, "Top": 0.21138478815555573}, "Polygon": [{"X": 0.2481691688299179, "Y": 0.21141965687274933}, {"X": 0.30159446597099304, "Y": 0.21138478815555573}, {"X": 0.301604300737381, "Y": 0.22381985187530518}, {"X": 0.24817843735218048, "Y": 0.22385463118553162}]}, "Id": "17c355b8-2ede-40af-a4ce-1e96f7016de4"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03893620893359184, "Height": 0.012485845945775509, "Left": 0.3316425681114197, "Top": 0.2109547108411789}, "Polygon": [{"X": 0.3316425681114197, "Y": 0.2109801173210144}, {"X": 0.3705682158470154, "Y": 0.2109547108411789}, {"X": 0.3705787658691406, "Y": 0.22341522574424744}, {"X": 0.3316527009010315, "Y": 0.22344055771827698}]}, "Id": "cdce87ab-9bc9-4dcc-bcf2-66613c44ee07"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051167819648981094, "Height": 0.012044334784150124, "Left": 0.34116190671920776, "Top": 0.24282944202423096}, "Polygon": [{"X": 0.34116190671920776, "Y": 0.24286261200904846}, {"X": 0.39231935143470764, "Y": 0.24282944202423096}, {"X": 0.39232972264289856, "Y": 0.2548407018184662}, {"X": 0.3411717712879181, "Y": 0.25487378239631653}]}, "Id": "afcdb056-ce23-4c76-8e81-ead3065e7dcd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04733435809612274, "Height": 0.011899909004569054, "Left": 0.8630751967430115, "Top": 0.25322225689888}, "Polygon": [{"X": 0.8630751967430115, "Y": 0.25325286388397217}, {"X": 0.9103942513465881, "Y": 0.25322225689888}, {"X": 0.9104095697402954, "Y": 0.2650916278362274}, {"X": 0.8630900382995605, "Y": 0.2651221752166748}]}, "Id": "a78c3171-8340-4c8f-a76a-0f4d96477758"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04834018647670746, "Height": 0.011118972674012184, "Left": 0.914879322052002, "Top": 0.2528887987136841}, "Polygon": [{"X": 0.914879322052002, "Y": 0.2529200613498688}, {"X": 0.9632047414779663, "Y": 0.2528887987136841}, {"X": 0.9632195234298706, "Y": 0.2639766037464142}, {"X": 0.9148936867713928, "Y": 0.2640077769756317}]}, "Id": "f6db1ce9-a2a3-4c6d-ad1d-e39dc7cb008e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10767072439193726, "Height": 0.01283766608685255, "Left": 0.15752527117729187, "Top": 0.26422953605651855}, "Polygon": [{"X": 0.15752527117729187, "Y": 0.2642990052700043}, {"X": 0.2651863098144531, "Y": 0.26422953605651855}, {"X": 0.2651959955692291, "Y": 0.2769978940486908}, {"X": 0.15753385424613953, "Y": 0.2770671844482422}]}, "Id": "be665f3e-d991-430a-aa8d-cc6f9b484786"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025493381544947624, "Height": 0.0126876225695014, "Left": 0.3544650375843048, "Top": 0.26230761408805847}, "Polygon": [{"X": 0.3544650375843048, "Y": 0.2623240649700165}, {"X": 0.37994760274887085, "Y": 0.26230761408805847}, {"X": 0.3799584209918976, "Y": 0.2749788463115692}, {"X": 0.35447558760643005, "Y": 0.27499523758888245}]}, "Id": "36355f7e-aa15-4d85-b9c0-5c9937261412"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03618374466896057, "Height": 0.012243750505149364, "Left": 0.4246581196784973, "Top": 0.2618671655654907}, "Polygon": [{"X": 0.4246581196784973, "Y": 0.2618905007839203}, {"X": 0.4608306288719177, "Y": 0.2618671655654907}, {"X": 0.4608418643474579, "Y": 0.2740876078605652}, {"X": 0.4246689975261688, "Y": 0.27411091327667236}]}, "Id": "c07496b6-c172-414d-a512-c5a6567d9020"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06004638597369194, "Height": 0.012375444173812866, "Left": 0.5928009152412415, "Top": 0.261625736951828}, "Polygon": [{"X": 0.5928009152412415, "Y": 0.2616645097732544}, {"X": 0.6528340578079224, "Y": 0.261625736951828}, {"X": 0.6528473496437073, "Y": 0.27396252751350403}, {"X": 0.5928136110305786, "Y": 0.27400118112564087}]}, "Id": "0b87dbdb-39b1-4f6a-bb59-ddeb18d210ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06737028062343597, "Height": 0.012443634681403637, "Left": 0.6583317518234253, "Top": 0.2611883580684662}, "Polygon": [{"X": 0.6583317518234253, "Y": 0.26123183965682983}, {"X": 0.7256879210472107, "Y": 0.2611883580684662}, {"X": 0.7257020473480225, "Y": 0.2735885977745056}, {"X": 0.6583451628684998, "Y": 0.2736319899559021}]}, "Id": "55373c38-ac3d-4fea-bb10-f743e8c397b2"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013204371556639671, "Height": 0.012118943966925144, "Left": 0.7316330075263977, "Top": 0.26100343465805054}, "Polygon": [{"X": 0.7316330075263977, "Y": 0.2610119581222534}, {"X": 0.7448233962059021, "Y": 0.26100343465805054}, {"X": 0.7448373436927795, "Y": 0.2731139063835144}, {"X": 0.7316467761993408, "Y": 0.2731224000453949}]}, "Id": "83653f58-73dc-46e8-b8c5-cc8ce83c0191"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "1025", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07541435956954956, "Height": 0.01827477477490902, "Left": 0.8736101984977722, "Top": 0.28849515318870544}, "Polygon": [{"X": 0.8736101984977722, "Y": 0.28854355216026306}, {"X": 0.9490005373954773, "Y": 0.28849515318870544}, {"X": 0.9490245580673218, "Y": 0.3067217469215393}, {"X": 0.8736331462860107, "Y": 0.3067699372768402}]}, "Id": "7aa49d85-d62a-40e3-b7cf-a928e0ba57ea"}, {"BlockType": "WORD", "Confidence": 71.41163635253906, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.051616184413433075, "Height": 0.022746475413441658, "Left": 0.5122780203819275, "Top": 0.3041526973247528}, "Polygon": [{"X": 0.5122780203819275, "Y": 0.304185688495636}, {"X": 0.5638713836669922, "Y": 0.3041526973247528}, {"X": 0.5638942122459412, "Y": 0.3268663287162781}, {"X": 0.5122998356819153, "Y": 0.3268991708755493}]}, "Id": "a1faafda-65fa-4a27-9a56-5ba1c025c305"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "X-Rite", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05952225625514984, "Height": 0.013337465934455395, "Left": 0.0916421189904213, "Top": 0.3353727161884308}, "Polygon": [{"X": 0.0916421189904213, "Y": 0.33541053533554077}, {"X": 0.1511555165052414, "Y": 0.3353727161884308}, {"X": 0.15116438269615173, "Y": 0.348672479391098}, {"X": 0.0916503369808197, "Y": 0.34871017932891846}]}, "Id": "5e6701ef-a24c-4a4a-8985-8584a4fffe4e"}, {"BlockType": "WORD", "Confidence": 94.9792709350586, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01338166743516922, "Height": 0.013218274340033531, "Left": 0.4367141127586365, "Top": 0.33411556482315063}, "Polygon": [{"X": 0.4367141127586365, "Y": 0.33412405848503113}, {"X": 0.45008376240730286, "Y": 0.33411556482315063}, {"X": 0.4500957727432251, "Y": 0.3473253548145294}, {"X": 0.43672600388526917, "Y": 0.3473338484764099}]}, "Id": "7bc2c6df-98cf-4730-b6ca-7eec4a6f2569"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007744918577373028, "Height": 0.01034898217767477, "Left": 0.48381149768829346, "Top": 0.3349463939666748}, "Polygon": [{"X": 0.48381149768829346, "Y": 0.3349513113498688}, {"X": 0.4915466606616974, "Y": 0.3349463939666748}, {"X": 0.49155643582344055, "Y": 0.34529048204421997}, {"X": 0.48382121324539185, "Y": 0.34529539942741394}]}, "Id": "8596cfaf-9f29-4a7d-bb9d-d47b09ee0e64"}, {"BlockType": "WORD", "Confidence": 97.96875, "Text": "BH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020745867863297462, "Height": 0.010655850172042847, "Left": 0.49813157320022583, "Top": 0.33464792370796204}, "Polygon": [{"X": 0.49813157320022583, "Y": 0.3346610963344574}, {"X": 0.5188671350479126, "Y": 0.33464792370796204}, {"X": 0.5188774466514587, "Y": 0.3452906310558319}, {"X": 0.49814167618751526, "Y": 0.3453037738800049}]}, "Id": "13d12351-430d-4d60-adc8-8c5187fc81ce"}, {"BlockType": "WORD", "Confidence": 99.4345932006836, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09354967623949051, "Height": 0.01111269649118185, "Left": 0.5250163078308105, "Top": 0.33400195837020874}, "Polygon": [{"X": 0.5250163078308105, "Y": 0.3340614140033722}, {"X": 0.618554413318634, "Y": 0.33400195837020874}, {"X": 0.6185660362243652, "Y": 0.34505534172058105}, {"X": 0.5250270962715149, "Y": 0.34511467814445496}]}, "Id": "acb2d5b2-3c30-4d8a-b703-01e6c9299053"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1035", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07742656767368317, "Height": 0.021021690219640732, "Left": 0.8656136393547058, "Top": 0.3259267210960388}, "Polygon": [{"X": 0.8656136393547058, "Y": 0.32597601413726807}, {"X": 0.9430126547813416, "Y": 0.3259267210960388}, {"X": 0.9430401921272278, "Y": 0.3468993604183197}, {"X": 0.865639865398407, "Y": 0.34694841504096985}]}, "Id": "35679376-f597-46a0-8a37-ba3b241534df"}, {"BlockType": "WORD", "Confidence": 47.785396575927734, "Text": "1 SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05798918381333351, "Height": 0.021306835114955902, "Left": 0.4959995448589325, "Top": 0.43253424763679504}, "Polygon": [{"X": 0.4959995448589325, "Y": 0.43257030844688416}, {"X": 0.5539675354957581, "Y": 0.43253424763679504}, {"X": 0.5539887547492981, "Y": 0.45380520820617676}, {"X": 0.49601975083351135, "Y": 0.45384109020233154}]}, "Id": "938969ad-5a01-4445-a13b-f4ec56bf140f"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "1250", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09510881453752518, "Height": 0.024248141795396805, "Left": 0.8821449875831604, "Top": 0.42209717631340027}, "Polygon": [{"X": 0.8821449875831604, "Y": 0.422156423330307}, {"X": 0.9772213101387024, "Y": 0.42209717631340027}, {"X": 0.977253794670105, "Y": 0.4462863504886627}, {"X": 0.8821755647659302, "Y": 0.4463452994823456}]}, "Id": "78c30d0b-2fe5-4298-b193-8e60a49adeaf"}, {"BlockType": "WORD", "Confidence": 59.79212951660156, "Text": "MKE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04230092093348503, "Height": 0.012976177036762238, "Left": 0.0923917293548584, "Top": 0.4540971517562866}, "Polygon": [{"X": 0.0923917293548584, "Y": 0.454123318195343}, {"X": 0.13468420505523682, "Y": 0.4540971517562866}, {"X": 0.13469265401363373, "Y": 0.46704721450805664}, {"X": 0.09239973872900009, "Y": 0.46707332134246826}]}, "Id": "f67329d1-e302-45d9-ab77-3c7ec912aee4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08424998819828033, "Height": 0.013159574009478092, "Left": 0.1418035924434662, "Top": 0.4537929892539978}, "Polygon": [{"X": 0.1418035924434662, "Y": 0.45384514331817627}, {"X": 0.22604404389858246, "Y": 0.4537929892539978}, {"X": 0.22605358064174652, "Y": 0.4669005572795868}, {"X": 0.14181222021579742, "Y": 0.4669525623321533}]}, "Id": "0ed56963-469a-4650-8ea2-f74368925631"}, {"BlockType": "WORD", "Confidence": 90.14569091796875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014294770546257496, "Height": 0.013029608875513077, "Left": 0.3634490966796875, "Top": 0.46200641989707947}, "Polygon": [{"X": 0.3634490966796875, "Y": 0.4620152711868286}, {"X": 0.3777327537536621, "Y": 0.46200641989707947}, {"X": 0.37774384021759033, "Y": 0.4750272333621979}, {"X": 0.3634600341320038, "Y": 0.47503605484962463}]}, "Id": "f15ca76e-9e61-4602-be38-4d5f7dc6b19a"}, {"BlockType": "WORD", "Confidence": 97.65963745117188, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013684896752238274, "Height": 0.012953940778970718, "Left": 0.4394785761833191, "Top": 0.4616023898124695}, "Polygon": [{"X": 0.4394785761833191, "Y": 0.4616108536720276}, {"X": 0.4531516432762146, "Y": 0.4616023898124695}, {"X": 0.4531634747982025, "Y": 0.4745479226112366}, {"X": 0.43949025869369507, "Y": 0.4745563268661499}]}, "Id": "c1ba0e1f-8818-4f2d-94f8-6fa90a40587d"}, {"BlockType": "WORD", "Confidence": 99.23369598388672, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09331873804330826, "Height": 0.011114976368844509, "Left": 0.4858905076980591, "Top": 0.4614695608615875}, "Polygon": [{"X": 0.4858905076980591, "Y": 0.4615272283554077}, {"X": 0.5791980028152466, "Y": 0.4614695608615875}, {"X": 0.5792092680931091, "Y": 0.47252702713012695}, {"X": 0.4859009087085724, "Y": 0.4725845456123352}]}, "Id": "8008463f-20d1-4f09-84b2-31156c6ddc45"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MACHINING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11939875781536102, "Height": 0.01331488136202097, "Left": 0.09224744141101837, "Top": 0.47421929240226746}, "Polygon": [{"X": 0.09224744141101837, "Y": 0.4742928743362427}, {"X": 0.2116367220878601, "Y": 0.47421929240226746}, {"X": 0.2116461992263794, "Y": 0.48746082186698914}, {"X": 0.09225562959909439, "Y": 0.48753419518470764}]}, "Id": "e4447cf2-2697-4262-b6f2-82997a8a758a"}, {"BlockType": "WORD", "Confidence": 97.00155639648438, "Text": "1300", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08181269466876984, "Height": 0.02233395352959633, "Left": 0.8700106739997864, "Top": 0.4832892119884491}, "Polygon": [{"X": 0.8700106739997864, "Y": 0.48333948850631714}, {"X": 0.951793909072876, "Y": 0.4832892119884491}, {"X": 0.951823353767395, "Y": 0.5055731534957886}, {"X": 0.870038628578186, "Y": 0.5056231617927551}]}, "Id": "e10bc022-be97-4ade-a629-730591be4e8f"}, {"BlockType": "WORD", "Confidence": 62.42087936401367, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10366231948137283, "Height": 0.053209688514471054, "Left": 0.16321435570716858, "Top": 0.5355206727981567}, "Polygon": [{"X": 0.16321435570716858, "Y": 0.5355836749076843}, {"X": 0.2668362259864807, "Y": 0.5355206727981567}, {"X": 0.2668766677379608, "Y": 0.588668167591095}, {"X": 0.16325029730796814, "Y": 0.5887303948402405}]}, "Id": "27cd7fc0-187a-44e4-a1d8-cde369b08986"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1315", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0761420950293541, "Height": 0.021358123049139977, "Left": 0.8741515874862671, "Top": 0.5384814143180847}, "Polygon": [{"X": 0.8741515874862671, "Y": 0.5385276079177856}, {"X": 0.9502655863761902, "Y": 0.5384814143180847}, {"X": 0.9502937197685242, "Y": 0.5597935318946838}, {"X": 0.874178409576416, "Y": 0.5598395466804504}]}, "Id": "4fe6c96d-7608-4f15-9fb8-3b715f4b5211"}, {"BlockType": "WORD", "Confidence": 64.61595153808594, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05480049550533295, "Height": 0.02022732049226761, "Left": 0.5054538249969482, "Top": 0.5652329921722412}, "Polygon": [{"X": 0.5054538249969482, "Y": 0.5652660727500916}, {"X": 0.5602341294288635, "Y": 0.5652329921722412}, {"X": 0.5602543354034424, "Y": 0.5854274034500122}, {"X": 0.5054731369018555, "Y": 0.585460364818573}]}, "Id": "62cdd6de-dbc8-4f67-8967-cd1b6724e4c1"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03627008944749832, "Height": 0.01377758290618658, "Left": 0.09143692255020142, "Top": 0.5881506204605103}, "Polygon": [{"X": 0.09143692255020142, "Y": 0.5881723761558533}, {"X": 0.1276981085538864, "Y": 0.5881506204605103}, {"X": 0.12770701944828033, "Y": 0.6019065380096436}, {"X": 0.09144541621208191, "Y": 0.6019282341003418}]}, "Id": "e977d7e0-a3c4-4e21-bf01-b08f1ea819b0"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Engineering", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11407791823148727, "Height": 0.01707981340587139, "Left": 0.13579301536083221, "Top": 0.5870837569236755}, "Polygon": [{"X": 0.13579301536083221, "Y": 0.5871522426605225}, {"X": 0.24985821545124054, "Y": 0.5870837569236755}, {"X": 0.2498709261417389, "Y": 0.6040953397750854}, {"X": 0.13580413162708282, "Y": 0.6041635870933533}]}, "Id": "1a897714-5dd3-4aa7-a85a-a763a8003a8e"}, {"BlockType": "WORD", "Confidence": 92.3582992553711, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014318541623651981, "Height": 0.013645639643073082, "Left": 0.36545634269714355, "Top": 0.5938563346862793}, "Polygon": [{"X": 0.36545634269714355, "Y": 0.593864917755127}, {"X": 0.37976324558258057, "Y": 0.5938563346862793}, {"X": 0.37977486848831177, "Y": 0.6074934005737305}, {"X": 0.3654678165912628, "Y": 0.6075019836425781}]}, "Id": "f4b52ccc-7052-46e5-94f5-1b3053897208"}, {"BlockType": "WORD", "Confidence": 91.42119598388672, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013713042251765728, "Height": 0.013344217091798782, "Left": 0.44184985756874084, "Top": 0.592602014541626}, "Polygon": [{"X": 0.44184985756874084, "Y": 0.5926101803779602}, {"X": 0.4555506706237793, "Y": 0.592602014541626}, {"X": 0.45556288957595825, "Y": 0.6059380173683167}, {"X": 0.4418618977069855, "Y": 0.6059461832046509}]}, "Id": "fdadd850-9d33-4c32-ba20-6de1f34ec969"}, {"BlockType": "WORD", "Confidence": 99.5224838256836, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09485774487257004, "Height": 0.01150088757276535, "Left": 0.4885379672050476, "Top": 0.5923745036125183}, "Polygon": [{"X": 0.4885379672050476, "Y": 0.5924314260482788}, {"X": 0.5833840370178223, "Y": 0.5923745036125183}, {"X": 0.5833957195281982, "Y": 0.6038186550140381}, {"X": 0.48854875564575195, "Y": 0.6038753986358643}]}, "Id": "7a9538d3-7637-4c9f-a21d-8929192886c5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "(formerly", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08938785642385483, "Height": 0.017818110063672066, "Left": 0.09313604980707169, "Top": 0.6085562705993652}, "Polygon": [{"X": 0.09313604980707169, "Y": 0.608609676361084}, {"X": 0.1825116127729416, "Y": 0.6085562705993652}, {"X": 0.1825239062309265, "Y": 0.6263211965560913}, {"X": 0.09314704686403275, "Y": 0.6263744235038757}]}, "Id": "a6219b11-4e75-49bb-a4c6-dfa67e27ecf7"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Renishaw)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09877799451351166, "Height": 0.016865188255906105, "Left": 0.18969763815402985, "Top": 0.606086790561676}, "Polygon": [{"X": 0.18969763815402985, "Y": 0.6061458587646484}, {"X": 0.2884625494480133, "Y": 0.606086790561676}, {"X": 0.2884756326675415, "Y": 0.622893214225769}, {"X": 0.1897093653678894, "Y": 0.6229519844055176}]}, "Id": "0f00b71d-7ee1-4e7c-b2d4-c58f9782bdf3"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "1330", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07649876922369003, "Height": 0.025058606639504433, "Left": 0.8878089785575867, "Top": 0.6226698756217957}, "Polygon": [{"X": 0.8878089785575867, "Y": 0.622715413570404}, {"X": 0.9642744064331055, "Y": 0.6226698756217957}, {"X": 0.9643077254295349, "Y": 0.6476832032203674}, {"X": 0.8878406882286072, "Y": 0.6477285027503967}]}, "Id": "8d51d768-4565-4d56-8a7d-ce33dc50e9d6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1140", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07566630095243454, "Height": 0.020148571580648422, "Left": 0.8858582973480225, "Top": 0.681958794593811}, "Polygon": [{"X": 0.8858582973480225, "Y": 0.6820032000541687}, {"X": 0.9614979028701782, "Y": 0.681958794593811}, {"X": 0.9615246057510376, "Y": 0.7020631432533264}, {"X": 0.8858838081359863, "Y": 0.7021073698997498}]}, "Id": "97e2db30-0dab-4f50-9daf-9bc96b945489"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "4", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.01851477287709713, "Height": 0.024726958945393562, "Left": 0.494832843542099, "Top": 0.697756290435791}, "Polygon": [{"X": 0.494832843542099, "Y": 0.6977671384811401}, {"X": 0.5133238434791565, "Y": 0.697756290435791}, {"X": 0.5133476257324219, "Y": 0.7224725484848022}, {"X": 0.4948562681674957, "Y": 0.7224832773208618}]}, "Id": "2e3a666c-b539-4e9c-ae76-f506a8e3774e"}, {"BlockType": "WORD", "Confidence": 96.38313293457031, "Text": "Bsk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07515483349561691, "Height": 0.026363402605056763, "Left": 0.5346101522445679, "Top": 0.691970705986023}, "Polygon": [{"X": 0.5346101522445679, "Y": 0.6920146942138672}, {"X": 0.6097375750541687, "Y": 0.691970705986023}, {"X": 0.6097649931907654, "Y": 0.7182903289794922}, {"X": 0.5346359610557556, "Y": 0.7183340787887573}]}, "Id": "9b36cb01-e034-48ea-a83e-f45e0c105601"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06800682842731476, "Height": 0.013563943095505238, "Left": 0.0919470563530922, "Top": 0.7417404651641846}, "Polygon": [{"X": 0.0919470563530922, "Y": 0.7417798042297363}, {"X": 0.15994477272033691, "Y": 0.7417404651641846}, {"X": 0.15995387732982635, "Y": 0.7552651166915894}, {"X": 0.09195540845394135, "Y": 0.7553043961524963}]}, "Id": "cb089de9-40cc-4693-a1b9-019b0a027595"}, {"BlockType": "WORD", "Confidence": 68.40621185302734, "Text": "3BSK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1396639347076416, "Height": 0.03767422214150429, "Left": 0.13306140899658203, "Top": 0.7091639637947083}, "Polygon": [{"X": 0.13306140899658203, "Y": 0.7092454433441162}, {"X": 0.2726965546607971, "Y": 0.7091639637947083}, {"X": 0.27272534370422363, "Y": 0.746757447719574}, {"X": 0.13308589160442352, "Y": 0.7468382120132446}]}, "Id": "c2ab89f9-2d69-4580-a2f9-5ce4ce50cd87"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Hydraulics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09953176975250244, "Height": 0.018175505101680756, "Left": 0.16749006509780884, "Top": 0.7386487126350403}, "Polygon": [{"X": 0.16749006509780884, "Y": 0.7387064099311829}, {"X": 0.2670080363750458, "Y": 0.7386487126350403}, {"X": 0.2670218348503113, "Y": 0.7567667961120605}, {"X": 0.16750237345695496, "Y": 0.756824254989624}]}, "Id": "1b52d1ed-0732-429d-9eff-84fa18626d80"}, {"BlockType": "WORD", "Confidence": 99.294677734375, "Text": "SO-2143111", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09486491978168488, "Height": 0.01254759170114994, "Left": 0.49171188473701477, "Top": 0.7235254645347595}, "Polygon": [{"X": 0.49171188473701477, "Y": 0.7235805988311768}, {"X": 0.5865640640258789, "Y": 0.7235254645347595}, {"X": 0.5865768194198608, "Y": 0.7360180616378784}, {"X": 0.4917236864566803, "Y": 0.7360730171203613}]}, "Id": "1fa45277-f39b-483c-a043-c85a77a65718"}, {"BlockType": "WORD", "Confidence": 83.07398223876953, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.099741630256176, "Height": 0.012578452005982399, "Left": 0.5981636047363281, "Top": 0.7217389941215515}, "Polygon": [{"X": 0.5981636047363281, "Y": 0.721796989440918}, {"X": 0.6978912949562073, "Y": 0.7217389941215515}, {"X": 0.6979052424430847, "Y": 0.7342596054077148}, {"X": 0.5981765389442444, "Y": 0.734317421913147}]}, "Id": "2160fee1-7609-4091-ae4a-82be6fe6681b"}, {"BlockType": "WORD", "Confidence": 99.15557861328125, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09574781358242035, "Height": 0.012426207773387432, "Left": 0.705679714679718, "Top": 0.720318615436554}, "Polygon": [{"X": 0.705679714679718, "Y": 0.720374345779419}, {"X": 0.8014127016067505, "Y": 0.720318615436554}, {"X": 0.8014275431632996, "Y": 0.7326892614364624}, {"X": 0.7056935429573059, "Y": 0.7327448129653931}]}, "Id": "4eddaefc-91f6-44dd-ab1a-91b74cee3abd"}, {"BlockType": "WORD", "Confidence": 77.52212524414062, "Text": "SO=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029408393427729607, "Height": 0.01174697745591402, "Left": 0.8125019669532776, "Top": 0.7192971706390381}, "Polygon": [{"X": 0.8125019669532776, "Y": 0.7193142771720886}, {"X": 0.8418959379196167, "Y": 0.7192971706390381}, {"X": 0.8419103622436523, "Y": 0.7310270667076111}, {"X": 0.8125161528587341, "Y": 0.7310441136360168}]}, "Id": "cc25cba0-03a5-4c66-adcb-17f66e19b9fe"}, {"BlockType": "WORD", "Confidence": 96.41581726074219, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01211322657763958, "Height": 0.010712170973420143, "Left": 0.3677826225757599, "Top": 0.7383894920349121}, "Polygon": [{"X": 0.3677826225757599, "Y": 0.7383965253829956}, {"X": 0.3798867166042328, "Y": 0.7383894920349121}, {"X": 0.3798958659172058, "Y": 0.7490946650505066}, {"X": 0.36779165267944336, "Y": 0.7491016387939453}]}, "Id": "92b527ff-c1bb-4e24-8d03-a8d2c7220a14"}, {"BlockType": "WORD", "Confidence": 89.43878173828125, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01176295056939125, "Height": 0.011210707016289234, "Left": 0.44537925720214844, "Top": 0.7361752390861511}, "Polygon": [{"X": 0.44537925720214844, "Y": 0.7361820936203003}, {"X": 0.457131952047348, "Y": 0.7361752390861511}, {"X": 0.4571422040462494, "Y": 0.7473791837692261}, {"X": 0.44538941979408264, "Y": 0.7473859786987305}]}, "Id": "99a62aa8-d84e-4fb6-92e1-ac294d3c9653"}, {"BlockType": "WORD", "Confidence": 92.25824737548828, "Text": "1200", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0895143523812294, "Height": 0.020893357694149017, "Left": 0.8964565396308899, "Top": 0.725365161895752}, "Polygon": [{"X": 0.8964565396308899, "Y": 0.7254171967506409}, {"X": 0.9859427809715271, "Y": 0.725365161895752}, {"X": 0.9859709143638611, "Y": 0.7462067604064941}, {"X": 0.8964831233024597, "Y": 0.746258556842804}]}, "Id": "ad5702dc-26ed-4fda-aaf2-d26d16ecedb3"}, {"BlockType": "WORD", "Confidence": 99.10076141357422, "Text": "2143114", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06698443740606308, "Height": 0.012590054422616959, "Left": 0.49198946356773376, "Top": 0.7416430115699768}, "Polygon": [{"X": 0.49198946356773376, "Y": 0.7416818141937256}, {"X": 0.5589613318443298, "Y": 0.7416430115699768}, {"X": 0.5589739084243774, "Y": 0.754194438457489}, {"X": 0.49200132489204407, "Y": 0.7542330622673035}]}, "Id": "4d7d4537-7349-40ee-90ca-6d8b6bedd5ba"}, {"BlockType": "WORD", "Confidence": 97.70348358154297, "Text": "50-2142101", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17951054871082306, "Height": 0.022924115881323814, "Left": 0.5686962008476257, "Top": 0.7474031448364258}, "Polygon": [{"X": 0.5686962008476257, "Y": 0.7475069761276245}, {"X": 0.7481803894042969, "Y": 0.7474031448364258}, {"X": 0.7482067346572876, "Y": 0.7702240347862244}, {"X": 0.568719208240509, "Y": 0.7703272700309753}]}, "Id": "459785f4-ba86-4d3b-b7b0-a9b673019ed6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GRAND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07095491886138916, "Height": 0.014665629714727402, "Left": 0.4602822959423065, "Top": 0.9840295314788818}, "Polygon": [{"X": 0.4602822959423065, "Y": 0.9840682148933411}, {"X": 0.531222939491272, "Y": 0.9840295314788818}, {"X": 0.5312371850013733, "Y": 0.9986566305160522}, {"X": 0.46029573678970337, "Y": 0.9986951351165771}]}, "Id": "440b2bf1-5a57-455d-9d23-be2ebb2d3654"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RAPIDS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07331916689872742, "Height": 0.015713829547166824, "Left": 0.5380618572235107, "Top": 0.981703519821167}, "Polygon": [{"X": 0.5380618572235107, "Y": 0.9817435145378113}, {"X": 0.6113647222518921, "Y": 0.981703519821167}, {"X": 0.6113810539245605, "Y": 0.9973775148391724}, {"X": 0.5380772948265076, "Y": 0.9974173307418823}]}, "Id": "0a63101b-9171-4c51-bf3c-c66568d582cc"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "0ac1f0c1-8bea-4cb0-abc9-7235c7946e3e", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "0ac1f0c1-8bea-4cb0-abc9-7235c7946e3e", "content-type": "application/x-amz-json-1.1", "content-length": "58407", "date": "Wed, 24 Sep 2025 13:40:02 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "21804056-bf91-4183-8276-9f41b4b07686", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:40:04 GMT", "content-type": "application/json", "content-length": "640", "connection": "keep-alive", "x-amzn-requestid": "21804056-bf91-4183-8276-9f41b4b07686"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The content looks like a log of dock times, customer, work orders. It's likely a log file. The keywords: \"Dock Time\", \"CUSTOMER\", \"WORK ORDER #\". This seems like a driver log or activity log. So doc_type = \"log\". Check if any other type? It's not invoice, BOL, POD, etc. So log. Output for page 1."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"log\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2784, "outputTokens": 103, "totalTokens": 2887}, "metrics": {"latencyMs": 657}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/e188e460_8_log_8.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nDATE: 2-Sep\nUNIT #: 6063/283420\nTruck Ready Time:\nDRIVER: KARRA\nTruck Departure:\nDEPARTURE TIME: 5am\nPICK-\nDock Time\nCUSTOMER\nUP\nDEL\nWORK ORDER #\n1025\nISK\nX-Rite\nX 1 BH **********\n1035\n1 SK\n1250\nMKE CUSTOM\nX\nX **********\nMACHINING\n1300\nIsk\n1315\n1sk\nZed Engineering\nX\nX **********\n(formerly Renishaw)\n1330\n1140\n4 Bsk\nBucher 3BSK Hydraulics\nSO-2143111 ********** ********** SO=\nX\nX\n1200\n2143114\n50-2142101\nGRAND RAPIDS\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}