{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 1.8547615354691516e-07}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "685e4bbf-8df1-4c94-9999-c327dc945450", "Relationships": [{"Type": "CHILD", "Ids": ["d17182b2-2799-40b9-9dfa-172520abb1f5", "86b6ead0-5c95-4aa1-a2ca-4a3ce84ab704", "9d0c4f58-2e2e-42f4-aef4-cc3c95a703c5", "e4d477a2-401d-4a88-8f4d-1348f48ad75a", "1cc5817b-6d0e-4baf-929b-05a92ae38127", "a55d51f7-c456-4167-80ce-e151ca9afe57", "a560a059-7393-43bc-bcd8-81fdfd01f820", "9134c54c-7a05-4dd9-968a-5c9d8fc785d1", "e07e77f0-abb9-4e90-ab35-df62c80768c0", "a5ca7d0a-59dd-4fd9-89f2-26419b700a27", "d253fb07-c44d-4dc5-a93c-df59676b68d2", "a7d03a58-3b7a-4a23-a363-b527c597aef4", "a2dc7aec-af28-4aed-91c1-838a656e1195", "f811fa29-11dc-4a56-94d2-d95133d5ad8e", "79104abc-66b3-49ab-aa35-d1f732238e33", "ea95af19-d4e9-4865-ac0b-db74c4240412", "4ce65059-ea07-42bf-b22a-530b2529b7b4", "1cd1d0d1-cbfa-4421-9525-f5c1f21e3a50", "b52417b9-7db8-4522-9e6b-c4d8a86f6118", "be8a427b-3b74-43f0-ae69-5d0d79e2e9ad", "d4c82856-46e7-4869-8215-6f59d644dd1c", "c4de6545-ab80-4279-a124-267ef8de3811"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "WEIGHED ON A FAIRBANKS SCALE", "Geometry": {"BoundingBox": {"Width": 0.***************, "Height": 0.028429651632905006, "Left": 0.*****************, "Top": 0.062025584280490875}, "Polygon": [{"X": 0.*****************, "Y": 0.062025584280490875}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "d17182b2-2799-40b9-9dfa-172520abb1f5", "Relationships": [{"Type": "CHILD", "Ids": ["6a186da9-e38b-4b0e-b7f5-8413ce5d7230", "ecffdce3-e2bb-4f7f-b433-3d62c171a733", "db6da9b8-cdfd-4b6d-afd4-3d26fff0afb2", "ce8136a1-5a8b-4c99-9042-e09da948cd24", "c1552230-ba94-4d22-b4af-577e1ace88bb"]}]}, {"BlockType": "LINE", "Confidence": 99.*************, "Text": "CUSTOMER'S NAME", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.****************, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.**************32, "Y": 0.21618668735027313}, {"X": 0.40114960074424744, "Y": 0.23886145651340485}, {"X": 0.*****************, "Y": 0.23879826068878174}]}, "Id": "86b6ead0-5c95-4aa1-a2ca-4a3ce84ab704", "Relationships": [{"Type": "CHILD", "Ids": ["1ca5da2a-5784-4b95-8924-c828c05c6920", "b0d68dca-a0f4-40ab-a992-d35b4c0187b9"]}]}, {"BlockType": "LINE", "Confidence": 87.4046401977539, "Text": "Rec. All", "Geometry": {"BoundingBox": {"Width": 0.2815878689289093, "Height": 0.0693616047501564, "Left": 0.6515306830406189, "Top": 0.185936838388443}, "Polygon": [{"X": 0.6515441536903381, "Y": 0.185936838388443}, {"X": 0.9331185221672058, "Y": 0.1859920173883438}, {"X": 0.9331051707267761, "Y": 0.2552984356880188}, {"X": 0.6515306830406189, "Y": 0.2552427649497986}]}, "Id": "9d0c4f58-2e2e-42f4-aef4-cc3c95a703c5", "Relationships": [{"Type": "CHILD", "Ids": ["4c940975-3f56-4871-abdf-e772037e29cc", "b8cfd741-6a43-4a60-adbb-785af2760aff"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "ADDRESS", "Geometry": {"BoundingBox": {"Width": 0.1607072800397873, "Height": 0.019836321473121643, "Left": 0.07997357845306396, "Top": 0.25052410364151}, "Polygon": [{"X": 0.07997749000787735, "Y": 0.25052410364151}, {"X": 0.24068084359169006, "Y": 0.2505558729171753}, {"X": 0.24067695438861847, "Y": 0.27036044001579285}, {"X": 0.07997357845306396, "Y": 0.2703285813331604}]}, "Id": "e4d477a2-401d-4a88-8f4d-1348f48ad75a", "Relationships": [{"Type": "CHILD", "Ids": ["814af5cb-75ae-4893-a593-e83d61018b8b"]}]}, {"BlockType": "LINE", "Confidence": 98.95328521728516, "Text": "CARRIER COMMODITY GRS #413", "Geometry": {"BoundingBox": {"Width": 0.7240763902664185, "Height": 0.07702089846134186, "Left": 0.0806892141699791, "Top": 0.2849733829498291}, "Polygon": [{"X": 0.08070440590381622, "Y": 0.2849733829498291}, {"X": 0.8047655820846558, "Y": 0.28511711955070496}, {"X": 0.8047507405281067, "Y": 0.36199426651000977}, {"X": 0.0806892141699791, "Y": 0.3618490695953369}]}, "Id": "1cc5817b-6d0e-4baf-929b-05a92ae38127", "Relationships": [{"Type": "CHILD", "Ids": ["61bfe8f7-091a-412f-bbd7-7c2cb024dcf1", "7a4729ed-8813-4ca4-8663-672a54600021", "4a3027f7-c4db-4ae6-b6fa-fb7f20686a15", "a59d256b-15f1-4d7d-8431-bb71192a56c5"]}]}, {"BlockType": "LINE", "Confidence": 92.22509765625, "Text": "INBOUND 59160 1b", "Geometry": {"BoundingBox": {"Width": 0.3133399784564972, "Height": 0.025307510048151016, "Left": 0.3759710192680359, "Top": 0.35540226101875305}, "Polygon": [{"X": 0.37597596645355225, "Y": 0.35540226101875305}, {"X": 0.6893110275268555, "Y": 0.35546502470970154}, {"X": 0.6893060803413391, "Y": 0.38070976734161377}, {"X": 0.3759710192680359, "Y": 0.3806467652320862}]}, "Id": "a55d51f7-c456-4167-80ce-e151ca9afe57", "Relationships": [{"Type": "CHILD", "Ids": ["4bac4097-3e52-4d2f-9fbd-af1ddd724903", "a501ebb3-85ee-4dff-955e-5cc63b87cc26", "aab083b8-215e-459d-9ffb-d014aaf4e4b6"]}]}, {"BlockType": "LINE", "Confidence": 98.708740234375, "Text": "LOOP ID 440", "Geometry": {"BoundingBox": {"Width": 0.19155023992061615, "Height": 0.022052224725484848, "Left": 0.3729349970817566, "Top": 0.37496498227119446}, "Polygon": [{"X": 0.3729392886161804, "Y": 0.37496498227119446}, {"X": 0.5644852519035339, "Y": 0.375003457069397}, {"X": 0.5644809603691101, "Y": 0.3970172107219696}, {"X": 0.3729349970817566, "Y": 0.39697861671447754}]}, "Id": "a560a059-7393-43bc-bcd8-81fdfd01f820", "Relationships": [{"Type": "CHILD", "Ids": ["9e08704e-bb19-4daa-9863-23d75a4bc466", "6447a052-b932-47fd-8ce6-110a544128e7", "9494fc34-6dff-48df-aa4e-992987bdc244"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "INBOUND DATE", "Geometry": {"BoundingBox": {"Width": 0.24397481977939606, "Height": 0.0205305814743042, "Left": 0.08365032821893692, "Top": 0.4282741844654083}, "Polygon": [{"X": 0.08365438133478165, "Y": 0.4282741844654083}, {"X": 0.3276251554489136, "Y": 0.42832353711128235}, {"X": 0.32762113213539124, "Y": 0.4488047659397125}, {"X": 0.08365032821893692, "Y": 0.44875529408454895}]}, "Id": "9134c54c-7a05-4dd9-968a-5c9d8fc785d1", "Relationships": [{"Type": "CHILD", "Ids": ["bcef12b6-7e1a-42d5-8165-88a16d8eeb52", "21966acc-aeca-474c-aa13-5b006bc21ef6"]}]}, {"BlockType": "LINE", "Confidence": 90.26934814453125, "Text": "8-29-25 TIME", "Geometry": {"BoundingBox": {"Width": 0.2359216809272766, "Height": 0.020930029451847076, "Left": 0.4423697888851166, "Top": 0.43449100852012634}, "Polygon": [{"X": 0.4423738718032837, "Y": 0.43449100852012634}, {"X": 0.6782914996147156, "Y": 0.4345387816429138}, {"X": 0.6782874464988708, "Y": 0.4554210603237152}, {"X": 0.4423697888851166, "Y": 0.4553731679916382}]}, "Id": "e07e77f0-abb9-4e90-ab35-df62c80768c0", "Relationships": [{"Type": "CHILD", "Ids": ["3774f020-cc4b-4881-b893-b4917bf55e56", "eb215b83-92e7-412e-82f2-0ae1ef09bb41"]}]}, {"BlockType": "LINE", "Confidence": 99.40110778808594, "Text": "8:23AM", "Geometry": {"BoundingBox": {"Width": 0.10577557981014252, "Height": 0.019830064848065376, "Left": 0.7739468216896057, "Top": 0.44121435284614563}, "Polygon": [{"X": 0.7739506959915161, "Y": 0.44121435284614563}, {"X": 0.8797224164009094, "Y": 0.4412357807159424}, {"X": 0.8797186017036438, "Y": 0.46104440093040466}, {"X": 0.7739468216896057, "Y": 0.4610229432582855}]}, "Id": "a5ca7d0a-59dd-4fd9-89f2-26419b700a27", "Relationships": [{"Type": "CHILD", "Ids": ["4f7a9dc8-8916-4011-a828-e3197d2564bc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "OUTBOUND DATE", "Geometry": {"BoundingBox": {"Width": 0.2817770540714264, "Height": 0.020221585407853127, "Left": 0.08224739879369736, "Top": 0.4651366174221039}, "Polygon": [{"X": 0.08225138485431671, "Y": 0.4651366174221039}, {"X": 0.36402446031570435, "Y": 0.46519389748573303}, {"X": 0.3640204966068268, "Y": 0.48535820841789246}, {"X": 0.08224739879369736, "Y": 0.48530077934265137}]}, "Id": "d253fb07-c44d-4dc5-a93c-df59676b68d2", "Relationships": [{"Type": "CHILD", "Ids": ["9c33e6a8-2993-431c-87ca-6068bc2939a2", "528d5925-85ff-4747-afb5-5c05ce07918e"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "8-29-25 TIME", "Geometry": {"BoundingBox": {"Width": 0.23479443788528442, "Height": 0.022608155384659767, "Left": 0.4435425102710724, "Top": 0.4722200334072113}, "Polygon": [{"X": 0.44354692101478577, "Y": 0.4722200334072113}, {"X": 0.6783369779586792, "Y": 0.4722677767276764}, {"X": 0.6783325672149658, "Y": 0.4948281943798065}, {"X": 0.4435425102710724, "Y": 0.4947802722454071}]}, "Id": "a7d03a58-3b7a-4a23-a363-b527c597aef4", "Relationships": [{"Type": "CHILD", "Ids": ["2d0d728a-5f47-4aec-bba2-915a4d4afccf", "a8c646a5-da15-49dc-87b1-c516c3d03936"]}]}, {"BlockType": "LINE", "Confidence": 99.73313903808594, "Text": "8:59AM", "Geometry": {"BoundingBox": {"Width": 0.10593078285455704, "Height": 0.020057471469044685, "Left": 0.7742763161659241, "Top": 0.48087477684020996}, "Polygon": [{"X": 0.7742801904678345, "Y": 0.48087477684020996}, {"X": 0.8802071213722229, "Y": 0.48089635372161865}, {"X": 0.8802032470703125, "Y": 0.5009322762489319}, {"X": 0.7742763161659241, "Y": 0.5009106397628784}]}, "Id": "a2dc7aec-af28-4aed-91c1-838a656e1195", "Relationships": [{"Type": "CHILD", "Ids": ["cc2d5339-acdd-433b-bb57-69936c8625d7"]}]}, {"BlockType": "LINE", "Confidence": 86.07674407958984, "Text": "59160 1b GROSS", "Geometry": {"BoundingBox": {"Width": 0.40441685914993286, "Height": 0.02703237533569336, "Left": 0.43012434244155884, "Top": 0.5304410457611084}, "Polygon": [{"X": 0.43012961745262146, "Y": 0.5304410457611084}, {"X": 0.8345412015914917, "Y": 0.530523955821991}, {"X": 0.8345360159873962, "Y": 0.5574734210968018}, {"X": 0.43012434244155884, "Y": 0.5573902726173401}]}, "Id": "f811fa29-11dc-4a56-94d2-d95133d5ad8e", "Relationships": [{"Type": "CHILD", "Ids": ["5ef67ea1-219f-4e31-a116-0e0c3524a2cb", "8267d7e7-74b4-46af-80f9-870efd231806", "9b3887fa-391a-4108-929e-ff32c11129df"]}]}, {"BlockType": "LINE", "Confidence": 98.11922454833984, "Text": "31240 1b TARE", "Geometry": {"BoundingBox": {"Width": 0.37412869930267334, "Height": 0.02523639425635338, "Left": 0.43207207322120667, "Top": 0.5507842302322388}, "Polygon": [{"X": 0.432077020406723, "Y": 0.5507842302322388}, {"X": 0.8062008023262024, "Y": 0.550861120223999}, {"X": 0.8061959147453308, "Y": 0.5760205984115601}, {"X": 0.43207207322120667, "Y": 0.5759435296058655}]}, "Id": "79104abc-66b3-49ab-aa35-d1f732238e33", "Relationships": [{"Type": "CHILD", "Ids": ["d4aab715-c0b7-41ee-9f90-bfcb4a6b9570", "a5e442e6-57b0-4fd8-b953-bdad2f764f0a", "06ed23e0-37f7-4be5-a975-0d5423fd282d"]}]}, {"BlockType": "LINE", "Confidence": 98.39312744140625, "Text": "27920 1b NET", "Geometry": {"BoundingBox": {"Width": 0.3462423086166382, "Height": 0.024482285603880882, "Left": 0.42999300360679626, "Top": 0.5697481632232666}, "Polygon": [{"X": 0.4299977719783783, "Y": 0.5697481632232666}, {"X": 0.7762353420257568, "Y": 0.5698195099830627}, {"X": 0.7762305736541748, "Y": 0.5942304730415344}, {"X": 0.42999300360679626, "Y": 0.594158947467804}]}, "Id": "ea95af19-d4e9-4865-ac0b-db74c4240412", "Relationships": [{"Type": "CHILD", "Ids": ["fb94a6c1-58fe-4181-83ab-d943f1c7cb14", "157da11d-5b5b-4a7c-b585-1fe1ac28c236", "23f751d4-f7fb-4ad4-b22a-36db8c1a564d"]}]}, {"BlockType": "LINE", "Confidence": 99.9473876953125, "Text": "LOOP ID 440", "Geometry": {"BoundingBox": {"Width": 0.1926974505186081, "Height": 0.022482523694634438, "Left": 0.3733019530773163, "Top": 0.6270836591720581}, "Polygon": [{"X": 0.3733063340187073, "Y": 0.6270836591720581}, {"X": 0.5659993886947632, "Y": 0.6271236538887024}, {"X": 0.5659950375556946, "Y": 0.6495661735534668}, {"X": 0.3733019530773163, "Y": 0.6495261192321777}]}, "Id": "4ce65059-ea07-42bf-b22a-530b2529b7b4", "Relationships": [{"Type": "CHILD", "Ids": ["00098ad7-67ad-44be-b399-ae84441d5134", "661932a2-9597-4c51-b274-1d1df23811ef", "d785dc3b-5050-4dbd-a672-915244cee6d0"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "DRIVER ON", "Geometry": {"BoundingBox": {"Width": 0.1842626929283142, "Height": 0.018623292446136475, "Left": 0.08308286219835281, "Top": 0.6703963875770569}, "Polygon": [{"X": 0.0830865353345871, "Y": 0.6703963875770569}, {"X": 0.2673455476760864, "Y": 0.670434832572937}, {"X": 0.2673419117927551, "Y": 0.6890196800231934}, {"X": 0.08308286219835281, "Y": 0.6889811754226685}]}, "Id": "1cd1d0d1-cbfa-4421-9525-f5c1f21e3a50", "Relationships": [{"Type": "CHILD", "Ids": ["52f4a683-7940-4d4c-8c07-c0321df617af", "d9ac31e3-b8c2-46c9-bcde-b51b720640a1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "OFF", "Geometry": {"BoundingBox": {"Width": 0.06643310934305191, "Height": 0.018057823181152344, "Left": 0.5410284996032715, "Top": 0.6748583316802979}, "Polygon": [{"X": 0.5410320162773132, "Y": 0.6748583316802979}, {"X": 0.6074615716934204, "Y": 0.6748722195625305}, {"X": 0.6074580550193787, "Y": 0.6929161548614502}, {"X": 0.5410284996032715, "Y": 0.6929022669792175}]}, "Id": "b52417b9-7db8-4522-9e6b-c4d8a86f6118", "Relationships": [{"Type": "CHILD", "Ids": ["dba16128-97c9-41a5-8e20-41a2304802ae"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "SHIPPER", "Geometry": {"BoundingBox": {"Width": 0.14644023776054382, "Height": 0.018395427614450455, "Left": 0.08271474391222, "Top": 0.824546217918396}, "Polygon": [{"X": 0.0827183723449707, "Y": 0.824546217918396}, {"X": 0.22915497422218323, "Y": 0.8245773911476135}, {"X": 0.22915136814117432, "Y": 0.8429416418075562}, {"X": 0.08271474391222, "Y": 0.8429104685783386}]}, "Id": "be8a427b-3b74-43f0-ae69-5d0d79e2e9ad", "Relationships": [{"Type": "CHILD", "Ids": ["fa57b36b-dc2e-4c1d-a78f-82bf8ba7de1f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "WEIGHER", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.018372323364019394, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "d4c82856-46e7-4869-8215-6f59d644dd1c", "Relationships": [{"Type": "CHILD", "Ids": ["b5aca64f-d542-493f-872b-5f2d64de63cd"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "FAIRBANKS SCALE CAT. 16288", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.****************, "Top": 0.***************}, "Polygon": [{"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "c4de6545-ab80-4279-a124-267ef8de3811", "Relationships": [{"Type": "CHILD", "Ids": ["200ed8e8-a17e-4513-9ae6-1ae333cab9c0", "298e4ad3-7419-461e-92b7-0327c7bcbac2", "4fc426e3-1ed1-4b30-a100-6d903aad21c0", "9b78df47-88c8-4f53-909c-4d92711aed0d"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.***************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.062025584280490875}, "Polygon": [{"X": 0.*****************, "Y": 0.062025584280490875}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.3236997425556183, "Y": 0.08724797517061234}, {"X": 0.*****************, "Y": 0.08720596134662628}]}, "Id": "6a186da9-e38b-4b0e-b7f5-8413ce5d7230"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0660189688205719, "Height": 0.0233315397053957, "Left": 0.3370174467563629, "Top": 0.06474221497774124}, "Polygon": [{"X": 0.3370220363140106, "Y": 0.06474221497774124}, {"X": 0.4030364155769348, "Y": 0.06475494056940079}, {"X": 0.4030318558216095, "Y": 0.08807375282049179}, {"X": 0.3370174467563629, "Y": 0.08806098997592926}]}, "Id": "ecffdce3-e2bb-4f7f-b433-3d62c171a733"}, {"BlockType": "WORD", "Confidence": 98.84565734863281, "Text": "A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "db6da9b8-cdfd-4b6d-afd4-3d26fff0afb2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FAIRBANKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.024859024211764336, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.***************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "ce8136a1-5a8b-4c99-9042-e09da948cd24"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.024554863572120667, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "c1552230-ba94-4d22-b4af-577e1ace88bb"}, {"BlockType": "WORD", "Confidence": 99.11331939697266, "Text": "CUSTOMER'S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21730318665504456, "Height": 0.02102109044790268, "Left": 0.08092354983091354, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.2982267439365387, "Y": 0.2161664366722107}, {"X": 0.2982226014137268, "Y": 0.23714476823806763}, {"X": 0.08092354983091354, "Y": 0.23710189759731293}]}, "Id": "1ca5da2a-5784-4b95-8924-c828c05c6920"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "NAME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09364941716194153, "Height": 0.01853085868060589, "Left": 0.3075037896633148, "Top": 0.2203305959701538}, "Polygon": [{"X": 0.3075074255466461, "Y": 0.2203305959701538}, {"X": 0.40115320682525635, "Y": 0.2203490287065506}, {"X": 0.40114960074424744, "Y": 0.23886145651340485}, {"X": 0.3075037896633148, "Y": 0.23884296417236328}]}, "Id": "b0d68dca-a0f4-40ab-a992-d35b4c0187b9"}, {"BlockType": "WORD", "Confidence": 76.35981750488281, "Text": "Rec.", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16289401054382324, "Height": 0.06353222578763962, "Left": 0.6515318155288696, "Top": 0.185936838388443}, "Polygon": [{"X": 0.6515441536903381, "Y": 0.185936838388443}, {"X": 0.8144258260726929, "Y": 0.18596875667572021}, {"X": 0.8144135475158691, "Y": 0.24946905672550201}, {"X": 0.6515318155288696, "Y": 0.2494368702173233}]}, "Id": "4c940975-3f56-4871-abdf-e772037e29cc"}, {"BlockType": "WORD", "Confidence": 98.44945526123047, "Text": "All", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09840112924575806, "Height": 0.06686420738697052, "Left": 0.8347169160842896, "Top": 0.18843422830104828}, "Polygon": [{"X": 0.8347298502922058, "Y": 0.18843422830104828}, {"X": 0.9331180453300476, "Y": 0.1884535253047943}, {"X": 0.9331051707267761, "Y": 0.2552984356880188}, {"X": 0.8347169160842896, "Y": 0.25527897477149963}]}, "Id": "b8cfd741-6a43-4a60-adbb-785af2760aff"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ADDRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1607072800397873, "Height": 0.019836321473121643, "Left": 0.07997357845306396, "Top": 0.25052410364151}, "Polygon": [{"X": 0.07997749000787735, "Y": 0.25052410364151}, {"X": 0.24068084359169006, "Y": 0.2505558729171753}, {"X": 0.24067695438861847, "Y": 0.27036044001579285}, {"X": 0.07997357845306396, "Y": 0.2703285813331604}]}, "Id": "814af5cb-75ae-4893-a593-e83d61018b8b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "CARRIER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14991030097007751, "Height": 0.019587527960538864, "Left": 0.08069383352994919, "Top": 0.31892383098602295}, "Polygon": [{"X": 0.08069770038127899, "Y": 0.31892383098602295}, {"X": 0.2306041270494461, "Y": 0.3189537227153778}, {"X": 0.2306002825498581, "Y": 0.3385113775730133}, {"X": 0.08069383352994919, "Y": 0.3384813964366913}]}, "Id": "61bfe8f7-091a-412f-bbd7-7c2cb024dcf1"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "COMMODITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20571179687976837, "Height": 0.019621293991804123, "Left": 0.08098001778125763, "Top": 0.2849734127521515}, "Polygon": [{"X": 0.08098389208316803, "Y": 0.2849734127521515}, {"X": 0.286691814661026, "Y": 0.285014271736145}, {"X": 0.286687970161438, "Y": 0.3045947253704071}, {"X": 0.08098001778125763, "Y": 0.3045537769794464}]}, "Id": "7a4729ed-8813-4ca4-8663-672a54600021"}, {"BlockType": "WORD", "Confidence": 97.58708953857422, "Text": "GRS", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16596563160419464, "Height": 0.06593924015760422, "Left": 0.46483299136161804, "Top": 0.29602012038230896}, "Polygon": [{"X": 0.4648458659648895, "Y": 0.29602012038230896}, {"X": 0.6307986378669739, "Y": 0.2960531413555145}, {"X": 0.6307858228683472, "Y": 0.3619593679904938}, {"X": 0.46483299136161804, "Y": 0.3619261085987091}]}, "Id": "4a3027f7-c4db-4ae6-b6fa-fb7f20686a15"}, {"BlockType": "WORD", "Confidence": 98.34402465820312, "Text": "#413", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1390327662229538, "Height": 0.03931397572159767, "Left": 0.665727436542511, "Top": 0.31293341517448425}, "Polygon": [{"X": 0.6657350659370422, "Y": 0.31293341517448425}, {"X": 0.804760217666626, "Y": 0.3129611313343048}, {"X": 0.8047525882720947, "Y": 0.3522473871707916}, {"X": 0.665727436542511, "Y": 0.3522195518016815}]}, "Id": "a59d256b-15f1-4d7d-8431-bb71192a56c5"}, {"BlockType": "WORD", "Confidence": 82.89122772216797, "Text": "INBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11884454637765884, "Height": 0.020779678598046303, "Left": 0.3759719133377075, "Top": 0.35540226101875305}, "Polygon": [{"X": 0.37597596645355225, "Y": 0.35540226101875305}, {"X": 0.49481645226478577, "Y": 0.3554260730743408}, {"X": 0.49481239914894104, "Y": 0.3761819303035736}, {"X": 0.3759719133377075, "Y": 0.37615805864334106}]}, "Id": "4bac4097-3e52-4d2f-9fbd-af1ddd724903"}, {"BlockType": "WORD", "Confidence": 98.97480773925781, "Text": "59160", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08899781852960587, "Height": 0.021487422287464142, "Left": 0.545189619064331, "Top": 0.35854947566986084}, "Polygon": [{"X": 0.5451938509941101, "Y": 0.35854947566986084}, {"X": 0.6341874599456787, "Y": 0.35856732726097107}, {"X": 0.6341832876205444, "Y": 0.3800368905067444}, {"X": 0.545189619064331, "Y": 0.38001900911331177}]}, "Id": "a501ebb3-85ee-4dff-955e-5cc63b87cc26"}, {"BlockType": "WORD", "Confidence": 94.80927276611328, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035746019333601, "Height": 0.019939979538321495, "Left": 0.6535639762878418, "Top": 0.36076977849006653}, "Polygon": [{"X": 0.6535678505897522, "Y": 0.36076977849006653}, {"X": 0.6893099546432495, "Y": 0.3607769310474396}, {"X": 0.6893060803413391, "Y": 0.38070976734161377}, {"X": 0.6535639762878418, "Y": 0.38070258498191833}]}, "Id": "aab083b8-215e-459d-9ffb-d014aaf4e4b6"}, {"BlockType": "WORD", "Confidence": 96.26375579833984, "Text": "LOOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07007550448179245, "Height": 0.02019791677594185, "Left": 0.37293535470962524, "Top": 0.37496498227119446}, "Polygon": [{"X": 0.3729392886161804, "Y": 0.37496498227119446}, {"X": 0.4430108666419983, "Y": 0.37497907876968384}, {"X": 0.4430069029331207, "Y": 0.3951629102230072}, {"X": 0.37293535470962524, "Y": 0.39514878392219543}]}, "Id": "9e08704e-bb19-4daa-9863-23d75a4bc466"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033157460391521454, "Height": 0.019046159461140633, "Left": 0.46260780096054077, "Top": 0.3770730793476105}, "Polygon": [{"X": 0.46261152625083923, "Y": 0.3770730793476105}, {"X": 0.4957652688026428, "Y": 0.37707972526550293}, {"X": 0.49576154351234436, "Y": 0.39611923694610596}, {"X": 0.46260780096054077, "Y": 0.3961125612258911}]}, "Id": "6447a052-b932-47fd-8ce6-110a544128e7"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "440", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0518970713019371, "Height": 0.01967407390475273, "Left": 0.5125876665115356, "Top": 0.37734314799308777}, "Polygon": [{"X": 0.512591540813446, "Y": 0.37734314799308777}, {"X": 0.5644847750663757, "Y": 0.37735357880592346}, {"X": 0.5644809603691101, "Y": 0.3970172107219696}, {"X": 0.5125876665115356, "Y": 0.3970067501068115}]}, "Id": "9494fc34-6dff-48df-aa4e-992987bdc244"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "INBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15186209976673126, "Height": 0.019280413165688515, "Left": 0.08365057408809662, "Top": 0.4282741844654083}, "Polygon": [{"X": 0.08365438133478165, "Y": 0.4282741844654083}, {"X": 0.23551267385482788, "Y": 0.42830491065979004}, {"X": 0.23550888895988464, "Y": 0.4475545883178711}, {"X": 0.08365057408809662, "Y": 0.4475238025188446}]}, "Id": "bcef12b6-7e1a-42d5-8165-88a16d8eeb52"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0825585275888443, "Height": 0.018261516466736794, "Left": 0.24506619572639465, "Top": 0.4305432438850403}, "Polygon": [{"X": 0.24506977200508118, "Y": 0.4305432438850403}, {"X": 0.32762470841407776, "Y": 0.4305599629878998}, {"X": 0.32762113213539124, "Y": 0.4488047659397125}, {"X": 0.24506619572639465, "Y": 0.44878801703453064}]}, "Id": "21966acc-aeca-474c-aa13-5b006bc21ef6"}, {"BlockType": "WORD", "Confidence": 80.53870391845703, "Text": "8-29-25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12320298701524734, "Height": 0.020907150581479073, "Left": 0.4423697888851166, "Top": 0.43449100852012634}, "Polygon": [{"X": 0.4423738718032837, "Y": 0.43449100852012634}, {"X": 0.5655727982521057, "Y": 0.43451595306396484}, {"X": 0.565568745136261, "Y": 0.45539817214012146}, {"X": 0.4423697888851166, "Y": 0.4553731679916382}]}, "Id": "3774f020-cc4b-4881-b893-b4917bf55e56"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08003747463226318, "Height": 0.01834859512746334, "Left": 0.5982536673545837, "Top": 0.4361626207828522}, "Polygon": [{"X": 0.5982572436332703, "Y": 0.4361626207828522}, {"X": 0.6782911419868469, "Y": 0.4361788332462311}, {"X": 0.6782876253128052, "Y": 0.45451122522354126}, {"X": 0.5982536673545837, "Y": 0.45449498295783997}]}, "Id": "eb215b83-92e7-412e-82f2-0ae1ef09bb41"}, {"BlockType": "WORD", "Confidence": 99.40110778808594, "Text": "8:23AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10577557981014252, "Height": 0.019830064848065376, "Left": 0.7739468216896057, "Top": 0.44121435284614563}, "Polygon": [{"X": 0.7739506959915161, "Y": 0.44121435284614563}, {"X": 0.8797224164009094, "Y": 0.4412357807159424}, {"X": 0.8797186017036438, "Y": 0.46104440093040466}, {"X": 0.7739468216896057, "Y": 0.4610229432582855}]}, "Id": "4f7a9dc8-8916-4011-a828-e3197d2564bc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OUTBOUND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1897272914648056, "Height": 0.019061271101236343, "Left": 0.08224762976169586, "Top": 0.4651366174221039}, "Polygon": [{"X": 0.08225138485431671, "Y": 0.4651366174221039}, {"X": 0.27197492122650146, "Y": 0.46517518162727356}, {"X": 0.271971195936203, "Y": 0.4841978847980499}, {"X": 0.08224762976169586, "Y": 0.4841592311859131}]}, "Id": "9c33e6a8-2993-431c-87ca-6068bc2939a2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0818978026509285, "Height": 0.018123310059309006, "Left": 0.282126247882843, "Top": 0.46723487973213196}, "Polygon": [{"X": 0.28212982416152954, "Y": 0.46723487973213196}, {"X": 0.3640240728855133, "Y": 0.4672515392303467}, {"X": 0.3640204966068268, "Y": 0.48535820841789246}, {"X": 0.282126247882843, "Y": 0.48534151911735535}]}, "Id": "528d5925-85ff-4747-afb5-5c05ce07918e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "8-29-25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1222822368144989, "Height": 0.021752888336777687, "Left": 0.4435425102710724, "Top": 0.47305235266685486}, "Polygon": [{"X": 0.44354677200317383, "Y": 0.47305235266685486}, {"X": 0.5658247470855713, "Y": 0.4730772078037262}, {"X": 0.5658205151557922, "Y": 0.4948052167892456}, {"X": 0.4435425102710724, "Y": 0.4947802722454071}]}, "Id": "2d0d728a-5f47-4aec-bba2-915a4d4afccf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07954118400812149, "Height": 0.01823299564421177, "Left": 0.5987957715988159, "Top": 0.4722515940666199}, "Polygon": [{"X": 0.5987992882728577, "Y": 0.4722515940666199}, {"X": 0.6783369779586792, "Y": 0.4722677767276764}, {"X": 0.6783334016799927, "Y": 0.4904845952987671}, {"X": 0.5987957715988159, "Y": 0.4904683828353882}]}, "Id": "a8c646a5-da15-49dc-87b1-c516c3d03936"}, {"BlockType": "WORD", "Confidence": 99.73313903808594, "Text": "8:59AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10593078285455704, "Height": 0.020057471469044685, "Left": 0.7742763161659241, "Top": 0.48087477684020996}, "Polygon": [{"X": 0.7742801904678345, "Y": 0.48087477684020996}, {"X": 0.8802071213722229, "Y": 0.48089635372161865}, {"X": 0.8802032470703125, "Y": 0.5009322762489319}, {"X": 0.7742763161659241, "Y": 0.5009106397628784}]}, "Id": "cc2d5339-acdd-433b-bb57-69936c8625d7"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "59160", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1451762616634369, "Height": 0.02199375070631504, "Left": 0.43012532591819763, "Top": 0.5304410457611084}, "Polygon": [{"X": 0.43012961745262146, "Y": 0.5304410457611084}, {"X": 0.5753015875816345, "Y": 0.5304707884788513}, {"X": 0.5752972960472107, "Y": 0.5524348020553589}, {"X": 0.43012532591819763, "Y": 0.5524049401283264}]}, "Id": "5ef67ea1-219f-4e31-a116-0e0c3524a2cb"}, {"BlockType": "WORD", "Confidence": 58.30835723876953, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05250401422381401, "Height": 0.019229022786021233, "Left": 0.6087237000465393, "Top": 0.5342801213264465}, "Polygon": [{"X": 0.6087274551391602, "Y": 0.5342801213264465}, {"X": 0.6612277030944824, "Y": 0.5342909097671509}, {"X": 0.6612240076065063, "Y": 0.5535091757774353}, {"X": 0.6087237000465393, "Y": 0.5534983277320862}]}, "Id": "8267d7e7-74b4-46af-80f9-870efd231806"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14220768213272095, "Height": 0.021380102261900902, "Left": 0.6923324465751648, "Top": 0.5360933542251587}, "Polygon": [{"X": 0.6923365592956543, "Y": 0.5360933542251587}, {"X": 0.8345401287078857, "Y": 0.5361225008964539}, {"X": 0.8345360159873962, "Y": 0.5574734210968018}, {"X": 0.6923324465751648, "Y": 0.5574442148208618}]}, "Id": "9b3887fa-391a-4108-929e-ff32c11129df"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "31240", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14186567068099976, "Height": 0.020033108070492744, "Left": 0.43207308650016785, "Top": 0.5507842302322388}, "Polygon": [{"X": 0.432077020406723, "Y": 0.5507842302322388}, {"X": 0.57393878698349, "Y": 0.5508133769035339}, {"X": 0.5739348530769348, "Y": 0.5708173513412476}, {"X": 0.43207308650016785, "Y": 0.5707880854606628}]}, "Id": "d4aab715-c0b7-41ee-9f90-bfcb4a6b9570"}, {"BlockType": "WORD", "Confidence": 94.62372589111328, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052954718470573425, "Height": 0.01974509097635746, "Left": 0.6083171367645264, "Top": 0.5533062219619751}, "Polygon": [{"X": 0.6083210110664368, "Y": 0.5533062219619751}, {"X": 0.661271870136261, "Y": 0.553317129611969}, {"X": 0.6612680554389954, "Y": 0.5730513334274292}, {"X": 0.6083171367645264, "Y": 0.5730404257774353}]}, "Id": "a5e442e6-57b0-4fd8-b953-bdad2f764f0a"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "TARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11264969408512115, "Height": 0.02064317837357521, "Left": 0.6935502290725708, "Top": 0.5553774237632751}, "Polygon": [{"X": 0.6935542225837708, "Y": 0.5553774237632751}, {"X": 0.8061999082565308, "Y": 0.5554006099700928}, {"X": 0.8061959147453308, "Y": 0.5760205984115601}, {"X": 0.6935502290725708, "Y": 0.5759974122047424}]}, "Id": "06ed23e0-37f7-4be5-a975-0d5423fd282d"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "27920", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14272385835647583, "Height": 0.02090553380548954, "Left": 0.4299936890602112, "Top": 0.5697481632232666}, "Polygon": [{"X": 0.4299977719783783, "Y": 0.5697481632232666}, {"X": 0.572717547416687, "Y": 0.5697776079177856}, {"X": 0.5727134943008423, "Y": 0.5906537175178528}, {"X": 0.4299936890602112, "Y": 0.5906242728233337}]}, "Id": "fb94a6c1-58fe-4181-83ab-d943f1c7cb14"}, {"BlockType": "WORD", "Confidence": 95.25749206542969, "Text": "1b", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05281238257884979, "Height": 0.02025456354022026, "Left": 0.6068776845932007, "Top": 0.5725001692771912}, "Polygon": [{"X": 0.6068816184997559, "Y": 0.5725001692771912}, {"X": 0.6596900820732117, "Y": 0.5725110173225403}, {"X": 0.6596861481666565, "Y": 0.5927547216415405}, {"X": 0.6068776845932007, "Y": 0.5927438139915466}]}, "Id": "157da11d-5b5b-4a7c-b585-1fe1ac28c236"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08563113212585449, "Height": 0.019937891513109207, "Left": 0.6906033158302307, "Top": 0.5742926001548767}, "Polygon": [{"X": 0.6906071901321411, "Y": 0.5742926001548767}, {"X": 0.7762344479560852, "Y": 0.5743102431297302}, {"X": 0.7762305736541748, "Y": 0.5942304730415344}, {"X": 0.6906033158302307, "Y": 0.5942127704620361}]}, "Id": "23f751d4-f7fb-4ad4-b22a-36db8c1a564d"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "LOOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07069334387779236, "Height": 0.02097962237894535, "Left": 0.3733022212982178, "Top": 0.6270836591720581}, "Polygon": [{"X": 0.3733063340187073, "Y": 0.6270836591720581}, {"X": 0.44399556517601013, "Y": 0.6270983219146729}, {"X": 0.443991482257843, "Y": 0.6480633020401001}, {"X": 0.3733022212982178, "Y": 0.6480485796928406}]}, "Id": "00098ad7-67ad-44be-b399-ae84441d5134"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03380384296178818, "Height": 0.020017996430397034, "Left": 0.4634304642677307, "Top": 0.6287124752998352}, "Polygon": [{"X": 0.4634343981742859, "Y": 0.6287124752998352}, {"X": 0.4972343146800995, "Y": 0.6287195086479187}, {"X": 0.4972304105758667, "Y": 0.6487305164337158}, {"X": 0.4634304642677307, "Y": 0.6487234830856323}]}, "Id": "661932a2-9597-4c51-b274-1d1df23811ef"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "440", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05257903039455414, "Height": 0.019712435081601143, "Left": 0.5134198069572449, "Top": 0.6298537850379944}, "Polygon": [{"X": 0.5134236812591553, "Y": 0.6298537850379944}, {"X": 0.5659988522529602, "Y": 0.6298646926879883}, {"X": 0.5659950375556946, "Y": 0.6495661735534668}, {"X": 0.5134198069572449, "Y": 0.6495552659034729}]}, "Id": "d785dc3b-5050-4dbd-a672-915244cee6d0"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DRIVER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12637890875339508, "Height": 0.01861119084060192, "Left": 0.08308286219835281, "Top": 0.6703963875770569}, "Polygon": [{"X": 0.0830865353345871, "Y": 0.6703963875770569}, {"X": 0.2094617635011673, "Y": 0.6704227328300476}, {"X": 0.2094581127166748, "Y": 0.689007580280304}, {"X": 0.08308286219835281, "Y": 0.6889811754226685}]}, "Id": "52f4a683-7940-4d4c-8c07-c0321df617af"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049447767436504364, "Height": 0.017606325447559357, "Left": 0.21789760887622833, "Top": 0.6713253259658813}, "Polygon": [{"X": 0.2179010808467865, "Y": 0.6713253259658813}, {"X": 0.2673453688621521, "Y": 0.6713356375694275}, {"X": 0.2673419117927551, "Y": 0.6889316439628601}, {"X": 0.21789760887622833, "Y": 0.688921332359314}]}, "Id": "d9ac31e3-b8c2-46c9-bcde-b51b720640a1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06643310934305191, "Height": 0.018057823181152344, "Left": 0.5410284996032715, "Top": 0.6748583316802979}, "Polygon": [{"X": 0.5410320162773132, "Y": 0.6748583316802979}, {"X": 0.6074615716934204, "Y": 0.6748722195625305}, {"X": 0.6074580550193787, "Y": 0.6929161548614502}, {"X": 0.5410284996032715, "Y": 0.6929022669792175}]}, "Id": "dba16128-97c9-41a5-8e20-41a2304802ae"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "SHIPPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14644023776054382, "Height": 0.018395427614450455, "Left": 0.08271474391222, "Top": 0.824546217918396}, "Polygon": [{"X": 0.0827183723449707, "Y": 0.824546217918396}, {"X": 0.22915497422218323, "Y": 0.8245773911476135}, {"X": 0.22915136814117432, "Y": 0.8429416418075562}, {"X": 0.08271474391222, "Y": 0.8429104685783386}]}, "Id": "fa57b36b-dc2e-4c1d-a78f-82bf8ba7de1f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.018372323364019394, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "b5aca64f-d542-493f-872b-5f2d64de63cd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FAIRBANKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.015574008226394653, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.***************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "200ed8e8-a17e-4513-9ae6-1ae333cab9c0"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.015204988420009613, "Left": 0.***************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.***************, "Y": 0.****************}]}, "Id": "298e4ad3-7419-461e-92b7-0327c7bcbac2"}, {"BlockType": "WORD", "Confidence": 99.***********422, "Text": "CAT.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057219281792640686, "Height": 0.015440416522324085, "Left": 0.7949519753456116, "Top": 0.926659882068634}, "Polygon": [{"X": 0.7949549555778503, "Y": 0.926659882068634}, {"X": 0.8521712422370911, "Y": 0.9266722202301025}, {"X": 0.8521682620048523, "Y": 0.9421002864837646}, {"X": 0.7949519753456116, "Y": 0.9420879483222961}]}, "Id": "4fc426e3-1ed1-4b30-a100-6d903aad21c0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "16288", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07256916910409927, "Height": 0.014413564465939999, "Left": 0.8652224540710449, "Top": 0.9273789525032043}, "Polygon": [{"X": 0.8652252554893494, "Y": 0.9273789525032043}, {"X": 0.937791645526886, "Y": 0.9273945689201355}, {"X": 0.9377888441085815, "Y": 0.9417924880981445}, {"X": 0.8652224540710449, "Y": 0.9417768716812134}]}, "Id": "9b78df47-88c8-4f53-909c-4d92711aed0d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "9df4ab88-aaf5-4d28-b010-19ab8be757c4", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "9df4ab88-aaf5-4d28-b010-19ab8be757c4", "content-type": "application/x-amz-json-1.1", "content-length": "38277", "date": "Wed, 24 Sep 2025 12:46:16 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "6faeac45-bb08-4e44-b782-1e29cac14289", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:18 GMT", "content-type": "application/json", "content-length": "625", "connection": "keep-alive", "x-amzn-requestid": "6faeac45-bb08-4e44-b782-1e29cac14289"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page with weight information. It's a scale ticket. Keywords: WEIGHED ON A FAIRBANKS SCALE, GROSS, TARE, NET, WEIGHER. So doc_type should be scale_ticket. We need to output via tool classify_logistics_doc_type. Provide documents array with one object: page_no:1, doc_type:\"scale_ticket\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2657, "outputTokens": 94, "totalTokens": 2751}, "metrics": {"latencyMs": 673}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7e1e036e_Z1N2YIEHAA2FERWDQ38E.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nWEIG<PERSON>ED ON A FAIRBANKS SCALE\nCUSTOMER'S NAME\nRec. All\nADDRESS\nCARRIER COMMODITY GRS #413\nINBOUND 59160 1b\nLOOP ID 440\nINBOUND DATE\n8-29-25 TIME\n8:23AM\nOUTBOUND DATE\n8-29-25 TIME\n8:59AM\n59160 1b GROSS\n31240 1b TARE\n27920 1b NET\nLOOP ID 440\nDRIVER ON\nOFF\nSHIPPER\nWEIGHER\nFAIRBANKS SCALE CAT. 16288\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}