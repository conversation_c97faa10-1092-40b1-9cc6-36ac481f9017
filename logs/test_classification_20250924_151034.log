2025-09-24 15:10:34,322 - INFO - Logging initialized. Log file: logs/test_classification_20250924_151034.log
2025-09-24 15:10:34,323 - INFO - 📁 Found 1 files to process
2025-09-24 15:10:34,323 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:10:34,323 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 15:10:34,323 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 15:10:34,323 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 15:10:34,324 - INFO - ⬆️ [15:10:34] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:37,431 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/80717fa9_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:37,432 - INFO - 🔍 [15:10:37] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:37,434 - INFO - Initializing TextractProcessor...
2025-09-24 15:10:37,453 - INFO - Initializing BedrockProcessor...
2025-09-24 15:10:37,461 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/80717fa9_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:37,461 - INFO - Processing PDF from S3...
2025-09-24 15:10:37,461 - INFO - Downloading PDF from S3 to /tmp/tmp207604k3/80717fa9_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:39,775 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:10:39,775 - INFO - Splitting PDF into individual pages...
2025-09-24 15:10:39,779 - INFO - Splitting PDF 80717fa9_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:10:39,785 - INFO - Split PDF into 1 pages
2025-09-24 15:10:39,785 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:10:39,785 - INFO - Expected pages: [1]
2025-09-24 15:10:49,165 - INFO - Page 1: Extracted 939 characters, 64 lines from 80717fa9_A34CDFDJ66EDOZEKZWJL_6db40767_page_001.pdf
2025-09-24 15:10:49,165 - INFO - Successfully processed page 1
2025-09-24 15:10:49,165 - INFO - Combined 1 pages into final text
2025-09-24 15:10:49,165 - INFO - Text validation for 80717fa9_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:10:49,166 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:10:49,166 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:10:51,822 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '05097fb1-300c-4cd4-a704-81edc5c29856', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:40:51 GMT', 'content-type': 'application/json', 'content-length': '1376', 'connection': 'keep-alive', 'x-amzn-requestid': '05097fb1-300c-4cd4-a704-81edc5c29856'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. The content is an Inspection Certificate. Keywords: "Inspection Certificate", "Mettler-Toledo", "Weight(Lbs)", "Dimensions", "Certificate No." It looks like a weight and inspection certificate. It says "This is to certify that the shipment transported on the referenced Pro number was measured and weighed". So it\'s a weight and inspection certificate. According to doc_type list: weight_and_inspection_cert. However we need to check if it has nmfc_cert characteristics: "Strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information", "NMFC Code", "class #". This page doesn\'t have those. So weight_and_inspection_cert.\n\nSo result: page_no 1, doc_type "weight_and_inspection_cert".\n\nReturn via classify_logistics_doc_type function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 237, 'totalTokens': 3063}, 'metrics': {'latencyMs': 1088}}
2025-09-24 15:10:51,823 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/80717fa9_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:52,171 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:10:52,171 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:10:53,326 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/80717fa9_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:53,328 - INFO - 
📊 Processing Summary:
2025-09-24 15:10:53,328 - INFO -    Total files: 1
2025-09-24 15:10:53,328 - INFO -    Successful: 1
2025-09-24 15:10:53,328 - INFO -    Failed: 0
2025-09-24 15:10:53,328 - INFO -    Duration: 19.00 seconds
2025-09-24 15:10:53,329 - INFO -    Output directory: output
2025-09-24 15:10:53,329 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:10:53,329 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:10:53,329 - INFO - 
============================================================================================================================================
2025-09-24 15:10:53,330 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:10:53,330 - INFO - ============================================================================================================================================
2025-09-24 15:10:53,330 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:10:53,330 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:10:53,330 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:10:53,330 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:10:53,331 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:10:53,331 - INFO - 
2025-09-24 15:10:53,331 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:10:53,331 - INFO - Total entries: 1
2025-09-24 15:10:53,331 - INFO - ============================================================================================================================================
2025-09-24 15:10:53,331 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:10:53,331 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:10:53,332 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:10:53,332 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:10:53,332 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 19.004192, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
