{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 5.331627050964016e-08}, {"X": 1.0, "Y": 1.0}, {"X": 1.1444807768157261e-08, "Y": 1.0}]}, "Id": "ebbb79b6-9f4c-4188-9c53-c393c019e0c6", "Relationships": [{"Type": "CHILD", "Ids": ["ea6f9cba-68d6-4eeb-96f3-fae46e9b239a", "5f2a7f8b-f240-41ec-bd63-d798f5de3646", "9eb3457d-4240-4751-bc03-05558bb8fe53", "6213c365-c815-498c-950b-dcb85fa7d9ad", "253680a6-58d2-461a-8bec-f9837f4eaaee", "598851bf-d71d-41ae-bae6-edbca40ef360", "c47a8d39-d838-4519-92de-cf70f38b14cd", "905008a2-b1db-40be-8c0f-bd18c32e0d02", "b11ceaec-cb01-45bd-8bce-1db8170edc13", "526a60e2-61a7-4405-96be-8daa4f0c4cd5", "d87d8c5e-8202-47a9-8c60-c91463c23cf2", "a03eafcb-226f-4186-95b3-abd00cfe1932", "6dd8726b-38f1-4319-971f-5ce611620fcc", "45929990-e53c-4980-9fa3-0ddcb2fd77ef", "cc5c31db-8ff7-4e23-a38a-93ae297524e5", "e1c06c25-c8c6-43e2-ac21-74f37791aa75", "70e2b5c6-4871-4e51-a194-92c2e1e3fa3a", "f48c95f7-d838-46b3-8b00-09d24ad7f8a9", "00d80760-bb06-473e-82f3-cff79695a7e4", "85a53ca9-8cab-4ed9-9fcb-9362e2a91bac", "886761fe-7124-42ac-b10e-833e5a6fcf8e", "87b48190-9b38-41fc-8357-4486695b4646", "5196bcf7-423f-4b43-a483-5fc71c8ca134", "49084200-17fb-4813-ac48-b7d6160f4d57", "8cf5e8bf-fcd6-4628-ae19-8b2f39476e78", "bd4ca3d0-7074-4a4e-b08d-d57032acde61", "caf2139b-96fc-45ec-8638-256c20b4d698", "ed8cf8f6-990f-4dcd-a601-146063d9da8e"]}]}, {"BlockType": "LINE", "Confidence": 98.3177261352539, "Text": "Lineage", "Geometry": {"BoundingBox": {"Width": 0.12350434064865112, "Height": 0.024207163602113724, "Left": 0.13369432091712952, "Top": 0.04372582957148552}, "Polygon": [{"X": 0.13369432091712952, "Y": 0.04372582957148552}, {"X": 0.25719863176345825, "Y": 0.04372597858309746}, {"X": 0.25719866156578064, "Y": 0.06793299317359924}, {"X": 0.13369442522525787, "Y": 0.0679328590631485}]}, "Id": "ea6f9cba-68d6-4eeb-96f3-fae46e9b239a", "Relationships": [{"Type": "CHILD", "Ids": ["54b27f2f-553d-4183-9dbd-0c1cda69d063"]}]}, {"BlockType": "LINE", "Confidence": 99.7461929321289, "Text": "Wilmington, IL", "Geometry": {"BoundingBox": {"Width": 0.08332865685224533, "Height": 0.009798364713788033, "Left": 0.360679030418396, "Top": 0.047026779502630234}, "Polygon": [{"X": 0.3606790602207184, "Y": 0.047026779502630234}, {"X": 0.4440076947212219, "Y": 0.04702688008546829}, {"X": 0.44400766491889954, "Y": 0.056825146079063416}, {"X": 0.360679030418396, "Y": 0.056825049221515656}]}, "Id": "5f2a7f8b-f240-41ec-bd63-d798f5de3646", "Relationships": [{"Type": "CHILD", "Ids": ["35bbb71f-95f2-480d-97b2-c6857276b3eb", "28dfc9e2-4b26-4641-b84c-5e65b494a1ca"]}]}, {"BlockType": "LINE", "Confidence": 99.86141967773438, "Text": "Relay Sale# 8000", "Geometry": {"BoundingBox": {"Width": 0.10237634927034378, "Height": 0.009901385754346848, "Left": 0.8236644864082336, "Top": 0.0469571128487587}, "Polygon": [{"X": 0.8236646056175232, "Y": 0.0469571128487587}, {"X": 0.9260408282279968, "Y": 0.04695723578333855}, {"X": 0.9260407090187073, "Y": 0.056858498603105545}, {"X": 0.8236644864082336, "Y": 0.05685838311910629}]}, "Id": "9eb3457d-4240-4751-bc03-05558bb8fe53", "Relationships": [{"Type": "CHILD", "Ids": ["4928043a-19d4-4786-ae70-9cbd44b5d263", "45b56a79-b8cf-44cf-bf3d-ba2ba0552e12", "36d56577-5ade-4efa-8be5-ec5c18d5d161"]}]}, {"BlockType": "LINE", "Confidence": 99.70609283447266, "Text": "29700 S Graaskamp Blvd, Wilmington, IL 60481", "Geometry": {"BoundingBox": {"Width": 0.2771126329898834, "Height": 0.009818061254918575, "Left": 0.36122891306877136, "Top": 0.06017545983195305}, "Polygon": [{"X": 0.36122894287109375, "Y": 0.06017545983195305}, {"X": 0.6383415460586548, "Y": 0.06017576530575752}, {"X": 0.63834148645401, "Y": 0.0699935182929039}, {"X": 0.36122891306877136, "Y": 0.06999322772026062}]}, "Id": "6213c365-c815-498c-950b-dcb85fa7d9ad", "Relationships": [{"Type": "CHILD", "Ids": ["eabac441-f918-4ea9-87e6-12613353a842", "a8f174fd-8f8b-4aa8-bb1d-a056f88c0dae", "3b144cf1-405d-4d0f-8888-f9397f147ab3", "e79add0d-7d98-4c56-923e-38c47682e88d", "0964e46f-6d2c-4834-8175-9f1ce1a985ef", "030f0c14-84d4-422c-a695-7ecb7b0ea83c", "2657f0a0-8eb4-41da-aeeb-24ec111b7319"]}]}, {"BlockType": "LINE", "Confidence": 99.79767608642578, "Text": "Sep 19, 2025 20:03 PM EDT", "Geometry": {"BoundingBox": {"Width": 0.16670581698417664, "Height": 0.009704177267849445, "Left": 0.7593706250190735, "Top": 0.06021388992667198}, "Polygon": [{"X": 0.759370744228363, "Y": 0.06021388992667198}, {"X": 0.9260764718055725, "Y": 0.060214072465896606}, {"X": 0.9260762929916382, "Y": 0.06991806626319885}, {"X": 0.7593706250190735, "Y": 0.06991789489984512}]}, "Id": "253680a6-58d2-461a-8bec-f9837f4eaaee", "Relationships": [{"Type": "CHILD", "Ids": ["4acec91a-a894-473f-b29d-b10c79564574", "01caadf9-4441-40c3-b168-15fc0e03a1af", "5d9483d4-2f44-4533-9c78-c8919f99af91", "d0cd7527-0ea8-4fbb-94fa-112492fc5ef0", "26eb244e-081e-4750-914e-b4e21117ecd0", "d33cb1d2-c858-47a3-b3dd-85877cb2fe88"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Paid By:", "Geometry": {"BoundingBox": {"Width": 0.06373906135559082, "Height": 0.01224919967353344, "Left": 0.07476148754358292, "Top": 0.1434316784143448}, "Polygon": [{"X": 0.07476148754358292, "Y": 0.1434316784143448}, {"X": 0.13850049674510956, "Y": 0.14343172311782837}, {"X": 0.13850054144859314, "Y": 0.15568087995052338}, {"X": 0.07476155459880829, "Y": 0.155680850148201}]}, "Id": "598851bf-d71d-41ae-bae6-edbca40ef360", "Relationships": [{"Type": "CHILD", "Ids": ["95ccdf93-ca90-4a3c-aa50-af6d128b3f44", "37fce55a-64e8-4be2-9457-6db0b77dfea1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Location:", "Geometry": {"BoundingBox": {"Width": 0.07202892005443573, "Height": 0.00970015674829483, "Left": 0.5055063366889954, "Top": 0.14356237649917603}, "Polygon": [{"X": 0.5055063962936401, "Y": 0.14356237649917603}, {"X": 0.5775352716445923, "Y": 0.1435624212026596}, {"X": 0.5775352120399475, "Y": 0.15326254069805145}, {"X": 0.5055063366889954, "Y": 0.15326249599456787}]}, "Id": "c47a8d39-d838-4519-92de-cf70f38b14cd", "Relationships": [{"Type": "CHILD", "Ids": ["682bc9b7-fdd1-47a8-9b92-c23edcbef4b6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "G<PERSON><PERSON> R<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10961658507585526, "Height": 0.009800612926483154, "Left": 0.07434289902448654, "Top": 0.1680653691291809}, "Polygon": [{"X": 0.07434289902448654, "Y": 0.1680653691291809}, {"X": 0.1839594542980194, "Y": 0.1680654138326645}, {"X": 0.1839594841003418, "Y": 0.17786598205566406}, {"X": 0.07434295117855072, "Y": 0.17786593735218048}]}, "Id": "905008a2-b1db-40be-8c0f-bd18c32e0d02", "Relationships": [{"Type": "CHILD", "Ids": ["42aebf60-dc91-4079-846d-c0f709127c66", "9163a395-ab4d-4ed6-aaa7-8e9c18a12558"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Wilmington, IL", "Geometry": {"BoundingBox": {"Width": 0.10386893153190613, "Height": 0.012230025604367256, "Left": 0.5047810673713684, "Top": 0.1680527776479721}, "Polygon": [{"X": 0.5047811269760132, "Y": 0.1680527776479721}, {"X": 0.6086500287055969, "Y": 0.1680528223514557}, {"X": 0.6086499094963074, "Y": 0.1802828013896942}, {"X": 0.5047810673713684, "Y": 0.18028275668621063}]}, "Id": "b11ceaec-cb01-45bd-8bce-1db8170edc13", "Relationships": [{"Type": "CHILD", "Ids": ["afade3bb-8e15-43a5-a7cc-a3bc19b35ea4", "43a58cc0-e706-437e-ba3a-6bff8e19c6f6"]}]}, {"BlockType": "LINE", "Confidence": 99.87367248535156, "Text": "29700 S Graaskamp Blvd, Wilmington, IL 60481", "Geometry": {"BoundingBox": {"Width": 0.3461087942123413, "Height": 0.012213250622153282, "Left": 0.5048787593841553, "Top": 0.18560081720352173}, "Polygon": [{"X": 0.5048788189888, "Y": 0.18560081720352173}, {"X": 0.8509875535964966, "Y": 0.18560092151165009}, {"X": 0.8509873747825623, "Y": 0.19781406223773956}, {"X": 0.5048787593841553, "Y": 0.1978139728307724}]}, "Id": "526a60e2-61a7-4405-96be-8daa4f0c4cd5", "Relationships": [{"Type": "CHILD", "Ids": ["d727ebf8-f08c-4594-93b6-12ca0015e2e6", "d74cc4c4-1a32-42e0-b2fd-117f6f7038cd", "8f8eae33-a52e-4b34-ac5a-a78e55f51a78", "c186f185-c9cd-456a-a955-dfbfdc8996de", "7056e053-b63c-40b0-977b-8d1de720814d", "113c6162-7c12-42ce-8494-4c068c325e1f", "c666e158-aa42-4721-a1d8-c79120468cc9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Services", "Geometry": {"BoundingBox": {"Width": 0.06653530895709991, "Height": 0.00978798232972622, "Left": 0.07440520823001862, "Top": 0.27109846472740173}, "Polygon": [{"X": 0.07440520823001862, "Y": 0.27109846472740173}, {"X": 0.14094047248363495, "Y": 0.27109846472740173}, {"X": 0.14094051718711853, "Y": 0.2808864116668701}, {"X": 0.0744052603840828, "Y": 0.2808864414691925}]}, "Id": "d87d8c5e-8202-47a9-8c60-c91463c23cf2", "Relationships": [{"Type": "CHILD", "Ids": ["5678c52f-fe8b-4db4-84d1-b7ce2d976d0b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.06211394816637039, "Height": 0.009940003976225853, "Left": 0.8642380833625793, "Top": 0.2710636556148529}, "Polygon": [{"X": 0.8642382025718689, "Y": 0.2710636854171753}, {"X": 0.9263520240783691, "Y": 0.2710636556148529}, {"X": 0.9263519048690796, "Y": 0.2810036540031433}, {"X": 0.8642380833625793, "Y": 0.2810036540031433}]}, "Id": "a03eafcb-226f-4186-95b3-abd00cfe1932", "Relationships": [{"Type": "CHILD", "Ids": ["03fc9920-f8ba-42ed-b230-7867276f185e"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "(2) <PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.07256848365068436, "Height": 0.01219338271766901, "Left": 0.07467116415500641, "Top": 0.29290807247161865}, "Polygon": [{"X": 0.07467116415500641, "Y": 0.29290810227394104}, {"X": 0.1472395956516266, "Y": 0.29290807247161865}, {"X": 0.14723965525627136, "Y": 0.3051014244556427}, {"X": 0.07467123866081238, "Y": 0.3051014542579651}]}, "Id": "6dd8726b-38f1-4319-971f-5ce611620fcc", "Relationships": [{"Type": "CHILD", "Ids": ["c3c87e83-f867-4453-bd67-aa64b14e83bd", "d72e5a52-8ce1-48c9-8687-54461d0fb75b"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "$12.00", "Geometry": {"BoundingBox": {"Width": 0.04903696849942207, "Height": 0.011365290731191635, "Left": 0.8768056035041809, "Top": 0.29247599840164185}, "Polygon": [{"X": 0.8768057823181152, "Y": 0.29247602820396423}, {"X": 0.9258425831794739, "Y": 0.29247599840164185}, {"X": 0.9258424043655396, "Y": 0.3038412630558014}, {"X": 0.8768056035041809, "Y": 0.3038412928581238}]}, "Id": "45929990-e53c-4980-9fa3-0ddcb2fd77ef", "Relationships": [{"Type": "CHILD", "Ids": ["67543b84-d7ff-43e1-9b19-e949bb417e04"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Comments", "Geometry": {"BoundingBox": {"Width": 0.08402799069881439, "Height": 0.009740062057971954, "Left": 0.07404574006795883, "Top": 0.337019681930542}, "Polygon": [{"X": 0.07404574006795883, "Y": 0.3370197117328644}, {"X": 0.15807369351387024, "Y": 0.337019681930542}, {"X": 0.15807372331619263, "Y": 0.3467596769332886}, {"X": 0.07404579222202301, "Y": 0.34675973653793335}]}, "Id": "cc5c31db-8ff7-4e23-a38a-93ae297524e5", "Relationships": [{"Type": "CHILD", "Ids": ["7dd6d6e7-28c2-473b-935b-3bd34cbb8233"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Receipt Details", "Geometry": {"BoundingBox": {"Width": 0.11616511642932892, "Height": 0.012286282144486904, "Left": 0.07481163740158081, "Top": 0.40259718894958496}, "Polygon": [{"X": 0.07481163740158081, "Y": 0.4025973081588745}, {"X": 0.19097672402858734, "Y": 0.40259718894958496}, {"X": 0.19097675383090973, "Y": 0.4148833453655243}, {"X": 0.07481170445680618, "Y": 0.41488346457481384}]}, "Id": "e1c06c25-c8c6-43e2-ac21-74f37791aa75", "Relationships": [{"Type": "CHILD", "Ids": ["b03f3f86-8c86-4b5a-bdf7-3550d7f646f8", "c4c900b3-186f-47eb-ae83-f951c9c224e9"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Payment Method", "Geometry": {"BoundingBox": {"Width": 0.12216624617576599, "Height": 0.012087738141417503, "Left": 0.0750582292675972, "Top": 0.****************}, "Polygon": [{"X": 0.0750582292675972, "Y": 0.****************}, {"X": 0.1972244381904602, "Y": 0.****************}, {"X": 0.1972244828939438, "Y": 0.****************}, {"X": 0.07505829632282257, "Y": 0.****************}]}, "Id": "70e2b5c6-4871-4e51-a194-92c2e1e3fa3a", "Relationships": [{"Type": "CHILD", "Ids": ["2cee148a-afd4-4d5b-bca7-fe0cd93e9607", "044e891e-bca3-4d49-ab2e-d22214cadf12"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "visa", "Geometry": {"BoundingBox": {"Width": 0.02905770018696785, "Height": 0.009342947043478489, "Left": 0.8966723084449768, "Top": 0.*****************}, "Polygon": [{"X": 0.8966724276542664, "Y": 0.*****************}, {"X": 0.9257299900054932, "Y": 0.*****************}, {"X": 0.9257298707962036, "Y": 0.****************}, {"X": 0.8966723084449768, "Y": 0.*****************}]}, "Id": "f48c95f7-d838-46b3-8b00-09d24ad7f8a9", "Relationships": [{"Type": "CHILD", "Ids": ["a5dc0b1f-ffaf-43f2-b744-c0aae8cacbfa"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "BOL Number", "Geometry": {"BoundingBox": {"Width": 0.0949360802769661, "Height": 0.009511062875390053, "Left": 0.07479853928089142, "Top": 0.*****************}, "Polygon": [{"X": 0.07479853928089142, "Y": 0.****************}, {"X": 0.16973458230495453, "Y": 0.*****************}, {"X": 0.16973461210727692, "Y": 0.****************}, {"X": 0.0747985914349556, "Y": 0.*****************}]}, "Id": "00d80760-bb06-473e-82f3-cff79695a7e4", "Relationships": [{"Type": "CHILD", "Ids": ["72f80860-c23a-4a32-86d0-13f1f1efdc01", "be28410f-d520-46fb-a3a9-d703e4c56d34"]}]}, {"BlockType": "LINE", "Confidence": 99.39373016357422, "Text": "PO #", "Geometry": {"BoundingBox": {"Width": 0.03655201569199562, "Height": 0.009729031473398209, "Left": 0.0750604122877121, "Top": 0.46861734986305237}, "Polygon": [{"X": 0.0750604122877121, "Y": 0.46861740946769714}, {"X": 0.11161237955093384, "Y": 0.46861734986305237}, {"X": 0.11161242425441742, "Y": 0.4783463478088379}, {"X": 0.07506046444177628, "Y": 0.4783463776111603}]}, "Id": "85a53ca9-8cab-4ed9-9fcb-9362e2a91bac", "Relationships": [{"Type": "CHILD", "Ids": ["d02e5c6c-58fd-4a6d-b795-c0caf68b1906", "e0f5cd70-dbd6-457b-b3a8-de7179cdc714"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1219697", "Geometry": {"BoundingBox": {"Width": 0.06248772516846657, "Height": 0.009520740248262882, "Left": 0.8633800148963928, "Top": 0.4688837230205536}, "Polygon": [{"X": 0.8633801341056824, "Y": 0.46888381242752075}, {"X": 0.9258677363395691, "Y": 0.4688837230205536}, {"X": 0.9258676171302795, "Y": 0.47840437293052673}, {"X": 0.8633800148963928, "Y": 0.4784044623374939}]}, "Id": "886761fe-7124-42ac-b10e-833e5a6fcf8e", "Relationships": [{"Type": "CHILD", "Ids": ["1e1aa121-0b0f-4e49-985f-8aabe2c64149"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Driver Name", "Geometry": {"BoundingBox": {"Width": 0.09061087667942047, "Height": 0.009436044842004776, "Left": 0.07485846430063248, "Top": 0.490667462348938}, "Polygon": [{"X": 0.07485846430063248, "Y": 0.49066758155822754}, {"X": 0.16546930372714996, "Y": 0.490667462348938}, {"X": 0.16546934843063354, "Y": 0.5001033544540405}, {"X": 0.07485851645469666, "Y": 0.5001034736633301}]}, "Id": "87b48190-9b38-41fc-8357-4486695b4646", "Relationships": [{"Type": "CHILD", "Ids": ["834845ff-692e-4efa-8b03-d01b80870f80", "8c7c9b97-df0f-4caf-a1c0-f85bcd0afc33"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.05696062371134758, "Height": 0.009542347863316536, "Left": 0.07376940548419952, "Top": 0.5126492977142334}, "Polygon": [{"X": 0.07376940548419952, "Y": 0.512649416923523}, {"X": 0.13072998821735382, "Y": 0.5126492977142334}, {"X": 0.1307300329208374, "Y": 0.52219158411026}, {"X": 0.0737694576382637, "Y": 0.5221916437149048}]}, "Id": "5196bcf7-423f-4b43-a483-5fc71c8ca134", "Relationships": [{"Type": "CHILD", "Ids": ["547eda2e-ae11-46fb-85e9-96039439a5f2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$24.00", "Geometry": {"BoundingBox": {"Width": 0.04926770552992821, "Height": 0.011416771449148655, "Left": 0.8766346573829651, "Top": 0.511880099773407}, "Polygon": [{"X": 0.8766348361968994, "Y": 0.5118801593780518}, {"X": 0.9259023666381836, "Y": 0.511880099773407}, {"X": 0.9259021878242493, "Y": 0.5232967734336853}, {"X": 0.8766346573829651, "Y": 0.5232968926429749}]}, "Id": "49084200-17fb-4813-ac48-b7d6160f4d57", "Relationships": [{"Type": "CHILD", "Ids": ["65aca72f-ef1c-49dc-8a47-99c52ca3d018"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Convenience Fee", "Geometry": {"BoundingBox": {"Width": 0.12750613689422607, "Height": 0.009750516153872013, "Left": 0.07437697798013687, "Top": 0.5344655513763428}, "Polygon": [{"X": 0.07437697798013687, "Y": 0.5344657897949219}, {"X": 0.20188307762145996, "Y": 0.5344655513763428}, {"X": 0.20188310742378235, "Y": 0.5442157983779907}, {"X": 0.07437703013420105, "Y": 0.5442160367965698}]}, "Id": "8cf5e8bf-fcd6-4628-ae19-8b2f39476e78", "Relationships": [{"Type": "CHILD", "Ids": ["506022b6-859b-472e-b68e-b96bdedc55d0", "58a32178-0459-4fe9-9773-ac43b5fb990f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$0.84", "Geometry": {"BoundingBox": {"Width": 0.04088054969906807, "Height": 0.01144512090831995, "Left": 0.8854340314865112, "Top": 0.5337560772895813}, "Polygon": [{"X": 0.8854342103004456, "Y": 0.5337561964988708}, {"X": 0.9263145923614502, "Y": 0.5337560772895813}, {"X": 0.9263144135475159, "Y": 0.5452011227607727}, {"X": 0.8854340314865112, "Y": 0.5452012419700623}]}, "Id": "bd4ca3d0-7074-4a4e-b08d-d57032acde61", "Relationships": [{"Type": "CHILD", "Ids": ["69e314ca-564f-4359-864a-ec65e7704d7b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Grand Total:", "Geometry": {"BoundingBox": {"Width": 0.0956936776638031, "Height": 0.009706220589578152, "Left": 0.6858007311820984, "Top": 0.5783768892288208}, "Polygon": [{"X": 0.6858007907867432, "Y": 0.5783770680427551}, {"X": 0.7814943790435791, "Y": 0.5783768892288208}, {"X": 0.7814942598342896, "Y": 0.5880828499794006}, {"X": 0.6858007311820984, "Y": 0.5880830883979797}]}, "Id": "caf2139b-96fc-45ec-8638-256c20b4d698", "Relationships": [{"Type": "CHILD", "Ids": ["8ac19a7f-0837-4ec4-8891-1593497e0fea", "143c1882-0c08-4648-a889-1dfce90a4175"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$24.84", "Geometry": {"BoundingBox": {"Width": 0.05040359124541283, "Height": 0.0115444865077734, "Left": 0.8766169548034668, "Top": 0.5777134895324707}, "Polygon": [{"X": 0.8766170740127563, "Y": 0.5777136087417603}, {"X": 0.9270204901695251, "Y": 0.5777134895324707}, {"X": 0.9270203709602356, "Y": 0.5892578363418579}, {"X": 0.8766169548034668, "Y": 0.5892579555511475}]}, "Id": "ed8cf8f6-990f-4dcd-a601-146063d9da8e", "Relationships": [{"Type": "CHILD", "Ids": ["79d2df02-9823-4976-9756-412254a3478f"]}]}, {"BlockType": "WORD", "Confidence": 98.3177261352539, "Text": "Lineage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12350434064865112, "Height": 0.024207163602113724, "Left": 0.13369432091712952, "Top": 0.04372582957148552}, "Polygon": [{"X": 0.13369432091712952, "Y": 0.04372582957148552}, {"X": 0.25719863176345825, "Y": 0.04372597858309746}, {"X": 0.25719866156578064, "Y": 0.06793299317359924}, {"X": 0.13369442522525787, "Y": 0.0679328590631485}]}, "Id": "54b27f2f-553d-4183-9dbd-0c1cda69d063"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "Wilmington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06829316914081573, "Height": 0.009798347018659115, "Left": 0.360679030418396, "Top": 0.047026779502630234}, "Polygon": [{"X": 0.3606790602207184, "Y": 0.047026779502630234}, {"X": 0.4289722144603729, "Y": 0.0470268614590168}, {"X": 0.42897218465805054, "Y": 0.05682512745261192}, {"X": 0.360679030418396, "Y": 0.056825049221515656}]}, "Id": "35bbb71f-95f2-480d-97b2-c6857276b3eb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01052111480385065, "Height": 0.00770201301202178, "Left": 0.43348658084869385, "Top": 0.04714304208755493}, "Polygon": [{"X": 0.43348661065101624, "Y": 0.04714304208755493}, {"X": 0.4440076947212219, "Y": 0.047143056988716125}, {"X": 0.44400766491889954, "Y": 0.05484505742788315}, {"X": 0.43348658084869385, "Y": 0.054845042526721954}]}, "Id": "28dfc9e2-4b26-4641-b84c-5e65b494a1ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032764941453933716, "Height": 0.009805254638195038, "Left": 0.8236644864082336, "Top": 0.04705316573381424}, "Polygon": [{"X": 0.8236646056175232, "Y": 0.04705316573381424}, {"X": 0.8564294576644897, "Y": 0.04705320671200752}, {"X": 0.8564293384552002, "Y": 0.05685842037200928}, {"X": 0.8236644864082336, "Y": 0.05685838311910629}]}, "Id": "4928043a-19d4-4786-ae70-9cbd44b5d263"}, {"BlockType": "WORD", "Confidence": 99.6534194946289, "Text": "Sale#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0331725999712944, "Height": 0.008017143234610558, "Left": 0.8609510660171509, "Top": 0.04695715755224228}, "Polygon": [{"X": 0.8609511852264404, "Y": 0.04695715755224228}, {"X": 0.8941236734390259, "Y": 0.04695719853043556}, {"X": 0.8941235542297363, "Y": 0.05497429892420769}, {"X": 0.8609510660171509, "Y": 0.0549742616713047}]}, "Id": "45b56a79-b8cf-44cf-bf3d-ba2ba0552e12"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "8000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028500782325863838, "Height": 0.007637352216988802, "Left": 0.8975400924682617, "Top": 0.04723707213997841}, "Polygon": [{"X": 0.8975401520729065, "Y": 0.04723707213997841}, {"X": 0.9260408282279968, "Y": 0.047237105667591095}, {"X": 0.9260407090187073, "Y": 0.05487442389130592}, {"X": 0.8975400924682617, "Y": 0.05487439036369324}]}, "Id": "36d56577-5ade-4efa-8be5-ec5c18d5d161"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "29700", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036398790776729584, "Height": 0.007756699342280626, "Left": 0.36122891306877136, "Top": 0.0604214072227478}, "Polygon": [{"X": 0.36122894287109375, "Y": 0.0604214072227478}, {"X": 0.39762771129608154, "Y": 0.060421448200941086}, {"X": 0.39762771129608154, "Y": 0.06817810237407684}, {"X": 0.36122891306877136, "Y": 0.06817806512117386}]}, "Id": "eabac441-f918-4ea9-87e6-12613353a842"}, {"BlockType": "WORD", "Confidence": 98.80660247802734, "Text": "S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007898319512605667, "Height": 0.007833344861865044, "Left": 0.4016684591770172, "Top": 0.06017550453543663}, "Polygon": [{"X": 0.4016684889793396, "Y": 0.06017550453543663}, {"X": 0.4095667898654938, "Y": 0.06017551198601723}, {"X": 0.4095667600631714, "Y": 0.06800884753465652}, {"X": 0.4016684591770172, "Y": 0.06800884008407593}]}, "Id": "a8f174fd-8f8b-4aa8-bb1d-a056f88c0dae"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "Graaskamp", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06773955374956131, "Height": 0.009654410183429718, "Left": 0.4135873317718506, "Top": 0.06023579090833664}, "Polygon": [{"X": 0.413587361574173, "Y": 0.06023579090833664}, {"X": 0.4813268780708313, "Y": 0.06023586541414261}, {"X": 0.4813268482685089, "Y": 0.06989020109176636}, {"X": 0.4135873317718506, "Y": 0.06989012658596039}]}, "Id": "3b144cf1-405d-4d0f-8888-f9397f147ab3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Blvd,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027729015797376633, "Height": 0.008902361616492271, "Left": 0.4854487180709839, "Top": 0.0603083036839962}, "Polygon": [{"X": 0.4854487478733063, "Y": 0.0603083036839962}, {"X": 0.513177752494812, "Y": 0.06030833348631859}, {"X": 0.5131776928901672, "Y": 0.06921066343784332}, {"X": 0.4854487180709839, "Y": 0.06921063363552094}]}, "Id": "e79add0d-7d98-4c56-923e-38c47682e88d"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Wilmington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06818483769893646, "Height": 0.009772198274731636, "Left": 0.5171667337417603, "Top": 0.060221266001462936}, "Polygon": [{"X": 0.517166793346405, "Y": 0.060221266001462936}, {"X": 0.5853515863418579, "Y": 0.060221340507268906}, {"X": 0.5853515267372131, "Y": 0.06999346613883972}, {"X": 0.5171667337417603, "Y": 0.06999339163303375}]}, "Id": "0964e46f-6d2c-4834-8175-9f1ce1a985ef"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0108263548463583, "Height": 0.007676010485738516, "Left": 0.5898141860961914, "Top": 0.06037398427724838}, "Polygon": [{"X": 0.5898142457008362, "Y": 0.06037398427724838}, {"X": 0.6006405353546143, "Y": 0.06037399545311928}, {"X": 0.6006404757499695, "Y": 0.06804999709129333}, {"X": 0.5898141860961914, "Y": 0.06804998219013214}]}, "Id": "030f0c14-84d4-422c-a695-7ecb7b0ea83c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "60481", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034400247037410736, "Height": 0.007673470303416252, "Left": 0.6039413213729858, "Top": 0.060460224747657776}, "Polygon": [{"X": 0.6039413809776306, "Y": 0.060460224747657776}, {"X": 0.6383415460586548, "Y": 0.06046026572585106}, {"X": 0.63834148645401, "Y": 0.06813369691371918}, {"X": 0.6039413213729858, "Y": 0.06813365966081619}]}, "Id": "2657f0a0-8eb4-41da-aeeb-24ec111b7319"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Sep", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02282590977847576, "Height": 0.009704027324914932, "Left": 0.7593706250190735, "Top": 0.06021388992667198}, "Polygon": [{"X": 0.759370744228363, "Y": 0.06021388992667198}, {"X": 0.782196581363678, "Y": 0.06021391600370407}, {"X": 0.7821964621543884, "Y": 0.06991791725158691}, {"X": 0.7593706250190735, "Y": 0.06991789489984512}]}, "Id": "4acec91a-a894-473f-b29d-b10c79564574"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "19,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01679803431034088, "Height": 0.008823910728096962, "Left": 0.7868016362190247, "Top": 0.06048886850476265}, "Polygon": [{"X": 0.7868017554283142, "Y": 0.06048886850476265}, {"X": 0.8035996556282043, "Y": 0.06048888713121414}, {"X": 0.8035995364189148, "Y": 0.06931278109550476}, {"X": 0.7868016362190247, "Y": 0.06931276619434357}]}, "Id": "01caadf9-4441-40c3-b168-15fc0e03a1af"}, {"BlockType": "WORD", "Confidence": 99.51351165771484, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028788041323423386, "Height": 0.007452189456671476, "Left": 0.8079750537872314, "Top": 0.06054725497961044}, "Polygon": [{"X": 0.807975172996521, "Y": 0.06054725497961044}, {"X": 0.8367631435394287, "Y": 0.06054728478193283}, {"X": 0.8367630243301392, "Y": 0.0679994449019432}, {"X": 0.8079750537872314, "Y": 0.06799941509962082}]}, "Id": "5d9483d4-2f44-4533-9c78-c8919f99af91"}, {"BlockType": "WORD", "Confidence": 99.7265625, "Text": "20:03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03240179270505905, "Height": 0.0076025440357625484, "Left": 0.840553879737854, "Top": 0.060493484139442444}, "Polygon": [{"X": 0.8405539393424988, "Y": 0.060493484139442444}, {"X": 0.8729556798934937, "Y": 0.06049351766705513}, {"X": 0.8729555606842041, "Y": 0.06809602677822113}, {"X": 0.840553879737854, "Y": 0.06809598952531815}]}, "Id": "d0cd7527-0ea8-4fbb-94fa-112492fc5ef0"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018406454473733902, "Height": 0.007605127990245819, "Left": 0.8776912689208984, "Top": 0.060367319732904434}, "Polygon": [{"X": 0.877691388130188, "Y": 0.060367319732904434}, {"X": 0.896097719669342, "Y": 0.060367338359355927}, {"X": 0.8960976004600525, "Y": 0.06797244399785995}, {"X": 0.8776912689208984, "Y": 0.06797242909669876}]}, "Id": "26eb244e-081e-4750-914e-b4e21117ecd0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "EDT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025204066187143326, "Height": 0.007725039031356573, "Left": 0.9008724093437195, "Top": 0.060295164585113525}, "Polygon": [{"X": 0.9008724689483643, "Y": 0.060295164585113525}, {"X": 0.9260764718055725, "Y": 0.06029519438743591}, {"X": 0.926076352596283, "Y": 0.06802020221948624}, {"X": 0.9008724093437195, "Y": 0.06802017986774445}]}, "Id": "d33cb1d2-c858-47a3-b3dd-85877cb2fe88"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Paid", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033511966466903687, "Height": 0.009832463227212429, "Left": 0.07476148754358292, "Top": 0.1434316784143448}, "Polygon": [{"X": 0.07476148754358292, "Y": 0.1434316784143448}, {"X": 0.10827340185642242, "Y": 0.14343169331550598}, {"X": 0.1082734540104866, "Y": 0.1532641500234604}, {"X": 0.0747615396976471, "Y": 0.153264120221138}]}, "Id": "95ccdf93-ca90-4a3c-aa50-af6d128b3f44"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "By:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024495277553796768, "Height": 0.01208855677396059, "Left": 0.11400526762008667, "Top": 0.14359232783317566}, "Polygon": [{"X": 0.11400526762008667, "Y": 0.14359232783317566}, {"X": 0.13850049674510956, "Y": 0.14359234273433685}, {"X": 0.13850054144859314, "Y": 0.15568087995052338}, {"X": 0.11400532722473145, "Y": 0.15568086504936218}]}, "Id": "37fce55a-64e8-4be2-9457-6db0b77dfea1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07202892005443573, "Height": 0.00970015674829483, "Left": 0.5055063366889954, "Top": 0.14356237649917603}, "Polygon": [{"X": 0.5055063962936401, "Y": 0.14356237649917603}, {"X": 0.5775352716445923, "Y": 0.1435624212026596}, {"X": 0.5775352120399475, "Y": 0.15326254069805145}, {"X": 0.5055063366889954, "Y": 0.15326249599456787}]}, "Id": "682bc9b7-fdd1-47a8-9b92-c23edcbef4b6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "GREEN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05748232454061508, "Height": 0.009749753400683403, "Left": 0.07434289902448654, "Top": 0.1680653691291809}, "Polygon": [{"X": 0.07434289902448654, "Y": 0.1680653691291809}, {"X": 0.13182517886161804, "Y": 0.1680653989315033}, {"X": 0.13182522356510162, "Y": 0.17781512439250946}, {"X": 0.07434295117855072, "Y": 0.17781510949134827}]}, "Id": "42aebf60-dc91-4079-846d-c0f709127c66"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ROAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0461406446993351, "Height": 0.00977368000894785, "Left": 0.137818843126297, "Top": 0.1680922955274582}, "Polygon": [{"X": 0.137818843126297, "Y": 0.1680922955274582}, {"X": 0.1839594542980194, "Y": 0.16809232532978058}, {"X": 0.1839594841003418, "Y": 0.17786598205566406}, {"X": 0.13781888782978058, "Y": 0.17786596715450287}]}, "Id": "9163a395-ab4d-4ed6-aaa7-8e9c18a12558"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Wilmington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08441755175590515, "Height": 0.01223001815378666, "Left": 0.5047810673713684, "Top": 0.1680527776479721}, "Polygon": [{"X": 0.5047811269760132, "Y": 0.1680527776479721}, {"X": 0.589198648929596, "Y": 0.1680528074502945}, {"X": 0.5891985297203064, "Y": 0.18028278648853302}, {"X": 0.5047810673713684, "Y": 0.18028275668621063}]}, "Id": "afade3bb-8e15-43a5-a7cc-a3bc19b35ea4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013243281282484531, "Height": 0.009602793492376804, "Left": 0.595406711101532, "Top": 0.16819214820861816}, "Polygon": [{"X": 0.5954067707061768, "Y": 0.16819214820861816}, {"X": 0.6086500287055969, "Y": 0.16819216310977936}, {"X": 0.6086499691009521, "Y": 0.177794948220253}, {"X": 0.595406711101532, "Y": 0.1777949333190918}]}, "Id": "43a58cc0-e706-437e-ba3a-6bff8e19c6f6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "29700", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04544549807906151, "Height": 0.009427494369447231, "Left": 0.5048787593841553, "Top": 0.18594837188720703}, "Polygon": [{"X": 0.5048788189888, "Y": 0.18594837188720703}, {"X": 0.5503242611885071, "Y": 0.18594838678836823}, {"X": 0.5503242015838623, "Y": 0.19537585973739624}, {"X": 0.5048787593841553, "Y": 0.19537584483623505}]}, "Id": "d727ebf8-f08c-4594-93b6-12ca0015e2e6"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009782744571566582, "Height": 0.00970036443322897, "Left": 0.5553314685821533, "Top": 0.18560083210468292}, "Polygon": [{"X": 0.5553315281867981, "Y": 0.18560083210468292}, {"X": 0.5651142001152039, "Y": 0.18560083210468292}, {"X": 0.5651141405105591, "Y": 0.19530119001865387}, {"X": 0.5553314685821533, "Y": 0.19530119001865387}]}, "Id": "d74cc4c4-1a32-42e0-b2fd-117f6f7038cd"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Graaskamp", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0838465616106987, "Height": 0.012007055804133415, "Left": 0.5706950426101685, "Top": 0.18561981618404388}, "Polygon": [{"X": 0.5706951022148132, "Y": 0.18561981618404388}, {"X": 0.6545416116714478, "Y": 0.18561984598636627}, {"X": 0.6545414924621582, "Y": 0.19762687385082245}, {"X": 0.5706950426101685, "Y": 0.19762685894966125}]}, "Id": "8f8eae33-a52e-4b34-ac5a-a78e55f51a78"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Blvd,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034871745854616165, "Height": 0.011137755587697029, "Left": 0.6598144173622131, "Top": 0.18569983541965485}, "Polygon": [{"X": 0.6598144769668579, "Y": 0.18569983541965485}, {"X": 0.6946861743927002, "Y": 0.18569985032081604}, {"X": 0.6946860551834106, "Y": 0.19683758914470673}, {"X": 0.6598144173622131, "Y": 0.19683757424354553}]}, "Id": "c186f185-c9cd-456a-a955-dfbfdc8996de"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Wilmington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08450838923454285, "Height": 0.01212989166378975, "Left": 0.6999197602272034, "Top": 0.18568415939807892}, "Polygon": [{"X": 0.6999198794364929, "Y": 0.18568415939807892}, {"X": 0.7844281196594238, "Y": 0.1856841742992401}, {"X": 0.7844280004501343, "Y": 0.19781404733657837}, {"X": 0.6999197602272034, "Y": 0.19781403243541718}]}, "Id": "7056e053-b63c-40b0-977b-8d1de720814d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012806839309632778, "Height": 0.009464570321142673, "Left": 0.7909278869628906, "Top": 0.1858268529176712}, "Polygon": [{"X": 0.7909280061721802, "Y": 0.1858268529176712}, {"X": 0.8037347197532654, "Y": 0.1858268529176712}, {"X": 0.8037346601486206, "Y": 0.1952914297580719}, {"X": 0.7909278869628906, "Y": 0.1952914148569107}]}, "Id": "113c6162-7c12-42ce-8494-4c068c325e1f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "60481", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042761996388435364, "Height": 0.009472331032156944, "Left": 0.8082255125045776, "Top": 0.18593429028987885}, "Polygon": [{"X": 0.8082256317138672, "Y": 0.18593429028987885}, {"X": 0.8509875535964966, "Y": 0.18593430519104004}, {"X": 0.850987434387207, "Y": 0.19540663063526154}, {"X": 0.8082255125045776, "Y": 0.19540661573410034}]}, "Id": "c666e158-aa42-4721-a1d8-c79120468cc9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Services", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06653530895709991, "Height": 0.00978798232972622, "Left": 0.07440520823001862, "Top": 0.27109846472740173}, "Polygon": [{"X": 0.07440520823001862, "Y": 0.27109846472740173}, {"X": 0.14094047248363495, "Y": 0.27109846472740173}, {"X": 0.14094051718711853, "Y": 0.2808864116668701}, {"X": 0.0744052603840828, "Y": 0.2808864414691925}]}, "Id": "5678c52f-fe8b-4db4-84d1-b7ce2d976d0b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06211394816637039, "Height": 0.009940003976225853, "Left": 0.8642380833625793, "Top": 0.2710636556148529}, "Polygon": [{"X": 0.8642382025718689, "Y": 0.2710636854171753}, {"X": 0.9263520240783691, "Y": 0.2710636556148529}, {"X": 0.9263519048690796, "Y": 0.2810036540031433}, {"X": 0.8642380833625793, "Y": 0.2810036540031433}]}, "Id": "03fc9920-f8ba-42ed-b230-7867276f185e"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "(2)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01861303299665451, "Height": 0.012193363159894943, "Left": 0.07467116415500641, "Top": 0.29290807247161865}, "Polygon": [{"X": 0.07467116415500641, "Y": 0.29290810227394104}, {"X": 0.09328413754701614, "Y": 0.29290807247161865}, {"X": 0.09328419715166092, "Y": 0.3051014542579651}, {"X": 0.07467123866081238, "Y": 0.3051014542579651}]}, "Id": "c3c87e83-f867-4453-bd67-aa64b14e83bd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04778190329670906, "Height": 0.00980061199516058, "Left": 0.0994577407836914, "Top": 0.2930448651313782}, "Polygon": [{"X": 0.0994577407836914, "Y": 0.29304489493370056}, {"X": 0.1472395956516266, "Y": 0.2930448651313782}, {"X": 0.14723964035511017, "Y": 0.30284547805786133}, {"X": 0.09945778548717499, "Y": 0.30284547805786133}]}, "Id": "d72e5a52-8ce1-48c9-8687-54461d0fb75b"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "$12.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04903696849942207, "Height": 0.011365290731191635, "Left": 0.8768056035041809, "Top": 0.29247599840164185}, "Polygon": [{"X": 0.8768057823181152, "Y": 0.29247602820396423}, {"X": 0.9258425831794739, "Y": 0.29247599840164185}, {"X": 0.9258424043655396, "Y": 0.3038412630558014}, {"X": 0.8768056035041809, "Y": 0.3038412928581238}]}, "Id": "67543b84-d7ff-43e1-9b19-e949bb417e04"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Comments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08402799069881439, "Height": 0.009740062057971954, "Left": 0.07404574006795883, "Top": 0.337019681930542}, "Polygon": [{"X": 0.07404574006795883, "Y": 0.3370197117328644}, {"X": 0.15807369351387024, "Y": 0.337019681930542}, {"X": 0.15807372331619263, "Y": 0.3467596769332886}, {"X": 0.07404579222202301, "Y": 0.34675973653793335}]}, "Id": "7dd6d6e7-28c2-473b-935b-3bd34cbb8233"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05829281732439995, "Height": 0.012286221608519554, "Left": 0.07481163740158081, "Top": 0.40259724855422974}, "Polygon": [{"X": 0.07481163740158081, "Y": 0.4025973081588745}, {"X": 0.13310439884662628, "Y": 0.40259724855422974}, {"X": 0.13310445845127106, "Y": 0.41488340497016907}, {"X": 0.07481170445680618, "Y": 0.41488346457481384}]}, "Id": "b03f3f86-8c86-4b5a-bdf7-3550d7f646f8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Details", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0525863915681839, "Height": 0.00976833887398243, "Left": 0.13839036226272583, "Top": 0.4027988314628601}, "Polygon": [{"X": 0.13839036226272583, "Y": 0.4027988910675049}, {"X": 0.19097672402858734, "Y": 0.4027988314628601}, {"X": 0.19097673892974854, "Y": 0.4125671088695526}, {"X": 0.13839039206504822, "Y": 0.4125671684741974}]}, "Id": "c4c900b3-186f-47eb-ae83-f951c9c224e9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06383348256349564, "Height": 0.01181495189666748, "Left": 0.0750582292675972, "Top": 0.42500028014183044}, "Polygon": [{"X": 0.0750582292675972, "Y": 0.4250003397464752}, {"X": 0.13889166712760925, "Y": 0.42500028014183044}, {"X": 0.13889171183109283, "Y": 0.43681514263153076}, {"X": 0.07505829632282257, "Y": 0.****************}]}, "Id": "2cee148a-afd4-4d5b-bca7-fe0cd93e9607"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Method", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053554583340883255, "Height": 0.009655301459133625, "Left": 0.14366988837718964, "Top": 0.****************}, "Polygon": [{"X": 0.14366988837718964, "Y": 0.*****************}, {"X": 0.1972244381904602, "Y": 0.****************}, {"X": 0.1972244679927826, "Y": 0.*****************}, {"X": 0.14366991817951202, "Y": 0.****************}]}, "Id": "044e891e-bca3-4d49-ab2e-d22214cadf12"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "visa", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02905770018696785, "Height": 0.009342947043478489, "Left": 0.8966723084449768, "Top": 0.*****************}, "Polygon": [{"X": 0.8966724276542664, "Y": 0.*****************}, {"X": 0.9257299900054932, "Y": 0.*****************}, {"X": 0.9257298707962036, "Y": 0.****************}, {"X": 0.8966723084449768, "Y": 0.*****************}]}, "Id": "a5dc0b1f-ffaf-43f2-b744-c0aae8cacbfa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BOL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032119281589984894, "Height": 0.00947080459445715, "Left": 0.07479853928089142, "Top": 0.****************}, "Polygon": [{"X": 0.07479853928089142, "Y": 0.****************}, {"X": 0.10691777616739273, "Y": 0.****************}, {"X": 0.10691782087087631, "Y": 0.*****************}, {"X": 0.0747985914349556, "Y": 0.*****************}]}, "Id": "72f80860-c23a-4a32-86d0-13f1f1efdc01"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057507965713739395, "Height": 0.009438557550311089, "Left": 0.11222665011882782, "Top": 0.44684118032455444}, "Polygon": [{"X": 0.11222665011882782, "Y": 0.4468412697315216}, {"X": 0.16973458230495453, "Y": 0.44684118032455444}, {"X": 0.16973461210727692, "Y": 0.****************}, {"X": 0.1122266948223114, "Y": 0.4562797546386719}]}, "Id": "be28410f-d520-46fb-a3a9-d703e4c56d34"}, {"BlockType": "WORD", "Confidence": 99.55974578857422, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022653283551335335, "Height": 0.009729010984301567, "Left": 0.0750604122877121, "Top": 0.46861737966537476}, "Polygon": [{"X": 0.0750604122877121, "Y": 0.46861740946769714}, {"X": 0.0977136492729187, "Y": 0.46861737966537476}, {"X": 0.09771369397640228, "Y": 0.4783463478088379}, {"X": 0.07506046444177628, "Y": 0.4783463776111603}]}, "Id": "d02e5c6c-58fd-4a6d-b795-c0caf68b1906"}, {"BlockType": "WORD", "Confidence": 99.22772216796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009555385448038578, "Height": 0.009167845360934734, "Left": 0.10205703973770142, "Top": 0.46893879771232605}, "Polygon": [{"X": 0.10205703973770142, "Y": 0.46893882751464844}, {"X": 0.11161237955093384, "Y": 0.46893879771232605}, {"X": 0.11161242425441742, "Y": 0.47810661792755127}, {"X": 0.102057084441185, "Y": 0.47810664772987366}]}, "Id": "e0f5cd70-dbd6-457b-b3a8-de7179cdc714"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1219697", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06248772516846657, "Height": 0.009520740248262882, "Left": 0.8633800148963928, "Top": 0.4688837230205536}, "Polygon": [{"X": 0.8633801341056824, "Y": 0.46888381242752075}, {"X": 0.9258677363395691, "Y": 0.4688837230205536}, {"X": 0.9258676171302795, "Y": 0.47840437293052673}, {"X": 0.8633800148963928, "Y": 0.4784044623374939}]}, "Id": "1e1aa121-0b0f-4e49-985f-8aabe2c64149"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04322545975446701, "Height": 0.009408249519765377, "Left": 0.07485846430063248, "Top": 0.49066752195358276}, "Polygon": [{"X": 0.07485846430063248, "Y": 0.49066758155822754}, {"X": 0.1180838793516159, "Y": 0.49066752195358276}, {"X": 0.11808392405509949, "Y": 0.5000756978988647}, {"X": 0.07485851645469666, "Y": 0.5000757575035095}]}, "Id": "834845ff-692e-4efa-8b03-d01b80870f80"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04245607554912567, "Height": 0.009297734126448631, "Left": 0.12301326543092728, "Top": 0.4908056855201721}, "Polygon": [{"X": 0.12301326543092728, "Y": 0.4908057451248169}, {"X": 0.16546931862831116, "Y": 0.4908056855201721}, {"X": 0.16546934843063354, "Y": 0.5001033544540405}, {"X": 0.12301331013441086, "Y": 0.5001034140586853}]}, "Id": "8c7c9b97-df0f-4caf-a1c0-f85bcd0afc33"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05696062371134758, "Height": 0.009542347863316536, "Left": 0.07376940548419952, "Top": 0.5126492977142334}, "Polygon": [{"X": 0.07376940548419952, "Y": 0.512649416923523}, {"X": 0.13072998821735382, "Y": 0.5126492977142334}, {"X": 0.1307300329208374, "Y": 0.52219158411026}, {"X": 0.0737694576382637, "Y": 0.5221916437149048}]}, "Id": "547eda2e-ae11-46fb-85e9-96039439a5f2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$24.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04926770552992821, "Height": 0.011416771449148655, "Left": 0.8766346573829651, "Top": 0.511880099773407}, "Polygon": [{"X": 0.8766348361968994, "Y": 0.5118801593780518}, {"X": 0.9259023666381836, "Y": 0.511880099773407}, {"X": 0.9259021878242493, "Y": 0.5232967734336853}, {"X": 0.8766346573829651, "Y": 0.5232968926429749}]}, "Id": "65aca72f-ef1c-49dc-8a47-99c52ca3d018"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09471801668405533, "Height": 0.009652887471020222, "Left": 0.07437697798013687, "Top": 0.534468948841095}, "Polygon": [{"X": 0.07437697798013687, "Y": 0.5344691276550293}, {"X": 0.16909494996070862, "Y": 0.534468948841095}, {"X": 0.1690949946641922, "Y": 0.5441216230392456}, {"X": 0.07437703013420105, "Y": 0.5441218614578247}]}, "Id": "506022b6-859b-472e-b68e-b96bdedc55d0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026939403265714645, "Height": 0.009750320576131344, "Left": 0.1749437004327774, "Top": 0.5344655513763428}, "Polygon": [{"X": 0.1749437004327774, "Y": 0.5344656109809875}, {"X": 0.20188307762145996, "Y": 0.5344655513763428}, {"X": 0.20188310742378235, "Y": 0.5442157983779907}, {"X": 0.1749437302350998, "Y": 0.5442158579826355}]}, "Id": "58a32178-0459-4fe9-9773-ac43b5fb990f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$0.84", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04088054969906807, "Height": 0.01144512090831995, "Left": 0.8854340314865112, "Top": 0.5337560772895813}, "Polygon": [{"X": 0.8854342103004456, "Y": 0.5337561964988708}, {"X": 0.9263145923614502, "Y": 0.5337560772895813}, {"X": 0.9263144135475159, "Y": 0.5452011227607727}, {"X": 0.8854340314865112, "Y": 0.5452012419700623}]}, "Id": "69e314ca-564f-4359-864a-ec65e7704d7b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Grand", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04721277579665184, "Height": 0.009706116281449795, "Left": 0.6858007311820984, "Top": 0.5783769488334656}, "Polygon": [{"X": 0.6858007907867432, "Y": 0.5783770680427551}, {"X": 0.7330135107040405, "Y": 0.5783769488334656}, {"X": 0.733013391494751, "Y": 0.5880829691886902}, {"X": 0.6858007311820984, "Y": 0.5880830883979797}]}, "Id": "8ac19a7f-0837-4ec4-8891-1593497e0fea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043622612953186035, "Height": 0.009512153454124928, "Left": 0.7378717660903931, "Top": 0.5783904790878296}, "Polygon": [{"X": 0.7378718852996826, "Y": 0.5783905386924744}, {"X": 0.7814943790435791, "Y": 0.5783904790878296}, {"X": 0.7814942598342896, "Y": 0.5879024863243103}, {"X": 0.7378717660903931, "Y": 0.5879026055335999}]}, "Id": "143c1882-0c08-4648-a889-1dfce90a4175"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$24.84", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05040359124541283, "Height": 0.0115444865077734, "Left": 0.8766169548034668, "Top": 0.5777134895324707}, "Polygon": [{"X": 0.8766170740127563, "Y": 0.5777136087417603}, {"X": 0.9270204901695251, "Y": 0.5777134895324707}, {"X": 0.9270203709602356, "Y": 0.5892578363418579}, {"X": 0.8766169548034668, "Y": 0.5892579555511475}]}, "Id": "79d2df02-9823-4976-9756-412254a3478f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "9db4b88a-8409-49dd-94c0-7862f067a7e1", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "9db4b88a-8409-49dd-94c0-7862f067a7e1", "content-type": "application/x-amz-json-1.1", "content-length": "45731", "date": "Wed, 24 Sep 2025 13:26:48 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "6ff7d47f-291e-436c-858d-e6544963b35e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:51 GMT", "content-type": "application/json", "content-length": "983", "connection": "keep-alive", "x-amzn-requestid": "6ff7d47f-291e-436c-858d-e6544963b35e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. Need to determine doc_type. Text shows a receipt? It mentions \"BOL Number\" and \"PO #\", \"Paid By\", \"Payment Method visa\", \"Services\" with pallets, convenience fee, grand total. This looks like a \"lumper_receipt\" (invoice for loading/unloading labor). The description says lumper_receipt: keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. Here we have PO #, services, pallets, convenience fee, total. So lumper_receipt. Also could be an invoice but since it has PO and service charge, lumper_receipt. So output page 1 doc_type lumper_receipt."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"lumper_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2725, "outputTokens": 177, "totalTokens": 2902}, "metrics": {"latencyMs": 930}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/c17aaebc_MMNWUYGBLL1K5KSJBNOT.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nLineage\nWilmington, IL\nRelay Sale# 8000\n29700 S Graaskamp Blvd, Wilmington, IL 60481\nSep 19, 2025 20:03 PM EDT\nPaid By:\nLocation:\nGREEN ROAD\nWilmington, IL\n29700 S Graaskamp Blvd, Wilmington, IL 60481\nServices\nAmount\n(2) Pallets\n$12.00\nComments\nReceipt Details\nPayment Method\nvisa\nBOL Number\nPO #\n1219697\nDriver Name\nAmount\n$24.00\nConvenience Fee\n$0.84\nGrand Total:\n$24.84\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}