2025-09-24 15:11:39,222 - INFO - Logging initialized. Log file: logs/test_classification_20250924_151139.log
2025-09-24 15:11:39,224 - INFO - 📁 Found 2 files to process
2025-09-24 15:11:39,224 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:11:39,224 - INFO - 🚀 Processing 2 files in FORCED PARALLEL MODE...
2025-09-24 15:11:39,224 - INFO - 🚀 Creating 2 parallel tasks...
2025-09-24 15:11:39,224 - INFO - 🚀 All 2 tasks created - executing in parallel...
2025-09-24 15:11:39,224 - INFO - ⬆️ [15:11:39] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:42,342 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/04078ac6_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:42,343 - INFO - 🔍 [15:11:42] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:42,345 - INFO - ⬆️ [15:11:42] Uploading: DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:42,346 - INFO - Initializing TextractProcessor...
2025-09-24 15:11:42,383 - INFO - Initializing BedrockProcessor...
2025-09-24 15:11:42,390 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/04078ac6_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:42,390 - INFO - Processing PDF from S3...
2025-09-24 15:11:42,391 - INFO - Downloading PDF from S3 to /tmp/tmpp_42vtp3/04078ac6_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:43,024 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/64fe75dc_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:43,025 - INFO - 🔍 [15:11:43] Starting classification: DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:43,028 - INFO - Initializing TextractProcessor...
2025-09-24 15:11:43,078 - INFO - Initializing BedrockProcessor...
2025-09-24 15:11:43,084 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/64fe75dc_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:43,084 - INFO - Processing PDF from S3...
2025-09-24 15:11:43,084 - INFO - Downloading PDF from S3 to /tmp/tmpdm6ikley/64fe75dc_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:44,813 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:11:44,813 - INFO - Splitting PDF into individual pages...
2025-09-24 15:11:44,816 - INFO - Splitting PDF 04078ac6_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:11:44,821 - INFO - Split PDF into 1 pages
2025-09-24 15:11:44,822 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:11:44,822 - INFO - Expected pages: [1]
2025-09-24 15:11:44,931 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 15:11:44,932 - INFO - Splitting PDF into individual pages...
2025-09-24 15:11:44,935 - INFO - Splitting PDF 64fe75dc_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 15:11:44,939 - INFO - Split PDF into 1 pages
2025-09-24 15:11:44,939 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:11:44,939 - INFO - Expected pages: [1]
2025-09-24 15:11:49,343 - INFO - Page 1: Extracted 732 characters, 59 lines from 64fe75dc_DCY7SLNMWUXIENOREHQF_9426d14e_page_001.pdf
2025-09-24 15:11:49,344 - INFO - Successfully processed page 1
2025-09-24 15:11:49,344 - INFO - Combined 1 pages into final text
2025-09-24 15:11:49,344 - INFO - Text validation for 64fe75dc_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 15:11:49,345 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:11:49,345 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:11:51,887 - INFO - Page 1: Extracted 939 characters, 64 lines from 04078ac6_A34CDFDJ66EDOZEKZWJL_0e459e90_page_001.pdf
2025-09-24 15:11:51,887 - INFO - Successfully processed page 1
2025-09-24 15:11:51,888 - INFO - Combined 1 pages into final text
2025-09-24 15:11:51,888 - INFO - Text validation for 04078ac6_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:11:51,888 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:11:51,888 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:11:51,963 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6454954a-1761-45cb-b57d-6b9aafd42a11', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:41:51 GMT', 'content-type': 'application/json', 'content-length': '1561', 'connection': 'keep-alive', 'x-amzn-requestid': '6454954a-1761-45cb-b57d-6b9aafd42a11'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to examine keywords. This page contains "WEIGHT VALIDATION CERTIFICATE" and weight details. It is likely a "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert". Let\'s analyze.\n\nThe header: "WEIGHT VALIDATION CERTIFICATE". That\'s a weight validation certificate. There is no mention of NMFC code, inspection classification, etc. It includes weight, billed weight, actual weight, etc. The definition for weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". This seems to match. Also inspection_cert: "Inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned." So weight_and_inspection_cert is correct.\n\nAlso need to check nmfc_cert: only if there are keywords like "Inspected against original" or "Corrected against Actual" etc. Not present. So weight_and_inspection_cert.\n\nTherefore doc_type: "weight_and_inspection_cert". Page 1. Output JSON array with one object: {page_no:1, doc_type:"weight_and_inspection_cert"}.\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2718, 'outputTokens': 276, 'totalTokens': 2994}, 'metrics': {'latencyMs': 1236}}
2025-09-24 15:11:51,963 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/64fe75dc_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:52,225 - INFO - 

DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:11:52,225 - INFO - 

✓ Saved result: debug_output/run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 15:11:53,299 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/64fe75dc_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:54,054 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b09f69bb-ab58-4ad8-a877-f9df675cb60f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:41:53 GMT', 'content-type': 'application/json', 'content-length': '1115', 'connection': 'keep-alive', 'x-amzn-requestid': 'b09f69bb-ab58-4ad8-a877-f9df675cb60f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The page contains "Inspection Certificate" heading, and content includes "This is to certify that the shipment transported on the referenced Pro number was measured and weighed ... Dimensions and weights determined and certified by use of Mettler-Toledo ... Weight(Lbs) ...". This looks like a Weight and Inspection Certificate (weight_and_inspection_cert). It also has no mention of NMFC. So likely weight_and_inspection_cert. The definition: certificate confirming weight possibly with length, width, height. Yes. So output doc_type: weight_and_inspection_cert. One page. Provide JSON array? The tool expects a call with documents array. The structure: documents: array of objects with page_no and doc_type. So produce single object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 178, 'totalTokens': 3004}, 'metrics': {'latencyMs': 953}}
2025-09-24 15:11:54,055 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/04078ac6_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:54,444 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:11:54,444 - INFO - 

✓ Saved result: debug_output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:11:54,794 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/04078ac6_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:54,795 - INFO - 
📊 Processing Summary:
2025-09-24 15:11:54,795 - INFO -    Total files: 2
2025-09-24 15:11:54,795 - INFO -    Successful: 2
2025-09-24 15:11:54,795 - INFO -    Failed: 0
2025-09-24 15:11:54,795 - INFO -    Duration: 15.57 seconds
2025-09-24 15:11:54,795 - INFO -    Output directory: debug_output
2025-09-24 15:11:54,795 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:11:54,795 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:11:54,796 - INFO -    📄 DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:11:54,796 - INFO - 
============================================================================================================================================
2025-09-24 15:11:54,796 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:11:54,796 - INFO - ============================================================================================================================================
2025-09-24 15:11:54,796 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:11:54,796 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:11:54,796 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:11:54,796 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:11:54,796 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/debug_output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:11:54,796 - INFO - 
2025-09-24 15:11:54,797 - INFO - DCY7SLNMWUXIENOREHQF.pdf                           1      weight_and_inspect... run1_DCY7SLNMWUXIENOREHQF.json                    
2025-09-24 15:11:54,797 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 15:11:54,797 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/debug_output/run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 15:11:54,797 - INFO - 
2025-09-24 15:11:54,797 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:11:54,797 - INFO - Total entries: 2
2025-09-24 15:11:54,797 - INFO - ============================================================================================================================================
2025-09-24 15:11:54,797 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:11:54,797 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:11:54,797 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:11:54,798 - INFO -   2. DCY7SLNMWUXIENOREHQF.pdf            Page 1   → weight_and_inspection_cert | run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 15:11:54,798 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:11:54,801 - INFO - 
📈 Statistics saved to: debug_output/run1_stats.json
2025-09-24 15:11:54,801 - INFO - 
🎉 Processing completed!
2025-09-24 15:11:54,801 - INFO - 📊 Summary: 2/2 files processed successfully
2025-09-24 15:11:54,802 - INFO - ⏱️  Total duration: 15.57 seconds
2025-09-24 15:11:54,802 - INFO - 📁 Output directory: debug_output
2025-09-24 15:11:54,802 - INFO - 📈 Statistics file: debug_output/run1_stats.json
