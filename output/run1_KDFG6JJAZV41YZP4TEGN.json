{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9996255040168762, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 1.775000981751873e-07}, {"X": 1.0, "Y": 0.9996255040168762}, {"X": 7.032326010403267e-08, "Y": 0.9995651841163635}]}, "Id": "7e364d62-97fb-44d0-bd23-43159b0a47fd", "Relationships": [{"Type": "CHILD", "Ids": ["66cf292c-01ee-4917-9df2-41f9af57f05a", "21050cb8-a897-4852-8a03-c70191288d68", "8112d877-8fb1-4a58-9ac4-c41b1b3c57b7", "a505abfa-c753-499e-8ed8-ad3acffe4fd5", "e954702c-a908-48fb-a889-c58d59e732a8", "6a5f0647-9212-439e-ac03-84201f15b3f4", "bb3ca77c-f60d-426d-bdac-329e0e04f99f", "373ff91b-947d-49e1-9e49-441c4a6005a4", "d11e6ff9-0182-4ead-826f-772816a4c707", "18c3e007-9245-457e-a8d1-a26643708894", "76f55586-6d24-4444-96f1-c26e88300614", "41a4a3ce-afb6-4039-bcaf-07d9d4727391", "e8a92c51-d7ad-4c71-bdc9-6df949c6522f", "4317bdab-d19d-498e-8587-a5125d75752a", "cbbd0b25-6cc6-4913-9457-ae491989c81d", "339791e8-e8b7-4b22-a446-174c45749605", "4b9f3b6d-ca6c-4d68-9bfa-75c3b44499cd", "87440e22-c297-44d0-87f5-ff31dc802ba2", "c175addd-ad66-402f-b703-dacd66c7f56b", "f29a10f8-9faa-4e57-88fd-36f84682e8bc", "4e4e641a-f78d-414c-b23a-08ed7d5b361e", "a3f2d51e-4895-4c0a-abba-4a469f6a8766", "3c507ae8-01a7-4bff-999f-e3c3769c602c", "6223c839-9b1a-46f1-b147-114cb927b160", "d4b202e1-a901-4004-9238-7b76392e8dce", "ab6ce079-5334-4a9b-8487-afb7daf76497", "e5d5e939-7713-49f2-b9bb-341a52d2b161", "e8930f85-939a-454a-a2f0-3e08cb87ca86", "3470af41-bc86-49aa-93c2-c570cde52095", "94259844-ebd3-4c8f-a72b-5aae769065b2", "08e31f27-9321-40b7-bf4d-9102774ddffb", "271262e9-8426-4697-8228-26d5d5db5510", "2deda4c0-e638-4af0-86f4-f9258db98b9b", "830b6282-c597-4795-834c-8fd46d5b9095", "bbb775a6-2a5a-4881-8430-4bc465373b56", "3c36d194-f6e4-47df-980c-b11c16fbba2e", "ec11b611-993a-4c96-b746-6810b88e738e", "42859acd-c1b9-4fb5-95e8-0c3da3476507", "95ab4165-fa39-438a-96ba-f96de431028f", "e9288a78-7f32-4450-9921-2daea9c32cbe", "57113a41-0371-465c-b294-f95ee0a010e1", "ed0b96d8-68ae-4f16-9af1-83bcfa5b7c30", "7e440140-b908-4876-9503-98ffb4f09826", "97d6b88e-84ef-44a9-a21b-c712407c11e1", "39db59d8-ef0b-43e7-adaa-36f8ab5ea9ca", "168460ab-8fae-460d-82a6-8e37e34e0f61", "fcf90b20-0b4e-44cc-948f-eeabe57756d8", "7d65816e-7f1b-4760-901a-327fcecee403", "f65f15b0-209c-4b77-b1d7-fc9a2e2213e7", "ee091c64-5829-44fa-9896-1d29304fa13b", "84615b4a-7c63-4e9f-97ce-ede389e93d22", "1a66cd93-2d74-4c22-a489-4382479cb1ab", "483b073c-623e-4155-9bd1-d50687616d84", "b18cf12c-4afd-4faa-afe6-f4f11102cb01", "9931dbfc-6089-411d-b142-1a0d35ee792e", "fc5d02ec-3216-42b7-8e04-9276ff5cec73", "923ee059-0ea4-4190-abf7-4a06c45713f6", "ce715204-4f8d-4b85-bcf2-583c83fe2247"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Capstone Logistics", "Geometry": {"BoundingBox": {"Width": 0.3197097182273865, "Height": 0.028090883046388626, "Left": 0.06309524178504944, "Top": 0.011393219232559204}, "Polygon": [{"X": 0.06309524178504944, "Y": 0.011393219232559204}, {"X": 0.3828049600124359, "Y": 0.01142030581831932}, {"X": 0.3828040361404419, "Y": 0.03948410227894783}, {"X": 0.06309528648853302, "Y": 0.03945723921060562}]}, "Id": "66cf292c-01ee-4917-9df2-41f9af57f05a", "Relationships": [{"Type": "CHILD", "Ids": ["875f9ec3-d7c0-45b0-980c-617d25c7f70d", "0640f8e9-2163-4272-92f9-ff235106df98"]}]}, {"BlockType": "LINE", "Confidence": 99.96256256103516, "Text": "30 TECHNOLOGY PKWY SOUTH SUITE 200", "Geometry": {"BoundingBox": {"Width": 0.33068913221359253, "Height": 0.009219166822731495, "Left": 0.057492226362228394, "Top": 0.057326994836330414}, "Polygon": [{"X": 0.057492226362228394, "Y": 0.057326994836330414}, {"X": 0.3881813585758209, "Y": 0.057354636490345}, {"X": 0.38818106055259705, "Y": 0.06654616445302963}, {"X": 0.057492248713970184, "Y": 0.06651859730482101}]}, "Id": "21050cb8-a897-4852-8a03-c70191288d68", "Relationships": [{"Type": "CHILD", "Ids": ["ba9e073f-1d91-497d-a37f-b2fed4fadd2f", "40029030-189f-4719-ab21-66ecd86e9f71", "131dc3a0-fc43-41a4-8e02-bf5baab62f3c", "f7939724-7b36-4ba7-837a-9d6853d04e56", "818894ab-4419-4561-9768-038461bd49b7", "5867e4f0-f87e-4805-a4fd-4940acbc5fba"]}]}, {"BlockType": "LINE", "Confidence": 99.89606475830078, "Text": "PEACHTREE CORNERS, GA 30092", "Geometry": {"BoundingBox": {"Width": 0.2576748728752136, "Height": 0.010551213286817074, "Left": 0.09354114532470703, "Top": 0.07109233736991882}, "Polygon": [{"X": 0.09354116022586823, "Y": 0.07109233736991882}, {"X": 0.35121601819992065, "Y": 0.07111378759145737}, {"X": 0.3512157201766968, "Y": 0.08164355158805847}, {"X": 0.09354114532470703, "Y": 0.0816221684217453}]}, "Id": "8112d877-8fb1-4a58-9ac4-c41b1b3c57b7", "Relationships": [{"Type": "CHILD", "Ids": ["7e05a0b5-6aaf-4ed4-b45c-a388b14562db", "28c34414-51f8-4a79-b960-76b97c0d4e47", "76682dde-7cf0-41da-b124-3ebd22a317a6", "f446c110-f9aa-4e25-8c2b-f9e3e13293c7"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "770-414-1929", "Geometry": {"BoundingBox": {"Width": 0.09453842788934708, "Height": 0.009097663685679436, "Left": 0.1752629578113556, "Top": 0.08471370488405228}, "Polygon": [{"X": 0.17526306211948395, "Y": 0.08471370488405228}, {"X": 0.26980137825012207, "Y": 0.08472154289484024}, {"X": 0.26980119943618774, "Y": 0.09381137043237686}, {"X": 0.1752629578113556, "Y": 0.09380354732275009}]}, "Id": "a505abfa-c753-499e-8ed8-ad3acffe4fd5", "Relationships": [{"Type": "CHILD", "Ids": ["b6ebbc1b-ad12-4c85-8006-9d59ea77f020"]}]}, {"BlockType": "LINE", "Confidence": 99.68424224853516, "Text": "FED ID# 45-3087555", "Geometry": {"BoundingBox": {"Width": 0.14685724675655365, "Height": 0.009239531122148037, "Left": 0.14908289909362793, "Top": 0.09826555848121643}, "Polygon": [{"X": 0.1490829735994339, "Y": 0.09826555848121643}, {"X": 0.2959401309490204, "Y": 0.09827768802642822}, {"X": 0.29593992233276367, "Y": 0.10750509053468704}, {"X": 0.14908289909362793, "Y": 0.10749299824237823}]}, "Id": "e954702c-a908-48fb-a889-c58d59e732a8", "Relationships": [{"Type": "CHILD", "Ids": ["98f80a0b-db44-4695-888e-e5cfec5cbb53", "03e17cb0-04bc-4264-8264-1195ddd26d28", "56b1ad8b-fb65-43d0-8f59-766badd03595"]}]}, {"BlockType": "LINE", "Confidence": 99.81689453125, "Text": "15:20:43 September 19, 2025", "Geometry": {"BoundingBox": {"Width": 0.29426443576812744, "Height": 0.01701257936656475, "Left": 0.07643646001815796, "Top": 0.12794101238250732}, "Polygon": [{"X": 0.07643646001815796, "Y": 0.12794101238250732}, {"X": 0.3707008957862854, "Y": 0.12796509265899658}, {"X": 0.3707003593444824, "Y": 0.14495359361171722}, {"X": 0.07643646746873856, "Y": 0.14492963254451752}]}, "Id": "6a5f0647-9212-439e-ac03-84201f15b3f4", "Relationships": [{"Type": "CHILD", "Ids": ["7ea4713b-0983-4694-b9e6-c76409a89295", "43675605-a00d-4acb-9c33-9a2a55ece214", "b3c5b61c-10e8-4187-8774-40901def538c", "bc409b12-6502-48eb-94b8-520cce7bce39"]}]}, {"BlockType": "LINE", "Confidence": 99.4091796875, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.06806080788373947, "Height": 0.01165066473186016, "Left": 0.008145999163389206, "Top": 0.16235260665416718}, "Polygon": [{"X": 0.008145999163389206, "Y": 0.16235260665416718}, {"X": 0.07620680332183838, "Y": 0.1623581200838089}, {"X": 0.07620680332183838, "Y": 0.17400327324867249}, {"X": 0.008146089501678944, "Y": 0.17399777472019196}]}, "Id": "bb3ca77c-f60d-426d-bdac-329e0e04f99f", "Relationships": [{"Type": "CHILD", "Ids": ["1d97963f-8c76-484d-957c-68369b50dcbd", "8a9964a0-7586-4a2d-a859-54e993c4575c"]}]}, {"BlockType": "LINE", "Confidence": 98.98457336425781, "Text": "cf513bca-8e3c-", "Geometry": {"BoundingBox": {"Width": 0.10397268831729889, "Height": 0.009087328799068928, "Left": 0.21867360174655914, "Top": 0.16255013644695282}, "Polygon": [{"X": 0.2186737358570099, "Y": 0.16255013644695282}, {"X": 0.32264629006385803, "Y": 0.16255855560302734}, {"X": 0.32264605164527893, "Y": 0.17163746058940887}, {"X": 0.21867360174655914, "Y": 0.17162905633449554}]}, "Id": "373ff91b-947d-49e1-9e49-441c4a6005a4", "Relationships": [{"Type": "CHILD", "Ids": ["ba425659-2f4a-41b4-92d8-92f496cfa27a"]}]}, {"BlockType": "LINE", "Confidence": 99.2833251953125, "Text": "476d-ad19-17e9202168b3", "Geometry": {"BoundingBox": {"Width": 0.17760580778121948, "Height": 0.009221995249390602, "Left": 0.2178833931684494, "Top": 0.17592766880989075}, "Polygon": [{"X": 0.21788354218006134, "Y": 0.17592766880989075}, {"X": 0.3954892158508301, "Y": 0.17594198882579803}, {"X": 0.3954888880252838, "Y": 0.1851496696472168}, {"X": 0.2178833931684494, "Y": 0.1851353794336319}]}, "Id": "d11e6ff9-0182-4ead-826f-772816a4c707", "Relationships": [{"Type": "CHILD", "Ids": ["e69a052f-9a62-4f48-b6e0-69ec3e549f70"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Location:", "Geometry": {"BoundingBox": {"Width": 0.06375987082719803, "Height": 0.008999418467283249, "Left": 0.008231271989643574, "Top": 0.19816763699054718}, "Polygon": [{"X": 0.008231271989643574, "Y": 0.19816763699054718}, {"X": 0.07199113070964813, "Y": 0.19817274808883667}, {"X": 0.07199113816022873, "Y": 0.20716705918312073}, {"X": 0.00823134183883667, "Y": 0.20716196298599243}]}, "Id": "18c3e007-9245-457e-a8d1-a26643708894", "Relationships": [{"Type": "CHILD", "Ids": ["bc7f9011-b527-4f3b-9760-c434b5990c54"]}]}, {"BlockType": "LINE", "Confidence": 99.75505828857422, "Text": "PFS CINCINNATI OH", "Geometry": {"BoundingBox": {"Width": 0.15913546085357666, "Height": 0.009064546786248684, "Left": 0.21833477914333344, "Top": 0.19812463223934174}, "Polygon": [{"X": 0.21833491325378418, "Y": 0.19812463223934174}, {"X": 0.3774702250957489, "Y": 0.19813737273216248}, {"X": 0.377469927072525, "Y": 0.2071891725063324}, {"X": 0.21833477914333344, "Y": 0.20717646181583405}]}, "Id": "76f55586-6d24-4444-96f1-c26e88300614", "Relationships": [{"Type": "CHILD", "Ids": ["82effffe-e6d7-43ed-9f6d-216f20337498", "ef72e2b5-d208-45ae-93e1-8ff9c9f3f485", "783d3673-cdb3-4f0f-9d34-7fbb16040e17"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "Work Date:", "Geometry": {"BoundingBox": {"Width": 0.07790262252092361, "Height": 0.008925079368054867, "Left": 0.008132929913699627, "Top": 0.2338302731513977}, "Polygon": [{"X": 0.008132929913699627, "Y": 0.2338302731513977}, {"X": 0.08603555709123611, "Y": 0.23383644223213196}, {"X": 0.08603554964065552, "Y": 0.24275535345077515}, {"X": 0.008132999762892723, "Y": 0.2427491992712021}]}, "Id": "41a4a3ce-afb6-4039-bcaf-07d9d4727391", "Relationships": [{"Type": "CHILD", "Ids": ["30dcbdbb-aa43-4a58-b496-3c2d8299e0cf", "19f49788-98c8-4121-8c84-5c08628a2591"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "2025-09-19", "Geometry": {"BoundingBox": {"Width": 0.07868044823408127, "Height": 0.009164560586214066, "Left": 0.21833966672420502, "Top": 0.23379871249198914}, "Polygon": [{"X": 0.21833980083465576, "Y": 0.23379871249198914}, {"X": 0.2970201075077057, "Y": 0.23380494117736816}, {"X": 0.297019898891449, "Y": 0.2429632693529129}, {"X": 0.21833966672420502, "Y": 0.24295705556869507}]}, "Id": "e8a92c51-d7ad-4c71-bdc9-6df949c6522f", "Relationships": [{"Type": "CHILD", "Ids": ["598d65ec-ecfe-4fcd-9b7c-9aee282ccbdb"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Bill Code:", "Geometry": {"BoundingBox": {"Width": 0.06854791939258575, "Height": 0.00896855816245079, "Left": 0.00849233753979206, "Top": 0.2514283061027527}, "Polygon": [{"X": 0.00849233753979206, "Y": 0.2514283061027527}, {"X": 0.07704025506973267, "Y": 0.25143370032310486}, {"X": 0.07704025506973267, "Y": 0.2603968381881714}, {"X": 0.008492406457662582, "Y": 0.2603914737701416}]}, "Id": "4317bdab-d19d-498e-8587-a5125d75752a", "Relationships": [{"Type": "CHILD", "Ids": ["75e4021c-c9f9-48fe-b588-79734a0d5e75", "887c7dd0-8b2a-4937-8641-0fb37daeccd1"]}]}, {"BlockType": "LINE", "Confidence": 99.71121215820312, "Text": "RCOD30274", "Geometry": {"BoundingBox": {"Width": 0.08860987424850464, "Height": 0.009075343608856201, "Left": 0.218523308634758, "Top": 0.2515418529510498}, "Polygon": [{"X": 0.21852344274520874, "Y": 0.2515418529510498}, {"X": 0.30713319778442383, "Y": 0.2515488266944885}, {"X": 0.3071329593658447, "Y": 0.260617196559906}, {"X": 0.218523308634758, "Y": 0.2606102228164673}]}, "Id": "cbbd0b25-6cc6-4913-9457-ae491989c81d", "Relationships": [{"Type": "CHILD", "Ids": ["22ac6341-194f-4043-a792-d906e91980ca"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.05119946599006653, "Height": 0.008980024605989456, "Left": 0.008698731660842896, "Top": 0.26940613985061646}, "Polygon": [{"X": 0.008698731660842896, "Y": 0.26940613985061646}, {"X": 0.05989817902445793, "Y": 0.2694101631641388}, {"X": 0.059898197650909424, "Y": 0.2783861756324768}, {"X": 0.008698800578713417, "Y": 0.27838218212127686}]}, "Id": "339791e8-e8b7-4b22-a446-174c45749605", "Relationships": [{"Type": "CHILD", "Ids": ["0a99f0cf-a307-4428-8ffc-a17a39c195dd"]}]}, {"BlockType": "LINE", "Confidence": 99.4189453125, "Text": "DREAM LAND", "Geometry": {"BoundingBox": {"Width": 0.11140259355306625, "Height": 0.008775938302278519, "Left": 0.21851246058940887, "Top": 0.26957476139068604}, "Polygon": [{"X": 0.21851259469985962, "Y": 0.26957476139068604}, {"X": 0.32991504669189453, "Y": 0.26958349347114563}, {"X": 0.32991480827331543, "Y": 0.27835071086883545}, {"X": 0.21851246058940887, "Y": 0.27834200859069824}]}, "Id": "4b9f3b6d-ca6c-4d68-9bfa-75c3b44499cd", "Relationships": [{"Type": "CHILD", "Ids": ["d7dd3666-9b30-4e32-9d88-9c236597f97d", "28579397-3e10-48e1-a2e2-dadd2ba42be3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock:", "Geometry": {"BoundingBox": {"Width": 0.04030928388237953, "Height": 0.009025296196341515, "Left": 0.008433465845882893, "Top": 0.28706350922584534}, "Polygon": [{"X": 0.008433465845882893, "Y": 0.28706350922584534}, {"X": 0.04874272271990776, "Y": 0.28706663846969604}, {"X": 0.04874275252223015, "Y": 0.2960887849330902}, {"X": 0.008433534763753414, "Y": 0.2960856556892395}]}, "Id": "87440e22-c297-44d0-87f5-ff31dc802ba2", "Relationships": [{"Type": "CHILD", "Ids": ["e23cf1a1-d806-4378-a82a-992e57537d32"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRY", "Geometry": {"BoundingBox": {"Width": 0.03519865870475769, "Height": 0.008858436718583107, "Left": 0.21859151124954224, "Top": 0.2872175872325897}, "Polygon": [{"X": 0.21859164535999298, "Y": 0.2872175872325897}, {"X": 0.2537901699542999, "Y": 0.2872203290462494}, {"X": 0.253790020942688, "Y": 0.2960760295391083}, {"X": 0.21859151124954224, "Y": 0.2960732877254486}]}, "Id": "c175addd-ad66-402f-b703-dacd66c7f56b", "Relationships": [{"Type": "CHILD", "Ids": ["908af529-8d76-4dba-bf13-465024851c24"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.03815705329179764, "Height": 0.008805360645055771, "Left": 0.008498143404722214, "Top": 0.3052511513233185}, "Polygon": [{"X": 0.008498143404722214, "Y": 0.3052511513233185}, {"X": 0.046655163168907166, "Y": 0.30525410175323486}, {"X": 0.04665519669651985, "Y": 0.31405651569366455}, {"X": 0.00849821139127016, "Y": 0.31405356526374817}]}, "Id": "f29a10f8-9faa-4e57-88fd-36f84682e8bc", "Relationships": [{"Type": "CHILD", "Ids": ["e743a764-a608-4877-a86a-e6aeceb994c4"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "14", "Geometry": {"BoundingBox": {"Width": 0.015271622687578201, "Height": 0.008638577535748482, "Left": 0.22007900476455688, "Top": 0.305248886346817}, "Polygon": [{"X": 0.22007913887500763, "Y": 0.305248886346817}, {"X": 0.2353506237268448, "Y": 0.3052500784397125}, {"X": 0.23535047471523285, "Y": 0.31388744711875916}, {"X": 0.22007900476455688, "Y": 0.31388628482818604}]}, "Id": "4e4e641a-f78d-414c-b23a-08ed7d5b361e", "Relationships": [{"Type": "CHILD", "Ids": ["6f64181d-a899-4b06-9cf7-55ceb1f1d711"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Purchase Orders", "Geometry": {"BoundingBox": {"Width": 0.11073260009288788, "Height": 0.008844932541251183, "Left": 0.0081587303429842, "Top": 0.32989683747291565}, "Polygon": [{"X": 0.0081587303429842, "Y": 0.32989683747291565}, {"X": 0.11889132857322693, "Y": 0.32990536093711853}, {"X": 0.11889129132032394, "Y": 0.3387417793273926}, {"X": 0.008158799260854721, "Y": 0.3387332856655121}]}, "Id": "a3f2d51e-4895-4c0a-abba-4a469f6a8766", "Relationships": [{"Type": "CHILD", "Ids": ["42761d9b-481c-4abb-a061-b4972b805882", "a27bfc82-11c6-4317-acad-1307cd82aa3a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.05113367363810539, "Height": 0.009044213220477104, "Left": 0.21829824149608612, "Top": 0.32979652285575867}, "Polygon": [{"X": 0.21829837560653687, "Y": 0.32979652285575867}, {"X": 0.2694319188594818, "Y": 0.32980045676231384}, {"X": 0.2694317102432251, "Y": 0.3388407528400421}, {"X": 0.21829824149608612, "Y": 0.33883681893348694}]}, "Id": "3c507ae8-01a7-4bff-999f-e3c3769c602c", "Relationships": [{"Type": "CHILD", "Ids": ["82576eb1-1f77-4a9d-a8f7-6e56f9d620e5"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "21927", "Geometry": {"BoundingBox": {"Width": 0.041989319026470184, "Height": 0.008949325419962406, "Left": 0.008274061605334282, "Top": 0.3478981852531433}, "Polygon": [{"X": 0.008274061605334282, "Y": 0.3478981852531433}, {"X": 0.05026335269212723, "Y": 0.3479014039039612}, {"X": 0.050263382494449615, "Y": 0.35684752464294434}, {"X": 0.008274130523204803, "Y": 0.35684433579444885}]}, "Id": "6223c839-9b1a-46f1-b147-114cb927b160", "Relationships": [{"Type": "CHILD", "Ids": ["ca4c1e6b-d52b-4347-a6a2-db401bdfd9c2"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "AMERCAREROYAL", "Geometry": {"BoundingBox": {"Width": 0.15152399241924286, "Height": 0.008894551545381546, "Left": 0.21798090636730194, "Top": 0.3479148745536804}, "Polygon": [{"X": 0.21798104047775269, "Y": 0.3479148745536804}, {"X": 0.3695048987865448, "Y": 0.34792643785476685}, {"X": 0.3695046305656433, "Y": 0.35680943727493286}, {"X": 0.21798090636730194, "Y": 0.35679787397384644}]}, "Id": "d4b202e1-a901-4004-9238-7b76392e8dce", "Relationships": [{"Type": "CHILD", "Ids": ["5f52c8bf-fd3c-4844-bc5f-d3c7559ed88a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Initial Pallets:", "Geometry": {"BoundingBox": {"Width": 0.13261854648590088, "Height": 0.009070505388081074, "Left": 0.00833059847354889, "Top": 0.39019325375556946}, "Polygon": [{"X": 0.00833059847354889, "Y": 0.39019325375556946}, {"X": 0.14094914495944977, "Y": 0.3902032673358917}, {"X": 0.140949085354805, "Y": 0.39926376938819885}, {"X": 0.008330668322741985, "Y": 0.39925381541252136}]}, "Id": "ab6ce079-5334-4a9b-8487-afb7daf76497", "Relationships": [{"Type": "CHILD", "Ids": ["724ff151-c0c0-464d-95f4-315766707a46", "9a3af3c7-f56c-4149-ac8a-d48a47ef65ae", "e871cd58-e32e-4ad5-b6d5-898aee59cf72"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "25.00", "Geometry": {"BoundingBox": {"Width": 0.037960849702358246, "Height": 0.009056627750396729, "Left": 0.21835647523403168, "Top": 0.39030709862709045}, "Polygon": [{"X": 0.21835660934448242, "Y": 0.39030709862709045}, {"X": 0.2563173174858093, "Y": 0.3903099596500397}, {"X": 0.256317138671875, "Y": 0.3993637263774872}, {"X": 0.21835647523403168, "Y": 0.39936086535453796}]}, "Id": "e5d5e939-7713-49f2-b9bb-341a52d2b161", "Relationships": [{"Type": "CHILD", "Ids": ["9e201722-5825-4428-b7d8-6452079bdc06"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "Total Finished Pallets:", "Geometry": {"BoundingBox": {"Width": 0.15049655735492706, "Height": 0.00905684381723404, "Left": 0.008309916593134403, "Top": 0.4081292748451233}, "Polygon": [{"X": 0.008309916593134403, "Y": 0.4081292748451233}, {"X": 0.1588064730167389, "Y": 0.40814054012298584}, {"X": 0.15880638360977173, "Y": 0.41718611121177673}, {"X": 0.008309987373650074, "Y": 0.4171748757362366}]}, "Id": "e8930f85-939a-454a-a2f0-3e08cb87ca86", "Relationships": [{"Type": "CHILD", "Ids": ["849ebdef-01cd-4f3c-a83f-d0a9da8a5b77", "508199e2-b509-46f1-ab9f-03d8a5b01684", "3eeeb30d-81b1-4376-b617-caa3e7153d2f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "57", "Geometry": {"BoundingBox": {"Width": 0.01632171869277954, "Height": 0.008703814819455147, "Left": 0.21884691715240479, "Top": 0.40841296315193176}, "Polygon": [{"X": 0.21884705126285553, "Y": 0.40841296315193176}, {"X": 0.23516863584518433, "Y": 0.40841418504714966}, {"X": 0.2351684868335724, "Y": 0.41711676120758057}, {"X": 0.21884691715240479, "Y": 0.41711553931236267}]}, "Id": "3470af41-bc86-49aa-93c2-c570cde52095", "Relationships": [{"Type": "CHILD", "Ids": ["2bb63a9a-e6d6-4648-bff5-d1a3746e6efe"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "Total Case Count:", "Geometry": {"BoundingBox": {"Width": 0.12145963311195374, "Height": 0.00884196162223816, "Left": 0.00836922973394394, "Top": 0.42595890164375305}, "Polygon": [{"X": 0.00836922973394394, "Y": 0.42595890164375305}, {"X": 0.12982885539531708, "Y": 0.4259679317474365}, {"X": 0.1298288106918335, "Y": 0.4348008632659912}, {"X": 0.00836929865181446, "Y": 0.43479183316230774}]}, "Id": "94259844-ebd3-4c8f-a72b-5aae769065b2", "Relationships": [{"Type": "CHILD", "Ids": ["fb6f0309-99d9-4a81-bfd5-401d8dcde6dc", "5f113865-8449-438f-acee-4f713fb77444", "a3da2a04-9d69-4173-888a-b37da3a61b96"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1909", "Geometry": {"BoundingBox": {"Width": 0.03227212280035019, "Height": 0.009036189876496792, "Left": 0.21987150609493256, "Top": 0.4259832799434662}, "Polygon": [{"X": 0.2198716402053833, "Y": 0.4259832799434662}, {"X": 0.25214362144470215, "Y": 0.4259856641292572}, {"X": 0.2521434724330902, "Y": 0.43501946330070496}, {"X": 0.21987150609493256, "Y": 0.43501707911491394}]}, "Id": "08e31f27-9321-40b7-bf4d-9102774ddffb", "Relationships": [{"Type": "CHILD", "Ids": ["79f75a30-8ecb-4555-a359-78d94e59f45d"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Trailer Number:", "Geometry": {"BoundingBox": {"Width": 0.10910247266292572, "Height": 0.008952525444328785, "Left": 0.008214366622269154, "Top": 0.44387173652648926}, "Polygon": [{"X": 0.008214366622269154, "Y": 0.44387173652648926}, {"X": 0.1173168420791626, "Y": 0.4438798129558563}, {"X": 0.11731680482625961, "Y": 0.45282426476478577}, {"X": 0.00821443647146225, "Y": 0.4528162181377411}]}, "Id": "271262e9-8426-4697-8228-26d5d5db5510", "Relationships": [{"Type": "CHILD", "Ids": ["529bae46-1b37-4105-8e48-a7f96256d0ce", "d9335c34-7b23-4b6d-90c8-9f05dda74ed8"]}]}, {"BlockType": "LINE", "Confidence": 75.65130615234375, "Text": "x", "Geometry": {"BoundingBox": {"Width": 0.0085075031965971, "Height": 0.006549193523824215, "Left": 0.21835273504257202, "Top": 0.44640815258026123}, "Polygon": [{"X": 0.21835282444953918, "Y": 0.44640815258026123}, {"X": 0.22686024010181427, "Y": 0.44640877842903137}, {"X": 0.22686012089252472, "Y": 0.4529573321342468}, {"X": 0.21835273504257202, "Y": 0.4529567062854767}]}, "Id": "2deda4c0-e638-4af0-86f4-f9258db98b9b", "Relationships": [{"Type": "CHILD", "Ids": ["fdd93ce1-7499-4f40-ba1a-1409011dff9c"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Tractor Number:", "Geometry": {"BoundingBox": {"Width": 0.1125926747918129, "Height": 0.00902920588850975, "Left": 0.008351952768862247, "Top": 0.4615432322025299}, "Polygon": [{"X": 0.008351952768862247, "Y": 0.4615432322025299}, {"X": 0.12094462662935257, "Y": 0.4615515172481537}, {"X": 0.12094458192586899, "Y": 0.47057244181632996}, {"X": 0.008352022618055344, "Y": 0.47056418657302856}]}, "Id": "830b6282-c597-4795-834c-8fd46d5b9095", "Relationships": [{"Type": "CHILD", "Ids": ["a2e028bf-05a5-4fb3-8ad1-fcf419ef64f2", "29ce85c1-440a-4a20-821c-341f4200f124"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "BOL:", "Geometry": {"BoundingBox": {"Width": 0.037209197878837585, "Height": 0.008752327412366867, "Left": 0.008386384695768356, "Top": 0.4796783924102783}, "Polygon": [{"X": 0.008386384695768356, "Y": 0.4796783924102783}, {"X": 0.045595552772283554, "Y": 0.479681134223938}, {"X": 0.04559558257460594, "Y": 0.4884307384490967}, {"X": 0.008386452682316303, "Y": 0.4884280264377594}]}, "Id": "bbb775a6-2a5a-4881-8430-4bc465373b56", "Relationships": [{"Type": "CHILD", "Ids": ["a3fc7b51-441e-4c40-b34a-40ce8a2b9d4e"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Comments:", "Geometry": {"BoundingBox": {"Width": 0.07628358155488968, "Height": 0.008733326569199562, "Left": 0.008561443537473679, "Top": 0.4974142014980316}, "Polygon": [{"X": 0.008561443537473679, "Y": 0.4974142014980316}, {"X": 0.08484502136707306, "Y": 0.4974197447299957}, {"X": 0.08484501391649246, "Y": 0.5061475038528442}, {"X": 0.008561510592699051, "Y": 0.5061419606208801}]}, "Id": "3c36d194-f6e4-47df-980c-b11c16fbba2e", "Relationships": [{"Type": "CHILD", "Ids": ["feb1e5ea-7e90-4702-afbb-b5b600f3f52f"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Canned Comments:", "Geometry": {"BoundingBox": {"Width": 0.13179709017276764, "Height": 0.008833715692162514, "Left": 0.008709277957677841, "Top": 0.5151969194412231}, "Polygon": [{"X": 0.008709277957677841, "Y": 0.5151969194412231}, {"X": 0.14050637185573578, "Y": 0.5152064561843872}, {"X": 0.140506312251091, "Y": 0.5240306258201599}, {"X": 0.008709345944225788, "Y": 0.5240211486816406}]}, "Id": "ec11b611-993a-4c96-b746-6810b88e738e", "Relationships": [{"Type": "CHILD", "Ids": ["2e21dbaf-3a7f-45a2-9ab2-a01f91c60a1c", "c74a457e-6ca2-4ff1-8cff-cb8e3700b837"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Unloaders:", "Geometry": {"BoundingBox": {"Width": 0.07250798493623734, "Height": 0.009071151725947857, "Left": 0.008508577011525631, "Top": 0.5326732397079468}, "Polygon": [{"X": 0.008508577011525631, "Y": 0.5326732397079468}, {"X": 0.08101656287908554, "Y": 0.5326784253120422}, {"X": 0.08101656287908554, "Y": 0.5417444109916687}, {"X": 0.008508646860718727, "Y": 0.5417391657829285}]}, "Id": "42859acd-c1b9-4fb5-95e8-0c3da3476507", "Relationships": [{"Type": "CHILD", "Ids": ["ccd2ecbd-40cd-4f87-a845-b900b64d1edd"]}]}, {"BlockType": "LINE", "Confidence": 99.2432632446289, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004739221651107073, "Height": 0.008766092360019684, "Left": 0.22023016214370728, "Top": 0.5329622626304626}, "Polygon": [{"X": 0.22023029625415802, "Y": 0.5329622626304626}, {"X": 0.22496938705444336, "Y": 0.5329626202583313}, {"X": 0.22496925294399261, "Y": 0.5417283773422241}, {"X": 0.22023016214370728, "Y": 0.5417280197143555}]}, "Id": "95ab4165-fa39-438a-96ba-f96de431028f", "Relationships": [{"Type": "CHILD", "Ids": ["5b884573-3b9c-40c9-b13b-76798c80ebee"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "Add Fee", "Geometry": {"BoundingBox": {"Width": 0.057300690561532974, "Height": 0.009031052701175213, "Left": 0.008549115620553493, "Top": 0.5577662587165833}, "Polygon": [{"X": 0.008549115620553493, "Y": 0.5577662587165833}, {"X": 0.06584979593753815, "Y": 0.5577703714370728}, {"X": 0.06584980338811874, "Y": 0.5667973160743713}, {"X": 0.00854918546974659, "Y": 0.5667932629585266}]}, "Id": "e9288a78-7f32-4450-9921-2daea9c32cbe", "Relationships": [{"Type": "CHILD", "Ids": ["7d7ec592-4d69-4c42-93f6-cf32d33c2289", "fd6699c4-54de-4557-9711-f26b1f8f540a"]}]}, {"BlockType": "LINE", "Confidence": 99.8193359375, "Text": "PO: 21927", "Geometry": {"BoundingBox": {"Width": 0.07217693328857422, "Height": 0.008966122753918171, "Left": 0.008512494154274464, "Top": 0.5756213665008545}, "Polygon": [{"X": 0.008512494154274464, "Y": 0.5756213665008545}, {"X": 0.0806894302368164, "Y": 0.5756264328956604}, {"X": 0.0806894302368164, "Y": 0.5845874547958374}, {"X": 0.008512563072144985, "Y": 0.5845823884010315}]}, "Id": "57113a41-0371-465c-b294-f95ee0a010e1", "Relationships": [{"Type": "CHILD", "Ids": ["11d8b4aa-3bf0-4e26-8af8-5cdc3419e7c2", "194e1598-dec2-4fe9-860e-920e9861d725"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Pinwheeled", "Geometry": {"BoundingBox": {"Width": 0.0788310319185257, "Height": 0.008955278433859348, "Left": 0.040311772376298904, "Top": 0.5934683680534363}, "Polygon": [{"X": 0.040311772376298904, "Y": 0.5934683680534363}, {"X": 0.1191428080201149, "Y": 0.5934739112854004}, {"X": 0.11914277076721191, "Y": 0.6024236083030701}, {"X": 0.04031180962920189, "Y": 0.602418065071106}]}, "Id": "ed0b96d8-68ae-4f16-9af1-83bcfa5b7c30", "Relationships": [{"Type": "CHILD", "Ids": ["71aa5250-1b16-4511-b62b-9565b5cd779e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$15.00", "Geometry": {"BoundingBox": {"Width": 0.04616813734173775, "Height": 0.010342037305235863, "Left": 0.21866139769554138, "Top": 0.5929902791976929}, "Polygon": [{"X": 0.21866156160831451, "Y": 0.5929902791976929}, {"X": 0.26482954621315, "Y": 0.5929935574531555}, {"X": 0.2648293375968933, "Y": 0.6033323407173157}, {"X": 0.21866139769554138, "Y": 0.6033291220664978}]}, "Id": "7e440140-b908-4876-9503-98ffb4f09826", "Relationships": [{"Type": "CHILD", "Ids": ["1c3248be-74d3-4045-8fd9-504d94444abe"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "Total Add Charges:", "Geometry": {"BoundingBox": {"Width": 0.13150998950004578, "Height": 0.01144166849553585, "Left": 0.008433337323367596, "Top": 0.6111221313476562}, "Polygon": [{"X": 0.008433337323367596, "Y": 0.6111221313476562}, {"X": 0.13994333148002625, "Y": 0.6111313700675964}, {"X": 0.13994325697422028, "Y": 0.6225638389587402}, {"X": 0.008433425799012184, "Y": 0.6225546598434448}]}, "Id": "97d6b88e-84ef-44a9-a21b-c712407c11e1", "Relationships": [{"Type": "CHILD", "Ids": ["4093a2ab-2449-471b-88af-afae13775229", "fb674a8b-d373-4cf4-be73-67850ae4828d", "7707fd81-f293-4116-b5d0-cccd5ae58d6a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$15.00", "Geometry": {"BoundingBox": {"Width": 0.0460163876414299, "Height": 0.01031513512134552, "Left": 0.2187175303697586, "Top": 0.6107532978057861}, "Polygon": [{"X": 0.21871767938137054, "Y": 0.6107532978057861}, {"X": 0.2647339105606079, "Y": 0.610756516456604}, {"X": 0.2647337019443512, "Y": 0.6210684776306152}, {"X": 0.2187175303697586, "Y": 0.6210652589797974}]}, "Id": "39db59d8-ef0b-43e7-adaa-36f8ab5ea9ca", "Relationships": [{"Type": "CHILD", "Ids": ["a16f7c69-c1a6-4590-984e-21dd10c60dde"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Base Charge:", "Geometry": {"BoundingBox": {"Width": 0.08901464194059372, "Height": 0.011166648007929325, "Left": 0.008403293788433075, "Top": 0.6291494369506836}, "Polygon": [{"X": 0.008403293788433075, "Y": 0.6291494369506836}, {"X": 0.0974179357290268, "Y": 0.6291556358337402}, {"X": 0.097417913377285, "Y": 0.6403160691261292}, {"X": 0.00840337947010994, "Y": 0.6403099298477173}]}, "Id": "168460ab-8fae-460d-82a6-8e37e34e0f61", "Relationships": [{"Type": "CHILD", "Ids": ["a5a1f2f5-9d20-4b80-8b8c-919632f79630", "529d4f03-9271-4fe5-ae0f-04e87140da38"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "435.00", "Geometry": {"BoundingBox": {"Width": 0.046525608748197556, "Height": 0.008861579932272434, "Left": 0.2179958075284958, "Top": 0.6291859745979309}, "Polygon": [{"X": 0.21799594163894653, "Y": 0.6291859745979309}, {"X": 0.26452142000198364, "Y": 0.6291891932487488}, {"X": 0.2645212411880493, "Y": 0.6380475759506226}, {"X": 0.2179958075284958, "Y": 0.6380443572998047}]}, "Id": "fcf90b20-0b4e-44cc-948f-eeabe57756d8", "Relationships": [{"Type": "CHILD", "Ids": ["ecc90214-36db-43ed-bf51-ce92df2dbac4"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Convenience Fee:", "Geometry": {"BoundingBox": {"Width": 0.11974706500768661, "Height": 0.009217776358127594, "Left": 0.008685870096087456, "Top": 0.6466784477233887}, "Polygon": [{"X": 0.008685870096087456, "Y": 0.6466784477233887}, {"X": 0.12843294441699982, "Y": 0.6466867327690125}, {"X": 0.12843288481235504, "Y": 0.6558962464332581}, {"X": 0.008685940876603127, "Y": 0.655888020992279}]}, "Id": "7d65816e-7f1b-4760-901a-327fcecee403", "Relationships": [{"Type": "CHILD", "Ids": ["41fa52d1-2fea-4637-9878-0b93a6ced55e", "72289a48-305d-4183-b94f-2d8233b6153c"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "10.00", "Geometry": {"BoundingBox": {"Width": 0.03634907677769661, "Height": 0.008525945246219635, "Left": 0.21981187164783478, "Top": 0.6467564702033997}, "Polygon": [{"X": 0.21981200575828552, "Y": 0.6467564702033997}, {"X": 0.2561609447002411, "Y": 0.6467589735984802}, {"X": 0.25616079568862915, "Y": 0.6552824378013611}, {"X": 0.21981187164783478, "Y": 0.6552799344062805}]}, "Id": "f65f15b0-209c-4b77-b1d7-fc9a2e2213e7", "Relationships": [{"Type": "CHILD", "Ids": ["3b006392-75f2-4bfb-a60c-c6007784dcad"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Total Cost:", "Geometry": {"BoundingBox": {"Width": 0.07444490492343903, "Height": 0.009075223468244076, "Left": 0.008332470431923866, "Top": 0.6648595929145813}, "Polygon": [{"X": 0.008332470431923866, "Y": 0.6648595929145813}, {"X": 0.08277737349271774, "Y": 0.664864718914032}, {"X": 0.08277737349271774, "Y": 0.673934817314148}, {"X": 0.008332540281116962, "Y": 0.673929750919342}]}, "Id": "ee091c64-5829-44fa-9896-1d29304fa13b", "Relationships": [{"Type": "CHILD", "Ids": ["1b0931cc-4dda-4b60-a2f5-b9a67f51f317", "73a46cc7-92d2-481e-a270-04eaed231505"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "460.00", "Geometry": {"BoundingBox": {"Width": 0.04669065773487091, "Height": 0.008987697772681713, "Left": 0.21801142394542694, "Top": 0.6646637916564941}, "Polygon": [{"X": 0.21801157295703888, "Y": 0.6646637916564941}, {"X": 0.26470208168029785, "Y": 0.664667010307312}, {"X": 0.2647019028663635, "Y": 0.6736515164375305}, {"X": 0.21801142394542694, "Y": 0.6736482977867126}]}, "Id": "84615b4a-7c63-4e9f-97ce-ede389e93d22", "Relationships": [{"Type": "CHILD", "Ids": ["d4de5868-8514-40a5-80c3-d99b1152114b"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Payments:", "Geometry": {"BoundingBox": {"Width": 0.0688609927892685, "Height": 0.011400335468351841, "Left": 0.008329876698553562, "Top": 0.6895126104354858}, "Polygon": [{"X": 0.008329876698553562, "Y": 0.6895126104354858}, {"X": 0.07719086855649948, "Y": 0.6895173192024231}, {"X": 0.07719086855649948, "Y": 0.7009129524230957}, {"X": 0.008329964242875576, "Y": 0.7009083032608032}]}, "Id": "1a66cd93-2d74-4c22-a489-4382479cb1ab", "Relationships": [{"Type": "CHILD", "Ids": ["f3c394b6-a0f8-4a4b-89e6-566ff7e14352"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.055709730833768845, "Height": 0.008595049381256104, "Left": 0.21832123398780823, "Top": 0.6898230314254761}, "Polygon": [{"X": 0.21832135319709778, "Y": 0.6898230314254761}, {"X": 0.2740309536457062, "Y": 0.6898268461227417}, {"X": 0.27403077483177185, "Y": 0.6984180808067322}, {"X": 0.21832123398780823, "Y": 0.6984143257141113}]}, "Id": "483b073c-623e-4155-9bd1-d50687616d84", "Relationships": [{"Type": "CHILD", "Ids": ["c50ca85e-3dcc-4f31-99cb-59c8975241b5"]}]}, {"BlockType": "LINE", "Confidence": 98.9343490600586, "Text": "CapstonePay-", "Geometry": {"BoundingBox": {"Width": 0.09235386550426483, "Height": 0.011640091426670551, "Left": 0.04242369532585144, "Top": 0.707446277141571}, "Polygon": [{"X": 0.04242369532585144, "Y": 0.707446277141571}, {"X": 0.13477756083011627, "Y": 0.7074525356292725}, {"X": 0.1347774863243103, "Y": 0.7190864086151123}, {"X": 0.04242374002933502, "Y": 0.7190801501274109}]}, "Id": "b18cf12c-4afd-4faa-afe6-f4f11102cb01", "Relationships": [{"Type": "CHILD", "Ids": ["22880021-7246-4fe2-a483-e5203e90721f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$460.00", "Geometry": {"BoundingBox": {"Width": 0.05458301305770874, "Height": 0.010026516392827034, "Left": 0.21855401992797852, "Top": 0.7070488333702087}, "Polygon": [{"X": 0.21855416893959045, "Y": 0.7070488333702087}, {"X": 0.27313703298568726, "Y": 0.7070525288581848}, {"X": 0.27313682436943054, "Y": 0.7170753479003906}, {"X": 0.21855401992797852, "Y": 0.7170716524124146}]}, "Id": "9931dbfc-6089-411d-b142-1a0d35ee792e", "Relationships": [{"Type": "CHILD", "Ids": ["b53554de-eb60-4061-80cb-8bd51d5129fd"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "38028520", "Geometry": {"BoundingBox": {"Width": 0.06657826155424118, "Height": 0.008622176945209503, "Left": 0.04247520864009857, "Top": 0.7213122248649597}, "Polygon": [{"X": 0.04247520864009857, "Y": 0.7213122248649597}, {"X": 0.10905347019433975, "Y": 0.7213166952133179}, {"X": 0.10905344039201736, "Y": 0.7299343943595886}, {"X": 0.042475245893001556, "Y": 0.7299299240112305}]}, "Id": "fc5d02ec-3216-42b7-8e04-9276ff5cec73", "Relationships": [{"Type": "CHILD", "Ids": ["f1c5c0c7-5598-497f-8ca0-6dd0397cf1f4"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Total Payments", "Geometry": {"BoundingBox": {"Width": 0.10484036058187485, "Height": 0.011345161125063896, "Left": 0.04201509431004524, "Top": 0.7433323860168457}, "Polygon": [{"X": 0.04201509431004524, "Y": 0.7433323860168457}, {"X": 0.1468554586172104, "Y": 0.7433393597602844}, {"X": 0.14685536921024323, "Y": 0.7546775341033936}, {"X": 0.042015139013528824, "Y": 0.7546705603599548}]}, "Id": "923ee059-0ea4-4190-abf7-4a06c45713f6", "Relationships": [{"Type": "CHILD", "Ids": ["4462f6d1-5044-4783-a146-8ff247fe205c", "3497382b-48a9-4e3c-b2f0-87048f80fc52"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$460.00", "Geometry": {"BoundingBox": {"Width": 0.05459902063012123, "Height": 0.010217910632491112, "Left": 0.21853196620941162, "Top": 0.7425822615623474}, "Polygon": [{"X": 0.21853211522102356, "Y": 0.7425822615623474}, {"X": 0.27313098311424255, "Y": 0.7425859570503235}, {"X": 0.27313077449798584, "Y": 0.7528002262115479}, {"X": 0.21853196620941162, "Y": 0.7527965903282166}]}, "Id": "ce715204-4f8d-4b85-bcf2-583c83fe2247", "Relationships": [{"Type": "CHILD", "Ids": ["e5306a48-c193-4bb3-a9f4-6a6b22c1b2f8"]}]}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Capstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15523160994052887, "Height": 0.027558909729123116, "Left": 0.06309524178504944, "Top": 0.011911370791494846}, "Polygon": [{"X": 0.06309524178504944, "Y": 0.011911370791494846}, {"X": 0.2183268517255783, "Y": 0.011924520134925842}, {"X": 0.2183264195919037, "Y": 0.039470281451940536}, {"X": 0.06309528648853302, "Y": 0.03945723921060562}]}, "Id": "875f9ec3-d7c0-45b0-980c-617d25c7f70d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1541903167963028, "Height": 0.02807196043431759, "Left": 0.22861464321613312, "Top": 0.01140724215656519}, "Polygon": [{"X": 0.22861510515213013, "Y": 0.01140724215656519}, {"X": 0.3828049600124359, "Y": 0.01142030581831932}, {"X": 0.3828040361404419, "Y": 0.03947920352220535}, {"X": 0.22861464321613312, "Y": 0.0394662469625473}]}, "Id": "0640f8e9-2163-4272-92f9-ff235106df98"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016247976571321487, "Height": 0.009149217046797276, "Left": 0.057492226362228394, "Top": 0.057326994836330414}, "Polygon": [{"X": 0.057492226362228394, "Y": 0.057326994836330414}, {"X": 0.07374019920825958, "Y": 0.05732835456728935}, {"X": 0.07374020665884018, "Y": 0.06647621095180511}, {"X": 0.057492248713970184, "Y": 0.06647485494613647}]}, "Id": "ba9e073f-1d91-497d-a37f-b2fed4fadd2f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TECHNOLOGY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1148732379078865, "Height": 0.00897232349961996, "Left": 0.07799293100833893, "Top": 0.05740157142281532}, "Polygon": [{"X": 0.07799293100833893, "Y": 0.05740157142281532}, {"X": 0.19286617636680603, "Y": 0.05741117522120476}, {"X": 0.19286605715751648, "Y": 0.06637389957904816}, {"X": 0.07799293100833893, "Y": 0.06636431813240051}]}, "Id": "40029030-189f-4719-ab21-66ecd86e9f71"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "PKWY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049291130155324936, "Height": 0.009052327834069729, "Left": 0.1970328390598297, "Top": 0.057357363402843475}, "Polygon": [{"X": 0.19703295826911926, "Y": 0.057357363402843475}, {"X": 0.24632397294044495, "Y": 0.057361483573913574}, {"X": 0.24632379412651062, "Y": 0.06640969216823578}, {"X": 0.1970328390598297, "Y": 0.06640558689832687}]}, "Id": "131dc3a0-fc43-41a4-8e02-bf5baab62f3c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SOUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055649321526288986, "Height": 0.009173947386443615, "Left": 0.2511528432369232, "Top": 0.057365432381629944}, "Polygon": [{"X": 0.25115302205085754, "Y": 0.057365432381629944}, {"X": 0.3068021535873413, "Y": 0.057370081543922424}, {"X": 0.3068019449710846, "Y": 0.06653937697410583}, {"X": 0.2511528432369232, "Y": 0.06653474271297455}]}, "Id": "f7939724-7b36-4ba7-837a-9d6853d04e56"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "SUITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047042135149240494, "Height": 0.009005194529891014, "Left": 0.3114703893661499, "Top": 0.05741887912154198}, "Polygon": [{"X": 0.311470627784729, "Y": 0.05741887912154198}, {"X": 0.3585125207901001, "Y": 0.057422809302806854}, {"X": 0.3585122525691986, "Y": 0.06642407178878784}, {"X": 0.3114703893661499, "Y": 0.06642015278339386}]}, "Id": "818894ab-4419-4561-9768-038461bd49b7"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025080230087041855, "Height": 0.009013588540256023, "Left": 0.36310112476348877, "Top": 0.05738769471645355}, "Polygon": [{"X": 0.36310142278671265, "Y": 0.05738769471645355}, {"X": 0.3881813585758209, "Y": 0.057389792054891586}, {"X": 0.38818106055259705, "Y": 0.06640128791332245}, {"X": 0.36310112476348877, "Y": 0.06639919430017471}]}, "Id": "5867e4f0-f87e-4805-a4fd-4940acbc5fba"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PEACHTREE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09732238203287125, "Height": 0.008848474361002445, "Left": 0.09354114532470703, "Top": 0.0711643174290657}, "Polygon": [{"X": 0.09354116022586823, "Y": 0.0711643174290657}, {"X": 0.19086353480815887, "Y": 0.07117241621017456}, {"X": 0.19086341559886932, "Y": 0.08001279085874557}, {"X": 0.09354114532470703, "Y": 0.08000471442937851}]}, "Id": "7e05a0b5-6aaf-4ed4-b45c-a388b14562db"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "CORNERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08112544566392899, "Height": 0.010515091009438038, "Left": 0.19548021256923676, "Top": 0.07112227380275726}, "Polygon": [{"X": 0.1954803466796875, "Y": 0.07112227380275726}, {"X": 0.27660566568374634, "Y": 0.07112902402877808}, {"X": 0.27660542726516724, "Y": 0.08163736015558243}, {"X": 0.19548021256923676, "Y": 0.0816306322813034}]}, "Id": "28c34414-51f8-4a79-b960-76b97c0d4e47"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02413923107087612, "Height": 0.008911184035241604, "Left": 0.28136369585990906, "Top": 0.07115468382835388}, "Polygon": [{"X": 0.2813638746738434, "Y": 0.07115468382835388}, {"X": 0.30550292134284973, "Y": 0.07115668803453445}, {"X": 0.30550268292427063, "Y": 0.08006586879491806}, {"X": 0.28136369585990906, "Y": 0.08006386458873749}]}, "Id": "76682dde-7cf0-41da-b124-3ebd22a317a6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "30092", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04137367755174637, "Height": 0.008909432217478752, "Left": 0.3098423480987549, "Top": 0.07111034542322159}, "Polygon": [{"X": 0.3098425567150116, "Y": 0.07111034542322159}, {"X": 0.35121601819992065, "Y": 0.07111378759145737}, {"X": 0.35121574997901917, "Y": 0.08001977950334549}, {"X": 0.3098423480987549, "Y": 0.08001634478569031}]}, "Id": "f446c110-f9aa-4e25-8c2b-f9e3e13293c7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "770-414-1929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09453842788934708, "Height": 0.009097663685679436, "Left": 0.1752629578113556, "Top": 0.08471370488405228}, "Polygon": [{"X": 0.17526306211948395, "Y": 0.08471370488405228}, {"X": 0.26980137825012207, "Y": 0.08472154289484024}, {"X": 0.26980119943618774, "Y": 0.09381137043237686}, {"X": 0.1752629578113556, "Y": 0.09380354732275009}]}, "Id": "b6ebbc1b-ad12-4c85-8006-9d59ea77f020"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03129667416214943, "Height": 0.008876536041498184, "Left": 0.14908289909362793, "Top": 0.09846191108226776}, "Polygon": [{"X": 0.1490829735994339, "Y": 0.09846191108226776}, {"X": 0.18037958443164825, "Y": 0.0984644964337349}, {"X": 0.1803794801235199, "Y": 0.10733845084905624}, {"X": 0.14908289909362793, "Y": 0.10733587294816971}]}, "Id": "98f80a0b-db44-4695-888e-e5cfec5cbb53"}, {"BlockType": "WORD", "Confidence": 99.267578125, "Text": "ID#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02633083052933216, "Height": 0.00882148277014494, "Left": 0.1851007491350174, "Top": 0.09846045821905136}, "Polygon": [{"X": 0.18510085344314575, "Y": 0.09846045821905136}, {"X": 0.2114315778017044, "Y": 0.09846263378858566}, {"X": 0.21143145859241486, "Y": 0.10728194564580917}, {"X": 0.1851007491350174, "Y": 0.10727977752685547}]}, "Id": "03e17cb0-04bc-4264-8264-1195ddd26d28"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "45-3087555", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08108128607273102, "Height": 0.009234100580215454, "Left": 0.21485885977745056, "Top": 0.09827098995447159}, "Polygon": [{"X": 0.2148589938879013, "Y": 0.09827098995447159}, {"X": 0.2959401309490204, "Y": 0.09827768802642822}, {"X": 0.29593992233276367, "Y": 0.10750509053468704}, {"X": 0.21485885977745056, "Y": 0.1074984148144722}]}, "Id": "56b1ad8b-fb65-43d0-8f59-766badd03595"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "15:20:43", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08512918651103973, "Height": 0.013200118206441402, "Left": 0.07643646001815796, "Top": 0.12812015414237976}, "Polygon": [{"X": 0.07643646001815796, "Y": 0.12812015414237976}, {"X": 0.1615656465291977, "Y": 0.12812712788581848}, {"X": 0.16156552731990814, "Y": 0.14132027328014374}, {"X": 0.07643646746873856, "Y": 0.1413133442401886}]}, "Id": "7ea4713b-0983-4694-b9e6-c76409a89295"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "September", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10761868208646774, "Height": 0.01699734292924404, "Left": 0.1703573614358902, "Top": 0.12794870138168335}, "Polygon": [{"X": 0.17035752534866333, "Y": 0.12794870138168335}, {"X": 0.27797603607177734, "Y": 0.1279575079679489}, {"X": 0.2779756784439087, "Y": 0.14494603872299194}, {"X": 0.1703573614358902, "Y": 0.14493727684020996}]}, "Id": "43675605-a00d-4acb-9c33-9a2a55ece214"}, {"BlockType": "WORD", "Confidence": 99.462890625, "Text": "19,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028585562482476234, "Height": 0.015883250162005424, "Left": 0.28576818108558655, "Top": 0.12803564965724945}, "Polygon": [{"X": 0.2857685387134552, "Y": 0.12803564965724945}, {"X": 0.31435373425483704, "Y": 0.12803798913955688}, {"X": 0.314353346824646, "Y": 0.14391890168190002}, {"X": 0.28576818108558655, "Y": 0.14391657710075378}]}, "Id": "b3c5b61c-10e8-4187-8774-40901def538c"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0489613339304924, "Height": 0.012772844173014164, "Left": 0.3217395544052124, "Top": 0.12831586599349976}, "Polygon": [{"X": 0.32173991203308105, "Y": 0.12831586599349976}, {"X": 0.3707008957862854, "Y": 0.1283198744058609}, {"X": 0.370700478553772, "Y": 0.14108870923519135}, {"X": 0.3217395544052124, "Y": 0.1410847157239914}]}, "Id": "bc409b12-6502-48eb-94b8-520cce7bce39"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05189066752791405, "Height": 0.011649359948933125, "Left": 0.008145999163389206, "Top": 0.16235260665416718}, "Polygon": [{"X": 0.008145999163389206, "Y": 0.16235260665416718}, {"X": 0.06003664433956146, "Y": 0.16235680878162384}, {"X": 0.06003666669130325, "Y": 0.17400196194648743}, {"X": 0.008146089501678944, "Y": 0.17399777472019196}]}, "Id": "1d97963f-8c76-484d-957c-68369b50dcbd"}, {"BlockType": "WORD", "Confidence": 98.857421875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012495717965066433, "Height": 0.008767912164330482, "Left": 0.06371108442544937, "Top": 0.16267652809619904}, "Polygon": [{"X": 0.06371108442544937, "Y": 0.16267652809619904}, {"X": 0.07620680332183838, "Y": 0.16267754137516022}, {"X": 0.07620680332183838, "Y": 0.17144444584846497}, {"X": 0.06371109932661057, "Y": 0.17144343256950378}]}, "Id": "8a9964a0-7586-4a2d-a859-54e993c4575c"}, {"BlockType": "WORD", "Confidence": 98.98457336425781, "Text": "cf513bca-8e3c-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10397268831729889, "Height": 0.009087328799068928, "Left": 0.21867360174655914, "Top": 0.16255013644695282}, "Polygon": [{"X": 0.2186737358570099, "Y": 0.16255013644695282}, {"X": 0.32264629006385803, "Y": 0.16255855560302734}, {"X": 0.32264605164527893, "Y": 0.17163746058940887}, {"X": 0.21867360174655914, "Y": 0.17162905633449554}]}, "Id": "ba425659-2f4a-41b4-92d8-92f496cfa27a"}, {"BlockType": "WORD", "Confidence": 99.2833251953125, "Text": "476d-ad19-17e9202168b3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17760580778121948, "Height": 0.009221995249390602, "Left": 0.2178833931684494, "Top": 0.17592766880989075}, "Polygon": [{"X": 0.21788354218006134, "Y": 0.17592766880989075}, {"X": 0.3954892158508301, "Y": 0.17594198882579803}, {"X": 0.3954888880252838, "Y": 0.1851496696472168}, {"X": 0.2178833931684494, "Y": 0.1851353794336319}]}, "Id": "e69a052f-9a62-4f48-b6e0-69ec3e549f70"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06375987082719803, "Height": 0.008999418467283249, "Left": 0.008231271989643574, "Top": 0.19816763699054718}, "Polygon": [{"X": 0.008231271989643574, "Y": 0.19816763699054718}, {"X": 0.07199113070964813, "Y": 0.19817274808883667}, {"X": 0.07199113816022873, "Y": 0.20716705918312073}, {"X": 0.00823134183883667, "Y": 0.20716196298599243}]}, "Id": "bc7f9011-b527-4f3b-9760-c434b5990c54"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "PFS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02782253362238407, "Height": 0.00905405730009079, "Left": 0.21833477914333344, "Top": 0.19812463223934174}, "Polygon": [{"X": 0.21833491325378418, "Y": 0.19812463223934174}, {"X": 0.24615731835365295, "Y": 0.19812685251235962}, {"X": 0.24615713953971863, "Y": 0.20717868208885193}, {"X": 0.21833477914333344, "Y": 0.20717646181583405}]}, "Id": "82effffe-e6d7-43ed-9f6d-216f20337498"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "CINCINNATI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09862416982650757, "Height": 0.008937426842749119, "Left": 0.25090229511260986, "Top": 0.1981545239686966}, "Polygon": [{"X": 0.2509024441242218, "Y": 0.1981545239686966}, {"X": 0.34952646493911743, "Y": 0.19816242158412933}, {"X": 0.34952619671821594, "Y": 0.20709194242954254}, {"X": 0.25090229511260986, "Y": 0.2070840746164322}]}, "Id": "ef72e2b5-d208-45ae-93e1-8ff9c9f3f485"}, {"BlockType": "WORD", "Confidence": 99.61674499511719, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0245934147387743, "Height": 0.00882562156766653, "Left": 0.35287681221961975, "Top": 0.1982838213443756}, "Polygon": [{"X": 0.35287708044052124, "Y": 0.1982838213443756}, {"X": 0.3774702250957489, "Y": 0.1982858031988144}, {"X": 0.3774699568748474, "Y": 0.2071094512939453}, {"X": 0.35287681221961975, "Y": 0.20710748434066772}]}, "Id": "783d3673-cdb3-4f0f-9d34-7fbb16040e17"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038915205746889114, "Height": 0.008921999484300613, "Left": 0.008132929913699627, "Top": 0.2338302731513977}, "Polygon": [{"X": 0.008132929913699627, "Y": 0.2338302731513977}, {"X": 0.04704810678958893, "Y": 0.23383334279060364}, {"X": 0.047048136591911316, "Y": 0.24275226891040802}, {"X": 0.008132999762892723, "Y": 0.2427491992712021}]}, "Id": "30dcbdbb-aa43-4a58-b496-3c2d8299e0cf"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03518103435635567, "Height": 0.008796993643045425, "Left": 0.05085452273488045, "Top": 0.2338821291923523}, "Polygon": [{"X": 0.05085452273488045, "Y": 0.2338821291923523}, {"X": 0.08603555709123611, "Y": 0.23388491570949554}, {"X": 0.08603554964065552, "Y": 0.24267911911010742}, {"X": 0.05085454881191254, "Y": 0.24267634749412537}]}, "Id": "19f49788-98c8-4121-8c84-5c08628a2591"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2025-09-19", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07868044823408127, "Height": 0.009164560586214066, "Left": 0.21833966672420502, "Top": 0.23379871249198914}, "Polygon": [{"X": 0.21833980083465576, "Y": 0.23379871249198914}, {"X": 0.2970201075077057, "Y": 0.23380494117736816}, {"X": 0.297019898891449, "Y": 0.2429632693529129}, {"X": 0.21833966672420502, "Y": 0.24295705556869507}]}, "Id": "598d65ec-ecfe-4fcd-9b7c-9aee282ccbdb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02498807944357395, "Height": 0.008965135551989079, "Left": 0.00849233753979206, "Top": 0.2514283061027527}, "Polygon": [{"X": 0.00849233753979206, "Y": 0.2514283061027527}, {"X": 0.03348037227988243, "Y": 0.2514302730560303}, {"X": 0.03348041698336601, "Y": 0.2603934407234192}, {"X": 0.008492406457662582, "Y": 0.2603914737701416}]}, "Id": "75e4021c-c9f9-48fe-b588-79734a0d5e75"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03900134190917015, "Height": 0.008897394873201847, "Left": 0.03803890943527222, "Top": 0.2514796257019043}, "Polygon": [{"X": 0.03803890943527222, "Y": 0.2514796257019043}, {"X": 0.07704025506973267, "Y": 0.25148269534111023}, {"X": 0.07704025506973267, "Y": 0.26037701964378357}, {"X": 0.0380389504134655, "Y": 0.26037395000457764}]}, "Id": "887c7dd0-8b2a-4937-8641-0fb37daeccd1"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "RCOD30274", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08860987424850464, "Height": 0.009075343608856201, "Left": 0.218523308634758, "Top": 0.2515418529510498}, "Polygon": [{"X": 0.21852344274520874, "Y": 0.2515418529510498}, {"X": 0.30713319778442383, "Y": 0.2515488266944885}, {"X": 0.3071329593658447, "Y": 0.260617196559906}, {"X": 0.218523308634758, "Y": 0.2606102228164673}]}, "Id": "22ac6341-194f-4043-a792-d906e91980ca"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05119946599006653, "Height": 0.008980024605989456, "Left": 0.008698731660842896, "Top": 0.26940613985061646}, "Polygon": [{"X": 0.008698731660842896, "Y": 0.26940613985061646}, {"X": 0.05989817902445793, "Y": 0.2694101631641388}, {"X": 0.059898197650909424, "Y": 0.2783861756324768}, {"X": 0.008698800578713417, "Y": 0.27838218212127686}]}, "Id": "0a99f0cf-a307-4428-8ffc-a17a39c195dd"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "DREAM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060464292764663696, "Height": 0.008709985762834549, "Left": 0.21851246058940887, "Top": 0.26958757638931274}, "Polygon": [{"X": 0.21851259469985962, "Y": 0.26958757638931274}, {"X": 0.2789767384529114, "Y": 0.2695923149585724}, {"X": 0.27897655963897705, "Y": 0.2782975733280182}, {"X": 0.21851246058940887, "Y": 0.27829283475875854}]}, "Id": "d7dd3666-9b30-4e32-9d88-9c236597f97d"}, {"BlockType": "WORD", "Confidence": 99.4140625, "Text": "LAND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04684819281101227, "Height": 0.008770881220698357, "Left": 0.28306683897972107, "Top": 0.26957982778549194}, "Polygon": [{"X": 0.2830670475959778, "Y": 0.26957982778549194}, {"X": 0.32991504669189453, "Y": 0.26958349347114563}, {"X": 0.32991480827331543, "Y": 0.27835071086883545}, {"X": 0.28306683897972107, "Y": 0.27834704518318176}]}, "Id": "28579397-3e10-48e1-a2e2-dadd2ba42be3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04030928388237953, "Height": 0.009025296196341515, "Left": 0.008433465845882893, "Top": 0.28706350922584534}, "Polygon": [{"X": 0.008433465845882893, "Y": 0.28706350922584534}, {"X": 0.04874272271990776, "Y": 0.28706663846969604}, {"X": 0.04874275252223015, "Y": 0.2960887849330902}, {"X": 0.008433534763753414, "Y": 0.2960856556892395}]}, "Id": "e23cf1a1-d806-4378-a82a-992e57537d32"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03519865870475769, "Height": 0.008858436718583107, "Left": 0.21859151124954224, "Top": 0.2872175872325897}, "Polygon": [{"X": 0.21859164535999298, "Y": 0.2872175872325897}, {"X": 0.2537901699542999, "Y": 0.2872203290462494}, {"X": 0.253790020942688, "Y": 0.2960760295391083}, {"X": 0.21859151124954224, "Y": 0.2960732877254486}]}, "Id": "908af529-8d76-4dba-bf13-465024851c24"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03815705329179764, "Height": 0.008805360645055771, "Left": 0.008498143404722214, "Top": 0.3052511513233185}, "Polygon": [{"X": 0.008498143404722214, "Y": 0.3052511513233185}, {"X": 0.046655163168907166, "Y": 0.30525410175323486}, {"X": 0.04665519669651985, "Y": 0.31405651569366455}, {"X": 0.00849821139127016, "Y": 0.31405356526374817}]}, "Id": "e743a764-a608-4877-a86a-e6aeceb994c4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015271622687578201, "Height": 0.008638577535748482, "Left": 0.22007900476455688, "Top": 0.305248886346817}, "Polygon": [{"X": 0.22007913887500763, "Y": 0.305248886346817}, {"X": 0.2353506237268448, "Y": 0.3052500784397125}, {"X": 0.23535047471523285, "Y": 0.31388744711875916}, {"X": 0.22007900476455688, "Y": 0.31388628482818604}]}, "Id": "6f64181d-a899-4b06-9cf7-55ceb1f1d711"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060900405049324036, "Height": 0.008837957866489887, "Left": 0.0081587303429842, "Top": 0.32989683747291565}, "Polygon": [{"X": 0.0081587303429842, "Y": 0.32989683747291565}, {"X": 0.06905912607908249, "Y": 0.3299015164375305}, {"X": 0.06905913352966309, "Y": 0.33873480558395386}, {"X": 0.008158798329532146, "Y": 0.338730126619339}]}, "Id": "42761d9b-481c-4abb-a061-b4972b805882"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Orders", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04557483643293381, "Height": 0.008828829042613506, "Left": 0.07331649214029312, "Top": 0.329912930727005}, "Polygon": [{"X": 0.07331649214029312, "Y": 0.329912930727005}, {"X": 0.11889132857322693, "Y": 0.32991644740104675}, {"X": 0.11889129132032394, "Y": 0.3387417793273926}, {"X": 0.07331649959087372, "Y": 0.3387382924556732}]}, "Id": "a27bfc82-11c6-4317-acad-1307cd82aa3a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05113367363810539, "Height": 0.009044213220477104, "Left": 0.21829824149608612, "Top": 0.32979652285575867}, "Polygon": [{"X": 0.21829837560653687, "Y": 0.32979652285575867}, {"X": 0.2694319188594818, "Y": 0.32980045676231384}, {"X": 0.2694317102432251, "Y": 0.3388407528400421}, {"X": 0.21829824149608612, "Y": 0.33883681893348694}]}, "Id": "82576eb1-1f77-4a9d-a8f7-6e56f9d620e5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "21927", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041989319026470184, "Height": 0.008949325419962406, "Left": 0.008274061605334282, "Top": 0.3478981852531433}, "Polygon": [{"X": 0.008274061605334282, "Y": 0.3478981852531433}, {"X": 0.05026335269212723, "Y": 0.3479014039039612}, {"X": 0.050263382494449615, "Y": 0.35684752464294434}, {"X": 0.008274130523204803, "Y": 0.35684433579444885}]}, "Id": "ca4c1e6b-d52b-4347-a6a2-db401bdfd9c2"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "AMERCAREROYAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15152399241924286, "Height": 0.008894551545381546, "Left": 0.21798090636730194, "Top": 0.3479148745536804}, "Polygon": [{"X": 0.21798104047775269, "Y": 0.3479148745536804}, {"X": 0.3695048987865448, "Y": 0.34792643785476685}, {"X": 0.3695046305656433, "Y": 0.35680943727493286}, {"X": 0.21798090636730194, "Y": 0.35679787397384644}]}, "Id": "5f52c8bf-fd3c-4844-bc5f-d3c7559ed88a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035278212279081345, "Height": 0.008906510658562183, "Left": 0.00833059847354889, "Top": 0.39025479555130005}, "Polygon": [{"X": 0.00833059847354889, "Y": 0.39025479555130005}, {"X": 0.04360877722501755, "Y": 0.39025744795799255}, {"X": 0.043608810752630234, "Y": 0.39916130900382996}, {"X": 0.00833066739141941, "Y": 0.39915865659713745}]}, "Id": "724ff151-c0c0-464d-95f4-315766707a46"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Initial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03961243852972984, "Height": 0.008951087482273579, "Left": 0.04826044291257858, "Top": 0.3901962637901306}, "Polygon": [{"X": 0.04826044291257858, "Y": 0.3901962637901306}, {"X": 0.08787287771701813, "Y": 0.3901992440223694}, {"X": 0.08787287026643753, "Y": 0.3991473615169525}, {"X": 0.04826047271490097, "Y": 0.39914438128471375}]}, "Id": "9a3af3c7-f56c-4149-ac8a-d48a47ef65ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04865733161568642, "Height": 0.009007702581584454, "Left": 0.09229180961847305, "Top": 0.3902560770511627}, "Polygon": [{"X": 0.09229182451963425, "Y": 0.3902560770511627}, {"X": 0.14094914495944977, "Y": 0.3902597427368164}, {"X": 0.140949085354805, "Y": 0.39926376938819885}, {"X": 0.09229180961847305, "Y": 0.39926010370254517}]}, "Id": "e871cd58-e32e-4ad5-b6d5-898aee59cf72"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037960849702358246, "Height": 0.009056627750396729, "Left": 0.21835647523403168, "Top": 0.39030709862709045}, "Polygon": [{"X": 0.21835660934448242, "Y": 0.39030709862709045}, {"X": 0.2563173174858093, "Y": 0.3903099596500397}, {"X": 0.256317138671875, "Y": 0.3993637263774872}, {"X": 0.21835647523403168, "Y": 0.39936086535453796}]}, "Id": "9e201722-5825-4428-b7d8-6452079bdc06"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03532830625772476, "Height": 0.00904824212193489, "Left": 0.008309916593134403, "Top": 0.4081292748451233}, "Polygon": [{"X": 0.008309916593134403, "Y": 0.4081292748451233}, {"X": 0.043638188391923904, "Y": 0.4081318974494934}, {"X": 0.04363822191953659, "Y": 0.4171774983406067}, {"X": 0.008309987373650074, "Y": 0.4171748757362366}]}, "Id": "849ebdef-01cd-4f3c-a83f-d0a9da8a5b77"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Finished", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05802173539996147, "Height": 0.008876838721334934, "Left": 0.048023905605077744, "Top": 0.40823760628700256}, "Polygon": [{"X": 0.048023905605077744, "Y": 0.40823760628700256}, {"X": 0.10604564100503922, "Y": 0.40824195742607117}, {"X": 0.10604561120271683, "Y": 0.4171144664287567}, {"X": 0.04802393540740013, "Y": 0.4171101152896881}]}, "Id": "508199e2-b509-46f1-ab9f-03d8a5b01684"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04872797802090645, "Height": 0.0089422557502985, "Left": 0.11007849127054214, "Top": 0.40817850828170776}, "Polygon": [{"X": 0.11007852107286453, "Y": 0.40817850828170776}, {"X": 0.1588064730167389, "Y": 0.40818214416503906}, {"X": 0.15880638360977173, "Y": 0.4171207547187805}, {"X": 0.11007849127054214, "Y": 0.4171171188354492}]}, "Id": "3eeeb30d-81b1-4376-b617-caa3e7153d2f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01632171869277954, "Height": 0.008703814819455147, "Left": 0.21884691715240479, "Top": 0.40841296315193176}, "Polygon": [{"X": 0.21884705126285553, "Y": 0.40841296315193176}, {"X": 0.23516863584518433, "Y": 0.40841418504714966}, {"X": 0.2351684868335724, "Y": 0.41711676120758057}, {"X": 0.21884691715240479, "Y": 0.41711553931236267}]}, "Id": "2bb63a9a-e6d6-4648-bff5-d1a3746e6efe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03509838879108429, "Height": 0.008754033595323563, "Left": 0.00836922973394394, "Top": 0.42595890164375305}, "Polygon": [{"X": 0.00836922973394394, "Y": 0.42595890164375305}, {"X": 0.04346758499741554, "Y": 0.4259614944458008}, {"X": 0.04346761852502823, "Y": 0.4347129166126251}, {"X": 0.008369297720491886, "Y": 0.4347103238105774}]}, "Id": "fb6f0309-99d9-4a81-bfd5-401d8dcde6dc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032436251640319824, "Height": 0.008754518814384937, "Left": 0.04822363331913948, "Top": 0.42604267597198486}, "Polygon": [{"X": 0.04822363331913948, "Y": 0.42604267597198486}, {"X": 0.0806598886847496, "Y": 0.42604508996009827}, {"X": 0.080659881234169, "Y": 0.4347971975803375}, {"X": 0.04822366312146187, "Y": 0.4347947835922241}]}, "Id": "5f113865-8449-438f-acee-4f713fb77444"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Count:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04473481327295303, "Height": 0.008665968663990498, "Left": 0.08509404957294464, "Top": 0.42606261372566223}, "Polygon": [{"X": 0.08509405702352524, "Y": 0.42606261372566223}, {"X": 0.12982885539531708, "Y": 0.42606595158576965}, {"X": 0.1298288106918335, "Y": 0.43472856283187866}, {"X": 0.08509404957294464, "Y": 0.43472525477409363}]}, "Id": "a3da2a04-9d69-4173-888a-b37da3a61b96"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1909", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03227212280035019, "Height": 0.009036189876496792, "Left": 0.21987150609493256, "Top": 0.4259832799434662}, "Polygon": [{"X": 0.2198716402053833, "Y": 0.4259832799434662}, {"X": 0.25214362144470215, "Y": 0.4259856641292572}, {"X": 0.2521434724330902, "Y": 0.43501946330070496}, {"X": 0.21987150609493256, "Y": 0.43501707911491394}]}, "Id": "79f75a30-8ecb-4555-a359-78d94e59f45d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046501535922288895, "Height": 0.008947905153036118, "Left": 0.008214366622269154, "Top": 0.44387173652648926}, "Polygon": [{"X": 0.008214366622269154, "Y": 0.44387173652648926}, {"X": 0.05471587926149368, "Y": 0.44387516379356384}, {"X": 0.054715901613235474, "Y": 0.4528196454048157}, {"X": 0.00821443647146225, "Y": 0.4528162181377411}]}, "Id": "529bae46-1b37-4105-8e48-a7f96256d0ce"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05947277694940567, "Height": 0.00888256449252367, "Left": 0.05784406512975693, "Top": 0.44389593601226807}, "Polygon": [{"X": 0.05784406512975693, "Y": 0.44389593601226807}, {"X": 0.1173168420791626, "Y": 0.44390034675598145}, {"X": 0.11731680482625961, "Y": 0.45277848839759827}, {"X": 0.05784408748149872, "Y": 0.4527741074562073}]}, "Id": "d9335c34-7b23-4b6d-90c8-9f05dda74ed8"}, {"BlockType": "WORD", "Confidence": 75.65130615234375, "Text": "x", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0085075031965971, "Height": 0.006549193523824215, "Left": 0.21835273504257202, "Top": 0.44640815258026123}, "Polygon": [{"X": 0.21835282444953918, "Y": 0.44640815258026123}, {"X": 0.22686024010181427, "Y": 0.44640877842903137}, {"X": 0.22686012089252472, "Y": 0.4529573321342468}, {"X": 0.21835273504257202, "Y": 0.4529567062854767}]}, "Id": "fdd93ce1-7499-4f40-ba1a-1409011dff9c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05015705153346062, "Height": 0.008945145644247532, "Left": 0.008351953700184822, "Top": 0.4615977108478546}, "Polygon": [{"X": 0.008351953700184822, "Y": 0.4615977108478546}, {"X": 0.0585089847445488, "Y": 0.4616014063358307}, {"X": 0.05850900337100029, "Y": 0.47054287791252136}, {"X": 0.008352022618055344, "Y": 0.4705391824245453}]}, "Id": "a2e028bf-05a5-4fb3-8ad1-fcf419ef64f2"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05915329232811928, "Height": 0.00902527291327715, "Left": 0.06179133430123329, "Top": 0.4615471661090851}, "Polygon": [{"X": 0.06179133430123329, "Y": 0.4615471661090851}, {"X": 0.12094462662935257, "Y": 0.4615515172481537}, {"X": 0.12094458192586899, "Y": 0.47057244181632996}, {"X": 0.061791349202394485, "Y": 0.47056812047958374}]}, "Id": "29ce85c1-440a-4a20-821c-341f4200f124"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BOL:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037209197878837585, "Height": 0.008752327412366867, "Left": 0.008386384695768356, "Top": 0.4796783924102783}, "Polygon": [{"X": 0.008386384695768356, "Y": 0.4796783924102783}, {"X": 0.045595552772283554, "Y": 0.479681134223938}, {"X": 0.04559558257460594, "Y": 0.4884307384490967}, {"X": 0.008386452682316303, "Y": 0.4884280264377594}]}, "Id": "a3fc7b51-441e-4c40-b34a-40ce8a2b9d4e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07628358155488968, "Height": 0.008733326569199562, "Left": 0.008561443537473679, "Top": 0.4974142014980316}, "Polygon": [{"X": 0.008561443537473679, "Y": 0.4974142014980316}, {"X": 0.08484502136707306, "Y": 0.4974197447299957}, {"X": 0.08484501391649246, "Y": 0.5061475038528442}, {"X": 0.008561510592699051, "Y": 0.5061419606208801}]}, "Id": "feb1e5ea-7e90-4702-afbb-b5b600f3f52f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Canned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05125569924712181, "Height": 0.00868691224604845, "Left": 0.008709277957677841, "Top": 0.5151969194412231}, "Polygon": [{"X": 0.008709277957677841, "Y": 0.5151969194412231}, {"X": 0.05996495857834816, "Y": 0.5152006149291992}, {"X": 0.05996497720479965, "Y": 0.5238838195800781}, {"X": 0.008709345012903214, "Y": 0.523880124092102}]}, "Id": "2e21dbaf-3a7f-45a2-9ab2-a01f91c60a1c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07641203701496124, "Height": 0.008746814914047718, "Left": 0.06409433484077454, "Top": 0.5152838230133057}, "Polygon": [{"X": 0.06409433484077454, "Y": 0.5152838230133057}, {"X": 0.14050637185573578, "Y": 0.5152893662452698}, {"X": 0.140506312251091, "Y": 0.5240306258201599}, {"X": 0.06409434974193573, "Y": 0.5240251421928406}]}, "Id": "c74a457e-6ca2-4ff1-8cff-cb8e3700b837"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Unloaders:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07250798493623734, "Height": 0.009071151725947857, "Left": 0.008508577011525631, "Top": 0.5326732397079468}, "Polygon": [{"X": 0.008508577011525631, "Y": 0.5326732397079468}, {"X": 0.08101656287908554, "Y": 0.5326784253120422}, {"X": 0.08101656287908554, "Y": 0.5417444109916687}, {"X": 0.008508646860718727, "Y": 0.5417391657829285}]}, "Id": "ccd2ecbd-40cd-4f87-a845-b900b64d1edd"}, {"BlockType": "WORD", "Confidence": 99.2432632446289, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004739221651107073, "Height": 0.008766092360019684, "Left": 0.22023016214370728, "Top": 0.5329622626304626}, "Polygon": [{"X": 0.22023029625415802, "Y": 0.5329622626304626}, {"X": 0.22496938705444336, "Y": 0.5329626202583313}, {"X": 0.22496925294399261, "Y": 0.5417283773422241}, {"X": 0.22023016214370728, "Y": 0.5417280197143555}]}, "Id": "5b884573-3b9c-40c9-b13b-76798c80ebee"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Add", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029056504368782043, "Height": 0.009029047563672066, "Left": 0.008549115620553493, "Top": 0.5577662587165833}, "Polygon": [{"X": 0.008549115620553493, "Y": 0.5577662587165833}, {"X": 0.03760557994246483, "Y": 0.5577683448791504}, {"X": 0.03760562092065811, "Y": 0.5667953491210938}, {"X": 0.00854918546974659, "Y": 0.5667932629585266}]}, "Id": "7d7ec592-4d69-4c42-93f6-cf32d33c2289"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024117058143019676, "Height": 0.008773848414421082, "Left": 0.041732750833034515, "Top": 0.558020830154419}, "Polygon": [{"X": 0.041732750833034515, "Y": 0.558020830154419}, {"X": 0.06584979593753815, "Y": 0.5580225586891174}, {"X": 0.06584980338811874, "Y": 0.5667946934700012}, {"X": 0.0417327843606472, "Y": 0.5667929649353027}]}, "Id": "fd6699c4-54de-4557-9711-f26b1f8f540a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025063415989279747, "Height": 0.008660158142447472, "Left": 0.008512496948242188, "Top": 0.5759239792823792}, "Polygon": [{"X": 0.008512496948242188, "Y": 0.5759239792823792}, {"X": 0.0335758700966835, "Y": 0.5759257674217224}, {"X": 0.033575911074876785, "Y": 0.58458411693573}, {"X": 0.008512563072144985, "Y": 0.5845823884010315}]}, "Id": "11d8b4aa-3bf0-4e26-8af8-5cdc3419e7c2"}, {"BlockType": "WORD", "Confidence": 99.677734375, "Text": "21927", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041998594999313354, "Height": 0.008939801715314388, "Left": 0.03869083523750305, "Top": 0.5756234526634216}, "Polygon": [{"X": 0.03869083523750305, "Y": 0.5756234526634216}, {"X": 0.0806894302368164, "Y": 0.5756264328956604}, {"X": 0.0806894302368164, "Y": 0.5845632553100586}, {"X": 0.038690872490406036, "Y": 0.5845603346824646}]}, "Id": "194e1598-dec2-4fe9-860e-920e9861d725"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Pinwheeled", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0788310319185257, "Height": 0.008955278433859348, "Left": 0.040311772376298904, "Top": 0.5934683680534363}, "Polygon": [{"X": 0.040311772376298904, "Y": 0.5934683680534363}, {"X": 0.1191428080201149, "Y": 0.5934739112854004}, {"X": 0.11914277076721191, "Y": 0.6024236083030701}, {"X": 0.04031180962920189, "Y": 0.602418065071106}]}, "Id": "71aa5250-1b16-4511-b62b-9565b5cd779e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$15.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04616813734173775, "Height": 0.010342037305235863, "Left": 0.21866139769554138, "Top": 0.5929902791976929}, "Polygon": [{"X": 0.21866156160831451, "Y": 0.5929902791976929}, {"X": 0.26482954621315, "Y": 0.5929935574531555}, {"X": 0.2648293375968933, "Y": 0.6033323407173157}, {"X": 0.21866139769554138, "Y": 0.6033291220664978}]}, "Id": "1c3248be-74d3-4045-8fd9-504d94444abe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035189248621463776, "Height": 0.009083198383450508, "Left": 0.008433337323367596, "Top": 0.6111547946929932}, "Polygon": [{"X": 0.008433337323367596, "Y": 0.6111547946929932}, {"X": 0.04362254962325096, "Y": 0.611157238483429}, {"X": 0.043622586876153946, "Y": 0.6202379465103149}, {"X": 0.008433407172560692, "Y": 0.6202355027198792}]}, "Id": "4093a2ab-2449-471b-88af-afae13775229"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Add", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029038095846772194, "Height": 0.009006215259432793, "Left": 0.04800542816519737, "Top": 0.6111249327659607}, "Polygon": [{"X": 0.04800542816519737, "Y": 0.6111249327659607}, {"X": 0.07704352587461472, "Y": 0.6111269593238831}, {"X": 0.07704352587461472, "Y": 0.6201311349868774}, {"X": 0.04800545796751976, "Y": 0.6201291084289551}]}, "Id": "fb674a8b-d373-4cf4-be73-67850ae4828d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charges:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05849364027380943, "Height": 0.011392063461244106, "Left": 0.08144969493150711, "Top": 0.6111717820167542}, "Polygon": [{"X": 0.08144969493150711, "Y": 0.6111717820167542}, {"X": 0.13994333148002625, "Y": 0.6111758351325989}, {"X": 0.13994325697422028, "Y": 0.6225638389587402}, {"X": 0.08144969493150711, "Y": 0.6225597858428955}]}, "Id": "7707fd81-f293-4116-b5d0-cccd5ae58d6a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$15.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0460163876414299, "Height": 0.01031513512134552, "Left": 0.2187175303697586, "Top": 0.6107532978057861}, "Polygon": [{"X": 0.21871767938137054, "Y": 0.6107532978057861}, {"X": 0.2647339105606079, "Y": 0.610756516456604}, {"X": 0.2647337019443512, "Y": 0.6210684776306152}, {"X": 0.2187175303697586, "Y": 0.6210652589797974}]}, "Id": "a16f7c69-c1a6-4590-984e-21dd10c60dde"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Base", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03271251916885376, "Height": 0.008780866861343384, "Left": 0.008403296582400799, "Top": 0.6294850707054138}, "Polygon": [{"X": 0.008403296582400799, "Y": 0.6294850707054138}, {"X": 0.04111577570438385, "Y": 0.6294873356819153}, {"X": 0.041115812957286835, "Y": 0.6382659673690796}, {"X": 0.008403363637626171, "Y": 0.6382637023925781}]}, "Id": "a5a1f2f5-9d20-4b80-8b8c-919632f79630"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051887694746255875, "Height": 0.011164069175720215, "Left": 0.04553024470806122, "Top": 0.6291519999504089}, "Polygon": [{"X": 0.04553024470806122, "Y": 0.6291519999504089}, {"X": 0.0974179357290268, "Y": 0.6291556358337402}, {"X": 0.097417913377285, "Y": 0.6403160691261292}, {"X": 0.0455302856862545, "Y": 0.6403124928474426}]}, "Id": "529d4f03-9271-4fe5-ae0f-04e87140da38"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "435.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046525608748197556, "Height": 0.008861579932272434, "Left": 0.2179958075284958, "Top": 0.6291859745979309}, "Polygon": [{"X": 0.21799594163894653, "Y": 0.6291859745979309}, {"X": 0.26452142000198364, "Y": 0.6291891932487488}, {"X": 0.2645212411880493, "Y": 0.6380475759506226}, {"X": 0.2179958075284958, "Y": 0.6380443572998047}]}, "Id": "ecc90214-36db-43ed-bf51-ce92df2dbac4"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08744331449270248, "Height": 0.009215554222464561, "Left": 0.008685870096087456, "Top": 0.6466784477233887}, "Polygon": [{"X": 0.008685870096087456, "Y": 0.6466784477233887}, {"X": 0.09612918645143509, "Y": 0.6466845273971558}, {"X": 0.0961291640996933, "Y": 0.6558940410614014}, {"X": 0.008685940876603127, "Y": 0.655888020992279}]}, "Id": "41fa52d1-2fea-4637-9878-0b93a6ced55e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02809102088212967, "Height": 0.008718512952327728, "Left": 0.10034191608428955, "Top": 0.6469520926475525}, "Polygon": [{"X": 0.10034193843603134, "Y": 0.6469520926475525}, {"X": 0.12843292951583862, "Y": 0.6469539999961853}, {"X": 0.12843288481235504, "Y": 0.6556705832481384}, {"X": 0.10034191608428955, "Y": 0.6556686758995056}]}, "Id": "72289a48-305d-4183-b94f-2d8233b6153c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03634907677769661, "Height": 0.008525945246219635, "Left": 0.21981187164783478, "Top": 0.6467564702033997}, "Polygon": [{"X": 0.21981200575828552, "Y": 0.6467564702033997}, {"X": 0.2561609447002411, "Y": 0.6467589735984802}, {"X": 0.25616079568862915, "Y": 0.6552824378013611}, {"X": 0.21981187164783478, "Y": 0.6552799344062805}]}, "Id": "3b006392-75f2-4bfb-a60c-c6007784dcad"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03534692898392677, "Height": 0.009072551503777504, "Left": 0.008332470431923866, "Top": 0.6648595929145813}, "Polygon": [{"X": 0.008332470431923866, "Y": 0.6648595929145813}, {"X": 0.043679364025592804, "Y": 0.6648620367050171}, {"X": 0.04367940127849579, "Y": 0.6739321351051331}, {"X": 0.008332540281116962, "Y": 0.673929750919342}]}, "Id": "1b0931cc-4dda-4b60-a2f5-b9a67f51f317"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Cost:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03451630100607872, "Height": 0.008580689318478107, "Left": 0.04826107248663902, "Top": 0.6650562286376953}, "Polygon": [{"X": 0.04826107248663902, "Y": 0.6650562286376953}, {"X": 0.08277737349271774, "Y": 0.6650586128234863}, {"X": 0.08277737349271774, "Y": 0.6736369132995605}, {"X": 0.04826110228896141, "Y": 0.6736345291137695}]}, "Id": "73a46cc7-92d2-481e-a270-04eaed231505"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "460.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04669065773487091, "Height": 0.008987697772681713, "Left": 0.21801142394542694, "Top": 0.6646637916564941}, "Polygon": [{"X": 0.21801157295703888, "Y": 0.6646637916564941}, {"X": 0.26470208168029785, "Y": 0.664667010307312}, {"X": 0.2647019028663635, "Y": 0.6736515164375305}, {"X": 0.21801142394542694, "Y": 0.6736482977867126}]}, "Id": "d4de5868-8514-40a5-80c3-d99b1152114b"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Payments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0688609927892685, "Height": 0.011400335468351841, "Left": 0.008329876698553562, "Top": 0.6895126104354858}, "Polygon": [{"X": 0.008329876698553562, "Y": 0.6895126104354858}, {"X": 0.07719086855649948, "Y": 0.6895173192024231}, {"X": 0.07719086855649948, "Y": 0.7009129524230957}, {"X": 0.008329964242875576, "Y": 0.7009083032608032}]}, "Id": "f3c394b6-a0f8-4a4b-89e6-566ff7e14352"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055709730833768845, "Height": 0.008595049381256104, "Left": 0.21832123398780823, "Top": 0.6898230314254761}, "Polygon": [{"X": 0.21832135319709778, "Y": 0.6898230314254761}, {"X": 0.2740309536457062, "Y": 0.6898268461227417}, {"X": 0.27403077483177185, "Y": 0.6984180808067322}, {"X": 0.21832123398780823, "Y": 0.6984143257141113}]}, "Id": "c50ca85e-3dcc-4f31-99cb-59c8975241b5"}, {"BlockType": "WORD", "Confidence": 98.9343490600586, "Text": "CapstonePay-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09235386550426483, "Height": 0.011640091426670551, "Left": 0.04242369532585144, "Top": 0.707446277141571}, "Polygon": [{"X": 0.04242369532585144, "Y": 0.707446277141571}, {"X": 0.13477756083011627, "Y": 0.7074525356292725}, {"X": 0.1347774863243103, "Y": 0.7190864086151123}, {"X": 0.04242374002933502, "Y": 0.7190801501274109}]}, "Id": "22880021-7246-4fe2-a483-e5203e90721f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$460.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05458301305770874, "Height": 0.010026516392827034, "Left": 0.21855401992797852, "Top": 0.7070488333702087}, "Polygon": [{"X": 0.21855416893959045, "Y": 0.7070488333702087}, {"X": 0.27313703298568726, "Y": 0.7070525288581848}, {"X": 0.27313682436943054, "Y": 0.7170753479003906}, {"X": 0.21855401992797852, "Y": 0.7170716524124146}]}, "Id": "b53554de-eb60-4061-80cb-8bd51d5129fd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "38028520", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06657826155424118, "Height": 0.008622176945209503, "Left": 0.04247520864009857, "Top": 0.7213122248649597}, "Polygon": [{"X": 0.04247520864009857, "Y": 0.7213122248649597}, {"X": 0.10905347019433975, "Y": 0.7213166952133179}, {"X": 0.10905344039201736, "Y": 0.7299343943595886}, {"X": 0.042475245893001556, "Y": 0.7299299240112305}]}, "Id": "f1c5c0c7-5598-497f-8ca0-6dd0397cf1f4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03505267575383186, "Height": 0.008894908241927624, "Left": 0.04201509431004524, "Top": 0.7433323860168457}, "Polygon": [{"X": 0.04201509431004524, "Y": 0.7433323860168457}, {"X": 0.0770677700638771, "Y": 0.7433347105979919}, {"X": 0.0770677700638771, "Y": 0.7522273063659668}, {"X": 0.04201513156294823, "Y": 0.7522249817848206}]}, "Id": "4462f6d1-5044-4783-a146-8ff247fe205c"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Payments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06524994224309921, "Height": 0.011213215067982674, "Left": 0.08160550892353058, "Top": 0.7434642910957336}, "Polygon": [{"X": 0.08160550892353058, "Y": 0.7434642910957336}, {"X": 0.1468554586172104, "Y": 0.7434686422348022}, {"X": 0.14685536921024323, "Y": 0.7546775341033936}, {"X": 0.08160550892353058, "Y": 0.754673182964325}]}, "Id": "3497382b-48a9-4e3c-b2f0-87048f80fc52"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$460.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05459902063012123, "Height": 0.010217910632491112, "Left": 0.21853196620941162, "Top": 0.7425822615623474}, "Polygon": [{"X": 0.21853211522102356, "Y": 0.7425822615623474}, {"X": 0.27313098311424255, "Y": 0.7425859570503235}, {"X": 0.27313077449798584, "Y": 0.7528002262115479}, {"X": 0.21853196620941162, "Y": 0.7527965903282166}]}, "Id": "e5306a48-c193-4bb3-a9f4-6a6b22c1b2f8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "31e9435a-7be8-4424-8d1b-ba51452544d4", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "31e9435a-7be8-4424-8d1b-ba51452544d4", "content-type": "application/x-amz-json-1.1", "content-length": "81484", "date": "Wed, 24 Sep 2025 13:26:48 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "9c5485d4-221e-4254-9796-a06e867d10ae", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:51 GMT", "content-type": "application/json", "content-length": "1205", "connection": "keep-alive", "x-amzn-requestid": "9c5485d4-221e-4254-9796-a06e867d10ae"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify page1. The content looks like a receipt or invoice? It has Purchase Orders, Vendor, Total Initial Pallets, Finished Pallets, Trailer Number, etc. It includes \"Carrier: DREAM LAND\" \"Bill Code: RCOD30274\" \"Total Cost: 460.00\" \"Payments: CapstonePay\". This seems like a carrier invoice or receipt? It says \"Receipt #\" and \"Payments\". Could be an invoice. There's no HS codes, so not comm_invoice. No mention of loading/unloading labor? It has \"Unloaders:1\" and \"Add Fee\". Could be lumper_receipt? But lumper receipt is for loading/unloading labor. This has some fees and charges but seems more like a general invoice for freight charges. The presence of \"Bill Code\" and \"Total Cost\" suggests a carrier invoice. So classify as invoice.\n\nReturn JSON with documents: [{\"page_no\": 1, \"doc_type\":\"invoice\"}]"}}}, {"text": "{\n  \"documents\": [\n    {\n      \"page_no\": 1,\n      \"doc_type\": \"invoice\"\n    }\n  ]\n}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2852, "outputTokens": 231, "totalTokens": 3083}, "metrics": {"latencyMs": 1219}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/cd2a3633_KDFG6JJAZV41YZP4TEGN.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nCapstone Logistics\n30 TECHNOLOGY PKWY SOUTH SUITE 200\nPEACHTREE CORNERS, GA 30092\n770-414-1929\nFED ID# 45-3087555\n15:20:43 September 19, 2025\nReceipt #:\ncf513bca-8e3c-\n476d-ad19-17e9202168b3\nLocation:\nPFS CINCINNATI OH\nWork Date:\n2025-09-19\nBill Code:\nRCOD30274\nCarrier:\nDREAM LAND\nDock:\nDRY\nDoor:\n14\nPurchase Orders\nVendor\n21927\nAMERCAREROYAL\nTotal Initial Pallets:\n25.00\nTotal Finished Pallets:\n57\nTotal Case Count:\n1909\nTrailer Number:\nx\nTractor Number:\nBOL:\nComments:\nCanned Comments:\nUnloaders:\n1\nAdd Fee\nPO: 21927\nPinwheeled\n$15.00\nTotal Add Charges:\n$15.00\nBase Charge:\n435.00\nConvenience Fee:\n10.00\nTotal Cost:\n460.00\nPayments:\nAmount\nCapstonePay-\n$460.00\n38028520\nTotal Payments\n$460.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}