{"classification_result": {"documents": [{"page_no": 1, "doc_type": "other"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.8910360336303711, "Left": 0.0, "Top": 0.10896395891904831}, "Polygon": [{"X": 0.027758190408349037, "Y": 0.11487141996622086}, {"X": 0.9871435165405273, "Y": 0.10896395891904831}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "3a3e2b5b-88e8-4d7d-96b0-32fcb2412f24", "Relationships": [{"Type": "CHILD", "Ids": ["3565dc48-450c-4b50-82dc-c2021637617e", "4d1d18ca-c1e7-4910-8fa0-8fc1e95369a7", "a9068365-74e0-40d6-b1f8-4df99dfc6cf4", "c698bbfa-eb64-4f99-b249-5341ed5ad24f", "87e53577-c8ba-49b9-aacf-8b01fc5e22ad", "12cf9739-c334-4514-93d1-e356ad3c40cf", "f7354c16-71f6-4e37-92f9-bc32e134cc66", "887dae7d-f49c-48fe-90a1-a3fbe5c39261", "74570c09-b117-46d2-b24c-968a1c97b557", "32a1419e-f0a8-46c5-ba8a-d7b9f110a00e", "ea426f03-1574-4c28-a625-f513eb9e8c38", "fa2a0ff4-0043-4936-ae55-90f3477ee7fc", "0217d25c-7b1c-4c48-880f-f784589e4b8e", "d5dbcb09-3ed2-43af-9162-0c6aa6120a5b", "e7a3209b-21f1-411a-8be6-53418441079d", "fa6977b7-6e33-4e65-9c69-e68af2659ff4", "234c8551-1052-4931-850f-872d1346360f", "28d00fde-57a0-4a60-9be5-398a41cd22eb", "30f273a9-d6c5-4c2c-bad0-2a16f685ec57", "d5be9f12-7633-4b2b-9f21-6961739d1ce8", "bc6bd95c-c440-43c1-af52-a279bc13461f", "e311c402-bd71-42cf-91ea-de19c2862520", "7d48eba1-0552-4fe7-a122-f757d76c6e7a", "00ae2bdc-461c-4af2-a7cc-d59e15f95fc6", "9d1663fc-9914-484c-b753-08620d4d3483", "529ee47c-319e-41ca-be6e-deb4a55a68e8", "3dc6b37f-b958-4821-8e46-43827c43ff1c", "5549586f-3a24-4a5d-bf00-282dbca47500", "62a76bac-fb62-485e-ac9b-e223f60c2c78", "da147ed5-73df-41bb-aa7a-435a41e296b5", "ce8e88f5-fd2d-4290-ae0d-8dda6af0a538", "585601f0-30f1-4eef-ad39-cff7116f9fff", "18120cbb-9da0-4dfc-b86c-aa639d607135", "79ea5f0f-551f-49b8-b1ac-6f1e51460ad7", "994d7efe-a9ac-422b-a61f-a7128e37d815", "1848fdfa-2042-41ea-89ed-d8ccfdfc8105", "127760ac-f3a5-4a85-b276-a19c9f1f65e0", "4123c3dd-7b03-4d32-9363-613e2489777e", "f70d6878-3a83-4ff4-b6dd-e1cd6797f05f", "6ad65b6c-11a9-4ae9-9af0-cbaa090a77f6", "8687b74f-4956-4acf-bba5-cc398c3b8491", "328baff7-5c67-45b0-a8c5-6cdc065b9757", "06649531-de2f-40ed-b231-6303ada1a4f3", "349a9d0c-3846-4fde-b98e-485aa1cbb931", "56b7d6cc-aa34-4389-a4f7-e9ac4bc50ced", "33653218-ea9d-456c-bb05-2c9a43653909", "3f67436d-2993-4a26-840a-f62ec006f9f6", "aa8097ea-be90-4453-89ed-aeb7d69364de", "d083d7c7-cb46-41c7-95fb-fb675a5a7da1", "f665741e-b749-4b3c-8c69-784efc8b680b", "a2a3c53e-5309-4d05-8795-a6431e50a67a", "517805e1-c97f-45b8-ad10-6c2e264c2191", "2b122bef-dc49-4487-96b9-85e6bbcee4d0"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "DATE: 5-Sep", "Geometry": {"BoundingBox": {"Width": 0.11106859892606735, "Height": 0.0128299156203866, "Left": 0.2818344533443451, "Top": 0.16151244938373566}, "Polygon": [{"X": 0.2820190191268921, "Y": 0.16216953098773956}, {"X": 0.39290305972099304, "Y": 0.16151244938373566}, {"X": 0.39281409978866577, "Y": 0.17369112372398376}, {"X": 0.2818344533443451, "Y": 0.17434236407279968}]}, "Id": "3565dc48-450c-4b50-82dc-c2021637617e", "Relationships": [{"Type": "CHILD", "Ids": ["8ed10603-8aee-4471-96a7-71cfd4b98936", "1f3815ec-624f-4dab-951f-0b8c70b9ae41"]}]}, {"BlockType": "LINE", "Confidence": 99.5602798461914, "Text": "UNIT #: 6063/283420", "Geometry": {"BoundingBox": {"Width": 0.18422095477581024, "Height": 0.012787594459950924, "Left": 0.2655228078365326, "Top": 0.18916282057762146}, "Polygon": [{"X": 0.2657136917114258, "Y": 0.19022895395755768}, {"X": 0.44974374771118164, "Y": 0.18916282057762146}, {"X": 0.4497053921222687, "Y": 0.20089362561702728}, {"X": 0.2655228078365326, "Y": 0.20195041596889496}]}, "Id": "4d1d18ca-c1e7-4910-8fa0-8fc1e95369a7", "Relationships": [{"Type": "CHILD", "Ids": ["81dfc286-4780-4442-9a15-184d64d25874", "cd4caa12-b40a-4a42-a742-e361f5e689de", "1a8c58aa-15c0-47dc-a52f-ee84ea6d704f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck Ready Time:", "Geometry": {"BoundingBox": {"Width": 0.14511802792549133, "Height": 0.013614829629659653, "Left": 0.47625842690467834, "Top": 0.20126919448375702}, "Polygon": [{"X": 0.4762761890888214, "Y": 0.20210015773773193}, {"X": 0.6212631464004517, "Y": 0.20126919448375702}, {"X": 0.6213764548301697, "Y": 0.21406111121177673}, {"X": 0.47625842690467834, "Y": 0.21488402783870697}]}, "Id": "a9068365-74e0-40d6-b1f8-4df99dfc6cf4", "Relationships": [{"Type": "CHILD", "Ids": ["d837c3c1-9d1e-4958-8d91-a6e50a6f9cd2", "b60b0347-97da-4b24-b322-4414c1e97d2e", "7e3ef307-ba64-494b-b1ae-01d9197fb5ef"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.14516210556030273, "Height": 0.011497213505208492, "Left": 0.25505393743515015, "Top": 0.21835391223430634}, "Polygon": [{"X": 0.25523537397384644, "Y": 0.21917396783828735}, {"X": 0.4002160429954529, "Y": 0.21835391223430634}, {"X": 0.4001438319683075, "Y": 0.2290377914905548}, {"X": 0.25505393743515015, "Y": 0.2298511266708374}]}, "Id": "c698bbfa-eb64-4f99-b249-5341ed5ad24f", "Relationships": [{"Type": "CHILD", "Ids": ["cbfee325-4f3a-4564-925a-f81780f749bd", "6bce9811-2b32-4e1e-8c4b-5b5b10c352ef"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.13144098222255707, "Height": 0.013225018978118896, "Left": 0.47630321979522705, "Top": 0.23105917870998383}, "Polygon": [{"X": 0.4763205051422119, "Y": 0.2317933738231659}, {"X": 0.6076457500457764, "Y": 0.23105917870998383}, {"X": 0.6077442169189453, "Y": 0.2435571402311325}, {"X": 0.47630321979522705, "Y": 0.24428419768810272}]}, "Id": "87e53577-c8ba-49b9-aacf-8b01fc5e22ad", "Relationships": [{"Type": "CHILD", "Ids": ["ec4fbc5c-6c33-4f79-9b21-971d710864df", "ce8792a0-97a0-4b0d-8291-7f2da5766913"]}]}, {"BlockType": "LINE", "Confidence": 99.8009033203125, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.20548883080482483, "Height": 0.012733173556625843, "Left": 0.17284563183784485, "Top": 0.2475498616695404}, "Polygon": [{"X": 0.17310947179794312, "Y": 0.2486824095249176}, {"X": 0.3783344626426697, "Y": 0.2475498616695404}, {"X": 0.3782382607460022, "Y": 0.2591608762741089}, {"X": 0.17284563183784485, "Y": 0.26028305292129517}]}, "Id": "12cf9739-c334-4514-93d1-e356ad3c40cf", "Relationships": [{"Type": "CHILD", "Ids": ["7cd6ad8d-0fa3-47dd-b60d-ab8e5830b29f", "a3262b86-26b1-49cf-b89c-d6ed2bfc89aa", "6bbb00cd-83c3-4f9a-8a20-f443e081bb65"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.04333392530679703, "Height": 0.010078788734972477, "Left": 0.35375338792800903, "Top": 0.27318575978279114}, "Polygon": [{"X": 0.3538517653942108, "Y": 0.2734190821647644}, {"X": 0.39708730578422546, "Y": 0.27318575978279114}, {"X": 0.3970188498497009, "Y": 0.2830330729484558}, {"X": 0.35375338792800903, "Y": 0.28326454758644104}]}, "Id": "f7354c16-71f6-4e37-92f9-bc32e134cc66", "Relationships": [{"Type": "CHILD", "Ids": ["ddb19a5a-c328-4a7d-9331-9750bfef30e0"]}]}, {"BlockType": "LINE", "Confidence": 92.02088928222656, "Text": "Receipt", "Geometry": {"BoundingBox": {"Width": 0.09629561752080917, "Height": 0.01784023456275463, "Left": 0.4994954764842987, "Top": 0.2728380560874939}, "Polygon": [{"X": 0.4994954764842987, "Y": 0.27335673570632935}, {"X": 0.5956695675849915, "Y": 0.2728380560874939}, {"X": 0.5957911014556885, "Y": 0.2901668846607208}, {"X": 0.4994997978210449, "Y": 0.2906782925128937}]}, "Id": "887dae7d-f49c-48fe-90a1-a3fbe5c39261", "Relationships": [{"Type": "CHILD", "Ids": ["abf5656b-f908-4866-b391-32c8420ad6ad"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.08998044580221176, "Height": 0.01132143009454012, "Left": 0.8078932762145996, "Top": 0.27671244740486145}, "Polygon": [{"X": 0.8078932762145996, "Y": 0.27719375491142273}, {"X": 0.8975675106048584, "Y": 0.27671244740486145}, {"X": 0.8978736996650696, "Y": 0.28755682706832886}, {"X": 0.8081310391426086, "Y": 0.2880338728427887}]}, "Id": "74570c09-b117-46d2-b24c-968a1c97b557", "Relationships": [{"Type": "CHILD", "Ids": ["b00b39b8-fbe4-486d-851b-886e87eb4946", "55e1259b-4a45-47f8-842d-0cc1a1800d40"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.09023511409759521, "Height": 0.010960289277136326, "Left": 0.1974409967660904, "Top": 0.29138559103012085}, "Polygon": [{"X": 0.19766059517860413, "Y": 0.2918638586997986}, {"X": 0.2876761257648468, "Y": 0.29138559103012085}, {"X": 0.28752273321151733, "Y": 0.30187174677848816}, {"X": 0.1974409967660904, "Y": 0.3023459017276764}]}, "Id": "32a1419e-f0a8-46c5-ba8a-d7b9f110a00e", "Relationships": [{"Type": "CHILD", "Ids": ["3f278718-0ef0-452e-aef8-bc338ebbd4dd"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.02109665609896183, "Height": 0.010236918926239014, "Left": 0.3645259439945221, "Top": 0.28907278180122375}, "Polygon": [{"X": 0.3646193742752075, "Y": 0.2891845405101776}, {"X": 0.38562262058258057, "Y": 0.28907278180122375}, {"X": 0.38554415106773376, "Y": 0.29919886589050293}, {"X": 0.3645259439945221, "Y": 0.29930970072746277}]}, "Id": "ea426f03-1574-4c28-a625-f513eb9e8c38", "Relationships": [{"Type": "CHILD", "Ids": ["53a63974-b026-4f4b-8371-2fe2aa15b3ae"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.031135501340031624, "Height": 0.010444971732795238, "Left": 0.42465940117836, "Top": 0.28869664669036865}, "Polygon": [{"X": 0.42471083998680115, "Y": 0.28886207938194275}, {"X": 0.45579490065574646, "Y": 0.28869664669036865}, {"X": 0.45576590299606323, "Y": 0.2989776134490967}, {"X": 0.42465940117836, "Y": 0.29914161562919617}]}, "Id": "fa2a0ff4-0043-4936-ae55-90f3477ee7fc", "Relationships": [{"Type": "CHILD", "Ids": ["eaded07e-ab83-44b6-972a-aafda751b236"]}]}, {"BlockType": "LINE", "Confidence": 99.87955474853516, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.13326866924762726, "Height": 0.012093272991478443, "Left": 0.5686385035514832, "Top": 0.2865668535232544}, "Polygon": [{"X": 0.5686385035514832, "Y": 0.2872757017612457}, {"X": 0.7017424702644348, "Y": 0.2865668535232544}, {"X": 0.7019071578979492, "Y": 0.2979578971862793}, {"X": 0.5686966180801392, "Y": 0.29866012930870056}]}, "Id": "0217d25c-7b1c-4c48-880f-f784589e4b8e", "Relationships": [{"Type": "CHILD", "Ids": ["99cc3a59-81fb-486f-bb8a-fcdaeedfe284", "9b574b1d-2185-4445-acf6-1fd0fb648bfa", "a2728bf9-6a22-4d9e-bce8-2669ae85e972"]}]}, {"BlockType": "LINE", "Confidence": 94.1954345703125, "Text": "1025", "Geometry": {"BoundingBox": {"Width": 0.06885294616222382, "Height": 0.01676912233233452, "Left": 0.8012471795082092, "Top": 0.30962246656417847}, "Polygon": [{"X": 0.8012471795082092, "Y": 0.309979110956192}, {"X": 0.8696697354316711, "Y": 0.30962246656417847}, {"X": 0.8701000809669495, "Y": 0.3260398805141449}, {"X": 0.8015986084938049, "Y": 0.3263916075229645}]}, "Id": "d5dbcb09-3ed2-43af-9162-0c6aa6120a5b", "Relationships": [{"Type": "CHILD", "Ids": ["daf520c6-cb04-4be8-b0e3-0e1f834738c1"]}]}, {"BlockType": "LINE", "Confidence": 96.65497589111328, "Text": "3SK", "Geometry": {"BoundingBox": {"Width": 0.09110535681247711, "Height": 0.02468687854707241, "Left": 0.18377244472503662, "Top": 0.32854583859443665}, "Polygon": [{"X": 0.1843012273311615, "Y": 0.32901114225387573}, {"X": 0.2748778164386749, "Y": 0.32854583859443665}, {"X": 0.27450254559516907, "Y": 0.35277703404426575}, {"X": 0.18377244472503662, "Y": 0.3532327115535736}]}, "Id": "e7a3209b-21f1-411a-8be6-53418441079d", "Relationships": [{"Type": "CHILD", "Ids": ["b179a1bf-a052-4eb4-9886-dc1f0c102639"]}]}, {"BlockType": "LINE", "Confidence": 66.689453125, "Text": "4sk", "Geometry": {"BoundingBox": {"Width": 0.05581986531615257, "Height": 0.01808406412601471, "Left": 0.48379531502723694, "Top": 0.3220384418964386}, "Polygon": [{"X": 0.4838104546070099, "Y": 0.3223261833190918}, {"X": 0.5395607352256775, "Y": 0.3220384418964386}, {"X": 0.5396151542663574, "Y": 0.33983907103538513}, {"X": 0.48379531502723694, "Y": 0.3401224911212921}]}, "Id": "fa6977b7-6e33-4e65-9c69-e68af2659ff4", "Relationships": [{"Type": "CHILD", "Ids": ["94cfb166-1f3c-46a6-a003-67b4d0942b86"]}]}, {"BlockType": "LINE", "Confidence": 72.60951232910156, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.06918258219957352, "Height": 0.0564197413623333, "Left": 0.07948210090398788, "Top": 0.3371361196041107}, "Polygon": [{"X": 0.0811106413602829, "Y": 0.3374806046485901}, {"X": 0.1486646831035614, "Y": 0.3371361196041107}, {"X": 0.14730089902877808, "Y": 0.39322805404663086}, {"X": 0.07948210090398788, "Y": 0.3935558497905731}]}, "Id": "234c8551-1052-4931-850f-872d1346360f", "Relationships": [{"Type": "CHILD", "Ids": ["e263150e-36ff-4d8b-91e7-5a5038c3647c"]}]}, {"BlockType": "LINE", "Confidence": 91.96102905273438, "Text": "Alumalloy - Avon Lake", "Geometry": {"BoundingBox": {"Width": 0.17723138630390167, "Height": 0.01449145469814539, "Left": 0.140276238322258, "Top": 0.34995734691619873}, "Polygon": [{"X": 0.14061427116394043, "Y": 0.35084792971611023}, {"X": 0.31750762462615967, "Y": 0.34995734691619873}, {"X": 0.3173377513885498, "Y": 0.3635687828063965}, {"X": 0.140276238322258, "Y": 0.36444878578186035}]}, "Id": "28d00fde-57a0-4a60-9be5-398a41cd22eb", "Relationships": [{"Type": "CHILD", "Ids": ["b1a930c8-db59-44a7-b328-2ae4addf68d9", "6c468dd1-91c3-45a5-af23-8b85720c2951", "5768ecc8-d1b5-46e2-a51a-782ed0baad7d"]}]}, {"BlockType": "LINE", "Confidence": 95.72923278808594, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011499851010739803, "Height": 0.010871955193579197, "Left": 0.43313419818878174, "Top": 0.34896937012672424}, "Polygon": [{"X": 0.43318167328834534, "Y": 0.34902703762054443}, {"X": 0.4446340501308441, "Y": 0.34896937012672424}, {"X": 0.44459521770477295, "Y": 0.35978418588638306}, {"X": 0.43313419818878174, "Y": 0.35984131693840027}]}, "Id": "30f273a9-d6c5-4c2c-bad0-2a16f685ec57", "Relationships": [{"Type": "CHILD", "Ids": ["6a6ae969-ca3d-44d0-869e-1ae4d6c1a633"]}]}, {"BlockType": "LINE", "Confidence": 99.34550476074219, "Text": "SO-02143353 ***********", "Geometry": {"BoundingBox": {"Width": 0.1858363002538681, "Height": 0.011012443341314793, "Left": 0.4772059917449951, "Top": 0.34763118624687195}, "Polygon": [{"X": 0.47721919417381287, "Y": 0.3485666811466217}, {"X": 0.6629244685173035, "Y": 0.34763118624687195}, {"X": 0.6630423069000244, "Y": 0.3577163517475128}, {"X": 0.4772059917449951, "Y": 0.35864362120628357}]}, "Id": "d5be9f12-7633-4b2b-9f21-6961739d1ce8", "Relationships": [{"Type": "CHILD", "Ids": ["fd142bb4-a401-4bae-96b0-a6a9e0ab3b11", "1729bf99-393f-452b-8e16-b6824d7b36d1"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "1050", "Geometry": {"BoundingBox": {"Width": 0.07072294503450394, "Height": 0.01679832488298416, "Left": 0.8047999739646912, "Top": 0.3447118103504181}, "Polygon": [{"X": 0.8047999739646912, "Y": 0.34506648778915405}, {"X": 0.8750865459442139, "Y": 0.3447118103504181}, {"X": 0.8755229115486145, "Y": 0.3611605167388916}, {"X": 0.805155336856842, "Y": 0.36151012778282166}]}, "Id": "bc6bd95c-c440-43c1-af52-a279bc13461f", "Relationships": [{"Type": "CHILD", "Ids": ["76937cf9-3595-4b93-97fb-1ae6694a3429"]}]}, {"BlockType": "LINE", "Confidence": 79.7520751953125, "Text": "6SK", "Geometry": {"BoundingBox": {"Width": 0.0617954283952713, "Height": 0.021563010290265083, "Left": 0.4913441836833954, "Top": 0.4381857216358185}, "Polygon": [{"X": 0.4913509786128998, "Y": 0.4384702444076538}, {"X": 0.5530551075935364, "Y": 0.4381857216358185}, {"X": 0.5531396269798279, "Y": 0.4594700038433075}, {"X": 0.4913441836833954, "Y": 0.4597487449645996}]}, "Id": "e311c402-bd71-42cf-91ea-de19c2862520", "Relationships": [{"Type": "CHILD", "Ids": ["d17e452a-651b-4bed-973a-03a7e7e1a135"]}]}, {"BlockType": "LINE", "Confidence": 73.34171295166016, "Text": "X General 2sm Boxes Extrusion", "Geometry": {"BoundingBox": {"Width": 0.2814428508281708, "Height": 0.08059182018041611, "Left": 0.05758822709321976, "Top": 0.43955937027931213}, "Polygon": [{"X": 0.05999129265546799, "Y": 0.4408456087112427}, {"X": 0.33903107047080994, "Y": 0.43955937027931213}, {"X": 0.33816495537757874, "Y": 0.5189635157585144}, {"X": 0.05758822709321976, "Y": 0.5201511979103088}]}, "Id": "7d48eba1-0552-4fe7-a122-f757d76c6e7a", "Relationships": [{"Type": "CHILD", "Ids": ["d67bd491-12bb-432b-b5d7-f9aa297cf19b", "f0d73512-7746-4333-aeaa-a198ce2052a3", "d3c1a87f-85d6-4352-9d6b-33919e26e523", "33eecb6b-7257-4d8a-ad80-0ad88e427fb2"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1235", "Geometry": {"BoundingBox": {"Width": 0.07125698775053024, "Height": 0.017501451075077057, "Left": 0.8216785192489624, "Top": 0.4451032280921936}, "Polygon": [{"X": 0.8216785192489624, "Y": 0.4454267919063568}, {"X": 0.8924620747566223, "Y": 0.4451032280921936}, {"X": 0.8929355144500732, "Y": 0.46228650212287903}, {"X": 0.8220673203468323, "Y": 0.46260470151901245}]}, "Id": "00ae2bdc-461c-4af2-a7cc-d59e15f95fc6", "Relationships": [{"Type": "CHILD", "Ids": ["eecbd411-ad91-4160-86d4-73540c976135"]}]}, {"BlockType": "LINE", "Confidence": 95.78703308105469, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01018470712006092, "Height": 0.008606708608567715, "Left": 0.3656158447265625, "Top": 0.47279852628707886}, "Polygon": [{"X": 0.36569318175315857, "Y": 0.4728435277938843}, {"X": 0.37580054998397827, "Y": 0.47279852628707886}, {"X": 0.37572920322418213, "Y": 0.48136064410209656}, {"X": 0.3656158447265625, "Y": 0.48140525817871094}]}, "Id": "9d1663fc-9914-484c-b753-08620d4d3483", "Relationships": [{"Type": "CHILD", "Ids": ["fe4223a8-5ff9-41fc-a04f-f1d4a02372f4"]}]}, {"BlockType": "LINE", "Confidence": 97.41071319580078, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011586965061724186, "Height": 0.01153433509171009, "Left": 0.43226373195648193, "Top": 0.46973079442977905}, "Polygon": [{"X": 0.432314395904541, "Y": 0.469782292842865}, {"X": 0.44385069608688354, "Y": 0.46973079442977905}, {"X": 0.4438091814517975, "Y": 0.4812142252922058}, {"X": 0.43226373195648193, "Y": 0.481265127658844}]}, "Id": "529ee47c-319e-41ca-be6e-deb4a55a68e8", "Relationships": [{"Type": "CHILD", "Ids": ["afa2506a-b358-4379-8c0c-d6f9c2eda371"]}]}, {"BlockType": "LINE", "Confidence": 99.2385482788086, "Text": "SO-02142762 *********** ***********", "Geometry": {"BoundingBox": {"Width": 0.29036062955856323, "Height": 0.011875880882143974, "Left": 0.47236818075180054, "Top": 0.468094140291214}, "Polygon": [{"X": 0.4723854660987854, "Y": 0.4693897068500519}, {"X": 0.7625328898429871, "Y": 0.468094140291214}, {"X": 0.7627288103103638, "Y": 0.4786880612373352}, {"X": 0.47236818075180054, "Y": 0.4799700081348419}]}, "Id": "3dc6b37f-b958-4821-8e46-43827c43ff1c", "Relationships": [{"Type": "CHILD", "Ids": ["3565b8f6-4657-48a7-a30f-244071feebcc", "870f8f18-399e-4e9a-81fe-46b9dda1aae2", "597c8126-df6c-4280-abcb-6c12a7963ce6"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "2143358", "Geometry": {"BoundingBox": {"Width": 0.11974821239709854, "Height": 0.017683526501059532, "Left": 0.48226073384284973, "Top": 0.48960188031196594}, "Polygon": [{"X": 0.482276976108551, "Y": 0.4901241958141327}, {"X": 0.6018829345703125, "Y": 0.48960188031196594}, {"X": 0.6020089387893677, "Y": 0.5067722201347351}, {"X": 0.48226073384284973, "Y": 0.5072854161262512}]}, "Id": "5549586f-3a24-4a5d-bf00-282dbca47500", "Relationships": [{"Type": "CHILD", "Ids": ["000d10d7-faef-442e-9005-de78ceaed10c"]}]}, {"BlockType": "LINE", "Confidence": 94.97847747802734, "Text": "1255", "Geometry": {"BoundingBox": {"Width": 0.07768413424491882, "Height": 0.02288704551756382, "Left": 0.8251683712005615, "Top": 0.47568467259407043}, "Polygon": [{"X": 0.8251683712005615, "Y": 0.4760257303714752}, {"X": 0.9022171497344971, "Y": 0.47568467259407043}, {"X": 0.9028525352478027, "Y": 0.49823838472366333}, {"X": 0.8256829977035522, "Y": 0.4985717236995697}]}, "Id": "62a76bac-fb62-485e-ac9b-e223f60c2c78", "Relationships": [{"Type": "CHILD", "Ids": ["ad86f2e0-848b-4f66-b2e8-31fa9a11b4c4"]}]}, {"BlockType": "LINE", "Confidence": 65.80940246582031, "Text": "O Vari-Wall 2SK Tube", "Geometry": {"BoundingBox": {"Width": 0.20752987265586853, "Height": 0.0960840955376625, "Left": 0.03820807486772537, "Top": 0.5820821523666382}, "Polygon": [{"X": 0.04119022190570831, "Y": 0.5828871726989746}, {"X": 0.2457379549741745, "Y": 0.5820821523666382}, {"X": 0.2440955638885498, "Y": 0.6774490475654602}, {"X": 0.03820807486772537, "Y": 0.6781662702560425}]}, "Id": "da147ed5-73df-41bb-aa7a-435a41e296b5", "Relationships": [{"Type": "CHILD", "Ids": ["06d7a7dc-6687-47ec-986c-ec85f36d55e2", "6c3b8093-0e6a-46e9-93e4-366f4269ac65", "dacbbf3d-0d78-47ff-953c-af80e96307fe", "7aff2b3a-e86e-4ccd-86e4-7cb01aad2eb7"]}]}, {"BlockType": "LINE", "Confidence": 80.35554504394531, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.05240174010396004, "Height": 0.023495879024267197, "Left": 0.48225313425064087, "Top": 0.5860322713851929}, "Polygon": [{"X": 0.4822750389575958, "Y": 0.5862368941307068}, {"X": 0.5345929265022278, "Y": 0.5860322713851929}, {"X": 0.5346548557281494, "Y": 0.6093289256095886}, {"X": 0.48225313425064087, "Y": 0.609528124332428}]}, "Id": "ce8e88f5-fd2d-4290-ae0d-8dda6af0a538", "Relationships": [{"Type": "CHILD", "Ids": ["d697901e-7248-463d-9b10-dcda3e78052f"]}]}, {"BlockType": "LINE", "Confidence": 99.7217788696289, "Text": "1335", "Geometry": {"BoundingBox": {"Width": 0.07100424915552139, "Height": 0.019956152886152267, "Left": 0.8299850821495056, "Top": 0.5964555144309998}, "Polygon": [{"X": 0.8299850821495056, "Y": 0.596727192401886}, {"X": 0.9004416465759277, "Y": 0.5964555144309998}, {"X": 0.9009893536567688, "Y": 0.6161462664604187}, {"X": 0.830437183380127, "Y": 0.6164116859436035}]}, "Id": "585601f0-30f1-4eef-ad39-cff7116f9fff", "Relationships": [{"Type": "CHILD", "Ids": ["71a9c41a-a679-4c3a-ac52-39125ef8ae31"]}]}, {"BlockType": "LINE", "Confidence": 97.44319915771484, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010968219488859177, "Height": 0.009919018484652042, "Left": 0.36130180954933167, "Top": 0.626947283744812}, "Polygon": [{"X": 0.361393004655838, "Y": 0.6269877552986145}, {"X": 0.37227001786231995, "Y": 0.626947283744812}, {"X": 0.37218618392944336, "Y": 0.6368263363838196}, {"X": 0.36130180954933167, "Y": 0.6368663311004639}]}, "Id": "18120cbb-9da0-4dfc-b86c-aa639d607135", "Relationships": [{"Type": "CHILD", "Ids": ["6685574b-7135-4900-8761-f05044e55579"]}]}, {"BlockType": "LINE", "Confidence": 98.54864501953125, "Text": "X *********** 2144114", "Geometry": {"BoundingBox": {"Width": 0.2846467196941376, "Height": 0.022186657413840294, "Left": 0.431342214345932, "Top": 0.6151215434074402}, "Polygon": [{"X": 0.4314357340335846, "Y": 0.6161932945251465}, {"X": 0.7156698703765869, "Y": 0.6151215434074402}, {"X": 0.7159889340400696, "Y": 0.6362634301185608}, {"X": 0.431342214345932, "Y": 0.6373082399368286}]}, "Id": "79ea5f0f-551f-49b8-b1ac-6f1e51460ad7", "Relationships": [{"Type": "CHILD", "Ids": ["bc74301a-7934-4f41-b578-a526c72fdc6e", "002cb160-b6bd-4a8e-b347-885862fac994", "3238e992-917f-4456-9e23-0f7212f16ade"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1350", "Geometry": {"BoundingBox": {"Width": 0.06724908947944641, "Height": 0.017352038994431496, "Left": 0.8326568603515625, "Top": 0.6311973929405212}, "Polygon": [{"X": 0.8326568603515625, "Y": 0.6314438581466675}, {"X": 0.899432361125946, "Y": 0.6311973929405212}, {"X": 0.8999059200286865, "Y": 0.6483080387115479}, {"X": 0.8330519199371338, "Y": 0.6485494375228882}]}, "Id": "994d7efe-a9ac-422b-a61f-a7128e37d815", "Relationships": [{"Type": "CHILD", "Ids": ["5f533fe3-d922-47cd-99f2-eac3efcb1e29"]}]}, {"BlockType": "LINE", "Confidence": 28.481245040893555, "Text": "A", "Geometry": {"BoundingBox": {"Width": 0.08578246831893921, "Height": 0.10898826271295547, "Left": 0.030134841799736023, "Top": 0.7488461136817932}, "Polygon": [{"X": 0.033555954694747925, "Y": 0.7491052746772766}, {"X": 0.11591731011867523, "Y": 0.7488461136817932}, {"X": 0.1131044551730156, "Y": 0.857616126537323}, {"X": 0.030134841799736023, "Y": 0.8578343987464905}]}, "Id": "1848fdfa-2042-41ea-89ed-d8ccfdfc8105", "Relationships": [{"Type": "CHILD", "Ids": ["f568e661-05a2-459f-84d6-cff63edc6411"]}]}, {"BlockType": "LINE", "Confidence": 63.52997589111328, "Text": "JBSK", "Geometry": {"BoundingBox": {"Width": 0.12453446537256241, "Height": 0.049952153116464615, "Left": 0.13224372267723083, "Top": 0.7376835346221924}, "Polygon": [{"X": 0.13346703350543976, "Y": 0.7380778789520264}, {"X": 0.25677818059921265, "Y": 0.7376835346221924}, {"X": 0.2559705674648285, "Y": 0.7872691750526428}, {"X": 0.13224372267723083, "Y": 0.7876357436180115}]}, "Id": "127760ac-f3a5-4a85-b276-a19c9f1f65e0", "Relationships": [{"Type": "CHILD", "Ids": ["7fd0b2c5-085f-4207-8872-ed799b3dad45"]}]}, {"BlockType": "LINE", "Confidence": 54.852718353271484, "Text": "55K", "Geometry": {"BoundingBox": {"Width": 0.06585340201854706, "Height": 0.02629137597978115, "Left": 0.4993748962879181, "Top": 0.7528534531593323}, "Polygon": [{"X": 0.4993748962879181, "Y": 0.7530586123466492}, {"X": 0.5651055574417114, "Y": 0.7528534531593323}, {"X": 0.565228283405304, "Y": 0.7789474725723267}, {"X": 0.4993809759616852, "Y": 0.779144823551178}]}, "Id": "4123c3dd-7b03-4d32-9363-613e2489777e", "Relationships": [{"Type": "CHILD", "Ids": ["8cb1dda3-b6e2-4b87-80e3-a9b6af579db3"]}]}, {"BlockType": "LINE", "Confidence": 99.44993591308594, "Text": "0810", "Geometry": {"BoundingBox": {"Width": 0.08441594988107681, "Height": 0.015546862035989761, "Left": 0.8726592659950256, "Top": 0.7744803428649902}, "Polygon": [{"X": 0.8726592659950256, "Y": 0.774733304977417}, {"X": 0.9565964937210083, "Y": 0.7744803428649902}, {"X": 0.9570752382278442, "Y": 0.7897801399230957}, {"X": 0.8730506300926208, "Y": 0.7900272011756897}]}, "Id": "f70d6878-3a83-4ff4-b6dd-e1cd6797f05f", "Relationships": [{"Type": "CHILD", "Ids": ["9680f0c0-5748-454d-a8d3-e39cff4492e2"]}]}, {"BlockType": "LINE", "Confidence": 95.72076416015625, "Text": "<PERSON> -", "Geometry": {"BoundingBox": {"Width": 0.15013398230075836, "Height": 0.013538721948862076, "Left": 0.10310797393321991, "Top": 0.7860400080680847}, "Polygon": [{"X": 0.10345680266618729, "Y": 0.7864846587181091}, {"X": 0.25324195623397827, "Y": 0.7860400080680847}, {"X": 0.2530260980129242, "Y": 0.7991430759429932}, {"X": 0.10310797393321991, "Y": 0.7995787858963013}]}, "Id": "6ad65b6c-11a9-4ae9-9af0-cbaa090a77f6", "Relationships": [{"Type": "CHILD", "Ids": ["9008d852-71f4-4af2-a814-be80e3f03e62", "6bed8194-6499-4d27-9b56-464ab1f15462"]}]}, {"BlockType": "LINE", "Confidence": 89.08474731445312, "Text": "so 02143642 *********** *********** SO-", "Geometry": {"BoundingBox": {"Width": 0.3455475866794586, "Height": 0.013935198076069355, "Left": 0.47175219655036926, "Top": 0.7877082824707031}, "Polygon": [{"X": 0.4717733860015869, "Y": 0.7887275815010071}, {"X": 0.8170179128646851, "Y": 0.7877082824707031}, {"X": 0.8172997832298279, "Y": 0.8006443977355957}, {"X": 0.47175219655036926, "Y": 0.8016434907913208}]}, "Id": "8687b74f-4956-4acf-bba5-cc398c3b8491", "Relationships": [{"Type": "CHILD", "Ids": ["79d25d1b-b6d2-4743-b82a-c7d86480b4a4", "aaebaaa9-3ab3-4002-b334-b10e748e30da", "c607f85e-f0a7-42ff-a6a1-2c1e0a0433f2", "9585b273-159d-4946-a2f0-2e886d85ec9f", "f57522c9-fa65-4d8a-812a-d3c556631fdd"]}]}, {"BlockType": "LINE", "Confidence": 97.35192108154297, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011368862353265285, "Height": 0.010477334260940552, "Left": 0.3564881682395935, "Top": 0.7988196611404419}, "Polygon": [{"X": 0.35658687353134155, "Y": 0.7988524436950684}, {"X": 0.3678570091724396, "Y": 0.7988196611404419}, {"X": 0.36776629090309143, "Y": 0.8092647790908813}, {"X": 0.3564881682395935, "Y": 0.8092970252037048}]}, "Id": "328baff7-5c67-45b0-a8c5-6cdc065b9757", "Relationships": [{"Type": "CHILD", "Ids": ["8ff976e7-4356-4898-879e-fb0f56ade646"]}]}, {"BlockType": "LINE", "Confidence": 94.2332992553711, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010945194400846958, "Height": 0.010165690444409847, "Left": 0.42942869663238525, "Top": 0.7988258600234985}, "Polygon": [{"X": 0.4294743835926056, "Y": 0.7988575100898743}, {"X": 0.44037389755249023, "Y": 0.7988258600234985}, {"X": 0.4403356909751892, "Y": 0.8089603781700134}, {"X": 0.42942869663238525, "Y": 0.808991551399231}]}, "Id": "06649531-de2f-40ed-b231-6303ada1a4f3", "Relationships": [{"Type": "CHILD", "Ids": ["1e7a2986-2344-4bf6-817c-087187b2d16e"]}]}, {"BlockType": "LINE", "Confidence": 99.88201141357422, "Text": "Metamora", "Geometry": {"BoundingBox": {"Width": 0.08984909951686859, "Height": 0.012662841007113457, "Left": 0.10179024189710617, "Top": 0.8072381019592285}, "Polygon": [{"X": 0.10212139785289764, "Y": 0.8074948787689209}, {"X": 0.19163934886455536, "Y": 0.8072381019592285}, {"X": 0.19138336181640625, "Y": 0.8196492195129395}, {"X": 0.10179024189710617, "Y": 0.8199009299278259}]}, "Id": "349a9d0c-3846-4fde-b98e-485aa1cbb931", "Relationships": [{"Type": "CHILD", "Ids": ["3c0467e6-21a1-4f13-a268-4704cf77bbcf"]}]}, {"BlockType": "LINE", "Confidence": 84.13863372802734, "Text": "02143649 *********** ***********", "Geometry": {"BoundingBox": {"Width": 0.28925299644470215, "Height": 0.013043996877968311, "Left": 0.4720233380794525, "Top": 0.8049250841140747}, "Polygon": [{"X": 0.47204312682151794, "Y": 0.805755078792572}, {"X": 0.7610565423965454, "Y": 0.8049250841140747}, {"X": 0.7612763047218323, "Y": 0.817155122756958}, {"X": 0.4720233380794525, "Y": 0.8179690837860107}]}, "Id": "56b7d6cc-aa34-4389-a4f7-e9ac4bc50ced", "Relationships": [{"Type": "CHILD", "Ids": ["95a02a9c-19b5-469c-bb40-0511f3a447ef", "c7ef30d4-8a36-4578-9906-40884fd278ff", "4e4636fd-ba5a-4915-87b5-4fee55c369fa"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "0820", "Geometry": {"BoundingBox": {"Width": 0.07849562913179398, "Height": 0.01957092434167862, "Left": 0.8775814771652222, "Top": 0.8109831213951111}, "Polygon": [{"X": 0.8775814771652222, "Y": 0.8112043738365173}, {"X": 0.9554743766784668, "Y": 0.8109831213951111}, {"X": 0.9560770988464355, "Y": 0.8303396701812744}, {"X": 0.8780818581581116, "Y": 0.8305540680885315}]}, "Id": "33653218-ea9d-456c-bb05-2c9a43653909", "Relationships": [{"Type": "CHILD", "Ids": ["35fd3d9a-1c65-4cb6-bea6-ea2511ee2f33"]}]}, {"BlockType": "LINE", "Confidence": 99.92636108398438, "Text": "2144190 2144112", "Geometry": {"BoundingBox": {"Width": 0.2775053381919861, "Height": 0.025684181600809097, "Left": 0.5432321429252625, "Top": 0.8214754462242126}, "Polygon": [{"X": 0.5432321429252625, "Y": 0.8222489356994629}, {"X": 0.8201898336410522, "Y": 0.8214754462242126}, {"X": 0.8207374811172485, "Y": 0.8464175462722778}, {"X": 0.5433118939399719, "Y": 0.8471596240997314}]}, "Id": "3f67436d-2993-4a26-840a-f62ec006f9f6", "Relationships": [{"Type": "CHILD", "Ids": ["90df3417-fca5-49b5-b446-97cecb251bff", "109bad4c-b3ca-436b-a785-e5060c2adfe7"]}]}, {"BlockType": "LINE", "Confidence": 59.37818908691406, "Text": "1 SK", "Geometry": {"BoundingBox": {"Width": 0.07067469507455826, "Height": 0.038822002708911896, "Left": 0.15742184221744537, "Top": 0.853912353515625}, "Polygon": [{"X": 0.15830327570438385, "Y": 0.8540971279144287}, {"X": 0.22809654474258423, "Y": 0.853912353515625}, {"X": 0.22739708423614502, "Y": 0.8925619721412659}, {"X": 0.15742184221744537, "Y": 0.8927343487739563}]}, "Id": "aa8097ea-be90-4453-89ed-aeb7d69364de", "Relationships": [{"Type": "CHILD", "Ids": ["e213796a-c188-4d5e-bbe7-cd734e41b724"]}]}, {"BlockType": "LINE", "Confidence": 60.670440673828125, "Text": "Pickup", "Geometry": {"BoundingBox": {"Width": 0.13209716975688934, "Height": 0.034099314361810684, "Left": 0.4795437753200531, "Top": 0.8708834052085876}, "Polygon": [{"X": 0.4795810580253601, "Y": 0.8712210059165955}, {"X": 0.6113777756690979, "Y": 0.8708834052085876}, {"X": 0.6116409301757812, "Y": 0.904665470123291}, {"X": 0.4795437753200531, "Y": 0.9049827456474304}]}, "Id": "d083d7c7-cb46-41c7-95fb-fb675a5a7da1", "Relationships": [{"Type": "CHILD", "Ids": ["dc3731e6-5f6b-44e7-b9a6-50593e5eb979"]}]}, {"BlockType": "LINE", "Confidence": 90.62400817871094, "Text": "1300", "Geometry": {"BoundingBox": {"Width": 0.08463035523891449, "Height": 0.02895326167345047, "Left": 0.8800288438796997, "Top": 0.8594411015510559}, "Polygon": [{"X": 0.8800288438796997, "Y": 0.859659731388092}, {"X": 0.9637510180473328, "Y": 0.8594411015510559}, {"X": 0.9646592140197754, "Y": 0.8881866931915283}, {"X": 0.8807742595672607, "Y": 0.8883943557739258}]}, "Id": "f665741e-b749-4b3c-8c69-784efc8b680b", "Relationships": [{"Type": "CHILD", "Ids": ["2b14a5c6-95c2-40ba-abef-eef24fef026d"]}]}, {"BlockType": "LINE", "Confidence": 76.55990600585938, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01872570998966694, "Height": 0.028062280267477036, "Left": 0.3625558912754059, "Top": 0.8780162334442139}, "Polygon": [{"X": 0.3628075122833252, "Y": 0.8780630230903625}, {"X": 0.38128161430358887, "Y": 0.8780162334442139}, {"X": 0.38106489181518555, "Y": 0.9060341715812683}, {"X": 0.3625558912754059, "Y": 0.9060785174369812}]}, "Id": "a2a3c53e-5309-4d05-8795-a6431e50a67a", "Relationships": [{"Type": "CHILD", "Ids": ["6828c269-818e-453a-94b9-308ec65c43a5"]}]}, {"BlockType": "LINE", "Confidence": 44.64544677734375, "Text": "1345", "Geometry": {"BoundingBox": {"Width": 0.0771438404917717, "Height": 0.030931809917092323, "Left": 0.8937699198722839, "Top": 0.9007700085639954}, "Polygon": [{"X": 0.8937699198722839, "Y": 0.900954008102417}, {"X": 0.9699318408966064, "Y": 0.9007700085639954}, {"X": 0.9709137678146362, "Y": 0.9315285086631775}, {"X": 0.8945937752723694, "Y": 0.9317017793655396}]}, "Id": "517805e1-c97f-45b8-ad10-6c2e264c2191", "Relationships": [{"Type": "CHILD", "Ids": ["fcdd3d77-525e-4e94-9eec-478a30459bac"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "YOUNGSTOWN", "Geometry": {"BoundingBox": {"Width": 0.13918401300907135, "Height": 0.015937021002173424, "Left": 0.4375349283218384, "Top": 0.9418146014213562}, "Polygon": [{"X": 0.4375961720943451, "Y": 0.9421241283416748}, {"X": 0.5766341686248779, "Y": 0.9418146014213562}, {"X": 0.5767189264297485, "Y": 0.9574520587921143}, {"X": 0.4375349283218384, "Y": 0.9577515721321106}]}, "Id": "2b122bef-dc49-4487-96b9-85e6bbcee4d0", "Relationships": [{"Type": "CHILD", "Ids": ["07e5f1db-7b9c-446f-959a-4bc2f47f12f8"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04810188710689545, "Height": 0.010216466151177883, "Left": 0.2818598747253418, "Top": 0.16244925558567047}, "Polygon": [{"X": 0.2820104658603668, "Y": 0.1627332866191864}, {"X": 0.32996174693107605, "Y": 0.16244925558567047}, {"X": 0.3298448920249939, "Y": 0.17238377034664154}, {"X": 0.2818598747253418, "Y": 0.17266573011875153}]}, "Id": "8ed10603-8aee-4471-96a7-71cfd4b98936"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "5-Sep", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04387959465384483, "Height": 0.012435640208423138, "Left": 0.3490234613418579, "Top": 0.16151244938373566}, "Polygon": [{"X": 0.349150151014328, "Y": 0.1617717295885086}, {"X": 0.39290305972099304, "Y": 0.16151244938373566}, {"X": 0.39281409978866577, "Y": 0.17369112372398376}, {"X": 0.3490234613418579, "Y": 0.17394809424877167}]}, "Id": "1f3815ec-624f-4dab-951f-0b8c70b9ae41"}, {"BlockType": "WORD", "Confidence": 99.453125, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0428379625082016, "Height": 0.010093491524457932, "Left": 0.2655361294746399, "Top": 0.19103766977787018}, "Polygon": [{"X": 0.2656964957714081, "Y": 0.19128470122814178}, {"X": 0.3083741068840027, "Y": 0.19103766977787018}, {"X": 0.30824342370033264, "Y": 0.20088596642017365}, {"X": 0.2655361294746399, "Y": 0.2011311650276184}]}, "Id": "81dfc286-4780-4442-9a15-184d64d25874"}, {"BlockType": "WORD", "Confidence": 99.306640625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0182033758610487, "Height": 0.009983835741877556, "Left": 0.3103424310684204, "Top": 0.19067618250846863}, "Polygon": [{"X": 0.3104720413684845, "Y": 0.19078081846237183}, {"X": 0.32854580879211426, "Y": 0.19067618250846863}, {"X": 0.32842880487442017, "Y": 0.20055615901947021}, {"X": 0.3103424310684204, "Y": 0.20066002011299133}]}, "Id": "cd4caa12-b40a-4a42-a742-e361f5e689de"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "6063/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10241226106882095, "Height": 0.012318200431764126, "Left": 0.3473314940929413, "Top": 0.18916282057762146}, "Polygon": [{"X": 0.34745466709136963, "Y": 0.1897554099559784}, {"X": 0.44974374771118164, "Y": 0.18916282057762146}, {"X": 0.4497053921222687, "Y": 0.20089362561702728}, {"X": 0.3473314940929413, "Y": 0.20148101449012756}]}, "Id": "1a8c58aa-15c0-47dc-a52f-ee84ea6d704f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04264553263783455, "Height": 0.010528765618801117, "Left": 0.4762611389160156, "Top": 0.20241571962833405}, "Polygon": [{"X": 0.47627541422843933, "Y": 0.20265984535217285}, {"X": 0.5188899636268616, "Y": 0.20241571962833405}, {"X": 0.5189066529273987, "Y": 0.21270225942134857}, {"X": 0.4762611389160156, "Y": 0.21294449269771576}]}, "Id": "d837c3c1-9d1e-4958-8d91-a6e50a6f9cd2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050124842673540115, "Height": 0.012874303385615349, "Left": 0.5227655172348022, "Top": 0.201745867729187}, "Polygon": [{"X": 0.5227655172348022, "Y": 0.20203270018100739}, {"X": 0.5728219151496887, "Y": 0.201745867729187}, {"X": 0.5728903412818909, "Y": 0.21433605253696442}, {"X": 0.5227893590927124, "Y": 0.21462015807628632}]}, "Id": "b60b0347-97da-4b24-b322-4414c1e97d2e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043663159012794495, "Height": 0.010107507929205894, "Left": 0.5776873230934143, "Top": 0.20126919448375702}, "Polygon": [{"X": 0.5776873230934143, "Y": 0.2015189379453659}, {"X": 0.6212631464004517, "Y": 0.20126919448375702}, {"X": 0.6213504672050476, "Y": 0.2111288160085678}, {"X": 0.5777443051338196, "Y": 0.21137671172618866}]}, "Id": "7e3ef307-ba64-494b-b1ae-01d9197fb5ef"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.067081019282341, "Height": 0.010480304248631, "Left": 0.25505393743515015, "Top": 0.21937082707881927}, "Polygon": [{"X": 0.25522559881210327, "Y": 0.2197490930557251}, {"X": 0.32213497161865234, "Y": 0.21937082707881927}, {"X": 0.32201096415519714, "Y": 0.22947578132152557}, {"X": 0.25505393743515015, "Y": 0.2298511266708374}]}, "Id": "cbfee325-4f3a-4564-925a-f81780f749bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05335063114762306, "Height": 0.010048815980553627, "Left": 0.3468654155731201, "Top": 0.21835391223430634}, "Polygon": [{"X": 0.3469679355621338, "Y": 0.21865509450435638}, {"X": 0.4002160429954529, "Y": 0.21835391223430634}, {"X": 0.4001501500606537, "Y": 0.22810378670692444}, {"X": 0.3468654155731201, "Y": 0.2284027338027954}]}, "Id": "6bce9811-2b32-4e1e-8c4b-5b5b10c352ef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0434502512216568, "Height": 0.010575738735496998, "Left": 0.4763062000274658, "Top": 0.23155063390731812}, "Polygon": [{"X": 0.4763205051422119, "Y": 0.2317933738231659}, {"X": 0.5197390913963318, "Y": 0.23155063390731812}, {"X": 0.5197564363479614, "Y": 0.24188558757305145}, {"X": 0.4763062000274658, "Y": 0.24212637543678284}]}, "Id": "ec4fbc5c-6c33-4f79-9b21-971d710864df"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08451168984174728, "Height": 0.0128419054672122, "Left": 0.5232325196266174, "Top": 0.23118257522583008}, "Polygon": [{"X": 0.5232325196266174, "Y": 0.23165445029735565}, {"X": 0.6076467037200928, "Y": 0.23118257522583008}, {"X": 0.6077442169189453, "Y": 0.2435571402311325}, {"X": 0.5232563018798828, "Y": 0.24402448534965515}]}, "Id": "ce8792a0-97a0-4b0d-8291-7f2da5766913"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09848883748054504, "Height": 0.011071193031966686, "Left": 0.17284563183784485, "Top": 0.249211847782135}, "Polygon": [{"X": 0.17308510839939117, "Y": 0.24975353479385376}, {"X": 0.2713344693183899, "Y": 0.249211847782135}, {"X": 0.2711677849292755, "Y": 0.25974586606025696}, {"X": 0.17284563183784485, "Y": 0.26028305292129517}]}, "Id": "7cd6ad8d-0fa3-47dd-b60d-ab8e5830b29f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044582147151231766, "Height": 0.010279140435159206, "Left": 0.27603626251220703, "Top": 0.24849872291088104}, "Polygon": [{"X": 0.2761915922164917, "Y": 0.24874375760555267}, {"X": 0.3206183910369873, "Y": 0.24849872291088104}, {"X": 0.3204944431781769, "Y": 0.2585347592830658}, {"X": 0.27603626251220703, "Y": 0.2587778568267822}]}, "Id": "a3262b86-26b1-49cf-b89c-d6ed2bfc89aa"}, {"BlockType": "WORD", "Confidence": 99.45233154296875, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03271181508898735, "Height": 0.010315967723727226, "Left": 0.3456226587295532, "Top": 0.2475498616695404}, "Polygon": [{"X": 0.34572991728782654, "Y": 0.24772979319095612}, {"X": 0.3783344626426697, "Y": 0.2475498616695404}, {"X": 0.37825044989585876, "Y": 0.2576873302459717}, {"X": 0.3456226587295532, "Y": 0.257865846157074}]}, "Id": "6bbb00cd-83c3-4f9a-8a20-f443e081bb65"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04333392530679703, "Height": 0.010078788734972477, "Left": 0.35375338792800903, "Top": 0.27318575978279114}, "Polygon": [{"X": 0.3538517653942108, "Y": 0.2734190821647644}, {"X": 0.39708730578422546, "Y": 0.27318575978279114}, {"X": 0.3970188498497009, "Y": 0.2830330729484558}, {"X": 0.35375338792800903, "Y": 0.28326454758644104}]}, "Id": "ddb19a5a-c328-4a7d-9331-9750bfef30e0"}, {"BlockType": "WORD", "Confidence": 92.02088928222656, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09629561752080917, "Height": 0.01784023456275463, "Left": 0.4994954764842987, "Top": 0.2728380560874939}, "Polygon": [{"X": 0.4994954764842987, "Y": 0.27335673570632935}, {"X": 0.5956695675849915, "Y": 0.2728380560874939}, {"X": 0.5957911014556885, "Y": 0.2901668846607208}, {"X": 0.4994997978210449, "Y": 0.2906782925128937}]}, "Id": "abf5656b-f908-4866-b391-32c8420ad6ad"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0426652766764164, "Height": 0.010585583746433258, "Left": 0.8079038262367249, "Top": 0.27744829654693604}, "Polygon": [{"X": 0.8079038262367249, "Y": 0.2776758074760437}, {"X": 0.8503110408782959, "Y": 0.27744829654693604}, {"X": 0.8505691289901733, "Y": 0.28780829906463623}, {"X": 0.8081310391426086, "Y": 0.2880338728427887}]}, "Id": "b00b39b8-fbe4-486d-851b-886e87eb4946"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04377588629722595, "Height": 0.010416083037853241, "Left": 0.8540791869163513, "Top": 0.27671244740486145}, "Polygon": [{"X": 0.8540791869163513, "Y": 0.2769458591938019}, {"X": 0.8975675106048584, "Y": 0.27671244740486145}, {"X": 0.8978551030158997, "Y": 0.28689706325531006}, {"X": 0.854335606098175, "Y": 0.2871285378932953}]}, "Id": "55e1259b-4a45-47f8-842d-0cc1a1800d40"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09023511409759521, "Height": 0.010960289277136326, "Left": 0.1974409967660904, "Top": 0.29138559103012085}, "Polygon": [{"X": 0.19766059517860413, "Y": 0.2918638586997986}, {"X": 0.2876761257648468, "Y": 0.29138559103012085}, {"X": 0.28752273321151733, "Y": 0.30187174677848816}, {"X": 0.1974409967660904, "Y": 0.3023459017276764}]}, "Id": "3f278718-0ef0-452e-aef8-bc338ebbd4dd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02109665609896183, "Height": 0.010236918926239014, "Left": 0.3645259439945221, "Top": 0.28907278180122375}, "Polygon": [{"X": 0.3646193742752075, "Y": 0.2891845405101776}, {"X": 0.38562262058258057, "Y": 0.28907278180122375}, {"X": 0.38554415106773376, "Y": 0.29919886589050293}, {"X": 0.3645259439945221, "Y": 0.29930970072746277}]}, "Id": "53a63974-b026-4f4b-8371-2fe2aa15b3ae"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031135501340031624, "Height": 0.010444971732795238, "Left": 0.42465940117836, "Top": 0.28869664669036865}, "Polygon": [{"X": 0.42471083998680115, "Y": 0.28886207938194275}, {"X": 0.45579490065574646, "Y": 0.28869664669036865}, {"X": 0.45576590299606323, "Y": 0.2989776134490967}, {"X": 0.42465940117836, "Y": 0.29914161562919617}]}, "Id": "eaded07e-ab83-44b6-972a-aafda751b236"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05215398594737053, "Height": 0.010639880783855915, "Left": 0.5686437487602234, "Top": 0.2880202531814575}, "Polygon": [{"X": 0.5686437487602234, "Y": 0.28829726576805115}, {"X": 0.6207068562507629, "Y": 0.2880202531814575}, {"X": 0.6207976937294006, "Y": 0.29838547110557556}, {"X": 0.5686966180801392, "Y": 0.29866012930870056}]}, "Id": "99cc3a59-81fb-486f-bb8a-fcdaeedfe284"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058920975774526596, "Height": 0.010970057919621468, "Left": 0.6255868077278137, "Top": 0.2869761884212494}, "Polygon": [{"X": 0.6255868077278137, "Y": 0.2872891426086426}, {"X": 0.6843666434288025, "Y": 0.2869761884212494}, {"X": 0.6845077872276306, "Y": 0.2976360321044922}, {"X": 0.6256838440895081, "Y": 0.2979462444782257}]}, "Id": "9b574b1d-2185-4445-acf6-1fd0fb648bfa"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011787569150328636, "Height": 0.010163122788071632, "Left": 0.6901009678840637, "Top": 0.2865668535232544}, "Polygon": [{"X": 0.6901009678840637, "Y": 0.2866288423538208}, {"X": 0.7017424702644348, "Y": 0.2865668535232544}, {"X": 0.7018885612487793, "Y": 0.29666849970817566}, {"X": 0.6902387738227844, "Y": 0.2967299818992615}]}, "Id": "a2728bf9-6a22-4d9e-bce8-2669ae85e972"}, {"BlockType": "WORD", "Confidence": 94.1954345703125, "Text": "1025", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06885294616222382, "Height": 0.01676912233233452, "Left": 0.8012471795082092, "Top": 0.30962246656417847}, "Polygon": [{"X": 0.8012471795082092, "Y": 0.309979110956192}, {"X": 0.8696697354316711, "Y": 0.30962246656417847}, {"X": 0.8701000809669495, "Y": 0.3260398805141449}, {"X": 0.8015986084938049, "Y": 0.3263916075229645}]}, "Id": "daf520c6-cb04-4be8-b0e3-0e1f834738c1"}, {"BlockType": "WORD", "Confidence": 96.65497589111328, "Text": "3SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09110535681247711, "Height": 0.02468687854707241, "Left": 0.18377244472503662, "Top": 0.32854583859443665}, "Polygon": [{"X": 0.1843012273311615, "Y": 0.32901114225387573}, {"X": 0.2748778164386749, "Y": 0.32854583859443665}, {"X": 0.27450254559516907, "Y": 0.35277703404426575}, {"X": 0.18377244472503662, "Y": 0.3532327115535736}]}, "Id": "b179a1bf-a052-4eb4-9886-dc1f0c102639"}, {"BlockType": "WORD", "Confidence": 66.689453125, "Text": "4sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05581986531615257, "Height": 0.01808406412601471, "Left": 0.48379531502723694, "Top": 0.3220384418964386}, "Polygon": [{"X": 0.4838104546070099, "Y": 0.3223261833190918}, {"X": 0.5395607352256775, "Y": 0.3220384418964386}, {"X": 0.5396151542663574, "Y": 0.33983907103538513}, {"X": 0.48379531502723694, "Y": 0.3401224911212921}]}, "Id": "94cfb166-1f3c-46a6-a003-67b4d0942b86"}, {"BlockType": "WORD", "Confidence": 72.60951232910156, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06918258219957352, "Height": 0.0564197413623333, "Left": 0.07948210090398788, "Top": 0.3371361196041107}, "Polygon": [{"X": 0.0811106413602829, "Y": 0.3374806046485901}, {"X": 0.1486646831035614, "Y": 0.3371361196041107}, {"X": 0.14730089902877808, "Y": 0.39322805404663086}, {"X": 0.07948210090398788, "Y": 0.3935558497905731}]}, "Id": "e263150e-36ff-4d8b-91e7-5a5038c3647c"}, {"BlockType": "WORD", "Confidence": 76.17605590820312, "Text": "Alumalloy -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08682260662317276, "Height": 0.013695485889911652, "Left": 0.140276238322258, "Top": 0.3507533073425293}, "Polygon": [{"X": 0.14060580730438232, "Y": 0.35118862986564636}, {"X": 0.22709885239601135, "Y": 0.3507533073425293}, {"X": 0.22684940695762634, "Y": 0.3640185296535492}, {"X": 0.140276238322258, "Y": 0.36444878578186035}]}, "Id": "b1a930c8-db59-44a7-b328-2ae4addf68d9"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "Avon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03996255248785019, "Height": 0.010508131235837936, "Left": 0.23479890823364258, "Top": 0.3512435555458069}, "Polygon": [{"X": 0.2349870502948761, "Y": 0.3514436185359955}, {"X": 0.27476146817207336, "Y": 0.3512435555458069}, {"X": 0.2746019661426544, "Y": 0.36155346035957336}, {"X": 0.23479890823364258, "Y": 0.3617517054080963}]}, "Id": "6c468dd1-91c3-45a5-af23-8b85720c2951"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Lake", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03630420193076134, "Height": 0.011276636272668839, "Left": 0.28120341897010803, "Top": 0.34995734691619873}, "Polygon": [{"X": 0.2813699543476105, "Y": 0.3501392900943756}, {"X": 0.31750762462615967, "Y": 0.34995734691619873}, {"X": 0.31736913323402405, "Y": 0.36105379462242126}, {"X": 0.28120341897010803, "Y": 0.36123397946357727}]}, "Id": "5768ecc8-d1b5-46e2-a51a-782ed0baad7d"}, {"BlockType": "WORD", "Confidence": 95.72923278808594, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011499851010739803, "Height": 0.010871955193579197, "Left": 0.43313419818878174, "Top": 0.34896937012672424}, "Polygon": [{"X": 0.43318167328834534, "Y": 0.34902703762054443}, {"X": 0.4446340501308441, "Y": 0.34896937012672424}, {"X": 0.44459521770477295, "Y": 0.35978418588638306}, {"X": 0.43313419818878174, "Y": 0.35984131693840027}]}, "Id": "6a6ae969-ca3d-44d0-869e-1ae4d6c1a633"}, {"BlockType": "WORD", "Confidence": 99.44355773925781, "Text": "SO-02143353", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08981869369745255, "Height": 0.009816725738346577, "Left": 0.4772059917449951, "Top": 0.34882691502571106}, "Polygon": [{"X": 0.47721824049949646, "Y": 0.3492787778377533}, {"X": 0.5669780969619751, "Y": 0.34882691502571106}, {"X": 0.5670246481895447, "Y": 0.3581954538822174}, {"X": 0.4772059917449951, "Y": 0.35864362120628357}]}, "Id": "fd142bb4-a401-4bae-96b0-a6a9e0ab3b11"}, {"BlockType": "WORD", "Confidence": 99.24745178222656, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09121807664632797, "Height": 0.009825886227190495, "Left": 0.5718158483505249, "Top": 0.34763118624687195}, "Polygon": [{"X": 0.5718158483505249, "Y": 0.34809014201164246}, {"X": 0.6629244685173035, "Y": 0.34763118624687195}, {"X": 0.6630339622497559, "Y": 0.3570018708705902}, {"X": 0.5718655586242676, "Y": 0.35745707154273987}]}, "Id": "1729bf99-393f-452b-8e16-b6824d7b36d1"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "1050", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07072294503450394, "Height": 0.01679832488298416, "Left": 0.8047999739646912, "Top": 0.3447118103504181}, "Polygon": [{"X": 0.8047999739646912, "Y": 0.34506648778915405}, {"X": 0.8750865459442139, "Y": 0.3447118103504181}, {"X": 0.8755229115486145, "Y": 0.3611605167388916}, {"X": 0.805155336856842, "Y": 0.36151012778282166}]}, "Id": "76937cf9-3595-4b93-97fb-1ae6694a3429"}, {"BlockType": "WORD", "Confidence": 79.7520751953125, "Text": "6SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0617954283952713, "Height": 0.021563010290265083, "Left": 0.4913441836833954, "Top": 0.4381857216358185}, "Polygon": [{"X": 0.4913509786128998, "Y": 0.4384702444076538}, {"X": 0.5530551075935364, "Y": 0.4381857216358185}, {"X": 0.5531396269798279, "Y": 0.4594700038433075}, {"X": 0.4913441836833954, "Y": 0.4597487449645996}]}, "Id": "d17e452a-651b-4bed-973a-03a7e7e1a135"}, {"BlockType": "WORD", "Confidence": 65.42141723632812, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09494316577911377, "Height": 0.06966271251440048, "Left": 0.05758822709321976, "Top": 0.45048847794532776}, "Polygon": [{"X": 0.059686265885829926, "Y": 0.4509120285511017}, {"X": 0.15253138542175293, "Y": 0.45048847794532776}, {"X": 0.1508791297674179, "Y": 0.5197563171386719}, {"X": 0.05758822709321976, "Y": 0.5201511979103088}]}, "Id": "d67bd491-12bb-432b-b5d7-f9aa297cf19b"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "General", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06348034739494324, "Height": 0.011606658808887005, "Left": 0.13064876198768616, "Top": 0.4711284041404724}, "Polygon": [{"X": 0.1309354603290558, "Y": 0.4714104235172272}, {"X": 0.1941291093826294, "Y": 0.4711284041404724}, {"X": 0.1938919723033905, "Y": 0.4824562072753906}, {"X": 0.13064876198768616, "Y": 0.4827350676059723}]}, "Id": "f0d73512-7746-4333-aeaa-a198ce2052a3"}, {"BlockType": "WORD", "Confidence": 32.42446517944336, "Text": "2sm Boxes", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1596786081790924, "Height": 0.040466174483299255, "Left": 0.17935246229171753, "Top": 0.43955937027931213}, "Polygon": [{"X": 0.1802244484424591, "Y": 0.4402913749217987}, {"X": 0.33903107047080994, "Y": 0.43955937027931213}, {"X": 0.3385973572731018, "Y": 0.47932153940200806}, {"X": 0.17935246229171753, "Y": 0.4800255596637726}]}, "Id": "d3c1a87f-85d6-4352-9d6b-33919e26e523"}, {"BlockType": "WORD", "Confidence": 95.66825103759766, "Text": "Extrusion", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07617488503456116, "Height": 0.01183949876576662, "Left": 0.20094172656536102, "Top": 0.4709688723087311}, "Polygon": [{"X": 0.20117685198783875, "Y": 0.4713076949119568}, {"X": 0.2771166265010834, "Y": 0.4709688723087311}, {"X": 0.2769419848918915, "Y": 0.4824734032154083}, {"X": 0.20094172656536102, "Y": 0.482808381319046}]}, "Id": "33eecb6b-7257-4d8a-ad80-0ad88e427fb2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1235", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07125698775053024, "Height": 0.017501451075077057, "Left": 0.8216785192489624, "Top": 0.4451032280921936}, "Polygon": [{"X": 0.8216785192489624, "Y": 0.4454267919063568}, {"X": 0.8924620747566223, "Y": 0.4451032280921936}, {"X": 0.8929355144500732, "Y": 0.46228650212287903}, {"X": 0.8220673203468323, "Y": 0.46260470151901245}]}, "Id": "eecbd411-ad91-4160-86d4-73540c976135"}, {"BlockType": "WORD", "Confidence": 95.78703308105469, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01018470712006092, "Height": 0.008606708608567715, "Left": 0.3656158447265625, "Top": 0.47279852628707886}, "Polygon": [{"X": 0.36569318175315857, "Y": 0.4728435277938843}, {"X": 0.37580054998397827, "Y": 0.47279852628707886}, {"X": 0.37572920322418213, "Y": 0.48136064410209656}, {"X": 0.3656158447265625, "Y": 0.48140525817871094}]}, "Id": "fe4223a8-5ff9-41fc-a04f-f1d4a02372f4"}, {"BlockType": "WORD", "Confidence": 97.41071319580078, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011586965061724186, "Height": 0.01153433509171009, "Left": 0.43226373195648193, "Top": 0.46973079442977905}, "Polygon": [{"X": 0.432314395904541, "Y": 0.469782292842865}, {"X": 0.44385069608688354, "Y": 0.46973079442977905}, {"X": 0.4438091814517975, "Y": 0.4812142252922058}, {"X": 0.43226373195648193, "Y": 0.481265127658844}]}, "Id": "afa2506a-b358-4379-8c0c-d6f9c2eda371"}, {"BlockType": "WORD", "Confidence": 99.33374786376953, "Text": "SO-02142762", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09197529405355453, "Height": 0.010044234804809093, "Left": 0.47236818075180054, "Top": 0.46992579102516174}, "Polygon": [{"X": 0.47238391637802124, "Y": 0.47033578157424927}, {"X": 0.5642977952957153, "Y": 0.46992579102516174}, {"X": 0.5643434524536133, "Y": 0.47956395149230957}, {"X": 0.47236818075180054, "Y": 0.4799700081348419}]}, "Id": "3565b8f6-4657-48a7-a30f-244071feebcc"}, {"BlockType": "WORD", "Confidence": 98.92857360839844, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09327472001314163, "Height": 0.00984400138258934, "Left": 0.5695444941520691, "Top": 0.46905332803726196}, "Polygon": [{"X": 0.5695444941520691, "Y": 0.4694691002368927}, {"X": 0.6627100706100464, "Y": 0.46905332803726196}, {"X": 0.6628192067146301, "Y": 0.47848543524742126}, {"X": 0.5695925951004028, "Y": 0.4788973033428192}]}, "Id": "870f8f18-399e-4e9a-81fe-46b9dda1aae2"}, {"BlockType": "WORD", "Confidence": 99.45332336425781, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09460635483264923, "Height": 0.00991747435182333, "Left": 0.6681022047996521, "Top": 0.468094140291214}, "Polygon": [{"X": 0.6681022047996521, "Y": 0.4685157835483551}, {"X": 0.7625328898429871, "Y": 0.468094140291214}, {"X": 0.7627085447311401, "Y": 0.47759392857551575}, {"X": 0.6682156324386597, "Y": 0.4780116081237793}]}, "Id": "597c8126-df6c-4280-abcb-6c12a7963ce6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2143358", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11974821239709854, "Height": 0.017683526501059532, "Left": 0.48226073384284973, "Top": 0.48960188031196594}, "Polygon": [{"X": 0.482276976108551, "Y": 0.4901241958141327}, {"X": 0.6018829345703125, "Y": 0.48960188031196594}, {"X": 0.6020089387893677, "Y": 0.5067722201347351}, {"X": 0.48226073384284973, "Y": 0.5072854161262512}]}, "Id": "000d10d7-faef-442e-9005-de78ceaed10c"}, {"BlockType": "WORD", "Confidence": 94.97847747802734, "Text": "1255", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07768413424491882, "Height": 0.02288704551756382, "Left": 0.8251683712005615, "Top": 0.47568467259407043}, "Polygon": [{"X": 0.8251683712005615, "Y": 0.4760257303714752}, {"X": 0.9022171497344971, "Y": 0.47568467259407043}, {"X": 0.9028525352478027, "Y": 0.49823838472366333}, {"X": 0.8256829977035522, "Y": 0.4985717236995697}]}, "Id": "ad86f2e0-848b-4f66-b2e8-31fa9a11b4c4"}, {"BlockType": "WORD", "Confidence": 18.4060115814209, "Text": "O", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09105972200632095, "Height": 0.09562571346759796, "Left": 0.03820807486772537, "Top": 0.5825405120849609}, "Polygon": [{"X": 0.04119022190570831, "Y": 0.5828871726989746}, {"X": 0.12926779687404633, "Y": 0.5825405120849609}, {"X": 0.12686222791671753, "Y": 0.6778574585914612}, {"X": 0.03820807486772537, "Y": 0.6781662702560425}]}, "Id": "06d7a7dc-6687-47ec-986c-ec85f36d55e2"}, {"BlockType": "WORD", "Confidence": 98.88612365722656, "Text": "Vari-Wall", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07738099247217178, "Height": 0.012157472781836987, "Left": 0.1172061339020729, "Top": 0.6254146695137024}, "Polygon": [{"X": 0.11751438677310944, "Y": 0.6257022023200989}, {"X": 0.1945871263742447, "Y": 0.6254146695137024}, {"X": 0.19434155523777008, "Y": 0.6372886896133423}, {"X": 0.1172061339020729, "Y": 0.6375721096992493}]}, "Id": "6c3b8093-0e6a-46e9-93e4-366f4269ac65"}, {"BlockType": "WORD", "Confidence": 46.429569244384766, "Text": "2SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0923072025179863, "Height": 0.046686600893735886, "Left": 0.153347447514534, "Top": 0.5869187116622925}, "Polygon": [{"X": 0.15443609654903412, "Y": 0.5872756242752075}, {"X": 0.2456546574831009, "Y": 0.5869187116622925}, {"X": 0.24485644698143005, "Y": 0.633267343044281}, {"X": 0.153347447514534, "Y": 0.6336053013801575}]}, "Id": "dacbbf3d-0d78-47ff-953c-af80e96307fe"}, {"BlockType": "WORD", "Confidence": 99.51589965820312, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0441281832754612, "Height": 0.012050117366015911, "Left": 0.19969379901885986, "Top": 0.6254611015319824}, "Polygon": [{"X": 0.19993525743484497, "Y": 0.6256247758865356}, {"X": 0.24382199347019196, "Y": 0.6254611015319824}, {"X": 0.2436162829399109, "Y": 0.6373498439788818}, {"X": 0.19969379901885986, "Y": 0.6375111937522888}]}, "Id": "7aff2b3a-e86e-4ccd-86e4-7cb01aad2eb7"}, {"BlockType": "WORD", "Confidence": 80.35554504394531, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05240174010396004, "Height": 0.023495879024267197, "Left": 0.48225313425064087, "Top": 0.5860322713851929}, "Polygon": [{"X": 0.4822750389575958, "Y": 0.5862368941307068}, {"X": 0.5345929265022278, "Y": 0.5860322713851929}, {"X": 0.5346548557281494, "Y": 0.6093289256095886}, {"X": 0.48225313425064087, "Y": 0.609528124332428}]}, "Id": "d697901e-7248-463d-9b10-dcda3e78052f"}, {"BlockType": "WORD", "Confidence": 99.7217788696289, "Text": "1335", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07100424915552139, "Height": 0.019956152886152267, "Left": 0.8299850821495056, "Top": 0.5964555144309998}, "Polygon": [{"X": 0.8299850821495056, "Y": 0.596727192401886}, {"X": 0.9004416465759277, "Y": 0.5964555144309998}, {"X": 0.9009893536567688, "Y": 0.6161462664604187}, {"X": 0.830437183380127, "Y": 0.6164116859436035}]}, "Id": "71a9c41a-a679-4c3a-ac52-39125ef8ae31"}, {"BlockType": "WORD", "Confidence": 97.44319915771484, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010968219488859177, "Height": 0.009919018484652042, "Left": 0.36130180954933167, "Top": 0.626947283744812}, "Polygon": [{"X": 0.361393004655838, "Y": 0.6269877552986145}, {"X": 0.37227001786231995, "Y": 0.626947283744812}, {"X": 0.37218618392944336, "Y": 0.6368263363838196}, {"X": 0.36130180954933167, "Y": 0.6368663311004639}]}, "Id": "6685574b-7135-4900-8761-f05044e55579"}, {"BlockType": "WORD", "Confidence": 96.65577697753906, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010455013252794743, "Height": 0.009578564204275608, "Left": 0.43134674429893494, "Top": 0.6267068982124329}, "Polygon": [{"X": 0.4313890039920807, "Y": 0.6267456412315369}, {"X": 0.4418017566204071, "Y": 0.6267068982124329}, {"X": 0.44176629185676575, "Y": 0.6362472176551819}, {"X": 0.43134674429893494, "Y": 0.6362854838371277}]}, "Id": "bc74301a-7934-4f41-b578-a526c72fdc6e"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09506702423095703, "Height": 0.010791641660034657, "Left": 0.4727807939052582, "Top": 0.6244590878486633}, "Polygon": [{"X": 0.4727973937988281, "Y": 0.6248133182525635}, {"X": 0.5677963495254517, "Y": 0.6244590878486633}, {"X": 0.5678478479385376, "Y": 0.6349009275436401}, {"X": 0.4727807939052582, "Y": 0.6352506875991821}]}, "Id": "002cb160-b6bd-4a8e-b347-885862fac994"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "2144114", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12774288654327393, "Height": 0.02161025069653988, "Left": 0.5882460474967957, "Top": 0.6151215434074402}, "Polygon": [{"X": 0.5882460474967957, "Y": 0.6156020164489746}, {"X": 0.7156698703765869, "Y": 0.6151215434074402}, {"X": 0.7159889340400696, "Y": 0.6362634301185608}, {"X": 0.5883799195289612, "Y": 0.6367318034172058}]}, "Id": "3238e992-917f-4456-9e23-0f7212f16ade"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1350", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06724908947944641, "Height": 0.017352038994431496, "Left": 0.8326568603515625, "Top": 0.6311973929405212}, "Polygon": [{"X": 0.8326568603515625, "Y": 0.6314438581466675}, {"X": 0.899432361125946, "Y": 0.6311973929405212}, {"X": 0.8999059200286865, "Y": 0.6483080387115479}, {"X": 0.8330519199371338, "Y": 0.6485494375228882}]}, "Id": "5f533fe3-d922-47cd-99f2-eac3efcb1e29"}, {"BlockType": "WORD", "Confidence": 28.481245040893555, "Text": "A", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08578246831893921, "Height": 0.10898826271295547, "Left": 0.030134841799736023, "Top": 0.7488461136817932}, "Polygon": [{"X": 0.033555954694747925, "Y": 0.7491052746772766}, {"X": 0.11591731011867523, "Y": 0.7488461136817932}, {"X": 0.1131044551730156, "Y": 0.857616126537323}, {"X": 0.030134841799736023, "Y": 0.8578343987464905}]}, "Id": "f568e661-05a2-459f-84d6-cff63edc6411"}, {"BlockType": "WORD", "Confidence": 63.52997589111328, "Text": "JBSK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12453446537256241, "Height": 0.049952153116464615, "Left": 0.13224372267723083, "Top": 0.7376835346221924}, "Polygon": [{"X": 0.13346703350543976, "Y": 0.7380778789520264}, {"X": 0.25677818059921265, "Y": 0.7376835346221924}, {"X": 0.2559705674648285, "Y": 0.7872691750526428}, {"X": 0.13224372267723083, "Y": 0.7876357436180115}]}, "Id": "7fd0b2c5-085f-4207-8872-ed799b3dad45"}, {"BlockType": "WORD", "Confidence": 54.852718353271484, "Text": "55K", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06585340201854706, "Height": 0.02629137597978115, "Left": 0.4993748962879181, "Top": 0.7528534531593323}, "Polygon": [{"X": 0.4993748962879181, "Y": 0.7530586123466492}, {"X": 0.5651055574417114, "Y": 0.7528534531593323}, {"X": 0.565228283405304, "Y": 0.7789474725723267}, {"X": 0.4993809759616852, "Y": 0.779144823551178}]}, "Id": "8cb1dda3-b6e2-4b87-80e3-a9b6af579db3"}, {"BlockType": "WORD", "Confidence": 99.44993591308594, "Text": "0810", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08441594988107681, "Height": 0.015546862035989761, "Left": 0.8726592659950256, "Top": 0.7744803428649902}, "Polygon": [{"X": 0.8726592659950256, "Y": 0.774733304977417}, {"X": 0.9565964937210083, "Y": 0.7744803428649902}, {"X": 0.9570752382278442, "Y": 0.7897801399230957}, {"X": 0.8730506300926208, "Y": 0.7900272011756897}]}, "Id": "9680f0c0-5748-454d-a8d3-e39cff4492e2"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0586438924074173, "Height": 0.013139449059963226, "Left": 0.1031089499592781, "Top": 0.7864025831222534}, "Polygon": [{"X": 0.10345438122749329, "Y": 0.7865756154060364}, {"X": 0.1617528349161148, "Y": 0.7864025831222534}, {"X": 0.16145864129066467, "Y": 0.7993724346160889}, {"X": 0.1031089499592781, "Y": 0.7995420098304749}]}, "Id": "9008d852-71f4-4af2-a814-be80e3f03e62"}, {"BlockType": "WORD", "Confidence": 91.52044677734375, "Text": "<PERSON><PERSON><PERSON> -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08523836731910706, "Height": 0.013350122608244419, "Left": 0.16800357401371002, "Top": 0.7860400080680847}, "Polygon": [{"X": 0.16829487681388855, "Y": 0.7862921953201294}, {"X": 0.25324195623397827, "Y": 0.7860400080680847}, {"X": 0.2530260980129242, "Y": 0.7991430759429932}, {"X": 0.16800357401371002, "Y": 0.7993901371955872}]}, "Id": "6bed8194-6499-4d27-9b56-464ab1f15462"}, {"BlockType": "WORD", "Confidence": 64.2528305053711, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020728327333927155, "Height": 0.010893130674958229, "Left": 0.47175541520118713, "Top": 0.7887774705886841}, "Polygon": [{"X": 0.4717732071876526, "Y": 0.7888386249542236}, {"X": 0.4924837350845337, "Y": 0.7887774705886841}, {"X": 0.49248120188713074, "Y": 0.7996104955673218}, {"X": 0.47175541520118713, "Y": 0.7996706366539001}]}, "Id": "79d25d1b-b6d2-4743-b82a-c7d86480b4a4"}, {"BlockType": "WORD", "Confidence": 97.18669891357422, "Text": "02143642", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07277651131153107, "Height": 0.011796885170042515, "Left": 0.4988088607788086, "Top": 0.7884330749511719}, "Polygon": [{"X": 0.4988088607788086, "Y": 0.7886477708816528}, {"X": 0.5715259313583374, "Y": 0.7884330749511719}, {"X": 0.5715853571891785, "Y": 0.8000190854072571}, {"X": 0.49881112575531006, "Y": 0.8002299666404724}]}, "Id": "aaebaaa9-3ab3-4002-b334-b10e748e30da"}, {"BlockType": "WORD", "Confidence": 99.36941528320312, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10025493800640106, "Height": 0.011609002947807312, "Left": 0.5767843723297119, "Top": 0.7887476086616516}, "Polygon": [{"X": 0.5767843723297119, "Y": 0.7890428900718689}, {"X": 0.6769002676010132, "Y": 0.7887476086616516}, {"X": 0.6770392656326294, "Y": 0.8000664710998535}, {"X": 0.5768464207649231, "Y": 0.8003566265106201}]}, "Id": "c607f85e-f0a7-42ff-a6a1-2c1e0a0433f2"}, {"BlockType": "WORD", "Confidence": 99.31979370117188, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10167774558067322, "Height": 0.011654924601316452, "Left": 0.6822301745414734, "Top": 0.7890265583992004}, "Polygon": [{"X": 0.6822301745414734, "Y": 0.7893255352973938}, {"X": 0.7836860418319702, "Y": 0.7890265583992004}, {"X": 0.7839078903198242, "Y": 0.8003877401351929}, {"X": 0.6823737025260925, "Y": 0.800681471824646}]}, "Id": "9585b273-159d-4946-a2f0-2e886d85ec9f"}, {"BlockType": "WORD", "Confidence": 85.29496002197266, "Text": "SO-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02828962355852127, "Height": 0.011466697789728642, "Left": 0.7890101671218872, "Top": 0.7892588376998901}, "Polygon": [{"X": 0.7890101671218872, "Y": 0.789341390132904}, {"X": 0.8170517086982727, "Y": 0.7892588376998901}, {"X": 0.8172997832298279, "Y": 0.8006443977355957}, {"X": 0.7892365455627441, "Y": 0.800725519657135}]}, "Id": "f57522c9-fa65-4d8a-812a-d3c556631fdd"}, {"BlockType": "WORD", "Confidence": 97.35192108154297, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011368862353265285, "Height": 0.010477334260940552, "Left": 0.3564881682395935, "Top": 0.7988196611404419}, "Polygon": [{"X": 0.35658687353134155, "Y": 0.7988524436950684}, {"X": 0.3678570091724396, "Y": 0.7988196611404419}, {"X": 0.36776629090309143, "Y": 0.8092647790908813}, {"X": 0.3564881682395935, "Y": 0.8092970252037048}]}, "Id": "8ff976e7-4356-4898-879e-fb0f56ade646"}, {"BlockType": "WORD", "Confidence": 94.2332992553711, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010945194400846958, "Height": 0.010165690444409847, "Left": 0.42942869663238525, "Top": 0.7988258600234985}, "Polygon": [{"X": 0.4294743835926056, "Y": 0.7988575100898743}, {"X": 0.44037389755249023, "Y": 0.7988258600234985}, {"X": 0.4403356909751892, "Y": 0.8089603781700134}, {"X": 0.42942869663238525, "Y": 0.808991551399231}]}, "Id": "1e7a2986-2344-4bf6-817c-087187b2d16e"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Metamora", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08984909951686859, "Height": 0.012662841007113457, "Left": 0.10179024189710617, "Top": 0.8072381019592285}, "Polygon": [{"X": 0.10212139785289764, "Y": 0.8074948787689209}, {"X": 0.19163934886455536, "Y": 0.8072381019592285}, {"X": 0.19138336181640625, "Y": 0.8196492195129395}, {"X": 0.10179024189710617, "Y": 0.8199009299278259}]}, "Id": "3c0467e6-21a1-4f13-a268-4704cf77bbcf"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "02143649", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07232972979545593, "Height": 0.011368567124009132, "Left": 0.472025066614151, "Top": 0.8055474758148193}, "Polygon": [{"X": 0.47204312682151794, "Y": 0.805755078792572}, {"X": 0.5443181991577148, "Y": 0.8055474758148193}, {"X": 0.5443547964096069, "Y": 0.8167121410369873}, {"X": 0.472025066614151, "Y": 0.8169160485267639}]}, "Id": "95a02a9c-19b5-469c-bb40-0511f3a447ef"}, {"BlockType": "WORD", "Confidence": 99.35408020019531, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10048762708902359, "Height": 0.011195294559001923, "Left": 0.5498266220092773, "Top": 0.8059464693069458}, "Polygon": [{"X": 0.5498266220092773, "Y": 0.8062344193458557}, {"X": 0.6502001881599426, "Y": 0.8059464693069458}, {"X": 0.6503142714500427, "Y": 0.8168588280677795}, {"X": 0.5498664975166321, "Y": 0.8171417713165283}]}, "Id": "c7ef30d4-8a36-4578-9906-40884fd278ff"}, {"BlockType": "WORD", "Confidence": 53.188777923583984, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10580578446388245, "Height": 0.011674792505800724, "Left": 0.6554705500602722, "Top": 0.8057776689529419}, "Polygon": [{"X": 0.6554705500602722, "Y": 0.8060805201530457}, {"X": 0.7610718607902527, "Y": 0.8057776689529419}, {"X": 0.7612763047218323, "Y": 0.817155122756958}, {"X": 0.6555935144424438, "Y": 0.8174524903297424}]}, "Id": "4e4636fd-ba5a-4915-87b5-4fee55c369fa"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "0820", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07849562913179398, "Height": 0.01957092434167862, "Left": 0.8775814771652222, "Top": 0.8109831213951111}, "Polygon": [{"X": 0.8775814771652222, "Y": 0.8112043738365173}, {"X": 0.9554743766784668, "Y": 0.8109831213951111}, {"X": 0.9560770988464355, "Y": 0.8303396701812744}, {"X": 0.8780818581581116, "Y": 0.8305540680885315}]}, "Id": "35fd3d9a-1c65-4cb6-bea6-ea2511ee2f33"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2144190", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1139921173453331, "Height": 0.01964999921619892, "Left": 0.5432329773902893, "Top": 0.8221961259841919}, "Polygon": [{"X": 0.5432329773902893, "Y": 0.8225136995315552}, {"X": 0.6570141315460205, "Y": 0.8221961259841919}, {"X": 0.6572250723838806, "Y": 0.8415384888648987}, {"X": 0.5432948470115662, "Y": 0.8418461084365845}]}, "Id": "90df3417-fca5-49b5-b446-97cecb251bff"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "2144112", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13451839983463287, "Height": 0.025301072746515274, "Left": 0.6862190961837769, "Top": 0.8214754462242126}, "Polygon": [{"X": 0.6862190961837769, "Y": 0.8218495845794678}, {"X": 0.8201898336410522, "Y": 0.8214754462242126}, {"X": 0.8207374811172485, "Y": 0.8464175462722778}, {"X": 0.6865401864051819, "Y": 0.8467764854431152}]}, "Id": "109bad4c-b3ca-436b-a785-e5060c2adfe7"}, {"BlockType": "WORD", "Confidence": 59.37818908691406, "Text": "1 SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07067469507455826, "Height": 0.038822002708911896, "Left": 0.15742184221744537, "Top": 0.853912353515625}, "Polygon": [{"X": 0.15830327570438385, "Y": 0.8540971279144287}, {"X": 0.22809654474258423, "Y": 0.853912353515625}, {"X": 0.22739708423614502, "Y": 0.8925619721412659}, {"X": 0.15742184221744537, "Y": 0.8927343487739563}]}, "Id": "e213796a-c188-4d5e-bbe7-cd734e41b724"}, {"BlockType": "WORD", "Confidence": 60.670440673828125, "Text": "Pickup", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13209716975688934, "Height": 0.034099314361810684, "Left": 0.4795437753200531, "Top": 0.8708834052085876}, "Polygon": [{"X": 0.4795810580253601, "Y": 0.8712210059165955}, {"X": 0.6113777756690979, "Y": 0.8708834052085876}, {"X": 0.6116409301757812, "Y": 0.904665470123291}, {"X": 0.4795437753200531, "Y": 0.9049827456474304}]}, "Id": "dc3731e6-5f6b-44e7-b9a6-50593e5eb979"}, {"BlockType": "WORD", "Confidence": 90.62400817871094, "Text": "1300", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08463035523891449, "Height": 0.02895326167345047, "Left": 0.8800288438796997, "Top": 0.8594411015510559}, "Polygon": [{"X": 0.8800288438796997, "Y": 0.859659731388092}, {"X": 0.9637510180473328, "Y": 0.8594411015510559}, {"X": 0.9646592140197754, "Y": 0.8881866931915283}, {"X": 0.8807742595672607, "Y": 0.8883943557739258}]}, "Id": "2b14a5c6-95c2-40ba-abef-eef24fef026d"}, {"BlockType": "WORD", "Confidence": 76.55990600585938, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.01872570998966694, "Height": 0.028062280267477036, "Left": 0.3625558912754059, "Top": 0.8780162334442139}, "Polygon": [{"X": 0.3628075122833252, "Y": 0.8780630230903625}, {"X": 0.38128161430358887, "Y": 0.8780162334442139}, {"X": 0.38106489181518555, "Y": 0.9060341715812683}, {"X": 0.3625558912754059, "Y": 0.9060785174369812}]}, "Id": "6828c269-818e-453a-94b9-308ec65c43a5"}, {"BlockType": "WORD", "Confidence": 44.64544677734375, "Text": "1345", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0771438404917717, "Height": 0.030931809917092323, "Left": 0.8937699198722839, "Top": 0.9007700085639954}, "Polygon": [{"X": 0.8937699198722839, "Y": 0.900954008102417}, {"X": 0.9699318408966064, "Y": 0.9007700085639954}, {"X": 0.9709137678146362, "Y": 0.9315285086631775}, {"X": 0.8945937752723694, "Y": 0.9317017793655396}]}, "Id": "fcdd3d77-525e-4e94-9eec-478a30459bac"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "YOUNGSTOWN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13918401300907135, "Height": 0.015937021002173424, "Left": 0.4375349283218384, "Top": 0.9418146014213562}, "Polygon": [{"X": 0.4375961720943451, "Y": 0.9421241283416748}, {"X": 0.5766341686248779, "Y": 0.9418146014213562}, {"X": 0.5767189264297485, "Y": 0.9574520587921143}, {"X": 0.4375349283218384, "Y": 0.9577515721321106}]}, "Id": "07e5f1db-7b9c-446f-959a-4bc2f47f12f8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "294de750-51c9-41a7-8087-4e40e7f3aacb", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "294de750-51c9-41a7-8087-4e40e7f3aacb", "content-type": "application/x-amz-json-1.1", "content-length": "73333", "date": "Wed, 24 Sep 2025 13:40:00 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "0bd53531-dd5b-4051-af48-0d9661a96f5e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:40:03 GMT", "content-type": "application/json", "content-length": "890", "connection": "keep-alive", "x-amzn-requestid": "0bd53531-dd5b-4051-af48-0d9661a96f5e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page only. Need to classify. The content seems like a Work Order or loading list, not a recognized document type. It lists work order numbers, SKUs, times, etc. This could be a \"work order\" but not in list. Could be \"pack_list\"? But pack list contains \"packing list\" header. Not present. Could be \"po\"? No. Could be \"ingate\" or \"outgate\"? No gate info. Could be \"tender_from_cust\"? No tender. Could be \"combined_carrier_documents\"? No multiple headers. Could be \"other\". So classify as other. But check for continuation: no. So page 1 other."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"other\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2842, "outputTokens": 159, "totalTokens": 3001}, "metrics": {"latencyMs": 913}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/84fe3b46_8_log_6.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nDATE: 5-Sep\nUNIT #: 6063/283420\nTruck Ready Time:\nDRIVER: KARRA\nTruck Departure:\nDEPARTURE TIME: 5am\nPICK-\nReceipt\nDock Time\nCUSTOMER\nUP\nDEL\nWORK ORDER #\n1025\n3SK\n4sk\nX\nAlumalloy - Avon Lake\nX\nSO-02143353 ***********\n1050\n6SK\nX General 2sm Boxes Extrusion\n1235\nX\nX\nSO-02142762 *********** ***********\n2143358\n1255\nO Vari-Wall 2SK Tube\nISK\n1335\nX\nX *********** 2144114\n1350\nA\nJBSK\n55K\n0810\nParker Hannifin -\nso 02143642 *********** *********** SO-\nX\nX\nMetamora\n02143649 *********** ***********\n0820\n2144190 2144112\n1 SK\nPickup\n1300\nX\n1345\nYOUNGSTOWN\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}