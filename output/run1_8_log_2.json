{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "01fd8b4c-fea9-420f-b551-23e7191a0da8", "Relationships": [{"Type": "CHILD", "Ids": ["7d5ffdf7-d03b-4adf-9873-f00e0d871323", "ee01d992-777f-412b-9848-4cdd744a8557", "11b46f54-87b9-4f5b-9300-dcc37f85b424", "eb654947-64fe-4354-82fc-b3acf0de1d23", "3ffa2dd7-33e9-40b2-b0b5-3f0582e08ec7", "ad213ca5-7b1d-4e82-a487-0e5000ed7270", "0f35522f-3670-4b9f-99ca-940634581a9f", "3d0ad064-b146-4164-a443-28a8cb195b2d", "73a8552f-0754-4850-a2dd-6f6e92f05bca", "85282e84-160c-49e9-927d-44952cfe697d", "744b6b9a-26cb-49c5-bf0a-85b2d9b226a3", "7a36792e-b0c8-4953-96e5-c95b8defde91", "ca4cb482-554f-4d2c-ac37-7e3db8d6c2ff", "538f75c5-4bea-4e5b-904e-6d0bcdc609e3", "b57031ee-50d2-45eb-a9d6-6593236d4e55", "813f48b3-46aa-487e-8658-85b40a839301", "b7e4513f-6f1e-438d-a7a8-13880242d745", "16da35d2-02da-4bbd-a497-a70819a80582", "872608e7-5436-401b-9e78-dd33fce30f17", "2ad6fadb-73d7-4de2-8520-b75c2626a378", "5ac3f42d-d93d-4a8f-a57b-f84897e07c51", "77e33c39-3328-4984-b3f1-840d7524b0c4", "21e9b241-ccd8-45de-9495-218467ca7985", "5c3a45ee-96f1-4e92-815e-ee9054bad3dc", "a74db651-86f7-4ed8-b1a2-d6669169cd0f", "ea204ca7-d6cc-48ee-960d-c63b68e9a0d9", "089a8bd3-2820-4f6b-86a5-75c5633c00f4", "be5da984-6299-468a-b6f4-22b2d0244aff", "8a805bf1-d653-43d8-9cdf-852af20f3519", "e5afa92e-a5f5-4761-9305-56eb4c8c10cc", "35f29599-4c02-4530-beaa-54131ffcb3d2", "96cbb2f0-fdec-4395-b24d-4f90266ba2d5", "a44a4aec-87e3-4a3a-a397-4a973168a6c2", "3ff9ef21-83a0-4445-899f-37c21c7cda4f", "951546cf-b0c8-4c0f-b657-e3e17e9593c4", "c59b534d-9fb1-4d8a-b63e-e8c2952473f5", "3789eb6b-b755-40f3-bf2c-81aea3bb7655", "fee5981d-1701-4d81-924f-1750ca697e37", "1fefdd99-7bf3-481e-b5a5-1206ff9f7381", "cce565a8-bfaf-48ac-9c58-15f05487da21", "c08e1374-e015-48b6-a124-2581e835e31e", "35774ef5-2062-4313-8798-f212ae2656ea", "49ff26e3-d259-4611-b3dd-16589b46c827", "eb008558-f6d0-4487-92d6-09b63ac6c779", "47956069-5d83-47eb-b193-b21b36e77715", "3f016810-e79d-4e7d-90e3-4e70869059b1", "42040be5-856d-4b4c-9241-2ac2547b440f", "d4bc4765-0b71-4160-bfeb-27e369006135", "69456896-3072-4739-8c7b-2d6129abcee6"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "DATE: 19-Feb", "Geometry": {"BoundingBox": {"Width": 0.13736645877361298, "Height": 0.013747281394898891, "Left": 0.242889404296875, "Top": 0.10980033129453659}, "Polygon": [{"X": 0.2428894191980362, "Y": 0.10980033129453659}, {"X": 0.3802558481693268, "Y": 0.10980384796857834}, {"X": 0.38025587797164917, "Y": 0.12354760617017746}, {"X": 0.242889404296875, "Y": 0.1235441043972969}]}, "Id": "7d5ffdf7-d03b-4adf-9873-f00e0d871323", "Relationships": [{"Type": "CHILD", "Ids": ["06d88c8a-0691-4df5-b888-679e21661490", "0445db2d-b779-4913-bb37-bf25b161a06e"]}]}, {"BlockType": "LINE", "Confidence": 99.39453125, "Text": "UNIT #: 92644/283420", "Geometry": {"BoundingBox": {"Width": 0.220144584774971, "Height": 0.015344031155109406, "Left": 0.2269246131181717, "Top": 0.14510323107242584}, "Polygon": [{"X": 0.22692462801933289, "Y": 0.14510323107242584}, {"X": 0.4470691680908203, "Y": 0.14510881900787354}, {"X": 0.4470691978931427, "Y": 0.16044725477695465}, {"X": 0.2269246131181717, "Y": 0.16044169664382935}]}, "Id": "ee01d992-777f-412b-9848-4cdd744a8557", "Relationships": [{"Type": "CHILD", "Ids": ["35c48415-91e0-4b54-8eae-aad1c7437afa", "dcf59897-671b-44b8-b506-a7ef936f99e9", "1eee5855-af7e-4544-baca-f2d02a2e3ece"]}]}, {"BlockType": "LINE", "Confidence": 99.74061584472656, "Text": "Truck Ready Time: 5:00", "Geometry": {"BoundingBox": {"Width": 0.29648563265800476, "Height": 0.03428361937403679, "Left": 0.4656304717063904, "Top": 0.14309269189834595}, "Polygon": [{"X": 0.4656304717063904, "Y": 0.14309269189834595}, {"X": 0.7621158361434937, "Y": 0.14310021698474884}, {"X": 0.7621161341667175, "Y": 0.17737631499767303}, {"X": 0.46563059091567993, "Y": 0.1773688644170761}]}, "Id": "11b46f54-87b9-4f5b-9300-dcc37f85b424", "Relationships": [{"Type": "CHILD", "Ids": ["d0d42ba5-2538-43cc-9f33-855460e49374", "ba666687-d495-41c1-ba5c-6a284e7009ef", "2c0af061-6329-498d-b7b5-8a9e8bcfb6fc", "f4f63b21-4f54-4cf1-8974-dba12f8f3d6f"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.1634538322687149, "Height": 0.013153064996004105, "Left": 0.21907995641231537, "Top": 0.18155279755592346}, "Polygon": [{"X": 0.21907997131347656, "Y": 0.18155279755592346}, {"X": 0.3825337588787079, "Y": 0.18155691027641296}, {"X": 0.3825337886810303, "Y": 0.19470587372779846}, {"X": 0.21907995641231537, "Y": 0.19470177590847015}]}, "Id": "eb654947-64fe-4354-82fc-b3acf0de1d23", "Relationships": [{"Type": "CHILD", "Ids": ["41c3edf4-73fe-46b7-8b82-aa9ec2209511", "28d4440b-cc89-4a2d-89cc-5e520a70270d"]}]}, {"BlockType": "LINE", "Confidence": 99.46109771728516, "Text": "Truck Departure: 5:00", "Geometry": {"BoundingBox": {"Width": 0.29770052433013916, "Height": 0.0331379659473896, "Left": 0.4665309488773346, "Top": 0.17971383035182953}, "Polygon": [{"X": 0.4665309488773346, "Y": 0.17971383035182953}, {"X": 0.7642312049865723, "Y": 0.17972131073474884}, {"X": 0.7642314434051514, "Y": 0.21285179257392883}, {"X": 0.46653103828430176, "Y": 0.2128443866968155}]}, "Id": "3ffa2dd7-33e9-40b2-b0b5-3f0582e08ec7", "Relationships": [{"Type": "CHILD", "Ids": ["2804b0ba-168d-486b-9dc7-bc6cc11a3732", "dbb768ae-fa15-428c-81a5-26d96dc65fda", "22b98fc8-a5d5-4914-b0fe-76232d808228"]}]}, {"BlockType": "LINE", "Confidence": 99.75885009765625, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.22939634323120117, "Height": 0.013709722086787224, "Left": 0.13103356957435608, "Top": 0.21678389608860016}, "Polygon": [{"X": 0.13103359937667847, "Y": 0.21678389608860016}, {"X": 0.36042988300323486, "Y": 0.2167896032333374}, {"X": 0.36042991280555725, "Y": 0.23049362003803253}, {"X": 0.13103356957435608, "Y": 0.23048794269561768}]}, "Id": "ad213ca5-7b1d-4e82-a487-0e5000ed7270", "Relationships": [{"Type": "CHILD", "Ids": ["dd4e2bbd-7042-484b-8ac8-3cf724801ec0", "589ceab8-c49d-4c08-9753-ca77309a8334", "7cf2e591-8426-4a09-8840-15c723ad5bd4"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.04785889387130737, "Height": 0.010938746854662895, "Left": 0.3347736895084381, "Top": 0.2478572279214859}, "Polygon": [{"X": 0.3347736895084381, "Y": 0.2478572279214859}, {"X": 0.3826325535774231, "Y": 0.24785840511322021}, {"X": 0.3826325535774231, "Y": 0.25879597663879395}, {"X": 0.3347736895084381, "Y": 0.2587948143482208}]}, "Id": "0f35522f-3670-4b9f-99ca-940634581a9f", "Relationships": [{"Type": "CHILD", "Ids": ["b124732a-bfb2-4ef3-b2e9-374a3229367c"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.09535559266805649, "Height": 0.01263468898832798, "Left": 0.8257697224617004, "Top": 0.2528533637523651}, "Polygon": [{"X": 0.8257697224617004, "Y": 0.2528533637523651}, {"X": 0.9211251735687256, "Y": 0.25285571813583374}, {"X": 0.9211253523826599, "Y": 0.26548805832862854}, {"X": 0.82576984167099, "Y": 0.2654857337474823}]}, "Id": "3d0ad064-b146-4164-a443-28a8cb195b2d", "Relationships": [{"Type": "CHILD", "Ids": ["22d3074b-a2ae-4f91-b38e-ee5aefc0dc14", "cd673ac1-305b-4249-afce-74240e7a1bc6"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.10067279636859894, "Height": 0.012615521438419819, "Left": 0.16310399770736694, "Top": 0.2676003575325012}, "Polygon": [{"X": 0.16310402750968933, "Y": 0.2676003575325012}, {"X": 0.2637767791748047, "Y": 0.267602801322937}, {"X": 0.2637767791748047, "Y": 0.280215859413147}, {"X": 0.16310399770736694, "Y": 0.2802134156227112}]}, "Id": "73a8552f-0754-4850-a2dd-6f6e92f05bca", "Relationships": [{"Type": "CHILD", "Ids": ["5ec588a7-ff1c-456b-9485-a0b658fb983d"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "UP DEL", "Geometry": {"BoundingBox": {"Width": 0.09985791891813278, "Height": 0.012824908830225468, "Left": 0.3471435308456421, "Top": 0.26519033312797546}, "Polygon": [{"X": 0.3471435308456421, "Y": 0.26519033312797546}, {"X": 0.4470013976097107, "Y": 0.26519277691841125}, {"X": 0.44700145721435547, "Y": 0.27801522612571716}, {"X": 0.3471435308456421, "Y": 0.27801281213760376}]}, "Id": "85282e84-160c-49e9-927d-44952cfe697d", "Relationships": [{"Type": "CHILD", "Ids": ["c7d559cc-ef73-414d-9fc0-4ddb016cdbfe", "0e593f8b-c431-437d-8179-d591d7a49a9c"]}]}, {"BlockType": "LINE", "Confidence": 99.79166412353516, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.1428801417350769, "Height": 0.014172698371112347, "Left": 0.5687410831451416, "Top": 0.2624422609806061}, "Polygon": [{"X": 0.5687410831451416, "Y": 0.2624422609806061}, {"X": 0.711621105670929, "Y": 0.2624457776546478}, {"X": 0.7116212248802185, "Y": 0.2766149640083313}, {"X": 0.5687411427497864, "Y": 0.27661147713661194}]}, "Id": "744b6b9a-26cb-49c5-bf0a-85b2d9b226a3", "Relationships": [{"Type": "CHILD", "Ids": ["7a5cdb67-4ff2-4f77-82c8-4107c6d638d5", "59ea0f62-cfd2-4272-8f8a-c5cfa783ebda", "eb8baba7-e80f-40af-be7e-1272abd66b9f"]}]}, {"BlockType": "LINE", "Confidence": 60.5707893371582, "Text": "755h tempty", "Geometry": {"BoundingBox": {"Width": 0.25745174288749695, "Height": 0.031768668442964554, "Left": 0.34646841883659363, "Top": 0.29425063729286194}, "Polygon": [{"X": 0.34646841883659363, "Y": 0.29425063729286194}, {"X": 0.6039199829101562, "Y": 0.29425686597824097}, {"X": 0.6039201617240906, "Y": 0.326019287109375}, {"X": 0.346468448638916, "Y": 0.32601311802864075}]}, "Id": "7a36792e-b0c8-4953-96e5-c95b8defde91", "Relationships": [{"Type": "CHILD", "Ids": ["e9070a0d-0a84-4605-b975-1ca3d8e37995", "2806e239-e001-4527-9725-442ddb05a3f8"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "1230", "Geometry": {"BoundingBox": {"Width": 0.08113858103752136, "Height": 0.01772553101181984, "Left": 0.8646883368492126, "Top": 0.2991727888584137}, "Polygon": [{"X": 0.8646883368492126, "Y": 0.2991727888584137}, {"X": 0.9458267092704773, "Y": 0.2991747558116913}, {"X": 0.9458268880844116, "Y": 0.31689831614494324}, {"X": 0.864688515663147, "Y": 0.31689637899398804}]}, "Id": "ca4cb482-554f-4d2c-ac37-7e3db8d6c2ff", "Relationships": [{"Type": "CHILD", "Ids": ["fbe3a23c-74d6-4b7c-b8bb-cbe9af6f4fb5"]}]}, {"BlockType": "LINE", "Confidence": 99.6479721069336, "Text": "3) General Extrusion", "Geometry": {"BoundingBox": {"Width": 0.21305465698242188, "Height": 0.05147218704223633, "Left": 0.0483834333717823, "Top": 0.31561723351478577}, "Polygon": [{"X": 0.04838363081216812, "Y": 0.31561723351478577}, {"X": 0.2614381015300751, "Y": 0.31562235951423645}, {"X": 0.2614380717277527, "Y": 0.3670894205570221}, {"X": 0.0483834333717823, "Y": 0.3670843541622162}]}, "Id": "538f75c5-4bea-4e5b-904e-6d0bcdc609e3", "Relationships": [{"Type": "CHILD", "Ids": ["6d861e26-3c8f-46bd-a014-7ea01bd2a7d2", "e1356693-b8ed-431c-a79c-6c4f53502e15", "91da31a8-70da-4701-995a-db4b46a333a8"]}]}, {"BlockType": "LINE", "Confidence": 92.66522216796875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01097073033452034, "Height": 0.009613018482923508, "Left": 0.35543903708457947, "Top": 0.3360922932624817}, "Polygon": [{"X": 0.35543903708457947, "Y": 0.3360922932624817}, {"X": 0.3664097487926483, "Y": 0.3360925316810608}, {"X": 0.3664097785949707, "Y": 0.3457053005695343}, {"X": 0.35543903708457947, "Y": 0.3457050323486328}]}, "Id": "b57031ee-50d2-45eb-a9d6-6593236d4e55", "Relationships": [{"Type": "CHILD", "Ids": ["0a80a3ee-225f-4f50-a2c6-04df1c2a47cd"]}]}, {"BlockType": "LINE", "Confidence": 95.95553588867188, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.13798269629478455, "Height": 0.012423958629369736, "Left": 0.42450857162475586, "Top": 0.3325625956058502}, "Polygon": [{"X": 0.42450857162475586, "Y": 0.3325625956058502}, {"X": 0.5624911785125732, "Y": 0.33256590366363525}, {"X": 0.562491238117218, "Y": 0.34498655796051025}, {"X": 0.42450860142707825, "Y": 0.3449832499027252}]}, "Id": "813f48b3-46aa-487e-8658-85b40a839301", "Relationships": [{"Type": "CHILD", "Ids": ["a2e17e7e-a021-4b79-9674-b82e2de81590", "b971a092-1016-4d56-b2d9-44e364137efa"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1305", "Geometry": {"BoundingBox": {"Width": 0.06859823316335678, "Height": 0.025042375549674034, "Left": 0.8614248633384705, "Top": 0.3425601124763489}, "Polygon": [{"X": 0.8614248633384705, "Y": 0.3425601124763489}, {"X": 0.9300227761268616, "Y": 0.3425617516040802}, {"X": 0.9300230741500854, "Y": 0.36760246753692627}, {"X": 0.8614251017570496, "Y": 0.36760085821151733}]}, "Id": "b7e4513f-6f1e-438d-a7a8-13880242d745", "Relationships": [{"Type": "CHILD", "Ids": ["a0b9ec94-3569-4f1e-9e30-98a5196b9513"]}]}, {"BlockType": "LINE", "Confidence": 38.58457946777344, "Text": "x", "Geometry": {"BoundingBox": {"Width": 0.14296767115592957, "Height": 0.05359954759478569, "Left": 0.15001697838306427, "Top": 0.3605818748474121}, "Polygon": [{"X": 0.15001708269119263, "Y": 0.3605818748474121}, {"X": 0.29298463463783264, "Y": 0.3605852723121643}, {"X": 0.29298463463783264, "Y": 0.4141814112663269}, {"X": 0.15001697838306427, "Y": 0.4141780734062195}]}, "Id": "16da35d2-02da-4bbd-a497-a70819a80582", "Relationships": [{"Type": "CHILD", "Ids": ["f1249ef7-5d82-4b68-98d1-768c28689bf7"]}]}, {"BlockType": "LINE", "Confidence": 36.12962341308594, "Text": "tsk", "Geometry": {"BoundingBox": {"Width": 0.08811807632446289, "Height": 0.048168737441301346, "Left": 0.3784377872943878, "Top": 0.41067251563072205}, "Polygon": [{"X": 0.3784377872943878, "Y": 0.41067251563072205}, {"X": 0.46655571460723877, "Y": 0.4106745719909668}, {"X": 0.4665558636188507, "Y": 0.4588412642478943}, {"X": 0.378437876701355, "Y": 0.4588392376899719}]}, "Id": "872608e7-5436-401b-9e78-dd33fce30f17", "Relationships": [{"Type": "CHILD", "Ids": ["08073102-e5e1-400b-a3b7-21d476407f4f"]}]}, {"BlockType": "LINE", "Confidence": 90.77068328857422, "Text": "2) Vari-Wall 5 SK Tube", "Geometry": {"BoundingBox": {"Width": 0.18958589434623718, "Height": 0.060547247529029846, "Left": 0.04938208684325218, "Top": 0.4360446333885193}, "Polygon": [{"X": 0.049382321536540985, "Y": 0.4360446333885193}, {"X": 0.23896798491477966, "Y": 0.43604904413223267}, {"X": 0.23896794021129608, "Y": 0.4965918958187103}, {"X": 0.04938208684325218, "Y": 0.4965875744819641}]}, "Id": "2ad6fadb-73d7-4de2-8520-b75c2626a378", "Relationships": [{"Type": "CHILD", "Ids": ["8e76194c-f768-49c2-9091-85e4e978d17f", "a37c6e49-6e24-41b6-a999-74770971e426", "120dc646-a0c0-49c8-a7c0-2819c76c6368", "b2a64854-65a0-41e7-8994-cc6254c7a664"]}]}, {"BlockType": "LINE", "Confidence": 87.46551513671875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01127795409411192, "Height": 0.009594044648110867, "Left": 0.35788819193840027, "Top": 0.4525049924850464}, "Polygon": [{"X": 0.35788819193840027, "Y": 0.4525049924850464}, {"X": 0.3691661059856415, "Y": 0.4525052607059479}, {"X": 0.36916613578796387, "Y": 0.4620990455150604}, {"X": 0.35788819193840027, "Y": 0.46209877729415894}]}, "Id": "5ac3f42d-d93d-4a8f-a57b-f84897e07c51", "Relationships": [{"Type": "CHILD", "Ids": ["9f9fe495-70cc-46a8-adf2-01591afe80b2"]}]}, {"BlockType": "LINE", "Confidence": 94.29707336425781, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010941325686872005, "Height": 0.009150148369371891, "Left": 0.42788010835647583, "Top": 0.45209845900535583}, "Polygon": [{"X": 0.42788010835647583, "Y": 0.45209845900535583}, {"X": 0.438821405172348, "Y": 0.4520987272262573}, {"X": 0.4388214349746704, "Y": 0.46124860644340515}, {"X": 0.4278801381587982, "Y": 0.46124836802482605}]}, "Id": "77e33c39-3328-4984-b3f1-840d7524b0c4", "Relationships": [{"Type": "CHILD", "Ids": ["e1465bad-5eb6-4934-bfcc-824593bd9b19"]}]}, {"BlockType": "LINE", "Confidence": 87.66873931884766, "Text": "so 02115817 ***********", "Geometry": {"BoundingBox": {"Width": 0.195656880736351, "Height": 0.011820915155112743, "Left": 0.46836045384407043, "Top": 0.44820475578308105}, "Polygon": [{"X": 0.46836045384407043, "Y": 0.44820475578308105}, {"X": 0.6640172600746155, "Y": 0.448209285736084}, {"X": 0.6640173196792603, "Y": 0.4600256681442261}, {"X": 0.4683605134487152, "Y": 0.46002116799354553}]}, "Id": "21e9b241-ccd8-45de-9495-218467ca7985", "Relationships": [{"Type": "CHILD", "Ids": ["51144936-4989-48e8-9e31-11e5a5103bad", "1e0704c0-33e9-465b-92d3-fae10fcb97b0", "819142a8-2b6b-4037-9309-c74967b1862b"]}]}, {"BlockType": "LINE", "Confidence": 49.876434326171875, "Text": "1000", "Geometry": {"BoundingBox": {"Width": 0.08367662876844406, "Height": 0.020543985068798065, "Left": 0.8451106548309326, "Top": 0.4423898756504059}, "Polygon": [{"X": 0.8451106548309326, "Y": 0.4423898756504059}, {"X": 0.9287870526313782, "Y": 0.4423918128013611}, {"X": 0.9287872314453125, "Y": 0.46293386816978455}, {"X": 0.8451108336448669, "Y": 0.46293193101882935}]}, "Id": "5c3a45ee-96f1-4e92-815e-ee9054bad3dc", "Relationships": [{"Type": "CHILD", "Ids": ["e10bcdc1-e468-4466-87d8-ddc6bc3314ad"]}]}, {"BlockType": "LINE", "Confidence": 62.83063888549805, "Text": "follow to", "Geometry": {"BoundingBox": {"Width": 0.2991342842578888, "Height": 0.05636539310216904, "Left": 0.507244884967804, "Top": 0.462825208902359}, "Polygon": [{"X": 0.507244884967804, "Y": 0.462825208902359}, {"X": 0.8063786625862122, "Y": 0.46283209323883057}, {"X": 0.8063791990280151, "Y": 0.5191906094551086}, {"X": 0.5072451233863831, "Y": 0.519183874130249}]}, "Id": "a74db651-86f7-4ed8-b1a2-d6669169cd0f", "Relationships": [{"Type": "CHILD", "Ids": ["0537b688-067f-48ff-ad5a-cdb0a03f530f", "d4136cc6-bae5-414c-a8f9-2bee32479980"]}]}, {"BlockType": "LINE", "Confidence": 98.583984375, "Text": "0936", "Geometry": {"BoundingBox": {"Width": 0.074602872133255, "Height": 0.017255874350667, "Left": 0.8730655312538147, "Top": 0.46400681138038635}, "Polygon": [{"X": 0.8730655312538147, "Y": 0.46400681138038635}, {"X": 0.947668194770813, "Y": 0.46400853991508484}, {"X": 0.9476684331893921, "Y": 0.4812626838684082}, {"X": 0.873065710067749, "Y": 0.4812609851360321}]}, "Id": "ea204ca7-d6cc-48ee-960d-c63b68e9a0d9", "Relationships": [{"Type": "CHILD", "Ids": ["78c8f473-c11f-4404-8289-46f45465b8f9"]}]}, {"BlockType": "LINE", "Confidence": 73.31373596191406, "Text": "1015", "Geometry": {"BoundingBox": {"Width": 0.09938754886388779, "Height": 0.024359211325645447, "Left": 0.8383670449256897, "Top": 0.48931023478507996}, "Polygon": [{"X": 0.8383670449256897, "Y": 0.48931023478507996}, {"X": 0.9377543330192566, "Y": 0.4893124997615814}, {"X": 0.9377545714378357, "Y": 0.5136694312095642}, {"X": 0.8383672833442688, "Y": 0.5136672258377075}]}, "Id": "089a8bd3-2820-4f6b-86a5-75c5633c00f4", "Relationships": [{"Type": "CHILD", "Ids": ["707d5716-b115-4503-8f70-4a737fd035f9"]}]}, {"BlockType": "LINE", "Confidence": 93.0676040649414, "Text": "1)", "Geometry": {"BoundingBox": {"Width": 0.02804512158036232, "Height": 0.04745045676827431, "Left": 0.06031886115670204, "Top": 0.5581309795379639}, "Polygon": [{"X": 0.06031903252005577, "Y": 0.5581309795379639}, {"X": 0.08836398273706436, "Y": 0.5581316351890564}, {"X": 0.08836383372545242, "Y": 0.6055814623832703}, {"X": 0.06031886115670204, "Y": 0.6055808067321777}]}, "Id": "be5da984-6299-468a-b6f4-22b2d0244aff", "Relationships": [{"Type": "CHILD", "Ids": ["91051dfa-667d-4144-bc96-bd54729bf2cb"]}]}, {"BlockType": "LINE", "Confidence": 63.26906204223633, "Text": "In Inlandplt plt", "Geometry": {"BoundingBox": {"Width": 0.09732144325971603, "Height": 0.015106776729226112, "Left": 0.12440148741006851, "Top": 0.5579501390457153}, "Polygon": [{"X": 0.1244015246629715, "Y": 0.5579501390457153}, {"X": 0.22172293066978455, "Y": 0.557952344417572}, {"X": 0.22172291576862335, "Y": 0.5730569362640381}, {"X": 0.12440148741006851, "Y": 0.5730547904968262}]}, "Id": "8a805bf1-d653-43d8-9cdf-852af20f3519", "Relationships": [{"Type": "CHILD", "Ids": ["bdcc176b-ec3f-4d5f-a56c-b993a293fba7", "aafd2775-24ff-4246-b85b-3a18bcedbe70", "960e5c17-4822-4c51-a346-09e9bdc48c9a"]}]}, {"BlockType": "LINE", "Confidence": 96.89147186279297, "Text": "<PERSON> -", "Geometry": {"BoundingBox": {"Width": 0.146476611495018, "Height": 0.013580741360783577, "Left": 0.11173728853464127, "Top": 0.5699862837791443}, "Polygon": [{"X": 0.11173732578754425, "Y": 0.5699862837791443}, {"X": 0.25821390748023987, "Y": 0.5699895024299622}, {"X": 0.2582138776779175, "Y": 0.5835670232772827}, {"X": 0.11173728853464127, "Y": 0.5835638046264648}]}, "Id": "e5afa92e-a5f5-4761-9305-56eb4c8c10cc", "Relationships": [{"Type": "CHILD", "Ids": ["67582f41-4555-4ecb-89de-31d20d6a2abf", "9ac2bd45-87a9-4e78-b09a-c0137fdea838", "c517560e-83df-4a2b-91c3-76c8ca67d399"]}]}, {"BlockType": "LINE", "Confidence": 88.34123229980469, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011289657093584538, "Height": 0.00977187417447567, "Left": 0.3594965636730194, "Top": 0.580009400844574}, "Polygon": [{"X": 0.3594965636730194, "Y": 0.580009400844574}, {"X": 0.370786190032959, "Y": 0.5800096988677979}, {"X": 0.37078621983528137, "Y": 0.5897812843322754}, {"X": 0.3594965636730194, "Y": 0.5897810459136963}]}, "Id": "35f29599-4c02-4530-beaa-54131ffcb3d2", "Relationships": [{"Type": "CHILD", "Ids": ["7a488626-043c-4b26-8306-1e48242e07eb"]}]}, {"BlockType": "LINE", "Confidence": 89.63150024414062, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011617789044976234, "Height": 0.009949511848390102, "Left": 0.42950552701950073, "Top": 0.5791550278663635}, "Polygon": [{"X": 0.42950552701950073, "Y": 0.5791550278663635}, {"X": 0.4411233067512512, "Y": 0.5791552662849426}, {"X": 0.4411233067512512, "Y": 0.5891045331954956}, {"X": 0.4295055568218231, "Y": 0.5891042947769165}]}, "Id": "96cbb2f0-fdec-4395-b24d-4f90266ba2d5", "Relationships": [{"Type": "CHILD", "Ids": ["d507ec1d-2b61-4c33-8ed2-925c93d080a3"]}]}, {"BlockType": "LINE", "Confidence": 84.64356994628906, "Text": "so 02115450 *********** so 0211626", "Geometry": {"BoundingBox": {"Width": 0.293038547039032, "Height": 0.01309666596353054, "Left": 0.47128432989120483, "Top": 0.5739253759384155}, "Polygon": [{"X": 0.47128432989120483, "Y": 0.5739253759384155}, {"X": 0.764322817325592, "Y": 0.5739319324493408}, {"X": 0.7643228769302368, "Y": 0.587022066116333}, {"X": 0.4712843894958496, "Y": 0.5870155692100525}]}, "Id": "a44a4aec-87e3-4a3a-a397-4a973168a6c2", "Relationships": [{"Type": "CHILD", "Ids": ["18bea0dd-fa3e-462f-bad7-eca1f14bffd3", "dd2db0ce-c473-41c1-bf63-adb7d97a9be1", "f0a5f569-1006-4328-b633-aa3111dee178", "3e579f13-bde5-464b-bef0-0a7534be0092", "62304cf2-e654-448c-9924-957099841594"]}]}, {"BlockType": "LINE", "Confidence": 99.69486999511719, "Text": "Metamora", "Geometry": {"BoundingBox": {"Width": 0.0857909545302391, "Height": 0.012538857758045197, "Left": 0.1119719073176384, "Top": 0.5901594161987305}, "Polygon": [{"X": 0.11197194457054138, "Y": 0.5901594161987305}, {"X": 0.1977628618478775, "Y": 0.5901613235473633}, {"X": 0.1977628469467163, "Y": 0.6026982665061951}, {"X": 0.1119719073176384, "Y": 0.602696418762207}]}, "Id": "3ff9ef21-83a0-4445-899f-37c21c7cda4f", "Relationships": [{"Type": "CHILD", "Ids": ["32cde4ea-c93f-4e25-bbc9-23f9bbf2cc2b"]}]}, {"BlockType": "LINE", "Confidence": 87.93367004394531, "Text": "50-2116053", "Geometry": {"BoundingBox": {"Width": 0.21994942426681519, "Height": 0.023283783346414566, "Left": 0.47301778197288513, "Top": 0.5878908038139343}, "Polygon": [{"X": 0.47301778197288513, "Y": 0.5878908038139343}, {"X": 0.6929670572280884, "Y": 0.5878956913948059}, {"X": 0.6929671764373779, "Y": 0.6111745834350586}, {"X": 0.4730178415775299, "Y": 0.6111698150634766}]}, "Id": "951546cf-b0c8-4c0f-b657-e3e17e9593c4", "Relationships": [{"Type": "CHILD", "Ids": ["bd2520fa-376c-4d7f-bc24-37a48881888b"]}]}, {"BlockType": "LINE", "Confidence": 97.95121002197266, "Text": "0540", "Geometry": {"BoundingBox": {"Width": 0.07629060745239258, "Height": 0.01622743532061577, "Left": 0.8599780201911926, "Top": 0.5859130620956421}, "Polygon": [{"X": 0.8599780201911926, "Y": 0.5859130620956421}, {"X": 0.9362684488296509, "Y": 0.5859147310256958}, {"X": 0.9362686276435852, "Y": 0.602140486240387}, {"X": 0.859978199005127, "Y": 0.6021388173103333}]}, "Id": "c59b534d-9fb1-4d8a-b63e-e8c2952473f5", "Relationships": [{"Type": "CHILD", "Ids": ["9605bcd7-685d-4cf4-8443-074bd7321442"]}]}, {"BlockType": "LINE", "Confidence": 52.90119171142578, "Text": "1 sm box", "Geometry": {"BoundingBox": {"Width": 0.117005355656147, "Height": 0.029255716130137444, "Left": 0.125104159116745, "Top": 0.6088439226150513}, "Polygon": [{"X": 0.12510423362255096, "Y": 0.6088439226150513}, {"X": 0.2421095222234726, "Y": 0.6088464856147766}, {"X": 0.2421095073223114, "Y": 0.6380996704101562}, {"X": 0.125104159116745, "Y": 0.6380971074104309}]}, "Id": "3789eb6b-b755-40f3-bf2c-81aea3bb7655", "Relationships": [{"Type": "CHILD", "Ids": ["01d9d915-6eac-4f96-b873-9325fd0265d1"]}]}, {"BlockType": "LINE", "Confidence": 99.521484375, "Text": "0600", "Geometry": {"BoundingBox": {"Width": 0.08056437224149704, "Height": 0.017424000427126884, "Left": 0.8577093482017517, "Top": 0.6094968914985657}, "Polygon": [{"X": 0.8577093482017517, "Y": 0.6094968914985657}, {"X": 0.938273549079895, "Y": 0.6094986200332642}, {"X": 0.9382737278938293, "Y": 0.6269208788871765}, {"X": 0.857709527015686, "Y": 0.6269190907478333}]}, "Id": "fee5981d-1701-4d81-924f-1750ca697e37", "Relationships": [{"Type": "CHILD", "Ids": ["2fcb2683-ae55-44d8-86b9-5b70f417b64a"]}]}, {"BlockType": "LINE", "Confidence": 68.00103759765625, "Text": "Cempties aspots", "Geometry": {"BoundingBox": {"Width": 0.3441469073295593, "Height": 0.04259765148162842, "Left": 0.39649271965026855, "Top": 0.6148432493209839}, "Polygon": [{"X": 0.39649271965026855, "Y": 0.6148432493209839}, {"X": 0.7406392693519592, "Y": 0.6148508191108704}, {"X": 0.7406396269798279, "Y": 0.6574409008026123}, {"X": 0.3964928090572357, "Y": 0.6574335098266602}]}, "Id": "1fefdd99-7bf3-481e-b5a5-1206ff9f7381", "Relationships": [{"Type": "CHILD", "Ids": ["b8f448fb-ea25-49d7-a6c1-c7970933a388", "fb41e9f5-83c8-49da-a497-1d754e5429d4"]}]}, {"BlockType": "LINE", "Confidence": 98.31871795654297, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.026098841801285744, "Height": 0.04726731777191162, "Left": 0.0359298475086689, "Top": 0.6634374260902405}, "Polygon": [{"X": 0.03593004122376442, "Y": 0.6634374260902405}, {"X": 0.06202869117259979, "Y": 0.6634379625320435}, {"X": 0.06202851980924606, "Y": 0.7107047438621521}, {"X": 0.0359298475086689, "Y": 0.7107042074203491}]}, "Id": "cce565a8-bfaf-48ac-9c58-15f05487da21", "Relationships": [{"Type": "CHILD", "Ids": ["afe3c3de-5f58-4327-8464-1a27048b492a"]}]}, {"BlockType": "LINE", "Confidence": 81.10152435302734, "Text": "PeNNex", "Geometry": {"BoundingBox": {"Width": 0.17136432230472565, "Height": 0.03317827731370926, "Left": 0.1023721843957901, "Top": 0.6689649820327759}, "Polygon": [{"X": 0.10237228125333786, "Y": 0.6689649820327759}, {"X": 0.27373650670051575, "Y": 0.668968677520752}, {"X": 0.27373650670051575, "Y": 0.7021432518959045}, {"X": 0.1023721843957901, "Y": 0.7021396160125732}]}, "Id": "c08e1374-e015-48b6-a124-2581e835e31e", "Relationships": [{"Type": "CHILD", "Ids": ["58b2c751-a4fd-4177-b795-0c0e0954f998"]}]}, {"BlockType": "LINE", "Confidence": 88.56365966796875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.019124368205666542, "Height": 0.025946438312530518, "Left": 0.35739386081695557, "Top": 0.6663710474967957}, "Polygon": [{"X": 0.35739386081695557, "Y": 0.6663710474967957}, {"X": 0.376518189907074, "Y": 0.6663714647293091}, {"X": 0.37651821970939636, "Y": 0.6923174858093262}, {"X": 0.35739389061927795, "Y": 0.6923171281814575}]}, "Id": "35774ef5-2062-4313-8798-f212ae2656ea", "Relationships": [{"Type": "CHILD", "Ids": ["7c7f1b97-383c-4c99-acd3-3ed3f1f01b02"]}]}, {"BlockType": "LINE", "Confidence": 67.73497772216797, "Text": "Pickup", "Geometry": {"BoundingBox": {"Width": 0.17593133449554443, "Height": 0.027651553973555565, "Left": 0.45222336053848267, "Top": 0.6548234224319458}, "Polygon": [{"X": 0.45222336053848267, "Y": 0.6548234224319458}, {"X": 0.6281545162200928, "Y": 0.6548272371292114}, {"X": 0.6281546950340271, "Y": 0.6824749708175659}, {"X": 0.45222344994544983, "Y": 0.6824712157249451}]}, "Id": "49ff26e3-d259-4611-b3dd-16589b46c827", "Relationships": [{"Type": "CHILD", "Ids": ["646ef0a3-a145-448f-8a65-50aabfee0c9b"]}]}, {"BlockType": "LINE", "Confidence": 38.002681732177734, "Text": "$ HYS", "Geometry": {"BoundingBox": {"Width": 0.12047018855810165, "Height": 0.03520325571298599, "Left": 0.6848227977752686, "Top": 0.6698034405708313}, "Polygon": [{"X": 0.6848227977752686, "Y": 0.6698034405708313}, {"X": 0.8052926659584045, "Y": 0.6698060035705566}, {"X": 0.8052930235862732, "Y": 0.7050066590309143}, {"X": 0.6848230957984924, "Y": 0.705004096031189}]}, "Id": "eb008558-f6d0-4487-92d6-09b63ac6c779", "Relationships": [{"Type": "CHILD", "Ids": ["8d013fc9-27da-4ef0-9806-739a6a2f2c1d", "631287a2-dbd6-4db2-9f8d-af38e5aff634"]}]}, {"BlockType": "LINE", "Confidence": 74.37659454345703, "Text": "20sk", "Geometry": {"BoundingBox": {"Width": 0.11407884955406189, "Height": 0.030752021819353104, "Left": 0.14665275812149048, "Top": 0.719403088092804}, "Polygon": [{"X": 0.14665281772613525, "Y": 0.719403088092804}, {"X": 0.26073160767555237, "Y": 0.719405472278595}, {"X": 0.26073160767555237, "Y": 0.7501550912857056}, {"X": 0.14665275812149048, "Y": 0.7501527070999146}]}, "Id": "47956069-5d83-47eb-b193-b21b36e77715", "Relationships": [{"Type": "CHILD", "Ids": ["9f066c44-b38c-40d1-8d2c-cd47b83f37ef"]}]}, {"BlockType": "LINE", "Confidence": 23.12111473083496, "Text": "DSI", "Geometry": {"BoundingBox": {"Width": 0.07473167777061462, "Height": 0.0362405925989151, "Left": 0.7319081425666809, "Top": 0.6990905404090881}, "Polygon": [{"X": 0.7319081425666809, "Y": 0.6990905404090881}, {"X": 0.8066394925117493, "Y": 0.6990921497344971}, {"X": 0.8066398501396179, "Y": 0.735331118106842}, {"X": 0.7319084405899048, "Y": 0.7353295683860779}]}, "Id": "3f016810-e79d-4e7d-90e3-4e70869059b1", "Relationships": [{"Type": "CHILD", "Ids": ["6529ee63-84bf-4319-966a-ea099c0bb105"]}]}, {"BlockType": "LINE", "Confidence": 55.92494583129883, "Text": "10 40", "Geometry": {"BoundingBox": {"Width": 0.07327038049697876, "Height": 0.024272389709949493, "Left": 0.6715286374092102, "Top": 0.74648118019104}, "Polygon": [{"X": 0.6715286374092102, "Y": 0.74648118019104}, {"X": 0.7447987794876099, "Y": 0.7464827299118042}, {"X": 0.744799017906189, "Y": 0.7707535624504089}, {"X": 0.6715287566184998, "Y": 0.7707520723342896}]}, "Id": "42040be5-856d-4b4c-9241-2ac2547b440f", "Relationships": [{"Type": "CHILD", "Ids": ["976ff70b-3d77-4f9d-ac71-256c00bed8a4"]}]}, {"BlockType": "LINE", "Confidence": 99.6484375, "Text": "1145", "Geometry": {"BoundingBox": {"Width": 0.06058167666196823, "Height": 0.02555784583091736, "Left": 0.6812065839767456, "Top": 0.7851274013519287}, "Polygon": [{"X": 0.6812065839767456, "Y": 0.7851274013519287}, {"X": 0.7417880296707153, "Y": 0.785128653049469}, {"X": 0.7417882680892944, "Y": 0.8106852173805237}, {"X": 0.6812067627906799, "Y": 0.8106839656829834}]}, "Id": "d4bc4765-0b71-4160-bfeb-27e369006135", "Relationships": [{"Type": "CHILD", "Ids": ["908da53b-4c20-445e-aacf-e52ac449605f"]}]}, {"BlockType": "LINE", "Confidence": 99.84295654296875, "Text": "YOUNGSTOWN", "Geometry": {"BoundingBox": {"Width": 0.1360902637243271, "Height": 0.014133377000689507, "Left": 0.4435255229473114, "Top": 0.9436905980110168}, "Polygon": [{"X": 0.4435255229473114, "Y": 0.9436905980110168}, {"X": 0.5796157121658325, "Y": 0.943693220615387}, {"X": 0.5796157717704773, "Y": 0.9578239321708679}, {"X": 0.4435255527496338, "Y": 0.9578213095664978}]}, "Id": "69456896-3072-4739-8c7b-2d6129abcee6", "Relationships": [{"Type": "CHILD", "Ids": ["0c729e3c-8cac-422b-bd99-6c393793c2b3"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054820816963911057, "Height": 0.012857222929596901, "Left": 0.242889404296875, "Top": 0.11068827658891678}, "Polygon": [{"X": 0.2428894191980362, "Y": 0.11068827658891678}, {"X": 0.29771021008491516, "Y": 0.1106896847486496}, {"X": 0.29771021008491516, "Y": 0.12354550510644913}, {"X": 0.242889404296875, "Y": 0.1235441043972969}]}, "Id": "06d88c8a-0691-4df5-b888-679e21661490"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "19-Feb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059968773275613785, "Height": 0.013181901536881924, "Left": 0.3202871084213257, "Top": 0.10980231314897537}, "Polygon": [{"X": 0.3202871084213257, "Y": 0.10980231314897537}, {"X": 0.3802558481693268, "Y": 0.10980384796857834}, {"X": 0.38025587797164917, "Y": 0.12298421561717987}, {"X": 0.3202871084213257, "Y": 0.1229826807975769}]}, "Id": "0445db2d-b779-4913-bb37-bf25b161a06e"}, {"BlockType": "WORD", "Confidence": 99.013671875, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04843570664525032, "Height": 0.01270240917801857, "Left": 0.2269246131181717, "Top": 0.14646433293819427}, "Polygon": [{"X": 0.22692462801933289, "Y": 0.14646433293819427}, {"X": 0.2753603160381317, "Y": 0.14646556973457336}, {"X": 0.2753603160381317, "Y": 0.15916675329208374}, {"X": 0.2269246131181717, "Y": 0.15916551649570465}]}, "Id": "35c48415-91e0-4b54-8eae-aad1c7437afa"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020779017359018326, "Height": 0.01265506912022829, "Left": 0.2779847979545593, "Top": 0.14623339474201202}, "Polygon": [{"X": 0.2779847979545593, "Y": 0.14623339474201202}, {"X": 0.29876381158828735, "Y": 0.1462339162826538}, {"X": 0.29876381158828735, "Y": 0.15888845920562744}, {"X": 0.2779847979545593, "Y": 0.15888793766498566}]}, "Id": "dcf59897-671b-44b8-b506-a7ef936f99e9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "92644/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1269402801990509, "Height": 0.015341666527092457, "Left": 0.3201289176940918, "Top": 0.14510560035705566}, "Polygon": [{"X": 0.3201289176940918, "Y": 0.14510560035705566}, {"X": 0.4470691680908203, "Y": 0.14510881900787354}, {"X": 0.4470691978931427, "Y": 0.16044725477695465}, {"X": 0.3201289176940918, "Y": 0.16044405102729797}]}, "Id": "1eee5855-af7e-4544-baca-f2d02a2e3ece"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049054764211177826, "Height": 0.01320650428533554, "Left": 0.46563053131103516, "Top": 0.16198886930942535}, "Polygon": [{"X": 0.46563053131103516, "Y": 0.16198886930942535}, {"X": 0.5146852135658264, "Y": 0.16199010610580444}, {"X": 0.5146852731704712, "Y": 0.1751953810453415}, {"X": 0.46563059091567993, "Y": 0.1751941442489624}]}, "Id": "d0d42ba5-2538-43cc-9f33-855460e49374"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05667145922780037, "Height": 0.016442129388451576, "Left": 0.5181140303611755, "Top": 0.16092947125434875}, "Polygon": [{"X": 0.5181140303611755, "Y": 0.16092947125434875}, {"X": 0.5747854113578796, "Y": 0.16093090176582336}, {"X": 0.5747855305671692, "Y": 0.17737160623073578}, {"X": 0.5181141495704651, "Y": 0.17737017571926117}]}, "Id": "ba666687-d495-41c1-ba5c-6a284e7009ef"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049083780497312546, "Height": 0.012729278765618801, "Left": 0.5790734887123108, "Top": 0.16137921810150146}, "Polygon": [{"X": 0.5790734887123108, "Y": 0.16137921810150146}, {"X": 0.6281571984291077, "Y": 0.16138045489788055}, {"X": 0.6281572580337524, "Y": 0.17410849034786224}, {"X": 0.5790735483169556, "Y": 0.17410726845264435}]}, "Id": "2c0af061-6329-498d-b7b5-8a9e8bcfb6fc"}, {"BlockType": "WORD", "Confidence": 99.02104949951172, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10507532209157944, "Height": 0.03219890594482422, "Left": 0.6570407748222351, "Top": 0.14309754967689514}, "Polygon": [{"X": 0.6570407748222351, "Y": 0.14309754967689514}, {"X": 0.7621158361434937, "Y": 0.14310021698474884}, {"X": 0.7621160745620728, "Y": 0.17529645562171936}, {"X": 0.6570409536361694, "Y": 0.17529381811618805}]}, "Id": "f4f63b21-4f54-4cf1-8974-dba12f8f3d6f"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07543918490409851, "Height": 0.012693328782916069, "Left": 0.21907995641231537, "Top": 0.18201033771038055}, "Polygon": [{"X": 0.21907997131347656, "Y": 0.18201033771038055}, {"X": 0.2945191264152527, "Y": 0.18201223015785217}, {"X": 0.2945191562175751, "Y": 0.19470366835594177}, {"X": 0.21907995641231537, "Y": 0.19470177590847015}]}, "Id": "41c3edf4-73fe-46b7-8b82-aa9ec2209511"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060760632157325745, "Height": 0.01273215003311634, "Left": 0.32177314162254333, "Top": 0.18155537545681}, "Polygon": [{"X": 0.32177314162254333, "Y": 0.18155537545681}, {"X": 0.3825337588787079, "Y": 0.18155691027641296}, {"X": 0.3825337886810303, "Y": 0.1942875236272812}, {"X": 0.3217731714248657, "Y": 0.19428600370883942}]}, "Id": "28d4440b-cc89-4a2d-89cc-5e520a70270d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04893266782164574, "Height": 0.013104367069900036, "Left": 0.46653100848197937, "Top": 0.19756092131137848}, "Polygon": [{"X": 0.46653100848197937, "Y": 0.19756092131137848}, {"X": 0.5154635906219482, "Y": 0.19756214320659637}, {"X": 0.515463650226593, "Y": 0.2106652855873108}, {"X": 0.46653103828430176, "Y": 0.2106640785932541}]}, "Id": "2804b0ba-168d-486b-9dc7-bc6cc11a3732"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09423540532588959, "Height": 0.01560566108673811, "Left": 0.5189868211746216, "Top": 0.1972423791885376}, "Polygon": [{"X": 0.5189868211746216, "Y": 0.1972423791885376}, {"X": 0.6132221221923828, "Y": 0.19724473357200623}, {"X": 0.6132222414016724, "Y": 0.21284803748130798}, {"X": 0.5189868807792664, "Y": 0.21284569799900055}]}, "Id": "dbb768ae-fa15-428c-81a5-26d96dc65fda"}, {"BlockType": "WORD", "Confidence": 98.43291473388672, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10562808811664581, "Height": 0.027190543711185455, "Left": 0.658603310585022, "Top": 0.17971865832805634}, "Polygon": [{"X": 0.658603310585022, "Y": 0.17971865832805634}, {"X": 0.7642312049865723, "Y": 0.17972131073474884}, {"X": 0.7642314434051514, "Y": 0.2069091945886612}, {"X": 0.6586034893989563, "Y": 0.20690657198429108}]}, "Id": "22b98fc8-a5d5-4914-b0fe-76232d808228"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1091952845454216, "Height": 0.01282552070915699, "Left": 0.13103356957435608, "Top": 0.21766513586044312}, "Polygon": [{"X": 0.13103359937667847, "Y": 0.21766513586044312}, {"X": 0.24022884666919708, "Y": 0.2176678478717804}, {"X": 0.24022884666919708, "Y": 0.23049065470695496}, {"X": 0.13103356957435608, "Y": 0.23048794269561768}]}, "Id": "dd4e2bbd-7042-484b-8ac8-3cf724801ec0"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049798086285591125, "Height": 0.012448420748114586, "Left": 0.24547803401947021, "Top": 0.21741876006126404}, "Polygon": [{"X": 0.2454780489206314, "Y": 0.21741876006126404}, {"X": 0.29527613520622253, "Y": 0.21741999685764313}, {"X": 0.29527613520622253, "Y": 0.22986717522144318}, {"X": 0.24547803401947021, "Y": 0.22986595332622528}]}, "Id": "589ceab8-c49d-4c08-9753-ca77309a8334"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0369638092815876, "Height": 0.012884262949228287, "Left": 0.32346609234809875, "Top": 0.21678867936134338}, "Polygon": [{"X": 0.32346609234809875, "Y": 0.21678867936134338}, {"X": 0.36042988300323486, "Y": 0.2167896032333374}, {"X": 0.36042991280555725, "Y": 0.22967293858528137}, {"X": 0.32346612215042114, "Y": 0.22967202961444855}]}, "Id": "7cf2e591-8426-4a09-8840-15c723ad5bd4"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04785889387130737, "Height": 0.010938746854662895, "Left": 0.3347736895084381, "Top": 0.2478572279214859}, "Polygon": [{"X": 0.3347736895084381, "Y": 0.2478572279214859}, {"X": 0.3826325535774231, "Y": 0.24785840511322021}, {"X": 0.3826325535774231, "Y": 0.25879597663879395}, {"X": 0.3347736895084381, "Y": 0.2587948143482208}]}, "Id": "b124732a-bfb2-4ef3-b2e9-374a3229367c"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04563652351498604, "Height": 0.0120528070256114, "Left": 0.8257697224617004, "Top": 0.25343403220176697}, "Polygon": [{"X": 0.8257697224617004, "Y": 0.25343403220176697}, {"X": 0.8714061379432678, "Y": 0.2534351646900177}, {"X": 0.8714062571525574, "Y": 0.26548683643341064}, {"X": 0.82576984167099, "Y": 0.2654857337474823}]}, "Id": "22d3074b-a2ae-4f91-b38e-ee5aefc0dc14"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046147432178258896, "Height": 0.011875751428306103, "Left": 0.8749778866767883, "Top": 0.252854585647583}, "Polygon": [{"X": 0.8749778866767883, "Y": 0.252854585647583}, {"X": 0.9211251735687256, "Y": 0.25285571813583374}, {"X": 0.9211252927780151, "Y": 0.2647303342819214}, {"X": 0.8749780058860779, "Y": 0.26472920179367065}]}, "Id": "cd673ac1-305b-4249-afce-74240e7a1bc6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10067279636859894, "Height": 0.012615521438419819, "Left": 0.16310399770736694, "Top": 0.2676003575325012}, "Polygon": [{"X": 0.16310402750968933, "Y": 0.2676003575325012}, {"X": 0.2637767791748047, "Y": 0.267602801322937}, {"X": 0.2637767791748047, "Y": 0.280215859413147}, {"X": 0.16310399770736694, "Y": 0.2802134156227112}]}, "Id": "5ec588a7-ff1c-456b-9485-a0b658fb983d"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023942895233631134, "Height": 0.012423768639564514, "Left": 0.3471435308456421, "Top": 0.2655896246433258}, "Polygon": [{"X": 0.3471435308456421, "Y": 0.2655896246433258}, {"X": 0.37108638882637024, "Y": 0.26559019088745117}, {"X": 0.3710864186286926, "Y": 0.2780133783817291}, {"X": 0.3471435308456421, "Y": 0.27801281213760376}]}, "Id": "c7d559cc-ef73-414d-9fc0-4ddb016cdbfe"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03438418731093407, "Height": 0.012267366982996464, "Left": 0.4126172661781311, "Top": 0.2651919424533844}, "Polygon": [{"X": 0.4126172661781311, "Y": 0.2651919424533844}, {"X": 0.4470013976097107, "Y": 0.26519277691841125}, {"X": 0.44700145721435547, "Y": 0.2774592936038971}, {"X": 0.4126172959804535, "Y": 0.27745845913887024}]}, "Id": "0e593f8b-c431-437d-8179-d591d7a49a9c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056625280529260635, "Height": 0.012728850357234478, "Left": 0.5687410831451416, "Top": 0.2638840079307556}, "Polygon": [{"X": 0.5687410831451416, "Y": 0.2638840079307556}, {"X": 0.6253662705421448, "Y": 0.26388540863990784}, {"X": 0.6253663301467896, "Y": 0.27661287784576416}, {"X": 0.5687411427497864, "Y": 0.27661147713661194}]}, "Id": "7a5cdb67-4ff2-4f77-82c8-4107c6d638d5"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06312157213687897, "Height": 0.012895585037767887, "Left": 0.6303342580795288, "Top": 0.26302003860473633}, "Polygon": [{"X": 0.6303342580795288, "Y": 0.26302003860473633}, {"X": 0.6934557557106018, "Y": 0.2630215883255005}, {"X": 0.6934558153152466, "Y": 0.27591562271118164}, {"X": 0.6303343176841736, "Y": 0.27591410279273987}]}, "Id": "59ea0f62-cfd2-4272-8f8a-c5cfa783ebda"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01235488522797823, "Height": 0.012175197713077068, "Left": 0.6992663145065308, "Top": 0.26244547963142395}, "Polygon": [{"X": 0.6992663145065308, "Y": 0.26244547963142395}, {"X": 0.711621105670929, "Y": 0.2624457776546478}, {"X": 0.7116211652755737, "Y": 0.2746206521987915}, {"X": 0.6992663741111755, "Y": 0.2746203541755676}]}, "Id": "eb8baba7-e80f-40af-be7e-1272abd66b9f"}, {"BlockType": "WORD", "Confidence": 28.024354934692383, "Text": "755h", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10852892696857452, "Height": 0.030838720500469208, "Left": 0.34646841883659363, "Top": 0.29517698287963867}, "Polygon": [{"X": 0.34646841883659363, "Y": 0.29517698287963867}, {"X": 0.4549972414970398, "Y": 0.2951796352863312}, {"X": 0.45499736070632935, "Y": 0.3260157108306885}, {"X": 0.346468448638916, "Y": 0.32601311802864075}]}, "Id": "e9070a0d-0a84-4605-b975-1ca3d8e37995"}, {"BlockType": "WORD", "Confidence": 93.11722564697266, "Text": "tempty", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13623382151126862, "Height": 0.028560955077409744, "Left": 0.46768632531166077, "Top": 0.29425355792045593}, "Polygon": [{"X": 0.46768632531166077, "Y": 0.29425355792045593}, {"X": 0.6039199829101562, "Y": 0.29425686597824097}, {"X": 0.6039201617240906, "Y": 0.3228145241737366}, {"X": 0.46768641471862793, "Y": 0.3228112459182739}]}, "Id": "2806e239-e001-4527-9725-442ddb05a3f8"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1230", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08113858103752136, "Height": 0.01772553101181984, "Left": 0.8646883368492126, "Top": 0.2991727888584137}, "Polygon": [{"X": 0.8646883368492126, "Y": 0.2991727888584137}, {"X": 0.9458267092704773, "Y": 0.2991747558116913}, {"X": 0.9458268880844116, "Y": 0.31689831614494324}, {"X": 0.864688515663147, "Y": 0.31689637899398804}]}, "Id": "fbe3a23c-74d6-4b7c-b8bb-cbe9af6f4fb5"}, {"BlockType": "WORD", "Confidence": 99.02204132080078, "Text": "3)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.045506466180086136, "Height": 0.05146821215748787, "Left": 0.0483834333717823, "Top": 0.31561723351478577}, "Polygon": [{"X": 0.04838363081216812, "Y": 0.31561723351478577}, {"X": 0.09388989955186844, "Y": 0.3156183063983917}, {"X": 0.0938897430896759, "Y": 0.36708542704582214}, {"X": 0.0483834333717823, "Y": 0.3670843541622162}]}, "Id": "6d861e26-3c8f-46bd-a014-7ea01bd2a7d2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "General", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06725455820560455, "Height": 0.01306302659213543, "Left": 0.10845371335744858, "Top": 0.33443596959114075}, "Polygon": [{"X": 0.10845375061035156, "Y": 0.33443596959114075}, {"X": 0.17570827901363373, "Y": 0.3344375789165497}, {"X": 0.17570824921131134, "Y": 0.34749898314476013}, {"X": 0.10845371335744858, "Y": 0.3474973738193512}]}, "Id": "e1356693-b8ed-431c-a79c-6c4f53502e15"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Extrusion", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07922009378671646, "Height": 0.012516379356384277, "Left": 0.18221800029277802, "Top": 0.3345755636692047}, "Polygon": [{"X": 0.1822180151939392, "Y": 0.3345755636692047}, {"X": 0.2614381015300751, "Y": 0.3345774710178375}, {"X": 0.2614380717277527, "Y": 0.347091943025589}, {"X": 0.18221800029277802, "Y": 0.34709006547927856}]}, "Id": "91da31a8-70da-4701-995a-db4b46a333a8"}, {"BlockType": "WORD", "Confidence": 92.66522216796875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01097073033452034, "Height": 0.009613018482923508, "Left": 0.35543903708457947, "Top": 0.3360922932624817}, "Polygon": [{"X": 0.35543903708457947, "Y": 0.3360922932624817}, {"X": 0.3664097487926483, "Y": 0.3360925316810608}, {"X": 0.3664097785949707, "Y": 0.3457053005695343}, {"X": 0.35543903708457947, "Y": 0.3457050323486328}]}, "Id": "0a80a3ee-225f-4f50-a2c6-04df1c2a47cd"}, {"BlockType": "WORD", "Confidence": 92.70427703857422, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013096112757921219, "Height": 0.01223896536976099, "Left": 0.42450857162475586, "Top": 0.3327445983886719}, "Polygon": [{"X": 0.42450857162475586, "Y": 0.3327445983886719}, {"X": 0.4376046359539032, "Y": 0.33274492621421814}, {"X": 0.4376046657562256, "Y": 0.3449835479259491}, {"X": 0.42450860142707825, "Y": 0.3449832499027252}]}, "Id": "a2e17e7e-a021-4b79-9674-b82e2de81590"}, {"BlockType": "WORD", "Confidence": 99.20679473876953, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09623313695192337, "Height": 0.011271430179476738, "Left": 0.46625810861587524, "Top": 0.332563579082489}, "Polygon": [{"X": 0.46625810861587524, "Y": 0.332563579082489}, {"X": 0.5624911785125732, "Y": 0.33256590366363525}, {"X": 0.562491238117218, "Y": 0.3438350260257721}, {"X": 0.46625816822052, "Y": 0.34383273124694824}]}, "Id": "b971a092-1016-4d56-b2d9-44e364137efa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1305", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06859823316335678, "Height": 0.025042375549674034, "Left": 0.8614248633384705, "Top": 0.3425601124763489}, "Polygon": [{"X": 0.8614248633384705, "Y": 0.3425601124763489}, {"X": 0.9300227761268616, "Y": 0.3425617516040802}, {"X": 0.9300230741500854, "Y": 0.36760246753692627}, {"X": 0.8614251017570496, "Y": 0.36760085821151733}]}, "Id": "a0b9ec94-3569-4f1e-9e30-98a5196b9513"}, {"BlockType": "WORD", "Confidence": 38.58457946777344, "Text": "x", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14296767115592957, "Height": 0.05359954759478569, "Left": 0.15001697838306427, "Top": 0.3605818748474121}, "Polygon": [{"X": 0.15001708269119263, "Y": 0.3605818748474121}, {"X": 0.29298463463783264, "Y": 0.3605852723121643}, {"X": 0.29298463463783264, "Y": 0.4141814112663269}, {"X": 0.15001697838306427, "Y": 0.4141780734062195}]}, "Id": "f1249ef7-5d82-4b68-98d1-768c28689bf7"}, {"BlockType": "WORD", "Confidence": 36.12962341308594, "Text": "tsk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08811807632446289, "Height": 0.048168737441301346, "Left": 0.3784377872943878, "Top": 0.41067251563072205}, "Polygon": [{"X": 0.3784377872943878, "Y": 0.41067251563072205}, {"X": 0.46655571460723877, "Y": 0.4106745719909668}, {"X": 0.4665558636188507, "Y": 0.4588412642478943}, {"X": 0.378437876701355, "Y": 0.4588392376899719}]}, "Id": "08073102-e5e1-400b-a3b7-21d476407f4f"}, {"BlockType": "WORD", "Confidence": 98.17681884765625, "Text": "2)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.053603459149599075, "Height": 0.05119029060006142, "Left": 0.04938212409615517, "Top": 0.4360446333885193}, "Polygon": [{"X": 0.049382321536540985, "Y": 0.4360446333885193}, {"X": 0.10298558324575424, "Y": 0.43604588508605957}, {"X": 0.1029854342341423, "Y": 0.4872349500656128}, {"X": 0.04938212409615517, "Y": 0.4872336983680725}]}, "Id": "8e76194c-f768-49c2-9091-85e4e978d17f"}, {"BlockType": "WORD", "Confidence": 99.56791687011719, "Text": "Vari-Wall", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07831351459026337, "Height": 0.012987930327653885, "Left": 0.1102224662899971, "Top": 0.4515991806983948}, "Polygon": [{"X": 0.11022250354290009, "Y": 0.4515991806983948}, {"X": 0.18853597342967987, "Y": 0.4516009986400604}, {"X": 0.18853595852851868, "Y": 0.46458712220191956}, {"X": 0.1102224662899971, "Y": 0.4645853042602539}]}, "Id": "a37c6e49-6e24-41b6-a999-74770971e426"}, {"BlockType": "WORD", "Confidence": 65.4942626953125, "Text": "5 SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08004599064588547, "Height": 0.025789255276322365, "Left": 0.14935287833213806, "Top": 0.4708024263381958}, "Polygon": [{"X": 0.14935292303562164, "Y": 0.4708024263381958}, {"X": 0.22939886152744293, "Y": 0.47080424427986145}, {"X": 0.22939884662628174, "Y": 0.4965916872024536}, {"X": 0.14935287833213806, "Y": 0.4965898394584656}]}, "Id": "120dc646-a0c0-49c8-a7c0-2819c76c6368"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045103128999471664, "Height": 0.012653177604079247, "Left": 0.1938648372888565, "Top": 0.4513533413410187}, "Polygon": [{"X": 0.1938648670911789, "Y": 0.4513533413410187}, {"X": 0.23896797001361847, "Y": 0.45135438442230225}, {"X": 0.23896797001361847, "Y": 0.4640065133571625}, {"X": 0.1938648372888565, "Y": 0.4640054702758789}]}, "Id": "b2a64854-65a0-41e7-8994-cc6254c7a664"}, {"BlockType": "WORD", "Confidence": 87.46551513671875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01127795409411192, "Height": 0.009594044648110867, "Left": 0.35788819193840027, "Top": 0.4525049924850464}, "Polygon": [{"X": 0.35788819193840027, "Y": 0.4525049924850464}, {"X": 0.3691661059856415, "Y": 0.4525052607059479}, {"X": 0.36916613578796387, "Y": 0.4620990455150604}, {"X": 0.35788819193840027, "Y": 0.46209877729415894}]}, "Id": "9f9fe495-70cc-46a8-adf2-01591afe80b2"}, {"BlockType": "WORD", "Confidence": 94.29707336425781, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010941325686872005, "Height": 0.009150148369371891, "Left": 0.42788010835647583, "Top": 0.45209845900535583}, "Polygon": [{"X": 0.42788010835647583, "Y": 0.45209845900535583}, {"X": 0.438821405172348, "Y": 0.4520987272262573}, {"X": 0.4388214349746704, "Y": 0.46124860644340515}, {"X": 0.4278801381587982, "Y": 0.46124836802482605}]}, "Id": "e1465bad-5eb6-4934-bfcc-824593bd9b19"}, {"BlockType": "WORD", "Confidence": 69.29528045654297, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020275412127375603, "Height": 0.010189954191446304, "Left": 0.4683604836463928, "Top": 0.44983166456222534}, "Polygon": [{"X": 0.4683604836463928, "Y": 0.44983166456222534}, {"X": 0.4886358380317688, "Y": 0.44983214139938354}, {"X": 0.4886358678340912, "Y": 0.46002161502838135}, {"X": 0.4683605134487152, "Y": 0.46002116799354553}]}, "Id": "51144936-4989-48e8-9e31-11e5a5103bad"}, {"BlockType": "WORD", "Confidence": 98.4327163696289, "Text": "02115817", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06870908290147781, "Height": 0.01084152702242136, "Left": 0.4946866035461426, "Top": 0.4488900899887085}, "Polygon": [{"X": 0.4946866035461426, "Y": 0.4488900899887085}, {"X": 0.563395619392395, "Y": 0.44889166951179504}, {"X": 0.5633956789970398, "Y": 0.4597316086292267}, {"X": 0.49468663334846497, "Y": 0.45973002910614014}]}, "Id": "1e0704c0-33e9-465b-92d3-fae10fcb97b0"}, {"BlockType": "WORD", "Confidence": 95.2782211303711, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09501313418149948, "Height": 0.010701981373131275, "Left": 0.5690041780471802, "Top": 0.4482070803642273}, "Polygon": [{"X": 0.5690041780471802, "Y": 0.4482070803642273}, {"X": 0.6640172600746155, "Y": 0.448209285736084}, {"X": 0.6640173196792603, "Y": 0.4589090645313263}, {"X": 0.569004237651825, "Y": 0.4589068591594696}]}, "Id": "819142a8-2b6b-4037-9309-c74967b1862b"}, {"BlockType": "WORD", "Confidence": 49.876434326171875, "Text": "1000", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08367662876844406, "Height": 0.020543985068798065, "Left": 0.8451106548309326, "Top": 0.4423898756504059}, "Polygon": [{"X": 0.8451106548309326, "Y": 0.4423898756504059}, {"X": 0.9287870526313782, "Y": 0.4423918128013611}, {"X": 0.9287872314453125, "Y": 0.46293386816978455}, {"X": 0.8451108336448669, "Y": 0.46293193101882935}]}, "Id": "e10bcdc1-e468-4466-87d8-ddc6bc3314ad"}, {"BlockType": "WORD", "Confidence": 70.61602783203125, "Text": "follow", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1095709502696991, "Height": 0.03753760829567909, "Left": 0.5072449445724487, "Top": 0.481648713350296}, "Polygon": [{"X": 0.5072449445724487, "Y": 0.481648713350296}, {"X": 0.6168156862258911, "Y": 0.4816512167453766}, {"X": 0.6168159246444702, "Y": 0.5191863179206848}, {"X": 0.5072451233863831, "Y": 0.519183874130249}]}, "Id": "0537b688-067f-48ff-ad5a-cdb0a03f530f"}, {"BlockType": "WORD", "Confidence": 55.04524230957031, "Text": "to", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.040477603673934937, "Height": 0.019059455022215843, "Left": 0.7659012079238892, "Top": 0.46283116936683655}, "Polygon": [{"X": 0.7659012079238892, "Y": 0.46283116936683655}, {"X": 0.8063786625862122, "Y": 0.46283209323883057}, {"X": 0.8063788414001465, "Y": 0.48189061880111694}, {"X": 0.7659013867378235, "Y": 0.4818896949291229}]}, "Id": "d4136cc6-bae5-414c-a8f9-2bee32479980"}, {"BlockType": "WORD", "Confidence": 98.583984375, "Text": "0936", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.074602872133255, "Height": 0.017255874350667, "Left": 0.8730655312538147, "Top": 0.46400681138038635}, "Polygon": [{"X": 0.8730655312538147, "Y": 0.46400681138038635}, {"X": 0.947668194770813, "Y": 0.46400853991508484}, {"X": 0.9476684331893921, "Y": 0.4812626838684082}, {"X": 0.873065710067749, "Y": 0.4812609851360321}]}, "Id": "78c8f473-c11f-4404-8289-46f45465b8f9"}, {"BlockType": "WORD", "Confidence": 73.31373596191406, "Text": "1015", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09938754886388779, "Height": 0.024359211325645447, "Left": 0.8383670449256897, "Top": 0.48931023478507996}, "Polygon": [{"X": 0.8383670449256897, "Y": 0.48931023478507996}, {"X": 0.9377543330192566, "Y": 0.4893124997615814}, {"X": 0.9377545714378357, "Y": 0.5136694312095642}, {"X": 0.8383672833442688, "Y": 0.5136672258377075}]}, "Id": "707d5716-b115-4503-8f70-4a737fd035f9"}, {"BlockType": "WORD", "Confidence": 93.0676040649414, "Text": "1)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.02804512158036232, "Height": 0.04745045676827431, "Left": 0.06031886115670204, "Top": 0.5581309795379639}, "Polygon": [{"X": 0.06031903252005577, "Y": 0.5581309795379639}, {"X": 0.08836398273706436, "Y": 0.5581316351890564}, {"X": 0.08836383372545242, "Y": 0.6055814623832703}, {"X": 0.06031886115670204, "Y": 0.6055808067321777}]}, "Id": "91051dfa-667d-4144-bc96-bd54729bf2cb"}, {"BlockType": "WORD", "Confidence": 67.90657043457031, "Text": "In", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.022876592352986336, "Height": 0.011997882276773453, "Left": 0.12440148741006851, "Top": 0.5601868629455566}, "Polygon": [{"X": 0.1244015172123909, "Y": 0.5601868629455566}, {"X": 0.1472780853509903, "Y": 0.5601873397827148}, {"X": 0.1472780555486679, "Y": 0.5721847414970398}, {"X": 0.12440148741006851, "Y": 0.5721842050552368}]}, "Id": "bdcc176b-ec3f-4d5f-a56c-b993a293fba7"}, {"BlockType": "WORD", "Confidence": 36.677093505859375, "Text": "Inlandplt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09715534001588821, "Height": 0.015106773003935814, "Left": 0.12456759065389633, "Top": 0.5579501390457153}, "Polygon": [{"X": 0.12456762790679932, "Y": 0.5579501390457153}, {"X": 0.22172293066978455, "Y": 0.557952344417572}, {"X": 0.22172291576862335, "Y": 0.5730569362640381}, {"X": 0.12456759065389633, "Y": 0.5730547904968262}]}, "Id": "aafd2775-24ff-4246-b85b-3a18bcedbe70"}, {"BlockType": "WORD", "Confidence": 85.2235107421875, "Text": "plt", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03245953097939491, "Height": 0.013446365483105183, "Left": 0.18900640308856964, "Top": 0.5581424236297607}, "Polygon": [{"X": 0.18900641798973083, "Y": 0.5581424236297607}, {"X": 0.22146593034267426, "Y": 0.558143138885498}, {"X": 0.22146591544151306, "Y": 0.5715887546539307}, {"X": 0.18900640308856964, "Y": 0.5715880393981934}]}, "Id": "960e5c17-4822-4c51-a346-09e9bdc48c9a"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056485556066036224, "Height": 0.011727984063327312, "Left": 0.11173728853464127, "Top": 0.5718370676040649}, "Polygon": [{"X": 0.11173731833696365, "Y": 0.5718370676040649}, {"X": 0.1682228446006775, "Y": 0.5718383193016052}, {"X": 0.1682228296995163, "Y": 0.5835649967193604}, {"X": 0.11173728853464127, "Y": 0.5835638046264648}]}, "Id": "67582f41-4555-4ecb-89de-31d20d6a2abf"}, {"BlockType": "WORD", "Confidence": 99.45890045166016, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07091508060693741, "Height": 0.012475800700485706, "Left": 0.17448420822620392, "Top": 0.5699876546859741}, "Polygon": [{"X": 0.1744842231273651, "Y": 0.5699876546859741}, {"X": 0.24539928138256073, "Y": 0.5699892640113831}, {"X": 0.24539928138256073, "Y": 0.5824634432792664}, {"X": 0.17448420822620392, "Y": 0.5824618935585022}]}, "Id": "9ac2bd45-87a9-4e78-b09a-c0137fdea838"}, {"BlockType": "WORD", "Confidence": 91.33271026611328, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007058866322040558, "Height": 0.0027441878337413073, "Left": 0.2511550188064575, "Top": 0.5749856233596802}, "Polygon": [{"X": 0.2511550188064575, "Y": 0.5749856233596802}, {"X": 0.25821390748023987, "Y": 0.5749857425689697}, {"X": 0.25821390748023987, "Y": 0.5777297616004944}, {"X": 0.2511550188064575, "Y": 0.5777296423912048}]}, "Id": "c517560e-83df-4a2b-91c3-76c8ca67d399"}, {"BlockType": "WORD", "Confidence": 88.34123229980469, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011289657093584538, "Height": 0.00977187417447567, "Left": 0.3594965636730194, "Top": 0.580009400844574}, "Polygon": [{"X": 0.3594965636730194, "Y": 0.580009400844574}, {"X": 0.370786190032959, "Y": 0.5800096988677979}, {"X": 0.37078621983528137, "Y": 0.5897812843322754}, {"X": 0.3594965636730194, "Y": 0.5897810459136963}]}, "Id": "7a488626-043c-4b26-8306-1e48242e07eb"}, {"BlockType": "WORD", "Confidence": 89.63150024414062, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011617789044976234, "Height": 0.009949511848390102, "Left": 0.42950552701950073, "Top": 0.5791550278663635}, "Polygon": [{"X": 0.42950552701950073, "Y": 0.5791550278663635}, {"X": 0.4411233067512512, "Y": 0.5791552662849426}, {"X": 0.4411233067512512, "Y": 0.5891045331954956}, {"X": 0.4295055568218231, "Y": 0.5891042947769165}]}, "Id": "d507ec1d-2b61-4c33-8ed2-925c93d080a3"}, {"BlockType": "WORD", "Confidence": 72.27778625488281, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02025182917714119, "Height": 0.010049100033938885, "Left": 0.4712843596935272, "Top": 0.5769669413566589}, "Polygon": [{"X": 0.4712843596935272, "Y": 0.5769669413566589}, {"X": 0.49153614044189453, "Y": 0.5769674181938171}, {"X": 0.4915361702442169, "Y": 0.5870160460472107}, {"X": 0.4712843894958496, "Y": 0.5870155692100525}]}, "Id": "18bea0dd-fa3e-462f-bad7-eca1f14bffd3"}, {"BlockType": "WORD", "Confidence": 98.29021453857422, "Text": "02115450", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07027614861726761, "Height": 0.010867350734770298, "Left": 0.4967392086982727, "Top": 0.5759356617927551}, "Polygon": [{"X": 0.4967392086982727, "Y": 0.5759356617927551}, {"X": 0.5670152902603149, "Y": 0.5759372711181641}, {"X": 0.5670153498649597, "Y": 0.5868030190467834}, {"X": 0.4967392385005951, "Y": 0.5868014693260193}]}, "Id": "dd2db0ce-c473-41c1-bf63-adb7d97a9be1"}, {"BlockType": "WORD", "Confidence": 97.1534194946289, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09607934206724167, "Height": 0.010989418253302574, "Left": 0.5721423029899597, "Top": 0.5749853253364563}, "Polygon": [{"X": 0.5721423029899597, "Y": 0.5749853253364563}, {"X": 0.6682215332984924, "Y": 0.5749874711036682}, {"X": 0.668221652507782, "Y": 0.5859747529029846}, {"X": 0.5721423625946045, "Y": 0.5859726071357727}]}, "Id": "f0a5f569-1006-4328-b633-aa3111dee178"}, {"BlockType": "WORD", "Confidence": 66.57047271728516, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020847702398896217, "Height": 0.010122407227754593, "Left": 0.6726579070091248, "Top": 0.5748129487037659}, "Polygon": [{"X": 0.6726579070091248, "Y": 0.5748129487037659}, {"X": 0.6935055255889893, "Y": 0.5748134255409241}, {"X": 0.6935056447982788, "Y": 0.5849353671073914}, {"X": 0.6726579666137695, "Y": 0.5849348902702332}]}, "Id": "3e579f13-bde5-464b-bef0-0a7534be0092"}, {"BlockType": "WORD", "Confidence": 88.92597961425781, "Text": "0211626", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06724178045988083, "Height": 0.010987631045281887, "Left": 0.6970810890197754, "Top": 0.5739304423332214}, "Polygon": [{"X": 0.6970810890197754, "Y": 0.5739304423332214}, {"X": 0.764322817325592, "Y": 0.5739319324493408}, {"X": 0.7643228769302368, "Y": 0.5849180221557617}, {"X": 0.6970812082290649, "Y": 0.5849165320396423}]}, "Id": "62304cf2-e654-448c-9924-957099841594"}, {"BlockType": "WORD", "Confidence": 99.69486999511719, "Text": "Metamora", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0857909545302391, "Height": 0.012538857758045197, "Left": 0.1119719073176384, "Top": 0.5901594161987305}, "Polygon": [{"X": 0.11197194457054138, "Y": 0.5901594161987305}, {"X": 0.1977628618478775, "Y": 0.5901613235473633}, {"X": 0.1977628469467163, "Y": 0.6026982665061951}, {"X": 0.1119719073176384, "Y": 0.602696418762207}]}, "Id": "32cde4ea-c93f-4e25-bbc9-23f9bbf2cc2b"}, {"BlockType": "WORD", "Confidence": 87.93367004394531, "Text": "50-2116053", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.21994942426681519, "Height": 0.023283783346414566, "Left": 0.47301778197288513, "Top": 0.5878908038139343}, "Polygon": [{"X": 0.47301778197288513, "Y": 0.5878908038139343}, {"X": 0.6929670572280884, "Y": 0.5878956913948059}, {"X": 0.6929671764373779, "Y": 0.6111745834350586}, {"X": 0.4730178415775299, "Y": 0.6111698150634766}]}, "Id": "bd2520fa-376c-4d7f-bc24-37a48881888b"}, {"BlockType": "WORD", "Confidence": 97.95121002197266, "Text": "0540", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07629060745239258, "Height": 0.01622743532061577, "Left": 0.8599780201911926, "Top": 0.5859130620956421}, "Polygon": [{"X": 0.8599780201911926, "Y": 0.5859130620956421}, {"X": 0.9362684488296509, "Y": 0.5859147310256958}, {"X": 0.9362686276435852, "Y": 0.602140486240387}, {"X": 0.859978199005127, "Y": 0.6021388173103333}]}, "Id": "9605bcd7-685d-4cf4-8443-074bd7321442"}, {"BlockType": "WORD", "Confidence": 52.90119171142578, "Text": "1 sm box", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.117005355656147, "Height": 0.029255716130137444, "Left": 0.125104159116745, "Top": 0.6088439226150513}, "Polygon": [{"X": 0.12510423362255096, "Y": 0.6088439226150513}, {"X": 0.2421095222234726, "Y": 0.6088464856147766}, {"X": 0.2421095073223114, "Y": 0.6380996704101562}, {"X": 0.125104159116745, "Y": 0.6380971074104309}]}, "Id": "01d9d915-6eac-4f96-b873-9325fd0265d1"}, {"BlockType": "WORD", "Confidence": 99.521484375, "Text": "0600", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08056437224149704, "Height": 0.017424000427126884, "Left": 0.8577093482017517, "Top": 0.6094968914985657}, "Polygon": [{"X": 0.8577093482017517, "Y": 0.6094968914985657}, {"X": 0.938273549079895, "Y": 0.6094986200332642}, {"X": 0.9382737278938293, "Y": 0.6269208788871765}, {"X": 0.857709527015686, "Y": 0.6269190907478333}]}, "Id": "2fcb2683-ae55-44d8-86b9-5b70f417b64a"}, {"BlockType": "WORD", "Confidence": 64.70683288574219, "Text": "Cempties", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1722884476184845, "Height": 0.03803272545337677, "Left": 0.39649271965026855, "Top": 0.6194044947624207}, "Polygon": [{"X": 0.39649271965026855, "Y": 0.6194044947624207}, {"X": 0.5687810182571411, "Y": 0.6194082498550415}, {"X": 0.5687811970710754, "Y": 0.6574372053146362}, {"X": 0.3964928090572357, "Y": 0.6574335098266602}]}, "Id": "b8f448fb-ea25-49d7-a6c1-c7970933a388"}, {"BlockType": "WORD", "Confidence": 71.29524230957031, "Text": "aspots", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13270403444766998, "Height": 0.03328462317585945, "Left": 0.6079355478286743, "Top": 0.6148478984832764}, "Polygon": [{"X": 0.6079355478286743, "Y": 0.6148478984832764}, {"X": 0.7406392693519592, "Y": 0.6148508191108704}, {"X": 0.7406395673751831, "Y": 0.6481325030326843}, {"X": 0.6079357266426086, "Y": 0.6481296420097351}]}, "Id": "fb41e9f5-83c8-49da-a497-1d754e5429d4"}, {"BlockType": "WORD", "Confidence": 98.31871795654297, "Text": "4", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.026098841801285744, "Height": 0.04726731777191162, "Left": 0.0359298475086689, "Top": 0.6634374260902405}, "Polygon": [{"X": 0.03593004122376442, "Y": 0.6634374260902405}, {"X": 0.06202869117259979, "Y": 0.6634379625320435}, {"X": 0.06202851980924606, "Y": 0.7107047438621521}, {"X": 0.0359298475086689, "Y": 0.7107042074203491}]}, "Id": "afe3c3de-5f58-4327-8464-1a27048b492a"}, {"BlockType": "WORD", "Confidence": 81.10152435302734, "Text": "PeNNex", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17136432230472565, "Height": 0.03317827731370926, "Left": 0.1023721843957901, "Top": 0.6689649820327759}, "Polygon": [{"X": 0.10237228125333786, "Y": 0.6689649820327759}, {"X": 0.27373650670051575, "Y": 0.668968677520752}, {"X": 0.27373650670051575, "Y": 0.7021432518959045}, {"X": 0.1023721843957901, "Y": 0.7021396160125732}]}, "Id": "58b2c751-a4fd-4177-b795-0c0e0954f998"}, {"BlockType": "WORD", "Confidence": 88.56365966796875, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.019124368205666542, "Height": 0.025946438312530518, "Left": 0.35739386081695557, "Top": 0.6663710474967957}, "Polygon": [{"X": 0.35739386081695557, "Y": 0.6663710474967957}, {"X": 0.376518189907074, "Y": 0.6663714647293091}, {"X": 0.37651821970939636, "Y": 0.6923174858093262}, {"X": 0.35739389061927795, "Y": 0.6923171281814575}]}, "Id": "7c7f1b97-383c-4c99-acd3-3ed3f1f01b02"}, {"BlockType": "WORD", "Confidence": 67.73497772216797, "Text": "Pickup", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17593133449554443, "Height": 0.027651553973555565, "Left": 0.45222336053848267, "Top": 0.6548234224319458}, "Polygon": [{"X": 0.45222336053848267, "Y": 0.6548234224319458}, {"X": 0.6281545162200928, "Y": 0.6548272371292114}, {"X": 0.6281546950340271, "Y": 0.6824749708175659}, {"X": 0.45222344994544983, "Y": 0.6824712157249451}]}, "Id": "646ef0a3-a145-448f-8a65-50aabfee0c9b"}, {"BlockType": "WORD", "Confidence": 60.226898193359375, "Text": "$", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03814014419913292, "Height": 0.027876194566488266, "Left": 0.6848228573799133, "Top": 0.6771287322044373}, "Polygon": [{"X": 0.6848228573799133, "Y": 0.6771287322044373}, {"X": 0.7229627966880798, "Y": 0.6771295666694641}, {"X": 0.7229630351066589, "Y": 0.7050049304962158}, {"X": 0.6848230957984924, "Y": 0.705004096031189}]}, "Id": "8d013fc9-27da-4ef0-9806-739a6a2f2c1d"}, {"BlockType": "WORD", "Confidence": 15.778459548950195, "Text": "HYS", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08013866096735, "Height": 0.031022416427731514, "Left": 0.7251543402671814, "Top": 0.6698042750358582}, "Polygon": [{"X": 0.7251543402671814, "Y": 0.6698042750358582}, {"X": 0.8052926659584045, "Y": 0.6698060035705566}, {"X": 0.8052929639816284, "Y": 0.7008267045021057}, {"X": 0.7251545786857605, "Y": 0.7008249759674072}]}, "Id": "631287a2-dbd6-4db2-9f8d-af38e5aff634"}, {"BlockType": "WORD", "Confidence": 74.37659454345703, "Text": "20sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11407884955406189, "Height": 0.030752021819353104, "Left": 0.14665275812149048, "Top": 0.719403088092804}, "Polygon": [{"X": 0.14665281772613525, "Y": 0.719403088092804}, {"X": 0.26073160767555237, "Y": 0.719405472278595}, {"X": 0.26073160767555237, "Y": 0.7501550912857056}, {"X": 0.14665275812149048, "Y": 0.7501527070999146}]}, "Id": "9f066c44-b38c-40d1-8d2c-cd47b83f37ef"}, {"BlockType": "WORD", "Confidence": 23.12111473083496, "Text": "DSI", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07473167777061462, "Height": 0.0362405925989151, "Left": 0.7319081425666809, "Top": 0.6990905404090881}, "Polygon": [{"X": 0.7319081425666809, "Y": 0.6990905404090881}, {"X": 0.8066394925117493, "Y": 0.6990921497344971}, {"X": 0.8066398501396179, "Y": 0.735331118106842}, {"X": 0.7319084405899048, "Y": 0.7353295683860779}]}, "Id": "6529ee63-84bf-4319-966a-ea099c0bb105"}, {"BlockType": "WORD", "Confidence": 55.92494583129883, "Text": "10 40", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07327038049697876, "Height": 0.024272389709949493, "Left": 0.6715286374092102, "Top": 0.74648118019104}, "Polygon": [{"X": 0.6715286374092102, "Y": 0.74648118019104}, {"X": 0.7447987794876099, "Y": 0.7464827299118042}, {"X": 0.744799017906189, "Y": 0.7707535624504089}, {"X": 0.6715287566184998, "Y": 0.7707520723342896}]}, "Id": "976ff70b-3d77-4f9d-ac71-256c00bed8a4"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "1145", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06058167666196823, "Height": 0.02555784583091736, "Left": 0.6812065839767456, "Top": 0.7851274013519287}, "Polygon": [{"X": 0.6812065839767456, "Y": 0.7851274013519287}, {"X": 0.7417880296707153, "Y": 0.785128653049469}, {"X": 0.7417882680892944, "Y": 0.8106852173805237}, {"X": 0.6812067627906799, "Y": 0.8106839656829834}]}, "Id": "908da53b-4c20-445e-aacf-e52ac449605f"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "YOUNGSTOWN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1360902637243271, "Height": 0.014133377000689507, "Left": 0.4435255229473114, "Top": 0.9436905980110168}, "Polygon": [{"X": 0.4435255229473114, "Y": 0.9436905980110168}, {"X": 0.5796157121658325, "Y": 0.943693220615387}, {"X": 0.5796157717704773, "Y": 0.9578239321708679}, {"X": 0.4435255527496338, "Y": 0.9578213095664978}]}, "Id": "0c729e3c-8cac-422b-bd99-6c393793c2b3"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c12ebee8-b7f9-4f98-a359-9419215db0f2", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c12ebee8-b7f9-4f98-a359-9419215db0f2", "content-type": "application/x-amz-json-1.1", "content-length": "70011", "date": "Wed, 24 Sep 2025 13:39:57 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c20445f6-b48f-4640-b391-f7b33682015a", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:40:00 GMT", "content-type": "application/json", "content-length": "1597", "connection": "keep-alive", "x-amzn-requestid": "c20445f6-b48f-4640-b391-f7b33682015a"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to classify. The text seems like some kind of gate entry or work order. There's \"WORK ORDER #\", \"SO-\", \"PARKER HANNIFIN\", \"Truck Ready Time\", \"Truck Departure\". This looks like an ingate or outgate? Let's analyze.\n\nWords: \"Inland plt\", \"Truck Ready Time\". Could be a gate log? There's no \"Ingate\" header explicitly. But \"Truck Ready Time\", \"Dock Time\", etc. It might be an ingate document (vehicle arrival). But we don't see \"Ingate\" specifically. There's \"Truck Ready Time: 5:00\", \"Truck Departure: 5:00\". Could be a gate log or a log document. The presence of \"Work Order #\" suggests maybe a gate log. The categories: ingate, outgate, log. \"Ingate\" definition: Record of vehicle/container entering a facility. It includes truck number, time in, etc. This page has \"Truck Ready Time\", \"Truck Departure\". Might be both entry and exit? Might be a log page. The \"log\" category includes driver log, activity record, etc. So likely it's a log.\n\nCheck for keywords: \"Ingate\" is not present. \"Outgate\" not present. But \"Log\" has keywords: \"driver log\", \"logbook\", \"activity\". There's \"Dock Time\". That seems like a log.\n\nThus classify as \"log\". We need to output JSON with page_no 1 and doc_type \"log\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"log\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2813, "outputTokens": 333, "totalTokens": 3146}, "metrics": {"latencyMs": 1466}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/b800bb45_8_log_2.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nDATE: 19-Feb\nUNIT #: 92644/283420\nTruck Ready Time: 5:00\nDRIVER: KARRA\nTruck Departure: 5:00\nDEPARTURE TIME: 5am\nPICK-\nDock Time\nCUSTOMER\nUP DEL\nWORK ORDER #\n755h tempty\n1230\n3) General Extrusion\nX\nX ***********\n1305\nx\ntsk\n2) Vari-Wall 5 SK Tube\nX\nX\nso 02115817 ***********\n1000\nfollow to\n0936\n1015\n1)\nIn Inlandplt plt\nParker Hannifin -\nX\nX\nso 02115450 *********** so 0211626\nMetamora\n50-2116053\n0540\n1 sm box\n0600\nCempties aspots\n4\nPeNNex\nX\nPickup\n$ HYS\n20sk\nDSI\n10 40\n1145\nYOUNGSTOWN\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}