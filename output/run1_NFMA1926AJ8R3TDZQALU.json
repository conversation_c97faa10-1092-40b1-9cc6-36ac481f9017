{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 7.847232552649075e-08}, {"X": 1.0, "Y": 1.0}, {"X": 2.224882855728083e-08, "Y": 1.0}]}, "Id": "a14b34f5-34eb-416e-b0f5-4059289cf49b", "Relationships": [{"Type": "CHILD", "Ids": ["d5efa0d6-5161-428f-90b8-dfaf0a6b5ff2", "9f8e1a7b-211d-4de4-90c3-a609c3d60cc7", "7970d1ee-0914-4bc8-b0e4-cd27bfac64e6", "9597845c-fffd-43d5-bb0f-8bb0621b883c", "67e25626-8504-4dfa-991f-4c7453f3fc52", "2f9bbcb9-b2b3-4212-a17c-e678af5859a7", "e56de16e-bdf6-4b68-a71b-2d5ecd036b8f", "cf440ae9-6562-462d-aef7-70a8a25e7cfa", "53321258-e387-44a1-ad85-c4d5b0b6ae1d", "5fe39227-5ed4-4b18-b2d8-d3e6b301f109", "618c1d51-1e6b-4c9b-9d15-8c55a706f5b3", "5ff21f19-1632-42c7-a230-c832c740a7d3", "47eee54e-35ac-474b-96ea-4c94a8e418e7", "92f1557b-836e-42fa-947c-f0ccce830475", "17e50573-ec88-443d-902a-5cdb2d6b104a", "b305e944-97eb-4dcc-bb59-0a48d4f22815", "9754422e-98ec-4092-b8ab-09f764330134", "61e73b16-d0c1-4620-8dfd-c167e7141d6c", "ef2e670e-8fa6-4771-9d66-3c002d0d3a33", "e269972d-3d86-4c06-a2a2-aafc04c3a29d", "7e8877f7-f3e4-4299-a18d-4ff5005edaa0", "aeba607f-830f-4dd7-934d-1512baa43b5e", "45aa11c7-d5f4-4149-b69f-8af06ed3f0b3", "abdcd028-f0e5-4f72-82c7-ca92f7a1bfce", "ce369bd9-32d3-4bcf-a1ac-8676fc7f8f84", "d3ef1af8-0bbd-41f0-9acb-222da1dab63e", "240d107a-2733-47c7-b0c5-4a0e831e6659", "d9948009-b138-4570-aa97-0005d18b3a82", "19cdda0c-9c2a-4ab8-809c-b1b9201fc4ae", "9ede9472-e0b0-461a-b946-33a0698fdbbb", "3f685e2c-30a6-4028-94f4-96cad904c0e2", "0564c2a2-b848-4bae-917b-61c38b6258c7", "bf99500a-c2fd-4eb9-b8cc-b9cce1c36b9c", "8be2c272-76ee-4c0a-b51f-c2718e2c3693", "b347b7ab-7619-4c2a-84ae-0230a82ed0dd", "2646befb-a812-4cd8-bd01-245f3aba3dad"]}]}, {"BlockType": "LINE", "Confidence": 99.07146453857422, "Text": "F", "Geometry": {"BoundingBox": {"Width": 0.006951880641281605, "Height": 0.008595552295446396, "Left": 0.5110378861427307, "Top": 0.06563873589038849}, "Polygon": [{"X": 0.5110378861427307, "Y": 0.06563873589038849}, {"X": 0.5179895162582397, "Y": 0.06563881039619446}, {"X": 0.5179897546768188, "Y": 0.07423429191112518}, {"X": 0.5110381245613098, "Y": 0.07423421740531921}]}, "Id": "d5efa0d6-5161-428f-90b8-dfaf0a6b5ff2", "Relationships": [{"Type": "CHILD", "Ids": ["f48969b0-081f-4779-a03a-bde495dbe4b4"]}]}, {"BlockType": "LINE", "Confidence": 98.38628387451172, "Text": "E", "Geometry": {"BoundingBox": {"Width": 0.006760384887456894, "Height": 0.008390610106289387, "Left": 0.4681168794631958, "Top": 0.07455413043498993}, "Polygon": [{"X": 0.4681168794631958, "Y": 0.07455413043498993}, {"X": 0.4748770594596863, "Y": 0.0745541974902153}, {"X": 0.474877268075943, "Y": 0.08294473588466644}, {"X": 0.4681170880794525, "Y": 0.08294466882944107}]}, "Id": "9f8e1a7b-211d-4de4-90c3-a609c3d60cc7", "Relationships": [{"Type": "CHILD", "Ids": ["26557a5c-c7fd-4c2d-9fbd-fe4ca7744977"]}]}, {"BlockType": "LINE", "Confidence": 97.2255630493164, "Text": "H", "Geometry": {"BoundingBox": {"Width": 0.00936376117169857, "Height": 0.0089179752394557, "Left": 0.5565693378448486, "Top": 0.07422791421413422}, "Polygon": [{"X": 0.5565693378448486, "Y": 0.07422791421413422}, {"X": 0.5659328103065491, "Y": 0.07422801107168198}, {"X": 0.565933108329773, "Y": 0.08314588665962219}, {"X": 0.5565695762634277, "Y": 0.08314578980207443}]}, "Id": "7970d1ee-0914-4bc8-b0e4-cd27bfac64e6", "Relationships": [{"Type": "CHILD", "Ids": ["6f89c3f2-031e-437a-a05d-911f3a961f7a"]}]}, {"BlockType": "LINE", "Confidence": 99.97274780273438, "Text": "Express Freight Handlers, Inc.", "Geometry": {"BoundingBox": {"Width": 0.2636076509952545, "Height": 0.01821000501513481, "Left": 0.3667372763156891, "Top": 0.12089742720127106}, "Polygon": [{"X": 0.3667372763156891, "Y": 0.12089742720127106}, {"X": 0.6303443312644958, "Y": 0.12090060114860535}, {"X": 0.6303449273109436, "Y": 0.13910743594169617}, {"X": 0.3667376637458801, "Y": 0.13910409808158875}]}, "Id": "9597845c-fffd-43d5-bb0f-8bb0621b883c", "Relationships": [{"Type": "CHILD", "Ids": ["7d313515-54fa-43ba-9a45-d308637b0a3a", "301db025-f2b0-4cbd-86b8-8dc9fa011eee", "62fb7f69-f809-4635-be3f-72ec12095800", "1f70f363-6553-4a61-aa1d-0effaebaad2f"]}]}, {"BlockType": "LINE", "Confidence": 98.52482604980469, "Text": "PO Box 30 * Glen Head * New York * 11545", "Geometry": {"BoundingBox": {"Width": 0.2563011050224304, "Height": 0.008495323359966278, "Left": 0.37269389629364014, "Top": 0.17790210247039795}, "Polygon": [{"X": 0.37269389629364014, "Y": 0.17790210247039795}, {"X": 0.6289947628974915, "Y": 0.17790569365024567}, {"X": 0.6289950013160706, "Y": 0.18639743328094482}, {"X": 0.37269410490989685, "Y": 0.18639376759529114}]}, "Id": "67e25626-8504-4dfa-991f-4c7453f3fc52", "Relationships": [{"Type": "CHILD", "Ids": ["929602f3-b890-4e1c-9a01-6b0cdf416e3c", "e391376e-b530-4fbb-bcd5-c56c5249e410", "13fef8ae-1562-415c-9b75-8f6397d8e3fe", "f5be3449-6c2e-404a-ac4f-ba7d1c16a83b", "51525fbd-2225-42f5-9d45-9b70ddf4ec61", "98ef2b7c-5616-41d2-96b9-85fd5feca574", "632ccea0-0294-4cd0-84f7-a35f75cde272", "80cae19d-8a5c-4550-9e84-c30d96b168e8", "6cfdcffa-125d-464b-8be6-0c508e12c9ab", "7f960ad2-dff1-410c-91ef-d30eda66de3d", "55a51217-36c7-42fd-aa57-92b0e2fb28dc"]}]}, {"BlockType": "LINE", "Confidence": 99.79248046875, "Text": "Tel: ************ Fax: ************", "Geometry": {"BoundingBox": {"Width": 0.21957112848758698, "Height": 0.008367220871150494, "Left": 0.39095595479011536, "Top": 0.19317269325256348}, "Polygon": [{"X": 0.39095595479011536, "Y": 0.19317269325256348}, {"X": 0.6105268001556396, "Y": 0.19317588210105896}, {"X": 0.6105270385742188, "Y": 0.20153990387916565}, {"X": 0.3909561336040497, "Y": 0.2015366554260254}]}, "Id": "2f9bbcb9-b2b3-4212-a17c-e678af5859a7", "Relationships": [{"Type": "CHILD", "Ids": ["3a7f7868-a6f1-4a8b-97c5-fd3f0c560a2a", "7a6ddaa1-95a4-474a-863b-fb4b82460130", "ac4a4d12-279f-4084-8190-1b2b5f98dbd8", "f0210889-5b60-457e-9054-494ec7ecbc47"]}]}, {"BlockType": "LINE", "Confidence": 88.04976654052734, "Text": "Email: <EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.20716942846775055, "Height": 0.0106860576197505, "Left": 0.39806750416755676, "Top": 0.20804569125175476}, "Polygon": [{"X": 0.39806750416755676, "Y": 0.20804569125175476}, {"X": 0.6052365899085999, "Y": 0.20804880559444427}, {"X": 0.6052369475364685, "Y": 0.21873174607753754}, {"X": 0.39806774258613586, "Y": 0.21872855722904205}]}, "Id": "e56de16e-bdf6-4b68-a71b-2d5ecd036b8f", "Relationships": [{"Type": "CHILD", "Ids": ["470afe4f-440e-41d8-9afc-ab62a9ce4a88", "85f3026f-fd86-4912-a8be-c327d60922c7"]}]}, {"BlockType": "LINE", "Confidence": 99.81425476074219, "Text": "Warehouse: BUNZL CINCINNATI", "Geometry": {"BoundingBox": {"Width": 0.2595404386520386, "Height": 0.010306796059012413, "Left": 0.0879838764667511, "Top": 0.24911220371723175}, "Polygon": [{"X": 0.0879838764667511, "Y": 0.24911220371723175}, {"X": 0.34752410650253296, "Y": 0.2491164654493332}, {"X": 0.3475243151187897, "Y": 0.2594189941883087}, {"X": 0.08798398077487946, "Y": 0.2594146430492401}]}, "Id": "cf440ae9-6562-462d-aef7-70a8a25e7cfa", "Relationships": [{"Type": "CHILD", "Ids": ["20d78200-d29e-4340-9ccb-700832bf3c34", "dc6ae0bd-966e-4ae2-a2ba-5b0af9e3d849", "b41777e3-8e1d-47ee-97d1-0f0796a9c5c7"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.05339823663234711, "Height": 0.01020638644695282, "Left": 0.576462984085083, "Top": 0.2491791993379593}, "Polygon": [{"X": 0.576462984085083, "Y": 0.2491791993379593}, {"X": 0.6298608779907227, "Y": 0.24918007850646973}, {"X": 0.6298612356185913, "Y": 0.2593855857849121}, {"X": 0.5764632821083069, "Y": 0.2593846917152405}]}, "Id": "53321258-e387-44a1-ad85-c4d5b0b6ae1d", "Relationships": [{"Type": "CHILD", "Ids": ["6f196244-f3e1-449e-9f8e-b13e652caaef"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "MFS TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.14004312455654144, "Height": 0.010341928340494633, "Left": 0.6549314856529236, "Top": 0.2491089254617691}, "Polygon": [{"X": 0.6549314856529236, "Y": 0.2491089254617691}, {"X": 0.7949742078781128, "Y": 0.24911123514175415}, {"X": 0.7949746251106262, "Y": 0.25945085287094116}, {"X": 0.6549317836761475, "Y": 0.25944849848747253}]}, "Id": "5fe39227-5ed4-4b18-b2d8-d3e6b301f109", "Relationships": [{"Type": "CHILD", "Ids": ["9197879b-67b2-4a8d-a2e0-063b75b38784", "0f18ee6f-b631-4823-976d-1e608d7f70db"]}]}, {"BlockType": "LINE", "Confidence": 99.9509048461914, "Text": "Load No: 546-250922-10263774", "Geometry": {"BoundingBox": {"Width": 0.2508414685726166, "Height": 0.010165429674088955, "Left": 0.11069457232952118, "Top": 0.27212393283843994}, "Polygon": [{"X": 0.11069457232952118, "Y": 0.27212393283843994}, {"X": 0.36153584718704224, "Y": 0.27212825417518616}, {"X": 0.36153605580329895, "Y": 0.2822893559932709}, {"X": 0.11069467663764954, "Y": 0.2822849452495575}]}, "Id": "618c1d51-1e6b-4c9b-9d15-8c55a706f5b3", "Relationships": [{"Type": "CHILD", "Ids": ["905966f6-d31a-4909-b227-4132eb1555d3", "c961deb9-abb4-4571-8467-4b5afa303b9d", "5b379f31-fab4-47be-a0c6-ea350cfb0ccd"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Tractor No:", "Geometry": {"BoundingBox": {"Width": 0.0817163810133934, "Height": 0.010024398565292358, "Left": 0.5528192520141602, "Top": 0.2720709443092346}, "Polygon": [{"X": 0.5528192520141602, "Y": 0.2720709443092346}, {"X": 0.6345353126525879, "Y": 0.27207234501838684}, {"X": 0.6345356702804565, "Y": 0.282095342874527}, {"X": 0.552819550037384, "Y": 0.28209388256073}]}, "Id": "5ff21f19-1632-42c7-a230-c832c740a7d3", "Relationships": [{"Type": "CHILD", "Ids": ["7dd377fa-9739-4dc0-881a-c1d522e24563", "685df4ea-8f74-4fee-a8aa-7c6e1d94c3fa"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "3398", "Geometry": {"BoundingBox": {"Width": 0.036064863204956055, "Height": 0.010023591108620167, "Left": 0.6548202633857727, "Top": 0.2721776068210602}, "Polygon": [{"X": 0.6548202633857727, "Y": 0.2721776068210602}, {"X": 0.6908847689628601, "Y": 0.2721782326698303}, {"X": 0.6908851265907288, "Y": 0.28220120072364807}, {"X": 0.6548205614089966, "Y": 0.28220057487487793}]}, "Id": "47eee54e-35ac-474b-96ea-4c94a8e418e7", "Relationships": [{"Type": "CHILD", "Ids": ["1e80dff0-52e5-43a5-b8df-d337010918f8"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Door No:", "Geometry": {"BoundingBox": {"Width": 0.0640050396323204, "Height": 0.010106916539371014, "Left": 0.11158276349306107, "Top": 0.29466012120246887}, "Polygon": [{"X": 0.11158276349306107, "Y": 0.29466012120246887}, {"X": 0.17558766901493073, "Y": 0.2946612536907196}, {"X": 0.17558780312538147, "Y": 0.30476701259613037}, {"X": 0.11158286780118942, "Y": 0.30476585030555725}]}, "Id": "92f1557b-836e-42fa-947c-f0ccce830475", "Relationships": [{"Type": "CHILD", "Ids": ["d861c801-53c9-497e-ae63-c04ece125ef9", "38168f82-7c07-4a45-967c-ef9f319c7f7e"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "13", "Geometry": {"BoundingBox": {"Width": 0.01710517518222332, "Height": 0.009732283651828766, "Left": 0.19693772494792938, "Top": 0.29505375027656555}, "Polygon": [{"X": 0.19693772494792938, "Y": 0.29505375027656555}, {"X": 0.2140427678823471, "Y": 0.2950540781021118}, {"X": 0.21404290199279785, "Y": 0.3047860562801361}, {"X": 0.19693787395954132, "Y": 0.30478572845458984}]}, "Id": "17e50573-ec88-443d-902a-5cdb2d6b104a", "Relationships": [{"Type": "CHILD", "Ids": ["68cb5166-11a1-47fa-a035-133dd331e46b"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Trailer No: 92940", "Geometry": {"BoundingBox": {"Width": 0.14138343930244446, "Height": 0.010191794484853745, "Left": 0.5586643815040588, "Top": 0.29474467039108276}, "Polygon": [{"X": 0.5586643815040588, "Y": 0.29474467039108276}, {"X": 0.700047492980957, "Y": 0.2947472035884857}, {"X": 0.7000478506088257, "Y": 0.3049364686012268}, {"X": 0.5586646795272827, "Y": 0.3049338757991791}]}, "Id": "b305e944-97eb-4dcc-bb59-0a48d4f22815", "Relationships": [{"Type": "CHILD", "Ids": ["4ce8fb0c-267d-4d06-8f98-ed3acef7e3e1", "5e87fbc7-220b-4a55-a6e3-9183ffbbd04e", "7dba456d-8b80-47a0-af2b-c787d196addd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Payment Type:", "Geometry": {"BoundingBox": {"Width": 0.10824707895517349, "Height": 0.012819333001971245, "Left": 0.0673382505774498, "Top": 0.31741005182266235}, "Polygon": [{"X": 0.0673382505774498, "Y": 0.31741005182266235}, {"X": 0.17558516561985016, "Y": 0.3174121081829071}, {"X": 0.1755853295326233, "Y": 0.33022940158843994}, {"X": 0.06733836233615875, "Y": 0.3302273154258728}]}, "Id": "9754422e-98ec-4092-b8ab-09f764330134", "Relationships": [{"Type": "CHILD", "Ids": ["61d54cc5-164f-448f-a1ea-43000fdc41de", "fa0a94e7-4233-4332-a364-bf3a7bb39080"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.0411771796643734, "Height": 0.012826220132410526, "Left": 0.1966267079114914, "Top": 0.3173351585865021}, "Polygon": [{"X": 0.1966267079114914, "Y": 0.3173351585865021}, {"X": 0.23780368268489838, "Y": 0.31733593344688416}, {"X": 0.2378038763999939, "Y": 0.33016136288642883}, {"X": 0.19662688672542572, "Y": 0.33016058802604675}]}, "Id": "61e73b16-d0c1-4620-8dfd-c167e7141d6c", "Relationships": [{"Type": "CHILD", "Ids": ["ac1ecaea-d043-47ee-84a4-0c669102876e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver Name: MOHAMED", "Geometry": {"BoundingBox": {"Width": 0.20066164433956146, "Height": 0.01012786291539669, "Left": 0.5395957231521606, "Top": 0.3174363970756531}, "Polygon": [{"X": 0.5395957231521606, "Y": 0.3174363970756531}, {"X": 0.7402570247650146, "Y": 0.3174401521682739}, {"X": 0.7402573823928833, "Y": 0.3275642693042755}, {"X": 0.5395960211753845, "Y": 0.3275604248046875}]}, "Id": "ef2e670e-8fa6-4771-9d66-3c002d0d3a33", "Relationships": [{"Type": "CHILD", "Ids": ["c72219aa-a190-490c-b4b2-a16c55ef1595", "e7c509d8-544c-4d5b-b104-f724601bff2d", "a2e80b0d-53d0-4de2-9233-7e916c3b271a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date: 09/22/2025", "Geometry": {"BoundingBox": {"Width": 0.13964836299419403, "Height": 0.010074948891997337, "Left": 0.13806554675102234, "Top": 0.34026989340782166}, "Polygon": [{"X": 0.13806554675102234, "Y": 0.34026989340782166}, {"X": 0.27771374583244324, "Y": 0.3402726352214813}, {"X": 0.2777138948440552, "Y": 0.35034483671188354}, {"X": 0.1380656659603119, "Y": 0.3503420650959015}]}, "Id": "e269972d-3d86-4c06-a2a2-aafc04c3a29d", "Relationships": [{"Type": "CHILD", "Ids": ["f5030f2a-0a9f-4155-bad9-db1529686210", "db87443f-348e-45aa-b167-f46f43fb2430"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Signature:", "Geometry": {"BoundingBox": {"Width": 0.07281677424907684, "Height": 0.012610767036676407, "Left": 0.5614590644836426, "Top": 0.3401263654232025}, "Polygon": [{"X": 0.5614590644836426, "Y": 0.3401263654232025}, {"X": 0.6342754364013672, "Y": 0.3401277959346771}, {"X": 0.6342758536338806, "Y": 0.3527371287345886}, {"X": 0.5614594221115112, "Y": 0.352735698223114}]}, "Id": "7e8877f7-f3e4-4299-a18d-4ff5005edaa0", "Relationships": [{"Type": "CHILD", "Ids": ["dbe941f7-083d-4ba5-88e6-536789407734"]}]}, {"BlockType": "LINE", "Confidence": 99.84783172607422, "Text": "PO No:", "Geometry": {"BoundingBox": {"Width": 0.04168291389942169, "Height": 0.008322002366185188, "Left": 0.09316108375787735, "Top": 0.3849753737449646}, "Polygon": [{"X": 0.09316108375787735, "Y": 0.3849753737449646}, {"X": 0.13484390079975128, "Y": 0.38497623801231384}, {"X": 0.13484399020671844, "Y": 0.39329737424850464}, {"X": 0.09316116571426392, "Y": 0.393296480178833}]}, "Id": "aeba607f-830f-4dd7-934d-1512baa43b5e", "Relationships": [{"Type": "CHILD", "Ids": ["1d506704-f7f4-42f6-827a-5321de9690fa", "185aee0f-62e6-47e8-949f-746af15da60d"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Vendor Name:", "Geometry": {"BoundingBox": {"Width": 0.08405861258506775, "Height": 0.00833145622164011, "Left": 0.2305338978767395, "Top": 0.38501328229904175}, "Polygon": [{"X": 0.2305338978767395, "Y": 0.38501328229904175}, {"X": 0.3145923614501953, "Y": 0.3850150406360626}, {"X": 0.31459251046180725, "Y": 0.3933447301387787}, {"X": 0.23053403198719025, "Y": 0.3933429419994354}]}, "Id": "45aa11c7-d5f4-4149-b69f-8af06ed3f0b3", "Relationships": [{"Type": "CHILD", "Ids": ["7750f2da-dc12-4996-ae67-5acee864f0b6", "4d84e155-e9ed-44de-b14a-6c5565ef1a1b"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "Vendor No:", "Geometry": {"BoundingBox": {"Width": 0.06583129614591599, "Height": 0.008363592438399792, "Left": 0.3851209878921509, "Top": 0.385010689496994}, "Polygon": [{"X": 0.3851209878921509, "Y": 0.385010689496994}, {"X": 0.45095208287239075, "Y": 0.38501206040382385}, {"X": 0.45095229148864746, "Y": 0.3933742642402649}, {"X": 0.3851211667060852, "Y": 0.39337286353111267}]}, "Id": "abdcd028-f0e5-4f72-82c7-ca92f7a1bfce", "Relationships": [{"Type": "CHILD", "Ids": ["5f4c53da-eb2a-4650-93d8-d5fc71e45fb9", "72fa7ec3-2273-4fbd-9aec-ec8aba5dbc71"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Qty:", "Geometry": {"BoundingBox": {"Width": 0.023051561787724495, "Height": 0.010232093743979931, "Left": 0.48613956570625305, "Top": 0.38496649265289307}, "Polygon": [{"X": 0.48613956570625305, "Y": 0.38496649265289307}, {"X": 0.5091908574104309, "Y": 0.38496696949005127}, {"X": 0.5091911554336548, "Y": 0.3951985836029053}, {"X": 0.48613983392715454, "Y": 0.3951980769634247}]}, "Id": "ce369bd9-32d3-4bcf-a1ac-8676fc7f8f84", "Relationships": [{"Type": "CHILD", "Ids": ["8b4fca02-55af-4dab-adee-05b93eba0bb5"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Weight:", "Geometry": {"BoundingBox": {"Width": 0.0444023497402668, "Height": 0.010231433436274529, "Left": 0.545785129070282, "Top": 0.38499367237091064}, "Polygon": [{"X": 0.545785129070282, "Y": 0.38499367237091064}, {"X": 0.590187132358551, "Y": 0.38499462604522705}, {"X": 0.5901874899864197, "Y": 0.3952251076698303}, {"X": 0.5457854270935059, "Y": 0.3952241539955139}]}, "Id": "d3ef1af8-0bbd-41f0-9acb-222da1dab63e", "Relationships": [{"Type": "CHILD", "Ids": ["a9ff1709-15ca-4c75-92ce-0947fbe56dc7"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Charge:", "Geometry": {"BoundingBox": {"Width": 0.04569367691874504, "Height": 0.01043517142534256, "Left": 0.6158967018127441, "Top": 0.3847538232803345}, "Polygon": [{"X": 0.6158967018127441, "Y": 0.3847538232803345}, {"X": 0.6615899801254272, "Y": 0.3847547769546509}, {"X": 0.6615903377532959, "Y": 0.39518898725509644}, {"X": 0.615896999835968, "Y": 0.39518800377845764}]}, "Id": "240d107a-2733-47c7-b0c5-4a0e831e6659", "Relationships": [{"Type": "CHILD", "Ids": ["ac70520e-57c9-414f-889a-414a0c7d6942"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Fee:", "Geometry": {"BoundingBox": {"Width": 0.02531522884964943, "Height": 0.008393465541303158, "Left": 0.6970247030258179, "Top": 0.38502052426338196}, "Polygon": [{"X": 0.6970247030258179, "Y": 0.38502052426338196}, {"X": 0.7223396301269531, "Y": 0.38502106070518494}, {"X": 0.722339928150177, "Y": 0.3934139907360077}, {"X": 0.6970250010490417, "Y": 0.3934134542942047}]}, "Id": "d9948009-b138-4570-aa97-0005d18b3a82", "Relationships": [{"Type": "CHILD", "Ids": ["9753b8c9-6dcc-454b-8ed6-6032c755ffd7"]}]}, {"BlockType": "LINE", "Confidence": 97.88494873046875, "Text": "Add'l Info:", "Geometry": {"BoundingBox": {"Width": 0.05747023597359657, "Height": 0.008402165025472641, "Left": 0.8126881122589111, "Top": 0.3848889172077179}, "Polygon": [{"X": 0.8126881122589111, "Y": 0.3848889172077179}, {"X": 0.8701580166816711, "Y": 0.3848901391029358}, {"X": 0.8701583743095398, "Y": 0.39329108595848083}, {"X": 0.8126884698867798, "Y": 0.39328986406326294}]}, "Id": "19cdda0c-9c2a-4ab8-809c-b1b9201fc4ae", "Relationships": [{"Type": "CHILD", "Ids": ["e58ac4a7-bfe5-438a-9d7d-bf5a37333538", "13a3c7bf-469f-49e6-91c0-e178ee534405"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "669254", "Geometry": {"BoundingBox": {"Width": 0.04367305338382721, "Height": 0.008346701972186565, "Left": 0.0923449918627739, "Top": 0.4180149435997009}, "Polygon": [{"X": 0.0923449918627739, "Y": 0.4180149435997009}, {"X": 0.13601794838905334, "Y": 0.4180159270763397}, {"X": 0.1360180526971817, "Y": 0.42636165022850037}, {"X": 0.09234507381916046, "Y": 0.4263606667518616}]}, "Id": "9ede9472-e0b0-461a-b946-33a0698fdbbb", "Relationships": [{"Type": "CHILD", "Ids": ["9a336805-4f96-4a01-af9d-0eea99939c64"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "LINDAR FORMING", "Geometry": {"BoundingBox": {"Width": 0.11262648552656174, "Height": 0.008453307673335075, "Left": 0.2165529429912567, "Top": 0.4178619980812073}, "Polygon": [{"X": 0.2165529429912567, "Y": 0.4178619980812073}, {"X": 0.3291792571544647, "Y": 0.41786450147628784}, {"X": 0.32917943596839905, "Y": 0.4263152778148651}, {"X": 0.21655307710170746, "Y": 0.42631274461746216}]}, "Id": "3f685e2c-30a6-4028-94f4-96cad904c0e2", "Relationships": [{"Type": "CHILD", "Ids": ["f2bce3dc-3ba9-421a-8dbc-be9605567a58", "630e8b51-9b82-4a94-b794-35b545f851b5"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "293800", "Geometry": {"BoundingBox": {"Width": 0.043537139892578125, "Height": 0.008146032691001892, "Left": 0.396616131067276, "Top": 0.4181402623653412}, "Polygon": [{"X": 0.396616131067276, "Y": 0.4181402623653412}, {"X": 0.4401530623435974, "Y": 0.4181412160396576}, {"X": 0.4401532709598541, "Y": 0.4262862801551819}, {"X": 0.3966163098812103, "Y": 0.4262852966785431}]}, "Id": "0564c2a2-b848-4bae-917b-61c38b6258c7", "Relationships": [{"Type": "CHILD", "Ids": ["09ed9d1c-3d18-4909-afd9-a45a5f47e3f5"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "932", "Geometry": {"BoundingBox": {"Width": 0.021716533228754997, "Height": 0.008253717795014381, "Left": 0.48700088262557983, "Top": 0.41806164383888245}, "Polygon": [{"X": 0.48700088262557983, "Y": 0.41806164383888245}, {"X": 0.5087172389030457, "Y": 0.41806212067604065}, {"X": 0.50871741771698, "Y": 0.4263153374195099}, {"X": 0.48700112104415894, "Y": 0.4263148605823517}]}, "Id": "bf99500a-c2fd-4eb9-b8cc-b9cce1c36b9c", "Relationships": [{"Type": "CHILD", "Ids": ["2ecdd24b-dbb5-4712-b311-0d52c0bb53a0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0.0", "Geometry": {"BoundingBox": {"Width": 0.018288269639015198, "Height": 0.008167123422026634, "Left": 0.5591519474983215, "Top": 0.41814374923706055}, "Polygon": [{"X": 0.5591519474983215, "Y": 0.41814374923706055}, {"X": 0.5774400234222412, "Y": 0.418144166469574}, {"X": 0.5774402618408203, "Y": 0.42631086707115173}, {"X": 0.5591521859169006, "Y": 0.4263104498386383}]}, "Id": "8be2c272-76ee-4c0a-b51f-c2718e2c3693", "Relationships": [{"Type": "CHILD", "Ids": ["4de2fc17-cbea-4c93-9a1b-1df0d5367e34"]}]}, {"BlockType": "LINE", "Confidence": 99.853515625, "Text": "$140.0", "Geometry": {"BoundingBox": {"Width": 0.04003424569964409, "Height": 0.009499159641563892, "Left": 0.6188591718673706, "Top": 0.41763997077941895}, "Polygon": [{"X": 0.6188591718673706, "Y": 0.41763997077941895}, {"X": 0.6588931083679199, "Y": 0.4176408648490906}, {"X": 0.6588934063911438, "Y": 0.42713913321495056}, {"X": 0.6188594698905945, "Y": 0.42713823914527893}]}, "Id": "b347b7ab-7619-4c2a-84ae-0230a82ed0dd", "Relationships": [{"Type": "CHILD", "Ids": ["fdd47c36-844c-403d-a701-fcd91cde7894"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "$7.7", "Geometry": {"BoundingBox": {"Width": 0.025716353207826614, "Height": 0.009561344049870968, "Left": 0.6967229247093201, "Top": 0.417610764503479}, "Polygon": [{"X": 0.6967229247093201, "Y": 0.417610764503479}, {"X": 0.7224389314651489, "Y": 0.41761133074760437}, {"X": 0.7224392890930176, "Y": 0.42717212438583374}, {"X": 0.696723222732544, "Y": 0.427171528339386}]}, "Id": "2646befb-a812-4cd8-bd01-245f3aba3dad", "Relationships": [{"Type": "CHILD", "Ids": ["86061c82-1915-404e-8f74-3654043b718e"]}]}, {"BlockType": "WORD", "Confidence": 99.07146453857422, "Text": "F", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006951880641281605, "Height": 0.008595552295446396, "Left": 0.5110378861427307, "Top": 0.06563873589038849}, "Polygon": [{"X": 0.5110378861427307, "Y": 0.06563873589038849}, {"X": 0.5179895162582397, "Y": 0.06563881039619446}, {"X": 0.5179897546768188, "Y": 0.07423429191112518}, {"X": 0.5110381245613098, "Y": 0.07423421740531921}]}, "Id": "f48969b0-081f-4779-a03a-bde495dbe4b4"}, {"BlockType": "WORD", "Confidence": 98.38628387451172, "Text": "E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006760384887456894, "Height": 0.008390610106289387, "Left": 0.4681168794631958, "Top": 0.07455413043498993}, "Polygon": [{"X": 0.4681168794631958, "Y": 0.07455413043498993}, {"X": 0.4748770594596863, "Y": 0.0745541974902153}, {"X": 0.474877268075943, "Y": 0.08294473588466644}, {"X": 0.4681170880794525, "Y": 0.08294466882944107}]}, "Id": "26557a5c-c7fd-4c2d-9fbd-fe4ca7744977"}, {"BlockType": "WORD", "Confidence": 97.2255630493164, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00936376117169857, "Height": 0.0089179752394557, "Left": 0.5565693378448486, "Top": 0.07422791421413422}, "Polygon": [{"X": 0.5565693378448486, "Y": 0.07422791421413422}, {"X": 0.5659328103065491, "Y": 0.07422801107168198}, {"X": 0.565933108329773, "Y": 0.08314588665962219}, {"X": 0.5565695762634277, "Y": 0.08314578980207443}]}, "Id": "6f89c3f2-031e-437a-a05d-911f3a961f7a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Express", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06566169857978821, "Height": 0.01698046736419201, "Left": 0.3667373061180115, "Top": 0.12186527252197266}, "Polygon": [{"X": 0.3667373061180115, "Y": 0.12186527252197266}, {"X": 0.43239858746528625, "Y": 0.12186606228351593}, {"X": 0.4323990046977997, "Y": 0.13884574174880981}, {"X": 0.36673763394355774, "Y": 0.13884490728378296}]}, "Id": "7d313515-54fa-43ba-9a45-d308637b0a3a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06247914209961891, "Height": 0.017972949892282486, "Left": 0.43821635842323303, "Top": 0.12113284319639206}, "Polygon": [{"X": 0.43821635842323303, "Y": 0.12113284319639206}, {"X": 0.5006950497627258, "Y": 0.12113359570503235}, {"X": 0.500695526599884, "Y": 0.13910579681396484}, {"X": 0.43821680545806885, "Y": 0.13910500705242157}]}, "Id": "301db025-f2b0-4cbd-86b8-8dc9fa011eee"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Handlers,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08623532205820084, "Height": 0.016384536400437355, "Left": 0.5063319802284241, "Top": 0.12089911103248596}, "Polygon": [{"X": 0.5063319802284241, "Y": 0.12089911103248596}, {"X": 0.5925667881965637, "Y": 0.12090014666318893}, {"X": 0.5925673246383667, "Y": 0.13728365302085876}, {"X": 0.5063323974609375, "Y": 0.1372825652360916}]}, "Id": "62fb7f69-f809-4635-be3f-72ec12095800"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031009498983621597, "Height": 0.013270002789795399, "Left": 0.5993353128433228, "Top": 0.12191068381071091}, "Polygon": [{"X": 0.5993353128433228, "Y": 0.12191068381071091}, {"X": 0.6303443908691406, "Y": 0.12191106379032135}, {"X": 0.630344808101654, "Y": 0.13518068194389343}, {"X": 0.5993357300758362, "Y": 0.1351802945137024}]}, "Id": "1f70f363-6553-4a61-aa1d-0effaebaad2f"}, {"BlockType": "WORD", "Confidence": 99.80229949951172, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01898111216723919, "Height": 0.008436697535216808, "Left": 0.37269389629364014, "Top": 0.17791323363780975}, "Polygon": [{"X": 0.37269389629364014, "Y": 0.17791323363780975}, {"X": 0.39167484641075134, "Y": 0.17791350185871124}, {"X": 0.39167502522468567, "Y": 0.18634992837905884}, {"X": 0.37269407510757446, "Y": 0.18634966015815735}]}, "Id": "929602f3-b890-4e1c-9a01-6b0cdf416e3c"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022848941385746002, "Height": 0.008330055512487888, "Left": 0.39535921812057495, "Top": 0.17794843018054962}, "Polygon": [{"X": 0.39535921812057495, "Y": 0.17794843018054962}, {"X": 0.41820797324180603, "Y": 0.1779487580060959}, {"X": 0.41820818185806274, "Y": 0.18627849221229553}, {"X": 0.39535942673683167, "Y": 0.18627816438674927}]}, "Id": "e391376e-b530-4fbb-bcd5-c56c5249e410"}, {"BlockType": "WORD", "Confidence": 99.0479507446289, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014560744166374207, "Height": 0.008134812116622925, "Left": 0.42174434661865234, "Top": 0.17816394567489624}, "Polygon": [{"X": 0.42174434661865234, "Y": 0.17816394567489624}, {"X": 0.43630489706993103, "Y": 0.17816415429115295}, {"X": 0.43630510568618774, "Y": 0.18629875779151917}, {"X": 0.42174455523490906, "Y": 0.18629854917526245}]}, "Id": "13fef8ae-1562-415c-9b75-8f6397d8e3fe"}, {"BlockType": "WORD", "Confidence": 96.56169891357422, "Text": "*", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005271525122225285, "Height": 0.0036343534011393785, "Left": 0.44050630927085876, "Top": 0.17812933027744293}, "Polygon": [{"X": 0.44050630927085876, "Y": 0.17812933027744293}, {"X": 0.4457777440547943, "Y": 0.1781294047832489}, {"X": 0.4457778334617615, "Y": 0.1817636787891388}, {"X": 0.4405063986778259, "Y": 0.18176360428333282}]}, "Id": "f5be3449-6c2e-404a-ac4f-ba7d1c16a83b"}, {"BlockType": "WORD", "Confidence": 99.75426483154297, "Text": "Glen", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02749883383512497, "Height": 0.008389095775783062, "Left": 0.4485822916030884, "Top": 0.1779286414384842}, "Polygon": [{"X": 0.4485822916030884, "Y": 0.1779286414384842}, {"X": 0.47608089447021484, "Y": 0.17792902886867523}, {"X": 0.47608110308647156, "Y": 0.18631774187088013}, {"X": 0.4485824704170227, "Y": 0.1863173544406891}]}, "Id": "51525fbd-2225-42f5-9d45-9b70ddf4ec61"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "Head", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032460495829582214, "Height": 0.00841500237584114, "Left": 0.47928088903427124, "Top": 0.17790359258651733}, "Polygon": [{"X": 0.47928088903427124, "Y": 0.17790359258651733}, {"X": 0.5117411613464355, "Y": 0.17790405452251434}, {"X": 0.5117413997650146, "Y": 0.18631859123706818}, {"X": 0.47928112745285034, "Y": 0.18631812930107117}]}, "Id": "98ef2b7c-5616-41d2-96b9-85fd5feca574"}, {"BlockType": "WORD", "Confidence": 93.97301483154297, "Text": "*", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0052144769579172134, "Height": 0.00356805557385087, "Left": 0.5152611136436462, "Top": 0.17820988595485687}, "Polygon": [{"X": 0.5152611136436462, "Y": 0.17820988595485687}, {"X": 0.520475447177887, "Y": 0.17820996046066284}, {"X": 0.5204755663871765, "Y": 0.1817779392004013}, {"X": 0.515261173248291, "Y": 0.18177786469459534}]}, "Id": "632ccea0-0294-4cd0-84f7-a35f75cde272"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "New", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026920827105641365, "Height": 0.008299236185848713, "Left": 0.5233808755874634, "Top": 0.17803646624088287}, "Polygon": [{"X": 0.5233808755874634, "Y": 0.17803646624088287}, {"X": 0.5503014326095581, "Y": 0.17803683876991272}, {"X": 0.5503016710281372, "Y": 0.1863356977701187}, {"X": 0.5233810544013977, "Y": 0.18633531033992767}]}, "Id": "80cae19d-8a5c-4550-9e84-c30d96b168e8"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "York", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026485024020075798, "Height": 0.008278011344373226, "Left": 0.5539129376411438, "Top": 0.17791208624839783}, "Polygon": [{"X": 0.5539129376411438, "Y": 0.17791208624839783}, {"X": 0.5803977251052856, "Y": 0.17791245877742767}, {"X": 0.5803979635238647, "Y": 0.18619009852409363}, {"X": 0.5539131760597229, "Y": 0.18618972599506378}]}, "Id": "6cfdcffa-125d-464b-8be6-0c508e12c9ab"}, {"BlockType": "WORD", "Confidence": 95.54528045654297, "Text": "*", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005177057348191738, "Height": 0.0037423570174723864, "Left": 0.5844544172286987, "Top": 0.17801684141159058}, "Polygon": [{"X": 0.5844544172286987, "Y": 0.17801684141159058}, {"X": 0.5896313786506653, "Y": 0.17801691591739655}, {"X": 0.5896314382553101, "Y": 0.18175919353961945}, {"X": 0.5844545364379883, "Y": 0.18175911903381348}]}, "Id": "7f960ad2-dff1-410c-91ef-d30eda66de3d"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "11545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035345807671546936, "Height": 0.008224684745073318, "Left": 0.5936492085456848, "Top": 0.1781727373600006}, "Polygon": [{"X": 0.5936492085456848, "Y": 0.1781727373600006}, {"X": 0.6289947628974915, "Y": 0.1781732439994812}, {"X": 0.6289950013160706, "Y": 0.18639743328094482}, {"X": 0.5936494469642639, "Y": 0.18639692664146423}]}, "Id": "55a51217-36c7-42fd-aa57-92b0e2fb28dc"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "Tel:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020918484777212143, "Height": 0.008254196494817734, "Left": 0.39095595479011536, "Top": 0.19317269325256348}, "Polygon": [{"X": 0.39095595479011536, "Y": 0.19317269325256348}, {"X": 0.4118742346763611, "Y": 0.19317299127578735}, {"X": 0.4118744134902954, "Y": 0.2014268934726715}, {"X": 0.3909561336040497, "Y": 0.20142658054828644}]}, "Id": "3a7f7868-a6f1-4a8b-97c5-fd3f0c560a2a"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08185170590877533, "Height": 0.008092312142252922, "Left": 0.41578811407089233, "Top": 0.19335336983203888}, "Polygon": [{"X": 0.41578811407089233, "Y": 0.19335336983203888}, {"X": 0.49763959646224976, "Y": 0.1933545619249344}, {"X": 0.49763980507850647, "Y": 0.20144568383693695}, {"X": 0.41578829288482666, "Y": 0.20144446194171906}]}, "Id": "7a6ddaa1-95a4-474a-863b-fb4b82460130"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "Fax:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025100242346525192, "Height": 0.008170032873749733, "Left": 0.5005030035972595, "Top": 0.1932753622531891}, "Polygon": [{"X": 0.5005030035972595, "Y": 0.1932753622531891}, {"X": 0.5256029963493347, "Y": 0.19327573478221893}, {"X": 0.5256032347679138, "Y": 0.20144540071487427}, {"X": 0.5005031824111938, "Y": 0.20144502818584442}]}, "Id": "ac4a4d12-279f-4084-8190-1b2b5f98dbd8"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08070240914821625, "Height": 0.008228846825659275, "Left": 0.5298246741294861, "Top": 0.19331106543540955}, "Polygon": [{"X": 0.5298246741294861, "Y": 0.19331106543540955}, {"X": 0.6105268001556396, "Y": 0.19331224262714386}, {"X": 0.6105270385742188, "Y": 0.20153990387916565}, {"X": 0.5298248529434204, "Y": 0.20153871178627014}]}, "Id": "f0210889-5b60-457e-9054-494ec7ecbc47"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "Email:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03635449707508087, "Height": 0.008329208008944988, "Left": 0.39806750416755676, "Top": 0.2082485854625702}, "Polygon": [{"X": 0.39806750416755676, "Y": 0.2082485854625702}, {"X": 0.4344218075275421, "Y": 0.20824913680553436}, {"X": 0.43442201614379883, "Y": 0.21657779812812805}, {"X": 0.3980676829814911, "Y": 0.21657723188400269}]}, "Id": "470afe4f-440e-41d8-9afc-ab62a9ce4a88"}, {"BlockType": "WORD", "Confidence": 76.337890625, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16707590222358704, "Height": 0.010685455054044724, "Left": 0.43816104531288147, "Top": 0.2080463021993637}, "Polygon": [{"X": 0.43816104531288147, "Y": 0.2080463021993637}, {"X": 0.6052365899085999, "Y": 0.20804880559444427}, {"X": 0.6052369475364685, "Y": 0.21873174607753754}, {"X": 0.43816128373146057, "Y": 0.2187291830778122}]}, "Id": "85f3026f-fd86-4912-a8be-c327d60922c7"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "Warehouse:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08782555162906647, "Height": 0.010192771442234516, "Left": 0.0879838839173317, "Top": 0.24922333657741547}, "Polygon": [{"X": 0.0879838839173317, "Y": 0.24922333657741547}, {"X": 0.17580929398536682, "Y": 0.24922478199005127}, {"X": 0.17580942809581757, "Y": 0.2594161033630371}, {"X": 0.08798398077487946, "Y": 0.2594146430492401}]}, "Id": "20d78200-d29e-4340-9ccb-700832bf3c34"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "BUNZL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05330253764986992, "Height": 0.010096270591020584, "Left": 0.1964319944381714, "Top": 0.24918359518051147}, "Polygon": [{"X": 0.1964319944381714, "Y": 0.24918359518051147}, {"X": 0.24973437190055847, "Y": 0.24918445944786072}, {"X": 0.2497345358133316, "Y": 0.25927984714508057}, {"X": 0.19643214344978333, "Y": 0.25927895307540894}]}, "Id": "dc6ae0bd-966e-4ae2-a2ba-5b0af9e3d849"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CINCINNATI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09352359920740128, "Height": 0.010152180679142475, "Left": 0.254000723361969, "Top": 0.24911493062973022}, "Polygon": [{"X": 0.254000723361969, "Y": 0.24911493062973022}, {"X": 0.34752410650253296, "Y": 0.2491164654493332}, {"X": 0.3475243151187897, "Y": 0.259267121553421}, {"X": 0.25400087237358093, "Y": 0.2592655420303345}]}, "Id": "b41777e3-8e1d-47ee-97d1-0f0796a9c5c7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05339823663234711, "Height": 0.01020638644695282, "Left": 0.576462984085083, "Top": 0.2491791993379593}, "Polygon": [{"X": 0.576462984085083, "Y": 0.2491791993379593}, {"X": 0.6298608779907227, "Y": 0.24918007850646973}, {"X": 0.6298612356185913, "Y": 0.2593855857849121}, {"X": 0.5764632821083069, "Y": 0.2593846917152405}]}, "Id": "6f196244-f3e1-449e-9f8e-b13e652caaef"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "MFS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03408452495932579, "Height": 0.010339822620153427, "Left": 0.6549314856529236, "Top": 0.24910925328731537}, "Polygon": [{"X": 0.6549314856529236, "Y": 0.24910925328731537}, {"X": 0.6890156269073486, "Y": 0.24910981953144073}, {"X": 0.6890159845352173, "Y": 0.2594490647315979}, {"X": 0.6549317836761475, "Y": 0.25944849848747253}]}, "Id": "9197879b-67b2-4a8d-a2e0-063b75b38784"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10148962587118149, "Height": 0.010204914957284927, "Left": 0.6934849619865417, "Top": 0.24910956621170044}, "Polygon": [{"X": 0.6934849619865417, "Y": 0.24910956621170044}, {"X": 0.7949742078781128, "Y": 0.24911123514175415}, {"X": 0.7949745655059814, "Y": 0.25931447744369507}, {"X": 0.6934853196144104, "Y": 0.25931277871131897}]}, "Id": "0f18ee6f-b631-4823-976d-1e608d7f70db"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035394471138715744, "Height": 0.010017347522079945, "Left": 0.11069457232952118, "Top": 0.27212393283843994}, "Polygon": [{"X": 0.11069457232952118, "Y": 0.27212393283843994}, {"X": 0.14608891308307648, "Y": 0.2721245288848877}, {"X": 0.14608904719352722, "Y": 0.2821412682533264}, {"X": 0.11069467663764954, "Y": 0.2821406424045563}]}, "Id": "905966f6-d31a-4909-b227-4132eb1555d3"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024077143520116806, "Height": 0.00994336698204279, "Left": 0.151542067527771, "Top": 0.27215421199798584}, "Polygon": [{"X": 0.151542067527771, "Y": 0.27215421199798584}, {"X": 0.17561908066272736, "Y": 0.27215462923049927}, {"X": 0.1756192147731781, "Y": 0.28209757804870605}, {"X": 0.15154218673706055, "Y": 0.2820971608161926}]}, "Id": "c961deb9-abb4-4571-8467-4b5afa303b9d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "546-250922-10263774", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1656929850578308, "Height": 0.010060818865895271, "Left": 0.19584305584430695, "Top": 0.27222853899002075}, "Polygon": [{"X": 0.19584305584430695, "Y": 0.27222853899002075}, {"X": 0.36153584718704224, "Y": 0.27223140001296997}, {"X": 0.36153605580329895, "Y": 0.2822893559932709}, {"X": 0.19584320485591888, "Y": 0.2822864353656769}]}, "Id": "5b379f31-fab4-47be-a0c6-ea350cfb0ccd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05285346508026123, "Height": 0.010023891925811768, "Left": 0.5528192520141602, "Top": 0.2720709443092346}, "Polygon": [{"X": 0.5528192520141602, "Y": 0.2720709443092346}, {"X": 0.6056724190711975, "Y": 0.27207183837890625}, {"X": 0.6056727170944214, "Y": 0.2820948362350464}, {"X": 0.552819550037384, "Y": 0.28209388256073}]}, "Id": "7dd377fa-9739-4dc0-881a-c1d522e24563"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024265475571155548, "Height": 0.009871724992990494, "Left": 0.6102702021598816, "Top": 0.27217376232147217}, "Polygon": [{"X": 0.6102702021598816, "Y": 0.27217376232147217}, {"X": 0.6345353126525879, "Y": 0.2721741795539856}, {"X": 0.6345356702804565, "Y": 0.28204548358917236}, {"X": 0.6102705001831055, "Y": 0.28204506635665894}]}, "Id": "685df4ea-8f74-4fee-a8aa-7c6e1d94c3fa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "3398", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036064863204956055, "Height": 0.010023591108620167, "Left": 0.6548202633857727, "Top": 0.2721776068210602}, "Polygon": [{"X": 0.6548202633857727, "Y": 0.2721776068210602}, {"X": 0.6908847689628601, "Y": 0.2721782326698303}, {"X": 0.6908851265907288, "Y": 0.28220120072364807}, {"X": 0.6548205614089966, "Y": 0.28220057487487793}]}, "Id": "1e80dff0-52e5-43a5-b8df-d337010918f8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Door", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03527427837252617, "Height": 0.0100469421595335, "Left": 0.11158276349306107, "Top": 0.29466012120246887}, "Polygon": [{"X": 0.11158276349306107, "Y": 0.29466012120246887}, {"X": 0.14685691893100739, "Y": 0.294660747051239}, {"X": 0.14685703814029694, "Y": 0.30470705032348633}, {"X": 0.11158286780118942, "Y": 0.3047063946723938}]}, "Id": "d861c801-53c9-497e-ae63-c04ece125ef9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02404388226568699, "Height": 0.009948043152689934, "Left": 0.15154391527175903, "Top": 0.2948189675807953}, "Polygon": [{"X": 0.15154391527175903, "Y": 0.2948189675807953}, {"X": 0.17558766901493073, "Y": 0.2948194146156311}, {"X": 0.17558780312538147, "Y": 0.30476701259613037}, {"X": 0.15154403448104858, "Y": 0.30476659536361694}]}, "Id": "38168f82-7c07-4a45-967c-ef9f319c7f7e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "13", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01710517518222332, "Height": 0.009732283651828766, "Left": 0.19693772494792938, "Top": 0.29505375027656555}, "Polygon": [{"X": 0.19693772494792938, "Y": 0.29505375027656555}, {"X": 0.2140427678823471, "Y": 0.2950540781021118}, {"X": 0.21404290199279785, "Y": 0.3047860562801361}, {"X": 0.19693787395954132, "Y": 0.30478572845458984}]}, "Id": "68cb5166-11a1-47fa-a035-133dd331e46b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04715164378285408, "Height": 0.009960919618606567, "Left": 0.5586643815040588, "Top": 0.29474467039108276}, "Polygon": [{"X": 0.5586643815040588, "Y": 0.29474467039108276}, {"X": 0.6058157682418823, "Y": 0.2947455048561096}, {"X": 0.6058160662651062, "Y": 0.30470559000968933}, {"X": 0.5586646795272827, "Y": 0.3047047257423401}]}, "Id": "4ce8fb0c-267d-4d06-8f98-ed3acef7e3e1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023978468030691147, "Height": 0.009993991814553738, "Left": 0.6104435920715332, "Top": 0.29479944705963135}, "Polygon": [{"X": 0.6104435920715332, "Y": 0.29479944705963135}, {"X": 0.634421706199646, "Y": 0.2947998642921448}, {"X": 0.6344220638275146, "Y": 0.30479344725608826}, {"X": 0.6104438900947571, "Y": 0.30479300022125244}]}, "Id": "5e87fbc7-220b-4a55-a6e3-9183ffbbd04e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "92940", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04529600962996483, "Height": 0.009909468702971935, "Left": 0.6547518372535706, "Top": 0.2950269877910614}, "Polygon": [{"X": 0.6547518372535706, "Y": 0.2950269877910614}, {"X": 0.700047492980957, "Y": 0.29502779245376587}, {"X": 0.7000478506088257, "Y": 0.3049364686012268}, {"X": 0.6547521352767944, "Y": 0.30493563413619995}]}, "Id": "7dba456d-8b80-47a0-af2b-c787d196addd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06398268043994904, "Height": 0.012739435769617558, "Left": 0.0673382505774498, "Top": 0.3174891173839569}, "Polygon": [{"X": 0.0673382505774498, "Y": 0.3174891173839569}, {"X": 0.1313207894563675, "Y": 0.3174903094768524}, {"X": 0.13132093846797943, "Y": 0.3302285373210907}, {"X": 0.06733836233615875, "Y": 0.3302273154258728}]}, "Id": "61d54cc5-164f-448f-a1ea-43000fdc41de"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04031340405344963, "Height": 0.012541861273348331, "Left": 0.13527192175388336, "Top": 0.317411333322525}, "Polygon": [{"X": 0.13527192175388336, "Y": 0.317411333322525}, {"X": 0.17558516561985016, "Y": 0.3174121081829071}, {"X": 0.1755853295326233, "Y": 0.3299531936645508}, {"X": 0.1352720707654953, "Y": 0.3299524188041687}]}, "Id": "fa0a94e7-4233-4332-a364-bf3a7bb39080"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0411771796643734, "Height": 0.012826220132410526, "Left": 0.1966267079114914, "Top": 0.3173351585865021}, "Polygon": [{"X": 0.1966267079114914, "Y": 0.3173351585865021}, {"X": 0.23780368268489838, "Y": 0.31733593344688416}, {"X": 0.2378038763999939, "Y": 0.33016136288642883}, {"X": 0.19662688672542572, "Y": 0.33016058802604675}]}, "Id": "ac1ecaea-d043-47ee-84a4-0c669102876e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043158337473869324, "Height": 0.010028989985585213, "Left": 0.5395957231521606, "Top": 0.3175322413444519}, "Polygon": [{"X": 0.5395957231521606, "Y": 0.3175322413444519}, {"X": 0.5827537775039673, "Y": 0.31753307580947876}, {"X": 0.5827540755271912, "Y": 0.32756122946739197}, {"X": 0.5395960211753845, "Y": 0.3275604248046875}]}, "Id": "c72219aa-a190-490c-b4b2-a16c55ef1595"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046463340520858765, "Height": 0.009906529448926449, "Left": 0.5877634286880493, "Top": 0.3175622820854187}, "Polygon": [{"X": 0.5877634286880493, "Y": 0.3175622820854187}, {"X": 0.6342264413833618, "Y": 0.31756314635276794}, {"X": 0.6342267990112305, "Y": 0.3274688124656677}, {"X": 0.5877637267112732, "Y": 0.3274679183959961}]}, "Id": "e7c509d8-544c-4d5b-b104-f724601bff2d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MOHAMED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08537163585424423, "Height": 0.009936990216374397, "Left": 0.6548857688903809, "Top": 0.3174385726451874}, "Polygon": [{"X": 0.6548857688903809, "Y": 0.3174385726451874}, {"X": 0.7402570247650146, "Y": 0.3174401521682739}, {"X": 0.7402573823928833, "Y": 0.3273755609989166}, {"X": 0.6548860669136047, "Y": 0.3273739218711853}]}, "Id": "a2e80b0d-53d0-4de2-9233-7e916c3b271a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03769433870911598, "Height": 0.010072917677462101, "Left": 0.13806554675102234, "Top": 0.34026989340782166}, "Polygon": [{"X": 0.13806554675102234, "Y": 0.34026989340782166}, {"X": 0.17575974762439728, "Y": 0.34027063846588135}, {"X": 0.17575988173484802, "Y": 0.3503428101539612}, {"X": 0.1380656659603119, "Y": 0.3503420650959015}]}, "Id": "f5030f2a-0a9f-4155-bad9-db1529686210"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/22/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08170955628156662, "Height": 0.010026414878666401, "Left": 0.19600434601306915, "Top": 0.3402952253818512}, "Polygon": [{"X": 0.19600434601306915, "Y": 0.3402952253818512}, {"X": 0.27771374583244324, "Y": 0.34029683470726013}, {"X": 0.2777138948440552, "Y": 0.3503216505050659}, {"X": 0.1960044950246811, "Y": 0.3503200113773346}]}, "Id": "db87443f-348e-45aa-b167-f46f43fb2430"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07281677424907684, "Height": 0.012610767036676407, "Left": 0.5614590644836426, "Top": 0.3401263654232025}, "Polygon": [{"X": 0.5614590644836426, "Y": 0.3401263654232025}, {"X": 0.6342754364013672, "Y": 0.3401277959346771}, {"X": 0.6342758536338806, "Y": 0.3527371287345886}, {"X": 0.5614594221115112, "Y": 0.352735698223114}]}, "Id": "dbe941f7-083d-4ba5-88e6-536789407734"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018444469198584557, "Height": 0.008321505971252918, "Left": 0.09316108375787735, "Top": 0.3849753737449646}, "Polygon": [{"X": 0.09316108375787735, "Y": 0.3849753737449646}, {"X": 0.1116054579615593, "Y": 0.38497576117515564}, {"X": 0.11160554736852646, "Y": 0.39329686760902405}, {"X": 0.09316116571426392, "Y": 0.393296480178833}]}, "Id": "1d506704-f7f4-42f6-827a-5321de9690fa"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01919868029654026, "Height": 0.008129202760756016, "Left": 0.11564531177282333, "Top": 0.384982168674469}, "Polygon": [{"X": 0.11564531177282333, "Y": 0.384982168674469}, {"X": 0.13484390079975128, "Y": 0.38498255610466003}, {"X": 0.13484399020671844, "Y": 0.39311134815216064}, {"X": 0.1156454011797905, "Y": 0.3931109607219696}]}, "Id": "185aee0f-62e6-47e8-949f-746af15da60d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04264228045940399, "Height": 0.00810686033219099, "Left": 0.2305338978767395, "Top": 0.38501328229904175}, "Polygon": [{"X": 0.2305338978767395, "Y": 0.38501328229904175}, {"X": 0.27317604422569275, "Y": 0.3850141763687134}, {"X": 0.2731761932373047, "Y": 0.393120139837265}, {"X": 0.23053403198719025, "Y": 0.393119215965271}]}, "Id": "7750f2da-dc12-4996-ae67-5acee864f0b6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03788309544324875, "Height": 0.00814930722117424, "Left": 0.2767094075679779, "Top": 0.38519543409347534}, "Polygon": [{"X": 0.2767094075679779, "Y": 0.38519543409347534}, {"X": 0.3145923614501953, "Y": 0.3851962387561798}, {"X": 0.31459251046180725, "Y": 0.3933447301387787}, {"X": 0.27670955657958984, "Y": 0.3933439254760742}]}, "Id": "4d84e155-e9ed-44de-b14a-6c5565ef1a1b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04289396107196808, "Height": 0.008107112720608711, "Left": 0.3851209878921509, "Top": 0.385010689496994}, "Polygon": [{"X": 0.3851209878921509, "Y": 0.385010689496994}, {"X": 0.4280147850513458, "Y": 0.38501158356666565}, {"X": 0.42801496386528015, "Y": 0.3931177854537964}, {"X": 0.3851211667060852, "Y": 0.39311686158180237}]}, "Id": "5f4c53da-eb2a-4650-93d8-d5fc71e45fb9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019353138282895088, "Height": 0.008252180181443691, "Left": 0.4315991699695587, "Top": 0.3851220905780792}, "Polygon": [{"X": 0.4315991699695587, "Y": 0.3851220905780792}, {"X": 0.45095208287239075, "Y": 0.38512250781059265}, {"X": 0.45095229148864746, "Y": 0.3933742642402649}, {"X": 0.43159934878349304, "Y": 0.39337384700775146}]}, "Id": "72fa7ec3-2273-4fbd-9aec-ec8aba5dbc71"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Qty:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023051561787724495, "Height": 0.010232093743979931, "Left": 0.48613956570625305, "Top": 0.38496649265289307}, "Polygon": [{"X": 0.48613956570625305, "Y": 0.38496649265289307}, {"X": 0.5091908574104309, "Y": 0.38496696949005127}, {"X": 0.5091911554336548, "Y": 0.3951985836029053}, {"X": 0.48613983392715454, "Y": 0.3951980769634247}]}, "Id": "8b4fca02-55af-4dab-adee-05b93eba0bb5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0444023497402668, "Height": 0.010231433436274529, "Left": 0.545785129070282, "Top": 0.38499367237091064}, "Polygon": [{"X": 0.545785129070282, "Y": 0.38499367237091064}, {"X": 0.590187132358551, "Y": 0.38499462604522705}, {"X": 0.5901874899864197, "Y": 0.3952251076698303}, {"X": 0.5457854270935059, "Y": 0.3952241539955139}]}, "Id": "a9ff1709-15ca-4c75-92ce-0947fbe56dc7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04569367691874504, "Height": 0.01043517142534256, "Left": 0.6158967018127441, "Top": 0.3847538232803345}, "Polygon": [{"X": 0.6158967018127441, "Y": 0.3847538232803345}, {"X": 0.6615899801254272, "Y": 0.3847547769546509}, {"X": 0.6615903377532959, "Y": 0.39518898725509644}, {"X": 0.615896999835968, "Y": 0.39518800377845764}]}, "Id": "ac70520e-57c9-414f-889a-414a0c7d6942"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02531522884964943, "Height": 0.008393465541303158, "Left": 0.6970247030258179, "Top": 0.38502052426338196}, "Polygon": [{"X": 0.6970247030258179, "Y": 0.38502052426338196}, {"X": 0.7223396301269531, "Y": 0.38502106070518494}, {"X": 0.722339928150177, "Y": 0.3934139907360077}, {"X": 0.6970250010490417, "Y": 0.3934134542942047}]}, "Id": "9753b8c9-6dcc-454b-8ed6-6032c755ffd7"}, {"BlockType": "WORD", "Confidence": 96.05548095703125, "Text": "Add'l", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028811564669013023, "Height": 0.008276017382740974, "Left": 0.8126881122589111, "Top": 0.3848889172077179}, "Polygon": [{"X": 0.8126881122589111, "Y": 0.3848889172077179}, {"X": 0.8414993286132812, "Y": 0.38488951325416565}, {"X": 0.8414996862411499, "Y": 0.3931649327278137}, {"X": 0.8126884698867798, "Y": 0.3931643068790436}]}, "Id": "e58ac4a7-bfe5-438a-9d7d-bf5a37333538"}, {"BlockType": "WORD", "Confidence": 99.71440124511719, "Text": "Info:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023702990263700485, "Height": 0.00825316458940506, "Left": 0.8464553952217102, "Top": 0.38503792881965637}, "Polygon": [{"X": 0.8464553952217102, "Y": 0.38503792881965637}, {"X": 0.8701580166816711, "Y": 0.3850384056568146}, {"X": 0.8701583743095398, "Y": 0.39329108595848083}, {"X": 0.8464556932449341, "Y": 0.39329057931900024}]}, "Id": "13a3c7bf-469f-49e6-91c0-e178ee534405"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "669254", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04367305338382721, "Height": 0.008346701972186565, "Left": 0.0923449918627739, "Top": 0.4180149435997009}, "Polygon": [{"X": 0.0923449918627739, "Y": 0.4180149435997009}, {"X": 0.13601794838905334, "Y": 0.4180159270763397}, {"X": 0.1360180526971817, "Y": 0.42636165022850037}, {"X": 0.09234507381916046, "Y": 0.4263606667518616}]}, "Id": "9a336805-4f96-4a01-af9d-0eea99939c64"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LINDAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047522708773612976, "Height": 0.008451840840280056, "Left": 0.2165529429912567, "Top": 0.4178619980812073}, "Polygon": [{"X": 0.2165529429912567, "Y": 0.4178619980812073}, {"X": 0.26407551765441895, "Y": 0.41786304116249084}, {"X": 0.2640756368637085, "Y": 0.4263138175010681}, {"X": 0.21655307710170746, "Y": 0.42631274461746216}]}, "Id": "f2bce3dc-3ba9-421a-8dbc-be9605567a58"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "FORMING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061016298830509186, "Height": 0.00828051008284092, "Left": 0.26816311478614807, "Top": 0.4179764986038208}, "Polygon": [{"X": 0.26816311478614807, "Y": 0.4179764986038208}, {"X": 0.3291792571544647, "Y": 0.41797783970832825}, {"X": 0.32917943596839905, "Y": 0.42625701427459717}, {"X": 0.26816326379776, "Y": 0.42625561356544495}]}, "Id": "630e8b51-9b82-4a94-b794-35b545f851b5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "293800", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043537139892578125, "Height": 0.008146032691001892, "Left": 0.396616131067276, "Top": 0.4181402623653412}, "Polygon": [{"X": 0.396616131067276, "Y": 0.4181402623653412}, {"X": 0.4401530623435974, "Y": 0.4181412160396576}, {"X": 0.4401532709598541, "Y": 0.4262862801551819}, {"X": 0.3966163098812103, "Y": 0.4262852966785431}]}, "Id": "09ed9d1c-3d18-4909-afd9-a45a5f47e3f5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "932", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021716533228754997, "Height": 0.008253717795014381, "Left": 0.48700088262557983, "Top": 0.41806164383888245}, "Polygon": [{"X": 0.48700088262557983, "Y": 0.41806164383888245}, {"X": 0.5087172389030457, "Y": 0.41806212067604065}, {"X": 0.50871741771698, "Y": 0.4263153374195099}, {"X": 0.48700112104415894, "Y": 0.4263148605823517}]}, "Id": "2ecdd24b-dbb5-4712-b311-0d52c0bb53a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018288269639015198, "Height": 0.008167123422026634, "Left": 0.5591519474983215, "Top": 0.41814374923706055}, "Polygon": [{"X": 0.5591519474983215, "Y": 0.41814374923706055}, {"X": 0.5774400234222412, "Y": 0.418144166469574}, {"X": 0.5774402618408203, "Y": 0.42631086707115173}, {"X": 0.5591521859169006, "Y": 0.4263104498386383}]}, "Id": "4de2fc17-cbea-4c93-9a1b-1df0d5367e34"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "$140.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04003424569964409, "Height": 0.009499159641563892, "Left": 0.6188591718673706, "Top": 0.41763997077941895}, "Polygon": [{"X": 0.6188591718673706, "Y": 0.41763997077941895}, {"X": 0.6588931083679199, "Y": 0.4176408648490906}, {"X": 0.6588934063911438, "Y": 0.42713913321495056}, {"X": 0.6188594698905945, "Y": 0.42713823914527893}]}, "Id": "fdd47c36-844c-403d-a701-fcd91cde7894"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "$7.7", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025716353207826614, "Height": 0.009561344049870968, "Left": 0.6967229247093201, "Top": 0.417610764503479}, "Polygon": [{"X": 0.6967229247093201, "Y": 0.417610764503479}, {"X": 0.7224389314651489, "Y": 0.41761133074760437}, {"X": 0.7224392890930176, "Y": 0.42717212438583374}, {"X": 0.696723222732544, "Y": 0.427171528339386}]}, "Id": "86061c82-1915-404e-8f74-3654043b718e"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "31505efc-2f65-453a-81e4-23d1ce7ea718", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "31505efc-2f65-453a-81e4-23d1ce7ea718", "content-type": "application/x-amz-json-1.1", "content-length": "56261", "date": "Wed, 24 Sep 2025 13:26:50 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "38c482ef-6a43-4a9c-8cc7-1520ba31f6e5", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:54 GMT", "content-type": "application/json", "content-length": "1589", "connection": "keep-alive", "x-amzn-requestid": "38c482ef-6a43-4a9c-8cc7-1520ba31f6e5"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify this page. The content seems like a form for a freight handler, with PO, vendor name, etc. It includes \"Load No\", \"Tractor No\", \"Door No\", \"Trailer No\", \"Payment Type\", \"Driver Name\", \"Date\", \"PO No\", \"Vendor Name\", \"Vendor No\", \"Qty\", \"Weight\", \"Charge\", \"Fee\". This looks like a receipt or invoice for freight services. However, it's not necessarily a commercial invoice because not seeing HS codes or customs info. Could be a lumper receipt? Lumper receipts include PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. That seems close. The page may be a lumper receipt form. There's no mention of \"Service Charge\" but \"Charge\" and \"Fee\". There's PO no, driver name, date, load no. I think lumper receipt is appropriate.\n\nCheck if other types might fit: invoice - general carrier invoice. The presence of PO No, driver name, date, and charges. But lumper receipt is more specific. The instructions mention that if invoice has some keywords for lumper receipt like PO, door number, driver etc then lumper receipt. Yes. So classify as lumper_receipt.\n\nThus output:\n\n{\n  \"documents\": [\n    {\"page_no\": 1, \"doc_type\": \"lumper_receipt\"}\n  ]\n}\n\n"}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"lumper_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2768, "outputTokens": 312, "totalTokens": 3080}, "metrics": {"latencyMs": 1564}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/412b4bf7_NFMA1926AJ8R3TDZQALU.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nF\nE\nH\nExpress Freight Handlers, Inc.\nPO Box 30 * Glen Head * New York * 11545\nTel: ************ Fax: ************\nEmail: <EMAIL>\nWarehouse: BUNZL CINCINNATI\nCarrier:\nMFS TRANSPORT\nLoad No: 546-250922-10263774\nTractor No:\n3398\nDoor No:\n13\nTrailer No: 92940\nPayment Type:\nRelay\nDriver Name: MOHAMED\nDate: 09/22/2025\nSignature:\nPO No:\nVendor Name:\nVendor No:\nQty:\nWeight:\nCharge:\nFee:\nAdd'l Info:\n669254\nLINDAR FORMING\n293800\n932\n0.0\n$140.0\n$7.7\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}