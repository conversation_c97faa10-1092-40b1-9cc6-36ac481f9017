{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.7436594367027283, "Height": 0.8911022543907166, "Left": 0.11340716481208801, "Top": 0.08583910763263702}, "Polygon": [{"X": 0.11340716481208801, "Y": 0.08583910763263702}, {"X": 0.7987969517707825, "Y": 0.11364315450191498}, {"X": 0.8570665717124939, "Y": 0.939441442489624}, {"X": 0.11686258763074875, "Y": 0.9769413471221924}]}, "Id": "38a7cb86-3233-4d19-9f1b-907464d056e2", "Relationships": [{"Type": "CHILD", "Ids": ["5cd2b42d-791e-43b4-817f-1092dddb68ce", "3887cf34-9aaf-4410-8b58-81e02c8ca9ea", "17a23502-6fd8-490a-ba50-1d344af979db", "106b2257-1446-4814-9f14-c9c5c95d5ce0", "b10d093d-2a54-4997-8eaf-5322e343b578", "bf17c4a9-f8a4-4fc8-84de-deaf967e2209", "5556d045-7d6f-48a7-873f-f3eaefaf3b56", "690c9fde-bd6f-448e-a2f6-3ca43808b6b8", "65a84c6b-b9f5-4a66-bfa1-c0250f646438", "339dcc86-48a4-49ec-9bc0-5f1282959644", "92be8bb6-0e86-4fd1-ba90-5ec240ae6f80", "b36a5793-64c2-43fa-8c18-447686ac3648"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Lumper Receipt", "Geometry": {"BoundingBox": {"Width": 0.23245401680469513, "Height": 0.03531830012798309, "Left": 0.35847631096839905, "Top": 0.13466958701610565}, "Polygon": [{"X": 0.35847631096839905, "Y": 0.13466958701610565}, {"X": 0.5895844101905823, "Y": 0.1431012898683548}, {"X": 0.5909303426742554, "Y": 0.16998788714408875}, {"X": 0.3592378795146942, "Y": 0.16220404207706451}]}, "Id": "5cd2b42d-791e-43b4-817f-1092dddb68ce", "Relationships": [{"Type": "CHILD", "Ids": ["b578acfc-8358-4009-aea1-07b74d78ed41", "e4f3a3d6-f5df-4b40-a244-72986c16b8c7"]}]}, {"BlockType": "LINE", "Confidence": 94.7513198852539, "Text": "DATE: 8 125/25 AMOUNT: $225.00", "Geometry": {"BoundingBox": {"Width": 0.5513936877250671, "Height": 0.05167773365974426, "Left": 0.17267034947872162, "Top": 0.18310818076133728}, "Polygon": [{"X": 0.17267034947872162, "Y": 0.18310818076133728}, {"X": 0.7218894958496094, "Y": 0.20002271234989166}, {"X": 0.7240639925003052, "Y": 0.23478591442108154}, {"X": 0.17302244901657104, "Y": 0.21990269422531128}]}, "Id": "3887cf34-9aaf-4410-8b58-81e02c8ca9ea", "Relationships": [{"Type": "CHILD", "Ids": ["cc0fc3de-e1fa-4c16-9419-71eeeff1e62e", "155ccc31-e047-4923-a912-97964ba0407a", "1a2f6e1d-85ba-4823-b7c9-166a98bfe0ab", "812a9827-8ecd-427f-8aef-9df23124c05f", "bbcd3e74-ddf4-46df-bb35-214baa687cc1"]}]}, {"BlockType": "LINE", "Confidence": 98.99374389648438, "Text": "TRUCK#", "Geometry": {"BoundingBox": {"Width": 0.1122627705335617, "Height": 0.024506889283657074, "Left": 0.17303480207920074, "Top": 0.2947786748409271}, "Polygon": [{"X": 0.17303480207920074, "Y": 0.2947786748409271}, {"X": 0.28484636545181274, "Y": 0.29693642258644104}, {"X": 0.28529757261276245, "Y": 0.3192855715751648}, {"X": 0.17324960231781006, "Y": 0.3173840641975403}]}, "Id": "17a23502-6fd8-490a-ba50-1d344af979db", "Relationships": [{"Type": "CHILD", "Ids": ["8ab72c93-036a-478b-a84c-72200feeb5f5"]}]}, {"BlockType": "LINE", "Confidence": 99.35566711425781, "Text": "TRAILER#", "Geometry": {"BoundingBox": {"Width": 0.13025853037834167, "Height": 0.024420730769634247, "Left": 0.45636042952537537, "Top": 0.29591500759124756}, "Polygon": [{"X": 0.45636042952537537, "Y": 0.29591500759124756}, {"X": 0.5855489373207092, "Y": 0.2984674572944641}, {"X": 0.586618959903717, "Y": 0.3203357458114624}, {"X": 0.45717155933380127, "Y": 0.31808236241340637}]}, "Id": "106b2257-1446-4814-9f14-c9c5c95d5ce0", "Relationships": [{"Type": "CHILD", "Ids": ["9e75af47-789e-4540-ade6-605a89cb84b9"]}]}, {"BlockType": "LINE", "Confidence": 99.82063293457031, "Text": "CASE COUNT:", "Geometry": {"BoundingBox": {"Width": 0.1497657150030136, "Height": 0.02257779985666275, "Left": 0.17576941847801208, "Top": 0.3939250111579895}, "Polygon": [{"X": 0.17576941847801208, "Y": 0.3939250111579895}, {"X": 0.3250299096107483, "Y": 0.3952818214893341}, {"X": 0.3255351483821869, "Y": 0.41650283336639404}, {"X": 0.17597785592079163, "Y": 0.41547533869743347}]}, "Id": "b10d093d-2a54-4997-8eaf-5322e343b578", "Relationships": [{"Type": "CHILD", "Ids": ["a7bf17e3-3048-4575-b792-6a5f2cd134ca", "a266e7ba-489e-460c-8d3c-79b43ebda016"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "TRIP#", "Geometry": {"BoundingBox": {"Width": 0.08196884393692017, "Height": 0.021819401532411575, "Left": 0.4587930738925934, "Top": 0.39110758900642395}, "Polygon": [{"X": 0.4587930738925934, "Y": 0.39110758900642395}, {"X": 0.5398328304290771, "Y": 0.39189058542251587}, {"X": 0.5407618880271912, "Y": 0.41292697191238403}, {"X": 0.4595673084259033, "Y": 0.4123252332210541}]}, "Id": "bf17c4a9-f8a4-4fc8-84de-deaf967e2209", "Relationships": [{"Type": "CHILD", "Ids": ["8c7cd340-e04a-466d-ac45-4f1eb92c902e"]}]}, {"BlockType": "LINE", "Confidence": 99.47815704345703, "Text": "FACILITY: Demoulas / Market Basket", "Geometry": {"BoundingBox": {"Width": 0.5468223690986633, "Height": 0.029536526650190353, "Left": 0.17807526886463165, "Top": 0.48459896445274353}, "Polygon": [{"X": 0.17807526886463165, "Y": 0.48474374413490295}, {"X": 0.7232056260108948, "Y": 0.48459896445274353}, {"X": 0.7248976826667786, "Y": 0.5123350620269775}, {"X": 0.17836348712444305, "Y": 0.514135479927063}]}, "Id": "5556d045-7d6f-48a7-873f-f3eaefaf3b56", "Relationships": [{"Type": "CHILD", "Ids": ["21917821-490a-4124-9ba6-06ae1477ff11", "45ae6677-8fc9-46ca-9604-b4f8ffd572ba", "25685a30-7636-42bb-b1a3-b7bba949a7de", "78b98ed6-dcd0-46b7-b2bd-c0a0a9a60827", "3d1e3bda-d189-4eb9-a7ae-ed961ee3e8f6"]}]}, {"BlockType": "LINE", "Confidence": 99.64830017089844, "Text": "LOCATION: Lawrence, MA", "Geometry": {"BoundingBox": {"Width": 0.3867453336715698, "Height": 0.030716970562934875, "Left": 0.17798060178756714, "Top": 0.5832256078720093}, "Polygon": [{"X": 0.17798060178756714, "Y": 0.5874068140983582}, {"X": 0.5635648369789124, "Y": 0.5832256078720093}, {"X": 0.564725935459137, "Y": 0.6086946725845337}, {"X": 0.17823807895183563, "Y": 0.613942563533783}]}, "Id": "690c9fde-bd6f-448e-a2f6-3ca43808b6b8", "Relationships": [{"Type": "CHILD", "Ids": ["ccbc2d0c-0668-4fcf-aebb-1c5fa17f2407", "9d0041a2-6c99-4178-8202-5a8355c6e2f4", "7e674b98-1fbe-4d0d-8407-284b46287708"]}]}, {"BlockType": "LINE", "Confidence": 99.87045288085938, "Text": "LUMPER NAME: <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.5931205749511719, "Height": 0.06512930989265442, "Left": 0.17674489319324493, "Top": 0.6609018445014954}, "Polygon": [{"X": 0.17674489319324493, "Y": 0.6724674105644226}, {"X": 0.7666489481925964, "Y": 0.6609018445014954}, {"X": 0.7698654532432556, "Y": 0.7111415266990662}, {"X": 0.17725446820259094, "Y": 0.7260311841964722}]}, "Id": "65a84c6b-b9f5-4a66-bfa1-c0250f646438", "Relationships": [{"Type": "CHILD", "Ids": ["e03cada6-3ebe-4521-ae70-791a13edecec", "4e94d955-def1-4ac8-a865-b93db1cda6ea", "8510081f-53db-4a91-9cd7-8328d77d8cac", "463dffd5-3646-46e0-8705-4e036eabbf69"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "SIGNATURE:", "Geometry": {"BoundingBox": {"Width": 0.1797441840171814, "Height": 0.028800539672374725, "Left": 0.17560192942619324, "Top": 0.7930818200111389}, "Polygon": [{"X": 0.17560192942619324, "Y": 0.7989281415939331}, {"X": 0.3547683656215668, "Y": 0.7930818200111389}, {"X": 0.35534611344337463, "Y": 0.8155995607376099}, {"X": 0.1758153885602951, "Y": 0.8218823671340942}]}, "Id": "339dcc86-48a4-49ec-9bc0-5f1282959644", "Relationships": [{"Type": "CHILD", "Ids": ["e11c145f-d7a8-4776-a995-b139104a2c6c"]}]}, {"BlockType": "LINE", "Confidence": 73.89508819580078, "Text": "W", "Geometry": {"BoundingBox": {"Width": 0.25758227705955505, "Height": 0.06771890819072723, "Left": 0.43915489315986633, "Top": 0.7475873827934265}, "Polygon": [{"X": 0.43915489315986633, "Y": 0.7549306750297546}, {"X": 0.6934028267860413, "Y": 0.7475873827934265}, {"X": 0.6967371702194214, "Y": 0.8062893748283386}, {"X": 0.44117605686187744, "Y": 0.8153063058853149}]}, "Id": "92be8bb6-0e86-4fd1-ba90-5ec240ae6f80", "Relationships": [{"Type": "CHILD", "Ids": ["f62b9d56-d90d-494b-a432-8bcd140d4d9d"]}]}, {"BlockType": "LINE", "Confidence": 93.12659454345703, "Text": "7", "Geometry": {"BoundingBox": {"Width": 0.05080896243453026, "Height": 0.05211222544312477, "Left": 0.6971163153648376, "Top": 0.8352310061454773}, "Polygon": [{"X": 0.6971163153648376, "Y": 0.8370761275291443}, {"X": 0.7448740601539612, "Y": 0.8352310061454773}, {"X": 0.7479252815246582, "Y": 0.8852271437644958}, {"X": 0.6999652981758118, "Y": 0.8873432278633118}]}, "Id": "b36a5793-64c2-43fa-8c18-447686ac3648", "Relationships": [{"Type": "CHILD", "Ids": ["07480541-75ca-4312-8756-316ad3b330a2"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11415569484233856, "Height": 0.029599815607070923, "Left": 0.3585245907306671, "Top": 0.13641539216041565}, "Polygon": [{"X": 0.3585245907306671, "Y": 0.13641539216041565}, {"X": 0.47169554233551025, "Y": 0.14052358269691467}, {"X": 0.47268030047416687, "Y": 0.16601520776748657}, {"X": 0.3592378795146942, "Y": 0.16220404207706451}]}, "Id": "b578acfc-8358-4009-aea1-07b74d78ed41"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10733433812856674, "Height": 0.02990690991282463, "Left": 0.48355352878570557, "Top": 0.13923288881778717}, "Polygon": [{"X": 0.48355352878570557, "Y": 0.13923288881778717}, {"X": 0.5895844101905823, "Y": 0.1431012898683548}, {"X": 0.5908879041671753, "Y": 0.1691398024559021}, {"X": 0.4846009910106659, "Y": 0.1655593365430832}]}, "Id": "e4f3a3d6-f5df-4b40-a244-72986c16b8c7"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08577467501163483, "Height": 0.02317110262811184, "Left": 0.17280833423137665, "Top": 0.19752782583236694}, "Polygon": [{"X": 0.17280833423137665, "Y": 0.19752782583236694}, {"X": 0.25821495056152344, "Y": 0.2000313401222229}, {"X": 0.2585830092430115, "Y": 0.22069892287254333}, {"X": 0.1730078160762787, "Y": 0.2183741331100464}]}, "Id": "cc0fc3de-e1fa-4c16-9419-71eeeff1e62e"}, {"BlockType": "WORD", "Confidence": 98.73944091796875, "Text": "8", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.027891898527741432, "Height": 0.03209083899855614, "Left": 0.2720325291156769, "Top": 0.1912393867969513}, "Polygon": [{"X": 0.2720325291156769, "Y": 0.1912393867969513}, {"X": 0.299243301153183, "Y": 0.19206304848194122}, {"X": 0.29992443323135376, "Y": 0.22333021461963654}, {"X": 0.2726331949234009, "Y": 0.22259309887886047}]}, "Id": "155ccc31-e047-4923-a912-97964ba0407a"}, {"BlockType": "WORD", "Confidence": 77.05097961425781, "Text": "125/25", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1116025298833847, "Height": 0.028508396819233894, "Left": 0.31316491961479187, "Top": 0.1958634853363037}, "Polygon": [{"X": 0.31316491961479187, "Y": 0.1958634853363037}, {"X": 0.423915833234787, "Y": 0.19917678833007812}, {"X": 0.4247674345970154, "Y": 0.22437188029289246}, {"X": 0.3137540817260742, "Y": 0.2213461548089981}]}, "Id": "1a2f6e1d-85ba-4823-b7c9-166a98bfe0ab"}, {"BlockType": "WORD", "Confidence": 98.57561492919922, "Text": "AMOUNT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12803971767425537, "Height": 0.02331215888261795, "Left": 0.45471248030662537, "Top": 0.20386628806591034}, "Polygon": [{"X": 0.45471248030662537, "Y": 0.20386628806591034}, {"X": 0.5817937850952148, "Y": 0.20761734247207642}, {"X": 0.5827522277832031, "Y": 0.2271784394979477}, {"X": 0.45544105768203735, "Y": 0.22368797659873962}]}, "Id": "812a9827-8ecd-427f-8aef-9df23124c05f"}, {"BlockType": "WORD", "Confidence": 99.55974578857422, "Text": "$225.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1326291412115097, "Height": 0.032823897898197174, "Left": 0.591061532497406, "Top": 0.1959935426712036}, "Polygon": [{"X": 0.591061532497406, "Y": 0.1959935426712036}, {"X": 0.7218894958496094, "Y": 0.20002271234989166}, {"X": 0.7236906886100769, "Y": 0.22881744801998138}, {"X": 0.5925197601318359, "Y": 0.22518928349018097}]}, "Id": "bbcd3e74-ddf4-46df-bb35-214baa687cc1"}, {"BlockType": "WORD", "Confidence": 98.99374389648438, "Text": "TRUCK#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1122627705335617, "Height": 0.024506889283657074, "Left": 0.17303480207920074, "Top": 0.2947786748409271}, "Polygon": [{"X": 0.17303480207920074, "Y": 0.2947786748409271}, {"X": 0.28484636545181274, "Y": 0.29693642258644104}, {"X": 0.28529757261276245, "Y": 0.3192855715751648}, {"X": 0.17324960231781006, "Y": 0.3173840641975403}]}, "Id": "8ab72c93-036a-478b-a84c-72200feeb5f5"}, {"BlockType": "WORD", "Confidence": 99.35566711425781, "Text": "TRAILER#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13025853037834167, "Height": 0.024420730769634247, "Left": 0.45636042952537537, "Top": 0.29591500759124756}, "Polygon": [{"X": 0.45636042952537537, "Y": 0.29591500759124756}, {"X": 0.5855489373207092, "Y": 0.2984674572944641}, {"X": 0.586618959903717, "Y": 0.3203357458114624}, {"X": 0.45717155933380127, "Y": 0.31808236241340637}]}, "Id": "9e75af47-789e-4540-ade6-605a89cb84b9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CASE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057367924600839615, "Height": 0.021037565544247627, "Left": 0.17577818036079407, "Top": 0.39483052492141724}, "Polygon": [{"X": 0.17577818036079407, "Y": 0.39483052492141724}, {"X": 0.23283669352531433, "Y": 0.3953438699245453}, {"X": 0.23314610123634338, "Y": 0.4158681035041809}, {"X": 0.17597785592079163, "Y": 0.41547533869743347}]}, "Id": "a7bf17e3-3048-4575-b792-6a5f2cd134ca"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "COUNT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08257810026407242, "Height": 0.021037574857473373, "Left": 0.242934912443161, "Top": 0.3945355713367462}, "Polygon": [{"X": 0.242934912443161, "Y": 0.3945355713367462}, {"X": 0.3250299096107483, "Y": 0.3952818214893341}, {"X": 0.32551300525665283, "Y": 0.4155731499195099}, {"X": 0.24326302111148834, "Y": 0.4150000512599945}]}, "Id": "a266e7ba-489e-460c-8d3c-79b43ebda016"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "TRIP#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08196884393692017, "Height": 0.021819401532411575, "Left": 0.4587930738925934, "Top": 0.39110758900642395}, "Polygon": [{"X": 0.4587930738925934, "Y": 0.39110758900642395}, {"X": 0.5398328304290771, "Y": 0.39189058542251587}, {"X": 0.5407618880271912, "Y": 0.41292697191238403}, {"X": 0.4595673084259033, "Y": 0.4123252332210541}]}, "Id": "8c7cd340-e04a-466d-ac45-4f1eb92c902e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FACILITY:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13366420567035675, "Height": 0.021206799894571304, "Left": 0.1781570017337799, "Top": 0.492928683757782}, "Polygon": [{"X": 0.1781570017337799, "Y": 0.49307847023010254}, {"X": 0.3113579750061035, "Y": 0.492928683757782}, {"X": 0.31182119250297546, "Y": 0.5136958360671997}, {"X": 0.17836348712444305, "Y": 0.514135479927063}]}, "Id": "21917821-490a-4124-9ba6-06ae1477ff11"}, {"BlockType": "WORD", "Confidence": 99.56951904296875, "Text": "<PERSON><PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1461241990327835, "Height": 0.021650349721312523, "Left": 0.3482784628868103, "Top": 0.48874080181121826}, "Polygon": [{"X": 0.3482784628868103, "Y": 0.48884254693984985}, {"X": 0.4935661554336548, "Y": 0.48874080181121826}, {"X": 0.4944026470184326, "Y": 0.5099602341651917}, {"X": 0.34883400797843933, "Y": 0.5103911757469177}]}, "Id": "45ae6677-8fc9-46ca-9604-b4f8ffd572ba"}, {"BlockType": "WORD", "Confidence": 97.9091567993164, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012797658331692219, "Height": 0.020413482561707497, "Left": 0.5018003582954407, "Top": 0.48783817887306213}, "Polygon": [{"X": 0.5018003582954407, "Y": 0.48784542083740234}, {"X": 0.5137559175491333, "Y": 0.48783817887306213}, {"X": 0.51459801197052, "Y": 0.5082183480262756}, {"X": 0.5026206374168396, "Y": 0.5082516670227051}]}, "Id": "25685a30-7636-42bb-b1a3-b7bba949a7de"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Market", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10141894966363907, "Height": 0.01993963122367859, "Left": 0.5217465162277222, "Top": 0.4868346154689789}, "Polygon": [{"X": 0.5217465162277222, "Y": 0.4868852198123932}, {"X": 0.6221521496772766, "Y": 0.4868346154689789}, {"X": 0.623165488243103, "Y": 0.5065098404884338}, {"X": 0.5225833654403687, "Y": 0.5067742466926575}]}, "Id": "78b98ed6-dcd0-46b7-b2bd-c0a0a9a60827"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Basket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09385701268911362, "Height": 0.0195021815598011, "Left": 0.6305249333381653, "Top": 0.48459896445274353}, "Polygon": [{"X": 0.6305249333381653, "Y": 0.48462358117103577}, {"X": 0.7232056260108948, "Y": 0.48459896445274353}, {"X": 0.7243819236755371, "Y": 0.50388103723526}, {"X": 0.6315435767173767, "Y": 0.5041011571884155}]}, "Id": "3d1e3bda-d189-4eb9-a7ae-ed961ee3e8f6"}, {"BlockType": "WORD", "Confidence": 99.67215728759766, "Text": "LOCATION:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15688830614089966, "Height": 0.02285138890147209, "Left": 0.17803364992141724, "Top": 0.5910911560058594}, "Polygon": [{"X": 0.17803364992141724, "Y": 0.5928751230239868}, {"X": 0.33441945910453796, "Y": 0.5910911560058594}, {"X": 0.3349219560623169, "Y": 0.6118150353431702}, {"X": 0.17823807895183563, "Y": 0.613942563533783}]}, "Id": "ccbc2d0c-0668-4fcf-aebb-1c5fa17f2407"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14681224524974823, "Height": 0.022644247859716415, "Left": 0.3536892533302307, "Top": 0.587000846862793}, "Polygon": [{"X": 0.3536892533302307, "Y": 0.5886319279670715}, {"X": 0.49968165159225464, "Y": 0.587000846862793}, {"X": 0.5005015134811401, "Y": 0.6076883673667908}, {"X": 0.35423654317855835, "Y": 0.6096451282501221}]}, "Id": "9d0041a2-6c99-4178-8202-5a8355c6e2f4"}, {"BlockType": "WORD", "Confidence": 99.35247802734375, "Text": "MA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056344278156757355, "Height": 0.020967399701476097, "Left": 0.5081434845924377, "Top": 0.5832256078720093}, "Polygon": [{"X": 0.5081434845924377, "Y": 0.5838265419006348}, {"X": 0.5635648369789124, "Y": 0.5832256078720093}, {"X": 0.5644877552986145, "Y": 0.6034702658653259}, {"X": 0.508966863155365, "Y": 0.6041929721832275}]}, "Id": "7e674b98-1fbe-4d0d-8407-284b46287708"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "LUMPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12159281969070435, "Height": 0.0229689572006464, "Left": 0.17696169018745422, "Top": 0.6925950050354004}, "Polygon": [{"X": 0.17696169018745422, "Y": 0.6952553391456604}, {"X": 0.29813989996910095, "Y": 0.6925950050354004}, {"X": 0.29855450987815857, "Y": 0.7126446962356567}, {"X": 0.17715488374233246, "Y": 0.715563952922821}]}, "Id": "e03cada6-3ebe-4521-ae70-791a13edecec"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "NAME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09242690354585648, "Height": 0.022742748260498047, "Left": 0.3141244351863861, "Top": 0.6861547827720642}, "Polygon": [{"X": 0.3141244351863861, "Y": 0.6881306171417236}, {"X": 0.4059215486049652, "Y": 0.6861547827720642}, {"X": 0.406551331281662, "Y": 0.7067183256149292}, {"X": 0.3145846128463745, "Y": 0.7088975310325623}]}, "Id": "4e94d955-def1-4ac8-a865-b93db1cda6ea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.167760968208313, "Height": 0.03237372636795044, "Left": 0.4175608158111572, "Top": 0.6722910404205322}, "Polygon": [{"X": 0.4175608158111572, "Y": 0.6756935715675354}, {"X": 0.5839817523956299, "Y": 0.6722910404205322}, {"X": 0.5853217840194702, "Y": 0.7007425427436829}, {"X": 0.4184800386428833, "Y": 0.7046647667884827}]}, "Id": "8510081f-53db-4a91-9cd7-8328d77d8cac"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1606750339269638, "Height": 0.05421317741274834, "Left": 0.6091904044151306, "Top": 0.6609018445014954}, "Polygon": [{"X": 0.6091904044151306, "Y": 0.6639889478683472}, {"X": 0.7666489481925964, "Y": 0.6609018445014954}, {"X": 0.7698654532432556, "Y": 0.7111415266990662}, {"X": 0.6117192506790161, "Y": 0.7151150107383728}]}, "Id": "463dffd5-3646-46e0-8705-4e036eabbf69"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "SIGNATURE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1797441840171814, "Height": 0.028800539672374725, "Left": 0.17560192942619324, "Top": 0.7930818200111389}, "Polygon": [{"X": 0.17560192942619324, "Y": 0.7989281415939331}, {"X": 0.3547683656215668, "Y": 0.7930818200111389}, {"X": 0.35534611344337463, "Y": 0.8155995607376099}, {"X": 0.1758153885602951, "Y": 0.8218823671340942}]}, "Id": "e11c145f-d7a8-4776-a995-b139104a2c6c"}, {"BlockType": "WORD", "Confidence": 73.89508819580078, "Text": "W", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.25758227705955505, "Height": 0.06771890819072723, "Left": 0.43915489315986633, "Top": 0.7475873827934265}, "Polygon": [{"X": 0.43915489315986633, "Y": 0.7549306750297546}, {"X": 0.6934028267860413, "Y": 0.7475873827934265}, {"X": 0.6967371702194214, "Y": 0.8062893748283386}, {"X": 0.44117605686187744, "Y": 0.8153063058853149}]}, "Id": "f62b9d56-d90d-494b-a432-8bcd140d4d9d"}, {"BlockType": "WORD", "Confidence": 93.12659454345703, "Text": "7", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05080896243453026, "Height": 0.05211222544312477, "Left": 0.6971163153648376, "Top": 0.8352310061454773}, "Polygon": [{"X": 0.6971163153648376, "Y": 0.8370761275291443}, {"X": 0.7448740601539612, "Y": 0.8352310061454773}, {"X": 0.7479252815246582, "Y": 0.8852271437644958}, {"X": 0.6999652981758118, "Y": 0.8873432278633118}]}, "Id": "07480541-75ca-4312-8756-316ad3b330a2"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "921f3578-ad71-4cc4-9c9f-1ccb6246c3a6", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "921f3578-ad71-4cc4-9c9f-1ccb6246c3a6", "content-type": "application/x-amz-json-1.1", "content-length": "20966", "date": "Thu, 18 Sep 2025 10:33:54 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "8705a431-2b6a-4179-bc50-4b6027f87afb", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:33:56 GMT", "content-type": "application/json", "content-length": "649", "connection": "keep-alive", "x-amzn-requestid": "8705a431-2b6a-4179-bc50-4b6027f87afb"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page content: \"Lumper Receipt\" at top. So doc_type = lumper_receipt.\n\nWe need to output using tool classify_logistics_doc_type with documents array: one object with page_no 1 doc_type lumper_receipt.\n\nWe call functions."}}}, {"toolUse": {"toolUseId": "tooluse__vSAsoMtSfqX2LjksRVwlQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2214, "outputTokens": 97, "totalTokens": 2311}, "metrics": {"latencyMs": 648}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/ac2d8ed7_21_lumper_receipt_2.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nLumper Receipt\nDATE: 8 125/25 AMOUNT: $225.00\nTRUCK#\nTRAILER#\nCASE COUNT:\nTRIP#\nFACILITY: Demoulas / Market Basket\nLOCATION: <PERSON>, MA\nLUMPER NAME: <PERSON>\nSIGNATURE:\nW\n7\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}