{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 2.6250435780639236e-07, "Y": 1.0}]}, "Id": "459bf49e-4ff1-4212-8384-77b55f4c4a70", "Relationships": [{"Type": "CHILD", "Ids": ["82b77862-5198-486a-b87c-bb75556e3930", "67300717-a451-44ec-88ed-4205eb66557d", "1e8d9792-f7d7-4fcb-a53a-3f3c51f1744d", "41033d14-684b-4993-bcc3-eb01c179cecc", "ff37a151-da30-418f-9858-de56c5bcc11f", "89f70059-cb9e-4ca8-b22e-2a1113121923", "7a7b2e55-4a27-4ca2-9215-9d21c43b4904", "72d40c71-9468-47ff-acc4-6950e40e2233", "1bee0472-aa30-4062-bae5-456691cfafa8", "95ae5111-9c63-472e-8df9-b61fcac5e873", "931bf92a-f9eb-470f-8ccc-6e6cff7203a1", "64f259bd-4fa3-4f97-85e9-69f51047b334", "d916e746-6908-49c8-8209-fd5884a65cc8", "94b4cb98-6a84-4dcd-a580-02816a62458c", "658ce953-ec54-47c3-a004-eca85e3a5069", "7bef821f-d2f2-46e9-b0e9-eff295be7144", "d97a2a15-c6ee-4067-8e85-ee9fb5fedf44"]}]}, {"BlockType": "LINE", "Confidence": 99.80741119384766, "Text": "Intermodal Gate Receipt Outgate 2023-02-07 14:49", "Geometry": {"BoundingBox": {"Width": 0.44195079803466797, "Height": 0.08915729075670242, "Left": 0.027166593819856644, "Top": 0.1257755607366562}, "Polygon": [{"X": 0.027166593819856644, "Y": 0.12580139935016632}, {"X": 0.4691160023212433, "Y": 0.1257755607366562}, {"X": 0.4691174030303955, "Y": 0.21490779519081116}, {"X": 0.027168553322553635, "Y": 0.21493284404277802}]}, "Id": "82b77862-5198-486a-b87c-bb75556e3930", "Relationships": [{"Type": "CHILD", "Ids": ["e9c8b89e-cb47-4c22-bd6a-f1bc8b8811f1", "655bb2d2-4887-46ec-bf2f-7de38917d527", "1866ef68-aec0-4852-916e-1273717f6792", "24f514f2-5301-42af-8d12-b929d9dca639", "2e57b5b1-0a06-449f-a99a-a55b4fb9ae77", "2f7005c2-a67c-4c99-b874-296fc92be771"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Equipment Type", "Geometry": {"BoundingBox": {"Width": 0.1353171169757843, "Height": 0.09234867990016937, "Left": 0.023703308776021004, "Top": 0.4145963788032532}, "Polygon": [{"X": 0.023703308776021004, "Y": 0.41460347175598145}, {"X": 0.15901857614517212, "Y": 0.4145963788032532}, {"X": 0.15902042388916016, "Y": 0.5069381594657898}, {"X": 0.023705344647169113, "Y": 0.5069450736045837}]}, "Id": "67300717-a451-44ec-88ed-4205eb66557d", "Relationships": [{"Type": "CHILD", "Ids": ["ed269d3f-59f2-4c91-b132-80d3e7563d78", "28ef65a8-e6bf-44bb-b552-f036cedbdc04"]}]}, {"BlockType": "LINE", "Confidence": 89.04914855957031, "Text": "C", "Geometry": {"BoundingBox": {"Width": 0.014931438490748405, "Height": 0.07590273022651672, "Left": 0.19431725144386292, "Top": 0.4149669110774994}, "Polygon": [{"X": 0.19431725144386292, "Y": 0.41496768593788147}, {"X": 0.20924721658229828, "Y": 0.4149669110774994}, {"X": 0.20924869179725647, "Y": 0.49086886644363403}, {"X": 0.1943187415599823, "Y": 0.4908696115016937}]}, "Id": "1e8d9792-f7d7-4fcb-a53a-3f3c51f1744d", "Relationships": [{"Type": "CHILD", "Ids": ["c2ba0290-1f38-4365-967a-b68b264d07f6"]}]}, {"BlockType": "LINE", "Confidence": 99.61196899414062, "Text": "L/E", "Geometry": {"BoundingBox": {"Width": 0.02937070094048977, "Height": 0.07494915276765823, "Left": 0.5434074401855469, "Top": 0.41540709137916565}, "Polygon": [{"X": 0.5434074401855469, "Y": 0.4154086112976074}, {"X": 0.5727770924568176, "Y": 0.41540709137916565}, {"X": 0.5727781653404236, "Y": 0.4903547465801239}, {"X": 0.5434085726737976, "Y": 0.4903562366962433}]}, "Id": "41033d14-684b-4993-bcc3-eb01c179cecc", "Relationships": [{"Type": "CHILD", "Ids": ["d70f3d50-06a7-4c3c-958c-3b574808c17f"]}]}, {"BlockType": "LINE", "Confidence": 99.41246795654297, "Text": "L", "Geometry": {"BoundingBox": {"Width": 0.011561802588403225, "Height": 0.07061076909303665, "Left": 0.7159417271614075, "Top": 0.4184122085571289}, "Polygon": [{"X": 0.7159417271614075, "Y": 0.41841280460357666}, {"X": 0.727502703666687, "Y": 0.4184122085571289}, {"X": 0.7275035381317139, "Y": 0.4890223741531372}, {"X": 0.7159426212310791, "Y": 0.48902297019958496}]}, "Id": "ff37a151-da30-418f-9858-de56c5bcc11f", "Relationships": [{"Type": "CHILD", "Ids": ["8b04dd7f-7ecc-4685-a5ac-4e1644e1b457"]}]}, {"BlockType": "LINE", "Confidence": 99.94589233398438, "Text": "Unit Initial", "Geometry": {"BoundingBox": {"Width": 0.08252846449613571, "Height": 0.07290656119585037, "Left": 0.023377513512969017, "Top": 0.5780459046363831}, "Polygon": [{"X": 0.023377513512969017, "Y": 0.5780499577522278}, {"X": 0.1059044599533081, "Y": 0.5780459046363831}, {"X": 0.10590597987174988, "Y": 0.6509485244750977}, {"X": 0.023379119113087654, "Y": 0.6509524583816528}]}, "Id": "89f70059-cb9e-4ca8-b22e-2a1113121923", "Relationships": [{"Type": "CHILD", "Ids": ["54f48fa9-219e-4531-babe-ce925f30b148", "4cddb76d-1565-45ce-9ede-e2c21e491416"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "EGHU", "Geometry": {"BoundingBox": {"Width": 0.054449696093797684, "Height": 0.07456839829683304, "Left": 0.19487372040748596, "Top": 0.5771324634552002}, "Polygon": [{"X": 0.19487372040748596, "Y": 0.5771351456642151}, {"X": 0.24932201206684113, "Y": 0.5771324634552002}, {"X": 0.24932341277599335, "Y": 0.6516982913017273}, {"X": 0.19487518072128296, "Y": 0.6517009139060974}]}, "Id": "7a7b2e55-4a27-4ca2-9215-9d21c43b4904", "Relationships": [{"Type": "CHILD", "Ids": ["586851ad-6e69-435a-9541-d29082362153"]}]}, {"BlockType": "LINE", "Confidence": 97.099609375, "Text": "Unit #", "Geometry": {"BoundingBox": {"Width": 0.05064675956964493, "Height": 0.07334990054368973, "Left": 0.5436804890632629, "Top": 0.5784773826599121}, "Polygon": [{"X": 0.5436804890632629, "Y": 0.5784798264503479}, {"X": 0.5943261981010437, "Y": 0.5784773826599121}, {"X": 0.5943272709846497, "Y": 0.6518248319625854}, {"X": 0.5436815619468689, "Y": 0.6518272757530212}]}, "Id": "72d40c71-9468-47ff-acc4-6950e40e2233", "Relationships": [{"Type": "CHILD", "Ids": ["ab526bbd-0de2-4468-8bbd-227143d11655", "01877351-5eff-4d55-a40e-b54b2d421cb4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "926272", "Geometry": {"BoundingBox": {"Width": 0.06334324181079865, "Height": 0.07382965832948685, "Left": 0.7152725458145142, "Top": 0.578279435634613}, "Polygon": [{"X": 0.7152725458145142, "Y": 0.5782825946807861}, {"X": 0.7786149978637695, "Y": 0.578279435634613}, {"X": 0.7786158323287964, "Y": 0.6521061062812805}, {"X": 0.7152734994888306, "Y": 0.6521090865135193}]}, "Id": "1bee0472-aa30-4062-bae5-456691cfafa8", "Relationships": [{"Type": "CHILD", "Ids": ["576fc03a-b4bd-41ca-ac7b-dc89dcc3f554"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.11552809178829193, "Height": 0.0744151696562767, "Left": 0.023064926266670227, "Top": 0.7380905747413635}, "Polygon": [{"X": 0.023064926266670227, "Y": 0.7380958795547485}, {"X": 0.13859149813652039, "Y": 0.7380905747413635}, {"X": 0.13859301805496216, "Y": 0.8125005960464478}, {"X": 0.0230665672570467, "Y": 0.8125057220458984}]}, "Id": "95ae5111-9c63-472e-8df9-b61fcac5e873", "Relationships": [{"Type": "CHILD", "Ids": ["01f35b3e-1565-440f-a2d0-facf1cbbf02e", "0cb3535a-112a-4636-ada7-bf8a943b93b8"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "NAPZ", "Geometry": {"BoundingBox": {"Width": 0.050890225917100906, "Height": 0.07139606773853302, "Left": 0.19478870928287506, "Top": 0.739068865776062}, "Polygon": [{"X": 0.19478870928287506, "Y": 0.739071249961853}, {"X": 0.24567757546901703, "Y": 0.739068865776062}, {"X": 0.24567893147468567, "Y": 0.8104626536369324}, {"X": 0.1947900950908661, "Y": 0.8104649782180786}]}, "Id": "931bf92a-f9eb-470f-8ccc-6e6cff7203a1", "Relationships": [{"Type": "CHILD", "Ids": ["bfe2300f-a192-42ef-92d4-81c17c099701"]}]}, {"BlockType": "LINE", "Confidence": 98.359375, "Text": "Chassis #", "Geometry": {"BoundingBox": {"Width": 0.08423570543527603, "Height": 0.07229047268629074, "Left": 0.5431575775146484, "Top": 0.7387927174568176}, "Polygon": [{"X": 0.5431575775146484, "Y": 0.738796591758728}, {"X": 0.6273922920227051, "Y": 0.7387927174568176}, {"X": 0.6273932456970215, "Y": 0.8110794425010681}, {"X": 0.5431585907936096, "Y": 0.811083197593689}]}, "Id": "64f259bd-4fa3-4f97-85e9-69f51047b334", "Relationships": [{"Type": "CHILD", "Ids": ["a14d0401-a4c3-41f4-a345-45ecb299b404", "5172fc85-01f8-447b-a856-2be508822736"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "185525", "Geometry": {"BoundingBox": {"Width": 0.061549991369247437, "Height": 0.07241547852754593, "Left": 0.7169332504272461, "Top": 0.7406144738197327}, "Polygon": [{"X": 0.7169332504272461, "Y": 0.7406173348426819}, {"X": 0.7784824371337891, "Y": 0.7406144738197327}, {"X": 0.7784832715988159, "Y": 0.8130272030830383}, {"X": 0.7169341444969177, "Y": 0.813029944896698}]}, "Id": "d916e746-6908-49c8-8209-fd5884a65cc8", "Relationships": [{"Type": "CHILD", "Ids": ["cd280db9-ae16-4d70-b67e-c1dac3129454"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Length", "Geometry": {"BoundingBox": {"Width": 0.05752940848469734, "Height": 0.08578422665596008, "Left": 0.023408180102705956, "Top": 0.9031063914299011}, "Polygon": [{"X": 0.023408180102705956, "Y": 0.9031088352203369}, {"X": 0.0809357687830925, "Y": 0.9031063914299011}, {"X": 0.08093758672475815, "Y": 0.9888882637023926}, {"X": 0.023410070687532425, "Y": 0.9888905882835388}]}, "Id": "94b4cb98-6a84-4dcd-a580-02816a62458c", "Relationships": [{"Type": "CHILD", "Ids": ["df5ce4a9-d397-4092-bd85-ac8ce111cd8f"]}]}, {"BlockType": "LINE", "Confidence": 99.49238586425781, "Text": "40'", "Geometry": {"BoundingBox": {"Width": 0.025806771591305733, "Height": 0.0698472261428833, "Left": 0.1942209005355835, "Top": 0.90297532081604}, "Polygon": [{"X": 0.1942209005355835, "Y": 0.9029764533042908}, {"X": 0.22002632915973663, "Y": 0.90297532081604}, {"X": 0.22002767026424408, "Y": 0.9728214740753174}, {"X": 0.19422227144241333, "Y": 0.9728225469589233}]}, "Id": "658ce953-ec54-47c3-a004-eca85e3a5069", "Relationships": [{"Type": "CHILD", "Ids": ["f22ad2a9-1af2-4a17-814a-d2a70ad32d38"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Seal", "Geometry": {"BoundingBox": {"Width": 0.03919746354222298, "Height": 0.07223936915397644, "Left": 0.5437790155410767, "Top": 0.9009342789649963}, "Polygon": [{"X": 0.5437790155410767, "Y": 0.90093594789505}, {"X": 0.582975447177887, "Y": 0.9009342789649963}, {"X": 0.5829765200614929, "Y": 0.9731720685958862}, {"X": 0.5437800884246826, "Y": 0.9731736779212952}]}, "Id": "7bef821f-d2f2-46e9-b0e9-eff295be7144", "Relationships": [{"Type": "CHILD", "Ids": ["b72fa124-e165-4aca-9395-6eb15162f60e"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "EMCNUY7952", "Geometry": {"BoundingBox": {"Width": 0.12251462787389755, "Height": 0.07106944173574448, "Left": 0.7164345979690552, "Top": 0.9026058912277222}, "Polygon": [{"X": 0.7164345979690552, "Y": 0.9026110768318176}, {"X": 0.8389484286308289, "Y": 0.9026058912277222}, {"X": 0.8389492034912109, "Y": 0.973670244216919}, {"X": 0.716435432434082, "Y": 0.9736753106117249}]}, "Id": "d97a2a15-c6ee-4067-8e85-ee9fb5fedf44", "Relationships": [{"Type": "CHILD", "Ids": ["823ea8e4-ab88-4b62-9931-6d88f45ed5cb"]}]}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Intermodal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09579207003116608, "Height": 0.07242342829704285, "Left": 0.027166612446308136, "Top": 0.12661267817020416}, "Polygon": [{"X": 0.027166612446308136, "Y": 0.12661828100681305}, {"X": 0.12295718491077423, "Y": 0.12661267817020416}, {"X": 0.12295867502689362, "Y": 0.19903065264225006}, {"X": 0.02716820314526558, "Y": 0.199036106467247}]}, "Id": "e9c8b89e-cb47-4c22-bd6a-f1bc8b8811f1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0420047827064991, "Height": 0.07367997616529465, "Left": 0.12686467170715332, "Top": 0.12579311430454254}, "Polygon": [{"X": 0.12686467170715332, "Y": 0.12579557299613953}, {"X": 0.16886799037456512, "Y": 0.12579311430454254}, {"X": 0.16886945068836212, "Y": 0.19947069883346558}, {"X": 0.1268661916255951, "Y": 0.19947309792041779}]}, "Id": "655bb2d2-4887-46ec-bf2f-7de38917d527"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0690782442688942, "Height": 0.08766095340251923, "Left": 0.17321272194385529, "Top": 0.12619009613990784}, "Polygon": [{"X": 0.17321272194385529, "Y": 0.12619413435459137}, {"X": 0.24228930473327637, "Y": 0.12619009613990784}, {"X": 0.24229095876216888, "Y": 0.21384713053703308}, {"X": 0.17321446537971497, "Y": 0.21385104954242706}]}, "Id": "1866ef68-aec0-4852-916e-1273717f6792"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Outgate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07120011746883392, "Height": 0.08848374336957932, "Left": 0.24539104104042053, "Top": 0.1264367401599884}, "Polygon": [{"X": 0.24539104104042053, "Y": 0.1264408975839615}, {"X": 0.3165895640850067, "Y": 0.1264367401599884}, {"X": 0.31659117341041565, "Y": 0.21491645276546478}, {"X": 0.24539270997047424, "Y": 0.21492047607898712}]}, "Id": "24f514f2-5301-42af-8d12-b929d9dca639"}, {"BlockType": "WORD", "Confidence": 99.560546875, "Text": "2023-02-07", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09581591933965683, "Height": 0.07023462653160095, "Left": 0.3205566108226776, "Top": 0.12800805270671844}, "Polygon": [{"X": 0.3205566108226776, "Y": 0.12801365554332733}, {"X": 0.4163713753223419, "Y": 0.12800805270671844}, {"X": 0.41637250781059265, "Y": 0.19823722541332245}, {"X": 0.3205578625202179, "Y": 0.1982426792383194}]}, "Id": "2e57b5b1-0a06-449f-a99a-a55b4fb9ae77"}, {"BlockType": "WORD", "Confidence": 99.41326141357422, "Text": "14:49", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048173073679208755, "Height": 0.0686359852552414, "Left": 0.42094406485557556, "Top": 0.1292201727628708}, "Polygon": [{"X": 0.42094406485557556, "Y": 0.12922298908233643}, {"X": 0.46911606192588806, "Y": 0.1292201727628708}, {"X": 0.469117134809494, "Y": 0.19785341620445251}, {"X": 0.4209451675415039, "Y": 0.19785615801811218}]}, "Id": "2f7005c2-a67c-4c99-b874-296fc92be771"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09011196345090866, "Height": 0.09015024453401566, "Left": 0.023703332990407944, "Top": 0.41563284397125244}, "Polygon": [{"X": 0.023703332990407944, "Y": 0.4156375825405121}, {"X": 0.11381342262029648, "Y": 0.41563284397125244}, {"X": 0.1138152927160263, "Y": 0.5057785511016846}, {"X": 0.023705318570137024, "Y": 0.5057830810546875}]}, "Id": "ed269d3f-59f2-4c91-b132-80d3e7563d78"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04230809956789017, "Height": 0.09234396368265152, "Left": 0.11671233177185059, "Top": 0.4145963788032532}, "Polygon": [{"X": 0.11671233177185059, "Y": 0.41459858417510986}, {"X": 0.15901857614517212, "Y": 0.4145963788032532}, {"X": 0.15902042388916016, "Y": 0.5069381594657898}, {"X": 0.116714246571064, "Y": 0.5069403052330017}]}, "Id": "28ef65a8-e6bf-44bb-b552-f036cedbdc04"}, {"BlockType": "WORD", "Confidence": 89.04914855957031, "Text": "C", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014931438490748405, "Height": 0.07590273022651672, "Left": 0.19431725144386292, "Top": 0.4149669110774994}, "Polygon": [{"X": 0.19431725144386292, "Y": 0.41496768593788147}, {"X": 0.20924721658229828, "Y": 0.4149669110774994}, {"X": 0.20924869179725647, "Y": 0.49086886644363403}, {"X": 0.1943187415599823, "Y": 0.4908696115016937}]}, "Id": "c2ba0290-1f38-4365-967a-b68b264d07f6"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "L/E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02937070094048977, "Height": 0.07494915276765823, "Left": 0.5434074401855469, "Top": 0.41540709137916565}, "Polygon": [{"X": 0.5434074401855469, "Y": 0.4154086112976074}, {"X": 0.5727770924568176, "Y": 0.41540709137916565}, {"X": 0.5727781653404236, "Y": 0.4903547465801239}, {"X": 0.5434085726737976, "Y": 0.4903562366962433}]}, "Id": "d70f3d50-06a7-4c3c-958c-3b574808c17f"}, {"BlockType": "WORD", "Confidence": 99.41246795654297, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011561802588403225, "Height": 0.07061076909303665, "Left": 0.7159417271614075, "Top": 0.4184122085571289}, "Polygon": [{"X": 0.7159417271614075, "Y": 0.41841280460357666}, {"X": 0.727502703666687, "Y": 0.4184122085571289}, {"X": 0.7275035381317139, "Y": 0.4890223741531372}, {"X": 0.7159426212310791, "Y": 0.48902297019958496}]}, "Id": "8b04dd7f-7ecc-4685-a5ac-4e1644e1b457"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03459545224905014, "Height": 0.07290419936180115, "Left": 0.023377513512969017, "Top": 0.5780482292175293}, "Polygon": [{"X": 0.023377513512969017, "Y": 0.5780499577522278}, {"X": 0.057971395552158356, "Y": 0.5780482292175293}, {"X": 0.05797296762466431, "Y": 0.6509507894515991}, {"X": 0.023379119113087654, "Y": 0.6509524583816528}]}, "Id": "54f48fa9-219e-4531-babe-ce925f30b148"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Initial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04440786689519882, "Height": 0.07219883799552917, "Left": 0.06149810925126076, "Top": 0.5786901712417603}, "Polygon": [{"X": 0.06149810925126076, "Y": 0.5786923766136169}, {"X": 0.1059044674038887, "Y": 0.5786901712417603}, {"X": 0.10590597987174988, "Y": 0.6508868932723999}, {"X": 0.061499662697315216, "Y": 0.6508890390396118}]}, "Id": "4cddb76d-1565-45ce-9ede-e2c21e491416"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "EGHU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054449696093797684, "Height": 0.07456839829683304, "Left": 0.19487372040748596, "Top": 0.5771324634552002}, "Polygon": [{"X": 0.19487372040748596, "Y": 0.5771351456642151}, {"X": 0.24932201206684113, "Y": 0.5771324634552002}, {"X": 0.24932341277599335, "Y": 0.6516982913017273}, {"X": 0.19487518072128296, "Y": 0.6517009139060974}]}, "Id": "586851ad-6e69-435a-9541-d29082362153"}, {"BlockType": "WORD", "Confidence": 99.169921875, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03558960184454918, "Height": 0.07309123128652573, "Left": 0.5436804890632629, "Top": 0.5787360072135925}, "Polygon": [{"X": 0.5436804890632629, "Y": 0.5787377953529358}, {"X": 0.5792690515518188, "Y": 0.5787360072135925}, {"X": 0.57927006483078, "Y": 0.6518255472183228}, {"X": 0.5436815619468689, "Y": 0.6518272757530212}]}, "Id": "ab526bbd-0de2-4468-8bbd-227143d11655"}, {"BlockType": "WORD", "Confidence": 95.029296875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012910384684801102, "Height": 0.07145126163959503, "Left": 0.5814168453216553, "Top": 0.5784773826599121}, "Polygon": [{"X": 0.5814168453216553, "Y": 0.5784779787063599}, {"X": 0.5943261981010437, "Y": 0.5784773826599121}, {"X": 0.5943272113800049, "Y": 0.6499279737472534}, {"X": 0.5814178586006165, "Y": 0.649928629398346}]}, "Id": "01877351-5eff-4d55-a40e-b54b2d421cb4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "926272", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06334324181079865, "Height": 0.07382965832948685, "Left": 0.7152725458145142, "Top": 0.578279435634613}, "Polygon": [{"X": 0.7152725458145142, "Y": 0.5782825946807861}, {"X": 0.7786149978637695, "Y": 0.578279435634613}, {"X": 0.7786158323287964, "Y": 0.6521061062812805}, {"X": 0.7152734994888306, "Y": 0.6521090865135193}]}, "Id": "576fc03a-b4bd-41ca-ac7b-dc89dcc3f554"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06702529639005661, "Height": 0.07441294193267822, "Left": 0.023064926266670227, "Top": 0.738092839717865}, "Polygon": [{"X": 0.023064926266670227, "Y": 0.7380958795547485}, {"X": 0.09008865058422089, "Y": 0.738092839717865}, {"X": 0.09009022265672684, "Y": 0.8125027418136597}, {"X": 0.0230665672570467, "Y": 0.8125057220458984}]}, "Id": "01f35b3e-1565-440f-a2d0-facf1cbbf02e"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Initial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04405556991696358, "Height": 0.0714300274848938, "Left": 0.09453742206096649, "Top": 0.7398585081100464}, "Polygon": [{"X": 0.09453742206096649, "Y": 0.7398605346679688}, {"X": 0.13859152793884277, "Y": 0.7398585081100464}, {"X": 0.13859298825263977, "Y": 0.8112865686416626}, {"X": 0.09453891962766647, "Y": 0.8112885355949402}]}, "Id": "0cb3535a-112a-4636-ada7-bf8a943b93b8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "NAPZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050890225917100906, "Height": 0.07139606773853302, "Left": 0.19478870928287506, "Top": 0.739068865776062}, "Polygon": [{"X": 0.19478870928287506, "Y": 0.739071249961853}, {"X": 0.24567757546901703, "Y": 0.739068865776062}, {"X": 0.24567893147468567, "Y": 0.8104626536369324}, {"X": 0.1947900950908661, "Y": 0.8104649782180786}]}, "Id": "bfe2300f-a192-42ef-92d4-81c17c099701"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06868012249469757, "Height": 0.07228975743055344, "Left": 0.5431575775146484, "Top": 0.7387934327125549}, "Polygon": [{"X": 0.5431575775146484, "Y": 0.738796591758728}, {"X": 0.6118366718292236, "Y": 0.7387934327125549}, {"X": 0.6118376851081848, "Y": 0.8110800981521606}, {"X": 0.5431585907936096, "Y": 0.811083197593689}]}, "Id": "a14d0401-a4c3-41f4-a345-45ecb299b404"}, {"BlockType": "WORD", "Confidence": 96.826171875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013314595445990562, "Height": 0.06974641978740692, "Left": 0.6140786409378052, "Top": 0.7405375838279724}, "Polygon": [{"X": 0.6140786409378052, "Y": 0.7405381798744202}, {"X": 0.6273922920227051, "Y": 0.7405375838279724}, {"X": 0.6273932456970215, "Y": 0.8102834224700928}, {"X": 0.6140795946121216, "Y": 0.8102839589118958}]}, "Id": "5172fc85-01f8-447b-a856-2be508822736"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "185525", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061549991369247437, "Height": 0.07241547852754593, "Left": 0.7169332504272461, "Top": 0.7406144738197327}, "Polygon": [{"X": 0.7169332504272461, "Y": 0.7406173348426819}, {"X": 0.7784824371337891, "Y": 0.7406144738197327}, {"X": 0.7784832715988159, "Y": 0.8130272030830383}, {"X": 0.7169341444969177, "Y": 0.813029944896698}]}, "Id": "cd280db9-ae16-4d70-b67e-c1dac3129454"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Length", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05752940848469734, "Height": 0.08578422665596008, "Left": 0.023408180102705956, "Top": 0.9031063914299011}, "Polygon": [{"X": 0.023408180102705956, "Y": 0.9031088352203369}, {"X": 0.0809357687830925, "Y": 0.9031063914299011}, {"X": 0.08093758672475815, "Y": 0.9888882637023926}, {"X": 0.023410070687532425, "Y": 0.9888905882835388}]}, "Id": "df5ce4a9-d397-4092-bd85-ac8ce111cd8f"}, {"BlockType": "WORD", "Confidence": 99.49238586425781, "Text": "40'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025806771591305733, "Height": 0.0698472261428833, "Left": 0.1942209005355835, "Top": 0.90297532081604}, "Polygon": [{"X": 0.1942209005355835, "Y": 0.9029764533042908}, {"X": 0.22002632915973663, "Y": 0.90297532081604}, {"X": 0.22002767026424408, "Y": 0.9728214740753174}, {"X": 0.19422227144241333, "Y": 0.9728225469589233}]}, "Id": "f22ad2a9-1af2-4a17-814a-d2a70ad32d38"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Seal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03919746354222298, "Height": 0.07223936915397644, "Left": 0.5437790155410767, "Top": 0.9009342789649963}, "Polygon": [{"X": 0.5437790155410767, "Y": 0.90093594789505}, {"X": 0.582975447177887, "Y": 0.9009342789649963}, {"X": 0.5829765200614929, "Y": 0.9731720685958862}, {"X": 0.5437800884246826, "Y": 0.9731736779212952}]}, "Id": "b72fa124-e165-4aca-9395-6eb15162f60e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "EMCNUY7952", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12251462787389755, "Height": 0.07106944173574448, "Left": 0.7164345979690552, "Top": 0.9026058912277222}, "Polygon": [{"X": 0.7164345979690552, "Y": 0.9026110768318176}, {"X": 0.8389484286308289, "Y": 0.9026058912277222}, {"X": 0.8389492034912109, "Y": 0.973670244216919}, {"X": 0.716435432434082, "Y": 0.9736753106117249}]}, "Id": "823ea8e4-ab88-4b62-9931-6d88f45ed5cb"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "52a9748c-8612-4009-a2db-049ef54e3425", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "52a9748c-8612-4009-a2db-049ef54e3425", "content-type": "application/x-amz-json-1.1", "content-length": "23255", "date": "Thu, 18 Sep 2025 08:00:15 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "d94060b3-20cd-487b-aa76-774b46db8b92", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:00:18 GMT", "content-type": "application/json", "content-length": "1032", "connection": "keep-alive", "x-amzn-requestid": "d94060b3-20cd-487b-aa76-774b46db8b92"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page content: \"Intermodal Gate Receipt Outgate 2023-02-07 14:49\". That seems to be an outgate or ingate? The header says \"Intermodal Gate Receipt Outgate\". So it's a gate receipt outgate. That matches \"outgate\" document type. The doc type is \"outgate\". There's keywords: Outgate, Gate Out. So we classify as outgate.\n\nAlso note: \"Intermodal Gate Receipt Outgate 2023-02-07 14:49\" indicates it's an outgate receipt. So doc_type = outgate.\n\nWe need to output via the tool call classify_logistics_doc_type with documents list containing one object: page_no = 1, doc_type = \"outgate\".\n\nSo produce tool call."}}}, {"toolUse": {"toolUseId": "tooluse_q72GNNvrQIKcmzHNGuwt7w", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2153, "outputTokens": 200, "totalTokens": 2353}, "metrics": {"latencyMs": 965}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/fcf74ff8_26_outgate_12.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nIntermodal Gate Receipt Outgate 2023-02-07 14:49\nEquipment Type\nC\nL/E\nL\nUnit Initial\nEGHU\nUnit #\n926272\nChassis Initial\nNAPZ\nChassis #\n185525\nLength\n40'\nSeal\nEMCNUY7952\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}