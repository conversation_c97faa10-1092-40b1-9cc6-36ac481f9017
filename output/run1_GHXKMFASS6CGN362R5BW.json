{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 8.277499574660396e-08}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "201e3d31-ec4d-4f6e-9561-975406dd6466", "Relationships": [{"Type": "CHILD", "Ids": ["deb1c38b-b6d6-4deb-a5a5-ab36d8f857af", "09a28a1d-cdfa-460d-8f39-61188662cd0e", "0d865b85-5234-4447-8afa-fdfb03bf867f", "88a4a990-9b4c-4d02-89cb-d86d19414696", "1d9cf16d-5622-4c63-bcca-dc04458276c3", "804147b6-84c8-416c-8a01-c8b6f72b2fd4", "6156e4d2-73a2-4aea-a97a-a6eed9b9eb67", "19031288-5f9a-4ff6-9bf3-098fb106870d", "648b7f2a-670f-4c0d-a673-3e71ca911bff", "3c5d5d58-8854-473b-90db-d6c1cdf603b5", "a5241ee6-0de9-4a81-9fac-c469b139a309", "365638e9-fd6e-4abc-947a-ee94d595d632", "7711cce1-728d-4596-9e53-8f4bc9cc45c0", "b1c7d656-c974-42dc-9a2b-adef681e40c8", "1c07e406-d161-4720-9764-d162d1a9dae2", "d141c988-6370-44c7-ae71-bb11c72d1355", "05867d16-4dfb-4ad7-8020-2422109104ae", "ae94666c-fc36-4120-896b-81ddcac1f99c", "ec6b902b-ee9b-4ef2-8239-a86ae811ccad", "64fbc893-921e-4d69-9f59-fdf39ea6610e", "66b07a52-5a9d-4f87-8e6e-d6bead58e1b5", "db0ff834-f7fa-40f2-8e6d-bac030823c5b", "15baee67-c757-4efc-93ab-1d447cb91b6d", "8b983d26-1853-426c-bd1e-f9f5f788306f", "1d1c3596-cbe5-4456-802b-ae3058d73841", "a318b213-51c0-4d84-82a0-7ed37aa999ae", "6efc51a0-374c-479b-9326-d95a470efc18", "78d855ed-cb74-4120-a157-b873f5b4f837"]}]}, {"BlockType": "LINE", "Confidence": 54.34251403808594, "Text": "[csx", "Geometry": {"BoundingBox": {"Width": 0.0953437015414238, "Height": 0.032856687903404236, "Left": 0.031788069754838943, "Top": 0.02382233366370201}, "Polygon": [{"X": 0.03178844228386879, "Y": 0.02382233366370201}, {"X": 0.12713177502155304, "Y": 0.023822860792279243}, {"X": 0.1271313577890396, "Y": 0.05667902156710625}, {"X": 0.031788069754838943, "Y": 0.05667850002646446}]}, "Id": "deb1c38b-b6d6-4deb-a5a5-ab36d8f857af", "Relationships": [{"Type": "CHILD", "Ids": ["79ddee84-a326-4210-a3bc-9de3d94287e7"]}]}, {"BlockType": "LINE", "Confidence": 92.65884399414062, "Text": "]", "Geometry": {"BoundingBox": {"Width": 0.012050646357238293, "Height": 0.0324283204972744, "Left": 0.1400676965713501, "Top": 0.023275911808013916}, "Polygon": [{"X": 0.14006811380386353, "Y": 0.023275911808013916}, {"X": 0.15211834013462067, "Y": 0.02327597886323929}, {"X": 0.15211792290210724, "Y": 0.055704232305288315}, {"X": 0.1400676965713501, "Y": 0.05570416525006294}]}, "Id": "09a28a1d-cdfa-460d-8f39-61188662cd0e", "Relationships": [{"Type": "CHILD", "Ids": ["9be0b419-c082-47d0-b748-07e4b1d399a2"]}]}, {"BlockType": "LINE", "Confidence": 99.94466400146484, "Text": "J1 GATE RECEIPT", "Geometry": {"BoundingBox": {"Width": 0.18694548308849335, "Height": 0.012912421487271786, "Left": 0.17489652335643768, "Top": 0.02683756873011589}, "Polygon": [{"X": 0.174896702170372, "Y": 0.02683756873011589}, {"X": 0.36184200644493103, "Y": 0.026838602498173714}, {"X": 0.3618417978286743, "Y": 0.03974999114871025}, {"X": 0.17489652335643768, "Y": 0.03974895924329758}]}, "Id": "0d865b85-5234-4447-8afa-fdfb03bf867f", "Relationships": [{"Type": "CHILD", "Ids": ["8eae1862-7338-4c35-ab03-e48cda0dd7fe", "768644d8-b0ab-4873-9c72-ea70745a7ae4", "03a79719-eb9d-42a7-a045-b2740c0a2c5d"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Chicago 59th St, IL", "Geometry": {"BoundingBox": {"Width": 0.16936320066452026, "Height": 0.014379591681063175, "Left": 0.796782910823822, "Top": 0.02605413645505905}, "Polygon": [{"X": 0.7967832088470459, "Y": 0.02605413645505905}, {"X": 0.9661460518836975, "Y": 0.026055073365569115}, {"X": 0.9661456942558289, "Y": 0.04043372720479965}, {"X": 0.796782910823822, "Y": 0.04043279215693474}]}, "Id": "88a4a990-9b4c-4d02-89cb-d86d19414696", "Relationships": [{"Type": "CHILD", "Ids": ["ada63bdd-4889-43df-bad9-f96e60642609", "f1a354ab-6848-4786-b373-e92ebe82ad20", "638e64bc-f33b-47d4-a774-27638d76a980", "8533a448-d980-4c34-8c98-8c9b825f8d27"]}]}, {"BlockType": "LINE", "Confidence": 99.91883850097656, "Text": "For questions call, 1-877-SHIPCSX", "Geometry": {"BoundingBox": {"Width": 0.20361821353435516, "Height": 0.009714464657008648, "Left": 0.17518264055252075, "Top": 0.04818618297576904}, "Polygon": [{"X": 0.1751827746629715, "Y": 0.04818618297576904}, {"X": 0.3788008689880371, "Y": 0.04818730428814888}, {"X": 0.3788006901741028, "Y": 0.057900648564100266}, {"X": 0.17518264055252075, "Y": 0.05789953097701073}]}, "Id": "1d9cf16d-5622-4c63-bcca-dc04458276c3", "Relationships": [{"Type": "CHILD", "Ids": ["c52f97d6-f836-4295-98ef-c2596a2adc8e", "e4846f9b-c9c7-4fc3-8303-10c0d4d73437", "5dbc3020-3d5c-4e89-9d1f-c687f14f010f", "c90212b6-dd0c-4d3d-817b-279218c20a0d"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "MSDU 143108", "Geometry": {"BoundingBox": {"Width": 0.12847059965133667, "Height": 0.011120729148387909, "Left": 0.837049126625061, "Top": 0.053268034011125565}, "Polygon": [{"X": 0.8370494246482849, "Y": 0.053268034011125565}, {"X": 0.9655197262763977, "Y": 0.053268738090991974}, {"X": 0.9655194282531738, "Y": 0.06438875943422318}, {"X": 0.837049126625061, "Y": 0.06438805907964706}]}, "Id": "804147b6-84c8-416c-8a01-c8b6f72b2fd4", "Relationships": [{"Type": "CHILD", "Ids": ["864b6159-94a5-4d04-845f-acc5298c88f6", "e739d817-d094-4404-b8bd-f6920c2ff757"]}]}, {"BlockType": "LINE", "Confidence": 99.55406951904297, "Text": "PAGE 1", "Geometry": {"BoundingBox": {"Width": 0.041483353823423386, "Height": 0.007690343074500561, "Left": 0.03389347717165947, "Top": 0.07553796470165253}, "Polygon": [{"X": 0.033893562853336334, "Y": 0.07553796470165253}, {"X": 0.07537683099508286, "Y": 0.07553818821907043}, {"X": 0.07537674158811569, "Y": 0.08322830498218536}, {"X": 0.03389347717165947, "Y": 0.08322808146476746}]}, "Id": "6156e4d2-73a2-4aea-a97a-a6eed9b9eb67", "Relationships": [{"Type": "CHILD", "Ids": ["fc99d57f-aa72-4e98-baa0-3b314d3bd196", "5b5e8a92-1fe5-468f-a34c-b48630e31255"]}]}, {"BlockType": "LINE", "Confidence": 99.78655242919922, "Text": "020L 0000W 0857H", "Geometry": {"BoundingBox": {"Width": 0.11724918335676193, "Height": 0.007789273746311665, "Left": 0.8481223583221436, "Top": 0.07145019620656967}, "Polygon": [{"X": 0.8481225967407227, "Y": 0.07145019620656967}, {"X": 0.9653715491294861, "Y": 0.07145083695650101}, {"X": 0.9653713703155518, "Y": 0.07923947274684906}, {"X": 0.8481223583221436, "Y": 0.07923883199691772}]}, "Id": "19031288-5f9a-4ff6-9bf3-098fb106870d", "Relationships": [{"Type": "CHILD", "Ids": ["e82d4a17-73f5-4ee6-a6db-03a2c6dce6fa", "5eaff773-b9a9-401a-9504-9982a781ec1b", "c978e287-395b-464f-922d-b04bd86d0e07"]}]}, {"BlockType": "LINE", "Confidence": 99.93408203125, "Text": "J1 RECEIPT DETAILS (1)", "Geometry": {"BoundingBox": {"Width": 0.2010900229215622, "Height": 0.013451674021780491, "Left": 0.3989529609680176, "Top": 0.12046519666910172}, "Polygon": [{"X": 0.3989531695842743, "Y": 0.12046519666910172}, {"X": 0.600042998790741, "Y": 0.12046628445386887}, {"X": 0.6000427007675171, "Y": 0.13391686975955963}, {"X": 0.3989529609680176, "Y": 0.13391578197479248}]}, "Id": "648b7f2a-670f-4c0d-a673-3e71ca911bff", "Relationships": [{"Type": "CHILD", "Ids": ["ae860f28-89e5-4f1f-a484-36cd15e0808f", "688301ee-a11a-410a-933e-4a106ef80e1c", "d5bbd0e1-9176-4d92-9689-e4716536308f", "643a840a-0f91-4077-87ab-8efbe7dd4b54"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.050919145345687866, "Height": 0.0107437027618289, "Left": 0.04012782499194145, "Top": 0.15501870214939117}, "Polygon": [{"X": 0.0401279479265213, "Y": 0.15501870214939117}, {"X": 0.09104697406291962, "Y": 0.15501898527145386}, {"X": 0.09104683995246887, "Y": 0.16576240956783295}, {"X": 0.04012782499194145, "Y": 0.16576214134693146}]}, "Id": "3c5d5d58-8854-473b-90db-d6c1cdf603b5", "Relationships": [{"Type": "CHILD", "Ids": ["44d49fc6-1ee2-418c-bb7d-34036d68c6ea"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Associated", "Geometry": {"BoundingBox": {"Width": 0.07560575008392334, "Height": 0.008704077452421188, "Left": 0.18801763653755188, "Top": 0.154935821890831}, "Polygon": [{"X": 0.18801775574684143, "Y": 0.154935821890831}, {"X": 0.2636233866214752, "Y": 0.15493622422218323}, {"X": 0.2636232376098633, "Y": 0.16363990306854248}, {"X": 0.18801763653755188, "Y": 0.16363950073719025}]}, "Id": "a5241ee6-0de9-4a81-9fac-c469b139a309", "Relationships": [{"Type": "CHILD", "Ids": ["adb77c4f-3903-4517-8c8d-72a172ea8931"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "Load or", "Geometry": {"BoundingBox": {"Width": 0.05102875083684921, "Height": 0.008642204105854034, "Left": 0.4843212068080902, "Top": 0.1549380123615265}, "Polygon": [{"X": 0.48432135581970215, "Y": 0.1549380123615265}, {"X": 0.53534996509552, "Y": 0.15493829548358917}, {"X": 0.5353497862815857, "Y": 0.16358022391796112}, {"X": 0.4843212068080902, "Y": 0.16357995569705963}]}, "Id": "365638e9-fd6e-4abc-947a-ee94d595d632", "Relationships": [{"Type": "CHILD", "Ids": ["a2132b26-76dd-4642-8503-834477eff799", "41c9e581-312d-4566-9bc8-9452f3c8c46f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Seal(s)", "Geometry": {"BoundingBox": {"Width": 0.04679520055651665, "Height": 0.011002695187926292, "Left": 0.33602067828178406, "Top": 0.1605820208787918}, "Polygon": [{"X": 0.3360208570957184, "Y": 0.1605820208787918}, {"X": 0.3828158676624298, "Y": 0.1605822741985321}, {"X": 0.3828156888484955, "Y": 0.17158472537994385}, {"X": 0.33602067828178406, "Y": 0.17158447206020355}]}, "Id": "7711cce1-728d-4596-9e53-8f4bc9cc45c0", "Relationships": [{"Type": "CHILD", "Ids": ["e7f93c89-17e0-4696-b5f5-009c7c180bdc"]}]}, {"BlockType": "LINE", "Confidence": 99.82421875, "Text": "Booking #", "Geometry": {"BoundingBox": {"Width": 0.06804821640253067, "Height": 0.010799068957567215, "Left": 0.5985212326049805, "Top": 0.16091540455818176}, "Polygon": [{"X": 0.5985214710235596, "Y": 0.16091540455818176}, {"X": 0.6665694713592529, "Y": 0.16091576218605042}, {"X": 0.6665692329406738, "Y": 0.17171446979045868}, {"X": 0.5985212326049805, "Y": 0.17171411216259003}]}, "Id": "b1c7d656-c974-42dc-9a2b-adef681e40c8", "Relationships": [{"Type": "CHILD", "Ids": ["a4ad43a8-3159-47e6-bb66-dc577633caac", "5bd92a29-ab26-4b47-b433-a18ef635af2d"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Transportation Company", "Geometry": {"BoundingBox": {"Width": 0.16879312694072723, "Height": 0.010775611735880375, "Left": 0.7453497052192688, "Top": 0.15506026148796082}, "Polygon": [{"X": 0.7453499436378479, "Y": 0.15506026148796082}, {"X": 0.9141428470611572, "Y": 0.15506115555763245}, {"X": 0.9141425490379333, "Y": 0.16583587229251862}, {"X": 0.7453497052192688, "Y": 0.1658349633216858}]}, "Id": "1c07e406-d161-4720-9764-d162d1a9dae2", "Relationships": [{"Type": "CHILD", "Ids": ["772738fd-7a26-4568-924a-9815ca4061e2", "17eaeeda-f6d7-4865-b86b-9b9bd592f45c"]}]}, {"BlockType": "LINE", "Confidence": 99.87630462646484, "Text": "Date & Time", "Geometry": {"BoundingBox": {"Width": 0.08246390521526337, "Height": 0.008610490709543228, "Left": 0.04051117226481438, "Top": 0.16760988533496857}, "Polygon": [{"X": 0.04051126912236214, "Y": 0.16760988533496857}, {"X": 0.12297508120536804, "Y": 0.1676103174686432}, {"X": 0.12297496944665909, "Y": 0.1762203723192215}, {"X": 0.04051117226481438, "Y": 0.17621994018554688}]}, "Id": "d141c988-6370-44c7-ae71-bb11c72d1355", "Relationships": [{"Type": "CHILD", "Ids": ["7c55fb9f-ea7b-4315-9c27-d4a60badc4c1", "910e2c42-87d5-4d48-91a9-21403ea7c0a8", "d9ad47f4-360d-422a-9fdc-a4b1f14fc93f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Equipment", "Geometry": {"BoundingBox": {"Width": 0.07222753018140793, "Height": 0.010699338279664516, "Left": 0.18863140046596527, "Top": 0.16756883263587952}, "Polygon": [{"X": 0.18863153457641602, "Y": 0.16756883263587952}, {"X": 0.2608589231967926, "Y": 0.16756922006607056}, {"X": 0.26085877418518066, "Y": 0.178268164396286}, {"X": 0.18863140046596527, "Y": 0.17826777696609497}]}, "Id": "05867d16-4dfb-4ad7-8020-2422109104ae", "Relationships": [{"Type": "CHILD", "Ids": ["6a216b54-a4a1-4b06-be65-70b46fe8a4d5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Empty", "Geometry": {"BoundingBox": {"Width": 0.04202501103281975, "Height": 0.01073907408863306, "Left": 0.4844515919685364, "Top": 0.16767942905426025}, "Polygon": [{"X": 0.4844517707824707, "Y": 0.16767942905426025}, {"X": 0.5264766216278076, "Y": 0.16767965257167816}, {"X": 0.5264763832092285, "Y": 0.17841850221157074}, {"X": 0.4844515919685364, "Y": 0.17841827869415283}]}, "Id": "ae94666c-fc36-4120-896b-81ddcac1f99c", "Relationships": [{"Type": "CHILD", "Ids": ["da4b8b93-c648-422c-9cfb-fa8e4f9d7262"]}]}, {"BlockType": "LINE", "Confidence": 99.8486328125, "Text": "& Driver", "Geometry": {"BoundingBox": {"Width": 0.05339868366718292, "Height": 0.008619964122772217, "Left": 0.7460808157920837, "Top": 0.16759122908115387}, "Polygon": [{"X": 0.7460809946060181, "Y": 0.16759122908115387}, {"X": 0.7994794845581055, "Y": 0.16759151220321655}, {"X": 0.7994793057441711, "Y": 0.1762111932039261}, {"X": 0.7460808157920837, "Y": 0.1762109100818634}]}, "Id": "ec6b902b-ee9b-4ef2-8239-a86ae811ccad", "Relationships": [{"Type": "CHILD", "Ids": ["2002f2e3-2822-4aa3-85b4-f7eac784d470", "74152da9-c57f-4a84-afd6-6ddf0c95adf5"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "Out-Gate", "Geometry": {"BoundingBox": {"Width": 0.05277660861611366, "Height": 0.007833165116608143, "Left": 0.040394414216279984, "Top": 0.20010599493980408}, "Polygon": [{"X": 0.04039450362324715, "Y": 0.20010599493980408}, {"X": 0.09317102283239365, "Y": 0.20010626316070557}, {"X": 0.09317092597484589, "Y": 0.20793914794921875}, {"X": 0.040394414216279984, "Y": 0.20793887972831726}]}, "Id": "64fbc893-921e-4d69-9f59-fdf39ea6610e", "Relationships": [{"Type": "CHILD", "Ids": ["21cf0573-b988-4cf6-b668-085daa1d7c7b"]}]}, {"BlockType": "LINE", "Confidence": 99.5556640625, "Text": "MCCZ 341835", "Geometry": {"BoundingBox": {"Width": 0.08551188558340073, "Height": 0.007730578538030386, "Left": 0.1884511411190033, "Top": 0.20011895895004272}, "Polygon": [{"X": 0.18845124542713165, "Y": 0.20011895895004272}, {"X": 0.2739630341529846, "Y": 0.20011942088603973}, {"X": 0.27396291494369507, "Y": 0.20784954726696014}, {"X": 0.1884511411190033, "Y": 0.20784908533096313}]}, "Id": "66b07a52-5a9d-4f87-8e6e-d6bead58e1b5", "Relationships": [{"Type": "CHILD", "Ids": ["d129aa32-cc86-4ca9-9965-0c3650188af2", "1dcac4ce-731b-4267-ab45-9eade8f65b84"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "FX36673264", "Geometry": {"BoundingBox": {"Width": 0.07622420787811279, "Height": 0.007731626741588116, "Left": 0.3363126516342163, "Top": 0.20013010501861572}, "Polygon": [{"X": 0.33631277084350586, "Y": 0.20013010501861572}, {"X": 0.4125368595123291, "Y": 0.20013050734996796}, {"X": 0.41253674030303955, "Y": 0.2078617364168167}, {"X": 0.3363126516342163, "Y": 0.20786133408546448}]}, "Id": "db0ff834-f7fa-40f2-8e6d-bac030823c5b", "Relationships": [{"Type": "CHILD", "Ids": ["95485913-7384-44b3-a472-6583bd4195ba"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Load", "Geometry": {"BoundingBox": {"Width": 0.029214391484856606, "Height": 0.007818891666829586, "Left": 0.4841298460960388, "Top": 0.20001554489135742}, "Polygon": [{"X": 0.48412999510765076, "Y": 0.20001554489135742}, {"X": 0.5133442282676697, "Y": 0.20001570880413055}, {"X": 0.5133441090583801, "Y": 0.20783443748950958}, {"X": 0.4841298460960388, "Y": 0.20783428847789764}]}, "Id": "15baee67-c757-4efc-93ab-1d447cb91b6d", "Relationships": [{"Type": "CHILD", "Ids": ["d4464b91-8c72-4324-a547-3fb32d7b7227"]}]}, {"BlockType": "LINE", "Confidence": 91.9884033203125, "Text": "---", "Geometry": {"BoundingBox": {"Width": 0.011886738240718842, "Height": 0.0018588016973808408, "Left": 0.59744793176651, "Top": 0.20347531139850616}, "Polygon": [{"X": 0.5974479913711548, "Y": 0.20347531139850616}, {"X": 0.6093347072601318, "Y": 0.20347538590431213}, {"X": 0.6093346476554871, "Y": 0.2053341269493103}, {"X": 0.59744793176651, "Y": 0.20533405244350433}]}, "Id": "8b983d26-1853-426c-bd1e-f9f5f788306f", "Relationships": [{"Type": "CHILD", "Ids": ["8a09e591-8f13-40d5-b2f0-393dd2ee5686"]}]}, {"BlockType": "LINE", "Confidence": 99.90644836425781, "Text": "Desert Eagle Express Inc (DEEE)", "Geometry": {"BoundingBox": {"Width": 0.1910490244626999, "Height": 0.009959136135876179, "Left": 0.7462791204452515, "Top": 0.19968007504940033}, "Polygon": [{"X": 0.7462793588638306, "Y": 0.19968007504940033}, {"X": 0.9373281598091125, "Y": 0.19968107342720032}, {"X": 0.9373279213905334, "Y": 0.20963920652866364}, {"X": 0.7462791204452515, "Y": 0.20963819324970245}]}, "Id": "1d1c3596-cbe5-4456-802b-ae3058d73841", "Relationships": [{"Type": "CHILD", "Ids": ["1fa5bab4-69b8-4185-af57-cf3d784ff468", "a95189f5-a240-46f5-80d7-c0047e500a85", "62efc866-5e9d-4a3b-9af1-e37370f7da26", "c5ed03bb-034c-4fac-b1e4-a9e1c1604ae1", "cc3910ae-cc4a-45ab-a1fe-21e9d6bd4f5f"]}]}, {"BlockType": "LINE", "Confidence": 99.27206420898438, "Text": "08/23/25 14:46", "Geometry": {"BoundingBox": {"Width": 0.10003220289945602, "Height": 0.0078892158344388, "Left": 0.040291182696819305, "Top": 0.21117179095745087}, "Polygon": [{"X": 0.04029127210378647, "Y": 0.21117179095745087}, {"X": 0.14032338559627533, "Y": 0.21117231249809265}, {"X": 0.14032328128814697, "Y": 0.2190610021352768}, {"X": 0.040291182696819305, "Y": 0.219060480594635}]}, "Id": "a318b213-51c0-4d84-82a0-7ed37aa999ae", "Relationships": [{"Type": "CHILD", "Ids": ["f83c8709-f9ef-4283-a7c4-5ba091ead072", "c6d0dd2b-34fc-419a-80fd-09a3d0a043c3"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "<PERSON><PERSON><PERSON><PERSON>, Mohammed", "Geometry": {"BoundingBox": {"Width": 0.14547541737556458, "Height": 0.008892662823200226, "Left": 0.7463070154190063, "Top": 0.21111929416656494}, "Polygon": [{"X": 0.7463072538375854, "Y": 0.21111929416656494}, {"X": 0.8917824625968933, "Y": 0.21112006902694702}, {"X": 0.8917822241783142, "Y": 0.22001196444034576}, {"X": 0.7463070154190063, "Y": 0.22001120448112488}]}, "Id": "6efc51a0-374c-479b-9326-d95a470efc18", "Relationships": [{"Type": "CHILD", "Ids": ["7caca86a-d495-45a3-8eea-adb5a3a58af6", "7603c422-a947-45a7-b820-c9460e0960c1"]}]}, {"BlockType": "LINE", "Confidence": 99.97142791748047, "Text": "Damage is captured on recorded images at AGS locations", "Geometry": {"BoundingBox": {"Width": 0.3432459533214569, "Height": 0.009936961345374584, "Left": 0.03398407995700836, "Top": 0.2621892988681793}, "Polygon": [{"X": 0.033984195441007614, "Y": 0.2621892988681793}, {"X": 0.37723004817962646, "Y": 0.2621910870075226}, {"X": 0.37722986936569214, "Y": 0.2721262574195862}, {"X": 0.03398407995700836, "Y": 0.2721244990825653}]}, "Id": "78d855ed-cb74-4120-a157-b873f5b4f837", "Relationships": [{"Type": "CHILD", "Ids": ["63e3366b-a8d2-4202-ba27-4879a41077be", "f92b6397-4e9a-4f00-9621-a360c7c22bbc", "a6a6980f-b449-4800-84c1-1e8842469101", "11692843-03ff-46f4-ae03-e4f48964d8ac", "aafa9006-bc6a-4b75-b856-d4d6860bc3f1", "38a6933c-01cf-41a5-8207-fa7e3546a234", "5ce9ce4c-ca11-46df-9135-88765d56fcc0", "855fc146-048e-4729-bd53-c6b205e3b680", "dcac382c-fffe-4a39-8e5d-e44d1e906c19"]}]}, {"BlockType": "WORD", "Confidence": 54.34251403808594, "Text": "[csx", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0953437015414238, "Height": 0.032856687903404236, "Left": 0.031788069754838943, "Top": 0.02382233366370201}, "Polygon": [{"X": 0.03178844228386879, "Y": 0.02382233366370201}, {"X": 0.12713177502155304, "Y": 0.023822860792279243}, {"X": 0.1271313577890396, "Y": 0.05667902156710625}, {"X": 0.031788069754838943, "Y": 0.05667850002646446}]}, "Id": "79ddee84-a326-4210-a3bc-9de3d94287e7"}, {"BlockType": "WORD", "Confidence": 92.65884399414062, "Text": "]", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012050646357238293, "Height": 0.0324283204972744, "Left": 0.1400676965713501, "Top": 0.023275911808013916}, "Polygon": [{"X": 0.14006811380386353, "Y": 0.023275911808013916}, {"X": 0.15211834013462067, "Y": 0.02327597886323929}, {"X": 0.15211792290210724, "Y": 0.055704232305288315}, {"X": 0.1400676965713501, "Y": 0.05570416525006294}]}, "Id": "9be0b419-c082-47d0-b748-07e4b1d399a2"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022102581337094307, "Height": 0.012557001784443855, "Left": 0.17489652335643768, "Top": 0.027161402627825737}, "Polygon": [{"X": 0.17489668726921082, "Y": 0.027161402627825737}, {"X": 0.19699910283088684, "Y": 0.027161525562405586}, {"X": 0.19699892401695251, "Y": 0.03971840441226959}, {"X": 0.17489652335643768, "Y": 0.03971828520298004}]}, "Id": "8eae1862-7338-4c35-ab03-e48cda0dd7fe"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056163739413022995, "Height": 0.012911699712276459, "Left": 0.20747828483581543, "Top": 0.026837749406695366}, "Polygon": [{"X": 0.20747846364974976, "Y": 0.026837749406695366}, {"X": 0.26364201307296753, "Y": 0.026838060468435287}, {"X": 0.2636418342590332, "Y": 0.039749447256326675}, {"X": 0.20747828483581543, "Y": 0.0397491380572319}]}, "Id": "768644d8-b0ab-4873-9c72-ea70745a7ae4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09095849841833115, "Height": 0.012732623144984245, "Left": 0.2708835005760193, "Top": 0.026965690776705742}, "Polygon": [{"X": 0.2708836793899536, "Y": 0.026965690776705742}, {"X": 0.36184200644493103, "Y": 0.026966193690896034}, {"X": 0.3618417978286743, "Y": 0.03969831392168999}, {"X": 0.2708835005760193, "Y": 0.039697811007499695}]}, "Id": "03a79719-eb9d-42a7-a045-b2740c0a2c5d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07356178015470505, "Height": 0.014379063621163368, "Left": 0.796782910823822, "Top": 0.02605413645505905}, "Polygon": [{"X": 0.7967832088470459, "Y": 0.02605413645505905}, {"X": 0.8703446388244629, "Y": 0.026054542511701584}, {"X": 0.8703442811965942, "Y": 0.04043319821357727}, {"X": 0.796782910823822, "Y": 0.04043279215693474}]}, "Id": "ada63bdd-4889-43df-bad9-f96e60642609"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "59th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038900818675756454, "Height": 0.011613487266004086, "Left": 0.8764403462409973, "Top": 0.026083962991833687}, "Polygon": [{"X": 0.8764406442642212, "Y": 0.026083962991833687}, {"X": 0.9153411388397217, "Y": 0.02608417719602585}, {"X": 0.9153408408164978, "Y": 0.0376974493265152}, {"X": 0.8764403462409973, "Y": 0.037697236984968185}]}, "Id": "f1a354ab-6848-4786-b373-e92ebe82ad20"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "St,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023143431171774864, "Height": 0.013040349818766117, "Left": 0.9211626648902893, "Top": 0.02651405893266201}, "Polygon": [{"X": 0.921163022518158, "Y": 0.02651405893266201}, {"X": 0.9443060755729675, "Y": 0.026514187455177307}, {"X": 0.9443057775497437, "Y": 0.0395544096827507}, {"X": 0.9211626648902893, "Y": 0.039554283022880554}]}, "Id": "638e64bc-f33b-47d4-a774-27638d76a980"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015800844877958298, "Height": 0.011128504760563374, "Left": 0.9503452181816101, "Top": 0.02641633152961731}, "Polygon": [{"X": 0.950345516204834, "Y": 0.02641633152961731}, {"X": 0.9661460518836975, "Y": 0.026416419073939323}, {"X": 0.9661457538604736, "Y": 0.03754483535885811}, {"X": 0.9503452181816101, "Y": 0.037544749677181244}]}, "Id": "8533a448-d980-4c34-8c98-8c9b825f8d27"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01912476122379303, "Height": 0.0076410965994000435, "Left": 0.17518267035484314, "Top": 0.04848077520728111}, "Polygon": [{"X": 0.1751827746629715, "Y": 0.04848077520728111}, {"X": 0.19430743157863617, "Y": 0.04848087951540947}, {"X": 0.1943073272705078, "Y": 0.05612187087535858}, {"X": 0.17518267035484314, "Y": 0.056121766567230225}]}, "Id": "c52f97d6-f836-4295-98ef-c2596a2adc8e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "questions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05830542743206024, "Height": 0.00937641505151987, "Left": 0.19756588339805603, "Top": 0.04852355644106865}, "Polygon": [{"X": 0.19756601750850677, "Y": 0.04852355644106865}, {"X": 0.25587132573127747, "Y": 0.04852387681603432}, {"X": 0.2558711767196655, "Y": 0.057899974286556244}, {"X": 0.19756588339805603, "Y": 0.057899653911590576}]}, "Id": "e4846f9b-c9c7-4fc3-8303-10c0d4d73437"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "call,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023321598768234253, "Height": 0.008941498585045338, "Left": 0.2596563398838043, "Top": 0.04818664863705635}, "Polygon": [{"X": 0.25965645909309387, "Y": 0.04818664863705635}, {"X": 0.2829779386520386, "Y": 0.0481867752969265}, {"X": 0.28297778964042664, "Y": 0.057128146290779114}, {"X": 0.2596563398838043, "Y": 0.057128019630908966}]}, "Id": "5dbc3020-3d5c-4e89-9d1f-c687f14f010f"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "1-877-SHIPCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09149174392223358, "Height": 0.007767483126372099, "Left": 0.28730911016464233, "Top": 0.048323556780815125}, "Polygon": [{"X": 0.2873092293739319, "Y": 0.048323556780815125}, {"X": 0.3788008689880371, "Y": 0.04832405969500542}, {"X": 0.37880071997642517, "Y": 0.05609104037284851}, {"X": 0.28730911016464233, "Y": 0.05609053745865822}]}, "Id": "c90212b6-dd0c-4d3d-817b-279218c20a0d"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "MSDU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05518566071987152, "Height": 0.01112032774835825, "Left": 0.837049126625061, "Top": 0.053268034011125565}, "Polygon": [{"X": 0.8370494246482849, "Y": 0.053268034011125565}, {"X": 0.8922348022460938, "Y": 0.05326833575963974}, {"X": 0.8922345042228699, "Y": 0.06438835710287094}, {"X": 0.837049126625061, "Y": 0.06438805907964706}]}, "Id": "864b6159-94a5-4d04-845f-acc5298c88f6"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "143108", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06640009582042694, "Height": 0.010645738802850246, "Left": 0.8991196155548096, "Top": 0.05346858873963356}, "Polygon": [{"X": 0.8991199135780334, "Y": 0.05346858873963356}, {"X": 0.9655197262763977, "Y": 0.05346895381808281}, {"X": 0.9655194282531738, "Y": 0.06411432474851608}, {"X": 0.8991196155548096, "Y": 0.06411396712064743}]}, "Id": "e739d817-d094-4404-b8bd-f6920c2ff757"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "PAGE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032679155468940735, "Height": 0.007643769960850477, "Left": 0.03389347717165947, "Top": 0.07558448612689972}, "Polygon": [{"X": 0.033893562853336334, "Y": 0.07558448612689972}, {"X": 0.0665726289153099, "Y": 0.07558466494083405}, {"X": 0.06657253950834274, "Y": 0.08322826027870178}, {"X": 0.03389347717165947, "Y": 0.08322808146476746}]}, "Id": "fc99d57f-aa72-4e98-baa0-3b314d3bd196"}, {"BlockType": "WORD", "Confidence": 99.18626403808594, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0047399322502315044, "Height": 0.007300850003957748, "Left": 0.07063689827919006, "Top": 0.07553816586732864}, "Polygon": [{"X": 0.07063698768615723, "Y": 0.07553816586732864}, {"X": 0.07537683099508286, "Y": 0.07553818821907043}, {"X": 0.07537674158811569, "Y": 0.0828390121459961}, {"X": 0.07063689827919006, "Y": 0.0828389897942543}]}, "Id": "5b5e8a92-1fe5-468f-a34c-b48630e31255"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "020L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029701778665184975, "Height": 0.007738027255982161, "Left": 0.8481223583221436, "Top": 0.07150096446275711}, "Polygon": [{"X": 0.8481225967407227, "Y": 0.07150096446275711}, {"X": 0.8778241872787476, "Y": 0.07150112837553024}, {"X": 0.8778239488601685, "Y": 0.07923898845911026}, {"X": 0.8481223583221436, "Y": 0.07923883199691772}]}, "Id": "e82d4a17-73f5-4ee6-a6db-03a2c6dce6fa"}, {"BlockType": "WORD", "Confidence": 99.75426483154297, "Text": "0000W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04275267943739891, "Height": 0.007581203244626522, "Left": 0.8810858726501465, "Top": 0.07150508463382721}, "Polygon": [{"X": 0.8810860514640808, "Y": 0.07150508463382721}, {"X": 0.9238385558128357, "Y": 0.07150531560182571}, {"X": 0.9238383769989014, "Y": 0.0790862888097763}, {"X": 0.8810858726501465, "Y": 0.0790860503911972}]}, "Id": "5eaff773-b9a9-401a-9504-9982a781ec1b"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "0857H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03858284279704094, "Height": 0.007742379326373339, "Left": 0.9267887473106384, "Top": 0.0714506283402443}, "Polygon": [{"X": 0.9267889261245728, "Y": 0.0714506283402443}, {"X": 0.9653715491294861, "Y": 0.07145083695650101}, {"X": 0.9653713703155518, "Y": 0.07919300347566605}, {"X": 0.9267887473106384, "Y": 0.07919279485940933}]}, "Id": "c978e287-395b-464f-922d-b04bd86d0e07"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01808607019484043, "Height": 0.010400122031569481, "Left": 0.39895299077033997, "Top": 0.12111622095108032}, "Polygon": [{"X": 0.3989531695842743, "Y": 0.12111622095108032}, {"X": 0.41703906655311584, "Y": 0.12111631780862808}, {"X": 0.4170388877391815, "Y": 0.13151633739471436}, {"X": 0.39895299077033997, "Y": 0.1315162479877472}]}, "Id": "ae860f28-89e5-4f1f-a484-36cd15e0808f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07285984605550766, "Height": 0.010464158840477467, "Left": 0.4250635504722595, "Top": 0.12103845179080963}, "Polygon": [{"X": 0.42506372928619385, "Y": 0.12103845179080963}, {"X": 0.4979233741760254, "Y": 0.12103884667158127}, {"X": 0.49792319536209106, "Y": 0.13150261342525482}, {"X": 0.4250635504722595, "Y": 0.13150222599506378}]}, "Id": "688301ee-a11a-410a-933e-4a106ef80e1c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "DETAILS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07105246186256409, "Height": 0.01048013474792242, "Left": 0.5016834139823914, "Top": 0.12103862315416336}, "Polygon": [{"X": 0.5016835927963257, "Y": 0.12103862315416336}, {"X": 0.5727358460426331, "Y": 0.1210390031337738}, {"X": 0.5727356672286987, "Y": 0.13151875138282776}, {"X": 0.5016834139823914, "Y": 0.1315183788537979}]}, "Id": "d5bbd0e1-9176-4d92-9689-e4716536308f"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "(1)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02215646207332611, "Height": 0.01345070730894804, "Left": 0.5778865218162537, "Top": 0.12046615779399872}, "Polygon": [{"X": 0.5778867602348328, "Y": 0.12046615779399872}, {"X": 0.600042998790741, "Y": 0.12046628445386887}, {"X": 0.6000427007675171, "Y": 0.13391686975955963}, {"X": 0.5778865218162537, "Y": 0.13391675055027008}]}, "Id": "643a840a-0f91-4077-87ab-8efbe7dd4b54"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050919145345687866, "Height": 0.0107437027618289, "Left": 0.04012782499194145, "Top": 0.15501870214939117}, "Polygon": [{"X": 0.0401279479265213, "Y": 0.15501870214939117}, {"X": 0.09104697406291962, "Y": 0.15501898527145386}, {"X": 0.09104683995246887, "Y": 0.16576240956783295}, {"X": 0.04012782499194145, "Y": 0.16576214134693146}]}, "Id": "44d49fc6-1ee2-418c-bb7d-34036d68c6ea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Associated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07560575008392334, "Height": 0.008704077452421188, "Left": 0.18801763653755188, "Top": 0.154935821890831}, "Polygon": [{"X": 0.18801775574684143, "Y": 0.154935821890831}, {"X": 0.2636233866214752, "Y": 0.15493622422218323}, {"X": 0.2636232376098633, "Y": 0.16363990306854248}, {"X": 0.18801763653755188, "Y": 0.16363950073719025}]}, "Id": "adb77c4f-3903-4517-8c8d-72a172ea8931"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03265435993671417, "Height": 0.0086421063169837, "Left": 0.4843212068080902, "Top": 0.1549380123615265}, "Polygon": [{"X": 0.48432135581970215, "Y": 0.1549380123615265}, {"X": 0.5169755816459656, "Y": 0.15493819117546082}, {"X": 0.5169754028320312, "Y": 0.16358011960983276}, {"X": 0.4843212068080902, "Y": 0.16357995569705963}]}, "Id": "a2132b26-76dd-4642-8503-834477eff799"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01428280957043171, "Height": 0.006389146205037832, "Left": 0.5210670828819275, "Top": 0.1570792943239212}, "Polygon": [{"X": 0.521067202091217, "Y": 0.1570792943239212}, {"X": 0.5353499054908752, "Y": 0.15707938373088837}, {"X": 0.5353497862815857, "Y": 0.16346845030784607}, {"X": 0.5210670828819275, "Y": 0.1634683758020401}]}, "Id": "41c9e581-312d-4566-9bc8-9452f3c8c46f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Seal(s)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04679520055651665, "Height": 0.011002695187926292, "Left": 0.33602067828178406, "Top": 0.1605820208787918}, "Polygon": [{"X": 0.3360208570957184, "Y": 0.1605820208787918}, {"X": 0.3828158676624298, "Y": 0.1605822741985321}, {"X": 0.3828156888484955, "Y": 0.17158472537994385}, {"X": 0.33602067828178406, "Y": 0.17158447206020355}]}, "Id": "e7f93c89-17e0-4696-b5f5-009c7c180bdc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055196262896060944, "Height": 0.010799000971019268, "Left": 0.5985212326049805, "Top": 0.16091540455818176}, "Polygon": [{"X": 0.5985214710235596, "Y": 0.16091540455818176}, {"X": 0.6537175178527832, "Y": 0.16091570258140564}, {"X": 0.6537172794342041, "Y": 0.1717144101858139}, {"X": 0.5985212326049805, "Y": 0.17171411216259003}]}, "Id": "a4ad43a8-3159-47e6-bb66-dc577633caac"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008915552869439125, "Height": 0.008323067799210548, "Left": 0.6576539278030396, "Top": 0.16101735830307007}, "Polygon": [{"X": 0.6576541066169739, "Y": 0.16101735830307007}, {"X": 0.6665694713592529, "Y": 0.16101740300655365}, {"X": 0.6665692925453186, "Y": 0.16934043169021606}, {"X": 0.6576539278030396, "Y": 0.1693403720855713}]}, "Id": "5bd92a29-ab26-4b47-b433-a18ef635af2d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transportation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10026700794696808, "Height": 0.010603534989058971, "Left": 0.7453497052192688, "Top": 0.15508194267749786}, "Polygon": [{"X": 0.7453499436378479, "Y": 0.15508194267749786}, {"X": 0.8456166982650757, "Y": 0.15508247911930084}, {"X": 0.8456164598464966, "Y": 0.1656854748725891}, {"X": 0.7453497052192688, "Y": 0.16568493843078613}]}, "Id": "772738fd-7a26-4568-924a-9815ca4061e2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06466621160507202, "Height": 0.010775054804980755, "Left": 0.8494766354560852, "Top": 0.155060812830925}, "Polygon": [{"X": 0.8494768738746643, "Y": 0.155060812830925}, {"X": 0.9141428470611572, "Y": 0.15506115555763245}, {"X": 0.9141425490379333, "Y": 0.16583587229251862}, {"X": 0.8494766354560852, "Y": 0.16583551466464996}]}, "Id": "17eaeeda-f6d7-4865-b86b-9b9bd592f45c"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030993469059467316, "Height": 0.008608518168330193, "Left": 0.04051117226481438, "Top": 0.16761158406734467}, "Polygon": [{"X": 0.04051126912236214, "Y": 0.16761158406734467}, {"X": 0.07150464504957199, "Y": 0.1676117479801178}, {"X": 0.07150454074144363, "Y": 0.17622010409832}, {"X": 0.04051117226481438, "Y": 0.17621994018554688}]}, "Id": "7c55fb9f-ea7b-4315-9c27-d4a60badc4c1"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009609516710042953, "Height": 0.008438249118626118, "Left": 0.07551273703575134, "Top": 0.16777978837490082}, "Polygon": [{"X": 0.0755128338932991, "Y": 0.16777978837490082}, {"X": 0.085122250020504, "Y": 0.1677798330783844}, {"X": 0.08512215316295624, "Y": 0.17621803283691406}, {"X": 0.07551273703575134, "Y": 0.17621798813343048}]}, "Id": "910e2c42-87d5-4d48-91a9-21403ea7c0a8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03449082747101784, "Height": 0.008592508733272552, "Left": 0.0884842500090599, "Top": 0.16761013865470886}, "Polygon": [{"X": 0.08848435431718826, "Y": 0.16761013865470886}, {"X": 0.12297508120536804, "Y": 0.1676103174686432}, {"X": 0.12297496944665909, "Y": 0.17620263993740082}, {"X": 0.0884842500090599, "Y": 0.1762024611234665}]}, "Id": "d9ad47f4-360d-422a-9fdc-a4b1f14fc93f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07222753018140793, "Height": 0.010699338279664516, "Left": 0.18863140046596527, "Top": 0.16756883263587952}, "Polygon": [{"X": 0.18863153457641602, "Y": 0.16756883263587952}, {"X": 0.2608589231967926, "Y": 0.16756922006607056}, {"X": 0.26085877418518066, "Y": 0.178268164396286}, {"X": 0.18863140046596527, "Y": 0.17826777696609497}]}, "Id": "6a216b54-a4a1-4b06-be65-70b46fe8a4d5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04202501103281975, "Height": 0.01073907408863306, "Left": 0.4844515919685364, "Top": 0.16767942905426025}, "Polygon": [{"X": 0.4844517707824707, "Y": 0.16767942905426025}, {"X": 0.5264766216278076, "Y": 0.16767965257167816}, {"X": 0.5264763832092285, "Y": 0.17841850221157074}, {"X": 0.4844515919685364, "Y": 0.17841827869415283}]}, "Id": "da4b8b93-c648-422c-9cfb-fa8e4f9d7262"}, {"BlockType": "WORD", "Confidence": 99.7265625, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009379959665238857, "Height": 0.00842752493917942, "Left": 0.7460808157920837, "Top": 0.1677834391593933}, "Polygon": [{"X": 0.7460809946060181, "Y": 0.1677834391593933}, {"X": 0.755460798740387, "Y": 0.1677834838628769}, {"X": 0.7554605603218079, "Y": 0.17621095478534698}, {"X": 0.7460808157920837, "Y": 0.1762109100818634}]}, "Id": "2002f2e3-2822-4aa3-85b4-f7eac784d470"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03966568037867546, "Height": 0.008406152948737144, "Left": 0.7598138451576233, "Top": 0.16759130358695984}, "Polygon": [{"X": 0.7598140239715576, "Y": 0.16759130358695984}, {"X": 0.7994794845581055, "Y": 0.16759151220321655}, {"X": 0.7994793057441711, "Y": 0.17599745094776154}, {"X": 0.7598138451576233, "Y": 0.17599724233150482}]}, "Id": "74152da9-c57f-4a84-afd6-6ddf0c95adf5"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Out-Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05277660861611366, "Height": 0.007833165116608143, "Left": 0.040394414216279984, "Top": 0.20010599493980408}, "Polygon": [{"X": 0.04039450362324715, "Y": 0.20010599493980408}, {"X": 0.09317102283239365, "Y": 0.20010626316070557}, {"X": 0.09317092597484589, "Y": 0.20793914794921875}, {"X": 0.040394414216279984, "Y": 0.20793887972831726}]}, "Id": "21cf0573-b988-4cf6-b668-085daa1d7c7b"}, {"BlockType": "WORD", "Confidence": 99.619140625, "Text": "MCCZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03684834763407707, "Height": 0.007730321492999792, "Left": 0.1884511411190033, "Top": 0.20011895895004272}, "Polygon": [{"X": 0.18845124542713165, "Y": 0.20011895895004272}, {"X": 0.22529949247837067, "Y": 0.20011915266513824}, {"X": 0.2252993881702423, "Y": 0.20784927904605865}, {"X": 0.1884511411190033, "Y": 0.20784908533096313}]}, "Id": "d129aa32-cc86-4ca9-9965-0c3650188af2"}, {"BlockType": "WORD", "Confidence": 99.4921875, "Text": "341835", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04539753869175911, "Height": 0.00766717316582799, "Left": 0.22856548428535461, "Top": 0.200140580534935}, "Polygon": [{"X": 0.22856560349464417, "Y": 0.200140580534935}, {"X": 0.2739630341529846, "Y": 0.2001408189535141}, {"X": 0.27396291494369507, "Y": 0.2078077644109726}, {"X": 0.22856548428535461, "Y": 0.2078075259923935}]}, "Id": "1dcac4ce-731b-4267-ab45-9eade8f65b84"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FX36673264", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07622420787811279, "Height": 0.007731626741588116, "Left": 0.3363126516342163, "Top": 0.20013010501861572}, "Polygon": [{"X": 0.33631277084350586, "Y": 0.20013010501861572}, {"X": 0.4125368595123291, "Y": 0.20013050734996796}, {"X": 0.41253674030303955, "Y": 0.2078617364168167}, {"X": 0.3363126516342163, "Y": 0.20786133408546448}]}, "Id": "95485913-7384-44b3-a472-6583bd4195ba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029214391484856606, "Height": 0.007818891666829586, "Left": 0.4841298460960388, "Top": 0.20001554489135742}, "Polygon": [{"X": 0.48412999510765076, "Y": 0.20001554489135742}, {"X": 0.5133442282676697, "Y": 0.20001570880413055}, {"X": 0.5133441090583801, "Y": 0.20783443748950958}, {"X": 0.4841298460960388, "Y": 0.20783428847789764}]}, "Id": "d4464b91-8c72-4324-a547-3fb32d7b7227"}, {"BlockType": "WORD", "Confidence": 91.9884033203125, "Text": "---", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011886738240718842, "Height": 0.0018588016973808408, "Left": 0.59744793176651, "Top": 0.20347531139850616}, "Polygon": [{"X": 0.5974479913711548, "Y": 0.20347531139850616}, {"X": 0.6093347072601318, "Y": 0.20347538590431213}, {"X": 0.6093346476554871, "Y": 0.2053341269493103}, {"X": 0.59744793176651, "Y": 0.20533405244350433}]}, "Id": "8a09e591-8f13-40d5-b2f0-393dd2ee5686"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Desert", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038839515298604965, "Height": 0.007602128200232983, "Left": 0.7462791800498962, "Top": 0.20018316805362701}, "Polygon": [{"X": 0.7462793588638306, "Y": 0.20018316805362701}, {"X": 0.7851186990737915, "Y": 0.20018337666988373}, {"X": 0.7851185202598572, "Y": 0.20778529345989227}, {"X": 0.7462791800498962, "Y": 0.20778509974479675}]}, "Id": "1fa5bab4-69b8-4185-af57-cf3d784ff468"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Eagle", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03219826519489288, "Height": 0.009473408572375774, "Left": 0.7886824011802673, "Top": 0.20003104209899902}, "Polygon": [{"X": 0.7886826395988464, "Y": 0.20003104209899902}, {"X": 0.8208807110786438, "Y": 0.20003120601177216}, {"X": 0.8208804726600647, "Y": 0.20950445532798767}, {"X": 0.7886824011802673, "Y": 0.20950427651405334}]}, "Id": "a95189f5-a240-46f5-80d7-c0047e500a85"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Express", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04643072187900543, "Height": 0.009489662945270538, "Left": 0.8249260187149048, "Top": 0.20012159645557404}, "Polygon": [{"X": 0.8249262571334839, "Y": 0.20012159645557404}, {"X": 0.871356725692749, "Y": 0.20012183487415314}, {"X": 0.8713564872741699, "Y": 0.20961125195026398}, {"X": 0.8249260187149048, "Y": 0.20961101353168488}]}, "Id": "62efc866-5e9d-4a3b-9af1-e37370f7da26"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017915965989232063, "Height": 0.007672048173844814, "Left": 0.8754919171333313, "Top": 0.20018166303634644}, "Polygon": [{"X": 0.8754920959472656, "Y": 0.20018166303634644}, {"X": 0.8934078812599182, "Y": 0.2001817524433136}, {"X": 0.8934077024459839, "Y": 0.20785370469093323}, {"X": 0.8754919171333313, "Y": 0.20785361528396606}]}, "Id": "c5ed03bb-034c-4fac-b1e4-a9e1c1604ae1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "(DEEE)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04025775566697121, "Height": 0.009958338923752308, "Left": 0.8970704078674316, "Top": 0.1996808648109436}, "Polygon": [{"X": 0.8970706462860107, "Y": 0.1996808648109436}, {"X": 0.9373281598091125, "Y": 0.19968107342720032}, {"X": 0.9373279213905334, "Y": 0.20963920652866364}, {"X": 0.8970704078674316, "Y": 0.20963899791240692}]}, "Id": "cc3910ae-cc4a-45ab-a1fe-21e9d6bd4f5f"}, {"BlockType": "WORD", "Confidence": 98.94451141357422, "Text": "08/23/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05671102926135063, "Height": 0.00788898766040802, "Left": 0.040291182696819305, "Top": 0.21117179095745087}, "Polygon": [{"X": 0.04029127210378647, "Y": 0.21117179095745087}, {"X": 0.09700220823287964, "Y": 0.21117208898067474}, {"X": 0.09700211137533188, "Y": 0.2190607786178589}, {"X": 0.040291182696819305, "Y": 0.219060480594635}]}, "Id": "f83c8709-f9ef-4283-a7c4-5ba091ead072"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "14:46", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0326213575899601, "Height": 0.007565627805888653, "Left": 0.10770202428102493, "Top": 0.21130669116973877}, "Polygon": [{"X": 0.10770212113857269, "Y": 0.21130669116973877}, {"X": 0.14032338559627533, "Y": 0.2113068699836731}, {"X": 0.14032328128814697, "Y": 0.2188723236322403}, {"X": 0.10770202428102493, "Y": 0.21887215971946716}]}, "Id": "c6d0dd2b-34fc-419a-80fd-09a3d0a043c3"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "<PERSON><PERSON><PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07035356760025024, "Height": 0.008892267942428589, "Left": 0.7463070154190063, "Top": 0.21111929416656494}, "Polygon": [{"X": 0.7463072538375854, "Y": 0.21111929416656494}, {"X": 0.8166605830192566, "Y": 0.2111196666955948}, {"X": 0.8166604042053223, "Y": 0.22001156210899353}, {"X": 0.7463070154190063, "Y": 0.22001120448112488}]}, "Id": "7caca86a-d495-45a3-8eea-adb5a3a58af6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07147654891014099, "Height": 0.007757317740470171, "Left": 0.8203059434890747, "Top": 0.21112065017223358}, "Polygon": [{"X": 0.820306122303009, "Y": 0.21112065017223358}, {"X": 0.8917824625968933, "Y": 0.21112102270126343}, {"X": 0.891782283782959, "Y": 0.21887797117233276}, {"X": 0.8203059434890747, "Y": 0.21887758374214172}]}, "Id": "7603c422-a947-45a7-b820-c9460e0960c1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04936644062399864, "Height": 0.009503179229795933, "Left": 0.03398408368229866, "Top": 0.2625455856323242}, "Polygon": [{"X": 0.033984191715717316, "Y": 0.2625455856323242}, {"X": 0.0833505243062973, "Y": 0.2625458240509033}, {"X": 0.08335041254758835, "Y": 0.2720487415790558}, {"X": 0.03398408368229866, "Y": 0.2720485031604767}]}, "Id": "63e3366b-a8d2-4202-ba27-4879a41077be"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009767211973667145, "Height": 0.007824597880244255, "Left": 0.08701301366090775, "Top": 0.2624775767326355}, "Polygon": [{"X": 0.0870131105184555, "Y": 0.2624775767326355}, {"X": 0.09678022563457489, "Y": 0.2624776065349579}, {"X": 0.09678012877702713, "Y": 0.2703021764755249}, {"X": 0.08701301366090775, "Y": 0.2703021168708801}]}, "Id": "f92b6397-4e9a-4f00-9621-a360c7c22bbc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052354633808135986, "Height": 0.009681438095867634, "Left": 0.10034021735191345, "Top": 0.26238781213760376}, "Polygon": [{"X": 0.1003403440117836, "Y": 0.26238781213760376}, {"X": 0.15269485116004944, "Y": 0.26238808035850525}, {"X": 0.1526947319507599, "Y": 0.2720692455768585}, {"X": 0.10034021735191345, "Y": 0.27206897735595703}]}, "Id": "a6a6980f-b449-4800-84c1-1e8842469101"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014751802198588848, "Height": 0.0058775171637535095, "Left": 0.15647685527801514, "Top": 0.264335960149765}, "Polygon": [{"X": 0.1564769446849823, "Y": 0.264335960149765}, {"X": 0.17122866213321686, "Y": 0.2643360197544098}, {"X": 0.1712285876274109, "Y": 0.27021345496177673}, {"X": 0.15647685527801514, "Y": 0.27021339535713196}]}, "Id": "11692843-03ff-46f4-ae03-e4f48964d8ac"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05248543247580528, "Height": 0.008104232139885426, "Left": 0.17488345503807068, "Top": 0.262190043926239}, "Polygon": [{"X": 0.17488355934619904, "Y": 0.262190043926239}, {"X": 0.22736887633800507, "Y": 0.2621903121471405}, {"X": 0.22736875712871552, "Y": 0.27029427886009216}, {"X": 0.17488345503807068, "Y": 0.2702940106391907}]}, "Id": "aafa9006-bc6a-4b75-b856-d4d6860bc3f1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04359230771660805, "Height": 0.009620781987905502, "Left": 0.23121269047260284, "Top": 0.2625049650669098}, "Polygon": [{"X": 0.2312128245830536, "Y": 0.2625049650669098}, {"X": 0.2748050093650818, "Y": 0.2625051736831665}, {"X": 0.27480486035346985, "Y": 0.2721257507801056}, {"X": 0.23121269047260284, "Y": 0.2721255123615265}]}, "Id": "38a6933c-01cf-41a5-8207-fa7e3546a234"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011720801703631878, "Height": 0.007211640942841768, "Left": 0.27847012877464294, "Top": 0.2631077766418457}, "Polygon": [{"X": 0.2784702479839325, "Y": 0.2631077766418457}, {"X": 0.2901909351348877, "Y": 0.2631078362464905}, {"X": 0.29019081592559814, "Y": 0.270319402217865}, {"X": 0.27847012877464294, "Y": 0.2703193426132202}]}, "Id": "5ce9ce4c-ca11-46df-9135-88765d56fcc0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "AGS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026036249473690987, "Height": 0.00777202844619751, "Left": 0.2930905520915985, "Top": 0.2625553011894226}, "Polygon": [{"X": 0.29309067130088806, "Y": 0.2625553011894226}, {"X": 0.31912678480148315, "Y": 0.26255545020103455}, {"X": 0.3191266655921936, "Y": 0.2703273296356201}, {"X": 0.2930905520915985, "Y": 0.27032721042633057}]}, "Id": "855fc146-048e-4729-bd53-c6b205e3b680"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "locations", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05441678687930107, "Height": 0.008100088685750961, "Left": 0.3228132426738739, "Top": 0.262249231338501}, "Polygon": [{"X": 0.32281336188316345, "Y": 0.262249231338501}, {"X": 0.37723004817962646, "Y": 0.26224949955940247}, {"X": 0.3772298991680145, "Y": 0.27034929394721985}, {"X": 0.3228132426738739, "Y": 0.27034902572631836}]}, "Id": "dcac382c-fffe-4a39-8e5d-e44d1e906c19"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d7078aa7-df8a-418f-ab50-8bc995258255", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d7078aa7-df8a-418f-ab50-8bc995258255", "content-type": "application/x-amz-json-1.1", "content-length": "48525", "date": "Wed, 24 Sep 2025 13:32:24 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "441a80e3-2692-49a4-8467-80f49cbfcd06", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:26 GMT", "content-type": "application/json", "content-length": "850", "connection": "keep-alive", "x-amzn-requestid": "441a80e3-2692-49a4-8467-80f49cbfcd06"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. We need to classify the document type. The content: \"J1 GATE RECEIPT\" and \"Out-Gate\" etc. It looks like a gate receipt, an outgate document. So doc_type should be outgate. Let's check the keywords: Outgate, Gate Receipt, Out-Gate, etc. That matches outgate definition.\n\nWe also have \"J1 GATE RECEIPT\" which is a gate receipt. So classify as outgate.\n\nWe must produce JSON with documents array containing one object: page_no 1, doc_type \"outgate\".\n\nReturn via tool call classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2738, "outputTokens": 151, "totalTokens": 2889}, "metrics": {"latencyMs": 898}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/f4a879bf_GHXKMFASS6CGN362R5BW.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n[csx\n]\nJ1 GATE RECEIPT\nChicago 59th St, IL\nFor questions call, 1-877-SHIPCSX\nMSDU 143108\nPAGE 1\n020L 0000W 0857H\nJ1 RECEIPT DETAILS (1)\nActivity\nAssociated\nLoad or\nSeal(s)\nBooking #\nTransportation Company\nDate & Time\nEquipment\nEmpty\n& Driver\nOut-Gate\nMCCZ 341835\nFX36673264\nLoad\n---\nDesert Eagle Express Inc (DEEE)\n08/23/25 14:46\nDardawoud, <PERSON>\nDamage is captured on recorded images at AGS locations\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}