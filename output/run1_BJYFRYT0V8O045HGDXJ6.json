{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "8bfd8219-68c0-46e9-883a-eb5c7b181455", "Relationships": [{"Type": "CHILD", "Ids": ["3b32714c-6c36-49f5-af78-5ff76655fe45", "06f06ab5-5935-4293-938d-6f1b916deb86", "5f7fa0e4-7553-42ca-88ea-1ca6c9b5bcce", "24650da2-d49b-4eaf-ad04-bb22e33c0e6a", "f7923e5c-37bf-46b7-b3b2-9bd0c09a38e1", "a103956e-d626-4750-8021-70030f2dd520", "252b456d-667a-4b14-82e1-0a0133244dad", "8fb423fc-b42b-4d9a-8757-168ecef548f3", "b62e6683-4c7c-4e61-b8c2-a6a263e99da2", "4e942a6e-48de-4ec1-b9a1-4b4ae6ef246a", "e1f443ea-793c-44f2-9752-77c71c468cf4", "aec503b1-eaec-498c-bc9f-7f351b89e4a6", "14fa5173-7a6a-4335-9d92-034ec4365096", "49cc3c78-ad94-46c6-a68b-aee4eb09240b", "7b8fd3ae-0b1c-405d-8ff8-ffbe8189e254", "c402d543-8717-4632-b94b-256ac60a883e", "85862c50-2ee5-47ad-88f6-ee1d515aab47", "9f957b42-cce4-4ec2-ab15-6b6c6b703d13", "99e9825d-c27c-4b9e-9bb1-441e7462e525", "340ff39d-b6b4-4cda-a7e6-62a6b040e0f1", "209ffcb5-93ea-4329-8e2c-92a2e94e8cf2", "75261bc2-f53e-46d0-92db-f57efe0f6202", "ecfeccc8-96c3-4768-9cf0-a8fc3da89fe6", "fe2ae27e-7da7-4d51-8b21-cd09ee76ba24", "c37317e6-4ed7-4ac0-9a07-97b5b87ddf8d", "35dfec05-783a-4a48-ace3-cdcfdbc0c74f", "939c0711-9fee-42ab-b5a6-1ccebdfeeb03", "f3a08ab2-97a3-4576-8777-64bb1fc11015"]}]}, {"BlockType": "LINE", "Confidence": 31.470623016357422, "Text": "[ CSX", "Geometry": {"BoundingBox": {"Width": 0.0940861850976944, "Height": 0.031865958124399185, "Left": 0.03290572762489319, "Top": 0.024153364822268486}, "Polygon": [{"X": 0.032906267791986465, "Y": 0.024153364822268486}, {"X": 0.12699191272258759, "Y": 0.02415413409471512}, {"X": 0.12699131667613983, "Y": 0.05601932108402252}, {"X": 0.03290572762489319, "Y": 0.056018561124801636}]}, "Id": "3b32714c-6c36-49f5-af78-5ff76655fe45", "Relationships": [{"Type": "CHILD", "Ids": ["4f90b7b9-3d0f-44b2-bbdd-a228eeb2dd0d"]}]}, {"BlockType": "LINE", "Confidence": 92.48306274414062, "Text": "]", "Geometry": {"BoundingBox": {"Width": 0.011988402344286442, "Height": 0.032433513551950455, "Left": 0.14008095860481262, "Top": 0.023242410272359848}, "Polygon": [{"X": 0.14008156955242157, "Y": 0.023242410272359848}, {"X": 0.1520693600177765, "Y": 0.023242508992552757}, {"X": 0.15206874907016754, "Y": 0.055675920099020004}, {"X": 0.14008095860481262, "Y": 0.055675823241472244}]}, "Id": "06f06ab5-5935-4293-938d-6f1b916deb86", "Relationships": [{"Type": "CHILD", "Ids": ["8a3e9759-5081-4365-a059-78e3cc77f159"]}]}, {"BlockType": "LINE", "Confidence": 99.94466400146484, "Text": "J1 GATE RECEIPT", "Geometry": {"BoundingBox": {"Width": 0.18704238533973694, "Height": 0.012883887626230717, "Left": 0.17479710280895233, "Top": 0.02685581147670746}, "Polygon": [{"X": 0.17479735612869263, "Y": 0.02685581147670746}, {"X": 0.3618394732475281, "Y": 0.026857338845729828}, {"X": 0.3618391752243042, "Y": 0.0397396981716156}, {"X": 0.17479710280895233, "Y": 0.03973817825317383}]}, "Id": "5f7fa0e4-7553-42ca-88ea-1ca6c9b5bcce", "Relationships": [{"Type": "CHILD", "Ids": ["77b3a316-db5c-4e95-a64c-11ee8fe26ea4", "d0295384-14bb-40e2-8273-a88933c0baf7", "7219c5ed-64e6-4d51-a592-989fb97f32a3"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Chicago 59th St, IL", "Geometry": {"BoundingBox": {"Width": 0.16897422075271606, "Height": 0.014313336461782455, "Left": 0.7971082329750061, "Top": 0.02608403004705906}, "Polygon": [{"X": 0.7971087098121643, "Y": 0.02608403004705906}, {"X": 0.9660825133323669, "Y": 0.02608540840446949}, {"X": 0.9660820364952087, "Y": 0.040397364646196365}, {"X": 0.7971082329750061, "Y": 0.04039599373936653}]}, "Id": "24650da2-d49b-4eaf-ad04-bb22e33c0e6a", "Relationships": [{"Type": "CHILD", "Ids": ["1cec1c8e-9a0c-42e2-bdff-3d520029fdf1", "9f65e1bd-8262-4a18-b54d-f7b2a38758a0", "193e25f4-cfd2-4e4b-9b2f-af878de08795", "90f8bd14-4c67-42df-9c31-0a9dda13ace8"]}]}, {"BlockType": "LINE", "Confidence": 99.92127227783203, "Text": "For questions call, 1-877-SHIPCSX", "Geometry": {"BoundingBox": {"Width": 0.20364023745059967, "Height": 0.009709827601909637, "Left": 0.17516060173511505, "Top": 0.048192158341407776}, "Polygon": [{"X": 0.17516078054904938, "Y": 0.048192158341407776}, {"X": 0.3788008391857147, "Y": 0.048193808645009995}, {"X": 0.378800630569458, "Y": 0.05790198594331741}, {"X": 0.17516060173511505, "Y": 0.05790034309029579}]}, "Id": "f7923e5c-37bf-46b7-b3b2-9bd0c09a38e1", "Relationships": [{"Type": "CHILD", "Ids": ["10c78e8f-ac18-4092-9a55-ac6e56ff77d0", "d7c3ae23-ad57-46f9-b593-f0d0dc481652", "948b6893-d9cd-4d1c-9e30-cb3f38e8abe7", "c0edb9b0-2053-4cdd-bc46-cd7ed487a1cf"]}]}, {"BlockType": "LINE", "Confidence": 99.75546264648438, "Text": "FFAU 217080", "Geometry": {"BoundingBox": {"Width": 0.11886774003505707, "Height": 0.011034546419978142, "Left": 0.8466758728027344, "Top": 0.05329373851418495}, "Polygon": [{"X": 0.8466761708259583, "Y": 0.05329373851418495}, {"X": 0.9655435681343079, "Y": 0.053294699639081955}, {"X": 0.9655432105064392, "Y": 0.06432828307151794}, {"X": 0.8466758728027344, "Y": 0.06432732939720154}]}, "Id": "a103956e-d626-4750-8021-70030f2dd520", "Relationships": [{"Type": "CHILD", "Ids": ["af1b7e34-677f-4521-b56d-3c679618d162", "4323e803-89eb-435f-8072-3f281187fb9d"]}]}, {"BlockType": "LINE", "Confidence": 99.54918670654297, "Text": "PAGE 1", "Geometry": {"BoundingBox": {"Width": 0.041482146829366684, "Height": 0.007689214777201414, "Left": 0.03389681875705719, "Top": 0.07553156465291977}, "Polygon": [{"X": 0.033896949142217636, "Y": 0.07553156465291977}, {"X": 0.07537896186113358, "Y": 0.07553189247846603}, {"X": 0.07537882775068283, "Y": 0.08322077989578247}, {"X": 0.03389681875705719, "Y": 0.08322044461965561}]}, "Id": "252b456d-667a-4b14-82e1-0a0133244dad", "Relationships": [{"Type": "CHILD", "Ids": ["5cd0534e-1149-457e-ba5c-36ac7008cd9a", "1b1bd022-4bfe-4292-9dbc-65c601d159fd"]}]}, {"BlockType": "LINE", "Confidence": 99.37467193603516, "Text": "040L 0000W 0000H", "Geometry": {"BoundingBox": {"Width": 0.11703171581029892, "Height": 0.007783465087413788, "Left": 0.8481225371360779, "Top": 0.07146593183279037}, "Polygon": [{"X": 0.848122775554657, "Y": 0.07146593183279037}, {"X": 0.965154230594635, "Y": 0.07146687060594559}, {"X": 0.9651539921760559, "Y": 0.07924939692020416}, {"X": 0.8481225371360779, "Y": 0.07924845814704895}]}, "Id": "8fb423fc-b42b-4d9a-8757-168ecef548f3", "Relationships": [{"Type": "CHILD", "Ids": ["01b90eb3-d814-4c65-9fc5-606a931b30d2", "856de416-7c54-461b-a5f3-dafa92862ab8", "1e003ff3-d30a-4b43-ac64-52c3a56bbe2f"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "J1 RECEIPT DETAILS (1)", "Geometry": {"BoundingBox": {"Width": 0.2010316401720047, "Height": 0.013417885638773441, "Left": 0.39893218874931335, "Top": 0.12046519666910172}, "Polygon": [{"X": 0.3989325165748596, "Y": 0.12046519666910172}, {"X": 0.5999638438224792, "Y": 0.12046678364276886}, {"X": 0.5999634861946106, "Y": 0.13388308882713318}, {"X": 0.39893218874931335, "Y": 0.13388150930404663}]}, "Id": "b62e6683-4c7c-4e61-b8c2-a6a263e99da2", "Relationships": [{"Type": "CHILD", "Ids": ["4bf9681c-bada-45d5-af44-185c6ccaaee6", "d630b6f4-f9e0-4350-bb63-7418e01df60d", "444722fd-0534-4a6f-8957-af96d172b8be", "4e41d8b9-336f-42d3-aed8-59aa803b3c64"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.05092349275946617, "Height": 0.010725928470492363, "Left": 0.04012558236718178, "Top": 0.15502355992794037}, "Polygon": [{"X": 0.0401257649064064, "Y": 0.15502355992794037}, {"X": 0.09104907512664795, "Y": 0.1550239622592926}, {"X": 0.09104888141155243, "Y": 0.16574949026107788}, {"X": 0.04012558236718178, "Y": 0.16574910283088684}]}, "Id": "4e942a6e-48de-4ec1-b9a1-4b4ae6ef246a", "Relationships": [{"Type": "CHILD", "Ids": ["b8fd0642-8028-478c-8db0-1255e638acb6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Associated", "Geometry": {"BoundingBox": {"Width": 0.07554110139608383, "Height": 0.008692550472915173, "Left": 0.18796482682228088, "Top": 0.15494760870933533}, "Polygon": [{"X": 0.1879650056362152, "Y": 0.15494760870933533}, {"X": 0.2635059356689453, "Y": 0.15494820475578308}, {"X": 0.263505756855011, "Y": 0.16364015638828278}, {"X": 0.18796482682228088, "Y": 0.16363957524299622}]}, "Id": "e1f443ea-793c-44f2-9752-77c71c468cf4", "Relationships": [{"Type": "CHILD", "Ids": ["3a84e692-0fa7-46ea-a9be-edbe354e0523"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "Load or", "Geometry": {"BoundingBox": {"Width": 0.051099129021167755, "Height": 0.00863533653318882, "Left": 0.4842701554298401, "Top": 0.15494708716869354}, "Polygon": [{"X": 0.4842703640460968, "Y": 0.15494708716869354}, {"X": 0.5353692770004272, "Y": 0.15494747459888458}, {"X": 0.5353690385818481, "Y": 0.16358241438865662}, {"X": 0.4842701554298401, "Y": 0.16358202695846558}]}, "Id": "aec503b1-eaec-498c-bc9f-7f351b89e4a6", "Relationships": [{"Type": "CHILD", "Ids": ["ecdfdf82-7385-4f3a-8f13-8929c3828dba", "9d1f7983-9e2a-41c7-b1c6-b9d78c69ec30"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Seal(s)", "Geometry": {"BoundingBox": {"Width": 0.04685559123754501, "Height": 0.011060251854360104, "Left": 0.3359811305999756, "Top": 0.16046734154224396}, "Polygon": [{"X": 0.3359813690185547, "Y": 0.16046734154224396}, {"X": 0.3828367292881012, "Y": 0.1604677140712738}, {"X": 0.3828364610671997, "Y": 0.17152759432792664}, {"X": 0.3359811305999756, "Y": 0.17152723670005798}]}, "Id": "14fa5173-7a6a-4335-9d92-034ec4365096", "Relationships": [{"Type": "CHILD", "Ids": ["89648590-b1a3-450e-8e6f-b047f0b2d975"]}]}, {"BlockType": "LINE", "Confidence": 99.8583984375, "Text": "Booking #", "Geometry": {"BoundingBox": {"Width": 0.06787800788879395, "Height": 0.010850371792912483, "Left": 0.5986450910568237, "Top": 0.16090214252471924}, "Polygon": [{"X": 0.5986453890800476, "Y": 0.16090214252471924}, {"X": 0.6665230989456177, "Y": 0.16090266406536102}, {"X": 0.6665228009223938, "Y": 0.17175251245498657}, {"X": 0.5986450910568237, "Y": 0.1717519760131836}]}, "Id": "49cc3c78-ad94-46c6-a68b-aee4eb09240b", "Relationships": [{"Type": "CHILD", "Ids": ["87f8a11b-4ada-43ef-badc-05bfe6555ec0", "4899a42a-872d-4130-8b63-75ab82ba773a"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Transportation Company", "Geometry": {"BoundingBox": {"Width": 0.16851806640625, "Height": 0.010851090773940086, "Left": 0.7455465197563171, "Top": 0.15501612424850464}, "Polygon": [{"X": 0.745546817779541, "Y": 0.15501612424850464}, {"X": 0.9140645861625671, "Y": 0.1550174504518509}, {"X": 0.9140642285346985, "Y": 0.16586722433567047}, {"X": 0.7455465197563171, "Y": 0.1658659130334854}]}, "Id": "7b8fd3ae-0b1c-405d-8ff8-ffbe8189e254", "Relationships": [{"Type": "CHILD", "Ids": ["a41d06ff-0966-495c-8ef2-018b004de099", "0ed598f7-c63f-478e-b478-85e48b60f6be"]}]}, {"BlockType": "LINE", "Confidence": 99.89908599853516, "Text": "Date & Time", "Geometry": {"BoundingBox": {"Width": 0.08244684338569641, "Height": 0.008627290837466717, "Left": 0.04053718224167824, "Top": 0.16759923100471497}, "Polygon": [{"X": 0.04053732752799988, "Y": 0.16759923100471497}, {"X": 0.12298402190208435, "Y": 0.1675998717546463}, {"X": 0.12298386543989182, "Y": 0.17622652649879456}, {"X": 0.04053718224167824, "Y": 0.17622588574886322}]}, "Id": "c402d543-8717-4632-b94b-256ac60a883e", "Relationships": [{"Type": "CHILD", "Ids": ["bc115524-823a-460b-9a13-fd9e2aa604c6", "69255e2d-ff7c-42e9-a7dd-33521b4fc890", "d7aa9e21-13cd-4f72-9a10-7356c9e9743f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Equipment", "Geometry": {"BoundingBox": {"Width": 0.07228359580039978, "Height": 0.01074756495654583, "Left": 0.1885979324579239, "Top": 0.1675521731376648}, "Polygon": [{"X": 0.1885981410741806, "Y": 0.1675521731376648}, {"X": 0.26088154315948486, "Y": 0.16755272448062897}, {"X": 0.26088130474090576, "Y": 0.17829972505569458}, {"X": 0.1885979324579239, "Y": 0.1782991737127304}]}, "Id": "85862c50-2ee5-47ad-88f6-ee1d515aab47", "Relationships": [{"Type": "CHILD", "Ids": ["9a1f3550-f3ed-4c28-8177-ff4a0e772f8b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Empty", "Geometry": {"BoundingBox": {"Width": 0.04224119335412979, "Height": 0.010688474401831627, "Left": 0.4844607412815094, "Top": 0.16771122813224792}, "Polygon": [{"X": 0.4844610095024109, "Y": 0.16771122813224792}, {"X": 0.5267019271850586, "Y": 0.1677115559577942}, {"X": 0.5267016291618347, "Y": 0.1783996969461441}, {"X": 0.4844607412815094, "Y": 0.17839936912059784}]}, "Id": "9f957b42-cce4-4ec2-ab15-6b6c6b703d13", "Relationships": [{"Type": "CHILD", "Ids": ["5cd58bc3-1ce1-4bd6-b529-b0df7c9547a3"]}]}, {"BlockType": "LINE", "Confidence": 99.8974609375, "Text": "& Driver", "Geometry": {"BoundingBox": {"Width": 0.0533469058573246, "Height": 0.008557128719985485, "Left": 0.7461428046226501, "Top": 0.1676042228937149}, "Polygon": [{"X": 0.7461430430412292, "Y": 0.1676042228937149}, {"X": 0.7994897365570068, "Y": 0.16760464012622833}, {"X": 0.799489438533783, "Y": 0.17616136372089386}, {"X": 0.7461428046226501, "Y": 0.17616094648838043}]}, "Id": "99e9825d-c27c-4b9e-9bb1-441e7462e525", "Relationships": [{"Type": "CHILD", "Ids": ["31ff4776-8dfa-4827-aa5e-c4e886cadec3", "1b6062f4-7ab0-43ab-9cce-8eb90aa254d8"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "Out-Gate", "Geometry": {"BoundingBox": {"Width": 0.05277927964925766, "Height": 0.007777148392051458, "Left": 0.040364623069763184, "Top": 0.20011046528816223}, "Polygon": [{"X": 0.04036475718021393, "Y": 0.20011046528816223}, {"X": 0.09314390271902084, "Y": 0.20011086761951447}, {"X": 0.0931437611579895, "Y": 0.20788761973381042}, {"X": 0.040364623069763184, "Y": 0.2078872174024582}]}, "Id": "340ff39d-b6b4-4cda-a7e6-62a6b040e0f1", "Relationships": [{"Type": "CHILD", "Ids": ["3a66df9e-4912-4b8e-86f8-4659bdb5813a"]}]}, {"BlockType": "LINE", "Confidence": 99.4921875, "Text": "MCCZ 417372", "Geometry": {"BoundingBox": {"Width": 0.08537795394659042, "Height": 0.007727531716227531, "Left": 0.18848897516727448, "Top": 0.20005956292152405}, "Polygon": [{"X": 0.1884891241788864, "Y": 0.20005956292152405}, {"X": 0.2738669216632843, "Y": 0.20006021857261658}, {"X": 0.27386677265167236, "Y": 0.20778709650039673}, {"X": 0.18848897516727448, "Y": 0.2077864408493042}]}, "Id": "209ffcb5-93ea-4329-8e2c-92a2e94e8cf2", "Relationships": [{"Type": "CHILD", "Ids": ["9ccef23d-ea9a-44f1-905f-ee3591cec7e0", "b9067521-82df-40b4-97c4-068a273c44bf"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "000106", "Geometry": {"BoundingBox": {"Width": 0.04514642059803009, "Height": 0.007830976508557796, "Left": 0.33596861362457275, "Top": 0.20001420378684998}, "Polygon": [{"X": 0.3359687924385071, "Y": 0.20001420378684998}, {"X": 0.38111504912376404, "Y": 0.20001454651355743}, {"X": 0.3811148703098297, "Y": 0.20784518122673035}, {"X": 0.33596861362457275, "Y": 0.2078448385000229}]}, "Id": "75261bc2-f53e-46d0-92db-f57efe0f6202", "Relationships": [{"Type": "CHILD", "Ids": ["c12141ab-9249-4d39-9fef-0a86bba21fcf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Load", "Geometry": {"BoundingBox": {"Width": 0.028992094099521637, "Height": 0.007867890410125256, "Left": 0.48419904708862305, "Top": 0.19996051490306854}, "Polygon": [{"X": 0.4841992259025574, "Y": 0.19996051490306854}, {"X": 0.5131911039352417, "Y": 0.19996073842048645}, {"X": 0.5131909251213074, "Y": 0.20782840251922607}, {"X": 0.48419904708862305, "Y": 0.20782817900180817}]}, "Id": "ecfeccc8-96c3-4768-9cf0-a8fc3da89fe6", "Relationships": [{"Type": "CHILD", "Ids": ["ee594266-31ab-47e1-bcc4-ec042feb1311"]}]}, {"BlockType": "LINE", "Confidence": 91.93798065185547, "Text": "---", "Geometry": {"BoundingBox": {"Width": 0.01174247171729803, "Height": 0.0018301175441592932, "Left": 0.5975432395935059, "Top": 0.20348088443279266}, "Polygon": [{"X": 0.5975432991981506, "Y": 0.20348088443279266}, {"X": 0.6092857122421265, "Y": 0.20348097383975983}, {"X": 0.6092857122421265, "Y": 0.20531100034713745}, {"X": 0.5975432395935059, "Y": 0.2053109109401703}]}, "Id": "fe2ae27e-7da7-4d51-8b21-cd09ee76ba24", "Relationships": [{"Type": "CHILD", "Ids": ["b8b37549-070f-496f-afe9-cba864171bc8"]}]}, {"BlockType": "LINE", "Confidence": 99.8586196899414, "Text": "Desert Eagle Express Inc (DEEE)", "Geometry": {"BoundingBox": {"Width": 0.18996937572956085, "Height": 0.009959893301129341, "Left": 0.7462416291236877, "Top": 0.1997065395116806}, "Polygon": [{"X": 0.7462419271469116, "Y": 0.1997065395116806}, {"X": 0.9362109899520874, "Y": 0.1997079849243164}, {"X": 0.9362106323242188, "Y": 0.2096664309501648}, {"X": 0.7462416291236877, "Y": 0.2096649706363678}]}, "Id": "c37317e6-4ed7-4ac0-9a07-97b5b87ddf8d", "Relationships": [{"Type": "CHILD", "Ids": ["b782d261-b1c8-47a3-a9e3-379701512499", "753070d6-fcd6-4bb8-9920-da5bec2e3559", "46d5001a-8647-4eb1-bba9-468ee1689f1e", "1efb6931-0d34-4cd2-8b83-2cae1976e092", "fb11894a-9f1d-4d79-8696-6a2e61576f6e"]}]}, {"BlockType": "LINE", "Confidence": 99.521484375, "Text": "07/24/25 14:05", "Geometry": {"BoundingBox": {"Width": 0.1001027524471283, "Height": 0.007860642857849598, "Left": 0.04030829295516014, "Top": 0.21119286119937897}, "Polygon": [{"X": 0.040308427065610886, "Y": 0.21119286119937897}, {"X": 0.14041104912757874, "Y": 0.21119362115859985}, {"X": 0.1404109001159668, "Y": 0.2190535068511963}, {"X": 0.04030829295516014, "Y": 0.2190527319908142}]}, "Id": "35dfec05-783a-4a48-ace3-cdcfdbc0c74f", "Relationships": [{"Type": "CHILD", "Ids": ["41eda1ae-18d4-45c1-8135-681b0cdfa491", "05a82641-dded-4e21-a2d6-989c714585ac"]}]}, {"BlockType": "LINE", "Confidence": 99.89058685302734, "Text": "<PERSON><PERSON>, <PERSON>allah", "Geometry": {"BoundingBox": {"Width": 0.07773227989673615, "Height": 0.009050203487277031, "Left": 0.7455921173095703, "Top": 0.21112167835235596}, "Polygon": [{"X": 0.7455924153327942, "Y": 0.21112167835235596}, {"X": 0.8233243823051453, "Y": 0.2111222743988037}, {"X": 0.8233241438865662, "Y": 0.22017188370227814}, {"X": 0.7455921173095703, "Y": 0.22017128765583038}]}, "Id": "939c0711-9fee-42ab-b5a6-1ccebdfeeb03", "Relationships": [{"Type": "CHILD", "Ids": ["c07d29f0-5787-4df7-816c-61132d12c2c5", "56463ef8-1e5e-4709-93de-47e33b069795"]}]}, {"BlockType": "LINE", "Confidence": 99.97694396972656, "Text": "Damage is captured on recorded images at AGS locations", "Geometry": {"BoundingBox": {"Width": 0.34317681193351746, "Height": 0.00992098730057478, "Left": 0.03399395942687988, "Top": 0.26220962405204773}, "Polygon": [{"X": 0.033994127064943314, "Y": 0.26220962405204773}, {"X": 0.37717077136039734, "Y": 0.26221218705177307}, {"X": 0.37717053294181824, "Y": 0.2721306085586548}, {"X": 0.03399395942687988, "Y": 0.27212804555892944}]}, "Id": "f3a08ab2-97a3-4576-8777-64bb1fc11015", "Relationships": [{"Type": "CHILD", "Ids": ["e8041db5-a0d3-41e0-93aa-4a4e39b0986e", "c264ea2e-6957-419d-90d0-a64a7a092ccd", "4fca71a8-b3d0-4496-a057-4c10afbbdd01", "e5994bc5-3ae0-44f1-adee-40fc85c51da3", "7b5337a2-d098-4953-ab65-f1cb8dbf7ac4", "2f5c8672-fbb8-4035-a9e4-fed08800adc8", "865a0fbf-4c39-46dd-a066-401bd6584ae1", "a63447ed-76b3-4d84-ab92-7337da2598f1", "971501cb-d6d7-4360-9c16-972cb7f96539"]}]}, {"BlockType": "WORD", "Confidence": 31.470623016357422, "Text": "[ CSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0940861850976944, "Height": 0.031865958124399185, "Left": 0.03290572762489319, "Top": 0.024153364822268486}, "Polygon": [{"X": 0.032906267791986465, "Y": 0.024153364822268486}, {"X": 0.12699191272258759, "Y": 0.02415413409471512}, {"X": 0.12699131667613983, "Y": 0.05601932108402252}, {"X": 0.03290572762489319, "Y": 0.056018561124801636}]}, "Id": "4f90b7b9-3d0f-44b2-bbdd-a228eeb2dd0d"}, {"BlockType": "WORD", "Confidence": 92.48306274414062, "Text": "]", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011988402344286442, "Height": 0.032433513551950455, "Left": 0.14008095860481262, "Top": 0.023242410272359848}, "Polygon": [{"X": 0.14008156955242157, "Y": 0.023242410272359848}, {"X": 0.1520693600177765, "Y": 0.023242508992552757}, {"X": 0.15206874907016754, "Y": 0.055675920099020004}, {"X": 0.14008095860481262, "Y": 0.055675823241472244}]}, "Id": "8a3e9759-5081-4365-a059-78e3cc77f159"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022251993417739868, "Height": 0.012640238739550114, "Left": 0.17479710280895233, "Top": 0.02702164463698864}, "Polygon": [{"X": 0.17479735612869263, "Y": 0.02702164463698864}, {"X": 0.1970490962266922, "Y": 0.027021827176213264}, {"X": 0.1970488429069519, "Y": 0.03966188430786133}, {"X": 0.17479710280895233, "Y": 0.039661701768636703}]}, "Id": "77b3a316-db5c-4e95-a64c-11ee8fe26ea4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05620359629392624, "Height": 0.012882823124527931, "Left": 0.20753544569015503, "Top": 0.0268560778349638}, "Polygon": [{"X": 0.20753569900989532, "Y": 0.0268560778349638}, {"X": 0.26373904943466187, "Y": 0.02685653790831566}, {"X": 0.2637387812137604, "Y": 0.03973890095949173}, {"X": 0.20753544569015503, "Y": 0.03973844647407532}]}, "Id": "d0295384-14bb-40e2-8273-a88933c0baf7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09098125249147415, "Height": 0.012689901515841484, "Left": 0.2708582282066345, "Top": 0.02699410356581211}, "Polygon": [{"X": 0.270858496427536, "Y": 0.02699410356581211}, {"X": 0.3618394732475281, "Y": 0.026994846761226654}, {"X": 0.3618391752243042, "Y": 0.039684005081653595}, {"X": 0.2708582282066345, "Y": 0.0396832674741745}]}, "Id": "7219c5ed-64e6-4d51-a592-989fb97f32a3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07310036569833755, "Height": 0.014312557876110077, "Left": 0.7971082329750061, "Top": 0.02608403004705906}, "Polygon": [{"X": 0.7971087098121643, "Y": 0.02608403004705906}, {"X": 0.8702086210250854, "Y": 0.026084626093506813}, {"X": 0.8702081441879272, "Y": 0.04039658606052399}, {"X": 0.7971082329750061, "Y": 0.04039599373936653}]}, "Id": "1cec1c8e-9a0c-42e2-bdff-3d520029fdf1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "59th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03877054527401924, "Height": 0.011561717838048935, "Left": 0.876460075378418, "Top": 0.026115743443369865}, "Polygon": [{"X": 0.8764604330062866, "Y": 0.026115743443369865}, {"X": 0.9152306318283081, "Y": 0.026116058230400085}, {"X": 0.9152302145957947, "Y": 0.03767745941877365}, {"X": 0.876460075378418, "Y": 0.03767714649438858}]}, "Id": "9f65e1bd-8262-4a18-b54d-f7b2a38758a0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "St,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023151669651269913, "Height": 0.013038291595876217, "Left": 0.9211311936378479, "Top": 0.026517516002058983}, "Polygon": [{"X": 0.9211316108703613, "Y": 0.026517516002058983}, {"X": 0.9442828297615051, "Y": 0.026517704129219055}, {"X": 0.9442824125289917, "Y": 0.039555806666612625}, {"X": 0.9211311936378479, "Y": 0.0395556204020977}]}, "Id": "193e25f4-cfd2-4e4b-9b2f-af878de08795"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01567341759800911, "Height": 0.011106300167739391, "Left": 0.9504090547561646, "Top": 0.026439150795340538}, "Polygon": [{"X": 0.9504094123840332, "Y": 0.026439150795340538}, {"X": 0.9660824537277222, "Y": 0.026439279317855835}, {"X": 0.9660820960998535, "Y": 0.037545450031757355}, {"X": 0.9504090547561646, "Y": 0.03754532337188721}]}, "Id": "90f8bd14-4c67-42df-9c31-0a9dda13ace8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01912192441523075, "Height": 0.007592541165649891, "Left": 0.17516063153743744, "Top": 0.048506271094083786}, "Polygon": [{"X": 0.17516078054904938, "Y": 0.048506271094083786}, {"X": 0.19428256154060364, "Y": 0.04850642755627632}, {"X": 0.1942824125289917, "Y": 0.0560988113284111}, {"X": 0.17516063153743744, "Y": 0.056098658591508865}]}, "Id": "10c78e8f-ac18-4092-9a55-ac6e56ff77d0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "questions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05847861245274544, "Height": 0.009375151246786118, "Left": 0.19749346375465393, "Top": 0.048525843769311905}, "Polygon": [{"X": 0.19749365746974945, "Y": 0.048525843769311905}, {"X": 0.25597208738327026, "Y": 0.04852631688117981}, {"X": 0.25597187876701355, "Y": 0.05790099501609802}, {"X": 0.19749346375465393, "Y": 0.05790052190423012}]}, "Id": "d7c3ae23-ad57-46f9-b593-f0d0dc481652"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "call,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023290684446692467, "Height": 0.008917172439396381, "Left": 0.25965720415115356, "Top": 0.04819284379482269}, "Polygon": [{"X": 0.2596573829650879, "Y": 0.04819284379482269}, {"X": 0.2829478979110718, "Y": 0.048193033784627914}, {"X": 0.28294768929481506, "Y": 0.0571100153028965}, {"X": 0.25965720415115356, "Y": 0.05710982903838158}]}, "Id": "948b6893-d9cd-4d1c-9e30-cb3f38e8abe7"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "1-877-SHIPCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09154999256134033, "Height": 0.007772580720484257, "Left": 0.2872508466243744, "Top": 0.0483291931450367}, "Polygon": [{"X": 0.28725099563598633, "Y": 0.0483291931450367}, {"X": 0.3788008391857147, "Y": 0.04832993447780609}, {"X": 0.3788006603717804, "Y": 0.05610177293419838}, {"X": 0.2872508466243744, "Y": 0.056101035326719284}]}, "Id": "c0edb9b0-2053-4cdd-bc46-cd7ed487a1cf"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "FFAU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04524198919534683, "Height": 0.01097144652158022, "Left": 0.8466758728027344, "Top": 0.053304169327020645}, "Polygon": [{"X": 0.8466761708259583, "Y": 0.053304169327020645}, {"X": 0.8919178247451782, "Y": 0.053304534405469894}, {"X": 0.8919174671173096, "Y": 0.06427561491727829}, {"X": 0.8466758728027344, "Y": 0.06427524983882904}]}, "Id": "af1b7e34-677f-4521-b56d-3c679618d162"}, {"BlockType": "WORD", "Confidence": 99.580078125, "Text": "217080", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06801720708608627, "Height": 0.011034135706722736, "Left": 0.8975263833999634, "Top": 0.05329415202140808}, "Polygon": [{"X": 0.897526741027832, "Y": 0.05329415202140808}, {"X": 0.9655435681343079, "Y": 0.053294699639081955}, {"X": 0.9655432105064392, "Y": 0.06432828307151794}, {"X": 0.8975263833999634, "Y": 0.06432773917913437}]}, "Id": "4323e803-89eb-435f-8072-3f281187fb9d"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "PAGE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032724011689424515, "Height": 0.007651981431990862, "Left": 0.03389681875705719, "Top": 0.07556872814893723}, "Polygon": [{"X": 0.033896949142217636, "Y": 0.07556872814893723}, {"X": 0.0666208267211914, "Y": 0.07556898891925812}, {"X": 0.06662069261074066, "Y": 0.0832207053899765}, {"X": 0.03389681875705719, "Y": 0.08322044461965561}]}, "Id": "5cd0534e-1149-457e-ba5c-36ac7008cd9a"}, {"BlockType": "WORD", "Confidence": 99.17649841308594, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004761844873428345, "Height": 0.007298013661056757, "Left": 0.07061711698770523, "Top": 0.07553185522556305}, "Polygon": [{"X": 0.07061725109815598, "Y": 0.07553185522556305}, {"X": 0.07537896186113358, "Y": 0.07553189247846603}, {"X": 0.07537883520126343, "Y": 0.08282987028360367}, {"X": 0.07061711698770523, "Y": 0.08282983303070068}]}, "Id": "1b1bd022-4bfe-4292-9dbc-65c601d159fd"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "040L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029568659141659737, "Height": 0.00778276426717639, "Left": 0.8481225371360779, "Top": 0.07146593183279037}, "Polygon": [{"X": 0.848122775554657, "Y": 0.07146593183279037}, {"X": 0.8776912093162537, "Y": 0.07146617025136948}, {"X": 0.8776909112930298, "Y": 0.07924869656562805}, {"X": 0.8481225371360779, "Y": 0.07924845814704895}]}, "Id": "01b90eb3-d814-4c65-9fc5-606a931b30d2"}, {"BlockType": "WORD", "Confidence": 99.77379608154297, "Text": "0000W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04259447008371353, "Height": 0.0075882901437580585, "Left": 0.8810239434242249, "Top": 0.07151180505752563}, "Polygon": [{"X": 0.8810242414474487, "Y": 0.07151180505752563}, {"X": 0.9236184358596802, "Y": 0.0715121477842331}, {"X": 0.9236181974411011, "Y": 0.0791000947356224}, {"X": 0.8810239434242249, "Y": 0.07909975945949554}]}, "Id": "856de416-7c54-461b-a5f3-dafa92862ab8"}, {"BlockType": "WORD", "Confidence": 98.55767822265625, "Text": "0000H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039137642830610275, "Height": 0.007617599330842495, "Left": 0.9260165691375732, "Top": 0.0715293139219284}, "Polygon": [{"X": 0.9260168671607971, "Y": 0.0715293139219284}, {"X": 0.965154230594635, "Y": 0.07152963429689407}, {"X": 0.9651539921760559, "Y": 0.07914691418409348}, {"X": 0.9260165691375732, "Y": 0.0791466012597084}]}, "Id": "1e003ff3-d30a-4b43-ac64-52c3a56bbe2f"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "J1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017918357625603676, "Height": 0.010302451439201832, "Left": 0.39893224835395813, "Top": 0.12116830796003342}, "Polygon": [{"X": 0.39893248677253723, "Y": 0.12116830796003342}, {"X": 0.41685059666633606, "Y": 0.12116844952106476}, {"X": 0.41685035824775696, "Y": 0.13147075474262238}, {"X": 0.39893224835395813, "Y": 0.13147062063217163}]}, "Id": "4bf9681c-bada-45d5-af44-185c6ccaaee6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07255012542009354, "Height": 0.010431882925331593, "Left": 0.4250572919845581, "Top": 0.12107910215854645}, "Polygon": [{"X": 0.4250575304031372, "Y": 0.12107910215854645}, {"X": 0.49760740995407104, "Y": 0.12107967585325241}, {"X": 0.49760717153549194, "Y": 0.13151098787784576}, {"X": 0.4250572919845581, "Y": 0.1315104216337204}]}, "Id": "d630b6f4-f9e0-4350-bb63-7418e01df60d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DETAILS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07108505070209503, "Height": 0.010548721067607403, "Left": 0.5017507672309875, "Top": 0.12101078033447266}, "Polygon": [{"X": 0.5017510652542114, "Y": 0.12101078033447266}, {"X": 0.5728358030319214, "Y": 0.12101133912801743}, {"X": 0.5728355646133423, "Y": 0.13155950605869293}, {"X": 0.5017507672309875, "Y": 0.13155893981456757}]}, "Id": "444722fd-0534-4a6f-8957-af96d172b8be"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "(1)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022046778351068497, "Height": 0.01341647282242775, "Left": 0.5779170393943787, "Top": 0.12046661227941513}, "Polygon": [{"X": 0.5779173970222473, "Y": 0.12046661227941513}, {"X": 0.5999638438224792, "Y": 0.12046678364276886}, {"X": 0.5999634861946106, "Y": 0.13388308882713318}, {"X": 0.5779170393943787, "Y": 0.13388291001319885}]}, "Id": "4e41d8b9-336f-42d3-aed8-59aa803b3c64"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05092349275946617, "Height": 0.010725928470492363, "Left": 0.04012558236718178, "Top": 0.15502355992794037}, "Polygon": [{"X": 0.0401257649064064, "Y": 0.15502355992794037}, {"X": 0.09104907512664795, "Y": 0.1550239622592926}, {"X": 0.09104888141155243, "Y": 0.16574949026107788}, {"X": 0.04012558236718178, "Y": 0.16574910283088684}]}, "Id": "b8fd0642-8028-478c-8db0-1255e638acb6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Associated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07554110139608383, "Height": 0.008692550472915173, "Left": 0.18796482682228088, "Top": 0.15494760870933533}, "Polygon": [{"X": 0.1879650056362152, "Y": 0.15494760870933533}, {"X": 0.2635059356689453, "Y": 0.15494820475578308}, {"X": 0.263505756855011, "Y": 0.16364015638828278}, {"X": 0.18796482682228088, "Y": 0.16363957524299622}]}, "Id": "3a84e692-0fa7-46ea-a9be-edbe354e0523"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032758988440036774, "Height": 0.008635194040834904, "Left": 0.4842701554298401, "Top": 0.15494708716869354}, "Polygon": [{"X": 0.4842703640460968, "Y": 0.15494708716869354}, {"X": 0.5170291662216187, "Y": 0.15494734048843384}, {"X": 0.5170289278030396, "Y": 0.16358228027820587}, {"X": 0.4842701554298401, "Y": 0.16358202695846558}]}, "Id": "ecdfdf82-7385-4f3a-8f13-8929c3828dba"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01427541021257639, "Height": 0.006465971004217863, "Left": 0.5210938453674316, "Top": 0.15706363320350647}, "Polygon": [{"X": 0.5210939645767212, "Y": 0.15706363320350647}, {"X": 0.5353692173957825, "Y": 0.15706375241279602}, {"X": 0.5353690385818481, "Y": 0.16352960467338562}, {"X": 0.5210938453674316, "Y": 0.16352950036525726}]}, "Id": "9d1f7983-9e2a-41c7-b1c6-b9d78c69ec30"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Seal(s)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04685559123754501, "Height": 0.011060251854360104, "Left": 0.3359811305999756, "Top": 0.16046734154224396}, "Polygon": [{"X": 0.3359813690185547, "Y": 0.16046734154224396}, {"X": 0.3828367292881012, "Y": 0.1604677140712738}, {"X": 0.3828364610671997, "Y": 0.17152759432792664}, {"X": 0.3359811305999756, "Y": 0.17152723670005798}]}, "Id": "89648590-b1a3-450e-8e6f-b047f0b2d975"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05502678453922272, "Height": 0.010850272141397, "Left": 0.5986450910568237, "Top": 0.16090214252471924}, "Polygon": [{"X": 0.5986453890800476, "Y": 0.16090214252471924}, {"X": 0.6536718606948853, "Y": 0.16090255975723267}, {"X": 0.6536715626716614, "Y": 0.17175240814685822}, {"X": 0.5986450910568237, "Y": 0.1717519760131836}]}, "Id": "87f8a11b-4ada-43ef-badc-05bfe6555ec0"}, {"BlockType": "WORD", "Confidence": 99.7265625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008817948400974274, "Height": 0.008241461589932442, "Left": 0.6577051281929016, "Top": 0.16107556223869324}, "Polygon": [{"X": 0.6577053666114807, "Y": 0.16107556223869324}, {"X": 0.6665230989456177, "Y": 0.161075621843338}, {"X": 0.6665228605270386, "Y": 0.16931702196598053}, {"X": 0.6577051281929016, "Y": 0.16931694746017456}]}, "Id": "4899a42a-872d-4130-8b63-75ab82ba773a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transportation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1000494733452797, "Height": 0.010716957040131092, "Left": 0.7455465197563171, "Top": 0.15501612424850464}, "Polygon": [{"X": 0.745546817779541, "Y": 0.15501612424850464}, {"X": 0.8455960154533386, "Y": 0.1550169140100479}, {"X": 0.84559565782547, "Y": 0.16573308408260345}, {"X": 0.7455465197563171, "Y": 0.16573230922222137}]}, "Id": "a41d06ff-0966-495c-8ef2-018b004de099"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06449887901544571, "Height": 0.010810562409460545, "Left": 0.8495656847953796, "Top": 0.1550566554069519}, "Polygon": [{"X": 0.8495660424232483, "Y": 0.1550566554069519}, {"X": 0.9140645861625671, "Y": 0.1550571620464325}, {"X": 0.9140642285346985, "Y": 0.16586722433567047}, {"X": 0.8495656847953796, "Y": 0.16586671769618988}]}, "Id": "0ed598f7-c63f-478e-b478-85e48b60f6be"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03094051592051983, "Height": 0.008543560281395912, "Left": 0.04053718224167824, "Top": 0.16765189170837402}, "Polygon": [{"X": 0.04053732752799988, "Y": 0.16765189170837402}, {"X": 0.07147769629955292, "Y": 0.16765213012695312}, {"X": 0.07147754728794098, "Y": 0.17619545757770538}, {"X": 0.04053718224167824, "Y": 0.17619521915912628}]}, "Id": "bc115524-823a-460b-9a13-fd9e2aa604c6"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009535694494843483, "Height": 0.008444451726973057, "Left": 0.07558897137641907, "Top": 0.1677817851305008}, "Polygon": [{"X": 0.075589120388031, "Y": 0.1677817851305008}, {"X": 0.0851246640086174, "Y": 0.16778184473514557}, {"X": 0.08512451499700546, "Y": 0.17622622847557068}, {"X": 0.07558897137641907, "Y": 0.1762261539697647}]}, "Id": "69255e2d-ff7c-42e9-a7dd-33521b4fc890"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03449985384941101, "Height": 0.008601071313023567, "Left": 0.08848416805267334, "Top": 0.1675996035337448}, "Polygon": [{"X": 0.08848431706428528, "Y": 0.1675996035337448}, {"X": 0.12298402190208435, "Y": 0.1675998717546463}, {"X": 0.12298386543989182, "Y": 0.17620067298412323}, {"X": 0.08848416805267334, "Y": 0.17620040476322174}]}, "Id": "d7aa9e21-13cd-4f72-9a10-7356c9e9743f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07228359580039978, "Height": 0.01074756495654583, "Left": 0.1885979324579239, "Top": 0.1675521731376648}, "Polygon": [{"X": 0.1885981410741806, "Y": 0.1675521731376648}, {"X": 0.26088154315948486, "Y": 0.16755272448062897}, {"X": 0.26088130474090576, "Y": 0.17829972505569458}, {"X": 0.1885979324579239, "Y": 0.1782991737127304}]}, "Id": "9a1f3550-f3ed-4c28-8177-ff4a0e772f8b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04224119335412979, "Height": 0.010688474401831627, "Left": 0.4844607412815094, "Top": 0.16771122813224792}, "Polygon": [{"X": 0.4844610095024109, "Y": 0.16771122813224792}, {"X": 0.5267019271850586, "Y": 0.1677115559577942}, {"X": 0.5267016291618347, "Y": 0.1783996969461441}, {"X": 0.4844607412815094, "Y": 0.17839936912059784}]}, "Id": "5cd58bc3-1ce1-4bd6-b529-b0df7c9547a3"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009273377247154713, "Height": 0.008403067477047443, "Left": 0.7461428046226501, "Top": 0.16775794327259064}, "Polygon": [{"X": 0.7461430430412292, "Y": 0.16775794327259064}, {"X": 0.755416214466095, "Y": 0.1677580177783966}, {"X": 0.7554159164428711, "Y": 0.1761610209941864}, {"X": 0.7461428046226501, "Y": 0.17616094648838043}]}, "Id": "31ff4776-8dfa-4827-aa5e-c4e886cadec3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03970174491405487, "Height": 0.008390297181904316, "Left": 0.7597879767417908, "Top": 0.16760432720184326}, "Polygon": [{"X": 0.7597882151603699, "Y": 0.16760432720184326}, {"X": 0.7994897365570068, "Y": 0.16760464012622833}, {"X": 0.799489438533783, "Y": 0.1759946346282959}, {"X": 0.7597879767417908, "Y": 0.17599432170391083}]}, "Id": "1b6062f4-7ab0-43ab-9cce-8eb90aa254d8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Out-Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05277927964925766, "Height": 0.007777148392051458, "Left": 0.040364623069763184, "Top": 0.20011046528816223}, "Polygon": [{"X": 0.04036475718021393, "Y": 0.20011046528816223}, {"X": 0.09314390271902084, "Y": 0.20011086761951447}, {"X": 0.0931437611579895, "Y": 0.20788761973381042}, {"X": 0.040364623069763184, "Y": 0.2078872174024582}]}, "Id": "3a66df9e-4912-4b8e-86f8-4659bdb5813a"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "MCCZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0368376299738884, "Height": 0.007720545865595341, "Left": 0.18848897516727448, "Top": 0.20006617903709412}, "Polygon": [{"X": 0.1884891241788864, "Y": 0.20006617903709412}, {"X": 0.22532659769058228, "Y": 0.2000664621591568}, {"X": 0.22532643377780914, "Y": 0.20778672397136688}, {"X": 0.18848897516727448, "Y": 0.2077864408493042}]}, "Id": "9ccef23d-ea9a-44f1-905f-ee3591cec7e0"}, {"BlockType": "WORD", "Confidence": 99.267578125, "Text": "417372", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045398712158203125, "Height": 0.007722177542746067, "Left": 0.22846820950508118, "Top": 0.20005986094474792}, "Polygon": [{"X": 0.2284683734178543, "Y": 0.20005986094474792}, {"X": 0.2738669216632843, "Y": 0.20006021857261658}, {"X": 0.27386677265167236, "Y": 0.20778204500675201}, {"X": 0.22846820950508118, "Y": 0.20778170228004456}]}, "Id": "b9067521-82df-40b4-97c4-068a273c44bf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "000106", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04514642059803009, "Height": 0.007830976508557796, "Left": 0.33596861362457275, "Top": 0.20001420378684998}, "Polygon": [{"X": 0.3359687924385071, "Y": 0.20001420378684998}, {"X": 0.38111504912376404, "Y": 0.20001454651355743}, {"X": 0.3811148703098297, "Y": 0.20784518122673035}, {"X": 0.33596861362457275, "Y": 0.2078448385000229}]}, "Id": "c12141ab-9249-4d39-9fef-0a86bba21fcf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028992094099521637, "Height": 0.007867890410125256, "Left": 0.48419904708862305, "Top": 0.19996051490306854}, "Polygon": [{"X": 0.4841992259025574, "Y": 0.19996051490306854}, {"X": 0.5131911039352417, "Y": 0.19996073842048645}, {"X": 0.5131909251213074, "Y": 0.20782840251922607}, {"X": 0.48419904708862305, "Y": 0.20782817900180817}]}, "Id": "ee594266-31ab-47e1-bcc4-ec042feb1311"}, {"BlockType": "WORD", "Confidence": 91.93798065185547, "Text": "---", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01174247171729803, "Height": 0.0018301175441592932, "Left": 0.5975432395935059, "Top": 0.20348088443279266}, "Polygon": [{"X": 0.5975432991981506, "Y": 0.20348088443279266}, {"X": 0.6092857122421265, "Y": 0.20348097383975983}, {"X": 0.6092857122421265, "Y": 0.20531100034713745}, {"X": 0.5975432395935059, "Y": 0.2053109109401703}]}, "Id": "b8b37549-070f-496f-afe9-cba864171bc8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Desert", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0390268974006176, "Height": 0.007647139951586723, "Left": 0.7462416887283325, "Top": 0.20016855001449585}, "Polygon": [{"X": 0.7462418675422668, "Y": 0.20016855001449585}, {"X": 0.7852685451507568, "Y": 0.20016884803771973}, {"X": 0.7852683067321777, "Y": 0.20781569182872772}, {"X": 0.7462416887283325, "Y": 0.20781539380550385}]}, "Id": "b782d261-b1c8-47a3-a9e3-379701512499"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Eagle", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03230402618646622, "Height": 0.0096218790858984, "Left": 0.7886561751365662, "Top": 0.20000623166561127}, "Polygon": [{"X": 0.78865647315979, "Y": 0.20000623166561127}, {"X": 0.8209602236747742, "Y": 0.20000648498535156}, {"X": 0.8209599256515503, "Y": 0.2096281200647354}, {"X": 0.7886561751365662, "Y": 0.20962786674499512}]}, "Id": "753070d6-fcd6-4bb8-9920-da5bec2e3559"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Express", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04645179584622383, "Height": 0.009467472322285175, "Left": 0.8250511288642883, "Top": 0.20007319748401642}, "Polygon": [{"X": 0.8250513672828674, "Y": 0.20007319748401642}, {"X": 0.8715028762817383, "Y": 0.20007355511188507}, {"X": 0.8715025782585144, "Y": 0.20954066514968872}, {"X": 0.8250511288642883, "Y": 0.20954030752182007}]}, "Id": "46d5001a-8647-4eb1-bba9-468ee1689f1e"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017908472567796707, "Height": 0.00776668731123209, "Left": 0.8754616975784302, "Top": 0.2001526802778244}, "Polygon": [{"X": 0.8754619359970093, "Y": 0.2001526802778244}, {"X": 0.8933701515197754, "Y": 0.20015282928943634}, {"X": 0.8933699131011963, "Y": 0.20791937410831451}, {"X": 0.8754616975784302, "Y": 0.20791923999786377}]}, "Id": "1efb6931-0d34-4cd2-8b83-2cae1976e092"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "(DEEE)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03919152542948723, "Height": 0.009958736598491669, "Left": 0.8970194458961487, "Top": 0.19970768690109253}, "Polygon": [{"X": 0.8970198035240173, "Y": 0.19970768690109253}, {"X": 0.9362109899520874, "Y": 0.1997079849243164}, {"X": 0.9362106323242188, "Y": 0.2096664309501648}, {"X": 0.8970194458961487, "Y": 0.20966613292694092}]}, "Id": "fb11894a-9f1d-4d79-8696-6a2e61576f6e"}, {"BlockType": "WORD", "Confidence": 99.404296875, "Text": "07/24/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056925151497125626, "Height": 0.007858645170927048, "Left": 0.04030829295516014, "Top": 0.21119453012943268}, "Polygon": [{"X": 0.040308427065610886, "Y": 0.21119453012943268}, {"X": 0.09723344445228577, "Y": 0.2111949622631073}, {"X": 0.09723330289125443, "Y": 0.21905316412448883}, {"X": 0.04030829295516014, "Y": 0.2190527319908142}]}, "Id": "41eda1ae-18d4-45c1-8135-681b0cdfa491"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "14:05", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03274243697524071, "Height": 0.007677426561713219, "Left": 0.10766860842704773, "Top": 0.21119336783885956}, "Polygon": [{"X": 0.10766874998807907, "Y": 0.21119336783885956}, {"X": 0.14041104912757874, "Y": 0.21119362115859985}, {"X": 0.1404109001159668, "Y": 0.21887080371379852}, {"X": 0.10766860842704773, "Y": 0.21887055039405823}]}, "Id": "05a82641-dded-4e21-a2d6-989c714585ac"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "<PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02208215929567814, "Height": 0.00899757631123066, "Left": 0.7455921173095703, "Top": 0.211173877120018}, "Polygon": [{"X": 0.7455924153327942, "Y": 0.211173877120018}, {"X": 0.7676742672920227, "Y": 0.21117405593395233}, {"X": 0.7676740288734436, "Y": 0.22017145156860352}, {"X": 0.7455921173095703, "Y": 0.22017128765583038}]}, "Id": "c07d29f0-5787-4df7-816c-61132d12c2c5"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052334658801555634, "Height": 0.00764528289437294, "Left": 0.770989716053009, "Top": 0.21112187206745148}, "Polygon": [{"X": 0.7709899544715881, "Y": 0.21112187206745148}, {"X": 0.8233243823051453, "Y": 0.2111222743988037}, {"X": 0.8233241438865662, "Y": 0.21876715123653412}, {"X": 0.770989716053009, "Y": 0.21876674890518188}]}, "Id": "56463ef8-1e5e-4709-93de-47e33b069795"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04937145113945007, "Height": 0.009476909413933754, "Left": 0.03399396315217018, "Top": 0.2625681757926941}, "Polygon": [{"X": 0.033994123339653015, "Y": 0.2625681757926941}, {"X": 0.08336541056632996, "Y": 0.26256853342056274}, {"X": 0.08336524665355682, "Y": 0.2720450758934021}, {"X": 0.03399396315217018, "Y": 0.27204471826553345}]}, "Id": "e8041db5-a0d3-41e0-93aa-4a4e39b0986e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009701655246317387, "Height": 0.0077858357690274715, "Left": 0.0870528370141983, "Top": 0.26249557733535767}, "Polygon": [{"X": 0.08705297857522964, "Y": 0.26249557733535767}, {"X": 0.09675449132919312, "Y": 0.26249566674232483}, {"X": 0.09675434976816177, "Y": 0.27028143405914307}, {"X": 0.0870528370141983, "Y": 0.2702813446521759}]}, "Id": "c264ea2e-6957-419d-90d0-a64a7a092ccd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05234120041131973, "Height": 0.009693008847534657, "Left": 0.1003694087266922, "Top": 0.262358158826828}, "Polygon": [{"X": 0.10036958754062653, "Y": 0.262358158826828}, {"X": 0.15271061658859253, "Y": 0.26235857605934143}, {"X": 0.152710422873497, "Y": 0.2720511853694916}, {"X": 0.1003694087266922, "Y": 0.27205079793930054}]}, "Id": "4fca71a8-b3d0-4496-a057-4c10afbbdd01"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014703396707773209, "Height": 0.005870650988072157, "Left": 0.1564982682466507, "Top": 0.2643462121486664}, "Polygon": [{"X": 0.15649838745594025, "Y": 0.2643462121486664}, {"X": 0.1712016612291336, "Y": 0.26434630155563354}, {"X": 0.17120155692100525, "Y": 0.27021685242652893}, {"X": 0.1564982682466507, "Y": 0.2702167332172394}]}, "Id": "e5994bc5-3ae0-44f1-adee-40fc85c51da3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05240817740559578, "Height": 0.008075316436588764, "Left": 0.17494502663612366, "Top": 0.2622106671333313}, "Polygon": [{"X": 0.1749451756477356, "Y": 0.2622106671333313}, {"X": 0.22735320031642914, "Y": 0.26221105456352234}, {"X": 0.227353036403656, "Y": 0.2702859938144684}, {"X": 0.17494502663612366, "Y": 0.27028560638427734}]}, "Id": "7b5337a2-d098-4953-ab65-f1cb8dbf7ac4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0435430184006691, "Height": 0.009599319659173489, "Left": 0.2312457114458084, "Top": 0.26253050565719604}, "Polygon": [{"X": 0.23124590516090393, "Y": 0.26253050565719604}, {"X": 0.2747887372970581, "Y": 0.2625308334827423}, {"X": 0.2747885286808014, "Y": 0.2721298336982727}, {"X": 0.2312457114458084, "Y": 0.27212950587272644}]}, "Id": "2f5c8672-fbb8-4035-a9e4-fed08800adc8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011726605705916882, "Height": 0.007197526749223471, "Left": 0.2784808278083801, "Top": 0.26312753558158875}, "Polygon": [{"X": 0.27848097681999207, "Y": 0.26312753558158875}, {"X": 0.2902074158191681, "Y": 0.2631276249885559}, {"X": 0.29020726680755615, "Y": 0.27032506465911865}, {"X": 0.2784808278083801, "Y": 0.2703249752521515}]}, "Id": "865a0fbf-4c39-46dd-a066-401bd6584ae1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "AGS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02603032998740673, "Height": 0.007794052828103304, "Left": 0.2931026816368103, "Top": 0.26253852248191833}, "Polygon": [{"X": 0.29310286045074463, "Y": 0.26253852248191833}, {"X": 0.3191330134868622, "Y": 0.26253870129585266}, {"X": 0.31913286447525024, "Y": 0.27033257484436035}, {"X": 0.2931026816368103, "Y": 0.27033236622810364}]}, "Id": "a63447ed-76b3-4d84-ab92-7337da2598f1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "locations", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054331209510564804, "Height": 0.007993999868631363, "Left": 0.32283955812454224, "Top": 0.262326717376709}, "Polygon": [{"X": 0.32283973693847656, "Y": 0.262326717376709}, {"X": 0.37717077136039734, "Y": 0.2623271048069}, {"X": 0.377170592546463, "Y": 0.27032071352005005}, {"X": 0.32283955812454224, "Y": 0.2703202962875366}]}, "Id": "971501cb-d6d7-4360-9c16-972cb7f96539"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1de81543-a5ed-4adf-a407-232aeb6e0faf", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1de81543-a5ed-4adf-a407-232aeb6e0faf", "content-type": "application/x-amz-json-1.1", "content-length": "48560", "date": "Wed, 24 Sep 2025 13:32:22 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "6a504009-8427-4b10-87a8-4db7525b22e6", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:25 GMT", "content-type": "application/json", "content-length": "1396", "connection": "keep-alive", "x-amzn-requestid": "6a504009-8427-4b10-87a8-4db7525b22e6"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We need to determine document type. The content: \"CSX J1 GATE RECEIPT\" \"Chicago 59th St, IL\" \"For questions call, 1-877-SHIPCSX\" \"FFAU 217080\" \"PAGE 1\" \"040L 0000W 0000H\" \"J1 RECEIPT DETAILS (1)\" \"Activity Associated Load or Seal(s) Booking # Transportation Company Date & Time Equipment Empty & Driver Out-Gate MCCZ 417372 000106 Load --- Desert Eagle Express Inc (DEEE) 07/24/25 14:05 Adi, Abdallah Damage is captured on recorded images at AGS locations\".\n\nThis seems to be an outgate or ingate receipt? The header says \"J1 GATE RECEIPT\" and includes \"Out-Gate\" column. It says \"Out-Gate\". So it's likely an outgate document. The page shows \"Out-Gate\" as part of heading. This is a record of vehicle/container exiting a facility. So doc_type is outgate.\n\nCheck keywords: Outgate, Departed, Gate Out, etc. The page has \"Out-Gate\" term. So classify as outgate.\n\nThus output JSON with documents array containing page_no 1, doc_type \"outgate\".\n\nWe must use tool classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2734, "outputTokens": 311, "totalTokens": 3045}, "metrics": {"latencyMs": 1414}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7f42ccf2_BJYFRYT0V8O045HGDXJ6.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n[ CSX\n]\nJ1 GATE RECEIPT\nChicago 59th St, IL\nFor questions call, 1-877-SHIPCSX\nFFAU 217080\nPAGE 1\n040L 0000W 0000H\nJ1 RECEIPT DETAILS (1)\nActivity\nAssociated\nLoad or\nSeal(s)\nBooking #\nTransportation Company\nDate & Time\nEquipment\nEmpty\n& Driver\nOut-Gate\nMCCZ 417372\n000106\nLoad\n---\nDesert Eagle Express Inc (DEEE)\n07/24/25 14:05\nAdi, Abdallah\nDamage is captured on recorded images at AGS locations\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}