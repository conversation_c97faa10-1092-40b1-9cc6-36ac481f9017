2025-09-23 17:14:35,199 - INFO - Logging initialized. Log file: logs/test_classification_20250923_171435.log
2025-09-23 17:14:35,200 - INFO - 📁 Found 1 files to process
2025-09-23 17:14:35,200 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 17:14:35,200 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 17:14:35,200 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 17:14:35,200 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 17:14:35,201 - INFO - ⬆️ [17:14:35] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:14:43,172 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/a045ebe7_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:14:43,173 - INFO - 🔍 [17:14:43] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:14:43,173 - INFO - Initializing TextractProcessor...
2025-09-23 17:14:43,184 - INFO - Initializing BedrockProcessor...
2025-09-23 17:14:43,189 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a045ebe7_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:14:43,189 - INFO - Processing PDF from S3...
2025-09-23 17:14:43,190 - INFO - Downloading PDF from S3 to /tmp/tmpcvx0_20e/a045ebe7_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:14:45,982 - INFO - Downloaded PDF size: 1.1 MB
2025-09-23 17:14:45,982 - INFO - Splitting PDF into individual pages...
2025-09-23 17:14:45,986 - INFO - Splitting PDF a045ebe7_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 17:14:46,000 - INFO - Split PDF into 7 pages
2025-09-23 17:14:46,000 - INFO - Processing pages with Textract in parallel...
2025-09-23 17:14:46,000 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 17:14:51,130 - INFO - Page 4: Extracted 1151 characters, 63 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_004.pdf
2025-09-23 17:14:51,131 - INFO - Successfully processed page 4
2025-09-23 17:14:51,153 - INFO - Page 5: Extracted 2022 characters, 27 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_005.pdf
2025-09-23 17:14:51,154 - INFO - Successfully processed page 5
2025-09-23 17:14:51,280 - INFO - Page 1: Extracted 1168 characters, 45 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_001.pdf
2025-09-23 17:14:51,280 - INFO - Successfully processed page 1
2025-09-23 17:14:51,337 - INFO - Page 2: Extracted 1174 characters, 29 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_002.pdf
2025-09-23 17:14:51,338 - INFO - Successfully processed page 2
2025-09-23 17:14:51,603 - INFO - Page 3: Extracted 929 characters, 64 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_003.pdf
2025-09-23 17:14:51,603 - INFO - Successfully processed page 3
2025-09-23 17:14:52,620 - INFO - Page 6: Extracted 4159 characters, 115 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_006.pdf
2025-09-23 17:14:52,620 - INFO - Successfully processed page 6
2025-09-23 17:14:54,614 - INFO - Page 7: Extracted 788 characters, 55 lines from a045ebe7_Purchase_Order_VRSD0E9045781_d9b8140c_page_007.pdf
2025-09-23 17:14:54,615 - INFO - Successfully processed page 7
2025-09-23 17:14:54,615 - INFO - Combined 7 pages into final text
2025-09-23 17:14:54,616 - INFO - Text validation for a045ebe7_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 17:14:54,617 - INFO - Analyzing document types with Bedrock...
2025-09-23 17:14:54,617 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 17:14:59,689 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-23 17:14:59,692 - ERROR - Processing failed for s3://document-extraction-logistically/temp/a045ebe7_Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:14:59,693 - ERROR - ✗ Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:15:00,816 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a045ebe7_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:15:00,817 - INFO - 
📊 Processing Summary:
2025-09-23 17:15:00,818 - INFO -    Total files: 1
2025-09-23 17:15:00,818 - INFO -    Successful: 0
2025-09-23 17:15:00,818 - INFO -    Failed: 1
2025-09-23 17:15:00,818 - INFO -    Duration: 25.62 seconds
2025-09-23 17:15:00,818 - INFO -    Output directory: output
2025-09-23 17:15:00,818 - ERROR - 
❌ Errors:
2025-09-23 17:15:00,818 - ERROR -    Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:15:00,819 - INFO - 
📋 No files were successfully processed.
2025-09-23 17:15:00,819 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 25.61677, 'processed_files': []}
