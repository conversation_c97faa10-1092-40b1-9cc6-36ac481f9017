2025-09-24 15:14:19,557 - INFO - Logging initialized. Log file: logs/test_classification_20250924_151419.log
2025-09-24 15:14:19,557 - INFO - 📁 Found 1 files to process
2025-09-24 15:14:19,557 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:14:19,557 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 15:14:19,557 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 15:14:19,557 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 15:14:19,557 - INFO - ⬆️ [15:14:19] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:22,390 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/e1fb7f16_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:22,390 - INFO - 🔍 [15:14:22] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:22,391 - INFO - Initializing TextractProcessor...
2025-09-24 15:14:22,404 - INFO - Initializing BedrockProcessor...
2025-09-24 15:14:22,413 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e1fb7f16_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:22,413 - INFO - Processing PDF from S3...
2025-09-24 15:14:22,414 - INFO - Downloading PDF from S3 to /tmp/tmp3y6bsnxl/e1fb7f16_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:24,898 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:14:24,898 - INFO - Splitting PDF into individual pages...
2025-09-24 15:14:24,900 - INFO - Splitting PDF e1fb7f16_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:14:24,903 - INFO - Split PDF into 1 pages
2025-09-24 15:14:24,903 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:14:24,903 - INFO - Expected pages: [1]
2025-09-24 15:14:32,431 - INFO - Page 1: Extracted 939 characters, 64 lines from e1fb7f16_A34CDFDJ66EDOZEKZWJL_bc574236_page_001.pdf
2025-09-24 15:14:32,432 - INFO - Successfully processed page 1
2025-09-24 15:14:32,432 - INFO - Combined 1 pages into final text
2025-09-24 15:14:32,432 - INFO - Text validation for e1fb7f16_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:14:32,432 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:14:32,432 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:14:34,858 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8c9b693a-bd19-4537-9fbf-152a5785ab20', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:44:34 GMT', 'content-type': 'application/json', 'content-length': '1441', 'connection': 'keep-alive', 'x-amzn-requestid': '8c9b693a-bd19-4537-9fbf-152a5785ab20'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. Determine type. Document appears to be "Inspection Certificate". Keywords: "Inspection Certificate" at top. Also has weight, dimensions, etc. That matches weight_and_inspection_cert or inspection_cert. Need to determine if weight_and_inspection_cert or inspection_cert.\n\nWe look at definition: weight_and_inspection_cert: certificate confirming weight possibly with length, width, height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I. And note: strictly check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise, inspection_cert.\n\nThe page has Weight(Lbs) and dimensions, so it\'s weight_and_inspection_cert. There\'s no NMFC code mention. So classify as weight_and_inspection_cert.\n\nThus output JSON via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 238, 'totalTokens': 3064}, 'metrics': {'latencyMs': 1127}}
2025-09-24 15:14:34,859 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e1fb7f16_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:34,881 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:14:34,881 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:14:36,011 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e1fb7f16_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:36,011 - INFO - 
📊 Processing Summary:
2025-09-24 15:14:36,011 - INFO -    Total files: 1
2025-09-24 15:14:36,011 - INFO -    Successful: 1
2025-09-24 15:14:36,011 - INFO -    Failed: 0
2025-09-24 15:14:36,011 - INFO -    Duration: 16.45 seconds
2025-09-24 15:14:36,011 - INFO -    Output directory: output
2025-09-24 15:14:36,011 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:14:36,011 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:14:36,011 - INFO - 
============================================================================================================================================
2025-09-24 15:14:36,012 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:14:36,012 - INFO - ============================================================================================================================================
2025-09-24 15:14:36,012 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:14:36,012 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:14:36,012 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:14:36,012 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:14:36,012 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:14:36,012 - INFO - 
2025-09-24 15:14:36,012 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:14:36,012 - INFO - Total entries: 1
2025-09-24 15:14:36,012 - INFO - ============================================================================================================================================
2025-09-24 15:14:36,012 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:14:36,012 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:14:36,012 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:14:36,012 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:14:36,012 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 16.453721, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
