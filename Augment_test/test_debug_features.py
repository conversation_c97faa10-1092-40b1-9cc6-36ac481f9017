#!/usr/bin/env python3
"""
Test script to verify debug features work correctly.
This script intentionally creates errors to test the debug configuration.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

from test_classification import FileProcessor


async def test_error_handling_with_debug():
    """Test error handling with debug features enabled."""
    print("🧪 Testing Error Handling with Debug Features")
    print("=" * 50)
    
    # Enable debug mode
    os.environ['DEBUG_ON_ERROR'] = 'true'
    print("✅ Debug mode enabled")
    
    # Create processor
    processor = FileProcessor("Augment_test/test_output", "Augment_test/test_logs")
    
    # Test 1: Try to process a non-existent folder
    print("\n📁 Test 1: Processing non-existent folder")
    try:
        stats = await processor.process_files("/non/existent/folder", max_files=1)
        print(f"Unexpected success: {stats}")
    except Exception as e:
        print(f"✅ Expected error caught: {e}")
    
    # Test 2: Try to process a folder with no supported files
    print("\n📁 Test 2: Processing folder with no supported files")
    try:
        # Create a temporary folder with unsupported files
        temp_dir = Path("Augment_test/temp_test")
        temp_dir.mkdir(exist_ok=True)
        
        # Create some unsupported files
        (temp_dir / "test.txt").write_text("test")
        (temp_dir / "test.json").write_text('{"test": "data"}')
        
        stats = await processor.process_files(str(temp_dir), max_files=1)
        print(f"Result: {stats}")
        
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n✅ Error handling test completed")


def test_debug_flags():
    """Test the debug command line flags."""
    print("\n🧪 Testing Debug Command Line Flags")
    print("=" * 50)
    
    # Test the argument parser with debug flags
    import argparse
    
    # Simulate the argument parser from test_classification.py
    parser = argparse.ArgumentParser(description='Test document classification with S3 upload/download')
    parser.add_argument('folder_path', help='Path to folder containing files to classify')
    parser.add_argument('--max-files', type=int, default=None,
                       help='Maximum number of files to process (default: all files)')
    parser.add_argument('--output-dir', default='output',
                       help='Output directory for results (default: output)')
    parser.add_argument('--run-number', type=int, default=1,
                       help='Run number for output file naming (default: 1)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode with post-mortem debugging on errors')
    parser.add_argument('--debug-on-start', action='store_true',
                       help='Start debugger immediately at the beginning')
    
    # Test different argument combinations
    test_args = [
        ['test_folder'],
        ['test_folder', '--debug'],
        ['test_folder', '--debug-on-start'],
        ['test_folder', '--debug', '--debug-on-start', '--max-files', '5'],
    ]
    
    for args in test_args:
        try:
            parsed = parser.parse_args(args)
            print(f"✅ Args: {args}")
            print(f"   Parsed: debug={getattr(parsed, 'debug', False)}, debug_on_start={getattr(parsed, 'debug_on_start', False)}")
        except SystemExit:
            print(f"❌ Args: {args} - Failed to parse")
    
    print("\n✅ Debug flags test completed")


def test_import_paths():
    """Test the import paths and module loading."""
    print("\n🧪 Testing Import Paths and Module Loading")
    print("=" * 50)
    
    # Check sys.path
    print("Python sys.path:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    # Check PYTHONPATH
    pythonpath = os.getenv('PYTHONPATH', '')
    print(f"\nPYTHONPATH: {pythonpath}")
    
    # Test importing the classification function
    try:
        from test_classification import llm_classification
        print(f"\n✅ Successfully imported llm_classification: {llm_classification}")
        print(f"   Function module: {llm_classification.__module__}")
        print(f"   Function file: {getattr(llm_classification, '__code__', {}).co_filename}")
    except Exception as e:
        print(f"\n❌ Failed to import llm_classification: {e}")
    
    # Test importing other modules
    modules_to_test = [
        'boto3',
        'dotenv',
        'asyncio',
        'concurrent.futures',
    ]
    
    print(f"\nTesting other required modules:")
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
    
    print("\n✅ Import paths test completed")


async def main():
    """Run all debug feature tests."""
    print("🚀 Testing Debug Features for test_classification.py")
    print("=" * 60)
    
    # Test 1: Debug flags
    test_debug_flags()
    
    # Test 2: Import paths
    test_import_paths()
    
    # Test 3: Error handling with debug (commented out to avoid actual debugging session)
    # await test_error_handling_with_debug()
    print("\n🧪 Error Handling Test")
    print("=" * 50)
    print("⚠ Error handling test skipped to avoid entering debug session")
    print("To test error handling:")
    print("1. Uncomment the test_error_handling_with_debug() call above")
    print("2. Run this script again")
    print("3. The script will enter debug mode when errors occur")
    
    print(f"\n🎉 Debug feature tests completed!")
    print(f"\n📋 Summary:")
    print(f"✅ Debug command line flags are working")
    print(f"✅ Import paths are configured correctly")
    print(f"✅ Classification function is importable")
    print(f"✅ All required modules are available")
    
    print(f"\n🐛 To use debug features:")
    print(f"1. In VS Code: Use Run and Debug (Ctrl+Shift+D)")
    print(f"2. Command line: Add --debug or --debug-on-start flags")
    print(f"3. Environment: Set DEBUG_ON_ERROR=true")


if __name__ == "__main__":
    asyncio.run(main())
