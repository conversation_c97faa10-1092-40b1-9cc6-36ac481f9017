{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9999999403953552, "Height": 1.0, "Left": 4.949992771230427e-08, "Top": 0.0}, "Polygon": [{"X": 7.87358942488936e-08, "Y": 6.834374843833757e-09}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 4.949992771230427e-08, "Y": 1.0}]}, "Id": "67980e65-6ee1-4731-b59b-51aa8d9cce32", "Relationships": [{"Type": "CHILD", "Ids": ["2a498e47-de40-4228-b16f-cda8a05d2b1e", "74a1f848-474f-4b55-b5f9-f21f4bb57071", "beb230b7-84df-4336-8994-91eefe4890d2", "b3a88166-05c7-44f9-8e10-cfa08e9c386b", "c35ef27b-46a6-4de2-8438-a2b0286b6e6f", "38103bb7-d503-4da8-b4cc-d3a7f566fc7c", "a0c7d546-9e21-4159-8625-4f029eba7522", "a4531ada-4f2e-42c9-a76b-99bc4cfa11b8", "3d2c7fbc-9c99-4903-a7b3-2da477fe2e77", "4bd8fbd3-9d19-45e9-9956-d8ec7b0c4263", "bd42f028-b2be-4ae7-8e24-71eb5400950c", "28a3e967-4317-44fa-b4ab-6c770b98d78a", "9e15a946-3ffd-45c8-8dd9-c7e165f27e5f", "a223ec42-6ef0-4a4c-ba4a-1a7a9560471a", "bd635db3-f143-4dae-9393-bf94fb61f08c", "113277e4-aa20-49e2-a054-241b170f2489", "6b543867-d59a-4793-b787-e1448f1c06ed", "0c153816-ba75-4a3d-aac4-3ae47acb0bc1", "2c0b3d16-1656-404e-9496-9f29795c1534", "faa661aa-e28a-40cb-a42d-224ef03dc6f3", "96c903e2-0d2e-4e60-ac96-f3a75f8a149e", "4fe19c88-e1e5-4141-bb4c-238c403eab6a", "55edd543-5c8b-465c-a745-cc83d8cd0fc4", "f8e9cb66-d963-4152-9f66-500df566fb05", "4557197b-5487-4052-a9c4-a642a76d4039", "e5d0690f-4fe1-4019-8e8c-5799b263d9e1", "fc4c9348-ac6e-4297-a8db-6109be18a9fc", "af972a80-394e-4576-9cb6-399021674b28", "150a19aa-64ca-430c-9e43-dd74f469ffbc", "891cbdd6-f3c3-4701-be18-2d2ba24d4eb2"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "<PERSON><PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.1373581886291504, "Height": 0.014732122421264648, "Left": 0.4494950473308563, "Top": 0.07228734344244003}, "Polygon": [{"X": 0.4494951069355011, "Y": 0.07228788733482361}, {"X": 0.5868532061576843, "Y": 0.07228734344244003}, {"X": 0.5868531465530396, "Y": 0.0870189294219017}, {"X": 0.4494950473308563, "Y": 0.08701946586370468}]}, "Id": "2a498e47-de40-4228-b16f-cda8a05d2b1e", "Relationships": [{"Type": "CHILD", "Ids": ["5a183304-484b-46ee-b46f-c5905e6ca956", "51172863-eb27-4551-a316-76c30d919373"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "TForce", "Geometry": {"BoundingBox": {"Width": 0.25794869661331177, "Height": 0.03545314818620682, "Left": 0.0924140065908432, "Top": 0.08627042919397354}, "Polygon": [{"X": 0.09241403639316559, "Y": 0.08627142757177353}, {"X": 0.3503626883029938, "Y": 0.08627042919397354}, {"X": 0.3503625690937042, "Y": 0.12172261625528336}, {"X": 0.0924140065908432, "Y": 0.12172356992959976}]}, "Id": "74a1f848-474f-4b55-b5f9-f21f4bb57071", "Relationships": [{"Type": "CHILD", "Ids": ["77e3f322-6090-4936-aa0a-620bf7a2dc81"]}]}, {"BlockType": "LINE", "Confidence": 99.82807159423828, "Text": "P.O. Box 1216", "Geometry": {"BoundingBox": {"Width": 0.10352131724357605, "Height": 0.010270725004374981, "Left": 0.44967931509017944, "Top": 0.08917700499296188}, "Polygon": [{"X": 0.4496793746948242, "Y": 0.08917740732431412}, {"X": 0.5532006621360779, "Y": 0.08917700499296188}, {"X": 0.5532006025314331, "Y": 0.0994473397731781}, {"X": 0.44967931509017944, "Y": 0.09944773465394974}]}, "Id": "beb230b7-84df-4336-8994-91eefe4890d2", "Relationships": [{"Type": "CHILD", "Ids": ["74596354-fc55-4aff-b711-0719ba313300", "372a8159-7266-42e1-89b8-43870327adeb", "953e2899-0a20-4484-8f7c-57068894b857"]}]}, {"BlockType": "LINE", "Confidence": 99.72935485839844, "Text": "Richmond, Virginia 23218-1216", "Geometry": {"BoundingBox": {"Width": 0.22786398231983185, "Height": 0.012532331049442291, "Left": 0.4499940872192383, "Top": 0.10307473689317703}, "Polygon": [{"X": 0.44999414682388306, "Y": 0.10307560861110687}, {"X": 0.6778580546379089, "Y": 0.10307473689317703}, {"X": 0.6778579950332642, "Y": 0.11560621112585068}, {"X": 0.4499940872192383, "Y": 0.11560706794261932}]}, "Id": "b3a88166-05c7-44f9-8e10-cfa08e9c386b", "Relationships": [{"Type": "CHILD", "Ids": ["ca278759-5e1e-4355-99fb-abd31dfdbac8", "6ea8df65-1300-4b18-9c15-3ad80a1419c9", "dbf39ccf-f0eb-45e3-9e2a-2ecfad87f8c1"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1-800-333-7400", "Geometry": {"BoundingBox": {"Width": 0.11509879678487778, "Height": 0.00992684904485941, "Left": 0.4501526951789856, "Top": 0.11739657819271088}, "Polygon": [{"X": 0.450152724981308, "Y": 0.1173970103263855}, {"X": 0.5652514696121216, "Y": 0.11739657819271088}, {"X": 0.5652514100074768, "Y": 0.1273230016231537}, {"X": 0.4501526951789856, "Y": 0.1273234337568283}]}, "Id": "c35ef27b-46a6-4de2-8438-a2b0286b6e6f", "Relationships": [{"Type": "CHILD", "Ids": ["98e940bf-87c8-4a51-bb91-5fda8ca51768"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Freight", "Geometry": {"BoundingBox": {"Width": 0.12150909751653671, "Height": 0.019270051270723343, "Left": 0.21730205416679382, "Top": 0.12642861902713776}, "Polygon": [{"X": 0.2173020988702774, "Y": 0.12642906606197357}, {"X": 0.33881115913391113, "Y": 0.12642861902713776}, {"X": 0.33881109952926636, "Y": 0.14569823443889618}, {"X": 0.21730205416679382, "Y": 0.145698681473732}]}, "Id": "38103bb7-d503-4da8-b4cc-d3a7f566fc7c", "Relationships": [{"Type": "CHILD", "Ids": ["8f2c11f2-3f7a-45f6-b6d0-e670ec7e91cf"]}]}, {"BlockType": "LINE", "Confidence": 85.22261047363281, "Text": "https://www.tforcefreight.com", "Geometry": {"BoundingBox": {"Width": 0.21084065735340118, "Height": 0.012840968556702137, "Left": 0.4485003650188446, "Top": 0.1314457803964615}, "Polygon": [{"X": 0.448500394821167, "Y": 0.13144655525684357}, {"X": 0.6593409776687622, "Y": 0.1314457803964615}, {"X": 0.6593409180641174, "Y": 0.14428599178791046}, {"X": 0.4485003650188446, "Y": 0.14428675174713135}]}, "Id": "a0c7d546-9e21-4159-8625-4f029eba7522", "Relationships": [{"Type": "CHILD", "Ids": ["2711f158-46bc-4301-ad04-48e3e69f35d2"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "WEIGHT CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.17543253302574158, "Height": 0.010097984224557877, "Left": 0.44886475801467896, "Top": 0.16050425171852112}, "Polygon": [{"X": 0.44886481761932373, "Y": 0.16050487756729126}, {"X": 0.6242973208427429, "Y": 0.16050425171852112}, {"X": 0.6242972016334534, "Y": 0.17060163617134094}, {"X": 0.44886475801467896, "Y": 0.1706022471189499}]}, "Id": "a4531ada-4f2e-42c9-a76b-99bc4cfa11b8", "Relationships": [{"Type": "CHILD", "Ids": ["0c259597-237a-4ef3-9e02-a67f1fd97464", "03df8478-6db0-46a0-a6d4-55f57724e73e"]}]}, {"BlockType": "LINE", "Confidence": 99.9255599975586, "Text": "Freight Bill Number: 465486206", "Geometry": {"BoundingBox": {"Width": 0.2300592064857483, "Height": 0.012277976609766483, "Left": 0.09271377325057983, "Top": 0.1747279316186905}, "Polygon": [{"X": 0.09271378070116043, "Y": 0.17472873628139496}, {"X": 0.3227729797363281, "Y": 0.1747279316186905}, {"X": 0.32277294993400574, "Y": 0.18700513243675232}, {"X": 0.09271377325057983, "Y": 0.1870059072971344}]}, "Id": "3d2c7fbc-9c99-4903-a7b3-2da477fe2e77", "Relationships": [{"Type": "CHILD", "Ids": ["9deddfea-2e4a-4265-b240-ed92a0d1888f", "d90f1646-f3b8-4a8b-9995-2c766e8ed998", "070b4ee5-2c6c-41fc-83c3-0fe6a8ef6161", "620951df-cae2-46bf-9d4d-a1c95a66edf9"]}]}, {"BlockType": "LINE", "Confidence": 99.98697662353516, "Text": "Weighed Date: 9/8/2025", "Geometry": {"BoundingBox": {"Width": 0.17658570408821106, "Height": 0.012469016946852207, "Left": 0.6771246790885925, "Top": 0.17456121742725372}, "Polygon": [{"X": 0.6771247386932373, "Y": 0.17456182837486267}, {"X": 0.8537103533744812, "Y": 0.17456121742725372}, {"X": 0.8537102937698364, "Y": 0.1870296448469162}, {"X": 0.6771246790885925, "Y": 0.18703024089336395}]}, "Id": "4bd8fbd3-9d19-45e9-9956-d8ec7b0c4263", "Relationships": [{"Type": "CHILD", "Ids": ["d45684ca-0a5c-4541-a079-9de8488c8761", "a8585c01-18af-4175-b609-f77df25c44ec", "aa3d13ca-5865-4f63-a0c5-325cc41261ce"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "<PERSON><PERSON><PERSON> performed at KAN Service Center", "Geometry": {"BoundingBox": {"Width": 0.3167237937450409, "Height": 0.012516752816736698, "Left": 0.09172104299068451, "Top": 0.1889001876115799}, "Polygon": [{"X": 0.0917210578918457, "Y": 0.18890126049518585}, {"X": 0.4084448516368866, "Y": 0.1889001876115799}, {"X": 0.4084447920322418, "Y": 0.20141589641571045}, {"X": 0.09172104299068451, "Y": 0.20141693949699402}]}, "Id": "bd42f028-b2be-4ae7-8e24-71eb5400950c", "Relationships": [{"Type": "CHILD", "Ids": ["044321a3-acb2-41ae-84db-422f1cd7386a", "b83ddf91-e75b-441b-86f3-44e5c26a0182", "19e6989e-61f8-4c73-8107-5639d3699cb4", "5d05ede2-9a69-4e3e-88b0-b3d74422340e", "bdfefdd7-3bde-48c3-ba01-e2a4304bb2a3", "f93396ef-3e24-45ea-9a14-446dcc2714bb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "WEIGHT CERTIFICATION", "Geometry": {"BoundingBox": {"Width": 0.1935378462076187, "Height": 0.010060454718768597, "Left": 0.09195930510759354, "Top": 0.2161771059036255}, "Polygon": [{"X": 0.09195931255817413, "Y": 0.21617773175239563}, {"X": 0.28549715876579285, "Y": 0.2161771059036255}, {"X": 0.28549712896347046, "Y": 0.22623693943023682}, {"X": 0.09195930510759354, "Y": 0.22623756527900696}]}, "Id": "28a3e967-4317-44fa-b4ab-6c770b98d78a", "Relationships": [{"Type": "CHILD", "Ids": ["23dd3d0a-612f-4854-b866-d0ba7fd7e497", "b5d4baea-a43c-4b8b-81f7-822208a833d5"]}]}, {"BlockType": "LINE", "Confidence": 99.98210144042969, "Text": "We certify that the mentioned shipment has been weighed and that the correct weight as stated is the true and", "Geometry": {"BoundingBox": {"Width": 0.808756947517395, "Height": 0.01248771883547306, "Left": 0.09348776936531067, "Top": 0.2446107566356659}, "Polygon": [{"X": 0.09348778426647186, "Y": 0.24461324512958527}, {"X": 0.9022447466850281, "Y": 0.2446107566356659}, {"X": 0.9022446274757385, "Y": 0.2570960223674774}, {"X": 0.09348776936531067, "Y": 0.2570984661579132}]}, "Id": "9e15a946-3ffd-45c8-8dd9-c7e165f27e5f", "Relationships": [{"Type": "CHILD", "Ids": ["057212e4-358f-49f9-8592-51694b2f9835", "74c06e01-e231-46a9-8058-6d2bbddb429f", "dc5a13ab-49d2-4d7f-a11e-87a0f092dc9b", "b0353970-65a8-428f-95be-8d0c92d05544", "b6c4830a-01ab-4d42-9a32-08fc98af0974", "829ebff7-ac15-4ba2-9f27-11240f3d037f", "d2579552-1362-4df7-b6ea-9c85416e8f91", "067835dd-dfff-41f7-adf6-aa1f67a75723", "cb0c04a2-cfa2-4edf-a6fe-833b72c5b3a8", "05dec146-ed7f-401f-968e-2bbb6d53e89a", "eba6408f-ee6b-4148-8616-1b6f1ec2ccb9", "c9239973-5f93-4186-82ee-2a992dbb08c4", "f10f8178-21a5-449d-840c-06d438ff6b4c", "b6b6c925-b33d-4e52-b8f0-c603696ac4ca", "fc336b62-c076-4874-a4fc-71de8503b90f", "de9e4a59-6805-45b2-9264-7b0e185bb28b", "6b214672-05d2-4439-aa1b-d9612f9f84c1", "c9e5d9c2-8f7e-4f9b-aeed-317b2184aafe", "49189095-4a4d-492f-98cd-5c9fc2a7edf7", "66fc763d-57a8-4d0c-986d-c2248ecd3c8e"]}]}, {"BlockType": "LINE", "Confidence": 99.2978515625, "Text": "accurate weight of the shipment. The scales used are certified 'legal for trade' and are maintained to conform to", "Geometry": {"BoundingBox": {"Width": 0.8082341551780701, "Height": 0.012426944449543953, "Left": 0.09412213414907455, "Top": 0.2589010000228882}, "Polygon": [{"X": 0.09412214905023575, "Y": 0.2589034140110016}, {"X": 0.9023563265800476, "Y": 0.2589010000228882}, {"X": 0.9023562073707581, "Y": 0.27132555842399597}, {"X": 0.09412213414907455, "Y": 0.271327942609787}]}, "Id": "a223ec42-6ef0-4a4c-ba4a-1a7a9560471a", "Relationships": [{"Type": "CHILD", "Ids": ["05cedbc7-8323-43db-a7be-08f82dfbca0e", "c2fdd488-2186-4a58-af58-5ccbd5edf096", "b766e4fe-a672-4c21-b278-09a0761968c1", "afec94ba-5b84-435f-9ce7-f4481081c72b", "a7659015-4636-4f6f-a7cc-ad5d930ccfba", "dfab3151-1771-429d-8d5a-26b8fd373726", "bb0d3e78-7a62-4b53-a047-927e9265f3f1", "0410ec9c-6b33-4861-b300-bf248465ed0d", "dfde3f29-5dd9-4c65-aa84-e5834cdec952", "9b0b60b8-70f9-4cb9-826c-64dbe5816b22", "4e0764c4-e76f-4a5b-a7da-b587e1dcf29e", "967d90ba-2512-4e1a-8dc1-1604ba3951be", "5e1dd1fa-6c06-4b03-85d4-ad0451d2230b", "bb919f7f-3226-49e4-9184-3959bd39bccc", "266577aa-f763-432c-932c-5ad1303b86ff", "31a6d223-95fe-4562-8c47-c16afd4254ce", "946cdee0-e707-474f-a069-733991e1229a", "4e442778-9782-4a13-b3a3-bc90e64b05ff", "be1c77b7-e23b-4783-8774-a79e143fbb78"]}]}, {"BlockType": "LINE", "Confidence": 99.91388702392578, "Text": "all established federal and state guidelines. Established procedures for verifying weight meets the guidelines", "Geometry": {"BoundingBox": {"Width": 0.8078359365463257, "Height": 0.012571134604513645, "Left": 0.09407386928796768, "Top": 0.27311527729034424}, "Polygon": [{"X": 0.09407388418912888, "Y": 0.27311766147613525}, {"X": 0.9019098281860352, "Y": 0.27311527729034424}, {"X": 0.9019097089767456, "Y": 0.28568407893180847}, {"X": 0.09407386928796768, "Y": 0.2856864035129547}]}, "Id": "bd635db3-f143-4dae-9393-bf94fb61f08c", "Relationships": [{"Type": "CHILD", "Ids": ["ad93f9f3-5ad3-49ff-9fc2-a991f85e2d21", "68e2bc8d-390e-499e-ad65-0852f771a8cb", "f1a92747-4a7e-41ce-b046-b91d6b4107ba", "e785f1bb-febd-4721-b67d-3f2b076d1d02", "93d365c5-5b9b-4175-a7b0-4d25c685fcea", "65e87345-622d-4a71-9a70-544aefda79e0", "13fddf3a-a73d-4664-b609-5c6731b0c34a", "4d7b02d2-d558-4655-bf99-d31bd64ad473", "f0f15314-f7c9-43c8-9610-bd22a2e10643", "8840ad52-40a5-47e2-b8cb-d548529f2d37", "78b3f081-2749-4cc5-a55e-5010ce4d1f5d", "e4875e62-4762-4abc-9b5c-e5bb33fc3da7", "5c2e744e-4e05-49ba-9622-47e26896a9f9", "c7bba4be-fe99-44c5-9a7d-f21c392b59c2"]}]}, {"BlockType": "LINE", "Confidence": 99.92047119140625, "Text": "published in the National Motor Freight Classification 100-series items 360 and 995 and the UPGF 102-series", "Geometry": {"BoundingBox": {"Width": 0.8082669377326965, "Height": 0.012343212962150574, "Left": 0.09436369687318802, "Top": 0.28738829493522644}, "Polygon": [{"X": 0.09436370432376862, "Y": 0.2873905897140503}, {"X": 0.902630627155304, "Y": 0.28738829493522644}, {"X": 0.9026305675506592, "Y": 0.29972922801971436}, {"X": 0.09436369687318802, "Y": 0.2997314929962158}]}, "Id": "113277e4-aa20-49e2-a054-241b170f2489", "Relationships": [{"Type": "CHILD", "Ids": ["3137282d-ebf3-4a31-872a-faa536a89009", "dfb51669-fa3f-41bf-881d-21abf77695b0", "2df9fba2-4577-4fff-b691-3d47a71253f7", "916f7ea3-c1ce-44b3-9bc6-ce6a8cc23cce", "7faf735f-9640-44b9-8ea2-69aaac1edfd7", "f793970e-e14a-439b-a837-d35678ff2495", "7cad8113-b51a-4ed4-a2d7-feb24cff5fe1", "d86c56f0-9e32-4f92-8867-928a89885aa5", "5a70b061-8867-4c39-9eb9-b523b40e4adf", "ac7b2d9c-7271-4851-9c44-50958e285891", "3405093c-bef9-400f-96fb-9cb29f8bb4c6", "d915a036-d47b-43af-9aae-460f25e1dc51", "739f7248-1d8f-44b9-97a9-028d39fc991f", "21bcd591-c53b-4d81-908a-141d2f73aa98", "259c9a32-00ad-425a-b67b-90c6f229a958", "a3af5d7e-3efa-446e-83d2-885ebb1eed01"]}]}, {"BlockType": "LINE", "Confidence": 99.8393325805664, "Text": "rules tariff items 362 and 990.", "Geometry": {"BoundingBox": {"Width": 0.2137860357761383, "Height": 0.0101381940767169, "Left": 0.09430932998657227, "Top": 0.3016105592250824}, "Polygon": [{"X": 0.09430933743715286, "Y": 0.30161115527153015}, {"X": 0.30809536576271057, "Y": 0.3016105592250824}, {"X": 0.3080953359603882, "Y": 0.3117481470108032}, {"X": 0.09430932998657227, "Y": 0.311748743057251}]}, "Id": "6b543867-d59a-4793-b787-e1448f1c06ed", "Relationships": [{"Type": "CHILD", "Ids": ["c886fb04-aa37-4bde-8be8-58ee3879fb77", "41a9d12e-8367-4b98-8b4a-03663c9ca0be", "dd0f536e-4798-4c5a-a45c-4ca95ec455fb", "94eb70a5-d6ba-4c22-b23f-6eff16e1aa44", "5a05d025-3f06-4bea-90ec-35d74c5aa67d", "053fe598-e181-4ca4-86ff-59720bc60d7a"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Handling", "Geometry": {"BoundingBox": {"Width": 0.06309546530246735, "Height": 0.012526462785899639, "Left": 0.44676703214645386, "Top": 0.3331822156906128}, "Polygon": [{"X": 0.44676709175109863, "Y": 0.33318236470222473}, {"X": 0.50986248254776, "Y": 0.3331822156906128}, {"X": 0.5098624229431152, "Y": 0.3457085192203522}, {"X": 0.44676703214645386, "Y": 0.3457086682319641}]}, "Id": "0c153816-ba75-4a3d-aac4-3ae47acb0bc1", "Relationships": [{"Type": "CHILD", "Ids": ["f6515c4c-80b6-47fa-943a-0fab4aef0fb9"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Weight", "Geometry": {"BoundingBox": {"Width": 0.0514938049018383, "Height": 0.012421411462128162, "Left": 0.6810327768325806, "Top": 0.33351945877075195}, "Polygon": [{"X": 0.6810328960418701, "Y": 0.3335195779800415}, {"X": 0.7325266003608704, "Y": 0.33351945877075195}, {"X": 0.7325264811515808, "Y": 0.3459407389163971}, {"X": 0.6810327768325806, "Y": 0.34594085812568665}]}, "Id": "2c0b3d16-1656-404e-9496-9f29795c1534", "Relationships": [{"Type": "CHILD", "Ids": ["3cf1914a-79bc-4eaf-817b-e1ae0be4f772"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Units", "Geometry": {"BoundingBox": {"Width": 0.03660602867603302, "Height": 0.009925858117640018, "Left": 0.4601740837097168, "Top": 0.34745562076568604}, "Polygon": [{"X": 0.4601741135120392, "Y": 0.3474557399749756}, {"X": 0.4967800974845886, "Y": 0.34745562076568604}, {"X": 0.49678006768226624, "Y": 0.3573814034461975}, {"X": 0.4601740837097168, "Y": 0.3573814928531647}]}, "Id": "faa661aa-e28a-40cb-a42d-224ef03dc6f3", "Relationships": [{"Type": "CHILD", "Ids": ["9a04277b-87d7-4683-985a-09efa04e320d"]}]}, {"BlockType": "LINE", "Confidence": 77.4499740600586, "Text": "(lbs.)", "Geometry": {"BoundingBox": {"Width": 0.03575987368822098, "Height": 0.012354874052107334, "Left": 0.6892008185386658, "Top": 0.34784987568855286}, "Polygon": [{"X": 0.6892009377479553, "Y": 0.34784996509552}, {"X": 0.7249606847763062, "Y": 0.34784987568855286}, {"X": 0.7249606251716614, "Y": 0.36020466685295105}, {"X": 0.6892008185386658, "Y": 0.3602047562599182}]}, "Id": "96c903e2-0d2e-4e60-ac96-f3a75f8a149e", "Relationships": [{"Type": "CHILD", "Ids": ["64cfbcdf-128b-4365-83fd-6d83e66ce173"]}]}, {"BlockType": "LINE", "Confidence": 99.609375, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.005624322220683098, "Height": 0.009576208889484406, "Left": 0.487356573343277, "Top": 0.37887826561927795}, "Polygon": [{"X": 0.48735660314559937, "Y": 0.37887829542160034}, {"X": 0.4929808974266052, "Y": 0.37887826561927795}, {"X": 0.49298083782196045, "Y": 0.38845446705818176}, {"X": 0.487356573343277, "Y": 0.38845446705818176}]}, "Id": "4fe19c88-e1e5-4141-bb4c-238c403eab6a", "Relationships": [{"Type": "CHILD", "Ids": ["780fcc36-28c7-44e0-8f87-f5e3bc44df5c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "385", "Geometry": {"BoundingBox": {"Width": 0.02673410438001156, "Height": 0.009930433705449104, "Left": 0.6971701979637146, "Top": 0.3786565065383911}, "Polygon": [{"X": 0.6971702575683594, "Y": 0.3786565661430359}, {"X": 0.7239043116569519, "Y": 0.3786565065383911}, {"X": 0.7239042520523071, "Y": 0.3885868787765503}, {"X": 0.6971701979637146, "Y": 0.38858693838119507}]}, "Id": "55edd543-5c8b-465c-a745-cc83d8cd0fc4", "Relationships": [{"Type": "CHILD", "Ids": ["24995c9e-a88a-4599-bcf9-5624dad5a0dc"]}]}, {"BlockType": "LINE", "Confidence": 99.6484375, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.00526722427457571, "Height": 0.009874563664197922, "Left": 0.48748067021369934, "Top": 0.3961121737957001}, "Polygon": [{"X": 0.48748070001602173, "Y": 0.3961121737957001}, {"X": 0.4927479028701782, "Y": 0.3961121737957001}, {"X": 0.49274784326553345, "Y": 0.4059867262840271}, {"X": 0.48748067021369934, "Y": 0.4059867262840271}]}, "Id": "f8e9cb66-d963-4152-9f66-500df566fb05", "Relationships": [{"Type": "CHILD", "Ids": ["c42c40b9-a510-4d26-8d57-262809e0d33b"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "670", "Geometry": {"BoundingBox": {"Width": 0.026832304894924164, "Height": 0.009791750460863113, "Left": 0.6972293257713318, "Top": 0.39598944783210754}, "Polygon": [{"X": 0.6972293853759766, "Y": 0.3959895074367523}, {"X": 0.7240616083145142, "Y": 0.39598944783210754}, {"X": 0.7240615487098694, "Y": 0.4057811200618744}, {"X": 0.6972293257713318, "Y": 0.40578117966651917}]}, "Id": "4557197b-5487-4052-a9c4-a642a76d4039", "Relationships": [{"Type": "CHILD", "Ids": ["de0a742b-dcb5-4106-bece-b590c342b91b"]}]}, {"BlockType": "LINE", "Confidence": 99.6484375, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.005300783086568117, "Height": 0.009753221645951271, "Left": 0.48747172951698303, "Top": 0.4130985736846924}, "Polygon": [{"X": 0.4874717891216278, "Y": 0.4130985736846924}, {"X": 0.49277251958847046, "Y": 0.4130985736846924}, {"X": 0.4927724599838257, "Y": 0.4228517711162567}, {"X": 0.48747172951698303, "Y": 0.4228518009185791}]}, "Id": "e5d0690f-4fe1-4019-8e8c-5799b263d9e1", "Relationships": [{"Type": "CHILD", "Ids": ["94a0880b-53ce-4760-91d8-de9320227572"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "750", "Geometry": {"BoundingBox": {"Width": 0.026598377153277397, "Height": 0.009648303501307964, "Left": 0.6972914338111877, "Top": 0.41314181685447693}, "Polygon": [{"X": 0.6972914934158325, "Y": 0.4131418764591217}, {"X": 0.7238897681236267, "Y": 0.41314181685447693}, {"X": 0.7238897085189819, "Y": 0.4227900505065918}, {"X": 0.6972914338111877, "Y": 0.4227901101112366}]}, "Id": "fc4c9348-ac6e-4297-a8db-6109be18a9fc", "Relationships": [{"Type": "CHILD", "Ids": ["931542b9-f254-4df8-9c50-18e285cf809c"]}]}, {"BlockType": "LINE", "Confidence": 99.88719940185547, "Text": "Actual Scale Total", "Geometry": {"BoundingBox": {"Width": 0.12940199673175812, "Height": 0.010108343325555325, "Left": 0.0935739204287529, "Top": 0.4468284845352173}, "Polygon": [{"X": 0.0935739278793335, "Y": 0.4468287527561188}, {"X": 0.2229759246110916, "Y": 0.4468284845352173}, {"X": 0.22297589480876923, "Y": 0.4569365680217743}, {"X": 0.0935739204287529, "Y": 0.4569368064403534}]}, "Id": "af972a80-394e-4576-9cb6-399021674b28", "Relationships": [{"Type": "CHILD", "Ids": ["a1d01661-3181-4f6e-8258-efe0ea9777a7", "b35819a2-2cda-4d03-9eda-6d453d324a79", "e0d9e686-85b2-408a-9e66-cdcaa4e376b6"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.008547382429242134, "Height": 0.009630661457777023, "Left": 0.4861716628074646, "Top": 0.4474080204963684}, "Polygon": [{"X": 0.4861717224121094, "Y": 0.4474080502986908}, {"X": 0.4947190582752228, "Y": 0.4474080204963684}, {"X": 0.494718998670578, "Y": 0.45703867077827454}, {"X": 0.4861716628074646, "Y": 0.4570387005805969}]}, "Id": "150a19aa-64ca-430c-9e43-dd74f469ffbc", "Relationships": [{"Type": "CHILD", "Ids": ["a845dc49-3495-4e58-a749-4b21c303fd36"]}]}, {"BlockType": "LINE", "Confidence": 99.51908874511719, "Text": "1,805", "Geometry": {"BoundingBox": {"Width": 0.039480969309806824, "Height": 0.011169543489813805, "Left": 0.6843679547309875, "Top": 0.4473262429237366}, "Polygon": [{"X": 0.6843680143356323, "Y": 0.44732633233070374}, {"X": 0.7238489389419556, "Y": 0.4473262429237366}, {"X": 0.723848819732666, "Y": 0.45849570631980896}, {"X": 0.6843679547309875, "Y": 0.4584957957267761}]}, "Id": "891cbdd6-f3c3-4701-be18-2d2ba24d4eb2", "Relationships": [{"Type": "CHILD", "Ids": ["9579adf0-9c41-4101-9a10-6c20df0a93c8"]}]}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "TForce", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06458566337823868, "Height": 0.011882470920681953, "Left": 0.4494950473308563, "Top": 0.07231646031141281}, "Polygon": [{"X": 0.4494951069355011, "Y": 0.0723167210817337}, {"X": 0.5140807032585144, "Y": 0.07231646031141281}, {"X": 0.5140806436538696, "Y": 0.08419868350028992}, {"X": 0.4494950473308563, "Y": 0.08419893682003021}]}, "Id": "5a183304-484b-46ee-b46f-c5905e6ca956"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06627840548753738, "Height": 0.014731845818459988, "Left": 0.5205748081207275, "Top": 0.07228734344244003}, "Polygon": [{"X": 0.5205748677253723, "Y": 0.07228760421276093}, {"X": 0.5868532061576843, "Y": 0.07228734344244003}, {"X": 0.5868531465530396, "Y": 0.0870189294219017}, {"X": 0.5205748081207275, "Y": 0.087019182741642}]}, "Id": "51172863-eb27-4551-a316-76c30d919373"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "TForce", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25794869661331177, "Height": 0.03545314818620682, "Left": 0.0924140065908432, "Top": 0.08627042919397354}, "Polygon": [{"X": 0.09241403639316559, "Y": 0.08627142757177353}, {"X": 0.3503626883029938, "Y": 0.08627042919397354}, {"X": 0.3503625690937042, "Y": 0.12172261625528336}, {"X": 0.0924140065908432, "Y": 0.12172356992959976}]}, "Id": "77e3f322-6090-4936-aa0a-620bf7a2dc81"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "P.O.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028944913297891617, "Height": 0.010218395851552486, "Left": 0.44967931509017944, "Top": 0.08922933787107468}, "Polygon": [{"X": 0.4496793746948242, "Y": 0.08922944962978363}, {"X": 0.47862422466278076, "Y": 0.08922933787107468}, {"X": 0.4786241948604584, "Y": 0.09944762289524078}, {"X": 0.44967931509017944, "Y": 0.09944773465394974}]}, "Id": "74596354-fc55-4aff-b711-0719ba313300"}, {"BlockType": "WORD", "Confidence": 99.6021957397461, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02792278304696083, "Height": 0.010028885677456856, "Left": 0.4850561320781708, "Top": 0.08922058343887329}, "Polygon": [{"X": 0.48505619168281555, "Y": 0.08922069519758224}, {"X": 0.5129789113998413, "Y": 0.08922058343887329}, {"X": 0.5129788517951965, "Y": 0.09924936294555664}, {"X": 0.4850561320781708, "Y": 0.099249467253685}]}, "Id": "372a8159-7266-42e1-89b8-43870327adeb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1216", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034897517412900925, "Height": 0.00997814629226923, "Left": 0.5183031558990479, "Top": 0.08917700499296188}, "Polygon": [{"X": 0.5183031558990479, "Y": 0.08917714655399323}, {"X": 0.5532006621360779, "Y": 0.08917700499296188}, {"X": 0.5532006025314331, "Y": 0.09915502369403839}, {"X": 0.5183031558990479, "Y": 0.09915515035390854}]}, "Id": "953e2899-0a20-4484-8f7c-57068894b857"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Richmond,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07683546096086502, "Height": 0.011789689771831036, "Left": 0.44999411702156067, "Top": 0.10313335806131363}, "Polygon": [{"X": 0.44999414682388306, "Y": 0.10313364863395691}, {"X": 0.5268295407295227, "Y": 0.10313335806131363}, {"X": 0.5268294811248779, "Y": 0.11492275446653366}, {"X": 0.44999411702156067, "Y": 0.11492304503917694}]}, "Id": "ca278759-5e1e-4355-99fb-abd31dfdbac8"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Virginia", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0547080896794796, "Height": 0.012531676329672337, "Left": 0.5313601493835449, "Top": 0.10307508707046509}, "Polygon": [{"X": 0.5313602089881897, "Y": 0.1030752956867218}, {"X": 0.5860682725906372, "Y": 0.10307508707046509}, {"X": 0.5860681533813477, "Y": 0.11560656130313873}, {"X": 0.5313601493835449, "Y": 0.11560676246881485}]}, "Id": "6ea8df65-1300-4b18-9c15-3ad80a1419c9"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "23218-1216", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08712024986743927, "Height": 0.009916706942021847, "Left": 0.5907378196716309, "Top": 0.10329686105251312}, "Polygon": [{"X": 0.5907378792762756, "Y": 0.10329718887805939}, {"X": 0.6778580546379089, "Y": 0.10329686105251312}, {"X": 0.6778579950332642, "Y": 0.11321324110031128}, {"X": 0.5907378196716309, "Y": 0.11321356892585754}]}, "Id": "dbf39ccf-f0eb-45e3-9e2a-2ecfad87f8c1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1-800-333-7400", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11509879678487778, "Height": 0.00992684904485941, "Left": 0.4501526951789856, "Top": 0.11739657819271088}, "Polygon": [{"X": 0.450152724981308, "Y": 0.1173970103263855}, {"X": 0.5652514696121216, "Y": 0.11739657819271088}, {"X": 0.5652514100074768, "Y": 0.1273230016231537}, {"X": 0.4501526951789856, "Y": 0.1273234337568283}]}, "Id": "98e940bf-87c8-4a51-bb91-5fda8ca51768"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12150909751653671, "Height": 0.019270051270723343, "Left": 0.21730205416679382, "Top": 0.12642861902713776}, "Polygon": [{"X": 0.2173020988702774, "Y": 0.12642906606197357}, {"X": 0.33881115913391113, "Y": 0.12642861902713776}, {"X": 0.33881109952926636, "Y": 0.14569823443889618}, {"X": 0.21730205416679382, "Y": 0.145698681473732}]}, "Id": "8f2c11f2-3f7a-45f6-b6d0-e670ec7e91cf"}, {"BlockType": "WORD", "Confidence": 85.22261047363281, "Text": "https://www.tforcefreight.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21084065735340118, "Height": 0.012840968556702137, "Left": 0.4485003650188446, "Top": 0.1314457803964615}, "Polygon": [{"X": 0.448500394821167, "Y": 0.13144655525684357}, {"X": 0.6593409776687622, "Y": 0.1314457803964615}, {"X": 0.6593409180641174, "Y": 0.14428599178791046}, {"X": 0.4485003650188446, "Y": 0.14428675174713135}]}, "Id": "2711f158-46bc-4301-ad04-48e3e69f35d2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "WEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06558413803577423, "Height": 0.010072605684399605, "Left": 0.44886475801467896, "Top": 0.16052964329719543}, "Polygon": [{"X": 0.44886481761932373, "Y": 0.16052986681461334}, {"X": 0.514448881149292, "Y": 0.16052964329719543}, {"X": 0.514448881149292, "Y": 0.1706020087003708}, {"X": 0.44886475801467896, "Y": 0.1706022471189499}]}, "Id": "0c259597-237a-4ef3-9e02-a67f1fd97464"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10493634641170502, "Height": 0.010022000409662724, "Left": 0.5193609595298767, "Top": 0.16050425171852112}, "Polygon": [{"X": 0.5193610191345215, "Y": 0.16050462424755096}, {"X": 0.6242973208427429, "Y": 0.16050425171852112}, {"X": 0.6242972016334534, "Y": 0.17052589356899261}, {"X": 0.5193609595298767, "Y": 0.17052625119686127}]}, "Id": "03df8478-6db0-46a0-a6d4-55f57724e73e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05077457055449486, "Height": 0.012277360074222088, "Left": 0.09271377325057983, "Top": 0.17472855746746063}, "Polygon": [{"X": 0.09271378070116043, "Y": 0.17472873628139496}, {"X": 0.143488347530365, "Y": 0.17472855746746063}, {"X": 0.1434883177280426, "Y": 0.18700574338436127}, {"X": 0.09271377325057983, "Y": 0.1870059072971344}]}, "Id": "9deddfea-2e4a-4265-b240-ed92a0d1888f"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02107052505016327, "Height": 0.009897972457110882, "Left": 0.1480688750743866, "Top": 0.1747346669435501}, "Polygon": [{"X": 0.1480688899755478, "Y": 0.17473474144935608}, {"X": 0.16913940012454987, "Y": 0.1747346669435501}, {"X": 0.16913938522338867, "Y": 0.1846325695514679}, {"X": 0.1480688750743866, "Y": 0.18463264405727386}]}, "Id": "d90f1646-f3b8-4a8b-9995-2c766e8ed998"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060919828712940216, "Height": 0.010024985298514366, "Left": 0.17457164824008942, "Top": 0.17477604746818542}, "Polygon": [{"X": 0.1745716631412506, "Y": 0.17477625608444214}, {"X": 0.23549146950244904, "Y": 0.17477604746818542}, {"X": 0.23549145460128784, "Y": 0.18480081856250763}, {"X": 0.17457164824008942, "Y": 0.18480102717876434}]}, "Id": "070b4ee5-2c6c-41fc-83c3-0fe6a8ef6161"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "465486206", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08231423795223236, "Height": 0.009868066757917404, "Left": 0.24045874178409576, "Top": 0.1748201549053192}, "Polygon": [{"X": 0.24045877158641815, "Y": 0.1748204380273819}, {"X": 0.3227729797363281, "Y": 0.1748201549053192}, {"X": 0.32277294993400574, "Y": 0.18468794226646423}, {"X": 0.24045874178409576, "Y": 0.18468822538852692}]}, "Id": "620951df-cae2-46bf-9d4d-a1c95a66edf9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Weighed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06463451683521271, "Height": 0.012468631379306316, "Left": 0.6771246790885925, "Top": 0.17456160485744476}, "Polygon": [{"X": 0.6771247386932373, "Y": 0.17456182837486267}, {"X": 0.741759181022644, "Y": 0.17456160485744476}, {"X": 0.7417591214179993, "Y": 0.18703001737594604}, {"X": 0.6771246790885925, "Y": 0.18703024089336395}]}, "Id": "d45684ca-0a5c-4541-a079-9de8488c8761"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0377606637775898, "Height": 0.010061548091471195, "Left": 0.7470070123672485, "Top": 0.1746961623430252}, "Polygon": [{"X": 0.7470071315765381, "Y": 0.17469629645347595}, {"X": 0.7847676873207092, "Y": 0.1746961623430252}, {"X": 0.7847676277160645, "Y": 0.18475759029388428}, {"X": 0.7470070123672485, "Y": 0.18475770950317383}]}, "Id": "a8585c01-18af-4175-b609-f77df25c44ec"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "9/8/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06367723643779755, "Height": 0.009813915006816387, "Left": 0.7900331616401672, "Top": 0.17484277486801147}, "Polygon": [{"X": 0.790033221244812, "Y": 0.1748429834842682}, {"X": 0.8537103533744812, "Y": 0.17484277486801147}, {"X": 0.8537102937698364, "Y": 0.18465647101402283}, {"X": 0.7900331616401672, "Y": 0.18465667963027954}]}, "Id": "aa3d13ca-5865-4f63-a0c5-325cc41261ce"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Weighing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06760026514530182, "Height": 0.01251591369509697, "Left": 0.09172104299068451, "Top": 0.18890102207660675}, "Polygon": [{"X": 0.0917210578918457, "Y": 0.18890126049518585}, {"X": 0.15932130813598633, "Y": 0.18890102207660675}, {"X": 0.15932129323482513, "Y": 0.2014167159795761}, {"X": 0.09172104299068451, "Y": 0.20141693949699402}]}, "Id": "044321a3-acb2-41ae-84db-422f1cd7386a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "performed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07365833967924118, "Height": 0.012323022820055485, "Left": 0.16518522799015045, "Top": 0.18899531662464142}, "Polygon": [{"X": 0.16518525779247284, "Y": 0.1889955699443817}, {"X": 0.23884357511997223, "Y": 0.18899531662464142}, {"X": 0.23884354531764984, "Y": 0.20131810009479523}, {"X": 0.16518522799015045, "Y": 0.20131833851337433}]}, "Id": "b83ddf91-e75b-441b-86f3-44e5c26a0182"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013843774795532227, "Height": 0.009742560796439648, "Left": 0.24381123483181, "Top": 0.18932703137397766}, "Polygon": [{"X": 0.24381126463413239, "Y": 0.18932707607746124}, {"X": 0.2576550245285034, "Y": 0.18932703137397766}, {"X": 0.25765499472618103, "Y": 0.199069544672966}, {"X": 0.24381123483181, "Y": 0.19906958937644958}]}, "Id": "19e6989e-61f8-4c73-8107-5639d3699cb4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "KAN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03258855268359184, "Height": 0.009958906099200249, "Left": 0.2622276544570923, "Top": 0.1889195442199707}, "Polygon": [{"X": 0.2622276544570923, "Y": 0.18891964852809906}, {"X": 0.29481619596481323, "Y": 0.1889195442199707}, {"X": 0.29481616616249084, "Y": 0.19887833297252655}, {"X": 0.2622276544570923, "Y": 0.1988784521818161}]}, "Id": "5d05ede2-9a69-4e3e-88b0-b3d74422340e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05399196967482567, "Height": 0.009923506528139114, "Left": 0.300205796957016, "Top": 0.1890183389186859}, "Polygon": [{"X": 0.3002058267593384, "Y": 0.18901851773262024}, {"X": 0.35419777035713196, "Y": 0.1890183389186859}, {"X": 0.35419774055480957, "Y": 0.1989416629076004}, {"X": 0.300205796957016, "Y": 0.19894184172153473}]}, "Id": "bdfefdd7-3bde-48c3-ba01-e2a4304bb2a3"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Center", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0491664819419384, "Height": 0.00997205264866352, "Left": 0.3592783510684967, "Top": 0.18906714022159576}, "Polygon": [{"X": 0.3592784106731415, "Y": 0.1890673041343689}, {"X": 0.4084448516368866, "Y": 0.18906714022159576}, {"X": 0.4084447920322418, "Y": 0.199039027094841}, {"X": 0.3592783510684967, "Y": 0.19903919100761414}]}, "Id": "f93396ef-3e24-45ea-9a14-446dcc2714bb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06575717031955719, "Height": 0.009963735938072205, "Left": 0.09195930510759354, "Top": 0.21627382934093475}, "Polygon": [{"X": 0.09195931255817413, "Y": 0.21627403795719147}, {"X": 0.15771646797657013, "Y": 0.21627382934093475}, {"X": 0.15771645307540894, "Y": 0.22623735666275024}, {"X": 0.09195930510759354, "Y": 0.22623756527900696}]}, "Id": "23dd3d0a-612f-4854-b866-d0ba7fd7e497"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFICATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.123714379966259, "Height": 0.010053210891783237, "Left": 0.16178277134895325, "Top": 0.2161771059036255}, "Polygon": [{"X": 0.16178278625011444, "Y": 0.21617750823497772}, {"X": 0.28549715876579285, "Y": 0.2161771059036255}, {"X": 0.28549712896347046, "Y": 0.22622992098331451}, {"X": 0.16178277134895325, "Y": 0.22623032331466675}]}, "Id": "b5d4baea-a43c-4b8b-81f7-822208a833d5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "We", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02419915609061718, "Height": 0.009872986003756523, "Left": 0.09348777681589127, "Top": 0.24470694363117218}, "Polygon": [{"X": 0.09348778426647186, "Y": 0.24470701813697815}, {"X": 0.117686927318573, "Y": 0.24470694363117218}, {"X": 0.1176869198679924, "Y": 0.2545798420906067}, {"X": 0.09348777681589127, "Y": 0.25457993149757385}]}, "Id": "057212e4-358f-49f9-8592-51694b2f9835"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0437026210129261, "Height": 0.012206642888486385, "Left": 0.12334764748811722, "Top": 0.24482016265392303}, "Polygon": [{"X": 0.12334766238927841, "Y": 0.24482029676437378}, {"X": 0.16705027222633362, "Y": 0.24482016265392303}, {"X": 0.16705024242401123, "Y": 0.25702667236328125}, {"X": 0.12334764748811722, "Y": 0.2570268213748932}]}, "Id": "74c06e01-e231-46a9-8058-6d2bbddb429f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027858711779117584, "Height": 0.009894615039229393, "Left": 0.17180916666984558, "Top": 0.24477341771125793}, "Polygon": [{"X": 0.17180918157100677, "Y": 0.2447735071182251}, {"X": 0.19966788589954376, "Y": 0.24477341771125793}, {"X": 0.19966785609722137, "Y": 0.2546679377555847}, {"X": 0.17180916666984558, "Y": 0.2546680271625519}]}, "Id": "dc5a13ab-49d2-4d7f-a11e-87a0f092dc9b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022591106593608856, "Height": 0.009788872674107552, "Left": 0.20440690219402313, "Top": 0.24481835961341858}, "Polygon": [{"X": 0.20440691709518433, "Y": 0.24481841921806335}, {"X": 0.22699801623821259, "Y": 0.24481835961341858}, {"X": 0.2269979864358902, "Y": 0.2546071708202362}, {"X": 0.20440690219402313, "Y": 0.254607230424881}]}, "Id": "b0353970-65a8-428f-95be-8d0c92d05544"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "mentioned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07524509727954865, "Height": 0.00988894421607256, "Left": 0.23296786844730377, "Top": 0.24475793540477753}, "Polygon": [{"X": 0.23296789824962616, "Y": 0.24475817382335663}, {"X": 0.3082129657268524, "Y": 0.24475793540477753}, {"X": 0.30821293592453003, "Y": 0.2546466588973999}, {"X": 0.23296786844730377, "Y": 0.254646897315979}]}, "Id": "b6c4830a-01ab-4d42-9a32-08fc98af0974"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06654611974954605, "Height": 0.012380734086036682, "Left": 0.3141397535800934, "Top": 0.24468836188316345}, "Polygon": [{"X": 0.31413978338241577, "Y": 0.24468857049942017}, {"X": 0.38068586587905884, "Y": 0.24468836188316345}, {"X": 0.38068580627441406, "Y": 0.2570689022541046}, {"X": 0.3141397535800934, "Y": 0.25706908106803894}]}, "Id": "829ebff7-ac15-4ba2-9f27-11240f3d037f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "has", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02559284120798111, "Height": 0.009952764958143234, "Left": 0.38574138283729553, "Top": 0.24474772810935974}, "Polygon": [{"X": 0.3857414126396179, "Y": 0.2447478175163269}, {"X": 0.41133421659469604, "Y": 0.24474772810935974}, {"X": 0.41133415699005127, "Y": 0.2547004222869873}, {"X": 0.38574138283729553, "Y": 0.25470051169395447}]}, "Id": "d2579552-1362-4df7-b6ea-9c85416e8f91"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03548474982380867, "Height": 0.009894223883748055, "Left": 0.41739633679389954, "Top": 0.24473169445991516}, "Polygon": [{"X": 0.4173963665962219, "Y": 0.24473179876804352}, {"X": 0.4528810977935791, "Y": 0.24473169445991516}, {"X": 0.4528810381889343, "Y": 0.2546257972717285}, {"X": 0.41739633679389954, "Y": 0.25462591648101807}]}, "Id": "067835dd-dfff-41f7-adf6-aa1f67a75723"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "weighed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06134190410375595, "Height": 0.012273472733795643, "Left": 0.4579906761646271, "Top": 0.24470672011375427}, "Polygon": [{"X": 0.45799073576927185, "Y": 0.2447068989276886}, {"X": 0.5193325877189636, "Y": 0.24470672011375427}, {"X": 0.5193325281143188, "Y": 0.2569800019264221}, {"X": 0.4579906761646271, "Y": 0.25698018074035645}]}, "Id": "cb0c04a2-cfa2-4edf-a6fe-833b72c5b3a8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026459375396370888, "Height": 0.00984132755547762, "Left": 0.5246422290802002, "Top": 0.24484576284885406}, "Polygon": [{"X": 0.524642288684845, "Y": 0.24484585225582123}, {"X": 0.5511016249656677, "Y": 0.24484576284885406}, {"X": 0.551101565361023, "Y": 0.25468701124191284}, {"X": 0.5246422290802002, "Y": 0.25468710064888}]}, "Id": "05dec146-ed7f-401f-968e-2bbb6d53e89a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028065018355846405, "Height": 0.00999588705599308, "Left": 0.556896984577179, "Top": 0.24477967619895935}, "Polygon": [{"X": 0.5568970441818237, "Y": 0.2447797656059265}, {"X": 0.584962010383606, "Y": 0.24477967619895935}, {"X": 0.5849619507789612, "Y": 0.2547754943370819}, {"X": 0.556896984577179, "Y": 0.2547755539417267}]}, "Id": "eba6408f-ee6b-4148-8616-1b6f1ec2ccb9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022604167461395264, "Height": 0.009722035378217697, "Left": 0.5893186926841736, "Top": 0.24486695230007172}, "Polygon": [{"X": 0.5893187522888184, "Y": 0.2448670119047165}, {"X": 0.6119228601455688, "Y": 0.24486695230007172}, {"X": 0.6119228005409241, "Y": 0.25458890199661255}, {"X": 0.5893186926841736, "Y": 0.2545889914035797}]}, "Id": "c9239973-5f93-4186-82ee-2a992dbb08c4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "correct", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05020463839173317, "Height": 0.009499470703303814, "Left": 0.6176386475563049, "Top": 0.2452308088541031}, "Polygon": [{"X": 0.6176387071609497, "Y": 0.24523097276687622}, {"X": 0.6678432822227478, "Y": 0.2452308088541031}, {"X": 0.667843222618103, "Y": 0.25473013520240784}, {"X": 0.6176386475563049, "Y": 0.2547302842140198}]}, "Id": "f10f8178-21a5-449d-840c-06d438ff6b4c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04809565097093582, "Height": 0.012485411949455738, "Left": 0.672263503074646, "Top": 0.24461132287979126}, "Polygon": [{"X": 0.6722635626792908, "Y": 0.2446114718914032}, {"X": 0.7203591465950012, "Y": 0.24461132287979126}, {"X": 0.7203590273857117, "Y": 0.2570965886116028}, {"X": 0.672263503074646, "Y": 0.2570967376232147}]}, "Id": "b6b6c925-b33d-4e52-b8f0-c603696ac4ca"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01670256443321705, "Height": 0.007787687238305807, "Left": 0.7255321145057678, "Top": 0.246928408741951}, "Polygon": [{"X": 0.7255321741104126, "Y": 0.24692845344543457}, {"X": 0.7422346472740173, "Y": 0.246928408741951}, {"X": 0.7422345876693726, "Y": 0.25471603870391846}, {"X": 0.7255321145057678, "Y": 0.25471609830856323}]}, "Id": "fc336b62-c076-4874-a4fc-71de8503b90f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044325053691864014, "Height": 0.00985964946448803, "Left": 0.7473032474517822, "Top": 0.2448464035987854}, "Polygon": [{"X": 0.747303307056427, "Y": 0.24484653770923615}, {"X": 0.7916283011436462, "Y": 0.2448464035987854}, {"X": 0.7916282415390015, "Y": 0.25470593571662903}, {"X": 0.7473032474517822, "Y": 0.2547060549259186}]}, "Id": "de9e4a59-6805-45b2-9264-7b0e185bb28b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011127399280667305, "Height": 0.00985425803810358, "Left": 0.7975053787231445, "Top": 0.24474099278450012}, "Polygon": [{"X": 0.7975054979324341, "Y": 0.2447410225868225}, {"X": 0.8086327910423279, "Y": 0.24474099278450012}, {"X": 0.8086327314376831, "Y": 0.25459522008895874}, {"X": 0.7975053787231445, "Y": 0.25459524989128113}]}, "Id": "6b214672-05d2-4439-aa1b-d9612f9f84c1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022456670179963112, "Height": 0.009750946424901485, "Left": 0.8140978217124939, "Top": 0.24485479295253754}, "Polygon": [{"X": 0.8140978813171387, "Y": 0.2448548674583435}, {"X": 0.8365544676780701, "Y": 0.24485479295253754}, {"X": 0.8365544080734253, "Y": 0.2546056807041168}, {"X": 0.8140978217124939, "Y": 0.2546057403087616}]}, "Id": "c9e5d9c2-8f7e-4f9b-aeed-317b2184aafe"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "true", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028456207364797592, "Height": 0.009617151692509651, "Left": 0.8417659401893616, "Top": 0.2450789511203766}, "Polygon": [{"X": 0.8417659997940063, "Y": 0.24507904052734375}, {"X": 0.8702221512794495, "Y": 0.2450789511203766}, {"X": 0.8702220320701599, "Y": 0.2546960115432739}, {"X": 0.8417659401893616, "Y": 0.2546961009502411}]}, "Id": "49189095-4a4d-492f-98cd-5c9fc2a7edf7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02704009599983692, "Height": 0.009963825345039368, "Left": 0.8752046227455139, "Top": 0.24473436176776886}, "Polygon": [{"X": 0.8752047419548035, "Y": 0.24473443627357483}, {"X": 0.9022447466850281, "Y": 0.24473436176776886}, {"X": 0.9022446274757385, "Y": 0.25469809770584106}, {"X": 0.8752046227455139, "Y": 0.2546981871128082}]}, "Id": "66fc763d-57a8-4d0c-986d-c2248ecd3c8e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "accurate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061954185366630554, "Height": 0.00967529509216547, "Left": 0.09412214159965515, "Top": 0.25938349962234497}, "Polygon": [{"X": 0.09412214905023575, "Y": 0.2593836784362793}, {"X": 0.1560763269662857, "Y": 0.25938349962234497}, {"X": 0.1560763120651245, "Y": 0.26905858516693115}, {"X": 0.09412214159965515, "Y": 0.26905879378318787}]}, "Id": "05cedbc7-8323-43db-a7be-08f82dfbca0e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048160869628190994, "Height": 0.012256045825779438, "Left": 0.16082167625427246, "Top": 0.2590717077255249}, "Polygon": [{"X": 0.16082170605659485, "Y": 0.25907185673713684}, {"X": 0.20898254215717316, "Y": 0.2590717077255249}, {"X": 0.20898252725601196, "Y": 0.2713276147842407}, {"X": 0.16082167625427246, "Y": 0.2713277339935303}]}, "Id": "c2fdd488-2186-4a58-af58-5ccbd5edf096"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0146279726177454, "Height": 0.009859954006969929, "Left": 0.21329455077648163, "Top": 0.258918434381485}, "Polygon": [{"X": 0.21329456567764282, "Y": 0.2589184641838074}, {"X": 0.22792251408100128, "Y": 0.258918434381485}, {"X": 0.2279224991798401, "Y": 0.26877835392951965}, {"X": 0.21329455077648163, "Y": 0.26877838373184204}]}, "Id": "b766e4fe-a672-4c21-b278-09a0761968c1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022665582597255707, "Height": 0.00982910580933094, "Left": 0.23148712515830994, "Top": 0.2590888738632202}, "Polygon": [{"X": 0.23148715496063232, "Y": 0.259088933467865}, {"X": 0.25415271520614624, "Y": 0.2590888738632202}, {"X": 0.25415268540382385, "Y": 0.26891788840293884}, {"X": 0.23148712515830994, "Y": 0.268917977809906}]}, "Id": "afec94ba-5b84-435f-9ce7-f4481081c72b"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "shipment.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0696960985660553, "Height": 0.01209938619285822, "Left": 0.25926390290260315, "Top": 0.2590777575969696}, "Polygon": [{"X": 0.25926393270492554, "Y": 0.2590779662132263}, {"X": 0.32896000146865845, "Y": 0.2590777575969696}, {"X": 0.32895997166633606, "Y": 0.27117693424224854}, {"X": 0.25926390290260315, "Y": 0.27117714285850525}]}, "Id": "a7659015-4636-4f6f-a7cc-ad5d930ccfba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02811605855822563, "Height": 0.009923051111400127, "Left": 0.3347083032131195, "Top": 0.25899970531463623}, "Polygon": [{"X": 0.3347083330154419, "Y": 0.2589997947216034}, {"X": 0.36282435059547424, "Y": 0.25899970531463623}, {"X": 0.36282432079315186, "Y": 0.26892268657684326}, {"X": 0.3347083032131195, "Y": 0.2689227759838104}]}, "Id": "dfab3151-1771-429d-8d5a-26b8fd373726"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "scales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04564438387751579, "Height": 0.00988130271434784, "Left": 0.36802470684051514, "Top": 0.2590540945529938}, "Polygon": [{"X": 0.3680247366428375, "Y": 0.2590542435646057}, {"X": 0.4136691093444824, "Y": 0.2590540945529938}, {"X": 0.41366904973983765, "Y": 0.26893526315689087}, {"X": 0.36802470684051514, "Y": 0.2689354121685028}]}, "Id": "bb0d3e78-7a62-4b53-a047-927e9265f3f1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "used", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03445048630237579, "Height": 0.009817244485020638, "Left": 0.41914182901382446, "Top": 0.2591233551502228}, "Polygon": [{"X": 0.41914188861846924, "Y": 0.25912347435951233}, {"X": 0.45359233021736145, "Y": 0.2591233551502228}, {"X": 0.4535922706127167, "Y": 0.2689405083656311}, {"X": 0.41914182901382446, "Y": 0.26894059777259827}]}, "Id": "0410ec9c-6b33-4861-b300-bf248465ed0d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022951779887080193, "Height": 0.00789177231490612, "Left": 0.4593779742717743, "Top": 0.2610648274421692}, "Polygon": [{"X": 0.45937803387641907, "Y": 0.26106491684913635}, {"X": 0.48232975602149963, "Y": 0.2610648274421692}, {"X": 0.48232972621917725, "Y": 0.2689565420150757}, {"X": 0.4593779742717743, "Y": 0.26895660161972046}]}, "Id": "dfde3f29-5dd9-4c65-aa84-e5834cdec952"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "certified", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05689307674765587, "Height": 0.010113558731973171, "Left": 0.48749321699142456, "Top": 0.2589166760444641}, "Polygon": [{"X": 0.48749327659606934, "Y": 0.25891682505607605}, {"X": 0.5443863272666931, "Y": 0.2589166760444641}, {"X": 0.5443862676620483, "Y": 0.26903006434440613}, {"X": 0.48749321699142456, "Y": 0.26903021335601807}]}, "Id": "9b0b60b8-70f9-4cb9-826c-64dbe5816b22"}, {"BlockType": "WORD", "Confidence": 89.39732360839844, "Text": "'legal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03723074868321419, "Height": 0.012392355129122734, "Left": 0.5498030185699463, "Top": 0.2589019536972046}, "Polygon": [{"X": 0.5498030781745911, "Y": 0.25890204310417175}, {"X": 0.587033748626709, "Y": 0.2589019536972046}, {"X": 0.5870336890220642, "Y": 0.27129417657852173}, {"X": 0.5498030185699463, "Y": 0.2712942957878113}]}, "Id": "4e0764c4-e76f-4a5b-a7da-b587e1dcf29e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019330525770783424, "Height": 0.00984590407460928, "Left": 0.5922791957855225, "Top": 0.2589884400367737}, "Polygon": [{"X": 0.5922792553901672, "Y": 0.25898849964141846}, {"X": 0.611609697341919, "Y": 0.2589884400367737}, {"X": 0.6116096377372742, "Y": 0.26883429288864136}, {"X": 0.5922791957855225, "Y": 0.26883435249328613}]}, "Id": "967d90ba-2512-4e1a-8dc1-1604ba3951be"}, {"BlockType": "WORD", "Confidence": 98.037109375, "Text": "trade'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040630169212818146, "Height": 0.009831702336668968, "Left": 0.61592036485672, "Top": 0.2590867877006531}, "Polygon": [{"X": 0.6159204244613647, "Y": 0.2590869069099426}, {"X": 0.6565505266189575, "Y": 0.2590867877006531}, {"X": 0.6565504670143127, "Y": 0.26891836524009705}, {"X": 0.61592036485672, "Y": 0.2689184844493866}]}, "Id": "5e1dd1fa-6c06-4b03-85d4-ad0451d2230b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026211749762296677, "Height": 0.009876150637865067, "Left": 0.6620696187019348, "Top": 0.2590761184692383}, "Polygon": [{"X": 0.6620696783065796, "Y": 0.25907620787620544}, {"X": 0.6882813572883606, "Y": 0.2590761184692383}, {"X": 0.6882812976837158, "Y": 0.2689521908760071}, {"X": 0.6620696187019348, "Y": 0.26895228028297424}]}, "Id": "bb919f7f-3226-49e4-9184-3959bd39bccc"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022995952516794205, "Height": 0.007850589230656624, "Left": 0.6941574811935425, "Top": 0.2611048221588135}, "Polygon": [{"X": 0.6941575407981873, "Y": 0.26110488176345825}, {"X": 0.7171534299850464, "Y": 0.2611048221588135}, {"X": 0.7171533703804016, "Y": 0.2689553499221802}, {"X": 0.6941574811935425, "Y": 0.26895540952682495}]}, "Id": "266577aa-f763-432c-932c-5ad1303b86ff"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "maintained", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07894451171159744, "Height": 0.009957049041986465, "Left": 0.7226133942604065, "Top": 0.2589879631996155}, "Polygon": [{"X": 0.7226134538650513, "Y": 0.2589882016181946}, {"X": 0.8015578985214233, "Y": 0.2589879631996155}, {"X": 0.8015578389167786, "Y": 0.26894477009773254}, {"X": 0.7226133942604065, "Y": 0.26894500851631165}]}, "Id": "31a6d223-95fe-4562-8c47-c16afd4254ce"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013687369413673878, "Height": 0.00946726929396391, "Left": 0.8066387176513672, "Top": 0.2593887150287628}, "Polygon": [{"X": 0.8066388368606567, "Y": 0.2593887448310852}, {"X": 0.8203260898590088, "Y": 0.2593887150287628}, {"X": 0.820326030254364, "Y": 0.2688559293746948}, {"X": 0.8066387176513672, "Y": 0.2688559889793396}]}, "Id": "946cdee0-e707-474f-a069-733991e1229a"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "conform", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05773400515317917, "Height": 0.009966494515538216, "Left": 0.8253000974655151, "Top": 0.25904354453086853}, "Polygon": [{"X": 0.8253001570701599, "Y": 0.25904372334480286}, {"X": 0.8830341100692749, "Y": 0.25904354453086853}, {"X": 0.8830339908599854, "Y": 0.26900985836982727}, {"X": 0.8253000974655151, "Y": 0.2690100371837616}]}, "Id": "4e442778-9782-4a13-b3a3-bc90e64b05ff"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013560629449784756, "Height": 0.00958785880357027, "Left": 0.8887956738471985, "Top": 0.2592446506023407}, "Polygon": [{"X": 0.8887957334518433, "Y": 0.2592447102069855}, {"X": 0.9023563265800476, "Y": 0.2592446506023407}, {"X": 0.9023562073707581, "Y": 0.2688324749469757}, {"X": 0.8887956738471985, "Y": 0.2688325047492981}]}, "Id": "be1c77b7-e23b-4783-8774-a79e143fbb78"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "all", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01595013216137886, "Height": 0.00982541125267744, "Left": 0.09407386928796768, "Top": 0.273343026638031}, "Polygon": [{"X": 0.09407388418912888, "Y": 0.2733430862426758}, {"X": 0.11002400517463684, "Y": 0.273343026638031}, {"X": 0.11002399027347565, "Y": 0.28316840529441833}, {"X": 0.09407386928796768, "Y": 0.2831684648990631}]}, "Id": "ad93f9f3-5ad3-49ff-9fc2-a991f85e2d21"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "established", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08213331550359726, "Height": 0.010129298083484173, "Left": 0.1169782504439354, "Top": 0.2731630206108093}, "Polygon": [{"X": 0.11697825789451599, "Y": 0.2731632590293884}, {"X": 0.19911156594753265, "Y": 0.2731630206108093}, {"X": 0.19911155104637146, "Y": 0.28329208493232727}, {"X": 0.1169782504439354, "Y": 0.28329232335090637}]}, "Id": "68e2bc8d-390e-499e-ad65-0852f771a8cb"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "federal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049984414130449295, "Height": 0.009956949390470982, "Left": 0.20584924519062042, "Top": 0.2732057273387909}, "Polygon": [{"X": 0.2058492749929428, "Y": 0.27320587635040283}, {"X": 0.2558336555957794, "Y": 0.2732057273387909}, {"X": 0.25583362579345703, "Y": 0.28316253423690796}, {"X": 0.20584924519062042, "Y": 0.2831626832485199}]}, "Id": "f1a92747-4a7e-41ce-b046-b91d6b4107ba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026866499334573746, "Height": 0.009978965856134892, "Left": 0.26248106360435486, "Top": 0.2733217775821686}, "Polygon": [{"X": 0.26248109340667725, "Y": 0.27332186698913574}, {"X": 0.2893475592136383, "Y": 0.2733217775821686}, {"X": 0.2893475592136383, "Y": 0.2833006680011749}, {"X": 0.26248106360435486, "Y": 0.2833007276058197}]}, "Id": "e785f1bb-febd-4721-b67d-3f2b076d1d02"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "state", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03508780896663666, "Height": 0.009725752286612988, "Left": 0.29637616872787476, "Top": 0.27358973026275635}, "Polygon": [{"X": 0.29637619853019714, "Y": 0.2735898196697235}, {"X": 0.3314639627933502, "Y": 0.27358973026275635}, {"X": 0.33146393299102783, "Y": 0.28331539034843445}, {"X": 0.29637616872787476, "Y": 0.2833154797554016}]}, "Id": "93d365c5-5b9b-4175-a7b0-4d25c685fcea"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "guidelines.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07726893573999405, "Height": 0.01226478535681963, "Left": 0.3379874527454376, "Top": 0.2733532786369324}, "Polygon": [{"X": 0.33798748254776, "Y": 0.2733535170555115}, {"X": 0.4152563810348511, "Y": 0.2733532786369324}, {"X": 0.4152563214302063, "Y": 0.2856178283691406}, {"X": 0.3379874527454376, "Y": 0.2856180667877197}]}, "Id": "65e87345-622d-4a71-9a70-544aefda79e0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Established", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08390729129314423, "Height": 0.01008778065443039, "Left": 0.4234050214290619, "Top": 0.27311643958091736}, "Polygon": [{"X": 0.4234050512313843, "Y": 0.27311667799949646}, {"X": 0.5073122978210449, "Y": 0.27311643958091736}, {"X": 0.5073122382164001, "Y": 0.28320398926734924}, {"X": 0.4234050214290619, "Y": 0.28320422768592834}]}, "Id": "13fddf3a-a73d-4664-b609-5c6731b0c34a"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "procedures", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08108913153409958, "Height": 0.012235663831233978, "Left": 0.5141271948814392, "Top": 0.2732778787612915}, "Polygon": [{"X": 0.514127254486084, "Y": 0.2732781171798706}, {"X": 0.5952163338661194, "Y": 0.2732778787612915}, {"X": 0.5952162742614746, "Y": 0.285513311624527}, {"X": 0.5141271948814392, "Y": 0.2855135500431061}]}, "Id": "4d7b02d2-d558-4655-bf99-d31bd64ad473"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01975369267165661, "Height": 0.009976073168218136, "Left": 0.6018904447555542, "Top": 0.2731698453426361}, "Polygon": [{"X": 0.601890504360199, "Y": 0.2731699049472809}, {"X": 0.621644139289856, "Y": 0.2731698453426361}, {"X": 0.6216440796852112, "Y": 0.28314587473869324}, {"X": 0.6018904447555542, "Y": 0.283145934343338}]}, "Id": "f0f15314-f7c9-43c8-9610-bd22a2e10643"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "verifying", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06092580407857895, "Height": 0.012426065281033516, "Left": 0.6273812055587769, "Top": 0.2732383906841278}, "Polygon": [{"X": 0.6273812651634216, "Y": 0.27323856949806213}, {"X": 0.688306987285614, "Y": 0.2732383906841278}, {"X": 0.6883069276809692, "Y": 0.28566426038742065}, {"X": 0.6273812055587769, "Y": 0.285664439201355}]}, "Id": "8840ad52-40a5-47e2-b8cb-d548529f2d37"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04822578281164169, "Height": 0.012048238888382912, "Left": 0.6945999264717102, "Top": 0.27335426211357117}, "Polygon": [{"X": 0.694599986076355, "Y": 0.2733544111251831}, {"X": 0.7428256869316101, "Y": 0.27335426211357117}, {"X": 0.7428256273269653, "Y": 0.2854023575782776}, {"X": 0.6945999264717102, "Y": 0.2854025065898895}]}, "Id": "78b3f081-2749-4cc5-a55e-5010ce4d1f5d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "meets", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04372347891330719, "Height": 0.009588160552084446, "Left": 0.7492759823799133, "Top": 0.27363231778144836}, "Polygon": [{"X": 0.7492760419845581, "Y": 0.2736324369907379}, {"X": 0.7929994463920593, "Y": 0.27363231778144836}, {"X": 0.7929993867874146, "Y": 0.2832203507423401}, {"X": 0.7492759823799133, "Y": 0.28322046995162964}]}, "Id": "e4875e62-4762-4abc-9b5c-e5bb33fc3da7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022577518597245216, "Height": 0.009865437634289265, "Left": 0.7998011708259583, "Top": 0.2733144760131836}, "Polygon": [{"X": 0.799801230430603, "Y": 0.27331453561782837}, {"X": 0.8223786950111389, "Y": 0.2733144760131836}, {"X": 0.8223785758018494, "Y": 0.2831798493862152}, {"X": 0.7998011708259583, "Y": 0.28317990899086}]}, "Id": "5c2e744e-4e05-49ba-9622-47e26896a9f9"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "guidelines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07237418740987778, "Height": 0.01241550873965025, "Left": 0.8295356631278992, "Top": 0.27326878905296326}, "Polygon": [{"X": 0.829535722732544, "Y": 0.27326899766921997}, {"X": 0.9019098281860352, "Y": 0.27326878905296326}, {"X": 0.9019097089767456, "Y": 0.28568407893180847}, {"X": 0.8295356631278992, "Y": 0.2856842875480652}]}, "Id": "c7bba4be-fe99-44c5-9a7d-f21c392b59c2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "published", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06908948719501495, "Height": 0.012313785962760448, "Left": 0.09436369687318802, "Top": 0.2873992919921875}, "Polygon": [{"X": 0.09436370432376862, "Y": 0.2873995006084442}, {"X": 0.16345317661762238, "Y": 0.2873992919921875}, {"X": 0.16345316171646118, "Y": 0.2997128963470459}, {"X": 0.09436369687318802, "Y": 0.2997130751609802}]}, "Id": "3137282d-ebf3-4a31-872a-faa536a89009"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01184256374835968, "Height": 0.00977970939129591, "Left": 0.16990283131599426, "Top": 0.2875209152698517}, "Polygon": [{"X": 0.16990284621715546, "Y": 0.2875209450721741}, {"X": 0.18174539506435394, "Y": 0.2875209152698517}, {"X": 0.18174538016319275, "Y": 0.2973005771636963}, {"X": 0.16990283131599426, "Y": 0.2973006069660187}]}, "Id": "dfb51669-fa3f-41bf-881d-21abf77695b0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022702191025018692, "Height": 0.009806198999285698, "Left": 0.18790127336978912, "Top": 0.2875637412071228}, "Polygon": [{"X": 0.18790128827095032, "Y": 0.2875638008117676}, {"X": 0.21060346066951752, "Y": 0.2875637412071228}, {"X": 0.21060343086719513, "Y": 0.2973698675632477}, {"X": 0.18790127336978912, "Y": 0.29736992716789246}]}, "Id": "2df9fba2-4577-4fff-b691-3d47a71253f7"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "National", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059337716549634933, "Height": 0.01002256479114294, "Left": 0.2172730416059494, "Top": 0.28743526339530945}, "Polygon": [{"X": 0.2172730565071106, "Y": 0.2874354422092438}, {"X": 0.27661076188087463, "Y": 0.28743526339530945}, {"X": 0.27661073207855225, "Y": 0.29745766520500183}, {"X": 0.2172730416059494, "Y": 0.29745781421661377}]}, "Id": "916f7ea3-c1ce-44b3-9bc6-ce6a8cc23cce"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Motor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04179361090064049, "Height": 0.010010306723415852, "Left": 0.28282293677330017, "Top": 0.28738993406295776}, "Polygon": [{"X": 0.28282296657562256, "Y": 0.2873900532722473}, {"X": 0.32461655139923096, "Y": 0.28738993406295776}, {"X": 0.32461652159690857, "Y": 0.2974001169204712}, {"X": 0.28282293677330017, "Y": 0.29740023612976074}]}, "Id": "7faf735f-9640-44b9-8ea2-69aaac1edfd7"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050464898347854614, "Height": 0.01223775278776884, "Left": 0.3307819664478302, "Top": 0.2874930799007416}, "Polygon": [{"X": 0.3307819962501526, "Y": 0.2874932289123535}, {"X": 0.3812468647956848, "Y": 0.2874930799007416}, {"X": 0.38124680519104004, "Y": 0.29973068833351135}, {"X": 0.3307819664478302, "Y": 0.2997308373451233}]}, "Id": "f793970e-e14a-439b-a837-d35678ff2495"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Classification", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0958217903971672, "Height": 0.010076976381242275, "Left": 0.38669493794441223, "Top": 0.2874513864517212}, "Polygon": [{"X": 0.3866949677467346, "Y": 0.2874516546726227}, {"X": 0.48251673579216003, "Y": 0.2874513864517212}, {"X": 0.48251667618751526, "Y": 0.29752808809280396}, {"X": 0.38669493794441223, "Y": 0.29752835631370544}]}, "Id": "7cad8113-b51a-4ed4-a2d7-feb24cff5fe1"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "100-series", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07476882636547089, "Height": 0.00998769886791706, "Left": 0.48970577120780945, "Top": 0.28744950890541077}, "Polygon": [{"X": 0.48970580101013184, "Y": 0.2874497175216675}, {"X": 0.5644745826721191, "Y": 0.28744950890541077}, {"X": 0.5644745230674744, "Y": 0.29743698239326477}, {"X": 0.48970577120780945, "Y": 0.2974371910095215}]}, "Id": "d86c56f0-9e32-4f92-8867-928a89885aa5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "items", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038915447890758514, "Height": 0.0099613256752491, "Left": 0.5710078477859497, "Top": 0.28739920258522034}, "Polygon": [{"X": 0.5710079073905945, "Y": 0.2873993217945099}, {"X": 0.6099233031272888, "Y": 0.28739920258522034}, {"X": 0.609923243522644, "Y": 0.2973604202270508}, {"X": 0.5710078477859497, "Y": 0.29736053943634033}]}, "Id": "5a70b061-8867-4c39-9eb9-b523b40e4adf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "360", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026587633416056633, "Height": 0.009835273958742619, "Left": 0.616032063961029, "Top": 0.2874945104122162}, "Polygon": [{"X": 0.6160321235656738, "Y": 0.28749459981918335}, {"X": 0.6426196694374084, "Y": 0.2874945104122162}, {"X": 0.6426196098327637, "Y": 0.29732972383499146}, {"X": 0.616032063961029, "Y": 0.29732978343963623}]}, "Id": "ac7b2d9c-7271-4851-9c44-50958e285891"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02647036872804165, "Height": 0.009872478432953358, "Left": 0.6489255428314209, "Top": 0.2875790297985077}, "Polygon": [{"X": 0.6489256024360657, "Y": 0.28757908940315247}, {"X": 0.6753959059715271, "Y": 0.2875790297985077}, {"X": 0.6753958463668823, "Y": 0.2974514365196228}, {"X": 0.6489255428314209, "Y": 0.2974514961242676}]}, "Id": "3405093c-bef9-400f-96fb-9cb29f8bb4c6"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "995", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027095014229416847, "Height": 0.009764453396201134, "Left": 0.6820686459541321, "Top": 0.2875269651412964}, "Polygon": [{"X": 0.6820687055587769, "Y": 0.28752702474594116}, {"X": 0.7091636657714844, "Y": 0.2875269651412964}, {"X": 0.7091636061668396, "Y": 0.2972913384437561}, {"X": 0.6820686459541321, "Y": 0.2972913980484009}]}, "Id": "d915a036-d47b-43af-9aae-460f25e1dc51"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02666095457971096, "Height": 0.009800253435969353, "Left": 0.7139073014259338, "Top": 0.2875789701938629}, "Polygon": [{"X": 0.7139073610305786, "Y": 0.2875790596008301}, {"X": 0.7405682802200317, "Y": 0.2875789701938629}, {"X": 0.7405681610107422, "Y": 0.29737916588783264}, {"X": 0.7139073014259338, "Y": 0.2973792254924774}]}, "Id": "739f7248-1d8f-44b9-97a9-028d39fc991f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02256743609905243, "Height": 0.00984259508550167, "Left": 0.7464625239372253, "Top": 0.2875422537326813}, "Polygon": [{"X": 0.7464625835418701, "Y": 0.28754231333732605}, {"X": 0.7690299153327942, "Y": 0.2875422537326813}, {"X": 0.7690298557281494, "Y": 0.29738476872444153}, {"X": 0.7464625239372253, "Y": 0.2973848283290863}]}, "Id": "21bcd591-c53b-4d81-908a-141d2f73aa98"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "UPGF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04447125270962715, "Height": 0.00985063798725605, "Left": 0.7757918834686279, "Top": 0.28745895624160767}, "Polygon": [{"X": 0.7757919430732727, "Y": 0.2874590754508972}, {"X": 0.820263147354126, "Y": 0.28745895624160767}, {"X": 0.8202630877494812, "Y": 0.2973094582557678}, {"X": 0.7757918834686279, "Y": 0.2973095774650574}]}, "Id": "259c9a32-00ad-425a-b67b-90c6f229a958"}, {"BlockType": "WORD", "Confidence": 99.68590545654297, "Text": "102-series", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07486095279455185, "Height": 0.009869084693491459, "Left": 0.8277696967124939, "Top": 0.2876264452934265}, "Polygon": [{"X": 0.8277697563171387, "Y": 0.2876266539096832}, {"X": 0.902630627155304, "Y": 0.2876264452934265}, {"X": 0.9026305675506592, "Y": 0.2974953353404999}, {"X": 0.8277696967124939, "Y": 0.2974955439567566}]}, "Id": "a3af5d7e-3efa-446e-83d2-885ebb1eed01"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "rules", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034716200083494186, "Height": 0.009959370829164982, "Left": 0.09430932998657227, "Top": 0.30178016424179077}, "Polygon": [{"X": 0.09430933743715286, "Y": 0.30178025364875793}, {"X": 0.12902551889419556, "Y": 0.30178016424179077}, {"X": 0.12902551889419556, "Y": 0.311739444732666}, {"X": 0.09430932998657227, "Y": 0.3117395341396332}]}, "Id": "c886fb04-aa37-4bde-8be8-58ee3879fb77"}, {"BlockType": "WORD", "Confidence": 99.33294677734375, "Text": "tariff", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03263344243168831, "Height": 0.010107752867043018, "Left": 0.13362731039524078, "Top": 0.3016268312931061}, "Polygon": [{"X": 0.13362732529640198, "Y": 0.30162692070007324}, {"X": 0.1662607491016388, "Y": 0.3016268312931061}, {"X": 0.1662607342004776, "Y": 0.31173449754714966}, {"X": 0.13362731039524078, "Y": 0.3117345869541168}]}, "Id": "41a9d12e-8367-4b98-8b4a-03663c9ca0be"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "items", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03859976306557655, "Height": 0.010032448917627335, "Left": 0.17001017928123474, "Top": 0.3017001450061798}, "Polygon": [{"X": 0.17001019418239594, "Y": 0.30170026421546936}, {"X": 0.208609938621521, "Y": 0.3017001450061798}, {"X": 0.2086099237203598, "Y": 0.3117325007915497}, {"X": 0.17001017928123474, "Y": 0.31173259019851685}]}, "Id": "dd0f536e-4798-4c5a-a45c-4ca95ec455fb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "362", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026840394362807274, "Height": 0.009976796805858612, "Left": 0.21351468563079834, "Top": 0.3016107380390167}, "Polygon": [{"X": 0.21351470053195953, "Y": 0.3016107976436615}, {"X": 0.24035507440567017, "Y": 0.3016107380390167}, {"X": 0.24035504460334778, "Y": 0.31158745288848877}, {"X": 0.21351468563079834, "Y": 0.31158754229545593}]}, "Id": "94eb70a5-d6ba-4c22-b23f-6eff16e1aa44"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026571054011583328, "Height": 0.01005273126065731, "Left": 0.24540583789348602, "Top": 0.3016955852508545}, "Polygon": [{"X": 0.2454058676958084, "Y": 0.30169567465782166}, {"X": 0.27197688817977905, "Y": 0.3016955852508545}, {"X": 0.27197685837745667, "Y": 0.3117482662200928}, {"X": 0.24540583789348602, "Y": 0.31174832582473755}]}, "Id": "5a05d025-3f06-4bea-90ec-35d74c5aa67d"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "990.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030828295275568962, "Height": 0.009904297068715096, "Left": 0.27726706862449646, "Top": 0.30176734924316406}, "Polygon": [{"X": 0.27726709842681885, "Y": 0.3017674386501312}, {"X": 0.30809536576271057, "Y": 0.30176734924316406}, {"X": 0.3080953359603882, "Y": 0.31167158484458923}, {"X": 0.27726706862449646, "Y": 0.311671644449234}]}, "Id": "053fe598-e181-4ca4-86ff-59720bc60d7a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Handling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06309546530246735, "Height": 0.012526462785899639, "Left": 0.44676703214645386, "Top": 0.3331822156906128}, "Polygon": [{"X": 0.44676709175109863, "Y": 0.33318236470222473}, {"X": 0.50986248254776, "Y": 0.3331822156906128}, {"X": 0.5098624229431152, "Y": 0.3457085192203522}, {"X": 0.44676703214645386, "Y": 0.3457086682319641}]}, "Id": "f6515c4c-80b6-47fa-943a-0fab4aef0fb9"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0514938049018383, "Height": 0.012421411462128162, "Left": 0.6810327768325806, "Top": 0.33351945877075195}, "Polygon": [{"X": 0.6810328960418701, "Y": 0.3335195779800415}, {"X": 0.7325266003608704, "Y": 0.33351945877075195}, {"X": 0.7325264811515808, "Y": 0.3459407389163971}, {"X": 0.6810327768325806, "Y": 0.34594085812568665}]}, "Id": "3cf1914a-79bc-4eaf-817b-e1ae0be4f772"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Units", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03660602867603302, "Height": 0.009925858117640018, "Left": 0.4601740837097168, "Top": 0.34745562076568604}, "Polygon": [{"X": 0.4601741135120392, "Y": 0.3474557399749756}, {"X": 0.4967800974845886, "Y": 0.34745562076568604}, {"X": 0.49678006768226624, "Y": 0.3573814034461975}, {"X": 0.4601740837097168, "Y": 0.3573814928531647}]}, "Id": "9a04277b-87d7-4683-985a-09efa04e320d"}, {"BlockType": "WORD", "Confidence": 77.4499740600586, "Text": "(lbs.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03575987368822098, "Height": 0.012354874052107334, "Left": 0.6892008185386658, "Top": 0.34784987568855286}, "Polygon": [{"X": 0.6892009377479553, "Y": 0.34784996509552}, {"X": 0.7249606847763062, "Y": 0.34784987568855286}, {"X": 0.7249606251716614, "Y": 0.36020466685295105}, {"X": 0.6892008185386658, "Y": 0.3602047562599182}]}, "Id": "64cfbcdf-128b-4365-83fd-6d83e66ce173"}, {"BlockType": "WORD", "Confidence": 99.609375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005624322220683098, "Height": 0.009576208889484406, "Left": 0.487356573343277, "Top": 0.37887826561927795}, "Polygon": [{"X": 0.48735660314559937, "Y": 0.37887829542160034}, {"X": 0.4929808974266052, "Y": 0.37887826561927795}, {"X": 0.49298083782196045, "Y": 0.38845446705818176}, {"X": 0.487356573343277, "Y": 0.38845446705818176}]}, "Id": "780fcc36-28c7-44e0-8f87-f5e3bc44df5c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "385", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02673410438001156, "Height": 0.009930433705449104, "Left": 0.6971701979637146, "Top": 0.3786565065383911}, "Polygon": [{"X": 0.6971702575683594, "Y": 0.3786565661430359}, {"X": 0.7239043116569519, "Y": 0.3786565065383911}, {"X": 0.7239042520523071, "Y": 0.3885868787765503}, {"X": 0.6971701979637146, "Y": 0.38858693838119507}]}, "Id": "24995c9e-a88a-4599-bcf9-5624dad5a0dc"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00526722427457571, "Height": 0.009874563664197922, "Left": 0.48748067021369934, "Top": 0.3961121737957001}, "Polygon": [{"X": 0.48748070001602173, "Y": 0.3961121737957001}, {"X": 0.4927479028701782, "Y": 0.3961121737957001}, {"X": 0.49274784326553345, "Y": 0.4059867262840271}, {"X": 0.48748067021369934, "Y": 0.4059867262840271}]}, "Id": "c42c40b9-a510-4d26-8d57-262809e0d33b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "670", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026832304894924164, "Height": 0.009791750460863113, "Left": 0.6972293257713318, "Top": 0.39598944783210754}, "Polygon": [{"X": 0.6972293853759766, "Y": 0.3959895074367523}, {"X": 0.7240616083145142, "Y": 0.39598944783210754}, {"X": 0.7240615487098694, "Y": 0.4057811200618744}, {"X": 0.6972293257713318, "Y": 0.40578117966651917}]}, "Id": "de0a742b-dcb5-4106-bece-b590c342b91b"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005300783086568117, "Height": 0.009753221645951271, "Left": 0.48747172951698303, "Top": 0.4130985736846924}, "Polygon": [{"X": 0.4874717891216278, "Y": 0.4130985736846924}, {"X": 0.49277251958847046, "Y": 0.4130985736846924}, {"X": 0.4927724599838257, "Y": 0.4228517711162567}, {"X": 0.48747172951698303, "Y": 0.4228518009185791}]}, "Id": "94a0880b-53ce-4760-91d8-de9320227572"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "750", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026598377153277397, "Height": 0.009648303501307964, "Left": 0.6972914338111877, "Top": 0.41314181685447693}, "Polygon": [{"X": 0.6972914934158325, "Y": 0.4131418764591217}, {"X": 0.7238897681236267, "Y": 0.41314181685447693}, {"X": 0.7238897085189819, "Y": 0.4227900505065918}, {"X": 0.6972914338111877, "Y": 0.4227901101112366}]}, "Id": "931542b9-f254-4df8-9c50-18e285cf809c"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Actual", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04524030536413193, "Height": 0.009914706461131573, "Left": 0.0935739204287529, "Top": 0.44688180088996887}, "Polygon": [{"X": 0.0935739278793335, "Y": 0.44688189029693604}, {"X": 0.13881422579288483, "Y": 0.44688180088996887}, {"X": 0.13881421089172363, "Y": 0.45679643750190735}, {"X": 0.0935739204287529, "Y": 0.4567965269088745}]}, "Id": "a1d01661-3181-4f6e-8258-efe0ea9777a7"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04021941125392914, "Height": 0.010059604421257973, "Left": 0.14436036348342896, "Top": 0.44682854413986206}, "Polygon": [{"X": 0.14436037838459015, "Y": 0.4468286335468292}, {"X": 0.1845797747373581, "Y": 0.44682854413986206}, {"X": 0.1845797598361969, "Y": 0.4568880796432495}, {"X": 0.14436036348342896, "Y": 0.4568881690502167}]}, "Id": "b35819a2-2cda-4d03-9eda-6d453d324a79"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03398623317480087, "Height": 0.010061873123049736, "Left": 0.18898968398571014, "Top": 0.4468747675418854}, "Polygon": [{"X": 0.18898969888687134, "Y": 0.44687482714653015}, {"X": 0.2229759246110916, "Y": 0.4468747675418854}, {"X": 0.22297589480876923, "Y": 0.4569365680217743}, {"X": 0.18898968398571014, "Y": 0.45693662762641907}]}, "Id": "e0d9e686-85b2-408a-9e66-cdcaa4e376b6"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008547382429242134, "Height": 0.009630661457777023, "Left": 0.4861716628074646, "Top": 0.4474080204963684}, "Polygon": [{"X": 0.4861717224121094, "Y": 0.4474080502986908}, {"X": 0.4947190582752228, "Y": 0.4474080204963684}, {"X": 0.494718998670578, "Y": 0.45703867077827454}, {"X": 0.4861716628074646, "Y": 0.4570387005805969}]}, "Id": "a845dc49-3495-4e58-a749-4b21c303fd36"}, {"BlockType": "WORD", "Confidence": 99.51908874511719, "Text": "1,805", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039480969309806824, "Height": 0.011169543489813805, "Left": 0.6843679547309875, "Top": 0.4473262429237366}, "Polygon": [{"X": 0.6843680143356323, "Y": 0.44732633233070374}, {"X": 0.7238489389419556, "Y": 0.4473262429237366}, {"X": 0.723848819732666, "Y": 0.45849570631980896}, {"X": 0.6843679547309875, "Y": 0.4584957957267761}]}, "Id": "9579adf0-9c41-4101-9a10-6c20df0a93c8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "0811ccfc-c156-4412-b977-a14a12efdb5e", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "0811ccfc-c156-4412-b977-a14a12efdb5e", "content-type": "application/x-amz-json-1.1", "content-length": "78059", "date": "Wed, 24 Sep 2025 09:55:19 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "b1ef2f6a-7c8e-41c8-bb04-a75e3e251773", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:55:22 GMT", "content-type": "application/json", "content-length": "2310", "connection": "keep-alive", "x-amzn-requestid": "b1ef2f6a-7c8e-41c8-bb04-a75e3e251773"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page. Determine doc type. The page is a weight certificate. Keywords: WEIGHT CERTIFICATE, WEIGHT CERTIFICATION, scale, weight, lbs. So likely scale_ticket or weight_and_inspection_cert. Let's see definitions.\n\nscale_ticket: weight record from a scale for vehicle/load used for billing/compliance. Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, date, time, Ticket no. This page has weight, date, weighs. So likely scale_ticket.\n\nBut weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width but not rechecked. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. It also says check if nmfc_cert. But this page is weight certificate, not inspection. It doesn't have \"Inspection\" or \"Certificate of Analysis\". It says \"WEIGHT CERTIFICATE\". That might be weight_and_inspection_cert? But weight_and_inspection_cert is for weight certificates, but the classification says if weight and inspection cert has weight mentioned in lbs or tons. That might fit. But let's see difference: scale_ticket is more about scale reading for billing. This is a weight certificate from a service center, \"WEIGHT CERTIFICATE\" with mention of legal for trade. That seems like a weight certificate for compliance, maybe weight_and_inspection_cert. But the difference: weight_and_inspection_cert is a certificate confirming weight. Yes. That matches. The content: \"WEIGHT CERTIFICATION\" and \"We certify that the mentioned shipment has been weighed and that the correct weight as stated is the true and accurate weight of the shipment.\" That is a certification. So weight_and_inspection_cert seems appropriate.\n\nCheck nmfc_cert: It requires NMFC code, correction, etc. Not present. So weight_and_inspection_cert.\n\nThus output page 1 with doc_type weight_and_inspection_cert."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2672, "outputTokens": 426, "totalTokens": 3098}, "metrics": {"latencyMs": 1819}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/838ea519_K9VSARJOKAIZHNJ5RBDT.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nT<PERSON><PERSON><PERSON> Freight\nTForce\nP.O. Box 1216\nRichmond, Virginia 23218-1216\n1-800-333-7400\nFreight\nhttps://www.tforcefreight.com\nWEIGHT CERTIFICATE\nFreight Bill Number: 465486206\nWeighed Date: 9/8/2025\nWeighing performed at KAN Service Center\nWEIGHT CERTIFICATION\nWe certify that the mentioned shipment has been weighed and that the correct weight as stated is the true and\naccurate weight of the shipment. The scales used are certified 'legal for trade' and are maintained to conform to\nall established federal and state guidelines. Established procedures for verifying weight meets the guidelines\npublished in the National Motor Freight Classification 100-series items 360 and 995 and the UPGF 102-series\nrules tariff items 362 and 990.\nHandling\nWeight\nUnits\n(lbs.)\n1\n385\n1\n670\n1\n750\nActual Scale Total\n3\n1,805\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}