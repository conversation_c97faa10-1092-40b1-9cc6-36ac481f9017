{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0022182585671544075, "Y": 0.0}, {"X": 1.0, "Y": 0.004608245566487312}, {"X": 0.9987070560455322, "Y": 1.0}, {"X": 0.0, "Y": 0.9995465874671936}]}, "Id": "2514934b-2058-4b08-8431-b577ba4894d2", "Relationships": [{"Type": "CHILD", "Ids": ["f1354940-f056-4dfc-834d-f02ddcc83c0d", "976325a9-9e4e-40d1-8228-05601fe14101", "a21b1014-1ad9-4c5a-ab85-6a4a60563d43", "83434e7f-a01c-450e-b855-c2269c93ea5b", "5e4414c9-e620-4629-b0ea-c74ab521946b", "23808ed1-2a4f-4e27-8cbe-59dec08e0b06", "eb8e5994-b976-4240-a74e-120bed67d1a7", "ec716268-8699-481a-91cd-d39e4efbc8c3", "8fed87a4-dcf7-42a3-8c1e-d486cb32a50f", "20204ad9-99b0-4149-9e9b-d0abbda7efbe", "cc6e3209-68f6-4d39-94f1-372b986c5a49", "2c479e2e-51cd-4f40-9c72-76224289f187", "5bc5ae93-1ad1-4c4c-9301-2cbfb12a5bdc", "43b92816-f0e5-46c3-8980-353a151b7b9c", "16a05011-ca08-465a-becb-a19d9d572974", "cc3e7044-2c70-4b49-915d-f226e36bbef1", "6c9bcd67-a3bb-4bc2-83eb-68a7299ea3c9", "5717af93-17dc-4c46-96bd-3ca7d28740a2", "53be481f-3d2a-43a0-8f65-5c81a829d8bd", "afafe053-dfab-4a02-b731-49a37d2926e4", "a8125c69-b763-4e98-8496-e065299ff0a7", "0f513839-cf3a-47ec-ae79-474168fa7790", "6cbdf251-bb7b-4d60-9edf-3f8eec7ea668", "1a357bf8-bd10-4b66-8c6b-ff57d5acecf4"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "DATE: 20-Feb", "Geometry": {"BoundingBox": {"Width": 0.1579749882221222, "Height": 0.03215382620692253, "Left": 0.21548213064670563, "Top": 0.0429195910692215}, "Polygon": [{"X": 0.21636752784252167, "Y": 0.0429195910692215}, {"X": 0.373457133769989, "Y": 0.04811938852071762}, {"X": 0.37257885932922363, "Y": 0.07507342100143433}, {"X": 0.21548213064670563, "Y": 0.06989724189043045}]}, "Id": "f1354940-f056-4dfc-834d-f02ddcc83c0d", "Relationships": [{"Type": "CHILD", "Ids": ["70ec6daf-7c7c-4f0f-be8d-7b1fb810349e", "39a079d8-79a2-4537-ad8e-1b2f19a65869"]}]}, {"BlockType": "LINE", "Confidence": 99.74555969238281, "Text": "UNIT #: 92644/283420", "Geometry": {"BoundingBox": {"Width": 0.2517589330673218, "Height": 0.04012957215309143, "Left": 0.1949082463979721, "Top": 0.11189242452383041}, "Polygon": [{"X": 0.1959584802389145, "Y": 0.11189242452383041}, {"X": 0.4466671645641327, "Y": 0.12009291350841522}, {"X": 0.44563040137290955, "Y": 0.15202198922634125}, {"X": 0.1949082463979721, "Y": 0.1438661813735962}]}, "Id": "976325a9-9e4e-40d1-8228-05601fe14101", "Relationships": [{"Type": "CHILD", "Ids": ["b44cef86-0f85-4edc-bf52-ca85cf2bcfda", "34379af1-8abd-49c9-9500-a1dd6c57a63c", "434240bf-1251-4882-98ee-15dec94e28ed"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Truck Ready Time: 5:00", "Geometry": {"BoundingBox": {"Width": 0.3100365698337555, "Height": 0.049046456813812256, "Left": 0.47112029790878296, "Top": 0.14964473247528076}, "Polygon": [{"X": 0.4723871946334839, "Y": 0.14964473247528076}, {"X": 0.7811568379402161, "Y": 0.15969444811344147}, {"X": 0.779910147190094, "Y": 0.19869118928909302}, {"X": 0.47112029790878296, "Y": 0.1887088268995285}]}, "Id": "a21b1014-1ad9-4c5a-ab85-6a4a60563d43", "Relationships": [{"Type": "CHILD", "Ids": ["0ecde86f-3cd3-4da6-a1f9-bba78a5100e3", "c317153e-aa41-4799-be90-eb8ae9a4771f", "593e0942-fc25-4f23-b435-2bf6e6e102aa", "50f7f40e-c439-4229-bbfa-ea941c7a367f"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.18603163957595825, "Height": 0.03132793307304382, "Left": 0.18472658097743988, "Top": 0.18267029523849487}, "Polygon": [{"X": 0.1855602264404297, "Y": 0.18267029523849487}, {"X": 0.37075820565223694, "Y": 0.18865393102169037}, {"X": 0.36993247270584106, "Y": 0.2139982283115387}, {"X": 0.18472658097743988, "Y": 0.2080407738685608}]}, "Id": "83434e7f-a01c-450e-b855-c2269c93ea5b", "Relationships": [{"Type": "CHILD", "Ids": ["256aef87-3086-48f2-be6e-8d0b7d577f7c", "92b16c85-e576-4e4a-854b-36f0e594fbe5"]}]}, {"BlockType": "LINE", "Confidence": 98.97720336914062, "Text": "Truck Departure: 5:00", "Geometry": {"BoundingBox": {"Width": 0.31131747364997864, "Height": 0.05023849755525589, "Left": 0.4700527489185333, "Top": 0.2184925228357315}, "Polygon": [{"X": 0.4713609218597412, "Y": 0.2184925228357315}, {"X": 0.781370222568512, "Y": 0.2284623384475708}, {"X": 0.7800830006599426, "Y": 0.268731027841568}, {"X": 0.4700527489185333, "Y": 0.25883105397224426}]}, "Id": "5e4414c9-e620-4629-b0ea-c74ab521946b", "Relationships": [{"Type": "CHILD", "Ids": ["5fd867a2-c51e-4177-8084-e7e9546af78c", "56e13b35-6bd3-49b7-aa8e-bf35fac17468", "c89ad3de-6602-4b38-b31c-a02b6f57d384"]}]}, {"BlockType": "LINE", "Confidence": 99.78707885742188, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.2669418156147003, "Height": 0.04037947952747345, "Left": 0.07733608037233353, "Top": 0.24358193576335907}, "Polygon": [{"X": 0.07839035242795944, "Y": 0.24358193576335907}, {"X": 0.34427788853645325, "Y": 0.2520764470100403}, {"X": 0.34323784708976746, "Y": 0.2839614152908325}, {"X": 0.07733608037233353, "Y": 0.2755142152309418}]}, "Id": "23808ed1-2a4f-4e27-8cbe-59dec08e0b06", "Relationships": [{"Type": "CHILD", "Ids": ["ea1cf238-664b-4635-80ad-506a7ed304b2", "3549c926-58e7-46b3-8413-1cba6b222337", "05318c6c-4f91-4df5-acf8-4889e99174da"]}]}, {"BlockType": "LINE", "Confidence": 99.68112182617188, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.05514424294233322, "Height": 0.022276688367128372, "Left": 0.31274813413619995, "Top": 0.31856173276901245}, "Polygon": [{"X": 0.31341975927352905, "Y": 0.31856173276901245}, {"X": 0.36789238452911377, "Y": 0.32028132677078247}, {"X": 0.3672226369380951, "Y": 0.3408384323120117}, {"X": 0.31274813413619995, "Y": 0.3391250669956207}]}, "Id": "eb8e5994-b976-4240-a74e-120bed67d1a7", "Relationships": [{"Type": "CHILD", "Ids": ["487f6ef1-49b4-4af7-88ec-e8f4c62381f6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.11779531836509705, "Height": 0.03273661062121391, "Left": 0.1132243275642395, "Top": 0.3450755476951599}, "Polygon": [{"X": 0.11418301612138748, "Y": 0.3450755476951599}, {"X": 0.23101964592933655, "Y": 0.3487423360347748}, {"X": 0.23006665706634521, "Y": 0.3778121769428253}, {"X": 0.1132243275642395, "Y": 0.37416431307792664}]}, "Id": "ec716268-8699-481a-91cd-d39e4efbc8c3", "Relationships": [{"Type": "CHILD", "Ids": ["df8af35f-066c-43e0-84d3-30319af014a8"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.027638239786028862, "Height": 0.024810055270791054, "Left": 0.32599076628685, "Top": 0.35430312156677246}, "Polygon": [{"X": 0.32677316665649414, "Y": 0.35430312156677246}, {"X": 0.3536290228366852, "Y": 0.35514557361602783}, {"X": 0.352847695350647, "Y": 0.37911316752433777}, {"X": 0.32599076628685, "Y": 0.3782743215560913}]}, "Id": "8fed87a4-dcf7-42a3-8c1e-d486cb32a50f", "Relationships": [{"Type": "CHILD", "Ids": ["a2e81c20-8463-451d-ab16-c740e5c4af6f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.03969407081604004, "Height": 0.02513890154659748, "Left": 0.40297356247901917, "Top": 0.35680967569351196}, "Polygon": [{"X": 0.4037516415119171, "Y": 0.35680967569351196}, {"X": 0.4426676332950592, "Y": 0.35803043842315674}, {"X": 0.4418911039829254, "Y": 0.3819485902786255}, {"X": 0.40297356247901917, "Y": 0.38073301315307617}]}, "Id": "20204ad9-99b0-4149-9e9b-d0abbda7efbe", "Relationships": [{"Type": "CHILD", "Ids": ["c8ae1fe3-13f6-4938-9d5a-78f1d8817f40"]}]}, {"BlockType": "LINE", "Confidence": 99.94791412353516, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.1641554832458496, "Height": 0.029660876840353012, "Left": 0.5835081338882446, "Top": 0.36434274911880493}, "Polygon": [{"X": 0.5843002796173096, "Y": 0.36434274911880493}, {"X": 0.7476636171340942, "Y": 0.36946555972099304}, {"X": 0.7468781471252441, "Y": 0.39400362968444824}, {"X": 0.5835081338882446, "Y": 0.3889032304286957}]}, "Id": "cc6e3209-68f6-4d39-94f1-372b986c5a49", "Relationships": [{"Type": "CHILD", "Ids": ["dca8b043-be65-4d40-8a06-e6aa141dc50f", "5a73abcc-df68-43d2-a026-8158b42cf0a9", "4c55835f-6306-4ab5-8db8-8ca50a5424b3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.10653107613325119, "Height": 0.026315394788980484, "Left": 0.8774412870407104, "Top": 0.3616449534893036}, "Polygon": [{"X": 0.8781731128692627, "Y": 0.3616449534893036}, {"X": 0.9839723110198975, "Y": 0.36496976017951965}, {"X": 0.9832445383071899, "Y": 0.3879603445529938}, {"X": 0.8774412870407104, "Y": 0.3846491873264313}]}, "Id": "2c479e2e-51cd-4f40-9c72-76224289f187", "Relationships": [{"Type": "CHILD", "Ids": ["3897256f-b2ba-4dde-a258-baeeb2923ec5", "334f507c-3218-4e4a-bba5-b0d918a305c6"]}]}, {"BlockType": "LINE", "Confidence": 84.02682495117188, "Text": "Isk", "Geometry": {"BoundingBox": {"Width": 0.05760449171066284, "Height": 0.05161376670002937, "Left": 0.2268626093864441, "Top": 0.4105585515499115}, "Polygon": [{"X": 0.22849825024604797, "Y": 0.4105585515499115}, {"X": 0.28446710109710693, "Y": 0.4122955799102783}, {"X": 0.2828361392021179, "Y": 0.4621722996234894}, {"X": 0.2268626093864441, "Y": 0.4604508578777313}]}, "Id": "5bc5ae93-1ad1-4c4c-9301-2cbfb12a5bdc", "Relationships": [{"Type": "CHILD", "Ids": ["8efa4bae-1497-4686-88c8-67185970fd4f"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1040", "Geometry": {"BoundingBox": {"Width": 0.06970304250717163, "Height": 0.03342719376087189, "Left": 0.8907788395881653, "Top": 0.42256876826286316}, "Polygon": [{"X": 0.89177405834198, "Y": 0.42256876826286316}, {"X": 0.9604818820953369, "Y": 0.42470449209213257}, {"X": 0.9594902992248535, "Y": 0.45599597692489624}, {"X": 0.8907788395881653, "Y": 0.45387229323387146}]}, "Id": "43b92816-f0e5-46c3-8980-353a151b7b9c", "Relationships": [{"Type": "CHILD", "Ids": ["dd639875-a1e7-4bee-8574-c9370a01134e"]}]}, {"BlockType": "LINE", "Confidence": 59.43339538574219, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.05564732104539871, "Height": 0.046167951077222824, "Left": 0.4481831192970276, "Top": 0.42416319251060486}, "Polygon": [{"X": 0.4496272802352905, "Y": 0.42416319251060486}, {"X": 0.5038304328918457, "Y": 0.42584335803985596}, {"X": 0.5023903250694275, "Y": 0.4703311324119568}, {"X": 0.4481831192970276, "Y": 0.4686644375324249}]}, "Id": "16a05011-ca08-465a-becb-a19d9d572974", "Relationships": [{"Type": "CHILD", "Ids": ["0ca8a920-2b5b-413c-b954-f8d62966dffa"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Parkn Manufacturing", "Geometry": {"BoundingBox": {"Width": 0.20832109451293945, "Height": 0.040868084877729416, "Left": 0.04540211334824562, "Top": 0.45253515243530273}, "Polygon": [{"X": 0.04654354974627495, "Y": 0.45253515243530273}, {"X": 0.2537232041358948, "Y": 0.45890969038009644}, {"X": 0.2525937855243683, "Y": 0.49340322613716125}, {"X": 0.04540211334824562, "Y": 0.4870685338973999}]}, "Id": "cc3e7044-2c70-4b49-915d-f226e36bbef1", "Relationships": [{"Type": "CHILD", "Ids": ["ae7236cf-d4bb-4c71-bffb-14a38f2fa620", "e793e481-69ce-443d-97d0-c49b19689d81"]}]}, {"BlockType": "LINE", "Confidence": 91.66035461425781, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01628855988383293, "Height": 0.025888467207551003, "Left": 0.3308030962944031, "Top": 0.4660031199455261}, "Polygon": [{"X": 0.3316322863101959, "Y": 0.4660031199455261}, {"X": 0.3470916450023651, "Y": 0.4664783775806427}, {"X": 0.3462630808353424, "Y": 0.4918915927410126}, {"X": 0.3308030962944031, "Y": 0.4914185404777527}]}, "Id": "6c9bcd67-a3bb-4bc2-83eb-68a7299ea3c9", "Relationships": [{"Type": "CHILD", "Ids": ["92f08899-c78c-49d9-9f84-2abe4d2bf431"]}]}, {"BlockType": "LINE", "Confidence": 89.10963439941406, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.16032011806964874, "Height": 0.029697904363274574, "Left": 0.4126451015472412, "Top": 0.46869713068008423}, "Polygon": [{"X": 0.41345176100730896, "Y": 0.46869713068008423}, {"X": 0.5729652643203735, "Y": 0.47360068559646606}, {"X": 0.5721652507781982, "Y": 0.49839502573013306}, {"X": 0.4126451015472412, "Y": 0.4935135543346405}]}, "Id": "5717af93-17dc-4c46-96bd-3ca7d28740a2", "Relationships": [{"Type": "CHILD", "Ids": ["0eefb6fd-db57-4b33-b8e6-9534c3c61758", "4abbcf1e-190b-4380-86bd-25d0c278a9a7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1055", "Geometry": {"BoundingBox": {"Width": 0.07424264401197433, "Height": 0.04109891876578331, "Left": 0.8813396692276001, "Top": 0.510494589805603}, "Polygon": [{"X": 0.8825761675834656, "Y": 0.510494589805603}, {"X": 0.955582320690155, "Y": 0.5127276182174683}, {"X": 0.9543505907058716, "Y": 0.551593542098999}, {"X": 0.8813396692276001, "Y": 0.5493764281272888}]}, "Id": "53be481f-3d2a-43a0-8f65-5c81a829d8bd", "Relationships": [{"Type": "CHILD", "Ids": ["03322bae-331d-4e5c-bc8a-66e9c0cd791c"]}]}, {"BlockType": "LINE", "Confidence": 71.42219543457031, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.06532468646764755, "Height": 0.047232694923877716, "Left": 0.4515901505947113, "Top": 0.620549201965332}, "Polygon": [{"X": 0.4530608654022217, "Y": 0.620549201965332}, {"X": 0.5169148445129395, "Y": 0.6224579811096191}, {"X": 0.5154489874839783, "Y": 0.6677818894386292}, {"X": 0.4515901505947113, "Y": 0.665889322757721}]}, "Id": "afafe053-dfab-4a02-b731-49a37d2926e4", "Relationships": [{"Type": "CHILD", "Ids": ["4ded61a0-dfb1-4e18-8f6b-9456901b1ad2"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "0830", "Geometry": {"BoundingBox": {"Width": 0.07970772683620453, "Height": 0.037620823830366135, "Left": 0.8743544220924377, "Top": 0.6406471729278564}, "Polygon": [{"X": 0.8754768371582031, "Y": 0.6406471729278564}, {"X": 0.9540621042251587, "Y": 0.6429930329322815}, {"X": 0.9529443383216858, "Y": 0.6782680153846741}, {"X": 0.8743544220924377, "Y": 0.6759377717971802}]}, "Id": "a8125c69-b763-4e98-8496-e065299ff0a7", "Relationships": [{"Type": "CHILD", "Ids": ["0a7ca96e-3738-4ae0-975c-aad8528b1359"]}]}, {"BlockType": "LINE", "Confidence": 96.56635284423828, "Text": "Alumalloy - Avon Lake", "Geometry": {"BoundingBox": {"Width": 0.22078944742679596, "Height": 0.03716304898262024, "Left": 0.045977212488651276, "Top": 0.658808708190918}, "Polygon": [{"X": 0.04699138551950455, "Y": 0.658808708190918}, {"X": 0.26676666736602783, "Y": 0.6653161644935608}, {"X": 0.2657638192176819, "Y": 0.6959717869758606}, {"X": 0.045977212488651276, "Y": 0.689501941204071}]}, "Id": "0f513839-cf3a-47ec-ae79-474168fa7790", "Relationships": [{"Type": "CHILD", "Ids": ["376bcc5c-04b8-4dff-9125-62d200c65f5e", "88675896-d3cf-470f-ad17-e005a3164b5b", "fa73e31e-f617-483e-9dbf-47c9b0f66509"]}]}, {"BlockType": "LINE", "Confidence": 91.06285858154297, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15923504531383514, "Height": 0.029367107897996902, "Left": 0.4088922441005707, "Top": 0.6709004044532776}, "Polygon": [{"X": 0.40969493985176086, "Y": 0.6709004044532776}, {"X": 0.5681272745132446, "Y": 0.6755902767181396}, {"X": 0.5673311352729797, "Y": 0.700267493724823}, {"X": 0.4088922441005707, "Y": 0.6955994367599487}]}, "Id": "6cbdf251-bb7b-4d60-9edf-3f8eec7ea668", "Relationships": [{"Type": "CHILD", "Ids": ["c7cea906-b159-4b5a-91c1-efe0e10477c5", "baa4d2d0-07c4-4e62-8f05-dc38e425f30e"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "0840", "Geometry": {"BoundingBox": {"Width": 0.08023902028799057, "Height": 0.03638775274157524, "Left": 0.8741713166236877, "Top": 0.7459439039230347}, "Polygon": [{"X": 0.875255286693573, "Y": 0.7459439039230347}, {"X": 0.9544103145599365, "Y": 0.7482596635818481}, {"X": 0.9533308744430542, "Y": 0.782331645488739}, {"X": 0.8741713166236877, "Y": 0.7800310254096985}]}, "Id": "1a357bf8-bd10-4b66-8c6b-ff57d5acecf4", "Relationships": [{"Type": "CHILD", "Ids": ["1f1e6129-ade5-44e9-a713-2ea671699b75"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06366045027971268, "Height": 0.02693450078368187, "Left": 0.21555152535438538, "Top": 0.0429195910692215}, "Polygon": [{"X": 0.21636752784252167, "Y": 0.0429195910692215}, {"X": 0.27921199798583984, "Y": 0.04499979689717293}, {"X": 0.2783986032009125, "Y": 0.06985409557819366}, {"X": 0.21555152535438538, "Y": 0.06778259575366974}]}, "Id": "70ec6daf-7c7c-4f0f-be8d-7b1fb810349e"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "20-Feb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06987423449754715, "Height": 0.028067966923117638, "Left": 0.3035447597503662, "Top": 0.04700545221567154}, "Polygon": [{"X": 0.3043878674507141, "Y": 0.04700545221567154}, {"X": 0.37341898679733276, "Y": 0.04928998276591301}, {"X": 0.37257885932922363, "Y": 0.07507342100143433}, {"X": 0.3035447597503662, "Y": 0.07279881834983826}]}, "Id": "39a079d8-79a2-4537-ad8e-1b2f19a65869"}, {"BlockType": "WORD", "Confidence": 99.60857391357422, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05581699684262276, "Height": 0.026889165863394737, "Left": 0.19513103365898132, "Top": 0.11198543757200241}, "Polygon": [{"X": 0.19595542550086975, "Y": 0.11198543757200241}, {"X": 0.250948041677475, "Y": 0.11378417909145355}, {"X": 0.2501259744167328, "Y": 0.1388746052980423}, {"X": 0.19513103365898132, "Y": 0.13708356022834778}]}, "Id": "b44cef86-0f85-4edc-bf52-ca85cf2bcfda"}, {"BlockType": "WORD", "Confidence": 99.63787078857422, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02441558614373207, "Height": 0.025526700541377068, "Left": 0.2539274990558624, "Top": 0.1138150691986084}, "Polygon": [{"X": 0.25473853945732117, "Y": 0.1138150691986084}, {"X": 0.2783430814743042, "Y": 0.11458715796470642}, {"X": 0.27753302454948425, "Y": 0.13934177160263062}, {"X": 0.2539274990558624, "Y": 0.13857294619083405}]}, "Id": "34379af1-8abd-49c9-9500-a1dd6c57a63c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "92644/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1445275843143463, "Height": 0.035232093185186386, "Left": 0.302094429731369, "Top": 0.11678989976644516}, "Polygon": [{"X": 0.30309343338012695, "Y": 0.11678989976644516}, {"X": 0.44662201404571533, "Y": 0.12148348987102509}, {"X": 0.44563040137290955, "Y": 0.15202198922634125}, {"X": 0.302094429731369, "Y": 0.14735287427902222}]}, "Id": "434240bf-1251-4882-98ee-15dec94e28ed"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056382160633802414, "Height": 0.026549579575657845, "Left": 0.4713059961795807, "Top": 0.1582314521074295}, "Polygon": [{"X": 0.47210872173309326, "Y": 0.1582314521074295}, {"X": 0.5276881456375122, "Y": 0.16003774106502533}, {"X": 0.5268877744674683, "Y": 0.1847810298204422}, {"X": 0.4713059961795807, "Y": 0.1829824298620224}]}, "Id": "0ecde86f-3cd3-4da6-a1f9-bba78a5100e3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06513089686632156, "Height": 0.0327693447470665, "Left": 0.530707061290741, "Top": 0.15993928909301758}, "Polygon": [{"X": 0.5316998362541199, "Y": 0.15993928909301758}, {"X": 0.5958380103111267, "Y": 0.16202381253242493}, {"X": 0.5948485136032104, "Y": 0.19270864129066467}, {"X": 0.530707061290741, "Y": 0.19063511490821838}]}, "Id": "c317153e-aa41-4799-be90-eb8ae9a4771f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05584581568837166, "Height": 0.026303930208086967, "Left": 0.6008630990982056, "Top": 0.1634168177843094}, "Polygon": [{"X": 0.6016536951065063, "Y": 0.1634168177843094}, {"X": 0.6567089557647705, "Y": 0.16520576179027557}, {"X": 0.655920684337616, "Y": 0.1897207498550415}, {"X": 0.6008630990982056, "Y": 0.1879393458366394}]}, "Id": "593e0942-fc25-4f23-b435-2bf6e6e102aa"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0994606614112854, "Height": 0.03780429810285568, "Left": 0.6816961765289307, "Top": 0.1564934253692627}, "Polygon": [{"X": 0.6828081607818604, "Y": 0.1564934253692627}, {"X": 0.7811568379402161, "Y": 0.15969444811344147}, {"X": 0.7800506353378296, "Y": 0.19429773092269897}, {"X": 0.6816961765289307, "Y": 0.19111575186252594}]}, "Id": "50f7f40e-c439-4229-bbfa-ea941c7a367f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08547724783420563, "Height": 0.02809329703450203, "Left": 0.18472658097743988, "Top": 0.18267029523849487}, "Polygon": [{"X": 0.1855602264404297, "Y": 0.18267029523849487}, {"X": 0.2702038288116455, "Y": 0.18540507555007935}, {"X": 0.2693738043308258, "Y": 0.2107635885477066}, {"X": 0.18472658097743988, "Y": 0.2080407738685608}]}, "Id": "256aef87-3086-48f2-be6e-8d0b7d577f7c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06880593299865723, "Height": 0.027407344430685043, "Left": 0.3019501566886902, "Top": 0.18652324378490448}, "Polygon": [{"X": 0.30277442932128906, "Y": 0.18652324378490448}, {"X": 0.3707560896873474, "Y": 0.18871967494487762}, {"X": 0.36993467807769775, "Y": 0.21393059194087982}, {"X": 0.3019501566886902, "Y": 0.21174374222755432}]}, "Id": "92b16c85-e576-4e4a-854b-36f0e594fbe5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055950768291950226, "Height": 0.026835797354578972, "Left": 0.47026529908180237, "Top": 0.2272041141986847}, "Polygon": [{"X": 0.47107839584350586, "Y": 0.2272041141986847}, {"X": 0.5262160301208496, "Y": 0.2289746105670929}, {"X": 0.5254052877426147, "Y": 0.2540399134159088}, {"X": 0.47026529908180237, "Y": 0.252277135848999}]}, "Id": "5fd867a2-c51e-4177-8084-e7e9546af78c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10819485038518906, "Height": 0.03401782736182213, "Left": 0.5298004746437073, "Top": 0.23014459013938904}, "Polygon": [{"X": 0.5307899117469788, "Y": 0.23014459013938904}, {"X": 0.6379953026771545, "Y": 0.2335864007472992}, {"X": 0.6370113492012024, "Y": 0.26416242122650146}, {"X": 0.5298004746437073, "Y": 0.26073890924453735}]}, "Id": "56e13b35-6bd3-49b7-aa8e-bf35fac17468"}, {"BlockType": "WORD", "Confidence": 96.9714584350586, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10368028283119202, "Height": 0.03505605086684227, "Left": 0.6776899695396423, "Top": 0.22516083717346191}, "Polygon": [{"X": 0.6787104606628418, "Y": 0.22516083717346191}, {"X": 0.781370222568512, "Y": 0.2284623384475708}, {"X": 0.7803551554679871, "Y": 0.2602168917655945}, {"X": 0.6776899695396423, "Y": 0.2569335997104645}]}, "Id": "c89ad3de-6602-4b38-b31c-a02b6f57d384"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13017912209033966, "Height": 0.03603455424308777, "Left": 0.07733608037233353, "Top": 0.24358193576335907}, "Polygon": [{"X": 0.07839035242795944, "Y": 0.24358193576335907}, {"X": 0.20751520991325378, "Y": 0.24770718812942505}, {"X": 0.20646785199642181, "Y": 0.27961650490760803}, {"X": 0.07733608037233353, "Y": 0.2755142152309418}]}, "Id": "ea1cf238-664b-4635-80ad-506a7ed304b2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05738750845193863, "Height": 0.02622099779546261, "Left": 0.21226535737514496, "Top": 0.2549988925457001}, "Polygon": [{"X": 0.21306678652763367, "Y": 0.2549988925457001}, {"X": 0.2696528732776642, "Y": 0.2568044364452362}, {"X": 0.2688537836074829, "Y": 0.2812198996543884}, {"X": 0.21226535737514496, "Y": 0.2794220745563507}]}, "Id": "3549c926-58e7-46b3-8413-1cba6b222337"}, {"BlockType": "WORD", "Confidence": 99.44097137451172, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042970240116119385, "Height": 0.026401376351714134, "Left": 0.3011181056499481, "Top": 0.25654202699661255}, "Polygon": [{"X": 0.30193716287612915, "Y": 0.25654202699661255}, {"X": 0.3440883457660675, "Y": 0.2578873038291931}, {"X": 0.34327107667922974, "Y": 0.2829434275627136}, {"X": 0.3011181056499481, "Y": 0.2816040515899658}]}, "Id": "05318c6c-4f91-4df5-acf8-4889e99174da"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05514424294233322, "Height": 0.022276688367128372, "Left": 0.31274813413619995, "Top": 0.31856173276901245}, "Polygon": [{"X": 0.31341975927352905, "Y": 0.31856173276901245}, {"X": 0.36789238452911377, "Y": 0.32028132677078247}, {"X": 0.3672226369380951, "Y": 0.3408384323120117}, {"X": 0.31274813413619995, "Y": 0.3391250669956207}]}, "Id": "487f6ef1-49b4-4af7-88ec-e8f4c62381f6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11779531836509705, "Height": 0.03273661062121391, "Left": 0.1132243275642395, "Top": 0.3450755476951599}, "Polygon": [{"X": 0.11418301612138748, "Y": 0.3450755476951599}, {"X": 0.23101964592933655, "Y": 0.3487423360347748}, {"X": 0.23006665706634521, "Y": 0.3778121769428253}, {"X": 0.1132243275642395, "Y": 0.37416431307792664}]}, "Id": "df8af35f-066c-43e0-84d3-30319af014a8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027638239786028862, "Height": 0.024810055270791054, "Left": 0.32599076628685, "Top": 0.35430312156677246}, "Polygon": [{"X": 0.32677316665649414, "Y": 0.35430312156677246}, {"X": 0.3536290228366852, "Y": 0.35514557361602783}, {"X": 0.352847695350647, "Y": 0.37911316752433777}, {"X": 0.32599076628685, "Y": 0.3782743215560913}]}, "Id": "a2e81c20-8463-451d-ab16-c740e5c4af6f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03969407081604004, "Height": 0.02513890154659748, "Left": 0.40297356247901917, "Top": 0.35680967569351196}, "Polygon": [{"X": 0.4037516415119171, "Y": 0.35680967569351196}, {"X": 0.4426676332950592, "Y": 0.35803043842315674}, {"X": 0.4418911039829254, "Y": 0.3819485902786255}, {"X": 0.40297356247901917, "Y": 0.38073301315307617}]}, "Id": "c8ae1fe3-13f6-4938-9d5a-78f1d8817f40"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06476809084415436, "Height": 0.026079218834638596, "Left": 0.5835183262825012, "Top": 0.3645056188106537}, "Polygon": [{"X": 0.5842950344085693, "Y": 0.3645056188106537}, {"X": 0.6482864022254944, "Y": 0.3665122389793396}, {"X": 0.6475123167037964, "Y": 0.3905848562717438}, {"X": 0.5835183262825012, "Y": 0.3885868489742279}]}, "Id": "dca8b043-be65-4d40-8a06-e6aa141dc50f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07329865545034409, "Height": 0.026687582954764366, "Left": 0.6532099843025208, "Top": 0.3666556775569916}, "Polygon": [{"X": 0.653995156288147, "Y": 0.3666556775569916}, {"X": 0.726508617401123, "Y": 0.36892953515052795}, {"X": 0.7257263660430908, "Y": 0.3933432698249817}, {"X": 0.6532099843025208, "Y": 0.391079306602478}]}, "Id": "5a73abcc-df68-43d2-a026-8158b42cf0a9"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014958512037992477, "Height": 0.024634690955281258, "Left": 0.7327051162719727, "Top": 0.3690207600593567}, "Polygon": [{"X": 0.7334799766540527, "Y": 0.3690207600593567}, {"X": 0.7476636171340942, "Y": 0.36946555972099304}, {"X": 0.7468892931938171, "Y": 0.3936554491519928}, {"X": 0.7327051162719727, "Y": 0.39321261644363403}]}, "Id": "4c55835f-6306-4ab5-8db8-8ca50a5424b3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05118170753121376, "Height": 0.0243005882948637, "Left": 0.8774502873420715, "Top": 0.3616449534893036}, "Polygon": [{"X": 0.8781731128692627, "Y": 0.3616449534893036}, {"X": 0.9286319613456726, "Y": 0.3632306754589081}, {"X": 0.9279110431671143, "Y": 0.38594555854797363}, {"X": 0.8774502873420715, "Y": 0.3843662738800049}]}, "Id": "3897256f-b2ba-4dde-a258-baeeb2923ec5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05099555850028992, "Height": 0.023978594690561295, "Left": 0.9329580664634705, "Top": 0.3639817535877228}, "Polygon": [{"X": 0.9336689710617065, "Y": 0.3639817535877228}, {"X": 0.983953595161438, "Y": 0.3655618131160736}, {"X": 0.9832445383071899, "Y": 0.3879603445529938}, {"X": 0.9329580664634705, "Y": 0.38638660311698914}]}, "Id": "334f507c-3218-4e4a-bba5-b0d918a305c6"}, {"BlockType": "WORD", "Confidence": 84.02682495117188, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05760449171066284, "Height": 0.05161376670002937, "Left": 0.2268626093864441, "Top": 0.4105585515499115}, "Polygon": [{"X": 0.22849825024604797, "Y": 0.4105585515499115}, {"X": 0.28446710109710693, "Y": 0.4122955799102783}, {"X": 0.2828361392021179, "Y": 0.4621722996234894}, {"X": 0.2268626093864441, "Y": 0.4604508578777313}]}, "Id": "8efa4bae-1497-4686-88c8-67185970fd4f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1040", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06970304250717163, "Height": 0.03342719376087189, "Left": 0.8907788395881653, "Top": 0.42256876826286316}, "Polygon": [{"X": 0.89177405834198, "Y": 0.42256876826286316}, {"X": 0.9604818820953369, "Y": 0.42470449209213257}, {"X": 0.9594902992248535, "Y": 0.45599597692489624}, {"X": 0.8907788395881653, "Y": 0.45387229323387146}]}, "Id": "dd639875-a1e7-4bee-8574-c9370a01134e"}, {"BlockType": "WORD", "Confidence": 59.43339538574219, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05564732104539871, "Height": 0.046167951077222824, "Left": 0.4481831192970276, "Top": 0.42416319251060486}, "Polygon": [{"X": 0.4496272802352905, "Y": 0.42416319251060486}, {"X": 0.5038304328918457, "Y": 0.42584335803985596}, {"X": 0.5023903250694275, "Y": 0.4703311324119568}, {"X": 0.4481831192970276, "Y": 0.4686644375324249}]}, "Id": "0ca8a920-2b5b-413c-b954-f8d62966dffa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Parkn", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05719675123691559, "Height": 0.027103153988718987, "Left": 0.04570475593209267, "Top": 0.45253515243530273}, "Polygon": [{"X": 0.04654354974627495, "Y": 0.45253515243530273}, {"X": 0.10290151089429855, "Y": 0.4542691707611084}, {"X": 0.10206511616706848, "Y": 0.47963830828666687}, {"X": 0.04570475593209267, "Y": 0.47791221737861633}]}, "Id": "ae7236cf-d4bb-4c71-bffb-14a38f2fa620"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Manufacturing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14374566078186035, "Height": 0.03725487366318703, "Left": 0.10992428660392761, "Top": 0.4561483561992645}, "Polygon": [{"X": 0.11100832372903824, "Y": 0.4561483561992645}, {"X": 0.25366994738578796, "Y": 0.46053647994995117}, {"X": 0.2525937855243683, "Y": 0.49340322613716125}, {"X": 0.10992428660392761, "Y": 0.4890412390232086}]}, "Id": "e793e481-69ce-443d-97d0-c49b19689d81"}, {"BlockType": "WORD", "Confidence": 91.66035461425781, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01628855988383293, "Height": 0.025888467207551003, "Left": 0.3308030962944031, "Top": 0.4660031199455261}, "Polygon": [{"X": 0.3316322863101959, "Y": 0.4660031199455261}, {"X": 0.3470916450023651, "Y": 0.4664783775806427}, {"X": 0.3462630808353424, "Y": 0.4918915927410126}, {"X": 0.3308030962944031, "Y": 0.4914185404777527}]}, "Id": "92f08899-c78c-49d9-9f84-2abe4d2bf431"}, {"BlockType": "WORD", "Confidence": 79.57369995117188, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015035339631140232, "Height": 0.02525186352431774, "Left": 0.4126451015472412, "Top": 0.46869713068008423}, "Polygon": [{"X": 0.41345176100730896, "Y": 0.46869713068008423}, {"X": 0.42768046259880066, "Y": 0.46913453936576843}, {"X": 0.42687439918518066, "Y": 0.4939489960670471}, {"X": 0.4126451015472412, "Y": 0.4935135543346405}]}, "Id": "0eefb6fd-db57-4b33-b8e6-9534c3c61758"}, {"BlockType": "WORD", "Confidence": 98.64556884765625, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1111287996172905, "Height": 0.024160338565707207, "Left": 0.46177488565444946, "Top": 0.47211411595344543}, "Polygon": [{"X": 0.46244874596595764, "Y": 0.47211411595344543}, {"X": 0.5729036927223206, "Y": 0.4755083918571472}, {"X": 0.5722336769104004, "Y": 0.4962744414806366}, {"X": 0.46177488565444946, "Y": 0.4928929805755615}]}, "Id": "4abbcf1e-190b-4380-86bd-25d0c278a9a7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1055", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07424264401197433, "Height": 0.04109891876578331, "Left": 0.8813396692276001, "Top": 0.510494589805603}, "Polygon": [{"X": 0.8825761675834656, "Y": 0.510494589805603}, {"X": 0.955582320690155, "Y": 0.5127276182174683}, {"X": 0.9543505907058716, "Y": 0.551593542098999}, {"X": 0.8813396692276001, "Y": 0.5493764281272888}]}, "Id": "03322bae-331d-4e5c-bc8a-66e9c0cd791c"}, {"BlockType": "WORD", "Confidence": 71.42219543457031, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06532468646764755, "Height": 0.047232694923877716, "Left": 0.4515901505947113, "Top": 0.620549201965332}, "Polygon": [{"X": 0.4530608654022217, "Y": 0.620549201965332}, {"X": 0.5169148445129395, "Y": 0.6224579811096191}, {"X": 0.5154489874839783, "Y": 0.6677818894386292}, {"X": 0.4515901505947113, "Y": 0.665889322757721}]}, "Id": "4ded61a0-dfb1-4e18-8f6b-9456901b1ad2"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "0830", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07970772683620453, "Height": 0.037620823830366135, "Left": 0.8743544220924377, "Top": 0.6406471729278564}, "Polygon": [{"X": 0.8754768371582031, "Y": 0.6406471729278564}, {"X": 0.9540621042251587, "Y": 0.6429930329322815}, {"X": 0.9529443383216858, "Y": 0.6782680153846741}, {"X": 0.8743544220924377, "Y": 0.6759377717971802}]}, "Id": "0a7ca96e-3738-4ae0-975c-aad8528b1359"}, {"BlockType": "WORD", "Confidence": 89.88619995117188, "Text": "Alumalloy -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1093287542462349, "Height": 0.033881813287734985, "Left": 0.045977212488651276, "Top": 0.658808708190918}, "Polygon": [{"X": 0.04699138551950455, "Y": 0.658808708190918}, {"X": 0.15530596673488617, "Y": 0.6620158553123474}, {"X": 0.1542973667383194, "Y": 0.6926905512809753}, {"X": 0.045977212488651276, "Y": 0.689501941204071}]}, "Id": "376bcc5c-04b8-4dff-9125-62d200c65f5e"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "Avon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05033715069293976, "Height": 0.025274360552430153, "Left": 0.16340602934360504, "Top": 0.6641214489936829}, "Polygon": [{"X": 0.16418872773647308, "Y": 0.6641214489936829}, {"X": 0.2137431800365448, "Y": 0.6655882596969604}, {"X": 0.21296246349811554, "Y": 0.6893958449363708}, {"X": 0.16340602934360504, "Y": 0.6879356503486633}]}, "Id": "88675896-d3cf-470f-ad17-e005a3164b5b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Lake", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04594001919031143, "Height": 0.02567380852997303, "Left": 0.22079934179782867, "Top": 0.664814293384552}, "Polygon": [{"X": 0.221597358584404, "Y": 0.664814293384552}, {"X": 0.2667393684387207, "Y": 0.666150689125061}, {"X": 0.2659431993961334, "Y": 0.6904881000518799}, {"X": 0.22079934179782867, "Y": 0.6891577839851379}]}, "Id": "fa73e31e-f617-483e-9dbf-47c9b0f66509"}, {"BlockType": "WORD", "Confidence": 93.99175262451172, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01586502231657505, "Height": 0.025142852216959, "Left": 0.4088922441005707, "Top": 0.6709004044532776}, "Polygon": [{"X": 0.40969493985176086, "Y": 0.6709004044532776}, {"X": 0.4247572422027588, "Y": 0.6713462471961975}, {"X": 0.42395517230033875, "Y": 0.6960432529449463}, {"X": 0.4088922441005707, "Y": 0.6955994367599487}]}, "Id": "c7cea906-b159-4b5a-91c1-efe0e10477c5"}, {"BlockType": "WORD", "Confidence": 88.13396453857422, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11303995549678802, "Height": 0.02407018095254898, "Left": 0.45501336455345154, "Top": 0.6745582222938538}, "Polygon": [{"X": 0.4556865692138672, "Y": 0.6745582222938538}, {"X": 0.5680533051490784, "Y": 0.6778830289840698}, {"X": 0.5673840045928955, "Y": 0.6986284255981445}, {"X": 0.45501336455345154, "Y": 0.6953166127204895}]}, "Id": "baa4d2d0-07c4-4e62-8f05-dc38e425f30e"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "0840", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08023902028799057, "Height": 0.03638775274157524, "Left": 0.8741713166236877, "Top": 0.7459439039230347}, "Polygon": [{"X": 0.875255286693573, "Y": 0.7459439039230347}, {"X": 0.9544103145599365, "Y": 0.7482596635818481}, {"X": 0.9533308744430542, "Y": 0.782331645488739}, {"X": 0.8741713166236877, "Y": 0.7800310254096985}]}, "Id": "1f1e6129-ade5-44e9-a713-2ea671699b75"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "4d0ff045-50a1-4e20-ab7a-02b21db1f4fc", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "4d0ff045-50a1-4e20-ab7a-02b21db1f4fc", "content-type": "application/x-amz-json-1.1", "content-length": "35355", "date": "Wed, 24 Sep 2025 13:39:56 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "5a2a6f5f-4a21-43fa-ba01-26949592c914", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:39:59 GMT", "content-type": "application/json", "content-length": "1553", "connection": "keep-alive", "x-amzn-requestid": "5a2a6f5f-4a21-43fa-ba01-26949592c914"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. We need to classify. The content looks like an Ingate or Outgate? It shows \"Truck Ready Time\", \"Truck Departure\", \"Driver\", \"Dock Time\", \"Work Order #\", \"Customer\". This likely is a gate log: Ingate or Outgate? It's \"Truck Ready Time\" and \"Truck Departure\" maybe the departure time at gate. This might be a Ingate or Outgate? Let's parse: It shows \"Unit #\", \"Truck Ready Time\", \"Driver\", \"Truck Departure\", \"Departure Time\", \"Dock Time\", \"Work Order #\". It might be an \"ingate\" document because it records arrival. But it also has departure times. The heading \"Truck Ready Time\" and \"Truck Departure\" suggests it's a departure log (Outgate). Usually, an Outgate record would have \"Truck departure\" and \"Departure time\" and \"Dock time\" maybe the times when truck left the dock. Ingate would have arrival times. This looks more like departure/outgate. So doc_type likely \"outgate\". The instructions: \"Record of vehicle/container exiting a facility (gate-out/release).\" This matches. So classify as \"outgate\". Ensure output format.\n\nReturn:\n\n{\n \"documents\": [\n  {\"page_no\": 1, \"doc_type\": \"outgate\"}\n ]\n}\n\nWe must not add extraneous.\n\nLet's output."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2705, "outputTokens": 303, "totalTokens": 3008}, "metrics": {"latencyMs": 1518}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/35bbc50d_8_log_3.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nDATE: 20-Feb\nUNIT #: 92644/283420\nTruck Ready Time: 5:00\nDRIVER: <PERSON>ARRA\nTruck Departure: 5:00\nDEPARTURE TIME: 5am\nPICK-\nCUSTOMER\nUP\nDEL\nWORK ORDER #\nDock Time\nIsk\n1040\n1sk\nParkn Manufacturing\nX\nX ***********\n1055\n1sk\n0830\nAlumalloy - Avon Lake\nX ***********\n0840\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}