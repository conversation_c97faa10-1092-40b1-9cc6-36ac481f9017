2025-09-24 14:30:04,988 - INFO - Logging initialized. Log file: logs/test_classification_20250924_143004.log
2025-09-24 14:30:04,989 - INFO - 📁 Found 11 files to process
2025-09-24 14:30:04,989 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:30:04,989 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-24 14:30:04,989 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-24 14:30:04,989 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-24 14:30:04,989 - INFO - ⬆️ [14:30:04] Uploading: A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:06,528 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/A331OW5F2Q6LZDSTAWR6.pdf -> s3://document-extraction-logistically/temp/b1adc625_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:06,529 - INFO - 🔍 [14:30:06] Starting classification: A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:06,530 - INFO - ⬆️ [14:30:06] Uploading: AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:06,536 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:06,552 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:06,557 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b1adc625_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:06,557 - INFO - Processing PDF from S3...
2025-09-24 14:30:06,558 - INFO - Downloading PDF from S3 to /tmp/tmp67htv_pw/b1adc625_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:07,853 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:30:07,853 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:07,854 - INFO - Splitting PDF b1adc625_A331OW5F2Q6LZDSTAWR6 into 1 pages
2025-09-24 14:30:07,862 - INFO - Split PDF into 1 pages
2025-09-24 14:30:07,863 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:07,863 - INFO - Expected pages: [1]
2025-09-24 14:30:08,647 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/AC4907XUC5FFS63AXXBG.pdf -> s3://document-extraction-logistically/temp/3b6e2d69_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:08,648 - INFO - 🔍 [14:30:08] Starting classification: AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:08,649 - INFO - ⬆️ [14:30:08] Uploading: BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:08,650 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:08,666 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:08,670 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3b6e2d69_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:08,670 - INFO - Processing PDF from S3...
2025-09-24 14:30:08,670 - INFO - Downloading PDF from S3 to /tmp/tmp8cxd7jje/3b6e2d69_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:09,263 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/BPKB6NN7SWJR9KBHTWZD.pdf -> s3://document-extraction-logistically/temp/d7de5d13_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:09,263 - INFO - 🔍 [14:30:09] Starting classification: BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:09,264 - INFO - ⬆️ [14:30:09] Uploading: DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:09,265 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:09,280 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:09,283 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d7de5d13_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:09,284 - INFO - Processing PDF from S3...
2025-09-24 14:30:09,284 - INFO - Downloading PDF from S3 to /tmp/tmpacpvchew/d7de5d13_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:09,877 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/DY354TSLN9XYYH2RWAW5.pdf -> s3://document-extraction-logistically/temp/88f2bf75_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:09,878 - INFO - 🔍 [14:30:09] Starting classification: DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:09,879 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:09,879 - INFO - ⬆️ [14:30:09] Uploading: N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:09,894 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:09,895 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/88f2bf75_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:09,896 - INFO - Processing PDF from S3...
2025-09-24 14:30:09,896 - INFO - Downloading PDF from S3 to /tmp/tmpn2nf28_j/88f2bf75_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:10,452 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/N5ZTF7GH4USQJIPCIXEA.pdf -> s3://document-extraction-logistically/temp/29a65973_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:10,452 - INFO - 🔍 [14:30:10] Starting classification: N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:10,452 - INFO - ⬆️ [14:30:10] Uploading: NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:10,455 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:10,465 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:10,468 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/29a65973_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:10,468 - INFO - Processing PDF from S3...
2025-09-24 14:30:10,468 - INFO - Downloading PDF from S3 to /tmp/tmptgjaxy1m/29a65973_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:10,925 - INFO - Page 1: Extracted 274 characters, 22 lines from b1adc625_A331OW5F2Q6LZDSTAWR6_dbbd14f7_page_001.pdf
2025-09-24 14:30:10,925 - INFO - Successfully processed page 1
2025-09-24 14:30:10,926 - INFO - Combined 1 pages into final text
2025-09-24 14:30:10,926 - INFO - Text validation for b1adc625_A331OW5F2Q6LZDSTAWR6: 291 characters, 1 pages
2025-09-24 14:30:10,926 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:10,927 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:11,067 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/NUYKWFH99COJT60C78IZ.pdf -> s3://document-extraction-logistically/temp/2044c970_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:11,067 - INFO - 🔍 [14:30:11] Starting classification: NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:11,067 - INFO - ⬆️ [14:30:11] Uploading: OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:11,071 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:11,082 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:11,086 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:30:11,087 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2044c970_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:11,087 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:11,087 - INFO - Processing PDF from S3...
2025-09-24 14:30:11,087 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 14:30:11,088 - INFO - Downloading PDF from S3 to /tmp/tmprtrjtqw1/2044c970_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:11,088 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:11,097 - INFO - Splitting PDF 88f2bf75_DY354TSLN9XYYH2RWAW5 into 1 pages
2025-09-24 14:30:11,104 - INFO - Splitting PDF 3b6e2d69_AC4907XUC5FFS63AXXBG into 1 pages
2025-09-24 14:30:11,105 - INFO - Split PDF into 1 pages
2025-09-24 14:30:11,105 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:11,105 - INFO - Expected pages: [1]
2025-09-24 14:30:11,117 - INFO - Split PDF into 1 pages
2025-09-24 14:30:11,117 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:11,117 - INFO - Expected pages: [1]
2025-09-24 14:30:11,125 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:30:11,125 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:11,126 - INFO - Splitting PDF d7de5d13_BPKB6NN7SWJR9KBHTWZD into 1 pages
2025-09-24 14:30:11,168 - INFO - Split PDF into 1 pages
2025-09-24 14:30:11,168 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:11,168 - INFO - Expected pages: [1]
2025-09-24 14:30:11,650 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/OBUNADUYEXKR65OJ917C.pdf -> s3://document-extraction-logistically/temp/6f963017_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:11,650 - INFO - 🔍 [14:30:11] Starting classification: OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:11,650 - INFO - ⬆️ [14:30:11] Uploading: S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:11,652 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:11,666 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:11,671 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6f963017_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:11,671 - INFO - Processing PDF from S3...
2025-09-24 14:30:11,671 - INFO - Downloading PDF from S3 to /tmp/tmpcqausv0e/6f963017_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:11,695 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:30:11,696 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:11,698 - INFO - Splitting PDF 29a65973_N5ZTF7GH4USQJIPCIXEA into 1 pages
2025-09-24 14:30:11,707 - INFO - Split PDF into 1 pages
2025-09-24 14:30:11,707 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:11,707 - INFO - Expected pages: [1]
2025-09-24 14:30:12,217 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/S917SYX1NI0B6KSG1VL8.pdf -> s3://document-extraction-logistically/temp/cc268be0_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:12,218 - INFO - 🔍 [14:30:12] Starting classification: S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:12,218 - INFO - ⬆️ [14:30:12] Uploading: VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:12,219 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:12,231 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:12,233 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cc268be0_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:12,233 - INFO - Processing PDF from S3...
2025-09-24 14:30:12,233 - INFO - Downloading PDF from S3 to /tmp/tmptegabteg/cc268be0_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:12,680 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5583dea9-cf67-4828-83d4-df75faaf766e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:12 GMT', 'content-type': 'application/json', 'content-length': '646', 'connection': 'keep-alive', 'x-amzn-requestid': '5583dea9-cf67-4828-83d4-df75faaf766e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. It shows a purchase order. Keywords: "Purchase Order". So doc_type = "po". No continuation. So output an array with one object: page_no 1, doc_type "po". Use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_cn6WV4FeQjGhoEQ37yabxA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2567, 'outputTokens': 77, 'totalTokens': 2644}, 'metrics': {'latencyMs': 525}}
2025-09-24 14:30:12,680 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b1adc625_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:12,824 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/VIASDRA5CL365YHEEWOV.pdf -> s3://document-extraction-logistically/temp/dd4cff76_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:12,824 - INFO - 🔍 [14:30:12] Starting classification: VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:12,825 - INFO - ⬆️ [14:30:12] Uploading: XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:12,827 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:12,844 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:12,847 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dd4cff76_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:12,848 - INFO - Processing PDF from S3...
2025-09-24 14:30:12,848 - INFO - Downloading PDF from S3 to /tmp/tmp8jvfqzh8/dd4cff76_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:13,089 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:30:13,089 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:13,090 - INFO - Splitting PDF 2044c970_NUYKWFH99COJT60C78IZ into 1 pages
2025-09-24 14:30:13,095 - INFO - Split PDF into 1 pages
2025-09-24 14:30:13,095 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:13,095 - INFO - Expected pages: [1]
2025-09-24 14:30:13,380 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/XIYO2PDG2ILYXT57MES6.pdf -> s3://document-extraction-logistically/temp/9146558a_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:13,381 - INFO - 🔍 [14:30:13] Starting classification: XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:13,382 - INFO - ⬆️ [14:30:13] Uploading: ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:13,382 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:13,402 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:13,405 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9146558a_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:13,405 - INFO - Processing PDF from S3...
2025-09-24 14:30:13,405 - INFO - Downloading PDF from S3 to /tmp/tmpvnlhdb0l/9146558a_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:13,962 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:30:13,962 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/ZFBHUC7YFRE4Z58IJGC5.PDF -> s3://document-extraction-logistically/temp/8b5d4099_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:13,963 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:13,963 - INFO - 🔍 [14:30:13] Starting classification: ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:13,968 - INFO - Splitting PDF cc268be0_S917SYX1NI0B6KSG1VL8 into 1 pages
2025-09-24 14:30:13,969 - INFO - Initializing TextractProcessor...
2025-09-24 14:30:13,993 - INFO - Initializing BedrockProcessor...
2025-09-24 14:30:14,003 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8b5d4099_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:14,004 - INFO - Processing PDF from S3...
2025-09-24 14:30:14,011 - INFO - Downloading PDF from S3 to /tmp/tmp319egm5y/8b5d4099_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:14,018 - INFO - Split PDF into 1 pages
2025-09-24 14:30:14,018 - INFO - 

A331OW5F2Q6LZDSTAWR6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:14,018 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:14,019 - INFO - 

✓ Saved result: output/run1_A331OW5F2Q6LZDSTAWR6.json
2025-09-24 14:30:14,019 - INFO - Expected pages: [1]
2025-09-24 14:30:14,302 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b1adc625_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:14,546 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:30:14,547 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:14,548 - INFO - Splitting PDF dd4cff76_VIASDRA5CL365YHEEWOV into 1 pages
2025-09-24 14:30:14,553 - INFO - Split PDF into 1 pages
2025-09-24 14:30:14,553 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:14,553 - INFO - Expected pages: [1]
2025-09-24 14:30:14,601 - INFO - Page 1: Extracted 272 characters, 23 lines from 29a65973_N5ZTF7GH4USQJIPCIXEA_89397457_page_001.pdf
2025-09-24 14:30:14,601 - INFO - Successfully processed page 1
2025-09-24 14:30:14,602 - INFO - Combined 1 pages into final text
2025-09-24 14:30:14,602 - INFO - Text validation for 29a65973_N5ZTF7GH4USQJIPCIXEA: 289 characters, 1 pages
2025-09-24 14:30:14,602 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:14,602 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:14,844 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:30:14,844 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:14,845 - INFO - Splitting PDF 9146558a_XIYO2PDG2ILYXT57MES6 into 1 pages
2025-09-24 14:30:14,850 - INFO - Split PDF into 1 pages
2025-09-24 14:30:14,850 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:14,850 - INFO - Expected pages: [1]
2025-09-24 14:30:15,378 - INFO - Page 1: Extracted 1915 characters, 67 lines from 88f2bf75_DY354TSLN9XYYH2RWAW5_664c7da8_page_001.pdf
2025-09-24 14:30:15,378 - INFO - Successfully processed page 1
2025-09-24 14:30:15,378 - INFO - Combined 1 pages into final text
2025-09-24 14:30:15,379 - INFO - Text validation for 88f2bf75_DY354TSLN9XYYH2RWAW5: 1932 characters, 1 pages
2025-09-24 14:30:15,379 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:15,379 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:15,452 - INFO - Page 1: Extracted 760 characters, 52 lines from d7de5d13_BPKB6NN7SWJR9KBHTWZD_f179acb7_page_001.pdf
2025-09-24 14:30:15,453 - INFO - Successfully processed page 1
2025-09-24 14:30:15,454 - INFO - Combined 1 pages into final text
2025-09-24 14:30:15,454 - INFO - Text validation for d7de5d13_BPKB6NN7SWJR9KBHTWZD: 777 characters, 1 pages
2025-09-24 14:30:15,454 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:15,454 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:16,126 - INFO - Page 1: Extracted 826 characters, 39 lines from 3b6e2d69_AC4907XUC5FFS63AXXBG_ff6e8118_page_001.pdf
2025-09-24 14:30:16,126 - INFO - Successfully processed page 1
2025-09-24 14:30:16,126 - INFO - Combined 1 pages into final text
2025-09-24 14:30:16,126 - INFO - Text validation for 3b6e2d69_AC4907XUC5FFS63AXXBG: 843 characters, 1 pages
2025-09-24 14:30:16,127 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:16,127 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:16,169 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:30:16,170 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:16,172 - INFO - Splitting PDF 6f963017_OBUNADUYEXKR65OJ917C into 1 pages
2025-09-24 14:30:16,179 - INFO - Split PDF into 1 pages
2025-09-24 14:30:16,179 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:16,180 - INFO - Expected pages: [1]
2025-09-24 14:30:16,306 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:30:16,306 - INFO - Splitting PDF into individual pages...
2025-09-24 14:30:16,307 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '266a887f-3a34-4d18-aeac-084f6ee3f67f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:16 GMT', 'content-type': 'application/json', 'content-length': '639', 'connection': 'keep-alive', 'x-amzn-requestid': '266a887f-3a34-4d18-aeac-084f6ee3f67f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have only page1. It\'s a purchase order. Keywords: "Purchase Order". So doc_type is "po". No continuation. So output array with one object: {page_no:1, doc_type:"po"}. Use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_o3W88N1FRWS-3GKVdpu0lg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2570, 'outputTokens': 78, 'totalTokens': 2648}, 'metrics': {'latencyMs': 552}}
2025-09-24 14:30:16,307 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/29a65973_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:16,309 - INFO - Splitting PDF 8b5d4099_ZFBHUC7YFRE4Z58IJGC5 into 1 pages
2025-09-24 14:30:16,317 - INFO - 

N5ZTF7GH4USQJIPCIXEA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:16,318 - INFO - 

✓ Saved result: output/run1_N5ZTF7GH4USQJIPCIXEA.json
2025-09-24 14:30:16,321 - INFO - Split PDF into 1 pages
2025-09-24 14:30:16,321 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:30:16,321 - INFO - Expected pages: [1]
2025-09-24 14:30:16,598 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/29a65973_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:17,051 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ca626b15-ef86-4f7f-a745-e2193e7a0dad', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:16 GMT', 'content-type': 'application/json', 'content-length': '523', 'connection': 'keep-alive', 'x-amzn-requestid': 'ca626b15-ef86-4f7f-a745-e2193e7a0dad'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. It\'s a purchase order. Keywords: "Framework Purchase Order", "PURCHASE ORDER NUMBER". So doc_type: po. Output via tool. Return array with single object: page_no:1, doc_type: "po".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2980, 'outputTokens': 70, 'totalTokens': 3050}, 'metrics': {'latencyMs': 533}}
2025-09-24 14:30:17,052 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/88f2bf75_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:17,097 - INFO - 

DY354TSLN9XYYH2RWAW5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:17,097 - INFO - 

✓ Saved result: output/run1_DY354TSLN9XYYH2RWAW5.json
2025-09-24 14:30:17,100 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1f1ec19c-07fa-474f-97a8-bad373ba7df4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:17 GMT', 'content-type': 'application/json', 'content-length': '606', 'connection': 'keep-alive', 'x-amzn-requestid': '1f1ec19c-07fa-474f-97a8-bad373ba7df4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to output classification via tool classify_logistics_doc_type. Input has one page. Need to return array of objects with page_no and doc_type.\n\nWe see "Purchase Order" header. So doc_type = "po". Also it\'s single page. So output: {page_no:1, doc_type:"po"}\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2732, 'outputTokens': 87, 'totalTokens': 2819}, 'metrics': {'latencyMs': 527}}
2025-09-24 14:30:17,100 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d7de5d13_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:17,372 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/88f2bf75_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:17,392 - INFO - 

BPKB6NN7SWJR9KBHTWZD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:17,392 - INFO - 

✓ Saved result: output/run1_BPKB6NN7SWJR9KBHTWZD.json
2025-09-24 14:30:17,671 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d7de5d13_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:17,795 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'da7166f0-737f-4610-99c7-0833ae6dec3f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:17 GMT', 'content-type': 'application/json', 'content-length': '556', 'connection': 'keep-alive', 'x-amzn-requestid': 'da7166f0-737f-4610-99c7-0833ae6dec3f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. We see "Purchase Order" header. Keywords: Purchase Order, PO#, etc. So doc_type = "po". It\'s a single page, not continuation. So output one object: page_no:1 doc_type:"po". Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2725, 'outputTokens': 79, 'totalTokens': 2804}, 'metrics': {'latencyMs': 566}}
2025-09-24 14:30:17,796 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3b6e2d69_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:17,818 - INFO - 

AC4907XUC5FFS63AXXBG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:17,818 - INFO - 

✓ Saved result: output/run1_AC4907XUC5FFS63AXXBG.json
2025-09-24 14:30:18,105 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3b6e2d69_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:18,325 - INFO - Page 1: Extracted 1223 characters, 85 lines from 2044c970_NUYKWFH99COJT60C78IZ_e1ec15a7_page_001.pdf
2025-09-24 14:30:18,325 - INFO - Successfully processed page 1
2025-09-24 14:30:18,326 - INFO - Combined 1 pages into final text
2025-09-24 14:30:18,326 - INFO - Text validation for 2044c970_NUYKWFH99COJT60C78IZ: 1240 characters, 1 pages
2025-09-24 14:30:18,326 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:18,326 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:18,493 - INFO - Page 1: Extracted 1031 characters, 59 lines from cc268be0_S917SYX1NI0B6KSG1VL8_82012f75_page_001.pdf
2025-09-24 14:30:18,493 - INFO - Successfully processed page 1
2025-09-24 14:30:18,494 - INFO - Combined 1 pages into final text
2025-09-24 14:30:18,494 - INFO - Text validation for cc268be0_S917SYX1NI0B6KSG1VL8: 1048 characters, 1 pages
2025-09-24 14:30:18,495 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:18,495 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:18,947 - INFO - Page 1: Extracted 746 characters, 72 lines from dd4cff76_VIASDRA5CL365YHEEWOV_c298269c_page_001.pdf
2025-09-24 14:30:18,947 - INFO - Successfully processed page 1
2025-09-24 14:30:18,948 - INFO - Combined 1 pages into final text
2025-09-24 14:30:18,948 - INFO - Text validation for dd4cff76_VIASDRA5CL365YHEEWOV: 763 characters, 1 pages
2025-09-24 14:30:18,948 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:18,948 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:19,377 - INFO - Page 1: Extracted 613 characters, 46 lines from 9146558a_XIYO2PDG2ILYXT57MES6_b11a4377_page_001.pdf
2025-09-24 14:30:19,378 - INFO - Successfully processed page 1
2025-09-24 14:30:19,378 - INFO - Combined 1 pages into final text
2025-09-24 14:30:19,378 - INFO - Text validation for 9146558a_XIYO2PDG2ILYXT57MES6: 630 characters, 1 pages
2025-09-24 14:30:19,378 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:19,378 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:20,060 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b21ee2c1-8429-4e96-b9b3-36e5f811013d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:19 GMT', 'content-type': 'application/json', 'content-length': '530', 'connection': 'keep-alive', 'x-amzn-requestid': 'b21ee2c1-8429-4e96-b9b3-36e5f811013d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page, page1. It\'s a purchase order. Keywords: "PURCHASE ORDER". So doc_type = po. Must output array with object: page_no 1, doc_type "po". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2902, 'outputTokens': 73, 'totalTokens': 2975}, 'metrics': {'latencyMs': 544}}
2025-09-24 14:30:20,060 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2044c970_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:20,095 - INFO - 

NUYKWFH99COJT60C78IZ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:20,095 - INFO - 

✓ Saved result: output/run1_NUYKWFH99COJT60C78IZ.json
2025-09-24 14:30:20,172 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f58e7cee-5724-4684-8ffd-4f06bdc72ab8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:20 GMT', 'content-type': 'application/json', 'content-length': '459', 'connection': 'keep-alive', 'x-amzn-requestid': 'f58e7cee-5724-4684-8ffd-4f06bdc72ab8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 present. It is a purchase order. Keywords: Purchase Order, PO#, etc. So doc_type = "po". Output via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2800, 'outputTokens': 55, 'totalTokens': 2855}, 'metrics': {'latencyMs': 468}}
2025-09-24 14:30:20,173 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cc268be0_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:20,191 - INFO - Page 1: Extracted 664 characters, 59 lines from 8b5d4099_ZFBHUC7YFRE4Z58IJGC5_375787e9_page_001.pdf
2025-09-24 14:30:20,191 - INFO - Successfully processed page 1
2025-09-24 14:30:20,191 - INFO - Combined 1 pages into final text
2025-09-24 14:30:20,191 - INFO - Text validation for 8b5d4099_ZFBHUC7YFRE4Z58IJGC5: 681 characters, 1 pages
2025-09-24 14:30:20,192 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:20,192 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:20,374 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2044c970_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:20,394 - INFO - 

S917SYX1NI0B6KSG1VL8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:20,394 - INFO - 

✓ Saved result: output/run1_S917SYX1NI0B6KSG1VL8.json
2025-09-24 14:30:20,660 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fa37cb6a-c98c-493c-a5b8-c540ac0f8619', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:20 GMT', 'content-type': 'application/json', 'content-length': '584', 'connection': 'keep-alive', 'x-amzn-requestid': 'fa37cb6a-c98c-493c-a5b8-c540ac0f8619'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. The content is a purchase order. So doc_type: "po". Output via function call.\n\nWe must return an array of objects for each page. Only one page. The format: documents: [{page_no:1, doc_type:"po"}]. So call function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2733, 'outputTokens': 84, 'totalTokens': 2817}, 'metrics': {'latencyMs': 545}}
2025-09-24 14:30:20,661 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dd4cff76_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:20,730 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cc268be0_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:20,749 - INFO - 

VIASDRA5CL365YHEEWOV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:20,749 - INFO - 

✓ Saved result: output/run1_VIASDRA5CL365YHEEWOV.json
2025-09-24 14:30:20,892 - INFO - Page 1: Extracted 562 characters, 41 lines from 6f963017_OBUNADUYEXKR65OJ917C_08b1ea1d_page_001.pdf
2025-09-24 14:30:20,892 - INFO - Successfully processed page 1
2025-09-24 14:30:20,893 - INFO - Combined 1 pages into final text
2025-09-24 14:30:20,893 - INFO - Text validation for 6f963017_OBUNADUYEXKR65OJ917C: 579 characters, 1 pages
2025-09-24 14:30:20,893 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:30:20,893 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:30:21,023 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dd4cff76_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:21,385 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a9bdd210-3045-48cb-b969-70419bb72ed9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:21 GMT', 'content-type': 'application/json', 'content-length': '861', 'connection': 'keep-alive', 'x-amzn-requestid': 'a9bdd210-3045-48cb-b969-70419bb72ed9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'The user provided a page content: seems a Purchase Order page. The page shows "Purchase Order" header, vendor info, etc. The page is the entire document? It says "1 of 1" at bottom. So likely the document is a PO. According to the instructions: we must return exactly one object with page_no and doc_type. The doc_type should be "po". Let\'s confirm: The header is "Purchase Order". No other pages. So classification: po.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing one object.\n\nLet\'s produce.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2688, 'outputTokens': 136, 'totalTokens': 2824}, 'metrics': {'latencyMs': 863}}
2025-09-24 14:30:21,386 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9146558a_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:21,403 - INFO - 

XIYO2PDG2ILYXT57MES6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:21,403 - INFO - 

✓ Saved result: output/run1_XIYO2PDG2ILYXT57MES6.json
2025-09-24 14:30:21,694 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9146558a_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:21,911 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c407377e-7bac-4eae-9deb-1a79693eaa1e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:21 GMT', 'content-type': 'application/json', 'content-length': '585', 'connection': 'keep-alive', 'x-amzn-requestid': 'c407377e-7bac-4eae-9deb-1a79693eaa1e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page page1. It\'s a purchase order. The doc type should be "po". We must output using the function classify_logistics_doc_type with documents array containing objects: {page_no: 1, doc_type: "po"}.\n\nCheck continuation? No.\n\nThus produce function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2716, 'outputTokens': 79, 'totalTokens': 2795}, 'metrics': {'latencyMs': 586}}
2025-09-24 14:30:21,911 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8b5d4099_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:21,921 - INFO - 

ZFBHUC7YFRE4Z58IJGC5.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:21,921 - INFO - 

✓ Saved result: output/run1_ZFBHUC7YFRE4Z58IJGC5.json
2025-09-24 14:30:22,198 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8b5d4099_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:22,700 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8abb7213-1b77-4b02-913c-9cb16dd7e78b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:00:22 GMT', 'content-type': 'application/json', 'content-length': '669', 'connection': 'keep-alive', 'x-amzn-requestid': '8abb7213-1b77-4b02-913c-9cb16dd7e78b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this page. It\'s a Purchase Order. So doc_type "po". Need to output using classify_logistics_doc_type tool. For page 1, page_no 1. Provide array with one object? The function expects documents: array of objects. For each page, produce one object. So we call tool with documents: [ {page_no:1, doc_type:"po"} ].\n\nLet\'s ensure correct.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2674, 'outputTokens': 104, 'totalTokens': 2778}, 'metrics': {'latencyMs': 635}}
2025-09-24 14:30:22,700 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6f963017_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:22,718 - INFO - 

OBUNADUYEXKR65OJ917C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:30:22,718 - INFO - 

✓ Saved result: output/run1_OBUNADUYEXKR65OJ917C.json
2025-09-24 14:30:23,084 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6f963017_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:23,085 - INFO - 
📊 Processing Summary:
2025-09-24 14:30:23,085 - INFO -    Total files: 11
2025-09-24 14:30:23,085 - INFO -    Successful: 11
2025-09-24 14:30:23,085 - INFO -    Failed: 0
2025-09-24 14:30:23,085 - INFO -    Duration: 18.10 seconds
2025-09-24 14:30:23,085 - INFO -    Output directory: output
2025-09-24 14:30:23,085 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:30:23,085 - INFO -    📄 A331OW5F2Q6LZDSTAWR6.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 AC4907XUC5FFS63AXXBG.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 BPKB6NN7SWJR9KBHTWZD.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 DY354TSLN9XYYH2RWAW5.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 N5ZTF7GH4USQJIPCIXEA.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 NUYKWFH99COJT60C78IZ.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 OBUNADUYEXKR65OJ917C.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 S917SYX1NI0B6KSG1VL8.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,085 - INFO -    📄 VIASDRA5CL365YHEEWOV.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,086 - INFO -    📄 XIYO2PDG2ILYXT57MES6.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,086 - INFO -    📄 ZFBHUC7YFRE4Z58IJGC5.PDF: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:30:23,086 - INFO - 
============================================================================================================================================
2025-09-24 14:30:23,086 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:30:23,086 - INFO - ============================================================================================================================================
2025-09-24 14:30:23,086 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:30:23,086 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:30:23,086 - INFO - A331OW5F2Q6LZDSTAWR6.pdf                           1      po                   run1_A331OW5F2Q6LZDSTAWR6.json                    
2025-09-24 14:30:23,086 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:30:23,086 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A331OW5F2Q6LZDSTAWR6.json
2025-09-24 14:30:23,086 - INFO - 
2025-09-24 14:30:23,086 - INFO - AC4907XUC5FFS63AXXBG.pdf                           1      po                   run1_AC4907XUC5FFS63AXXBG.json                    
2025-09-24 14:30:23,086 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:30:23,086 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AC4907XUC5FFS63AXXBG.json
2025-09-24 14:30:23,086 - INFO - 
2025-09-24 14:30:23,086 - INFO - BPKB6NN7SWJR9KBHTWZD.pdf                           1      po                   run1_BPKB6NN7SWJR9KBHTWZD.json                    
2025-09-24 14:30:23,086 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:30:23,086 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BPKB6NN7SWJR9KBHTWZD.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - DY354TSLN9XYYH2RWAW5.pdf                           1      po                   run1_DY354TSLN9XYYH2RWAW5.json                    
2025-09-24 14:30:23,087 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:30:23,087 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY354TSLN9XYYH2RWAW5.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - N5ZTF7GH4USQJIPCIXEA.pdf                           1      po                   run1_N5ZTF7GH4USQJIPCIXEA.json                    
2025-09-24 14:30:23,087 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:30:23,087 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N5ZTF7GH4USQJIPCIXEA.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - NUYKWFH99COJT60C78IZ.pdf                           1      po                   run1_NUYKWFH99COJT60C78IZ.json                    
2025-09-24 14:30:23,087 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:30:23,087 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NUYKWFH99COJT60C78IZ.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - OBUNADUYEXKR65OJ917C.pdf                           1      po                   run1_OBUNADUYEXKR65OJ917C.json                    
2025-09-24 14:30:23,087 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:30:23,087 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OBUNADUYEXKR65OJ917C.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - S917SYX1NI0B6KSG1VL8.pdf                           1      po                   run1_S917SYX1NI0B6KSG1VL8.json                    
2025-09-24 14:30:23,087 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:30:23,087 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_S917SYX1NI0B6KSG1VL8.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - VIASDRA5CL365YHEEWOV.pdf                           1      po                   run1_VIASDRA5CL365YHEEWOV.json                    
2025-09-24 14:30:23,087 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:30:23,087 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VIASDRA5CL365YHEEWOV.json
2025-09-24 14:30:23,087 - INFO - 
2025-09-24 14:30:23,087 - INFO - XIYO2PDG2ILYXT57MES6.pdf                           1      po                   run1_XIYO2PDG2ILYXT57MES6.json                    
2025-09-24 14:30:23,088 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:30:23,088 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XIYO2PDG2ILYXT57MES6.json
2025-09-24 14:30:23,088 - INFO - 
2025-09-24 14:30:23,088 - INFO - ZFBHUC7YFRE4Z58IJGC5.PDF                           1      po                   run1_ZFBHUC7YFRE4Z58IJGC5.json                    
2025-09-24 14:30:23,088 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:30:23,088 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZFBHUC7YFRE4Z58IJGC5.json
2025-09-24 14:30:23,088 - INFO - 
2025-09-24 14:30:23,088 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:30:23,088 - INFO - Total entries: 11
2025-09-24 14:30:23,088 - INFO - ============================================================================================================================================
2025-09-24 14:30:23,088 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:30:23,088 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:30:23,088 - INFO -   1. A331OW5F2Q6LZDSTAWR6.pdf            Page 1   → po              | run1_A331OW5F2Q6LZDSTAWR6.json
2025-09-24 14:30:23,088 - INFO -   2. AC4907XUC5FFS63AXXBG.pdf            Page 1   → po              | run1_AC4907XUC5FFS63AXXBG.json
2025-09-24 14:30:23,088 - INFO -   3. BPKB6NN7SWJR9KBHTWZD.pdf            Page 1   → po              | run1_BPKB6NN7SWJR9KBHTWZD.json
2025-09-24 14:30:23,088 - INFO -   4. DY354TSLN9XYYH2RWAW5.pdf            Page 1   → po              | run1_DY354TSLN9XYYH2RWAW5.json
2025-09-24 14:30:23,088 - INFO -   5. N5ZTF7GH4USQJIPCIXEA.pdf            Page 1   → po              | run1_N5ZTF7GH4USQJIPCIXEA.json
2025-09-24 14:30:23,088 - INFO -   6. NUYKWFH99COJT60C78IZ.pdf            Page 1   → po              | run1_NUYKWFH99COJT60C78IZ.json
2025-09-24 14:30:23,088 - INFO -   7. OBUNADUYEXKR65OJ917C.pdf            Page 1   → po              | run1_OBUNADUYEXKR65OJ917C.json
2025-09-24 14:30:23,088 - INFO -   8. S917SYX1NI0B6KSG1VL8.pdf            Page 1   → po              | run1_S917SYX1NI0B6KSG1VL8.json
2025-09-24 14:30:23,088 - INFO -   9. VIASDRA5CL365YHEEWOV.pdf            Page 1   → po              | run1_VIASDRA5CL365YHEEWOV.json
2025-09-24 14:30:23,088 - INFO -  10. XIYO2PDG2ILYXT57MES6.pdf            Page 1   → po              | run1_XIYO2PDG2ILYXT57MES6.json
2025-09-24 14:30:23,088 - INFO -  11. ZFBHUC7YFRE4Z58IJGC5.PDF            Page 1   → po              | run1_ZFBHUC7YFRE4Z58IJGC5.json
2025-09-24 14:30:23,088 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:30:23,088 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 18.09611, 'processed_files': [{'filename': 'A331OW5F2Q6LZDSTAWR6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/A331OW5F2Q6LZDSTAWR6.pdf'}, {'filename': 'AC4907XUC5FFS63AXXBG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/AC4907XUC5FFS63AXXBG.pdf'}, {'filename': 'BPKB6NN7SWJR9KBHTWZD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/BPKB6NN7SWJR9KBHTWZD.pdf'}, {'filename': 'DY354TSLN9XYYH2RWAW5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/DY354TSLN9XYYH2RWAW5.pdf'}, {'filename': 'N5ZTF7GH4USQJIPCIXEA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/N5ZTF7GH4USQJIPCIXEA.pdf'}, {'filename': 'NUYKWFH99COJT60C78IZ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/NUYKWFH99COJT60C78IZ.pdf'}, {'filename': 'OBUNADUYEXKR65OJ917C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/OBUNADUYEXKR65OJ917C.pdf'}, {'filename': 'S917SYX1NI0B6KSG1VL8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/S917SYX1NI0B6KSG1VL8.pdf'}, {'filename': 'VIASDRA5CL365YHEEWOV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/VIASDRA5CL365YHEEWOV.pdf'}, {'filename': 'XIYO2PDG2ILYXT57MES6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/XIYO2PDG2ILYXT57MES6.pdf'}, {'filename': 'ZFBHUC7YFRE4Z58IJGC5.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/ZFBHUC7YFRE4Z58IJGC5.PDF'}]}
