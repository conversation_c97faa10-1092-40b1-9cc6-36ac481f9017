2025-09-24 17:54:09,304 - INFO - Logging initialized. Log file: logs/test_classification_20250924_175409.log
2025-09-24 17:54:09,304 - INFO - 📁 Found 7 files to process
2025-09-24 17:54:09,304 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:54:09,304 - INFO - 🚀 Processing 7 files in FORCED PARALLEL MODE...
2025-09-24 17:54:09,304 - INFO - 🚀 Creating 7 parallel tasks...
2025-09-24 17:54:09,304 - INFO - 🚀 All 7 tasks created - executing in parallel...
2025-09-24 17:54:09,304 - INFO - ⬆️ [17:54:09] Uploading: A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:12,733 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf -> s3://document-extraction-logistically/temp/f40b6de6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:12,733 - INFO - 🔍 [17:54:12] Starting classification: A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:12,734 - INFO - ⬆️ [17:54:12] Uploading: DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:12,735 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:12,759 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:12,766 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f40b6de6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:12,766 - INFO - Processing PDF from S3...
2025-09-24 17:54:12,766 - INFO - Downloading PDF from S3 to /tmp/tmpruvwn6u3/f40b6de6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:14,280 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf -> s3://document-extraction-logistically/temp/e713400a_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:14,280 - INFO - 🔍 [17:54:14] Starting classification: DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:14,281 - INFO - ⬆️ [17:54:14] Uploading: EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:14,283 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:14,301 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:14,306 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e713400a_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:14,307 - INFO - Processing PDF from S3...
2025-09-24 17:54:14,307 - INFO - Downloading PDF from S3 to /tmp/tmpv_ux4kpi/e713400a_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:15,061 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf -> s3://document-extraction-logistically/temp/e3369bdf_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:15,062 - INFO - 🔍 [17:54:15] Starting classification: EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:15,063 - INFO - ⬆️ [17:54:15] Uploading: FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:15,064 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:15,088 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:15,095 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e3369bdf_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:15,095 - INFO - Processing PDF from S3...
2025-09-24 17:54:15,096 - INFO - Downloading PDF from S3 to /tmp/tmp99yvm1lp/e3369bdf_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:15,096 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:54:15,097 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:15,105 - INFO - Splitting PDF f40b6de6_A7CS2V2FOYJ4C84TQCE2 into 3 pages
2025-09-24 17:54:15,123 - INFO - Split PDF into 3 pages
2025-09-24 17:54:15,123 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:15,123 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:54:16,035 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf -> s3://document-extraction-logistically/temp/62558527_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:16,036 - INFO - 🔍 [17:54:16] Starting classification: FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:16,037 - INFO - ⬆️ [17:54:16] Uploading: I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:16,038 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:16,059 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:16,063 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/62558527_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:16,063 - INFO - Processing PDF from S3...
2025-09-24 17:54:16,064 - INFO - Downloading PDF from S3 to /tmp/tmpvbgw5_pj/62558527_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:16,497 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:54:16,498 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:16,499 - INFO - Splitting PDF e713400a_DZSJ7XNECMHN04RDNRIA into 2 pages
2025-09-24 17:54:16,516 - INFO - Split PDF into 2 pages
2025-09-24 17:54:16,516 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:16,516 - INFO - Expected pages: [1, 2]
2025-09-24 17:54:16,734 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf -> s3://document-extraction-logistically/temp/7a9d1c32_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:16,734 - INFO - 🔍 [17:54:16] Starting classification: I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:16,735 - INFO - ⬆️ [17:54:16] Uploading: YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:16,738 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:16,758 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:16,762 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7a9d1c32_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:16,762 - INFO - Processing PDF from S3...
2025-09-24 17:54:16,763 - INFO - Downloading PDF from S3 to /tmp/tmpx180ife9/7a9d1c32_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:16,922 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:54:16,922 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:16,923 - INFO - Splitting PDF e3369bdf_EOGW8CVHOLX2BALX5AA5 into 3 pages
2025-09-24 17:54:16,935 - INFO - Split PDF into 3 pages
2025-09-24 17:54:16,936 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:16,936 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:54:17,418 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf -> s3://document-extraction-logistically/temp/e2a15965_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:17,418 - INFO - 🔍 [17:54:17] Starting classification: YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:17,419 - INFO - ⬆️ [17:54:17] Uploading: ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:17,421 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:17,492 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:17,497 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e2a15965_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:17,497 - INFO - Processing PDF from S3...
2025-09-24 17:54:17,497 - INFO - Downloading PDF from S3 to /tmp/tmp9cap3sm4/e2a15965_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:18,213 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:54:18,213 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:18,216 - INFO - Splitting PDF 62558527_FSH16W3D0EIW0C8PSEF9 into 2 pages
2025-09-24 17:54:18,232 - INFO - Split PDF into 2 pages
2025-09-24 17:54:18,232 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:18,232 - INFO - Expected pages: [1, 2]
2025-09-24 17:54:18,265 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:54:18,265 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:18,267 - INFO - Splitting PDF 7a9d1c32_I3XU9KPI7B1QVKBAHOYB into 2 pages
2025-09-24 17:54:18,277 - INFO - Split PDF into 2 pages
2025-09-24 17:54:18,277 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:18,277 - INFO - Expected pages: [1, 2]
2025-09-24 17:54:18,313 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf -> s3://document-extraction-logistically/temp/33e5ef2b_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:18,313 - INFO - 🔍 [17:54:18] Starting classification: ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:18,314 - INFO - Initializing TextractProcessor...
2025-09-24 17:54:18,325 - INFO - Initializing BedrockProcessor...
2025-09-24 17:54:18,327 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33e5ef2b_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:18,327 - INFO - Processing PDF from S3...
2025-09-24 17:54:18,328 - INFO - Downloading PDF from S3 to /tmp/tmp6oy7pip4/33e5ef2b_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:18,453 - INFO - Page 3: Extracted 16 characters, 2 lines from f40b6de6_A7CS2V2FOYJ4C84TQCE2_303b4f20_page_003.pdf
2025-09-24 17:54:18,454 - INFO - Successfully processed page 3
2025-09-24 17:54:19,314 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:54:19,315 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:19,316 - INFO - Splitting PDF e2a15965_YUC3NIJMZ2FWJZR6IN67 into 2 pages
2025-09-24 17:54:19,329 - INFO - Split PDF into 2 pages
2025-09-24 17:54:19,330 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:19,330 - INFO - Expected pages: [1, 2]
2025-09-24 17:54:20,187 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:54:20,187 - INFO - Splitting PDF into individual pages...
2025-09-24 17:54:20,191 - INFO - Splitting PDF 33e5ef2b_ZXVPNXXEV01AJD108C3M into 3 pages
2025-09-24 17:54:20,212 - INFO - Split PDF into 3 pages
2025-09-24 17:54:20,212 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:54:20,212 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:54:21,510 - INFO - Page 2: Extracted 3517 characters, 68 lines from f40b6de6_A7CS2V2FOYJ4C84TQCE2_303b4f20_page_002.pdf
2025-09-24 17:54:21,511 - INFO - Successfully processed page 2
2025-09-24 17:54:21,575 - INFO - Page 1: Extracted 3373 characters, 74 lines from f40b6de6_A7CS2V2FOYJ4C84TQCE2_303b4f20_page_001.pdf
2025-09-24 17:54:21,575 - INFO - Successfully processed page 1
2025-09-24 17:54:21,576 - INFO - Combined 3 pages into final text
2025-09-24 17:54:21,576 - INFO - Text validation for f40b6de6_A7CS2V2FOYJ4C84TQCE2: 6961 characters, 3 pages
2025-09-24 17:54:21,577 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:21,577 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:21,657 - INFO - Page 3: Extracted 1638 characters, 27 lines from e3369bdf_EOGW8CVHOLX2BALX5AA5_70b431b6_page_003.pdf
2025-09-24 17:54:21,658 - INFO - Successfully processed page 3
2025-09-24 17:54:21,738 - INFO - Page 1: Extracted 1548 characters, 74 lines from e3369bdf_EOGW8CVHOLX2BALX5AA5_70b431b6_page_001.pdf
2025-09-24 17:54:21,738 - INFO - Successfully processed page 1
2025-09-24 17:54:22,274 - INFO - Page 2: Extracted 275 characters, 10 lines from 62558527_FSH16W3D0EIW0C8PSEF9_05970c3f_page_002.pdf
2025-09-24 17:54:22,274 - INFO - Successfully processed page 2
2025-09-24 17:54:22,543 - INFO - Page 2: Extracted 2296 characters, 58 lines from e713400a_DZSJ7XNECMHN04RDNRIA_2c9916be_page_002.pdf
2025-09-24 17:54:22,543 - INFO - Successfully processed page 2
2025-09-24 17:54:22,641 - INFO - Page 2: Extracted 5315 characters, 51 lines from e3369bdf_EOGW8CVHOLX2BALX5AA5_70b431b6_page_002.pdf
2025-09-24 17:54:22,642 - INFO - Successfully processed page 2
2025-09-24 17:54:22,642 - INFO - Combined 3 pages into final text
2025-09-24 17:54:22,642 - INFO - Text validation for e3369bdf_EOGW8CVHOLX2BALX5AA5: 8556 characters, 3 pages
2025-09-24 17:54:22,643 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:22,643 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:23,184 - INFO - Page 1: Extracted 1514 characters, 74 lines from 7a9d1c32_I3XU9KPI7B1QVKBAHOYB_f0be1cbb_page_001.pdf
2025-09-24 17:54:23,184 - INFO - Successfully processed page 1
2025-09-24 17:54:23,211 - INFO - Page 2: Extracted 650 characters, 16 lines from 7a9d1c32_I3XU9KPI7B1QVKBAHOYB_f0be1cbb_page_002.pdf
2025-09-24 17:54:23,212 - INFO - Successfully processed page 2
2025-09-24 17:54:23,213 - INFO - Combined 2 pages into final text
2025-09-24 17:54:23,213 - INFO - Text validation for 7a9d1c32_I3XU9KPI7B1QVKBAHOYB: 2200 characters, 2 pages
2025-09-24 17:54:23,214 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:23,214 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:23,318 - INFO - Page 1: Extracted 1491 characters, 73 lines from 62558527_FSH16W3D0EIW0C8PSEF9_05970c3f_page_001.pdf
2025-09-24 17:54:23,319 - INFO - Successfully processed page 1
2025-09-24 17:54:23,319 - INFO - Combined 2 pages into final text
2025-09-24 17:54:23,319 - INFO - Text validation for 62558527_FSH16W3D0EIW0C8PSEF9: 1802 characters, 2 pages
2025-09-24 17:54:23,319 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:23,319 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:23,597 - INFO - Page 2: Extracted 656 characters, 16 lines from e2a15965_YUC3NIJMZ2FWJZR6IN67_61cc0ce1_page_002.pdf
2025-09-24 17:54:23,598 - INFO - Successfully processed page 2
2025-09-24 17:54:23,761 - INFO - Page 1: Extracted 3618 characters, 74 lines from e713400a_DZSJ7XNECMHN04RDNRIA_2c9916be_page_001.pdf
2025-09-24 17:54:23,761 - INFO - Successfully processed page 1
2025-09-24 17:54:23,762 - INFO - Combined 2 pages into final text
2025-09-24 17:54:23,762 - INFO - Text validation for e713400a_DZSJ7XNECMHN04RDNRIA: 5950 characters, 2 pages
2025-09-24 17:54:23,762 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:23,762 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:24,506 - INFO - Page 2: Extracted 1101 characters, 43 lines from 33e5ef2b_ZXVPNXXEV01AJD108C3M_b90d029c_page_002.pdf
2025-09-24 17:54:24,507 - INFO - Successfully processed page 2
2025-09-24 17:54:24,862 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '03ade66a-2474-4101-8e79-4f7214ff36bb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:24 GMT', 'content-type': 'application/json', 'content-length': '2160', 'connection': 'keep-alive', 'x-amzn-requestid': '03ade66a-2474-4101-8e79-4f7214ff36bb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a document with pages: page1, page2, page3. It\'s a "LOAD CONFIRMATION" from Echo, with details about load number, carrier, etc. It includes payment requirements, invoice, etc. It\'s basically a load confirmation, also similar to a rate confirmation? But the keywords: "LOAD CONFIRMATION" at top. Also "Carrier Rate" etc. This is a load confirmation from Echo to carrier; i.e., a "Load Confirmation" which is a type of rate confirmation. In the given enum, we have "rate_confirmation" for Carrier Rate Confirmation, and "cust_rate_confirmation" for Customer Rate Confirmation. Which one fits? The document is provided by Echo (carrier) to the carrier? Actually Echo is the broker. The "Carrier Rate Confirmation" is a carrier confirming rate/terms for a specific load. But this seems more like a load tender from customer? Wait, "LOAD CONFIRMATION" from Echo to the carrier: Echo sends a load confirmation to carrier. This is likely a "Load Confirmation" which is akin to "rate_confirmation". The instructions mention "rate_confirmation" is "Carrier Rate Confirmation" or "Load conformation". The doc has "LOAD CONFIRMATION" header, and includes "Rate" details. So we classify as "rate_confirmation".\n\nCheck for other types: It\'s not invoice (no invoice header). Not BOL. Not POD. Not lumper receipt. Not combined. Not purchase order. So "rate_confirmation" seems correct.\n\nAlso page3 only has "SIGNATURE: DATE:" - continuation of page2. So page1, page2, page3 all same doc type: rate_confirmation.\n\nWe need to output tool call with array of objects: each page has page_no and doc_type.\n\nThus output: [{"page_no":1,"doc_type":"rate_confirmation"}, ...]. Use the function.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4285, 'outputTokens': 422, 'totalTokens': 4707}, 'metrics': {'latencyMs': 2057}}
2025-09-24 17:54:24,863 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f40b6de6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:24,928 - INFO - 

A7CS2V2FOYJ4C84TQCE2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:24,928 - INFO - 

✓ Saved result: output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:25,085 - INFO - Page 1: Extracted 1589 characters, 76 lines from e2a15965_YUC3NIJMZ2FWJZR6IN67_61cc0ce1_page_001.pdf
2025-09-24 17:54:25,085 - INFO - Successfully processed page 1
2025-09-24 17:54:25,085 - INFO - Combined 2 pages into final text
2025-09-24 17:54:25,085 - INFO - Text validation for e2a15965_YUC3NIJMZ2FWJZR6IN67: 2281 characters, 2 pages
2025-09-24 17:54:25,085 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:25,085 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:25,284 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2c801f55-fc95-4228-a799-22c7c550274e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:25 GMT', 'content-type': 'application/json', 'content-length': '927', 'connection': 'keep-alive', 'x-amzn-requestid': '2c801f55-fc95-4228-a799-22c7c550274e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. The content is a "Shipping Instructions & Rate Confirmation". So likely a "rate_confirmation" document. It includes rate details, pay, etc. The header says "Shipping Instructions & Rate Confirmation". The keywords: "Rate Confirmation", "Payment", "Total Amount", etc. So classify as "rate_confirmation" for both pages. We need to output via tool classify_logistics_doc_type. Provide array of objects with page_no and doc_type. Both pages 1 and 2. And because page 2 is continuation, still same doc_type. Let\'s do.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3185, 'outputTokens': 147, 'totalTokens': 3332}, 'metrics': {'latencyMs': 861}}
2025-09-24 17:54:25,284 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7a9d1c32_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:25,414 - INFO - Page 1: Extracted 1786 characters, 73 lines from 33e5ef2b_ZXVPNXXEV01AJD108C3M_b90d029c_page_001.pdf
2025-09-24 17:54:25,415 - INFO - Successfully processed page 1
2025-09-24 17:54:25,460 - INFO - Page 3: Extracted 639 characters, 18 lines from 33e5ef2b_ZXVPNXXEV01AJD108C3M_b90d029c_page_003.pdf
2025-09-24 17:54:25,461 - INFO - Successfully processed page 3
2025-09-24 17:54:25,461 - INFO - Combined 3 pages into final text
2025-09-24 17:54:25,461 - INFO - Text validation for 33e5ef2b_ZXVPNXXEV01AJD108C3M: 3581 characters, 3 pages
2025-09-24 17:54:25,462 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:54:25,462 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:54:25,628 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '631f9ba8-7b3f-4407-ab21-22382a63ff48', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:25 GMT', 'content-type': 'application/json', 'content-length': '1094', 'connection': 'keep-alive', 'x-amzn-requestid': '631f9ba8-7b3f-4407-ab21-22382a63ff48'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document is "Shipping Instructions & Rate Confirmation". This seems like a rate confirmation. Keywords: "Rate Confirmation". The description says "Shipping Instructions & Rate Confirmation". That matches rate_confirmation (carrier rate confirmation). There\'s no invoice, no POD, no BOL. It\'s likely a rate confirmation from carrier. The pages include "Shipping Instructions & Rate Confirmation" header, with details about pickup, delivery, weight, etc. So doc_type: rate_confirmation. Both pages are part of same document. For page 2, check for continuation: page 2 has "Page 2 of 2". So it\'s continuation. So page 2 same type. Return two objects, one per page. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3086, 'outputTokens': 178, 'totalTokens': 3264}, 'metrics': {'latencyMs': 1033}}
2025-09-24 17:54:25,628 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/62558527_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:25,949 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f40b6de6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:25,968 - INFO - 

I3XU9KPI7B1QVKBAHOYB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:25,968 - INFO - 

✓ Saved result: output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:54:26,053 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0a6fc917-50d5-40de-894a-12c4f910868e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:25 GMT', 'content-type': 'application/json', 'content-length': '1200', 'connection': 'keep-alive', 'x-amzn-requestid': '0a6fc917-50d5-40de-894a-12c4f910868e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The input has two pages. Page1 looks like a "Load Confirmation" which is essentially a carrier rate confirmation or load tender. The keywords: "LOAD CONFIRMATION", "Carrier Rate", "Rate Confirmation". So doc_type likely rate_confirmation. It is a Load Confirmation by Echo. That is a carrier rate confirmation. So page1 doc_type "rate_confirmation".\n\nPage2 continues the load confirmation details. It starts with "Pickup" etc. It doesn\'t have a continuation header? It is part of same document. There\'s no "continued" but it\'s continuation of the same load confirmation. Should assign same doc_type: rate_confirmation.\n\nThus output two objects: page_no 1 and 2, both doc_type "rate_confirmation".\n\nWe must use tool call classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4025, 'outputTokens': 197, 'totalTokens': 4222}, 'metrics': {'latencyMs': 1062}}
2025-09-24 17:54:26,054 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e713400a_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:26,175 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8c16b9e6-e213-4d0c-8d50-28d8c32bb267', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:26 GMT', 'content-type': 'application/json', 'content-length': '2677', 'connection': 'keep-alive', 'x-amzn-requestid': '8c16b9e6-e213-4d0c-8d50-28d8c32bb267'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a 3-page document. The content appears to be a "Shipping Instructions & Rate Confirmation" with a lot of terms, carrier, etc. It includes a header "Shipping Instructions & Rate Confirmation" and has fields like Order #, Carrier, etc. It looks like a rate confirmation (load tender). The description says "Shipping Instructions & Rate Confirmation" and "Rate Confirmation". The doc type might be "rate_confirmation" or "cust_rate_confirmation" or "tender_from_cust". Let\'s see.\n\nWe need to classify each page. Since it\'s a multi-page single document, each page should get the same doc_type.\n\nNeed to determine which type it is. Let\'s analyze keyword patterns:\n\n- It\'s a Rate Confirmation: "Shipping Instructions & Rate Confirmation" appears. It includes "Carrier agrees to sign the Confirmation and return it to Broker". This is a rate confirmation for a load. It also includes an embedded "load tender" aspect: "This Rate and Load Confirmation ("Confirmation") governs the movement..." It appears to be a "Load Confirmation".\n\n- The doc types: rate_confirmation: "Carrier Rate Confirmation" is a document confirming rate/terms for a load. That\'s consistent.\n\n- cust_rate_confirmation: "Customer Rate Confirmation". But this one seems to be from the broker to the carrier, not the customer. It\'s a rate confirmation for the carrier to confirm. So likely "rate_confirmation" rather than cust_rate_confirmation.\n\n- tender_from_cust: "Load Tender from Customer". This would be a load tender from customer to carrier. But the content is a rate confirmation, not just a tender. It\'s similar though. But the header: "Shipping Instructions & Rate Confirmation" indicates it\'s a rate confirmation.\n\nThe definition for rate_confirmation: "Agreement from a carrier confirming rate/terms for a specific load." The content is a rate confirmation that the carrier must sign. Yes.\n\nThus doc_type = "rate_confirmation".\n\nWe must output one object per page:\n\n{\n  documents: [\n    {page_no: 1, doc_type: "rate_confirmation"},\n    {page_no: 2, doc_type: "rate_confirmation"},\n    {page_no: 3, doc_type: "rate_confirmation"}\n  ]\n}\n\nWe must output using tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4478, 'outputTokens': 500, 'totalTokens': 4978}, 'metrics': {'latencyMs': 2322}}
2025-09-24 17:54:26,175 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e3369bdf_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:26,278 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7a9d1c32_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:26,297 - INFO - 

FSH16W3D0EIW0C8PSEF9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:26,298 - INFO - 

✓ Saved result: output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:54:26,633 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/62558527_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:26,675 - INFO - 

DZSJ7XNECMHN04RDNRIA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:26,675 - INFO - 

✓ Saved result: output/run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:54:26,975 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e713400a_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:27,035 - INFO - 

EOGW8CVHOLX2BALX5AA5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:27,036 - INFO - 

✓ Saved result: output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:27,115 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8b48884f-32a5-4dcf-89ad-1b2a6e4863d4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:26 GMT', 'content-type': 'application/json', 'content-length': '991', 'connection': 'keep-alive', 'x-amzn-requestid': '8b48884f-32a5-4dcf-89ad-1b2a6e4863d4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We must classify each page. The document is "Shipping Instructions & Rate Confirmation". That seems like a rate confirmation. The keywords: "Rate Confirmation", "Total Amount", "Terms", "Payment". So doc_type likely "rate_confirmation". Check if any other type: maybe combined_carrier_documents? But this looks single page with rate confirmation. The header says "Shipping Instructions & Rate Confirmation". So classification per page: page1 doc_type rate_confirmation, page2 doc_type rate_confirmation. No continuation indicator. So output array of two objects. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3206, 'outputTokens': 146, 'totalTokens': 3352}, 'metrics': {'latencyMs': 890}}
2025-09-24 17:54:27,115 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e2a15965_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:27,367 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e3369bdf_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:27,394 - INFO - 

YUC3NIJMZ2FWJZR6IN67.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:27,395 - INFO - 

✓ Saved result: output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:54:27,449 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '61692a1b-3da3-427e-a14f-1d9fe4f736dc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:24:27 GMT', 'content-type': 'application/json', 'content-length': '755', 'connection': 'keep-alive', 'x-amzn-requestid': '61692a1b-3da3-427e-a14f-1d9fe4f736dc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1: "Shipping Instructions & Rate Confirmation" - this looks like rate confirmation. Keywords: "Rate Confirmation". So doc_type: rate_confirmation. Page2 also same content, continuation. Page3 same. So all three pages same type. Return array of objects with page_no and doc_type. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3860, 'outputTokens': 110, 'totalTokens': 3970}, 'metrics': {'latencyMs': 709}}
2025-09-24 17:54:27,449 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33e5ef2b_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:27,694 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e2a15965_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:27,722 - INFO - 

ZXVPNXXEV01AJD108C3M.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:54:27,722 - INFO - 

✓ Saved result: output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,059 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33e5ef2b_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:28,060 - INFO - 
📊 Processing Summary:
2025-09-24 17:54:28,060 - INFO -    Total files: 7
2025-09-24 17:54:28,060 - INFO -    Successful: 7
2025-09-24 17:54:28,060 - INFO -    Failed: 0
2025-09-24 17:54:28,060 - INFO -    Duration: 18.76 seconds
2025-09-24 17:54:28,060 - INFO -    Output directory: output
2025-09-24 17:54:28,060 - INFO - 
📋 Successfully Processed Files:
2025-09-24 17:54:28,060 - INFO -    📄 A7CS2V2FOYJ4C84TQCE2.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,060 - INFO -    📄 DZSJ7XNECMHN04RDNRIA.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,060 - INFO -    📄 EOGW8CVHOLX2BALX5AA5.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,061 - INFO -    📄 FSH16W3D0EIW0C8PSEF9.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,061 - INFO -    📄 I3XU9KPI7B1QVKBAHOYB.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,061 - INFO -    📄 YUC3NIJMZ2FWJZR6IN67.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,061 - INFO -    📄 ZXVPNXXEV01AJD108C3M.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}
2025-09-24 17:54:28,061 - INFO - 
============================================================================================================================================
2025-09-24 17:54:28,061 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 17:54:28,061 - INFO - ============================================================================================================================================
2025-09-24 17:54:28,061 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 17:54:28,061 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:54:28,061 - INFO - A7CS2V2FOYJ4C84TQCE2.pdf                           1      rate_confirmation                        run1_A7CS2V2FOYJ4C84TQCE2.json                                                  
2025-09-24 17:54:28,061 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:28,061 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:28,061 - INFO - 
2025-09-24 17:54:28,061 - INFO - A7CS2V2FOYJ4C84TQCE2.pdf                           2      rate_confirmation                        run1_A7CS2V2FOYJ4C84TQCE2.json                                                  
2025-09-24 17:54:28,061 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:28,061 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:28,061 - INFO - 
2025-09-24 17:54:28,061 - INFO - A7CS2V2FOYJ4C84TQCE2.pdf                           3      rate_confirmation                        run1_A7CS2V2FOYJ4C84TQCE2.json                                                  
2025-09-24 17:54:28,061 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:54:28,061 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:28,061 - INFO - 
2025-09-24 17:54:28,061 - INFO - DZSJ7XNECMHN04RDNRIA.pdf                           1      rate_confirmation                        run1_DZSJ7XNECMHN04RDNRIA.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - DZSJ7XNECMHN04RDNRIA.pdf                           2      rate_confirmation                        run1_DZSJ7XNECMHN04RDNRIA.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - EOGW8CVHOLX2BALX5AA5.pdf                           1      rate_confirmation                        run1_EOGW8CVHOLX2BALX5AA5.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - EOGW8CVHOLX2BALX5AA5.pdf                           2      rate_confirmation                        run1_EOGW8CVHOLX2BALX5AA5.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - EOGW8CVHOLX2BALX5AA5.pdf                           3      rate_confirmation                        run1_EOGW8CVHOLX2BALX5AA5.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - FSH16W3D0EIW0C8PSEF9.pdf                           1      rate_confirmation                        run1_FSH16W3D0EIW0C8PSEF9.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - FSH16W3D0EIW0C8PSEF9.pdf                           2      rate_confirmation                        run1_FSH16W3D0EIW0C8PSEF9.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,062 - INFO - I3XU9KPI7B1QVKBAHOYB.pdf                           1      rate_confirmation                        run1_I3XU9KPI7B1QVKBAHOYB.json                                                  
2025-09-24 17:54:28,062 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:28,062 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:54:28,062 - INFO - 
2025-09-24 17:54:28,063 - INFO - I3XU9KPI7B1QVKBAHOYB.pdf                           2      rate_confirmation                        run1_I3XU9KPI7B1QVKBAHOYB.json                                                  
2025-09-24 17:54:28,063 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:54:28,063 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:54:28,063 - INFO - 
2025-09-24 17:54:28,063 - INFO - YUC3NIJMZ2FWJZR6IN67.pdf                           1      rate_confirmation                        run1_YUC3NIJMZ2FWJZR6IN67.json                                                  
2025-09-24 17:54:28,063 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:28,063 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:54:28,063 - INFO - 
2025-09-24 17:54:28,063 - INFO - YUC3NIJMZ2FWJZR6IN67.pdf                           2      rate_confirmation                        run1_YUC3NIJMZ2FWJZR6IN67.json                                                  
2025-09-24 17:54:28,063 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:54:28,063 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:54:28,063 - INFO - 
2025-09-24 17:54:28,063 - INFO - ZXVPNXXEV01AJD108C3M.pdf                           1      rate_confirmation                        run1_ZXVPNXXEV01AJD108C3M.json                                                  
2025-09-24 17:54:28,063 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:28,063 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,063 - INFO - 
2025-09-24 17:54:28,063 - INFO - ZXVPNXXEV01AJD108C3M.pdf                           2      rate_confirmation                        run1_ZXVPNXXEV01AJD108C3M.json                                                  
2025-09-24 17:54:28,063 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:28,063 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,063 - INFO - 
2025-09-24 17:54:28,063 - INFO - ZXVPNXXEV01AJD108C3M.pdf                           3      rate_confirmation                        run1_ZXVPNXXEV01AJD108C3M.json                                                  
2025-09-24 17:54:28,063 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:54:28,063 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,063 - INFO - 
2025-09-24 17:54:28,063 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:54:28,063 - INFO - Total entries: 17
2025-09-24 17:54:28,063 - INFO - ============================================================================================================================================
2025-09-24 17:54:28,063 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 17:54:28,063 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:54:28,063 - INFO -   1. A7CS2V2FOYJ4C84TQCE2.pdf            Page 1   → rate_confirmation | run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:28,063 - INFO -   2. A7CS2V2FOYJ4C84TQCE2.pdf            Page 2   → rate_confirmation | run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:28,064 - INFO -   3. A7CS2V2FOYJ4C84TQCE2.pdf            Page 3   → rate_confirmation | run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:54:28,064 - INFO -   4. DZSJ7XNECMHN04RDNRIA.pdf            Page 1   → rate_confirmation | run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:54:28,064 - INFO -   5. DZSJ7XNECMHN04RDNRIA.pdf            Page 2   → rate_confirmation | run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:54:28,064 - INFO -   6. EOGW8CVHOLX2BALX5AA5.pdf            Page 1   → rate_confirmation | run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:28,064 - INFO -   7. EOGW8CVHOLX2BALX5AA5.pdf            Page 2   → rate_confirmation | run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:28,064 - INFO -   8. EOGW8CVHOLX2BALX5AA5.pdf            Page 3   → rate_confirmation | run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:54:28,064 - INFO -   9. FSH16W3D0EIW0C8PSEF9.pdf            Page 1   → rate_confirmation | run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:54:28,064 - INFO -  10. FSH16W3D0EIW0C8PSEF9.pdf            Page 2   → rate_confirmation | run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:54:28,064 - INFO -  11. I3XU9KPI7B1QVKBAHOYB.pdf            Page 1   → rate_confirmation | run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:54:28,064 - INFO -  12. I3XU9KPI7B1QVKBAHOYB.pdf            Page 2   → rate_confirmation | run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:54:28,064 - INFO -  13. YUC3NIJMZ2FWJZR6IN67.pdf            Page 1   → rate_confirmation | run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:54:28,064 - INFO -  14. YUC3NIJMZ2FWJZR6IN67.pdf            Page 2   → rate_confirmation | run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:54:28,064 - INFO -  15. ZXVPNXXEV01AJD108C3M.pdf            Page 1   → rate_confirmation | run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,064 - INFO -  16. ZXVPNXXEV01AJD108C3M.pdf            Page 2   → rate_confirmation | run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,064 - INFO -  17. ZXVPNXXEV01AJD108C3M.pdf            Page 3   → rate_confirmation | run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:54:28,064 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:54:28,064 - INFO - 
✅ Test completed: {'total_files': 7, 'processed': 7, 'failed': 0, 'errors': [], 'duration_seconds': 18.755872, 'processed_files': [{'filename': 'A7CS2V2FOYJ4C84TQCE2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf'}, {'filename': 'DZSJ7XNECMHN04RDNRIA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf'}, {'filename': 'EOGW8CVHOLX2BALX5AA5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf'}, {'filename': 'FSH16W3D0EIW0C8PSEF9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf'}, {'filename': 'I3XU9KPI7B1QVKBAHOYB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf'}, {'filename': 'YUC3NIJMZ2FWJZR6IN67.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf'}, {'filename': 'ZXVPNXXEV01AJD108C3M.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf'}]}
