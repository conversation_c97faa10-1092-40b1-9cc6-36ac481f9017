2025-09-24 17:40:33,256 - INFO - Logging initialized. Log file: logs/test_classification_20250924_174033.log
2025-09-24 17:40:33,257 - INFO - 📁 Found 16 files to process
2025-09-24 17:40:33,257 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:40:33,257 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-24 17:40:33,257 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-24 17:40:33,257 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-24 17:40:33,257 - INFO - ⬆️ [17:40:33] Uploading: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:40:35,350 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg -> s3://document-extraction-logistically/temp/bc130a2b_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:40:35,350 - INFO - 🔍 [17:40:35] Starting classification: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:40:35,351 - INFO - ⬆️ [17:40:35] Uploading: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:35,353 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:35,380 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:35,396 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bc130a2b_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:40:35,397 - INFO - Processing image from S3...
2025-09-24 17:40:36,724 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf -> s3://document-extraction-logistically/temp/bf73db02_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:36,724 - INFO - 🔍 [17:40:36] Starting classification: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:36,725 - INFO - ⬆️ [17:40:36] Uploading: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:36,726 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:36,739 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:36,742 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf73db02_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:36,742 - INFO - Processing PDF from S3...
2025-09-24 17:40:36,743 - INFO - Downloading PDF from S3 to /tmp/tmpg430vc04/bf73db02_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:38,091 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf -> s3://document-extraction-logistically/temp/a60317f4_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:38,092 - INFO - 🔍 [17:40:38] Starting classification: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:38,093 - INFO - ⬆️ [17:40:38] Uploading: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:38,096 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:38,122 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:38,125 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a60317f4_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:38,126 - INFO - Processing PDF from S3...
2025-09-24 17:40:38,126 - INFO - Downloading PDF from S3 to /tmp/tmp4j5px_yk/a60317f4_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:38,875 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf -> s3://document-extraction-logistically/temp/ecf3d13d_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:38,876 - INFO - 🔍 [17:40:38] Starting classification: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:38,877 - INFO - ⬆️ [17:40:38] Uploading: S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:38,879 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:38,899 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:38,903 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ecf3d13d_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:38,903 - INFO - Processing PDF from S3...
2025-09-24 17:40:38,903 - INFO - Downloading PDF from S3 to /tmp/tmpcybxnkbm/ecf3d13d_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:39,064 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:40:39,064 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:39,068 - INFO - Splitting PDF bf73db02_GKYUUU2YTZ1YTPGXHO51 into 3 pages
2025-09-24 17:40:39,099 - INFO - Split PDF into 3 pages
2025-09-24 17:40:39,099 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:39,099 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:40:39,638 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf -> s3://document-extraction-logistically/temp/afd08e2e_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:39,638 - INFO - 🔍 [17:40:39] Starting classification: S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:39,638 - INFO - ⬆️ [17:40:39] Uploading: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:39,640 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:39,650 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:39,652 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/afd08e2e_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:39,653 - INFO - Processing PDF from S3...
2025-09-24 17:40:39,653 - INFO - Downloading PDF from S3 to /tmp/tmpygx2hxay/afd08e2e_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:40,119 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:40:40,119 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:40,120 - INFO - Splitting PDF a60317f4_I6IM526GOVKAWH0B5WKE into 1 pages
2025-09-24 17:40:40,122 - INFO - Split PDF into 1 pages
2025-09-24 17:40:40,122 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:40,122 - INFO - Expected pages: [1]
2025-09-24 17:40:40,318 - INFO - S3 Image temp/bc130a2b_AOYIL346IKT06LKO6D2R.jpg: Extracted 882 characters, 69 lines
2025-09-24 17:40:40,318 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:40,318 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:40,365 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf -> s3://document-extraction-logistically/temp/151658bd_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:40,365 - INFO - 🔍 [17:40:40] Starting classification: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:40,366 - INFO - ⬆️ [17:40:40] Uploading: bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:40:40,368 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:40,389 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:40,395 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/151658bd_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:40,398 - INFO - Processing PDF from S3...
2025-09-24 17:40:40,398 - INFO - Downloading PDF from S3 to /tmp/tmpou5zkldx/151658bd_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:41,297 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:40:41,298 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:41,298 - WARNING - incorrect startxref pointer(1)
2025-09-24 17:40:41,302 - INFO - Splitting PDF ecf3d13d_KK3PKFLS4ROTEZ7LOMMD into 1 pages
2025-09-24 17:40:41,313 - INFO - Split PDF into 1 pages
2025-09-24 17:40:41,313 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:41,313 - INFO - Expected pages: [1]
2025-09-24 17:40:41,424 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 17:40:41,424 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:41,426 - INFO - Splitting PDF afd08e2e_S92OW12RYFF5G3READPW into 1 pages
2025-09-24 17:40:41,427 - INFO - Split PDF into 1 pages
2025-09-24 17:40:41,427 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:41,428 - INFO - Expected pages: [1]
2025-09-24 17:40:41,791 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 17:40:41,791 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:41,792 - INFO - Splitting PDF 151658bd_TQSZJLFFGGKB4NFN12RD into 1 pages
2025-09-24 17:40:41,793 - INFO - Split PDF into 1 pages
2025-09-24 17:40:41,793 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:41,793 - INFO - Expected pages: [1]
2025-09-24 17:40:42,770 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ccae128f-e86a-45d7-99ac-995370357faf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:42 GMT', 'content-type': 'application/json', 'content-length': '1217', 'connection': 'keep-alive', 'x-amzn-requestid': 'ccae128f-e86a-45d7-99ac-995370357faf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We must examine keywords. The content shows "Delivery Receipt" and "Proof of Delivery"? It says "Delivery Receipt". It contains fields like Shipper, Consignee, Bill of Lading Number, Purchase Order Number, Description, Class, Weight, etc. It also has "Receiver Name", "Time in", "Time Out", "Signature". That indicates Proof of Delivery or Delivery Receipt. Which doc_type? The enumeration includes "pod" for Proof of Delivery. "Delivery Receipt" could be POD or a delivery receipt. POD is defined as record confirming recipient received goods, includes signatures, dates, condition notes. This page has "Receiver Name", "Signature", "Time in", "Time Out" – that\'s POD. So doc_type = pod.\n\nWe should output JSON via tool call \'classify_logistics_doc_type\' with documents array containing one object: page_no 1, doc_type "pod".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2813, 'outputTokens': 212, 'totalTokens': 3025}, 'metrics': {'latencyMs': 1174}}
2025-09-24 17:40:42,771 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bc130a2b_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:40:43,837 - INFO - Page 1: Extracted 643 characters, 34 lines from bf73db02_GKYUUU2YTZ1YTPGXHO51_47a583c2_page_001.pdf
2025-09-24 17:40:43,837 - INFO - Successfully processed page 1
2025-09-24 17:40:43,941 - INFO - Page 3: Extracted 1566 characters, 104 lines from bf73db02_GKYUUU2YTZ1YTPGXHO51_47a583c2_page_003.pdf
2025-09-24 17:40:43,941 - INFO - Successfully processed page 3
2025-09-24 17:40:44,345 - INFO - Page 2: Extracted 1426 characters, 86 lines from bf73db02_GKYUUU2YTZ1YTPGXHO51_47a583c2_page_002.pdf
2025-09-24 17:40:44,345 - INFO - Successfully processed page 2
2025-09-24 17:40:44,345 - INFO - Combined 3 pages into final text
2025-09-24 17:40:44,345 - INFO - Text validation for bf73db02_GKYUUU2YTZ1YTPGXHO51: 3690 characters, 3 pages
2025-09-24 17:40:44,346 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:44,346 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:44,475 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf -> s3://document-extraction-logistically/temp/7d4e55ed_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:40:44,476 - INFO - 🔍 [17:40:44] Starting classification: bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:40:44,476 - INFO - ⬆️ [17:40:44] Uploading: bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:44,477 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:44,490 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:44,492 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7d4e55ed_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:40:44,493 - INFO - Processing PDF from S3...
2025-09-24 17:40:44,493 - INFO - Downloading PDF from S3 to /tmp/tmpho08nrw5/7d4e55ed_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:40:46,248 - INFO - Page 1: Extracted 1312 characters, 65 lines from 151658bd_TQSZJLFFGGKB4NFN12RD_f1e21384_page_001.pdf
2025-09-24 17:40:46,249 - INFO - Successfully processed page 1
2025-09-24 17:40:46,249 - INFO - Combined 1 pages into final text
2025-09-24 17:40:46,259 - INFO - Text validation for 151658bd_TQSZJLFFGGKB4NFN12RD: 1329 characters, 1 pages
2025-09-24 17:40:46,265 - INFO - Page 1: Extracted 1921 characters, 143 lines from a60317f4_I6IM526GOVKAWH0B5WKE_3de0270a_page_001.pdf
2025-09-24 17:40:46,266 - INFO - Successfully processed page 1
2025-09-24 17:40:46,266 - INFO - Combined 1 pages into final text
2025-09-24 17:40:46,266 - INFO - Text validation for a60317f4_I6IM526GOVKAWH0B5WKE: 1938 characters, 1 pages
2025-09-24 17:40:46,266 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:46,266 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:46,268 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:46,268 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:46,434 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf -> s3://document-extraction-logistically/temp/645db968_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:46,435 - INFO - 🔍 [17:40:46] Starting classification: bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:46,436 - INFO - ⬆️ [17:40:46] Uploading: bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:46,437 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:46,453 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:46,457 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/645db968_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:46,458 - INFO - Processing PDF from S3...
2025-09-24 17:40:46,458 - INFO - Downloading PDF from S3 to /tmp/tmpngu7ptkx/645db968_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:46,785 - INFO - Page 1: Extracted 1576 characters, 111 lines from afd08e2e_S92OW12RYFF5G3READPW_1fdfba48_page_001.pdf
2025-09-24 17:40:46,785 - INFO - Successfully processed page 1
2025-09-24 17:40:46,786 - INFO - Combined 1 pages into final text
2025-09-24 17:40:46,786 - INFO - Text validation for afd08e2e_S92OW12RYFF5G3READPW: 1593 characters, 1 pages
2025-09-24 17:40:46,786 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:46,786 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:46,833 - INFO - Page 1: Extracted 1453 characters, 86 lines from ecf3d13d_KK3PKFLS4ROTEZ7LOMMD_ce239752_page_001.pdf
2025-09-24 17:40:46,834 - INFO - Successfully processed page 1
2025-09-24 17:40:46,834 - INFO - Combined 1 pages into final text
2025-09-24 17:40:46,834 - INFO - Text validation for ecf3d13d_KK3PKFLS4ROTEZ7LOMMD: 1470 characters, 1 pages
2025-09-24 17:40:46,835 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:46,835 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:47,143 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf -> s3://document-extraction-logistically/temp/1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:47,143 - INFO - 🔍 [17:40:47] Starting classification: bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:47,144 - INFO - ⬆️ [17:40:47] Uploading: bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:40:47,146 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:47,161 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:47,167 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:47,168 - INFO - Processing PDF from S3...
2025-09-24 17:40:47,168 - INFO - Downloading PDF from S3 to /tmp/tmpxqh_bfzy/1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:47,808 - INFO - Downloaded PDF size: 1.8 MB
2025-09-24 17:40:47,809 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:47,811 - INFO - Splitting PDF 7d4e55ed_bol_DV1B199A0TJGUQ3FETIU into 3 pages
2025-09-24 17:40:47,840 - INFO - Split PDF into 3 pages
2025-09-24 17:40:47,840 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:47,840 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:40:48,162 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf -> s3://document-extraction-logistically/temp/a00c9286_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:40:48,162 - INFO - 🔍 [17:40:48] Starting classification: bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:40:48,163 - INFO - ⬆️ [17:40:48] Uploading: bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:40:48,169 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:48,181 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:48,187 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a00c9286_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:40:48,187 - INFO - Processing PDF from S3...
2025-09-24 17:40:48,187 - INFO - Downloading PDF from S3 to /tmp/tmpu87pfuoa/a00c9286_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:40:48,337 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0c5a24ed-b6aa-4438-a073-aefc4bfd334f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:48 GMT', 'content-type': 'application/json', 'content-length': '788', 'connection': 'keep-alive', 'x-amzn-requestid': '0c5a24ed-b6aa-4438-a073-aefc4bfd334f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The content looks like a "Delivery Receipt" or "Delivery Ticket". The header says "DELIVERY RECEIPT". The page contains Received by, Delivery details, etc. There are fields typical of Proof of Delivery: Received by, Delivery Date, Driver Name, etc. Also "Manifest Number" and "Shipment Details". So this is a POD (Proof of Delivery). The doc_type should be "pod". There\'s no continuation indicator. So return as single object for page 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2944, 'outputTokens': 122, 'totalTokens': 3066}, 'metrics': {'latencyMs': 732}}
2025-09-24 17:40:48,337 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/151658bd_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:48,567 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6bc77837-98cb-43dd-8ac9-8e22e6e23c5e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:48 GMT', 'content-type': 'application/json', 'content-length': '2857', 'connection': 'keep-alive', 'x-amzn-requestid': '6bc77837-98cb-43dd-8ac9-8e22e6e23c5e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 3 pages. Need to classify each page. We must consider if page2 and page3 are continuation. Page2 says "Page 2 of 2" and page3 "Page 1 of 2". Page3 seems to be similar to page2 but maybe a different set? Actually page3 seems to be similar but not same content. It looks like page3 is a separate document (maybe another receipt). But page2 had "Page 2 of 2" and page3 "Page 1 of 2". They appear to be separate documents. The first page1 is a proof-of-delivery. Page1 has header: "proof-of-delivery". So page1 doc_type = pod.\n\nPage2: Looks like a delivery receipt freight bill, includes freight bill, bill of lading, charges, etc. This looks like an invoice? Actually it\'s a "Freight Bill" which is a carrier invoice. It includes "Freight Bill 6435169926 R0". And includes charges, etc. So doc_type likely "invoice". But check if it has signs of commercial invoice? Does it have HS codes? I see NMFC codes but not HS codes. So plain invoice. Also it\'s a freight bill, not commercial. So invoice.\n\nPage3: This also looks similar to page2, a delivery receipt. It also contains "Freight Bill 6435169926 R0". So also invoice. But maybe it\'s a different freight bill for a different BOL number? It includes Bill of Lading 11746 same? Yes same BOL. Might be same? But page3 header says "Page 1 of 2". Page2 header says "Page 2 of 2". So page2 and page3 are two pages of a single document: page2 is page 2 of 2, page3 is page 1 of 2. They are reversed? Actually page2 says "Page 2 of 2" but page3 says "Page 1 of 2". But page3 seems to be first page of a two-page document. So they are same document but reversed order? Wait, the user provided page2 then page3. Page2 says "Page 2 of 2". Page3 says "Page 1 of 2". That suggests that page3 is actually the first page of a 2-page document, and page2 is the second page. So page3 and page2 are part of same document. But they are separate pages of a single document. They might be separate documents? But the content is similar. They likely belong to same shipment. The question is classification: each page gets same doc_type. So page2 and page3 are invoice. Page1 is pod.\n\nCheck if page2 or page3 might be "combined_carrier_documents" if they contain multiple distinct document headers? They contain only freight bill. What about page1: It contains a proof-of-delivery header. So pod.\n\nThus output: three objects: page1 pod, page2 invoice, page3 invoice.\n\nWe must use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3806, 'outputTokens': 669, 'totalTokens': 4475}, 'metrics': {'latencyMs': 3032}}
2025-09-24 17:40:48,568 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf73db02_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:48,705 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be4b0ddc-28fb-4b5f-a263-7f2957cbc99c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:48 GMT', 'content-type': 'application/json', 'content-length': '1177', 'connection': 'keep-alive', 'x-amzn-requestid': 'be4b0ddc-28fb-4b5f-a263-7f2957cbc99c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Here we have only page1. Determine if this is a receipt, maybe a delivery receipt from Estes Express. Keywords: "RECEIPT", "Delivery", "Good Order", "Shipper BL or GBL Number", "Consignee", "Receiver\'s Signature". This looks like a "Receipt" of goods, maybe a "Proof of Delivery" (POD). POD includes signatures, delivered conditions, date, etc. It contains "Receiver\'s Signature". Also has title "RECEIPT". So likely POD. There\'s no explicit "Proof of Delivery" but it\'s a receipt from carrier. According to definitions, POD: "Proof of Delivery". Usually includes signature, date, condition notes. This page has those. So doc_type "pod".\n\nBut also may be "Delivery Receipt" which is POD. So assign "pod". Also check for continuation: no. So output single object: page_no 1, doc_type "pod". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3220, 'outputTokens': 219, 'totalTokens': 3439}, 'metrics': {'latencyMs': 1118}}
2025-09-24 17:40:48,705 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a60317f4_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:49,321 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:40:49,322 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:49,324 - INFO - Splitting PDF 1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17 into 1 pages
2025-09-24 17:40:49,327 - INFO - Split PDF into 1 pages
2025-09-24 17:40:49,327 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:49,327 - INFO - Expected pages: [1]
2025-09-24 17:40:49,391 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 17:40:49,391 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:49,394 - INFO - Splitting PDF 645db968_bol_HEMTH4BHCWXWE044I0SK into 2 pages
2025-09-24 17:40:49,401 - INFO - Split PDF into 2 pages
2025-09-24 17:40:49,402 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:49,402 - INFO - Expected pages: [1, 2]
2025-09-24 17:40:49,523 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf -> s3://document-extraction-logistically/temp/6ffe7abe_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:40:49,524 - INFO - 🔍 [17:40:49] Starting classification: bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:40:49,525 - INFO - ⬆️ [17:40:49] Uploading: bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:40:49,526 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:49,542 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:49,549 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ffe7abe_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:40:49,549 - INFO - Processing PDF from S3...
2025-09-24 17:40:49,550 - INFO - Downloading PDF from S3 to /tmp/tmprpwvr31m/6ffe7abe_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:40:50,892 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:40:50,892 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:50,894 - INFO - Splitting PDF a00c9286_bol_K88AFHJU4HIQUZYTMOF4 into 1 pages
2025-09-24 17:40:50,897 - INFO - Split PDF into 1 pages
2025-09-24 17:40:50,898 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:50,898 - INFO - Expected pages: [1]
2025-09-24 17:40:52,583 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 17:40:52,583 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:52,584 - INFO - Splitting PDF 6ffe7abe_bol_P2EZG64EJI98NT69TSAL into 1 pages
2025-09-24 17:40:52,588 - INFO - Split PDF into 1 pages
2025-09-24 17:40:52,588 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:52,589 - INFO - Expected pages: [1]
2025-09-24 17:40:52,680 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf -> s3://document-extraction-logistically/temp/a4c2cadc_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:40:52,681 - INFO - 🔍 [17:40:52] Starting classification: bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:40:52,682 - INFO - ⬆️ [17:40:52] Uploading: bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:40:52,685 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:52,711 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:52,719 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a4c2cadc_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:40:52,720 - INFO - Processing PDF from S3...
2025-09-24 17:40:52,720 - INFO - Downloading PDF from S3 to /tmp/tmp6_53638p/a4c2cadc_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:40:54,033 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '88d69032-b9c4-4e96-a279-7172d5fb1500', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:53 GMT', 'content-type': 'application/json', 'content-length': '882', 'connection': 'keep-alive', 'x-amzn-requestid': '88d69032-b9c4-4e96-a279-7172d5fb1500'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to decide doc_type. The content shows "Delivery Receipt" header, includes delivery details, signatures, etc. It seems a Proof of Delivery (POD). The header says "Delivery Receipt". That matches POD. The page includes "Consignee Signature" etc. Should be pod. Note that "Delivery Receipt" could also be a "proof of delivery" or "delivery receipt" is a type of POD. We should classify as pod.\n\nCheck for continuation? No. So output for page 1: doc_type: pod.\n\nWe need to output JSON via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3014, 'outputTokens': 147, 'totalTokens': 3161}, 'metrics': {'latencyMs': 926}}
2025-09-24 17:40:54,034 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ecf3d13d_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:54,110 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf -> s3://document-extraction-logistically/temp/8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:40:54,111 - INFO - 🔍 [17:40:54] Starting classification: bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:40:54,111 - INFO - ⬆️ [17:40:54] Uploading: pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:40:54,112 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:54,132 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:54,141 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:40:54,146 - INFO - Processing PDF from S3...
2025-09-24 17:40:54,149 - INFO - Page 3: Extracted 990 characters, 86 lines from 7d4e55ed_bol_DV1B199A0TJGUQ3FETIU_7e574e16_page_003.pdf
2025-09-24 17:40:54,149 - INFO - Downloading PDF from S3 to /tmp/tmpxab4rs6c/8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:40:54,150 - INFO - Successfully processed page 3
2025-09-24 17:40:54,241 - INFO - Page 2: Extracted 2112 characters, 162 lines from 7d4e55ed_bol_DV1B199A0TJGUQ3FETIU_7e574e16_page_002.pdf
2025-09-24 17:40:54,241 - INFO - Successfully processed page 2
2025-09-24 17:40:54,849 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg -> s3://document-extraction-logistically/temp/bca37a5c_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:40:54,850 - INFO - 🔍 [17:40:54] Starting classification: pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:40:54,851 - INFO - ⬆️ [17:40:54] Uploading: pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:40:54,852 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:54,877 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:54,888 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bca37a5c_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:40:54,889 - INFO - Processing image from S3...
2025-09-24 17:40:54,954 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '86ee8728-f18c-4805-bd29-70f5f919a0c9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:54 GMT', 'content-type': 'application/json', 'content-length': '1945', 'connection': 'keep-alive', 'x-amzn-requestid': '86ee8728-f18c-4805-bd29-70f5f919a0c9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Determine doc type. Text: appears to be a freight bill, with "SEFL FREIGHT BILL NUMBER", "Receipt", "SEFL", "Freight Bill Number", "P.O. Number", "Shipper", "Consignee", "Weight", "Total", etc. Also mentions "Weight & Inspection review" etc. The keywords: "Freight Bill", "Freight Bill Number", "Receipt", "Freight Bill". Could be invoice? But it\'s more like a freight bill receipt. The doc type list: invoice, comm_invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, other, tender_from_cust, so_confirmation, ingate, outgate.\n\nWhich fits? "Freight Bill" is typically a carrier invoice (bill of lading + invoice). Might be "invoice" type. But also weight & inspection review indicates some weight & inspection. But the document includes weight and inspection review but is still a freight bill. Could it be "weight_and_inspection_cert"? That type is a certificate issued by authority confirming weight. This doc includes weight and inspection review but also includes freight charges etc. Might not be a certificate. The doc type "invoice" is more appropriate. Also "receipt" indicates payment receipt? Possibly "invoice". Let\'s decide: Likely "invoice".\n\nBut check if "invoice" vs "comm_invoice". It has no HS codes, so not comm_invoice. It\'s not lumper_receipt. So "invoice".\n\nThus output single object: page_no 1, doc_type "invoice".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3106, 'outputTokens': 386, 'totalTokens': 3492}, 'metrics': {'latencyMs': 1851}}
2025-09-24 17:40:54,954 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/afd08e2e_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:55,388 - INFO - Page 1: Extracted 2979 characters, 110 lines from 7d4e55ed_bol_DV1B199A0TJGUQ3FETIU_7e574e16_page_001.pdf
2025-09-24 17:40:55,388 - INFO - Successfully processed page 1
2025-09-24 17:40:55,388 - INFO - Combined 3 pages into final text
2025-09-24 17:40:55,389 - INFO - Text validation for 7d4e55ed_bol_DV1B199A0TJGUQ3FETIU: 6135 characters, 3 pages
2025-09-24 17:40:55,390 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:55,390 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:55,574 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg -> s3://document-extraction-logistically/temp/95bbe640_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:40:55,575 - INFO - 🔍 [17:40:55] Starting classification: pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:40:55,576 - INFO - ⬆️ [17:40:55] Uploading: pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:40:55,578 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:55,594 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:55,598 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/95bbe640_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:40:55,598 - INFO - Processing image from S3...
2025-09-24 17:40:55,802 - INFO - Page 1: Extracted 3736 characters, 122 lines from 1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17_1d10b8d1_page_001.pdf
2025-09-24 17:40:55,802 - INFO - Successfully processed page 1
2025-09-24 17:40:55,802 - INFO - Combined 1 pages into final text
2025-09-24 17:40:55,803 - INFO - Text validation for 1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17: 3753 characters, 1 pages
2025-09-24 17:40:55,804 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:55,804 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:56,304 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_YC2ROS9NUB31VO0ABEV5.jpeg -> s3://document-extraction-logistically/temp/a7ab27a0_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:40:56,305 - INFO - 🔍 [17:40:56] Starting classification: pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:40:56,307 - INFO - Initializing TextractProcessor...
2025-09-24 17:40:56,321 - INFO - Initializing BedrockProcessor...
2025-09-24 17:40:56,326 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a7ab27a0_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:40:56,327 - INFO - Processing image from S3...
2025-09-24 17:40:56,344 - INFO - 

AOYIL346IKT06LKO6D2R.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:40:56,344 - INFO - 

✓ Saved result: output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:40:56,410 - INFO - Page 1: Extracted 3181 characters, 88 lines from 645db968_bol_HEMTH4BHCWXWE044I0SK_dc66828b_page_001.pdf
2025-09-24 17:40:56,410 - INFO - Successfully processed page 1
2025-09-24 17:40:56,608 - INFO - Page 2: Extracted 3230 characters, 90 lines from 645db968_bol_HEMTH4BHCWXWE044I0SK_dc66828b_page_002.pdf
2025-09-24 17:40:56,609 - INFO - Successfully processed page 2
2025-09-24 17:40:56,609 - INFO - Combined 2 pages into final text
2025-09-24 17:40:56,609 - INFO - Text validation for 645db968_bol_HEMTH4BHCWXWE044I0SK: 6447 characters, 2 pages
2025-09-24 17:40:56,609 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:56,609 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:56,674 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bc130a2b_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:40:56,687 - INFO - 

TQSZJLFFGGKB4NFN12RD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:40:56,687 - INFO - 

✓ Saved result: output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:40:57,076 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/151658bd_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:40:57,118 - INFO - 

GKYUUU2YTZ1YTPGXHO51.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:40:57,118 - INFO - 

✓ Saved result: output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:40:57,326 - INFO - Page 1: Extracted 2991 characters, 109 lines from a00c9286_bol_K88AFHJU4HIQUZYTMOF4_32e195a9_page_001.pdf
2025-09-24 17:40:57,326 - INFO - Successfully processed page 1
2025-09-24 17:40:57,327 - INFO - Combined 1 pages into final text
2025-09-24 17:40:57,327 - INFO - Text validation for a00c9286_bol_K88AFHJU4HIQUZYTMOF4: 3008 characters, 1 pages
2025-09-24 17:40:57,328 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:57,328 - INFO - Downloaded PDF size: 1.3 MB
2025-09-24 17:40:57,328 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:57,328 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:57,330 - INFO - Splitting PDF 8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R into 1 pages
2025-09-24 17:40:57,333 - INFO - Split PDF into 1 pages
2025-09-24 17:40:57,333 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:57,333 - INFO - Expected pages: [1]
2025-09-24 17:40:57,461 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf73db02_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:40:57,492 - INFO - 

I6IM526GOVKAWH0B5WKE.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:40:57,493 - INFO - 

✓ Saved result: output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:40:57,719 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be1d50f5-6f92-48dc-bc65-ce05c86ade0e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:57 GMT', 'content-type': 'application/json', 'content-length': '841', 'connection': 'keep-alive', 'x-amzn-requestid': 'be1d50f5-6f92-48dc-bc65-ce05c86ade0e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to decide doc_type.\n\nContent: It\'s a "WAYBILL" from Watchpoint Logistics. It contains shipping details, pickup, delivery, etc. This looks like a Bill of Lading. The header says "WAYBILL". The keywords: Waybill, Ship From, Ship To, Bill To, Carrier acknowledges receipt, etc. That matches bol (Bill of Lading). There\'s no invoice, no POD. It\'s the bill of lading. So doc_type = bol.\n\nReturn via tool. Need to output array with object: page_no:1, doc_type:"bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3530, 'outputTokens': 149, 'totalTokens': 3679}, 'metrics': {'latencyMs': 846}}
2025-09-24 17:40:57,721 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:57,842 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a60317f4_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:40:57,866 - INFO - 

KK3PKFLS4ROTEZ7LOMMD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 17:40:57,867 - INFO - 

✓ Saved result: output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:40:58,080 - INFO - S3 Image temp/bca37a5c_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg: Extracted 515 characters, 39 lines
2025-09-24 17:40:58,080 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:58,080 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:58,205 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ecf3d13d_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:40:58,231 - INFO - 

S92OW12RYFF5G3READPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:40:58,231 - INFO - 

✓ Saved result: output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:40:58,537 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '10f6c4fc-4585-41a3-8891-3166a7d02686', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:58 GMT', 'content-type': 'application/json', 'content-length': '713', 'connection': 'keep-alive', 'x-amzn-requestid': '10f6c4fc-4585-41a3-8891-3166a7d02686'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Both pages are similar: They are Bill of Lading documents. So doc_type=bol. Check if continuation? Page 2 content same header. Likely two pages of a multi-page BOL. Page 1 is first page, page 2 continuation. Should assign bol for both. Ensure each page object. Output using tool call classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4137, 'outputTokens': 111, 'totalTokens': 4248}, 'metrics': {'latencyMs': 782}}
2025-09-24 17:40:58,537 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/645db968_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:58,555 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/afd08e2e_S92OW12RYFF5G3READPW.pdf
2025-09-24 17:40:58,599 - INFO - 

bol_IJW9IJUOROYY1SWO0B17.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:40:58,599 - INFO - 

✓ Saved result: output/run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:40:58,937 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1fb5e8c7_bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:40:58,977 - INFO - Downloaded PDF size: 3.3 MB
2025-09-24 17:40:58,978 - INFO - Splitting PDF into individual pages...
2025-09-24 17:40:58,981 - INFO - Splitting PDF a4c2cadc_bol_PK5AIZFCER7DUFL15N01 into 1 pages
2025-09-24 17:40:58,990 - INFO - Split PDF into 1 pages
2025-09-24 17:40:58,990 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:40:58,991 - INFO - Expected pages: [1]
2025-09-24 17:40:59,028 - INFO - 

bol_HEMTH4BHCWXWE044I0SK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:40:59,028 - INFO - 

✓ Saved result: output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:40:59,303 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '685c0195-b0d3-4cf2-9077-a68d53383dbc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:59 GMT', 'content-type': 'application/json', 'content-length': '628', 'connection': 'keep-alive', 'x-amzn-requestid': '685c0195-b0d3-4cf2-9077-a68d53383dbc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It\'s a Bill of Lading. The text includes "BILL OF LADING" header. So doc_type is "bol". According to instruction, for each page output exactly one object. So we output JSON calling the tool classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3287, 'outputTokens': 90, 'totalTokens': 3377}, 'metrics': {'latencyMs': 754}}
2025-09-24 17:40:59,306 - INFO - S3 Image temp/95bbe640_pack_list_P6RAGXD27RW4QUXN96TH.jpeg: Extracted 456 characters, 31 lines
2025-09-24 17:40:59,306 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a00c9286_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:40:59,307 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:59,307 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:59,348 - INFO - S3 Image temp/a7ab27a0_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg: Extracted 454 characters, 31 lines
2025-09-24 17:40:59,349 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:59,349 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:59,351 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/645db968_bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:40:59,382 - INFO - 

bol_K88AFHJU4HIQUZYTMOF4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:40:59,382 - INFO - 

✓ Saved result: output/run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:40:59,604 - INFO - Page 1: Extracted 1608 characters, 74 lines from 6ffe7abe_bol_P2EZG64EJI98NT69TSAL_6893cfdc_page_001.pdf
2025-09-24 17:40:59,604 - INFO - Successfully processed page 1
2025-09-24 17:40:59,604 - INFO - Combined 1 pages into final text
2025-09-24 17:40:59,605 - INFO - Text validation for 6ffe7abe_bol_P2EZG64EJI98NT69TSAL: 1625 characters, 1 pages
2025-09-24 17:40:59,605 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:40:59,605 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:40:59,719 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a00c9286_bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:41:00,048 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '633f702e-e397-43dd-bf5e-82312ce974c0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:10:59 GMT', 'content-type': 'application/json', 'content-length': '785', 'connection': 'keep-alive', 'x-amzn-requestid': '633f702e-e397-43dd-bf5e-82312ce974c0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. This looks like a Packing Slip. Packing Slip is similar to Packing List? The keyword: "Packing Slip". The doc types list includes "pack_list" for Packing List. "Packing Slip" is similar content: shipment details, items, shipping, etc. It\'s a shipping document. The classification likely "pack_list". The other categories: invoice, bol, etc. It\'s not invoice, not bol. So pack_list. Ensure we output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2695, 'outputTokens': 121, 'totalTokens': 2816}, 'metrics': {'latencyMs': 786}}
2025-09-24 17:41:00,049 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bca37a5c_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:41:00,065 - INFO - 

pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:41:00,065 - INFO - 

✓ Saved result: output/run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:41:00,386 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bca37a5c_pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:41:01,088 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cdade2eb-4286-444c-b49a-5fbb63457230', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:11:00 GMT', 'content-type': 'application/json', 'content-length': '619', 'connection': 'keep-alive', 'x-amzn-requestid': 'cdade2eb-4286-444c-b49a-5fbb63457230'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page. It\'s a packing slip, likely a "pack_list" or "packing list". The header shows "Packing Slip". Keywords: Packing Slip, Pack List. So doc_type: pack_list. No continuation. So output array with one object: {page_no:1, doc_type:"pack_list"}. Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2660, 'outputTokens': 93, 'totalTokens': 2753}, 'metrics': {'latencyMs': 670}}
2025-09-24 17:41:01,088 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a7ab27a0_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:41:01,107 - INFO - 

pack_list_YC2ROS9NUB31VO0ABEV5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:41:01,107 - INFO - 

✓ Saved result: output/run1_pack_list_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:41:01,227 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e3d396bf-0071-40b6-a244-0ca1bc1ea2d2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:11:01 GMT', 'content-type': 'application/json', 'content-length': '797', 'connection': 'keep-alive', 'x-amzn-requestid': 'e3d396bf-0071-40b6-a244-0ca1bc1ea2d2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We examine content: "Packing Slip" header. It\'s a packing slip, similar to packing list. The enum has "pack_list" for Packing List. The header is "Packing Slip". The keywords: Packing List, Pack List, Contents, Qty, Item Description. We see "Part Number", "Order Quantity", "Ship Quantity". So it\'s a pack list. So output doc_type: pack_list. Single page. Output with page_no:1. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2659, 'outputTokens': 130, 'totalTokens': 2789}, 'metrics': {'latencyMs': 760}}
2025-09-24 17:41:01,228 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/95bbe640_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:41:01,340 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a1ef55b3-f7af-4eef-8ce0-384e23f8b84d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:11:01 GMT', 'content-type': 'application/json', 'content-length': '562', 'connection': 'keep-alive', 'x-amzn-requestid': 'a1ef55b3-f7af-4eef-8ce0-384e23f8b84d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s a straight bill of lading - short form. So doc_type should be bol. We need to output via tool call classify_logistics_doc_type with documents array containing object: page_no:1, doc_type:"bol".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2966, 'outputTokens': 75, 'totalTokens': 3041}, 'metrics': {'latencyMs': 554}}
2025-09-24 17:41:01,340 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6ffe7abe_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:41:01,432 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a7ab27a0_pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:41:01,445 - INFO - 

pack_list_P6RAGXD27RW4QUXN96TH.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 17:41:01,445 - INFO - 

✓ Saved result: output/run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:41:01,790 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/95bbe640_pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:41:01,815 - INFO - 

bol_P2EZG64EJI98NT69TSAL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:41:01,815 - INFO - 

✓ Saved result: output/run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:41:02,139 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ffe7abe_bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:41:02,638 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '218945bf-2713-4c03-8003-3d76842d5bff', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:11:02 GMT', 'content-type': 'application/json', 'content-length': '5846', 'connection': 'keep-alive', 'x-amzn-requestid': '218945bf-2713-4c03-8003-3d76842d5bff'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with 3 pages. The user is providing text for each page. We must classify each page type.\n\nWe need to inspect each page content.\n\nPage 1 appears to be a Bill of Lading (BOL) with header "BILL OF LADING". It contains many BOL fields, addresses, and shipping info. So doc_type should be bol.\n\nPage 2 appears to be some freight bill information, maybe a BOL? The text includes "FREIGHT BILL NO.", "BILL TO", etc. It also includes "PICK UP DATE", "SHIPPING COORDINATOR". It might be the same BOL but split across pages? Actually page2 is the continuation of page1? Let\'s examine page1: It ended with "FRANK HAMPIN ... 92225 ... 9/19/25". That might be signature page. Page2 is a new page with "ABF www.arcb.com/abf Freight ...". It looks like a separate page maybe the same BOL but with additional details. It includes "FREIGHT BILL NO.", "SHIPPER\'S NO. RYC011767547", etc. It might be part of a combined document or a separate page part of the same BOL. The page numbering shows "PAGE 1 OF 3" etc. Actually page2 shows "PAGE 1 OF 3" at top, page3 shows "PAGE 3 OF 3". Wait page2 has "PAGE 1 OF 3" and page3 "PAGE 3 OF 3". So these are 3 pages of a single document. So page1 is "PAGE 1 of 1" but page2 and page3 show "PAGE 1 OF 3" and "PAGE 3 OF 3". That suggests page1 may be a separate cover page? Actually page1 shows "BILL OF LADING" and "PAGE 1 of 1". That might be a separate document (BOL) but page2 and page3 show "PAGE 1 OF 3" and "PAGE 3 OF 3" with details. But the content of page2 includes "FREIGHT BILL NO." etc. It may be a freight bill or a BOL. But the presence of "BILL OF LADING" header only on page1 suggests page1 is a BOL. Pages 2 and 3 have no "BILL OF LADING" header, but they have freight bill details. They may be a freight bill (carrier invoice) or a freight bill. Actually "FREIGHT BILL NO." indicates it\'s a freight bill. But freight bill is a carrier invoice. But the content includes charges and "PAY THIS AMOUNT", "COD AMOUNT". That is an invoice. The doc type would be invoice. But we see also "BOL". The question: Are pages 2 and 3 part of the same document as page1? The header "PAGE 1 of 3" indicates they belong to a document covering 3 pages. But page1 says "PAGE 1 of 1". That suggests page1 is separate. But the presence of "BILL OF LADING" header only on page1 might be a separate BOL. The pages 2 and 3 might be a freight bill for the same shipment. But the content includes "FREIGHT BILL NO.", "BILL TO", "CODE TO", "P.O. NO.", etc. They look like a freight bill (invoice). So we might classify page2 and page3 as invoice. However, we also need to check if the page is continuation. The rule: if page starts with "continued" or "page x of y" or similar, then it\'s continuation and assign same doc_type as previous page. But here page2 starts with "ABF ... PAGE 1 OF 3". That indicates it\'s the first page of a 3-page document. It does not indicate continuation from previous page. So it\'s a separate document. So page1 is a BOL. Page2 and page3 are separate documents, each a freight bill (invoice). But we need to decide classification for each page.\n\nBut we must read carefully: The text includes "FREIGHT BILL NO." which is typical of freight bill (invoice). The other doc type for freight bill would be "invoice". So page2 and page3 should be invoice.\n\nWait, but there is also "BILL OF LADING" header on page1. So page1 is BOL. So we output 3 objects: page 1 bol, page 2 invoice, page 3 invoice.\n\nBut check if pages 2 and 3 are identical content? They are similar but differ in some details. They likely belong to the same freight bill, but each page is separate. But per instruction, each page gets its own doc_type, but if it\'s a multi-page single document, each page gets same doc_type repeated. But here pages 2 and 3 are separate documents? Actually they appear to be part of the same freight bill spanning 3 pages, but the header "PAGE 1 OF 3" and "PAGE 3 OF 3" suggests they are part of a multi-page document. But page1 is separate with "PAGE 1 of 1". So page1 is BOL; page2 and page3 are part of a freight bill. But page2 is "PAGE 1 OF 3", page3 "PAGE 3 OF 3". That indicates page2 is the first page of that freight bill. So page3 is the third page of that freight bill. So we treat page2 and page3 as invoice. But we also need to check if page3 is continuation of page2. Page3 starts with "ABF" and "PAGE 3 OF 3". That indicates it\'s a continuation. But rule says if page is continuation of previous page/doc, assign same doc_type as previous page. So page3 should be same doc_type as page2, which is invoice. So we output page2 invoice, page3 invoice.\n\nNow we need to output via tool call classify_logistics_doc_type. Provide an array of objects: each with page_no and doc_type.\n\nWe must include all pages in ascending order. So:\n\npage 1: bol\npage 2: invoice\npage 3: invoice\n\nWe must output JSON in the tool. The function expects:\n\n{\n  documents: [\n    { page_no: number, doc_type: "invoice" | ... }\n  ]\n}\n\nWe need to match the doc_type enum exactly.\n\nThus:\n\n{\n  documents: [\n    { page_no: 1, doc_type: "bol" },\n    { page_no: 2, doc_type: "invoice" },\n    { page_no: 3, doc_type: "invoice" }\n  ]\n}\n\nLet\'s ensure the doc_type string matches. For BOL, it\'s "bol". For invoice, it\'s "invoice". Good.\n\nWe need to call the function.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4624, 'outputTokens': 1479, 'totalTokens': 6103}, 'metrics': {'latencyMs': 6012}}
2025-09-24 17:41:02,638 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7d4e55ed_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:41:02,702 - INFO - 

bol_DV1B199A0TJGUQ3FETIU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 17:41:02,702 - INFO - 

✓ Saved result: output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:03,025 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7d4e55ed_bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:41:04,636 - INFO - Page 1: Extracted 2992 characters, 114 lines from 8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R_e1078f39_page_001.pdf
2025-09-24 17:41:04,636 - INFO - Successfully processed page 1
2025-09-24 17:41:04,637 - INFO - Combined 1 pages into final text
2025-09-24 17:41:04,637 - INFO - Text validation for 8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R: 3009 characters, 1 pages
2025-09-24 17:41:04,637 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:41:04,637 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:41:06,485 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '38706435-5a8c-4ed9-a787-6a67a738cf42', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:11:06 GMT', 'content-type': 'application/json', 'content-length': '639', 'connection': 'keep-alive', 'x-amzn-requestid': '38706435-5a8c-4ed9-a787-6a67a738cf42'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. Determine doc type. Text shows "BILL OF LADING" header. Also many details typical of BOL. So doc_type = bol. Output a JSON array with one object with page_no:1 doc_type:"bol". Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_qsiGpe-RSYypz7kYwt8Zsg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3287, 'outputTokens': 78, 'totalTokens': 3365}, 'metrics': {'latencyMs': 640}}
2025-09-24 17:41:06,486 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:41:06,516 - INFO - 

bol_RCNPMJFRSZTXQWBGLB9R.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:41:06,516 - INFO - 

✓ Saved result: output/run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:41:06,861 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8a7c64bf_bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:41:08,110 - INFO - Page 1: Extracted 3032 characters, 118 lines from a4c2cadc_bol_PK5AIZFCER7DUFL15N01_604dcc01_page_001.pdf
2025-09-24 17:41:08,111 - INFO - Successfully processed page 1
2025-09-24 17:41:08,111 - INFO - Combined 1 pages into final text
2025-09-24 17:41:08,111 - INFO - Text validation for a4c2cadc_bol_PK5AIZFCER7DUFL15N01: 3049 characters, 1 pages
2025-09-24 17:41:08,113 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:41:08,113 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:41:09,902 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b80c5f58-484b-4bef-bca0-9df99b5f170b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:11:09 GMT', 'content-type': 'application/json', 'content-length': '595', 'connection': 'keep-alive', 'x-amzn-requestid': 'b80c5f58-484b-4bef-bca0-9df99b5f170b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. It is a Bill of Lading. Keywords: "BILL OF LADING", "BOL #", etc. So doc_type: bol. We need to output via tool call classify_logistics_doc_type with documents array: one object page_no:1, doc_type:"bol". Ensure correct type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3305, 'outputTokens': 91, 'totalTokens': 3396}, 'metrics': {'latencyMs': 583}}
2025-09-24 17:41:09,903 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a4c2cadc_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:41:09,947 - INFO - 

bol_PK5AIZFCER7DUFL15N01.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 17:41:09,947 - INFO - 

✓ Saved result: output/run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:41:10,312 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a4c2cadc_bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:41:10,314 - INFO - 
📊 Processing Summary:
2025-09-24 17:41:10,314 - INFO -    Total files: 16
2025-09-24 17:41:10,314 - INFO -    Successful: 16
2025-09-24 17:41:10,314 - INFO -    Failed: 0
2025-09-24 17:41:10,314 - INFO -    Duration: 37.06 seconds
2025-09-24 17:41:10,314 - INFO -    Output directory: output
2025-09-24 17:41:10,314 - INFO - 
📋 Successfully Processed Files:
2025-09-24 17:41:10,314 - INFO -    📄 AOYIL346IKT06LKO6D2R.jpg: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:41:10,315 - INFO -    📄 GKYUUU2YTZ1YTPGXHO51.pdf: {"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 17:41:10,315 - INFO -    📄 I6IM526GOVKAWH0B5WKE.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:41:10,315 - INFO -    📄 KK3PKFLS4ROTEZ7LOMMD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:41:10,315 - INFO -    📄 S92OW12RYFF5G3READPW.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 17:41:10,315 - INFO -    📄 TQSZJLFFGGKB4NFN12RD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 17:41:10,315 - INFO -    📄 bol_DV1B199A0TJGUQ3FETIU.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 17:41:10,316 - INFO -    📄 bol_HEMTH4BHCWXWE044I0SK.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 17:41:10,316 - INFO -    📄 bol_IJW9IJUOROYY1SWO0B17.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:41:10,316 - INFO -    📄 bol_K88AFHJU4HIQUZYTMOF4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:41:10,316 - INFO -    📄 bol_P2EZG64EJI98NT69TSAL.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:41:10,316 - INFO -    📄 bol_PK5AIZFCER7DUFL15N01.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:41:10,316 - INFO -    📄 bol_RCNPMJFRSZTXQWBGLB9R.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 17:41:10,316 - INFO -    📄 pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:41:10,317 - INFO -    📄 pack_list_P6RAGXD27RW4QUXN96TH.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:41:10,317 - INFO -    📄 pack_list_YC2ROS9NUB31VO0ABEV5.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 17:41:10,318 - INFO - 
============================================================================================================================================
2025-09-24 17:41:10,318 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 17:41:10,318 - INFO - ============================================================================================================================================
2025-09-24 17:41:10,319 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 17:41:10,319 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:41:10,319 - INFO - AOYIL346IKT06LKO6D2R.jpg                           1      pod                                      run1_AOYIL346IKT06LKO6D2R.json                                                  
2025-09-24 17:41:10,319 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg
2025-09-24 17:41:10,319 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:41:10,319 - INFO - 
2025-09-24 17:41:10,319 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           1      pod                                      run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:41:10,319 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:41:10,320 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:41:10,320 - INFO - 
2025-09-24 17:41:10,320 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           2      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:41:10,320 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:41:10,320 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:41:10,320 - INFO - 
2025-09-24 17:41:10,320 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           3      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 17:41:10,320 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 17:41:10,320 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:41:10,321 - INFO - 
2025-09-24 17:41:10,321 - INFO - I6IM526GOVKAWH0B5WKE.pdf                           1      pod                                      run1_I6IM526GOVKAWH0B5WKE.json                                                  
2025-09-24 17:41:10,321 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 17:41:10,321 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:41:10,321 - INFO - 
2025-09-24 17:41:10,321 - INFO - KK3PKFLS4ROTEZ7LOMMD.pdf                           1      pod                                      run1_KK3PKFLS4ROTEZ7LOMMD.json                                                  
2025-09-24 17:41:10,321 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 17:41:10,321 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:41:10,321 - INFO - 
2025-09-24 17:41:10,321 - INFO - S92OW12RYFF5G3READPW.pdf                           1      invoice                                  run1_S92OW12RYFF5G3READPW.json                                                  
2025-09-24 17:41:10,321 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf
2025-09-24 17:41:10,321 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:41:10,321 - INFO - 
2025-09-24 17:41:10,321 - INFO - TQSZJLFFGGKB4NFN12RD.pdf                           1      pod                                      run1_TQSZJLFFGGKB4NFN12RD.json                                                  
2025-09-24 17:41:10,321 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 17:41:10,322 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:41:10,322 - INFO - 
2025-09-24 17:41:10,322 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       1      bol                                      run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:41:10,322 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:41:10,322 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:10,322 - INFO - 
2025-09-24 17:41:10,322 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       2      invoice                                  run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:41:10,322 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:41:10,322 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:10,322 - INFO - 
2025-09-24 17:41:10,322 - INFO - bol_DV1B199A0TJGUQ3FETIU.pdf                       3      invoice                                  run1_bol_DV1B199A0TJGUQ3FETIU.json                                              
2025-09-24 17:41:10,322 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 17:41:10,322 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:10,322 - INFO - 
2025-09-24 17:41:10,322 - INFO - bol_HEMTH4BHCWXWE044I0SK.pdf                       1      bol                                      run1_bol_HEMTH4BHCWXWE044I0SK.json                                              
2025-09-24 17:41:10,322 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:41:10,322 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:41:10,322 - INFO - 
2025-09-24 17:41:10,322 - INFO - bol_HEMTH4BHCWXWE044I0SK.pdf                       2      bol                                      run1_bol_HEMTH4BHCWXWE044I0SK.json                                              
2025-09-24 17:41:10,322 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - bol_IJW9IJUOROYY1SWO0B17.pdf                       1      bol                                      run1_bol_IJW9IJUOROYY1SWO0B17.json                                              
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - bol_K88AFHJU4HIQUZYTMOF4.pdf                       1      bol                                      run1_bol_K88AFHJU4HIQUZYTMOF4.json                                              
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - bol_P2EZG64EJI98NT69TSAL.pdf                       1      bol                                      run1_bol_P2EZG64EJI98NT69TSAL.json                                              
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - bol_PK5AIZFCER7DUFL15N01.pdf                       1      bol                                      run1_bol_PK5AIZFCER7DUFL15N01.json                                              
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - bol_RCNPMJFRSZTXQWBGLB9R.pdf                       1      bol                                      run1_bol_RCNPMJFRSZTXQWBGLB9R.json                                              
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg                1      pack_list                                run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json                                        
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - pack_list_P6RAGXD27RW4QUXN96TH.jpeg                1      pack_list                                run1_pack_list_P6RAGXD27RW4QUXN96TH.json                                        
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - pack_list_YC2ROS9NUB31VO0ABEV5.jpeg                1      pack_list                                run1_pack_list_YC2ROS9NUB31VO0ABEV5.json                                        
2025-09-24 17:41:10,323 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 17:41:10,323 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_pack_list_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:41:10,323 - INFO - 
2025-09-24 17:41:10,323 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:41:10,323 - INFO - Total entries: 21
2025-09-24 17:41:10,323 - INFO - ============================================================================================================================================
2025-09-24 17:41:10,323 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 17:41:10,323 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:41:10,323 - INFO -   1. AOYIL346IKT06LKO6D2R.jpg            Page 1   → pod             | run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 17:41:10,323 - INFO -   2. GKYUUU2YTZ1YTPGXHO51.pdf            Page 1   → pod             | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:41:10,323 - INFO -   3. GKYUUU2YTZ1YTPGXHO51.pdf            Page 2   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:41:10,323 - INFO -   4. GKYUUU2YTZ1YTPGXHO51.pdf            Page 3   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 17:41:10,323 - INFO -   5. I6IM526GOVKAWH0B5WKE.pdf            Page 1   → pod             | run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 17:41:10,323 - INFO -   6. KK3PKFLS4ROTEZ7LOMMD.pdf            Page 1   → pod             | run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 17:41:10,323 - INFO -   7. S92OW12RYFF5G3READPW.pdf            Page 1   → invoice         | run1_S92OW12RYFF5G3READPW.json
2025-09-24 17:41:10,323 - INFO -   8. TQSZJLFFGGKB4NFN12RD.pdf            Page 1   → pod             | run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 17:41:10,323 - INFO -   9. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 1   → bol             | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:10,324 - INFO -  10. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 2   → invoice         | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:10,324 - INFO -  11. bol_DV1B199A0TJGUQ3FETIU.pdf        Page 3   → invoice         | run1_bol_DV1B199A0TJGUQ3FETIU.json
2025-09-24 17:41:10,324 - INFO -  12. bol_HEMTH4BHCWXWE044I0SK.pdf        Page 1   → bol             | run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:41:10,324 - INFO -  13. bol_HEMTH4BHCWXWE044I0SK.pdf        Page 2   → bol             | run1_bol_HEMTH4BHCWXWE044I0SK.json
2025-09-24 17:41:10,324 - INFO -  14. bol_IJW9IJUOROYY1SWO0B17.pdf        Page 1   → bol             | run1_bol_IJW9IJUOROYY1SWO0B17.json
2025-09-24 17:41:10,324 - INFO -  15. bol_K88AFHJU4HIQUZYTMOF4.pdf        Page 1   → bol             | run1_bol_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 17:41:10,324 - INFO -  16. bol_P2EZG64EJI98NT69TSAL.pdf        Page 1   → bol             | run1_bol_P2EZG64EJI98NT69TSAL.json
2025-09-24 17:41:10,324 - INFO -  17. bol_PK5AIZFCER7DUFL15N01.pdf        Page 1   → bol             | run1_bol_PK5AIZFCER7DUFL15N01.json
2025-09-24 17:41:10,324 - INFO -  18. bol_RCNPMJFRSZTXQWBGLB9R.pdf        Page 1   → bol             | run1_bol_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 17:41:10,324 - INFO -  19. pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg Page 1   → pack_list       | run1_pack_list_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 17:41:10,324 - INFO -  20. pack_list_P6RAGXD27RW4QUXN96TH.jpeg Page 1   → pack_list       | run1_pack_list_P6RAGXD27RW4QUXN96TH.json
2025-09-24 17:41:10,324 - INFO -  21. pack_list_YC2ROS9NUB31VO0ABEV5.jpeg Page 1   → pack_list       | run1_pack_list_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 17:41:10,324 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:41:10,324 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 37.056751, 'processed_files': [{'filename': 'AOYIL346IKT06LKO6D2R.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg'}, {'filename': 'GKYUUU2YTZ1YTPGXHO51.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf'}, {'filename': 'I6IM526GOVKAWH0B5WKE.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf'}, {'filename': 'KK3PKFLS4ROTEZ7LOMMD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf'}, {'filename': 'S92OW12RYFF5G3READPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf'}, {'filename': 'TQSZJLFFGGKB4NFN12RD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf'}, {'filename': 'bol_DV1B199A0TJGUQ3FETIU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_DV1B199A0TJGUQ3FETIU.pdf'}, {'filename': 'bol_HEMTH4BHCWXWE044I0SK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_HEMTH4BHCWXWE044I0SK.pdf'}, {'filename': 'bol_IJW9IJUOROYY1SWO0B17.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_IJW9IJUOROYY1SWO0B17.pdf'}, {'filename': 'bol_K88AFHJU4HIQUZYTMOF4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_K88AFHJU4HIQUZYTMOF4.pdf'}, {'filename': 'bol_P2EZG64EJI98NT69TSAL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_P2EZG64EJI98NT69TSAL.pdf'}, {'filename': 'bol_PK5AIZFCER7DUFL15N01.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_PK5AIZFCER7DUFL15N01.pdf'}, {'filename': 'bol_RCNPMJFRSZTXQWBGLB9R.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/bol_RCNPMJFRSZTXQWBGLB9R.pdf'}, {'filename': 'pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_KUYJRWRDFCC2FB7SKI9T.jpeg'}, {'filename': 'pack_list_P6RAGXD27RW4QUXN96TH.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_P6RAGXD27RW4QUXN96TH.jpeg'}, {'filename': 'pack_list_YC2ROS9NUB31VO0ABEV5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/pack_list_YC2ROS9NUB31VO0ABEV5.jpeg'}]}
