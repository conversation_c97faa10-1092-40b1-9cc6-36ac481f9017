#!/usr/bin/env python3
"""
Debug De<PERSON> Script

This script demonstrates how to use VS Code debugging with test_classification.py
and external dependencies. Set breakpoints in this file and external modules
to see debugging in action.

Usage:
1. Open this file in VS Code
2. Set breakpoints at the marked locations
3. Run this script using VS Code debugger
4. Step through the code and observe variable values
"""

import sys
import os
import asyncio
from pathlib import Path

# Add paths (same as test_classification.py)
sys.path.append(r"/home/<USER>/Documents/repositories/document-data-extraction")
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import test_classification modules
try:
    from scripts.test_classification import FileProcessor, S3Manager
    print("✅ Successfully imported test_classification modules")
except ImportError as e:
    print(f"❌ Failed to import: {e}")
    sys.exit(1)


def demo_local_debugging():
    """Demonstrate local debugging features."""
    print("\n🔍 Demo: Local Debugging")
    print("=" * 40)
    
    # SET BREAKPOINT HERE - Line 1
    local_variable = "This is a local variable for debugging"
    print(f"Local variable: {local_variable}")
    
    # SET BREAKPOINT HERE - Line 2
    numbers = [1, 2, 3, 4, 5]
    total = sum(numbers)
    print(f"Sum of {numbers} = {total}")
    
    # SET BREAKPOINT HERE - Line 3
    result = {"status": "success", "total": total, "numbers": numbers}
    return result


def demo_class_debugging():
    """Demonstrate debugging with classes from test_classification.py."""
    print("\n🔍 Demo: Class Debugging")
    print("=" * 40)
    
    # SET BREAKPOINT HERE - Line 4
    print("Creating S3Manager instance...")
    s3_manager = S3Manager()
    
    # SET BREAKPOINT HERE - Line 5
    # Inspect s3_manager properties in the debugger
    print(f"S3 Bucket: {s3_manager.bucket_name}")
    print(f"Temp Prefix: {s3_manager.temp_prefix}")
    
    # SET BREAKPOINT HERE - Line 6
    print("Creating FileProcessor instance...")
    processor = FileProcessor("Augment_test/debug_output", "Augment_test/debug_logs")
    
    # SET BREAKPOINT HERE - Line 7
    # Inspect processor properties in the debugger
    print(f"Output Directory: {processor.output_dir}")
    print(f"Processed Count: {processor.processed_count}")
    
    return {"s3_manager": s3_manager, "processor": processor}


async def demo_async_debugging():
    """Demonstrate async debugging."""
    print("\n🔍 Demo: Async Debugging")
    print("=" * 40)
    
    # SET BREAKPOINT HERE - Line 8
    print("Starting async operations...")
    
    # SET BREAKPOINT HERE - Line 9
    await asyncio.sleep(0.1)
    async_data = []
    
    # SET BREAKPOINT HERE - Line 10
    for i in range(3):
        await asyncio.sleep(0.05)
        item = f"async_item_{i}"
        async_data.append(item)
        print(f"Added: {item}")
        # SET BREAKPOINT HERE - Line 11 (inside loop)
    
    # SET BREAKPOINT HERE - Line 12
    print(f"Async data collected: {async_data}")
    return async_data


def demo_error_debugging():
    """Demonstrate error debugging (intentional error)."""
    print("\n🔍 Demo: Error Debugging")
    print("=" * 40)
    
    try:
        # SET BREAKPOINT HERE - Line 13
        print("About to cause an intentional error...")
        
        # SET BREAKPOINT HERE - Line 14
        # This will cause a ZeroDivisionError
        result = 10 / 0
        
    except ZeroDivisionError as e:
        # SET BREAKPOINT HERE - Line 15
        print(f"Caught expected error: {e}")
        
        # In VS Code debugger, you can inspect the exception
        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "error_occurred": True
        }
        
        # SET BREAKPOINT HERE - Line 16
        return error_info


def demo_external_module_simulation():
    """Simulate calling external modules (for breakpoint testing)."""
    print("\n🔍 Demo: External Module Simulation")
    print("=" * 40)
    
    # SET BREAKPOINT HERE - Line 17
    print("Simulating external module calls...")
    
    # This simulates the pattern used in test_classification.py
    # where external modules are called
    
    # SET BREAKPOINT HERE - Line 18
    try:
        # Import external module (if available)
        sys.path.insert(0, r"/home/<USER>/Documents/repositories/document-data-extraction")
        
        # SET BREAKPOINT HERE - Line 19
        # Try to import the classification module
        # (Set breakpoints in the external module to test external debugging)
        from app.llm.classification import main as llm_classification
        
        # SET BREAKPOINT HERE - Line 20
        print("✅ External module imported successfully")
        print("   You can now set breakpoints in the external module!")
        print("   File: /home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py")
        
        # Note: We don't actually call it to avoid S3 errors
        # But you can set breakpoints in the external file and they will work
        
        return {"external_available": True, "module": "app.llm.classification"}
        
    except ImportError as e:
        # SET BREAKPOINT HERE - Line 21
        print(f"⚠ External module not available: {e}")
        return {"external_available": False, "error": str(e)}


def main():
    """Main demo function."""
    print("🐛 VS Code Debug Demo")
    print("=" * 50)
    print("Instructions:")
    print("1. Set breakpoints at the marked locations (search for 'SET BREAKPOINT HERE')")
    print("2. Run this script using VS Code debugger (F5)")
    print("3. Step through the code using F10 (step over) and F11 (step into)")
    print("4. Inspect variables in the Variables panel")
    print("5. Use the Debug Console to evaluate expressions")
    print("=" * 50)
    
    # Demo 1: Local debugging
    local_result = demo_local_debugging()
    
    # Demo 2: Class debugging
    class_result = demo_class_debugging()
    
    # Demo 3: Async debugging
    async_result = asyncio.run(demo_async_debugging())
    
    # Demo 4: Error debugging
    error_result = demo_error_debugging()
    
    # Demo 5: External module simulation
    external_result = demo_external_module_simulation()
    
    # Final summary
    print("\n📊 Demo Summary:")
    print(f"   Local result: {local_result}")
    print(f"   Class result keys: {list(class_result.keys())}")
    print(f"   Async result: {async_result}")
    print(f"   Error result: {error_result}")
    print(f"   External result: {external_result}")
    
    print("\n✅ Debug demo completed!")
    print("   If you were able to hit all breakpoints and inspect variables,")
    print("   then your VS Code debugging setup is working perfectly!")
    
    return {
        "local": local_result,
        "class": class_result,
        "async": async_result,
        "error": error_result,
        "external": external_result
    }


if __name__ == "__main__":
    # SET BREAKPOINT HERE - Main Entry Point
    print("🚀 Starting debug demo...")
    
    try:
        results = main()
        print(f"\n🎉 All demos completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during demo: {e}")
        
        # Demonstrate post-mortem debugging
        import traceback
        traceback.print_exc()
        
        # Uncomment the next line to enter debugger on error
        # import pdb; pdb.post_mortem()
        
        raise
