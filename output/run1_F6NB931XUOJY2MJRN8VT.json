{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.7812409996986389, "Height": 0.9494072198867798, "Left": 0.09244085103273392, "Top": 0.024357901886105537}, "Polygon": [{"X": 0.8394057154655457, "Y": 0.04444783180952072}, {"X": 0.8736818432807922, "Y": 0.9225624799728394}, {"X": 0.09244085103273392, "Y": 0.9737651348114014}, {"X": 0.10498303174972534, "Y": 0.024357901886105537}]}, "Id": "3ad785e2-9ad7-4a5d-926e-3b98b9b0b29a", "Relationships": [{"Type": "CHILD", "Ids": ["b73070f7-ca4e-40e3-a4ff-f15af39c1a36", "42e06130-ac6a-421a-a24c-279ec74b16c7", "3350a850-3d87-4942-8efc-796d0f16099d", "d8876a1f-b7cc-4f67-b9b6-2d5b6fce5677", "a92ce109-4b97-405d-9889-d8542cacd012", "9e685c35-6f46-4fb6-aa37-c2064d10a338", "f61de7c9-48ea-4f47-9292-3dddc842a8e9", "b4ed7088-7b62-49f2-8d9a-1e2ac48c86cb", "a42dcd9f-57f9-494e-937d-53a0d1c91d2d", "c8209700-b773-496b-9966-402f08da387c", "648e1321-9170-44c1-9bdb-fb1425a9892c", "7efd6912-b7ad-4ede-b9d7-b1a4fa8c7713", "8eb1f06d-2052-4f3a-a141-653aca3a36e1", "dae9e5cd-2002-47a7-ae97-56e7ca30df0d", "35177162-86f2-4c86-98c0-80ea0c2df68a", "5ae44a79-641f-40e4-af22-9eb4f46055b1", "215ac39f-5944-40ea-acd7-3bd1006bbb40", "d64774fa-3223-49e7-aca9-ef1535c10697", "2b0f5acd-ffed-4efd-8f66-7391e7251668", "8a620ad6-d3ca-4be4-be65-807901b7f218", "9e6accc8-2c2f-45a6-904f-df61d09ccfbd", "d6c66cc7-cb4c-4952-a553-1a9bddcf2e44", "40a896bd-c66a-4a1d-9994-d1bdd26a2580", "f6e73436-3237-4cb2-a2bb-a50aabba23d0", "e3eba631-4c08-42eb-8bb9-531c8af61e66", "233ecbec-ecec-4bda-82f0-a5c5df011a3b", "9f560cd9-8df8-42b6-b028-76328c1660c9", "a9d12b6c-fc12-4f19-b381-2b4ccdacac05", "b7c73f49-6ba0-47f5-8c7a-24f758d2a8f2", "77a8ff79-e0a2-4be7-9105-f97be4a93630", "d38b8d96-5627-4488-a1b2-22318b0bfe8a", "0aa28bb1-8f37-47d7-a9fa-8de62f3d6bb6", "33d9ccda-9c4c-4f47-8211-9d6178d9d1de", "79fad181-5a8e-49e0-a033-fa099475e634", "7c83712e-95b7-438f-ab8e-a535c6cbf50e", "aafccfb1-de3e-4970-ba14-a5f5fb2864a3", "652df88a-0071-410b-99e8-e27ebf155798", "e3efe3cf-e9a7-4c33-b6f1-81c9ffa1e15a", "93a33764-7a1a-40d1-988e-600b1cc13161", "21b7f18b-ff6c-4d7f-85c2-39cb056f4f12", "55dc26a3-e149-40d6-b881-82622dc8a2e5"]}]}, {"BlockType": "LINE", "Confidence": 94.61786651611328, "Text": "******* (DUPLICATE RECEIPT) *******", "Geometry": {"BoundingBox": {"Width": 0.041879963129758835, "Height": 0.47836756706237793, "Left": 0.7354210019111633, "Top": 0.24976809322834015}, "Polygon": [{"X": 0.7615519165992737, "Y": 0.24991515278816223}, {"X": 0.7773009538650513, "Y": 0.72693932056427}, {"X": 0.7503384351730347, "Y": 0.7281356453895569}, {"X": 0.7354210019111633, "Y": 0.24976809322834015}]}, "Id": "b73070f7-ca4e-40e3-a4ff-f15af39c1a36", "Relationships": [{"Type": "CHILD", "Ids": ["720c8f5a-4aa6-41bd-b3c7-2a2c6b97a16c", "7f5d1c1d-0fac-45d5-a6e4-997c317de4cc", "22e2f43b-3e55-43b2-9321-8b79b2d059ea", "ec6ea395-895b-4e3a-a958-e1f343a27650"]}]}, {"BlockType": "LINE", "Confidence": 98.1852035522461, "Text": "TRIP 679 /4220 COUNTY ROAD J /SHEBOYGAN, 9 WI 53083/(920) 803-2078", "Geometry": {"BoundingBox": {"Width": 0.05602299049496651, "Height": 0.6281489729881287, "Left": 0.7017662525177002, "Top": 0.15191978216171265}, "Polygon": [{"X": 0.738028347492218, "Y": 0.1524921953678131}, {"X": 0.7577892541885376, "Y": 0.7781926989555359}, {"X": 0.7199978828430176, "Y": 0.7800687551498413}, {"X": 0.7017662525177002, "Y": 0.15191978216171265}]}, "Id": "42e06130-ac6a-421a-a24c-279ec74b16c7", "Relationships": [{"Type": "CHILD", "Ids": ["72a84875-e5ad-402f-ba53-c85866d80989", "eae8d7cf-6b06-4cb0-87f4-d482965021b0", "88a2f38e-7bd7-4795-a060-e0bad83fda4a", "94d3cb40-71c5-4ab3-bb7f-a16cc22c8421", "f1f574a6-44a1-4cdb-9467-40ecad78aea5", "133ceb7c-958c-4b78-bd03-96bcc9bcdb84", "62e2dc1b-cdde-4456-941c-ab4884adcc8b", "75006084-7b35-4de2-a7ee-801fff605f9b", "76524f71-d284-4b80-9334-fcf7b057ca7e", "0a4e0c87-cbfb-4653-9565-f81b320c02d7", "0dbc695f-11aa-4276-b0b7-095cc840517d"]}]}, {"BlockType": "LINE", "Confidence": 99.51542663574219, "Text": "Number: KWIK 0900-9991 01/31/2025 Station:", "Geometry": {"BoundingBox": {"Width": 0.04749060422182083, "Height": 0.3619541823863983, "Left": 0.6779745817184448, "Top": 0.10770674049854279}, "Polygon": [{"X": 0.7146393656730652, "Y": 0.10845225304365158}, {"X": 0.7254651784896851, "Y": 0.46901342272758484}, {"X": 0.6879032850265503, "Y": 0.4696609079837799}, {"X": 0.6779745817184448, "Y": 0.10770674049854279}]}, "Id": "3350a850-3d87-4942-8efc-796d0f16099d", "Relationships": [{"Type": "CHILD", "Ids": ["65df0be4-3231-4c04-b9bf-839759cd5e5c", "657bbd4a-582f-4fb9-b930-d7248e88f0c7", "38088ffb-3c1b-464a-aabb-0d08db09a690", "1c765062-d7c0-4cd3-8d84-238078dc639a", "b375097f-929c-44b1-b914-611a82c330d7"]}]}, {"BlockType": "LINE", "Confidence": 99.43315124511719, "Text": "Response: APPROVED AUTH REF#: P58049", "Geometry": {"BoundingBox": {"Width": 0.0451873354613781, "Height": 0.33559542894363403, "Left": 0.6349397301673889, "Top": 0.1058417558670044}, "Polygon": [{"X": 0.6711177229881287, "Y": 0.10658106952905655}, {"X": 0.6801270842552185, "Y": 0.4409102499485016}, {"X": 0.6431241035461426, "Y": 0.4414371848106384}, {"X": 0.6349397301673889, "Y": 0.1058417558670044}]}, "Id": "d8876a1f-b7cc-4f67-b9b6-2d5b6fce5677", "Relationships": [{"Type": "CHILD", "Ids": ["a73fa534-23db-47b7-a33e-46ca1bb9b162", "6c55fcde-f951-4a19-bb00-4b4f0d639f14", "b28c45b0-b611-49b4-b3d5-a789620046e6", "50eaeeff-f0f0-4c30-b6b3-f3f4930a7363", "a64d044d-01d0-487f-ad7c-af7fc2b2ad6d"]}]}, {"BlockType": "LINE", "Confidence": 99.61173248291016, "Text": "LOYALTY Response: APPROVED, Total Kwik Rewards Fleet Points: $19.83 Shower credi", "Geometry": {"BoundingBox": {"Width": 0.05105240270495415, "Height": 0.7949374318122864, "Left": 0.5959683656692505, "Top": 0.10220135748386383}, "Polygon": [{"X": 0.6280815601348877, "Y": 0.1028670072555542}, {"X": 0.6470207571983337, "Y": 0.8950682282447815}, {"X": 0.6131659150123596, "Y": 0.8971388339996338}, {"X": 0.5959683656692505, "Y": 0.10220135748386383}]}, "Id": "a92ce109-4b97-405d-9889-d8542cacd012", "Relationships": [{"Type": "CHILD", "Ids": ["7231182f-e727-4c14-adbf-ec70b578313b", "73811860-c57f-4ae2-8fb3-9aa6e6375cd7", "ec63f5b6-19ff-4118-936e-a057a3d84c15", "68771046-11ef-4d12-a51e-f4a4a0e44984", "53a8bd21-f989-4051-b1f9-85a52fd1c615", "81ad9528-56e6-4a43-8b72-72f816b9f9fb", "a9b4037e-ffb1-468f-9502-b61a0a1c1946", "9bf59823-0cd7-44bf-b623-1be8c0d0c2e3", "287a0212-85af-4493-a498-2498bd60dc24", "77d07865-0836-4d19-b933-519f06f30d39", "4a9fe966-4d64-4f52-b440-7353c5a2e350"]}]}, {"BlockType": "LINE", "Confidence": 99.49903106689453, "Text": "t earned! Expires in 14 days", "Geometry": {"BoundingBox": {"Width": 0.03163579851388931, "Height": 0.25845274329185486, "Left": 0.5755464434623718, "Top": 0.10135968774557114}, "Polygon": [{"X": 0.6015046834945679, "Y": 0.10189887136220932}, {"X": 0.6071822047233582, "Y": 0.3596601188182831}, {"X": 0.5807642936706543, "Y": 0.3598124384880066}, {"X": 0.5755464434623718, "Y": 0.10135968774557114}]}, "Id": "9e685c35-6f46-4fb6-aa37-c2064d10a338", "Relationships": [{"Type": "CHILD", "Ids": ["1972e802-d02e-4a6c-b194-18671ec758b8", "f1aee667-1b38-4d0a-8c16-a028eb5fe6c0", "e45dbaf8-5ac9-4b30-bcef-f47852e2ed77", "d49141a7-3f8e-45c7-af9f-f9512fb48563", "7b7dbfb7-9e24-4ba8-b1a5-37ccc0277bd9", "90fe154a-3a9e-491f-8ceb-f150b7622f0d"]}]}, {"BlockType": "LINE", "Confidence": 99.56314086914062, "Text": "Products", "Geometry": {"BoundingBox": {"Width": 0.01855430379509926, "Height": 0.07687968760728836, "Left": 0.5441340804100037, "Top": 0.28254857659339905}, "Polygon": [{"X": 0.5612354278564453, "Y": 0.2825855612754822}, {"X": 0.5626883506774902, "Y": 0.3593301773071289}, {"X": 0.5454976558685303, "Y": 0.3594282567501068}, {"X": 0.5441340804100037, "Y": 0.28254857659339905}]}, "Id": "f61de7c9-48ea-4f47-9292-3dddc842a8e9", "Relationships": [{"Type": "CHILD", "Ids": ["90d56e22-0098-4ee6-88c9-837738934cef"]}]}, {"BlockType": "LINE", "Confidence": 99.87224578857422, "Text": "Reefer", "Geometry": {"BoundingBox": {"Width": 0.017580317333340645, "Height": 0.059968575835227966, "Left": 0.5501558780670166, "Top": 0.3916538655757904}, "Polygon": [{"X": 0.5665919780731201, "Y": 0.3916538655757904}, {"X": 0.5677362084388733, "Y": 0.4513724446296692}, {"X": 0.5512338280677795, "Y": 0.45162245631217957}, {"X": 0.5501558780670166, "Y": 0.3918021619319916}]}, "Id": "b4ed7088-7b62-49f2-8d9a-1e2ac48c86cb", "Relationships": [{"Type": "CHILD", "Ids": ["9f9e3251-766e-490e-81e2-4607f5d42351"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.020884331315755844, "Height": 0.08182689547538757, "Left": 0.5485273599624634, "Top": 0.4838936924934387}, "Polygon": [{"X": 0.5678566098213196, "Y": 0.4838936924934387}, {"X": 0.5694116950035095, "Y": 0.5651991367340088}, {"X": 0.5499769449234009, "Y": 0.5657206177711487}, {"X": 0.5485273599624634, "Y": 0.4842510223388672}]}, "Id": "a42dcd9f-57f9-494e-937d-53a0d1c91d2d", "Relationships": [{"Type": "CHILD", "Ids": ["a981e5b8-d694-4e77-9b9f-4210b47221d4"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "Unit Cost", "Geometry": {"BoundingBox": {"Width": 0.017250869423151016, "Height": 0.09424883127212524, "Left": 0.5544348359107971, "Top": 0.5880442261695862}, "Polygon": [{"X": 0.5698943734169006, "Y": 0.5880442261695862}, {"X": 0.5716857314109802, "Y": 0.6816896796226501}, {"X": 0.5561298131942749, "Y": 0.6822930574417114}, {"X": 0.5544348359107971, "Y": 0.5884952545166016}]}, "Id": "c8209700-b773-496b-9966-402f08da387c", "Relationships": [{"Type": "CHILD", "Ids": ["df775379-581a-43da-be3c-6399eb2467a2", "2387686e-058c-479b-ac6a-5b7e2d48d744"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.016488369554281235, "Height": 0.05148951709270477, "Left": 0.5556374788284302, "Top": 0.7466084957122803}, "Polygon": [{"X": 0.5711620450019836, "Y": 0.7466084957122803}, {"X": 0.5721258521080017, "Y": 0.7973089218139648}, {"X": 0.5565494298934937, "Y": 0.7980979681015015}, {"X": 0.5556374788284302, "Y": 0.7473140358924866}]}, "Id": "648e1321-9170-44c1-9bdb-fb1425a9892c", "Relationships": [{"Type": "CHILD", "Ids": ["def7c7b2-c06f-4d99-abf9-931bd328c07a"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Pump", "Geometry": {"BoundingBox": {"Width": 0.017225226387381554, "Height": 0.03472035005688667, "Left": 0.5297978520393372, "Top": 0.09927887469530106}, "Polygon": [{"X": 0.5464000105857849, "Y": 0.09962563961744308}, {"X": 0.5470230579376221, "Y": 0.13399922847747803}, {"X": 0.5303815007209778, "Y": 0.13371017575263977}, {"X": 0.5297978520393372, "Y": 0.09927887469530106}]}, "Id": "7efd6912-b7ad-4ede-b9d7-b1a4fa8c7713", "Relationships": [{"Type": "CHILD", "Ids": ["866c3f83-f8a0-42f5-b7af-53764ceea159"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "Diesel Prem", "Geometry": {"BoundingBox": {"Width": 0.019406810402870178, "Height": 0.10556089878082275, "Left": 0.5201149582862854, "Top": 0.25439542531967163}, "Polygon": [{"X": 0.5376967191696167, "Y": 0.2544839084148407}, {"X": 0.5395217537879944, "Y": 0.3598546087741852}, {"X": 0.5218132734298706, "Y": 0.3599563539028168}, {"X": 0.5201149582862854, "Y": 0.25439542531967163}]}, "Id": "8eb1f06d-2052-4f3a-a141-653aca3a36e1", "Relationships": [{"Type": "CHILD", "Ids": ["dc8efa37-e23d-4347-9a05-171db8774dae", "b9a20c86-b553-43d4-bffe-c9e105cbd378"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.014863758347928524, "Height": 0.009643830358982086, "Left": 0.5286896824836731, "Top": 0.41149279475212097}, "Polygon": [{"X": 0.5433875322341919, "Y": 0.41149279475212097}, {"X": 0.5435534715652466, "Y": 0.42096012830734253}, {"X": 0.5288462042808533, "Y": 0.42113661766052246}, {"X": 0.5286896824836731, "Y": 0.41165491938591003}]}, "Id": "dae9e5cd-2002-47a7-ae97-56e7ca30df0d", "Relationships": [{"Type": "CHILD", "Ids": ["e8601fed-3dbb-416c-b811-f48f0b431bb8"]}]}, {"BlockType": "LINE", "Confidence": 97.12471771240234, "Text": "102.493", "Geometry": {"BoundingBox": {"Width": 0.016627725213766098, "Height": 0.07150653749704361, "Left": 0.5295024514198303, "Top": 0.49628162384033203}, "Polygon": [{"X": 0.544884204864502, "Y": 0.49628162384033203}, {"X": 0.5461301803588867, "Y": 0.5673710703849792}, {"X": 0.5306749939918518, "Y": 0.5677881836891174}, {"X": 0.5295024514198303, "Y": 0.49658480286598206}]}, "Id": "35177162-86f2-4c86-98c0-80ea0c2df68a", "Relationships": [{"Type": "CHILD", "Ids": ["f8297d0f-6ed1-4f15-a5f3-938702800e8d"]}]}, {"BlockType": "LINE", "Confidence": 99.7217788696289, "Text": "3.339", "Geometry": {"BoundingBox": {"Width": 0.01609494909644127, "Height": 0.052821725606918335, "Left": 0.5322599411010742, "Top": 0.6317789554595947}, "Polygon": [{"X": 0.5474388599395752, "Y": 0.6317789554595947}, {"X": 0.5483549237251282, "Y": 0.6840075850486755}, {"X": 0.533123254776001, "Y": 0.6846006512641907}, {"X": 0.5322599411010742, "Y": 0.6322886943817139}]}, "Id": "5ae44a79-641f-40e4-af22-9eb4f46055b1", "Relationships": [{"Type": "CHILD", "Ids": ["6ba09c47-5a9e-4a23-92e4-8ced9866f98d"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "342.22", "Geometry": {"BoundingBox": {"Width": 0.016550075262784958, "Height": 0.06340805441141129, "Left": 0.5322934985160828, "Top": 0.7374754548072815}, "Polygon": [{"X": 0.5477518439292908, "Y": 0.7374754548072815}, {"X": 0.5488435626029968, "Y": 0.8000946640968323}, {"X": 0.5333212614059448, "Y": 0.8008835315704346}, {"X": 0.5322934985160828, "Y": 0.7381619215011597}]}, "Id": "215ac39f-5944-40ea-acd7-3bd1006bbb40", "Relationships": [{"Type": "CHILD", "Ids": ["3864710c-d1ad-44cd-b5c0-d441b5985d32"]}]}, {"BlockType": "LINE", "Confidence": 96.19349670410156, "Text": "# 2", "Geometry": {"BoundingBox": {"Width": 0.015409254468977451, "Height": 0.026712460443377495, "Left": 0.511184811592102, "Top": 0.0981152355670929}, "Polygon": [{"X": 0.5261533856391907, "Y": 0.09842906147241592}, {"X": 0.5265941023826599, "Y": 0.12482769042253494}, {"X": 0.5115981698036194, "Y": 0.12455371767282486}, {"X": 0.511184811592102, "Y": 0.0981152355670929}]}, "Id": "d64774fa-3223-49e7-aca9-ef1535c10697", "Relationships": [{"Type": "CHILD", "Ids": ["fba4ed5c-09ba-4981-af00-c474f99b1deb", "294fd518-c05e-46b9-ab9d-d1b877b1e411"]}]}, {"BlockType": "LINE", "Confidence": 99.9011459350586, "Text": "DEF Bulk", "Geometry": {"BoundingBox": {"Width": 0.017801493406295776, "Height": 0.07815175503492355, "Left": 0.5002031922340393, "Top": 0.2812064290046692}, "Polygon": [{"X": 0.51677006483078, "Y": 0.2812443673610687}, {"X": 0.5180047154426575, "Y": 0.35926371812820435}, {"X": 0.5013493895530701, "Y": 0.35935819149017334}, {"X": 0.5002031922340393, "Y": 0.2812064290046692}]}, "Id": "2b0f5acd-ffed-4efd-8f66-7391e7251668", "Relationships": [{"Type": "CHILD", "Ids": ["998d2ed3-676d-49b7-8b0d-1b18d4159ccf", "8c117187-c37f-4509-9fc1-c35b3f3b4be8"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.015248531475663185, "Height": 0.009746819734573364, "Left": 0.5059031248092651, "Top": 0.4111149311065674}, "Polygon": [{"X": 0.5209988355636597, "Y": 0.4111149311065674}, {"X": 0.5211516618728638, "Y": 0.42068132758140564}, {"X": 0.5060461759567261, "Y": 0.42086175084114075}, {"X": 0.5059031248092651, "Y": 0.41128048300743103}]}, "Id": "8a620ad6-d3ca-4be4-be65-807901b7f218", "Relationships": [{"Type": "CHILD", "Ids": ["d307307b-2f3f-44de-bdb5-37b2820694bd"]}]}, {"BlockType": "LINE", "Confidence": 99.60299682617188, "Text": "13.390", "Geometry": {"BoundingBox": {"Width": 0.01628631353378296, "Height": 0.061124660074710846, "Left": 0.5073559284210205, "Top": 0.5065613389015198}, "Polygon": [{"X": 0.5226717591285706, "Y": 0.5065613389015198}, {"X": 0.5236422419548035, "Y": 0.5672720670700073}, {"X": 0.5082637667655945, "Y": 0.5676859617233276}, {"X": 0.5073559284210205, "Y": 0.5068785548210144}]}, "Id": "9e6accc8-2c2f-45a6-904f-df61d09ccfbd", "Relationships": [{"Type": "CHILD", "Ids": ["6814fe6b-a4d8-4532-a72b-810637a647fc"]}]}, {"BlockType": "LINE", "Confidence": 99.79173278808594, "Text": "3.599", "Geometry": {"BoundingBox": {"Width": 0.016430038958787918, "Height": 0.053497135639190674, "Left": 0.5090845227241516, "Top": 0.6318365931510925}, "Polygon": [{"X": 0.5246691703796387, "Y": 0.6318365931510925}, {"X": 0.525514543056488, "Y": 0.6847250461578369}, {"X": 0.5098749399185181, "Y": 0.6853337287902832}, {"X": 0.5090845227241516, "Y": 0.6323588490486145}]}, "Id": "d6c66cc7-cb4c-4952-a553-1a9bddcf2e44", "Relationships": [{"Type": "CHILD", "Ids": ["6a42c97e-db6e-4567-8663-6f49fed20308"]}]}, {"BlockType": "LINE", "Confidence": 99.64205932617188, "Text": "48.19", "Geometry": {"BoundingBox": {"Width": 0.01694946363568306, "Height": 0.0536964125931263, "Left": 0.509276807308197, "Top": 0.7491553425788879}, "Polygon": [{"X": 0.5253852009773254, "Y": 0.7491553425788879}, {"X": 0.5262262225151062, "Y": 0.8020289540290833}, {"X": 0.5100614428520203, "Y": 0.8028517961502075}, {"X": 0.509276807308197, "Y": 0.7498882412910461}]}, "Id": "40a896bd-c66a-4a1d-9994-d1bdd26a2580", "Relationships": [{"Type": "CHILD", "Ids": ["149ecb44-dfee-42c8-b6ef-de0df4da1f05"]}]}, {"BlockType": "LINE", "Confidence": 96.3611068725586, "Text": "# 2", "Geometry": {"BoundingBox": {"Width": 0.015661373734474182, "Height": 0.026644540950655937, "Left": 0.4899387061595917, "Top": 0.09668527543544769}, "Polygon": [{"X": 0.505199670791626, "Y": 0.09700676053762436}, {"X": 0.505600094795227, "Y": 0.12332981079816818}, {"X": 0.49031123518943787, "Y": 0.12304875254631042}, {"X": 0.4899387061595917, "Y": 0.09668527543544769}]}, "Id": "f6e73436-3237-4cb2-a2bb-a50aabba23d0", "Relationships": [{"Type": "CHILD", "Ids": ["2ba9658e-ba2c-4e37-8a81-66a507bf236c", "a6acbdf2-1af1-49a6-89d0-d9d833378ad6"]}]}, {"BlockType": "LINE", "Confidence": 99.4678726196289, "Text": "subtotal =", "Geometry": {"BoundingBox": {"Width": 0.018587639555335045, "Height": 0.09459757804870605, "Left": 0.45016181468963623, "Top": 0.20487375557422638}, "Polygon": [{"X": 0.4675738513469696, "Y": 0.20504814386367798}, {"X": 0.468749463558197, "Y": 0.29947131872177124}, {"X": 0.4512237012386322, "Y": 0.29946383833885193}, {"X": 0.45016181468963623, "Y": 0.20487375557422638}]}, "Id": "e3eba631-4c08-42eb-8bb9-531c8af61e66", "Relationships": [{"Type": "CHILD", "Ids": ["923f54fc-e233-44e4-bc81-fc18af0b2348", "8f76bf05-3b6b-4b58-be4b-2c5165daec87"]}]}, {"BlockType": "LINE", "Confidence": 99.44674682617188, "Text": "390.41", "Geometry": {"BoundingBox": {"Width": 0.01707647182047367, "Height": 0.05797561630606651, "Left": 0.45633840560913086, "Top": 0.3405677378177643}, "Polygon": [{"X": 0.4726812541484833, "Y": 0.3405677378177643}, {"X": 0.4734148681163788, "Y": 0.39838549494743347}, {"X": 0.4570073187351227, "Y": 0.3985433578491211}, {"X": 0.45633840560913086, "Y": 0.3406289517879486}]}, "Id": "233ecbec-ecec-4bda-82f0-a5c5df011a3b", "Relationships": [{"Type": "CHILD", "Ids": ["d5332c3f-63ea-4b4a-a67b-7b037c46cc9a"]}]}, {"BlockType": "LINE", "Confidence": 99.628173828125, "Text": "sales tax =", "Geometry": {"BoundingBox": {"Width": 0.017256179824471474, "Height": 0.10539103299379349, "Left": 0.4043056070804596, "Top": 0.19344432651996613}, "Polygon": [{"X": 0.4205985963344574, "Y": 0.1936255395412445}, {"X": 0.4215617775917053, "Y": 0.2988353669643402}, {"X": 0.40514954924583435, "Y": 0.29882732033729553}, {"X": 0.4043056070804596, "Y": 0.19344432651996613}]}, "Id": "9f560cd9-8df8-42b6-b028-76328c1660c9", "Relationships": [{"Type": "CHILD", "Ids": ["63b6445a-0442-4bad-bd93-25ce5f7b33a3", "d580650b-cda0-447a-bed5-4d1794516bd9", "a9a80c0e-3b26-450e-a011-65460e391de2"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.016406897455453873, "Height": 0.04035699740052223, "Left": 0.41010433435440063, "Top": 0.35954469442367554}, "Polygon": [{"X": 0.4261319637298584, "Y": 0.35954469442367554}, {"X": 0.4265112280845642, "Y": 0.3997455835342407}, {"X": 0.41043931245803833, "Y": 0.39990168809890747}, {"X": 0.41010433435440063, "Y": 0.35963520407676697}]}, "Id": "a9d12b6c-fc12-4f19-b381-2b4ccdacac05", "Relationships": [{"Type": "CHILD", "Ids": ["b479094d-4c3f-47ab-99d7-38eb929d8fa3"]}]}, {"BlockType": "LINE", "Confidence": 99.56632995605469, "Text": "TOTAL =", "Geometry": {"BoundingBox": {"Width": 0.016767844557762146, "Height": 0.06787659227848053, "Left": 0.38260895013809204, "Top": 0.23022744059562683}, "Polygon": [{"X": 0.39886143803596497, "Y": 0.2303476333618164}, {"X": 0.399376779794693, "Y": 0.29810404777526855}, {"X": 0.3830476999282837, "Y": 0.29809483885765076}, {"X": 0.38260895013809204, "Y": 0.23022744059562683}]}, "Id": "b7c73f49-6ba0-47f5-8c7a-24f758d2a8f2", "Relationships": [{"Type": "CHILD", "Ids": ["5b202ccd-c8c7-4fc6-a498-d5c5892826a3", "c161d262-6f56-4baa-aad6-cf6d35bb7e72"]}]}, {"BlockType": "LINE", "Confidence": 99.42521667480469, "Text": "390.41 Signature:", "Geometry": {"BoundingBox": {"Width": 0.020679626613855362, "Height": 0.18596920371055603, "Left": 0.3839632272720337, "Top": 0.3388030529022217}, "Polygon": [{"X": 0.40318650007247925, "Y": 0.3388030529022217}, {"X": 0.40464285016059875, "Y": 0.5243388414382935}, {"X": 0.3851735591888428, "Y": 0.5247722864151001}, {"X": 0.3839632272720337, "Y": 0.33887115120887756}]}, "Id": "77a8ff79-e0a2-4be7-9105-f97be4a93630", "Relationships": [{"Type": "CHILD", "Ids": ["aabbf366-d332-4d0f-8b15-13b05997479f", "3c99e825-eb97-4e00-a9fa-2e1f81af5f1b"]}]}, {"BlockType": "LINE", "Confidence": 99.87849426269531, "Text": "I agree to pay total amount", "Geometry": {"BoundingBox": {"Width": 0.021775098517537117, "Height": 0.28922033309936523, "Left": 0.3621384799480438, "Top": 0.5830105543136597}, "Polygon": [{"X": 0.38210615515708923, "Y": 0.5830105543136597}, {"X": 0.3839135766029358, "Y": 0.8710678815841675}, {"X": 0.36355486512184143, "Y": 0.8722308874130249}, {"X": 0.3621384799480438, "Y": 0.5835719704627991}]}, "Id": "d38b8d96-5627-4488-a1b2-22318b0bfe8a", "Relationships": [{"Type": "CHILD", "Ids": ["6bb38ea4-813f-4bf8-9433-b3e3a61a7836", "81b6f0ae-a884-4126-b3df-2cc7b50219e1", "fe938bd0-2953-4225-93f3-cbee2712cb5a", "491866a7-b8ce-43a5-af1c-6f1195f90c91", "f761ab4e-5120-465c-a377-e0ec8e47b81c", "4194333a-b7e6-4037-b7bd-963fc67d24a5"]}]}, {"BlockType": "LINE", "Confidence": 99.93865203857422, "Text": "according to card issuer agreement.", "Geometry": {"BoundingBox": {"Width": 0.022036077454686165, "Height": 0.37246325612068176, "Left": 0.3378969430923462, "Top": 0.****************}, "Polygon": [{"X": 0.3582063615322113, "Y": 0.****************}, {"X": 0.3599330186843872, "Y": 0.9103820323944092}, {"X": 0.33910825848579407, "Y": 0.9116509556770325}, {"X": 0.3378969430923462, "Y": 0.****************}]}, "Id": "0aa28bb1-8f37-47d7-a9fa-8de62f3d6bb6", "Relationships": [{"Type": "CHILD", "Ids": ["f2168c7e-c314-458a-b6f1-56ecca6a9837", "13d9f375-f948-4963-b49d-34040d72e3df", "4e4ca92e-43aa-41be-86b4-f01d7dca5b77", "4df0bc56-1b7a-437a-91b7-b63a19ef795d", "afed67e2-679e-44bc-bf5b-56b054a9fdf5"]}]}, {"BlockType": "LINE", "Confidence": 99.15497589111328, "Text": "Salesperson ID: AUTO/SMARTFUEL", "Geometry": {"BoundingBox": {"Width": 0.022182002663612366, "Height": 0.296774685382843, "Left": 0.2848261594772339, "Top": 0.12424083054065704}, "Polygon": [{"X": 0.3066694140434265, "Y": 0.12462949007749557}, {"X": 0.30700814723968506, "Y": 0.42075610160827637}, {"X": 0.2848261594772339, "Y": 0.42101553082466125}, {"X": 0.2849425971508026, "Y": 0.12424083054065704}]}, "Id": "33d9ccda-9c4c-4f47-8211-9d6178d9d1de", "Relationships": [{"Type": "CHILD", "Ids": ["3a67224d-2050-4bac-a73c-c2cc5d55e438", "16ad374a-203c-48d7-b4fa-e1290c71d369", "6b85cc09-d2b1-4fb2-b03f-000621c466aa"]}]}, {"BlockType": "LINE", "Confidence": 99.51829528808594, "Text": "Invoice #: 0099991", "Geometry": {"BoundingBox": {"Width": 0.01676257885992527, "Height": 0.19443288445472717, "Left": 0.2930886745452881, "Top": 0.5842564105987549}, "Polygon": [{"X": 0.3095978796482086, "Y": 0.5842564105987549}, {"X": 0.3098512291908264, "Y": 0.777896523475647}, {"X": 0.29312315583229065, "Y": 0.7786892652511597}, {"X": 0.2930886745452881, "Y": 0.5847192406654358}]}, "Id": "79fad181-5a8e-49e0-a033-fa099475e634", "Relationships": [{"Type": "CHILD", "Ids": ["a100430a-7c9d-41c0-8ba8-17262bbdf096", "566f9017-75eb-4c6b-a695-88a89ab2feb2", "c3072664-ccaf-4d59-9b19-3b8e8abca9f2"]}]}, {"BlockType": "LINE", "Confidence": 99.91077423095703, "Text": "Truck Number: 3482", "Geometry": {"BoundingBox": {"Width": 0.01707983948290348, "Height": 0.17329546809196472, "Left": 0.2645787000656128, "Top": 0.14253348112106323}, "Polygon": [{"X": 0.2816585600376129, "Y": 0.14280229806900024}, {"X": 0.28155064582824707, "Y": 0.3158084452152252}, {"X": 0.2645787000656128, "Y": 0.31582894921302795}, {"X": 0.26489198207855225, "Y": 0.14253348112106323}]}, "Id": "7c83712e-95b7-438f-ab8e-a535c6cbf50e", "Relationships": [{"Type": "CHILD", "Ids": ["0d0f8acd-5f52-4e3a-a4ff-92988ed36fa7", "00c23d49-6781-4776-a4d9-d0f20fcdc965", "1525ac33-29d0-4d2b-882a-0b43c02a8ab8"]}]}, {"BlockType": "LINE", "Confidence": 98.2254409790039, "Text": "Merchant Number: *******6793", "Geometry": {"BoundingBox": {"Width": 0.01757030189037323, "Height": 0.30742788314819336, "Left": 0.2690318524837494, "Top": 0.****************}, "Polygon": [{"X": 0.2866021692752838, "Y": 0.****************}, {"X": 0.2865201532840729, "Y": 0.8243372440338135}, {"X": 0.2690318524837494, "Y": 0.8252450823783875}, {"X": 0.2694757878780365, "Y": 0.****************}]}, "Id": "aafccfb1-de3e-4970-ba14-a5f5fb2864a3", "Relationships": [{"Type": "CHILD", "Ids": ["3b7535e3-6977-4b33-b112-1fae687458d7", "9c5ed05c-cb63-4e12-b8f8-7b095495eeb3", "ec5e5aec-157c-44b3-aed6-bf8a7468eefb"]}]}, {"BlockType": "LINE", "Confidence": 99.84135437011719, "Text": "Card Number:", "Geometry": {"BoundingBox": {"Width": 0.016798358410596848, "Height": 0.11297959834337234, "Left": 0.24095016717910767, "Top": 0.15150593221187592}, "Polygon": [{"X": 0.2577485144138336, "Y": 0.1517537534236908}, {"X": 0.25748804211616516, "Y": 0.26448553800582886}, {"X": 0.24095016717910767, "Y": 0.2644210755825043}, {"X": 0.24134182929992676, "Y": 0.15150593221187592}]}, "Id": "652df88a-0071-410b-99e8-e27ebf155798", "Relationships": [{"Type": "CHILD", "Ids": ["d07629bf-fe4b-46cd-934f-522fdb344b4a", "ac10f695-6faf-461a-9fa8-f05211d0438e"]}]}, {"BlockType": "LINE", "Confidence": 64.5240707397461, "Text": "xxxxxxxxxxxxxx1773 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.021025530993938446, "Height": 0.2529072165489197, "Left": 0.2401062697172165, "Top": 0.2756681740283966}, "Polygon": [{"X": 0.26113179326057434, "Y": 0.2757242023944855}, {"X": 0.2606133818626404, "Y": 0.****************}, {"X": 0.2401062697172165, "Y": 0.****************}, {"X": 0.2409822642803192, "Y": 0.2756681740283966}]}, "Id": "e3efe3cf-e9a7-4c33-b6f1-81c9ffa1e15a", "Relationships": [{"Type": "CHILD", "Ids": ["867788bc-8803-4647-bc21-b787127b4ad8", "24f6880f-1767-438d-86c5-cc3eef30a1bc"]}]}, {"BlockType": "LINE", "Confidence": 96.36384582519531, "Text": "Loyalty Card#: xxxxxxxxxxxx8997 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.02436676062643528, "Height": 0.3743703365325928, "Left": 0.21376949548721313, "Top": 0.13327564299106598}, "Polygon": [{"X": 0.23813626170158386, "Y": 0.13365337252616882}, {"X": 0.23675437271595, "Y": 0.5071819424629211}, {"X": 0.21376949548721313, "Y": 0.5076459646224976}, {"X": 0.21574684977531433, "Y": 0.13327564299106598}]}, "Id": "93a33764-7a1a-40d1-988e-600b1cc13161", "Relationships": [{"Type": "CHILD", "Ids": ["3a3d3619-8b84-487b-b865-73a709ffd9ad", "36c807d4-c64b-4e03-9f56-6f5012364c9f", "139ef8e9-e0c5-42fb-a4d4-9b8c5ebe54b4", "3bac0f8c-2a22-4018-bf45-185705629c1b"]}]}, {"BlockType": "LINE", "Confidence": 99.07764434814453, "Text": "Billing Company: NBS / KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.027094604447484016, "Height": 0.3733161389827728, "Left": 0.1648295372724533, "Top": 0.09141580015420914}, "Polygon": [{"X": 0.19192413985729218, "Y": 0.09191478788852692}, {"X": 0.18932250142097473, "Y": 0.4643433690071106}, {"X": 0.1648295372724533, "Y": 0.46473193168640137}, {"X": 0.16806866228580475, "Y": 0.09141580015420914}]}, "Id": "21b7f18b-ff6c-4d7f-85c2-39cb056f4f12", "Relationships": [{"Type": "CHILD", "Ids": ["fe4ede06-573a-438c-b048-a1cb33aadcd4", "456707fc-7779-4b8c-9bd7-d3a0b29e0034", "96d125d0-d717-4711-8e9e-d4cc30d0cc8b", "3dace52f-bddb-4a46-86bd-ec9ac8b7877d", "9363cc80-ca4e-4fd6-878e-db9d1f3f21b8", "1738c0b9-4b5f-4f11-b894-ef0aaf037fb4", "5bb6662f-4257-4fc0-8f7a-638de3ce2e2f"]}]}, {"BlockType": "LINE", "Confidence": 91.27796936035156, "Text": "ALL TAXES INCLUDED IN FUEL PRICE ------------------------- THANK YOU!", "Geometry": {"BoundingBox": {"Width": 0.032651450484991074, "Height": 0.4901982247829437, "Left": 0.11400303989648819, "Top": 0.0925765335559845}, "Polygon": [{"X": 0.14665448665618896, "Y": 0.09312988072633743}, {"X": 0.14167121052742004, "Y": 0.5820178985595703}, {"X": 0.11400303989648819, "Y": 0.5827747583389282}, {"X": 0.11992833018302917, "Y": 0.0925765335559845}]}, "Id": "55dc26a3-e149-40d6-b881-82622dc8a2e5", "Relationships": [{"Type": "CHILD", "Ids": ["4abefee7-30eb-4a2f-96cc-abf5f77924db", "9d6d031e-5532-4ebc-830a-98dff70f12e8", "5b3cba19-2fb0-486e-9f3f-eeed5e617bc0", "673b6de5-7a18-4b57-a4ab-d3ab3efda57f", "18173e33-36a4-446d-a401-0ef8dd8df53d", "1935a737-71ef-435d-8897-5457f880f61e", "6a805bf6-e79d-4729-9fe7-f56fc44ca0a9", "e8613788-34df-4f31-808c-2a24e7df732d", "95373ce1-5ad9-499b-9648-1290433cab17"]}]}, {"BlockType": "WORD", "Confidence": 89.44993591308594, "Text": "*******", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024920305237174034, "Height": 0.12203118950128555, "Left": 0.7354210019111633, "Top": 0.24976809322834015}, "Polygon": [{"X": 0.7563655376434326, "Y": 0.24988596141338348}, {"X": 0.7603412866592407, "Y": 0.3716491162776947}, {"X": 0.7392264008522034, "Y": 0.3717992901802063}, {"X": 0.7354210019111633, "Y": 0.24976809322834015}]}, "Id": "720c8f5a-4aa6-41bd-b3c7-2a2c6b97a16c"}, {"BlockType": "WORD", "Confidence": 97.56118774414062, "Text": "(DUPLICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02072226256132126, "Height": 0.09760016202926636, "Left": 0.7488316297531128, "Top": 0.3950352072715759}, "Polygon": [{"X": 0.7663430571556091, "Y": 0.3950352072715759}, {"X": 0.7695538997650146, "Y": 0.49228721857070923}, {"X": 0.7519299387931824, "Y": 0.4926353693008423}, {"X": 0.7488316297531128, "Y": 0.39520272612571716}]}, "Id": "7f5d1c1d-0fac-45d5-a6e4-997c317de4cc"}, {"BlockType": "WORD", "Confidence": 98.38428497314453, "Text": "RECEIPT)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019165920093655586, "Height": 0.07678481191396713, "Left": 0.7521623969078064, "Top": 0.5039618611335754}, "Polygon": [{"X": 0.7688151597976685, "Y": 0.5039618611335754}, {"X": 0.771328330039978, "Y": 0.580261766910553}, {"X": 0.7545922994613647, "Y": 0.5807466506958008}, {"X": 0.7521623969078064, "Y": 0.504311203956604}]}, "Id": "22e2f43b-3e55-43b2-9321-8b79b2d059ea"}, {"BlockType": "WORD", "Confidence": 93.0760498046875, "Text": "*******", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019727414473891258, "Height": 0.13299843668937683, "Left": 0.7524169683456421, "Top": 0.5948591232299805}, "Polygon": [{"X": 0.7678225040435791, "Y": 0.5948591232299805}, {"X": 0.7721443772315979, "Y": 0.7271681427955627}, {"X": 0.7566061615943909, "Y": 0.7278575897216797}, {"X": 0.7524169683456421, "Y": 0.5953289270401001}]}, "Id": "ec6ea395-895b-4e3a-a958-e1f343a27650"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01718170940876007, "Height": 0.034256719052791595, "Left": 0.7033568024635315, "Top": 0.15194489061832428}, "Polygon": [{"X": 0.7195090651512146, "Y": 0.1521998643875122}, {"X": 0.7205384969711304, "Y": 0.18620161712169647}, {"X": 0.704349160194397, "Y": 0.1860034018754959}, {"X": 0.7033568024635315, "Y": 0.15194489061832428}]}, "Id": "72a84875-e5ad-402f-ba53-c85866d80989"}, {"BlockType": "WORD", "Confidence": 98.115234375, "Text": "679", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016830788925290108, "Height": 0.02624206617474556, "Left": 0.7087573409080505, "Top": 0.19457772374153137}, "Polygon": [{"X": 0.7247918248176575, "Y": 0.19475983083248138}, {"X": 0.7255881428718567, "Y": 0.22081978619098663}, {"X": 0.7095255255699158, "Y": 0.22068101167678833}, {"X": 0.7087573409080505, "Y": 0.19457772374153137}]}, "Id": "eae8d7cf-6b06-4cb0-87f4-d482965021b0"}, {"BlockType": "WORD", "Confidence": 98.59953308105469, "Text": "/4220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017296073958277702, "Height": 0.04609626531600952, "Left": 0.7132627367973328, "Top": 0.23039068281650543}, "Polygon": [{"X": 0.7291434407234192, "Y": 0.23051190376281738}, {"X": 0.7305588126182556, "Y": 0.27648696303367615}, {"X": 0.7146291136741638, "Y": 0.276441752910614}, {"X": 0.7132627367973328, "Y": 0.23039068281650543}]}, "Id": "88a2f38e-7bd7-4795-a060-e0bad83fda4a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "COUNTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020361291244626045, "Height": 0.05705221742391586, "Left": 0.7180632948875427, "Top": 0.285819411277771}, "Polygon": [{"X": 0.7366484999656677, "Y": 0.2858540117740631}, {"X": 0.7384245991706848, "Y": 0.3427954614162445}, {"X": 0.7197687029838562, "Y": 0.34287163615226746}, {"X": 0.7180632948875427, "Y": 0.285819411277771}]}, "Id": "94d3cb40-71c5-4ab3-bb7f-a16cc22c8421"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "ROAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017082950100302696, "Height": 0.03799964115023613, "Left": 0.725515604019165, "Top": 0.35275423526763916}, "Polygon": [{"X": 0.7414107918739319, "Y": 0.35275423526763916}, {"X": 0.7425985932350159, "Y": 0.39060917496681213}, {"X": 0.7266634702682495, "Y": 0.3907538652420044}, {"X": 0.725515604019165, "Y": 0.35283568501472473}]}, "Id": "f1f574a6-44a1-4cdb-9467-40ecad78aea5"}, {"BlockType": "WORD", "Confidence": 99.64684295654297, "Text": "J", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014497396536171436, "Height": 0.009564084000885487, "Left": 0.7292867302894592, "Top": 0.4015740752220154}, "Polygon": [{"X": 0.7434886693954468, "Y": 0.4015740752220154}, {"X": 0.7437841296195984, "Y": 0.41097888350486755}, {"X": 0.7295733690261841, "Y": 0.4111381769180298}, {"X": 0.7292867302894592, "Y": 0.4017193019390106}]}, "Id": "133ceb7c-958c-4b78-bd03-96bcc9bcdb84"}, {"BlockType": "WORD", "Confidence": 99.31581115722656, "Text": "/SHEBOYGAN,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02247115597128868, "Height": 0.10657597333192825, "Left": 0.7274333238601685, "Top": 0.42240989208221436}, "Polygon": [{"X": 0.7465528845787048, "Y": 0.42240989208221436}, {"X": 0.7499045133590698, "Y": 0.5285333395004272}, {"X": 0.7306509017944336, "Y": 0.528985857963562}, {"X": 0.7274333238601685, "Y": 0.422647088766098}]}, "Id": "62e2dc1b-cdde-4456-941c-ab4884adcc8b"}, {"BlockType": "WORD", "Confidence": 99.52567291259766, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014728435315191746, "Height": 0.009992056526243687, "Left": 0.7113727927207947, "Top": 0.48263266682624817}, "Polygon": [{"X": 0.7258098125457764, "Y": 0.48263266682624817}, {"X": 0.7261012196540833, "Y": 0.4923405349254608}, {"X": 0.7116550207138062, "Y": 0.4926247000694275}, {"X": 0.7113727927207947, "Y": 0.48290202021598816}]}, "Id": "75006084-7b35-4de2-a7ee-801fff605f9b"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015404137782752514, "Height": 0.019318830221891403, "Left": 0.7336580157279968, "Top": 0.543372392654419}, "Polygon": [{"X": 0.7484671473503113, "Y": 0.543372392654419}, {"X": 0.749062180519104, "Y": 0.5622904300689697}, {"X": 0.7342346906661987, "Y": 0.5626912117004395}, {"X": 0.7336580157279968, "Y": 0.543743371963501}]}, "Id": "76524f71-d284-4b80-9334-fcf7b057ca7e"}, {"BlockType": "WORD", "Confidence": 86.56489562988281, "Text": "53083/(920)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019479988142848015, "Height": 0.11063964664936066, "Left": 0.7326292395591736, "Top": 0.5738608241081238}, "Polygon": [{"X": 0.7486554384231567, "Y": 0.5738608241081238}, {"X": 0.7521092295646667, "Y": 0.6838588714599609}, {"X": 0.7359678149223328, "Y": 0.684500515460968}, {"X": 0.7326292395591736, "Y": 0.5743134021759033}]}, "Id": "0a4e0c87-cbfb-4653-9565-f81b320c02d7"}, {"BlockType": "WORD", "Confidence": 98.464599609375, "Text": "803-2078", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01975058950483799, "Height": 0.08108830451965332, "Left": 0.7321419715881348, "Top": 0.6982578635215759}, "Polygon": [{"X": 0.7493908405303955, "Y": 0.6982578635215759}, {"X": 0.7518925666809082, "Y": 0.7784854173660278}, {"X": 0.7345540523529053, "Y": 0.7793461680412292}, {"X": 0.7321419715881348, "Y": 0.6989692449569702}]}, "Id": "0dbc695f-11aa-4276-b0b7-095cc840517d"}, {"BlockType": "WORD", "Confidence": 99.6924819946289, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017106642946600914, "Height": 0.05756156146526337, "Left": 0.6779745817184448, "Top": 0.10770674049854279}, "Polygon": [{"X": 0.6934481859207153, "Y": 0.10802137106657028}, {"X": 0.6950812339782715, "Y": 0.16526830196380615}, {"X": 0.6795474290847778, "Y": 0.16504481434822083}, {"X": 0.6779745817184448, "Y": 0.10770674049854279}]}, "Id": "65df0be4-3231-4c04-b9bf-839759cd5e5c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01690724305808544, "Height": 0.0342368520796299, "Left": 0.6981754302978516, "Top": 0.10884860903024673}, "Polygon": [{"X": 0.7140657901763916, "Y": 0.10917050391435623}, {"X": 0.7150827050209045, "Y": 0.14308546483516693}, {"X": 0.6991557478904724, "Y": 0.14281904697418213}, {"X": 0.6981754302978516, "Y": 0.10884860903024673}]}, "Id": "657bbd4a-582f-4fb9-b930-d7248e88f0c7"}, {"BlockType": "WORD", "Confidence": 99.21556091308594, "Text": "0900-9991", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021390201523900032, "Height": 0.07905302196741104, "Left": 0.6862224340438843, "Top": 0.17660307884216309}, "Polygon": [{"X": 0.7053098082542419, "Y": 0.1768549680709839}, {"X": 0.7076126337051392, "Y": 0.25565609335899353}, {"X": 0.6884236335754395, "Y": 0.2555601894855499}, {"X": 0.6862224340438843, "Y": 0.17660307884216309}]}, "Id": "38088ffb-3c1b-464a-aabb-0d08db09a690"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "01/31/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02432129718363285, "Height": 0.09524978697299957, "Left": 0.6953594088554382, "Top": 0.276454895734787}, "Polygon": [{"X": 0.7168461680412292, "Y": 0.2765157222747803}, {"X": 0.7196807265281677, "Y": 0.3715517818927765}, {"X": 0.698057234287262, "Y": 0.37170469760894775}, {"X": 0.6953594088554382, "Y": 0.276454895734787}]}, "Id": "1c765062-d7c0-4cd3-8d84-238078dc639a"}, {"BlockType": "WORD", "Confidence": 98.85543060302734, "Text": "Station:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01910925842821598, "Height": 0.07786587625741959, "Left": 0.7063559293746948, "Top": 0.3914383053779602}, "Polygon": [{"X": 0.7231360077857971, "Y": 0.3914383053779602}, {"X": 0.7254651784896851, "Y": 0.46901342272758484}, {"X": 0.7085986733436584, "Y": 0.4693041741847992}, {"X": 0.7063559293746948, "Y": 0.39159178733825684}]}, "Id": "b375097f-929c-44b1-b914-611a82c330d7"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020471248775720596, "Height": 0.07447176426649094, "Left": 0.6349397301673889, "Top": 0.1058417558670044}, "Polygon": [{"X": 0.6535066962242126, "Y": 0.10622118413448334}, {"X": 0.6554110050201416, "Y": 0.18031351268291473}, {"X": 0.6367501020431519, "Y": 0.18007521331310272}, {"X": 0.6349397301673889, "Y": 0.1058417558670044}]}, "Id": "a73fa534-23db-47b7-a33e-46ca1bb9b162"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020701203495264053, "Height": 0.07345152646303177, "Left": 0.6451044082641602, "Top": 0.19195079803466797}, "Polygon": [{"X": 0.6638813018798828, "Y": 0.1921677440404892}, {"X": 0.6658056378364563, "Y": 0.26540231704711914}, {"X": 0.6469355225563049, "Y": 0.26532748341560364}, {"X": 0.6451044082641602, "Y": 0.19195079803466797}]}, "Id": "6c55fcde-f951-4a19-bb00-4b4f0d639f14"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018058154731988907, "Height": 0.03785285726189613, "Left": 0.6522531509399414, "Top": 0.27512165904045105}, "Polygon": [{"X": 0.6693098545074463, "Y": 0.27517205476760864}, {"X": 0.6703113317489624, "Y": 0.3129580616950989}, {"X": 0.653211236000061, "Y": 0.3129745125770569}, {"X": 0.6522531509399414, "Y": 0.27512165904045105}]}, "Id": "b28c45b0-b611-49b4-b3d5-a789620046e6"}, {"BlockType": "WORD", "Confidence": 99.27515411376953, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0173479113727808, "Height": 0.045598581433296204, "Left": 0.6574212312698364, "Top": 0.32306018471717834}, "Polygon": [{"X": 0.6735540628433228, "Y": 0.32306018471717834}, {"X": 0.6747691631317139, "Y": 0.3685499131679535}, {"X": 0.6585870981216431, "Y": 0.36865878105163574}, {"X": 0.6574212312698364, "Y": 0.32309260964393616}]}, "Id": "50eaeeff-f0f0-4c30-b6b3-f3f4930a7363"}, {"BlockType": "WORD", "Confidence": 98.57640838623047, "Text": "P58049", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019018860533833504, "Height": 0.059828270226716995, "Left": 0.6611081957817078, "Top": 0.3813309371471405}, "Polygon": [{"X": 0.6785215735435486, "Y": 0.3813309371471405}, {"X": 0.6801270842552185, "Y": 0.4409102499485016}, {"X": 0.6626444458961487, "Y": 0.4411592185497284}, {"X": 0.6611081957817078, "Y": 0.3814712166786194}]}, "Id": "a64d044d-01d0-487f-ad7c-af7fc2b2ad6d"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "LOYALTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019190659746527672, "Height": 0.06115070730447769, "Left": 0.5959683656692505, "Top": 0.10220135748386383}, "Polygon": [{"X": 0.6137675046920776, "Y": 0.1025703027844429}, {"X": 0.6151590347290039, "Y": 0.16335207223892212}, {"X": 0.5972856879234314, "Y": 0.16309352219104767}, {"X": 0.5959683656692505, "Y": 0.10220135748386383}]}, "Id": "7231182f-e727-4c14-adbf-ec70b578313b"}, {"BlockType": "WORD", "Confidence": 99.63229370117188, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019064420834183693, "Height": 0.08045998215675354, "Left": 0.6001825928688049, "Top": 0.1728396862745285}, "Polygon": [{"X": 0.6173988580703735, "Y": 0.17307154834270477}, {"X": 0.6192470192909241, "Y": 0.25329968333244324}, {"X": 0.6019365191459656, "Y": 0.2532097399234772}, {"X": 0.6001825928688049, "Y": 0.1728396862745285}]}, "Id": "73811860-c57f-4ae2-8fb3-9aa6e6375cd7"}, {"BlockType": "WORD", "Confidence": 98.97421264648438, "Text": "APPROVED,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020860562101006508, "Height": 0.08196301758289337, "Left": 0.6091384887695312, "Top": 0.26480382680892944}, "Polygon": [{"X": 0.6280657052993774, "Y": 0.26487964391708374}, {"X": 0.629999041557312, "Y": 0.3466823697090149}, {"X": 0.6109670400619507, "Y": 0.346766859292984}, {"X": 0.6091384887695312, "Y": 0.26480382680892944}]}, "Id": "ec63f5b6-19ff-4118-936e-a057a3d84c15"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016525277867913246, "Height": 0.04799384996294975, "Left": 0.6168004274368286, "Top": 0.36112481355667114}, "Polygon": [{"X": 0.632189154624939, "Y": 0.36112481355667114}, {"X": 0.6333256959915161, "Y": 0.4089508354663849}, {"X": 0.6178874373435974, "Y": 0.4091186821460724}, {"X": 0.6168004274368286, "Y": 0.36121609807014465}]}, "Id": "68771046-11ef-4d12-a51e-f4a4a0e44984"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "K<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01622002013027668, "Height": 0.040526434779167175, "Left": 0.6204286217689514, "Top": 0.4209396243095398}, "Polygon": [{"X": 0.6356857419013977, "Y": 0.4209396243095398}, {"X": 0.6366486549377441, "Y": 0.46121710538864136}, {"X": 0.6213504672050476, "Y": 0.46146607398986816}, {"X": 0.6204286217689514, "Y": 0.4211244285106659}]}, "Id": "53a8bd21-f989-4051-b1f9-85a52fd1c615"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Rewards", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0169738307595253, "Height": 0.07045996189117432, "Left": 0.6211497187614441, "Top": 0.47313037514686584}, "Polygon": [{"X": 0.636450469493866, "Y": 0.47313037514686584}, {"X": 0.6381235718727112, "Y": 0.5432099103927612}, {"X": 0.6227512955665588, "Y": 0.5435903668403625}, {"X": 0.6211497187614441, "Y": 0.47339820861816406}]}, "Id": "81ad9528-56e6-4a43-8b72-72f816b9f9fb"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01595863327383995, "Height": 0.05050162971019745, "Left": 0.6229143738746643, "Top": 0.5553577542304993}, "Polygon": [{"X": 0.6376808881759644, "Y": 0.5553577542304993}, {"X": 0.6388729810714722, "Y": 0.6053975224494934}, {"X": 0.6240575313568115, "Y": 0.6058593988418579}, {"X": 0.6229143738746643, "Y": 0.5557417273521423}]}, "Id": "a9b4037e-ffb1-468f-9502-b61a0a1c1946"}, {"BlockType": "WORD", "Confidence": 99.40290832519531, "Text": "Points:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017720133066177368, "Height": 0.07135103642940521, "Left": 0.6231653690338135, "Top": 0.6180668473243713}, "Polygon": [{"X": 0.6392008662223816, "Y": 0.6180668473243713}, {"X": 0.6408855319023132, "Y": 0.6887766718864441}, {"X": 0.6247752904891968, "Y": 0.6894178986549377}, {"X": 0.6231653690338135, "Y": 0.6185877919197083}]}, "Id": "9bf59823-0cd7-44bf-b623-1be8c0d0c2e3"}, {"BlockType": "WORD", "Confidence": 98.67964935302734, "Text": "$19.83", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017561297863721848, "Height": 0.06253553181886673, "Left": 0.6231175065040588, "Top": 0.701746940612793}, "Polygon": [{"X": 0.6392155289649963, "Y": 0.701746940612793}, {"X": 0.6406788229942322, "Y": 0.7635142803192139}, {"X": 0.6245155930519104, "Y": 0.7642824649810791}, {"X": 0.6231175065040588, "Y": 0.7024090886116028}]}, "Id": "287a0212-85af-4493-a498-2498bd60dc24"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01681486703455448, "Height": 0.06356945633888245, "Left": 0.6241512298583984, "Top": 0.7739503383636475}, "Polygon": [{"X": 0.6394863128662109, "Y": 0.7739503383636475}, {"X": 0.6409661173820496, "Y": 0.8366715908050537}, {"X": 0.6255682706832886, "Y": 0.8375198245048523}, {"X": 0.6241512298583984, "Y": 0.7746955752372742}]}, "Id": "77d07865-0836-4d19-b933-519f06f30d39"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "credi", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016143932938575745, "Height": 0.0488460510969162, "Left": 0.6241438388824463, "Top": 0.8475554585456848}, "Polygon": [{"X": 0.6391637325286865, "Y": 0.8475554585456848}, {"X": 0.6402877569198608, "Y": 0.8954800367355347}, {"X": 0.6252211332321167, "Y": 0.8964015245437622}, {"X": 0.6241438388824463, "Y": 0.8483995795249939}]}, "Id": "4a9fe966-4d64-4f52-b440-7353c5a2e350"}, {"BlockType": "WORD", "Confidence": 97.61659240722656, "Text": "t", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013526341877877712, "Height": 0.007262163329869509, "Left": 0.5755464434623718, "Top": 0.10135968774557114}, "Polygon": [{"X": 0.5889251232147217, "Y": 0.1016375795006752}, {"X": 0.5890727639198303, "Y": 0.10862185060977936}, {"X": 0.5756876468658447, "Y": 0.10835343599319458}, {"X": 0.5755464434623718, "Y": 0.10135968774557114}]}, "Id": "1972e802-d02e-4a6c-b194-18671ec758b8"}, {"BlockType": "WORD", "Confidence": 99.57290649414062, "Text": "earned!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019010029733181, "Height": 0.05813115835189819, "Left": 0.5761619806289673, "Top": 0.11957264691591263}, "Polygon": [{"X": 0.5939315557479858, "Y": 0.11990854144096375}, {"X": 0.5951720476150513, "Y": 0.17770381271839142}, {"X": 0.5773319602012634, "Y": 0.17747260630130768}, {"X": 0.5761619806289673, "Y": 0.11957264691591263}]}, "Id": "f1aee667-1b38-4d0a-8c16-a028eb5fe6c0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Expires", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019002510234713554, "Height": 0.06458435207605362, "Left": 0.5793383717536926, "Top": 0.18953776359558105}, "Polygon": [{"X": 0.5969522595405579, "Y": 0.1897442638874054}, {"X": 0.5983408689498901, "Y": 0.25412213802337646}, {"X": 0.5806495547294617, "Y": 0.2540318965911865}, {"X": 0.5793383717536926, "Y": 0.18953776359558105}]}, "Id": "e45dbaf8-5ac9-4b30-bcef-f47852e2ed77"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014610550366342068, "Height": 0.01627732254564762, "Left": 0.587567150592804, "Top": 0.2660283148288727}, "Polygon": [{"X": 0.601824164390564, "Y": 0.2660835087299347}, {"X": 0.6021777391433716, "Y": 0.28230562806129456}, {"X": 0.5879050493240356, "Y": 0.2822742164134979}, {"X": 0.587567150592804, "Y": 0.2660283148288727}]}, "Id": "d49141a7-3f8e-45c7-af9f-f9512fb48563"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015040409751236439, "Height": 0.016802972182631493, "Left": 0.5897344350814819, "Top": 0.29455575346946716}, "Polygon": [{"X": 0.6044068336486816, "Y": 0.2945695221424103}, {"X": 0.6047748327255249, "Y": 0.3113471567630768}, {"X": 0.5900857448577881, "Y": 0.3113587200641632}, {"X": 0.5897344350814819, "Y": 0.29455575346946716}]}, "Id": "7b7dbfb7-9e24-4ba8-b1a5-37ccc0277bd9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018307965248823166, "Height": 0.03830155357718468, "Left": 0.5888742804527283, "Top": 0.3214595317840576}, "Polygon": [{"X": 0.6063408255577087, "Y": 0.3214595317840576}, {"X": 0.6071822047233582, "Y": 0.3596601188182831}, {"X": 0.5896705985069275, "Y": 0.3597610890865326}, {"X": 0.5888742804527283, "Y": 0.3214915096759796}]}, "Id": "90fe154a-3a9e-491f-8ceb-f150b7622f0d"}, {"BlockType": "WORD", "Confidence": 99.56314086914062, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01855430379509926, "Height": 0.07687968760728836, "Left": 0.5441340804100037, "Top": 0.28254857659339905}, "Polygon": [{"X": 0.5612354278564453, "Y": 0.2825855612754822}, {"X": 0.5626883506774902, "Y": 0.3593301773071289}, {"X": 0.5454976558685303, "Y": 0.3594282567501068}, {"X": 0.5441340804100037, "Y": 0.28254857659339905}]}, "Id": "90d56e22-0098-4ee6-88c9-837738934cef"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Reefer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017580317333340645, "Height": 0.059968575835227966, "Left": 0.5501558780670166, "Top": 0.3916538655757904}, "Polygon": [{"X": 0.5665919780731201, "Y": 0.3916538655757904}, {"X": 0.5677362084388733, "Y": 0.4513724446296692}, {"X": 0.5512338280677795, "Y": 0.45162245631217957}, {"X": 0.5501558780670166, "Y": 0.3918021619319916}]}, "Id": "9f9e3251-766e-490e-81e2-4607f5d42351"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020884331315755844, "Height": 0.08182689547538757, "Left": 0.5485273599624634, "Top": 0.4838936924934387}, "Polygon": [{"X": 0.5678566098213196, "Y": 0.4838936924934387}, {"X": 0.5694116950035095, "Y": 0.5651991367340088}, {"X": 0.5499769449234009, "Y": 0.5657206177711487}, {"X": 0.5485273599624634, "Y": 0.4842510223388672}]}, "Id": "a981e5b8-d694-4e77-9b9f-4210b47221d4"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015900444239377975, "Height": 0.040767449885606766, "Left": 0.5544348359107971, "Top": 0.588053822517395}, "Polygon": [{"X": 0.569566011428833, "Y": 0.588053822517395}, {"X": 0.570335328578949, "Y": 0.6283159255981445}, {"X": 0.555163562297821, "Y": 0.6288212537765503}, {"X": 0.5544348359107971, "Y": 0.5884952545166016}]}, "Id": "df775379-581a-43da-be3c-6399eb2467a2"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01609022542834282, "Height": 0.041544124484062195, "Left": 0.5555955171585083, "Top": 0.6407409310340881}, "Polygon": [{"X": 0.570902407169342, "Y": 0.6407409310340881}, {"X": 0.5716857314109802, "Y": 0.6816896796226501}, {"X": 0.5563372373580933, "Y": 0.6822850108146667}, {"X": 0.5555955171585083, "Y": 0.6412702798843384}]}, "Id": "2387686e-058c-479b-ac6a-5b7e2d48d744"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016488369554281235, "Height": 0.05148951709270477, "Left": 0.5556374788284302, "Top": 0.7466084957122803}, "Polygon": [{"X": 0.5711620450019836, "Y": 0.7466084957122803}, {"X": 0.5721258521080017, "Y": 0.7973089218139648}, {"X": 0.5565494298934937, "Y": 0.7980979681015015}, {"X": 0.5556374788284302, "Y": 0.7473140358924866}]}, "Id": "def7c7b2-c06f-4d99-abf9-931bd328c07a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017225226387381554, "Height": 0.03472035005688667, "Left": 0.5297978520393372, "Top": 0.09927887469530106}, "Polygon": [{"X": 0.5464000105857849, "Y": 0.09962563961744308}, {"X": 0.5470230579376221, "Y": 0.13399922847747803}, {"X": 0.5303815007209778, "Y": 0.13371017575263977}, {"X": 0.5297978520393372, "Y": 0.09927887469530106}]}, "Id": "866c3f83-f8a0-42f5-b7af-53764ceea159"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01816115528345108, "Height": 0.05400786176323891, "Left": 0.5201149582862854, "Top": 0.25439542531967163}, "Polygon": [{"X": 0.5373436212539673, "Y": 0.25448212027549744}, {"X": 0.5382760763168335, "Y": 0.3083949685096741}, {"X": 0.520983874797821, "Y": 0.3084033131599426}, {"X": 0.5201149582862854, "Y": 0.25439542531967163}]}, "Id": "dc8efa37-e23d-4347-9a05-171db8774dae"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01535533182322979, "Height": 0.03885648772120476, "Left": 0.5241664052009583, "Top": 0.3210827112197876}, "Polygon": [{"X": 0.5388501882553101, "Y": 0.3210827112197876}, {"X": 0.5395217537879944, "Y": 0.3598546087741852}, {"X": 0.5247992277145386, "Y": 0.35993918776512146}, {"X": 0.5241664052009583, "Y": 0.3211088180541992}]}, "Id": "b9a20c86-b553-43d4-bffe-c9e105cbd378"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014863758347928524, "Height": 0.009643830358982086, "Left": 0.5286896824836731, "Top": 0.41149279475212097}, "Polygon": [{"X": 0.5433875322341919, "Y": 0.41149279475212097}, {"X": 0.5435534715652466, "Y": 0.42096012830734253}, {"X": 0.5288462042808533, "Y": 0.42113661766052246}, {"X": 0.5286896824836731, "Y": 0.41165491938591003}]}, "Id": "e8601fed-3dbb-416c-b811-f48f0b431bb8"}, {"BlockType": "WORD", "Confidence": 97.12471771240234, "Text": "102.493", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016627725213766098, "Height": 0.07150653749704361, "Left": 0.5295024514198303, "Top": 0.49628162384033203}, "Polygon": [{"X": 0.544884204864502, "Y": 0.49628162384033203}, {"X": 0.5461301803588867, "Y": 0.5673710703849792}, {"X": 0.5306749939918518, "Y": 0.5677881836891174}, {"X": 0.5295024514198303, "Y": 0.49658480286598206}]}, "Id": "f8297d0f-6ed1-4f15-a5f3-938702800e8d"}, {"BlockType": "WORD", "Confidence": 99.7217788696289, "Text": "3.339", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01609494909644127, "Height": 0.052821725606918335, "Left": 0.5322599411010742, "Top": 0.6317789554595947}, "Polygon": [{"X": 0.5474388599395752, "Y": 0.6317789554595947}, {"X": 0.5483549237251282, "Y": 0.6840075850486755}, {"X": 0.533123254776001, "Y": 0.6846006512641907}, {"X": 0.5322599411010742, "Y": 0.6322886943817139}]}, "Id": "6ba09c47-5a9e-4a23-92e4-8ced9866f98d"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "342.22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016550075262784958, "Height": 0.06340805441141129, "Left": 0.5322934985160828, "Top": 0.7374754548072815}, "Polygon": [{"X": 0.5477518439292908, "Y": 0.7374754548072815}, {"X": 0.5488435626029968, "Y": 0.8000946640968323}, {"X": 0.5333212614059448, "Y": 0.8008835315704346}, {"X": 0.5322934985160828, "Y": 0.7381619215011597}]}, "Id": "3864710c-d1ad-44cd-b5c0-d441b5985d32"}, {"BlockType": "WORD", "Confidence": 97.96875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014174687676131725, "Height": 0.008950293995440006, "Left": 0.511184811592102, "Top": 0.0981152355670929}, "Polygon": [{"X": 0.5252155661582947, "Y": 0.09840939939022064}, {"X": 0.5253595113754272, "Y": 0.10706552863121033}, {"X": 0.5113203525543213, "Y": 0.10678359121084213}, {"X": 0.511184811592102, "Y": 0.0981152355670929}]}, "Id": "fba4ed5c-09ba-4981-af00-c474f99b1deb"}, {"BlockType": "WORD", "Confidence": 94.41824340820312, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014603214338421822, "Height": 0.009474918246269226, "Left": 0.5119909048080444, "Top": 0.11535277217626572}, "Polygon": [{"X": 0.5264405608177185, "Y": 0.11563034355640411}, {"X": 0.5265941023826599, "Y": 0.12482769042253494}, {"X": 0.5121352076530457, "Y": 0.12456353008747101}, {"X": 0.5119909048080444, "Y": 0.11535277217626572}]}, "Id": "294fd518-c05e-46b9-ab9d-d1b877b1e411"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "DEF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01576828397810459, "Height": 0.028315860778093338, "Left": 0.5002031922340393, "Top": 0.2812064290046692}, "Polygon": [{"X": 0.5155265927314758, "Y": 0.2812415361404419}, {"X": 0.5159714818000793, "Y": 0.3095131516456604}, {"X": 0.5006184577941895, "Y": 0.3095223009586334}, {"X": 0.5002031922340393, "Y": 0.2812064290046692}]}, "Id": "998d2ed3-676d-49b7-8b0d-1b18d4159ccf"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Bulk", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01598314940929413, "Height": 0.03881442919373512, "Left": 0.5020215511322021, "Top": 0.32053670287132263}, "Polygon": [{"X": 0.5173918604850769, "Y": 0.32053670287132263}, {"X": 0.5180047154426575, "Y": 0.35926371812820435}, {"X": 0.5025937557220459, "Y": 0.35935112833976746}, {"X": 0.5020215511322021, "Y": 0.3205631375312805}]}, "Id": "8c117187-c37f-4509-9fc1-c35b3f3b4be8"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015248531475663185, "Height": 0.009746819734573364, "Left": 0.5059031248092651, "Top": 0.4111149311065674}, "Polygon": [{"X": 0.5209988355636597, "Y": 0.4111149311065674}, {"X": 0.5211516618728638, "Y": 0.42068132758140564}, {"X": 0.5060461759567261, "Y": 0.42086175084114075}, {"X": 0.5059031248092651, "Y": 0.41128048300743103}]}, "Id": "d307307b-2f3f-44de-bdb5-37b2820694bd"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "13.390", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01628631353378296, "Height": 0.061124660074710846, "Left": 0.5073559284210205, "Top": 0.5065613389015198}, "Polygon": [{"X": 0.5226717591285706, "Y": 0.5065613389015198}, {"X": 0.5236422419548035, "Y": 0.5672720670700073}, {"X": 0.5082637667655945, "Y": 0.5676859617233276}, {"X": 0.5073559284210205, "Y": 0.5068785548210144}]}, "Id": "6814fe6b-a4d8-4532-a72b-810637a647fc"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "3.599", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016430038958787918, "Height": 0.053497135639190674, "Left": 0.5090845227241516, "Top": 0.6318365931510925}, "Polygon": [{"X": 0.5246691703796387, "Y": 0.6318365931510925}, {"X": 0.525514543056488, "Y": 0.6847250461578369}, {"X": 0.5098749399185181, "Y": 0.6853337287902832}, {"X": 0.5090845227241516, "Y": 0.6323588490486145}]}, "Id": "6a42c97e-db6e-4567-8663-6f49fed20308"}, {"BlockType": "WORD", "Confidence": 99.64205932617188, "Text": "48.19", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01694946363568306, "Height": 0.0536964125931263, "Left": 0.509276807308197, "Top": 0.7491553425788879}, "Polygon": [{"X": 0.5253852009773254, "Y": 0.7491553425788879}, {"X": 0.5262262225151062, "Y": 0.8020289540290833}, {"X": 0.5100614428520203, "Y": 0.8028517961502075}, {"X": 0.509276807308197, "Y": 0.7498882412910461}]}, "Id": "149ecb44-dfee-42c8-b6ef-de0df4da1f05"}, {"BlockType": "WORD", "Confidence": 96.55193328857422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01441532839089632, "Height": 0.009096452966332436, "Left": 0.4899387061595917, "Top": 0.09668527543544769}, "Polygon": [{"X": 0.5042208433151245, "Y": 0.09698613733053207}, {"X": 0.5043540596961975, "Y": 0.10578172653913498}, {"X": 0.4900631606578827, "Y": 0.10549348592758179}, {"X": 0.4899387061595917, "Y": 0.09668527543544769}]}, "Id": "2ba9658e-ba2c-4e37-8a81-66a507bf236c"}, {"BlockType": "WORD", "Confidence": 96.17028045654297, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014887466095387936, "Height": 0.009270207956433296, "Left": 0.49071261286735535, "Top": 0.11405960470438004}, "Polygon": [{"X": 0.5054634213447571, "Y": 0.1143442839384079}, {"X": 0.505600094795227, "Y": 0.12332981079816818}, {"X": 0.4908401072025299, "Y": 0.12305847555398941}, {"X": 0.49071261286735535, "Y": 0.11405960470438004}]}, "Id": "a6acbdf2-1af1-49a6-89d0-d9d833378ad6"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018316611647605896, "Height": 0.07282879203557968, "Left": 0.45016181468963623, "Top": 0.20487375557422638}, "Polygon": [{"X": 0.4675738513469696, "Y": 0.20504814386367798}, {"X": 0.4684784412384033, "Y": 0.27770254015922546}, {"X": 0.4509789049625397, "Y": 0.2776563763618469}, {"X": 0.45016181468963623, "Y": 0.20487375557422638}]}, "Id": "923f54fc-e233-44e4-bc81-fc18af0b2348"}, {"BlockType": "WORD", "Confidence": 99.10255432128906, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007783898152410984, "Height": 0.00940658338367939, "Left": 0.4580841362476349, "Top": 0.2900635004043579}, "Polygon": [{"X": 0.4657529592514038, "Y": 0.29007411003112793}, {"X": 0.4658680558204651, "Y": 0.29947009682655334}, {"X": 0.4581942856311798, "Y": 0.2994668185710907}, {"X": 0.4580841362476349, "Y": 0.2900635004043579}]}, "Id": "8f76bf05-3b6b-4b58-be4b-2c5165daec87"}, {"BlockType": "WORD", "Confidence": 99.44674682617188, "Text": "390.41", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01707647182047367, "Height": 0.05797561630606651, "Left": 0.45633840560913086, "Top": 0.3405677378177643}, "Polygon": [{"X": 0.4726812541484833, "Y": 0.3405677378177643}, {"X": 0.4734148681163788, "Y": 0.39838549494743347}, {"X": 0.4570073187351227, "Y": 0.3985433578491211}, {"X": 0.45633840560913086, "Y": 0.3406289517879486}]}, "Id": "d5332c3f-63ea-4b4a-a67b-7b037c46cc9a"}, {"BlockType": "WORD", "Confidence": 99.81205749511719, "Text": "sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015923408791422844, "Height": 0.04601700231432915, "Left": 0.4043056070804596, "Top": 0.19344432651996613}, "Polygon": [{"X": 0.41981184482574463, "Y": 0.19361679255962372}, {"X": 0.4202290177345276, "Y": 0.23946131765842438}, {"X": 0.404673308134079, "Y": 0.23936036229133606}, {"X": 0.4043056070804596, "Y": 0.19344432651996613}]}, "Id": "63b6445a-0442-4bad-bd93-25ce5f7b33a3"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015241770073771477, "Height": 0.028311235830187798, "Left": 0.40613675117492676, "Top": 0.2505056858062744}, "Polygon": [{"X": 0.4211200475692749, "Y": 0.2505860924720764}, {"X": 0.4213785231113434, "Y": 0.27881693840026855}, {"X": 0.40636590123176575, "Y": 0.27877920866012573}, {"X": 0.40613675117492676, "Y": 0.2505056858062744}]}, "Id": "d580650b-cda0-447a-bed5-4d1794516bd9"}, {"BlockType": "WORD", "Confidence": 99.17988586425781, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008068302646279335, "Height": 0.009518776088953018, "Left": 0.4111920893192291, "Top": 0.28931546211242676}, "Polygon": [{"X": 0.4191748797893524, "Y": 0.2893270254135132}, {"X": 0.4192603826522827, "Y": 0.2988342344760895}, {"X": 0.4112723469734192, "Y": 0.2988303005695343}, {"X": 0.4111920893192291, "Y": 0.28931546211242676}]}, "Id": "a9a80c0e-3b26-450e-a011-65460e391de2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016406897455453873, "Height": 0.04035699740052223, "Left": 0.41010433435440063, "Top": 0.35954469442367554}, "Polygon": [{"X": 0.4261319637298584, "Y": 0.35954469442367554}, {"X": 0.4265112280845642, "Y": 0.3997455835342407}, {"X": 0.41043931245803833, "Y": 0.39990168809890747}, {"X": 0.41010433435440063, "Y": 0.35963520407676697}]}, "Id": "b479094d-4c3f-47ab-99d7-38eb929d8fa3"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016616815701127052, "Height": 0.048019252717494965, "Left": 0.38260895013809204, "Top": 0.23022744059562683}, "Polygon": [{"X": 0.39886143803596497, "Y": 0.2303476333618164}, {"X": 0.39922574162483215, "Y": 0.2782467007637024}, {"X": 0.3829191327095032, "Y": 0.27820485830307007}, {"X": 0.38260895013809204, "Y": 0.23022744059562683}]}, "Id": "5b202ccd-c8c7-4fc6-a498-d5c5892826a3"}, {"BlockType": "WORD", "Confidence": 99.24007415771484, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00797341763973236, "Height": 0.009443344548344612, "Left": 0.38788947463035583, "Top": 0.2886587083339691}, "Polygon": [{"X": 0.39579346776008606, "Y": 0.28867068886756897}, {"X": 0.395862877368927, "Y": 0.2981020510196686}, {"X": 0.3879537284374237, "Y": 0.2980976104736328}, {"X": 0.38788947463035583, "Y": 0.2886587083339691}]}, "Id": "c161d262-6f56-4baa-aad6-cf6d35bb7e72"}, {"BlockType": "WORD", "Confidence": 99.4971694946289, "Text": "390.41", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016539933159947395, "Height": 0.059432972222566605, "Left": 0.385978639125824, "Top": 0.33880704641342163}, "Polygon": [{"X": 0.4020579159259796, "Y": 0.33880704641342163}, {"X": 0.4025185704231262, "Y": 0.398086279630661}, {"X": 0.3863735496997833, "Y": 0.39824002981185913}, {"X": 0.385978639125824, "Y": 0.3388639986515045}]}, "Id": "aabbf366-d332-4d0f-8b15-13b05997479f"}, {"BlockType": "WORD", "Confidence": 99.35327911376953, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02014216221868992, "Height": 0.10357760637998581, "Left": 0.3845007121562958, "Top": 0.4211946725845337}, "Polygon": [{"X": 0.4038332402706146, "Y": 0.4211946725845337}, {"X": 0.40464285016059875, "Y": 0.5243388414382935}, {"X": 0.3851735591888428, "Y": 0.5247722864151001}, {"X": 0.3845007121562958, "Y": 0.4214238226413727}]}, "Id": "3c99e825-eb97-4e00-a9fa-2e1f81af5f1b"}, {"BlockType": "WORD", "Confidence": 99.4971694946289, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015373241156339645, "Height": 0.007439770270138979, "Left": 0.36677682399749756, "Top": 0.5830105543136597}, "Polygon": [{"X": 0.38210615515708923, "Y": 0.5830105543136597}, {"X": 0.3821500837802887, "Y": 0.5900082588195801}, {"X": 0.36681345105171204, "Y": 0.5904502868652344}, {"X": 0.36677682399749756, "Y": 0.5834415555000305}]}, "Id": "6bb38ea4-813f-4bf8-9433-b3e3a61a7836"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "agree", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016099493950605392, "Height": 0.0524742528796196, "Left": 0.36248838901519775, "Top": 0.6036953926086426}, "Polygon": [{"X": 0.37827619910240173, "Y": 0.6036953926086426}, {"X": 0.37858787178993225, "Y": 0.655608594417572}, {"X": 0.36274442076683044, "Y": 0.6561696529388428}, {"X": 0.36248838901519775, "Y": 0.6041719913482666}]}, "Id": "81b6f0ae-a884-4126-b3df-2cc7b50219e1"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015837155282497406, "Height": 0.021660838276147842, "Left": 0.3666972815990448, "Top": 0.6676799058914185}, "Polygon": [{"X": 0.3824026882648468, "Y": 0.6676799058914185}, {"X": 0.3825344443321228, "Y": 0.6887311339378357}, {"X": 0.36680668592453003, "Y": 0.6893407106399536}, {"X": 0.3666972815990448, "Y": 0.6682553887367249}]}, "Id": "fe938bd0-2953-4225-93f3-cbee2712cb5a"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016161218285560608, "Height": 0.031109660863876343, "Left": 0.3627236783504486, "Top": 0.7021934390068054}, "Polygon": [{"X": 0.3787026107311249, "Y": 0.7021934390068054}, {"X": 0.378884881734848, "Y": 0.7326119542121887}, {"X": 0.3628731667995453, "Y": 0.7333030700683594}, {"X": 0.3627236783504486, "Y": 0.7028341889381409}]}, "Id": "491866a7-b8ce-43a5-af1c-6f1195f90c91"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016275322064757347, "Height": 0.053474489599466324, "Left": 0.3667406439781189, "Top": 0.7449039816856384}, "Polygon": [{"X": 0.3826870024204254, "Y": 0.7449039816856384}, {"X": 0.3830159604549408, "Y": 0.7975828051567078}, {"X": 0.367013156414032, "Y": 0.7983784675598145}, {"X": 0.3667406439781189, "Y": 0.7456122636795044}]}, "Id": "f761ab4e-5120-465c-a377-e0ec8e47b81c"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01619572751224041, "Height": 0.06131460890173912, "Left": 0.36699196696281433, "Top": 0.8107020854949951}, "Polygon": [{"X": 0.38281163573265076, "Y": 0.8107020854949951}, {"X": 0.3831877112388611, "Y": 0.8711093664169312}, {"X": 0.36730411648750305, "Y": 0.8720167279243469}, {"X": 0.36699196696281433, "Y": 0.8115095496177673}]}, "Id": "4194333a-b7e6-4037-b7bd-963fc67d24a5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "according", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020268253982067108, "Height": 0.09601223468780518, "Left": 0.33829715847969055, "Top": 0.5391896963119507}, "Polygon": [{"X": 0.35812243819236755, "Y": 0.5391896963119507}, {"X": 0.35856541991233826, "Y": 0.6345389485359192}, {"X": 0.3386109173297882, "Y": 0.6352019309997559}, {"X": 0.33829715847969055, "Y": 0.5396585464477539}]}, "Id": "f2168c7e-c314-458a-b6f1-56ecca6a9837"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015880541875958443, "Height": 0.020441973581910133, "Left": 0.3427649736404419, "Top": 0.6466458439826965}, "Polygon": [{"X": 0.35855332016944885, "Y": 0.6466458439826965}, {"X": 0.3586455285549164, "Y": 0.6665117144584656}, {"X": 0.3428359031677246, "Y": 0.6670877933502197}, {"X": 0.3427649736404419, "Y": 0.6471895575523376}]}, "Id": "13d9f375-f948-4963-b49d-34040d72e3df"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01606982760131359, "Height": 0.043088022619485855, "Left": 0.3422921299934387, "Top": 0.6792965531349182}, "Polygon": [{"X": 0.3581666350364685, "Y": 0.6792965531349182}, {"X": 0.35836195945739746, "Y": 0.7217162847518921}, {"X": 0.3424418866634369, "Y": 0.7223845720291138}, {"X": 0.3422921299934387, "Y": 0.6798953413963318}]}, "Id": "4e4ca92e-43aa-41be-86b4-f01d7dca5b77"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "issuer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0159496758133173, "Height": 0.06485084444284439, "Left": 0.34258103370666504, "Top": 0.7359729409217834}, "Polygon": [{"X": 0.35823652148246765, "Y": 0.7359729409217834}, {"X": 0.3585307002067566, "Y": 0.8000400066375732}, {"X": 0.3428075909614563, "Y": 0.8008237481117249}, {"X": 0.34258103370666504, "Y": 0.7366525530815125}]}, "Id": "4df0bc56-1b7a-437a-91b7-b63a19ef795d"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "agreement.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02114703878760338, "Height": 0.10002277791500092, "Left": 0.33878597617149353, "Top": 0.8116282224655151}, "Polygon": [{"X": 0.35947364568710327, "Y": 0.8116282224655151}, {"X": 0.3599330186843872, "Y": 0.9103820323944092}, {"X": 0.33910825848579407, "Y": 0.9116509556770325}, {"X": 0.33878597617149353, "Y": 0.8126835823059082}]}, "Id": "afed67e2-679e-44bc-bf5b-56b054a9fdf5"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Salesperson", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018542425706982613, "Height": 0.10237531363964081, "Left": 0.2849024832248688, "Top": 0.12424083054065704}, "Polygon": [{"X": 0.30335211753845215, "Y": 0.12457014620304108}, {"X": 0.30344492197036743, "Y": 0.22661614418029785}, {"X": 0.2849024832248688, "Y": 0.22647342085838318}, {"X": 0.2849425971508026, "Y": 0.12424083054065704}]}, "Id": "3a67224d-2050-4bac-a73c-c2cc5d55e438"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015658516436815262, "Height": 0.024859214201569557, "Left": 0.2887962758541107, "Top": 0.23951023817062378}, "Polygon": [{"X": 0.30443060398101807, "Y": 0.23961028456687927}, {"X": 0.3044548034667969, "Y": 0.2643694579601288}, {"X": 0.2887962758541107, "Y": 0.26430797576904297}, {"X": 0.2887992262840271, "Y": 0.23951023817062378}]}, "Id": "16ad374a-203c-48d7-b4fa-e1290c71d369"}, {"BlockType": "WORD", "Confidence": 97.89122009277344, "Text": "AUTO/SMARTFUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017940452322363853, "Height": 0.14385169744491577, "Left": 0.28906768560409546, "Top": 0.27711421251296997}, "Polygon": [{"X": 0.30684390664100647, "Y": 0.27716130018234253}, {"X": 0.30700814723968506, "Y": 0.42075610160827637}, {"X": 0.28906768560409546, "Y": 0.42096590995788574}, {"X": 0.289081871509552, "Y": 0.27711421251296997}]}, "Id": "6b85cc09-d2b1-4fb2-b03f-000621c466aa"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0160747691988945, "Height": 0.07429402321577072, "Left": 0.2934826910495758, "Top": 0.5842602252960205}, "Polygon": [{"X": 0.30946168303489685, "Y": 0.5842602252960205}, {"X": 0.3095574378967285, "Y": 0.6579856276512146}, {"X": 0.29349780082702637, "Y": 0.658554196357727}, {"X": 0.2934826910495758, "Y": 0.5847082138061523}]}, "Id": "a100430a-7c9d-41c0-8ba8-17262bbdf096"}, {"BlockType": "WORD", "Confidence": 98.63380432128906, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016612417995929718, "Height": 0.019690178334712982, "Left": 0.2931039333343506, "Top": 0.6699884533882141}, "Polygon": [{"X": 0.309691458940506, "Y": 0.6699884533882141}, {"X": 0.3097163438796997, "Y": 0.6890389919281006}, {"X": 0.2931073307991028, "Y": 0.6896786093711853}, {"X": 0.2931039333343506, "Y": 0.670595645904541}]}, "Id": "566f9017-75eb-4c6b-a695-88a89ab2feb2"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "0099991", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016234565526247025, "Height": 0.07547038793563843, "Left": 0.2936166822910309, "Top": 0.7031947374343872}, "Polygon": [{"X": 0.30975350737571716, "Y": 0.7031947374343872}, {"X": 0.3098512291908264, "Y": 0.777896523475647}, {"X": 0.2936325669288635, "Y": 0.7786651253700256}, {"X": 0.2936166822910309, "Y": 0.7038390040397644}]}, "Id": "c3072664-ccaf-4d59-9b19-3b8e8abca9f2"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015484544448554516, "Height": 0.04602881148457527, "Left": 0.2648612856864929, "Top": 0.1425343155860901}, "Polygon": [{"X": 0.2803458273410797, "Y": 0.14278124272823334}, {"X": 0.28031304478645325, "Y": 0.18856312334537506}, {"X": 0.2648612856864929, "Y": 0.18838587403297424}, {"X": 0.26494401693344116, "Y": 0.1425343155860901}]}, "Id": "0d0f8acd-5f52-4e3a-a4ff-92988ed36fa7"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01598084159195423, "Height": 0.06591423600912094, "Left": 0.2646719515323639, "Top": 0.19839687645435333}, "Polygon": [{"X": 0.280652791261673, "Y": 0.19856302440166473}, {"X": 0.28060728311538696, "Y": 0.2643111050128937}, {"X": 0.2646719515323639, "Y": 0.2642485797405243}, {"X": 0.26479101181030273, "Y": 0.19839687645435333}]}, "Id": "00c23d49-6781-4776-a4d9-d0f20fcdc965"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "3482", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0161345936357975, "Height": 0.03902726620435715, "Left": 0.26544034481048584, "Top": 0.27680063247680664}, "Polygon": [{"X": 0.28157493472099304, "Y": 0.2768436074256897}, {"X": 0.28155064582824707, "Y": 0.3158084452152252}, {"X": 0.26544034481048584, "Y": 0.3158279061317444}, {"X": 0.2655085623264313, "Y": 0.27680063247680664}]}, "Id": "1525ac33-29d0-4d2b-882a-0b43c02a8ab8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Merchant", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016702592372894287, "Height": 0.08474771678447723, "Left": 0.269394189119339, "Top": 0.5178279876708984}, "Polygon": [{"X": 0.2860967814922333, "Y": 0.5178279876708984}, {"X": 0.2860713005065918, "Y": 0.6020795702934265}, {"X": 0.269394189119339, "Y": 0.6025757193565369}, {"X": 0.2695159614086151, "Y": 0.5181819796562195}]}, "Id": "3b7535e3-6977-4b33-b112-1fae687458d7"}, {"BlockType": "WORD", "Confidence": 99.40290832519531, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0167885422706604, "Height": 0.07567562907934189, "Left": 0.2692263722419739, "Top": 0.6150276064872742}, "Polygon": [{"X": 0.2860149145126343, "Y": 0.6150276064872742}, {"X": 0.28599196672439575, "Y": 0.6900574564933777}, {"X": 0.2692263722419739, "Y": 0.690703272819519}, {"X": 0.2693350315093994, "Y": 0.6155453324317932}]}, "Id": "9c5ed05c-cb63-4e12-b8f8-7b095495eeb3"}, {"BlockType": "WORD", "Confidence": 95.302734375, "Text": "*******6793", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01679382286965847, "Height": 0.12199568748474121, "Left": 0.2697587311267853, "Top": 0.7032116651535034}, "Polygon": [{"X": 0.2865525484085083, "Y": 0.7032116651535034}, {"X": 0.2865201532840729, "Y": 0.8243372440338135}, {"X": 0.2697587311267853, "Y": 0.8252073526382446}, {"X": 0.2699282169342041, "Y": 0.703873872756958}]}, "Id": "ec5e5aec-157c-44b3-aed6-bf8a7468eefb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015899673104286194, "Height": 0.037502143532037735, "Left": 0.24184885621070862, "Top": 0.15151552855968475}, "Polygon": [{"X": 0.2577485144138336, "Y": 0.1517537534236908}, {"X": 0.25766241550445557, "Y": 0.1890176683664322}, {"X": 0.24184885621070862, "Y": 0.18883739411830902}, {"X": 0.24197663366794586, "Y": 0.15151552855968475}]}, "Id": "d07629bf-fe4b-46cd-934f-522fdb344b4a"}, {"BlockType": "WORD", "Confidence": 99.6827163696289, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016264135017991066, "Height": 0.0663263350725174, "Left": 0.24095016717910767, "Top": 0.19815753400325775}, "Polygon": [{"X": 0.2572143077850342, "Y": 0.19832547008991241}, {"X": 0.2570594549179077, "Y": 0.26448386907577515}, {"X": 0.24095016717910767, "Y": 0.2644210755825043}, {"X": 0.24118001759052277, "Y": 0.19815753400325775}]}, "Id": "ac10f695-6faf-461a-9fa8-f05211d0438e"}, {"BlockType": "WORD", "Confidence": 31.300024032592773, "Text": "xxxxxxxxxxxxxx1773", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01942383497953415, "Height": 0.18799394369125366, "Left": 0.24033111333847046, "Top": 0.2756681740283966}, "Polygon": [{"X": 0.2597549557685852, "Y": 0.27572038769721985}, {"X": 0.2593514621257782, "Y": 0.46336013078689575}, {"X": 0.24033111333847046, "Y": 0.46366211771965027}, {"X": 0.2409822642803192, "Y": 0.2756681740283966}]}, "Id": "867788bc-8803-4647-bc21-b787127b4ad8"}, {"BlockType": "WORD", "Confidence": 97.74812316894531, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01691046729683876, "Height": 0.0536189042031765, "Left": 0.24381227791309357, "Top": 0.47487378120422363}, "Polygon": [{"X": 0.2607227563858032, "Y": 0.47487378120422363}, {"X": 0.2606133818626404, "Y": 0.****************}, {"X": 0.24381227791309357, "Y": 0.5284926891326904}, {"X": 0.2439834177494049, "Y": 0.4751587510108948}]}, "Id": "24f6880f-1767-438d-86c5-cc3eef30a1bc"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Loyalty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02007574401795864, "Height": 0.06404753029346466, "Left": 0.21540966629981995, "Top": 0.13327564299106598}, "Polygon": [{"X": 0.23548540472984314, "Y": 0.1336086541414261}, {"X": 0.23523776233196259, "Y": 0.19732315838336945}, {"X": 0.21540966629981995, "Y": 0.19711396098136902}, {"X": 0.21574684977531433, "Y": 0.13327564299106598}]}, "Id": "3a3d3619-8b84-487b-b865-73a709ffd9ad"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Card#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016117479652166367, "Height": 0.05574703961610794, "Left": 0.21827785670757294, "Top": 0.20775485038757324}, "Polygon": [{"X": 0.2343953251838684, "Y": 0.20790530741214752}, {"X": 0.23417609930038452, "Y": 0.2635018825531006}, {"X": 0.21827785670757294, "Y": 0.26343852281570435}, {"X": 0.2185594141483307, "Y": 0.20775485038757324}]}, "Id": "36c807d4-c64b-4e03-9f56-6f5012364c9f"}, {"BlockType": "WORD", "Confidence": 87.30209350585938, "Text": "xxxxxxxxxxxx8997", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019275914877653122, "Height": 0.1677960604429245, "Left": 0.21648254990577698, "Top": 0.27588140964508057}, "Polygon": [{"X": 0.2357584685087204, "Y": 0.2759321331977844}, {"X": 0.2351171374320984, "Y": 0.****************}, {"X": 0.21648254990577698, "Y": 0.*****************}, {"X": 0.21734125912189484, "Y": 0.27588140964508057}]}, "Id": "139ef8e9-e0c5-42fb-a4d4-9b8c5ebe54b4"}, {"BlockType": "WORD", "Confidence": 98.4119873046875, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017064670100808144, "Height": 0.05403449758887291, "Left": 0.2198883444070816, "Top": 0.45348793268203735}, "Polygon": [{"X": 0.236953005194664, "Y": 0.45348793268203735}, {"X": 0.23675437271595, "Y": 0.5071819424629211}, {"X": 0.2198883444070816, "Y": 0.507522463798523}, {"X": 0.22014977037906647, "Y": 0.45373767614364624}]}, "Id": "3bac0f8c-2a22-4018-bf45-185705629c1b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022300733253359795, "Height": 0.05980698764324188, "Left": 0.16962340474128723, "Top": 0.09145893156528473}, "Polygon": [{"X": 0.19192413985729218, "Y": 0.09191478788852692}, {"X": 0.19150954484939575, "Y": 0.1512659192085266}, {"X": 0.16962340474128723, "Y": 0.15093643963336945}, {"X": 0.17013077437877655, "Y": 0.09145893156528473}]}, "Id": "fe4ede06-573a-438c-b048-a1cb33aadcd4"}, {"BlockType": "WORD", "Confidence": 98.29659271240234, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021542329341173172, "Height": 0.07319842278957367, "Left": 0.16682791709899902, "Top": 0.16136030852794647}, "Polygon": [{"X": 0.1883702427148819, "Y": 0.1616535186767578}, {"X": 0.18784518539905548, "Y": 0.23455873131752014}, {"X": 0.16682791709899902, "Y": 0.23441527783870697}, {"X": 0.16746178269386292, "Y": 0.16136030852794647}]}, "Id": "456707fc-7779-4b8c-9bd7-d3a0b29e0034"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "NBS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016751142218708992, "Height": 0.028868108987808228, "Left": 0.16963496804237366, "Top": 0.24742697179317474}, "Polygon": [{"X": 0.1863861083984375, "Y": 0.24751849472522736}, {"X": 0.1861761212348938, "Y": 0.2762950658798218}, {"X": 0.16963496804237366, "Y": 0.27625036239624023}, {"X": 0.16987867653369904, "Y": 0.24742697179317474}]}, "Id": "96d125d0-d717-4711-8e9e-d4cc30d0cc8b"}, {"BlockType": "WORD", "Confidence": 95.53092956542969, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016361692920327187, "Height": 0.00740841357037425, "Left": 0.16921338438987732, "Top": 0.287677139043808}, "Polygon": [{"X": 0.18557506799697876, "Y": 0.28770285844802856}, {"X": 0.1855209320783615, "Y": 0.2950855493545532}, {"X": 0.16921338438987732, "Y": 0.29507169127464294}, {"X": 0.16927602887153625, "Y": 0.287677139043808}]}, "Id": "3dace52f-bddb-4a46-86bd-ec9ac8b7877d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017041491344571114, "Height": 0.040045365691185, "Left": 0.1679452806711197, "Top": 0.3065754771232605}, "Polygon": [{"X": 0.18498677015304565, "Y": 0.3065754771232605}, {"X": 0.1846923530101776, "Y": 0.34654998779296875}, {"X": 0.1679452806711197, "Y": 0.3466208577156067}, {"X": 0.16828687489032745, "Y": 0.30658024549484253}]}, "Id": "9363cc80-ca4e-4fd6-878e-db9d1f3f21b8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017521191388368607, "Height": 0.04084458202123642, "Left": 0.16652174293994904, "Top": 0.357634037733078}, "Polygon": [{"X": 0.18404293060302734, "Y": 0.357634037733078}, {"X": 0.18374167382717133, "Y": 0.398317813873291}, {"X": 0.16652174293994904, "Y": 0.398478627204895}, {"X": 0.16687217354774475, "Y": 0.35772547125816345}]}, "Id": "1738c0b9-4b5f-4f11-b894-ef0aaf037fb4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02010629139840603, "Height": 0.0549275279045105, "Left": 0.1663244068622589, "Top": 0.40978068113327026}, "Polygon": [{"X": 0.1864306926727295, "Y": 0.40978068113327026}, {"X": 0.18603679537773132, "Y": 0.4643954932689667}, {"X": 0.1663244068622589, "Y": 0.46470820903778076}, {"X": 0.166793555021286, "Y": 0.40998634696006775}]}, "Id": "5bb6662f-4257-4fc0-8f7a-638de3ce2e2f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016603980213403702, "Height": 0.025969035923480988, "Left": 0.13005051016807556, "Top": 0.09279213845729828}, "Polygon": [{"X": 0.14665448665618896, "Y": 0.09312988072633743}, {"X": 0.14639322459697723, "Y": 0.11876117438077927}, {"X": 0.13005051016807556, "Y": 0.11846400797367096}, {"X": 0.13034187257289886, "Y": 0.09279213845729828}]}, "Id": "4abefee7-30eb-4a2f-96cc-abf5f77924db"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TAXES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01793166995048523, "Height": 0.04476528242230415, "Left": 0.12652821838855743, "Top": 0.12648116052150726}, "Polygon": [{"X": 0.14445988833904266, "Y": 0.1267840713262558}, {"X": 0.1440008580684662, "Y": 0.1712464541196823}, {"X": 0.12652821838855743, "Y": 0.17101895809173584}, {"X": 0.12704287469387054, "Y": 0.12648116052150726}]}, "Id": "9d6d031e-5532-4ebc-830a-98dff70f12e8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "INCLUDED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01888340525329113, "Height": 0.07404254376888275, "Left": 0.12226931005716324, "Top": 0.18196682631969452}, "Polygon": [{"X": 0.14115270972251892, "Y": 0.1821819245815277}, {"X": 0.14037629961967468, "Y": 0.25600937008857727}, {"X": 0.12226931005716324, "Y": 0.2559245526790619}, {"X": 0.12314091622829437, "Y": 0.18196682631969452}]}, "Id": "5b3cba19-2fb0-486e-9f3f-eeed5e617bc0"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "IN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016957318410277367, "Height": 0.0175484586507082, "Left": 0.12055356055498123, "Top": 0.2674286365509033}, "Polygon": [{"X": 0.13751088082790375, "Y": 0.26748818159103394}, {"X": 0.13732360303401947, "Y": 0.2849770784378052}, {"X": 0.12055356055498123, "Y": 0.2849462628364563}, {"X": 0.1207616925239563, "Y": 0.2674286365509033}]}, "Id": "673b6de5-7a18-4b57-a4ab-d3ab3efda57f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "FUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017914364114403725, "Height": 0.03992602601647377, "Left": 0.11857372522354126, "Top": 0.29513370990753174}, "Polygon": [{"X": 0.13648808002471924, "Y": 0.2951483428478241}, {"X": 0.1360592395067215, "Y": 0.3350059390068054}, {"X": 0.11857372522354126, "Y": 0.3350597321987152}, {"X": 0.11905194818973541, "Y": 0.29513370990753174}]}, "Id": "18173e33-36a4-446d-a401-0ef8dd8df53d"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "PRICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018412280827760696, "Height": 0.051641516387462616, "Left": 0.1162380501627922, "Top": 0.3462314307689667}, "Polygon": [{"X": 0.1346503347158432, "Y": 0.3462314307689667}, {"X": 0.13409185409545898, "Y": 0.39770808815956116}, {"X": 0.1162380501627922, "Y": 0.3978729248046875}, {"X": 0.11686136573553085, "Y": 0.3463057577610016}]}, "Id": "1935a737-71ef-435d-8897-5457f880f61e"}, {"BlockType": "WORD", "Confidence": 22.408920288085938, "Text": "-------------------------", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007119614165276289, "Height": 0.032468531280756, "Left": 0.12257352471351624, "Top": 0.42095109820365906}, "Polygon": [{"X": 0.1296931356191635, "Y": 0.42095109820365906}, {"X": 0.12933266162872314, "Y": 0.45332032442092896}, {"X": 0.12257352471351624, "Y": 0.45341962575912476}, {"X": 0.12294936925172806, "Y": 0.4210287034511566}]}, "Id": "6a805bf6-e79d-4729-9fe7-f56fc44ca0a9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01851133443415165, "Height": 0.05464832857251167, "Left": 0.11899649351835251, "Top": 0.47584256529808044}, "Polygon": [{"X": 0.137507826089859, "Y": 0.47584256529808044}, {"X": 0.13693523406982422, "Y": 0.5300918817520142}, {"X": 0.11899649351835251, "Y": 0.5304908752441406}, {"X": 0.11963711678981781, "Y": 0.47614482045173645}]}, "Id": "e8613788-34df-4f31-808c-2a24e7df732d"}, {"BlockType": "WORD", "Confidence": 99.44435119628906, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018636219203472137, "Height": 0.04052497819066048, "Left": 0.11958490312099457, "Top": 0.5420970916748047}, "Polygon": [{"X": 0.1382211297750473, "Y": 0.5420970916748047}, {"X": 0.13780252635478973, "Y": 0.5821236968040466}, {"X": 0.11958490312099457, "Y": 0.5826220512390137}, {"X": 0.12005428224802017, "Y": 0.5425226092338562}]}, "Id": "95373ce1-5ad9-499b-9648-1290433cab17"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "e5e77fdb-216e-476c-91d6-4a585077106f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "e5e77fdb-216e-476c-91d6-4a585077106f", "content-type": "application/x-amz-json-1.1", "content-length": "86246", "date": "Wed, 24 Sep 2025 12:47:13 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "7cf4f948-27b6-4e4c-b354-9be9d9afb8e2", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:16 GMT", "content-type": "application/json", "content-length": "1142", "connection": "keep-alive", "x-amzn-requestid": "7cf4f948-27b6-4e4c-b354-9be9d9afb8e2"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page of a fuel receipt or a fuel purchase receipt. Contains \"Fuel\", \"Diesel\", \"Pump\", \"Truck Number\", \"Card Number\", \"Billing Company: NBS / KWIK TRIP FLEET\", \"Invoice #\", \"Total\", etc. Keywords: Fuel, Diesel, Pump, Card Number, etc. This matches fuel_receipt.\n\nCheck if any other doc type possible: It could be a fuel receipt. It's not an invoice, because it's a fuel receipt. There is no \"fuel receipt\" keyword but we have \"Fuel\", \"Diesel\", \"Pump\". The definition says Fuel Receipt includes Fuel, Diesel, Pump, Gallons, Ltrs, Fuel Receipt. So fuel_receipt.\n\nThus output single object for page1: {page_no: 1, doc_type: \"fuel_receipt\"}.\n\nWe need to use the tool classify_logistics_doc_type. Provide array of documents, with each object having page_no and doc_type.\n\nThus call."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2809, "outputTokens": 222, "totalTokens": 3031}, "metrics": {"latencyMs": 1100}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/d7ecf83e_F6NB931XUOJY2MJRN8VT.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n******* (DUPLICATE RECEIPT) *******\nTRIP 679 /4220 COUNTY ROAD J /SHEBOYGAN, 9 WI 53083/(920) 803-2078\nNumber: KWIK 0900-9991 01/31/2025 Station:\nResponse: APPROVED AUTH REF#: P58049\nLOYALTY Response: APPROVED, Total Kwik Rewards Fleet Points: $19.83 Shower credi\nt earned! Expires in 14 days\nProducts\nReefer\nQuantity\nUnit Cost\nTotal\nPump\nDiesel Prem\nN\n102.493\n3.339\n342.22\n# 2\nDEF Bulk\nN\n13.390\n3.599\n48.19\n# 2\nsubtotal =\n390.41\nsales tax =\n0.00\nTOTAL =\n390.41 Signature:\nI agree to pay total amount\naccording to card issuer agreement.\nSalesperson ID: AUTO/SMARTFUEL\nInvoice #: 0099991\nTruck Number: 3482\nMerchant Number: *******6793\nCard Number:\nxxxxxxxxxxxxxx1773 -ICR-\nLoyalty Card#: xxxxxxxxxxxx8997 -ICR-\nBilling Company: NBS / KWIK TRIP FLEET\nALL TAXES INCLUDED IN FUEL PRICE ------------------------- THANK YOU!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}