2025-09-24 18:12:22,439 - INFO - Logging initialized. Log file: logs/test_classification_20250924_181222.log
2025-09-24 18:12:22,439 - INFO - 📁 Found 9 files to process
2025-09-24 18:12:22,440 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:12:22,440 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 18:12:22,440 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 18:12:22,440 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 18:12:22,440 - INFO - ⬆️ [18:12:22] Uploading: BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:24,609 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/BVLD9CCIUN6D2HGVETKO.pdf -> s3://document-extraction-logistically/temp/202529a4_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:24,609 - INFO - 🔍 [18:12:24] Starting classification: BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:24,610 - INFO - ⬆️ [18:12:24] Uploading: ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:24,611 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:24,631 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:24,637 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/202529a4_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:24,637 - INFO - Processing PDF from S3...
2025-09-24 18:12:24,637 - INFO - Downloading PDF from S3 to /tmp/tmpw334923x/202529a4_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:26,473 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:12:26,474 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:26,476 - INFO - Splitting PDF 202529a4_BVLD9CCIUN6D2HGVETKO into 1 pages
2025-09-24 18:12:26,479 - INFO - Split PDF into 1 pages
2025-09-24 18:12:26,479 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:26,479 - INFO - Expected pages: [1]
2025-09-24 18:12:29,053 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ILOJODH5ADT10MHGK0TI.pdf -> s3://document-extraction-logistically/temp/996fd583_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:29,054 - INFO - 🔍 [18:12:29] Starting classification: ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:29,054 - INFO - ⬆️ [18:12:29] Uploading: NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:29,056 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:29,070 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:29,074 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/996fd583_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:29,074 - INFO - Processing PDF from S3...
2025-09-24 18:12:29,075 - INFO - Downloading PDF from S3 to /tmp/tmpxbcx8tz3/996fd583_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:31,103 - INFO - Page 1: Extracted 545 characters, 28 lines from 202529a4_BVLD9CCIUN6D2HGVETKO_1a74d44e_page_001.pdf
2025-09-24 18:12:31,104 - INFO - Successfully processed page 1
2025-09-24 18:12:31,104 - INFO - Combined 1 pages into final text
2025-09-24 18:12:31,104 - INFO - Text validation for 202529a4_BVLD9CCIUN6D2HGVETKO: 562 characters, 1 pages
2025-09-24 18:12:31,104 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:31,104 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:31,435 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf -> s3://document-extraction-logistically/temp/15c0c8d3_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:31,435 - INFO - 🔍 [18:12:31] Starting classification: NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:31,436 - INFO - ⬆️ [18:12:31] Uploading: NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:31,436 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:31,453 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:31,458 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15c0c8d3_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:31,459 - INFO - Processing PDF from S3...
2025-09-24 18:12:31,459 - INFO - Downloading PDF from S3 to /tmp/tmpb52qln1j/15c0c8d3_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:31,624 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 18:12:31,624 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:31,625 - WARNING - /Prev=0 in the trailer - assuming there is no previous xref table
2025-09-24 18:12:31,625 - INFO - Splitting PDF 996fd583_ILOJODH5ADT10MHGK0TI into 1 pages
2025-09-24 18:12:31,628 - INFO - Split PDF into 1 pages
2025-09-24 18:12:31,628 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:31,628 - INFO - Expected pages: [1]
2025-09-24 18:12:32,215 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMWG7N543A7USM9VG3WI.pdf -> s3://document-extraction-logistically/temp/05a905f7_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:32,215 - INFO - 🔍 [18:12:32] Starting classification: NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:32,216 - INFO - ⬆️ [18:12:32] Uploading: QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:32,217 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:32,241 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:32,246 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/05a905f7_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:32,246 - INFO - Processing PDF from S3...
2025-09-24 18:12:32,246 - INFO - Downloading PDF from S3 to /tmp/tmp4qtvuzrp/05a905f7_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:33,231 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '276c21c3-2422-4d4b-9d5d-b99233a2380e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:33 GMT', 'content-type': 'application/json', 'content-length': '983', 'connection': 'keep-alive', 'x-amzn-requestid': '276c21c3-2422-4d4b-9d5d-b99233a2380e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s just one page. It\'s an ACH Payment Notification. Keywords: "ACH PAYMENT NOTIFICATION", "Approved ACH", "Payment Amount". This matches clear_to_pay definition: "Clear to Pay" – Approval for payment or ACH/Wire/Check transfer request or approval. So doc_type should be "clear_to_pay". There\'s no continuation. So we return JSON with array of objects: one page.\n\nWe must use the tool classify_logistics_doc_type. The tool expects an object with documents: an array of objects each with page_no and doc_type. So we call the function with documents: [{page_no:1, doc_type:"clear_to_pay"}].\n\nLet\'s produce that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2695, 'outputTokens': 163, 'totalTokens': 2858}, 'metrics': {'latencyMs': 913}}
2025-09-24 18:12:33,231 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/202529a4_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:33,791 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/QW3BG1TZMWHOTTZKGA8Y.pdf -> s3://document-extraction-logistically/temp/364b9095_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:33,792 - INFO - 🔍 [18:12:33] Starting classification: QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:33,792 - INFO - ⬆️ [18:12:33] Uploading: ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:33,793 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:33,808 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:33,813 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/364b9095_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:33,814 - INFO - Processing PDF from S3...
2025-09-24 18:12:33,814 - INFO - Downloading PDF from S3 to /tmp/tmpfr17c8kh/364b9095_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:33,850 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 18:12:33,850 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:33,851 - INFO - Splitting PDF 15c0c8d3_NMAGOA3H1CROTNXQ4GR8 into 7 pages
2025-09-24 18:12:33,880 - INFO - Split PDF into 7 pages
2025-09-24 18:12:33,880 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:33,880 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 18:12:33,955 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:12:33,956 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:33,957 - INFO - Splitting PDF 05a905f7_NMWG7N543A7USM9VG3WI into 1 pages
2025-09-24 18:12:33,959 - INFO - Split PDF into 1 pages
2025-09-24 18:12:33,960 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:33,960 - INFO - Expected pages: [1]
2025-09-24 18:12:34,534 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ULZ4VHKA9IH82VWCSF3C.pdf -> s3://document-extraction-logistically/temp/10478366_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:34,534 - INFO - 🔍 [18:12:34] Starting classification: ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:34,535 - INFO - ⬆️ [18:12:34] Uploading: W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:34,537 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:34,560 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:34,564 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/10478366_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:34,565 - INFO - Processing PDF from S3...
2025-09-24 18:12:34,565 - INFO - Downloading PDF from S3 to /tmp/tmp2_h86i4v/10478366_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:35,221 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ (1).pdf -> s3://document-extraction-logistically/temp/4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:35,221 - INFO - 🔍 [18:12:35] Starting classification: W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:35,222 - INFO - ⬆️ [18:12:35] Uploading: W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:35,224 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:35,250 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:35,253 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:35,254 - INFO - Processing PDF from S3...
2025-09-24 18:12:35,254 - INFO - Downloading PDF from S3 to /tmp/tmp8c_4_xhs/4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:35,934 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ.pdf -> s3://document-extraction-logistically/temp/4b4d6912_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:35,935 - INFO - 🔍 [18:12:35] Starting classification: W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:35,935 - INFO - ⬆️ [18:12:35] Uploading: Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:35,937 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:35,949 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:35,951 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4b4d6912_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:35,951 - INFO - Processing PDF from S3...
2025-09-24 18:12:35,951 - INFO - Downloading PDF from S3 to /tmp/tmp_j95z6_r/4b4d6912_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:36,181 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 18:12:36,182 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:36,184 - INFO - Splitting PDF 364b9095_QW3BG1TZMWHOTTZKGA8Y into 2 pages
2025-09-24 18:12:36,208 - INFO - Split PDF into 2 pages
2025-09-24 18:12:36,208 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:36,208 - INFO - Expected pages: [1, 2]
2025-09-24 18:12:36,355 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:12:36,355 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:36,356 - INFO - Splitting PDF 10478366_ULZ4VHKA9IH82VWCSF3C into 1 pages
2025-09-24 18:12:36,359 - INFO - Split PDF into 1 pages
2025-09-24 18:12:36,359 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:36,359 - INFO - Expected pages: [1]
2025-09-24 18:12:36,638 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/Z7I9W27YVP7F6SPWV8UK.pdf -> s3://document-extraction-logistically/temp/ab820a23_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:36,639 - INFO - 🔍 [18:12:36] Starting classification: Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:36,640 - INFO - Initializing TextractProcessor...
2025-09-24 18:12:36,656 - INFO - Initializing BedrockProcessor...
2025-09-24 18:12:36,659 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ab820a23_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:36,660 - INFO - Processing PDF from S3...
2025-09-24 18:12:36,661 - INFO - Downloading PDF from S3 to /tmp/tmpijmlj9il/ab820a23_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:36,664 - INFO - 

BVLD9CCIUN6D2HGVETKO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:36,664 - INFO - 

✓ Saved result: output/run1_BVLD9CCIUN6D2HGVETKO.json
2025-09-24 18:12:36,955 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/202529a4_BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:36,974 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:12:36,975 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:36,976 - INFO - Splitting PDF 4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1) into 1 pages
2025-09-24 18:12:36,977 - INFO - Split PDF into 1 pages
2025-09-24 18:12:36,977 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:36,977 - INFO - Expected pages: [1]
2025-09-24 18:12:37,787 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:12:37,788 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:37,789 - INFO - Splitting PDF 4b4d6912_W7K89V5JE3EN9H4SOSTZ into 1 pages
2025-09-24 18:12:37,792 - INFO - Split PDF into 1 pages
2025-09-24 18:12:37,792 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:37,792 - INFO - Expected pages: [1]
2025-09-24 18:12:38,197 - INFO - Page 1: Extracted 1392 characters, 59 lines from 996fd583_ILOJODH5ADT10MHGK0TI_8e306c3a_page_001.pdf
2025-09-24 18:12:38,197 - INFO - Successfully processed page 1
2025-09-24 18:12:38,197 - INFO - Combined 1 pages into final text
2025-09-24 18:12:38,198 - INFO - Text validation for 996fd583_ILOJODH5ADT10MHGK0TI: 1409 characters, 1 pages
2025-09-24 18:12:38,198 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:38,198 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:38,618 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:12:38,619 - INFO - Splitting PDF into individual pages...
2025-09-24 18:12:38,620 - INFO - Splitting PDF ab820a23_Z7I9W27YVP7F6SPWV8UK into 1 pages
2025-09-24 18:12:38,621 - INFO - Split PDF into 1 pages
2025-09-24 18:12:38,621 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:12:38,621 - INFO - Expected pages: [1]
2025-09-24 18:12:38,772 - INFO - Page 1: Extracted 1186 characters, 37 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_001.pdf
2025-09-24 18:12:38,772 - INFO - Successfully processed page 1
2025-09-24 18:12:39,278 - INFO - Page 1: Extracted 761 characters, 42 lines from 05a905f7_NMWG7N543A7USM9VG3WI_d7d8a3cb_page_001.pdf
2025-09-24 18:12:39,278 - INFO - Successfully processed page 1
2025-09-24 18:12:39,278 - INFO - Combined 1 pages into final text
2025-09-24 18:12:39,278 - INFO - Text validation for 05a905f7_NMWG7N543A7USM9VG3WI: 778 characters, 1 pages
2025-09-24 18:12:39,279 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:39,279 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:39,321 - INFO - Page 2: Extracted 1897 characters, 42 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_002.pdf
2025-09-24 18:12:39,322 - INFO - Successfully processed page 2
2025-09-24 18:12:39,477 - INFO - Page 6: Extracted 1677 characters, 38 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_006.pdf
2025-09-24 18:12:39,477 - INFO - Successfully processed page 6
2025-09-24 18:12:39,525 - INFO - Page 4: Extracted 1535 characters, 35 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_004.pdf
2025-09-24 18:12:39,526 - INFO - Successfully processed page 4
2025-09-24 18:12:39,758 - INFO - Page 5: Extracted 1454 characters, 40 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_005.pdf
2025-09-24 18:12:39,759 - INFO - Successfully processed page 5
2025-09-24 18:12:40,206 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c6045b34-1266-49cf-980f-5e6554cac265', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:40 GMT', 'content-type': 'application/json', 'content-length': '918', 'connection': 'keep-alive', 'x-amzn-requestid': 'c6045b34-1266-49cf-980f-5e6554cac265'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 provided. Determine doc_type. The page is an outgoing wire transfer request, likely a payment authorization. Not among defined categories. The categories include clear_to_pay which is for approval for payment or ACH/Wire/Wire transfer request or approval. So this is a wire transfer request. That matches clear_to_pay as definition: Approval for payment or ACH/Wire/Check transfer request or approval. So doc_type likely clear_to_pay. We should return an array with one object: {page_no:1, doc_type:"clear_to_pay"}. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2884, 'outputTokens': 146, 'totalTokens': 3030}, 'metrics': {'latencyMs': 814}}
2025-09-24 18:12:40,207 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/996fd583_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:40,236 - INFO - 

ILOJODH5ADT10MHGK0TI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:40,236 - INFO - 

✓ Saved result: output/run1_ILOJODH5ADT10MHGK0TI.json
2025-09-24 18:12:40,415 - INFO - Page 3: Extracted 1672 characters, 51 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_003.pdf
2025-09-24 18:12:40,415 - INFO - Successfully processed page 3
2025-09-24 18:12:40,542 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/996fd583_ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:41,445 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd6f76039-c530-457f-8983-29b60168e86c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:41 GMT', 'content-type': 'application/json', 'content-length': '996', 'connection': 'keep-alive', 'x-amzn-requestid': 'd6f76039-c530-457f-8983-29b60168e86c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The page shows "Truckload Check-in Form" and "Clear-to-Pay Voucher". It contains Load #, broker, date processed, load cost, rack returns, deductions, net cost, manager approval. The keywords: "Clear-to-Pay Voucher". So doc type likely clear_to_pay. Also has check-in form, but it\'s more a voucher for payment. The categories: clear_to_pay defined as approval for payment or ACH/Wire/Check transfer request or approval. This page appears to be a clearance to pay. So doc_type = clear_to_pay. No continuation. So we output array with one object: page_no 1, doc_type "clear_to_pay". Need to use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2781, 'outputTokens': 176, 'totalTokens': 2957}, 'metrics': {'latencyMs': 1035}}
2025-09-24 18:12:41,445 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/05a905f7_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:41,475 - INFO - 

NMWG7N543A7USM9VG3WI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:41,475 - INFO - 

✓ Saved result: output/run1_NMWG7N543A7USM9VG3WI.json
2025-09-24 18:12:41,781 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/05a905f7_NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:42,094 - INFO - Page 1: Extracted 1700 characters, 39 lines from 364b9095_QW3BG1TZMWHOTTZKGA8Y_3e9cc2c7_page_001.pdf
2025-09-24 18:12:42,094 - INFO - Successfully processed page 1
2025-09-24 18:12:42,120 - INFO - Page 1: Extracted 732 characters, 34 lines from 4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1)_5acb01c8_page_001.pdf
2025-09-24 18:12:42,120 - INFO - Successfully processed page 1
2025-09-24 18:12:42,121 - INFO - Combined 1 pages into final text
2025-09-24 18:12:42,121 - INFO - Text validation for 4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1): 749 characters, 1 pages
2025-09-24 18:12:42,121 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:42,121 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:42,306 - INFO - Page 7: Extracted 500 characters, 15 lines from 15c0c8d3_NMAGOA3H1CROTNXQ4GR8_9811ffc1_page_007.pdf
2025-09-24 18:12:42,306 - INFO - Successfully processed page 7
2025-09-24 18:12:42,307 - INFO - Combined 7 pages into final text
2025-09-24 18:12:42,307 - INFO - Text validation for 15c0c8d3_NMAGOA3H1CROTNXQ4GR8: 10052 characters, 7 pages
2025-09-24 18:12:42,308 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:42,308 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:42,897 - INFO - Page 2: Extracted 335 characters, 15 lines from 364b9095_QW3BG1TZMWHOTTZKGA8Y_3e9cc2c7_page_002.pdf
2025-09-24 18:12:42,898 - INFO - Successfully processed page 2
2025-09-24 18:12:42,899 - INFO - Combined 2 pages into final text
2025-09-24 18:12:42,899 - INFO - Text validation for 364b9095_QW3BG1TZMWHOTTZKGA8Y: 2071 characters, 2 pages
2025-09-24 18:12:42,900 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:42,901 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:43,360 - INFO - Page 1: Extracted 736 characters, 39 lines from ab820a23_Z7I9W27YVP7F6SPWV8UK_70733a0f_page_001.pdf
2025-09-24 18:12:43,360 - INFO - Successfully processed page 1
2025-09-24 18:12:43,361 - INFO - Combined 1 pages into final text
2025-09-24 18:12:43,361 - INFO - Text validation for ab820a23_Z7I9W27YVP7F6SPWV8UK: 753 characters, 1 pages
2025-09-24 18:12:43,361 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:43,361 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:43,734 - INFO - Page 1: Extracted 732 characters, 34 lines from 4b4d6912_W7K89V5JE3EN9H4SOSTZ_ebd77b32_page_001.pdf
2025-09-24 18:12:43,735 - INFO - Successfully processed page 1
2025-09-24 18:12:43,735 - INFO - Combined 1 pages into final text
2025-09-24 18:12:43,735 - INFO - Text validation for 4b4d6912_W7K89V5JE3EN9H4SOSTZ: 749 characters, 1 pages
2025-09-24 18:12:43,735 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:43,735 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:44,355 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a14543b6-9008-406b-90b8-a93379834850', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:44 GMT', 'content-type': 'application/json', 'content-length': '914', 'connection': 'keep-alive', 'x-amzn-requestid': 'a14543b6-9008-406b-90b8-a93379834850'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. The content: "Truckload Check-in Form Clear-to-Pay Voucher" etc. There is "Clear-to-Pay Voucher". So doc type likely clear_to_pay. The page says "Clear-to-Pay Voucher". It\'s a check-in form but includes "Clear-to-Pay Voucher". So we classify as clear_to_pay. According to definition: Clear to Pay: Approval for payment or ACH/Wire/Check transfer request or approval. Document contains "Clear to Pay" header. So yes. Output JSON with documents array containing one object: page_no 1, doc_type "clear_to_pay". Use tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2772, 'outputTokens': 156, 'totalTokens': 2928}, 'metrics': {'latencyMs': 945}}
2025-09-24 18:12:44,356 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:44,380 - INFO - 

W7K89V5JE3EN9H4SOSTZ (1).pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:44,380 - INFO - 

✓ Saved result: output/run1_W7K89V5JE3EN9H4SOSTZ (1).json
2025-09-24 18:12:44,672 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4c9a3ac3_W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:44,809 - INFO - Page 1: Extracted 1065 characters, 60 lines from 10478366_ULZ4VHKA9IH82VWCSF3C_8f8075ce_page_001.pdf
2025-09-24 18:12:44,810 - INFO - Successfully processed page 1
2025-09-24 18:12:44,810 - INFO - Combined 1 pages into final text
2025-09-24 18:12:44,810 - INFO - Text validation for 10478366_ULZ4VHKA9IH82VWCSF3C: 1082 characters, 1 pages
2025-09-24 18:12:44,810 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:12:44,810 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:12:45,232 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c3f5919c-b9fa-48be-9d23-c4850d73a743', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:45 GMT', 'content-type': 'application/json', 'content-length': '654', 'connection': 'keep-alive', 'x-amzn-requestid': 'c3f5919c-b9fa-48be-9d23-c4850d73a743'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The content includes "Truckload Check-in Form" and "Clear-to-Pay Voucher". The page has "Clear-to-Pay Voucher" header. That suggests the type: clear_to_pay. The content includes load cost, deductions, manager approval. So doc_type clear_to_pay. page_no 1.\n\nReturn list with one object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2776, 'outputTokens': 97, 'totalTokens': 2873}, 'metrics': {'latencyMs': 614}}
2025-09-24 18:12:45,232 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ab820a23_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:45,255 - INFO - 

Z7I9W27YVP7F6SPWV8UK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:45,255 - INFO - 

✓ Saved result: output/run1_Z7I9W27YVP7F6SPWV8UK.json
2025-09-24 18:12:45,560 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ab820a23_Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:45,779 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ecce96ef-0b2f-44db-99cc-51381bd9a934', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:45 GMT', 'content-type': 'application/json', 'content-length': '916', 'connection': 'keep-alive', 'x-amzn-requestid': 'ecce96ef-0b2f-44db-99cc-51381bd9a934'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. We need to classify. The content: "Truckload Check-in Form Clear-to-Pay Voucher". It says "Clear-to-Pay Voucher". The keywords for clear_to_pay: "Clear to Pay", "Approved for Payment", "Payment Authorization", "Clear to Pay Stamp". This page has "Clear-to-Pay Voucher" header. So it\'s likely clear_to_pay. But also might be a "Truckload Check-in Form" - but that\'s not a category. The presence of "Clear-to-Pay Voucher" strongly indicates clear_to_pay.\n\nThus output: page_no:1 doc_type: "clear_to_pay".\n\nReturn via tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2772, 'outputTokens': 159, 'totalTokens': 2931}, 'metrics': {'latencyMs': 822}}
2025-09-24 18:12:45,780 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4b4d6912_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:45,799 - INFO - 

W7K89V5JE3EN9H4SOSTZ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:45,799 - INFO - 

✓ Saved result: output/run1_W7K89V5JE3EN9H4SOSTZ.json
2025-09-24 18:12:45,932 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '348f1706-6b90-4eae-8ea5-a12fb8e80aee', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:45 GMT', 'content-type': 'application/json', 'content-length': '1875', 'connection': 'keep-alive', 'x-amzn-requestid': '348f1706-6b90-4eae-8ea5-a12fb8e80aee'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be an email chain about a freight driver charging extra. It seems like a communication, not any of the listed categories. Let\'s see the categories: invoice, comm_invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, other, tender_from_cust, so_confirmation, ingate, outgate. Is this an email about a load tender? The subject: "R6U4G9 Kankakee, IL to Indianapolis, IN (Requesting Approval)". It refers to a driver charging extra due to wrong address. It\'s an email. Does it indicate a tender? There\'s mention of "wrong jobsite location on the load tender." But the email is not a load tender document itself. It\'s a request for approval. This likely falls under "other" because it\'s an email. There\'s no invoice, no bol, no pod. So classify as "other". But check if any continuation? Page1 ends with "1/2". Page2 continues "2/2". So page2 is a continuation of page1. According to instruction: "If document is not from any of catagories mentioned, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign the same doc_type as previous page." So page1 is "other". Page2 is continuation page, so also "other". So output array with two objects. Ensure each page object. Use tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3244, 'outputTokens': 377, 'totalTokens': 3621}, 'metrics': {'latencyMs': 1863}}
2025-09-24 18:12:45,932 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/364b9095_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:46,053 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '10d71602-12b1-487e-b4c6-4124cdabebc0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:45 GMT', 'content-type': 'application/json', 'content-length': '2538', 'connection': 'keep-alive', 'x-amzn-requestid': '10d71602-12b1-487e-b4c6-4124cdabebc0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 7 pages. They are all emails, not invoices. They mention invoices but not actual invoice content. They seem to be email threads about invoicing. We need to classify each page. According to the instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." But these are separate email pages. All pages appear to be part of the same email thread? Actually it\'s a single email thread about an invoice. But each page is part of the same email thread. The pages contain email headers and body. The doc_type is likely "invoice" or "other"? The content is not an invoice. It\'s email. There is no invoice. There\'s no header "Invoice". There\'s mention of invoice attachments but not the actual content. So probably "other". But maybe "invoice" because attachments included images of invoice? But we don\'t have the images text. The pages show only emails. The classification should be "other" because it\'s email. According to instructions, if a page is part of a multi-page single document: each page still gets the same doc_type. But here it\'s an email thread, but we treat each page as part of the same email document. So we assign same doc_type to all pages. Which type? "other". But need to check if any page indicates continuation. Page 1 ends with "1" page number? It has "1" at the bottom. Page 2 has "2". It appears each page has a page number. They are separate pages of a PDF maybe captured from an email. They are part of the same document. So doc_type = "other".\n\nBut we need to ensure no other type matches. There is no "invoice" content. There\'s no "Proof of Delivery" or "Bill of Lading" content. The only mention of "bill of lading" appears in page 5, but that\'s just a mention. But no actual BOL content. So "other".\n\nThus output: a list of objects: page_no: 1, doc_type: "other", etc. For 7 pages. That is the required. Let\'s produce the JSON via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"},{"page_no":4,"doc_type":"other"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 5619, 'outputTokens': 527, 'totalTokens': 6146}, 'metrics': {'latencyMs': 2499}}
2025-09-24 18:12:46,053 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15c0c8d3_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:46,141 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4b4d6912_W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:46,165 - INFO - 

QW3BG1TZMWHOTTZKGA8Y.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:12:46,165 - INFO - 

✓ Saved result: output/run1_QW3BG1TZMWHOTTZKGA8Y.json
2025-09-24 18:12:46,467 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/364b9095_QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:46,532 - INFO - 

NMAGOA3H1CROTNXQ4GR8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        },
        {
            "page_no": 4,
            "doc_type": "other"
        },
        {
            "page_no": 5,
            "doc_type": "other"
        },
        {
            "page_no": 6,
            "doc_type": "other"
        },
        {
            "page_no": 7,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:12:46,533 - INFO - 

✓ Saved result: output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:46,870 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15c0c8d3_NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,110 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1634f203-d09c-4178-9ed1-a75f09eb7155', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:42:46 GMT', 'content-type': 'application/json', 'content-length': '1189', 'connection': 'keep-alive', 'x-amzn-requestid': '1634f203-d09c-4178-9ed1-a75f09eb7155'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content seems to be an approval confirmation for PayCargo Invoice 08AIR44777. It\'s basically a payment approval, not an invoice or a receipt. It\'s more like a payment confirmation. It includes "Type Invoice" but it\'s a "Approval Confirmation for PayCargo Invoice 08AIR44777". It might be a "clear to pay" or "approval confirmation". The enum includes "clear_to_pay" for approval for payment or ACH/Wire/Check transfer request or approval. That seems relevant. It\'s a confirmation of payment: "Approval Confirmation". So likely "clear_to_pay". Not an invoice, because it\'s not a carrier invoice; it\'s a payment confirmation. There\'s no invoice details. So classify as clear_to_pay.\n\nWe must output using tool: classify_logistics_doc_type with documents array: page_no 1, doc_type "clear_to_pay".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2855, 'outputTokens': 204, 'totalTokens': 3059}, 'metrics': {'latencyMs': 1029}}
2025-09-24 18:12:47,110 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/10478366_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:47,138 - INFO - 

ULZ4VHKA9IH82VWCSF3C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 18:12:47,138 - INFO - 

✓ Saved result: output/run1_ULZ4VHKA9IH82VWCSF3C.json
2025-09-24 18:12:47,450 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/10478366_ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:47,451 - INFO - 
📊 Processing Summary:
2025-09-24 18:12:47,451 - INFO -    Total files: 9
2025-09-24 18:12:47,451 - INFO -    Successful: 9
2025-09-24 18:12:47,451 - INFO -    Failed: 0
2025-09-24 18:12:47,451 - INFO -    Duration: 25.01 seconds
2025-09-24 18:12:47,452 - INFO -    Output directory: output
2025-09-24 18:12:47,452 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:12:47,452 - INFO -    📄 BVLD9CCIUN6D2HGVETKO.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,452 - INFO -    📄 ILOJODH5ADT10MHGK0TI.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,452 - INFO -    📄 NMAGOA3H1CROTNXQ4GR8.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"other"},{"page_no":4,"doc_type":"other"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"}]}
2025-09-24 18:12:47,452 - INFO -    📄 NMWG7N543A7USM9VG3WI.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,452 - INFO -    📄 QW3BG1TZMWHOTTZKGA8Y.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 18:12:47,452 - INFO -    📄 ULZ4VHKA9IH82VWCSF3C.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,452 - INFO -    📄 W7K89V5JE3EN9H4SOSTZ (1).pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,452 - INFO -    📄 W7K89V5JE3EN9H4SOSTZ.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,452 - INFO -    📄 Z7I9W27YVP7F6SPWV8UK.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 18:12:47,453 - INFO - 
============================================================================================================================================
2025-09-24 18:12:47,453 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:12:47,453 - INFO - ============================================================================================================================================
2025-09-24 18:12:47,453 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:12:47,453 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:12:47,453 - INFO - BVLD9CCIUN6D2HGVETKO.pdf                           1      clear_to_pay                             run1_BVLD9CCIUN6D2HGVETKO.json                                                  
2025-09-24 18:12:47,453 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/BVLD9CCIUN6D2HGVETKO.pdf
2025-09-24 18:12:47,453 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BVLD9CCIUN6D2HGVETKO.json
2025-09-24 18:12:47,454 - INFO - 
2025-09-24 18:12:47,454 - INFO - ILOJODH5ADT10MHGK0TI.pdf                           1      clear_to_pay                             run1_ILOJODH5ADT10MHGK0TI.json                                                  
2025-09-24 18:12:47,454 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ILOJODH5ADT10MHGK0TI.pdf
2025-09-24 18:12:47,454 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ILOJODH5ADT10MHGK0TI.json
2025-09-24 18:12:47,454 - INFO - 
2025-09-24 18:12:47,454 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           1      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,454 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,454 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,454 - INFO - 
2025-09-24 18:12:47,454 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           2      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,454 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,454 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,454 - INFO - 
2025-09-24 18:12:47,454 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           3      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,454 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,454 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,454 - INFO - 
2025-09-24 18:12:47,455 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           4      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           5      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           6      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - NMAGOA3H1CROTNXQ4GR8.pdf                           7      other                                    run1_NMAGOA3H1CROTNXQ4GR8.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - NMWG7N543A7USM9VG3WI.pdf                           1      clear_to_pay                             run1_NMWG7N543A7USM9VG3WI.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMWG7N543A7USM9VG3WI.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMWG7N543A7USM9VG3WI.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - QW3BG1TZMWHOTTZKGA8Y.pdf                           1      other                                    run1_QW3BG1TZMWHOTTZKGA8Y.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QW3BG1TZMWHOTTZKGA8Y.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - QW3BG1TZMWHOTTZKGA8Y.pdf                           2      other                                    run1_QW3BG1TZMWHOTTZKGA8Y.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/QW3BG1TZMWHOTTZKGA8Y.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QW3BG1TZMWHOTTZKGA8Y.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - ULZ4VHKA9IH82VWCSF3C.pdf                           1      clear_to_pay                             run1_ULZ4VHKA9IH82VWCSF3C.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ULZ4VHKA9IH82VWCSF3C.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ULZ4VHKA9IH82VWCSF3C.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - W7K89V5JE3EN9H4SOSTZ (1).pdf                       1      clear_to_pay                             run1_W7K89V5JE3EN9H4SOSTZ (1).json                                              
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ (1).pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W7K89V5JE3EN9H4SOSTZ (1).json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - W7K89V5JE3EN9H4SOSTZ.pdf                           1      clear_to_pay                             run1_W7K89V5JE3EN9H4SOSTZ.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W7K89V5JE3EN9H4SOSTZ.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - Z7I9W27YVP7F6SPWV8UK.pdf                           1      clear_to_pay                             run1_Z7I9W27YVP7F6SPWV8UK.json                                                  
2025-09-24 18:12:47,455 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/Z7I9W27YVP7F6SPWV8UK.pdf
2025-09-24 18:12:47,455 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z7I9W27YVP7F6SPWV8UK.json
2025-09-24 18:12:47,455 - INFO - 
2025-09-24 18:12:47,455 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:12:47,456 - INFO - Total entries: 16
2025-09-24 18:12:47,456 - INFO - ============================================================================================================================================
2025-09-24 18:12:47,456 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:12:47,456 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:12:47,456 - INFO -   1. BVLD9CCIUN6D2HGVETKO.pdf            Page 1   → clear_to_pay    | run1_BVLD9CCIUN6D2HGVETKO.json
2025-09-24 18:12:47,456 - INFO -   2. ILOJODH5ADT10MHGK0TI.pdf            Page 1   → clear_to_pay    | run1_ILOJODH5ADT10MHGK0TI.json
2025-09-24 18:12:47,456 - INFO -   3. NMAGOA3H1CROTNXQ4GR8.pdf            Page 1   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -   4. NMAGOA3H1CROTNXQ4GR8.pdf            Page 2   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -   5. NMAGOA3H1CROTNXQ4GR8.pdf            Page 3   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -   6. NMAGOA3H1CROTNXQ4GR8.pdf            Page 4   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -   7. NMAGOA3H1CROTNXQ4GR8.pdf            Page 5   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -   8. NMAGOA3H1CROTNXQ4GR8.pdf            Page 6   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -   9. NMAGOA3H1CROTNXQ4GR8.pdf            Page 7   → other           | run1_NMAGOA3H1CROTNXQ4GR8.json
2025-09-24 18:12:47,456 - INFO -  10. NMWG7N543A7USM9VG3WI.pdf            Page 1   → clear_to_pay    | run1_NMWG7N543A7USM9VG3WI.json
2025-09-24 18:12:47,456 - INFO -  11. QW3BG1TZMWHOTTZKGA8Y.pdf            Page 1   → other           | run1_QW3BG1TZMWHOTTZKGA8Y.json
2025-09-24 18:12:47,456 - INFO -  12. QW3BG1TZMWHOTTZKGA8Y.pdf            Page 2   → other           | run1_QW3BG1TZMWHOTTZKGA8Y.json
2025-09-24 18:12:47,456 - INFO -  13. ULZ4VHKA9IH82VWCSF3C.pdf            Page 1   → clear_to_pay    | run1_ULZ4VHKA9IH82VWCSF3C.json
2025-09-24 18:12:47,456 - INFO -  14. W7K89V5JE3EN9H4SOSTZ (1).pdf        Page 1   → clear_to_pay    | run1_W7K89V5JE3EN9H4SOSTZ (1).json
2025-09-24 18:12:47,456 - INFO -  15. W7K89V5JE3EN9H4SOSTZ.pdf            Page 1   → clear_to_pay    | run1_W7K89V5JE3EN9H4SOSTZ.json
2025-09-24 18:12:47,456 - INFO -  16. Z7I9W27YVP7F6SPWV8UK.pdf            Page 1   → clear_to_pay    | run1_Z7I9W27YVP7F6SPWV8UK.json
2025-09-24 18:12:47,456 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:12:47,456 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 25.011421, 'processed_files': [{'filename': 'BVLD9CCIUN6D2HGVETKO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/BVLD9CCIUN6D2HGVETKO.pdf'}, {'filename': 'ILOJODH5ADT10MHGK0TI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ILOJODH5ADT10MHGK0TI.pdf'}, {'filename': 'NMAGOA3H1CROTNXQ4GR8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'other'}, {'page_no': 4, 'doc_type': 'other'}, {'page_no': 5, 'doc_type': 'other'}, {'page_no': 6, 'doc_type': 'other'}, {'page_no': 7, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMAGOA3H1CROTNXQ4GR8.pdf'}, {'filename': 'NMWG7N543A7USM9VG3WI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/NMWG7N543A7USM9VG3WI.pdf'}, {'filename': 'QW3BG1TZMWHOTTZKGA8Y.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/QW3BG1TZMWHOTTZKGA8Y.pdf'}, {'filename': 'ULZ4VHKA9IH82VWCSF3C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/ULZ4VHKA9IH82VWCSF3C.pdf'}, {'filename': 'W7K89V5JE3EN9H4SOSTZ (1).pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ (1).pdf'}, {'filename': 'W7K89V5JE3EN9H4SOSTZ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/W7K89V5JE3EN9H4SOSTZ.pdf'}, {'filename': 'Z7I9W27YVP7F6SPWV8UK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/clear_to_pay/Z7I9W27YVP7F6SPWV8UK.pdf'}]}
