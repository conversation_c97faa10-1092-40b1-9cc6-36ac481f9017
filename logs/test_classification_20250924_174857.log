2025-09-24 17:48:57,883 - INFO - Logging initialized. Log file: logs/test_classification_20250924_174857.log
2025-09-24 17:48:57,883 - INFO - 📁 Found 7 files to process
2025-09-24 17:48:57,883 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:48:57,883 - INFO - 🚀 Processing 7 files in FORCED PARALLEL MODE...
2025-09-24 17:48:57,883 - INFO - 🚀 Creating 7 parallel tasks...
2025-09-24 17:48:57,883 - INFO - 🚀 All 7 tasks created - executing in parallel...
2025-09-24 17:48:57,883 - INFO - ⬆️ [17:48:57] Uploading: A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:00,946 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf -> s3://document-extraction-logistically/temp/816cfd5b_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:00,946 - INFO - 🔍 [17:49:00] Starting classification: A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:00,947 - INFO - ⬆️ [17:49:00] Uploading: DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:00,949 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:00,965 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:00,971 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/816cfd5b_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:00,971 - INFO - Processing PDF from S3...
2025-09-24 17:49:00,972 - INFO - Downloading PDF from S3 to /tmp/tmp7p5c4r2m/816cfd5b_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:01,613 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf -> s3://document-extraction-logistically/temp/40a466c7_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:01,613 - INFO - 🔍 [17:49:01] Starting classification: DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:01,614 - INFO - ⬆️ [17:49:01] Uploading: EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:01,615 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:01,631 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:01,636 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40a466c7_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:01,636 - INFO - Processing PDF from S3...
2025-09-24 17:49:01,637 - INFO - Downloading PDF from S3 to /tmp/tmpa5828n2r/40a466c7_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:02,253 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf -> s3://document-extraction-logistically/temp/6edf2da1_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:02,253 - INFO - 🔍 [17:49:02] Starting classification: EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:02,254 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:02,258 - INFO - ⬆️ [17:49:02] Uploading: FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:02,265 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:02,269 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6edf2da1_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:02,269 - INFO - Processing PDF from S3...
2025-09-24 17:49:02,270 - INFO - Downloading PDF from S3 to /tmp/tmp50ykwc7a/6edf2da1_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:02,921 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf -> s3://document-extraction-logistically/temp/e10c9688_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:02,921 - INFO - 🔍 [17:49:02] Starting classification: FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:02,922 - INFO - ⬆️ [17:49:02] Uploading: I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:02,923 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:02,939 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:02,946 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e10c9688_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:02,946 - INFO - Processing PDF from S3...
2025-09-24 17:49:02,946 - INFO - Downloading PDF from S3 to /tmp/tmpv2hqrsj9/e10c9688_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:03,293 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:49:03,293 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:03,294 - INFO - Splitting PDF 816cfd5b_A7CS2V2FOYJ4C84TQCE2 into 3 pages
2025-09-24 17:49:03,310 - INFO - Split PDF into 3 pages
2025-09-24 17:49:03,310 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:03,310 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:49:03,510 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf -> s3://document-extraction-logistically/temp/de63f770_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:03,510 - INFO - 🔍 [17:49:03] Starting classification: I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:03,511 - INFO - ⬆️ [17:49:03] Uploading: YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:03,513 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:03,531 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:03,534 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/de63f770_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:03,534 - INFO - Processing PDF from S3...
2025-09-24 17:49:03,535 - INFO - Downloading PDF from S3 to /tmp/tmp40owa9ah/de63f770_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:03,988 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:49:03,988 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:03,990 - INFO - Splitting PDF 40a466c7_DZSJ7XNECMHN04RDNRIA into 2 pages
2025-09-24 17:49:04,006 - INFO - Split PDF into 2 pages
2025-09-24 17:49:04,006 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:04,006 - INFO - Expected pages: [1, 2]
2025-09-24 17:49:04,087 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf -> s3://document-extraction-logistically/temp/6da5fe86_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:04,087 - INFO - 🔍 [17:49:04] Starting classification: YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:04,088 - INFO - ⬆️ [17:49:04] Uploading: ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:04,088 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:04,106 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:04,112 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6da5fe86_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:04,112 - INFO - Processing PDF from S3...
2025-09-24 17:49:04,112 - INFO - Downloading PDF from S3 to /tmp/tmpvf2bvhmb/6da5fe86_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:04,164 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:49:04,164 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:04,166 - INFO - Splitting PDF 6edf2da1_EOGW8CVHOLX2BALX5AA5 into 3 pages
2025-09-24 17:49:04,179 - INFO - Split PDF into 3 pages
2025-09-24 17:49:04,179 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:04,179 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:49:04,691 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf -> s3://document-extraction-logistically/temp/c4a2a97a_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:04,691 - INFO - 🔍 [17:49:04] Starting classification: ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:04,692 - INFO - Initializing TextractProcessor...
2025-09-24 17:49:04,705 - INFO - Initializing BedrockProcessor...
2025-09-24 17:49:04,709 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c4a2a97a_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:04,710 - INFO - Processing PDF from S3...
2025-09-24 17:49:04,710 - INFO - Downloading PDF from S3 to /tmp/tmpoxay2gg7/c4a2a97a_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:04,931 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:49:04,931 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:04,933 - INFO - Splitting PDF e10c9688_FSH16W3D0EIW0C8PSEF9 into 2 pages
2025-09-24 17:49:04,951 - INFO - Split PDF into 2 pages
2025-09-24 17:49:04,951 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:04,951 - INFO - Expected pages: [1, 2]
2025-09-24 17:49:05,291 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:49:05,291 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:05,292 - INFO - Splitting PDF de63f770_I3XU9KPI7B1QVKBAHOYB into 2 pages
2025-09-24 17:49:05,299 - INFO - Split PDF into 2 pages
2025-09-24 17:49:05,299 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:05,299 - INFO - Expected pages: [1, 2]
2025-09-24 17:49:05,966 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:49:05,966 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:05,967 - INFO - Splitting PDF 6da5fe86_YUC3NIJMZ2FWJZR6IN67 into 2 pages
2025-09-24 17:49:05,976 - INFO - Split PDF into 2 pages
2025-09-24 17:49:05,976 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:05,976 - INFO - Expected pages: [1, 2]
2025-09-24 17:49:06,451 - INFO - Page 3: Extracted 16 characters, 2 lines from 816cfd5b_A7CS2V2FOYJ4C84TQCE2_61ffca70_page_003.pdf
2025-09-24 17:49:06,451 - INFO - Successfully processed page 3
2025-09-24 17:49:06,708 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:49:06,709 - INFO - Splitting PDF into individual pages...
2025-09-24 17:49:06,712 - INFO - Splitting PDF c4a2a97a_ZXVPNXXEV01AJD108C3M into 3 pages
2025-09-24 17:49:06,727 - INFO - Split PDF into 3 pages
2025-09-24 17:49:06,727 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:49:06,727 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:49:08,286 - INFO - Page 1: Extracted 1548 characters, 74 lines from 6edf2da1_EOGW8CVHOLX2BALX5AA5_0198fd6c_page_001.pdf
2025-09-24 17:49:08,287 - INFO - Successfully processed page 1
2025-09-24 17:49:09,013 - INFO - Page 2: Extracted 275 characters, 10 lines from e10c9688_FSH16W3D0EIW0C8PSEF9_0f8c0e19_page_002.pdf
2025-09-24 17:49:09,014 - INFO - Successfully processed page 2
2025-09-24 17:49:09,136 - INFO - Page 2: Extracted 650 characters, 16 lines from de63f770_I3XU9KPI7B1QVKBAHOYB_ac767759_page_002.pdf
2025-09-24 17:49:09,136 - INFO - Successfully processed page 2
2025-09-24 17:49:09,305 - INFO - Page 3: Extracted 1638 characters, 27 lines from 6edf2da1_EOGW8CVHOLX2BALX5AA5_0198fd6c_page_003.pdf
2025-09-24 17:49:09,305 - INFO - Successfully processed page 3
2025-09-24 17:49:09,318 - INFO - Page 1: Extracted 1491 characters, 73 lines from e10c9688_FSH16W3D0EIW0C8PSEF9_0f8c0e19_page_001.pdf
2025-09-24 17:49:09,318 - INFO - Successfully processed page 1
2025-09-24 17:49:09,319 - INFO - Combined 2 pages into final text
2025-09-24 17:49:09,319 - INFO - Text validation for e10c9688_FSH16W3D0EIW0C8PSEF9: 1802 characters, 2 pages
2025-09-24 17:49:09,320 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:09,320 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:09,678 - INFO - Page 1: Extracted 3373 characters, 74 lines from 816cfd5b_A7CS2V2FOYJ4C84TQCE2_61ffca70_page_001.pdf
2025-09-24 17:49:09,679 - INFO - Successfully processed page 1
2025-09-24 17:49:09,762 - INFO - Page 2: Extracted 3517 characters, 68 lines from 816cfd5b_A7CS2V2FOYJ4C84TQCE2_61ffca70_page_002.pdf
2025-09-24 17:49:09,762 - INFO - Successfully processed page 2
2025-09-24 17:49:09,763 - INFO - Combined 3 pages into final text
2025-09-24 17:49:09,763 - INFO - Text validation for 816cfd5b_A7CS2V2FOYJ4C84TQCE2: 6961 characters, 3 pages
2025-09-24 17:49:09,763 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:09,763 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:10,003 - INFO - Page 2: Extracted 656 characters, 16 lines from 6da5fe86_YUC3NIJMZ2FWJZR6IN67_ae22e57d_page_002.pdf
2025-09-24 17:49:10,003 - INFO - Successfully processed page 2
2025-09-24 17:49:10,150 - INFO - Page 2: Extracted 2296 characters, 58 lines from 40a466c7_DZSJ7XNECMHN04RDNRIA_b779b933_page_002.pdf
2025-09-24 17:49:10,150 - INFO - Successfully processed page 2
2025-09-24 17:49:10,230 - INFO - Page 1: Extracted 1514 characters, 74 lines from de63f770_I3XU9KPI7B1QVKBAHOYB_ac767759_page_001.pdf
2025-09-24 17:49:10,230 - INFO - Successfully processed page 1
2025-09-24 17:49:10,231 - INFO - Combined 2 pages into final text
2025-09-24 17:49:10,231 - INFO - Text validation for de63f770_I3XU9KPI7B1QVKBAHOYB: 2200 characters, 2 pages
2025-09-24 17:49:10,232 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:10,232 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:10,398 - INFO - Page 2: Extracted 5315 characters, 51 lines from 6edf2da1_EOGW8CVHOLX2BALX5AA5_0198fd6c_page_002.pdf
2025-09-24 17:49:10,399 - INFO - Successfully processed page 2
2025-09-24 17:49:10,399 - INFO - Combined 3 pages into final text
2025-09-24 17:49:10,399 - INFO - Text validation for 6edf2da1_EOGW8CVHOLX2BALX5AA5: 8556 characters, 3 pages
2025-09-24 17:49:10,399 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:10,399 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:10,652 - INFO - Page 1: Extracted 1589 characters, 76 lines from 6da5fe86_YUC3NIJMZ2FWJZR6IN67_ae22e57d_page_001.pdf
2025-09-24 17:49:10,652 - INFO - Successfully processed page 1
2025-09-24 17:49:10,652 - INFO - Combined 2 pages into final text
2025-09-24 17:49:10,652 - INFO - Text validation for 6da5fe86_YUC3NIJMZ2FWJZR6IN67: 2281 characters, 2 pages
2025-09-24 17:49:10,653 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:10,653 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:10,681 - INFO - Page 3: Extracted 639 characters, 18 lines from c4a2a97a_ZXVPNXXEV01AJD108C3M_a3762251_page_003.pdf
2025-09-24 17:49:10,681 - INFO - Successfully processed page 3
2025-09-24 17:49:10,778 - INFO - Page 2: Extracted 1101 characters, 43 lines from c4a2a97a_ZXVPNXXEV01AJD108C3M_a3762251_page_002.pdf
2025-09-24 17:49:10,778 - INFO - Successfully processed page 2
2025-09-24 17:49:10,900 - INFO - Page 1: Extracted 3618 characters, 74 lines from 40a466c7_DZSJ7XNECMHN04RDNRIA_b779b933_page_001.pdf
2025-09-24 17:49:10,900 - INFO - Successfully processed page 1
2025-09-24 17:49:10,900 - INFO - Combined 2 pages into final text
2025-09-24 17:49:10,901 - INFO - Text validation for 40a466c7_DZSJ7XNECMHN04RDNRIA: 5950 characters, 2 pages
2025-09-24 17:49:10,901 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:10,901 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:11,387 - INFO - Page 1: Extracted 1786 characters, 73 lines from c4a2a97a_ZXVPNXXEV01AJD108C3M_a3762251_page_001.pdf
2025-09-24 17:49:11,387 - INFO - Successfully processed page 1
2025-09-24 17:49:11,388 - INFO - Combined 3 pages into final text
2025-09-24 17:49:11,388 - INFO - Text validation for c4a2a97a_ZXVPNXXEV01AJD108C3M: 3581 characters, 3 pages
2025-09-24 17:49:11,388 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:49:11,388 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:49:11,458 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'da0f6c10-ad2e-458c-90a2-596c2a6a0d1f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:11 GMT', 'content-type': 'application/json', 'content-length': '1031', 'connection': 'keep-alive', 'x-amzn-requestid': 'da0f6c10-ad2e-458c-90a2-596c2a6a0d1f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have two pages of a shipping instructions & rate confirmation. The document type likely is "rate_confirmation" (Carrier Rate Confirmation). It includes shipping instructions, order number, pickup, delivery, rate details. So doc_type: rate_confirmation. Are there any other types? It is not invoice, not BOL, not POD, etc. So both pages are part of same document, so both are rate_confirmation. Also check continuation signals: page 2 says "Page 2 of 2" but not "continued". So we can treat page2 as same doc type. So output: array of two objects: page_no 1 and 2 with doc_type rate_confirmation. Use tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3079, 'outputTokens': 177, 'totalTokens': 3256}, 'metrics': {'latencyMs': 972}}
2025-09-24 17:49:11,459 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e10c9688_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:11,489 - INFO - 

FSH16W3D0EIW0C8PSEF9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:49:11,489 - INFO - 

✓ Saved result: output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:49:12,387 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e10c9688_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:12,521 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'abe65e44-6f21-462e-9df4-23ff9f32605f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:12 GMT', 'content-type': 'application/json', 'content-length': '825', 'connection': 'keep-alive', 'x-amzn-requestid': 'abe65e44-6f21-462e-9df4-23ff9f32605f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a two-page document. The text suggests "Shipping Instructions & Rate Confirmation" header. That looks like "rate_confirmation". Keywords: "Rate Confirmation", "Carrier Rate", "Carrier Phone", etc. There\'s a "Total Amount", "Payment: Line Haul", etc. So classify as "rate_confirmation". The tool expects an output array of objects with page_no and doc_type. So we return two objects: page 1 and page 2 both rate_confirmation.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3199, 'outputTokens': 125, 'totalTokens': 3324}, 'metrics': {'latencyMs': 749}}
2025-09-24 17:49:12,522 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6da5fe86_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:12,548 - INFO - 

YUC3NIJMZ2FWJZR6IN67.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:49:12,548 - INFO - 

✓ Saved result: output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:49:12,720 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cc598109-65da-4b37-abfd-a8c0c9a27b90', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:12 GMT', 'content-type': 'application/json', 'content-length': '636', 'connection': 'keep-alive', 'x-amzn-requestid': 'cc598109-65da-4b37-abfd-a8c0c9a27b90'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 and page2 are a load confirmation (tender). The content: "LOAD CONFIRMATION" header. It\'s a tender from customer. The doc type should be tender_from_cust. Both pages same doc. So output for page 1 and page 2. Use tool.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4018, 'outputTokens': 101, 'totalTokens': 4119}, 'metrics': {'latencyMs': 648}}
2025-09-24 17:49:12,720 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40a466c7_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:12,764 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '52f0c764-98ed-4a9c-8f37-a44567894deb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:12 GMT', 'content-type': 'application/json', 'content-length': '1473', 'connection': 'keep-alive', 'x-amzn-requestid': '52f0c764-98ed-4a9c-8f37-a44567894deb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each of the 3 pages. The document appears to be a Rate Confirmation (Shipping Instructions & Rate Confirmation). It is a multi-page single document: pages 1-3 all part of the same “Shipping Instructions & Rate Confirmation”. The definition: rate_confirmation. The header says "Shipping Instructions & Rate Confirmation" and contains terms such as "Rate and Load Confirmation". So doc_type = rate_confirmation. For each page, we must output an object with page_no and doc_type.\n\nAlso check if any page is continuation. Page 2 starts: "This Rate and Load Confirmation ..." not "continued" but it\'s part of same doc. Page 3 starts: "Rate Confirmation, and invoice sent to payables..." Not "continued" but part of same. There\'s no explicit "continued". So each page gets same doc_type.\n\nHence output array:\n\n[\n {page_no:1, doc_type:"rate_confirmation"},\n {page_no:2, doc_type:"rate_confirmation"},\n {page_no:3, doc_type:"rate_confirmation"}\n]\n\nWe need to use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4471, 'outputTokens': 264, 'totalTokens': 4735}, 'metrics': {'latencyMs': 1261}}
2025-09-24 17:49:12,765 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6edf2da1_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:12,829 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'abc0ca40-8eef-480e-a2eb-c69c2410f2dd', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:12 GMT', 'content-type': 'application/json', 'content-length': '1650', 'connection': 'keep-alive', 'x-amzn-requestid': 'abc0ca40-8eef-480e-a2eb-c69c2410f2dd'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a two-page document. The content appears to be "Shipping Instructions & Rate Confirmation". It includes fields like "Bill To", "Order #", "Pickup Date", "Carrier", "Equipment", "Total Amount", "Pickup", "Delivery", weights, etc. It\'s a rate confirmation. It asks to classify each page with doc_type. According to definitions, "rate_confirmation" is "Carrier Rate Confirmation". The doc has header: "Shipping Instructions & Rate Confirmation". That likely indicates a rate confirmation. There is no other document type like invoice, etc. It\'s a rate confirmation, not a combined document. So each page should be doc_type: "rate_confirmation". Also check for continuation. Page 2 is continuation of page 1. The instructions say: "If the page starts with any of the following: \'continued\', ... or indicates pagination like \'page 2 of 3\', or any other signal indicating continuation of the previous page/document." Yes page 2 has "Page 2 of 2" and is continuation. So page 2 should have same doc_type as page 1. So output two objects:\n\n{\n  documents: [\n    { page_no: 1, doc_type: "rate_confirmation" },\n    { page_no: 2, doc_type: "rate_confirmation" }\n  ]\n}\n\nWe must call the tool classify_logistics_doc_type with this.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3178, 'outputTokens': 312, 'totalTokens': 3490}, 'metrics': {'latencyMs': 1491}}
2025-09-24 17:49:12,829 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/de63f770_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:12,834 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6da5fe86_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:12,892 - INFO - 

DZSJ7XNECMHN04RDNRIA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 17:49:12,892 - INFO - 

✓ Saved result: output/run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:49:13,176 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/40a466c7_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:13,243 - INFO - 

EOGW8CVHOLX2BALX5AA5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:49:13,243 - INFO - 

✓ Saved result: output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:13,341 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4ded5d48-4e65-4563-945b-2e55a991ccc4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:13 GMT', 'content-type': 'application/json', 'content-length': '2305', 'connection': 'keep-alive', 'x-amzn-requestid': '4ded5d48-4e65-4563-945b-2e55a991ccc4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The text is a Load Confirmation from EchoDrive. This appears to be a tender from customer? Actually it\'s a Load Confirmation, which is a document that confirms the load, presumably a tender from customer to carrier. The definition: tender_from_cust — Load Tender from Customer. It has keywords "Load Tender" maybe. The text includes "LOAD CONFIRMATION". It includes "Load Number 64145171" and "Echo tenders this Load as a broker only". So it\'s a load tender. So doc_type likely tender_from_cust.\n\nCheck other types: could it be rate_confirmation? It includes rate details: "Line Haul $299.00". It includes "Rate Confirmation". It says "Carrier Rate Confirmation"? It says "Carrier Rate Confirmation" at start? Let\'s see: In page1, header: "LOAD CONFIRMATION". Under "Service for Load # 64145171" there\'s "Rate". The term "Rate Confirmation" appears? At the start of page1, there is "LOAD CONFIRMATION". In page2, I saw "INVOICE PAYMENT REQUIREMENTS". There is "INVOICE". But the document is more like a tender: "Echo tenders this Load as a broker only and Carrier accepts this Load as the motor carrier responsible for its transport." That is a tender.\n\nThus it\'s tender_from_cust. It might also be a combined document? No, it\'s a single document. So each page is tender_from_cust. However, we need to check if any page is a continuation: Page3 only has "SIGNATURE: DATE:" which is continuation of page2. It\'s part of the same page. So all pages are same doc_type: tender_from_cust.\n\nLet\'s ensure no other type fits better: This is not a bill of lading, not invoice, not POD. It\'s a tender.\n\nThus output:\n\n{\n  documents: [\n    {page_no:1, doc_type:"tender_from_cust"},\n    {page_no:2, doc_type:"tender_from_cust"},\n    {page_no:3, doc_type:"tender_from_cust"}\n  ]\n}\n\nWe must use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"},{"page_no":3,"doc_type":"tender_from_cust"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4278, 'outputTokens': 496, 'totalTokens': 4774}, 'metrics': {'latencyMs': 2388}}
2025-09-24 17:49:13,341 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/816cfd5b_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:13,525 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6edf2da1_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:13,558 - INFO - 

I3XU9KPI7B1QVKBAHOYB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:49:13,558 - INFO - 

✓ Saved result: output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:49:13,560 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '345cc70e-b085-4137-91ae-79c200a817d1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:19:13 GMT', 'content-type': 'application/json', 'content-length': '983', 'connection': 'keep-alive', 'x-amzn-requestid': '345cc70e-b085-4137-91ae-79c200a817d1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 3 pages. They are a Shipping Instructions & Rate Confirmation. The document type is rate_confirmation. Keywords: "Rate Confirmation" appears in header. Also "Shipping Instructions & Rate Confirmation". So doc_type = rate_confirmation. All pages same.\n\nWe check for continuation signals: page 1 says "Page 1 of 3". Page 2 "Page 2 of 3". Not continuation markers but still part of same doc. So identify each page as rate_confirmation.\n\nReturn tool call with array of objects: page_no and doc_type.\n\nWe need to output via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3853, 'outputTokens': 160, 'totalTokens': 4013}, 'metrics': {'latencyMs': 960}}
2025-09-24 17:49:13,561 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c4a2a97a_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:13,849 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/de63f770_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:13,919 - INFO - 

A7CS2V2FOYJ4C84TQCE2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 3,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-24 17:49:13,919 - INFO - 

✓ Saved result: output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,249 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/816cfd5b_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:14,277 - INFO - 

ZXVPNXXEV01AJD108C3M.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:49:14,278 - INFO - 

✓ Saved result: output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,559 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c4a2a97a_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:14,560 - INFO - 
📊 Processing Summary:
2025-09-24 17:49:14,560 - INFO -    Total files: 7
2025-09-24 17:49:14,561 - INFO -    Successful: 7
2025-09-24 17:49:14,561 - INFO -    Failed: 0
2025-09-24 17:49:14,561 - INFO -    Duration: 16.68 seconds
2025-09-24 17:49:14,561 - INFO -    Output directory: output
2025-09-24 17:49:14,561 - INFO - 
📋 Successfully Processed Files:
2025-09-24 17:49:14,561 - INFO -    📄 A7CS2V2FOYJ4C84TQCE2.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"},{"page_no":3,"doc_type":"tender_from_cust"}]}
2025-09-24 17:49:14,562 - INFO -    📄 DZSJ7XNECMHN04RDNRIA.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-24 17:49:14,562 - INFO -    📄 EOGW8CVHOLX2BALX5AA5.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}
2025-09-24 17:49:14,562 - INFO -    📄 FSH16W3D0EIW0C8PSEF9.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:49:14,562 - INFO -    📄 I3XU9KPI7B1QVKBAHOYB.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:49:14,562 - INFO -    📄 YUC3NIJMZ2FWJZR6IN67.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 17:49:14,562 - INFO -    📄 ZXVPNXXEV01AJD108C3M.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}
2025-09-24 17:49:14,563 - INFO - 
============================================================================================================================================
2025-09-24 17:49:14,563 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 17:49:14,563 - INFO - ============================================================================================================================================
2025-09-24 17:49:14,563 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 17:49:14,563 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:49:14,563 - INFO - A7CS2V2FOYJ4C84TQCE2.pdf                           1      tender_from_cust                         run1_A7CS2V2FOYJ4C84TQCE2.json                                                  
2025-09-24 17:49:14,563 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:14,564 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,564 - INFO - 
2025-09-24 17:49:14,564 - INFO - A7CS2V2FOYJ4C84TQCE2.pdf                           2      tender_from_cust                         run1_A7CS2V2FOYJ4C84TQCE2.json                                                  
2025-09-24 17:49:14,564 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:14,564 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,564 - INFO - 
2025-09-24 17:49:14,564 - INFO - A7CS2V2FOYJ4C84TQCE2.pdf                           3      tender_from_cust                         run1_A7CS2V2FOYJ4C84TQCE2.json                                                  
2025-09-24 17:49:14,564 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:49:14,564 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,564 - INFO - 
2025-09-24 17:49:14,565 - INFO - DZSJ7XNECMHN04RDNRIA.pdf                           1      tender_from_cust                         run1_DZSJ7XNECMHN04RDNRIA.json                                                  
2025-09-24 17:49:14,565 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:14,565 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:49:14,565 - INFO - 
2025-09-24 17:49:14,565 - INFO - DZSJ7XNECMHN04RDNRIA.pdf                           2      tender_from_cust                         run1_DZSJ7XNECMHN04RDNRIA.json                                                  
2025-09-24 17:49:14,565 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:49:14,565 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:49:14,565 - INFO - 
2025-09-24 17:49:14,565 - INFO - EOGW8CVHOLX2BALX5AA5.pdf                           1      rate_confirmation                        run1_EOGW8CVHOLX2BALX5AA5.json                                                  
2025-09-24 17:49:14,565 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:14,565 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:14,565 - INFO - 
2025-09-24 17:49:14,565 - INFO - EOGW8CVHOLX2BALX5AA5.pdf                           2      rate_confirmation                        run1_EOGW8CVHOLX2BALX5AA5.json                                                  
2025-09-24 17:49:14,565 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:14,565 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:14,565 - INFO - 
2025-09-24 17:49:14,565 - INFO - EOGW8CVHOLX2BALX5AA5.pdf                           3      rate_confirmation                        run1_EOGW8CVHOLX2BALX5AA5.json                                                  
2025-09-24 17:49:14,565 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:49:14,565 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:14,565 - INFO - 
2025-09-24 17:49:14,566 - INFO - FSH16W3D0EIW0C8PSEF9.pdf                           1      rate_confirmation                        run1_FSH16W3D0EIW0C8PSEF9.json                                                  
2025-09-24 17:49:14,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:14,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:49:14,566 - INFO - 
2025-09-24 17:49:14,566 - INFO - FSH16W3D0EIW0C8PSEF9.pdf                           2      rate_confirmation                        run1_FSH16W3D0EIW0C8PSEF9.json                                                  
2025-09-24 17:49:14,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:49:14,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:49:14,566 - INFO - 
2025-09-24 17:49:14,566 - INFO - I3XU9KPI7B1QVKBAHOYB.pdf                           1      rate_confirmation                        run1_I3XU9KPI7B1QVKBAHOYB.json                                                  
2025-09-24 17:49:14,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:14,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:49:14,566 - INFO - 
2025-09-24 17:49:14,566 - INFO - I3XU9KPI7B1QVKBAHOYB.pdf                           2      rate_confirmation                        run1_I3XU9KPI7B1QVKBAHOYB.json                                                  
2025-09-24 17:49:14,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:49:14,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:49:14,566 - INFO - 
2025-09-24 17:49:14,566 - INFO - YUC3NIJMZ2FWJZR6IN67.pdf                           1      rate_confirmation                        run1_YUC3NIJMZ2FWJZR6IN67.json                                                  
2025-09-24 17:49:14,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:14,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:49:14,566 - INFO - 
2025-09-24 17:49:14,566 - INFO - YUC3NIJMZ2FWJZR6IN67.pdf                           2      rate_confirmation                        run1_YUC3NIJMZ2FWJZR6IN67.json                                                  
2025-09-24 17:49:14,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:49:14,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:49:14,567 - INFO - 
2025-09-24 17:49:14,567 - INFO - ZXVPNXXEV01AJD108C3M.pdf                           1      rate_confirmation                        run1_ZXVPNXXEV01AJD108C3M.json                                                  
2025-09-24 17:49:14,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:14,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,567 - INFO - 
2025-09-24 17:49:14,567 - INFO - ZXVPNXXEV01AJD108C3M.pdf                           2      rate_confirmation                        run1_ZXVPNXXEV01AJD108C3M.json                                                  
2025-09-24 17:49:14,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:14,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,567 - INFO - 
2025-09-24 17:49:14,567 - INFO - ZXVPNXXEV01AJD108C3M.pdf                           3      rate_confirmation                        run1_ZXVPNXXEV01AJD108C3M.json                                                  
2025-09-24 17:49:14,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:49:14,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,567 - INFO - 
2025-09-24 17:49:14,567 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 17:49:14,568 - INFO - Total entries: 17
2025-09-24 17:49:14,568 - INFO - ============================================================================================================================================
2025-09-24 17:49:14,568 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 17:49:14,568 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:49:14,568 - INFO -   1. A7CS2V2FOYJ4C84TQCE2.pdf            Page 1   → tender_from_cust | run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,568 - INFO -   2. A7CS2V2FOYJ4C84TQCE2.pdf            Page 2   → tender_from_cust | run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,568 - INFO -   3. A7CS2V2FOYJ4C84TQCE2.pdf            Page 3   → tender_from_cust | run1_A7CS2V2FOYJ4C84TQCE2.json
2025-09-24 17:49:14,568 - INFO -   4. DZSJ7XNECMHN04RDNRIA.pdf            Page 1   → tender_from_cust | run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:49:14,568 - INFO -   5. DZSJ7XNECMHN04RDNRIA.pdf            Page 2   → tender_from_cust | run1_DZSJ7XNECMHN04RDNRIA.json
2025-09-24 17:49:14,568 - INFO -   6. EOGW8CVHOLX2BALX5AA5.pdf            Page 1   → rate_confirmation | run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:14,568 - INFO -   7. EOGW8CVHOLX2BALX5AA5.pdf            Page 2   → rate_confirmation | run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:14,568 - INFO -   8. EOGW8CVHOLX2BALX5AA5.pdf            Page 3   → rate_confirmation | run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:49:14,569 - INFO -   9. FSH16W3D0EIW0C8PSEF9.pdf            Page 1   → rate_confirmation | run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:49:14,569 - INFO -  10. FSH16W3D0EIW0C8PSEF9.pdf            Page 2   → rate_confirmation | run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:49:14,569 - INFO -  11. I3XU9KPI7B1QVKBAHOYB.pdf            Page 1   → rate_confirmation | run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:49:14,569 - INFO -  12. I3XU9KPI7B1QVKBAHOYB.pdf            Page 2   → rate_confirmation | run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:49:14,569 - INFO -  13. YUC3NIJMZ2FWJZR6IN67.pdf            Page 1   → rate_confirmation | run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:49:14,569 - INFO -  14. YUC3NIJMZ2FWJZR6IN67.pdf            Page 2   → rate_confirmation | run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:49:14,569 - INFO -  15. ZXVPNXXEV01AJD108C3M.pdf            Page 1   → rate_confirmation | run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,569 - INFO -  16. ZXVPNXXEV01AJD108C3M.pdf            Page 2   → rate_confirmation | run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,569 - INFO -  17. ZXVPNXXEV01AJD108C3M.pdf            Page 3   → rate_confirmation | run1_ZXVPNXXEV01AJD108C3M.json
2025-09-24 17:49:14,569 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 17:49:14,570 - INFO - 
✅ Test completed: {'total_files': 7, 'processed': 7, 'failed': 0, 'errors': [], 'duration_seconds': 16.676867, 'processed_files': [{'filename': 'A7CS2V2FOYJ4C84TQCE2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}, {'page_no': 3, 'doc_type': 'tender_from_cust'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf'}, {'filename': 'DZSJ7XNECMHN04RDNRIA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf'}, {'filename': 'EOGW8CVHOLX2BALX5AA5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf'}, {'filename': 'FSH16W3D0EIW0C8PSEF9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf'}, {'filename': 'I3XU9KPI7B1QVKBAHOYB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf'}, {'filename': 'YUC3NIJMZ2FWJZR6IN67.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf'}, {'filename': 'ZXVPNXXEV01AJD108C3M.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf'}]}
