{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 3.5557158639676345e-07, "Y": 0.0}, {"X": 1.0, "Y": 7.703799929004163e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "14907a22-888f-4200-9a52-dbf305ffb50e", "Relationships": [{"Type": "CHILD", "Ids": ["cd35ee1f-0c31-4faf-9426-2ce429d6188f", "3624b878-9bee-4386-b541-d8f43909954e", "1c622fb6-acd4-4404-a57c-5e576f0dd328", "643d43d0-7147-45d2-9a44-ddfa80863782", "dd14ded7-b717-40ac-a890-60ffec5d003b", "011d8e8d-d38c-4bb8-8559-847df7c2683f", "43bb9805-51b2-4c60-8b79-8a29db166ea8", "d9fe69dc-1985-45da-9330-b96876096c7c", "02c74cea-7074-4fac-b630-2f267449d3ba", "a3e021be-6897-43c6-b6ba-8f6845e9a88b", "8e0f0967-9654-4409-b5da-19888b11c50b", "8932d52a-618f-4a68-b0b3-021208a5806a", "1e94059f-4f17-493c-98d9-f5fb63fcf598", "c8779530-53a9-40c2-8a0b-43bc277b5ff4", "7dbc2c7a-565a-4412-91fe-c9613813f789", "0afaafa9-8e2c-4330-b458-e5bab3553061", "57697a0d-477b-4bab-9226-928d46204e31", "7db21e73-b121-4aff-a52f-7f7c56d73d28", "c50c7768-966a-49c8-9454-6038869ab3ac", "4d0cf8c8-4d61-4dd7-9fdc-0286c216f73b", "4f8b13b3-9ed1-4ba3-8511-7a7da25c3f2e", "61b6acfa-b084-446c-a191-6a23e904efb8", "faf30ca1-d290-4ad4-bb34-0bc7eada557d", "be68db16-45ec-4136-9c89-918eb4b67f81", "a0c3606b-1a0f-44c3-aed1-4a4469e5e967", "b8023a77-6cfd-4f24-8da1-bcc493ed190b", "23ac653e-a562-45bf-980e-893a3dc51ee6", "11815453-720f-426c-982e-e1381cbd4926", "074a7f60-7bd6-4ac8-bb45-2469dd0a2a43", "89e19157-6fd5-48c5-ae25-44f8082b9e12", "2bfe7517-506e-4ac4-a2f0-5ef287d31b17", "699b1602-1abb-4ea9-9407-b16746a1faff", "bd869b8f-47e1-4fe7-9176-513b9227c2e4", "51eec608-9546-4806-9c11-ade9475f9cf3", "65be194b-4fe4-4390-914a-cc991eedd216", "a2348c1e-ff96-4e5a-a932-c2fc0b7e0e49", "080b92fc-ae5b-48d8-9e50-65b92abe32c7", "6581cfbc-2c09-4fa5-98a1-6afec2f53470", "97bcc81a-23ca-41b4-9253-79035386be14", "f480e16f-21b5-43ad-b7f9-e99ecaaee333", "4a917911-781a-4d4d-964e-771a02cdb93b", "f08474db-e25f-416d-a91e-4d1f0f79d1da", "5c8e4274-976f-4320-aaf1-65ad0ba0ac09", "606e4a49-bf1b-4b83-9a81-8a9b51f75adc", "8677559b-d28a-4c36-9c43-761c57d4126d", "1368597a-099c-4680-bf3c-bb041488053c", "7ac5cced-f752-4a80-aa22-9a5e88ca7ac5", "07bdb5e5-be85-4467-b523-2e7c77bdddb1", "6ef199b9-a2d5-469e-a722-631aaf11a7af", "4261952e-0e8e-4688-8c9e-d320f4165cca", "e5be6c31-e279-49ce-9971-9bf52e9335d7", "f4d2947d-9ac1-4664-9afc-1822cf8e00d0", "19e606a3-0110-4d35-95bc-e886a7a2408f", "6a06da94-357c-4918-a10b-0450d2bc14cd", "ec03fbd5-dda2-4612-bd71-0f91f8682d56", "73154f62-f96a-4e04-ae25-710ab34f7a35", "91ea1e7b-2090-4b0e-8988-cf03eaba1d99", "9e9c97ab-7a0d-46fa-8d26-67876dd6f6ca", "abb16d22-e25a-43d0-baa4-e1a8dc688365"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "HTLF Bank", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.010007196106016636, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.044180043041706085}]}, "Id": "cd35ee1f-0c31-4faf-9426-2ce429d6188f", "Relationships": [{"Type": "CHILD", "Ids": ["20c7e8ec-2160-4340-9f9a-9f9a19b82e30", "084ff59b-c996-46be-a009-3f792ce9fbab"]}]}, {"BlockType": "LINE", "Confidence": 99.*************, "Text": "Outgoing Wire Transfer Request", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.053061485290527344}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.07127656042575836}]}, "Id": "3624b878-9bee-4386-b541-d8f43909954e", "Relationships": [{"Type": "CHILD", "Ids": ["f3f5e663-82e5-4101-a175-3394f8cac527", "f61e5f62-22c2-4285-b78e-4b7bedfe4f86", "5cacddea-c3ac-487f-b6cf-833decbae342", "12720b0d-1d47-4fea-8809-cb8b441de499"]}]}, {"BlockType": "LINE", "Confidence": 99.4140625, "Text": "Date: 02/07/2025 11:12:43", "Geometry": {"BoundingBox": {"Width": 0.19112251698970795, "Height": 0.010882832109928131, "Left": 0.10548191517591476, "Top": 0.08546706289052963}, "Polygon": [{"X": 0.10548628121614456, "Y": 0.08546706289052963}, {"X": 0.2966044247150421, "Y": 0.08568000048398972}, {"X": 0.29659998416900635, "Y": 0.09634989500045776}, {"X": 0.10548191517591476, "Y": 0.09613694995641708}]}, "Id": "1c622fb6-acd4-4404-a57c-5e576f0dd328", "Relationships": [{"Type": "CHILD", "Ids": ["402f3d78-ff63-42e7-8d4f-f6a23ee5bc0d", "78f286ac-24ff-42ba-a3e9-ed0d7fbcf31a", "9f29bfe2-f803-4a25-b212-a3f543e49c21"]}]}, {"BlockType": "LINE", "Confidence": 99.82991790771484, "Text": "Wire Transfer Message Reference Number:", "Geometry": {"BoundingBox": {"Width": 0.3154875934123993, "Height": 0.013764197938144207, "Left": 0.10531511902809143, "Top": 0.09994842857122421}, "Polygon": [{"X": 0.10532061010599136, "Y": 0.09994842857122421}, {"X": 0.4208027124404907, "Y": 0.10029993951320648}, {"X": 0.42079704999923706, "Y": 0.11371263116598129}, {"X": 0.10531511902809143, "Y": 0.11336110532283783}]}, "Id": "643d43d0-7147-45d2-9a44-ddfa80863782", "Relationships": [{"Type": "CHILD", "Ids": ["8e13d51d-835a-4b8a-9a3e-254bac3d574c", "017dbf4a-ea0d-4495-8478-391bb8964113", "a0aadf7c-2f50-4b85-8a67-71c616f66672", "a8df33bd-9f9b-4e2f-b46b-4e1ee6ade68b", "0e8cef70-d186-4833-940b-c8a936f86f1c"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "2025038000120600", "Geometry": {"BoundingBox": {"Width": 0.14914968609809875, "Height": 0.012380646541714668, "Left": 0.47030505537986755, "Top": 0.10340685397386551}, "Polygon": [{"X": 0.4703102111816406, "Y": 0.10340685397386551}, {"X": 0.6194547414779663, "Y": 0.1035730317234993}, {"X": 0.6194494962692261, "Y": 0.11578750610351562}, {"X": 0.47030505537986755, "Y": 0.11562132090330124}]}, "Id": "dd14ded7-b717-40ac-a890-60ffec5d003b", "Relationships": [{"Type": "CHILD", "Ids": ["4a2f2e5a-2bc9-4038-bd93-b42e5df84c2b"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "WIRE TO:", "Geometry": {"BoundingBox": {"Width": 0.07278941571712494, "Height": 0.009073537774384022, "Left": 0.123**************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.123**************, "Y": 0.*****************}]}, "Id": "011d8e8d-d38c-4bb8-8559-847df7c2683f", "Relationships": [{"Type": "CHILD", "Ids": ["72920fac-e1d5-47b7-b231-42ea446bdd62", "f1b13618-792f-408e-9453-570b3bacd404"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "Bank ABA No:", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.009662535041570663, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "43bb9805-51b2-4c60-8b79-8a29db166ea8", "Relationships": [{"Type": "CHILD", "Ids": ["a4b830fb-11f8-4359-ab07-9c999856f439", "1a01e384-fc42-47c1-ba47-6b87d53a8a9c", "1a52dba7-9e91-432f-8136-f8925970ce67"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "*********", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008926702663302422, "Left": 0.**************, "Top": 0.***************}, "Polygon": [{"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.**************, "Y": 0.*****************}]}, "Id": "d9fe69dc-1985-45da-9330-b96876096c7c", "Relationships": [{"Type": "CHILD", "Ids": ["e4c765e2-e5a3-43dc-99f6-4dded0038e43"]}]}, {"BlockType": "LINE", "Confidence": 99.********, "Text": "Bank Name:", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009055928327143192, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "02c74cea-7074-4fac-b630-2f267449d3ba", "Relationships": [{"Type": "CHILD", "Ids": ["4c24e7b9-5ef5-44d3-b293-d9eda7058a62", "dfccea4c-582a-46c1-bce3-e610b69468f4"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "BK AMER NYC", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009178164415061474, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.16501623392105103}]}, "Id": "a3e021be-6897-43c6-b6ba-8f6845e9a88b", "Relationships": [{"Type": "CHILD", "Ids": ["d4d96573-7676-4cd6-8051-7b36788b2a7f", "564c206f-442b-4390-8d0b-615cdda4b824", "e36fe0ec-2f0f-4c37-a1be-0331baa28453"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "Amount:", "Geometry": {"BoundingBox": {"Width": 0.053944338113069534, "Height": 0.00878367479890585, "Left": 0.10365726798772812, "Top": 0.16590599715709686}, "Polygon": [{"X": 0.10366084426641464, "Y": 0.16590599715709686}, {"X": 0.15760160982608795, "Y": 0.16596610844135284}, {"X": 0.15759801864624023, "Y": 0.1746896654367447}, {"X": 0.10365726798772812, "Y": 0.1746295541524887}]}, "Id": "8e0f0967-9654-4409-b5da-19888b11c50b", "Relationships": [{"Type": "CHILD", "Ids": ["f2f762ef-0ffe-4ffb-891e-af3c9d7a4962"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "750.00", "Geometry": {"BoundingBox": {"Width": 0.04192624241113663, "Height": 0.008782812394201756, "Left": 0.28693363070487976, "Top": 0.16809536516666412}, "Polygon": [{"X": 0.28693726658821106, "Y": 0.16809536516666412}, {"X": 0.3288598954677582, "Y": 0.16814208030700684}, {"X": 0.3288562297821045, "Y": 0.1768781840801239}, {"X": 0.28693363070487976, "Y": 0.17683145403862}]}, "Id": "8932d52a-618f-4a68-b0b3-021208a5806a", "Relationships": [{"Type": "CHILD", "Ids": ["6375833e-e7d5-459e-8ccd-34fec17e38f8"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "Special Instructions (BBI):", "Geometry": {"BoundingBox": {"Width": 0.16385464370250702, "Height": 0.011922648176550865, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "1e94059f-4f17-493c-98d9-f5fb63fcf598", "Relationships": [{"Type": "CHILD", "Ids": ["8034b0fc-e215-49e0-b35f-45d4355ff13e", "ec763e2a-2505-4bfd-a3a4-5045e2fb1fc4", "8ca87f49-01c6-4f07-b1a9-063957e3680b"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "BENEFICIARY BANK:", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.010004955343902111, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "c8779530-53a9-40c2-8a0b-43bc277b5ff4", "Relationships": [{"Type": "CHILD", "Ids": ["510943cf-6f2a-4e6c-9c7f-1462e3b3a9f2", "d5eac826-370a-4a82-8d7b-0eeefd5d34f6"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "Bank ABA No:", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009198099374771118, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "7dbc2c7a-565a-4412-91fe-c9613813f789", "Relationships": [{"Type": "CHILD", "Ids": ["c87e9568-9c74-4903-b2b2-05838a55d8c4", "7cb7d58b-1131-4ebd-9e0e-1dad870e4b69", "8b2519df-9d2e-41c7-b1c7-eca8dbcfd6e6"]}]}, {"BlockType": "LINE", "Confidence": 99.**********, "Text": "Bank Name:", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "0afaafa9-8e2c-4330-b458-e5bab3553061", "Relationships": [{"Type": "CHILD", "Ids": ["1b1b6321-4570-4764-927b-edcd8a029623", "175d4301-9555-4f1a-bc07-2992080c7aab"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "CREDIT TO:", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*********5741577}]}, "Id": "57697a0d-477b-4bab-9226-928d46204e31", "Relationships": [{"Type": "CHILD", "Ids": ["5ddfd849-d074-4ac9-84bc-750469c98add", "d393eaad-ae20-44ed-b226-4d95ea2b0b31"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Name:", "Geometry": {"BoundingBox": {"Width": 0.04029395058751106, "Height": 0.00865392480045557, "Left": 0.10172683000564575, "Top": 0.26473817229270935}, "Polygon": [{"X": 0.1017303466796875, "Y": 0.26473817229270935}, {"X": 0.14202077686786652, "Y": 0.2647830843925476}, {"X": 0.14201724529266357, "Y": 0.27339211106300354}, {"X": 0.10172683000564575, "Y": 0.2733471989631653}]}, "Id": "7db21e73-b121-4aff-a52f-7f7c56d73d28", "Relationships": [{"Type": "CHILD", "Ids": ["bbd6823a-b33b-4440-99e8-e284fde6a1e0"]}]}, {"BlockType": "LINE", "Confidence": 99.70543670654297, "Text": "WOODLANDS TRANSPORT, LLC", "Geometry": {"BoundingBox": {"Width": 0.22734545171260834, "Height": 0.011898632161319256, "Left": 0.28522101044654846, "Top": 0.26698753237724304}, "Polygon": [{"X": 0.28522586822509766, "Y": 0.26698753237724304}, {"X": 0.5125664472579956, "Y": 0.267240971326828}, {"X": 0.5125615000724792, "Y": 0.27888616919517517}, {"X": 0.28522101044654846, "Y": 0.2786327600479126}]}, "Id": "c50c7768-966a-49c8-9454-6038869ab3ac", "Relationships": [{"Type": "CHILD", "Ids": ["8e30396b-32d9-46e4-bd67-f4a8f8870a9a", "31654d25-d5b9-4b46-9b60-02db32d3c6a6", "e9f16b76-783b-40f3-a959-376ac0cdbd9d"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.05404909700155258, "Height": 0.008698641322553158, "Left": 0.10155691206455231, "Top": 0.27727097272872925}, "Polygon": [{"X": 0.10156045109033585, "Y": 0.27727097272872925}, {"X": 0.15560601651668549, "Y": 0.27733123302459717}, {"X": 0.15560245513916016, "Y": 0.285969614982605}, {"X": 0.10155691206455231, "Y": 0.28590938448905945}]}, "Id": "4d0cf8c8-4d61-4dd7-9fdc-0286c216f73b", "Relationships": [{"Type": "CHILD", "Ids": ["27b1c4bf-c625-4ca8-a713-92f2c315df3d"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "2700 TECHNOLOGY", "Geometry": {"BoundingBox": {"Width": 0.1382671594619751, "Height": 0.00949722807854414, "Left": 0.28472059965133667, "Top": 0.2794714868068695}, "Polygon": [{"X": 0.28472447395324707, "Y": 0.2794714868068695}, {"X": 0.42298775911331177, "Y": 0.27962562441825867}, {"X": 0.4229838252067566, "Y": 0.2889687120914459}, {"X": 0.28472059965133667, "Y": 0.28881460428237915}]}, "Id": "4f8b13b3-9ed1-4ba3-8511-7a7da25c3f2e", "Relationships": [{"Type": "CHILD", "Ids": ["85e539a2-3af2-4d82-9929-53cecd7a0171", "2a48d9d6-7a82-495a-b50c-a1a8c601c03f"]}]}, {"BlockType": "LINE", "Confidence": 99.560546875, "Text": "FOREST BLVD STE 250", "Geometry": {"BoundingBox": {"Width": 0.1600300669670105, "Height": 0.00991329550743103, "Left": 0.1017124131321907, "Top": 0.2893182337284088}, "Polygon": [{"X": 0.10171639174222946, "Y": 0.2893182337284088}, {"X": 0.2617424726486206, "Y": 0.28949663043022156}, {"X": 0.26173844933509827, "Y": 0.29923152923583984}, {"X": 0.1017124131321907, "Y": 0.2990531325340271}]}, "Id": "61b6acfa-b084-446c-a191-6a23e904efb8", "Relationships": [{"Type": "CHILD", "Ids": ["0d229859-e2be-4871-9461-11b3f0bba7ee", "7aa4c596-3899-47de-8b6e-0f7ca2bdc0b4", "7e6595ab-ff95-45fd-95a6-4465f81dcdbb", "1832081c-8e72-4cbb-8e2d-d07f2a646bfb"]}]}, {"BlockType": "LINE", "Confidence": 99.38496398925781, "Text": "THE WOODLANDS, TX 77381-3909", "Geometry": {"BoundingBox": {"Width": 0.23791895806789398, "Height": 0.011534041725099087, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.10105062276124954, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "faf30ca1-d290-4ad4-bb34-0bc7eada557d", "Relationships": [{"Type": "CHILD", "Ids": ["b87b907d-1d99-4d0c-b86d-b56589792ead", "7ff4850b-1edb-469f-b943-39fb80b9a11a", "445de1e6-0a0c-4697-af30-2813fa9a0ae7", "a757816c-dee0-40fe-bbcd-a5040f846c2b"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "Account Number:", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "be68db16-45ec-4136-9c89-918eb4b67f81", "Relationships": [{"Type": "CHILD", "Ids": ["f29d253f-d2bd-473d-b274-9499ec4f8a94", "c76e0d1f-9443-4dec-ab4b-d348e63e8ece"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "************", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009159157052636147, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.**************02}]}, "Id": "a0c3606b-1a0f-44c3-aed1-4a4469e5e967", "Relationships": [{"Type": "CHILD", "Ids": ["a7b4ee52-82e6-47e6-a7e4-145a9689c9f3"]}]}, {"BlockType": "LINE", "Confidence": 99.77791595458984, "Text": "Special Instructions (OBI):", "Geometry": {"BoundingBox": {"Width": 0.1648186594247818, "Height": 0.011956693604588509, "Left": 0.10108193010091782, "Top": 0.3386909067630768}, "Polygon": [{"X": 0.10108675062656403, "Y": 0.3386909067630768}, {"X": 0.265900582075119, "Y": 0.3388746678829193}, {"X": 0.26589569449424744, "Y": 0.35064759850502014}, {"X": 0.10108193010091782, "Y": 0.3504638671875}]}, "Id": "b8023a77-6cfd-4f24-8da1-bcc493ed190b", "Relationships": [{"Type": "CHILD", "Ids": ["ca7fed3a-19fd-410d-83fe-d53c0020b428", "b93f67bb-d520-4beb-a896-aecb7ada3bf8", "723b8841-db90-4cf3-b38c-d365242a8282"]}]}, {"BlockType": "LINE", "Confidence": 99.90194702148438, "Text": "CHARGE TO:", "Geometry": {"BoundingBox": {"Width": 0.09672243148088455, "Height": 0.009510510601103306, "Left": 0.1194073036313057, "Top": 0.3635045886039734}, "Polygon": [{"X": 0.1194111555814743, "Y": 0.3635045886039734}, {"X": 0.21612973511219025, "Y": 0.36361244320869446}, {"X": 0.21612584590911865, "Y": 0.3730151057243347}, {"X": 0.1194073036313057, "Y": 0.37290728092193604}]}, "Id": "23ac653e-a562-45bf-980e-893a3dc51ee6", "Relationships": [{"Type": "CHILD", "Ids": ["d8ebfc17-b33a-4f5d-a201-a51ca94d279c", "209bba7c-e6f1-4a6d-907c-6c024e1000a4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Name:", "Geometry": {"BoundingBox": {"Width": 0.04015088453888893, "Height": 0.008593789301812649, "Left": 0.09991026669740677, "Top": 0.3756920099258423}, "Polygon": [{"X": 0.09991376101970673, "Y": 0.3756920099258423}, {"X": 0.140061154961586, "Y": 0.375736802816391}, {"X": 0.14005763828754425, "Y": 0.3842858076095581}, {"X": 0.09991026669740677, "Y": 0.3842410445213318}]}, "Id": "11815453-720f-426c-982e-e1381cbd4926", "Relationships": [{"Type": "CHILD", "Ids": ["fa1f2198-8595-4366-9752-d6f2a64731c6"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "TEXAS CONTRACT ROUSTABOUT LLC", "Geometry": {"BoundingBox": {"Width": 0.27418285608291626, "Height": 0.011612147092819214, "Left": 0.2829388976097107, "Top": 0.3776739537715912}, "Polygon": [{"X": 0.28294360637664795, "Y": 0.3776739537715912}, {"X": 0.557121753692627, "Y": 0.37797969579696655}, {"X": 0.5571169257164001, "Y": 0.3892861008644104}, {"X": 0.2829388976097107, "Y": 0.3889803886413574}]}, "Id": "074a7f60-7bd6-4ac8-bb45-2469dd0a2a43", "Relationships": [{"Type": "CHILD", "Ids": ["cd9f3783-f30e-4f12-8807-f0f5ce5d84ac", "89ef3fb7-5d7a-4f69-b57a-965d2f045655", "2ff6626e-5057-4682-a1a0-91f2dbf657b2", "25eca990-902c-4161-98e5-62d2b3749384"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.05423600599169731, "Height": 0.008727127686142921, "Left": 0.099732905626297, "Top": 0.388418048620224}, "Polygon": [{"X": 0.09973645210266113, "Y": 0.388418048620224}, {"X": 0.1539689153432846, "Y": 0.38847851753234863}, {"X": 0.15396533906459808, "Y": 0.39714518189430237}, {"X": 0.099732905626297, "Y": 0.39708471298217773}]}, "Id": "89e19157-6fd5-48c5-ae25-44f8082b9e12", "Relationships": [{"Type": "CHILD", "Ids": ["73290a7b-3517-4656-bae1-eb336112277d"]}]}, {"BlockType": "LINE", "Confidence": 99.73174285888672, "Text": "2906 DENISON AVE", "Geometry": {"BoundingBox": {"Width": 0.1368737667798996, "Height": 0.00991442147642374, "Left": 0.28277796506881714, "Top": 0.39035454392433167}, "Polygon": [{"X": 0.28278201818466187, "Y": 0.39035454392433167}, {"X": 0.41965171694755554, "Y": 0.39050716161727905}, {"X": 0.41964760422706604, "Y": 0.4002689719200134}, {"X": 0.28277796506881714, "Y": 0.40011635422706604}]}, "Id": "2bfe7517-506e-4ac4-a2f0-5ef287d31b17", "Relationships": [{"Type": "CHILD", "Ids": ["95a131f8-6524-40d2-bd3b-38f5fa5f8c3c", "7113e9bb-fc3a-45ff-a34f-8066a34a4d02", "0bcbe92c-d4dc-4883-9eeb-dd46291d08f4"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.053599581122398376, "Height": 0.008574213832616806, "Left": 0.099972665309906, "Top": 0.40091922879219055}, "Polygon": [{"X": 0.09997614473104477, "Y": 0.40091922879219055}, {"X": 0.15357224643230438, "Y": 0.40097901225090027}, {"X": 0.15356874465942383, "Y": 0.40949344635009766}, {"X": 0.099972665309906, "Y": 0.4094336926937103}]}, "Id": "699b1602-1abb-4ea9-9407-b16746a1faff", "Relationships": [{"Type": "CHILD", "Ids": ["1890dd06-7225-4909-9480-7a1d414df78b"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "SNYDER TX 79549", "Geometry": {"BoundingBox": {"Width": 0.12682190537452698, "Height": 0.009644769132137299, "Left": 0.28325334191322327, "Top": 0.40296220779418945}, "Polygon": [{"X": 0.28325727581977844, "Y": 0.40296220779418945}, {"X": 0.41007524728775024, "Y": 0.40310361981391907}, {"X": 0.4100712239742279, "Y": 0.41260698437690735}, {"X": 0.28325334191322327, "Y": 0.41246554255485535}]}, "Id": "bd869b8f-47e1-4fe7-9176-513b9227c2e4", "Relationships": [{"Type": "CHILD", "Ids": ["e0f7948f-9188-4588-ac67-c3fbf416ba20", "c6200d6d-bde3-4a3c-8af5-50329e4e37ad", "edcd055c-1d23-4027-883e-f89ccc32cbf6"]}]}, {"BlockType": "LINE", "Confidence": 99.92420196533203, "Text": "Acct. No. to be Debited: **********", "Geometry": {"BoundingBox": {"Width": 0.23269414901733398, "Height": 0.010705971159040928, "Left": 0.*****************, "Top": 0.41321390867233276}, "Polygon": [{"X": 0.09953618049621582, "Y": 0.41321390867233276}, {"X": 0.332226037979126, "Y": 0.4134733974933624}, {"X": 0.3322216868400574, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "51eec608-9546-4806-9c11-ade9475f9cf3", "Relationships": [{"Type": "CHILD", "Ids": ["e2df2b7c-6267-4e50-9d61-9276e45901a2", "3c82b5f1-5217-4a3d-b52e-a483fa05efc9", "fbbc66a4-6974-4166-b7db-d22c81661dc6", "540fafc7-ae64-47b8-aad9-e34af71e9080", "5571f474-bc12-426e-a01e-6aaa57b1eee2", "a5db0a22-38bc-4519-b4ed-54b8e9860ffc"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "I hereby authorize the bank to execute the wire transfer of funds according to my instructions stated above and to charge my", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.018511701375246048, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.***************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "65be194b-4fe4-4390-914a-cc991eedd216", "Relationships": [{"Type": "CHILD", "Ids": ["62dd8fa4-4398-4f7b-a7ff-638b330e3f6c", "d10d7388-e0ce-478c-bdf4-75d09dc34995", "ddee104e-0cdb-4951-9766-f08883605307", "5280d888-de80-4574-a18a-bbe06965ab83", "13657ca0-48fa-4d94-89d0-85ba36a6c24e", "017af653-237f-45e0-822b-498481ea94f4", "d8b23b74-3491-4595-bb7e-b264ec110db0", "72c21efa-41d8-4b12-8a5b-10dd164671bf", "9fc55dfd-e541-4020-98ae-2f1be3175adc", "ba3efa73-674b-4634-9e09-d5a1a352bc53", "a2064664-17ab-4ad3-9c74-9a9ce0c5d59e", "3972932d-987b-4fce-b9fb-aed47f54980c", "9887c52a-516f-4bf5-83e9-9e849d24501f", "6ebdec5b-b7fa-4de1-85b1-ace27231ddf9", "e50abbab-4a57-4005-8822-8494dba3dd74", "04223ff0-bf28-4520-8638-4e0249738633", "d495299c-e636-4172-886b-1fe7a718b474", "cfdf705b-dffb-4e51-a3a6-87587ae19fa4", "6ceb0824-48d2-44d6-881d-e5ff735a12da", "0c8d6641-5ef8-4886-9a52-bb2245bc2e80", "a2abf37e-8d49-4408-8fc9-e2b5dae0bab3", "77e333e8-c6b5-4ec9-8a7e-6a0fadeb354e"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "account or collect cash payment for the amount of the wire transfer and all applicable fees. I understand the bank handles wire", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.015586822293698788, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "a2348c1e-ff96-4e5a-a932-c2fc0b7e0e49", "Relationships": [{"Type": "CHILD", "Ids": ["17615c7c-fcba-45ef-b6da-3b0913c42694", "73cd659a-e0ec-4137-9790-9601d6e5f4b9", "9512b080-46ce-4123-b7ef-617f540ad558", "8e48df75-6252-46c6-ae55-ee74ec640a75", "208a9cb5-2018-4a96-a1b0-6bf11155b385", "92d3997f-e9c4-45bc-a9de-b58da563f8f9", "95533e36-1993-4ed3-87cf-8dc83bf9517a", "********-d6e0-4307-b573-a434a1be2c69", "bf6a162b-c02b-4808-81a8-9dcb32462316", "6aad7d34-f303-4931-b084-55a8c1108d0f", "5179d25d-0f38-45e2-a3d6-b3ab3e10a6a3", "d2989d45-3eb5-4725-af17-27c41a7f2f3d", "cf7a51f9-a347-4525-a214-5fa7505b736b", "5f509a66-7a1a-44b6-8e82-7407b0f729b3", "bc2f2112-3ae3-4e91-8061-ec331d778618", "287e17df-7a94-479d-bbd6-93dffcb1b035", "3be0200e-2884-4b29-80ed-31410e510d5b", "bf4b8b3d-720f-4d24-87ea-39b6d73014db", "e5155130-f2a4-4dc8-812b-8305d51f7fff", "893e12a3-6321-4eb1-a62d-3f5879bc0619", "4d1f45fb-2bab-4eb4-a74c-cd1f736832a8", "4e5c7ec5-6cd7-4500-9bce-b5a7f0827d7c"]}]}, {"BlockType": "LINE", "Confidence": 99.*************, "Text": "transfer requests expeditiously, but that the bank cannot guarantee this will be completed in any specific time period.", "Geometry": {"BoundingBox": {"Width": 0.***************, "Height": 0.017168452963232994, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "080b92fc-ae5b-48d8-9e50-65b92abe32c7", "Relationships": [{"Type": "CHILD", "Ids": ["891d9cf4-5270-4e21-a649-b6220b13d90b", "95f6637e-91e4-400a-a9fc-817feffb5e8b", "f54e1bb4-e66a-436d-b2ec-aac17c577ea9", "cc229cdd-3b1d-4c63-ae41-53f414ba0ac3", "38bce648-8a56-4805-971f-3eb17869ec48", "be00bc07-50f5-4b0a-be50-d0a68eb8f9ce", "646b2aad-29cc-4028-8eab-178c26ffabb7", "fe54728c-7a6e-4f0b-81f6-b3eb0ec8ce0c", "329eb6ee-bf10-47a8-8775-d00c7c8bae32", "cf81e0fb-11bf-4f6e-b663-1283c584add9", "f8a278b1-e4ee-4f66-ab2f-7f7996b2a07e", "a970b50e-3701-4ba7-b25e-27d996800d35", "9b2e6825-b800-4f84-84cd-4099a6ea63fb", "5902830f-dda5-47ca-a2f7-8a32a4e4b29b", "6e3be9cf-b616-4ae7-a67e-a641a8de13b6", "1892e0d8-3b48-49bf-9423-d903becd50f0", "dc1466e8-20a1-4d04-b326-8d2a7fa4106e", "3144a588-8e59-4514-9eca-a2892bb95bd9"]}]}, {"BlockType": "LINE", "Confidence": 99.89530181884766, "Text": "CUSTOMER SIGNATURE (required)", "Geometry": {"BoundingBox": {"Width": 0.280990868806839, "Height": 0.014171212911605835, "Left": 0.09833689779043198, "Top": 0.5022342205047607}, "Polygon": [{"X": 0.09834256023168564, "Y": 0.5022342205047607}, {"X": 0.37932777404785156, "Y": 0.5025476217269897}, {"X": 0.37932196259498596, "Y": 0.5164054036140442}, {"X": 0.09833689779043198, "Y": 0.5160920023918152}]}, "Id": "6581cfbc-2c09-4fa5-98a1-6afec2f53470", "Relationships": [{"Type": "CHILD", "Ids": ["b0b78538-610a-4b43-a5e6-3705c9f04da1", "e121f059-ec42-47a4-9f71-936f7e2534f4", "37fccdd9-cd02-4e20-a02e-d453ba2ee605"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Phone", "Geometry": {"BoundingBox": {"Width": 0.04249117150902748, "Height": 0.009108005091547966, "Left": 0.4642619490623474, "Top": 0.5063906311988831}, "Polygon": [{"X": 0.46426576375961304, "Y": 0.5063906311988831}, {"X": 0.5067530870437622, "Y": 0.5064380168914795}, {"X": 0.5067492723464966, "Y": 0.5154986381530762}, {"X": 0.4642619490623474, "Y": 0.5154512524604797}]}, "Id": "97bcc81a-23ca-41b4-9253-79035386be14", "Relationships": [{"Type": "CHILD", "Ids": ["6873e06e-1383-406f-aa0c-d69dadf400c9"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.03161102533340454, "Height": 0.008885793387889862, "Left": 0.6465009450912476, "Top": 0.5085980296134949}, "Polygon": [{"X": 0.6465047597885132, "Y": 0.5085980296134949}, {"X": 0.6781119704246521, "Y": 0.5086332559585571}, {"X": 0.6781081557273865, "Y": 0.5174838304519653}, {"X": 0.6465009450912476, "Y": 0.5174485445022583}]}, "Id": "f480e16f-21b5-43ad-b7f9-e99ecaaee333", "Relationships": [{"Type": "CHILD", "Ids": ["8f686daa-14a0-4ca8-ad0e-fea0b61c965e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.03600800037384033, "Height": 0.009145515039563179, "Left": 0.7675648331642151, "Top": 0.5096938014030457}, "Polygon": [{"X": 0.7675687670707703, "Y": 0.5096938014030457}, {"X": 0.8035728335380554, "Y": 0.5097339153289795}, {"X": 0.8035688400268555, "Y": 0.5188392996788025}, {"X": 0.7675648331642151, "Y": 0.5187991261482239}]}, "Id": "4a917911-781a-4d4d-964e-771a02cdb93b", "Relationships": [{"Type": "CHILD", "Ids": ["11de57a0-7b4a-404e-bc20-6dfcbb636f5e"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "Callback", "Geometry": {"BoundingBox": {"Width": 0.058271802961826324, "Height": 0.008705748245120049, "Left": 0.4647212624549866, "Top": 0.5617637038230896}, "Polygon": [{"X": 0.46472489833831787, "Y": 0.5617637038230896}, {"X": 0.5229930877685547, "Y": 0.5618286728858948}, {"X": 0.5229893922805786, "Y": 0.5704694390296936}, {"X": 0.4647212624549866, "Y": 0.5704044103622437}]}, "Id": "f08474db-e25f-416d-a91e-4d1f0f79d1da", "Relationships": [{"Type": "CHILD", "Ids": ["f90a8f52-fa3b-4d05-ad37-0d8aa784f689"]}]}, {"BlockType": "LINE", "Confidence": 99.70948791503906, "Text": "Phone # called:", "Geometry": {"BoundingBox": {"Width": 0.09350452572107315, "Height": 0.009463321417570114, "Left": 0.0972340852022171, "Top": 0.5697985291481018}, "Polygon": [{"X": 0.09723791480064392, "Y": 0.5697985291481018}, {"X": 0.19073860347270966, "Y": 0.5699028372764587}, {"X": 0.19073474407196045, "Y": 0.5792618989944458}, {"X": 0.0972340852022171, "Y": 0.5791575908660889}]}, "Id": "5c8e4274-976f-4320-aaf1-65ad0ba0ac09", "Relationships": [{"Type": "CHILD", "Ids": ["47cf69ee-1a9d-477a-ac14-5b5823bb4149", "96c4219b-d8ec-47be-b219-b08099ae0f8e", "4664b200-7981-481a-a201-37990d0c9a41"]}]}, {"BlockType": "LINE", "Confidence": 99.93761444091797, "Text": "Time of call:", "Geometry": {"BoundingBox": {"Width": 0.07898540049791336, "Height": 0.009105440229177475, "Left": 0.09645872563123703, "Top": 0.5821663737297058}, "Polygon": [{"X": 0.09646241366863251, "Y": 0.5821663737297058}, {"X": 0.1754441261291504, "Y": 0.5822544693946838}, {"X": 0.17544041574001312, "Y": 0.5912717580795288}, {"X": 0.09645872563123703, "Y": 0.5911836624145508}]}, "Id": "606e4a49-bf1b-4b83-9a81-8a9b51f75adc", "Relationships": [{"Type": "CHILD", "Ids": ["cec08cb6-4194-4f67-abfd-07c1f1da0799", "1e9ff490-483b-463f-b5ed-557296d9efd5", "0e439505-4822-4d57-8a97-3e477d99ebca"]}]}, {"BlockType": "LINE", "Confidence": 99.59907531738281, "Text": "Source of #:", "Geometry": {"BoundingBox": {"Width": 0.07374131679534912, "Height": 0.009002191945910454, "Left": 0.5026664137840271, "Top": 0.5865266919136047}, "Polygon": [{"X": 0.5026702284812927, "Y": 0.5865266919136047}, {"X": 0.5764077305793762, "Y": 0.5866089463233948}, {"X": 0.5764039158821106, "Y": 0.5955289006233215}, {"X": 0.5026664137840271, "Y": 0.5954465866088867}]}, "Id": "8677559b-d28a-4c36-9c43-761c57d4126d", "Relationships": [{"Type": "CHILD", "Ids": ["3240a76d-a0c9-40dd-8ac4-553f7e6ea2ea", "99dfd946-b4af-410a-ac49-ae01d2c05e70", "075ca6ea-3a00-45f2-91e2-ef7956def84e"]}]}, {"BlockType": "LINE", "Confidence": 99.81166076660156, "Text": "Verified with:", "Geometry": {"BoundingBox": {"Width": 0.08563270419836044, "Height": 0.008803128264844418, "Left": 0.09690220654010773, "Top": 0.5942326784133911}, "Polygon": [{"X": 0.09690576791763306, "Y": 0.5942326784133911}, {"X": 0.18253491818904877, "Y": 0.5943282246589661}, {"X": 0.18253132700920105, "Y": 0.6030358076095581}, {"X": 0.09690220654010773, "Y": 0.6029402613639832}]}, "Id": "1368597a-099c-4680-bf3c-bb041488053c", "Relationships": [{"Type": "CHILD", "Ids": ["e86828c9-5a3e-45fc-be68-204bf355fa21", "f9d4a81b-f3e0-4da3-b835-eb380b8a8dd5"]}]}, {"BlockType": "LINE", "Confidence": 99.904541015625, "Text": "Callback made by:", "Geometry": {"BoundingBox": {"Width": 0.11296602338552475, "Height": 0.011112294159829617, "Left": 0.5023662447929382, "Top": 0.5987325310707092}, "Polygon": [{"X": 0.5023708939552307, "Y": 0.5987325310707092}, {"X": 0.6153322458267212, "Y": 0.5988585352897644}, {"X": 0.6153275370597839, "Y": 0.6098448038101196}, {"X": 0.5023662447929382, "Y": 0.6097187995910645}]}, "Id": "7ac5cced-f752-4a80-aa22-9a5e88ca7ac5", "Relationships": [{"Type": "CHILD", "Ids": ["530bba0a-7c31-425c-af68-b0f85f5c5172", "5b81391a-6324-41cb-96d1-3bbe1ff0ae45", "c4d4068f-fb72-494d-9418-8c634fcb1005"]}]}, {"BlockType": "LINE", "Confidence": 99.79320526123047, "Text": "High Risk Destination Wire callback:", "Geometry": {"BoundingBox": {"Width": 0.2285979837179184, "Height": 0.010944920592010021, "Left": 0.09642978012561798, "Top": 0.6185111403465271}, "Polygon": [{"X": 0.09643415361642838, "Y": 0.6185111403465271}, {"X": 0.3250277638435364, "Y": 0.618766188621521}, {"X": 0.3250232934951782, "Y": 0.6294560432434082}, {"X": 0.09642978012561798, "Y": 0.6292009949684143}]}, "Id": "07bdb5e5-be85-4467-b523-2e7c77bdddb1", "Relationships": [{"Type": "CHILD", "Ids": ["f5c9ca8f-51e4-4d8a-be42-10dc4cd3c7c3", "d2c679a4-a675-4700-af81-4af2b3e6716a", "14ebd181-3c71-460e-b7ec-f81e6c2c6c0d", "6c600335-6a75-4ad6-8a15-2bb2a33599d7", "1ed6d89b-1582-4051-8708-8a0abebd1536"]}]}, {"BlockType": "LINE", "Confidence": 99.92433166503906, "Text": "PURPOSE OF WIRE:", "Geometry": {"BoundingBox": {"Width": 0.13699741661548615, "Height": 0.009984403848648071, "Left": 0.09604628384113312, "Top": 0.6432508230209351}, "Polygon": [{"X": 0.09605030715465546, "Y": 0.6432508230209351}, {"X": 0.23304370045661926, "Y": 0.6434037089347839}, {"X": 0.23303963243961334, "Y": 0.6532352566719055}, {"X": 0.09604628384113312, "Y": 0.6530823707580566}]}, "Id": "6ef199b9-a2d5-469e-a722-631aaf11a7af", "Relationships": [{"Type": "CHILD", "Ids": ["b1511d0a-e989-4eeb-9801-ebea846b827a", "23bc1ebc-15f1-47e1-a79d-ac22fb2164e4", "27dc72c7-2c56-458d-ac6c-6f8c3131a6a1"]}]}, {"BlockType": "LINE", "Confidence": 98.5508041381836, "Text": "If funds are uncollected,", "Geometry": {"BoundingBox": {"Width": 0.1471872478723526, "Height": 0.010667117312550545, "Left": 0.**************, "Top": 0.6994679570198059}, "Polygon": [{"X": 0.7044152617454529, "Y": 0.6994679570198059}, {"X": 0.8515979*********, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.**************, "Y": 0.****************}]}, "Id": "4261952e-0e8e-4688-8c9e-d320f4165cca", "Relationships": [{"Type": "CHILD", "Ids": ["e90209b3-1757-4ad6-9fdf-87597fa83808", "7ec3cef5-6732-4cd4-b171-711ee2752cdc", "c4ff1919-5849-44db-b75a-2d985e2dd605", "4dd55831-5134-4251-bead-4aa7974ddfea"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "I certify that funds are available in the Customer's account and submit this transfer request.", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.016010833904147148, "Left": 0.***************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.***************, "Y": 0.****************}]}, "Id": "e5be6c31-e279-49ce-9971-9bf52e9335d7", "Relationships": [{"Type": "CHILD", "Ids": ["e39b9a19-4d4c-492a-9129-819562dbf701", "d6029c30-31a4-4a43-aed8-af97a2c0ae30", "22dc04ab-1190-4791-a96a-8cfdc47ddb24", "ae2e2b8d-0943-410f-aa0d-49cb62af94c6", "6602802b-ef55-49b9-b324-2e8c38f19dd8", "d4c46285-f875-4502-9c03-e42ba671eff0", "1c51e32a-6316-4f93-a33e-ee7a50fec75f", "15d26e76-eb05-4c56-b03b-b8478160f06d", "51b7cac8-ca07-4a9c-94da-9fc6534a7669", "006aa11d-e294-46d1-a0b2-b252bcaf7eb8", "fa41ae0b-3eab-4f43-8702-321f954534ab", "3bda2be4-6dcf-4263-80ed-51ffa10962ae", "a11c1784-cfad-44c1-ad74-ab2d50eb8124", "9e3452f9-7900-4c5e-a385-48412a2c54b8", "3c9a8b1d-ecba-4087-8c04-e0f90525bf82"]}]}, {"BlockType": "LINE", "Confidence": 99.97369384765625, "Text": "Officer approval required", "Geometry": {"BoundingBox": {"Width": 0.15541322529315948, "Height": 0.011401812545955181, "Left": 0.7039339542388916, "Top": 0.7120754718780518}, "Polygon": [{"X": 0.7039387822151184, "Y": 0.7120754718780518}, {"X": 0.8593471646308899, "Y": 0.7122489213943481}, {"X": 0.8593422174453735, "Y": 0.723477303981781}, {"X": 0.7039339542388916, "Y": 0.7233038544654846}]}, "Id": "f4d2947d-9ac1-4664-9afc-1822cf8e00d0", "Relationships": [{"Type": "CHILD", "Ids": ["6c0f32d7-abc2-4267-8513-55039d6ec632", "9c3bcb5a-dd8b-402c-845e-b4ef6578ac45", "4521f972-845a-459c-8331-cf23c04ed219"]}]}, {"BlockType": "LINE", "Confidence": 99.93383026123047, "Text": "Employee Completing Request", "Geometry": {"BoundingBox": {"Width": 0.19143544137477875, "Height": 0.012344323098659515, "Left": 0.09421100467443466, "Top": 0.7422691583633423}, "Polygon": [{"X": 0.09421596676111221, "Y": 0.7422691583633423}, {"X": 0.2856464385986328, "Y": 0.7424827814102173}, {"X": 0.2856414020061493, "Y": 0.75461345911026}, {"X": 0.09421100467443466, "Y": 0.7543997764587402}]}, "Id": "19e606a3-0110-4d35-95bc-e886a7a2408f", "Relationships": [{"Type": "CHILD", "Ids": ["e6459c00-a289-494d-be21-f713936545be", "b80eb485-f03a-4f37-aaa1-7d0c8c2c0392", "bbaa84db-ba05-4d51-84ff-648d118eea22"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Phone", "Geometry": {"BoundingBox": {"Width": 0.03840826079249382, "Height": 0.008556368760764599, "Left": 0.40328678488731384, "Top": 0.7456466555595398}, "Polygon": [{"X": 0.40329039096832275, "Y": 0.7456466555595398}, {"X": 0.44169506430625916, "Y": 0.7456895112991333}, {"X": 0.44169145822525024, "Y": 0.7542030215263367}, {"X": 0.40328678488731384, "Y": 0.7541601657867432}]}, "Id": "6a06da94-357c-4918-a10b-0450d2bc14cd", "Relationships": [{"Type": "CHILD", "Ids": ["d42d713d-4764-4238-be0a-beb6e59fa934"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.028867069631814957, "Height": 0.008462034165859222, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "ec03fbd5-dda2-4612-bd71-0f91f8682d56", "Relationships": [{"Type": "CHILD", "Ids": ["cf67c079-826c-4ae5-aa23-4c4ecb7b40ce"]}]}, {"BlockType": "LINE", "Confidence": 99.********, "Text": "Authorized Bank", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009229036048054695, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "73154f62-f96a-4e04-ae25-710ab34f7a35", "Relationships": [{"Type": "CHILD", "Ids": ["5fd0ddcd-4602-4fa3-89fa-3b34eb6b614e", "c231e878-6df4-41aa-a0bf-1d706cecb2e3"]}]}, {"BlockType": "LINE", "Confidence": 87.**************, "Text": "2ⁿᵈ Authorized Bank", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.010875304229557514, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "91ea1e7b-2090-4b0e-8988-cf03eaba1d99", "Relationships": [{"Type": "CHILD", "Ids": ["ab3b32ad-ecdd-4a09-a953-8af2943d0d60", "a166d250-1681-4f2b-9f62-6247daf76842", "2a887e31-98e8-4275-8794-27f41c6d3d7a"]}]}, {"BlockType": "LINE", "Confidence": 99.**********, "Text": "Signature Required", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.7903963923454285}]}, "Id": "9e9c97ab-7a0d-46fa-8d26-67876dd6f6ca", "Relationships": [{"Type": "CHILD", "Ids": ["44b3ec42-9f8b-433b-9035-70f8a56ed9f3", "db909e24-fca3-4d58-b03d-c6b96069ed58"]}]}, {"BlockType": "LINE", "Confidence": 99.90128326416016, "Text": "Signature If Required:", "Geometry": {"BoundingBox": {"Width": 0.1407478302717209, "Height": 0.011390780098736286, "Left": 0.4679658114910126, "Top": 0.7830936312675476}, "Polygon": [{"X": 0.4679705500602722, "Y": 0.7830936312675476}, {"X": 0.6087136268615723, "Y": 0.7832507491111755}, {"X": 0.6087087988853455, "Y": 0.7944843769073486}, {"X": 0.4679658114910126, "Y": 0.7943272590637207}]}, "Id": "abb16d22-e25a-43d0-baa4-e1a8dc688365", "Relationships": [{"Type": "CHILD", "Ids": ["842224fd-abcc-4618-9bed-900168cda36f", "072a7aad-3abb-4361-bfa3-386cbe918a64", "ee90ef87-7dc1-4c03-ad9a-380656c11d60"]}]}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "HTLF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009293156675994396, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "20c7e8ec-2160-4340-9f9a-9f9a19b82e30"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009317769668996334, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.035003628581762314}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "084ff59b-c996-46be-a009-3f792ce9fbab"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Outgoing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "f3f5e663-82e5-4101-a175-3394f8cac527"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05148332938551903, "Height": 0.012634639628231525, "Left": 0.4359470307826996, "Top": 0.05449533089995384}, "Polygon": [{"X": 0.4359523355960846, "Y": 0.05449533089995384}, {"X": 0.4874303638935089, "Y": 0.05455268174409866}, {"X": 0.4874250292778015, "Y": 0.06712996959686279}, {"X": 0.4359470307826996, "Y": 0.06707261502742767}]}, "Id": "f61e5f62-22c2-4285-b78e-4b7bedfe4f86"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Transfer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08957627415657043, "Height": 0.013089423067867756, "Left": 0.4928927719593048, "Top": 0.055252183228731155}, "Polygon": [{"X": 0.49289828538894653, "Y": 0.055252183228731155}, {"X": 0.5824690461158752, "Y": 0.055351972579956055}, {"X": 0.5824635028839111, "Y": 0.06834160536527634}, {"X": 0.4928927719593048, "Y": 0.06824181228876114}]}, "Id": "5cacddea-c3ac-487f-b6cf-833decbae342"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Request", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08099163323640823, "Height": 0.015012404881417751, "Left": 0.5882366895675659, "Top": 0.05663599073886871}, "Polygon": [{"X": 0.5882430672645569, "Y": 0.05663599073886871}, {"X": 0.6692283153533936, "Y": 0.05672621354460716}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.5882366895675659, "Y": 0.07155817002058029}]}, "Id": "12720b0d-1d47-4fea-8809-cb8b441de499"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038640886545181274, "Height": 0.009089394472539425, "Left": 0.10548257827758789, "Top": 0.08546706289052963}, "Polygon": [{"X": 0.10548628121614456, "Y": 0.08546706289052963}, {"X": 0.14412346482276917, "Y": 0.08551011234521866}, {"X": 0.1441197544336319, "Y": 0.09455645829439163}, {"X": 0.10548257827758789, "Y": 0.0945134088397026}]}, "Id": "402f3d78-ff63-42e7-8d4f-f6a23ee5bc0d"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "02/07/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07682006806135178, "Height": 0.009592822752892971, "Left": 0.153550922870636, "Top": 0.08607235550880432}, "Polygon": [{"X": 0.15355482697486877, "Y": 0.08607235550880432}, {"X": 0.23037098348140717, "Y": 0.08615794032812119}, {"X": 0.230367049574852, "Y": 0.09566517919301987}, {"X": 0.153550922870636, "Y": 0.0955795869231224}]}, "Id": "78f286ac-24ff-42ba-a3e9-ed0d7fbcf31a"}, {"BlockType": "WORD", "Confidence": 98.9453125, "Text": "11:12:43", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06027071923017502, "Height": 0.009195682592689991, "Left": 0.23633307218551636, "Top": 0.08715420961380005}, "Polygon": [{"X": 0.2363368570804596, "Y": 0.08715420961380005}, {"X": 0.296603798866272, "Y": 0.08722136169672012}, {"X": 0.29659998416900635, "Y": 0.09634989500045776}, {"X": 0.23633307218551636, "Y": 0.09628274291753769}]}, "Id": "9f29bfe2-f803-4a25-b212-a3f543e49c21"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03661381080746651, "Height": 0.00912721361964941, "Left": 0.1053168922662735, "Top": 0.09994842857122421}, "Polygon": [{"X": 0.10532061010599136, "Y": 0.09994842857122421}, {"X": 0.1419306993484497, "Y": 0.09998922049999237}, {"X": 0.14192697405815125, "Y": 0.1090756431221962}, {"X": 0.1053168922662735, "Y": 0.10903485119342804}]}, "Id": "8e13d51d-835a-4b8a-9a3e-254bac3d574c"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Transfer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06430374085903168, "Height": 0.00968189723789692, "Left": 0.14596043527126312, "Top": 0.10045415908098221}, "Polygon": [{"X": 0.1459643840789795, "Y": 0.10045415908098221}, {"X": 0.2102641761302948, "Y": 0.10052580386400223}, {"X": 0.21026021242141724, "Y": 0.11013605445623398}, {"X": 0.14596043527126312, "Y": 0.11006440967321396}]}, "Id": "017dbf4a-ea0d-4495-8478-391bb8964113"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Message", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06091860681772232, "Height": 0.012237967923283577, "Left": 0.21450354158878326, "Top": 0.10131267458200455}, "Polygon": [{"X": 0.21450857818126678, "Y": 0.10131267458200455}, {"X": 0.2754221558570862, "Y": 0.10138054192066193}, {"X": 0.2754170894622803, "Y": 0.11355064064264297}, {"X": 0.21450354158878326, "Y": 0.113482765853405}]}, "Id": "a0aadf7c-2f50-4b85-8a67-71c616f66672"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Reference", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07240341603755951, "Height": 0.009923449717462063, "Left": 0.27983179688453674, "Top": 0.10205981135368347}, "Polygon": [{"X": 0.27983590960502625, "Y": 0.10205981135368347}, {"X": 0.35223522782325745, "Y": 0.10214048624038696}, {"X": 0.35223108530044556, "Y": 0.11198326200246811}, {"X": 0.27983179688453674, "Y": 0.11190259456634521}]}, "Id": "a8df33bd-9f9b-4e2f-b46b-4e1ee6ade68b"}, {"BlockType": "WORD", "Confidence": 99.24724578857422, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06445739418268204, "Height": 0.00985044427216053, "Left": 0.3563441336154938, "Top": 0.10300400853157043}, "Polygon": [{"X": 0.3563482463359833, "Y": 0.10300400853157043}, {"X": 0.4208015501499176, "Y": 0.10307582467794418}, {"X": 0.4207974076271057, "Y": 0.11285445839166641}, {"X": 0.3563441336154938, "Y": 0.11278264224529266}]}, "Id": "0e8cef70-d186-4833-940b-c8a936f86f1c"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "2025038000120600", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14914968609809875, "Height": 0.012380646541714668, "Left": 0.47030505537986755, "Top": 0.10340685397386551}, "Polygon": [{"X": 0.4703102111816406, "Y": 0.10340685397386551}, {"X": 0.6194547414779663, "Y": 0.1035730317234993}, {"X": 0.6194494962692261, "Y": 0.11578750610351562}, {"X": 0.47030505537986755, "Y": 0.11562132090330124}]}, "Id": "4a2f2e5a-2bc9-4038-bd93-b42e5df84c2b"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "WIRE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042793694883584976, "Height": 0.008553672581911087, "Left": 0.12308549135923386, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.16587918996810913, "Y": 0.12942178547382355}, {"X": 0.16587568819522858, "Y": 0.13792777061462402}, {"X": 0.12308549135923386, "Y": 0.1378800868988037}]}, "Id": "72920fac-e1d5-47b7-b231-42ea446bdd62"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026158051565289497, "Height": 0.008677496574819088, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "f1b13618-792f-408e-9453-570b3bacd404"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032519273459911346, "Height": 0.008746602572500706, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "a4b830fb-11f8-4359-ab07-9c999856f439"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "ABA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "1a01e384-fc42-47c1-ba47-6b87d53a8a9c"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02252073958516121, "Height": 0.008508138358592987, "Left": 0.17613303661346436, "Top": 0.14259643852710724}, "Polygon": [{"X": 0.1761365383863449, "Y": 0.14259643852710724}, {"X": 0.19865377247333527, "Y": 0.14262153208255768}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.17613303661346436, "Y": 0.1510794758796692}]}, "Id": "1a52dba7-9e91-432f-8136-f8925970ce67"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "*********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008926702663302422, "Left": 0.**************, "Top": 0.***************}, "Polygon": [{"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.**************, "Y": 0.*****************}]}, "Id": "e4c765e2-e5a3-43dc-99f6-4dded0038e43"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032422710210084915, "Height": 0.008513903245329857, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "4c24e7b9-5ef5-44d3-b293-d9eda7058a62"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.008492338471114635, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "dfccea4c-582a-46c1-bce3-e610b69468f4"}, {"BlockType": "WORD", "Confidence": 99.60857391357422, "Text": "BK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02099228836596012, "Height": 0.008084723725914955, "Left": 0.28723543882369995, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.3082277178764343, "Y": 0.15597842633724213}, {"X": 0.3082243800163269, "Y": 0.1640397608280182}, {"X": 0.28723543882369995, "Y": 0.16401636600494385}]}, "Id": "d4d96573-7676-4cd6-8051-7b36788b2a7f"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "AMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04424399510025978, "Height": 0.00842062383890152, "Left": 0.311872273683548, "Top": 0.15629099309444427}, "Polygon": [{"X": 0.3118757903575897, "Y": 0.15629099309444427}, {"X": 0.35611629486083984, "Y": 0.15634028613567352}, {"X": 0.3561127781867981, "Y": 0.1647116094827652}, {"X": 0.311872273683548, "Y": 0.16466230154037476}]}, "Id": "564c206f-442b-4390-8d0b-615cdda4b824"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "NYC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0325576588511467, "Height": 0.008398809470236301, "Left": 0.3596372902393341, "Top": 0.1567343920469284}, "Polygon": [{"X": 0.35964077711105347, "Y": 0.1567343920469284}, {"X": 0.3921949565410614, "Y": 0.15677067637443542}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.3596372902393341, "Y": 0.1650969237089157}]}, "Id": "e36fe0ec-2f0f-4c37-a1be-0331baa28453"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Amount:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053944338113069534, "Height": 0.00878367479890585, "Left": 0.10365726798772812, "Top": 0.16590599715709686}, "Polygon": [{"X": 0.10366084426641464, "Y": 0.16590599715709686}, {"X": 0.15760160982608795, "Y": 0.16596610844135284}, {"X": 0.15759801864624023, "Y": 0.1746896654367447}, {"X": 0.10365726798772812, "Y": 0.1746295541524887}]}, "Id": "f2f762ef-0ffe-4ffb-891e-af3c9d7a4962"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "750.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04192624241113663, "Height": 0.008782812394201756, "Left": 0.28693363070487976, "Top": 0.16809536516666412}, "Polygon": [{"X": 0.28693726658821106, "Y": 0.16809536516666412}, {"X": 0.3288598954677582, "Y": 0.16814208030700684}, {"X": 0.3288562297821045, "Y": 0.1768781840801239}, {"X": 0.28693363070487976, "Y": 0.17683145403862}]}, "Id": "6375833e-e7d5-459e-8ccd-34fec17e38f8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Special", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044831208884716034, "Height": 0.010406317189335823, "Left": 0.10364838689565659, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.14847959578037262, "Y": 0.1784081906080246}, {"X": 0.14847533404827118, "Y": 0.1887645572423935}, {"X": 0.10364838689565659, "Y": 0.18871459364891052}]}, "Id": "8034b0fc-e215-49e0-b35f-45d4355ff13e"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Instructions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07192172110080719, "Height": 0.00920976884663105, "Left": 0.15274496376514435, "Top": 0.1789950728416443}, "Polygon": [{"X": 0.1527487188577652, "Y": 0.1789950728416443}, {"X": 0.22466668486595154, "Y": 0.1790752112865448}, {"X": 0.2246628999710083, "Y": 0.1882048398256302}, {"X": 0.15274496376514435, "Y": 0.18812468647956848}]}, "Id": "ec763e2a-2505-4bfd-a3a4-5045e2fb1fc4"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "(BBI):", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038784995675086975, "Height": 0.010337789542973042, "Left": 0.2287168651819229, "Top": 0.17994309961795807}, "Polygon": [{"X": 0.22872112691402435, "Y": 0.17994309961795807}, {"X": 0.2675018608570099, "Y": 0.17998631298542023}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.2287168651819229, "Y": 0.19023767113685608}]}, "Id": "8ca87f49-01c6-4f07-b1a9-063957e3680b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "BENEFICIARY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009369582869112492, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "510943cf-6f2a-4e6c-9c7f-1462e3b3a9f2"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "BANK:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008710596710443497, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "d5eac826-370a-4a82-8d7b-0eeefd5d34f6"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032582029700279236, "Height": 0.008220180869102478, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "c87e9568-9c74-4903-b2b2-05838a55d8c4"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "ABA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032324034720659256, "Height": 0.008062789216637611, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.***************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "7cb7d58b-1131-4ebd-9e0e-1dad870e4b69"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008400177583098412, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "8b2519df-9d2e-41c7-b1c7-eca8dbcfd6e6"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032711006700992584, "Height": 0.008568049408495426, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "1b1b6321-4570-4764-927b-edcd8a029623"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008523483760654926, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "175d4301-9555-4f1a-bc07-2992080c7aab"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "CREDIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0585370697081089, "Height": 0.008735712617635727, "Left": 0.12140129506587982, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.17993836104869843, "Y": 0.25281938910484314}, {"X": 0.1799347996711731, "Y": 0.2614898681640625}, {"X": 0.12140129506587982, "Y": 0.26142463088035583}]}, "Id": "5ddfd849-d074-4ac9-84bc-750469c98add"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "TO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02635074220597744, "Height": 0.008713819086551666, "Left": 0.1843125969171524, "Top": 0.2533889710903168}, "Polygon": [{"X": 0.18431618809700012, "Y": 0.2533889710903168}, {"X": 0.2106633484363556, "Y": 0.25341835618019104}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.1843125969171524, "Y": 0.26207342743873596}]}, "Id": "d393eaad-ae20-44ed-b226-4d95ea2b0b31"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04029395058751106, "Height": 0.00865392480045557, "Left": 0.10172683000564575, "Top": 0.26473817229270935}, "Polygon": [{"X": 0.1017303466796875, "Y": 0.26473817229270935}, {"X": 0.14202077686786652, "Y": 0.2647830843925476}, {"X": 0.14201724529266357, "Y": 0.27339211106300354}, {"X": 0.10172683000564575, "Y": 0.2733471989631653}]}, "Id": "bbd6823a-b33b-4440-99e8-e284fde6a1e0"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "WOODLANDS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09777306020259857, "Height": 0.009220272302627563, "Left": 0.28522205352783203, "Top": 0.26698753237724304}, "Polygon": [{"X": 0.28522586822509766, "Y": 0.26698753237724304}, {"X": 0.3829951286315918, "Y": 0.26709651947021484}, {"X": 0.38299131393432617, "Y": 0.2762078046798706}, {"X": 0.28522205352783203, "Y": 0.2760988473892212}]}, "Id": "8e30396b-32d9-46e4-bd67-f4a8f8870a9a"}, {"BlockType": "WORD", "Confidence": 99.5752944946289, "Text": "TRANSPORT,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0929509624838829, "Height": 0.010863345116376877, "Left": 0.38679978251457214, "Top": 0.2679862380027771}, "Polygon": [{"X": 0.3868043124675751, "Y": 0.2679862380027771}, {"X": 0.47975075244903564, "Y": 0.2680898606777191}, {"X": 0.4797461926937103, "Y": 0.27884960174560547}, {"X": 0.38679978251457214, "Y": 0.27874597907066345}]}, "Id": "31654d25-d5b9-4b46-9b60-02db32d3c6a6"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029061676934361458, "Height": 0.008757504634559155, "Left": 0.4835040867328644, "Top": 0.2688838243484497}, "Polygon": [{"X": 0.48350778222084045, "Y": 0.2688838243484497}, {"X": 0.5125657320022583, "Y": 0.26891621947288513}, {"X": 0.5125620365142822, "Y": 0.27764132618904114}, {"X": 0.4835040867328644, "Y": 0.2776089310646057}]}, "Id": "e9f16b76-783b-40f3-a959-376ac0cdbd9d"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05404909700155258, "Height": 0.008698641322553158, "Left": 0.10155691206455231, "Top": 0.27727097272872925}, "Polygon": [{"X": 0.10156045109033585, "Y": 0.27727097272872925}, {"X": 0.15560601651668549, "Y": 0.27733123302459717}, {"X": 0.15560245513916016, "Y": 0.285969614982605}, {"X": 0.10155691206455231, "Y": 0.28590938448905945}]}, "Id": "27b1c4bf-c625-4ca8-a713-92f2c315df3d"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "2700", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03075961396098137, "Height": 0.008325014263391495, "Left": 0.2847210168838501, "Top": 0.2794714868068695}, "Polygon": [{"X": 0.28472447395324707, "Y": 0.2794714868068695}, {"X": 0.31548064947128296, "Y": 0.27950578927993774}, {"X": 0.315477192401886, "Y": 0.2877964973449707}, {"X": 0.2847210168838501, "Y": 0.28776222467422485}]}, "Id": "85e539a2-3af2-4d82-9929-53cecd7a0171"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "TECHNOLOGY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10388093441724777, "Height": 0.009033500216901302, "Left": 0.3191066384315491, "Top": 0.27993521094322205}, "Polygon": [{"X": 0.31911036372184753, "Y": 0.27993521094322205}, {"X": 0.42298758029937744, "Y": 0.28005102276802063}, {"X": 0.4229838252067566, "Y": 0.2889687120914459}, {"X": 0.3191066384315491, "Y": 0.2888529300689697}]}, "Id": "2a48d9d6-7a82-495a-b50c-a1a8c601c03f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "FOREST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05708656460046768, "Height": 0.008785001002252102, "Left": 0.10171282291412354, "Top": 0.2893182337284088}, "Polygon": [{"X": 0.10171639174222946, "Y": 0.2893182337284088}, {"X": 0.15879939496517181, "Y": 0.28938186168670654}, {"X": 0.1587958037853241, "Y": 0.2981032431125641}, {"X": 0.10171282291412354, "Y": 0.29803961515426636}]}, "Id": "0d229859-e2be-4871-9461-11b3f0bba7ee"}, {"BlockType": "WORD", "Confidence": 98.9453125, "Text": "BLVD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040949929505586624, "Height": 0.008525261655449867, "Left": 0.16271625459194183, "Top": 0.29015615582466125}, "Polygon": [{"X": 0.1627197414636612, "Y": 0.29015615582466125}, {"X": 0.20366618037223816, "Y": 0.2902018129825592}, {"X": 0.2036626785993576, "Y": 0.2986814081668854}, {"X": 0.16271625459194183, "Y": 0.2986357808113098}]}, "Id": "7aa4c596-3899-47de-8b6e-0f7ca2bdc0b4"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "STE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026890089735388756, "Height": 0.008404159918427467, "Left": 0.20810139179229736, "Top": 0.2905678153038025}, "Polygon": [{"X": 0.20810484886169434, "Y": 0.2905678153038025}, {"X": 0.23499149084091187, "Y": 0.2905977666378021}, {"X": 0.2349880188703537, "Y": 0.2989719808101654}, {"X": 0.20810139179229736, "Y": 0.2989419996738434}]}, "Id": "7e6595ab-ff95-45fd-95a6-4465f81dcdbb"}, {"BlockType": "WORD", "Confidence": 99.609375, "Text": "250", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023067181929945946, "Height": 0.00808121357113123, "Left": 0.238674595952034, "Top": 0.2911503314971924}, "Polygon": [{"X": 0.23867793381214142, "Y": 0.2911503314971924}, {"X": 0.2617417871952057, "Y": 0.2911760210990906}, {"X": 0.26173844933509827, "Y": 0.29923152923583984}, {"X": 0.238674595952034, "Y": 0.29920580983161926}]}, "Id": "1832081c-8e72-4cbb-8e2d-d07f2a646bfb"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "THE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030124947428703308, "Height": 0.008549128659069538, "Left": 0.10104713588953018, "Top": 0.*****************}, "Polygon": [{"X": 0.10105062276124954, "Y": 0.*****************}, {"X": 0.1311720907688141, "Y": 0.30188310146331787}, {"X": 0.13116858899593353, "Y": 0.3103986382484436}, {"X": 0.10104713588953018, "Y": 0.31036508083343506}]}, "Id": "b87b907d-1d99-4d0c-b86d-b56589792ead"}, {"BlockType": "WORD", "Confidence": 98.05823516845703, "Text": "WOODLANDS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10115976631641388, "Height": 0.011071949265897274, "Left": 0.13548655807971954, "Top": 0.30219754576683044}, "Polygon": [{"X": 0.13549105823040009, "Y": 0.30219754576683044}, {"X": 0.23664632439613342, "Y": 0.3023103177547455}, {"X": 0.2366417795419693, "Y": 0.3132694959640503}, {"X": 0.13548655807971954, "Y": 0.31315672397613525}]}, "Id": "7ff4850b-1edb-469f-b943-39fb80b9a11a"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02051389031112194, "Height": 0.008406306616961956, "Left": 0.24066729843616486, "Top": 0.30346304178237915}, "Polygon": [{"X": 0.24067077040672302, "Y": 0.30346304178237915}, {"X": 0.26118117570877075, "Y": 0.3034859001636505}, {"X": 0.2611777186393738, "Y": 0.3118693232536316}, {"X": 0.24066729843616486, "Y": 0.31184646487236023}]}, "Id": "445de1e6-0a0c-4697-af30-2813fa9a0ae7"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "77381-3909", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008863039314746857, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "a757816c-dee0-40fe-bbcd-a5040f846c2b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Account", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052085962146520615, "Height": 0.008692216128110886, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "f29d253f-d2bd-473d-b274-9499ec4f8a94"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008619884960353374, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "c76e0d1f-9443-4dec-ab4b-d348e63e8ece"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009159157052636147, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.**************02}]}, "Id": "a7b4ee52-82e6-47e6-a7e4-145a9689c9f3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Special", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04475109279155731, "Height": 0.01051967404782772, "Left": 0.1010824665427208, "Top": 0.3386909067630768}, "Polygon": [{"X": 0.10108675062656403, "Y": 0.3386909067630768}, {"X": 0.1458335518836975, "Y": 0.3387407958507538}, {"X": 0.14582926034927368, "Y": 0.34921059012413025}, {"X": 0.1010824665427208, "Y": 0.34916070103645325}]}, "Id": "ca7fed3a-19fd-410d-83fe-d53c0020b428"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Instructions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07220950722694397, "Height": 0.009081649594008923, "Left": 0.1498894989490509, "Top": 0.3395041525363922}, "Polygon": [{"X": 0.14989320933818817, "Y": 0.3395041525363922}, {"X": 0.22209900617599487, "Y": 0.3395846486091614}, {"X": 0.2220952808856964, "Y": 0.3485858142375946}, {"X": 0.1498894989490509, "Y": 0.34850528836250305}]}, "Id": "b93f67bb-d520-4beb-a896-aecb7ada3bf8"}, {"BlockType": "WORD", "Confidence": 99.5224838256836, "Text": "(OBI):", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04009995982050896, "Height": 0.010482333600521088, "Left": 0.22580008208751678, "Top": 0.34016528725624084}, "Polygon": [{"X": 0.225804403424263, "Y": 0.34016528725624084}, {"X": 0.26590004563331604, "Y": 0.3402099907398224}, {"X": 0.26589569449424744, "Y": 0.35064759850502014}, {"X": 0.22580008208751678, "Y": 0.3506028950214386}]}, "Id": "723b8841-db90-4cf3-b38c-d365242a8282"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CHARGE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06670963019132614, "Height": 0.008909854106605053, "Left": 0.1194075345993042, "Top": 0.3635045886039734}, "Polygon": [{"X": 0.1194111555814743, "Y": 0.3635045886039734}, {"X": 0.18611715734004974, "Y": 0.3635789752006531}, {"X": 0.18611352145671844, "Y": 0.3724144697189331}, {"X": 0.1194075345993042, "Y": 0.3723400831222534}]}, "Id": "d8ebfc17-b33a-4f5d-a201-a51ca94d279c"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "TO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026191910728812218, "Height": 0.008656591176986694, "Left": 0.1899375021457672, "Top": 0.364358514547348}, "Polygon": [{"X": 0.18994106352329254, "Y": 0.364358514547348}, {"X": 0.21612940728664398, "Y": 0.36438772082328796}, {"X": 0.21612584590911865, "Y": 0.3730151057243347}, {"X": 0.1899375021457672, "Y": 0.3729858994483948}]}, "Id": "209bba7c-e6f1-4a6d-907c-6c024e1000a4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04015088453888893, "Height": 0.008593789301812649, "Left": 0.09991026669740677, "Top": 0.3756920099258423}, "Polygon": [{"X": 0.09991376101970673, "Y": 0.3756920099258423}, {"X": 0.140061154961586, "Y": 0.375736802816391}, {"X": 0.14005763828754425, "Y": 0.3842858076095581}, {"X": 0.09991026669740677, "Y": 0.3842410445213318}]}, "Id": "fa1f2198-8595-4366-9752-d6f2a64731c6"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "TEXAS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04919342324137688, "Height": 0.008965137414634228, "Left": 0.2829399108886719, "Top": 0.3776739537715912}, "Polygon": [{"X": 0.28294360637664795, "Y": 0.3776739537715912}, {"X": 0.33213332295417786, "Y": 0.37772881984710693}, {"X": 0.3321295976638794, "Y": 0.3866390883922577}, {"X": 0.2829399108886719, "Y": 0.38658425211906433}]}, "Id": "cd9f3783-f30e-4f12-8807-f0f5ce5d84ac"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "CONTRACT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08202576637268066, "Height": 0.009243417531251907, "Left": 0.3365127444267273, "Top": 0.37831395864486694}, "Polygon": [{"X": 0.3365165591239929, "Y": 0.37831395864486694}, {"X": 0.41853851079940796, "Y": 0.3784054219722748}, {"X": 0.41853463649749756, "Y": 0.38755738735198975}, {"X": 0.3365127444267273, "Y": 0.3874659240245819}]}, "Id": "89ef3fb7-5d7a-4f69-b57a-965d2f045655"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "ROUSTABOUT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1023705005645752, "Height": 0.00911008007824421, "Left": 0.4222847521305084, "Top": 0.37953558564186096}, "Polygon": [{"X": 0.42228853702545166, "Y": 0.37953558564186096}, {"X": 0.524655282497406, "Y": 0.37964972853660583}, {"X": 0.5246514081954956, "Y": 0.3886456787586212}, {"X": 0.4222847521305084, "Y": 0.38853153586387634}]}, "Id": "2ff6626e-5057-4682-a1a0-91f2dbf657b2"}, {"BlockType": "WORD", "Confidence": 99.40350341796875, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028968077152967453, "Height": 0.008738613687455654, "Left": 0.528152585029602, "Top": 0.38054749369621277}, "Polygon": [{"X": 0.5281562805175781, "Y": 0.38054749369621277}, {"X": 0.557120680809021, "Y": 0.38057979941368103}, {"X": 0.5571169257164001, "Y": 0.3892861008644104}, {"X": 0.528152585029602, "Y": 0.3892538249492645}]}, "Id": "25eca990-902c-4161-98e5-62d2b3749384"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05423600599169731, "Height": 0.008727127686142921, "Left": 0.099732905626297, "Top": 0.388418048620224}, "Polygon": [{"X": 0.09973645210266113, "Y": 0.388418048620224}, {"X": 0.1539689153432846, "Y": 0.38847851753234863}, {"X": 0.15396533906459808, "Y": 0.39714518189430237}, {"X": 0.099732905626297, "Y": 0.39708471298217773}]}, "Id": "73290a7b-3517-4656-bae1-eb336112277d"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "2906", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031262367963790894, "Height": 0.008652666583657265, "Left": 0.28277844190597534, "Top": 0.39035454392433167}, "Polygon": [{"X": 0.28278201818466187, "Y": 0.39035454392433167}, {"X": 0.31404080986976624, "Y": 0.39038941264152527}, {"X": 0.3140372037887573, "Y": 0.39900723099708557}, {"X": 0.28277844190597534, "Y": 0.39897236227989197}]}, "Id": "95a131f8-6524-40d2-bd3b-38f5fa5f8c3c"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "DENISON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0670616626739502, "Height": 0.008845232427120209, "Left": 0.31744351983070374, "Top": 0.3908889591693878}, "Polygon": [{"X": 0.3174471855163574, "Y": 0.3908889591693878}, {"X": 0.38450518250465393, "Y": 0.39096373319625854}, {"X": 0.38450148701667786, "Y": 0.3997341990470886}, {"X": 0.31744351983070374, "Y": 0.3996594250202179}]}, "Id": "7113e9bb-fc3a-45ff-a34f-8066a34a4d02"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "AVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03165145963430405, "Height": 0.00873060617595911, "Left": 0.3879998028278351, "Top": 0.3915383815765381}, "Polygon": [{"X": 0.38800346851348877, "Y": 0.3915383815765381}, {"X": 0.4196512699127197, "Y": 0.3915736675262451}, {"X": 0.41964760422706604, "Y": 0.4002689719200134}, {"X": 0.3879998028278351, "Y": 0.4002336859703064}]}, "Id": "0bcbe92c-d4dc-4883-9eeb-dd46291d08f4"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053599581122398376, "Height": 0.008574213832616806, "Left": 0.099972665309906, "Top": 0.40091922879219055}, "Polygon": [{"X": 0.09997614473104477, "Y": 0.40091922879219055}, {"X": 0.15357224643230438, "Y": 0.40097901225090027}, {"X": 0.15356874465942383, "Y": 0.40949344635009766}, {"X": 0.099972665309906, "Y": 0.4094336926937103}]}, "Id": "1890dd06-7225-4909-9480-7a1d414df78b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "SNYDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06038913130760193, "Height": 0.008732439950108528, "Left": 0.28325366973876953, "Top": 0.40296220779418945}, "Polygon": [{"X": 0.28325727581977844, "Y": 0.40296220779418945}, {"X": 0.34364280104637146, "Y": 0.40302953124046326}, {"X": 0.34363919496536255, "Y": 0.41169464588165283}, {"X": 0.28325366973876953, "Y": 0.41162729263305664}]}, "Id": "e0f7948f-9188-4588-ac67-c3fbf416ba20"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020572863519191742, "Height": 0.008406602777540684, "Left": 0.34731775522232056, "Top": 0.4036155641078949}, "Polygon": [{"X": 0.3473212718963623, "Y": 0.4036155641078949}, {"X": 0.3678906261920929, "Y": 0.4036385118961334}, {"X": 0.36788710951805115, "Y": 0.4120221734046936}, {"X": 0.34731775522232056, "Y": 0.4119992256164551}]}, "Id": "c6200d6d-bde3-4a3c-8af5-50329e4e37ad"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "79549", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037705667316913605, "Height": 0.008693672716617584, "Left": 0.3723691999912262, "Top": 0.403913289308548}, "Polygon": [{"X": 0.3723728358745575, "Y": 0.403913289308548}, {"X": 0.4100748896598816, "Y": 0.403955340385437}, {"X": 0.4100712239742279, "Y": 0.41260698437690735}, {"X": 0.3723691999912262, "Y": 0.4125649333000183}]}, "Id": "edcd055c-1d23-4027-883e-f89ccc32cbf6"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Acct.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03210276737809181, "Height": 0.008214171975851059, "Left": 0.0995328351855278, "Top": 0.41321390867233276}, "Polygon": [{"X": 0.09953618049621582, "Y": 0.41321390867233276}, {"X": 0.13163559138774872, "Y": 0.4132497310638428}, {"X": 0.1316322386264801, "Y": 0.4214280843734741}, {"X": 0.0995328351855278, "Y": 0.4213922917842865}]}, "Id": "e2df2b7c-6267-4e50-9d61-9276e45901a2"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "No.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022065771743655205, "Height": 0.00846121460199356, "Left": 0.13591158390045166, "Top": 0.41347169876098633}, "Polygon": [{"X": 0.13591504096984863, "Y": 0.41347169876098633}, {"X": 0.15797735750675201, "Y": 0.4134962856769562}, {"X": 0.15797388553619385, "Y": 0.4219329059123993}, {"X": 0.13591158390045166, "Y": 0.42190831899642944}]}, "Id": "3c82b5f1-5217-4a3d-b52e-a483fa05efc9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012069943360984325, "Height": 0.007032664027065039, "Left": 0.16219891607761383, "Top": 0.4150606691837311}, "Polygon": [{"X": 0.16220180690288544, "Y": 0.4150606691837311}, {"X": 0.17426885664463043, "Y": 0.4150741398334503}, {"X": 0.17426596581935883, "Y": 0.42209333181381226}, {"X": 0.16219891607761383, "Y": 0.4220798909664154}]}, "Id": "fbbc66a4-6974-4166-b7db-d22c81661dc6"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014562991447746754, "Height": 0.008239878341555595, "Left": 0.17838606238365173, "Top": 0.4139339327812195}, "Polygon": [{"X": 0.17838945984840393, "Y": 0.4139339327812195}, {"X": 0.1929490566253662, "Y": 0.4139501750469208}, {"X": 0.1929456740617752, "Y": 0.42217379808425903}, {"X": 0.17838606238365173, "Y": 0.4221575856208801}]}, "Id": "540fafc7-ae64-47b8-aad9-e34af71e9080"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Debited:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05197345092892647, "Height": 0.008852246217429638, "Left": 0.19673806428909302, "Top": 0.4140974283218384}, "Polygon": [{"X": 0.19674168527126312, "Y": 0.4140974283218384}, {"X": 0.2487115114927292, "Y": 0.41415539383888245}, {"X": 0.2487078607082367, "Y": 0.4229496717453003}, {"X": 0.19673806428909302, "Y": 0.4228917360305786}]}, "Id": "5571f474-bc12-426e-a01e-6aaa57b1eee2"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0763183981180191, "Height": 0.009059110656380653, "Left": 0.2559070289134979, "Top": 0.4148607850074768}, "Polygon": [{"X": 0.2559107542037964, "Y": 0.4148607850074768}, {"X": 0.3322254419326782, "Y": 0.4149458706378937}, {"X": 0.3322216868400574, "Y": 0.****************}, {"X": 0.2559070289134979, "Y": 0.42383480072021484}]}, "Id": "a5db0a22-38bc-4519-b4ed-54b8e9860ffc"}, {"BlockType": "WORD", "Confidence": 99.36443328857422, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004890156909823418, "Height": 0.008111842907965183, "Left": 0.09921630471944809, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.10410646349191666, "Y": 0.4380359351634979}, {"X": 0.10410314798355103, "Y": 0.4461423456668854}, {"X": 0.09921630471944809, "Y": 0.4461368918418884}]}, "Id": "62dd8fa4-4398-4f7b-a7ff-638b330e3f6c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "hereby", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041725654155015945, "Height": 0.010362897999584675, "Left": 0.1079772412776947, "Top": 0.4381806552410126}, "Polygon": [{"X": 0.10798146575689316, "Y": 0.4381806552410126}, {"X": 0.14970289170742035, "Y": 0.43822717666625977}, {"X": 0.1496986597776413, "Y": 0.4485435485839844}, {"X": 0.1079772412776947, "Y": 0.4484970271587372}]}, "Id": "d10d7388-e0ce-478c-bdf4-75d09dc34995"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "authorize", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05691879615187645, "Height": 0.00862996093928814, "Left": 0.1537306010723114, "Top": 0.4389561116695404}, "Polygon": [{"X": 0.15373411774635315, "Y": 0.4389561116695404}, {"X": 0.21064940094947815, "Y": 0.4390195906162262}, {"X": 0.210645854473114, "Y": 0.4475860893726349}, {"X": 0.1537306010723114, "Y": 0.4475226104259491}]}, "Id": "ddee104e-0cdb-4951-9766-f08883605307"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019032850861549377, "Height": 0.008390411734580994, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "5280d888-de80-4574-a18a-bbe06965ab83"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029869254678487778, "Height": 0.008501370437443256, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "13657ca0-48fa-4d94-89d0-85ba36a6c24e"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.007100374437868595, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.***************}]}, "Id": "017af653-237f-45e0-822b-498481ea94f4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "execute", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046046797186136246, "Height": 0.006626857444643974, "Left": 0.28592348098754883, "Top": 0.4418908953666687}, "Polygon": [{"X": 0.2859262228012085, "Y": 0.4418908953666687}, {"X": 0.3319702744483948, "Y": 0.4419422447681427}, {"X": 0.3319675326347351, "Y": 0.44851773977279663}, {"X": 0.28592348098754883, "Y": 0.44846639037132263}]}, "Id": "d8b23b74-3491-4595-bb7e-b264ec110db0"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01873108372092247, "Height": 0.008116455748677254, "Left": 0.3357938230037689, "Top": 0.44077304005622864}, "Polygon": [{"X": 0.33579719066619873, "Y": 0.44077304005622864}, {"X": 0.3545249104499817, "Y": 0.4407939314842224}, {"X": 0.3545215129852295, "Y": 0.44888949394226074}, {"X": 0.3357938230037689, "Y": 0.44886860251426697}]}, "Id": "72c21efa-41d8-4b12-8a5b-10dd164671bf"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02743031270802021, "Height": 0.008401285856962204, "Left": 0.3582764267921448, "Top": 0.44082534313201904}, "Polygon": [{"X": 0.35827991366386414, "Y": 0.44082534313201904}, {"X": 0.38570672273635864, "Y": 0.4408559501171112}, {"X": 0.3857032060623169, "Y": 0.44922664761543274}, {"X": 0.3582764267921448, "Y": 0.4491960406303406}]}, "Id": "9fc55dfd-e541-4020-98ae-2f1be3175adc"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "transfer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047389917075634, "Height": 0.008226776495575905, "Left": 0.3890056014060974, "Top": 0.4415534436702728}, "Polygon": [{"X": 0.389009028673172, "Y": 0.4415534436702728}, {"X": 0.436395525932312, "Y": 0.4416062831878662}, {"X": 0.43639206886291504, "Y": 0.4497802257537842}, {"X": 0.3890056014060974, "Y": 0.4497273862361908}]}, "Id": "ba3efa73-674b-4634-9e09-d5a1a352bc53"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014392286539077759, "Height": 0.008148986846208572, "Left": 0.4396112561225891, "Top": 0.4418027997016907}, "Polygon": [{"X": 0.4396146833896637, "Y": 0.4418027997016907}, {"X": 0.45400354266166687, "Y": 0.44181883335113525}, {"X": 0.4540001153945923, "Y": 0.44995176792144775}, {"X": 0.4396112561225891, "Y": 0.4499357342720032}]}, "Id": "a2064664-17ab-4ad3-9c74-9a9ce0c5d59e"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "funds", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033999551087617874, "Height": 0.008589833974838257, "Left": 0.4562873840332031, "Top": 0.4418957233428955}, "Polygon": [{"X": 0.45629099011421204, "Y": 0.4418957233428955}, {"X": 0.4902869462966919, "Y": 0.44193363189697266}, {"X": 0.4902833104133606, "Y": 0.45048555731773376}, {"X": 0.4562873840332031, "Y": 0.45044761896133423}]}, "Id": "3972932d-987b-4fce-b9fb-aed47f54980c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "according", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059646520763635635, "Height": 0.010708454996347427, "Left": 0.49379652738571167, "Top": 0.44252657890319824}, "Polygon": [{"X": 0.4938010275363922, "Y": 0.44252657890319824}, {"X": 0.5534430742263794, "Y": 0.4425930976867676}, {"X": 0.5534385442733765, "Y": 0.45323505997657776}, {"X": 0.49379652738571167, "Y": 0.4531685411930084}]}, "Id": "9887c52a-516f-4bf5-83e9-9e849d24501f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011696933768689632, "Height": 0.007251929026097059, "Left": 0.5571650862693787, "Top": 0.44416284561157227}, "Polygon": [{"X": 0.5571681261062622, "Y": 0.44416284561157227}, {"X": 0.5688620209693909, "Y": 0.4441758692264557}, {"X": 0.5688589215278625, "Y": 0.4514147639274597}, {"X": 0.5571650862693787, "Y": 0.4514017105102539}]}, "Id": "6ebdec5b-b7fa-4de1-85b1-ace27231ddf9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "my", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019385643303394318, "Height": 0.007953919470310211, "Left": 0.572927713394165, "Top": 0.4454951882362366}, "Polygon": [{"X": 0.5729311108589172, "Y": 0.4454951882362366}, {"X": 0.5923133492469788, "Y": 0.44551682472229004}, {"X": 0.5923099517822266, "Y": 0.4534491300582886}, {"X": 0.572927713394165, "Y": 0.4534274935722351}]}, "Id": "e50abbab-4a57-4005-8822-8494dba3dd74"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "instructions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07111731171607971, "Height": 0.008886292576789856, "Left": 0.5960896611213684, "Top": 0.44351470470428467}, "Polygon": [{"X": 0.596093475818634, "Y": 0.44351470470428467}, {"X": 0.6672070026397705, "Y": 0.4435940086841583}, {"X": 0.6672031879425049, "Y": 0.45240098237991333}, {"X": 0.5960896611213684, "Y": 0.4523216784000397}]}, "Id": "04223ff0-bf28-4520-8638-4e0249738633"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034960512071847916, "Height": 0.008260300382971764, "Left": 0.6709778308868408, "Top": 0.4445864260196686}, "Polygon": [{"X": 0.6709813475608826, "Y": 0.4445864260196686}, {"X": 0.7059383392333984, "Y": 0.4446254074573517}, {"X": 0.7059347629547119, "Y": 0.4528467357158661}, {"X": 0.6709778308868408, "Y": 0.4528077244758606}]}, "Id": "d495299c-e636-4172-886b-1fe7a718b474"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03667367994785309, "Height": 0.00847367662936449, "Left": 0.7099365592002869, "Top": 0.444845050573349}, "Polygon": [{"X": 0.7099402546882629, "Y": 0.444845050573349}, {"X": 0.7466102838516235, "Y": 0.4448859393596649}, {"X": 0.7466065883636475, "Y": 0.45331871509552}, {"X": 0.7099365592002869, "Y": 0.4532778263092041}]}, "Id": "cfdf705b-dffb-4e51-a3a6-87587ae19fa4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021830685436725616, "Height": 0.008184153586626053, "Left": 0.750264585018158, "Top": 0.44534578919410706}, "Polygon": [{"X": 0.7502681016921997, "Y": 0.44534578919410706}, {"X": 0.772095263004303, "Y": 0.4453701376914978}, {"X": 0.7720916867256165, "Y": 0.453529953956604}, {"X": 0.750264585018158, "Y": 0.45350560545921326}]}, "Id": "6ceb0824-48d2-44d6-881d-e5ff735a12da"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012088479474186897, "Height": 0.006961895618587732, "Left": 0.7756710648536682, "Top": 0.4467576742172241}, "Polygon": [{"X": 0.7756741046905518, "Y": 0.4467576742172241}, {"X": 0.78775954246521, "Y": 0.44677114486694336}, {"X": 0.7877565622329712, "Y": 0.4537195563316345}, {"X": 0.7756710648536682, "Y": 0.4537060856819153}]}, "Id": "0c8d6641-5ef8-4886-9a52-bb2245bc2e80"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "charge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04037613421678543, "Height": 0.010659088380634785, "Left": 0.7917807102203369, "Top": 0.44566184282302856}, "Polygon": [{"X": 0.7917852997779846, "Y": 0.44566184282302856}, {"X": 0.8321568369865417, "Y": 0.44570687413215637}, {"X": 0.8321521878242493, "Y": 0.45632094144821167}, {"X": 0.7917807102203369, "Y": 0.45627591013908386}]}, "Id": "a2abf37e-8d49-4408-8fc9-e2b5dae0bab3"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "my", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008194501511752605, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.***************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "77e333e8-c6b5-4ec9-8a7e-6a0fadeb354e"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "account", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.007088018115609884, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "17615c7c-fcba-45ef-b6da-3b0913c42694"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013100226409733295, "Height": 0.005740098189562559, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "73cd659a-e0ec-4137-9790-9601d6e5f4b9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "collect", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04098619893193245, "Height": 0.008436135947704315, "Left": 0.16717898845672607, "Top": 0.45125535130500793}, "Polygon": [{"X": 0.16718243062496185, "Y": 0.45125535130500793}, {"X": 0.20816518366336823, "Y": 0.45130106806755066}, {"X": 0.20816171169281006, "Y": 0.45969149470329285}, {"X": 0.16717898845672607, "Y": 0.4596457779407501}]}, "Id": "9512b080-46ce-4123-b7ef-617f540ad558"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "cash", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026509398594498634, "Height": 0.008001621812582016, "Left": 0.21178288757801056, "Top": 0.45182353258132935}, "Polygon": [{"X": 0.2117861956357956, "Y": 0.45182353258132935}, {"X": 0.23829229176044464, "Y": 0.45185306668281555}, {"X": 0.2382889837026596, "Y": 0.4598251283168793}, {"X": 0.21178288757801056, "Y": 0.45979559421539307}]}, "Id": "8e48df75-6252-46c6-ae55-ee74ec640a75"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053016312420368195, "Height": 0.0090982336550951, "Left": 0.24212227761745453, "Top": 0.4533713161945343}, "Polygon": [{"X": 0.24212603271007538, "Y": 0.4533713161945343}, {"X": 0.2951385974884033, "Y": 0.4534304440021515}, {"X": 0.2951348125934601, "Y": 0.46246954798698425}, {"X": 0.24212227761745453, "Y": 0.46241042017936707}]}, "Id": "208a9cb5-2018-4a96-a1b0-6bf11155b385"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017911184579133987, "Height": 0.008286252617835999, "Left": 0.2990667223930359, "Top": 0.45250943303108215}, "Polygon": [{"X": 0.2990701496601105, "Y": 0.45250943303108215}, {"X": 0.3169778883457184, "Y": 0.4525294005870819}, {"X": 0.3169744610786438, "Y": 0.46079567074775696}, {"X": 0.2990667223930359, "Y": 0.4607757031917572}]}, "Id": "92d3997f-e9c4-45bc-a9de-b58da563f8f9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01881771720945835, "Height": 0.008215215057134628, "Left": 0.3203960657119751, "Top": 0.452716588973999}, "Polygon": [{"X": 0.3203994929790497, "Y": 0.452716588973999}, {"X": 0.3392137885093689, "Y": 0.45273756980895996}, {"X": 0.3392103612422943, "Y": 0.46093180775642395}, {"X": 0.3203960657119751, "Y": 0.460910826921463}]}, "Id": "95533e36-1993-4ed3-87cf-8dc83bf9517a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0458095520734787, "Height": 0.00681643933057785, "Left": 0.34295982122421265, "Top": 0.45474568009376526}, "Polygon": [{"X": 0.3429626524448395, "Y": 0.45474568009376526}, {"X": 0.38876935839653015, "Y": 0.45479676127433777}, {"X": 0.3887665271759033, "Y": 0.4615621268749237}, {"X": 0.34295982122421265, "Y": 0.4615110158920288}]}, "Id": "********-d6e0-4307-b573-a434a1be2c69"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01460993755608797, "Height": 0.00846224557608366, "Left": 0.39235177636146545, "Top": 0.4534642696380615}, "Polygon": [{"X": 0.3923553228378296, "Y": 0.4534642696380615}, {"X": 0.40696173906326294, "Y": 0.4534805715084076}, {"X": 0.4069581627845764, "Y": 0.46192651987075806}, {"X": 0.39235177636146545, "Y": 0.4619102478027344}]}, "Id": "bf6a162b-c02b-4808-81a8-9dcb32462316"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018777405843138695, "Height": 0.008223882876336575, "Left": 0.4087464511394501, "Top": 0.453635573387146}, "Polygon": [{"X": 0.40874987840652466, "Y": 0.453635573387146}, {"X": 0.4275238513946533, "Y": 0.45365652441978455}, {"X": 0.42752039432525635, "Y": 0.46185946464538574}, {"X": 0.4087464511394501, "Y": 0.4618385136127472}]}, "Id": "6aad7d34-f303-4931-b084-55a8c1108d0f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027277568355202675, "Height": 0.008199985139071941, "Left": 0.43114376068115234, "Top": 0.45400696992874146}, "Polygon": [{"X": 0.4311472177505493, "Y": 0.45400696992874146}, {"X": 0.45842134952545166, "Y": 0.4540373682975769}, {"X": 0.4584178924560547, "Y": 0.46220695972442627}, {"X": 0.43114376068115234, "Y": 0.46217653155326843}]}, "Id": "5179d25d-0f38-45e2-a3d6-b3ab3e10a6a3"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "transfer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04711737483739853, "Height": 0.008412654511630535, "Left": 0.4618349075317383, "Top": 0.45453083515167236}, "Polygon": [{"X": 0.4618384540081024, "Y": 0.45453083515167236}, {"X": 0.508952260017395, "Y": 0.45458337664604187}, {"X": 0.5089487433433533, "Y": 0.46294349431991577}, {"X": 0.4618349075317383, "Y": 0.46289095282554626}]}, "Id": "d2989d45-3eb5-4725-af17-27c41a7f2f3d"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021846994757652283, "Height": 0.00805593840777874, "Left": 0.5122414827346802, "Top": 0.4551319479942322}, "Polygon": [{"X": 0.5122448801994324, "Y": 0.4551319479942322}, {"X": 0.5340884327888489, "Y": 0.4551562964916229}, {"X": 0.5340850353240967, "Y": 0.4631878733634949}, {"X": 0.5122414827346802, "Y": 0.4631635248661041}]}, "Id": "cf7a51f9-a347-4525-a214-5fa7505b736b"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "all", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01480418536812067, "Height": 0.007930445484817028, "Left": 0.5380664467811584, "Top": 0.4552616477012634}, "Polygon": [{"X": 0.5380698442459106, "Y": 0.4552616477012634}, {"X": 0.5528706312179565, "Y": 0.4552781581878662}, {"X": 0.5528672933578491, "Y": 0.4631921052932739}, {"X": 0.5380664467811584, "Y": 0.46317559480667114}]}, "Id": "5f509a66-7a1a-44b6-8e82-7407b0f729b3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "applicable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06231577694416046, "Height": 0.010307629592716694, "Left": 0.5571380257606506, "Top": 0.45546409487724304}, "Polygon": [{"X": 0.5571423768997192, "Y": 0.45546409487724304}, {"X": 0.6194537878036499, "Y": 0.45553359389305115}, {"X": 0.6194493770599365, "Y": 0.46577173471450806}, {"X": 0.5571380257606506, "Y": 0.46570223569869995}]}, "Id": "bc2f2112-3ae3-4e91-8061-ec331d778618"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "fees.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0270523801445961, "Height": 0.008708188310265541, "Left": 0.6235731244087219, "Top": 0.45592790842056274}, "Polygon": [{"X": 0.6235768795013428, "Y": 0.45592790842056274}, {"X": 0.6506255269050598, "Y": 0.4559580683708191}, {"X": 0.650621771812439, "Y": 0.46463608741760254}, {"X": 0.6235731244087219, "Y": 0.4646059274673462}]}, "Id": "287e17df-7a94-479d-bbd6-93dffcb1b035"}, {"BlockType": "WORD", "Confidence": 99.10893249511719, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004769246559590101, "Height": 0.007959961891174316, "Left": 0.6593051552772522, "Top": 0.4564855694770813}, "Polygon": [{"X": 0.6593086123466492, "Y": 0.4564855694770813}, {"X": 0.6640744209289551, "Y": 0.4564908742904663}, {"X": 0.6640710234642029, "Y": 0.4644455313682556}, {"X": 0.6593051552772522, "Y": 0.4644401967525482}]}, "Id": "3be0200e-2884-4b29-80ed-31410e510d5b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "understand", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06608206778764725, "Height": 0.008565043099224567, "Left": 0.6680253744125366, "Top": 0.4567655324935913}, "Polygon": [{"X": 0.6680290699005127, "Y": 0.4567655324935913}, {"X": 0.7341074347496033, "Y": 0.4568392336368561}, {"X": 0.734103798866272, "Y": 0.465330570936203}, {"X": 0.6680253744125366, "Y": 0.46525686979293823}]}, "Id": "bf4b8b3d-720f-4d24-87ea-39b6d73014db"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008100870996713638, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "e5155130-f2a4-4dc8-812b-8305d51f7fff"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029191846027970314, "Height": 0.008732191286981106, "Left": 0.****************, "Top": 0.***************}, "Polygon": [{"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "893e12a3-6321-4eb1-a62d-3f5879bc0619"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "handles", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "4d1f45fb-2bab-4eb4-a74c-cd1f736832a8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027048079296946526, "Height": 0.008377334102988243, "Left": 0.8445830941200256, "Top": 0.45838871598243713}, "Polygon": [{"X": 0.8445867896080017, "Y": 0.45838871598243713}, {"X": 0.8716312050819397, "Y": 0.4584188759326935}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.8445830941200256, "Y": 0.4667358994483948}]}, "Id": "4e5c7ec5-6cd7-4500-9bce-b5a7f0827d7c"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "transfer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04741330444812775, "Height": 0.008345115929841995, "Left": 0.09858890622854233, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.14600220322608948, "Y": 0.4627547562122345}, {"X": 0.14599880576133728, "Y": 0.4710470139980316}, {"X": 0.09858890622854233, "Y": 0.47099411487579346}]}, "Id": "891d9cf4-5270-4e21-a649-b6220b13d90b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "requests", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050150688737630844, "Height": 0.009041705168783665, "Left": 0.14933016896247864, "Top": 0.4645138084888458}, "Polygon": [{"X": 0.1493338644504547, "Y": 0.4645138084888458}, {"X": 0.19948086142539978, "Y": 0.46456974744796753}, {"X": 0.1994771510362625, "Y": 0.4735555350780487}, {"X": 0.14933016896247864, "Y": 0.473499596118927}]}, "Id": "95f6637e-91e4-400a-a9fc-817feffb5e8b"}, {"BlockType": "WORD", "Confidence": 89.83577728271484, "Text": "expeditiously,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08518634736537933, "Height": 0.010515481233596802, "Left": 0.20324233174324036, "Top": 0.46382835507392883}, "Polygon": [{"X": 0.20324663817882538, "Y": 0.46382835507392883}, {"X": 0.2884286940097809, "Y": 0.4639233350753784}, {"X": 0.2884243428707123, "Y": 0.47434383630752563}, {"X": 0.20324233174324036, "Y": 0.47424882650375366}]}, "Id": "f54e1bb4-e66a-436d-b2ec-aac17c577ea9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "but", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01968301273882389, "Height": 0.00836698617786169, "Left": 0.2930307984352112, "Top": 0.46450984477996826}, "Polygon": [{"X": 0.29303428530693054, "Y": 0.46450984477996826}, {"X": 0.3127138316631317, "Y": 0.4645317792892456}, {"X": 0.31271034479141235, "Y": 0.47287681698799133}, {"X": 0.2930307984352112, "Y": 0.472854882478714}]}, "Id": "cc229cdd-3b1d-4c63-ae41-53f414ba0ac3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023173505440354347, "Height": 0.008323458023369312, "Left": 0.31607866287231445, "Top": 0.4649817645549774}, "Polygon": [{"X": 0.3160821199417114, "Y": 0.4649817645549774}, {"X": 0.33925217390060425, "Y": 0.46500760316848755}, {"X": 0.3392487168312073, "Y": 0.47330522537231445}, {"X": 0.31607866287231445, "Y": 0.47327935695648193}]}, "Id": "38bce648-8a56-4805-971f-3eb17869ec48"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "be00bc07-50f5-4b0a-be50-d0a68eb8f9ce"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029767371714115143, "Height": 0.008602643385529518, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "646b2aad-29cc-4028-8eab-178c26ffabb7"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "cannot", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.007238657213747501, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "fe54728c-7a6e-4f0b-81f6-b3eb0ec8ce0c"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "guarantee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05935715138912201, "Height": 0.008909755386412144, "Left": 0.4428468644618988, "Top": 0.4676520824432373}, "Polygon": [{"X": 0.44285058975219727, "Y": 0.4676520824432373}, {"X": 0.5022040009498596, "Y": 0.4677182734012604}, {"X": 0.5022002458572388, "Y": 0.47656184434890747}, {"X": 0.4428468644618988, "Y": 0.4764956533908844}]}, "Id": "329eb6ee-bf10-47a8-8775-d00c7c8bae32"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02222578413784504, "Height": 0.00831039622426033, "Left": 0.5057535171508789, "Top": 0.4668445885181427}, "Polygon": [{"X": 0.5057570338249207, "Y": 0.4668445885181427}, {"X": 0.52797931432724, "Y": 0.46686938405036926}, {"X": 0.5279757976531982, "Y": 0.4751549959182739}, {"X": 0.5057535171508789, "Y": 0.47513020038604736}]}, "Id": "cf81e0fb-11bf-4f6e-b663-1283c584add9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02349705994129181, "Height": 0.008273470215499401, "Left": 0.5317381024360657, "Top": 0.46700114011764526}, "Polygon": [{"X": 0.5317416191101074, "Y": 0.46700114011764526}, {"X": 0.5552351474761963, "Y": 0.46702733635902405}, {"X": 0.5552316308021545, "Y": 0.47527462244033813}, {"X": 0.5317381024360657, "Y": 0.47524842619895935}]}, "Id": "f8a278b1-e4ee-4f66-ab2f-7f7996b2a07e"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014590943232178688, "Height": 0.007952060550451279, "Left": 0.5590907335281372, "Top": 0.4675652086734772}, "Polygon": [{"X": 0.5590941309928894, "Y": 0.4675652086734772}, {"X": 0.573681652545929, "Y": 0.46758148074150085}, {"X": 0.5736782550811768, "Y": 0.47551727294921875}, {"X": 0.5590907335281372, "Y": 0.47550100088119507}]}, "Id": "a970b50e-3701-4ba7-b25e-27d996800d35"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "completed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06262937188148499, "Height": 0.010065287351608276, "Left": 0.577730655670166, "Top": 0.4682685434818268}, "Polygon": [{"X": 0.5777348875999451, "Y": 0.4682685434818268}, {"X": 0.6403599977493286, "Y": 0.46833840012550354}, {"X": 0.6403557062149048, "Y": 0.47833383083343506}, {"X": 0.577730655670166, "Y": 0.4782640039920807}]}, "Id": "9b2e6825-b800-4f84-84cd-4099a6ea63fb"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01164386048913002, "Height": 0.007967093028128147, "Left": 0.6446012258529663, "Top": 0.4684979319572449}, "Polygon": [{"X": 0.6446046233177185, "Y": 0.4684979319572449}, {"X": 0.6562450528144836, "Y": 0.4685109257698059}, {"X": 0.6562416553497314, "Y": 0.47646501660346985}, {"X": 0.6446012258529663, "Y": 0.4764520525932312}]}, "Id": "5902830f-dda5-47ca-a2f7-8a32a4e4b29b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02161458693444729, "Height": 0.00840549636632204, "Left": 0.6600939631462097, "Top": 0.4705846905708313}, "Polygon": [{"X": 0.660097599029541, "Y": 0.4705846905708313}, {"X": 0.681708574295044, "Y": 0.47060877084732056}, {"X": 0.6817049384117126, "Y": 0.4789901673793793}, {"X": 0.6600939631462097, "Y": 0.4789660573005676}]}, "Id": "6e3be9cf-b616-4ae7-a67e-a641a8de13b6"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "specific", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04724394157528877, "Height": 0.01032158825546503, "Left": 0.6855500936508179, "Top": 0.46898069977760315}, "Polygon": [{"X": 0.6855545043945312, "Y": 0.46898069977760315}, {"X": 0.7327940464019775, "Y": 0.4690333902835846}, {"X": 0.7327895760536194, "Y": 0.4793022871017456}, {"X": 0.6855500936508179, "Y": 0.47924959659576416}]}, "Id": "1892e0d8-3b48-49bf-9423-d903becd50f0"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027207178995013237, "Height": 0.008113895542919636, "Left": 0.7366442084312439, "Top": 0.46941882371902466}, "Polygon": [{"X": 0.7366476655006409, "Y": 0.46941882371902466}, {"X": 0.7638513445854187, "Y": 0.46944916248321533}, {"X": 0.7638478875160217, "Y": 0.4775327146053314}, {"X": 0.7366442084312439, "Y": 0.47750237584114075}]}, "Id": "dc1466e8-20a1-4d04-b326-8d2a7fa4106e"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "period.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042179618030786514, "Height": 0.010091736912727356, "Left": 0.7674094438552856, "Top": 0.4697785973548889}, "Polygon": [{"X": 0.7674137949943542, "Y": 0.4697785973548889}, {"X": 0.8095890879631042, "Y": 0.4698256552219391}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.7674094438552856, "Y": 0.4798232913017273}]}, "Id": "3144a588-8e59-4514-9eca-a2892bb95bd9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09786926209926605, "Height": 0.010364057496190071, "Left": 0.09833836555480957, "Top": 0.5022342205047607}, "Polygon": [{"X": 0.09834256023168564, "Y": 0.5022342205047607}, {"X": 0.19620762765407562, "Y": 0.5023433566093445}, {"X": 0.19620339572429657, "Y": 0.5125982761383057}, {"X": 0.09833836555480957, "Y": 0.5124890804290771}]}, "Id": "b0b78538-610a-4b43-a5e6-3705c9f04da1"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "SIGNATURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1005384624004364, "Height": 0.010216997936367989, "Left": 0.20034272968769073, "Top": 0.5032880902290344}, "Polygon": [{"X": 0.200346902012825, "Y": 0.5032880902290344}, {"X": 0.30088117718696594, "Y": 0.5034002661705017}, {"X": 0.3008769750595093, "Y": 0.5135051012039185}, {"X": 0.20034272968769073, "Y": 0.513392984867096}]}, "Id": "e121f059-ec42-47a4-9f71-936f7e2534f4"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "(required)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07381375879049301, "Height": 0.011675126850605011, "Left": 0.30551305413246155, "Top": 0.5047302842140198}, "Polygon": [{"X": 0.30551788210868835, "Y": 0.5047302842140198}, {"X": 0.37932682037353516, "Y": 0.5048125982284546}, {"X": 0.37932196259498596, "Y": 0.5164054036140442}, {"X": 0.30551305413246155, "Y": 0.5163230895996094}]}, "Id": "37fccdd9-cd02-4e20-a02e-d453ba2ee605"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Phone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04249117150902748, "Height": 0.009108005091547966, "Left": 0.4642619490623474, "Top": 0.5063906311988831}, "Polygon": [{"X": 0.46426576375961304, "Y": 0.5063906311988831}, {"X": 0.5067530870437622, "Y": 0.5064380168914795}, {"X": 0.5067492723464966, "Y": 0.5154986381530762}, {"X": 0.4642619490623474, "Y": 0.5154512524604797}]}, "Id": "6873e06e-1383-406f-aa0c-d69dadf400c9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03161102533340454, "Height": 0.008885793387889862, "Left": 0.6465009450912476, "Top": 0.5085980296134949}, "Polygon": [{"X": 0.6465047597885132, "Y": 0.5085980296134949}, {"X": 0.6781119704246521, "Y": 0.5086332559585571}, {"X": 0.6781081557273865, "Y": 0.5174838304519653}, {"X": 0.6465009450912476, "Y": 0.5174485445022583}]}, "Id": "8f686daa-14a0-4ca8-ad0e-fea0b61c965e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03600800037384033, "Height": 0.009145515039563179, "Left": 0.7675648331642151, "Top": 0.5096938014030457}, "Polygon": [{"X": 0.7675687670707703, "Y": 0.5096938014030457}, {"X": 0.8035728335380554, "Y": 0.5097339153289795}, {"X": 0.8035688400268555, "Y": 0.5188392996788025}, {"X": 0.7675648331642151, "Y": 0.5187991261482239}]}, "Id": "11de57a0-7b4a-404e-bc20-6dfcbb636f5e"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Callback", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058271802961826324, "Height": 0.008705748245120049, "Left": 0.4647212624549866, "Top": 0.5617637038230896}, "Polygon": [{"X": 0.46472489833831787, "Y": 0.5617637038230896}, {"X": 0.5229930877685547, "Y": 0.5618286728858948}, {"X": 0.5229893922805786, "Y": 0.5704694390296936}, {"X": 0.4647212624549866, "Y": 0.5704044103622437}]}, "Id": "f90a8f52-fa3b-4d05-ad37-0d8aa784f689"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Phone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03808131068944931, "Height": 0.008610144257545471, "Left": 0.09723440557718277, "Top": 0.5697985291481018}, "Polygon": [{"X": 0.09723791480064392, "Y": 0.5697985291481018}, {"X": 0.13531571626663208, "Y": 0.5698410272598267}, {"X": 0.13531219959259033, "Y": 0.5784087181091309}, {"X": 0.09723440557718277, "Y": 0.578366219997406}]}, "Id": "47cf69ee-1a9d-477a-ac14-5b5823bb4149"}, {"BlockType": "WORD", "Confidence": 99.32537841796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00790818128734827, "Height": 0.007995094172656536, "Left": 0.13870137929916382, "Top": 0.5703064203262329}, "Polygon": [{"X": 0.13870465755462646, "Y": 0.5703064203262329}, {"X": 0.14660955965518951, "Y": 0.5703152418136597}, {"X": 0.14660628139972687, "Y": 0.5783014893531799}, {"X": 0.13870137929916382, "Y": 0.5782926678657532}]}, "Id": "96c4219b-d8ec-47be-b219-b08099ae0f8e"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "called:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04000106826424599, "Height": 0.008847520686686039, "Left": 0.15073731541633606, "Top": 0.5704143643379211}, "Polygon": [{"X": 0.15074092149734497, "Y": 0.5704143643379211}, {"X": 0.19073837995529175, "Y": 0.5704589486122131}, {"X": 0.19073474407196045, "Y": 0.5792618989944458}, {"X": 0.15073731541633606, "Y": 0.579217255115509}]}, "Id": "4664b200-7981-481a-a201-37990d0c9a41"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032812342047691345, "Height": 0.008474858477711678, "Left": 0.09645896404981613, "Top": 0.5821663737297058}, "Polygon": [{"X": 0.09646241366863251, "Y": 0.5821663737297058}, {"X": 0.12927129864692688, "Y": 0.5822029709815979}, {"X": 0.1292678415775299, "Y": 0.59064120054245}, {"X": 0.09645896404981613, "Y": 0.5906046032905579}]}, "Id": "cec08cb6-4194-4f67-abfd-07c1f1da0799"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01427219994366169, "Height": 0.008179904893040657, "Left": 0.13302414119243622, "Top": 0.5826135277748108}, "Polygon": [{"X": 0.13302749395370483, "Y": 0.5826135277748108}, {"X": 0.14729633927345276, "Y": 0.5826294422149658}, {"X": 0.14729298651218414, "Y": 0.5907934308052063}, {"X": 0.13302414119243622, "Y": 0.5907775163650513}]}, "Id": "1e9ff490-483b-463f-b5ed-557296d9efd5"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "call:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02563156746327877, "Height": 0.008318763226270676, "Left": 0.1498122662305832, "Top": 0.5829530358314514}, "Polygon": [{"X": 0.14981567859649658, "Y": 0.5829530358314514}, {"X": 0.1754438281059265, "Y": 0.5829815864562988}, {"X": 0.17544041574001312, "Y": 0.5912717580795288}, {"X": 0.1498122662305832, "Y": 0.5912432074546814}]}, "Id": "0e439505-4822-4d57-8a97-3e477d99ebca"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Source", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042558327317237854, "Height": 0.008514762856066227, "Left": 0.5026665925979614, "Top": 0.5865266919136047}, "Polygon": [{"X": 0.5026702284812927, "Y": 0.5865266919136047}, {"X": 0.5452249646186829, "Y": 0.586574137210846}, {"X": 0.5452213287353516, "Y": 0.5950414538383484}, {"X": 0.5026665925979614, "Y": 0.5949939489364624}]}, "Id": "3240a76d-a0c9-40dd-8ac4-553f7e6ea2ea"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014116033911705017, "Height": 0.008343616500496864, "Left": 0.5486706495285034, "Top": 0.5869782567024231}, "Polygon": [{"X": 0.5486741662025452, "Y": 0.5869782567024231}, {"X": 0.5627866983413696, "Y": 0.5869939923286438}, {"X": 0.5627831220626831, "Y": 0.5953218936920166}, {"X": 0.5486706495285034, "Y": 0.5953061580657959}]}, "Id": "99dfd946-b4af-410a-ac49-ae01d2c05e70"}, {"BlockType": "WORD", "Confidence": 99.03240966796875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011814323253929615, "Height": 0.008316055871546268, "Left": 0.5645931363105774, "Top": 0.5872128009796143}, "Polygon": [{"X": 0.5645967125892639, "Y": 0.5872128009796143}, {"X": 0.5764074921607971, "Y": 0.5872259736061096}, {"X": 0.5764039158821106, "Y": 0.5955289006233215}, {"X": 0.5645931363105774, "Y": 0.5955156683921814}]}, "Id": "075ca6ea-3a00-45f2-91e2-ef7956def84e"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Verified", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05045429989695549, "Height": 0.008330372162163258, "Left": 0.09690238535404205, "Top": 0.5942326784133911}, "Polygon": [{"X": 0.09690576791763306, "Y": 0.5942326784133911}, {"X": 0.14735668897628784, "Y": 0.5942889451980591}, {"X": 0.14735329151153564, "Y": 0.6025630831718445}, {"X": 0.09690238535404205, "Y": 0.6025067567825317}]}, "Id": "e86828c9-5a3e-45fc-be68-204bf355fa21"}, {"BlockType": "WORD", "Confidence": 99.73233795166016, "Text": "with:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03145020082592964, "Height": 0.007961143739521503, "Left": 0.15108439326286316, "Top": 0.5950746536254883}, "Polygon": [{"X": 0.1510876566171646, "Y": 0.5950746536254883}, {"X": 0.1825345903635025, "Y": 0.595109760761261}, {"X": 0.18253132700920105, "Y": 0.6030358076095581}, {"X": 0.15108439326286316, "Y": 0.6030007004737854}]}, "Id": "f9d4a81b-f3e0-4da3-b835-eb380b8a8dd5"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Callback", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0537237711250782, "Height": 0.00847055297344923, "Left": 0.5023673176765442, "Top": 0.5987325310707092}, "Polygon": [{"X": 0.5023708939552307, "Y": 0.5987325310707092}, {"X": 0.5560911297798157, "Y": 0.5987924337387085}, {"X": 0.5560874938964844, "Y": 0.6072030663490295}, {"X": 0.5023673176765442, "Y": 0.6071431040763855}]}, "Id": "530bba0a-7c31-425c-af68-b0f85f5c5172"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033273499459028244, "Height": 0.008045083843171597, "Left": 0.5599201321601868, "Top": 0.5994306206703186}, "Polygon": [{"X": 0.559923529624939, "Y": 0.5994306206703186}, {"X": 0.5931936502456665, "Y": 0.5994677543640137}, {"X": 0.5931901931762695, "Y": 0.6074756979942322}, {"X": 0.5599201321601868, "Y": 0.6074385643005371}]}, "Id": "5b81391a-6324-41cb-96d1-3bbe1ff0ae45"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "by:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018451016396284103, "Height": 0.01009330339729786, "Left": 0.5968808531761169, "Top": 0.5997514724731445}, "Polygon": [{"X": 0.5968851447105408, "Y": 0.5997514724731445}, {"X": 0.6153318881988525, "Y": 0.5997720956802368}, {"X": 0.6153275370597839, "Y": 0.6098448038101196}, {"X": 0.5968808531761169, "Y": 0.6098242402076721}]}, "Id": "c4d4068f-fb72-494d-9418-8c634fcb1005"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "High", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030393477529287338, "Height": 0.010660999454557896, "Left": 0.09642980992794037, "Top": 0.6185111403465271}, "Polygon": [{"X": 0.09643415361642838, "Y": 0.6185111403465271}, {"X": 0.126823291182518, "Y": 0.6185449957847595}, {"X": 0.1268189251422882, "Y": 0.629172146320343}, {"X": 0.09642980992794037, "Y": 0.6291382312774658}]}, "Id": "f5c9ca8f-51e4-4d8a-be42-10dc4cd3c7c3"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Risk", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027529921382665634, "Height": 0.008538225665688515, "Left": 0.13105864822864532, "Top": 0.6191741824150085}, "Polygon": [{"X": 0.13106213510036469, "Y": 0.6191741824150085}, {"X": 0.15858855843544006, "Y": 0.6192048788070679}, {"X": 0.1585850715637207, "Y": 0.6277123689651489}, {"X": 0.13105864822864532, "Y": 0.6276816725730896}]}, "Id": "d2c679a4-a675-4700-af81-4af2b3e6716a"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Destination", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07074423879384995, "Height": 0.008976924233138561, "Left": 0.1622028797864914, "Top": 0.6197272539138794}, "Polygon": [{"X": 0.16220654547214508, "Y": 0.6197272539138794}, {"X": 0.23294711112976074, "Y": 0.619806170463562}, {"X": 0.23294343054294586, "Y": 0.6287041902542114}, {"X": 0.1622028797864914, "Y": 0.6286252737045288}]}, "Id": "14ebd181-3c71-460e-b7ec-f81e6c2c6c0d"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030482811853289604, "Height": 0.008461292833089828, "Left": 0.23694472014904022, "Top": 0.6203759908676147}, "Polygon": [{"X": 0.23694820702075958, "Y": 0.6203759908676147}, {"X": 0.267427533864975, "Y": 0.6204100251197815}, {"X": 0.2674240469932556, "Y": 0.6288372874259949}, {"X": 0.23694472014904022, "Y": 0.6288032531738281}]}, "Id": "6c600335-6a75-4ad6-8a15-2bb2a33599d7"}, {"BlockType": "WORD", "Confidence": 99.23290252685547, "Text": "callback:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05392231047153473, "Height": 0.008665937930345535, "Left": 0.2711045742034912, "Top": 0.6207901239395142}, "Polygon": [{"X": 0.27110815048217773, "Y": 0.6207901239395142}, {"X": 0.32502689957618713, "Y": 0.6208502650260925}, {"X": 0.3250232934951782, "Y": 0.6294560432434082}, {"X": 0.2711045742034912, "Y": 0.6293959021568298}]}, "Id": "1ed6d89b-1582-4051-8708-8a0abebd1536"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "PURPOSE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06675336509943008, "Height": 0.008760428056120872, "Left": 0.09604675322771072, "Top": 0.6432508230209351}, "Polygon": [{"X": 0.09605030715465546, "Y": 0.6432508230209351}, {"X": 0.1628001183271408, "Y": 0.6433253288269043}, {"X": 0.16279654204845428, "Y": 0.6520112752914429}, {"X": 0.09604675322771072, "Y": 0.6519367694854736}]}, "Id": "b1511d0a-e989-4eeb-9801-ebea846b827a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "OF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019517123699188232, "Height": 0.00842767208814621, "Left": 0.1671220362186432, "Top": 0.6442732810974121}, "Polygon": [{"X": 0.16712549328804016, "Y": 0.6442732810974121}, {"X": 0.18663915991783142, "Y": 0.6442950367927551}, {"X": 0.18663568794727325, "Y": 0.6527009606361389}, {"X": 0.1671220362186432, "Y": 0.6526791453361511}]}, "Id": "23bc1ebc-15f1-47e1-a79d-ac22fb2164e4"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "WIRE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04243304207921028, "Height": 0.008796872571110725, "Left": 0.19061020016670227, "Top": 0.6444383859634399}, "Polygon": [{"X": 0.19061382114887238, "Y": 0.6444383859634399}, {"X": 0.23304325342178345, "Y": 0.6444857120513916}, {"X": 0.23303963243961334, "Y": 0.6532352566719055}, {"X": 0.19061020016670227, "Y": 0.6531878709793091}]}, "Id": "27dc72c7-2c56-458d-ac6c-6f8c3131a6a1"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "If", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011569865979254246, "Height": 0.00887797400355339, "Left": 0.7044114470481873, "Top": 0.6994679570198059}, "Polygon": [{"X": 0.7044152617454529, "Y": 0.6994679570198059}, {"X": 0.7159813046455383, "Y": 0.6994808912277222}, {"X": 0.7159774899482727, "Y": 0.7083459496498108}, {"X": 0.7044114470481873, "Y": 0.7083330750465393}]}, "Id": "e90209b3-1757-4ad6-9fdf-87597fa83808"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "funds", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034213997423648834, "Height": 0.008663739077746868, "Left": 0.7180585861206055, "Top": 0.6997331380844116}, "Polygon": [{"X": 0.7180623412132263, "Y": 0.6997331380844116}, {"X": 0.7522726058959961, "Y": 0.6997713446617126}, {"X": 0.7522688508033752, "Y": 0.708396852016449}, {"X": 0.7180585861206055, "Y": 0.7083587050437927}]}, "Id": "7ec3cef5-6732-4cd4-b171-711ee2752cdc"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018722306936979294, "Height": 0.0060327243991196156, "Left": 0.7558165192604065, "Top": 0.7022709846496582}, "Polygon": [{"X": 0.7558191418647766, "Y": 0.7022709846496582}, {"X": 0.7745388150215149, "Y": 0.7022918462753296}, {"X": 0.7745361924171448, "Y": 0.708303689956665}, {"X": 0.7558165192604065, "Y": 0.7082827687263489}]}, "Id": "c4ff1919-5849-44db-b75a-2d985e2dd605"}, {"BlockType": "WORD", "Confidence": 94.47006225585938, "Text": "uncollected,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07344549894332886, "Height": 0.00984619464725256, "Left": 0.7781521677970886, "Top": 0.7002888917922974}, "Polygon": [{"X": 0.7781563997268677, "Y": 0.7002888917922974}, {"X": 0.8515976667404175, "Y": 0.7003708481788635}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.7781521677970886, "Y": 0.7100531458854675}]}, "Id": "4dd55831-5134-4251-bead-4aa7974ddfea"}, {"BlockType": "WORD", "Confidence": 99.44097137451172, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00521520571783185, "Height": 0.00819044467061758, "Left": 0.09481660276651382, "Top": 0.7056586742401123}, "Polygon": [{"X": 0.09481994807720184, "Y": 0.7056586742401123}, {"X": 0.10003180801868439, "Y": 0.7056645154953003}, {"X": 0.10002846270799637, "Y": 0.7138491272926331}, {"X": 0.09481660276651382, "Y": 0.7138433456420898}]}, "Id": "e39b9a19-4d4c-492a-9129-819562dbf701"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04004564508795738, "Height": 0.010418243706226349, "Left": 0.10374236851930618, "Top": 0.7053948044776917}, "Polygon": [{"X": 0.10374661535024643, "Y": 0.7053948044776917}, {"X": 0.14378800988197327, "Y": 0.7054395079612732}, {"X": 0.14378374814987183, "Y": 0.7158130407333374}, {"X": 0.10374236851930618, "Y": 0.7157683372497559}]}, "Id": "d6029c30-31a4-4a43-aed8-af97a2c0ae30"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023754717782139778, "Height": 0.00814088061451912, "Left": 0.1474270522594452, "Top": 0.7063084244728088}, "Polygon": [{"X": 0.1474303901195526, "Y": 0.7063084244728088}, {"X": 0.17118176817893982, "Y": 0.7063349485397339}, {"X": 0.1711784303188324, "Y": 0.7144493460655212}, {"X": 0.1474270522594452, "Y": 0.7144228219985962}]}, "Id": "22dc04ab-1190-4791-a96a-8cfdc47ddb24"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "funds", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03389729931950569, "Height": 0.008913212455809116, "Left": 0.17454931139945984, "Top": 0.7059713006019592}, "Polygon": [{"X": 0.17455297708511353, "Y": 0.7059713006019592}, {"X": 0.20844660699367523, "Y": 0.7060091495513916}, {"X": 0.20844294130802155, "Y": 0.7148845195770264}, {"X": 0.17454931139945984, "Y": 0.714846670627594}]}, "Id": "ae2e2b8d-0943-410f-aa0d-49cb62af94c6"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01863669790327549, "Height": 0.006185909733176231, "Left": 0.21228155493736267, "Top": 0.7087076306343079}, "Polygon": [{"X": 0.21228410303592682, "Y": 0.7087076306343079}, {"X": 0.23091824352741241, "Y": 0.7087284326553345}, {"X": 0.23091569542884827, "Y": 0.7148935794830322}, {"X": 0.21228155493736267, "Y": 0.7148727774620056}]}, "Id": "6602802b-ef55-49b9-b324-2e8c38f19dd8"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "available", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05527544394135475, "Height": 0.008750727400183678, "Left": 0.23452059924602509, "Top": 0.7068696618080139}, "Polygon": [{"X": 0.2345241904258728, "Y": 0.7068696618080139}, {"X": 0.28979605436325073, "Y": 0.7069313526153564}, {"X": 0.28979241847991943, "Y": 0.7156203985214233}, {"X": 0.23452059924602509, "Y": 0.7155587077140808}]}, "Id": "d4c46285-f875-4502-9c03-e42ba671eff0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010943859815597534, "Height": 0.008538516238331795, "Left": 0.29382798075675964, "Top": 0.7075251936912537}, "Polygon": [{"X": 0.2938315272331238, "Y": 0.7075251936912537}, {"X": 0.3047718405723572, "Y": 0.7075374126434326}, {"X": 0.30476826429367065, "Y": 0.7160636782646179}, {"X": 0.29382798075675964, "Y": 0.716051459312439}]}, "Id": "1c51e32a-6316-4f93-a33e-ee7a50fec75f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018969474360346794, "Height": 0.008308863267302513, "Left": 0.3088098168373108, "Top": 0.7078701257705688}, "Polygon": [{"X": 0.30881327390670776, "Y": 0.7078701257705688}, {"X": 0.32777929306030273, "Y": 0.7078912854194641}, {"X": 0.3277758061885834, "Y": 0.7161790132522583}, {"X": 0.3088098168373108, "Y": 0.716157853603363}]}, "Id": "15d26e76-eb05-4c56-b03b-b8478160f06d"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "Customer's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008548413403332233, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "51b7cac8-ca07-4a9c-94da-9fc6534a7669"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "account", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.007150924298912287, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "006aa11d-e294-46d1-a0b2-b252bcaf7eb8"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022007975727319717, "Height": 0.008143007755279541, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.***************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "fa41ae0b-3eab-4f43-8702-321f954534ab"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "submit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041412100195884705, "Height": 0.008753577247262001, "Left": 0.4825078547000885, "Top": 0.7094569802284241}, "Polygon": [{"X": 0.4825115203857422, "Y": 0.7094569802284241}, {"X": 0.523919939994812, "Y": 0.709503173828125}, {"X": 0.5239162445068359, "Y": 0.7182105779647827}, {"X": 0.4825078547000885, "Y": 0.718164324760437}]}, "Id": "3bda2be4-6dcf-4263-80ed-51ffa10962ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022573446854948997, "Height": 0.008950973860919476, "Left": 0.5271785855293274, "Top": 0.7099260687828064}, "Polygon": [{"X": 0.5271823406219482, "Y": 0.7099260687828064}, {"X": 0.5497519969940186, "Y": 0.7099512815475464}, {"X": 0.5497481822967529, "Y": 0.7188770771026611}, {"X": 0.5271785855293274, "Y": 0.7188518643379211}]}, "Id": "a11c1784-cfad-44c1-ad74-ab2d50eb8124"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "transfer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04716324806213379, "Height": 0.008309641852974892, "Left": 0.5531561970710754, "Top": 0.7104987502098083}, "Polygon": [{"X": 0.5531597137451172, "Y": 0.7104987502098083}, {"X": 0.6003194451332092, "Y": 0.710551381111145}, {"X": 0.6003159284591675, "Y": 0.7188083529472351}, {"X": 0.5531561970710754, "Y": 0.7187557220458984}]}, "Id": "9e3452f9-7900-4c5e-a385-48412a2c54b8"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "request.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04697383940219879, "Height": 0.009308728389441967, "Left": 0.60347580909729, "Top": 0.7120869159698486}, "Polygon": [{"X": 0.6034797430038452, "Y": 0.7120869159698486}, {"X": 0.6504496335983276, "Y": 0.712139368057251}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.60347580909729, "Y": 0.7213432192802429}]}, "Id": "3c9a8b1d-ecba-4087-8c04-e0f90525bf82"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Officer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04413103684782982, "Height": 0.008695396594703197, "Left": 0.7039350271224976, "Top": 0.7120754718780518}, "Polygon": [{"X": 0.7039387822151184, "Y": 0.7120754718780518}, {"X": 0.7480660676956177, "Y": 0.7121247053146362}, {"X": 0.7480623126029968, "Y": 0.7207708954811096}, {"X": 0.7039350271224976, "Y": 0.7207216024398804}]}, "Id": "6c0f32d7-abc2-4267-8513-55039d6ec632"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "approval", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05316806957125664, "Height": 0.00997074507176876, "Left": 0.7517259120941162, "Top": 0.7128076553344727}, "Polygon": [{"X": 0.75173020362854, "Y": 0.7128076553344727}, {"X": 0.8048940300941467, "Y": 0.712867021560669}, {"X": 0.8048896789550781, "Y": 0.7227784395217896}, {"X": 0.7517259120941162, "Y": 0.7227190732955933}]}, "Id": "9c3bcb5a-dd8b-402c-845e-b4ef6578ac45"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "required", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05064272880554199, "Height": 0.010228030383586884, "Left": 0.8087039589881897, "Top": 0.7132492661476135}, "Polygon": [{"X": 0.8087084293365479, "Y": 0.7132492661476135}, {"X": 0.8593466877937317, "Y": 0.7133057713508606}, {"X": 0.8593422174453735, "Y": 0.723477303981781}, {"X": 0.8087039589881897, "Y": 0.7234207987785339}]}, "Id": "4521f972-845a-459c-8331-cf23c04ed219"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Employee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06252393871545792, "Height": 0.010687128640711308, "Left": 0.0942116230726242, "Top": 0.7422691583633423}, "Polygon": [{"X": 0.09421596676111221, "Y": 0.7422691583633423}, {"X": 0.15673556923866272, "Y": 0.7423388957977295}, {"X": 0.15673120319843292, "Y": 0.7529562711715698}, {"X": 0.0942116230726242, "Y": 0.7528864741325378}]}, "Id": "e6459c00-a289-494d-be21-f713936545be"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Completing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07203670591115952, "Height": 0.011629634536802769, "Left": 0.16053496301174164, "Top": 0.7427302002906799}, "Polygon": [{"X": 0.16053971648216248, "Y": 0.7427302002906799}, {"X": 0.23257166147232056, "Y": 0.7428106069564819}, {"X": 0.23256687819957733, "Y": 0.7543598413467407}, {"X": 0.16053496301174164, "Y": 0.7542794346809387}]}, "Id": "b80eb485-f03a-4f37-aaa1-7d0c8c2c0392"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Request", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04945949465036392, "Height": 0.010616947896778584, "Left": 0.23618629574775696, "Top": 0.7439965009689331}, "Polygon": [{"X": 0.23619067668914795, "Y": 0.7439965009689331}, {"X": 0.2856457829475403, "Y": 0.7440516948699951}, {"X": 0.2856414020061493, "Y": 0.75461345911026}, {"X": 0.23618629574775696, "Y": 0.754558265209198}]}, "Id": "bbaa84db-ba05-4d51-84ff-648d118eea22"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Phone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03840826079249382, "Height": 0.008556368760764599, "Left": 0.40328678488731384, "Top": 0.7456466555595398}, "Polygon": [{"X": 0.40329039096832275, "Y": 0.7456466555595398}, {"X": 0.44169506430625916, "Y": 0.7456895112991333}, {"X": 0.44169145822525024, "Y": 0.7542030215263367}, {"X": 0.40328678488731384, "Y": 0.7541601657867432}]}, "Id": "d42d713d-4764-4238-be0a-beb6e59fa934"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028867069631814957, "Height": 0.008462034165859222, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "cf67c079-826c-4ae5-aa23-4c4ecb7b40ce"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Authorized", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008776943199336529, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "5fd0ddcd-4602-4fa3-89fa-3b34eb6b614e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008618912659585476, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "c231e878-6df4-41aa-a0bf-1d706cecb2e3"}, {"BlockType": "WORD", "Confidence": 61.***************, "Text": "2ⁿᵈ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018048197031021118, "Height": 0.009441746398806572, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}]}, "Id": "ab3b32ad-ecdd-4a09-a953-8af2943d0d60"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Authorized", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008845738135278225, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.***************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "a166d250-1681-4f2b-9f62-6247daf76842"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Bank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.008410265669226646, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.***************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "2a887e31-98e8-4275-8794-27f41c6d3d7a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.010230370797216892, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*************}]}, "Id": "44b3ec42-9f8b-433b-9035-70f8a56ed9f3"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Required", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06024593859910965, "Height": 0.010089955292642117, "Left": 0.15677757561206818, "Top": 0.780443549156189}, "Polygon": [{"X": 0.15678170323371887, "Y": 0.780443549156189}, {"X": 0.21702352166175842, "Y": 0.7805107831954956}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.15677757561206818, "Y": 0.7904662489891052}]}, "Id": "db909e24-fca3-4d58-b03d-c6b96069ed58"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058173634111881256, "Height": 0.01056218147277832, "Left": 0.46796610951423645, "Top": 0.7830936312675476}, "Polygon": [{"X": 0.4679705500602722, "Y": 0.7830936312675476}, {"X": 0.5261397361755371, "Y": 0.783158540725708}, {"X": 0.526135265827179, "Y": 0.7936558127403259}, {"X": 0.46796610951423645, "Y": 0.7935908436775208}]}, "Id": "842224fd-abcc-4618-9bed-900168cda36f"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "If", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012564814649522305, "Height": 0.00847605336457491, "Left": 0.5299780964851379, "Top": 0.7840481400489807}, "Polygon": [{"X": 0.5299816727638245, "Y": 0.7840481400489807}, {"X": 0.5425428748130798, "Y": 0.7840622067451477}, {"X": 0.5425392985343933, "Y": 0.7925242185592651}, {"X": 0.5299780964851379, "Y": 0.7925102114677429}]}, "Id": "072a7aad-3abb-4361-bfa3-386cbe918a64"}, {"BlockType": "WORD", "Confidence": 99.**************, "Text": "Required:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0636778473854065, "Height": 0.010006037540733814, "Left": 0.5450352430343628, "Top": 0.7844783663749695}, "Polygon": [{"X": 0.5450394749641418, "Y": 0.7844783663749695}, {"X": 0.6087130904197693, "Y": 0.7845494151115417}, {"X": 0.6087087988853455, "Y": 0.7944843769073486}, {"X": 0.5450352430343628, "Y": 0.7944133281707764}]}, "Id": "ee90ef87-7dc1-4c03-ad9a-380656c11d60"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d954f2e7-2514-40c3-b738-5a0f6ffa2d9b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d954f2e7-2514-40c3-b738-5a0f6ffa2d9b", "content-type": "application/x-amz-json-1.1", "content-length": "140828", "date": "Wed, 24 Sep 2025 12:42:37 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c6045b34-1266-49cf-980f-5e6554cac265", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:42:40 GMT", "content-type": "application/json", "content-length": "918", "connection": "keep-alive", "x-amzn-requestid": "c6045b34-1266-49cf-980f-5e6554cac265"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only page1 provided. Determine doc_type. The page is an outgoing wire transfer request, likely a payment authorization. Not among defined categories. The categories include clear_to_pay which is for approval for payment or ACH/Wire/Wire transfer request or approval. So this is a wire transfer request. That matches clear_to_pay as definition: Approval for payment or ACH/Wire/Check transfer request or approval. So doc_type likely clear_to_pay. We should return an array with one object: {page_no:1, doc_type:\"clear_to_pay\"}. Use tool classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"clear_to_pay\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2884, "outputTokens": 146, "totalTokens": 3030}, "metrics": {"latencyMs": 814}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/996fd583_ILOJODH5ADT10MHGK0TI.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nHTLF Bank\nOutgoing Wire Transfer Request\nDate: 02/07/2025 11:12:43\nWire Transfer Message Reference Number:\n2025038000120600\nWIRE TO:\nBank ABA No:\n*********\nBank Name:\nBK AMER NYC\nAmount:\n750.00\nSpecial Instructions (BBI):\nBENEFICIARY BANK:\nBank ABA No:\nBank Name:\nCREDIT TO:\nName:\nWOODLANDS TRANSPORT, LLC\nAddress:\n2700 TECHNOLOGY\nFOREST BLVD STE 250\nTHE WOODLANDS, TX 77381-3909\nAccount Number:\n************\nSpecial Instructions (OBI):\nCHARGE TO:\nName:\nTEXAS CONTRACT ROUSTABOUT LLC\nAddress:\n2906 DENISON AVE\nAddress:\nSNYDER TX 79549\nAcct. No. to be Debited: **********\nI hereby authorize the bank to execute the wire transfer of funds according to my instructions stated above and to charge my\naccount or collect cash payment for the amount of the wire transfer and all applicable fees. I understand the bank handles wire\ntransfer requests expeditiously, but that the bank cannot guarantee this will be completed in any specific time period.\nCUSTOMER SIGNATURE (required)\nPhone\nDate\nTime\nCallback\nPhone # called:\nTime of call:\nSource of #:\nVerified with:\nCallback made by:\nHigh Risk Destination Wire callback:\nPURPOSE OF WIRE:\nIf funds are uncollected,\nI certify that funds are available in the Customer's account and submit this transfer request.\nOfficer approval required\nEmployee Completing Request\nPhone\nDate\nAuthorized Bank\n2ⁿᵈ Authorized Bank\nSignature Required\nSignature If Required:\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}