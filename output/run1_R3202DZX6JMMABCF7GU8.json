{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.40972021222114563, "Height": 0.9062283635139465, "Left": 0.27433934807777405, "Top": 0.057905592024326324}, "Polygon": [{"X": 0.2854083180427551, "Y": 0.057905592024326324}, {"X": 0.6294946670532227, "Y": 0.061070170253515244}, {"X": 0.6840595602989197, "Y": 0.9557186961174011}, {"X": 0.27433934807777405, "Y": 0.9641339778900146}]}, "Id": "132a90a4-9128-49fd-81c1-42b7ee5d678f", "Relationships": [{"Type": "CHILD", "Ids": ["81be6048-e11c-4f35-8f97-54b500811f95", "71b04cc4-929b-4fee-9ee5-20f45499b9b0", "c6deeafc-7726-458d-96cb-6ec6d60e8601", "439c3e5c-7cbc-4525-adfd-21cb2a7f4a0c", "141ff13a-5913-4d45-bd8a-00c631ca9b72", "c006e791-ff04-457a-bb0a-33f2750b43e7", "2c29eb7e-2670-4e00-bc6f-61fc62b9d401", "00ebd864-637a-4840-9a7d-0ba8a07f7c0e", "89e4693b-701c-460b-8e93-2c080d852e9c", "0be092ac-379c-4a52-8065-e28e30994a12", "bc110429-c4a7-4a2b-b4d6-629a9fa1d3fa", "6a44c022-6d61-4670-884c-78d097db0dc5", "90643aee-0af2-459c-97d6-e336d477718c", "abad1ea8-184e-40ea-886b-0aaf86f5c1ca", "a9788830-26d5-4d34-b8a8-273396df8bc9", "8e83f3ab-9aea-48ba-b4cd-f05475e30064", "d6a195aa-9f80-4257-89a0-e9f99da1ec5b", "8d933b77-658e-4be6-9679-65812f118371", "46ee4d39-679c-435d-ba83-be81f8269703", "0578e183-a9c6-40e6-8bee-9f93161f2012", "9d1576b2-3ae6-4fe3-b520-5a39b1318130", "0ba83a8b-0f38-4420-8b49-fc09761a88dd", "7ea78aa8-76a4-4be1-90fc-0721314900b5", "d133a8b7-90e6-48ec-9174-39beb54d0551", "288abc8e-d6b4-478c-80b8-569610bdcaeb", "b310a881-3bfb-45ef-9622-adc78e81add6", "7fcf1e4f-3fb6-416f-ae30-5f7695d89f9c", "48f419e0-64e3-4ad4-bfdd-82df45003ec0", "04bca3c1-9618-453d-99c0-c32967536644", "7ec30030-8efe-4114-a1e1-de88f3861b17", "ef44778d-66c7-4563-9d0c-2d375b1f8be7", "160cfc4e-cda5-40ab-98ad-1213716fdd30", "5f8e261c-cb59-4b6a-9c86-95a60c716688", "bd6cf360-29ec-40f4-b11c-de103e8d5de7", "f839ea9b-d4bf-4f15-9c88-621d6bdd6837"]}]}, {"BlockType": "LINE", "Confidence": 99.96392822265625, "Text": "EXXON EXPRESS PAY", "Geometry": {"BoundingBox": {"Width": 0.19592967629432678, "Height": 0.0136892544105649, "Left": 0.35224464535713196, "Top": 0.11890909820795059}, "Polygon": [{"X": 0.35224464535713196, "Y": 0.11890909820795059}, {"X": 0.5476458072662354, "Y": 0.12031807005405426}, {"X": 0.5481743216514587, "Y": 0.13259835541248322}, {"X": 0.3522692024707794, "Y": 0.13126537203788757}]}, "Id": "81be6048-e11c-4f35-8f97-54b500811f95", "Relationships": [{"Type": "CHILD", "Ids": ["f0a2ab29-2e0f-4f13-8e47-3454346601a1", "36de24c0-09ad-4594-851b-14b265215911", "5de63ed9-7d06-4b34-943b-52a91f44ccd6"]}]}, {"BlockType": "LINE", "Confidence": 99.98759460449219, "Text": "580 LONE STAR DRIVE", "Geometry": {"BoundingBox": {"Width": 0.2199183702468872, "Height": 0.014958161860704422, "Left": 0.34069108963012695, "Top": 0.14957499504089355}, "Polygon": [{"X": 0.3406970500946045, "Y": 0.14957499504089355}, {"X": 0.5599929094314575, "Y": 0.15093454718589783}, {"X": 0.5606094598770142, "Y": 0.16453316807746887}, {"X": 0.34069108963012695, "Y": 0.1632687747478485}]}, "Id": "71b04cc4-929b-4fee-9ee5-20f45499b9b0", "Relationships": [{"Type": "CHILD", "Ids": ["0f5c3821-bb84-4d58-9a7b-6b9075ee60d0", "40f529cf-9d02-4682-959d-a10da4386e09", "4d759be5-d5c2-495b-b274-da924a5ffcc7", "57a10fdf-7236-40b9-8b6a-0598ed5d6eb8"]}]}, {"BlockType": "LINE", "Confidence": 99.9378890991211, "Text": "PARADISE QUICKSTOP #", "Geometry": {"BoundingBox": {"Width": 0.23329652845859528, "Height": 0.015428060665726662, "Left": 0.34091442823410034, "Top": 0.16489063203334808}, "Polygon": [{"X": 0.34091994166374207, "Y": 0.16489063203334808}, {"X": 0.573533833026886, "Y": 0.16621561348438263}, {"X": 0.5742109417915344, "Y": 0.180318683385849}, {"X": 0.34091442823410034, "Y": 0.17909881472587585}]}, "Id": "c6deeafc-7726-458d-96cb-6ec6d60e8601", "Relationships": [{"Type": "CHILD", "Ids": ["7047b06b-c45c-42e9-b756-2c97c71f76e2", "88756949-7099-431f-9891-5b3a7dc08565", "4f2d741f-2a78-4c52-a1a7-47dbbdf1544a"]}]}, {"BlockType": "LINE", "Confidence": 96.7322998046875, "Text": "XXXXXXXXX0001", "Geometry": {"BoundingBox": {"Width": 0.14988823235034943, "Height": 0.013753502629697323, "Left": 0.34017515182495117, "Top": 0.1823374480009079}, "Polygon": [{"X": 0.34018221497535706, "Y": 0.1823374480009079}, {"X": 0.4896678626537323, "Y": 0.18310314416885376}, {"X": 0.4900633990764618, "Y": 0.1960909515619278}, {"X": 0.34017515182495117, "Y": 0.19538749754428864}]}, "Id": "439c3e5c-7cbc-4525-adfd-21cb2a7f4a0c", "Relationships": [{"Type": "CHILD", "Ids": ["ce46510d-a9e4-4d9d-a558-f2c234ed385f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "HOOKS", "Geometry": {"BoundingBox": {"Width": 0.05814799666404724, "Height": 0.01160817313939333, "Left": 0.34088459610939026, "Top": 0.2001017928123474}, "Polygon": [{"X": 0.3408890664577484, "Y": 0.2001017928123474}, {"X": 0.3989010751247406, "Y": 0.2003650665283203}, {"X": 0.3990325927734375, "Y": 0.21170996129512787}, {"X": 0.34088459610939026, "Y": 0.21146780252456665}]}, "Id": "141ff13a-5913-4d45-bd8a-00c631ca9b72", "Relationships": [{"Type": "CHILD", "Ids": ["2069570a-bfcb-438b-82a9-d81176ae7c8b"]}]}, {"BlockType": "LINE", "Confidence": 98.2800521850586, "Text": ", TX", "Geometry": {"BoundingBox": {"Width": 0.043919797986745834, "Height": 0.014618240296840668, "Left": 0.48427653312683105, "Top": 0.19828884303569794}, "Polygon": [{"X": 0.48427653312683105, "Y": 0.19828884303569794}, {"X": 0.527645468711853, "Y": 0.1984892040491104}, {"X": 0.5281963348388672, "Y": 0.2129070907831192}, {"X": 0.4846987724304199, "Y": 0.212726891040802}]}, "Id": "c006e791-ff04-457a-bb0a-33f2750b43e7", "Relationships": [{"Type": "CHILD", "Ids": ["55bda3bd-cf5a-4e43-a758-5b75afb5da2d", "343e40b7-f29a-40aa-86e5-8288d748aa3b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "75561", "Geometry": {"BoundingBox": {"Width": 0.05529950186610222, "Height": 0.011499155312776566, "Left": 0.34109926223754883, "Top": 0.21658317744731903}, "Polygon": [{"X": 0.341103196144104, "Y": 0.21658317744731903}, {"X": 0.3962745666503906, "Y": 0.2168036550283432}, {"X": 0.39639878273010254, "Y": 0.2280823290348053}, {"X": 0.34109926223754883, "Y": 0.22788187861442566}]}, "Id": "2c29eb7e-2670-4e00-bc6f-61fc62b9d401", "Relationships": [{"Type": "CHILD", "Ids": ["734cf51d-f109-49ea-bfc2-0d1056eb5ffe"]}]}, {"BlockType": "LINE", "Confidence": 99.86248779296875, "Text": "05/13/2025 590412430", "Geometry": {"BoundingBox": {"Width": 0.23583224415779114, "Height": 0.018295034766197205, "Left": 0.34093961119651794, "Top": 0.22842618823051453}, "Polygon": [{"X": 0.3409462571144104, "Y": 0.22842618823051453}, {"X": 0.5759363770484924, "Y": 0.22927378118038177}, {"X": 0.5767718553543091, "Y": 0.24672122299671173}, {"X": 0.34093961119651794, "Y": 0.24600692093372345}]}, "Id": "00ebd864-637a-4840-9a7d-0ba8a07f7c0e", "Relationships": [{"Type": "CHILD", "Ids": ["55002aac-4a3b-45b2-b5cd-26af58a92282", "4b3addd2-15f4-4b39-a66e-36f751344214"]}]}, {"BlockType": "LINE", "Confidence": 99.76971435546875, "Text": "08:56:09 AM", "Geometry": {"BoundingBox": {"Width": 0.12962239980697632, "Height": 0.014085020869970322, "Left": 0.34087786078453064, "Top": 0.24708795547485352}, "Polygon": [{"X": 0.3408832252025604, "Y": 0.24708795547485352}, {"X": 0.47014328837394714, "Y": 0.24747487902641296}, {"X": 0.47050026059150696, "Y": 0.26117298007011414}, {"X": 0.34087786078453064, "Y": 0.2608436048030853}]}, "Id": "89e4693b-701c-460b-8e93-2c080d852e9c", "Relationships": [{"Type": "CHILD", "Ids": ["636bb634-ebdf-46a4-9f42-3e8e13f82836", "6eea690f-046d-4269-90f6-c699f262b50b"]}]}, {"BlockType": "LINE", "Confidence": 75.34120178222656, "Text": "XXXXXXXXXXXXXXX8412", "Geometry": {"BoundingBox": {"Width": 0.2270612269639969, "Height": 0.0158492811024189, "Left": 0.3399864435195923, "Top": 0.2779603898525238}, "Polygon": [{"X": 0.33999523520469666, "Y": 0.2779603898525238}, {"X": 0.5663475394248962, "Y": 0.27840813994407654}, {"X": 0.567047655582428, "Y": 0.2938096523284912}, {"X": 0.3399864435195923, "Y": 0.2934763431549072}]}, "Id": "0be092ac-379c-4a52-8065-e28e30994a12", "Relationships": [{"Type": "CHILD", "Ids": ["c8b8833b-30e0-4052-af9c-3066f2f530fa"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "TCH Fuel", "Geometry": {"BoundingBox": {"Width": 0.09620760381221771, "Height": 0.012651937082409859, "Left": 0.3389332890510559, "Top": 0.29757392406463623}, "Polygon": [{"X": 0.33894309401512146, "Y": 0.29757392406463623}, {"X": 0.4349072277545929, "Y": 0.2977018654346466}, {"X": 0.4351409077644348, "Y": 0.31022587418556213}, {"X": 0.3389332890510559, "Y": 0.3101373612880707}]}, "Id": "bc110429-c4a7-4a2b-b4d6-629a9fa1d3fa", "Relationships": [{"Type": "CHILD", "Ids": ["c6c9ba33-3992-4712-90eb-d9512e837e73", "b82a1dbc-720f-4daf-a11a-4ca3e6508196"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "INVOICE 051693", "Geometry": {"BoundingBox": {"Width": 0.16597290337085724, "Height": 0.014313063584268093, "Left": 0.3418831527233124, "Top": 0.31263312697410583}, "Polygon": [{"X": 0.3418857455253601, "Y": 0.31263312697410583}, {"X": 0.5073849558830261, "Y": 0.31277182698249817}, {"X": 0.5078560709953308, "Y": 0.3269461691379547}, {"X": 0.3418831527233124, "Y": 0.3268848657608032}]}, "Id": "6a44c022-6d61-4670-884c-78d097db0dc5", "Relationships": [{"Type": "CHILD", "Ids": ["173caffe-fc42-4939-9ae7-23b8c66b0c5e", "a2e49631-9671-4f36-b702-8aee1d0fdaa0"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "AUTH 210836", "Geometry": {"BoundingBox": {"Width": 0.13214069604873657, "Height": 0.013407156802713871, "Left": 0.340076208114624, "Top": 0.33064645528793335}, "Polygon": [{"X": 0.340083509683609, "Y": 0.33064645528793335}, {"X": 0.4718702435493469, "Y": 0.3306788504123688}, {"X": 0.472216933965683, "Y": 0.3440278172492981}, {"X": 0.340076208114624, "Y": 0.34405359625816345}]}, "Id": "90643aee-0af2-459c-97d6-e336d477718c", "Relationships": [{"Type": "CHILD", "Ids": ["312fd296-bab0-4d6a-8ed1-1fc81c4541f9", "5828bb7b-27f9-45a9-9b85-6feb9ebe2e90"]}]}, {"BlockType": "LINE", "Confidence": 99.9157943725586, "Text": "PUMP# 13", "Geometry": {"BoundingBox": {"Width": 0.09633312374353409, "Height": 0.013401183299720287, "Left": 0.3400532603263855, "Top": 0.36551135778427124}, "Polygon": [{"X": 0.3400605022907257, "Y": 0.3655981719493866}, {"X": 0.4361388683319092, "Y": 0.36551135778427124}, {"X": 0.436386376619339, "Y": 0.37878334522247314}, {"X": 0.3400532603263855, "Y": 0.3789125680923462}]}, "Id": "abad1ea8-184e-40ea-886b-0aaf86f5c1ca", "Relationships": [{"Type": "CHILD", "Ids": ["4895a74f-b17e-4709-9a4d-c6fd3dd434fd", "ff345d99-1f3b-4b98-8f5c-6b5911c13f44"]}]}, {"BlockType": "LINE", "Confidence": 99.6583023071289, "Text": "Diesel 1", "Geometry": {"BoundingBox": {"Width": 0.09432636201381683, "Height": 0.012914023362100124, "Left": 0.3398655354976654, "Top": 0.4005368947982788}, "Polygon": [{"X": 0.33987289667129517, "Y": 0.40073058009147644}, {"X": 0.43396252393722534, "Y": 0.4005368947982788}, {"X": 0.43419191241264343, "Y": 0.4132172763347626}, {"X": 0.3398655354976654, "Y": 0.4134509265422821}]}, "Id": "a9788830-26d5-4d34-b8a8-273396df8bc9", "Relationships": [{"Type": "CHILD", "Ids": ["4fd68d61-599c-42ce-9079-1b6df49bbbd9", "b996c521-4528-4f5d-8f24-e40219c1c7ed"]}]}, {"BlockType": "LINE", "Confidence": 99.4345932006836, "Text": "24.616G", "Geometry": {"BoundingBox": {"Width": 0.0856003537774086, "Height": 0.013262216001749039, "Left": 0.4983149468898773, "Top": 0.39768263697624207}, "Polygon": [{"X": 0.4983149468898773, "Y": 0.3978503942489624}, {"X": 0.5832918882369995, "Y": 0.39768263697624207}, {"X": 0.5839152932167053, "Y": 0.41073980927467346}, {"X": 0.4987192153930664, "Y": 0.4109448492527008}]}, "Id": "8e83f3ab-9aea-48ba-b4cd-f05475e30064", "Relationships": [{"Type": "CHILD", "Ids": ["aab5e8d9-bb64-4f54-a5d8-b1c0e71e4be7"]}]}, {"BlockType": "LINE", "Confidence": 95.77965545654297, "Text": "PRICE/GAL", "Geometry": {"BoundingBox": {"Width": 0.10867486149072647, "Height": 0.013879196718335152, "Left": 0.340284526348114, "Top": 0.4183305501937866}, "Polygon": [{"X": 0.34029123187065125, "Y": 0.4186173975467682}, {"X": 0.44867587089538574, "Y": 0.4183305501937866}, {"X": 0.4489594101905823, "Y": 0.43187353014945984}, {"X": 0.340284526348114, "Y": 0.43220973014831543}]}, "Id": "d6a195aa-9f80-4257-89a0-e9f99da1ec5b", "Relationships": [{"Type": "CHILD", "Ids": ["053be3bf-5c4f-4287-a9e0-db1cb4e44668"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "$3.399", "Geometry": {"BoundingBox": {"Width": 0.07294952869415283, "Height": 0.014125720597803593, "Left": 0.5112459063529968, "Top": 0.4151552617549896}, "Polygon": [{"X": 0.5112459063529968, "Y": 0.4153398275375366}, {"X": 0.5835330486297607, "Y": 0.4151552617549896}, {"X": 0.5841954350471497, "Y": 0.42906248569488525}, {"X": 0.5117104649543762, "Y": 0.42928096652030945}]}, "Id": "8d933b77-658e-4be6-9679-65812f118371", "Relationships": [{"Type": "CHILD", "Ids": ["ec7905df-656c-433a-96aa-f920813b598f"]}]}, {"BlockType": "LINE", "Confidence": 99.5458984375, "Text": "FUEL TOTAL $ 83.67", "Geometry": {"BoundingBox": {"Width": 0.2466721087694168, "Height": 0.015452401712536812, "Left": 0.33888500928878784, "Top": 0.45088598132133484}, "Polygon": [{"X": 0.33889612555503845, "Y": 0.45180538296699524}, {"X": 0.5848717093467712, "Y": 0.45088598132133484}, {"X": 0.5855571031570435, "Y": 0.46529850363731384}, {"X": 0.33888500928878784, "Y": 0.4663383960723877}]}, "Id": "46ee4d39-679c-435d-ba83-be81f8269703", "Relationships": [{"Type": "CHILD", "Ids": ["1f09d826-b470-4583-8e0d-451eda6a481d", "e6845c2a-87ab-4113-8ac5-f8fdd7a3a48e", "50318da9-0c15-487d-aaf8-1455f3e54df0", "5e4200b7-c83c-4d30-a1ce-ce6ea0dc356a"]}]}, {"BlockType": "LINE", "Confidence": 99.46717834472656, "Text": "TOTAL = $ 83.67", "Geometry": {"BoundingBox": {"Width": 0.20112298429012299, "Height": 0.015012252144515514, "Left": 0.38693174719810486, "Top": 0.5041924715042114}, "Polygon": [{"X": 0.38693174719810486, "Y": 0.5052961111068726}, {"X": 0.5873978734016418, "Y": 0.5041924715042114}, {"X": 0.5880547761917114, "Y": 0.****************}, {"X": 0.3870511054992676, "Y": 0.****************}]}, "Id": "0578e183-a9c6-40e6-8bee-9f93161f2012", "Relationships": [{"Type": "CHILD", "Ids": ["7a6b3b13-eb4a-4d5c-a4d4-325721622373", "7e8b5848-30ca-490b-82c9-5aaf23d72176", "1161d8ce-2789-43e8-b7b3-af771a54d861", "45d0d768-e635-4c51-a680-e9079fdabe39"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "CREDIT", "Geometry": {"BoundingBox": {"Width": 0.07523081451654434, "Height": 0.013127401471138, "Left": 0.3370814025402069, "Top": 0.543509304523468}, "Polygon": [{"X": 0.3370952606201172, "Y": 0.****************}, {"X": 0.****************, "Y": 0.543509304523468}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.3370814025402069, "Y": 0.****************}]}, "Id": "9d1576b2-3ae6-4fe3-b520-5a39b1318130", "Relationships": [{"Type": "CHILD", "Ids": ["ae88eefc-670c-4018-ad49-24bec3ac68a1"]}]}, {"BlockType": "LINE", "Confidence": 97.734375, "Text": "$ 83.67", "Geometry": {"BoundingBox": {"Width": 0.1006607785820961, "Height": 0.01505692582577467, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.589262068271637, "Y": 0.****************}, {"X": 0.5899443030357361, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "0ba83a8b-0f38-4420-8b49-fc09761a88dd", "Relationships": [{"Type": "CHILD", "Ids": ["ac5e12ee-5faf-4e42-b7cf-abe5bd20b63c", "cda5a3d0-156e-40a8-a57c-73945eec9289"]}]}, {"BlockType": "LINE", "Confidence": 99.05462646484375, "Text": "Customer-activated Purchase/Capture", "Geometry": {"BoundingBox": {"Width": 0.22737643122673035, "Height": 0.018164247274398804, "Left": 0.33430033922195435, "Top": 0.5969215631484985}, "Polygon": [{"X": 0.33432653546333313, "Y": 0.5988645553588867}, {"X": 0.5610061287879944, "Y": 0.5969215631484985}, {"X": 0.5616768002510071, "Y": 0.6130155324935913}, {"X": 0.33430033922195435, "Y": 0.615085780620575}]}, "Id": "7ea78aa8-76a4-4be1-90fc-0721314900b5", "Relationships": [{"Type": "CHILD", "Ids": ["0aad3800-a809-4b9a-a82e-3d1b9c3c81ae", "5d11ae27-6cd5-4b34-8478-27aab408e7c3"]}]}, {"BlockType": "LINE", "Confidence": 77.84571838378906, "Text": "Site #: 0000008004816757", "Geometry": {"BoundingBox": {"Width": 0.15646947920322418, "Height": 0.01525123044848442, "Left": 0.3343777656555176, "Top": 0.6180891990661621}, "Polygon": [{"X": 0.3343997895717621, "Y": 0.6195329427719116}, {"X": 0.490461528301239, "Y": 0.6180891990661621}, {"X": 0.49084722995758057, "Y": 0.6318219304084778}, {"X": 0.3343777656555176, "Y": 0.6333404779434204}]}, "Id": "d133a8b7-90e6-48ec-9174-39beb54d0551", "Relationships": [{"Type": "CHILD", "Ids": ["e8caab98-0298-43b0-8184-64e67d26cab5", "e7065cb6-e80e-4269-9e79-b11993a16d44", "c2094104-415c-46ce-95dd-bd2488e6caf8"]}]}, {"BlockType": "LINE", "Confidence": 99.80229949951172, "Text": "Shift Number 1", "Geometry": {"BoundingBox": {"Width": 0.09120272099971771, "Height": 0.015171535313129425, "Left": 0.3341400921344757, "Top": 0.6381615400314331}, "Polygon": [{"X": 0.3341633975505829, "Y": 0.6390613913536072}, {"X": 0.42512089014053345, "Y": 0.6381615400314331}, {"X": 0.4253427982330322, "Y": 0.6523879766464233}, {"X": 0.3341400921344757, "Y": 0.6533330678939819}]}, "Id": "288abc8e-d6b4-478c-80b8-569610bdcaeb", "Relationships": [{"Type": "CHILD", "Ids": ["ba68de39-75ef-494a-affa-b9c6b808820b", "763317cb-f3cb-4e6e-aca4-b0d5a919b573", "76aad1e5-f758-4b2b-91c7-8f3e796800fc"]}]}, {"BlockType": "LINE", "Confidence": 99.86713409423828, "Text": "Sequence Number 58096", "Geometry": {"BoundingBox": {"Width": 0.13810336589813232, "Height": 0.018485039472579956, "Left": 0.33442243933677673, "Top": 0.6570566892623901}, "Polygon": [{"X": 0.33444923162460327, "Y": 0.6585065722465515}, {"X": 0.47211188077926636, "Y": 0.6570566892623901}, {"X": 0.47252580523490906, "Y": 0.6740097999572754}, {"X": 0.33442243933677673, "Y": 0.6755416989326477}]}, "Id": "b310a881-3bfb-45ef-9622-adc78e81add6", "Relationships": [{"Type": "CHILD", "Ids": ["c9383ce4-ed21-4c0b-9a37-c39067590482", "99441338-3dd9-4309-823d-1fd8ff174067", "d39e6b72-b69f-422b-bcf3-419cbcf5476c"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Swiped", "Geometry": {"BoundingBox": {"Width": 0.04065830633044243, "Height": 0.01744830794632435, "Left": 0.33436277508735657, "Top": 0.678260326385498}, "Polygon": [{"X": 0.3343895971775055, "Y": 0.6787141561508179}, {"X": 0.37491855025291443, "Y": 0.678260326385498}, {"X": 0.3750210702419281, "Y": 0.6952307224273682}, {"X": 0.33436277508735657, "Y": 0.6957086324691772}]}, "Id": "7fcf1e4f-3fb6-416f-ae30-5f7695d89f9c", "Relationships": [{"Type": "CHILD", "Ids": ["faba676b-9bab-4ae8-be15-bf9000ffd6ba"]}]}, {"BlockType": "LINE", "Confidence": 85.84492492675781, "Text": "APPROVED 210836", "Geometry": {"BoundingBox": {"Width": 0.10639365017414093, "Height": 0.01574724353849888, "Left": 0.3344919979572296, "Top": 0.696584165096283}, "Polygon": [{"X": 0.33451446890830994, "Y": 0.6978389024734497}, {"X": 0.44062095880508423, "Y": 0.696584165096283}, {"X": 0.44088566303253174, "Y": 0.7110225558280945}, {"X": 0.3344919979572296, "Y": 0.7123313546180725}]}, "Id": "48f419e0-64e3-4ad4-bfdd-82df45003ec0", "Relationships": [{"Type": "CHILD", "Ids": ["3a327468-ff92-48b3-b7a2-8769d7819356", "e3638c3d-985e-4afb-be55-d74f4bb69a7a"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Customer Information", "Geometry": {"BoundingBox": {"Width": 0.13265672326087952, "Height": 0.01622610352933407, "Left": 0.3346213400363922, "Top": 0.716018557548523}, "Polygon": [{"X": 0.33464351296424866, "Y": 0.7176693081855774}, {"X": 0.466941773891449, "Y": 0.716018557548523}, {"X": 0.46727806329727173, "Y": 0.7305259108543396}, {"X": 0.3346213400363922, "Y": 0.7322446703910828}]}, "Id": "04bca3c1-9618-453d-99c0-c32967536644", "Relationships": [{"Type": "CHILD", "Ids": ["3a727c3c-9e23-4ad1-846b-6f15ebfeb8ba", "7707a82a-f0b7-47e5-bb75-85667da4485b"]}]}, {"BlockType": "LINE", "Confidence": 90.90547943115234, "Text": "Name: HP DISTRIBUTION", "Geometry": {"BoundingBox": {"Width": 0.14584209024906158, "Height": 0.016375910490751266, "Left": 0.33483412861824036, "Top": 0.7362344861030579}, "Polygon": [{"X": 0.33485546708106995, "Y": 0.7381472587585449}, {"X": 0.4803081750869751, "Y": 0.7362344861030579}, {"X": 0.48067623376846313, "Y": 0.7506231069564819}, {"X": 0.33483412861824036, "Y": 0.7526103854179382}]}, "Id": "7ec30030-8efe-4114-a1e1-de88f3861b17", "Relationships": [{"Type": "CHILD", "Ids": ["36973fcc-8f74-464d-9a3a-141b97e9be54", "6573b56c-c899-4cf5-abd1-ba8847e5409f", "21292b9d-0180-40e9-a5c4-3756d39407bb"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "City: OLATHE", "Geometry": {"BoundingBox": {"Width": 0.08076298236846924, "Height": 0.019381089136004448, "Left": 0.3347426652908325, "Top": 0.7571379542350769}, "Polygon": [{"X": 0.3347697854042053, "Y": 0.7582496404647827}, {"X": 0.41526100039482117, "Y": 0.7571379542350769}, {"X": 0.41550564765930176, "Y": 0.7753550410270691}, {"X": 0.3347426652908325, "Y": 0.7765190005302429}]}, "Id": "ef44778d-66c7-4563-9d0c-2d375b1f8be7", "Relationships": [{"Type": "CHILD", "Ids": ["d43897e2-8e84-4f79-a439-96e63e4f407b", "5e1b7588-0b8b-4f08-b2e3-bd3fc4e259f5"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "State: KS", "Geometry": {"BoundingBox": {"Width": 0.06116371974349022, "Height": 0.016178609803318977, "Left": 0.33465874195098877, "Top": 0.7777979373931885}, "Polygon": [{"X": 0.3346816301345825, "Y": 0.7786812782287598}, {"X": 0.3956734836101532, "Y": 0.7777979373931885}, {"X": 0.3958224654197693, "Y": 0.7930599451065063}, {"X": 0.33465874195098877, "Y": 0.7939765453338623}]}, "Id": "160cfc4e-cda5-40ab-98ad-1213716fdd30", "Relationships": [{"Type": "CHILD", "Ids": ["84123c35-466a-4e79-b480-6f48a1d02bd8", "82cfd20b-dda9-4087-ad21-55c8a4b74f6d"]}]}, {"BlockType": "LINE", "Confidence": 99.01974487304688, "Text": "Acc.: 132643", "Geometry": {"BoundingBox": {"Width": 0.08208019286394119, "Height": 0.016688603907823563, "Left": 0.3342275619506836, "Top": 0.7981279492378235}, "Polygon": [{"X": 0.3342517912387848, "Y": 0.7993690371513367}, {"X": 0.4161001145839691, "Y": 0.7981279492378235}, {"X": 0.4163077473640442, "Y": 0.8135303258895874}, {"X": 0.3342275619506836, "Y": 0.8148165941238403}]}, "Id": "5f8e261c-cb59-4b6a-9c86-95a60c716688", "Relationships": [{"Type": "CHILD", "Ids": ["a6545191-3e54-4cd8-8a17-53891f53c7c7", "c4793a4e-1c89-4391-b615-5ee3f0f88779"]}]}, {"BlockType": "LINE", "Confidence": 99.37728881835938, "Text": "Odometer: 274352", "Geometry": {"BoundingBox": {"Width": 0.10906081646680832, "Height": 0.018117502331733704, "Left": 0.3341817855834961, "Top": 0.8178001046180725}, "Polygon": [{"X": 0.33420756459236145, "Y": 0.819520890712738}, {"X": 0.44294288754463196, "Y": 0.8178001046180725}, {"X": 0.443242609500885, "Y": 0.8341329097747803}, {"X": 0.3341817855834961, "Y": 0.835917592048645}]}, "Id": "bd6cf360-29ec-40f4-b11c-de103e8d5de7", "Relationships": [{"Type": "CHILD", "Ids": ["0dc65896-7140-45d6-bb5a-ba987cf2a649", "babdeec0-bbf8-4b6f-a4b3-0e919b21ecae"]}]}, {"BlockType": "LINE", "Confidence": 99.30497741699219, "Text": "Trip #: 4408", "Geometry": {"BoundingBox": {"Width": 0.08312000334262848, "Height": 0.02016131393611431, "Left": 0.3340495526790619, "Top": 0.8402074575424194}, "Polygon": [{"X": 0.33407941460609436, "Y": 0.841578483581543}, {"X": 0.4169161319732666, "Y": 0.8402074575424194}, {"X": 0.41716957092285156, "Y": 0.8589417338371277}, {"X": 0.3340495526790619, "Y": 0.8603687882423401}]}, "Id": "f839ea9b-d4bf-4f15-9c88-621d6bdd6837", "Relationships": [{"Type": "CHILD", "Ids": ["ff65fb82-b7ac-4fa4-bfef-3a6d1d407175", "60c9efc4-52b4-45bc-b051-e6483ddb7c88", "a9e51ac1-c571-4106-88df-956e5fcdb146"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "EXXON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05786612257361412, "Height": 0.011695860885083675, "Left": 0.35224661231040955, "Top": 0.11989569664001465}, "Polygon": [{"X": 0.35224661231040955, "Y": 0.11989569664001465}, {"X": 0.4099535346031189, "Y": 0.12030992656946182}, {"X": 0.41011273860931396, "Y": 0.1315915584564209}, {"X": 0.3522690534591675, "Y": 0.13119785487651825}]}, "Id": "f0a2ab29-2e0f-4f13-8e47-3454346601a1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "EXPRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08072920143604279, "Height": 0.011625202372670174, "Left": 0.421574205160141, "Top": 0.12066103518009186}, "Polygon": [{"X": 0.421574205160141, "Y": 0.12066103518009186}, {"X": 0.5019340515136719, "Y": 0.12123716622591019}, {"X": 0.502303421497345, "Y": 0.13228623569011688}, {"X": 0.4217575192451477, "Y": 0.13173818588256836}]}, "Id": "36de24c0-09ad-4594-851b-14b265215911"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PAY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03451504185795784, "Height": 0.011126633733510971, "Left": 0.5135990381240845, "Top": 0.12007256597280502}, "Polygon": [{"X": 0.5135990381240845, "Y": 0.12007256597280502}, {"X": 0.5476458072662354, "Y": 0.12031807005405426}, {"X": 0.5481141209602356, "Y": 0.1311991959810257}, {"X": 0.5139899849891663, "Y": 0.1309654414653778}]}, "Id": "5de63ed9-7d06-4b34-943b-52a91f44ccd6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "580", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03469226881861687, "Height": 0.011775407940149307, "Left": 0.34069132804870605, "Top": 0.15115471184253693}, "Polygon": [{"X": 0.3406963646411896, "Y": 0.15115471184253693}, {"X": 0.3753051161766052, "Y": 0.15136748552322388}, {"X": 0.375383585691452, "Y": 0.16293013095855713}, {"X": 0.34069132804870605, "Y": 0.16273005306720734}]}, "Id": "0f5c3821-bb84-4d58-9a7b-6b9075ee60d0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LONE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04550861567258835, "Height": 0.011277410201728344, "Left": 0.3877868354320526, "Top": 0.15252378582954407}, "Polygon": [{"X": 0.3877868354320526, "Y": 0.15252378582954407}, {"X": 0.43308818340301514, "Y": 0.15280066430568695}, {"X": 0.43329542875289917, "Y": 0.1638011932373047}, {"X": 0.3878902792930603, "Y": 0.16354013979434967}]}, "Id": "40f529cf-9d02-4682-959d-a10da4386e09"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "STAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04602464661002159, "Height": 0.010982183739542961, "Left": 0.4454279839992523, "Top": 0.15203474462032318}, "Polygon": [{"X": 0.4454279839992523, "Y": 0.15203474462032318}, {"X": 0.491121381521225, "Y": 0.15231528878211975}, {"X": 0.4914526343345642, "Y": 0.1630169153213501}, {"X": 0.44565755128860474, "Y": 0.1627519428730011}]}, "Id": "4d759be5-d5c2-495b-b274-da924a5ffcc7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DRIVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05726071447134018, "Height": 0.011812184937298298, "Left": 0.5032517910003662, "Top": 0.15058277547359467}, "Polygon": [{"X": 0.5032517910003662, "Y": 0.15058277547359467}, {"X": 0.5599929094314575, "Y": 0.15093454718589783}, {"X": 0.5605124831199646, "Y": 0.1623949557542801}, {"X": 0.503636360168457, "Y": 0.16206392645835876}]}, "Id": "57a10fdf-7236-40b9-8b6a-0598ed5d6eb8"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PARADISE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09284334629774094, "Height": 0.011851252987980843, "Left": 0.34091442823410034, "Top": 0.16773302853107452}, "Polygon": [{"X": 0.3409188389778137, "Y": 0.16773302853107452}, {"X": 0.433543860912323, "Y": 0.16825196146965027}, {"X": 0.4337577819824219, "Y": 0.17958427965641022}, {"X": 0.34091442823410034, "Y": 0.17909881472587585}]}, "Id": "7047b06b-c45c-42e9-b756-2c97c71f76e2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "QUICKSTOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10426542162895203, "Height": 0.012823943980038166, "Left": 0.4456956684589386, "Top": 0.16623571515083313}, "Polygon": [{"X": 0.4456956684589386, "Y": 0.16623571515083313}, {"X": 0.549435019493103, "Y": 0.16682405769824982}, {"X": 0.5499610900878906, "Y": 0.17905965447425842}, {"X": 0.44595852494239807, "Y": 0.17851194739341736}]}, "Id": "88756949-7099-431f-9891-5b3a7dc08565"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013212187215685844, "Height": 0.010819819755852222, "Left": 0.5608376860618591, "Top": 0.1661432832479477}, "Polygon": [{"X": 0.5608376860618591, "Y": 0.1661432832479477}, {"X": 0.573533833026886, "Y": 0.16621561348438263}, {"X": 0.5740498304367065, "Y": 0.17696310579776764}, {"X": 0.5613254904747009, "Y": 0.1768951565027237}]}, "Id": "4f2d741f-2a78-4c52-a1a7-47dbbdf1544a"}, {"BlockType": "WORD", "Confidence": 96.7322998046875, "Text": "XXXXXXXXX0001", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14988823235034943, "Height": 0.013753502629697323, "Left": 0.34017515182495117, "Top": 0.1823374480009079}, "Polygon": [{"X": 0.34018221497535706, "Y": 0.1823374480009079}, {"X": 0.4896678626537323, "Y": 0.18310314416885376}, {"X": 0.4900633990764618, "Y": 0.1960909515619278}, {"X": 0.34017515182495117, "Y": 0.19538749754428864}]}, "Id": "ce46510d-a9e4-4d9d-a558-f2c234ed385f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "HOOKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05814799666404724, "Height": 0.01160817313939333, "Left": 0.34088459610939026, "Top": 0.2001017928123474}, "Polygon": [{"X": 0.3408890664577484, "Y": 0.2001017928123474}, {"X": 0.3989010751247406, "Y": 0.2003650665283203}, {"X": 0.3990325927734375, "Y": 0.21170996129512787}, {"X": 0.34088459610939026, "Y": 0.21146780252456665}]}, "Id": "2069570a-bfcb-438b-82a9-d81176ae7c8b"}, {"BlockType": "WORD", "Confidence": 98.35698699951172, "Text": ",", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0050649503245949745, "Height": 0.006658837199211121, "Left": 0.4845046103000641, "Top": 0.20608823001384735}, "Polygon": [{"X": 0.4845046103000641, "Y": 0.20608823001384735}, {"X": 0.4893687963485718, "Y": 0.2061094492673874}, {"X": 0.4895695745944977, "Y": 0.21274706721305847}, {"X": 0.4846987724304199, "Y": 0.212726891040802}]}, "Id": "55bda3bd-cf5a-4e43-a758-5b75afb5da2d"}, {"BlockType": "WORD", "Confidence": 98.203125, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024155760183930397, "Height": 0.010927719064056873, "Left": 0.5039030313491821, "Top": 0.1983795166015625}, "Polygon": [{"X": 0.5039030313491821, "Y": 0.1983795166015625}, {"X": 0.527645468711853, "Y": 0.1984892040491104}, {"X": 0.5280587673187256, "Y": 0.2093072384595871}, {"X": 0.5042635202407837, "Y": 0.20920582115650177}]}, "Id": "343e40b7-f29a-40aa-86e5-8288d748aa3b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "75561", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05529950186610222, "Height": 0.011499155312776566, "Left": 0.34109926223754883, "Top": 0.21658317744731903}, "Polygon": [{"X": 0.341103196144104, "Y": 0.21658317744731903}, {"X": 0.3962745666503906, "Y": 0.2168036550283432}, {"X": 0.39639878273010254, "Y": 0.2280823290348053}, {"X": 0.34109926223754883, "Y": 0.22788187861442566}]}, "Id": "734cf51d-f109-49ea-bfc2-0d1056eb5ffe"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "05/13/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11801565438508987, "Height": 0.014721178449690342, "Left": 0.34093961119651794, "Top": 0.2316431999206543}, "Polygon": [{"X": 0.3409450352191925, "Y": 0.2316431999206543}, {"X": 0.4586150646209717, "Y": 0.2320551723241806}, {"X": 0.4589552581310272, "Y": 0.24636436998844147}, {"X": 0.34093961119651794, "Y": 0.24600692093372345}]}, "Id": "55002aac-4a3b-45b2-b5cd-26af58a92282"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "590412430", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10588514804840088, "Height": 0.01363923866301775, "Left": 0.4706861674785614, "Top": 0.22889414429664612}, "Polygon": [{"X": 0.4706861674785614, "Y": 0.22889414429664612}, {"X": 0.5759363770484924, "Y": 0.22927378118038177}, {"X": 0.5765712857246399, "Y": 0.2425333857536316}, {"X": 0.4710356891155243, "Y": 0.2421990931034088}]}, "Id": "4b3addd2-15f4-4b39-a66e-36f751344214"}, {"BlockType": "WORD", "Confidence": 99.57848358154297, "Text": "08:56:09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09398981928825378, "Height": 0.012804435566067696, "Left": 0.34087786078453064, "Top": 0.24827800691127777}, "Polygon": [{"X": 0.3408827483654022, "Y": 0.24827800691127777}, {"X": 0.4346323013305664, "Y": 0.24855495989322662}, {"X": 0.4348676800727844, "Y": 0.2610824406147003}, {"X": 0.34087786078453064, "Y": 0.2608436048030853}]}, "Id": "636bb634-ebdf-46a4-9f42-3e8e13f82836"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023156220093369484, "Height": 0.01107764057815075, "Left": 0.44727396965026855, "Top": 0.24740642309188843}, "Polygon": [{"X": 0.44727396965026855, "Y": 0.24740642309188843}, {"X": 0.47014328837394714, "Y": 0.24747487902641296}, {"X": 0.4704301953315735, "Y": 0.2584840655326843}, {"X": 0.4475095272064209, "Y": 0.258423775434494}]}, "Id": "6eea690f-046d-4269-90f6-c699f262b50b"}, {"BlockType": "WORD", "Confidence": 75.34120178222656, "Text": "XXXXXXXXXXXXXXX8412", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2270612269639969, "Height": 0.0158492811024189, "Left": 0.3399864435195923, "Top": 0.2779603898525238}, "Polygon": [{"X": 0.33999523520469666, "Y": 0.2779603898525238}, {"X": 0.5663475394248962, "Y": 0.27840813994407654}, {"X": 0.567047655582428, "Y": 0.2938096523284912}, {"X": 0.3399864435195923, "Y": 0.2934763431549072}]}, "Id": "c8b8833b-30e0-4052-af9c-3066f2f530fa"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "TCH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03643665090203285, "Height": 0.011666781269013882, "Left": 0.3389332890510559, "Top": 0.2985040843486786}, "Polygon": [{"X": 0.33894234895706177, "Y": 0.2985040843486786}, {"X": 0.37529346346855164, "Y": 0.29855144023895264}, {"X": 0.37536993622779846, "Y": 0.31017088890075684}, {"X": 0.3389332890510559, "Y": 0.3101373612880707}]}, "Id": "c6c9ba33-3992-4712-90eb-d9512e837e73"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fuel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04685355722904205, "Height": 0.011490339413285255, "Left": 0.3882668912410736, "Top": 0.2976396977901459}, "Polygon": [{"X": 0.3882668912410736, "Y": 0.2976396977901459}, {"X": 0.4349072277545929, "Y": 0.2977018654346466}, {"X": 0.43512046337127686, "Y": 0.3091300427913666}, {"X": 0.3883723318576813, "Y": 0.30908533930778503}]}, "Id": "b82a1dbc-720f-4daf-a11a-4ca3e6508196"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "INVOICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08165399730205536, "Height": 0.012370078824460506, "Left": 0.3418831527233124, "Top": 0.3145449459552765}, "Polygon": [{"X": 0.34188541769981384, "Y": 0.3145449459552765}, {"X": 0.42333707213401794, "Y": 0.314608097076416}, {"X": 0.42353716492652893, "Y": 0.32691502571105957}, {"X": 0.3418831527233124, "Y": 0.3268848657608032}]}, "Id": "173caffe-fc42-4939-9ae7-23b8c66b0c5e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "051693", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07181643694639206, "Height": 0.012793551199138165, "Left": 0.43599173426628113, "Top": 0.3127119839191437}, "Polygon": [{"X": 0.43599173426628113, "Y": 0.3127119839191437}, {"X": 0.5073849558830261, "Y": 0.31277182698249817}, {"X": 0.5078082084655762, "Y": 0.3255055248737335}, {"X": 0.43623194098472595, "Y": 0.32547569274902344}]}, "Id": "a2e49631-9671-4f36-b702-8aee1d0fdaa0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04724903404712677, "Height": 0.012205393053591251, "Left": 0.340076208114624, "Top": 0.3318482041358948}, "Polygon": [{"X": 0.3400828540325165, "Y": 0.3318482041358948}, {"X": 0.38721635937690735, "Y": 0.33185791969299316}, {"X": 0.387325257062912, "Y": 0.34404438734054565}, {"X": 0.340076208114624, "Y": 0.34405359625816345}]}, "Id": "312fd296-bab0-4d6a-8ed1-1fc81c4541f9"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "210836", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07202047854661942, "Height": 0.012336391024291515, "Left": 0.40016940236091614, "Top": 0.33066120743751526}, "Polygon": [{"X": 0.40016940236091614, "Y": 0.33066120743751526}, {"X": 0.4718702435493469, "Y": 0.3306788504123688}, {"X": 0.47218987345695496, "Y": 0.3429860472679138}, {"X": 0.40031179785728455, "Y": 0.34299761056900024}]}, "Id": "5828bb7b-27f9-45a9-9b85-6feb9ebe2e90"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "PUMP#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061068080365657806, "Height": 0.01278444193303585, "Left": 0.3400532603263855, "Top": 0.3661281168460846}, "Polygon": [{"X": 0.34006020426750183, "Y": 0.3661843240261078}, {"X": 0.40097370743751526, "Y": 0.3661281168460846}, {"X": 0.4011213481426239, "Y": 0.3788306415081024}, {"X": 0.3400532603263855, "Y": 0.3789125680923462}]}, "Id": "4895a74f-b17e-4709-9a4d-c6fd3dd434fd"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "13", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023304974660277367, "Height": 0.01155840139836073, "Left": 0.4130488932132721, "Top": 0.36551135778427124}, "Polygon": [{"X": 0.4130488932132721, "Y": 0.3655322194099426}, {"X": 0.4361388683319092, "Y": 0.36551135778427124}, {"X": 0.436353862285614, "Y": 0.3770400583744049}, {"X": 0.4132108688354492, "Y": 0.37706977128982544}]}, "Id": "ff345d99-1f3b-4b98-8f5c-6b5911c13f44"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07240243256092072, "Height": 0.012869004160165787, "Left": 0.3398655354976654, "Top": 0.4005819261074066}, "Polygon": [{"X": 0.33987289667129517, "Y": 0.40073058009147644}, {"X": 0.41209349036216736, "Y": 0.4005819261074066}, {"X": 0.4122679829597473, "Y": 0.41327157616615295}, {"X": 0.3398655354976654, "Y": 0.4134509265422821}]}, "Id": "4fd68d61-599c-42ce-9079-1b6df49bbbd9"}, {"BlockType": "WORD", "Confidence": 99.32637023925781, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008319541811943054, "Height": 0.011291200295090675, "Left": 0.4258476197719574, "Top": 0.40057849884033203}, "Polygon": [{"X": 0.4258476197719574, "Y": 0.40059521794319153}, {"X": 0.43396326899528503, "Y": 0.40057849884033203}, {"X": 0.43416717648506165, "Y": 0.4118499159812927}, {"X": 0.4260334074497223, "Y": 0.41186970472335815}]}, "Id": "b996c521-4528-4f5d-8f24-e40219c1c7ed"}, {"BlockType": "WORD", "Confidence": 99.4345932006836, "Text": "24.616G", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0856003537774086, "Height": 0.013262216001749039, "Left": 0.4983149468898773, "Top": 0.39768263697624207}, "Polygon": [{"X": 0.4983149468898773, "Y": 0.3978503942489624}, {"X": 0.5832918882369995, "Y": 0.39768263697624207}, {"X": 0.5839152932167053, "Y": 0.41073980927467346}, {"X": 0.4987192153930664, "Y": 0.4109448492527008}]}, "Id": "aab5e8d9-bb64-4f54-a5d8-b1c0e71e4be7"}, {"BlockType": "WORD", "Confidence": 95.77965545654297, "Text": "PRICE/GAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10867486149072647, "Height": 0.013879196718335152, "Left": 0.340284526348114, "Top": 0.4183305501937866}, "Polygon": [{"X": 0.34029123187065125, "Y": 0.4186173975467682}, {"X": 0.44867587089538574, "Y": 0.4183305501937866}, {"X": 0.4489594101905823, "Y": 0.43187353014945984}, {"X": 0.340284526348114, "Y": 0.43220973014831543}]}, "Id": "053be3bf-5c4f-4287-a9e0-db1cb4e44668"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "$3.399", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07294952869415283, "Height": 0.014125720597803593, "Left": 0.5112459063529968, "Top": 0.4151552617549896}, "Polygon": [{"X": 0.5112459063529968, "Y": 0.4153398275375366}, {"X": 0.5835330486297607, "Y": 0.4151552617549896}, {"X": 0.5841954350471497, "Y": 0.42906248569488525}, {"X": 0.5117104649543762, "Y": 0.42928096652030945}]}, "Id": "ec7905df-656c-433a-96aa-f920813b598f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048909686505794525, "Height": 0.012340622022747993, "Left": 0.33888500928878784, "Top": 0.45399776101112366}, "Polygon": [{"X": 0.3388943076133728, "Y": 0.454183965921402}, {"X": 0.3876877427101135, "Y": 0.45399776101112366}, {"X": 0.38779470324516296, "Y": 0.46613219380378723}, {"X": 0.33888500928878784, "Y": 0.4663383960723877}]}, "Id": "1f09d826-b470-4583-8e0d-451eda6a481d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06222457066178322, "Height": 0.01190238632261753, "Left": 0.3998355269432068, "Top": 0.4533562958240509}, "Polygon": [{"X": 0.3998355269432068, "Y": 0.4535919725894928}, {"X": 0.4617880582809448, "Y": 0.4533562958240509}, {"X": 0.4620600938796997, "Y": 0.46499860286712646}, {"X": 0.39996618032455444, "Y": 0.4652586579322815}]}, "Id": "e6845c2a-87ab-4113-8ac5-f8fdd7a3a48e"}, {"BlockType": "WORD", "Confidence": 99.091796875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01342830341309309, "Height": 0.013865859247744083, "Left": 0.4871888756752014, "Top": 0.4515383541584015}, "Polygon": [{"X": 0.4871888756752014, "Y": 0.45158708095550537}, {"X": 0.5001901984214783, "Y": 0.4515383541584015}, {"X": 0.5006172060966492, "Y": 0.46534937620162964}, {"X": 0.4875807762145996, "Y": 0.465404212474823}]}, "Id": "50318da9-0c15-487d-aaf8-1455f3e54df0"}, {"BlockType": "WORD", "Confidence": 99.091796875, "Text": "83.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061388976871967316, "Height": 0.013025863096117973, "Left": 0.5240901112556458, "Top": 0.45088598132133484}, "Polygon": [{"X": 0.5240901112556458, "Y": 0.45111316442489624}, {"X": 0.5848717093467712, "Y": 0.45088598132133484}, {"X": 0.5854791402816772, "Y": 0.46365830302238464}, {"X": 0.5245459079742432, "Y": 0.46391186118125916}]}, "Id": "5e4200b7-c83c-4d30-a1ce-ce6ea0dc356a"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06322432309389114, "Height": 0.012634980492293835, "Left": 0.386945515871048, "Top": 0.5065507292747498}, "Polygon": [{"X": 0.386945515871048, "Y": 0.5069007277488708}, {"X": 0.4499146640300751, "Y": 0.5065507292747498}, {"X": 0.4501698315143585, "Y": 0.5188093185424805}, {"X": 0.38705092668533325, "Y": 0.5191857218742371}]}, "Id": "7a6b3b13-eb4a-4d5c-a4d4-325721622373"}, {"BlockType": "WORD", "Confidence": 99.14800262451172, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013346925377845764, "Height": 0.006545821204781532, "Left": 0.46191683411598206, "Top": 0.5103152990341187}, "Polygon": [{"X": 0.46191683411598206, "Y": 0.5103902816772461}, {"X": 0.4750975966453552, "Y": 0.5103152990341187}, {"X": 0.475263774394989, "Y": 0.5167832374572754}, {"X": 0.46206653118133545, "Y": 0.5168611407279968}]}, "Id": "7e8b5848-30ca-490b-82c9-5aaf23d72176"}, {"BlockType": "WORD", "Confidence": 99.453125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013264195062220097, "Height": 0.013756806030869484, "Left": 0.48854121565818787, "Top": 0.5048403739929199}, "Polygon": [{"X": 0.48854121565818787, "Y": 0.5049111843109131}, {"X": 0.5013836622238159, "Y": 0.5048403739929199}, {"X": 0.5018054246902466, "Y": 0.5185204148292542}, {"X": 0.4889289438724518, "Y": 0.5185971856117249}]}, "Id": "1161d8ce-2789-43e8-b7b3-af771a54d861"}, {"BlockType": "WORD", "Confidence": 99.4140625, "Text": "83.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06217293068766594, "Height": 0.012747634202241898, "Left": 0.****************, "Top": 0.5041924715042114}, "Polygon": [{"X": 0.****************, "Y": 0.5045315027236938}, {"X": 0.5873978734016418, "Y": 0.5041924715042114}, {"X": 0.5879867076873779, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "45d0d768-e635-4c51-a680-e9079fdabe39"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "CREDIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07523081451654434, "Height": 0.013127401471138, "Left": 0.3370814025402069, "Top": 0.543509304523468}, "Polygon": [{"X": 0.3370952606201172, "Y": 0.****************}, {"X": 0.****************, "Y": 0.543509304523468}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.3370814025402069, "Y": 0.****************}]}, "Id": "ae88eefc-670c-4018-ad49-24bec3ac68a1"}, {"BlockType": "WORD", "Confidence": 96.298828125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013607455417513847, "Height": 0.014475191943347454, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.502447783946991, "Y": 0.****************}, {"X": 0.502890944480896, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}]}, "Id": "ac5e12ee-5faf-4e42-b7cf-abe5bd20b63c"}, {"BlockType": "WORD", "Confidence": 99.169921875, "Text": "83.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06260302662849426, "Height": 0.01316982600837946, "Left": 0.527265191078186, "Top": 0.5402618646621704}, "Polygon": [{"X": 0.527265191078186, "Y": 0.5406773090362549}, {"X": 0.5892626643180847, "Y": 0.5402618646621704}, {"X": 0.5898682475090027, "Y": 0.5529889464378357}, {"X": 0.5277193784713745, "Y": 0.5534316897392273}]}, "Id": "cda5a3d0-156e-40a8-a57c-73945eec9289"}, {"BlockType": "WORD", "Confidence": 98.34602355957031, "Text": "Customer-activated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11754953861236572, "Height": 0.015091994777321815, "Left": 0.3343028426170349, "Top": 0.598453164100647}, "Polygon": [{"X": 0.3343255817890167, "Y": 0.5994603633880615}, {"X": 0.4515610933303833, "Y": 0.598453164100647}, {"X": 0.45185238122940063, "Y": 0.6124808192253113}, {"X": 0.3343028426170349, "Y": 0.6135451793670654}]}, "Id": "0aad3800-a809-4b9a-a82e-3d1b9c3c81ae"}, {"BlockType": "WORD", "Confidence": 99.76322937011719, "Text": "Purchase/Capture", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10411178320646286, "Height": 0.01703871227800846, "Left": 0.45756497979164124, "Top": 0.5969215631484985}, "Polygon": [{"X": 0.45756497979164124, "Y": 0.5978081822395325}, {"X": 0.5610061287879944, "Y": 0.5969215631484985}, {"X": 0.5616768002510071, "Y": 0.6130155324935913}, {"X": 0.4579189419746399, "Y": 0.6139602661132812}]}, "Id": "5d11ae27-6cd5-4b34-8478-27aab408e7c3"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Site", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026627859100699425, "Height": 0.013557132333517075, "Left": 0.3343777656555176, "Top": 0.619783341884613}, "Polygon": [{"X": 0.3343989849090576, "Y": 0.6200294494628906}, {"X": 0.3609596788883209, "Y": 0.619783341884613}, {"X": 0.36100560426712036, "Y": 0.6330820322036743}, {"X": 0.3343777656555176, "Y": 0.6333404779434204}]}, "Id": "e8caab98-0298-43b0-8184-64e67d26cab5"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012744901701807976, "Height": 0.012871015816926956, "Left": 0.3662455976009369, "Top": 0.619874894618988}, "Polygon": [{"X": 0.3662455976009369, "Y": 0.6199923157691956}, {"X": 0.37890297174453735, "Y": 0.619874894618988}, {"X": 0.37899050116539, "Y": 0.6326228976249695}, {"X": 0.366302490234375, "Y": 0.6327459216117859}]}, "Id": "e7065cb6-e80e-4269-9e79-b11993a16d44"}, {"BlockType": "WORD", "Confidence": 33.77311706542969, "Text": "0000008004816757", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10408898442983627, "Height": 0.013742565177381039, "Left": 0.38673028349876404, "Top": 0.6180891990661621}, "Polygon": [{"X": 0.38673028349876404, "Y": 0.6190488338470459}, {"X": 0.490461528301239, "Y": 0.6180891990661621}, {"X": 0.4908192753791809, "Y": 0.6308260560035706}, {"X": 0.38683709502220154, "Y": 0.6318317651748657}]}, "Id": "c2094104-415c-46ce-95dd-bd2488e6caf8"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Shift", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03369184583425522, "Height": 0.014604107476770878, "Left": 0.3341400921344757, "Top": 0.6387289762496948}, "Polygon": [{"X": 0.3341633975505829, "Y": 0.6390613913536072}, {"X": 0.3677644729614258, "Y": 0.6387289762496948}, {"X": 0.36783191561698914, "Y": 0.6529839634895325}, {"X": 0.3341400921344757, "Y": 0.6533330678939819}]}, "Id": "ba68de39-75ef-494a-affa-b9c6b808820b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039499614387750626, "Height": 0.013289711438119411, "Left": 0.37366580963134766, "Top": 0.6388044357299805}, "Polygon": [{"X": 0.37366580963134766, "Y": 0.6391941905021667}, {"X": 0.4129940867424011, "Y": 0.6388044357299805}, {"X": 0.413165420293808, "Y": 0.6516867280006409}, {"X": 0.3737412691116333, "Y": 0.6520941853523254}]}, "Id": "763317cb-f3cb-4e6e-aca4-b0d5a919b573"}, {"BlockType": "WORD", "Confidence": 99.64604187011719, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005725864786654711, "Height": 0.012504030019044876, "Left": 0.4195985198020935, "Top": 0.6387603878974915}, "Polygon": [{"X": 0.4195985198020935, "Y": 0.6388152241706848}, {"X": 0.4251302182674408, "Y": 0.6387603878974915}, {"X": 0.42532438039779663, "Y": 0.6512072086334229}, {"X": 0.4197796583175659, "Y": 0.6512644290924072}]}, "Id": "76aad1e5-f758-4b2b-91c7-8f3e796800fc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Sequence", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05323966220021248, "Height": 0.01616300828754902, "Left": 0.33442243933677673, "Top": 0.659378707408905}, "Polygon": [{"X": 0.3344469964504242, "Y": 0.6599403023719788}, {"X": 0.3875306248664856, "Y": 0.659378707408905}, {"X": 0.3876620829105377, "Y": 0.6749511361122131}, {"X": 0.33442243933677673, "Y": 0.6755416989326477}]}, "Id": "c9383ce4-ed21-4c0b-9a37-c39067590482"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039305854588747025, "Height": 0.01331415493041277, "Left": 0.3936237394809723, "Top": 0.6577954888343811}, "Polygon": [{"X": 0.3936237394809723, "Y": 0.6582075953483582}, {"X": 0.4327108860015869, "Y": 0.6577954888343811}, {"X": 0.4329296052455902, "Y": 0.6706799268722534}, {"X": 0.3937475383281708, "Y": 0.671109676361084}]}, "Id": "99441338-3dd9-4309-823d-1fd8ff174067"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "58096", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03307010978460312, "Height": 0.013204086571931839, "Left": 0.43935537338256836, "Top": 0.6570566892623901}, "Polygon": [{"X": 0.43935537338256836, "Y": 0.6574016809463501}, {"X": 0.47211188077926636, "Y": 0.6570566892623901}, {"X": 0.4724254906177521, "Y": 0.6699010133743286}, {"X": 0.43958979845046997, "Y": 0.6702607870101929}]}, "Id": "d39e6b72-b69f-422b-bcf3-419cbcf5476c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Swiped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04065830633044243, "Height": 0.01744830794632435, "Left": 0.33436277508735657, "Top": 0.678260326385498}, "Polygon": [{"X": 0.3343895971775055, "Y": 0.6787141561508179}, {"X": 0.37491855025291443, "Y": 0.678260326385498}, {"X": 0.3750210702419281, "Y": 0.6952307224273682}, {"X": 0.33436277508735657, "Y": 0.6957086324691772}]}, "Id": "faba676b-9bab-4ae8-be15-bf9000ffd6ba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0537681020796299, "Height": 0.01431447733193636, "Left": 0.3344919979572296, "Top": 0.698016881942749}, "Polygon": [{"X": 0.33451318740844727, "Y": 0.6986525654792786}, {"X": 0.3881440758705139, "Y": 0.698016881942749}, {"X": 0.3882600963115692, "Y": 0.711669921875}, {"X": 0.3344919979572296, "Y": 0.7123313546180725}]}, "Id": "3a327468-ff92-48b3-b7a2-8769d7819356"}, {"BlockType": "WORD", "Confidence": 71.68984985351562, "Text": "210836", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04038885608315468, "Height": 0.013860289007425308, "Left": 0.4004771411418915, "Top": 0.696584165096283}, "Polygon": [{"X": 0.4004771411418915, "Y": 0.6970588564872742}, {"X": 0.44062095880508423, "Y": 0.696584165096283}, {"X": 0.44086599349975586, "Y": 0.7099508047103882}, {"X": 0.40062183141708374, "Y": 0.710444450378418}]}, "Id": "e3638c3d-985e-4afb-be55-d74f4bb69a7a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05332792177796364, "Height": 0.013620592653751373, "Left": 0.3346213400363922, "Top": 0.7186241149902344}, "Polygon": [{"X": 0.3346410393714905, "Y": 0.7192907333374023}, {"X": 0.3878405690193176, "Y": 0.7186241149902344}, {"X": 0.38794925808906555, "Y": 0.7315537333488464}, {"X": 0.3346213400363922, "Y": 0.7322446703910828}]}, "Id": "3a727c3c-9e23-4ad1-846b-6f15ebfeb8ba"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07188063859939575, "Height": 0.0147877037525177, "Left": 0.3953824043273926, "Top": 0.716018557548523}, "Polygon": [{"X": 0.3953824043273926, "Y": 0.7169114351272583}, {"X": 0.466941773891449, "Y": 0.716018557548523}, {"X": 0.46726304292678833, "Y": 0.7298782467842102}, {"X": 0.39551883935928345, "Y": 0.730806291103363}]}, "Id": "7707a82a-f0b7-47e5-bb75-85667da4485b"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0324096716940403, "Height": 0.014000899158418179, "Left": 0.33483412861824036, "Top": 0.7386094927787781}, "Polygon": [{"X": 0.3348541557788849, "Y": 0.7390355467796326}, {"X": 0.36718231439590454, "Y": 0.7386094927787781}, {"X": 0.36724379658699036, "Y": 0.7521687746047974}, {"X": 0.33483412861824036, "Y": 0.7526103854179382}]}, "Id": "36973fcc-8f74-464d-9a3a-141b97e9be54"}, {"BlockType": "WORD", "Confidence": 72.97512817382812, "Text": "HP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020693747326731682, "Height": 0.014083849266171455, "Left": 0.3748238682746887, "Top": 0.7373512983322144}, "Polygon": [{"X": 0.3748238682746887, "Y": 0.7376216650009155}, {"X": 0.3953825831413269, "Y": 0.7373512983322144}, {"X": 0.39551761746406555, "Y": 0.7511547207832336}, {"X": 0.37490615248680115, "Y": 0.7514351606369019}]}, "Id": "6573b56c-c899-4cf5-abd1-ba8847e5409f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DISTRIBUTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07922664284706116, "Height": 0.0142713263630867, "Left": 0.401422917842865, "Top": 0.7363840341567993}, "Polygon": [{"X": 0.401422917842865, "Y": 0.7374218106269836}, {"X": 0.4803119897842407, "Y": 0.7363840341567993}, {"X": 0.48064956068992615, "Y": 0.749580442905426}, {"X": 0.4015672206878662, "Y": 0.7506553530693054}]}, "Id": "21292b9d-0180-40e9-a5c4-3756d39407bb"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "City:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03262776508927345, "Height": 0.018712574616074562, "Left": 0.3347426652908325, "Top": 0.7578064799308777}, "Polygon": [{"X": 0.3347697854042053, "Y": 0.7582556009292603}, {"X": 0.36728760600090027, "Y": 0.7578064799308777}, {"X": 0.3673704266548157, "Y": 0.7760487794876099}, {"X": 0.3347426652908325, "Y": 0.7765190005302429}]}, "Id": "d43897e2-8e84-4f79-a439-96e63e4f407b"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "OLATHE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04025190323591232, "Height": 0.014400924555957317, "Left": 0.375194787979126, "Top": 0.7571379542350769}, "Polygon": [{"X": 0.375194787979126, "Y": 0.7576913237571716}, {"X": 0.41526100039482117, "Y": 0.7571379542350769}, {"X": 0.4154466986656189, "Y": 0.7709657549858093}, {"X": 0.37527793645858765, "Y": 0.7715388536453247}]}, "Id": "5e1b7588-0b8b-4f08-b2e3-bd3fc4e259f5"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "State:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03944970294833183, "Height": 0.014821258373558521, "Left": 0.33465874195098877, "Top": 0.7791552543640137}, "Polygon": [{"X": 0.33468005061149597, "Y": 0.779726505279541}, {"X": 0.37402641773223877, "Y": 0.7791552543640137}, {"X": 0.3741084337234497, "Y": 0.7933853268623352}, {"X": 0.33465874195098877, "Y": 0.7939765453338623}]}, "Id": "84123c35-466a-4e79-b480-6f48a1d02bd8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013738519512116909, "Height": 0.013752116821706295, "Left": 0.3820672035217285, "Top": 0.7777979373931885}, "Polygon": [{"X": 0.3820672035217285, "Y": 0.7779949903488159}, {"X": 0.3956734836101532, "Y": 0.7777979373931885}, {"X": 0.3958057165145874, "Y": 0.7913463711738586}, {"X": 0.38216549158096313, "Y": 0.7915500402450562}]}, "Id": "82cfd20b-dda9-4087-ad21-55c8a4b74f6d"}, {"BlockType": "WORD", "Confidence": 98.03950500488281, "Text": "Acc.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03313225135207176, "Height": 0.014488106593489647, "Left": 0.3342275619506836, "Top": 0.8003284931182861}, "Polygon": [{"X": 0.33424949645996094, "Y": 0.800831139087677}, {"X": 0.36729687452316284, "Y": 0.8003284931182861}, {"X": 0.36735981702804565, "Y": 0.8142973780632019}, {"X": 0.3342275619506836, "Y": 0.8148165941238403}]}, "Id": "a6545191-3e54-4cd8-8a17-53891f53c7c7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "132643", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04061980918049812, "Height": 0.014931004494428635, "Left": 0.3756730556488037, "Top": 0.7981279492378235}, "Polygon": [{"X": 0.3756730556488037, "Y": 0.7987409830093384}, {"X": 0.4161001145839691, "Y": 0.7981279492378235}, {"X": 0.41629284620285034, "Y": 0.8124252557754517}, {"X": 0.37575963139533997, "Y": 0.8130589723587036}]}, "Id": "c4793a4e-1c89-4391-b615-5ee3f0f88779"}, {"BlockType": "WORD", "Confidence": 98.75458526611328, "Text": "Odometer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06052955240011215, "Height": 0.015407819300889969, "Left": 0.3341817855834961, "Top": 0.8205097913742065}, "Polygon": [{"X": 0.3342044949531555, "Y": 0.8214690685272217}, {"X": 0.3945746123790741, "Y": 0.8205097913742065}, {"X": 0.39471134543418884, "Y": 0.8349270820617676}, {"X": 0.3341817855834961, "Y": 0.835917592048645}]}, "Id": "0dc65896-7140-45d6-bb5a-ba987cf2a649"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "274352", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04108935222029686, "Height": 0.014541400596499443, "Left": 0.40210816264152527, "Top": 0.8178001046180725}, "Polygon": [{"X": 0.40210816264152527, "Y": 0.8184463381767273}, {"X": 0.44294288754463196, "Y": 0.8178001046180725}, {"X": 0.4431975185871124, "Y": 0.8316748738288879}, {"X": 0.4022591710090637, "Y": 0.8323414921760559}]}, "Id": "babdeec0-bbf8-4b6f-a4b3-0e919b21ecae"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Trip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02829389087855816, "Height": 0.018846485763788223, "Left": 0.3340495526790619, "Top": 0.8415222764015198}, "Polygon": [{"X": 0.33407875895500183, "Y": 0.8419893980026245}, {"X": 0.36227816343307495, "Y": 0.8415222764015198}, {"X": 0.362343430519104, "Y": 0.8598830103874207}, {"X": 0.3340495526790619, "Y": 0.8603687882423401}]}, "Id": "ff65fb82-b7ac-4fa4-bfef-3a6d1d407175"}, {"BlockType": "WORD", "Confidence": 99.65740203857422, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013035054318606853, "Height": 0.01317590195685625, "Left": 0.36825618147850037, "Top": 0.842046856880188}, "Polygon": [{"X": 0.36825618147850037, "Y": 0.8422616124153137}, {"X": 0.3812004625797272, "Y": 0.842046856880188}, {"X": 0.3812912404537201, "Y": 0.8550019264221191}, {"X": 0.3683163821697235, "Y": 0.855222761631012}]}, "Id": "60c9efc4-52b4-45bc-b051-e6483ddb7c88"}, {"BlockType": "WORD", "Confidence": 98.32588958740234, "Text": "4408", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027972718700766563, "Height": 0.013680599629878998, "Left": 0.3891220688819885, "Top": 0.8402074575424194}, "Polygon": [{"X": 0.3891220688819885, "Y": 0.8406674861907959}, {"X": 0.4169161319732666, "Y": 0.8402074575424194}, {"X": 0.41709479689598083, "Y": 0.8534148335456848}, {"X": 0.38923385739326477, "Y": 0.8538880944252014}]}, "Id": "a9e51ac1-c571-4106-88df-956e5fcdb146"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "4366d7aa-db5c-4e79-9a6e-b48343bc2952", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "4366d7aa-db5c-4e79-9a6e-b48343bc2952", "content-type": "application/x-amz-json-1.1", "content-length": "57445", "date": "Wed, 24 Sep 2025 12:47:18 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c4aa3cc6-ec09-4e18-ad8b-757f8b7fc420", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:21 GMT", "content-type": "application/json", "content-length": "770", "connection": "keep-alive", "x-amzn-requestid": "c4aa3cc6-ec09-4e18-ad8b-757f8b7fc420"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. We need to identify doc type.\n\nContent: Fuel receipt. Keywords: Fuel, Diesel, Pump#, Gallons, Fuel Receipt. So doc_type = fuel_receipt.\n\nWe must output via tool classify_logistics_doc_type with array of objects. Only one page.\n\nLet's ensure correct JSON format. The function expects:\n\n{\n  documents: [\n    {\n      page_no: number,\n      doc_type: \"...\",\n    }\n  ]\n}\n\nReturn that."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2726, "outputTokens": 121, "totalTokens": 2847}, "metrics": {"latencyMs": 768}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/0daf4da1_R3202DZX6JMMABCF7GU8.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n<PERSON><PERSON><PERSON><PERSON> EXPRESS PAY\n580 LONE STAR DRIVE\nPARADISE QUICKSTOP #\nXXXXXXXXX0001\nHOOKS\n, TX\n75561\n05/13/2025 590412430\n08:56:09 AM\nXXXXXXXXXXXXXXX8412\nTCH Fuel\nINVOICE 051693\nAUTH 210836\nPUMP# 13\nDiesel 1\n24.616G\nPRICE/GAL\n$3.399\nFUEL TOTAL $ 83.67\nTOTAL = $ 83.67\nCREDIT\n$ 83.67\nCustomer-activated Purchase/Capture\nSite #: 0000008004816757\nShift Number 1\nSequence Number 58096\nSwiped\nAPPROVED 210836\nCustomer Information\nName: HP DISTRIBUTION\nCity: OLATHE\nState: KS\nAcc.: 132643\nOdometer: 274352\nTrip #: 4408\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}