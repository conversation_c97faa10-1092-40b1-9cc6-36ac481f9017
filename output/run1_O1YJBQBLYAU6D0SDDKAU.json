{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "62fb34e1-3665-43e1-b778-de4e93da24ce", "Relationships": [{"Type": "CHILD", "Ids": ["c0c65127-fc30-4647-b063-f44a526f5abd", "2ffaeffe-0cc2-474e-91ac-cd2a164f56cb", "e693a2d1-f2e6-403e-887d-932bec9c8a49", "be9cd5a5-3bae-4410-89a1-4b3af9260377", "0f551bde-5e82-4ecb-ae43-1511fc8be0a3", "0adcb268-c3f4-43f9-8282-64e0d00df555", "071b7d11-839b-4eeb-817e-864a731994c6", "4d128d83-30ba-4613-be7a-29cc2bd57af3", "696eb445-ce30-443c-b403-2e2934dffa6d", "70fd7a1f-1a0d-43fd-a5f9-dd6fb4434ae6", "64b291d8-746a-44a5-a329-12352a9ca3f3", "9ec7bd92-5444-4e31-af5f-32431aaa3b3b", "0011f710-8266-4f23-8a79-62c09524bd6b", "2264b6a5-5e25-4d2b-99bb-7b1f8c62f87b", "23b5ee0b-a539-4f5d-b49f-b87ac98a82ac", "f93b4175-99fe-4acd-a6d3-6eb6883cc96b", "7c60ded8-2dde-4df7-8478-d1f23e89df18", "5eed8faa-df61-4a16-845f-d3da458084fd", "6ddb8b96-ec2c-4d86-a197-3cd573677b63", "b77bef81-1fe7-4e68-87c0-00669564e4fa", "cbf61b58-728f-4b87-95da-a502802d695c", "ac85ba88-e7b1-4b0c-9df4-a3d138fc267e", "85a21cd2-7780-4f65-b0b2-177c881183af", "87c1513e-66b4-4b12-b876-54b646f67fad", "06fd5044-646d-444d-8ead-eba7a4eaf862", "9533e7b8-7e5b-454f-a822-affbca95a096", "17e78bdf-cefd-4c85-a0d6-928c1a418e7f", "a2a6d599-b033-4e5c-910a-76a76dd72439", "ad84a75f-77fd-4307-a75e-0f96e07aa43f", "192b5ce5-e38d-4765-8ff3-1ae5b3d4f4a4", "ce501329-8d58-4eb1-a553-c44ac5db1160", "5ccad9a9-8295-4d89-b0aa-733b79bfc31c"]}]}, {"BlockType": "LINE", "Confidence": 99.99003601074219, "Text": "TWS of Carteret LLC", "Geometry": {"BoundingBox": {"Width": 0.4312579929828644, "Height": 0.02052975818514824, "Left": 0.28362569212913513, "Top": 0.052415043115615845}, "Polygon": [{"X": 0.28362610936164856, "Y": 0.052415043115615845}, {"X": 0.7148836851119995, "Y": 0.052422892302274704}, {"X": 0.7148831486701965, "Y": 0.07294479757547379}, {"X": 0.28362569212913513, "Y": 0.07293703407049179}]}, "Id": "c0c65127-fc30-4647-b063-f44a526f5abd", "Relationships": [{"Type": "CHILD", "Ids": ["728adc16-db8b-4537-af62-92ba9a6e6a0b", "cca04e4e-0eaa-4892-91a7-2b41856d54d5", "a4d51f6f-89df-4d96-8a8d-c0774f9b42ba", "dfd4a26e-a093-4280-85fb-48099962eacb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "855-897-9675", "Geometry": {"BoundingBox": {"Width": 0.2865448594093323, "Height": 0.01968085952103138, "Left": 0.35689473152160645, "Top": 0.12296231836080551}, "Polygon": [{"X": 0.35689517855644226, "Y": 0.12296231836080551}, {"X": 0.6434395909309387, "Y": 0.12296734750270844}, {"X": 0.6434391140937805, "Y": 0.14264318346977234}, {"X": 0.35689473152160645, "Y": 0.1426382064819336}]}, "Id": "2ffaeffe-0cc2-474e-91ac-cd2a164f56cb", "Relationships": [{"Type": "CHILD", "Ids": ["30647910-ed31-4d42-a903-e645bd7916a3"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Warehouse:", "Geometry": {"BoundingBox": {"Width": 0.2477707862854004, "Height": 0.0206758975982666, "Left": 0.0468941405415535, "Top": 0.19191275537014008}, "Polygon": [{"X": 0.04689450562000275, "Y": 0.19191275537014008}, {"X": 0.2946649193763733, "Y": 0.19191695749759674}, {"X": 0.29466450214385986, "Y": 0.21258865296840668}, {"X": 0.0468941405415535, "Y": 0.2125845104455948}]}, "Id": "e693a2d1-f2e6-403e-887d-932bec9c8a49", "Relationships": [{"Type": "CHILD", "Ids": ["bd469d2b-c084-449c-908b-714d3ba93d07"]}]}, {"BlockType": "LINE", "Confidence": 91.54523468017578, "Text": "Imperial Dade - Jersey", "Geometry": {"BoundingBox": {"Width": 0.4631699323654175, "Height": 0.025545809417963028, "Left": 0.4911980628967285, "Top": 0.19187280535697937}, "Polygon": [{"X": 0.49119868874549866, "Y": 0.19187280535697937}, {"X": 0.954367995262146, "Y": 0.19188062846660614}, {"X": 0.9543672204017639, "Y": 0.2174186110496521}, {"X": 0.4911980628967285, "Y": 0.2174108922481537}]}, "Id": "be9cd5a5-3bae-4410-89a1-4b3af9260377", "Relationships": [{"Type": "CHILD", "Ids": ["57371c59-15d2-4971-9114-2f3467f230b7", "ddfca0f8-3cc2-45f2-b452-eeab39d6c2df", "ddfcc2e7-7a37-4035-b617-0979508b5040", "4d7fe4e6-9882-4e1f-aa7f-d458917fd2b5"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "City, NJ", "Geometry": {"BoundingBox": {"Width": 0.15987834334373474, "Height": 0.0255233496427536, "Left": 0.7923925518989563, "Top": 0.22694365680217743}, "Polygon": [{"X": 0.7923932671546936, "Y": 0.22694365680217743}, {"X": 0.9522708654403687, "Y": 0.22694630920886993}, {"X": 0.9522701501846313, "Y": 0.25246700644493103}, {"X": 0.7923925518989563, "Y": 0.2524643838405609}]}, "Id": "0f551bde-5e82-4ecb-ae43-1511fc8be0a3", "Relationships": [{"Type": "CHILD", "Ids": ["8645dbc2-2581-44da-a123-04a9b070a50c", "3c827174-75a2-487a-a435-9f843a0d166c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Tax ID:", "Geometry": {"BoundingBox": {"Width": 0.1472923308610916, "Height": 0.020316172391176224, "Left": 0.04640492424368858, "Top": 0.2621638774871826}, "Polygon": [{"X": 0.046405285596847534, "Y": 0.2621638774871826}, {"X": 0.1936972439289093, "Y": 0.262166291475296}, {"X": 0.19369685649871826, "Y": 0.28248006105422974}, {"X": 0.04640492424368858, "Y": 0.2824776768684387}]}, "Id": "0adcb268-c3f4-43f9-8282-64e0d00df555", "Relationships": [{"Type": "CHILD", "Ids": ["90e88f7b-3612-46d4-8121-88bb9bba7c73", "4fa9e3f1-ff22-4a25-b05f-1b0f700064c8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "46-5443689", "Geometry": {"BoundingBox": {"Width": 0.24588635563850403, "Height": 0.019972719252109528, "Left": 0.7064048647880554, "Top": 0.2627529203891754}, "Polygon": [{"X": 0.7064054012298584, "Y": 0.2627529203891754}, {"X": 0.9522911906242371, "Y": 0.26275694370269775}, {"X": 0.9522905945777893, "Y": 0.28272566199302673}, {"X": 0.7064048647880554, "Y": 0.28272169828414917}]}, "Id": "071b7d11-839b-4eeb-817e-864a731994c6", "Relationships": [{"Type": "CHILD", "Ids": ["58305198-e61f-40ce-a264-40e4282ed1e3"]}]}, {"BlockType": "LINE", "Confidence": 99.83159637451172, "Text": "Receipt#:", "Geometry": {"BoundingBox": {"Width": 0.19017890095710754, "Height": 0.025243347510695457, "Left": 0.04932868853211403, "Top": 0.29688823223114014}, "Polygon": [{"X": 0.049329135566949844, "Y": 0.29688823223114014}, {"X": 0.23950758576393127, "Y": 0.2968912720680237}, {"X": 0.23950707912445068, "Y": 0.32213157415390015}, {"X": 0.04932868853211403, "Y": 0.3221285939216614}]}, "Id": "4d128d83-30ba-4613-be7a-29cc2bd57af3", "Relationships": [{"Type": "CHILD", "Ids": ["8eefb859-c334-4c33-a75b-4464c99dba4a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "15323", "Geometry": {"BoundingBox": {"Width": 0.12353456020355225, "Height": 0.02004109136760235, "Left": 0.8288025856018066, "Top": 0.2973767817020416}, "Polygon": [{"X": 0.8288031816482544, "Y": 0.2973767817020416}, {"X": 0.9523371458053589, "Y": 0.2973787486553192}, {"X": 0.9523366093635559, "Y": 0.31741786003112793}, {"X": 0.8288025856018066, "Y": 0.31741592288017273}]}, "Id": "696eb445-ce30-443c-b403-2e2934dffa6d", "Relationships": [{"Type": "CHILD", "Ids": ["0143e1bb-4ba7-47db-b44b-e5d1fb4b99e6"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Timestamp:", "Geometry": {"BoundingBox": {"Width": 0.2406199723482132, "Height": 0.025651173666119576, "Left": 0.045401863753795624, "Top": 0.3319285213947296}, "Polygon": [{"X": 0.045402318239212036, "Y": 0.3319285213947296}, {"X": 0.2860218286514282, "Y": 0.33193227648735046}, {"X": 0.28602129220962524, "Y": 0.35757970809936523}, {"X": 0.045401863753795624, "Y": 0.3575759828090668}]}, "Id": "70fd7a1f-1a0d-43fd-a5f9-dd6fb4434ae6", "Relationships": [{"Type": "CHILD", "Ids": ["97aeae56-436a-4a8c-ae44-db03e39f2190"]}]}, {"BlockType": "LINE", "Confidence": 99.9085922241211, "Text": "09/22/25 10:16 AM", "Geometry": {"BoundingBox": {"Width": 0.3884803354740143, "Height": 0.02050844021141529, "Left": 0.5632190108299255, "Top": 0.33197376132011414}, "Polygon": [{"X": 0.5632194876670837, "Y": 0.33197376132011414}, {"X": 0.9516993165016174, "Y": 0.33197981119155884}, {"X": 0.9516987204551697, "Y": 0.3524821996688843}, {"X": 0.5632190108299255, "Y": 0.35247620940208435}]}, "Id": "64b291d8-746a-44a5-a329-12352a9ca3f3", "Relationships": [{"Type": "CHILD", "Ids": ["dddc6b5e-269d-4dea-bf8f-efbcabf2282e", "b4704aed-63c1-434a-bf87-1a21135e1ca5", "2ef30e04-dcc0-47d0-83bc-697531f1d2c3"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payment:", "Geometry": {"BoundingBox": {"Width": 0.19090382754802704, "Height": 0.025794850662350655, "Left": 0.04895781725645065, "Top": 0.3670712113380432}, "Polygon": [{"X": 0.048958275467157364, "Y": 0.3670712113380432}, {"X": 0.2398616522550583, "Y": 0.3670741319656372}, {"X": 0.2398611307144165, "Y": 0.3928660750389099}, {"X": 0.04895781725645065, "Y": 0.3928632140159607}]}, "Id": "9ec7bd92-5444-4e31-af5f-32431aaa3b3b", "Relationships": [{"Type": "CHILD", "Ids": ["b1cb9159-a184-48c9-a194-9109ae836123"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "visa", "Geometry": {"BoundingBox": {"Width": 0.08160719275474548, "Height": 0.020181508734822273, "Left": 0.8713672757148743, "Top": 0.3672292232513428}, "Polygon": [{"X": 0.871367871761322, "Y": 0.3672292232513428}, {"X": 0.9529744386672974, "Y": 0.36723047494888306}, {"X": 0.9529738426208496, "Y": 0.3874107301235199}, {"X": 0.8713672757148743, "Y": 0.387409508228302}]}, "Id": "0011f710-8266-4f23-8a79-62c09524bd6b", "Relationships": [{"Type": "CHILD", "Ids": ["9d5bea06-fd3c-41f5-9cf0-433484e625ae"]}]}, {"BlockType": "LINE", "Confidence": 98.9636459350586, "Text": "PO#:", "Geometry": {"BoundingBox": {"Width": 0.09985372424125671, "Height": 0.020289115607738495, "Left": 0.04924589768052101, "Top": 0.****************}, "Polygon": [{"X": 0.049246255308389664, "Y": 0.****************}, {"X": 0.14909961819648743, "Y": 0.****************}, {"X": 0.1490992307662964, "Y": 0.****************}, {"X": 0.04924589768052101, "Y": 0.****************}]}, "Id": "2264b6a5-5e25-4d2b-99bb-7b1f8c62f87b", "Relationships": [{"Type": "CHILD", "Ids": ["2b268cce-cd7d-4470-8d12-e4e39b14549e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1685260", "Geometry": {"BoundingBox": {"Width": 0.17581000924110413, "Height": 0.019978666678071022, "Left": 0.7767528295516968, "Top": 0.40217125415802}, "Polygon": [{"X": 0.7767533659934998, "Y": 0.40217125415802}, {"X": 0.9525628089904785, "Y": 0.40217387676239014}, {"X": 0.9525622129440308, "Y": 0.4221499264240265}, {"X": 0.7767528295516968, "Y": 0.42214733362197876}]}, "Id": "23b5ee0b-a539-4f5d-b49f-b87ac98a82ac", "Relationships": [{"Type": "CHILD", "Ids": ["42b32db3-cd11-4d57-a62b-7db7529af77f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.151025652885437, "Height": 0.020478112623095512, "Left": 0.04802578315138817, "Top": 0.436792254447937}, "Polygon": [{"X": 0.04802614822983742, "Y": 0.436792254447937}, {"X": 0.19905143976211548, "Y": 0.4367944598197937}, {"X": 0.19905103743076324, "Y": 0.45727038383483887}, {"X": 0.04802578315138817, "Y": 0.45726820826530457}]}, "Id": "f93b4175-99fe-4acd-a6d3-6eb6883cc96b", "Relationships": [{"Type": "CHILD", "Ids": ["d2bf8f53-7f96-4932-a071-b23ea0d2eb1c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "RAF TRUCKING", "Geometry": {"BoundingBox": {"Width": 0.3419702351093292, "Height": 0.02034151181578636, "Left": 0.6092404723167419, "Top": 0.4368846118450165}, "Polygon": [{"X": 0.6092409491539001, "Y": 0.4368846118450165}, {"X": 0.9512107372283936, "Y": 0.4368896186351776}, {"X": 0.9512101411819458, "Y": 0.45722612738609314}, {"X": 0.6092404723167419, "Y": 0.4572211802005768}]}, "Id": "7c60ded8-2dde-4df7-8478-d1f23e89df18", "Relationships": [{"Type": "CHILD", "Ids": ["bbe41e82-6e0d-4ed9-9d79-962ddef7e6fe", "6cc4c6d6-07ba-4112-bd4d-14e89ba2edc0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.1085403636097908, "Height": 0.01995547115802765, "Left": 0.04916888475418091, "Top": 0.47232794761657715}, "Polygon": [{"X": 0.04916923865675926, "Y": 0.47232794761657715}, {"X": 0.1577092558145523, "Y": 0.4723294973373413}, {"X": 0.15770886838436127, "Y": 0.4922834038734436}, {"X": 0.04916888475418091, "Y": 0.49228188395500183}]}, "Id": "5eed8faa-df61-4a16-845f-d3da458084fd", "Relationships": [{"Type": "CHILD", "Ids": ["4aeb2b0e-f706-4c4d-a783-2edf225f6f45"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "40", "Geometry": {"BoundingBox": {"Width": 0.0511213093996048, "Height": 0.02014627493917942, "Left": 0.9021452069282532, "Top": 0.4724908471107483}, "Polygon": [{"X": 0.9021457433700562, "Y": 0.4724908471107483}, {"X": 0.9532665014266968, "Y": 0.472491592168808}, {"X": 0.953265905380249, "Y": 0.49263712763786316}, {"X": 0.9021452069282532, "Y": 0.49263641238212585}]}, "Id": "6ddb8b96-ec2c-4d86-a197-3cd573677b63", "Relationships": [{"Type": "CHILD", "Ids": ["da0900ab-858f-44b8-a654-0bc26e197392"]}]}, {"BlockType": "LINE", "Confidence": 99.95565795898438, "Text": "Invoice #:", "Geometry": {"BoundingBox": {"Width": 0.19216813147068024, "Height": 0.02002985216677189, "Left": 0.05013350769877434, "Top": 0.5068502426147461}, "Polygon": [{"X": 0.05013386532664299, "Y": 0.5068502426147461}, {"X": 0.24230164289474487, "Y": 0.506852924823761}, {"X": 0.24230124056339264, "Y": 0.5268800854682922}, {"X": 0.05013350769877434, "Y": 0.5268774628639221}]}, "Id": "b77bef81-1fe7-4e68-87c0-00669564e4fa", "Relationships": [{"Type": "CHILD", "Ids": ["f3829b78-14e9-4032-9601-4054b5577098", "fe21ff4e-7bae-4e57-a7ae-86c09a344f1e"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "162340IDJC", "Geometry": {"BoundingBox": {"Width": 0.2519837021827698, "Height": 0.020059719681739807, "Left": 0.6996123790740967, "Top": 0.506869375705719}, "Polygon": [{"X": 0.6996129155158997, "Y": 0.506869375705719}, {"X": 0.9515961408615112, "Y": 0.5068728923797607}, {"X": 0.9515955448150635, "Y": 0.5269290804862976}, {"X": 0.6996123790740967, "Y": 0.5269255638122559}]}, "Id": "cbf61b58-728f-4b87-95da-a502802d695c", "Relationships": [{"Type": "CHILD", "Ids": ["50a13df6-aef3-4ce7-82d8-af3767a41e25"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Activity:", "Geometry": {"BoundingBox": {"Width": 0.15713314712047577, "Height": 0.025534454733133316, "Left": 0.0466785728931427, "Top": 0.5416256785392761}, "Polygon": [{"X": 0.046679023653268814, "Y": 0.5416256785392761}, {"X": 0.20381172001361847, "Y": 0.541627824306488}, {"X": 0.20381122827529907, "Y": 0.5671601891517639}, {"X": 0.0466785728931427, "Y": 0.567158043384552}]}, "Id": "ac85ba88-e7b1-4b0c-9df4-a3d138fc267e", "Relationships": [{"Type": "CHILD", "Ids": ["12b276e4-b4b7-4080-a16d-3e22ff3b849c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Unloading", "Geometry": {"BoundingBox": {"Width": 0.3234335780143738, "Height": 0.025393052026629448, "Left": 0.04650276154279709, "Top": 0.5763596892356873}, "Polygon": [{"X": 0.0465032123029232, "Y": 0.5763596892356873}, {"X": 0.36993634700775146, "Y": 0.5763640403747559}, {"X": 0.3699357807636261, "Y": 0.601752758026123}, {"X": 0.04650276154279709, "Y": 0.601748526096344}]}, "Id": "85a21cd2-7780-4f65-b0b2-177c881183af", "Relationships": [{"Type": "CHILD", "Ids": ["0ee0aa67-2573-4ec9-b1e3-c000c5a69171", "fd76ab45-510a-4434-988d-a41e99f938f6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$275.00", "Geometry": {"BoundingBox": {"Width": 0.16548076272010803, "Height": 0.024262893944978714, "Left": 0.7870455384254456, "Top": 0.5747923851013184}, "Polygon": [{"X": 0.7870461940765381, "Y": 0.5747923851013184}, {"X": 0.9525262713432312, "Y": 0.5747946500778198}, {"X": 0.9525255560874939, "Y": 0.599055290222168}, {"X": 0.7870455384254456, "Y": 0.599053144454956}]}, "Id": "87c1513e-66b4-4b12-b876-54b646f67fad", "Relationships": [{"Type": "CHILD", "Ids": ["d78877cb-577a-4492-b1c2-a42113964385"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Fee", "Geometry": {"BoundingBox": {"Width": 0.07690847665071487, "Height": 0.02056773751974106, "Left": 0.04910625144839287, "Top": 0.6115281581878662}, "Polygon": [{"X": 0.04910661652684212, "Y": 0.6115281581878662}, {"X": 0.12601472437381744, "Y": 0.6115291714668274}, {"X": 0.1260143518447876, "Y": 0.6320958733558655}, {"X": 0.04910625144839287, "Y": 0.6320949196815491}]}, "Id": "06fd5044-646d-444d-8ead-eba7a4eaf862", "Relationships": [{"Type": "CHILD", "Ids": ["241bf294-76b4-49aa-a5c0-43011c371bf8"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Amount:", "Geometry": {"BoundingBox": {"Width": 0.16983263194561005, "Height": 0.02011696621775627, "Left": 0.04695221409201622, "Top": 0.6814631223678589}, "Polygon": [{"X": 0.046952567994594574, "Y": 0.6814631223678589}, {"X": 0.21678484976291656, "Y": 0.681465208530426}, {"X": 0.21678444743156433, "Y": 0.7015801072120667}, {"X": 0.04695221409201622, "Y": 0.7015780210494995}]}, "Id": "9533e7b8-7e5b-454f-a822-affbca95a096", "Relationships": [{"Type": "CHILD", "Ids": ["93f0a389-a5be-4457-af9d-a1b89e979cf4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$275.00", "Geometry": {"BoundingBox": {"Width": 0.16609638929367065, "Height": 0.024054475128650665, "Left": 0.7865124940872192, "Top": 0.6802257895469666}, "Polygon": [{"X": 0.7865131497383118, "Y": 0.6802257895469666}, {"X": 0.9526088833808899, "Y": 0.6802278161048889}, {"X": 0.9526081681251526, "Y": 0.7042802572250366}, {"X": 0.7865124940872192, "Y": 0.7042782306671143}]}, "Id": "17e78bdf-cefd-4c85-a0d6-928c1a418e7f", "Relationships": [{"Type": "CHILD", "Ids": ["d2e80dfe-be7b-4917-b3bf-0bf98be97181"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Convenience", "Geometry": {"BoundingBox": {"Width": 0.26740023493766785, "Height": 0.020725468173623085, "Left": 0.04850957542657852, "Top": 0.716090202331543}, "Polygon": [{"X": 0.04850994423031807, "Y": 0.716090202331543}, {"X": 0.31590983271598816, "Y": 0.7160934209823608}, {"X": 0.31590938568115234, "Y": 0.7368156909942627}, {"X": 0.04850957542657852, "Y": 0.7368124723434448}]}, "Id": "a2a6d599-b033-4e5c-910a-76a76dd72439", "Relationships": [{"Type": "CHILD", "Ids": ["e9c22813-8c53-4061-b068-234d8bd60334"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$9.62", "Geometry": {"BoundingBox": {"Width": 0.11380720138549805, "Height": 0.02396281808614731, "Left": 0.8386116027832031, "Top": 0.7153252959251404}, "Polygon": [{"X": 0.8386122584342957, "Y": 0.7153252959251404}, {"X": 0.9524188041687012, "Y": 0.7153266668319702}, {"X": 0.9524180889129639, "Y": 0.7392881512641907}, {"X": 0.8386116027832031, "Y": 0.7392867803573608}]}, "Id": "ad84a75f-77fd-4307-a75e-0f96e07aa43f", "Relationships": [{"Type": "CHILD", "Ids": ["2f64a614-c9bb-4743-9f78-1f2a8edbef2a"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Fee:", "Geometry": {"BoundingBox": {"Width": 0.08725167065858841, "Height": 0.020423531532287598, "Left": 0.0495205894112587, "Top": 0.7515762448310852}, "Polygon": [{"X": 0.04952095076441765, "Y": 0.7515762448310852}, {"X": 0.1367722600698471, "Y": 0.7515772581100464}, {"X": 0.13677187263965607, "Y": 0.7719997763633728}, {"X": 0.0495205894112587, "Y": 0.7719987630844116}]}, "Id": "192b5ce5-e38d-4765-8ff3-1ae5b3d4f4a4", "Relationships": [{"Type": "CHILD", "Ids": ["d2412358-0e14-40aa-9e77-815be3bd2288"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Total:", "Geometry": {"BoundingBox": {"Width": 0.11416375637054443, "Height": 0.020603202283382416, "Left": 0.046385571360588074, "Top": 0.7859847545623779}, "Polygon": [{"X": 0.04638593643903732, "Y": 0.7859847545623779}, {"X": 0.1605493277311325, "Y": 0.785986065864563}, {"X": 0.16054894030094147, "Y": 0.8065879940986633}, {"X": 0.046385571360588074, "Y": 0.8065866827964783}]}, "Id": "ce501329-8d58-4eb1-a553-c44ac5db1160", "Relationships": [{"Type": "CHILD", "Ids": ["9f9f22d3-9e3b-4e1f-9eb5-e3bc360f9d31"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$284.62", "Geometry": {"BoundingBox": {"Width": 0.16663096845149994, "Height": 0.024378420785069466, "Left": 0.7860877513885498, "Top": 0.7845718860626221}, "Polygon": [{"X": 0.7860884070396423, "Y": 0.7845718860626221}, {"X": 0.9527186751365662, "Y": 0.7845737934112549}, {"X": 0.9527179598808289, "Y": 0.8089503049850464}, {"X": 0.7860877513885498, "Y": 0.8089484572410583}]}, "Id": "5ccad9a9-8295-4d89-b0aa-733b79bfc31c", "Relationships": [{"Type": "CHILD", "Ids": ["94ea6aa6-330a-4264-ae94-e1eb611cf31a"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TWS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10219911485910416, "Height": 0.02038721926510334, "Left": 0.28362569212913513, "Top": 0.05255165323615074}, "Polygon": [{"X": 0.28362610936164856, "Y": 0.05255165323615074}, {"X": 0.3858247995376587, "Y": 0.05255351588129997}, {"X": 0.3858243525028229, "Y": 0.07293887436389923}, {"X": 0.28362569212913513, "Y": 0.07293703407049179}]}, "Id": "728adc16-db8b-4537-af62-92ba9a6e6a0b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03912283107638359, "Height": 0.020266981795430183, "Left": 0.4003802537918091, "Top": 0.05243661254644394}, "Polygon": [{"X": 0.4003807306289673, "Y": 0.05243661254644394}, {"X": 0.43950310349464417, "Y": 0.052437324076890945}, {"X": 0.43950262665748596, "Y": 0.07270359247922897}, {"X": 0.4003802537918091, "Y": 0.07270289212465286}]}, "Id": "cca04e4e-0eaa-4892-91a7-2b41856d54d5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16742931306362152, "Height": 0.02041647396981716, "Left": 0.4518624246120453, "Top": 0.05241810530424118}, "Polygon": [{"X": 0.4518629014492035, "Y": 0.05241810530424118}, {"X": 0.6192917227745056, "Y": 0.05242115259170532}, {"X": 0.6192912459373474, "Y": 0.07283458113670349}, {"X": 0.4518624246120453, "Y": 0.07283156365156174}]}, "Id": "a4d51f6f-89df-4d96-8a8d-c0774f9b42ba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08085392415523529, "Height": 0.01996985822916031, "Left": 0.634029746055603, "Top": 0.05277734249830246}, "Polygon": [{"X": 0.6340302228927612, "Y": 0.05277734249830246}, {"X": 0.7148836851119995, "Y": 0.05277881398797035}, {"X": 0.7148831486701965, "Y": 0.07274720072746277}, {"X": 0.634029746055603, "Y": 0.07274574041366577}]}, "Id": "dfd4a26e-a093-4280-85fb-48099962eacb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "855-897-9675", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2865448594093323, "Height": 0.01968085952103138, "Left": 0.35689473152160645, "Top": 0.12296231836080551}, "Polygon": [{"X": 0.35689517855644226, "Y": 0.12296231836080551}, {"X": 0.6434395909309387, "Y": 0.12296734750270844}, {"X": 0.6434391140937805, "Y": 0.14264318346977234}, {"X": 0.35689473152160645, "Y": 0.1426382064819336}]}, "Id": "30647910-ed31-4d42-a903-e645bd7916a3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Warehouse:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2477707862854004, "Height": 0.0206758975982666, "Left": 0.0468941405415535, "Top": 0.19191275537014008}, "Polygon": [{"X": 0.04689450562000275, "Y": 0.19191275537014008}, {"X": 0.2946649193763733, "Y": 0.19191695749759674}, {"X": 0.29466450214385986, "Y": 0.21258865296840668}, {"X": 0.0468941405415535, "Y": 0.2125845104455948}]}, "Id": "bd469d2b-c084-449c-908b-714d3ba93d07"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Imperial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15993745625019073, "Height": 0.025321342051029205, "Left": 0.4911980628967285, "Top": 0.19209221005439758}, "Polygon": [{"X": 0.49119865894317627, "Y": 0.19209221005439758}, {"X": 0.6511355042457581, "Y": 0.19209492206573486}, {"X": 0.6511349081993103, "Y": 0.21741355955600739}, {"X": 0.4911980628967285, "Y": 0.2174108922481537}]}, "Id": "57371c59-15d2-4971-9114-2f3467f230b7"}, {"BlockType": "WORD", "Confidence": 78.50665283203125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12515799701213837, "Height": 0.020282965153455734, "Left": 0.6675956845283508, "Top": 0.19187578558921814}, "Polygon": [{"X": 0.6675962209701538, "Y": 0.19187578558921814}, {"X": 0.7927536964416504, "Y": 0.19187790155410767}, {"X": 0.7927531599998474, "Y": 0.21215875446796417}, {"X": 0.6675956845283508, "Y": 0.21215665340423584}]}, "Id": "ddfca0f8-3cc2-45f2-b452-eeab39d6c2df"}, {"BlockType": "WORD", "Confidence": 87.80363464355469, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014187537133693695, "Height": 0.0041177039965987206, "Left": 0.7898546457290649, "Top": 0.20202526450157166}, "Polygon": [{"X": 0.7898547649383545, "Y": 0.20202526450157166}, {"X": 0.8040421605110168, "Y": 0.20202550292015076}, {"X": 0.8040421009063721, "Y": 0.20614296197891235}, {"X": 0.7898546457290649, "Y": 0.20614272356033325}]}, "Id": "ddfcc2e7-7a37-4035-b617-0979508b5040"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Jersey", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13754507899284363, "Height": 0.02505900152027607, "Left": 0.81682288646698, "Top": 0.19233360886573792}, "Polygon": [{"X": 0.8168236017227173, "Y": 0.19233360886573792}, {"X": 0.954367995262146, "Y": 0.19233593344688416}, {"X": 0.9543672204017639, "Y": 0.21739260852336884}, {"X": 0.81682288646698, "Y": 0.21739031374454498}]}, "Id": "4d7fe4e6-9882-4e1f-aa7f-d458917fd2b5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "City,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08987826108932495, "Height": 0.025522204115986824, "Left": 0.7923925518989563, "Top": 0.22694365680217743}, "Polygon": [{"X": 0.7923932671546936, "Y": 0.22694365680217743}, {"X": 0.8822708129882812, "Y": 0.22694514691829681}, {"X": 0.882270097732544, "Y": 0.2524658739566803}, {"X": 0.7923925518989563, "Y": 0.2524643838405609}]}, "Id": "8645dbc2-2581-44da-a123-04a9b070a50c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "NJ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052933357656002045, "Height": 0.020317789167165756, "Left": 0.8993375301361084, "Top": 0.22716215252876282}, "Polygon": [{"X": 0.8993381261825562, "Y": 0.22716215252876282}, {"X": 0.9522708654403687, "Y": 0.22716303169727325}, {"X": 0.9522702693939209, "Y": 0.24747993052005768}, {"X": 0.8993375301361084, "Y": 0.24747906625270844}]}, "Id": "3c827174-75a2-487a-a435-9f843a0d166c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0779232606291771, "Height": 0.019880492240190506, "Left": 0.04640492796897888, "Top": 0.2623896598815918}, "Polygon": [{"X": 0.046405281871557236, "Y": 0.2623896598815918}, {"X": 0.12432819604873657, "Y": 0.26239094138145447}, {"X": 0.12432782351970673, "Y": 0.2822701632976532}, {"X": 0.04640492796897888, "Y": 0.2822689116001129}]}, "Id": "90e88f7b-3612-46d4-8121-88bb9bba7c73"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05400972440838814, "Height": 0.02031465619802475, "Left": 0.13968752324581146, "Top": 0.2621653974056244}, "Polygon": [{"X": 0.1396879106760025, "Y": 0.2621653974056244}, {"X": 0.1936972439289093, "Y": 0.262166291475296}, {"X": 0.19369685649871826, "Y": 0.28248006105422974}, {"X": 0.13968752324581146, "Y": 0.2824791967868805}]}, "Id": "4fa9e3f1-ff22-4a25-b05f-1b0f700064c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "46-5443689", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24588635563850403, "Height": 0.019972719252109528, "Left": 0.7064048647880554, "Top": 0.2627529203891754}, "Polygon": [{"X": 0.7064054012298584, "Y": 0.2627529203891754}, {"X": 0.9522911906242371, "Y": 0.26275694370269775}, {"X": 0.9522905945777893, "Y": 0.28272566199302673}, {"X": 0.7064048647880554, "Y": 0.28272169828414917}]}, "Id": "58305198-e61f-40ce-a264-40e4282ed1e3"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "Receipt#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19017890095710754, "Height": 0.025243347510695457, "Left": 0.04932868853211403, "Top": 0.29688823223114014}, "Polygon": [{"X": 0.049329135566949844, "Y": 0.29688823223114014}, {"X": 0.23950758576393127, "Y": 0.2968912720680237}, {"X": 0.23950707912445068, "Y": 0.32213157415390015}, {"X": 0.04932868853211403, "Y": 0.3221285939216614}]}, "Id": "8eefb859-c334-4c33-a75b-4464c99dba4a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "15323", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12353456020355225, "Height": 0.02004109136760235, "Left": 0.8288025856018066, "Top": 0.2973767817020416}, "Polygon": [{"X": 0.8288031816482544, "Y": 0.2973767817020416}, {"X": 0.9523371458053589, "Y": 0.2973787486553192}, {"X": 0.9523366093635559, "Y": 0.31741786003112793}, {"X": 0.8288025856018066, "Y": 0.31741592288017273}]}, "Id": "0143e1bb-4ba7-47db-b44b-e5d1fb4b99e6"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Timestamp:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2406199723482132, "Height": 0.025651173666119576, "Left": 0.045401863753795624, "Top": 0.3319285213947296}, "Polygon": [{"X": 0.045402318239212036, "Y": 0.3319285213947296}, {"X": 0.2860218286514282, "Y": 0.33193227648735046}, {"X": 0.28602129220962524, "Y": 0.35757970809936523}, {"X": 0.045401863753795624, "Y": 0.3575759828090668}]}, "Id": "97aeae56-436a-4a8c-ae44-db03e39f2190"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/22/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17825496196746826, "Height": 0.02050519920885563, "Left": 0.5632190108299255, "Top": 0.33197376132011414}, "Polygon": [{"X": 0.5632194876670837, "Y": 0.33197376132011414}, {"X": 0.7414739727973938, "Y": 0.3319765329360962}, {"X": 0.741473376750946, "Y": 0.352478951215744}, {"X": 0.5632190108299255, "Y": 0.35247620940208435}]}, "Id": "dddc6b5e-269d-4dea-bf8f-efbcabf2282e"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "10:16", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1118757575750351, "Height": 0.019896941259503365, "Left": 0.7589481472969055, "Top": 0.33255937695503235}, "Polygon": [{"X": 0.7589486837387085, "Y": 0.33255937695503235}, {"X": 0.8708239197731018, "Y": 0.33256110548973083}, {"X": 0.870823323726654, "Y": 0.35245630145072937}, {"X": 0.7589481472969055, "Y": 0.3524545729160309}]}, "Id": "b4704aed-63c1-434a-bf87-1a21135e1ca5"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06789843738079071, "Height": 0.019940247759222984, "Left": 0.8838008642196655, "Top": 0.332145094871521}, "Polygon": [{"X": 0.8838014602661133, "Y": 0.332145094871521}, {"X": 0.9516993165016174, "Y": 0.33214616775512695}, {"X": 0.9516987204551697, "Y": 0.3520853519439697}, {"X": 0.8838008642196655, "Y": 0.35208430886268616}]}, "Id": "2ef30e04-dcc0-47d0-83bc-697531f1d2c3"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payment:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19090382754802704, "Height": 0.025794850662350655, "Left": 0.04895781725645065, "Top": 0.3670712113380432}, "Polygon": [{"X": 0.048958275467157364, "Y": 0.3670712113380432}, {"X": 0.2398616522550583, "Y": 0.3670741319656372}, {"X": 0.2398611307144165, "Y": 0.3928660750389099}, {"X": 0.04895781725645065, "Y": 0.3928632140159607}]}, "Id": "b1cb9159-a184-48c9-a194-9109ae836123"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "visa", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08160719275474548, "Height": 0.020181508734822273, "Left": 0.8713672757148743, "Top": 0.3672292232513428}, "Polygon": [{"X": 0.871367871761322, "Y": 0.3672292232513428}, {"X": 0.9529744386672974, "Y": 0.36723047494888306}, {"X": 0.9529738426208496, "Y": 0.3874107301235199}, {"X": 0.8713672757148743, "Y": 0.387409508228302}]}, "Id": "9d5bea06-fd3c-41f5-9cf0-433484e625ae"}, {"BlockType": "WORD", "Confidence": 98.9636459350586, "Text": "PO#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09985372424125671, "Height": 0.020289115607738495, "Left": 0.04924589768052101, "Top": 0.****************}, "Polygon": [{"X": 0.049246255308389664, "Y": 0.****************}, {"X": 0.14909961819648743, "Y": 0.****************}, {"X": 0.1490992307662964, "Y": 0.****************}, {"X": 0.04924589768052101, "Y": 0.****************}]}, "Id": "2b268cce-cd7d-4470-8d12-e4e39b14549e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1685260", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17581000924110413, "Height": 0.019978666678071022, "Left": 0.7767528295516968, "Top": 0.40217125415802}, "Polygon": [{"X": 0.7767533659934998, "Y": 0.40217125415802}, {"X": 0.9525628089904785, "Y": 0.40217387676239014}, {"X": 0.9525622129440308, "Y": 0.4221499264240265}, {"X": 0.7767528295516968, "Y": 0.42214733362197876}]}, "Id": "42b32db3-cd11-4d57-a62b-7db7529af77f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.151025652885437, "Height": 0.020478112623095512, "Left": 0.04802578315138817, "Top": 0.436792254447937}, "Polygon": [{"X": 0.04802614822983742, "Y": 0.436792254447937}, {"X": 0.19905143976211548, "Y": 0.4367944598197937}, {"X": 0.19905103743076324, "Y": 0.45727038383483887}, {"X": 0.04802578315138817, "Y": 0.45726820826530457}]}, "Id": "d2bf8f53-7f96-4932-a071-b23ea0d2eb1c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RAF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0889538899064064, "Height": 0.02009454183280468, "Left": 0.6092404723167419, "Top": 0.43695178627967834}, "Polygon": [{"X": 0.6092409491539001, "Y": 0.43695178627967834}, {"X": 0.6981943845748901, "Y": 0.4369530975818634}, {"X": 0.6981938481330872, "Y": 0.4570463299751282}, {"X": 0.6092404723167419, "Y": 0.4570450484752655}]}, "Id": "bbe41e82-6e0d-4ed9-9d79-962ddef7e6fe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRUCKING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24076411128044128, "Height": 0.020340031012892723, "Left": 0.7104465961456299, "Top": 0.43688610196113586}, "Polygon": [{"X": 0.7104471325874329, "Y": 0.43688610196113586}, {"X": 0.9512107372283936, "Y": 0.4368896186351776}, {"X": 0.9512101411819458, "Y": 0.45722612738609314}, {"X": 0.7104465961456299, "Y": 0.4572226405143738}]}, "Id": "6cc4c6d6-07ba-4112-bd4d-14e89ba2edc0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1085403636097908, "Height": 0.01995547115802765, "Left": 0.04916888475418091, "Top": 0.47232794761657715}, "Polygon": [{"X": 0.04916923865675926, "Y": 0.47232794761657715}, {"X": 0.1577092558145523, "Y": 0.4723294973373413}, {"X": 0.15770886838436127, "Y": 0.4922834038734436}, {"X": 0.04916888475418091, "Y": 0.49228188395500183}]}, "Id": "4aeb2b0e-f706-4c4d-a783-2edf225f6f45"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0511213093996048, "Height": 0.02014627493917942, "Left": 0.9021452069282532, "Top": 0.4724908471107483}, "Polygon": [{"X": 0.9021457433700562, "Y": 0.4724908471107483}, {"X": 0.9532665014266968, "Y": 0.472491592168808}, {"X": 0.953265905380249, "Y": 0.49263712763786316}, {"X": 0.9021452069282532, "Y": 0.49263641238212585}]}, "Id": "da0900ab-858f-44b8-a654-0bc26e197392"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14271213114261627, "Height": 0.019988253712654114, "Left": 0.05013350769877434, "Top": 0.5068502426147461}, "Polygon": [{"X": 0.05013386532664299, "Y": 0.5068502426147461}, {"X": 0.1928456425666809, "Y": 0.5068522095680237}, {"X": 0.19284524023532867, "Y": 0.526838481426239}, {"X": 0.05013350769877434, "Y": 0.5268365144729614}]}, "Id": "f3829b78-14e9-4032-9601-4054b5577098"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036155834794044495, "Height": 0.0196747537702322, "Left": 0.20614579319953918, "Top": 0.5072053670883179}, "Polygon": [{"X": 0.20614618062973022, "Y": 0.5072053670883179}, {"X": 0.24230162799358368, "Y": 0.5072058439254761}, {"X": 0.24230124056339264, "Y": 0.5268800854682922}, {"X": 0.20614579319953918, "Y": 0.526879608631134}]}, "Id": "fe21ff4e-7bae-4e57-a7ae-86c09a344f1e"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "162340IDJC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2519837021827698, "Height": 0.020059719681739807, "Left": 0.6996123790740967, "Top": 0.506869375705719}, "Polygon": [{"X": 0.6996129155158997, "Y": 0.506869375705719}, {"X": 0.9515961408615112, "Y": 0.5068728923797607}, {"X": 0.9515955448150635, "Y": 0.5269290804862976}, {"X": 0.6996123790740967, "Y": 0.5269255638122559}]}, "Id": "50a13df6-aef3-4ce7-82d8-af3767a41e25"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Activity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15713314712047577, "Height": 0.025534454733133316, "Left": 0.0466785728931427, "Top": 0.5416256785392761}, "Polygon": [{"X": 0.046679023653268814, "Y": 0.5416256785392761}, {"X": 0.20381172001361847, "Y": 0.541627824306488}, {"X": 0.20381122827529907, "Y": 0.5671601891517639}, {"X": 0.0466785728931427, "Y": 0.567158043384552}]}, "Id": "12b276e4-b4b7-4080-a16d-3e22ff3b849c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10156842321157455, "Height": 0.020123258233070374, "Left": 0.04650285467505455, "Top": 0.5764358639717102}, "Polygon": [{"X": 0.0465032123029232, "Y": 0.5764358639717102}, {"X": 0.1480712741613388, "Y": 0.57643723487854}, {"X": 0.14807090163230896, "Y": 0.5965591669082642}, {"X": 0.04650285467505455, "Y": 0.5965577960014343}]}, "Id": "0ee0aa67-2573-4ec9-b1e3-c000c5a69171"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Unloading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20427227020263672, "Height": 0.025391461327672005, "Left": 0.16566406190395355, "Top": 0.5763612985610962}, "Polygon": [{"X": 0.16566455364227295, "Y": 0.5763612985610962}, {"X": 0.36993634700775146, "Y": 0.5763640403747559}, {"X": 0.3699357807636261, "Y": 0.601752758026123}, {"X": 0.16566406190395355, "Y": 0.6017500758171082}]}, "Id": "fd76ab45-510a-4434-988d-a41e99f938f6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$275.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16548076272010803, "Height": 0.024262893944978714, "Left": 0.7870455384254456, "Top": 0.5747923851013184}, "Polygon": [{"X": 0.7870461940765381, "Y": 0.5747923851013184}, {"X": 0.9525262713432312, "Y": 0.5747946500778198}, {"X": 0.9525255560874939, "Y": 0.599055290222168}, {"X": 0.7870455384254456, "Y": 0.599053144454956}]}, "Id": "d78877cb-577a-4492-b1c2-a42113964385"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07690847665071487, "Height": 0.02056773751974106, "Left": 0.04910625144839287, "Top": 0.6115281581878662}, "Polygon": [{"X": 0.04910661652684212, "Y": 0.6115281581878662}, {"X": 0.12601472437381744, "Y": 0.6115291714668274}, {"X": 0.1260143518447876, "Y": 0.6320958733558655}, {"X": 0.04910625144839287, "Y": 0.6320949196815491}]}, "Id": "241bf294-76b4-49aa-a5c0-43011c371bf8"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Amount:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16983263194561005, "Height": 0.02011696621775627, "Left": 0.04695221409201622, "Top": 0.6814631223678589}, "Polygon": [{"X": 0.046952567994594574, "Y": 0.6814631223678589}, {"X": 0.21678484976291656, "Y": 0.681465208530426}, {"X": 0.21678444743156433, "Y": 0.7015801072120667}, {"X": 0.04695221409201622, "Y": 0.7015780210494995}]}, "Id": "93f0a389-a5be-4457-af9d-a1b89e979cf4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$275.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16609638929367065, "Height": 0.024054475128650665, "Left": 0.7865124940872192, "Top": 0.6802257895469666}, "Polygon": [{"X": 0.7865131497383118, "Y": 0.6802257895469666}, {"X": 0.9526088833808899, "Y": 0.6802278161048889}, {"X": 0.9526081681251526, "Y": 0.7042802572250366}, {"X": 0.7865124940872192, "Y": 0.7042782306671143}]}, "Id": "d2e80dfe-be7b-4917-b3bf-0bf98be97181"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.26740023493766785, "Height": 0.020725468173623085, "Left": 0.04850957542657852, "Top": 0.716090202331543}, "Polygon": [{"X": 0.04850994423031807, "Y": 0.716090202331543}, {"X": 0.31590983271598816, "Y": 0.7160934209823608}, {"X": 0.31590938568115234, "Y": 0.7368156909942627}, {"X": 0.04850957542657852, "Y": 0.7368124723434448}]}, "Id": "e9c22813-8c53-4061-b068-234d8bd60334"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$9.62", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11380720138549805, "Height": 0.02396281808614731, "Left": 0.8386116027832031, "Top": 0.7153252959251404}, "Polygon": [{"X": 0.8386122584342957, "Y": 0.7153252959251404}, {"X": 0.9524188041687012, "Y": 0.7153266668319702}, {"X": 0.9524180889129639, "Y": 0.7392881512641907}, {"X": 0.8386116027832031, "Y": 0.7392867803573608}]}, "Id": "2f64a614-c9bb-4743-9f78-1f2a8edbef2a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08725167065858841, "Height": 0.020423531532287598, "Left": 0.0495205894112587, "Top": 0.7515762448310852}, "Polygon": [{"X": 0.04952095076441765, "Y": 0.7515762448310852}, {"X": 0.1367722600698471, "Y": 0.7515772581100464}, {"X": 0.13677187263965607, "Y": 0.7719997763633728}, {"X": 0.0495205894112587, "Y": 0.7719987630844116}]}, "Id": "d2412358-0e14-40aa-9e77-815be3bd2288"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Total:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11416375637054443, "Height": 0.020603202283382416, "Left": 0.046385571360588074, "Top": 0.7859847545623779}, "Polygon": [{"X": 0.04638593643903732, "Y": 0.7859847545623779}, {"X": 0.1605493277311325, "Y": 0.785986065864563}, {"X": 0.16054894030094147, "Y": 0.8065879940986633}, {"X": 0.046385571360588074, "Y": 0.8065866827964783}]}, "Id": "9f9f22d3-9e3b-4e1f-9eb5-e3bc360f9d31"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$284.62", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16663096845149994, "Height": 0.024378420785069466, "Left": 0.7860877513885498, "Top": 0.7845718860626221}, "Polygon": [{"X": 0.7860884070396423, "Y": 0.7845718860626221}, {"X": 0.9527186751365662, "Y": 0.7845737934112549}, {"X": 0.9527179598808289, "Y": 0.8089503049850464}, {"X": 0.7860877513885498, "Y": 0.8089484572410583}]}, "Id": "94ea6aa6-330a-4264-ae94-e1eb611cf31a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "483c2aa5-917a-4b83-be16-bcad6ebe59b5", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "483c2aa5-917a-4b83-be16-bcad6ebe59b5", "content-type": "application/x-amz-json-1.1", "content-length": "40445", "date": "Wed, 24 Sep 2025 13:26:49 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "b8bdf1f5-0877-41ed-8278-1f210eb597b0", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:52 GMT", "content-type": "application/json", "content-length": "1067", "connection": "keep-alive", "x-amzn-requestid": "b8bdf1f5-0877-41ed-8278-1f210eb597b0"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Here we have only page1 content. We need to determine doc_type. The text shows Receipt, Payment, PO#, Carrier, Activity, etc. This looks like a lumper receipt? Let's review: lumper receipt keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO#, Door, Carrier, Activity: Total Unloading, Fee, Amount, Convenience Fee. It is a receipt for loading/unloading labor. So lumper_receipt. Also invoice? But invoice would have more billing details. This is a receipt. So doc_type = lumper_receipt.\n\nWe output:\n\n{\n \"documents\": [\n   {\"page_no\": 1, \"doc_type\": \"lumper_receipt\"}\n ]\n}\n\nWe must use tool classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"lumper_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2711, "outputTokens": 196, "totalTokens": 2907}, "metrics": {"latencyMs": 1007}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/675c1678_O1YJBQBLYAU6D0SDDKAU.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nTWS of Carteret LLC\n855-897-9675\nWarehouse:\nImperial Dade - Jersey\nCity, NJ\nTax ID:\n46-5443689\nReceipt#:\n15323\nTimestamp:\n09/22/25 10:16 AM\nPayment:\nvisa\nPO#:\n1685260\nCarrier:\nRAF TRUCKING\nDoor:\n40\nInvoice #:\n162340IDJC\nActivity:\nTotal Unloading\n$275.00\nFee\nAmount:\n$275.00\nConvenience\n$9.62\nFee:\nTotal:\n$284.62\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}