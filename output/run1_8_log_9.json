{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.5933798280798328e-08, "Y": 0.0}, {"X": 1.0, "Y": 2.698072648854577e-06}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "842400aa-09b2-4d1f-87e1-4b2df61c3a2b", "Relationships": [{"Type": "CHILD", "Ids": ["b6ed3f1c-9361-4b9d-b4f3-bdf5c8985018", "060c0e08-1f16-422c-beef-40692cfd183d", "d712fa5b-8ddb-4c0c-a3cf-e54d83da4cc5", "a8a665c8-33bb-4bac-926c-e675e48ed1b0", "0eb1bdf4-2fa1-4a2d-a466-7d8f8c96d2c1", "40a45c35-9a69-480c-b0b2-a5266cc12cf0", "7764a0be-d450-49e8-806b-052a9f1cba49", "c58a2ce1-8e0e-47a5-a424-ac7b30a5a4fd", "58e16a6a-b8ed-4b1d-bb3a-9b83ddc25846", "d3635f9f-c650-4220-8062-92d586f97a7a", "fbc1bfde-e239-4445-9005-4ad0a10e8ddb", "44f66b7c-439e-4161-b23a-e7d04c84dc54", "e4491dea-80bc-4cd8-9ead-7d8c00fe8816", "d62d6d25-1379-4eb1-8e1f-ebd0465f0b83", "c045f241-656a-4695-834c-d2c979d1b78b", "fac81615-51ce-4bd3-9324-d18bf2af3666", "3194c7cc-1199-4cff-9e62-07e372fce292", "74981f45-f960-4e77-9471-cd5fb26f7978", "8106eb6e-cc43-4fea-9c1b-0c95cc797ea3", "7fcc38d5-98cb-4580-b455-a450cf356abf", "c5c504dd-2a13-46c7-a55f-23297a395337", "c7880a32-2256-4bc3-8b34-d5760a7cc9f3", "b203dbef-e5f7-4fa0-ba20-0e981ad15ed9", "5ca3839e-e79e-47e2-899f-9c532e4a8a1c", "1b7c567a-65d2-4aec-a073-554171714288", "e8c9c6c4-0b91-49e6-880e-13fa260bfacd", "e580a4e8-653a-4594-97e4-b2303f78b89e", "cc378b66-c6f1-4a01-aa52-d8def52127db", "897951f0-27d7-4f73-8831-a2bcdb9743cf", "8615c55c-7da0-407b-bb23-1bc51adc9a74", "ce5233fc-1dbc-4a7f-b74c-b8d0546157ec", "e05ce3f5-4fb1-4070-94b2-c088dc7f1401", "446534bb-76bb-4c35-9d7e-cd8c248087f2", "f6028319-f4f3-452a-897e-5e7e67eddfa7", "8aaf7e9a-1a1e-4878-87a0-85e4837ddcc6", "654bb838-bc36-4578-a260-6bece9fdfc85", "f32ddaa3-6292-42c4-b832-a89f9a7978d7", "8cf07c18-c906-428f-b22d-bb0196e28364", "51cc128f-a837-424a-b9e4-524b674635a3", "0a680cd3-fafa-4c8d-991b-192f6af5621e"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "DATE: 13-Jun", "Geometry": {"BoundingBox": {"Width": 0.13579200208187103, "Height": 0.014356535859405994, "Left": 0.2523845136165619, "Top": 0.007132180966436863}, "Polygon": [{"X": 0.25238466262817383, "Y": 0.007132180966436863}, {"X": 0.38817650079727173, "Y": 0.007133452221751213}, {"X": 0.3881763219833374, "Y": 0.021488716825842857}, {"X": 0.2523845136165619, "Y": 0.021487466990947723}]}, "Id": "b6ed3f1c-9361-4b9d-b4f3-bdf5c8985018", "Relationships": [{"Type": "CHILD", "Ids": ["a87cca09-a28e-4024-b95e-5ce2db717f97", "a7bdcb01-fc9e-4a10-a5ab-c39cf98a0236"]}]}, {"BlockType": "LINE", "Confidence": 99.43983459472656, "Text": "UNIT #: 6063/283420", "Geometry": {"BoundingBox": {"Width": 0.20881959795951843, "Height": 0.015228815376758575, "Left": 0.2356344759464264, "Top": 0.043498579412698746}, "Polygon": [{"X": 0.23563463985919952, "Y": 0.043498579412698746}, {"X": 0.4444540739059448, "Y": 0.04350045323371887}, {"X": 0.4444538652896881, "Y": 0.05872739478945732}, {"X": 0.2356344759464264, "Y": 0.05872555449604988}]}, "Id": "060c0e08-1f16-422c-beef-40692cfd183d", "Relationships": [{"Type": "CHILD", "Ids": ["45f4eb74-6d05-4061-a910-5592df0be600", "c7c66a0b-7a81-4e82-8956-5d95d12c2aec", "4d0f9584-27ca-416a-8291-d7bc3e48a04f"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Truck Ready Time: 5:30", "Geometry": {"BoundingBox": {"Width": 0.2880890965461731, "Height": 0.02247079834342003, "Left": 0.4741477966308594, "Top": 0.054887983947992325}, "Polygon": [{"X": 0.47414809465408325, "Y": 0.054887983947992325}, {"X": 0.7622368931770325, "Y": 0.05489053204655647}, {"X": 0.762236475944519, "Y": 0.07735878229141235}, {"X": 0.4741477966308594, "Y": 0.07735630124807358}]}, "Id": "d712fa5b-8ddb-4c0c-a3cf-e54d83da4cc5", "Relationships": [{"Type": "CHILD", "Ids": ["7743f73d-2491-4719-80c9-16f49f18ee9e", "7389d537-9944-444c-933f-f04a6bfaa1d6", "0224b984-4698-4d4a-9f79-5da6124c3432", "c7fa0973-6d3c-49cd-9393-fcb305d2ba6e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.16430693864822388, "Height": 0.013105669058859348, "Left": 0.2256195843219757, "Top": 0.07953045517206192}, "Polygon": [{"X": 0.22561973333358765, "Y": 0.07953045517206192}, {"X": 0.3899265229701996, "Y": 0.07953187078237534}, {"X": 0.38992637395858765, "Y": 0.09263613075017929}, {"X": 0.2256195843219757, "Y": 0.09263473749160767}]}, "Id": "a8a665c8-33bb-4bac-926c-e675e48ed1b0", "Relationships": [{"Type": "CHILD", "Ids": ["07835b16-cf29-492d-97f0-19eae362995c", "eb81d93d-294f-412b-9962-13b01a3f3798"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Truck Departure: 5:30", "Geometry": {"BoundingBox": {"Width": 0.2839870750904083, "Height": 0.02239825390279293, "Left": 0.4746589660644531, "Top": 0.09066174179315567}, "Polygon": [{"X": 0.474659264087677, "Y": 0.09066174179315567}, {"X": 0.7586460709571838, "Y": 0.09066415578126907}, {"X": 0.7586456537246704, "Y": 0.11305999755859375}, {"X": 0.4746589660644531, "Y": 0.11305765807628632}]}, "Id": "0eb1bdf4-2fa1-4a2d-a466-7d8f8c96d2c1", "Relationships": [{"Type": "CHILD", "Ids": ["dd04eaf2-ea99-4da8-b28e-12d1ee066c7a", "c4248374-a162-4f7c-ab3d-07165e059e93", "2dcabc47-2a78-4634-b07c-4fe14c5d809d"]}]}, {"BlockType": "LINE", "Confidence": 99.765625, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.23332542181015015, "Height": 0.013665849342942238, "Left": 0.13355939090251923, "Top": 0.11536646634340286}, "Polygon": [{"X": 0.13355952501296997, "Y": 0.11536646634340286}, {"X": 0.36688482761383057, "Y": 0.11536838114261627}, {"X": 0.36688464879989624, "Y": 0.12903231382369995}, {"X": 0.13355939090251923, "Y": 0.12903043627738953}]}, "Id": "40a45c35-9a69-480c-b0b2-a5266cc12cf0", "Relationships": [{"Type": "CHILD", "Ids": ["7e9a17b8-780f-479f-a7a9-98ca9c0b6a2d", "5b3bcabd-0cd1-481f-8a80-810c0630a10b", "b6b0e104-67c0-46ed-9a26-e3d66654f616"]}]}, {"BlockType": "LINE", "Confidence": 99.71121215820312, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.04843425378203392, "Height": 0.011299011297523975, "Left": 0.33933189511299133, "Top": 0.14802676439285278}, "Polygon": [{"X": 0.33933204412460327, "Y": 0.14802676439285278}, {"X": 0.38776615262031555, "Y": 0.14802713692188263}, {"X": 0.3877660036087036, "Y": 0.1593257635831833}, {"X": 0.33933189511299133, "Y": 0.15932539105415344}]}, "Id": "7764a0be-d450-49e8-806b-052a9f1cba49", "Relationships": [{"Type": "CHILD", "Ids": ["dbaa1ca0-b182-4e6b-9061-41e61780500d"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.09505943208932877, "Height": 0.012269191443920135, "Left": 0.8364191651344299, "Top": 0.15956756472587585}, "Polygon": [{"X": 0.836419403553009, "Y": 0.15956756472587585}, {"X": 0.9314786195755005, "Y": 0.15956830978393555}, {"X": 0.9314783811569214, "Y": 0.1718367636203766}, {"X": 0.8364191651344299, "Y": 0.1718360334634781}]}, "Id": "c58a2ce1-8e0e-47a5-a424-ac7b30a5a4fd", "Relationships": [{"Type": "CHILD", "Ids": ["e348ae11-1d90-434a-bab8-c3579f82d814", "d767e75c-7797-47da-9524-4653095a5248"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.10112185776233673, "Height": 0.013659371994435787, "Left": 0.16423135995864868, "Top": 0.1680711805820465}, "Polygon": [{"X": 0.16423150897026062, "Y": 0.1680711805820465}, {"X": 0.2653532326221466, "Y": 0.1680719554424286}, {"X": 0.2653530538082123, "Y": 0.18173055350780487}, {"X": 0.16423135995864868, "Y": 0.18172979354858398}]}, "Id": "58e16a6a-b8ed-4b1d-bb3a-9b83ddc25846", "Relationships": [{"Type": "CHILD", "Ids": ["9f7064a5-b575-4ed1-a51f-6c7003f29da7"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "UP DEL", "Geometry": {"BoundingBox": {"Width": 0.10160849243402481, "Height": 0.013223511166870594, "Left": 0.35173434019088745, "Top": 0.16650831699371338}, "Polygon": [{"X": 0.3517344892024994, "Y": 0.16650831699371338}, {"X": 0.45334282517433167, "Y": 0.16650910675525665}, {"X": 0.45334264636039734, "Y": 0.1797318309545517}, {"X": 0.35173434019088745, "Y": 0.1797310709953308}]}, "Id": "d3635f9f-c650-4220-8062-92d586f97a7a", "Relationships": [{"Type": "CHILD", "Ids": ["3237fcb0-e2e8-4fe3-853b-3f4959be82d2", "04c5f51a-ffc2-4eed-babf-5829af8b1918"]}]}, {"BlockType": "LINE", "Confidence": 99.70052337646484, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.1444416344165802, "Height": 0.013450696133077145, "Left": 0.5779888033866882, "Top": 0.1667587012052536}, "Polygon": [{"X": 0.5779889822006226, "Y": 0.1667587012052536}, {"X": 0.722430408000946, "Y": 0.16675981879234314}, {"X": 0.7224301695823669, "Y": 0.18020939826965332}, {"X": 0.5779888033866882, "Y": 0.18020831048488617}]}, "Id": "fbc1bfde-e239-4445-9005-4ad0a10e8ddb", "Relationships": [{"Type": "CHILD", "Ids": ["b205ca08-f5a5-4683-b44e-6ff7a4947a7b", "6db542be-1aa0-42e0-87fc-2c9babdfab91", "7f54bf29-3e2b-4270-ae7a-65e75b8c587e"]}]}, {"BlockType": "LINE", "Confidence": 86.63323211669922, "Text": "X Vari-Wall Isk Tube = spot X X 2sk", "Geometry": {"BoundingBox": {"Width": 0.49358871579170227, "Height": 0.08886811882257462, "Left": 0.02610657922923565, "Top": 0.21025507152080536}, "Polygon": [{"X": 0.026107333600521088, "Y": 0.21025507152080536}, {"X": 0.5196952819824219, "Y": 0.2102586328983307}, {"X": 0.5196940302848816, "Y": 0.29912319779396057}, {"X": 0.02610657922923565, "Y": 0.29912009835243225}]}, "Id": "44f66b7c-439e-4161-b23a-e7d04c84dc54", "Relationships": [{"Type": "CHILD", "Ids": ["c1924261-2d31-4735-8c58-2f580d649b7f", "0f3ae8a3-75c0-48f4-ac2b-b3e275577880", "5be2c35e-98c3-4d9e-a669-ca31ced6bf26", "152c44ca-d4da-4fe1-969a-f4acb68b94ed", "827f797b-8f1d-4b15-ad92-8be7490cab79", "16f3ad63-2836-4afb-b00e-37e8a8ecd5b6", "5b6a0f68-fe88-476d-bcd8-73536cd7c531", "e1621f5e-5aa0-4b01-84fb-815738504706", "c95f99a8-26d1-4d34-8f77-3dacd07080be"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "1315", "Geometry": {"BoundingBox": {"Width": 0.07226567715406418, "Height": 0.026328952983021736, "Left": 0.8599408268928528, "Top": 0.2136409878730774}, "Polygon": [{"X": 0.859941303730011, "Y": 0.2136409878730774}, {"X": 0.9322065114974976, "Y": 0.21364150941371918}, {"X": 0.9322060346603394, "Y": 0.23996993899345398}, {"X": 0.8599408268928528, "Y": 0.23996944725513458}]}, "Id": "e4491dea-80bc-4cd8-9ead-7d8c00fe8816", "Relationships": [{"Type": "CHILD", "Ids": ["d04ece78-8b8f-4953-87cb-16b860700073"]}]}, {"BlockType": "LINE", "Confidence": 99.44276428222656, "Text": "SO-02132290", "Geometry": {"BoundingBox": {"Width": 0.09837828576564789, "Height": 0.012301827780902386, "Left": 0.47299832105636597, "Top": 0.2583687901496887}, "Polygon": [{"X": 0.4729984700679779, "Y": 0.2583687901496887}, {"X": 0.571376621723175, "Y": 0.25836944580078125}, {"X": 0.5713764429092407, "Y": 0.270670622587204}, {"X": 0.47299832105636597, "Y": 0.27066996693611145}]}, "Id": "d62d6d25-1379-4eb1-8e1f-ebd0465f0b83", "Relationships": [{"Type": "CHILD", "Ids": ["0c66e693-e2d5-46ca-8b62-cc3a2f35de67"]}]}, {"BlockType": "LINE", "Confidence": 99.55895233154297, "Text": "2132051", "Geometry": {"BoundingBox": {"Width": 0.14168855547904968, "Height": 0.02986805886030197, "Left": 0.6031256318092346, "Top": 0.24110861122608185}, "Polygon": [{"X": 0.603126049041748, "Y": 0.24110861122608185}, {"X": 0.7448141574859619, "Y": 0.24110957980155945}, {"X": 0.7448136806488037, "Y": 0.2709766626358032}, {"X": 0.6031256318092346, "Y": 0.2709757387638092}]}, "Id": "c045f241-656a-4695-834c-d2c979d1b78b", "Relationships": [{"Type": "CHILD", "Ids": ["cd11c19d-0e51-4797-9188-3c75857fcd8b"]}]}, {"BlockType": "LINE", "Confidence": 99.83079528808594, "Text": "1345", "Geometry": {"BoundingBox": {"Width": 0.07860422134399414, "Height": 0.021719438955187798, "Left": 0.8573949933052063, "Top": 0.2520458698272705}, "Polygon": [{"X": 0.8573954105377197, "Y": 0.2520458698272705}, {"X": 0.9359992146492004, "Y": 0.2520464062690735}, {"X": 0.935998797416687, "Y": 0.27376529574394226}, {"X": 0.8573949933052063, "Y": 0.27376478910446167}]}, "Id": "fac81615-51ce-4bd3-9324-d18bf2af3666", "Relationships": [{"Type": "CHILD", "Ids": ["6e372ab3-b95a-4afa-983c-549ed3d83a7f"]}]}, {"BlockType": "LINE", "Confidence": 70.23541259765625, "Text": "D Z General D Extrusion", "Geometry": {"BoundingBox": {"Width": 0.25575780868530273, "Height": 0.07985156774520874, "Left": 0.00999092124402523, "Top": 0.31092509627342224}, "Polygon": [{"X": 0.009991583414375782, "Y": 0.31092509627342224}, {"X": 0.2657487392425537, "Y": 0.3109266757965088}, {"X": 0.2657478153705597, "Y": 0.390776664018631}, {"X": 0.00999092124402523, "Y": 0.39077529311180115}]}, "Id": "3194c7cc-1199-4cff-9e62-07e372fce292", "Relationships": [{"Type": "CHILD", "Ids": ["16f76b8d-24e6-41e3-a9cf-8d19afc212dc", "a04ac840-719b-45d4-8a84-ece4b7eeb9cb", "a790cd07-89eb-43bd-8b44-c74fc2bd1fa3", "6cfc2170-1e0d-457d-87a7-b582b1c69eb4", "d056235e-9b54-48d4-a5fe-a9e4c1a8ad2d"]}]}, {"BlockType": "LINE", "Confidence": 65.51239776611328, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.04802145063877106, "Height": 0.023934802040457726, "Left": 0.47938624024391174, "Top": 0.3280894458293915}, "Polygon": [{"X": 0.479386568069458, "Y": 0.3280894458293915}, {"X": 0.527407705783844, "Y": 0.32808974385261536}, {"X": 0.5274073481559753, "Y": 0.35202425718307495}, {"X": 0.47938624024391174, "Y": 0.3520239591598511}]}, "Id": "74981f45-f960-4e77-9471-cd5fb26f7978", "Relationships": [{"Type": "CHILD", "Ids": ["55882bbf-e870-4f68-bb83-b3a1a828928c"]}]}, {"BlockType": "LINE", "Confidence": 99.7315444946289, "Text": "1230", "Geometry": {"BoundingBox": {"Width": 0.06769219785928726, "Height": 0.015355191193521023, "Left": 0.889171838760376, "Top": 0.3335933983325958}, "Polygon": [{"X": 0.8891721367835999, "Y": 0.3335933983325958}, {"X": 0.956864058971405, "Y": 0.33359381556510925}, {"X": 0.9568637609481812, "Y": 0.34894859790802}, {"X": 0.889171838760376, "Y": 0.348948210477829}]}, "Id": "8106eb6e-cc43-4fea-9c1b-0c95cc797ea3", "Relationships": [{"Type": "CHILD", "Ids": ["57319aac-4b34-4779-9fd3-7f3549fda3f7"]}]}, {"BlockType": "LINE", "Confidence": 92.08306884765625, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011121721006929874, "Height": 0.010539134033024311, "Left": 0.35945549607276917, "Top": 0.3569931983947754}, "Polygon": [{"X": 0.3594556152820587, "Y": 0.3569931983947754}, {"X": 0.37057721614837646, "Y": 0.35699325799942017}, {"X": 0.3705770969390869, "Y": 0.367532342672348}, {"X": 0.35945549607276917, "Y": 0.36753228306770325}]}, "Id": "7fcc38d5-98cb-4580-b455-a450cf356abf", "Relationships": [{"Type": "CHILD", "Ids": ["a5b3ab3e-a58f-4935-857a-9d431125e62b"]}]}, {"BlockType": "LINE", "Confidence": 98.15868377685547, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.14132477343082428, "Height": 0.013662817887961864, "Left": 0.43122783303260803, "Top": 0.35378268361091614}, "Polygon": [{"X": 0.43122801184654236, "Y": 0.35378268361091614}, {"X": 0.5725526213645935, "Y": 0.3537834882736206}, {"X": 0.5725523829460144, "Y": 0.3674454987049103}, {"X": 0.43122783303260803, "Y": 0.3674447238445282}]}, "Id": "c5c504dd-2a13-46c7-a55f-23297a395337", "Relationships": [{"Type": "CHILD", "Ids": ["abe667e4-0c0a-40e3-b5b4-841bbf5bd0ce", "a9f94c9b-6128-411d-8f8b-ec49fe048279"]}]}, {"BlockType": "LINE", "Confidence": 99.68511199951172, "Text": "1250", "Geometry": {"BoundingBox": {"Width": 0.08497408777475357, "Height": 0.018794093281030655, "Left": 0.8867148160934448, "Top": 0.37212705612182617}, "Polygon": [{"X": 0.8867151737213135, "Y": 0.37212705612182617}, {"X": 0.9716889262199402, "Y": 0.3721275329589844}, {"X": 0.9716885685920715, "Y": 0.3909211754798889}, {"X": 0.8867148160934448, "Y": 0.3909206986427307}]}, "Id": "c7880a32-2256-4bc3-8b34-d5760a7cc9f3", "Relationships": [{"Type": "CHILD", "Ids": ["84534662-a975-463c-bb1e-9dad3c8824b8"]}]}, {"BlockType": "LINE", "Confidence": 77.72590637207031, "Text": "x", "Geometry": {"BoundingBox": {"Width": 0.0653858333826065, "Height": 0.05692465230822563, "Left": 0.01689285784959793, "Top": 0.43178531527519226}, "Polygon": [{"X": 0.016893334686756134, "Y": 0.43178531527519226}, {"X": 0.08227869123220444, "Y": 0.43178561329841614}, {"X": 0.08227816969156265, "Y": 0.488709956407547}, {"X": 0.01689285784959793, "Y": 0.4887096881866455}]}, "Id": "b203dbef-e5f7-4fa0-ba20-0e981ad15ed9", "Relationships": [{"Type": "CHILD", "Ids": ["cc81a248-90c4-4b4d-94e7-bcfac205a438"]}]}, {"BlockType": "LINE", "Confidence": 80.11527252197266, "Text": "I 3sm Bsk = 2.5 2.5spots2sk spots 2sk", "Geometry": {"BoundingBox": {"Width": 0.4390866160392761, "Height": 0.040705375373363495, "Left": 0.101462222635746, "Top": 0.4241240322589874}, "Polygon": [{"X": 0.10146260261535645, "Y": 0.4241240322589874}, {"X": 0.5405488610267639, "Y": 0.42412620782852173}, {"X": 0.5405482649803162, "Y": 0.4648294150829315}, {"X": 0.101462222635746, "Y": 0.46482741832733154}]}, "Id": "5ca3839e-e79e-47e2-899f-9c532e4a8a1c", "Relationships": [{"Type": "CHILD", "Ids": ["471f1de5-eb82-494c-aaf6-bfc4017323b2", "4b428390-bc65-4b13-a97e-dd02c501874a", "0a303c6c-a108-4dea-aa8e-21595d8895d4", "ff904271-a672-4f14-b1a9-fe399a8439fb", "b4997170-b537-4f45-9d71-4e78f6e24838", "07b5f18a-2eb6-49c1-a044-942577b0a7c6", "ae005b3e-e534-4e14-ba16-3df8b8e769fb", "90d9b969-cacf-4115-8b5d-abb4d3d1bf67"]}]}, {"BlockType": "LINE", "Confidence": 68.98178100585938, "Text": "0820", "Geometry": {"BoundingBox": {"Width": 0.09193702042102814, "Height": 0.012241599150002003, "Left": 0.876714289188385, "Top": 0.4467671513557434}, "Polygon": [{"X": 0.8767144680023193, "Y": 0.4467671513557434}, {"X": 0.968651294708252, "Y": 0.44676756858825684}, {"X": 0.9686510562896729, "Y": 0.45900875329971313}, {"X": 0.876714289188385, "Y": 0.4590083062648773}]}, "Id": "1b7c567a-65d2-4aec-a073-554171714288", "Relationships": [{"Type": "CHILD", "Ids": ["6b0d32d3-fe1c-4770-a011-23f84f01c20c"]}]}, {"BlockType": "LINE", "Confidence": 84.69898986816406, "Text": "<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.2258094996213913, "Height": 0.014591367915272713, "Left": 0.10113867372274399, "Top": 0.4643873870372772}, "Polygon": [{"X": 0.10113880783319473, "Y": 0.4643873870372772}, {"X": 0.3269481956958771, "Y": 0.4643884003162384}, {"X": 0.32694801688194275, "Y": 0.4789787530899048}, {"X": 0.10113867372274399, "Y": 0.478977769613266}]}, "Id": "e8c9c6c4-0b91-49e6-880e-13fa260bfacd", "Relationships": [{"Type": "CHILD", "Ids": ["8cd12bd1-1793-4335-8092-aebc0ed102b3", "ad18d20b-c8c2-41ad-83a4-850b1ecf7a72", "ebfa8382-8dcd-497c-8d78-9eb7b67b61b5", "544c0880-1a60-48aa-b563-f14fc7a43395"]}]}, {"BlockType": "LINE", "Confidence": 94.76104736328125, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010984264314174652, "Height": 0.010626890696585178, "Left": 0.3602912425994873, "Top": 0.4665246605873108}, "Polygon": [{"X": 0.36029139161109924, "Y": 0.4665246605873108}, {"X": 0.37127551436424255, "Y": 0.4665246903896332}, {"X": 0.3712753653526306, "Y": 0.4771515429019928}, {"X": 0.3602912425994873, "Y": 0.477151483297348}]}, "Id": "e580a4e8-653a-4594-97e4-b2303f78b89e", "Relationships": [{"Type": "CHILD", "Ids": ["98277778-684a-4c00-aefa-a922799cbe93"]}]}, {"BlockType": "LINE", "Confidence": 95.5909194946289, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01062251441180706, "Height": 0.010421774350106716, "Left": 0.43348512053489685, "Top": 0.4659290313720703}, "Polygon": [{"X": 0.4334852695465088, "Y": 0.4659290313720703}, {"X": 0.44410762190818787, "Y": 0.4659290909767151}, {"X": 0.4441075026988983, "Y": 0.4763508141040802}, {"X": 0.43348512053489685, "Y": 0.4763507544994354}]}, "Id": "cc378b66-c6f1-4a01-aa52-d8def52127db", "Relationships": [{"Type": "CHILD", "Ids": ["4ac0a3a3-4c4d-4000-979f-d6c6ae579e95"]}]}, {"BlockType": "LINE", "Confidence": 99.4614486694336, "Text": "1 BASKET ********** ***********", "Geometry": {"BoundingBox": {"Width": 0.28815534710884094, "Height": 0.01289977878332138, "Left": 0.4762982428073883, "Top": 0.46305611729621887}, "Polygon": [{"X": 0.47629842162132263, "Y": 0.46305611729621887}, {"X": 0.7644535899162292, "Y": 0.46305742859840393}, {"X": 0.7644533514976501, "Y": 0.47595587372779846}, {"X": 0.4762982428073883, "Y": 0.4759546220302582}]}, "Id": "897951f0-27d7-4f73-8831-a2bcdb9743cf", "Relationships": [{"Type": "CHILD", "Ids": ["329ddc86-81f6-417b-a63a-92f622a0ba1d", "9b3719e1-1509-4788-915e-0ae923949c16", "dc463e39-2ad8-4cf5-93cb-e252dfa46aa5", "7550f430-5976-46db-9775-6a8e5874dcdc"]}]}, {"BlockType": "LINE", "Confidence": 99.638671875, "Text": "0840", "Geometry": {"BoundingBox": {"Width": 0.09090698510408401, "Height": 0.016911448910832405, "Left": 0.8822465538978577, "Top": 0.4679184854030609}, "Polygon": [{"X": 0.8822469115257263, "Y": 0.4679184854030609}, {"X": 0.9731535911560059, "Y": 0.46791890263557434}, {"X": 0.9731532335281372, "Y": 0.48482993245124817}, {"X": 0.8822465538978577, "Y": 0.48482954502105713}]}, "Id": "8615c55c-7da0-407b-bb23-1bc51adc9a74", "Relationships": [{"Type": "CHILD", "Ids": ["9fb50418-2a43-4eaf-a3c7-bddb83be580a"]}]}, {"BlockType": "LINE", "Confidence": 69.44584655761719, "Text": "/smSk = / spot", "Geometry": {"BoundingBox": {"Width": 0.26447898149490356, "Height": 0.048035841435194016, "Left": 0.11104823648929596, "Top": 0.5050631761550903}, "Polygon": [{"X": 0.11104869842529297, "Y": 0.5050631761550903}, {"X": 0.37552720308303833, "Y": 0.5050642490386963}, {"X": 0.3755266070365906, "Y": 0.5530990362167358}, {"X": 0.11104823648929596, "Y": 0.5530980825424194}]}, "Id": "ce5233fc-1dbc-4a7f-b74c-b8d0546157ec", "Relationships": [{"Type": "CHILD", "Ids": ["3f03129f-c23b-4aa0-a363-f6a24ecf45d4", "aa62f134-7b1d-4c0f-91d9-fb58f9d3ca9f", "9b8dae56-a139-471a-b9f2-b7077ad1ebce", "6ba21742-e045-4770-bf44-4aa87133d016"]}]}, {"BlockType": "LINE", "Confidence": 40.71249008178711, "Text": "&", "Geometry": {"BoundingBox": {"Width": 0.026127200573682785, "Height": 0.032245784997940063, "Left": 0.46722880005836487, "Top": 0.5080786943435669}, "Polygon": [{"X": 0.4672292470932007, "Y": 0.5080786943435669}, {"X": 0.49335598945617676, "Y": 0.5080787539482117}, {"X": 0.49335554242134094, "Y": 0.5403244495391846}, {"X": 0.46722880005836487, "Y": 0.540324330329895}]}, "Id": "e05ce3f5-4fb1-4070-94b2-c088dc7f1401", "Relationships": [{"Type": "CHILD", "Ids": ["bc8f4099-335a-4419-9f80-c70291eb1b07"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "1050", "Geometry": {"BoundingBox": {"Width": 0.06188691779971123, "Height": 0.0175942275673151, "Left": 0.8748548626899719, "Top": 0.5267840623855591}, "Polygon": [{"X": 0.8748551607131958, "Y": 0.5267840623855591}, {"X": 0.9367417693138123, "Y": 0.5267843008041382}, {"X": 0.9367414116859436, "Y": 0.5443782806396484}, {"X": 0.8748548626899719, "Y": 0.5443780422210693}]}, "Id": "446534bb-76bb-4c35-9d7e-cd8c248087f2", "Relationships": [{"Type": "CHILD", "Ids": ["2ffb6d64-5c8e-4f96-b421-0051927caf47"]}]}, {"BlockType": "LINE", "Confidence": 22.70667266845703, "Text": "D", "Geometry": {"BoundingBox": {"Width": 0.0516221709549427, "Height": 0.07295437902212143, "Left": 0.00040261223330162466, "Top": 0.5279433727264404}, "Polygon": [{"X": 0.00040320883272215724, "Y": 0.5279433727264404}, {"X": 0.05202478542923927, "Y": 0.5279435515403748}, {"X": 0.05202414095401764, "Y": 0.6008977293968201}, {"X": 0.00040261223330162466, "Y": 0.6008976101875305}]}, "Id": "f6028319-f4f3-452a-897e-5e7e67eddfa7", "Relationships": [{"Type": "CHILD", "Ids": ["33cc5e2c-85bb-4e33-9165-898606cbe1d0"]}]}, {"BlockType": "LINE", "Confidence": 87.47508239746094, "Text": "ELyRia", "Geometry": {"BoundingBox": {"Width": 0.15374040603637695, "Height": 0.03579412028193474, "Left": 0.08873278647661209, "Top": 0.5466986298561096}, "Polygon": [{"X": 0.08873311430215836, "Y": 0.5466986298561096}, {"X": 0.24247319996356964, "Y": 0.5466992259025574}, {"X": 0.2424727976322174, "Y": 0.5824927687644958}, {"X": 0.08873278647661209, "Y": 0.5824922323226929}]}, "Id": "8aaf7e9a-1a1e-4878-87a0-85e4837ddcc6", "Relationships": [{"Type": "CHILD", "Ids": ["87a85934-c781-4a75-a8b8-6fdd59b83753"]}]}, {"BlockType": "LINE", "Confidence": 82.94464111328125, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.024065649136900902, "Height": 0.025540361180901527, "Left": 0.366391122341156, "Top": 0.5430426597595215}, "Polygon": [{"X": 0.3663914203643799, "Y": 0.5430426597595215}, {"X": 0.39045676589012146, "Y": 0.543042778968811}, {"X": 0.3904564380645752, "Y": 0.5685830116271973}, {"X": 0.366391122341156, "Y": 0.5685829520225525}]}, "Id": "654bb838-bc36-4578-a260-6bece9fdfc85", "Relationships": [{"Type": "CHILD", "Ids": ["e612b2e2-58fd-4102-bd25-54574ed23869"]}]}, {"BlockType": "LINE", "Confidence": 74.30424499511719, "Text": "Pickup", "Geometry": {"BoundingBox": {"Width": 0.1276935189962387, "Height": 0.028271706774830818, "Left": 0.4700011909008026, "Top": 0.5402073264122009}, "Polygon": [{"X": 0.47000157833099365, "Y": 0.5402073264122009}, {"X": 0.5976946949958801, "Y": 0.5402078032493591}, {"X": 0.5976942777633667, "Y": 0.568479061126709}, {"X": 0.4700011909008026, "Y": 0.5684785842895508}]}, "Id": "f32ddaa3-6292-42c4-b832-a89f9a7978d7", "Relationships": [{"Type": "CHILD", "Ids": ["c8588a14-69eb-4cc0-a8a7-ce0e3215a6a4"]}]}, {"BlockType": "LINE", "Confidence": 99.45252227783203, "Text": "1055", "Geometry": {"BoundingBox": {"Width": 0.07308492064476013, "Height": 0.019535735249519348, "Left": 0.8638453483581543, "Top": 0.5707002282142639}, "Polygon": [{"X": 0.863845705986023, "Y": 0.5707002282142639}, {"X": 0.936930239200592, "Y": 0.570700466632843}, {"X": 0.9369298815727234, "Y": 0.5902359485626221}, {"X": 0.8638453483581543, "Y": 0.590235710144043}]}, "Id": "8cf07c18-c906-428f-b22d-bb0196e28364", "Relationships": [{"Type": "CHILD", "Ids": ["a49275af-3798-4ed6-aea4-7c57263acce5"]}]}, {"BlockType": "LINE", "Confidence": 91.4805908203125, "Text": "(EMC)", "Geometry": {"BoundingBox": {"Width": 0.13689275085926056, "Height": 0.041787926107645035, "Left": 0.05756580829620361, "Top": 0.5943755507469177}, "Polygon": [{"X": 0.05756617709994316, "Y": 0.5943755507469177}, {"X": 0.19445855915546417, "Y": 0.5943759679794312}, {"X": 0.19445812702178955, "Y": 0.6361634731292725}, {"X": 0.05756580829620361, "Y": 0.6361631155014038}]}, "Id": "51cc128f-a837-424a-b9e4-524b674635a3", "Relationships": [{"Type": "CHILD", "Ids": ["beda33ac-117c-49a2-9466-1a6ccf6d1fcf"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "YOUNGSTOWN", "Geometry": {"BoundingBox": {"Width": 0.14146214723587036, "Height": 0.016021793708205223, "Left": 0.44487854838371277, "Top": 0.9240139126777649}, "Polygon": [{"X": 0.4448787569999695, "Y": 0.9240139722824097}, {"X": 0.5863407254219055, "Y": 0.9240139126777649}, {"X": 0.5863404273986816, "Y": 0.9400356411933899}, {"X": 0.44487854838371277, "Y": 0.9400357007980347}]}, "Id": "0a680cd3-fafa-4c8d-991b-192f6af5621e", "Relationships": [{"Type": "CHILD", "Ids": ["c41dd060-b69e-4cb6-971c-5c0b47e17392"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055434323847293854, "Height": 0.01348981261253357, "Left": 0.2523845136165619, "Top": 0.007132180966436863}, "Polygon": [{"X": 0.25238466262817383, "Y": 0.007132180966436863}, {"X": 0.30781885981559753, "Y": 0.007132699713110924}, {"X": 0.3078186810016632, "Y": 0.020621994510293007}, {"X": 0.2523845136165619, "Y": 0.02062148228287697}]}, "Id": "a87cca09-a28e-4024-b95e-5ce2db717f97"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "13-Jun", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057647403329610825, "Height": 0.013493373990058899, "Left": 0.3305290937423706, "Top": 0.007995342835783958}, "Polygon": [{"X": 0.33052924275398254, "Y": 0.007995342835783958}, {"X": 0.38817650079727173, "Y": 0.007995881140232086}, {"X": 0.3881763219833374, "Y": 0.021488716825842857}, {"X": 0.3305290937423706, "Y": 0.021488185971975327}]}, "Id": "a7bdcb01-fc9e-4a10-a5ab-c39cf98a0236"}, {"BlockType": "WORD", "Confidence": 99.384765625, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04826163873076439, "Height": 0.013118927367031574, "Left": 0.23563450574874878, "Top": 0.043498579412698746}, "Polygon": [{"X": 0.23563463985919952, "Y": 0.043498579412698746}, {"X": 0.28389614820480347, "Y": 0.043499015271663666}, {"X": 0.28389599919319153, "Y": 0.056617505848407745}, {"X": 0.23563450574874878, "Y": 0.05661708116531372}]}, "Id": "45f4eb74-6d05-4061-a910-5592df0be600"}, {"BlockType": "WORD", "Confidence": 98.98358154296875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021007467061281204, "Height": 0.013013102114200592, "Left": 0.2865600883960724, "Top": 0.04364752769470215}, "Polygon": [{"X": 0.2865602374076843, "Y": 0.04364752769470215}, {"X": 0.3075675368309021, "Y": 0.04364771395921707}, {"X": 0.30756738781929016, "Y": 0.05666062980890274}, {"X": 0.2865600883960724, "Y": 0.05666044354438782}]}, "Id": "c7c66a0b-7a81-4e82-8956-5d95d12c2aec"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "6063/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11517588049173355, "Height": 0.015181449241936207, "Left": 0.32927820086479187, "Top": 0.04354594647884369}, "Polygon": [{"X": 0.3292783796787262, "Y": 0.04354594647884369}, {"X": 0.4444540739059448, "Y": 0.04354697838425636}, {"X": 0.4444538652896881, "Y": 0.05872739478945732}, {"X": 0.32927820086479187, "Y": 0.05872638151049614}]}, "Id": "4d0f9584-27ca-416a-8291-d7bc3e48a04f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04795743152499199, "Height": 0.012920652516186237, "Left": 0.47414782643318176, "Top": 0.06137381121516228}, "Polygon": [{"X": 0.4741480052471161, "Y": 0.06137381121516228}, {"X": 0.5221052765846252, "Y": 0.0613742358982563}, {"X": 0.5221050977706909, "Y": 0.07429446280002594}, {"X": 0.47414782643318176, "Y": 0.07429405301809311}]}, "Id": "7743f73d-2491-4719-80c9-16f49f18ee9e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05596064403653145, "Height": 0.01615837961435318, "Left": 0.5262998342514038, "Top": 0.06119885295629501}, "Polygon": [{"X": 0.5263000726699829, "Y": 0.06119885295629501}, {"X": 0.5822604894638062, "Y": 0.06119934469461441}, {"X": 0.582260251045227, "Y": 0.0773572325706482}, {"X": 0.5262998342514038, "Y": 0.0773567482829094}]}, "Id": "7389d537-9944-444c-933f-f04a6bfaa1d6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04780168458819389, "Height": 0.01276309508830309, "Left": 0.5875736474990845, "Top": 0.06167801469564438}, "Polygon": [{"X": 0.5875738263130188, "Y": 0.06167801469564438}, {"X": 0.6353753209114075, "Y": 0.061678435653448105}, {"X": 0.6353751420974731, "Y": 0.07444111257791519}, {"X": 0.5875736474990845, "Y": 0.07444069534540176}]}, "Id": "0224b984-4698-4d4a-9f79-5da6124c3432"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "5:30", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09460558742284775, "Height": 0.020666582509875298, "Left": 0.6676312685012817, "Top": 0.05488969758152962}, "Polygon": [{"X": 0.6676316261291504, "Y": 0.05488969758152962}, {"X": 0.7622368931770325, "Y": 0.05489053204655647}, {"X": 0.7622365355491638, "Y": 0.07555627822875977}, {"X": 0.6676312685012817, "Y": 0.0755554586648941}]}, "Id": "c7fa0973-6d3c-49cd-9393-fcb305d2ba6e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07579152286052704, "Height": 0.012899333611130714, "Left": 0.2256195843219757, "Top": 0.07973604649305344}, "Polygon": [{"X": 0.22561973333358765, "Y": 0.07973604649305344}, {"X": 0.30141112208366394, "Y": 0.07973669469356537}, {"X": 0.301410973072052, "Y": 0.092635378241539}, {"X": 0.2256195843219757, "Y": 0.09263473749160767}]}, "Id": "07835b16-cf29-492d-97f0-19eae362995c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06036031246185303, "Height": 0.01278630644083023, "Left": 0.32956621050834656, "Top": 0.07953134924173355}, "Polygon": [{"X": 0.3295663893222809, "Y": 0.07953134924173355}, {"X": 0.3899265229701996, "Y": 0.07953187078237534}, {"X": 0.38992637395858765, "Y": 0.09231765568256378}, {"X": 0.32956621050834656, "Y": 0.09231714904308319}]}, "Id": "eb81d93d-294f-412b-9962-13b01a3f3798"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048024967312812805, "Height": 0.013050032779574394, "Left": 0.4746589958667755, "Top": 0.09708280861377716}, "Polygon": [{"X": 0.47465917468070984, "Y": 0.09708280861377716}, {"X": 0.5226839780807495, "Y": 0.0970832109451294}, {"X": 0.5226837992668152, "Y": 0.1101328432559967}, {"X": 0.4746589958667755, "Y": 0.11013244092464447}]}, "Id": "dd04eaf2-ea99-4da8-b28e-12d1ee066c7a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09399998188018799, "Height": 0.015729224309325218, "Left": 0.5266625881195068, "Top": 0.09732963889837265}, "Polygon": [{"X": 0.5266628265380859, "Y": 0.09732963889837265}, {"X": 0.6206625699996948, "Y": 0.09733042865991592}, {"X": 0.6206623315811157, "Y": 0.11305886507034302}, {"X": 0.5266625881195068, "Y": 0.11305809020996094}]}, "Id": "c4248374-a162-4f7c-ab3d-07165e059e93"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "5:30", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08919630944728851, "Height": 0.01949622668325901, "Left": 0.6694497466087341, "Top": 0.09066339582204819}, "Polygon": [{"X": 0.669450044631958, "Y": 0.09066339582204819}, {"X": 0.7586460709571838, "Y": 0.09066415578126907}, {"X": 0.7586457133293152, "Y": 0.11015962064266205}, {"X": 0.6694497466087341, "Y": 0.11015888303518295}]}, "Id": "2dcabc47-2a78-4634-b07c-4fe14c5d809d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11146997660398483, "Height": 0.013440879993140697, "Left": 0.13355939090251923, "Top": 0.11559045314788818}, "Polygon": [{"X": 0.13355952501296997, "Y": 0.11559045314788818}, {"X": 0.24502936005592346, "Y": 0.1155913695693016}, {"X": 0.24502921104431152, "Y": 0.12903133034706116}, {"X": 0.13355939090251923, "Y": 0.12903043627738953}]}, "Id": "7e9a17b8-780f-479f-a7a9-98ca9c0b6a2d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05049433931708336, "Height": 0.012808509171009064, "Left": 0.25043728947639465, "Top": 0.11588837951421738}, "Polygon": [{"X": 0.2504374086856842, "Y": 0.11588837951421738}, {"X": 0.3009316325187683, "Y": 0.1158887967467308}, {"X": 0.30093148350715637, "Y": 0.12869688868522644}, {"X": 0.25043728947639465, "Y": 0.1286964863538742}]}, "Id": "5b3bcabd-0cd1-481f-8a80-810c0630a10b"}, {"BlockType": "WORD", "Confidence": 99.296875, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03793775290250778, "Height": 0.01334445085376501, "Left": 0.3289470672607422, "Top": 0.1153680682182312}, "Polygon": [{"X": 0.3289472162723541, "Y": 0.1153680682182312}, {"X": 0.36688482761383057, "Y": 0.11536838114261627}, {"X": 0.36688464879989624, "Y": 0.1287125200033188}, {"X": 0.3289470672607422, "Y": 0.1287122219800949}]}, "Id": "b6b0e104-67c0-46ed-9a26-e3d66654f616"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04843425378203392, "Height": 0.011299011297523975, "Left": 0.33933189511299133, "Top": 0.14802676439285278}, "Polygon": [{"X": 0.33933204412460327, "Y": 0.14802676439285278}, {"X": 0.38776615262031555, "Y": 0.14802713692188263}, {"X": 0.3877660036087036, "Y": 0.1593257635831833}, {"X": 0.33933189511299133, "Y": 0.15932539105415344}]}, "Id": "dbaa1ca0-b182-4e6b-9061-41e61780500d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04562446102499962, "Height": 0.01222815178334713, "Left": 0.8364191651344299, "Top": 0.15956756472587585}, "Polygon": [{"X": 0.836419403553009, "Y": 0.15956756472587585}, {"X": 0.8820436596870422, "Y": 0.1595679223537445}, {"X": 0.8820434212684631, "Y": 0.17179572582244873}, {"X": 0.8364191651344299, "Y": 0.17179536819458008}]}, "Id": "e348ae11-1d90-434a-bab8-c3579f82d814"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04639126732945442, "Height": 0.011998143047094345, "Left": 0.8850873112678528, "Top": 0.15983861684799194}, "Polygon": [{"X": 0.8850875496864319, "Y": 0.15983861684799194}, {"X": 0.9314786195755005, "Y": 0.1598389744758606}, {"X": 0.9314783811569214, "Y": 0.1718367636203766}, {"X": 0.8850873112678528, "Y": 0.17183640599250793}]}, "Id": "d767e75c-7797-47da-9524-4653095a5248"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10112185776233673, "Height": 0.013659371994435787, "Left": 0.16423135995864868, "Top": 0.1680711805820465}, "Polygon": [{"X": 0.16423150897026062, "Y": 0.1680711805820465}, {"X": 0.2653532326221466, "Y": 0.1680719554424286}, {"X": 0.2653530538082123, "Y": 0.18173055350780487}, {"X": 0.16423135995864868, "Y": 0.18172979354858398}]}, "Id": "9f7064a5-b575-4ed1-a51f-6c7003f29da7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02478152886033058, "Height": 0.013081332668662071, "Left": 0.35173434019088745, "Top": 0.1666499227285385}, "Polygon": [{"X": 0.3517344892024994, "Y": 0.1666499227285385}, {"X": 0.37651586532592773, "Y": 0.16665011644363403}, {"X": 0.3765156865119934, "Y": 0.17973124980926514}, {"X": 0.35173434019088745, "Y": 0.1797310709953308}]}, "Id": "3237fcb0-e2e8-4fe3-853b-3f4959be82d2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034740548580884933, "Height": 0.01299379300326109, "Left": 0.4186022877693176, "Top": 0.16650883853435516}, "Polygon": [{"X": 0.41860243678092957, "Y": 0.16650883853435516}, {"X": 0.45334282517433167, "Y": 0.16650910675525665}, {"X": 0.45334264636039734, "Y": 0.17950262129306793}, {"X": 0.4186022877693176, "Y": 0.17950236797332764}]}, "Id": "04c5f51a-ffc2-4eed-babf-5829af8b1918"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05672604218125343, "Height": 0.013331301510334015, "Left": 0.5779888033866882, "Top": 0.16687743365764618}, "Polygon": [{"X": 0.5779889822006226, "Y": 0.16687743365764618}, {"X": 0.6347148418426514, "Y": 0.166877880692482}, {"X": 0.6347146034240723, "Y": 0.1802087426185608}, {"X": 0.5779888033866882, "Y": 0.18020831048488617}]}, "Id": "b205ca08-f5a5-4683-b44e-6ff7a4947a7b"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06441924721002579, "Height": 0.013074885122478008, "Left": 0.6397500038146973, "Top": 0.16692538559436798}, "Polygon": [{"X": 0.6397502422332764, "Y": 0.16692538559436798}, {"X": 0.7041692733764648, "Y": 0.16692587733268738}, {"X": 0.7041690349578857, "Y": 0.18000027537345886}, {"X": 0.6397500038146973, "Y": 0.17999978363513947}]}, "Id": "6db542be-1aa0-42e0-87fc-2c9babdfab91"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012328180484473705, "Height": 0.012309787794947624, "Left": 0.7101022601127625, "Top": 0.16675971448421478}, "Polygon": [{"X": 0.7101024389266968, "Y": 0.16675971448421478}, {"X": 0.722430408000946, "Y": 0.16675981879234314}, {"X": 0.7224302291870117, "Y": 0.17906950414180756}, {"X": 0.7101022601127625, "Y": 0.1790694147348404}]}, "Id": "7f54bf29-3e2b-4270-ae7a-65e75b8c587e"}, {"BlockType": "WORD", "Confidence": 66.5794448852539, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08011402189731598, "Height": 0.08886552602052689, "Left": 0.02610657922923565, "Top": 0.21025507152080536}, "Polygon": [{"X": 0.026107333600521088, "Y": 0.21025507152080536}, {"X": 0.10622060298919678, "Y": 0.21025565266609192}, {"X": 0.10621976107358932, "Y": 0.29912060499191284}, {"X": 0.02610657922923565, "Y": 0.29912009835243225}]}, "Id": "c1924261-2d31-4735-8c58-2f580d649b7f"}, {"BlockType": "WORD", "Confidence": 99.4092788696289, "Text": "Vari-Wall", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0802953913807869, "Height": 0.013490481302142143, "Left": 0.10301521420478821, "Top": 0.2577275037765503}, "Polygon": [{"X": 0.10301534086465836, "Y": 0.2577275037765503}, {"X": 0.1833106130361557, "Y": 0.25772804021835327}, {"X": 0.18331046402454376, "Y": 0.2712180018424988}, {"X": 0.10301521420478821, "Y": 0.2712174654006958}]}, "Id": "0f3ae8a3-75c0-48f4-ac2b-b3e275577880"}, {"BlockType": "WORD", "Confidence": 41.505699157714844, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07823441177606583, "Height": 0.043384745717048645, "Left": 0.15284016728401184, "Top": 0.2180786430835724}, "Polygon": [{"X": 0.15284059941768646, "Y": 0.2180786430835724}, {"X": 0.23107458651065826, "Y": 0.21807919442653656}, {"X": 0.23107410967350006, "Y": 0.26146337389945984}, {"X": 0.15284016728401184, "Y": 0.26146286725997925}]}, "Id": "5be2c35e-98c3-4d9e-a669-ca31ced6bf26"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04543992504477501, "Height": 0.013346920721232891, "Left": 0.18918374180793762, "Top": 0.25762614607810974}, "Polygon": [{"X": 0.18918389081954956, "Y": 0.25762614607810974}, {"X": 0.23462367057800293, "Y": 0.2576264441013336}, {"X": 0.234623521566391, "Y": 0.2709730565547943}, {"X": 0.18918374180793762, "Y": 0.27097275853157043}]}, "Id": "152c44ca-d4da-4fe1-969a-f4acb68b94ed"}, {"BlockType": "WORD", "Confidence": 96.86942291259766, "Text": "=", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.013934305869042873, "Height": 0.010692929849028587, "Left": 0.2517198920249939, "Top": 0.23264968395233154}, "Polygon": [{"X": 0.25172001123428345, "Y": 0.23264968395233154}, {"X": 0.26565420627593994, "Y": 0.2326497733592987}, {"X": 0.2656540870666504, "Y": 0.24334260821342468}, {"X": 0.2517198920249939, "Y": 0.24334251880645752}]}, "Id": "827f797b-8f1d-4b15-ad92-8be7490cab79"}, {"BlockType": "WORD", "Confidence": 99.23767852783203, "Text": "spot", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09673143178224564, "Height": 0.03974096477031708, "Left": 0.2947133779525757, "Top": 0.22376862168312073}, "Polygon": [{"X": 0.2947138249874115, "Y": 0.22376862168312073}, {"X": 0.3914448022842407, "Y": 0.22376930713653564}, {"X": 0.39144429564476013, "Y": 0.263509601354599}, {"X": 0.2947133779525757, "Y": 0.26350894570350647}]}, "Id": "16f3ad63-2836-4afb-b00e-37e8a8ecd5b6"}, {"BlockType": "WORD", "Confidence": 97.22676086425781, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01095448900014162, "Height": 0.010222552344202995, "Left": 0.35880810022354126, "Top": 0.259928822517395}, "Polygon": [{"X": 0.3588082492351532, "Y": 0.259928822517395}, {"X": 0.3697625994682312, "Y": 0.2599288821220398}, {"X": 0.36976248025894165, "Y": 0.27015137672424316}, {"X": 0.35880810022354126, "Y": 0.270151287317276}]}, "Id": "5b6a0f68-fe88-476d-bcd8-73536cd7c531"}, {"BlockType": "WORD", "Confidence": 91.93099975585938, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010966421104967594, "Height": 0.010303117334842682, "Left": 0.43121662735939026, "Top": 0.2604021430015564}, "Polygon": [{"X": 0.4312167763710022, "Y": 0.2604021430015564}, {"X": 0.4421830475330353, "Y": 0.26040220260620117}, {"X": 0.4421829283237457, "Y": 0.2707052528858185}, {"X": 0.43121662735939026, "Y": 0.2707051932811737}]}, "Id": "e1621f5e-5aa0-4b01-84fb-815738504706"}, {"BlockType": "WORD", "Confidence": 87.04719543457031, "Text": "2sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06455573439598083, "Height": 0.024761298671364784, "Left": 0.45513930916786194, "Top": 0.2276562601327896}, "Polygon": [{"X": 0.4551396667957306, "Y": 0.2276562601327896}, {"X": 0.5196950435638428, "Y": 0.22765672206878662}, {"X": 0.5196946859359741, "Y": 0.25241756439208984}, {"X": 0.45513930916786194, "Y": 0.25241711735725403}]}, "Id": "c95f99a8-26d1-4d34-8f77-3dacd07080be"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "1315", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07226567715406418, "Height": 0.026328952983021736, "Left": 0.8599408268928528, "Top": 0.2136409878730774}, "Polygon": [{"X": 0.859941303730011, "Y": 0.2136409878730774}, {"X": 0.9322065114974976, "Y": 0.21364150941371918}, {"X": 0.9322060346603394, "Y": 0.23996993899345398}, {"X": 0.8599408268928528, "Y": 0.23996944725513458}]}, "Id": "d04ece78-8b8f-4953-87cb-16b860700073"}, {"BlockType": "WORD", "Confidence": 99.44276428222656, "Text": "SO-02132290", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09837828576564789, "Height": 0.012301827780902386, "Left": 0.47299832105636597, "Top": 0.2583687901496887}, "Polygon": [{"X": 0.4729984700679779, "Y": 0.2583687901496887}, {"X": 0.571376621723175, "Y": 0.25836944580078125}, {"X": 0.5713764429092407, "Y": 0.270670622587204}, {"X": 0.47299832105636597, "Y": 0.27066996693611145}]}, "Id": "0c66e693-e2d5-46ca-8b62-cc3a2f35de67"}, {"BlockType": "WORD", "Confidence": 99.55895233154297, "Text": "2132051", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14168855547904968, "Height": 0.02986805886030197, "Left": 0.6031256318092346, "Top": 0.24110861122608185}, "Polygon": [{"X": 0.603126049041748, "Y": 0.24110861122608185}, {"X": 0.7448141574859619, "Y": 0.24110957980155945}, {"X": 0.7448136806488037, "Y": 0.2709766626358032}, {"X": 0.6031256318092346, "Y": 0.2709757387638092}]}, "Id": "cd11c19d-0e51-4797-9188-3c75857fcd8b"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "1345", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07860422134399414, "Height": 0.021719438955187798, "Left": 0.8573949933052063, "Top": 0.2520458698272705}, "Polygon": [{"X": 0.8573954105377197, "Y": 0.2520458698272705}, {"X": 0.9359992146492004, "Y": 0.2520464062690735}, {"X": 0.935998797416687, "Y": 0.27376529574394226}, {"X": 0.8573949933052063, "Y": 0.27376478910446167}]}, "Id": "6e372ab3-b95a-4afa-983c-549ed3d83a7f"}, {"BlockType": "WORD", "Confidence": 24.561742782592773, "Text": "D", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08508028835058212, "Height": 0.06342592090368271, "Left": 0.00999092124402523, "Top": 0.32734984159469604}, "Polygon": [{"X": 0.009991447441279888, "Y": 0.32734984159469604}, {"X": 0.0950712114572525, "Y": 0.32735034823417664}, {"X": 0.09507061541080475, "Y": 0.39077576994895935}, {"X": 0.00999092124402523, "Y": 0.39077529311180115}]}, "Id": "16f76b8d-24e6-41e3-a9cf-8d19afc212dc"}, {"BlockType": "WORD", "Confidence": 68.76913452148438, "Text": "Z", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.02463720366358757, "Height": 0.01779576949775219, "Left": 0.04026656970381737, "Top": 0.3517394959926605}, "Polygon": [{"X": 0.040266722440719604, "Y": 0.3517394959926605}, {"X": 0.06490377336740494, "Y": 0.35173964500427246}, {"X": 0.0649036169052124, "Y": 0.36953526735305786}, {"X": 0.04026656970381737, "Y": 0.3695351481437683}]}, "Id": "a04ac840-719b-45d4-8a84-ece4b7eeb9cb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "General", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06873119622468948, "Height": 0.013597416691482067, "Left": 0.10226013511419296, "Top": 0.35502126812934875}, "Polygon": [{"X": 0.10226026922464371, "Y": 0.35502126812934875}, {"X": 0.17099133133888245, "Y": 0.3550216555595398}, {"X": 0.1709911972284317, "Y": 0.3686186671257019}, {"X": 0.10226013511419296, "Y": 0.36861830949783325}]}, "Id": "a790cd07-89eb-43bd-8b44-c74fc2bd1fa3"}, {"BlockType": "WORD", "Confidence": 57.91454315185547, "Text": "D", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12941309809684753, "Height": 0.0479271337389946, "Left": 0.13633562624454498, "Top": 0.3109258711338043}, "Polygon": [{"X": 0.13633610308170319, "Y": 0.3109258711338043}, {"X": 0.2657487392425537, "Y": 0.3109266757965088}, {"X": 0.26574820280075073, "Y": 0.3588530123233795}, {"X": 0.13633562624454498, "Y": 0.3588522672653198}]}, "Id": "6cfc2170-1e0d-457d-87a7-b582b1c69eb4"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Extrusion", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08127226680517197, "Height": 0.013430791907012463, "Left": 0.1786278486251831, "Top": 0.3548397421836853}, "Polygon": [{"X": 0.17862798273563385, "Y": 0.3548397421836853}, {"X": 0.2599000930786133, "Y": 0.3548402190208435}, {"X": 0.25989994406700134, "Y": 0.36827054619789124}, {"X": 0.1786278486251831, "Y": 0.3682700991630554}]}, "Id": "d056235e-9b54-48d4-a5fe-a9e4c1a8ad2d"}, {"BlockType": "WORD", "Confidence": 65.51239776611328, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04802145063877106, "Height": 0.023934802040457726, "Left": 0.47938624024391174, "Top": 0.3280894458293915}, "Polygon": [{"X": 0.479386568069458, "Y": 0.3280894458293915}, {"X": 0.527407705783844, "Y": 0.32808974385261536}, {"X": 0.5274073481559753, "Y": 0.35202425718307495}, {"X": 0.47938624024391174, "Y": 0.3520239591598511}]}, "Id": "55882bbf-e870-4f68-bb83-b3a1a828928c"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "1230", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06769219785928726, "Height": 0.015355191193521023, "Left": 0.889171838760376, "Top": 0.3335933983325958}, "Polygon": [{"X": 0.8891721367835999, "Y": 0.3335933983325958}, {"X": 0.956864058971405, "Y": 0.33359381556510925}, {"X": 0.9568637609481812, "Y": 0.34894859790802}, {"X": 0.889171838760376, "Y": 0.348948210477829}]}, "Id": "57319aac-4b34-4779-9fd3-7f3549fda3f7"}, {"BlockType": "WORD", "Confidence": 92.08306884765625, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011121721006929874, "Height": 0.010539134033024311, "Left": 0.35945549607276917, "Top": 0.3569931983947754}, "Polygon": [{"X": 0.3594556152820587, "Y": 0.3569931983947754}, {"X": 0.37057721614837646, "Y": 0.35699325799942017}, {"X": 0.3705770969390869, "Y": 0.367532342672348}, {"X": 0.35945549607276917, "Y": 0.36753228306770325}]}, "Id": "a5b3ab3e-a58f-4935-857a-9d431125e62b"}, {"BlockType": "WORD", "Confidence": 97.08446502685547, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013322092592716217, "Height": 0.013662107288837433, "Left": 0.43122783303260803, "Top": 0.35378268361091614}, "Polygon": [{"X": 0.43122801184654236, "Y": 0.35378268361091614}, {"X": 0.44454991817474365, "Y": 0.3537827432155609}, {"X": 0.4445497393608093, "Y": 0.367444783449173}, {"X": 0.43122783303260803, "Y": 0.3674447238445282}]}, "Id": "abe667e4-0c0a-40e3-b5b4-841bbf5bd0ce"}, {"BlockType": "WORD", "Confidence": 99.23290252685547, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09834727644920349, "Height": 0.011890161782503128, "Left": 0.4742053151130676, "Top": 0.3553765118122101}, "Polygon": [{"X": 0.47420549392700195, "Y": 0.3553765118122101}, {"X": 0.5725525617599487, "Y": 0.35537707805633545}, {"X": 0.5725523829460144, "Y": 0.3672666847705841}, {"X": 0.4742053151130676, "Y": 0.36726614832878113}]}, "Id": "a9f94c9b-6128-411d-8f8b-ec49fe048279"}, {"BlockType": "WORD", "Confidence": 99.68511199951172, "Text": "1250", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08497408777475357, "Height": 0.018794093281030655, "Left": 0.8867148160934448, "Top": 0.37212705612182617}, "Polygon": [{"X": 0.8867151737213135, "Y": 0.37212705612182617}, {"X": 0.9716889262199402, "Y": 0.3721275329589844}, {"X": 0.9716885685920715, "Y": 0.3909211754798889}, {"X": 0.8867148160934448, "Y": 0.3909206986427307}]}, "Id": "84534662-a975-463c-bb1e-9dad3c8824b8"}, {"BlockType": "WORD", "Confidence": 77.72590637207031, "Text": "x", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0653858333826065, "Height": 0.05692465230822563, "Left": 0.01689285784959793, "Top": 0.43178531527519226}, "Polygon": [{"X": 0.016893334686756134, "Y": 0.43178531527519226}, {"X": 0.08227869123220444, "Y": 0.43178561329841614}, {"X": 0.08227816969156265, "Y": 0.488709956407547}, {"X": 0.01689285784959793, "Y": 0.4887096881866455}]}, "Id": "cc81a248-90c4-4b4d-94e7-bcfac205a438"}, {"BlockType": "WORD", "Confidence": 45.81632614135742, "Text": "I", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.005816271994262934, "Height": 0.03582003340125084, "Left": 0.101462222635746, "Top": 0.4290074110031128}, "Polygon": [{"X": 0.10146255791187286, "Y": 0.4290074110031128}, {"X": 0.1072784960269928, "Y": 0.4290074408054352}, {"X": 0.10727815330028534, "Y": 0.46482744812965393}, {"X": 0.101462222635746, "Y": 0.46482741832733154}]}, "Id": "471f1de5-eb82-494c-aaf6-bfc4017323b2"}, {"BlockType": "WORD", "Confidence": 87.75859069824219, "Text": "3sm", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06896643340587616, "Height": 0.030243363231420517, "Left": 0.16409848630428314, "Top": 0.4335740804672241}, "Polygon": [{"X": 0.1640987992286682, "Y": 0.4335740804672241}, {"X": 0.2330649197101593, "Y": 0.4335744082927704}, {"X": 0.23306459188461304, "Y": 0.46381741762161255}, {"X": 0.16409848630428314, "Y": 0.46381711959838867}]}, "Id": "4b428390-bc65-4b13-a97e-dd02c501874a"}, {"BlockType": "WORD", "Confidence": 96.86304473876953, "Text": "Bsk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06995970010757446, "Height": 0.03278429061174393, "Left": 0.23783186078071594, "Top": 0.42895933985710144}, "Polygon": [{"X": 0.2378322184085846, "Y": 0.42895933985710144}, {"X": 0.3077915608882904, "Y": 0.4289596676826477}, {"X": 0.30779117345809937, "Y": 0.46174362301826477}, {"X": 0.23783186078071594, "Y": 0.4617432951927185}]}, "Id": "0a303c6c-a108-4dea-aa8e-21595d8895d4"}, {"BlockType": "WORD", "Confidence": 90.89763641357422, "Text": "=", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.01688465103507042, "Height": 0.01110528502613306, "Left": 0.3119468688964844, "Top": 0.44104504585266113}, "Polygon": [{"X": 0.3119470179080963, "Y": 0.44104504585266113}, {"X": 0.3288315236568451, "Y": 0.4410451352596283}, {"X": 0.32883140444755554, "Y": 0.4521503448486328}, {"X": 0.3119468688964844, "Y": 0.45215025544166565}]}, "Id": "ff904271-a672-4f14-b1a9-fe399a8439fb"}, {"BlockType": "WORD", "Confidence": 90.29137420654297, "Text": "2.5", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.053179994225502014, "Height": 0.025738097727298737, "Left": 0.3438423275947571, "Top": 0.4263506531715393}, "Polygon": [{"X": 0.34384262561798096, "Y": 0.4263506531715393}, {"X": 0.3970223069190979, "Y": 0.4263509213924408}, {"X": 0.39702197909355164, "Y": 0.45208874344825745}, {"X": 0.3438423275947571, "Y": 0.45208850502967834}]}, "Id": "b4997170-b537-4f45-9d71-4e78f6e24838"}, {"BlockType": "WORD", "Confidence": 79.88570404052734, "Text": "2.5spots2sk", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19500724971294403, "Height": 0.032353948801755905, "Left": 0.344271719455719, "Top": 0.4241252541542053}, "Polygon": [{"X": 0.3442721366882324, "Y": 0.4241252541542053}, {"X": 0.5392789840698242, "Y": 0.42412620782852173}, {"X": 0.539278507232666, "Y": 0.45647919178009033}, {"X": 0.344271719455719, "Y": 0.4564782977104187}]}, "Id": "07b5f18a-2eb6-49c1-a044-942577b0a7c6"}, {"BlockType": "WORD", "Confidence": 91.64341735839844, "Text": "spots", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07331287860870361, "Height": 0.02672659046947956, "Left": 0.39954695105552673, "Top": 0.42418554425239563}, "Polygon": [{"X": 0.3995473086833954, "Y": 0.42418554425239563}, {"X": 0.47285985946655273, "Y": 0.42418593168258667}, {"X": 0.4728594720363617, "Y": 0.45091214776039124}, {"X": 0.39954695105552673, "Y": 0.4509117901325226}]}, "Id": "ae005b3e-e534-4e14-ba16-3df8b8e769fb"}, {"BlockType": "WORD", "Confidence": 57.76606369018555, "Text": "2sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06474862992763519, "Height": 0.02267962321639061, "Left": 0.4758000671863556, "Top": 0.43400898575782776}, "Polygon": [{"X": 0.47580039501190186, "Y": 0.43400898575782776}, {"X": 0.5405486822128296, "Y": 0.434009313583374}, {"X": 0.5405483841896057, "Y": 0.45668861269950867}, {"X": 0.4758000671863556, "Y": 0.4566883146762848}]}, "Id": "90d9b969-cacf-4115-8b5d-abb4d3d1bf67"}, {"BlockType": "WORD", "Confidence": 68.98178100585938, "Text": "0820", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09193702042102814, "Height": 0.012241599150002003, "Left": 0.876714289188385, "Top": 0.4467671513557434}, "Polygon": [{"X": 0.8767144680023193, "Y": 0.4467671513557434}, {"X": 0.968651294708252, "Y": 0.44676756858825684}, {"X": 0.9686510562896729, "Y": 0.45900875329971313}, {"X": 0.876714289188385, "Y": 0.4590083062648773}]}, "Id": "6b0d32d3-fe1c-4770-a011-23f84f01c20c"}, {"BlockType": "WORD", "Confidence": 66.52921295166016, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05982830375432968, "Height": 0.013329551555216312, "Left": 0.10113867372274399, "Top": 0.46564847230911255}, "Polygon": [{"X": 0.10113880038261414, "Y": 0.46564847230911255}, {"X": 0.16096697747707367, "Y": 0.46564874053001404}, {"X": 0.16096684336662292, "Y": 0.4789780080318451}, {"X": 0.10113867372274399, "Y": 0.478977769613266}]}, "Id": "8cd12bd1-1793-4335-8092-aebc0ed102b3"}, {"BlockType": "WORD", "Confidence": 99.21715545654297, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07374710589647293, "Height": 0.013857371173799038, "Left": 0.16612720489501953, "Top": 0.464786171913147}, "Polygon": [{"X": 0.16612733900547028, "Y": 0.464786171913147}, {"X": 0.23987431824207306, "Y": 0.46478649973869324}, {"X": 0.23987415432929993, "Y": 0.478643536567688}, {"X": 0.16612720489501953, "Y": 0.4786432087421417}]}, "Id": "ad18d20b-c8c2-41ad-83a4-850b1ecf7a72"}, {"BlockType": "WORD", "Confidence": 89.39303588867188, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007670368067920208, "Height": 0.002899251412600279, "Left": 0.2462003231048584, "Top": 0.47099021077156067}, "Polygon": [{"X": 0.24620036780834198, "Y": 0.47099021077156067}, {"X": 0.2538706958293915, "Y": 0.47099024057388306}, {"X": 0.2538706660270691, "Y": 0.47388947010040283}, {"X": 0.2462003231048584, "Y": 0.47388944029808044}]}, "Id": "ebfa8382-8dcd-497c-8d78-9eb7b67b61b5"}, {"BlockType": "WORD", "Confidence": 83.65653228759766, "Text": "Metam", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06635316461324692, "Height": 0.013391062617301941, "Left": 0.26059502363204956, "Top": 0.4643881022930145}, "Polygon": [{"X": 0.2605951726436615, "Y": 0.4643881022930145}, {"X": 0.3269481956958771, "Y": 0.4643884003162384}, {"X": 0.32694801688194275, "Y": 0.4777791500091553}, {"X": 0.26059502363204956, "Y": 0.4777788817882538}]}, "Id": "544c0880-1a60-48aa-b563-f14fc7a43395"}, {"BlockType": "WORD", "Confidence": 94.76104736328125, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010984264314174652, "Height": 0.010626890696585178, "Left": 0.3602912425994873, "Top": 0.4665246605873108}, "Polygon": [{"X": 0.36029139161109924, "Y": 0.4665246605873108}, {"X": 0.37127551436424255, "Y": 0.4665246903896332}, {"X": 0.3712753653526306, "Y": 0.4771515429019928}, {"X": 0.3602912425994873, "Y": 0.477151483297348}]}, "Id": "98277778-684a-4c00-aefa-a922799cbe93"}, {"BlockType": "WORD", "Confidence": 95.5909194946289, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01062251441180706, "Height": 0.010421774350106716, "Left": 0.43348512053489685, "Top": 0.4659290313720703}, "Polygon": [{"X": 0.4334852695465088, "Y": 0.4659290313720703}, {"X": 0.44410762190818787, "Y": 0.4659290909767151}, {"X": 0.4441075026988983, "Y": 0.4763508141040802}, {"X": 0.43348512053489685, "Y": 0.4763507544994354}]}, "Id": "4ac0a3a3-4c4d-4000-979f-d6c6ae579e95"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007623989600688219, "Height": 0.010931368917226791, "Left": 0.4762982428073883, "Top": 0.4650232791900635}, "Polygon": [{"X": 0.47629839181900024, "Y": 0.4650232791900635}, {"X": 0.48392224311828613, "Y": 0.46502330899238586}, {"X": 0.4839220643043518, "Y": 0.47595465183258057}, {"X": 0.4762982428073883, "Y": 0.4759546220302582}]}, "Id": "329ddc86-81f6-417b-a63a-92f622a0ba1d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BASKET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05824160948395729, "Height": 0.011431433260440826, "Left": 0.490022748708725, "Top": 0.46449872851371765}, "Polygon": [{"X": 0.4900228977203369, "Y": 0.46449872851371765}, {"X": 0.5482643842697144, "Y": 0.46449896693229675}, {"X": 0.54826420545578, "Y": 0.4759301543235779}, {"X": 0.490022748708725, "Y": 0.4759298861026764}]}, "Id": "9b3719e1-1509-4788-915e-0ae923949c16"}, {"BlockType": "WORD", "Confidence": 98.92617797851562, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08913788944482803, "Height": 0.01155517902225256, "Left": 0.5527153611183167, "Top": 0.46400919556617737}, "Polygon": [{"X": 0.552715539932251, "Y": 0.46400919556617737}, {"X": 0.6418532133102417, "Y": 0.4640096127986908}, {"X": 0.6418530344963074, "Y": 0.4755643904209137}, {"X": 0.5527153611183167, "Y": 0.47556400299072266}]}, "Id": "dc463e39-2ad8-4cf5-93cb-e252dfa46aa5"}, {"BlockType": "WORD", "Confidence": 99.17350769042969, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1008332222700119, "Height": 0.011635120026767254, "Left": 0.6636203527450562, "Top": 0.4630569517612457}, "Polygon": [{"X": 0.6636205315589905, "Y": 0.4630569517612457}, {"X": 0.7644535899162292, "Y": 0.46305742859840393}, {"X": 0.7644534111022949, "Y": 0.47469207644462585}, {"X": 0.6636203527450562, "Y": 0.47469162940979004}]}, "Id": "7550f430-5976-46db-9775-6a8e5874dcdc"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "0840", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09090698510408401, "Height": 0.016911448910832405, "Left": 0.8822465538978577, "Top": 0.4679184854030609}, "Polygon": [{"X": 0.8822469115257263, "Y": 0.4679184854030609}, {"X": 0.9731535911560059, "Y": 0.46791890263557434}, {"X": 0.9731532335281372, "Y": 0.48482993245124817}, {"X": 0.8822465538978577, "Y": 0.48482954502105713}]}, "Id": "9fb50418-2a43-4eaf-a3c7-bddb83be580a"}, {"BlockType": "WORD", "Confidence": 34.82541275024414, "Text": "/smSk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13076138496398926, "Height": 0.045546241104602814, "Left": 0.11104825884103775, "Top": 0.5050631761550903}, "Polygon": [{"X": 0.11104869842529297, "Y": 0.5050631761550903}, {"X": 0.2418096512556076, "Y": 0.5050637125968933}, {"X": 0.24180914461612701, "Y": 0.5506094098091125}, {"X": 0.11104825884103775, "Y": 0.5506089329719543}]}, "Id": "3f03129f-c23b-4aa0-a363-f6a24ecf45d4"}, {"BlockType": "WORD", "Confidence": 97.275390625, "Text": "=", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.014926322735846043, "Height": 0.012038828805088997, "Left": 0.2485286295413971, "Top": 0.5316933393478394}, "Polygon": [{"X": 0.24852876365184784, "Y": 0.5316933393478394}, {"X": 0.26345494389533997, "Y": 0.5316933989524841}, {"X": 0.2634548246860504, "Y": 0.5437321662902832}, {"X": 0.2485286295413971, "Y": 0.5437321066856384}]}, "Id": "aa62f134-7b1d-4c0f-91d9-fb58f9d3ca9f"}, {"BlockType": "WORD", "Confidence": 54.001312255859375, "Text": "/", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.006859841290861368, "Height": 0.027843687683343887, "Left": 0.2899521589279175, "Top": 0.5168160200119019}, "Polygon": [{"X": 0.28995248675346375, "Y": 0.5168160200119019}, {"X": 0.2968119978904724, "Y": 0.5168160796165466}, {"X": 0.29681167006492615, "Y": 0.5446597337722778}, {"X": 0.2899521589279175, "Y": 0.5446597337722778}]}, "Id": "9b8dae56-a139-471a-b9f2-b7077ad1ebce"}, {"BlockType": "WORD", "Confidence": 91.68128204345703, "Text": "spot", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07522521167993546, "Height": 0.03484600409865379, "Left": 0.30030184984207153, "Top": 0.5182530283927917}, "Polygon": [{"X": 0.3003022372722626, "Y": 0.5182530283927917}, {"X": 0.3755270540714264, "Y": 0.5182533264160156}, {"X": 0.3755266070365906, "Y": 0.5530990362167358}, {"X": 0.30030184984207153, "Y": 0.553098738193512}]}, "Id": "6ba21742-e045-4770-bf44-4aa87133d016"}, {"BlockType": "WORD", "Confidence": 40.71249008178711, "Text": "&", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.026127200573682785, "Height": 0.032245784997940063, "Left": 0.46722880005836487, "Top": 0.5080786943435669}, "Polygon": [{"X": 0.4672292470932007, "Y": 0.5080786943435669}, {"X": 0.49335598945617676, "Y": 0.5080787539482117}, {"X": 0.49335554242134094, "Y": 0.5403244495391846}, {"X": 0.46722880005836487, "Y": 0.540324330329895}]}, "Id": "bc8f4099-335a-4419-9f80-c70291eb1b07"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "1050", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06188691779971123, "Height": 0.0175942275673151, "Left": 0.8748548626899719, "Top": 0.5267840623855591}, "Polygon": [{"X": 0.8748551607131958, "Y": 0.5267840623855591}, {"X": 0.9367417693138123, "Y": 0.5267843008041382}, {"X": 0.9367414116859436, "Y": 0.5443782806396484}, {"X": 0.8748548626899719, "Y": 0.5443780422210693}]}, "Id": "2ffb6d64-5c8e-4f96-b421-0051927caf47"}, {"BlockType": "WORD", "Confidence": 22.70667266845703, "Text": "D", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0516221709549427, "Height": 0.07295437902212143, "Left": 0.00040261223330162466, "Top": 0.5279433727264404}, "Polygon": [{"X": 0.00040320883272215724, "Y": 0.5279433727264404}, {"X": 0.05202478542923927, "Y": 0.5279435515403748}, {"X": 0.05202414095401764, "Y": 0.6008977293968201}, {"X": 0.00040261223330162466, "Y": 0.6008976101875305}]}, "Id": "33cc5e2c-85bb-4e33-9165-898606cbe1d0"}, {"BlockType": "WORD", "Confidence": 87.47508239746094, "Text": "ELyRia", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.15374040603637695, "Height": 0.03579412028193474, "Left": 0.08873278647661209, "Top": 0.5466986298561096}, "Polygon": [{"X": 0.08873311430215836, "Y": 0.5466986298561096}, {"X": 0.24247319996356964, "Y": 0.5466992259025574}, {"X": 0.2424727976322174, "Y": 0.5824927687644958}, {"X": 0.08873278647661209, "Y": 0.5824922323226929}]}, "Id": "87a85934-c781-4a75-a8b8-6fdd59b83753"}, {"BlockType": "WORD", "Confidence": 82.94464111328125, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.024065649136900902, "Height": 0.025540361180901527, "Left": 0.366391122341156, "Top": 0.5430426597595215}, "Polygon": [{"X": 0.3663914203643799, "Y": 0.5430426597595215}, {"X": 0.39045676589012146, "Y": 0.543042778968811}, {"X": 0.3904564380645752, "Y": 0.5685830116271973}, {"X": 0.366391122341156, "Y": 0.5685829520225525}]}, "Id": "e612b2e2-58fd-4102-bd25-54574ed23869"}, {"BlockType": "WORD", "Confidence": 74.30424499511719, "Text": "Pickup", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1276935189962387, "Height": 0.028271706774830818, "Left": 0.4700011909008026, "Top": 0.5402073264122009}, "Polygon": [{"X": 0.47000157833099365, "Y": 0.5402073264122009}, {"X": 0.5976946949958801, "Y": 0.5402078032493591}, {"X": 0.5976942777633667, "Y": 0.568479061126709}, {"X": 0.4700011909008026, "Y": 0.5684785842895508}]}, "Id": "c8588a14-69eb-4cc0-a8a7-ce0e3215a6a4"}, {"BlockType": "WORD", "Confidence": 99.45252227783203, "Text": "1055", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07308492064476013, "Height": 0.019535735249519348, "Left": 0.8638453483581543, "Top": 0.5707002282142639}, "Polygon": [{"X": 0.863845705986023, "Y": 0.5707002282142639}, {"X": 0.936930239200592, "Y": 0.570700466632843}, {"X": 0.9369298815727234, "Y": 0.5902359485626221}, {"X": 0.8638453483581543, "Y": 0.590235710144043}]}, "Id": "a49275af-3798-4ed6-aea4-7c57263acce5"}, {"BlockType": "WORD", "Confidence": 91.4805908203125, "Text": "(EMC)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13689275085926056, "Height": 0.041787926107645035, "Left": 0.05756580829620361, "Top": 0.5943755507469177}, "Polygon": [{"X": 0.05756617709994316, "Y": 0.5943755507469177}, {"X": 0.19445855915546417, "Y": 0.5943759679794312}, {"X": 0.19445812702178955, "Y": 0.6361634731292725}, {"X": 0.05756580829620361, "Y": 0.6361631155014038}]}, "Id": "beda33ac-117c-49a2-9466-1a6ccf6d1fcf"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "YOUNGSTOWN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14146214723587036, "Height": 0.016021793708205223, "Left": 0.44487854838371277, "Top": 0.9240139126777649}, "Polygon": [{"X": 0.4448787569999695, "Y": 0.9240139722824097}, {"X": 0.5863407254219055, "Y": 0.9240139126777649}, {"X": 0.5863404273986816, "Y": 0.9400356411933899}, {"X": 0.44487854838371277, "Y": 0.9400357007980347}]}, "Id": "c41dd060-b69e-4cb6-971c-5c0b47e17392"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "ff1ebe20-fc5a-4320-8897-43cf97797526", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "ff1ebe20-fc5a-4320-8897-43cf97797526", "content-type": "application/x-amz-json-1.1", "content-length": "65367", "date": "Wed, 24 Sep 2025 13:40:03 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ae5971f8-4acc-4ecc-85b8-386ac655d4a5", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:40:07 GMT", "content-type": "application/json", "content-length": "1405", "connection": "keep-alive", "x-amzn-requestid": "ae5971f8-4acc-4ecc-85b8-386ac655d4a5"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have only page 1 content. Determine document type. The text seems like a work order list with work order numbers, times, truck ready times, driver, etc. Looks like a \"Work Order\" or \"Truck Manifest\" maybe. Not clearly any of the defined types. Let's see keywords: \"WORK ORDER #\" appears. \"SO-\" maybe Sales Order. But likely it's a work order sheet for a truck. Not invoice, bol, pod, rate_confirmation, etc. Could be a \"log\" type? It could be a log of truck activity: driver log, activity record. The doc has times, truck ready time, departure time, dock time, etc. So it might be \"log\" – driver log or activity record.\n\nDefinition of log: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log or 3. Driver detention certificate. This page has times and driver. So \"log\" seems plausible.\n\nCheck for \"log\" keywords: \"Truck Ready Time\", \"Driver\", \"Dock Time\", \"Departure Time\" – yes. So classify as \"log\".\n\nThus output: page 1 -> doc_type \"log\".\n\nWe need to output JSON with documents array."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"log\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2804, "outputTokens": 273, "totalTokens": 3077}, "metrics": {"latencyMs": 1354}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/d8a84095_8_log_9.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nDATE: 13-Jun\nUNIT #: 6063/283420\nTruck Ready Time: 5:30\nDRIVER: <PERSON>ARRA\nTruck Departure: 5:30\nDEPARTURE TIME: 5am\nPICK-\nDock Time\nCUSTOMER\nUP DEL\nWORK ORDER #\nX Vari-Wall Isk Tube = spot X X 2sk\n1315\nSO-02132290\n2132051\n1345\nD Z General D Extrusion\n1sk\n1230\nX\nX ***********\n1250\nx\nI 3sm Bsk = 2.5 2.5spots2sk spots 2sk\n0820\nParker Hannifin - Metam\nX\nX\n1 BASKET ********** ***********\n0840\n/smSk = / spot\n&\n1050\nD\nELyRia\nX\nPickup\n1055\n(EMC)\nYOUNGSTOWN\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}