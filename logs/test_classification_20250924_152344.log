2025-09-24 15:23:44,333 - INFO - Logging initialized. Log file: logs/test_classification_20250924_152344.log
2025-09-24 15:23:44,334 - INFO - 📁 Found 1 files to process
2025-09-24 15:23:44,334 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:23:44,334 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 15:23:44,334 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 15:23:44,334 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 15:23:44,334 - INFO - ⬆️ [15:23:44] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:23:47,950 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:23:47,951 - INFO - 🔍 [15:23:47] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:23:47,952 - INFO - Initializing TextractProcessor...
2025-09-24 15:23:47,968 - INFO - Initializing BedrockProcessor...
2025-09-24 15:23:47,977 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:23:47,977 - INFO - Processing PDF from S3...
2025-09-24 15:23:47,978 - INFO - Downloading PDF from S3 to /tmp/tmpn7rk534p/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:23:50,343 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:23:50,343 - INFO - Splitting PDF into individual pages...
2025-09-24 15:23:50,345 - INFO - Splitting PDF f5c9128d_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:23:50,348 - INFO - Split PDF into 1 pages
2025-09-24 15:23:50,348 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:23:50,348 - INFO - Expected pages: [1]
2025-09-24 15:23:57,785 - INFO - Page 1: Extracted 939 characters, 64 lines from f5c9128d_A34CDFDJ66EDOZEKZWJL_0d677d43_page_001.pdf
2025-09-24 15:23:57,786 - INFO - Successfully processed page 1
2025-09-24 15:23:57,786 - INFO - Combined 1 pages into final text
2025-09-24 15:23:57,786 - INFO - Text validation for f5c9128d_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:23:57,787 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:23:57,787 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:23:59,991 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c0fc3286-f243-46cd-b2a5-c245de42b6dc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:53:59 GMT', 'content-type': 'application/json', 'content-length': '246', 'connection': 'keep-alive', 'x-amzn-requestid': 'c0fc3286-f243-46cd-b2a5-c245de42b6dc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': ''}}}]}}, 'stopReason': 'max_tokens', 'usage': {'inputTokens': 2826, 'outputTokens': 0, 'totalTokens': 2826}, 'metrics': {'latencyMs': 147}}
2025-09-24 15:23:59,992 - ERROR - Max tokens reached
2025-09-24 15:23:59,992 - ERROR - Failed to extract tool response: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file
2025-09-24 15:23:59,993 - ERROR - Processing failed for s3://document-extraction-logistically/temp/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf: Unexpected tool response format from Bedrock
2025-09-24 15:23:59,993 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf: Unexpected tool response format from Bedrock
2025-09-24 15:23:59,994 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response
    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")
Exception: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 15:23:59,995 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 15:23:59,996 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf: Unexpected tool response format from Bedrock
2025-09-24 15:23:59,999 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response
    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")
Exception: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 15:24:00,000 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 15:24:01,052 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:24:01,052 - INFO - 
📊 Processing Summary:
2025-09-24 15:24:01,052 - INFO -    Total files: 1
2025-09-24 15:24:01,052 - INFO -    Successful: 0
2025-09-24 15:24:01,052 - INFO -    Failed: 1
2025-09-24 15:24:01,052 - INFO -    Duration: 16.72 seconds
2025-09-24 15:24:01,052 - INFO -    Output directory: output
2025-09-24 15:24:01,052 - ERROR - 
❌ Errors:
2025-09-24 15:24:01,052 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf: Unexpected tool response format from Bedrock
2025-09-24 15:24:01,053 - INFO - 
📋 No files were successfully processed.
2025-09-24 15:24:01,053 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 15:24:01,053 - ERROR - 🐛 Debug info: {'exception': Exception('Unexpected tool response format from Bedrock'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response\n    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")\nException: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock\n    content = bedrock_processor.extract_tool_response(response)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response\n    raise Exception("Unexpected tool response format from Bedrock")\nException: Unexpected tool response format from Bedrock\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf', 's3_uri': 's3://document-extraction-logistically/temp/f5c9128d_A34CDFDJ66EDOZEKZWJL.pdf', 'location': 'process_single_file'}
2025-09-24 15:24:01,053 - ERROR - 🐛 Location: process_single_file
2025-09-24 15:24:01,053 - ERROR - 🐛 Exception: Unexpected tool response format from Bedrock
2025-09-24 15:24:01,053 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response
    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")
Exception: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

