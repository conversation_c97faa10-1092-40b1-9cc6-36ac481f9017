{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9465000629425049, "Height": 0.9948314428329468, "Left": 0.053499944508075714, "Top": 0.0051685506477952}, "Polygon": [{"X": 0.053499944508075714, "Y": 0.025295259431004524}, {"X": 0.8353887796401978, "Y": 0.0051685506477952}, {"X": 1.0, "Y": 0.967174768447876}, {"X": 0.19245637953281403, "Y": 1.0}]}, "Id": "51e9b360-84be-4619-a374-d19da18ab9ad", "Relationships": [{"Type": "CHILD", "Ids": ["e8031910-eadd-44f1-b2ce-2243fd1d34f6", "0c9e6ed2-4b01-4b7a-adb6-6760e71b7bc6", "2bd63b44-518e-4afe-8000-720616b91750", "7847bb20-840c-4e36-abb2-727e10393d5d", "3ecca917-5beb-423c-8b59-1e31f9fd392d", "08de1610-959d-4f48-92aa-d68df02cad8a", "0d1f78cf-31c0-41c0-87d3-1843b2c136c6", "792f10c0-dc97-4312-9b32-fe73c57cc2f0", "4edd0cf3-2550-44d7-bc71-2bd8e2e5f5e5", "47793f18-57e6-4ea0-a840-a7bd5b54b05d", "c0af5488-bf1b-40f6-bb73-e2d95718a0c8", "8b8f6e19-f113-4d3a-823b-f8706a129693", "12f52117-d1dc-4cef-a4ae-19e36547a245", "a732ce0f-1f08-47a7-b7a9-ff2ce855d529", "6ed26125-1400-4a4a-aa19-d029d1c98fe1", "a7e4a0d1-e6d8-4072-8f87-9815ab8d9bb3", "3cf821d7-97ef-4e4b-9407-4db3f335d259", "ccf25bec-abb7-4e94-9414-84a44e896ef6", "d4b34db6-69fc-4b0c-ae28-e0216d746d0b", "0584706c-1739-4a57-a507-50a8a7878a39", "7605eb05-81f6-4feb-a3dd-eca93a1451e1", "1c7e70fa-a063-481d-a6f6-8da80cc691bc", "5bc3aae9-0982-43cb-b4fe-a23a9f66df74", "529447ad-d96f-4891-848a-a9c918b0f9ee", "57896d1a-eadb-46c9-9931-f5aac5ec58b1", "6607cd29-0f1c-4969-af8b-b4004bd40bd2", "63b8e44f-cfa7-4330-86f6-68a2e98eb862", "08f4198e-e5da-4011-8c43-3a84e6a1c36c", "10f9d535-8f9a-4508-8037-0c4085190f45"]}]}, {"BlockType": "LINE", "Confidence": 74.54181671142578, "Text": "*1396602*", "Geometry": {"BoundingBox": {"Width": 0.1349448561668396, "Height": 0.015623306855559349, "Left": 0.13302426040172577, "Top": 0.08920320123434067}, "Polygon": [{"X": 0.13302426040172577, "Y": 0.09278835356235504}, {"X": 0.2660951316356659, "Y": 0.08920320123434067}, {"X": 0.26796913146972656, "Y": 0.10121074318885803}, {"X": 0.134793221950531, "Y": 0.10482650995254517}]}, "Id": "e8031910-eadd-44f1-b2ce-2243fd1d34f6", "Relationships": [{"Type": "CHILD", "Ids": ["88b7c6a1-38dd-4807-83db-595a0485d878"]}]}, {"BlockType": "LINE", "Confidence": 91.2978744506836, "Text": "<PERSON><PERSON> of New England", "Geometry": {"BoundingBox": {"Width": 0.42142796516418457, "Height": 0.027393829077482224, "Left": 0.13648922741413116, "Top": 0.10871437191963196}, "Polygon": [{"X": 0.13648922741413116, "Y": 0.12019240111112595}, {"X": 0.555145263671875, "Y": 0.10871437191963196}, {"X": 0.5579171776771545, "Y": 0.12450265884399414}, {"X": 0.1388273686170578, "Y": 0.13610820472240448}]}, "Id": "0c9e6ed2-4b01-4b7a-adb6-6760e71b7bc6", "Relationships": [{"Type": "CHILD", "Ids": ["91339ebd-855b-4a00-9a2e-90847b030b7b", "81668a2f-a8f3-4703-a2ba-5701fa35ed67", "0cc34e8c-cfe5-4559-9f7e-b5fd379d6926", "6f58f396-0846-469b-9481-e01d021e9b1a", "4ba166ad-dd56-44aa-88dd-e81e096c5b71"]}]}, {"BlockType": "LINE", "Confidence": 97.2920150756836, "Text": "A Division of Casella Waste Systems Inc", "Geometry": {"BoundingBox": {"Width": 0.4747917652130127, "Height": 0.02734806388616562, "Left": 0.1409294754266739, "Top": 0.13180091977119446}, "Polygon": [{"X": 0.1409294754266739, "Y": 0.144951730966568}, {"X": 0.6131994724273682, "Y": 0.13180091977119446}, {"X": 0.6157212257385254, "Y": 0.14586970210075378}, {"X": 0.14301592111587524, "Y": 0.15914897620677948}]}, "Id": "2bd63b44-518e-4afe-8000-720616b91750", "Relationships": [{"Type": "CHILD", "Ids": ["fa4ca7be-6c54-49e1-a04a-f7b9c5392c21", "e039a7a9-58ca-41ee-80b9-d7ae4d3285bc", "432e9001-780a-49cc-9a4a-2c90ea97c2ac", "75307024-0658-41c8-97b0-18f0ffcd500d", "3b445e71-7292-458c-9f7b-18a6b9bd743a", "aee83ce5-467c-435f-bbe0-ce9882163bbb", "3a75439a-4aa2-418f-9203-af8ea8658a03"]}]}, {"BlockType": "LINE", "Confidence": 97.77172088623047, "Text": "14 BUNKER HILL INDUSTRIAL PARK", "Geometry": {"BoundingBox": {"Width": 0.48277246952056885, "Height": 0.027260949835181236, "Left": 0.1463705152273178, "Top": 0.14523006975650787}, "Polygon": [{"X": 0.1463705152273178, "Y": 0.15872040390968323}, {"X": 0.6266871094703674, "Y": 0.14523006975650787}, {"X": 0.6291429996490479, "Y": 0.15887385606765747}, {"X": 0.14839747548103333, "Y": 0.17249101400375366}]}, "Id": "7847bb20-840c-4e36-abb2-727e10393d5d", "Relationships": [{"Type": "CHILD", "Ids": ["725d99fa-1ddd-4619-91a8-e6c49b475abd", "1bb74d59-1c66-44bd-8719-5d55ade8190f", "95e97d15-864e-4663-a058-558d19852817", "c997db90-6154-4311-b334-d44b7687c44b", "f47ebefd-e2af-45c0-b594-c85aa6bbfd7b"]}]}, {"BlockType": "LINE", "Confidence": 96.40193176269531, "Text": "617-242-7746P SCALE HOUSE617-242-3649F", "Geometry": {"BoundingBox": {"Width": 0.5658025741577148, "Height": 0.029978478327393532, "Left": 0.14564216136932373, "Top": 0.1564374566078186}, "Polygon": [{"X": 0.14564216136932373, "Y": 0.17238923907279968}, {"X": 0.7088716626167297, "Y": 0.1564374566078186}, {"X": 0.7114447355270386, "Y": 0.1703125685453415}, {"X": 0.14770419895648956, "Y": 0.18641594052314758}]}, "Id": "3ecca917-5beb-423c-8b59-1e31f9fd392d", "Relationships": [{"Type": "CHILD", "Ids": ["60ea2c93-8878-40fe-b739-82e26a4db2d0", "76cf0fe4-7aa0-4a30-b73c-26273128f54b", "04a1d956-197d-4114-aaea-1b794b8f78b7"]}]}, {"BlockType": "LINE", "Confidence": 76.67768859863281, "Text": "Ticket 1396602", "Geometry": {"BoundingBox": {"Width": 0.19627320766448975, "Height": 0.017217209562659264, "Left": 0.15827874839305878, "Top": 0.23358537256717682}, "Polygon": [{"X": 0.15827874839305878, "Y": 0.23931820690631866}, {"X": 0.3527167737483978, "Y": 0.23358537256717682}, {"X": 0.3545519709587097, "Y": 0.24502672255039215}, {"X": 0.15996922552585602, "Y": 0.25080257654190063}]}, "Id": "08de1610-959d-4f48-92aa-d68df02cad8a", "Relationships": [{"Type": "CHILD", "Ids": ["90fc0e21-9f28-4b9e-9acd-84c082454f1b", "4120ab0f-1861-426d-b35a-6c93a9e4ccd4"]}]}, {"BlockType": "LINE", "Confidence": 90.93949127197266, "Text": "Date: 9/15/2025", "Geometry": {"BoundingBox": {"Width": 0.19395454227924347, "Height": 0.01740848273038864, "Left": 0.17769716680049896, "Top": 0.25007203221321106}, "Polygon": [{"X": 0.17769716680049896, "Y": 0.2557917535305023}, {"X": 0.36977243423461914, "Y": 0.25007203221321106}, {"X": 0.37165170907974243, "Y": 0.26171746850013733}, {"X": 0.17943112552165985, "Y": 0.2674805223941803}]}, "Id": "0d1f78cf-31c0-41c0-87d3-1843b2c136c6", "Relationships": [{"Type": "CHILD", "Ids": ["6fec6ece-9b15-4959-bcda-a1e93a22978c", "6645a2d1-207e-4f80-b7a2-6e436779c1dd"]}]}, {"BlockType": "LINE", "Confidence": 75.1393051147461, "Text": "Time 13:47:04-14:24:30", "Geometry": {"BoundingBox": {"Width": 0.309985876083374, "Height": 0.020979449152946472, "Left": 0.17877718806266785, "Top": 0.26361745595932007}, "Polygon": [{"X": 0.17877718806266785, "Y": 0.272880882024765}, {"X": 0.4867933988571167, "Y": 0.26361745595932007}, {"X": 0.48876306414604187, "Y": 0.27526381611824036}, {"X": 0.18051405251026154, "Y": 0.28459692001342773}]}, "Id": "792f10c0-dc97-4312-9b32-fe73c57cc2f0", "Relationships": [{"Type": "CHILD", "Ids": ["8afb5d81-48af-481b-8b9b-0ce7e49b0082", "e4e49a88-75f2-49eb-8c33-fac5d4e090ed"]}]}, {"BlockType": "LINE", "Confidence": 79.66268920898438, "Text": "Customer KO00508/EARTHWATCH WASTE SYSTEM", "Geometry": {"BoundingBox": {"Width": 0.6590497493743896, "Height": 0.032646309584379196, "Left": 0.1927410215139389, "Top": 0.28414103388786316}, "Polygon": [{"X": 0.1927410215139389, "Y": 0.3042503595352173}, {"X": 0.8493978977203369, "Y": 0.28414103388786316}, {"X": 0.8517907857894897, "Y": 0.29651886224746704}, {"X": 0.1946074515581131, "Y": 0.31678733229637146}]}, "Id": "4edd0cf3-2550-44d7-bc71-2bd8e2e5f5e5", "Relationships": [{"Type": "CHILD", "Ids": ["8e53716f-c40c-4e3b-9aa8-1aa8abb5dc77", "d0815a94-369d-4612-8856-b6b67005896b", "8d0b830f-e099-40ca-9d19-d65f3dacc4c8", "1bc26092-e1a9-4ebe-8561-130ca030aa84"]}]}, {"BlockType": "LINE", "Confidence": 91.03634643554688, "Text": "Truck 2306", "Geometry": {"BoundingBox": {"Width": 0.1494765430688858, "Height": 0.01627921126782894, "Left": 0.21616679430007935, "Top": 0.3150462508201599}, "Polygon": [{"X": 0.21616679430007935, "Y": 0.3196074664592743}, {"X": 0.3637707233428955, "Y": 0.3150462508201599}, {"X": 0.36564332246780396, "Y": 0.32673075795173645}, {"X": 0.217927947640419, "Y": 0.3313254714012146}]}, "Id": "47793f18-57e6-4ea0-a840-a7bd5b54b05d", "Relationships": [{"Type": "CHILD", "Ids": ["8808ede2-f7eb-4b51-85f5-9e0878e04fa1", "f0c82231-0cb7-479a-a79d-4eae78eac4d9"]}]}, {"BlockType": "LINE", "Confidence": 80.87571716308594, "Text": "Trailer 4560", "Geometry": {"BoundingBox": {"Width": 0.15783356130123138, "Height": 0.016529425978660583, "Left": 0.20557381212711334, "Top": 0.3317609131336212}, "Polygon": [{"X": 0.20557381212711334, "Y": 0.33662575483322144}, {"X": 0.36154744029045105, "Y": 0.3317609131336212}, {"X": 0.3634073734283447, "Y": 0.34339022636413574}, {"X": 0.20731663703918457, "Y": 0.348290354013443}]}, "Id": "c0af5488-bf1b-40f6-bb73-e2d95718a0c8", "Relationships": [{"Type": "CHILD", "Ids": ["08efa07f-87df-42d8-8ce9-edab09e5a0a1", "589438bb-5593-4b70-912b-1e47b32c4da8"]}]}, {"BlockType": "LINE", "Confidence": 79.35836029052734, "Text": "Destination: A/A", "Geometry": {"BoundingBox": {"Width": 0.2047024816274643, "Height": 0.01800714060664177, "Left": 0.18068742752075195, "Top": 0.3479258418083191}, "Polygon": [{"X": 0.18068742752075195, "Y": 0.35431158542633057}, {"X": 0.38352346420288086, "Y": 0.3479258418083191}, {"X": 0.38538989424705505, "Y": 0.35950151085853577}, {"X": 0.1824023276567459, "Y": 0.36593297123908997}]}, "Id": "8b8f6e19-f113-4d3a-823b-f8706a129693", "Relationships": [{"Type": "CHILD", "Ids": ["c5cb6450-3bfc-42e7-adbb-d80859220128", "88bdb48e-d891-468e-b305-d5677008d535"]}]}, {"BlockType": "LINE", "Confidence": 81.65606689453125, "Text": "P.O.: EARTH091525-2", "Geometry": {"BoundingBox": {"Width": 0.2796075940132141, "Height": 0.020602134987711906, "Left": 0.2356989085674286, "Top": 0.36044612526893616}, "Polygon": [{"X": 0.2356989085674286, "Y": 0.3692663013935089}, {"X": 0.5133166909217834, "Y": 0.36044612526893616}, {"X": 0.5153064727783203, "Y": 0.3721645474433899}, {"X": 0.23747919499874115, "Y": 0.3810482621192932}]}, "Id": "12f52117-d1dc-4cef-a4ae-19e36547a245", "Relationships": [{"Type": "CHILD", "Ids": ["c47e3af3-6369-4841-9f05-1c86ef479912", "59497d9f-44f5-4f9f-a83d-8f08e8c9799a"]}]}, {"BlockType": "LINE", "Confidence": 75.9239501953125, "Text": "D.O: 2522611", "Geometry": {"BoundingBox": {"Width": 0.1736484318971634, "Height": 0.016938023269176483, "Left": 0.23947714269161224, "Top": 0.38047000765800476}, "Polygon": [{"X": 0.23947714269161224, "Y": 0.385978102684021}, {"X": 0.4112716615200043, "Y": 0.38047000765800476}, {"X": 0.41312557458877563, "Y": 0.39186176657676697}, {"X": 0.24120520055294037, "Y": 0.39740803837776184}]}, "Id": "a732ce0f-1f08-47a7-b7a9-ff2ce855d529", "Relationships": [{"Type": "CHILD", "Ids": ["8b7d4a4c-fe17-4c07-9547-fd3523060b84", "3dd3e851-c9df-436c-9a8c-adbf546d85bf"]}]}, {"BlockType": "LINE", "Confidence": 96.16360473632812, "Text": "Seal: 531833", "Geometry": {"BoundingBox": {"Width": 0.1581163853406906, "Height": 0.016812965273857117, "Left": 0.23941373825073242, "Top": 0.39722904562950134}, "Polygon": [{"X": 0.23941373825073242, "Y": 0.40228191018104553}, {"X": 0.3956364691257477, "Y": 0.39722904562950134}, {"X": 0.39753013849258423, "Y": 0.40895339846611023}, {"X": 0.24118973314762115, "Y": 0.41404202580451965}]}, "Id": "6ed26125-1400-4a4a-aa19-d029d1c98fe1", "Relationships": [{"Type": "CHILD", "Ids": ["eac7bf93-864f-47c0-94e5-cbd4f77825a2", "8f21c430-de7a-485a-a3c2-1079dc7fb8bd"]}]}, {"BlockType": "LINE", "Confidence": 90.96819305419922, "Text": "Comment NAVIGATION GLOBAL LLC", "Geometry": {"BoundingBox": {"Width": 0.4978332221508026, "Height": 0.02882012352347374, "Left": 0.22298648953437805, "Top": 0.41908401250839233}, "Polygon": [{"X": 0.22298648953437805, "Y": 0.43539178371429443}, {"X": 0.7185540199279785, "Y": 0.41908401250839233}, {"X": 0.7208197116851807, "Y": 0.43147554993629456}, {"X": 0.22485817968845367, "Y": 0.44790413975715637}]}, "Id": "a7e4a0d1-e6d8-4072-8f87-9815ab8d9bb3", "Relationships": [{"Type": "CHILD", "Ids": ["fbd699f9-b084-441a-b34e-994a10c1a0be", "5b61ad3a-bf5a-4315-80ce-68d402bdd2fc", "4d7b5ff4-40b6-43cf-9b2c-e33f95620475", "437caf8a-ba3e-420e-b9f5-2d84dc3d8e42"]}]}, {"BlockType": "LINE", "Confidence": 79.60407257080078, "Text": "Grown 75200 L Out Scale 3", "Geometry": {"BoundingBox": {"Width": 0.3468654453754425, "Height": 0.023669663816690445, "Left": 0.20803877711296082, "Top": 0.4861482083797455}, "Polygon": [{"X": 0.20803877711296082, "Y": 0.4978652596473694}, {"X": 0.5528739094734192, "Y": 0.4861482083797455}, {"X": 0.5549042224884033, "Y": 0.4980202913284302}, {"X": 0.20980745553970337, "Y": 0.509817898273468}]}, "Id": "3cf821d7-97ef-4e4b-9407-4db3f335d259", "Relationships": [{"Type": "CHILD", "Ids": ["5b5af999-427e-4ed6-8176-9ccfd98d9f19", "f3141559-22d9-42f3-86d9-16e7cdf8c06d", "d7956d43-be18-47af-a10f-84b8313f10e5", "c031ca2d-e569-4779-965f-eb8ade890134", "ab80a64b-e2f9-446c-964c-39810d1da37b"]}]}, {"BlockType": "LINE", "Confidence": 78.20348358154297, "Text": "Tare: 32100 LIn Scale 3", "Geometry": {"BoundingBox": {"Width": 0.3197806477546692, "Height": 0.023430364206433296, "Left": 0.21757744252681732, "Top": 0.5036036968231201}, "Polygon": [{"X": 0.21757744252681732, "Y": 0.5144906640052795}, {"X": 0.5352433919906616, "Y": 0.5036036968231201}, {"X": 0.5373581051826477, "Y": 0.5160691738128662}, {"X": 0.21943944692611694, "Y": 0.5270340442657471}]}, "Id": "ccf25bec-abb7-4e94-9414-84a44e896ef6", "Relationships": [{"Type": "CHILD", "Ids": ["75a614dd-2a43-47fd-bdf8-22536817a002", "3da7c3d5-9e4d-4287-9388-854d8a66e2f9", "978b244e-8d1b-40f3-8066-e3e60174d483", "0e9871c8-71d7-4b9e-ba5f-d611d524d00c", "5a7d9358-2804-484b-a5ec-2cbd52afcbd8"]}]}, {"BlockType": "LINE", "Confidence": 57.8911247253418, "Text": "Net 431001.", "Geometry": {"BoundingBox": {"Width": 0.18032003939151764, "Height": 0.018047813326120377, "Left": 0.22850248217582703, "Top": 0.5254641771316528}, "Polygon": [{"X": 0.22850248217582703, "Y": 0.5316328406333923}, {"X": 0.4069179594516754, "Y": 0.5254641771316528}, {"X": 0.40882250666618347, "Y": 0.5373018383979797}, {"X": 0.23027247190475464, "Y": 0.5435119867324829}]}, "Id": "d4b34db6-69fc-4b0c-ae28-e0216d746d0b", "Relationships": [{"Type": "CHILD", "Ids": ["e8372443-7c18-4f99-98e3-94e624c6c38c", "e20587ff-1f3a-4420-badb-8bbb0062bba6"]}]}, {"BlockType": "LINE", "Confidence": 75.2853012084961, "Text": "Total 21.55", "Geometry": {"BoundingBox": {"Width": 0.14413875341415405, "Height": 0.016875801607966423, "Left": 0.224258691072464, "Top": 0.5532279014587402}, "Polygon": [{"X": 0.224258691072464, "Y": 0.5582117438316345}, {"X": 0.36652469635009766, "Y": 0.5532279014587402}, {"X": 0.36839744448661804, "Y": 0.5650867223739624}, {"X": 0.22602413594722748, "Y": 0.5701037049293518}]}, "Id": "0584706c-1739-4a57-a507-50a8a7878a39", "Relationships": [{"Type": "CHILD", "Ids": ["be35c171-ae8b-4f8e-a5c0-68a7710fb3c9", "c86d3e64-5d37-4eac-982a-409749671d54"]}]}, {"BlockType": "LINE", "Confidence": 99.67262268066406, "Text": "Materials & Services", "Geometry": {"BoundingBox": {"Width": 0.2577053904533386, "Height": 0.021710123866796494, "Left": 0.21420957148075104, "Top": 0.5846008658409119}, "Polygon": [{"X": 0.21420957148075104, "Y": 0.5937118530273438}, {"X": 0.46985381841659546, "Y": 0.5846008658409119}, {"X": 0.47191494703292847, "Y": 0.5971367359161377}, {"X": 0.21606725454330444, "Y": 0.606311023235321}]}, "Id": "7605eb05-81f6-4feb-a3dd-eca93a1451e1", "Relationships": [{"Type": "CHILD", "Ids": ["d85bce70-527e-4f27-94fc-3c8a91049139", "c861a469-bf2d-409a-bf3a-9cb8c4d2f7d8", "c5999773-d2ef-481d-bb47-818306dc2a15"]}]}, {"BlockType": "LINE", "Confidence": 95.58008575439453, "Text": "Origin: NA/NON APPLICABLE", "Geometry": {"BoundingBox": {"Width": 0.4022722840309143, "Height": 0.029998742043972015, "Left": 0.2565916180610657, "Top": 0.621539831161499}, "Polygon": [{"X": 0.2565916180610657, "Y": 0.6360834836959839}, {"X": 0.6561607122421265, "Y": 0.621539831161499}, {"X": 0.65886390209198, "Y": 0.6368732452392578}, {"X": 0.25890716910362244, "Y": 0.6515385508537292}]}, "Id": "1c7e70fa-a063-481d-a6f6-8da80cc691bc", "Relationships": [{"Type": "CHILD", "Ids": ["398491a0-82f1-4a9e-80bf-e6e0428c8039", "204ff56b-d769-452c-8209-53a3554ec08b", "b50daa7b-411d-457e-af7f-fbb265116337"]}]}, {"BlockType": "LINE", "Confidence": 85.26931762695312, "Text": "Material 539-1/OBGN MIX COMMODITIES OL", "Geometry": {"BoundingBox": {"Width": 0.6081558465957642, "Height": 0.03721608221530914, "Left": 0.2454003393650055, "Top": 0.631058931350708}, "Polygon": [{"X": 0.2454003393650055, "Y": 0.6532685160636902}, {"X": 0.850753903388977, "Y": 0.631058931350708}, {"X": 0.853556215763092, "Y": 0.6458864212036133}, {"X": 0.2476350963115692, "Y": 0.668274998664856}]}, "Id": "5bc3aae9-0982-43cb-b4fe-a23a9f66df74", "Relationships": [{"Type": "CHILD", "Ids": ["f24ca1f7-bfaa-4407-9524-6fb16eef61b4", "a5bfd307-504b-4b98-91ed-6e2bc166c21c", "2ab841cf-e490-48ce-84fc-bdd66c77e8c2", "71dd6052-be8c-45be-9c67-98e25d654b93", "7b2e8e08-b676-412e-8575-caa174993ea6"]}]}, {"BlockType": "LINE", "Confidence": 94.51424407958984, "Text": "Quantity: 21.55 Ton", "Geometry": {"BoundingBox": {"Width": 0.24837225675582886, "Height": 0.024096587672829628, "Left": 0.2465037703514099, "Top": 0.6643572449684143}, "Polygon": [{"X": 0.2465037703514099, "Y": 0.6734654307365417}, {"X": 0.49241435527801514, "Y": 0.6643572449684143}, {"X": 0.49487602710723877, "Y": 0.6792728304862976}, {"X": 0.24873395264148712, "Y": 0.6884537935256958}]}, "Id": "529447ad-d96f-4891-848a-a9c918b0f9ee", "Relationships": [{"Type": "CHILD", "Ids": ["fb4056a1-2631-4aaf-99f6-55e83d1376f7", "72df38ba-cc76-43de-a71e-1aaea4fbab09", "21059a1d-acdd-4d8c-a8f3-c69701c3e94d"]}]}, {"BlockType": "LINE", "Confidence": 87.23593139648438, "Text": "Weighmaster TOM", "Geometry": {"BoundingBox": {"Width": 0.24472206830978394, "Height": 0.02445579320192337, "Left": 0.23882776498794556, "Top": 0.779807448387146}, "Polygon": [{"X": 0.23882776498794556, "Y": 0.7892655730247498}, {"X": 0.48111608624458313, "Y": 0.779807448387146}, {"X": 0.4835498332977295, "Y": 0.7947330474853516}, {"X": 0.24103499948978424, "Y": 0.8042632341384888}]}, "Id": "57896d1a-eadb-46c9-9931-f5aac5ec58b1", "Relationships": [{"Type": "CHILD", "Ids": ["d126aacb-a738-44c1-ba5d-52186fb4404d", "084aaf4b-b0a7-40a5-bab7-11f251ebf28f"]}]}, {"BlockType": "LINE", "Confidence": 58.2774658203125, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.08790715038776398, "Height": 0.015748606994748116, "Left": 0.2811667323112488, "Top": 0.8125483989715576}, "Polygon": [{"X": 0.2811667323112488, "Y": 0.815947413444519}, {"X": 0.36715930700302124, "Y": 0.8125483989715576}, {"X": 0.36907389760017395, "Y": 0.8248768448829651}, {"X": 0.28301510214805603, "Y": 0.828296959400177}]}, "Id": "6607cd29-0f1c-4969-af8b-b4004bd40bd2", "Relationships": [{"Type": "CHILD", "Ids": ["ae65d31d-d90b-4478-b040-71531d83c6b6"]}]}, {"BlockType": "LINE", "Confidence": 92.7193374633789, "Text": "By signing above, I declare that I did NOT", "Geometry": {"BoundingBox": {"Width": 0.6565141081809998, "Height": 0.045436225831508636, "Left": 0.25012052059173584, "Top": 0.83858722448349}, "Polygon": [{"X": 0.25012052059173584, "Y": 0.8649352788925171}, {"X": 0.9030579328536987, "Y": 0.83858722448349}, {"X": 0.9066346287727356, "Y": 0.8574272394180298}, {"X": 0.2529299259185791, "Y": 0.8840234279632568}]}, "Id": "63b8e44f-cfa7-4330-86f6-68a2e98eb862", "Relationships": [{"Type": "CHILD", "Ids": ["2427e33a-1455-47b3-a350-66b5146bf4ed", "d56805e1-3790-42a1-8b0a-4adfd70c2d52", "63643da3-ea8f-4e30-a1cf-e1cd0bf3dbcc", "85b121f7-9810-4b28-80a0-c3170df1b67b", "1150fe3f-e022-476a-8b54-da01669182e1", "0afbcb14-9db1-492d-8fb1-12114d1568e2", "a1510bc2-15f2-4e2b-8362-f05603b1a9d6", "69064303-1b54-4a1a-9c0d-39593ca87461", "a1ff8813-cd59-4154-b925-f8528c2c123c"]}]}, {"BlockType": "LINE", "Confidence": 99.9111099243164, "Text": "deposit any PROHIBITED WASTES", "Geometry": {"BoundingBox": {"Width": 0.5357205867767334, "Height": 0.0400056429207325, "Left": 0.25187596678733826, "Top": 0.8647798299789429}, "Polygon": [{"X": 0.25187596678733826, "Y": 0.8864637613296509}, {"X": 0.7843006253242493, "Y": 0.8647798299789429}, {"X": 0.7875965237617493, "Y": 0.8829070925712585}, {"X": 0.2545709013938904, "Y": 0.9047855138778687}]}, "Id": "08f4198e-e5da-4011-8c43-3a84e6a1c36c", "Relationships": [{"Type": "CHILD", "Ids": ["ed6b815b-3563-46b6-b084-7c32c7a608a5", "aa94fc63-506e-43f4-8d21-f4fbb2efd3be", "b9b7fc14-2e17-41d9-8e30-ab1cf50e841a", "80310bd4-f177-4633-938f-5632c9e53b40"]}]}, {"BlockType": "LINE", "Confidence": 97.54244995117188, "Text": "CONSIGNEE PRINT NAME", "Geometry": {"BoundingBox": {"Width": 0.3197786211967468, "Height": 0.026403063908219337, "Left": 0.2685093879699707, "Top": 0.9629071950912476}, "Polygon": [{"X": 0.2685093879699707, "Y": 0.9763402938842773}, {"X": 0.5861241817474365, "Y": 0.9629071950912476}, {"X": 0.5882880091667175, "Y": 0.9757946729660034}, {"X": 0.27041998505592346, "Y": 0.9893102645874023}]}, "Id": "10f9d535-8f9a-4508-8037-0c4085190f45", "Relationships": [{"Type": "CHILD", "Ids": ["8536f099-d1bc-472a-901d-bea5c7ef5451", "862f5f45-e6b8-4008-a963-03f364a28350", "4f78a2bd-4632-4051-98a5-8303b24b66b8"]}]}, {"BlockType": "WORD", "Confidence": 74.54181671142578, "Text": "*1396602*", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1349448561668396, "Height": 0.015623306855559349, "Left": 0.13302426040172577, "Top": 0.08920320123434067}, "Polygon": [{"X": 0.13302426040172577, "Y": 0.09278835356235504}, {"X": 0.2660951316356659, "Y": 0.08920320123434067}, {"X": 0.26796913146972656, "Y": 0.10121074318885803}, {"X": 0.134793221950531, "Y": 0.10482650995254517}]}, "Id": "88b7c6a1-38dd-4807-83db-595a0485d878"}, {"BlockType": "WORD", "Confidence": 97.50019836425781, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09072184562683105, "Height": 0.014448823407292366, "Left": 0.1366722732782364, "Top": 0.11899951100349426}, "Polygon": [{"X": 0.1366722732782364, "Y": 0.1214384064078331}, {"X": 0.22555986046791077, "Y": 0.11899951100349426}, {"X": 0.22739411890506744, "Y": 0.13098900020122528}, {"X": 0.13843661546707153, "Y": 0.13344833254814148}]}, "Id": "91339ebd-855b-4a00-9a2e-90847b030b7b"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Recycling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11988086998462677, "Height": 0.017601901665329933, "Left": 0.23109139502048492, "Top": 0.1158902496099472}, "Polygon": [{"X": 0.23109139502048492, "Y": 0.11911655962467194}, {"X": 0.34865713119506836, "Y": 0.1158902496099472}, {"X": 0.3509722650051117, "Y": 0.13023343682289124}, {"X": 0.23329609632492065, "Y": 0.13349215686321259}]}, "Id": "81668a2f-a8f3-4703-a2ba-5701fa35ed67"}, {"BlockType": "WORD", "Confidence": 59.98326110839844, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03867132216691971, "Height": 0.012614574283361435, "Left": 0.3529667258262634, "Top": 0.11428337544202805}, "Polygon": [{"X": 0.3529667258262634, "Y": 0.11529204249382019}, {"X": 0.3897334039211273, "Y": 0.11428337544202805}, {"X": 0.3916380703449249, "Y": 0.12588109076023102}, {"X": 0.35484352707862854, "Y": 0.12689794600009918}]}, "Id": "0cc34e8c-cfe5-4559-9f7e-b5fd379d6926"}, {"BlockType": "WORD", "Confidence": 99.44355773925781, "Text": "New", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05808096379041672, "Height": 0.013073433190584183, "Left": 0.3966117799282074, "Top": 0.11226250976324081}, "Polygon": [{"X": 0.3966117799282074, "Y": 0.11380235850811005}, {"X": 0.45275089144706726, "Y": 0.11226250976324081}, {"X": 0.4546927511692047, "Y": 0.123783640563488}, {"X": 0.3985113799571991, "Y": 0.1253359466791153}]}, "Id": "6f58f396-0846-469b-9481-e01d021e9b1a"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "England", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09831393510103226, "Height": 0.01644105277955532, "Left": 0.4592517912387848, "Top": 0.10871437191963196}, "Polygon": [{"X": 0.4592517912387848, "Y": 0.11134342849254608}, {"X": 0.555145263671875, "Y": 0.10871437191963196}, {"X": 0.5575657486915588, "Y": 0.12250088900327682}, {"X": 0.46158602833747864, "Y": 0.12515543401241302}]}, "Id": "4ba166ad-dd56-44aa-88dd-e81e096c5b71"}, {"BlockType": "WORD", "Confidence": 98.02076721191406, "Text": "A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025046776980161667, "Height": 0.011277779005467892, "Left": 0.14128835499286652, "Top": 0.14673924446105957}, "Polygon": [{"X": 0.14128835499286652, "Y": 0.14739376306533813}, {"X": 0.1647575944662094, "Y": 0.14673924446105957}, {"X": 0.16633513569831848, "Y": 0.15735772252082825}, {"X": 0.14284956455230713, "Y": 0.15801702439785004}]}, "Id": "fa4ca7be-6c54-49e1-a04a-f7b9c5392c21"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "Division", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10184773802757263, "Height": 0.01455265935510397, "Left": 0.16859929263591766, "Top": 0.1432303935289383}, "Polygon": [{"X": 0.16859929263591766, "Y": 0.14601871371269226}, {"X": 0.26861923933029175, "Y": 0.1432303935289383}, {"X": 0.2704470455646515, "Y": 0.1549721509218216}, {"X": 0.17035022377967834, "Y": 0.15778304636478424}]}, "Id": "e039a7a9-58ca-41ee-80b9-d7ae4d3285bc"}, {"BlockType": "WORD", "Confidence": 99.44255828857422, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03187190741300583, "Height": 0.012186537496745586, "Left": 0.2731974124908447, "Top": 0.1423957347869873}, "Polygon": [{"X": 0.2731974124908447, "Y": 0.14323435723781586}, {"X": 0.30327701568603516, "Y": 0.1423957347869873}, {"X": 0.30506932735443115, "Y": 0.15373709797859192}, {"X": 0.2749674320220947, "Y": 0.15458227694034576}]}, "Id": "432e9001-780a-49cc-9a4a-2c90ea97c2ac"}, {"BlockType": "WORD", "Confidence": 97.96296691894531, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08856987208127975, "Height": 0.013887193985283375, "Left": 0.30529969930648804, "Top": 0.13964709639549255}, "Polygon": [{"X": 0.30529969930648804, "Y": 0.14206360280513763}, {"X": 0.39199039340019226, "Y": 0.13964709639549255}, {"X": 0.3938695788383484, "Y": 0.15109865367412567}, {"X": 0.3071140646934509, "Y": 0.15353427827358246}]}, "Id": "75307024-0658-41c8-97b0-18f0ffcd500d"}, {"BlockType": "WORD", "Confidence": 96.73310089111328, "Text": "Waste", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07575654238462448, "Height": 0.013308808207511902, "Left": 0.3956282436847687, "Top": 0.13711221516132355}, "Polygon": [{"X": 0.3956282436847687, "Y": 0.13917042315006256}, {"X": 0.4694816470146179, "Y": 0.13711221516132355}, {"X": 0.47138479351997375, "Y": 0.14834684133529663}, {"X": 0.39747729897499084, "Y": 0.15042102336883545}]}, "Id": "3b445e71-7292-458c-9f7b-18a6b9bd743a"}, {"BlockType": "WORD", "Confidence": 99.40369415283203, "Text": "Systems", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09845206141471863, "Height": 0.015924714505672455, "Left": 0.4734123647212982, "Top": 0.13387960195541382}, "Polygon": [{"X": 0.4734123647212982, "Y": 0.13655763864517212}, {"X": 0.5695340633392334, "Y": 0.13387960195541382}, {"X": 0.5718644261360168, "Y": 0.1471017301082611}, {"X": 0.4756599962711334, "Y": 0.14980430901050568}]}, "Id": "aee83ce5-467c-435f-bbe0-ce9882163bbb"}, {"BlockType": "WORD", "Confidence": 89.75845336914062, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04056473821401596, "Height": 0.012020460329949856, "Left": 0.5745952129364014, "Top": 0.13180091977119446}, "Polygon": [{"X": 0.5745952129364014, "Y": 0.13287588953971863}, {"X": 0.6131994724273682, "Y": 0.13180091977119446}, {"X": 0.6151599287986755, "Y": 0.1427382528781891}, {"X": 0.5765281915664673, "Y": 0.1438213735818863}]}, "Id": "3a75439a-4aa2-418f-9203-af8ea8658a03"}, {"BlockType": "WORD", "Confidence": 96.42757415771484, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031224237754940987, "Height": 0.010854489170014858, "Left": 0.14675268530845642, "Top": 0.16048043966293335}, "Polygon": [{"X": 0.14675268530845642, "Y": 0.16131678223609924}, {"X": 0.17648284137248993, "Y": 0.16048043966293335}, {"X": 0.17797692120075226, "Y": 0.17049285769462585}, {"X": 0.14822730422019958, "Y": 0.17133492231369019}]}, "Id": "725d99fa-1ddd-4619-91a8-e6c49b475abd"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "BUNKER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1212654784321785, "Height": 0.015243381261825562, "Left": 0.18219460546970367, "Top": 0.15623995661735535}, "Polygon": [{"X": 0.18219460546970367, "Y": 0.15959718823432922}, {"X": 0.30158913135528564, "Y": 0.15623995661735535}, {"X": 0.30346009135246277, "Y": 0.16809885203838348}, {"X": 0.18397299945354462, "Y": 0.1714833378791809}]}, "Id": "1bb74d59-1c66-44bd-8719-5d55ade8190f"}, {"BlockType": "WORD", "Confidence": 93.69798278808594, "Text": "HILL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06981110572814941, "Height": 0.013158639892935753, "Left": 0.30711013078689575, "Top": 0.15447993576526642}, "Polygon": [{"X": 0.30711013078689575, "Y": 0.156391903758049}, {"X": 0.37509286403656006, "Y": 0.15447993576526642}, {"X": 0.37692123651504517, "Y": 0.165711909532547}, {"X": 0.308888703584671, "Y": 0.16763858497142792}]}, "Id": "95e97d15-864e-4663-a058-558d19852817"}, {"BlockType": "WORD", "Confidence": 99.44255828857422, "Text": "INDUSTRIAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1714351922273636, "Height": 0.01636822335422039, "Left": 0.37857308983802795, "Top": 0.14847104251384735}, "Polygon": [{"X": 0.37857308983802795, "Y": 0.15323233604431152}, {"X": 0.54798823595047, "Y": 0.14847104251384735}, {"X": 0.5500082969665527, "Y": 0.16004012525081635}, {"X": 0.38046541810035706, "Y": 0.1648392677307129}]}, "Id": "c997db90-6154-4311-b334-d44b7687c44b"}, {"BlockType": "WORD", "Confidence": 99.65501403808594, "Text": "PARK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07570680230855942, "Height": 0.013385123573243618, "Left": 0.5530142784118652, "Top": 0.14523006975650787}, "Polygon": [{"X": 0.5530142784118652, "Y": 0.14729927480220795}, {"X": 0.6266871094703674, "Y": 0.14523006975650787}, {"X": 0.6287210583686829, "Y": 0.15652988851070404}, {"X": 0.5549941658973694, "Y": 0.15861518681049347}]}, "Id": "f47ebefd-e2af-45c0-b594-c85aa6bbfd7b"}, {"BlockType": "WORD", "Confidence": 95.78125, "Text": "617-242-7746P", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1885795295238495, "Height": 0.016916809603571892, "Left": 0.14599590003490448, "Top": 0.16949912905693054}, "Polygon": [{"X": 0.14599590003490448, "Y": 0.1747954785823822}, {"X": 0.3327258229255676, "Y": 0.16949912905693054}, {"X": 0.3345754146575928, "Y": 0.18107791244983673}, {"X": 0.14770419895648956, "Y": 0.18641594052314758}]}, "Id": "60ea2c93-8878-40fe-b739-82e26a4db2d0"}, {"BlockType": "WORD", "Confidence": 98.86479187011719, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0949995294213295, "Height": 0.014017988927662373, "Left": 0.34139230847358704, "Top": 0.16651052236557007}, "Polygon": [{"X": 0.34139230847358704, "Y": 0.1691512018442154}, {"X": 0.4344988167285919, "Y": 0.16651052236557007}, {"X": 0.4363918602466583, "Y": 0.17786742746829987}, {"X": 0.3432164788246155, "Y": 0.18052850663661957}]}, "Id": "76cf0fe4-7aa0-4a30-b73c-26273128f54b"}, {"BlockType": "WORD", "Confidence": 94.55974578857422, "Text": "HOUSE617-242-3649F", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2713027894496918, "Height": 0.020337769761681557, "Left": 0.43991559743881226, "Top": 0.1564374566078186}, "Polygon": [{"X": 0.43991559743881226, "Y": 0.16405482590198517}, {"X": 0.7088716626167297, "Y": 0.1564374566078186}, {"X": 0.7112183570861816, "Y": 0.1690918654203415}, {"X": 0.44204095005989075, "Y": 0.17677521705627441}]}, "Id": "04a1d956-197d-4114-aaea-1b794b8f78b7"}, {"BlockType": "WORD", "Confidence": 55.820308685302734, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08272228389978409, "Height": 0.013871748000383377, "Left": 0.15827874839305878, "Top": 0.23693083226680756}, "Polygon": [{"X": 0.15827874839305878, "Y": 0.23931820690631866}, {"X": 0.23925016820430756, "Y": 0.23693083226680756}, {"X": 0.24100103974342346, "Y": 0.24839729070663452}, {"X": 0.15996922552585602, "Y": 0.25080257654190063}]}, "Id": "90fc0e21-9f28-4b9e-9acd-84c082454f1b"}, {"BlockType": "WORD", "Confidence": 97.53507232666016, "Text": "1396602", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10576577484607697, "Height": 0.014012414030730724, "Left": 0.2487459033727646, "Top": 0.23385237157344818}, "Polygon": [{"X": 0.2487459033727646, "Y": 0.2369195967912674}, {"X": 0.35275959968566895, "Y": 0.23385237157344818}, {"X": 0.35451167821884155, "Y": 0.24477557837963104}, {"X": 0.25042420625686646, "Y": 0.24786478281021118}]}, "Id": "4120ab0f-1861-426d-b35a-6c93a9e4ccd4"}, {"BlockType": "WORD", "Confidence": 82.58689880371094, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06356840580701828, "Height": 0.012993303127586842, "Left": 0.1777413934469223, "Top": 0.2542472183704376}, "Polygon": [{"X": 0.1777413934469223, "Y": 0.2560899257659912}, {"X": 0.2396109253168106, "Y": 0.2542472183704376}, {"X": 0.24130980670452118, "Y": 0.2653844952583313}, {"X": 0.17939552664756775, "Y": 0.2672405242919922}]}, "Id": "6fec6ece-9b15-4959-bcda-a1e93a22978c"}, {"BlockType": "WORD", "Confidence": 99.2920913696289, "Text": "9/15/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1263234168291092, "Height": 0.015379266813397408, "Left": 0.24532829225063324, "Top": 0.25007203221321106}, "Polygon": [{"X": 0.24532829225063324, "Y": 0.25377780199050903}, {"X": 0.36977243423461914, "Y": 0.25007203221321106}, {"X": 0.37165170907974243, "Y": 0.26171746850013733}, {"X": 0.24711352586746216, "Y": 0.2654513120651245}]}, "Id": "6645a2d1-207e-4f80-b7a2-6e436779c1dd"}, {"BlockType": "WORD", "Confidence": 89.82681274414062, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07017666846513748, "Height": 0.013569338247179985, "Left": 0.17880752682685852, "Top": 0.2710275650024414}, "Polygon": [{"X": 0.17880752682685852, "Y": 0.27308547496795654}, {"X": 0.24722664058208466, "Y": 0.2710275650024414}, {"X": 0.2489841878414154, "Y": 0.2825237810611725}, {"X": 0.18051405251026154, "Y": 0.28459692001342773}]}, "Id": "8afb5d81-48af-481b-8b9b-0ce7e49b0082"}, {"BlockType": "WORD", "Confidence": 60.451812744140625, "Text": "13:47:04-14:24:30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2344079315662384, "Height": 0.018431780859827995, "Left": 0.25431153178215027, "Top": 0.26361745595932007}, "Polygon": [{"X": 0.25431153178215027, "Y": 0.2706092298030853}, {"X": 0.4867933988571167, "Y": 0.26361745595932007}, {"X": 0.48871946334838867, "Y": 0.27500608563423157}, {"X": 0.2560659945011139, "Y": 0.2820492386817932}]}, "Id": "e4e49a88-75f2-49eb-8c33-fac5d4e090ed"}, {"BlockType": "WORD", "Confidence": 77.07170867919922, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12534713745117188, "Height": 0.015346129424870014, "Left": 0.1928863376379013, "Top": 0.3014412224292755}, "Polygon": [{"X": 0.1928863376379013, "Y": 0.3052263855934143}, {"X": 0.31642013788223267, "Y": 0.3014412224292755}, {"X": 0.318233460187912, "Y": 0.3129745423793793}, {"X": 0.1946074515581131, "Y": 0.31678733229637146}]}, "Id": "8e53716f-c40c-4e3b-9aa8-1aa8abb5dc77"}, {"BlockType": "WORD", "Confidence": 42.38938903808594, "Text": "KO00508/EARTHWATCH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.31825825572013855, "Height": 0.02123808115720749, "Left": 0.3224954903125763, "Top": 0.291144460439682}, "Polygon": [{"X": 0.3224954903125763, "Y": 0.30083081126213074}, {"X": 0.6386982202529907, "Y": 0.291144460439682}, {"X": 0.6407537460327148, "Y": 0.3026253879070282}, {"X": 0.32431653141975403, "Y": 0.3123825490474701}]}, "Id": "d0815a94-369d-4612-8856-b6b67005896b"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "WASTE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09474486857652664, "Height": 0.01352293323725462, "Left": 0.6461043953895569, "Top": 0.28812843561172485}, "Polygon": [{"X": 0.6461043953895569, "Y": 0.29097017645835876}, {"X": 0.738867998123169, "Y": 0.28812843561172485}, {"X": 0.7408492565155029, "Y": 0.29879030585289}, {"X": 0.6480221152305603, "Y": 0.30165135860443115}]}, "Id": "8d0b830f-e099-40ca-9d19-d65f3dacc4c8"}, {"BlockType": "WORD", "Confidence": 99.33613586425781, "Text": "SYSTEM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10754930973052979, "Height": 0.01421110238879919, "Left": 0.7439673542976379, "Top": 0.28414103388786316}, "Polygon": [{"X": 0.7439673542976379, "Y": 0.2873697280883789}, {"X": 0.8493978977203369, "Y": 0.28414103388786316}, {"X": 0.8515166640281677, "Y": 0.2951008677482605}, {"X": 0.7460119724273682, "Y": 0.29835212230682373}]}, "Id": "1bc26092-e1a9-4ebe-8561-130ca030aa84"}, {"BlockType": "WORD", "Confidence": 82.90178680419922, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07923440635204315, "Height": 0.013809697702527046, "Left": 0.21621224284172058, "Top": 0.3175157606601715}, "Polygon": [{"X": 0.21621224284172058, "Y": 0.31990987062454224}, {"X": 0.29367390275001526, "Y": 0.3175157606601715}, {"X": 0.2954466640949249, "Y": 0.32891422510147095}, {"X": 0.217927947640419, "Y": 0.3313254714012146}]}, "Id": "8808ede2-f7eb-4b51-85f5-9e0878e04fa1"}, {"BlockType": "WORD", "Confidence": 99.17091369628906, "Text": "2306", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06329233944416046, "Height": 0.013255130499601364, "Left": 0.30229610204696655, "Top": 0.3150462508201599}, "Polygon": [{"X": 0.30229610204696655, "Y": 0.3169459402561188}, {"X": 0.3637707233428955, "Y": 0.3150462508201599}, {"X": 0.3655884265899658, "Y": 0.3263881802558899}, {"X": 0.30406880378723145, "Y": 0.32830139994621277}]}, "Id": "f0c82231-0cb7-479a-a79d-4eae78eac4d9"}, {"BlockType": "WORD", "Confidence": 62.645687103271484, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09131848812103271, "Height": 0.014456362463533878, "Left": 0.20557381212711334, "Top": 0.3338339924812317}, "Polygon": [{"X": 0.20557381212711334, "Y": 0.33662575483322144}, {"X": 0.2950821816921234, "Y": 0.3338339924812317}, {"X": 0.29689231514930725, "Y": 0.3454783260822296}, {"X": 0.20731663703918457, "Y": 0.348290354013443}]}, "Id": "08efa07f-87df-42d8-8ce9-edab09e5a0a1"}, {"BlockType": "WORD", "Confidence": 99.10574340820312, "Text": "4560", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06297945231199265, "Height": 0.01307589840143919, "Left": 0.3003852963447571, "Top": 0.33196985721588135}, "Polygon": [{"X": 0.3003852963447571, "Y": 0.3338787853717804}, {"X": 0.36158084869384766, "Y": 0.33196985721588135}, {"X": 0.36336472630500793, "Y": 0.34312355518341064}, {"X": 0.30212515592575073, "Y": 0.3450457453727722}]}, "Id": "589438bb-5593-4b70-912b-1e47b32c4da8"}, {"BlockType": "WORD", "Confidence": 59.31381607055664, "Text": "Destination:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1443401277065277, "Height": 0.016108207404613495, "Left": 0.18068742752075195, "Top": 0.3498247563838959}, "Polygon": [{"X": 0.18068742752075195, "Y": 0.35431158542633057}, {"X": 0.3232060670852661, "Y": 0.3498247563838959}, {"X": 0.32502755522727966, "Y": 0.36141401529312134}, {"X": 0.1824023276567459, "Y": 0.36593297123908997}]}, "Id": "c5cb6450-3bfc-42e7-adbb-d80859220128"}, {"BlockType": "WORD", "Confidence": 99.40290832519531, "Text": "A/A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055858515202999115, "Height": 0.01264252606779337, "Left": 0.329498291015625, "Top": 0.34836873412132263}, "Polygon": [{"X": 0.329498291015625, "Y": 0.3500722348690033}, {"X": 0.3835948705673218, "Y": 0.34836873412132263}, {"X": 0.3853568136692047, "Y": 0.3592962324619293}, {"X": 0.33122217655181885, "Y": 0.361011266708374}]}, "Id": "88bdb48e-d891-468e-b305-d5677008d535"}, {"BlockType": "WORD", "Confidence": 68.03791046142578, "Text": "P.O.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06157340854406357, "Height": 0.01314316876232624, "Left": 0.2357623130083084, "Top": 0.3677845299243927}, "Polygon": [{"X": 0.2357623130083084, "Y": 0.3696858882904053}, {"X": 0.295593798160553, "Y": 0.3677845299243927}, {"X": 0.2973357141017914, "Y": 0.3790132999420166}, {"X": 0.23746098577976227, "Y": 0.380927711725235}]}, "Id": "c47e3af3-6369-4841-9f05-1c86ef479912"}, {"BlockType": "WORD", "Confidence": 95.27423095703125, "Text": "EARTH091525-2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21153230965137482, "Height": 0.018423734232783318, "Left": 0.3037741780281067, "Top": 0.36044612526893616}, "Polygon": [{"X": 0.3037741780281067, "Y": 0.3671034872531891}, {"X": 0.5133166909217834, "Y": 0.36044612526893616}, {"X": 0.5153064727783203, "Y": 0.3721645474433899}, {"X": 0.30560606718063354, "Y": 0.3788698613643646}]}, "Id": "59497d9f-44f5-4f9f-a83d-8f08e8c9799a"}, {"BlockType": "WORD", "Confidence": 58.91960144042969, "Text": "D.O:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06169573590159416, "Height": 0.013218104839324951, "Left": 0.23949739336967468, "Top": 0.3841899335384369}, "Polygon": [{"X": 0.23949739336967468, "Y": 0.3861120045185089}, {"X": 0.2994418144226074, "Y": 0.3841899335384369}, {"X": 0.30119311809539795, "Y": 0.39547279477119446}, {"X": 0.24120520055294037, "Y": 0.39740803837776184}]}, "Id": "8b7d4a4c-fe17-4c07-9547-fd3523060b84"}, {"BlockType": "WORD", "Confidence": 92.92829132080078, "Text": "2522611", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10306835174560547, "Height": 0.014561040326952934, "Left": 0.3100411593914032, "Top": 0.38047000765800476}, "Polygon": [{"X": 0.3100411593914032, "Y": 0.3837156593799591}, {"X": 0.4112716615200043, "Y": 0.38047000765800476}, {"X": 0.41310951113700867, "Y": 0.3917630910873413}, {"X": 0.3118056058883667, "Y": 0.39503103494644165}]}, "Id": "3dd3e851-c9df-436c-9a8c-adbf546d85bf"}, {"BlockType": "WORD", "Confidence": 92.90816497802734, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0617486871778965, "Height": 0.013371455483138561, "Left": 0.2394634187221527, "Top": 0.4006705582141876}, "Polygon": [{"X": 0.2394634187221527, "Y": 0.4026108384132385}, {"X": 0.2994418144226074, "Y": 0.4006705582141876}, {"X": 0.3012121021747589, "Y": 0.41208839416503906}, {"X": 0.24118973314762115, "Y": 0.41404202580451965}]}, "Id": "eac7bf93-864f-47c0-94e5-cbd4f77825a2"}, {"BlockType": "WORD", "Confidence": 99.4190444946289, "Text": "531833", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09237903356552124, "Height": 0.014489059336483479, "Left": 0.3051214814186096, "Top": 0.39722904562950134}, "Polygon": [{"X": 0.3051214814186096, "Y": 0.4001566767692566}, {"X": 0.3956364691257477, "Y": 0.39722904562950134}, {"X": 0.39750051498413086, "Y": 0.4087701141834259}, {"X": 0.30691850185394287, "Y": 0.41171810030937195}]}, "Id": "8f21c430-de7a-485a-a3c2-1079dc7fb8bd"}, {"BlockType": "WORD", "Confidence": 96.09455108642578, "Text": "Comment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1277463138103485, "Height": 0.01563938893377781, "Left": 0.2231389731168747, "Top": 0.4322647452354431}, "Polygon": [{"X": 0.2231389731168747, "Y": 0.43641114234924316}, {"X": 0.34907349944114685, "Y": 0.4322647452354431}, {"X": 0.350885272026062, "Y": 0.4437295198440552}, {"X": 0.22485817968845367, "Y": 0.44790413975715637}]}, "Id": "fbd699f9-b084-441a-b34e-994a10c1a0be"}, {"BlockType": "WORD", "Confidence": 97.88822937011719, "Text": "NAVIGATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17730765044689178, "Height": 0.0173383466899395, "Left": 0.354952335357666, "Top": 0.4257912039756775}, "Polygon": [{"X": 0.354952335357666, "Y": 0.4315629303455353}, {"X": 0.5302981734275818, "Y": 0.4257912039756775}, {"X": 0.532260000705719, "Y": 0.43731823563575745}, {"X": 0.356784850358963, "Y": 0.4431295692920685}]}, "Id": "5b61ad3a-bf5a-4315-80ce-68d402bdd2fc"}, {"BlockType": "WORD", "Confidence": 96.89572143554688, "Text": "GLOBAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1218789741396904, "Height": 0.015267166309058666, "Left": 0.5360425710678101, "Top": 0.42118173837661743}, "Polygon": [{"X": 0.5360425710678101, "Y": 0.4251261055469513}, {"X": 0.655903697013855, "Y": 0.42118173837661743}, {"X": 0.657921552658081, "Y": 0.4324779510498047}, {"X": 0.5379741191864014, "Y": 0.4364489018917084}]}, "Id": "4d7b5ff4-40b6-43cf-9b2c-e33f95620475"}, {"BlockType": "WORD", "Confidence": 72.9942626953125, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05941084027290344, "Height": 0.012417228892445564, "Left": 0.6610655188560486, "Top": 0.41908401250839233}, "Polygon": [{"X": 0.6610655188560486, "Y": 0.42097580432891846}, {"X": 0.7185540199279785, "Y": 0.41908401250839233}, {"X": 0.7204763293266296, "Y": 0.42959755659103394}, {"X": 0.6629493236541748, "Y": 0.43150123953819275}]}, "Id": "437caf8a-ba3e-420e-b9f5-2d84dc3d8e42"}, {"BlockType": "WORD", "Confidence": 54.03280258178711, "Text": "Grown", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08229470252990723, "Height": 0.014208143576979637, "Left": 0.208110049366951, "Top": 0.49560973048210144}, "Polygon": [{"X": 0.208110049366951, "Y": 0.4983469843864441}, {"X": 0.28864845633506775, "Y": 0.49560973048210144}, {"X": 0.2904047667980194, "Y": 0.507062554359436}, {"X": 0.20980745553970337, "Y": 0.509817898273468}]}, "Id": "5b5af999-427e-4ed6-8176-9ccfd98d9f19"}, {"BlockType": "WORD", "Confidence": 99.65660858154297, "Text": "75200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07898706942796707, "Height": 0.013697287999093533, "Left": 0.30797234177589417, "Top": 0.49243712425231934}, "Polygon": [{"X": 0.30797234177589417, "Y": 0.4950617551803589}, {"X": 0.3851926922798157, "Y": 0.49243712425231934}, {"X": 0.38695940375328064, "Y": 0.5034930109977722}, {"X": 0.3096846342086792, "Y": 0.5061343908309937}]}, "Id": "f3141559-22d9-42f3-86d9-16e7cdf8c06d"}, {"BlockType": "WORD", "Confidence": 50.181758880615234, "Text": "L Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07105936855077744, "Height": 0.013557798229157925, "Left": 0.392768919467926, "Top": 0.48978400230407715}, "Polygon": [{"X": 0.392768919467926, "Y": 0.4921364486217499}, {"X": 0.46198269724845886, "Y": 0.48978400230407715}, {"X": 0.46382829546928406, "Y": 0.5009741187095642}, {"X": 0.394565224647522, "Y": 0.503341794013977}]}, "Id": "d7956d43-be18-47af-a10f-84b8313f10e5"}, {"BlockType": "WORD", "Confidence": 99.52069091796875, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06748618930578232, "Height": 0.01342771202325821, "Left": 0.46694329380989075, "Top": 0.4868394136428833}, "Polygon": [{"X": 0.46694329380989075, "Y": 0.48906803131103516}, {"X": 0.5325319766998291, "Y": 0.4868394136428833}, {"X": 0.5344294905662537, "Y": 0.4980240762233734}, {"X": 0.4687941372394562, "Y": 0.5002671480178833}]}, "Id": "c031ca2d-e569-4779-965f-eb8ade890134"}, {"BlockType": "WORD", "Confidence": 94.62850952148438, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01633809693157673, "Height": 0.011165633797645569, "Left": 0.5384024381637573, "Top": 0.48639386892318726}, "Polygon": [{"X": 0.5384024381637573, "Y": 0.4868870675563812}, {"X": 0.5529159307479858, "Y": 0.48639386892318726}, {"X": 0.5547405481338501, "Y": 0.4970632493495941}, {"X": 0.5402172207832336, "Y": 0.49755948781967163}]}, "Id": "ab80a64b-e2f9-446c-964c-39810d1da37b"}, {"BlockType": "WORD", "Confidence": 63.180206298828125, "Text": "Tare:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06664808094501495, "Height": 0.013774567283689976, "Left": 0.21772494912147522, "Top": 0.5132595300674438}, "Polygon": [{"X": 0.21772494912147522, "Y": 0.5154843926429749}, {"X": 0.28261080384254456, "Y": 0.5132595300674438}, {"X": 0.28437304496765137, "Y": 0.5247945189476013}, {"X": 0.21943944692611694, "Y": 0.5270340442657471}]}, "Id": "75a614dd-2a43-47fd-bdf8-22536817a002"}, {"BlockType": "WORD", "Confidence": 98.83988189697266, "Text": "32100", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07796308398246765, "Height": 0.013771298341453075, "Left": 0.303107887506485, "Top": 0.5097447633743286}, "Polygon": [{"X": 0.303107887506485, "Y": 0.5123569369316101}, {"X": 0.37929680943489075, "Y": 0.5097447633743286}, {"X": 0.38107097148895264, "Y": 0.5208872556686401}, {"X": 0.3048279881477356, "Y": 0.523516058921814}]}, "Id": "3da7c3d5-9e4d-4287-9388-854d8a66e2f9"}, {"BlockType": "WORD", "Confidence": 51.10630416870117, "Text": "LIn", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05593757703900337, "Height": 0.013668395578861237, "Left": 0.38545721769332886, "Top": 0.5068861246109009}, "Polygon": [{"X": 0.38545721769332886, "Y": 0.5087371468544006}, {"X": 0.4394676685333252, "Y": 0.5068861246109009}, {"X": 0.4413948059082031, "Y": 0.5186909437179565}, {"X": 0.38734379410743713, "Y": 0.5205544829368591}]}, "Id": "978b244e-8d1b-40f3-8066-e3e60174d483"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "Scale", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06845995783805847, "Height": 0.013434353284537792, "Left": 0.448285311460495, "Top": 0.504703164100647}, "Polygon": [{"X": 0.448285311460495, "Y": 0.5069856643676758}, {"X": 0.5148711204528809, "Y": 0.504703164100647}, {"X": 0.5167452692985535, "Y": 0.515840470790863}, {"X": 0.45011237263679504, "Y": 0.518137514591217}]}, "Id": "0e9871c8-71d7-4b9e-ba5f-d611d524d00c"}, {"BlockType": "WORD", "Confidence": 78.1164321899414, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016888819634914398, "Height": 0.011017290875315666, "Left": 0.5202091336250305, "Top": 0.5040394067764282}, "Polygon": [{"X": 0.5202091336250305, "Y": 0.5045573115348816}, {"X": 0.5353173017501831, "Y": 0.5040394067764282}, {"X": 0.5370979309082031, "Y": 0.514535665512085}, {"X": 0.5219796895980835, "Y": 0.5150567293167114}]}, "Id": "5a7d9358-2804-484b-a5ec-2cbd52afcbd8"}, {"BlockType": "WORD", "Confidence": 65.01435089111328, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05585712194442749, "Height": 0.01337731909006834, "Left": 0.2285580188035965, "Top": 0.5301346778869629}, "Polygon": [{"X": 0.2285580188035965, "Y": 0.5320056080818176}, {"X": 0.2826610803604126, "Y": 0.5301346778869629}, {"X": 0.2844151556491852, "Y": 0.5416288375854492}, {"X": 0.23027247190475464, "Y": 0.5435119867324829}]}, "Id": "e8372443-7c18-4f99-98e3-94e624c6c38c"}, {"BlockType": "WORD", "Confidence": 50.76789855957031, "Text": "431001.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10426557809114456, "Height": 0.014993388205766678, "Left": 0.30449119210243225, "Top": 0.5254641771316528}, "Polygon": [{"X": 0.30449119210243225, "Y": 0.5290055274963379}, {"X": 0.4069179594516754, "Y": 0.5254641771316528}, {"X": 0.4087567627429962, "Y": 0.5368931889533997}, {"X": 0.3062555193901062, "Y": 0.540457546710968}]}, "Id": "e20587ff-1f3a-4420-badb-8bbb0062bba6"}, {"BlockType": "WORD", "Confidence": 51.56150436401367, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06690941005945206, "Height": 0.013751623220741749, "Left": 0.22432155907154083, "Top": 0.5563520789146423}, "Polygon": [{"X": 0.22432155907154083, "Y": 0.5586352348327637}, {"X": 0.28948092460632324, "Y": 0.5563520789146423}, {"X": 0.2912309765815735, "Y": 0.5678059458732605}, {"X": 0.22602413594722748, "Y": 0.5701037049293518}]}, "Id": "be35c171-ae8b-4f8e-a5c0-68a7710fb3c9"}, {"BlockType": "WORD", "Confidence": 99.00909423828125, "Text": "21.55", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.071373850107193, "Height": 0.013863898813724518, "Left": 0.29695287346839905, "Top": 0.5532279014587402}, "Polygon": [{"X": 0.29695287346839905, "Y": 0.555665135383606}, {"X": 0.36652469635009766, "Y": 0.5532279014587402}, {"X": 0.36832672357559204, "Y": 0.5646389722824097}, {"X": 0.29870450496673584, "Y": 0.5670918226242065}]}, "Id": "c86d3e64-5d37-4eac-982a-409749671d54"}, {"BlockType": "WORD", "Confidence": 99.6534194946289, "Text": "Materials", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11799555271863937, "Height": 0.016482101753354073, "Left": 0.21424712240695953, "Top": 0.5898289084434509}, "Polygon": [{"X": 0.21424712240695953, "Y": 0.5939666032791138}, {"X": 0.33033180236816406, "Y": 0.5898289084434509}, {"X": 0.3322426676750183, "Y": 0.6021451354026794}, {"X": 0.21606725454330444, "Y": 0.606311023235321}]}, "Id": "d85bce70-527e-4f27-94fc-3c8a91049139"}, {"BlockType": "WORD", "Confidence": 99.4140625, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02542578987777233, "Height": 0.011978906579315662, "Left": 0.3358660042285919, "Top": 0.5889912247657776}, "Polygon": [{"X": 0.3358660042285919, "Y": 0.5898352265357971}, {"X": 0.35954347252845764, "Y": 0.5889912247657776}, {"X": 0.3612917959690094, "Y": 0.6001209020614624}, {"X": 0.33759766817092896, "Y": 0.6009700894355774}]}, "Id": "c861a469-bf2d-409a-bf3a-9cb8c4d2f7d8"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Services", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10550336539745331, "Height": 0.015036956407129765, "Left": 0.3662116825580597, "Top": 0.5846008658409119}, "Polygon": [{"X": 0.3662116825580597, "Y": 0.5882946252822876}, {"X": 0.46985381841659546, "Y": 0.5846008658409119}, {"X": 0.4717150330543518, "Y": 0.5959209203720093}, {"X": 0.3679986298084259, "Y": 0.5996378064155579}]}, "Id": "c5999773-d2ef-481d-bb47-818306dc2a15"}, {"BlockType": "WORD", "Confidence": 89.32896423339844, "Text": "Origin:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08661395311355591, "Height": 0.0164843387901783, "Left": 0.2568988502025604, "Top": 0.6350542306900024}, "Polygon": [{"X": 0.2568988502025604, "Y": 0.6381341218948364}, {"X": 0.34143298864364624, "Y": 0.6350542306900024}, {"X": 0.34351280331611633, "Y": 0.6484363079071045}, {"X": 0.25890716910362244, "Y": 0.6515385508537292}]}, "Id": "398491a0-82f1-4a9e-80bf-e6e0428c8039"}, {"BlockType": "WORD", "Confidence": 98.07537078857422, "Text": "NA/NON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11851166933774948, "Height": 0.016347933560609818, "Left": 0.3492322564125061, "Top": 0.6292621493339539}, "Polygon": [{"X": 0.3492322564125061, "Y": 0.6335054636001587}, {"X": 0.46576738357543945, "Y": 0.6292621493339539}, {"X": 0.4677439332008362, "Y": 0.6413389444351196}, {"X": 0.3511199653148651, "Y": 0.6456100940704346}]}, "Id": "204ff56b-d769-452c-8209-53a3554ec08b"}, {"BlockType": "WORD", "Confidence": 99.3359375, "Text": "APPLICABLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.187995046377182, "Height": 0.01943780481815338, "Left": 0.4703921973705292, "Top": 0.621539831161499}, "Polygon": [{"X": 0.4703921973705292, "Y": 0.6283015012741089}, {"X": 0.6561607122421265, "Y": 0.621539831161499}, {"X": 0.6583872437477112, "Y": 0.6341693997383118}, {"X": 0.47247084975242615, "Y": 0.6409776210784912}]}, "Id": "b50daa7b-411d-457e-af7f-fbb265116337"}, {"BlockType": "WORD", "Confidence": 86.48835754394531, "Text": "Material", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11392860859632492, "Height": 0.016433095559477806, "Left": 0.24580064415931702, "Top": 0.651841938495636}, "Polygon": [{"X": 0.24580064415931702, "Y": 0.6559565663337708}, {"X": 0.35780784487724304, "Y": 0.651841938495636}, {"X": 0.35972926020622253, "Y": 0.6641331911087036}, {"X": 0.2476350963115692, "Y": 0.668274998664856}]}, "Id": "f24ca1f7-bfaa-4407-9524-6fb16eef61b4"}, {"BlockType": "WORD", "Confidence": 90.0223159790039, "Text": "539-1/OBGN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16601786017417908, "Height": 0.018182002007961273, "Left": 0.3626737892627716, "Top": 0.6449037194252014}, "Polygon": [{"X": 0.3626737892627716, "Y": 0.6509257555007935}, {"X": 0.5266613960266113, "Y": 0.6449037194252014}, {"X": 0.5286916494369507, "Y": 0.6570242643356323}, {"X": 0.3645786941051483, "Y": 0.6630856990814209}]}, "Id": "a5bfd307-504b-4b98-91ed-6e2bc166c21c"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "MIX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060719575732946396, "Height": 0.014276562258601189, "Left": 0.5343002676963806, "Top": 0.6416712999343872}, "Polygon": [{"X": 0.5343002676963806, "Y": 0.6438238024711609}, {"X": 0.5929381251335144, "Y": 0.6416712999343872}, {"X": 0.5950198173522949, "Y": 0.6537812352180481}, {"X": 0.5363373160362244, "Y": 0.6559478640556335}]}, "Id": "2ab841cf-e490-48ce-84fc-bdd66c77e8c2"}, {"BlockType": "WORD", "Confidence": 98.56326293945312, "Text": "COMMODITIES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20943519473075867, "Height": 0.02005673386156559, "Left": 0.5973566770553589, "Top": 0.6329676508903503}, "Polygon": [{"X": 0.5973566770553589, "Y": 0.6405676603317261}, {"X": 0.8044853806495667, "Y": 0.6329676508903503}, {"X": 0.8067919015884399, "Y": 0.6453731656074524}, {"X": 0.5995017886161804, "Y": 0.6530243754386902}]}, "Id": "71dd6052-be8c-45be-9c67-98e25d654b93"}, {"BlockType": "WORD", "Confidence": 51.5484504699707, "Text": "OL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04318644106388092, "Height": 0.013036574237048626, "Left": 0.8097451329231262, "Top": 0.631058931350708}, "Polygon": [{"X": 0.8097451329231262, "Y": 0.6325634717941284}, {"X": 0.850753903388977, "Y": 0.631058931350708}, {"X": 0.8529316186904907, "Y": 0.6425815224647522}, {"X": 0.8118932843208313, "Y": 0.6440954804420471}]}, "Id": "7b2e8e08-b676-412e-8575-caa174993ea6"}, {"BlockType": "WORD", "Confidence": 97.65067291259766, "Text": "Quantity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11790814995765686, "Height": 0.018738452345132828, "Left": 0.24658331274986267, "Top": 0.6697153449058533}, "Polygon": [{"X": 0.24658331274986267, "Y": 0.6740000247955322}, {"X": 0.3622356057167053, "Y": 0.6697153449058533}, {"X": 0.36449146270751953, "Y": 0.6841360926628113}, {"X": 0.24873395264148712, "Y": 0.6884537935256958}]}, "Id": "fb4056a1-2631-4aaf-99f6-55e83d1376f7"}, {"BlockType": "WORD", "Confidence": 97.41171264648438, "Text": "21.55", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07147468626499176, "Height": 0.014687524177134037, "Left": 0.36879417300224304, "Top": 0.6663609743118286}, "Polygon": [{"X": 0.36879417300224304, "Y": 0.6689359545707703}, {"X": 0.43831583857536316, "Y": 0.6663609743118286}, {"X": 0.440268874168396, "Y": 0.6784568428993225}, {"X": 0.37069427967071533, "Y": 0.6810485124588013}]}, "Id": "72df38ba-cc76-43de-a71e-1aaea4fbab09"}, {"BlockType": "WORD", "Confidence": 88.4803466796875, "Text": "Ton", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04975099861621857, "Height": 0.013295863755047321, "Left": 0.44456931948661804, "Top": 0.6643930673599243}, "Polygon": [{"X": 0.44456931948661804, "Y": 0.6661654710769653}, {"X": 0.4924202859401703, "Y": 0.6643930673599243}, {"X": 0.4943203032016754, "Y": 0.6759056448936462}, {"X": 0.4464346766471863, "Y": 0.6776889562606812}]}, "Id": "21059a1d-acdd-4d8c-a8f3-c69701c3e94d"}, {"BlockType": "WORD", "Confidence": 78.8646011352539, "Text": "<PERSON>gh<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16837313771247864, "Height": 0.021060563623905182, "Left": 0.23888979852199554, "Top": 0.7832027077674866}, "Polygon": [{"X": 0.23888979852199554, "Y": 0.7896869778633118}, {"X": 0.4049665927886963, "Y": 0.7832027077674866}, {"X": 0.407262921333313, "Y": 0.7977309226989746}, {"X": 0.24103499948978424, "Y": 0.8042632341384888}]}, "Id": "d126aacb-a738-44c1-ba5d-52186fb4404d"}, {"BlockType": "WORD", "Confidence": 95.60726165771484, "Text": "TOM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07196306437253952, "Height": 0.014977161772549152, "Left": 0.41114702820777893, "Top": 0.779807448387146}, "Polygon": [{"X": 0.41114702820777893, "Y": 0.7825388312339783}, {"X": 0.48111608624458313, "Y": 0.779807448387146}, {"X": 0.48311010003089905, "Y": 0.792036235332489}, {"X": 0.4130876362323761, "Y": 0.7947846055030823}]}, "Id": "084aaf4b-b0a7-40a5-bab7-11f251ebf28f"}, {"BlockType": "WORD", "Confidence": 58.2774658203125, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08790715038776398, "Height": 0.015748606994748116, "Left": 0.2811667323112488, "Top": 0.8125483989715576}, "Polygon": [{"X": 0.2811667323112488, "Y": 0.815947413444519}, {"X": 0.36715930700302124, "Y": 0.8125483989715576}, {"X": 0.36907389760017395, "Y": 0.8248768448829651}, {"X": 0.28301510214805603, "Y": 0.828296959400177}]}, "Id": "ae65d31d-d90b-4478-b040-71531d83c6b6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04357802867889404, "Height": 0.01829151064157486, "Left": 0.25046366453170776, "Top": 0.8656070828437805}, "Polygon": [{"X": 0.25046366453170776, "Y": 0.8672667741775513}, {"X": 0.29155126214027405, "Y": 0.8656070828437805}, {"X": 0.2940416932106018, "Y": 0.8822253346443176}, {"X": 0.2529115676879883, "Y": 0.8838986158370972}]}, "Id": "2427e33a-1455-47b3-a350-66b5146bf4ed"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "signing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11729420721530914, "Height": 0.022271551191806793, "Left": 0.29770544171333313, "Top": 0.8598222732543945}, "Polygon": [{"X": 0.29770544171333313, "Y": 0.864446222782135}, {"X": 0.4122222065925598, "Y": 0.8598222732543945}, {"X": 0.41499966382980347, "Y": 0.8774296045303345}, {"X": 0.3003573715686798, "Y": 0.8820938467979431}]}, "Id": "d56805e1-3790-42a1-8b0a-4adfd70c2d52"}, {"BlockType": "WORD", "Confidence": 98.4755630493164, "Text": "above,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11079635471105576, "Height": 0.020462188869714737, "Left": 0.41912880539894104, "Top": 0.8550707697868347}, "Polygon": [{"X": 0.41912880539894104, "Y": 0.859437108039856}, {"X": 0.5272709131240845, "Y": 0.8550707697868347}, {"X": 0.5299251675605774, "Y": 0.8711318373680115}, {"X": 0.4216751158237457, "Y": 0.8755329847335815}]}, "Id": "63643da3-ea8f-4e30-a1cf-e1cd0bf3dbcc"}, {"BlockType": "WORD", "Confidence": 64.44973754882812, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01191954966634512, "Height": 0.013842889107763767, "Left": 0.536983072757721, "Top": 0.8547049164772034}, "Polygon": [{"X": 0.536983072757721, "Y": 0.8550958633422852}, {"X": 0.5466629862785339, "Y": 0.8547049164772034}, {"X": 0.5489026308059692, "Y": 0.8681542873382568}, {"X": 0.5392146110534668, "Y": 0.8685478568077087}]}, "Id": "85b121f7-9810-4b28-80a0-c3170df1b67b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "declare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12130101025104523, "Height": 0.01920943148434162, "Left": 0.5542197823524475, "Top": 0.8486906886100769}, "Polygon": [{"X": 0.5542197823524475, "Y": 0.8534858226776123}, {"X": 0.6730073690414429, "Y": 0.8486906886100769}, {"X": 0.6755208373069763, "Y": 0.8630706667900085}, {"X": 0.5566273331642151, "Y": 0.8679001331329346}]}, "Id": "1150fe3f-e022-476a-8b54-da01669182e1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06914009898900986, "Height": 0.017160799354314804, "Left": 0.6798560619354248, "Top": 0.8453179001808167}, "Polygon": [{"X": 0.6798560619354248, "Y": 0.848003625869751}, {"X": 0.7464000582695007, "Y": 0.8453179001808167}, {"X": 0.7489961981773376, "Y": 0.8597736358642578}, {"X": 0.6823926568031311, "Y": 0.8624786734580994}]}, "Id": "0afbcb14-9db1-492d-8fb1-12114d1568e2"}, {"BlockType": "WORD", "Confidence": 71.91326904296875, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012406759895384312, "Height": 0.013585412874817848, "Left": 0.7543941140174866, "Top": 0.8448857665061951}, "Polygon": [{"X": 0.7543941140174866, "Y": 0.8452904224395752}, {"X": 0.7644187211990356, "Y": 0.8448857665061951}, {"X": 0.7668008804321289, "Y": 0.8580638766288757}, {"X": 0.7567681074142456, "Y": 0.8584712147712708}]}, "Id": "a1510bc2-15f2-4e2b-8362-f05603b1a9d6"}, {"BlockType": "WORD", "Confidence": 99.80229949951172, "Text": "did", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0528816282749176, "Height": 0.016202881932258606, "Left": 0.7725808620452881, "Top": 0.8418653011322021}, "Polygon": [{"X": 0.7725808620452881, "Y": 0.843893826007843}, {"X": 0.8228486180305481, "Y": 0.8418653011322021}, {"X": 0.8254624605178833, "Y": 0.8560253977775574}, {"X": 0.7751507759094238, "Y": 0.8580681681632996}]}, "Id": "69064303-1b54-4a1a-9c0d-39593ca87461"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "NOT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07389303296804428, "Height": 0.017119886353611946, "Left": 0.8318657875061035, "Top": 0.83858722448349}, "Polygon": [{"X": 0.8318657875061035, "Y": 0.8414600491523743}, {"X": 0.9030579328536987, "Y": 0.83858722448349}, {"X": 0.905758798122406, "Y": 0.8528138995170593}, {"X": 0.8345041275024414, "Y": 0.8557071089744568}]}, "Id": "a1ff8813-cd59-4154-b925-f8528c2c123c"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "deposit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1228351891040802, "Height": 0.022239360958337784, "Left": 0.2520197927951813, "Top": 0.8825461268424988}, "Polygon": [{"X": 0.2520197927951813, "Y": 0.8874416947364807}, {"X": 0.37217456102371216, "Y": 0.8825461268424988}, {"X": 0.3748549818992615, "Y": 0.8998483419418335}, {"X": 0.2545709013938904, "Y": 0.9047855138778687}]}, "Id": "ed6b815b-3563-46b6-b084-7c32c7a608a5"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062489815056324005, "Height": 0.016561657190322876, "Left": 0.3790363371372223, "Top": 0.8827505707740784}, "Polygon": [{"X": 0.3790363371372223, "Y": 0.8852083086967468}, {"X": 0.4392825961112976, "Y": 0.8827505707740784}, {"X": 0.4415261447429657, "Y": 0.8968375325202942}, {"X": 0.38122716546058655, "Y": 0.8993122577667236}]}, "Id": "aa94fc63-506e-43f4-8d21-f4fbb2efd3be"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "PROHIBITED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19986721873283386, "Height": 0.022507278248667717, "Left": 0.4460555613040924, "Top": 0.8710096478462219}, "Polygon": [{"X": 0.4460555613040924, "Y": 0.8790499567985535}, {"X": 0.6434354186058044, "Y": 0.8710096478462219}, {"X": 0.6459227800369263, "Y": 0.8854194283485413}, {"X": 0.44836652278900146, "Y": 0.8935168981552124}]}, "Id": "b9b7fc14-2e17-41d9-8e30-ab1cf50e841a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WASTES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1370316445827484, "Height": 0.020178917795419693, "Left": 0.649935781955719, "Top": 0.8647798299789429}, "Polygon": [{"X": 0.649935781955719, "Y": 0.8702520728111267}, {"X": 0.7843006253242493, "Y": 0.8647798299789429}, {"X": 0.786967396736145, "Y": 0.8794468641281128}, {"X": 0.6524807214736938, "Y": 0.8849587440490723}]}, "Id": "80310bd4-f177-4633-938f-5632c9e53b40"}, {"BlockType": "WORD", "Confidence": 97.85255432128906, "Text": "CONSIGNEE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1556169092655182, "Height": 0.017705246806144714, "Left": 0.2685093879699707, "Top": 0.9698329567909241}, "Polygon": [{"X": 0.2685093879699707, "Y": 0.9763402938842773}, {"X": 0.4223705232143402, "Y": 0.9698329567909241}, {"X": 0.4241262972354889, "Y": 0.980996310710907}, {"X": 0.27015894651412964, "Y": 0.98753821849823}]}, "Id": "8536f099-d1bc-472a-901d-bea5c7ef5451"}, {"BlockType": "WORD", "Confidence": 95.44861602783203, "Text": "PRINT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07885944843292236, "Height": 0.012941129505634308, "Left": 0.42910462617874146, "Top": 0.9681535959243774}, "Polygon": [{"X": 0.42910462617874146, "Y": 0.97142493724823}, {"X": 0.5063931345939636, "Y": 0.9681535959243774}, {"X": 0.5079640746116638, "Y": 0.9778083562850952}, {"X": 0.43062952160835266, "Y": 0.9810947179794312}]}, "Id": "862f5f45-e6b8-4008-a963-03f364a28350"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "NAME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07622984796762466, "Height": 0.0130934938788414, "Left": 0.5120581388473511, "Top": 0.9658735394477844}, "Polygon": [{"X": 0.5120581388473511, "Y": 0.9690309762954712}, {"X": 0.5866222381591797, "Y": 0.9658735394477844}, {"X": 0.5882880091667175, "Y": 0.9757946729660034}, {"X": 0.5136783719062805, "Y": 0.978967010974884}]}, "Id": "4f78a2bd-4632-4051-98a5-8303b24b66b8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "7f6338fb-8859-4cfc-b1cb-b53acaaf02b1", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "7f6338fb-8859-4cfc-b1cb-b53acaaf02b1", "content-type": "application/x-amz-json-1.1", "content-length": "65044", "date": "Wed, 24 Sep 2025 12:46:08 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ee7afefd-52a7-47a7-9fcf-8c3dcc578cc5", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:11 GMT", "content-type": "application/json", "content-length": "1011", "connection": "keep-alive", "x-amzn-requestid": "ee7afefd-52a7-47a7-9fcf-8c3dcc578cc5"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to classify. Let's examine text.\n\nPage shows \"Ticket 1396602\", \"Scale House\", \"Ticket\", date/time, customer, truck, trailer, destination, P.O., <PERSON><PERSON>O, seal, comment. Then \"Grown 75200 L Out Scale 3\", \"Tare: 32100 LIn Scale 3\", \"Net 431001.\" Total 21.55. Materials & Services. Origin, Material, Quantity: 21.55 Ton. Weighmaster TOM. This looks like a Scale Ticket: weight record from a scale for vehicle/load. Keywords: Gross, Tare, Net, Weighed At, Ticket no, weight. Yes. So doc_type: scale_ticket.\n\nWe must output via tool call classify_logistics_doc_type with documents array: one object: page_no 1, doc_type \"scale_ticket\".\n\nLet's do that."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2764, "outputTokens": 204, "totalTokens": 2968}, "metrics": {"latencyMs": 1138}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/9d87cf31_KBFTEJIYFMKC7ZILXM5M.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n*1396602*\nCasella Recycling of New England\nA Division of Casella Waste Systems Inc\n14 BUNKER HILL INDUSTRIAL PARK\n617-242-7746P SCALE HOUSE617-242-3649F\nTicket 1396602\nDate: 9/15/2025\nTime 13:47:04-14:24:30\nCustomer KO00508/EARTHWATCH WASTE SYSTEM\nTruck 2306\nTrailer 4560\nDestination: A/A\nP.O.: EARTH091525-2\nD.O: 2522611\nSeal: 531833\nComment NAVIGATION GLOBAL LLC\nGrown 75200 L Out Scale 3\nTare: 32100 LIn Scale 3\nNet 431001.\nTotal 21.55\nMaterials & Services\nOrigin: NA/NON APPLICABLE\nMaterial 539-1/OBGN MIX COMMODITIES OL\nQuantity: 21.55 Ton\nWeighmaster TOM\nDriver\nBy signing above, I declare that I did NOT\ndeposit any PROHIBITED WASTES\nCONSIGNEE PRINT NAME\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}