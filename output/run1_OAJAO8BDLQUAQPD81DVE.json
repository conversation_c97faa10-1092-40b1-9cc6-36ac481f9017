{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.454081654548645, "Height": 0.7581125497817993, "Left": 0.25131797790527344, "Top": 0.13454341888427734}, "Polygon": [{"X": 0.25131797790527344, "Y": 0.15017805993556976}, {"X": 0.690207302570343, "Y": 0.13454341888427734}, {"X": 0.7053996324539185, "Y": 0.8770462870597839}, {"X": 0.2865610122680664, "Y": 0.8926559686660767}]}, "Id": "f6670f87-030c-4a23-88c1-94c40e969514", "Relationships": [{"Type": "CHILD", "Ids": ["1545fd7b-e8fe-46a6-bae2-9529533682ac", "708aa8d1-3afc-4ac4-81b5-ba678824b80c", "e3e84452-3f44-4aa7-b120-b9f546db24c0", "31c752f5-186a-46b3-b44b-1e392d30da23", "beff333d-13c6-4583-9fdb-33e24f02ee6d", "2b87f818-b3e7-4a49-86b8-a2a27258973e", "b2981fe4-5e8e-4f16-a2a3-c202ffab5a9f", "cabb74c4-ae97-4a81-9e44-9f2ed3151309", "a519f47b-80e9-4ee9-99dc-aa7669d5593b", "f6864572-848a-4cb0-bcb7-d8f362da0bcc", "aefd88d6-77f8-4422-bafa-33a9a47d1d8a", "2eba54cf-fce0-4512-b74a-cf0443d598eb", "07adec34-4577-4d04-98a3-b7e67b3aa636", "fe20f90a-aca5-4662-af98-15ff84a7ebf6", "a135bf3a-aef1-42ea-a48f-cece29d4b4e9", "16069fb4-81b1-4335-ab54-b51b94339977", "bc94196b-0970-402b-87cb-17c79e5e6968", "17476ba8-d053-47dc-af8e-77f37e803d04", "425639da-1712-45f2-be06-70f1301e8f4e", "21eea2b1-ab16-455c-bf88-895941924272", "da7d95c5-26c3-4daa-943a-dfc46357d511", "bb98b5cc-54c2-44af-b0bf-c7ab6bf14d1b", "e9c7c8a9-784e-42e2-b154-3721a9e9b595", "93feb92d-f0d9-4c98-8672-4c7487e5e6f9", "47a4e25a-ccca-4846-be7d-5ec028865be4", "c17a29b3-a544-4679-8e1b-3ed80f0f609f", "f14b39e7-6d8f-4164-afb4-e8fc285d5ac7", "76059652-60dd-4353-bd78-e406180707ff", "17cf893f-28b4-41cd-9df5-2b621fb74f3d"]}]}, {"BlockType": "LINE", "Confidence": 99.95063781738281, "Text": "KWIK TRIP #674", "Geometry": {"BoundingBox": {"Width": 0.19756744801998138, "Height": 0.023608893156051636, "Left": 0.36072900891304016, "Top": 0.36962443590164185}, "Polygon": [{"X": 0.36072900891304016, "Y": 0.37674590945243835}, {"X": 0.5578181147575378, "Y": 0.36962443590164185}, {"X": 0.5582964420318604, "Y": 0.3861120045185089}, {"X": 0.3614102005958557, "Y": 0.3932333290576935}]}, "Id": "1545fd7b-e8fe-46a6-bae2-9529533682ac", "Relationships": [{"Type": "CHILD", "Ids": ["eeee4e68-418f-4ebe-9b36-2defd7b56cf9", "9d959c37-7421-4262-a6aa-dd0d1b2f7383", "3ba775ac-4bdb-43a9-9e14-d10abed055e5"]}]}, {"BlockType": "LINE", "Confidence": 99.65913391113281, "Text": "6107 3M DR", "Geometry": {"BoundingBox": {"Width": 0.13964559137821198, "Height": 0.020413720980286598, "Left": 0.3908270299434662, "Top": 0.3920116722583771}, "Polygon": [{"X": 0.3908270299434662, "Y": 0.3970470130443573}, {"X": 0.5299991369247437, "Y": 0.3920116722583771}, {"X": 0.5304726362228394, "Y": 0.40739017724990845}, {"X": 0.39143431186676025, "Y": 0.4124253988265991}]}, "Id": "708aa8d1-3afc-4ac4-81b5-ba678824b80c", "Relationships": [{"Type": "CHILD", "Ids": ["42cf9187-55ad-4f91-be89-e484668c824d", "a9b2c589-b7e9-4af4-8ccd-f44d1aacd6d3", "c8de30d5-5f21-465e-bd86-700b2ccf6185"]}]}, {"BlockType": "LINE", "Confidence": 98.72528839111328, "Text": "MENOMONIE, WI", "Geometry": {"BoundingBox": {"Width": 0.18348535895347595, "Height": 0.024856187403202057, "Left": 0.36184778809547424, "Top": 0.41202372312545776}, "Polygon": [{"X": 0.36184778809547424, "Y": 0.41865092515945435}, {"X": 0.5447880029678345, "Y": 0.41202372312545776}, {"X": 0.5453331470489502, "Y": 0.43025287985801697}, {"X": 0.3626016080379486, "Y": 0.4368799030780792}]}, "Id": "e3e84452-3f44-4aa7-b120-b9f546db24c0", "Relationships": [{"Type": "CHILD", "Ids": ["776aabbf-f06a-46f3-8354-820ccdb583ca", "2ce40453-9d5e-4081-b3ca-1c8454db9323"]}]}, {"BlockType": "LINE", "Confidence": 98.86439514160156, "Text": "Invoice# 0091264", "Geometry": {"BoundingBox": {"Width": 0.23525457084178925, "Height": 0.024674849584698677, "Left": 0.3254137635231018, "Top": 0.45204710960388184}, "Polygon": [{"X": 0.3254137635231018, "Y": 0.46057361364364624}, {"X": 0.5601997971534729, "Y": 0.45204710960388184}, {"X": 0.5606683492660522, "Y": 0.46819573640823364}, {"X": 0.32612016797065735, "Y": 0.47672197222709656}]}, "Id": "31c752f5-186a-46b3-b44b-1e392d30da23", "Relationships": [{"Type": "CHILD", "Ids": ["b05f1bbb-e73e-4f11-afa2-604bee9ac4d2", "1f13a5d0-ba87-4a93-b497-8b85bafb2c91"]}]}, {"BlockType": "LINE", "Confidence": 98.19050598144531, "Text": "Trans # 0900-1264", "Geometry": {"BoundingBox": {"Width": 0.26495516300201416, "Height": 0.025396080687642097, "Left": 0.32348528504371643, "Top": 0.4718352258205414}, "Polygon": [{"X": 0.32348528504371643, "Y": 0.48145386576652527}, {"X": 0.5880096554756165, "Y": 0.4718352258205414}, {"X": 0.5884404182434082, "Y": 0.4876129627227783}, {"X": 0.3241782784461975, "Y": 0.49723130464553833}]}, "Id": "beff333d-13c6-4583-9fdb-33e24f02ee6d", "Relationships": [{"Type": "CHILD", "Ids": ["b77eda8f-becd-432d-9f07-f0258ca87c94", "355c6d72-29e9-4a39-8591-e655524f3d52", "be415db4-2097-467d-a30e-9f97e1413407"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.05476602539420128, "Height": 0.015079988166689873, "Left": 0.326709121465683, "Top": 0.4999234676361084}, "Polygon": [{"X": 0.326709121465683, "Y": 0.5018981099128723}, {"X": 0.38094615936279297, "Y": 0.4999234676361084}, {"X": 0.38147515058517456, "Y": 0.5130288600921631}, {"X": 0.3272828459739685, "Y": 0.5150034427642822}]}, "Id": "2b87f818-b3e7-4a49-86b8-a2a27258973e", "Relationships": [{"Type": "CHILD", "Ids": ["6cd749ed-2692-40b8-b8cd-1a1cf4e92d8b"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "10/14/2024", "Geometry": {"BoundingBox": {"Width": 0.13732492923736572, "Height": 0.020604457706212997, "Left": 0.4663439691066742, "Top": 0.49336540699005127}, "Polygon": [{"X": 0.4663439691066742, "Y": 0.4983505308628082}, {"X": 0.6032568216323853, "Y": 0.49336540699005127}, {"X": 0.6036689281463623, "Y": 0.5089849233627319}, {"X": 0.4668905735015869, "Y": 0.513969898223877}]}, "Id": "b2981fe4-5e8e-4f16-a2a3-c202ffab5a9f", "Relationships": [{"Type": "CHILD", "Ids": ["5d56dd03-83b1-4fcb-a9a3-3cda4a269c96"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.05644448474049568, "Height": 0.01606922037899494, "Left": 0.32618141174316406, "Top": 0.5192623734474182}, "Polygon": [{"X": 0.32618141174316406, "Y": 0.5212991237640381}, {"X": 0.382059782743454, "Y": 0.5192623734474182}, {"X": 0.38262587785720825, "Y": 0.5332948565483093}, {"X": 0.3267969489097595, "Y": 0.5353315472602844}]}, "Id": "cabb74c4-ae97-4a81-9e44-9f2ed3151309", "Relationships": [{"Type": "CHILD", "Ids": ["06251e87-fa39-4d4e-8453-5ed91377ed8e"]}]}, {"BlockType": "LINE", "Confidence": 99.3092269897461, "Text": "04:31 PM", "Geometry": {"BoundingBox": {"Width": 0.10984169691801071, "Height": 0.01794710010290146, "Left": 0.46685606241226196, "Top": 0.5144302845001221}, "Polygon": [{"X": 0.46685606241226196, "Y": 0.5184202790260315}, {"X": 0.5763053894042969, "Y": 0.5144302845001221}, {"X": 0.5766977667808533, "Y": 0.5283874869346619}, {"X": 0.46734464168548584, "Y": 0.5323773622512817}]}, "Id": "a519f47b-80e9-4ee9-99dc-aa7669d5593b", "Relationships": [{"Type": "CHILD", "Ids": ["ff080975-66bf-43e5-ae64-0aa6654d7db1", "4369fdce-306c-4692-a68d-1f1a86ca616e"]}]}, {"BlockType": "LINE", "Confidence": 99.9140396118164, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.2766816318035126, "Height": 0.028106873854994774, "Left": 0.32986927032470703, "Top": 0.5522388815879822}, "Polygon": [{"X": 0.32986927032470703, "Y": 0.5623319149017334}, {"X": 0.6060770750045776, "Y": 0.5522388815879822}, {"X": 0.606550931930542, "Y": 0.5702531933784485}, {"X": 0.33065730333328247, "Y": 0.5803457498550415}]}, "Id": "f6864572-848a-4cb0-bcb7-d8f362da0bcc", "Relationships": [{"Type": "CHILD", "Ids": ["da67ac21-e37f-4e10-8da8-8042db87cd90", "36c4d13d-49d4-44c4-8d2a-6edf60dc4a1b", "c0c95bab-82f0-4a1f-94a8-14f66653dda4"]}]}, {"BlockType": "LINE", "Confidence": 99.52168273925781, "Text": "04", "Geometry": {"BoundingBox": {"Width": 0.02663435973227024, "Height": 0.014045603573322296, "Left": 0.3448355197906494, "Top": 0.5812066197395325}, "Polygon": [{"X": 0.3448355197906494, "Y": 0.5821613669395447}, {"X": 0.3709304630756378, "Y": 0.5812066197395325}, {"X": 0.3714698553085327, "Y": 0.5942975282669067}, {"X": 0.3453965187072754, "Y": 0.5952522158622742}]}, "Id": "aefd88d6-77f8-4422-bafa-33a9a47d1d8a", "Relationships": [{"Type": "CHILD", "Ids": ["a985b7af-0363-46d9-a939-77cee8e577fb"]}]}, {"BlockType": "LINE", "Confidence": 99.68679809570312, "Text": "171.800 3.589", "Geometry": {"BoundingBox": {"Width": 0.19322726130485535, "Height": 0.02314114198088646, "Left": 0.41411861777305603, "Top": 0.5732132792472839}, "Polygon": [{"X": 0.41411861777305603, "Y": 0.5802676677703857}, {"X": 0.6069230437278748, "Y": 0.5732132792472839}, {"X": 0.6073458790779114, "Y": 0.5893003940582275}, {"X": 0.4147375226020813, "Y": 0.5963544249534607}]}, "Id": "2eba54cf-fce0-4512-b74a-cf0443d598eb", "Relationships": [{"Type": "CHILD", "Ids": ["6519c9e7-c331-48b3-a2c0-bf4ce4c1479a", "25d0bf08-e99e-4158-845e-2db8c6bc7370"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "04", "Geometry": {"BoundingBox": {"Width": 0.02648155763745308, "Height": 0.014114859513938427, "Left": 0.34591373801231384, "Top": 0.6015783548355103}, "Polygon": [{"X": 0.34591373801231384, "Y": 0.6025285720825195}, {"X": 0.37185290455818176, "Y": 0.6015783548355103}, {"X": 0.37239527702331543, "Y": 0.6147430539131165}, {"X": 0.3464777171611786, "Y": 0.615693211555481}]}, "Id": "07adec34-4577-4d04-98a3-b7e67b3aa636", "Relationships": [{"Type": "CHILD", "Ids": ["88712eca-48fe-4ed8-8354-1a7beb3563a7"]}]}, {"BlockType": "LINE", "Confidence": 99.66358184814453, "Text": "20.692 4.189", "Geometry": {"BoundingBox": {"Width": 0.17895229160785675, "Height": 0.022649768739938736, "Left": 0.4293588399887085, "Top": 0.5935106873512268}, "Polygon": [{"X": 0.4293588399887085, "Y": 0.6000507473945618}, {"X": 0.6078881025314331, "Y": 0.5935106873512268}, {"X": 0.608311116695404, "Y": 0.6096206903457642}, {"X": 0.4299638867378235, "Y": 0.6161604523658752}]}, "Id": "fe20f90a-aca5-4662-af98-15ff84a7ebf6", "Relationships": [{"Type": "CHILD", "Ids": ["8bf219d4-ebe2-45e0-9f4d-7b8597c6841a", "91a9176b-4a3e-4b74-9a79-e635d9578314"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.09675811976194382, "Height": 0.017069604247808456, "Left": 0.33332645893096924, "Top": 0.6404123306274414}, "Polygon": [{"X": 0.33332645893096924, "Y": 0.6439467668533325}, {"X": 0.429575115442276, "Y": 0.6404123306274414}, {"X": 0.43008458614349365, "Y": 0.6539475917816162}, {"X": 0.33391866087913513, "Y": 0.6574819087982178}]}, "Id": "a135bf3a-aef1-42ea-a48f-cece29d4b4e9", "Relationships": [{"Type": "CHILD", "Ids": ["4239e047-b3fd-48e8-b98f-18056e706988"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.08349441736936569, "Height": 0.0188782699406147, "Left": 0.5274695754051208, "Top": 0.6347059011459351}, "Polygon": [{"X": 0.5274695754051208, "Y": 0.6377570033073425}, {"X": 0.6105499863624573, "Y": 0.6347059011459351}, {"X": 0.6109640002250671, "Y": 0.6505332589149475}, {"X": 0.5279670357704163, "Y": 0.6535841822624207}]}, "Id": "16069fb4-81b1-4335-ab54-b51b94339977", "Relationships": [{"Type": "CHILD", "Ids": ["e967d357-112a-452d-a089-68436ff98e19"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.05495951697230339, "Height": 0.01497382391244173, "Left": 0.33432072401046753, "Top": 0.6626691818237305}, "Polygon": [{"X": 0.33432072401046753, "Y": 0.6646707653999329}, {"X": 0.3887576460838318, "Y": 0.6626691818237305}, {"X": 0.3892802298069, "Y": 0.6756415367126465}, {"X": 0.334888219833374, "Y": 0.6776430010795593}]}, "Id": "bc94196b-0970-402b-87cb-17c79e5e6968", "Relationships": [{"Type": "CHILD", "Ids": ["16c08929-3010-40f7-b6af-2dfb183a00b1"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "616.59", "Geometry": {"BoundingBox": {"Width": 0.08259709924459457, "Height": 0.01827443577349186, "Left": 0.5286075472831726, "Top": 0.6554474830627441}, "Polygon": [{"X": 0.5286075472831726, "Y": 0.6584699153900146}, {"X": 0.6108053922653198, "Y": 0.6554474830627441}, {"X": 0.6112046241760254, "Y": 0.6706995964050293}, {"X": 0.5290864109992981, "Y": 0.6737219095230103}]}, "Id": "17476ba8-d053-47dc-af8e-77f37e803d04", "Relationships": [{"Type": "CHILD", "Ids": ["e915a9ad-2c5b-4903-9f34-9f97fa6e3e7a"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "DEFBULK", "Geometry": {"BoundingBox": {"Width": 0.09657197445631027, "Height": 0.016623321920633316, "Left": 0.33505934476852417, "Top": 0.6816321015357971}, "Polygon": [{"X": 0.33505934476852417, "Y": 0.6851691603660583}, {"X": 0.4311387538909912, "Y": 0.6816321015357971}, {"X": 0.43163132667541504, "Y": 0.6947185397148132}, {"X": 0.33563196659088135, "Y": 0.6982554793357849}]}, "Id": "425639da-1712-45f2-be06-70f1301e8f4e", "Relationships": [{"Type": "CHILD", "Ids": ["96c3da03-81a6-44c4-9734-5c8419880014"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "86.68", "Geometry": {"BoundingBox": {"Width": 0.06907442212104797, "Height": 0.016308285295963287, "Left": 0.5434471964836121, "Top": 0.6766374111175537}, "Polygon": [{"X": 0.5434471964836121, "Y": 0.6791672706604004}, {"X": 0.6121616363525391, "Y": 0.6766374111175537}, {"X": 0.6125215888023376, "Y": 0.6904159188270569}, {"X": 0.5438673496246338, "Y": 0.692945659160614}]}, "Id": "21eea2b1-ab16-455c-bf88-895941924272", "Relationships": [{"Type": "CHILD", "Ids": ["dc929d69-c98e-4a11-a948-fdcafd66c3b7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06977012008428574, "Height": 0.015891438350081444, "Left": 0.33563369512557983, "Top": 0.7233551144599915}, "Polygon": [{"X": 0.33563369512557983, "Y": 0.725910484790802}, {"X": 0.404878169298172, "Y": 0.7233551144599915}, {"X": 0.4054037928581238, "Y": 0.7366912961006165}, {"X": 0.3362182676792145, "Y": 0.7392465472221375}]}, "Id": "da7d95c5-26c3-4daa-943a-dfc46357d511", "Relationships": [{"Type": "CHILD", "Ids": ["8ce0699c-0dc0-4d35-a88e-5451f70071fb"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "703.27", "Geometry": {"BoundingBox": {"Width": 0.08198404312133789, "Height": 0.01752222515642643, "Left": 0.5179861187934875, "Top": 0.7172594666481018}, "Polygon": [{"X": 0.5179861187934875, "Y": 0.7202706933021545}, {"X": 0.5995784401893616, "Y": 0.7172594666481018}, {"X": 0.5999701619148254, "Y": 0.7317705750465393}, {"X": 0.5184533596038818, "Y": 0.7347816824913025}]}, "Id": "bb98b5cc-54c2-44af-b0bf-c7ab6bf14d1b", "Relationships": [{"Type": "CHILD", "Ids": ["6a751946-1795-4db4-9606-1ed81366fcd3"]}]}, {"BlockType": "LINE", "Confidence": 99.8396987915039, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.26311570405960083, "Height": 0.025132104754447937, "Left": 0.3385973572731018, "Top": 0.7567457556724548}, "Polygon": [{"X": 0.3385973572731018, "Y": 0.7664640545845032}, {"X": 0.6012976169586182, "Y": 0.7567457556724548}, {"X": 0.6017130613327026, "Y": 0.7721601128578186}, {"X": 0.3392718434333801, "Y": 0.781877875328064}]}, "Id": "e9c7c8a9-784e-42e2-b154-3721a9e9b595", "Relationships": [{"Type": "CHILD", "Ids": ["33d039cd-ebed-4f1b-a39b-6f866ba1f9fe", "cb4d11d7-3dac-4b13-beff-b3b1d1ee3ad6", "7226e68c-cda6-4a41-8fe8-2caac2a9f696"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.12422561645507812, "Height": 0.017659248784184456, "Left": 0.33996036648750305, "Top": 0.7825700044631958}, "Polygon": [{"X": 0.33996036648750305, "Y": 0.7871540188789368}, {"X": 0.*****************, "Y": 0.7825700044631958}, {"X": 0.****************, "Y": 0.7956455945968628}, {"X": 0.3405321538448334, "Y": 0.8002292513847351}]}, "Id": "93feb92d-f0d9-4c98-8672-4c7487e5e6f9", "Relationships": [{"Type": "CHILD", "Ids": ["fe9014b1-043d-43d9-ab9b-f7a79daf47ec", "ebab29cd-f2bd-4ff8-adec-425ede11c0d1"]}]}, {"BlockType": "LINE", "Confidence": 28.0056209564209, "Text": "XXXXXXXXXXXXXX1591", "Geometry": {"BoundingBox": {"Width": 0.24606989324092865, "Height": 0.021812358871102333, "Left": 0.3405051827430725, "Top": 0.7990379929542542}, "Polygon": [{"X": 0.3405051827430725, "Y": 0.8081507086753845}, {"X": 0.5862196087837219, "Y": 0.7990379929542542}, {"X": 0.5865750908851624, "Y": 0.8117382526397705}, {"X": 0.3410608470439911, "Y": 0.8208503723144531}]}, "Id": "47a4e25a-ccca-4846-be7d-5ec028865be4", "Relationships": [{"Type": "CHILD", "Ids": ["9c781593-1024-4911-a066-1e1598149376"]}]}, {"BlockType": "LINE", "Confidence": 99.9365234375, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.08407134562730789, "Height": 0.016345659270882607, "Left": 0.34168991446495056, "Top": 0.8245274424552917}, "Polygon": [{"X": 0.34168991446495056, "Y": 0.8276301026344299}, {"X": 0.4252532124519348, "Y": 0.8245274424552917}, {"X": 0.42576128244400024, "Y": 0.8377705812454224}, {"X": 0.34226906299591064, "Y": 0.8408730626106262}]}, "Id": "c17a29b3-a544-4679-8e1b-3ed80f0f609f", "Relationships": [{"Type": "CHILD", "Ids": ["bc9f4785-2343-40b2-9b47-0cde9f4a9da9", "4df0ecd8-7e15-4934-a2a0-e3259599a340"]}]}, {"BlockType": "LINE", "Confidence": 99.5517807006836, "Text": "P02385", "Geometry": {"BoundingBox": {"Width": 0.08157550543546677, "Height": 0.015940601006150246, "Left": 0.5366358160972595, "Top": 0.8179303407669067}, "Polygon": [{"X": 0.5366358160972595, "Y": 0.8209468126296997}, {"X": 0.617875337600708, "Y": 0.8179303407669067}, {"X": 0.6182113289833069, "Y": 0.830854594707489}, {"X": 0.5370392203330994, "Y": 0.8338708877563477}]}, "Id": "f14b39e7-6d8f-4164-afb4-e8fc285d5ac7", "Relationships": [{"Type": "CHILD", "Ids": ["6469031b-aef2-4fc0-96f4-46a2952c4781"]}]}, {"BlockType": "LINE", "Confidence": 99.65631103515625, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.24871289730072021, "Height": 0.022571705281734467, "Left": 0.34280648827552795, "Top": 0.8391445875167847}, "Polygon": [{"X": 0.34280648827552795, "Y": 0.8483769297599792}, {"X": 0.5911492705345154, "Y": 0.8391445875167847}, {"X": 0.5915194153785706, "Y": 0.8524845838546753}, {"X": 0.3433896601200104, "Y": 0.8617163300514221}]}, "Id": "76059652-60dd-4353-bd78-e406180707ff", "Relationships": [{"Type": "CHILD", "Ids": ["65d46552-a89b-4835-9852-77561f6e81b3", "98aa6446-e748-4c96-b4ae-c04761be74b3"]}]}, {"BlockType": "LINE", "Confidence": 99.56207275390625, "Text": "AUTH REF#: P02385", "Geometry": {"BoundingBox": {"Width": 0.2347254604101181, "Height": 0.02228526957333088, "Left": 0.35775017738342285, "Top": 0.8592377305030823}, "Polygon": [{"X": 0.35775017738342285, "Y": 0.8679602742195129}, {"X": 0.5920996069908142, "Y": 0.8592377305030823}, {"X": 0.5924756526947021, "Y": 0.8728010654449463}, {"X": 0.3583308160305023, "Y": 0.8815230131149292}]}, "Id": "17cf893f-28b4-41cd-9df5-2b621fb74f3d", "Relationships": [{"Type": "CHILD", "Ids": ["43606e29-a4cf-431f-85f4-0cca00a1aa2e", "cdae377a-4c10-4853-aa82-f5c381401dca", "e7c998ac-91fa-4aed-a759-c823299fc301"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056037452071905136, "Height": 0.016172492876648903, "Left": 0.36072900891304016, "Top": 0.37474048137664795}, "Polygon": [{"X": 0.36072900891304016, "Y": 0.37674590945243835}, {"X": 0.4162302613258362, "Y": 0.37474048137664795}, {"X": 0.4167664647102356, "Y": 0.3889075517654419}, {"X": 0.3613143265247345, "Y": 0.3909129798412323}]}, "Id": "eeee4e68-418f-4ebe-9b36-2defd7b56cf9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056412920355796814, "Height": 0.016160478815436363, "Left": 0.4302762448787689, "Top": 0.37340250611305237}, "Polygon": [{"X": 0.4302762448787689, "Y": 0.37542393803596497}, {"X": 0.486215740442276, "Y": 0.37340250611305237}, {"X": 0.48668915033340454, "Y": 0.38754159212112427}, {"X": 0.43079903721809387, "Y": 0.3895629942417145}]}, "Id": "9d959c37-7421-4262-a6aa-dd0d1b2f7383"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "#674", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05677762255072594, "Height": 0.01606944017112255, "Left": 0.5015188455581665, "Top": 0.37207967042922974}, "Polygon": [{"X": 0.5015188455581665, "Y": 0.37411683797836304}, {"X": 0.5578893423080444, "Y": 0.37207967042922974}, {"X": 0.5582964420318604, "Y": 0.3861120045185089}, {"X": 0.5019752979278564, "Y": 0.38814911246299744}]}, "Id": "3ba775ac-4bdb-43a9-9e14-d10abed055e5"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "6107", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054580312222242355, "Height": 0.016072213649749756, "Left": 0.3908270299434662, "Top": 0.3950906991958618}, "Polygon": [{"X": 0.3908270299434662, "Y": 0.3970470130443573}, {"X": 0.4448976516723633, "Y": 0.3950906991958618}, {"X": 0.44540736079216003, "Y": 0.4092066287994385}, {"X": 0.39138445258140564, "Y": 0.4111629128456116}]}, "Id": "42cf9187-55ad-4f91-be89-e484668c824d"}, {"BlockType": "WORD", "Confidence": 99.33294677734375, "Text": "3M", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027121642604470253, "Height": 0.014809180982410908, "Left": 0.46118026971817017, "Top": 0.39473244547843933}, "Polygon": [{"X": 0.46118026971817017, "Y": 0.3956970274448395}, {"X": 0.48783913254737854, "Y": 0.39473244547843933}, {"X": 0.4883019030094147, "Y": 0.408577024936676}, {"X": 0.4616661071777344, "Y": 0.40954160690307617}]}, "Id": "a9b2c589-b7e9-4af4-8ccd-f44d1aacd6d3"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027008796110749245, "Height": 0.014382705092430115, "Left": 0.5034638047218323, "Top": 0.3939698040485382}, "Polygon": [{"X": 0.5034638047218323, "Y": 0.39493218064308167}, {"X": 0.5300594568252563, "Y": 0.3939698040485382}, {"X": 0.5304726362228394, "Y": 0.40739017724990845}, {"X": 0.503899335861206, "Y": 0.4083525240421295}]}, "Id": "c8de30d5-5f21-465e-bd86-700b2ccf6185"}, {"BlockType": "WORD", "Confidence": 97.55958557128906, "Text": "MENOMONIE,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13841810822486877, "Height": 0.023221712559461594, "Left": 0.36184778809547424, "Top": 0.4136582016944885}, "Polygon": [{"X": 0.36184778809547424, "Y": 0.41865092515945435}, {"X": 0.49966931343078613, "Y": 0.4136582016944885}, {"X": 0.500265896320343, "Y": 0.43188729882240295}, {"X": 0.3626016080379486, "Y": 0.4368799030780792}]}, "Id": "776aabbf-f06a-46f3-8354-820ccdb583ca"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028730962425470352, "Height": 0.01432209275662899, "Left": 0.5165271759033203, "Top": 0.4144487977027893}, "Polygon": [{"X": 0.5165271759033203, "Y": 0.41547536849975586}, {"X": 0.5448605418205261, "Y": 0.4144487977027893}, {"X": 0.545258104801178, "Y": 0.4277443587779999}, {"X": 0.5169483423233032, "Y": 0.42877089977264404}]}, "Id": "2ce40453-9d5e-4081-b3ca-1c8454db9323"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11098285019397736, "Height": 0.018895041197538376, "Left": 0.3254137635231018, "Top": 0.4565630555152893}, "Polygon": [{"X": 0.3254137635231018, "Y": 0.46057361364364624}, {"X": 0.4358486533164978, "Y": 0.4565630555152893}, {"X": 0.43639659881591797, "Y": 0.4714476466178894}, {"X": 0.3260648846626282, "Y": 0.4754580855369568}]}, "Id": "b05f1bbb-e73e-4f11-afa2-604bee9ac4d2"}, {"BlockType": "WORD", "Confidence": 98.15669250488281, "Text": "0091264", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09673810005187988, "Height": 0.017570653930306435, "Left": 0.46393021941185, "Top": 0.45412376523017883}, "Polygon": [{"X": 0.46393021941185, "Y": 0.4576224982738495}, {"X": 0.5602600574493408, "Y": 0.45412376523017883}, {"X": 0.5606683492660522, "Y": 0.46819573640823364}, {"X": 0.4644235372543335, "Y": 0.4716944098472595}]}, "Id": "1f13a5d0-ba87-4a93-b497-8b85bafb2c91"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07099373638629913, "Height": 0.016604751348495483, "Left": 0.32348528504371643, "Top": 0.47889256477355957}, "Polygon": [{"X": 0.32348528504371643, "Y": 0.48145386576652527}, {"X": 0.393924355506897, "Y": 0.47889256477355957}, {"X": 0.39447900652885437, "Y": 0.49293607473373413}, {"X": 0.32410213351249695, "Y": 0.49549731612205505}]}, "Id": "b77eda8f-becd-432d-9f07-f0258ca87c94"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015050084330141544, "Height": 0.013492811471223831, "Left": 0.4224761724472046, "Top": 0.47850680351257324}, "Polygon": [{"X": 0.4224761724472046, "Y": 0.47903674840927124}, {"X": 0.43704935908317566, "Y": 0.47850680351257324}, {"X": 0.43752625584602356, "Y": 0.49146968126296997}, {"X": 0.4229649305343628, "Y": 0.4919995963573456}]}, "Id": "355c6d72-29e9-4a39-8591-e655524f3d52"}, {"BlockType": "WORD", "Confidence": 94.86766815185547, "Text": "0900-1264", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12322931736707687, "Height": 0.018567979335784912, "Left": 0.4652111232280731, "Top": 0.473512202501297}, "Polygon": [{"X": 0.4652111232280731, "Y": 0.47797951102256775}, {"X": 0.588055431842804, "Y": 0.473512202501297}, {"X": 0.5884404182434082, "Y": 0.4876129627227783}, {"X": 0.46570494771003723, "Y": 0.4920801818370819}]}, "Id": "be415db4-2097-467d-a30e-9f97e1413407"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05476602539420128, "Height": 0.015079988166689873, "Left": 0.326709121465683, "Top": 0.4999234676361084}, "Polygon": [{"X": 0.326709121465683, "Y": 0.5018981099128723}, {"X": 0.38094615936279297, "Y": 0.4999234676361084}, {"X": 0.38147515058517456, "Y": 0.5130288600921631}, {"X": 0.3272828459739685, "Y": 0.5150034427642822}]}, "Id": "6cd749ed-2692-40b8-b8cd-1a1cf4e92d8b"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "10/14/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13732492923736572, "Height": 0.020604457706212997, "Left": 0.4663439691066742, "Top": 0.49336540699005127}, "Polygon": [{"X": 0.4663439691066742, "Y": 0.4983505308628082}, {"X": 0.6032568216323853, "Y": 0.49336540699005127}, {"X": 0.6036689281463623, "Y": 0.5089849233627319}, {"X": 0.4668905735015869, "Y": 0.513969898223877}]}, "Id": "5d56dd03-83b1-4fcb-a9a3-3cda4a269c96"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05644448474049568, "Height": 0.01606922037899494, "Left": 0.32618141174316406, "Top": 0.5192623734474182}, "Polygon": [{"X": 0.32618141174316406, "Y": 0.5212991237640381}, {"X": 0.382059782743454, "Y": 0.5192623734474182}, {"X": 0.38262587785720825, "Y": 0.5332948565483093}, {"X": 0.3267969489097595, "Y": 0.5353315472602844}]}, "Id": "06251e87-fa39-4d4e-8453-5ed91377ed8e"}, {"BlockType": "WORD", "Confidence": 98.8243408203125, "Text": "04:31", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06487687677145004, "Height": 0.015986574813723564, "Left": 0.46685606241226196, "Top": 0.516070544719696}, "Polygon": [{"X": 0.46685606241226196, "Y": 0.5184202790260315}, {"X": 0.5313109159469604, "Y": 0.516070544719696}, {"X": 0.5317329168319702, "Y": 0.5297074913978577}, {"X": 0.46733343601226807, "Y": 0.5320571660995483}]}, "Id": "ff080975-66bf-43e5-ae64-0aa6654d7db1"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02672739513218403, "Height": 0.014066804200410843, "Left": 0.5499703884124756, "Top": 0.515281617641449}, "Polygon": [{"X": 0.5499703884124756, "Y": 0.5162426233291626}, {"X": 0.5763292908668518, "Y": 0.515281617641449}, {"X": 0.5766977667808533, "Y": 0.5283874869346619}, {"X": 0.5503605604171753, "Y": 0.5293484330177307}]}, "Id": "4369fdce-306c-4692-a68d-1f1a86ca616e"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05492456257343292, "Height": 0.019047394394874573, "Left": 0.32986927032470703, "Top": 0.5603500604629517}, "Polygon": [{"X": 0.32986927032470703, "Y": 0.5623319149017334}, {"X": 0.38410577178001404, "Y": 0.5603500604629517}, {"X": 0.38479381799697876, "Y": 0.5774156451225281}, {"X": 0.3306158185005188, "Y": 0.579397439956665}]}, "Id": "da67ac21-e37f-4e10-8da8-8042db87cd90"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11002308875322342, "Height": 0.02181054838001728, "Left": 0.3995164632797241, "Top": 0.5559906363487244}, "Polygon": [{"X": 0.3995164632797241, "Y": 0.5599899291992188}, {"X": 0.5089618563652039, "Y": 0.5559906363487244}, {"X": 0.5095395445823669, "Y": 0.5738019943237305}, {"X": 0.4002172350883484, "Y": 0.5778011679649353}]}, "Id": "36c4d13d-49d4-44c4-8d2a-6edf60dc4a1b"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0690527930855751, "Height": 0.017594754695892334, "Left": 0.5374618768692017, "Top": 0.5537905097007751}, "Polygon": [{"X": 0.5374618768692017, "Y": 0.5562995672225952}, {"X": 0.6061179041862488, "Y": 0.5537905097007751}, {"X": 0.6065146923065186, "Y": 0.568876326084137}, {"X": 0.5379241108894348, "Y": 0.5713852643966675}]}, "Id": "c0c95bab-82f0-4a1f-94a8-14f66653dda4"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02663435973227024, "Height": 0.014045603573322296, "Left": 0.3448355197906494, "Top": 0.5812066197395325}, "Polygon": [{"X": 0.3448355197906494, "Y": 0.5821613669395447}, {"X": 0.3709304630756378, "Y": 0.5812066197395325}, {"X": 0.3714698553085327, "Y": 0.5942975282669067}, {"X": 0.3453965187072754, "Y": 0.5952522158622742}]}, "Id": "a985b7af-0363-46d9-a939-77cee8e577fb"}, {"BlockType": "WORD", "Confidence": 99.56233978271484, "Text": "171.800", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09644929319620132, "Height": 0.017138605937361717, "Left": 0.41411861777305603, "Top": 0.5767549276351929}, "Polygon": [{"X": 0.41411861777305603, "Y": 0.5802676677703857}, {"X": 0.5101263523101807, "Y": 0.5767549276351929}, {"X": 0.5105679035186768, "Y": 0.5903809070587158}, {"X": 0.4146428406238556, "Y": 0.5938935279846191}]}, "Id": "6519c9e7-c331-48b3-a2c0-bf4ce4c1479a"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "3.589", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06846767663955688, "Height": 0.017491890117526054, "Left": 0.5388782024383545, "Top": 0.5742992758750916}, "Polygon": [{"X": 0.5388782024383545, "Y": 0.5767900943756104}, {"X": 0.6069515347480774, "Y": 0.5742992758750916}, {"X": 0.6073458790779114, "Y": 0.5893003940582275}, {"X": 0.5393370389938354, "Y": 0.5917911529541016}]}, "Id": "25d0bf08-e99e-4158-845e-2db8c6bc7370"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02648155763745308, "Height": 0.014114859513938427, "Left": 0.34591373801231384, "Top": 0.6015783548355103}, "Polygon": [{"X": 0.34591373801231384, "Y": 0.6025285720825195}, {"X": 0.37185290455818176, "Y": 0.6015783548355103}, {"X": 0.37239527702331543, "Y": 0.6147430539131165}, {"X": 0.3464777171611786, "Y": 0.615693211555481}]}, "Id": "88712eca-48fe-4ed8-8354-1a7beb3563a7"}, {"BlockType": "WORD", "Confidence": 99.61355590820312, "Text": "20.692", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08186110109090805, "Height": 0.01677897572517395, "Left": 0.4293588399887085, "Top": 0.5970683097839355}, "Polygon": [{"X": 0.4293588399887085, "Y": 0.6000507473945618}, {"X": 0.5107728838920593, "Y": 0.5970683097839355}, {"X": 0.5112199187278748, "Y": 0.6108649969100952}, {"X": 0.42987701296806335, "Y": 0.6138473153114319}]}, "Id": "8bf219d4-ebe2-45e0-9f4d-7b8597c6841a"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "4.189", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06841839104890823, "Height": 0.01738291047513485, "Left": 0.5398927330970764, "Top": 0.5947299003601074}, "Polygon": [{"X": 0.5398927330970764, "Y": 0.5972221493721008}, {"X": 0.6079201102256775, "Y": 0.5947299003601074}, {"X": 0.608311116695404, "Y": 0.6096206903457642}, {"X": 0.5403478145599365, "Y": 0.612112820148468}]}, "Id": "91a9176b-4a3e-4b74-9a79-e635d9578314"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09675811976194382, "Height": 0.017069604247808456, "Left": 0.33332645893096924, "Top": 0.6404123306274414}, "Polygon": [{"X": 0.33332645893096924, "Y": 0.6439467668533325}, {"X": 0.429575115442276, "Y": 0.6404123306274414}, {"X": 0.43008458614349365, "Y": 0.6539475917816162}, {"X": 0.33391866087913513, "Y": 0.6574819087982178}]}, "Id": "4239e047-b3fd-48e8-b98f-18056e706988"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08349441736936569, "Height": 0.0188782699406147, "Left": 0.5274695754051208, "Top": 0.6347059011459351}, "Polygon": [{"X": 0.5274695754051208, "Y": 0.6377570033073425}, {"X": 0.6105499863624573, "Y": 0.6347059011459351}, {"X": 0.6109640002250671, "Y": 0.6505332589149475}, {"X": 0.5279670357704163, "Y": 0.6535841822624207}]}, "Id": "e967d357-112a-452d-a089-68436ff98e19"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05495951697230339, "Height": 0.01497382391244173, "Left": 0.33432072401046753, "Top": 0.6626691818237305}, "Polygon": [{"X": 0.33432072401046753, "Y": 0.6646707653999329}, {"X": 0.3887576460838318, "Y": 0.6626691818237305}, {"X": 0.3892802298069, "Y": 0.6756415367126465}, {"X": 0.334888219833374, "Y": 0.6776430010795593}]}, "Id": "16c08929-3010-40f7-b6af-2dfb183a00b1"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "616.59", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08259709924459457, "Height": 0.01827443577349186, "Left": 0.5286075472831726, "Top": 0.6554474830627441}, "Polygon": [{"X": 0.5286075472831726, "Y": 0.6584699153900146}, {"X": 0.6108053922653198, "Y": 0.6554474830627441}, {"X": 0.6112046241760254, "Y": 0.6706995964050293}, {"X": 0.5290864109992981, "Y": 0.6737219095230103}]}, "Id": "e915a9ad-2c5b-4903-9f34-9f97fa6e3e7a"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "DEFBULK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09657197445631027, "Height": 0.016623321920633316, "Left": 0.33505934476852417, "Top": 0.6816321015357971}, "Polygon": [{"X": 0.33505934476852417, "Y": 0.6851691603660583}, {"X": 0.4311387538909912, "Y": 0.6816321015357971}, {"X": 0.43163132667541504, "Y": 0.6947185397148132}, {"X": 0.33563196659088135, "Y": 0.6982554793357849}]}, "Id": "96c3da03-81a6-44c4-9734-5c8419880014"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "86.68", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06907442212104797, "Height": 0.016308285295963287, "Left": 0.5434471964836121, "Top": 0.6766374111175537}, "Polygon": [{"X": 0.5434471964836121, "Y": 0.6791672706604004}, {"X": 0.6121616363525391, "Y": 0.6766374111175537}, {"X": 0.6125215888023376, "Y": 0.6904159188270569}, {"X": 0.5438673496246338, "Y": 0.692945659160614}]}, "Id": "dc929d69-c98e-4a11-a948-fdcafd66c3b7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06977012008428574, "Height": 0.015891438350081444, "Left": 0.33563369512557983, "Top": 0.7233551144599915}, "Polygon": [{"X": 0.33563369512557983, "Y": 0.725910484790802}, {"X": 0.404878169298172, "Y": 0.7233551144599915}, {"X": 0.4054037928581238, "Y": 0.7366912961006165}, {"X": 0.3362182676792145, "Y": 0.7392465472221375}]}, "Id": "8ce0699c-0dc0-4d35-a88e-5451f70071fb"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "703.27", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08198404312133789, "Height": 0.01752222515642643, "Left": 0.5179861187934875, "Top": 0.7172594666481018}, "Polygon": [{"X": 0.5179861187934875, "Y": 0.7202706933021545}, {"X": 0.5995784401893616, "Y": 0.7172594666481018}, {"X": 0.5999701619148254, "Y": 0.7317705750465393}, {"X": 0.5184533596038818, "Y": 0.7347816824913025}]}, "Id": "6a751946-1795-4db4-9606-1ed81366fcd3"}, {"BlockType": "WORD", "Confidence": 99.51908874511719, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11086464673280716, "Height": 0.01949417218565941, "Left": 0.3385973572731018, "Top": 0.7623836994171143}, "Polygon": [{"X": 0.3385973572731018, "Y": 0.7664640545845032}, {"X": 0.4488963186740875, "Y": 0.7623836994171143}, {"X": 0.44946199655532837, "Y": 0.7777976989746094}, {"X": 0.3392718434333801, "Y": 0.781877875328064}]}, "Id": "33d039cd-ebed-4f1b-a39b-6f866ba1f9fe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05551594868302345, "Height": 0.01500790473073721, "Left": 0.46281370520591736, "Top": 0.7604841589927673}, "Polygon": [{"X": 0.46281370520591736, "Y": 0.7625224590301514}, {"X": 0.517910897731781, "Y": 0.7604841589927673}, {"X": 0.5183296203613281, "Y": 0.7734538316726685}, {"X": 0.4632781445980072, "Y": 0.7754920125007629}]}, "Id": "cb4d11d7-3dac-4b13-beff-b3b1d1ee3ad6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06804925203323364, "Height": 0.015637317672371864, "Left": 0.****************, "Top": 0.7580264806747437}, "Polygon": [{"X": 0.****************, "Y": 0.760530948638916}, {"X": 0.6013321280479431, "Y": 0.7580264806747437}, {"X": 0.6016861200332642, "Y": 0.7711594104766846}, {"X": 0.****************, "Y": 0.7736637592315674}]}, "Id": "7226e68c-cda6-4a41-8fe8-2caac2a9f696"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06994561851024628, "Height": 0.015488600358366966, "Left": 0.33996036648750305, "Top": 0.7845820188522339}, "Polygon": [{"X": 0.33996036648750305, "Y": 0.7871540188789368}, {"X": 0.****************, "Y": 0.7845820188522339}, {"X": 0.****************, "Y": 0.7974987626075745}, {"X": 0.34052520990371704, "Y": 0.800070583820343}]}, "Id": "fe9014b1-043d-43d9-ab9b-f7a79daf47ec"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03840961307287216, "Height": 0.014294314198195934, "Left": 0.****************, "Top": 0.7827568054199219}, "Polygon": [{"X": 0.****************, "Y": 0.7841624021530151}, {"X": 0.****************, "Y": 0.7827568054199219}, {"X": 0.****************, "Y": 0.7956455945968628}, {"X": 0.****************, "Y": 0.7970511317253113}]}, "Id": "ebab29cd-f2bd-4ff8-adec-425ede11c0d1"}, {"BlockType": "WORD", "Confidence": 28.0056209564209, "Text": "XXXXXXXXXXXXXX1591", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24606989324092865, "Height": 0.021812358871102333, "Left": 0.3405051827430725, "Top": 0.7990379929542542}, "Polygon": [{"X": 0.3405051827430725, "Y": 0.8081507086753845}, {"X": 0.5862196087837219, "Y": 0.7990379929542542}, {"X": 0.5865750908851624, "Y": 0.8117382526397705}, {"X": 0.3410608470439911, "Y": 0.8208503723144531}]}, "Id": "9c781593-1024-4911-a066-1e1598149376"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05482092872262001, "Height": 0.01495266892015934, "Left": 0.341703325510025, "Top": 0.8259204030036926}, "Polygon": [{"X": 0.341703325510025, "Y": 0.827936589717865}, {"X": 0.3960036635398865, "Y": 0.8259204030036926}, {"X": 0.39652425050735474, "Y": 0.8388569951057434}, {"X": 0.34226906299591064, "Y": 0.8408730626106262}]}, "Id": "bc9f4785-2343-40b2-9b47-0cde9f4a9da9"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01531685795634985, "Height": 0.013441907241940498, "Left": 0.4104309380054474, "Top": 0.8245274424552917}, "Polygon": [{"X": 0.4104309380054474, "Y": 0.8250777721405029}, {"X": 0.4252532124519348, "Y": 0.8245274424552917}, {"X": 0.4257477819919586, "Y": 0.8374190330505371}, {"X": 0.41093775629997253, "Y": 0.8379693627357483}]}, "Id": "4df0ecd8-7e15-4934-a2a0-e3259599a340"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "P02385", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08157550543546677, "Height": 0.015940601006150246, "Left": 0.5366358160972595, "Top": 0.8179303407669067}, "Polygon": [{"X": 0.5366358160972595, "Y": 0.8209468126296997}, {"X": 0.617875337600708, "Y": 0.8179303407669067}, {"X": 0.6182113289833069, "Y": 0.830854594707489}, {"X": 0.5370392203330994, "Y": 0.8338708877563477}]}, "Id": "6469031b-aef2-4fc0-96f4-46a2952c4781"}, {"BlockType": "WORD", "Confidence": 99.3126220703125, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12110332399606705, "Height": 0.01782366819679737, "Left": 0.34280648827552795, "Top": 0.8438926339149475}, "Polygon": [{"X": 0.34280648827552795, "Y": 0.8483769297599792}, {"X": 0.46343013644218445, "Y": 0.8438926339149475}, {"X": 0.4639098346233368, "Y": 0.8572323322296143}, {"X": 0.3433896601200104, "Y": 0.8617163300514221}]}, "Id": "65d46552-a89b-4835-9852-77561f6e81b3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10926497727632523, "Height": 0.017230810597538948, "Left": 0.4822540581226349, "Top": 0.8392885327339172}, "Polygon": [{"X": 0.4822540581226349, "Y": 0.8433369994163513}, {"X": 0.5911532640457153, "Y": 0.8392885327339172}, {"X": 0.5915189981460571, "Y": 0.8524711728096008}, {"X": 0.48271211981773376, "Y": 0.856519341468811}]}, "Id": "98aa6446-e748-4c96-b4ae-c04761be74b3"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054728832095861435, "Height": 0.015208445489406586, "Left": 0.3577660918235779, "Top": 0.8663145303726196}, "Polygon": [{"X": 0.3577660918235779, "Y": 0.8683323264122009}, {"X": 0.41197627782821655, "Y": 0.8663145303726196}, {"X": 0.4124949276447296, "Y": 0.8795053958892822}, {"X": 0.3583308160305023, "Y": 0.8815230131149292}]}, "Id": "43606e29-a4cf-431f-85f4-0cca00a1aa2e"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06490358710289001, "Height": 0.015272888354957104, "Left": 0.4275889992713928, "Top": 0.8633873462677002}, "Polygon": [{"X": 0.4275889992713928, "Y": 0.8657867908477783}, {"X": 0.49205276370048523, "Y": 0.8633873462677002}, {"X": 0.49249258637428284, "Y": 0.8762609958648682}, {"X": 0.4280822277069092, "Y": 0.878660261631012}]}, "Id": "cdae377a-4c10-4853-aa82-f5c381401dca"}, {"BlockType": "WORD", "Confidence": 98.97421264648438, "Text": "P02385", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0819639191031456, "Height": 0.015991825610399246, "Left": 0.5104948282241821, "Top": 0.8592377305030823}, "Polygon": [{"X": 0.5104948282241821, "Y": 0.8622750639915466}, {"X": 0.5920996069908142, "Y": 0.8592377305030823}, {"X": 0.5924587249755859, "Y": 0.8721923828125}, {"X": 0.5109220147132874, "Y": 0.87522953748703}]}, "Id": "e7c998ac-91fa-4aed-a759-c823299fc301"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "43787c9a-f022-445f-99a7-9a6bc2cf822e", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "43787c9a-f022-445f-99a7-9a6bc2cf822e", "content-type": "application/x-amz-json-1.1", "content-length": "41213", "date": "Wed, 24 Sep 2025 12:47:17 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "4a9d31e1-1c04-4ac7-a96f-c96488ef0b69", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:19 GMT", "content-type": "application/json", "content-length": "887", "connection": "keep-alive", "x-amzn-requestid": "4a9d31e1-1c04-4ac7-a96f-c96488ef0b69"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We need to use tool classify_logistics_doc_type. Determine doc_type.\n\nThe content seems like a fuel receipt? It says \"KWIK TRIP #674\", address, \"Invoice# 0091264\", Pump Quantity Price, amounts, totals, card number, auth. That looks like a fuel receipt from a fuel station (KWIK TRIP). So doc_type should be fuel_receipt. Confirm: keywords include Fuel, Diesel? It says Pump Quantity Price, maybe fuel purchase. So yes fuel_receipt.\n\nReturn array with single object: page_no 1, doc_type \"fuel_receipt\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2670, "outputTokens": 154, "totalTokens": 2824}, "metrics": {"latencyMs": 903}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/939deb05_OAJAO8BDLQUAQPD81DVE.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP #674\n6107 3M DR\nMENOMONIE, WI\nInvoice# 0091264\nTrans # 0900-1264\nDate\n10/14/2024\nTime\n04:31 PM\nPump Quantity Price\n04\n171.800 3.589\n04\n20.692 4.189\nProduct\nAmount\nDSL2\n616.59\nDEFBULK\n86.68\nTOTAL\n703.27\nNBS/KWIK TRIP FLEET\nCARD# ICR\nXXXXXXXXXXXXXX1591\nAUTH #\nP02385\nRESPONSE: APPROVED\nAUTH REF#: P02385\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}