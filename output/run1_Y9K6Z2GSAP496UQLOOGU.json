{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "8883ae44-1c6e-429d-93cc-706c3f99c959", "Relationships": [{"Type": "CHILD", "Ids": ["333450dd-ff5f-450d-95ae-8c2ebd24ba1c", "4781fc5b-7db1-4f8a-987f-5112b052ad1f", "9ed37969-6d4d-4fbb-b30e-39d466f387c9", "176af50e-6b52-457f-906d-c864f05d237a", "d64c6f7c-ee74-4e94-9e29-ef8a53e2c9f8", "f0682fa8-c288-4746-bec8-59884a319b10", "1a042069-7066-4741-8ef5-2564a0ba1d96", "013a78d5-d6c8-4c0d-b7e9-5958724b1fd8", "5e169041-f9d4-4d9b-8144-e351827970fa", "5ef5b22c-c2cc-49e2-9002-2d9adb75fc22", "90a2e8e5-4711-4ca2-b33e-9de1170704b0", "c608f149-ab16-4c23-bd52-619e43408356", "0b38c684-ac73-4718-ac6f-381f00d18647", "ac1a2045-c4ff-4a69-b8b2-fbcebf323617", "1cc8d244-56c4-4a2d-aa0a-253ea0d71295", "055b593e-058f-4a25-81f1-777d85880f8d", "ba061f0e-e79a-4e10-8b47-5507019911e8", "1da923dc-3441-4df2-beaf-ed5d74a470a5", "43288258-9765-4bfd-903e-4b4d08276ed6", "be430636-7e09-45a3-873c-6ad4fdb73ff7", "61ea7a02-7a44-40b0-b85f-ed8cb76f9ff0", "52a1012d-ab7c-4a7b-aea7-a14ebdcddd80", "2ded0afd-522c-458d-915c-c30aa4e6df92", "549e9cf2-12e2-40ab-abd0-953e89e9afb8", "8f7abd56-c89f-4ca2-9d8e-46203ca18dc5", "6f20c695-c18f-41b0-acaf-4e75c69108a2", "3660db2d-9053-4e94-9e0e-7be0d42451c3", "fb301436-ef2b-4670-985f-5834032b54d3", "591d085f-81ac-4603-872a-7d02013b1efd", "798164e6-3887-47e2-b3ef-c9312a659dcc"]}]}, {"BlockType": "LINE", "Confidence": 99.69792938232422, "Text": "TW SERVICES, INC.", "Geometry": {"BoundingBox": {"Width": 0.32163363695144653, "Height": 0.026715951040387154, "Left": 0.047720737755298615, "Top": 0.055184438824653625}, "Polygon": [{"X": 0.04772116243839264, "Y": 0.055184438824653625}, {"X": 0.36935439705848694, "Y": 0.05518588423728943}, {"X": 0.3693542182445526, "Y": 0.08190039545297623}, {"X": 0.047720737755298615, "Y": 0.08189918845891953}]}, "Id": "333450dd-ff5f-450d-95ae-8c2ebd24ba1c", "Relationships": [{"Type": "CHILD", "Ids": ["633c7b20-29b5-48aa-bc07-63e12a9b323f", "e41bcc62-85ce-4dc6-8d8c-15cdcdaf4ad1", "c7222a68-3a6a-4e6a-b230-55afb6bb2778"]}]}, {"BlockType": "LINE", "Confidence": 99.9373550415039, "Text": "Tax ID: 20-2895480", "Geometry": {"BoundingBox": {"Width": 0.3457292914390564, "Height": 0.019954975694417953, "Left": 0.5426713824272156, "Top": 0.06969808787107468}, "Polygon": [{"X": 0.5426713824272156, "Y": 0.06969808787107468}, {"X": 0.8884004950523376, "Y": 0.06969950348138809}, {"X": 0.8884006142616272, "Y": 0.08965306729078293}, {"X": 0.5426713824272156, "Y": 0.08965185284614563}]}, "Id": "4781fc5b-7db1-4f8a-987f-5112b052ad1f", "Relationships": [{"Type": "CHILD", "Ids": ["ef5b8b29-66b9-4107-ba88-069b258d67e1", "543ea9d5-9b52-4d1e-b70c-84d370730aba", "70872828-e381-4e29-bd59-5a7cda283fea"]}]}, {"BlockType": "LINE", "Confidence": 99.79332733154297, "Text": "www.twserviceinc.com", "Geometry": {"BoundingBox": {"Width": 0.38836154341697693, "Height": 0.03073960915207863, "Left": 0.04550982639193535, "Top": 0.08810437470674515}, "Polygon": [{"X": 0.045510318130254745, "Y": 0.08810437470674515}, {"X": 0.43387138843536377, "Y": 0.08810576051473618}, {"X": 0.43387123942375183, "Y": 0.11884398013353348}, {"X": 0.04550982639193535, "Y": 0.11884293705224991}]}, "Id": "9ed37969-6d4d-4fbb-b30e-39d466f387c9", "Relationships": [{"Type": "CHILD", "Ids": ["aef19043-86c6-495b-939d-386a75348bab"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "877-509-9064", "Geometry": {"BoundingBox": {"Width": 0.23160552978515625, "Height": 0.024458009749650955, "Left": 0.04899294674396515, "Top": 0.12196454405784607}, "Polygon": [{"X": 0.04899333789944649, "Y": 0.12196454405784607}, {"X": 0.2805984616279602, "Y": 0.12196514755487442}, {"X": 0.2805982530117035, "Y": 0.14642255008220673}, {"X": 0.04899294674396515, "Y": 0.1464221179485321}]}, "Id": "176af50e-6b52-457f-906d-c864f05d237a", "Relationships": [{"Type": "CHILD", "Ids": ["9887d417-8900-4411-a99e-ad8b59ded242"]}]}, {"BlockType": "LINE", "Confidence": 99.69617462158203, "Text": "RECEIPT#: *********", "Geometry": {"BoundingBox": {"Width": 0.3611105978488922, "Height": 0.028175776824355125, "Left": 0.04986490681767464, "Top": 0.18978258967399597}, "Polygon": [{"X": 0.04986535757780075, "Y": 0.18978258967399597}, {"X": 0.41097551584243774, "Y": 0.18978284299373627}, {"X": 0.4109753370285034, "Y": 0.21795833110809326}, {"X": 0.04986490681767464, "Y": 0.21795837581157684}]}, "Id": "d64c6f7c-ee74-4e94-9e29-ef8a53e2c9f8", "Relationships": [{"Type": "CHILD", "Ids": ["19953792-cc0c-40c2-8342-1f0f942e9ac0", "4bfd5ebb-fb6b-4508-9b35-a01dbd647dd2"]}]}, {"BlockType": "LINE", "Confidence": 99.64166259765625, "Text": "Date: 2025-09-20", "Geometry": {"BoundingBox": {"Width": 0.30617305636405945, "Height": 0.01855023205280304, "Left": 0.5437986254692078, "Top": 0.20384465157985687}, "Polygon": [{"X": 0.5437986850738525, "Y": 0.20384465157985687}, {"X": 0.8499715924263, "Y": 0.20384474098682404}, {"X": 0.8499717116355896, "Y": 0.22239480912685394}, {"X": 0.5437986254692078, "Y": 0.2223948836326599}]}, "Id": "f0682fa8-c288-4746-bec8-59884a319b10", "Relationships": [{"Type": "CHILD", "Ids": ["f065f6e8-5cef-4c85-8667-920b34685350", "14ef17bf-4688-4467-a94b-81f1d5c044c6"]}]}, {"BlockType": "LINE", "Confidence": 95.86199188232422, "Text": "Location: SSA - Phoenix", "Geometry": {"BoundingBox": {"Width": 0.4372941255569458, "Height": 0.029346561059355736, "Left": 0.05029292404651642, "Top": 0.22478711605072021}, "Polygon": [{"X": 0.050293389707803726, "Y": 0.22478725016117096}, {"X": 0.4875870645046234, "Y": 0.22478711605072021}, {"X": 0.48758694529533386, "Y": 0.2541331946849823}, {"X": 0.05029292404651642, "Y": 0.2541336715221405}]}, "Id": "1a042069-7066-4741-8ef5-2564a0ba1d96", "Relationships": [{"Type": "CHILD", "Ids": ["339c729f-f0e6-4a13-81b2-1cf2315caf64", "982ac7c3-e252-425b-a517-c4b594bb90ae", "e227da75-e9aa-4d78-afba-a914a3813f8f", "f6e7d489-2b8b-4cee-9f69-e5987bd6ea00"]}]}, {"BlockType": "LINE", "Confidence": 99.7510757446289, "Text": "Base Charge:", "Geometry": {"BoundingBox": {"Width": 0.22598423063755035, "Height": 0.025459693744778633, "Left": 0.04993583634495735, "Top": 0.29281818866729736}, "Polygon": [{"X": 0.049936242401599884, "Y": 0.29281869530677795}, {"X": 0.2759200632572174, "Y": 0.29281818866729736}, {"X": 0.2759198248386383, "Y": 0.3182772397994995}, {"X": 0.04993583634495735, "Y": 0.31827789545059204}]}, "Id": "013a78d5-d6c8-4c0d-b7e9-5958724b1fd8", "Relationships": [{"Type": "CHILD", "Ids": ["b89c3189-65d4-4881-84d3-ac8ec4d3b25d", "f5ee7fc5-bf1b-4977-bf91-023324bf12de"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$190.00", "Geometry": {"BoundingBox": {"Width": 0.13189244270324707, "Height": 0.02001805417239666, "Left": 0.47011739015579224, "Top": 0.30277588963508606}, "Polygon": [{"X": 0.4701174795627594, "Y": 0.3027762472629547}, {"X": 0.6020098328590393, "Y": 0.30277588963508606}, {"X": 0.6020098328590393, "Y": 0.32279354333877563}, {"X": 0.47011739015579224, "Y": 0.32279396057128906}]}, "Id": "5e169041-f9d4-4d9b-8144-e351827970fa", "Relationships": [{"Type": "CHILD", "Ids": ["a2f3a99a-adf1-4248-b822-9a94199fc59a"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Processing Fee:", "Geometry": {"BoundingBox": {"Width": 0.28236302733421326, "Height": 0.025327714160084724, "Left": 0.05064067617058754, "Top": 0.3262384235858917}, "Polygon": [{"X": 0.050641078501939774, "Y": 0.32623931765556335}, {"X": 0.3330036997795105, "Y": 0.3262384235858917}, {"X": 0.33300352096557617, "Y": 0.35156503319740295}, {"X": 0.05064067617058754, "Y": 0.3515661358833313}]}, "Id": "5ef5b22c-c2cc-49e2-9002-2d9adb75fc22", "Relationships": [{"Type": "CHILD", "Ids": ["681ca358-5bff-4ecb-8780-6fda0694a8f2", "6fe351c4-d7ca-440a-ab98-6a43aff5894d"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "$10.00", "Geometry": {"BoundingBox": {"Width": 0.11291342228651047, "Height": 0.019526407122612, "Left": 0.4699711501598358, "Top": 0.3365229070186615}, "Polygon": [{"X": 0.469971239566803, "Y": 0.33652329444885254}, {"X": 0.5828845500946045, "Y": 0.3365229070186615}, {"X": 0.5828845500946045, "Y": 0.3560488522052765}, {"X": 0.4699711501598358, "Y": 0.3560493290424347}]}, "Id": "90a2e8e5-4711-4ca2-b33e-9de1170704b0", "Relationships": [{"Type": "CHILD", "Ids": ["82c2a322-bc06-436e-82e0-1d4e3fa84774"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "Total Due:", "Geometry": {"BoundingBox": {"Width": 0.18825069069862366, "Height": 0.021491236984729767, "Left": 0.049323536455631256, "Top": 0.3599635064601898}, "Polygon": [{"X": 0.049323879182338715, "Y": 0.3599642813205719}, {"X": 0.2375742346048355, "Y": 0.3599635064601898}, {"X": 0.2375739961862564, "Y": 0.38145384192466736}, {"X": 0.049323536455631256, "Y": 0.381454735994339}]}, "Id": "c608f149-ab16-4c23-bd52-619e43408356", "Relationships": [{"Type": "CHILD", "Ids": ["84bd6e2a-4ecb-48c8-87fb-dbbd4be595e6", "53259114-4f6e-4aaa-97f4-2de7c4c14210"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$200.00", "Geometry": {"BoundingBox": {"Width": 0.13114576041698456, "Height": 0.019399888813495636, "Left": 0.470653235912323, "Top": 0.3702639043331146}, "Polygon": [{"X": 0.4706532955169678, "Y": 0.37026447057724}, {"X": 0.6017990112304688, "Y": 0.3702639043331146}, {"X": 0.6017990112304688, "Y": 0.38966313004493713}, {"X": 0.470653235912323, "Y": 0.38966378569602966}]}, "Id": "0b38c684-ac73-4718-ac6f-381f00d18647", "Relationships": [{"Type": "CHILD", "Ids": ["fee22eb4-1e68-4a05-aa00-e105c19c2ba3"]}]}, {"BlockType": "LINE", "Confidence": 99.39129638671875, "Text": "Payment: $200.00 MC ..0143", "Geometry": {"BoundingBox": {"Width": 0.511227011680603, "Height": 0.028103062883019447, "Left": 0.05077486112713814, "Top": 0.3946177065372467}, "Polygon": [{"X": 0.05077530816197395, "Y": 0.39462029933929443}, {"X": 0.5620018243789673, "Y": 0.3946177065372467}, {"X": 0.5620018243789673, "Y": 0.42271775007247925}, {"X": 0.05077486112713814, "Y": 0.4227207601070404}]}, "Id": "ac1a2045-c4ff-4a69-b8b2-fbcebf323617", "Relationships": [{"Type": "CHILD", "Ids": ["b85efdaa-febe-4ea6-a742-1b8b9cff00bd", "90f633ae-b2ac-4450-9d39-5e1d0a38a4ea", "c7a22a41-a0e5-4ffe-bc77-7002d2a84d2a", "b7bd47bb-a320-443c-9d3b-98b41b25c35c"]}]}, {"BlockType": "LINE", "Confidence": 99.8272705078125, "Text": "Carrier: RANGE TRUCKING", "Geometry": {"BoundingBox": {"Width": 0.43890997767448425, "Height": 0.025131145492196083, "Left": 0.050653066486120224, "Top": 0.4651417136192322}, "Polygon": [{"X": 0.05065346881747246, "Y": 0.4651448428630829}, {"X": 0.4895630478858948, "Y": 0.4651417136192322}, {"X": 0.4895629584789276, "Y": 0.4902694523334503}, {"X": 0.050653066486120224, "Y": 0.4902728796005249}]}, "Id": "1cc8d244-56c4-4a2d-aa0a-253ea0d71295", "Relationships": [{"Type": "CHILD", "Ids": ["13674912-33c5-4a2c-bf6e-a229f0a33404", "fc70f44f-0887-4ea5-901c-cd9f65a3250f", "508816b3-01cc-4770-aaae-9dba6c159664"]}]}, {"BlockType": "LINE", "Confidence": 99.29441833496094, "Text": "PO: 746753 AMERCAREROYAL-QDOBA", "Geometry": {"BoundingBox": {"Width": 0.5707408785820007, "Height": 0.027907101437449455, "Left": 0.05093880370259285, "Top": 0.4998694956302643}, "Polygon": [{"X": 0.05093924701213837, "Y": 0.499874085187912}, {"X": 0.6216797232627869, "Y": 0.4998694956302643}, {"X": 0.6216797232627869, "Y": 0.527771532535553}, {"X": 0.05093880370259285, "Y": 0.5277765989303589}]}, "Id": "055b593e-058f-4a25-81f1-777d85880f8d", "Relationships": [{"Type": "CHILD", "Ids": ["489bb732-f7d0-4ce6-bb31-0d03e80b16d2", "cc452ddc-7e60-41ec-908b-7e893a41a3b1", "6dc3e0ef-fae5-4b9d-b8ca-598faeb93a77"]}]}, {"BlockType": "LINE", "Confidence": 99.73778533935547, "Text": "Pallets Start: 14", "Geometry": {"BoundingBox": {"Width": 0.32781124114990234, "Height": 0.021972794085741043, "Left": 0.051554370671510696, "Top": 0.5336777567863464}, "Polygon": [{"X": 0.05155471712350845, "Y": 0.5336807370185852}, {"X": 0.37936562299728394, "Y": 0.5336777567863464}, {"X": 0.379365473985672, "Y": 0.5556473731994629}, {"X": 0.051554370671510696, "Y": 0.555650532245636}]}, "Id": "ba061f0e-e79a-4e10-8b47-5507019911e8", "Relationships": [{"Type": "CHILD", "Ids": ["c21b5cde-0082-4afb-ad41-eab3bc44fcd5", "405dee28-fa9d-41d9-b00a-6fb83564aefd", "d4d36429-83ec-4bc9-a97b-7de284675d3c"]}]}, {"BlockType": "LINE", "Confidence": 97.85448455810547, "Text": "- Finish: 19", "Geometry": {"BoundingBox": {"Width": 0.22691000998020172, "Height": 0.017952650785446167, "Left": 0.5683097243309021, "Top": 0.5406142473220825}, "Polygon": [{"X": 0.5683097839355469, "Y": 0.5406163334846497}, {"X": 0.7952196598052979, "Y": 0.5406142473220825}, {"X": 0.7952197790145874, "Y": 0.5585646629333496}, {"X": 0.5683097243309021, "Y": 0.5585668683052063}]}, "Id": "1da923dc-3441-4df2-beaf-ed5d74a470a5", "Relationships": [{"Type": "CHILD", "Ids": ["2a2b40f8-fed1-44ea-b983-3caa10a49ed6", "0796ad7b-8b6e-4554-9e29-e44297601627", "96d14129-72f7-401c-b515-ed15e9bc696d"]}]}, {"BlockType": "LINE", "Confidence": 96.71157836914062, "Text": "Cases: 272", "Geometry": {"BoundingBox": {"Width": 0.19246262311935425, "Height": 0.01841011457145214, "Left": 0.051748126745224, "Top": 0.5695822238922119}, "Polygon": [{"X": 0.05174841731786728, "Y": 0.5695841312408447}, {"X": 0.24421074986457825, "Y": 0.5695822238922119}, {"X": 0.24421055614948273, "Y": 0.5879902839660645}, {"X": 0.051748126745224, "Y": 0.5879923105239868}]}, "Id": "43288258-9765-4bfd-903e-4b4d08276ed6", "Relationships": [{"Type": "CHILD", "Ids": ["a5bc3109-66db-4b4e-b74b-a9349afd072c", "01963066-64a5-4250-af84-b0741946f02a"]}]}, {"BlockType": "LINE", "Confidence": 99.57659149169922, "Text": "Tractor: 0003", "Geometry": {"BoundingBox": {"Width": 0.25055739283561707, "Height": 0.01857582852244377, "Left": 0.05120306462049484, "Top": 0.6045538187026978}, "Polygon": [{"X": 0.05120335891842842, "Y": 0.6045565605163574}, {"X": 0.3017604649066925, "Y": 0.6045538187026978}, {"X": 0.3017602860927582, "Y": 0.623126745223999}, {"X": 0.05120306462049484, "Y": 0.6231296062469482}]}, "Id": "be430636-7e09-45a3-873c-6ad4fdb73ff7", "Relationships": [{"Type": "CHILD", "Ids": ["2218e163-e72a-41ee-8a5d-4f340a0c1ab9", "f7e6c11b-59de-424c-8e1f-a6134871b81f"]}]}, {"BlockType": "LINE", "Confidence": 99.69617462158203, "Text": "Trailer: 0016", "Geometry": {"BoundingBox": {"Width": 0.24660314619541168, "Height": 0.017848288640379906, "Left": 0.4720630943775177, "Top": 0.6076527833938599}, "Polygon": [{"X": 0.4720631539821625, "Y": 0.6076555252075195}, {"X": 0.7186661958694458, "Y": 0.6076527833938599}, {"X": 0.7186662554740906, "Y": 0.6254982352256775}, {"X": 0.4720630943775177, "Y": 0.6255010962486267}]}, "Id": "61ea7a02-7a44-40b0-b85f-ed8cb76f9ff0", "Relationships": [{"Type": "CHILD", "Ids": ["eddd7cb1-5679-4234-acbd-9ecc6f062fa1", "40a92285-605f-460b-906c-556ead93339f"]}]}, {"BlockType": "LINE", "Confidence": 99.66886901855469, "Text": "Load/Trip#: 16971955", "Geometry": {"BoundingBox": {"Width": 0.38044479489326477, "Height": 0.02288231812417507, "Left": 0.05361418426036835, "Top": 0.6394121050834656}, "Polygon": [{"X": 0.0536145456135273, "Y": 0.6394166350364685}, {"X": 0.4340589940547943, "Y": 0.6394121050834656}, {"X": 0.43405887484550476, "Y": 0.6622896194458008}, {"X": 0.05361418426036835, "Y": 0.6622943878173828}]}, "Id": "52a1012d-ab7c-4a7b-aea7-a14ebdcddd80", "Relationships": [{"Type": "CHILD", "Ids": ["0a17fe4a-afae-42a0-8319-34fa88f3703d", "b34b8d31-a61f-46fc-81ce-ee5c58ba9bd0"]}]}, {"BlockType": "LINE", "Confidence": 99.83079528808594, "Text": "Arrival Time: 07:02", "Geometry": {"BoundingBox": {"Width": 0.3832557797431946, "Height": 0.01877204142510891, "Left": 0.051000501960515976, "Top": 0.674353301525116}, "Polygon": [{"X": 0.05100079998373985, "Y": 0.6743583083152771}, {"X": 0.43425628542900085, "Y": 0.674353301525116}, {"X": 0.4342561960220337, "Y": 0.6931201815605164}, {"X": 0.051000501960515976, "Y": 0.6931253671646118}]}, "Id": "2ded0afd-522c-458d-915c-c30aa4e6df92", "Relationships": [{"Type": "CHILD", "Ids": ["0e29d970-8f8d-4742-937f-35e2ed00572a", "d45d8418-130e-4471-aaea-affc2265d548", "dbe26ce8-e81c-439e-b00b-ce9f4e4f97af"]}]}, {"BlockType": "LINE", "Confidence": 98.97161865234375, "Text": "Check-In: 07:05", "Geometry": {"BoundingBox": {"Width": 0.30702194571495056, "Height": 0.01820160262286663, "Left": 0.5484658479690552, "Top": 0.6757320761680603}, "Polygon": [{"X": 0.5484658479690552, "Y": 0.6757360696792603}, {"X": 0.8554876446723938, "Y": 0.6757320761680603}, {"X": 0.8554877638816833, "Y": 0.6939295530319214}, {"X": 0.5484658479690552, "Y": 0.6939336657524109}]}, "Id": "549e9cf2-12e2-40ab-abd0-953e89e9afb8", "Relationships": [{"Type": "CHILD", "Ids": ["55beac80-ab21-4c51-89db-80b7ea2d21b1", "953a9e72-0385-44f4-baff-ad604d579a24"]}]}, {"BlockType": "LINE", "Confidence": 98.98643493652344, "Text": "Unload Start: 07:09 - Finish: 08:15", "Geometry": {"BoundingBox": {"Width": 0.7436485886573792, "Height": 0.018178103491663933, "Left": 0.05290977656841278, "Top": 0.7093552947044373}, "Polygon": [{"X": 0.05291006714105606, "Y": 0.7093656659126282}, {"X": 0.7965582609176636, "Y": 0.7093552947044373}, {"X": 0.7965583801269531, "Y": 0.727522611618042}, {"X": 0.05290977656841278, "Y": 0.7275333404541016}]}, "Id": "8f7abd56-c89f-4ca2-9d8e-46203ca18dc5", "Relationships": [{"Type": "CHILD", "Ids": ["0ca6560d-c445-4ea7-9acd-6e6ec9743925", "bba55300-36ff-4aff-8a41-284a718f4fbc", "bb1dcc8b-429f-4cc6-b85f-b3542870fbc5", "c913e1dd-84e3-4ebc-bdbb-a41f23b2aaf4", "d1194b7b-9bdf-4687-8e34-59ca4fb4dd24", "04bb68a9-9581-41f2-af45-ee650c87e92d"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "Check Out: 08:36", "Geometry": {"BoundingBox": {"Width": 0.32764238119125366, "Height": 0.01827681064605713, "Left": 0.053372643887996674, "Top": 0.7436301112174988}, "Polygon": [{"X": 0.053372930735349655, "Y": 0.7436349987983704}, {"X": 0.38101500272750854, "Y": 0.7436301112174988}, {"X": 0.3810149133205414, "Y": 0.76190185546875}, {"X": 0.053372643887996674, "Y": 0.7619069218635559}]}, "Id": "6f20c695-c18f-41b0-acaf-4e75c69108a2", "Relationships": [{"Type": "CHILD", "Ids": ["e7ea9a71-a027-41ce-b5a8-6bc38d5574d1", "6b8e587b-a637-43a4-9175-bf76789f563b", "ebd52e89-ae65-498c-98db-ea349c678293"]}]}, {"BlockType": "LINE", "Confidence": 98.77099609375, "Text": "Gate Out: --:--", "Geometry": {"BoundingBox": {"Width": 0.30635881423950195, "Height": 0.017689907923340797, "Left": 0.5504456758499146, "Top": 0.7433445453643799}, "Polygon": [{"X": 0.5504457354545593, "Y": 0.7433491349220276}, {"X": 0.856804370880127, "Y": 0.7433445453643799}, {"X": 0.8568044900894165, "Y": 0.7610297203063965}, {"X": 0.5504456758499146, "Y": 0.7610344886779785}]}, "Id": "3660db2d-9053-4e94-9e0e-7be0d42451c3", "Relationships": [{"Type": "CHILD", "Ids": ["aa515938-cc71-4fca-b766-5ee89efc4ed5", "462cd44d-2bfa-455f-9538-d92de37a5120", "35ec02bc-462c-4caa-ab24-52f1122d832e"]}]}, {"BlockType": "LINE", "Confidence": 99.7559585571289, "Text": "Driver: HARPREET", "Geometry": {"BoundingBox": {"Width": 0.3101469576358795, "Height": 0.018236510455608368, "Left": 0.05351990833878517, "Top": 0.7787728309631348}, "Polygon": [{"X": 0.05352019518613815, "Y": 0.7787777185440063}, {"X": 0.3636668622493744, "Y": 0.7787728309631348}, {"X": 0.36366674304008484, "Y": 0.797004222869873}, {"X": 0.05351990833878517, "Y": 0.797009289264679}]}, "Id": "fb301436-ef2b-4670-985f-5834032b54d3", "Relationships": [{"Type": "CHILD", "Ids": ["7c05cee4-e400-4405-ba3f-f0bb61f97a02", "6bbaeff4-44a6-41b4-901a-bb9c9a7a0b96"]}]}, {"BlockType": "LINE", "Confidence": 99.44302368164062, "Text": "Door/Dock: 21 (GRO)", "Geometry": {"BoundingBox": {"Width": 0.3644624650478363, "Height": 0.02142050862312317, "Left": 0.05375288054347038, "Top": 0.8126194477081299}, "Polygon": [{"X": 0.053753215819597244, "Y": 0.8126255869865417}, {"X": 0.4182153344154358, "Y": 0.8126194477081299}, {"X": 0.41821521520614624, "Y": 0.8340335488319397}, {"X": 0.05375288054347038, "Y": 0.8340399265289307}]}, "Id": "591d085f-81ac-4603-872a-7d02013b1efd", "Relationships": [{"Type": "CHILD", "Ids": ["57348c42-b2cd-4ce5-9e7e-56d88cfc7b58", "26fdbaa7-8cab-496d-9e16-af9f1f8f67f5", "8c6bb515-2af9-4e71-bf78-d22a43ef5601"]}]}, {"BlockType": "LINE", "Confidence": 97.7784194946289, "Text": "Control#: 2902/1013013635/430869", "Geometry": {"BoundingBox": {"Width": 0.6120173335075378, "Height": 0.021676434203982353, "Left": 0.05431468412280083, "Top": 0.8451144695281982}, "Polygon": [{"X": 0.054315026849508286, "Y": 0.8451253771781921}, {"X": 0.666331946849823, "Y": 0.8451144695281982}, {"X": 0.6663320064544678, "Y": 0.866779625415802}, {"X": 0.05431468412280083, "Y": 0.8667908906936646}]}, "Id": "798164e6-3887-47e2-b3ef-c9312a659dcc", "Relationships": [{"Type": "CHILD", "Ids": ["f21b5851-8cbf-47dc-8bc0-c4de3075b01e", "39cb810e-517e-4537-b793-033f51856f0b"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TW", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04110307618975639, "Height": 0.016974996775388718, "Left": 0.04772089421749115, "Top": 0.055184438824653625}, "Polygon": [{"X": 0.04772116243839264, "Y": 0.055184438824653625}, {"X": 0.08882396668195724, "Y": 0.05518462508916855}, {"X": 0.08882371336221695, "Y": 0.07215943932533264}, {"X": 0.04772089421749115, "Y": 0.07215927541255951}]}, "Id": "633c7b20-29b5-48aa-bc07-63e12a9b323f"}, {"BlockType": "WORD", "Confidence": 99.48181915283203, "Text": "SERVICES,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16653801500797272, "Height": 0.02529851533472538, "Left": 0.10853254050016403, "Top": 0.056601524353027344}, "Polygon": [{"X": 0.10853289812803268, "Y": 0.056601524353027344}, {"X": 0.27507054805755615, "Y": 0.05660226568579674}, {"X": 0.27507030963897705, "Y": 0.08190003782510757}, {"X": 0.10853254050016403, "Y": 0.08189941942691803}]}, "Id": "e41bcc62-85ce-4dc6-8d8c-15cdcdaf4ad1"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06844380497932434, "Height": 0.018665550276637077, "Left": 0.3009105324745178, "Top": 0.06274262815713882}, "Polygon": [{"X": 0.30091068148612976, "Y": 0.06274262815713882}, {"X": 0.36935433745384216, "Y": 0.0627429261803627}, {"X": 0.3693542182445526, "Y": 0.08140818029642105}, {"X": 0.3009105324745178, "Y": 0.08140792697668076}]}, "Id": "c7222a68-3a6a-4e6a-b230-55afb6bb2778"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056283485144376755, "Height": 0.017160777002573013, "Left": 0.5426713824272156, "Top": 0.07161761820316315}, "Polygon": [{"X": 0.5426713824272156, "Y": 0.07161761820316315}, {"X": 0.5989548563957214, "Y": 0.07161784172058105}, {"X": 0.5989548563957214, "Y": 0.08877839148044586}, {"X": 0.5426713824272156, "Y": 0.08877819031476974}]}, "Id": "ef5b8b29-66b9-4107-ba88-069b258d67e1"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04809941351413727, "Height": 0.016410280019044876, "Left": 0.6206766963005066, "Top": 0.07295683771371841}, "Polygon": [{"X": 0.6206766963005066, "Y": 0.07295683771371841}, {"X": 0.6687760949134827, "Y": 0.07295703142881393}, {"X": 0.6687761545181274, "Y": 0.08936711400747299}, {"X": 0.6206766963005066, "Y": 0.08936694264411926}]}, "Id": "543ea9d5-9b52-4d1e-b70c-84d370730aba"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "20-2895480", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19414013624191284, "Height": 0.01995435729622841, "Left": 0.6942604780197144, "Top": 0.06969870626926422}, "Polygon": [{"X": 0.6942604780197144, "Y": 0.06969870626926422}, {"X": 0.8884004950523376, "Y": 0.06969950348138809}, {"X": 0.8884006142616272, "Y": 0.08965306729078293}, {"X": 0.6942605376243591, "Y": 0.08965238183736801}]}, "Id": "70872828-e381-4e29-bd59-5a7cda283fea"}, {"BlockType": "WORD", "Confidence": 99.79332733154297, "Text": "www.twserviceinc.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.38836154341697693, "Height": 0.03073960915207863, "Left": 0.04550982639193535, "Top": 0.08810437470674515}, "Polygon": [{"X": 0.045510318130254745, "Y": 0.08810437470674515}, {"X": 0.43387138843536377, "Y": 0.08810576051473618}, {"X": 0.43387123942375183, "Y": 0.11884398013353348}, {"X": 0.04550982639193535, "Y": 0.11884293705224991}]}, "Id": "aef19043-86c6-495b-939d-386a75348bab"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "877-509-9064", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23160552978515625, "Height": 0.024458009749650955, "Left": 0.04899294674396515, "Top": 0.12196454405784607}, "Polygon": [{"X": 0.04899333789944649, "Y": 0.12196454405784607}, {"X": 0.2805984616279602, "Y": 0.12196514755487442}, {"X": 0.2805982530117035, "Y": 0.14642255008220673}, {"X": 0.04899294674396515, "Y": 0.1464221179485321}]}, "Id": "9887d417-8900-4411-a99e-ad8b59ded242"}, {"BlockType": "WORD", "Confidence": 99.43219757080078, "Text": "RECEIPT#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16692104935646057, "Height": 0.022944044321775436, "Left": 0.0498649924993515, "Top": 0.18978258967399597}, "Polygon": [{"X": 0.04986535757780075, "Y": 0.18978258967399597}, {"X": 0.21678604185581207, "Y": 0.18978270888328552}, {"X": 0.21678578853607178, "Y": 0.2127266377210617}, {"X": 0.0498649924993515, "Y": 0.2127266377210617}]}, "Id": "19953792-cc0c-40c2-8342-1f0f942e9ac0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "*********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16904008388519287, "Height": 0.0214300025254488, "Left": 0.2419353872537613, "Top": 0.1965283453464508}, "Polygon": [{"X": 0.2419356107711792, "Y": 0.1965283453464508}, {"X": 0.41097545623779297, "Y": 0.19652843475341797}, {"X": 0.4109753370285034, "Y": 0.21795833110809326}, {"X": 0.2419353872537613, "Y": 0.21795836091041565}]}, "Id": "4bfd5ebb-fb6b-4508-9b35-a01dbd647dd2"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08728288114070892, "Height": 0.017601266503334045, "Left": 0.5437986254692078, "Top": 0.20442478358745575}, "Polygon": [{"X": 0.5437986850738525, "Y": 0.20442478358745575}, {"X": 0.6310815215110779, "Y": 0.20442481338977814}, {"X": 0.6310815215110779, "Y": 0.2220260351896286}, {"X": 0.5437986254692078, "Y": 0.2220260500907898}]}, "Id": "f065f6e8-5cef-4c85-8667-920b34685350"}, {"BlockType": "WORD", "Confidence": 99.40290832519531, "Text": "2025-09-20", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1936914175748825, "Height": 0.018550174310803413, "Left": 0.6562802791595459, "Top": 0.20384468138217926}, "Polygon": [{"X": 0.6562802791595459, "Y": 0.20384468138217926}, {"X": 0.8499715924263, "Y": 0.20384474098682404}, {"X": 0.8499717116355896, "Y": 0.22239480912685394}, {"X": 0.6562802791595459, "Y": 0.22239485383033752}]}, "Id": "14ef17bf-4688-4467-a94b-81f1d5c044c6"}, {"BlockType": "WORD", "Confidence": 99.1531753540039, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16671963036060333, "Height": 0.022435903549194336, "Left": 0.050293032079935074, "Top": 0.22478720545768738}, "Polygon": [{"X": 0.050293389707803726, "Y": 0.22478725016117096}, {"X": 0.2170126736164093, "Y": 0.22478720545768738}, {"X": 0.217012420296669, "Y": 0.24722294509410858}, {"X": 0.050293032079935074, "Y": 0.2472231090068817}]}, "Id": "339c729f-f0e6-4a13-81b2-1cf2315caf64"}, {"BlockType": "WORD", "Confidence": 98.78268432617188, "Text": "SSA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05769684910774231, "Height": 0.017729802057147026, "Left": 0.24408039450645447, "Top": 0.2314213365316391}, "Polygon": [{"X": 0.24408058822155, "Y": 0.2314213663339615}, {"X": 0.3017772436141968, "Y": 0.2314213365316391}, {"X": 0.30177709460258484, "Y": 0.2491510957479477}, {"X": 0.24408039450645447, "Y": 0.24915114045143127}]}, "Id": "982ac7c3-e252-425b-a517-c4b594bb90ae"}, {"BlockType": "WORD", "Confidence": 85.66097259521484, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013724486343562603, "Height": 0.004156091250479221, "Left": 0.3210250437259674, "Top": 0.24109390377998352}, "Polygon": [{"X": 0.3210251033306122, "Y": 0.24109390377998352}, {"X": 0.3347495496273041, "Y": 0.24109390377998352}, {"X": 0.3347495198249817, "Y": 0.24524998664855957}, {"X": 0.3210250437259674, "Y": 0.24524998664855957}]}, "Id": "e227da75-e9aa-4d78-afba-a914a3813f8f"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Phoenix", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13133321702480316, "Height": 0.02006305195391178, "Left": 0.35625380277633667, "Top": 0.234070286154747}, "Polygon": [{"X": 0.3562539517879486, "Y": 0.23407036066055298}, {"X": 0.487587034702301, "Y": 0.234070286154747}, {"X": 0.48758694529533386, "Y": 0.2541331946849823}, {"X": 0.35625380277633667, "Y": 0.25413334369659424}]}, "Id": "f6e7d489-2b8b-4cee-9f69-e5987bd6ea00"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Base", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07786745578050613, "Height": 0.018210511654615402, "Left": 0.049935951828956604, "Top": 0.29281851649284363}, "Polygon": [{"X": 0.049936242401599884, "Y": 0.29281869530677795}, {"X": 0.12780340015888214, "Y": 0.29281851649284363}, {"X": 0.12780316174030304, "Y": 0.3110288381576538}, {"X": 0.049935951828956604, "Y": 0.3110290467739105}]}, "Id": "b89c3189-65d4-4881-84d3-ac8ec4d3b25d"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1288200318813324, "Height": 0.02375306561589241, "Left": 0.14710001647472382, "Top": 0.29452455043792725}, "Polygon": [{"X": 0.1471003293991089, "Y": 0.29452481865882874}, {"X": 0.275920033454895, "Y": 0.29452455043792725}, {"X": 0.2759198248386383, "Y": 0.3182772397994995}, {"X": 0.14710001647472382, "Y": 0.31827759742736816}]}, "Id": "f5ee7fc5-bf1b-4977-bf91-023324bf12de"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$190.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13189244270324707, "Height": 0.02001805417239666, "Left": 0.47011739015579224, "Top": 0.30277588963508606}, "Polygon": [{"X": 0.4701174795627594, "Y": 0.3027762472629547}, {"X": 0.6020098328590393, "Y": 0.30277588963508606}, {"X": 0.6020098328590393, "Y": 0.32279354333877563}, {"X": 0.47011739015579224, "Y": 0.32279396057128906}]}, "Id": "a2f3a99a-adf1-4248-b822-9a94199fc59a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Processing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1932104527950287, "Height": 0.02532743290066719, "Left": 0.05064067617058754, "Top": 0.3262386918067932}, "Polygon": [{"X": 0.050641078501939774, "Y": 0.32623931765556335}, {"X": 0.24385112524032593, "Y": 0.3262386918067932}, {"X": 0.24385087192058563, "Y": 0.3515653908252716}, {"X": 0.05064067617058754, "Y": 0.3515661358833313}]}, "Id": "681ca358-5bff-4ecb-8780-6fda0694a8f2"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06857147812843323, "Height": 0.018396815285086632, "Left": 0.2644321918487549, "Top": 0.33209532499313354}, "Polygon": [{"X": 0.2644323706626892, "Y": 0.33209553360939026}, {"X": 0.3330036699771881, "Y": 0.33209532499313354}, {"X": 0.33300352096557617, "Y": 0.35049185156822205}, {"X": 0.2644321918487549, "Y": 0.35049211978912354}]}, "Id": "6fe351c4-d7ca-440a-ab98-6a43aff5894d"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "$10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11291342228651047, "Height": 0.019526407122612, "Left": 0.4699711501598358, "Top": 0.3365229070186615}, "Polygon": [{"X": 0.469971239566803, "Y": 0.33652329444885254}, {"X": 0.5828845500946045, "Y": 0.3365229070186615}, {"X": 0.5828845500946045, "Y": 0.3560488522052765}, {"X": 0.4699711501598358, "Y": 0.3560493290424347}]}, "Id": "82c2a322-bc06-436e-82e0-1d4e3fa84774"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09458088129758835, "Height": 0.019129589200019836, "Left": 0.04932357370853424, "Top": 0.35996389389038086}, "Polygon": [{"X": 0.049323879182338715, "Y": 0.3599642813205719}, {"X": 0.14390446245670319, "Y": 0.35996389389038086}, {"X": 0.1439042091369629, "Y": 0.3790930509567261}, {"X": 0.04932357370853424, "Y": 0.3790934681892395}]}, "Id": "84bd6e2a-4ecb-48c8-87fb-dbbd4be595e6"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Due:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0702938660979271, "Height": 0.018611643463373184, "Left": 0.16728033125400543, "Top": 0.36284253001213074}, "Polygon": [{"X": 0.16728056967258453, "Y": 0.3628428280353546}, {"X": 0.23757420480251312, "Y": 0.36284253001213074}, {"X": 0.2375739961862564, "Y": 0.38145384192466736}, {"X": 0.16728033125400543, "Y": 0.381454199552536}]}, "Id": "53259114-4f6e-4aaa-97f4-2de7c4c14210"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$200.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13114576041698456, "Height": 0.019399888813495636, "Left": 0.470653235912323, "Top": 0.3702639043331146}, "Polygon": [{"X": 0.4706532955169678, "Y": 0.37026447057724}, {"X": 0.6017990112304688, "Y": 0.3702639043331146}, {"X": 0.6017990112304688, "Y": 0.38966313004493713}, {"X": 0.470653235912323, "Y": 0.38966378569602966}]}, "Id": "fee22eb4-1e68-4a05-aa00-e105c19c2ba3"}, {"BlockType": "WORD", "Confidence": 99.04336547851562, "Text": "Payment:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14854790270328522, "Height": 0.022004345431923866, "Left": 0.0507749579846859, "Top": 0.39461955428123474}, "Polygon": [{"X": 0.05077530816197395, "Y": 0.39462029933929443}, {"X": 0.1993228644132614, "Y": 0.39461955428123474}, {"X": 0.19932261109352112, "Y": 0.4166230261325836}, {"X": 0.0507749579846859, "Y": 0.41662389039993286}]}, "Id": "b85efdaa-febe-4ea6-a742-1b8b9cff00bd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$200.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13211749494075775, "Height": 0.021917443722486496, "Left": 0.22578221559524536, "Top": 0.39693504571914673}, "Polygon": [{"X": 0.22578245401382446, "Y": 0.39693570137023926}, {"X": 0.3578996956348419, "Y": 0.39693504571914673}, {"X": 0.35789954662323, "Y": 0.41885170340538025}, {"X": 0.22578221559524536, "Y": 0.41885247826576233}]}, "Id": "90f633ae-b2ac-4450-9d39-5e1d0a38a4ea"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "MC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03698606416583061, "Height": 0.017798133194446564, "Left": 0.3955855965614319, "Top": 0.40319469571113586}, "Polygon": [{"X": 0.39558571577072144, "Y": 0.4031948745250702}, {"X": 0.432571679353714, "Y": 0.40319469571113586}, {"X": 0.4325715899467468, "Y": 0.4209926128387451}, {"X": 0.3955855965614319, "Y": 0.42099282145500183}]}, "Id": "c7a22a41-a0e5-4ffe-bc77-7002d2a84d2a"}, {"BlockType": "WORD", "Confidence": 98.68861389160156, "Text": "..0143", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10605386644601822, "Height": 0.017393773421645164, "Left": 0.45594796538352966, "Top": 0.4053246080875397}, "Polygon": [{"X": 0.4559480547904968, "Y": 0.40532517433166504}, {"X": 0.5620018243789673, "Y": 0.4053246080875397}, {"X": 0.5620018243789673, "Y": 0.42271775007247925}, {"X": 0.45594796538352966, "Y": 0.4227183759212494}]}, "Id": "b7bd47bb-a320-443c-9d3b-98b41b25c35c"}, {"BlockType": "WORD", "Confidence": 99.48181915283203, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14895698428153992, "Height": 0.019622301682829857, "Left": 0.05065315589308739, "Top": 0.46514376997947693}, "Polygon": [{"X": 0.05065346881747246, "Y": 0.4651448428630829}, {"X": 0.1996101438999176, "Y": 0.46514376997947693}, {"X": 0.1996099054813385, "Y": 0.4847649335861206}, {"X": 0.05065315589308739, "Y": 0.48476606607437134}]}, "Id": "13674912-33c5-4a2c-bf6e-a229f0a33404"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RANGE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09217439591884613, "Height": 0.01858733408153057, "Left": 0.22719904780387878, "Top": 0.4683372974395752}, "Polygon": [{"X": 0.2271992415189743, "Y": 0.4683379530906677}, {"X": 0.3193734288215637, "Y": 0.4683372974395752}, {"X": 0.3193732798099518, "Y": 0.4869239032268524}, {"X": 0.22719904780387878, "Y": 0.4869246184825897}]}, "Id": "fc70f44f-0887-4ea5-901c-cd9f65a3250f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRUCKING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15060198307037354, "Height": 0.01959671452641487, "Left": 0.33896103501319885, "Top": 0.47067391872406006}, "Polygon": [{"X": 0.3389611840248108, "Y": 0.470674991607666}, {"X": 0.4895630180835724, "Y": 0.47067391872406006}, {"X": 0.4895629584789276, "Y": 0.4902694523334503}, {"X": 0.33896103501319885, "Y": 0.49027061462402344}]}, "Id": "508816b3-01cc-4770-aaae-9dba6c159664"}, {"BlockType": "WORD", "Confidence": 99.20280456542969, "Text": "PO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05213037133216858, "Height": 0.017680905759334564, "Left": 0.05093896761536598, "Top": 0.49987366795539856}, "Polygon": [{"X": 0.05093924701213837, "Y": 0.499874085187912}, {"X": 0.10306934267282486, "Y": 0.49987366795539856}, {"X": 0.10306908190250397, "Y": 0.5175541043281555}, {"X": 0.05093896761536598, "Y": 0.5175545811653137}]}, "Id": "489bb732-f7d0-4ce6-bb31-0d03e80b16d2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "746753", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11249139159917831, "Height": 0.01805735193192959, "Left": 0.13054342567920685, "Top": 0.5009898543357849}, "Polygon": [{"X": 0.13054366409778595, "Y": 0.5009907484054565}, {"X": 0.24303480982780457, "Y": 0.5009898543357849}, {"X": 0.24303463101387024, "Y": 0.5190462470054626}, {"X": 0.13054342567920685, "Y": 0.519047200679779}]}, "Id": "cc452ddc-7e60-41ec-908b-7e893a41a3b1"}, {"BlockType": "WORD", "Confidence": 98.6804428100586, "Text": "AMERCAREROYAL-QDOBA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.35975074768066406, "Height": 0.02492646686732769, "Left": 0.2619289457798004, "Top": 0.5028482675552368}, "Polygon": [{"X": 0.2619291841983795, "Y": 0.5028511881828308}, {"X": 0.6216797232627869, "Y": 0.5028482675552368}, {"X": 0.6216797232627869, "Y": 0.527771532535553}, {"X": 0.2619289457798004, "Y": 0.5277746915817261}]}, "Id": "6dc3e0ef-fae5-4b9d-b8ca-598faeb93a77"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13408026099205017, "Height": 0.019206546247005463, "Left": 0.05155441537499428, "Top": 0.5336794853210449}, "Polygon": [{"X": 0.05155471712350845, "Y": 0.5336807370185852}, {"X": 0.18563467264175415, "Y": 0.5336794853210449}, {"X": 0.18563443422317505, "Y": 0.5528847575187683}, {"X": 0.05155441537499428, "Y": 0.5528860688209534}]}, "Id": "c21b5cde-0082-4afb-ad41-eab3bc44fcd5"}, {"BlockType": "WORD", "Confidence": 99.48181915283203, "Text": "Start:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10666556656360626, "Height": 0.01818765141069889, "Left": 0.20996679365634918, "Top": 0.5366290807723999}, "Polygon": [{"X": 0.2099669873714447, "Y": 0.5366300344467163}, {"X": 0.31663236021995544, "Y": 0.5366290807723999}, {"X": 0.3166322112083435, "Y": 0.5548157095909119}, {"X": 0.20996679365634918, "Y": 0.554816722869873}]}, "Id": "405dee28-fa9d-41d9-b00a-6fb83564aefd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03615035116672516, "Height": 0.016981983557343483, "Left": 0.3432152271270752, "Top": 0.538665771484375}, "Polygon": [{"X": 0.34321537613868713, "Y": 0.5386660695075989}, {"X": 0.37936559319496155, "Y": 0.538665771484375}, {"X": 0.379365473985672, "Y": 0.5556473731994629}, {"X": 0.3432152271270752, "Y": 0.5556477308273315}]}, "Id": "d4d36429-83ec-4bc9-a97b-7de284675d3c"}, {"BlockType": "WORD", "Confidence": 93.99134826660156, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014944470487535, "Height": 0.003874130081385374, "Left": 0.5683097243309021, "Top": 0.5486342310905457}, "Polygon": [{"X": 0.5683097839355469, "Y": 0.5486343502998352}, {"X": 0.5832542181015015, "Y": 0.5486342310905457}, {"X": 0.5832542181015015, "Y": 0.5525081753730774}, {"X": 0.5683097243309021, "Y": 0.5525083541870117}]}, "Id": "2a2b40f8-fed1-44ea-b983-3caa10a49ed6"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "Finish:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12614278495311737, "Height": 0.017951704561710358, "Left": 0.6047829389572144, "Top": 0.5406148433685303}, "Polygon": [{"X": 0.6047829389572144, "Y": 0.540615975856781}, {"X": 0.7309256792068481, "Y": 0.5406148433685303}, {"X": 0.7309257388114929, "Y": 0.5585653185844421}, {"X": 0.6047829389572144, "Y": 0.5585665106773376}]}, "Id": "0796ad7b-8b6e-4554-9e29-e44297601627"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "19", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035615451633930206, "Height": 0.016635440289974213, "Left": 0.759604275226593, "Top": 0.5414173603057861}, "Polygon": [{"X": 0.759604275226593, "Y": 0.54141765832901}, {"X": 0.7952196598052979, "Y": 0.5414173603057861}, {"X": 0.7952197194099426, "Y": 0.5580524206161499}, {"X": 0.7596043944358826, "Y": 0.5580527782440186}]}, "Id": "96d14129-72f7-401c-b515-ed15e9bc696d"}, {"BlockType": "WORD", "Confidence": 93.42314910888672, "Text": "Cases:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11077457666397095, "Height": 0.017735140398144722, "Left": 0.051748137921094894, "Top": 0.5695830583572388}, "Polygon": [{"X": 0.05174841731786728, "Y": 0.5695841312408447}, {"X": 0.16252270340919495, "Y": 0.5695830583572388}, {"X": 0.16252247989177704, "Y": 0.5873169898986816}, {"X": 0.051748137921094894, "Y": 0.5873181819915771}]}, "Id": "a5bc3109-66db-4b4e-b74b-a9349afd072c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "272", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05571427941322327, "Height": 0.01713278889656067, "Left": 0.1884964555501938, "Top": 0.5708581209182739}, "Polygon": [{"X": 0.1884966641664505, "Y": 0.5708586573600769}, {"X": 0.24421073496341705, "Y": 0.5708581209182739}, {"X": 0.24421055614948273, "Y": 0.5879902839660645}, {"X": 0.1884964555501938, "Y": 0.5879908800125122}]}, "Id": "01963066-64a5-4250-af84-b0741946f02a"}, {"BlockType": "WORD", "Confidence": 99.19303894042969, "Text": "Tractor:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15045051276683807, "Height": 0.018194161355495453, "Left": 0.05120307207107544, "Top": 0.6045548915863037}, "Polygon": [{"X": 0.05120335891842842, "Y": 0.6045565605163574}, {"X": 0.2016535848379135, "Y": 0.6045548915863037}, {"X": 0.2016533762216568, "Y": 0.6227473020553589}, {"X": 0.05120307207107544, "Y": 0.6227490305900574}]}, "Id": "2218e163-e72a-41ee-8a5d-4f340a0c1ab9"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "0003", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07531165331602097, "Height": 0.016932843253016472, "Left": 0.22644878923892975, "Top": 0.6061947345733643}, "Polygon": [{"X": 0.22644896805286407, "Y": 0.6061955690383911}, {"X": 0.3017604351043701, "Y": 0.6061947345733643}, {"X": 0.3017602860927582, "Y": 0.623126745223999}, {"X": 0.22644878923892975, "Y": 0.6231275796890259}]}, "Id": "f7e6c11b-59de-424c-8e1f-a6134871b81f"}, {"BlockType": "WORD", "Confidence": 99.43219757080078, "Text": "Trailer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1447954624891281, "Height": 0.017847158014774323, "Left": 0.4720630943775177, "Top": 0.6076539158821106}, "Polygon": [{"X": 0.4720631539821625, "Y": 0.6076555252075195}, {"X": 0.6168585419654846, "Y": 0.6076539158821106}, {"X": 0.6168585419654846, "Y": 0.625499427318573}, {"X": 0.4720630943775177, "Y": 0.6255010962486267}]}, "Id": "eddd7cb1-5679-4234-acbd-9ecc6f062fa1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "0016", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07748058438301086, "Height": 0.016036007553339005, "Left": 0.6411856412887573, "Top": 0.6094552874565125}, "Polygon": [{"X": 0.6411856412887573, "Y": 0.6094561219215393}, {"X": 0.7186661958694458, "Y": 0.6094552874565125}, {"X": 0.7186662554740906, "Y": 0.6254903674125671}, {"X": 0.6411856412887573, "Y": 0.6254912614822388}]}, "Id": "40a92285-605f-460b-906c-556ead93339f"}, {"BlockType": "WORD", "Confidence": 99.33773040771484, "Text": "Load/Trip#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20721091330051422, "Height": 0.02288023941218853, "Left": 0.05361418426036835, "Top": 0.6394141912460327}, "Polygon": [{"X": 0.0536145456135273, "Y": 0.6394166350364685}, {"X": 0.26082509756088257, "Y": 0.6394141912460327}, {"X": 0.26082485914230347, "Y": 0.6622917652130127}, {"X": 0.05361418426036835, "Y": 0.6622943878173828}]}, "Id": "0a17fe4a-afae-42a0-8319-34fa88f3703d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "16971955", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14682316780090332, "Height": 0.01779036968946457, "Left": 0.287235826253891, "Top": 0.6413296461105347}, "Polygon": [{"X": 0.28723597526550293, "Y": 0.6413314342498779}, {"X": 0.4340589940547943, "Y": 0.6413296461105347}, {"X": 0.43405890464782715, "Y": 0.6591181755065918}, {"X": 0.287235826253891, "Y": 0.6591200232505798}]}, "Id": "b34b8d31-a61f-46fc-81ce-ee5c58ba9bd0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Arrival", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13483911752700806, "Height": 0.018578175455331802, "Left": 0.051000505685806274, "Top": 0.6743565201759338}, "Polygon": [{"X": 0.05100079998373985, "Y": 0.6743583083152771}, {"X": 0.18583962321281433, "Y": 0.6743565201759338}, {"X": 0.18583939969539642, "Y": 0.6929329037666321}, {"X": 0.051000505685806274, "Y": 0.6929346919059753}]}, "Id": "0e29d970-8f8d-4742-937f-35e2ed00572a"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09071186929941177, "Height": 0.017858918756246567, "Left": 0.20792001485824585, "Top": 0.6752157211303711}, "Polygon": [{"X": 0.20792020857334137, "Y": 0.6752169132232666}, {"X": 0.298631876707077, "Y": 0.6752157211303711}, {"X": 0.2986317276954651, "Y": 0.6930734515190125}, {"X": 0.20792001485824585, "Y": 0.693074643611908}]}, "Id": "d45d8418-130e-4471-aaea-affc2265d548"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "07:02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09333480894565582, "Height": 0.016937877982854843, "Left": 0.34092146158218384, "Top": 0.6761835813522339}, "Polygon": [{"X": 0.3409215807914734, "Y": 0.6761847734451294}, {"X": 0.43425628542900085, "Y": 0.6761835813522339}, {"X": 0.4342561960220337, "Y": 0.6931201815605164}, {"X": 0.34092146158218384, "Y": 0.6931214332580566}]}, "Id": "dbe26ce8-e81c-439e-b00b-ce9f4e4f97af"}, {"BlockType": "WORD", "Confidence": 98.01239013671875, "Text": "Check-In:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16501352190971375, "Height": 0.018199753016233444, "Left": 0.5484658479690552, "Top": 0.6757339239120483}, "Polygon": [{"X": 0.5484658479690552, "Y": 0.6757360696792603}, {"X": 0.7134792804718018, "Y": 0.6757339239120483}, {"X": 0.7134793400764465, "Y": 0.6939314603805542}, {"X": 0.5484658479690552, "Y": 0.6939336657524109}]}, "Id": "55beac80-ab21-4c51-89db-80b7ea2d21b1"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "07:05", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09770196676254272, "Height": 0.016841206699609756, "Left": 0.7577857971191406, "Top": 0.6770631074905396}, "Polygon": [{"X": 0.7577857971191406, "Y": 0.6770643591880798}, {"X": 0.8554876446723938, "Y": 0.6770631074905396}, {"X": 0.8554877638816833, "Y": 0.6939029693603516}, {"X": 0.7577858567237854, "Y": 0.6939043402671814}]}, "Id": "953a9e72-0385-44f4-baff-ad604d579a24"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Unload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11746261268854141, "Height": 0.01812647469341755, "Left": 0.05290977656841278, "Top": 0.7094069123268127}, "Polygon": [{"X": 0.05291006341576576, "Y": 0.7094085216522217}, {"X": 0.1703723818063736, "Y": 0.7094069123268127}, {"X": 0.1703721582889557, "Y": 0.7275316715240479}, {"X": 0.05290977656841278, "Y": 0.7275333404541016}]}, "Id": "0ca6560d-c445-4ea7-9acd-6e6ec9743925"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Start:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10705429315567017, "Height": 0.01664039120078087, "Left": 0.191558837890625, "Top": 0.710442841053009}, "Polygon": [{"X": 0.19155903160572052, "Y": 0.7104443311691284}, {"X": 0.29861313104629517, "Y": 0.710442841053009}, {"X": 0.2986129820346832, "Y": 0.7270816564559937}, {"X": 0.191558837890625, "Y": 0.7270832061767578}]}, "Id": "bba55300-36ff-4aff-8a41-284a718f4fbc"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "07:09", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09489184617996216, "Height": 0.016977688297629356, "Left": 0.3415014147758484, "Top": 0.7099615335464478}, "Polygon": [{"X": 0.34150153398513794, "Y": 0.7099628448486328}, {"X": 0.43639326095581055, "Y": 0.7099615335464478}, {"X": 0.4363931715488434, "Y": 0.7269378304481506}, {"X": 0.3415014147758484, "Y": 0.7269392013549805}]}, "Id": "bb1dcc8b-429f-4cc6-b85f-b3542870fbc5"}, {"BlockType": "WORD", "Confidence": 95.2088623046875, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014178406447172165, "Height": 0.0035895640030503273, "Left": 0.4950065612792969, "Top": 0.7176173329353333}, "Polygon": [{"X": 0.49500659108161926, "Y": 0.7176175117492676}, {"X": 0.5091849565505981, "Y": 0.7176173329353333}, {"X": 0.5091849565505981, "Y": 0.7212066650390625}, {"X": 0.4950065612792969, "Y": 0.7212069034576416}]}, "Id": "c913e1dd-84e3-4ebc-bdbb-a41f23b2aaf4"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Finish:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12394943088293076, "Height": 0.017469292506575584, "Left": 0.5322257280349731, "Top": 0.7093572020530701}, "Polygon": [{"X": 0.5322257876396179, "Y": 0.7093589305877686}, {"X": 0.6561751365661621, "Y": 0.7093572020530701}, {"X": 0.6561751961708069, "Y": 0.7268247008323669}, {"X": 0.5322257280349731, "Y": 0.726826548576355}]}, "Id": "d1194b7b-9bdf-4687-8e34-59ca4fb4dd24"}, {"BlockType": "WORD", "Confidence": 99.31501007080078, "Text": "08:15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09674263745546341, "Height": 0.017003370448946953, "Left": 0.6998157501220703, "Top": 0.7101282477378845}, "Polygon": [{"X": 0.6998157501220703, "Y": 0.7101295590400696}, {"X": 0.7965582609176636, "Y": 0.7101282477378845}, {"X": 0.7965583801269531, "Y": 0.7271301746368408}, {"X": 0.6998158097267151, "Y": 0.7271316051483154}]}, "Id": "04bb68a9-9581-41f2-af45-ee650c87e92d"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Check", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09831453859806061, "Height": 0.018273385241627693, "Left": 0.053372643887996674, "Top": 0.7436335682868958}, "Polygon": [{"X": 0.053372930735349655, "Y": 0.7436349987983704}, {"X": 0.15168718993663788, "Y": 0.7436335682868958}, {"X": 0.15168695151805878, "Y": 0.7619054317474365}, {"X": 0.053372643887996674, "Y": 0.7619069218635559}]}, "Id": "e7ea9a71-a027-41ce-b5a8-6bc38d5574d1"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Out:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07016905397176743, "Height": 0.016956765204668045, "Left": 0.1711433380842209, "Top": 0.7443905472755432}, "Polygon": [{"X": 0.1711435467004776, "Y": 0.7443916201591492}, {"X": 0.2413123995065689, "Y": 0.7443905472755432}, {"X": 0.24131222069263458, "Y": 0.7613462209701538}, {"X": 0.1711433380842209, "Y": 0.7613472938537598}]}, "Id": "6b8e587b-a637-43a4-9175-bf76789f563b"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "08:36", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09578915685415268, "Height": 0.017011528834700584, "Left": 0.28522586822509766, "Top": 0.7439334392547607}, "Polygon": [{"X": 0.2852260172367096, "Y": 0.7439348697662354}, {"X": 0.38101500272750854, "Y": 0.7439334392547607}, {"X": 0.3810149133205414, "Y": 0.7609434723854065}, {"X": 0.28522586822509766, "Y": 0.7609449625015259}]}, "Id": "ebd52e89-ae65-498c-98db-ea349c678293"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07473348826169968, "Height": 0.017110256478190422, "Left": 0.5504456758499146, "Top": 0.7433480024337769}, "Polygon": [{"X": 0.5504457354545593, "Y": 0.7433491349220276}, {"X": 0.6251791715621948, "Y": 0.7433480024337769}, {"X": 0.6251791715621948, "Y": 0.7604570984840393}, {"X": 0.5504456758499146, "Y": 0.7604582905769348}]}, "Id": "aa515938-cc71-4fca-b766-5ee89efc4ed5"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Out:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0701037123799324, "Height": 0.01686999201774597, "Left": 0.6445141434669495, "Top": 0.7436503767967224}, "Polygon": [{"X": 0.6445141434669495, "Y": 0.7436514496803284}, {"X": 0.7146178483963013, "Y": 0.7436503767967224}, {"X": 0.714617908000946, "Y": 0.7605193257331848}, {"X": 0.6445142030715942, "Y": 0.7605203986167908}]}, "Id": "462cd44d-2bfa-455f-9538-d92de37a5120"}, {"BlockType": "WORD", "Confidence": 96.47241973876953, "Text": "--:--", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09415200352668762, "Height": 0.0128254946321249, "Left": 0.7626525163650513, "Top": 0.7482057213783264}, "Polygon": [{"X": 0.7626525163650513, "Y": 0.7482070922851562}, {"X": 0.8568044304847717, "Y": 0.7482057213783264}, {"X": 0.8568044900894165, "Y": 0.7610297203063965}, {"X": 0.762652575969696, "Y": 0.7610312104225159}]}, "Id": "35ec02bc-462c-4caa-ab24-52f1122d832e"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "Driver:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13027845323085785, "Height": 0.01812305673956871, "Left": 0.05351990833878517, "Top": 0.7788862586021423}, "Polygon": [{"X": 0.05352019518613815, "Y": 0.7788883447647095}, {"X": 0.18379835784435272, "Y": 0.7788862586021423}, {"X": 0.183798149228096, "Y": 0.797007143497467}, {"X": 0.05351990833878517, "Y": 0.797009289264679}]}, "Id": "7c05cee4-e400-4405-ba3f-f0bb61f97a02"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "HARPREET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1531127542257309, "Height": 0.01744932308793068, "Left": 0.2105541080236435, "Top": 0.7787728309631348}, "Polygon": [{"X": 0.210554301738739, "Y": 0.7787752151489258}, {"X": 0.3636668622493744, "Y": 0.7787728309631348}, {"X": 0.36366674304008484, "Y": 0.7962195873260498}, {"X": 0.2105541080236435, "Y": 0.7962221503257751}]}, "Id": "6bbaeff4-44a6-41b4-901a-bb9c9a7a0b96"}, {"BlockType": "WORD", "Confidence": 99.59402465820312, "Text": "Door/Dock:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.189228355884552, "Height": 0.018858598545193672, "Left": 0.05375290289521217, "Top": 0.8135925531387329}, "Polygon": [{"X": 0.05375320091843605, "Y": 0.8135957717895508}, {"X": 0.24298125505447388, "Y": 0.8135925531387329}, {"X": 0.24298106133937836, "Y": 0.83244788646698}, {"X": 0.05375290289521217, "Y": 0.8324511647224426}]}, "Id": "57348c42-b2cd-4ce5-9e7e-56d88cfc7b58"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "21", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038808587938547134, "Height": 0.016692621633410454, "Left": 0.26860731840133667, "Top": 0.8140354752540588}, "Polygon": [{"X": 0.2686074674129486, "Y": 0.8140361309051514}, {"X": 0.3074159026145935, "Y": 0.8140354752540588}, {"X": 0.30741575360298157, "Y": 0.8307273983955383}, {"X": 0.26860731840133667, "Y": 0.8307281136512756}]}, "Id": "26fdbaa7-8cab-496d-9e16-af9f1f8f67f5"}, {"BlockType": "WORD", "Confidence": 98.81477355957031, "Text": "(GRO)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09202498197555542, "Height": 0.021415742114186287, "Left": 0.32619035243988037, "Top": 0.8126194477081299}, "Polygon": [{"X": 0.3261905312538147, "Y": 0.812620997428894}, {"X": 0.4182153344154358, "Y": 0.8126194477081299}, {"X": 0.41821521520614624, "Y": 0.8340335488319397}, {"X": 0.32619035243988037, "Y": 0.8340351581573486}]}, "Id": "8c6bb515-2af9-4e71-bf78-d22a43ef5601"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Control#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16954082250595093, "Height": 0.0182406697422266, "Left": 0.05431468412280083, "Top": 0.8485502004623413}, "Polygon": [{"X": 0.05431497469544411, "Y": 0.8485532402992249}, {"X": 0.22385549545288086, "Y": 0.8485502004623413}, {"X": 0.22385530173778534, "Y": 0.8667877912521362}, {"X": 0.05431468412280083, "Y": 0.8667908906936646}]}, "Id": "f21b5851-8cbf-47dc-8bc0-c4de3075b01e"}, {"BlockType": "WORD", "Confidence": 95.8252944946289, "Text": "2902/1013013635/430869", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.41707101464271545, "Height": 0.021165991201996803, "Left": 0.24926096200942993, "Top": 0.8451144695281982}, "Polygon": [{"X": 0.24926118552684784, "Y": 0.8451219201087952}, {"X": 0.666331946849823, "Y": 0.8451144695281982}, {"X": 0.6663320064544678, "Y": 0.8662728071212769}, {"X": 0.24926096200942993, "Y": 0.8662804365158081}]}, "Id": "39cb810e-517e-4537-b793-033f51856f0b"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1e0b3bdf-4a2b-412d-af39-509a64bb33aa", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1e0b3bdf-4a2b-412d-af39-509a64bb33aa", "content-type": "application/x-amz-json-1.1", "content-length": "54410", "date": "Wed, 24 Sep 2025 13:26:50 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ce9fc411-8601-43ec-aff6-b9092b9d73b3", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:53 GMT", "content-type": "application/json", "content-length": "1360", "connection": "keep-alive", "x-amzn-requestid": "ce9fc411-8601-43ec-aff6-b9092b9d73b3"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We should decide doc_type. Text suggests receipt. Contains receipt#, base charge, total due, payment. Also PO, driver, etc. This looks like \"lumper receipt\" or \"invoice\". Let's differentiate.\n\nLumper receipt: Definition: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page includes PO, door/dock, driver, pallets start/finish, base charge, processing fee, total due. So lumper receipt likely. Also invoice: Bill issued by a carrier for goods; but includes amounts but not goods details? This includes truck details. Lumper receipt matches. So doc_type: lumper_receipt.\n\nCheck if any other doc type fits: invoice. But invoice vs lumper receipt: lumper receipt has \"service charge\" like base charge, processing fee. This is a service receipt. So lumper_receipt.\n\nThus output JSON with page 1."}}}, {"text": "{\n  \"documents\": [\n    {\n      \"page_no\": 1,\n      \"doc_type\": \"lumper_receipt\"\n    }\n  ]\n}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2816, "outputTokens": 252, "totalTokens": 3068}, "metrics": {"latencyMs": 1174}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/c846733f_Y9K6Z2GSAP496UQLOOGU.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nTW SERVICES, INC.\nTax ID: 20-2895480\nwww.twserviceinc.com\n877-509-9064\nRECEIPT#: *********\nDate: 2025-09-20\nLocation: SSA - Phoenix\nBase Charge:\n$190.00\nProcessing Fee:\n$10.00\nTotal Due:\n$200.00\nPayment: $200.00 MC ..0143\nCarrier: RANGE TRUCKING\nPO: 746753 AMERCAREROYAL-QDOBA\nPallets Start: 14\n- Finish: 19\nCases: 272\nTractor: 0003\nTrailer: 0016\nLoad/Trip#: 16971955\nArrival Time: 07:02\nCheck-In: 07:05\nUnload Start: 07:09 - Finish: 08:15\nCheck Out: 08:36\nGate Out: --:--\nDriver: HARPREET\nDoor/Dock: 21 (GRO)\nControl#: 2902/1013013635/430869\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}