{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.38283395767211914, "Height": 0.9371726512908936, "Left": 0.21208103001117706, "Top": 0.0031514847651124}, "Polygon": [{"X": 0.21805626153945923, "Y": 0.0031514847651124}, {"X": 0.594914972782135, "Y": 0.004918524995446205}, {"X": 0.5797246694564819, "Y": 0.9403241276741028}, {"X": 0.21208103001117706, "Y": 0.9287943840026855}]}, "Id": "6ed2b5c7-8a7a-4369-8c06-0f9c6f9fe9ed", "Relationships": [{"Type": "CHILD", "Ids": ["42ab0493-b18f-491a-9b34-75d2d157c887", "fcc272ee-69e2-4169-895d-5c9daed55740", "f18796d2-a1f1-4aaa-b447-109f72768646", "0a99f211-2a98-49e7-a958-fc0d6eefc94e", "c882029f-ee90-4953-a540-c80587a9a1ca", "def32ce3-a516-4168-b5fc-28cbc38de73a", "2b4defca-8d80-4da4-ab82-854c96a67325", "91251e39-47d4-451f-a3a3-a98393e85d93", "a0eb9113-5262-425d-9aa6-585ac8080c8f", "d64978e4-9f66-44a7-828b-822d1c1dfe1b", "86796d96-23a9-4bcd-97af-80c096eb678c", "4ecad98e-cf74-432d-8e4b-b03f23da35c0", "ecb361e1-070b-487a-b353-976be1281762", "92680b56-7853-4c7e-bc45-f8d26a08c121", "bc87ace6-a7d4-485f-a749-fc09ed76e393", "55fe9141-fca5-4a42-be31-bed96ae3f696", "02c1de43-10c1-4f25-8d79-52ddcc55d00c", "d188c5e6-cb74-4a5b-89bc-e670288a291d", "c39f4217-aa27-43b6-b00e-1e2cee20c809", "266207dc-a538-4619-aa32-e9fed1f9b4b8", "d7bfc8b0-0a12-4b11-aee9-dd783e38a93e", "18bd4d34-0a37-4fa7-a791-be9be8ea8608", "1a1df739-c81f-47e6-84ba-509f5ed87fad", "2bc9b1a9-9f40-49fa-bdcb-10fe8dc5c08d", "325744ad-1f43-4d23-ba5f-7b83ad3c0b9e", "8ef0fe7e-87ca-4d5f-b831-22359a42aae3", "80b319ba-6424-49ea-89ae-a1a9ebe2c5c3", "d6250fa7-c285-4eb2-9892-ea253a6e20d9", "94afa910-a7c1-461c-9d4d-234f877f360e", "68f554fa-828f-4216-b1db-a7ff6f9c69fc", "2bb64f94-5984-419b-a952-5b788c9f5436", "53ff9d12-2701-4f61-af4d-3e6db2525c8d", "426f4124-6f7a-462c-9a98-eae758439883", "20835886-5117-4f4b-8d2b-9f33072166e2", "2958d65e-c29e-47c5-b561-637ad5b766f2", "3afefde2-73bb-4356-8695-5d835639593c", "e7152606-c9a4-41c9-9ed0-f6efc60df053", "487e0954-e5d9-4d7d-bf19-d32190371c77", "4e0a20d6-768e-4c66-9256-3a7eee72a0b0", "4f4ae9e3-cd07-4483-828f-a2c6a41a51d2"]}]}, {"BlockType": "LINE", "Confidence": 99.97369384765625, "Text": "KWIK TRIP #1267", "Geometry": {"BoundingBox": {"Width": 0.1863568127155304, "Height": 0.014680744148790836, "Left": 0.2898452579975128, "Top": 0.1506165713071823}, "Polygon": [{"X": 0.28995344042778015, "Y": 0.1506165713071823}, {"X": 0.4762020707130432, "Y": 0.15227767825126648}, {"X": 0.4760301113128662, "Y": 0.16529731452465057}, {"X": 0.2898452579975128, "Y": 0.163567453622818}]}, "Id": "42ab0493-b18f-491a-9b34-75d2d157c887", "Relationships": [{"Type": "CHILD", "Ids": ["21ae0ef6-450f-41ce-b5e0-0ef7e6a762ca", "0b321c69-af8c-42f0-8649-5f6353cfa641", "89a86487-ee8f-4575-894a-233b6baa9684"]}]}, {"BlockType": "LINE", "Confidence": 98.89767456054688, "Text": "1267CHIPPEWACROSSING", "Geometry": {"BoundingBox": {"Width": 0.25136151909828186, "Height": 0.01534454245120287, "Left": 0.26321184635162354, "Top": 0.1686694175004959}, "Polygon": [{"X": 0.2633105218410492, "Y": 0.1686694175004959}, {"X": 0.514573335647583, "Y": 0.17104259133338928}, {"X": 0.5143889784812927, "Y": 0.1840139627456665}, {"X": 0.26321184635162354, "Y": 0.18154850602149963}]}, "Id": "fcc272ee-69e2-4169-895d-5c9daed55740", "Relationships": [{"Type": "CHILD", "Ids": ["cdfa93c1-fcfb-4626-a097-0885fd2667a5"]}]}, {"BlockType": "LINE", "Confidence": 99.85086059570312, "Text": "CHIPPEWA FALLS, WI", "Geometry": {"BoundingBox": {"Width": 0.22367852926254272, "Height": 0.017822157591581345, "Left": 0.276873916387558, "Top": 0.18634825944900513}, "Polygon": [{"X": 0.27699825167655945, "Y": 0.18634825944900513}, {"X": 0.5005524158477783, "Y": 0.18857255578041077}, {"X": 0.5003363490104675, "Y": 0.20417042076587677}, {"X": 0.276873916387558, "Y": 0.2018474042415619}]}, "Id": "f18796d2-a1f1-4aaa-b447-109f72768646", "Relationships": [{"Type": "CHILD", "Ids": ["91c1ccf9-e5b0-4fbe-868d-714fef54dc85", "3a8db989-6971-4f66-92ff-aef02f8826b9", "31865ff1-0461-4cbd-8cda-2dedd54797eb"]}]}, {"BlockType": "LINE", "Confidence": 99.36264038085938, "Text": "Invoice# 0098760", "Geometry": {"BoundingBox": {"Width": 0.20985223352909088, "Height": 0.015473902225494385, "Left": 0.26538902521133423, "Top": 0.22315609455108643}, "Polygon": [{"X": 0.26549017429351807, "Y": 0.22315609455108643}, {"X": 0.4752412736415863, "Y": 0.22546587884426117}, {"X": 0.4750673770904541, "Y": 0.2386299967765808}, {"X": 0.26538902521133423, "Y": 0.23624208569526672}]}, "Id": "0a99f211-2a98-49e7-a958-fc0d6eefc94e", "Relationships": [{"Type": "CHILD", "Ids": ["578d9298-f47f-4443-9a33-4a82560aae70", "44c93b9f-b21f-4b03-bb37-5988a21efe6d"]}]}, {"BlockType": "LINE", "Confidence": 99.0012435913086, "Text": "Trans # 0900-8760", "Geometry": {"BoundingBox": {"Width": 0.2376573383808136, "Height": 0.01604221574962139, "Left": 0.2618712782859802, "Top": 0.24122869968414307}, "Polygon": [{"X": 0.26197224855422974, "Y": 0.24122869968414307}, {"X": 0.4995286166667938, "Y": 0.24396845698356628}, {"X": 0.4993443787097931, "Y": 0.2572709321975708}, {"X": 0.2618712782859802, "Y": 0.2544418275356293}]}, "Id": "c882029f-ee90-4953-a540-c80587a9a1ca", "Relationships": [{"Type": "CHILD", "Ids": ["a75b2aae-cea6-43bb-8eb3-3b715d80dad6", "55e3b0ba-5874-498e-b77e-fd4a04a29835", "ae0c37a7-46b9-4768-bd60-dcb19dcebe97"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.04877998307347298, "Height": 0.012523078359663486, "Left": 0.26286938786506653, "Top": 0.260686457157135}, "Polygon": [{"X": 0.262960821390152, "Y": 0.260686457157135}, {"X": 0.3116493821144104, "Y": 0.26127520203590393}, {"X": 0.3115426301956177, "Y": 0.27320951223373413}, {"X": 0.26286938786506653, "Y": 0.2726042568683624}]}, "Id": "def32ce3-a516-4168-b5fc-28cbc38de73a", "Relationships": [{"Type": "CHILD", "Ids": ["38d8f99b-74c0-4fd3-ac6a-6470214b0460"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "10/09/2024", "Geometry": {"BoundingBox": {"Width": 0.12385853379964828, "Height": 0.016661958768963814, "Left": 0.38822123408317566, "Top": 0.26123374700546265}, "Polygon": [{"X": 0.3883867561817169, "Y": 0.26123374700546265}, {"X": 0.5120797753334045, "Y": 0.2627260684967041}, {"X": 0.5118646025657654, "Y": 0.2778956890106201}, {"X": 0.38822123408317566, "Y": 0.27635037899017334}]}, "Id": "2b4defca-8d80-4da4-ab82-854c96a67325", "Relationships": [{"Type": "CHILD", "Ids": ["93671d29-529f-4a6d-a38a-c1830c059a8c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.050160299986600876, "Height": 0.013867514207959175, "Left": 0.26123157143592834, "Top": 0.2780578136444092}, "Polygon": [{"X": 0.2613324820995331, "Y": 0.2780578136444092}, {"X": 0.3113918900489807, "Y": 0.2786882221698761}, {"X": 0.3112735152244568, "Y": 0.2919253408908844}, {"X": 0.26123157143592834, "Y": 0.29127612709999084}]}, "Id": "91251e39-47d4-451f-a3a3-a98393e85d93", "Relationships": [{"Type": "CHILD", "Ids": ["caed146c-230c-4530-86db-ea0e08cc2f09"]}]}, {"BlockType": "LINE", "Confidence": 99.60698699951172, "Text": "03:45 PM", "Geometry": {"BoundingBox": {"Width": 0.09885570406913757, "Height": 0.013867155648767948, "Left": 0.38757044076919556, "Top": 0.28031083941459656}, "Polygon": [{"X": 0.3877080976963043, "Y": 0.28031083941459656}, {"X": 0.48642614483833313, "Y": 0.2815558612346649}, {"X": 0.4862554967403412, "Y": 0.2941780090332031}, {"X": 0.38757044076919556, "Y": 0.2928977608680725}]}, "Id": "a0eb9113-5262-425d-9aa6-585ac8080c8f", "Relationships": [{"Type": "CHILD", "Ids": ["b6d278f0-09b2-4e46-b026-90ad08ae440d", "86d756f4-39e6-4f1b-ad26-f89dc9119007"]}]}, {"BlockType": "LINE", "Confidence": 99.94359588623047, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.24930015206336975, "Height": 0.020289842039346695, "Left": 0.2617838978767395, "Top": 0.3146825134754181}, "Polygon": [{"X": 0.2619122862815857, "Y": 0.3146825134754181}, {"X": 0.5110840201377869, "Y": 0.31808286905288696}, {"X": 0.5108445882797241, "Y": 0.3349723517894745}, {"X": 0.2617838978767395, "Y": 0.3314533233642578}]}, "Id": "d64978e4-9f66-44a7-828b-822d1c1dfe1b", "Relationships": [{"Type": "CHILD", "Ids": ["4267d89e-f32e-4ec4-a7a0-be4c792054f4", "b5413338-c88f-43a0-9b43-572cd36718f3", "16fc98e6-5676-40a9-818a-2a2359a664e8"]}]}, {"BlockType": "LINE", "Confidence": 99.83186340332031, "Text": "02 109.316 3.599", "Geometry": {"BoundingBox": {"Width": 0.2361631840467453, "Height": 0.016070261597633362, "Left": 0.27428504824638367, "Top": 0.33434200286865234}, "Polygon": [{"X": 0.274385929107666, "Y": 0.33434200286865234}, {"X": 0.5104482173919678, "Y": 0.33769577741622925}, {"X": 0.5102680325508118, "Y": 0.3504122793674469}, {"X": 0.27428504824638367, "Y": 0.34697386622428894}]}, "Id": "86796d96-23a9-4bcd-97af-80c096eb678c", "Relationships": [{"Type": "CHILD", "Ids": ["eacc98e3-5cf8-431a-a6ee-7a69d301a773", "e66c5fb9-8cd4-459b-a3c5-773223bbcffe", "7c68de9b-6f04-46eb-8f1a-716489b4a9bd"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.0874345600605011, "Height": 0.013880101963877678, "Left": 0.26115643978118896, "Top": 0.37129026651382446}, "Polygon": [{"X": 0.26125216484069824, "Y": 0.37129026651382446}, {"X": 0.34859099984169006, "Y": 0.3726244270801544}, {"X": 0.34846630692481995, "Y": 0.3851703703403473}, {"X": 0.26115643978118896, "Y": 0.38380521535873413}]}, "Id": "4ecad98e-cf74-432d-8e4b-b03f23da35c0", "Relationships": [{"Type": "CHILD", "Ids": ["5e9a563f-21a2-407f-8a4f-24042fa3d3ce"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.07345481961965561, "Height": 0.013332904316484928, "Left": 0.43638309836387634, "Top": 0.3739773631095886}, "Polygon": [{"X": 0.4365323483943939, "Y": 0.3739773631095886}, {"X": 0.5098379254341125, "Y": 0.3750971555709839}, {"X": 0.5096648931503296, "Y": 0.387310266494751}, {"X": 0.43638309836387634, "Y": 0.38616523146629333}]}, "Id": "ecb361e1-070b-487a-b353-976be1281762", "Relationships": [{"Type": "CHILD", "Ids": ["97230c78-097d-431f-9f38-db5bd76e8fdc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.049540601670742035, "Height": 0.012542606331408024, "Left": 0.26098576188087463, "Top": 0.3905240595340729}, "Polygon": [{"X": 0.26107558608055115, "Y": 0.3905240595340729}, {"X": 0.3105263411998749, "Y": 0.3913068175315857}, {"X": 0.3104211390018463, "Y": 0.4030666649341583}, {"X": 0.26098576188087463, "Y": 0.4022674262523651}]}, "Id": "92680b56-7853-4c7e-bc45-f8d26a08c121", "Relationships": [{"Type": "CHILD", "Ids": ["475dbd70-19a9-4071-bfc8-7faec40def70"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "393.43", "Geometry": {"BoundingBox": {"Width": 0.07265445590019226, "Height": 0.013539855368435383, "Left": 0.43667861819267273, "Top": 0.39233332872390747}, "Polygon": [{"X": 0.4368302524089813, "Y": 0.39233332872390747}, {"X": 0.509333074092865, "Y": 0.39347895979881287}, {"X": 0.5091575980186462, "Y": 0.40587317943573}, {"X": 0.43667861819267273, "Y": 0.40470224618911743}]}, "Id": "bc87ace6-a7d4-485f-a749-fc09ed76e393", "Relationships": [{"Type": "CHILD", "Ids": ["580de27d-3874-4065-a13f-e88cfeb8e252"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06326180696487427, "Height": 0.012959182262420654, "Left": 0.2595531642436981, "Top": 0.42735859751701355}, "Polygon": [{"X": 0.2596436142921448, "Y": 0.42735859751701355}, {"X": 0.3228149712085724, "Y": 0.42842552065849304}, {"X": 0.32270464301109314, "Y": 0.4403177797794342}, {"X": 0.2595531642436981, "Y": 0.4392295777797699}]}, "Id": "55fe9141-fca5-4a42-be31-bed96ae3f696", "Relationships": [{"Type": "CHILD", "Ids": ["ed5c5be8-a31f-4114-a860-a626f333932b"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "393.43", "Geometry": {"BoundingBox": {"Width": 0.07262898981571198, "Height": 0.013480834662914276, "Left": 0.42377662658691406, "Top": 0.42916810512542725}, "Polygon": [{"X": 0.423922598361969, "Y": 0.42916810512542725}, {"X": 0.49640563130378723, "Y": 0.430390328168869}, {"X": 0.49623602628707886, "Y": 0.4426489472389221}, {"X": 0.42377662658691406, "Y": 0.44140174984931946}]}, "Id": "02c1de43-10c1-4f25-8d79-52ddcc55d00c", "Relationships": [{"Type": "CHILD", "Ids": ["2d205fdd-cfbc-4742-b043-e1e35ee927fc"]}]}, {"BlockType": "LINE", "Confidence": 99.8299331665039, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.23454946279525757, "Height": 0.019664669409394264, "Left": 0.2607004642486572, "Top": 0.*****************}, "Polygon": [{"X": 0.26081815361976624, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.2607004642486572, "Y": 0.*****************}]}, "Id": "d188c5e6-cb74-4a5b-89bc-e670288a291d", "Relationships": [{"Type": "CHILD", "Ids": ["3c9777a6-137f-4e19-b9e8-89f1bb21b7db", "56e65b4c-f7c4-4e1c-822e-2a252ffe4d93", "defaea6e-e8aa-4b11-bc30-63e2985031ef"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.11170043051242828, "Height": 0.014446757733821869, "Left": 0.2608206868171692, "Top": 0.****************}, "Polygon": [{"X": 0.2609153091907501, "Y": 0.****************}, {"X": 0.37252113223075867, "Y": 0.****************}, {"X": 0.37238985300064087, "Y": 0.*****************}, {"X": 0.2608206868171692, "Y": 0.****************}]}, "Id": "c39f4217-aa27-43b6-b00e-1e2cee20c809", "Relationships": [{"Type": "CHILD", "Ids": ["51c93ced-0311-4207-a04d-5f14c54dc2bf", "e8ac30e5-520b-46d3-856d-dfbc2a3b749b"]}]}, {"BlockType": "LINE", "Confidence": 99.83847045898438, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.0754755437374115, "Height": 0.014415315352380276, "Left": 0.2599608302116394, "Top": 0.517835795879364}, "Polygon": [{"X": 0.2600596249103546, "Y": 0.517835795879364}, {"X": 0.3354363739490509, "Y": 0.****************}, {"X": 0.3353116810321808, "Y": 0.****************}, {"X": 0.2599608302116394, "Y": 0.****************}]}, "Id": "266207dc-a538-4619-aa32-e9fed1f9b4b8", "Relationships": [{"Type": "CHILD", "Ids": ["bd8f4199-61fb-4664-866e-7f692b958a6a", "b0e98d0a-3f9a-4950-8595-bd1353163486"]}]}, {"BlockType": "LINE", "Confidence": 99.41346740722656, "Text": "P40214", "Geometry": {"BoundingBox": {"Width": 0.07211124151945114, "Height": 0.013514695689082146, "Left": 0.434641033411026, "Top": 0.5194631814956665}, "Polygon": [{"X": 0.43478909134864807, "Y": 0.5194631814956665}, {"X": 0.5067522525787354, "Y": 0.5208622813224792}, {"X": 0.5065809488296509, "Y": 0.5329778790473938}, {"X": 0.434641033411026, "Y": 0.5315543413162231}]}, "Id": "d7bfc8b0-0a12-4b11-aee9-dd783e38a93e", "Relationships": [{"Type": "CHILD", "Ids": ["5c481ad4-080e-4f5d-9fcb-b2b2497a9362"]}]}, {"BlockType": "LINE", "Confidence": 99.47693634033203, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.2220548391342163, "Height": 0.01926802657544613, "Left": 0.26006680727005005, "Top": 0.5342166423797607}, "Polygon": [{"X": 0.26017966866493225, "Y": 0.5342166423797607}, {"X": 0.48212164640426636, "Y": 0.5386475324630737}, {"X": 0.48192137479782104, "Y": 0.5534846782684326}, {"X": 0.26006680727005005, "Y": 0.5489614009857178}]}, "Id": "18bd4d34-0a37-4fa7-a791-be9be8ea8608", "Relationships": [{"Type": "CHILD", "Ids": ["16e16531-7325-485a-9046-ef737d73a438", "8a8d75a4-1f38-4e10-8f03-e487f43ac40d"]}]}, {"BlockType": "LINE", "Confidence": 99.70497131347656, "Text": "AUTH REF#: P40214", "Geometry": {"BoundingBox": {"Width": 0.209329292178154, "Height": 0.019036121666431427, "Left": 0.2722238302230835, "Top": 0.552678644657135}, "Polygon": [{"X": 0.2723408043384552, "Y": 0.552678644657135}, {"X": 0.4815531075000763, "Y": 0.5569650530815125}, {"X": 0.48135414719581604, "Y": 0.5717147588729858}, {"X": 0.2722238302230835, "Y": 0.5673418641090393}]}, "Id": "1a1df739-c81f-47e6-84ba-509f5ed87fad", "Relationships": [{"Type": "CHILD", "Ids": ["151d32a4-f5ab-4a4e-9e4f-ba2997648781", "f45e37e4-1bac-4c28-b459-9270a1c5ffee", "f3d42683-d30c-40c7-8508-5c256342b487"]}]}, {"BlockType": "LINE", "Confidence": 99.50364685058594, "Text": "KWIK REWARDS/LOYALTY", "Geometry": {"BoundingBox": {"Width": 0.24677351117134094, "Height": 0.02185300923883915, "Left": 0.258836954832077, "Top": 0.5877439379692078}, "Polygon": [{"X": 0.25896239280700684, "Y": 0.5877439379692078}, {"X": 0.505610466003418, "Y": 0.593048095703125}, {"X": 0.5053765177726746, "Y": 0.6095969080924988}, {"X": 0.258836954832077, "Y": 0.6041784286499023}]}, "Id": "2bc9b1a9-9f40-49fa-bdcb-10fe8dc5c08d", "Relationships": [{"Type": "CHILD", "Ids": ["a972fdb1-aea1-4d01-a3a9-df70de6b42f6", "afdb7367-aa5d-41a5-ac93-1e40e31ce5a3"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.11145497858524323, "Height": 0.01679949276149273, "Left": 0.25863221287727356, "Top": 0.608081579208374}, "Polygon": [{"X": 0.2587413191795349, "Y": 0.608081579208374}, {"X": 0.***************, "Y": 0.6105412244796753}, {"X": 0.36993563175201416, "Y": 0.6248810291290283}, {"X": 0.25863221287727356, "Y": 0.6223765015602112}]}, "Id": "325744ad-1f43-4d23-ba5f-7b83ad3c0b9e", "Relationships": [{"Type": "CHILD", "Ids": ["235a3bbf-a198-4cc8-aaf4-c43c8caaaa04", "aef3a14b-7895-43f8-b635-765539931cb5"]}]}, {"BlockType": "LINE", "Confidence": 89.98086547851562, "Text": "XXXXXXXXXXXX6391", "Geometry": {"BoundingBox": {"Width": 0.1955157369375229, "Height": 0.020496027544140816, "Left": 0.25730758905410767, "Top": 0.6249175667762756}, "Polygon": [{"X": 0.25742918252944946, "Y": 0.6249175667762756}, {"X": 0.*****************, "Y": 0.6293287873268127}, {"X": 0.*****************, "Y": 0.6454136371612549}, {"X": 0.25730758905410767, "Y": 0.6409143805503845}]}, "Id": "8ef0fe7e-87ca-4d5f-b831-22359a42aae3", "Relationships": [{"Type": "CHILD", "Ids": ["4b79f7ee-eaa0-494f-82da-c9da534f0e96"]}]}, {"BlockType": "LINE", "Confidence": 99.8779296875, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.07505965232849121, "Height": 0.015557879582047462, "Left": 0.25791874527931213, "Top": 0.6450138092041016}, "Polygon": [{"X": 0.25802385807037354, "Y": 0.6450138092041016}, {"X": 0.33297839760780334, "Y": 0.6467493176460266}, {"X": 0.3328457176685333, "Y": 0.6605716943740845}, {"X": 0.25791874527931213, "Y": 0.658807098865509}]}, "Id": "80b319ba-6424-49ea-89ae-a1a9ebe2c5c3", "Relationships": [{"Type": "CHILD", "Ids": ["f1641fe5-43fe-4722-b90e-bfcc183ee9a1", "e68961bb-f2b2-4109-8377-1b67c3f6b508"]}]}, {"BlockType": "LINE", "Confidence": 94.38875579833984, "Text": "U:4395515", "Geometry": {"BoundingBox": {"Width": 0.10962565988302231, "Height": 0.015313642099499702, "Left": 0.39496397972106934, "Top": 0.6459076404571533}, "Polygon": [{"X": 0.395107239484787, "Y": 0.6459076404571533}, {"X": 0.5045896768569946, "Y": 0.6484354138374329}, {"X": 0.5044089555740356, "Y": 0.6612212657928467}, {"X": 0.39496397972106934, "Y": 0.6586543917655945}]}, "Id": "d6250fa7-c285-4eb2-9892-ea253a6e20d9", "Relationships": [{"Type": "CHILD", "Ids": ["b334c024-21bc-40a1-b7cc-506621e3a698"]}]}, {"BlockType": "LINE", "Confidence": 98.73246002197266, "Text": "RESPONSE:", "Geometry": {"BoundingBox": {"Width": 0.10777408629655838, "Height": 0.01711617223918438, "Left": 0.25805002450942993, "Top": 0.6625954508781433}, "Polygon": [{"X": 0.258160799741745, "Y": 0.6625954508781433}, {"X": 0.3658241033554077, "Y": 0.6651427149772644}, {"X": 0.3656715452671051, "Y": 0.679711639881134}, {"X": 0.25805002450942993, "Y": 0.6771203875541687}]}, "Id": "94afa910-a7c1-461c-9d4d-234f877f360e", "Relationships": [{"Type": "CHILD", "Ids": ["550a1c77-d610-4001-bc57-808ba92979b8"]}]}, {"BlockType": "LINE", "Confidence": 98.66998291015625, "Text": "APPROVED, Total", "Geometry": {"BoundingBox": {"Width": 0.18422642350196838, "Height": 0.02027924917638302, "Left": 0.270129531621933, "Top": 0.6801812052726746}, "Polygon": [{"X": 0.2702547311782837, "Y": 0.6801812052726746}, {"X": 0.45435595512390137, "Y": 0.6846285462379456}, {"X": 0.45415303111076355, "Y": 0.7004604339599609}, {"X": 0.270129531621933, "Y": 0.6959315538406372}]}, "Id": "68f554fa-828f-4216-b1db-a7ff6f9c69fc", "Relationships": [{"Type": "CHILD", "Ids": ["11aa07bb-ec5a-4aa6-b583-79c30c384679", "508b4e36-c5f0-44e5-9db4-0b9dfad3be09"]}]}, {"BlockType": "LINE", "Confidence": 99.63475799560547, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.2208070307970047, "Height": 0.019862471148371696, "Left": 0.2699495255947113, "Top": 0.6983774304389954}, "Polygon": [{"X": 0.2700634002685547, "Y": 0.6983774304389954}, {"X": 0.4907565712928772, "Y": 0.7038243412971497}, {"X": 0.49055784940719604, "Y": 0.7182399034500122}, {"X": 0.2699495255947113, "Y": 0.7127041816711426}]}, "Id": "2bb64f94-5984-419b-a952-5b788c9f5436", "Relationships": [{"Type": "CHILD", "Ids": ["a93a554b-2bba-4f37-a5ac-e841560d995b", "f8dc831c-9ced-4b19-b18e-ffad19db0622", "cb2cfc42-fedd-418c-8815-9417234387ff"]}]}, {"BlockType": "LINE", "Confidence": 99.40409851074219, "Text": "Points: $11.41", "Geometry": {"BoundingBox": {"Width": 0.16906127333641052, "Height": 0.019697556272149086, "Left": 0.2694297730922699, "Top": 0.7159676551818848}, "Polygon": [{"X": 0.2695517838001251, "Y": 0.7159676551818848}, {"X": 0.****************, "Y": 0.7202227711677551}, {"X": 0.*****************, "Y": 0.7356652021408081}, {"X": 0.2694297730922699, "Y": 0.7313371896743774}]}, "Id": "53ff9d12-2701-4f61-af4d-3e6db2525c8d", "Relationships": [{"Type": "CHILD", "Ids": ["8afa09b3-c1aa-458f-a49c-ebcd2bedc9b2", "7947484b-859a-4338-8338-0a5a3d742b85"]}]}, {"BlockType": "LINE", "Confidence": 99.23778533935547, "Text": "Shower credit", "Geometry": {"BoundingBox": {"Width": 0.15877395868301392, "Height": 0.01931605115532875, "Left": 0.2698585093021393, "Top": 0.7337750792503357}, "Polygon": [{"X": 0.26997917890548706, "Y": 0.7337750792503357}, {"X": 0.****************, "Y": 0.7378523349761963}, {"X": 0.*****************, "Y": 0.7530910968780518}, {"X": 0.2698585093021393, "Y": 0.7489463090896606}]}, "Id": "426f4124-6f7a-462c-9a98-eae758439883", "Relationships": [{"Type": "CHILD", "Ids": ["857611a4-02fe-46c5-a7f7-c701a56881c0", "0fc58fb6-79c5-4d57-8733-c5af3eea3842"]}]}, {"BlockType": "LINE", "Confidence": 99.*************, "Text": "earned! Expires in", "Geometry": {"BoundingBox": {"Width": 0.22029052674770355, "Height": 0.02241976000368595, "Left": 0.26959875226020813, "Top": 0.7516298890113831}, "Polygon": [{"X": 0.2697303295135498, "Y": 0.7516298890113831}, {"X": 0.****************, "Y": 0.7574008703231812}, {"X": 0.**************, "Y": 0.7740496397018433}, {"X": 0.26959875226020813, "Y": 0.7681764364242554}]}, "Id": "20835886-5117-4f4b-8d2b-9f33072166e2", "Relationships": [{"Type": "CHILD", "Ids": ["2431d55c-4a8f-4d26-b901-06d64ca367b6", "c939bf16-0a08-43da-8cfa-c9be01416354", "b3f016e8-d20e-4caf-b7e1-d27da55bb7b1"]}]}, {"BlockType": "LINE", "Confidence": 99.61754608154297, "Text": "14 days", "Geometry": {"BoundingBox": {"Width": 0.08424364030361176, "Height": 0.016810987144708633, "Left": 0.2702852189540863, "Top": 0.7714377641677856}, "Polygon": [{"X": 0.2704010307788849, "Y": 0.7714377641677856}, {"X": 0.35452887415885925, "Y": 0.7736908793449402}, {"X": 0.354380339384079, "Y": 0.7882487773895264}, {"X": 0.2702852189540863, "Y": 0.785961389541626}]}, "Id": "2958d65e-c29e-47c5-b561-637ad5b766f2", "Relationships": [{"Type": "CHILD", "Ids": ["6fa23faa-c0c7-40b0-b59a-a503e7ac6b7b", "77a12fa5-4860-4289-be60-7278d894c383"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "TRUCK NUMBER 02929", "Geometry": {"BoundingBox": {"Width": 0.24496011435985565, "Height": 0.02020471729338169, "Left": 0.2560611069202423, "Top": 0.8059808611869812}, "Polygon": [{"X": 0.2561623156070709, "Y": 0.8059808611869812}, {"X": 0.5010212063789368, "Y": 0.8127847909927368}, {"X": 0.5008323192596436, "Y": 0.8261855840682983}, {"X": 0.2560611069202423, "Y": 0.819290280342102}]}, "Id": "3afefde2-73bb-4356-8695-5d835639593c", "Relationships": [{"Type": "CHILD", "Ids": ["2df9ee04-edb7-46cf-b55d-8b512bcbfac3", "fe2a8bed-6054-4305-a783-fcaf3cf2031c", "a2f287ee-9273-43c9-ba49-ecccc4b62b8e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "MERCHANT NUMBER", "Geometry": {"BoundingBox": {"Width": 0.1836656928062439, "Height": 0.018804948776960373, "Left": 0.25607118010520935, "Top": 0.8236389756202698}, "Polygon": [{"X": 0.2561741769313812, "Y": 0.8236389756202698}, {"X": 0.43973687291145325, "Y": 0.8288329243659973}, {"X": 0.43956702947616577, "Y": 0.8424439430236816}, {"X": 0.25607118010520935, "Y": 0.8371803164482117}]}, "Id": "e7152606-c9a4-41c9-9ed0-f6efc60df053", "Relationships": [{"Type": "CHILD", "Ids": ["8f27d655-0019-4463-a5de-39c6a99a49bb", "5721d07e-02ba-47ae-adbe-d324a46454bf"]}]}, {"BlockType": "LINE", "Confidence": 93.54034423828125, "Text": "*******2676", "Geometry": {"BoundingBox": {"Width": 0.13414841890335083, "Height": 0.015911469236016273, "Left": 0.2804955542087555, "Top": 0.8420714735984802}, "Polygon": [{"X": 0.28059470653533936, "Y": 0.8420714735984802}, {"X": 0.4146439731121063, "Y": 0.8459328413009644}, {"X": 0.4145015776157379, "Y": 0.8579829931259155}, {"X": 0.2804955542087555, "Y": 0.8540765643119812}]}, "Id": "487e0954-e5d9-4d7d-bf19-d32190371c77", "Relationships": [{"Type": "CHILD", "Ids": ["7c1734d0-bdb6-4ba5-9503-15c18c742b03"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "ALL TAXES INCLUDED", "Geometry": {"BoundingBox": {"Width": 0.218326598405838, "Height": 0.01976887881755829, "Left": 0.26911240816116333, "Top": 0.8596827983856201}, "Polygon": [{"X": 0.26921820640563965, "Y": 0.8596827983856201}, {"X": 0.48743903636932373, "Y": 0.8660813570022583}, {"X": 0.4872550964355469, "Y": 0.8794516921043396}, {"X": 0.26911240816116333, "Y": 0.8729719519615173}]}, "Id": "4e0a20d6-768e-4c66-9256-3a7eee72a0b0", "Relationships": [{"Type": "CHILD", "Ids": ["e765dd94-e3f8-4745-a3db-ac9201681023", "37d63eee-77df-427e-8c7b-8c4ad56f80e3", "9e25ccbf-92ff-4620-8747-4734c2601a24"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "IN FUEL PRICE", "Geometry": {"BoundingBox": {"Width": 0.15478071570396423, "Height": 0.01724581979215145, "Left": 0.295271098613739, "Top": 0.8783009648323059}, "Polygon": [{"X": 0.29538002610206604, "Y": 0.8783009648323059}, {"X": 0.45005184412002563, "Y": 0.8829155564308167}, {"X": 0.44989052414894104, "Y": 0.8955467939376831}, {"X": 0.295271098613739, "Y": 0.8908778429031372}]}, "Id": "4f4ae9e3-cd07-4483-828f-a2c6a41a51d2", "Relationships": [{"Type": "CHILD", "Ids": ["74526d0e-0ec0-4215-8bb5-8814a83f839a", "a54c6be1-7b6f-4872-a1ec-1fe17fca7dd5", "6d9805d9-ad58-417c-b3be-5ca2e027d847"]}]}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049218662083148956, "Height": 0.013032154180109501, "Left": 0.2898483872413635, "Top": 0.1506165713071823}, "Polygon": [{"X": 0.28995344042778015, "Y": 0.1506165713071823}, {"X": 0.3390670418739319, "Y": 0.15105460584163666}, {"X": 0.3389457166194916, "Y": 0.16364872455596924}, {"X": 0.2898483872413635, "Y": 0.16319309175014496}]}, "Id": "21ae0ef6-450f-41ce-b5e0-0ef7e6a762ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04978397861123085, "Height": 0.012821529991924763, "Left": 0.35163193941116333, "Top": 0.15178124606609344}, "Polygon": [{"X": 0.3517551124095917, "Y": 0.15178124606609344}, {"X": 0.40141594409942627, "Y": 0.15222503244876862}, {"X": 0.401276558637619, "Y": 0.16460277140140533}, {"X": 0.35163193941116333, "Y": 0.16414152085781097}]}, "Id": "0b321c69-af8c-42f0-8649-5f6353cfa641"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "#1267", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062046609818935394, "Height": 0.0129091190174222, "Left": 0.41414934396743774, "Top": 0.15218904614448547}, "Polygon": [{"X": 0.4142923653125763, "Y": 0.15218904614448547}, {"X": 0.47619596123695374, "Y": 0.15274196863174438}, {"X": 0.4760327637195587, "Y": 0.165098175406456}, {"X": 0.41414934396743774, "Y": 0.16452355682849884}]}, "Id": "89a86487-ee8f-4575-894a-233b6baa9684"}, {"BlockType": "WORD", "Confidence": 98.89767456054688, "Text": "1267CHIPPEWACROSSING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25136151909828186, "Height": 0.01534454245120287, "Left": 0.26321184635162354, "Top": 0.1686694175004959}, "Polygon": [{"X": 0.2633105218410492, "Y": 0.1686694175004959}, {"X": 0.514573335647583, "Y": 0.17104259133338928}, {"X": 0.5143889784812927, "Y": 0.1840139627456665}, {"X": 0.26321184635162354, "Y": 0.18154850602149963}]}, "Id": "cdfa93c1-fcfb-4626-a097-0885fd2667a5"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "CHIPPEWA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09927059710025787, "Height": 0.01349348109215498, "Left": 0.27688953280448914, "Top": 0.1874299943447113}, "Polygon": [{"X": 0.27698957920074463, "Y": 0.1874299943447113}, {"X": 0.3761601448059082, "Y": 0.18841980397701263}, {"X": 0.3760274648666382, "Y": 0.20092348754405975}, {"X": 0.27688953280448914, "Y": 0.19989845156669617}]}, "Id": "91c1ccf9-e5b0-4fbe-868d-714fef54dc85"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "FALLS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06978416442871094, "Height": 0.015243149362504482, "Left": 0.3897975981235504, "Top": 0.18850167095661163}, "Polygon": [{"X": 0.38995686173439026, "Y": 0.18850167095661163}, {"X": 0.45958176255226135, "Y": 0.18919646739959717}, {"X": 0.45939573645591736, "Y": 0.2037448137998581}, {"X": 0.3897975981235504, "Y": 0.20302130281925201}]}, "Id": "3a8db989-6971-4f66-92ff-aef02f8826b9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02561960183084011, "Height": 0.01242397166788578, "Left": 0.47493284940719604, "Top": 0.18831925094127655}, "Polygon": [{"X": 0.475093275308609, "Y": 0.18831925094127655}, {"X": 0.5005524158477783, "Y": 0.18857255578041077}, {"X": 0.5003837943077087, "Y": 0.20074321329593658}, {"X": 0.47493284940719604, "Y": 0.2004811316728592}]}, "Id": "31865ff1-0461-4cbd-8cda-2dedd54797eb"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09795412421226501, "Height": 0.014199994504451752, "Left": 0.26538902521133423, "Top": 0.22315609455108643}, "Polygon": [{"X": 0.26549017429351807, "Y": 0.22315609455108643}, {"X": 0.36334314942359924, "Y": 0.22423365712165833}, {"X": 0.36320817470550537, "Y": 0.23735609650611877}, {"X": 0.26538902521133423, "Y": 0.23624208569526672}]}, "Id": "578d9298-f47f-4443-9a33-4a82560aae70"}, {"BlockType": "WORD", "Confidence": 99.13365173339844, "Text": "0098760", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08652368187904358, "Height": 0.013847311027348042, "Left": 0.38871458172798157, "Top": 0.22474071383476257}, "Polygon": [{"X": 0.3888554871082306, "Y": 0.22474071383476257}, {"X": 0.47523826360702515, "Y": 0.2256925106048584}, {"X": 0.47506794333457947, "Y": 0.23858802020549774}, {"X": 0.38871458172798157, "Y": 0.23760469257831573}]}, "Id": "44c93b9f-b21f-4b03-bb37-5988a21efe6d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0628865510225296, "Height": 0.012804289348423481, "Left": 0.2618718445301056, "Top": 0.24231024086475372}, "Polygon": [{"X": 0.26196399331092834, "Y": 0.24231024086475372}, {"X": 0.324758380651474, "Y": 0.2430364042520523}, {"X": 0.3246462643146515, "Y": 0.25511452555656433}, {"X": 0.2618718445301056, "Y": 0.25436681509017944}]}, "Id": "a75b2aae-cea6-43bb-8eb3-3b715d80dad6"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013129659928381443, "Height": 0.01211633812636137, "Left": 0.3499661982059479, "Top": 0.2432851791381836}, "Polygon": [{"X": 0.35008516907691956, "Y": 0.2432851791381836}, {"X": 0.36309587955474854, "Y": 0.24343562126159668}, {"X": 0.36297279596328735, "Y": 0.25540152192115784}, {"X": 0.3499661982059479, "Y": 0.255246639251709}]}, "Id": "55e3b0ba-5874-498e-b77e-fd4a04a29835"}, {"BlockType": "WORD", "Confidence": 97.15023040771484, "Text": "0900-8760", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1110859289765358, "Height": 0.014581947587430477, "Left": 0.3884426951408386, "Top": 0.24268896877765656}, "Polygon": [{"X": 0.3885878920555115, "Y": 0.24268896877765656}, {"X": 0.4995286166667938, "Y": 0.24396845698356628}, {"X": 0.4993443787097931, "Y": 0.2572709321975708}, {"X": 0.3884426951408386, "Y": 0.2559497058391571}]}, "Id": "ae0c37a7-46b9-4768-bd60-dcb19dcebe97"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04877998307347298, "Height": 0.012523078359663486, "Left": 0.26286938786506653, "Top": 0.260686457157135}, "Polygon": [{"X": 0.262960821390152, "Y": 0.260686457157135}, {"X": 0.3116493821144104, "Y": 0.26127520203590393}, {"X": 0.3115426301956177, "Y": 0.27320951223373413}, {"X": 0.26286938786506653, "Y": 0.2726042568683624}]}, "Id": "38d8f99b-74c0-4fd3-ac6a-6470214b0460"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "10/09/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12385853379964828, "Height": 0.016661958768963814, "Left": 0.38822123408317566, "Top": 0.26123374700546265}, "Polygon": [{"X": 0.3883867561817169, "Y": 0.26123374700546265}, {"X": 0.5120797753334045, "Y": 0.2627260684967041}, {"X": 0.5118646025657654, "Y": 0.2778956890106201}, {"X": 0.38822123408317566, "Y": 0.27635037899017334}]}, "Id": "93671d29-529f-4a6d-a38a-c1830c059a8c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050160299986600876, "Height": 0.013867514207959175, "Left": 0.26123157143592834, "Top": 0.2780578136444092}, "Polygon": [{"X": 0.2613324820995331, "Y": 0.2780578136444092}, {"X": 0.3113918900489807, "Y": 0.2786882221698761}, {"X": 0.3112735152244568, "Y": 0.2919253408908844}, {"X": 0.26123157143592834, "Y": 0.29127612709999084}]}, "Id": "caed146c-230c-4530-86db-ea0e08cc2f09"}, {"BlockType": "WORD", "Confidence": 99.4971694946289, "Text": "03:45", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061534300446510315, "Height": 0.01319463737308979, "Left": 0.38757044076919556, "Top": 0.2804993987083435}, "Polygon": [{"X": 0.38770604133605957, "Y": 0.2804993987083435}, {"X": 0.4491047263145447, "Y": 0.28127408027648926}, {"X": 0.4489489495754242, "Y": 0.29369401931762695}, {"X": 0.38757044076919556, "Y": 0.2928977608680725}]}, "Id": "b6d278f0-09b2-4e46-b026-90ad08ae440d"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023786205798387527, "Height": 0.012543823570013046, "Left": 0.4626399278640747, "Top": 0.28125786781311035}, "Polygon": [{"X": 0.4627978205680847, "Y": 0.28125786781311035}, {"X": 0.48642614483833313, "Y": 0.2815558612346649}, {"X": 0.4862605631351471, "Y": 0.2938016951084137}, {"X": 0.4626399278640747, "Y": 0.2934955358505249}]}, "Id": "86d756f4-39e6-4f1b-ad26-f89dc9119007"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0493994802236557, "Height": 0.016330813989043236, "Left": 0.2617838978767395, "Top": 0.31581851840019226}, "Polygon": [{"X": 0.2619035840034485, "Y": 0.31581851840019226}, {"X": 0.311183363199234, "Y": 0.31649261713027954}, {"X": 0.31104332208633423, "Y": 0.33214932680130005}, {"X": 0.2617838978767395, "Y": 0.3314533233642578}]}, "Id": "4267d89e-f32e-4ec4-a7a0-be4c792054f4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09891011565923691, "Height": 0.017453959211707115, "Left": 0.32509714365005493, "Top": 0.31621211767196655}, "Polygon": [{"X": 0.3252467215061188, "Y": 0.31621211767196655}, {"X": 0.42400723695755005, "Y": 0.3175617456436157}, {"X": 0.42381563782691956, "Y": 0.333666056394577}, {"X": 0.32509714365005493, "Y": 0.3322714567184448}]}, "Id": "b5413338-c88f-43a0-9b43-572cd36718f3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06159638985991478, "Height": 0.014306023716926575, "Left": 0.4494876563549042, "Top": 0.31724458932876587}, "Polygon": [{"X": 0.4496566653251648, "Y": 0.31724458932876587}, {"X": 0.5110840201377869, "Y": 0.31808286905288696}, {"X": 0.5108931064605713, "Y": 0.33155059814453125}, {"X": 0.4494876563549042, "Y": 0.330689013004303}]}, "Id": "16fc98e6-5676-40a9-818a-2a2359a664e8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02440033294260502, "Height": 0.012667212635278702, "Left": 0.27428504824638367, "Top": 0.3346606492996216}, "Polygon": [{"X": 0.2743833661079407, "Y": 0.3346606492996216}, {"X": 0.29868537187576294, "Y": 0.33500611782073975}, {"X": 0.2985791265964508, "Y": 0.34732785820961}, {"X": 0.27428504824638367, "Y": 0.34697386622428894}]}, "Id": "eacc98e3-5cf8-431a-a6ee-7a69d301a773"}, {"BlockType": "WORD", "Confidence": 99.6534194946289, "Text": "109.316", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08627577126026154, "Height": 0.01363332662731409, "Left": 0.33758214116096497, "Top": 0.33547544479370117}, "Polygon": [{"X": 0.3377015292644501, "Y": 0.33547544479370117}, {"X": 0.4238578975200653, "Y": 0.33670005202293396}, {"X": 0.4237102270126343, "Y": 0.3491087853908539}, {"X": 0.33758214116096497, "Y": 0.34785395860671997}]}, "Id": "e66c5fb9-8cd4-459b-a3c5-773223bbcffe"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "3.599", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060746703296899796, "Height": 0.013154217973351479, "Left": 0.4497015178203583, "Top": 0.33683493733406067}, "Polygon": [{"X": 0.4498559236526489, "Y": 0.33683493733406067}, {"X": 0.5104482173919678, "Y": 0.33769577741622925}, {"X": 0.5102740526199341, "Y": 0.3499891459941864}, {"X": 0.4497015178203583, "Y": 0.3491073250770569}]}, "Id": "7c68de9b-6f04-46eb-8f1a-716489b4a9bd"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0874345600605011, "Height": 0.013880101963877678, "Left": 0.26115643978118896, "Top": 0.37129026651382446}, "Polygon": [{"X": 0.26125216484069824, "Y": 0.37129026651382446}, {"X": 0.34859099984169006, "Y": 0.3726244270801544}, {"X": 0.34846630692481995, "Y": 0.3851703703403473}, {"X": 0.26115643978118896, "Y": 0.38380521535873413}]}, "Id": "5e9a563f-21a2-407f-8a4f-24042fa3d3ce"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07345481961965561, "Height": 0.013332904316484928, "Left": 0.43638309836387634, "Top": 0.3739773631095886}, "Polygon": [{"X": 0.4365323483943939, "Y": 0.3739773631095886}, {"X": 0.5098379254341125, "Y": 0.3750971555709839}, {"X": 0.5096648931503296, "Y": 0.387310266494751}, {"X": 0.43638309836387634, "Y": 0.38616523146629333}]}, "Id": "97230c78-097d-431f-9f38-db5bd76e8fdc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049540601670742035, "Height": 0.012542606331408024, "Left": 0.26098576188087463, "Top": 0.3905240595340729}, "Polygon": [{"X": 0.26107558608055115, "Y": 0.3905240595340729}, {"X": 0.3105263411998749, "Y": 0.3913068175315857}, {"X": 0.3104211390018463, "Y": 0.4030666649341583}, {"X": 0.26098576188087463, "Y": 0.4022674262523651}]}, "Id": "475dbd70-19a9-4071-bfc8-7faec40def70"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "393.43", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07265445590019226, "Height": 0.013539855368435383, "Left": 0.43667861819267273, "Top": 0.39233332872390747}, "Polygon": [{"X": 0.4368302524089813, "Y": 0.39233332872390747}, {"X": 0.509333074092865, "Y": 0.39347895979881287}, {"X": 0.5091575980186462, "Y": 0.40587317943573}, {"X": 0.43667861819267273, "Y": 0.40470224618911743}]}, "Id": "580de27d-3874-4065-a13f-e88cfeb8e252"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06326180696487427, "Height": 0.012959182262420654, "Left": 0.2595531642436981, "Top": 0.42735859751701355}, "Polygon": [{"X": 0.2596436142921448, "Y": 0.42735859751701355}, {"X": 0.3228149712085724, "Y": 0.42842552065849304}, {"X": 0.32270464301109314, "Y": 0.4403177797794342}, {"X": 0.2595531642436981, "Y": 0.4392295777797699}]}, "Id": "ed5c5be8-a31f-4114-a860-a626f333932b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "393.43", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07262898981571198, "Height": 0.013480834662914276, "Left": 0.42377662658691406, "Top": 0.42916810512542725}, "Polygon": [{"X": 0.423922598361969, "Y": 0.42916810512542725}, {"X": 0.49640563130378723, "Y": 0.430390328168869}, {"X": 0.49623602628707886, "Y": 0.4426489472389221}, {"X": 0.42377662658691406, "Y": 0.44140174984931946}]}, "Id": "2d205fdd-cfbc-4742-b043-e1e35ee927fc"}, {"BlockType": "WORD", "Confidence": 99.48979949951172, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0998363271355629, "Height": 0.016141079366207123, "Left": 0.2607004642486572, "Top": 0.4632772207260132}, "Polygon": [{"X": 0.26081007719039917, "Y": 0.4632772207260132}, {"X": 0.3605368137359619, "Y": 0.4650645852088928}, {"X": 0.3603892922401428, "Y": 0.4794182777404785}, {"X": 0.2607004642486572, "Y": 0.*****************}]}, "Id": "3c9777a6-137f-4e19-b9e8-89f1bb21b7db"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04917534813284874, "Height": 0.012644547037780285, "Left": 0.3726806044578552, "Top": 0.46522894501686096}, "Polygon": [{"X": 0.3728051483631134, "Y": 0.46522894501686096}, {"X": 0.42185595631599426, "Y": 0.4661079943180084}, {"X": 0.4217160642147064, "Y": 0.4778735041618347}, {"X": 0.3726806044578552, "Y": 0.47697821259498596}]}, "Id": "56e65b4c-f7c4-4e1c-822e-2a252ffe4d93"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060058169066905975, "Height": 0.01331726461648941, "Left": 0.****************, "Top": 0.*****************}, "Polygon": [{"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "defaea6e-e8aa-4b11-bc30-63e2985031ef"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06274625658988953, "Height": 0.012975305318832397, "Left": 0.2608206868171692, "Top": 0.***************}, "Polygon": [{"X": 0.26091110706329346, "Y": 0.***************}, {"X": 0.3235669434070587, "Y": 0.*****************}, {"X": 0.32345691323280334, "Y": 0.****************}, {"X": 0.2608206868171692, "Y": 0.****************}]}, "Id": "51c93ced-0311-4207-a04d-5f14c54dc2bf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03458234295248985, "Height": 0.012293526902794838, "Left": 0.3379387855529785, "Top": 0.*****************}, "Polygon": [{"X": 0.3380516469478607, "Y": 0.*****************}, {"X": 0.37252113223075867, "Y": 0.****************}, {"X": 0.3723975718021393, "Y": 0.*****************}, {"X": 0.3379387855529785, "Y": 0.*****************}]}, "Id": "e8ac30e5-520b-46d3-856d-dfbc2a3b749b"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049837470054626465, "Height": 0.013454834930598736, "Left": 0.2599608302116394, "Top": 0.5182872414588928}, "Polygon": [{"X": 0.26005616784095764, "Y": 0.5182872414588928}, {"X": 0.30979830026626587, "Y": 0.5192574858665466}, {"X": 0.30968648195266724, "Y": 0.5317420959472656}, {"X": 0.2599608302116394, "Y": 0.****************}]}, "Id": "bd8f4199-61fb-4664-866e-7f692b958a6a"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013102187775075436, "Height": 0.012465604580938816, "Left": 0.3223341703414917, "Top": 0.5190519094467163}, "Polygon": [{"X": 0.32244759798049927, "Y": 0.5190519094467163}, {"X": 0.3354363739490509, "Y": 0.****************}, {"X": 0.33531874418258667, "Y": 0.531517505645752}, {"X": 0.3223341703414917, "Y": 0.5312598943710327}]}, "Id": "b0e98d0a-3f9a-4950-8595-bd1353163486"}, {"BlockType": "WORD", "Confidence": 99.41346740722656, "Text": "P40214", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07211124151945114, "Height": 0.013514695689082146, "Left": 0.434641033411026, "Top": 0.5194631814956665}, "Polygon": [{"X": 0.43478909134864807, "Y": 0.5194631814956665}, {"X": 0.5067522525787354, "Y": 0.5208622813224792}, {"X": 0.5065809488296509, "Y": 0.5329778790473938}, {"X": 0.434641033411026, "Y": 0.5315543413162231}]}, "Id": "5c481ad4-080e-4f5d-9fcb-b2b2497a9362"}, {"BlockType": "WORD", "Confidence": 98.95388793945312, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1082005426287651, "Height": 0.015308885835111141, "Left": 0.26006680727005005, "Top": 0.5358557105064392}, "Polygon": [{"X": 0.26016712188720703, "Y": 0.5358557105064392}, {"X": 0.36826732754707336, "Y": 0.5380189418792725}, {"X": 0.36812931299209595, "Y": 0.5511646270751953}, {"X": 0.26006680727005005, "Y": 0.5489614009857178}]}, "Id": "16e16531-7325-485a-9046-ef737d73a438"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09717338532209396, "Height": 0.014534655027091503, "Left": 0.3849482536315918, "Top": 0.5367103219032288}, "Polygon": [{"X": 0.38508573174476624, "Y": 0.5367103219032288}, {"X": 0.48212164640426636, "Y": 0.5386475324630737}, {"X": 0.48195159435272217, "Y": 0.5512449741363525}, {"X": 0.3849482536315918, "Y": 0.5492734313011169}]}, "Id": "8a8d75a4-1f38-4e10-8f03-e487f43ac40d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04945117235183716, "Height": 0.013839442282915115, "Left": 0.2722238302230835, "Top": 0.5545339584350586}, "Polygon": [{"X": 0.2723259925842285, "Y": 0.5545339584350586}, {"X": 0.32167500257492065, "Y": 0.5555476546287537}, {"X": 0.3215560019016266, "Y": 0.5683733820915222}, {"X": 0.2722238302230835, "Y": 0.5673418641090393}]}, "Id": "151d32a4-f5ab-4a4e-9e4f-ba2997648781"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05730075389146805, "Height": 0.013173053041100502, "Left": 0.3353986442089081, "Top": 0.5552175641059875}, "Polygon": [{"X": 0.3355141580104828, "Y": 0.5552175641059875}, {"X": 0.39269939064979553, "Y": 0.5563912391662598}, {"X": 0.39256560802459717, "Y": 0.5683906674385071}, {"X": 0.3353986442089081, "Y": 0.5671976804733276}]}, "Id": "f45e37e4-1bac-4c28-b459-9270a1c5ffee"}, {"BlockType": "WORD", "Confidence": 99.33294677734375, "Text": "P40214", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07243652641773224, "Height": 0.013526815921068192, "Left": 0.40911656618118286, "Top": 0.5554838180541992}, "Polygon": [{"X": 0.40925583243370056, "Y": 0.5554838180541992}, {"X": 0.4815531075000763, "Y": 0.5569650530815125}, {"X": 0.4813906252384186, "Y": 0.5690106153488159}, {"X": 0.40911656618118286, "Y": 0.5675049424171448}]}, "Id": "f3d42683-d30c-40c7-8508-5c256342b487"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049944594502449036, "Height": 0.014266706071794033, "Left": 0.258836954832077, "Top": 0.5910068154335022}, "Polygon": [{"X": 0.2589375078678131, "Y": 0.5910068154335022}, {"X": 0.30878156423568726, "Y": 0.5920833945274353}, {"X": 0.308663547039032, "Y": 0.6052735447883606}, {"X": 0.258836954832077, "Y": 0.6041784286499023}]}, "Id": "a972fdb1-aea1-4d01-a3a9-df70de6b42f6"}, {"BlockType": "WORD", "Confidence": 99.05691528320312, "Text": "REWARDS/LOYALTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18384914100170135, "Height": 0.019664503633975983, "Left": 0.3217613399028778, "Top": 0.5890975594520569}, "Polygon": [{"X": 0.3219066262245178, "Y": 0.5890975594520569}, {"X": 0.505610466003418, "Y": 0.593048095703125}, {"X": 0.5053883194923401, "Y": 0.6087620258331299}, {"X": 0.3217613399028778, "Y": 0.6047306656837463}]}, "Id": "afdb7367-aa5d-41a5-ac93-1e40e31ce5a3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06290282309055328, "Height": 0.01455801259726286, "Left": 0.25863221287727356, "Top": 0.609231173992157}, "Polygon": [{"X": 0.25873252749443054, "Y": 0.609231173992157}, {"X": 0.32153505086898804, "Y": 0.6106206178665161}, {"X": 0.32141274213790894, "Y": 0.6237891912460327}, {"X": 0.25863221287727356, "Y": 0.6223765015602112}]}, "Id": "235a3bbf-a198-4cc8-aaf4-c43c8caaaa04"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03411258012056351, "Height": 0.01282087154686451, "Left": 0.3359746038913727, "Top": 0.6097902655601501}, "Polygon": [{"X": 0.3360912501811981, "Y": 0.6097902655601501}, {"X": 0.***************, "Y": 0.6105412244796753}, {"X": 0.36995962262153625, "Y": 0.6226111054420471}, {"X": 0.3359746038913727, "Y": 0.6218485832214355}]}, "Id": "aef3a14b-7895-43f8-b635-765539931cb5"}, {"BlockType": "WORD", "Confidence": 89.98086547851562, "Text": "XXXXXXXXXXXX6391", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1955157369375229, "Height": 0.020496027544140816, "Left": 0.25730758905410767, "Top": 0.6249175667762756}, "Polygon": [{"X": 0.25742918252944946, "Y": 0.6249175667762756}, {"X": 0.*****************, "Y": 0.6293287873268127}, {"X": 0.*****************, "Y": 0.6454136371612549}, {"X": 0.25730758905410767, "Y": 0.6409143805503845}]}, "Id": "4b79f7ee-eaa0-494f-82da-c9da534f0e96"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04957182705402374, "Height": 0.014504090882837772, "Left": 0.25791874527931213, "Top": 0.6454676389694214}, "Polygon": [{"X": 0.25802040100097656, "Y": 0.6454676389694214}, {"X": 0.30749058723449707, "Y": 0.6466137170791626}, {"X": 0.3073713481426239, "Y": 0.6599717736244202}, {"X": 0.25791874527931213, "Y": 0.658807098865509}]}, "Id": "f1641fe5-43fe-4722-b90e-bfcc183ee9a1"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013063713908195496, "Height": 0.012176815420389175, "Left": 0.31991466879844666, "Top": 0.6464493870735168}, "Polygon": [{"X": 0.3200245797634125, "Y": 0.6464493870735168}, {"X": 0.33297839760780334, "Y": 0.6467493176460266}, {"X": 0.3328644037246704, "Y": 0.6586261987686157}, {"X": 0.31991466879844666, "Y": 0.6583219170570374}]}, "Id": "e68961bb-f2b2-4109-8377-1b67c3f6b508"}, {"BlockType": "WORD", "Confidence": 94.38875579833984, "Text": "U:4395515", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10962565988302231, "Height": 0.015313642099499702, "Left": 0.39496397972106934, "Top": 0.6459076404571533}, "Polygon": [{"X": 0.395107239484787, "Y": 0.6459076404571533}, {"X": 0.5045896768569946, "Y": 0.6484354138374329}, {"X": 0.5044089555740356, "Y": 0.6612212657928467}, {"X": 0.39496397972106934, "Y": 0.6586543917655945}]}, "Id": "b334c024-21bc-40a1-b7cc-506621e3a698"}, {"BlockType": "WORD", "Confidence": 98.73246002197266, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10777408629655838, "Height": 0.01711617223918438, "Left": 0.25805002450942993, "Top": 0.6625954508781433}, "Polygon": [{"X": 0.258160799741745, "Y": 0.6625954508781433}, {"X": 0.3658241033554077, "Y": 0.6651427149772644}, {"X": 0.3656715452671051, "Y": 0.679711639881134}, {"X": 0.25805002450942993, "Y": 0.6771203875541687}]}, "Id": "550a1c77-d610-4001-bc57-808ba92979b8"}, {"BlockType": "WORD", "Confidence": 97.3399658203125, "Text": "APPROVED,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10815921425819397, "Height": 0.017474502325057983, "Left": 0.270129531621933, "Top": 0.681114912033081}, "Polygon": [{"X": 0.27024731040000916, "Y": 0.681114912033081}, {"X": 0.37828874588012695, "Y": 0.6837278008460999}, {"X": 0.37812814116477966, "Y": 0.6985894441604614}, {"X": 0.270129531621933, "Y": 0.6959315538406372}]}, "Id": "11aa07bb-ec5a-4aa6-b583-79c30c384679"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061503998935222626, "Height": 0.013668688014149666, "Left": 0.39285194873809814, "Top": 0.683146059513092}, "Polygon": [{"X": 0.3929881155490875, "Y": 0.683146059513092}, {"X": 0.45435595512390137, "Y": 0.6846285462379456}, {"X": 0.45419976115226746, "Y": 0.696814775466919}, {"X": 0.39285194873809814, "Y": 0.6953113675117493}]}, "Id": "508b4e36-c5f0-44e5-9db4-0b9dfad3be09"}, {"BlockType": "WORD", "Confidence": 98.91402435302734, "Text": "K<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04726089909672737, "Height": 0.014651291072368622, "Left": 0.2699495255947113, "Top": 0.699235737323761}, "Polygon": [{"X": 0.2700565755367279, "Y": 0.699235737323761}, {"X": 0.31721043586730957, "Y": 0.7004006505012512}, {"X": 0.317086398601532, "Y": 0.7138869762420654}, {"X": 0.2699495255947113, "Y": 0.7127041816711426}]}, "Id": "a93a554b-2bba-4f37-a5ac-e841560d995b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rewards", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08487007021903992, "Height": 0.014369803480803967, "Left": 0.33228012919425964, "Top": 0.7003979682922363}, "Polygon": [{"X": 0.3323976695537567, "Y": 0.7003979682922363}, {"X": 0.41715019941329956, "Y": 0.7024908661842346}, {"X": 0.41700485348701477, "Y": 0.7147677540779114}, {"X": 0.33228012919425964, "Y": 0.7126457095146179}]}, "Id": "f8dc831c-9ced-4b19-b18e-ffad19db0622"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060291849076747894, "Height": 0.0133429616689682, "Left": 0.4304647147655487, "Top": 0.7023398280143738}, "Polygon": [{"X": 0.4306090772151947, "Y": 0.7023398280143738}, {"X": 0.4907565712928772, "Y": 0.7038243412971497}, {"X": 0.4905930757522583, "Y": 0.7156828045845032}, {"X": 0.4304647147655487, "Y": 0.7141783833503723}]}, "Id": "cb2cfc42-fedd-418c-8815-9417234387ff"}, {"BlockType": "WORD", "Confidence": 98.99374389648438, "Text": "Points:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08282690495252609, "Height": 0.016448115929961205, "Left": 0.2694297730922699, "Top": 0.7170081734657288}, "Polygon": [{"X": 0.26954352855682373, "Y": 0.7170081734657288}, {"X": 0.3522566854953766, "Y": 0.719093918800354}, {"X": 0.35211125016212463, "Y": 0.7334562540054321}, {"X": 0.2694297730922699, "Y": 0.7313371896743774}]}, "Id": "8afa09b3-c1aa-458f-a49c-ebcd2bedc9b2"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "$11.41", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06986909359693527, "Height": 0.01580897532403469, "Left": 0.36862194538116455, "Top": 0.7184666991233826}, "Polygon": [{"X": 0.36877012252807617, "Y": 0.7184666991233826}, {"X": 0.****************, "Y": 0.7202227711677551}, {"X": 0.43831667304039, "Y": 0.7342756390571594}, {"X": 0.36862194538116455, "Y": 0.732492208480835}]}, "Id": "7947484b-859a-4338-8338-0a5a3d742b85"}, {"BlockType": "WORD", "Confidence": 98.51542663574219, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07227227836847305, "Height": 0.01391790434718132, "Left": 0.2698585093021393, "Top": 0.7369141578674316}, "Polygon": [{"X": 0.26995420455932617, "Y": 0.7369141578674316}, {"X": 0.34213078022003174, "Y": 0.7387755513191223}, {"X": 0.34201183915138245, "Y": 0.7508320808410645}, {"X": 0.2698585093021393, "Y": 0.7489463090896606}]}, "Id": "857611a4-02fe-46c5-a7f7-c701a56881c0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "credit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07246133685112, "Height": 0.015253739431500435, "Left": 0.3561711013317108, "Top": 0.7359936237335205}, "Polygon": [{"X": 0.3563079833984375, "Y": 0.7359936237335205}, {"X": 0.****************, "Y": 0.7378523349761963}, {"X": 0.****************, "Y": 0.7512474060058594}, {"X": 0.3561711013317108, "Y": 0.7493616342544556}]}, "Id": "0fc58fb6-79c5-4d57-8733-c5af3eea3842"}, {"BlockType": "WORD", "Confidence": 98.605712890625, "Text": "earned!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08266082406044006, "Height": 0.01563911698758602, "Left": 0.26961180567741394, "Top": 0.753093957901001}, "Polygon": [{"X": 0.2697186768054962, "Y": 0.753093957901001}, {"X": 0.352272629737854, "Y": 0.7552614212036133}, {"X": 0.3521360754966736, "Y": 0.7687330842018127}, {"X": 0.26961180567741394, "Y": 0.7665345072746277}]}, "Id": "2431d55c-4a8f-4d26-b901-06d64ca367b6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Expires", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08495328575372696, "Height": 0.01885388419032097, "Left": 0.36798515915870667, "Top": 0.7542099952697754}, "Polygon": [{"X": 0.36816033720970154, "Y": 0.7542099952697754}, {"X": 0.452938437461853, "Y": 0.756432294845581}, {"X": 0.45272549986839294, "Y": 0.7730638980865479}, {"X": 0.36798515915870667, "Y": 0.7708022594451904}]}, "Id": "c939bf16-0a08-43da-8cfa-c9be01416354"}, {"BlockType": "WORD", "Confidence": 99.65581512451172, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024988282471895218, "Height": 0.013508345000445843, "Left": 0.4648953378200531, "Top": 0.7571608424186707}, "Polygon": [{"X": 0.4650639593601227, "Y": 0.7571608424186707}, {"X": 0.4898836314678192, "Y": 0.7578117251396179}, {"X": 0.489706426858902, "Y": 0.770669162273407}, {"X": 0.4648953378200531, "Y": 0.7700093984603882}]}, "Id": "b3f016e8-d20e-4caf-b7e1-d27da55bb7b1"}, {"BlockType": "WORD", "Confidence": 99.28472137451172, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023254524916410446, "Height": 0.012599337846040726, "Left": 0.27029895782470703, "Top": 0.772268533706665}, "Polygon": [{"X": 0.2703944146633148, "Y": 0.772268533706665}, {"X": 0.29355350136756897, "Y": 0.7728893160820007}, {"X": 0.29345062375068665, "Y": 0.7848678827285767}, {"X": 0.27029895782470703, "Y": 0.7842392921447754}]}, "Id": "6fa23faa-c0c7-40b0-b59a-a503e7ac6b7b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047292936593294144, "Height": 0.015820981934666634, "Left": 0.3072359263896942, "Top": 0.7724277973175049}, "Polygon": [{"X": 0.30736610293388367, "Y": 0.7724277973175049}, {"X": 0.35452887415885925, "Y": 0.7736908793449402}, {"X": 0.354380339384079, "Y": 0.7882487773895264}, {"X": 0.3072359263896942, "Y": 0.7869664430618286}]}, "Id": "77a12fa5-4860-4289-be60-7278d894c383"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRUCK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062186602503061295, "Height": 0.014596930705010891, "Left": 0.2560611069202423, "Top": 0.8064418435096741}, "Polygon": [{"X": 0.2561587989330292, "Y": 0.8064418435096741}, {"X": 0.3182477056980133, "Y": 0.8081679344177246}, {"X": 0.31812867522239685, "Y": 0.8210387229919434}, {"X": 0.2560611069202423, "Y": 0.819290280342102}]}, "Id": "2df9ee04-edb7-46cf-b55d-8b512bcbfac3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NUMBER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07203010469675064, "Height": 0.013702173717319965, "Left": 0.3312080502510071, "Top": 0.8080692887306213}, "Polygon": [{"X": 0.3313201367855072, "Y": 0.8080692887306213}, {"X": 0.4032381772994995, "Y": 0.8100676536560059}, {"X": 0.4031035304069519, "Y": 0.8217714428901672}, {"X": 0.3312080502510071, "Y": 0.8197495341300964}]}, "Id": "fe2a8bed-6054-4305-a783-fcaf3cf2031c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "02929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059923022985458374, "Height": 0.013419222086668015, "Left": 0.4410970211029053, "Top": 0.8112071752548218}, "Polygon": [{"X": 0.44124388694763184, "Y": 0.8112071752548218}, {"X": 0.501020073890686, "Y": 0.8128683567047119}, {"X": 0.5008543133735657, "Y": 0.8246263861656189}, {"X": 0.4410970211029053, "Y": 0.8229457139968872}]}, "Id": "a2f287ee-9273-43c9-ba49-ecccc4b62b8e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MERCHANT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09787338227033615, "Height": 0.016295175999403, "Left": 0.25607118010520935, "Top": 0.8236886858940125}, "Polygon": [{"X": 0.2561738193035126, "Y": 0.8236886858940125}, {"X": 0.3539445698261261, "Y": 0.826455295085907}, {"X": 0.3538065552711487, "Y": 0.8399838805198669}, {"X": 0.25607118010520935, "Y": 0.8371803164482117}]}, "Id": "8f27d655-0019-4463-a5de-39c6a99a49bb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NUMBER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07231906801462173, "Height": 0.013773309998214245, "Left": 0.3674178123474121, "Top": 0.8267901539802551}, "Polygon": [{"X": 0.36754146218299866, "Y": 0.8267901539802551}, {"X": 0.43973687291145325, "Y": 0.8288329243659973}, {"X": 0.4395905137062073, "Y": 0.8405634760856628}, {"X": 0.3674178123474121, "Y": 0.8384970426559448}]}, "Id": "5721d07e-02ba-47ae-adbe-d324a46454bf"}, {"BlockType": "WORD", "Confidence": 93.54034423828125, "Text": "*******2676", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13414841890335083, "Height": 0.015911469236016273, "Left": 0.2804955542087555, "Top": 0.8420714735984802}, "Polygon": [{"X": 0.28059470653533936, "Y": 0.8420714735984802}, {"X": 0.4146439731121063, "Y": 0.8459328413009644}, {"X": 0.4145015776157379, "Y": 0.8579829931259155}, {"X": 0.2804955542087555, "Y": 0.8540765643119812}]}, "Id": "7c1734d0-bdb6-4ba5-9503-15c18c742b03"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03549572080373764, "Height": 0.013084737583994865, "Left": 0.26911240816116333, "Top": 0.8609384298324585}, "Polygon": [{"X": 0.26920822262763977, "Y": 0.8609384298324585}, {"X": 0.30460813641548157, "Y": 0.8619776964187622}, {"X": 0.304500937461853, "Y": 0.8740231394767761}, {"X": 0.26911240816116333, "Y": 0.8729719519615173}]}, "Id": "e765dd94-e3f8-4745-a3db-ac9201681023"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TAXES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060987621545791626, "Height": 0.013783210888504982, "Left": 0.31701385974884033, "Top": 0.8611904382705688}, "Polygon": [{"X": 0.31712445616722107, "Y": 0.8611904382705688}, {"X": 0.37800148129463196, "Y": 0.8629755973815918}, {"X": 0.3778713047504425, "Y": 0.8749736547470093}, {"X": 0.31701385974884033, "Y": 0.8731680512428284}]}, "Id": "37d63eee-77df-427e-8c7b-8c4ad56f80e3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "INCLUDED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09430620819330215, "Height": 0.014514587819576263, "Left": 0.3931328058242798, "Top": 0.8633200526237488}, "Polygon": [{"X": 0.3932647407054901, "Y": 0.8633200526237488}, {"X": 0.48743903636932373, "Y": 0.8660813570022583}, {"X": 0.4872773289680481, "Y": 0.8778346180915833}, {"X": 0.3931328058242798, "Y": 0.8750424981117249}]}, "Id": "9e25ccbf-92ff-4620-8747-4734c2601a24"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "IN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021528061479330063, "Height": 0.012611019425094128, "Left": 0.295271098613739, "Top": 0.8789135813713074}, "Polygon": [{"X": 0.29537472128868103, "Y": 0.8789135813713074}, {"X": 0.3167991638183594, "Y": 0.8795531392097473}, {"X": 0.3166886866092682, "Y": 0.8915245532989502}, {"X": 0.295271098613739, "Y": 0.8908778429031372}]}, "Id": "74526d0e-0ec0-4215-8bb5-8814a83f839a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04727991670370102, "Height": 0.013335295021533966, "Left": 0.3300810754299164, "Top": 0.8793396949768066}, "Polygon": [{"X": 0.33019524812698364, "Y": 0.8793396949768066}, {"X": 0.377360999584198, "Y": 0.8807468414306641}, {"X": 0.3772317171096802, "Y": 0.89267498254776}, {"X": 0.3300810754299164, "Y": 0.8912521004676819}]}, "Id": "a54c6be1-7b6f-4872-a1ec-1fe17fca7dd5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PRICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0590958334505558, "Height": 0.013495083898305893, "Left": 0.3909558951854706, "Top": 0.881163477897644}, "Polygon": [{"X": 0.3910871744155884, "Y": 0.881163477897644}, {"X": 0.45005175471305847, "Y": 0.8829227089881897}, {"X": 0.44990187883377075, "Y": 0.8946585655212402}, {"X": 0.3909558951854706, "Y": 0.8928801417350769}]}, "Id": "6d9805d9-ad58-417c-b3be-5ca2e027d847"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "6256400d-e85b-4246-8943-02db177e1c40", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "6256400d-e85b-4246-8943-02db177e1c40", "content-type": "application/x-amz-json-1.1", "content-length": "61584", "date": "Wed, 24 Sep 2025 12:47:11 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "155d58f3-68b8-4509-b4c8-b1083efba6ae", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:13 GMT", "content-type": "application/json", "content-length": "885", "connection": "keep-alive", "x-amzn-requestid": "155d58f3-68b8-4509-b4c8-b1083efba6ae"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to classify doc type. The page content: \"KWIK TRIP #1267 ... Invoice# 0098760\". It seems a fuel receipt: has pump quantity, price, product, amount, totals, merchant number, card number, etc. It is a fuel receipt for a fuel purchase. So doc_type: fuel_receipt. Confirm keywords: Fuel? It mentions \"KWIK TRIP\", \"Invoice#\", \"Pump Quantity Price\", \"Product\", \"Total\", \"Merchant Number\", \"Card#\". So yes fuel receipt. Output via function call. Need array of documents: one object with page_no 1 and doc_type \"fuel_receipt\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2738, "outputTokens": 160, "totalTokens": 2898}, "metrics": {"latencyMs": 911}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/2f18e57f_DDRWUNS7W350XYVX3TFS.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nK<PERSON>K TRIP #1267\n1267CH<PERSON><PERSON>EWACROSSING\nCHIPPEWA FALLS, WI\nInvoice# 0098760\nTrans # 0900-8760\nDate\n10/09/2024\nTime\n03:45 PM\nPump Quantity Price\n02 109.316 3.599\nProduct\nAmount\nDSL2\n393.43\nTOTAL\n393.43\nNBS/KWIK TRIP FLEET\nCARD# ICR\nAUTH #\nP40214\nRESPONSE: APPROVED\nAUTH REF#: P40214\nKWIK REWARDS/LOYALTY\nCARD# ICR\nXXXXXXXXXXXX6391\nAUTH #\nU:4395515\nRESPONSE:\nAPPROVED, Total\nKwik Rewards Fleet\nPoints: $11.41\nShower credit\nearned! Expires in\n14 days\nTRUCK NUMBER 02929\nMERCHANT NUMBER\n*******2676\nALL TAXES INCLUDED\nIN FUEL PRICE\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}