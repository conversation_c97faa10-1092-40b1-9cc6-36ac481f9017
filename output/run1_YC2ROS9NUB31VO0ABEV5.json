{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.8905228671428631e-06, "Y": 0.0}, {"X": 1.0, "Y": 1.0289252713846508e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "b7123288-47ee-4d3c-8bd0-e406f2e72b5b", "Relationships": [{"Type": "CHILD", "Ids": ["630b92f4-f744-4d16-b620-ff0c2a4adb19", "6b45c141-f8be-440e-afec-e77c4263ecc2", "193714c7-5213-4057-bce3-ce7c64899cdf", "9dd9fd2b-6baf-443b-81d8-47f496751ff0", "82adc934-9b0e-49b5-b5d2-32450f1c3a74", "f5f82b54-0cd4-4833-b486-cc9657e3f485", "93ae17b9-82bb-401f-ad49-df561690509f", "7ce84f51-8e36-4174-9c8f-36de831a9c24", "68123f2c-e40f-4100-bcef-01527d5ee15e", "df51a504-3ff0-41ae-9735-8bb9041e7e9b", "d52e2a19-6560-4ecb-8e11-9560f0f358d3", "b6123011-48d6-4607-811d-8e553e103c77", "271d2f05-8b25-422f-a73c-99024a9ec023", "347bc013-a101-47a0-9201-87fd9afe8b85", "f95d603e-4bcc-4a3c-88a4-8d30f45788b0", "bd56452e-a326-4af8-9b0d-f01461abce6f", "b5f34c9c-a282-4b77-b7ed-e1fc8b060c79", "3317f917-720e-4757-ad38-8bec2f7bc3f4", "f1eeb515-e85d-4773-a277-a0ca82c65302", "614092d9-02e5-4e7b-b97d-4f23fca6ca85", "813491cc-87dd-49bc-a7b7-85d8b0193eaa", "cc2b3e43-2c4a-45e1-8b23-eb67ccb84579", "a1035863-abfd-49be-95bf-133da33e203e", "2856ceea-d973-4d57-8011-290cee59881b", "79a2c8af-3c8e-49b8-9e7a-2264061d5f8b", "cde03e05-e739-4b15-b742-ebe761872d5f", "c89a478d-dbb0-493d-8feb-a2702694023a", "12006be3-4180-486b-9ced-43d74750c9e2", "a91da7a8-c4d8-4a2f-8c1f-94937b635904", "197c4982-f1c3-4045-896f-0fb41a9ae41f", "9126f7ad-bbe7-408a-abab-8f3cf4eff495"]}]}, {"BlockType": "LINE", "Confidence": 99.86288452148438, "Text": "FAB-MASTERS, INC.", "Geometry": {"BoundingBox": {"Width": 0.33993303775787354, "Height": 0.027209147810935974, "Left": 0.11321188509464264, "Top": 0.06286675482988358}, "Polygon": [{"X": 0.11329008638858795, "Y": 0.06286675482988358}, {"X": 0.453144907951355, "Y": 0.0630674734711647}, {"X": 0.4530876874923706, "Y": 0.09007590264081955}, {"X": 0.11321188509464264, "Y": 0.08987520635128021}]}, "Id": "630b92f4-f744-4d16-b620-ff0c2a4adb19", "Relationships": [{"Type": "CHILD", "Ids": ["d6b343e6-1dd5-462b-84d2-6d3a8a2a7792", "13d6f869-94ca-42f1-8076-e2f406c55deb"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Packing Slip", "Geometry": {"BoundingBox": {"Width": 0.25006216764450073, "Height": 0.03609446808695793, "Left": 0.6520259976387024, "Top": 0.04386517405509949}, "Polygon": [{"X": 0.6520857810974121, "Y": 0.04386517405509949}, {"X": 0.9020881652832031, "Y": 0.04401284083724022}, {"X": 0.9020488262176514, "Y": 0.07995964586734772}, {"X": 0.6520259976387024, "Y": 0.07981199771165848}]}, "Id": "6b45c141-f8be-440e-afec-e77c4263ecc2", "Relationships": [{"Type": "CHILD", "Ids": ["53f22011-21ea-416c-99da-8ebff937c4bb", "67d18dcd-c8f3-48f8-b548-0e7c0a4ada30"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "42011", "Geometry": {"BoundingBox": {"Width": 0.03689040616154671, "Height": 0.00999403651803732, "Left": 0.8584620952606201, "Top": 0.08601761609315872}, "Polygon": [{"X": 0.8584739565849304, "Y": 0.08601761609315872}, {"X": 0.8953524827957153, "Y": 0.08603939414024353}, {"X": 0.8953414559364319, "Y": 0.09601165354251862}, {"X": 0.8584620952606201, "Y": 0.09598987549543381}]}, "Id": "193714c7-5213-4057-bce3-ce7c64899cdf", "Relationships": [{"Type": "CHILD", "Ids": ["aa256b76-8307-4d59-8fe7-9e7befc57da0"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Address:", "Geometry": {"BoundingBox": {"Width": 0.06147180125117302, "Height": 0.009599120356142521, "Left": 0.10660583525896072, "Top": 0.14761248230934143}, "Polygon": [{"X": 0.10663367062807083, "Y": 0.14761248230934143}, {"X": 0.16807764768600464, "Y": 0.14764876663684845}, {"X": 0.16805115342140198, "Y": 0.15721160173416138}, {"X": 0.10660583525896072, "Y": 0.15717533230781555}]}, "Id": "9dd9fd2b-6baf-443b-81d8-47f496751ff0", "Relationships": [{"Type": "CHILD", "Ids": ["695e6ee5-ae24-4dfc-bedd-77050e03c14c"]}]}, {"BlockType": "LINE", "Confidence": 99.67919921875, "Text": "901 Joliet St", "Geometry": {"BoundingBox": {"Width": 0.08280424773693085, "Height": 0.009933996014297009, "Left": 0.3148934245109558, "Top": 0.14928953349590302}, "Polygon": [{"X": 0.3149174749851227, "Y": 0.14928953349590302}, {"X": 0.39769765734672546, "Y": 0.14933840930461884}, {"X": 0.39767545461654663, "Y": 0.1592235267162323}, {"X": 0.3148934245109558, "Y": 0.15917466580867767}]}, "Id": "82adc934-9b0e-49b5-b5d2-32450f1c3a74", "Relationships": [{"Type": "CHILD", "Ids": ["9dda9c36-ae93-40a1-8d07-912f688e16bb", "eb4ede17-bcfd-43b7-92d5-28df5ffa01f0", "336bf2af-eb84-465a-a593-e2b7a179d111"]}]}, {"BlockType": "LINE", "Confidence": 99.9133529663086, "Text": "Ship To: RexCon LLC", "Geometry": {"BoundingBox": {"Width": 0.14269210398197174, "Height": 0.011826923117041588, "Left": 0.5503053069114685, "Top": 0.1514817178249359}, "Polygon": [{"X": 0.5503275394439697, "Y": 0.1514817178249359}, {"X": 0.692997395992279, "Y": 0.15156593918800354}, {"X": 0.6929789781570435, "Y": 0.16330863535404205}, {"X": 0.5503053069114685, "Y": 0.16322441399097443}]}, "Id": "f5f82b54-0cd4-4833-b486-cc9657e3f485", "Relationships": [{"Type": "CHILD", "Ids": ["3200531d-e0d6-4307-ac46-f0f77b297cfe", "6e58ee11-403e-40f3-ae66-13d2e51d9db8", "c0dbdf7f-c9bd-48c5-a68d-18f2812f2ba0", "25526865-3262-4314-8249-5c965e5935e1"]}]}, {"BlockType": "LINE", "Confidence": 99.94386291503906, "Text": "Janesville, WI 53548", "Geometry": {"BoundingBox": {"Width": 0.1387762427330017, "Height": 0.011427939869463444, "Left": 0.31422558426856995, "Top": 0.16664530336856842}, "Polygon": [{"X": 0.3142532408237457, "Y": 0.16664530336856842}, {"X": 0.45300182700157166, "Y": 0.1667272001504898}, {"X": 0.4529778063297272, "Y": 0.1780732423067093}, {"X": 0.31422558426856995, "Y": 0.1779913455247879}]}, "Id": "93ae17b9-82bb-401f-ad49-df561690509f", "Relationships": [{"Type": "CHILD", "Ids": ["643210ec-2adf-454c-8a70-699ba3b51d06", "9084c5cb-c9ba-4517-8ece-dec55c21826b", "873963e9-6d0a-40b8-bf9a-e00108cd748a"]}]}, {"BlockType": "LINE", "Confidence": 99.92080688476562, "Text": "2841 Whiting Rd.", "Geometry": {"BoundingBox": {"Width": 0.12023106217384338, "Height": 0.012425738386809826, "Left": 0.6099357604980469, "Top": 0.1702669858932495}, "Polygon": [{"X": 0.6099575161933899, "Y": 0.1702669858932495}, {"X": 0.7301667928695679, "Y": 0.17033794522285461}, {"X": 0.730148434638977, "Y": 0.1826927214860916}, {"X": 0.6099357604980469, "Y": 0.1826217770576477}]}, "Id": "7ce84f51-8e36-4174-9c8f-36de831a9c24", "Relationships": [{"Type": "CHILD", "Ids": ["86c8ed2f-1535-46f4-8736-cf6b4121d3df", "ba0c17e6-524c-4a04-98d1-903fa950d3e8", "a2aecf3a-aa3d-4206-9c28-b4b0168dc5fe"]}]}, {"BlockType": "LINE", "Confidence": 99.91619873046875, "Text": "Phone: ************", "Geometry": {"BoundingBox": {"Width": 0.13726486265659332, "Height": 0.010705206543207169, "Left": 0.31468331813812256, "Top": 0.18416982889175415}, "Polygon": [{"X": 0.3147091567516327, "Y": 0.18416982889175415}, {"X": 0.4519481658935547, "Y": 0.1842508316040039}, {"X": 0.4519256353378296, "Y": 0.19487503170967102}, {"X": 0.31468331813812256, "Y": 0.19479402899742126}]}, "Id": "68123f2c-e40f-4100-bcef-01527d5ee15e", "Relationships": [{"Type": "CHILD", "Ids": ["ce9597e6-98bb-4447-9a0e-0e2686dabbb3", "18c59f8c-b01b-495e-83b9-a356a9f4faa0"]}]}, {"BlockType": "LINE", "Confidence": 99.87470245361328, "Text": "Burlington, WI 53105", "Geometry": {"BoundingBox": {"Width": 0.14988215267658234, "Height": 0.011947562918066978, "Left": 0.6102584004402161, "Top": 0.1879032999277115}, "Polygon": [{"X": 0.6102792620658875, "Y": 0.1879032999277115}, {"X": 0.7601405382156372, "Y": 0.18799175322055817}, {"X": 0.7601237297058105, "Y": 0.19985085725784302}, {"X": 0.6102584004402161, "Y": 0.19976241886615753}]}, "Id": "df51a504-3ff0-41ae-9735-8bb9041e7e9b", "Relationships": [{"Type": "CHILD", "Ids": ["abcfa978-6ac0-4993-b9ec-8f169044659e", "7f1fe872-ffcc-4ba6-ab13-c73c37f62fb1", "8ee30fe0-b2b2-4f81-8943-eed489c3afaa"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Fax: ************", "Geometry": {"BoundingBox": {"Width": 0.12015782296657562, "Height": 0.010407835245132446, "Left": 0.3143838346004486, "Top": 0.2019696831703186}, "Polygon": [{"X": 0.3144090175628662, "Y": 0.2019696831703186}, {"X": 0.4345416724681854, "Y": 0.20204058289527893}, {"X": 0.43451932072639465, "Y": 0.21237751841545105}, {"X": 0.3143838346004486, "Y": 0.21230661869049072}]}, "Id": "d52e2a19-6560-4ecb-8e11-9560f0f358d3", "Relationships": [{"Type": "CHILD", "Ids": ["ccdc1f85-79be-482e-b639-5caa590ca253", "1ed3458f-ebe3-4ab0-a1db-7e69b7e0a01b"]}]}, {"BlockType": "LINE", "Confidence": 99.95416259765625, "Text": "Ship Date: 9/19/2025", "Geometry": {"BoundingBox": {"Width": 0.15725833177566528, "Height": 0.012729189358651638, "Left": 0.10295334458351135, "Top": 0.23474736511707306}, "Polygon": [{"X": 0.10299021750688553, "Y": 0.23474736511707306}, {"X": 0.26021167635917664, "Y": 0.23484013974666595}, {"X": 0.260179340839386, "Y": 0.24747654795646667}, {"X": 0.10295334458351135, "Y": 0.24738378822803497}]}, "Id": "b6123011-48d6-4607-811d-8e553e103c77", "Relationships": [{"Type": "CHILD", "Ids": ["02f92b01-fa5d-453a-8d9e-25fee3e25f61", "53df6e66-e655-44f6-93bf-d28d71e9ae7d", "87e96291-6708-43cb-ae9a-a355db0f625f"]}]}, {"BlockType": "LINE", "Confidence": 99.9574203491211, "Text": "Sales Person: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.1292000561952591, "Height": 0.011380664072930813, "Left": 0.10228630900382996, "Top": 0.25242120027542114}, "Polygon": [{"X": 0.10231930762529373, "Y": 0.25242120027542114}, {"X": 0.23148636519908905, "Y": 0.2524974048137665}, {"X": 0.2314566969871521, "Y": 0.2638018727302551}, {"X": 0.10228630900382996, "Y": 0.2637256383895874}]}, "Id": "271d2f05-8b25-422f-a73c-99024a9ec023", "Relationships": [{"Type": "CHILD", "Ids": ["3d0692b9-d8ed-4186-bfde-dbcc500e25af", "ed9ba64f-7607-42e5-9fc1-bb83f1dd7f14", "42acdba9-f1e5-495c-bc34-7ab0a7c23d92"]}]}, {"BlockType": "LINE", "Confidence": 99.90154266357422, "Text": "Bill To: <PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.09988313913345337, "Height": 0.010135631076991558, "Left": 0.550470769405365, "Top": 0.2580232620239258}, "Polygon": [{"X": 0.5504899024963379, "Y": 0.2580232620239258}, {"X": 0.6503539681434631, "Y": 0.25808218121528625}, {"X": 0.6503371596336365, "Y": 0.268158882856369}, {"X": 0.550470769405365, "Y": 0.26809996366500854}]}, "Id": "347bc013-a101-47a0-9201-87fd9afe8b85", "Relationships": [{"Type": "CHILD", "Ids": ["54ea28c9-9c3e-48ae-9a7f-a7b037126a9a", "ee6ce067-5fc0-453b-a858-e5dd61a9f26b", "2a40f3dc-6b7c-4de0-a0fe-304eba15c8e4"]}]}, {"BlockType": "LINE", "Confidence": 99.86494445800781, "Text": "Purchase Order: PUR3165-105166", "Geometry": {"BoundingBox": {"Width": 0.24311040341854095, "Height": 0.012326186522841454, "Left": 0.10205425322055817, "Top": 0.27028441429138184}, "Polygon": [{"X": 0.10208982974290848, "Y": 0.27028441429138184}, {"X": 0.3451646566390991, "Y": 0.2704278230667114}, {"X": 0.3451358675956726, "Y": 0.28261059522628784}, {"X": 0.10205425322055817, "Y": 0.28246718645095825}]}, "Id": "f95d603e-4bcc-4a3c-88a4-8d30f45788b0", "Relationships": [{"Type": "CHILD", "Ids": ["7fbfa2c9-8903-4afb-9649-285c0f751c10", "0176d1fd-33aa-4901-a0c4-2f40b16f5d27", "ff2d508c-6a07-40bb-9c1e-4eaa684bb15d"]}]}, {"BlockType": "LINE", "Confidence": 99.86819458007812, "Text": "Shipped Via: CPU", "Geometry": {"BoundingBox": {"Width": 0.13529877364635468, "Height": 0.01282179169356823, "Left": 0.10153787583112717, "Top": 0.2868586480617523}, "Polygon": [{"X": 0.1015750914812088, "Y": 0.2868586480617523}, {"X": 0.23683665692806244, "Y": 0.2869384288787842}, {"X": 0.23680338263511658, "Y": 0.2996804416179657}, {"X": 0.10153787583112717, "Y": 0.29960063099861145}]}, "Id": "bd56452e-a326-4af8-9b0d-f01461abce6f", "Relationships": [{"Type": "CHILD", "Ids": ["aabcd2e2-2d80-4a40-af23-6caa0ddd3a47", "f539fb61-0614-4be9-a3dc-fa7db8062c57", "d37b6d6e-7c81-4d31-8778-47db4806e207"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Part Number", "Geometry": {"BoundingBox": {"Width": 0.09081481397151947, "Height": 0.009804426692426205, "Left": 0.3630725145339966, "Top": 0.372030645608902}, "Polygon": [{"X": 0.36309516429901123, "Y": 0.372030645608902}, {"X": 0.45388731360435486, "Y": 0.3720841705799103}, {"X": 0.4538666903972626, "Y": 0.38183507323265076}, {"X": 0.3630725145339966, "Y": 0.38178154826164246}]}, "Id": "b5f34c9c-a282-4b77-b7ed-e1fc8b060c79", "Relationships": [{"Type": "CHILD", "Ids": ["da3605c2-3466-4d43-a388-023989530763", "4c5461a4-b92b-4468-b78c-8ebde88d844f"]}]}, {"BlockType": "LINE", "Confidence": 99.75386810302734, "Text": "Our Job#", "Geometry": {"BoundingBox": {"Width": 0.06422566622495651, "Height": 0.009322538040578365, "Left": 0.5602496862411499, "Top": 0.3742678463459015}, "Polygon": [{"X": 0.5602670311927795, "Y": 0.3742678463459015}, {"X": 0.6244753003120422, "Y": 0.37430569529533386}, {"X": 0.6244592666625977, "Y": 0.38359037041664124}, {"X": 0.5602496862411499, "Y": 0.38355252146720886}]}, "Id": "3317f917-720e-4757-ad38-8bec2f7bc3f4", "Relationships": [{"Type": "CHILD", "Ids": ["b3175135-63ba-471a-a1a6-48c57219d9cb", "bcbe29bf-9aba-48a2-bef5-ec6e7b250bd1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Order Quantity", "Geometry": {"BoundingBox": {"Width": 0.10627743601799011, "Height": 0.011450564488768578, "Left": 0.6574920415878296, "Top": 0.3742363154888153}, "Polygon": [{"X": 0.6575108766555786, "Y": 0.3742363154888153}, {"X": 0.7637695074081421, "Y": 0.37429898977279663}, {"X": 0.7637534737586975, "Y": 0.3856869041919708}, {"X": 0.6574920415878296, "Y": 0.3856242299079895}]}, "Id": "f1eeb515-e85d-4773-a277-a0ca82c65302", "Relationships": [{"Type": "CHILD", "Ids": ["eb180ee8-941f-4f98-9065-9e0af474a930", "6cc40d3b-fa74-4114-aa73-4fa8a2e3a5bc"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Ship Quantity", "Geometry": {"BoundingBox": {"Width": 0.09705446660518646, "Height": 0.011477812193334103, "Left": 0.8052487373352051, "Top": 0.3749060034751892}, "Polygon": [{"X": 0.8052636981010437, "Y": 0.3749060034751892}, {"X": 0.902303159236908, "Y": 0.3749632239341736}, {"X": 0.9022907018661499, "Y": 0.3863838315010071}, {"X": 0.8052487373352051, "Y": 0.3863266110420227}]}, "Id": "614092d9-02e5-4e7b-b97d-4f23fca6ca85", "Relationships": [{"Type": "CHILD", "Ids": ["ba9b8182-87d7-4342-b227-068586960891", "9e472dff-5243-44c1-add4-ce07f557124f"]}]}, {"BlockType": "LINE", "Confidence": 96.53559112548828, "Text": "544-15008-80,RV R05", "Geometry": {"BoundingBox": {"Width": 0.15945522487163544, "Height": 0.012986520305275917, "Left": 0.10191446542739868, "Top": 0.3925946056842804}, "Polygon": [{"X": 0.10195209830999374, "Y": 0.3925946056842804}, {"X": 0.2613696753978729, "Y": 0.3926886022090912}, {"X": 0.2613367438316345, "Y": 0.40558114647865295}, {"X": 0.10191446542739868, "Y": 0.40548714995384216}]}, "Id": "813491cc-87dd-49bc-a7b7-85d8b0193eaa", "Relationships": [{"Type": "CHILD", "Ids": ["797ba4e4-aabf-47c1-84d0-5a5878e7d9d4", "03317b49-7c2a-4d3b-9427-6907f83bdde1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "41941", "Geometry": {"BoundingBox": {"Width": 0.044650498777627945, "Height": 0.009661072865128517, "Left": 0.5616632103919983, "Top": 0.39821141958236694}, "Polygon": [{"X": 0.5616812109947205, "Y": 0.39821141958236694}, {"X": 0.6063137054443359, "Y": 0.3982377350330353}, {"X": 0.6062966585159302, "Y": 0.4078724980354309}, {"X": 0.5616632103919983, "Y": 0.4078461825847626}]}, "Id": "cc2b3e43-2c4a-45e1-8b23-eb67ccb84579", "Relationships": [{"Type": "CHILD", "Ids": ["c2331cce-c3b9-455d-b4e4-07544f28a0b8"]}]}, {"BlockType": "LINE", "Confidence": 99.25801086425781, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.008198490366339684, "Height": 0.009699119254946709, "Left": 0.769997775554657, "Top": 0.39847108721733093}, "Polygon": [{"X": 0.770011305809021, "Y": 0.39847108721733093}, {"X": 0.7781962752342224, "Y": 0.39847591519355774}, {"X": 0.7781829237937927, "Y": 0.408170223236084}, {"X": 0.769997775554657, "Y": 0.4081653952598572}]}, "Id": "a1035863-abfd-49be-95bf-133da33e203e", "Relationships": [{"Type": "CHILD", "Ids": ["85374994-78bc-4978-a48a-a413bc876f9f"]}]}, {"BlockType": "LINE", "Confidence": 98.92279052734375, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008932854048907757, "Height": 0.00949004665017128, "Left": 0.9128589630126953, "Top": 0.3990378975868225}, "Polygon": [{"X": 0.9128690958023071, "Y": 0.3990378975868225}, {"X": 0.9217917919158936, "Y": 0.39904317259788513}, {"X": 0.9217818975448608, "Y": 0.4085279405117035}, {"X": 0.9128589630126953, "Y": 0.40852269530296326}]}, "Id": "2856ceea-d973-4d57-8011-290cee59881b", "Relationships": [{"Type": "CHILD", "Ids": ["f5396b0c-f8fa-41e2-8bda-5803a3679bc4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipping", "Geometry": {"BoundingBox": {"Width": 0.062230851501226425, "Height": 0.012969802133738995, "Left": 0.5624654293060303, "Top": 0.6395642757415771}, "Polygon": [{"X": 0.5624895691871643, "Y": 0.6395642757415771}, {"X": 0.6246963143348694, "Y": 0.6396008729934692}, {"X": 0.6246740221977234, "Y": 0.6525340676307678}, {"X": 0.5624654293060303, "Y": 0.6524974703788757}]}, "Id": "79a2c8af-3c8e-49b8-9e7a-2264061d5f8b", "Relationships": [{"Type": "CHILD", "Ids": ["72210b3f-a50e-49ce-a476-a6b931bb5b19"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.045537348836660385, "Height": 0.010100631043314934, "Left": 0.6931411027908325, "Top": 0.6402238011360168}, "Polygon": [{"X": 0.693156898021698, "Y": 0.6402238011360168}, {"X": 0.7386784553527832, "Y": 0.640250563621521}, {"X": 0.7386637330055237, "Y": 0.6503244042396545}, {"X": 0.6931411027908325, "Y": 0.6502976417541504}]}, "Id": "cde03e05-e739-4b15-b742-ebe761872d5f", "Relationships": [{"Type": "CHILD", "Ids": ["0b549772-1361-4b5c-9885-63e3945e9e97"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Inspector", "Geometry": {"BoundingBox": {"Width": 0.07192175835371017, "Height": 0.012350550852715969, "Left": 0.8308875560760498, "Top": 0.6396564841270447}, "Polygon": [{"X": 0.8309029936790466, "Y": 0.6396564841270447}, {"X": 0.9028093218803406, "Y": 0.6396988034248352}, {"X": 0.9027959108352661, "Y": 0.6520070433616638}, {"X": 0.8308875560760498, "Y": 0.6519647240638733}]}, "Id": "c89a478d-dbb0-493d-8feb-a2702694023a", "Relationships": [{"Type": "CHILD", "Ids": ["79746bca-1c75-4c59-8e4a-da1dd7148994"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipment visually", "Geometry": {"BoundingBox": {"Width": 0.1329101324081421, "Height": 0.01392030157148838, "Left": 0.09316544234752655, "Top": 0.6584966778755188}, "Polygon": [{"X": 0.09320610761642456, "Y": 0.6584966778755188}, {"X": 0.22607557475566864, "Y": 0.6585748791694641}, {"X": 0.2260391116142273, "Y": 0.6724169850349426}, {"X": 0.09316544234752655, "Y": 0.6723387241363525}]}, "Id": "12006be3-4180-486b-9ced-43d74750c9e2", "Relationships": [{"Type": "CHILD", "Ids": ["0ce133b4-f428-4f13-8bf2-e45459078b9b", "4fc8e9d7-dd60-4fa7-af43-c7da8c6aa4b7"]}]}, {"BlockType": "LINE", "Confidence": 99.97694396972656, "Text": "checked for damage", "Geometry": {"BoundingBox": {"Width": 0.15215478837490082, "Height": 0.013631144538521767, "Left": 0.09157972782850266, "Top": 0.6746164560317993}, "Polygon": [{"X": 0.09161955118179321, "Y": 0.6746164560317993}, {"X": 0.24373450875282288, "Y": 0.6747060418128967}, {"X": 0.24369938671588898, "Y": 0.6882476210594177}, {"X": 0.09157972782850266, "Y": 0.6881580948829651}]}, "Id": "a91da7a8-c4d8-4a2f-8c1f-94937b635904", "Relationships": [{"Type": "CHILD", "Ids": ["3d1019b0-dee5-4a2a-81c6-20bf9aff8856", "17e14fa5-e66a-44df-be08-55e8aad33420", "3d4ec62e-9784-4b68-b47e-312d307288a4"]}]}, {"BlockType": "LINE", "Confidence": 80.82644653320312, "Text": "Received by: <PERSON><PERSON>er", "Geometry": {"BoundingBox": {"Width": 0.5162212252616882, "Height": 0.07867902517318726, "Left": 0.41354772448539734, "Top": 0.7561668753623962}, "Polygon": [{"X": 0.41372060775756836, "Y": 0.7561668753623962}, {"X": 0.929768979549408, "Y": 0.7564705610275269}, {"X": 0.9296883940696716, "Y": 0.8348459005355835}, {"X": 0.41354772448539734, "Y": 0.8345422744750977}]}, "Id": "197c4982-f1c3-4045-896f-0fb41a9ae41f", "Relationships": [{"Type": "CHILD", "Ids": ["9ba2b5ad-27a7-4319-a0a6-a8abb6db81f9", "c6fc8876-800c-4b56-b8e2-3eb4e5c272a0", "9fab0e11-5eb8-4c65-b964-7d80cb353be4", "b0b3a99d-d3e7-40cf-a57f-58ae75be64ad"]}]}, {"BlockType": "LINE", "Confidence": 41.93239974975586, "Text": "<PERSON><PERSON>.", "Geometry": {"BoundingBox": {"Width": 0.06566952913999557, "Height": 0.03334707021713257, "Left": 0.45996540784835815, "Top": 0.8548255562782288}, "Polygon": [{"X": 0.4600353538990021, "Y": 0.8548255562782288}, {"X": 0.5256349444389343, "Y": 0.8548641204833984}, {"X": 0.5255699753761292, "Y": 0.8881726264953613}, {"X": 0.45996540784835815, "Y": 0.8881340026855469}]}, "Id": "9126f7ad-bbe7-408a-abab-8f3cf4eff495", "Relationships": [{"Type": "CHILD", "Ids": ["61f9bcd8-8615-4cae-ae0d-fafb3000e7bc"]}]}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "FAB-MASTERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25461193919181824, "Height": 0.027158761397004128, "Left": 0.11321188509464264, "Top": 0.06286675482988358}, "Polygon": [{"X": 0.11329008638858795, "Y": 0.06286675482988358}, {"X": 0.3678238093852997, "Y": 0.06301708519458771}, {"X": 0.3677613139152527, "Y": 0.09002552181482315}, {"X": 0.11321188509464264, "Y": 0.08987520635128021}]}, "Id": "d6b343e6-1dd5-462b-84d2-6d3a8a2a7792"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07342520356178284, "Height": 0.01989101432263851, "Left": 0.37971195578575134, "Top": 0.06667652726173401}, "Polygon": [{"X": 0.3797573447227478, "Y": 0.06667652726173401}, {"X": 0.4531371593475342, "Y": 0.06671986728906631}, {"X": 0.45309510827064514, "Y": 0.08656754344701767}, {"X": 0.37971195578575134, "Y": 0.08652421087026596}]}, "Id": "13d6f869-94ca-42f1-8076-e2f406c55deb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Packing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16039395332336426, "Height": 0.03314083442091942, "Left": 0.6520308256149292, "Top": 0.04386517405509949}, "Polygon": [{"X": 0.6520857810974121, "Y": 0.04386517405509949}, {"X": 0.8124247789382935, "Y": 0.043959878385066986}, {"X": 0.8123818635940552, "Y": 0.07700600475072861}, {"X": 0.6520308256149292, "Y": 0.0769113153219223}]}, "Id": "53f22011-21ea-416c-99da-8ebff937c4bb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Slip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07538188993930817, "Height": 0.030219251289963722, "Left": 0.8266999125480652, "Top": 0.049740396440029144}, "Polygon": [{"X": 0.8267381191253662, "Y": 0.049740396440029144}, {"X": 0.9020818471908569, "Y": 0.04978489503264427}, {"X": 0.9020488262176514, "Y": 0.07995964586734772}, {"X": 0.8266999125480652, "Y": 0.07991515100002289}]}, "Id": "67d18dcd-c8f3-48f8-b548-0e7c0a4ada30"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "42011", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03689040616154671, "Height": 0.00999403651803732, "Left": 0.8584620952606201, "Top": 0.08601761609315872}, "Polygon": [{"X": 0.8584739565849304, "Y": 0.08601761609315872}, {"X": 0.8953524827957153, "Y": 0.08603939414024353}, {"X": 0.8953414559364319, "Y": 0.09601165354251862}, {"X": 0.8584620952606201, "Y": 0.09598987549543381}]}, "Id": "aa256b76-8307-4d59-8fe7-9e7befc57da0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Address:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06147180125117302, "Height": 0.009599120356142521, "Left": 0.10660583525896072, "Top": 0.14761248230934143}, "Polygon": [{"X": 0.10663367062807083, "Y": 0.14761248230934143}, {"X": 0.16807764768600464, "Y": 0.14764876663684845}, {"X": 0.16805115342140198, "Y": 0.15721160173416138}, {"X": 0.10660583525896072, "Y": 0.15717533230781555}]}, "Id": "695e6ee5-ae24-4dfc-bedd-77050e03c14c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "901", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02191382274031639, "Height": 0.009212459437549114, "Left": 0.3148946464061737, "Top": 0.1494624763727188}, "Polygon": [{"X": 0.31491705775260925, "Y": 0.1494624763727188}, {"X": 0.3368084728717804, "Y": 0.14947539567947388}, {"X": 0.33678653836250305, "Y": 0.1586749255657196}, {"X": 0.3148946464061737, "Y": 0.15866200625896454}]}, "Id": "9dda9c36-ae93-40a1-8d07-912f688e16bb"}, {"BlockType": "WORD", "Confidence": 99.51271057128906, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03781925514340401, "Height": 0.009719750843942165, "Left": 0.3419208228588104, "Top": 0.14930549263954163}, "Polygon": [{"X": 0.3419438302516937, "Y": 0.14930549263954163}, {"X": 0.37974005937576294, "Y": 0.14932779967784882}, {"X": 0.3797178864479065, "Y": 0.15902523696422577}, {"X": 0.3419208228588104, "Y": 0.15900292992591858}]}, "Id": "eb4ede17-bcfd-43b7-92d5-28df5ffa01f0"}, {"BlockType": "WORD", "Confidence": 99.55416870117188, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014665024355053902, "Height": 0.00956039410084486, "Left": 0.3830318748950958, "Top": 0.14966313540935516}, "Polygon": [{"X": 0.38305366039276123, "Y": 0.14966313540935516}, {"X": 0.39769691228866577, "Y": 0.1496717780828476}, {"X": 0.39767545461654663, "Y": 0.1592235267162323}, {"X": 0.3830318748950958, "Y": 0.15921488404273987}]}, "Id": "336bf2af-eb84-465a-a593-e2b7a179d111"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030968068167567253, "Height": 0.011332421563565731, "Left": 0.5503060817718506, "Top": 0.1514817178249359}, "Polygon": [{"X": 0.5503275394439697, "Y": 0.1514817178249359}, {"X": 0.581274151802063, "Y": 0.15149997174739838}, {"X": 0.5812535285949707, "Y": 0.16281412541866302}, {"X": 0.5503060817718506, "Y": 0.16279587149620056}]}, "Id": "3200531d-e0d6-4307-ac46-f0f77b297cfe"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021762769669294357, "Height": 0.00916788075119257, "Left": 0.5852570533752441, "Top": 0.15222156047821045}, "Polygon": [{"X": 0.5852736830711365, "Y": 0.15222156047821045}, {"X": 0.6070197820663452, "Y": 0.15223439037799835}, {"X": 0.6070036292076111, "Y": 0.16138944029808044}, {"X": 0.5852570533752441, "Y": 0.16137659549713135}]}, "Id": "6e58ee11-403e-40f3-ae66-13d2e51d9db8"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "RexCon", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051604464650154114, "Height": 0.010137093253433704, "Left": 0.6119340062141418, "Top": 0.15263457596302032}, "Polygon": [{"X": 0.6119517683982849, "Y": 0.15263457596302032}, {"X": 0.6635384559631348, "Y": 0.15266501903533936}, {"X": 0.6635218858718872, "Y": 0.16277165710926056}, {"X": 0.6119340062141418, "Y": 0.16274121403694153}]}, "Id": "c0dbdf7f-c9bd-48c5-a68d-18f2812f2ba0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02445872128009796, "Height": 0.009189593605697155, "Left": 0.6685346364974976, "Top": 0.15411904454231262}, "Polygon": [{"X": 0.6685495376586914, "Y": 0.15411904454231262}, {"X": 0.6929933428764343, "Y": 0.15413346886634827}, {"X": 0.6929789781570435, "Y": 0.16330863535404205}, {"X": 0.6685346364974976, "Y": 0.1632942110300064}]}, "Id": "25526865-3262-4314-8249-5c965e5935e1"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Janesville,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07120312005281448, "Height": 0.011388055048882961, "Left": 0.31422558426856995, "Top": 0.16664530336856842}, "Polygon": [{"X": 0.3142532408237457, "Y": 0.16664530336856842}, {"X": 0.38542869687080383, "Y": 0.16668730974197388}, {"X": 0.3854029178619385, "Y": 0.17803335189819336}, {"X": 0.31422558426856995, "Y": 0.1779913455247879}]}, "Id": "643210ec-2adf-454c-8a70-699ba3b51d06"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017223767936229706, "Height": 0.009423796087503433, "Left": 0.38907280564308167, "Top": 0.16724513471126556}, "Polygon": [{"X": 0.38909414410591125, "Y": 0.16724513471126556}, {"X": 0.40629658102989197, "Y": 0.16725528240203857}, {"X": 0.4062756299972534, "Y": 0.1766689270734787}, {"X": 0.38907280564308167, "Y": 0.1766587793827057}]}, "Id": "9084c5cb-c9ba-4517-8ece-dec55c21826b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "53548", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03841789439320564, "Height": 0.009335821494460106, "Left": 0.4145815372467041, "Top": 0.1678440272808075}, "Polygon": [{"X": 0.4146020710468292, "Y": 0.1678440272808075}, {"X": 0.45299941301345825, "Y": 0.16786669194698334}, {"X": 0.4529796838760376, "Y": 0.17717985808849335}, {"X": 0.4145815372467041, "Y": 0.1771571934223175}]}, "Id": "873963e9-6d0a-40b8-bf9a-e00108cd748a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2841", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431665152311325, "Height": 0.009499778971076012, "Left": 0.6099408268928528, "Top": 0.1702669858932495}, "Polygon": [{"X": 0.6099575161933899, "Y": 0.1702669858932495}, {"X": 0.6442574858665466, "Y": 0.17028723657131195}, {"X": 0.6442415118217468, "Y": 0.17976675927639008}, {"X": 0.6099408268928528, "Y": 0.17974652349948883}]}, "Id": "86c8ed2f-1535-46f4-8736-cf6b4121d3df"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054159946739673615, "Height": 0.01186362374573946, "Left": 0.649296224117279, "Top": 0.17081333696842194}, "Polygon": [{"X": 0.6493160128593445, "Y": 0.17081333696842194}, {"X": 0.7034561634063721, "Y": 0.17084528505802155}, {"X": 0.703437864780426, "Y": 0.18267695605754852}, {"X": 0.649296224117279, "Y": 0.1826450079679489}]}, "Id": "ba0c17e6-524c-4a04-98d1-903fa950d3e8"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021797074005007744, "Height": 0.009300634264945984, "Left": 0.708367109298706, "Top": 0.1720736175775528}, "Polygon": [{"X": 0.7083814144134521, "Y": 0.1720736175775528}, {"X": 0.7301642298698425, "Y": 0.17208647727966309}, {"X": 0.7301504015922546, "Y": 0.18137425184249878}, {"X": 0.708367109298706, "Y": 0.1813613921403885}]}, "Id": "a2aecf3a-aa3d-4206-9c28-b4b0168dc5fe"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04665335640311241, "Height": 0.00989560317248106, "Left": 0.3146851360797882, "Top": 0.18416982889175415}, "Polygon": [{"X": 0.3147091567516327, "Y": 0.18416982889175415}, {"X": 0.3613384962081909, "Y": 0.1841973513364792}, {"X": 0.36131551861763, "Y": 0.19406543672084808}, {"X": 0.3146851360797882, "Y": 0.19403791427612305}]}, "Id": "ce9597e6-98bb-4447-9a0e-0e2686dabbb3"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08615048974752426, "Height": 0.010124246589839458, "Left": 0.3657965064048767, "Top": 0.1847507804632187}, "Polygon": [{"X": 0.3658198416233063, "Y": 0.1847507804632187}, {"X": 0.45194700360298157, "Y": 0.1848016232252121}, {"X": 0.4519256353378296, "Y": 0.19487503170967102}, {"X": 0.3657965064048767, "Y": 0.1948242038488388}]}, "Id": "18c59f8c-b01b-495e-83b9-a356a9f4faa0"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Burlington,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07691449671983719, "Height": 0.011904497630894184, "Left": 0.6102584004402161, "Top": 0.1879032999277115}, "Polygon": [{"X": 0.6102792620658875, "Y": 0.1879032999277115}, {"X": 0.6871728897094727, "Y": 0.18794868886470795}, {"X": 0.6871541142463684, "Y": 0.1998077929019928}, {"X": 0.6102584004402161, "Y": 0.19976241886615753}]}, "Id": "abcfa978-6ac0-4993-b9ec-8f169044659e"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02112223580479622, "Height": 0.008839434944093227, "Left": 0.6916007399559021, "Top": 0.189245343208313}, "Polygon": [{"X": 0.6916146278381348, "Y": 0.189245343208313}, {"X": 0.7127229571342468, "Y": 0.18925781548023224}, {"X": 0.7127094864845276, "Y": 0.1980847865343094}, {"X": 0.6916007399559021, "Y": 0.19807232916355133}]}, "Id": "7f1fe872-ffcc-4ba6-ab13-c73c37f62fb1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "53105", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04315675422549248, "Height": 0.009744483046233654, "Left": 0.7169814109802246, "Top": 0.18964140117168427}, "Polygon": [{"X": 0.7169961333274841, "Y": 0.18964140117168427}, {"X": 0.7601381540298462, "Y": 0.18966685235500336}, {"X": 0.7601243853569031, "Y": 0.1993858814239502}, {"X": 0.7169814109802246, "Y": 0.1993604153394699}]}, "Id": "8ee30fe0-b2b2-4f81-8943-eed489c3afaa"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Fax:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027891559526324272, "Height": 0.00959622859954834, "Left": 0.31438568234443665, "Top": 0.2019696831703186}, "Polygon": [{"X": 0.3144090175628662, "Y": 0.2019696831703186}, {"X": 0.34227725863456726, "Y": 0.2019861340522766}, {"X": 0.34225451946258545, "Y": 0.21156591176986694}, {"X": 0.31438568234443665, "Y": 0.21154946088790894}]}, "Id": "ccdc1f85-79be-482e-b639-5caa590ca253"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08795750141143799, "Height": 0.010044928640127182, "Left": 0.34658342599868774, "Top": 0.20233258605003357}, "Polygon": [{"X": 0.3466070294380188, "Y": 0.20233258605003357}, {"X": 0.43454092741012573, "Y": 0.20238448679447174}, {"X": 0.43451932072639465, "Y": 0.21237751841545105}, {"X": 0.34658342599868774, "Y": 0.21232561767101288}]}, "Id": "1ed3458f-ebe3-4ab0-a1db-7e69b7e0a01b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031334079802036285, "Height": 0.011169755831360817, "Left": 0.10295768082141876, "Top": 0.23474736511707306}, "Polygon": [{"X": 0.10299021750688553, "Y": 0.23474736511707306}, {"X": 0.13429175317287445, "Y": 0.23476584255695343}, {"X": 0.13426001369953156, "Y": 0.24591712653636932}, {"X": 0.10295768082141876, "Y": 0.24589864909648895}]}, "Id": "02f92b01-fa5d-453a-8d9e-25fee3e25f61"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03710061311721802, "Height": 0.009330346249043941, "Left": 0.1387639343738556, "Top": 0.2354821264743805}, "Polygon": [{"X": 0.1387903392314911, "Y": 0.2354821264743805}, {"X": 0.1758645474910736, "Y": 0.23550400137901306}, {"X": 0.17583893239498138, "Y": 0.244812473654747}, {"X": 0.1387639343738556, "Y": 0.24479059875011444}]}, "Id": "53df6e66-e655-44f6-93bf-d28d71e9ae7d"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "9/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0794830173254013, "Height": 0.011299424804747105, "Left": 0.1807251125574112, "Top": 0.23617713153362274}, "Polygon": [{"X": 0.18075594305992126, "Y": 0.23617713153362274}, {"X": 0.2602081298828125, "Y": 0.2362240105867386}, {"X": 0.260179340839386, "Y": 0.24747654795646667}, {"X": 0.1807251125574112, "Y": 0.24742966890335083}]}, "Id": "87e96291-6708-43cb-ae9a-a355db0f625f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037951819598674774, "Height": 0.00942244566977024, "Left": 0.10229186713695526, "Top": 0.25242120027542114}, "Polygon": [{"X": 0.10231930762529373, "Y": 0.25242120027542114}, {"X": 0.14024369418621063, "Y": 0.2524435818195343}, {"X": 0.14021706581115723, "Y": 0.26184365153312683}, {"X": 0.10229186713695526, "Y": 0.26182126998901367}]}, "Id": "3d0692b9-d8ed-4186-bfde-dbcc500e25af"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Person:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052747249603271484, "Height": 0.009582189843058586, "Left": 0.14461135864257812, "Top": 0.25340214371681213}, "Polygon": [{"X": 0.1446383148431778, "Y": 0.25340214371681213}, {"X": 0.1973586082458496, "Y": 0.2534332573413849}, {"X": 0.19733279943466187, "Y": 0.26298433542251587}, {"X": 0.14461135864257812, "Y": 0.2629532516002655}]}, "Id": "ed9ba64f-7607-42e5-9fc1-bb83f1dd7f14"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025287607684731483, "Height": 0.009228254668414593, "Left": 0.20619326829910278, "Top": 0.25457361340522766}, "Polygon": [{"X": 0.20621797442436218, "Y": 0.25457361340522766}, {"X": 0.23148088157176971, "Y": 0.2545885145664215}, {"X": 0.2314566969871521, "Y": 0.2638018727302551}, {"X": 0.20619326829910278, "Y": 0.2637869417667389}]}, "Id": "42acdba9-f1e5-495c-bc34-7ab0a7c23d92"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02159924991428852, "Height": 0.00919167511165142, "Left": 0.5504724979400635, "Top": 0.2580232620239258}, "Polygon": [{"X": 0.5504899024963379, "Y": 0.2580232620239258}, {"X": 0.5720717310905457, "Y": 0.2580359876155853}, {"X": 0.5720548033714294, "Y": 0.26721492409706116}, {"X": 0.5504724979400635, "Y": 0.2672021985054016}]}, "Id": "54ea28c9-9c3e-48ae-9a7f-a7b037126a9a"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021527890115976334, "Height": 0.00893675908446312, "Left": 0.5763189196586609, "Top": 0.2585461437702179}, "Polygon": [{"X": 0.5763353109359741, "Y": 0.2585461437702179}, {"X": 0.5978468060493469, "Y": 0.25855883955955505}, {"X": 0.5978308916091919, "Y": 0.2674829065799713}, {"X": 0.5763189196586609, "Y": 0.26747021079063416}]}, "Id": "ee6ce067-5fc0-453b-a858-e5dd61a9f26b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Astec", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03922554850578308, "Height": 0.009219682775437832, "Left": 0.6111268997192383, "Top": 0.2589392066001892}, "Polygon": [{"X": 0.6111431121826172, "Y": 0.2589392066001892}, {"X": 0.6503524780273438, "Y": 0.25896233320236206}, {"X": 0.6503371596336365, "Y": 0.268158882856369}, {"X": 0.6111268997192383, "Y": 0.26813575625419617}]}, "Id": "2a40f3dc-6b7c-4de0-a0fe-304eba15c8e4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06499185413122177, "Height": 0.00997052900493145, "Left": 0.10206082463264465, "Top": 0.27028441429138184}, "Polygon": [{"X": 0.10208982974290848, "Y": 0.27028441429138184}, {"X": 0.16705268621444702, "Y": 0.2703227400779724}, {"X": 0.16702516376972198, "Y": 0.28025493025779724}, {"X": 0.10206082463264465, "Y": 0.28021660447120667}]}, "Id": "7fbfa2c9-8903-4afb-9649-285c0f751c10"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Order:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04470785707235336, "Height": 0.009890219196677208, "Left": 0.1707945019006729, "Top": 0.27139654755592346}, "Polygon": [{"X": 0.17082175612449646, "Y": 0.27139654755592346}, {"X": 0.21550235152244568, "Y": 0.2714228928089142}, {"X": 0.2154761105775833, "Y": 0.2812867760658264}, {"X": 0.1707945019006729, "Y": 0.2812604010105133}]}, "Id": "0176d1fd-33aa-4901-a0c4-2f40b16f5d27"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "PUR3165-105166", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12467621266841888, "Height": 0.009997377172112465, "Left": 0.22048310935497284, "Top": 0.2726132273674011}, "Polygon": [{"X": 0.2205093950033188, "Y": 0.2726132273674011}, {"X": 0.3451593220233917, "Y": 0.27268674969673157}, {"X": 0.3451358675956726, "Y": 0.28261059522628784}, {"X": 0.22048310935497284, "Y": 0.2825370728969574}]}, "Id": "ff2d508c-6a07-40bb-9c1e-4eaa684bb15d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipped", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06332710385322571, "Height": 0.012779337354004383, "Left": 0.10153787583112717, "Top": 0.2868586480617523}, "Polygon": [{"X": 0.1015750914812088, "Y": 0.2868586480617523}, {"X": 0.16486498713493347, "Y": 0.2868959903717041}, {"X": 0.16482961177825928, "Y": 0.29963797330856323}, {"X": 0.10153787583112717, "Y": 0.29960063099861145}]}, "Id": "aabcd2e2-2d80-4a40-af23-6caa0ddd3a47"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "Via:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030755573883652687, "Height": 0.01056718360632658, "Left": 0.16930922865867615, "Top": 0.2882201671600342}, "Polygon": [{"X": 0.1693384051322937, "Y": 0.2882201671600342}, {"X": 0.20006480813026428, "Y": 0.2882382869720459}, {"X": 0.20003636181354523, "Y": 0.29878735542297363}, {"X": 0.16930922865867615, "Y": 0.2987692356109619}]}, "Id": "f539fb61-0614-4be9-a3dc-fa7db8062c57"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "CPU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03173026442527771, "Height": 0.01002749428153038, "Left": 0.20510059595108032, "Top": 0.28914347290992737}, "Polygon": [{"X": 0.20512744784355164, "Y": 0.28914347290992737}, {"X": 0.23683084547519684, "Y": 0.28916215896606445}, {"X": 0.23680470883846283, "Y": 0.29917094111442566}, {"X": 0.20510059595108032, "Y": 0.2991522550582886}]}, "Id": "d37b6d6e-7c81-4d31-8778-47db4806e207"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Part", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029847480356693268, "Height": 0.009277384728193283, "Left": 0.3630736470222473, "Top": 0.372030645608902}, "Polygon": [{"X": 0.36309516429901123, "Y": 0.372030645608902}, {"X": 0.3929211497306824, "Y": 0.3720482289791107}, {"X": 0.3929002583026886, "Y": 0.38130804896354675}, {"X": 0.3630736470222473, "Y": 0.3812904357910156}]}, "Id": "da3605c2-3466-4d43-a388-023989530763"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056627023965120316, "Height": 0.009593135677278042, "Left": 0.3972598910331726, "Top": 0.37224194407463074}, "Polygon": [{"X": 0.39728134870529175, "Y": 0.37224194407463074}, {"X": 0.4538869261741638, "Y": 0.37227532267570496}, {"X": 0.4538666903972626, "Y": 0.38183507323265076}, {"X": 0.3972598910331726, "Y": 0.38180169463157654}]}, "Id": "4c5461a4-b92b-4468-b78c-8ebde88d844f"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026230327785015106, "Height": 0.009221863932907581, "Left": 0.5602498054504395, "Top": 0.3742678463459015}, "Polygon": [{"X": 0.5602670311927795, "Y": 0.3742678463459015}, {"X": 0.5864801406860352, "Y": 0.3742832839488983}, {"X": 0.586463451385498, "Y": 0.3834896981716156}, {"X": 0.5602498054504395, "Y": 0.3834742605686188}]}, "Id": "b3175135-63ba-471a-a1a6-48c57219d9cb"}, {"BlockType": "WORD", "Confidence": 99.55655670166016, "Text": "Job#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03522883728146553, "Height": 0.009288681671023369, "Left": 0.5892464518547058, "Top": 0.3743017017841339}, "Polygon": [{"X": 0.5892632007598877, "Y": 0.3743017017841339}, {"X": 0.6244753003120422, "Y": 0.37432244420051575}, {"X": 0.6244592666625977, "Y": 0.38359037041664124}, {"X": 0.5892464518547058, "Y": 0.3835696280002594}]}, "Id": "bcbe29bf-9aba-48a2-bef5-ec6e7b250bd1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041065096855163574, "Height": 0.009469482116401196, "Left": 0.6574952602386475, "Top": 0.3742363154888153}, "Polygon": [{"X": 0.6575108766555786, "Y": 0.3742363154888153}, {"X": 0.698560357093811, "Y": 0.3742605447769165}, {"X": 0.6985456347465515, "Y": 0.3837057948112488}, {"X": 0.6574952602386475, "Y": 0.38368159532546997}]}, "Id": "eb180ee8-941f-4f98-9065-9e0af474a930"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06214268133044243, "Height": 0.010940417647361755, "Left": 0.7016261219978333, "Top": 0.3747464716434479}, "Polygon": [{"X": 0.7016430497169495, "Y": 0.3747464716434479}, {"X": 0.7637687921524048, "Y": 0.37478309869766235}, {"X": 0.7637534737586975, "Y": 0.3856869041919708}, {"X": 0.7016261219978333, "Y": 0.38565024733543396}]}, "Id": "6cc40d3b-fa74-4114-aa73-4fa8a2e3a5bc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031075704842805862, "Height": 0.011119081638753414, "Left": 0.8052491545677185, "Top": 0.3749060034751892}, "Polygon": [{"X": 0.8052636981010437, "Y": 0.3749060034751892}, {"X": 0.8363248109817505, "Y": 0.37492433190345764}, {"X": 0.8363110423088074, "Y": 0.3860251009464264}, {"X": 0.8052491545677185, "Y": 0.38600677251815796}]}, "Id": "ba9b8182-87d7-4342-b227-068586960891"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06207212060689926, "Height": 0.011285703629255295, "Left": 0.8402308821678162, "Top": 0.3750981092453003}, "Polygon": [{"X": 0.8402447700500488, "Y": 0.3750981092453003}, {"X": 0.9023029804229736, "Y": 0.3751347064971924}, {"X": 0.9022907018661499, "Y": 0.3863838315010071}, {"X": 0.8402308821678162, "Y": 0.386347234249115}]}, "Id": "9e472dff-5243-44c1-add4-ce07f557124f"}, {"BlockType": "WORD", "Confidence": 98.50187683105469, "Text": "544-15008-80,RV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1274154633283615, "Height": 0.012967631220817566, "Left": 0.10191446542739868, "Top": 0.3925946056842804}, "Polygon": [{"X": 0.10195209830999374, "Y": 0.3925946056842804}, {"X": 0.2293299287557602, "Y": 0.3926697075366974}, {"X": 0.22929604351520538, "Y": 0.40556225180625916}, {"X": 0.10191446542739868, "Y": 0.40548714995384216}]}, "Id": "797ba4e4-aabf-47c1-84d0-5a5878e7d9d4"}, {"BlockType": "WORD", "Confidence": 94.5693130493164, "Text": "R05", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027508599683642387, "Height": 0.0100110974162817, "Left": 0.23385684192180634, "Top": 0.3943328261375427}, "Polygon": [{"X": 0.23388300836086273, "Y": 0.3943328261375427}, {"X": 0.26136544346809387, "Y": 0.39434900879859924}, {"X": 0.2613399028778076, "Y": 0.4043439030647278}, {"X": 0.23385684192180634, "Y": 0.40432772040367126}]}, "Id": "03317b49-7c2a-4d3b-9427-6907f83bdde1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "41941", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044650498777627945, "Height": 0.009661072865128517, "Left": 0.5616632103919983, "Top": 0.39821141958236694}, "Polygon": [{"X": 0.5616812109947205, "Y": 0.39821141958236694}, {"X": 0.6063137054443359, "Y": 0.3982377350330353}, {"X": 0.6062966585159302, "Y": 0.4078724980354309}, {"X": 0.5616632103919983, "Y": 0.4078461825847626}]}, "Id": "c2331cce-c3b9-455d-b4e4-07544f28a0b8"}, {"BlockType": "WORD", "Confidence": 99.25801086425781, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008198490366339684, "Height": 0.009699119254946709, "Left": 0.769997775554657, "Top": 0.39847108721733093}, "Polygon": [{"X": 0.770011305809021, "Y": 0.39847108721733093}, {"X": 0.7781962752342224, "Y": 0.39847591519355774}, {"X": 0.7781829237937927, "Y": 0.408170223236084}, {"X": 0.769997775554657, "Y": 0.4081653952598572}]}, "Id": "85374994-78bc-4978-a48a-a413bc876f9f"}, {"BlockType": "WORD", "Confidence": 98.92279052734375, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008932854048907757, "Height": 0.00949004665017128, "Left": 0.9128589630126953, "Top": 0.3990378975868225}, "Polygon": [{"X": 0.9128690958023071, "Y": 0.3990378975868225}, {"X": 0.9217917919158936, "Y": 0.39904317259788513}, {"X": 0.9217818975448608, "Y": 0.4085279405117035}, {"X": 0.9128589630126953, "Y": 0.40852269530296326}]}, "Id": "f5396b0c-f8fa-41e2-8bda-5803a3679bc4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062230851501226425, "Height": 0.012969802133738995, "Left": 0.5624654293060303, "Top": 0.6395642757415771}, "Polygon": [{"X": 0.5624895691871643, "Y": 0.6395642757415771}, {"X": 0.6246963143348694, "Y": 0.6396008729934692}, {"X": 0.6246740221977234, "Y": 0.6525340676307678}, {"X": 0.5624654293060303, "Y": 0.6524974703788757}]}, "Id": "72210b3f-a50e-49ce-a476-a6b931bb5b19"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045537348836660385, "Height": 0.010100631043314934, "Left": 0.6931411027908325, "Top": 0.6402238011360168}, "Polygon": [{"X": 0.693156898021698, "Y": 0.6402238011360168}, {"X": 0.7386784553527832, "Y": 0.640250563621521}, {"X": 0.7386637330055237, "Y": 0.6503244042396545}, {"X": 0.6931411027908325, "Y": 0.6502976417541504}]}, "Id": "0b549772-1361-4b5c-9885-63e3945e9e97"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07192175835371017, "Height": 0.012350550852715969, "Left": 0.8308875560760498, "Top": 0.6396564841270447}, "Polygon": [{"X": 0.8309029936790466, "Y": 0.6396564841270447}, {"X": 0.9028093218803406, "Y": 0.6396988034248352}, {"X": 0.9027959108352661, "Y": 0.6520070433616638}, {"X": 0.8308875560760498, "Y": 0.6519647240638733}]}, "Id": "79746bca-1c75-4c59-8e4a-da1dd7148994"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0726637989282608, "Height": 0.013042994774878025, "Left": 0.09316791594028473, "Top": 0.6584966778755188}, "Polygon": [{"X": 0.09320610761642456, "Y": 0.6584966778755188}, {"X": 0.16583171486854553, "Y": 0.6585394144058228}, {"X": 0.1657956838607788, "Y": 0.6715396642684937}, {"X": 0.09316791594028473, "Y": 0.6714968681335449}]}, "Id": "0ce133b4-f428-4f13-8bf2-e45459078b9b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "visually", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055904921144247055, "Height": 0.012892266735434532, "Left": 0.17016807198524475, "Top": 0.65952467918396}, "Polygon": [{"X": 0.1702035814523697, "Y": 0.65952467918396}, {"X": 0.2260729819536209, "Y": 0.659557580947876}, {"X": 0.2260391116142273, "Y": 0.6724169850349426}, {"X": 0.17016807198524475, "Y": 0.6723840832710266}]}, "Id": "4fc8e9d7-dd60-4fa7-af43-c7da8c6aa4b7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0614500418305397, "Height": 0.010700305923819542, "Left": 0.09158818423748016, "Top": 0.6746164560317993}, "Polygon": [{"X": 0.09161955118179321, "Y": 0.6746164560317993}, {"X": 0.15303823351860046, "Y": 0.674652636051178}, {"X": 0.1530083566904068, "Y": 0.685316801071167}, {"X": 0.09158818423748016, "Y": 0.6852806210517883}]}, "Id": "3d1019b0-dee5-4a2a-81c6-20bf9aff8856"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021979479119181633, "Height": 0.0105592617765069, "Left": 0.1578899323940277, "Top": 0.6751007437705994}, "Polygon": [{"X": 0.15791936218738556, "Y": 0.6751007437705994}, {"X": 0.1798694133758545, "Y": 0.6751136183738708}, {"X": 0.17984052002429962, "Y": 0.6856600046157837}, {"X": 0.1578899323940277, "Y": 0.6856470704078674}]}, "Id": "17e14fa5-e66a-44df-be08-55e8aad33420"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05976700037717819, "Height": 0.012816310860216618, "Left": 0.1839655488729477, "Top": 0.6754313111305237}, "Polygon": [{"X": 0.1840004324913025, "Y": 0.6754313111305237}, {"X": 0.2437325417995453, "Y": 0.6754664778709412}, {"X": 0.24369938671588898, "Y": 0.6882476210594177}, {"X": 0.1839655488729477, "Y": 0.6882124543190002}]}, "Id": "3d4ec62e-9784-4b68-b47e-312d307288a4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11427921801805496, "Height": 0.01879861205816269, "Left": 0.41360947489738464, "Top": 0.7878174185752869}, "Polygon": [{"X": 0.413650780916214, "Y": 0.7878174185752869}, {"X": 0.5278887152671814, "Y": 0.7878846526145935}, {"X": 0.5278522372245789, "Y": 0.8066160678863525}, {"X": 0.41360947489738464, "Y": 0.8065488338470459}]}, "Id": "9ba2b5ad-27a7-4319-a0a6-a8abb6db81f9"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "by:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03770871087908745, "Height": 0.022683974355459213, "Left": 0.5403268933296204, "Top": 0.7884222269058228}, "Polygon": [{"X": 0.5403702855110168, "Y": 0.7884222269058228}, {"X": 0.5780355930328369, "Y": 0.7884443402290344}, {"X": 0.5779941082000732, "Y": 0.8111062049865723}, {"X": 0.5403268933296204, "Y": 0.8110840320587158}]}, "Id": "c6fc8876-800c-4b56-b8e2-3eb4e5c272a0"}, {"BlockType": "WORD", "Confidence": 45.79261016845703, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16860760748386383, "Height": 0.0589708536863327, "Left": 0.5980849862098694, "Top": 0.7562754154205322}, "Polygon": [{"X": 0.5981900691986084, "Y": 0.7562754154205322}, {"X": 0.766692578792572, "Y": 0.7563745975494385}, {"X": 0.7666101455688477, "Y": 0.8152462840080261}, {"X": 0.5980849862098694, "Y": 0.8151471018791199}]}, "Id": "9fab0e11-5eb8-4c65-b964-7d80cb353be4"}, {"BlockType": "WORD", "Confidence": 77.80193328857422, "Text": "parker", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1535376012325287, "Height": 0.05337739363312721, "Left": 0.7762055397033691, "Top": 0.7814685106277466}, "Polygon": [{"X": 0.7762790322303772, "Y": 0.7814685106277466}, {"X": 0.9297431707382202, "Y": 0.7815588116645813}, {"X": 0.9296883940696716, "Y": 0.8348459005355835}, {"X": 0.7762055397033691, "Y": 0.8347555994987488}]}, "Id": "b0b3a99d-d3e7-40cf-a57f-58ae75be64ad"}, {"BlockType": "WORD", "Confidence": 41.93239974975586, "Text": "<PERSON><PERSON>.", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06566952913999557, "Height": 0.03334707021713257, "Left": 0.45996540784835815, "Top": 0.8548255562782288}, "Polygon": [{"X": 0.4600353538990021, "Y": 0.8548255562782288}, {"X": 0.5256349444389343, "Y": 0.8548641204833984}, {"X": 0.5255699753761292, "Y": 0.8881726264953613}, {"X": 0.45996540784835815, "Y": 0.8881340026855469}]}, "Id": "61f9bcd8-8615-4cae-ae0d-fafb3000e7bc"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b469766f-aad9-4e66-b06f-3acd81376352", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b469766f-aad9-4e66-b06f-3acd81376352", "content-type": "application/x-amz-json-1.1", "content-length": "51650", "date": "Wed, 24 Sep 2025 12:01:37 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "2a1764f0-16d9-4059-8919-2865751d8869", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:01:40 GMT", "content-type": "application/json", "content-length": "560", "connection": "keep-alive", "x-amzn-requestid": "2a1764f0-16d9-4059-8919-2865751d8869"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It appears to be a packing slip. That matches pack_list. Pack list keywords: Packing List, Pack List, Contents, Qty, Item Description. Yes. So doc_type: pack_list. Provide array with page_no 1 and doc_type \"pack_list\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"pack_list\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2642, "outputTokens": 77, "totalTokens": 2719}, "metrics": {"latencyMs": 536}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7a41e466_YC2ROS9NUB31VO0ABEV5.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n        Optional keywords indication: Freight bill, Bill of ladding, PO\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nFAB-MASTERS, INC.\nPacking Slip\n42011\nAddress:\n901 Joliet St\nShip To: RexCon LLC\nJanesville, WI 53548\n2841 Whiting Rd.\nPhone: ************\nBurlington, WI 53105\nFax: ************\nShip Date: 9/19/2025\nSales Person: Zac\nBill To: Astec\nPurchase Order: PUR3165-105166\nShipped Via: CPU\nPart Number\nOur Job#\nOrder Quantity\nShip Quantity\n544-15008-80,RV R05\n41941\n3\n2\nShipping\nDriver\nInspector\nShipment visually\nchecked for damage\nReceived by: <PERSON><PERSON> parker\n<PERSON>.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}