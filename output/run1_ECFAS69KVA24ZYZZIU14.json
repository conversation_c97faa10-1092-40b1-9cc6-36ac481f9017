{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.884765088558197, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.884765088558197, "Y": 0.012681088410317898}, {"X": 0.8689437508583069, "Y": 0.9686429500579834}, {"X": 0.019997697323560715, "Y": 1.0}, {"X": 0.0, "Y": 0.0}]}, "Id": "0b1cdf20-36b6-4795-88c7-86d423bcf8a9", "Relationships": [{"Type": "CHILD", "Ids": ["72564419-b6a6-485c-9180-0557e6ed7045", "6913fbbc-67ac-41e5-b758-36cba924ae11", "d2f9ba28-562d-41ca-8be2-e9d9eb822728", "c9528fec-543e-4903-9392-9e6465daa0a1", "df0688db-cee1-4332-9b17-e0ebd39d5cce", "beb084da-2571-455f-a183-17fce21ba36d", "7df03bbb-c60a-457f-afd0-c8d861c7c9a7", "edf76fd6-1751-428e-993b-e1f67a649acc", "d626e4e3-f5e7-42cb-9c9f-624bc22f329c", "43c54af7-c96b-4245-b16a-53381643ba57", "29851e1c-1c65-4ab0-b0fe-622fcbec3828", "3e69411c-0731-4e96-84fc-df901f24c298", "247dc92c-42c0-4cda-89ac-572c69549d3a", "1807320f-dfdb-4707-a58b-7354aa53b834", "ff1a7050-27ac-4790-a295-a3f2988e0c77", "1542ab50-e7d9-4e09-8dee-1ec60406d236", "330c47c0-874b-4bdb-9cc6-297b6ca61614", "cfeaa27f-d45d-44cf-bf0e-0d856c923837", "e45482c3-7b25-48bb-afed-c64cb8596885", "98a29daa-f704-4536-b71b-82a112f02fd1", "91eb1425-1809-444a-9fa1-d8ea9c132e9e", "32e50f1f-5a31-4c94-ac6a-cb10a612e41e", "1e13207f-9ad6-4edb-8af9-3fe36aa0e2b2", "9fed38ac-1a6c-49c9-a549-86650c3e5815", "8e57759c-6a82-4945-8d45-21d3ecbd93b6"]}]}, {"BlockType": "LINE", "Confidence": 95.3340072631836, "Text": "KWIK TRIP 1281 /106 N. ROYAL AVENUE /BELGIUM. WI 53004/(*************", "Geometry": {"BoundingBox": {"Width": 0.038819171488285065, "Height": 0.7708173394203186, "Left": 0.7968462109565735, "Top": 0.06489217281341553}, "Polygon": [{"X": 0.8356653451919556, "Y": 0.06548881530761719}, {"X": 0.825443685054779, "Y": 0.8343454599380493}, {"X": 0.7968462109565735, "Y": 0.8357095122337341}, {"X": 0.8055252432823181, "Y": 0.06489217281341553}]}, "Id": "72564419-b6a6-485c-9180-0557e6ed7045", "Relationships": [{"Type": "CHILD", "Ids": ["f578117a-59c4-4475-ad85-922ef84e113e", "e2e06fac-6e51-4025-9332-e846cca895d1", "e0b47e70-b610-479d-98db-f4285a0bd19d", "435f560e-229d-40bb-9944-3b5541080a2d", "1f344f1b-4319-4ee6-ac00-32e9aca956b6", "1b235c6d-4751-4db4-98e4-5ddd10c88cbd", "66cd1900-0875-4b94-b968-ea540a6e516d", "cda1e8d9-12f3-4bbc-9f81-92a3660653f8", "69df109d-a96f-4f5e-ae83-80932d8be5cf", "152502cd-0479-4cb8-aa63-d02a489b1e7e", "67d9ffce-d439-45b4-a6f0-ee6633fb1418"]}]}, {"BlockType": "LINE", "Confidence": 95.69419860839844, "Text": "Number: 0100-0881 10/10/2024 Station: 1.", "Geometry": {"BoundingBox": {"Width": 0.03501477837562561, "Height": 0.4728206992149353, "Left": 0.7754600048065186, "Top": 0.06226557493209839}, "Polygon": [{"X": 0.8104747533798218, "Y": 0.06287514418363571}, {"X": 0.8050081729888916, "Y": 0.5344556570053101}, {"X": 0.7754600048065186, "Y": 0.5350862741470337}, {"X": 0.7799671292304993, "Y": 0.06226557493209839}]}, "Id": "6913fbbc-67ac-41e5-b758-36cba924ae11", "Relationships": [{"Type": "CHILD", "Ids": ["2f67bd78-ce02-44a8-946b-9734a4b3dec8", "f1036678-e0ad-43d4-909e-357e79171581", "63b3535a-7cc5-4a79-a4be-4991d9be1b6c", "de321f1e-4cee-431a-b4bc-77378e236444", "ad426b3a-ec45-4f24-adf7-c15435f5913a"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "Pump", "Geometry": {"BoundingBox": {"Width": 0.020288163796067238, "Height": 0.04972032457590103, "Left": 0.7145830392837524, "Top": 0.05851023644208908}, "Polygon": [{"X": 0.7348712086677551, "Y": 0.05891481414437294}, {"X": 0.7345512509346008, "Y": 0.10823056101799011}, {"X": 0.7145830392837524, "Y": 0.1079130694270134}, {"X": 0.7148367762565613, "Y": 0.05851023644208908}]}, "Id": "d2f9ba28-562d-41ca-8be2-e9d9eb822728", "Relationships": [{"Type": "CHILD", "Ids": ["0ca3ca8e-b2b7-44f9-bef4-72706cd47adb"]}]}, {"BlockType": "LINE", "Confidence": 99.75299072265625, "Text": "Products Reefer Quantity Unit Cost", "Geometry": {"BoundingBox": {"Width": 0.03233999386429787, "Height": 0.429961234331131, "Left": 0.7009264230728149, "Top": 0.30393868684768677}, "Polygon": [{"X": 0.7332664132118225, "Y": 0.30393868684768677}, {"X": 0.7304848432540894, "Y": 0.7327629923820496}, {"X": 0.7009264230728149, "Y": 0.7338999509811401}, {"X": 0.702816903591156, "Y": 0.30397340655326843}]}, "Id": "c9528fec-543e-4903-9392-9e6465daa0a1", "Relationships": [{"Type": "CHILD", "Ids": ["bdca58d1-cf81-4a53-81c6-c85d23911e0e", "cd7db785-777d-4fe6-89a5-3928c81fd083", "6d194a87-87a4-42c3-bd7e-758ca8bc891c", "67796d66-8481-4024-8e6e-adbd740bfcfe", "ff269b48-6be2-4478-9c3c-286ac95604ac"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.018072018399834633, "Height": 0.052846331149339676, "Left": 0.7132983803749084, "Top": 0.7977793216705322}, "Polygon": [{"X": 0.7313703894615173, "Y": 0.7977793216705322}, {"X": 0.7310283780097961, "Y": 0.8497630953788757}, {"X": 0.7132983803749084, "Y": 0.8506256341934204}, {"X": 0.7135751247406006, "Y": 0.7985645532608032}]}, "Id": "df0688db-cee1-4332-9b17-e0ebd39d5cce", "Relationships": [{"Type": "CHILD", "Ids": ["0f84e0e2-320b-491d-a383-f676dbcd2f07"]}]}, {"BlockType": "LINE", "Confidence": 97.74872589111328, "Text": "# 2", "Geometry": {"BoundingBox": {"Width": 0.01801501028239727, "Height": 0.037378035485744476, "Left": 0.6923985481262207, "Top": 0.05731113255023956}, "Polygon": [{"X": 0.7104135751724243, "Y": 0.0576733723282814}, {"X": 0.710234522819519, "Y": 0.09468916803598404}, {"X": 0.6923985481262207, "Y": 0.09438519924879074}, {"X": 0.6925331354141235, "Y": 0.05731113255023956}]}, "Id": "beb084da-2571-455f-a183-17fce21ba36d", "Relationships": [{"Type": "CHILD", "Ids": ["c7105815-5f24-4b66-9740-8bc74a0700e0", "d70a191a-3adf-44c7-9956-868f577b79b6"]}]}, {"BlockType": "LINE", "Confidence": 98.88014221191406, "Text": "Diesel 2", "Geometry": {"BoundingBox": {"Width": 0.022729987278580666, "Height": 0.09227970242500305, "Left": 0.6849694848060608, "Top": 0.30475670099258423}, "Polygon": [{"X": 0.7076994776725769, "Y": 0.30475670099258423}, {"X": 0.7072637677192688, "Y": 0.3968311548233032}, {"X": 0.6849694848060608, "Y": 0.3970364034175873}, {"X": 0.6852640509605408, "Y": 0.30478379130363464}]}, "Id": "7df03bbb-c60a-457f-afd0-c8d861c7c9a7", "Relationships": [{"Type": "CHILD", "Ids": ["db950332-c4a8-40af-bc77-fe1319f4e7a7", "906f91ba-dc9b-4556-a9ef-e3754605d04e"]}]}, {"BlockType": "LINE", "Confidence": 98.96842956542969, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.01638827659189701, "Height": 0.011348837055265903, "Left": 0.6818737387657166, "Top": 0.45224958658218384}, "Polygon": [{"X": 0.6982619762420654, "Y": 0.45224958658218384}, {"X": 0.6982161998748779, "Y": 0.4633537530899048}, {"X": 0.6818737387657166, "Y": 0.46359843015670776}, {"X": 0.6819069981575012, "Y": 0.45247870683670044}]}, "Id": "edf76fd6-1751-428e-993b-e1f67a649acc", "Relationships": [{"Type": "CHILD", "Ids": ["93e5c08b-180b-4b74-b9af-ee86a1d80973"]}]}, {"BlockType": "LINE", "Confidence": 98.69758605957031, "Text": "97.780", "Geometry": {"BoundingBox": {"Width": 0.017762508243322372, "Height": 0.06503745913505554, "Left": 0.6800444722175598, "Top": 0.5485329031944275}, "Polygon": [{"X": 0.6978069543838501, "Y": 0.5485329031944275}, {"X": 0.6975408792495728, "Y": 0.6130810976028442}, {"X": 0.6800444722175598, "Y": 0.6135703921318054}, {"X": 0.6802316904067993, "Y": 0.5489259958267212}]}, "Id": "d626e4e3-f5e7-42cb-9c9f-624bc22f329c", "Relationships": [{"Type": "CHILD", "Ids": ["6625e0ec-1171-4f21-a303-177c6d9f8143"]}]}, {"BlockType": "LINE", "Confidence": 99.4345932006836, "Text": "3.229", "Geometry": {"BoundingBox": {"Width": 0.018880175426602364, "Height": 0.054749052971601486, "Left": 0.684943437576294, "Top": 0.6790245175361633}, "Polygon": [{"X": 0.7038236260414124, "Y": 0.6790245175361633}, {"X": 0.7035759091377258, "Y": 0.7330580949783325}, {"X": 0.684943437576294, "Y": 0.7337735295295715}, {"X": 0.6851202845573425, "Y": 0.6796549558639526}]}, "Id": "43c54af7-c96b-4245-b16a-53381643ba57", "Relationships": [{"Type": "CHILD", "Ids": ["a0a5f0e6-c5e2-4dac-bd6e-c2577e102183"]}]}, {"BlockType": "LINE", "Confidence": 99.44993591308594, "Text": "315.73", "Geometry": {"BoundingBox": {"Width": 0.018922336399555206, "Height": 0.0654359757900238, "Left": 0.6894869804382324, "Top": 0.7878795266151428}, "Polygon": [{"X": 0.7084093689918518, "Y": 0.7878795266151428}, {"X": 0.7080902457237244, "Y": 0.8524079918861389}, {"X": 0.6894869804382324, "Y": 0.853315532207489}, {"X": 0.689720869064331, "Y": 0.7886864542961121}]}, "Id": "29851e1c-1c65-4ab0-b0fe-622fcbec3828", "Relationships": [{"Type": "CHILD", "Ids": ["0fd10bb2-0de7-4e76-91b6-6a9f63720932"]}]}, {"BlockType": "LINE", "Confidence": 64.64813995361328, "Text": "subtotal ===", "Geometry": {"BoundingBox": {"Width": 0.018877927213907242, "Height": 0.12209482491016388, "Left": 0.6407230496406555, "Top": 0.20683737099170685}, "Polygon": [{"X": 0.6596009731292725, "Y": 0.20697420835494995}, {"X": 0.6594275236129761, "Y": 0.3288706839084625}, {"X": 0.6407230496406555, "Y": 0.3289321959018707}, {"X": 0.6407397985458374, "Y": 0.20683737099170685}]}, "Id": "3e69411c-0731-4e96-84fc-df901f24c298", "Relationships": [{"Type": "CHILD", "Ids": ["616d851e-4a51-4bf7-ac8f-bf9678fe010a", "cd97cf68-a6b9-4ef5-9352-cbcd30638cd9"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "315.73", "Geometry": {"BoundingBox": {"Width": 0.019708838313817978, "Height": 0.06703101098537445, "Left": 0.634432852268219, "Top": 0.37539708614349365}, "Polygon": [{"X": 0.6541417241096497, "Y": 0.37539708614349365}, {"X": 0.6540707349777222, "Y": 0.4421714246273041}, {"X": 0.6344527006149292, "Y": 0.4424280822277069}, {"X": 0.634432852268219, "Y": 0.37554115056991577}]}, "Id": "247dc92c-42c0-4cda-89ac-572c69549d3a", "Relationships": [{"Type": "CHILD", "Ids": ["a5abee79-ae13-40aa-bc85-94577134806f"]}]}, {"BlockType": "LINE", "Confidence": 83.91182708740234, "Text": "sales tax ::::", "Geometry": {"BoundingBox": {"Width": 0.019206689670681953, "Height": 0.13631249964237213, "Left": 0.5897607803344727, "Top": 0.19352465867996216}, "Polygon": [{"X": 0.6086891293525696, "Y": 0.19368304312229156}, {"X": 0.6089674830436707, "Y": 0.32977429032325745}, {"X": 0.5902153253555298, "Y": 0.3298371732234955}, {"X": 0.5897607803344727, "Y": 0.19352465867996216}]}, "Id": "1807320f-dfdb-4707-a58b-7354aa53b834", "Relationships": [{"Type": "CHILD", "Ids": ["b25e7533-5844-4f5c-a842-d66fe1265ea2", "34528ea3-43b1-4ac9-8342-8450bd50c135", "e643b0b6-db9f-4dce-9e53-64e914ec820e"]}]}, {"BlockType": "LINE", "Confidence": 98.52519226074219, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.01664208434522152, "Height": 0.04443131387233734, "Left": 0.5876409411430359, "Top": 0.39943742752075195}, "Polygon": [{"X": 0.6041775345802307, "Y": 0.39943742752075195}, {"X": 0.6042830348014832, "Y": 0.44365188479423523}, {"X": 0.5877971649169922, "Y": 0.4438687562942505}, {"X": 0.5876409411430359, "Y": 0.3995920419692993}]}, "Id": "ff1a7050-27ac-4790-a295-a3f2988e0c77", "Relationships": [{"Type": "CHILD", "Ids": ["9bb2916b-9a3e-40c9-b644-505ca1ae051b"]}]}, {"BlockType": "LINE", "Confidence": 81.8615493774414, "Text": "TOTAL ====", "Geometry": {"BoundingBox": {"Width": 0.019667359068989754, "Height": 0.08603733777999878, "Left": 0.5631992816925049, "Top": 0.2441115379333496}, "Polygon": [{"X": 0.5825368165969849, "Y": 0.24418905377388}, {"X": 0.5828666090965271, "Y": 0.33008405566215515}, {"X": 0.5636432766914368, "Y": 0.3301488757133484}, {"X": 0.5631992816925049, "Y": 0.2441115379333496}]}, "Id": "1542ab50-e7d9-4e09-8dee-1ec60406d236", "Relationships": [{"Type": "CHILD", "Ids": ["7ac141c4-75db-4414-9117-7a840dcb7f38", "2f1d1736-f81c-4ad1-85d1-3f3ced0b24b3"]}]}, {"BlockType": "LINE", "Confidence": 99.7315444946289, "Text": "315.73", "Geometry": {"BoundingBox": {"Width": 0.01873810961842537, "Height": 0.06755582243204117, "Left": 0.5620203614234924, "Top": 0.3766874372959137}, "Polygon": [{"X": 0.5804881453514099, "Y": 0.3766874372959137}, {"X": 0.5807584524154663, "Y": 0.44400137662887573}, {"X": 0.5623769760131836, "Y": 0.44424325227737427}, {"X": 0.5620203614234924, "Y": 0.37682363390922546}]}, "Id": "330c47c0-874b-4bdb-9cc6-297b6ca61614", "Relationships": [{"Type": "CHILD", "Ids": ["e84928b3-a89d-4152-9fa0-3f4edfcd18c0"]}]}, {"BlockType": "LINE", "Confidence": 92.92583465576172, "Text": "Salesperson ID: 53268/ALL,CASHIERS", "Geometry": {"BoundingBox": {"Width": 0.029988432303071022, "Height": 0.41368141770362854, "Left": 0.5060635805130005, "Top": 0.10096487402915955}, "Polygon": [{"X": 0.5331017971038818, "Y": 0.10140315443277359}, {"X": 0.5360519886016846, "Y": 0.5141441226005554}, {"X": 0.509777307510376, "Y": 0.5146462917327881}, {"X": 0.5060635805130005, "Y": 0.10096487402915955}]}, "Id": "cfeaa27f-d45d-44cf-bf0e-0d856c923837", "Relationships": [{"Type": "CHILD", "Ids": ["cca98537-7ef1-49a5-80f3-b194c909e173", "dd1092d2-3e04-4751-8faf-af097ce21c07", "e5fab865-4840-49ef-abc1-8f706620f934"]}]}, {"BlockType": "LINE", "Confidence": 99.34723663330078, "Text": "Invoice #: 00010881", "Geometry": {"BoundingBox": {"Width": 0.022118955850601196, "Height": 0.20841246843338013, "Left": 0.5178424119949341, "Top": 0.6328248977661133}, "Polygon": [{"X": 0.5385017991065979, "Y": 0.6328248977661133}, {"X": 0.5399613380432129, "Y": 0.8402798175811768}, {"X": 0.5196058750152588, "Y": 0.8412373661994934}, {"X": 0.5178424119949341, "Y": 0.6334297060966492}]}, "Id": "e45482c3-7b25-48bb-afed-c64cb8596885", "Relationships": [{"Type": "CHILD", "Ids": ["771bd9ff-6515-4c8c-ae00-3b1581a1ad24", "b05c3271-9e95-4f33-be50-7851e8b6a39d", "630856ad-ee47-4c18-938d-fcd6fa95ccf8"]}]}, {"BlockType": "LINE", "Confidence": 99.73506164550781, "Text": "Truck Number: 3482", "Geometry": {"BoundingBox": {"Width": 0.02268272638320923, "Height": 0.22825738787651062, "Left": 0.48180338740348816, "Top": 0.1260964572429657}, "Polygon": [{"X": 0.5023801326751709, "Y": 0.12638525664806366}, {"X": 0.504486083984375, "Y": 0.3542442321777344}, {"X": 0.4842313528060913, "Y": 0.3543538451194763}, {"X": 0.48180338740348816, "Y": 0.1260964572429657}]}, "Id": "98a29daa-f704-4536-b71b-82a112f02fd1", "Relationships": [{"Type": "CHILD", "Ids": ["950049e1-06ab-427b-95f8-c959afaafe1d", "84fa9955-6cc0-4fe6-90cd-59cf7aea1936", "66f3bde2-87a0-4f0b-9660-4e10e93b66dc"]}]}, {"BlockType": "LINE", "Confidence": 91.9090805053711, "Text": "Billing Company: MISCELLANEOUS TENDERS / DRIVE OFF", "Geometry": {"BoundingBox": {"Width": 0.04702015966176987, "Height": 0.603670597076416, "Left": 0.4214055836200714, "Top": 0.04545839503407478}, "Polygon": [{"X": 0.461225301027298, "Y": 0.04628656432032585}, {"X": 0.4684257507324219, "Y": 0.6479688882827759}, {"X": 0.430249959230423, "Y": 0.6491290330886841}, {"X": 0.4214055836200714, "Y": 0.04545839503407478}]}, "Id": "91eb1425-1809-444a-9fa1-d8ea9c132e9e", "Relationships": [{"Type": "CHILD", "Ids": ["00e10547-7906-4a52-a7b9-0707a13bb1d7", "f7a9b691-1196-47ae-a678-133f7f4f15e4", "c5859b0b-dc91-462f-a20b-4b6d62b7cd2d", "a005da55-a799-45cb-98c7-fd92b1f717a8", "f2614350-4bff-4001-992f-d8c19c52109e", "d024792e-bcc1-4c01-a03a-1bbd83053518", "2e9dba82-5682-434d-9f06-7105ca1eb914"]}]}, {"BlockType": "LINE", "Confidence": 90.51564025878906, "Text": "Company Name : AMERLAX", "Geometry": {"BoundingBox": {"Width": 0.034147340804338455, "Height": 0.31125473976135254, "Left": 0.39198026061058044, "Top": 0.04430163651704788}, "Polygon": [{"X": 0.4215826392173767, "Y": 0.04491867125034332}, {"X": 0.4261275827884674, "Y": 0.3553978204727173}, {"X": 0.3971574902534485, "Y": 0.3555563688278198}, {"X": 0.39198026061058044, "Y": 0.04430163651704788}]}, "Id": "32e50f1f-5a31-4c94-ac6a-cb10a612e41e", "Relationships": [{"Type": "CHILD", "Ids": ["cef41ac4-8684-4f11-8e94-c9395d8035a0", "9be093c9-55cf-4e21-a063-e8b282e2c06d", "1dea3f98-83c0-4053-b10f-3e07665901f1", "be03d6cb-65d9-4c2b-9654-6e70258e2460"]}]}, {"BlockType": "LINE", "Confidence": 99.62332916259766, "Text": "THANK YOU!", "Geometry": {"BoundingBox": {"Width": 0.02475442737340927, "Height": 0.13009968400001526, "Left": 0.33866703510284424, "Top": 0.042009975761175156}, "Polygon": [{"X": 0.36099475622177124, "Y": 0.042477596551179886}, {"X": 0.3634214401245117, "Y": 0.1721096634864807}, {"X": 0.34129369258880615, "Y": 0.171888068318367}, {"X": 0.33866703510284424, "Y": 0.042009975761175156}]}, "Id": "1e13207f-9ad6-4edb-8af9-3fe36aa0e2b2", "Relationships": [{"Type": "CHILD", "Ids": ["31c6cdf7-5efd-4737-b899-e9c4afb231f3", "db2e9aad-405f-4d19-a2c9-0c7da1d25645"]}]}, {"BlockType": "LINE", "Confidence": 34.32686996459961, "Text": "*************", "Geometry": {"BoundingBox": {"Width": 0.0073314509354531765, "Height": 0.03918011114001274, "Left": 0.35275575518608093, "Top": 0.20316149294376373}, "Polygon": [{"X": 0.3593422472476959, "Y": 0.20321016013622284}, {"X": 0.36008718609809875, "Y": 0.24234160780906677}, {"X": 0.35351869463920593, "Y": 0.24231474101543427}, {"X": 0.35275575518608093, "Y": 0.20316149294376373}]}, "Id": "9fed38ac-1a6c-49c9-a549-86650c3e5815", "Relationships": [{"Type": "CHILD", "Ids": ["68db0872-0bcc-4645-9dc8-9d62426ddc8c"]}]}, {"BlockType": "LINE", "Confidence": 99.82221984863281, "Text": "THANK YOU!", "Geometry": {"BoundingBox": {"Width": 0.024699334055185318, "Height": 0.1197281926870346, "Left": 0.3502512574195862, "Top": 0.26839539408683777}, "Polygon": [{"X": 0.37277528643608093, "Y": 0.2684380114078522}, {"X": 0.3749505877494812, "Y": 0.38794049620628357}, {"X": 0.35261526703834534, "Y": 0.38812360167503357}, {"X": 0.3502512574195862, "Y": 0.26839539408683777}]}, "Id": "8e57759c-6a82-4945-8d45-21d3ecbd93b6", "Relationships": [{"Type": "CHILD", "Ids": ["d3071dfa-4f20-49b6-b4eb-955c266181fd", "4677c4fe-9bda-4a33-9342-5047593616c7"]}]}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018271232023835182, "Height": 0.0481625497341156, "Left": 0.8156300187110901, "Top": 0.06510356068611145}, "Polygon": [{"X": 0.8339012265205383, "Y": 0.06545388698577881}, {"X": 0.833271324634552, "Y": 0.11326611042022705}, {"X": 0.8156300187110901, "Y": 0.11299099028110504}, {"X": 0.8162037134170532, "Y": 0.06510356068611145}]}, "Id": "f578117a-59c4-4475-ad85-922ef84e113e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017931262031197548, "Height": 0.047662388533353806, "Left": 0.8154973387718201, "Top": 0.12488874047994614}, "Polygon": [{"X": 0.8334286212921143, "Y": 0.12514138221740723}, {"X": 0.832802951335907, "Y": 0.17255112528800964}, {"X": 0.8154973387718201, "Y": 0.17237135767936707}, {"X": 0.8160681128501892, "Y": 0.12488874047994614}]}, "Id": "e2e06fac-6e51-4025-9332-e846cca895d1"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "1281", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017730416730046272, "Height": 0.045254819095134735, "Left": 0.8159360289573669, "Top": 0.1861143559217453}, "Polygon": [{"X": 0.8336664438247681, "Y": 0.18627217411994934}, {"X": 0.8330681324005127, "Y": 0.23136918246746063}, {"X": 0.8159360289573669, "Y": 0.23127968609333038}, {"X": 0.8164824843406677, "Y": 0.1861143559217453}]}, "Id": "e0b47e70-b610-479d-98db-f4285a0bd19d"}, {"BlockType": "WORD", "Confidence": 99.32557678222656, "Text": "/106", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017829257994890213, "Height": 0.046670570969581604, "Left": 0.8154508471488953, "Top": 0.24483299255371094}, "Polygon": [{"X": 0.8332801461219788, "Y": 0.24490265548229218}, {"X": 0.8326606154441833, "Y": 0.29150256514549255}, {"X": 0.8154508471488953, "Y": 0.29150357842445374}, {"X": 0.8160162568092346, "Y": 0.24483299255371094}]}, "Id": "435f560e-229d-40bb-9944-3b5541080a2d"}, {"BlockType": "WORD", "Confidence": 88.04368591308594, "Text": "N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01761001907289028, "Height": 0.019077319651842117, "Left": 0.8131298422813416, "Top": 0.3035200536251068}, "Polygon": [{"X": 0.8307398557662964, "Y": 0.3035200536251068}, {"X": 0.8304891586303711, "Y": 0.32254907488822937}, {"X": 0.8131298422813416, "Y": 0.3225973844528198}, {"X": 0.8133581280708313, "Y": 0.3035393953323364}]}, "Id": "1f344f1b-4319-4ee6-ac00-32e9aca956b6"}, {"BlockType": "WORD", "Confidence": 95.7252426147461, "Text": "ROYAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021867770701646805, "Height": 0.05617889016866684, "Left": 0.8064277172088623, "Top": 0.3379836678504944}, "Polygon": [{"X": 0.8282954692840576, "Y": 0.3379836678504944}, {"X": 0.8275655508041382, "Y": 0.3939712345600128}, {"X": 0.8064277172088623, "Y": 0.3941625654697418}, {"X": 0.8070771098136902, "Y": 0.33807146549224854}]}, "Id": "1b235c6d-4751-4db4-98e4-5ddd10c88cbd"}, {"BlockType": "WORD", "Confidence": 98.8366928100586, "Text": "AVENUE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01973186992108822, "Height": 0.0661819726228714, "Left": 0.8032737970352173, "Top": 0.4065350592136383}, "Polygon": [{"X": 0.8230056166648865, "Y": 0.4065350592136383}, {"X": 0.8221667408943176, "Y": 0.4724160432815552}, {"X": 0.8032737970352173, "Y": 0.4727170169353485}, {"X": 0.8040274977684021, "Y": 0.40672770142555237}]}, "Id": "66cd1900-0875-4b94-b968-ea540a6e516d"}, {"BlockType": "WORD", "Confidence": 86.58183288574219, "Text": "/BELGIUM.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020848119631409645, "Height": 0.09290271252393723, "Left": 0.7997702956199646, "Top": 0.4831050634384155}, "Polygon": [{"X": 0.8206184506416321, "Y": 0.4831050634384155}, {"X": 0.8194507956504822, "Y": 0.5755163431167603}, {"X": 0.7997702956199646, "Y": 0.5760077834129333}, {"X": 0.8008126020431519, "Y": 0.4834390878677368}]}, "Id": "cda1e8d9-12f3-4bbc-9f81-92a3660653f8"}, {"BlockType": "WORD", "Confidence": 90.54428100585938, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01806398294866085, "Height": 0.021670950576663017, "Left": 0.8014482855796814, "Top": 0.5892229676246643}, "Polygon": [{"X": 0.8195122480392456, "Y": 0.5892229676246643}, {"X": 0.819244384765625, "Y": 0.610395073890686}, {"X": 0.8014482855796814, "Y": 0.6108939051628113}, {"X": 0.8016901016235352, "Y": 0.5896893739700317}]}, "Id": "69df109d-a96f-4f5e-ae83-80932d8be5cf"}, {"BlockType": "WORD", "Confidence": 96.5870132446289, "Text": "53004/(262)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024143029004335403, "Height": 0.11657798290252686, "Left": 0.8002669811248779, "Top": 0.6220905780792236}, "Polygon": [{"X": 0.8244100213050842, "Y": 0.6220905780792236}, {"X": 0.8229032158851624, "Y": 0.7377808690071106}, {"X": 0.8002669811248779, "Y": 0.7386685609817505}, {"X": 0.8015912175178528, "Y": 0.6227538585662842}]}, "Id": "152502cd-0479-4cb8-aa63-d02a489b1e7e"}, {"BlockType": "WORD", "Confidence": 93.4349136352539, "Text": "988-7024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019308021292090416, "Height": 0.0842452272772789, "Left": 0.8057875633239746, "Top": 0.7510377764701843}, "Polygon": [{"X": 0.8250955939292908, "Y": 0.7510377764701843}, {"X": 0.8239957094192505, "Y": 0.834414541721344}, {"X": 0.8057875633239746, "Y": 0.8352830410003662}, {"X": 0.8067809343338013, "Y": 0.7517774701118469}]}, "Id": "67d9ffce-d439-45b4-a6f0-ee6633fb1418"}, {"BlockType": "WORD", "Confidence": 97.39935302734375, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01786188781261444, "Height": 0.08249587565660477, "Left": 0.790631890296936, "Top": 0.06249561533331871}, "Polygon": [{"X": 0.8084937334060669, "Y": 0.06283555924892426}, {"X": 0.8075523972511292, "Y": 0.14499148726463318}, {"X": 0.790631890296936, "Y": 0.14477524161338806}, {"X": 0.7914801239967346, "Y": 0.06249561533331871}]}, "Id": "2f67bd78-ce02-44a8-946b-9734a4b3dec8"}, {"BlockType": "WORD", "Confidence": 97.78400421142578, "Text": "0100-0881", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018681218847632408, "Height": 0.1066003143787384, "Left": 0.7906609773635864, "Top": 0.1603780835866928}, "Polygon": [{"X": 0.8093421459197998, "Y": 0.16057869791984558}, {"X": 0.8081088066101074, "Y": 0.26697838306427}, {"X": 0.7906609773635864, "Y": 0.2669419050216675}, {"X": 0.7917689681053162, "Y": 0.1603780835866928}]}, "Id": "f1036678-e0ad-43d4-909e-357e79171581"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "10/10/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02620302326977253, "Height": 0.11345898360013962, "Left": 0.781022310256958, "Top": 0.2927057147026062}, "Polygon": [{"X": 0.8072253465652466, "Y": 0.2927057147026062}, {"X": 0.8059175610542297, "Y": 0.40591371059417725}, {"X": 0.781022310256958, "Y": 0.40616467595100403}, {"X": 0.7821378111839294, "Y": 0.2927097976207733}]}, "Id": "63b3535a-7cc5-4a79-a4be-4991d9be1b6c"}, {"BlockType": "WORD", "Confidence": 97.5302963256836, "Text": "Station:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021910546347498894, "Height": 0.08527960628271103, "Left": 0.7756621241569519, "Top": 0.42860129475593567}, "Polygon": [{"X": 0.797572672367096, "Y": 0.42860129475593567}, {"X": 0.7966397404670715, "Y": 0.5134720802307129}, {"X": 0.7756621241569519, "Y": 0.5138809084892273}, {"X": 0.7764726281166077, "Y": 0.4288557469844818}]}, "Id": "de321f1e-4cee-431a-b4bc-77378e236444"}, {"BlockType": "WORD", "Confidence": 85.90620422363281, "Text": "1.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017383437603712082, "Height": 0.008531793020665646, "Left": 0.7767187356948853, "Top": 0.5265275835990906}, "Polygon": [{"X": 0.7941021919250488, "Y": 0.5265275835990906}, {"X": 0.7940137982368469, "Y": 0.5346903204917908}, {"X": 0.7767187356948853, "Y": 0.53505939245224}, {"X": 0.776797354221344, "Y": 0.5268845558166504}]}, "Id": "ad426b3a-ec45-4f24-adf7-c15435f5913a"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020288163796067238, "Height": 0.04972032457590103, "Left": 0.7145830392837524, "Top": 0.05851023644208908}, "Polygon": [{"X": 0.7348712086677551, "Y": 0.05891481414437294}, {"X": 0.7345512509346008, "Y": 0.10823056101799011}, {"X": 0.7145830392837524, "Y": 0.1079130694270134}, {"X": 0.7148367762565613, "Y": 0.05851023644208908}]}, "Id": "0ca3ca8e-b2b7-44f9-bef4-72706cd47adb"}, {"BlockType": "WORD", "Confidence": 99.81205749511719, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025390800088644028, "Height": 0.0931907370686531, "Left": 0.7078756093978882, "Top": 0.30393868684768677}, "Polygon": [{"X": 0.7332664132118225, "Y": 0.30393868684768677}, {"X": 0.7326633930206299, "Y": 0.39690056443214417}, {"X": 0.7078756093978882, "Y": 0.3971294164657593}, {"X": 0.7083204388618469, "Y": 0.30396711826324463}]}, "Id": "bdca58d1-cf81-4a53-81c6-c85d23911e0e"}, {"BlockType": "WORD", "Confidence": 99.4834213256836, "Text": "Reefer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02037087082862854, "Height": 0.06552308052778244, "Left": 0.7026849389076233, "Top": 0.42934611439704895}, "Polygon": [{"X": 0.7230557799339294, "Y": 0.42934611439704895}, {"X": 0.7226754426956177, "Y": 0.494515061378479}, {"X": 0.7026849389076233, "Y": 0.494869202375412}, {"X": 0.7029751539230347, "Y": 0.4295880198478699}]}, "Id": "cd7db785-777d-4fe6-89a5-3928c81fd083"}, {"BlockType": "WORD", "Confidence": 99.67693328857422, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019769061356782913, "Height": 0.08625075221061707, "Left": 0.7014603614807129, "Top": 0.5262203812599182}, "Polygon": [{"X": 0.7212294340133667, "Y": 0.5262203812599182}, {"X": 0.7207367420196533, "Y": 0.6119328737258911}, {"X": 0.7014603614807129, "Y": 0.6124711036682129}, {"X": 0.7018378376960754, "Y": 0.5266172885894775}]}, "Id": "6d194a87-87a4-42c3-bd7e-758ca8bc891c"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018067821860313416, "Height": 0.0436241440474987, "Left": 0.706391453742981, "Top": 0.6339796185493469}, "Polygon": [{"X": 0.7244592308998108, "Y": 0.6339796185493469}, {"X": 0.7242003083229065, "Y": 0.6770055294036865}, {"X": 0.706391453742981, "Y": 0.6776037812232971}, {"X": 0.7065967321395874, "Y": 0.6345127820968628}]}, "Id": "67796d66-8481-4024-8e6e-adbd740bfcfe"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018066953867673874, "Height": 0.04492127522826195, "Left": 0.709307849407196, "Top": 0.6886562705039978}, "Polygon": [{"X": 0.727374792098999, "Y": 0.6886562705039978}, {"X": 0.7270984053611755, "Y": 0.7328932881355286}, {"X": 0.709307849407196, "Y": 0.73357754945755}, {"X": 0.7095288634300232, "Y": 0.68927401304245}]}, "Id": "ff269b48-6be2-4478-9c3c-286ac95604ac"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018072018399834633, "Height": 0.052846331149339676, "Left": 0.7132983803749084, "Top": 0.7977793216705322}, "Polygon": [{"X": 0.7313703894615173, "Y": 0.7977793216705322}, {"X": 0.7310283780097961, "Y": 0.8497630953788757}, {"X": 0.7132983803749084, "Y": 0.8506256341934204}, {"X": 0.7135751247406006, "Y": 0.7985645532608032}]}, "Id": "0f84e0e2-320b-491d-a383-f676dbcd2f07"}, {"BlockType": "WORD", "Confidence": 96.33231353759766, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017034366726875305, "Height": 0.012246075086295605, "Left": 0.6924898624420166, "Top": 0.05731113255023956}, "Polygon": [{"X": 0.7095242142677307, "Y": 0.05765535682439804}, {"X": 0.7094674110412598, "Y": 0.06955720484256744}, {"X": 0.6924898624420166, "Y": 0.069230817258358}, {"X": 0.6925331354141235, "Y": 0.05731113255023956}]}, "Id": "c7105815-5f24-4b66-9740-8bc74a0700e0"}, {"BlockType": "WORD", "Confidence": 99.1651382446289, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017174892127513885, "Height": 0.013175754807889462, "Left": 0.6931218504905701, "Top": 0.081513412296772}, "Polygon": [{"X": 0.7102967500686646, "Y": 0.08182445913553238}, {"X": 0.710234522819519, "Y": 0.09468916803598404}, {"X": 0.6931218504905701, "Y": 0.09439752250909805}, {"X": 0.6931692361831665, "Y": 0.081513412296772}]}, "Id": "d70a191a-3adf-44c7-9956-868f577b79b6"}, {"BlockType": "WORD", "Confidence": 99.5167007446289, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02121756039559841, "Height": 0.06856003403663635, "Left": 0.6864819526672363, "Top": 0.30475670099258423}, "Polygon": [{"X": 0.7076994776725769, "Y": 0.30475670099258423}, {"X": 0.7073757648468018, "Y": 0.3731673061847687}, {"X": 0.6864819526672363, "Y": 0.3733167350292206}, {"X": 0.6867075562477112, "Y": 0.30478206276893616}]}, "Id": "db950332-c4a8-40af-bc77-fe1319f4e7a7"}, {"BlockType": "WORD", "Confidence": 98.24358367919922, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016523487865924835, "Height": 0.010939089581370354, "Left": 0.6849694848060608, "Top": 0.3860973119735718}, "Polygon": [{"X": 0.701492965221405, "Y": 0.3860973119735718}, {"X": 0.7014462947845459, "Y": 0.3968847095966339}, {"X": 0.6849694848060608, "Y": 0.3970364034175873}, {"X": 0.6850039958953857, "Y": 0.3862336575984955}]}, "Id": "906f91ba-dc9b-4556-a9ef-e3754605d04e"}, {"BlockType": "WORD", "Confidence": 98.96842956542969, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01638827659189701, "Height": 0.011348837055265903, "Left": 0.6818737387657166, "Top": 0.45224958658218384}, "Polygon": [{"X": 0.6982619762420654, "Y": 0.45224958658218384}, {"X": 0.6982161998748779, "Y": 0.4633537530899048}, {"X": 0.6818737387657166, "Y": 0.46359843015670776}, {"X": 0.6819069981575012, "Y": 0.45247870683670044}]}, "Id": "93e5c08b-180b-4b74-b9af-ee86a1d80973"}, {"BlockType": "WORD", "Confidence": 98.69758605957031, "Text": "97.780", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017762508243322372, "Height": 0.06503745913505554, "Left": 0.6800444722175598, "Top": 0.5485329031944275}, "Polygon": [{"X": 0.6978069543838501, "Y": 0.5485329031944275}, {"X": 0.6975408792495728, "Y": 0.6130810976028442}, {"X": 0.6800444722175598, "Y": 0.6135703921318054}, {"X": 0.6802316904067993, "Y": 0.5489259958267212}]}, "Id": "6625e0ec-1171-4f21-a303-177c6d9f8143"}, {"BlockType": "WORD", "Confidence": 99.4345932006836, "Text": "3.229", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018880175426602364, "Height": 0.054749052971601486, "Left": 0.684943437576294, "Top": 0.6790245175361633}, "Polygon": [{"X": 0.7038236260414124, "Y": 0.6790245175361633}, {"X": 0.7035759091377258, "Y": 0.7330580949783325}, {"X": 0.684943437576294, "Y": 0.7337735295295715}, {"X": 0.6851202845573425, "Y": 0.6796549558639526}]}, "Id": "a0a5f0e6-c5e2-4dac-bd6e-c2577e102183"}, {"BlockType": "WORD", "Confidence": 99.44993591308594, "Text": "315.73", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018922336399555206, "Height": 0.0654359757900238, "Left": 0.6894869804382324, "Top": 0.7878795266151428}, "Polygon": [{"X": 0.7084093689918518, "Y": 0.7878795266151428}, {"X": 0.7080902457237244, "Y": 0.8524079918861389}, {"X": 0.6894869804382324, "Y": 0.853315532207489}, {"X": 0.689720869064331, "Y": 0.7886864542961121}]}, "Id": "0fd10bb2-0de7-4e76-91b6-6a9f63720932"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01887439750134945, "Height": 0.09634970873594284, "Left": 0.6407265663146973, "Top": 0.20683737099170685}, "Polygon": [{"X": 0.6596009731292725, "Y": 0.20697420835494995}, {"X": 0.6594641208648682, "Y": 0.30316710472106934}, {"X": 0.6407265663146973, "Y": 0.3031870722770691}, {"X": 0.6407397985458374, "Y": 0.20683737099170685}]}, "Id": "616d851e-4a51-4bf7-ac8f-bf9678fe010a"}, {"BlockType": "WORD", "Confidence": 29.53224754333496, "Text": "===", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008246926590800285, "Height": 0.011756892316043377, "Left": 0.6444141268730164, "Top": 0.3171631693840027}, "Polygon": [{"X": 0.6526610851287842, "Y": 0.3171631693840027}, {"X": 0.6526498198509216, "Y": 0.3288929760456085}, {"X": 0.6444141268730164, "Y": 0.32892006635665894}, {"X": 0.6444187164306641, "Y": 0.31718191504478455}]}, "Id": "cd97cf68-a6b9-4ef5-9352-cbcd30638cd9"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "315.73", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019708838313817978, "Height": 0.06703101098537445, "Left": 0.634432852268219, "Top": 0.37539708614349365}, "Polygon": [{"X": 0.6541417241096497, "Y": 0.37539708614349365}, {"X": 0.6540707349777222, "Y": 0.4421714246273041}, {"X": 0.6344527006149292, "Y": 0.4424280822277069}, {"X": 0.634432852268219, "Y": 0.37554115056991577}]}, "Id": "a5abee79-ae13-40aa-bc85-94577134806f"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017804289236664772, "Height": 0.062042757868766785, "Left": 0.5910114049911499, "Top": 0.19353511929512024}, "Polygon": [{"X": 0.6086891293525696, "Y": 0.19368304312229156}, {"X": 0.6088157296180725, "Y": 0.2555778920650482}, {"X": 0.5912128686904907, "Y": 0.2555244266986847}, {"X": 0.5910114049911499, "Y": 0.19353511929512024}]}, "Id": "b25e7533-5844-4f5c-a842-d66fe1265ea2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018091626465320587, "Height": 0.03357649967074394, "Left": 0.5900174379348755, "Top": 0.2704934775829315}, "Polygon": [{"X": 0.6080386638641357, "Y": 0.2705250084400177}, {"X": 0.6081090569496155, "Y": 0.3040495216846466}, {"X": 0.5901294350624084, "Y": 0.30406996607780457}, {"X": 0.5900174379348755, "Y": 0.2704934775829315}]}, "Id": "34528ea3-43b1-4ac9-8342-8450bd50c135"}, {"BlockType": "WORD", "Confidence": 51.923431396484375, "Text": "::::", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00885346345603466, "Height": 0.012207352556288242, "Left": 0.5943344235420227, "Top": 0.3176158666610718}, "Polygon": [{"X": 0.603158175945282, "Y": 0.3176158666610718}, {"X": 0.6031879186630249, "Y": 0.32979366183280945}, {"X": 0.5943716168403625, "Y": 0.32982322573661804}, {"X": 0.5943344235420227, "Y": 0.3176361918449402}]}, "Id": "e643b0b6-db9f-4dce-9e53-64e914ec820e"}, {"BlockType": "WORD", "Confidence": 98.52519226074219, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01664208434522152, "Height": 0.04443131387233734, "Left": 0.5876409411430359, "Top": 0.39943742752075195}, "Polygon": [{"X": 0.6041775345802307, "Y": 0.39943742752075195}, {"X": 0.6042830348014832, "Y": 0.44365188479423523}, {"X": 0.5877971649169922, "Y": 0.4438687562942505}, {"X": 0.5876409411430359, "Y": 0.3995920419692993}]}, "Id": "9bb2916b-9a3e-40c9-b644-505ca1ae051b"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01957329735159874, "Height": 0.06149912625551224, "Left": 0.5631992816925049, "Top": 0.2441115379333496}, "Polygon": [{"X": 0.5825368165969849, "Y": 0.24418905377388}, {"X": 0.5827725529670715, "Y": 0.3055862784385681}, {"X": 0.5635166764259338, "Y": 0.30561065673828125}, {"X": 0.5631992816925049, "Y": 0.2441115379333496}]}, "Id": "7ac141c4-75db-4414-9117-7a840dcb7f38"}, {"BlockType": "WORD", "Confidence": 63.80201721191406, "Text": "====", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008191482163965702, "Height": 0.01246592029929161, "Left": 0.5686972141265869, "Top": 0.317665696144104}, "Polygon": [{"X": 0.5768358707427979, "Y": 0.317665696144104}, {"X": 0.5768887400627136, "Y": 0.33010420203208923}, {"X": 0.5687571167945862, "Y": 0.3301316201686859}, {"X": 0.5686972141265869, "Y": 0.31768444180488586}]}, "Id": "2f1d1736-f81c-4ad1-85d1-3f3ced0b24b3"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "315.73", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01873810961842537, "Height": 0.06755582243204117, "Left": 0.5620203614234924, "Top": 0.3766874372959137}, "Polygon": [{"X": 0.5804881453514099, "Y": 0.3766874372959137}, {"X": 0.5807584524154663, "Y": 0.44400137662887573}, {"X": 0.5623769760131836, "Y": 0.44424325227737427}, {"X": 0.5620203614234924, "Y": 0.37682363390922546}]}, "Id": "e84928b3-a89d-4152-9fa0-3f4edfcd18c0"}, {"BlockType": "WORD", "Confidence": 99.45890045166016, "Text": "Salesperson", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022720955312252045, "Height": 0.14138725399971008, "Left": 0.5060635805130005, "Top": 0.10096487402915955}, "Polygon": [{"X": 0.527725100517273, "Y": 0.10131600499153137}, {"X": 0.5287845134735107, "Y": 0.24235212802886963}, {"X": 0.5073320269584656, "Y": 0.24226315319538116}, {"X": 0.5060635805130005, "Y": 0.10096487402915955}]}, "Id": "cca98537-7ef1-49a5-80f3-b194c909e173"}, {"BlockType": "WORD", "Confidence": 96.50032043457031, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01896667666733265, "Height": 0.03319154307246208, "Left": 0.5108234286308289, "Top": 0.2579314410686493}, "Polygon": [{"X": 0.5295426249504089, "Y": 0.25798407196998596}, {"X": 0.5297901034355164, "Y": 0.29112255573272705}, {"X": 0.5111137628555298, "Y": 0.29112300276756287}, {"X": 0.5108234286308289, "Y": 0.2579314410686493}]}, "Id": "dd1092d2-3e04-4751-8faf-af097ce21c07"}, {"BlockType": "WORD", "Confidence": 82.81828308105469, "Text": "53268/ALL,CASHIERS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027867862954735756, "Height": 0.2084798663854599, "Left": 0.5081841349601746, "Top": 0.30616116523742676}, "Polygon": [{"X": 0.5345653295516968, "Y": 0.30616116523742676}, {"X": 0.5360519886016846, "Y": 0.5141441226005554}, {"X": 0.510051429271698, "Y": 0.5146410465240479}, {"X": 0.5081841349601746, "Y": 0.3061957359313965}]}, "Id": "e5fab865-4840-49ef-abc1-8f706620f934"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01780247688293457, "Height": 0.07565826922655106, "Left": 0.5178424119949341, "Top": 0.632924497127533}, "Polygon": [{"X": 0.5350989699363708, "Y": 0.632924497127533}, {"X": 0.5356448888778687, "Y": 0.7079700231552124}, {"X": 0.518480122089386, "Y": 0.7085828185081482}, {"X": 0.5178424119949341, "Y": 0.6334297060966492}]}, "Id": "771bd9ff-6515-4c8c-ae00-3b1581a1ad24"}, {"BlockType": "WORD", "Confidence": 98.14990997314453, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016733326017856598, "Height": 0.02059628814458847, "Left": 0.5194962024688721, "Top": 0.7185139060020447}, "Polygon": [{"X": 0.5360848307609558, "Y": 0.7185139060020447}, {"X": 0.5362295508384705, "Y": 0.7384755611419678}, {"X": 0.5196645259857178, "Y": 0.7391102313995361}, {"X": 0.5194962024688721, "Y": 0.7191211581230164}]}, "Id": "b05c3271-9e95-4f33-be50-7851e8b6a39d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "00010881", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01999007910490036, "Height": 0.08923882246017456, "Left": 0.5199712514877319, "Top": 0.7519463896751404}, "Polygon": [{"X": 0.5393399000167847, "Y": 0.7519463896751404}, {"X": 0.5399613380432129, "Y": 0.8402798175811768}, {"X": 0.520715057849884, "Y": 0.8411852121353149}, {"X": 0.5199712514877319, "Y": 0.7527109980583191}]}, "Id": "630856ad-ee47-4c18-938d-fcd6fa95ccf8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019674120470881462, "Height": 0.06657193601131439, "Left": 0.48180338740348816, "Top": 0.1260964572429657}, "Polygon": [{"X": 0.5008578300476074, "Y": 0.12636388838291168}, {"X": 0.5014774799346924, "Y": 0.1926683932542801}, {"X": 0.4825098216533661, "Y": 0.1925094872713089}, {"X": 0.48180338740348816, "Y": 0.1260964572429657}]}, "Id": "950049e1-06ab-427b-95f8-c959afaafe1d"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019584866240620613, "Height": 0.0860593169927597, "Left": 0.4833084046840668, "Top": 0.20419570803642273}, "Polygon": [{"X": 0.5020932555198669, "Y": 0.20433439314365387}, {"X": 0.5028932690620422, "Y": 0.29025503993034363}, {"X": 0.48421984910964966, "Y": 0.2902540862560272}, {"X": 0.4833084046840668, "Y": 0.20419570803642273}]}, "Id": "84fa9955-6cc0-4fe6-90cd-59cf7aea1936"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "3482", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01926606521010399, "Height": 0.04847216233611107, "Left": 0.4852200448513031, "Top": 0.3058735728263855}, "Polygon": [{"X": 0.5040390491485596, "Y": 0.3058735728263855}, {"X": 0.504486083984375, "Y": 0.3542442321777344}, {"X": 0.48573037981987, "Y": 0.35434573888778687}, {"X": 0.4852200448513031, "Y": 0.30589771270751953}]}, "Id": "66f3bde2-87a0-4f0b-9660-4e10e93b66dc"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0242368895560503, "Height": 0.09172605723142624, "Left": 0.4214055836200714, "Top": 0.04545839503407478}, "Polygon": [{"X": 0.4444473385810852, "Y": 0.045937616378068924}, {"X": 0.44564247131347656, "Y": 0.13718445599079132}, {"X": 0.42274510860443115, "Y": 0.1368856281042099}, {"X": 0.4214055836200714, "Y": 0.04545839503407478}]}, "Id": "00e10547-7906-4a52-a7b9-0707a13bb1d7"}, {"BlockType": "WORD", "Confidence": 95.6259994506836, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023579824715852737, "Height": 0.10179679095745087, "Left": 0.42436739802360535, "Top": 0.15150049328804016}, "Polygon": [{"X": 0.446622759103775, "Y": 0.15176339447498322}, {"X": 0.4479472041130066, "Y": 0.25329726934432983}, {"X": 0.4258480370044708, "Y": 0.25322678685188293}, {"X": 0.42436739802360535, "Y": 0.15150049328804016}]}, "Id": "f7a9b691-1196-47ae-a678-133f7f4f15e4"}, {"BlockType": "WORD", "Confidence": 98.50187683105469, "Text": "MISCELLANEOUS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02428356185555458, "Height": 0.15756240487098694, "Left": 0.4311334490776062, "Top": 0.26854366064071655}, "Polygon": [{"X": 0.45342230796813965, "Y": 0.26858580112457275}, {"X": 0.4554170072078705, "Y": 0.42585307359695435}, {"X": 0.43337222933769226, "Y": 0.4261060655117035}, {"X": 0.4311334490776062, "Y": 0.26854366064071655}]}, "Id": "c5859b0b-dc91-462f-a20b-4b6d62b7cd2d"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "TENDERS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025275209918618202, "Height": 0.08093307167291641, "Left": 0.43757569789886475, "Top": 0.43714550137519836}, "Polygon": [{"X": 0.4618654251098633, "Y": 0.43714550137519836}, {"X": 0.4628508985042572, "Y": 0.5176127552986145}, {"X": 0.43869879841804504, "Y": 0.5180785655975342}, {"X": 0.43757569789886475, "Y": 0.437447726726532}]}, "Id": "a005da55-a799-45cb-98c7-fd92b1f717a8"}, {"BlockType": "WORD", "Confidence": 49.7369270324707, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01902942918241024, "Height": 0.011341686360538006, "Left": 0.4481028616428375, "Top": 0.5289442539215088}, "Polygon": [{"X": 0.46700137853622437, "Y": 0.5289442539215088}, {"X": 0.4671323001384735, "Y": 0.5398857593536377}, {"X": 0.4482484459877014, "Y": 0.5402859449386597}, {"X": 0.4481028616428375, "Y": 0.5293270945549011}]}, "Id": "f2614350-4bff-4001-992f-d8c19c52109e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DRIVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020217809826135635, "Height": 0.05554290860891342, "Left": 0.44732144474983215, "Top": 0.5512295961380005}, "Polygon": [{"X": 0.46687933802604675, "Y": 0.5512295961380005}, {"X": 0.4675392806529999, "Y": 0.6062496304512024}, {"X": 0.44805771112442017, "Y": 0.6067724823951721}, {"X": 0.44732144474983215, "Y": 0.5516628623008728}]}, "Id": "d024792e-bcc1-4c01-a03a-1bbd83053518"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "OFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01812051050364971, "Height": 0.03192717209458351, "Left": 0.44705817103385925, "Top": 0.6166782379150391}, "Polygon": [{"X": 0.464795857667923, "Y": 0.6166782379150391}, {"X": 0.4651786684989929, "Y": 0.6480675339698792}, {"X": 0.4474806487560272, "Y": 0.6486054062843323}, {"X": 0.44705817103385925, "Y": 0.6171698570251465}]}, "Id": "2e9dba82-5682-434d-9f06-7105ca1eb914"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023981375619769096, "Height": 0.09300083667039871, "Left": 0.39198026061058044, "Top": 0.04430163651704788}, "Polygon": [{"X": 0.4145633578300476, "Y": 0.04477236047387123}, {"X": 0.4159616231918335, "Y": 0.1373024731874466}, {"X": 0.39352232217788696, "Y": 0.1370105892419815}, {"X": 0.39198026061058044, "Y": 0.04430163651704788}]}, "Id": "cef41ac4-8684-4f11-8e94-c9395d8035a0"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019914701581001282, "Height": 0.05256429687142372, "Left": 0.3997201919555664, "Top": 0.1508517563343048}, "Polygon": [{"X": 0.4188535213470459, "Y": 0.15107837319374084}, {"X": 0.4196348786354065, "Y": 0.20341606438159943}, {"X": 0.40057092905044556, "Y": 0.20327481627464294}, {"X": 0.3997201919555664, "Y": 0.1508517563343048}]}, "Id": "9be093c9-55cf-4e21-a063-e8b282e2c06d"}, {"BlockType": "WORD", "Confidence": 62.4180908203125, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014190101996064186, "Height": 0.007266702130436897, "Left": 0.4035797417163849, "Top": 0.24566875398159027}, "Polygon": [{"X": 0.4176608622074127, "Y": 0.24572259187698364}, {"X": 0.4177698493003845, "Y": 0.2529354393482208}, {"X": 0.4036957919597626, "Y": 0.2528902590274811}, {"X": 0.4035797417163849, "Y": 0.24566875398159027}]}, "Id": "1dea3f98-83c0-4053-b10f-3e07665901f1"}, {"BlockType": "WORD", "Confidence": 99.76322937011719, "Text": "AMERLAX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0218681488186121, "Height": 0.08745266497135162, "Left": 0.40425944328308105, "Top": 0.2680571675300598}, "Polygon": [{"X": 0.42484962940216064, "Y": 0.2680968642234802}, {"X": 0.4261275827884674, "Y": 0.3553978204727173}, {"X": 0.40566286444664, "Y": 0.35550981760025024}, {"X": 0.40425944328308105, "Y": 0.2680571675300598}]}, "Id": "be03d6cb-65d9-4c2b-9654-6e70258e2460"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021748749539256096, "Height": 0.06745258718729019, "Left": 0.33866703510284424, "Top": 0.042009975761175156}, "Polygon": [{"X": 0.35915279388427734, "Y": 0.042439017444849014}, {"X": 0.3604157567024231, "Y": 0.10946255922317505}, {"X": 0.3400248885154724, "Y": 0.10915070027112961}, {"X": 0.33866703510284424, "Y": 0.042009975761175156}]}, "Id": "31c6cdf7-5efd-4737-b899-e9c4afb231f3"}, {"BlockType": "WORD", "Confidence": 99.25642395019531, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021349994465708733, "Height": 0.048977483063936234, "Left": 0.34207144379615784, "Top": 0.12313217669725418}, "Polygon": [{"X": 0.36250999569892883, "Y": 0.12342076003551483}, {"X": 0.3634214401245117, "Y": 0.1721096634864807}, {"X": 0.3430520296096802, "Y": 0.17190566658973694}, {"X": 0.34207144379615784, "Y": 0.12313217669725418}]}, "Id": "db2e9aad-405f-4d19-a2c9-0c7da1d25645"}, {"BlockType": "WORD", "Confidence": 34.32686996459961, "Text": "*************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0073314509354531765, "Height": 0.03918011114001274, "Left": 0.35275575518608093, "Top": 0.20316149294376373}, "Polygon": [{"X": 0.3593422472476959, "Y": 0.20321016013622284}, {"X": 0.36008718609809875, "Y": 0.24234160780906677}, {"X": 0.35351869463920593, "Y": 0.24231474101543427}, {"X": 0.35275575518608093, "Y": 0.20316149294376373}]}, "Id": "68db0872-0bcc-4645-9dc8-9d62426ddc8c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0215834379196167, "Height": 0.06334859132766724, "Left": 0.3502512574195862, "Top": 0.26839539408683777}, "Polygon": [{"X": 0.3706744313240051, "Y": 0.2684340178966522}, {"X": 0.3718346953392029, "Y": 0.33167392015457153}, {"X": 0.35150206089019775, "Y": 0.331743985414505}, {"X": 0.3502512574195862, "Y": 0.26839539408683777}]}, "Id": "d3071dfa-4f20-49b6-b4eb-955c266181fd"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020320400595664978, "Height": 0.04456040635704994, "Left": 0.3546302020549774, "Top": 0.3435395359992981}, "Polygon": [{"X": 0.3741423487663269, "Y": 0.3435395359992981}, {"X": 0.3749505877494812, "Y": 0.38794049620628357}, {"X": 0.3554994463920593, "Y": 0.38809993863105774}, {"X": 0.3546302020549774, "Y": 0.34362635016441345}]}, "Id": "4677c4fe-9bda-4a33-9342-5047593616c7"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "0638f1cc-f3f2-4975-805f-4d872cda32eb", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "0638f1cc-f3f2-4975-805f-4d872cda32eb", "content-type": "application/x-amz-json-1.1", "content-length": "48065", "date": "Wed, 24 Sep 2025 12:47:12 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "d0d1896d-5ce5-4424-88ef-4bebfc65f5e0", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:14 GMT", "content-type": "application/json", "content-length": "885", "connection": "keep-alive", "x-amzn-requestid": "d0d1896d-5ce5-4424-88ef-4bebfc65f5e0"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. The content looks like a fuel receipt: It shows \"Pump\", \"Products\", \"Diesel\", \"Total\", \"Invoice #\", \"Truck Number\", \"Billing Company\", etc. So it's a fuel receipt. According to definitions, fuel_receipt keywords: Fuel, Diesel, Pump, gallons, etc. This page has Diesel, Pump, etc. So classify as fuel_receipt. No continuation signals. So output: array with one object: page_no:1 doc_type:\"fuel_receipt\". Use tool."}}}, {"toolUse": {"toolUseId": "tooluse_yu0EuyXhRwSHP4N6NApYdw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2697, "outputTokens": 135, "totalTokens": 2832}, "metrics": {"latencyMs": 762}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/bfce0b37_ECFAS69KVA24ZYZZIU14.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP 1281 /106 N. ROYAL AVENUE /BELGIUM. WI 53004/(*************\nNumber: 0100-0881 10/10/2024 Station: 1.\nPump\nProducts Reefer Quantity Unit Cost\nTotal\n# 2\nDiesel 2\nN\n97.780\n3.229\n315.73\nsubtotal ===\n315.73\nsales tax ::::\n0.00\nTOTAL ====\n315.73\nSalesperson ID: 53268/ALL,CASHIERS\nInvoice #: 00010881\nTruck Number: 3482\nBilling Company: MISCELLANEOUS TENDERS / DRIVE OFF\nCompany Name : AMERLAX\nTHANK YOU!\n*************\nTHANK YOU!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}