{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 5.584165137406671e-06}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 6.305848273768788e-06, "Y": 1.0}]}, "Id": "87497f43-3566-4d12-a5b5-82c84aec12e9", "Relationships": [{"Type": "CHILD", "Ids": ["82e97e20-628b-4a36-8954-0da0687355d3", "f15435bf-a725-4fcd-85b9-5e1fea0a5ea7", "22ee830c-da44-4635-9e38-63a307b4a03d", "a021125b-7092-4c18-84c2-7b95a750bcb9", "aa23c370-9df7-45a5-af62-8af18e256fbf", "476e4706-92a8-4f1e-92d3-dde7a00ff1ed", "30976301-7c58-403c-90ac-0310abc1abd2", "1988a23d-88fb-486b-b8d8-0b2c690da411", "213f4946-9fb5-43d7-bb3c-af8787cb2d58", "ee8c3bd6-8883-4143-bd26-f308efe91ed1", "9c8b34f0-8f03-4ea1-8155-985040c18bfc", "7a66de89-c135-484c-a687-a1122b327bbf", "53452913-5ae3-4e52-9cf5-ce5bfeea6780", "32c162e3-d876-49f5-a640-4d6107eda01c", "119b4c6d-2b82-4806-8864-e76bedd81da3", "9e05f475-6b19-4e22-b8bb-c81126874f1a", "c852d519-bfeb-4dfc-8eef-d5a1b307f82b", "9134d3f9-cba3-48a5-9fc3-c6732f8d233c", "19168d93-29cc-467a-bb8d-2f85869d71a7", "6e210787-8702-4ce5-9b53-6c072f0f46c3", "be2dca80-78f6-459e-981f-cbb3cff892fb", "b3477daa-7d84-4f36-ae54-26e5ae8a2a8b", "793313d6-106f-49df-82c5-42129d9dce73", "60459f01-c203-4074-9e38-a35882cf45af", "3835e0a3-4aff-42e4-89dc-8cddd314f08e"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "License", "Geometry": {"BoundingBox": {"Width": 0.04416491463780403, "Height": 0.14279916882514954, "Left": 0.016434943303465843, "Top": 0.1937331110239029}, "Polygon": [{"X": 0.016434943303465843, "Y": 0.1940050572156906}, {"X": 0.06048756092786789, "Y": 0.1937331110239029}, {"X": 0.060599859803915024, "Y": 0.33629313111305237}, {"X": 0.016547249630093575, "Y": 0.3365322947502136}]}, "Id": "82e97e20-628b-4a36-8954-0da0687355d3", "Relationships": [{"Type": "CHILD", "Ids": ["d9b4fa34-6f21-4d99-94e4-dd312c04b73d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.04349987208843231, "Height": 0.17575329542160034, "Left": 0.11139988154172897, "Top": 0.1933315396308899}, "Polygon": [{"X": 0.11139988154172897, "Y": 0.1935991793870926}, {"X": 0.15476158261299133, "Y": 0.1933315396308899}, {"X": 0.15489976108074188, "Y": 0.36885693669319153}, {"X": 0.11153806746006012, "Y": 0.36908483505249023}]}, "Id": "f15435bf-a725-4fcd-85b9-5e1fea0a5ea7", "Relationships": [{"Type": "CHILD", "Ids": ["e9749857-a2de-4793-8c39-236858307a68"]}]}, {"BlockType": "LINE", "Confidence": 98.84407043457031, "Text": "Container", "Geometry": {"BoundingBox": {"Width": 0.05552111193537712, "Height": 0.14300943911075592, "Left": 0.18934492766857147, "Top": 0.19341948628425598}, "Polygon": [{"X": 0.18934492766857147, "Y": 0.19376130402088165}, {"X": 0.24475377798080444, "Y": 0.19341948628425598}, {"X": 0.2448660433292389, "Y": 0.3361283838748932}, {"X": 0.18945720791816711, "Y": 0.3364289402961731}]}, "Id": "22ee830c-da44-4635-9e38-63a307b4a03d", "Relationships": [{"Type": "CHILD", "Ids": ["2bc8ac49-66cd-4f11-ab86-c353d89b60a0"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.04462762176990509, "Height": 0.1440693885087967, "Left": 0.3003682494163513, "Top": 0.19227971136569977}, "Polygon": [{"X": 0.3003682494163513, "Y": 0.19255444407463074}, {"X": 0.3448827862739563, "Y": 0.19227971136569977}, {"X": 0.3449958562850952, "Y": 0.3361077606678009}, {"X": 0.30048131942749023, "Y": 0.33634909987449646}]}, "Id": "a021125b-7092-4c18-84c2-7b95a750bcb9", "Relationships": [{"Type": "CHILD", "Ids": ["c84c7c0c-0b96-432e-a6b5-08157d9426cc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Genset", "Geometry": {"BoundingBox": {"Width": 0.040061596781015396, "Height": 0.1402297019958496, "Left": 0.3645818531513214, "Top": 0.19547711312770844}, "Polygon": [{"X": 0.3645818531513214, "Y": 0.19572293758392334}, {"X": 0.40453341603279114, "Y": 0.19547711312770844}, {"X": 0.4046434462070465, "Y": 0.3354901671409607}, {"X": 0.3646918833255768, "Y": 0.33570683002471924}]}, "Id": "aa23c370-9df7-45a5-af62-8af18e256fbf", "Relationships": [{"Type": "CHILD", "Ids": ["c16f1985-1a84-4143-aa9e-bdee0caa12cd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Line", "Geometry": {"BoundingBox": {"Width": 0.024044163525104523, "Height": 0.14111773669719696, "Left": 0.4229796826839447, "Top": 0.19348369538784027}, "Polygon": [{"X": 0.4229796826839447, "Y": 0.19363118708133698}, {"X": 0.44691306352615356, "Y": 0.19348369538784027}, {"X": 0.44702383875846863, "Y": 0.33447155356407166}, {"X": 0.4230904281139374, "Y": 0.33460143208503723}]}, "Id": "476e4706-92a8-4f1e-92d3-dde7a00ff1ed", "Relationships": [{"Type": "CHILD", "Ids": ["7778a597-e743-45c3-9dcc-fefc8a8e82fd"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Cat", "Geometry": {"BoundingBox": {"Width": 0.019892239943146706, "Height": 0.13987398147583008, "Left": 0.46541470289230347, "Top": 0.1956511288881302}, "Polygon": [{"X": 0.46541470289230347, "Y": 0.19577278196811676}, {"X": 0.48519715666770935, "Y": 0.1956511288881302}, {"X": 0.48530691862106323, "Y": 0.33541786670684814}, {"X": 0.46552446484565735, "Y": 0.3355250954627991}]}, "Id": "30976301-7c58-403c-90ac-0310abc1abd2", "Relationships": [{"Type": "CHILD", "Ids": ["24cb57c3-02d0-4d3c-8328-bac966687b70"]}]}, {"BlockType": "LINE", "Confidence": 98.96563720703125, "Text": "Weight (MT)", "Geometry": {"BoundingBox": {"Width": 0.07001156359910965, "Height": 0.18225336074829102, "Left": 0.5201161503791809, "Top": 0.18754735589027405}, "Polygon": [{"X": 0.5201161503791809, "Y": 0.18797972798347473}, {"X": 0.5899849534034729, "Y": 0.18754735589027405}, {"X": 0.59012770652771, "Y": 0.36943453550338745}, {"X": 0.520258903503418, "Y": 0.36980071663856506}]}, "Id": "1988a23d-88fb-486b-b8d8-0b2c690da411", "Relationships": [{"Type": "CHILD", "Ids": ["3f1f7f61-5f42-48ad-b9e9-cefc0dea57f5", "218293fd-93a1-42d5-afc9-084e0b2983b7"]}]}, {"BlockType": "LINE", "Confidence": 99.41346740722656, "Text": "Sz/Tp", "Geometry": {"BoundingBox": {"Width": 0.032941848039627075, "Height": 0.17241768538951874, "Left": 0.6192891001701355, "Top": 0.19500623643398285}, "Polygon": [{"X": 0.6192891001701355, "Y": 0.19520790874958038}, {"X": 0.6520957946777344, "Y": 0.19500623643398285}, {"X": 0.6522309184074402, "Y": 0.3672516644001007}, {"X": 0.6194242238998413, "Y": 0.3674239218235016}]}, "Id": "213f4946-9fb5-43d7-bb3c-af8787cb2d58", "Relationships": [{"Type": "CHILD", "Ids": ["40809841-6821-49ad-bdc1-2f69f360c6dc"]}]}, {"BlockType": "LINE", "Confidence": 99.75426483154297, "Text": "Vessel/Voyage", "Geometry": {"BoundingBox": {"Width": 0.08484594523906708, "Height": 0.18061290681362152, "Left": 0.6647131443023682, "Top": 0.18954519927501678}, "Polygon": [{"X": 0.6647131443023682, "Y": 0.1900680810213089}, {"X": 0.7494178414344788, "Y": 0.18954519927501678}, {"X": 0.7495591044425964, "Y": 0.3697146773338318}, {"X": 0.6648544073104858, "Y": 0.3701581060886383}]}, "Id": "ee8c3bd6-8883-4143-bd26-f308efe91ed1", "Relationships": [{"Type": "CHILD", "Ids": ["02596ee8-f3dd-49f7-90e8-a6fc2ea58e2e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.02608916535973549, "Height": 0.13864627480506897, "Left": 0.7806330323219299, "Top": 0.19681869447231293}, "Polygon": [{"X": 0.7806330323219299, "Y": 0.19697804749011993}, {"X": 0.8066136837005615, "Y": 0.19681869447231293}, {"X": 0.8067222237586975, "Y": 0.33532434701919556}, {"X": 0.7807415723800659, "Y": 0.3354649841785431}]}, "Id": "9c8b34f0-8f03-4ea1-8155-985040c18bfc", "Relationships": [{"Type": "CHILD", "Ids": ["a6a5a4be-dea8-42fb-ae90-a4873d9ba4d6"]}]}, {"BlockType": "LINE", "Confidence": 99.67215728759766, "Text": "Time In", "Geometry": {"BoundingBox": {"Width": 0.042558301240205765, "Height": 0.14026670157909393, "Left": 0.8704893589019775, "Top": 0.19468924403190613}, "Polygon": [{"X": 0.8704893589019775, "Y": 0.1949499100446701}, {"X": 0.9129379987716675, "Y": 0.19468924403190613}, {"X": 0.9130476713180542, "Y": 0.3347261846065521}, {"X": 0.8705990314483643, "Y": 0.33495593070983887}]}, "Id": "7a66de89-c135-484c-a687-a1122b327bbf", "Relationships": [{"Type": "CHILD", "Ids": ["a9524004-2776-4aeb-8f85-cc85520c96a6", "9198c867-227b-4109-a320-33c8574e2b23"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "Time Out", "Geometry": {"BoundingBox": {"Width": 0.05258886143565178, "Height": 0.14039336144924164, "Left": 0.9367455840110779, "Top": 0.19451090693473816}, "Polygon": [{"X": 0.9367455840110779, "Y": 0.19483309984207153}, {"X": 0.9892247915267944, "Y": 0.19451090693473816}, {"X": 0.9893344044685364, "Y": 0.33462029695510864}, {"X": 0.9368551969528198, "Y": 0.3349042534828186}]}, "Id": "53452913-5ae3-4e52-9cf5-ce5bfeea6780", "Relationships": [{"Type": "CHILD", "Ids": ["a4e6af6f-f6d4-4cfc-a5f8-a67ceb917f42", "39f85165-7211-422d-80d4-a807bdf5ec20"]}]}, {"BlockType": "LINE", "Confidence": 99.52069091796875, "Text": "D12713DWA", "Geometry": {"BoundingBox": {"Width": 0.07053928077220917, "Height": 0.1378844827413559, "Left": 0.01685773953795433, "Top": 0.5828462243080139}, "Polygon": [{"X": 0.01685773953795433, "Y": 0.5831378102302551}, {"X": 0.08728861808776855, "Y": 0.5828462243080139}, {"X": 0.0873970165848732, "Y": 0.7204897999763489}, {"X": 0.016966156661510468, "Y": 0.720730721950531}]}, "Id": "32c162e3-d876-49f5-a640-4d6107eda01c", "Relationships": [{"Type": "CHILD", "Ids": ["96bfec7b-e2dc-4d8d-a162-8517eaee930d"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "FULL OUT", "Geometry": {"BoundingBox": {"Width": 0.05553096905350685, "Height": 0.14442013204097748, "Left": 0.11230527609586716, "Top": 0.5801776051521301}, "Polygon": [{"X": 0.11230527609586716, "Y": 0.5804076790809631}, {"X": 0.16772271692752838, "Y": 0.5801776051521301}, {"X": 0.1678362488746643, "Y": 0.7244093418121338}, {"X": 0.11241881549358368, "Y": 0.724597692489624}]}, "Id": "119b4c6d-2b82-4806-8864-e76bedd81da3", "Relationships": [{"Type": "CHILD", "Ids": ["c532b3da-e2ea-4899-8330-e93986c3ca1a", "d7ff94a3-a818-466a-9b9e-538b11752074"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "MEDU4189050", "Geometry": {"BoundingBox": {"Width": 0.08308784663677216, "Height": 0.1369239240884781, "Left": 0.18968966603279114, "Top": 0.5830782055854797}, "Polygon": [{"X": 0.18968966603279114, "Y": 0.5834212899208069}, {"X": 0.27267006039619446, "Y": 0.5830782055854797}, {"X": 0.2727775275707245, "Y": 0.7197181582450867}, {"X": 0.18979716300964355, "Y": 0.7200021147727966}]}, "Id": "9e05f475-6b19-4e22-b8bb-c81126874f1a", "Relationships": [{"Type": "CHILD", "Ids": ["92f91128-0bc5-441b-9ec6-c4edd4ac609c"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "OWN", "Geometry": {"BoundingBox": {"Width": 0.02833348698914051, "Height": 0.13703984022140503, "Left": 0.3006397783756256, "Top": 0.5823085308074951}, "Polygon": [{"X": 0.3006397783756256, "Y": 0.5824252963066101}, {"X": 0.3288656175136566, "Y": 0.5823085308074951}, {"X": 0.32897329330444336, "Y": 0.7192517518997192}, {"X": 0.30074745416641235, "Y": 0.7193483710289001}]}, "Id": "c852d519-bfeb-4dfc-8eef-d5a1b307f82b", "Relationships": [{"Type": "CHILD", "Ids": ["26f758ad-a6bd-48d7-b6bc-b9079878dec2"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "MSC", "Geometry": {"BoundingBox": {"Width": 0.026454215869307518, "Height": 0.13807383179664612, "Left": 0.42332708835601807, "Top": 0.5831447839736938}, "Polygon": [{"X": 0.42332708835601807, "Y": 0.5832536220550537}, {"X": 0.4496729075908661, "Y": 0.5831447839736938}, {"X": 0.44978129863739014, "Y": 0.7211287617683411}, {"X": 0.4234354794025421, "Y": 0.7212186455726624}]}, "Id": "9134d3f9-cba3-48a5-9fc3-c6732f8d233c", "Relationships": [{"Type": "CHILD", "Ids": ["84833bd6-6436-452c-84ee-674ecf8fe027"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "Import", "Geometry": {"BoundingBox": {"Width": 0.03702136501669884, "Height": 0.16915477812290192, "Left": 0.46586817502975464, "Top": 0.584791362285614}, "Polygon": [{"X": 0.46586817502975464, "Y": 0.5849433541297913}, {"X": 0.5027567744255066, "Y": 0.584791362285614}, {"X": 0.5028895139694214, "Y": 0.7538266181945801}, {"X": 0.46600091457366943, "Y": 0.7539461255073547}]}, "Id": "19168d93-29cc-467a-bb8d-2f85869d71a7", "Relationships": [{"Type": "CHILD", "Ids": ["cccc2dc7-f7b3-487b-8e3f-dcd063c6cd48"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "23", "Geometry": {"BoundingBox": {"Width": 0.013919953256845474, "Height": 0.13678918778896332, "Left": 0.5207078456878662, "Top": 0.5830709934234619}, "Polygon": [{"X": 0.5207078456878662, "Y": 0.583128035068512}, {"X": 0.5345204472541809, "Y": 0.5830709934234619}, {"X": 0.5346277952194214, "Y": 0.7198129892349243}, {"X": 0.5208151936531067, "Y": 0.7198601961135864}]}, "Id": "6e210787-8702-4ce5-9b53-6c072f0f46c3", "Relationships": [{"Type": "CHILD", "Ids": ["c40089ec-0921-47af-a87c-ec1d556a2930"]}]}, {"BlockType": "LINE", "Confidence": 99.80229949951172, "Text": "40DR", "Geometry": {"BoundingBox": {"Width": 0.03029661811888218, "Height": 0.13834010064601898, "Left": 0.6209090352058411, "Top": 0.5828173160552979}, "Polygon": [{"X": 0.6209090352058411, "Y": 0.5829418897628784}, {"X": 0.6510972380638123, "Y": 0.5828173160552979}, {"X": 0.6512056589126587, "Y": 0.7210544943809509}, {"X": 0.6210175156593323, "Y": 0.7211573719978333}]}, "Id": "be2dca80-78f6-459e-981f-cbb3cff892fb", "Relationships": [{"Type": "CHILD", "Ids": ["34dc571f-b515-4f86-ae3f-f062202b4f27"]}]}, {"BlockType": "LINE", "Confidence": 99.44515228271484, "Text": "MSCLUCI/524A", "Geometry": {"BoundingBox": {"Width": 0.08687042444944382, "Height": 0.14584718644618988, "Left": 0.6657428741455078, "Top": 0.5822030305862427}, "Polygon": [{"X": 0.6657428741455078, "Y": 0.5825612545013428}, {"X": 0.7524991631507874, "Y": 0.5822030305862427}, {"X": 0.7526133060455322, "Y": 0.7277577519416809}, {"X": 0.6658569574356079, "Y": 0.7280502319335938}]}, "Id": "b3477daa-7d84-4f36-ae54-26e5ae8a2a8b", "Relationships": [{"Type": "CHILD", "Ids": ["b6a08a6c-b805-4efb-b362-e09eac3c4eba"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "07/16/2025", "Geometry": {"BoundingBox": {"Width": 0.06680700927972794, "Height": 0.15709932148456573, "Left": 0.78055340051651, "Top": 0.5764428377151489}, "Polygon": [{"X": 0.78055340051651, "Y": 0.5767200589179993}, {"X": 0.8472375273704529, "Y": 0.5764428377151489}, {"X": 0.8473604321479797, "Y": 0.7333194017410278}, {"X": 0.7806763052940369, "Y": 0.7335421442985535}]}, "Id": "793313d6-106f-49df-82c5-42129d9dce73", "Relationships": [{"Type": "CHILD", "Ids": ["f004e125-a19e-4b0d-a1f7-c0ed80c0616e"]}]}, {"BlockType": "LINE", "Confidence": 99.4361801147461, "Text": "9:54 AM", "Geometry": {"BoundingBox": {"Width": 0.04596097394824028, "Height": 0.1415417194366455, "Left": 0.8708734512329102, "Top": 0.5813812613487244}, "Polygon": [{"X": 0.8708734512329102, "Y": 0.5815706253051758}, {"X": 0.9167237281799316, "Y": 0.5813812613487244}, {"X": 0.9168344140052795, "Y": 0.7227673530578613}, {"X": 0.8709841966629028, "Y": 0.7229229807853699}]}, "Id": "60459f01-c203-4074-9e38-a35882cf45af", "Relationships": [{"Type": "CHILD", "Ids": ["210443f7-36b4-4630-b649-1a1317f8a3af", "891edef7-1e38-475c-88dd-51cdd13bc1bc"]}]}, {"BlockType": "LINE", "Confidence": 99.52447509765625, "Text": "10:44 AM", "Geometry": {"BoundingBox": {"Width": 0.051948726177215576, "Height": 0.14107201993465424, "Left": 0.9378957748413086, "Top": 0.5818672180175781}, "Polygon": [{"X": 0.9378957748413086, "Y": 0.5820810794830322}, {"X": 0.9897342324256897, "Y": 0.5818672180175781}, {"X": 0.9898445010185242, "Y": 0.7227632999420166}, {"X": 0.9380060434341431, "Y": 0.7229391932487488}]}, "Id": "3835e0a3-4aff-42e4-89dc-8cddd314f08e", "Relationships": [{"Type": "CHILD", "Ids": ["cf0ad6b1-4138-43d1-9eab-0c5b02bdeeac", "309cc0db-7d3e-4d92-b1ae-74d4cff3b39b"]}]}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "License", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04416491463780403, "Height": 0.14279916882514954, "Left": 0.016434943303465843, "Top": 0.1937331110239029}, "Polygon": [{"X": 0.016434943303465843, "Y": 0.1940050572156906}, {"X": 0.06048756092786789, "Y": 0.1937331110239029}, {"X": 0.060599859803915024, "Y": 0.33629313111305237}, {"X": 0.016547249630093575, "Y": 0.3365322947502136}]}, "Id": "d9b4fa34-6f21-4d99-94e4-dd312c04b73d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04349987208843231, "Height": 0.17575329542160034, "Left": 0.11139988154172897, "Top": 0.1933315396308899}, "Polygon": [{"X": 0.11139988154172897, "Y": 0.1935991793870926}, {"X": 0.15476158261299133, "Y": 0.1933315396308899}, {"X": 0.15489976108074188, "Y": 0.36885693669319153}, {"X": 0.11153806746006012, "Y": 0.36908483505249023}]}, "Id": "e9749857-a2de-4793-8c39-236858307a68"}, {"BlockType": "WORD", "Confidence": 98.84407043457031, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05552111193537712, "Height": 0.14300943911075592, "Left": 0.18934492766857147, "Top": 0.19341948628425598}, "Polygon": [{"X": 0.18934492766857147, "Y": 0.19376130402088165}, {"X": 0.24475377798080444, "Y": 0.19341948628425598}, {"X": 0.2448660433292389, "Y": 0.3361283838748932}, {"X": 0.18945720791816711, "Y": 0.3364289402961731}]}, "Id": "2bc8ac49-66cd-4f11-ab86-c353d89b60a0"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04462762176990509, "Height": 0.1440693885087967, "Left": 0.3003682494163513, "Top": 0.19227971136569977}, "Polygon": [{"X": 0.3003682494163513, "Y": 0.19255444407463074}, {"X": 0.3448827862739563, "Y": 0.19227971136569977}, {"X": 0.3449958562850952, "Y": 0.3361077606678009}, {"X": 0.30048131942749023, "Y": 0.33634909987449646}]}, "Id": "c84c7c0c-0b96-432e-a6b5-08157d9426cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Genset", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040061596781015396, "Height": 0.1402297019958496, "Left": 0.3645818531513214, "Top": 0.19547711312770844}, "Polygon": [{"X": 0.3645818531513214, "Y": 0.19572293758392334}, {"X": 0.40453341603279114, "Y": 0.19547711312770844}, {"X": 0.4046434462070465, "Y": 0.3354901671409607}, {"X": 0.3646918833255768, "Y": 0.33570683002471924}]}, "Id": "c16f1985-1a84-4143-aa9e-bdee0caa12cd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Line", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024044163525104523, "Height": 0.14111773669719696, "Left": 0.4229796826839447, "Top": 0.19348369538784027}, "Polygon": [{"X": 0.4229796826839447, "Y": 0.19363118708133698}, {"X": 0.44691306352615356, "Y": 0.19348369538784027}, {"X": 0.44702383875846863, "Y": 0.33447155356407166}, {"X": 0.4230904281139374, "Y": 0.33460143208503723}]}, "Id": "7778a597-e743-45c3-9dcc-fefc8a8e82fd"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Cat", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019892239943146706, "Height": 0.13987398147583008, "Left": 0.46541470289230347, "Top": 0.1956511288881302}, "Polygon": [{"X": 0.46541470289230347, "Y": 0.19577278196811676}, {"X": 0.48519715666770935, "Y": 0.1956511288881302}, {"X": 0.48530691862106323, "Y": 0.33541786670684814}, {"X": 0.46552446484565735, "Y": 0.3355250954627991}]}, "Id": "24cb57c3-02d0-4d3c-8328-bac966687b70"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04014509916305542, "Height": 0.18059790134429932, "Left": 0.5201172828674316, "Top": 0.18920281529426575}, "Polygon": [{"X": 0.5201172828674316, "Y": 0.18945007026195526}, {"X": 0.5601207613945007, "Y": 0.18920281529426575}, {"X": 0.5602623820304871, "Y": 0.3695910573005676}, {"X": 0.520258903503418, "Y": 0.36980071663856506}]}, "Id": "3f1f7f61-5f42-48ad-b9e9-cefc0dea57f5"}, {"BlockType": "WORD", "Confidence": 98.02076721191406, "Text": "(MT)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026959598064422607, "Height": 0.1813959926366806, "Left": 0.5631676316261292, "Top": 0.18754735589027405}, "Polygon": [{"X": 0.5631676316261292, "Y": 0.18771331012248993}, {"X": 0.5899849534034729, "Y": 0.18754735589027405}, {"X": 0.5901272296905518, "Y": 0.3688026964664459}, {"X": 0.5633098483085632, "Y": 0.36894333362579346}]}, "Id": "218293fd-93a1-42d5-afc9-084e0b2983b7"}, {"BlockType": "WORD", "Confidence": 99.41346740722656, "Text": "Sz/Tp", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032941848039627075, "Height": 0.17241768538951874, "Left": 0.6192891001701355, "Top": 0.19500623643398285}, "Polygon": [{"X": 0.6192891001701355, "Y": 0.19520790874958038}, {"X": 0.6520957946777344, "Y": 0.19500623643398285}, {"X": 0.6522309184074402, "Y": 0.3672516644001007}, {"X": 0.6194242238998413, "Y": 0.3674239218235016}]}, "Id": "40809841-6821-49ad-bdc1-2f69f360c6dc"}, {"BlockType": "WORD", "Confidence": 99.75426483154297, "Text": "Vessel/Voyage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08484594523906708, "Height": 0.18061290681362152, "Left": 0.6647131443023682, "Top": 0.18954519927501678}, "Polygon": [{"X": 0.6647131443023682, "Y": 0.1900680810213089}, {"X": 0.7494178414344788, "Y": 0.18954519927501678}, {"X": 0.7495591044425964, "Y": 0.3697146773338318}, {"X": 0.6648544073104858, "Y": 0.3701581060886383}]}, "Id": "02596ee8-f3dd-49f7-90e8-a6fc2ea58e2e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02608916535973549, "Height": 0.13864627480506897, "Left": 0.7806330323219299, "Top": 0.19681869447231293}, "Polygon": [{"X": 0.7806330323219299, "Y": 0.19697804749011993}, {"X": 0.8066136837005615, "Y": 0.19681869447231293}, {"X": 0.8067222237586975, "Y": 0.33532434701919556}, {"X": 0.7807415723800659, "Y": 0.3354649841785431}]}, "Id": "a6a5a4be-dea8-42fb-ae90-a4873d9ba4d6"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029559750109910965, "Height": 0.1401868760585785, "Left": 0.8704893589019775, "Top": 0.19476905465126038}, "Polygon": [{"X": 0.8704893589019775, "Y": 0.1949499100446701}, {"X": 0.8999394774436951, "Y": 0.19476905465126038}, {"X": 0.9000491499900818, "Y": 0.3347965180873871}, {"X": 0.8705990314483643, "Y": 0.33495593070983887}]}, "Id": "a9524004-2776-4aeb-8f85-cc85520c96a6"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010308120399713516, "Height": 0.13562552630901337, "Left": 0.9027381539344788, "Top": 0.1974014937877655}, "Polygon": [{"X": 0.9027381539344788, "Y": 0.1974640041589737}, {"X": 0.9129401445388794, "Y": 0.1974014937877655}, {"X": 0.9130463004112244, "Y": 0.3329716920852661}, {"X": 0.9028443098068237, "Y": 0.3330270051956177}]}, "Id": "9198c867-227b-4109-a320-33c8574e2b23"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029620448127388954, "Height": 0.14025235176086426, "Left": 0.9367455840110779, "Top": 0.19465191662311554}, "Polygon": [{"X": 0.9367455840110779, "Y": 0.19483309984207153}, {"X": 0.9662563800811768, "Y": 0.19465191662311554}, {"X": 0.9663659930229187, "Y": 0.33474457263946533}, {"X": 0.9368551969528198, "Y": 0.3349042534828186}]}, "Id": "a4e6af6f-f6d4-4cfc-a5f8-a67ceb917f42"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02084493450820446, "Height": 0.13601580262184143, "Left": 0.968488335609436, "Top": 0.19727462530136108}, "Polygon": [{"X": 0.968488335609436, "Y": 0.19740164279937744}, {"X": 0.9892269372940063, "Y": 0.19727462530136108}, {"X": 0.9893332719802856, "Y": 0.3331780433654785}, {"X": 0.9685947299003601, "Y": 0.3332904279232025}]}, "Id": "39f85165-7211-422d-80d4-a807bdf5ec20"}, {"BlockType": "WORD", "Confidence": 99.52069091796875, "Text": "D12713DWA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07053928077220917, "Height": 0.1378844827413559, "Left": 0.01685773953795433, "Top": 0.5828462243080139}, "Polygon": [{"X": 0.01685773953795433, "Y": 0.5831378102302551}, {"X": 0.08728861808776855, "Y": 0.5828462243080139}, {"X": 0.0873970165848732, "Y": 0.7204897999763489}, {"X": 0.016966156661510468, "Y": 0.720730721950531}]}, "Id": "96bfec7b-e2dc-4d8d-a162-8517eaee930d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "FULL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02833232469856739, "Height": 0.14315059781074524, "Left": 0.11230527609586716, "Top": 0.5802904963493347}, "Polygon": [{"X": 0.11230527609586716, "Y": 0.5804076790809631}, {"X": 0.1405249834060669, "Y": 0.5802904963493347}, {"X": 0.14063760638237, "Y": 0.7233449816703796}, {"X": 0.11241790652275085, "Y": 0.7234411239624023}]}, "Id": "c532b3da-e2ea-4899-8330-e93986c3ca1a"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "OUT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024671440944075584, "Height": 0.1419558972120285, "Left": 0.14316479861736298, "Top": 0.5825369358062744}, "Polygon": [{"X": 0.14316479861736298, "Y": 0.5826386213302612}, {"X": 0.1677245795726776, "Y": 0.5825369358062744}, {"X": 0.1678362488746643, "Y": 0.7244093418121338}, {"X": 0.14327648282051086, "Y": 0.7244928479194641}]}, "Id": "d7ff94a3-a818-466a-9b9e-538b11752074"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "MEDU4189050", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08308784663677216, "Height": 0.1369239240884781, "Left": 0.18968966603279114, "Top": 0.5830782055854797}, "Polygon": [{"X": 0.18968966603279114, "Y": 0.5834212899208069}, {"X": 0.27267006039619446, "Y": 0.5830782055854797}, {"X": 0.2727775275707245, "Y": 0.7197181582450867}, {"X": 0.18979716300964355, "Y": 0.7200021147727966}]}, "Id": "92f91128-0bc5-441b-9ec6-c4edd4ac609c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "OWN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02833348698914051, "Height": 0.13703984022140503, "Left": 0.3006397783756256, "Top": 0.5823085308074951}, "Polygon": [{"X": 0.3006397783756256, "Y": 0.5824252963066101}, {"X": 0.3288656175136566, "Y": 0.5823085308074951}, {"X": 0.32897329330444336, "Y": 0.7192517518997192}, {"X": 0.30074745416641235, "Y": 0.7193483710289001}]}, "Id": "26f758ad-a6bd-48d7-b6bc-b9079878dec2"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "MSC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026454215869307518, "Height": 0.13807383179664612, "Left": 0.42332708835601807, "Top": 0.5831447839736938}, "Polygon": [{"X": 0.42332708835601807, "Y": 0.5832536220550537}, {"X": 0.4496729075908661, "Y": 0.5831447839736938}, {"X": 0.44978129863739014, "Y": 0.7211287617683411}, {"X": 0.4234354794025421, "Y": 0.7212186455726624}]}, "Id": "84833bd6-6436-452c-84ee-674ecf8fe027"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Import", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03702136501669884, "Height": 0.16915477812290192, "Left": 0.46586817502975464, "Top": 0.584791362285614}, "Polygon": [{"X": 0.46586817502975464, "Y": 0.5849433541297913}, {"X": 0.5027567744255066, "Y": 0.584791362285614}, {"X": 0.5028895139694214, "Y": 0.7538266181945801}, {"X": 0.46600091457366943, "Y": 0.7539461255073547}]}, "Id": "cccc2dc7-f7b3-487b-8e3f-dcd063c6cd48"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "23", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013919953256845474, "Height": 0.13678918778896332, "Left": 0.5207078456878662, "Top": 0.5830709934234619}, "Polygon": [{"X": 0.5207078456878662, "Y": 0.583128035068512}, {"X": 0.5345204472541809, "Y": 0.5830709934234619}, {"X": 0.5346277952194214, "Y": 0.7198129892349243}, {"X": 0.5208151936531067, "Y": 0.7198601961135864}]}, "Id": "c40089ec-0921-47af-a87c-ec1d556a2930"}, {"BlockType": "WORD", "Confidence": 99.80229949951172, "Text": "40DR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03029661811888218, "Height": 0.13834010064601898, "Left": 0.6209090352058411, "Top": 0.5828173160552979}, "Polygon": [{"X": 0.6209090352058411, "Y": 0.5829418897628784}, {"X": 0.6510972380638123, "Y": 0.5828173160552979}, {"X": 0.6512056589126587, "Y": 0.7210544943809509}, {"X": 0.6210175156593323, "Y": 0.7211573719978333}]}, "Id": "34dc571f-b515-4f86-ae3f-f062202b4f27"}, {"BlockType": "WORD", "Confidence": 99.44515228271484, "Text": "MSCLUCI/524A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08687042444944382, "Height": 0.14584718644618988, "Left": 0.6657428741455078, "Top": 0.5822030305862427}, "Polygon": [{"X": 0.6657428741455078, "Y": 0.5825612545013428}, {"X": 0.7524991631507874, "Y": 0.5822030305862427}, {"X": 0.7526133060455322, "Y": 0.7277577519416809}, {"X": 0.6658569574356079, "Y": 0.7280502319335938}]}, "Id": "b6a08a6c-b805-4efb-b362-e09eac3c4eba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "07/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06680700927972794, "Height": 0.15709932148456573, "Left": 0.78055340051651, "Top": 0.5764428377151489}, "Polygon": [{"X": 0.78055340051651, "Y": 0.5767200589179993}, {"X": 0.8472375273704529, "Y": 0.5764428377151489}, {"X": 0.8473604321479797, "Y": 0.7333194017410278}, {"X": 0.7806763052940369, "Y": 0.7335421442985535}]}, "Id": "f004e125-a19e-4b0d-a1f7-c0ed80c0616e"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "9:54", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024261552840471268, "Height": 0.1414521038532257, "Left": 0.8708734512329102, "Top": 0.5814708471298218}, "Polygon": [{"X": 0.8708734512329102, "Y": 0.5815706253051758}, {"X": 0.895024299621582, "Y": 0.5814708471298218}, {"X": 0.8951349854469299, "Y": 0.7228410243988037}, {"X": 0.8709841966629028, "Y": 0.7229229807853699}]}, "Id": "210443f7-36b4-4630-b649-1a1317f8a3af"}, {"BlockType": "WORD", "Confidence": 99.07744598388672, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01894879713654518, "Height": 0.14010857045650482, "Left": 0.8978847861289978, "Top": 0.5816695094108582}, "Polygon": [{"X": 0.8978847861289978, "Y": 0.58174729347229}, {"X": 0.9167239665985107, "Y": 0.5816695094108582}, {"X": 0.9168335795402527, "Y": 0.7217140197753906}, {"X": 0.8979944586753845, "Y": 0.7217780947685242}]}, "Id": "891edef7-1e38-475c-88dd-51cdd13bc1bc"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "10:44", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03059534728527069, "Height": 0.13989846408367157, "Left": 0.9378963708877563, "Top": 0.582656979560852}, "Polygon": [{"X": 0.9378963708877563, "Y": 0.5827826261520386}, {"X": 0.9683822393417358, "Y": 0.582656979560852}, {"X": 0.9684916734695435, "Y": 0.72245192527771}, {"X": 0.9380057454109192, "Y": 0.7225554585456848}]}, "Id": "cf0ad6b1-4138-43d1-9eab-0c5b02bdeeac"}, {"BlockType": "WORD", "Confidence": 99.1368408203125, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019154971465468407, "Height": 0.14096073806285858, "Left": 0.9706895351409912, "Top": 0.5818672180175781}, "Polygon": [{"X": 0.9706895351409912, "Y": 0.5819457769393921}, {"X": 0.9897342324256897, "Y": 0.5818672180175781}, {"X": 0.9898445010185242, "Y": 0.7227632999420166}, {"X": 0.9707998037338257, "Y": 0.7228279113769531}]}, "Id": "309cc0db-7d3e-4d92-b1ae-74d4cff3b39b"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c9e17baf-8d97-4375-b628-4a08802c4e05", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c9e17baf-8d97-4375-b628-4a08802c4e05", "content-type": "application/x-amz-json-1.1", "content-length": "29691", "date": "Wed, 24 Sep 2025 13:32:23 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ea0ec404-43b5-42fd-9f55-b429de3e67fb", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:26 GMT", "content-type": "application/json", "content-length": "1048", "connection": "keep-alive", "x-amzn-requestid": "ea0ec404-43b5-42fd-9f55-b429de3e67fb"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The text: \"License Activity Container Chassis Genset Line Cat Weight (MT) Sz/Tp Vessel/Voyage Date Time In Time Out D12713DWA FULL OUT MEDU4189050 OWN MSC Import 23 40DR MSCLUCI/524A 07/16/2025 9:54 AM 10:44 AM\". Looks like an outgate or ingate? It lists \"FULL OUT\" and \"Time In\" and \"Time Out\". This looks like an \"outgate\" record: record of vehicle/container exiting a facility. It has \"FULL OUT\" maybe indicating full out. Contains Time In and Time Out. So doc_type: outgate.\n\nBut check \"ingate\" is vehicle/container entering facility. Outgate is exiting. So this is outgate. So page 1: outgate. No continuation. Provide JSON with documents array containing page_no 1 and doc_type outgate."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"outgate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2681, "outputTokens": 216, "totalTokens": 2897}, "metrics": {"latencyMs": 1067}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/4408cbb9_T5U48WX7H3FNSZPBDBLZ.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nLicense\nActivity\nContainer\nChassis\nGenset\nLine\nCat\nWeight (MT)\nSz/Tp\nVessel/Voyage\nDate\nTime In\nTime Out\nD12713DWA\nFULL OUT\nMEDU4189050\nOWN\nMSC\nImport\n23\n40DR\nMSCLUCI/524A\n07/16/2025\n9:54 AM\n10:44 AM\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}