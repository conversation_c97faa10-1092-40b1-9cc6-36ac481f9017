{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.4089048504829407, "Height": 0.6872744560241699, "Left": 0.2366572618484497, "Top": 0.1673707216978073}, "Polygon": [{"X": 0.24769896268844604, "Y": 0.1673707216978073}, {"X": 0.6410211324691772, "Y": 0.1756865531206131}, {"X": 0.6455621123313904, "Y": 0.8521214127540588}, {"X": 0.2366572618484497, "Y": 0.8546451926231384}]}, "Id": "a9bc742a-74ae-42bb-b7b6-b9dd0dca385d", "Relationships": [{"Type": "CHILD", "Ids": ["98c23213-1c08-4959-a46e-4fb4cccde645", "54259030-d063-403e-96d9-bae51aeb6a7f", "321b9bd9-081f-4d2d-8279-9a15b813f005", "01d939e5-978c-45d2-bee2-adadbc0f9256", "cc000fb6-8c4d-4fc3-8ae0-26f0ae277231", "cd35e662-f2af-4e48-b2df-80e0157f7edc", "6d02de46-000e-4b96-b711-953a0ff6721b", "1efeb6d5-6714-4ba9-a77c-e90bcd85d888", "cb1aab9c-9dac-4cee-93ae-6bf6fcf36eda", "3521a08e-77e8-427d-92d4-76e3bd7729ad", "7f05f039-f3cc-4c40-a81d-10566b140bed", "bed1a629-e213-4d88-bb1d-153f8c03a3b3", "2a784a3b-b605-4487-a7fe-7aab8b6a8f53", "ef2f32c4-87a2-4938-962b-fad229240ac7", "d26edc69-c71a-4bfd-8d2d-bab92f1ebe2f", "fc006bfe-3d29-4cd5-9041-be12a142f211", "2c4bd776-bba6-4e3c-9399-210e9920d318", "4a41396a-9f24-41c6-8e47-2ab232e1f190", "1a2e7f27-b7b1-4d7c-a25f-cc0bd2b34c9d", "10a65cd5-677e-4221-a47b-3d29f5b72dc6", "714dd4b2-aed7-411c-afbf-e3165e7ec92d", "554c1703-284f-4c0f-813b-2b2128d345ef", "9d37cc12-22ef-42f6-81d2-18e95ff6876f", "e87e3ed2-d104-4a3b-997e-53239f6f69cd", "ae94714b-4a31-425f-a559-0600d1aca68b", "12920be4-100e-4e89-bb71-3791918fc395", "51029cf7-9d7f-4283-88db-d12921c54b09", "83a6c3c7-8460-427b-a024-d1e8f8eec7e7", "a3c6a2bd-894a-4d70-bf10-4291689abd1e", "f2d413f3-6c72-46ef-a71a-ebf6c6a659fd", "34fee5a9-e8fe-4cb1-8a0d-9ca81753442b", "a8b25100-d918-439c-977a-d5346f39a20b", "d4bc154d-ce1d-4f7c-840d-56fa42f22a7c", "134c07ca-c4b1-48d9-8390-7604e0776bec", "2bd9b217-164f-4d46-b1a5-33de9b985d80", "20191b61-0444-412a-a953-894bf6502b31", "d2f99188-219b-4cab-8417-b2ca1bfd1052", "a9a7c564-4f1c-4a0e-ac33-2bc5c2898795", "ef92b768-b9a8-4b78-bc77-46a1577b4980", "1ad48f5e-d69a-4197-bb87-aa0bdaa5ffdf", "34a741dd-e653-49b9-bcb8-e64952347d57", "df19897b-21d3-4b95-9ddb-a0862f553920", "06cfe69e-c7e9-4c5c-8a4f-ccad40cbdd0b", "4f4a2919-9137-4cb8-80f2-91ef0eee73d0", "fe31ef3a-e54f-4262-89ae-ad2daaf82e64", "3d6cf55b-8d26-4ae4-9ed8-ecfacea8e36c", "07a20e65-af05-46cc-a395-33d3efa2aca3", "ec97588c-c99e-45bc-a155-45d3e8d3a02c", "ade6f25e-34ec-495d-8ac5-5e7135a5c73d"]}]}, {"BlockType": "LINE", "Confidence": 58.34821319580078, "Text": "Loves.", "Geometry": {"BoundingBox": {"Width": 0.17179198563098907, "Height": 0.04243770241737366, "Left": 0.3969765603542328, "Top": 0.2628079354763031}, "Polygon": [{"X": 0.3972688913345337, "Y": 0.2628079354763031}, {"X": 0.5686694979667664, "Y": 0.2657993733882904}, {"X": 0.5687685608863831, "Y": 0.30524563789367676}, {"X": 0.3969765603542328, "Y": 0.30252018570899963}]}, "Id": "98c23213-1c08-4959-a46e-4fb4cccde645", "Relationships": [{"Type": "CHILD", "Ids": ["623c0ee6-3eb3-40d1-9a8a-e9fc4a559286"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Loves #395", "Geometry": {"BoundingBox": {"Width": 0.05996580049395561, "Height": 0.013404522091150284, "Left": 0.4118615984916687, "Top": 0.3157282769680023}, "Polygon": [{"X": 0.41194289922714233, "Y": 0.3157282769680023}, {"X": 0.4718273878097534, "Y": 0.3166472315788269}, {"X": 0.4717892110347748, "Y": 0.3291327953338623}, {"X": 0.4118615984916687, "Y": 0.3282431364059448}]}, "Id": "54259030-d063-403e-96d9-bae51aeb6a7f", "Relationships": [{"Type": "CHILD", "Ids": ["82223a1a-8a43-4567-a673-71d90b1846e3", "723a17a0-df51-4a5b-85fd-ff73b9603524"]}]}, {"BlockType": "LINE", "Confidence": 99.98616790771484, "Text": "3407 South State Road 45/52", "Geometry": {"BoundingBox": {"Width": 0.16068698465824127, "Height": 0.018494993448257446, "Left": 0.3597559928894043, "Top": 0.3264930844306946}, "Polygon": [{"X": 0.35990962386131287, "Y": 0.3264930844306946}, {"X": 0.5204429626464844, "Y": 0.32888251543045044}, {"X": 0.5204387307167053, "Y": 0.344988077878952}, {"X": 0.3597559928894043, "Y": 0.34270042181015015}]}, "Id": "321b9bd9-081f-4d2d-8279-9a15b813f005", "Relationships": [{"Type": "CHILD", "Ids": ["d6eeef42-dda3-4854-b63d-e06e8caed1ef", "5cc95a69-7421-4db8-81c9-8ecdf5688213", "f2e50bf3-e611-4cb8-b9f0-7b495abcc5b7", "c8a0ea81-4d3d-4ec2-80d5-dbbb72d93b7f", "d3d6aa7f-eb1a-4f4f-919a-b27a519b8bc4"]}]}, {"BlockType": "LINE", "Confidence": 99.72151184082031, "Text": "Kankakee, IL 60901", "Geometry": {"BoundingBox": {"Width": 0.1074315682053566, "Height": 0.015731321647763252, "Left": 0.3894091844558716, "Top": 0.34119418263435364}, "Polygon": [{"X": 0.38951998949050903, "Y": 0.34119418263435364}, {"X": 0.49684077501296997, "Y": 0.34273040294647217}, {"X": 0.49681779742240906, "Y": 0.35692548751831055}, {"X": 0.3894091844558716, "Y": 0.35544922947883606}]}, "Id": "01d939e5-978c-45d2-bee2-adadbc0f9256", "Relationships": [{"Type": "CHILD", "Ids": ["d0e84867-5959-485e-97af-7dacd2242e7c", "ff6d337c-3ab7-480f-a4f7-32ff7a11783f", "4400bb83-e020-479e-b237-9cf719e50326"]}]}, {"BlockType": "LINE", "Confidence": 90.05520629882812, "Text": "(815) 929-1549", "Geometry": {"BoundingBox": {"Width": 0.08309073001146317, "Height": 0.014291251078248024, "Left": 0.4024497866630554, "Top": 0.354943186044693}, "Polygon": [{"X": 0.4025424122810364, "Y": 0.354943186044693}, {"X": 0.485540509223938, "Y": 0.35608622431755066}, {"X": 0.4855107367038727, "Y": 0.36923444271087646}, {"X": 0.4024497866630554, "Y": 0.36813437938690186}]}, "Id": "cc000fb6-8c4d-4fc3-8ae0-26f0ae277231", "Relationships": [{"Type": "CHILD", "Ids": ["0c841b32-b95a-48ff-878b-c85b5037abe4"]}]}, {"BlockType": "LINE", "Confidence": 99.9568862915039, "Text": "07/16/2025 Tkt #99479847", "Geometry": {"BoundingBox": {"Width": 0.1434461623430252, "Height": 0.014299322851002216, "Left": 0.30017659068107605, "Top": 0.3807969391345978}, "Polygon": [{"X": 0.3003380000591278, "Y": 0.3807969391345978}, {"X": 0.44362276792526245, "Y": 0.3826146423816681}, {"X": 0.44356459379196167, "Y": 0.3950962722301483}, {"X": 0.30017659068107605, "Y": 0.39334893226623535}]}, "Id": "cd35e662-f2af-4e48-b2df-80e0157f7edc", "Relationships": [{"Type": "CHILD", "Ids": ["be693c43-3509-4ef1-be2a-c38f978f0973", "645b550c-06bb-4cf7-8f37-459daa8c92b0", "e3fbb354-605a-4af2-a6dc-fc33c760e7f5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Type: SALE", "Geometry": {"BoundingBox": {"Width": 0.060390591621398926, "Height": 0.010728958062827587, "Left": 0.2979479134082794, "Top": 0.4064086079597473}, "Polygon": [{"X": 0.2980782389640808, "Y": 0.4064086079597473}, {"X": 0.35833850502967834, "Y": 0.4071115255355835}, {"X": 0.3582429885864258, "Y": 0.4171375632286072}, {"X": 0.2979479134082794, "Y": 0.4164583683013916}]}, "Id": "6d02de46-000e-4b96-b711-953a0ff6721b", "Relationships": [{"Type": "CHILD", "Ids": ["2d19df84-6003-40f1-bb80-c64690c13c2a", "8a7a8659-f6f3-4171-bf8f-c62b6e0b2595"]}]}, {"BlockType": "LINE", "Confidence": 99.09378814697266, "Text": "(ORIGINAL)", "Geometry": {"BoundingBox": {"Width": 0.05835532397031784, "Height": 0.010424274951219559, "Left": 0.40156951546669006, "Top": 0.40539637207984924}, "Polygon": [{"X": 0.40163835883140564, "Y": 0.40539637207984924}, {"X": 0.4599248468875885, "Y": 0.4060814678668976}, {"X": 0.4598885774612427, "Y": 0.4158206582069397}, {"X": 0.40156951546669006, "Y": 0.4151579737663269}]}, "Id": "1efeb6d5-6714-4ba9-a77c-e90bcd85d888", "Relationships": [{"Type": "CHILD", "Ids": ["15321c38-2089-4353-954b-e0a466ae14be"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Qty Name", "Geometry": {"BoundingBox": {"Width": 0.04945627599954605, "Height": 0.010598265565931797, "Left": 0.29361701011657715, "Top": 0.427952378988266}, "Polygon": [{"X": 0.2937501072883606, "Y": 0.427952378988266}, {"X": 0.3430732786655426, "Y": 0.42848530411720276}, {"X": 0.34296876192092896, "Y": 0.4385506212711334}, {"X": 0.29361701011657715, "Y": 0.4380372166633606}]}, "Id": "cb1aab9c-9dac-4cee-93ae-6bf6fcf36eda", "Relationships": [{"Type": "CHILD", "Ids": ["89300fdd-9510-4805-9e97-7b7d3c8841d3", "6ce0ece9-a042-454b-a73d-35875d72bc40"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.030655253678560257, "Height": 0.008415746502578259, "Left": 0.4533301293849945, "Top": 0.4277014136314392}, "Polygon": [{"X": 0.45336323976516724, "Y": 0.4277014136314392}, {"X": 0.48398539423942566, "Y": 0.4280347228050232}, {"X": 0.4839664399623871, "Y": 0.43611717224121094}, {"X": 0.4533301293849945, "Y": 0.4357936680316925}]}, "Id": "3521a08e-77e8-427d-92d4-76e3bd7729ad", "Relationships": [{"Type": "CHILD", "Ids": ["545cb36f-46cd-4b01-af0c-b137b0d74b28"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.029140856117010117, "Height": 0.008396722376346588, "Left": 0.5089405179023743, "Top": 0.4273689091205597}, "Polygon": [{"X": 0.5089479684829712, "Y": 0.4273689091205597}, {"X": 0.5380753874778748, "Y": 0.42768704891204834}, {"X": 0.5380813479423523, "Y": 0.43576565384864807}, {"X": 0.5089405179023743, "Y": 0.4354568421840668}]}, "Id": "7f05f039-f3cc-4c40-a81d-10566b140bed", "Relationships": [{"Type": "CHILD", "Ids": ["67d445bb-9dff-477a-aae9-c5741f48361a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DIESEL", "Geometry": {"BoundingBox": {"Width": 0.03675799444317818, "Height": 0.01052020862698555, "Left": 0.3174756169319153, "Top": 0.45237067341804504}, "Polygon": [{"X": 0.317595899105072, "Y": 0.45237067341804504}, {"X": 0.35423362255096436, "Y": 0.45273125171661377}, {"X": 0.3541347086429596, "Y": 0.4628908932209015}, {"X": 0.3174756169319153, "Y": 0.4625449478626251}]}, "Id": "bed1a629-e213-4d88-bb1d-153f8c03a3b3", "Relationships": [{"Type": "CHILD", "Ids": ["79397dab-c047-4b99-bcb4-1cd2ec21d38a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "520.61", "Geometry": {"BoundingBox": {"Width": 0.03698154538869858, "Height": 0.0105452174320817, "Left": 0.5025667548179626, "Top": 0.45125240087509155}, "Polygon": [{"X": 0.5025798082351685, "Y": 0.45125240087509155}, {"X": 0.5395399332046509, "Y": 0.45162051916122437}, {"X": 0.5395482778549194, "Y": 0.4617976248264313}, {"X": 0.5025667548179626, "Y": 0.4614444375038147}]}, "Id": "2a784a3b-b605-4487-a7fe-7aab8b6a8f53", "Relationships": [{"Type": "CHILD", "Ids": ["441b6789-fb60-41f2-bb8a-aade6e8b202c"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "Pump:", "Geometry": {"BoundingBox": {"Width": 0.02928336337208748, "Height": 0.01144578866660595, "Left": 0.3233851194381714, "Top": 0.46576520800590515}, "Polygon": [{"X": 0.3235135078430176, "Y": 0.46576520800590515}, {"X": 0.35266849398612976, "Y": 0.4660366475582123}, {"X": 0.35255879163742065, "Y": 0.47721099853515625}, {"X": 0.3233851194381714, "Y": 0.4769524037837982}]}, "Id": "ef2f32c4-87a2-4938-962b-fad229240ac7", "Relationships": [{"Type": "CHILD", "Ids": ["9af64cab-a2a1-4945-8904-af735a75a5f1"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "18", "Geometry": {"BoundingBox": {"Width": 0.011463267728686333, "Height": 0.00925323087722063, "Left": 0.45409098267555237, "Top": 0.4653787612915039}, "Polygon": [{"X": 0.4541279673576355, "Y": 0.4653787612915039}, {"X": 0.46555426716804504, "Y": 0.46548590064048767}, {"X": 0.46552327275276184, "Y": 0.47463199496269226}, {"X": 0.45409098267555237, "Y": 0.4745290279388428}]}, "Id": "d26edc69-c71a-4bfd-8d2d-bab92f1ebe2f", "Relationships": [{"Type": "CHILD", "Ids": ["f781c69b-4425-4609-8a21-a4660b3a4828"]}]}, {"BlockType": "LINE", "Confidence": 99.68590545654297, "Text": "Gallons:", "Geometry": {"BoundingBox": {"Width": 0.047946587204933167, "Height": 0.010707670822739601, "Left": 0.32322049140930176, "Top": 0.47857698798179626}, "Polygon": [{"X": 0.3233388066291809, "Y": 0.47857698798179626}, {"X": 0.3711670935153961, "Y": 0.4789978563785553}, {"X": 0.3710770010948181, "Y": 0.4892846643924713}, {"X": 0.32322049140930176, "Y": 0.48888322710990906}]}, "Id": "fc006bfe-3d29-4cd5-9041-be12a142f211", "Relationships": [{"Type": "CHILD", "Ids": ["8f5043f1-9775-4bcc-8007-501b2736c05f"]}]}, {"BlockType": "LINE", "Confidence": 99.72098541259766, "Text": "133.868", "Geometry": {"BoundingBox": {"Width": 0.042919617146253586, "Height": 0.010256355628371239, "Left": 0.44755178689956665, "Top": 0.47794750332832336}, "Polygon": [{"X": 0.44759538769721985, "Y": 0.47794750332832336}, {"X": 0.49047139286994934, "Y": 0.47832775115966797}, {"X": 0.49045196175575256, "Y": 0.48820385336875916}, {"X": 0.44755178689956665, "Y": 0.4878404140472412}]}, "Id": "2c4bd776-bba6-4e3c-9399-210e9920d318", "Relationships": [{"Type": "CHILD", "Ids": ["525b6b61-1c3b-4290-b158-1f1f82feb42a"]}]}, {"BlockType": "LINE", "Confidence": 97.55194854736328, "Text": "Price / Gal:", "Geometry": {"BoundingBox": {"Width": 0.07215077430009842, "Height": 0.011469832621514797, "Left": 0.3236068785190582, "Top": 0.49122658371925354}, "Polygon": [{"X": 0.3237316906452179, "Y": 0.49122658371925354}, {"X": 0.39575764536857605, "Y": 0.49182406067848206}, {"X": 0.39567771553993225, "Y": 0.5026963949203491}, {"X": 0.3236068785190582, "Y": 0.5021299123764038}]}, "Id": "4a41396a-9f24-41c6-8e47-2ab232e1f190", "Relationships": [{"Type": "CHILD", "Ids": ["48197183-723d-4b21-9078-1b03f46b3438", "e75ea434-5700-427f-9ee7-a9bd96da2fa3", "9fb274d3-ba89-4d1a-a700-f3c19e627a0d"]}]}, {"BlockType": "LINE", "Confidence": 99.81205749511719, "Text": "3.889", "Geometry": {"BoundingBox": {"Width": 0.031188972294330597, "Height": 0.01007451955229044, "Left": 0.459010511636734, "Top": 0.4913272261619568}, "Polygon": [{"X": 0.45904740691185, "Y": 0.4913272261619568}, {"X": 0.490199476480484, "Y": 0.49158692359924316}, {"X": 0.49018001556396484, "Y": 0.5014017224311829}, {"X": 0.459010511636734, "Y": 0.5011541843414307}]}, "Id": "1a2e7f27-b7b1-4d7c-a25f-cc0bd2b34c9d", "Relationships": [{"Type": "CHILD", "Ids": ["4ef99271-6f3a-4182-b31e-56d5a699fcdc"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Subtotal", "Geometry": {"BoundingBox": {"Width": 0.04685430973768234, "Height": 0.010452276095747948, "Left": 0.29362016916275024, "Top": 0.5180655121803284}, "Polygon": [{"X": 0.2937532365322113, "Y": 0.5180655121803284}, {"X": 0.3404744863510132, "Y": 0.5184026956558228}, {"X": 0.34036847949028015, "Y": 0.5285177826881409}, {"X": 0.29362016916275024, "Y": 0.528199315071106}]}, "Id": "10a65cd5-677e-4221-a47b-3d29f5b72dc6", "Relationships": [{"Type": "CHILD", "Ids": ["f22aebba-2cd0-4cac-b0a4-96921b0801d6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "520.61", "Geometry": {"BoundingBox": {"Width": 0.03754885867238045, "Height": 0.010437089018523693, "Left": 0.5023636221885681, "Top": 0.5174089670181274}, "Polygon": [{"X": 0.5023767352104187, "Y": 0.5174089670181274}, {"X": 0.5399039387702942, "Y": 0.5176830291748047}, {"X": 0.5399124622344971, "Y": 0.5278460383415222}, {"X": 0.5023636221885681, "Y": 0.5275871753692627}]}, "Id": "714dd4b2-aed7-411c-afbf-e3165e7ec92d", "Relationships": [{"Type": "CHILD", "Ids": ["bb4f185e-9c63-4e30-9779-c317e1d85313"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Sales Tax", "Geometry": {"BoundingBox": {"Width": 0.054718442261219025, "Height": 0.010514152236282825, "Left": 0.29339075088500977, "Top": 0.5312128067016602}, "Polygon": [{"X": 0.2935243546962738, "Y": 0.5312128067016602}, {"X": 0.3481091856956482, "Y": 0.5315781235694885}, {"X": 0.34800732135772705, "Y": 0.5417269468307495}, {"X": 0.29339075088500977, "Y": 0.5413835048675537}]}, "Id": "554c1703-284f-4c0f-813b-2b2128d345ef", "Relationships": [{"Type": "CHILD", "Ids": ["f96efe65-2e8e-491f-9ad4-7695189d8fa7", "33a6f884-b82f-4c6c-88b7-45eb73dfd6f4"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.02538549341261387, "Height": 0.010209642350673676, "Left": 0.5150367617607117, "Top": 0.5307148098945618}, "Polygon": [{"X": 0.5150424242019653, "Y": 0.5307148098945618}, {"X": 0.540413498878479, "Y": 0.5308866500854492}, {"X": 0.540422260761261, "Y": 0.5409244298934937}, {"X": 0.5150367617607117, "Y": 0.5407627820968628}]}, "Id": "9d37cc12-22ef-42f6-81d2-18e95ff6876f", "Relationships": [{"Type": "CHILD", "Ids": ["c24c5f2f-d4d4-44f0-9c4f-2faef2cabeef"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.028774499893188477, "Height": 0.010384700261056423, "Left": 0.29303935170173645, "Top": 0.5446302890777588}, "Polygon": [{"X": 0.2931737005710602, "Y": 0.5446302890777588}, {"X": 0.3218138515949249, "Y": 0.5448066592216492}, {"X": 0.3216962516307831, "Y": 0.555014967918396}, {"X": 0.29303935170173645, "Y": 0.554850161075592}]}, "Id": "e87e3ed2-d104-4a3b-997e-53239f6f69cd", "Relationships": [{"Type": "CHILD", "Ids": ["311239df-0cd8-45f5-ae9b-b06119c3330f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "520.61", "Geometry": {"BoundingBox": {"Width": 0.037438396364450455, "Height": 0.010554016567766666, "Left": 0.502533495426178, "Top": 0.5440988540649414}, "Polygon": [{"X": 0.5025467276573181, "Y": 0.5440988540649414}, {"X": 0.5399631857872009, "Y": 0.5443320274353027}, {"X": 0.5399718880653381, "Y": 0.5546528697013855}, {"X": 0.502533495426178, "Y": 0.554435133934021}]}, "Id": "ae94714b-4a31-425f-a559-0600d1aca68b", "Relationships": [{"Type": "CHILD", "Ids": ["f7ea285e-2975-4aad-b6ea-5a96137336cb"]}]}, {"BlockType": "LINE", "Confidence": 99.72576141357422, "Text": "Received:", "Geometry": {"BoundingBox": {"Width": 0.05402747914195061, "Height": 0.010773943737149239, "Left": 0.29147446155548096, "Top": 0.5712147355079651}, "Polygon": [{"X": 0.29161348938941956, "Y": 0.5712147355079651}, {"X": 0.34550195932388306, "Y": 0.5714895129203796}, {"X": 0.3453952372074127, "Y": 0.5819886326789856}, {"X": 0.29147446155548096, "Y": 0.5817362666130066}]}, "Id": "12920be4-100e-4e89-bb71-3791918fc395", "Relationships": [{"Type": "CHILD", "Ids": ["edb40e03-361c-4973-aeec-a28a2e2d9bbc"]}]}, {"BlockType": "LINE", "Confidence": 99.86740112304688, "Text": "Fleet One OTR", "Geometry": {"BoundingBox": {"Width": 0.08083158731460571, "Height": 0.011000357568264008, "Left": 0.30335694551467896, "Top": 0.5845891237258911}, "Polygon": [{"X": 0.30349060893058777, "Y": 0.5845891237258911}, {"X": 0.38418853282928467, "Y": 0.5849578380584717}, {"X": 0.38410383462905884, "Y": 0.5955894589424133}, {"X": 0.30335694551467896, "Y": 0.5952548384666443}]}, "Id": "51029cf7-9d7f-4283-88db-d12921c54b09", "Relationships": [{"Type": "CHILD", "Ids": ["7f4b41e7-252f-4b17-b5a1-674803e70591", "6b50c074-1684-47c8-baac-b70ee8f3cecb", "2a02040b-e233-427a-866a-0ff6a6e9953f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "520.61", "Geometry": {"BoundingBox": {"Width": 0.03773930296301842, "Height": 0.01058113481849432, "Left": 0.5024480223655701, "Top": 0.5846767425537109}, "Polygon": [{"X": 0.5024613738059998, "Y": 0.5846767425537109}, {"X": 0.5401784181594849, "Y": 0.5848503708839417}, {"X": 0.5401873588562012, "Y": 0.5952578783035278}, {"X": 0.5024480223655701, "Y": 0.5950999855995178}]}, "Id": "83a6c3c7-8460-427b-a024-d1e8f8eec7e7", "Relationships": [{"Type": "CHILD", "Ids": ["c3a83d46-f75c-400e-b014-f8a92d53633f"]}]}, {"BlockType": "LINE", "Confidence": 99.5703125, "Text": "0149 SWIPED", "Geometry": {"BoundingBox": {"Width": 0.07538707554340363, "Height": 0.010828590020537376, "Left": 0.39594316482543945, "Top": 0.5982499122619629}, "Polygon": [{"X": 0.39602017402648926, "Y": 0.5982499122619629}, {"X": 0.4713302552700043, "Y": 0.5985541939735413}, {"X": 0.4712982773780823, "Y": 0.6090785264968872}, {"X": 0.39594316482543945, "Y": 0.6088058352470398}]}, "Id": "a3c6a2bd-894a-4d70-bf10-4291689abd1e", "Relationships": [{"Type": "CHILD", "Ids": ["70be3fc6-c135-4db7-b1c5-9808e04608cc", "68617877-3235-4c4f-bc57-e2e3bc041d83"]}]}, {"BlockType": "LINE", "Confidence": 99.92732238769531, "Text": "Auth No: 114525", "Geometry": {"BoundingBox": {"Width": 0.09381067752838135, "Height": 0.01067864615470171, "Left": 0.3145129084587097, "Top": 0.6118013262748718}, "Polygon": [{"X": 0.31463634967803955, "Y": 0.6118013262748718}, {"X": 0.40832358598709106, "Y": 0.612127959728241}, {"X": 0.40825533866882324, "Y": 0.6224799752235413}, {"X": 0.3145129084587097, "Y": 0.6221919059753418}]}, "Id": "f2d413f3-6c72-46ef-a71a-ebf6c6a659fd", "Relationships": [{"Type": "CHILD", "Ids": ["4bcbf5ec-bca5-4d81-a230-15813eb71696", "1cebf853-888c-4212-ac5c-787a083a062d", "2b33305a-44ba-49a3-bc94-f2bfc8af90e9"]}]}, {"BlockType": "LINE", "Confidence": 99.94685363769531, "Text": "Company: AMERILUX TRANSPORTATION", "Geometry": {"BoundingBox": {"Width": 0.20186378061771393, "Height": 0.012542315758764744, "Left": 0.3010958433151245, "Top": 0.6252632737159729}, "Polygon": [{"X": 0.3012479543685913, "Y": 0.6252632737159729}, {"X": 0.5029596090316772, "Y": 0.6258580684661865}, {"X": 0.5029447078704834, "Y": 0.6378055810928345}, {"X": 0.3010958433151245, "Y": 0.6373071670532227}]}, "Id": "34fee5a9-e8fe-4cb1-8a0d-9ca81753442b", "Relationships": [{"Type": "CHILD", "Ids": ["f462a761-55d0-477d-8e67-14d8580d9a11", "a191fce9-abeb-4506-b907-5661fc33181c", "04a0cbfa-a957-4bf6-8c18-5ecb54786859"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "INVOICE# 07717", "Geometry": {"BoundingBox": {"Width": 0.08769335597753525, "Height": 0.010455039329826832, "Left": 0.3016186058521271, "Top": 0.6390161514282227}, "Polygon": [{"X": 0.30174803733825684, "Y": 0.6390161514282227}, {"X": 0.3893119692802429, "Y": 0.6392263770103455}, {"X": 0.3892335593700409, "Y": 0.6494711637496948}, {"X": 0.3016186058521271, "Y": 0.6492966413497925}]}, "Id": "a8b25100-d918-439c-977a-d5346f39a20b", "Relationships": [{"Type": "CHILD", "Ids": ["2dc18442-9b06-4cbf-bf22-0c593ab06d4f", "5d5ee261-7725-4d3a-84aa-88f4f7527cd0"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "TripNumber", "Geometry": {"BoundingBox": {"Width": 0.06352675706148148, "Height": 0.01237125601619482, "Left": 0.2863508462905884, "Top": 0.6809547543525696}, "Polygon": [{"X": 0.28651663661003113, "Y": 0.6809547543525696}, {"X": 0.34987759590148926, "Y": 0.6810010075569153}, {"X": 0.3497561514377594, "Y": 0.6933260560035706}, {"X": 0.2863508462905884, "Y": 0.6933109164237976}]}, "Id": "d4bc154d-ce1d-4f7c-840d-56fa42f22a7c", "Relationships": [{"Type": "CHILD", "Ids": ["2366c26c-f146-4bc6-99f9-f58f6effcc60"]}]}, {"BlockType": "LINE", "Confidence": 99.2735595703125, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.006150976754724979, "Height": 0.009307133965194225, "Left": 0.5349729061126709, "Top": 0.680779755115509}, "Polygon": [{"X": 0.5349729061126709, "Y": 0.680779755115509}, {"X": 0.5411154627799988, "Y": 0.6807843446731567}, {"X": 0.5411238670349121, "Y": 0.6900869011878967}, {"X": 0.5349780917167664, "Y": 0.6900846362113953}]}, "Id": "134c07ca-c4b1-48d9-8390-7604e0776bec", "Relationships": [{"Type": "CHILD", "Ids": ["b706fef0-9c43-4085-8ae9-34ad6ed70353"]}]}, {"BlockType": "LINE", "Confidence": 99.21077728271484, "Text": "VehicleID", "Geometry": {"BoundingBox": {"Width": 0.05816200003027916, "Height": 0.010674909688532352, "Left": 0.2855813801288605, "Top": 0.6946746706962585}, "Polygon": [{"X": 0.2857249975204468, "Y": 0.6946746706962585}, {"X": 0.34374338388442993, "Y": 0.6946853399276733}, {"X": 0.34363484382629395, "Y": 0.7053355574607849}, {"X": 0.2855813801288605, "Y": 0.7053495645523071}]}, "Id": "2bd9b217-164f-4d46-b1a5-33de9b985d80", "Relationships": [{"Type": "CHILD", "Ids": ["d7af3184-9d51-4722-9f36-ef617b6b4e02"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "282663", "Geometry": {"BoundingBox": {"Width": 0.03811751678586006, "Height": 0.009301897138357162, "Left": 0.503038763999939, "Top": 0.6935829520225525}, "Polygon": [{"X": 0.5030502676963806, "Y": 0.6935829520225525}, {"X": 0.5411478877067566, "Y": 0.6935917139053345}, {"X": 0.5411562323570251, "Y": 0.7028793692588806}, {"X": 0.503038763999939, "Y": 0.7028848528862}]}, "Id": "20191b61-0444-412a-a953-894bf6502b31", "Relationships": [{"Type": "CHILD", "Ids": ["0bc49841-0385-468e-a42b-ec0af7981d57"]}]}, {"BlockType": "LINE", "Confidence": 58.188777923583984, "Text": "HubOdometer", "Geometry": {"BoundingBox": {"Width": 0.07033190876245499, "Height": 0.010423227213323116, "Left": 0.2858525514602661, "Top": 0.7084086537361145}, "Polygon": [{"X": 0.28599217534065247, "Y": 0.7084341645240784}, {"X": 0.3561844527721405, "Y": 0.7084086537361145}, {"X": 0.3560861051082611, "Y": 0.7187772393226624}, {"X": 0.2858525514602661, "Y": 0.7188318967819214}]}, "Id": "d2f99188-219b-4cab-8417-b2ca1bfd1052", "Relationships": [{"Type": "CHILD", "Ids": ["14c578e2-e072-4d89-8539-c03b38aa0884"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "200029", "Geometry": {"BoundingBox": {"Width": 0.03803401440382004, "Height": 0.009461664594709873, "Left": 0.5030787587165833, "Top": 0.7061673402786255}, "Polygon": [{"X": 0.5030904412269592, "Y": 0.7061778903007507}, {"X": 0.541104257106781, "Y": 0.7061673402786255}, {"X": 0.5411127805709839, "Y": 0.7156040668487549}, {"X": 0.5030787587165833, "Y": 0.7156290411949158}]}, "Id": "a9a7c564-4f1c-4a0e-ac33-2bc5c2898795", "Relationships": [{"Type": "CHILD", "Ids": ["d1b7f168-e90f-4d0f-a561-3376cc2fb4e5"]}]}, {"BlockType": "LINE", "Confidence": 99.74483489990234, "Text": "My Love Rewards", "Geometry": {"BoundingBox": {"Width": 0.09565664827823639, "Height": 0.013184945099055767, "Left": 0.3572510778903961, "Top": 0.7449614405632019}, "Polygon": [{"X": 0.35737335681915283, "Y": 0.7451362609863281}, {"X": 0.4529077112674713, "Y": 0.7449614405632019}, {"X": 0.45285528898239136, "Y": 0.7579218149185181}, {"X": 0.3572510778903961, "Y": 0.7581464052200317}]}, "Id": "ef92b768-b9a8-4b78-bc77-46a1577b4980", "Relationships": [{"Type": "CHILD", "Ids": ["fbc0b20a-a552-4486-8266-8c731082c222", "4222fd40-2e1d-45d7-b15c-56c7aa7d194f", "4a943cd4-0d07-445f-8ded-5997a089f8cb"]}]}, {"BlockType": "LINE", "Confidence": 99.87633514404297, "Text": "Loyalty Member <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.1492033451795578, "Height": 0.011970208026468754, "Left": 0.2896702289581299, "Top": 0.7688994407653809}, "Polygon": [{"X": 0.28982236981391907, "Y": 0.7693149447441101}, {"X": 0.4388735890388489, "Y": 0.7688994407653809}, {"X": 0.43881818652153015, "Y": 0.7803851962089539}, {"X": 0.2896702289581299, "Y": 0.7808696031570435}]}, "Id": "1ad48f5e-d69a-4197-bb87-aa0bdaa5ffdf", "Relationships": [{"Type": "CHILD", "Ids": ["4dff8da5-9f5b-4415-8f7b-fe3f738736a1", "9be4055f-7d87-4236-aa9b-0c6d7add85bd", "7e25585a-9384-4f7f-a906-0c9adb2f20ed", "e4f206a5-a864-40bd-acd0-482fa42aa7ba"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Points Earned", "Geometry": {"BoundingBox": {"Width": 0.08126842230558395, "Height": 0.01159859448671341, "Left": 0.2907939553260803, "Top": 0.7802562117576599}, "Polygon": [{"X": 0.2909424304962158, "Y": 0.7805185914039612}, {"X": 0.3720623850822449, "Y": 0.7802562117576599}, {"X": 0.3719656765460968, "Y": 0.7915557026863098}, {"X": 0.2907939553260803, "Y": 0.7918547987937927}]}, "Id": "34a741dd-e653-49b9-bcb8-e64952347d57", "Relationships": [{"Type": "CHILD", "Ids": ["dc4d6e30-68d5-4126-b4b6-dc05e5724a80", "b18b0690-bb98-42ff-a6de-62e23546885f"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "134", "Geometry": {"BoundingBox": {"Width": 0.01794712245464325, "Height": 0.009592688642442226, "Left": 0.5100551247596741, "Top": 0.7826313376426697}, "Polygon": [{"X": 0.510063111782074, "Y": 0.7826914191246033}, {"X": 0.5280006527900696, "Y": 0.7826313376426697}, {"X": 0.5280022025108337, "Y": 0.7921570539474487}, {"X": 0.5100551247596741, "Y": 0.7922240495681763}]}, "Id": "df19897b-21d3-4b95-9ddb-a0862f553920", "Relationships": [{"Type": "CHILD", "Ids": ["ebb8d4cf-6c7e-4c72-a104-eade6ff6d778"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Points Redeemed", "Geometry": {"BoundingBox": {"Width": 0.09335650503635406, "Height": 0.011939551681280136, "Left": 0.29217228293418884, "Top": 0.7909353375434875}, "Polygon": [{"X": 0.29232320189476013, "Y": 0.7912766933441162}, {"X": 0.3855288028717041, "Y": 0.7909353375434875}, {"X": 0.3854386508464813, "Y": 0.802490234375}, {"X": 0.29217228293418884, "Y": 0.8028748631477356}]}, "Id": "06cfe69e-c7e9-4c5c-8a4f-ccad40cbdd0b", "Relationships": [{"Type": "CHILD", "Ids": ["def120c5-64da-4b22-9e3e-6413e6c54798", "a9ff7cad-14d5-4229-8ad6-bd38dc745591"]}]}, {"BlockType": "LINE", "Confidence": 98.51702117919922, "Text": "0", "Geometry": {"BoundingBox": {"Width": 0.006245152093470097, "Height": 0.00869249552488327, "Left": 0.5217739343643188, "Top": 0.7948458790779114}, "Polygon": [{"X": 0.5217755436897278, "Y": 0.7948698401451111}, {"X": 0.5280176997184753, "Y": 0.7948458790779114}, {"X": 0.52801913022995, "Y": 0.8035122156143188}, {"X": 0.5217739343643188, "Y": 0.8035383820533752}]}, "Id": "4f4a2919-9137-4cb8-80f2-91ef0eee73d0", "Relationships": [{"Type": "CHILD", "Ids": ["2e8d64f2-ba20-45ee-9fc6-7093d78b70b0"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Points Balance", "Geometry": {"BoundingBox": {"Width": 0.08648919314146042, "Height": 0.011862740851938725, "Left": 0.29305461049079895, "Top": 0.8017643690109253}, "Polygon": [{"X": 0.2932037115097046, "Y": 0.8021178245544434}, {"X": 0.37954381108283997, "Y": 0.8017643690109253}, {"X": 0.37945055961608887, "Y": 0.8132337927818298}, {"X": 0.29305461049079895, "Y": 0.8136270642280579}]}, "Id": "fe31ef3a-e54f-4262-89ae-ad2daaf82e64", "Relationships": [{"Type": "CHILD", "Ids": ["ca026593-d90a-4a31-986b-caaf53f9d656", "8a3952fe-6a8f-4711-89cd-15d20cca59eb"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "150", "Geometry": {"BoundingBox": {"Width": 0.01813640259206295, "Height": 0.010115102864801884, "Left": 0.5099960565567017, "Top": 0.8060806393623352}, "Polygon": [{"X": 0.****************, "Y": 0.8061584234237671}, {"X": 0.****************, "Y": 0.8060806393623352}, {"X": 0.****************, "Y": 0.8161106109619141}, {"X": 0.5099960565567017, "Y": 0.8161957263946533}]}, "Id": "3d6cf55b-8d26-4ae4-9ed8-ecfacea8e36c", "Relationships": [{"Type": "CHILD", "Ids": ["51c4a49c-db31-4376-ad23-4c9553eeecd3"]}]}, {"BlockType": "LINE", "Confidence": 99.94764709472656, "Text": "Drink Refill Credits", "Geometry": {"BoundingBox": {"Width": 0.12380817532539368, "Height": 0.014939587563276291, "Left": 0.29210004210472107, "Top": 0.8142572641372681}, "Polygon": [{"X": 0.29228681325912476, "Y": 0.8148259520530701}, {"X": 0.*****************, "Y": 0.8142572641372681}, {"X": 0.****************, "Y": 0.8285569548606873}, {"X": 0.29210004210472107, "Y": 0.8291968703269958}]}, "Id": "07a20e65-af05-46cc-a395-33d3efa2aca3", "Relationships": [{"Type": "CHILD", "Ids": ["4bacee5e-b87e-4f66-8473-980f46acbf2a", "e3ae4bfd-962f-4b4e-9f20-7bf779ca276d", "baca320d-639a-4eca-a90a-03018bf0750e"]}]}, {"BlockType": "LINE", "Confidence": 99.24645233154297, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.0051827216520905495, "Height": 0.010582732036709785, "Left": 0.522331714630127, "Top": 0.8201528191566467}, "Polygon": [{"X": 0.****************, "Y": 0.8201779723167419}, {"X": 0.****************, "Y": 0.8201528191566467}, {"X": 0.****************, "Y": 0.8307081460952759}, {"X": 0.522331714630127, "Y": 0.8307355046272278}]}, "Id": "ec97588c-c99e-45bc-a155-45d3e8d3a02c", "Relationships": [{"Type": "CHILD", "Ids": ["cb57647a-8bdc-417e-ac55-0693840ac5d9"]}]}, {"BlockType": "LINE", "Confidence": 99.8425521850586, "Text": "Shower Credits", "Geometry": {"BoundingBox": {"Width": 0.08675622195005417, "Height": 0.011230895295739174, "Left": 0.2920898497104645, "Top": 0.828168511390686}, "Polygon": [{"X": 0.29222992062568665, "Y": 0.8286144733428955}, {"X": 0.37884607911109924, "Y": 0.828168511390686}, {"X": 0.37875840067863464, "Y": 0.8389158844947815}, {"X": 0.2920898497104645, "Y": 0.8393993973731995}]}, "Id": "ade6f25e-34ec-495d-8ac5-5e7135a5c73d", "Relationships": [{"Type": "CHILD", "Ids": ["5afb7ea5-f132-4973-ad93-e252ae236981", "981d3654-08f5-44d1-b58c-ef90ac3a278c"]}]}, {"BlockType": "WORD", "Confidence": 58.34821319580078, "Text": "Loves.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17179198563098907, "Height": 0.04243770241737366, "Left": 0.3969765603542328, "Top": 0.2628079354763031}, "Polygon": [{"X": 0.3972688913345337, "Y": 0.2628079354763031}, {"X": 0.5686694979667664, "Y": 0.2657993733882904}, {"X": 0.5687685608863831, "Y": 0.30524563789367676}, {"X": 0.3969765603542328, "Y": 0.30252018570899963}]}, "Id": "623c0ee6-3eb3-40d1-9a8a-e9fc4a559286"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Loves", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03003636933863163, "Height": 0.010686921887099743, "Left": 0.4118615984916687, "Top": 0.318001389503479}, "Polygon": [{"X": 0.4119281470775604, "Y": 0.318001389503479}, {"X": 0.4418979585170746, "Y": 0.31845858693122864}, {"X": 0.44184911251068115, "Y": 0.3286883234977722}, {"X": 0.4118615984916687, "Y": 0.3282431364059448}]}, "Id": "82223a1a-8a43-4567-a673-71d90b1846e3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "#395", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02380475401878357, "Height": 0.011520500294864178, "Left": 0.44802263379096985, "Top": 0.3162826895713806}, "Polygon": [{"X": 0.44807201623916626, "Y": 0.3162826895713806}, {"X": 0.4718273878097534, "Y": 0.3166472315788269}, {"X": 0.4717932939529419, "Y": 0.32780319452285767}, {"X": 0.44802263379096985, "Y": 0.3274490535259247}]}, "Id": "723a17a0-df51-4a5b-85fd-ff73b9603524"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "3407", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02361604943871498, "Height": 0.010661369189620018, "Left": 0.3597559928894043, "Top": 0.33237409591674805}, "Polygon": [{"X": 0.3598538637161255, "Y": 0.33237409591674805}, {"X": 0.383372038602829, "Y": 0.3327186107635498}, {"X": 0.38328817486763, "Y": 0.3430354595184326}, {"X": 0.3597559928894043, "Y": 0.34270042181015015}]}, "Id": "d6eeef42-dda3-4854-b63d-e06e8caed1ef"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "South", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030170239508152008, "Height": 0.01107910368591547, "Left": 0.3890082836151123, "Top": 0.3313170373439789}, "Polygon": [{"X": 0.3890913724899292, "Y": 0.3313170373439789}, {"X": 0.4191785454750061, "Y": 0.33175957202911377}, {"X": 0.4191139042377472, "Y": 0.3423961400985718}, {"X": 0.3890082836151123, "Y": 0.3419661521911621}]}, "Id": "5cc95a69-7421-4db8-81c9-8ecdf5688213"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "State", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030100304633378983, "Height": 0.010019063018262386, "Left": 0.42451032996177673, "Top": 0.3314317762851715}, "Polygon": [{"X": 0.4245656430721283, "Y": 0.3314317762851715}, {"X": 0.4546106457710266, "Y": 0.33187419176101685}, {"X": 0.454571932554245, "Y": 0.34145084023475647}, {"X": 0.42451032996177673, "Y": 0.34101971983909607}]}, "Id": "f2e50bf3-e611-4cb8-b9f0-7b495abcc5b7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02448221854865551, "Height": 0.011417213827371597, "Left": 0.46007248759269714, "Top": 0.3294815421104431}, "Polygon": [{"X": 0.4601137340068817, "Y": 0.3294815421104431}, {"X": 0.4845547080039978, "Y": 0.3298438787460327}, {"X": 0.4845290184020996, "Y": 0.3408987820148468}, {"X": 0.46007248759269714, "Y": 0.3405470550060272}]}, "Id": "c8a0ea81-4d3d-4ec2-80d5-dbbb72d93b7f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "45/52", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030280007049441338, "Height": 0.011333298869431019, "Left": 0.4901629686355591, "Top": 0.3284321427345276}, "Polygon": [{"X": 0.4901847839355469, "Y": 0.3284321427345276}, {"X": 0.5204429626464844, "Y": 0.32888251543045044}, {"X": 0.5204401016235352, "Y": 0.33976542949676514}, {"X": 0.4901629686355591, "Y": 0.33932802081108093}]}, "Id": "d3d6aa7f-eb1a-4f4f-919a-b27a519b8bc4"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "Kankakee,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05204438418149948, "Height": 0.011842645704746246, "Left": 0.3894091844558716, "Top": 0.3443211615085602}, "Polygon": [{"X": 0.38949570059776306, "Y": 0.3443211615085602}, {"X": 0.44145357608795166, "Y": 0.345058411359787}, {"X": 0.44140034914016724, "Y": 0.3561638295650482}, {"X": 0.3894091844558716, "Y": 0.35544922947883606}]}, "Id": "d0e84867-5959-485e-97af-7dacd2242e7c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011153561994433403, "Height": 0.010032866150140762, "Left": 0.44944003224372864, "Top": 0.3437179625034332}, "Polygon": [{"X": 0.44948282837867737, "Y": 0.3437179625034332}, {"X": 0.460593581199646, "Y": 0.3438762426376343}, {"X": 0.46055707335472107, "Y": 0.3537508249282837}, {"X": 0.44944003224372864, "Y": 0.35359683632850647}]}, "Id": "ff6d337c-3ab7-480f-a4f7-32ff7a11783f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "60901", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03023080714046955, "Height": 0.011295322328805923, "Left": 0.4666099548339844, "Top": 0.3422981798648834}, "Polygon": [{"X": 0.4666464030742645, "Y": 0.3422981798648834}, {"X": 0.49684077501296997, "Y": 0.34273040294647217}, {"X": 0.49682319164276123, "Y": 0.35359349846839905}, {"X": 0.4666099548339844, "Y": 0.35317420959472656}]}, "Id": "4400bb83-e020-479e-b237-9cf719e50326"}, {"BlockType": "WORD", "Confidence": 90.05520629882812, "Text": "(815) 929-1549", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08309073001146317, "Height": 0.014291251078248024, "Left": 0.4024497866630554, "Top": 0.354943186044693}, "Polygon": [{"X": 0.4025424122810364, "Y": 0.354943186044693}, {"X": 0.485540509223938, "Y": 0.35608622431755066}, {"X": 0.4855107367038727, "Y": 0.36923444271087646}, {"X": 0.4024497866630554, "Y": 0.36813437938690186}]}, "Id": "0c841b32-b95a-48ff-878b-c85b5037abe4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "07/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06003175675868988, "Height": 0.010871248319745064, "Left": 0.30017659068107605, "Top": 0.3832080662250519}, "Polygon": [{"X": 0.3003070056438446, "Y": 0.3832080662250519}, {"X": 0.3602083623409271, "Y": 0.3839622139930725}, {"X": 0.3601129353046417, "Y": 0.394079327583313}, {"X": 0.30017659068107605, "Y": 0.39334893226623535}]}, "Id": "be693c43-3509-4ef1-be2a-c38f978f0973"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Tkt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01777125522494316, "Height": 0.009287203662097454, "Left": 0.36611878871917725, "Top": 0.38364797830581665}, "Polygon": [{"X": 0.3662012219429016, "Y": 0.38364797830581665}, {"X": 0.3838900327682495, "Y": 0.3838709592819214}, {"X": 0.38381683826446533, "Y": 0.392935186624527}, {"X": 0.36611878871917725, "Y": 0.39271852374076843}]}, "Id": "645b550c-06bb-4cf7-8f37-459daa8c92b0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "#99479847", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05363492667675018, "Height": 0.011204048059880733, "Left": 0.3899878263473511, "Top": 0.3819352686405182}, "Polygon": [{"X": 0.39006927609443665, "Y": 0.3819352686405182}, {"X": 0.44362276792526245, "Y": 0.3826146423816681}, {"X": 0.4435737133026123, "Y": 0.3931393027305603}, {"X": 0.3899878263473511, "Y": 0.39248213171958923}]}, "Id": "e3fbb354-605a-4af2-a6dc-fc33c760e7f5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028166523203253746, "Height": 0.009590467438101768, "Left": 0.2979479134082794, "Top": 0.40718400478363037}, "Polygon": [{"X": 0.29806816577911377, "Y": 0.40718400478363037}, {"X": 0.3261144161224365, "Y": 0.40751028060913086}, {"X": 0.3260091245174408, "Y": 0.4167744815349579}, {"X": 0.2979479134082794, "Y": 0.4164583683013916}]}, "Id": "2d19df84-6003-40f1-bb80-c64690c13c2a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02426202781498432, "Height": 0.008769984357059002, "Left": 0.334076464176178, "Top": 0.4068295955657959}, "Polygon": [{"X": 0.3341691195964813, "Y": 0.4068295955657959}, {"X": 0.35833850502967834, "Y": 0.4071115255355835}, {"X": 0.3582576513290405, "Y": 0.4155995845794678}, {"X": 0.334076464176178, "Y": 0.41532573103904724}]}, "Id": "8a7a8659-f6f3-4171-bf8f-c62b6e0b2595"}, {"BlockType": "WORD", "Confidence": 99.09378814697266, "Text": "(ORIGINAL)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05835532397031784, "Height": 0.010424274951219559, "Left": 0.40156951546669006, "Top": 0.40539637207984924}, "Polygon": [{"X": 0.40163835883140564, "Y": 0.40539637207984924}, {"X": 0.4599248468875885, "Y": 0.4060814678668976}, {"X": 0.4598885774612427, "Y": 0.4158206582069397}, {"X": 0.40156951546669006, "Y": 0.4151579737663269}]}, "Id": "15321c38-2089-4353-954b-e0a466ae14be"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Qty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018917616456747055, "Height": 0.010131032206118107, "Left": 0.29361701011657715, "Top": 0.42810171842575073}, "Polygon": [{"X": 0.293748140335083, "Y": 0.42810171842575073}, {"X": 0.3125346302986145, "Y": 0.42830461263656616}, {"X": 0.3124142289161682, "Y": 0.43823274970054626}, {"X": 0.29361701011657715, "Y": 0.4380372166633606}]}, "Id": "89300fdd-9510-4805-9e97-7b7d3c8841d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024772686883807182, "Height": 0.0086456798017025, "Left": 0.31830060482025146, "Top": 0.42821869254112244}, "Polygon": [{"X": 0.31839948892593384, "Y": 0.42821869254112244}, {"X": 0.3430732786655426, "Y": 0.42848530411720276}, {"X": 0.34298625588417053, "Y": 0.43686437606811523}, {"X": 0.31830060482025146, "Y": 0.43660590052604675}]}, "Id": "6ce0ece9-a042-454b-a73d-35875d72bc40"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030655253678560257, "Height": 0.008415746502578259, "Left": 0.4533301293849945, "Top": 0.4277014136314392}, "Polygon": [{"X": 0.45336323976516724, "Y": 0.4277014136314392}, {"X": 0.48398539423942566, "Y": 0.4280347228050232}, {"X": 0.4839664399623871, "Y": 0.43611717224121094}, {"X": 0.4533301293849945, "Y": 0.4357936680316925}]}, "Id": "545cb36f-46cd-4b01-af0c-b137b0d74b28"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029140856117010117, "Height": 0.008396722376346588, "Left": 0.5089405179023743, "Top": 0.4273689091205597}, "Polygon": [{"X": 0.5089479684829712, "Y": 0.4273689091205597}, {"X": 0.5380753874778748, "Y": 0.42768704891204834}, {"X": 0.5380813479423523, "Y": 0.43576565384864807}, {"X": 0.5089405179023743, "Y": 0.4354568421840668}]}, "Id": "67d445bb-9dff-477a-aae9-c5741f48361a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DIESEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03675799444317818, "Height": 0.01052020862698555, "Left": 0.3174756169319153, "Top": 0.45237067341804504}, "Polygon": [{"X": 0.317595899105072, "Y": 0.45237067341804504}, {"X": 0.35423362255096436, "Y": 0.45273125171661377}, {"X": 0.3541347086429596, "Y": 0.4628908932209015}, {"X": 0.3174756169319153, "Y": 0.4625449478626251}]}, "Id": "79397dab-c047-4b99-bcb4-1cd2ec21d38a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "520.61", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03698154538869858, "Height": 0.0105452174320817, "Left": 0.5025667548179626, "Top": 0.45125240087509155}, "Polygon": [{"X": 0.5025798082351685, "Y": 0.45125240087509155}, {"X": 0.5395399332046509, "Y": 0.45162051916122437}, {"X": 0.5395482778549194, "Y": 0.4617976248264313}, {"X": 0.5025667548179626, "Y": 0.4614444375038147}]}, "Id": "441b6789-fb60-41f2-bb8a-aade6e8b202c"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Pump:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02928336337208748, "Height": 0.01144578866660595, "Left": 0.3233851194381714, "Top": 0.46576520800590515}, "Polygon": [{"X": 0.3235135078430176, "Y": 0.46576520800590515}, {"X": 0.35266849398612976, "Y": 0.4660366475582123}, {"X": 0.35255879163742065, "Y": 0.47721099853515625}, {"X": 0.3233851194381714, "Y": 0.4769524037837982}]}, "Id": "9af64cab-a2a1-4945-8904-af735a75a5f1"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "18", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011463267728686333, "Height": 0.00925323087722063, "Left": 0.45409098267555237, "Top": 0.4653787612915039}, "Polygon": [{"X": 0.4541279673576355, "Y": 0.4653787612915039}, {"X": 0.46555426716804504, "Y": 0.46548590064048767}, {"X": 0.46552327275276184, "Y": 0.47463199496269226}, {"X": 0.45409098267555237, "Y": 0.4745290279388428}]}, "Id": "f781c69b-4425-4609-8a21-a4660b3a4828"}, {"BlockType": "WORD", "Confidence": 99.68590545654297, "Text": "Gallons:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047946587204933167, "Height": 0.010707670822739601, "Left": 0.32322049140930176, "Top": 0.47857698798179626}, "Polygon": [{"X": 0.3233388066291809, "Y": 0.47857698798179626}, {"X": 0.3711670935153961, "Y": 0.4789978563785553}, {"X": 0.3710770010948181, "Y": 0.4892846643924713}, {"X": 0.32322049140930176, "Y": 0.48888322710990906}]}, "Id": "8f5043f1-9775-4bcc-8007-501b2736c05f"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "133.868", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042919617146253586, "Height": 0.010256355628371239, "Left": 0.44755178689956665, "Top": 0.47794750332832336}, "Polygon": [{"X": 0.44759538769721985, "Y": 0.47794750332832336}, {"X": 0.49047139286994934, "Y": 0.47832775115966797}, {"X": 0.49045196175575256, "Y": 0.48820385336875916}, {"X": 0.44755178689956665, "Y": 0.4878404140472412}]}, "Id": "525b6b61-1c3b-4290-b158-1f1f82feb42a"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030896181240677834, "Height": 0.010338164865970612, "Left": 0.3236068785190582, "Top": 0.49203383922576904}, "Polygon": [{"X": 0.3237224519252777, "Y": 0.49203383922576904}, {"X": 0.3545030653476715, "Y": 0.49228817224502563}, {"X": 0.3544052839279175, "Y": 0.5023720264434814}, {"X": 0.3236068785190582, "Y": 0.5021299123764038}]}, "Id": "48197183-723d-4b21-9078-1b03f46b3438"}, {"BlockType": "WORD", "Confidence": 93.31074523925781, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005886511877179146, "Height": 0.009565073996782303, "Left": 0.3606595993041992, "Top": 0.49198734760284424}, "Polygon": [{"X": 0.3607485294342041, "Y": 0.49198734760284424}, {"X": 0.3665461242198944, "Y": 0.4920353293418884}, {"X": 0.3664603531360626, "Y": 0.501552402973175}, {"X": 0.3606595993041992, "Y": 0.5015066266059875}]}, "Id": "e75ea434-5700-427f-9ee7-a9bd96da2fa3"}, {"BlockType": "WORD", "Confidence": 99.45252227783203, "Text": "Gal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023077748715877533, "Height": 0.01028914749622345, "Left": 0.3726799190044403, "Top": 0.4916333556175232}, "Polygon": [{"X": 0.3727674186229706, "Y": 0.4916333556175232}, {"X": 0.39575764536857605, "Y": 0.49182406067848206}, {"X": 0.3956834077835083, "Y": 0.5019224882125854}, {"X": 0.3726799190044403, "Y": 0.5017409920692444}]}, "Id": "9fb274d3-ba89-4d1a-a700-f3c19e627a0d"}, {"BlockType": "WORD", "Confidence": 99.81205749511719, "Text": "3.889", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031188972294330597, "Height": 0.01007451955229044, "Left": 0.459010511636734, "Top": 0.4913272261619568}, "Polygon": [{"X": 0.45904740691185, "Y": 0.4913272261619568}, {"X": 0.490199476480484, "Y": 0.49158692359924316}, {"X": 0.49018001556396484, "Y": 0.5014017224311829}, {"X": 0.459010511636734, "Y": 0.5011541843414307}]}, "Id": "4ef99271-6f3a-4182-b31e-56d5a699fcdc"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04685430973768234, "Height": 0.010452276095747948, "Left": 0.29362016916275024, "Top": 0.5180655121803284}, "Polygon": [{"X": 0.2937532365322113, "Y": 0.5180655121803284}, {"X": 0.3404744863510132, "Y": 0.5184026956558228}, {"X": 0.34036847949028015, "Y": 0.5285177826881409}, {"X": 0.29362016916275024, "Y": 0.528199315071106}]}, "Id": "f22aebba-2cd0-4cac-b0a4-96921b0801d6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "520.61", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03754885867238045, "Height": 0.010437089018523693, "Left": 0.5023636221885681, "Top": 0.5174089670181274}, "Polygon": [{"X": 0.5023767352104187, "Y": 0.5174089670181274}, {"X": 0.5399039387702942, "Y": 0.5176830291748047}, {"X": 0.5399124622344971, "Y": 0.5278460383415222}, {"X": 0.5023636221885681, "Y": 0.5275871753692627}]}, "Id": "bb4f185e-9c63-4e30-9779-c317e1d85313"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030040744692087173, "Height": 0.010018966160714626, "Left": 0.29339075088500977, "Top": 0.5315527319908142}, "Polygon": [{"X": 0.29351988434791565, "Y": 0.5315527319908142}, {"X": 0.32343149185180664, "Y": 0.5317525267601013}, {"X": 0.3233191967010498, "Y": 0.5415716767311096}, {"X": 0.29339075088500977, "Y": 0.5413835048675537}]}, "Id": "f96efe65-2e8e-491f-9ad4-7695189d8fa7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01850982941687107, "Height": 0.009967793710529804, "Left": 0.32959938049316406, "Top": 0.5314549803733826}, "Polygon": [{"X": 0.3297085464000702, "Y": 0.5314549803733826}, {"X": 0.3481091856956482, "Y": 0.5315781235694885}, {"X": 0.348010390996933, "Y": 0.5414227843284607}, {"X": 0.32959938049316406, "Y": 0.5413067936897278}]}, "Id": "33a6f884-b82f-4c6c-88b7-45eb73dfd6f4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02538549341261387, "Height": 0.010209642350673676, "Left": 0.5150367617607117, "Top": 0.5307148098945618}, "Polygon": [{"X": 0.5150424242019653, "Y": 0.5307148098945618}, {"X": 0.540413498878479, "Y": 0.5308866500854492}, {"X": 0.540422260761261, "Y": 0.5409244298934937}, {"X": 0.5150367617607117, "Y": 0.5407627820968628}]}, "Id": "c24c5f2f-d4d4-44f0-9c4f-2faef2cabeef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028774499893188477, "Height": 0.010384700261056423, "Left": 0.29303935170173645, "Top": 0.5446302890777588}, "Polygon": [{"X": 0.2931737005710602, "Y": 0.5446302890777588}, {"X": 0.3218138515949249, "Y": 0.5448066592216492}, {"X": 0.3216962516307831, "Y": 0.555014967918396}, {"X": 0.29303935170173645, "Y": 0.554850161075592}]}, "Id": "311239df-0cd8-45f5-ae9b-b06119c3330f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "520.61", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037438396364450455, "Height": 0.010554016567766666, "Left": 0.502533495426178, "Top": 0.5440988540649414}, "Polygon": [{"X": 0.5025467276573181, "Y": 0.5440988540649414}, {"X": 0.5399631857872009, "Y": 0.5443320274353027}, {"X": 0.5399718880653381, "Y": 0.5546528697013855}, {"X": 0.502533495426178, "Y": 0.554435133934021}]}, "Id": "f7ea285e-2975-4aad-b6ea-5a96137336cb"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "Received:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05402747914195061, "Height": 0.010773943737149239, "Left": 0.29147446155548096, "Top": 0.5712147355079651}, "Polygon": [{"X": 0.29161348938941956, "Y": 0.5712147355079651}, {"X": 0.34550195932388306, "Y": 0.5714895129203796}, {"X": 0.3453952372074127, "Y": 0.5819886326789856}, {"X": 0.29147446155548096, "Y": 0.5817362666130066}]}, "Id": "edb40e03-361c-4973-aeec-a28a2e2d9bbc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03178718686103821, "Height": 0.010569250211119652, "Left": 0.30335694551467896, "Top": 0.5848168730735779}, "Polygon": [{"X": 0.30348774790763855, "Y": 0.5848168730735779}, {"X": 0.33514413237571716, "Y": 0.5849612355232239}, {"X": 0.3350321650505066, "Y": 0.5953860878944397}, {"X": 0.30335694551467896, "Y": 0.5952548384666443}]}, "Id": "7f4b41e7-252f-4b17-b5a1-674803e70591"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "One", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018163910135626793, "Height": 0.009946363978087902, "Left": 0.341127872467041, "Top": 0.5851773619651794}, "Polygon": [{"X": 0.34123051166534424, "Y": 0.5851773619651794}, {"X": 0.35929179191589355, "Y": 0.5852595567703247}, {"X": 0.35919931530952454, "Y": 0.5951237082481384}, {"X": 0.341127872467041, "Y": 0.5950485467910767}]}, "Id": "6b50c074-1684-47c8-baac-b70ee8f3cecb"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "OTR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018702112138271332, "Height": 0.010396297089755535, "Left": 0.36548641324043274, "Top": 0.584872841835022}, "Polygon": [{"X": 0.3655795156955719, "Y": 0.584872841835022}, {"X": 0.38418853282928467, "Y": 0.5849578380584717}, {"X": 0.3841063976287842, "Y": 0.5952691435813904}, {"X": 0.36548641324043274, "Y": 0.5951917171478271}]}, "Id": "2a02040b-e233-427a-866a-0ff6a6e9953f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "520.61", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03773930296301842, "Height": 0.01058113481849432, "Left": 0.5024480223655701, "Top": 0.5846767425537109}, "Polygon": [{"X": 0.5024613738059998, "Y": 0.5846767425537109}, {"X": 0.5401784181594849, "Y": 0.5848503708839417}, {"X": 0.5401873588562012, "Y": 0.5952578783035278}, {"X": 0.5024480223655701, "Y": 0.5950999855995178}]}, "Id": "c3a83d46-f75c-400e-b014-f8a92d53633f"}, {"BlockType": "WORD", "Confidence": 99.140625, "Text": "0149", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025221170857548714, "Height": 0.010470618493855, "Left": 0.39594316482543945, "Top": 0.5984262824058533}, "Polygon": [{"X": 0.3960188627243042, "Y": 0.5984262824058533}, {"X": 0.4211643636226654, "Y": 0.5985276699066162}, {"X": 0.42110347747802734, "Y": 0.6088969111442566}, {"X": 0.39594316482543945, "Y": 0.6088058352470398}]}, "Id": "70be3fc6-c135-4db7-b1c5-9808e04608cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SWIPED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03786572068929672, "Height": 0.010249549522995949, "Left": 0.43346452713012695, "Top": 0.5984014272689819}, "Polygon": [{"X": 0.43351686000823975, "Y": 0.5984014272689819}, {"X": 0.4713302552700043, "Y": 0.5985541939735413}, {"X": 0.47129958868026733, "Y": 0.6086509227752686}, {"X": 0.43346452713012695, "Y": 0.6085134148597717}]}, "Id": "68617877-3235-4c4f-bc57-e2e3bc041d83"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025770941749215126, "Height": 0.01024918258190155, "Left": 0.3145129084587097, "Top": 0.6120215654373169}, "Polygon": [{"X": 0.3146337568759918, "Y": 0.6120215654373169}, {"X": 0.3402838408946991, "Y": 0.6121107935905457}, {"X": 0.34017783403396606, "Y": 0.6222707629203796}, {"X": 0.3145129084587097, "Y": 0.6221919059753418}]}, "Id": "4bcbf5ec-bca5-4d81-a230-15813eb71696"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016906213015317917, "Height": 0.01014717947691679, "Left": 0.3462231755256653, "Top": 0.6121739745140076}, "Polygon": [{"X": 0.3463250696659088, "Y": 0.6121739745140076}, {"X": 0.3631294071674347, "Y": 0.6122323870658875}, {"X": 0.3630371391773224, "Y": 0.6223211288452148}, {"X": 0.3462231755256653, "Y": 0.6222694516181946}]}, "Id": "1cebf853-888c-4212-ac5c-787a083a062d"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "114525", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03672295808792114, "Height": 0.010477079078555107, "Left": 0.3716006278991699, "Top": 0.6120002269744873}, "Polygon": [{"X": 0.37169039249420166, "Y": 0.6120002269744873}, {"X": 0.40832358598709106, "Y": 0.612127959728241}, {"X": 0.40825533866882324, "Y": 0.6224773526191711}, {"X": 0.3716006278991699, "Y": 0.6223646998405457}]}, "Id": "2b33305a-44ba-49a3-bc94-f2bfc8af90e9"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04903612285852432, "Height": 0.011424263939261436, "Left": 0.3010958433151245, "Top": 0.6260037422180176}, "Polygon": [{"X": 0.3012385964393616, "Y": 0.6260037422180176}, {"X": 0.35013195872306824, "Y": 0.6261464357376099}, {"X": 0.3500206172466278, "Y": 0.6374279856681824}, {"X": 0.3010958433151245, "Y": 0.6373071670532227}]}, "Id": "f462a761-55d0-477d-8e67-14d8580d9a11"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "AMERILUX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05009050294756889, "Height": 0.010230762884020805, "Left": 0.35817545652389526, "Top": 0.6256202459335327}, "Polygon": [{"X": 0.3582705557346344, "Y": 0.6256202459335327}, {"X": 0.40826597809791565, "Y": 0.6257672905921936}, {"X": 0.4081995189189911, "Y": 0.6358510255813599}, {"X": 0.35817545652389526, "Y": 0.6357240676879883}]}, "Id": "a191fce9-abeb-4506-b907-5661fc33181c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRANSPORTATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08834318071603775, "Height": 0.010639987885951996, "Left": 0.4146164357662201, "Top": 0.6255977749824524}, "Polygon": [{"X": 0.4146813154220581, "Y": 0.6255977749824524}, {"X": 0.5029596090316772, "Y": 0.6258580684661865}, {"X": 0.502946674823761, "Y": 0.6362377405166626}, {"X": 0.4146164357662201, "Y": 0.6360141038894653}]}, "Id": "04a0cbfa-a957-4bf6-8c18-5ecb54786859"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "INVOICE#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05034299194812775, "Height": 0.010322345420718193, "Left": 0.3016186058521271, "Top": 0.6390743851661682}, "Polygon": [{"X": 0.30174732208251953, "Y": 0.6390743851661682}, {"X": 0.35196158289909363, "Y": 0.6391948461532593}, {"X": 0.35186201333999634, "Y": 0.6493967175483704}, {"X": 0.3016186058521271, "Y": 0.6492966413497925}]}, "Id": "2dc18442-9b06-4cbf-bf22-0c593ab06d4f"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "07717", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031376536935567856, "Height": 0.01018434762954712, "Left": 0.35793542861938477, "Top": 0.6391512751579285}, "Polygon": [{"X": 0.358030766248703, "Y": 0.6391512751579285}, {"X": 0.3893119692802429, "Y": 0.6392263770103455}, {"X": 0.3892345726490021, "Y": 0.6493356227874756}, {"X": 0.35793542861938477, "Y": 0.6492730975151062}]}, "Id": "5d5ee261-7725-4d3a-84aa-88f4f7527cd0"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "TripNumber", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06352675706148148, "Height": 0.01237125601619482, "Left": 0.2863508462905884, "Top": 0.6809547543525696}, "Polygon": [{"X": 0.28651663661003113, "Y": 0.6809547543525696}, {"X": 0.34987759590148926, "Y": 0.6810010075569153}, {"X": 0.3497561514377594, "Y": 0.6933260560035706}, {"X": 0.2863508462905884, "Y": 0.6933109164237976}]}, "Id": "2366c26c-f146-4bc6-99f9-f58f6effcc60"}, {"BlockType": "WORD", "Confidence": 99.2735595703125, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006150976754724979, "Height": 0.009307133965194225, "Left": 0.5349729061126709, "Top": 0.680779755115509}, "Polygon": [{"X": 0.5349729061126709, "Y": 0.680779755115509}, {"X": 0.5411154627799988, "Y": 0.6807843446731567}, {"X": 0.5411238670349121, "Y": 0.6900869011878967}, {"X": 0.5349780917167664, "Y": 0.6900846362113953}]}, "Id": "b706fef0-9c43-4085-8ae9-34ad6ed70353"}, {"BlockType": "WORD", "Confidence": 99.21077728271484, "Text": "VehicleID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05816200003027916, "Height": 0.010674909688532352, "Left": 0.2855813801288605, "Top": 0.6946746706962585}, "Polygon": [{"X": 0.2857249975204468, "Y": 0.6946746706962585}, {"X": 0.34374338388442993, "Y": 0.6946853399276733}, {"X": 0.34363484382629395, "Y": 0.7053355574607849}, {"X": 0.2855813801288605, "Y": 0.7053495645523071}]}, "Id": "d7af3184-9d51-4722-9f36-ef617b6b4e02"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "282663", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03811751678586006, "Height": 0.009301897138357162, "Left": 0.503038763999939, "Top": 0.6935829520225525}, "Polygon": [{"X": 0.5030502676963806, "Y": 0.6935829520225525}, {"X": 0.5411478877067566, "Y": 0.6935917139053345}, {"X": 0.5411562323570251, "Y": 0.7028793692588806}, {"X": 0.503038763999939, "Y": 0.7028848528862}]}, "Id": "0bc49841-0385-468e-a42b-ec0af7981d57"}, {"BlockType": "WORD", "Confidence": 58.188777923583984, "Text": "HubOdometer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07033190876245499, "Height": 0.010423227213323116, "Left": 0.2858525514602661, "Top": 0.7084086537361145}, "Polygon": [{"X": 0.28599217534065247, "Y": 0.7084341645240784}, {"X": 0.3561844527721405, "Y": 0.7084086537361145}, {"X": 0.3560861051082611, "Y": 0.7187772393226624}, {"X": 0.2858525514602661, "Y": 0.7188318967819214}]}, "Id": "14c578e2-e072-4d89-8539-c03b38aa0884"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "200029", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03803401440382004, "Height": 0.009461664594709873, "Left": 0.5030787587165833, "Top": 0.7061673402786255}, "Polygon": [{"X": 0.5030904412269592, "Y": 0.7061778903007507}, {"X": 0.541104257106781, "Y": 0.7061673402786255}, {"X": 0.5411127805709839, "Y": 0.7156040668487549}, {"X": 0.5030787587165833, "Y": 0.7156290411949158}]}, "Id": "d1b7f168-e90f-4d0f-a561-3376cc2fb4e5"}, {"BlockType": "WORD", "Confidence": 99.39313507080078, "Text": "My", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013665087521076202, "Height": 0.010640526190400124, "Left": 0.3572510778903961, "Top": 0.7475059032440186}, "Polygon": [{"X": 0.35735082626342773, "Y": 0.7475320100784302}, {"X": 0.3709161579608917, "Y": 0.7475059032440186}, {"X": 0.37082451581954956, "Y": 0.7581145167350769}, {"X": 0.3572510778903961, "Y": 0.7581464052200317}]}, "Id": "fbc0b20a-a552-4486-8266-8c731082c222"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Love", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026107192039489746, "Height": 0.009222397580742836, "Left": 0.37653180956840515, "Top": 0.7471837401390076}, "Polygon": [{"X": 0.37660813331604004, "Y": 0.7472335696220398}, {"X": 0.4026390016078949, "Y": 0.7471837401390076}, {"X": 0.40257614850997925, "Y": 0.7563467025756836}, {"X": 0.37653180956840515, "Y": 0.7564061284065247}]}, "Id": "4222fd40-2e1d-45d7-b15c-56c7aa7d194f"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Rewards", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044665757566690445, "Height": 0.00997892301529646, "Left": 0.40824195742607117, "Top": 0.7449614405632019}, "Polygon": [{"X": 0.40830671787261963, "Y": 0.7450430989265442}, {"X": 0.4529077112674713, "Y": 0.7449614405632019}, {"X": 0.4528677463531494, "Y": 0.7548410296440125}, {"X": 0.40824195742607117, "Y": 0.754940390586853}]}, "Id": "4a943cd4-0d07-445f-8ded-5997a089f8cb"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "Loyalty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04440281540155411, "Height": 0.011154881678521633, "Left": 0.28967636823654175, "Top": 0.7692495584487915}, "Polygon": [{"X": 0.289821594953537, "Y": 0.7693729996681213}, {"X": 0.33407917618751526, "Y": 0.7692495584487915}, {"X": 0.33396145701408386, "Y": 0.7802613973617554}, {"X": 0.28967636823654175, "Y": 0.7804044485092163}]}, "Id": "4dff8da5-9f5b-4415-8f7b-fe3f738736a1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Member", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037755146622657776, "Height": 0.009691226296126842, "Left": 0.33932191133499146, "Top": 0.7708356976509094}, "Polygon": [{"X": 0.33942148089408875, "Y": 0.7709433436393738}, {"X": 0.37707704305648804, "Y": 0.7708356976509094}, {"X": 0.37699779868125916, "Y": 0.7804048657417297}, {"X": 0.33932191133499146, "Y": 0.7805269360542297}]}, "Id": "9be4055f-7d87-4236-aa9b-0c6d7add85bd"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024099132046103477, "Height": 0.009969380684196949, "Left": 0.3833608329296112, "Top": 0.7705959677696228}, "Polygon": [{"X": 0.3834393322467804, "Y": 0.7706644535064697}, {"X": 0.40745997428894043, "Y": 0.7705959677696228}, {"X": 0.4073948562145233, "Y": 0.7804872989654541}, {"X": 0.3833608329296112, "Y": 0.7805653214454651}]}, "Id": "7e25585a-9384-4f7f-a906-0c9adb2f20ed"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "<PERSON>ll", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023639274761080742, "Height": 0.010227356106042862, "Left": 0.4152342975139618, "Top": 0.7688994407653809}, "Polygon": [{"X": 0.415296733379364, "Y": 0.7689651250839233}, {"X": 0.4388735890388489, "Y": 0.7688994407653809}, {"X": 0.4388245940208435, "Y": 0.7790514230728149}, {"X": 0.4152342975139618, "Y": 0.779126763343811}]}, "Id": "e4f206a5-a864-40bd-acd0-482fa42aa7ba"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Points", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03783285245299339, "Height": 0.009685929864645004, "Left": 0.29081717133522034, "Top": 0.7803966403007507}, "Polygon": [{"X": 0.2909424304962158, "Y": 0.7805185914039612}, {"X": 0.32865002751350403, "Y": 0.7803966403007507}, {"X": 0.32854512333869934, "Y": 0.7899461984634399}, {"X": 0.29081717133522034, "Y": 0.790082573890686}]}, "Id": "dc4d6e30-68d5-4126-b4b6-dc05e5724a80"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Earned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03778614103794098, "Height": 0.009925417602062225, "Left": 0.33426329493522644, "Top": 0.7817692160606384}, "Polygon": [{"X": 0.3343678414821625, "Y": 0.7818933725357056}, {"X": 0.3720494508743286, "Y": 0.7817692160606384}, {"X": 0.3719656765460968, "Y": 0.7915557026863098}, {"X": 0.33426329493522644, "Y": 0.7916946411132812}]}, "Id": "b18b0690-bb98-42ff-a6de-62e23546885f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "134", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01794712245464325, "Height": 0.009592688642442226, "Left": 0.5100551247596741, "Top": 0.7826313376426697}, "Polygon": [{"X": 0.510063111782074, "Y": 0.7826914191246033}, {"X": 0.5280006527900696, "Y": 0.7826313376426697}, {"X": 0.5280022025108337, "Y": 0.7921570539474487}, {"X": 0.5100551247596741, "Y": 0.7922240495681763}]}, "Id": "ebb8d4cf-6c7e-4c72-a104-eade6ff6d778"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Points", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03739241510629654, "Height": 0.009215693920850754, "Left": 0.2922050654888153, "Top": 0.7911401391029358}, "Polygon": [{"X": 0.29232320189476013, "Y": 0.7912766933441162}, {"X": 0.32959747314453125, "Y": 0.7911401391029358}, {"X": 0.32949841022491455, "Y": 0.8002057671546936}, {"X": 0.2922050654888153, "Y": 0.800355851650238}]}, "Id": "def120c5-64da-4b22-9e3e-6413e6c54798"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Redeemed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050182461738586426, "Height": 0.009947970509529114, "Left": 0.3353321850299835, "Top": 0.7927488684654236}, "Polygon": [{"X": 0.3354356586933136, "Y": 0.7929359078407288}, {"X": 0.38551464676856995, "Y": 0.7927488684654236}, {"X": 0.3854386508464813, "Y": 0.802490234375}, {"X": 0.3353321850299835, "Y": 0.8026968836784363}]}, "Id": "a9ff7cad-14d5-4229-8ad6-bd38dc745591"}, {"BlockType": "WORD", "Confidence": 98.51702117919922, "Text": "0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006245152093470097, "Height": 0.00869249552488327, "Left": 0.5217739343643188, "Top": 0.7948458790779114}, "Polygon": [{"X": 0.5217755436897278, "Y": 0.7948698401451111}, {"X": 0.5280176997184753, "Y": 0.7948458790779114}, {"X": 0.52801913022995, "Y": 0.8035122156143188}, {"X": 0.5217739343643188, "Y": 0.8035383820533752}]}, "Id": "2e8d64f2-ba20-45ee-9fc6-7093d78b70b0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Points", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03720004856586456, "Height": 0.010658725164830685, "Left": 0.29306760430336, "Top": 0.8019660711288452}, "Polygon": [{"X": 0.2932037115097046, "Y": 0.8021178245544434}, {"X": 0.33026763796806335, "Y": 0.8019660711288452}, {"X": 0.3301534652709961, "Y": 0.8124574422836304}, {"X": 0.29306760430336, "Y": 0.8126248121261597}]}, "Id": "ca026593-d90a-4a31-986b-caaf53f9d656"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Balance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043283846229314804, "Height": 0.011034182272851467, "Left": 0.3362548351287842, "Top": 0.8023962378501892}, "Polygon": [{"X": 0.33636927604675293, "Y": 0.802574098110199}, {"X": 0.3795386850833893, "Y": 0.8023962378501892}, {"X": 0.37945055961608887, "Y": 0.8132337927818298}, {"X": 0.3362548351287842, "Y": 0.8134304285049438}]}, "Id": "8a3952fe-6a8f-4711-89cd-15d20cca59eb"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "150", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01813640259206295, "Height": 0.010115102864801884, "Left": 0.5099960565567017, "Top": 0.8060806393623352}, "Polygon": [{"X": 0.****************, "Y": 0.8061584234237671}, {"X": 0.****************, "Y": 0.8060806393623352}, {"X": 0.****************, "Y": 0.8161106109619141}, {"X": 0.5099960565567017, "Y": 0.8161957263946533}]}, "Id": "51c4a49c-db31-4376-ad23-4c9553eeecd3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Drink", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031635578721761703, "Height": 0.011760111898183823, "Left": 0.2921358644962311, "Top": 0.8146811127662659}, "Polygon": [{"X": 0.29228681325912476, "Y": 0.8148259520530701}, {"X": 0.3237714469432831, "Y": 0.8146811127662659}, {"X": 0.3236410915851593, "Y": 0.826281726360321}, {"X": 0.2921358644962311, "Y": 0.82644122838974}]}, "Id": "4bacee5e-b87e-4f66-8473-980f46acbf2a"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Refill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036419566720724106, "Height": 0.011883284896612167, "Left": 0.32919400930404663, "Top": 0.8154942393302917}, "Polygon": [{"X": 0.3293220102787018, "Y": 0.815662682056427}, {"X": 0.36561357975006104, "Y": 0.8154942393302917}, {"X": 0.36550942063331604, "Y": 0.8271920680999756}, {"X": 0.32919400930404663, "Y": 0.8273775577545166}]}, "Id": "e3ae4bfd-962f-4b4e-9f20-7bf779ca276d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Credits", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04341369494795799, "Height": 0.01206067856401205, "Left": 0.37247952818870544, "Top": 0.8167204260826111}, "Polygon": [{"X": 0.37258046865463257, "Y": 0.816923975944519}, {"X": 0.*****************, "Y": 0.8167204260826111}, {"X": 0.****************, "Y": 0.8285569548606873}, {"X": 0.37247952818870544, "Y": 0.8287811279296875}]}, "Id": "baca320d-639a-4eca-a90a-03018bf0750e"}, {"BlockType": "WORD", "Confidence": 99.24645233154297, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0051827216520905495, "Height": 0.010582732036709785, "Left": 0.522331714630127, "Top": 0.8201528191566467}, "Polygon": [{"X": 0.****************, "Y": 0.8201779723167419}, {"X": 0.****************, "Y": 0.8201528191566467}, {"X": 0.****************, "Y": 0.8307081460952759}, {"X": 0.522331714630127, "Y": 0.8307355046272278}]}, "Id": "cb57647a-8bdc-417e-ac55-0693840ac5d9"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03721095249056816, "Height": 0.010282017290592194, "Left": 0.29209885001182556, "Top": 0.8284235596656799}, "Polygon": [{"X": 0.29222992062568665, "Y": 0.8286144733428955}, {"X": 0.3293098211288452, "Y": 0.8284235596656799}, {"X": 0.32919979095458984, "Y": 0.8384996652603149}, {"X": 0.29209885001182556, "Y": 0.8387055993080139}]}, "Id": "5afb7ea5-f132-4973-ad93-e252ae236981"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "Credits", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043688930571079254, "Height": 0.009569814428687096, "Left": 0.3351455330848694, "Top": 0.8295893669128418}, "Polygon": [{"X": 0.33524447679519653, "Y": 0.8298163414001465}, {"X": 0.37883448600769043, "Y": 0.8295893669128418}, {"X": 0.37875840067863464, "Y": 0.8389158844947815}, {"X": 0.3351455330848694, "Y": 0.8391591906547546}]}, "Id": "981d3654-08f5-44d1-b58c-ef90ac3a278c"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "02ff22ed-b743-4c1c-966f-fac0be7d4966", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "02ff22ed-b743-4c1c-966f-fac0be7d4966", "content-type": "application/x-amz-json-1.1", "content-length": "68825", "date": "Wed, 24 Sep 2025 12:47:07 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "cc352944-d54b-4595-b4aa-586e50c74994", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:11 GMT", "content-type": "application/json", "content-length": "1687", "connection": "keep-alive", "x-amzn-requestid": "cc352944-d54b-4595-b4aa-586e50c74994"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. We need to analyze content. The text shows:\n\n- Company: AMERILUX TRANSPORTATION\n- INVOICE# 07717\n- details: \"TripNumber 2 VehicleID 282663 HubOdometer 200029\"\n- \"Received: Fleet One OTR 520.61\"\n- \"Pump: 18 Gallons: 133.868 Price/ Gal: 3.889 Subtotal 520.61 Sales Tax 0.00 Total 520.61\"\n- \"Loves #395\"\n- \"Type: SALE (ORIGINAL)\"\n- \"Qty Name Price Total\" \n- \"DIESEL 520.61\" maybe a line item.\n\nThis appears to be a fuel receipt, as it's about diesel, gallons, price per gallon. However, it also has INVOICE# 07717 and TripNumber, VehicleID, etc. But the main focus is fuel purchase. The keywords: Fuel, Diesel, Pump, Gallons, Price/ Gal. It also says \"Fuel Receipt\" implicitly? The page has \"Received: Fleet One OTR\" maybe indicates receipt. Could be \"fuel_receipt\". The guidelines say: fuel_receipt: Receipt for fuel purchase. Keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. Yes.\n\nThus doc_type likely \"fuel_receipt\".\n\nWe should check if it's a continuation. No. Just page1. So output: page 1 with doc_type fuel_receipt.\n\nWe need to output via tool call classify_logistics_doc_type. The input requires array of documents objects each with page_no and doc_type. So:\n\n{\n \"documents\": [\n  { \"page_no\": 1, \"doc_type\": \"fuel_receipt\" }\n ]\n}\n\nThus call."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2736, "outputTokens": 394, "totalTokens": 3130}, "metrics": {"latencyMs": 1856}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/52567568_BBWPIXK6JXYXQJO2PEFR.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nLoves.\nLoves #395\n3407 South State Road 45/52\nKankakee, IL 60901\n(815) 929-1549\n07/16/2025 Tkt #99479847\nType: SALE\n(ORIGINAL)\nQty Name\nPrice\nTotal\nDIESEL\n520.61\nPump:\n18\nGallons:\n133.868\nPrice / Gal:\n3.889\nSubtotal\n520.61\nSales Tax\n0.00\nTotal\n520.61\nReceived:\nFleet One OTR\n520.61\n0149 SWIPED\nAuth No: 114525\nCompany: AMERILUX TRANSPORTATION\nINVOICE# 07717\nTripNumber\n2\nVehicleID\n282663\nHubOdometer\n200029\nMy Love Rewards\nLoyalty Member <PERSON>\nPoints Earned\n134\nPoints Redeemed\n0\nPoints Balance\n150\nDrink Refill Credits\n1\nShower Credits\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}