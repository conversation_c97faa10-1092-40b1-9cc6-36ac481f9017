2025-09-24 00:25:43,557 - INFO - Logging initialized. Log file: logs/test_classification_20250924_002543.log
2025-09-24 00:25:43,557 - INFO - 📁 Found 9 files to process
2025-09-24 00:25:43,557 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:25:43,557 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 00:25:43,557 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 00:25:43,558 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 00:25:43,558 - INFO - ⬆️ [00:25:43] Uploading: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:45,198 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf -> s3://document-extraction-logistically/temp/ff255dd2_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:45,198 - INFO - 🔍 [00:25:45] Starting classification: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:45,199 - INFO - ⬆️ [00:25:45] Uploading: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:45,199 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:45,212 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:45,224 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ff255dd2_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:45,224 - INFO - Processing PDF from S3...
2025-09-24 00:25:45,224 - INFO - Downloading PDF from S3 to /tmp/tmpvwfa79si/ff255dd2_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:46,442 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf -> s3://document-extraction-logistically/temp/bcca0129_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:46,442 - INFO - 🔍 [00:25:46] Starting classification: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:46,443 - INFO - ⬆️ [00:25:46] Uploading: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:25:46,445 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:46,467 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:46,471 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bcca0129_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:46,471 - INFO - Processing PDF from S3...
2025-09-24 00:25:46,471 - INFO - Downloading PDF from S3 to /tmp/tmpvp1if2iy/bcca0129_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:46,725 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:25:46,726 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:46,729 - INFO - Splitting PDF ff255dd2_CZ7K9JE7PH88JUBC19JF into 1 pages
2025-09-24 00:25:46,771 - INFO - Split PDF into 1 pages
2025-09-24 00:25:46,771 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:46,771 - INFO - Expected pages: [1]
2025-09-24 00:25:47,481 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf -> s3://document-extraction-logistically/temp/00d9b38a_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:25:47,481 - INFO - 🔍 [00:25:47] Starting classification: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:25:47,482 - INFO - ⬆️ [00:25:47] Uploading: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:47,485 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:47,504 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:47,508 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/00d9b38a_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:25:47,508 - INFO - Processing PDF from S3...
2025-09-24 00:25:47,508 - INFO - Downloading PDF from S3 to /tmp/tmph0t1m3dw/00d9b38a_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:25:48,112 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf -> s3://document-extraction-logistically/temp/736b4021_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:48,113 - INFO - 🔍 [00:25:48] Starting classification: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:48,113 - INFO - ⬆️ [00:25:48] Uploading: GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:48,114 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:48,127 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:48,131 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/736b4021_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:48,131 - INFO - Processing PDF from S3...
2025-09-24 00:25:48,131 - INFO - Downloading PDF from S3 to /tmp/tmpyizfxgsf/736b4021_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:48,425 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:25:48,426 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:48,427 - INFO - Splitting PDF bcca0129_DGAKPGYVH59IXR7KRKZS into 4 pages
2025-09-24 00:25:48,439 - INFO - Split PDF into 4 pages
2025-09-24 00:25:48,440 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:48,440 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 00:25:48,701 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf -> s3://document-extraction-logistically/temp/23779907_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:48,701 - INFO - 🔍 [00:25:48] Starting classification: GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:48,702 - INFO - ⬆️ [00:25:48] Uploading: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:48,703 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:48,717 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:48,720 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/23779907_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:48,720 - INFO - Processing PDF from S3...
2025-09-24 00:25:48,721 - INFO - Downloading PDF from S3 to /tmp/tmppfa5cy__/23779907_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:49,325 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf -> s3://document-extraction-logistically/temp/2ceb3d2d_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:49,325 - INFO - 🔍 [00:25:49] Starting classification: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:49,326 - INFO - ⬆️ [00:25:49] Uploading: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:25:49,328 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:49,345 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:49,352 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2ceb3d2d_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:49,352 - INFO - Processing PDF from S3...
2025-09-24 00:25:49,352 - INFO - Downloading PDF from S3 to /tmp/tmpzy2ump41/2ceb3d2d_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:49,934 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf -> s3://document-extraction-logistically/temp/0de49547_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:25:49,934 - INFO - 🔍 [00:25:49] Starting classification: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:25:49,938 - INFO - ⬆️ [00:25:49] Uploading: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:25:49,939 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:49,956 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:49,960 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0de49547_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:25:49,960 - INFO - Processing PDF from S3...
2025-09-24 00:25:49,960 - INFO - Downloading PDF from S3 to /tmp/tmpi6l0cnvt/0de49547_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:25:50,181 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:25:50,182 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:50,184 - INFO - Splitting PDF 23779907_GMCKX2ERTX05S300COLL into 3 pages
2025-09-24 00:25:50,194 - INFO - Split PDF into 3 pages
2025-09-24 00:25:50,195 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:50,195 - INFO - Expected pages: [1, 2, 3]
2025-09-24 00:25:50,469 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 00:25:50,469 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:50,471 - INFO - Splitting PDF 736b4021_EDYM381JJDTUB87YAAPI into 1 pages
2025-09-24 00:25:50,479 - INFO - Split PDF into 1 pages
2025-09-24 00:25:50,479 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:50,479 - INFO - Expected pages: [1]
2025-09-24 00:25:50,540 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf -> s3://document-extraction-logistically/temp/5486e617_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:25:50,540 - INFO - 🔍 [00:25:50] Starting classification: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:25:50,540 - INFO - ⬆️ [00:25:50] Uploading: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:25:50,540 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:50,549 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:50,551 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5486e617_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:25:50,551 - INFO - Processing PDF from S3...
2025-09-24 00:25:50,551 - INFO - Downloading PDF from S3 to /tmp/tmp05v1p9dj/5486e617_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:25:51,066 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:25:51,067 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:51,069 - INFO - Splitting PDF 2ceb3d2d_T51X0UC3WJL168AL2PGB into 1 pages
2025-09-24 00:25:51,078 - INFO - Split PDF into 1 pages
2025-09-24 00:25:51,079 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:51,079 - INFO - Expected pages: [1]
2025-09-24 00:25:51,098 - INFO - Page 1: Extracted 1384 characters, 71 lines from ff255dd2_CZ7K9JE7PH88JUBC19JF_3e74b51c_page_001.pdf
2025-09-24 00:25:51,099 - INFO - Successfully processed page 1
2025-09-24 00:25:51,099 - INFO - Combined 1 pages into final text
2025-09-24 00:25:51,099 - INFO - Text validation for ff255dd2_CZ7K9JE7PH88JUBC19JF: 1401 characters, 1 pages
2025-09-24 00:25:51,100 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:51,100 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:25:51,270 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf -> s3://document-extraction-logistically/temp/3c17273b_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:25:51,271 - INFO - 🔍 [00:25:51] Starting classification: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:25:51,272 - INFO - Initializing TextractProcessor...
2025-09-24 00:25:51,284 - INFO - Initializing BedrockProcessor...
2025-09-24 00:25:51,289 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3c17273b_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:25:51,289 - INFO - Processing PDF from S3...
2025-09-24 00:25:51,289 - INFO - Downloading PDF from S3 to /tmp/tmp83vwla2d/3c17273b_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:25:52,104 - INFO - Page 3: Extracted 541 characters, 39 lines from bcca0129_DGAKPGYVH59IXR7KRKZS_bc152b84_page_003.pdf
2025-09-24 00:25:52,104 - INFO - Successfully processed page 3
2025-09-24 00:25:52,315 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 00:25:52,316 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:52,317 - INFO - Splitting PDF 0de49547_TCM8BE9P052RZLZGHQIW into 1 pages
2025-09-24 00:25:52,319 - INFO - Split PDF into 1 pages
2025-09-24 00:25:52,319 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:52,319 - INFO - Expected pages: [1]
2025-09-24 00:25:52,384 - INFO - Page 2: Extracted 579 characters, 42 lines from bcca0129_DGAKPGYVH59IXR7KRKZS_bc152b84_page_002.pdf
2025-09-24 00:25:52,385 - INFO - Successfully processed page 2
2025-09-24 00:25:52,900 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ff255dd2_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:52,926 - INFO - 

CZ7K9JE7PH88JUBC19JF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:25:52,926 - INFO - 

✓ Saved result: output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:25:53,064 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:25:53,064 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:53,065 - INFO - Splitting PDF 5486e617_V44T1WF2N7RT33JSFU5F into 1 pages
2025-09-24 00:25:53,068 - INFO - Split PDF into 1 pages
2025-09-24 00:25:53,068 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:53,069 - INFO - Expected pages: [1]
2025-09-24 00:25:53,218 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ff255dd2_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:25:53,727 - INFO - Page 4: Extracted 3255 characters, 170 lines from bcca0129_DGAKPGYVH59IXR7KRKZS_bc152b84_page_004.pdf
2025-09-24 00:25:53,728 - INFO - Successfully processed page 4
2025-09-24 00:25:54,176 - INFO - Page 1: Extracted 2957 characters, 137 lines from bcca0129_DGAKPGYVH59IXR7KRKZS_bc152b84_page_001.pdf
2025-09-24 00:25:54,177 - INFO - Successfully processed page 1
2025-09-24 00:25:54,177 - INFO - Combined 4 pages into final text
2025-09-24 00:25:54,178 - INFO - Text validation for bcca0129_DGAKPGYVH59IXR7KRKZS: 7406 characters, 4 pages
2025-09-24 00:25:54,178 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:54,178 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:25:54,467 - INFO - Page 1: Extracted 1271 characters, 100 lines from 23779907_GMCKX2ERTX05S300COLL_59ad8033_page_001.pdf
2025-09-24 00:25:54,472 - INFO - Page 3: Extracted 526 characters, 49 lines from 23779907_GMCKX2ERTX05S300COLL_59ad8033_page_003.pdf
2025-09-24 00:25:54,472 - INFO - Successfully processed page 1
2025-09-24 00:25:54,472 - INFO - Successfully processed page 3
2025-09-24 00:25:54,603 - INFO - Page 2: Extracted 1411 characters, 134 lines from 23779907_GMCKX2ERTX05S300COLL_59ad8033_page_002.pdf
2025-09-24 00:25:54,603 - INFO - Successfully processed page 2
2025-09-24 00:25:54,604 - INFO - Combined 3 pages into final text
2025-09-24 00:25:54,604 - INFO - Text validation for 23779907_GMCKX2ERTX05S300COLL: 3263 characters, 3 pages
2025-09-24 00:25:54,604 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:54,604 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:25:54,682 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 00:25:54,682 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:54,684 - INFO - Splitting PDF 00d9b38a_ECY73YCNA7IPQSM8NAKW into 1 pages
2025-09-24 00:25:54,685 - INFO - Split PDF into 1 pages
2025-09-24 00:25:54,686 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:54,686 - INFO - Expected pages: [1]
2025-09-24 00:25:55,623 - INFO - Page 1: Extracted 1163 characters, 75 lines from 736b4021_EDYM381JJDTUB87YAAPI_65b73437_page_001.pdf
2025-09-24 00:25:55,623 - INFO - Successfully processed page 1
2025-09-24 00:25:55,624 - INFO - Combined 1 pages into final text
2025-09-24 00:25:55,624 - INFO - Text validation for 736b4021_EDYM381JJDTUB87YAAPI: 1180 characters, 1 pages
2025-09-24 00:25:55,624 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:55,624 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:25:56,350 - INFO - Page 1: Extracted 2010 characters, 210 lines from 2ceb3d2d_T51X0UC3WJL168AL2PGB_40bb805d_page_001.pdf
2025-09-24 00:25:56,350 - INFO - Successfully processed page 1
2025-09-24 00:25:56,350 - INFO - Combined 1 pages into final text
2025-09-24 00:25:56,351 - INFO - Text validation for 2ceb3d2d_T51X0UC3WJL168AL2PGB: 2027 characters, 1 pages
2025-09-24 00:25:56,351 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:56,351 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:25:56,701 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/23779907_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:56,739 - INFO - 

GMCKX2ERTX05S300COLL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 00:25:56,739 - INFO - 

✓ Saved result: output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:25:57,027 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/23779907_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:25:57,447 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/736b4021_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:57,481 - INFO - 

EDYM381JJDTUB87YAAPI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:25:57,482 - INFO - 

✓ Saved result: output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:25:57,773 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/736b4021_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:25:58,230 - INFO - Page 1: Extracted 1341 characters, 86 lines from 0de49547_TCM8BE9P052RZLZGHQIW_31824522_page_001.pdf
2025-09-24 00:25:58,230 - INFO - Successfully processed page 1
2025-09-24 00:25:58,230 - INFO - Combined 1 pages into final text
2025-09-24 00:25:58,230 - INFO - Text validation for 0de49547_TCM8BE9P052RZLZGHQIW: 1358 characters, 1 pages
2025-09-24 00:25:58,230 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:58,230 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:25:58,439 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2ceb3d2d_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:58,447 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bcca0129_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:58,501 - INFO - 

T51X0UC3WJL168AL2PGB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:25:58,501 - INFO - 

✓ Saved result: output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:25:58,791 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2ceb3d2d_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:25:58,859 - INFO - 

DGAKPGYVH59IXR7KRKZS.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 4,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 00:25:58,859 - INFO - 

✓ Saved result: output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:25:59,152 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bcca0129_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:25:59,323 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 00:25:59,323 - INFO - Splitting PDF into individual pages...
2025-09-24 00:25:59,325 - INFO - Splitting PDF 3c17273b_Z106V9IKGLMUAGC3VOK8 into 2 pages
2025-09-24 00:25:59,330 - INFO - Split PDF into 2 pages
2025-09-24 00:25:59,330 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:25:59,331 - INFO - Expected pages: [1, 2]
2025-09-24 00:25:59,749 - INFO - Page 1: Extracted 4288 characters, 110 lines from 5486e617_V44T1WF2N7RT33JSFU5F_5e78e739_page_001.pdf
2025-09-24 00:25:59,750 - INFO - Successfully processed page 1
2025-09-24 00:25:59,750 - INFO - Combined 1 pages into final text
2025-09-24 00:25:59,750 - INFO - Text validation for 5486e617_V44T1WF2N7RT33JSFU5F: 4305 characters, 1 pages
2025-09-24 00:25:59,751 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:25:59,751 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:26:00,188 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0de49547_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:26:00,205 - INFO - 

TCM8BE9P052RZLZGHQIW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:26:00,205 - INFO - 

✓ Saved result: output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:26:00,495 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0de49547_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:26:00,694 - INFO - Page 1: Extracted 1545 characters, 72 lines from 00d9b38a_ECY73YCNA7IPQSM8NAKW_974bf723_page_001.pdf
2025-09-24 00:26:00,694 - INFO - Successfully processed page 1
2025-09-24 00:26:00,695 - INFO - Combined 1 pages into final text
2025-09-24 00:26:00,695 - INFO - Text validation for 00d9b38a_ECY73YCNA7IPQSM8NAKW: 1562 characters, 1 pages
2025-09-24 00:26:00,695 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:26:00,695 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:26:03,194 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5486e617_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:26:03,226 - INFO - 

V44T1WF2N7RT33JSFU5F.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 00:26:03,226 - INFO - 

✓ Saved result: output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:26:03,424 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/00d9b38a_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:26:03,557 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5486e617_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:26:03,581 - INFO - 

ECY73YCNA7IPQSM8NAKW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:26:03,581 - INFO - 

✓ Saved result: output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:26:03,873 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/00d9b38a_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:26:04,400 - INFO - Page 2: Extracted 727 characters, 48 lines from 3c17273b_Z106V9IKGLMUAGC3VOK8_474b99e9_page_002.pdf
2025-09-24 00:26:04,400 - INFO - Successfully processed page 2
2025-09-24 00:26:06,137 - INFO - Page 1: Extracted 1789 characters, 98 lines from 3c17273b_Z106V9IKGLMUAGC3VOK8_474b99e9_page_001.pdf
2025-09-24 00:26:06,137 - INFO - Successfully processed page 1
2025-09-24 00:26:06,137 - INFO - Combined 2 pages into final text
2025-09-24 00:26:06,138 - INFO - Text validation for 3c17273b_Z106V9IKGLMUAGC3VOK8: 2552 characters, 2 pages
2025-09-24 00:26:06,138 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:26:06,138 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:26:09,786 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3c17273b_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:26:09,822 - INFO - 

Z106V9IKGLMUAGC3VOK8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:26:09,822 - INFO - 

✓ Saved result: output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:26:11,345 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3c17273b_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:26:11,347 - INFO - 
📊 Processing Summary:
2025-09-24 00:26:11,347 - INFO -    Total files: 9
2025-09-24 00:26:11,347 - INFO -    Successful: 9
2025-09-24 00:26:11,347 - INFO -    Failed: 0
2025-09-24 00:26:11,347 - INFO -    Duration: 27.79 seconds
2025-09-24 00:26:11,347 - INFO -    Output directory: output
2025-09-24 00:26:11,347 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:26:11,348 - INFO -    📄 CZ7K9JE7PH88JUBC19JF.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 DGAKPGYVH59IXR7KRKZS.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"comm_invoice"},{"page_no":4,"doc_type":"customs_doc"}]}
2025-09-24 00:26:11,348 - INFO -    📄 ECY73YCNA7IPQSM8NAKW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 EDYM381JJDTUB87YAAPI.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 GMCKX2ERTX05S300COLL.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 T51X0UC3WJL168AL2PGB.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 TCM8BE9P052RZLZGHQIW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 V44T1WF2N7RT33JSFU5F.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 00:26:11,348 - INFO -    📄 Z106V9IKGLMUAGC3VOK8.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"}]}
2025-09-24 00:26:11,349 - INFO - 
============================================================================================================================================
2025-09-24 00:26:11,349 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:26:11,349 - INFO - ============================================================================================================================================
2025-09-24 00:26:11,349 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:26:11,350 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:26:11,350 - INFO - CZ7K9JE7PH88JUBC19JF.pdf                           1      comm_invoice         run1_CZ7K9JE7PH88JUBC19JF.json                    
2025-09-24 00:26:11,350 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:26:11,350 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:26:11,350 - INFO - 
2025-09-24 00:26:11,350 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           1      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:26:11,350 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:26:11,350 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,350 - INFO - 
2025-09-24 00:26:11,350 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           2      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:26:11,350 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:26:11,351 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,351 - INFO - 
2025-09-24 00:26:11,351 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           3      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:26:11,351 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:26:11,351 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,351 - INFO - 
2025-09-24 00:26:11,351 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           4      customs_doc          run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:26:11,351 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:26:11,351 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,351 - INFO - 
2025-09-24 00:26:11,351 - INFO - ECY73YCNA7IPQSM8NAKW.pdf                           1      comm_invoice         run1_ECY73YCNA7IPQSM8NAKW.json                    
2025-09-24 00:26:11,351 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:26:11,351 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:26:11,352 - INFO - 
2025-09-24 00:26:11,352 - INFO - EDYM381JJDTUB87YAAPI.pdf                           1      comm_invoice         run1_EDYM381JJDTUB87YAAPI.json                    
2025-09-24 00:26:11,352 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:26:11,352 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:26:11,352 - INFO - 
2025-09-24 00:26:11,352 - INFO - GMCKX2ERTX05S300COLL.pdf                           1      invoice              run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:26:11,352 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:26:11,352 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:26:11,352 - INFO - 
2025-09-24 00:26:11,352 - INFO - GMCKX2ERTX05S300COLL.pdf                           2      invoice              run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:26:11,352 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:26:11,352 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:26:11,353 - INFO - 
2025-09-24 00:26:11,353 - INFO - GMCKX2ERTX05S300COLL.pdf                           3      invoice              run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:26:11,353 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:26:11,353 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:26:11,353 - INFO - 
2025-09-24 00:26:11,353 - INFO - T51X0UC3WJL168AL2PGB.pdf                           1      comm_invoice         run1_T51X0UC3WJL168AL2PGB.json                    
2025-09-24 00:26:11,353 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:26:11,353 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:26:11,353 - INFO - 
2025-09-24 00:26:11,353 - INFO - TCM8BE9P052RZLZGHQIW.pdf                           1      comm_invoice         run1_TCM8BE9P052RZLZGHQIW.json                    
2025-09-24 00:26:11,353 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:26:11,353 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:26:11,353 - INFO - 
2025-09-24 00:26:11,353 - INFO - V44T1WF2N7RT33JSFU5F.pdf                           1      invoice              run1_V44T1WF2N7RT33JSFU5F.json                    
2025-09-24 00:26:11,354 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:26:11,354 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:26:11,354 - INFO - 
2025-09-24 00:26:11,354 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           1      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 00:26:11,354 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:26:11,354 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:26:11,354 - INFO - 
2025-09-24 00:26:11,354 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           2      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 00:26:11,354 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:26:11,354 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:26:11,354 - INFO - 
2025-09-24 00:26:11,354 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:26:11,354 - INFO - Total entries: 15
2025-09-24 00:26:11,355 - INFO - ============================================================================================================================================
2025-09-24 00:26:11,355 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:26:11,355 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:26:11,355 - INFO -   1. CZ7K9JE7PH88JUBC19JF.pdf            Page 1   → comm_invoice    | run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:26:11,355 - INFO -   2. DGAKPGYVH59IXR7KRKZS.pdf            Page 1   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,355 - INFO -   3. DGAKPGYVH59IXR7KRKZS.pdf            Page 2   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,355 - INFO -   4. DGAKPGYVH59IXR7KRKZS.pdf            Page 3   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,355 - INFO -   5. DGAKPGYVH59IXR7KRKZS.pdf            Page 4   → customs_doc     | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:26:11,355 - INFO -   6. ECY73YCNA7IPQSM8NAKW.pdf            Page 1   → comm_invoice    | run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:26:11,355 - INFO -   7. EDYM381JJDTUB87YAAPI.pdf            Page 1   → comm_invoice    | run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:26:11,356 - INFO -   8. GMCKX2ERTX05S300COLL.pdf            Page 1   → invoice         | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:26:11,356 - INFO -   9. GMCKX2ERTX05S300COLL.pdf            Page 2   → invoice         | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:26:11,356 - INFO -  10. GMCKX2ERTX05S300COLL.pdf            Page 3   → invoice         | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:26:11,356 - INFO -  11. T51X0UC3WJL168AL2PGB.pdf            Page 1   → comm_invoice    | run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:26:11,356 - INFO -  12. TCM8BE9P052RZLZGHQIW.pdf            Page 1   → comm_invoice    | run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:26:11,356 - INFO -  13. V44T1WF2N7RT33JSFU5F.pdf            Page 1   → invoice         | run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:26:11,356 - INFO -  14. Z106V9IKGLMUAGC3VOK8.pdf            Page 1   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:26:11,356 - INFO -  15. Z106V9IKGLMUAGC3VOK8.pdf            Page 2   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:26:11,356 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:26:11,357 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 27.789336, 'processed_files': [{'filename': 'CZ7K9JE7PH88JUBC19JF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf'}, {'filename': 'DGAKPGYVH59IXR7KRKZS.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'comm_invoice'}, {'page_no': 4, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf'}, {'filename': 'ECY73YCNA7IPQSM8NAKW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf'}, {'filename': 'EDYM381JJDTUB87YAAPI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf'}, {'filename': 'GMCKX2ERTX05S300COLL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf'}, {'filename': 'T51X0UC3WJL168AL2PGB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf'}, {'filename': 'TCM8BE9P052RZLZGHQIW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf'}, {'filename': 'V44T1WF2N7RT33JSFU5F.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf'}, {'filename': 'Z106V9IKGLMUAGC3VOK8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf'}]}
