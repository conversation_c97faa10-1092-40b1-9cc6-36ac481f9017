2025-09-24 19:08:11,699 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190811.log
2025-09-24 19:08:11,699 - INFO - 📁 Found 2 files to process
2025-09-24 19:08:11,699 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:08:11,699 - INFO - 🚀 Processing 2 files in FORCED PARALLEL MODE...
2025-09-24 19:08:11,699 - INFO - 🚀 Creating 2 parallel tasks...
2025-09-24 19:08:11,699 - INFO - 🚀 All 2 tasks created - executing in parallel...
2025-09-24 19:08:11,699 - INFO - ⬆️ [19:08:11] Uploading: 5_clear_to_pay_1.pdf
2025-09-24 19:08:14,430 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_1.pdf -> s3://document-extraction-logistically/temp/8dc28e36_5_clear_to_pay_1.pdf
2025-09-24 19:08:14,430 - INFO - 🔍 [19:08:14] Starting classification: 5_clear_to_pay_1.pdf
2025-09-24 19:08:14,431 - INFO - ⬆️ [19:08:14] Uploading: 5_clear_to_pay_2.pdf
2025-09-24 19:08:14,431 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:14,455 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:14,463 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8dc28e36_5_clear_to_pay_1.pdf
2025-09-24 19:08:14,464 - INFO - Processing PDF from S3...
2025-09-24 19:08:14,464 - INFO - Downloading PDF from S3 to /tmp/tmp10mhyt4y/8dc28e36_5_clear_to_pay_1.pdf
2025-09-24 19:08:15,147 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_2.pdf -> s3://document-extraction-logistically/temp/003b4389_5_clear_to_pay_2.pdf
2025-09-24 19:08:15,148 - INFO - 🔍 [19:08:15] Starting classification: 5_clear_to_pay_2.pdf
2025-09-24 19:08:15,149 - INFO - Initializing TextractProcessor...
2025-09-24 19:08:15,160 - INFO - Initializing BedrockProcessor...
2025-09-24 19:08:15,164 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/003b4389_5_clear_to_pay_2.pdf
2025-09-24 19:08:15,164 - INFO - Processing PDF from S3...
2025-09-24 19:08:15,164 - INFO - Downloading PDF from S3 to /tmp/tmpovqngbcp/003b4389_5_clear_to_pay_2.pdf
2025-09-24 19:08:16,851 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 19:08:16,851 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:16,852 - INFO - Splitting PDF 8dc28e36_5_clear_to_pay_1 into 1 pages
2025-09-24 19:08:16,858 - INFO - Split PDF into 1 pages
2025-09-24 19:08:16,859 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:16,859 - INFO - Expected pages: [1]
2025-09-24 19:08:17,001 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:08:17,001 - INFO - Splitting PDF into individual pages...
2025-09-24 19:08:17,003 - INFO - Splitting PDF 003b4389_5_clear_to_pay_2 into 1 pages
2025-09-24 19:08:17,004 - INFO - Split PDF into 1 pages
2025-09-24 19:08:17,005 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:08:17,005 - INFO - Expected pages: [1]
2025-09-24 19:08:21,317 - INFO - Page 1: Extracted 721 characters, 35 lines from 003b4389_5_clear_to_pay_2_81306663_page_001.pdf
2025-09-24 19:08:21,317 - INFO - Successfully processed page 1
2025-09-24 19:08:21,318 - INFO - Combined 1 pages into final text
2025-09-24 19:08:21,318 - INFO - Text validation for 003b4389_5_clear_to_pay_2: 738 characters, 1 pages
2025-09-24 19:08:21,318 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:21,318 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:22,323 - INFO - Page 1: Extracted 749 characters, 42 lines from 8dc28e36_5_clear_to_pay_1_3bc427fd_page_001.pdf
2025-09-24 19:08:22,324 - INFO - Successfully processed page 1
2025-09-24 19:08:22,324 - INFO - Combined 1 pages into final text
2025-09-24 19:08:22,325 - INFO - Text validation for 8dc28e36_5_clear_to_pay_1: 766 characters, 1 pages
2025-09-24 19:08:22,325 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:08:22,326 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:08:23,438 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f13a4133-87d9-4b93-b5fe-b11fd140e525', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:23 GMT', 'content-type': 'application/json', 'content-length': '740', 'connection': 'keep-alive', 'x-amzn-requestid': 'f13a4133-87d9-4b93-b5fe-b11fd140e525'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The content: "Truckload Check-in Form", "Clear-to-Pay Voucher", "Load # 20081", "Broker", etc. This seems a Clear-to-Pay Voucher, which is a document type "clear_to_pay". The definition: Approval for payment or ACH/Wire/Check transfer request or approval. Contains "Clear-to-Pay". So doc_type should be "clear_to_pay". There\'s no continuation. So output page 1 as clear_to_pay.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2827, 'outputTokens': 123, 'totalTokens': 2950}, 'metrics': {'latencyMs': 741}}
2025-09-24 19:08:23,439 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/003b4389_5_clear_to_pay_2.pdf
2025-09-24 19:08:23,460 - INFO - 

5_clear_to_pay_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 19:08:23,460 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_2.json
2025-09-24 19:08:24,361 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9ef5e095-2d2e-4e02-b7ad-9144652f6874', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:38:24 GMT', 'content-type': 'application/json', 'content-length': '699', 'connection': 'keep-alive', 'x-amzn-requestid': '9ef5e095-2d2e-4e02-b7ad-9144652f6874'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It says "Truckload Check-in Form", "Clear-to-Pay Voucher". Many cost items. So likely "clear_to_pay" because it contains "Clear-to-Pay Voucher" and approval. So doc_type: clear_to_pay.\n\nCheck if any other types could fit: invoice? but it\'s a voucher. Not a freight bill. So clear_to_pay.\n\nReturn JSON with documents array page 1: clear_to_pay.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2848, 'outputTokens': 111, 'totalTokens': 2959}, 'metrics': {'latencyMs': 720}}
2025-09-24 19:08:24,361 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8dc28e36_5_clear_to_pay_1.pdf
2025-09-24 19:08:24,509 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/003b4389_5_clear_to_pay_2.pdf
2025-09-24 19:08:24,522 - INFO - 

5_clear_to_pay_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-24 19:08:24,522 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_1.json
2025-09-24 19:08:25,077 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8dc28e36_5_clear_to_pay_1.pdf
2025-09-24 19:08:25,078 - INFO - 
📊 Processing Summary:
2025-09-24 19:08:25,078 - INFO -    Total files: 2
2025-09-24 19:08:25,078 - INFO -    Successful: 2
2025-09-24 19:08:25,078 - INFO -    Failed: 0
2025-09-24 19:08:25,078 - INFO -    Duration: 13.38 seconds
2025-09-24 19:08:25,078 - INFO -    Output directory: output
2025-09-24 19:08:25,079 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:08:25,079 - INFO -    📄 5_clear_to_pay_1.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 19:08:25,079 - INFO -    📄 5_clear_to_pay_2.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-24 19:08:25,079 - INFO - 
============================================================================================================================================
2025-09-24 19:08:25,079 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:08:25,079 - INFO - ============================================================================================================================================
2025-09-24 19:08:25,079 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:08:25,079 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:08:25,079 - INFO - 5_clear_to_pay_1.pdf                               1      clear_to_pay                             run1_5_clear_to_pay_1.json                                                      
2025-09-24 19:08:25,079 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_1.pdf
2025-09-24 19:08:25,079 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_5_clear_to_pay_1.json
2025-09-24 19:08:25,080 - INFO - 
2025-09-24 19:08:25,080 - INFO - 5_clear_to_pay_2.pdf                               1      clear_to_pay                             run1_5_clear_to_pay_2.json                                                      
2025-09-24 19:08:25,080 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_2.pdf
2025-09-24 19:08:25,080 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_5_clear_to_pay_2.json
2025-09-24 19:08:25,080 - INFO - 
2025-09-24 19:08:25,080 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:08:25,080 - INFO - Total entries: 2
2025-09-24 19:08:25,080 - INFO - ============================================================================================================================================
2025-09-24 19:08:25,080 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:08:25,080 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:08:25,080 - INFO -   1. 5_clear_to_pay_1.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_1.json
2025-09-24 19:08:25,080 - INFO -   2. 5_clear_to_pay_2.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_2.json
2025-09-24 19:08:25,080 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:08:25,080 - INFO - 
✅ Test completed: {'total_files': 2, 'processed': 2, 'failed': 0, 'errors': [], 'duration_seconds': 13.378985, 'processed_files': [{'filename': '5_clear_to_pay_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_1.pdf'}, {'filename': '5_clear_to_pay_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_2.pdf'}]}
