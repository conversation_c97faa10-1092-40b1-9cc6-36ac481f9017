2025-09-24 17:53:15,954 - INFO - Logging initialized. Log file: logs/test_classification_20250924_175315.log
2025-09-24 17:53:15,954 - INFO - 📁 Found 7 files to process
2025-09-24 17:53:15,954 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 17:53:15,954 - INFO - 🚀 Processing 7 files in FORCED PARALLEL MODE...
2025-09-24 17:53:15,954 - INFO - 🚀 Creating 7 parallel tasks...
2025-09-24 17:53:15,954 - INFO - 🚀 All 7 tasks created - executing in parallel...
2025-09-24 17:53:15,954 - INFO - ⬆️ [17:53:15] Uploading: A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:53:19,441 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/A7CS2V2FOYJ4C84TQCE2.pdf -> s3://document-extraction-logistically/temp/811ccae6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:53:19,442 - INFO - 🔍 [17:53:19] Starting classification: A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:53:19,443 - INFO - ⬆️ [17:53:19] Uploading: DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:53:19,446 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:19,470 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:19,482 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/811ccae6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:53:19,482 - INFO - Processing PDF from S3...
2025-09-24 17:53:19,482 - INFO - Downloading PDF from S3 to /tmp/tmp5kfjpsju/811ccae6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:53:20,333 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/DZSJ7XNECMHN04RDNRIA.pdf -> s3://document-extraction-logistically/temp/23797809_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:53:20,334 - INFO - 🔍 [17:53:20] Starting classification: DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:53:20,335 - INFO - ⬆️ [17:53:20] Uploading: EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:20,336 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:20,365 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:20,369 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/23797809_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:53:20,370 - INFO - Processing PDF from S3...
2025-09-24 17:53:20,370 - INFO - Downloading PDF from S3 to /tmp/tmp7oiaid4o/23797809_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:53:21,038 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/EOGW8CVHOLX2BALX5AA5.pdf -> s3://document-extraction-logistically/temp/29c97c57_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:21,039 - INFO - 🔍 [17:53:21] Starting classification: EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:21,039 - INFO - ⬆️ [17:53:21] Uploading: FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:21,040 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:21,070 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:21,076 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/29c97c57_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:21,076 - INFO - Processing PDF from S3...
2025-09-24 17:53:21,077 - INFO - Downloading PDF from S3 to /tmp/tmpptm5pldv/29c97c57_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:21,663 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/FSH16W3D0EIW0C8PSEF9.pdf -> s3://document-extraction-logistically/temp/58e5bd6b_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:21,664 - INFO - 🔍 [17:53:21] Starting classification: FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:21,664 - INFO - ⬆️ [17:53:21] Uploading: I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:21,666 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:21,690 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:21,695 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/58e5bd6b_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:21,695 - INFO - Processing PDF from S3...
2025-09-24 17:53:21,695 - INFO - Downloading PDF from S3 to /tmp/tmpy6j_dgo4/58e5bd6b_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:22,265 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/I3XU9KPI7B1QVKBAHOYB.pdf -> s3://document-extraction-logistically/temp/873476ae_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:22,266 - INFO - 🔍 [17:53:22] Starting classification: I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:22,267 - INFO - ⬆️ [17:53:22] Uploading: YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:22,274 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:22,292 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:22,295 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/873476ae_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:22,295 - INFO - Processing PDF from S3...
2025-09-24 17:53:22,295 - INFO - Downloading PDF from S3 to /tmp/tmpiv591rsy/873476ae_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:22,401 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:53:22,401 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:22,404 - INFO - Splitting PDF 811ccae6_A7CS2V2FOYJ4C84TQCE2 into 3 pages
2025-09-24 17:53:22,426 - INFO - Split PDF into 3 pages
2025-09-24 17:53:22,426 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:22,426 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:53:22,791 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 17:53:22,791 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:22,794 - INFO - Splitting PDF 23797809_DZSJ7XNECMHN04RDNRIA into 2 pages
2025-09-24 17:53:22,824 - INFO - Split PDF into 2 pages
2025-09-24 17:53:22,824 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:22,824 - INFO - Expected pages: [1, 2]
2025-09-24 17:53:22,888 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/YUC3NIJMZ2FWJZR6IN67.pdf -> s3://document-extraction-logistically/temp/b84b04f4_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:22,888 - INFO - 🔍 [17:53:22] Starting classification: YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:22,889 - INFO - ⬆️ [17:53:22] Uploading: ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:53:22,889 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:22,899 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:22,901 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b84b04f4_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:22,901 - INFO - Processing PDF from S3...
2025-09-24 17:53:22,901 - INFO - Downloading PDF from S3 to /tmp/tmphf2bx4qz/b84b04f4_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:23,114 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:53:23,114 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:23,116 - INFO - Splitting PDF 29c97c57_EOGW8CVHOLX2BALX5AA5 into 3 pages
2025-09-24 17:53:23,154 - INFO - Split PDF into 3 pages
2025-09-24 17:53:23,154 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:23,154 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:53:23,506 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/rate_confirmation/ZXVPNXXEV01AJD108C3M.pdf -> s3://document-extraction-logistically/temp/60396423_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:53:23,506 - INFO - 🔍 [17:53:23] Starting classification: ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:53:23,507 - INFO - Initializing TextractProcessor...
2025-09-24 17:53:23,517 - INFO - Initializing BedrockProcessor...
2025-09-24 17:53:23,519 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/60396423_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:53:23,519 - INFO - Processing PDF from S3...
2025-09-24 17:53:23,519 - INFO - Downloading PDF from S3 to /tmp/tmpjyrtmnji/60396423_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:53:23,879 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:53:23,879 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:23,879 - INFO - Splitting PDF 873476ae_I3XU9KPI7B1QVKBAHOYB into 2 pages
2025-09-24 17:53:23,890 - INFO - Split PDF into 2 pages
2025-09-24 17:53:23,895 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:23,896 - INFO - Expected pages: [1, 2]
2025-09-24 17:53:23,908 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:53:23,908 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:23,911 - INFO - Splitting PDF 58e5bd6b_FSH16W3D0EIW0C8PSEF9 into 2 pages
2025-09-24 17:53:23,931 - INFO - Split PDF into 2 pages
2025-09-24 17:53:23,931 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:23,931 - INFO - Expected pages: [1, 2]
2025-09-24 17:53:25,273 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:53:25,274 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:25,275 - INFO - Splitting PDF b84b04f4_YUC3NIJMZ2FWJZR6IN67 into 2 pages
2025-09-24 17:53:25,284 - INFO - Split PDF into 2 pages
2025-09-24 17:53:25,284 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:25,284 - INFO - Expected pages: [1, 2]
2025-09-24 17:53:25,631 - INFO - Page 3: Extracted 16 characters, 2 lines from 811ccae6_A7CS2V2FOYJ4C84TQCE2_680e1010_page_003.pdf
2025-09-24 17:53:25,631 - INFO - Successfully processed page 3
2025-09-24 17:53:25,835 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 17:53:25,836 - INFO - Splitting PDF into individual pages...
2025-09-24 17:53:25,839 - INFO - Splitting PDF 60396423_ZXVPNXXEV01AJD108C3M into 3 pages
2025-09-24 17:53:25,857 - INFO - Split PDF into 3 pages
2025-09-24 17:53:25,857 - INFO - Processing pages with Textract in parallel...
2025-09-24 17:53:25,857 - INFO - Expected pages: [1, 2, 3]
2025-09-24 17:53:27,689 - INFO - Page 2: Extracted 650 characters, 16 lines from 873476ae_I3XU9KPI7B1QVKBAHOYB_e480821d_page_002.pdf
2025-09-24 17:53:27,690 - INFO - Successfully processed page 2
2025-09-24 17:53:27,842 - INFO - Page 2: Extracted 275 characters, 10 lines from 58e5bd6b_FSH16W3D0EIW0C8PSEF9_bbb72058_page_002.pdf
2025-09-24 17:53:27,843 - INFO - Successfully processed page 2
2025-09-24 17:53:28,102 - INFO - Page 1: Extracted 1548 characters, 74 lines from 29c97c57_EOGW8CVHOLX2BALX5AA5_3029a8d8_page_001.pdf
2025-09-24 17:53:28,103 - INFO - Successfully processed page 1
2025-09-24 17:53:28,228 - INFO - Page 3: Extracted 1638 characters, 27 lines from 29c97c57_EOGW8CVHOLX2BALX5AA5_3029a8d8_page_003.pdf
2025-09-24 17:53:28,229 - INFO - Successfully processed page 3
2025-09-24 17:53:28,543 - INFO - Page 1: Extracted 1514 characters, 74 lines from 873476ae_I3XU9KPI7B1QVKBAHOYB_e480821d_page_001.pdf
2025-09-24 17:53:28,544 - INFO - Successfully processed page 1
2025-09-24 17:53:28,544 - INFO - Combined 2 pages into final text
2025-09-24 17:53:28,544 - INFO - Text validation for 873476ae_I3XU9KPI7B1QVKBAHOYB: 2200 characters, 2 pages
2025-09-24 17:53:28,544 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:28,545 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:28,831 - INFO - Page 2: Extracted 2296 characters, 58 lines from 23797809_DZSJ7XNECMHN04RDNRIA_5a38be9f_page_002.pdf
2025-09-24 17:53:28,831 - INFO - Successfully processed page 2
2025-09-24 17:53:28,892 - INFO - Page 2: Extracted 5315 characters, 51 lines from 29c97c57_EOGW8CVHOLX2BALX5AA5_3029a8d8_page_002.pdf
2025-09-24 17:53:28,892 - INFO - Successfully processed page 2
2025-09-24 17:53:28,893 - INFO - Combined 3 pages into final text
2025-09-24 17:53:28,893 - INFO - Text validation for 29c97c57_EOGW8CVHOLX2BALX5AA5: 8556 characters, 3 pages
2025-09-24 17:53:28,893 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:28,893 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:29,057 - INFO - Page 2: Extracted 3517 characters, 68 lines from 811ccae6_A7CS2V2FOYJ4C84TQCE2_680e1010_page_002.pdf
2025-09-24 17:53:29,057 - INFO - Successfully processed page 2
2025-09-24 17:53:29,318 - INFO - Page 2: Extracted 656 characters, 16 lines from b84b04f4_YUC3NIJMZ2FWJZR6IN67_2c31ff18_page_002.pdf
2025-09-24 17:53:29,318 - INFO - Successfully processed page 2
2025-09-24 17:53:29,397 - INFO - Page 1: Extracted 3618 characters, 74 lines from 23797809_DZSJ7XNECMHN04RDNRIA_5a38be9f_page_001.pdf
2025-09-24 17:53:29,397 - INFO - Successfully processed page 1
2025-09-24 17:53:29,397 - INFO - Combined 2 pages into final text
2025-09-24 17:53:29,397 - INFO - Text validation for 23797809_DZSJ7XNECMHN04RDNRIA: 5950 characters, 2 pages
2025-09-24 17:53:29,398 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:29,398 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:29,408 - INFO - Page 1: Extracted 1491 characters, 73 lines from 58e5bd6b_FSH16W3D0EIW0C8PSEF9_bbb72058_page_001.pdf
2025-09-24 17:53:29,409 - INFO - Successfully processed page 1
2025-09-24 17:53:29,409 - INFO - Combined 2 pages into final text
2025-09-24 17:53:29,409 - INFO - Text validation for 58e5bd6b_FSH16W3D0EIW0C8PSEF9: 1802 characters, 2 pages
2025-09-24 17:53:29,409 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:29,410 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:29,664 - INFO - Page 1: Extracted 1589 characters, 76 lines from b84b04f4_YUC3NIJMZ2FWJZR6IN67_2c31ff18_page_001.pdf
2025-09-24 17:53:29,664 - INFO - Successfully processed page 1
2025-09-24 17:53:29,665 - INFO - Combined 2 pages into final text
2025-09-24 17:53:29,665 - INFO - Text validation for b84b04f4_YUC3NIJMZ2FWJZR6IN67: 2281 characters, 2 pages
2025-09-24 17:53:29,665 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:29,665 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:29,862 - INFO - Page 2: Extracted 1101 characters, 43 lines from 60396423_ZXVPNXXEV01AJD108C3M_c051787c_page_002.pdf
2025-09-24 17:53:29,862 - INFO - Successfully processed page 2
2025-09-24 17:53:30,270 - INFO - Page 1: Extracted 3373 characters, 74 lines from 811ccae6_A7CS2V2FOYJ4C84TQCE2_680e1010_page_001.pdf
2025-09-24 17:53:30,270 - INFO - Successfully processed page 1
2025-09-24 17:53:30,270 - INFO - Combined 3 pages into final text
2025-09-24 17:53:30,271 - INFO - Text validation for 811ccae6_A7CS2V2FOYJ4C84TQCE2: 6961 characters, 3 pages
2025-09-24 17:53:30,271 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:30,271 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:30,470 - INFO - Page 3: Extracted 639 characters, 18 lines from 60396423_ZXVPNXXEV01AJD108C3M_c051787c_page_003.pdf
2025-09-24 17:53:30,471 - INFO - Successfully processed page 3
2025-09-24 17:53:30,614 - INFO - Page 1: Extracted 1786 characters, 73 lines from 60396423_ZXVPNXXEV01AJD108C3M_c051787c_page_001.pdf
2025-09-24 17:53:30,614 - INFO - Successfully processed page 1
2025-09-24 17:53:30,615 - INFO - Combined 3 pages into final text
2025-09-24 17:53:30,615 - INFO - Text validation for 60396423_ZXVPNXXEV01AJD108C3M: 3581 characters, 3 pages
2025-09-24 17:53:30,615 - INFO - Analyzing document types with Bedrock...
2025-09-24 17:53:30,615 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 17:53:31,184 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c2f6a608-88a1-4654-a848-188f0953aeed', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:31 GMT', 'content-type': 'application/json', 'content-length': '1501', 'connection': 'keep-alive', 'x-amzn-requestid': 'c2f6a608-88a1-4654-a848-188f0953aeed'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to determine doc_type for each page. The document appears to be "Shipping Instructions & Rate Confirmation". The keywords: "Rate Confirmation", "Shipping Instructions", "Bill To", "Carrier", "Total Amount". This seems like a rate confirmation document: "Carrier Rate Confirmation" or "Rate Confirmation". The doc_type enum includes "rate_confirmation" for "Load confirmation" or "Carrier Rate Confirmation", and "cust_rate_confirmation" for customer rate confirmation. This document is titled "Shipping Instructions & Rate Confirmation" and includes "Carrier" and "Order #". It\'s likely a "rate_confirmation". The page 1 and page 2 are part of the same document, so both pages should be classified as "rate_confirmation". No continuation markers? Page 1 says "Page 1 of 2". Page 2 says "Page 2 of 2". So page 2 is not marked as continuation, but it\'s a continuation of the multi-page doc. But the instructions say for each page we must return object describing that page. Each page gets doc_type. So page1: rate_confirmation. page2: rate_confirmation.\n\nThus output tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3185, 'outputTokens': 263, 'totalTokens': 3448}, 'metrics': {'latencyMs': 1371}}
2025-09-24 17:53:31,185 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/873476ae_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:31,208 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'abde624b-5410-40f2-8e1b-0adb349e3153', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:31 GMT', 'content-type': 'application/json', 'content-length': '1265', 'connection': 'keep-alive', 'x-amzn-requestid': 'abde624b-5410-40f2-8e1b-0adb349e3153'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 3 pages. Need to classify each page. The document is "Shipping Instructions & Rate Confirmation" containing rate confirmation and shipping instructions. What is the category? Likely "rate_confirmation". According to definitions: rate_confirmation is an agreement from a carrier confirming rate/terms for a specific load. This seems to be that. The content includes rate, terms, and shipping instructions. So all pages are part of same multi-page rate confirmation document.\n\nWe need to check if any page is a continuation: page 2 indicates "continued" lines? It mentions "Page 2 of 3". So page 2 is continuation of page 1. Page 3 is continuation of page 2. So all same doc_type "rate_confirmation".\n\nReturn array of objects: page_no, doc_type. Must use tool classify_logistics_doc_type.\n\nLet\'s produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4478, 'outputTokens': 216, 'totalTokens': 4694}, 'metrics': {'latencyMs': 1126}}
2025-09-24 17:53:31,208 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/29c97c57_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:31,238 - INFO - 

I3XU9KPI7B1QVKBAHOYB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:53:31,238 - INFO - 

✓ Saved result: output/run1_I3XU9KPI7B1QVKBAHOYB.json
2025-09-24 17:53:31,585 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6289eddc-f65c-4496-8222-f6801ade785c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:31 GMT', 'content-type': 'application/json', 'content-length': '805', 'connection': 'keep-alive', 'x-amzn-requestid': '6289eddc-f65c-4496-8222-f6801ade785c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be "Shipping Instructions & Rate Confirmation" which is a rate confirmation. The pages have "Rate Confirmation" header. So doc_type: rate_confirmation. Both page1 and page2. Need to check if page2 is continuation. It says "Page 2 of 2". So page2 is continuation. So both pages same type.\n\nReturn JSON array of objects: each with page_no and doc_type. Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3206, 'outputTokens': 126, 'totalTokens': 3332}, 'metrics': {'latencyMs': 756}}
2025-09-24 17:53:31,585 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b84b04f4_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:31,697 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a3c03448-97a8-47aa-af33-f9b4e405c3ab', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:31 GMT', 'content-type': 'application/json', 'content-length': '1435', 'connection': 'keep-alive', 'x-amzn-requestid': 'a3c03448-97a8-47aa-af33-f9b4e405c3ab'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages of a document. It appears to be a "Shipping Instructions & Rate Confirmation". This is likely a rate confirmation document - possibly "rate_confirmation". The keywords: "Rate Confirmation" appears in header. It\'s not an invoice, just a rate confirmation. The keyword "Shipping Instructions & Rate Confirmation" indicates a rate confirmation document. This falls under rate_confirmation. There\'s no mention of "Customer Rate Confirmation" vs "Carrier Rate Confirmation" but typical "Rate Confirmation" is from carrier to customer. That would be "rate_confirmation" as per definitions. So we classify both pages as rate_confirmation.\n\nCheck for continuation: page2 starts with "Payment: Line Haul: $449.00" etc. It says "Page 2 of 2". The content is continuation of page1. But we still must classify each page separately, but same type. So output two objects.\n\nReturn via tool call classify_logistics_doc_type with documents array containing objects with page_no and doc_type.\n\nLet\'s produce JSON array accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3086, 'outputTokens': 236, 'totalTokens': 3322}, 'metrics': {'latencyMs': 1152}}
2025-09-24 17:53:31,697 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/58e5bd6b_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:31,997 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd939a064-0b8a-410f-99cc-9295658de005', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:31 GMT', 'content-type': 'application/json', 'content-length': '1657', 'connection': 'keep-alive', 'x-amzn-requestid': 'd939a064-0b8a-410f-99cc-9295658de005'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 2 pages. The content appears to be a "Load Confirmation" from EchoDrive. It\'s a type of rate confirmation? Let\'s see definitions.\n\nrate_confirmation: "Carrier Rate Confirmation" agreement from carrier confirming rate/terms for a specific load. Keywords: "Carrier Rate Confirmation", "Rate Confirmation", "Carrier:". This page includes "LOAD CONFIRMATION" header, not "Rate Confirmation", but similar.\n\nAlso there\'s "LOAD CONFIRMATION" with details, rate, terms. That seems like a "Load Confirmation" which is basically a rate confirmation. So likely doc_type: rate_confirmation.\n\nCheck other types: "cust_rate_confirmation" is Customer Rate Confirmation. This is from Echo (broker) to carrier. It\'s a broker load tender confirmation. But doc type "tender_from_cust" is load tender from customer. But this page is load confirmation from broker to carrier, not to customer. So maybe still rate_confirmation.\n\nAlso "combined_carrier_documents" is page containing multiple documents; but this page appears to be a single document. So not combined.\n\nThus assign both pages as rate_confirmation.\n\nNeed to output the tool call with array of objects, each with page_no and doc_type.\n\nOrder ascending: page 1, page 2.\n\nReturn JSON via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4025, 'outputTokens': 290, 'totalTokens': 4315}, 'metrics': {'latencyMs': 1394}}
2025-09-24 17:53:31,997 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/23797809_DZSJ7XNECMHN04RDNRIA.pdf
2025-09-24 17:53:32,211 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/873476ae_I3XU9KPI7B1QVKBAHOYB.pdf
2025-09-24 17:53:32,281 - INFO - 

EOGW8CVHOLX2BALX5AA5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:53:32,281 - INFO - 

✓ Saved result: output/run1_EOGW8CVHOLX2BALX5AA5.json
2025-09-24 17:53:32,616 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/29c97c57_EOGW8CVHOLX2BALX5AA5.pdf
2025-09-24 17:53:32,658 - INFO - 

YUC3NIJMZ2FWJZR6IN67.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:53:32,658 - INFO - 

✓ Saved result: output/run1_YUC3NIJMZ2FWJZR6IN67.json
2025-09-24 17:53:32,919 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b1cdb3f4-1ac8-42d3-bc47-647adb7d208b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:32 GMT', 'content-type': 'application/json', 'content-length': '1006', 'connection': 'keep-alive', 'x-amzn-requestid': 'b1cdb3f4-1ac8-42d3-bc47-647adb7d208b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a 3-page document titled "Shipping Instructions & Rate Confirmation" with header, shipping instructions, rate confirmation. The content includes shipping instructions, pickup, delivery, rates. It\'s a rate confirmation document. So doc_type: rate_confirmation. Each page is part of same document. No continuation notes but likely part of same. According to instructions, each page must have doc_type. So page1, page2, page3 all rate_confirmation.\n\nReturn JSON array of objects with page_no and doc_type. Use tool classify_logistics_doc_type. Provide array of objects.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3860, 'outputTokens': 155, 'totalTokens': 4015}, 'metrics': {'latencyMs': 983}}
2025-09-24 17:53:32,919 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/60396423_ZXVPNXXEV01AJD108C3M.pdf
2025-09-24 17:53:32,946 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b84b04f4_YUC3NIJMZ2FWJZR6IN67.pdf
2025-09-24 17:53:32,977 - INFO - 

FSH16W3D0EIW0C8PSEF9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:53:32,977 - INFO - 

✓ Saved result: output/run1_FSH16W3D0EIW0C8PSEF9.json
2025-09-24 17:53:33,235 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7c646746-1b4e-42b6-a863-eab3acf02593', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:23:33 GMT', 'content-type': 'application/json', 'content-length': '2079', 'connection': 'keep-alive', 'x-amzn-requestid': '7c646746-1b4e-42b6-a863-eab3acf02593'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document is a Load Confirmation. Which category? The definitions: "rate_confirmation" is "Load confirmation" of Carrier Rate Confirmation. The description: "Agreement from a carrier confirming rate/terms for a specific load." The keywords: "Carrier Rate Confirmation", "Rate Confirmation". This document looks like a Load Confirmation, with rate summary, etc. It is a "Load Confirmation" which is a type of rate confirmation. The doc is basically a load confirmation (like a customer rate confirmation? Actually it\'s a load tender? Let\'s consider categories: "rate_confirmation", "cust_rate_confirmation", "tender_from_cust".\n\nThe text says "LOAD CONFIRMATION" at top. It\'s a load confirmation from Echo to carrier. So it\'s a rate confirmation directed to the carrier. The classification "rate_confirmation" is "Carrier Rate Confirmation". "cust_rate_confirmation" is "Customer Rate Confirmation" directed to the customer. This seems to be a load confirmation from broker to carrier. So "rate_confirmation" is appropriate.\n\nAlso, there is a field "LOAD CONFIRMATION" and "LOAD / UNLOAD / LUMPER RECEIPTS MUST ACCOMPANY INVOICING OR THEY WILL NOT BE PAID." This is a load confirmation. So classify as rate_confirmation.\n\nCheck if any other doc types: It\'s not invoice, not BOL, not POD. So rate_confirmation.\n\nPage 1, 2, 3 all same type. No continuation signals? page3 just signature. No "continued". So each page same type.\n\nThus we produce output: array of objects each with page_no and doc_type. Use tool call with objects array.\n\nWe must output only via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4285, 'outputTokens': 389, 'totalTokens': 4674}, 'metrics': {'latencyMs': 1768}}
2025-09-24 17:53:33,236 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/811ccae6_A7CS2V2FOYJ4C84TQCE2.pdf
2025-09-24 17:53:33,319 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/58e5bd6b_FSH16W3D0EIW0C8PSEF9.pdf
2025-09-24 17:53:33,371 - INFO - 

DZSJ7XNECMHN04RDNRIA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 17:53:33,371 - INFO - 

✓ Saved result: output/run1_DZSJ7XNECMHN04RDNRIA.json
