{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.5451193451881409, "Height": 0.7697938084602356, "Left": 0.23574282228946686, "Top": 0.0883314460515976}, "Polygon": [{"X": 0.23574282228946686, "Y": 0.1131523847579956}, {"X": 0.7143117785453796, "Y": 0.0883314460515976}, {"X": 0.7808622121810913, "Y": 0.8509402275085449}, {"X": 0.33418703079223633, "Y": 0.8581252694129944}]}, "Id": "6479030e-2184-4193-a5f0-08d2fa5e6069", "Relationships": [{"Type": "CHILD", "Ids": ["2486c72e-d8a8-4bd5-afb5-1eb5a9c2f66f", "12579a11-734f-4691-ac47-aa5eefb290ed", "a9655878-b99a-4cd5-b2e3-1da72fa26d19", "69644017-6ee2-44e5-aea8-15dde4e93ceb", "880a619f-109d-49ef-9b0a-cd6da46155ea", "431156e6-5be3-43f7-b4d8-75c1738cf423", "7e57a5a9-497f-41e1-947d-73f666cb8415", "70342f2f-4c50-4164-94dc-eecd884d8915", "2f0f9002-eedb-4487-943b-4344fa63e275", "a028c135-9841-4df0-85f1-146c5f533e0f", "b188a54e-67b6-4695-8c40-4173857c05ff", "55ca253e-3ed9-4a55-8d63-5a4d6be3c6a7", "d5752ff0-e2a8-402d-b08d-090517e6f266", "0a0ef5ee-513a-412d-8e09-f26febd4aebd", "d58c143d-b522-4e9f-8f76-7c8d7515881e", "6cf8e254-e9d6-4a6e-8281-63fbe2052871", "f8ac08f7-27a8-449b-9b64-72f53d188e89", "0e5f56b2-371c-43a0-9fe4-431c433368c0", "7f59b103-4f5d-471d-9d49-ded6b6331f7f", "b5939d2b-01a1-4ebd-b963-979ef2e45b41", "9d0985da-83f7-4a95-9413-8b2a5ca75b72", "73d9e203-ac17-4e43-b89a-66dd9b45c2be", "64dc51db-d268-4111-a973-3423be4d63d0", "34d8a6b6-8ba4-45a2-a39f-9d2dcc77b9a9", "a32355b2-1438-4bca-b5fa-ab0c5edbc9db", "815a3c3c-2f04-4eba-84af-8ed00056fd2c", "539a9680-8bd7-42ff-bcd5-920e57c82c20", "336b7a42-cd6c-486b-802e-4e46ec0ed467", "b7b58816-1070-44d5-9668-049d29fc69f5"]}]}, {"BlockType": "LINE", "Confidence": 99.9606704711914, "Text": "KWIK TRIP #674", "Geometry": {"BoundingBox": {"Width": 0.22412803769111633, "Height": 0.028686532750725746, "Left": 0.36450856924057007, "Top": 0.29189926385879517}, "Polygon": [{"X": 0.36450856924057007, "Y": 0.3013594150543213}, {"X": 0.5866711139678955, "Y": 0.29189926385879517}, {"X": 0.5886366367340088, "Y": 0.311346173286438}, {"X": 0.36685943603515625, "Y": 0.32058578729629517}]}, "Id": "2486c72e-d8a8-4bd5-afb5-1eb5a9c2f66f", "Relationships": [{"Type": "CHILD", "Ids": ["47b60276-93c2-4d05-bc1a-a522748627be", "14358980-8e79-4697-892d-3d3b79b810ad", "4a1cd40c-0b9e-4679-ae63-da33dbf88e5c"]}]}, {"BlockType": "LINE", "Confidence": 99.78900909423828, "Text": "6107 3M DR", "Geometry": {"BoundingBox": {"Width": 0.15971264243125916, "Height": 0.024565402418375015, "Left": 0.39893510937690735, "Top": 0.3180364966392517}, "Polygon": [{"X": 0.39893510937690735, "Y": 0.3245715796947479}, {"X": 0.5567542910575867, "Y": 0.3180364966392517}, {"X": 0.5586477518081665, "Y": 0.33621320128440857}, {"X": 0.4010854959487915, "Y": 0.3426018953323364}]}, "Id": "12579a11-734f-4691-ac47-aa5eefb290ed", "Relationships": [{"Type": "CHILD", "Ids": ["f8207b58-dabd-40d5-b471-959a54bcc661", "2658cb99-9edb-4186-ba8f-f4327787da70", "16369848-ae6f-49e9-bbc8-775dce5e6e05"]}]}, {"BlockType": "LINE", "Confidence": 98.7960433959961, "Text": "MENOMONIE, WI", "Geometry": {"BoundingBox": {"Width": 0.20903971791267395, "Height": 0.029010625556111336, "Left": 0.36814799904823303, "Top": 0.34074148535728455}, "Polygon": [{"X": 0.36814799904823303, "Y": 0.34907981753349304}, {"X": 0.5750430822372437, "Y": 0.34074148535728455}, {"X": 0.577187716960907, "Y": 0.36163368821144104}, {"X": 0.37067997455596924, "Y": 0.3697521388530731}]}, "Id": "a9655878-b99a-4cd5-b2e3-1da72fa26d19", "Relationships": [{"Type": "CHILD", "Ids": ["e0eadf10-0e23-43d8-ba07-99068937adb7", "4daefc1b-9f68-40ac-99ac-7155dd4373ca"]}]}, {"BlockType": "LINE", "Confidence": 99.26299285888672, "Text": "Invoice# 0091491", "Geometry": {"BoundingBox": {"Width": 0.2628609240055084, "Height": 0.029134349897503853, "Left": 0.3304470479488373, "Top": 0.38755884766578674}, "Polygon": [{"X": 0.3304470479488373, "Y": 0.39748942852020264}, {"X": 0.5913317203521729, "Y": 0.38755884766578674}, {"X": 0.5933079719543457, "Y": 0.407019704580307}, {"X": 0.33287951350212097, "Y": 0.41669318079948425}]}, "Id": "69644017-6ee2-44e5-aea8-15dde4e93ceb", "Relationships": [{"Type": "CHILD", "Ids": ["05847da6-9368-4dbc-ad76-34d6b0d2de95", "41b0abb5-bcc9-44fb-8f74-5ea72b6e8291"]}]}, {"BlockType": "LINE", "Confidence": 98.81576538085938, "Text": "Trans # 0900-1491", "Geometry": {"BoundingBox": {"Width": 0.2966269254684448, "Height": 0.030342483893036842, "Left": 0.32984963059425354, "Top": 0.40946972370147705}, "Polygon": [{"X": 0.32984963059425354, "Y": 0.42036548256874084}, {"X": 0.6245306134223938, "Y": 0.40946972370147705}, {"X": 0.626476526260376, "Y": 0.42921000719070435}, {"X": 0.33231937885284424, "Y": 0.43981221318244934}]}, "Id": "880a619f-109d-49ef-9b0a-cd6da46155ea", "Relationships": [{"Type": "CHILD", "Ids": ["fbf0049e-290d-44fd-a898-a5a5702cb596", "0049aed9-ab38-4306-9d0f-ae1a38cd304d", "89ab038f-0bbf-48e6-89cd-b4b7d9da70b5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.062143724411726, "Height": 0.016646860167384148, "Left": 0.33524245023727417, "Top": 0.4454403221607208}, "Polygon": [{"X": 0.33524245023727417, "Y": 0.4475940465927124}, {"X": 0.395627498626709, "Y": 0.4454403221607208}, {"X": 0.3973861634731293, "Y": 0.4599781632423401}, {"X": 0.3370804190635681, "Y": 0.4620871841907501}]}, "Id": "431156e6-5be3-43f7-b4d8-75c1738cf423", "Relationships": [{"Type": "CHILD", "Ids": ["d0e43970-205a-4645-a47d-c917c43ba5e2"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "10/16/2024", "Geometry": {"BoundingBox": {"Width": 0.1586758941411972, "Height": 0.02549244649708271, "Left": 0.4896986782550812, "Top": 0.43124064803123474}, "Polygon": [{"X": 0.4896986782550812, "Y": 0.43686971068382263}, {"X": 0.6464390158653259, "Y": 0.43124064803123474}, {"X": 0.6483745574951172, "Y": 0.4512619972229004}, {"X": 0.49191972613334656, "Y": 0.4567330777645111}]}, "Id": "7e57a5a9-497f-41e1-947d-73f666cb8415", "Relationships": [{"Type": "CHILD", "Ids": ["cd9c2cd8-b086-465a-bc7e-96c3c28f6fc6"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.06320150941610336, "Height": 0.01760995015501976, "Left": 0.33687490224838257, "Top": 0.4667598307132721}, "Polygon": [{"X": 0.33687490224838257, "Y": 0.46888452768325806}, {"X": 0.3981972634792328, "Y": 0.4667598307132721}, {"X": 0.40007638931274414, "Y": 0.4822934567928314}, {"X": 0.3388402462005615, "Y": 0.48436978459358215}]}, "Id": "70342f2f-4c50-4164-94dc-eecd884d8915", "Relationships": [{"Type": "CHILD", "Ids": ["4ded9c9d-af76-4f60-b313-fb6e5c62a13d"]}]}, {"BlockType": "LINE", "Confidence": 99.73593139648438, "Text": "05:24 PM", "Geometry": {"BoundingBox": {"Width": 0.1259315460920334, "Height": 0.020963819697499275, "Left": 0.49180230498313904, "Top": 0.4562765657901764}, "Polygon": [{"X": 0.49180230498313904, "Y": 0.4605991244316101}, {"X": 0.6160619258880615, "Y": 0.4562765657901764}, {"X": 0.6177338361740112, "Y": 0.47302258014678955}, {"X": 0.4936639368534088, "Y": 0.4772403836250305}]}, "Id": "2f0f9002-eedb-4487-943b-4344fa63e275", "Relationships": [{"Type": "CHILD", "Ids": ["2c3d6638-154d-4826-93af-a0dcba7fe787", "6e1db85c-bb8f-43bc-867e-1f09565392e5"]}]}, {"BlockType": "LINE", "Confidence": 99.88745880126953, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.3101479709148407, "Height": 0.032535258680582047, "Left": 0.34331533312797546, "Top": 0.49969905614852905}, "Polygon": [{"X": 0.34331533312797546, "Y": 0.5097650289535522}, {"X": 0.6512531042098999, "Y": 0.49969905614852905}, {"X": 0.6534633040428162, "Y": 0.5225197076797485}, {"X": 0.34616437554359436, "Y": 0.5322343111038208}]}, "Id": "a028c135-9841-4df0-85f1-146c5f533e0f", "Relationships": [{"Type": "CHILD", "Ids": ["6e58de82-b43e-4945-a449-afef4d024d0f", "2e197f0e-075e-4105-bff9-b623de4154ef", "22404185-0619-4306-9432-f5afbfaa0fe3"]}]}, {"BlockType": "LINE", "Confidence": 99.59243774414062, "Text": "03", "Geometry": {"BoundingBox": {"Width": 0.030798165127635002, "Height": 0.015194559469819069, "Left": 0.3620113134384155, "Top": 0.5346757769584656}, "Polygon": [{"X": 0.3620113134384155, "Y": 0.5355886816978455}, {"X": 0.39105790853500366, "Y": 0.5346757769584656}, {"X": 0.3928094804286957, "Y": 0.5489784479141235}, {"X": 0.3638007938861847, "Y": 0.5498703718185425}]}, "Id": "b188a54e-67b6-4695-8c40-4173857c05ff", "Relationships": [{"Type": "CHILD", "Ids": ["f547d6b8-2448-4304-bb9b-2cc4ebd6c4e6"]}]}, {"BlockType": "LINE", "Confidence": 99.7022476196289, "Text": "140.310 3.589", "Geometry": {"BoundingBox": {"Width": 0.21772027015686035, "Height": 0.02366919256746769, "Left": 0.4370604157447815, "Top": 0.5228736400604248}, "Polygon": [{"X": 0.4370604157447815, "Y": 0.529700756072998}, {"X": 0.6531311273574829, "Y": 0.5228736400604248}, {"X": 0.6547806859016418, "Y": 0.539899468421936}, {"X": 0.439046710729599, "Y": 0.5465428233146667}]}, "Id": "55ca253e-3ed9-4a55-8d63-5a4d6be3c6a7", "Relationships": [{"Type": "CHILD", "Ids": ["09e6343d-a980-4930-ac13-25cf74bbfb1a", "6aaaf9d3-66de-4683-99fb-6cd8693e8a24"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "03", "Geometry": {"BoundingBox": {"Width": 0.03068307600915432, "Height": 0.015253177843987942, "Left": 0.36476922035217285, "Top": 0.5564401149749756}, "Polygon": [{"X": 0.36476922035217285, "Y": 0.5573189854621887}, {"X": 0.3936893343925476, "Y": 0.5564401149749756}, {"X": 0.3954522907733917, "Y": 0.5708354115486145}, {"X": 0.36657023429870605, "Y": 0.5716933012008667}]}, "Id": "d5752ff0-e2a8-402d-b08d-090517e6f266", "Relationships": [{"Type": "CHILD", "Ids": ["2f88b4e7-6088-4b65-bd41-6287d29fb4c6"]}]}, {"BlockType": "LINE", "Confidence": 99.4785385131836, "Text": "9.166 4.189", "Geometry": {"BoundingBox": {"Width": 0.1860649287700653, "Height": 0.022075070068240166, "Left": 0.470461905002594, "Top": 0.5453230738639832}, "Polygon": [{"X": 0.470461905002594, "Y": 0.5509562492370605}, {"X": 0.6549184322357178, "Y": 0.5453230738639832}, {"X": 0.6565268635749817, "Y": 0.5619176030158997}, {"X": 0.4723515808582306, "Y": 0.567398190498352}]}, "Id": "0a0ef5ee-513a-412d-8e09-f26febd4aebd", "Relationships": [{"Type": "CHILD", "Ids": ["4d7aa2da-77ed-4afe-8527-d7b3b8364414", "2f065732-d0c9-4743-866c-0ca139da6e17"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.10547059774398804, "Height": 0.017530757933855057, "Left": 0.3550730347633362, "Top": 0.5979002714157104}, "Polygon": [{"X": 0.3550730347633362, "Y": 0.6008397936820984}, {"X": 0.4588334858417511, "Y": 0.5979002714157104}, {"X": 0.4605436325073242, "Y": 0.6125677227973938}, {"X": 0.35692286491394043, "Y": 0.615431010723114}]}, "Id": "d58c143d-b522-4e9f-8f76-7c8d7515881e", "Relationships": [{"Type": "CHILD", "Ids": ["1f9c5596-189c-42b8-97df-54f6fcf5283f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.09359259158372879, "Height": 0.01802532933652401, "Left": 0.5669374465942383, "Top": 0.5900750160217285}, "Polygon": [{"X": 0.5669374465942383, "Y": 0.5926933288574219}, {"X": 0.659029483795166, "Y": 0.5900750160217285}, {"X": 0.6605300307273865, "Y": 0.6055528521537781}, {"X": 0.5685701370239258, "Y": 0.6081003546714783}]}, "Id": "6cf8e254-e9d6-4a6e-8281-63fbe2052871", "Relationships": [{"Type": "CHILD", "Ids": ["65129e85-7525-4fb1-9dc4-a92de96b8c72"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.05959165096282959, "Height": 0.015447062440216541, "Left": 0.3579924404621124, "Top": 0.6210565567016602}, "Polygon": [{"X": 0.3579924404621124, "Y": 0.6226367354393005}, {"X": 0.4159003794193268, "Y": 0.6210565567016602}, {"X": 0.417584091424942, "Y": 0.6349638104438782}, {"X": 0.35975024104118347, "Y": 0.6365036368370056}]}, "Id": "f8ac08f7-27a8-449b-9b64-72f53d188e89", "Relationships": [{"Type": "CHILD", "Ids": ["ddc28b03-8002-4029-a800-398a90a43a42"]}]}, {"BlockType": "LINE", "Confidence": 99.20280456542969, "Text": "503.57", "Geometry": {"BoundingBox": {"Width": 0.0926872044801712, "Height": 0.01778591237962246, "Left": 0.5691189169883728, "Top": 0.6122534275054932}, "Polygon": [{"X": 0.5691189169883728, "Y": 0.6147511005401611}, {"X": 0.660315990447998, "Y": 0.6122534275054932}, {"X": 0.6618061065673828, "Y": 0.6276111006736755}, {"X": 0.5707392692565918, "Y": 0.6300393342971802}]}, "Id": "0e5f56b2-371c-43a0-9fe4-431c433368c0", "Relationships": [{"Type": "CHILD", "Ids": ["02cff960-93fb-41bd-ae7a-e517c2a74a55"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "DEFBULK", "Geometry": {"BoundingBox": {"Width": 0.1041448786854744, "Height": 0.01696399785578251, "Left": 0.3604995012283325, "Top": 0.6412212252616882}, "Polygon": [{"X": 0.3604995012283325, "Y": 0.643913209438324}, {"X": 0.462970495223999, "Y": 0.6412212252616882}, {"X": 0.4646443724632263, "Y": 0.6555666327476501}, {"X": 0.36230891942977905, "Y": 0.6581852436065674}]}, "Id": "7f59b103-4f5d-471d-9d49-ded6b6331f7f", "Relationships": [{"Type": "CHILD", "Ids": ["3d48b5c4-1cf4-478c-83ee-62ad5098ce7e"]}]}, {"BlockType": "LINE", "Confidence": 99.51191711425781, "Text": "38.40", "Geometry": {"BoundingBox": {"Width": 0.07695779949426651, "Height": 0.01672719419002533, "Left": 0.5866591334342957, "Top": 0.6345533728599548}, "Polygon": [{"X": 0.5866591334342957, "Y": 0.6365424990653992}, {"X": 0.6621810793876648, "Y": 0.6345533728599548}, {"X": 0.663616955280304, "Y": 0.6493467092514038}, {"X": 0.5881991386413574, "Y": 0.6512805819511414}]}, "Id": "b5939d2b-01a1-4ebd-b963-979ef2e45b41", "Relationships": [{"Type": "CHILD", "Ids": ["dfcb2211-1b3d-403d-baab-4e44c4128192"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.07541544735431671, "Height": 0.01585560292005539, "Left": 0.3640249967575073, "Top": 0.6849971413612366}, "Polygon": [{"X": 0.3640249967575073, "Y": 0.6867830753326416}, {"X": 0.4377504587173462, "Y": 0.6849971413612366}, {"X": 0.43944042921066284, "Y": 0.6991186141967773}, {"X": 0.36581140756607056, "Y": 0.7008527517318726}]}, "Id": "9d0985da-83f7-4a95-9413-8b2a5ca75b72", "Relationships": [{"Type": "CHILD", "Ids": ["ca246c08-f57e-4705-947d-74439c87b533"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "541.97", "Geometry": {"BoundingBox": {"Width": 0.09199905395507812, "Height": 0.017412381246685982, "Left": 0.5598547458648682, "Top": 0.6787101626396179}, "Polygon": [{"X": 0.5598547458648682, "Y": 0.6809070706367493}, {"X": 0.6503461003303528, "Y": 0.6787101626396179}, {"X": 0.6518537998199463, "Y": 0.6939938068389893}, {"X": 0.5614917874336243, "Y": 0.6961225271224976}]}, "Id": "73d9e203-ac17-4e43-b89a-66dd9b45c2be", "Relationships": [{"Type": "CHILD", "Ids": ["aa16bb7c-2d2a-480f-8eb0-3752b9888994"]}]}, {"BlockType": "LINE", "Confidence": 99.84919738769531, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.2852361798286438, "Height": 0.022898932918906212, "Left": 0.37003281712532043, "Top": 0.7221386432647705}, "Polygon": [{"X": 0.37003281712532043, "Y": 0.7284435033798218}, {"X": 0.****************, "Y": 0.7221386432647705}, {"X": 0.****************, "Y": 0.7389669418334961}, {"X": 0.3721385598182678, "Y": 0.7450376152992249}]}, "Id": "64dc51db-d268-4111-a973-3423be4d63d0", "Relationships": [{"Type": "CHILD", "Ids": ["3760fb11-913f-40ea-8e30-0089714326be", "ee5998ca-b386-4cdb-a848-17416a497700", "0945c69d-5bec-4d00-a018-11b3a776fdf5"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.13305813074111938, "Height": 0.017461387440562248, "Left": 0.3725605309009552, "Top": 0.7470425367355347}, "Polygon": [{"X": 0.3725605309009552, "Y": 0.7498294115066528}, {"X": 0.5039371252059937, "Y": 0.7470425367355347}, {"X": 0.505618691444397, "Y": 0.7618128657341003}, {"X": 0.37442296743392944, "Y": 0.7645039558410645}]}, "Id": "34d8a6b6-8ba4-45a2-a39f-9d2dcc77b9a9", "Relationships": [{"Type": "CHILD", "Ids": ["26c9febe-d91e-45f7-a6f0-024d9839ae04", "916f8214-7b43-4f27-89fa-16f598fce4cb"]}]}, {"BlockType": "LINE", "Confidence": 94.93084716796875, "Text": "1591", "Geometry": {"BoundingBox": {"Width": 0.054683275520801544, "Height": 0.014805714599788189, "Left": 0.5850005149841309, "Top": 0.7654070258140564}, "Polygon": [{"X": 0.5850005149841309, "Y": 0.7664844393730164}, {"X": 0.6382977366447449, "Y": 0.7654070258140564}, {"X": 0.6396837830543518, "Y": 0.7791712284088135}, {"X": 0.586455762386322, "Y": 0.7802127599716187}]}, "Id": "a32355b2-1438-4bca-b5fa-ab0c5edbc9db", "Relationships": [{"Type": "CHILD", "Ids": ["8221fcae-d9da-4d91-8dfa-7b95739005d7"]}]}, {"BlockType": "LINE", "Confidence": 99.82870483398438, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.08977527171373367, "Height": 0.015417153015732765, "Left": 0.3771515488624573, "Top": 0.7900159358978271}, "Polygon": [{"X": 0.3771515488624573, "Y": 0.7917096018791199}, {"X": 0.4652978777885437, "Y": 0.7900159358978271}, {"X": 0.46692681312561035, "Y": 0.8037992715835571}, {"X": 0.37889426946640015, "Y": 0.8054330945014954}]}, "Id": "815a3c3c-2f04-4eba-84af-8ed00056fd2c", "Relationships": [{"Type": "CHILD", "Ids": ["11b309e0-edc2-425f-a41e-3690ae1d9bd6", "a64c1656-32f8-4a7a-8ad8-4412ab911b68"]}]}, {"BlockType": "LINE", "Confidence": 99.73233795166016, "Text": "P32191", "Geometry": {"BoundingBox": {"Width": 0.08712770789861679, "Height": 0.015634816139936447, "Left": 0.5846709609031677, "Top": 0.7858286499977112}, "Polygon": [{"X": 0.5846709609031677, "Y": 0.7874773740768433}, {"X": 0.6704264879226685, "Y": 0.7858286499977112}, {"X": 0.6717987060546875, "Y": 0.7998735308647156}, {"X": 0.5861571431159973, "Y": 0.8014634847640991}]}, "Id": "539a9680-8bd7-42ff-bcd5-920e57c82c20", "Relationships": [{"Type": "CHILD", "Ids": ["3e8c0c90-7169-46c6-80de-3432e79793b5"]}]}, {"BlockType": "LINE", "Confidence": 99.5566635131836, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.26672065258026123, "Height": 0.019108595326542854, "Left": 0.38009411096572876, "Top": 0.8070172071456909}, "Polygon": [{"X": 0.38009411096572876, "Y": 0.8118590712547302}, {"X": 0.645363450050354, "Y": 0.8070172071456909}, {"X": 0.64681476354599, "Y": 0.8214707970619202}, {"X": 0.3819052577018738, "Y": 0.8261258006095886}]}, "Id": "336b7a42-cd6c-486b-802e-4e46ec0ed467", "Relationships": [{"Type": "CHILD", "Ids": ["7de7b3c6-9f3c-4d32-a9c8-af3f0d5aedb1", "1e98ba48-e5b8-4ce8-89e3-147f709c7e4d"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "AUTH REF#: P32191", "Geometry": {"BoundingBox": {"Width": 0.24828214943408966, "Height": 0.018057966604828835, "Left": 0.3964996635913849, "Top": 0.8278249502182007}, "Polygon": [{"X": 0.3964996635913849, "Y": 0.8320899605751038}, {"X": 0.6433743834495544, "Y": 0.8278249502182007}, {"X": 0.6447818279266357, "Y": 0.8417855501174927}, {"X": 0.3982315957546234, "Y": 0.8458828926086426}]}, "Id": "b7b58816-1070-44d5-9668-049d29fc69f5", "Relationships": [{"Type": "CHILD", "Ids": ["a4cd4eef-ab25-46d7-889c-69c689b94356", "c1e6a03a-1e0b-461c-9c0f-fe1ce7673e3a", "d0bfbb8c-cf30-4268-80d0-69191fd9fb21"]}]}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06386203318834305, "Height": 0.018546247854828835, "Left": 0.36491337418556213, "Top": 0.3020395338535309}, "Polygon": [{"X": 0.36491337418556213, "Y": 0.30466997623443604}, {"X": 0.4269176721572876, "Y": 0.3020395338535309}, {"X": 0.4287753999233246, "Y": 0.3180062472820282}, {"X": 0.36685943603515625, "Y": 0.32058578729629517}]}, "Id": "47b60276-93c2-4d05-bc1a-a522748627be"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06565983593463898, "Height": 0.018807195127010345, "Left": 0.4425946772098541, "Top": 0.29714518785476685}, "Polygon": [{"X": 0.4425946772098541, "Y": 0.2998608350753784}, {"X": 0.5064989924430847, "Y": 0.29714518785476685}, {"X": 0.5082545280456543, "Y": 0.3132896423339844}, {"X": 0.4444425702095032, "Y": 0.3159523904323578}]}, "Id": "14358980-8e79-4697-892d-3d3b79b810ad"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "#674", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06610865890979767, "Height": 0.01962912827730179, "Left": 0.5222748517990112, "Top": 0.29189926385879517}, "Polygon": [{"X": 0.5222748517990112, "Y": 0.294641375541687}, {"X": 0.5866711139678955, "Y": 0.29189926385879517}, {"X": 0.5883834958076477, "Y": 0.3088420033454895}, {"X": 0.5240853428840637, "Y": 0.31152838468551636}]}, "Id": "4a1cd40c-0b9e-4679-ae63-da33dbf88e5c"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "6107", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06359550356864929, "Height": 0.01879962906241417, "Left": 0.3991476893424988, "Top": 0.32380226254463196}, "Polygon": [{"X": 0.3991476893424988, "Y": 0.32635390758514404}, {"X": 0.4608955383300781, "Y": 0.32380226254463196}, {"X": 0.46274319291114807, "Y": 0.34010186791419983}, {"X": 0.4010854959487915, "Y": 0.3426018953323364}]}, "Id": "f8207b58-dabd-40d5-b471-959a54bcc661"}, {"BlockType": "WORD", "Confidence": 99.76243591308594, "Text": "3M", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032108504325151443, "Height": 0.017209062352776527, "Left": 0.47859182953834534, "Top": 0.3209352493286133}, "Polygon": [{"X": 0.47859182953834534, "Y": 0.3221915662288666}, {"X": 0.5089628100395203, "Y": 0.3209352493286133}, {"X": 0.5107003450393677, "Y": 0.3369128406047821}, {"X": 0.48037299513816833, "Y": 0.33814433217048645}]}, "Id": "2658cb99-9edb-4186-ba8f-f4327787da70"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03205502778291702, "Height": 0.016810450702905655, "Left": 0.5263216495513916, "Top": 0.3180364966392517}, "Polygon": [{"X": 0.5263216495513916, "Y": 0.319296658039093}, {"X": 0.5567542910575867, "Y": 0.3180364966392517}, {"X": 0.558376669883728, "Y": 0.33361098170280457}, {"X": 0.5279867649078369, "Y": 0.33484694361686707}]}, "Id": "16369848-ae6f-49e9-bbc8-775dce5e6e05"}, {"BlockType": "WORD", "Confidence": 97.70108795166016, "Text": "MENOMONIE,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15706591308116913, "Height": 0.02541816607117653, "Left": 0.3683304190635681, "Top": 0.3443339467048645}, "Polygon": [{"X": 0.3683304190635681, "Y": 0.35056912899017334}, {"X": 0.5233153700828552, "Y": 0.3443339467048645}, {"X": 0.5253963470458984, "Y": 0.3636697828769684}, {"X": 0.37067997455596924, "Y": 0.3697521388530731}]}, "Id": "e0eadf10-0e23-43d8-ba07-99068937adb7"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034196823835372925, "Height": 0.016869185492396355, "Left": 0.542445719242096, "Top": 0.34074148535728455}, "Polygon": [{"X": 0.542445719242096, "Y": 0.34205523133277893}, {"X": 0.5750430822372437, "Y": 0.34074148535728455}, {"X": 0.5766425728797913, "Y": 0.3563227951526642}, {"X": 0.5440910458564758, "Y": 0.35761067271232605}]}, "Id": "4daefc1b-9f68-40ac-99ac-7155dd4373ca"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12558676302433014, "Height": 0.021616565063595772, "Left": 0.33073580265045166, "Top": 0.3950766324996948}, "Polygon": [{"X": 0.33073580265045166, "Y": 0.3997691869735718}, {"X": 0.45436814427375793, "Y": 0.3950766324996948}, {"X": 0.456322580575943, "Y": 0.4121079444885254}, {"X": 0.33287951350212097, "Y": 0.41669318079948425}]}, "Id": "05847da6-9368-4dbc-ad76-34d6b0d2de95"}, {"BlockType": "WORD", "Confidence": 98.85462951660156, "Text": "0091491", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10822438448667526, "Height": 0.021128593012690544, "Left": 0.4848507344722748, "Top": 0.38755884766578674}, "Polygon": [{"X": 0.4848507344722748, "Y": 0.39161205291748047}, {"X": 0.5913317203521729, "Y": 0.38755884766578674}, {"X": 0.593075156211853, "Y": 0.40472686290740967}, {"X": 0.4867596924304962, "Y": 0.40868744254112244}]}, "Id": "41b0abb5-bcc9-44fb-8f74-5ea72b6e8291"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07961297035217285, "Height": 0.017532072961330414, "Left": 0.33045610785484314, "Top": 0.42228013277053833}, "Polygon": [{"X": 0.33045610785484314, "Y": 0.425140917301178}, {"X": 0.40830913186073303, "Y": 0.42228013277053833}, {"X": 0.410069078207016, "Y": 0.43700990080833435}, {"X": 0.33231937885284424, "Y": 0.43981221318244934}]}, "Id": "fbf0049e-290d-44fd-a898-a5a5702cb596"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01864887773990631, "Height": 0.01588614471256733, "Left": 0.4394169747829437, "Top": 0.4186437129974365}, "Polygon": [{"X": 0.4394169747829437, "Y": 0.419266015291214}, {"X": 0.4563116431236267, "Y": 0.4186437129974365}, {"X": 0.45806586742401123, "Y": 0.433920681476593}, {"X": 0.4411945939064026, "Y": 0.4345298409461975}]}, "Id": "0049aed9-ab38-4306-9d0f-ae1a38cd304d"}, {"BlockType": "WORD", "Confidence": 96.77933502197266, "Text": "0900-1491", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13832098245620728, "Height": 0.022073710337281227, "Left": 0.4878992438316345, "Top": 0.40946972370147705}, "Polygon": [{"X": 0.4878992438316345, "Y": 0.41452163457870483}, {"X": 0.6245306134223938, "Y": 0.40946972370147705}, {"X": 0.6262202262878418, "Y": 0.4266097843647003}, {"X": 0.48980140686035156, "Y": 0.4315434396266937}]}, "Id": "89ab038f-0bbf-48e6-89cd-b4b7d9da70b5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062143724411726, "Height": 0.016646860167384148, "Left": 0.33524245023727417, "Top": 0.4454403221607208}, "Polygon": [{"X": 0.33524245023727417, "Y": 0.4475940465927124}, {"X": 0.395627498626709, "Y": 0.4454403221607208}, {"X": 0.3973861634731293, "Y": 0.4599781632423401}, {"X": 0.3370804190635681, "Y": 0.4620871841907501}]}, "Id": "d0e43970-205a-4645-a47d-c917c43ba5e2"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "10/16/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1586758941411972, "Height": 0.02549244649708271, "Left": 0.4896986782550812, "Top": 0.43124064803123474}, "Polygon": [{"X": 0.4896986782550812, "Y": 0.43686971068382263}, {"X": 0.6464390158653259, "Y": 0.43124064803123474}, {"X": 0.6483745574951172, "Y": 0.4512619972229004}, {"X": 0.49191972613334656, "Y": 0.4567330777645111}]}, "Id": "cd9c2cd8-b086-465a-bc7e-96c3c28f6fc6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06320150941610336, "Height": 0.01760995015501976, "Left": 0.33687490224838257, "Top": 0.4667598307132721}, "Polygon": [{"X": 0.33687490224838257, "Y": 0.46888452768325806}, {"X": 0.3981972634792328, "Y": 0.4667598307132721}, {"X": 0.40007638931274414, "Y": 0.4822934567928314}, {"X": 0.3388402462005615, "Y": 0.48436978459358215}]}, "Id": "4ded9c9d-af76-4f60-b313-fb6e5c62a13d"}, {"BlockType": "WORD", "Confidence": 99.67693328857422, "Text": "05:24", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0783000960946083, "Height": 0.018229618668556213, "Left": 0.491922527551651, "Top": 0.4590107798576355}, "Polygon": [{"X": 0.491922527551651, "Y": 0.4616738557815552}, {"X": 0.5685904622077942, "Y": 0.4590107798576355}, {"X": 0.5702226161956787, "Y": 0.4746377468109131}, {"X": 0.4936639368534088, "Y": 0.4772403836250305}]}, "Id": "2c3d6638-154d-4826-93af-a0dcba7fe787"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03184271231293678, "Height": 0.016164714470505714, "Left": 0.5857300758361816, "Top": 0.4562765657901764}, "Polygon": [{"X": 0.5857300758361816, "Y": 0.45733171701431274}, {"X": 0.6160619258880615, "Y": 0.4562765657901764}, {"X": 0.6175727844238281, "Y": 0.47140926122665405}, {"X": 0.5872829556465149, "Y": 0.47244128584861755}]}, "Id": "6e1db85c-bb8f-43bc-867e-1f09565392e5"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0617203488945961, "Height": 0.019755631685256958, "Left": 0.343904584646225, "Top": 0.5124786496162415}, "Polygon": [{"X": 0.343904584646225, "Y": 0.5144122242927551}, {"X": 0.4034619927406311, "Y": 0.5124786496162415}, {"X": 0.40562495589256287, "Y": 0.5303545594215393}, {"X": 0.34616437554359436, "Y": 0.5322343111038208}]}, "Id": "6e58de82-b43e-4945-a449-afef4d024d0f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12355337291955948, "Height": 0.02313827909529209, "Left": 0.4201680123806, "Top": 0.505038321018219}, "Polygon": [{"X": 0.4201680123806, "Y": 0.508999228477478}, {"X": 0.5416467189788818, "Y": 0.505038321018219}, {"X": 0.5437213778495789, "Y": 0.5243335962295532}, {"X": 0.42245662212371826, "Y": 0.5281766057014465}]}, "Id": "2e197f0e-075e-4105-bff9-b623de4154ef"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07925321161746979, "Height": 0.01877269335091114, "Left": 0.5735782384872437, "Top": 0.49969905614852905}, "Polygon": [{"X": 0.5735782384872437, "Y": 0.5022380948066711}, {"X": 0.6512531042098999, "Y": 0.49969905614852905}, {"X": 0.652831494808197, "Y": 0.5159960389137268}, {"X": 0.5752730369567871, "Y": 0.5184717178344727}]}, "Id": "22404185-0619-4306-9432-f5afbfaa0fe3"}, {"BlockType": "WORD", "Confidence": 99.59243774414062, "Text": "03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030798165127635002, "Height": 0.015194559469819069, "Left": 0.3620113134384155, "Top": 0.5346757769584656}, "Polygon": [{"X": 0.3620113134384155, "Y": 0.5355886816978455}, {"X": 0.39105790853500366, "Y": 0.5346757769584656}, {"X": 0.3928094804286957, "Y": 0.5489784479141235}, {"X": 0.3638007938861847, "Y": 0.5498703718185425}]}, "Id": "f547d6b8-2448-4304-bb9b-2cc4ebd6c4e6"}, {"BlockType": "WORD", "Confidence": 99.57369995117188, "Text": "140.310", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10809123516082764, "Height": 0.018392741680145264, "Left": 0.43727320432662964, "Top": 0.5281500816345215}, "Polygon": [{"X": 0.43727320432662964, "Y": 0.5315048694610596}, {"X": 0.5437382459640503, "Y": 0.5281500816345215}, {"X": 0.5453644394874573, "Y": 0.5432688593864441}, {"X": 0.439046710729599, "Y": 0.5465428233146667}]}, "Id": "09e6343d-a980-4930-ac13-25cf74bbfb1a"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "3.589", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07843261957168579, "Height": 0.017608720809221268, "Left": 0.5761746168136597, "Top": 0.5228736400604248}, "Polygon": [{"X": 0.5761746168136597, "Y": 0.5253052115440369}, {"X": 0.6531311273574829, "Y": 0.5228736400604248}, {"X": 0.6546072363853455, "Y": 0.5381093621253967}, {"X": 0.5777588486671448, "Y": 0.5404823422431946}]}, "Id": "6aaaf9d3-66de-4683-99fb-6cd8693e8a24"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03068307600915432, "Height": 0.015253177843987942, "Left": 0.36476922035217285, "Top": 0.5564401149749756}, "Polygon": [{"X": 0.36476922035217285, "Y": 0.5573189854621887}, {"X": 0.3936893343925476, "Y": 0.5564401149749756}, {"X": 0.3954522907733917, "Y": 0.5708354115486145}, {"X": 0.36657023429870605, "Y": 0.5716933012008667}]}, "Id": "2f88b4e7-6088-4b65-bd41-6287d29fb4c6"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "9.166", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07656673341989517, "Height": 0.01707472838461399, "Left": 0.47065162658691406, "Top": 0.5503234267234802}, "Polygon": [{"X": 0.47065162658691406, "Y": 0.5526070594787598}, {"X": 0.5456207394599915, "Y": 0.5503234267234802}, {"X": 0.547218382358551, "Y": 0.5651703476905823}, {"X": 0.4723515808582306, "Y": 0.567398190498352}]}, "Id": "4d7aa2da-77ed-4afe-8527-d7b3b8364414"}, {"BlockType": "WORD", "Confidence": 99.16533660888672, "Text": "4.189", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07822974026203156, "Height": 0.017483863979578018, "Left": 0.5781617164611816, "Top": 0.5453230738639832}, "Polygon": [{"X": 0.5781617164611816, "Y": 0.5476672053337097}, {"X": 0.6549184322357178, "Y": 0.5453230738639832}, {"X": 0.6563915014266968, "Y": 0.5605210661888123}, {"X": 0.5797425508499146, "Y": 0.5628069639205933}]}, "Id": "2f065732-d0c9-4743-866c-0ca139da6e17"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10547059774398804, "Height": 0.017530757933855057, "Left": 0.3550730347633362, "Top": 0.5979002714157104}, "Polygon": [{"X": 0.3550730347633362, "Y": 0.6008397936820984}, {"X": 0.4588334858417511, "Y": 0.5979002714157104}, {"X": 0.4605436325073242, "Y": 0.6125677227973938}, {"X": 0.35692286491394043, "Y": 0.615431010723114}]}, "Id": "1f9c5596-189c-42b8-97df-54f6fcf5283f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09359259158372879, "Height": 0.01802532933652401, "Left": 0.5669374465942383, "Top": 0.5900750160217285}, "Polygon": [{"X": 0.5669374465942383, "Y": 0.5926933288574219}, {"X": 0.659029483795166, "Y": 0.5900750160217285}, {"X": 0.6605300307273865, "Y": 0.6055528521537781}, {"X": 0.5685701370239258, "Y": 0.6081003546714783}]}, "Id": "65129e85-7525-4fb1-9dc4-a92de96b8c72"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05959165096282959, "Height": 0.015447062440216541, "Left": 0.3579924404621124, "Top": 0.6210565567016602}, "Polygon": [{"X": 0.3579924404621124, "Y": 0.6226367354393005}, {"X": 0.4159003794193268, "Y": 0.6210565567016602}, {"X": 0.417584091424942, "Y": 0.6349638104438782}, {"X": 0.35975024104118347, "Y": 0.6365036368370056}]}, "Id": "ddc28b03-8002-4029-a800-398a90a43a42"}, {"BlockType": "WORD", "Confidence": 99.20280456542969, "Text": "503.57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0926872044801712, "Height": 0.01778591237962246, "Left": 0.5691189169883728, "Top": 0.6122534275054932}, "Polygon": [{"X": 0.5691189169883728, "Y": 0.6147511005401611}, {"X": 0.660315990447998, "Y": 0.6122534275054932}, {"X": 0.6618061065673828, "Y": 0.6276111006736755}, {"X": 0.5707392692565918, "Y": 0.6300393342971802}]}, "Id": "02cff960-93fb-41bd-ae7a-e517c2a74a55"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "DEFBULK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1041448786854744, "Height": 0.01696399785578251, "Left": 0.3604995012283325, "Top": 0.6412212252616882}, "Polygon": [{"X": 0.3604995012283325, "Y": 0.643913209438324}, {"X": 0.462970495223999, "Y": 0.6412212252616882}, {"X": 0.4646443724632263, "Y": 0.6555666327476501}, {"X": 0.36230891942977905, "Y": 0.6581852436065674}]}, "Id": "3d48b5c4-1cf4-478c-83ee-62ad5098ce7e"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "38.40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07695779949426651, "Height": 0.01672719419002533, "Left": 0.5866591334342957, "Top": 0.6345533728599548}, "Polygon": [{"X": 0.5866591334342957, "Y": 0.6365424990653992}, {"X": 0.6621810793876648, "Y": 0.6345533728599548}, {"X": 0.663616955280304, "Y": 0.6493467092514038}, {"X": 0.5881991386413574, "Y": 0.6512805819511414}]}, "Id": "dfcb2211-1b3d-403d-baab-4e44c4128192"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07541544735431671, "Height": 0.01585560292005539, "Left": 0.3640249967575073, "Top": 0.6849971413612366}, "Polygon": [{"X": 0.3640249967575073, "Y": 0.6867830753326416}, {"X": 0.4377504587173462, "Y": 0.6849971413612366}, {"X": 0.43944042921066284, "Y": 0.6991186141967773}, {"X": 0.36581140756607056, "Y": 0.7008527517318726}]}, "Id": "ca246c08-f57e-4705-947d-74439c87b533"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "541.97", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09199905395507812, "Height": 0.017412381246685982, "Left": 0.5598547458648682, "Top": 0.6787101626396179}, "Polygon": [{"X": 0.5598547458648682, "Y": 0.6809070706367493}, {"X": 0.6503461003303528, "Y": 0.6787101626396179}, {"X": 0.6518537998199463, "Y": 0.6939938068389893}, {"X": 0.5614917874336243, "Y": 0.6961225271224976}]}, "Id": "aa16bb7c-2d2a-480f-8eb0-3752b9888994"}, {"BlockType": "WORD", "Confidence": 99.55735778808594, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11907178908586502, "Height": 0.019053809344768524, "Left": 0.37005114555358887, "Top": 0.7259837985038757}, "Polygon": [{"X": 0.37005114555358887, "Y": 0.7285879850387573}, {"X": 0.4872158467769623, "Y": 0.7259837985038757}, {"X": 0.4891229569911957, "Y": 0.7425293326377869}, {"X": 0.3721385598182678, "Y": 0.7450376152992249}]}, "Id": "3760fb11-913f-40ea-8e30-0089714326be"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061724644154310226, "Height": 0.015624284744262695, "Left": 0.5019854307174683, "Top": 0.7250997424125671}, "Polygon": [{"X": 0.5019854307174683, "Y": 0.7264350652694702}, {"X": 0.5621646642684937, "Y": 0.7250997424125671}, {"X": 0.56371009349823, "Y": 0.7394312024116516}, {"X": 0.5036115646362305, "Y": 0.7407240271568298}]}, "Id": "ee5998ca-b386-4cdb-a848-17416a497700"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0760304257273674, "Height": 0.01602984219789505, "Left": 0.5790011882781982, "Top": 0.7221386432647705}, "Polygon": [{"X": 0.5790011882781982, "Y": 0.7237974405288696}, {"X": 0.****************, "Y": 0.7221386432647705}, {"X": 0.****************, "Y": 0.7365626096725464}, {"X": 0.5805266499519348, "Y": 0.7381685376167297}]}, "Id": "0945c69d-5bec-4d00-a018-11b3a776fdf5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0754198282957077, "Height": 0.015597942285239697, "Left": 0.3725605309009552, "Top": 0.748265266418457}, "Polygon": [{"X": 0.3725605309009552, "Y": 0.7498294115066528}, {"X": 0.****************, "Y": 0.748265266418457}, {"X": 0.****************, "Y": 0.7623504996299744}, {"X": 0.3743416368961334, "Y": 0.763863205909729}]}, "Id": "26c9febe-d91e-45f7-a6f0-024d9839ae04"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04265856370329857, "Height": 0.014642882160842419, "Left": 0.****************, "Top": 0.7480116486549377}, "Polygon": [{"X": 0.****************, "Y": 0.7488813400268555}, {"X": 0.5040474534034729, "Y": 0.7480116486549377}, {"X": 0.505618691444397, "Y": 0.7618128657341003}, {"X": 0.****************, "Y": 0.7626545429229736}]}, "Id": "916f8214-7b43-4f27-89fa-16f598fce4cb"}, {"BlockType": "WORD", "Confidence": 94.93084716796875, "Text": "1591", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054683275520801544, "Height": 0.014805714599788189, "Left": 0.5850005149841309, "Top": 0.7654070258140564}, "Polygon": [{"X": 0.5850005149841309, "Y": 0.7664844393730164}, {"X": 0.6382977366447449, "Y": 0.7654070258140564}, {"X": 0.6396837830543518, "Y": 0.7791712284088135}, {"X": 0.586455762386322, "Y": 0.7802127599716187}]}, "Id": "8221fcae-d9da-4d91-8dfa-7b95739005d7"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05919795110821724, "Height": 0.014415208250284195, "Left": 0.3771515488624573, "Top": 0.7906032204627991}, "Polygon": [{"X": 0.3771515488624573, "Y": 0.7917096018791199}, {"X": 0.4347313940525055, "Y": 0.7906032204627991}, {"X": 0.436349481344223, "Y": 0.8039500117301941}, {"X": 0.3788416087627411, "Y": 0.8050184845924377}]}, "Id": "11b309e0-edc2-425f-a41e-3690ae1d9bd6"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017572999000549316, "Height": 0.013901620171964169, "Left": 0.44935381412506104, "Top": 0.7901935577392578}, "Polygon": [{"X": 0.44935381412506104, "Y": 0.7905001640319824}, {"X": 0.46531885862350464, "Y": 0.7901935577392578}, {"X": 0.46692681312561035, "Y": 0.8037992715835571}, {"X": 0.4509821832180023, "Y": 0.8040951490402222}]}, "Id": "a64c1656-32f8-4a7a-8ad8-4412ab911b68"}, {"BlockType": "WORD", "Confidence": 99.73233795166016, "Text": "P32191", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08712770789861679, "Height": 0.015634816139936447, "Left": 0.5846709609031677, "Top": 0.7858286499977112}, "Polygon": [{"X": 0.5846709609031677, "Y": 0.7874773740768433}, {"X": 0.6704264879226685, "Y": 0.7858286499977112}, {"X": 0.6717987060546875, "Y": 0.7998735308647156}, {"X": 0.5861571431159973, "Y": 0.8014634847640991}]}, "Id": "3e8c0c90-7169-46c6-80de-3432e79793b5"}, {"BlockType": "WORD", "Confidence": 99.11331939697266, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12792809307575226, "Height": 0.016571810469031334, "Left": 0.38009411096572876, "Top": 0.8095539808273315}, "Polygon": [{"X": 0.38009411096572876, "Y": 0.8118590712547302}, {"X": 0.5063811540603638, "Y": 0.8095539808273315}, {"X": 0.5080221891403198, "Y": 0.8239096999168396}, {"X": 0.3819052577018738, "Y": 0.8261258006095886}]}, "Id": "7de7b3c6-9f3c-4d32-a9c8-af3f0d5aedb1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12019389122724533, "Height": 0.015714526176452637, "Left": 0.5265572667121887, "Top": 0.8072118163108826}, "Polygon": [{"X": 0.5265572667121887, "Y": 0.8093795776367188}, {"X": 0.6453830003738403, "Y": 0.8072118163108826}, {"X": 0.6467511653900146, "Y": 0.8208374977111816}, {"X": 0.5280784964561462, "Y": 0.8229263424873352}]}, "Id": "1e98ba48-e5b8-4ce8-89e3-147f709c7e4d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05880536511540413, "Height": 0.014169133268296719, "Left": 0.39653313159942627, "Top": 0.8313685655593872}, "Polygon": [{"X": 0.39653313159942627, "Y": 0.8323563933372498}, {"X": 0.45375457406044006, "Y": 0.8313685655593872}, {"X": 0.4553384780883789, "Y": 0.844586968421936}, {"X": 0.3981882333755493, "Y": 0.8455376625061035}]}, "Id": "a4cd4eef-ab25-46d7-889c-69c689b94356"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07039988040924072, "Height": 0.01461489126086235, "Left": 0.4695174992084503, "Top": 0.8300569653511047}, "Polygon": [{"X": 0.4695174992084503, "Y": 0.8312458992004395}, {"X": 0.5384174585342407, "Y": 0.8300569653511047}, {"X": 0.5399174094200134, "Y": 0.8435282707214355}, {"X": 0.47110509872436523, "Y": 0.8446718454360962}]}, "Id": "c1e6a03a-1e0b-461c-9c0f-fe1ce7673e3a"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "P32191", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08654528111219406, "Height": 0.014742119237780571, "Left": 0.5581725239753723, "Top": 0.8278249502182007}, "Polygon": [{"X": 0.5581725239753723, "Y": 0.829296886920929}, {"X": 0.6433743834495544, "Y": 0.8278249502182007}, {"X": 0.644717812538147, "Y": 0.8411503434181213}, {"X": 0.559623658657074, "Y": 0.8425670862197876}]}, "Id": "d0bfbb8c-cf30-4268-80d0-69191fd9fb21"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1a339256-cdb8-408b-b365-5801c072b8cb", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1a339256-cdb8-408b-b365-5801c072b8cb", "content-type": "application/x-amz-json-1.1", "content-length": "41187", "date": "Wed, 24 Sep 2025 12:47:19 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "7848d987-6d0a-4d11-91f1-95e5941f28ec", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:22 GMT", "content-type": "application/json", "content-length": "1346", "connection": "keep-alive", "x-amzn-requestid": "7848d987-6d0a-4d11-91f1-95e5941f28ec"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. We need to determine doc_type.\n\nContent: \"KWIK TRIP #674 ... Invoice# 0091491 ... Pump Quantity Price ... DSL2 ... DEFBULK ... Total 541.97 ... CARD# ICR 1591 AUTH # P32191 RESPONSE: APPROVED AUTH REF#: P32191\"\n\nThis looks like a Fuel Receipt: fuel purchase at pump, maybe \"Fuel Receipt\" with pump quantity price. Keywords: Fuel, Diesel, Pump, Gallons, etc. There's \"Pump Quantity Price\" and \"Response: Approved\" indicating fuel purchase authorization. So classification: fuel_receipt.\n\nCheck definitions: fuel_receipt keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. We have Pump. Also \"AUTH\" typical of fuel receipts.\n\nNo invoice header. It's more like a fuel receipt rather than invoice. So doc_type: fuel_receipt.\n\nReturn using tool classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type \"fuel_receipt\".\n\nMake sure use correct enum names: \"fuel_receipt\" is in the list.\n\nSo produce tool call."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2667, "outputTokens": 263, "totalTokens": 2930}, "metrics": {"latencyMs": 1188}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/eb1eae57_VAMKEOJNCLNVRBTZBF3S.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP #674\n6107 3M DR\nMEN<PERSON>ONIE, WI\nInvoice# 0091491\nTrans # 0900-1491\nDate\n10/16/2024\nTime\n05:24 PM\nPump Quantity Price\n03\n140.310 3.589\n03\n9.166 4.189\nProduct\nAmount\nDSL2\n503.57\nDEFBULK\n38.40\nTOTAL\n541.97\nNBS/KWIK TRIP FLEET\nCARD# ICR\n1591\nAUTH #\nP32191\nRESPONSE: APPROVED\nAUTH REF#: P32191\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}