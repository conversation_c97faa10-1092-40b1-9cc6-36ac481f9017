2025-09-23 17:15:58,113 - INFO - Logging initialized. Log file: logs/test_classification_20250923_171558.log
2025-09-23 17:15:58,114 - INFO - 📁 Found 1 files to process
2025-09-23 17:15:58,114 - INFO - 🚀 Starting processing with run number: 1
2025-09-23 17:15:58,114 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-23 17:15:58,114 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-23 17:15:58,114 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-23 17:15:58,115 - INFO - ⬆️ [17:15:58] Uploading: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:16:04,612 - INFO - ✓ Uploaded: mansi/Purchase_Order_VRSD0E9045781.pdf -> s3://document-extraction-logistically/temp/42fa8d65_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:16:04,612 - INFO - 🔍 [17:16:04] Starting classification: Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:16:04,614 - INFO - Initializing TextractProcessor...
2025-09-23 17:16:04,637 - INFO - Initializing BedrockProcessor...
2025-09-23 17:16:04,669 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/42fa8d65_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:16:04,670 - INFO - Processing PDF from S3...
2025-09-23 17:16:04,671 - INFO - Downloading PDF from S3 to /tmp/tmpif4t4ww9/42fa8d65_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:16:07,692 - INFO - Downloaded PDF size: 1.1 MB
2025-09-23 17:16:07,692 - INFO - Splitting PDF into individual pages...
2025-09-23 17:16:07,699 - INFO - Splitting PDF 42fa8d65_Purchase_Order_VRSD0E9045781 into 7 pages
2025-09-23 17:16:07,707 - INFO - Split PDF into 7 pages
2025-09-23 17:16:07,708 - INFO - Processing pages with Textract in parallel...
2025-09-23 17:16:07,708 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-23 17:16:13,350 - INFO - Page 3: Extracted 929 characters, 64 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_003.pdf
2025-09-23 17:16:13,351 - INFO - Successfully processed page 3
2025-09-23 17:16:13,595 - INFO - Page 2: Extracted 1174 characters, 29 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_002.pdf
2025-09-23 17:16:13,596 - INFO - Successfully processed page 2
2025-09-23 17:16:13,717 - INFO - Page 1: Extracted 1168 characters, 45 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_001.pdf
2025-09-23 17:16:13,717 - INFO - Successfully processed page 1
2025-09-23 17:16:13,853 - INFO - Page 5: Extracted 2022 characters, 27 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_005.pdf
2025-09-23 17:16:13,853 - INFO - Successfully processed page 5
2025-09-23 17:16:14,083 - INFO - Page 4: Extracted 1151 characters, 63 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_004.pdf
2025-09-23 17:16:14,084 - INFO - Successfully processed page 4
2025-09-23 17:16:15,398 - INFO - Page 6: Extracted 4159 characters, 115 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_006.pdf
2025-09-23 17:16:15,398 - INFO - Successfully processed page 6
2025-09-23 17:16:17,200 - INFO - Page 7: Extracted 788 characters, 55 lines from 42fa8d65_Purchase_Order_VRSD0E9045781_52e848d1_page_007.pdf
2025-09-23 17:16:17,201 - INFO - Successfully processed page 7
2025-09-23 17:16:17,202 - INFO - Combined 7 pages into final text
2025-09-23 17:16:17,202 - INFO - Text validation for 42fa8d65_Purchase_Order_VRSD0E9045781: 11522 characters, 7 pages
2025-09-23 17:16:17,205 - INFO - Analyzing document types with Bedrock...
2025-09-23 17:16:17,205 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-23 17:16:34,308 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-23 17:16:34,310 - ERROR - Processing failed for s3://document-extraction-logistically/temp/42fa8d65_Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:16:34,312 - ERROR - ✗ Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:16:35,741 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/42fa8d65_Purchase_Order_VRSD0E9045781.pdf
2025-09-23 17:16:35,742 - INFO - 
📊 Processing Summary:
2025-09-23 17:16:35,742 - INFO -    Total files: 1
2025-09-23 17:16:35,743 - INFO -    Successful: 0
2025-09-23 17:16:35,743 - INFO -    Failed: 1
2025-09-23 17:16:35,743 - INFO -    Duration: 37.63 seconds
2025-09-23 17:16:35,743 - INFO -    Output directory: output
2025-09-23 17:16:35,743 - ERROR - 
❌ Errors:
2025-09-23 17:16:35,743 - ERROR -    Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock
2025-09-23 17:16:35,744 - INFO - 
📋 No files were successfully processed.
2025-09-23 17:16:35,744 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process mansi/Purchase_Order_VRSD0E9045781.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 37.627679, 'processed_files': []}
