2025-09-24 19:07:20,186 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190720.log
2025-09-24 19:07:20,186 - INFO - 📁 Found 5 files to process
2025-09-24 19:07:20,186 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:07:20,186 - INFO - 🚀 Processing 5 files in FORCED PARALLEL MODE...
2025-09-24 19:07:20,186 - INFO - 🚀 Creating 5 parallel tasks...
2025-09-24 19:07:20,186 - INFO - 🚀 All 5 tasks created - executing in parallel...
2025-09-24 19:07:20,187 - INFO - ⬆️ [19:07:20] Uploading: 3_rate_confirmation_1.pdf
2025-09-24 19:07:22,857 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_1.pdf -> s3://document-extraction-logistically/temp/a7699ac0_3_rate_confirmation_1.pdf
2025-09-24 19:07:22,857 - INFO - 🔍 [19:07:22] Starting classification: 3_rate_confirmation_1.pdf
2025-09-24 19:07:22,858 - INFO - Initializing TextractProcessor...
2025-09-24 19:07:22,859 - INFO - ⬆️ [19:07:22] Uploading: 3_rate_confirmation_2.pdf
2025-09-24 19:07:22,877 - INFO - Initializing BedrockProcessor...
2025-09-24 19:07:22,883 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a7699ac0_3_rate_confirmation_1.pdf
2025-09-24 19:07:22,883 - INFO - Processing PDF from S3...
2025-09-24 19:07:22,884 - INFO - Downloading PDF from S3 to /tmp/tmp6ehbhqof/a7699ac0_3_rate_confirmation_1.pdf
2025-09-24 19:07:23,846 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_2.pdf -> s3://document-extraction-logistically/temp/f8c1ba67_3_rate_confirmation_2.pdf
2025-09-24 19:07:23,846 - INFO - 🔍 [19:07:23] Starting classification: 3_rate_confirmation_2.pdf
2025-09-24 19:07:23,846 - INFO - ⬆️ [19:07:23] Uploading: 3_rate_confirmation_3.pdf
2025-09-24 19:07:23,847 - INFO - Initializing TextractProcessor...
2025-09-24 19:07:23,861 - INFO - Initializing BedrockProcessor...
2025-09-24 19:07:23,865 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f8c1ba67_3_rate_confirmation_2.pdf
2025-09-24 19:07:23,865 - INFO - Processing PDF from S3...
2025-09-24 19:07:23,865 - INFO - Downloading PDF from S3 to /tmp/tmpwb1c68_n/f8c1ba67_3_rate_confirmation_2.pdf
2025-09-24 19:07:24,530 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_3.pdf -> s3://document-extraction-logistically/temp/3fad223a_3_rate_confirmation_3.pdf
2025-09-24 19:07:24,530 - INFO - 🔍 [19:07:24] Starting classification: 3_rate_confirmation_3.pdf
2025-09-24 19:07:24,531 - INFO - ⬆️ [19:07:24] Uploading: 3_rate_confirmation_4.pdf
2025-09-24 19:07:24,531 - INFO - Initializing TextractProcessor...
2025-09-24 19:07:24,541 - INFO - Initializing BedrockProcessor...
2025-09-24 19:07:24,543 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3fad223a_3_rate_confirmation_3.pdf
2025-09-24 19:07:24,543 - INFO - Processing PDF from S3...
2025-09-24 19:07:24,543 - INFO - Downloading PDF from S3 to /tmp/tmpbv39hc32/3fad223a_3_rate_confirmation_3.pdf
2025-09-24 19:07:25,200 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 19:07:25,200 - INFO - Splitting PDF into individual pages...
2025-09-24 19:07:25,201 - INFO - Splitting PDF a7699ac0_3_rate_confirmation_1 into 1 pages
2025-09-24 19:07:25,203 - INFO - Split PDF into 1 pages
2025-09-24 19:07:25,203 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:07:25,203 - INFO - Expected pages: [1]
2025-09-24 19:07:25,280 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_4.pdf -> s3://document-extraction-logistically/temp/5c54faa4_3_rate_confirmation_4.pdf
2025-09-24 19:07:25,280 - INFO - 🔍 [19:07:25] Starting classification: 3_rate_confirmation_4.pdf
2025-09-24 19:07:25,281 - INFO - ⬆️ [19:07:25] Uploading: 3_rate_confirmation_5.pdf
2025-09-24 19:07:25,282 - INFO - Initializing TextractProcessor...
2025-09-24 19:07:25,291 - INFO - Initializing BedrockProcessor...
2025-09-24 19:07:25,293 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5c54faa4_3_rate_confirmation_4.pdf
2025-09-24 19:07:25,293 - INFO - Processing PDF from S3...
2025-09-24 19:07:25,294 - INFO - Downloading PDF from S3 to /tmp/tmpt8hqgjnd/5c54faa4_3_rate_confirmation_4.pdf
2025-09-24 19:07:26,107 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 19:07:26,107 - INFO - Splitting PDF into individual pages...
2025-09-24 19:07:26,109 - INFO - Splitting PDF f8c1ba67_3_rate_confirmation_2 into 1 pages
2025-09-24 19:07:26,113 - INFO - Split PDF into 1 pages
2025-09-24 19:07:26,114 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:07:26,114 - INFO - Expected pages: [1]
2025-09-24 19:07:26,341 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:07:26,341 - INFO - Splitting PDF into individual pages...
2025-09-24 19:07:26,345 - INFO - Splitting PDF 3fad223a_3_rate_confirmation_3 into 2 pages
2025-09-24 19:07:26,365 - INFO - Split PDF into 2 pages
2025-09-24 19:07:26,365 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:07:26,365 - INFO - Expected pages: [1, 2]
2025-09-24 19:07:26,726 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_5.pdf -> s3://document-extraction-logistically/temp/0f32e943_3_rate_confirmation_5.pdf
2025-09-24 19:07:26,727 - INFO - 🔍 [19:07:26] Starting classification: 3_rate_confirmation_5.pdf
2025-09-24 19:07:26,727 - INFO - Initializing TextractProcessor...
2025-09-24 19:07:26,732 - INFO - Initializing BedrockProcessor...
2025-09-24 19:07:26,734 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0f32e943_3_rate_confirmation_5.pdf
2025-09-24 19:07:26,734 - INFO - Processing PDF from S3...
2025-09-24 19:07:26,734 - INFO - Downloading PDF from S3 to /tmp/tmpsz5dng9e/0f32e943_3_rate_confirmation_5.pdf
2025-09-24 19:07:27,307 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:07:27,307 - INFO - Splitting PDF into individual pages...
2025-09-24 19:07:27,309 - INFO - Splitting PDF 5c54faa4_3_rate_confirmation_4 into 2 pages
2025-09-24 19:07:27,314 - INFO - Split PDF into 2 pages
2025-09-24 19:07:27,314 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:07:27,314 - INFO - Expected pages: [1, 2]
2025-09-24 19:07:29,233 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 19:07:29,233 - INFO - Splitting PDF into individual pages...
2025-09-24 19:07:29,234 - INFO - Splitting PDF 0f32e943_3_rate_confirmation_5 into 2 pages
2025-09-24 19:07:29,237 - INFO - Split PDF into 2 pages
2025-09-24 19:07:29,237 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:07:29,237 - INFO - Expected pages: [1, 2]
2025-09-24 19:07:30,067 - INFO - Page 2: Extracted 510 characters, 15 lines from 3fad223a_3_rate_confirmation_3_d5a3e40d_page_002.pdf
2025-09-24 19:07:30,068 - INFO - Successfully processed page 2
2025-09-24 19:07:30,920 - INFO - Page 1: Extracted 1211 characters, 66 lines from a7699ac0_3_rate_confirmation_1_bedaf874_page_001.pdf
2025-09-24 19:07:30,921 - INFO - Successfully processed page 1
2025-09-24 19:07:30,921 - INFO - Combined 1 pages into final text
2025-09-24 19:07:30,921 - INFO - Text validation for a7699ac0_3_rate_confirmation_1: 1228 characters, 1 pages
2025-09-24 19:07:30,921 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:07:30,921 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:07:31,196 - INFO - Page 1: Extracted 1245 characters, 64 lines from 3fad223a_3_rate_confirmation_3_d5a3e40d_page_001.pdf
2025-09-24 19:07:31,196 - INFO - Successfully processed page 1
2025-09-24 19:07:31,196 - INFO - Combined 2 pages into final text
2025-09-24 19:07:31,196 - INFO - Text validation for 3fad223a_3_rate_confirmation_3: 1791 characters, 2 pages
2025-09-24 19:07:31,196 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:07:31,196 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:07:31,794 - INFO - Page 2: Extracted 469 characters, 15 lines from 5c54faa4_3_rate_confirmation_4_b96881ea_page_002.pdf
2025-09-24 19:07:31,794 - INFO - Successfully processed page 2
2025-09-24 19:07:32,173 - INFO - Page 1: Extracted 1259 characters, 67 lines from f8c1ba67_3_rate_confirmation_2_3bee38fc_page_001.pdf
2025-09-24 19:07:32,174 - INFO - Successfully processed page 1
2025-09-24 19:07:32,174 - INFO - Combined 1 pages into final text
2025-09-24 19:07:32,174 - INFO - Text validation for f8c1ba67_3_rate_confirmation_2: 1276 characters, 1 pages
2025-09-24 19:07:32,175 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:07:32,175 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:07:32,696 - INFO - Page 1: Extracted 1266 characters, 72 lines from 5c54faa4_3_rate_confirmation_4_b96881ea_page_001.pdf
2025-09-24 19:07:32,696 - INFO - Successfully processed page 1
2025-09-24 19:07:32,697 - INFO - Combined 2 pages into final text
2025-09-24 19:07:32,697 - INFO - Text validation for 5c54faa4_3_rate_confirmation_4: 1771 characters, 2 pages
2025-09-24 19:07:32,698 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:07:32,698 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:07:32,863 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f781d734-7e5d-4320-90db-8af854659100', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:37:32 GMT', 'content-type': 'application/json', 'content-length': '838', 'connection': 'keep-alive', 'x-amzn-requestid': 'f781d734-7e5d-4320-90db-8af854659100'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. The content is shipping instructions & rate confirmation. This is likely a rate confirmation document. Keywords: "Shipping Instructions & Rate Confirmation", "Rate Confirmation" header. So doc_type: rate_confirmation.\n\nCheck: page has "Page 1 of 2" indicates continuation but no "continued" on top. But we only have page1. It\'s first page. Should be rate_confirmation. No other page. So output accordingly.\n\nReturn JSON with documents array.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2986, 'outputTokens': 121, 'totalTokens': 3107}, 'metrics': {'latencyMs': 737}}
2025-09-24 19:07:32,863 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a7699ac0_3_rate_confirmation_1.pdf
2025-09-24 19:07:32,889 - INFO - 

3_rate_confirmation_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 19:07:32,889 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_1.json
2025-09-24 19:07:33,201 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8c8ef251-decc-4ee5-b5b8-81a0d797fdbf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:37:33 GMT', 'content-type': 'application/json', 'content-length': '920', 'connection': 'keep-alive', 'x-amzn-requestid': '8c8ef251-decc-4ee5-b5b8-81a0d797fdbf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document is a "Shipping Instructions & Rate Confirmation" with fields. Likely it\'s a rate confirmation. The doc_type "rate_confirmation" seems appropriate. Check keywords: "Rate Confirmation", "Carrier Rate Confirmation", "Carrier Rate", etc. The page shows "Shipping Instructions & Rate Confirmation". That indicates rate confirmation. So both pages are rate_confirmation. Need to output JSON with documents array containing page_no 1 and 2. Ensure doc_type string exactly "rate_confirmation".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3171, 'outputTokens': 132, 'totalTokens': 3303}, 'metrics': {'latencyMs': 833}}
2025-09-24 19:07:33,202 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3fad223a_3_rate_confirmation_3.pdf
2025-09-24 19:07:33,825 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a7699ac0_3_rate_confirmation_1.pdf
2025-09-24 19:07:33,853 - INFO - 

3_rate_confirmation_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 19:07:33,854 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_3.json
2025-09-24 19:07:34,087 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e72afdbb-8f0b-466b-aa76-7f9e1424804c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:37:33 GMT', 'content-type': 'application/json', 'content-length': '711', 'connection': 'keep-alive', 'x-amzn-requestid': 'e72afdbb-8f0b-466b-aa76-7f9e1424804c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page (page 1) content. The header says "Shipping Instructions & Rate Confirmation". This is likely a rate confirmation. The document type: "rate_confirmation" as defined. It includes shipping instructions, order number, pickup date, carrier details, total amount. Yes, "Rate Confirmation" is a document type. So doc_type: "rate_confirmation".\n\nWe need output JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2989, 'outputTokens': 98, 'totalTokens': 3087}, 'metrics': {'latencyMs': 639}}
2025-09-24 19:07:34,088 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f8c1ba67_3_rate_confirmation_2.pdf
2025-09-24 19:07:34,133 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3fad223a_3_rate_confirmation_3.pdf
2025-09-24 19:07:34,146 - INFO - 

3_rate_confirmation_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 19:07:34,146 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_2.json
2025-09-24 19:07:34,432 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f8c1ba67_3_rate_confirmation_2.pdf
2025-09-24 19:07:34,897 - INFO - Page 2: Extracted 827 characters, 33 lines from 0f32e943_3_rate_confirmation_5_76848f04_page_002.pdf
2025-09-24 19:07:34,897 - INFO - Successfully processed page 2
2025-09-24 19:07:34,933 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be030ff6-b624-4899-a9b2-61132970a18d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:37:34 GMT', 'content-type': 'application/json', 'content-length': '1170', 'connection': 'keep-alive', 'x-amzn-requestid': 'be030ff6-b624-4899-a9b2-61132970a18d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages: page1 and page2. The content indicates Shipping Instructions & Rate Confirmation. The header says "Shipping Instructions & Rate Confirmation". This seems to be a "rate_confirmation" doc. The description: "Rate Confirmation" matches definition of rate_confirmation: "Carrier Rate Confirmation". Keywords: "Rate Confirmation", "Carrier", "Total Amount". So doc_type likely "rate_confirmation". No other document types. It\'s not a POD because POD required upon delivery but not the doc. It\'s a Rate Confirmation. So both pages same type.\n\nCheck for "continued" etc: no.\n\nThus output JSON with documents array containing two items: page_no 1 doc_type rate_confirmation, page_no2 same.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "rate_confirmation"\n    },\n    {\n      "page_no": 2,\n      "doc_type": "rate_confirmation"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3160, 'outputTokens': 196, 'totalTokens': 3356}, 'metrics': {'latencyMs': 1009}}
2025-09-24 19:07:34,934 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5c54faa4_3_rate_confirmation_4.pdf
2025-09-24 19:07:34,963 - INFO - 

3_rate_confirmation_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 19:07:34,963 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_4.json
2025-09-24 19:07:35,266 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5c54faa4_3_rate_confirmation_4.pdf
2025-09-24 19:07:36,523 - INFO - Page 1: Extracted 1455 characters, 63 lines from 0f32e943_3_rate_confirmation_5_76848f04_page_001.pdf
2025-09-24 19:07:36,523 - INFO - Successfully processed page 1
2025-09-24 19:07:36,524 - INFO - Combined 2 pages into final text
2025-09-24 19:07:36,524 - INFO - Text validation for 0f32e943_3_rate_confirmation_5: 2318 characters, 2 pages
2025-09-24 19:07:36,525 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:07:36,525 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:07:38,442 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6f7940de-6468-4ed0-b1dc-df93264936b1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:37:38 GMT', 'content-type': 'application/json', 'content-length': '764', 'connection': 'keep-alive', 'x-amzn-requestid': '6f7940de-6468-4ed0-b1dc-df93264936b1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 2 pages. The content: "Shipping Instructions & Rate Confirmation" appears, with header. That matches "rate_confirmation" definition: "Carrier Rate Confirmation". So both pages are part of the same document, with pages 1 and 2. Must output documents array: page_no 1 and 2, doc_type "rate_confirmation". Ensure not "continued" etc. It\'s the same doc. Output as required.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3297, 'outputTokens': 116, 'totalTokens': 3413}, 'metrics': {'latencyMs': 670}}
2025-09-24 19:07:38,443 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0f32e943_3_rate_confirmation_5.pdf
2025-09-24 19:07:38,472 - INFO - 

3_rate_confirmation_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 19:07:38,472 - INFO - 

✓ Saved result: output/run1_3_rate_confirmation_5.json
2025-09-24 19:07:38,796 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0f32e943_3_rate_confirmation_5.pdf
2025-09-24 19:07:38,797 - INFO - 
📊 Processing Summary:
2025-09-24 19:07:38,798 - INFO -    Total files: 5
2025-09-24 19:07:38,798 - INFO -    Successful: 5
2025-09-24 19:07:38,798 - INFO -    Failed: 0
2025-09-24 19:07:38,798 - INFO -    Duration: 18.61 seconds
2025-09-24 19:07:38,798 - INFO -    Output directory: output
2025-09-24 19:07:38,798 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:07:38,798 - INFO -    📄 3_rate_confirmation_1.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-24 19:07:38,798 - INFO -    📄 3_rate_confirmation_2.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-24 19:07:38,798 - INFO -    📄 3_rate_confirmation_3.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 19:07:38,798 - INFO -    📄 3_rate_confirmation_4.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 19:07:38,799 - INFO -    📄 3_rate_confirmation_5.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-24 19:07:38,799 - INFO - 
============================================================================================================================================
2025-09-24 19:07:38,799 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:07:38,799 - INFO - ============================================================================================================================================
2025-09-24 19:07:38,799 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:07:38,799 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:07:38,799 - INFO - 3_rate_confirmation_1.pdf                          1      rate_confirmation                        run1_3_rate_confirmation_1.json                                                 
2025-09-24 19:07:38,799 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_1.pdf
2025-09-24 19:07:38,800 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_1.json
2025-09-24 19:07:38,800 - INFO - 
2025-09-24 19:07:38,800 - INFO - 3_rate_confirmation_2.pdf                          1      rate_confirmation                        run1_3_rate_confirmation_2.json                                                 
2025-09-24 19:07:38,800 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_2.pdf
2025-09-24 19:07:38,800 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_2.json
2025-09-24 19:07:38,800 - INFO - 
2025-09-24 19:07:38,800 - INFO - 3_rate_confirmation_3.pdf                          1      rate_confirmation                        run1_3_rate_confirmation_3.json                                                 
2025-09-24 19:07:38,800 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_3.pdf
2025-09-24 19:07:38,800 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_3.json
2025-09-24 19:07:38,800 - INFO - 
2025-09-24 19:07:38,800 - INFO - 3_rate_confirmation_3.pdf                          2      rate_confirmation                        run1_3_rate_confirmation_3.json                                                 
2025-09-24 19:07:38,800 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_3.pdf
2025-09-24 19:07:38,800 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_3.json
2025-09-24 19:07:38,800 - INFO - 
2025-09-24 19:07:38,800 - INFO - 3_rate_confirmation_4.pdf                          1      rate_confirmation                        run1_3_rate_confirmation_4.json                                                 
2025-09-24 19:07:38,800 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_4.pdf
2025-09-24 19:07:38,801 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_4.json
2025-09-24 19:07:38,801 - INFO - 
2025-09-24 19:07:38,801 - INFO - 3_rate_confirmation_4.pdf                          2      rate_confirmation                        run1_3_rate_confirmation_4.json                                                 
2025-09-24 19:07:38,801 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_4.pdf
2025-09-24 19:07:38,801 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_4.json
2025-09-24 19:07:38,801 - INFO - 
2025-09-24 19:07:38,801 - INFO - 3_rate_confirmation_5.pdf                          1      rate_confirmation                        run1_3_rate_confirmation_5.json                                                 
2025-09-24 19:07:38,801 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_5.pdf
2025-09-24 19:07:38,801 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_5.json
2025-09-24 19:07:38,801 - INFO - 
2025-09-24 19:07:38,801 - INFO - 3_rate_confirmation_5.pdf                          2      rate_confirmation                        run1_3_rate_confirmation_5.json                                                 
2025-09-24 19:07:38,801 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_5.pdf
2025-09-24 19:07:38,801 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_3_rate_confirmation_5.json
2025-09-24 19:07:38,801 - INFO - 
2025-09-24 19:07:38,801 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:07:38,801 - INFO - Total entries: 8
2025-09-24 19:07:38,801 - INFO - ============================================================================================================================================
2025-09-24 19:07:38,802 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:07:38,802 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:07:38,802 - INFO -   1. 3_rate_confirmation_1.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_1.json
2025-09-24 19:07:38,802 - INFO -   2. 3_rate_confirmation_2.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_2.json
2025-09-24 19:07:38,802 - INFO -   3. 3_rate_confirmation_3.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_3.json
2025-09-24 19:07:38,802 - INFO -   4. 3_rate_confirmation_3.pdf           Page 2   → rate_confirmation | run1_3_rate_confirmation_3.json
2025-09-24 19:07:38,802 - INFO -   5. 3_rate_confirmation_4.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_4.json
2025-09-24 19:07:38,802 - INFO -   6. 3_rate_confirmation_4.pdf           Page 2   → rate_confirmation | run1_3_rate_confirmation_4.json
2025-09-24 19:07:38,802 - INFO -   7. 3_rate_confirmation_5.pdf           Page 1   → rate_confirmation | run1_3_rate_confirmation_5.json
2025-09-24 19:07:38,803 - INFO -   8. 3_rate_confirmation_5.pdf           Page 2   → rate_confirmation | run1_3_rate_confirmation_5.json
2025-09-24 19:07:38,803 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:07:38,803 - INFO - 
✅ Test completed: {'total_files': 5, 'processed': 5, 'failed': 0, 'errors': [], 'duration_seconds': 18.610978, 'processed_files': [{'filename': '3_rate_confirmation_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_1.pdf'}, {'filename': '3_rate_confirmation_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_2.pdf'}, {'filename': '3_rate_confirmation_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_3.pdf'}, {'filename': '3_rate_confirmation_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_4.pdf'}, {'filename': '3_rate_confirmation_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/3_rate_confirmation/3_rate_confirmation_5.pdf'}]}
