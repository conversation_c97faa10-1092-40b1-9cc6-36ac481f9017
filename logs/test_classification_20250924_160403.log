2025-09-24 16:04:03,139 - INFO - Logging initialized. Log file: logs/test_classification_20250924_160403.log
2025-09-24 16:04:03,139 - INFO - 📁 Found 12 files to process
2025-09-24 16:04:03,139 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 16:04:03,139 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-24 16:04:03,139 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-24 16:04:03,140 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-24 16:04:03,140 - INFO - ⬆️ [16:04:03] Uploading: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:05,719 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf -> s3://document-extraction-logistically/temp/00a18760_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:05,720 - INFO - 🔍 [16:04:05] Starting classification: AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:05,721 - INFO - ⬆️ [16:04:05] Uploading: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:05,723 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:05,750 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:05,763 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/00a18760_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:05,763 - INFO - Processing PDF from S3...
2025-09-24 16:04:05,764 - INFO - Downloading PDF from S3 to /tmp/tmp888191iz/00a18760_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:07,736 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:04:07,737 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:07,740 - INFO - Splitting PDF 00a18760_AYEA5J1NILYPMWA7PN4V into 2 pages
2025-09-24 16:04:07,747 - INFO - Split PDF into 2 pages
2025-09-24 16:04:07,748 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:07,748 - INFO - Expected pages: [1, 2]
2025-09-24 16:04:10,298 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf -> s3://document-extraction-logistically/temp/d810cd74_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:10,298 - INFO - 🔍 [16:04:10] Starting classification: C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:10,300 - INFO - ⬆️ [16:04:10] Uploading: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:10,303 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:10,324 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:10,330 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d810cd74_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:10,330 - INFO - Processing PDF from S3...
2025-09-24 16:04:10,330 - INFO - Downloading PDF from S3 to /tmp/tmprzzimmir/d810cd74_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:10,800 - INFO - Page 2: Extracted 216 characters, 11 lines from 00a18760_AYEA5J1NILYPMWA7PN4V_491b0ee5_page_002.pdf
2025-09-24 16:04:10,801 - INFO - Successfully processed page 2
2025-09-24 16:04:13,278 - INFO - Page 1: Extracted 2153 characters, 114 lines from 00a18760_AYEA5J1NILYPMWA7PN4V_491b0ee5_page_001.pdf
2025-09-24 16:04:13,279 - INFO - Successfully processed page 1
2025-09-24 16:04:13,279 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf -> s3://document-extraction-logistically/temp/5ac3d560_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:13,279 - INFO - Combined 2 pages into final text
2025-09-24 16:04:13,279 - INFO - 🔍 [16:04:13] Starting classification: C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:13,279 - INFO - Text validation for 00a18760_AYEA5J1NILYPMWA7PN4V: 2405 characters, 2 pages
2025-09-24 16:04:13,280 - INFO - ⬆️ [16:04:13] Uploading: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:13,280 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:13,292 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:13,293 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:13,294 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:13,298 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ac3d560_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:13,299 - INFO - Processing PDF from S3...
2025-09-24 16:04:13,299 - INFO - Downloading PDF from S3 to /tmp/tmparbt7_m9/5ac3d560_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:14,834 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf -> s3://document-extraction-logistically/temp/b0cb5f92_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:14,834 - INFO - 🔍 [16:04:14] Starting classification: DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:14,835 - INFO - ⬆️ [16:04:14] Uploading: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:14,836 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:14,859 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:14,865 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b0cb5f92_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:14,865 - INFO - Processing PDF from S3...
2025-09-24 16:04:14,865 - INFO - Downloading PDF from S3 to /tmp/tmp_1ro9nmy/b0cb5f92_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:15,609 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:04:15,609 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:15,615 - INFO - Splitting PDF d810cd74_C3I3XLR18U7J6P1N2LZR into 1 pages
2025-09-24 16:04:15,762 - INFO - Split PDF into 1 pages
2025-09-24 16:04:15,762 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:15,762 - INFO - Expected pages: [1]
2025-09-24 16:04:15,901 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg -> s3://document-extraction-logistically/temp/71acfe4a_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:15,902 - INFO - 🔍 [16:04:15] Starting classification: G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:15,903 - INFO - ⬆️ [16:04:15] Uploading: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:15,903 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:15,927 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:15,932 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/71acfe4a_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:15,932 - INFO - Processing image from S3...
2025-09-24 16:04:16,174 - INFO - Downloaded PDF size: 1.4 MB
2025-09-24 16:04:16,174 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:16,174 - INFO - Splitting PDF 5ac3d560_C94JBR3RYYOOM5J2PFUM into 1 pages
2025-09-24 16:04:16,176 - INFO - Split PDF into 1 pages
2025-09-24 16:04:16,176 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:16,176 - INFO - Expected pages: [1]
2025-09-24 16:04:16,542 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf -> s3://document-extraction-logistically/temp/24d23204_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:16,542 - INFO - 🔍 [16:04:16] Starting classification: GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:16,543 - INFO - ⬆️ [16:04:16] Uploading: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:16,545 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:16,551 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:16,553 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/24d23204_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:16,553 - INFO - Processing PDF from S3...
2025-09-24 16:04:16,553 - INFO - Downloading PDF from S3 to /tmp/tmp6ebcmefp/24d23204_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:16,772 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f4ea75a4-6aed-4cf4-962f-6df771812836', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:16 GMT', 'content-type': 'application/json', 'content-length': '2299', 'connection': 'keep-alive', 'x-amzn-requestid': 'f4ea75a4-6aed-4cf4-962f-6df771812836'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be a Sea Freight FCL Delivery Order. That seems like a shipping document. It includes "Sea Freight FCL Delivery Order" header, with shipping details, container info, etc. It\'s like a delivery order, not explicitly one of the enumerated types. Which types could fit: maybe "bol" (Bill of Lading) or "pod" (Proof of Delivery). It\'s more like a Delivery Order, which is a type of shipping instruction, but not listed. Could be "combined_carrier_documents" if it contains multiple docs. But it seems to be one document. "bol" includes "Bill of Lading" header. This one says "Sea Freight FCL Delivery Order" and includes "EBL - Express Bill of Lading" lines and "OCEAN BILL OF LADING" etc. So maybe it\'s a combined document? It contains references to BOLs. But it\'s a delivery order. None of the categories exactly match. But maybe we can consider it not a POD (Proof of Delivery). It\'s not an invoice. It\'s not a rate confirmation. So likely "other". But we might consider "combined_carrier_documents" because the page shows shipping instruction plus references to BOL. But the requirement: "If a page is part of a multi-page single document: each page still gets the same doc_type." So we need one type for both pages.\n\nCheck if page 2 is continuation: yes, page 2 shows "Page 2 of 2" and "END OF DOCUMENT". So page2 is continuation of page1. So same type.\n\nWhich type? We should look at keywords: "Sea Freight FCL Delivery Order", "Delivery Order", "Shipments", "Delivery Notes". Not any of the enumerated types.\n\nThus classify as "other". But check if maybe "combined_carrier_documents" because the page includes multiple distinct headers? It has "Sea Freight FCL Delivery Order" header, and then has BOL references. But no separate header per doc. So likely "other".\n\nThus output two objects: page 1 doc_type other, page 2 doc_type other.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3273, 'outputTokens': 470, 'totalTokens': 3743}, 'metrics': {'latencyMs': 2258}}
2025-09-24 16:04:16,773 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/00a18760_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:17,152 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf -> s3://document-extraction-logistically/temp/e500efe6_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:17,152 - INFO - 🔍 [16:04:17] Starting classification: N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:17,153 - INFO - ⬆️ [16:04:17] Uploading: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:17,154 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:17,176 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:17,179 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e500efe6_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:17,180 - INFO - Processing PDF from S3...
2025-09-24 16:04:17,180 - INFO - Downloading PDF from S3 to /tmp/tmpqtxnqcbl/e500efe6_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:17,589 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 16:04:17,591 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:17,593 - INFO - Splitting PDF b0cb5f92_DY3D94HTH1ZH420GMDO6 into 1 pages
2025-09-24 16:04:17,596 - INFO - Split PDF into 1 pages
2025-09-24 16:04:17,596 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:17,596 - INFO - Expected pages: [1]
2025-09-24 16:04:18,298 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:04:18,299 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:18,301 - INFO - Splitting PDF 24d23204_GJ7Z12W1O0CQO9E9H39H into 2 pages
2025-09-24 16:04:18,319 - INFO - Split PDF into 2 pages
2025-09-24 16:04:18,319 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:18,319 - INFO - Expected pages: [1, 2]
2025-09-24 16:04:18,414 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf -> s3://document-extraction-logistically/temp/923f5ce5_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:18,414 - INFO - 🔍 [16:04:18] Starting classification: NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:18,415 - INFO - ⬆️ [16:04:18] Uploading: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:18,416 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:18,438 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:18,441 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/923f5ce5_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:18,441 - INFO - Processing PDF from S3...
2025-09-24 16:04:18,441 - INFO - Downloading PDF from S3 to /tmp/tmp18dy3x_j/923f5ce5_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:18,948 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:04:18,948 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:18,949 - INFO - Splitting PDF e500efe6_N9XYNNZR54XFORX6RGH2 into 2 pages
2025-09-24 16:04:18,953 - INFO - Split PDF into 2 pages
2025-09-24 16:04:18,953 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:18,953 - INFO - Expected pages: [1, 2]
2025-09-24 16:04:19,036 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf -> s3://document-extraction-logistically/temp/8a510971_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:19,036 - INFO - 🔍 [16:04:19] Starting classification: ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:19,038 - INFO - ⬆️ [16:04:19] Uploading: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:19,041 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:19,062 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:19,066 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8a510971_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:19,066 - INFO - Processing PDF from S3...
2025-09-24 16:04:19,066 - INFO - Downloading PDF from S3 to /tmp/tmphmqeafcf/8a510971_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:19,654 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf -> s3://document-extraction-logistically/temp/ac048253_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:19,655 - INFO - 🔍 [16:04:19] Starting classification: SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:19,655 - INFO - ⬆️ [16:04:19] Uploading: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:19,657 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:19,675 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:19,678 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ac048253_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:19,679 - INFO - Processing PDF from S3...
2025-09-24 16:04:19,679 - INFO - Downloading PDF from S3 to /tmp/tmpmjz8gxns/ac048253_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:20,452 - INFO - S3 Image temp/71acfe4a_G7K3JPMT2OXZWEI7RRNQ.jpg: Extracted 2237 characters, 96 lines
2025-09-24 16:04:20,452 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:20,452 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:20,829 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:04:20,829 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:20,830 - INFO - Splitting PDF 8a510971_ODBS2NBTU6S3GF9JVE6V into 1 pages
2025-09-24 16:04:20,832 - INFO - Split PDF into 1 pages
2025-09-24 16:04:20,832 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:20,832 - INFO - Expected pages: [1]
2025-09-24 16:04:21,037 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:04:21,037 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:21,039 - INFO - Splitting PDF 923f5ce5_NFG5QQXEN21TEZZ9SYPW into 2 pages
2025-09-24 16:04:21,047 - INFO - Split PDF into 2 pages
2025-09-24 16:04:21,048 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:21,048 - INFO - Expected pages: [1, 2]
2025-09-24 16:04:21,255 - INFO - Page 2: Extracted 0 characters, 0 lines from 24d23204_GJ7Z12W1O0CQO9E9H39H_81ba7cf6_page_002.pdf
2025-09-24 16:04:21,255 - INFO - Successfully processed page 2
2025-09-24 16:04:21,458 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:04:21,458 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:21,459 - INFO - Splitting PDF ac048253_SMX8DOTQ89U191SMP0TO into 1 pages
2025-09-24 16:04:21,460 - INFO - Split PDF into 1 pages
2025-09-24 16:04:21,460 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:21,460 - INFO - Expected pages: [1]
2025-09-24 16:04:21,760 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg -> s3://document-extraction-logistically/temp/1490c586_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:21,761 - INFO - 🔍 [16:04:21] Starting classification: Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:21,761 - INFO - ⬆️ [16:04:21] Uploading: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:21,763 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:21,779 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:21,783 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1490c586_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:21,783 - INFO - Processing image from S3...
2025-09-24 16:04:22,400 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf -> s3://document-extraction-logistically/temp/a62acbb0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:22,400 - INFO - 🔍 [16:04:22] Starting classification: Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:22,401 - INFO - Initializing TextractProcessor...
2025-09-24 16:04:22,417 - INFO - Initializing BedrockProcessor...
2025-09-24 16:04:22,423 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a62acbb0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:22,425 - INFO - Processing PDF from S3...
2025-09-24 16:04:22,429 - INFO - Downloading PDF from S3 to /tmp/tmp5pgeh9i5/a62acbb0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:22,462 - INFO - 

AYEA5J1NILYPMWA7PN4V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:04:22,462 - INFO - 

✓ Saved result: output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:04:22,688 - INFO - Page 1: Extracted 1847 characters, 114 lines from d810cd74_C3I3XLR18U7J6P1N2LZR_711b4d48_page_001.pdf
2025-09-24 16:04:22,689 - INFO - Successfully processed page 1
2025-09-24 16:04:22,689 - INFO - Combined 1 pages into final text
2025-09-24 16:04:22,689 - INFO - Text validation for d810cd74_C3I3XLR18U7J6P1N2LZR: 1864 characters, 1 pages
2025-09-24 16:04:22,690 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:22,690 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:22,766 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/00a18760_AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:22,786 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b23289e4-c83e-47b1-851d-2427b4abe127', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:22 GMT', 'content-type': 'application/json', 'content-length': '1254', 'connection': 'keep-alive', 'x-amzn-requestid': 'b23289e4-c83e-47b1-851d-2427b4abe127'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content: Delivery Order. Looks like a Delivery Order document. None of the listed types exactly match "Delivery Order". Maybe it\'s a "Delivery Order" but not in list. Possibly considered as "other". But let\'s see if any type matches: invoice? no. bol? no. pod? no. rate_confirmation? no. clear_to_pay? no. scale_ticket? no. log? maybe? No. fuel_receipt? no. pack_list? no. po? no. customs_doc? no. weight_and_inspection_cert? no. inspection_cert? no. nmfc_cert? no. tender_from_cust? no. so_confirmation? no. ingate/outgate? no. combined_carrier_documents? no. So classification likely other.\n\nHowever, maybe delivery order is a type of "combined_carrier_documents"? But no. It\'s a single document. So "other".\n\nWe must use function call classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "other".\n\nReturn only the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3218, 'outputTokens': 243, 'totalTokens': 3461}, 'metrics': {'latencyMs': 1150}}
2025-09-24 16:04:22,786 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/71acfe4a_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:22,821 - INFO - 

G7K3JPMT2OXZWEI7RRNQ.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 16:04:22,821 - INFO - 

✓ Saved result: output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:04:23,106 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/71acfe4a_G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:23,850 - INFO - Page 2: Extracted 1932 characters, 48 lines from e500efe6_N9XYNNZR54XFORX6RGH2_5483446b_page_002.pdf
2025-09-24 16:04:23,851 - INFO - Successfully processed page 2
2025-09-24 16:04:24,031 - INFO - Page 1: Extracted 1185 characters, 52 lines from b0cb5f92_DY3D94HTH1ZH420GMDO6_76a5694a_page_001.pdf
2025-09-24 16:04:24,032 - INFO - Successfully processed page 1
2025-09-24 16:04:24,032 - INFO - Combined 1 pages into final text
2025-09-24 16:04:24,032 - INFO - Text validation for b0cb5f92_DY3D94HTH1ZH420GMDO6: 1202 characters, 1 pages
2025-09-24 16:04:24,033 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:24,033 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:24,431 - INFO - Page 1: Extracted 3353 characters, 164 lines from 5ac3d560_C94JBR3RYYOOM5J2PFUM_02fdf423_page_001.pdf
2025-09-24 16:04:24,431 - INFO - Successfully processed page 1
2025-09-24 16:04:24,432 - INFO - Combined 1 pages into final text
2025-09-24 16:04:24,432 - INFO - Text validation for 5ac3d560_C94JBR3RYYOOM5J2PFUM: 3370 characters, 1 pages
2025-09-24 16:04:24,454 - INFO - Page 1: Extracted 4034 characters, 142 lines from 24d23204_GJ7Z12W1O0CQO9E9H39H_81ba7cf6_page_001.pdf
2025-09-24 16:04:24,454 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:24,455 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f987dbdc-013f-41ca-a2a7-113f66c9659a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:24 GMT', 'content-type': 'application/json', 'content-length': '583', 'connection': 'keep-alive', 'x-amzn-requestid': 'f987dbdc-013f-41ca-a2a7-113f66c9659a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify into one doc_type. The page content is "Bill of Lading" header and many BOL fields. It\'s clearly a Bill of Lading. No other pages. So output array with one object: page_no 1, doc_type "bol". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3052, 'outputTokens': 85, 'totalTokens': 3137}, 'metrics': {'latencyMs': 556}}
2025-09-24 16:04:24,455 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:24,455 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d810cd74_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:24,456 - INFO - Successfully processed page 1
2025-09-24 16:04:24,460 - INFO - Combined 2 pages into final text
2025-09-24 16:04:24,460 - INFO - Text validation for 24d23204_GJ7Z12W1O0CQO9E9H39H: 4069 characters, 2 pages
2025-09-24 16:04:24,465 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:24,466 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:24,482 - INFO - 

C3I3XLR18U7J6P1N2LZR.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:24,482 - INFO - 

✓ Saved result: output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:04:24,614 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 16:04:24,615 - INFO - Splitting PDF into individual pages...
2025-09-24 16:04:24,618 - INFO - Splitting PDF a62acbb0_Z10BLMPIZ96XLUZ5NUPF into 1 pages
2025-09-24 16:04:24,696 - INFO - Split PDF into 1 pages
2025-09-24 16:04:24,696 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:04:24,696 - INFO - Expected pages: [1]
2025-09-24 16:04:24,766 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d810cd74_C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:25,387 - INFO - Page 1: Extracted 1616 characters, 146 lines from e500efe6_N9XYNNZR54XFORX6RGH2_5483446b_page_001.pdf
2025-09-24 16:04:25,388 - INFO - Successfully processed page 1
2025-09-24 16:04:25,388 - INFO - Combined 2 pages into final text
2025-09-24 16:04:25,388 - INFO - Text validation for e500efe6_N9XYNNZR54XFORX6RGH2: 3584 characters, 2 pages
2025-09-24 16:04:25,388 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:25,388 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:25,941 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '03ecebf4-5807-4001-96b5-bde1b504f810', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:25 GMT', 'content-type': 'application/json', 'content-length': '619', 'connection': 'keep-alive', 'x-amzn-requestid': '03ecebf4-5807-4001-96b5-bde1b504f810'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. There\'s only page1. We need to use tool call classify_logistics_doc_type with array of documents: each object page_no, doc_type.\n\nGiven the content: "BILL OF LADING" header, with shipper, consignee, etc. So doc_type = "bol". No continuation. So output one object page 1 bol.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2781, 'outputTokens': 93, 'totalTokens': 2874}, 'metrics': {'latencyMs': 580}}
2025-09-24 16:04:25,941 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b0cb5f92_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:25,972 - INFO - 

DY3D94HTH1ZH420GMDO6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:25,972 - INFO - 

✓ Saved result: output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:04:26,267 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b0cb5f92_DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:26,803 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '43864159-b325-4831-95ad-ceaf0cacccfa', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:26 GMT', 'content-type': 'application/json', 'content-length': '905', 'connection': 'keep-alive', 'x-amzn-requestid': '43864159-b325-4831-95ad-ceaf0cacccfa'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to produce output via the tool. Identify document type.\n\nPage appears to be an Air Waybill for a shipment. That is a Bill of Lading? For air, it\'s an Air Waybill, which is a type of Bill of Lading. So doc_type should be bol. Confirm? The keywords: "Air Waybill", "Bill of Lading"? It\'s an Air Waybill. The definition of bol: Bill of Lading. But an Air Waybill is similar. So classification: bol.\n\nThus output JSON with documents array containing one object: page_no: 1, doc_type: "bol".\n\nWe have to call the function.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3437, 'outputTokens': 162, 'totalTokens': 3599}, 'metrics': {'latencyMs': 1147}}
2025-09-24 16:04:26,803 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ac3d560_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:26,853 - INFO - 

C94JBR3RYYOOM5J2PFUM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:26,853 - INFO - 

✓ Saved result: output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:04:26,995 - INFO - S3 Image temp/1490c586_Y7P2IPB0S079FZE11EEA.jpg: Extracted 821 characters, 52 lines
2025-09-24 16:04:26,996 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:26,996 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:27,135 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ac3d560_C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:27,314 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '86226ffe-79e4-496a-97c6-9bd02a1ec0b4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:27 GMT', 'content-type': 'application/json', 'content-length': '1326', 'connection': 'keep-alive', 'x-amzn-requestid': '86226ffe-79e4-496a-97c6-9bd02a1ec0b4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages: page1 has a Bill of Lading. The header: "Conocimiento de embarque recto - Original". That\'s Spanish for "Straight Bill of Lading - Original". So the doc_type is "bol". Page2 is empty? The input shows <page2></page2> meaning likely a blank page. According to instructions: For every page, return exactly one object. For page2, as it\'s empty, we need to decide: It might be a continuation of the previous page? There\'s no "continued" text. It\'s blank, could be a blank page in a multipage doc. Since it\'s part of the same document, we should assign same doc_type as previous page. The rule: "If document is not from any of categories mentioned, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign the same doc_type as previous page." A blank page likely continuation. So page2 should be bol as well. So produce two objects: page_no 1 and 2, both doc_type bol. Let\'s output via function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3577, 'outputTokens': 253, 'totalTokens': 3830}, 'metrics': {'latencyMs': 1640}}
2025-09-24 16:04:27,315 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/24d23204_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:27,362 - INFO - 

GJ7Z12W1O0CQO9E9H39H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:27,362 - INFO - 

✓ Saved result: output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:04:27,500 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ad5e5a35-cd34-4ff4-ae3d-639c943b8eb7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:27 GMT', 'content-type': 'application/json', 'content-length': '916', 'connection': 'keep-alive', 'x-amzn-requestid': 'ad5e5a35-cd34-4ff4-ae3d-639c943b8eb7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be a Bill of Lading. Page 1 contains header "BILL OF LADING" and many details. Page 2 continues the BOL. It\'s likely page 2 is continuation. We need to output tool call classify_logistics_doc_type with array of objects: page_no and doc_type for each page.\n\nDoc type: bol. I\'m confident it\'s bol. There\'s no other document headers. So output page1 bol, page2 bol.\n\nCheck if page 2 is continuation: page 2 starts with "1" maybe page number? It is continuation. So same doc_type.\n\nReturn JSON accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3549, 'outputTokens': 161, 'totalTokens': 3710}, 'metrics': {'latencyMs': 950}}
2025-09-24 16:04:27,500 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e500efe6_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:27,609 - INFO - Page 1: Extracted 2057 characters, 76 lines from 8a510971_ODBS2NBTU6S3GF9JVE6V_1a3d3404_page_001.pdf
2025-09-24 16:04:27,609 - INFO - Successfully processed page 1
2025-09-24 16:04:27,615 - INFO - Combined 1 pages into final text
2025-09-24 16:04:27,622 - INFO - Page 1: Extracted 2916 characters, 126 lines from ac048253_SMX8DOTQ89U191SMP0TO_22b5fdcb_page_001.pdf
2025-09-24 16:04:27,622 - INFO - Text validation for 8a510971_ODBS2NBTU6S3GF9JVE6V: 2074 characters, 1 pages
2025-09-24 16:04:27,622 - INFO - Successfully processed page 1
2025-09-24 16:04:27,622 - INFO - Combined 1 pages into final text
2025-09-24 16:04:27,623 - INFO - Text validation for ac048253_SMX8DOTQ89U191SMP0TO: 2933 characters, 1 pages
2025-09-24 16:04:27,623 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:27,623 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:27,624 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:27,624 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:27,670 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/24d23204_GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:27,722 - INFO - 

N9XYNNZR54XFORX6RGH2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:27,722 - INFO - 

✓ Saved result: output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:04:28,031 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e500efe6_N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:28,153 - INFO - Page 2: Extracted 1028 characters, 92 lines from 923f5ce5_NFG5QQXEN21TEZZ9SYPW_56845757_page_002.pdf
2025-09-24 16:04:28,153 - INFO - Successfully processed page 2
2025-09-24 16:04:28,739 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3f06b7d0-66c5-49a8-92af-8b42149674e1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:28 GMT', 'content-type': 'application/json', 'content-length': '652', 'connection': 'keep-alive', 'x-amzn-requestid': '3f06b7d0-66c5-49a8-92af-8b42149674e1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page provided page1. We must analyze. It says "BILL OF LADING" header. So doc_type should be bol. Also page contains PO #15667, but that is purchase order number within the BOL. So doc_type: bol. Output via tool call.'}}}, {'toolUse': {'toolUseId': 'tooluse_H5vxHipXTKyEX0HcBRY8VA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2751, 'outputTokens': 82, 'totalTokens': 2833}, 'metrics': {'latencyMs': 586}}
2025-09-24 16:04:28,739 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1490c586_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:28,766 - INFO - 

Y7P2IPB0S079FZE11EEA.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:28,767 - INFO - 

✓ Saved result: output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:04:28,811 - INFO - Page 1: Extracted 1621 characters, 166 lines from 923f5ce5_NFG5QQXEN21TEZZ9SYPW_56845757_page_001.pdf
2025-09-24 16:04:28,811 - INFO - Successfully processed page 1
2025-09-24 16:04:28,811 - INFO - Combined 2 pages into final text
2025-09-24 16:04:28,812 - INFO - Text validation for 923f5ce5_NFG5QQXEN21TEZZ9SYPW: 2685 characters, 2 pages
2025-09-24 16:04:28,812 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:28,812 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:29,059 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1490c586_Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:29,372 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '14997508-79dc-4569-91b1-607325449ba6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:29 GMT', 'content-type': 'application/json', 'content-length': '533', 'connection': 'keep-alive', 'x-amzn-requestid': '14997508-79dc-4569-91b1-607325449ba6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. The page contains "VICs Bill of Lading - Order" header. That indicates Bill of Lading. So doc_type = "bol". Output JSON via tool call with page_no 1 and doc_type bol.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3250, 'outputTokens': 74, 'totalTokens': 3324}, 'metrics': {'latencyMs': 509}}
2025-09-24 16:04:29,373 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ac048253_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:29,405 - INFO - 

SMX8DOTQ89U191SMP0TO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:29,405 - INFO - 

✓ Saved result: output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:04:29,718 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ac048253_SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:30,851 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '980a1f6d-24d2-420e-b70f-e7ff931d48a1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:30 GMT', 'content-type': 'application/json', 'content-length': '709', 'connection': 'keep-alive', 'x-amzn-requestid': '980a1f6d-24d2-420e-b70f-e7ff931d48a1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Both pages appear to be a "PKG LIST" i.e., packing list. They have header "PKG LIST". No other document type. So doc_type = pack_list. Both pages part of same multi-page document, page 2 is continuation. So page1 and page2 both pack_list.\n\nReturn array of objects: {page_no:1,doc_type:"pack_list"} etc. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3730, 'outputTokens': 119, 'totalTokens': 3849}, 'metrics': {'latencyMs': 689}}
2025-09-24 16:04:30,851 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/923f5ce5_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:30,902 - INFO - 

NFG5QQXEN21TEZZ9SYPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:04:30,902 - INFO - 

✓ Saved result: output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:04:31,187 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/923f5ce5_NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:32,257 - INFO - Page 1: Extracted 3550 characters, 185 lines from a62acbb0_Z10BLMPIZ96XLUZ5NUPF_f9793eb5_page_001.pdf
2025-09-24 16:04:32,257 - INFO - Successfully processed page 1
2025-09-24 16:04:32,258 - INFO - Combined 1 pages into final text
2025-09-24 16:04:32,258 - INFO - Text validation for a62acbb0_Z10BLMPIZ96XLUZ5NUPF: 3567 characters, 1 pages
2025-09-24 16:04:32,258 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:04:32,258 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:04:32,929 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '67431919-df11-45a4-a8cb-0ba92f3679ee', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:32 GMT', 'content-type': 'application/json', 'content-length': '4838', 'connection': 'keep-alive', 'x-amzn-requestid': '67431919-df11-45a4-a8cb-0ba92f3679ee'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The page content appears to be a Load Confirmation. It has "Load Confirmation # 1536427", "In order to receive payment for this load, this confirmation must be signed and returned." It also has rates, etc. This seems like a "Load Confirmation" or "Load Tender" type? But the categories we have: tender_from_cust — Load Tender from Customer. The page says "Load Confirmation #...". It includes booking number, purchase order, etc. It is a confirmation that the carrier has valid contract. Is this a "tender_from_cust"? Typically a load tender is from customer to carrier. This might be the tender. The doc type "tender_from_cust" is defined as "Customer\'s load tender or request to a carrier to transport a load." The page contains "Load Confirmation # 1536427" and "Brokered To: THREE-D BROKERS LTD." It seems like a load confirmation that the carrier has to sign. This is likely a tender.\n\nOther possibilities: "rate_confirmation" or "cust_rate_confirmation". Rate confirmation is a document from carrier confirming rate/terms. This has "Agreed Rate" and "LINEHAUL 1,900.00". It includes "Load Confirmation #". The line "In order to receive payment for this load, this confirmation must be signed and returned...". That is like a confirmation that the carrier has to sign to confirm the load. That might be a "Load Confirmation" or "Rate Confirmation". But "rate_confirmation" is defined as "Agreement from a carrier confirming rate/terms for a specific load." This page has "Agreed Rate ... Total $1,900.00". So it might fit rate_confirmation. But "tender_from_cust" is a load tender. The page: "Load Confirmation # 1536427" and "Brokered To: THREE-D BROKERS LTD." The "Brokered To" indicates the broker. The page includes the carrier\'s details? It has "Driver:" but not name. It has truck. It\'s basically a "Load Confirmation" that the carrier must sign to confirm. That might be an "invoice" but no invoice. It\'s not an invoice. It\'s not a BOL. Not POD. It\'s not a combined. Not a purchase order. It\'s not a packing list. It\'s not a log. Not a fuel receipt. Not customs. So likely "tender_from_cust" or "rate_confirmation".\n\nCheck definitions: "tender_from_cust — Load Tender from Customer" is a request to a carrier to transport a load. The page appears to be a confirmation that the load will be accepted and the carrier must sign. It is a load confirmation, not a tender. The tender would be earlier. The "Load Confirmation" is a signed document to confirm the load. That might be a "tender" type. The question: This is likely a "tender_from_cust" because it originates from a customer to a carrier. But the page says "Brokered To: THREE-D BROKERS LTD." So the customer is the broker? The page is probably from the broker to the carrier. The broker is confirming the load to the carrier. So it\'s a tender? The load tender is a request to a carrier to transport a load. The page includes "Load Confirmation #1536427". The question: The document type "Load Confirmation" is not in the list. The closest might be "tender_from_cust" or "rate_confirmation". Let\'s differentiate: "rate_confirmation" is from carrier confirming rates. Typically the carrier sends a rate confirmation to the customer. This page includes "In order to receive payment for this load, this confirmation must be signed and returned." The "rate confirmation" would be signed by the carrier to confirm the rate. The page includes rate. So yes, it\'s a rate confirmation. But the text says "this confirmation must be signed and returned." The "rate confirmation" might be signed by the carrier to confirm the rate. The document appears to be from the carrier/ broker to the carrier? It\'s ambiguous.\n\nGiven the definitions, "rate_confirmation" is an agreement from a carrier confirming rate/terms. This page includes "Agreed Rate" and "Total $1,900.00". It also says "A copy of the original bills must be provided with your invoice." That is typical of a rate confirmation. Also "Print Name, Signature, Date" suggests the carrier signs to confirm. So I\'d classify as "rate_confirmation". Alternatively, "tender_from_cust" would be a tender. The page includes "Load Confirmation #". Tender might not include total rate. But the page includes rate. So "rate_confirmation" is more accurate.\n\nThus classification: page 1 is "rate_confirmation".\n\nWe must output using the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3038, 'outputTokens': 1015, 'totalTokens': 4053}, 'metrics': {'latencyMs': 4036}}
2025-09-24 16:04:32,929 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8a510971_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:32,967 - INFO - 

ODBS2NBTU6S3GF9JVE6V.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 16:04:32,967 - INFO - 

✓ Saved result: output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:04:33,330 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8a510971_ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:34,209 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e6af9982-bb88-402a-ad9c-5cbfe74d2701', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:34:34 GMT', 'content-type': 'application/json', 'content-length': '726', 'connection': 'keep-alive', 'x-amzn-requestid': 'e6af9982-bb88-402a-ad9c-5cbfe74d2701'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page here page1. We need to decide which doc_type matches. Let\'s analyze content. It says "BILL OF LADING" header at top. The page includes a BOL details: Bill of Lading number, consignor, consignee, vessel, order no, etc. So doc_type is "bol". It\'s a Bill of Lading. There\'s no other header. So output: page_no 1, doc_type "bol". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3722, 'outputTokens': 124, 'totalTokens': 3846}, 'metrics': {'latencyMs': 788}}
2025-09-24 16:04:34,210 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a62acbb0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:34,259 - INFO - 

Z10BLMPIZ96XLUZ5NUPF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:04:34,259 - INFO - 

✓ Saved result: output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:04:34,566 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a62acbb0_Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:34,568 - INFO - 
📊 Processing Summary:
2025-09-24 16:04:34,568 - INFO -    Total files: 12
2025-09-24 16:04:34,568 - INFO -    Successful: 12
2025-09-24 16:04:34,568 - INFO -    Failed: 0
2025-09-24 16:04:34,568 - INFO -    Duration: 31.43 seconds
2025-09-24 16:04:34,569 - INFO -    Output directory: output
2025-09-24 16:04:34,569 - INFO - 
📋 Successfully Processed Files:
2025-09-24 16:04:34,569 - INFO -    📄 AYEA5J1NILYPMWA7PN4V.pdf: {"documents":[{"page_no":1,"doc_type":"other"},{"page_no":2,"doc_type":"other"}]}
2025-09-24 16:04:34,569 - INFO -    📄 C3I3XLR18U7J6P1N2LZR.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:04:34,569 - INFO -    📄 C94JBR3RYYOOM5J2PFUM.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:04:34,569 - INFO -    📄 DY3D94HTH1ZH420GMDO6.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:04:34,569 - INFO -    📄 G7K3JPMT2OXZWEI7RRNQ.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 16:04:34,569 - INFO -    📄 GJ7Z12W1O0CQO9E9H39H.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:04:34,569 - INFO -    📄 N9XYNNZR54XFORX6RGH2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:04:34,569 - INFO -    📄 NFG5QQXEN21TEZZ9SYPW.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-24 16:04:34,569 - INFO -    📄 ODBS2NBTU6S3GF9JVE6V.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"}]}
2025-09-24 16:04:34,569 - INFO -    📄 SMX8DOTQ89U191SMP0TO.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:04:34,570 - INFO -    📄 Y7P2IPB0S079FZE11EEA.jpg: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:04:34,570 - INFO -    📄 Z10BLMPIZ96XLUZ5NUPF.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:04:34,570 - INFO - 
============================================================================================================================================
2025-09-24 16:04:34,570 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 16:04:34,570 - INFO - ============================================================================================================================================
2025-09-24 16:04:34,571 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 16:04:34,571 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:04:34,571 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           1      other                                    run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:04:34,571 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:34,571 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:04:34,571 - INFO - 
2025-09-24 16:04:34,571 - INFO - AYEA5J1NILYPMWA7PN4V.pdf                           2      other                                    run1_AYEA5J1NILYPMWA7PN4V.json                                                  
2025-09-24 16:04:34,571 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf
2025-09-24 16:04:34,571 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:04:34,571 - INFO - 
2025-09-24 16:04:34,571 - INFO - C3I3XLR18U7J6P1N2LZR.pdf                           1      bol                                      run1_C3I3XLR18U7J6P1N2LZR.json                                                  
2025-09-24 16:04:34,571 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf
2025-09-24 16:04:34,571 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:04:34,571 - INFO - 
2025-09-24 16:04:34,571 - INFO - C94JBR3RYYOOM5J2PFUM.pdf                           1      bol                                      run1_C94JBR3RYYOOM5J2PFUM.json                                                  
2025-09-24 16:04:34,571 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf
2025-09-24 16:04:34,571 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:04:34,572 - INFO - 
2025-09-24 16:04:34,572 - INFO - DY3D94HTH1ZH420GMDO6.pdf                           1      bol                                      run1_DY3D94HTH1ZH420GMDO6.json                                                  
2025-09-24 16:04:34,572 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf
2025-09-24 16:04:34,572 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:04:34,572 - INFO - 
2025-09-24 16:04:34,572 - INFO - G7K3JPMT2OXZWEI7RRNQ.jpg                           1      other                                    run1_G7K3JPMT2OXZWEI7RRNQ.json                                                  
2025-09-24 16:04:34,572 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg
2025-09-24 16:04:34,572 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:04:34,572 - INFO - 
2025-09-24 16:04:34,572 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           1      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:04:34,572 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:34,572 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:04:34,572 - INFO - 
2025-09-24 16:04:34,572 - INFO - GJ7Z12W1O0CQO9E9H39H.pdf                           2      bol                                      run1_GJ7Z12W1O0CQO9E9H39H.json                                                  
2025-09-24 16:04:34,572 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf
2025-09-24 16:04:34,573 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:04:34,573 - INFO - 
2025-09-24 16:04:34,573 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           1      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:04:34,573 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:34,573 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:04:34,573 - INFO - 
2025-09-24 16:04:34,573 - INFO - N9XYNNZR54XFORX6RGH2.pdf                           2      bol                                      run1_N9XYNNZR54XFORX6RGH2.json                                                  
2025-09-24 16:04:34,573 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf
2025-09-24 16:04:34,573 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:04:34,573 - INFO - 
2025-09-24 16:04:34,573 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           1      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:04:34,573 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:34,573 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:04:34,573 - INFO - 
2025-09-24 16:04:34,573 - INFO - NFG5QQXEN21TEZZ9SYPW.pdf                           2      pack_list                                run1_NFG5QQXEN21TEZZ9SYPW.json                                                  
2025-09-24 16:04:34,573 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf
2025-09-24 16:04:34,574 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:04:34,574 - INFO - 
2025-09-24 16:04:34,574 - INFO - ODBS2NBTU6S3GF9JVE6V.pdf                           1      rate_confirmation                        run1_ODBS2NBTU6S3GF9JVE6V.json                                                  
2025-09-24 16:04:34,574 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf
2025-09-24 16:04:34,574 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:04:34,574 - INFO - 
2025-09-24 16:04:34,574 - INFO - SMX8DOTQ89U191SMP0TO.pdf                           1      bol                                      run1_SMX8DOTQ89U191SMP0TO.json                                                  
2025-09-24 16:04:34,574 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf
2025-09-24 16:04:34,574 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:04:34,574 - INFO - 
2025-09-24 16:04:34,574 - INFO - Y7P2IPB0S079FZE11EEA.jpg                           1      bol                                      run1_Y7P2IPB0S079FZE11EEA.json                                                  
2025-09-24 16:04:34,574 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg
2025-09-24 16:04:34,574 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:04:34,574 - INFO - 
2025-09-24 16:04:34,574 - INFO - Z10BLMPIZ96XLUZ5NUPF.pdf                           1      bol                                      run1_Z10BLMPIZ96XLUZ5NUPF.json                                                  
2025-09-24 16:04:34,574 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf
2025-09-24 16:04:34,574 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:04:34,574 - INFO - 
2025-09-24 16:04:34,575 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:04:34,575 - INFO - Total entries: 16
2025-09-24 16:04:34,575 - INFO - ============================================================================================================================================
2025-09-24 16:04:34,575 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 16:04:34,575 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:04:34,575 - INFO -   1. AYEA5J1NILYPMWA7PN4V.pdf            Page 1   → other           | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:04:34,575 - INFO -   2. AYEA5J1NILYPMWA7PN4V.pdf            Page 2   → other           | run1_AYEA5J1NILYPMWA7PN4V.json
2025-09-24 16:04:34,575 - INFO -   3. C3I3XLR18U7J6P1N2LZR.pdf            Page 1   → bol             | run1_C3I3XLR18U7J6P1N2LZR.json
2025-09-24 16:04:34,575 - INFO -   4. C94JBR3RYYOOM5J2PFUM.pdf            Page 1   → bol             | run1_C94JBR3RYYOOM5J2PFUM.json
2025-09-24 16:04:34,575 - INFO -   5. DY3D94HTH1ZH420GMDO6.pdf            Page 1   → bol             | run1_DY3D94HTH1ZH420GMDO6.json
2025-09-24 16:04:34,575 - INFO -   6. G7K3JPMT2OXZWEI7RRNQ.jpg            Page 1   → other           | run1_G7K3JPMT2OXZWEI7RRNQ.json
2025-09-24 16:04:34,575 - INFO -   7. GJ7Z12W1O0CQO9E9H39H.pdf            Page 1   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:04:34,575 - INFO -   8. GJ7Z12W1O0CQO9E9H39H.pdf            Page 2   → bol             | run1_GJ7Z12W1O0CQO9E9H39H.json
2025-09-24 16:04:34,575 - INFO -   9. N9XYNNZR54XFORX6RGH2.pdf            Page 1   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:04:34,575 - INFO -  10. N9XYNNZR54XFORX6RGH2.pdf            Page 2   → bol             | run1_N9XYNNZR54XFORX6RGH2.json
2025-09-24 16:04:34,576 - INFO -  11. NFG5QQXEN21TEZZ9SYPW.pdf            Page 1   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:04:34,576 - INFO -  12. NFG5QQXEN21TEZZ9SYPW.pdf            Page 2   → pack_list       | run1_NFG5QQXEN21TEZZ9SYPW.json
2025-09-24 16:04:34,576 - INFO -  13. ODBS2NBTU6S3GF9JVE6V.pdf            Page 1   → rate_confirmation | run1_ODBS2NBTU6S3GF9JVE6V.json
2025-09-24 16:04:34,576 - INFO -  14. SMX8DOTQ89U191SMP0TO.pdf            Page 1   → bol             | run1_SMX8DOTQ89U191SMP0TO.json
2025-09-24 16:04:34,576 - INFO -  15. Y7P2IPB0S079FZE11EEA.jpg            Page 1   → bol             | run1_Y7P2IPB0S079FZE11EEA.json
2025-09-24 16:04:34,576 - INFO -  16. Z10BLMPIZ96XLUZ5NUPF.pdf            Page 1   → bol             | run1_Z10BLMPIZ96XLUZ5NUPF.json
2025-09-24 16:04:34,576 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:04:34,576 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 31.428466, 'processed_files': [{'filename': 'AYEA5J1NILYPMWA7PN4V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/AYEA5J1NILYPMWA7PN4V.pdf'}, {'filename': 'C3I3XLR18U7J6P1N2LZR.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C3I3XLR18U7J6P1N2LZR.pdf'}, {'filename': 'C94JBR3RYYOOM5J2PFUM.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/C94JBR3RYYOOM5J2PFUM.pdf'}, {'filename': 'DY3D94HTH1ZH420GMDO6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/DY3D94HTH1ZH420GMDO6.pdf'}, {'filename': 'G7K3JPMT2OXZWEI7RRNQ.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/G7K3JPMT2OXZWEI7RRNQ.jpg'}, {'filename': 'GJ7Z12W1O0CQO9E9H39H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/GJ7Z12W1O0CQO9E9H39H.pdf'}, {'filename': 'N9XYNNZR54XFORX6RGH2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/N9XYNNZR54XFORX6RGH2.pdf'}, {'filename': 'NFG5QQXEN21TEZZ9SYPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/NFG5QQXEN21TEZZ9SYPW.pdf'}, {'filename': 'ODBS2NBTU6S3GF9JVE6V.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/ODBS2NBTU6S3GF9JVE6V.pdf'}, {'filename': 'SMX8DOTQ89U191SMP0TO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/SMX8DOTQ89U191SMP0TO.pdf'}, {'filename': 'Y7P2IPB0S079FZE11EEA.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Y7P2IPB0S079FZE11EEA.jpg'}, {'filename': 'Z10BLMPIZ96XLUZ5NUPF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/bol/Z10BLMPIZ96XLUZ5NUPF.pdf'}]}
