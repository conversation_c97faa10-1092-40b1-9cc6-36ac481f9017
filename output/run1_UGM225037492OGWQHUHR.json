{"classification_result": {"documents": [{"page_no": 1, "doc_type": "customs_doc"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9979511499404907, "Height": 0.9981369972229004, "Left": 0.00044054019963368773, "Top": 0.0018629822880029678}, "Polygon": [{"X": 0.00044054019963368773, "Y": 0.0018674647435545921}, {"X": 0.9983879327774048, "Y": 0.0018629822880029678}, {"X": 0.9983916878700256, "Y": 1.0}, {"X": 0.00045883626444265246, "Y": 1.0}]}, "Id": "3f785219-0d7b-4ee3-b020-21ab5145c50f", "Relationships": [{"Type": "CHILD", "Ids": ["84f8a973-a269-4f33-9dfb-8ecd5aed5948", "d08ef8bc-8db7-41f5-9a8d-9bde7fdd2e34", "04615f6f-1313-4a5d-9297-8ae8abd85ccf", "ced636d6-575b-4a96-a830-e376a0c86a80", "6e2b2545-1598-424a-9005-9fc16f8d99ea", "d7e71104-3139-4fb6-9450-fe36cd8a9a67", "ef510d09-5298-4314-8516-99a5d0c0390c", "0005ae10-8d93-4535-90eb-5c8b65daa981", "79b0104d-6d24-4fc8-b497-f7a2f32bad1e", "0998ed81-11bb-43c5-9d02-8ce4b46c0853", "d882974c-4bac-4aa0-b9e1-9288f14f72b2", "1dd719b9-c046-470c-a74c-860b1839830e", "deabccd4-e0ce-4a3e-b4ab-ac556fa375f7", "4c23739a-0891-4d10-a2aa-ddd404c04e75", "54d5f2aa-abd2-41b7-a6f2-dd4fb1cb6016", "93db04c3-b133-4474-b6f7-60c26e5a216a", "8338f30b-1a68-423a-8ebe-08cb1ed04dec", "f83716da-bf46-44c1-a474-7cc9e0a618ff"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "Georgia Ports Authority, Savan", "Geometry": {"BoundingBox": {"Width": 0.11346086114645004, "Height": 0.04388951510190964, "Left": 0.1262177973985672, "Top": 0.04075723513960838}, "Polygon": [{"X": 0.1262177973985672, "Y": 0.04075770080089569}, {"X": 0.2396780103445053, "Y": 0.04075723513960838}, {"X": 0.23967866599559784, "Y": 0.0846463292837143}, {"X": 0.1262185126543045, "Y": 0.08464674651622772}]}, "Id": "84f8a973-a269-4f33-9dfb-8ecd5aed5948", "Relationships": [{"Type": "CHILD", "Ids": ["a7bd27bd-440d-4ec2-b39e-b4adf4145728", "ab9b43b7-1600-44af-af14-855471e8ddc7", "7a9f4749-a4ce-4ed5-a358-014d27cb42c6", "f6df5beb-ffef-49c0-bffc-bc987f2e6a0c"]}]}, {"BlockType": "LINE", "Confidence": 99.97342681884766, "Text": "Deliver Import Container", "Geometry": {"BoundingBox": {"Width": 0.09255275130271912, "Height": 0.04344771429896355, "Left": 0.6050004959106445, "Top": 0.04003208875656128}, "Polygon": [{"X": 0.6050004959106445, "Y": 0.04003247246146202}, {"X": 0.6975529193878174, "Y": 0.04003208875656128}, {"X": 0.6975532174110413, "Y": 0.08347945660352707}, {"X": 0.605000913143158, "Y": 0.08347979933023453}]}, "Id": "d08ef8bc-8db7-41f5-9a8d-9bde7fdd2e34", "Relationships": [{"Type": "CHILD", "Ids": ["f46852df-352a-419a-9c22-689bdfc81341", "cc264cf3-8540-4430-a1d8-f84823524030", "8e38b3f6-56c7-4eee-bf08-e3f540e8b43a"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "3501361", "Geometry": {"BoundingBox": {"Width": 0.03248152509331703, "Height": 0.03407321870326996, "Left": 0.8985869884490967, "Top": 0.04224210977554321}, "Polygon": [{"X": 0.8985869884490967, "Y": 0.04224224388599396}, {"X": 0.9310683012008667, "Y": 0.04224210977554321}, {"X": 0.931068480014801, "Y": 0.07631520181894302}, {"X": 0.8985871076583862, "Y": 0.07631532847881317}]}, "Id": "04615f6f-1313-4a5d-9297-8ae8abd85ccf", "Relationships": [{"Type": "CHILD", "Ids": ["33fada65-a494-430f-86a4-ddea8b6e49f9"]}]}, {"BlockType": "LINE", "Confidence": 99.66238403320312, "Text": "EVE:EVERGREEN SHIPPING AGENCY", "Geometry": {"BoundingBox": {"Width": 0.13518808782100677, "Height": 0.03423425927758217, "Left": 0.12636008858680725, "Top": 0.13231995701789856}, "Polygon": [{"X": 0.12636008858680725, "Y": 0.13232038915157318}, {"X": 0.26154768466949463, "Y": 0.13231995701789856}, {"X": 0.2615481913089752, "Y": 0.1665538102388382}, {"X": 0.12636065483093262, "Y": 0.16655421257019043}]}, "Id": "ced636d6-575b-4a96-a830-e376a0c86a80", "Relationships": [{"Type": "CHILD", "Ids": ["7318a237-e246-499b-aa49-8f2c439e74cf", "0f0fb2b2-603b-4be5-9122-5df8725ab0d5", "c27a83eb-8c63-4ea2-adb2-aec2da38b171"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "DRJK", "Geometry": {"BoundingBox": {"Width": 0.019104275852441788, "Height": 0.03405361995100975, "Left": 0.6049544811248779, "Top": 0.2228802889585495}, "Polygon": [{"X": 0.6049544811248779, "Y": 0.22288033366203308}, {"X": 0.6240584850311279, "Y": 0.2228802889585495}, {"X": 0.6240587830543518, "Y": 0.25693386793136597}, {"X": 0.6049548387527466, "Y": 0.25693389773368835}]}, "Id": "6e2b2545-1598-424a-9005-9fc16f8d99ea", "Relationships": [{"Type": "CHILD", "Ids": ["77e0089f-6d77-4061-bef7-66a0635d639c"]}]}, {"BlockType": "LINE", "Confidence": 99.81893920898438, "Text": "01-Mar-2023 06:39:43", "Geometry": {"BoundingBox": {"Width": 0.0853411853313446, "Height": 0.03449476510286331, "Left": 0.8981427550315857, "Top": 0.2231942117214203}, "Polygon": [{"X": 0.8981427550315857, "Y": 0.223194420337677}, {"X": 0.9834837913513184, "Y": 0.2231942117214203}, {"X": 0.9834839105606079, "Y": 0.2576887905597687}, {"X": 0.89814293384552, "Y": 0.257688969373703}]}, "Id": "d7e71104-3139-4fb6-9450-fe36cd8a9a67", "Relationships": [{"Type": "CHILD", "Ids": ["db7416f8-9948-4334-9e0d-c60cb165f8e4", "38e0a7a9-05aa-4e1c-aac1-f6a492e7c59a"]}]}, {"BlockType": "LINE", "Confidence": 99.9462890625, "Text": "Truck ID", "Geometry": {"BoundingBox": {"Width": 0.03178903087973595, "Height": 0.035489488393068314, "Left": 0.47984427213668823, "Top": 0.3116055428981781}, "Polygon": [{"X": 0.47984427213668823, "Y": 0.3116056025028229}, {"X": 0.5116329193115234, "Y": 0.3116055428981781}, {"X": 0.5116332769393921, "Y": 0.34709498286247253}, {"X": 0.4798446595668793, "Y": 0.3470950424671173}]}, "Id": "ef510d09-5298-4314-8516-99a5d0c0390c", "Relationships": [{"Type": "CHILD", "Ids": ["8999d13f-62f9-4839-982e-e66005c46132", "264ed1ad-fa66-4fe3-a8a2-58ee51a405a8"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Container", "Geometry": {"BoundingBox": {"Width": 0.03607495501637459, "Height": 0.035042956471443176, "Left": 0.0016393099213019013, "Top": 0.40270039439201355}, "Polygon": [{"X": 0.0016393099213019013, "Y": 0.40270039439201355}, {"X": 0.03771364316344261, "Y": 0.40270039439201355}, {"X": 0.037714265286922455, "Y": 0.43774333596229553}, {"X": 0.0016399504384025931, "Y": 0.43774333596229553}]}, "Id": "0005ae10-8d93-4535-90eb-5c8b65daa981", "Relationships": [{"Type": "CHILD", "Ids": ["ac6fe7cb-9442-40a3-9c3d-c4deb6644e9c"]}]}, {"BlockType": "LINE", "Confidence": 99.81205749511719, "Text": "EGHU9787630", "Geometry": {"BoundingBox": {"Width": 0.05354415252804756, "Height": 0.034645747393369675, "Left": 0.12639091908931732, "Top": 0.40304359793663025}, "Polygon": [{"X": 0.12639091908931732, "Y": 0.40304362773895264}, {"X": 0.1799345314502716, "Y": 0.40304359793663025}, {"X": 0.17993506789207458, "Y": 0.43768930435180664}, {"X": 0.1263914853334427, "Y": 0.43768933415412903}]}, "Id": "79b0104d-6d24-4fc8-b497-f7a2f32bad1e", "Relationships": [{"Type": "CHILD", "Ids": ["e05146a6-969e-48dc-bddf-6f4ea8d630cb"]}]}, {"BlockType": "LINE", "Confidence": 99.80388641357422, "Text": "Size/Type", "Geometry": {"BoundingBox": {"Width": 0.03614405542612076, "Height": 0.04284530505537987, "Left": 0.48021477460861206, "Top": 0.4016912877559662}, "Polygon": [{"X": 0.48021477460861206, "Y": 0.4016913175582886}, {"X": 0.5163583755493164, "Y": 0.4016912877559662}, {"X": 0.5163588523864746, "Y": 0.44453656673431396}, {"X": 0.48021528124809265, "Y": 0.44453659653663635}]}, "Id": "0998ed81-11bb-43c5-9d02-8ce4b46c0853", "Relationships": [{"Type": "CHILD", "Ids": ["31a3bc7e-e2ff-4e6b-a90e-62478f2c07ad"]}]}, {"BlockType": "LINE", "Confidence": 99.765625, "Text": "40_DRY_9.5", "Geometry": {"BoundingBox": {"Width": 0.04607405140995979, "Height": 0.04290961101651192, "Left": 0.6044583916664124, "Top": 0.4023168683052063}, "Polygon": [{"X": 0.6044583916664124, "Y": 0.4023168981075287}, {"X": 0.650532066822052, "Y": 0.4023168683052063}, {"X": 0.6505324244499207, "Y": 0.4452264606952667}, {"X": 0.6044588088989258, "Y": 0.4452264904975891}]}, "Id": "d882974c-4bac-4aa0-b9e1-9288f14f72b2", "Relationships": [{"Type": "CHILD", "Ids": ["dbeced81-7315-4025-bd25-89b8d1ea8925"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.02789592184126377, "Height": 0.03544527292251587, "Left": 0.0017841283697634935, "Top": 0.4918473958969116}, "Polygon": [{"X": 0.0017841283697634935, "Y": 0.4918473958969116}, {"X": 0.029679417610168457, "Y": 0.4918473958969116}, {"X": 0.029680050909519196, "Y": 0.5272926688194275}, {"X": 0.0017847762210294604, "Y": 0.5272926688194275}]}, "Id": "1dd719b9-c046-470c-a74c-860b1839830e", "Relationships": [{"Type": "CHILD", "Ids": ["265f55ba-4f10-4a88-a0c6-f2d1f10932b9"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "592919", "Geometry": {"BoundingBox": {"Width": 0.027692610397934914, "Height": 0.034773074090480804, "Left": 0.1263333410024643, "Top": 0.49325624108314514}, "Polygon": [{"X": 0.1263333410024643, "Y": 0.49325624108314514}, {"X": 0.1540253907442093, "Y": 0.49325624108314514}, {"X": 0.15402595698833466, "Y": 0.5280293226242065}, {"X": 0.12633390724658966, "Y": 0.5280293226242065}]}, "Id": "deabccd4-e0ce-4a3e-b4ab-ac556fa375f7", "Relationships": [{"Type": "CHILD", "Ids": ["3b400ea8-be39-4a22-9c41-75170d5efdd1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Booking", "Geometry": {"BoundingBox": {"Width": 0.02956588752567768, "Height": 0.044100333005189896, "Left": 0.48034393787384033, "Top": 0.49108177423477173}, "Polygon": [{"X": 0.48034393787384033, "Y": 0.49108177423477173}, {"X": 0.5099093317985535, "Y": 0.49108177423477173}, {"X": 0.5099098086357117, "Y": 0.5351821184158325}, {"X": 0.4803444445133209, "Y": 0.5351820588111877}]}, "Id": "4c23739a-0891-4d10-a2aa-ddd404c04e75", "Relationships": [{"Type": "CHILD", "Ids": ["fd6a17ba-c4eb-4339-88cb-ee9f872df628"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.02294234186410904, "Height": 0.03539237752556801, "Left": 0.0015818016836419702, "Top": 0.5823086500167847}, "Polygon": [{"X": 0.0015818016836419702, "Y": 0.5823086500167847}, {"X": 0.024523507803678513, "Y": 0.5823086500167847}, {"X": 0.0245241429656744, "Y": 0.61770099401474}, {"X": 0.0015824487200006843, "Y": 0.61770099401474}]}, "Id": "54d5f2aa-abd2-41b7-a6f2-dd4fb1cb6016", "Relationships": [{"Type": "CHILD", "Ids": ["01dac3fa-b62b-4b3b-840d-66dbbf92b974"]}]}, {"BlockType": "LINE", "Confidence": 99.81365966796875, "Text": "Temp", "Geometry": {"BoundingBox": {"Width": 0.02005792036652565, "Height": 0.04039425402879715, "Left": 0.0013963112141937017, "Top": 0.8551579713821411}, "Polygon": [{"X": 0.0013963112141937017, "Y": 0.8551579713821411}, {"X": 0.021453505381941795, "Y": 0.8551580309867859}, {"X": 0.021454231813549995, "Y": 0.8955522179603577}, {"X": 0.0013970497529953718, "Y": 0.8955521583557129}]}, "Id": "93db04c3-b133-4474-b6f7-60c26e5a216a", "Relationships": [{"Type": "CHILD", "Ids": ["8928599d-9a43-4cbd-bd0a-e5e9fd771f86"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "Seals", "Geometry": {"BoundingBox": {"Width": 0.01980029232800007, "Height": 0.03450945019721985, "Left": 0.0018080912996083498, "Top": 0.9441796541213989}, "Polygon": [{"X": 0.0018080912996083498, "Y": 0.9441796541213989}, {"X": 0.021607762202620506, "Y": 0.9441797137260437}, {"X": 0.02160838432610035, "Y": 0.9786890745162964}, {"X": 0.0018087220378220081, "Y": 0.9786890149116516}]}, "Id": "8338f30b-1a68-423a-8ebe-08cb1ed04dec", "Relationships": [{"Type": "CHILD", "Ids": ["a54ba9af-1d67-4dd9-9331-ea5265af7ca3"]}]}, {"BlockType": "LINE", "Confidence": 99.84375, "Text": "EMCNXN0202", "Geometry": {"BoundingBox": {"Width": 0.05054091662168503, "Height": 0.035258207470178604, "Left": 0.12634038925170898, "Top": 0.9441555738449097}, "Polygon": [{"X": 0.12634038925170898, "Y": 0.9441555738449097}, {"X": 0.17688076198101044, "Y": 0.944155752658844}, {"X": 0.1768813133239746, "Y": 0.9794137477874756}, {"X": 0.12634097039699554, "Y": 0.9794135093688965}]}, "Id": "f83716da-bf46-44c1-a474-7cc9e0a618ff", "Relationships": [{"Type": "CHILD", "Ids": ["c459ae03-59a6-4cfa-b716-6a8b393318b8"]}]}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Georgia", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02869880385696888, "Height": 0.04269510135054588, "Left": 0.1262177973985672, "Top": 0.040932055562734604}, "Polygon": [{"X": 0.1262177973985672, "Y": 0.040932171046733856}, {"X": 0.154915913939476, "Y": 0.040932055562734604}, {"X": 0.15491659939289093, "Y": 0.08362705260515213}, {"X": 0.1262184977531433, "Y": 0.08362715691328049}]}, "Id": "a7bd27bd-440d-4ec2-b39e-b4adf4145728"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ports", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018519271165132523, "Height": 0.034293126314878464, "Left": 0.15765893459320068, "Top": 0.042367979884147644}, "Polygon": [{"X": 0.15765893459320068, "Y": 0.04236805438995361}, {"X": 0.17617766559123993, "Y": 0.042367979884147644}, {"X": 0.1761782169342041, "Y": 0.07666103541851044}, {"X": 0.15765948593616486, "Y": 0.0766611099243164}]}, "Id": "ab9b43b7-1600-44af-af14-855471e8ddc7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Authority,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03609411418437958, "Height": 0.04388921707868576, "Left": 0.17831404507160187, "Top": 0.04075733944773674}, "Polygon": [{"X": 0.17831404507160187, "Y": 0.04075748845934868}, {"X": 0.21440748870372772, "Y": 0.04075733944773674}, {"X": 0.21440815925598145, "Y": 0.08464641869068146}, {"X": 0.17831473052501678, "Y": 0.0846465528011322}]}, "Id": "7a9f4749-a4ce-4ed5-a358-014d27cb42c6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022423507645726204, "Height": 0.03467723727226257, "Left": 0.21725504100322723, "Top": 0.04207627847790718}, "Polygon": [{"X": 0.21725504100322723, "Y": 0.04207637161016464}, {"X": 0.2396780252456665, "Y": 0.04207627847790718}, {"X": 0.2396785467863083, "Y": 0.07675343751907349}, {"X": 0.21725556254386902, "Y": 0.07675351947546005}]}, "Id": "f6df5beb-ffef-49c0-bffc-bc987f2e6a0c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deliver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025932222604751587, "Height": 0.036252234131097794, "Left": 0.6050004959106445, "Top": 0.040032364428043365}, "Polygon": [{"X": 0.6050004959106445, "Y": 0.04003247246146202}, {"X": 0.6309323906898499, "Y": 0.040032364428043365}, {"X": 0.6309326887130737, "Y": 0.0762845054268837}, {"X": 0.6050008535385132, "Y": 0.07628460228443146}]}, "Id": "f46852df-352a-419a-9c22-689bdfc81341"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Import", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025384077802300453, "Height": 0.04171958565711975, "Left": 0.6332671642303467, "Top": 0.04176011309027672}, "Polygon": [{"X": 0.6332671642303467, "Y": 0.041760217398405075}, {"X": 0.6586508750915527, "Y": 0.04176011309027672}, {"X": 0.6586512327194214, "Y": 0.08347960561513901}, {"X": 0.6332675218582153, "Y": 0.08347969502210617}]}, "Id": "cc264cf3-8540-4430-a1d8-f84823524030"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03660530596971512, "Height": 0.03538733348250389, "Left": 0.660947859287262, "Top": 0.040946610271930695}, "Polygon": [{"X": 0.660947859287262, "Y": 0.04094676300883293}, {"X": 0.6975529193878174, "Y": 0.040946610271930695}, {"X": 0.6975531578063965, "Y": 0.07633380591869354}, {"X": 0.6609481573104858, "Y": 0.07633394002914429}]}, "Id": "8e38b3f6-56c7-4eee-bf08-e3f540e8b43a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "3501361", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03248152509331703, "Height": 0.03407321870326996, "Left": 0.8985869884490967, "Top": 0.04224210977554321}, "Polygon": [{"X": 0.8985869884490967, "Y": 0.04224224388599396}, {"X": 0.9310683012008667, "Y": 0.04224210977554321}, {"X": 0.931068480014801, "Y": 0.07631520181894302}, {"X": 0.8985871076583862, "Y": 0.07631532847881317}]}, "Id": "33fada65-a494-430f-86a4-ddea8b6e49f9"}, {"BlockType": "WORD", "Confidence": 99.0066909790039, "Text": "EVE:EVERGREEN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0624384842813015, "Height": 0.03365368768572807, "Left": 0.12636008858680725, "Top": 0.13232019543647766}, "Polygon": [{"X": 0.12636008858680725, "Y": 0.13232038915157318}, {"X": 0.18879804015159607, "Y": 0.13232019543647766}, {"X": 0.18879857659339905, "Y": 0.1659736931324005}, {"X": 0.12636063992977142, "Y": 0.16597387194633484}]}, "Id": "7318a237-e246-499b-aa49-8f2c439e74cf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SHIPPING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03676134720444679, "Height": 0.03382093459367752, "Left": 0.19146475195884705, "Top": 0.13273309171199799}, "Polygon": [{"X": 0.19146475195884705, "Y": 0.13273321092128754}, {"X": 0.22822560369968414, "Y": 0.13273309171199799}, {"X": 0.22822611033916473, "Y": 0.16655391454696655}, {"X": 0.19146528840065002, "Y": 0.1665540188550949}]}, "Id": "0f0fb2b2-603b-4be5-9122-5df8725ab0d5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "AGENCY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03088117204606533, "Height": 0.03310396149754524, "Left": 0.23066699504852295, "Top": 0.1329985409975052}, "Polygon": [{"X": 0.23066699504852295, "Y": 0.13299864530563354}, {"X": 0.26154768466949463, "Y": 0.1329985409975052}, {"X": 0.26154816150665283, "Y": 0.16610240936279297}, {"X": 0.23066750168800354, "Y": 0.16610249876976013}]}, "Id": "c27a83eb-8c63-4ea2-adb2-aec2da38b171"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "DRJK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019104275852441788, "Height": 0.03405361995100975, "Left": 0.6049544811248779, "Top": 0.2228802889585495}, "Polygon": [{"X": 0.6049544811248779, "Y": 0.22288033366203308}, {"X": 0.6240584850311279, "Y": 0.2228802889585495}, {"X": 0.6240587830543518, "Y": 0.25693386793136597}, {"X": 0.6049548387527466, "Y": 0.25693389773368835}]}, "Id": "77e0089f-6d77-4061-bef7-66a0635d639c"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "01-Mar-2023", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048547349870204926, "Height": 0.03449467569589615, "Left": 0.8981427550315857, "Top": 0.22319430112838745}, "Polygon": [{"X": 0.8981427550315857, "Y": 0.223194420337677}, {"X": 0.9466899633407593, "Y": 0.22319430112838745}, {"X": 0.9466900825500488, "Y": 0.25768887996673584}, {"X": 0.89814293384552, "Y": 0.257688969373703}]}, "Id": "db7416f8-9948-4334-9e0d-c60cb165f8e4"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "06:39:43", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431560844182968, "Height": 0.03390169516205788, "Left": 0.94916832447052, "Top": 0.22326482832431793}, "Polygon": [{"X": 0.94916832447052, "Y": 0.2232649028301239}, {"X": 0.9834837913513184, "Y": 0.22326482832431793}, {"X": 0.9834839105606079, "Y": 0.25716644525527954}, {"X": 0.9491685032844543, "Y": 0.2571665048599243}]}, "Id": "38e0a7a9-05aa-4e1c-aac1-f6a492e7c59a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02075643464922905, "Height": 0.03548947349190712, "Left": 0.47984427213668823, "Top": 0.3116055727005005}, "Polygon": [{"X": 0.47984427213668823, "Y": 0.3116056025028229}, {"X": 0.5006003379821777, "Y": 0.3116055727005005}, {"X": 0.5006006956100464, "Y": 0.3470950126647949}, {"X": 0.4798446595668793, "Y": 0.3470950424671173}]}, "Id": "8999d13f-62f9-4839-982e-e66005c46132"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009023881517350674, "Height": 0.03318110480904579, "Left": 0.5026094317436218, "Top": 0.31327784061431885}, "Polygon": [{"X": 0.5026094317436218, "Y": 0.31327784061431885}, {"X": 0.5116329193115234, "Y": 0.31327784061431885}, {"X": 0.5116332769393921, "Y": 0.34645891189575195}, {"X": 0.5026097893714905, "Y": 0.34645894169807434}]}, "Id": "264ed1ad-fa66-4fe3-a8a2-58ee51a405a8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03607495501637459, "Height": 0.035042956471443176, "Left": 0.0016393099213019013, "Top": 0.40270039439201355}, "Polygon": [{"X": 0.0016393099213019013, "Y": 0.40270039439201355}, {"X": 0.03771364316344261, "Y": 0.40270039439201355}, {"X": 0.037714265286922455, "Y": 0.43774333596229553}, {"X": 0.0016399504384025931, "Y": 0.43774333596229553}]}, "Id": "ac6fe7cb-9442-40a3-9c3d-c4deb6644e9c"}, {"BlockType": "WORD", "Confidence": 99.81205749511719, "Text": "EGHU9787630", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05354415252804756, "Height": 0.034645747393369675, "Left": 0.12639091908931732, "Top": 0.40304359793663025}, "Polygon": [{"X": 0.12639091908931732, "Y": 0.40304362773895264}, {"X": 0.1799345314502716, "Y": 0.40304359793663025}, {"X": 0.17993506789207458, "Y": 0.43768930435180664}, {"X": 0.1263914853334427, "Y": 0.43768933415412903}]}, "Id": "e05146a6-969e-48dc-bddf-6f4ea8d630cb"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "Size/Type", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03614405542612076, "Height": 0.04284530505537987, "Left": 0.48021477460861206, "Top": 0.4016912877559662}, "Polygon": [{"X": 0.48021477460861206, "Y": 0.4016913175582886}, {"X": 0.5163583755493164, "Y": 0.4016912877559662}, {"X": 0.5163588523864746, "Y": 0.44453656673431396}, {"X": 0.48021528124809265, "Y": 0.44453659653663635}]}, "Id": "31a3bc7e-e2ff-4e6b-a90e-62478f2c07ad"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "40_DRY_9.5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04607405140995979, "Height": 0.04290961101651192, "Left": 0.6044583916664124, "Top": 0.4023168683052063}, "Polygon": [{"X": 0.6044583916664124, "Y": 0.4023168981075287}, {"X": 0.650532066822052, "Y": 0.4023168683052063}, {"X": 0.6505324244499207, "Y": 0.4452264606952667}, {"X": 0.6044588088989258, "Y": 0.4452264904975891}]}, "Id": "dbeced81-7315-4025-bd25-89b8d1ea8925"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02789592184126377, "Height": 0.03544527292251587, "Left": 0.0017841283697634935, "Top": 0.4918473958969116}, "Polygon": [{"X": 0.0017841283697634935, "Y": 0.4918473958969116}, {"X": 0.029679417610168457, "Y": 0.4918473958969116}, {"X": 0.029680050909519196, "Y": 0.5272926688194275}, {"X": 0.0017847762210294604, "Y": 0.5272926688194275}]}, "Id": "265f55ba-4f10-4a88-a0c6-f2d1f10932b9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "592919", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027692610397934914, "Height": 0.034773074090480804, "Left": 0.1263333410024643, "Top": 0.49325624108314514}, "Polygon": [{"X": 0.1263333410024643, "Y": 0.49325624108314514}, {"X": 0.1540253907442093, "Y": 0.49325624108314514}, {"X": 0.15402595698833466, "Y": 0.5280293226242065}, {"X": 0.12633390724658966, "Y": 0.5280293226242065}]}, "Id": "3b400ea8-be39-4a22-9c41-75170d5efdd1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02956588752567768, "Height": 0.044100333005189896, "Left": 0.48034393787384033, "Top": 0.49108177423477173}, "Polygon": [{"X": 0.48034393787384033, "Y": 0.49108177423477173}, {"X": 0.5099093317985535, "Y": 0.49108177423477173}, {"X": 0.5099098086357117, "Y": 0.5351821184158325}, {"X": 0.4803444445133209, "Y": 0.5351820588111877}]}, "Id": "fd6a17ba-c4eb-4339-88cb-ee9f872df628"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02294234186410904, "Height": 0.03539237752556801, "Left": 0.0015818016836419702, "Top": 0.5823086500167847}, "Polygon": [{"X": 0.0015818016836419702, "Y": 0.5823086500167847}, {"X": 0.024523507803678513, "Y": 0.5823086500167847}, {"X": 0.0245241429656744, "Y": 0.61770099401474}, {"X": 0.0015824487200006843, "Y": 0.61770099401474}]}, "Id": "01dac3fa-b62b-4b3b-840d-66dbbf92b974"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "Temp", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02005792036652565, "Height": 0.04039425402879715, "Left": 0.0013963112141937017, "Top": 0.8551579713821411}, "Polygon": [{"X": 0.0013963112141937017, "Y": 0.8551579713821411}, {"X": 0.021453505381941795, "Y": 0.8551580309867859}, {"X": 0.021454231813549995, "Y": 0.8955522179603577}, {"X": 0.0013970497529953718, "Y": 0.8955521583557129}]}, "Id": "8928599d-9a43-4cbd-bd0a-e5e9fd771f86"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Seals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01980029232800007, "Height": 0.03450945019721985, "Left": 0.0018080912996083498, "Top": 0.9441796541213989}, "Polygon": [{"X": 0.0018080912996083498, "Y": 0.9441796541213989}, {"X": 0.021607762202620506, "Y": 0.9441797137260437}, {"X": 0.02160838432610035, "Y": 0.9786890745162964}, {"X": 0.0018087220378220081, "Y": 0.9786890149116516}]}, "Id": "a54ba9af-1d67-4dd9-9331-ea5265af7ca3"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "EMCNXN0202", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05054091662168503, "Height": 0.035258207470178604, "Left": 0.12634038925170898, "Top": 0.9441555738449097}, "Polygon": [{"X": 0.12634038925170898, "Y": 0.9441555738449097}, {"X": 0.17688076198101044, "Y": 0.944155752658844}, {"X": 0.1768813133239746, "Y": 0.9794137477874756}, {"X": 0.12634097039699554, "Y": 0.9794135093688965}]}, "Id": "c459ae03-59a6-4cfa-b716-6a8b393318b8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "bcfde325-4550-418b-b693-63313b7e7e7c", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "bcfde325-4550-418b-b693-63313b7e7e7c", "content-type": "application/x-amz-json-1.1", "content-length": "24283", "date": "Wed, 24 Sep 2025 13:32:24 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "1cc7c723-bdcb-4206-8ce0-2d0aed42007c", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:32:27 GMT", "content-type": "application/json", "content-length": "1784", "connection": "keep-alive", "x-amzn-requestid": "1cc7c723-bdcb-4206-8ce0-2d0aed42007c"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The text appears to be some port authority container delivery info. Keywords: \"Deliver Import Container\", \"Container\", \"Booking\", \"Vessel\", \"Seals\". This looks like a customs/doc, maybe a customs document, or a shipping manifest. Let's see categories: customs_doc: \"General customs paperwork (declarations, certificates, permits).\" Keywords: Customs, Declaration, Import/Export Declaration, Customs Broker. This page has \"Deliver Import Container\" indicates import container. Could be a customs document. There's no HS code, no declared value. Could be a port authority manifest. Maybe \"customs_doc\" is appropriate.\n\nOther categories: \"ingate\" or \"outgate\"? It says \"Truck ID\" \"Container\" maybe it's an ingate or outgate. But ingate document: \"Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\". We have \"Truck ID\" and maybe the time \"01-Mar-2023 06:39:43\". It might be a gate-in record. But the title \"Deliver Import Container\" suggests a container delivery. Could be a delivery receipt at port. Not exactly ingate: ingate is for vehicle/container entering a facility. This might be a gate-in for container? Not sure.\n\nWe need to choose among \"customs_doc\", \"ingate\", \"outgate\", \"other\". The best is customs_doc. There's no other explicit header. There's no \"Bill of Lading\" etc.\n\nThus classify as customs_doc. Page 1, doc_type: customs_doc.\n\nReturn JSON."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"customs_doc\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2676, "outputTokens": 350, "totalTokens": 3026}, "metrics": {"latencyMs": 1540}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/2c871dfd_UGM225037492OGWQHUHR.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nGeorgia Ports Authority, <PERSON>van\nDeliver Import Container\n3501361\nEVE:EVERGREEN SHIPPING AGENCY\nDRJK\n01-Mar-2023 06:39:43\nTruck ID\nContainer\nEGHU9787630\nSize/Type\n40_DRY_9.5\nChassis\n592919\nBooking\nVessel\nTemp\nSeals\nEMCNXN0202\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}