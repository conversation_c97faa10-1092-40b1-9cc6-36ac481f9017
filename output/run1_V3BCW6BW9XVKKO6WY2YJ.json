{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 9.484870133746881e-06, "Y": 1.0702007280372072e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "dca74731-2b7e-402b-9755-72d897d725ff", "Relationships": [{"Type": "CHILD", "Ids": ["7900ce4c-c790-4fc7-85e7-24a8858ab25c", "41811098-fd78-4812-8023-61eae57d3dce", "d52b4e59-a3a7-4feb-94a4-1dd0e456e29e", "66dd75e7-a31f-4148-91fd-b173c9f72294", "41782596-3efe-4aba-8ec9-916ddbe30a79", "04e509ec-a422-4ac5-ab1d-160951968ce0", "43909ddb-40be-4d9d-8bbd-8babcfbdc97d", "b937d3cd-cae0-4ee5-b592-4779d2c5b15d", "d22c602e-8961-47a9-82a2-6b6691a0b194", "583bc59f-8d15-4f0e-a2df-328c55e3734a", "8428fbb1-b2fb-4841-9299-dcc4a6b70095", "252391d3-2edd-4d11-8b77-35fd25f821ac", "521b41eb-1d3e-409e-a4b5-8af860f777ff", "3dae09e1-4257-4b08-b21f-9c0a174845d2", "b2e88768-761e-4fc6-9c7b-5c33ece00bd5", "935e0aba-851a-40ba-b373-d4d2ab3df3c8", "4bdc306a-58ef-43ef-b4be-a62c2b5d8784", "2fafd372-3641-4a84-98c1-0db3016eb28f", "8bcc8999-330e-492f-ab8a-e4760eaf9a11", "54791ab7-93c5-4233-9754-96255222abc0", "934c4173-fe45-48ec-b9af-4aab61c9afa4", "b93a9a7d-3835-4da1-b539-40e52494fa26", "37f74972-f409-4b16-8f63-f69276256338", "7609385b-d172-4ff3-a085-cef30f891368", "35aab834-eb22-4cd3-8802-6797a344dccb", "305ab321-ec84-4b53-ad33-bdda7797deb3", "12ccff2f-d3ea-422f-8f9b-876bbf01951c", "c671b7c1-aa1b-47e7-ba81-344866104170"]}]}, {"BlockType": "LINE", "Confidence": 99.9704360961914, "Text": "Receipt number 128521", "Geometry": {"BoundingBox": {"Width": 0.3113001883029938, "Height": 0.021806783974170685, "Left": 0.14796043932437897, "Top": 0.03641052544116974}, "Polygon": [{"X": 0.14796076714992523, "Y": 0.03641366958618164}, {"X": 0.45926064252853394, "Y": 0.03641052544116974}, {"X": 0.4592604637145996, "Y": 0.058214373886585236}, {"X": 0.14796043932437897, "Y": 0.058217309415340424}]}, "Id": "7900ce4c-c790-4fc7-85e7-24a8858ab25c", "Relationships": [{"Type": "CHILD", "Ids": ["772b3f2e-6f80-4c70-96a4-b7b602987fe0", "d87a1818-ab2d-494b-bdc7-8eb3e647d521", "916dac62-02df-4a9f-bc06-5a358af6a39f"]}]}, {"BlockType": "LINE", "Confidence": 99.91560363769531, "Text": "CAPE STONE LUMPERS, LLC", "Geometry": {"BoundingBox": {"Width": 0.36281639337539673, "Height": 0.01899712160229683, "Left": 0.39562955498695374, "Top": 0.13754688203334808}, "Polygon": [{"X": 0.39562973380088806, "Y": 0.13754943013191223}, {"X": 0.7584459185600281, "Y": 0.13754688203334808}, {"X": 0.7584459185600281, "Y": 0.15654167532920837}, {"X": 0.39562955498695374, "Y": 0.15654399991035461}]}, "Id": "41811098-fd78-4812-8023-61eae57d3dce", "Relationships": [{"Type": "CHILD", "Ids": ["2ce0ae4d-b4f0-404c-a219-bd8332a3f4b6", "2d62ca21-a1ff-43da-8917-47581372b95b", "d9f3a0fe-7958-4425-b322-5428ad33ccf0", "a6b55e17-afc9-4a45-8aa4-48b9f997e189"]}]}, {"BlockType": "LINE", "Confidence": 99.69513702392578, "Text": "1222 LODI PL", "Geometry": {"BoundingBox": {"Width": 0.17559541761875153, "Height": 0.01672637090086937, "Left": 0.3973669111728668, "Top": 0.16237960755825043}, "Polygon": [{"X": 0.39736706018447876, "Y": 0.16238071024417877}, {"X": 0.5729622840881348, "Y": 0.16237960755825043}, {"X": 0.57296222448349, "Y": 0.1791049838066101}, {"X": 0.3973669111728668, "Y": 0.1791059821844101}]}, "Id": "d52b4e59-a3a7-4feb-94a4-1dd0e456e29e", "Relationships": [{"Type": "CHILD", "Ids": ["978b4b10-99f2-4138-a24f-f3a7a0a31c61", "c4fe879e-3d99-45e3-93ff-433d71c72806", "f5754568-b533-4ba0-b7de-26423bdb1d5d"]}]}, {"BlockType": "LINE", "Confidence": 99.75127410888672, "Text": "LOS ANGELES, CA 90038", "Geometry": {"BoundingBox": {"Width": 0.3200492262840271, "Height": 0.019533289596438408, "Left": 0.3962271511554718, "Top": 0.18715746700763702}, "Polygon": [{"X": 0.39622732996940613, "Y": 0.1871592253446579}, {"X": 0.7162763476371765, "Y": 0.18715746700763702}, {"X": 0.7162763476371765, "Y": 0.20668919384479523}, {"X": 0.3962271511554718, "Y": 0.20669075846672058}]}, "Id": "66dd75e7-a31f-4148-91fd-b173c9f72294", "Relationships": [{"Type": "CHILD", "Ids": ["4bdd5659-04e6-4d2b-867d-8fdb798119bc", "e589c882-c070-43df-ad5a-7b410f131937", "f505ee09-fec7-48fa-9191-099b5468a211", "d723aaef-d4f5-4160-9b1d-cce00cd817ea"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "GENERAL RECEIPT", "Geometry": {"BoundingBox": {"Width": 0.2402493953704834, "Height": 0.016896961256861687, "Left": 0.3595607876777649, "Top": 0.26209259033203125}, "Polygon": [{"X": 0.3595609664916992, "Y": 0.26209336519241333}, {"X": 0.5998101830482483, "Y": 0.26209259033203125}, {"X": 0.5998101234436035, "Y": 0.27898889780044556}, {"X": 0.3595607876777649, "Y": 0.2789895534515381}]}, "Id": "41782596-3efe-4aba-8ec9-916ddbe30a79", "Relationships": [{"Type": "CHILD", "Ids": ["42c1503d-6992-4bc0-aa98-d39d12da1273", "c9c2bd0c-3eda-400e-b753-ae5043167204"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DATE", "Geometry": {"BoundingBox": {"Width": 0.06986705213785172, "Height": 0.017276884987950325, "Left": 0.14604590833187103, "Top": 0.34937140345573425}, "Polygon": [{"X": 0.14604617655277252, "Y": 0.34937143325805664}, {"X": 0.21591296792030334, "Y": 0.34937140345573425}, {"X": 0.21591274440288544, "Y": 0.36664828658103943}, {"X": 0.14604590833187103, "Y": 0.36664828658103943}]}, "Id": "04e509ec-a422-4ac5-ab1d-160951968ce0", "Relationships": [{"Type": "CHILD", "Ids": ["8eb10135-7014-44f2-8961-26213e2fd180"]}]}, {"BlockType": "LINE", "Confidence": 99.736328125, "Text": "09/18/25", "Geometry": {"BoundingBox": {"Width": 0.11892370134592056, "Height": 0.017194919288158417, "Left": 0.42969322204589844, "Top": 0.349773645401001}, "Polygon": [{"X": 0.4296933710575104, "Y": 0.34977370500564575}, {"X": 0.548616886138916, "Y": 0.349773645401001}, {"X": 0.5486168265342712, "Y": 0.3669685423374176}, {"X": 0.42969322204589844, "Y": 0.3669685423374176}]}, "Id": "43909ddb-40be-4d9d-8bbd-8babcfbdc97d", "Relationships": [{"Type": "CHILD", "Ids": ["b0ce915b-f45d-4440-b1ba-579a3cf6ff20"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "TIME", "Geometry": {"BoundingBox": {"Width": 0.06506256014108658, "Height": 0.016878914088010788, "Left": 0.14476104080677032, "Top": 0.3746216297149658}, "Polygon": [{"X": 0.14476129412651062, "Y": 0.3746216297149658}, {"X": 0.2098236083984375, "Y": 0.3746216595172882}, {"X": 0.2098233699798584, "Y": 0.3915005624294281}, {"X": 0.14476104080677032, "Y": 0.3915005028247833}]}, "Id": "b937d3cd-cae0-4ee5-b592-4779d2c5b15d", "Relationships": [{"Type": "CHILD", "Ids": ["7d6e3590-642b-4469-9960-2c0775f6a239"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "1:15", "Geometry": {"BoundingBox": {"Width": 0.05506588891148567, "Height": 0.01733298785984516, "Left": 0.43181130290031433, "Top": 0.374639630317688}, "Polygon": [{"X": 0.43181145191192627, "Y": 0.374639630317688}, {"X": 0.4868772029876709, "Y": 0.374639630317688}, {"X": 0.48687705397605896, "Y": 0.3919726014137268}, {"X": 0.43181130290031433, "Y": 0.3919725716114044}]}, "Id": "d22c602e-8961-47a9-82a2-6b6691a0b194", "Relationships": [{"Type": "CHILD", "Ids": ["f5ec76f5-a9a6-4830-a440-ead508467395"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CASHIER NAME", "Geometry": {"BoundingBox": {"Width": 0.2020094096660614, "Height": 0.017080683261156082, "Left": 0.14524805545806885, "Top": 0.4005500376224518}, "Polygon": [{"X": 0.14524832367897034, "Y": 0.4005500376224518}, {"X": 0.34725746512413025, "Y": 0.4005502462387085}, {"X": 0.3472572863101959, "Y": 0.41763070225715637}, {"X": 0.14524805545806885, "Y": 0.4176304042339325}]}, "Id": "583bc59f-8d15-4f0e-a2df-328c55e3734a", "Relationships": [{"Type": "CHILD", "Ids": ["fe41bf39-291c-4528-963a-a4f61d3eec21", "eb675e33-2b58-4181-a5ed-0d9429cbef72"]}]}, {"BlockType": "LINE", "Confidence": 98.80171966552734, "Text": "BILL S.", "Geometry": {"BoundingBox": {"Width": 0.0854387879371643, "Height": 0.017388170585036278, "Left": 0.4308091402053833, "Top": 0.4008250832557678}, "Polygon": [{"X": 0.43080928921699524, "Y": 0.4008250832557678}, {"X": 0.5162479281425476, "Y": 0.400825172662735}, {"X": 0.5162478089332581, "Y": 0.41821324825286865}, {"X": 0.4308091402053833, "Y": 0.4182131290435791}]}, "Id": "8428fbb1-b2fb-4841-9299-dcc4a6b70095", "Relationships": [{"Type": "CHILD", "Ids": ["1a4a2df4-58a4-42bc-8194-eb832ddcabc3", "f39a0975-f0c9-4939-b30e-051bd7cfab67"]}]}, {"BlockType": "LINE", "Confidence": 99.99755859375, "Text": "CUSTOMER NAME RCH TRUCKING", "Geometry": {"BoundingBox": {"Width": 0.4885255694389343, "Height": 0.017471633851528168, "Left": 0.14509080350399017, "Top": 0.4255971610546112}, "Polygon": [{"X": 0.14509107172489166, "Y": 0.4255971610546112}, {"X": 0.6336163878440857, "Y": 0.42559805512428284}, {"X": 0.6336163282394409, "Y": 0.44306880235671997}, {"X": 0.14509080350399017, "Y": 0.44306766986846924}]}, "Id": "252391d3-2edd-4d11-8b77-35fd25f821ac", "Relationships": [{"Type": "CHILD", "Ids": ["43826853-07ee-49eb-9d0a-18fffaa41ad7", "0c8bd698-0ff0-4e7e-a21e-3141d2d7b923", "e62533cc-4672-4188-8878-cd7990c24065", "20e088cf-ad34-4bd7-beac-a4dc89c3724e"]}]}, {"BlockType": "LINE", "Confidence": 98.52519226074219, "Text": "Item(s)", "Geometry": {"BoundingBox": {"Width": 0.0907580628991127, "Height": 0.0198813509196043, "Left": 0.18134883046150208, "Top": 0.47574320435523987}, "Polygon": [{"X": 0.18134911358356476, "Y": 0.47574320435523987}, {"X": 0.2721068859100342, "Y": 0.47574350237846375}, {"X": 0.2721066474914551, "Y": 0.4956245422363281}, {"X": 0.18134883046150208, "Y": 0.4956241846084595}]}, "Id": "521b41eb-1d3e-409e-a4b5-8af860f777ff", "Relationships": [{"Type": "CHILD", "Ids": ["8ac84873-cc81-4b48-bf35-c839b228446b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.11660171300172806, "Height": 0.0214186180382967, "Left": 0.3939562737941742, "Top": 0.47580403089523315}, "Polygon": [{"X": 0.3939564824104309, "Y": 0.47580403089523315}, {"X": 0.510558009147644, "Y": 0.4758044183254242}, {"X": 0.5105578303337097, "Y": 0.4972226321697235}, {"X": 0.3939562737941742, "Y": 0.4972221851348877}]}, "Id": "3dae09e1-4257-4b08-b21f-9c0a174845d2", "Relationships": [{"Type": "CHILD", "Ids": ["18e822b3-3705-46ef-80cc-a3bf33883765"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.06536411494016647, "Height": 0.017286501824855804, "Left": 0.7336059212684631, "Top": 0.47594982385635376}, "Polygon": [{"X": 0.7336059212684631, "Y": 0.47594982385635376}, {"X": 0.7989700436592102, "Y": 0.4759500324726105}, {"X": 0.7989699840545654, "Y": 0.49323633313179016}, {"X": 0.7336059212684631, "Y": 0.49323606491088867}]}, "Id": "b2e88768-761e-4fc6-9c7b-5c33ece00bd5", "Relationships": [{"Type": "CHILD", "Ids": ["f7c3926f-69a2-4ed6-b3c0-8eb9f7f6537b"]}]}, {"BlockType": "LINE", "Confidence": 94.64903259277344, "Text": "5plts", "Geometry": {"BoundingBox": {"Width": 0.06395619362592697, "Height": 0.021163327619433403, "Left": 0.18070465326309204, "Top": 0.500420093536377}, "Polygon": [{"X": 0.1807049661874771, "Y": 0.500420093536377}, {"X": 0.2446608543395996, "Y": 0.500420331954956}, {"X": 0.24466058611869812, "Y": 0.5215834379196167}, {"X": 0.18070465326309204, "Y": 0.521583080291748}]}, "Id": "935e0aba-851a-40ba-b373-d4d2ab3df3c8", "Relationships": [{"Type": "CHILD", "Ids": ["a78e6462-bb1e-4670-8e96-9026c8e623a6"]}]}, {"BlockType": "LINE", "Confidence": 98.81059265136719, "Text": "into12", "Geometry": {"BoundingBox": {"Width": 0.08398232609033585, "Height": 0.01716327667236328, "Left": 0.18069301545619965, "Top": 0.5255658626556396}, "Polygon": [{"X": 0.18069326877593994, "Y": 0.5255658626556396}, {"X": 0.2646753489971161, "Y": 0.5255662798881531}, {"X": 0.2646751403808594, "Y": 0.5427291393280029}, {"X": 0.18069301545619965, "Y": 0.5427286624908447}]}, "Id": "4bdc306a-58ef-43ef-b4be-a62c2b5d8784", "Relationships": [{"Type": "CHILD", "Ids": ["967d1955-b5d5-4c77-ae90-e7db7b1b62fb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "446831572", "Geometry": {"BoundingBox": {"Width": 0.14760859310626984, "Height": 0.017352089285850525, "Left": 0.17945846915245056, "Top": 0.5506312251091003}, "Polygon": [{"X": 0.17945870757102966, "Y": 0.5506312251091003}, {"X": 0.3270670473575592, "Y": 0.5506319999694824}, {"X": 0.3270668685436249, "Y": 0.5679832696914673}, {"X": 0.17945846915245056, "Y": 0.5679823756217957}]}, "Id": "2fafd372-3641-4a84-98c1-0db3016eb28f", "Relationships": [{"Type": "CHILD", "Ids": ["85ac5ec3-5191-4060-8f30-347b4382d96d"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "140.00", "Geometry": {"BoundingBox": {"Width": 0.08760174363851547, "Height": 0.01673538237810135, "Left": 0.7344616055488586, "Top": 0.576383650302887}, "Polygon": [{"X": 0.7344616055488586, "Y": 0.576383650302887}, {"X": 0.8220633268356323, "Y": 0.5763842463493347}, {"X": 0.8220633268356323, "Y": 0.5931190252304077}, {"X": 0.7344616055488586, "Y": 0.59311842918396}]}, "Id": "8bcc8999-330e-492f-ab8a-e4760eaf9a11", "Relationships": [{"Type": "CHILD", "Ids": ["acf7dfaf-dbbd-4caa-bcb8-aa2a9b2467a4"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "SUBTOTAL", "Geometry": {"BoundingBox": {"Width": 0.1422644406557083, "Height": 0.017402630299329758, "Left": 0.2505494952201843, "Top": 0.6266503930091858}, "Polygon": [{"X": 0.25054970383644104, "Y": 0.6266503930091858}, {"X": 0.39281392097473145, "Y": 0.6266515254974365}, {"X": 0.3928137719631195, "Y": 0.6440529823303223}, {"X": 0.2505494952201843, "Y": 0.6440517902374268}]}, "Id": "54791ab7-93c5-4233-9754-96255222abc0", "Relationships": [{"Type": "CHILD", "Ids": ["6560a1ca-15fe-40b0-9dfc-174a0304b2f7"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "140.00", "Geometry": {"BoundingBox": {"Width": 0.08754293620586395, "Height": 0.016878917813301086, "Left": 0.7343147397041321, "Top": 0.6273229718208313}, "Polygon": [{"X": 0.7343147993087769, "Y": 0.6273229718208313}, {"X": 0.8218576908111572, "Y": 0.6273236274719238}, {"X": 0.8218576908111572, "Y": 0.6442018747329712}, {"X": 0.7343147397041321, "Y": 0.6442010998725891}]}, "Id": "934c4173-fe45-48ec-b9af-4aab61c9afa4", "Relationships": [{"Type": "CHILD", "Ids": ["078d2fbe-2b3b-40fc-a51b-00a6e38fff2c"]}]}, {"BlockType": "LINE", "Confidence": 99.71400451660156, "Text": "% DISCOUNT", "Geometry": {"BoundingBox": {"Width": 0.17528453469276428, "Height": 0.017527088522911072, "Left": 0.2503453493118286, "Top": 0.6516308188438416}, "Polygon": [{"X": 0.2503455579280853, "Y": 0.6516308188438416}, {"X": 0.4256298840045929, "Y": 0.6516323089599609}, {"X": 0.42562973499298096, "Y": 0.669157862663269}, {"X": 0.2503453493118286, "Y": 0.6691562533378601}]}, "Id": "b93a9a7d-3835-4da1-b539-40e52494fa26", "Relationships": [{"Type": "CHILD", "Ids": ["fc658fca-da94-41e2-9896-154a578cc862", "756e467b-1c26-49ee-aa16-0d96333768bb"]}]}, {"BlockType": "LINE", "Confidence": 99.56153869628906, "Text": "-0.00", "Geometry": {"BoundingBox": {"Width": 0.0668911263346672, "Height": 0.017207179218530655, "Left": 0.7325383424758911, "Top": 0.6521713137626648}, "Polygon": [{"X": 0.7325383424758911, "Y": 0.6521713137626648}, {"X": 0.7994294762611389, "Y": 0.6521718502044678}, {"X": 0.7994294762611389, "Y": 0.6693784594535828}, {"X": 0.7325383424758911, "Y": 0.669377863407135}]}, "Id": "37f74972-f409-4b16-8f63-f69276256338", "Relationships": [{"Type": "CHILD", "Ids": ["ceca8fdb-d407-4a50-a9cc-7f0e360806d6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.0878949984908104, "Height": 0.016998037695884705, "Left": 0.24931780993938446, "Top": 0.752142608165741}, "Polygon": [{"X": 0.24931801855564117, "Y": 0.752142608165741}, {"X": 0.33721280097961426, "Y": 0.7521436214447021}, {"X": 0.33721262216567993, "Y": 0.7691406011581421}, {"X": 0.24931780993938446, "Y": 0.7691395282745361}]}, "Id": "7609385b-d172-4ff3-a085-cef30f891368", "Relationships": [{"Type": "CHILD", "Ids": ["1fd383cf-864e-496b-8928-f7d4468bf696"]}]}, {"BlockType": "LINE", "Confidence": 99.64983367919922, "Text": "140.00 USD ($)", "Geometry": {"BoundingBox": {"Width": 0.1934470385313034, "Height": 0.0206294022500515, "Left": 0.5377163290977478, "Top": 0.752362072467804}, "Polygon": [{"X": 0.5377164483070374, "Y": 0.752362072467804}, {"X": 0.7311633825302124, "Y": 0.7523643374443054}, {"X": 0.7311633229255676, "Y": 0.7729914784431458}, {"X": 0.5377163290977478, "Y": 0.77298903465271}]}, "Id": "35aab834-eb22-4cd3-8802-6797a344dccb", "Relationships": [{"Type": "CHILD", "Ids": ["1e968058-df86-4bd1-b5d2-8aa8c29914a5", "522fdf57-32df-4787-8d15-e3f991738e35", "babb82f1-8d4d-45c6-9287-d334b2e83697"]}]}, {"BlockType": "LINE", "Confidence": 98.45807647705078, "Text": "Payment Method : Cash", "Geometry": {"BoundingBox": {"Width": 0.315268337726593, "Height": 0.02162126451730728, "Left": 0.2510679066181183, "Top": 0.7769203782081604}, "Polygon": [{"X": 0.2510681748390198, "Y": 0.7769203782081604}, {"X": 0.5663362145423889, "Y": 0.7769243717193604}, {"X": 0.5663360953330994, "Y": 0.7985416650772095}, {"X": 0.2510679066181183, "Y": 0.7985374927520752}]}, "Id": "305ab321-ec84-4b53-ad33-bdda7797deb3", "Relationships": [{"Type": "CHILD", "Ids": ["71e27014-6436-4c00-b2b3-31a4cb0ea888", "3fb32760-2a16-4195-8271-c0092d04ff59", "d3c7d71f-60a3-4908-9ebb-661f6159b21d", "9713fdaf-3af2-434e-b052-6378c2189d23"]}]}, {"BlockType": "LINE", "Confidence": 98.92694091796875, "Text": "Return Policy : No Returns", "Geometry": {"BoundingBox": {"Width": 0.3471832275390625, "Height": 0.021543266251683235, "Left": 0.25103068351745605, "Top": 0.8032747507095337}, "Polygon": [{"X": 0.25103095173835754, "Y": 0.8032747507095337}, {"X": 0.5982139110565186, "Y": 0.8032793998718262}, {"X": 0.598213791847229, "Y": 0.8248180150985718}, {"X": 0.25103068351745605, "Y": 0.824813187122345}]}, "Id": "12ccff2f-d3ea-422f-8f9b-876bbf01951c", "Relationships": [{"Type": "CHILD", "Ids": ["9f945e6e-3701-4c4e-af9f-2a32c155c411", "4874cae4-d824-434d-8c15-e6f7ffc24a5e", "4880de7b-3546-49ce-93e6-a39913a9c53e", "48cc114a-1f19-4932-a42a-a31e4760fe65", "47b4ce87-e5c3-4195-8f3c-21a9a97820d2"]}]}, {"BlockType": "LINE", "Confidence": 99.74778747558594, "Text": "UNFI-MORENO VALLEY", "Geometry": {"BoundingBox": {"Width": 0.30409932136535645, "Height": 0.017148727551102638, "Left": 0.21477819979190826, "Top": 0.85373455286026}, "Polygon": [{"X": 0.21477843821048737, "Y": 0.85373455286026}, {"X": 0.5188775062561035, "Y": 0.8537390828132629}, {"X": 0.518877387046814, "Y": 0.8708832859992981}, {"X": 0.21477819979190826, "Y": 0.8708785772323608}]}, "Id": "c671b7c1-aa1b-47e7-ba81-344866104170", "Relationships": [{"Type": "CHILD", "Ids": ["739ad959-cf5b-4ce2-a166-934186450bc9", "00e0c483-bcec-4193-a093-abbc08e7d840"]}]}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09747405350208282, "Height": 0.02145671658217907, "Left": 0.14796043932437897, "Top": 0.0367605946958065}, "Polygon": [{"X": 0.14796075224876404, "Y": 0.0367615781724453}, {"X": 0.2454344928264618, "Y": 0.0367605946958065}, {"X": 0.2454342097043991, "Y": 0.058216389268636703}, {"X": 0.14796043932437897, "Y": 0.058217309415340424}]}, "Id": "772b3f2e-6f80-4c70-96a4-b7b602987fe0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10593744367361069, "Height": 0.017400693148374557, "Left": 0.2524712383747101, "Top": 0.03641154617071152}, "Polygon": [{"X": 0.2524714767932892, "Y": 0.036412615329027176}, {"X": 0.35840868949890137, "Y": 0.03641154617071152}, {"X": 0.35840851068496704, "Y": 0.05381122604012489}, {"X": 0.2524712383747101, "Y": 0.053812239319086075}]}, "Id": "d87a1818-ab2d-494b-bdc7-8eb3e647d521"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "128521", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09220799803733826, "Height": 0.016884636133909225, "Left": 0.3670526444911957, "Top": 0.037058908492326736}, "Polygon": [{"X": 0.36705282330513, "Y": 0.03705983981490135}, {"X": 0.45926064252853394, "Y": 0.037058908492326736}, {"X": 0.459260493516922, "Y": 0.053942665457725525}, {"X": 0.3670526444911957, "Y": 0.05394354462623596}]}, "Id": "916dac62-02df-4a9f-bc06-5a358af6a39f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "CAPE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06805384904146194, "Height": 0.01625952497124672, "Left": 0.3956295847892761, "Top": 0.1376396268606186}, "Polygon": [{"X": 0.39562973380088806, "Y": 0.1376401036977768}, {"X": 0.46368342638015747, "Y": 0.1376396268606186}, {"X": 0.4636833071708679, "Y": 0.1538987010717392}, {"X": 0.3956295847892761, "Y": 0.153899148106575}]}, "Id": "2ce0ae4d-b4f0-404c-a219-bd8332a3f4b6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "STONE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09065021574497223, "Height": 0.016428804025053978, "Left": 0.4716264009475708, "Top": 0.13754825294017792}, "Polygon": [{"X": 0.47162652015686035, "Y": 0.13754889369010925}, {"X": 0.5622766017913818, "Y": 0.13754825294017792}, {"X": 0.5622764825820923, "Y": 0.1539764702320099}, {"X": 0.4716264009475708, "Y": 0.15397706627845764}]}, "Id": "2d62ca21-a1ff-43da-8917-47581372b95b"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "LUMPERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13174642622470856, "Height": 0.01890328899025917, "Left": 0.5717591047286987, "Top": 0.137639582157135}, "Polygon": [{"X": 0.5717592239379883, "Y": 0.13764050602912903}, {"X": 0.7035055160522461, "Y": 0.137639582157135}, {"X": 0.7035054564476013, "Y": 0.15654203295707703}, {"X": 0.5717591047286987, "Y": 0.15654286742210388}]}, "Id": "d9f3a0fe-7958-4425-b322-5428ad33ccf0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04577173665165901, "Height": 0.016622493043541908, "Left": 0.7126742005348206, "Top": 0.13769380748271942}, "Polygon": [{"X": 0.7126742601394653, "Y": 0.1376941204071045}, {"X": 0.7584459185600281, "Y": 0.13769380748271942}, {"X": 0.7584459185600281, "Y": 0.1543159931898117}, {"X": 0.7126742005348206, "Y": 0.15431629121303558}]}, "Id": "a6b55e17-afc9-4a45-8aa4-48b9f997e189"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "1222", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062332846224308014, "Height": 0.016494305804371834, "Left": 0.3973669111728668, "Top": 0.16238032281398773}, "Polygon": [{"X": 0.39736706018447876, "Y": 0.16238071024417877}, {"X": 0.45969974994659424, "Y": 0.16238032281398773}, {"X": 0.4596996009349823, "Y": 0.17887426912784576}, {"X": 0.3973669111728668, "Y": 0.17887462675571442}]}, "Id": "978b4b10-99f2-4138-a24f-f3a7a0a31c61"}, {"BlockType": "WORD", "Confidence": 99.34331512451172, "Text": "LODI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06412411481142044, "Height": 0.016708016395568848, "Left": 0.46812567114830017, "Top": 0.162397563457489}, "Polygon": [{"X": 0.4681258201599121, "Y": 0.16239796578884125}, {"X": 0.5322498083114624, "Y": 0.162397563457489}, {"X": 0.5322496891021729, "Y": 0.17910520732402802}, {"X": 0.46812567114830017, "Y": 0.17910557985305786}]}, "Id": "c4fe879e-3d99-45e3-93ff-433d71c72806"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "PL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03130292147397995, "Height": 0.016581116244196892, "Left": 0.541659414768219, "Top": 0.16250035166740417}, "Polygon": [{"X": 0.5416594743728638, "Y": 0.1625005453824997}, {"X": 0.5729622840881348, "Y": 0.16250035166740417}, {"X": 0.57296222448349, "Y": 0.1790812909603119}, {"X": 0.541659414768219, "Y": 0.17908146977424622}]}, "Id": "f5754568-b533-4ba0-b7de-26423bdb1d5d"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "LOS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05221732705831528, "Height": 0.017201781272888184, "Left": 0.3962271809577942, "Top": 0.18721777200698853}, "Polygon": [{"X": 0.39622732996940613, "Y": 0.1872180551290512}, {"X": 0.4484444856643677, "Y": 0.18721777200698853}, {"X": 0.4484443664550781, "Y": 0.20441928505897522}, {"X": 0.3962271809577942, "Y": 0.2044195532798767}]}, "Id": "4bdd5659-04e6-4d2b-867d-8fdb798119bc"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "ANGELES,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13034604489803314, "Height": 0.019532283768057823, "Left": 0.453917920589447, "Top": 0.18715818226337433}, "Polygon": [{"X": 0.45391806960105896, "Y": 0.18715889751911163}, {"X": 0.5842639803886414, "Y": 0.18715818226337433}, {"X": 0.5842638611793518, "Y": 0.20668983459472656}, {"X": 0.453917920589447, "Y": 0.2066904753446579}]}, "Id": "e589c882-c070-43df-ad5a-7b410f131937"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "CA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03677714243531227, "Height": 0.01678590662777424, "Left": 0.5917831063270569, "Top": 0.18743786215782166}, "Polygon": [{"X": 0.5917831659317017, "Y": 0.18743807077407837}, {"X": 0.6285602450370789, "Y": 0.18743786215782166}, {"X": 0.6285601854324341, "Y": 0.20422358810901642}, {"X": 0.5917831063270569, "Y": 0.20422376692295074}]}, "Id": "f505ee09-fec7-48fa-9191-099b5468a211"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "90038", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0816657543182373, "Height": 0.017316078767180443, "Left": 0.6346105933189392, "Top": 0.18720464408397675}, "Polygon": [{"X": 0.6346107125282288, "Y": 0.18720509111881256}, {"X": 0.7162763476371765, "Y": 0.18720464408397675}, {"X": 0.7162763476371765, "Y": 0.2045203149318695}, {"X": 0.6346105933189392, "Y": 0.20452071726322174}]}, "Id": "d723aaef-d4f5-4160-9b1d-cce00cd817ea"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "GENERAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12426218390464783, "Height": 0.016870742663741112, "Left": 0.3595607876777649, "Top": 0.2621188163757324}, "Polygon": [{"X": 0.3595609664916992, "Y": 0.26211920380592346}, {"X": 0.4838229715824127, "Y": 0.2621188163757324}, {"X": 0.48382285237312317, "Y": 0.2789892256259918}, {"X": 0.3595607876777649, "Y": 0.2789895534515381}]}, "Id": "42c1503d-6992-4bc0-aa98-d39d12da1273"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10786934196949005, "Height": 0.016670923680067062, "Left": 0.49194085597991943, "Top": 0.26209259033203125}, "Polygon": [{"X": 0.491940975189209, "Y": 0.2620929479598999}, {"X": 0.5998101830482483, "Y": 0.26209259033203125}, {"X": 0.5998101234436035, "Y": 0.2787632346153259}, {"X": 0.49194085597991943, "Y": 0.2787635028362274}]}, "Id": "c9c2bd0c-3eda-400e-b753-ae5043167204"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06986705213785172, "Height": 0.017276884987950325, "Left": 0.14604590833187103, "Top": 0.34937140345573425}, "Polygon": [{"X": 0.14604617655277252, "Y": 0.34937143325805664}, {"X": 0.21591296792030334, "Y": 0.34937140345573425}, {"X": 0.21591274440288544, "Y": 0.36664828658103943}, {"X": 0.14604590833187103, "Y": 0.36664828658103943}]}, "Id": "8eb10135-7014-44f2-8961-26213e2fd180"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "09/18/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11892370134592056, "Height": 0.017194919288158417, "Left": 0.42969322204589844, "Top": 0.349773645401001}, "Polygon": [{"X": 0.4296933710575104, "Y": 0.34977370500564575}, {"X": 0.548616886138916, "Y": 0.349773645401001}, {"X": 0.5486168265342712, "Y": 0.3669685423374176}, {"X": 0.42969322204589844, "Y": 0.3669685423374176}]}, "Id": "b0ce915b-f45d-4440-b1ba-579a3cf6ff20"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "TIME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06506256014108658, "Height": 0.016878914088010788, "Left": 0.14476104080677032, "Top": 0.3746216297149658}, "Polygon": [{"X": 0.14476129412651062, "Y": 0.3746216297149658}, {"X": 0.2098236083984375, "Y": 0.3746216595172882}, {"X": 0.2098233699798584, "Y": 0.3915005624294281}, {"X": 0.14476104080677032, "Y": 0.3915005028247833}]}, "Id": "7d6e3590-642b-4469-9960-2c0775f6a239"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "1:15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05506588891148567, "Height": 0.01733298785984516, "Left": 0.43181130290031433, "Top": 0.374639630317688}, "Polygon": [{"X": 0.43181145191192627, "Y": 0.374639630317688}, {"X": 0.4868772029876709, "Y": 0.374639630317688}, {"X": 0.48687705397605896, "Y": 0.3919726014137268}, {"X": 0.43181130290031433, "Y": 0.3919725716114044}]}, "Id": "f5ec76f5-a9a6-4830-a440-ead508467395"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CASHIER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11356265842914581, "Height": 0.01708054542541504, "Left": 0.14524805545806885, "Top": 0.4005500376224518}, "Polygon": [{"X": 0.14524832367897034, "Y": 0.4005500376224518}, {"X": 0.25881072878837585, "Y": 0.40055015683174133}, {"X": 0.25881049036979675, "Y": 0.4176305830478668}, {"X": 0.14524805545806885, "Y": 0.4176304042339325}]}, "Id": "fe41bf39-291c-4528-963a-a4f61d3eec21"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NAME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08025242388248444, "Height": 0.016371088102459908, "Left": 0.267005056142807, "Top": 0.40071260929107666}, "Polygon": [{"X": 0.26700523495674133, "Y": 0.40071260929107666}, {"X": 0.34725746512413025, "Y": 0.4007126986980438}, {"X": 0.3472572863101959, "Y": 0.4170836806297302}, {"X": 0.267005056142807, "Y": 0.4170835614204407}]}, "Id": "eb675e33-2b58-4181-a5ed-0d9429cbef72"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "BILL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05559641495347023, "Height": 0.016930080950260162, "Left": 0.4308091402053833, "Top": 0.4008505642414093}, "Polygon": [{"X": 0.43080928921699524, "Y": 0.4008505642414093}, {"X": 0.48640555143356323, "Y": 0.4008506238460541}, {"X": 0.4864054322242737, "Y": 0.41778063774108887}, {"X": 0.4308091402053833, "Y": 0.4177805483341217}]}, "Id": "1a4a2df4-58a4-42bc-8194-eb832ddcabc3"}, {"BlockType": "WORD", "Confidence": 97.72062683105469, "Text": "S.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02279682829976082, "Height": 0.017388105392456055, "Left": 0.4934510886669159, "Top": 0.4008251428604126}, "Polygon": [{"X": 0.49345123767852783, "Y": 0.4008251428604126}, {"X": 0.5162479281425476, "Y": 0.400825172662735}, {"X": 0.5162478089332581, "Y": 0.41821324825286865}, {"X": 0.4934510886669159, "Y": 0.41821321845054626}]}, "Id": "f39a0975-f0c9-4939-b30e-051bd7cfab67"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1515919715166092, "Height": 0.017001284286379814, "Left": 0.14509081840515137, "Top": 0.4255971610546112}, "Polygon": [{"X": 0.14509107172489166, "Y": 0.4255971610546112}, {"X": 0.29668277502059937, "Y": 0.4255974590778351}, {"X": 0.29668259620666504, "Y": 0.44259846210479736}, {"X": 0.14509081840515137, "Y": 0.4425981044769287}]}, "Id": "43826853-07ee-49eb-9d0a-18fffaa41ad7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "NAME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0800979882478714, "Height": 0.016554150730371475, "Left": 0.30467796325683594, "Top": 0.42586952447891235}, "Polygon": [{"X": 0.30467817187309265, "Y": 0.42586952447891235}, {"X": 0.38477596640586853, "Y": 0.4258696734905243}, {"X": 0.3847757875919342, "Y": 0.44242367148399353}, {"X": 0.30467796325683594, "Y": 0.4424234926700592}]}, "Id": "0c8bd698-0ff0-4e7e-a21e-3141d2d7b923"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RCH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05484120920300484, "Height": 0.01703059859573841, "Left": 0.43093621730804443, "Top": 0.42570918798446655}, "Polygon": [{"X": 0.43093636631965637, "Y": 0.42570918798446655}, {"X": 0.4857774078845978, "Y": 0.4257092773914337}, {"X": 0.4857772886753082, "Y": 0.4427397847175598}, {"X": 0.43093621730804443, "Y": 0.4427396357059479}]}, "Id": "e62533cc-4672-4188-8878-cd7990c24065"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRUCKING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14052052795886993, "Height": 0.01739138923585415, "Left": 0.49309584498405457, "Top": 0.42567741870880127}, "Polygon": [{"X": 0.4930959641933441, "Y": 0.42567741870880127}, {"X": 0.6336163878440857, "Y": 0.42567768692970276}, {"X": 0.6336163282394409, "Y": 0.44306880235671997}, {"X": 0.49309584498405457, "Y": 0.4430684745311737}]}, "Id": "20e088cf-ad34-4bd7-beac-a4dc89c3724e"}, {"BlockType": "WORD", "Confidence": 98.52519226074219, "Text": "Item(s)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0907580628991127, "Height": 0.0198813509196043, "Left": 0.18134883046150208, "Top": 0.47574320435523987}, "Polygon": [{"X": 0.18134911358356476, "Y": 0.47574320435523987}, {"X": 0.2721068859100342, "Y": 0.47574350237846375}, {"X": 0.2721066474914551, "Y": 0.4956245422363281}, {"X": 0.18134883046150208, "Y": 0.4956241846084595}]}, "Id": "8ac84873-cc81-4b48-bf35-c839b228446b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11660171300172806, "Height": 0.0214186180382967, "Left": 0.3939562737941742, "Top": 0.47580403089523315}, "Polygon": [{"X": 0.3939564824104309, "Y": 0.47580403089523315}, {"X": 0.510558009147644, "Y": 0.4758044183254242}, {"X": 0.5105578303337097, "Y": 0.4972226321697235}, {"X": 0.3939562737941742, "Y": 0.4972221851348877}]}, "Id": "18e822b3-3705-46ef-80cc-a3bf33883765"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06536411494016647, "Height": 0.017286501824855804, "Left": 0.7336059212684631, "Top": 0.47594982385635376}, "Polygon": [{"X": 0.7336059212684631, "Y": 0.47594982385635376}, {"X": 0.7989700436592102, "Y": 0.4759500324726105}, {"X": 0.7989699840545654, "Y": 0.49323633313179016}, {"X": 0.7336059212684631, "Y": 0.49323606491088867}]}, "Id": "f7c3926f-69a2-4ed6-b3c0-8eb9f7f6537b"}, {"BlockType": "WORD", "Confidence": 94.64903259277344, "Text": "5plts", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06395619362592697, "Height": 0.021163327619433403, "Left": 0.18070465326309204, "Top": 0.500420093536377}, "Polygon": [{"X": 0.1807049661874771, "Y": 0.500420093536377}, {"X": 0.2446608543395996, "Y": 0.500420331954956}, {"X": 0.24466058611869812, "Y": 0.5215834379196167}, {"X": 0.18070465326309204, "Y": 0.521583080291748}]}, "Id": "a78e6462-bb1e-4670-8e96-9026c8e623a6"}, {"BlockType": "WORD", "Confidence": 98.81059265136719, "Text": "into12", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08398232609033585, "Height": 0.01716327667236328, "Left": 0.18069301545619965, "Top": 0.5255658626556396}, "Polygon": [{"X": 0.18069326877593994, "Y": 0.5255658626556396}, {"X": 0.2646753489971161, "Y": 0.5255662798881531}, {"X": 0.2646751403808594, "Y": 0.5427291393280029}, {"X": 0.18069301545619965, "Y": 0.5427286624908447}]}, "Id": "967d1955-b5d5-4c77-ae90-e7db7b1b62fb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "446831572", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14760859310626984, "Height": 0.017352089285850525, "Left": 0.17945846915245056, "Top": 0.5506312251091003}, "Polygon": [{"X": 0.17945870757102966, "Y": 0.5506312251091003}, {"X": 0.3270670473575592, "Y": 0.5506319999694824}, {"X": 0.3270668685436249, "Y": 0.5679832696914673}, {"X": 0.17945846915245056, "Y": 0.5679823756217957}]}, "Id": "85ac5ec3-5191-4060-8f30-347b4382d96d"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "140.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08760174363851547, "Height": 0.01673538237810135, "Left": 0.7344616055488586, "Top": 0.576383650302887}, "Polygon": [{"X": 0.7344616055488586, "Y": 0.576383650302887}, {"X": 0.8220633268356323, "Y": 0.5763842463493347}, {"X": 0.8220633268356323, "Y": 0.5931190252304077}, {"X": 0.7344616055488586, "Y": 0.59311842918396}]}, "Id": "acf7dfaf-dbbd-4caa-bcb8-aa2a9b2467a4"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "SUBTOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1422644406557083, "Height": 0.017402630299329758, "Left": 0.2505494952201843, "Top": 0.6266503930091858}, "Polygon": [{"X": 0.25054970383644104, "Y": 0.6266503930091858}, {"X": 0.39281392097473145, "Y": 0.6266515254974365}, {"X": 0.3928137719631195, "Y": 0.6440529823303223}, {"X": 0.2505494952201843, "Y": 0.6440517902374268}]}, "Id": "6560a1ca-15fe-40b0-9dfc-174a0304b2f7"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "140.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08754293620586395, "Height": 0.016878917813301086, "Left": 0.7343147397041321, "Top": 0.6273229718208313}, "Polygon": [{"X": 0.7343147993087769, "Y": 0.6273229718208313}, {"X": 0.8218576908111572, "Y": 0.6273236274719238}, {"X": 0.8218576908111572, "Y": 0.6442018747329712}, {"X": 0.7343147397041321, "Y": 0.6442010998725891}]}, "Id": "078d2fbe-2b3b-40fc-a51b-00a6e38fff2c"}, {"BlockType": "WORD", "Confidence": 99.55735778808594, "Text": "%", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024221153929829597, "Height": 0.01737610250711441, "Left": 0.2503453493118286, "Top": 0.651780366897583}, "Polygon": [{"X": 0.2503455579280853, "Y": 0.651780366897583}, {"X": 0.27456650137901306, "Y": 0.6517806053161621}, {"X": 0.27456629276275635, "Y": 0.6691564917564392}, {"X": 0.2503453493118286, "Y": 0.6691562533378601}]}, "Id": "fc658fca-da94-41e2-9896-154a578cc862"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "DISCOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1424688696861267, "Height": 0.01728835515677929, "Left": 0.2831610143184662, "Top": 0.6516310572624207}, "Polygon": [{"X": 0.2831612229347229, "Y": 0.6516310572624207}, {"X": 0.4256298840045929, "Y": 0.6516323089599609}, {"X": 0.42562973499298096, "Y": 0.6689194440841675}, {"X": 0.2831610143184662, "Y": 0.6689181327819824}]}, "Id": "756e467b-1c26-49ee-aa16-0d96333768bb"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "-0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0668911263346672, "Height": 0.017207179218530655, "Left": 0.7325383424758911, "Top": 0.6521713137626648}, "Polygon": [{"X": 0.7325383424758911, "Y": 0.6521713137626648}, {"X": 0.7994294762611389, "Y": 0.6521718502044678}, {"X": 0.7994294762611389, "Y": 0.6693784594535828}, {"X": 0.7325383424758911, "Y": 0.669377863407135}]}, "Id": "ceca8fdb-d407-4a50-a9cc-7f0e360806d6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0878949984908104, "Height": 0.016998037695884705, "Left": 0.24931780993938446, "Top": 0.752142608165741}, "Polygon": [{"X": 0.24931801855564117, "Y": 0.752142608165741}, {"X": 0.33721280097961426, "Y": 0.7521436214447021}, {"X": 0.33721262216567993, "Y": 0.7691406011581421}, {"X": 0.24931780993938446, "Y": 0.7691395282745361}]}, "Id": "1fd383cf-864e-496b-8928-f7d4468bf696"}, {"BlockType": "WORD", "Confidence": 99.51750183105469, "Text": "140.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08750652521848679, "Height": 0.01718982309103012, "Left": 0.5377163887023926, "Top": 0.752362072467804}, "Polygon": [{"X": 0.5377164483070374, "Y": 0.752362072467804}, {"X": 0.6252228617668152, "Y": 0.7523630857467651}, {"X": 0.6252228021621704, "Y": 0.7695518732070923}, {"X": 0.5377163887023926, "Y": 0.7695508003234863}]}, "Id": "1e968058-df86-4bd1-b5d2-8aa8c29914a5"}, {"BlockType": "WORD", "Confidence": 99.75505828857422, "Text": "USD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05587856471538544, "Height": 0.017058495432138443, "Left": 0.6338269114494324, "Top": 0.7525560259819031}, "Polygon": [{"X": 0.6338269710540771, "Y": 0.7525560259819031}, {"X": 0.689705491065979, "Y": 0.7525566816329956}, {"X": 0.6897054314613342, "Y": 0.7696145176887512}, {"X": 0.6338269114494324, "Y": 0.7696138620376587}]}, "Id": "522fdf57-32df-4787-8d15-e3f991738e35"}, {"BlockType": "WORD", "Confidence": 99.67693328857422, "Text": "($)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03370094671845436, "Height": 0.020492704585194588, "Left": 0.6974624395370483, "Top": 0.7524987459182739}, "Polygon": [{"X": 0.6974624991416931, "Y": 0.7524987459182739}, {"X": 0.7311633825302124, "Y": 0.7524991631507874}, {"X": 0.7311633229255676, "Y": 0.7729914784431458}, {"X": 0.6974624395370483, "Y": 0.7729910612106323}]}, "Id": "babb82f1-8d4d-45c6-9287-d334b2e83697"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11770673096179962, "Height": 0.021005237475037575, "Left": 0.2510679066181183, "Top": 0.7775338292121887}, "Polygon": [{"X": 0.2510681748390198, "Y": 0.7775338292121887}, {"X": 0.3687746226787567, "Y": 0.7775352597236633}, {"X": 0.3687744140625, "Y": 0.7985390424728394}, {"X": 0.2510679066181183, "Y": 0.7985374927520752}]}, "Id": "71e27014-6436-4c00-b2b3-31a4cb0ea888"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Method", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10317442566156387, "Height": 0.017947297543287277, "Left": 0.37580734491348267, "Top": 0.7769219279289246}, "Polygon": [{"X": 0.375807523727417, "Y": 0.7769219279289246}, {"X": 0.47898176312446594, "Y": 0.7769232392311096}, {"X": 0.4789816439151764, "Y": 0.7948692440986633}, {"X": 0.37580734491348267, "Y": 0.7948678731918335}]}, "Id": "3fb32760-2a16-4195-8271-c0092d04ff59"}, {"BlockType": "WORD", "Confidence": 94.10076904296875, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0065496680326759815, "Height": 0.01334246527403593, "Left": 0.4873754382133484, "Top": 0.7810683250427246}, "Polygon": [{"X": 0.48737552762031555, "Y": 0.7810683250427246}, {"X": 0.4939250946044922, "Y": 0.7810683846473694}, {"X": 0.493925005197525, "Y": 0.794410765171051}, {"X": 0.4873754382133484, "Y": 0.7944107055664062}]}, "Id": "d3c7d71f-60a3-4908-9ebb-661f6159b21d"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Cash", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06405014544725418, "Height": 0.01731560379266739, "Left": 0.5022860765457153, "Top": 0.7773568034172058}, "Polygon": [{"X": 0.5022861957550049, "Y": 0.7773568034172058}, {"X": 0.5663362145423889, "Y": 0.7773575782775879}, {"X": 0.5663361549377441, "Y": 0.794672429561615}, {"X": 0.5022860765457153, "Y": 0.7946715950965881}]}, "Id": "9713fdaf-3af2-434e-b052-6378c2189d23"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Return", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08887077867984772, "Height": 0.016705887392163277, "Left": 0.25103074312210083, "Top": 0.803646981716156}, "Polygon": [{"X": 0.25103095173835754, "Y": 0.803646981716156}, {"X": 0.33990150690078735, "Y": 0.8036481738090515}, {"X": 0.339901328086853, "Y": 0.8203528523445129}, {"X": 0.25103074312210083, "Y": 0.8203516006469727}]}, "Id": "9f945e6e-3701-4c4e-af9f-2a32c155c411"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Policy", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07693478465080261, "Height": 0.02153954468667507, "Left": 0.3491474390029907, "Top": 0.8032760620117188}, "Polygon": [{"X": 0.34914764761924744, "Y": 0.8032760620117188}, {"X": 0.42608222365379333, "Y": 0.8032770752906799}, {"X": 0.4260820150375366, "Y": 0.8248156309127808}, {"X": 0.3491474390029907, "Y": 0.8248145580291748}]}, "Id": "4874cae4-d824-434d-8c15-e6f7ffc24a5e"}, {"BlockType": "WORD", "Confidence": 94.92107391357422, "Text": ":", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0063062007538974285, "Height": 0.013572930358350277, "Left": 0.43312790989875793, "Top": 0.8071457147598267}, "Polygon": [{"X": 0.4331280291080475, "Y": 0.8071457147598267}, {"X": 0.43943411111831665, "Y": 0.8071457743644714}, {"X": 0.4394339919090271, "Y": 0.8207186460494995}, {"X": 0.43312790989875793, "Y": 0.82071852684021}]}, "Id": "4880de7b-3546-49ce-93e6-a39913a9c53e"}, {"BlockType": "WORD", "Confidence": 99.85192108154297, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03749748691916466, "Height": 0.017055338248610497, "Left": 0.44840532541275024, "Top": 0.8036060333251953}, "Polygon": [{"X": 0.4484054744243622, "Y": 0.8036060333251953}, {"X": 0.4859028160572052, "Y": 0.8036065697669983}, {"X": 0.48590266704559326, "Y": 0.8206613659858704}, {"X": 0.44840532541275024, "Y": 0.8206608891487122}]}, "Id": "48cc114a-1f19-4932-a42a-a31e4760fe65"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Returns", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10380043834447861, "Height": 0.017088277265429497, "Left": 0.49441346526145935, "Top": 0.8038665056228638}, "Polygon": [{"X": 0.4944136142730713, "Y": 0.8038665056228638}, {"X": 0.5982139110565186, "Y": 0.8038679361343384}, {"X": 0.5982138514518738, "Y": 0.8209547996520996}, {"X": 0.49441346526145935, "Y": 0.820953369140625}]}, "Id": "47b4ce87-e5c3-4195-8f3c-21a9a97820d2"}, {"BlockType": "WORD", "Confidence": 99.56472778320312, "Text": "UNFI-MORENO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1994001716375351, "Height": 0.017147112637758255, "Left": 0.21477819979190826, "Top": 0.85373455286026}, "Polygon": [{"X": 0.21477843821048737, "Y": 0.85373455286026}, {"X": 0.41417837142944336, "Y": 0.8537375330924988}, {"X": 0.4141782224178314, "Y": 0.8708816766738892}, {"X": 0.21477819979190826, "Y": 0.8708785772323608}]}, "Id": "739ad959-cf5b-4ce2-a166-934186450bc9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "VALLEY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09850630164146423, "Height": 0.01695817895233631, "Left": 0.42037123441696167, "Top": 0.8538600206375122}, "Polygon": [{"X": 0.4203713834285736, "Y": 0.8538600206375122}, {"X": 0.5188775062561035, "Y": 0.8538615107536316}, {"X": 0.518877387046814, "Y": 0.8708181977272034}, {"X": 0.42037123441696167, "Y": 0.870816707611084}]}, "Id": "00e0c483-bcec-4193-a093-abbc08e7d840"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "0843ae75-80c1-40be-9891-8da55497dd87", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "0843ae75-80c1-40be-9891-8da55497dd87", "content-type": "application/x-amz-json-1.1", "content-length": "43663", "date": "Wed, 24 Sep 2025 13:26:52 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "2bbec784-76c9-4b9f-9b40-cbd74a623cbb", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:55 GMT", "content-type": "application/json", "content-length": "1101", "connection": "keep-alive", "x-amzn-requestid": "2bbec784-76c9-4b9f-9b40-cbd74a623cbb"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We need to determine doc_type.\n\nText looks like receipt, not invoice. Keywords: \"Receipt number\", \"GENERAL RECEIPT\", \"CASHIER NAME\", \"Payment Method: Cash\". This likely a lumper receipt? The lumper receipt definition: Invoice or receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This receipt is for 5plts. It shows quantity, price, subtotal, total. Looks like a lumper receipt. There's no invoice, it's a receipt. It's a \"Receipt\" for loading/unloading. So doc_type: lumper_receipt.\n\nThus output JSON: page 1 lumper_receipt."}}}, {"text": "{\n    \"documents\": [\n        {\n            \"page_no\": 1,\n            \"doc_type\": \"lumper_receipt\"\n        }\n    ]\n}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2722, "outputTokens": 199, "totalTokens": 2921}, "metrics": {"latencyMs": 1052}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/4dcbcd75_V3BCW6BW9XVKKO6WY2YJ.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nReceipt number 128521\nCAPE STONE LUMPERS, LLC\n1222 LODI PL\nLOS ANGELES, CA 90038\nGENERAL RECEIPT\nDATE\n09/18/25\nTIME\n1:15\nCASHIER NAME\nBILL S.\nCUSTOMER NAME RCH TRUCKING\nItem(s)\nQuantity\nPrice\n5plts\ninto12\n446831572\n140.00\nSUBTOTAL\n140.00\n% DISCOUNT\n-0.00\nTOTAL\n140.00 USD ($)\nPayment Method : Cash\nReturn Policy : No Returns\nUNFI-MORENO VALLEY\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}