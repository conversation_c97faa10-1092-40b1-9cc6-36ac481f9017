2025-09-18 13:01:01,539 - INFO - Logging initialized. Log file: logs/test_classification_20250918_130101.log
2025-09-18 13:01:01,540 - INFO - 📁 Found 16 files to process
2025-09-18 13:01:01,540 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:01:01,540 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-18 13:01:01,540 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-18 13:01:01,540 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-18 13:01:01,540 - INFO - ⬆️ [13:01:01] Uploading: 6_scale_ticket_1.pdf
2025-09-18 13:01:05,154 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/12a2a95b_6_scale_ticket_1.pdf
2025-09-18 13:01:05,154 - INFO - 🔍 [13:01:05] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 13:01:05,155 - INFO - ⬆️ [13:01:05] Uploading: 6_scale_ticket_10.png
2025-09-18 13:01:05,157 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:05,180 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:05,188 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/12a2a95b_6_scale_ticket_1.pdf
2025-09-18 13:01:05,188 - INFO - Processing PDF from S3...
2025-09-18 13:01:05,189 - INFO - Downloading PDF from S3 to /tmp/tmpb54jivys/12a2a95b_6_scale_ticket_1.pdf
2025-09-18 13:01:07,307 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/e7243627_6_scale_ticket_10.png
2025-09-18 13:01:07,307 - INFO - 🔍 [13:01:07] Starting classification: 6_scale_ticket_10.png
2025-09-18 13:01:07,308 - INFO - ⬆️ [13:01:07] Uploading: 6_scale_ticket_11.jpg
2025-09-18 13:01:07,310 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:07,325 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:07,330 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e7243627_6_scale_ticket_10.png
2025-09-18 13:01:07,330 - INFO - Processing image from S3...
2025-09-18 13:01:07,866 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:07,870 - INFO - Splitting PDF 12a2a95b_6_scale_ticket_1 into 1 pages
2025-09-18 13:01:07,872 - INFO - Split PDF into 1 pages
2025-09-18 13:01:07,872 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:07,872 - INFO - Expected pages: [1]
2025-09-18 13:01:09,093 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg -> s3://document-extraction-logistically/temp/9c133b30_6_scale_ticket_11.jpg
2025-09-18 13:01:09,093 - INFO - 🔍 [13:01:09] Starting classification: 6_scale_ticket_11.jpg
2025-09-18 13:01:09,094 - INFO - ⬆️ [13:01:09] Uploading: 6_scale_ticket_12.jpg
2025-09-18 13:01:09,096 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:09,123 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:09,129 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9c133b30_6_scale_ticket_11.jpg
2025-09-18 13:01:09,130 - INFO - Processing image from S3...
2025-09-18 13:01:10,920 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg -> s3://document-extraction-logistically/temp/d44a879d_6_scale_ticket_12.jpg
2025-09-18 13:01:10,920 - INFO - 🔍 [13:01:10] Starting classification: 6_scale_ticket_12.jpg
2025-09-18 13:01:10,921 - INFO - ⬆️ [13:01:10] Uploading: 6_scale_ticket_13.jpg
2025-09-18 13:01:10,922 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:10,932 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:10,936 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d44a879d_6_scale_ticket_12.jpg
2025-09-18 13:01:10,936 - INFO - Processing image from S3...
2025-09-18 13:01:11,034 - INFO - S3 Image temp/e7243627_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 13:01:11,034 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:11,035 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:11,563 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg -> s3://document-extraction-logistically/temp/249b4e83_6_scale_ticket_13.jpg
2025-09-18 13:01:11,563 - INFO - 🔍 [13:01:11] Starting classification: 6_scale_ticket_13.jpg
2025-09-18 13:01:11,564 - INFO - ⬆️ [13:01:11] Uploading: 6_scale_ticket_14.jpg
2025-09-18 13:01:11,564 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:11,574 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:11,576 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/249b4e83_6_scale_ticket_13.jpg
2025-09-18 13:01:11,576 - INFO - Processing image from S3...
2025-09-18 13:01:12,423 - INFO - S3 Image temp/9c133b30_6_scale_ticket_11.jpg: Extracted 361 characters, 32 lines
2025-09-18 13:01:12,423 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:12,423 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:13,373 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e7243627_6_scale_ticket_10.png
2025-09-18 13:01:13,577 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg -> s3://document-extraction-logistically/temp/f3584293_6_scale_ticket_14.jpg
2025-09-18 13:01:13,577 - INFO - 🔍 [13:01:13] Starting classification: 6_scale_ticket_14.jpg
2025-09-18 13:01:13,578 - INFO - ⬆️ [13:01:13] Uploading: 6_scale_ticket_15.pdf
2025-09-18 13:01:13,578 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:13,589 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:13,597 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f3584293_6_scale_ticket_14.jpg
2025-09-18 13:01:13,597 - INFO - Processing image from S3...
2025-09-18 13:01:13,831 - INFO - Page 1: Extracted 1382 characters, 62 lines from 12a2a95b_6_scale_ticket_1_8ce114bf_page_001.pdf
2025-09-18 13:01:13,831 - INFO - Successfully processed page 1
2025-09-18 13:01:13,831 - INFO - Combined 1 pages into final text
2025-09-18 13:01:13,832 - INFO - Text validation for 12a2a95b_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 13:01:13,832 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:13,832 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:14,186 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf -> s3://document-extraction-logistically/temp/ba99c498_6_scale_ticket_15.pdf
2025-09-18 13:01:14,187 - INFO - 🔍 [13:01:14] Starting classification: 6_scale_ticket_15.pdf
2025-09-18 13:01:14,187 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:14,190 - INFO - ⬆️ [13:01:14] Uploading: 6_scale_ticket_16.pdf
2025-09-18 13:01:14,247 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:14,258 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba99c498_6_scale_ticket_15.pdf
2025-09-18 13:01:14,259 - INFO - Processing PDF from S3...
2025-09-18 13:01:14,259 - INFO - Downloading PDF from S3 to /tmp/tmp_l24onke/ba99c498_6_scale_ticket_15.pdf
2025-09-18 13:01:14,422 - INFO - S3 Image temp/d44a879d_6_scale_ticket_12.jpg: Extracted 360 characters, 32 lines
2025-09-18 13:01:14,422 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:14,422 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:14,733 - INFO - S3 Image temp/249b4e83_6_scale_ticket_13.jpg: Extracted 904 characters, 46 lines
2025-09-18 13:01:14,733 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:14,733 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:15,236 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf -> s3://document-extraction-logistically/temp/9870dc58_6_scale_ticket_16.pdf
2025-09-18 13:01:15,236 - INFO - 🔍 [13:01:15] Starting classification: 6_scale_ticket_16.pdf
2025-09-18 13:01:15,237 - INFO - ⬆️ [13:01:15] Uploading: 6_scale_ticket_2.pdf
2025-09-18 13:01:15,239 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:15,253 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:15,259 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9870dc58_6_scale_ticket_16.pdf
2025-09-18 13:01:15,260 - INFO - Processing PDF from S3...
2025-09-18 13:01:15,260 - INFO - Downloading PDF from S3 to /tmp/tmptrfmte9s/9870dc58_6_scale_ticket_16.pdf
2025-09-18 13:01:15,385 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9c133b30_6_scale_ticket_11.jpg
2025-09-18 13:01:15,887 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf -> s3://document-extraction-logistically/temp/21b42ca0_6_scale_ticket_2.pdf
2025-09-18 13:01:15,887 - INFO - 🔍 [13:01:15] Starting classification: 6_scale_ticket_2.pdf
2025-09-18 13:01:15,888 - INFO - ⬆️ [13:01:15] Uploading: 6_scale_ticket_3.pdf
2025-09-18 13:01:15,890 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:15,910 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:15,920 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/21b42ca0_6_scale_ticket_2.pdf
2025-09-18 13:01:15,920 - INFO - Processing PDF from S3...
2025-09-18 13:01:15,932 - INFO - Downloading PDF from S3 to /tmp/tmpi_8q7h3q/21b42ca0_6_scale_ticket_2.pdf
2025-09-18 13:01:16,316 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:16,317 - INFO - Splitting PDF ba99c498_6_scale_ticket_15 into 1 pages
2025-09-18 13:01:16,318 - INFO - Split PDF into 1 pages
2025-09-18 13:01:16,318 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:16,318 - INFO - Expected pages: [1]
2025-09-18 13:01:16,457 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/12a2a95b_6_scale_ticket_1.pdf
2025-09-18 13:01:16,486 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d44a879d_6_scale_ticket_12.jpg
2025-09-18 13:01:16,487 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf -> s3://document-extraction-logistically/temp/20259ef4_6_scale_ticket_3.pdf
2025-09-18 13:01:16,489 - INFO - 🔍 [13:01:16] Starting classification: 6_scale_ticket_3.pdf
2025-09-18 13:01:16,489 - INFO - ⬆️ [13:01:16] Uploading: 6_scale_ticket_4.pdf
2025-09-18 13:01:16,490 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:16,500 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:16,502 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/20259ef4_6_scale_ticket_3.pdf
2025-09-18 13:01:16,502 - INFO - Processing PDF from S3...
2025-09-18 13:01:16,502 - INFO - Downloading PDF from S3 to /tmp/tmpu40ljyns/20259ef4_6_scale_ticket_3.pdf
2025-09-18 13:01:16,648 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/249b4e83_6_scale_ticket_13.jpg
2025-09-18 13:01:17,182 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf -> s3://document-extraction-logistically/temp/8201ca49_6_scale_ticket_4.pdf
2025-09-18 13:01:17,182 - INFO - 🔍 [13:01:17] Starting classification: 6_scale_ticket_4.pdf
2025-09-18 13:01:17,183 - INFO - ⬆️ [13:01:17] Uploading: 6_scale_ticket_5.pdf
2025-09-18 13:01:17,184 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:17,199 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:17,205 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8201ca49_6_scale_ticket_4.pdf
2025-09-18 13:01:17,205 - INFO - Processing PDF from S3...
2025-09-18 13:01:17,205 - INFO - Downloading PDF from S3 to /tmp/tmplhxm9s0f/8201ca49_6_scale_ticket_4.pdf
2025-09-18 13:01:17,668 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:17,670 - INFO - Splitting PDF 9870dc58_6_scale_ticket_16 into 1 pages
2025-09-18 13:01:17,675 - INFO - Split PDF into 1 pages
2025-09-18 13:01:17,675 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:17,675 - INFO - Expected pages: [1]
2025-09-18 13:01:17,714 - INFO - S3 Image temp/f3584293_6_scale_ticket_14.jpg: Extracted 489 characters, 64 lines
2025-09-18 13:01:17,714 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:17,714 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:17,808 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf -> s3://document-extraction-logistically/temp/232dc264_6_scale_ticket_5.pdf
2025-09-18 13:01:17,808 - INFO - 🔍 [13:01:17] Starting classification: 6_scale_ticket_5.pdf
2025-09-18 13:01:17,809 - INFO - ⬆️ [13:01:17] Uploading: 6_scale_ticket_6.pdf
2025-09-18 13:01:17,810 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:17,827 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:17,830 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/232dc264_6_scale_ticket_5.pdf
2025-09-18 13:01:17,830 - INFO - Processing PDF from S3...
2025-09-18 13:01:17,831 - INFO - Downloading PDF from S3 to /tmp/tmpx2kmoool/232dc264_6_scale_ticket_5.pdf
2025-09-18 13:01:18,365 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:18,368 - INFO - Splitting PDF 21b42ca0_6_scale_ticket_2 into 1 pages
2025-09-18 13:01:18,370 - INFO - Split PDF into 1 pages
2025-09-18 13:01:18,371 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:18,371 - INFO - Expected pages: [1]
2025-09-18 13:01:18,691 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf -> s3://document-extraction-logistically/temp/5ce5d512_6_scale_ticket_6.pdf
2025-09-18 13:01:18,691 - INFO - 🔍 [13:01:18] Starting classification: 6_scale_ticket_6.pdf
2025-09-18 13:01:18,691 - INFO - ⬆️ [13:01:18] Uploading: 6_scale_ticket_7.pdf
2025-09-18 13:01:18,692 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:18,703 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:18,705 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ce5d512_6_scale_ticket_6.pdf
2025-09-18 13:01:18,705 - INFO - Processing PDF from S3...
2025-09-18 13:01:18,705 - INFO - Downloading PDF from S3 to /tmp/tmpw1p260wt/5ce5d512_6_scale_ticket_6.pdf
2025-09-18 13:01:18,786 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:18,787 - INFO - Splitting PDF 20259ef4_6_scale_ticket_3 into 1 pages
2025-09-18 13:01:18,788 - INFO - Split PDF into 1 pages
2025-09-18 13:01:18,788 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:18,788 - INFO - Expected pages: [1]
2025-09-18 13:01:19,335 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf -> s3://document-extraction-logistically/temp/d5b573b8_6_scale_ticket_7.pdf
2025-09-18 13:01:19,336 - INFO - 🔍 [13:01:19] Starting classification: 6_scale_ticket_7.pdf
2025-09-18 13:01:19,337 - INFO - ⬆️ [13:01:19] Uploading: 6_scale_ticket_8.pdf
2025-09-18 13:01:19,337 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:19,352 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:19,356 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d5b573b8_6_scale_ticket_7.pdf
2025-09-18 13:01:19,356 - INFO - Processing PDF from S3...
2025-09-18 13:01:19,356 - INFO - Downloading PDF from S3 to /tmp/tmpz26vqzet/d5b573b8_6_scale_ticket_7.pdf
2025-09-18 13:01:19,581 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:19,584 - INFO - Splitting PDF 8201ca49_6_scale_ticket_4 into 1 pages
2025-09-18 13:01:19,585 - INFO - Split PDF into 1 pages
2025-09-18 13:01:19,585 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:19,586 - INFO - Expected pages: [1]
2025-09-18 13:01:19,800 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f3584293_6_scale_ticket_14.jpg
2025-09-18 13:01:19,936 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:19,939 - INFO - Splitting PDF 232dc264_6_scale_ticket_5 into 1 pages
2025-09-18 13:01:19,940 - INFO - Split PDF into 1 pages
2025-09-18 13:01:19,940 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:19,940 - INFO - Expected pages: [1]
2025-09-18 13:01:20,005 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf -> s3://document-extraction-logistically/temp/b48ada57_6_scale_ticket_8.pdf
2025-09-18 13:01:20,005 - INFO - 🔍 [13:01:20] Starting classification: 6_scale_ticket_8.pdf
2025-09-18 13:01:20,006 - INFO - ⬆️ [13:01:20] Uploading: 6_scale_ticket_9.pdf
2025-09-18 13:01:20,006 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:20,020 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:20,024 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b48ada57_6_scale_ticket_8.pdf
2025-09-18 13:01:20,024 - INFO - Processing PDF from S3...
2025-09-18 13:01:20,024 - INFO - Downloading PDF from S3 to /tmp/tmpg3rjekzu/b48ada57_6_scale_ticket_8.pdf
2025-09-18 13:01:20,591 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf -> s3://document-extraction-logistically/temp/131f797f_6_scale_ticket_9.pdf
2025-09-18 13:01:20,591 - INFO - 🔍 [13:01:20] Starting classification: 6_scale_ticket_9.pdf
2025-09-18 13:01:20,592 - INFO - Initializing TextractProcessor...
2025-09-18 13:01:20,608 - INFO - Initializing BedrockProcessor...
2025-09-18 13:01:20,611 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/131f797f_6_scale_ticket_9.pdf
2025-09-18 13:01:20,613 - INFO - Processing PDF from S3...
2025-09-18 13:01:20,615 - INFO - Downloading PDF from S3 to /tmp/tmpjbov3jyr/131f797f_6_scale_ticket_9.pdf
2025-09-18 13:01:20,636 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:20,636 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_10.json
2025-09-18 13:01:20,930 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e7243627_6_scale_ticket_10.png
2025-09-18 13:01:20,939 - INFO - 

6_scale_ticket_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:20,939 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_11.json
2025-09-18 13:01:21,115 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:21,117 - INFO - Splitting PDF 5ce5d512_6_scale_ticket_6 into 1 pages
2025-09-18 13:01:21,118 - INFO - Split PDF into 1 pages
2025-09-18 13:01:21,118 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:21,118 - INFO - Expected pages: [1]
2025-09-18 13:01:21,135 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:21,136 - INFO - Splitting PDF d5b573b8_6_scale_ticket_7 into 1 pages
2025-09-18 13:01:21,137 - INFO - Split PDF into 1 pages
2025-09-18 13:01:21,137 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:21,137 - INFO - Expected pages: [1]
2025-09-18 13:01:21,225 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9c133b30_6_scale_ticket_11.jpg
2025-09-18 13:01:21,249 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:21,249 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_1.json
2025-09-18 13:01:21,444 - INFO - Page 1: Extracted 678 characters, 29 lines from ba99c498_6_scale_ticket_15_35b9864f_page_001.pdf
2025-09-18 13:01:21,444 - INFO - Successfully processed page 1
2025-09-18 13:01:21,444 - INFO - Combined 1 pages into final text
2025-09-18 13:01:21,445 - INFO - Text validation for ba99c498_6_scale_ticket_15: 695 characters, 1 pages
2025-09-18 13:01:21,445 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:21,445 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:21,542 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/12a2a95b_6_scale_ticket_1.pdf
2025-09-18 13:01:21,547 - INFO - 

6_scale_ticket_12.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:21,547 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_12.json
2025-09-18 13:01:21,841 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d44a879d_6_scale_ticket_12.jpg
2025-09-18 13:01:21,849 - INFO - 

6_scale_ticket_13.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 13:01:21,850 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_13.json
2025-09-18 13:01:22,140 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/249b4e83_6_scale_ticket_13.jpg
2025-09-18 13:01:22,157 - INFO - 

6_scale_ticket_14.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:22,157 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_14.json
2025-09-18 13:01:22,357 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:22,359 - INFO - Splitting PDF b48ada57_6_scale_ticket_8 into 1 pages
2025-09-18 13:01:22,361 - INFO - Split PDF into 1 pages
2025-09-18 13:01:22,361 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:22,361 - INFO - Expected pages: [1]
2025-09-18 13:01:22,448 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f3584293_6_scale_ticket_14.jpg
2025-09-18 13:01:22,473 - INFO - Splitting PDF into individual pages...
2025-09-18 13:01:22,474 - INFO - Splitting PDF 131f797f_6_scale_ticket_9 into 1 pages
2025-09-18 13:01:22,476 - INFO - Split PDF into 1 pages
2025-09-18 13:01:22,477 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:01:22,477 - INFO - Expected pages: [1]
2025-09-18 13:01:23,139 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ba99c498_6_scale_ticket_15.pdf
2025-09-18 13:01:23,154 - INFO - 

6_scale_ticket_15.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:23,154 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_15.json
2025-09-18 13:01:23,308 - INFO - Page 1: Extracted 702 characters, 33 lines from 20259ef4_6_scale_ticket_3_e7077bf7_page_001.pdf
2025-09-18 13:01:23,309 - INFO - Successfully processed page 1
2025-09-18 13:01:23,309 - INFO - Combined 1 pages into final text
2025-09-18 13:01:23,309 - INFO - Text validation for 20259ef4_6_scale_ticket_3: 719 characters, 1 pages
2025-09-18 13:01:23,310 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:23,310 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:23,439 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba99c498_6_scale_ticket_15.pdf
2025-09-18 13:01:23,772 - INFO - Page 1: Extracted 1041 characters, 72 lines from 21b42ca0_6_scale_ticket_2_f4f3d86d_page_001.pdf
2025-09-18 13:01:23,772 - INFO - Successfully processed page 1
2025-09-18 13:01:23,786 - INFO - Page 1: Extracted 2006 characters, 95 lines from 9870dc58_6_scale_ticket_16_710b357e_page_001.pdf
2025-09-18 13:01:23,787 - INFO - Combined 1 pages into final text
2025-09-18 13:01:23,787 - INFO - Successfully processed page 1
2025-09-18 13:01:23,787 - INFO - Text validation for 21b42ca0_6_scale_ticket_2: 1058 characters, 1 pages
2025-09-18 13:01:23,787 - INFO - Combined 1 pages into final text
2025-09-18 13:01:23,787 - INFO - Text validation for 9870dc58_6_scale_ticket_16: 2023 characters, 1 pages
2025-09-18 13:01:23,788 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:23,788 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:23,788 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:23,788 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:24,796 - INFO - Page 1: Extracted 679 characters, 29 lines from 232dc264_6_scale_ticket_5_394ffe5d_page_001.pdf
2025-09-18 13:01:24,855 - INFO - Successfully processed page 1
2025-09-18 13:01:24,857 - INFO - Page 1: Extracted 421 characters, 38 lines from 8201ca49_6_scale_ticket_4_d8305c35_page_001.pdf
2025-09-18 13:01:24,857 - INFO - Combined 1 pages into final text
2025-09-18 13:01:24,857 - INFO - Text validation for 232dc264_6_scale_ticket_5: 696 characters, 1 pages
2025-09-18 13:01:24,857 - INFO - Successfully processed page 1
2025-09-18 13:01:24,858 - INFO - Combined 1 pages into final text
2025-09-18 13:01:24,858 - INFO - Text validation for 8201ca49_6_scale_ticket_4: 438 characters, 1 pages
2025-09-18 13:01:24,858 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:24,858 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:24,861 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:24,861 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:25,517 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/20259ef4_6_scale_ticket_3.pdf
2025-09-18 13:01:25,543 - INFO - 

6_scale_ticket_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:25,543 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_3.json
2025-09-18 13:01:25,707 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/21b42ca0_6_scale_ticket_2.pdf
2025-09-18 13:01:25,838 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/20259ef4_6_scale_ticket_3.pdf
2025-09-18 13:01:25,858 - INFO - 

6_scale_ticket_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 13:01:25,859 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_2.json
2025-09-18 13:01:26,152 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/21b42ca0_6_scale_ticket_2.pdf
2025-09-18 13:01:26,283 - INFO - Page 1: Extracted 1020 characters, 58 lines from d5b573b8_6_scale_ticket_7_58a32cc4_page_001.pdf
2025-09-18 13:01:26,284 - INFO - Successfully processed page 1
2025-09-18 13:01:26,285 - INFO - Combined 1 pages into final text
2025-09-18 13:01:26,285 - INFO - Text validation for d5b573b8_6_scale_ticket_7: 1037 characters, 1 pages
2025-09-18 13:01:26,285 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:26,285 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:26,361 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8201ca49_6_scale_ticket_4.pdf
2025-09-18 13:01:26,363 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/232dc264_6_scale_ticket_5.pdf
2025-09-18 13:01:26,378 - INFO - Page 1: Extracted 473 characters, 36 lines from 5ce5d512_6_scale_ticket_6_856277e8_page_001.pdf
2025-09-18 13:01:26,382 - INFO - Successfully processed page 1
2025-09-18 13:01:26,383 - INFO - Combined 1 pages into final text
2025-09-18 13:01:26,385 - INFO - Text validation for 5ce5d512_6_scale_ticket_6: 490 characters, 1 pages
2025-09-18 13:01:26,402 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:26,403 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:26,411 - INFO - 

6_scale_ticket_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:26,411 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_4.json
2025-09-18 13:01:26,707 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8201ca49_6_scale_ticket_4.pdf
2025-09-18 13:01:26,717 - INFO - 

6_scale_ticket_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:26,717 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_5.json
2025-09-18 13:01:27,009 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/232dc264_6_scale_ticket_5.pdf
2025-09-18 13:01:27,116 - INFO - Page 1: Extracted 622 characters, 27 lines from b48ada57_6_scale_ticket_8_5610c40a_page_001.pdf
2025-09-18 13:01:27,116 - INFO - Successfully processed page 1
2025-09-18 13:01:27,116 - INFO - Combined 1 pages into final text
2025-09-18 13:01:27,116 - INFO - Text validation for b48ada57_6_scale_ticket_8: 639 characters, 1 pages
2025-09-18 13:01:27,117 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:27,117 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:27,134 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9870dc58_6_scale_ticket_16.pdf
2025-09-18 13:01:27,166 - INFO - 

6_scale_ticket_16.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 13:01:27,166 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_16.json
2025-09-18 13:01:27,460 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9870dc58_6_scale_ticket_16.pdf
2025-09-18 13:01:27,771 - INFO - Page 1: Extracted 731 characters, 33 lines from 131f797f_6_scale_ticket_9_5dd93520_page_001.pdf
2025-09-18 13:01:27,771 - INFO - Successfully processed page 1
2025-09-18 13:01:27,772 - INFO - Combined 1 pages into final text
2025-09-18 13:01:27,772 - INFO - Text validation for 131f797f_6_scale_ticket_9: 748 characters, 1 pages
2025-09-18 13:01:27,773 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:01:27,773 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:01:27,898 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ce5d512_6_scale_ticket_6.pdf
2025-09-18 13:01:27,909 - INFO - 

6_scale_ticket_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:27,909 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_6.json
2025-09-18 13:01:27,912 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d5b573b8_6_scale_ticket_7.pdf
2025-09-18 13:01:28,192 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ce5d512_6_scale_ticket_6.pdf
2025-09-18 13:01:28,220 - INFO - 

6_scale_ticket_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:28,220 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_7.json
2025-09-18 13:01:28,501 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d5b573b8_6_scale_ticket_7.pdf
2025-09-18 13:01:29,035 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b48ada57_6_scale_ticket_8.pdf
2025-09-18 13:01:29,053 - INFO - 

6_scale_ticket_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:29,053 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_8.json
2025-09-18 13:01:29,337 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b48ada57_6_scale_ticket_8.pdf
2025-09-18 13:01:29,683 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/131f797f_6_scale_ticket_9.pdf
2025-09-18 13:01:29,700 - INFO - 

6_scale_ticket_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:01:29,700 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_9.json
2025-09-18 13:01:29,985 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/131f797f_6_scale_ticket_9.pdf
2025-09-18 13:01:29,986 - INFO - 
📊 Processing Summary:
2025-09-18 13:01:29,986 - INFO -    Total files: 16
2025-09-18 13:01:29,986 - INFO -    Successful: 16
2025-09-18 13:01:29,986 - INFO -    Failed: 0
2025-09-18 13:01:29,986 - INFO -    Duration: 28.45 seconds
2025-09-18 13:01:29,986 - INFO -    Output directory: output
2025-09-18 13:01:29,986 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:01:29,986 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,986 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,986 - INFO -    📄 6_scale_ticket_11.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_12.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_13.jpg: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_14.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_15.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_16.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_2.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_3.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_4.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_5.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_6.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_7.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,987 - INFO -    📄 6_scale_ticket_8.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,988 - INFO -    📄 6_scale_ticket_9.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:01:29,988 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 28.445746}
