{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9995244741439819, "Height": 0.9997826218605042, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.9995006322860718, "Y": 0.9997826218605042}, {"X": 0.0, "Y": 0.9997739791870117}, {"X": 0.0, "Y": 0.0}, {"X": 0.9995244741439819, "Y": 0.0}]}, "Id": "f958dc06-790b-4552-8293-fe9093b5e350", "Relationships": [{"Type": "CHILD", "Ids": ["24265668-93da-4242-8265-c36544ee3faf", "89c3f307-c786-4a02-88d7-52766d8a78b6", "e99a28be-57ee-435a-acb3-2323e8cf64f9", "c00af163-1721-4c28-af91-0cc290aa128c", "fa7abcb3-a3f5-48ce-ad47-02b5929fe036", "83eadc24-35b9-41cc-9252-0493667268d9", "5581288b-42de-43c4-a63d-1bad822c539b", "314b917d-b695-4871-8a7c-bd8d591a2667", "db1eafe9-ff10-4357-a77f-1391e008d9ac", "7fcd479f-df42-4219-b074-a1153c02aeb2", "74e2eedc-9127-4902-9261-b15871449244", "743a1fe1-4a9b-401d-968e-91df8562d668", "bf8f63fe-c06d-4e4e-ba83-9f501cb09aa4", "3afcefad-530b-4420-a909-82928e887c85", "2aa57900-4587-48c1-9171-1d6eb1348855", "7c66e3dd-6035-430e-a93d-74cdedf9d223", "529f21e2-7962-4066-a85d-bcaa45bc12f4", "e761ebde-1aa8-45f5-987b-241b55d85e55", "02aa6345-50e3-48d4-a30d-11f4569fbedf", "6d78b970-8d74-4f7e-bf99-5ab8e19b329e", "1e9b791a-821e-4ca1-97e9-84780171761d"]}]}, {"BlockType": "LINE", "Confidence": 98.43975830078125, "Text": "TSL Ingate: OMA", "Geometry": {"BoundingBox": {"Width": 0.2538628876209259, "Height": 0.02345920540392399, "Left": 0.6866597533226013, "Top": 0.9347478747367859}, "Polygon": [{"X": 0.9405220746994019, "Y": 0.9582070708274841}, {"X": 0.6866597533226013, "Y": 0.9582050442695618}, {"X": 0.6866602301597595, "Y": 0.9347478747367859}, {"X": 0.9405226111412048, "Y": 0.9347498416900635}]}, "Id": "24265668-93da-4242-8265-c36544ee3faf", "Relationships": [{"Type": "CHILD", "Ids": ["c3a5ec90-4fe0-4f1f-9af2-9b938837512d", "141f3f18-d909-4e2b-9245-ed9130cfa0ab", "19193e31-5568-4b1d-991d-79632b9160fc"]}]}, {"BlockType": "LINE", "Confidence": 94.05007934570312, "Text": "ActivityDate: 09/22/2025 13:52", "Geometry": {"BoundingBox": {"Width": 0.4654363691806793, "Height": 0.02271684817969799, "Left": 0.47433874011039734, "Top": 0.9128642678260803}, "Polygon": [{"X": 0.9397745728492737, "Y": 0.9355811476707458}, {"X": 0.47433874011039734, "Y": 0.9355775117874146}, {"X": 0.47433918714523315, "Y": 0.9128642678260803}, {"X": 0.9397751092910767, "Y": 0.9128677845001221}]}, "Id": "89c3f307-c786-4a02-88d7-52766d8a78b6", "Relationships": [{"Type": "CHILD", "Ids": ["5d5d55de-2123-49b3-8362-be98dac0df0f", "feb0f398-1493-425f-b65d-3167bced6f68", "4cf97c37-fa64-40d0-98f5-ad5ba0ae8215"]}]}, {"BlockType": "LINE", "Confidence": 98.56505584716797, "Text": "Inspector", "Geometry": {"BoundingBox": {"Width": 0.14187759160995483, "Height": 0.021482091397047043, "Left": 0.7965691089630127, "Top": 0.8904747366905212}, "Polygon": [{"X": 0.9384462237358093, "Y": 0.9119568467140198}, {"X": 0.7965691089630127, "Y": 0.9119557738304138}, {"X": 0.7965696454048157, "Y": 0.8904747366905212}, {"X": 0.9384467005729675, "Y": 0.8904757499694824}]}, "Id": "e99a28be-57ee-435a-acb3-2323e8cf64f9", "Relationships": [{"Type": "CHILD", "Ids": ["a8ce3068-be30-4dfe-9592-41d374815c23"]}]}, {"BlockType": "LINE", "Confidence": 99.94813537597656, "Text": "Carrier CALHOUN TRUCK LINES", "Geometry": {"BoundingBox": {"Width": 0.46992093324661255, "Height": 0.018299691379070282, "Left": 0.46882161498069763, "Top": 0.8716696500778198}, "Polygon": [{"X": 0.9387421607971191, "Y": 0.8899693489074707}, {"X": 0.46882161498069763, "Y": 0.8899659514427185}, {"X": 0.46882200241088867, "Y": 0.8716696500778198}, {"X": 0.9387425780296326, "Y": 0.8716728687286377}]}, "Id": "c00af163-1721-4c28-af91-0cc290aa128c", "Relationships": [{"Type": "CHILD", "Ids": ["2c51a11c-cf3e-4d95-b80b-59bb89b3ae72", "13530b32-94e9-42aa-8e26-7f7c3998c127", "47d11ab4-3e08-45e2-aa3f-75cd7826bdad", "b50ba07b-33e0-471b-ba7b-c78ec9a3e451"]}]}, {"BlockType": "LINE", "Confidence": 98.51642608642578, "Text": "Trucker: Unknown Unit: 2155", "Geometry": {"BoundingBox": {"Width": 0.43314823508262634, "Height": 0.019365129992365837, "Left": 0.5066865086555481, "Top": 0.848362922668457}, "Polygon": [{"X": 0.9398342967033386, "Y": 0.867728054523468}, {"X": 0.5066865086555481, "Y": 0.8677250742912292}, {"X": 0.5066869258880615, "Y": 0.848362922668457}, {"X": 0.939834713935852, "Y": 0.8483657836914062}]}, "Id": "fa7abcb3-a3f5-48ce-ad47-02b5929fe036", "Relationships": [{"Type": "CHILD", "Ids": ["7b94ade8-027b-4a2d-8c8a-4f277bc23ea3", "98e7482f-7c56-4eba-a4e7-806171d43b6a", "73f21916-f7f2-41cb-ae4f-e3336cdc4d8f", "cf6bbe20-1617-4606-a73c-f3025441eb47"]}]}, {"BlockType": "LINE", "Confidence": 86.75515747070312, "Text": "Hamu346538-8 40HC Owner:HAPAGLLOYDAMERICAING", "Geometry": {"BoundingBox": {"Width": 0.8399141430854797, "Height": 0.02403074875473976, "Left": 0.09747150540351868, "Top": 0.8269760012626648}, "Polygon": [{"X": 0.937385082244873, "Y": 0.8510067462921143}, {"X": 0.09747150540351868, "Y": 0.8510012030601501}, {"X": 0.0974719449877739, "Y": 0.8269760012626648}, {"X": 0.937385618686676, "Y": 0.8269813060760498}]}, "Id": "83eadc24-35b9-41cc-9252-0493667268d9", "Relationships": [{"Type": "CHILD", "Ids": ["2f69a027-8377-4b3b-bf0f-b1dae949789b", "146daf3d-e570-4380-99d1-e497777661f7", "4f91a5b9-8fcc-4cf3-a561-5c90d17c540e"]}]}, {"BlockType": "LINE", "Confidence": 96.5658187866211, "Text": "Loaded:No Hazardous:No Lifted:Yes", "Geometry": {"BoundingBox": {"Width": 0.5309292674064636, "Height": 0.01856638491153717, "Left": 0.4060421586036682, "Top": 0.8037989139556885}, "Polygon": [{"X": 0.9369710087776184, "Y": 0.8223652839660645}, {"X": 0.4060421586036682, "Y": 0.822361946105957}, {"X": 0.40604254603385925, "Y": 0.8037989139556885}, {"X": 0.9369714260101318, "Y": 0.8038020730018616}]}, "Id": "5581288b-42de-43c4-a63d-1bad822c539b", "Relationships": [{"Type": "CHILD", "Ids": ["4b44e85d-753c-4f89-979b-c94423382fae", "3f1e963a-a37c-483e-b67b-8f85aa4c7775", "8849b8ac-fa6f-4658-8272-a557f73b0d67"]}]}, {"BlockType": "LINE", "Confidence": 96.16230773925781, "Text": "Grade:NF+", "Geometry": {"BoundingBox": {"Width": 0.16021563112735748, "Height": 0.017554379999637604, "Left": 0.7763144373893738, "Top": 0.7794970870018005}, "Polygon": [{"X": 0.9365296959877014, "Y": 0.7970514297485352}, {"X": 0.7763144373893738, "Y": 0.7970504760742188}, {"X": 0.7763148546218872, "Y": 0.7794970870018005}, {"X": 0.9365300536155701, "Y": 0.7794979810714722}]}, "Id": "314b917d-b695-4871-8a7c-bd8d591a2667", "Relationships": [{"Type": "CHILD", "Ids": ["7d2c8f88-18ff-4101-9a80-b9393bc45359"]}]}, {"BlockType": "LINE", "Confidence": 86.18124389648438, "Text": "Seal: Genset:", "Geometry": {"BoundingBox": {"Width": 0.18834532797336578, "Height": 0.016377698630094528, "Left": 0.7478623986244202, "Top": 0.7567098140716553}, "Polygon": [{"X": 0.9362073540687561, "Y": 0.7730875015258789}, {"X": 0.7478623986244202, "Y": 0.7730864882469177}, {"X": 0.7478628158569336, "Y": 0.7567098140716553}, {"X": 0.9362077713012695, "Y": 0.7567108273506165}]}, "Id": "db1eafe9-ff10-4357-a77f-1391e008d9ac", "Relationships": [{"Type": "CHILD", "Ids": ["748732e0-553b-4c08-b076-21f631a0434e", "2ddda4e4-8176-4d20-968d-8b8010697d22"]}]}, {"BlockType": "LINE", "Confidence": 88.6272964477539, "Text": "Tjpz437956 40CH Owner:TRACINTERMODAL", "Geometry": {"BoundingBox": {"Width": 0.6595856547355652, "Height": 0.023809485137462616, "Left": 0.2782955467700958, "Top": 0.730380654335022}, "Polygon": [{"X": 0.9378806948661804, "Y": 0.7541901469230652}, {"X": 0.2782955467700958, "Y": 0.7541865706443787}, {"X": 0.27829602360725403, "Y": 0.730380654335022}, {"X": 0.9378812313079834, "Y": 0.7303839325904846}]}, "Id": "7fcd479f-df42-4219-b074-a1153c02aeb2", "Relationships": [{"Type": "CHILD", "Ids": ["6b541c8d-1351-4454-bf7b-154f3d634215", "3663a15a-030a-4461-8293-34c3b8b22ef1", "312ad9ad-ea16-446d-9636-cab942849ede"]}]}, {"BlockType": "LINE", "Confidence": 96.63265228271484, "Text": "Genset: Hubmeter:", "Geometry": {"BoundingBox": {"Width": 0.2783016264438629, "Height": 0.017121588811278343, "Left": 0.6570624113082886, "Top": 0.7108649015426636}, "Polygon": [{"X": 0.9353636503219604, "Y": 0.7279865145683289}, {"X": 0.6570624113082886, "Y": 0.7279850840568542}, {"X": 0.657062828540802, "Y": 0.7108649015426636}, {"X": 0.9353640675544739, "Y": 0.7108662128448486}]}, "Id": "74e2eedc-9127-4902-9261-b15871449244", "Relationships": [{"Type": "CHILD", "Ids": ["a25c8b1c-47a8-4894-a05d-c59f3edbeb84", "7b11a637-9bb2-44b2-96bf-2b0eb377fd10"]}]}, {"BlockType": "LINE", "Confidence": 97.15950012207031, "Text": "I hereby certify that on the date stated above. the person who conducted the", "Geometry": {"BoundingBox": {"Width": 0.8857678771018982, "Height": 0.02202637493610382, "Left": 0.04975232854485512, "Top": 0.6665007472038269}, "Polygon": [{"X": 0.9355196952819824, "Y": 0.6885271072387695}, {"X": 0.04975232854485512, "Y": 0.6885231137275696}, {"X": 0.049752723425626755, "Y": 0.6665007472038269}, {"X": 0.9355201721191406, "Y": 0.666504442691803}]}, "Id": "743a1fe1-4a9b-401d-968e-91df8562d668", "Relationships": [{"Type": "CHILD", "Ids": ["29a6257b-cce7-47d1-89b9-1ce6e9d51ee8", "61d51cf6-135c-427d-a1b9-e255c79d9037", "2502c8cf-1009-4d13-8a2a-02993569a795", "da069931-75dd-432c-a49f-ebb2d64b60c9", "d5049404-bd9a-4272-aaa6-3ef818f49680", "9a3e93aa-e015-4b2c-8e91-e17fe1d1867d", "07d4f85d-a363-4c16-97c5-b1048543ad2e", "362e1602-38a1-4208-8ba2-3edb1d523c19", "88ccd233-e4cc-457c-9270-0ec52f9fc8d2", "7ec27a0d-6513-41ad-a8ef-576d12460c76", "871ef429-e36c-4b3d-92d4-fd84d229671c", "f89dc6f0-ffcf-40ca-b065-2942344dded8", "182e5e5f-af9e-4b3e-9f64-8cafe2b1759a", "c3cdfa38-fdc0-46a3-8b6f-4c8ac9ba9fd6"]}]}, {"BlockType": "LINE", "Confidence": 98.70958709716797, "Text": "inspection covered by this report was competent and qualified to do so and", "Geometry": {"BoundingBox": {"Width": 0.8696082830429077, "Height": 0.022542888298630714, "Left": 0.06638168543577194, "Top": 0.6481720209121704}, "Polygon": [{"X": 0.9359894394874573, "Y": 0.6707149147987366}, {"X": 0.06638168543577194, "Y": 0.6707112193107605}, {"X": 0.06638209521770477, "Y": 0.6481720209121704}, {"X": 0.9359899759292603, "Y": 0.6481754183769226}]}, "Id": "bf8f63fe-c06d-4e4e-ba83-9f501cb09aa4", "Relationships": [{"Type": "CHILD", "Ids": ["79f84e1b-ace3-4067-b740-5ae03f043313", "9320c2d0-bf00-4ea9-b551-01e37a285878", "2e236e6b-0842-430e-b8e8-3cd5a185c77c", "9710e717-7578-424d-b234-abd55e824538", "e0587be7-0715-45f5-8399-0c2e5ed33c00", "76073798-8e1c-4854-a861-dc0b2b90f6d9", "687ee250-bdc5-40c4-b89f-eaeaf5df9351", "646b6e9a-c697-4fd1-8d78-2d3ea11709b6", "6c61ccc9-f870-4da5-ac6a-3acc77fac275", "93474b7b-4e22-483b-b3a9-fad407c59655", "0fb811bc-e116-4c05-adeb-574d55a14153", "fb9d6c18-4a98-4923-be57-0eb5d4a29a1c", "79ef2821-c0ca-4cb8-8063-d1cb8e156281"]}]}, {"BlockType": "LINE", "Confidence": 98.32080841064453, "Text": "was duly authorized to inspect and take possession of the equipment as a", "Geometry": {"BoundingBox": {"Width": 0.8433710336685181, "Height": 0.019678940996527672, "Left": 0.0928114801645279, "Top": 0.6309359669685364}, "Polygon": [{"X": 0.9361820816993713, "Y": 0.650614857673645}, {"X": 0.0928114801645279, "Y": 0.6506115198135376}, {"X": 0.09281184524297714, "Y": 0.6309359669685364}, {"X": 0.9361824989318848, "Y": 0.6309390664100647}]}, "Id": "3afcefad-530b-4420-a909-82928e887c85", "Relationships": [{"Type": "CHILD", "Ids": ["06fba52a-d80e-43a4-b253-69df5f300db4", "63001ddf-7a51-450a-9039-9a144e403bcf", "f233ed86-a4b8-4754-8f0b-cd7dd912d121", "8e808e4b-f7be-4dfb-872d-519ab033d3e8", "17a7659d-671e-42fe-9064-b04a952ecfe1", "8cb3fe12-fafc-423b-a252-11ae30434040", "6dd190eb-b29a-4dea-85f1-e04e92a93e12", "643346b7-8271-4c5b-85c2-f98337c4c7cf", "1d379206-5430-4518-9713-cd0d966630d5", "1bda4982-3171-4b54-b974-30bcb26f3ace", "ec333289-ce68-4036-a1b2-f6c22930537e", "cc916e49-bb05-4be2-bc77-7ff6234b9bbd", "f469fc58-1f38-483d-9f9c-11dcb2b682a4"]}]}, {"BlockType": "LINE", "Confidence": 99.71039581298828, "Text": "representative of the acquiring carrier. In consideration of the acceptance", "Geometry": {"BoundingBox": {"Width": 0.8407132029533386, "Height": 0.019886288791894913, "Left": 0.09503442794084549, "Top": 0.6125805377960205}, "Polygon": [{"X": 0.9357472062110901, "Y": 0.6324667930603027}, {"X": 0.09503442794084549, "Y": 0.6324636936187744}, {"X": 0.09503479301929474, "Y": 0.6125805377960205}, {"X": 0.9357476234436035, "Y": 0.6125833988189697}]}, "Id": "2aa57900-4587-48c1-9171-1d6eb1348855", "Relationships": [{"Type": "CHILD", "Ids": ["cc1da227-7ca6-4272-90c1-b3cdbd2297de", "64c156fc-f826-45a8-a03c-88328936b655", "d919906d-8732-4d58-a856-9fdb07cb9663", "396a27a9-0182-4f47-89b4-96ce2b892f96", "5d7845c8-3101-4161-bd83-74e01ec7d541", "007d6b37-eafb-49b2-9919-4fed53752950", "279dd4f3-09b1-4076-bb24-ac38a1d5620a", "3be42a00-33ea-41a8-9b18-4af4cbea44ba", "1a127c27-3db1-4580-9e21-ed0faf69b25f", "d304eda9-c1b8-44e8-81ea-11510b6ef764"]}]}, {"BlockType": "LINE", "Confidence": 96.32868957519531, "Text": "of the above container for storage, the Carrier acknowledges that all", "Geometry": {"BoundingBox": {"Width": 0.7828317284584045, "Height": 0.01924147829413414, "Left": 0.15210826694965363, "Top": 0.5964457988739014}, "Polygon": [{"X": 0.9349395632743835, "Y": 0.6156872510910034}, {"X": 0.15210826694965363, "Y": 0.6156845688819885}, {"X": 0.15210863947868347, "Y": 0.5964457988739014}, {"X": 0.934939980506897, "Y": 0.5964483022689819}]}, "Id": "7c66e3dd-6035-430e-a93d-74cdedf9d223", "Relationships": [{"Type": "CHILD", "Ids": ["eeb7347c-b193-4601-88b1-01342b78ca99", "9625236a-cec1-4de3-b03f-23981d72f582", "2417ffc5-a028-414a-91dd-bc9028c7da48", "5897a190-66cd-44e3-9d1d-709aad495813", "33af3a96-75d2-4f0d-b96a-5a24e2087b32", "5a953ad3-f572-423d-904c-62a44e2fd5c5", "f62f4569-0d89-4664-9199-e5ef043d7a31", "63d368ec-374f-4abf-af94-be581dc92358", "b8743f97-59eb-4b3d-9c87-9cac7adc3987", "ccefb7ae-d6cc-4ab1-bd5d-3811483f7e5f", "e79a1995-24f5-49ce-b1bd-8600be00f183"]}]}, {"BlockType": "LINE", "Confidence": 95.73694610595703, "Text": "freight charges have been paid or agrees to look solely to the consignor,", "Geometry": {"BoundingBox": {"Width": 0.8272303342819214, "Height": 0.020116977393627167, "Left": 0.10921679437160492, "Top": 0.5778477787971497}, "Polygon": [{"X": 0.9364466667175293, "Y": 0.5979647636413574}, {"X": 0.10921679437160492, "Y": 0.5979620814323425}, {"X": 0.10921716690063477, "Y": 0.5778477787971497}, {"X": 0.9364470839500427, "Y": 0.5778502821922302}]}, "Id": "529f21e2-7962-4066-a85d-bcaa45bc12f4", "Relationships": [{"Type": "CHILD", "Ids": ["25ede507-bb4d-46db-a742-fe6c655bd191", "fd721a0e-da40-433a-b00e-a12bbb4a5e80", "271ff83d-c110-4f27-b383-93fff8502400", "7afafe01-1950-42ed-b0e9-f6140a3858ed", "74667b70-9d7f-4756-ad5a-aa12a50a109e", "3846d091-aac1-4a68-a81d-52cd28c36461", "fa188e52-09a4-4ebb-9ff6-541bd2e6a843", "ef95a11b-127a-4ce7-9ffc-0025aa3f5480", "9f401109-6282-4e42-b857-e2e6faa1df7c", "dd7e2182-3b5a-463b-b9a6-d9db10d9b362", "165d7e3c-7553-41ef-9f80-adf1b834c10e", "7eda8f4f-14ce-4113-993e-72ef7bf4f548", "385ac665-fd56-4d28-a85e-3780282bd030"]}]}, {"BlockType": "LINE", "Confidence": 93.01573944091797, "Text": "broker. or the beneficial owner of the freight for payment. The Carrier", "Geometry": {"BoundingBox": {"Width": 0.8010479211807251, "Height": 0.018235009163618088, "Left": 0.1357114017009735, "Top": 0.5620555877685547}, "Polygon": [{"X": 0.9367589354515076, "Y": 0.5802906155586243}, {"X": 0.1357114017009735, "Y": 0.5802882313728333}, {"X": 0.13571174442768097, "Y": 0.5620555877685547}, {"X": 0.936759352684021, "Y": 0.5620578527450562}]}, "Id": "e761ebde-1aa8-45f5-987b-241b55d85e55", "Relationships": [{"Type": "CHILD", "Ids": ["1936c2bd-279a-4061-bdb8-c1d65e0ee026", "18b5ddc4-bdc3-42d3-a2e7-fe68246d98e1", "0568a036-a45d-495d-a4c1-f9bdfda7dba2", "01fbddc3-4ae8-4f7a-8541-b476a103c5f3", "ee5571d1-0aa7-4e9e-9aa3-578d3d471609", "b3973f65-b71b-436e-ab71-fc1b5539e480", "9419e8b7-58b6-4a57-b9b5-eeb11e09ac6d", "ae5ace57-be8c-4f83-a0a5-ad6a311fcf27", "5579d821-0c77-4828-b427-510eb28ddf9d", "3c76c1ab-95fd-404c-b787-9fa3b478db74", "42d45b08-99a7-4aef-9d76-648a27ce1ae1", "92f36211-e72c-429b-9d11-71aabef4cda4"]}]}, {"BlockType": "LINE", "Confidence": 96.55430603027344, "Text": "waives any right it may have to pursue TSL for payment.", "Geometry": {"BoundingBox": {"Width": 0.6429208517074585, "Height": 0.01846613548696041, "Left": 0.29400041699409485, "Top": 0.54244464635849}, "Polygon": [{"X": 0.9369208812713623, "Y": 0.5609107613563538}, {"X": 0.29400041699409485, "Y": 0.5609089732170105}, {"X": 0.2940007746219635, "Y": 0.54244464635849}, {"X": 0.9369212985038757, "Y": 0.5424462556838989}]}, "Id": "02aa6345-50e3-48d4-a30d-11f4569fbedf", "Relationships": [{"Type": "CHILD", "Ids": ["3a841e7c-df06-4426-9c9f-cc3f0fd92a5e", "f97f0e27-c37d-44ff-83d5-602715e1fbc5", "fed27130-968c-4fa0-9077-8b6a44df5dea", "a55d1f4e-9145-4ef2-968f-bc3dbe2c869b", "e289dcde-f85c-4b36-9a03-3b5527107cf1", "79722a52-2922-464e-b00a-a681855d8d36", "65351ff1-984f-459d-b49e-27e18b979e12", "98d73121-fd8a-4cce-b20b-d0b2e289f39e", "938c143d-08db-4cb3-9ef6-6dd954982aaf", "f473e791-e435-437b-8665-cc157b69008f", "18ee84ca-3cd2-401d-8d29-f38ab95b8054"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Sign here", "Geometry": {"BoundingBox": {"Width": 0.14022423326969147, "Height": 0.021029125899076462, "Left": 0.7959294319152832, "Top": 0.5201506018638611}, "Polygon": [{"X": 0.9361531734466553, "Y": 0.5411797165870667}, {"X": 0.7959294319152832, "Y": 0.5411794185638428}, {"X": 0.7959299087524414, "Y": 0.5201506018638611}, {"X": 0.9361536502838135, "Y": 0.520150899887085}]}, "Id": "6d78b970-8d74-4f7e-bf99-5ab8e19b329e", "Relationships": [{"Type": "CHILD", "Ids": ["a33f8a53-cdc8-44af-bc72-e258ec668108", "273a7ea1-2ce6-4f1f-8672-2cabf8024a50"]}]}, {"BlockType": "LINE", "Confidence": 94.29049682617188, "Text": "027225-1", "Geometry": {"BoundingBox": {"Width": 0.48342815041542053, "Height": 0.07124191522598267, "Left": 0.22210495173931122, "Top": 0.3025270402431488}, "Polygon": [{"X": 0.7055315375328064, "Y": 0.37376895546913147}, {"X": 0.22210495173931122, "Y": 0.3737688362598419}, {"X": 0.22210632264614105, "Y": 0.30252739787101746}, {"X": 0.7055330872535706, "Y": 0.3025270402431488}]}, "Id": "1e9b791a-821e-4ca1-97e9-84780171761d", "Relationships": [{"Type": "CHILD", "Ids": ["7a927426-2379-405c-86cc-de0eaa8c5cbb"]}]}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "TSL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05703980475664139, "Height": 0.018366623669862747, "Left": 0.8834826946258545, "Top": 0.9398404955863953}, "Polygon": [{"X": 0.9405220746994019, "Y": 0.9582070708274841}, {"X": 0.8834826946258545, "Y": 0.9582066535949707}, {"X": 0.8834831118583679, "Y": 0.9398404955863953}, {"X": 0.9405224919319153, "Y": 0.9398409128189087}]}, "Id": "c3a5ec90-4fe0-4f1f-9af2-9b938837512d"}, {"BlockType": "WORD", "Confidence": 95.76590728759766, "Text": "Ingate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1035832092165947, "Height": 0.021744605153799057, "Left": 0.7734624147415161, "Top": 0.9347485899925232}, "Polygon": [{"X": 0.8770450949668884, "Y": 0.9564931392669678}, {"X": 0.7734624147415161, "Y": 0.9564923048019409}, {"X": 0.7734628915786743, "Y": 0.9347485899925232}, {"X": 0.8770456314086914, "Y": 0.9347493648529053}]}, "Id": "141f3f18-d909-4e2b-9245-ed9130cfa0ab"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "OMA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07890383899211884, "Height": 0.01699042320251465, "Left": 0.6866597533226013, "Top": 0.940559983253479}, "Polygon": [{"X": 0.7655631899833679, "Y": 0.9575504064559937}, {"X": 0.6866597533226013, "Y": 0.9575498104095459}, {"X": 0.68666011095047, "Y": 0.940559983253479}, {"X": 0.7655636072158813, "Y": 0.9405606389045715}]}, "Id": "19193e31-5568-4b1d-991d-79632b9160fc"}, {"BlockType": "WORD", "Confidence": 84.01765441894531, "Text": "ActivityDate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19321247935295105, "Height": 0.021897315979003906, "Left": 0.7465626001358032, "Top": 0.9128662943840027}, "Polygon": [{"X": 0.9397745728492737, "Y": 0.9347636103630066}, {"X": 0.7465626001358032, "Y": 0.9347621202468872}, {"X": 0.7465630769729614, "Y": 0.9128662943840027}, {"X": 0.9397751092910767, "Y": 0.9128677845001221}]}, "Id": "5d5d55de-2123-49b3-8362-be98dac0df0f"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "09/22/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1733945906162262, "Height": 0.017972402274608612, "Left": 0.5648598670959473, "Top": 0.91713947057724}, "Polygon": [{"X": 0.7382540106773376, "Y": 0.9351118803024292}, {"X": 0.5648598670959473, "Y": 0.9351105093955994}, {"X": 0.5648602247238159, "Y": 0.91713947057724}, {"X": 0.7382544279098511, "Y": 0.917140781879425}]}, "Id": "feb0f398-1493-425f-b65d-3167bced6f68"}, {"BlockType": "WORD", "Confidence": 98.31971740722656, "Text": "13:52", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07930634170770645, "Height": 0.016836317256093025, "Left": 0.47433874011039734, "Top": 0.9187418222427368}, "Polygon": [{"X": 0.5536447167396545, "Y": 0.9355781078338623}, {"X": 0.47433874011039734, "Y": 0.9355775117874146}, {"X": 0.4743390679359436, "Y": 0.9187418222427368}, {"X": 0.5536450743675232, "Y": 0.9187424182891846}]}, "Id": "4cf97c37-fa64-40d0-98f5-ad5ba0ae8215"}, {"BlockType": "WORD", "Confidence": 98.56505584716797, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14187759160995483, "Height": 0.021482091397047043, "Left": 0.7965691089630127, "Top": 0.8904747366905212}, "Polygon": [{"X": 0.9384462237358093, "Y": 0.9119568467140198}, {"X": 0.7965691089630127, "Y": 0.9119557738304138}, {"X": 0.7965696454048157, "Y": 0.8904747366905212}, {"X": 0.9384467005729675, "Y": 0.8904757499694824}]}, "Id": "a8ce3068-be30-4dfe-9592-41d374815c23"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10436340421438217, "Height": 0.017631633207201958, "Left": 0.8343791961669922, "Top": 0.8716721534729004}, "Polygon": [{"X": 0.9387421607971191, "Y": 0.8893038034439087}, {"X": 0.8343791961669922, "Y": 0.8893030285835266}, {"X": 0.8343795537948608, "Y": 0.8716721534729004}, {"X": 0.9387425780296326, "Y": 0.8716728687286377}]}, "Id": "2c51a11c-cf3e-4d95-b80b-59bb89b3ae72"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "CALHOUN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15516453981399536, "Height": 0.01762840710580349, "Left": 0.6733017563819885, "Top": 0.8718726634979248}, "Polygon": [{"X": 0.8284658789634705, "Y": 0.8895010948181152}, {"X": 0.6733017563819885, "Y": 0.8894999623298645}, {"X": 0.673302173614502, "Y": 0.8718726634979248}, {"X": 0.8284662961959839, "Y": 0.8718737363815308}]}, "Id": "13530b32-94e9-42aa-8e26-7f7c3998c127"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TRUCK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10368018597364426, "Height": 0.016723792999982834, "Left": 0.5630146265029907, "Top": 0.8728236556053162}, "Polygon": [{"X": 0.6666944622993469, "Y": 0.8895474672317505}, {"X": 0.5630146265029907, "Y": 0.8895467519760132}, {"X": 0.5630149841308594, "Y": 0.8728236556053162}, {"X": 0.6666948199272156, "Y": 0.8728243708610535}]}, "Id": "47d11ab4-3e08-45e2-aa3f-75cd7826bdad"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "LINES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08643893152475357, "Height": 0.016638994216918945, "Left": 0.46882161498069763, "Top": 0.8733276128768921}, "Polygon": [{"X": 0.555260181427002, "Y": 0.889966607093811}, {"X": 0.46882161498069763, "Y": 0.8899659514427185}, {"X": 0.4688219726085663, "Y": 0.8733276128768921}, {"X": 0.5552605390548706, "Y": 0.8733282089233398}]}, "Id": "b50ba07b-33e0-471b-ba7b-c78ec9a3e451"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "Trucker:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12359357625246048, "Height": 0.01873793639242649, "Left": 0.8162411451339722, "Top": 0.8483649492263794}, "Polygon": [{"X": 0.9398342967033386, "Y": 0.8671029210090637}, {"X": 0.8162411451339722, "Y": 0.8671020865440369}, {"X": 0.8162415623664856, "Y": 0.8483649492263794}, {"X": 0.939834713935852, "Y": 0.8483657836914062}]}, "Id": "7b94ade8-027b-4a2d-8c8a-4f277bc23ea3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Unknown", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1423960030078888, "Height": 0.017764756456017494, "Left": 0.6667153835296631, "Top": 0.849657416343689}, "Polygon": [{"X": 0.8091109991073608, "Y": 0.8674221634864807}, {"X": 0.6667153835296631, "Y": 0.8674211502075195}, {"X": 0.6667158007621765, "Y": 0.849657416343689}, {"X": 0.8091114163398743, "Y": 0.8496583700180054}]}, "Id": "98e7482f-7c56-4eba-a4e7-806171d43b6a"}, {"BlockType": "WORD", "Confidence": 95.04843139648438, "Text": "Unit:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07192221283912659, "Height": 0.01815672591328621, "Left": 0.5864579677581787, "Top": 0.8495693802833557}, "Polygon": [{"X": 0.6583797931671143, "Y": 0.8677261471748352}, {"X": 0.5864579677581787, "Y": 0.8677256107330322}, {"X": 0.5864583253860474, "Y": 0.8495693802833557}, {"X": 0.6583801507949829, "Y": 0.8495698571205139}]}, "Id": "73f21916-f7f2-41cb-ae4f-e3336cdc4d8f"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "2155", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07231455296278, "Height": 0.017939751967787743, "Left": 0.5066865086555481, "Top": 0.8495970964431763}, "Polygon": [{"X": 0.5790006518363953, "Y": 0.8675368428230286}, {"X": 0.5066865086555481, "Y": 0.8675363659858704}, {"X": 0.5066868662834167, "Y": 0.8495970964431763}, {"X": 0.5790010690689087, "Y": 0.8495975732803345}]}, "Id": "cf6bbe20-1617-4606-a73c-f3025441eb47"}, {"BlockType": "WORD", "Confidence": 95.55583953857422, "Text": "Hamu346538-8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22987553477287292, "Height": 0.01699822209775448, "Left": 0.7075101137161255, "Top": 0.8270887136459351}, "Polygon": [{"X": 0.9373852014541626, "Y": 0.8440869450569153}, {"X": 0.7075101137161255, "Y": 0.8440854549407959}, {"X": 0.7075104713439941, "Y": 0.8270887136459351}, {"X": 0.937385618686676, "Y": 0.8270902037620544}]}, "Id": "2f69a027-8377-4b3b-bf0f-b1dae949789b"}, {"BlockType": "WORD", "Confidence": 91.55133819580078, "Text": "40HC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08299927413463593, "Height": 0.016426650807261467, "Left": 0.617554783821106, "Top": 0.8277559876441956}, "Polygon": [{"X": 0.7005537152290344, "Y": 0.8441826105117798}, {"X": 0.617554783821106, "Y": 0.8441820740699768}, {"X": 0.6175551414489746, "Y": 0.8277559876441956}, {"X": 0.7005540728569031, "Y": 0.8277565240859985}]}, "Id": "146daf3d-e570-4380-99d1-e497777661f7"}, {"BlockType": "WORD", "Confidence": 73.15827941894531, "Text": "Owner:HAPAGLLOYDAMERICAING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.5135214924812317, "Height": 0.024028582498431206, "Left": 0.09747150540351868, "Top": 0.8269760012626648}, "Polygon": [{"X": 0.6109924912452698, "Y": 0.8510046005249023}, {"X": 0.09747150540351868, "Y": 0.8510012030601501}, {"X": 0.0974719449877739, "Y": 0.8269760012626648}, {"X": 0.6109930276870728, "Y": 0.8269792795181274}]}, "Id": "4f91a5b9-8fcc-4cf3-a561-5c90d17c540e"}, {"BlockType": "WORD", "Confidence": 97.36766815185547, "Text": "Loaded:No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1633642464876175, "Height": 0.016960853710770607, "Left": 0.7736071944236755, "Top": 0.8038011193275452}, "Polygon": [{"X": 0.9369710683822632, "Y": 0.8207619786262512}, {"X": 0.7736071944236755, "Y": 0.82076096534729}, {"X": 0.773607611656189, "Y": 0.8038011193275452}, {"X": 0.9369714260101318, "Y": 0.8038020730018616}]}, "Id": "4b44e85d-753c-4f89-979b-c94423382fae"}, {"BlockType": "WORD", "Confidence": 96.92024230957031, "Text": "Hazardous:No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2098713517189026, "Height": 0.016788380220532417, "Left": 0.5561129450798035, "Top": 0.8048514127731323}, "Polygon": [{"X": 0.7659839391708374, "Y": 0.8216398358345032}, {"X": 0.5561129450798035, "Y": 0.8216385245323181}, {"X": 0.5561133027076721, "Y": 0.8048514127731323}, {"X": 0.7659843564033508, "Y": 0.8048527240753174}]}, "Id": "3f1e963a-a37c-483e-b67b-8f85aa4c7775"}, {"BlockType": "WORD", "Confidence": 95.40956115722656, "Text": "Lifted:Yes", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.142216295003891, "Height": 0.017329933121800423, "Left": 0.4060421586036682, "Top": 0.8050329089164734}, "Polygon": [{"X": 0.5482580661773682, "Y": 0.8223628401756287}, {"X": 0.4060421586036682, "Y": 0.822361946105957}, {"X": 0.40604251623153687, "Y": 0.8050329089164734}, {"X": 0.5482584834098816, "Y": 0.805033802986145}]}, "Id": "8849b8ac-fa6f-4658-8272-a557f73b0d67"}, {"BlockType": "WORD", "Confidence": 96.16230773925781, "Text": "Grade:NF+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16021563112735748, "Height": 0.017554379999637604, "Left": 0.7763144373893738, "Top": 0.7794970870018005}, "Polygon": [{"X": 0.9365296959877014, "Y": 0.7970514297485352}, {"X": 0.7763144373893738, "Y": 0.7970504760742188}, {"X": 0.7763148546218872, "Y": 0.7794970870018005}, {"X": 0.9365300536155701, "Y": 0.7794979810714722}]}, "Id": "7d2c8f88-18ff-4101-9a80-b9393bc45359"}, {"BlockType": "WORD", "Confidence": 74.32457733154297, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07083792984485626, "Height": 0.01637706719338894, "Left": 0.8653697967529297, "Top": 0.7567104697227478}, "Polygon": [{"X": 0.9362073540687561, "Y": 0.7730875015258789}, {"X": 0.8653697967529297, "Y": 0.7730871438980103}, {"X": 0.8653702139854431, "Y": 0.7567104697227478}, {"X": 0.9362077713012695, "Y": 0.7567108273506165}]}, "Id": "748732e0-553b-4c08-b076-21f631a0434e"}, {"BlockType": "WORD", "Confidence": 98.03790283203125, "Text": "Genset:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11111871898174286, "Height": 0.015872010961174965, "Left": 0.7478624582290649, "Top": 0.7570263743400574}, "Polygon": [{"X": 0.8589807748794556, "Y": 0.7728983759880066}, {"X": 0.7478624582290649, "Y": 0.7728977203369141}, {"X": 0.7478627562522888, "Y": 0.7570263743400574}, {"X": 0.8589811325073242, "Y": 0.7570269703865051}]}, "Id": "2ddda4e4-8176-4d20-968d-8b8010697d22"}, {"BlockType": "WORD", "Confidence": 75.73281860351562, "Text": "Tjpz437956", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17328308522701263, "Height": 0.02073456533253193, "Left": 0.7645981907844543, "Top": 0.7303830981254578}, "Polygon": [{"X": 0.9378807544708252, "Y": 0.7511176466941833}, {"X": 0.7645981907844543, "Y": 0.7511167526245117}, {"X": 0.7645986080169678, "Y": 0.7303830981254578}, {"X": 0.9378812313079834, "Y": 0.7303839325904846}]}, "Id": "6b541c8d-1351-4454-bf7b-154f3d634215"}, {"BlockType": "WORD", "Confidence": 98.21208953857422, "Text": "40CH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08095340430736542, "Height": 0.016481084749102592, "Left": 0.6763802170753479, "Top": 0.7353507280349731}, "Polygon": [{"X": 0.7573332786560059, "Y": 0.7518318295478821}, {"X": 0.6763802170753479, "Y": 0.7518314123153687}, {"X": 0.6763805747032166, "Y": 0.7353507280349731}, {"X": 0.7573336362838745, "Y": 0.7353511452674866}]}, "Id": "3663a15a-030a-4461-8293-34c3b8b22ef1"}, {"BlockType": "WORD", "Confidence": 91.93698120117188, "Text": "Owner:TRACINTERMODAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3895885944366455, "Height": 0.019089797511696815, "Left": 0.2782955467700958, "Top": 0.7350988984107971}, "Polygon": [{"X": 0.6678837537765503, "Y": 0.7541886568069458}, {"X": 0.2782955467700958, "Y": 0.7541865706443787}, {"X": 0.27829593420028687, "Y": 0.7350988984107971}, {"X": 0.667884111404419, "Y": 0.7351008653640747}]}, "Id": "312ad9ad-ea16-446d-9636-cab942849ede"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Genset:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11079615354537964, "Height": 0.015854723751544952, "Left": 0.8245679140090942, "Top": 0.7108657360076904}, "Polygon": [{"X": 0.9353637099266052, "Y": 0.7267204523086548}, {"X": 0.8245679140090942, "Y": 0.726719856262207}, {"X": 0.8245682716369629, "Y": 0.7108657360076904}, {"X": 0.9353640675544739, "Y": 0.7108662128448486}]}, "Id": "a25c8b1c-47a8-4894-a05d-c59f3edbeb84"}, {"BlockType": "WORD", "Confidence": 93.53376007080078, "Text": "Hubmeter:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1585913747549057, "Height": 0.016362614929676056, "Left": 0.6570624113082886, "Top": 0.7116233110427856}, "Polygon": [{"X": 0.8156534433364868, "Y": 0.7279859185218811}, {"X": 0.6570624113082886, "Y": 0.7279850840568542}, {"X": 0.6570627689361572, "Y": 0.7116233110427856}, {"X": 0.8156538009643555, "Y": 0.711624026298523}]}, "Id": "7b11a637-9bb2-44b2-96bf-2b0eb377fd10"}, {"BlockType": "WORD", "Confidence": 70.73979187011719, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008814319036900997, "Height": 0.011996781453490257, "Left": 0.9267058372497559, "Top": 0.6690874099731445}, "Polygon": [{"X": 0.9355198740959167, "Y": 0.681084156036377}, {"X": 0.9267058372497559, "Y": 0.6810840964317322}, {"X": 0.926706075668335, "Y": 0.6690874099731445}, {"X": 0.9355201125144958, "Y": 0.6690874099731445}]}, "Id": "29a6257b-cce7-47d1-89b9-1ce6e9d51ee8"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "hereby", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08182520419359207, "Height": 0.0162164568901062, "Left": 0.8395267724990845, "Top": 0.6665040254592896}, "Polygon": [{"X": 0.9213515520095825, "Y": 0.6827204823493958}, {"X": 0.8395267724990845, "Y": 0.6827201247215271}, {"X": 0.8395271301269531, "Y": 0.6665040254592896}, {"X": 0.921351969242096, "Y": 0.6665043830871582}]}, "Id": "61d51cf6-135c-427d-a1b9-e255c79d9037"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0758746936917305, "Height": 0.016240257769823074, "Left": 0.7592744827270508, "Top": 0.6669001579284668}, "Polygon": [{"X": 0.835148811340332, "Y": 0.6831403970718384}, {"X": 0.7592744827270508, "Y": 0.6831400394439697}, {"X": 0.7592748999595642, "Y": 0.6669001579284668}, {"X": 0.8351492285728455, "Y": 0.6669004559516907}]}, "Id": "2502c8cf-1009-4d13-8a2a-02993569a795"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04891112074255943, "Height": 0.013035687617957592, "Left": 0.7058401107788086, "Top": 0.6704294085502625}, "Polygon": [{"X": 0.7547509074211121, "Y": 0.6834651231765747}, {"X": 0.7058401107788086, "Y": 0.6834648847579956}, {"X": 0.7058404088020325, "Y": 0.6704294085502625}, {"X": 0.7547512054443359, "Y": 0.6704296469688416}]}, "Id": "da069931-75dd-432c-a49f-ebb2d64b60c9"}, {"BlockType": "WORD", "Confidence": 96.6053466796875, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030847935006022453, "Height": 0.009355802088975906, "Left": 0.669827938079834, "Top": 0.6708292961120605}, "Polygon": [{"X": 0.7006756663322449, "Y": 0.680185079574585}, {"X": 0.669827938079834, "Y": 0.6801849603652954}, {"X": 0.6698281764984131, "Y": 0.6708292961120605}, {"X": 0.700675904750824, "Y": 0.6708294153213501}]}, "Id": "d5049404-bd9a-4272-aaa6-3ef818f49680"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039043620228767395, "Height": 0.013069596141576767, "Left": 0.6255338788032532, "Top": 0.6705931425094604}, "Polygon": [{"X": 0.6645771861076355, "Y": 0.6836627721786499}, {"X": 0.6255338788032532, "Y": 0.6836625933647156}, {"X": 0.6255341172218323, "Y": 0.6705931425094604}, {"X": 0.6645774841308594, "Y": 0.6705933213233948}]}, "Id": "9a3e93aa-e015-4b2c-8e91-e17fe1d1867d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051899541169404984, "Height": 0.013148020952939987, "Left": 0.5690036416053772, "Top": 0.6703469753265381}, "Polygon": [{"X": 0.6209028959274292, "Y": 0.6834949851036072}, {"X": 0.5690036416053772, "Y": 0.6834947466850281}, {"X": 0.5690039396286011, "Y": 0.6703469753265381}, {"X": 0.6209031939506531, "Y": 0.6703471541404724}]}, "Id": "07d4f85d-a363-4c16-97c5-b1048543ad2e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07117634266614914, "Height": 0.012632214464247227, "Left": 0.4933755099773407, "Top": 0.6704918742179871}, "Polygon": [{"X": 0.564551591873169, "Y": 0.6831240653991699}, {"X": 0.4933755099773407, "Y": 0.683123767375946}, {"X": 0.4933757781982422, "Y": 0.6704918742179871}, {"X": 0.564551830291748, "Y": 0.6704921722412109}]}, "Id": "362e1602-38a1-4208-8ba2-3edb1d523c19"}, {"BlockType": "WORD", "Confidence": 93.30357360839844, "Text": "above.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07571128010749817, "Height": 0.012933079153299332, "Left": 0.41144701838493347, "Top": 0.6707744598388672}, "Polygon": [{"X": 0.48715803027153015, "Y": 0.6837075352668762}, {"X": 0.41144701838493347, "Y": 0.6837072372436523}, {"X": 0.41144728660583496, "Y": 0.6707744598388672}, {"X": 0.48715829849243164, "Y": 0.6707748174667358}]}, "Id": "88ccd233-e4cc-457c-9270-0ec52f9fc8d2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03822805732488632, "Height": 0.013157982379198074, "Left": 0.36687493324279785, "Top": 0.671776294708252}, "Polygon": [{"X": 0.4051027297973633, "Y": 0.6849342584609985}, {"X": 0.36687493324279785, "Y": 0.684934139251709}, {"X": 0.36687520146369934, "Y": 0.671776294708252}, {"X": 0.40510299801826477, "Y": 0.6717764735221863}]}, "Id": "7ec27a0d-6513-41ad-a8ef-576d12460c76"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "person", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0807286947965622, "Height": 0.013084867969155312, "Left": 0.28028857707977295, "Top": 0.6687130331993103}, "Polygon": [{"X": 0.36101701855659485, "Y": 0.6817978620529175}, {"X": 0.28028857707977295, "Y": 0.6817975640296936}, {"X": 0.28028884530067444, "Y": 0.6687130331993103}, {"X": 0.36101728677749634, "Y": 0.6687133312225342}]}, "Id": "871ef429-e36c-4b3d-92d4-fd84d229671c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "who", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0520155243575573, "Height": 0.01290773693472147, "Left": 0.2239198237657547, "Top": 0.6731554269790649}, "Polygon": [{"X": 0.2759351134300232, "Y": 0.6860631704330444}, {"X": 0.2239198237657547, "Y": 0.6860629320144653}, {"X": 0.223920077085495, "Y": 0.6731554269790649}, {"X": 0.2759353518486023, "Y": 0.6731556057929993}]}, "Id": "f89dc6f0-ffcf-40ca-b065-2942344dded8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "conducted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1233370304107666, "Height": 0.01410763245075941, "Left": 0.09518305212259293, "Top": 0.6738957762718201}, "Polygon": [{"X": 0.21851980686187744, "Y": 0.6880033612251282}, {"X": 0.09518305212259293, "Y": 0.6880028247833252}, {"X": 0.09518331289291382, "Y": 0.6738957762718201}, {"X": 0.21852008998394012, "Y": 0.6738962531089783}]}, "Id": "182e5e5f-af9e-4b3e-9f64-8cafe2b1759a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03972327336668968, "Height": 0.013209840282797813, "Left": 0.04975232854485512, "Top": 0.6753134727478027}, "Polygon": [{"X": 0.0894753560423851, "Y": 0.6885232925415039}, {"X": 0.04975232854485512, "Y": 0.6885231137275696}, {"X": 0.04975256696343422, "Y": 0.6753134727478027}, {"X": 0.0894756019115448, "Y": 0.6753136515617371}]}, "Id": "c3cdfa38-fdc0-46a3-8b6f-4c8ac9ba9fd6"}, {"BlockType": "WORD", "Confidence": 97.23991394042969, "Text": "inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12014583498239517, "Height": 0.01672339253127575, "Left": 0.8158441185951233, "Top": 0.6481749415397644}, "Polygon": [{"X": 0.9359895586967468, "Y": 0.6648983359336853}, {"X": 0.8158441185951233, "Y": 0.6648978590965271}, {"X": 0.8158444762229919, "Y": 0.6481749415397644}, {"X": 0.9359899759292603, "Y": 0.6481754183769226}]}, "Id": "79f84e1b-ace3-4067-b740-5ae03f043313"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "covered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09132838994264603, "Height": 0.013347520492970943, "Left": 0.7190773487091064, "Top": 0.6524311304092407}, "Polygon": [{"X": 0.810405433177948, "Y": 0.6657786965370178}, {"X": 0.7190773487091064, "Y": 0.6657782793045044}, {"X": 0.7190776467323303, "Y": 0.6524311304092407}, {"X": 0.8104057312011719, "Y": 0.6524314880371094}]}, "Id": "9320c2d0-bf00-4ea9-b551-01e37a285878"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030311647802591324, "Height": 0.016264595091342926, "Left": 0.6830435395240784, "Top": 0.6497853398323059}, "Polygon": [{"X": 0.7133548259735107, "Y": 0.6660499572753906}, {"X": 0.6830435395240784, "Y": 0.6660497784614563}, {"X": 0.683043897151947, "Y": 0.6497853398323059}, {"X": 0.7133551836013794, "Y": 0.6497854590415955}]}, "Id": "2e236e6b-0842-430e-b8e8-3cd5a185c77c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043014172464609146, "Height": 0.01331603154540062, "Left": 0.6350190043449402, "Top": 0.6530148386955261}, "Polygon": [{"X": 0.6780329346656799, "Y": 0.666330873966217}, {"X": 0.6350190043449402, "Y": 0.6663306951522827}, {"X": 0.6350193023681641, "Y": 0.6530148386955261}, {"X": 0.678033173084259, "Y": 0.6530150175094604}]}, "Id": "9710e717-7578-424d-b234-abd55e824538"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "report", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07421494275331497, "Height": 0.014881202019751072, "Left": 0.5557875037193298, "Top": 0.6495000123977661}, "Polygon": [{"X": 0.6300021409988403, "Y": 0.664381206035614}, {"X": 0.5557875037193298, "Y": 0.6643809080123901}, {"X": 0.5557878017425537, "Y": 0.6495000123977661}, {"X": 0.6300024390220642, "Y": 0.64950031042099}]}, "Id": "e0587be7-0715-45f5-8399-0c2e5ed33c00"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "was", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04534550756216049, "Height": 0.009278658777475357, "Left": 0.5070168972015381, "Top": 0.6530516743659973}, "Polygon": [{"X": 0.5523622035980225, "Y": 0.6623303294181824}, {"X": 0.5070168972015381, "Y": 0.662330150604248}, {"X": 0.5070170760154724, "Y": 0.6530516743659973}, {"X": 0.5523623824119568, "Y": 0.6530518531799316}]}, "Id": "76073798-8e1c-4854-a861-dc0b2b90f6d9"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "competent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12501247227191925, "Height": 0.014581362716853619, "Left": 0.3770012855529785, "Top": 0.6502420902252197}, "Polygon": [{"X": 0.5020134449005127, "Y": 0.6648234128952026}, {"X": 0.3770012855529785, "Y": 0.6648228764533997}, {"X": 0.3770015835762024, "Y": 0.6502420902252197}, {"X": 0.5020137429237366, "Y": 0.6502425670623779}]}, "Id": "687ee250-bdc5-40c4-b89f-eaeaf5df9351"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044432561844587326, "Height": 0.013291035778820515, "Left": 0.32775384187698364, "Top": 0.6544351577758789}, "Polygon": [{"X": 0.3721861243247986, "Y": 0.6677262187004089}, {"X": 0.32775384187698364, "Y": 0.6677259802818298}, {"X": 0.32775411009788513, "Y": 0.6544351577758789}, {"X": 0.3721863925457001, "Y": 0.6544353365898132}]}, "Id": "646b6e9a-c697-4fd1-8d78-2d3ea11709b6"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "qualified", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10431022942066193, "Height": 0.017379900440573692, "Left": 0.21853110194206238, "Top": 0.6516414284706116}, "Polygon": [{"X": 0.32284098863601685, "Y": 0.6690213084220886}, {"X": 0.21853110194206238, "Y": 0.6690208911895752}, {"X": 0.21853142976760864, "Y": 0.6516414284706116}, {"X": 0.3228413164615631, "Y": 0.651641845703125}]}, "Id": "6c61ccc9-f870-4da5-ac6a-3acc77fac275"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025251151993870735, "Height": 0.01108974777162075, "Left": 0.18779301643371582, "Top": 0.6559709906578064}, "Polygon": [{"X": 0.2130439579486847, "Y": 0.6670607328414917}, {"X": 0.18779301643371582, "Y": 0.6670606136322021}, {"X": 0.18779322504997253, "Y": 0.6559709906578064}, {"X": 0.2130441665649414, "Y": 0.6559710502624512}]}, "Id": "93474b7b-4e22-483b-b3a9-fad407c59655"}, {"BlockType": "WORD", "Confidence": 90.12535858154297, "Text": "do", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030939551070332527, "Height": 0.012902338989078999, "Left": 0.15109191834926605, "Top": 0.6564329862594604}, "Polygon": [{"X": 0.18203121423721313, "Y": 0.6693353652954102}, {"X": 0.15109191834926605, "Y": 0.6693351864814758}, {"X": 0.15109215676784515, "Y": 0.6564329862594604}, {"X": 0.18203146755695343, "Y": 0.65643310546875}]}, "Id": "0fb811bc-e116-4c05-adeb-574d55a14153"}, {"BlockType": "WORD", "Confidence": 96.96328735351562, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027301259338855743, "Height": 0.009592792950570583, "Left": 0.11805021017789841, "Top": 0.6566728353500366}, "Polygon": [{"X": 0.14535129070281982, "Y": 0.666265606880188}, {"X": 0.11805021017789841, "Y": 0.6662654876708984}, {"X": 0.11805038899183273, "Y": 0.6566728353500366}, {"X": 0.14535146951675415, "Y": 0.6566729545593262}]}, "Id": "fb9d6c18-4a98-4923-be57-0eb5d4a29a1c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04613166302442551, "Height": 0.013278605416417122, "Left": 0.06638168543577194, "Top": 0.6574327945709229}, "Polygon": [{"X": 0.11251310259103775, "Y": 0.6707113981246948}, {"X": 0.06638168543577194, "Y": 0.6707112193107605}, {"X": 0.06638193130493164, "Y": 0.6574327945709229}, {"X": 0.11251334846019745, "Y": 0.657433032989502}]}, "Id": "79ef2821-c0ca-4cb8-8063-d1cb8e156281"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "was", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045302864164114, "Height": 0.00950069259852171, "Left": 0.8908795714378357, "Top": 0.6335400342941284}, "Polygon": [{"X": 0.9361822605133057, "Y": 0.6430407762527466}, {"X": 0.8908795714378357, "Y": 0.6430405974388123}, {"X": 0.8908798098564148, "Y": 0.6335400342941284}, {"X": 0.93618243932724, "Y": 0.6335402131080627}]}, "Id": "06fba52a-d80e-43a4-b253-69df5f300db4"}, {"BlockType": "WORD", "Confidence": 99.43219757080078, "Text": "duly", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051697418093681335, "Height": 0.015923621132969856, "Left": 0.8343832492828369, "Top": 0.630938708782196}, "Polygon": [{"X": 0.8860803246498108, "Y": 0.6468623280525208}, {"X": 0.8343832492828369, "Y": 0.6468620896339417}, {"X": 0.8343836069107056, "Y": 0.630938708782196}, {"X": 0.8860806822776794, "Y": 0.6309388875961304}]}, "Id": "63001ddf-7a51-450a-9039-9a144e403bcf"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "authorized", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12578974664211273, "Height": 0.013108380138874054, "Left": 0.7038488388061523, "Top": 0.6346272230148315}, "Polygon": [{"X": 0.8296383023262024, "Y": 0.647735595703125}, {"X": 0.7038488388061523, "Y": 0.6477351188659668}, {"X": 0.7038491368293762, "Y": 0.6346272230148315}, {"X": 0.8296386003494263, "Y": 0.6346276998519897}]}, "Id": "f233ed86-a4b8-4754-8f0b-cd7dd912d121"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02470286749303341, "Height": 0.010822887532413006, "Left": 0.6738065481185913, "Top": 0.6351969838142395}, "Polygon": [{"X": 0.698509156703949, "Y": 0.6460198760032654}, {"X": 0.6738065481185913, "Y": 0.6460197567939758}, {"X": 0.6738067865371704, "Y": 0.6351969838142395}, {"X": 0.6985093951225281, "Y": 0.635197103023529}]}, "Id": "8e808e4b-f7be-4dfb-872d-519ab033d3e8"}, {"BlockType": "WORD", "Confidence": 99.40210723876953, "Text": "inspect", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08358564972877502, "Height": 0.015604797750711441, "Left": 0.5851131081581116, "Top": 0.6323875784873962}, "Polygon": [{"X": 0.6686984300613403, "Y": 0.6479923725128174}, {"X": 0.5851131081581116, "Y": 0.6479920148849487}, {"X": 0.5851134657859802, "Y": 0.6323875784873962}, {"X": 0.668698787689209, "Y": 0.6323878765106201}]}, "Id": "17a7659d-671e-42fe-9064-b04a952ecfe1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043731361627578735, "Height": 0.01273875217884779, "Left": 0.5364611148834229, "Top": 0.6350412964820862}, "Polygon": [{"X": 0.5801921486854553, "Y": 0.6477800607681274}, {"X": 0.5364611148834229, "Y": 0.6477798819541931}, {"X": 0.536461353302002, "Y": 0.6350412964820862}, {"X": 0.5801924467086792, "Y": 0.6350414156913757}]}, "Id": "8cb3fe12-fafc-423b-a252-11ae30434040"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "take", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05048281326889992, "Height": 0.012365469709038734, "Left": 0.4813796877861023, "Top": 0.6354128122329712}, "Polygon": [{"X": 0.5318622589111328, "Y": 0.6477782726287842}, {"X": 0.4813796877861023, "Y": 0.6477780342102051}, {"X": 0.4813799560070038, "Y": 0.6354128122329712}, {"X": 0.5318624973297119, "Y": 0.6354129910469055}]}, "Id": "6dd190eb-b29a-4dea-85f1-e04e92a93e12"}, {"BlockType": "WORD", "Confidence": 80.86734771728516, "Text": "possession", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12395448237657547, "Height": 0.015979310497641563, "Left": 0.3518754839897156, "Top": 0.6328980922698975}, "Polygon": [{"X": 0.4758296310901642, "Y": 0.6488773822784424}, {"X": 0.3518754839897156, "Y": 0.6488769054412842}, {"X": 0.35187581181526184, "Y": 0.6328980922698975}, {"X": 0.47582995891571045, "Y": 0.6328985691070557}]}, "Id": "643346b7-8271-4c5b-85c2-f98337c4c7cf"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026707708835601807, "Height": 0.013216990977525711, "Left": 0.32017359137535095, "Top": 0.6369357109069824}, "Polygon": [{"X": 0.34688106179237366, "Y": 0.6501526832580566}, {"X": 0.32017359137535095, "Y": 0.6501525640487671}, {"X": 0.32017385959625244, "Y": 0.6369357109069824}, {"X": 0.34688130021095276, "Y": 0.636935830116272}]}, "Id": "1d379206-5430-4518-9713-cd0d966630d5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038888320326805115, "Height": 0.012811260297894478, "Left": 0.2773059904575348, "Top": 0.6369658708572388}, "Polygon": [{"X": 0.3161940574645996, "Y": 0.6497771143913269}, {"X": 0.2773059904575348, "Y": 0.6497769355773926}, {"X": 0.2773062586784363, "Y": 0.6369658708572388}, {"X": 0.3161943256855011, "Y": 0.6369659900665283}]}, "Id": "1bda4982-3171-4b54-b974-30bcb26f3ace"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12767113745212555, "Height": 0.016038747504353523, "Left": 0.14458967745304108, "Top": 0.6345735192298889}, "Polygon": [{"X": 0.27226051688194275, "Y": 0.6506122350692749}, {"X": 0.14458967745304108, "Y": 0.6506117582321167}, {"X": 0.14458997547626495, "Y": 0.6345735192298889}, {"X": 0.2722608149051666, "Y": 0.6345739960670471}]}, "Id": "ec333289-ce68-4036-a1b2-f6c22930537e"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026334773749113083, "Height": 0.0097315339371562, "Left": 0.1133427694439888, "Top": 0.638746440410614}, "Polygon": [{"X": 0.13967736065387726, "Y": 0.6484779715538025}, {"X": 0.1133427694439888, "Y": 0.6484778523445129}, {"X": 0.11334294825792313, "Y": 0.638746440410614}, {"X": 0.13967753946781158, "Y": 0.6387465000152588}]}, "Id": "cc916e49-bb05-4be2-bc77-7ff6234b9bbd"}, {"BlockType": "WORD", "Confidence": 99.29946899414062, "Text": "a", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015597485937178135, "Height": 0.009001892991364002, "Left": 0.09281152486801147, "Top": 0.6394659280776978}, "Polygon": [{"X": 0.1084088459610939, "Y": 0.6484678387641907}, {"X": 0.09281152486801147, "Y": 0.6484677791595459}, {"X": 0.0928116887807846, "Y": 0.6394659280776978}, {"X": 0.10840900987386703, "Y": 0.6394659876823425}]}, "Id": "f469fc58-1f38-483d-9f9c-11dcb2b682a4"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "representative", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.166989266872406, "Height": 0.016479026526212692, "Left": 0.7687583565711975, "Top": 0.6125828623771667}, "Polygon": [{"X": 0.9357472658157349, "Y": 0.6290618777275085}, {"X": 0.7687583565711975, "Y": 0.6290612816810608}, {"X": 0.7687587738037109, "Y": 0.6125828623771667}, {"X": 0.9357476234436035, "Y": 0.6125833988189697}]}, "Id": "cc1da227-7ca6-4272-90c1-b3cdbd2297de"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026306452229619026, "Height": 0.013037854805588722, "Left": 0.738771915435791, "Top": 0.616723358631134}, "Polygon": [{"X": 0.765078067779541, "Y": 0.6297612190246582}, {"X": 0.738771915435791, "Y": 0.6297610998153687}, {"X": 0.7387722134590149, "Y": 0.616723358631134}, {"X": 0.7650783658027649, "Y": 0.6167234182357788}]}, "Id": "64c156fc-f826-45a8-a03c-88328936b655"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03839004412293434, "Height": 0.013002952560782433, "Left": 0.695423424243927, "Top": 0.6166897416114807}, "Polygon": [{"X": 0.7338131666183472, "Y": 0.6296926736831665}, {"X": 0.695423424243927, "Y": 0.6296924948692322}, {"X": 0.6954236626625061, "Y": 0.6166897416114807}, {"X": 0.733813464641571, "Y": 0.6166898608207703}]}, "Id": "d919906d-8732-4d58-a856-9fdb07cb9663"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "acquiring", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10882595926523209, "Height": 0.016103753820061684, "Left": 0.5814924240112305, "Top": 0.6140038967132568}, "Polygon": [{"X": 0.6903179883956909, "Y": 0.6301076412200928}, {"X": 0.5814924240112305, "Y": 0.6301072239875793}, {"X": 0.5814927220344543, "Y": 0.6140038967132568}, {"X": 0.6903183460235596, "Y": 0.6140042543411255}]}, "Id": "396a27a9-0182-4f47-89b4-96ce2b892f96"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "carrier.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07866442203521729, "Height": 0.012737179175019264, "Left": 0.49731558561325073, "Top": 0.6169065833091736}, "Polygon": [{"X": 0.5759797096252441, "Y": 0.6296437978744507}, {"X": 0.49731558561325073, "Y": 0.6296434998512268}, {"X": 0.49731582403182983, "Y": 0.6169065833091736}, {"X": 0.575980007648468, "Y": 0.6169068813323975}]}, "Id": "5d7845c8-3101-4161-bd83-74e01ec7d541"}, {"BlockType": "WORD", "Confidence": 98.37711334228516, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024111038073897362, "Height": 0.012537038885056973, "Left": 0.4679456651210785, "Top": 0.617412269115448}, "Polygon": [{"X": 0.4920564293861389, "Y": 0.6299492716789246}, {"X": 0.4679456651210785, "Y": 0.6299492120742798}, {"X": 0.4679459035396576, "Y": 0.617412269115448}, {"X": 0.4920566976070404, "Y": 0.6174123287200928}]}, "Id": "007d6b37-eafb-49b2-9919-4fed53752950"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "consideration", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15668711066246033, "Height": 0.013497107662260532, "Left": 0.30586743354797363, "Top": 0.6177054643630981}, "Polygon": [{"X": 0.46255427598953247, "Y": 0.6312025785446167}, {"X": 0.30586743354797363, "Y": 0.631201982498169}, {"X": 0.3058677017688751, "Y": 0.6177054643630981}, {"X": 0.46255454421043396, "Y": 0.6177060008049011}]}, "Id": "279dd4f3-09b1-4076-bb24-ac38a1d5620a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026581421494483948, "Height": 0.013536068610846996, "Left": 0.27382075786590576, "Top": 0.6189283728599548}, "Polygon": [{"X": 0.300401896238327, "Y": 0.6324644684791565}, {"X": 0.27382075786590576, "Y": 0.6324643492698669}, {"X": 0.27382102608680725, "Y": 0.6189283728599548}, {"X": 0.3004021644592285, "Y": 0.6189284920692444}]}, "Id": "3be42a00-33ea-41a8-9b18-4af4cbea44ba"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03876003250479698, "Height": 0.01293085515499115, "Left": 0.23086141049861908, "Top": 0.6192244291305542}, "Polygon": [{"X": 0.26962119340896606, "Y": 0.6321552991867065}, {"X": 0.23086141049861908, "Y": 0.632155179977417}, {"X": 0.23086166381835938, "Y": 0.6192244291305542}, {"X": 0.26962143182754517, "Y": 0.6192246079444885}]}, "Id": "1a127c27-3db1-4580-9e21-ed0faf69b25f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "acceptance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13037057220935822, "Height": 0.014061087742447853, "Left": 0.09503443539142609, "Top": 0.6176658868789673}, "Polygon": [{"X": 0.2254047393798828, "Y": 0.6317269802093506}, {"X": 0.09503443539142609, "Y": 0.6317264437675476}, {"X": 0.09503469616174698, "Y": 0.6176658868789673}, {"X": 0.2254050076007843, "Y": 0.6176663041114807}]}, "Id": "d304eda9-c1b8-44e8-81ea-11510b6ef764"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02593846246600151, "Height": 0.013196874409914017, "Left": 0.9090015292167664, "Top": 0.5979118943214417}, "Polygon": [{"X": 0.9349396824836731, "Y": 0.6111087799072266}, {"X": 0.9090015292167664, "Y": 0.611108660697937}, {"X": 0.9090018272399902, "Y": 0.5979118943214417}, {"X": 0.934939980506897, "Y": 0.5979119539260864}]}, "Id": "eeb7347c-b193-4601-88b1-01342b78ca99"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039168279618024826, "Height": 0.01269777026027441, "Left": 0.8659684658050537, "Top": 0.5981525778770447}, "Polygon": [{"X": 0.9051364660263062, "Y": 0.6108503341674805}, {"X": 0.8659684658050537, "Y": 0.6108502149581909}, {"X": 0.8659687638282776, "Y": 0.5981525778770447}, {"X": 0.90513676404953, "Y": 0.5981526970863342}]}, "Id": "9625236a-cec1-4de3-b03f-23981d72f582"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07069554179906845, "Height": 0.01264109369367361, "Left": 0.7904908061027527, "Top": 0.5986742973327637}, "Polygon": [{"X": 0.8611860275268555, "Y": 0.6113153696060181}, {"X": 0.7904908061027527, "Y": 0.611315131187439}, {"X": 0.7904910445213318, "Y": 0.5986742973327637}, {"X": 0.8611863255500793, "Y": 0.598674476146698}]}, "Id": "2417ffc5-a028-414a-91dd-bc9028c7da48"}, {"BlockType": "WORD", "Confidence": 98.44547271728516, "Text": "container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1107330471277237, "Height": 0.012726922519505024, "Left": 0.6745508313179016, "Top": 0.5992569327354431}, "Polygon": [{"X": 0.785283625125885, "Y": 0.6119838356971741}, {"X": 0.6745508313179016, "Y": 0.6119834780693054}, {"X": 0.6745511293411255, "Y": 0.5992569327354431}, {"X": 0.7852838635444641, "Y": 0.5992572903633118}]}, "Id": "5897a190-66cd-44e3-9d1d-709aad495813"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03519029915332794, "Height": 0.012818172574043274, "Left": 0.6359763741493225, "Top": 0.5992574095726013}, "Polygon": [{"X": 0.6711664199829102, "Y": 0.6120755672454834}, {"X": 0.6359763741493225, "Y": 0.6120754480361938}, {"X": 0.6359766721725464, "Y": 0.5992574095726013}, {"X": 0.6711666584014893, "Y": 0.5992574691772461}]}, "Id": "33af3a96-75d2-4f0d-b96a-5a24e2087b32"}, {"BlockType": "WORD", "Confidence": 63.28921890258789, "Text": "storage,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0918101966381073, "Height": 0.014717193320393562, "Left": 0.5388346910476685, "Top": 0.5964470505714417}, "Polygon": [{"X": 0.6306446194648743, "Y": 0.6111642122268677}, {"X": 0.5388346910476685, "Y": 0.6111639142036438}, {"X": 0.5388350486755371, "Y": 0.5964470505714417}, {"X": 0.6306449174880981, "Y": 0.5964473485946655}]}, "Id": "5a953ad3-f572-423d-904c-62a44e2fd5c5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03943977504968643, "Height": 0.012763304635882378, "Left": 0.49328717589378357, "Top": 0.5997596979141235}, "Polygon": [{"X": 0.5327266454696655, "Y": 0.6125230193138123}, {"X": 0.49328717589378357, "Y": 0.6125229001045227}, {"X": 0.49328744411468506, "Y": 0.5997596979141235}, {"X": 0.5327269434928894, "Y": 0.5997598767280579}]}, "Id": "f62f4569-0d89-4664-9199-e5ef043d7a31"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07797900587320328, "Height": 0.012881314381957054, "Left": 0.4104342460632324, "Top": 0.5998353958129883}, "Polygon": [{"X": 0.4884129762649536, "Y": 0.6127167344093323}, {"X": 0.4104342460632324, "Y": 0.6127164959907532}, {"X": 0.4104345142841339, "Y": 0.5998353958129883}, {"X": 0.4884132444858551, "Y": 0.5998356938362122}]}, "Id": "63d368ec-374f-4abf-af94-be581dc92358"}, {"BlockType": "WORD", "Confidence": 98.69519805908203, "Text": "acknowledges", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16447219252586365, "Height": 0.015819409862160683, "Left": 0.2408132553100586, "Top": 0.5985534191131592}, "Polygon": [{"X": 0.405285120010376, "Y": 0.6143728494644165}, {"X": 0.2408132553100586, "Y": 0.6143723130226135}, {"X": 0.24081356823444366, "Y": 0.5985534191131592}, {"X": 0.40528544783592224, "Y": 0.5985539555549622}]}, "Id": "b8743f97-59eb-4b3d-9c87-9cac7adc3987"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04914708063006401, "Height": 0.013150502927601337, "Left": 0.18631210923194885, "Top": 0.6019778251647949}, "Polygon": [{"X": 0.23545894026756287, "Y": 0.6151282787322998}, {"X": 0.18631210923194885, "Y": 0.6151281595230103}, {"X": 0.18631236255168915, "Y": 0.6019778251647949}, {"X": 0.23545919358730316, "Y": 0.6019779443740845}]}, "Id": "ccefb7ae-d6cc-4ab1-bd5d-3811483f7e5f"}, {"BlockType": "WORD", "Confidence": 99.39393615722656, "Text": "all", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030116595327854156, "Height": 0.012927723117172718, "Left": 0.15210826694965363, "Top": 0.602756917476654}, "Polygon": [{"X": 0.18222463130950928, "Y": 0.6156846284866333}, {"X": 0.15210826694965363, "Y": 0.6156845688819885}, {"X": 0.15210852026939392, "Y": 0.602756917476654}, {"X": 0.18222486972808838, "Y": 0.6027570366859436}]}, "Id": "e79a1995-24f5-49ce-b1bd-8600be00f183"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08086929470300674, "Height": 0.0162988118827343, "Left": 0.855577826499939, "Top": 0.5778500437736511}, "Polygon": [{"X": 0.9364467263221741, "Y": 0.5941488146781921}, {"X": 0.855577826499939, "Y": 0.594148576259613}, {"X": 0.8555781841278076, "Y": 0.5778500437736511}, {"X": 0.9364470839500427, "Y": 0.5778502821922302}]}, "Id": "25ede507-bb4d-46db-a742-fe6c655bd191"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "charges", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0896855965256691, "Height": 0.016231730580329895, "Left": 0.7614840269088745, "Top": 0.57793790102005}, "Polygon": [{"X": 0.851169228553772, "Y": 0.5941696166992188}, {"X": 0.7614840269088745, "Y": 0.5941693782806396}, {"X": 0.7614843845367432, "Y": 0.57793790102005}, {"X": 0.8511696457862854, "Y": 0.5779381990432739}]}, "Id": "fd721a0e-da40-433a-b00e-a12bbb4a5e80"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "have", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05583609640598297, "Height": 0.01278684101998806, "Left": 0.7006840109825134, "Top": 0.5817533731460571}, "Polygon": [{"X": 0.7565197944641113, "Y": 0.5945401787757874}, {"X": 0.7006840109825134, "Y": 0.594539999961853}, {"X": 0.7006843090057373, "Y": 0.5817533731460571}, {"X": 0.7565200924873352, "Y": 0.5817535519599915}]}, "Id": "271ff83d-c110-4f27-b383-93fff8502400"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0561707466840744, "Height": 0.013266053050756454, "Left": 0.6389874815940857, "Top": 0.5817098617553711}, "Polygon": [{"X": 0.6951578855514526, "Y": 0.5949759483337402}, {"X": 0.6389874815940857, "Y": 0.5949757695198059}, {"X": 0.6389877200126648, "Y": 0.5817098617553711}, {"X": 0.6951581835746765, "Y": 0.5817100405693054}]}, "Id": "7afafe01-1950-42ed-b0e9-f6140a3858ed"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "paid", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05156797915697098, "Height": 0.01600918546319008, "Left": 0.5822480916976929, "Top": 0.5790971517562866}, "Polygon": [{"X": 0.6338157653808594, "Y": 0.595106303691864}, {"X": 0.5822480916976929, "Y": 0.5951061844825745}, {"X": 0.5822484493255615, "Y": 0.5790971517562866}, {"X": 0.633816123008728, "Y": 0.579097330570221}]}, "Id": "74667b70-9d7f-4756-ad5a-aa12a50a109e"}, {"BlockType": "WORD", "Confidence": 98.75617980957031, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026386743411421776, "Height": 0.009684797376394272, "Left": 0.5512169599533081, "Top": 0.5821453928947449}, "Polygon": [{"X": 0.5776035189628601, "Y": 0.5918301939964294}, {"X": 0.5512169599533081, "Y": 0.5918300747871399}, {"X": 0.5512171983718872, "Y": 0.5821453928947449}, {"X": 0.5776036977767944, "Y": 0.5821454524993896}]}, "Id": "3846d091-aac1-4a68-a81d-52cd28c36461"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "agrees", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07561513781547546, "Height": 0.012892264872789383, "Left": 0.4709390103816986, "Top": 0.579382598400116}, "Polygon": [{"X": 0.546553909778595, "Y": 0.5922748446464539}, {"X": 0.4709390103816986, "Y": 0.5922746062278748}, {"X": 0.4709392786026001, "Y": 0.579382598400116}, {"X": 0.5465541481971741, "Y": 0.5793828368186951}]}, "Id": "fa188e52-09a4-4ebb-9ff6-541bd2e6a843"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025639446452260017, "Height": 0.01100409496575594, "Left": 0.4402535557746887, "Top": 0.582688570022583}, "Polygon": [{"X": 0.4658927619457245, "Y": 0.5936927199363708}, {"X": 0.4402535557746887, "Y": 0.5936926007270813}, {"X": 0.44025376439094543, "Y": 0.582688570022583}, {"X": 0.4658930003643036, "Y": 0.5826886892318726}]}, "Id": "ef95a11b-127a-4ce7-9ffc-0025aa3f5480"}, {"BlockType": "WORD", "Confidence": 99.12308502197266, "Text": "look", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05169162526726723, "Height": 0.013182944618165493, "Left": 0.38289979100227356, "Top": 0.5829898715019226}, "Polygon": [{"X": 0.4345911741256714, "Y": 0.5961728692054749}, {"X": 0.38289979100227356, "Y": 0.5961726903915405}, {"X": 0.38290005922317505, "Y": 0.5829898715019226}, {"X": 0.4345914423465729, "Y": 0.5829900503158569}]}, "Id": "9f401109-6282-4e42-b857-e2e6faa1df7c"}, {"BlockType": "WORD", "Confidence": 88.0420913696289, "Text": "solely", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06925680488348007, "Height": 0.015948813408613205, "Left": 0.3102709949016571, "Top": 0.5808543562889099}, "Polygon": [{"X": 0.3795274794101715, "Y": 0.5968031883239746}, {"X": 0.3102709949016571, "Y": 0.5968029499053955}, {"X": 0.31027132272720337, "Y": 0.5808543562889099}, {"X": 0.3795278072357178, "Y": 0.580854594707489}]}, "Id": "dd7e2182-3b5a-463b-b9a6-d9db10d9b362"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02513357438147068, "Height": 0.010878977365791798, "Left": 0.2806248068809509, "Top": 0.5840967893600464}, "Polygon": [{"X": 0.30575817823410034, "Y": 0.5949757695198059}, {"X": 0.2806248068809509, "Y": 0.5949756503105164}, {"X": 0.28062501549720764, "Y": 0.5840967893600464}, {"X": 0.30575838685035706, "Y": 0.5840968489646912}]}, "Id": "165d7e3c-7553-41ef-9f80-adf1b834c10e"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039637722074985504, "Height": 0.012613021768629551, "Left": 0.23534835875034332, "Top": 0.5843233466148376}, "Polygon": [{"X": 0.2749858498573303, "Y": 0.5969363451004028}, {"X": 0.23534835875034332, "Y": 0.5969362258911133}, {"X": 0.23534859716892242, "Y": 0.5843233466148376}, {"X": 0.2749860882759094, "Y": 0.5843234658241272}]}, "Id": "7eda8f4f-14ce-4113-993e-72ef7bf4f548"}, {"BlockType": "WORD", "Confidence": 59.661991119384766, "Text": "consignor,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12051564455032349, "Height": 0.015683215111494064, "Left": 0.10921679437160492, "Top": 0.5822792649269104}, "Polygon": [{"X": 0.22973214089870453, "Y": 0.5979624390602112}, {"X": 0.10921679437160492, "Y": 0.5979620814323425}, {"X": 0.1092170849442482, "Y": 0.5822792649269104}, {"X": 0.2297324389219284, "Y": 0.582279622554779}]}, "Id": "385ac665-fd56-4d28-a85e-3780282bd030"}, {"BlockType": "WORD", "Confidence": 62.890625, "Text": "broker.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08151645213365555, "Height": 0.013640040531754494, "Left": 0.855242908000946, "Top": 0.5625572800636292}, "Polygon": [{"X": 0.9367589950561523, "Y": 0.5761973261833191}, {"X": 0.855242908000946, "Y": 0.57619708776474}, {"X": 0.8552432060241699, "Y": 0.5625572800636292}, {"X": 0.936759352684021, "Y": 0.5625575184822083}]}, "Id": "1936c2bd-279a-4061-bdb8-c1d65e0ee026"}, {"BlockType": "WORD", "Confidence": 94.66039276123047, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02625918574631214, "Height": 0.009446482174098492, "Left": 0.822957456111908, "Top": 0.5637356042861938}, "Polygon": [{"X": 0.8492164611816406, "Y": 0.5731820464134216}, {"X": 0.822957456111908, "Y": 0.5731819868087769}, {"X": 0.8229576945304871, "Y": 0.5637356042861938}, {"X": 0.849216639995575, "Y": 0.5637356638908386}]}, "Id": "18b5ddc4-bdc3-42d3-a2e7-fe68246d98e1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03855538368225098, "Height": 0.012633475475013256, "Left": 0.7789535522460938, "Top": 0.5639105439186096}, "Polygon": [{"X": 0.8175086379051208, "Y": 0.5765439867973328}, {"X": 0.7789535522460938, "Y": 0.576543927192688}, {"X": 0.7789538502693176, "Y": 0.5639105439186096}, {"X": 0.8175089359283447, "Y": 0.5639106631278992}]}, "Id": "0568a036-a45d-495d-a4c1-f9bdfda7dba2"}, {"BlockType": "WORD", "Confidence": 99.35247802734375, "Text": "beneficial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1122390478849411, "Height": 0.013063130900263786, "Left": 0.6613574028015137, "Top": 0.5639635920524597}, "Polygon": [{"X": 0.7735961675643921, "Y": 0.5770267248153687}, {"X": 0.6613574028015137, "Y": 0.5770263671875}, {"X": 0.6613577008247375, "Y": 0.5639635920524597}, {"X": 0.773596465587616, "Y": 0.5639638900756836}]}, "Id": "01fbddc3-4ae8-4f7a-8541-b476a103c5f3"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "owner", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0726560428738594, "Height": 0.009852005168795586, "Left": 0.5835280418395996, "Top": 0.564336359500885}, "Polygon": [{"X": 0.6561838388442993, "Y": 0.5741883516311646}, {"X": 0.5835280418395996, "Y": 0.5741881132125854}, {"X": 0.5835282206535339, "Y": 0.564336359500885}, {"X": 0.6561840772628784, "Y": 0.5643365383148193}]}, "Id": "ee5571d1-0aa7-4e9e-9aa3-578d3d471609"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026633210480213165, "Height": 0.012878332287073135, "Left": 0.5519407391548157, "Top": 0.5647961497306824}, "Polygon": [{"X": 0.5785737037658691, "Y": 0.5776745080947876}, {"X": 0.5519407391548157, "Y": 0.577674388885498}, {"X": 0.5519410371780396, "Y": 0.5647961497306824}, {"X": 0.578574001789093, "Y": 0.5647962093353271}]}, "Id": "b3973f65-b71b-436e-ab71-fc1b5539e480"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03753625974059105, "Height": 0.012659996747970581, "Left": 0.5104682445526123, "Top": 0.5646892786026001}, "Polygon": [{"X": 0.5480042099952698, "Y": 0.5773492455482483}, {"X": 0.5104682445526123, "Y": 0.5773491263389587}, {"X": 0.5104684829711914, "Y": 0.5646892786026001}, {"X": 0.5480045080184937, "Y": 0.5646893382072449}]}, "Id": "9419e8b7-58b6-4a57-b9b5-eeb11e09ac6d"}, {"BlockType": "WORD", "Confidence": 98.9636459350586, "Text": "freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07950492948293686, "Height": 0.01596728153526783, "Left": 0.4253339171409607, "Top": 0.5620564222335815}, "Polygon": [{"X": 0.5048385262489319, "Y": 0.5780236721038818}, {"X": 0.4253339171409607, "Y": 0.5780234336853027}, {"X": 0.42533424496650696, "Y": 0.5620564222335815}, {"X": 0.5048388838768005, "Y": 0.5620566606521606}]}, "Id": "ae5ace57-be8c-4f83-a0a5-ad6a311fcf27"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03515314683318138, "Height": 0.012784055434167385, "Left": 0.3855343759059906, "Top": 0.5655120015144348}, "Polygon": [{"X": 0.4206872880458832, "Y": 0.5782960653305054}, {"X": 0.3855343759059906, "Y": 0.5782959461212158}, {"X": 0.3855346441268921, "Y": 0.5655120015144348}, {"X": 0.4206875264644623, "Y": 0.5655121207237244}]}, "Id": "5579d821-0c77-4828-b427-510eb28ddf9d"}, {"BlockType": "WORD", "Confidence": 97.32939910888672, "Text": "payment.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10899338126182556, "Height": 0.015131103806197643, "Left": 0.27112123370170593, "Top": 0.562703013420105}, "Polygon": [{"X": 0.3801143169403076, "Y": 0.5778341293334961}, {"X": 0.27112123370170593, "Y": 0.5778338313102722}, {"X": 0.2711215317249298, "Y": 0.562703013420105}, {"X": 0.3801146149635315, "Y": 0.5627033114433289}]}, "Id": "3c76c1ab-95fd-404c-b787-9fa3b478db74"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04430150240659714, "Height": 0.013077289797365665, "Left": 0.22171998023986816, "Top": 0.5668548345565796}, "Polygon": [{"X": 0.2660212218761444, "Y": 0.5799320936203003}, {"X": 0.22171998023986816, "Y": 0.5799319744110107}, {"X": 0.22172023355960846, "Y": 0.5668548345565796}, {"X": 0.2660214900970459, "Y": 0.5668549537658691}]}, "Id": "42d45b08-99a7-4aef-9d76-648a27ce1ae1"}, {"BlockType": "WORD", "Confidence": 63.24936294555664, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08053287863731384, "Height": 0.01290320884436369, "Left": 0.1357114017009735, "Top": 0.5673852562904358}, "Polygon": [{"X": 0.21624404191970825, "Y": 0.5802884697914124}, {"X": 0.1357114017009735, "Y": 0.5802882313728333}, {"X": 0.1357116401195526, "Y": 0.5673852562904358}, {"X": 0.21624428033828735, "Y": 0.5673854947090149}]}, "Id": "92f36211-e72c-429b-9d11-71aabef4cda4"}, {"BlockType": "WORD", "Confidence": 89.51689910888672, "Text": "waives", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0789075568318367, "Height": 0.012925920076668262, "Left": 0.8580136895179749, "Top": 0.5453796982765198}, "Polygon": [{"X": 0.9369209408760071, "Y": 0.5583056211471558}, {"X": 0.8580136895179749, "Y": 0.5583054423332214}, {"X": 0.858013927936554, "Y": 0.5453796982765198}, {"X": 0.936921238899231, "Y": 0.5453799366950989}]}, "Id": "3a841e7c-df06-4426-9c9f-cc3f0fd92a5e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042402978986501694, "Height": 0.012956792488694191, "Left": 0.8103601336479187, "Top": 0.542445957660675}, "Polygon": [{"X": 0.8527628183364868, "Y": 0.5554027557373047}, {"X": 0.8103601336479187, "Y": 0.5554026365280151}, {"X": 0.8103604316711426, "Y": 0.542445957660675}, {"X": 0.8527631163597107, "Y": 0.5424460172653198}]}, "Id": "f97f0e27-c37d-44ff-83d5-602715e1fbc5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "right", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05802999064326286, "Height": 0.016627194359898567, "Left": 0.747362494468689, "Top": 0.5427142381668091}, "Polygon": [{"X": 0.8053921461105347, "Y": 0.5593414306640625}, {"X": 0.747362494468689, "Y": 0.559341311454773}, {"X": 0.7473628520965576, "Y": 0.5427142381668091}, {"X": 0.8053925037384033, "Y": 0.5427144169807434}]}, "Id": "fed27130-968c-4fa0-9077-8b6a44df5dea"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "it", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019062276929616928, "Height": 0.012880446389317513, "Left": 0.723888635635376, "Top": 0.5463583469390869}, "Polygon": [{"X": 0.7429506182670593, "Y": 0.5592387914657593}, {"X": 0.723888635635376, "Y": 0.5592387318611145}, {"X": 0.7238889336585999, "Y": 0.5463583469390869}, {"X": 0.7429509162902832, "Y": 0.5463584065437317}]}, "Id": "a55d1f4e-9145-4ef2-968f-bc3dbe2c869b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049552783370018005, "Height": 0.013235555030405521, "Left": 0.6698978543281555, "Top": 0.5429935455322266}, "Polygon": [{"X": 0.7194502949714661, "Y": 0.5562290549278259}, {"X": 0.6698978543281555, "Y": 0.5562289357185364}, {"X": 0.6698980927467346, "Y": 0.5429935455322266}, {"X": 0.7194505929946899, "Y": 0.5429936647415161}]}, "Id": "e289dcde-f85c-4b36-9a03-3b5527107cf1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "have", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0546417161822319, "Height": 0.01286748331040144, "Left": 0.6103044152259827, "Top": 0.5467885732650757}, "Polygon": [{"X": 0.6649458408355713, "Y": 0.559656023979187}, {"X": 0.6103044152259827, "Y": 0.5596559047698975}, {"X": 0.6103047132492065, "Y": 0.5467885732650757}, {"X": 0.6649461388587952, "Y": 0.5467886924743652}]}, "Id": "79722a52-2922-464e-b00a-a681855d8d36"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025505539029836655, "Height": 0.010588276199996471, "Left": 0.5790634155273438, "Top": 0.5470855236053467}, "Polygon": [{"X": 0.6045687198638916, "Y": 0.5576738119125366}, {"X": 0.5790634155273438, "Y": 0.5576737523078918}, {"X": 0.5790635943412781, "Y": 0.5470855236053467}, {"X": 0.6045688986778259, "Y": 0.5470855832099915}]}, "Id": "65351ff1-984f-459d-b49e-27e18b979e12"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "pursue", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07880503684282303, "Height": 0.01318668294698, "Left": 0.49512842297554016, "Top": 0.5437162518501282}, "Polygon": [{"X": 0.5739331841468811, "Y": 0.5569029450416565}, {"X": 0.49512842297554016, "Y": 0.5569027066230774}, {"X": 0.49512869119644165, "Y": 0.5437162518501282}, {"X": 0.573933482170105, "Y": 0.5437164306640625}]}, "Id": "98d73121-fd8a-4cce-b20b-d0b2e289f39e"}, {"BlockType": "WORD", "Confidence": 74.599609375, "Text": "TSL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042369842529296875, "Height": 0.012824663892388344, "Left": 0.4485872685909271, "Top": 0.547336220741272}, "Polygon": [{"X": 0.4909568428993225, "Y": 0.5601608753204346}, {"X": 0.4485872685909271, "Y": 0.560160756111145}, {"X": 0.4485875368118286, "Y": 0.547336220741272}, {"X": 0.490957111120224, "Y": 0.5473363399505615}]}, "Id": "938c143d-08db-4cb3-9ef6-6dd954982aaf"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03588724136352539, "Height": 0.013279042206704617, "Left": 0.4079355001449585, "Top": 0.5476303696632385}, "Polygon": [{"X": 0.44382244348526, "Y": 0.5609093904495239}, {"X": 0.4079355001449585, "Y": 0.5609093308448792}, {"X": 0.40793576836586, "Y": 0.5476303696632385}, {"X": 0.4438227415084839, "Y": 0.5476304888725281}]}, "Id": "f473e791-e435-437b-8665-cc157b69008f"}, {"BlockType": "WORD", "Confidence": 98.51542663574219, "Text": "payment.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10867194086313248, "Height": 0.014986916445195675, "Left": 0.29400044679641724, "Top": 0.5447887778282166}, "Polygon": [{"X": 0.40267208218574524, "Y": 0.559775710105896}, {"X": 0.29400044679641724, "Y": 0.5597754120826721}, {"X": 0.2940007448196411, "Y": 0.5447887778282166}, {"X": 0.4026723802089691, "Y": 0.5447890162467957}]}, "Id": "18ee84ca-3cd2-401d-8d29-f38ab95b8054"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Sign", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06589648127555847, "Height": 0.020008942112326622, "Left": 0.8702571988105774, "Top": 0.5201507806777954}, "Polygon": [{"X": 0.9361531734466553, "Y": 0.5401597023010254}, {"X": 0.8702571988105774, "Y": 0.5401595830917358}, {"X": 0.8702576160430908, "Y": 0.5201507806777954}, {"X": 0.9361536502838135, "Y": 0.520150899887085}]}, "Id": "a33f8a53-cdc8-44af-bc72-e258ec668108"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "here", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06688570976257324, "Height": 0.01693063974380493, "Left": 0.7959294319152832, "Top": 0.5242488980293274}, "Polygon": [{"X": 0.862814724445343, "Y": 0.5411795377731323}, {"X": 0.7959294319152832, "Y": 0.5411794185638428}, {"X": 0.7959297895431519, "Y": 0.5242488980293274}, {"X": 0.8628151416778564, "Y": 0.5242490768432617}]}, "Id": "273a7ea1-2ce6-4f1f-8672-2cabf8024a50"}, {"BlockType": "WORD", "Confidence": 94.29049682617188, "Text": "027225-1", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.48342815041542053, "Height": 0.07124191522598267, "Left": 0.22210495173931122, "Top": 0.3025270402431488}, "Polygon": [{"X": 0.7055315375328064, "Y": 0.37376895546913147}, {"X": 0.22210495173931122, "Y": 0.3737688362598419}, {"X": 0.22210632264614105, "Y": 0.30252739787101746}, {"X": 0.7055330872535706, "Y": 0.3025270402431488}]}, "Id": "7a927426-2379-405c-86cc-de0eaa8c5cbb"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "defb03a4-5848-49dd-add8-bd07ef0811ed", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "defb03a4-5848-49dd-add8-bd07ef0811ed", "content-type": "application/x-amz-json-1.1", "content-length": "78308", "date": "Wed, 24 Sep 2025 13:28:31 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "40d69375-4b48-4273-8833-93a905ac8c33", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:28:34 GMT", "content-type": "application/json", "content-length": "680", "connection": "keep-alive", "x-amzn-requestid": "40d69375-4b48-4273-8833-93a905ac8c33"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We read content. It's an ingate? The header: \"TSL Ingate: OMA\". Contains \"ActivityDate\", \"Inspector\", \"Carrier CALHOUN TRUCK LINES\". It's an ingate document. So doc_type: ingate.\n\nCheck if any continuation? No. It's page1. So output page_no 1 and doc_type ingate.\n\nReturn JSON with documents array."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"ingate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2820, "outputTokens": 111, "totalTokens": 2931}, "metrics": {"latencyMs": 764}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/f1c4c021_AJ17T27ABDSGCYL1YI7Q.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nTSL Ingate: OMA\nActivityDate: 09/22/2025 13:52\nInspector\nCarrier CALHOUN TRUCK LINES\nTrucker: Unknown Unit: 2155\nHamu346538-8 40HC Owner:HAPAGLLOYDAMERICAING\nLoaded:No Hazardous:No Lifted:Yes\nGrade:NF+\nSeal: Genset:\nTjpz437956 40CH Owner:TRACINTERMODAL\nGenset: Hubmeter:\nI hereby certify that on the date stated above. the person who conducted the\ninspection covered by this report was competent and qualified to do so and\nwas duly authorized to inspect and take possession of the equipment as a\nrepresentative of the acquiring carrier. In consideration of the acceptance\nof the above container for storage, the Carrier acknowledges that all\nfreight charges have been paid or agrees to look solely to the consignor,\nbroker. or the beneficial owner of the freight for payment. The Carrier\nwaives any right it may have to pursue TSL for payment.\nSign here\n027225-1\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}