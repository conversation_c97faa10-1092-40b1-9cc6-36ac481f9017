{"classification_result": {"documents": [{"page_no": 1, "doc_type": "pack_list"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 6.534511953759647e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.809756255966022e-08, "Y": 1.0}]}, "Id": "f6c82ad0-fbde-4b9a-a956-df24a0f74a2e", "Relationships": [{"Type": "CHILD", "Ids": ["dc74c437-223b-46d4-86f1-dd143487bc99", "6221a839-5b9d-4b8d-9657-5b4b54935e5b", "01ecf009-ba9f-4537-9c9b-1ffb925d0ee6", "b9de25a1-3f7e-41f4-9019-966e538f1324", "41ea5c21-4302-42db-9221-ba498c3baf18", "7dd35651-8f1f-46a8-90bf-18ee26b1c68e", "c0baacf2-924a-4822-a402-887f0839f0fb", "07dcdbf1-a378-4ee4-913f-f106ba959afd", "2e517845-d26e-4b58-8308-d86862f92ae7", "12e3352f-83e2-4972-bb24-1f3d2c80ff31", "385bc1a2-83b9-475c-8012-c5d500b79a46", "e152dee2-3a9a-483e-9ded-b4ad7f94b0f9", "9c55b334-3c0b-42b6-b99c-a56ff61e8a4c", "53a4b04e-c830-4316-94ec-7d6baa14cfa7", "17692ce9-94f2-4a0e-939d-5a3148218c8b", "4b5b3e6c-57e8-4b92-afef-46e92e7f72c6", "8944d29e-8f25-46d7-b36d-9e734d8ec442", "af4fea55-13e1-4fc2-90e0-7ab7b71d59a8", "29f807d9-5900-43cc-b414-6266110ca36b", "577ce323-d147-4190-ba3a-fe63299534ec", "7a3d9485-0d7d-466f-bf16-5f76cd45e301", "31572533-bca3-4b1e-9bfd-215565e5e6cd", "f1deb264-0af6-4400-904d-2a96a2136684", "1b70d575-de97-43e9-af63-cd77e81abdc0", "2a86dc39-6834-474c-a00a-f76fa755a8f9", "a099a795-9fd8-416a-b5b0-d03922dfea37", "c7da2f34-d8ee-41b6-a2fc-f1b5456b9ef6", "2ca96430-54e3-4d9e-b46f-6b2c64ebbc2d", "4dc65811-c97a-4a22-bee2-3b1454731882"]}]}, {"BlockType": "LINE", "Confidence": 99.9072265625, "Text": "PACKING LIST", "Geometry": {"BoundingBox": {"Width": 0.13989140093326569, "Height": 0.01085684448480606, "Left": 0.36531582474708557, "Top": 0.0843358114361763}, "Polygon": [{"X": 0.36531585454940796, "Y": 0.0843365341424942}, {"X": 0.5052072405815125, "Y": 0.0843358114361763}, {"X": 0.5052071809768677, "Y": 0.09519195556640625}, {"X": 0.36531582474708557, "Y": 0.09519265592098236}]}, "Id": "dc74c437-223b-46d4-86f1-dd143487bc99", "Relationships": [{"Type": "CHILD", "Ids": ["d37c74d5-4937-4ed3-ae1b-5b4da72d3ddc", "be397993-3044-4506-a04e-45001605c6fe"]}]}, {"BlockType": "LINE", "Confidence": 98.97711944580078, "Text": "B/L #: 155734 Page 1", "Geometry": {"BoundingBox": {"Width": 0.26985782384872437, "Height": 0.014679105952382088, "Left": 0.6954561471939087, "Top": 0.08447351306676865}, "Polygon": [{"X": 0.6954562067985535, "Y": 0.08447489887475967}, {"X": 0.9653139710426331, "Y": 0.08447351306676865}, {"X": 0.9653137922286987, "Y": 0.09915127605199814}, {"X": 0.6954561471939087, "Y": 0.09915261715650558}]}, "Id": "6221a839-5b9d-4b8d-9657-5b4b54935e5b", "Relationships": [{"Type": "CHILD", "Ids": ["7fe9b689-46c3-4a43-afa3-2ada67e41fa4", "478db9b5-09b6-48bd-85ee-59a437bd0df2", "2d173e51-ecc9-4bb6-9fab-4ff0768ca337", "26597753-ef92-42fc-94c2-6ee9af02aa89", "0d25cfc8-eba8-4bb0-8d72-366669ca81c4"]}]}, {"BlockType": "LINE", "Confidence": 97.90676879882812, "Text": "Consignee:", "Geometry": {"BoundingBox": {"Width": 0.11395489424467087, "Height": 0.013315297663211823, "Left": 0.36429363489151, "Top": 0.11486790329217911}, "Polygon": [{"X": 0.3642936646938324, "Y": 0.11486844718456268}, {"X": 0.4782485365867615, "Y": 0.11486790329217911}, {"X": 0.4782484769821167, "Y": 0.12818266451358795}, {"X": 0.36429363489151, "Y": 0.12818320095539093}]}, "Id": "01ecf009-ba9f-4537-9c9b-1ffb925d0ee6", "Relationships": [{"Type": "CHILD", "Ids": ["854ec8de-510a-4a56-904d-10b5083a2cfc"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "0280", "Geometry": {"BoundingBox": {"Width": 0.0450289361178875, "Height": 0.010807805694639683, "Left": 0.5653091669082642, "Top": 0.11534329503774643}, "Polygon": [{"X": 0.5653092265129089, "Y": 0.11534351110458374}, {"X": 0.6103380918502808, "Y": 0.11534329503774643}, {"X": 0.6103380918502808, "Y": 0.12615089118480682}, {"X": 0.5653091669082642, "Y": 0.12615109980106354}]}, "Id": "b9de25a1-3f7e-41f4-9019-966e538f1324", "Relationships": [{"Type": "CHILD", "Ids": ["15a00379-008d-4e2e-8495-ca1bedfbe30f"]}]}, {"BlockType": "LINE", "Confidence": 99.70753479003906, "Text": "HEXPOL Middlefield", "Geometry": {"BoundingBox": {"Width": 0.2103724628686905, "Height": 0.011288822628557682, "Left": 0.047401707619428635, "Top": 0.12872549891471863}, "Polygon": [{"X": 0.047401707619428635, "Y": 0.12872646749019623}, {"X": 0.2577741742134094, "Y": 0.12872549891471863}, {"X": 0.2577741742134094, "Y": 0.14001336693763733}, {"X": 0.047401733696460724, "Y": 0.14001432061195374}]}, "Id": "41ea5c21-4302-42db-9221-ba498c3baf18", "Relationships": [{"Type": "CHILD", "Ids": ["b6e45209-e55d-4b94-b830-3d4c2acf21fd", "426552e4-c14a-4907-ab19-597492b3212d"]}]}, {"BlockType": "LINE", "Confidence": 99.85138702392578, "Text": "Ames Rubber Corporation", "Geometry": {"BoundingBox": {"Width": 0.2703813314437866, "Height": 0.013684600591659546, "Left": 0.3637791574001312, "Top": 0.1301092505455017}, "Polygon": [{"X": 0.3637791574001312, "Y": 0.130110502243042}, {"X": 0.6341604590415955, "Y": 0.1301092505455017}, {"X": 0.6341603994369507, "Y": 0.14379265904426575}, {"X": 0.3637791574001312, "Y": 0.14379385113716125}]}, "Id": "7dd35651-8f1f-46a8-90bf-18ee26b1c68e", "Relationships": [{"Type": "CHILD", "Ids": ["db9bb598-8300-46d9-8e93-9ea433c83a45", "aed2a9d6-99f3-4268-86db-12d806e55f1f", "3336ed5d-32fb-4223-abdb-f80e0c96680c"]}]}, {"BlockType": "LINE", "Confidence": 99.82341766357422, "Text": "14910 Madison Road", "Geometry": {"BoundingBox": {"Width": 0.2100534290075302, "Height": 0.011902444064617157, "Left": 0.04802773520350456, "Top": 0.14351043105125427}, "Polygon": [{"X": 0.04802773520350456, "Y": 0.14351136982440948}, {"X": 0.2580811679363251, "Y": 0.14351043105125427}, {"X": 0.2580811679363251, "Y": 0.155411958694458}, {"X": 0.04802776128053665, "Y": 0.15541286766529083}]}, "Id": "c0baacf2-924a-4822-a402-887f0839f0fb", "Relationships": [{"Type": "CHILD", "Ids": ["952bca84-4651-4320-8d72-dc209a1ee1ea", "47e66fa5-063e-46b0-a98d-922686419988", "c1de89ef-7251-4cd8-8d72-0d5f0e23dd1a"]}]}, {"BlockType": "LINE", "Confidence": 99.56041717529297, "Text": "Middlefield, Ohio 44062", "Geometry": {"BoundingBox": {"Width": 0.2689545452594757, "Height": 0.01322618592530489, "Left": 0.0465041846036911, "Top": 0.15874189138412476}, "Polygon": [{"X": 0.0465041846036911, "Y": 0.15874303877353668}, {"X": 0.315458744764328, "Y": 0.15874189138412476}, {"X": 0.3154587149620056, "Y": 0.17196696996688843}, {"X": 0.04650421440601349, "Y": 0.17196807265281677}]}, "Id": "07dcdbf1-a378-4ee4-913f-f106ba959afd", "Relationships": [{"Type": "CHILD", "Ids": ["c46d4ab7-8742-421d-918e-f661590b7d9f", "42036689-4d3b-46fa-97af-683138933554", "9e4c5b5f-b980-4f34-9218-80c64d932ba8"]}]}, {"BlockType": "LINE", "Confidence": 99.57801818847656, "Text": "19 Ames Boulevard", "Geometry": {"BoundingBox": {"Width": 0.19847165048122406, "Height": 0.011554566211998463, "Left": 0.3653525114059448, "Top": 0.16020721197128296}, "Polygon": [{"X": 0.3653525412082672, "Y": 0.160208061337471}, {"X": 0.5638241767883301, "Y": 0.16020721197128296}, {"X": 0.5638241171836853, "Y": 0.17176096141338348}, {"X": 0.3653525114059448, "Y": 0.17176178097724915}]}, "Id": "2e517845-d26e-4b58-8308-d86862f92ae7", "Relationships": [{"Type": "CHILD", "Ids": ["d9b3865c-a55d-40f9-bfc7-1b5707d2f1de", "f1987909-5836-418f-a1f9-8b4d4904091d", "c645ed95-7922-4452-bbdb-8a5c56d26b10"]}]}, {"BlockType": "LINE", "Confidence": 99.69407653808594, "Text": "Hamburg, NJ 07419", "Geometry": {"BoundingBox": {"Width": 0.19809237122535706, "Height": 0.01364427525550127, "Left": 0.3645101487636566, "Top": 0.1906934678554535}, "Polygon": [{"X": 0.364510178565979, "Y": 0.19069424271583557}, {"X": 0.5626025199890137, "Y": 0.1906934678554535}, {"X": 0.5626024603843689, "Y": 0.204337015748024}, {"X": 0.3645101487636566, "Y": 0.20433774590492249}]}, "Id": "12e3352f-83e2-4972-bb24-1f3d2c80ff31", "Relationships": [{"Type": "CHILD", "Ids": ["0394abc0-4e90-431b-a54f-b8889586a400", "4db95098-914b-497e-adeb-ef51b66f11e0", "819bdb26-0640-47fc-8577-df21d20bd994"]}]}, {"BlockType": "LINE", "Confidence": 99.77395629882812, "Text": "Bill To: Ames Rubber Corporation", "Geometry": {"BoundingBox": {"Width": 0.3751468062400818, "Height": 0.014952967874705791, "Left": 0.023403842002153397, "Top": 0.20441725850105286}, "Polygon": [{"X": 0.023403842002153397, "Y": 0.20441865921020508}, {"X": 0.3985506594181061, "Y": 0.20441725850105286}, {"X": 0.3985506296157837, "Y": 0.21936888992786407}, {"X": 0.02340388111770153, "Y": 0.21937023103237152}]}, "Id": "385bc1a2-83b9-475c-8012-c5d500b79a46", "Relationships": [{"Type": "CHILD", "Ids": ["ce88f0cc-c186-45fa-aa9a-f39730374aa0", "5a6b9550-4cdc-45d9-a825-90135ddef1c4", "51955997-154a-4472-b77d-e4125f359d37", "9ab4e501-13f6-468d-8983-ed2767b83c2a", "9db6dcd4-ee70-4c4d-ac2b-ca5f33be361b"]}]}, {"BlockType": "LINE", "Confidence": 97.41024780273438, "Text": "Ship Date: 09/16/2025", "Geometry": {"BoundingBox": {"Width": 0.23205198347568512, "Height": 0.013643910177052021, "Left": 0.023981843143701553, "Top": 0.23527586460113525}, "Polygon": [{"X": 0.023981843143701553, "Y": 0.23527663946151733}, {"X": 0.25603383779525757, "Y": 0.23527586460113525}, {"X": 0.2560338079929352, "Y": 0.2489190250635147}, {"X": 0.02398187853395939, "Y": 0.2489197701215744}]}, "Id": "e152dee2-3a9a-483e-9ded-b4ad7f94b0f9", "Relationships": [{"Type": "CHILD", "Ids": ["2a29a2ed-8d26-4afd-810b-c8b16b7a61f3", "36eba661-dd8d-482a-8df6-06906d04ab3c", "9167eeca-4f22-48b3-8044-323b5ecdac3d"]}]}, {"BlockType": "LINE", "Confidence": 99.35746002197266, "Text": "SHIP VIA: Email: <EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.5410919785499573, "Height": 0.014666632749140263, "Left": 0.02398800291121006, "Top": 0.2512836158275604}, "Polygon": [{"X": 0.02398800291121006, "Y": 0.2512853443622589}, {"X": 0.5650799870491028, "Y": 0.2512836158275604}, {"X": 0.565079927444458, "Y": 0.265948623418808}, {"X": 0.023988042026758194, "Y": 0.2659502625465393}]}, "Id": "9c55b334-3c0b-42b6-b99c-a56ff61e8a4c", "Relationships": [{"Type": "CHILD", "Ids": ["b3965690-a298-49be-8fc5-ec8901bd40dd", "186d2fa8-753d-4127-b278-27272c3e6f5f", "2ce70457-35ff-4203-8ec4-6407e0c7d320", "e7ef4a9a-a921-4a8f-8238-ac7f07cf2427"]}]}, {"BlockType": "LINE", "Confidence": 99.306640625, "Text": "613913 1 SKIDS", "Geometry": {"BoundingBox": {"Width": 0.1733102649450302, "Height": 0.011702428571879864, "Left": 0.02478020079433918, "Top": 0.2660052180290222}, "Polygon": [{"X": 0.02478020079433918, "Y": 0.2660057544708252}, {"X": 0.19809046387672424, "Y": 0.2660052180290222}, {"X": 0.19809046387672424, "Y": 0.27770715951919556}, {"X": 0.024780230596661568, "Y": 0.27770766615867615}]}, "Id": "53a4b04e-c830-4316-94ec-7d6baa14cfa7", "Relationships": [{"Type": "CHILD", "Ids": ["6f48d2f0-941c-435b-951d-0f40b2f3bdb1", "0cc86997-e3ea-4f48-b892-f50ab4d21b8a", "04bc63cf-282e-4fc2-85fa-5f4211548f1e"]}]}, {"BlockType": "LINE", "Confidence": 97.16637420654297, "Text": "CPD9420 A (M)", "Geometry": {"BoundingBox": {"Width": 0.1359325647354126, "Height": 0.012538510374724865, "Left": 0.2698536217212677, "Top": 0.26706093549728394}, "Polygon": [{"X": 0.2698536217212677, "Y": 0.267061322927475}, {"X": 0.4057861864566803, "Y": 0.26706093549728394}, {"X": 0.4057861566543579, "Y": 0.27959904074668884}, {"X": 0.2698536217212677, "Y": 0.2795994281768799}]}, "Id": "17692ce9-94f2-4a0e-939d-5a3148218c8b", "Relationships": [{"Type": "CHILD", "Ids": ["d0c10ba7-c405-4fe4-b928-0db462c7623a", "ae93576c-c781-4d21-a013-7797635f64dc", "648f5024-64f5-48f7-97fc-d693e6af8b11"]}]}, {"BlockType": "LINE", "Confidence": 98.80009460449219, "Text": "NET #: 259", "Geometry": {"BoundingBox": {"Width": 0.14174631237983704, "Height": 0.01182391494512558, "Left": 0.7648095488548279, "Top": 0.26743701100349426}, "Polygon": [{"X": 0.7648096084594727, "Y": 0.2674374282360077}, {"X": 0.9065558314323425, "Y": 0.26743701100349426}, {"X": 0.906555712223053, "Y": 0.279260516166687}, {"X": 0.7648095488548279, "Y": 0.27926090359687805}]}, "Id": "4b5b3e6c-57e8-4b92-afef-46e92e7f72c6", "Relationships": [{"Type": "CHILD", "Ids": ["4a61259d-ab7d-417d-a6f1-99318e2c94a5", "9bf3fdf6-b936-497d-8a7a-f6d95067b784", "45e7bf8f-9005-4fd0-ae6e-2b0b0085fc5f"]}]}, {"BlockType": "LINE", "Confidence": 99.05691528320312, "Text": "Code:", "Geometry": {"BoundingBox": {"Width": 0.05423964187502861, "Height": 0.010750575922429562, "Left": 0.14054979383945465, "Top": 0.2821986973285675}, "Polygon": [{"X": 0.14054979383945465, "Y": 0.28219884634017944}, {"X": 0.19478942453861237, "Y": 0.2821986973285675}, {"X": 0.19478942453861237, "Y": 0.2929491400718689}, {"X": 0.14054979383945465, "Y": 0.29294925928115845}]}, "Id": "8944d29e-8f25-46d7-b36d-9e734d8ec442", "Relationships": [{"Type": "CHILD", "Ids": ["378d807f-eb7e-46b0-8471-5bb161e40bf6"]}]}, {"BlockType": "LINE", "Confidence": 98.05773162841797, "Text": "TARE #:", "Geometry": {"BoundingBox": {"Width": 0.07910395413637161, "Height": 0.011893353424966335, "Left": 0.7528573870658875, "Top": 0.2824406623840332}, "Polygon": [{"X": 0.752857506275177, "Y": 0.2824408710002899}, {"X": 0.8319613933563232, "Y": 0.2824406623840332}, {"X": 0.8319612741470337, "Y": 0.294333815574646}, {"X": 0.7528573870658875, "Y": 0.2943340241909027}]}, "Id": "af4fea55-13e1-4fc2-90e0-7ab7b71d59a8", "Relationships": [{"Type": "CHILD", "Ids": ["960ca886-9b10-449b-ba1b-30c615cd2055", "fe4cd52a-84d1-4ecf-829f-5412de08274d"]}]}, {"BlockType": "LINE", "Confidence": 99.72337341308594, "Text": "29", "Geometry": {"BoundingBox": {"Width": 0.02152152918279171, "Height": 0.01107138954102993, "Left": 0.8845676183700562, "Top": 0.28288912773132324}, "Polygon": [{"X": 0.8845676779747009, "Y": 0.282889187335968}, {"X": 0.9060891270637512, "Y": 0.28288912773132324}, {"X": 0.9060890078544617, "Y": 0.29396045207977295}, {"X": 0.8845676183700562, "Y": 0.2939605116844177}]}, "Id": "29f807d9-5900-43cc-b414-6266110ca36b", "Relationships": [{"Type": "CHILD", "Ids": ["6b45ad9d-d72b-4db4-b42b-ad022593109b"]}]}, {"BlockType": "LINE", "Confidence": 99.63289642333984, "Text": "Mix Date: 09/08/2025 Batch: 1-2", "Geometry": {"BoundingBox": {"Width": 0.3865322768688202, "Height": 0.011858080513775349, "Left": 0.02277001366019249, "Top": 0.29690903425216675}, "Polygon": [{"X": 0.02277001366019249, "Y": 0.29691004753112793}, {"X": 0.409302294254303, "Y": 0.29690903425216675}, {"X": 0.4093022644519806, "Y": 0.3087661564350128}, {"X": 0.022770045325160027, "Y": 0.3087671101093292}]}, "Id": "577ce323-d147-4190-ba3a-fe63299534ec", "Relationships": [{"Type": "CHILD", "Ids": ["5c852c85-41d8-413d-aef6-536335322aeb", "09d09246-64c8-4b49-acd1-f04c4eb0a66f", "ddd8d826-38a8-4e41-86cc-b75e06e91c22", "cdf55fb2-7a38-4ccc-9152-9aa2a11f9c32", "8ddc4f21-7ec8-4775-b0f2-11a8676feacc"]}]}, {"BlockType": "LINE", "Confidence": 97.77552795410156, "Text": "GROSS #:", "Geometry": {"BoundingBox": {"Width": 0.09050679951906204, "Height": 0.012026750482618809, "Left": 0.7411400079727173, "Top": 0.29752764105796814}, "Polygon": [{"X": 0.7411401271820068, "Y": 0.29752787947654724}, {"X": 0.8316468000411987, "Y": 0.29752764105796814}, {"X": 0.831646740436554, "Y": 0.30955415964126587}, {"X": 0.7411400079727173, "Y": 0.30955439805984497}]}, "Id": "7a3d9485-0d7d-466f-bf16-5f76cd45e301", "Relationships": [{"Type": "CHILD", "Ids": ["97ddbfe7-7e58-4a4f-9f2c-2b3c26c34e43", "521965f8-b106-48a8-a581-d3827529d850"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "288", "Geometry": {"BoundingBox": {"Width": 0.03324932977557182, "Height": 0.010835977271199226, "Left": 0.8731508255004883, "Top": 0.2980085015296936}, "Polygon": [{"X": 0.8731509447097778, "Y": 0.29800859093666077}, {"X": 0.9064001441001892, "Y": 0.2980085015296936}, {"X": 0.9064000844955444, "Y": 0.3088444173336029}, {"X": 0.8731508255004883, "Y": 0.30884450674057007}]}, "Id": "31572533-bca3-4b1e-9bfd-215565e5e6cd", "Relationships": [{"Type": "CHILD", "Ids": ["8652852a-3590-42ea-aafe-07657843a2c0"]}]}, {"BlockType": "LINE", "Confidence": 98.43899536132812, "Text": "P.O.: 2025-1161-1", "Geometry": {"BoundingBox": {"Width": 0.19774195551872253, "Height": 0.011052476242184639, "Left": 0.14123280346393585, "Top": 0.3277193605899811}, "Polygon": [{"X": 0.14123280346393585, "Y": 0.3277198076248169}, {"X": 0.3389747738838196, "Y": 0.3277193605899811}, {"X": 0.3389747440814972, "Y": 0.33877140283584595}, {"X": 0.14123281836509705, "Y": 0.33877184987068176}]}, "Id": "f1deb264-0af6-4400-904d-2a96a2136684", "Relationships": [{"Type": "CHILD", "Ids": ["6dec4341-641b-48a7-8e34-e10bf414740a", "26b11297-b9a8-48f9-aaaf-36ce44c9431f"]}]}, {"BlockType": "LINE", "Confidence": 99.70198059082031, "Text": "Ames Rubber Corporation", "Geometry": {"BoundingBox": {"Width": 0.270131379365921, "Height": 0.013571728952229023, "Left": 0.03434920310974121, "Top": 0.37330785393714905}, "Polygon": [{"X": 0.03434920310974121, "Y": 0.37330833077430725}, {"X": 0.30448058247566223, "Y": 0.37330785393714905}, {"X": 0.30448055267333984, "Y": 0.3868791460990906}, {"X": 0.0343492329120636, "Y": 0.3868795931339264}]}, "Id": "1b70d575-de97-43e9-af63-cd77e81abdc0", "Relationships": [{"Type": "CHILD", "Ids": ["f93dd7c7-1cb9-4272-8d34-28958738f4e0", "c628edc0-b7e9-4359-9f4f-44ae5c8df2af", "40bed69d-1296-48b2-87f4-ac4084c60cf6"]}]}, {"BlockType": "LINE", "Confidence": 99.76363372802734, "Text": "TOTAL TARE:", "Geometry": {"BoundingBox": {"Width": 0.12626861035823822, "Height": 0.010485928505659103, "Left": 0.7053371071815491, "Top": 0.3742219805717468}, "Polygon": [{"X": 0.7053371667861938, "Y": 0.37422218918800354}, {"X": 0.8316056728363037, "Y": 0.3742219805717468}, {"X": 0.8316056132316589, "Y": 0.3847076892852783}, {"X": 0.7053371071815491, "Y": 0.38470789790153503}]}, "Id": "2a86dc39-6834-474c-a00a-f76fa755a8f9", "Relationships": [{"Type": "CHILD", "Ids": ["cd294445-84e5-4876-869a-f790f1e4a2e0", "8a03e9fa-a935-4807-a288-6c488ba653a1"]}]}, {"BlockType": "LINE", "Confidence": 99.814453125, "Text": "29", "Geometry": {"BoundingBox": {"Width": 0.02157229371368885, "Height": 0.01100540068000555, "Left": 0.884728193283081, "Top": 0.3737812042236328}, "Polygon": [{"X": 0.8847282528877258, "Y": 0.3737812638282776}, {"X": 0.9063004851341248, "Y": 0.3737812042236328}, {"X": 0.9063003659248352, "Y": 0.38478657603263855}, {"X": 0.884728193283081, "Y": 0.38478660583496094}]}, "Id": "a099a795-9fd8-416a-b5b0-d03922dfea37", "Relationships": [{"Type": "CHILD", "Ids": ["0ac4876a-e6e3-4989-a5e8-3850f7115422"]}]}, {"BlockType": "LINE", "Confidence": 90.66033935546875, "Text": "Material Certification Attached", "Geometry": {"BoundingBox": {"Width": 0.3642558753490448, "Height": 0.011632012203335762, "Left": 0.034280531108379364, "Top": 0.4037104547023773}, "Polygon": [{"X": 0.034280531108379364, "Y": 0.4037109613418579}, {"X": 0.39853641390800476, "Y": 0.4037104547023773}, {"X": 0.3985363841056824, "Y": 0.4153420329093933}, {"X": 0.03428056091070175, "Y": 0.4153424799442291}]}, "Id": "c7da2f34-d8ee-41b6-a2fc-f1b5456b9ef6", "Relationships": [{"Type": "CHILD", "Ids": ["b940ed29-4891-43cc-b9d5-8acc676d52d2", "439cbdee-6dc9-482a-bf18-b8c3d54c5173", "fd6c5cea-5f2c-4150-839a-1c63638a998b"]}]}, {"BlockType": "LINE", "Confidence": 99.8034896850586, "Text": "TOTAL GROSS:", "Geometry": {"BoundingBox": {"Width": 0.13830408453941345, "Height": 0.010540595278143883, "Left": 0.6941802501678467, "Top": 0.4048060476779938}, "Polygon": [{"X": 0.6941803097724915, "Y": 0.4048062264919281}, {"X": 0.8324843645095825, "Y": 0.4048060476779938}, {"X": 0.8324843049049377, "Y": 0.4153464734554291}, {"X": 0.6941802501678467, "Y": 0.415346622467041}]}, "Id": "2ca96430-54e3-4d9e-b46f-6b2c64ebbc2d", "Relationships": [{"Type": "CHILD", "Ids": ["2cc72da3-4109-4548-a00a-9d918de510e8", "62c3a204-2fc9-4baf-9101-11d995e8cc44"]}]}, {"BlockType": "LINE", "Confidence": 99.6045913696289, "Text": "288", "Geometry": {"BoundingBox": {"Width": 0.03223693370819092, "Height": 0.010903473943471909, "Left": 0.8733862638473511, "Top": 0.4041858911514282}, "Polygon": [{"X": 0.8733863830566406, "Y": 0.4041859209537506}, {"X": 0.905623197555542, "Y": 0.4041858911514282}, {"X": 0.9056231379508972, "Y": 0.41508930921554565}, {"X": 0.8733862638473511, "Y": 0.41508936882019043}]}, "Id": "4dc65811-c97a-4a22-bee2-3b1454731882", "Relationships": [{"Type": "CHILD", "Ids": ["2840dbf4-b50f-4a47-81c9-7bd96c34c3ad"]}]}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "PACKING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.081280417740345, "Height": 0.010538163594901562, "Left": 0.36531582474708557, "Top": 0.08433611690998077}, "Polygon": [{"X": 0.36531585454940796, "Y": 0.0843365341424942}, {"X": 0.44659626483917236, "Y": 0.08433611690998077}, {"X": 0.44659623503685, "Y": 0.09487386792898178}, {"X": 0.36531582474708557, "Y": 0.09487427771091461}]}, "Id": "d37c74d5-4937-4ed3-ae1b-5b4da72d3ddc"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "LIST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04606577381491661, "Height": 0.010301033034920692, "Left": 0.45914146304130554, "Top": 0.08489115536212921}, "Polygon": [{"X": 0.45914149284362793, "Y": 0.08489139378070831}, {"X": 0.5052072405815125, "Y": 0.08489115536212921}, {"X": 0.5052071809768677, "Y": 0.09519195556640625}, {"X": 0.45914146304130554, "Y": 0.09519218653440475}]}, "Id": "be397993-3044-4506-a04e-45001605c6fe"}, {"BlockType": "WORD", "Confidence": 99.41326141357422, "Text": "B/L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033929962664842606, "Height": 0.012366143055260181, "Left": 0.6954561471939087, "Top": 0.08447472751140594}, "Polygon": [{"X": 0.6954562067985535, "Y": 0.08447489887475967}, {"X": 0.7293860912322998, "Y": 0.08447472751140594}, {"X": 0.729386031627655, "Y": 0.09684070199728012}, {"X": 0.6954561471939087, "Y": 0.09684087336063385}]}, "Id": "7fe9b689-46c3-4a43-afa3-2ada67e41fa4"}, {"BlockType": "WORD", "Confidence": 97.3692626953125, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019555268809199333, "Height": 0.011917004361748695, "Left": 0.7423679828643799, "Top": 0.08485052734613419}, "Polygon": [{"X": 0.7423681020736694, "Y": 0.08485062420368195}, {"X": 0.7619232535362244, "Y": 0.08485052734613419}, {"X": 0.7619231939315796, "Y": 0.09676743298768997}, {"X": 0.7423679828643799, "Y": 0.09676752984523773}]}, "Id": "478db9b5-09b6-48bd-85ee-59a437bd0df2"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "155734", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06825215369462967, "Height": 0.01130167581140995, "Left": 0.7785965204238892, "Top": 0.08514711260795593}, "Polygon": [{"X": 0.7785965800285339, "Y": 0.08514746278524399}, {"X": 0.8468486666679382, "Y": 0.08514711260795593}, {"X": 0.8468485474586487, "Y": 0.09644844383001328}, {"X": 0.7785965204238892, "Y": 0.09644878655672073}]}, "Id": "2d173e51-ecc9-4bb6-9fab-4ff0768ca337"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04534810036420822, "Height": 0.012807958759367466, "Left": 0.8850861191749573, "Top": 0.08634371310472488}, "Polygon": [{"X": 0.8850862383842468, "Y": 0.08634394407272339}, {"X": 0.9304342269897461, "Y": 0.08634371310472488}, {"X": 0.9304341077804565, "Y": 0.09915144741535187}, {"X": 0.8850861191749573, "Y": 0.09915167093276978}]}, "Id": "26597753-ef92-42fc-94c2-6ee9af02aa89"}, {"BlockType": "WORD", "Confidence": 98.50505828857422, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008955003693699837, "Height": 0.011086978018283844, "Left": 0.9563589096069336, "Top": 0.08603980392217636}, "Polygon": [{"X": 0.9563590288162231, "Y": 0.08603985607624054}, {"X": 0.9653139114379883, "Y": 0.08603980392217636}, {"X": 0.9653138518333435, "Y": 0.09712673723697662}, {"X": 0.9563589096069336, "Y": 0.0971267819404602}]}, "Id": "0d25cfc8-eba8-4bb0-8d72-366669ca81c4"}, {"BlockType": "WORD", "Confidence": 97.90676879882812, "Text": "Consignee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11395489424467087, "Height": 0.013315297663211823, "Left": 0.36429363489151, "Top": 0.11486790329217911}, "Polygon": [{"X": 0.3642936646938324, "Y": 0.11486844718456268}, {"X": 0.4782485365867615, "Y": 0.11486790329217911}, {"X": 0.4782484769821167, "Y": 0.12818266451358795}, {"X": 0.36429363489151, "Y": 0.12818320095539093}]}, "Id": "854ec8de-510a-4a56-904d-10b5083a2cfc"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "0280", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0450289361178875, "Height": 0.010807805694639683, "Left": 0.5653091669082642, "Top": 0.11534329503774643}, "Polygon": [{"X": 0.5653092265129089, "Y": 0.11534351110458374}, {"X": 0.6103380918502808, "Y": 0.11534329503774643}, {"X": 0.6103380918502808, "Y": 0.12615089118480682}, {"X": 0.5653091669082642, "Y": 0.12615109980106354}]}, "Id": "15a00379-008d-4e2e-8495-ca1bedfbe30f"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "HEXPOL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0693126916885376, "Height": 0.010620378889143467, "Left": 0.047401707619428635, "Top": 0.12872615456581116}, "Polygon": [{"X": 0.047401707619428635, "Y": 0.12872646749019623}, {"X": 0.11671438813209534, "Y": 0.12872615456581116}, {"X": 0.11671440303325653, "Y": 0.13934621214866638}, {"X": 0.047401733696460724, "Y": 0.13934652507305145}]}, "Id": "b6e45209-e55d-4b94-b830-3d4c2acf21fd"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Middlefield", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12963974475860596, "Height": 0.011240112595260143, "Left": 0.12813442945480347, "Top": 0.12877383828163147}, "Polygon": [{"X": 0.12813442945480347, "Y": 0.12877443432807922}, {"X": 0.2577741742134094, "Y": 0.12877383828163147}, {"X": 0.2577741742134094, "Y": 0.14001336693763733}, {"X": 0.12813444435596466, "Y": 0.1400139480829239}]}, "Id": "426552e4-c14a-4907-ab19-597492b3212d"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Ames", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046414565294981, "Height": 0.010510735213756561, "Left": 0.3637791574001312, "Top": 0.13024549186229706}, "Polygon": [{"X": 0.3637791574001312, "Y": 0.13024570047855377}, {"X": 0.41019371151924133, "Y": 0.13024549186229706}, {"X": 0.41019368171691895, "Y": 0.1407560110092163}, {"X": 0.3637791574001312, "Y": 0.14075621962547302}]}, "Id": "db9bb598-8300-46d9-8e93-9ea433c83a45"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06943321973085403, "Height": 0.010861393995583057, "Left": 0.4234282374382019, "Top": 0.13010990619659424}, "Polygon": [{"X": 0.4234282672405243, "Y": 0.1301102340221405}, {"X": 0.49286144971847534, "Y": 0.13010990619659424}, {"X": 0.49286141991615295, "Y": 0.14097099006175995}, {"X": 0.4234282374382019, "Y": 0.14097130298614502}]}, "Id": "aed2a9d6-99f3-4268-86db-12d806e55f1f"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Corporation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12827621400356293, "Height": 0.013001143001019955, "Left": 0.5058842301368713, "Top": 0.13079208135604858}, "Polygon": [{"X": 0.5058842897415161, "Y": 0.13079267740249634}, {"X": 0.6341604590415955, "Y": 0.13079208135604858}, {"X": 0.6341603994369507, "Y": 0.14379265904426575}, {"X": 0.5058842301368713, "Y": 0.1437932252883911}]}, "Id": "3336ed5d-32fb-4223-abdb-f80e0c96680c"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "14910", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05660437047481537, "Height": 0.01126080472022295, "Left": 0.04802773520350456, "Top": 0.1435111165046692}, "Polygon": [{"X": 0.04802773520350456, "Y": 0.14351136982440948}, {"X": 0.10463208705186844, "Y": 0.1435111165046692}, {"X": 0.10463210940361023, "Y": 0.15477167069911957}, {"X": 0.04802776128053665, "Y": 0.15477190911769867}]}, "Id": "952bca84-4651-4320-8d72-dc209a1ee1ea"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "Madison", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08240849524736404, "Height": 0.011036811396479607, "Left": 0.11671420931816101, "Top": 0.14401443302631378}, "Polygon": [{"X": 0.11671420931816101, "Y": 0.14401480555534363}, {"X": 0.19912269711494446, "Y": 0.14401443302631378}, {"X": 0.19912269711494446, "Y": 0.15505088865756989}, {"X": 0.1167142242193222, "Y": 0.15505124628543854}]}, "Id": "47e66fa5-063e-46b0-a98d-922686419988"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04678436368703842, "Height": 0.010703643783926964, "Left": 0.21129679679870605, "Top": 0.1447085291147232}, "Polygon": [{"X": 0.21129679679870605, "Y": 0.14470873773097992}, {"X": 0.2580811679363251, "Y": 0.1447085291147232}, {"X": 0.2580811679363251, "Y": 0.155411958694458}, {"X": 0.21129679679870605, "Y": 0.15541216731071472}]}, "Id": "c1de89ef-7251-4cd8-8d72-0d5f0e23dd1a"}, {"BlockType": "WORD", "Confidence": 99.15397644042969, "Text": "Middlefield,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1368868052959442, "Height": 0.013225621543824673, "Left": 0.0465041846036911, "Top": 0.15874245762825012}, "Polygon": [{"X": 0.0465041846036911, "Y": 0.15874303877353668}, {"X": 0.18339097499847412, "Y": 0.15874245762825012}, {"X": 0.18339098989963531, "Y": 0.1719675064086914}, {"X": 0.04650421440601349, "Y": 0.17196807265281677}]}, "Id": "c46d4ab7-8742-421d-918e-f661590b7d9f"}, {"BlockType": "WORD", "Confidence": 99.73394012451172, "Text": "Ohio", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04650492966175079, "Height": 0.01084946095943451, "Left": 0.1995278298854828, "Top": 0.159732848405838}, "Polygon": [{"X": 0.1995278298854828, "Y": 0.15973304212093353}, {"X": 0.24603275954723358, "Y": 0.159732848405838}, {"X": 0.24603275954723358, "Y": 0.170582115650177}, {"X": 0.1995278298854828, "Y": 0.17058230936527252}]}, "Id": "42036689-4d3b-46fa-97af-683138933554"}, {"BlockType": "WORD", "Confidence": 99.79332733154297, "Text": "44062", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05615505203604698, "Height": 0.011069057509303093, "Left": 0.2593036890029907, "Top": 0.1598733365535736}, "Polygon": [{"X": 0.2593036890029907, "Y": 0.1598735749721527}, {"X": 0.315458744764328, "Y": 0.1598733365535736}, {"X": 0.3154587149620056, "Y": 0.17094215750694275}, {"X": 0.2593036890029907, "Y": 0.17094239592552185}]}, "Id": "9e4c5b5f-b980-4f34-9218-80c64d932ba8"}, {"BlockType": "WORD", "Confidence": 99.63787078857422, "Text": "19", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021036706864833832, "Height": 0.010860089212656021, "Left": 0.3653525114059448, "Top": 0.16020797193050385}, "Polygon": [{"X": 0.3653525412082672, "Y": 0.160208061337471}, {"X": 0.38638922572135925, "Y": 0.16020797193050385}, {"X": 0.38638919591903687, "Y": 0.1710679680109024}, {"X": 0.3653525114059448, "Y": 0.17106805741786957}]}, "Id": "d9b3865c-a55d-40f9-bfc7-1b5707d2f1de"}, {"BlockType": "WORD", "Confidence": 99.20440673828125, "Text": "Ames", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04651350528001785, "Height": 0.009439573623239994, "Left": 0.39871668815612793, "Top": 0.16104808449745178}, "Polygon": [{"X": 0.39871668815612793, "Y": 0.1610482782125473}, {"X": 0.4452301859855652, "Y": 0.16104808449745178}, {"X": 0.4452301561832428, "Y": 0.17048746347427368}, {"X": 0.39871668815612793, "Y": 0.1704876571893692}]}, "Id": "f1987909-5836-418f-a1f9-8b4d4904091d"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "Boulevard", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1054334044456482, "Height": 0.011019368655979633, "Left": 0.4583907425403595, "Top": 0.16074202954769135}, "Polygon": [{"X": 0.4583907723426819, "Y": 0.16074247658252716}, {"X": 0.5638241767883301, "Y": 0.16074202954769135}, {"X": 0.5638241171836853, "Y": 0.17176096141338348}, {"X": 0.4583907425403595, "Y": 0.1717613935470581}]}, "Id": "c645ed95-7922-4452-bbdb-8a5c56d26b10"}, {"BlockType": "WORD", "Confidence": 99.51271057128906, "Text": "Hamburg,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08854296058416367, "Height": 0.013643848709762096, "Left": 0.3645101487636566, "Top": 0.1906938999891281}, "Polygon": [{"X": 0.364510178565979, "Y": 0.19069424271583557}, {"X": 0.4530531167984009, "Y": 0.1906938999891281}, {"X": 0.4530530869960785, "Y": 0.20433741807937622}, {"X": 0.3645101487636566, "Y": 0.20433774590492249}]}, "Id": "0394abc0-4e90-431b-a54f-b8889586a400"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "NJ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0241374708712101, "Height": 0.010516738519072533, "Left": 0.46972396969795227, "Top": 0.19150733947753906}, "Polygon": [{"X": 0.46972399950027466, "Y": 0.19150742888450623}, {"X": 0.49386143684387207, "Y": 0.19150733947753906}, {"X": 0.4938614070415497, "Y": 0.20202398300170898}, {"X": 0.46972396969795227, "Y": 0.20202407240867615}]}, "Id": "4db95098-914b-497e-adeb-ef51b66f11e0"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "07419", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05616237595677376, "Height": 0.011100790463387966, "Left": 0.5064401626586914, "Top": 0.1909651756286621}, "Polygon": [{"X": 0.5064401626586914, "Y": 0.19096539914608002}, {"X": 0.5626025199890137, "Y": 0.1909651756286621}, {"X": 0.5626024603843689, "Y": 0.20206575095653534}, {"X": 0.5064401626586914, "Y": 0.20206595957279205}]}, "Id": "819bdb26-0640-47fc-8577-df21d20bd994"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045284077525138855, "Height": 0.01126953400671482, "Left": 0.023403842002153397, "Top": 0.20441849529743195}, "Polygon": [{"X": 0.023403842002153397, "Y": 0.20441865921020508}, {"X": 0.06868790090084076, "Y": 0.20441849529743195}, {"X": 0.06868792325258255, "Y": 0.2156878560781479}, {"X": 0.023403871804475784, "Y": 0.21568801999092102}]}, "Id": "ce88f0cc-c186-45fa-aa9a-f39730374aa0"}, {"BlockType": "WORD", "Confidence": 99.41246795654297, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031296733766794205, "Height": 0.010793068446218967, "Left": 0.08174625039100647, "Top": 0.2053314596414566}, "Polygon": [{"X": 0.08174625039100647, "Y": 0.20533157885074615}, {"X": 0.11304296553134918, "Y": 0.2053314596414566}, {"X": 0.11304298043251038, "Y": 0.21612441539764404}, {"X": 0.08174626529216766, "Y": 0.2161245346069336}]}, "Id": "5a6b9550-4cdc-45d9-a825-90135ddef1c4"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "Ames", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04632725566625595, "Height": 0.010581225156784058, "Left": 0.1283254325389862, "Top": 0.20571710169315338}, "Polygon": [{"X": 0.1283254325389862, "Y": 0.2057172805070877}, {"X": 0.17465269565582275, "Y": 0.20571710169315338}, {"X": 0.17465269565582275, "Y": 0.2162981629371643}, {"X": 0.1283254474401474, "Y": 0.21629832684993744}]}, "Id": "51955997-154a-4472-b77d-e4125f359d37"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06947264075279236, "Height": 0.011040989309549332, "Left": 0.18767444789409637, "Top": 0.20545515418052673}, "Polygon": [{"X": 0.18767444789409637, "Y": 0.20545540750026703}, {"X": 0.2571471035480499, "Y": 0.20545515418052673}, {"X": 0.25714707374572754, "Y": 0.21649588644504547}, {"X": 0.18767446279525757, "Y": 0.21649613976478577}]}, "Id": "9ab4e501-13f6-468d-8983-ed2767b83c2a"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "Corporation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12881359457969666, "Height": 0.013284300453960896, "Left": 0.2697370648384094, "Top": 0.20608505606651306}, "Polygon": [{"X": 0.2697370648384094, "Y": 0.20608553290367126}, {"X": 0.3985506594181061, "Y": 0.20608505606651306}, {"X": 0.3985506296157837, "Y": 0.21936888992786407}, {"X": 0.2697370648384094, "Y": 0.21936935186386108}]}, "Id": "9db6dcd4-ee70-4c4d-ac2b-ca5f33be361b"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04559407755732536, "Height": 0.01335784513503313, "Left": 0.023981843143701553, "Top": 0.23556192219257355}, "Polygon": [{"X": 0.023981843143701553, "Y": 0.23556208610534668}, {"X": 0.06957589834928513, "Y": 0.23556192219257355}, {"X": 0.06957592070102692, "Y": 0.24891962110996246}, {"X": 0.02398187853395939, "Y": 0.2489197701215744}]}, "Id": "2a29a2ed-8d26-4afd-810b-c8b16b7a61f3"}, {"BlockType": "WORD", "Confidence": 94.36284637451172, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05314260348677635, "Height": 0.01080293208360672, "Left": 0.08262355625629425, "Top": 0.23609870672225952}, "Polygon": [{"X": 0.08262355625629425, "Y": 0.23609888553619385}, {"X": 0.1357661485671997, "Y": 0.23609870672225952}, {"X": 0.1357661634683609, "Y": 0.2469014674425125}, {"X": 0.08262357860803604, "Y": 0.24690164625644684}]}, "Id": "36eba661-dd8d-482a-8df6-06906d04ab3c"}, {"BlockType": "WORD", "Confidence": 98.056640625, "Text": "09/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11345876008272171, "Height": 0.012489001266658306, "Left": 0.14257507026195526, "Top": 0.23527586460113525}, "Polygon": [{"X": 0.14257507026195526, "Y": 0.2352762371301651}, {"X": 0.25603383779525757, "Y": 0.23527586460113525}, {"X": 0.2560338079929352, "Y": 0.2477644979953766}, {"X": 0.14257508516311646, "Y": 0.24776487052440643}]}, "Id": "9167eeca-4f22-48b3-8044-323b5ecdac3d"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "SHIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04589485004544258, "Height": 0.0104362266138196, "Left": 0.02398800291121006, "Top": 0.251285195350647}, "Polygon": [{"X": 0.02398800291121006, "Y": 0.2512853443622589}, {"X": 0.069882832467556, "Y": 0.251285195350647}, {"X": 0.06988285481929779, "Y": 0.26172128319740295}, {"X": 0.0239880308508873, "Y": 0.2617214322090149}]}, "Id": "b3965690-a298-49be-8fc5-ec8901bd40dd"}, {"BlockType": "WORD", "Confidence": 99.56712341308594, "Text": "VIA:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04324083402752876, "Height": 0.010727482847869396, "Left": 0.08166881650686264, "Top": 0.251462459564209}, "Polygon": [{"X": 0.08166881650686264, "Y": 0.2514626085758209}, {"X": 0.12490963190793991, "Y": 0.251462459564209}, {"X": 0.1249096468091011, "Y": 0.2621898353099823}, {"X": 0.08166883140802383, "Y": 0.26218995451927185}]}, "Id": "186d2fa8-753d-4127-b278-27272c3e6f5f"}, {"BlockType": "WORD", "Confidence": 98.60630798339844, "Text": "Email:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06638091057538986, "Height": 0.011070232838392258, "Left": 0.1641867309808731, "Top": 0.2514285147190094}, "Polygon": [{"X": 0.1641867309808731, "Y": 0.2514287233352661}, {"X": 0.23056763410568237, "Y": 0.2514285147190094}, {"X": 0.23056763410568237, "Y": 0.26249855756759644}, {"X": 0.1641867458820343, "Y": 0.26249873638153076}]}, "Id": "2ce70457-35ff-4203-8ec4-6407e0c7d320"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3165089786052704, "Height": 0.014174902811646461, "Left": 0.2485710084438324, "Top": 0.2517746686935425}, "Polygon": [{"X": 0.2485710084438324, "Y": 0.25177568197250366}, {"X": 0.5650799870491028, "Y": 0.2517746686935425}, {"X": 0.565079927444458, "Y": 0.265948623418808}, {"X": 0.2485710084438324, "Y": 0.2659495770931244}]}, "Id": "e7ef4a9a-a921-4a8f-8238-ac7f07cf2427"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "613913", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06737156212329865, "Height": 0.011166487820446491, "Left": 0.02478020079433918, "Top": 0.2660055458545685}, "Polygon": [{"X": 0.02478020079433918, "Y": 0.2660057544708252}, {"X": 0.09215174615383148, "Y": 0.2660055458545685}, {"X": 0.09215176105499268, "Y": 0.2771718502044678}, {"X": 0.024780230596661568, "Y": 0.2771720290184021}]}, "Id": "6f48d2f0-941c-435b-951d-0f40b2f3bdb1"}, {"BlockType": "WORD", "Confidence": 98.349609375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008865234442055225, "Height": 0.010806073434650898, "Left": 0.11817214637994766, "Top": 0.26658713817596436}, "Polygon": [{"X": 0.11817214637994766, "Y": 0.26658716797828674}, {"X": 0.12703736126422882, "Y": 0.26658713817596436}, {"X": 0.12703737616539001, "Y": 0.2773931920528412}, {"X": 0.11817216128110886, "Y": 0.2773932218551636}]}, "Id": "0cc86997-e3ea-4f48-b892-f50ab4d21b8a"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "SKIDS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056859057396650314, "Height": 0.010617190971970558, "Left": 0.14123140275478363, "Top": 0.2670901417732239}, "Polygon": [{"X": 0.14123140275478363, "Y": 0.2670903205871582}, {"X": 0.19809046387672424, "Y": 0.2670901417732239}, {"X": 0.19809046387672424, "Y": 0.27770715951919556}, {"X": 0.14123141765594482, "Y": 0.2777073383331299}]}, "Id": "04bc63cf-282e-4fc2-85fa-5f4211548f1e"}, {"BlockType": "WORD", "Confidence": 99.30584716796875, "Text": "CPD9420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08109938353300095, "Height": 0.010864943265914917, "Left": 0.2698536217212677, "Top": 0.2670610845088959}, "Polygon": [{"X": 0.2698536217212677, "Y": 0.267061322927475}, {"X": 0.35095301270484924, "Y": 0.2670610845088959}, {"X": 0.35095298290252686, "Y": 0.2779257893562317}, {"X": 0.2698536217212677, "Y": 0.2779260277748108}]}, "Id": "d0c10ba7-c405-4fe4-b928-0db462c7623a"}, {"BlockType": "WORD", "Confidence": 95.71049499511719, "Text": "A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01202275138348341, "Height": 0.010258033871650696, "Left": 0.36358264088630676, "Top": 0.2677440941333771}, "Polygon": [{"X": 0.36358267068862915, "Y": 0.26774412393569946}, {"X": 0.37560540437698364, "Y": 0.2677440941333771}, {"X": 0.37560537457466125, "Y": 0.2780020833015442}, {"X": 0.36358264088630676, "Y": 0.2780021131038666}]}, "Id": "ae93576c-c781-4d21-a013-7797635f64dc"}, {"BlockType": "WORD", "Confidence": 96.48278045654297, "Text": "(M)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025482935830950737, "Height": 0.012154926545917988, "Left": 0.3803032338619232, "Top": 0.2674441933631897}, "Polygon": [{"X": 0.3803032636642456, "Y": 0.2674442529678345}, {"X": 0.4057861864566803, "Y": 0.2674441933631897}, {"X": 0.4057861566543579, "Y": 0.27959904074668884}, {"X": 0.3803032338619232, "Y": 0.279599130153656}]}, "Id": "648f5024-64f5-48f7-97fc-d693e6af8b11"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03452064469456673, "Height": 0.010148593224585056, "Left": 0.7648095488548279, "Top": 0.2683812379837036}, "Polygon": [{"X": 0.7648096084594727, "Y": 0.2683813273906708}, {"X": 0.7993301749229431, "Y": 0.2683812379837036}, {"X": 0.7993301153182983, "Y": 0.27852973341941833}, {"X": 0.7648095488548279, "Y": 0.2785298228263855}]}, "Id": "4a61259d-ab7d-417d-a6f1-99318e2c94a5"}, {"BlockType": "WORD", "Confidence": 96.81122589111328, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01936986669898033, "Height": 0.011823556385934353, "Left": 0.812652587890625, "Top": 0.267437219619751}, "Polygon": [{"X": 0.8126526474952698, "Y": 0.26743727922439575}, {"X": 0.8320224285125732, "Y": 0.267437219619751}, {"X": 0.8320223689079285, "Y": 0.2792607247829437}, {"X": 0.812652587890625, "Y": 0.2792607843875885}]}, "Id": "9bf3fdf6-b936-497d-8a7a-f6d95067b784"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "259", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033310987055301666, "Height": 0.010829729028046131, "Left": 0.8732448816299438, "Top": 0.2678245007991791}, "Polygon": [{"X": 0.8732449412345886, "Y": 0.26782459020614624}, {"X": 0.9065558314323425, "Y": 0.2678245007991791}, {"X": 0.9065557718276978, "Y": 0.2786541283130646}, {"X": 0.8732448816299438, "Y": 0.27865421772003174}]}, "Id": "45e7bf8f-9005-4fd0-ae6e-2b0b0085fc5f"}, {"BlockType": "WORD", "Confidence": 99.05691528320312, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05423964187502861, "Height": 0.010750575922429562, "Left": 0.14054979383945465, "Top": 0.2821986973285675}, "Polygon": [{"X": 0.14054979383945465, "Y": 0.28219884634017944}, {"X": 0.19478942453861237, "Y": 0.2821986973285675}, {"X": 0.19478942453861237, "Y": 0.2929491400718689}, {"X": 0.14054979383945465, "Y": 0.29294925928115845}]}, "Id": "378d807f-eb7e-46b0-8471-5bb161e40bf6"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "TARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04635526239871979, "Height": 0.010300788097083569, "Left": 0.7528574466705322, "Top": 0.2833234965801239}, "Polygon": [{"X": 0.752857506275177, "Y": 0.28332361578941345}, {"X": 0.7992126941680908, "Y": 0.2833234965801239}, {"X": 0.799212634563446, "Y": 0.2936241626739502}, {"X": 0.7528574466705322, "Y": 0.29362428188323975}]}, "Id": "960ca886-9b10-449b-ba1b-30c615cd2055"}, {"BlockType": "WORD", "Confidence": 96.69164276123047, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019322920590639114, "Height": 0.011893193237483501, "Left": 0.8126384615898132, "Top": 0.2824406623840332}, "Polygon": [{"X": 0.812638521194458, "Y": 0.282440721988678}, {"X": 0.8319613933563232, "Y": 0.2824406623840332}, {"X": 0.8319612741470337, "Y": 0.294333815574646}, {"X": 0.8126384615898132, "Y": 0.2943338453769684}]}, "Id": "fe4cd52a-84d1-4ecf-829f-5412de08274d"}, {"BlockType": "WORD", "Confidence": 99.72337341308594, "Text": "29", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02152152918279171, "Height": 0.01107138954102993, "Left": 0.8845676183700562, "Top": 0.28288912773132324}, "Polygon": [{"X": 0.8845676779747009, "Y": 0.282889187335968}, {"X": 0.9060891270637512, "Y": 0.28288912773132324}, {"X": 0.9060890078544617, "Y": 0.29396045207977295}, {"X": 0.8845676183700562, "Y": 0.2939605116844177}]}, "Id": "6b45ad9d-d72b-4db4-b42b-ad022593109b"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "Mix", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034829530864953995, "Height": 0.010811857879161835, "Left": 0.02277001366019249, "Top": 0.29690995812416077}, "Polygon": [{"X": 0.02277001366019249, "Y": 0.29691004753112793}, {"X": 0.057599522173404694, "Y": 0.29690995812416077}, {"X": 0.057599544525146484, "Y": 0.30772173404693604}, {"X": 0.022770041599869728, "Y": 0.3077218234539032}]}, "Id": "5c852c85-41d8-413d-aef6-536335322aeb"}, {"BlockType": "WORD", "Confidence": 99.60618591308594, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053763795644044876, "Height": 0.010730749927461147, "Left": 0.07102341204881668, "Top": 0.2974517047405243}, "Polygon": [{"X": 0.07102341204881668, "Y": 0.29745185375213623}, {"X": 0.12478718906641006, "Y": 0.2974517047405243}, {"X": 0.12478720396757126, "Y": 0.30818232893943787}, {"X": 0.07102343440055847, "Y": 0.3081824481487274}]}, "Id": "09d09246-64c8-4b49-acd1-f04c4eb0a66f"}, {"BlockType": "WORD", "Confidence": 99.75347137451172, "Text": "09/08/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11509717255830765, "Height": 0.011664406396448612, "Left": 0.15318335592746735, "Top": 0.2970173954963684}, "Polygon": [{"X": 0.15318335592746735, "Y": 0.2970176935195923}, {"X": 0.2682805061340332, "Y": 0.2970173954963684}, {"X": 0.2682805061340332, "Y": 0.30868151783943176}, {"X": 0.15318335592746735, "Y": 0.30868181586265564}]}, "Id": "ddd8d826-38a8-4e41-86cc-b75e06e91c22"}, {"BlockType": "WORD", "Confidence": 99.21735382080078, "Text": "Batch:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06662734597921371, "Height": 0.01081846747547388, "Left": 0.29337090253829956, "Top": 0.29785314202308655}, "Polygon": [{"X": 0.29337090253829956, "Y": 0.2978533208370209}, {"X": 0.35999825596809387, "Y": 0.29785314202308655}, {"X": 0.3599982261657715, "Y": 0.3086714446544647}, {"X": 0.29337090253829956, "Y": 0.30867162346839905}]}, "Id": "cdf55fb2-7a38-4ccc-9152-9aa2a11f9c32"}, {"BlockType": "WORD", "Confidence": 99.73394012451172, "Text": "1-2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0327482596039772, "Height": 0.011195329017937183, "Left": 0.376554012298584, "Top": 0.2975709140300751}, "Polygon": [{"X": 0.37655404210090637, "Y": 0.29757100343704224}, {"X": 0.409302294254303, "Y": 0.2975709140300751}, {"X": 0.4093022644519806, "Y": 0.3087661564350128}, {"X": 0.376554012298584, "Y": 0.30876624584198}]}, "Id": "8ddc4f21-7ec8-4775-b0f2-11a8676feacc"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058329857885837555, "Height": 0.010595687665045261, "Left": 0.7411400079727173, "Top": 0.2983379364013672}, "Polygon": [{"X": 0.7411401271820068, "Y": 0.2983381152153015}, {"X": 0.7994698882102966, "Y": 0.2983379364013672}, {"X": 0.7994698286056519, "Y": 0.3089334964752197}, {"X": 0.7411400079727173, "Y": 0.30893364548683167}]}, "Id": "97ddbfe7-7e58-4a4f-9f2c-2b3c26c34e43"}, {"BlockType": "WORD", "Confidence": 95.81473541259766, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01886824332177639, "Height": 0.012026572600007057, "Left": 0.8127785921096802, "Top": 0.29752764105796814}, "Polygon": [{"X": 0.812778651714325, "Y": 0.2975277006626129}, {"X": 0.8316468000411987, "Y": 0.29752764105796814}, {"X": 0.831646740436554, "Y": 0.30955415964126587}, {"X": 0.8127785921096802, "Y": 0.30955421924591064}]}, "Id": "521965f8-b106-48a8-a581-d3827529d850"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "288", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03324932977557182, "Height": 0.010835977271199226, "Left": 0.8731508255004883, "Top": 0.2980085015296936}, "Polygon": [{"X": 0.8731509447097778, "Y": 0.29800859093666077}, {"X": 0.9064001441001892, "Y": 0.2980085015296936}, {"X": 0.9064000844955444, "Y": 0.3088444173336029}, {"X": 0.8731508255004883, "Y": 0.30884450674057007}]}, "Id": "8652852a-3590-42ea-aafe-07657843a2c0"}, {"BlockType": "WORD", "Confidence": 99.04336547851562, "Text": "P.O.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05393243208527565, "Height": 0.0109443049877882, "Left": 0.14123280346393585, "Top": 0.3278275430202484}, "Polygon": [{"X": 0.14123280346393585, "Y": 0.32782766222953796}, {"X": 0.1951652318239212, "Y": 0.3278275430202484}, {"X": 0.1951652318239212, "Y": 0.3387717306613922}, {"X": 0.14123281836509705, "Y": 0.33877184987068176}]}, "Id": "6dec4341-641b-48a7-8e34-e10bf414740a"}, {"BlockType": "WORD", "Confidence": 97.83462524414062, "Text": "2025-1161-1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12794850766658783, "Height": 0.01094311848282814, "Left": 0.21102625131607056, "Top": 0.3277193605899811}, "Polygon": [{"X": 0.21102625131607056, "Y": 0.32771965861320496}, {"X": 0.3389747738838196, "Y": 0.3277193605899811}, {"X": 0.3389747440814972, "Y": 0.33866220712661743}, {"X": 0.21102625131607056, "Y": 0.3386624753475189}]}, "Id": "26b11297-b9a8-48f9-aaaf-36ce44c9431f"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "Ames", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0459994412958622, "Height": 0.010704979300498962, "Left": 0.03434920310974121, "Top": 0.37333551049232483}, "Polygon": [{"X": 0.03434920310974121, "Y": 0.373335599899292}, {"X": 0.08034862577915192, "Y": 0.37333551049232483}, {"X": 0.08034864068031311, "Y": 0.3840404152870178}, {"X": 0.0343492291867733, "Y": 0.3840404748916626}]}, "Id": "f93dd7c7-1cb9-4272-8d34-28958738f4e0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06923351436853409, "Height": 0.01102381944656372, "Left": 0.09374185651540756, "Top": 0.37330809235572815}, "Polygon": [{"X": 0.09374185651540756, "Y": 0.3733082115650177}, {"X": 0.16297535598278046, "Y": 0.37330809235572815}, {"X": 0.16297537088394165, "Y": 0.3843317925930023}, {"X": 0.09374187141656876, "Y": 0.38433191180229187}]}, "Id": "c628edc0-b7e9-4359-9f4f-44ae5c8df2af"}, {"BlockType": "WORD", "Confidence": 99.3126220703125, "Text": "Corporation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12883605062961578, "Height": 0.013369412161409855, "Left": 0.17564451694488525, "Top": 0.37350994348526}, "Polygon": [{"X": 0.17564451694488525, "Y": 0.3735101819038391}, {"X": 0.30448058247566223, "Y": 0.37350994348526}, {"X": 0.30448055267333984, "Y": 0.3868791460990906}, {"X": 0.17564453184604645, "Y": 0.3868793547153473}]}, "Id": "40bed69d-1296-48b2-87f4-ac4084c60cf6"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05901355296373367, "Height": 0.010485812090337276, "Left": 0.7053371071815491, "Top": 0.374222069978714}, "Polygon": [{"X": 0.7053371667861938, "Y": 0.37422218918800354}, {"X": 0.7643506526947021, "Y": 0.374222069978714}, {"X": 0.7643505930900574, "Y": 0.38470780849456787}, {"X": 0.7053371071815491, "Y": 0.38470789790153503}]}, "Id": "cd294445-84e5-4876-869a-f790f1e4a2e0"}, {"BlockType": "WORD", "Confidence": 99.59562683105469, "Text": "TARE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055108245462179184, "Height": 0.010377989150583744, "Left": 0.7764974236488342, "Top": 0.37427854537963867}, "Polygon": [{"X": 0.7764975428581238, "Y": 0.37427863478660583}, {"X": 0.8316056728363037, "Y": 0.37427854537963867}, {"X": 0.8316056132316589, "Y": 0.38465645909309387}, {"X": 0.7764974236488342, "Y": 0.38465654850006104}]}, "Id": "8a03e9fa-a935-4807-a288-6c488ba653a1"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "29", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02157229371368885, "Height": 0.01100540068000555, "Left": 0.884728193283081, "Top": 0.3737812042236328}, "Polygon": [{"X": 0.8847282528877258, "Y": 0.3737812638282776}, {"X": 0.9063004851341248, "Y": 0.3737812042236328}, {"X": 0.9063003659248352, "Y": 0.38478657603263855}, {"X": 0.884728193283081, "Y": 0.38478660583496094}]}, "Id": "0ac4876a-e6e3-4989-a5e8-3850f7115422"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Material", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0925595611333847, "Height": 0.01114811934530735, "Left": 0.034280531108379364, "Top": 0.40371084213256836}, "Polygon": [{"X": 0.034280531108379364, "Y": 0.4037109613418579}, {"X": 0.12684008479118347, "Y": 0.40371084213256836}, {"X": 0.12684009969234467, "Y": 0.4148588478565216}, {"X": 0.03428056091070175, "Y": 0.41485896706581116}]}, "Id": "b940ed29-4891-43cc-b9d5-8acc676d52d2"}, {"BlockType": "WORD", "Confidence": 72.22835540771484, "Text": "Certification", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15214504301548004, "Height": 0.011079302988946438, "Left": 0.1402856707572937, "Top": 0.4041200876235962}, "Polygon": [{"X": 0.1402856707572937, "Y": 0.4041202962398529}, {"X": 0.29243072867393494, "Y": 0.4041200876235962}, {"X": 0.29243069887161255, "Y": 0.4151992201805115}, {"X": 0.1402856856584549, "Y": 0.4151993989944458}]}, "Id": "439cbdee-6dc9-482a-bf18-b8c3d54c5173"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09375683218240738, "Height": 0.010976548306643963, "Left": 0.3047795593738556, "Top": 0.404365599155426}, "Polygon": [{"X": 0.304779589176178, "Y": 0.4043657183647156}, {"X": 0.39853641390800476, "Y": 0.404365599155426}, {"X": 0.3985363841056824, "Y": 0.4153420329093933}, {"X": 0.3047795593738556, "Y": 0.41534215211868286}]}, "Id": "fd6c5cea-5f2c-4150-839a-1c63638a998b"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058697398751974106, "Height": 0.010371402837336063, "Left": 0.6941802501678467, "Top": 0.40480613708496094}, "Polygon": [{"X": 0.6941803097724915, "Y": 0.4048062264919281}, {"X": 0.7528776526451111, "Y": 0.40480613708496094}, {"X": 0.7528775930404663, "Y": 0.41517746448516846}, {"X": 0.6941802501678467, "Y": 0.4151775538921356}]}, "Id": "2cc72da3-4109-4548-a00a-9d918de510e8"}, {"BlockType": "WORD", "Confidence": 99.69486999511719, "Text": "GROSS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06779786199331284, "Height": 0.010516208596527576, "Left": 0.7646864652633667, "Top": 0.40483033657073975}, "Polygon": [{"X": 0.7646865844726562, "Y": 0.4048304259777069}, {"X": 0.8324843645095825, "Y": 0.40483033657073975}, {"X": 0.8324843049049377, "Y": 0.4153464734554291}, {"X": 0.7646864652633667, "Y": 0.41534653306007385}]}, "Id": "62c3a204-2fc9-4baf-9101-11d995e8cc44"}, {"BlockType": "WORD", "Confidence": 99.6045913696289, "Text": "288", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03223693370819092, "Height": 0.010903473943471909, "Left": 0.8733862638473511, "Top": 0.4041858911514282}, "Polygon": [{"X": 0.8733863830566406, "Y": 0.4041859209537506}, {"X": 0.905623197555542, "Y": 0.4041858911514282}, {"X": 0.9056231379508972, "Y": 0.41508930921554565}, {"X": 0.8733862638473511, "Y": 0.41508936882019043}]}, "Id": "2840dbf4-b50f-4a47-81c9-7bd96c34c3ad"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "0f645dc4-367e-47be-8adc-8fe9a81e330e", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "0f645dc4-367e-47be-8adc-8fe9a81e330e", "content-type": "application/x-amz-json-1.1", "content-length": "53922", "date": "Wed, 24 Sep 2025 13:03:26 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "e94119f0-8eda-4702-9988-f1f792d1dbaa", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:03:28 GMT", "content-type": "application/json", "content-length": "534", "connection": "keep-alive", "x-amzn-requestid": "e94119f0-8eda-4702-9988-f1f792d1dbaa"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need classify each page. Only one page. It's a PACKING LIST. Keywords: \"PACKING LIST\" header. So doc_type pack_list. No continuation. So output array with one object: page_no 1, doc_type: \"pack_list\". Use tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"pack_list\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2702, "outputTokens": 74, "totalTokens": 2776}, "metrics": {"latencyMs": 488}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7ce1d3d3_FPCE2RZK61HZBY8HU6QY.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nPACKING LIST\nB/L #: 155734 Page 1\nConsignee:\n0280\nHEXPOL Middlefield\nAmes Rubber Corporation\n14910 Madison Road\nMiddlefield, Ohio 44062\n19 Ames Boulevard\nHamburg, NJ 07419\nBill To: Ames Rubber Corporation\nShip Date: 09/16/2025\nSHIP VIA: Email: <EMAIL>\n613913 1 SKIDS\nCPD9420 A (M)\nNET #: 259\nCode:\nTARE #:\n29\nMix Date: 09/08/2025 Batch: 1-2\nGROSS #:\n288\nP.O.: 2025-1161-1\nAmes Rubber Corporation\nTOTAL TARE:\n29\nMaterial Certification Attached\nTOTAL GROSS:\n288\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}