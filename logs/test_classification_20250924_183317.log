2025-09-24 18:33:17,338 - INFO - Logging initialized. Log file: logs/test_classification_20250924_183317.log
2025-09-24 18:33:17,338 - INFO - 📁 Found 8 files to process
2025-09-24 18:33:17,338 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:33:17,338 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 18:33:17,338 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 18:33:17,338 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 18:33:17,339 - INFO - ⬆️ [18:33:17] Uploading: B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:19,279 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/B03YMY2YXT4KMMTV2H8C.pdf -> s3://document-extraction-logistically/temp/4eb02dd4_B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:19,279 - INFO - 🔍 [18:33:19] Starting classification: B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:19,280 - INFO - ⬆️ [18:33:19] Uploading: FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:19,280 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:19,304 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:19,310 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4eb02dd4_B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:19,310 - INFO - Processing PDF from S3...
2025-09-24 18:33:19,310 - INFO - Downloading PDF from S3 to /tmp/tmp4ihuufub/4eb02dd4_B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:20,728 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:33:20,728 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:20,730 - INFO - Splitting PDF 4eb02dd4_B03YMY2YXT4KMMTV2H8C into 3 pages
2025-09-24 18:33:20,739 - INFO - Split PDF into 3 pages
2025-09-24 18:33:20,739 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:20,739 - INFO - Expected pages: [1, 2, 3]
2025-09-24 18:33:21,076 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FGSFKDWFU6FTBX4POAD2.pdf -> s3://document-extraction-logistically/temp/401ae83f_FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:21,076 - INFO - 🔍 [18:33:21] Starting classification: FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:21,077 - INFO - ⬆️ [18:33:21] Uploading: FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:21,079 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:21,097 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:21,102 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/401ae83f_FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:21,103 - INFO - Processing PDF from S3...
2025-09-24 18:33:21,103 - INFO - Downloading PDF from S3 to /tmp/tmpscxz27na/401ae83f_FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:21,700 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FPCE2RZK61HZBY8HU6QY.pdf -> s3://document-extraction-logistically/temp/7ce1d3d3_FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:21,701 - INFO - 🔍 [18:33:21] Starting classification: FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:21,702 - INFO - ⬆️ [18:33:21] Uploading: NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:21,702 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:21,725 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:21,732 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7ce1d3d3_FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:21,732 - INFO - Processing PDF from S3...
2025-09-24 18:33:21,732 - INFO - Downloading PDF from S3 to /tmp/tmpn72kjv7b/7ce1d3d3_FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:22,329 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/NZ8G8EVI9BW4333M4IOE.pdf -> s3://document-extraction-logistically/temp/09480f47_NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:22,329 - INFO - 🔍 [18:33:22] Starting classification: NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:22,331 - INFO - ⬆️ [18:33:22] Uploading: R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:22,334 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:22,361 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:22,366 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/09480f47_NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:22,366 - INFO - Processing PDF from S3...
2025-09-24 18:33:22,367 - INFO - Downloading PDF from S3 to /tmp/tmpfso_at_n/09480f47_NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:23,183 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/R23NVJEPXWF0BTMDCLIQ.pdf -> s3://document-extraction-logistically/temp/c49e9363_R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:23,183 - INFO - 🔍 [18:33:23] Starting classification: R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:23,184 - INFO - ⬆️ [18:33:23] Uploading: TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:23,186 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:23,203 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:33:23,204 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:23,207 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:23,210 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c49e9363_R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:23,210 - INFO - Processing PDF from S3...
2025-09-24 18:33:23,211 - INFO - Splitting PDF 7ce1d3d3_FPCE2RZK61HZBY8HU6QY into 1 pages
2025-09-24 18:33:23,211 - INFO - Downloading PDF from S3 to /tmp/tmpcyn1cuwi/c49e9363_R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:23,215 - INFO - Split PDF into 1 pages
2025-09-24 18:33:23,216 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:23,216 - INFO - Expected pages: [1]
2025-09-24 18:33:23,470 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 18:33:23,470 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:23,471 - INFO - Splitting PDF 401ae83f_FGSFKDWFU6FTBX4POAD2 into 2 pages
2025-09-24 18:33:23,474 - INFO - Split PDF into 2 pages
2025-09-24 18:33:23,474 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:23,480 - INFO - Expected pages: [1, 2]
2025-09-24 18:33:23,787 - INFO - Page 2: Extracted 43 characters, 3 lines from 4eb02dd4_B03YMY2YXT4KMMTV2H8C_f3baa70a_page_002.pdf
2025-09-24 18:33:23,787 - INFO - Successfully processed page 2
2025-09-24 18:33:24,188 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:33:24,188 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:24,190 - INFO - Splitting PDF 09480f47_NZ8G8EVI9BW4333M4IOE into 3 pages
2025-09-24 18:33:24,199 - INFO - Split PDF into 3 pages
2025-09-24 18:33:24,199 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:24,199 - INFO - Expected pages: [1, 2, 3]
2025-09-24 18:33:24,375 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf -> s3://document-extraction-logistically/temp/3e088e57_TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:24,375 - INFO - 🔍 [18:33:24] Starting classification: TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:24,376 - INFO - ⬆️ [18:33:24] Uploading: Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:24,376 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:24,386 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:24,395 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3e088e57_TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:24,395 - INFO - Processing PDF from S3...
2025-09-24 18:33:24,396 - INFO - Downloading PDF from S3 to /tmp/tmp27rhx5rl/3e088e57_TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:24,756 - INFO - Page 3: Extracted 264 characters, 29 lines from 4eb02dd4_B03YMY2YXT4KMMTV2H8C_f3baa70a_page_003.pdf
2025-09-24 18:33:24,757 - INFO - Successfully processed page 3
2025-09-24 18:33:24,979 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/Z4U3QN4FK1MG44THRGYC.pdf -> s3://document-extraction-logistically/temp/4116ec84_Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:24,980 - INFO - 🔍 [18:33:24] Starting classification: Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:24,981 - INFO - ⬆️ [18:33:24] Uploading: ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:24,983 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:25,053 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:25,057 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4116ec84_Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:25,057 - INFO - Processing PDF from S3...
2025-09-24 18:33:25,058 - INFO - Downloading PDF from S3 to /tmp/tmp59z2_d94/4116ec84_Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:25,537 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 18:33:25,538 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:25,538 - INFO - Splitting PDF c49e9363_R23NVJEPXWF0BTMDCLIQ into 1 pages
2025-09-24 18:33:25,540 - INFO - Split PDF into 1 pages
2025-09-24 18:33:25,541 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:25,541 - INFO - Expected pages: [1]
2025-09-24 18:33:25,647 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/ZH2T5IVU9KGDVY8WDQGM.pdf -> s3://document-extraction-logistically/temp/4b4c1d84_ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:25,648 - INFO - 🔍 [18:33:25] Starting classification: ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:25,649 - INFO - Initializing TextractProcessor...
2025-09-24 18:33:25,662 - INFO - Initializing BedrockProcessor...
2025-09-24 18:33:25,666 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4b4c1d84_ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:25,666 - INFO - Processing PDF from S3...
2025-09-24 18:33:25,666 - INFO - Downloading PDF from S3 to /tmp/tmp07r6cvl0/4b4c1d84_ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:26,046 - INFO - Page 1: Extracted 2583 characters, 161 lines from 4eb02dd4_B03YMY2YXT4KMMTV2H8C_f3baa70a_page_001.pdf
2025-09-24 18:33:26,046 - INFO - Successfully processed page 1
2025-09-24 18:33:26,047 - INFO - Combined 3 pages into final text
2025-09-24 18:33:26,047 - INFO - Text validation for 4eb02dd4_B03YMY2YXT4KMMTV2H8C: 2945 characters, 3 pages
2025-09-24 18:33:26,047 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:26,047 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:26,828 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:33:26,828 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:26,832 - INFO - Splitting PDF 4116ec84_Z4U3QN4FK1MG44THRGYC into 2 pages
2025-09-24 18:33:26,845 - INFO - Split PDF into 2 pages
2025-09-24 18:33:26,845 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:26,845 - INFO - Expected pages: [1, 2]
2025-09-24 18:33:26,995 - INFO - Page 1: Extracted 479 characters, 29 lines from 7ce1d3d3_FPCE2RZK61HZBY8HU6QY_7800fb33_page_001.pdf
2025-09-24 18:33:26,995 - INFO - Successfully processed page 1
2025-09-24 18:33:26,995 - INFO - Combined 1 pages into final text
2025-09-24 18:33:26,995 - INFO - Text validation for 7ce1d3d3_FPCE2RZK61HZBY8HU6QY: 496 characters, 1 pages
2025-09-24 18:33:26,996 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:26,996 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:27,425 - INFO - Page 1: Extracted 217 characters, 12 lines from 09480f47_NZ8G8EVI9BW4333M4IOE_05153c1f_page_001.pdf
2025-09-24 18:33:27,425 - INFO - Successfully processed page 1
2025-09-24 18:33:27,454 - INFO - Downloaded PDF size: 2.7 MB
2025-09-24 18:33:27,454 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:27,457 - INFO - Splitting PDF 3e088e57_TOPJSSHL5EO17O7Z7V4M into 10 pages
2025-09-24 18:33:27,471 - INFO - Split PDF into 10 pages
2025-09-24 18:33:27,472 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:27,472 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-09-24 18:33:27,894 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:33:27,894 - INFO - Splitting PDF into individual pages...
2025-09-24 18:33:27,895 - INFO - Splitting PDF 4b4c1d84_ZH2T5IVU9KGDVY8WDQGM into 1 pages
2025-09-24 18:33:27,899 - INFO - Split PDF into 1 pages
2025-09-24 18:33:27,900 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:33:27,900 - INFO - Expected pages: [1]
2025-09-24 18:33:28,462 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '10d7c10e-b47a-4d7a-8608-a29e9effec87', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:28 GMT', 'content-type': 'application/json', 'content-length': '1301', 'connection': 'keep-alive', 'x-amzn-requestid': '10d7c10e-b47a-4d7a-8608-a29e9effec87'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 3 pages. Need classify each page. The document appears to be a Packing Slip (or Packing List). The header says "Packing Slip" and has fields like Packing Slip Number, etc. It lists items, quantities. Also page 2 has "Receiving Entity (Consignee) Signature Date". Page 3 has "Integris Part Number Bulk Totals". This is likely a Packing Slip / Packing List. According to categories: pack_list is defined as Packing List. Also "Packing Slip" seems close to "Packing List". Are there categories for "packing slip"? Not explicitly. There\'s pack_list. So classify all pages as pack_list.\n\nCheck continuation: page 2 is just signature. It\'s continuation of page 1. So same type. Page 3 is totals, continuation. So all same. No other categories match. So output 3 objects: page_no 1 doc_type pack_list; page_no 2 same; page_no 3 same.\n\nReturn via tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"},{"page_no":3,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3539, 'outputTokens': 246, 'totalTokens': 3785}, 'metrics': {'latencyMs': 1207}}
2025-09-24 18:33:28,463 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4eb02dd4_B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:28,526 - INFO - 

B03YMY2YXT4KMMTV2H8C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        },
        {
            "page_no": 3,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:28,526 - INFO - 

✓ Saved result: output/run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:28,604 - INFO - Page 3: Extracted 785 characters, 69 lines from 09480f47_NZ8G8EVI9BW4333M4IOE_05153c1f_page_003.pdf
2025-09-24 18:33:28,605 - INFO - Successfully processed page 3
2025-09-24 18:33:28,626 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e94119f0-8eda-4702-9988-f1f792d1dbaa', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:28 GMT', 'content-type': 'application/json', 'content-length': '534', 'connection': 'keep-alive', 'x-amzn-requestid': 'e94119f0-8eda-4702-9988-f1f792d1dbaa'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page. It\'s a PACKING LIST. Keywords: "PACKING LIST" header. So doc_type pack_list. No continuation. So output array with one object: page_no 1, doc_type: "pack_list". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2702, 'outputTokens': 74, 'totalTokens': 2776}, 'metrics': {'latencyMs': 488}}
2025-09-24 18:33:28,626 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7ce1d3d3_FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:28,828 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4eb02dd4_B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:28,842 - INFO - 

FPCE2RZK61HZBY8HU6QY.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:28,842 - INFO - 

✓ Saved result: output/run1_FPCE2RZK61HZBY8HU6QY.json
2025-09-24 18:33:29,011 - INFO - Page 1: Extracted 1292 characters, 77 lines from 401ae83f_FGSFKDWFU6FTBX4POAD2_51ff4e5c_page_001.pdf
2025-09-24 18:33:29,011 - INFO - Successfully processed page 1
2025-09-24 18:33:29,128 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7ce1d3d3_FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:29,231 - INFO - Page 2: Extracted 1030 characters, 75 lines from 401ae83f_FGSFKDWFU6FTBX4POAD2_51ff4e5c_page_002.pdf
2025-09-24 18:33:29,231 - INFO - Successfully processed page 2
2025-09-24 18:33:29,231 - INFO - Combined 2 pages into final text
2025-09-24 18:33:29,231 - INFO - Text validation for 401ae83f_FGSFKDWFU6FTBX4POAD2: 2358 characters, 2 pages
2025-09-24 18:33:29,232 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:29,232 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:30,171 - INFO - Page 2: Extracted 145 characters, 13 lines from 4116ec84_Z4U3QN4FK1MG44THRGYC_c9e44da5_page_002.pdf
2025-09-24 18:33:30,171 - INFO - Successfully processed page 2
2025-09-24 18:33:31,363 - INFO - Page 1: Extracted 780 characters, 52 lines from c49e9363_R23NVJEPXWF0BTMDCLIQ_d739117f_page_001.pdf
2025-09-24 18:33:31,363 - INFO - Successfully processed page 1
2025-09-24 18:33:31,363 - INFO - Combined 1 pages into final text
2025-09-24 18:33:31,364 - INFO - Text validation for c49e9363_R23NVJEPXWF0BTMDCLIQ: 797 characters, 1 pages
2025-09-24 18:33:31,364 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:31,364 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:31,414 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4e993766-132b-4aff-a550-66de93e7d7b0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:31 GMT', 'content-type': 'application/json', 'content-length': '997', 'connection': 'keep-alive', 'x-amzn-requestid': '4e993766-132b-4aff-a550-66de93e7d7b0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content is a dispatch copy for B/L# 1344609-00. It\'s basically a Bill of Lading? It says "Dispatch Copy for B/L#". This looks like a Bill of Lading. But is it a BOL? Bill of Lading includes shipper, consignee, marks, description. This appears to be a dispatch copy for a B/L. So likely "bol". But also contains product details, weights, etc. It\'s not an invoice, not a POD, not a rate confirmation. So bol.\n\nCheck if page 2 is continuation: page 2 of 2. Yes second page. So both pages same doc type bol.\n\nThus we return array: page_no 1, doc_type "bol". page_no 2, doc_type "bol".\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3456, 'outputTokens': 200, 'totalTokens': 3656}, 'metrics': {'latencyMs': 1061}}
2025-09-24 18:33:31,415 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/401ae83f_FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:31,464 - INFO - 

FGSFKDWFU6FTBX4POAD2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:33:31,464 - INFO - 

✓ Saved result: output/run1_FGSFKDWFU6FTBX4POAD2.json
2025-09-24 18:33:32,284 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/401ae83f_FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:33,125 - INFO - Page 2: Extracted 3246 characters, 148 lines from 09480f47_NZ8G8EVI9BW4333M4IOE_05153c1f_page_002.pdf
2025-09-24 18:33:33,125 - INFO - Successfully processed page 2
2025-09-24 18:33:33,125 - INFO - Combined 3 pages into final text
2025-09-24 18:33:33,126 - INFO - Text validation for 09480f47_NZ8G8EVI9BW4333M4IOE: 4303 characters, 3 pages
2025-09-24 18:33:33,126 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:33,126 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:33,140 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b3adeba9-8bf2-4a1d-8bbf-a838ca8804ab', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:32 GMT', 'content-type': 'application/json', 'content-length': '593', 'connection': 'keep-alive', 'x-amzn-requestid': 'b3adeba9-8bf2-4a1d-8bbf-a838ca8804ab'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. There is only page1. Title: "Packing List". So doc_type likely pack_list. Check keyword list: "Packing List" present. So classify as pack_list. No continuation. So output array with one object: {page_no:1, doc_type:"pack_list"}. Use tool.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2835, 'outputTokens': 85, 'totalTokens': 2920}, 'metrics': {'latencyMs': 542}}
2025-09-24 18:33:33,141 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c49e9363_R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:33,159 - INFO - 

R23NVJEPXWF0BTMDCLIQ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:33,160 - INFO - 

✓ Saved result: output/run1_R23NVJEPXWF0BTMDCLIQ.json
2025-09-24 18:33:33,360 - INFO - Page 5: Extracted 726 characters, 53 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_005.pdf
2025-09-24 18:33:33,360 - INFO - Successfully processed page 5
2025-09-24 18:33:33,459 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c49e9363_R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:33,646 - INFO - Page 6: Extracted 719 characters, 52 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_006.pdf
2025-09-24 18:33:33,646 - INFO - Successfully processed page 6
2025-09-24 18:33:33,667 - INFO - Page 1: Extracted 1910 characters, 96 lines from 4116ec84_Z4U3QN4FK1MG44THRGYC_c9e44da5_page_001.pdf
2025-09-24 18:33:33,667 - INFO - Successfully processed page 1
2025-09-24 18:33:33,668 - INFO - Combined 2 pages into final text
2025-09-24 18:33:33,668 - INFO - Text validation for 4116ec84_Z4U3QN4FK1MG44THRGYC: 2091 characters, 2 pages
2025-09-24 18:33:33,668 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:33,668 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:34,342 - INFO - Page 2: Extracted 769 characters, 54 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_002.pdf
2025-09-24 18:33:34,342 - INFO - Successfully processed page 2
2025-09-24 18:33:34,556 - INFO - Page 1: Extracted 1049 characters, 71 lines from 4b4c1d84_ZH2T5IVU9KGDVY8WDQGM_65445585_page_001.pdf
2025-09-24 18:33:34,556 - INFO - Successfully processed page 1
2025-09-24 18:33:34,557 - INFO - Combined 1 pages into final text
2025-09-24 18:33:34,558 - INFO - Text validation for 4b4c1d84_ZH2T5IVU9KGDVY8WDQGM: 1066 characters, 1 pages
2025-09-24 18:33:34,558 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:34,559 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:34,650 - INFO - Page 3: Extracted 766 characters, 58 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_003.pdf
2025-09-24 18:33:34,651 - INFO - Successfully processed page 3
2025-09-24 18:33:35,644 - INFO - Page 1: Extracted 804 characters, 59 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_001.pdf
2025-09-24 18:33:35,645 - INFO - Successfully processed page 1
2025-09-24 18:33:36,464 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2d2090cb-a3e9-4008-b1be-8016cd59204e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:36 GMT', 'content-type': 'application/json', 'content-length': '2301', 'connection': 'keep-alive', 'x-amzn-requestid': '2d2090cb-a3e9-4008-b1be-8016cd59204e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 3 pages. Need to classify each page.\n\nPage1: The content: "3.31.25 ... Save, ... Supment for BI Tech. In Malaysia. 1 Box 53 Lbs. 24Kd. 24x16x16 their FEdEx ACCT # /S. 664 418 932 ..."\n\nThis looks like a packing slip? It references FedEx account. There\'s info about box, weight, dimensions. Might be a packing slip or shipment label. But no header "Packing List" or "Packing slip". However includes items like "1 Box" "53 Lbs". Could be a packing list. But it\'s just one item; maybe a packing slip. There\'s no header but likely a packing slip. Could be "pack_list". The doc type: pack_list.\n\nPage2: "SHIPPER\'S LETTER OF INSTRUCTIONS (SLI)" ... This is a Shipper\'s Letter of Instruction. That is not in the enum list. But it\'s basically a shipping instruction, maybe part of customs. But not listed. However the list includes "customs_doc" for customs paperwork. SLI likely is a customs instruction letter. But it\'s not explicitly in the list. The instruction says classify as "customs_doc" if customs related. This is a shipping instruction, includes export info, might be considered a customs document. So page2 doc_type: customs_doc.\n\nBut we need to check if any other type matches. The doc includes "Export License" etc. That is a customs-related form. So customs_doc seems appropriate.\n\nPage3: "Ames Rubber Corporation ... Packing slip ... BI TECHNOLOGIES CORP SDN ... Ship date ... Sales order: SO-20831 ... Bill to ... Terms of delivery ... Item number ... Description ... Ordered Unit ... Delivered ... Net weight Gross weight ... Receipt: ORIGINAL"\n\nThis clearly is a Packing slip. Also includes "Packing slip" header. So doc_type: pack_list.\n\nThus page1 and page3 are pack_list; page2 is customs_doc.\n\nCheck continuation rule: None of pages start with "continued". So no continuation.\n\nReturn as array with each page object. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"customs_doc"},{"page_no":3,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3853, 'outputTokens': 485, 'totalTokens': 4338}, 'metrics': {'latencyMs': 2096}}
2025-09-24 18:33:36,466 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dfe49d7c-bbb8-4a15-8107-820fcfc7290d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:36 GMT', 'content-type': 'application/json', 'content-length': '824', 'connection': 'keep-alive', 'x-amzn-requestid': 'dfe49d7c-bbb8-4a15-8107-820fcfc7290d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to parse the page. It\'s a packing list. The header says "PACKING LIST". Contains details: N/E Project Name, Package #, etc. Looks like a packing list document. No other doc types. So classify as pack_list.\n\nWe must output using classify_logistics_doc_type tool: array of objects with page_no and doc_type. Only one page.\n\nReturn JSON array? The tool expects an object containing documents array. So we call the function with documents: [{page_no:1, doc_type:"pack_list"}].\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2879, 'outputTokens': 132, 'totalTokens': 3011}, 'metrics': {'latencyMs': 720}}
2025-09-24 18:33:36,466 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/09480f47_NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:36,466 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4b4c1d84_ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:36,502 - INFO - 

ZH2T5IVU9KGDVY8WDQGM.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:36,502 - INFO - 

✓ Saved result: output/run1_ZH2T5IVU9KGDVY8WDQGM.json
2025-09-24 18:33:36,831 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4b4c1d84_ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:36,883 - INFO - 

NZ8G8EVI9BW4333M4IOE.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 3,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:36,883 - INFO - 

✓ Saved result: output/run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:36,983 - INFO - Page 4: Extracted 701 characters, 51 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_004.pdf
2025-09-24 18:33:36,983 - INFO - Successfully processed page 4
2025-09-24 18:33:37,199 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/09480f47_NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:37,444 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '93ff0bd9-bfb8-42b6-a466-326da3c18392', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:37 GMT', 'content-type': 'application/json', 'content-length': '851', 'connection': 'keep-alive', 'x-amzn-requestid': '93ff0bd9-bfb8-42b6-a466-326da3c18392'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1: It\'s a Packing Slip. The document type is "pack_list" (Packing List). The keywords: "Packing Slip", "Packing List". So doc_type = pack_list. Page2: It\'s a continuation of packing slip listing totals. It likely is same type. It starts with "Integris Part Number Bulk Totals". No "continued" but it\'s part of same doc. Should be same type. So we return two objects: page_no 1 doc_type pack_list, page_no 2 doc_type pack_list. Use function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3174, 'outputTokens': 148, 'totalTokens': 3322}, 'metrics': {'latencyMs': 905}}
2025-09-24 18:33:37,445 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4116ec84_Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:37,474 - INFO - 

Z4U3QN4FK1MG44THRGYC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:37,474 - INFO - 

✓ Saved result: output/run1_Z4U3QN4FK1MG44THRGYC.json
2025-09-24 18:33:37,618 - INFO - Page 7: Extracted 719 characters, 53 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_007.pdf
2025-09-24 18:33:37,619 - INFO - Successfully processed page 7
2025-09-24 18:33:37,772 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4116ec84_Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:38,451 - INFO - Page 8: Extracted 698 characters, 51 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_008.pdf
2025-09-24 18:33:38,452 - INFO - Successfully processed page 8
2025-09-24 18:33:39,473 - INFO - Page 10: Extracted 700 characters, 52 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_010.pdf
2025-09-24 18:33:39,473 - INFO - Successfully processed page 10
2025-09-24 18:33:39,818 - INFO - Page 9: Extracted 704 characters, 51 lines from 3e088e57_TOPJSSHL5EO17O7Z7V4M_0cad6451_page_009.pdf
2025-09-24 18:33:39,818 - INFO - Successfully processed page 9
2025-09-24 18:33:39,818 - INFO - Combined 10 pages into final text
2025-09-24 18:33:39,818 - INFO - Text validation for 3e088e57_TOPJSSHL5EO17O7Z7V4M: 7496 characters, 10 pages
2025-09-24 18:33:39,819 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:33:39,819 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:33:42,318 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6266471a-1652-40ff-acf8-9cd8ca03664d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:03:42 GMT', 'content-type': 'application/json', 'content-length': '1193', 'connection': 'keep-alive', 'x-amzn-requestid': '6266471a-1652-40ff-acf8-9cd8ca03664d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. All pages are "PACKING SLIP" with "TRISTONE" etc. It\'s a packing list? The header "PACKING SLIP" suggests maybe pack list. In definitions, "pack_list" is Packing List. The header "PACKING SLIP" but it is similar to packing slip. The content lists items, quantities, etc. So likely pack_list. No other document types. So each page is pack_list. No continuation signals. So output 10 objects with page_no 1-10 and doc_type "pack_list". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"},{"page_no":3,"doc_type":"pack_list"},{"page_no":4,"doc_type":"pack_list"},{"page_no":5,"doc_type":"pack_list"},{"page_no":6,"doc_type":"pack_list"},{"page_no":7,"doc_type":"pack_list"},{"page_no":8,"doc_type":"pack_list"},{"page_no":9,"doc_type":"pack_list"},{"page_no":10,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 5273, 'outputTokens': 238, 'totalTokens': 5511}, 'metrics': {'latencyMs': 1174}}
2025-09-24 18:33:42,318 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3e088e57_TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,393 - INFO - 

TOPJSSHL5EO17O7Z7V4M.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        },
        {
            "page_no": 3,
            "doc_type": "pack_list"
        },
        {
            "page_no": 4,
            "doc_type": "pack_list"
        },
        {
            "page_no": 5,
            "doc_type": "pack_list"
        },
        {
            "page_no": 6,
            "doc_type": "pack_list"
        },
        {
            "page_no": 7,
            "doc_type": "pack_list"
        },
        {
            "page_no": 8,
            "doc_type": "pack_list"
        },
        {
            "page_no": 9,
            "doc_type": "pack_list"
        },
        {
            "page_no": 10,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 18:33:42,393 - INFO - 

✓ Saved result: output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,700 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3e088e57_TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,704 - INFO - 
📊 Processing Summary:
2025-09-24 18:33:42,704 - INFO -    Total files: 8
2025-09-24 18:33:42,704 - INFO -    Successful: 8
2025-09-24 18:33:42,704 - INFO -    Failed: 0
2025-09-24 18:33:42,704 - INFO -    Duration: 25.37 seconds
2025-09-24 18:33:42,704 - INFO -    Output directory: output
2025-09-24 18:33:42,704 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:33:42,704 - INFO -    📄 B03YMY2YXT4KMMTV2H8C.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"},{"page_no":3,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,704 - INFO -    📄 FGSFKDWFU6FTBX4POAD2.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:33:42,704 - INFO -    📄 FPCE2RZK61HZBY8HU6QY.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,704 - INFO -    📄 NZ8G8EVI9BW4333M4IOE.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"customs_doc"},{"page_no":3,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,705 - INFO -    📄 R23NVJEPXWF0BTMDCLIQ.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,705 - INFO -    📄 TOPJSSHL5EO17O7Z7V4M.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"},{"page_no":3,"doc_type":"pack_list"},{"page_no":4,"doc_type":"pack_list"},{"page_no":5,"doc_type":"pack_list"},{"page_no":6,"doc_type":"pack_list"},{"page_no":7,"doc_type":"pack_list"},{"page_no":8,"doc_type":"pack_list"},{"page_no":9,"doc_type":"pack_list"},{"page_no":10,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,705 - INFO -    📄 Z4U3QN4FK1MG44THRGYC.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,705 - INFO -    📄 ZH2T5IVU9KGDVY8WDQGM.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 18:33:42,705 - INFO - 
============================================================================================================================================
2025-09-24 18:33:42,705 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:33:42,705 - INFO - ============================================================================================================================================
2025-09-24 18:33:42,705 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:33:42,706 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:33:42,706 - INFO - B03YMY2YXT4KMMTV2H8C.pdf                           1      pack_list                                run1_B03YMY2YXT4KMMTV2H8C.json                                                  
2025-09-24 18:33:42,706 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:42,706 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:42,706 - INFO - 
2025-09-24 18:33:42,706 - INFO - B03YMY2YXT4KMMTV2H8C.pdf                           2      pack_list                                run1_B03YMY2YXT4KMMTV2H8C.json                                                  
2025-09-24 18:33:42,706 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:42,706 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:42,706 - INFO - 
2025-09-24 18:33:42,706 - INFO - B03YMY2YXT4KMMTV2H8C.pdf                           3      pack_list                                run1_B03YMY2YXT4KMMTV2H8C.json                                                  
2025-09-24 18:33:42,706 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/B03YMY2YXT4KMMTV2H8C.pdf
2025-09-24 18:33:42,706 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:42,706 - INFO - 
2025-09-24 18:33:42,706 - INFO - FGSFKDWFU6FTBX4POAD2.pdf                           1      bol                                      run1_FGSFKDWFU6FTBX4POAD2.json                                                  
2025-09-24 18:33:42,706 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:42,706 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FGSFKDWFU6FTBX4POAD2.json
2025-09-24 18:33:42,706 - INFO - 
2025-09-24 18:33:42,706 - INFO - FGSFKDWFU6FTBX4POAD2.pdf                           2      bol                                      run1_FGSFKDWFU6FTBX4POAD2.json                                                  
2025-09-24 18:33:42,707 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FGSFKDWFU6FTBX4POAD2.pdf
2025-09-24 18:33:42,707 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FGSFKDWFU6FTBX4POAD2.json
2025-09-24 18:33:42,707 - INFO - 
2025-09-24 18:33:42,707 - INFO - FPCE2RZK61HZBY8HU6QY.pdf                           1      pack_list                                run1_FPCE2RZK61HZBY8HU6QY.json                                                  
2025-09-24 18:33:42,707 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FPCE2RZK61HZBY8HU6QY.pdf
2025-09-24 18:33:42,707 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FPCE2RZK61HZBY8HU6QY.json
2025-09-24 18:33:42,707 - INFO - 
2025-09-24 18:33:42,707 - INFO - NZ8G8EVI9BW4333M4IOE.pdf                           1      pack_list                                run1_NZ8G8EVI9BW4333M4IOE.json                                                  
2025-09-24 18:33:42,707 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:42,707 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:42,707 - INFO - 
2025-09-24 18:33:42,707 - INFO - NZ8G8EVI9BW4333M4IOE.pdf                           2      customs_doc                              run1_NZ8G8EVI9BW4333M4IOE.json                                                  
2025-09-24 18:33:42,707 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:42,707 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:42,707 - INFO - 
2025-09-24 18:33:42,707 - INFO - NZ8G8EVI9BW4333M4IOE.pdf                           3      pack_list                                run1_NZ8G8EVI9BW4333M4IOE.json                                                  
2025-09-24 18:33:42,707 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/NZ8G8EVI9BW4333M4IOE.pdf
2025-09-24 18:33:42,707 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:42,707 - INFO - 
2025-09-24 18:33:42,707 - INFO - R23NVJEPXWF0BTMDCLIQ.pdf                           1      pack_list                                run1_R23NVJEPXWF0BTMDCLIQ.json                                                  
2025-09-24 18:33:42,707 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/R23NVJEPXWF0BTMDCLIQ.pdf
2025-09-24 18:33:42,708 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_R23NVJEPXWF0BTMDCLIQ.json
2025-09-24 18:33:42,708 - INFO - 
2025-09-24 18:33:42,708 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           1      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,708 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,708 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,708 - INFO - 
2025-09-24 18:33:42,708 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           2      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,708 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,708 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,708 - INFO - 
2025-09-24 18:33:42,708 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           3      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,708 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,708 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,708 - INFO - 
2025-09-24 18:33:42,708 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           4      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,708 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,708 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,708 - INFO - 
2025-09-24 18:33:42,708 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           5      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,708 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,709 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,709 - INFO - 
2025-09-24 18:33:42,709 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           6      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,709 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,709 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,709 - INFO - 
2025-09-24 18:33:42,709 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           7      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,709 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,709 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,709 - INFO - 
2025-09-24 18:33:42,709 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           8      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,709 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,709 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,709 - INFO - 
2025-09-24 18:33:42,709 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           9      pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,709 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,709 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,709 - INFO - 
2025-09-24 18:33:42,709 - INFO - TOPJSSHL5EO17O7Z7V4M.pdf                           10     pack_list                                run1_TOPJSSHL5EO17O7Z7V4M.json                                                  
2025-09-24 18:33:42,709 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf
2025-09-24 18:33:42,709 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,709 - INFO - 
2025-09-24 18:33:42,709 - INFO - Z4U3QN4FK1MG44THRGYC.pdf                           1      pack_list                                run1_Z4U3QN4FK1MG44THRGYC.json                                                  
2025-09-24 18:33:42,710 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:42,710 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z4U3QN4FK1MG44THRGYC.json
2025-09-24 18:33:42,710 - INFO - 
2025-09-24 18:33:42,710 - INFO - Z4U3QN4FK1MG44THRGYC.pdf                           2      pack_list                                run1_Z4U3QN4FK1MG44THRGYC.json                                                  
2025-09-24 18:33:42,710 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/Z4U3QN4FK1MG44THRGYC.pdf
2025-09-24 18:33:42,710 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z4U3QN4FK1MG44THRGYC.json
2025-09-24 18:33:42,710 - INFO - 
2025-09-24 18:33:42,710 - INFO - ZH2T5IVU9KGDVY8WDQGM.pdf                           1      pack_list                                run1_ZH2T5IVU9KGDVY8WDQGM.json                                                  
2025-09-24 18:33:42,710 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/ZH2T5IVU9KGDVY8WDQGM.pdf
2025-09-24 18:33:42,710 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZH2T5IVU9KGDVY8WDQGM.json
2025-09-24 18:33:42,710 - INFO - 
2025-09-24 18:33:42,710 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:33:42,710 - INFO - Total entries: 23
2025-09-24 18:33:42,710 - INFO - ============================================================================================================================================
2025-09-24 18:33:42,710 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:33:42,710 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:33:42,710 - INFO -   1. B03YMY2YXT4KMMTV2H8C.pdf            Page 1   → pack_list       | run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:42,710 - INFO -   2. B03YMY2YXT4KMMTV2H8C.pdf            Page 2   → pack_list       | run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:42,710 - INFO -   3. B03YMY2YXT4KMMTV2H8C.pdf            Page 3   → pack_list       | run1_B03YMY2YXT4KMMTV2H8C.json
2025-09-24 18:33:42,710 - INFO -   4. FGSFKDWFU6FTBX4POAD2.pdf            Page 1   → bol             | run1_FGSFKDWFU6FTBX4POAD2.json
2025-09-24 18:33:42,711 - INFO -   5. FGSFKDWFU6FTBX4POAD2.pdf            Page 2   → bol             | run1_FGSFKDWFU6FTBX4POAD2.json
2025-09-24 18:33:42,711 - INFO -   6. FPCE2RZK61HZBY8HU6QY.pdf            Page 1   → pack_list       | run1_FPCE2RZK61HZBY8HU6QY.json
2025-09-24 18:33:42,711 - INFO -   7. NZ8G8EVI9BW4333M4IOE.pdf            Page 1   → pack_list       | run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:42,711 - INFO -   8. NZ8G8EVI9BW4333M4IOE.pdf            Page 2   → customs_doc     | run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:42,711 - INFO -   9. NZ8G8EVI9BW4333M4IOE.pdf            Page 3   → pack_list       | run1_NZ8G8EVI9BW4333M4IOE.json
2025-09-24 18:33:42,711 - INFO -  10. R23NVJEPXWF0BTMDCLIQ.pdf            Page 1   → pack_list       | run1_R23NVJEPXWF0BTMDCLIQ.json
2025-09-24 18:33:42,711 - INFO -  11. TOPJSSHL5EO17O7Z7V4M.pdf            Page 1   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  12. TOPJSSHL5EO17O7Z7V4M.pdf            Page 2   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  13. TOPJSSHL5EO17O7Z7V4M.pdf            Page 3   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  14. TOPJSSHL5EO17O7Z7V4M.pdf            Page 4   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  15. TOPJSSHL5EO17O7Z7V4M.pdf            Page 5   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  16. TOPJSSHL5EO17O7Z7V4M.pdf            Page 6   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  17. TOPJSSHL5EO17O7Z7V4M.pdf            Page 7   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  18. TOPJSSHL5EO17O7Z7V4M.pdf            Page 8   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  19. TOPJSSHL5EO17O7Z7V4M.pdf            Page 9   → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  20. TOPJSSHL5EO17O7Z7V4M.pdf            Page 10  → pack_list       | run1_TOPJSSHL5EO17O7Z7V4M.json
2025-09-24 18:33:42,711 - INFO -  21. Z4U3QN4FK1MG44THRGYC.pdf            Page 1   → pack_list       | run1_Z4U3QN4FK1MG44THRGYC.json
2025-09-24 18:33:42,711 - INFO -  22. Z4U3QN4FK1MG44THRGYC.pdf            Page 2   → pack_list       | run1_Z4U3QN4FK1MG44THRGYC.json
2025-09-24 18:33:42,711 - INFO -  23. ZH2T5IVU9KGDVY8WDQGM.pdf            Page 1   → pack_list       | run1_ZH2T5IVU9KGDVY8WDQGM.json
2025-09-24 18:33:42,712 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:33:42,712 - INFO - 
✅ Test completed: {'total_files': 8, 'processed': 8, 'failed': 0, 'errors': [], 'duration_seconds': 25.365071, 'processed_files': [{'filename': 'B03YMY2YXT4KMMTV2H8C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}, {'page_no': 3, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/B03YMY2YXT4KMMTV2H8C.pdf'}, {'filename': 'FGSFKDWFU6FTBX4POAD2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FGSFKDWFU6FTBX4POAD2.pdf'}, {'filename': 'FPCE2RZK61HZBY8HU6QY.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/FPCE2RZK61HZBY8HU6QY.pdf'}, {'filename': 'NZ8G8EVI9BW4333M4IOE.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'customs_doc'}, {'page_no': 3, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/NZ8G8EVI9BW4333M4IOE.pdf'}, {'filename': 'R23NVJEPXWF0BTMDCLIQ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/R23NVJEPXWF0BTMDCLIQ.pdf'}, {'filename': 'TOPJSSHL5EO17O7Z7V4M.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}, {'page_no': 3, 'doc_type': 'pack_list'}, {'page_no': 4, 'doc_type': 'pack_list'}, {'page_no': 5, 'doc_type': 'pack_list'}, {'page_no': 6, 'doc_type': 'pack_list'}, {'page_no': 7, 'doc_type': 'pack_list'}, {'page_no': 8, 'doc_type': 'pack_list'}, {'page_no': 9, 'doc_type': 'pack_list'}, {'page_no': 10, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/TOPJSSHL5EO17O7Z7V4M.pdf'}, {'filename': 'Z4U3QN4FK1MG44THRGYC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/Z4U3QN4FK1MG44THRGYC.pdf'}, {'filename': 'ZH2T5IVU9KGDVY8WDQGM.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pack_list/ZH2T5IVU9KGDVY8WDQGM.pdf'}]}
