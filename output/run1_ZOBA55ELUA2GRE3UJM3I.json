{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 3.7258306662124596e-08, "Y": 0.0}, {"X": 1.0, "Y": 1.5272829045898106e-07}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 0.999974250793457}]}, "Id": "b11116a9-3cb5-44a9-83ad-1cf55ccb3a84", "Relationships": [{"Type": "CHILD", "Ids": ["66a59033-7c61-4d98-9226-abb74da03f48", "af2235ca-d063-4a6f-9807-022a66442eb4", "db14141b-ca6b-4869-a9f7-e92846914fd6", "ff3978fa-56bd-4d61-bd3f-d4c613c4a881", "9d9e6afd-445e-4bad-98c6-22a8e83fbc48", "96a74017-4650-428e-beb6-0b2344e294f3", "6d772a14-da2e-4c9e-9683-27a9b8b498ba", "993cef89-3170-47b9-b55c-5e703b80ecdd", "10617f9d-3131-4408-923d-a7724e1d1606", "ec13efc8-3ce2-4c35-9716-514313dfd77c", "d1358c16-0804-4da5-be4c-b8919ece407a", "d2598fa4-8cf7-42cd-aa9c-fda9437a39ae", "7cf61932-744f-4b8b-a412-81a9efc7bc2f", "9414fe29-a81b-4ee2-a954-ad3c23e88fc1", "ffd59019-81f9-47ba-94b3-8b6d63b7588e", "e34bf889-201b-4379-822c-c69739fa8fb9", "d5782b1d-956c-401a-aa94-d651e97a0a83", "844e51a2-7b6f-4bfe-b33a-150080b70fa7", "5627214a-f153-4dec-a28f-dcf5bd8fd449", "01433c95-77dc-497e-b9dd-35f8ebeac938", "eea657c8-df5a-4c21-ac40-921651e94a64", "c55d61fe-d3ad-4e2c-946a-c541a8648be2", "3c0cc361-9dda-469a-89a4-6710e4d4e2b5", "90a8ca1d-997f-4d63-bc28-8d31044603f7", "e0833ec0-a2f8-434b-a55e-3691768c4b2b", "2497be36-be45-4a5f-b52e-edd54d05c025", "2e0ffcb8-2746-45bd-bc22-0efa18de91de", "8ed88cf9-1277-44eb-9ebb-0027a582a7ea", "6390ab6f-c4b5-40da-9c64-9655645553dd", "14ff6cd5-3010-41cf-8834-79011889dff6", "7f23880e-1468-4208-b7fa-ca984d5ec5b9", "7665859c-0ae2-4ea1-b88d-6c9fba9e7bb7", "86cefb82-1e5d-4b81-ab92-3a7e39ee9771", "53ac9d29-5978-4db1-870f-b714d15dd8bc", "651f0eb3-f5e4-4e13-a47b-591b788864d1", "e5a41e93-58c0-4553-bbd1-36f6c5be254b", "64614aca-f258-4956-896e-0a60765d579a", "1676bb61-e6f2-4c5c-bb3f-6340ffeb11f4", "02515640-9d00-4aa2-802c-4bc4e6bf178d", "2ce5fcaa-8cbf-4ca6-a227-c2e105e8f7fb", "35a31213-5522-46e2-a9b4-24b722b9cc5d", "a7d25f93-0526-4db9-9f22-548c7ffc8e51", "de51abcc-3382-4147-b6b0-f8cbb9af62e4", "7b634b61-29c2-4c3f-aa00-ce8b271bc805", "0e08ab94-7f17-42b6-ba98-b6167a661075", "337e4293-bb30-4572-8bc4-2049b994abf6", "fd8f16f4-bb54-40b6-b214-18030dca1259", "dcb89f34-cb9d-4b98-a6b0-fd777b2b57b8", "56dd8a16-d205-4f60-840e-1d7753b15ca7", "1447757b-e602-420f-8e38-fa61184123b9", "9c0904ab-fb1c-491e-a2a0-fb96d372df81", "d920bc7a-ab7b-41a3-8f4c-e808f7822266", "b965c73a-7c29-4e4b-82ef-bdfeabd259b8", "315d8771-5089-4a02-aa78-fde997ad1f4e", "e452319c-7d14-40b1-94d9-97a19dd51460", "ad7ef9a7-ec94-44d7-a117-980b884d4841", "dbfdea44-a764-46df-8f3c-a6758fb8ace5", "3bb412c0-7a44-4285-ba05-6a316a407488", "ed99882d-ee25-4d72-a78b-f49f27d6b839"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Capstone Logistics", "Geometry": {"BoundingBox": {"Width": 0.307231605052948, "Height": 0.02827439457178116, "Left": 0.07957328855991364, "Top": 0.010019805282354355}, "Polygon": [{"X": 0.07957354933023453, "Y": 0.010019805282354355}, {"X": 0.38680487871170044, "Y": 0.0100551201030612}, {"X": 0.3868038058280945, "Y": 0.03829419985413551}, {"X": 0.07957328855991364, "Y": 0.03825879469513893}]}, "Id": "66a59033-7c61-4d98-9226-abb74da03f48", "Relationships": [{"Type": "CHILD", "Ids": ["c38169f8-a080-4b6f-9f9c-0a2d66a24d7d", "94c54ee6-aaa4-466c-8b19-46d8ac3744f1"]}]}, {"BlockType": "LINE", "Confidence": 99.9655532836914, "Text": "30 TECHNOLOGY PKWY SOUTH SUITE 200", "Geometry": {"BoundingBox": {"Width": 0.31829309463500977, "Height": 0.009193949401378632, "Left": 0.07423622161149979, "Top": 0.05621491000056267}, "Polygon": [{"X": 0.07423629611730576, "Y": 0.05621491000056267}, {"X": 0.39252933859825134, "Y": 0.0562516450881958}, {"X": 0.3925289809703827, "Y": 0.065408855676651}, {"X": 0.07423622161149979, "Y": 0.06537208706140518}]}, "Id": "af2235ca-d063-4a6f-9807-022a66442eb4", "Relationships": [{"Type": "CHILD", "Ids": ["0901960b-ba95-44c7-b6df-8ec735d67eb5", "20296265-b5a2-4514-bc56-736e109bfe97", "f771d633-c9f2-4c95-9ed4-60a8baa916d0", "da289fe2-e79d-4b48-94d7-b51ca353413b", "421069f7-f85a-46a7-966e-7d4e40333fc1", "7209ce65-6f08-4213-8457-5b9435074135"]}]}, {"BlockType": "LINE", "Confidence": 99.7461929321289, "Text": "PEACHTREE CORNERS, GA 30092", "Geometry": {"BoundingBox": {"Width": 0.24765564501285553, "Height": 0.010909304022789001, "Left": 0.10911024361848831, "Top": 0.07021450251340866}, "Polygon": [{"X": 0.10911037772893906, "Y": 0.07021450251340866}, {"X": 0.35676589608192444, "Y": 0.07024312019348145}, {"X": 0.3567655086517334, "Y": 0.08112380653619766}, {"X": 0.10911024361848831, "Y": 0.08109515905380249}]}, "Id": "db14141b-ca6b-4869-a9f7-e92846914fd6", "Relationships": [{"Type": "CHILD", "Ids": ["5b68aeff-ddfc-4e23-9214-b791c1cb04bc", "b85c1edf-a1d8-437a-9345-187f2ca1c729", "5af1c5c5-6f2a-433d-807f-8a0ec690861b", "fc0694a9-dc2b-4583-8c7b-6cfc5d104890"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "770-414-1929", "Geometry": {"BoundingBox": {"Width": 0.09149593859910965, "Height": 0.009205000475049019, "Left": 0.18756142258644104, "Top": 0.08390548080205917}, "Polygon": [{"X": 0.18756160140037537, "Y": 0.08390548080205917}, {"X": 0.2790573537349701, "Y": 0.0839160680770874}, {"X": 0.279057115316391, "Y": 0.09311047941446304}, {"X": 0.18756142258644104, "Y": 0.09309988468885422}]}, "Id": "ff3978fa-56bd-4d61-bd3f-d4c613c4a881", "Relationships": [{"Type": "CHILD", "Ids": ["3839954c-2564-4c65-b934-83d15e5e8be7"]}]}, {"BlockType": "LINE", "Confidence": 99.8006362915039, "Text": "FED ID# 45-3087555", "Geometry": {"BoundingBox": {"Width": 0.14130957424640656, "Height": 0.009222261607646942, "Left": 0.16271962225437164, "Top": 0.09789491444826126}, "Polygon": [{"X": 0.16271978616714478, "Y": 0.09789491444826126}, {"X": 0.3040291965007782, "Y": 0.0979112908244133}, {"X": 0.3040289282798767, "Y": 0.1071171760559082}, {"X": 0.16271962225437164, "Y": 0.10710079222917557}]}, "Id": "9d9e6afd-445e-4bad-98c6-22a8e83fbc48", "Relationships": [{"Type": "CHILD", "Ids": ["414604df-9883-46b0-8417-414dd2d0a00b", "15fe8a48-7b8a-4f3a-a877-db42a401605e", "61bfe649-7e08-4a1a-96f0-2e51772af442"]}]}, {"BlockType": "LINE", "Confidence": 99.87284851074219, "Text": "08:32:49 September 23, 2025", "Geometry": {"BoundingBox": {"Width": 0.28565409779548645, "Height": 0.01733803004026413, "Left": 0.09004038572311401, "Top": 0.12784264981746674}, "Polygon": [{"X": 0.09004056453704834, "Y": 0.12784264981746674}, {"X": 0.37569448351860046, "Y": 0.12787583470344543}, {"X": 0.37569382786750793, "Y": 0.14518068730831146}, {"X": 0.09004038572311401, "Y": 0.145147442817688}]}, "Id": "96a74017-4650-428e-beb6-0b2344e294f3", "Relationships": [{"Type": "CHILD", "Ids": ["624b3699-4ed0-4835-97b8-98d510be898a", "09987890-d369-42fe-8a2d-47356f59104f", "a2c5eeab-7433-4e4e-8f7a-e9efd62d1826", "ae9ae40f-3045-4fd0-81fb-cfb2e3ad4a94"]}]}, {"BlockType": "LINE", "Confidence": 99.5458984375, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.06518501788377762, "Height": 0.011678417213261127, "Left": 0.02709079161286354, "Top": 0.1626720279455185}, "Polygon": [{"X": 0.02709084190428257, "Y": 0.1626720279455185}, {"X": 0.09227581322193146, "Y": 0.16267962753772736}, {"X": 0.09227568656206131, "Y": 0.17435044050216675}, {"X": 0.02709079161286354, "Y": 0.17434284090995789}]}, "Id": "6d772a14-da2e-4c9e-9683-27a9b8b498ba", "Relationships": [{"Type": "CHILD", "Ids": ["cb046ba1-1824-426c-b560-dd6414d59852", "d976f206-e4d9-4c5b-9e30-05a0d080188a"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "3b670798-9dde-", "Geometry": {"BoundingBox": {"Width": 0.10665805637836456, "Height": 0.00964568741619587, "Left": 0.22967711091041565, "Top": 0.16264940798282623}, "Polygon": [{"X": 0.22967733442783356, "Y": 0.16264940798282623}, {"X": 0.3363351821899414, "Y": 0.1626618355512619}, {"X": 0.33633485436439514, "Y": 0.17229509353637695}, {"X": 0.22967711091041565, "Y": 0.17228266596794128}]}, "Id": "993cef89-3170-47b9-b55c-5e703b80ecdd", "Relationships": [{"Type": "CHILD", "Ids": ["5e43cfbe-a676-4a13-b848-d3827ac8bb09"]}]}, {"BlockType": "LINE", "Confidence": 98.9148178100586, "Text": "404c-a323-f8bbdf2dbdde", "Geometry": {"BoundingBox": {"Width": 0.165127232670784, "Height": 0.00928560458123684, "Left": 0.22866462171077728, "Top": 0.17640194296836853}, "Polygon": [{"X": 0.2286648452281952, "Y": 0.17640194296836853}, {"X": 0.3937918543815613, "Y": 0.17642121016979218}, {"X": 0.3937914967536926, "Y": 0.18568754196166992}, {"X": 0.22866462171077728, "Y": 0.18566825985908508}]}, "Id": "10617f9d-3131-4408-923d-a7724e1d1606", "Relationships": [{"Type": "CHILD", "Ids": ["01d7635e-2f08-4f38-96d4-85e25f09bbbd"]}]}, {"BlockType": "LINE", "Confidence": 99.64126586914062, "Text": "Location:", "Geometry": {"BoundingBox": {"Width": 0.06126055866479874, "Height": 0.00911097601056099, "Left": 0.026779862120747566, "Top": 0.1989181786775589}, "Polygon": [{"X": 0.0267799012362957, "Y": 0.1989181786775589}, {"X": 0.08804041892290115, "Y": 0.19892534613609314}, {"X": 0.08804032951593399, "Y": 0.2080291509628296}, {"X": 0.026779862120747566, "Y": 0.20802198350429535}]}, "Id": "ec13efc8-3ce2-4c35-9716-514313dfd77c", "Relationships": [{"Type": "CHILD", "Ids": ["c26226d8-30e4-4ae0-82fc-be512a311c58"]}]}, {"BlockType": "LINE", "Confidence": 99.94764709472656, "Text": "EDWARD DON DALLAS", "Geometry": {"BoundingBox": {"Width": 0.1774781048297882, "Height": 0.009022440761327744, "Left": 0.22936950623989105, "Top": 0.19896651804447174}, "Polygon": [{"X": 0.22936972975730896, "Y": 0.19896651804447174}, {"X": 0.40684762597084045, "Y": 0.19898726046085358}, {"X": 0.4068472385406494, "Y": 0.2079889476299286}, {"X": 0.22936950623989105, "Y": 0.20796819031238556}]}, "Id": "d1358c16-0804-4da5-be4c-b8919ece407a", "Relationships": [{"Type": "CHILD", "Ids": ["e26b19aa-11ce-4e47-bd0b-0c0d569ab321", "c6a3a3be-bb2e-4abd-81b9-5e24b54f89f9", "56f0038e-ffd8-4372-ae38-f31eec4352bc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TX", "Geometry": {"BoundingBox": {"Width": 0.021806541830301285, "Height": 0.009093190543353558, "Left": 0.22926299273967743, "Top": 0.21249201893806458}, "Polygon": [{"X": 0.22926321625709534, "Y": 0.21249201893806458}, {"X": 0.2510695457458496, "Y": 0.21249456703662872}, {"X": 0.2510693073272705, "Y": 0.2215851992368698}, {"X": 0.22926299273967743, "Y": 0.22158265113830566}]}, "Id": "d2598fa4-8cf7-42cd-aa9c-fda9437a39ae", "Relationships": [{"Type": "CHILD", "Ids": ["c45aa9aa-81fd-4408-a672-c9eba780f8de"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Work Date:", "Geometry": {"BoundingBox": {"Width": 0.07513963431119919, "Height": 0.009049306623637676, "Left": 0.027036121115088463, "Top": 0.2349698692560196}, "Polygon": [{"X": 0.027036160230636597, "Y": 0.2349698692560196}, {"X": 0.1021757572889328, "Y": 0.23497867584228516}, {"X": 0.10217565298080444, "Y": 0.24401918053627014}, {"X": 0.027036121115088463, "Y": 0.24401035904884338}]}, "Id": "7cf61932-744f-4b8b-a412-81a9efc7bc2f", "Relationships": [{"Type": "CHILD", "Ids": ["326f68a3-d3f7-4955-b55f-97dbb520e663", "4ec1db09-d730-4b4a-9809-9c824ec9454d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "2025-09-23", "Geometry": {"BoundingBox": {"Width": 0.07467342913150787, "Height": 0.009046281687915325, "Left": 0.22946538031101227, "Top": 0.23502826690673828}, "Polygon": [{"X": 0.22946558892726898, "Y": 0.23502826690673828}, {"X": 0.30413880944252014, "Y": 0.23503701388835907}, {"X": 0.30413854122161865, "Y": 0.24407453835010529}, {"X": 0.22946538031101227, "Y": 0.2440657764673233}]}, "Id": "9414fe29-a81b-4ee2-a954-ad3c23e88fc1", "Relationships": [{"Type": "CHILD", "Ids": ["387a0a39-ce8e-4252-8090-aa615e7b2035"]}]}, {"BlockType": "LINE", "Confidence": 99.89058685302734, "Text": "Bill Code:", "Geometry": {"BoundingBox": {"Width": 0.0659601241350174, "Height": 0.009263691492378712, "Left": 0.02714214287698269, "Top": 0.2528725564479828}, "Polygon": [{"X": 0.027142181992530823, "Y": 0.2528725564479828}, {"X": 0.09310226142406464, "Y": 0.2528803050518036}, {"X": 0.09310216456651688, "Y": 0.2621362507343292}, {"X": 0.02714214287698269, "Y": 0.26212847232818604}]}, "Id": "ffd59019-81f9-47ba-94b3-8b6d63b7588e", "Relationships": [{"Type": "CHILD", "Ids": ["e400e29a-7912-463a-af09-9b956827f409", "210ec0f8-83c5-4e09-9cfa-9b2b2ac82f25"]}]}, {"BlockType": "LINE", "Confidence": 99.3223876953125, "Text": "RCOD30888", "Geometry": {"BoundingBox": {"Width": 0.08491439372301102, "Height": 0.009128590114414692, "Left": 0.2294970154762268, "Top": 0.2529875338077545}, "Polygon": [{"X": 0.22949722409248352, "Y": 0.2529875338077545}, {"X": 0.3144114017486572, "Y": 0.2529975175857544}, {"X": 0.31441113352775574, "Y": 0.26211613416671753}, {"X": 0.2294970154762268, "Y": 0.26210615038871765}]}, "Id": "e34bf889-201b-4379-822c-c69739fa8fb9", "Relationships": [{"Type": "CHILD", "Ids": ["6e6d9d42-8a11-4634-8299-f8844e67d0e4"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.0493045337498188, "Height": 0.009180132299661636, "Left": 0.027214767411351204, "Top": 0.27090147137641907}, "Polygon": [{"X": 0.027214808389544487, "Y": 0.27090147137641907}, {"X": 0.07651930302381516, "Y": 0.2709072530269623}, {"X": 0.07651922106742859, "Y": 0.2800815999507904}, {"X": 0.027214767411351204, "Y": 0.2800757884979248}]}, "Id": "d5782b1d-956c-401a-aa94-d651e97a0a83", "Relationships": [{"Type": "CHILD", "Ids": ["4c60559b-3a22-48e6-8ae6-2345488b4f4a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "FLAWLESS", "Geometry": {"BoundingBox": {"Width": 0.0831935703754425, "Height": 0.008985047228634357, "Left": 0.22927530109882355, "Top": 0.27109864354133606}, "Polygon": [{"X": 0.22927552461624146, "Y": 0.27109864354133606}, {"X": 0.31246888637542725, "Y": 0.2711084187030792}, {"X": 0.31246858835220337, "Y": 0.28008368611335754}, {"X": 0.22927530109882355, "Y": 0.2800739109516144}]}, "Id": "844e51a2-7b6f-4bfe-b33a-150080b70fa7", "Relationships": [{"Type": "CHILD", "Ids": ["12cecf32-add2-40a8-9bf5-886261dcb12c"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Dock:", "Geometry": {"BoundingBox": {"Width": 0.03843407705426216, "Height": 0.009150012396275997, "Left": 0.027146941050887108, "Top": 0.2889705300331116}, "Polygon": [{"X": 0.02714698016643524, "Y": 0.2889705300331116}, {"X": 0.06558101624250412, "Y": 0.2889750599861145}, {"X": 0.06558094173669815, "Y": 0.29812055826187134}, {"X": 0.027146941050887108, "Y": 0.298115998506546}]}, "Id": "5627214a-f153-4dec-a28f-dcf5bd8fd449", "Relationships": [{"Type": "CHILD", "Ids": ["c5c7b99a-9973-4a9d-abc7-18facc269e92"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRY", "Geometry": {"BoundingBox": {"Width": 0.03375209495425224, "Height": 0.009066986851394176, "Left": 0.2294788360595703, "Top": 0.2890565097332001}, "Polygon": [{"X": 0.22947904467582703, "Y": 0.2890565097332001}, {"X": 0.26323091983795166, "Y": 0.28906047344207764}, {"X": 0.26323068141937256, "Y": 0.29812347888946533}, {"X": 0.2294788360595703, "Y": 0.29811951518058777}]}, "Id": "01433c95-77dc-497e-b9dd-35f8ebeac938", "Relationships": [{"Type": "CHILD", "Ids": ["783df1fa-e296-425c-9c2d-2bdb919849cc"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.036937858909368515, "Height": 0.008981837891042233, "Left": 0.027047909796237946, "Top": 0.30718520283699036}, "Polygon": [{"X": 0.02704794891178608, "Y": 0.30718520283699036}, {"X": 0.06398576498031616, "Y": 0.30718955397605896}, {"X": 0.06398569792509079, "Y": 0.31616702675819397}, {"X": 0.027047909796237946, "Y": 0.31616267561912537}]}, "Id": "eea657c8-df5a-4c21-ac40-921651e94a64", "Relationships": [{"Type": "CHILD", "Ids": ["aaf4bc98-dc95-498c-9fa8-c8c4136cbf7b"]}]}, {"BlockType": "LINE", "Confidence": 99.83159637451172, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.008386456407606602, "Height": 0.008801685646176338, "Left": 0.22920601069927216, "Top": 0.307125449180603}, "Polygon": [{"X": 0.22920621931552887, "Y": 0.307125449180603}, {"X": 0.23759247362613678, "Y": 0.3071264326572418}, {"X": 0.23759225010871887, "Y": 0.315927118062973}, {"X": 0.22920601069927216, "Y": 0.31592613458633423}]}, "Id": "c55d61fe-d3ad-4e2c-946a-c541a8648be2", "Relationships": [{"Type": "CHILD", "Ids": ["60f8cd51-9cb8-4095-b6dd-b4508ce1207e"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Purchase Orders", "Geometry": {"BoundingBox": {"Width": 0.10652235895395279, "Height": 0.009368708357214928, "Left": 0.026951909065246582, "Top": 0.3322124779224396}, "Polygon": [{"X": 0.026951950043439865, "Y": 0.3322124779224396}, {"X": 0.13347427546977997, "Y": 0.33222508430480957}, {"X": 0.13347414135932922, "Y": 0.34158119559288025}, {"X": 0.026951909065246582, "Y": 0.34156858921051025}]}, "Id": "3c0cc361-9dda-469a-89a4-6710e4d4e2b5", "Relationships": [{"Type": "CHILD", "Ids": ["1de9a1f9-03e9-4abe-b5b6-e912fa9b2c4e", "726ada21-2f7f-4df2-a0bc-30f554e3417a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.048923101276159286, "Height": 0.00930540356785059, "Left": 0.22955836355686188, "Top": 0.3322174847126007}, "Polygon": [{"X": 0.22955858707427979, "Y": 0.3322174847126007}, {"X": 0.27848145365715027, "Y": 0.3322232663631439}, {"X": 0.2784811854362488, "Y": 0.3415229022502899}, {"X": 0.22955836355686188, "Y": 0.3415170907974243}]}, "Id": "90a8ca1d-997f-4d63-bc28-8d31044603f7", "Relationships": [{"Type": "CHILD", "Ids": ["09ba476f-3cd8-4fe1-8a90-b2b53321f716"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "**********", "Geometry": {"BoundingBox": {"Width": 0.07912813872098923, "Height": 0.00898846983909607, "Left": 0.026827482506632805, "Top": 0.3503831923007965}, "Polygon": [{"X": 0.02682752162218094, "Y": 0.3503831923007965}, {"X": 0.10595562309026718, "Y": 0.35039255023002625}, {"X": 0.10595551878213882, "Y": 0.3593716621398926}, {"X": 0.026827482506632805, "Y": 0.35936227440834045}]}, "Id": "e0833ec0-a2f8-434b-a55e-3691768c4b2b", "Relationships": [{"Type": "CHILD", "Ids": ["a396c3cf-1174-4bb2-bdc0-21f61a777d45"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "ANCHOR PACKAGING", "Geometry": {"BoundingBox": {"Width": 0.16713443398475647, "Height": 0.009070197120308876, "Left": 0.22949178516864777, "Top": 0.3503452241420746}, "Polygon": [{"X": 0.22949199378490448, "Y": 0.3503452241420746}, {"X": 0.39662620425224304, "Y": 0.3503650426864624}, {"X": 0.3966258466243744, "Y": 0.3594154417514801}, {"X": 0.22949178516864777, "Y": 0.3593956232070923}]}, "Id": "2497be36-be45-4a5f-b52e-edd54d05c025", "Relationships": [{"Type": "CHILD", "Ids": ["5aa813bf-2171-4d38-bd45-ec058264ff4b", "f8850c37-7824-491a-bd66-5c1366c4b769"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Total Initial Pallets:", "Geometry": {"BoundingBox": {"Width": 0.12749461829662323, "Height": 0.009317540563642979, "Left": 0.027044719085097313, "Top": 0.39345666766166687}, "Polygon": [{"X": 0.027044758200645447, "Y": 0.39345666766166687}, {"X": 0.1545393317937851, "Y": 0.3934718370437622}, {"X": 0.15453918278217316, "Y": 0.40277421474456787}, {"X": 0.027044719085097313, "Y": 0.40275904536247253}]}, "Id": "2e0ffcb8-2746-45bd-bc22-0efa18de91de", "Relationships": [{"Type": "CHILD", "Ids": ["121843d3-3324-4c50-8991-78fa3e005edd", "436dc3f3-40dd-42c7-89da-581dc4ec2143", "9591e78a-9259-4c7c-a823-76ba10000fc1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "29.00", "Geometry": {"BoundingBox": {"Width": 0.03657899424433708, "Height": 0.009235360659658909, "Left": 0.22921116650104523, "Top": 0.39362114667892456}, "Polygon": [{"X": 0.22921139001846313, "Y": 0.39362114667892456}, {"X": 0.2657901644706726, "Y": 0.39362549781799316}, {"X": 0.2657899260520935, "Y": 0.4028564989566803}, {"X": 0.22921116650104523, "Y": 0.4028521478176117}]}, "Id": "8ed88cf9-1277-44eb-9ebb-0027a582a7ea", "Relationships": [{"Type": "CHILD", "Ids": ["98c11ae1-21ef-48bb-a4f4-5fc61571ff8f"]}]}, {"BlockType": "LINE", "Confidence": 99.97342681884766, "Text": "Total Finished Pallets:", "Geometry": {"BoundingBox": {"Width": 0.1448148638010025, "Height": 0.009256972000002861, "Left": 0.027003366500139236, "Top": 0.411487877368927}, "Polygon": [{"X": 0.02700340561568737, "Y": 0.411487877368927}, {"X": 0.17181822657585144, "Y": 0.4115051329135895}, {"X": 0.1718180626630783, "Y": 0.4207448661327362}, {"X": 0.027003366500139236, "Y": 0.42072761058807373}]}, "Id": "6390ab6f-c4b5-40da-9c64-9655645553dd", "Relationships": [{"Type": "CHILD", "Ids": ["ffe9a4c8-b553-4a86-8eed-70947f39aa09", "1b3491e9-6243-45b0-90d1-28b9850478a1", "9ab3db20-7cd3-4dce-ae1d-f7ba624f5884"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "64", "Geometry": {"BoundingBox": {"Width": 0.01594833843410015, "Height": 0.008988077752292156, "Left": 0.22958111763000488, "Top": 0.41168174147605896}, "Polygon": [{"X": 0.2295813262462616, "Y": 0.41168174147605896}, {"X": 0.24552945792675018, "Y": 0.4116836488246918}, {"X": 0.24552923440933228, "Y": 0.420669823884964}, {"X": 0.22958111763000488, "Y": 0.4206679165363312}]}, "Id": "14ff6cd5-3010-41cf-8834-79011889dff6", "Relationships": [{"Type": "CHILD", "Ids": ["3f58e6f5-fe18-437c-93bf-ec0b730562dc"]}]}, {"BlockType": "LINE", "Confidence": 99.97342681884766, "Text": "Total Case Count:", "Geometry": {"BoundingBox": {"Width": 0.11662357300519943, "Height": 0.009298399090766907, "Left": 0.02705564722418785, "Top": 0.42948758602142334}, "Polygon": [{"X": 0.027055686339735985, "Y": 0.42948758602142334}, {"X": 0.14367921650409698, "Y": 0.4295015037059784}, {"X": 0.14367906749248505, "Y": 0.43878597021102905}, {"X": 0.02705564722418785, "Y": 0.438772052526474}]}, "Id": "7f23880e-1468-4208-b7fa-ca984d5ec5b9", "Relationships": [{"Type": "CHILD", "Ids": ["67534483-9ab2-4609-a220-c89a3417e857", "ee362a5e-42f4-4e47-bfec-abfc510ead38", "ae210092-a6f2-4d24-9149-272cdb03fd70"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1056", "Geometry": {"BoundingBox": {"Width": 0.03103836067020893, "Height": 0.009258665144443512, "Left": 0.2308683842420578, "Top": 0.42957571148872375}, "Polygon": [{"X": 0.2308686077594757, "Y": 0.42957571148872375}, {"X": 0.2619067430496216, "Y": 0.42957940697669983}, {"X": 0.2619065046310425, "Y": 0.43883436918258667}, {"X": 0.2308683842420578, "Y": 0.4388306736946106}]}, "Id": "7665859c-0ae2-4ea1-b88d-6c9fba9e7bb7", "Relationships": [{"Type": "CHILD", "Ids": ["31b0cc3d-be0c-46ab-93b4-38571cd6dac0"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Trailer Number:", "Geometry": {"BoundingBox": {"Width": 0.10510827600955963, "Height": 0.009347258135676384, "Left": 0.02687457762658596, "Top": 0.4475553631782532}, "Polygon": [{"X": 0.026874616742134094, "Y": 0.4475553631782532}, {"X": 0.13198286294937134, "Y": 0.4475679397583008}, {"X": 0.1319827288389206, "Y": 0.4569026231765747}, {"X": 0.02687457762658596, "Y": 0.4568900763988495}]}, "Id": "86cefb82-1e5d-4b81-ab92-3a7e39ee9771", "Relationships": [{"Type": "CHILD", "Ids": ["5718b774-97c2-411e-a5c8-3a723ea93350", "62269dc4-fcd0-4d24-9f13-40c4c4d64ded"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "01", "Geometry": {"BoundingBox": {"Width": 0.014559531584382057, "Height": 0.00883022416383028, "Left": 0.22946269810199738, "Top": 0.4476981461048126}, "Polygon": [{"X": 0.2294629067182541, "Y": 0.4476981461048126}, {"X": 0.2440222203731537, "Y": 0.4476998746395111}, {"X": 0.24402201175689697, "Y": 0.45652836561203003}, {"X": 0.22946269810199738, "Y": 0.45652663707733154}]}, "Id": "53ac9d29-5978-4db1-870f-b714d15dd8bc", "Relationships": [{"Type": "CHILD", "Ids": ["148f48dc-7993-4cf6-80f2-d08f6935afc4"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Tractor Number:", "Geometry": {"BoundingBox": {"Width": 0.10832668840885162, "Height": 0.009369396604597569, "Left": 0.026937171816825867, "Top": 0.4654642641544342}, "Polygon": [{"X": 0.026937210932374, "Y": 0.4654642641544342}, {"X": 0.1352638602256775, "Y": 0.46547722816467285}, {"X": 0.13526372611522675, "Y": 0.4748336672782898}, {"X": 0.026937171816825867, "Y": 0.47482070326805115}]}, "Id": "651f0eb3-f5e4-4e13-a47b-591b788864d1", "Relationships": [{"Type": "CHILD", "Ids": ["4604bbee-4ba3-4fe6-8a12-0bc7e4ceb80f", "32045141-c7b8-4884-8081-0392380b2df6"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "BOL:", "Geometry": {"BoundingBox": {"Width": 0.035833217203617096, "Height": 0.008918074890971184, "Left": 0.027086904272437096, "Top": 0.4837033450603485}, "Polygon": [{"X": 0.02708694152534008, "Y": 0.4837033450603485}, {"X": 0.06292012333869934, "Y": 0.48370763659477234}, {"X": 0.06292004883289337, "Y": 0.49262142181396484}, {"X": 0.027086904272437096, "Y": 0.492617130279541}]}, "Id": "e5a41e93-58c0-4553-bbd1-36f6c5be254b", "Relationships": [{"Type": "CHILD", "Ids": ["f3728473-9954-49a0-a26f-0559d90ffd96"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Comments:", "Geometry": {"BoundingBox": {"Width": 0.07346145063638687, "Height": 0.009278757497668266, "Left": 0.027359645813703537, "Top": 0.5016858577728271}, "Polygon": [{"X": 0.02735968679189682, "Y": 0.5016858577728271}, {"X": 0.1008211001753807, "Y": 0.5016946792602539}, {"X": 0.10082098841667175, "Y": 0.5109646320343018}, {"X": 0.027359645813703537, "Y": 0.510955810546875}]}, "Id": "64614aca-f258-4956-896e-0a60765d579a", "Relationships": [{"Type": "CHILD", "Ids": ["14318187-fe81-4d2c-a995-5c50927dbdae"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Canned Comments:", "Geometry": {"BoundingBox": {"Width": 0.12695752084255219, "Height": 0.0092924190685153, "Left": 0.027249695733189583, "Top": 0.5195531249046326}, "Polygon": [{"X": 0.027249736711382866, "Y": 0.5195531249046326}, {"X": 0.15420721471309662, "Y": 0.5195683836936951}, {"X": 0.15420706570148468, "Y": 0.5288455486297607}, {"X": 0.027249695733189583, "Y": 0.5288302898406982}]}, "Id": "1676bb61-e6f2-4c5c-bb3f-6340ffeb11f4", "Relationships": [{"Type": "CHILD", "Ids": ["d0065504-b002-411a-98f8-1f2209f578e7", "54839d48-388c-4cd5-885d-51e8b08a62e8"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Unloaders:", "Geometry": {"BoundingBox": {"Width": 0.07015246152877808, "Height": 0.00937582552433014, "Left": 0.02715350314974785, "Top": 0.5376774668693542}, "Polygon": [{"X": 0.02715354412794113, "Y": 0.5376774668693542}, {"X": 0.09730596095323563, "Y": 0.5376859307289124}, {"X": 0.09730586409568787, "Y": 0.5470532774925232}, {"X": 0.02715350314974785, "Y": 0.5470448136329651}]}, "Id": "02515640-9d00-4aa2-802c-4bc4e6bf178d", "Relationships": [{"Type": "CHILD", "Ids": ["c3f78453-f2e3-4f5c-b3c0-2f0ed9752c5c"]}]}, {"BlockType": "LINE", "Confidence": 98.28662872314453, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004733589477837086, "Height": 0.009089834988117218, "Left": 0.2309475988149643, "Top": 0.5378982424736023}, "Polygon": [{"X": 0.2309478223323822, "Y": 0.5378982424736023}, {"X": 0.2356811910867691, "Y": 0.5378987789154053}, {"X": 0.2356809675693512, "Y": 0.5469880700111389}, {"X": 0.2309475988149643, "Y": 0.5469874739646912}]}, "Id": "2ce5fcaa-8cbf-4ca6-a227-c2e105e8f7fb", "Relationships": [{"Type": "CHILD", "Ids": ["d7dd9543-60d0-420f-9587-d81289fe74e6"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Add Fee", "Geometry": {"BoundingBox": {"Width": 0.05525873601436615, "Height": 0.009325336664915085, "Left": 0.027160637080669403, "Top": 0.5628369450569153}, "Polygon": [{"X": 0.027160676196217537, "Y": 0.5628369450569153}, {"X": 0.08241937309503555, "Y": 0.5628436207771301}, {"X": 0.08241928368806839, "Y": 0.5721622705459595}, {"X": 0.027160637080669403, "Y": 0.5721555948257446}]}, "Id": "35a31213-5522-46e2-a9b4-24b722b9cc5d", "Relationships": [{"Type": "CHILD", "Ids": ["4a4e35a1-fb27-4b28-9579-134ef388ef08", "a173fe16-dc49-47ac-9b9b-cf8302748bff"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "PO: **********", "Geometry": {"BoundingBox": {"Width": 0.10777739435434341, "Height": 0.009053613059222698, "Left": 0.02726420946419239, "Top": 0.5811479687690735}, "Polygon": [{"X": 0.027264248579740524, "Y": 0.5811479687690735}, {"X": 0.13504160940647125, "Y": 0.5811609625816345}, {"X": 0.1350414752960205, "Y": 0.5902015566825867}, {"X": 0.02726420946419239, "Y": 0.5901885032653809}]}, "Id": "a7d25f93-0526-4db9-9f22-548c7ffc8e51", "Relationships": [{"Type": "CHILD", "Ids": ["c0bc63fa-94b6-404a-8db7-7440af6a9330", "7eeb839a-f693-4203-891d-52b830ea9030"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Pinwheeled", "Geometry": {"BoundingBox": {"Width": 0.0756034106016159, "Height": 0.009249738417565823, "Left": 0.057833366096019745, "Top": 0.5988349318504333}, "Polygon": [{"X": 0.05783343315124512, "Y": 0.5988349318504333}, {"X": 0.13343678414821625, "Y": 0.5988441109657288}, {"X": 0.1334366500377655, "Y": 0.6080846786499023}, {"X": 0.057833366096019745, "Y": 0.6080754995346069}]}, "Id": "de51abcc-3382-4147-b6b0-f8cbb9af62e4", "Relationships": [{"Type": "CHILD", "Ids": ["dbd730a8-3754-46d5-a948-dc6cdf07820a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$40.00", "Geometry": {"BoundingBox": {"Width": 0.04438871145248413, "Height": 0.010224013589322567, "Left": 0.2291085124015808, "Top": 0.5985558032989502}, "Polygon": [{"X": 0.2291087508201599, "Y": 0.5985558032989502}, {"X": 0.27349722385406494, "Y": 0.59856116771698}, {"X": 0.27349692583084106, "Y": 0.6087798476219177}, {"X": 0.2291085124015808, "Y": 0.6087744832038879}]}, "Id": "7b634b61-29c2-4c3f-aa00-ce8b271bc805", "Relationships": [{"Type": "CHILD", "Ids": ["7c046a54-aeef-42a7-9118-4aba5b168782"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "Total Add Charges:", "Geometry": {"BoundingBox": {"Width": 0.12671658396720886, "Height": 0.01180282887071371, "Left": 0.026989977806806564, "Top": 0.6168659329414368}, "Polygon": [{"X": 0.026990028098225594, "Y": 0.6168659329414368}, {"X": 0.15370656549930573, "Y": 0.6168813109397888}, {"X": 0.1537063717842102, "Y": 0.6286687850952148}, {"X": 0.026989977806806564, "Y": 0.6286534070968628}]}, "Id": "0e08ab94-7f17-42b6-ba98-b6167a661075", "Relationships": [{"Type": "CHILD", "Ids": ["ca7dd5d0-f408-4306-87a8-96e95e6a8488", "b376d9cb-03a0-4410-ac42-7157f6cdac3a", "c01b0468-b285-4239-921c-66f841f37f22"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$40.00", "Geometry": {"BoundingBox": {"Width": 0.04440949484705925, "Height": 0.010277644731104374, "Left": 0.2293684035539627, "Top": 0.6166789531707764}, "Polygon": [{"X": 0.2293686419725418, "Y": 0.6166789531707764}, {"X": 0.27377790212631226, "Y": 0.6166843175888062}, {"X": 0.2737776041030884, "Y": 0.626956582069397}, {"X": 0.2293684035539627, "Y": 0.6269512176513672}]}, "Id": "337e4293-bb30-4572-8bc4-2049b994abf6", "Relationships": [{"Type": "CHILD", "Ids": ["c01d2cb6-27dd-43a5-9675-8b59feaa299b"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Base Charge:", "Geometry": {"BoundingBox": {"Width": 0.08555785566568375, "Height": 0.011935851536691189, "Left": 0.027243809774518013, "Top": 0.6348451972007751}, "Polygon": [{"X": 0.02724386192858219, "Y": 0.6348451972007751}, {"X": 0.11280166357755661, "Y": 0.6348555684089661}, {"X": 0.11280151456594467, "Y": 0.6467810273170471}, {"X": 0.027243809774518013, "Y": 0.6467706561088562}]}, "Id": "fd8f16f4-bb54-40b6-b214-18030dca1259", "Relationships": [{"Type": "CHILD", "Ids": ["f1499a0c-0315-46f0-92e2-41aa580021c1", "63bbd3a6-68c6-43c0-be12-fc577410cbd8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "310.00", "Geometry": {"BoundingBox": {"Width": 0.04448733106255531, "Height": 0.00908602774143219, "Left": 0.22930239140987396, "Top": 0.6350730061531067}, "Polygon": [{"X": 0.22930260002613068, "Y": 0.6350730061531067}, {"X": 0.27378973364830017, "Y": 0.6350784301757812}, {"X": 0.2737894654273987, "Y": 0.6441590189933777}, {"X": 0.22930239140987396, "Y": 0.6441536545753479}]}, "Id": "dcb89f34-cb9d-4b98-a6b0-fd777b2b57b8", "Relationships": [{"Type": "CHILD", "Ids": ["bcaf8310-f546-4dd0-a1f0-b6faf9c83045"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Convenience Fee:", "Geometry": {"BoundingBox": {"Width": 0.11529774963855743, "Height": 0.009377223439514637, "Left": 0.02732766419649124, "Top": 0.6527150273323059}, "Polygon": [{"X": 0.027327703312039375, "Y": 0.6527150273323059}, {"X": 0.14262542128562927, "Y": 0.6527290344238281}, {"X": 0.14262527227401733, "Y": 0.6620922684669495}, {"X": 0.02732766419649124, "Y": 0.6620782017707825}]}, "Id": "56dd8a16-d205-4f60-840e-1d7753b15ca7", "Relationships": [{"Type": "CHILD", "Ids": ["4af9f21f-7810-4cea-9bd7-b816e7a42dc2", "21521cf5-8f44-4b22-a15d-80b86a720cc4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "10.00", "Geometry": {"BoundingBox": {"Width": 0.035167016088962555, "Height": 0.008965395390987396, "Left": 0.23067030310630798, "Top": 0.6532025933265686}, "Polygon": [{"X": 0.2306705117225647, "Y": 0.6532025933265686}, {"X": 0.26583731174468994, "Y": 0.6532068848609924}, {"X": 0.26583707332611084, "Y": 0.662168025970459}, {"X": 0.23067030310630798, "Y": 0.6621637344360352}]}, "Id": "1447757b-e602-420f-8e38-fa61184123b9", "Relationships": [{"Type": "CHILD", "Ids": ["b075fcdc-e260-4566-95ba-9f7b8246dc80"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "Total Cost:", "Geometry": {"BoundingBox": {"Width": 0.07136216759681702, "Height": 0.009118843823671341, "Left": 0.027079496532678604, "Top": 0.671040415763855}, "Polygon": [{"X": 0.027079535648226738, "Y": 0.671040415763855}, {"X": 0.09844166785478592, "Y": 0.6710491180419922}, {"X": 0.09844156354665756, "Y": 0.6801592707633972}, {"X": 0.027079496532678604, "Y": 0.68015056848526}]}, "Id": "9c0904ab-fb1c-491e-a2a0-fb96d372df81", "Relationships": [{"Type": "CHILD", "Ids": ["35253bf7-3486-4d43-815f-b65bb989fb0f", "df939e0b-e0c0-45c7-bc19-5437bacf439b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "360.00", "Geometry": {"BoundingBox": {"Width": 0.04432360455393791, "Height": 0.009055864997208118, "Left": 0.22964906692504883, "Top": 0.670859158039093}, "Polygon": [{"X": 0.22964927554130554, "Y": 0.670859158039093}, {"X": 0.27397266030311584, "Y": 0.6708645820617676}, {"X": 0.27397242188453674, "Y": 0.6799150109291077}, {"X": 0.22964906692504883, "Y": 0.6799096465110779}]}, "Id": "d920bc7a-ab7b-41a3-8f4c-e808f7822266", "Relationships": [{"Type": "CHILD", "Ids": ["20446be4-5e88-41ce-8875-53f92a72fd39"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payments:", "Geometry": {"BoundingBox": {"Width": 0.06650935113430023, "Height": 0.011521782726049423, "Left": 0.027063259854912758, "Top": 0.6962609887123108}, "Polygon": [{"X": 0.027063310146331787, "Y": 0.6962609887123108}, {"X": 0.09357260912656784, "Y": 0.6962690949440002}, {"X": 0.09357248991727829, "Y": 0.7077827453613281}, {"X": 0.027063259854912758, "Y": 0.7077746391296387}]}, "Id": "b965c73a-7c29-4e4b-82ef-bdfeabd259b8", "Relationships": [{"Type": "CHILD", "Ids": ["48ce64e4-5300-40fe-8546-98fe69b8fcb5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.0531301386654377, "Height": 0.009000932797789574, "Left": 0.22964595258235931, "Top": 0.696476936340332}, "Polygon": [{"X": 0.22964616119861603, "Y": 0.696476936340332}, {"X": 0.2827760875225067, "Y": 0.6964834332466125}, {"X": 0.2827758491039276, "Y": 0.7054778933525085}, {"X": 0.22964595258235931, "Y": 0.705471396446228}]}, "Id": "315d8771-5089-4a02-aa78-fde997ad1f4e", "Relationships": [{"Type": "CHILD", "Ids": ["9d12a247-cfbf-4061-b453-5fe2cb2d12ac"]}]}, {"BlockType": "LINE", "Confidence": 99.51191711425781, "Text": "CapstonePay-", "Geometry": {"BoundingBox": {"Width": 0.08933208137750626, "Height": 0.012161935679614544, "Left": 0.05822177976369858, "Top": 0.7139973044395447}, "Polygon": [{"X": 0.05822186544537544, "Y": 0.7139973044395447}, {"X": 0.14755386114120483, "Y": 0.7140082120895386}, {"X": 0.14755366742610931, "Y": 0.7261592149734497}, {"X": 0.05822177976369858, "Y": 0.7261483073234558}]}, "Id": "e452319c-7d14-40b1-94d9-97a19dd51460", "Relationships": [{"Type": "CHILD", "Ids": ["26d69df9-1ea2-41f2-b44d-8eedd67cabe5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$360.00", "Geometry": {"BoundingBox": {"Width": 0.05220530927181244, "Height": 0.009791131131350994, "Left": 0.22961415350437164, "Top": 0.7141082286834717}, "Polygon": [{"X": 0.22961437702178955, "Y": 0.7141082286834717}, {"X": 0.2818194627761841, "Y": 0.7141146063804626}, {"X": 0.2818191647529602, "Y": 0.7238993644714355}, {"X": 0.22961415350437164, "Y": 0.7238929867744446}]}, "Id": "ad7ef9a7-ec94-44d7-a117-980b884d4841", "Relationships": [{"Type": "CHILD", "Ids": ["02ac310a-c2bc-48e2-9123-bac1f8330ea9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "38053037", "Geometry": {"BoundingBox": {"Width": 0.06398124247789383, "Height": 0.008965009823441505, "Left": 0.05843393877148628, "Top": 0.7282271981239319}, "Polygon": [{"X": 0.058434005826711655, "Y": 0.7282271981239319}, {"X": 0.12241518497467041, "Y": 0.7282350063323975}, {"X": 0.12241505831480026, "Y": 0.7371922135353088}, {"X": 0.05843393877148628, "Y": 0.7371844053268433}]}, "Id": "dbfdea44-a764-46df-8f3c-a6758fb8ace5", "Relationships": [{"Type": "CHILD", "Ids": ["ab312a7d-fadf-491b-8bcc-63c62af14ee5"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "Total Payments", "Geometry": {"BoundingBox": {"Width": 0.10100814700126648, "Height": 0.011801310814917088, "Left": 0.05770012363791466, "Top": 0.7503200173377991}, "Polygon": [{"X": 0.05770020931959152, "Y": 0.7503200173377991}, {"X": 0.15870827436447144, "Y": 0.7503324151039124}, {"X": 0.15870806574821472, "Y": 0.762121319770813}, {"X": 0.05770012363791466, "Y": 0.7621089220046997}]}, "Id": "3bb412c0-7a44-4285-ba05-6a316a407488", "Relationships": [{"Type": "CHILD", "Ids": ["598e8b8c-188c-4039-9309-a75dd219de78", "c1cec2c1-c224-4319-9302-a18af67e86a9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$360.00", "Geometry": {"BoundingBox": {"Width": 0.052335336804389954, "Height": 0.00954828504472971, "Left": 0.22791503369808197, "Top": 0.7502108216285706}, "Polygon": [{"X": 0.22791525721549988, "Y": 0.7502108216285706}, {"X": 0.2802503705024719, "Y": 0.7502172589302063}, {"X": 0.28025010228157043, "Y": 0.7597591280937195}, {"X": 0.22791503369808197, "Y": 0.7597526907920837}]}, "Id": "ed99882d-ee25-4d72-a78b-f49f27d6b839", "Relationships": [{"Type": "CHILD", "Ids": ["f80e6848-f368-4dee-9129-21c1dfeeeb99"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Capstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1494193822145462, "Height": 0.027745593339204788, "Left": 0.07957328855991364, "Top": 0.010530420579016209}, "Polygon": [{"X": 0.07957354187965393, "Y": 0.010530420579016209}, {"X": 0.22899267077445984, "Y": 0.010547596029937267}, {"X": 0.2289920151233673, "Y": 0.03827601298689842}, {"X": 0.07957328855991364, "Y": 0.03825879469513893}]}, "Id": "c38169f8-a080-4b6f-9f9c-0a2d66a24d7d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14706602692604065, "Height": 0.028244096785783768, "Left": 0.23973886668682098, "Top": 0.010038215667009354}, "Polygon": [{"X": 0.2397395521402359, "Y": 0.010038215667009354}, {"X": 0.38680487871170044, "Y": 0.0100551201030612}, {"X": 0.3868038058280945, "Y": 0.03828231245279312}, {"X": 0.23973886668682098, "Y": 0.03826536610722542}]}, "Id": "94c54ee6-aaa4-466c-8b19-46d8ac3744f1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015733642503619194, "Height": 0.008973031304776669, "Left": 0.07423622161149979, "Top": 0.05635606497526169}, "Polygon": [{"X": 0.07423629611730576, "Y": 0.05635606497526169}, {"X": 0.08996985852718353, "Y": 0.05635787919163704}, {"X": 0.08996976912021637, "Y": 0.06532909721136093}, {"X": 0.07423622161149979, "Y": 0.06532727926969528}]}, "Id": "0901960b-ba95-44c7-b6df-8ec735d67eb5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TECHNOLOGY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11085470020771027, "Height": 0.008939475752413273, "Left": 0.09413416683673859, "Top": 0.056276094168424606}, "Polygon": [{"X": 0.09413426369428635, "Y": 0.056276094168424606}, {"X": 0.20498886704444885, "Y": 0.056288886815309525}, {"X": 0.20498868823051453, "Y": 0.065215565264225}, {"X": 0.09413416683673859, "Y": 0.06520276516675949}]}, "Id": "20296265-b5a2-4514-bc56-736e109bfe97"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PKWY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04734938219189644, "Height": 0.008865746669471264, "Left": 0.20867565274238586, "Top": 0.05629826709628105}, "Polygon": [{"X": 0.20867584645748138, "Y": 0.05629826709628105}, {"X": 0.2560250461101532, "Y": 0.056303732097148895}, {"X": 0.2560248076915741, "Y": 0.06516401469707489}, {"X": 0.20867565274238586, "Y": 0.06515854597091675}]}, "Id": "f771d633-c9f2-4c95-9ed4-60a8baa916d0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "SOUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053366757929325104, "Height": 0.009127733297646046, "Left": 0.2607370913028717, "Top": 0.05627206340432167}, "Polygon": [{"X": 0.2607373595237732, "Y": 0.05627206340432167}, {"X": 0.3141038715839386, "Y": 0.05627822503447533}, {"X": 0.3141035735607147, "Y": 0.06539979577064514}, {"X": 0.2607370913028717, "Y": 0.06539363414049149}]}, "Id": "da289fe2-e79d-4b48-94d7-b51ca353413b"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "SUITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04525657370686531, "Height": 0.009137333370745182, "Left": 0.318814754486084, "Top": 0.056243136525154114}, "Polygon": [{"X": 0.31881505250930786, "Y": 0.056243136525154114}, {"X": 0.3640713095664978, "Y": 0.056248363107442856}, {"X": 0.36407098174095154, "Y": 0.06538046896457672}, {"X": 0.318814754486084, "Y": 0.06537524610757828}]}, "Id": "421069f7-f85a-46a7-966e-7d4e40333fc1"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024200573563575745, "Height": 0.008811654523015022, "Left": 0.3683287501335144, "Top": 0.0563899502158165}, "Polygon": [{"X": 0.36832907795906067, "Y": 0.0563899502158165}, {"X": 0.39252930879592896, "Y": 0.056392744183540344}, {"X": 0.3925289809703827, "Y": 0.06520160287618637}, {"X": 0.3683287501335144, "Y": 0.06519880890846252}]}, "Id": "7209ce65-6f08-4213-8457-5b9435074135"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "PEACHTREE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09339497983455658, "Height": 0.008960944600403309, "Left": 0.1091102659702301, "Top": 0.07037527114152908}, "Polygon": [{"X": 0.10911037772893906, "Y": 0.07037527114152908}, {"X": 0.20250524580478668, "Y": 0.07038606703281403}, {"X": 0.20250505208969116, "Y": 0.07933621853590012}, {"X": 0.1091102659702301, "Y": 0.07932541519403458}]}, "Id": "5b68aeff-ddfc-4e23-9214-b791c1cb04bc"}, {"BlockType": "WORD", "Confidence": 99.12308502197266, "Text": "CORNERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07776647061109543, "Height": 0.010763821192085743, "Left": 0.2068370282649994, "Top": 0.07035163789987564}, "Polygon": [{"X": 0.2068372517824173, "Y": 0.07035163789987564}, {"X": 0.2846035063266754, "Y": 0.07036062330007553}, {"X": 0.28460317850112915, "Y": 0.08111546188592911}, {"X": 0.2068370282649994, "Y": 0.08110646158456802}]}, "Id": "b85c1edf-a1d8-437a-9345-187f2ca1c729"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023270750418305397, "Height": 0.009145139716565609, "Left": 0.28933700919151306, "Top": 0.07023533433675766}, "Polygon": [{"X": 0.28933727741241455, "Y": 0.07023533433675766}, {"X": 0.3126077651977539, "Y": 0.07023801654577255}, {"X": 0.31260746717453003, "Y": 0.07938046753406525}, {"X": 0.28933700919151306, "Y": 0.07937777787446976}]}, "Id": "5af1c5c5-6f2a-433d-807f-8a0ec690861b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "30092", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04012000933289528, "Height": 0.008971109054982662, "Left": 0.31664586067199707, "Top": 0.07039084285497665}, "Polygon": [{"X": 0.31664615869522095, "Y": 0.07039084285497665}, {"X": 0.35676589608192444, "Y": 0.07039548456668854}, {"X": 0.3567655682563782, "Y": 0.07936195284128189}, {"X": 0.31664586067199707, "Y": 0.0793573185801506}]}, "Id": "fc0694a9-dc2b-4583-8c7b-6cfc5d104890"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "770-414-1929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09149593859910965, "Height": 0.009205000475049019, "Left": 0.18756142258644104, "Top": 0.08390548080205917}, "Polygon": [{"X": 0.18756160140037537, "Y": 0.08390548080205917}, {"X": 0.2790573537349701, "Y": 0.0839160680770874}, {"X": 0.279057115316391, "Y": 0.09311047941446304}, {"X": 0.18756142258644104, "Y": 0.09309988468885422}]}, "Id": "3839954c-2564-4c65-b934-83d15e5e8be7"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "FED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030149666592478752, "Height": 0.009202331304550171, "Left": 0.16271962225437164, "Top": 0.09790196269750595}, "Polygon": [{"X": 0.16271978616714478, "Y": 0.09790196269750595}, {"X": 0.19286929070949554, "Y": 0.09790544956922531}, {"X": 0.19286909699440002, "Y": 0.10710429400205612}, {"X": 0.16271962225437164, "Y": 0.10710079222917557}]}, "Id": "414604df-9883-46b0-8417-414dd2d0a00b"}, {"BlockType": "WORD", "Confidence": 99.57848358154297, "Text": "ID#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02525905705988407, "Height": 0.009093874134123325, "Left": 0.197195366024971, "Top": 0.09789891541004181}, "Polygon": [{"X": 0.19719554483890533, "Y": 0.09789891541004181}, {"X": 0.22245441377162933, "Y": 0.0979018360376358}, {"X": 0.22245420515537262, "Y": 0.10699278861284256}, {"X": 0.197195366024971, "Y": 0.10698986053466797}]}, "Id": "15fe8a48-7b8a-4f3a-a877-db42a401605e"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "45-3087555", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07777949422597885, "Height": 0.009125135838985443, "Left": 0.22624970972537994, "Top": 0.0979529619216919}, "Polygon": [{"X": 0.22624991834163666, "Y": 0.0979529619216919}, {"X": 0.3040291965007782, "Y": 0.09796196967363358}, {"X": 0.3040289282798767, "Y": 0.10707809776067734}, {"X": 0.22624970972537994, "Y": 0.10706907510757446}]}, "Id": "61bfe649-7e08-4a1a-96f0-2e51772af442"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "08:32:49", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08529328554868698, "Height": 0.01341625303030014, "Left": 0.090040422976017, "Top": 0.1280396282672882}, "Polygon": [{"X": 0.09004056453704834, "Y": 0.1280396282672882}, {"X": 0.17533370852470398, "Y": 0.12804953753948212}, {"X": 0.17533347010612488, "Y": 0.14145588874816895}, {"X": 0.090040422976017, "Y": 0.14144596457481384}]}, "Id": "624b3699-4ed0-4835-97b8-98d510be898a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "September", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1038866713643074, "Height": 0.017316898331046104, "Left": 0.1827435940504074, "Top": 0.1278534233570099}, "Polygon": [{"X": 0.18274393677711487, "Y": 0.1278534233570099}, {"X": 0.2866302728652954, "Y": 0.1278654932975769}, {"X": 0.2866297662258148, "Y": 0.14517031610012054}, {"X": 0.1827435940504074, "Y": 0.14515823125839233}]}, "Id": "09987890-d369-42fe-8a2d-47356f59104f"}, {"BlockType": "WORD", "Confidence": 99.69647216796875, "Text": "23,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02959330566227436, "Height": 0.015944046899676323, "Left": 0.2919008433818817, "Top": 0.12817472219467163}, "Polygon": [{"X": 0.29190129041671753, "Y": 0.12817472219467163}, {"X": 0.32149413228034973, "Y": 0.1281781643629074}, {"X": 0.32149362564086914, "Y": 0.1441187709569931}, {"X": 0.2919008433818817, "Y": 0.14411532878875732}]}, "Id": "a2c5eeab-7433-4e4e-8f7a-e9efd62d1826"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047132860869169235, "Height": 0.013026597909629345, "Left": 0.32856160402297974, "Top": 0.12824323773384094}, "Polygon": [{"X": 0.32856202125549316, "Y": 0.12824323773384094}, {"X": 0.3756944537162781, "Y": 0.12824872136116028}, {"X": 0.3756939768791199, "Y": 0.14126983284950256}, {"X": 0.32856160402297974, "Y": 0.14126436412334442}]}, "Id": "ae9ae40f-3045-4fd0-81fb-cfb2e3ad4a94"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049397122114896774, "Height": 0.011676575988531113, "Left": 0.02709079161286354, "Top": 0.1626720279455185}, "Polygon": [{"X": 0.02709084190428257, "Y": 0.1626720279455185}, {"X": 0.07648791372776031, "Y": 0.16267777979373932}, {"X": 0.07648780941963196, "Y": 0.1743486076593399}, {"X": 0.02709079161286354, "Y": 0.17434284090995789}]}, "Id": "cb046ba1-1824-426c-b560-dd6414d59852"}, {"BlockType": "WORD", "Confidence": 99.1796875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012110182084143162, "Height": 0.009015269577503204, "Left": 0.08016562461853027, "Top": 0.16295044124126434}, "Polygon": [{"X": 0.08016571402549744, "Y": 0.16295044124126434}, {"X": 0.09227580577135086, "Y": 0.16295184195041656}, {"X": 0.0922757163643837, "Y": 0.17196570336818695}, {"X": 0.08016562461853027, "Y": 0.17196428775787354}]}, "Id": "d976f206-e4d9-4c5b-9e30-05a0d080188a"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "3b670798-9dde-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10665805637836456, "Height": 0.00964568741619587, "Left": 0.22967711091041565, "Top": 0.16264940798282623}, "Polygon": [{"X": 0.22967733442783356, "Y": 0.16264940798282623}, {"X": 0.3363351821899414, "Y": 0.1626618355512619}, {"X": 0.33633485436439514, "Y": 0.17229509353637695}, {"X": 0.22967711091041565, "Y": 0.17228266596794128}]}, "Id": "5e43cfbe-a676-4a13-b848-d3827ac8bb09"}, {"BlockType": "WORD", "Confidence": 98.9148178100586, "Text": "404c-a323-f8bbdf2dbdde", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.165127232670784, "Height": 0.00928560458123684, "Left": 0.22866462171077728, "Top": 0.17640194296836853}, "Polygon": [{"X": 0.2286648452281952, "Y": 0.17640194296836853}, {"X": 0.3937918543815613, "Y": 0.17642121016979218}, {"X": 0.3937914967536926, "Y": 0.18568754196166992}, {"X": 0.22866462171077728, "Y": 0.18566825985908508}]}, "Id": "01d7635e-2f08-4f38-96d4-85e25f09bbbd"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06126055866479874, "Height": 0.00911097601056099, "Left": 0.026779862120747566, "Top": 0.1989181786775589}, "Polygon": [{"X": 0.0267799012362957, "Y": 0.1989181786775589}, {"X": 0.08804041892290115, "Y": 0.19892534613609314}, {"X": 0.08804032951593399, "Y": 0.2080291509628296}, {"X": 0.026779862120747566, "Y": 0.20802198350429535}]}, "Id": "c26226d8-30e4-4ae0-82fc-be512a311c58"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "EDWARD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07070551067590714, "Height": 0.008899852633476257, "Left": 0.22936950623989105, "Top": 0.199043408036232}, "Polygon": [{"X": 0.22936972975730896, "Y": 0.199043408036232}, {"X": 0.3000750243663788, "Y": 0.19905167818069458}, {"X": 0.3000747561454773, "Y": 0.20794326066970825}, {"X": 0.22936950623989105, "Y": 0.20793499052524567}]}, "Id": "e26b19aa-11ce-4e47-bd0b-0c0d569ab321"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "DON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0351346954703331, "Height": 0.008888295851647854, "Left": 0.3043244183063507, "Top": 0.19902627170085907}, "Polygon": [{"X": 0.3043246865272522, "Y": 0.19902627170085907}, {"X": 0.3394591212272644, "Y": 0.19903038442134857}, {"X": 0.3394588232040405, "Y": 0.2079145759344101}, {"X": 0.3043244183063507, "Y": 0.2079104632139206}]}, "Id": "c6a3a3be-bb2e-4abd-81b9-5e24b54f89f9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DALLAS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06361980736255646, "Height": 0.009009131230413914, "Left": 0.3432278037071228, "Top": 0.19897982478141785}, "Polygon": [{"X": 0.34322813153266907, "Y": 0.19897982478141785}, {"X": 0.40684762597084045, "Y": 0.19898726046085358}, {"X": 0.4068472385406494, "Y": 0.2079889476299286}, {"X": 0.3432278037071228, "Y": 0.20798151195049286}]}, "Id": "56f0038e-ffd8-4372-ae38-f31eec4352bc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021806541830301285, "Height": 0.009093190543353558, "Left": 0.22926299273967743, "Top": 0.21249201893806458}, "Polygon": [{"X": 0.22926321625709534, "Y": 0.21249201893806458}, {"X": 0.2510695457458496, "Y": 0.21249456703662872}, {"X": 0.2510693073272705, "Y": 0.2215851992368698}, {"X": 0.22926299273967743, "Y": 0.22158265113830566}]}, "Id": "c45aa9aa-81fd-4408-a672-c9eba780f8de"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037191905081272125, "Height": 0.009044853039085865, "Left": 0.027036121115088463, "Top": 0.2349698692560196}, "Polygon": [{"X": 0.027036160230636597, "Y": 0.2349698692560196}, {"X": 0.06422802805900574, "Y": 0.2349742352962494}, {"X": 0.06422795355319977, "Y": 0.24401472508907318}, {"X": 0.027036121115088463, "Y": 0.24401035904884338}]}, "Id": "326f68a3-d3f7-4955-b55f-97dbb520e663"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0342133492231369, "Height": 0.008905453607439995, "Left": 0.0679624006152153, "Top": 0.23507794737815857}, "Polygon": [{"X": 0.06796247512102127, "Y": 0.23507794737815857}, {"X": 0.1021757498383522, "Y": 0.23508195579051971}, {"X": 0.10217565298080444, "Y": 0.2439834028482437}, {"X": 0.0679624006152153, "Y": 0.24397937953472137}]}, "Id": "4ec1db09-d730-4b4a-9809-9c824ec9454d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2025-09-23", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07467342913150787, "Height": 0.009046281687915325, "Left": 0.22946538031101227, "Top": 0.23502826690673828}, "Polygon": [{"X": 0.22946558892726898, "Y": 0.23502826690673828}, {"X": 0.30413880944252014, "Y": 0.23503701388835907}, {"X": 0.30413854122161865, "Y": 0.24407453835010529}, {"X": 0.22946538031101227, "Y": 0.2440657764673233}]}, "Id": "387a0a39-ce8e-4252-8090-aa615e7b2035"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023942643776535988, "Height": 0.009175731800496578, "Left": 0.02714214287698269, "Top": 0.252935528755188}, "Polygon": [{"X": 0.027142181992530823, "Y": 0.252935528755188}, {"X": 0.05108478665351868, "Y": 0.2529383599758148}, {"X": 0.0510847270488739, "Y": 0.26211127638816833}, {"X": 0.02714214287698269, "Y": 0.2621084451675415}]}, "Id": "e400e29a-7912-463a-af09-9b956827f409"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037669356912374496, "Height": 0.009260368533432484, "Left": 0.05543290823698044, "Top": 0.2528758645057678}, "Polygon": [{"X": 0.05543297156691551, "Y": 0.2528758645057678}, {"X": 0.09310226142406464, "Y": 0.2528803050518036}, {"X": 0.09310216456651688, "Y": 0.2621362507343292}, {"X": 0.05543290823698044, "Y": 0.26213181018829346}]}, "Id": "210ec0f8-83c5-4e09-9cfa-9b2b2ac82f25"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "RCOD30888", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08491439372301102, "Height": 0.009128590114414692, "Left": 0.2294970154762268, "Top": 0.2529875338077545}, "Polygon": [{"X": 0.22949722409248352, "Y": 0.2529875338077545}, {"X": 0.3144114017486572, "Y": 0.2529975175857544}, {"X": 0.31441113352775574, "Y": 0.26211613416671753}, {"X": 0.2294970154762268, "Y": 0.26210615038871765}]}, "Id": "6e6d9d42-8a11-4634-8299-f8844e67d0e4"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0493045337498188, "Height": 0.009180132299661636, "Left": 0.027214767411351204, "Top": 0.27090147137641907}, "Polygon": [{"X": 0.027214808389544487, "Y": 0.27090147137641907}, {"X": 0.07651930302381516, "Y": 0.2709072530269623}, {"X": 0.07651922106742859, "Y": 0.2800815999507904}, {"X": 0.027214767411351204, "Y": 0.2800757884979248}]}, "Id": "4c60559b-3a22-48e6-8ae6-2345488b4f4a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLAWLESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0831935703754425, "Height": 0.008985047228634357, "Left": 0.22927530109882355, "Top": 0.27109864354133606}, "Polygon": [{"X": 0.22927552461624146, "Y": 0.27109864354133606}, {"X": 0.31246888637542725, "Y": 0.2711084187030792}, {"X": 0.31246858835220337, "Y": 0.28008368611335754}, {"X": 0.22927530109882355, "Y": 0.2800739109516144}]}, "Id": "12cecf32-add2-40a8-9bf5-886261dcb12c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Dock:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03843407705426216, "Height": 0.009150012396275997, "Left": 0.027146941050887108, "Top": 0.2889705300331116}, "Polygon": [{"X": 0.02714698016643524, "Y": 0.2889705300331116}, {"X": 0.06558101624250412, "Y": 0.2889750599861145}, {"X": 0.06558094173669815, "Y": 0.29812055826187134}, {"X": 0.027146941050887108, "Y": 0.298115998506546}]}, "Id": "c5c7b99a-9973-4a9d-abc7-18facc269e92"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03375209495425224, "Height": 0.009066986851394176, "Left": 0.2294788360595703, "Top": 0.2890565097332001}, "Polygon": [{"X": 0.22947904467582703, "Y": 0.2890565097332001}, {"X": 0.26323091983795166, "Y": 0.28906047344207764}, {"X": 0.26323068141937256, "Y": 0.29812347888946533}, {"X": 0.2294788360595703, "Y": 0.29811951518058777}]}, "Id": "783df1fa-e296-425c-9c2d-2bdb919849cc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036937858909368515, "Height": 0.008981837891042233, "Left": 0.027047909796237946, "Top": 0.30718520283699036}, "Polygon": [{"X": 0.02704794891178608, "Y": 0.30718520283699036}, {"X": 0.06398576498031616, "Y": 0.30718955397605896}, {"X": 0.06398569792509079, "Y": 0.31616702675819397}, {"X": 0.027047909796237946, "Y": 0.31616267561912537}]}, "Id": "aaf4bc98-dc95-498c-9fa8-c8c4136cbf7b"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008386456407606602, "Height": 0.008801685646176338, "Left": 0.22920601069927216, "Top": 0.307125449180603}, "Polygon": [{"X": 0.22920621931552887, "Y": 0.307125449180603}, {"X": 0.23759247362613678, "Y": 0.3071264326572418}, {"X": 0.23759225010871887, "Y": 0.315927118062973}, {"X": 0.22920601069927216, "Y": 0.31592613458633423}]}, "Id": "60f8cd51-9cb8-4095-b6dd-b4508ce1207e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0586019866168499, "Height": 0.009309870190918446, "Left": 0.026951909065246582, "Top": 0.3322656452655792}, "Polygon": [{"X": 0.026951950043439865, "Y": 0.3322656452655792}, {"X": 0.08555389940738678, "Y": 0.33227258920669556}, {"X": 0.08555380254983902, "Y": 0.3415755331516266}, {"X": 0.026951909065246582, "Y": 0.34156858921051025}]}, "Id": "1de9a1f9-03e9-4abe-b5b6-e912fa9b2c4e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Orders", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043741773813962936, "Height": 0.009223164059221745, "Left": 0.08973249793052673, "Top": 0.3322198987007141}, "Polygon": [{"X": 0.0897325947880745, "Y": 0.3322198987007141}, {"X": 0.13347427546977997, "Y": 0.33222508430480957}, {"X": 0.13347414135932922, "Y": 0.3414430618286133}, {"X": 0.08973249793052673, "Y": 0.3414379060268402}]}, "Id": "726ada21-2f7f-4df2-a0bc-30f554e3417a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048923101276159286, "Height": 0.00930540356785059, "Left": 0.22955836355686188, "Top": 0.3322174847126007}, "Polygon": [{"X": 0.22955858707427979, "Y": 0.3322174847126007}, {"X": 0.27848145365715027, "Y": 0.3322232663631439}, {"X": 0.2784811854362488, "Y": 0.3415229022502899}, {"X": 0.22955836355686188, "Y": 0.3415170907974243}]}, "Id": "09ba476f-3cd8-4fe1-8a90-b2b53321f716"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07912813872098923, "Height": 0.00898846983909607, "Left": 0.026827482506632805, "Top": 0.3503831923007965}, "Polygon": [{"X": 0.02682752162218094, "Y": 0.3503831923007965}, {"X": 0.10595562309026718, "Y": 0.35039255023002625}, {"X": 0.10595551878213882, "Y": 0.3593716621398926}, {"X": 0.026827482506632805, "Y": 0.35936227440834045}]}, "Id": "a396c3cf-1174-4bb2-bdc0-21f61a777d45"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ANCHOR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06814230978488922, "Height": 0.008834123611450195, "Left": 0.22949178516864777, "Top": 0.3503875434398651}, "Polygon": [{"X": 0.22949199378490448, "Y": 0.3503875434398651}, {"X": 0.297634094953537, "Y": 0.3503956198692322}, {"X": 0.2976338267326355, "Y": 0.3592216670513153}, {"X": 0.22949178516864777, "Y": 0.35921359062194824}]}, "Id": "5aa813bf-2171-4d38-bd45-ec058264ff4b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PACKAGING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09499728679656982, "Height": 0.009061651304364204, "Left": 0.3016289174556732, "Top": 0.35035377740859985}, "Polygon": [{"X": 0.3016292154788971, "Y": 0.35035377740859985}, {"X": 0.39662620425224304, "Y": 0.3503650426864624}, {"X": 0.3966258466243744, "Y": 0.3594154417514801}, {"X": 0.3016289174556732, "Y": 0.35940417647361755}]}, "Id": "f8850c37-7824-491a-bd66-5c1366c4b769"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033765800297260284, "Height": 0.00928564090281725, "Left": 0.027044719085097313, "Top": 0.3934774100780487}, "Polygon": [{"X": 0.027044758200645447, "Y": 0.3934774100780487}, {"X": 0.06081051751971245, "Y": 0.39348143339157104}, {"X": 0.06081044673919678, "Y": 0.4027630686759949}, {"X": 0.027044719085097313, "Y": 0.40275904536247253}]}, "Id": "121843d3-3324-4c50-8991-78fa3e005edd"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Initial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038191623985767365, "Height": 0.009169678203761578, "Left": 0.06543778628110886, "Top": 0.3934612274169922}, "Polygon": [{"X": 0.06543786078691483, "Y": 0.3934612274169922}, {"X": 0.10362941026687622, "Y": 0.3934657871723175}, {"X": 0.10362930595874786, "Y": 0.40263092517852783}, {"X": 0.06543778628110886, "Y": 0.4026263654232025}]}, "Id": "436dc3f3-40dd-42c7-89da-581dc4ec2143"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04687586799263954, "Height": 0.00914778932929039, "Left": 0.10766346007585526, "Top": 0.39348211884498596}, "Polygon": [{"X": 0.10766357183456421, "Y": 0.39348211884498596}, {"X": 0.1545393317937851, "Y": 0.39348769187927246}, {"X": 0.15453918278217316, "Y": 0.40262991189956665}, {"X": 0.10766346007585526, "Y": 0.40262433886528015}]}, "Id": "9591e78a-9259-4c7c-a823-76ba10000fc1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "29.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03657899424433708, "Height": 0.009235360659658909, "Left": 0.22921116650104523, "Top": 0.39362114667892456}, "Polygon": [{"X": 0.22921139001846313, "Y": 0.39362114667892456}, {"X": 0.2657901644706726, "Y": 0.39362549781799316}, {"X": 0.2657899260520935, "Y": 0.4028564989566803}, {"X": 0.22921116650104523, "Y": 0.4028521478176117}]}, "Id": "98c11ae1-21ef-48bb-a4f4-5fc61571ff8f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033795345574617386, "Height": 0.00902253482490778, "Left": 0.027003366500139236, "Top": 0.41168925166130066}, "Polygon": [{"X": 0.02700340561568737, "Y": 0.41168925166130066}, {"X": 0.06079871207475662, "Y": 0.411693274974823}, {"X": 0.06079864501953125, "Y": 0.42071178555488586}, {"X": 0.027003366500139236, "Y": 0.4207077622413635}]}, "Id": "ffe9a4c8-b553-4a86-8eed-70947f39aa09"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Finished", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05576580762863159, "Height": 0.009078428149223328, "Left": 0.0652034655213356, "Top": 0.4116343557834625}, "Polygon": [{"X": 0.06520354002714157, "Y": 0.4116343557834625}, {"X": 0.1209692731499672, "Y": 0.411641001701355}, {"X": 0.12096915394067764, "Y": 0.42071276903152466}, {"X": 0.0652034655213356, "Y": 0.4207061231136322}]}, "Id": "1b3491e9-6243-45b0-90d1-28b9850478a1"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04689418151974678, "Height": 0.0092453109100461, "Left": 0.12492404878139496, "Top": 0.411499559879303}, "Polygon": [{"X": 0.1249241754412651, "Y": 0.411499559879303}, {"X": 0.17181822657585144, "Y": 0.4115051329135895}, {"X": 0.1718180626630783, "Y": 0.4207448661327362}, {"X": 0.12492404878139496, "Y": 0.4207392632961273}]}, "Id": "9ab3db20-7cd3-4dce-ae1d-f7ba624f5884"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "64", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01594833843410015, "Height": 0.008988077752292156, "Left": 0.22958111763000488, "Top": 0.41168174147605896}, "Polygon": [{"X": 0.2295813262462616, "Y": 0.41168174147605896}, {"X": 0.24552945792675018, "Y": 0.4116836488246918}, {"X": 0.24552923440933228, "Y": 0.420669823884964}, {"X": 0.22958111763000488, "Y": 0.4206679165363312}]}, "Id": "3f58e6f5-fe18-437c-93bf-ec0b730562dc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03376460447907448, "Height": 0.009288507513701916, "Left": 0.02705564722418785, "Top": 0.42948758602142334}, "Polygon": [{"X": 0.027055686339735985, "Y": 0.42948758602142334}, {"X": 0.06082024797797203, "Y": 0.4294916093349457}, {"X": 0.06082018092274666, "Y": 0.4387761056423187}, {"X": 0.02705564722418785, "Y": 0.438772052526474}]}, "Id": "67534483-9ab2-4609-a220-c89a3417e857"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031324438750743866, "Height": 0.008986599743366241, "Left": 0.06544133275747299, "Top": 0.42962008714675903}, "Polygon": [{"X": 0.06544140726327896, "Y": 0.42962008714675903}, {"X": 0.09676577150821686, "Y": 0.4296238124370575}, {"X": 0.0967656746506691, "Y": 0.4386066794395447}, {"X": 0.06544133275747299, "Y": 0.4386029541492462}]}, "Id": "ee362a5e-42f4-4e47-bfec-abfc510ead38"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Count:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04285430163145065, "Height": 0.009008267894387245, "Left": 0.10082491487264633, "Top": 0.4296351373195648}, "Polygon": [{"X": 0.10082501173019409, "Y": 0.4296351373195648}, {"X": 0.14367921650409698, "Y": 0.4296402335166931}, {"X": 0.14367908239364624, "Y": 0.4386433959007263}, {"X": 0.10082491487264633, "Y": 0.438638299703598}]}, "Id": "ae210092-a6f2-4d24-9149-272cdb03fd70"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1056", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03103836067020893, "Height": 0.009258665144443512, "Left": 0.2308683842420578, "Top": 0.42957571148872375}, "Polygon": [{"X": 0.2308686077594757, "Y": 0.42957571148872375}, {"X": 0.2619067430496216, "Y": 0.42957940697669983}, {"X": 0.2619065046310425, "Y": 0.43883436918258667}, {"X": 0.2308683842420578, "Y": 0.4388306736946106}]}, "Id": "31b0cc3d-be0c-46ab-93b4-38571cd6dac0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04479393735527992, "Height": 0.009106958284974098, "Left": 0.02687457762658596, "Top": 0.4475553631782532}, "Polygon": [{"X": 0.026874616742134094, "Y": 0.4475553631782532}, {"X": 0.07166851311922073, "Y": 0.44756072759628296}, {"X": 0.07166843861341476, "Y": 0.4566623270511627}, {"X": 0.02687457762658596, "Y": 0.45665696263313293}]}, "Id": "5718b774-97c2-411e-a5c8-3a723ea93350"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05713532492518425, "Height": 0.009281154721975327, "Left": 0.07484753429889679, "Top": 0.4476214647293091}, "Polygon": [{"X": 0.07484761625528336, "Y": 0.4476214647293091}, {"X": 0.13198284804821014, "Y": 0.44762828946113586}, {"X": 0.1319827288389206, "Y": 0.4569026231765747}, {"X": 0.07484753429889679, "Y": 0.4568957984447479}]}, "Id": "62269dc4-fcd0-4d24-9f13-40c4c4d64ded"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "01", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014559531584382057, "Height": 0.00883022416383028, "Left": 0.22946269810199738, "Top": 0.4476981461048126}, "Polygon": [{"X": 0.2294629067182541, "Y": 0.4476981461048126}, {"X": 0.2440222203731537, "Y": 0.4476998746395111}, {"X": 0.24402201175689697, "Y": 0.45652836561203003}, {"X": 0.22946269810199738, "Y": 0.45652663707733154}]}, "Id": "148f48dc-7993-4cf6-80f2-d08f6935afc4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04817970097064972, "Height": 0.009008852764964104, "Left": 0.026937171816825867, "Top": 0.46578606963157654}, "Polygon": [{"X": 0.02693720906972885, "Y": 0.46578606963157654}, {"X": 0.07511687278747559, "Y": 0.46579182147979736}, {"X": 0.07511679083108902, "Y": 0.4747949242591858}, {"X": 0.026937171816825867, "Y": 0.4747891426086426}]}, "Id": "4604bbee-4ba3-4fe6-8a12-0bc7e4ceb80f"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056723400950431824, "Height": 0.009363222867250443, "Left": 0.07854045927524567, "Top": 0.46547043323516846}, "Polygon": [{"X": 0.07854054868221283, "Y": 0.46547043323516846}, {"X": 0.1352638602256775, "Y": 0.46547722816467285}, {"X": 0.13526372611522675, "Y": 0.4748336672782898}, {"X": 0.07854045927524567, "Y": 0.4748268723487854}]}, "Id": "32045141-c7b8-4884-8081-0392380b2df6"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "BOL:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035833217203617096, "Height": 0.008918074890971184, "Left": 0.027086904272437096, "Top": 0.4837033450603485}, "Polygon": [{"X": 0.02708694152534008, "Y": 0.4837033450603485}, {"X": 0.06292012333869934, "Y": 0.48370763659477234}, {"X": 0.06292004883289337, "Y": 0.49262142181396484}, {"X": 0.027086904272437096, "Y": 0.492617130279541}]}, "Id": "f3728473-9954-49a0-a26f-0559d90ffd96"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07346145063638687, "Height": 0.009278757497668266, "Left": 0.027359645813703537, "Top": 0.5016858577728271}, "Polygon": [{"X": 0.02735968679189682, "Y": 0.5016858577728271}, {"X": 0.1008211001753807, "Y": 0.5016946792602539}, {"X": 0.10082098841667175, "Y": 0.5109646320343018}, {"X": 0.027359645813703537, "Y": 0.510955810546875}]}, "Id": "14318187-fe81-4d2c-a995-5c50927dbdae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Canned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04945964738726616, "Height": 0.009277891367673874, "Left": 0.027249695733189583, "Top": 0.5195531249046326}, "Polygon": [{"X": 0.027249736711382866, "Y": 0.5195531249046326}, {"X": 0.07670934498310089, "Y": 0.5195590853691101}, {"X": 0.07670925557613373, "Y": 0.5288310647010803}, {"X": 0.027249695733189583, "Y": 0.5288251042366028}]}, "Id": "d0065504-b002-411a-98f8-1f2209f578e7"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07345748692750931, "Height": 0.009187640622258186, "Left": 0.08074973523616791, "Top": 0.5196579098701477}, "Polygon": [{"X": 0.08074981719255447, "Y": 0.5196579098701477}, {"X": 0.15420721471309662, "Y": 0.5196667313575745}, {"X": 0.15420706570148468, "Y": 0.5288455486297607}, {"X": 0.08074973523616791, "Y": 0.528836727142334}]}, "Id": "54839d48-388c-4cd5-885d-51e8b08a62e8"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Unloaders:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07015246152877808, "Height": 0.00937582552433014, "Left": 0.02715350314974785, "Top": 0.5376774668693542}, "Polygon": [{"X": 0.02715354412794113, "Y": 0.5376774668693542}, {"X": 0.09730596095323563, "Y": 0.5376859307289124}, {"X": 0.09730586409568787, "Y": 0.5470532774925232}, {"X": 0.02715350314974785, "Y": 0.5470448136329651}]}, "Id": "c3f78453-f2e3-4f5c-b3c0-2f0ed9752c5c"}, {"BlockType": "WORD", "Confidence": 98.28662872314453, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004733589477837086, "Height": 0.009089834988117218, "Left": 0.2309475988149643, "Top": 0.5378982424736023}, "Polygon": [{"X": 0.2309478223323822, "Y": 0.5378982424736023}, {"X": 0.2356811910867691, "Y": 0.5378987789154053}, {"X": 0.2356809675693512, "Y": 0.5469880700111389}, {"X": 0.2309475988149643, "Y": 0.5469874739646912}]}, "Id": "d7dd9543-60d0-420f-9587-d81289fe74e6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Add", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027736345306038857, "Height": 0.009322013705968857, "Left": 0.027160637080669403, "Top": 0.5628369450569153}, "Polygon": [{"X": 0.027160676196217537, "Y": 0.5628369450569153}, {"X": 0.05489698052406311, "Y": 0.5628402829170227}, {"X": 0.054896917194128036, "Y": 0.5721589922904968}, {"X": 0.027160637080669403, "Y": 0.5721555948257446}]}, "Id": "4a4e35a1-fb27-4b28-9579-134ef388ef08"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023234248161315918, "Height": 0.008996724151074886, "Left": 0.059185124933719635, "Top": 0.5629449486732483}, "Polygon": [{"X": 0.05918519198894501, "Y": 0.5629449486732483}, {"X": 0.08241937309503555, "Y": 0.5629477500915527}, {"X": 0.08241928368806839, "Y": 0.5719416737556458}, {"X": 0.059185124933719635, "Y": 0.5719388723373413}]}, "Id": "a173fe16-dc49-47ac-9b9b-cf8302748bff"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023950718343257904, "Height": 0.008953533135354519, "Left": 0.02726420946419239, "Top": 0.5812379121780396}, "Polygon": [{"X": 0.027264248579740524, "Y": 0.5812379121780396}, {"X": 0.051214925944805145, "Y": 0.5812407732009888}, {"X": 0.05121487006545067, "Y": 0.5901914238929749}, {"X": 0.02726420946419239, "Y": 0.5901885032653809}]}, "Id": "c0bc63fa-94b6-404a-8db7-7440af6a9330"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07929976284503937, "Height": 0.008941875770688057, "Left": 0.055741842836141586, "Top": 0.5811513662338257}, "Polygon": [{"X": 0.05574190244078636, "Y": 0.5811513662338257}, {"X": 0.13504160940647125, "Y": 0.5811609625816345}, {"X": 0.1350414752960205, "Y": 0.5900932550430298}, {"X": 0.055741842836141586, "Y": 0.590083658695221}]}, "Id": "7eeb839a-f693-4203-891d-52b830ea9030"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pinwheeled", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0756034106016159, "Height": 0.009249738417565823, "Left": 0.057833366096019745, "Top": 0.5988349318504333}, "Polygon": [{"X": 0.05783343315124512, "Y": 0.5988349318504333}, {"X": 0.13343678414821625, "Y": 0.5988441109657288}, {"X": 0.1334366500377655, "Y": 0.6080846786499023}, {"X": 0.057833366096019745, "Y": 0.6080754995346069}]}, "Id": "dbd730a8-3754-46d5-a948-dc6cdf07820a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$40.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04438871145248413, "Height": 0.010224013589322567, "Left": 0.2291085124015808, "Top": 0.5985558032989502}, "Polygon": [{"X": 0.2291087508201599, "Y": 0.5985558032989502}, {"X": 0.27349722385406494, "Y": 0.59856116771698}, {"X": 0.27349692583084106, "Y": 0.6087798476219177}, {"X": 0.2291085124015808, "Y": 0.6087744832038879}]}, "Id": "7c046a54-aeef-42a7-9118-4aba5b168782"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03381022810935974, "Height": 0.009250695817172527, "Left": 0.02698998898267746, "Top": 0.616976797580719}, "Polygon": [{"X": 0.026990028098225594, "Y": 0.616976797580719}, {"X": 0.0608002170920372, "Y": 0.6169808506965637}, {"X": 0.06080014631152153, "Y": 0.6262274980545044}, {"X": 0.02698998898267746, "Y": 0.6262233853340149}]}, "Id": "ca7dd5d0-f408-4306-87a8-96e95e6a8488"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Add", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027818145230412483, "Height": 0.009214319288730621, "Left": 0.06537015736103058, "Top": 0.6168705821037292}, "Polygon": [{"X": 0.06537023186683655, "Y": 0.6168705821037292}, {"X": 0.09318830072879791, "Y": 0.6168739795684814}, {"X": 0.09318820387125015, "Y": 0.6260849237442017}, {"X": 0.06537015736103058, "Y": 0.6260815262794495}]}, "Id": "b376d9cb-03a0-4410-ac42-7157f6cdac3a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charges:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0563647486269474, "Height": 0.011646696366369724, "Left": 0.09734181314706802, "Top": 0.6170220971107483}, "Polygon": [{"X": 0.09734193980693817, "Y": 0.6170220971107483}, {"X": 0.15370656549930573, "Y": 0.6170288920402527}, {"X": 0.1537063717842102, "Y": 0.6286687850952148}, {"X": 0.09734181314706802, "Y": 0.6286619305610657}]}, "Id": "c01b0468-b285-4239-921c-66f841f37f22"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$40.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04440949484705925, "Height": 0.010277644731104374, "Left": 0.2293684035539627, "Top": 0.6166789531707764}, "Polygon": [{"X": 0.2293686419725418, "Y": 0.6166789531707764}, {"X": 0.27377790212631226, "Y": 0.6166843175888062}, {"X": 0.2737776041030884, "Y": 0.626956582069397}, {"X": 0.2293684035539627, "Y": 0.6269512176513672}]}, "Id": "c01d2cb6-27dd-43a5-9675-8b59feaa299b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Base", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031187506392598152, "Height": 0.008887563832104206, "Left": 0.02724382095038891, "Top": 0.635305643081665}, "Polygon": [{"X": 0.027243860065937042, "Y": 0.635305643081665}, {"X": 0.05843132734298706, "Y": 0.6353093981742859}, {"X": 0.05843126401305199, "Y": 0.644193172454834}, {"X": 0.02724382095038891, "Y": 0.6441894173622131}]}, "Id": "f1499a0c-0315-46f0-92e2-41aa580021c1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05011206492781639, "Height": 0.01193154789507389, "Left": 0.06268960237503052, "Top": 0.634849488735199}, "Polygon": [{"X": 0.06268969178199768, "Y": 0.634849488735199}, {"X": 0.11280166357755661, "Y": 0.6348555684089661}, {"X": 0.11280151456594467, "Y": 0.6467810273170471}, {"X": 0.06268960237503052, "Y": 0.64677494764328}]}, "Id": "63bbd3a6-68c6-43c0-be12-fc577410cbd8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "310.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04448733106255531, "Height": 0.00908602774143219, "Left": 0.22930239140987396, "Top": 0.6350730061531067}, "Polygon": [{"X": 0.22930260002613068, "Y": 0.6350730061531067}, {"X": 0.27378973364830017, "Y": 0.6350784301757812}, {"X": 0.2737894654273987, "Y": 0.6441590189933777}, {"X": 0.22930239140987396, "Y": 0.6441536545753479}]}, "Id": "bcaf8310-f546-4dd0-a1f0-b6faf9c83045"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08426756411790848, "Height": 0.009373447857797146, "Left": 0.02732766419649124, "Top": 0.6527150273323059}, "Polygon": [{"X": 0.027327703312039375, "Y": 0.6527150273323059}, {"X": 0.11159522831439972, "Y": 0.6527252793312073}, {"X": 0.11159511655569077, "Y": 0.6620885133743286}, {"X": 0.02732766419649124, "Y": 0.6620782017707825}]}, "Id": "4af9f21f-7810-4cea-9bd7-b816e7a42dc2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02709973230957985, "Height": 0.008804352022707462, "Left": 0.11552567780017853, "Top": 0.6531578302383423}, "Polygon": [{"X": 0.11552578955888748, "Y": 0.6531578302383423}, {"X": 0.14262540638446808, "Y": 0.6531611084938049}, {"X": 0.14262527227401733, "Y": 0.6619621515274048}, {"X": 0.11552567780017853, "Y": 0.6619588732719421}]}, "Id": "21521cf5-8f44-4b22-a15d-80b86a720cc4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035167016088962555, "Height": 0.008965395390987396, "Left": 0.23067030310630798, "Top": 0.6532025933265686}, "Polygon": [{"X": 0.2306705117225647, "Y": 0.6532025933265686}, {"X": 0.26583731174468994, "Y": 0.6532068848609924}, {"X": 0.26583707332611084, "Y": 0.662168025970459}, {"X": 0.23067030310630798, "Y": 0.6621637344360352}]}, "Id": "b075fcdc-e260-4566-95ba-9f7b8246dc80"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03356083855032921, "Height": 0.009091382846236229, "Left": 0.027079496532678604, "Top": 0.671040415763855}, "Polygon": [{"X": 0.027079535648226738, "Y": 0.671040415763855}, {"X": 0.06064033508300781, "Y": 0.6710444688796997}, {"X": 0.06064026802778244, "Y": 0.6801317930221558}, {"X": 0.027079496532678604, "Y": 0.6801276803016663}]}, "Id": "35253bf7-3486-4d43-815f-b65bb989fb0f"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Cost:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033090077340602875, "Height": 0.009108206257224083, "Left": 0.06535159051418304, "Top": 0.671051025390625}, "Polygon": [{"X": 0.06535165756940842, "Y": 0.671051025390625}, {"X": 0.09844166785478592, "Y": 0.6710550785064697}, {"X": 0.09844156354665756, "Y": 0.6801592707633972}, {"X": 0.06535159051418304, "Y": 0.6801552176475525}]}, "Id": "df939e0b-e0c0-45c7-bc19-5437bacf439b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "360.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04432360455393791, "Height": 0.009055864997208118, "Left": 0.22964906692504883, "Top": 0.670859158039093}, "Polygon": [{"X": 0.22964927554130554, "Y": 0.670859158039093}, {"X": 0.27397266030311584, "Y": 0.6708645820617676}, {"X": 0.27397242188453674, "Y": 0.6799150109291077}, {"X": 0.22964906692504883, "Y": 0.6799096465110779}]}, "Id": "20446be4-5e88-41ce-8875-53f92a72fd39"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06650935113430023, "Height": 0.011521782726049423, "Left": 0.027063259854912758, "Top": 0.6962609887123108}, "Polygon": [{"X": 0.027063310146331787, "Y": 0.6962609887123108}, {"X": 0.09357260912656784, "Y": 0.6962690949440002}, {"X": 0.09357248991727829, "Y": 0.7077827453613281}, {"X": 0.027063259854912758, "Y": 0.7077746391296387}]}, "Id": "48ce64e4-5300-40fe-8546-98fe69b8fcb5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0531301386654377, "Height": 0.009000932797789574, "Left": 0.22964595258235931, "Top": 0.696476936340332}, "Polygon": [{"X": 0.22964616119861603, "Y": 0.696476936340332}, {"X": 0.2827760875225067, "Y": 0.6964834332466125}, {"X": 0.2827758491039276, "Y": 0.7054778933525085}, {"X": 0.22964595258235931, "Y": 0.705471396446228}]}, "Id": "9d12a247-cfbf-4061-b453-5fe2cb2d12ac"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "CapstonePay-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08933208137750626, "Height": 0.012161935679614544, "Left": 0.05822177976369858, "Top": 0.7139973044395447}, "Polygon": [{"X": 0.05822186544537544, "Y": 0.7139973044395447}, {"X": 0.14755386114120483, "Y": 0.7140082120895386}, {"X": 0.14755366742610931, "Y": 0.7261592149734497}, {"X": 0.05822177976369858, "Y": 0.7261483073234558}]}, "Id": "26d69df9-1ea2-41f2-b44d-8eedd67cabe5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$360.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05220530927181244, "Height": 0.009791131131350994, "Left": 0.22961415350437164, "Top": 0.7141082286834717}, "Polygon": [{"X": 0.22961437702178955, "Y": 0.7141082286834717}, {"X": 0.2818194627761841, "Y": 0.7141146063804626}, {"X": 0.2818191647529602, "Y": 0.7238993644714355}, {"X": 0.22961415350437164, "Y": 0.7238929867744446}]}, "Id": "02ac310a-c2bc-48e2-9123-bac1f8330ea9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "38053037", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06398124247789383, "Height": 0.008965009823441505, "Left": 0.05843393877148628, "Top": 0.7282271981239319}, "Polygon": [{"X": 0.058434005826711655, "Y": 0.7282271981239319}, {"X": 0.12241518497467041, "Y": 0.7282350063323975}, {"X": 0.12241505831480026, "Y": 0.7371922135353088}, {"X": 0.05843393877148628, "Y": 0.7371844053268433}]}, "Id": "ab312a7d-fadf-491b-8bcc-63c62af14ee5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034132447093725204, "Height": 0.008668160997331142, "Left": 0.05770014598965645, "Top": 0.7503200173377991}, "Polygon": [{"X": 0.05770020931959152, "Y": 0.7503200173377991}, {"X": 0.09183259308338165, "Y": 0.7503241896629333}, {"X": 0.09183250367641449, "Y": 0.7589881420135498}, {"X": 0.05770014598965645, "Y": 0.7589839696884155}]}, "Id": "598e8b8c-188c-4039-9309-a75dd219de78"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Payments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06272928416728973, "Height": 0.011717860586941242, "Left": 0.0959789827466011, "Top": 0.7504034638404846}, "Polygon": [{"X": 0.09597910940647125, "Y": 0.7504034638404846}, {"X": 0.15870825946331024, "Y": 0.7504111528396606}, {"X": 0.15870806574821472, "Y": 0.762121319770813}, {"X": 0.0959789827466011, "Y": 0.762113630771637}]}, "Id": "c1cec2c1-c224-4319-9302-a18af67e86a9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$360.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052335336804389954, "Height": 0.00954828504472971, "Left": 0.22791503369808197, "Top": 0.7502108216285706}, "Polygon": [{"X": 0.22791525721549988, "Y": 0.7502108216285706}, {"X": 0.2802503705024719, "Y": 0.7502172589302063}, {"X": 0.28025010228157043, "Y": 0.7597591280937195}, {"X": 0.22791503369808197, "Y": 0.7597526907920837}]}, "Id": "f80e6848-f368-4dee-9129-21c1dfeeeb99"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d3dd2594-ba3d-4554-9ef0-4b3837221bb3", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d3dd2594-ba3d-4554-9ef0-4b3837221bb3", "content-type": "application/x-amz-json-1.1", "content-length": "82339", "date": "Wed, 24 Sep 2025 13:26:50 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "b1528c87-54bf-46bd-b670-c4e303c07a75", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:55 GMT", "content-type": "application/json", "content-length": "2093", "connection": "keep-alive", "x-amzn-requestid": "b1528c87-54bf-46bd-b670-c4e303c07a75"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1 content. Need to determine document type. Let's analyze. We have Capstone Logistics header. There's Receipt #, Work Date, Bill Code, Carrier: FLAWLESS, Dock: DRY, Door:4. Purchase Orders, Vendor: ********** ANCHOR PACKAGING. Total initial pallets, total finished pallets, case count. Trailer number, Tractor number, BOL: Comments, etc. There's Add Fee, PO: **********, Total Add Charges, Base Charge, Convenience Fee, Total Cost. Payments: Amount, CapstonePay, $360.00 etc.\n\nThis looks like a Carrier invoice for loading/unloading? It includes charges, total cost, payments. It also has a PO number. Could it be lumper receipt? The description: lumper receipt: Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO, Door number, Date-Time, initial pallets, finished pallets, carrier, etc. So lumper receipt fits.\n\nBut also could be invoice. Invoice definitions: Bill issued by a carrier for goods being transported. It includes charges, etc. The difference between invoice and lumper receipt: lumper receipt has the specific fields: PO, Door number, Date-Time, initial pallets, finished pallets, carrier, service charge, total amount. This page indeed contains those. So lumper receipt is likely correct.\n\nAlso \"Receipt #\" indicates a receipt. The page shows payments. It might be a lumper receipt indeed. So doc_type: lumper_receipt.\n\nWe must output JSON with documents array: page_no: 1, doc_type: lumper_receipt.\n\nCheck if it's continuation page? No, it's first page. No \"continued\". So classification stands.\n\nReturn using tool classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"lumper_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2851, "outputTokens": 417, "totalTokens": 3268}, "metrics": {"latencyMs": 1964}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/fc004b50_ZOBA55ELUA2GRE3UJM3I.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nCapstone Logistics\n30 TECHNOLOGY PKWY SOUTH SUITE 200\nPEACHTREE CORNERS, GA 30092\n770-414-1929\nFED ID# 45-3087555\n08:32:49 September 23, 2025\nReceipt #:\n3b670798-9dde-\n404c-a323-f8bbdf2dbdde\nLocation:\n<PERSON><PERSON><PERSON><PERSON> DON DALLAS\nTX\nWork Date:\n2025-09-23\nBill Code:\nRCOD30888\nCarrier:\nFLAWLESS\nDock:\nDRY\nDoor:\n4\nPurchase Orders\nVendor\n**********\nANCHOR PACKAGING\nTotal Initial Pallets:\n29.00\nTotal Finished Pallets:\n64\nTotal Case Count:\n1056\nTrailer Number:\n01\nTractor Number:\nBOL:\nComments:\nCanned Comments:\nUnloaders:\n1\nAdd Fee\nPO: **********\nPinwheeled\n$40.00\nTotal Add Charges:\n$40.00\nBase Charge:\n310.00\nConvenience Fee:\n10.00\nTotal Cost:\n360.00\nPayments:\nAmount\nCapstonePay-\n$360.00\n38053037\nTotal Payments\n$360.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}