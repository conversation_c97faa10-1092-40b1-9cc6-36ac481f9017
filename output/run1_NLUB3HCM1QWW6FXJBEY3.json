{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.7198778390884399, "Height": 0.9488614797592163, "Left": 0.16260075569152832, "Top": 0.02779337204992771}, "Polygon": [{"X": 0.16889064013957977, "Y": 0.02779337204992771}, {"X": 0.8824785947799683, "Y": 0.028324177488684654}, {"X": 0.8794227838516235, "Y": 0.9766548871994019}, {"X": 0.16260075569152832, "Y": 0.9744250774383545}]}, "Id": "c8a13b32-8db1-417b-b87a-3b612399d5c3", "Relationships": [{"Type": "CHILD", "Ids": ["493e8ab8-aed5-47fd-949f-63bece2299d6", "68413419-0815-4974-bf6b-6a717eeb380a", "94f72609-337b-464b-b1a9-ca63629eeb5c", "69d33009-f853-4554-b138-07bb79fb1d35", "95ed5216-bfe2-42ee-b17b-3dc27e0213f2", "751512f8-4459-4a2f-a0ea-dea465479472", "e730a284-467b-4de9-b0ae-f645e642ff5d", "1155b85b-a474-4ee0-ab72-df6d83eecac3", "8d9575f2-1e27-4568-96de-e87266d38a1d", "20943837-9817-46af-a690-7d9db30e001f", "8f1904ef-d814-4187-b7ba-b82d4dfd63b0", "4cc79a2e-b3b4-4a0d-b02e-8d45e3a0a8d7", "7e792ee1-eae1-424a-8080-e564d3224bf2", "190a6590-c7e7-4740-977b-bcbdb3ef3bb0", "438f3271-64e8-4676-9658-0fa196b3a9a2", "7ec43301-8c62-4979-8972-8927bc1e4df6", "0034085e-02fb-4f39-808b-cca4fd98c9ae", "e97599f2-af4c-4b21-8da3-636d4d543395", "3900522f-b01e-4865-ab44-9064941aafa1", "8021e415-a4fe-4d96-b3e2-62d2b67a74ae", "094a4ce5-94fc-42e1-9419-151e88bd681e", "3a09be19-4ddf-4efa-898a-d1cd2a031cf3", "8b2de28b-ecae-4d9d-8636-f8426a2f2e8d", "4b637a24-ea5c-4c4a-b741-a61df0fbc8a2", "c2f19b7e-915d-460f-a232-0846040af598", "485013ff-54ec-411b-9e30-8e217ac9491e"]}]}, {"BlockType": "LINE", "Confidence": 99.40369415283203, "Text": "HAMTRAMCK", "Geometry": {"BoundingBox": {"Width": 0.4573183059692383, "Height": 0.03111240454018116, "Left": 0.2522429823875427, "Top": 0.11573531478643417}, "Polygon": [{"X": 0.2524341940879822, "Y": 0.11573531478643417}, {"X": 0.709561288356781, "Y": 0.11617577075958252}, {"X": 0.7094370722770691, "Y": 0.14684772491455078}, {"X": 0.2522429823875427, "Y": 0.14637218415737152}]}, "Id": "493e8ab8-aed5-47fd-949f-63bece2299d6", "Relationships": [{"Type": "CHILD", "Ids": ["ffb233b7-16f8-47f8-8370-c6c76f997f54"]}]}, {"BlockType": "LINE", "Confidence": 99.37440490722656, "Text": "RECYCLING", "Geometry": {"BoundingBox": {"Width": 0.45429131388664246, "Height": 0.028220441192388535, "Left": 0.25673964619636536, "Top": 0.14993558824062347}, "Polygon": [{"X": 0.2569119930267334, "Y": 0.14993558824062347}, {"X": 0.7110309600830078, "Y": 0.1504119634628296}, {"X": 0.7109188437461853, "Y": 0.1781560182571411}, {"X": 0.25673964619636536, "Y": 0.17764812707901}]}, "Id": "68413419-0815-4974-bf6b-6a717eeb380a", "Relationships": [{"Type": "CHILD", "Ids": ["e6d4de0d-b415-45fb-8861-5e3d83b189fe"]}]}, {"BlockType": "LINE", "Confidence": 92.29890441894531, "Text": "&TRANSFER STATION", "Geometry": {"BoundingBox": {"Width": 0.24222391843795776, "Height": 0.016673101112246513, "Left": 0.46655911207199097, "Top": 0.18221867084503174}, "Polygon": [{"X": 0.4666445553302765, "Y": 0.18221867084503174}, {"X": 0.7087830305099487, "Y": 0.18249206244945526}, {"X": 0.7087165713310242, "Y": 0.1988917738199234}, {"X": 0.46655911207199097, "Y": 0.1986084282398224}]}, "Id": "94f72609-337b-464b-b1a9-ca63629eeb5c", "Relationships": [{"Type": "CHILD", "Ids": ["7ea6f3ff-f779-441b-87b7-ed1dd0b37332", "4d580aaa-f31a-4a44-9d8c-9648ad7fa697"]}]}, {"BlockType": "LINE", "Confidence": 99.81712341308594, "Text": "3333 Hamtramck Dr, Hamtramck, MI", "Geometry": {"BoundingBox": {"Width": 0.3880528509616852, "Height": 0.022848762571811676, "Left": 0.278805673122406, "Top": 0.23465940356254578}, "Polygon": [{"X": 0.27894216775894165, "Y": 0.23465940356254578}, {"X": 0.6668584942817688, "Y": 0.23514845967292786}, {"X": 0.666763424873352, "Y": 0.25750815868377686}, {"X": 0.278805673122406, "Y": 0.25699740648269653}]}, "Id": "69d33009-f853-4554-b138-07bb79fb1d35", "Relationships": [{"Type": "CHILD", "Ids": ["18ec540c-b280-4d3e-91ba-d2dfaf907406", "32a9c2ba-8971-4023-9268-74da58c9caaa", "1f9718ba-a1cb-4484-b9b8-a8b4f2a1eb4f", "f3e08c3e-de44-41ca-bb45-e4f210d1b23c", "6de07a4f-7653-4c83-a119-878832c5a7af"]}]}, {"BlockType": "LINE", "Confidence": 99.47714233398438, "Text": "**************", "Geometry": {"BoundingBox": {"Width": 0.1681712567806244, "Height": 0.02234589122235775, "Left": 0.39100348949432373, "Top": 0.26260825991630554}, "Polygon": [{"X": 0.39112669229507446, "Y": 0.26260825991630554}, {"X": 0.5591747164726257, "Y": 0.26283180713653564}, {"X": 0.5590692758560181, "Y": 0.28495416045188904}, {"X": 0.39100348949432373, "Y": 0.284721314907074}]}, "Id": "95ed5216-bfe2-42ee-b17b-3dc27e0213f2", "Relationships": [{"Type": "CHILD", "Ids": ["a030273b-8126-488a-920c-13dfc75a8910", "1dc48dc6-ef90-4f48-8788-ac4aacc648f2"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Ticket: 423087-1", "Geometry": {"BoundingBox": {"Width": 0.20684455335140228, "Height": 0.0197004321962595, "Left": 0.2541237473487854, "Top": 0.3450295329093933}, "Polygon": [{"X": 0.2542443573474884, "Y": 0.3450295329093933}, {"X": 0.4609682857990265, "Y": 0.34534719586372375}, {"X": 0.4608668088912964, "Y": 0.36472994089126587}, {"X": 0.2541237473487854, "Y": 0.36440223455429077}]}, "Id": "751512f8-4459-4a2f-a0ea-dea465479472", "Relationships": [{"Type": "CHILD", "Ids": ["f043af6f-0f11-47d2-90ef-164a45e4ff9b", "1d0618cc-b5aa-4cd5-9e3e-5ca0efa1135f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date: 8/18/2025", "Geometry": {"BoundingBox": {"Width": 0.1950230747461319, "Height": 0.0192983690649271, "Left": 0.27898073196411133, "Top": 0.3724212646484375}, "Polygon": [{"X": 0.2790965735912323, "Y": 0.3724212646484375}, {"X": 0.47400379180908203, "Y": 0.37273409962654114}, {"X": 0.4739055931568146, "Y": 0.39171963930130005}, {"X": 0.27898073196411133, "Y": 0.39139753580093384}]}, "Id": "e730a284-467b-4de9-b0ae-f645e642ff5d", "Relationships": [{"Type": "CHILD", "Ids": ["65a8cc0a-abc9-419b-b22f-abdf08e382fe", "583c0521-dc18-42f3-a265-1625cec0eda7"]}]}, {"BlockType": "LINE", "Confidence": 96.92343139648438, "Text": "Customer: WM - Target", "Geometry": {"BoundingBox": {"Width": 0.2670879662036896, "Height": 0.021283408626914024, "Left": 0.2302970588207245, "Top": 0.39927417039871216}, "Polygon": [{"X": 0.2304290384054184, "Y": 0.39927417039871216}, {"X": 0.49738502502441406, "Y": 0.39972060918807983}, {"X": 0.4972795844078064, "Y": 0.42055755853652954}, {"X": 0.2302970588207245, "Y": 0.4200971722602844}]}, "Id": "1155b85b-a474-4ee0-ab72-df6d83eecac3", "Relationships": [{"Type": "CHILD", "Ids": ["54c90208-5189-4591-bbda-19e4926786b6", "0ffaf39d-5eb8-4750-a1b0-913dfbac4784", "c462dd93-acd8-4f26-b482-a32f0be31749", "129227ab-ffb3-499e-b9ef-d03673102d7f"]}]}, {"BlockType": "LINE", "Confidence": 99.52696228027344, "Text": "Hauler: TKT", "Geometry": {"BoundingBox": {"Width": 0.14702162146568298, "Height": 0.019599836319684982, "Left": 0.25409260392189026, "Top": 0.4268690049648285}, "Polygon": [{"X": 0.2542129456996918, "Y": 0.4268690049648285}, {"X": 0.40111422538757324, "Y": 0.4271247982978821}, {"X": 0.40100744366645813, "Y": 0.44646885991096497}, {"X": 0.25409260392189026, "Y": 0.4462059438228607}]}, "Id": "8d9575f2-1e27-4568-96de-e87266d38a1d", "Relationships": [{"Type": "CHILD", "Ids": ["1d58ac0e-ea84-459a-97b9-eda6faca5d67", "a6c5afa7-0fb5-4e2c-b82e-450e6ec0b17f"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Truck: 1125", "Geometry": {"BoundingBox": {"Width": 0.1469074934720993, "Height": 0.01952662132680416, "Left": 0.2665557563304901, "Top": 0.45415517687797546}, "Polygon": [{"X": 0.2666743993759155, "Y": 0.45415517687797546}, {"X": 0.4134632349014282, "Y": 0.4544207751750946}, {"X": 0.41335806250572205, "Y": 0.473681777715683}, {"X": 0.2665557563304901, "Y": 0.47340908646583557}]}, "Id": "20943837-9817-46af-a690-7d9db30e001f", "Relationships": [{"Type": "CHILD", "Ids": ["4b7ec077-ff35-4b0b-a952-545ac1c3bfdf", "0a625826-7fc3-4f64-ad17-54c695c9d0c6"]}]}, {"BlockType": "LINE", "Confidence": 99.86048889160156, "Text": "Ref Number:", "Geometry": {"BoundingBox": {"Width": 0.13130033016204834, "Height": 0.019645821303129196, "Left": 0.20534268021583557, "Top": 0.48120543360710144}, "Polygon": [{"X": 0.20546786487102509, "Y": 0.48120543360710144}, {"X": 0.3366430103778839, "Y": 0.48145169019699097}, {"X": 0.3365299701690674, "Y": 0.5008512735366821}, {"X": 0.20534268021583557, "Y": 0.5005986094474792}]}, "Id": "8f1904ef-d814-4187-b7ba-b82d4dfd63b0", "Relationships": [{"Type": "CHILD", "Ids": ["ccd2d6f3-64f6-473e-a2be-3f231dfa2100", "091b54ae-026a-4202-9bec-3b8f2ae209d2"]}]}, {"BlockType": "LINE", "Confidence": 99.95077514648438, "Text": "In: 7:15am", "Geometry": {"BoundingBox": {"Width": 0.14508213102817535, "Height": 0.019866323098540306, "Left": 0.3050547242164612, "Top": 0.508373498916626}, "Polygon": [{"X": 0.3051717281341553, "Y": 0.508373498916626}, {"X": 0.45013684034347534, "Y": 0.5086554288864136}, {"X": 0.45003336668014526, "Y": 0.5282398462295532}, {"X": 0.3050547242164612, "Y": 0.5279508233070374}]}, "Id": "4cc79a2e-b3b4-4a0d-b02e-8d45e3a0a8d7", "Relationships": [{"Type": "CHILD", "Ids": ["1b4a903f-9df2-422a-96ef-222823afa52f", "0a9fb780-2f04-4471-9cab-e30abafe5448"]}]}, {"BlockType": "LINE", "Confidence": 86.21206665039062, "Text": "- 40,440 lb", "Geometry": {"BoundingBox": {"Width": 0.17013688385486603, "Height": 0.021943654865026474, "Left": 0.5833615660667419, "Top": 0.5091004967689514}, "Polygon": [{"X": 0.5834619402885437, "Y": 0.5091004967689514}, {"X": 0.7534984946250916, "Y": 0.5094313025474548}, {"X": 0.7534156441688538, "Y": 0.5310441851615906}, {"X": 0.5833615660667419, "Y": 0.5307042002677917}]}, "Id": "7e792ee1-eae1-424a-8080-e564d3224bf2", "Relationships": [{"Type": "CHILD", "Ids": ["2b0f8066-2a5d-40d7-a0b1-b6ba89e862a1", "d1137365-2913-4ad0-9a4a-5aa1339a6ab6", "c09010ea-78a9-4eeb-8cb1-460ea5ec87f9"]}]}, {"BlockType": "LINE", "Confidence": 99.81086730957031, "Text": "Out: 8:53am", "Geometry": {"BoundingBox": {"Width": 0.15959735214710236, "Height": 0.01970573328435421, "Left": 0.29038214683532715, "Top": 0.5357218384742737}, "Polygon": [{"X": 0.2904992997646332, "Y": 0.5357218384742737}, {"X": 0.4499794840812683, "Y": 0.5360429286956787}, {"X": 0.4498770833015442, "Y": 0.555427610874176}, {"X": 0.29038214683532715, "Y": 0.5550987720489502}]}, "Id": "190a6590-c7e7-4740-977b-bcbdb3ef3bb0", "Relationships": [{"Type": "CHILD", "Ids": ["d6037f00-3069-4397-b496-db8a1cd9bad2", "4c08136d-7321-4427-bc25-943ed4214d1c"]}]}, {"BlockType": "LINE", "Confidence": 89.80648040771484, "Text": "- 33,560 lb", "Geometry": {"BoundingBox": {"Width": 0.1702173799276352, "Height": 0.020869417116045952, "Left": 0.5833000540733337, "Top": 0.5364000201225281}, "Polygon": [{"X": 0.5833953619003296, "Y": 0.5364000201225281}, {"X": 0.7535174489021301, "Y": 0.5367425680160522}, {"X": 0.7534387707710266, "Y": 0.5572694540023804}, {"X": 0.5833000540733337, "Y": 0.556918203830719}]}, "Id": "438f3271-64e8-4676-9658-0fa196b3a9a2", "Relationships": [{"Type": "CHILD", "Ids": ["59c2a132-1c00-48f3-8087-c361721cb8b7", "234a76dc-f7e5-45b8-8fd0-2cbd6ddf278e", "eb568bf5-de85-490f-8cf6-80b7c01f1633"]}]}, {"BlockType": "LINE", "Confidence": 99.73552703857422, "Text": "Net:", "Geometry": {"BoundingBox": {"Width": 0.045805882662534714, "Height": 0.019209211692214012, "Left": 0.29047122597694397, "Top": 0.5632539391517639}, "Polygon": [{"X": 0.2905867397785187, "Y": 0.5632539391517639}, {"X": 0.3362770974636078, "Y": 0.5633490681648254}, {"X": 0.33616572618484497, "Y": 0.5824631452560425}, {"X": 0.29047122597694397, "Y": 0.582365870475769}]}, "Id": "7ec43301-8c62-4979-8972-8927bc1e4df6", "Relationships": [{"Type": "CHILD", "Ids": ["c36e2f37-6d1a-4ba1-83cb-c7e57afab616"]}]}, {"BlockType": "LINE", "Confidence": 96.1876220703125, "Text": "-", "Geometry": {"BoundingBox": {"Width": 0.011214141733944416, "Height": 0.003373469226062298, "Left": 0.5834993720054626, "Top": 0.5727984309196472}, "Polygon": [{"X": 0.583514928817749, "Y": 0.5727984309196472}, {"X": 0.5947135090827942, "Y": 0.5728219747543335}, {"X": 0.5946981310844421, "Y": 0.576171875}, {"X": 0.5834993720054626, "Y": 0.5761482119560242}]}, "Id": "0034085e-02fb-4f39-808b-cca4fd98c9ae", "Relationships": [{"Type": "CHILD", "Ids": ["239a1485-1a4f-4661-a2a7-bb5f2c48b070"]}]}, {"BlockType": "LINE", "Confidence": 83.52149963378906, "Text": "6,880 lb", "Geometry": {"BoundingBox": {"Width": 0.09795717895030975, "Height": 0.02162737026810646, "Left": 0.6556759476661682, "Top": 0.5640220046043396}, "Polygon": [{"X": 0.6557680368423462, "Y": 0.5640220046043396}, {"X": 0.7536331415176392, "Y": 0.5642257332801819}, {"X": 0.7535510659217834, "Y": 0.5856493711471558}, {"X": 0.6556759476661682, "Y": 0.5854403376579285}]}, "Id": "e97599f2-af4c-4b21-8da3-636d4d543395", "Relationships": [{"Type": "CHILD", "Ids": ["91092258-348c-495a-a684-a4c1cea56af6", "2f8aa0ca-a278-4aad-8a72-e3c7fa8bb0a1"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Product Description", "Geometry": {"BoundingBox": {"Width": 0.2569567561149597, "Height": 0.02203640341758728, "Left": 0.2044467329978943, "Top": 0.6445315480232239}, "Polygon": [{"X": 0.20458509027957916, "Y": 0.6445315480232239}, {"X": 0.4614035189151764, "Y": 0.6451185345649719}, {"X": 0.4612914025783539, "Y": 0.6665679812431335}, {"X": 0.2044467329978943, "Y": 0.6659671664237976}]}, "Id": "3900522f-b01e-4865-ab44-9064941aafa1", "Relationships": [{"Type": "CHILD", "Ids": ["cd113f59-3e18-4081-97a5-8b49ca142138", "4c6e1264-5766-46b3-aba9-b6fb6db4b337"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.0726456269621849, "Height": 0.01957634463906288, "Left": 0.6675446033477783, "Top": 0.6465558409690857}, "Polygon": [{"X": 0.6676269173622131, "Y": 0.6465558409690857}, {"X": 0.7401902675628662, "Y": 0.6467218995094299}, {"X": 0.740114688873291, "Y": 0.6661322116851807}, {"X": 0.6675446033477783, "Y": 0.6659626364707947}]}, "Id": "8021e415-a4fe-4d96-b3e2-62d2b67a74ae", "Relationships": [{"Type": "CHILD", "Ids": ["a3ea7a4c-7cf6-49a4-8106-67d2b2007427"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "MISC", "Geometry": {"BoundingBox": {"Width": 0.04933863878250122, "Height": 0.01884116604924202, "Left": 0.20351187884807587, "Top": 0.6998048424720764}, "Polygon": [{"X": 0.20363274216651917, "Y": 0.6998048424720764}, {"X": 0.2528505027294159, "Y": 0.699924111366272}, {"X": 0.2527340352535248, "Y": 0.7186459898948669}, {"X": 0.20351187884807587, "Y": 0.7185243964195251}]}, "Id": "094a4ce5-94fc-42e1-9419-151e88bd681e", "Relationships": [{"Type": "CHILD", "Ids": ["64f66ddd-dde0-49aa-8991-579152e8ad5a"]}]}, {"BlockType": "LINE", "Confidence": 98.65972137451172, "Text": "Miscellaneous", "Geometry": {"BoundingBox": {"Width": 0.15976309776306152, "Height": 0.020452886819839478, "Left": 0.3254988491535187, "Top": 0.6990044713020325}, "Polygon": [{"X": 0.32561665773391724, "Y": 0.6990044713020325}, {"X": 0.4852619469165802, "Y": 0.6993909478187561}, {"X": 0.48515939712524414, "Y": 0.7194573283195496}, {"X": 0.3254988491535187, "Y": 0.7190627455711365}]}, "Id": "3a09be19-4ddf-4efa-898a-d1cd2a031cf3", "Relationships": [{"Type": "CHILD", "Ids": ["0fe3be47-cd87-4415-accc-b544fd7c77b1"]}]}, {"BlockType": "LINE", "Confidence": 99.63298797607422, "Text": "3.44 tn", "Geometry": {"BoundingBox": {"Width": 0.08620564639568329, "Height": 0.019474303349852562, "Left": 0.6672330498695374, "Top": 0.7008814215660095}, "Polygon": [{"X": 0.6673147678375244, "Y": 0.7008814215660095}, {"X": 0.7534387111663818, "Y": 0.7010901570320129}, {"X": 0.7533649206161499, "Y": 0.720355749130249}, {"X": 0.6672330498695374, "Y": 0.7201428413391113}]}, "Id": "8b2de28b-ecae-4d9d-8636-f8426a2f2e8d", "Relationships": [{"Type": "CHILD", "Ids": ["b9d7282b-6d38-4242-b7b8-b1c29c2863d3", "a69588bc-8c46-4c70-bddd-f029f62b8552"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Thank you for your business", "Geometry": {"BoundingBox": {"Width": 0.3305330276489258, "Height": 0.022280141711235046, "Left": 0.31302061676979065, "Top": 0.7809587717056274}, "Polygon": [{"X": 0.31314751505851746, "Y": 0.7809587717056274}, {"X": 0.6435536742210388, "Y": 0.7818264365196228}, {"X": 0.6434604525566101, "Y": 0.8032389283180237}, {"X": 0.31302061676979065, "Y": 0.80235356092453}]}, "Id": "4b637a24-ea5c-4c4a-b741-a61df0fbc8a2", "Relationships": [{"Type": "CHILD", "Ids": ["3b586272-7525-45a4-849f-2af78fd71965", "c325bf4b-d0fa-4b5f-9693-22a7cd271cb9", "9433c0fc-62d6-45c8-9bec-7fb557ed2de1", "ec0adb59-f150-4837-8bf6-49943ae8a3e8", "9712926f-97c2-4a45-844f-45f615b05b3b"]}]}, {"BlockType": "LINE", "Confidence": 75.86256408691406, "Text": "CS", "Geometry": {"BoundingBox": {"Width": 0.028356675058603287, "Height": 0.01002954225987196, "Left": 0.7909205555915833, "Top": 0.9664381146430969}, "Polygon": [{"X": 0.7909567952156067, "Y": 0.9664381146430969}, {"X": 0.8192772269248962, "Y": 0.9665254950523376}, {"X": 0.8192423582077026, "Y": 0.9764676690101624}, {"X": 0.7909205555915833, "Y": 0.9763795733451843}]}, "Id": "c2f19b7e-915d-460f-a232-0846040af598", "Relationships": [{"Type": "CHILD", "Ids": ["8bca582b-51f7-40da-bc93-668a0e819e72"]}]}, {"BlockType": "LINE", "Confidence": 27.633432388305664, "Text": "ComS.", "Geometry": {"BoundingBox": {"Width": 0.05078970640897751, "Height": 0.006108875852078199, "Left": 0.8259729743003845, "Top": 0.9705376625061035}, "Polygon": [{"X": 0.8259937167167664, "Y": 0.9705376625061035}, {"X": 0.8767626881599426, "Y": 0.9706948399543762}, {"X": 0.8767434358596802, "Y": 0.9766465425491333}, {"X": 0.8259729743003845, "Y": 0.9764885902404785}]}, "Id": "485013ff-54ec-411b-9e30-8e217ac9491e", "Relationships": [{"Type": "CHILD", "Ids": ["23e7a9cf-4b1f-4034-9b29-8afcba558596"]}]}, {"BlockType": "WORD", "Confidence": 99.40369415283203, "Text": "HAMTRAMCK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.4573183059692383, "Height": 0.03111240454018116, "Left": 0.2522429823875427, "Top": 0.11573531478643417}, "Polygon": [{"X": 0.2524341940879822, "Y": 0.11573531478643417}, {"X": 0.709561288356781, "Y": 0.11617577075958252}, {"X": 0.7094370722770691, "Y": 0.14684772491455078}, {"X": 0.2522429823875427, "Y": 0.14637218415737152}]}, "Id": "ffb233b7-16f8-47f8-8370-c6c76f997f54"}, {"BlockType": "WORD", "Confidence": 99.37440490722656, "Text": "RECYCLING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.45429131388664246, "Height": 0.028220441192388535, "Left": 0.25673964619636536, "Top": 0.14993558824062347}, "Polygon": [{"X": 0.2569119930267334, "Y": 0.14993558824062347}, {"X": 0.7110309600830078, "Y": 0.1504119634628296}, {"X": 0.7109188437461853, "Y": 0.1781560182571411}, {"X": 0.25673964619636536, "Y": 0.17764812707901}]}, "Id": "e6d4de0d-b415-45fb-8861-5e3d83b189fe"}, {"BlockType": "WORD", "Confidence": 89.19742584228516, "Text": "&TRANSFER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13972531259059906, "Height": 0.0156813133507967, "Left": 0.46655911207199097, "Top": 0.18309052288532257}, "Polygon": [{"X": 0.46664002537727356, "Y": 0.18309052288532257}, {"X": 0.6062844395637512, "Y": 0.18324850499629974}, {"X": 0.6062139272689819, "Y": 0.19877183437347412}, {"X": 0.46655911207199097, "Y": 0.1986084282398224}]}, "Id": "7ea6f3ff-f779-441b-87b7-ed1dd0b37332"}, {"BlockType": "WORD", "Confidence": 95.400390625, "Text": "STATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09760177135467529, "Height": 0.015757059678435326, "Left": 0.6111812591552734, "Top": 0.18238194286823273}, "Polygon": [{"X": 0.611251950263977, "Y": 0.18238194286823273}, {"X": 0.7087830305099487, "Y": 0.18249206244945526}, {"X": 0.7087196111679077, "Y": 0.1981390118598938}, {"X": 0.6111812591552734, "Y": 0.19802506268024445}]}, "Id": "4d580aaa-f31a-4a44-9d8c-9648ad7fa697"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "3333", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04909178242087364, "Height": 0.018641704693436623, "Left": 0.27881839871406555, "Top": 0.23633916676044464}, "Polygon": [{"X": 0.2789319157600403, "Y": 0.23633916676044464}, {"X": 0.3279101550579071, "Y": 0.23640112578868866}, {"X": 0.327800989151001, "Y": 0.2549808621406555}, {"X": 0.27881839871406555, "Y": 0.25491663813591003}]}, "Id": "18ec540c-b280-4d3e-91ba-d2dfaf907406"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Hamtramck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11077331006526947, "Height": 0.01910937763750553, "Left": 0.3393577039241791, "Top": 0.2357940822839737}, "Polygon": [{"X": 0.3394680917263031, "Y": 0.2357940822839737}, {"X": 0.45013102889060974, "Y": 0.23593389987945557}, {"X": 0.450030654668808, "Y": 0.2549034655094147}, {"X": 0.3393577039241791, "Y": 0.25475838780403137}]}, "Id": "32a9c2ba-8971-4023-9268-74da58c9caaa"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Dr,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0329819954931736, "Height": 0.02121947892010212, "Left": 0.4616861343383789, "Top": 0.2360619753599167}, "Polygon": [{"X": 0.46179696917533875, "Y": 0.2360619753599167}, {"X": 0.4946681261062622, "Y": 0.23610351979732513}, {"X": 0.4945605993270874, "Y": 0.25728145241737366}, {"X": 0.4616861343383789, "Y": 0.2572381794452667}]}, "Id": "1f9718ba-a1cb-4484-b9b8-a8b4f2a1eb4f"}, {"BlockType": "WORD", "Confidence": 99.3223876953125, "Text": "<PERSON><PERSON><PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11780815571546555, "Height": 0.021158438175916672, "Left": 0.5097432732582092, "Top": 0.23553982377052307}, "Polygon": [{"X": 0.509848415851593, "Y": 0.23553982377052307}, {"X": 0.6275514364242554, "Y": 0.23568838834762573}, {"X": 0.6274581551551819, "Y": 0.25669825077056885}, {"X": 0.5097432732582092, "Y": 0.25654348731040955}]}, "Id": "f3e08c3e-de44-41ca-bb45-e4f210d1b23c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022651586681604385, "Height": 0.01889771595597267, "Left": 0.6442069411277771, "Top": 0.2351199984550476}, "Polygon": [{"X": 0.6442891955375671, "Y": 0.2351199984550476}, {"X": 0.6668584942817688, "Y": 0.23514845967292786}, {"X": 0.6667782664299011, "Y": 0.25401771068573}, {"X": 0.6442069411277771, "Y": 0.25398820638656616}]}, "Id": "6de07a4f-7653-4c83-a119-878832c5a7af"}, {"BlockType": "WORD", "Confidence": 99.384765625, "Text": "(313)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059263840317726135, "Height": 0.021893857046961784, "Left": 0.39100348949432373, "Top": 0.2629093825817108}, "Polygon": [{"X": 0.39112502336502075, "Y": 0.2629093825817108}, {"X": 0.45026731491088867, "Y": 0.2629881203174591}, {"X": 0.4501519501209259, "Y": 0.28480324149131775}, {"X": 0.39100348949432373, "Y": 0.284721314907074}]}, "Id": "a030273b-8126-488a-920c-13dfc75a8910"}, {"BlockType": "WORD", "Confidence": 99.56951904296875, "Text": "871-5000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.097746342420578, "Height": 0.019520670175552368, "Left": 0.4614284038543701, "Top": 0.2627018988132477}, "Polygon": [{"X": 0.4615298807621002, "Y": 0.2627018988132477}, {"X": 0.5591747164726257, "Y": 0.26283180713653564}, {"X": 0.5590822696685791, "Y": 0.28222256898880005}, {"X": 0.4614284038543701, "Y": 0.28208795189857483}]}, "Id": "1dc48dc6-ef90-4f48-8788-ac4aacc648f2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Ticket:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08316527307033539, "Height": 0.01948382891714573, "Left": 0.2541237473487854, "Top": 0.34505006670951843}, "Polygon": [{"X": 0.25424420833587646, "Y": 0.34505006670951843}, {"X": 0.3372890055179596, "Y": 0.34517768025398254}, {"X": 0.33717620372772217, "Y": 0.3645339012145996}, {"X": 0.2541237473487854, "Y": 0.36440223455429077}]}, "Id": "f043af6f-0f11-47d2-90ef-164a45e4ff9b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "423087-1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09643173962831497, "Height": 0.019241346046328545, "Left": 0.3645365536212921, "Top": 0.3451991677284241}, "Polygon": [{"X": 0.3646453022956848, "Y": 0.3451991677284241}, {"X": 0.4609682857990265, "Y": 0.34534719586372375}, {"X": 0.46086832880973816, "Y": 0.36444053053855896}, {"X": 0.3645365536212921, "Y": 0.3642878830432892}]}, "Id": "1d0618cc-b5aa-4cd5-9e3e-5ca0efa1135f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05826961621642113, "Height": 0.018986545503139496, "Left": 0.2789810299873352, "Top": 0.3724570870399475}, "Polygon": [{"X": 0.2790963649749756, "Y": 0.3724570870399475}, {"X": 0.33725064992904663, "Y": 0.37255042791366577}, {"X": 0.3371405303478241, "Y": 0.3914436399936676}, {"X": 0.2789810299873352, "Y": 0.3913475275039673}]}, "Id": "65a8cc0a-abc9-419b-b22f-abdf08e382fe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "8/18/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11001550406217575, "Height": 0.01916193962097168, "Left": 0.36398831009864807, "Top": 0.37255769968032837}, "Polygon": [{"X": 0.364096462726593, "Y": 0.37255769968032837}, {"X": 0.47400379180908203, "Y": 0.37273409962654114}, {"X": 0.4739055931568146, "Y": 0.39171963930130005}, {"X": 0.36398831009864807, "Y": 0.39153799414634705}]}, "Id": "583c0521-dc18-42f3-a265-1625cec0eda7"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Customer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10707425326108932, "Height": 0.019167769700288773, "Left": 0.23030568659305573, "Top": 0.39975112676620483}, "Polygon": [{"X": 0.23042601346969604, "Y": 0.39975112676620483}, {"X": 0.33737993240356445, "Y": 0.39993011951446533}, {"X": 0.33726930618286133, "Y": 0.4189188778400421}, {"X": 0.23030568659305573, "Y": 0.4187348186969757}]}, "Id": "54c90208-5189-4591-bbda-19e4926786b6"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "WM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02555849589407444, "Height": 0.019154196605086327, "Left": 0.36382487416267395, "Top": 0.3997593820095062}, "Polygon": [{"X": 0.3639337718486786, "Y": 0.3997593820095062}, {"X": 0.38938337564468384, "Y": 0.39980196952819824}, {"X": 0.38927677273750305, "Y": 0.4189136028289795}, {"X": 0.36382487416267395, "Y": 0.4188697934150696}]}, "Id": "0ffaf39d-5eb8-4750-a1b0-913dfbac4784"}, {"BlockType": "WORD", "Confidence": 88.5692367553711, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01195585448294878, "Height": 0.004109513014554977, "Left": 0.40096232295036316, "Top": 0.40799078345298767}, "Polygon": [{"X": 0.40098491311073303, "Y": 0.40799078345298767}, {"X": 0.41291818022727966, "Y": 0.40801098942756653}, {"X": 0.4128958284854889, "Y": 0.41210028529167175}, {"X": 0.40096232295036316, "Y": 0.41207996010780334}]}, "Id": "c462dd93-acd8-4f26-b482-a32f0be31749"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Target", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07255052775144577, "Height": 0.020958099514245987, "Left": 0.4248344898223877, "Top": 0.39959946274757385}, "Polygon": [{"X": 0.4249471127986908, "Y": 0.39959946274757385}, {"X": 0.49738502502441406, "Y": 0.39972060918807983}, {"X": 0.4972795844078064, "Y": 0.42055755853652954}, {"X": 0.4248344898223877, "Y": 0.4204326570034027}]}, "Id": "129227ab-ffb3-499e-b9ef-d03673102d7f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Hauler:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08301156759262085, "Height": 0.01945175603032112, "Left": 0.25409260392189026, "Top": 0.42690253257751465}, "Polygon": [{"X": 0.25421273708343506, "Y": 0.42690253257751465}, {"X": 0.3371041715145111, "Y": 0.42704686522483826}, {"X": 0.33699169754981995, "Y": 0.44635429978370667}, {"X": 0.25409260392189026, "Y": 0.4462059438228607}]}, "Id": "1d58ac0e-ea84-459a-97b9-eda6faca5d67"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "TKT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03684106841683388, "Height": 0.01896156370639801, "Left": 0.36427316069602966, "Top": 0.4270608425140381}, "Polygon": [{"X": 0.364380806684494, "Y": 0.4270608425140381}, {"X": 0.40111422538757324, "Y": 0.4271247982978821}, {"X": 0.4010099172592163, "Y": 0.4460224211215973}, {"X": 0.36427316069602966, "Y": 0.4459567070007324}]}, "Id": "a6c5afa7-0fb5-4e2c-b82e-450e6ec0b17f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Truck:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07064451277256012, "Height": 0.019384946674108505, "Left": 0.2665557563304901, "Top": 0.45415517687797546}, "Polygon": [{"X": 0.2666743993759155, "Y": 0.45415517687797546}, {"X": 0.33720025420188904, "Y": 0.4542827606201172}, {"X": 0.33708807826042175, "Y": 0.4735400974750519}, {"X": 0.2665557563304901, "Y": 0.47340908646583557}]}, "Id": "4b7ec077-ff35-4b0b-a952-545ac1c3bfdf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1125", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048254434019327164, "Height": 0.01897241733968258, "Left": 0.3652084171772003, "Top": 0.4544043242931366}, "Polygon": [{"X": 0.36531588435173035, "Y": 0.4544043242931366}, {"X": 0.4134628474712372, "Y": 0.4544914662837982}, {"X": 0.41335973143577576, "Y": 0.4733767509460449}, {"X": 0.3652084171772003, "Y": 0.47328734397888184}]}, "Id": "0a625826-7fc3-4f64-ad17-54c695c9d0c6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ref", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0365423709154129, "Height": 0.01905057765543461, "Left": 0.20534327626228333, "Top": 0.48152589797973633}, "Polygon": [{"X": 0.20546579360961914, "Y": 0.48152589797973633}, {"X": 0.24188564717769623, "Y": 0.4815942943096161}, {"X": 0.24176642298698425, "Y": 0.5005764961242676}, {"X": 0.20534327626228333, "Y": 0.5005063414573669}]}, "Id": "ccd2d6f3-64f6-473e-a2be-3f231dfa2100"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0827149972319603, "Height": 0.01955461874604225, "Left": 0.253928005695343, "Top": 0.4812966287136078}, "Polygon": [{"X": 0.2540487051010132, "Y": 0.4812966287136078}, {"X": 0.3366430103778839, "Y": 0.48145169019699097}, {"X": 0.3365299701690674, "Y": 0.5008512735366821}, {"X": 0.253928005695343, "Y": 0.5006921887397766}]}, "Id": "091b54ae-026a-4202-9bec-3b8f2ae209d2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "In:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03164073824882507, "Height": 0.019640136510133743, "Left": 0.3050547242164612, "Top": 0.508373498916626}, "Polygon": [{"X": 0.3051717281341553, "Y": 0.508373498916626}, {"X": 0.33669546246528625, "Y": 0.5084348320960999}, {"X": 0.33658137917518616, "Y": 0.5280136466026306}, {"X": 0.3050547242164612, "Y": 0.5279508233070374}]}, "Id": "1b4a903f-9df2-422a-96ef-222823afa52f"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "7:15am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0740489587187767, "Height": 0.01945650577545166, "Left": 0.37608781456947327, "Top": 0.5085247159004211}, "Polygon": [{"X": 0.3761966824531555, "Y": 0.5085247159004211}, {"X": 0.45013678073883057, "Y": 0.5086684823036194}, {"X": 0.4500347375869751, "Y": 0.5279812216758728}, {"X": 0.37608781456947327, "Y": 0.5278338193893433}]}, "Id": "0a9fb780-2f04-4471-9cab-e30abafe5448"}, {"BlockType": "WORD", "Confidence": 96.09474182128906, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011559183709323406, "Height": 0.003332126885652542, "Left": 0.5834044218063354, "Top": 0.5181783437728882}, "Polygon": [{"X": 0.5834197998046875, "Y": 0.5181783437728882}, {"X": 0.5949636101722717, "Y": 0.5182011127471924}, {"X": 0.594948410987854, "Y": 0.5215104818344116}, {"X": 0.5834044218063354, "Y": 0.5214876532554626}]}, "Id": "2b0f8066-2a5d-40d7-a0b1-b6ba89e862a1"}, {"BlockType": "WORD", "Confidence": 99.4776382446289, "Text": "40,440", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07423470169305801, "Height": 0.021528447046875954, "Left": 0.6433168649673462, "Top": 0.5094438791275024}, "Polygon": [{"X": 0.6434100270271301, "Y": 0.5094438791275024}, {"X": 0.7175515294075012, "Y": 0.5095881223678589}, {"X": 0.7174659371376038, "Y": 0.5309723019599915}, {"X": 0.6433168649673462, "Y": 0.5308240652084351}]}, "Id": "d1137365-2913-4ad0-9a4a-5aa1339a6ab6"}, {"BlockType": "WORD", "Confidence": 63.063819885253906, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022362947463989258, "Height": 0.019246360287070274, "Left": 0.7311355471611023, "Top": 0.5093879103660583}, "Polygon": [{"X": 0.7312111854553223, "Y": 0.5093879103660583}, {"X": 0.7534984946250916, "Y": 0.5094313025474548}, {"X": 0.753424882888794, "Y": 0.5286343097686768}, {"X": 0.7311355471611023, "Y": 0.5285898447036743}]}, "Id": "c09010ea-78a9-4eeb-8cb1-460ea5ec87f9"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Out:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04624294862151146, "Height": 0.019472019746899605, "Left": 0.29038214683532715, "Top": 0.5357218384742737}, "Polygon": [{"X": 0.2904992997646332, "Y": 0.5357218384742737}, {"X": 0.3366250991821289, "Y": 0.5358147025108337}, {"X": 0.33651217818260193, "Y": 0.5551939010620117}, {"X": 0.29038214683532715, "Y": 0.5550987720489502}]}, "Id": "d6037f00-3069-4397-b496-db8a1cd9bad2"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "8:53am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07405496388673782, "Height": 0.01914820820093155, "Left": 0.37592315673828125, "Top": 0.5361568331718445}, "Polygon": [{"X": 0.37603023648262024, "Y": 0.5361568331718445}, {"X": 0.4499781131744385, "Y": 0.5363057255744934}, {"X": 0.44987770915031433, "Y": 0.555305004119873}, {"X": 0.37592315673828125, "Y": 0.5551525950431824}]}, "Id": "4c08136d-7321-4427-bc25-943ed4214d1c"}, {"BlockType": "WORD", "Confidence": 98.5313720703125, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011406687088310719, "Height": 0.003578634699806571, "Left": 0.5833377242088318, "Top": 0.5452490448951721}, "Polygon": [{"X": 0.5833542346954346, "Y": 0.5452490448951721}, {"X": 0.5947444438934326, "Y": 0.5452722311019897}, {"X": 0.5947281122207642, "Y": 0.5488277077674866}, {"X": 0.5833377242088318, "Y": 0.5488044023513794}]}, "Id": "59c2a132-1c00-48f3-8087-c361721cb8b7"}, {"BlockType": "WORD", "Confidence": 99.25720977783203, "Text": "33,560", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07423632591962814, "Height": 0.020200202241539955, "Left": 0.6431987285614014, "Top": 0.5369947552680969}, "Polygon": [{"X": 0.6432861089706421, "Y": 0.5369947552680969}, {"X": 0.7174350619316101, "Y": 0.537144124507904}, {"X": 0.7173547744750977, "Y": 0.5571949481964111}, {"X": 0.6431987285614014, "Y": 0.5570418238639832}]}, "Id": "234a76dc-f7e5-45b8-8fd0-2cbd6ddf278e"}, {"BlockType": "WORD", "Confidence": 71.630859375, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022466691210865974, "Height": 0.01923169195652008, "Left": 0.7310507297515869, "Top": 0.536697506904602}, "Polygon": [{"X": 0.7311263084411621, "Y": 0.536697506904602}, {"X": 0.7535174489021301, "Y": 0.5367425680160522}, {"X": 0.7534438967704773, "Y": 0.5559291839599609}, {"X": 0.7310507297515869, "Y": 0.55588299036026}]}, "Id": "eb568bf5-de85-490f-8cf6-80b7c01f1633"}, {"BlockType": "WORD", "Confidence": 99.73552703857422, "Text": "Net:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045805882662534714, "Height": 0.019209211692214012, "Left": 0.29047122597694397, "Top": 0.5632539391517639}, "Polygon": [{"X": 0.2905867397785187, "Y": 0.5632539391517639}, {"X": 0.3362770974636078, "Y": 0.5633490681648254}, {"X": 0.33616572618484497, "Y": 0.5824631452560425}, {"X": 0.29047122597694397, "Y": 0.582365870475769}]}, "Id": "c36e2f37-6d1a-4ba1-83cb-c7e57afab616"}, {"BlockType": "WORD", "Confidence": 96.1876220703125, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011214141733944416, "Height": 0.003373469226062298, "Left": 0.5834993720054626, "Top": 0.5727984309196472}, "Polygon": [{"X": 0.583514928817749, "Y": 0.5727984309196472}, {"X": 0.5947135090827942, "Y": 0.5728219747543335}, {"X": 0.5946981310844421, "Y": 0.576171875}, {"X": 0.5834993720054626, "Y": 0.5761482119560242}]}, "Id": "239a1485-1a4f-4661-a2a7-bb5f2c48b070"}, {"BlockType": "WORD", "Confidence": 99.55895233154297, "Text": "6,880", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061782896518707275, "Height": 0.021431129425764084, "Left": 0.6556759476661682, "Top": 0.5641409754753113}, "Polygon": [{"X": 0.6557675004005432, "Y": 0.5641409754753113}, {"X": 0.7174588441848755, "Y": 0.5642694234848022}, {"X": 0.7173735499382019, "Y": 0.5855721235275269}, {"X": 0.6556759476661682, "Y": 0.5854403376579285}]}, "Id": "91092258-348c-495a-a684-a4c1cea56af6"}, {"BlockType": "WORD", "Confidence": 67.48405456542969, "Text": "lb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022466374561190605, "Height": 0.019288145005702972, "Left": 0.7311667799949646, "Top": 0.5641791224479675}, "Polygon": [{"X": 0.7312425374984741, "Y": 0.5641791224479675}, {"X": 0.7536331415176392, "Y": 0.5642257332801819}, {"X": 0.753559410572052, "Y": 0.5834673047065735}, {"X": 0.7311667799949646, "Y": 0.5834195613861084}]}, "Id": "2f8aa0ca-a278-4aad-8a72-e3c7fa8bb0a1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08456649631261826, "Height": 0.019627654924988747, "Left": 0.20445747673511505, "Top": 0.6448734402656555}, "Polygon": [{"X": 0.20458288490772247, "Y": 0.6448734402656555}, {"X": 0.2890239655971527, "Y": 0.645066499710083}, {"X": 0.28890636563301086, "Y": 0.6645011305809021}, {"X": 0.20445747673511505, "Y": 0.6643038988113403}]}, "Id": "cd113f59-3e18-4081-97a5-8b49ca142138"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1352027952671051, "Height": 0.021758146584033966, "Left": 0.3262006938457489, "Top": 0.6448098421096802}, "Polygon": [{"X": 0.3263266086578369, "Y": 0.6448098421096802}, {"X": 0.4614035189151764, "Y": 0.6451185345649719}, {"X": 0.4612914025783539, "Y": 0.6665679812431335}, {"X": 0.3262006938457489, "Y": 0.6662519574165344}]}, "Id": "4c6e1264-5766-46b3-aba9-b6fb6db4b337"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0726456269621849, "Height": 0.01957634463906288, "Left": 0.6675446033477783, "Top": 0.6465558409690857}, "Polygon": [{"X": 0.6676269173622131, "Y": 0.6465558409690857}, {"X": 0.7401902675628662, "Y": 0.6467218995094299}, {"X": 0.740114688873291, "Y": 0.6661322116851807}, {"X": 0.6675446033477783, "Y": 0.6659626364707947}]}, "Id": "a3ea7a4c-7cf6-49a4-8106-67d2b2007427"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MISC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04933863878250122, "Height": 0.01884116604924202, "Left": 0.20351187884807587, "Top": 0.6998048424720764}, "Polygon": [{"X": 0.20363274216651917, "Y": 0.6998048424720764}, {"X": 0.2528505027294159, "Y": 0.699924111366272}, {"X": 0.2527340352535248, "Y": 0.7186459898948669}, {"X": 0.20351187884807587, "Y": 0.7185243964195251}]}, "Id": "64f66ddd-dde0-49aa-8991-579152e8ad5a"}, {"BlockType": "WORD", "Confidence": 98.65972137451172, "Text": "Miscellaneous", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15976309776306152, "Height": 0.020452886819839478, "Left": 0.3254988491535187, "Top": 0.6990044713020325}, "Polygon": [{"X": 0.32561665773391724, "Y": 0.6990044713020325}, {"X": 0.4852619469165802, "Y": 0.6993909478187561}, {"X": 0.48515939712524414, "Y": 0.7194573283195496}, {"X": 0.3254988491535187, "Y": 0.7190627455711365}]}, "Id": "0fe3be47-cd87-4415-accc-b544fd7c77b1"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "3.44", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05035341903567314, "Height": 0.019090542569756508, "Left": 0.6672343015670776, "Top": 0.7008814215660095}, "Polygon": [{"X": 0.6673147678375244, "Y": 0.7008814215660095}, {"X": 0.7175877094268799, "Y": 0.7010032534599304}, {"X": 0.7175118327140808, "Y": 0.7199719548225403}, {"X": 0.6672343015670776, "Y": 0.7198477387428284}]}, "Id": "b9d7282b-6d38-4242-b7b8-b1c29c2863d3"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "tn", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023810137063264847, "Height": 0.018819406628608704, "Left": 0.7296266555786133, "Top": 0.7015362977981567}, "Polygon": [{"X": 0.7297006249427795, "Y": 0.7015362977981567}, {"X": 0.753436803817749, "Y": 0.7015938758850098}, {"X": 0.7533649206161499, "Y": 0.720355749130249}, {"X": 0.7296266555786133, "Y": 0.7202970385551453}]}, "Id": "a69588bc-8c46-4c70-bddd-f029f62b8552"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Thank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06165425479412079, "Height": 0.019338693469762802, "Left": 0.31303268671035767, "Top": 0.7811427712440491}, "Polygon": [{"X": 0.3131464123725891, "Y": 0.7811427712440491}, {"X": 0.37468695640563965, "Y": 0.7813043594360352}, {"X": 0.3745788335800171, "Y": 0.8004814386367798}, {"X": 0.31303268671035767, "Y": 0.8003168106079102}]}, "Id": "3b586272-7525-45a4-849f-2af78fd71965"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "you", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03759518265724182, "Height": 0.01805182360112667, "Left": 0.38583484292030334, "Top": 0.7845551371574402}, "Polygon": [{"X": 0.38593506813049316, "Y": 0.7845551371574402}, {"X": 0.42343002557754517, "Y": 0.784653902053833}, {"X": 0.42333298921585083, "Y": 0.8026069402694702}, {"X": 0.38583484292030334, "Y": 0.8025064468383789}]}, "Id": "c325bf4b-d0fa-4b5f-9693-22a7cd271cb9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03671589493751526, "Height": 0.01945933699607849, "Left": 0.4358672797679901, "Top": 0.7812813520431519}, "Polygon": [{"X": 0.4359707534313202, "Y": 0.7812813520431519}, {"X": 0.47258317470550537, "Y": 0.7813774943351746}, {"X": 0.4724830687046051, "Y": 0.800740659236908}, {"X": 0.4358672797679901, "Y": 0.800642728805542}]}, "Id": "9433c0fc-62d6-45c8-9bec-7fb557ed2de1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04937143623828888, "Height": 0.01818336732685566, "Left": 0.48405665159225464, "Top": 0.7847604751586914}, "Polygon": [{"X": 0.48414894938468933, "Y": 0.7847604751586914}, {"X": 0.5334280729293823, "Y": 0.7848902940750122}, {"X": 0.5333400368690491, "Y": 0.8029438853263855}, {"X": 0.48405665159225464, "Y": 0.8028118014335632}]}, "Id": "ec0adb59-f150-4837-8bf6-49943ae8a3e8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09798260033130646, "Height": 0.02013889141380787, "Left": 0.5455707311630249, "Top": 0.7816368341445923}, "Polygon": [{"X": 0.5456665754318237, "Y": 0.7816368341445923}, {"X": 0.6435533761978149, "Y": 0.7818939089775085}, {"X": 0.6434668302536011, "Y": 0.8017757534980774}, {"X": 0.5455707311630249, "Y": 0.8015137910842896}]}, "Id": "9712926f-97c2-4a45-844f-45f615b05b3b"}, {"BlockType": "WORD", "Confidence": 75.86256408691406, "Text": "CS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028357896953821182, "Height": 0.010364576242864132, "Left": 0.7909193634986877, "Top": 0.9664381146430969}, "Polygon": [{"X": 0.7909567952156067, "Y": 0.9664381146430969}, {"X": 0.8192772269248962, "Y": 0.9665254950523376}, {"X": 0.8192411661148071, "Y": 0.9768027067184448}, {"X": 0.7909193634986877, "Y": 0.976714551448822}]}, "Id": "8bca582b-51f7-40da-bc93-668a0e819e72"}, {"BlockType": "WORD", "Confidence": 27.633432388305664, "Text": "ComS.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05079087242484093, "Height": 0.006443957798182964, "Left": 0.8259718418121338, "Top": 0.9705376625061035}, "Polygon": [{"X": 0.8259937167167664, "Y": 0.9705376625061035}, {"X": 0.8767626881599426, "Y": 0.9706948399543762}, {"X": 0.8767423629760742, "Y": 0.9769816398620605}, {"X": 0.8259718418121338, "Y": 0.976823627948761}]}, "Id": "23e7a9cf-4b1f-4034-9b29-8afcba558596"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "91f01571-c155-4ef0-9517-518509d1aae9", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "91f01571-c155-4ef0-9517-518509d1aae9", "content-type": "application/x-amz-json-1.1", "content-length": "41672", "date": "Wed, 24 Sep 2025 12:46:19 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "2e718cd7-540b-4e33-8ece-f32e3f96f4c0", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:46:22 GMT", "content-type": "application/json", "content-length": "894", "connection": "keep-alive", "x-amzn-requestid": "2e718cd7-540b-4e33-8ece-f32e3f96f4c0"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page present. Let's analyze content. It's a document likely from a recycling and transfer station. It lists Ticket, Date, Customer, Hauler, Truck, times in and out, weights, net weight. Likely a \"scale ticket\" because it has weight info. Keywords: \"Ticket\", \"In\", \"Out\", \"lb\", \"Net\". That matches scale ticket definition. So doc_type: \"scale_ticket\". Also page number? We should output array with objects for pages. There's one page, page_no: 1. So output: documents: [{page_no:1, doc_type:\"scale_ticket\"}]. Use tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"scale_ticket\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2667, "outputTokens": 156, "totalTokens": 2823}, "metrics": {"latencyMs": 805}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/25c649ea_NLUB3HCM1QWW6FXJBEY3.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nHAM<PERSON>AMCK\nRECYCLING\n&TRANSFER STATION\n3333 Hamtramck Dr, Hamtramck, MI\n**************\nTicket: 423087-1\nDate: 8/18/2025\nCustomer: WM - Target\nHauler: TKT\nTruck: 1125\nRef Number:\nIn: 7:15am\n- 40,440 lb\nOut: 8:53am\n- 33,560 lb\nNet:\n-\n6,880 lb\nProduct Description\nAmount\nMISC\nMiscellaneous\n3.44 tn\nThank you for your business\nCS\nComS.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}