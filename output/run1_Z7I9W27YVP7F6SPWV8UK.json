{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 8.66221307660453e-05, "Y": 0.0}, {"X": 1.0, "Y": 0.0002699900942388922}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "214cbd51-22de-4759-a3a6-386e5a7102d0", "Relationships": [{"Type": "CHILD", "Ids": ["539919c8-e31b-408c-8c91-4801aa75c4e7", "2e303f38-a446-4ce2-bd8e-eaa1a0159241", "db5b9b92-292b-4d96-a448-73fe2e6548d4", "9f449e3e-56d1-4561-b126-fa0ac3c19c41", "2f6a0d7c-4481-4b04-a24e-4421a4f3d244", "993f479d-60ea-494d-ac00-e5277859142e", "a02df1e5-6a39-45a8-b68c-d5e18467c2b5", "997cf5de-be5c-4925-b5ce-8ac0bed632b4", "07e9a769-666e-416f-834a-1b0e3a2325f5", "61754127-bf7b-4a4a-a73e-655e20973c9b", "33222b77-8771-44cf-8a85-8a65599835d8", "d1f5bd2e-97b5-4d3a-a8da-b23cd375d476", "ef242c6d-7677-4578-a018-0c32ab01720c", "f90a17bc-2132-4a50-bb6d-6c52907aeb24", "fe2c500d-5ace-44e2-a524-b2ecf3d0c871", "7d5d9ded-2226-46a6-9df2-7aa51647272b", "508f7110-4e3d-4d7f-af03-13036fbfae98", "c8b1323f-d2f4-4f6f-b4a2-8180a76473bb", "b4474327-89d4-45f0-99fe-5c22dff16f53", "e2302f96-8099-4631-b826-2f338fdd751d", "c4bfa63c-baa2-4d18-9b23-d30f6f901161", "673654fb-da7d-447f-8682-0c1889915825", "f3dcae61-45b3-4f86-be27-162b7d2baebf", "915e239e-e203-491b-a202-23fc44610a1f", "8f909dc7-ecc4-4343-8649-bf3bd3402747", "fa8197f8-71c5-4757-8f9c-7fc4ae5b3396", "7129f795-3a80-4337-bb07-e84785fbc326", "0a59f316-1ef7-4687-8c84-5e74526f2d72", "011f90f1-7cbc-46e1-82b9-e636733bb4ae", "57bfa6fc-01db-48cf-9699-c89737ebf264", "0169022f-e3c1-441d-9b13-1b11e2a37b52", "f3176676-ee7d-4fd6-b806-bae192eecac2", "29835208-cdd3-4219-a30e-f59eb1f0214b", "a3b80d73-4cc4-4816-9326-747c1cd8250e", "c2111667-5072-47a6-bead-92fe91040a59", "b4034c21-79fc-4aba-80bf-d7d3ac928b7b", "9a012a42-48fa-413d-849e-2cb2a7e445e8", "8619eaa3-2322-490e-a647-340718118262", "fef3187a-0c62-48bb-925d-e70c79521073"]}]}, {"BlockType": "LINE", "Confidence": 15.540496826171875, "Text": "20011", "Geometry": {"BoundingBox": {"Width": 0.03969245404005051, "Height": 0.012352356687188148, "Left": 0.1096276342868805, "Top": 0.05979067087173462}, "Polygon": [{"X": 0.10975544899702072, "Y": 0.05979067087173462}, {"X": 0.1493200808763504, "Y": 0.060122840106487274}, {"X": 0.149192214012146, "Y": 0.07214302569627762}, {"X": 0.1096276342868805, "Y": 0.07181035727262497}]}, "Id": "539919c8-e31b-408c-8c91-4801aa75c4e7", "Relationships": [{"Type": "CHILD", "Ids": ["a45c2755-9008-4dd5-adb1-9d359f70d090"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "Saw 8785229", "Geometry": {"BoundingBox": {"Width": 0.41005200147628784, "Height": 0.055516649037599564, "Left": 0.4671572744846344, "Top": 0.04370582848787308}, "Polygon": [{"X": 0.467712938785553, "Y": 0.04370582848787308}, {"X": 0.8772092461585999, "Y": 0.04713558033108711}, {"X": 0.8766510486602783, "Y": 0.09922247380018234}, {"X": 0.4671572744846344, "Y": 0.09577028453350067}]}, "Id": "2e303f38-a446-4ce2-bd8e-eaa1a0159241", "Relationships": [{"Type": "CHILD", "Ids": ["5a45fef6-9ec3-432d-9625-f5a65e6420ce", "171bd6a4-9416-46f5-8496-f5c109ca5611"]}]}, {"BlockType": "LINE", "Confidence": 16.427175521850586, "Text": "FMBVB", "Geometry": {"BoundingBox": {"Width": 0.22239433228969574, "Height": 0.0352904386818409, "Left": 0.4028277099132538, "Top": 0.12224309891462326}, "Polygon": [{"X": 0.4031839966773987, "Y": 0.12224309891462326}, {"X": 0.6252220273017883, "Y": 0.1241212859749794}, {"X": 0.6248648762702942, "Y": 0.15753354132175446}, {"X": 0.4028277099132538, "Y": 0.15564754605293274}]}, "Id": "db5b9b92-292b-4d96-a448-73fe2e6548d4", "Relationships": [{"Type": "CHILD", "Ids": ["10a1a9ee-baf2-4933-a034-eae56dc493a7"]}]}, {"BlockType": "LINE", "Confidence": 96.53011322021484, "Text": "SAWYER NURSERY.", "Geometry": {"BoundingBox": {"Width": 0.4289090037345886, "Height": 0.028184739872813225, "Left": 0.298774778842926, "Top": 0.16971662640571594}, "Polygon": [{"X": 0.2990361154079437, "Y": 0.16971662640571594}, {"X": 0.7276837825775146, "Y": 0.17336434125900269}, {"X": 0.727421224117279, "Y": 0.19790136814117432}, {"X": 0.298774778842926, "Y": 0.19424259662628174}]}, "Id": "9f449e3e-56d1-4561-b126-fa0ac3c19c41", "Relationships": [{"Type": "CHILD", "Ids": ["304e40bf-b686-458f-9720-7b081426da22", "39b608fd-0f4f-41b4-9646-0194c8917d8c"]}]}, {"BlockType": "LINE", "Confidence": 99.88932037353516, "Text": "Truckload Check-in Form", "Geometry": {"BoundingBox": {"Width": 0.34187227487564087, "Height": 0.023687824606895447, "Left": 0.35000550746917725, "Top": 0.2029956430196762}, "Polygon": [{"X": 0.35022681951522827, "Y": 0.2029956430196762}, {"X": 0.6918777823448181, "Y": 0.20591484010219574}, {"X": 0.6916556358337402, "Y": 0.22668346762657166}, {"X": 0.35000550746917725, "Y": 0.2237567901611328}]}, "Id": "2f6a0d7c-4481-4b04-a24e-4421a4f3d244", "Relationships": [{"Type": "CHILD", "Ids": ["c78febd0-953f-4580-ad6e-60bdc7062405", "8440f76c-5f22-4d37-830d-e4f60a36c3d9", "a49c637e-eb09-4344-a75d-52db4cdff163"]}]}, {"BlockType": "LINE", "Confidence": 99.61386108398438, "Text": "Clear-to-Pay Voucher", "Geometry": {"BoundingBox": {"Width": 0.28980231285095215, "Height": 0.026830585673451424, "Left": 0.3768068253993988, "Top": 0.23184195160865784}, "Polygon": [{"X": 0.37706637382507324, "Y": 0.23184195160865784}, {"X": 0.6666091084480286, "Y": 0.234324648976326}, {"X": 0.6663487553596497, "Y": 0.2586725354194641}, {"X": 0.3768068253993988, "Y": 0.25618240237236023}]}, "Id": "993f479d-60ea-494d-ac00-e5277859142e", "Relationships": [{"Type": "CHILD", "Ids": ["bd03ced1-b501-4517-9bfe-78052c922f9f", "2efb8612-9cd3-4237-945a-1221f57399df"]}]}, {"BlockType": "LINE", "Confidence": 98.67346954345703, "Text": "Load # 20511 AL", "Geometry": {"BoundingBox": {"Width": 0.23870624601840973, "Height": 0.023633789271116257, "Left": 0.11473789066076279, "Top": 0.26200157403945923}, "Polygon": [{"X": 0.11496734619140625, "Y": 0.26200157403945923}, {"X": 0.3534441292285919, "Y": 0.264054536819458}, {"X": 0.3532140851020813, "Y": 0.2856353521347046}, {"X": 0.11473789066076279, "Y": 0.28357696533203125}]}, "Id": "a02df1e5-6a39-45a8-b68c-d5e18467c2b5", "Relationships": [{"Type": "CHILD", "Ids": ["a37914ed-ec93-48a0-900d-a74d551a8e29", "6f5872d3-916f-4974-ac84-8fcbe6679c43", "a0d259e0-b818-459a-8992-6f2ac2790bfd", "1fe90b6e-c4f0-430e-a914-8bb1410291e1"]}]}, {"BlockType": "LINE", "Confidence": 99.45083618164062, "Text": "Broker PE", "Geometry": {"BoundingBox": {"Width": 0.1437872350215912, "Height": 0.030147209763526917, "Left": 0.5792487859725952, "Top": 0.26192593574523926}, "Polygon": [{"X": 0.5795576572418213, "Y": 0.26192593574523926}, {"X": 0.723035991191864, "Y": 0.26316046714782715}, {"X": 0.722726583480835, "Y": 0.292073130607605}, {"X": 0.5792487859725952, "Y": 0.2908342182636261}]}, "Id": "997cf5de-be5c-4925-b5ce-8ac0bed632b4", "Relationships": [{"Type": "CHILD", "Ids": ["da77f974-e21e-4645-8aa6-b298f45639c5", "8d71ade4-9822-4eb8-a9de-cc23beed8da7"]}]}, {"BlockType": "LINE", "Confidence": 95.20733642578125, "Text": "Date Processed 4/25/24", "Geometry": {"BoundingBox": {"Width": 0.25593841075897217, "Height": 0.030661003664135933, "Left": 0.11387882381677628, "Top": 0.296202152967453}, "Polygon": [{"X": 0.11418131738901138, "Y": 0.296202152967453}, {"X": 0.36981722712516785, "Y": 0.2984120845794678}, {"X": 0.3695138692855835, "Y": 0.326863169670105}, {"X": 0.11387882381677628, "Y": 0.3246455788612366}]}, "Id": "07e9a769-666e-416f-834a-1b0e3a2325f5", "Relationships": [{"Type": "CHILD", "Ids": ["02167a12-995d-43a4-a028-99eb90059c9a", "88e1e6d3-72d3-4f81-a64e-e93961a3c831", "dafb57d9-b8dc-468c-9a76-6cdc92cac75c"]}]}, {"BlockType": "LINE", "Confidence": 95.51824188232422, "Text": "Load Cost $ 3451.00", "Geometry": {"BoundingBox": {"Width": 0.23876221477985382, "Height": 0.03044033981859684, "Left": 0.5774176120758057, "Top": 0.3158484697341919}, "Polygon": [{"X": 0.5777207016944885, "Y": 0.3158484697341919}, {"X": 0.8161798119544983, "Y": 0.31791380047798157}, {"X": 0.8158758878707886, "Y": 0.346288800239563}, {"X": 0.5774176120758057, "Y": 0.34421631693840027}]}, "Id": "61754127-bf7b-4a4a-a73e-655e20973c9b", "Relationships": [{"Type": "CHILD", "Ids": ["024f8a77-7715-4840-8b45-d9db922d592f", "0e4b7424-bd43-4f6e-83df-0c10f923774f", "021a763d-b6e6-4c3c-8b57-c335da500ce7"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "<PERSON><PERSON> Returned", "Geometry": {"BoundingBox": {"Width": 0.12484949082136154, "Height": 0.013492931611835957, "Left": 0.11807575821876526, "Top": 0.34072861075401306}, "Polygon": [{"X": 0.11820771545171738, "Y": 0.34072861075401306}, {"X": 0.2429252564907074, "Y": 0.3418126106262207}, {"X": 0.24279311299324036, "Y": 0.35422155261039734}, {"X": 0.11807575821876526, "Y": 0.3531359136104584}]}, "Id": "33222b77-8771-44cf-8a85-8a65599835d8", "Relationships": [{"Type": "CHILD", "Ids": ["1b33f5c1-7737-446c-87f7-8159eda0970d", "65d0b5a3-8e25-4d3f-a074-076a62764ae1"]}]}, {"BlockType": "LINE", "Confidence": 99.35327911376953, "Text": "19", "Geometry": {"BoundingBox": {"Width": 0.02817312628030777, "Height": 0.020659903064370155, "Left": 0.2897212505340576, "Top": 0.3345332741737366}, "Polygon": [{"X": 0.2899387776851654, "Y": 0.3345332741737366}, {"X": 0.3178943693637848, "Y": 0.33477601408958435}, {"X": 0.3176767826080322, "Y": 0.355193167924881}, {"X": 0.2897212505340576, "Y": 0.35494980216026306}]}, "Id": "d1f5bd2e-97b5-4d3a-a8da-b23cd375d476", "Relationships": [{"Type": "CHILD", "Ids": ["fa005b5f-11ad-410b-8b93-7cbfd5ee8410"]}]}, {"BlockType": "LINE", "Confidence": 99.2115707397461, "Text": "Surcharge + $", "Geometry": {"BoundingBox": {"Width": 0.11982236802577972, "Height": 0.01617765985429287, "Left": 0.5715932250022888, "Top": 0.36490046977996826}, "Polygon": [{"X": 0.5717548727989197, "Y": 0.36490046977996826}, {"X": 0.691415548324585, "Y": 0.3659430742263794}, {"X": 0.691253662109375, "Y": 0.3810781240463257}, {"X": 0.5715932250022888, "Y": 0.3800336420536041}]}, "Id": "ef242c6d-7677-4578-a018-0c32ab01720c", "Relationships": [{"Type": "CHILD", "Ids": ["ba57cf55-f956-4505-9ab1-653233fa018c", "1aacbe89-c09c-4976-aa05-a4d09c4fe7da", "4ebd5540-a588-4238-8cc9-6739aa404706"]}]}, {"BlockType": "LINE", "Confidence": 79.0798568725586, "Text": "19 X$ $ 200 = $ 38.00", "Geometry": {"BoundingBox": {"Width": 0.323551207780838, "Height": 0.028789440169930458, "Left": 0.12800754606723785, "Top": 0.3974420428276062}, "Polygon": [{"X": 0.1282835751771927, "Y": 0.3974420428276062}, {"X": 0.4515587389469147, "Y": 0.40027114748954773}, {"X": 0.45128169655799866, "Y": 0.4262314736843109}, {"X": 0.12800754606723785, "Y": 0.4233935475349426}]}, "Id": "f90a17bc-2132-4a50-bb6d-6c52907aeb24", "Relationships": [{"Type": "CHILD", "Ids": ["78592cd1-6334-4a68-8ba3-4ad038be7507", "8bee1147-0845-41c3-afd7-14cb0a307af7", "007bf163-4d12-416c-b337-302def74a683", "244ad020-b695-4f6c-bf7f-f05c0435d031", "3ac0648b-719b-4f55-91d4-076e490ba008"]}]}, {"BlockType": "LINE", "Confidence": 94.96817779541016, "Text": "Deductions - $", "Geometry": {"BoundingBox": {"Width": 0.12457311153411865, "Height": 0.013984983786940575, "Left": 0.5713309049606323, "Top": 0.3993717432022095}, "Polygon": [{"X": 0.5714686512947083, "Y": 0.3993717432022095}, {"X": 0.695904016494751, "Y": 0.40046045184135437}, {"X": 0.695766031742096, "Y": 0.4133567214012146}, {"X": 0.5713309049606323, "Y": 0.4122663140296936}]}, "Id": "fe2c500d-5ace-44e2-a524-b2ecf3d0c871", "Relationships": [{"Type": "CHILD", "Ids": ["546b6945-efd5-4209-bc1b-7148921a319a", "48e7e67f-5d77-47aa-a525-93b4ac8c179c", "e7162c4b-1976-4d7d-92d6-d5d6dad106e9"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "RACKS", "Geometry": {"BoundingBox": {"Width": 0.06509441137313843, "Height": 0.012707075104117393, "Left": 0.11123371869325638, "Top": 0.4270114302635193}, "Polygon": [{"X": 0.11136277765035629, "Y": 0.4270114302635193}, {"X": 0.1763281226158142, "Y": 0.4275820255279541}, {"X": 0.17619897425174713, "Y": 0.4397185146808624}, {"X": 0.11123371869325638, "Y": 0.43914711475372314}]}, "Id": "7d5d9ded-2226-46a6-9df2-7aa51647272b", "Relationships": [{"Type": "CHILD", "Ids": ["14b175d8-fdb8-42cc-a164-6db0380dde90"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "AMOUNT", "Geometry": {"BoundingBox": {"Width": 0.08712081611156464, "Height": 0.01244564913213253, "Left": 0.22659026086330414, "Top": 0.42883726954460144}, "Polygon": [{"X": 0.226714625954628, "Y": 0.42883726954460144}, {"X": 0.3137110769748688, "Y": 0.42960140109062195}, {"X": 0.31358659267425537, "Y": 0.4412829279899597}, {"X": 0.22659026086330414, "Y": 0.44051772356033325}]}, "Id": "508f7110-4e3d-4d7f-af03-13036fbfae98", "Relationships": [{"Type": "CHILD", "Ids": ["a0d94637-1a81-4b7d-8197-9492cb960e6e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06548167020082474, "Height": 0.012474516406655312, "Left": 0.3615785539150238, "Top": 0.43040937185287476}, "Polygon": [{"X": 0.3617054224014282, "Y": 0.43040937185287476}, {"X": 0.42706024646759033, "Y": 0.43098342418670654}, {"X": 0.42693325877189636, "Y": 0.4428838789463043}, {"X": 0.3615785539150238, "Y": 0.4423089921474457}]}, "Id": "c8b1323f-d2f4-4f6f-b4a2-8180a76473bb", "Relationships": [{"Type": "CHILD", "Ids": ["be28bd9d-5521-4aef-b9f8-1345763226a2"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.047641705721616745, "Height": 0.012129289098083973, "Left": 0.5742791295051575, "Top": 0.4508746862411499}, "Polygon": [{"X": 0.574404239654541, "Y": 0.4508746862411499}, {"X": 0.6219208240509033, "Y": 0.451293021440506}, {"X": 0.621795654296875, "Y": 0.4630039930343628}, {"X": 0.5742791295051575, "Y": 0.46258509159088135}]}, "Id": "b4474327-89d4-45f0-99fe-5c22dff16f53", "Relationships": [{"Type": "CHILD", "Ids": ["3fb532ce-4355-4f12-a1ba-5d8d58e55868"]}]}, {"BlockType": "LINE", "Confidence": 69.45232391357422, "Text": "+ $ 38.00", "Geometry": {"BoundingBox": {"Width": 0.1341155618429184, "Height": 0.027367135509848595, "Left": 0.6759076714515686, "Top": 0.4384637773036957}, "Polygon": [{"X": 0.6761877536773682, "Y": 0.4384637773036957}, {"X": 0.8100232481956482, "Y": 0.4396401047706604}, {"X": 0.8097427487373352, "Y": 0.46583089232444763}, {"X": 0.6759076714515686, "Y": 0.46465086936950684}]}, "Id": "e2302f96-8099-4631-b826-2f338fdd751d", "Relationships": [{"Type": "CHILD", "Ids": ["d95d207a-17d5-4558-8715-d9bcca7382f6", "03cee05e-6954-449f-bc10-f481dd42f05e"]}]}, {"BlockType": "LINE", "Confidence": 99.95087432861328, "Text": "(Rack checks will now be sent to the", "Geometry": {"BoundingBox": {"Width": 0.2897137701511383, "Height": 0.016613207757472992, "Left": 0.5734478235244751, "Top": 0.46765801310539246}, "Polygon": [{"X": 0.5735979676246643, "Y": 0.46765801310539246}, {"X": 0.863161563873291, "Y": 0.47021234035491943}, {"X": 0.8630109429359436, "Y": 0.48427122831344604}, {"X": 0.5734478235244751, "Y": 0.4817126393318176}]}, "Id": "c4bfa63c-baa2-4d18-9b23-d30f6f901161", "Relationships": [{"Type": "CHILD", "Ids": ["4f635101-55d4-434b-b687-c6c4c228ae72", "a17b51c9-7704-43b7-b2c3-31f893efb355", "9aedad16-d70e-4c1a-a05e-7c2a0aafc3ac", "742cfc0c-37ee-478e-9ddf-5b87fe2b211d", "cd2dae5f-4966-4583-8126-c51fc595e654", "cf60fa81-9dcf-47d7-899a-13b66ce22480", "0bb0a774-e18b-4ebf-a5bf-e90fdcd381f5", "883a0105-1405-407c-9b1a-a38605f9d8d0"]}]}, {"BlockType": "LINE", "Confidence": 99.6827163696289, "Text": "broker.)", "Geometry": {"BoundingBox": {"Width": 0.06281241774559021, "Height": 0.014736440032720566, "Left": 0.5837801098823547, "Top": 0.48478901386260986}, "Polygon": [{"X": 0.5839316844940186, "Y": 0.48478901386260986}, {"X": 0.6465925574302673, "Y": 0.4853428900241852}, {"X": 0.6464409232139587, "Y": 0.4995254576206207}, {"X": 0.5837801098823547, "Y": 0.4989706575870514}]}, "Id": "673654fb-da7d-447f-8682-0c1889915825", "Relationships": [{"Type": "CHILD", "Ids": ["0dc82343-5f77-4890-ba43-482006828f31"]}]}, {"BlockType": "LINE", "Confidence": 89.09278869628906, "Text": "Net Cost $ 3489.00", "Geometry": {"BoundingBox": {"Width": 0.22106042504310608, "Height": 0.03388524428009987, "Left": 0.5735058188438416, "Top": 0.5006726384162903}, "Polygon": [{"X": 0.5738469362258911, "Y": 0.5006726384162903}, {"X": 0.79456627368927, "Y": 0.5026273727416992}, {"X": 0.7942243218421936, "Y": 0.5345578789710999}, {"X": 0.5735058188438416, "Y": 0.5325958132743835}]}, "Id": "f3dcae61-45b3-4f86-be27-162b7d2baebf", "Relationships": [{"Type": "CHILD", "Ids": ["b6630327-2e6c-4c48-bde2-b73059b3b8f9", "9e446520-9ca8-41e5-9a59-a62d96cb252e", "5479b3b4-44c4-4cdc-a8c6-9aa171be8bb8"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Billable Shortages:", "Geometry": {"BoundingBox": {"Width": 0.24950189888477325, "Height": 0.025929559022188187, "Left": 0.10898515582084656, "Top": 0.5501824021339417}, "Polygon": [{"X": 0.10923722386360168, "Y": 0.5501824021339417}, {"X": 0.358487069606781, "Y": 0.5524038672447205}, {"X": 0.3582342863082886, "Y": 0.5761119723320007}, {"X": 0.10898515582084656, "Y": 0.5738843083381653}]}, "Id": "915e239e-e203-491b-a202-23fc44610a1f", "Relationships": [{"Type": "CHILD", "Ids": ["596a8a3a-25b7-43ce-9058-39e22a5eb251", "0d38ee06-2a2b-4ba9-a9ac-adc09e59b815"]}]}, {"BlockType": "LINE", "Confidence": 88.20982360839844, "Text": "(equipment missing with insufficient documentation", "Geometry": {"BoundingBox": {"Width": 0.28167974948883057, "Height": 0.013249682262539864, "Left": 0.1076815128326416, "Top": 0.5747796297073364}, "Polygon": [{"X": 0.10779561847448349, "Y": 0.5747796297073364}, {"X": 0.38936126232147217, "Y": 0.5772963762283325}, {"X": 0.38924679160118103, "Y": 0.5880293250083923}, {"X": 0.1076815128326416, "Y": 0.5855093598365784}]}, "Id": "8f909dc7-ecc4-4343-8649-bf3bd3402747", "Relationships": [{"Type": "CHILD", "Ids": ["18be897c-4f65-469a-aefd-bd028e485c7a", "979fa9be-7144-47a7-a66d-880d1a00528a", "22c97980-ac5f-471a-8b27-cdd661a5e181", "866c6a01-109c-4417-8e4c-f4e2d2cbcad8", "3a30ee94-2217-4252-8215-fe2e3afbcba2"]}]}, {"BlockType": "LINE", "Confidence": 94.36845397949219, "Text": "Racks: ( ) X $250.00 = $", "Geometry": {"BoundingBox": {"Width": 0.20068196952342987, "Height": 0.015787586569786072, "Left": 0.10838234424591064, "Top": 0.5988958477973938}, "Polygon": [{"X": 0.10853108763694763, "Y": 0.5988958477973938}, {"X": 0.3090642988681793, "Y": 0.60069340467453}, {"X": 0.30891522765159607, "Y": 0.6146834492683411}, {"X": 0.10838234424591064, "Y": 0.6128829121589661}]}, "Id": "fa8197f8-71c5-4757-8f9c-7fc4ae5b3396", "Relationships": [{"Type": "CHILD", "Ids": ["46adf26a-3748-4b0f-9f78-76e90b126285", "34384e75-5a3d-456a-a63d-b20aeea2f9c5", "6794da6c-304a-4aeb-b620-1edb52197e7d", "70d57dd8-be38-4a69-b047-e723a2f4cde9", "b6ad6478-a6ec-402a-828b-12b435dc4000", "b1d4af7f-87c1-4e27-88b9-b77d9a2a1d1a", "16806a07-56b6-4dfa-a739-0d7d5aa5fcaf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Deductions", "Geometry": {"BoundingBox": {"Width": 0.13739222288131714, "Height": 0.013103784061968327, "Left": 0.5899747610092163, "Top": 0.6049606800079346}, "Polygon": [{"X": 0.5901015996932983, "Y": 0.6049606800079346}, {"X": 0.7273669838905334, "Y": 0.6061913371086121}, {"X": 0.7272399067878723, "Y": 0.6180644631385803}, {"X": 0.5899747610092163, "Y": 0.6168320775032043}]}, "Id": "7129f795-3a80-4337-bb07-e84785fbc326", "Relationships": [{"Type": "CHILD", "Ids": ["db499037-0e96-4c84-893b-89015a4d6649", "fe6364b4-c127-49d1-af0f-bb2308bc25d8"]}]}, {"BlockType": "LINE", "Confidence": 99.87104797363281, "Text": "For Load:", "Geometry": {"BoundingBox": {"Width": 0.07641547918319702, "Height": 0.012637611478567123, "Left": 0.5917933583259583, "Top": 0.6223496198654175}, "Polygon": [{"X": 0.5919210910797119, "Y": 0.6223496198654175}, {"X": 0.6682088375091553, "Y": 0.6230349540710449}, {"X": 0.6680809855461121, "Y": 0.6349872350692749}, {"X": 0.5917933583259583, "Y": 0.6343008875846863}]}, "Id": "0a59f316-1ef7-4687-8c84-5e74526f2d72", "Relationships": [{"Type": "CHILD", "Ids": ["6dbc8319-7d1f-4d52-846a-3f22ed312ced", "41599ed4-8adb-4e4a-ba33-520048cd0abc"]}]}, {"BlockType": "LINE", "Confidence": 94.49744415283203, "Text": "Load Locks: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23602625727653503, "Height": 0.01642322540283203, "Left": 0.10802678018808365, "Top": 0.6335623264312744}, "Polygon": [{"X": 0.10817882418632507, "Y": 0.6335623264312744}, {"X": 0.3440530300140381, "Y": 0.6356853246688843}, {"X": 0.343900591135025, "Y": 0.6499855518341064}, {"X": 0.10802678018808365, "Y": 0.6478590369224548}]}, "Id": "011f90f1-7cbc-46e1-82b9-e636733bb4ae", "Relationships": [{"Type": "CHILD", "Ids": ["929b6c65-ba3c-4553-ae71-d395452b3bc6", "8962bf61-0ef0-4dad-958e-afceabb313c7", "668dd645-e0f8-40ac-9c73-89685f3b29e4", "17fa6f4d-1713-4337-adcc-c819b18b7e2b", "6e6fb528-5f6f-4a17-8884-fc6d83376aff", "1d470583-085c-4728-a664-aabd140dd5a3", "634a0fd1-dbc6-49c9-bea9-449a6f135cb6", "7b12e10c-d92f-4693-bb0d-8dc92e6cc123"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "$", "Geometry": {"BoundingBox": {"Width": 0.009546203538775444, "Height": 0.012938784435391426, "Left": 0.590423583984375, "Top": 0.6565326452255249}, "Polygon": [{"X": 0.5905609726905823, "Y": 0.6565326452255249}, {"X": 0.5999698042869568, "Y": 0.6566175222396851}, {"X": 0.5998324155807495, "Y": 0.6694714426994324}, {"X": 0.590423583984375, "Y": 0.6693864464759827}]}, "Id": "57bfa6fc-01db-48cf-9699-c89737ebf264", "Relationships": [{"Type": "CHILD", "Ids": ["f0b0c04e-379f-4329-8825-dfb0998fd4da"]}]}, {"BlockType": "LINE", "Confidence": 94.30215454101562, "Text": "Pallet Jack: ( ) X $500.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23755332827568054, "Height": 0.016640767455101013, "Left": 0.10682060569524765, "Top": 0.6681486368179321}, "Polygon": [{"X": 0.10697472095489502, "Y": 0.6681486368179321}, {"X": 0.3443739414215088, "Y": 0.6702939867973328}, {"X": 0.3442194163799286, "Y": 0.6847894191741943}, {"X": 0.10682060569524765, "Y": 0.6826404333114624}]}, "Id": "0169022f-e3c1-441d-9b13-1b11e2a37b52", "Relationships": [{"Type": "CHILD", "Ids": ["4b115c7f-bc67-410d-b1e1-f3fdba01a1ec", "47adf24e-7a54-4c87-95a9-c9690e94a642", "3062fd7b-d771-451f-bfb3-618336dd6dfd", "5a87abe5-f993-4885-8842-0fa1ef973001", "31643f8e-5ff0-49c6-9dcd-9123276601eb", "79bff771-47f2-4337-b523-dc3ff9b72a1e", "b5c97433-e060-425e-b68d-a3a2be811193"]}]}, {"BlockType": "LINE", "Confidence": 95.63269805908203, "Text": "Load Straps: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2371797263622284, "Height": 0.016651956364512444, "Left": 0.10643713921308517, "Top": 0.7024759650230408}, "Polygon": [{"X": 0.10659132152795792, "Y": 0.7024759650230408}, {"X": 0.34361687302589417, "Y": 0.7046265006065369}, {"X": 0.3434622883796692, "Y": 0.7191279530525208}, {"X": 0.10643713921308517, "Y": 0.7169737815856934}]}, "Id": "f3176676-ee7d-4fd6-b806-bae192eecac2", "Relationships": [{"Type": "CHILD", "Ids": ["980a1935-a506-4fcc-88a0-b25f9ea65514", "d60f9132-1fb1-4033-b2cb-2b71a2535825", "1c4d2523-9552-4439-82f2-0c31730e01e9", "ca6ac020-ad01-4bc5-a886-b3a7d8860803", "ad81529f-7c58-40a6-9a3d-3e06c3fe1d1d", "68c2e5dd-9152-4cc0-9681-946abbe389c3", "d360d9c9-07f1-45ef-a85c-a0cc1413f34e", "f2d089b1-1a0c-4461-acc7-5a41dca41ff8"]}]}, {"BlockType": "LINE", "Confidence": 99.93522644042969, "Text": "Paperwork & other Fines $", "Geometry": {"BoundingBox": {"Width": 0.21355943381786346, "Height": 0.01657821424305439, "Left": 0.10517112165689468, "Top": 0.7371671795845032}, "Polygon": [{"X": 0.10532671213150024, "Y": 0.7371671795845032}, {"X": 0.31873056292533875, "Y": 0.7391112446784973}, {"X": 0.31857460737228394, "Y": 0.7537453770637512}, {"X": 0.10517112165689468, "Y": 0.7517980933189392}]}, "Id": "29835208-cdd3-4219-a30e-f59eb1f0214b", "Relationships": [{"Type": "CHILD", "Ids": ["0521a5a8-59ea-43f6-a56f-6204ee480c7e", "b06d20e3-f5e3-4799-937c-a70add97de8c", "5270a6d6-7fd6-4599-a6ee-dcca14b122b1", "99671ffb-8423-408c-84eb-b03c7214b8a3", "42db5a52-eab7-4f57-8e28-523cb0e18136"]}]}, {"BlockType": "LINE", "Confidence": 99.27628326416016, "Text": "(see attached documentation)", "Geometry": {"BoundingBox": {"Width": 0.23106598854064941, "Height": 0.016540750861167908, "Left": 0.10499119758605957, "Top": 0.754318118095398}, "Polygon": [{"X": 0.10514464974403381, "Y": 0.754318118095398}, {"X": 0.336057186126709, "Y": 0.7564257979393005}, {"X": 0.3359033465385437, "Y": 0.7708588242530823}, {"X": 0.10499119758605957, "Y": 0.7687476277351379}]}, "Id": "a3b80d73-4cc4-4816-9326-747c1cd8250e", "Relationships": [{"Type": "CHILD", "Ids": ["50570412-ffea-4578-a228-628571881030", "e7b4ea4a-0f2c-473a-a811-0818b6fe8f11", "3f5f97c5-b313-4c61-8fc6-8acc325d036c"]}]}, {"BlockType": "LINE", "Confidence": 99.63638305664062, "Text": "Manager A<PERSON><PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.1532665193080902, "Height": 0.016582222655415535, "Left": 0.5337262749671936, "Top": 0.7601072192192078}, "Polygon": [{"X": 0.5338884592056274, "Y": 0.7601072192192078}, {"X": 0.6869928240776062, "Y": 0.7615050077438354}, {"X": 0.6868304014205933, "Y": 0.7766894102096558}, {"X": 0.5337262749671936, "Y": 0.7752891778945923}]}, "Id": "c2111667-5072-47a6-bead-92fe91040a59", "Relationships": [{"Type": "CHILD", "Ids": ["a16380a9-7008-40c2-89fa-fa326b73102e", "ce8bae85-32ff-407c-ab8a-8c42e43be9be"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Notes:", "Geometry": {"BoundingBox": {"Width": 0.050935082137584686, "Height": 0.012670502066612244, "Left": 0.10389607399702072, "Top": 0.7888162732124329}, "Polygon": [{"X": 0.10402585566043854, "Y": 0.7888162732124329}, {"X": 0.1548311561346054, "Y": 0.7892818450927734}, {"X": 0.15470129251480103, "Y": 0.8014867305755615}, {"X": 0.10389607399702072, "Y": 0.8010205030441284}]}, "Id": "b4034c21-79fc-4aba-80bf-d7d3ac928b7b", "Relationships": [{"Type": "CHILD", "Ids": ["6979b087-2737-456b-873f-5e8bb9a0ab16"]}]}, {"BlockType": "LINE", "Confidence": 64.25243377685547, "Text": "my", "Geometry": {"BoundingBox": {"Width": 0.086174376308918, "Height": 0.08377391844987869, "Left": 0.7600809931755066, "Top": 0.7368581295013428}, "Polygon": [{"X": 0.7609694004058838, "Y": 0.7368581295013428}, {"X": 0.846255362033844, "Y": 0.737634539604187}, {"X": 0.8453660607337952, "Y": 0.8206321001052856}, {"X": 0.7600809931755066, "Y": 0.8198482394218445}]}, "Id": "9a012a42-48fa-413d-849e-2cb2a7e445e8", "Relationships": [{"Type": "CHILD", "Ids": ["a94bf50d-3ea3-4c78-bad6-8226bae3ea06"]}]}, {"BlockType": "LINE", "Confidence": 97.49446105957031, "Text": "Any fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468", "Geometry": {"BoundingBox": {"Width": 0.7629446983337402, "Height": 0.021546872332692146, "Left": 0.10318593680858612, "Top": 0.839921772480011}, "Polygon": [{"X": 0.10334017872810364, "Y": 0.839921772480011}, {"X": 0.8661306500434875, "Y": 0.8469531536102295}, {"X": 0.8659750819206238, "Y": 0.8614686131477356}, {"X": 0.10318593680858612, "Y": 0.8544256091117859}]}, "Id": "8619eaa3-2322-490e-a647-340718118262", "Relationships": [{"Type": "CHILD", "Ids": ["8b8b2bc9-fc5d-44f1-85d6-0cb514e1d8e1", "5639eb82-9bf8-4128-90c0-c67ceac50f6b", "972fb920-e274-4a57-8426-aeefff755f6c", "574894e0-1553-4232-a811-600c018d8a84", "8257ced5-695c-40f6-8aa0-22fc3e9af6a6", "0402d62a-be8c-489e-8af6-36f55b4d77da", "d8eed6d7-23aa-430c-b1ea-c6008ad67b21", "7bbacc84-cf33-4a76-bd87-9c9effb5780e", "36ea0807-2759-4407-b2fd-329988546482", "c098de06-598d-44a9-a820-f24a9e1f8120", "771efe35-d620-4d86-a819-9d2d3ee5ad3e", "e178b6e0-e590-4732-8698-40e210288b8d", "48d9e67f-5a80-432d-bdcc-246771def8bd", "e19dc30a-ea37-4f30-ae67-665773e1c194", "8ba91cc0-3f80-4e9c-b1b1-70b3215108a5", "8ce7f24a-de8e-40a5-9655-e9be0d7716f0", "363e577e-ca66-470e-aa84-7e22140effc9", "5703b844-3740-40b2-ab56-e12e0cac7fda", "67398a25-3603-4f2e-9515-022646b623e5", "4b33edf3-bd89-488c-a1d8-43ca8086a99a"]}]}, {"BlockType": "LINE", "Confidence": 74.57380676269531, "Text": "71", "Geometry": {"BoundingBox": {"Width": 0.048062749207019806, "Height": 0.03361378610134125, "Left": 0.09863157570362091, "Top": 0.8571683764457703}, "Polygon": [{"X": 0.09898432344198227, "Y": 0.8571683764457703}, {"X": 0.14669431746006012, "Y": 0.8576090335845947}, {"X": 0.14634139835834503, "Y": 0.8907821178436279}, {"X": 0.09863157570362091, "Y": 0.8903397917747498}]}, "Id": "fef3187a-0c62-48bb-925d-e70c79521073", "Relationships": [{"Type": "CHILD", "Ids": ["d8b33573-9493-4da0-a817-a612b1882a54"]}]}, {"BlockType": "WORD", "Confidence": 15.540496826171875, "Text": "20011", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03969245404005051, "Height": 0.012352356687188148, "Left": 0.1096276342868805, "Top": 0.05979067087173462}, "Polygon": [{"X": 0.10975544899702072, "Y": 0.05979067087173462}, {"X": 0.1493200808763504, "Y": 0.060122840106487274}, {"X": 0.149192214012146, "Y": 0.07214302569627762}, {"X": 0.1096276342868805, "Y": 0.07181035727262497}]}, "Id": "a45c2755-9008-4dd5-adb1-9d359f70d090"}, {"BlockType": "WORD", "Confidence": 99.47206115722656, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13596265017986298, "Height": 0.04684826731681824, "Left": 0.4671572744846344, "Top": 0.05006411299109459}, "Polygon": [{"X": 0.4676450788974762, "Y": 0.05006411299109459}, {"X": 0.6031199097633362, "Y": 0.05119969695806503}, {"X": 0.6026313900947571, "Y": 0.09691238403320312}, {"X": 0.4671572744846344, "Y": 0.09577028453350067}]}, "Id": "5a45fef6-9ec3-432d-9625-f5a65e6420ce"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "8785229", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.24202166497707367, "Height": 0.04814106598496437, "Left": 0.6351876258850098, "Top": 0.045112647116184235}, "Polygon": [{"X": 0.6356804966926575, "Y": 0.045112647116184235}, {"X": 0.8772092461585999, "Y": 0.04713558033108711}, {"X": 0.8767150044441223, "Y": 0.0932537168264389}, {"X": 0.6351876258850098, "Y": 0.09121906012296677}]}, "Id": "171bd6a4-9416-46f5-8496-f5c109ca5611"}, {"BlockType": "WORD", "Confidence": 16.427175521850586, "Text": "FMBVB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22239433228969574, "Height": 0.0352904386818409, "Left": 0.4028277099132538, "Top": 0.12224309891462326}, "Polygon": [{"X": 0.4031839966773987, "Y": 0.12224309891462326}, {"X": 0.6252220273017883, "Y": 0.1241212859749794}, {"X": 0.6248648762702942, "Y": 0.15753354132175446}, {"X": 0.4028277099132538, "Y": 0.15564754605293274}]}, "Id": "10a1a9ee-baf2-4933-a034-eae56dc493a7"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "SAWYER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18378522992134094, "Height": 0.024833621457219124, "Left": 0.2987881898880005, "Top": 0.16971662640571594}, "Polygon": [{"X": 0.2990361154079437, "Y": 0.16971662640571594}, {"X": 0.48257341980934143, "Y": 0.17127850651741028}, {"X": 0.48232501745224, "Y": 0.19455024600028992}, {"X": 0.2987881898880005, "Y": 0.19298389554023743}]}, "Id": "304e40bf-b686-458f-9720-7b081426da22"}, {"BlockType": "WORD", "Confidence": 93.18717956542969, "Text": "NURSERY.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2177620828151703, "Height": 0.024967821314930916, "Left": 0.509906530380249, "Top": 0.17293354868888855}, "Polygon": [{"X": 0.5101532936096191, "Y": 0.17293354868888855}, {"X": 0.7276685833930969, "Y": 0.17478488385677338}, {"X": 0.727421224117279, "Y": 0.19790136814117432}, {"X": 0.509906530380249, "Y": 0.19604474306106567}]}, "Id": "39b608fd-0f4f-41b4-9646-0194c8917d8c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truckload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1350608915090561, "Height": 0.02047887071967125, "Left": 0.3500208258628845, "Top": 0.2029956430196762}, "Polygon": [{"X": 0.35022681951522827, "Y": 0.2029956430196762}, {"X": 0.4850817024707794, "Y": 0.20414789021015167}, {"X": 0.4848754107952118, "Y": 0.22347450256347656}, {"X": 0.3500208258628845, "Y": 0.22231951355934143}]}, "Id": "c78febd0-953f-4580-ad6e-60bdc7062405"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "Check-in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11851649731397629, "Height": 0.02050621621310711, "Left": 0.49450239539146423, "Top": 0.20516334474086761}, "Polygon": [{"X": 0.4947105050086975, "Y": 0.20516334474086761}, {"X": 0.6130189299583435, "Y": 0.2061743438243866}, {"X": 0.6128105521202087, "Y": 0.22566956281661987}, {"X": 0.49450239539146423, "Y": 0.2246561497449875}]}, "Id": "8440f76c-5f22-4d37-830d-e4f60a36c3d9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Form", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06915312260389328, "Height": 0.019079070538282394, "Left": 0.6227002739906311, "Top": 0.20760439336299896}, "Polygon": [{"X": 0.6228979229927063, "Y": 0.20760439336299896}, {"X": 0.691853404045105, "Y": 0.20819373428821564}, {"X": 0.6916556358337402, "Y": 0.22668346762657166}, {"X": 0.6227002739906311, "Y": 0.22609277069568634}]}, "Id": "a49c637e-eb09-4344-a75d-52db4cdff163"}, {"BlockType": "WORD", "Confidence": 99.267578125, "Text": "Clear-to-Pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1674390435218811, "Height": 0.025778237730264664, "Left": 0.3768068253993988, "Top": 0.23184195160865784}, "Polygon": [{"X": 0.37706637382507324, "Y": 0.23184195160865784}, {"X": 0.5442458987236023, "Y": 0.23327544331550598}, {"X": 0.5439858436584473, "Y": 0.2576201856136322}, {"X": 0.3768068253993988, "Y": 0.25618240237236023}]}, "Id": "bd03ced1-b501-4517-9bfe-78052c922f9f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Voucher", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11359170079231262, "Height": 0.019971758127212524, "Left": 0.5530005097389221, "Top": 0.23493164777755737}, "Polygon": [{"X": 0.5532034635543823, "Y": 0.23493164777755737}, {"X": 0.6665922403335571, "Y": 0.23590409755706787}, {"X": 0.6663890480995178, "Y": 0.2549034059047699}, {"X": 0.5530005097389221, "Y": 0.25392869114875793}]}, "Id": "2efb8612-9cd3-4237-945a-1221f57399df"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039486102759838104, "Height": 0.013153121806681156, "Left": 0.11473926901817322, "Top": 0.270633727312088}, "Polygon": [{"X": 0.11487554013729095, "Y": 0.270633727312088}, {"X": 0.15422537922859192, "Y": 0.2709728479385376}, {"X": 0.1540890485048294, "Y": 0.2837868332862854}, {"X": 0.11473926901817322, "Y": 0.2834472060203552}]}, "Id": "a37914ed-ec93-48a0-900d-a74d551a8e29"}, {"BlockType": "WORD", "Confidence": 97.529296875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010531223379075527, "Height": 0.012622297741472721, "Left": 0.1587720364332199, "Top": 0.27142447233200073}, "Polygon": [{"X": 0.15890538692474365, "Y": 0.27142447233200073}, {"X": 0.1693032681941986, "Y": 0.27151408791542053}, {"X": 0.16916990280151367, "Y": 0.2840467691421509}, {"X": 0.1587720364332199, "Y": 0.28395703434944153}]}, "Id": "6f5872d3-916f-4974-ac84-8fcbe6679c43"}, {"BlockType": "WORD", "Confidence": 98.1728286743164, "Text": "20511", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08101072162389755, "Height": 0.020286303013563156, "Left": 0.19784510135650635, "Top": 0.2627168297767639}, "Polygon": [{"X": 0.19805359840393066, "Y": 0.2627168297767639}, {"X": 0.2788558304309845, "Y": 0.2634124457836151}, {"X": 0.27864712476730347, "Y": 0.2830031216144562}, {"X": 0.19784510135650635, "Y": 0.28230586647987366}]}, "Id": "a0d259e0-b818-459a-8992-6f2ac2790bfd"}, {"BlockType": "WORD", "Confidence": 99.33354949951172, "Text": "AL", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0433846041560173, "Height": 0.017497148364782333, "Left": 0.3100314140319824, "Top": 0.26632174849510193}, "Polygon": [{"X": 0.3102138638496399, "Y": 0.26632174849510193}, {"X": 0.35341599583625793, "Y": 0.26669377088546753}, {"X": 0.3532334268093109, "Y": 0.28381890058517456}, {"X": 0.3100314140319824, "Y": 0.2834460735321045}]}, "Id": "1fe90b6e-c4f0-430e-a914-8bb1410291e1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Broker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05532407388091087, "Height": 0.012755017727613449, "Left": 0.5792487859725952, "Top": 0.2785557806491852}, "Polygon": [{"X": 0.5793799757957458, "Y": 0.2785557806491852}, {"X": 0.6345728635787964, "Y": 0.2790316641330719}, {"X": 0.6344415545463562, "Y": 0.2913108170032501}, {"X": 0.5792487859725952, "Y": 0.2908342182636261}]}, "Id": "da77f974-e21e-4645-8aa6-b298f45639c5"}, {"BlockType": "WORD", "Confidence": 98.90166473388672, "Text": "PE", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04065915197134018, "Height": 0.024944540113210678, "Left": 0.6823768615722656, "Top": 0.26281288266181946}, "Polygon": [{"X": 0.6826398968696594, "Y": 0.26281288266181946}, {"X": 0.723035991191864, "Y": 0.26316046714782715}, {"X": 0.7227727770805359, "Y": 0.28775742650032043}, {"X": 0.6823768615722656, "Y": 0.2874087989330292}]}, "Id": "8d71ade4-9822-4eb8-a9de-cc23beed8da7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03730719909071922, "Height": 0.012260730378329754, "Left": 0.11395111680030823, "Top": 0.30590900778770447}, "Polygon": [{"X": 0.1140780821442604, "Y": 0.30590900778770447}, {"X": 0.15125831961631775, "Y": 0.3062308132648468}, {"X": 0.1511313021183014, "Y": 0.3181697428226471}, {"X": 0.11395111680030823, "Y": 0.31784746050834656}]}, "Id": "02167a12-995d-43a4-a028-99eb90059c9a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Processed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07845620810985565, "Height": 0.012872464954853058, "Left": 0.15567299723625183, "Top": 0.3064587414264679}, "Polygon": [{"X": 0.15580272674560547, "Y": 0.3064587414264679}, {"X": 0.23412920534610748, "Y": 0.3071366846561432}, {"X": 0.2339993715286255, "Y": 0.31933122873306274}, {"X": 0.15567299723625183, "Y": 0.31865227222442627}]}, "Id": "88e1e6d3-72d3-4f81-a64e-e93961a3c831"}, {"BlockType": "WORD", "Confidence": 85.66107177734375, "Text": "4/25/24", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11227492243051529, "Height": 0.029419058933854103, "Left": 0.25754231214523315, "Top": 0.29744410514831543}, "Polygon": [{"X": 0.25784531235694885, "Y": 0.29744410514831543}, {"X": 0.36981722712516785, "Y": 0.2984120845794678}, {"X": 0.3695138692855835, "Y": 0.326863169670105}, {"X": 0.25754231214523315, "Y": 0.3258918225765228}]}, "Id": "dafb57d9-b8dc-468c-9a76-6cdc92cac75c"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039731137454509735, "Height": 0.01198496762663126, "Left": 0.5774403810501099, "Top": 0.33044445514678955}, "Polygon": [{"X": 0.5775647163391113, "Y": 0.33044445514678955}, {"X": 0.6171715259552002, "Y": 0.33078810572624207}, {"X": 0.6170470714569092, "Y": 0.34242939949035645}, {"X": 0.5774403810501099, "Y": 0.3420852720737457}]}, "Id": "024f8a77-7715-4840-8b45-d9db922d592f"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035962797701358795, "Height": 0.01227970514446497, "Left": 0.62276291847229, "Top": 0.3311964273452759}, "Polygon": [{"X": 0.622890830039978, "Y": 0.3311964273452759}, {"X": 0.6587256789207458, "Y": 0.3315073549747467}, {"X": 0.6585977077484131, "Y": 0.34347614645957947}, {"X": 0.62276291847229, "Y": 0.34316474199295044}]}, "Id": "0e4b7424-bd43-4f6e-83df-0c10f923774f"}, {"BlockType": "WORD", "Confidence": 86.67191314697266, "Text": "$ 3451.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12897059321403503, "Height": 0.02948940359055996, "Left": 0.6872091889381409, "Top": 0.3167994022369385}, "Polygon": [{"X": 0.6875126957893372, "Y": 0.3167994022369385}, {"X": 0.8161798119544983, "Y": 0.31791380047798157}, {"X": 0.8158758878707886, "Y": 0.346288800239563}, {"X": 0.6872091889381409, "Y": 0.3451705276966095}]}, "Id": "021a763d-b6e6-4c3c-8b57-c335da500ce7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047821979969739914, "Height": 0.012427236884832382, "Left": 0.11807996034622192, "Top": 0.34072861075401306}, "Polygon": [{"X": 0.11820771545171738, "Y": 0.34072861075401306}, {"X": 0.16590194404125214, "Y": 0.3411431610584259}, {"X": 0.1657741218805313, "Y": 0.35315585136413574}, {"X": 0.11807996034622192, "Y": 0.35274070501327515}]}, "Id": "1b33f5c1-7737-446c-87f7-8159eda0970d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Returned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0719442218542099, "Height": 0.012523293495178223, "Left": 0.17097559571266174, "Top": 0.3416982591152191}, "Polygon": [{"X": 0.17110221087932587, "Y": 0.3416982591152191}, {"X": 0.24291981756687164, "Y": 0.3423224985599518}, {"X": 0.24279311299324036, "Y": 0.35422155261039734}, {"X": 0.17097559571266174, "Y": 0.35359638929367065}]}, "Id": "65d0b5a3-8e25-4d3f-a074-076a62764ae1"}, {"BlockType": "WORD", "Confidence": 99.35327911376953, "Text": "19", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.02817312628030777, "Height": 0.020659903064370155, "Left": 0.2897212505340576, "Top": 0.3345332741737366}, "Polygon": [{"X": 0.2899387776851654, "Y": 0.3345332741737366}, {"X": 0.3178943693637848, "Y": 0.33477601408958435}, {"X": 0.3176767826080322, "Y": 0.355193167924881}, {"X": 0.2897212505340576, "Y": 0.35494980216026306}]}, "Id": "fa005b5f-11ad-410b-8b93-7cbfd5ee8410"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Surcharge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08037116378545761, "Height": 0.01583329401910305, "Left": 0.5715932250022888, "Top": 0.36490046977996826}, "Polygon": [{"X": 0.5717548727989197, "Y": 0.36490046977996826}, {"X": 0.6519643664360046, "Y": 0.3655993342399597}, {"X": 0.6518025398254395, "Y": 0.38073375821113586}, {"X": 0.5715932250022888, "Y": 0.3800336420536041}]}, "Id": "ba57cf55-f956-4505-9ab1-653233fa018c"}, {"BlockType": "WORD", "Confidence": 98.21647644042969, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011222307570278645, "Height": 0.008718364872038364, "Left": 0.6607760787010193, "Top": 0.36799341440200806}, "Polygon": [{"X": 0.660868227481842, "Y": 0.36799341440200806}, {"X": 0.6719983816146851, "Y": 0.36809042096138}, {"X": 0.6719061732292175, "Y": 0.37671175599098206}, {"X": 0.6607760787010193, "Y": 0.37661466002464294}]}, "Id": "1aacbe89-c09c-4976-aa05-a4d09c4fe7da"}, {"BlockType": "WORD", "Confidence": 99.4971694946289, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009908276610076427, "Height": 0.012633545324206352, "Left": 0.6815018653869629, "Top": 0.36636629700660706}, "Polygon": [{"X": 0.6816360950469971, "Y": 0.36636629700660706}, {"X": 0.6914101243019104, "Y": 0.3664514422416687}, {"X": 0.6912758946418762, "Y": 0.37899982929229736}, {"X": 0.6815018653869629, "Y": 0.3789145350456238}]}, "Id": "4ebd5540-a588-4238-8cc9-6739aa404706"}, {"BlockType": "WORD", "Confidence": 99.16194915771484, "Text": "19", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.024607542902231216, "Height": 0.0244069155305624, "Left": 0.12802624702453613, "Top": 0.3974420428276062}, "Polygon": [{"X": 0.1282835751771927, "Y": 0.3974420428276062}, {"X": 0.15263378620147705, "Y": 0.3976551294326782}, {"X": 0.15237638354301453, "Y": 0.42184895277023315}, {"X": 0.12802624702453613, "Y": 0.421635240316391}]}, "Id": "78592cd1-6334-4a68-8ba3-4ad038be7507"}, {"BlockType": "WORD", "Confidence": 55.0600700378418, "Text": "X$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029170088469982147, "Height": 0.012118513695895672, "Left": 0.19505749642848969, "Top": 0.41111308336257935}, "Polygon": [{"X": 0.19518376886844635, "Y": 0.41111308336257935}, {"X": 0.22422759234905243, "Y": 0.41136765480041504}, {"X": 0.22410127520561218, "Y": 0.4232316017150879}, {"X": 0.19505749642848969, "Y": 0.42297667264938354}]}, "Id": "8bee1147-0845-41c3-afd7-14cb0a307af7"}, {"BlockType": "WORD", "Confidence": 63.11244201660156, "Text": "$ 200", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08576064556837082, "Height": 0.02554081194102764, "Left": 0.21498921513557434, "Top": 0.3986492156982422}, "Polygon": [{"X": 0.21525312960147858, "Y": 0.3986492156982422}, {"X": 0.30074983835220337, "Y": 0.39939749240875244}, {"X": 0.30048567056655884, "Y": 0.42419004440307617}, {"X": 0.21498921513557434, "Y": 0.42343956232070923}]}, "Id": "007bf163-4d12-416c-b337-302def74a683"}, {"BlockType": "WORD", "Confidence": 96.72692108154297, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0107125174254179, "Height": 0.004973206203430891, "Left": 0.3381791114807129, "Top": 0.4162019193172455}, "Polygon": [{"X": 0.3382311165332794, "Y": 0.4162019193172455}, {"X": 0.34889161586761475, "Y": 0.4162954092025757}, {"X": 0.3488396108150482, "Y": 0.42117512226104736}, {"X": 0.3381791114807129, "Y": 0.4210815727710724}]}, "Id": "244ad020-b695-4f6c-bf7f-f05c0435d031"}, {"BlockType": "WORD", "Confidence": 81.337890625, "Text": "$ 38.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09852387756109238, "Height": 0.02470633201301098, "Left": 0.3530122935771942, "Top": 0.4015251398086548}, "Polygon": [{"X": 0.35326650738716125, "Y": 0.4015251398086548}, {"X": 0.4515361785888672, "Y": 0.4023853540420532}, {"X": 0.45128169655799866, "Y": 0.4262314736843109}, {"X": 0.3530122935771942, "Y": 0.4253687858581543}]}, "Id": "3ac0648b-719b-4f55-91d4-076e490ba008"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08972079306840897, "Height": 0.013317431323230267, "Left": 0.5713347792625427, "Top": 0.3993717432022095}, "Polygon": [{"X": 0.5714686512947083, "Y": 0.3993717432022095}, {"X": 0.6610555648803711, "Y": 0.40015554428100586}, {"X": 0.6609215140342712, "Y": 0.4126891493797302}, {"X": 0.5713347792625427, "Y": 0.41190415620803833}]}, "Id": "546b6945-efd5-4209-bc1b-7148921a319a"}, {"BlockType": "WORD", "Confidence": 88.52698516845703, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0070787472650408745, "Height": 0.0024002715945243835, "Left": 0.6696115732192993, "Top": 0.40727633237838745}, "Polygon": [{"X": 0.669636607170105, "Y": 0.40727633237838745}, {"X": 0.6766903400421143, "Y": 0.40733811259269714}, {"X": 0.6766653060913086, "Y": 0.40967661142349243}, {"X": 0.6696115732192993, "Y": 0.40961483120918274}]}, "Id": "48e7e67f-5d77-47aa-a525-93b4ac8c179c"}, {"BlockType": "WORD", "Confidence": 96.45567321777344, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009592813439667225, "Height": 0.012377478182315826, "Left": 0.6863047480583191, "Top": 0.40097925066947937}, "Polygon": [{"X": 0.6864362359046936, "Y": 0.40097925066947937}, {"X": 0.6958975791931152, "Y": 0.40106201171875}, {"X": 0.695766031742096, "Y": 0.4133567214012146}, {"X": 0.6863047480583191, "Y": 0.41327381134033203}]}, "Id": "e7162c4b-1976-4d7d-92d6-d5d6dad106e9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "RACKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06509441137313843, "Height": 0.012707075104117393, "Left": 0.11123371869325638, "Top": 0.4270114302635193}, "Polygon": [{"X": 0.11136277765035629, "Y": 0.4270114302635193}, {"X": 0.1763281226158142, "Y": 0.4275820255279541}, {"X": 0.17619897425174713, "Y": 0.4397185146808624}, {"X": 0.11123371869325638, "Y": 0.43914711475372314}]}, "Id": "14b175d8-fdb8-42cc-a164-6db0380dde90"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "AMOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08712081611156464, "Height": 0.01244564913213253, "Left": 0.22659026086330414, "Top": 0.42883726954460144}, "Polygon": [{"X": 0.226714625954628, "Y": 0.42883726954460144}, {"X": 0.3137110769748688, "Y": 0.42960140109062195}, {"X": 0.31358659267425537, "Y": 0.4412829279899597}, {"X": 0.22659026086330414, "Y": 0.44051772356033325}]}, "Id": "a0d94637-1a81-4b7d-8197-9492cb960e6e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06548167020082474, "Height": 0.012474516406655312, "Left": 0.3615785539150238, "Top": 0.43040937185287476}, "Polygon": [{"X": 0.3617054224014282, "Y": 0.43040937185287476}, {"X": 0.42706024646759033, "Y": 0.43098342418670654}, {"X": 0.42693325877189636, "Y": 0.4428838789463043}, {"X": 0.3615785539150238, "Y": 0.4423089921474457}]}, "Id": "be28bd9d-5521-4aef-b9f8-1345763226a2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047641705721616745, "Height": 0.012129289098083973, "Left": 0.5742791295051575, "Top": 0.4508746862411499}, "Polygon": [{"X": 0.574404239654541, "Y": 0.4508746862411499}, {"X": 0.6219208240509033, "Y": 0.451293021440506}, {"X": 0.621795654296875, "Y": 0.4630039930343628}, {"X": 0.5742791295051575, "Y": 0.46258509159088135}]}, "Id": "3fb532ce-4355-4f12-a1ba-5d8d58e55868"}, {"BlockType": "WORD", "Confidence": 92.02487182617188, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010639685206115246, "Height": 0.00827598012983799, "Left": 0.6759305596351624, "Top": 0.4543304145336151}, "Polygon": [{"X": 0.6760180592536926, "Y": 0.4543304145336151}, {"X": 0.6865702271461487, "Y": 0.45442333817481995}, {"X": 0.6864827275276184, "Y": 0.46260640025138855}, {"X": 0.6759305596351624, "Y": 0.46251338720321655}]}, "Id": "d95d207a-17d5-4558-8715-d9bcca7382f6"}, {"BlockType": "WORD", "Confidence": 46.879783630371094, "Text": "$ 38.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.130058154463768, "Height": 0.02733147330582142, "Left": 0.679965078830719, "Top": 0.43849942088127136}, "Polygon": [{"X": 0.6802452206611633, "Y": 0.43849942088127136}, {"X": 0.8100232481956482, "Y": 0.4396401047706604}, {"X": 0.8097427487373352, "Y": 0.46583089232444763}, {"X": 0.679965078830719, "Y": 0.46468663215637207}]}, "Id": "03cee05e-6954-449f-bc10-f481dd42f05e"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "(<PERSON>ck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046628598123788834, "Height": 0.014465301297605038, "Left": 0.5734478235244751, "Top": 0.46765801310539246}, "Polygon": [{"X": 0.5735979676246643, "Y": 0.46765801310539246}, {"X": 0.6200764179229736, "Y": 0.46806800365448}, {"X": 0.6199261546134949, "Y": 0.48212331533432007}, {"X": 0.5734478235244751, "Y": 0.4817126393318176}]}, "Id": "4f635101-55d4-434b-b687-c6c4c228ae72"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0537114292383194, "Height": 0.012460516765713692, "Left": 0.624947726726532, "Top": 0.46845152974128723}, "Polygon": [{"X": 0.6250758767127991, "Y": 0.46845152974128723}, {"X": 0.6786591410636902, "Y": 0.46892422437667847}, {"X": 0.6785309314727783, "Y": 0.4809120297431946}, {"X": 0.624947726726532, "Y": 0.4804386794567108}]}, "Id": "a17b51c9-7704-43b7-b2c3-31f893efb355"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029951341450214386, "Height": 0.0119241988286376, "Left": 0.6837324500083923, "Top": 0.4692479968070984}, "Polygon": [{"X": 0.6838571429252625, "Y": 0.4692479968070984}, {"X": 0.7136837840080261, "Y": 0.46951112151145935}, {"X": 0.7135589718818665, "Y": 0.48117220401763916}, {"X": 0.6837324500083923, "Y": 0.48090872168540955}]}, "Id": "9aedad16-d70e-4c1a-a05e-7c2a0aafc3ac"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "now", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033701617270708084, "Height": 0.00920778326690197, "Left": 0.7196651101112366, "Top": 0.47287485003471375}, "Polygon": [{"X": 0.7197604775428772, "Y": 0.47287485003471375}, {"X": 0.7533667683601379, "Y": 0.4731714427471161}, {"X": 0.7532713413238525, "Y": 0.48208263516426086}, {"X": 0.7196651101112366, "Y": 0.48178574442863464}]}, "Id": "742cfc0c-37ee-478e-9ddf-5b87fe2b211d"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019627146422863007, "Height": 0.01170144323259592, "Left": 0.7578073740005493, "Top": 0.47010868787765503}, "Polygon": [{"X": 0.7579308152198792, "Y": 0.47010868787765503}, {"X": 0.7774345278739929, "Y": 0.4702807366847992}, {"X": 0.7773110866546631, "Y": 0.4818101227283478}, {"X": 0.7578073740005493, "Y": 0.4816378355026245}]}, "Id": "cd2dae5f-4966-4583-8126-c51fc595e654"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031811583787202835, "Height": 0.01010800339281559, "Left": 0.7820531725883484, "Top": 0.47248053550720215}, "Polygon": [{"X": 0.7821584343910217, "Y": 0.47248053550720215}, {"X": 0.8138647675514221, "Y": 0.47276031970977783}, {"X": 0.8137595057487488, "Y": 0.482588529586792}, {"X": 0.7820531725883484, "Y": 0.48230841755867004}]}, "Id": "cf60fa81-9dcf-47d7-899a-13b66ce22480"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015507413074374199, "Height": 0.010358530096709728, "Left": 0.8185149431228638, "Top": 0.4725968539714813}, "Polygon": [{"X": 0.8186244368553162, "Y": 0.4725968539714813}, {"X": 0.8340223431587219, "Y": 0.4727327227592468}, {"X": 0.8339127898216248, "Y": 0.48295536637306213}, {"X": 0.8185149431228638, "Y": 0.4828193485736847}]}, "Id": "0bb0a774-e18b-4ebf-a5bf-e90fdcd381f5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0244571715593338, "Height": 0.012070898897945881, "Left": 0.8386893272399902, "Top": 0.4714076519012451}, "Polygon": [{"X": 0.8388163447380066, "Y": 0.4714076519012451}, {"X": 0.8631464838981628, "Y": 0.4716223180294037}, {"X": 0.8630194067955017, "Y": 0.4834785461425781}, {"X": 0.8386893272399902, "Y": 0.4832635819911957}]}, "Id": "883a0105-1405-407c-9b1a-a38605f9d8d0"}, {"BlockType": "WORD", "Confidence": 99.6827163696289, "Text": "broker.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06281241774559021, "Height": 0.014736440032720566, "Left": 0.5837801098823547, "Top": 0.48478901386260986}, "Polygon": [{"X": 0.5839316844940186, "Y": 0.48478901386260986}, {"X": 0.6465925574302673, "Y": 0.4853428900241852}, {"X": 0.6464409232139587, "Y": 0.4995254576206207}, {"X": 0.5837801098823547, "Y": 0.4989706575870514}]}, "Id": "0dc82343-5f77-4890-ba43-482006828f31"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028356090188026428, "Height": 0.0120331896468997, "Left": 0.5735213756561279, "Top": 0.5193597674369812}, "Polygon": [{"X": 0.5736472606658936, "Y": 0.5193597674369812}, {"X": 0.6018774509429932, "Y": 0.5196103453636169}, {"X": 0.6017515063285828, "Y": 0.5313929319381714}, {"X": 0.5735213756561279, "Y": 0.5311420559883118}]}, "Id": "b6630327-2e6c-4c48-bde2-b73059b3b8f9"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03627048805356026, "Height": 0.011967813596129417, "Left": 0.6069329380989075, "Top": 0.5199398398399353}, "Polygon": [{"X": 0.6070574522018433, "Y": 0.5199398398399353}, {"X": 0.6432034373283386, "Y": 0.5202606916427612}, {"X": 0.6430789232254028, "Y": 0.5319076776504517}, {"X": 0.6069329380989075, "Y": 0.5315864086151123}]}, "Id": "9e446520-9ca8-41e5-9a59-a62d96cb252e"}, {"BlockType": "WORD", "Confidence": 67.74713134765625, "Text": "$ 3489.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.15365496277809143, "Height": 0.03328830003738403, "Left": 0.6409112811088562, "Top": 0.5012696385383606}, "Polygon": [{"X": 0.6412526369094849, "Y": 0.5012696385383606}, {"X": 0.79456627368927, "Y": 0.5026273727416992}, {"X": 0.7942243218421936, "Y": 0.5345578789710999}, {"X": 0.6409112811088562, "Y": 0.5331950187683105}]}, "Id": "5479b3b4-44c4-4cdc-a8c6-9aa171be8bb8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10392781347036362, "Height": 0.019689204171299934, "Left": 0.10903768241405487, "Top": 0.5501824021339417}, "Polygon": [{"X": 0.10923722386360168, "Y": 0.5501824021339417}, {"X": 0.21296550333499908, "Y": 0.5511069297790527}, {"X": 0.21276572346687317, "Y": 0.5698716044425964}, {"X": 0.10903768241405487, "Y": 0.5689451098442078}]}, "Id": "596a8a3a-25b7-43ce-9058-39e22a5eb251"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Shortages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13614831864833832, "Height": 0.024422945454716682, "Left": 0.22233344614505768, "Top": 0.5516890287399292}, "Polygon": [{"X": 0.22258055210113525, "Y": 0.5516890287399292}, {"X": 0.358481764793396, "Y": 0.5529003143310547}, {"X": 0.3582342863082886, "Y": 0.5761119723320007}, {"X": 0.22233344614505768, "Y": 0.5748973488807678}]}, "Id": "0d38ee06-2a2b-4ba9-a9ac-adc09e59b815"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "(equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06127724424004555, "Height": 0.011234672740101814, "Left": 0.10768195986747742, "Top": 0.5747796297073364}, "Polygon": [{"X": 0.10779561847448349, "Y": 0.5747796297073364}, {"X": 0.16895920038223267, "Y": 0.5753263235092163}, {"X": 0.16884547472000122, "Y": 0.5860143303871155}, {"X": 0.10768195986747742, "Y": 0.5854669213294983}]}, "Id": "18be897c-4f65-469a-aefd-bd028e485c7a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "missing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041436050087213516, "Height": 0.011012761853635311, "Left": 0.17185699939727783, "Top": 0.5754408240318298}, "Polygon": [{"X": 0.17197026312351227, "Y": 0.5754408240318298}, {"X": 0.21329304575920105, "Y": 0.5758101344108582}, {"X": 0.21317973732948303, "Y": 0.5864535570144653}, {"X": 0.17185699939727783, "Y": 0.586083710193634}]}, "Id": "979fa9be-7144-47a7-a66d-880d1a00528a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02392592281103134, "Height": 0.008977060206234455, "Left": 0.2158747911453247, "Top": 0.5760851502418518}, "Polygon": [{"X": 0.2159680873155594, "Y": 0.5760851502418518}, {"X": 0.23980070650577545, "Y": 0.576298177242279}, {"X": 0.23970738053321838, "Y": 0.5850622057914734}, {"X": 0.2158747911453247, "Y": 0.584848940372467}]}, "Id": "22c97980-ac5f-471a-8b27-cdd661a5e181"}, {"BlockType": "WORD", "Confidence": 79.88141632080078, "Text": "insufficient", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06021925061941147, "Height": 0.009125638753175735, "Left": 0.24303817749023438, "Top": 0.5765876770019531}, "Polygon": [{"X": 0.24312962591648102, "Y": 0.5765876770019531}, {"X": 0.30325743556022644, "Y": 0.5771251320838928}, {"X": 0.30316591262817383, "Y": 0.5857133269309998}, {"X": 0.24303817749023438, "Y": 0.5851752758026123}]}, "Id": "866c6a01-109c-4417-8e4c-f4e2d2cbcad8"}, {"BlockType": "WORD", "Confidence": 61.585819244384766, "Text": "documentation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08354302495718002, "Height": 0.009265627712011337, "Left": 0.305807888507843, "Top": 0.5775192975997925}, "Polygon": [{"X": 0.30589866638183594, "Y": 0.5775192975997925}, {"X": 0.38935092091560364, "Y": 0.5782653093338013}, {"X": 0.38926005363464355, "Y": 0.5867848992347717}, {"X": 0.305807888507843, "Y": 0.5860381722450256}]}, "Id": "3a30ee94-2217-4252-8215-fe2e3afbcba2"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "Racks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05232549086213112, "Height": 0.012340602464973927, "Left": 0.10840483754873276, "Top": 0.5988958477973938}, "Polygon": [{"X": 0.10853108763694763, "Y": 0.5988958477973938}, {"X": 0.16073031723499298, "Y": 0.5993637442588806}, {"X": 0.16060400009155273, "Y": 0.6112364530563354}, {"X": 0.10840483754873276, "Y": 0.6107679009437561}]}, "Id": "46adf26a-3748-4b0f-9f78-76e90b126285"}, {"BlockType": "WORD", "Confidence": 94.03001403808594, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005704175215214491, "Height": 0.013133934698998928, "Left": 0.16686001420021057, "Top": 0.600215494632721}, "Polygon": [{"X": 0.16699925065040588, "Y": 0.600215494632721}, {"X": 0.17256419360637665, "Y": 0.600265383720398}, {"X": 0.17242495715618134, "Y": 0.613349437713623}, {"X": 0.16686001420021057, "Y": 0.6132994294166565}]}, "Id": "34384e75-5a3d-456a-a63d-b20aeea2f9c5"}, {"BlockType": "WORD", "Confidence": 93.6963882446289, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00599106727167964, "Height": 0.013404854573309422, "Left": 0.18836075067520142, "Top": 0.6002486944198608}, "Polygon": [{"X": 0.18850286304950714, "Y": 0.6002486944198608}, {"X": 0.19435182213783264, "Y": 0.6003011465072632}, {"X": 0.19420969486236572, "Y": 0.6136535406112671}, {"X": 0.18836075067520142, "Y": 0.61360102891922}]}, "Id": "6794da6c-304a-4aeb-b620-1edb52197e7d"}, {"BlockType": "WORD", "Confidence": 88.10407257080078, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0090889697894454, "Height": 0.008641580119729042, "Left": 0.19964934885501862, "Top": 0.603133499622345}, "Polygon": [{"X": 0.1997404843568802, "Y": 0.603133499622345}, {"X": 0.2087383270263672, "Y": 0.6032141447067261}, {"X": 0.20864719152450562, "Y": 0.6117750406265259}, {"X": 0.19964934885501862, "Y": 0.6116942763328552}]}, "Id": "70d57dd8-be38-4a69-b047-e723a2f4cde9"}, {"BlockType": "WORD", "Confidence": 95.21942901611328, "Text": "$250.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06448931992053986, "Height": 0.012903358787298203, "Left": 0.21422052383422852, "Top": 0.6000036001205444}, "Polygon": [{"X": 0.21435174345970154, "Y": 0.6000036001205444}, {"X": 0.27870985865592957, "Y": 0.6005805134773254}, {"X": 0.278578519821167, "Y": 0.6129069328308105}, {"X": 0.21422052383422852, "Y": 0.6123291850090027}]}, "Id": "b6ad6478-a6ec-402a-828b-12b435dc4000"}, {"BlockType": "WORD", "Confidence": 94.59263610839844, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011687500402331352, "Height": 0.004914498422294855, "Left": 0.2833232283592224, "Top": 0.6041830778121948}, "Polygon": [{"X": 0.28337448835372925, "Y": 0.6041830778121948}, {"X": 0.2950107455253601, "Y": 0.6042874455451965}, {"X": 0.29495948553085327, "Y": 0.6090975999832153}, {"X": 0.2833232283592224, "Y": 0.6089931726455688}]}, "Id": "b1d4af7f-87c1-4e27-88b9-b77d9a2a1d1a"}, {"BlockType": "WORD", "Confidence": 95.18474578857422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009872161783277988, "Height": 0.012019628658890724, "Left": 0.2991863489151001, "Top": 0.6011492609977722}, "Polygon": [{"X": 0.299313485622406, "Y": 0.6011492609977722}, {"X": 0.3090585172176361, "Y": 0.6012366414070129}, {"X": 0.3089313507080078, "Y": 0.6131688952445984}, {"X": 0.2991863489151001, "Y": 0.6130813956260681}]}, "Id": "16806a07-56b6-4dfa-a739-0d7d5aa5fcaf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041280630975961685, "Height": 0.012016115710139275, "Left": 0.5899771451950073, "Top": 0.6049606800079346}, "Polygon": [{"X": 0.5901015996932983, "Y": 0.6049606800079346}, {"X": 0.6312577724456787, "Y": 0.605329692363739}, {"X": 0.6311332583427429, "Y": 0.616976797580719}, {"X": 0.5899771451950073, "Y": 0.6166073083877563}]}, "Id": "db499037-0e96-4c84-893b-89015a4d6649"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0900847464799881, "Height": 0.012530963867902756, "Left": 0.6372806429862976, "Top": 0.6055334806442261}, "Polygon": [{"X": 0.6374059915542603, "Y": 0.6055334806442261}, {"X": 0.7273653745651245, "Y": 0.6063400506973267}, {"X": 0.7272399067878723, "Y": 0.6180644631385803}, {"X": 0.6372806429862976, "Y": 0.617256760597229}]}, "Id": "fe6364b4-c127-49d1-af0f-bb2308bc25d8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0274906475096941, "Height": 0.01181625947356224, "Left": 0.5917972326278687, "Top": 0.6223681569099426}, "Polygon": [{"X": 0.5919208526611328, "Y": 0.6223681569099426}, {"X": 0.6192878484725952, "Y": 0.6226140260696411}, {"X": 0.6191641688346863, "Y": 0.6341844201087952}, {"X": 0.5917972326278687, "Y": 0.633938193321228}]}, "Id": "6dbc8319-7d1f-4d52-846a-3f22ed312ced"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Load:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04427501559257507, "Height": 0.012348860502243042, "Left": 0.6239337921142578, "Top": 0.6226383447647095}, "Polygon": [{"X": 0.6240615844726562, "Y": 0.6226383447647095}, {"X": 0.6682088375091553, "Y": 0.6230349540710449}, {"X": 0.6680809855461121, "Y": 0.6349872350692749}, {"X": 0.6239337921142578, "Y": 0.6345900297164917}]}, "Id": "41599ed4-8adb-4e4a-ba33-520048cd0abc"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039441391825675964, "Height": 0.01213056966662407, "Left": 0.10805358737707138, "Top": 0.6335623264312744}, "Polygon": [{"X": 0.10817882418632507, "Y": 0.6335623264312744}, {"X": 0.14749497175216675, "Y": 0.6339161992073059}, {"X": 0.14736968278884888, "Y": 0.6456928849220276}, {"X": 0.10805358737707138, "Y": 0.6453385353088379}]}, "Id": "929b6c65-ba3c-4553-ae71-d395452b3bc6"}, {"BlockType": "WORD", "Confidence": 99.64285278320312, "Text": "Locks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05237100273370743, "Height": 0.012675218284130096, "Left": 0.15320132672786713, "Top": 0.6340501308441162}, "Polygon": [{"X": 0.15333117544651031, "Y": 0.6340501308441162}, {"X": 0.20557232201099396, "Y": 0.6345202922821045}, {"X": 0.2054423987865448, "Y": 0.6467253565788269}, {"X": 0.15320132672786713, "Y": 0.6462544798851013}]}, "Id": "8962bf61-0ef0-4dad-958e-afceabb313c7"}, {"BlockType": "WORD", "Confidence": 94.32298278808594, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005748007446527481, "Height": 0.013227987103164196, "Left": 0.21162192523479462, "Top": 0.6355246901512146}, "Polygon": [{"X": 0.2117622047662735, "Y": 0.6355246901512146}, {"X": 0.2173699289560318, "Y": 0.6355751156806946}, {"X": 0.21722963452339172, "Y": 0.6487526297569275}, {"X": 0.21162192523479462, "Y": 0.648702085018158}]}, "Id": "668dd645-e0f8-40ac-9c73-89685f3b29e4"}, {"BlockType": "WORD", "Confidence": 93.78767395019531, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00564377149567008, "Height": 0.013539738021790981, "Left": 0.2334030270576477, "Top": 0.6354992389678955}, "Polygon": [{"X": 0.2335466742515564, "Y": 0.6354992389678955}, {"X": 0.23904679715633392, "Y": 0.6355487108230591}, {"X": 0.23890314996242523, "Y": 0.6490389704704285}, {"X": 0.2334030270576477, "Y": 0.6489893794059753}]}, "Id": "17fa6f4d-1713-4337-adcc-c819b18b7e2b"}, {"BlockType": "WORD", "Confidence": 78.63361358642578, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010355092585086823, "Height": 0.00921503733843565, "Left": 0.24375353753566742, "Top": 0.6378905177116394}, "Polygon": [{"X": 0.2438506931066513, "Y": 0.6378905177116394}, {"X": 0.25410863757133484, "Y": 0.6379828453063965}, {"X": 0.25401148200035095, "Y": 0.6471055746078491}, {"X": 0.24375353753566742, "Y": 0.6470130681991577}]}, "Id": "6e6fb528-5f6f-4a17-8884-fc6d83376aff"}, {"BlockType": "WORD", "Confidence": 96.56090545654297, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054984502494335175, "Height": 0.012540433555841446, "Left": 0.25921210646629333, "Top": 0.6354221701622009}, "Polygon": [{"X": 0.25934040546417236, "Y": 0.6354221701622009}, {"X": 0.3141966164112091, "Y": 0.6359159350395203}, {"X": 0.3140682280063629, "Y": 0.6479625701904297}, {"X": 0.25921210646629333, "Y": 0.6474681496620178}]}, "Id": "1d470583-085c-4728-a664-aabd140dd5a3"}, {"BlockType": "WORD", "Confidence": 94.98784637451172, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010479705408215523, "Height": 0.005018445663154125, "Left": 0.31896141171455383, "Top": 0.6395023465156555}, "Polygon": [{"X": 0.31901389360427856, "Y": 0.6395023465156555}, {"X": 0.3294411301612854, "Y": 0.6395962238311768}, {"X": 0.32938864827156067, "Y": 0.6445207595825195}, {"X": 0.31896141171455383, "Y": 0.6444268226623535}]}, "Id": "634a0fd1-dbc6-49c9-bea9-449a6f135cb6"}, {"BlockType": "WORD", "Confidence": 98.11284637451172, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009755674749612808, "Height": 0.012789428234100342, "Left": 0.33429375290870667, "Top": 0.6359384655952454}, "Polygon": [{"X": 0.33442914485931396, "Y": 0.6359384655952454}, {"X": 0.3440494239330292, "Y": 0.636025071144104}, {"X": 0.3439140021800995, "Y": 0.6487278938293457}, {"X": 0.33429375290870667, "Y": 0.6486411690711975}]}, "Id": "7b12e10c-d92f-4693-bb0d-8dc92e6cc123"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009546203538775444, "Height": 0.012938784435391426, "Left": 0.590423583984375, "Top": 0.6565326452255249}, "Polygon": [{"X": 0.5905609726905823, "Y": 0.6565326452255249}, {"X": 0.5999698042869568, "Y": 0.6566175222396851}, {"X": 0.5998324155807495, "Y": 0.6694714426994324}, {"X": 0.590423583984375, "Y": 0.6693864464759827}]}, "Id": "f0b0c04e-379f-4329-8825-dfb0998fd4da"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04514038935303688, "Height": 0.012704901397228241, "Left": 0.10684394091367722, "Top": 0.6681486368179321}, "Polygon": [{"X": 0.10697472095489502, "Y": 0.6681486368179321}, {"X": 0.1519843339920044, "Y": 0.6685553789138794}, {"X": 0.15185348689556122, "Y": 0.680853545665741}, {"X": 0.10684394091367722, "Y": 0.680446207523346}]}, "Id": "4b115c7f-bc67-410d-b1e1-f3fdba01a1ec"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "<PERSON>:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040137045085430145, "Height": 0.012454976327717304, "Left": 0.15603305399417877, "Top": 0.6688918471336365}, "Polygon": [{"X": 0.15616172552108765, "Y": 0.6688918471336365}, {"X": 0.19617009162902832, "Y": 0.669253408908844}, {"X": 0.19604137539863586, "Y": 0.6813468337059021}, {"X": 0.15603305399417877, "Y": 0.6809847354888916}]}, "Id": "47adf24e-7a54-4c87-95a9-c9690e94a642"}, {"BlockType": "WORD", "Confidence": 85.91825103759766, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028138602152466774, "Height": 0.014518373645842075, "Left": 0.20202824473381042, "Top": 0.6692372560501099}, "Polygon": [{"X": 0.20218008756637573, "Y": 0.6692372560501099}, {"X": 0.23016685247421265, "Y": 0.6694901585578918}, {"X": 0.23001495003700256, "Y": 0.6837555766105652}, {"X": 0.20202824473381042, "Y": 0.6835022568702698}]}, "Id": "3062fd7b-d771-451f-bfb3-618336dd6dfd"}, {"BlockType": "WORD", "Confidence": 86.55353546142578, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009614626877009869, "Height": 0.008762451820075512, "Left": 0.2348942756652832, "Top": 0.6729953289031982}, "Polygon": [{"X": 0.23498666286468506, "Y": 0.6729953289031982}, {"X": 0.24450890719890594, "Y": 0.6730813980102539}, {"X": 0.2444165050983429, "Y": 0.6817577481269836}, {"X": 0.2348942756652832, "Y": 0.6816716194152832}]}, "Id": "5a87abe5-f993-4885-8842-0fa1ef973001"}, {"BlockType": "WORD", "Confidence": 96.13580322265625, "Text": "$500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06502430140972137, "Height": 0.012615283019840717, "Left": 0.24951714277267456, "Top": 0.6701006889343262}, "Polygon": [{"X": 0.24964523315429688, "Y": 0.6701006889343262}, {"X": 0.31454142928123474, "Y": 0.670687198638916}, {"X": 0.31441324949264526, "Y": 0.6827159523963928}, {"X": 0.24951714277267456, "Y": 0.6821286082267761}]}, "Id": "31643f8e-5ff0-49c6-9dcd-9123276601eb"}, {"BlockType": "WORD", "Confidence": 94.72516632080078, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011031140573322773, "Height": 0.005223207175731659, "Left": 0.3190732002258301, "Top": 0.6743082404136658}, "Polygon": [{"X": 0.31912779808044434, "Y": 0.6743082404136658}, {"X": 0.3301043212413788, "Y": 0.6744074821472168}, {"X": 0.3300497233867645, "Y": 0.679531455039978}, {"X": 0.3190732002258301, "Y": 0.6794321537017822}]}, "Id": "79bff771-47f2-4337-b523-dc3ff9b72a1e"}, {"BlockType": "WORD", "Confidence": 97.01829528808594, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009480370208621025, "Height": 0.01205457653850317, "Left": 0.33488526940345764, "Top": 0.6709883213043213}, "Polygon": [{"X": 0.33501285314559937, "Y": 0.6709883213043213}, {"X": 0.3443656265735626, "Y": 0.6710728406906128}, {"X": 0.3442380130290985, "Y": 0.6830428838729858}, {"X": 0.33488526940345764, "Y": 0.6829582452774048}]}, "Id": "b5c97433-e060-425e-b68d-a3a2be811193"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039613980799913406, "Height": 0.012152782641351223, "Left": 0.1064658984541893, "Top": 0.7024759650230408}, "Polygon": [{"X": 0.10659132152795792, "Y": 0.7024759650230408}, {"X": 0.146079882979393, "Y": 0.7028342485427856}, {"X": 0.14595440030097961, "Y": 0.7146287560462952}, {"X": 0.1064658984541893, "Y": 0.7142699956893921}]}, "Id": "980a1935-a506-4fcc-88a0-b25f9ea65514"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Straps:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05385854095220566, "Height": 0.014853174798190594, "Left": 0.15163332223892212, "Top": 0.7030194401741028}, "Polygon": [{"X": 0.15178616344928741, "Y": 0.7030194401741028}, {"X": 0.20549185574054718, "Y": 0.7035067081451416}, {"X": 0.20533892512321472, "Y": 0.7178726196289062}, {"X": 0.15163332223892212, "Y": 0.7173845171928406}]}, "Id": "d60f9132-1fb1-4033-b2cb-2b71a2535825"}, {"BlockType": "WORD", "Confidence": 94.6338882446289, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005485313478857279, "Height": 0.013751299120485783, "Left": 0.21135593950748444, "Top": 0.7041728496551514}, "Polygon": [{"X": 0.2115018218755722, "Y": 0.7041728496551514}, {"X": 0.21684125065803528, "Y": 0.7042213082313538}, {"X": 0.2166953682899475, "Y": 0.717924177646637}, {"X": 0.21135593950748444, "Y": 0.7178756594657898}]}, "Id": "1c4d2523-9552-4439-82f2-0c31730e01e9"}, {"BlockType": "WORD", "Confidence": 93.4791488647461, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0060141501016914845, "Height": 0.012901783920824528, "Left": 0.2332141399383545, "Top": 0.7042654752731323}, "Polygon": [{"X": 0.2333509474992752, "Y": 0.7042654752731323}, {"X": 0.2392282783985138, "Y": 0.7043187618255615}, {"X": 0.23909145593643188, "Y": 0.7171672582626343}, {"X": 0.2332141399383545, "Y": 0.7171138525009155}]}, "Id": "ca6ac020-ad01-4bc5-a886-b3a7d8860803"}, {"BlockType": "WORD", "Confidence": 87.333984375, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009017144329845905, "Height": 0.008410515263676643, "Left": 0.24430063366889954, "Top": 0.7075878977775574}, "Polygon": [{"X": 0.24438932538032532, "Y": 0.7075878977775574}, {"X": 0.25331777334213257, "Y": 0.7076689004898071}, {"X": 0.2532290518283844, "Y": 0.7159984111785889}, {"X": 0.24430063366889954, "Y": 0.7159172892570496}]}, "Id": "ad81529f-7c58-40a6-9a3d-3e06c3fe1d1d"}, {"BlockType": "WORD", "Confidence": 96.0889663696289, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05531832203269005, "Height": 0.01295586209744215, "Left": 0.25881296396255493, "Top": 0.7040554881095886}, "Polygon": [{"X": 0.25894561409950256, "Y": 0.7040554881095886}, {"X": 0.3141312897205353, "Y": 0.7045562267303467}, {"X": 0.3139985501766205, "Y": 0.7170113921165466}, {"X": 0.25881296396255493, "Y": 0.7165099382400513}]}, "Id": "68c2e5dd-9152-4cc0-9681-946abbe389c3"}, {"BlockType": "WORD", "Confidence": 95.81712341308594, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01088781002908945, "Height": 0.0047997464425861835, "Left": 0.31873562932014465, "Top": 0.7087100148200989}, "Polygon": [{"X": 0.31878572702407837, "Y": 0.7087100148200989}, {"X": 0.32962343096733093, "Y": 0.708808422088623}, {"X": 0.3295733332633972, "Y": 0.7135097980499268}, {"X": 0.31873562932014465, "Y": 0.7134113311767578}]}, "Id": "d360d9c9-07f1-45ef-a85c-a0cc1413f34e"}, {"BlockType": "WORD", "Confidence": 97.84598541259766, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009295203723013401, "Height": 0.012669475749135017, "Left": 0.3343183994293213, "Top": 0.7048513293266296}, "Polygon": [{"X": 0.3344525396823883, "Y": 0.7048513293266296}, {"X": 0.3436135947704315, "Y": 0.7049344778060913}, {"X": 0.3434794247150421, "Y": 0.7175208330154419}, {"X": 0.3343183994293213, "Y": 0.7174375653266907}]}, "Id": "f2d089b1-1a0c-4461-acc7-5a41dca41ff8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Paperwork", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08504835516214371, "Height": 0.015285666100680828, "Left": 0.10517112165689468, "Top": 0.7372871041297913}, "Polygon": [{"X": 0.10532543808221817, "Y": 0.7372871041297913}, {"X": 0.1902194768190384, "Y": 0.7380604147911072}, {"X": 0.19006501138210297, "Y": 0.7525727152824402}, {"X": 0.10517112165689468, "Y": 0.7517980933189392}]}, "Id": "0521a5a8-59ea-43f6-a56f-6204ee480c7e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014433803968131542, "Height": 0.012098720297217369, "Left": 0.19567127525806427, "Top": 0.7379977703094482}, "Polygon": [{"X": 0.19579868018627167, "Y": 0.7379977703094482}, {"X": 0.2101050764322281, "Y": 0.738128125667572}, {"X": 0.2099776715040207, "Y": 0.7500964999198914}, {"X": 0.19567127525806427, "Y": 0.749966025352478}]}, "Id": "b06d20e3-f5e3-4799-937c-a70add97de8c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "other", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041491180658340454, "Height": 0.012559476308524609, "Left": 0.2155773639678955, "Top": 0.738172709941864}, "Polygon": [{"X": 0.21570706367492676, "Y": 0.738172709941864}, {"X": 0.25706854462623596, "Y": 0.738549530506134}, {"X": 0.25693878531455994, "Y": 0.7507321834564209}, {"X": 0.2155773639678955, "Y": 0.7503548860549927}]}, "Id": "5270a6d6-7fd6-4599-a6ee-dcca14b122b1"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Fines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04214145243167877, "Height": 0.012495969422161579, "Left": 0.26186132431030273, "Top": 0.7387315034866333}, "Polygon": [{"X": 0.26199033856391907, "Y": 0.7387315034866333}, {"X": 0.3040027618408203, "Y": 0.7391142249107361}, {"X": 0.3038736879825592, "Y": 0.7512274980545044}, {"X": 0.26186132431030273, "Y": 0.7508442401885986}]}, "Id": "99671ffb-8423-408c-84eb-b03c7214b8a3"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009484948590397835, "Height": 0.012786711566150188, "Left": 0.309244841337204, "Top": 0.7390968203544617}, "Polygon": [{"X": 0.3093802034854889, "Y": 0.7390968203544617}, {"X": 0.31872978806495667, "Y": 0.7391819953918457}, {"X": 0.31859442591667175, "Y": 0.7518835663795471}, {"X": 0.309244841337204, "Y": 0.7517982721328735}]}, "Id": "42db5a52-eab7-4f57-8e28-523cb0e18136"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "(see", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03209865838289261, "Height": 0.013513381592929363, "Left": 0.10499902069568634, "Top": 0.7547909617424011}, "Polygon": [{"X": 0.10513962060213089, "Y": 0.7547909617424011}, {"X": 0.13709767162799835, "Y": 0.7550827264785767}, {"X": 0.13695701956748962, "Y": 0.7683043479919434}, {"X": 0.10499902069568634, "Y": 0.7680122256278992}]}, "Id": "50570412-ffea-4578-a228-628571881030"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06535860151052475, "Height": 0.012614882551133633, "Left": 0.14136016368865967, "Top": 0.754649817943573}, "Polygon": [{"X": 0.14148803055286407, "Y": 0.754649817943573}, {"X": 0.20671877264976501, "Y": 0.7552452683448792}, {"X": 0.20659081637859344, "Y": 0.767264723777771}, {"X": 0.14136016368865967, "Y": 0.7666684985160828}]}, "Id": "e7b4ea4a-0f2c-473a-a811-0818b6fe8f11"}, {"BlockType": "WORD", "Confidence": 97.91753387451172, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12427230179309845, "Height": 0.015163022093474865, "Left": 0.2117805927991867, "Top": 0.7556958198547363}, "Polygon": [{"X": 0.21192993223667145, "Y": 0.7556958198547363}, {"X": 0.33605289459228516, "Y": 0.7568288445472717}, {"X": 0.3359033465385437, "Y": 0.7708588242530823}, {"X": 0.2117805927991867, "Y": 0.7697240114212036}]}, "Id": "3f5f97c5-b313-4c61-8fc6-8acc325d036c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Manager", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07062003016471863, "Height": 0.015203069895505905, "Left": 0.5337329506874084, "Top": 0.7601072192192078}, "Polygon": [{"X": 0.5338884592056274, "Y": 0.7601072192192078}, {"X": 0.6043529510498047, "Y": 0.7607505321502686}, {"X": 0.6041973233222961, "Y": 0.7753102779388428}, {"X": 0.5337329506874084, "Y": 0.774665892124176}]}, "Id": "a16380a9-7008-40c2-89fa-fa326b73102e"}, {"BlockType": "WORD", "Confidence": 99.28252410888672, "Text": "Approval:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07822774350643158, "Height": 0.01533462479710579, "Left": 0.6087590456008911, "Top": 0.7613548040390015}, "Polygon": [{"X": 0.6089153289794922, "Y": 0.7613548040390015}, {"X": 0.6869868040084839, "Y": 0.7620676159858704}, {"X": 0.6868304014205933, "Y": 0.7766894102096558}, {"X": 0.6087590456008911, "Y": 0.7759754061698914}]}, "Id": "ce8bae85-32ff-407c-ab8a-8c42e43be9be"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Notes:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050935082137584686, "Height": 0.012670502066612244, "Left": 0.10389607399702072, "Top": 0.7888162732124329}, "Polygon": [{"X": 0.10402585566043854, "Y": 0.7888162732124329}, {"X": 0.1548311561346054, "Y": 0.7892818450927734}, {"X": 0.15470129251480103, "Y": 0.8014867305755615}, {"X": 0.10389607399702072, "Y": 0.8010205030441284}]}, "Id": "6979b087-2737-456b-873f-5e8bb9a0ab16"}, {"BlockType": "WORD", "Confidence": 64.25243377685547, "Text": "my", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.086174376308918, "Height": 0.08377391844987869, "Left": 0.7600809931755066, "Top": 0.7368581295013428}, "Polygon": [{"X": 0.7609694004058838, "Y": 0.7368581295013428}, {"X": 0.846255362033844, "Y": 0.737634539604187}, {"X": 0.8453660607337952, "Y": 0.8206321001052856}, {"X": 0.7600809931755066, "Y": 0.8198482394218445}]}, "Id": "a94bf50d-3ea3-4c78-bad6-8226bae3ea06"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028739068657159805, "Height": 0.012228965759277344, "Left": 0.10320621728897095, "Top": 0.8405536413192749}, "Polygon": [{"X": 0.10333345830440521, "Y": 0.8405536413192749}, {"X": 0.13194528222084045, "Y": 0.840817391872406}, {"X": 0.1318180114030838, "Y": 0.8527826070785522}, {"X": 0.10320621728897095, "Y": 0.8525184988975525}]}, "Id": "8b8b2bc9-fc5d-44f1-85d6-0cb514e1d8e1"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "fine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025235647335648537, "Height": 0.010431413538753986, "Left": 0.1358151137828827, "Top": 0.8402221202850342}, "Polygon": [{"X": 0.1359236091375351, "Y": 0.8402221202850342}, {"X": 0.16105075180530548, "Y": 0.8404537439346313}, {"X": 0.1609422266483307, "Y": 0.8506535291671753}, {"X": 0.1358151137828827, "Y": 0.850421667098999}]}, "Id": "5639eb82-9bf8-4128-90c0-c67ceac50f6b"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021251332014799118, "Height": 0.008725482039153576, "Left": 0.1651841700077057, "Top": 0.8425621390342712}, "Polygon": [{"X": 0.1652749478816986, "Y": 0.8425621390342712}, {"X": 0.1864355057477951, "Y": 0.8427572250366211}, {"X": 0.186344712972641, "Y": 0.8512876033782959}, {"X": 0.1651841700077057, "Y": 0.8510923385620117}]}, "Id": "972fb920-e274-4a57-8426-aeefff755f6c"}, {"BlockType": "WORD", "Confidence": 99.11331939697266, "Text": "appealed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05864504724740982, "Height": 0.01267149206250906, "Left": 0.1904066652059555, "Top": 0.8413351774215698}, "Polygon": [{"X": 0.1905357837677002, "Y": 0.8413351774215698}, {"X": 0.24905171990394592, "Y": 0.8418745994567871}, {"X": 0.24892251193523407, "Y": 0.8540066480636597}, {"X": 0.1904066652059555, "Y": 0.8534664511680603}]}, "Id": "574894e0-1553-4232-a811-600c018d8a84"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04207466542720795, "Height": 0.010523270815610886, "Left": 0.25330492854118347, "Top": 0.8418676853179932}, "Polygon": [{"X": 0.2534129023551941, "Y": 0.8418676853179932}, {"X": 0.2953796088695526, "Y": 0.8422545790672302}, {"X": 0.2952716052532196, "Y": 0.8523909449577332}, {"X": 0.25330492854118347, "Y": 0.8520036339759827}]}, "Id": "8257ced5-695c-40f6-8aa0-22fc3e9af6a6"}, {"BlockType": "WORD", "Confidence": 99.52965545654297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0071057649329304695, "Height": 0.01000670064240694, "Left": 0.2996779978275299, "Top": 0.8424915671348572}, "Polygon": [{"X": 0.29978394508361816, "Y": 0.8424915671348572}, {"X": 0.3067837655544281, "Y": 0.8425561189651489}, {"X": 0.30667781829833984, "Y": 0.8524982929229736}, {"X": 0.2996779978275299, "Y": 0.8524336814880371}]}, "Id": "0402d62a-be8c-489e-8af6-36f55b4d77da"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05625129118561745, "Height": 0.010887066833674908, "Left": 0.31153589487075806, "Top": 0.8424902558326721}, "Polygon": [{"X": 0.31164637207984924, "Y": 0.8424902558326721}, {"X": 0.3677871823310852, "Y": 0.8430078029632568}, {"X": 0.36767661571502686, "Y": 0.8533773422241211}, {"X": 0.31153589487075806, "Y": 0.8528591990470886}]}, "Id": "d8eed6d7-23aa-430c-b1ea-c6008ad67b21"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030484534800052643, "Height": 0.012612359598279, "Left": 0.3716355860233307, "Top": 0.8436751961708069}, "Polygon": [{"X": 0.3717671036720276, "Y": 0.8436751961708069}, {"X": 0.40212011337280273, "Y": 0.8439549803733826}, {"X": 0.40198856592178345, "Y": 0.8562875390052795}, {"X": 0.3716355860233307, "Y": 0.8560072779655457}]}, "Id": "7bbacc84-cf33-4a76-bd87-9c9effb5780e"}, {"BlockType": "WORD", "Confidence": 99.4541244506836, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011077419854700565, "Height": 0.010289262048900127, "Left": 0.4061490595340729, "Top": 0.8437153697013855}, "Polygon": [{"X": 0.4062577188014984, "Y": 0.8437153697013855}, {"X": 0.4172264635562897, "Y": 0.8438164591789246}, {"X": 0.41711777448654175, "Y": 0.8540046215057373}, {"X": 0.4061490595340729, "Y": 0.8539034128189087}]}, "Id": "36ea0807-2759-4407-b2fd-329988546482"}, {"BlockType": "WORD", "Confidence": 86.84630584716797, "Text": "final.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03409630060195923, "Height": 0.01078824046999216, "Left": 0.4209948480129242, "Top": 0.8435080051422119}, "Polygon": [{"X": 0.42110657691955566, "Y": 0.8435080051422119}, {"X": 0.4550911486148834, "Y": 0.8438212871551514}, {"X": 0.4549793303012848, "Y": 0.8542962074279785}, {"X": 0.4209948480129242, "Y": 0.8539825677871704}]}, "Id": "c098de06-598d-44a9-a820-f24a9e1f8120"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Appeals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054037563502788544, "Height": 0.012833076529204845, "Left": 0.4594576358795166, "Top": 0.8447242379188538}, "Polygon": [{"X": 0.45958927273750305, "Y": 0.8447242379188538}, {"X": 0.5134952068328857, "Y": 0.845221221446991}, {"X": 0.5133634805679321, "Y": 0.8575572967529297}, {"X": 0.4594576358795166, "Y": 0.8570595979690552}]}, "Id": "771efe35-d620-4d86-a819-9d2d3ee5ad3e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02839997597038746, "Height": 0.010072487406432629, "Left": 0.5172193646430969, "Top": 0.8478480577468872}, "Polygon": [{"X": 0.5173240900039673, "Y": 0.8478480577468872}, {"X": 0.5456193089485168, "Y": 0.8481090068817139}, {"X": 0.5455145239830017, "Y": 0.8579205274581909}, {"X": 0.5172193646430969, "Y": 0.8576593399047852}]}, "Id": "e178b6e0-e590-4732-8698-40e210288b8d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015791261568665504, "Height": 0.010610707104206085, "Left": 0.5495914816856384, "Top": 0.8452958464622498}, "Polygon": [{"X": 0.5497032999992371, "Y": 0.8452958464622498}, {"X": 0.5653827786445618, "Y": 0.8454403877258301}, {"X": 0.5652709603309631, "Y": 0.8559065461158752}, {"X": 0.5495914816856384, "Y": 0.8557618260383606}]}, "Id": "48d9e67f-5a80-432d-bdcc-246771def8bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03556923195719719, "Height": 0.010194066911935806, "Left": 0.56939697265625, "Top": 0.8462517261505127}, "Polygon": [{"X": 0.5695024132728577, "Y": 0.8462517261505127}, {"X": 0.6049662232398987, "Y": 0.8465787172317505}, {"X": 0.6048607230186462, "Y": 0.856445848941803}, {"X": 0.56939697265625, "Y": 0.8561185002326965}]}, "Id": "e19dc30a-ea37-4f30-ae67-665773e1c194"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012010093778371811, "Height": 0.010112256743013859, "Left": 0.6095715761184692, "Top": 0.8465177416801453}, "Polygon": [{"X": 0.6096784472465515, "Y": 0.8465177416801453}, {"X": 0.6215816736221313, "Y": 0.8466275334358215}, {"X": 0.6214747428894043, "Y": 0.8566300272941589}, {"X": 0.6095715761184692, "Y": 0.8565201759338379}]}, "Id": "8ba91cc0-3f80-4e9c-b1b1-70b3215108a5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "writing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047492384910583496, "Height": 0.012954669073224068, "Left": 0.6261134147644043, "Top": 0.8467365503311157}, "Polygon": [{"X": 0.626247227191925, "Y": 0.8467365503311157}, {"X": 0.6736057996749878, "Y": 0.8471731543540955}, {"X": 0.6734718680381775, "Y": 0.8596912026405334}, {"X": 0.6261134147644043, "Y": 0.8592539429664612}]}, "Id": "8ce7f24a-de8e-40a5-9655-e9be0d7716f0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02372356690466404, "Height": 0.010105710476636887, "Left": 0.6775763034820557, "Top": 0.8475210070610046}, "Polygon": [{"X": 0.677682101726532, "Y": 0.8475210070610046}, {"X": 0.7012999057769775, "Y": 0.8477388024330139}, {"X": 0.7011941075325012, "Y": 0.857626736164093}, {"X": 0.6775763034820557, "Y": 0.8574087023735046}]}, "Id": "363e577e-ca66-470e-aa84-7e22140effc9"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "faxed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03649220988154411, "Height": 0.011003965511918068, "Left": 0.705803632736206, "Top": 0.8473777174949646}, "Polygon": [{"X": 0.7059177756309509, "Y": 0.8473777174949646}, {"X": 0.7422958612442017, "Y": 0.8477131724357605}, {"X": 0.742181658744812, "Y": 0.8583816885948181}, {"X": 0.705803632736206, "Y": 0.8580458760261536}]}, "Id": "5703b844-3740-40b2-ab56-e12e0cac7fda"}, {"BlockType": "WORD", "Confidence": 99.73394012451172, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012820391915738583, "Height": 0.008416089229285717, "Left": 0.7460632920265198, "Top": 0.850210964679718}, "Polygon": [{"X": 0.7461521029472351, "Y": 0.850210964679718}, {"X": 0.7588837146759033, "Y": 0.8503283858299255}, {"X": 0.7587948441505432, "Y": 0.8586270213127136}, {"X": 0.7460632920265198, "Y": 0.8585095405578613}]}, "Id": "67398a25-3603-4f2e-9515-022646b623e5"}, {"BlockType": "WORD", "Confidence": 65.96360778808594, "Text": "(616)669-7468", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.103658027946949, "Height": 0.013090929947793484, "Left": 0.7624471783638, "Top": 0.8483725190162659}, "Polygon": [{"X": 0.7625770568847656, "Y": 0.8483725190162659}, {"X": 0.8661051988601685, "Y": 0.8493270874023438}, {"X": 0.8659751415252686, "Y": 0.8614634275436401}, {"X": 0.7624471783638, "Y": 0.8605075478553772}]}, "Id": "4b33edf3-bd89-488c-a1d8-43ca8086a99a"}, {"BlockType": "WORD", "Confidence": 74.57380676269531, "Text": "71", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.048062749207019806, "Height": 0.03361378610134125, "Left": 0.09863157570362091, "Top": 0.8571683764457703}, "Polygon": [{"X": 0.09898432344198227, "Y": 0.8571683764457703}, {"X": 0.14669431746006012, "Y": 0.8576090335845947}, {"X": 0.14634139835834503, "Y": 0.8907821178436279}, {"X": 0.09863157570362091, "Y": 0.8903397917747498}]}, "Id": "d8b33573-9493-4da0-a817-a612b1882a54"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "db7cd359-e8aa-412b-a0ce-249241ff5d92", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "db7cd359-e8aa-412b-a0ce-249241ff5d92", "content-type": "application/x-amz-json-1.1", "content-length": "88408", "date": "Wed, 24 Sep 2025 12:42:42 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c3f5919c-b9fa-48be-9d23-c4850d73a743", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:42:45 GMT", "content-type": "application/json", "content-length": "654", "connection": "keep-alive", "x-amzn-requestid": "c3f5919c-b9fa-48be-9d23-c4850d73a743"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to classify. The content includes \"Truckload Check-in Form\" and \"Clear-to-Pay Voucher\". The page has \"Clear-to-Pay Voucher\" header. That suggests the type: clear_to_pay. The content includes load cost, deductions, manager approval. So doc_type clear_to_pay. page_no 1.\n\nReturn list with one object."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"clear_to_pay\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2776, "outputTokens": 97, "totalTokens": 2873}, "metrics": {"latencyMs": 614}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/ab820a23_Z7I9W27YVP7F6SPWV8UK.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n20011\nSaw 8785229\nFMBVB\nSAWYER NURSERY.\nTruckload Check-in Form\nClear-to-Pay Voucher\nLoad # 20511 AL\nBroker PE\nDate Processed 4/25/24\nLoad Cost $ 3451.00\nRacks Returned\n19\nSurcharge + $\n19 X$ $ 200 = $ 38.00\nDeductions - $\nRACKS\nAMOUNT\nTOTAL\nRacks\n+ $ 38.00\n(Rack checks will now be sent to the\nbroker.)\nNet Cost $ 3489.00\nBillable Shortages:\n(equipment missing with insufficient documentation\nRacks: ( ) X $250.00 = $\nTotal Deductions\nFor Load:\nLoad Locks: ( ) X $25.00 = $\n$\nPallet Jack: ( ) X $500.00 = $\nLoad Straps: ( ) X $25.00 = $\nPaperwork & other Fines $\n(see attached documentation)\nManager Approval:\nNotes:\nmy\nAny fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468\n71\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}