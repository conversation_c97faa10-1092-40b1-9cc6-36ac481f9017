{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "c930ddfb-2c8c-4faa-bb2f-69b7d0147e77", "Relationships": [{"Type": "CHILD", "Ids": ["04b15d2a-2277-4308-a3ba-f0a59cd6d566", "e8778c5f-3266-4d57-8415-e5297edc17bb", "608f1f8e-e896-4d6a-9e3d-b6909e4385fd", "6fe0c5e7-e6d5-4b55-8e36-b03b7c2133e6", "1a7417d2-1fac-4824-a84a-fa178fbca2a5", "2aa8df03-cfd8-4a63-bf19-14062256ea0a", "478c86c4-21c4-48ac-97eb-d56b68eeb694", "d2ab9c70-8f39-4393-8a08-0717c4ebd2b0", "5794dccd-b2a4-4b40-9ea9-27c1af35efd1", "b840acf8-8a77-479c-9cb4-55afe17d1377", "ed93ca65-f220-4457-97fb-d52e690c2bb7", "f505e89a-f594-4d25-87e0-89af9128532b", "88c076ba-4087-4ec0-adb0-64b4ff28e994", "8bf3af01-a834-45b7-bc89-2b91a04baad7", "278522c0-e360-48bc-8938-ccbe79a6ba61", "41761472-9847-484b-89ad-896b4bfc0660", "29501e00-a7a8-4de3-a709-83ab51717730", "8680da97-4b75-445b-880a-ae531ccfae1d", "95eeebac-5d28-47b6-aa64-b9028e073011", "dd5b7b58-fdd0-4ad9-a4d4-77a6eb077c4a", "fbe074ca-e1ba-47e9-bad5-282c1a2e93a0", "63f4bde3-46da-4c17-a4ae-3c7f1630b351", "80928997-857b-4239-8e1d-a5c8f141cfdc", "6a71176c-3f62-4b8d-943e-5e5580a7910e", "ee8c2a12-9a61-4933-989c-7d710eca0e4c", "d4a2e4bc-7b64-4cd0-8fd6-080bbc45a22c", "4def9332-1ca5-4c3b-96d6-27073e0a185b", "353ab2bd-87fc-4d5a-aed0-a70ecb17fbeb", "0b0e11f9-0103-4ecb-95e9-62d859dab81a", "069fee07-bae0-4d2e-9c73-a8b90d6050dc", "7e7dd52c-b998-4672-b012-6e15b9e963a4", "9d9d7ff8-b92b-43fb-9b22-ad8d2de6b6ec", "dcd9f606-fe00-4ee2-8714-90d947cc8c93", "e99face6-ef4f-436d-b3d1-57710544197e", "5368c02d-0ff8-4f87-b27e-a35baa146a53", "bf3e008b-d072-4ef0-832c-93c45ad807f2", "07d6b926-466b-4b65-becc-27261e67b4e5", "5a76c2ed-985f-45a3-a299-29b5f1b21327", "66118cce-7319-4f3b-9392-8793308b58b8"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "09:57", "Geometry": {"BoundingBox": {"Width": 0.11713004112243652, "Height": 0.015407799743115902, "Left": 0.11504612863063812, "Top": 0.020125476643443108}, "Polygon": [{"X": 0.11504612863063812, "Y": 0.020125722512602806}, {"X": 0.23217593133449554, "Y": 0.020125476643443108}, {"X": 0.23217616975307465, "Y": 0.03553306683897972}, {"X": 0.11504639685153961, "Y": 0.035533275455236435}]}, "Id": "04b15d2a-2277-4308-a3ba-f0a59cd6d566", "Relationships": [{"Type": "CHILD", "Ids": ["953ec332-fd92-40c0-a313-d1e67f68c5c8"]}]}, {"BlockType": "LINE", "Confidence": 86.41920471191406, "Text": "5Gc", "Geometry": {"BoundingBox": {"Width": 0.05371016263961792, "Height": 0.012664314359426498, "Left": 0.7862324118614197, "Top": 0.02172193117439747}, "Polygon": [{"X": 0.7862324118614197, "Y": 0.021722042933106422}, {"X": 0.8399425148963928, "Y": 0.02172193117439747}, {"X": 0.8399425745010376, "Y": 0.03438614681363106}, {"X": 0.7862324714660645, "Y": 0.03438624367117882}]}, "Id": "e8778c5f-3266-4d57-8415-e5297edc17bb", "Relationships": [{"Type": "CHILD", "Ids": ["8278b446-6427-4ae3-8522-3922f0ca8342"]}]}, {"BlockType": "LINE", "Confidence": 99.55974578857422, "Text": "94", "Geometry": {"BoundingBox": {"Width": 0.04216840863227844, "Height": 0.011626769788563251, "Left": 0.8645285367965698, "Top": 0.02136772871017456}, "Polygon": [{"X": 0.8645285367965698, "Y": 0.021367816254496574}, {"X": 0.9066969156265259, "Y": 0.02136772871017456}, {"X": 0.9066969752311707, "Y": 0.03299441933631897}, {"X": 0.8645285964012146, "Y": 0.03299449756741524}]}, "Id": "608f1f8e-e896-4d6a-9e3d-b6909e4385fd", "Relationships": [{"Type": "CHILD", "Ids": ["1878b658-c42b-44e3-9f1e-e7e47c9f7251"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Done", "Geometry": {"BoundingBox": {"Width": 0.09560747444629669, "Height": 0.01437131967395544, "Left": 0.048845116049051285, "Top": 0.06749148666858673}, "Polygon": [{"X": 0.048845116049051285, "Y": 0.06749159097671509}, {"X": 0.14445234835147858, "Y": 0.06749148666858673}, {"X": 0.14445258677005768, "Y": 0.08186273276805878}, {"X": 0.04884537681937218, "Y": 0.08186280727386475}]}, "Id": "6fe0c5e7-e6d5-4b55-8e36-b03b7c2133e6", "Relationships": [{"Type": "CHILD", "Ids": ["403e3491-f4f9-421c-bef2-4d05b173537a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Retake", "Geometry": {"BoundingBox": {"Width": 0.11854176968336105, "Height": 0.014670895412564278, "Left": 0.8341136574745178, "Top": 0.06713664531707764}, "Polygon": [{"X": 0.8341136574745178, "Y": 0.06713677942752838}, {"X": 0.9526553750038147, "Y": 0.06713664531707764}, {"X": 0.9526554346084595, "Y": 0.0818074494600296}, {"X": 0.8341137766838074, "Y": 0.08180754631757736}]}, "Id": "1a7417d2-1fac-4824-a84a-fa178fbca2a5", "Relationships": [{"Type": "CHILD", "Ids": ["40fb6800-be6d-4c53-b5aa-01cb1afbd0a2"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "DATE: 24-Feb", "Geometry": {"BoundingBox": {"Width": 0.13439719378948212, "Height": 0.007935116067528725, "Left": 0.2616932988166809, "Top": 0.26063257455825806}, "Polygon": [{"X": 0.2616932988166809, "Y": 0.26063257455825806}, {"X": 0.39609038829803467, "Y": 0.2606329619884491}, {"X": 0.3960905075073242, "Y": 0.26856768131256104}, {"X": 0.26169341802597046, "Y": 0.2685672640800476}]}, "Id": "2aa8df03-cfd8-4a63-bf19-14062256ea0a", "Relationships": [{"Type": "CHILD", "Ids": ["a3b4d15a-88ad-4e70-83de-24b38abbb988", "4df23474-f700-47c0-96ac-16325fa54b92"]}]}, {"BlockType": "LINE", "Confidence": 99.7132797241211, "Text": "UNIT #: 92644/283420", "Geometry": {"BoundingBox": {"Width": 0.21596987545490265, "Height": 0.00892727356404066, "Left": 0.2449866235256195, "Top": 0.28213176131248474}, "Polygon": [{"X": 0.2449866235256195, "Y": 0.28213176131248474}, {"X": 0.4609563946723938, "Y": 0.28213247656822205}, {"X": 0.46095648407936096, "Y": 0.2910590171813965}, {"X": 0.24498675763607025, "Y": 0.2910582423210144}]}, "Id": "478c86c4-21c4-48ac-97eb-d56b68eeb694", "Relationships": [{"Type": "CHILD", "Ids": ["3c8a12ec-7520-4be7-8fc1-7f3a0f92271d", "47056356-273e-4dbd-a441-7689fd68a451", "3e8d3828-db32-46ee-9e8f-abd3d9320da6"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "Truck Ready Time: 5:00", "Geometry": {"BoundingBox": {"Width": 0.2777754068374634, "Height": 0.011000653728842735, "Left": 0.46594560146331787, "Top": 0.29109087586402893}, "Polygon": [{"X": 0.46594560146331787, "Y": 0.29109087586402893}, {"X": 0.7437209486961365, "Y": 0.2910918593406677}, {"X": 0.7437210083007812, "Y": 0.302091509103775}, {"X": 0.4659457206726074, "Y": 0.30209043622016907}]}, "Id": "d2ab9c70-8f39-4393-8a08-0717c4ebd2b0", "Relationships": [{"Type": "CHILD", "Ids": ["37d90733-9f1c-4e03-ae41-d4423a4c6963", "aaaa3577-0b26-4814-9e27-6c4bd5c280f3", "e2f4c7ad-f3e0-4e71-8500-4c04c79b09ef", "bbf9871c-8374-4c16-95d4-c6a4567a2bba"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.16027680039405823, "Height": 0.007915043272078037, "Left": 0.23588930070400238, "Top": 0.3040817677974701}, "Polygon": [{"X": 0.23588930070400238, "Y": 0.3040817677974701}, {"X": 0.39616599678993225, "Y": 0.30408239364624023}, {"X": 0.3961661159992218, "Y": 0.31199681758880615}, {"X": 0.23588943481445312, "Y": 0.3119961619377136}]}, "Id": "5794dccd-b2a4-4b40-9ea9-27c1af35efd1", "Relationships": [{"Type": "CHILD", "Ids": ["b7a55106-c979-45c8-b63b-40905b43279f", "e93767e1-f9b5-4501-968a-4174f596a8ca"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.1443394422531128, "Height": 0.009371564723551273, "Left": 0.4660872220993042, "Top": 0.31439220905303955}, "Polygon": [{"X": 0.4660872220993042, "Y": 0.31439220905303955}, {"X": 0.6104265451431274, "Y": 0.3143928050994873}, {"X": 0.610426664352417, "Y": 0.32376375794410706}, {"X": 0.46608734130859375, "Y": 0.3237631320953369}]}, "Id": "b840acf8-8a77-479c-9cb4-55afe17d1377", "Relationships": [{"Type": "CHILD", "Ids": ["fdf5b950-68c1-40fa-bde5-a80af8591ecb", "ac59a447-8f37-4060-8a5a-aaed26c4e007"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "5:00", "Geometry": {"BoundingBox": {"Width": 0.07429980486631393, "Height": 0.00934901274740696, "Left": 0.6597591042518616, "Top": 0.3140066862106323}, "Polygon": [{"X": 0.6597591042518616, "Y": 0.3140066862106323}, {"X": 0.7340588569641113, "Y": 0.3140069842338562}, {"X": 0.7340589165687561, "Y": 0.32335570454597473}, {"X": 0.6597592234611511, "Y": 0.32335537672042847}]}, "Id": "ed93ca65-f220-4457-97fb-d52e690c2bb7", "Relationships": [{"Type": "CHILD", "Ids": ["a433cc71-9a70-4628-8913-1740ab78d3cd"]}]}, {"BlockType": "LINE", "Confidence": 99.91429901123047, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.22560490667819977, "Height": 0.0084666283801198, "Left": 0.14832307398319244, "Top": 0.3255603015422821}, "Polygon": [{"X": 0.14832307398319244, "Y": 0.3255603015422821}, {"X": 0.37392786145210266, "Y": 0.3255612850189209}, {"X": 0.3739279806613922, "Y": 0.33402690291404724}, {"X": 0.14832322299480438, "Y": 0.33402588963508606}]}, "Id": "f505e89a-f594-4d25-87e0-89af9128532b", "Relationships": [{"Type": "CHILD", "Ids": ["dbc0e182-1a86-402c-ab09-f1c7ad7cc2c1", "a3ee2a79-83b6-46c8-b99b-398a089a29f6", "9af28629-a42e-4091-9655-9c29de01b1af"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "PICK", "Geometry": {"BoundingBox": {"Width": 0.040713440626859665, "Height": 0.006804634816944599, "Left": 0.34796833992004395, "Top": 0.34465473890304565}, "Polygon": [{"X": 0.34796833992004395, "Y": 0.34465473890304565}, {"X": 0.38868170976638794, "Y": 0.34465491771698}, {"X": 0.3886817991733551, "Y": 0.3514593839645386}, {"X": 0.3479684591293335, "Y": 0.35145917534828186}]}, "Id": "88c076ba-4087-4ec0-adb0-64b4ff28e994", "Relationships": [{"Type": "CHILD", "Ids": ["7dad7140-279d-4862-9a53-6c7ec3f89ba0"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.09310941398143768, "Height": 0.007601017132401466, "Left": 0.8243534564971924, "Top": 0.34984537959098816}, "Polygon": [{"X": 0.8243534564971924, "Y": 0.34984537959098816}, {"X": 0.9174628257751465, "Y": 0.349845826625824}, {"X": 0.9174628853797913, "Y": 0.3574464023113251}, {"X": 0.8243535161018372, "Y": 0.35744592547416687}]}, "Id": "8bf3af01-a834-45b7-bc89-2b91a04baad7", "Relationships": [{"Type": "CHILD", "Ids": ["54335183-85b9-4888-b30f-4751b57e7727", "a6613ff9-1ad6-4a6b-a84d-44adddbfdb7d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.098092220723629, "Height": 0.007674383465200663, "Left": 0.17871658504009247, "Top": 0.35688072443008423}, "Polygon": [{"X": 0.17871658504009247, "Y": 0.35688072443008423}, {"X": 0.2768086791038513, "Y": 0.35688120126724243}, {"X": 0.27680879831314087, "Y": 0.36455509066581726}, {"X": 0.17871670424938202, "Y": 0.36455458402633667}]}, "Id": "278522c0-e360-48bc-8938-ccbe79a6ba61", "Relationships": [{"Type": "CHILD", "Ids": ["67b97dbf-13e4-4e3a-8eca-93bdb7d4c383"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.023527460172772408, "Height": 0.007326602004468441, "Left": 0.3566812574863434, "Top": 0.35591238737106323}, "Polygon": [{"X": 0.3566812574863434, "Y": 0.35591238737106323}, {"X": 0.3802086114883423, "Y": 0.3559125065803528}, {"X": 0.38020873069763184, "Y": 0.36323899030685425}, {"X": 0.35668134689331055, "Y": 0.3632388710975647}]}, "Id": "41761472-9847-484b-89ad-896b4bfc0660", "Relationships": [{"Type": "CHILD", "Ids": ["1092a458-5328-4595-9f6a-fdc88c2502eb"]}]}, {"BlockType": "LINE", "Confidence": 99.72098541259766, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.03430702164769173, "Height": 0.007433968596160412, "Left": 0.4145145118236542, "Top": 0.3556877374649048}, "Polygon": [{"X": 0.4145145118236542, "Y": 0.3556877374649048}, {"X": 0.44882145524024963, "Y": 0.3556878864765167}, {"X": 0.4488215446472168, "Y": 0.3631216883659363}, {"X": 0.4145146310329437, "Y": 0.36312150955200195}]}, "Id": "29501e00-a7a8-4de3-a709-83ab51717730", "Relationships": [{"Type": "CHILD", "Ids": ["039d161a-cd70-4e92-906a-f8a74a7fa910"]}]}, {"BlockType": "LINE", "Confidence": 99.6875, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.14092165231704712, "Height": 0.007967273704707623, "Left": 0.5665446519851685, "Top": 0.3553646504878998}, "Polygon": [{"X": 0.5665446519851685, "Y": 0.3553646504878998}, {"X": 0.7074662446975708, "Y": 0.3553653359413147}, {"X": 0.7074663043022156, "Y": 0.3633319139480591}, {"X": 0.5665447115898132, "Y": 0.3633311986923218}]}, "Id": "8680da97-4b75-445b-880a-ae531ccfae1d", "Relationships": [{"Type": "CHILD", "Ids": ["3a3310aa-fcf7-4ca2-bdd6-7577e4b31280", "5a65f545-c8f2-43ba-ad32-1be577376b39", "f8239708-f84b-4fb0-b603-f89f05d34f58"]}]}, {"BlockType": "LINE", "Confidence": 67.92729187011719, "Text": "x", "Geometry": {"BoundingBox": {"Width": 0.03763885423541069, "Height": 0.024331852793693542, "Left": 0.07340550422668457, "Top": 0.37663552165031433}, "Polygon": [{"X": 0.07340550422668457, "Y": 0.37663552165031433}, {"X": 0.11104393005371094, "Y": 0.37663573026657104}, {"X": 0.11104435473680496, "Y": 0.40096738934516907}, {"X": 0.07340593636035919, "Y": 0.40096715092658997}]}, "Id": "95eeebac-5d28-47b6-aa64-b9028e073011", "Relationships": [{"Type": "CHILD", "Ids": ["e44786c2-a23a-450a-9a18-10f6d35e4d0a"]}]}, {"BlockType": "LINE", "Confidence": 79.84786987304688, "Text": "I small Sk", "Geometry": {"BoundingBox": {"Width": 0.11732590943574905, "Height": 0.011845052242279053, "Left": 0.1811721920967102, "Top": 0.37889063358306885}, "Polygon": [{"X": 0.1811721920967102, "Y": 0.37889063358306885}, {"X": 0.29849791526794434, "Y": 0.3788912892341614}, {"X": 0.29849809408187866, "Y": 0.3907356858253479}, {"X": 0.18117238581180573, "Y": 0.390735000371933}]}, "Id": "dd5b7b58-fdd0-4ad9-a4d4-77a6eb077c4a", "Relationships": [{"Type": "CHILD", "Ids": ["1b444c39-dacf-42fc-a601-a68925f62c21", "65a90883-11ca-4ace-9fa4-e521fc4b10e5", "824f26e8-ed45-431e-b1c6-46e53243c3b1"]}]}, {"BlockType": "LINE", "Confidence": 97.76945495605469, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.04892664775252342, "Height": 0.012097621336579323, "Left": 0.4636434316635132, "Top": 0.3771483600139618}, "Polygon": [{"X": 0.4636434316635132, "Y": 0.3771483600139618}, {"X": 0.5125699639320374, "Y": 0.3771486282348633}, {"X": 0.5125700831413269, "Y": 0.38924598693847656}, {"X": 0.4636435806751251, "Y": 0.3892456889152527}]}, "Id": "fbe074ca-e1ba-47e9-bad5-282c1a2e93a0", "Relationships": [{"Type": "CHILD", "Ids": ["db54503a-7788-49cd-8718-f07160f2b5f9"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "0705", "Geometry": {"BoundingBox": {"Width": 0.0678027868270874, "Height": 0.011791517026722431, "Left": 0.8269667625427246, "Top": 0.3760887086391449}, "Polygon": [{"X": 0.8269667625427246, "Y": 0.3760887086391449}, {"X": 0.8947694897651672, "Y": 0.37608906626701355}, {"X": 0.894769549369812, "Y": 0.38788020610809326}, {"X": 0.8269668221473694, "Y": 0.3878798186779022}]}, "Id": "63f4bde3-46da-4c17-a4ae-3c7f1630b351", "Relationships": [{"Type": "CHILD", "Ids": ["59822593-0d32-4932-a415-48b49af03222"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.15297523140907288, "Height": 0.009664046578109264, "Left": 0.12229514122009277, "Top": 0.3942706882953644}, "Polygon": [{"X": 0.12229514122009277, "Y": 0.3942706882953644}, {"X": 0.2752702236175537, "Y": 0.394271582365036}, {"X": 0.27527037262916565, "Y": 0.4039347469806671}, {"X": 0.1222953051328659, "Y": 0.4039338231086731}]}, "Id": "80928997-857b-4239-8e1d-a5c8f141cfdc", "Relationships": [{"Type": "CHILD", "Ids": ["134f085d-16fb-4651-9b85-2957d1c6b75c", "4d22d708-a00e-4f6e-bd41-8b0d0482156f"]}]}, {"BlockType": "LINE", "Confidence": 89.28551483154297, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010949739255011082, "Height": 0.0059378803707659245, "Left": 0.36353549361228943, "Top": 0.39598196744918823}, "Polygon": [{"X": 0.36353549361228943, "Y": 0.39598196744918823}, {"X": 0.3744851350784302, "Y": 0.395982027053833}, {"X": 0.37448522448539734, "Y": 0.4019198417663574}, {"X": 0.3635355532169342, "Y": 0.40191978216171265}]}, "Id": "6a71176c-3f62-4b8d-943e-5e5580a7910e", "Relationships": [{"Type": "CHILD", "Ids": ["69910941-09ed-44ba-a7d2-f625d38199f5"]}]}, {"BlockType": "LINE", "Confidence": 91.65596771240234, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011345498263835907, "Height": 0.006103744730353355, "Left": 0.4261852204799652, "Top": 0.395738810300827}, "Polygon": [{"X": 0.4261852204799652, "Y": 0.395738810300827}, {"X": 0.43753066658973694, "Y": 0.3957388699054718}, {"X": 0.4375307261943817, "Y": 0.40184256434440613}, {"X": 0.4261853098869324, "Y": 0.40184247493743896}]}, "Id": "ee8c2a12-9a61-4933-989c-7d710eca0e4c", "Relationships": [{"Type": "CHILD", "Ids": ["21f8dd90-5d5b-4f7e-83c5-9f0a4b00f84d"]}]}, {"BlockType": "LINE", "Confidence": 99.37519836425781, "Text": "*********** ***********", "Geometry": {"BoundingBox": {"Width": 0.19458195567131042, "Height": 0.007458490785211325, "Left": 0.4639710485935211, "Top": 0.39384007453918457}, "Polygon": [{"X": 0.4639710485935211, "Y": 0.39384007453918457}, {"X": 0.6585529446601868, "Y": 0.3938412070274353}, {"X": 0.6585530042648315, "Y": 0.4012985825538635}, {"X": 0.4639711380004883, "Y": 0.4012974202632904}]}, "Id": "d4a2e4bc-7b64-4cd0-8fd6-080bbc45a22c", "Relationships": [{"Type": "CHILD", "Ids": ["ec0efeb2-9c32-4566-85e6-e9b9408633a1", "c2b5c07d-b6c6-4095-b847-38c9122205df"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "0710", "Geometry": {"BoundingBox": {"Width": 0.0725608617067337, "Height": 0.00999318715184927, "Left": 0.8231115937232971, "Top": 0.40375179052352905}, "Polygon": [{"X": 0.8231115937232971, "Y": 0.40375179052352905}, {"X": 0.8956723809242249, "Y": 0.4037522077560425}, {"X": 0.8956724405288696, "Y": 0.4137449562549591}, {"X": 0.8231116533279419, "Y": 0.4137445092201233}]}, "Id": "4def9332-1ca5-4c3b-96d6-27073e0a185b", "Relationships": [{"Type": "CHILD", "Ids": ["56216135-8c8a-42be-b0f0-14b4bd8650f9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0755", "Geometry": {"BoundingBox": {"Width": 0.06733584403991699, "Height": 0.012236219830811024, "Left": 0.8282186985015869, "Top": 0.4362069368362427}, "Polygon": [{"X": 0.8282186985015869, "Y": 0.4362069368362427}, {"X": 0.8955544829368591, "Y": 0.4362073838710785}, {"X": 0.8955545425415039, "Y": 0.44844314455986023}, {"X": 0.8282188177108765, "Y": 0.4484426975250244}]}, "Id": "353ab2bd-87fc-4d5a-aed0-a70ecb17fbeb", "Relationships": [{"Type": "CHILD", "Ids": ["843da571-db64-4bc2-896b-95dfa1376b49"]}]}, {"BlockType": "LINE", "Confidence": 69.3919448852539, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.05929342284798622, "Height": 0.045260827988386154, "Left": 0.0594523586332798, "Top": 0.4380947947502136}, "Polygon": [{"X": 0.0594523586332798, "Y": 0.4380947947502136}, {"X": 0.11874500662088394, "Y": 0.43809518218040466}, {"X": 0.11874578148126602, "Y": 0.4833556115627289}, {"X": 0.05945317819714546, "Y": 0.48335516452789307}]}, "Id": "0b0e11f9-0103-4ecb-95e9-62d859dab81a", "Relationships": [{"Type": "CHILD", "Ids": ["d78ae9e4-a512-4525-89cf-1f9ed06015f9"]}]}, {"BlockType": "LINE", "Confidence": 89.5838623046875, "Text": "3sk", "Geometry": {"BoundingBox": {"Width": 0.056661881506443024, "Height": 0.011778688058257103, "Left": 0.15157723426818848, "Top": 0.4495817720890045}, "Polygon": [{"X": 0.15157723426818848, "Y": 0.4495817720890045}, {"X": 0.20823892951011658, "Y": 0.44958215951919556}, {"X": 0.2082391083240509, "Y": 0.46136045455932617}, {"X": 0.151577427983284, "Y": 0.46136003732681274}]}, "Id": "069fee07-bae0-4d2e-9c73-a8b90d6050dc", "Relationships": [{"Type": "CHILD", "Ids": ["e636b78e-1e89-4b7c-a8b1-3a7786cc40e3"]}]}, {"BlockType": "LINE", "Confidence": 88.65214538574219, "Text": "2sk", "Geometry": {"BoundingBox": {"Width": 0.059546131640672684, "Height": 0.012924063950777054, "Left": 0.4554550051689148, "Top": 0.44218820333480835}, "Polygon": [{"X": 0.4554550051689148, "Y": 0.44218820333480835}, {"X": 0.5150009989738464, "Y": 0.4421885907649994}, {"X": 0.515001118183136, "Y": 0.4551122486591339}, {"X": 0.45545515418052673, "Y": 0.4551118314266205}]}, "Id": "7e7dd52c-b998-4672-b012-6e15b9e963a4", "Relationships": [{"Type": "CHILD", "Ids": ["9aa98905-ecba-4dc6-abb3-b81eb26bd259"]}]}, {"BlockType": "LINE", "Confidence": 99.9216079711914, "Text": "Automation & Modular", "Geometry": {"BoundingBox": {"Width": 0.19269391894340515, "Height": 0.007872416637837887, "Left": 0.12197329103946686, "Top": 0.46106934547424316}, "Polygon": [{"X": 0.12197329103946686, "Y": 0.46106934547424316}, {"X": 0.31466710567474365, "Y": 0.4610707461833954}, {"X": 0.3146671950817108, "Y": 0.4689417779445648}, {"X": 0.1219734251499176, "Y": 0.4689403474330902}]}, "Id": "9d9d7ff8-b92b-43fb-9b22-ad8d2de6b6ec", "Relationships": [{"Type": "CHILD", "Ids": ["32a4b6fa-3203-4b5e-9a61-2eb777541e1d", "a911bab7-b202-4274-96a1-752d4798770c", "5e6be3ea-611b-4169-b518-29e5155d79ca"]}]}, {"BlockType": "LINE", "Confidence": 87.73197937011719, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010778182186186314, "Height": 0.005908254999667406, "Left": 0.364133358001709, "Top": 0.46260178089141846}, "Polygon": [{"X": 0.364133358001709, "Y": 0.46260178089141846}, {"X": 0.37491145730018616, "Y": 0.46260184049606323}, {"X": 0.37491151690483093, "Y": 0.4685100317001343}, {"X": 0.36413341760635376, "Y": 0.4685099422931671}]}, "Id": "dcd9f606-fe00-4ee2-8714-90d947cc8c93", "Relationships": [{"Type": "CHILD", "Ids": ["6c17445c-3a80-48a3-8756-c2b7478caea5"]}]}, {"BlockType": "LINE", "Confidence": 85.74797058105469, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011137685738503933, "Height": 0.005786150228232145, "Left": 0.42676064372062683, "Top": 0.46268731355667114}, "Polygon": [{"X": 0.42676064372062683, "Y": 0.46268731355667114}, {"X": 0.43789827823638916, "Y": 0.4626874029636383}, {"X": 0.43789833784103394, "Y": 0.4684734642505646}, {"X": 0.426760733127594, "Y": 0.4684734046459198}]}, "Id": "e99face6-ef4f-436d-b3d1-57710544197e", "Relationships": [{"Type": "CHILD", "Ids": ["12bd9e10-0b86-4bef-892b-d44127141c26"]}]}, {"BlockType": "LINE", "Confidence": 99.08482360839844, "Text": "SO-2116134", "Geometry": {"BoundingBox": {"Width": 0.08608701080083847, "Height": 0.006733916234225035, "Left": 0.46426793932914734, "Top": 0.4611221253871918}, "Polygon": [{"X": 0.46426793932914734, "Y": 0.4611221253871918}, {"X": 0.5503548979759216, "Y": 0.4611227512359619}, {"X": 0.5503549575805664, "Y": 0.4678560197353363}, {"X": 0.4642680287361145, "Y": 0.46785539388656616}]}, "Id": "5368c02d-0ff8-4f87-b27e-a35baa146a53", "Relationships": [{"Type": "CHILD", "Ids": ["d56af2de-2c22-40a2-9707-0daa40c3ea91"]}]}, {"BlockType": "LINE", "Confidence": 99.5655288696289, "Text": "2116736", "Geometry": {"BoundingBox": {"Width": 0.14135520160198212, "Height": 0.01314903236925602, "Left": 0.5804247856140137, "Top": 0.4552437663078308}, "Polygon": [{"X": 0.5804247856140137, "Y": 0.4552437663078308}, {"X": 0.7217798829078674, "Y": 0.455244779586792}, {"X": 0.721780002117157, "Y": 0.4683927893638611}, {"X": 0.580424964427948, "Y": 0.4683917462825775}]}, "Id": "bf3e008b-d072-4ef0-832c-93c45ad807f2", "Relationships": [{"Type": "CHILD", "Ids": ["2461af82-6213-4a80-acee-32676d82b251"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0815", "Geometry": {"BoundingBox": {"Width": 0.05711528658866882, "Height": 0.010189207270741463, "Left": 0.8299935460090637, "Top": 0.47014498710632324}, "Polygon": [{"X": 0.8299935460090637, "Y": 0.47014498710632324}, {"X": 0.8871087431907654, "Y": 0.47014540433883667}, {"X": 0.8871088027954102, "Y": 0.48033419251441956}, {"X": 0.8299936056137085, "Y": 0.48033374547958374}]}, "Id": "07d6b926-466b-4b65-becc-27261e67b4e5", "Relationships": [{"Type": "CHILD", "Ids": ["8cc8a0f4-cd6f-437e-a2d4-95fe4e7a7258"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "Detroit", "Geometry": {"BoundingBox": {"Width": 0.05822712928056717, "Height": 0.007702106609940529, "Left": 0.48029184341430664, "Top": 0.7640868425369263}, "Polygon": [{"X": 0.48029184341430664, "Y": 0.7640868425369263}, {"X": 0.5385189056396484, "Y": 0.7640876173973083}, {"X": 0.5385189652442932, "Y": 0.7717889547348022}, {"X": 0.4802919328212738, "Y": 0.7717881202697754}]}, "Id": "5a76c2ed-985f-45a3-a299-29b5f1b21327", "Relationships": [{"Type": "CHILD", "Ids": ["13dc07f7-607a-4f96-b483-bec20a802e16"]}]}, {"BlockType": "LINE", "Confidence": 85.14867401123047, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.055564768612384796, "Height": 0.02554180473089218, "Left": 0.05213910713791847, "Top": 0.9272449016571045}, "Polygon": [{"X": 0.05213910713791847, "Y": 0.9272449016571045}, {"X": 0.10770343244075775, "Y": 0.9272458553314209}, {"X": 0.10770387947559357, "Y": 0.9527866840362549}, {"X": 0.05213957279920578, "Y": 0.9527857303619385}]}, "Id": "66118cce-7319-4f3b-9392-8793308b58b8", "Relationships": [{"Type": "CHILD", "Ids": ["fb3bc905-4a3d-43cd-b79f-3f440807efb5"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "09:57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11713004112243652, "Height": 0.015407799743115902, "Left": 0.11504612863063812, "Top": 0.020125476643443108}, "Polygon": [{"X": 0.11504612863063812, "Y": 0.020125722512602806}, {"X": 0.23217593133449554, "Y": 0.020125476643443108}, {"X": 0.23217616975307465, "Y": 0.03553306683897972}, {"X": 0.11504639685153961, "Y": 0.035533275455236435}]}, "Id": "953ec332-fd92-40c0-a313-d1e67f68c5c8"}, {"BlockType": "WORD", "Confidence": 86.41920471191406, "Text": "5Gc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05371016263961792, "Height": 0.012664314359426498, "Left": 0.7862324118614197, "Top": 0.02172193117439747}, "Polygon": [{"X": 0.7862324118614197, "Y": 0.021722042933106422}, {"X": 0.8399425148963928, "Y": 0.02172193117439747}, {"X": 0.8399425745010376, "Y": 0.03438614681363106}, {"X": 0.7862324714660645, "Y": 0.03438624367117882}]}, "Id": "8278b446-6427-4ae3-8522-3922f0ca8342"}, {"BlockType": "WORD", "Confidence": 99.55974578857422, "Text": "94", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04216840863227844, "Height": 0.011626769788563251, "Left": 0.8645285367965698, "Top": 0.02136772871017456}, "Polygon": [{"X": 0.8645285367965698, "Y": 0.021367816254496574}, {"X": 0.9066969156265259, "Y": 0.02136772871017456}, {"X": 0.9066969752311707, "Y": 0.03299441933631897}, {"X": 0.8645285964012146, "Y": 0.03299449756741524}]}, "Id": "1878b658-c42b-44e3-9f1e-e7e47c9f7251"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Done", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09560747444629669, "Height": 0.01437131967395544, "Left": 0.048845116049051285, "Top": 0.06749148666858673}, "Polygon": [{"X": 0.048845116049051285, "Y": 0.06749159097671509}, {"X": 0.14445234835147858, "Y": 0.06749148666858673}, {"X": 0.14445258677005768, "Y": 0.08186273276805878}, {"X": 0.04884537681937218, "Y": 0.08186280727386475}]}, "Id": "403e3491-f4f9-421c-bef2-4d05b173537a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Retake", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11854176968336105, "Height": 0.014670895412564278, "Left": 0.8341136574745178, "Top": 0.06713664531707764}, "Polygon": [{"X": 0.8341136574745178, "Y": 0.06713677942752838}, {"X": 0.9526553750038147, "Y": 0.06713664531707764}, {"X": 0.9526554346084595, "Y": 0.0818074494600296}, {"X": 0.8341137766838074, "Y": 0.08180754631757736}]}, "Id": "40fb6800-be6d-4c53-b5aa-01cb1afbd0a2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053708065301179886, "Height": 0.0074724433943629265, "Left": 0.2616932988166809, "Top": 0.26109498739242554}, "Polygon": [{"X": 0.2616932988166809, "Y": 0.26109498739242554}, {"X": 0.31540125608444214, "Y": 0.2610951364040375}, {"X": 0.3154013752937317, "Y": 0.26856744289398193}, {"X": 0.26169341802597046, "Y": 0.2685672640800476}]}, "Id": "a3b4d15a-88ad-4e70-83de-24b38abbb988"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "24-Feb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058785051107406616, "Height": 0.007875480689108372, "Left": 0.3373054265975952, "Top": 0.26063278317451477}, "Polygon": [{"X": 0.3373054265975952, "Y": 0.26063278317451477}, {"X": 0.39609038829803467, "Y": 0.2606329619884491}, {"X": 0.39609047770500183, "Y": 0.26850828528404236}, {"X": 0.33730554580688477, "Y": 0.26850807666778564}]}, "Id": "4df23474-f700-47c0-96ac-16325fa54b92"}, {"BlockType": "WORD", "Confidence": 99.53044891357422, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046839639544487, "Height": 0.007610750384628773, "Left": 0.2449866235256195, "Top": 0.2827391028404236}, "Polygon": [{"X": 0.2449866235256195, "Y": 0.2827391028404236}, {"X": 0.29182615876197815, "Y": 0.2827392816543579}, {"X": 0.2918262779712677, "Y": 0.2903498709201813}, {"X": 0.24498674273490906, "Y": 0.29034969210624695}]}, "Id": "3c8a12ec-7520-4be7-8fc1-7f3a0f92271d"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019844060763716698, "Height": 0.007605979219079018, "Left": 0.29551127552986145, "Top": 0.282666951417923}, "Polygon": [{"X": 0.29551127552986145, "Y": 0.282666951417923}, {"X": 0.31535521149635315, "Y": 0.28266704082489014}, {"X": 0.3153553307056427, "Y": 0.29027295112609863}, {"X": 0.2955113649368286, "Y": 0.29027286171913147}]}, "Id": "47056356-273e-4dbd-a441-7689fd68a451"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "92644/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12454775720834732, "Height": 0.00892695877701044, "Left": 0.33640873432159424, "Top": 0.2821320593357086}, "Polygon": [{"X": 0.33640873432159424, "Y": 0.2821320593357086}, {"X": 0.4609563946723938, "Y": 0.28213247656822205}, {"X": 0.46095648407936096, "Y": 0.2910590171813965}, {"X": 0.3364088535308838, "Y": 0.29105857014656067}]}, "Id": "3e8d3828-db32-46ee-9e8f-abd3d9320da6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04772192984819412, "Height": 0.007759837433695793, "Left": 0.46594563126564026, "Top": 0.2926507890224457}, "Polygon": [{"X": 0.46594563126564026, "Y": 0.2926507890224457}, {"X": 0.5136674642562866, "Y": 0.29265096783638}, {"X": 0.5136675238609314, "Y": 0.3004106283187866}, {"X": 0.4659457206726074, "Y": 0.3004104495048523}]}, "Id": "37d90733-9f1c-4e03-ae41-d4423a4c6963"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05483131483197212, "Height": 0.009648546576499939, "Left": 0.5178360939025879, "Top": 0.29244232177734375}, "Polygon": [{"X": 0.5178360939025879, "Y": 0.29244232177734375}, {"X": 0.5726673007011414, "Y": 0.2924425005912781}, {"X": 0.5726674199104309, "Y": 0.3020908534526825}, {"X": 0.5178362131118774, "Y": 0.3020906448364258}]}, "Id": "aaaa3577-0b26-4814-9e27-6c4bd5c280f3"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04734331741929054, "Height": 0.0076330783776938915, "Left": 0.5779169797897339, "Top": 0.2926827073097229}, "Polygon": [{"X": 0.5779169797897339, "Y": 0.2926827073097229}, {"X": 0.6252602338790894, "Y": 0.2926828861236572}, {"X": 0.6252602934837341, "Y": 0.300315797328949}, {"X": 0.5779170989990234, "Y": 0.30031561851501465}]}, "Id": "e2f4c7ad-f3e0-4e71-8500-4c04c79b09ef"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0667114108800888, "Height": 0.010374313220381737, "Left": 0.6770095825195312, "Top": 0.2910916209220886}, "Polygon": [{"X": 0.6770095825195312, "Y": 0.2910916209220886}, {"X": 0.7437209486961365, "Y": 0.2910918593406677}, {"X": 0.7437210083007812, "Y": 0.3014659583568573}, {"X": 0.6770097017288208, "Y": 0.3014656901359558}]}, "Id": "bbf9871c-8374-4c16-95d4-c6a4567a2bba"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07398182898759842, "Height": 0.007552543189376593, "Left": 0.23588931560516357, "Top": 0.304443895816803}, "Polygon": [{"X": 0.23588931560516357, "Y": 0.304443895816803}, {"X": 0.30987101793289185, "Y": 0.30444419384002686}, {"X": 0.3098711371421814, "Y": 0.3119964599609375}, {"X": 0.23588943481445312, "Y": 0.3119961619377136}]}, "Id": "b7a55106-c979-45c8-b63b-40905b43279f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058671046048402786, "Height": 0.0073663778603076935, "Left": 0.3374950587749481, "Top": 0.30408215522766113}, "Polygon": [{"X": 0.3374950587749481, "Y": 0.30408215522766113}, {"X": 0.39616599678993225, "Y": 0.30408239364624023}, {"X": 0.3961660861968994, "Y": 0.3114485442638397}, {"X": 0.3374951481819153, "Y": 0.3114483058452606}]}, "Id": "e93767e1-f9b5-4501-968a-4174f596a8ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048247452825307846, "Height": 0.007814805023372173, "Left": 0.4660872220993042, "Top": 0.31439220905303955}, "Polygon": [{"X": 0.4660872220993042, "Y": 0.31439220905303955}, {"X": 0.5143345594406128, "Y": 0.3143923878669739}, {"X": 0.5143346786499023, "Y": 0.3222070038318634}, {"X": 0.46608731150627136, "Y": 0.3222067952156067}]}, "Id": "fdf5b950-68c1-40fa-bde5-a80af8591ecb"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09259023517370224, "Height": 0.009291324764490128, "Left": 0.5178364515304565, "Top": 0.3144724369049072}, "Polygon": [{"X": 0.5178364515304565, "Y": 0.3144724369049072}, {"X": 0.6104265451431274, "Y": 0.31447282433509827}, {"X": 0.610426664352417, "Y": 0.32376375794410706}, {"X": 0.5178365111351013, "Y": 0.323763370513916}]}, "Id": "ac59a447-8f37-4060-8a5a-aaed26c4e007"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07429980486631393, "Height": 0.00934901274740696, "Left": 0.6597591042518616, "Top": 0.3140066862106323}, "Polygon": [{"X": 0.6597591042518616, "Y": 0.3140066862106323}, {"X": 0.7340588569641113, "Y": 0.3140069842338562}, {"X": 0.7340589165687561, "Y": 0.32335570454597473}, {"X": 0.6597592234611511, "Y": 0.32335537672042847}]}, "Id": "a433cc71-9a70-4628-8913-1740ab78d3cd"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10694697499275208, "Height": 0.00777052016928792, "Left": 0.14832308888435364, "Top": 0.3262558579444885}, "Polygon": [{"X": 0.14832308888435364, "Y": 0.3262558579444885}, {"X": 0.25526994466781616, "Y": 0.32625633478164673}, {"X": 0.2552700638771057, "Y": 0.33402636647224426}, {"X": 0.14832322299480438, "Y": 0.33402588963508606}]}, "Id": "dbc0e182-1a86-402c-ab09-f1c7ad7cc2c1"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049339666962623596, "Height": 0.007473318371921778, "Left": 0.26071897149086, "Top": 0.3261425495147705}, "Polygon": [{"X": 0.26071897149086, "Y": 0.3261425495147705}, {"X": 0.3100585341453552, "Y": 0.3261427581310272}, {"X": 0.3100586533546448, "Y": 0.33361586928367615}, {"X": 0.26071909070014954, "Y": 0.33361563086509705}]}, "Id": "a3ee2a79-83b6-46c8-b99b-398a089a29f6"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03681879863142967, "Height": 0.007908767089247704, "Left": 0.33710917830467224, "Top": 0.3255611062049866}, "Polygon": [{"X": 0.33710917830467224, "Y": 0.3255611062049866}, {"X": 0.37392786145210266, "Y": 0.3255612850189209}, {"X": 0.3739279806613922, "Y": 0.33346986770629883}, {"X": 0.3371092975139618, "Y": 0.3334697186946869}]}, "Id": "9af28629-a42e-4091-9655-9c29de01b1af"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040713440626859665, "Height": 0.006804634816944599, "Left": 0.34796833992004395, "Top": 0.34465473890304565}, "Polygon": [{"X": 0.34796833992004395, "Y": 0.34465473890304565}, {"X": 0.38868170976638794, "Y": 0.34465491771698}, {"X": 0.3886817991733551, "Y": 0.3514593839645386}, {"X": 0.3479684591293335, "Y": 0.35145917534828186}]}, "Id": "7dad7140-279d-4862-9a53-6c7ec3f89ba0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044255927205085754, "Height": 0.007356932852417231, "Left": 0.8243534564971924, "Top": 0.3500892221927643}, "Polygon": [{"X": 0.8243534564971924, "Y": 0.3500892221927643}, {"X": 0.8686093688011169, "Y": 0.350089430809021}, {"X": 0.8686094284057617, "Y": 0.3574461340904236}, {"X": 0.8243535161018372, "Y": 0.35744592547416687}]}, "Id": "54335183-85b9-4888-b30f-4751b57e7727"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04497066140174866, "Height": 0.007032638881355524, "Left": 0.8724921941757202, "Top": 0.34984561800956726}, "Polygon": [{"X": 0.8724921941757202, "Y": 0.34984561800956726}, {"X": 0.9174628257751465, "Y": 0.349845826625824}, {"X": 0.9174628853797913, "Y": 0.35687825083732605}, {"X": 0.872492253780365, "Y": 0.35687801241874695}]}, "Id": "a6613ff9-1ad6-4a6b-a84d-44adddbfdb7d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.098092220723629, "Height": 0.007674383465200663, "Left": 0.17871658504009247, "Top": 0.35688072443008423}, "Polygon": [{"X": 0.17871658504009247, "Y": 0.35688072443008423}, {"X": 0.2768086791038513, "Y": 0.35688120126724243}, {"X": 0.27680879831314087, "Y": 0.36455509066581726}, {"X": 0.17871670424938202, "Y": 0.36455458402633667}]}, "Id": "67b97dbf-13e4-4e3a-8eca-93bdb7d4c383"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023527460172772408, "Height": 0.007326602004468441, "Left": 0.3566812574863434, "Top": 0.35591238737106323}, "Polygon": [{"X": 0.3566812574863434, "Y": 0.35591238737106323}, {"X": 0.3802086114883423, "Y": 0.3559125065803528}, {"X": 0.38020873069763184, "Y": 0.36323899030685425}, {"X": 0.35668134689331055, "Y": 0.3632388710975647}]}, "Id": "1092a458-5328-4595-9f6a-fdc88c2502eb"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03430702164769173, "Height": 0.007433968596160412, "Left": 0.4145145118236542, "Top": 0.3556877374649048}, "Polygon": [{"X": 0.4145145118236542, "Y": 0.3556877374649048}, {"X": 0.44882145524024963, "Y": 0.3556878864765167}, {"X": 0.4488215446472168, "Y": 0.3631216883659363}, {"X": 0.4145146310329437, "Y": 0.36312150955200195}]}, "Id": "039d161a-cd70-4e92-906a-f8a74a7fa910"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05563005432486534, "Height": 0.007411662023514509, "Left": 0.5665446519851685, "Top": 0.35591980814933777}, "Polygon": [{"X": 0.5665446519851685, "Y": 0.35591980814933777}, {"X": 0.6221746206283569, "Y": 0.35592010617256165}, {"X": 0.6221746802330017, "Y": 0.36333146691322327}, {"X": 0.5665447115898132, "Y": 0.3633311986923218}]}, "Id": "3a3310aa-fcf7-4ca2-bdd6-7577e4b31280"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062220748513936996, "Height": 0.007461553905159235, "Left": 0.6268037557601929, "Top": 0.3557145595550537}, "Polygon": [{"X": 0.6268037557601929, "Y": 0.3557145595550537}, {"X": 0.6890243887901306, "Y": 0.3557148873806}, {"X": 0.6890244483947754, "Y": 0.3631761372089386}, {"X": 0.6268038153648376, "Y": 0.36317580938339233}]}, "Id": "5a65f545-c8f2-43ba-ad32-1be577376b39"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012180105783045292, "Height": 0.007636393420398235, "Left": 0.695286214351654, "Top": 0.3553652763366699}, "Polygon": [{"X": 0.695286214351654, "Y": 0.3553652763366699}, {"X": 0.7074662446975708, "Y": 0.3553653359413147}, {"X": 0.7074663043022156, "Y": 0.36300167441368103}, {"X": 0.6952862739562988, "Y": 0.36300161480903625}]}, "Id": "f8239708-f84b-4fb0-b603-f89f05d34f58"}, {"BlockType": "WORD", "Confidence": 67.92729187011719, "Text": "x", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03763885423541069, "Height": 0.024331852793693542, "Left": 0.07340550422668457, "Top": 0.37663552165031433}, "Polygon": [{"X": 0.07340550422668457, "Y": 0.37663552165031433}, {"X": 0.11104393005371094, "Y": 0.37663573026657104}, {"X": 0.11104435473680496, "Y": 0.40096738934516907}, {"X": 0.07340593636035919, "Y": 0.40096715092658997}]}, "Id": "e44786c2-a23a-450a-9a18-10f6d35e4d0a"}, {"BlockType": "WORD", "Confidence": 73.32210540771484, "Text": "I", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.004757540766149759, "Height": 0.011665849015116692, "Left": 0.1811721920967102, "Top": 0.37889063358306885}, "Polygon": [{"X": 0.1811721920967102, "Y": 0.37889063358306885}, {"X": 0.185929536819458, "Y": 0.37889066338539124}, {"X": 0.18592973053455353, "Y": 0.3905564844608307}, {"X": 0.18117238581180573, "Y": 0.3905564546585083}]}, "Id": "1b444c39-dacf-42fc-a601-a68925f62c21"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "small", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07031191885471344, "Height": 0.01183638907968998, "Left": 0.19183644652366638, "Top": 0.37889909744262695}, "Polygon": [{"X": 0.19183644652366638, "Y": 0.37889909744262695}, {"X": 0.2621481716632843, "Y": 0.378899484872818}, {"X": 0.26214835047721863, "Y": 0.3907354772090912}, {"X": 0.1918366253376007, "Y": 0.39073505997657776}]}, "Id": "65a90883-11ca-4ace-9fa4-e521fc4b10e5"}, {"BlockType": "WORD", "Confidence": 66.37854766845703, "Text": "Sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.029764074832201004, "Height": 0.010773315094411373, "Left": 0.26873400807380676, "Top": 0.3794848918914795}, "Polygon": [{"X": 0.26873400807380676, "Y": 0.3794848918914795}, {"X": 0.2984979450702667, "Y": 0.3794850707054138}, {"X": 0.29849809408187866, "Y": 0.39025822281837463}, {"X": 0.2687341868877411, "Y": 0.3902580440044403}]}, "Id": "824f26e8-ed45-431e-b1c6-46e53243c3b1"}, {"BlockType": "WORD", "Confidence": 97.76945495605469, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04892664775252342, "Height": 0.012097621336579323, "Left": 0.4636434316635132, "Top": 0.3771483600139618}, "Polygon": [{"X": 0.4636434316635132, "Y": 0.3771483600139618}, {"X": 0.5125699639320374, "Y": 0.3771486282348633}, {"X": 0.5125700831413269, "Y": 0.38924598693847656}, {"X": 0.4636435806751251, "Y": 0.3892456889152527}]}, "Id": "db54503a-7788-49cd-8718-f07160f2b5f9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "0705", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0678027868270874, "Height": 0.011791517026722431, "Left": 0.8269667625427246, "Top": 0.3760887086391449}, "Polygon": [{"X": 0.8269667625427246, "Y": 0.3760887086391449}, {"X": 0.8947694897651672, "Y": 0.37608906626701355}, {"X": 0.894769549369812, "Y": 0.38788020610809326}, {"X": 0.8269668221473694, "Y": 0.3878798186779022}]}, "Id": "59822593-0d32-4932-a415-48b49af03222"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07109121978282928, "Height": 0.007817145437002182, "Left": 0.12229514122009277, "Top": 0.3943314254283905}, "Polygon": [{"X": 0.12229514122009277, "Y": 0.3943314254283905}, {"X": 0.1933862417936325, "Y": 0.39433184266090393}, {"X": 0.19338636100292206, "Y": 0.402148574590683}, {"X": 0.12229527533054352, "Y": 0.40214815735816956}]}, "Id": "134f085d-16fb-4651-9b85-2957d1c6b75c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gunsight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07651526480913162, "Height": 0.009663602337241173, "Left": 0.19875510036945343, "Top": 0.3942711353302002}, "Polygon": [{"X": 0.19875510036945343, "Y": 0.3942711353302002}, {"X": 0.2752702236175537, "Y": 0.394271582365036}, {"X": 0.27527037262916565, "Y": 0.4039347469806671}, {"X": 0.19875526428222656, "Y": 0.4039342701435089}]}, "Id": "4d22d708-a00e-4f6e-bd41-8b0d0482156f"}, {"BlockType": "WORD", "Confidence": 89.28551483154297, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010949739255011082, "Height": 0.0059378803707659245, "Left": 0.36353549361228943, "Top": 0.39598196744918823}, "Polygon": [{"X": 0.36353549361228943, "Y": 0.39598196744918823}, {"X": 0.3744851350784302, "Y": 0.395982027053833}, {"X": 0.37448522448539734, "Y": 0.4019198417663574}, {"X": 0.3635355532169342, "Y": 0.40191978216171265}]}, "Id": "69910941-09ed-44ba-a7d2-f625d38199f5"}, {"BlockType": "WORD", "Confidence": 91.65596771240234, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011345498263835907, "Height": 0.006103744730353355, "Left": 0.4261852204799652, "Top": 0.395738810300827}, "Polygon": [{"X": 0.4261852204799652, "Y": 0.395738810300827}, {"X": 0.43753066658973694, "Y": 0.3957388699054718}, {"X": 0.4375307261943817, "Y": 0.40184256434440613}, {"X": 0.4261853098869324, "Y": 0.40184247493743896}]}, "Id": "21f8dd90-5d5b-4f7e-83c5-9f0a4b00f84d"}, {"BlockType": "WORD", "Confidence": 99.4736557006836, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09484575688838959, "Height": 0.006871933117508888, "Left": 0.4639710485935211, "Top": 0.39442604780197144}, "Polygon": [{"X": 0.4639710485935211, "Y": 0.39442604780197144}, {"X": 0.5588167309761047, "Y": 0.3944265842437744}, {"X": 0.5588167905807495, "Y": 0.40129798650741577}, {"X": 0.4639711380004883, "Y": 0.4012974202632904}]}, "Id": "ec0efeb2-9c32-4566-85e6-e9b9408633a1"}, {"BlockType": "WORD", "Confidence": 99.27674865722656, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09489914029836655, "Height": 0.0068511986173689365, "Left": 0.563653826713562, "Top": 0.3938406705856323}, "Polygon": [{"X": 0.563653826713562, "Y": 0.3938406705856323}, {"X": 0.6585529446601868, "Y": 0.3938412070274353}, {"X": 0.6585530042648315, "Y": 0.4006918668746948}, {"X": 0.5636538863182068, "Y": 0.40069130063056946}]}, "Id": "c2b5c07d-b6c6-4095-b847-38c9122205df"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "0710", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0725608617067337, "Height": 0.00999318715184927, "Left": 0.8231115937232971, "Top": 0.40375179052352905}, "Polygon": [{"X": 0.8231115937232971, "Y": 0.40375179052352905}, {"X": 0.8956723809242249, "Y": 0.4037522077560425}, {"X": 0.8956724405288696, "Y": 0.4137449562549591}, {"X": 0.8231116533279419, "Y": 0.4137445092201233}]}, "Id": "56216135-8c8a-42be-b0f0-14b4bd8650f9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0755", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06733584403991699, "Height": 0.012236219830811024, "Left": 0.8282186985015869, "Top": 0.4362069368362427}, "Polygon": [{"X": 0.8282186985015869, "Y": 0.4362069368362427}, {"X": 0.8955544829368591, "Y": 0.4362073838710785}, {"X": 0.8955545425415039, "Y": 0.44844314455986023}, {"X": 0.8282188177108765, "Y": 0.4484426975250244}]}, "Id": "843da571-db64-4bc2-896b-95dfa1376b49"}, {"BlockType": "WORD", "Confidence": 69.3919448852539, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05929342284798622, "Height": 0.045260827988386154, "Left": 0.0594523586332798, "Top": 0.4380947947502136}, "Polygon": [{"X": 0.05945317819714546, "Y": 0.48335516452789307}, {"X": 0.0594523586332798, "Y": 0.4380947947502136}, {"X": 0.11874500662088394, "Y": 0.43809518218040466}, {"X": 0.11874578148126602, "Y": 0.4833556115627289}]}, "Id": "d78ae9e4-a512-4525-89cf-1f9ed06015f9"}, {"BlockType": "WORD", "Confidence": 89.5838623046875, "Text": "3sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.056661881506443024, "Height": 0.011778688058257103, "Left": 0.15157723426818848, "Top": 0.4495817720890045}, "Polygon": [{"X": 0.15157723426818848, "Y": 0.4495817720890045}, {"X": 0.20823892951011658, "Y": 0.44958215951919556}, {"X": 0.2082391083240509, "Y": 0.46136045455932617}, {"X": 0.151577427983284, "Y": 0.46136003732681274}]}, "Id": "e636b78e-1e89-4b7c-a8b1-3a7786cc40e3"}, {"BlockType": "WORD", "Confidence": 88.65214538574219, "Text": "2sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.059546131640672684, "Height": 0.012924063950777054, "Left": 0.4554550051689148, "Top": 0.44218820333480835}, "Polygon": [{"X": 0.4554550051689148, "Y": 0.44218820333480835}, {"X": 0.5150009989738464, "Y": 0.4421885907649994}, {"X": 0.515001118183136, "Y": 0.4551122486591339}, {"X": 0.45545515418052673, "Y": 0.4551118314266205}]}, "Id": "9aa98905-ecba-4dc6-abb3-b81eb26bd259"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Automation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09870389848947525, "Height": 0.007369108498096466, "Left": 0.12197329849004745, "Top": 0.46157196164131165}, "Polygon": [{"X": 0.12197329849004745, "Y": 0.46157196164131165}, {"X": 0.22067707777023315, "Y": 0.46157267689704895}, {"X": 0.2206771969795227, "Y": 0.4689410626888275}, {"X": 0.1219734251499176, "Y": 0.4689403474330902}]}, "Id": "32a4b6fa-3203-4b5e-9a61-2eb777541e1d"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014428775757551193, "Height": 0.007246694527566433, "Left": 0.22665175795555115, "Top": 0.4615752398967743}, "Polygon": [{"X": 0.22665175795555115, "Y": 0.4615752398967743}, {"X": 0.2410804182291031, "Y": 0.46157532930374146}, {"X": 0.24108052253723145, "Y": 0.4688219130039215}, {"X": 0.2266518622636795, "Y": 0.46882182359695435}]}, "Id": "a911bab7-b202-4274-96a1-752d4798770c"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Modular", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06844867765903473, "Height": 0.007787647191435099, "Left": 0.2462185174226761, "Top": 0.4610702395439148}, "Polygon": [{"X": 0.2462185174226761, "Y": 0.4610702395439148}, {"X": 0.31466710567474365, "Y": 0.4610707461833954}, {"X": 0.3146671950817108, "Y": 0.46885791420936584}, {"X": 0.24621863663196564, "Y": 0.46885740756988525}]}, "Id": "5e6be3ea-611b-4169-b518-29e5155d79ca"}, {"BlockType": "WORD", "Confidence": 87.73197937011719, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010778182186186314, "Height": 0.005908254999667406, "Left": 0.364133358001709, "Top": 0.46260178089141846}, "Polygon": [{"X": 0.364133358001709, "Y": 0.46260178089141846}, {"X": 0.37491145730018616, "Y": 0.46260184049606323}, {"X": 0.37491151690483093, "Y": 0.4685100317001343}, {"X": 0.36413341760635376, "Y": 0.4685099422931671}]}, "Id": "6c17445c-3a80-48a3-8756-c2b7478caea5"}, {"BlockType": "WORD", "Confidence": 85.74797058105469, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011137685738503933, "Height": 0.005786150228232145, "Left": 0.42676064372062683, "Top": 0.46268731355667114}, "Polygon": [{"X": 0.42676064372062683, "Y": 0.46268731355667114}, {"X": 0.43789827823638916, "Y": 0.4626874029636383}, {"X": 0.43789833784103394, "Y": 0.4684734642505646}, {"X": 0.426760733127594, "Y": 0.4684734046459198}]}, "Id": "12bd9e10-0b86-4bef-892b-d44127141c26"}, {"BlockType": "WORD", "Confidence": 99.08482360839844, "Text": "SO-2116134", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08608701080083847, "Height": 0.006733916234225035, "Left": 0.46426793932914734, "Top": 0.4611221253871918}, "Polygon": [{"X": 0.46426793932914734, "Y": 0.4611221253871918}, {"X": 0.5503548979759216, "Y": 0.4611227512359619}, {"X": 0.5503549575805664, "Y": 0.4678560197353363}, {"X": 0.4642680287361145, "Y": 0.46785539388656616}]}, "Id": "d56af2de-2c22-40a2-9707-0daa40c3ea91"}, {"BlockType": "WORD", "Confidence": 99.5655288696289, "Text": "2116736", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14135520160198212, "Height": 0.01314903236925602, "Left": 0.5804247856140137, "Top": 0.4552437663078308}, "Polygon": [{"X": 0.5804247856140137, "Y": 0.4552437663078308}, {"X": 0.7217798829078674, "Y": 0.455244779586792}, {"X": 0.721780002117157, "Y": 0.4683927893638611}, {"X": 0.580424964427948, "Y": 0.4683917462825775}]}, "Id": "2461af82-6213-4a80-acee-32676d82b251"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0815", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05711528658866882, "Height": 0.010189207270741463, "Left": 0.8299935460090637, "Top": 0.47014498710632324}, "Polygon": [{"X": 0.8299935460090637, "Y": 0.47014498710632324}, {"X": 0.8871087431907654, "Y": 0.47014540433883667}, {"X": 0.8871088027954102, "Y": 0.48033419251441956}, {"X": 0.8299936056137085, "Y": 0.48033374547958374}]}, "Id": "8cc8a0f4-cd6f-437e-a2d4-95fe4e7a7258"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Detroit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05822712928056717, "Height": 0.007702106609940529, "Left": 0.48029184341430664, "Top": 0.7640868425369263}, "Polygon": [{"X": 0.48029184341430664, "Y": 0.7640868425369263}, {"X": 0.5385189056396484, "Y": 0.7640876173973083}, {"X": 0.5385189652442932, "Y": 0.7717889547348022}, {"X": 0.4802919328212738, "Y": 0.7717881202697754}]}, "Id": "13dc07f7-607a-4f96-b483-bec20a802e16"}, {"BlockType": "WORD", "Confidence": 85.14867401123047, "Text": "4", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.055564768612384796, "Height": 0.02554180473089218, "Left": 0.05213910713791847, "Top": 0.9272449016571045}, "Polygon": [{"X": 0.05213910713791847, "Y": 0.9272449016571045}, {"X": 0.10770343244075775, "Y": 0.9272458553314209}, {"X": 0.10770387947559357, "Y": 0.9527866840362549}, {"X": 0.05213957279920578, "Y": 0.9527857303619385}]}, "Id": "fb3bc905-4a3d-43cd-b79f-3f440807efb5"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "a0c82220-9fa8-489a-9527-23c08d4f24c7", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "a0c82220-9fa8-489a-9527-23c08d4f24c7", "content-type": "application/x-amz-json-1.1", "content-length": "51126", "date": "Wed, 24 Sep 2025 13:39:59 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "79f88f4c-0cae-4d10-9150-c26334978a88", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:40:03 GMT", "content-type": "application/json", "content-length": "1891", "connection": "keep-alive", "x-amzn-requestid": "79f88f4c-0cae-4d10-9150-c26334978a88"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The content seems like a truck ready sheet with work order numbers, driver, dock time. Not obviously a recognized doc type like invoice, bol, pod, etc. Let's inspect: There's \"***********\" etc. Work order numbers. Not \"invoice\". Could be a \"log\" (driver log). But it's more like a loading sheet. Might be \"ingate\" or \"outgate\"? It has dock time, truck ready time, departure time. This looks like an \"Ingate\" document? But ingate has arrival activity, truck in. This appears to be a dispatch sheet. None of the categories exactly match. Might be \"other\".\n\nCheck for \"log\" keywords: \"Dock Time\" \"Truck Departure\" \"Driver\". Could be \"log\". But the log doc type includes \"Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries\". This might fit. It's a log of driver activity times. But no explicit \"Logbook\" header. It's more a sheet for driver. I'd classify as \"log\".\n\nBut also check \"ingate\" - includes \"Truck In\" and \"Entry Time\". But this page has \"Truck Ready Time\", \"Truck Departure\", \"Departure Time\". That is more like a departure sheet, maybe a \"log\" or \"outgate\". \"Outgate\" includes \"Gate Out, Release, Exit Time\". It has \"Departure Time\". Could be \"outgate\". But typically outgate would have exit time, release stamp. Not present.\n\nThus \"log\" seems appropriate.\n\nWe should output JSON with documents array containing one object: page_no 1, doc_type \"log\".\n\nLet's produce accordingly."}}}, {"text": "{\n  \"documents\": [\n    {\n      \"page_no\": 1,\n      \"doc_type\": \"log\"\n    }\n  ]\n}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2738, "outputTokens": 389, "totalTokens": 3127}, "metrics": {"latencyMs": 1847}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/2c7b45cd_8_log_5.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\n09:57\n5Gc\n94\nDone\nRetake\nDATE: 24-Feb\nUNIT #: 92644/283420\nTruck Ready Time: 5:00\nDRIVER: KARRA\nTruck Departure:\n5:00\nDEPARTURE TIME: 5am\nPICK\nDock Time\nCUSTOMER\nUP\nDEL\nWORK ORDER #\nx\nI small Sk\nISK\n0705\nWilliams Gunsight\nX\nX\n*********** ***********\n0710\n0755\nX\n3sk\n2sk\nAutomation & Modular\nX\nX\nSO-2116134\n2116736\n0815\nDetroit\n4\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}