2025-09-24 16:22:26,822 - INFO - Logging initialized. Log file: logs/test_classification_20250924_162226.log
2025-09-24 16:22:26,823 - INFO - 📁 Found 16 files to process
2025-09-24 16:22:26,823 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 16:22:26,824 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-24 16:22:26,824 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-24 16:22:26,824 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-24 16:22:26,824 - INFO - ⬆️ [16:22:26] Uploading: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:28,563 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg -> s3://document-extraction-logistically/temp/2c750900_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:28,563 - INFO - 🔍 [16:22:28] Starting classification: AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:28,564 - INFO - ⬆️ [16:22:28] Uploading: DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:28,567 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:28,581 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:28,586 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2c750900_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:28,586 - INFO - Processing image from S3...
2025-09-24 16:22:30,675 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/DV1B199A0TJGUQ3FETIU.pdf -> s3://document-extraction-logistically/temp/612cd0d7_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:30,675 - INFO - 🔍 [16:22:30] Starting classification: DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:30,676 - INFO - ⬆️ [16:22:30] Uploading: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:30,678 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:30,696 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:30,700 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/612cd0d7_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:30,700 - INFO - Processing PDF from S3...
2025-09-24 16:22:30,703 - INFO - Downloading PDF from S3 to /tmp/tmp4qbz0b0p/612cd0d7_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:31,306 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf -> s3://document-extraction-logistically/temp/ca6afadb_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:31,307 - INFO - 🔍 [16:22:31] Starting classification: GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:31,307 - INFO - ⬆️ [16:22:31] Uploading: HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:31,308 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:31,324 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:31,327 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ca6afadb_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:31,328 - INFO - Processing PDF from S3...
2025-09-24 16:22:31,328 - INFO - Downloading PDF from S3 to /tmp/tmp5repxd86/ca6afadb_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:31,861 - INFO - S3 Image temp/2c750900_AOYIL346IKT06LKO6D2R.jpg: Extracted 882 characters, 69 lines
2025-09-24 16:22:31,861 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:31,861 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:32,001 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/HEMTH4BHCWXWE044I0SK.pdf -> s3://document-extraction-logistically/temp/cd8b69fe_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:32,001 - INFO - 🔍 [16:22:32] Starting classification: HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:32,002 - INFO - ⬆️ [16:22:32] Uploading: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:32,003 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:32,013 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:32,015 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cd8b69fe_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:32,016 - INFO - Processing PDF from S3...
2025-09-24 16:22:32,016 - INFO - Downloading PDF from S3 to /tmp/tmpylz5lrqu/cd8b69fe_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:32,650 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf -> s3://document-extraction-logistically/temp/2010d4f8_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:32,651 - INFO - 🔍 [16:22:32] Starting classification: I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:32,652 - INFO - ⬆️ [16:22:32] Uploading: IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:32,654 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:32,670 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:32,674 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2010d4f8_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:32,674 - INFO - Processing PDF from S3...
2025-09-24 16:22:32,675 - INFO - Downloading PDF from S3 to /tmp/tmpy5eltqv5/2010d4f8_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:33,143 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:22:33,144 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:33,147 - INFO - Splitting PDF ca6afadb_GKYUUU2YTZ1YTPGXHO51 into 3 pages
2025-09-24 16:22:33,171 - INFO - Split PDF into 3 pages
2025-09-24 16:22:33,172 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:33,172 - INFO - Expected pages: [1, 2, 3]
2025-09-24 16:22:33,240 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/IJW9IJUOROYY1SWO0B17.pdf -> s3://document-extraction-logistically/temp/b9aa50dd_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:33,241 - INFO - 🔍 [16:22:33] Starting classification: IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:33,242 - INFO - ⬆️ [16:22:33] Uploading: K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:33,242 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:33,266 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:33,270 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b9aa50dd_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:33,270 - INFO - Processing PDF from S3...
2025-09-24 16:22:33,270 - INFO - Downloading PDF from S3 to /tmp/tmpi3pce78f/b9aa50dd_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:33,842 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/K88AFHJU4HIQUZYTMOF4.pdf -> s3://document-extraction-logistically/temp/c1bfd9b1_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:33,843 - INFO - 🔍 [16:22:33] Starting classification: K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:33,844 - INFO - ⬆️ [16:22:33] Uploading: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:33,846 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:33,860 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:33,864 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c1bfd9b1_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:33,864 - INFO - Processing PDF from S3...
2025-09-24 16:22:33,864 - INFO - Downloading PDF from S3 to /tmp/tmpi6670h3_/c1bfd9b1_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:34,108 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '840c498a-f222-4a6b-9d5c-170b3f1efe78', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:33 GMT', 'content-type': 'application/json', 'content-length': '898', 'connection': 'keep-alive', 'x-amzn-requestid': '840c498a-f222-4a6b-9d5c-170b3f1efe78'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The text includes "Delivery Receipt" heading. That is a type of POD? "Proof of Delivery" typically includes signature, Received by, etc. It also has "Receiver Name", "Signature", "Date", "Time in", "Time Out". So it\'s a POD or Delivery Receipt. The definitions: pod: Proof of Delivery. Delivery Receipt is a form of POD. The page includes "Delivery Receipt" header. So classify as pod.\n\nWe must output JSON via tool call classify_logistics_doc_type with documents array containing page_no 1, doc_type "pod".\n\nWe output via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2780, 'outputTokens': 146, 'totalTokens': 2926}, 'metrics': {'latencyMs': 877}}
2025-09-24 16:22:34,109 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2c750900_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:34,189 - INFO - Downloaded PDF size: 1.8 MB
2025-09-24 16:22:34,189 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:34,190 - INFO - Splitting PDF 612cd0d7_DV1B199A0TJGUQ3FETIU into 3 pages
2025-09-24 16:22:34,219 - INFO - Split PDF into 3 pages
2025-09-24 16:22:34,219 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:34,219 - INFO - Expected pages: [1, 2, 3]
2025-09-24 16:22:34,470 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf -> s3://document-extraction-logistically/temp/a591a49f_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:34,471 - INFO - 🔍 [16:22:34] Starting classification: KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:34,471 - INFO - ⬆️ [16:22:34] Uploading: KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:34,473 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:34,487 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:34,492 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a591a49f_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:34,492 - INFO - Processing PDF from S3...
2025-09-24 16:22:34,492 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:22:34,492 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:34,493 - INFO - Downloading PDF from S3 to /tmp/tmpshlefumj/a591a49f_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:34,500 - INFO - Splitting PDF 2010d4f8_I6IM526GOVKAWH0B5WKE into 1 pages
2025-09-24 16:22:34,502 - INFO - Split PDF into 1 pages
2025-09-24 16:22:34,502 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:34,502 - INFO - Expected pages: [1]
2025-09-24 16:22:35,081 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KUYJRWRDFCC2FB7SKI9T.jpeg -> s3://document-extraction-logistically/temp/15bad1bd_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:35,082 - INFO - 🔍 [16:22:35] Starting classification: KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:35,083 - INFO - ⬆️ [16:22:35] Uploading: P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:35,083 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:35,103 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:35,109 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15bad1bd_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:35,110 - INFO - Processing image from S3...
2025-09-24 16:22:35,158 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:22:35,158 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:35,159 - INFO - Splitting PDF b9aa50dd_IJW9IJUOROYY1SWO0B17 into 1 pages
2025-09-24 16:22:35,163 - INFO - Split PDF into 1 pages
2025-09-24 16:22:35,164 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:35,164 - INFO - Expected pages: [1]
2025-09-24 16:22:35,764 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/P2EZG64EJI98NT69TSAL.pdf -> s3://document-extraction-logistically/temp/abd6a4af_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:35,764 - INFO - 🔍 [16:22:35] Starting classification: P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:35,765 - INFO - ⬆️ [16:22:35] Uploading: P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:35,767 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:35,783 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:35,787 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/abd6a4af_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:35,787 - INFO - Processing PDF from S3...
2025-09-24 16:22:35,787 - INFO - Downloading PDF from S3 to /tmp/tmp7u3j745c/abd6a4af_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:36,318 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 16:22:36,319 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:36,321 - INFO - Splitting PDF c1bfd9b1_K88AFHJU4HIQUZYTMOF4 into 1 pages
2025-09-24 16:22:36,324 - INFO - Split PDF into 1 pages
2025-09-24 16:22:36,324 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:36,324 - INFO - Expected pages: [1]
2025-09-24 16:22:36,362 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/P6RAGXD27RW4QUXN96TH.jpeg -> s3://document-extraction-logistically/temp/e281475f_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:36,362 - INFO - 🔍 [16:22:36] Starting classification: P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:36,363 - INFO - ⬆️ [16:22:36] Uploading: PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:36,365 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:36,384 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:36,387 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e281475f_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:36,387 - INFO - Processing image from S3...
2025-09-24 16:22:36,466 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 16:22:36,466 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:36,467 - WARNING - incorrect startxref pointer(1)
2025-09-24 16:22:36,470 - INFO - Splitting PDF a591a49f_KK3PKFLS4ROTEZ7LOMMD into 1 pages
2025-09-24 16:22:36,482 - INFO - Split PDF into 1 pages
2025-09-24 16:22:36,482 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:36,482 - INFO - Expected pages: [1]
2025-09-24 16:22:37,009 - INFO - Page 1: Extracted 643 characters, 34 lines from ca6afadb_GKYUUU2YTZ1YTPGXHO51_443bc7c9_page_001.pdf
2025-09-24 16:22:37,009 - INFO - Successfully processed page 1
2025-09-24 16:22:37,238 - INFO - Downloaded PDF size: 1.0 MB
2025-09-24 16:22:37,238 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:37,241 - INFO - Splitting PDF cd8b69fe_HEMTH4BHCWXWE044I0SK into 2 pages
2025-09-24 16:22:37,246 - INFO - Split PDF into 2 pages
2025-09-24 16:22:37,247 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:37,247 - INFO - Expected pages: [1, 2]
2025-09-24 16:22:37,647 - INFO - Page 2: Extracted 1426 characters, 86 lines from ca6afadb_GKYUUU2YTZ1YTPGXHO51_443bc7c9_page_002.pdf
2025-09-24 16:22:37,647 - INFO - Successfully processed page 2
2025-09-24 16:22:37,841 - INFO - Page 3: Extracted 1566 characters, 104 lines from ca6afadb_GKYUUU2YTZ1YTPGXHO51_443bc7c9_page_003.pdf
2025-09-24 16:22:37,841 - INFO - Successfully processed page 3
2025-09-24 16:22:37,843 - INFO - Combined 3 pages into final text
2025-09-24 16:22:37,843 - INFO - Text validation for ca6afadb_GKYUUU2YTZ1YTPGXHO51: 3690 characters, 3 pages
2025-09-24 16:22:37,843 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:37,843 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:37,848 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/PK5AIZFCER7DUFL15N01.pdf -> s3://document-extraction-logistically/temp/6ece7f6c_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:37,848 - INFO - 🔍 [16:22:37] Starting classification: PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:37,849 - INFO - ⬆️ [16:22:37] Uploading: RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:37,849 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:37,862 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:37,865 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ece7f6c_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:37,865 - INFO - Processing PDF from S3...
2025-09-24 16:22:37,866 - INFO - Downloading PDF from S3 to /tmp/tmppqo7w4f3/6ece7f6c_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:37,977 - INFO - S3 Image temp/15bad1bd_KUYJRWRDFCC2FB7SKI9T.jpeg: Extracted 515 characters, 39 lines
2025-09-24 16:22:37,978 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:37,978 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:38,360 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 16:22:38,360 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:38,360 - INFO - Splitting PDF abd6a4af_P2EZG64EJI98NT69TSAL into 1 pages
2025-09-24 16:22:38,362 - INFO - Split PDF into 1 pages
2025-09-24 16:22:38,362 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:38,362 - INFO - Expected pages: [1]
2025-09-24 16:22:38,838 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/RCNPMJFRSZTXQWBGLB9R.pdf -> s3://document-extraction-logistically/temp/481557bf_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:38,839 - INFO - 🔍 [16:22:38] Starting classification: RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:38,840 - INFO - ⬆️ [16:22:38] Uploading: S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:38,841 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:38,856 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:38,860 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/481557bf_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:38,861 - INFO - Processing PDF from S3...
2025-09-24 16:22:38,861 - INFO - Downloading PDF from S3 to /tmp/tmp04fuofp_/481557bf_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:39,041 - INFO - S3 Image temp/e281475f_P6RAGXD27RW4QUXN96TH.jpeg: Extracted 456 characters, 31 lines
2025-09-24 16:22:39,041 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:39,041 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:39,417 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf -> s3://document-extraction-logistically/temp/eb58c43a_S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:39,417 - INFO - 🔍 [16:22:39] Starting classification: S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:39,418 - INFO - ⬆️ [16:22:39] Uploading: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:39,420 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:39,436 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:39,441 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb58c43a_S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:39,441 - INFO - Processing PDF from S3...
2025-09-24 16:22:39,442 - INFO - Downloading PDF from S3 to /tmp/tmp54lhx1vb/eb58c43a_S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:39,727 - INFO - Page 1: Extracted 1921 characters, 143 lines from 2010d4f8_I6IM526GOVKAWH0B5WKE_0d19045a_page_001.pdf
2025-09-24 16:22:39,727 - INFO - Successfully processed page 1
2025-09-24 16:22:39,727 - INFO - Combined 1 pages into final text
2025-09-24 16:22:39,727 - INFO - Text validation for 2010d4f8_I6IM526GOVKAWH0B5WKE: 1938 characters, 1 pages
2025-09-24 16:22:39,727 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:39,727 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:39,969 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f1750285-820e-4caa-b49c-5cb288553353', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:39 GMT', 'content-type': 'application/json', 'content-length': '969', 'connection': 'keep-alive', 'x-amzn-requestid': 'f1750285-820e-4caa-b49c-5cb288553353'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'The user provided a single page that appears to be a packing slip. It\'s a "Packing Slip" containing items, shipping details, etc. The doc type: pack_list? The definition: packing list. Indeed the keywords: "Packing Slip", "Ship Date", "Purchase Order", etc. The pack_list is the proper type. The page is a single page. So we return an array with one object: page_no: 1, doc_type: "pack_list".\n\nWe must use the tool classify_logistics_doc_type. The tool expects an argument: documents: array of objects with page_no and doc_type. Yes. We\'ll call the tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_uz9QrrePRvqAbMDT7usajA', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2662, 'outputTokens': 151, 'totalTokens': 2813}, 'metrics': {'latencyMs': 799}}
2025-09-24 16:22:39,969 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15bad1bd_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:39,998 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf -> s3://document-extraction-logistically/temp/5a53c63e_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:39,999 - INFO - 🔍 [16:22:39] Starting classification: TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:40,000 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:40,000 - INFO - ⬆️ [16:22:40] Uploading: YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:40,072 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:40,074 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5a53c63e_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:40,075 - INFO - Processing PDF from S3...
2025-09-24 16:22:40,075 - INFO - Downloading PDF from S3 to /tmp/tmp_mb5vfo5/5a53c63e_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:40,695 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/YC2ROS9NUB31VO0ABEV5.jpeg -> s3://document-extraction-logistically/temp/dc139c58_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:40,695 - INFO - 🔍 [16:22:40] Starting classification: YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:40,696 - INFO - Initializing TextractProcessor...
2025-09-24 16:22:40,713 - INFO - Initializing BedrockProcessor...
2025-09-24 16:22:40,721 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dc139c58_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:40,723 - INFO - Processing image from S3...
2025-09-24 16:22:40,739 - INFO - 

AOYIL346IKT06LKO6D2R.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 16:22:40,740 - INFO - 

✓ Saved result: output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 16:22:40,901 - INFO - Page 3: Extracted 990 characters, 86 lines from 612cd0d7_DV1B199A0TJGUQ3FETIU_5ee3ad6c_page_003.pdf
2025-09-24 16:22:40,902 - INFO - Successfully processed page 3
2025-09-24 16:22:40,955 - INFO - Downloaded PDF size: 3.3 MB
2025-09-24 16:22:40,955 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:40,957 - INFO - Splitting PDF 6ece7f6c_PK5AIZFCER7DUFL15N01 into 1 pages
2025-09-24 16:22:40,963 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 16:22:40,963 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:40,963 - INFO - Splitting PDF eb58c43a_S92OW12RYFF5G3READPW into 1 pages
2025-09-24 16:22:40,965 - INFO - Split PDF into 1 pages
2025-09-24 16:22:40,965 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:40,965 - INFO - Expected pages: [1]
2025-09-24 16:22:40,969 - INFO - Split PDF into 1 pages
2025-09-24 16:22:40,969 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:40,969 - INFO - Expected pages: [1]
2025-09-24 16:22:41,026 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2c750900_AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:41,043 - INFO - 

KUYJRWRDFCC2FB7SKI9T.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:22:41,043 - INFO - 

✓ Saved result: output/run1_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 16:22:41,329 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15bad1bd_KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:41,379 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 16:22:41,379 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:41,380 - INFO - Splitting PDF 5a53c63e_TQSZJLFFGGKB4NFN12RD into 1 pages
2025-09-24 16:22:41,381 - INFO - Split PDF into 1 pages
2025-09-24 16:22:41,381 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:41,381 - INFO - Expected pages: [1]
2025-09-24 16:22:41,486 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7ca39ef2-86c6-4ebc-b7a4-f4a7e4e329fb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:41 GMT', 'content-type': 'application/json', 'content-length': '2166', 'connection': 'keep-alive', 'x-amzn-requestid': '7ca39ef2-86c6-4ebc-b7a4-f4a7e4e329fb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The pages show FedEx Delivery Receipt, proof-of-delivery, and freight bill. The first page is proof of delivery. The second and third pages are freight bill containing freight charges. They might be Freight Bill (invoice). The doc type: invoice or related. Let\'s analyze: The first page clearly says "proof-of-delivery". So doc_type: pod. Page 2 includes "Delivery Receipt" and "Freight Bill". Looks like a freight bill invoice. Document type: invoice. The keywords: Freight Bill, Bill of Lading, total charges, etc. So invoice. Page 3 similar to page 2. It\'s also a freight bill. So invoice. Are they separate documents? The prompt: "For every page in the input PDF you MUST return exactly one object describing that page." So we need to output 3 objects. But we need to consider if page 2 and page 3 are part of same document: They are separate pages of the same freight bill? Page 2 says "Page 2 of 2" and page 3 says "Page 1 of 2". The content seems similar but not identical. Page 3 maybe a duplicate? Wait page 2 is "Page 2 of 2" and page 3 is "Page 1 of 2". So maybe page 3 is a separate freight bill for a different shipment? The text shows Bill of Lading 11746, same as page 2. So maybe page 3 is an earlier copy? It\'s unclear. But we treat each page individually. So page 2 is invoice, page 3 is invoice too. But we need to check if page 3 is continuation of page 2? Page 3 doesn\'t start with "continued". It has "Page 1 of 2". So it\'s a new document. So each page gets its own type.\n\nThus output:\n\n{\n  documents: [\n    {page_no: 1, doc_type: "pod"},\n    {page_no: 2, doc_type: "invoice"},\n    {page_no: 3, doc_type: "invoice"}\n  ]\n}\n\nWe must return via tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3773, 'outputTokens': 490, 'totalTokens': 4263}, 'metrics': {'latencyMs': 2272}}
2025-09-24 16:22:41,486 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ca6afadb_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:41,521 - INFO - 

GKYUUU2YTZ1YTPGXHO51.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 16:22:41,522 - INFO - 

✓ Saved result: output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:41,743 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '13dc98d2-a61f-4d5e-9e45-bf7000baaecd', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:41 GMT', 'content-type': 'application/json', 'content-length': '815', 'connection': 'keep-alive', 'x-amzn-requestid': '13dc98d2-a61f-4d5e-9e45-bf7000baaecd'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'The page has "Packing Slip" and "Purchase Order". This seems like a packing list? The header says "Packing Slip". The definition for pack_list: "Packing List, Pack List, Contents, Qty, Item Description". This appears to be a packing slip, which is similar to packing list. So doc_type likely pack_list. There\'s no BOL etc. So classify as pack_list.\n\nBut what about invoice? No invoice. So pack_list. It\'s a single page. So output array with one object: page_no 1, doc_type "pack_list".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2626, 'outputTokens': 133, 'totalTokens': 2759}, 'metrics': {'latencyMs': 707}}
2025-09-24 16:22:41,743 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e281475f_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:41,821 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ca6afadb_GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:41,834 - INFO - 

P6RAGXD27RW4QUXN96TH.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:22:41,835 - INFO - 

✓ Saved result: output/run1_P6RAGXD27RW4QUXN96TH.json
2025-09-24 16:22:41,908 - INFO - Page 2: Extracted 2112 characters, 162 lines from 612cd0d7_DV1B199A0TJGUQ3FETIU_5ee3ad6c_page_002.pdf
2025-09-24 16:22:41,908 - INFO - Successfully processed page 2
2025-09-24 16:22:41,908 - INFO - Downloaded PDF size: 1.3 MB
2025-09-24 16:22:41,908 - INFO - Splitting PDF into individual pages...
2025-09-24 16:22:41,909 - INFO - Splitting PDF 481557bf_RCNPMJFRSZTXQWBGLB9R into 1 pages
2025-09-24 16:22:41,911 - INFO - Split PDF into 1 pages
2025-09-24 16:22:41,911 - INFO - Processing pages with Textract in parallel...
2025-09-24 16:22:41,911 - INFO - Expected pages: [1]
2025-09-24 16:22:42,050 - INFO - Page 1: Extracted 3736 characters, 122 lines from b9aa50dd_IJW9IJUOROYY1SWO0B17_c261889a_page_001.pdf
2025-09-24 16:22:42,050 - INFO - Successfully processed page 1
2025-09-24 16:22:42,050 - INFO - Combined 1 pages into final text
2025-09-24 16:22:42,051 - INFO - Text validation for b9aa50dd_IJW9IJUOROYY1SWO0B17: 3753 characters, 1 pages
2025-09-24 16:22:42,051 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:42,051 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:42,126 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e281475f_P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:42,361 - INFO - Page 1: Extracted 2979 characters, 110 lines from 612cd0d7_DV1B199A0TJGUQ3FETIU_5ee3ad6c_page_001.pdf
2025-09-24 16:22:42,361 - INFO - Successfully processed page 1
2025-09-24 16:22:42,361 - INFO - Combined 3 pages into final text
2025-09-24 16:22:42,362 - INFO - Text validation for 612cd0d7_DV1B199A0TJGUQ3FETIU: 6135 characters, 3 pages
2025-09-24 16:22:42,362 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:42,362 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:42,396 - INFO - Page 1: Extracted 2991 characters, 109 lines from c1bfd9b1_K88AFHJU4HIQUZYTMOF4_42e85712_page_001.pdf
2025-09-24 16:22:42,397 - INFO - Successfully processed page 1
2025-09-24 16:22:42,397 - INFO - Combined 1 pages into final text
2025-09-24 16:22:42,397 - INFO - Text validation for c1bfd9b1_K88AFHJU4HIQUZYTMOF4: 3008 characters, 1 pages
2025-09-24 16:22:42,397 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:42,397 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:42,431 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3422b6f2-8039-4b3c-8780-873e66923469', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:42 GMT', 'content-type': 'application/json', 'content-length': '1351', 'connection': 'keep-alive', 'x-amzn-requestid': '3422b6f2-8039-4b3c-8780-873e66923469'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 provided. We need to output JSON object with page_no and doc_type.\n\nWe need to determine document type. The page is a delivery receipt? It looks like a delivery receipt with details of shipment, load count, etc. No header explicitly "Delivery Receipt". It has "RECEIPT" inside. The doc is a "Delivery Receipt" which is a type of "invoice"? Actually a delivery receipt is a document acknowledging receipt of goods, maybe a POD? But POD is proof of delivery. This could be a "Delivery Receipt" which is a type of POD? The page contains "Receiver\'s Signature". It has items, weights, etc. The doc type could be POD? But "Proof of Delivery" includes signature and date. This doc has that. It is not a bill of lading, not an invoice. So I think it\'s POD.\n\nBut check definitions: POD: Proof of Delivery. Yes.\n\nThus output: page_no: 1, doc_type: "pod".\n\nWe must call the tool classify_logistics_doc_type with documents array containing object with page_no 1 and doc_type "pod".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3187, 'outputTokens': 254, 'totalTokens': 3441}, 'metrics': {'latencyMs': 1347}}
2025-09-24 16:22:42,435 - INFO - Page 1: Extracted 1453 characters, 86 lines from a591a49f_KK3PKFLS4ROTEZ7LOMMD_857d78f4_page_001.pdf
2025-09-24 16:22:42,435 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2010d4f8_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:42,435 - INFO - Successfully processed page 1
2025-09-24 16:22:42,437 - INFO - Combined 1 pages into final text
2025-09-24 16:22:42,438 - INFO - Text validation for a591a49f_KK3PKFLS4ROTEZ7LOMMD: 1470 characters, 1 pages
2025-09-24 16:22:42,439 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:42,440 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:42,479 - INFO - 

I6IM526GOVKAWH0B5WKE.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 16:22:42,479 - INFO - 

✓ Saved result: output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 16:22:42,790 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2010d4f8_I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:43,717 - INFO - S3 Image temp/dc139c58_YC2ROS9NUB31VO0ABEV5.jpeg: Extracted 454 characters, 31 lines
2025-09-24 16:22:43,717 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:43,717 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:44,099 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9e273200-4293-40ee-a8a0-dd0d9b2abc9f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:43 GMT', 'content-type': 'application/json', 'content-length': '693', 'connection': 'keep-alive', 'x-amzn-requestid': '9e273200-4293-40ee-a8a0-dd0d9b2abc9f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. It looks like a waybill, a Bill of Lading? The header says WAYBILL. It contains details about shipper, consignee, shipment info, etc. It\'s basically a Bill of Lading. So doc_type = bol. We must output an array of objects for each page. It says page_no: 1. So output: {page_no:1, doc_type:"bol"}.\n\nWe need to use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3497, 'outputTokens': 119, 'totalTokens': 3616}, 'metrics': {'latencyMs': 732}}
2025-09-24 16:22:44,100 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b9aa50dd_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:44,131 - INFO - 

IJW9IJUOROYY1SWO0B17.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:44,131 - INFO - 

✓ Saved result: output/run1_IJW9IJUOROYY1SWO0B17.json
2025-09-24 16:22:44,254 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'eb4bc2e6-f8bf-40eb-b3ad-4934b160f7e4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:44 GMT', 'content-type': 'application/json', 'content-length': '638', 'connection': 'keep-alive', 'x-amzn-requestid': 'eb4bc2e6-f8bf-40eb-b3ad-4934b160f7e4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It is a Bill of Lading. We need to output via the tool classify_logistics_doc_type. The page has label "BILL OF LADING" and many BOL fields. So doc_type: "bol". So we output JSON with documents: array of objects each with page_no and doc_type.\n\nWe need to call the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3254, 'outputTokens': 99, 'totalTokens': 3353}, 'metrics': {'latencyMs': 601}}
2025-09-24 16:22:44,254 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c1bfd9b1_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:44,372 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2c19a4ce-e065-4703-b850-083c33d681b7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:44 GMT', 'content-type': 'application/json', 'content-length': '719', 'connection': 'keep-alive', 'x-amzn-requestid': '2c19a4ce-e065-4703-b850-083c33d681b7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s a Delivery Receipt from XPO. Keywords: "Delivery Receipt", "Pro Number", "Consignee", "Shipper", "Bill To", "PO#", "Weight", "Rate", "Charges", "Delivered", "Consignee Signature". That\'s a Proof of Delivery (POD). So doc_type = pod. There\'s no continuation. So output array with one object: page_no 1, doc_type "pod". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2981, 'outputTokens': 118, 'totalTokens': 3099}, 'metrics': {'latencyMs': 726}}
2025-09-24 16:22:44,372 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a591a49f_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:44,423 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b9aa50dd_IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:44,471 - INFO - 

K88AFHJU4HIQUZYTMOF4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:44,471 - INFO - 

✓ Saved result: output/run1_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 16:22:44,732 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3dcaf071-f537-41bc-8dcf-99e50c77d0a3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:44 GMT', 'content-type': 'application/json', 'content-length': '981', 'connection': 'keep-alive', 'x-amzn-requestid': '3dcaf071-f537-41bc-8dcf-99e50c77d0a3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. It\'s a multi-page BOL? Page1 shows "BILL OF LADING" header, appears to be a BOL. Page2 and 3 are also BOL pages but with "PAGE 1 OF 3" etc. They are all part of same document. So each page should be "bol". Ensure same type across pages. No continuation markers? Page2 starts with "ABF ... PAGE 1 OF 3" not "continued". But it\'s a separate page of same doc. So likely all pages are bol.\n\nThus output 3 objects, each with page_no and doc_type "bol".\n\nTool call format: array of documents: each object: page_no, doc_type.\n\nReturn via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4591, 'outputTokens': 191, 'totalTokens': 4782}, 'metrics': {'latencyMs': 1078}}
2025-09-24 16:22:44,733 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/612cd0d7_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:44,762 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c1bfd9b1_K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:44,786 - INFO - Page 2: Extracted 3230 characters, 90 lines from cd8b69fe_HEMTH4BHCWXWE044I0SK_194ca153_page_002.pdf
2025-09-24 16:22:44,788 - INFO - Successfully processed page 2
2025-09-24 16:22:44,804 - INFO - 

KK3PKFLS4ROTEZ7LOMMD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 16:22:44,804 - INFO - 

✓ Saved result: output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 16:22:44,848 - INFO - Page 1: Extracted 3181 characters, 88 lines from cd8b69fe_HEMTH4BHCWXWE044I0SK_194ca153_page_001.pdf
2025-09-24 16:22:44,848 - INFO - Successfully processed page 1
2025-09-24 16:22:44,849 - INFO - Combined 2 pages into final text
2025-09-24 16:22:44,849 - INFO - Text validation for cd8b69fe_HEMTH4BHCWXWE044I0SK: 6447 characters, 2 pages
2025-09-24 16:22:44,849 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:44,849 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:45,088 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a591a49f_KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:45,154 - INFO - 

DV1B199A0TJGUQ3FETIU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        },
        {
            "page_no": 3,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:45,154 - INFO - 

✓ Saved result: output/run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:45,282 - INFO - Page 1: Extracted 1608 characters, 74 lines from abd6a4af_P2EZG64EJI98NT69TSAL_43daac98_page_001.pdf
2025-09-24 16:22:45,282 - INFO - Successfully processed page 1
2025-09-24 16:22:45,283 - INFO - Combined 1 pages into final text
2025-09-24 16:22:45,283 - INFO - Text validation for abd6a4af_P2EZG64EJI98NT69TSAL: 1625 characters, 1 pages
2025-09-24 16:22:45,283 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:45,283 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:45,458 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/612cd0d7_DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:45,523 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be5fe6a2-bac0-4906-ad41-25e880f2193a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:45 GMT', 'content-type': 'application/json', 'content-length': '614', 'connection': 'keep-alive', 'x-amzn-requestid': 'be5fe6a2-bac0-4906-ad41-25e880f2193a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It includes "Packing Slip". So doc_type likely "pack_list". The keywords: "Packing Slip" indicates packing list. Not invoice, etc. So output one page with page_no 1, doc_type "pack_list".\n\nWe must call the function classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pack_list"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2627, 'outputTokens': 84, 'totalTokens': 2711}, 'metrics': {'latencyMs': 609}}
2025-09-24 16:22:45,524 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dc139c58_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:45,545 - INFO - 

YC2ROS9NUB31VO0ABEV5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-24 16:22:45,545 - INFO - 

✓ Saved result: output/run1_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 16:22:45,835 - INFO - Page 1: Extracted 1312 characters, 65 lines from 5a53c63e_TQSZJLFFGGKB4NFN12RD_82407927_page_001.pdf
2025-09-24 16:22:45,835 - INFO - Successfully processed page 1
2025-09-24 16:22:45,835 - INFO - Combined 1 pages into final text
2025-09-24 16:22:45,835 - INFO - Text validation for 5a53c63e_TQSZJLFFGGKB4NFN12RD: 1329 characters, 1 pages
2025-09-24 16:22:45,835 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:45,835 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:45,838 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dc139c58_YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:46,113 - INFO - Page 1: Extracted 1576 characters, 111 lines from eb58c43a_S92OW12RYFF5G3READPW_f6815a49_page_001.pdf
2025-09-24 16:22:46,113 - INFO - Successfully processed page 1
2025-09-24 16:22:46,113 - INFO - Combined 1 pages into final text
2025-09-24 16:22:46,113 - INFO - Text validation for eb58c43a_S92OW12RYFF5G3READPW: 1593 characters, 1 pages
2025-09-24 16:22:46,114 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:46,114 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:46,995 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '768caa92-df5a-4e51-801b-bb95d5d1e652', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:46 GMT', 'content-type': 'application/json', 'content-length': '851', 'connection': 'keep-alive', 'x-amzn-requestid': '768caa92-df5a-4e51-801b-bb95d5d1e652'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Both pages contain a "UNIFRON STRAIGHT BILL OF LADING" header. So doc_type should be bol.\n\nNeed to ensure continuation detection: page2 is continuation of page1. It shows same Bill of Lading. But the rule says we still classify each page individually. Both are bol. No special continuation header. So return two objects.\n\nReturn via tool call classify_logistics_doc_type with documents array containing page_no and doc_type. Page numbers are 1 and 2. Provide array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4104, 'outputTokens': 137, 'totalTokens': 4241}, 'metrics': {'latencyMs': 773}}
2025-09-24 16:22:46,995 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cd8b69fe_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:47,062 - INFO - 

HEMTH4BHCWXWE044I0SK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:47,062 - INFO - 

✓ Saved result: output/run1_HEMTH4BHCWXWE044I0SK.json
2025-09-24 16:22:47,270 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ad3777c9-67d4-4aed-bfbc-2c50e180e82c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:47 GMT', 'content-type': 'application/json', 'content-length': '599', 'connection': 'keep-alive', 'x-amzn-requestid': 'ad3777c9-67d4-4aed-bfbc-2c50e180e82c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify doc_type. The content shows "STRAIGHT BILL OF LADING - SHORT FORM - ORIGINAL - NOT NEGOTIABLE". So it\'s a Bill of Lading. Therefore doc_type "bol". Should we consider it as combined? No, just one header. So output for page 1: bol. Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2933, 'outputTokens': 90, 'totalTokens': 3023}, 'metrics': {'latencyMs': 600}}
2025-09-24 16:22:47,270 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/abd6a4af_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:47,362 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cd8b69fe_HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:47,388 - INFO - 

P2EZG64EJI98NT69TSAL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:47,388 - INFO - 

✓ Saved result: output/run1_P2EZG64EJI98NT69TSAL.json
2025-09-24 16:22:47,679 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/abd6a4af_P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:48,265 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b96a52e2-3c02-46e3-8b4f-7023ee067ae3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:48 GMT', 'content-type': 'application/json', 'content-length': '1080', 'connection': 'keep-alive', 'x-amzn-requestid': 'b96a52e2-3c02-46e3-8b4f-7023ee067ae3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The content: DELIVERY RECEIPT, with details like PRO NUMBER, Shipper, Consignee, Received by, Delivery date, etc. It looks like a delivery receipt / proof of delivery? The header says "DELIVERY RECEIPT". The keywords: Received by, Delivery Date, Driver Name, etc. It looks like a Proof of Delivery (POD) type. The doc type for POD is "Proof of Delivery" with keywords "Proof of Delivery", "Delivery Ticket", "POD", Received by, Delivered. This page indeed contains Received by, Delivery details. So classify as pod.\n\nCheck if any other type? Not invoice. It\'s a POD. So output page 1 with doc_type pod.\n\nWe need to use tool classify_logistics_doc_type. Provide array with one object: page_no:1, doc_type:"pod".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2911, 'outputTokens': 199, 'totalTokens': 3110}, 'metrics': {'latencyMs': 1088}}
2025-09-24 16:22:48,265 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5a53c63e_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:48,281 - INFO - 

TQSZJLFFGGKB4NFN12RD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 16:22:48,281 - INFO - 

✓ Saved result: output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 16:22:48,632 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5a53c63e_TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:48,777 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '11089e16-3c11-48b3-b6ba-235dc378e7a1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:48 GMT', 'content-type': 'application/json', 'content-length': '1419', 'connection': 'keep-alive', 'x-amzn-requestid': '11089e16-3c11-48b3-b6ba-235dc378e7a1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We need to determine doc_type.\n\nLet\'s analyze the content. It\'s a freight bill, with "SEFL FREIGHT BILL NUMBER", "Receipt", appears to be a freight bill or invoice. There\'s "C/LINE AMOUNT", "TOTAL COLLECT", "CHARGES", "PAYMENT OF FREIGHT CHARGES". It includes weight & inspection review: "Wgt incr from 2216Lbs to 2330Lbs". It\'s a freight bill, which is a carrier invoice. Check if it\'s a commercial invoice? It doesn\'t mention HS codes, customs, etc. It has a freight bill number, origin, destination, etc. So likely "invoice". Not a lumper receipt. So doc_type = "invoice".\n\nWe must output JSON via the tool call classify_logistics_doc_type.\n\nWe need to output array of documents: each page\'s object should include page_no and doc_type. The input has a single page. So page_no 1, doc_type "invoice". Also it says if the document is a multi-page single document, each page gets same doc_type. But here just one.\n\nThus call:\n\n{\n  "documents": [\n    { "page_no": 1, "doc_type": "invoice" }\n  ]\n}\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3073, 'outputTokens': 287, 'totalTokens': 3360}, 'metrics': {'latencyMs': 1278}}
2025-09-24 16:22:48,777 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb58c43a_S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:48,808 - INFO - 

S92OW12RYFF5G3READPW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 16:22:48,808 - INFO - 

✓ Saved result: output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 16:22:49,070 - INFO - Page 1: Extracted 2992 characters, 114 lines from 481557bf_RCNPMJFRSZTXQWBGLB9R_20cd125d_page_001.pdf
2025-09-24 16:22:49,071 - INFO - Successfully processed page 1
2025-09-24 16:22:49,071 - INFO - Combined 1 pages into final text
2025-09-24 16:22:49,072 - INFO - Text validation for 481557bf_RCNPMJFRSZTXQWBGLB9R: 3009 characters, 1 pages
2025-09-24 16:22:49,073 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:49,073 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:49,095 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb58c43a_S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:49,636 - INFO - Page 1: Extracted 3032 characters, 118 lines from 6ece7f6c_PK5AIZFCER7DUFL15N01_df91c4b5_page_001.pdf
2025-09-24 16:22:49,637 - INFO - Successfully processed page 1
2025-09-24 16:22:49,637 - INFO - Combined 1 pages into final text
2025-09-24 16:22:49,637 - INFO - Text validation for 6ece7f6c_PK5AIZFCER7DUFL15N01: 3049 characters, 1 pages
2025-09-24 16:22:49,638 - INFO - Analyzing document types with Bedrock...
2025-09-24 16:22:49,638 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 16:22:51,086 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b2ead0a2-657c-4bf5-93f4-ac5eee9d81f3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:50 GMT', 'content-type': 'application/json', 'content-length': '641', 'connection': 'keep-alive', 'x-amzn-requestid': 'b2ead0a2-657c-4bf5-93f4-ac5eee9d81f3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It\'s a Bill of Lading. The page shows header "BILL OF LADING". So doc_type should be "bol". No continuation. So output JSON via tool call.\n\nWe need output: {"documents":[{page_no:1, doc_type:"bol"}]}\n\nUse tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_4bBpkZzlTBm_iDKCgRb6eg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3254, 'outputTokens': 82, 'totalTokens': 3336}, 'metrics': {'latencyMs': 595}}
2025-09-24 16:22:51,086 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/481557bf_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:51,120 - INFO - 

RCNPMJFRSZTXQWBGLB9R.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:51,120 - INFO - 

✓ Saved result: output/run1_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 16:22:51,471 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/481557bf_RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:51,474 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7a56e7d1-0c43-4680-bc68-626feaa91e79', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 10:52:51 GMT', 'content-type': 'application/json', 'content-length': '502', 'connection': 'keep-alive', 'x-amzn-requestid': '7a56e7d1-0c43-4680-bc68-626feaa91e79'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. It contains a Bill of Lading. So doc_type "bol". The tool requires an array of objects with page_no and doc_type. Must output via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3272, 'outputTokens': 62, 'totalTokens': 3334}, 'metrics': {'latencyMs': 546}}
2025-09-24 16:22:51,475 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6ece7f6c_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:51,519 - INFO - 

PK5AIZFCER7DUFL15N01.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 16:22:51,519 - INFO - 

✓ Saved result: output/run1_PK5AIZFCER7DUFL15N01.json
2025-09-24 16:22:51,803 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ece7f6c_PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:51,804 - INFO - 
📊 Processing Summary:
2025-09-24 16:22:51,805 - INFO -    Total files: 16
2025-09-24 16:22:51,805 - INFO -    Successful: 16
2025-09-24 16:22:51,805 - INFO -    Failed: 0
2025-09-24 16:22:51,805 - INFO -    Duration: 24.98 seconds
2025-09-24 16:22:51,805 - INFO -    Output directory: output
2025-09-24 16:22:51,805 - INFO - 
📋 Successfully Processed Files:
2025-09-24 16:22:51,805 - INFO -    📄 AOYIL346IKT06LKO6D2R.jpg: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 16:22:51,805 - INFO -    📄 DV1B199A0TJGUQ3FETIU.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"bol"}]}
2025-09-24 16:22:51,805 - INFO -    📄 GKYUUU2YTZ1YTPGXHO51.pdf: {"documents":[{"page_no":1,"doc_type":"pod"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"}]}
2025-09-24 16:22:51,805 - INFO -    📄 HEMTH4BHCWXWE044I0SK.pdf: {"documents":[{"page_no":1,"doc_type":"bol"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 16:22:51,805 - INFO -    📄 I6IM526GOVKAWH0B5WKE.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 16:22:51,806 - INFO -    📄 IJW9IJUOROYY1SWO0B17.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:22:51,806 - INFO -    📄 K88AFHJU4HIQUZYTMOF4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:22:51,806 - INFO -    📄 KK3PKFLS4ROTEZ7LOMMD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 16:22:51,806 - INFO -    📄 KUYJRWRDFCC2FB7SKI9T.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 16:22:51,806 - INFO -    📄 P2EZG64EJI98NT69TSAL.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:22:51,806 - INFO -    📄 P6RAGXD27RW4QUXN96TH.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 16:22:51,806 - INFO -    📄 PK5AIZFCER7DUFL15N01.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:22:51,806 - INFO -    📄 RCNPMJFRSZTXQWBGLB9R.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-24 16:22:51,806 - INFO -    📄 S92OW12RYFF5G3READPW.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 16:22:51,806 - INFO -    📄 TQSZJLFFGGKB4NFN12RD.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-24 16:22:51,806 - INFO -    📄 YC2ROS9NUB31VO0ABEV5.jpeg: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-24 16:22:51,807 - INFO - 
============================================================================================================================================
2025-09-24 16:22:51,807 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 16:22:51,807 - INFO - ============================================================================================================================================
2025-09-24 16:22:51,807 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 16:22:51,807 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:22:51,807 - INFO - AOYIL346IKT06LKO6D2R.jpg                           1      pod                                      run1_AOYIL346IKT06LKO6D2R.json                                                  
2025-09-24 16:22:51,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg
2025-09-24 16:22:51,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 16:22:51,808 - INFO - 
2025-09-24 16:22:51,808 - INFO - DV1B199A0TJGUQ3FETIU.pdf                           1      bol                                      run1_DV1B199A0TJGUQ3FETIU.json                                                  
2025-09-24 16:22:51,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:51,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:51,808 - INFO - 
2025-09-24 16:22:51,808 - INFO - DV1B199A0TJGUQ3FETIU.pdf                           2      bol                                      run1_DV1B199A0TJGUQ3FETIU.json                                                  
2025-09-24 16:22:51,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:51,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:51,808 - INFO - 
2025-09-24 16:22:51,808 - INFO - DV1B199A0TJGUQ3FETIU.pdf                           3      bol                                      run1_DV1B199A0TJGUQ3FETIU.json                                                  
2025-09-24 16:22:51,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/DV1B199A0TJGUQ3FETIU.pdf
2025-09-24 16:22:51,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:51,808 - INFO - 
2025-09-24 16:22:51,808 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           1      pod                                      run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 16:22:51,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:51,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:51,808 - INFO - 
2025-09-24 16:22:51,808 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           2      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 16:22:51,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:51,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:51,809 - INFO - 
2025-09-24 16:22:51,809 - INFO - GKYUUU2YTZ1YTPGXHO51.pdf                           3      invoice                                  run1_GKYUUU2YTZ1YTPGXHO51.json                                                  
2025-09-24 16:22:51,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf
2025-09-24 16:22:51,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:51,809 - INFO - 
2025-09-24 16:22:51,809 - INFO - HEMTH4BHCWXWE044I0SK.pdf                           1      bol                                      run1_HEMTH4BHCWXWE044I0SK.json                                                  
2025-09-24 16:22:51,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:51,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HEMTH4BHCWXWE044I0SK.json
2025-09-24 16:22:51,809 - INFO - 
2025-09-24 16:22:51,809 - INFO - HEMTH4BHCWXWE044I0SK.pdf                           2      bol                                      run1_HEMTH4BHCWXWE044I0SK.json                                                  
2025-09-24 16:22:51,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/HEMTH4BHCWXWE044I0SK.pdf
2025-09-24 16:22:51,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HEMTH4BHCWXWE044I0SK.json
2025-09-24 16:22:51,809 - INFO - 
2025-09-24 16:22:51,809 - INFO - I6IM526GOVKAWH0B5WKE.pdf                           1      pod                                      run1_I6IM526GOVKAWH0B5WKE.json                                                  
2025-09-24 16:22:51,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf
2025-09-24 16:22:51,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 16:22:51,809 - INFO - 
2025-09-24 16:22:51,809 - INFO - IJW9IJUOROYY1SWO0B17.pdf                           1      bol                                      run1_IJW9IJUOROYY1SWO0B17.json                                                  
2025-09-24 16:22:51,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/IJW9IJUOROYY1SWO0B17.pdf
2025-09-24 16:22:51,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IJW9IJUOROYY1SWO0B17.json
2025-09-24 16:22:51,810 - INFO - 
2025-09-24 16:22:51,810 - INFO - K88AFHJU4HIQUZYTMOF4.pdf                           1      bol                                      run1_K88AFHJU4HIQUZYTMOF4.json                                                  
2025-09-24 16:22:51,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/K88AFHJU4HIQUZYTMOF4.pdf
2025-09-24 16:22:51,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 16:22:51,810 - INFO - 
2025-09-24 16:22:51,810 - INFO - KK3PKFLS4ROTEZ7LOMMD.pdf                           1      pod                                      run1_KK3PKFLS4ROTEZ7LOMMD.json                                                  
2025-09-24 16:22:51,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf
2025-09-24 16:22:51,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 16:22:51,810 - INFO - 
2025-09-24 16:22:51,810 - INFO - KUYJRWRDFCC2FB7SKI9T.jpeg                          1      pack_list                                run1_KUYJRWRDFCC2FB7SKI9T.json                                                  
2025-09-24 16:22:51,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KUYJRWRDFCC2FB7SKI9T.jpeg
2025-09-24 16:22:51,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 16:22:51,810 - INFO - 
2025-09-24 16:22:51,810 - INFO - P2EZG64EJI98NT69TSAL.pdf                           1      bol                                      run1_P2EZG64EJI98NT69TSAL.json                                                  
2025-09-24 16:22:51,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/P2EZG64EJI98NT69TSAL.pdf
2025-09-24 16:22:51,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_P2EZG64EJI98NT69TSAL.json
2025-09-24 16:22:51,810 - INFO - 
2025-09-24 16:22:51,810 - INFO - P6RAGXD27RW4QUXN96TH.jpeg                          1      pack_list                                run1_P6RAGXD27RW4QUXN96TH.json                                                  
2025-09-24 16:22:51,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/P6RAGXD27RW4QUXN96TH.jpeg
2025-09-24 16:22:51,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_P6RAGXD27RW4QUXN96TH.json
2025-09-24 16:22:51,811 - INFO - 
2025-09-24 16:22:51,811 - INFO - PK5AIZFCER7DUFL15N01.pdf                           1      bol                                      run1_PK5AIZFCER7DUFL15N01.json                                                  
2025-09-24 16:22:51,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/PK5AIZFCER7DUFL15N01.pdf
2025-09-24 16:22:51,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PK5AIZFCER7DUFL15N01.json
2025-09-24 16:22:51,811 - INFO - 
2025-09-24 16:22:51,811 - INFO - RCNPMJFRSZTXQWBGLB9R.pdf                           1      bol                                      run1_RCNPMJFRSZTXQWBGLB9R.json                                                  
2025-09-24 16:22:51,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/RCNPMJFRSZTXQWBGLB9R.pdf
2025-09-24 16:22:51,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 16:22:51,811 - INFO - 
2025-09-24 16:22:51,811 - INFO - S92OW12RYFF5G3READPW.pdf                           1      invoice                                  run1_S92OW12RYFF5G3READPW.json                                                  
2025-09-24 16:22:51,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf
2025-09-24 16:22:51,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_S92OW12RYFF5G3READPW.json
2025-09-24 16:22:51,811 - INFO - 
2025-09-24 16:22:51,811 - INFO - TQSZJLFFGGKB4NFN12RD.pdf                           1      pod                                      run1_TQSZJLFFGGKB4NFN12RD.json                                                  
2025-09-24 16:22:51,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf
2025-09-24 16:22:51,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 16:22:51,811 - INFO - 
2025-09-24 16:22:51,812 - INFO - YC2ROS9NUB31VO0ABEV5.jpeg                          1      pack_list                                run1_YC2ROS9NUB31VO0ABEV5.json                                                  
2025-09-24 16:22:51,812 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/YC2ROS9NUB31VO0ABEV5.jpeg
2025-09-24 16:22:51,812 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 16:22:51,812 - INFO - 
2025-09-24 16:22:51,812 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 16:22:51,812 - INFO - Total entries: 21
2025-09-24 16:22:51,812 - INFO - ============================================================================================================================================
2025-09-24 16:22:51,812 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 16:22:51,812 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:22:51,812 - INFO -   1. AOYIL346IKT06LKO6D2R.jpg            Page 1   → pod             | run1_AOYIL346IKT06LKO6D2R.json
2025-09-24 16:22:51,812 - INFO -   2. DV1B199A0TJGUQ3FETIU.pdf            Page 1   → bol             | run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:51,812 - INFO -   3. DV1B199A0TJGUQ3FETIU.pdf            Page 2   → bol             | run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:51,812 - INFO -   4. DV1B199A0TJGUQ3FETIU.pdf            Page 3   → bol             | run1_DV1B199A0TJGUQ3FETIU.json
2025-09-24 16:22:51,812 - INFO -   5. GKYUUU2YTZ1YTPGXHO51.pdf            Page 1   → pod             | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:51,812 - INFO -   6. GKYUUU2YTZ1YTPGXHO51.pdf            Page 2   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:51,813 - INFO -   7. GKYUUU2YTZ1YTPGXHO51.pdf            Page 3   → invoice         | run1_GKYUUU2YTZ1YTPGXHO51.json
2025-09-24 16:22:51,813 - INFO -   8. HEMTH4BHCWXWE044I0SK.pdf            Page 1   → bol             | run1_HEMTH4BHCWXWE044I0SK.json
2025-09-24 16:22:51,813 - INFO -   9. HEMTH4BHCWXWE044I0SK.pdf            Page 2   → bol             | run1_HEMTH4BHCWXWE044I0SK.json
2025-09-24 16:22:51,813 - INFO -  10. I6IM526GOVKAWH0B5WKE.pdf            Page 1   → pod             | run1_I6IM526GOVKAWH0B5WKE.json
2025-09-24 16:22:51,813 - INFO -  11. IJW9IJUOROYY1SWO0B17.pdf            Page 1   → bol             | run1_IJW9IJUOROYY1SWO0B17.json
2025-09-24 16:22:51,813 - INFO -  12. K88AFHJU4HIQUZYTMOF4.pdf            Page 1   → bol             | run1_K88AFHJU4HIQUZYTMOF4.json
2025-09-24 16:22:51,813 - INFO -  13. KK3PKFLS4ROTEZ7LOMMD.pdf            Page 1   → pod             | run1_KK3PKFLS4ROTEZ7LOMMD.json
2025-09-24 16:22:51,813 - INFO -  14. KUYJRWRDFCC2FB7SKI9T.jpeg           Page 1   → pack_list       | run1_KUYJRWRDFCC2FB7SKI9T.json
2025-09-24 16:22:51,813 - INFO -  15. P2EZG64EJI98NT69TSAL.pdf            Page 1   → bol             | run1_P2EZG64EJI98NT69TSAL.json
2025-09-24 16:22:51,813 - INFO -  16. P6RAGXD27RW4QUXN96TH.jpeg           Page 1   → pack_list       | run1_P6RAGXD27RW4QUXN96TH.json
2025-09-24 16:22:51,813 - INFO -  17. PK5AIZFCER7DUFL15N01.pdf            Page 1   → bol             | run1_PK5AIZFCER7DUFL15N01.json
2025-09-24 16:22:51,813 - INFO -  18. RCNPMJFRSZTXQWBGLB9R.pdf            Page 1   → bol             | run1_RCNPMJFRSZTXQWBGLB9R.json
2025-09-24 16:22:51,813 - INFO -  19. S92OW12RYFF5G3READPW.pdf            Page 1   → invoice         | run1_S92OW12RYFF5G3READPW.json
2025-09-24 16:22:51,814 - INFO -  20. TQSZJLFFGGKB4NFN12RD.pdf            Page 1   → pod             | run1_TQSZJLFFGGKB4NFN12RD.json
2025-09-24 16:22:51,814 - INFO -  21. YC2ROS9NUB31VO0ABEV5.jpeg           Page 1   → pack_list       | run1_YC2ROS9NUB31VO0ABEV5.json
2025-09-24 16:22:51,814 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 16:22:51,814 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 24.980627, 'processed_files': [{'filename': 'AOYIL346IKT06LKO6D2R.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/AOYIL346IKT06LKO6D2R.jpg'}, {'filename': 'DV1B199A0TJGUQ3FETIU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}, {'page_no': 3, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/DV1B199A0TJGUQ3FETIU.pdf'}, {'filename': 'GKYUUU2YTZ1YTPGXHO51.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/GKYUUU2YTZ1YTPGXHO51.pdf'}, {'filename': 'HEMTH4BHCWXWE044I0SK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/HEMTH4BHCWXWE044I0SK.pdf'}, {'filename': 'I6IM526GOVKAWH0B5WKE.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/I6IM526GOVKAWH0B5WKE.pdf'}, {'filename': 'IJW9IJUOROYY1SWO0B17.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/IJW9IJUOROYY1SWO0B17.pdf'}, {'filename': 'K88AFHJU4HIQUZYTMOF4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/K88AFHJU4HIQUZYTMOF4.pdf'}, {'filename': 'KK3PKFLS4ROTEZ7LOMMD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KK3PKFLS4ROTEZ7LOMMD.pdf'}, {'filename': 'KUYJRWRDFCC2FB7SKI9T.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/KUYJRWRDFCC2FB7SKI9T.jpeg'}, {'filename': 'P2EZG64EJI98NT69TSAL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/P2EZG64EJI98NT69TSAL.pdf'}, {'filename': 'P6RAGXD27RW4QUXN96TH.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/P6RAGXD27RW4QUXN96TH.jpeg'}, {'filename': 'PK5AIZFCER7DUFL15N01.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/PK5AIZFCER7DUFL15N01.pdf'}, {'filename': 'RCNPMJFRSZTXQWBGLB9R.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/RCNPMJFRSZTXQWBGLB9R.pdf'}, {'filename': 'S92OW12RYFF5G3READPW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/S92OW12RYFF5G3READPW.pdf'}, {'filename': 'TQSZJLFFGGKB4NFN12RD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/TQSZJLFFGGKB4NFN12RD.pdf'}, {'filename': 'YC2ROS9NUB31VO0ABEV5.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/pod/YC2ROS9NUB31VO0ABEV5.jpeg'}]}
