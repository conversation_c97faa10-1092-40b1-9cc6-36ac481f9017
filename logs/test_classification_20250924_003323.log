2025-09-24 00:33:23,144 - INFO - Logging initialized. Log file: logs/test_classification_20250924_003323.log
2025-09-24 00:33:23,145 - INFO - 📁 Found 9 files to process
2025-09-24 00:33:23,145 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 00:33:23,145 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 00:33:23,145 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 00:33:23,146 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 00:33:23,146 - INFO - ⬆️ [00:33:23] Uploading: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:25,051 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf -> s3://document-extraction-logistically/temp/dd2400e6_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:25,051 - INFO - 🔍 [00:33:25] Starting classification: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:25,053 - INFO - ⬆️ [00:33:25] Uploading: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:25,055 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:25,094 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:25,103 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dd2400e6_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:25,103 - INFO - Processing PDF from S3...
2025-09-24 00:33:25,104 - INFO - Downloading PDF from S3 to /tmp/tmpwiddywdt/dd2400e6_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:26,453 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf -> s3://document-extraction-logistically/temp/871a2723_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:26,453 - INFO - 🔍 [00:33:26] Starting classification: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:26,455 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:26,456 - INFO - ⬆️ [00:33:26] Uploading: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:26,509 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:26,514 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/871a2723_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:26,523 - INFO - Processing PDF from S3...
2025-09-24 00:33:26,523 - INFO - Downloading PDF from S3 to /tmp/tmp66uz4g3r/871a2723_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:26,656 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:33:26,656 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:26,664 - INFO - Splitting PDF dd2400e6_CZ7K9JE7PH88JUBC19JF into 1 pages
2025-09-24 00:33:26,736 - INFO - Split PDF into 1 pages
2025-09-24 00:33:26,736 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:26,736 - INFO - Expected pages: [1]
2025-09-24 00:33:27,429 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf -> s3://document-extraction-logistically/temp/e075ecbb_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:27,430 - INFO - 🔍 [00:33:27] Starting classification: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:27,432 - INFO - ⬆️ [00:33:27] Uploading: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:27,435 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:27,481 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:27,488 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e075ecbb_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:27,497 - INFO - Processing PDF from S3...
2025-09-24 00:33:27,497 - INFO - Downloading PDF from S3 to /tmp/tmph8vvt4mp/e075ecbb_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:28,152 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf -> s3://document-extraction-logistically/temp/68fd6cd1_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:28,153 - INFO - 🔍 [00:33:28] Starting classification: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:28,155 - INFO - ⬆️ [00:33:28] Uploading: GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:28,157 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:28,195 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:28,200 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/68fd6cd1_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:28,200 - INFO - Processing PDF from S3...
2025-09-24 00:33:28,200 - INFO - Downloading PDF from S3 to /tmp/tmphfpa1fgd/68fd6cd1_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:28,775 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf -> s3://document-extraction-logistically/temp/15850952_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:28,775 - INFO - 🔍 [00:33:28] Starting classification: GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:28,777 - INFO - ⬆️ [00:33:28] Uploading: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:28,777 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:28,819 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:28,825 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15850952_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:28,825 - INFO - Processing PDF from S3...
2025-09-24 00:33:28,825 - INFO - Downloading PDF from S3 to /tmp/tmp__darpym/15850952_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:29,121 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 00:33:29,121 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:29,123 - INFO - Splitting PDF 871a2723_DGAKPGYVH59IXR7KRKZS into 4 pages
2025-09-24 00:33:29,146 - INFO - Split PDF into 4 pages
2025-09-24 00:33:29,146 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:29,146 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 00:33:29,409 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf -> s3://document-extraction-logistically/temp/40b62ff7_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:29,409 - INFO - 🔍 [00:33:29] Starting classification: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:29,412 - INFO - ⬆️ [00:33:29] Uploading: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:29,414 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:29,471 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:29,476 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40b62ff7_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:29,477 - INFO - Processing PDF from S3...
2025-09-24 00:33:29,477 - INFO - Downloading PDF from S3 to /tmp/tmpplpv7vh0/40b62ff7_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:29,822 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 00:33:29,822 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:29,823 - INFO - Splitting PDF e075ecbb_ECY73YCNA7IPQSM8NAKW into 1 pages
2025-09-24 00:33:29,824 - INFO - Split PDF into 1 pages
2025-09-24 00:33:29,824 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:29,824 - INFO - Expected pages: [1]
2025-09-24 00:33:30,106 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf -> s3://document-extraction-logistically/temp/61040a4a_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:30,107 - INFO - 🔍 [00:33:30] Starting classification: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:30,110 - INFO - ⬆️ [00:33:30] Uploading: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:30,110 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:30,151 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:30,163 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/61040a4a_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:30,164 - INFO - Processing PDF from S3...
2025-09-24 00:33:30,164 - INFO - Downloading PDF from S3 to /tmp/tmp_d81wiuj/61040a4a_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:30,181 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 00:33:30,181 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:30,183 - INFO - Splitting PDF 68fd6cd1_EDYM381JJDTUB87YAAPI into 1 pages
2025-09-24 00:33:30,192 - INFO - Split PDF into 1 pages
2025-09-24 00:33:30,192 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:30,192 - INFO - Expected pages: [1]
2025-09-24 00:33:30,289 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 00:33:30,290 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:30,292 - INFO - Splitting PDF 15850952_GMCKX2ERTX05S300COLL into 3 pages
2025-09-24 00:33:30,308 - INFO - Split PDF into 3 pages
2025-09-24 00:33:30,308 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:30,308 - INFO - Expected pages: [1, 2, 3]
2025-09-24 00:33:30,743 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf -> s3://document-extraction-logistically/temp/4edd9b34_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:30,744 - INFO - 🔍 [00:33:30] Starting classification: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:30,746 - INFO - ⬆️ [00:33:30] Uploading: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:30,748 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:30,795 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:30,804 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4edd9b34_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:30,804 - INFO - Processing PDF from S3...
2025-09-24 00:33:30,804 - INFO - Downloading PDF from S3 to /tmp/tmpzxlk34pe/4edd9b34_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:31,222 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:33:31,222 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:31,227 - INFO - Splitting PDF 40b62ff7_T51X0UC3WJL168AL2PGB into 1 pages
2025-09-24 00:33:31,244 - INFO - Split PDF into 1 pages
2025-09-24 00:33:31,244 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:31,244 - INFO - Expected pages: [1]
2025-09-24 00:33:31,475 - INFO - Page 1: Extracted 1384 characters, 71 lines from dd2400e6_CZ7K9JE7PH88JUBC19JF_1084242b_page_001.pdf
2025-09-24 00:33:31,476 - INFO - Successfully processed page 1
2025-09-24 00:33:31,477 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf -> s3://document-extraction-logistically/temp/91f5ad54_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:31,477 - INFO - Combined 1 pages into final text
2025-09-24 00:33:31,477 - INFO - 🔍 [00:33:31] Starting classification: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:31,477 - INFO - Text validation for dd2400e6_CZ7K9JE7PH88JUBC19JF: 1401 characters, 1 pages
2025-09-24 00:33:31,479 - INFO - Initializing TextractProcessor...
2025-09-24 00:33:31,482 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:31,488 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:31,498 - INFO - Initializing BedrockProcessor...
2025-09-24 00:33:31,509 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/91f5ad54_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:31,509 - INFO - Processing PDF from S3...
2025-09-24 00:33:31,509 - INFO - Downloading PDF from S3 to /tmp/tmp3i9kzhd3/91f5ad54_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:32,461 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 00:33:32,461 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:32,464 - INFO - Splitting PDF 61040a4a_TCM8BE9P052RZLZGHQIW into 1 pages
2025-09-24 00:33:32,468 - INFO - Split PDF into 1 pages
2025-09-24 00:33:32,469 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:32,469 - INFO - Expected pages: [1]
2025-09-24 00:33:32,553 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 00:33:32,554 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:32,556 - INFO - Splitting PDF 4edd9b34_V44T1WF2N7RT33JSFU5F into 1 pages
2025-09-24 00:33:32,571 - INFO - Split PDF into 1 pages
2025-09-24 00:33:32,571 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:32,571 - INFO - Expected pages: [1]
2025-09-24 00:33:32,941 - INFO - Page 3: Extracted 541 characters, 39 lines from 871a2723_DGAKPGYVH59IXR7KRKZS_370a52dd_page_003.pdf
2025-09-24 00:33:32,942 - INFO - Successfully processed page 3
2025-09-24 00:33:33,308 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dd2400e6_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:33,433 - INFO - Page 2: Extracted 579 characters, 42 lines from 871a2723_DGAKPGYVH59IXR7KRKZS_370a52dd_page_002.pdf
2025-09-24 00:33:33,442 - INFO - Successfully processed page 2
2025-09-24 00:33:33,671 - INFO - Page 3: Extracted 526 characters, 49 lines from 15850952_GMCKX2ERTX05S300COLL_d62e745f_page_003.pdf
2025-09-24 00:33:33,672 - INFO - Successfully processed page 3
2025-09-24 00:33:33,756 - INFO - 

CZ7K9JE7PH88JUBC19JF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:33,756 - INFO - 

✓ Saved result: output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:33:33,775 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 00:33:33,776 - INFO - Splitting PDF into individual pages...
2025-09-24 00:33:33,776 - INFO - Splitting PDF 91f5ad54_Z106V9IKGLMUAGC3VOK8 into 2 pages
2025-09-24 00:33:33,781 - INFO - Split PDF into 2 pages
2025-09-24 00:33:33,781 - INFO - Processing pages with Textract in parallel...
2025-09-24 00:33:33,781 - INFO - Expected pages: [1, 2]
2025-09-24 00:33:34,063 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dd2400e6_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:34,482 - INFO - Page 2: Extracted 1411 characters, 134 lines from 15850952_GMCKX2ERTX05S300COLL_d62e745f_page_002.pdf
2025-09-24 00:33:34,482 - INFO - Successfully processed page 2
2025-09-24 00:33:35,009 - INFO - Page 1: Extracted 2957 characters, 137 lines from 871a2723_DGAKPGYVH59IXR7KRKZS_370a52dd_page_001.pdf
2025-09-24 00:33:35,009 - INFO - Successfully processed page 1
2025-09-24 00:33:35,047 - INFO - Page 4: Extracted 3255 characters, 170 lines from 871a2723_DGAKPGYVH59IXR7KRKZS_370a52dd_page_004.pdf
2025-09-24 00:33:35,047 - INFO - Successfully processed page 4
2025-09-24 00:33:35,048 - INFO - Combined 4 pages into final text
2025-09-24 00:33:35,049 - INFO - Text validation for 871a2723_DGAKPGYVH59IXR7KRKZS: 7406 characters, 4 pages
2025-09-24 00:33:35,050 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:35,050 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:35,200 - INFO - Page 1: Extracted 1271 characters, 100 lines from 15850952_GMCKX2ERTX05S300COLL_d62e745f_page_001.pdf
2025-09-24 00:33:35,201 - INFO - Successfully processed page 1
2025-09-24 00:33:35,201 - INFO - Combined 3 pages into final text
2025-09-24 00:33:35,201 - INFO - Text validation for 15850952_GMCKX2ERTX05S300COLL: 3263 characters, 3 pages
2025-09-24 00:33:35,202 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:35,202 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:35,507 - INFO - Page 1: Extracted 1163 characters, 75 lines from 68fd6cd1_EDYM381JJDTUB87YAAPI_90db05fa_page_001.pdf
2025-09-24 00:33:35,507 - INFO - Successfully processed page 1
2025-09-24 00:33:35,508 - INFO - Combined 1 pages into final text
2025-09-24 00:33:35,508 - INFO - Text validation for 68fd6cd1_EDYM381JJDTUB87YAAPI: 1180 characters, 1 pages
2025-09-24 00:33:35,508 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:35,508 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:36,028 - INFO - Page 1: Extracted 1545 characters, 72 lines from e075ecbb_ECY73YCNA7IPQSM8NAKW_992ea2d3_page_001.pdf
2025-09-24 00:33:36,028 - INFO - Successfully processed page 1
2025-09-24 00:33:36,028 - INFO - Combined 1 pages into final text
2025-09-24 00:33:36,028 - INFO - Text validation for e075ecbb_ECY73YCNA7IPQSM8NAKW: 1562 characters, 1 pages
2025-09-24 00:33:36,029 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:36,029 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:37,231 - INFO - Page 1: Extracted 2010 characters, 210 lines from 40b62ff7_T51X0UC3WJL168AL2PGB_7bede124_page_001.pdf
2025-09-24 00:33:37,231 - INFO - Successfully processed page 1
2025-09-24 00:33:37,232 - INFO - Combined 1 pages into final text
2025-09-24 00:33:37,232 - INFO - Text validation for 40b62ff7_T51X0UC3WJL168AL2PGB: 2027 characters, 1 pages
2025-09-24 00:33:37,232 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:37,233 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:37,315 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/68fd6cd1_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:37,697 - INFO - 

EDYM381JJDTUB87YAAPI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:37,697 - INFO - 

✓ Saved result: output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:33:37,745 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15850952_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:37,922 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e075ecbb_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:38,010 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/68fd6cd1_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:38,983 - INFO - Page 2: Extracted 727 characters, 48 lines from 91f5ad54_Z106V9IKGLMUAGC3VOK8_05063542_page_002.pdf
2025-09-24 00:33:38,997 - INFO - Successfully processed page 2
2025-09-24 00:33:39,074 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40b62ff7_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:39,179 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/871a2723_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:39,200 - INFO - 

GMCKX2ERTX05S300COLL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:39,200 - INFO - 

✓ Saved result: output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:39,468 - INFO - Page 1: Extracted 1341 characters, 86 lines from 61040a4a_TCM8BE9P052RZLZGHQIW_da25a52f_page_001.pdf
2025-09-24 00:33:39,468 - INFO - Successfully processed page 1
2025-09-24 00:33:39,469 - INFO - Combined 1 pages into final text
2025-09-24 00:33:39,470 - INFO - Text validation for 61040a4a_TCM8BE9P052RZLZGHQIW: 1358 characters, 1 pages
2025-09-24 00:33:39,470 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:39,470 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:39,483 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15850952_GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:39,597 - INFO - Page 1: Extracted 1789 characters, 98 lines from 91f5ad54_Z106V9IKGLMUAGC3VOK8_05063542_page_001.pdf
2025-09-24 00:33:39,600 - INFO - Successfully processed page 1
2025-09-24 00:33:39,628 - INFO - Combined 2 pages into final text
2025-09-24 00:33:39,641 - INFO - Text validation for 91f5ad54_Z106V9IKGLMUAGC3VOK8: 2552 characters, 2 pages
2025-09-24 00:33:39,687 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:39,696 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:39,992 - INFO - Page 1: Extracted 4288 characters, 110 lines from 4edd9b34_V44T1WF2N7RT33JSFU5F_bd60e369_page_001.pdf
2025-09-24 00:33:39,994 - INFO - Successfully processed page 1
2025-09-24 00:33:39,994 - INFO - Combined 1 pages into final text
2025-09-24 00:33:40,004 - INFO - Text validation for 4edd9b34_V44T1WF2N7RT33JSFU5F: 4305 characters, 1 pages
2025-09-24 00:33:40,052 - INFO - Analyzing document types with Bedrock...
2025-09-24 00:33:40,056 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 00:33:40,120 - INFO - 

ECY73YCNA7IPQSM8NAKW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:40,120 - INFO - 

✓ Saved result: output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:33:40,425 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e075ecbb_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:41,278 - INFO - 

T51X0UC3WJL168AL2PGB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:41,278 - INFO - 

✓ Saved result: output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:33:41,403 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/61040a4a_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:41,606 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/40b62ff7_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:43,383 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4edd9b34_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:43,756 - INFO - 

DGAKPGYVH59IXR7KRKZS.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 4,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 00:33:43,756 - INFO - 

✓ Saved result: output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:44,077 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/871a2723_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:44,496 - INFO - 

TCM8BE9P052RZLZGHQIW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:44,496 - INFO - 

✓ Saved result: output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:33:44,781 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/61040a4a_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:45,845 - INFO - 

V44T1WF2N7RT33JSFU5F.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:45,845 - INFO - 

✓ Saved result: output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:33:46,142 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4edd9b34_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:47,135 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/91f5ad54_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:47,883 - INFO - 

Z106V9IKGLMUAGC3VOK8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 00:33:47,883 - INFO - 

✓ Saved result: output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:33:48,251 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/91f5ad54_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:48,253 - INFO - 
📊 Processing Summary:
2025-09-24 00:33:48,253 - INFO -    Total files: 9
2025-09-24 00:33:48,253 - INFO -    Successful: 9
2025-09-24 00:33:48,254 - INFO -    Failed: 0
2025-09-24 00:33:48,254 - INFO -    Duration: 25.11 seconds
2025-09-24 00:33:48,254 - INFO -    Output directory: output
2025-09-24 00:33:48,254 - INFO - 
📋 Successfully Processed Files:
2025-09-24 00:33:48,255 - INFO -    📄 CZ7K9JE7PH88JUBC19JF.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,255 - INFO -    📄 DGAKPGYVH59IXR7KRKZS.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"comm_invoice"},{"page_no":4,"doc_type":"customs_doc"}]}
2025-09-24 00:33:48,255 - INFO -    📄 ECY73YCNA7IPQSM8NAKW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,255 - INFO -    📄 EDYM381JJDTUB87YAAPI.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,256 - INFO -    📄 GMCKX2ERTX05S300COLL.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,256 - INFO -    📄 T51X0UC3WJL168AL2PGB.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,256 - INFO -    📄 TCM8BE9P052RZLZGHQIW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,257 - INFO -    📄 V44T1WF2N7RT33JSFU5F.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,257 - INFO -    📄 Z106V9IKGLMUAGC3VOK8.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"}]}
2025-09-24 00:33:48,259 - INFO - 
============================================================================================================================================
2025-09-24 00:33:48,259 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 00:33:48,259 - INFO - ============================================================================================================================================
2025-09-24 00:33:48,260 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 00:33:48,260 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:33:48,260 - INFO - CZ7K9JE7PH88JUBC19JF.pdf                           1      comm_invoice         run1_CZ7K9JE7PH88JUBC19JF.json                    
2025-09-24 00:33:48,260 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 00:33:48,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:33:48,261 - INFO - 
2025-09-24 00:33:48,261 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           1      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:33:48,261 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:48,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,261 - INFO - 
2025-09-24 00:33:48,262 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           2      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:33:48,262 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:48,262 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,262 - INFO - 
2025-09-24 00:33:48,262 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           3      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:33:48,262 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:48,262 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,263 - INFO - 
2025-09-24 00:33:48,263 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           4      customs_doc          run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 00:33:48,263 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 00:33:48,263 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,263 - INFO - 
2025-09-24 00:33:48,263 - INFO - ECY73YCNA7IPQSM8NAKW.pdf                           1      comm_invoice         run1_ECY73YCNA7IPQSM8NAKW.json                    
2025-09-24 00:33:48,264 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 00:33:48,264 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:33:48,264 - INFO - 
2025-09-24 00:33:48,264 - INFO - EDYM381JJDTUB87YAAPI.pdf                           1      comm_invoice         run1_EDYM381JJDTUB87YAAPI.json                    
2025-09-24 00:33:48,264 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf
2025-09-24 00:33:48,264 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:33:48,265 - INFO - 
2025-09-24 00:33:48,265 - INFO - GMCKX2ERTX05S300COLL.pdf                           1      comm_invoice         run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:33:48,265 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:48,265 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:48,265 - INFO - 
2025-09-24 00:33:48,265 - INFO - GMCKX2ERTX05S300COLL.pdf                           2      comm_invoice         run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:33:48,266 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:48,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:48,266 - INFO - 
2025-09-24 00:33:48,266 - INFO - GMCKX2ERTX05S300COLL.pdf                           3      comm_invoice         run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 00:33:48,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 00:33:48,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:48,267 - INFO - 
2025-09-24 00:33:48,268 - INFO - T51X0UC3WJL168AL2PGB.pdf                           1      comm_invoice         run1_T51X0UC3WJL168AL2PGB.json                    
2025-09-24 00:33:48,268 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf
2025-09-24 00:33:48,268 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:33:48,268 - INFO - 
2025-09-24 00:33:48,268 - INFO - TCM8BE9P052RZLZGHQIW.pdf                           1      comm_invoice         run1_TCM8BE9P052RZLZGHQIW.json                    
2025-09-24 00:33:48,269 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 00:33:48,269 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:33:48,269 - INFO - 
2025-09-24 00:33:48,269 - INFO - V44T1WF2N7RT33JSFU5F.pdf                           1      comm_invoice         run1_V44T1WF2N7RT33JSFU5F.json                    
2025-09-24 00:33:48,269 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 00:33:48,269 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:33:48,269 - INFO - 
2025-09-24 00:33:48,269 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           1      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 00:33:48,269 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:48,270 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:33:48,270 - INFO - 
2025-09-24 00:33:48,270 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           2      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 00:33:48,270 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 00:33:48,270 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:33:48,270 - INFO - 
2025-09-24 00:33:48,270 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 00:33:48,270 - INFO - Total entries: 15
2025-09-24 00:33:48,271 - INFO - ============================================================================================================================================
2025-09-24 00:33:48,271 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 00:33:48,271 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:33:48,271 - INFO -   1. CZ7K9JE7PH88JUBC19JF.pdf            Page 1   → comm_invoice    | run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 00:33:48,271 - INFO -   2. DGAKPGYVH59IXR7KRKZS.pdf            Page 1   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,271 - INFO -   3. DGAKPGYVH59IXR7KRKZS.pdf            Page 2   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,271 - INFO -   4. DGAKPGYVH59IXR7KRKZS.pdf            Page 3   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,271 - INFO -   5. DGAKPGYVH59IXR7KRKZS.pdf            Page 4   → customs_doc     | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 00:33:48,272 - INFO -   6. ECY73YCNA7IPQSM8NAKW.pdf            Page 1   → comm_invoice    | run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 00:33:48,272 - INFO -   7. EDYM381JJDTUB87YAAPI.pdf            Page 1   → comm_invoice    | run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 00:33:48,272 - INFO -   8. GMCKX2ERTX05S300COLL.pdf            Page 1   → comm_invoice    | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:48,272 - INFO -   9. GMCKX2ERTX05S300COLL.pdf            Page 2   → comm_invoice    | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:48,272 - INFO -  10. GMCKX2ERTX05S300COLL.pdf            Page 3   → comm_invoice    | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 00:33:48,272 - INFO -  11. T51X0UC3WJL168AL2PGB.pdf            Page 1   → comm_invoice    | run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 00:33:48,272 - INFO -  12. TCM8BE9P052RZLZGHQIW.pdf            Page 1   → comm_invoice    | run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 00:33:48,272 - INFO -  13. V44T1WF2N7RT33JSFU5F.pdf            Page 1   → comm_invoice    | run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 00:33:48,272 - INFO -  14. Z106V9IKGLMUAGC3VOK8.pdf            Page 1   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:33:48,273 - INFO -  15. Z106V9IKGLMUAGC3VOK8.pdf            Page 2   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 00:33:48,273 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 00:33:48,273 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 25.107414, 'processed_files': [{'filename': 'CZ7K9JE7PH88JUBC19JF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf'}, {'filename': 'DGAKPGYVH59IXR7KRKZS.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'comm_invoice'}, {'page_no': 4, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf'}, {'filename': 'ECY73YCNA7IPQSM8NAKW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf'}, {'filename': 'EDYM381JJDTUB87YAAPI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/EDYM381JJDTUB87YAAPI.pdf'}, {'filename': 'GMCKX2ERTX05S300COLL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/GMCKX2ERTX05S300COLL.pdf'}, {'filename': 'T51X0UC3WJL168AL2PGB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/T51X0UC3WJL168AL2PGB.pdf'}, {'filename': 'TCM8BE9P052RZLZGHQIW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf'}, {'filename': 'V44T1WF2N7RT33JSFU5F.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf'}, {'filename': 'Z106V9IKGLMUAGC3VOK8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf'}]}
