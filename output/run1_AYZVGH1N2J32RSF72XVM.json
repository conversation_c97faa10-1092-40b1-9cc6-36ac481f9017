{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 7.813345348495204e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 4.457437171367928e-06, "Y": 1.0}]}, "Id": "fd5966b9-ce65-4f68-a795-9fa1d830d4eb", "Relationships": [{"Type": "CHILD", "Ids": ["ddd614ce-9ca4-4d66-bae9-7920bac5d01b", "33326665-16c0-4118-957a-493119a0161e", "7762366b-0a5b-4433-8b68-632401ef2a0a", "26478176-c270-460c-b2b6-0261d813c71a", "013b0ea8-5086-480b-aaf3-863c2b1626f7", "09170ff4-d494-4907-bbb8-99458f409b5a", "26a341cf-0b6a-4244-b325-773e36ec0613", "92c2f1ed-ba13-4103-b3a3-dddd5b7c1ab5", "720dcb36-443a-4dda-9c28-0e7588c2871b", "8fc6dac1-468b-494b-8e82-4ef240fb149b", "659a3e6f-fd59-461a-9583-977e952122d5", "8e34748b-e3d1-4822-8412-e8c46ed2033d", "14160c75-7c06-42a3-bed7-d558395cb2b8", "7cf22665-b6af-48c0-9e7d-be86c60a442c", "8a805306-c68b-43d5-bdff-6cd71af34e58", "af095b00-98af-4f41-a1fe-17c47e9ffecc", "e6f82ca4-2447-420c-9631-332f63516a1d", "f164c004-fda2-4311-9566-af2fe408b91a"]}]}, {"BlockType": "LINE", "Confidence": 98.5549545288086, "Text": "TSL Ingate: OMA", "Geometry": {"BoundingBox": {"Width": 0.24465952813625336, "Height": 0.02773892506957054, "Left": 0.039359889924526215, "Top": 0.015461914241313934}, "Polygon": [{"X": 0.039359889924526215, "Y": 0.015505875460803509}, {"X": 0.2840156555175781, "Y": 0.015461914241313934}, {"X": 0.284019410610199, "Y": 0.043157100677490234}, {"X": 0.03936382383108139, "Y": 0.043200839310884476}]}, "Id": "ddd614ce-9ca4-4d66-bae9-7920bac5d01b", "Relationships": [{"Type": "CHILD", "Ids": ["1ab7a803-1065-426d-8917-4cae68c527d3", "52974dc1-1bec-41d0-b6e4-a6f737f123a5", "a061a6b2-078f-4e06-810f-13d4e9446fb6"]}]}, {"BlockType": "LINE", "Confidence": 97.46040344238281, "Text": "ActivityDate: 08/15/2025 13:59", "Geometry": {"BoundingBox": {"Width": 0.46721112728118896, "Height": 0.03853963315486908, "Left": 0.031465545296669006, "Top": 0.030731603503227234}, "Polygon": [{"X": 0.031465545296669006, "Y": 0.03081532195210457}, {"X": 0.49867165088653564, "Y": 0.030731603503227234}, {"X": 0.4986766576766968, "Y": 0.06918810307979584}, {"X": 0.03147101774811745, "Y": 0.06927123665809631}]}, "Id": "33326665-16c0-4118-957a-493119a0161e", "Relationships": [{"Type": "CHILD", "Ids": ["477cf1e2-b941-44a0-a8f8-cfc28b5de331", "8b62a307-7440-4d22-9504-1ff72f5b637d", "6a9003fa-a33c-46e9-9efb-a28f5aab7707"]}]}, {"BlockType": "LINE", "Confidence": 99.746337890625, "Text": "Carrier Inspector CALHOUN TRUCK LINES", "Geometry": {"BoundingBox": {"Width": 0.47423338890075684, "Height": 0.047100771218538284, "Left": 0.030899053439497948, "Top": 0.07010301947593689}, "Polygon": [{"X": 0.030899053439497948, "Y": 0.07018738985061646}, {"X": 0.5051262974739075, "Y": 0.07010301947593689}, {"X": 0.5051324367523193, "Y": 0.1171201542019844}, {"X": 0.030905745923519135, "Y": 0.11720379441976547}]}, "Id": "7762366b-0a5b-4433-8b68-632401ef2a0a", "Relationships": [{"Type": "CHILD", "Ids": ["cd743b96-9eb7-480a-93de-e681b025cf9a", "c30e9eb8-5685-4cba-84ad-8cf18dcb786b", "eda9ed5e-23c7-42a1-89c1-35f6bb148db0", "dbb8b32e-75ed-4e03-ab96-37d4e0f12d25", "46f2a963-a482-4eae-bc4f-d47ded50136d"]}]}, {"BlockType": "LINE", "Confidence": 21.44730567932129, "Text": "COG", "Geometry": {"BoundingBox": {"Width": 0.8956819772720337, "Height": 0.054371681064367294, "Left": 0.03478820621967316, "Top": 0.1116977408528328}, "Polygon": [{"X": 0.03478820621967316, "Y": 0.1118558719754219}, {"X": 0.9304637312889099, "Y": 0.1116977408528328}, {"X": 0.9304702281951904, "Y": 0.16591288149356842}, {"X": 0.03479591757059097, "Y": 0.1660694181919098}]}, "Id": "26478176-c270-460c-b2b6-0261d813c71a", "Relationships": [{"Type": "CHILD", "Ids": ["1ffe7c24-c845-4694-8bbb-815a9937da68"]}]}, {"BlockType": "LINE", "Confidence": 94.51475524902344, "Text": "Loaded: No Hazardous:No Lifted:Yes", "Geometry": {"BoundingBox": {"Width": 0.5308886766433716, "Height": 0.033359069377183914, "Left": 0.038105327636003494, "Top": 0.15732228755950928}, "Polygon": [{"X": 0.038105327636003494, "Y": 0.1574152261018753}, {"X": 0.5689897537231445, "Y": 0.15732228755950928}, {"X": 0.5689940452575684, "Y": 0.19058899581432343}, {"X": 0.03811005502939224, "Y": 0.1906813532114029}]}, "Id": "013b0ea8-5086-480b-aaf3-863c2b1626f7", "Relationships": [{"Type": "CHILD", "Ids": ["9cd5c801-1814-43e7-bbd8-023c7991ebee", "0cda3578-3d2d-42ef-8c67-c9acfe6209e1", "55026030-b005-413a-99f1-e2b3adfdc518", "5f8bd77b-d414-48bb-be14-0ad575df7d7b"]}]}, {"BlockType": "LINE", "Confidence": 93.7721176147461, "Text": "Grade: BF", "Geometry": {"BoundingBox": {"Width": 0.13867881894111633, "Height": 0.02295946329832077, "Left": 0.039920318871736526, "Top": 0.19381310045719147}, "Polygon": [{"X": 0.039920318871736526, "Y": 0.1938372105360031}, {"X": 0.17859596014022827, "Y": 0.19381310045719147}, {"X": 0.17859914898872375, "Y": 0.21674856543540955}, {"X": 0.03992357850074768, "Y": 0.21677257120609283}]}, "Id": "09170ff4-d494-4907-bbb8-99458f409b5a", "Relationships": [{"Type": "CHILD", "Ids": ["6dbeef77-053d-4a12-bdc4-100e7090df6a", "46d76150-4bf7-4f55-8f19-c0fba76b92e2"]}]}, {"BlockType": "LINE", "Confidence": 97.73896026611328, "Text": "Seal: Genset:", "Geometry": {"BoundingBox": {"Width": 0.18687833845615387, "Height": 0.022029463201761246, "Left": 0.04212194308638573, "Top": 0.22016273438930511}, "Polygon": [{"X": 0.04212194308638573, "Y": 0.22019506990909576}, {"X": 0.22899726033210754, "Y": 0.22016273438930511}, {"X": 0.2290002852678299, "Y": 0.24216000735759735}, {"X": 0.042125068604946136, "Y": 0.24219219386577606}]}, "Id": "26a341cf-0b6a-4244-b325-773e36ec0613", "Relationships": [{"Type": "CHILD", "Ids": ["eaaa121f-08ab-4e0d-9034-cdd67b97ec99", "272e84e6-773d-4eaa-86d3-bebf116a1e54"]}]}, {"BlockType": "LINE", "Confidence": 94.12799835205078, "Text": "Apmz405128 40CH Owner:DIRECTCHASSISLINKING", "Geometry": {"BoundingBox": {"Width": 0.761511504650116, "Height": 0.03805212676525116, "Left": 0.04364318028092384, "Top": 0.23328445851802826}, "Polygon": [{"X": 0.04364318028092384, "Y": 0.233415886759758}, {"X": 0.805150032043457, "Y": 0.23328445851802826}, {"X": 0.8051546812057495, "Y": 0.2712061107158661}, {"X": 0.04364856705069542, "Y": 0.2713365852832794}]}, "Id": "92c2f1ed-ba13-4103-b3a3-dddd5b7c1ab5", "Relationships": [{"Type": "CHILD", "Ids": ["eba8784e-b796-40e0-b87f-7bfa3e17dfd6", "bc6f734c-07cd-403f-b262-aaaaa45dabca", "03ebe590-91ab-4a32-9289-2638ab36e463"]}]}, {"BlockType": "LINE", "Confidence": 97.29681396484375, "Text": "Genset: Hubmeter:", "Geometry": {"BoundingBox": {"Width": 0.2739682197570801, "Height": 0.023086631670594215, "Left": 0.04797565937042236, "Top": 0.2686631381511688}, "Polygon": [{"X": 0.04797565937042236, "Y": 0.26871010661125183}, {"X": 0.3219407796859741, "Y": 0.2686631381511688}, {"X": 0.32194387912750244, "Y": 0.2917030155658722}, {"X": 0.047978926450014114, "Y": 0.2917497754096985}]}, "Id": "720dcb36-443a-4dda-9c28-0e7588c2871b", "Relationships": [{"Type": "CHILD", "Ids": ["d5b2d714-da69-4113-b5b2-b2c50bf66ab2", "ac700831-218e-4c98-a00e-9c17f1e83136"]}]}, {"BlockType": "LINE", "Confidence": 97.50526428222656, "Text": "I hereby certify that on the date stated above the person who conducted the", "Geometry": {"BoundingBox": {"Width": 0.8884634375572205, "Height": 0.026504915207624435, "Left": 0.052961647510528564, "Top": 0.3108457326889038}, "Polygon": [{"X": 0.052961647510528564, "Y": 0.3109968304634094}, {"X": 0.9414219260215759, "Y": 0.3108457326889038}, {"X": 0.941425085067749, "Y": 0.3372003436088562}, {"X": 0.05296538397669792, "Y": 0.33735066652297974}]}, "Id": "8fc6dac1-468b-494b-8e82-4ef240fb149b", "Relationships": [{"Type": "CHILD", "Ids": ["5e8552d0-fe8a-41bc-8a86-5705f3b43698", "cb70ac78-bf9d-4924-b25d-2836f88f7bb1", "45ccdcbb-b182-4469-9498-ed28b1380f18", "b4e7c216-1b27-4e56-8e11-0564a2e59522", "12520db1-0cb6-4c0b-bafd-e023200146fe", "93f0302e-dd6d-4fa0-bec3-b86f53d99a12", "e90da3ba-ff1f-4895-b31a-0dd52ea32b90", "90f13c2b-0c5f-4112-b97c-c00ff3c3767e", "6f2762a5-0f58-4db3-b148-f3097f5a5e1b", "304ccb09-47e2-4211-b52f-43eaa8f8122d", "10a9bf51-d5f5-4330-b42d-22eeda670ed6", "cab79880-31d2-4be6-8682-9bb7fb6150ab", "c1106812-431e-499b-9c12-e3848e7e6b19", "614ffcaa-ad2a-480d-9d6e-226885aa6818"]}]}, {"BlockType": "LINE", "Confidence": 94.36017608642578, "Text": "Inspection covered by this report was competent and qualified to do so and", "Geometry": {"BoundingBox": {"Width": 0.8731081485748291, "Height": 0.024114493280649185, "Left": 0.05162716656923294, "Top": 0.33084073662757874}, "Polygon": [{"X": 0.05162716656923294, "Y": 0.33098864555358887}, {"X": 0.9247324466705322, "Y": 0.33084073662757874}, {"X": 0.9247353076934814, "Y": 0.3548080027103424}, {"X": 0.05163056403398514, "Y": 0.3549552261829376}]}, "Id": "659a3e6f-fd59-461a-9583-977e952122d5", "Relationships": [{"Type": "CHILD", "Ids": ["47163788-70e3-4acf-a9e5-0e48608f3bb4", "82e58e4d-1313-447d-816b-0814d34e6930", "b8df7eec-8d8f-4684-937f-650bb1d4bb1c", "7a7723fd-bb8a-435f-9deb-fc558d04a5cf", "b9331491-13db-4c9a-a6a1-8842b2c07ef1", "91b0be5d-3787-44d1-b2cf-c78780c60926", "1abaad37-13aa-401e-aac2-974332ee<PERSON><PERSON>e", "1c22f4b6-acdd-4b40-909c-86b227eac69b", "f01ec58b-28c4-45c5-b5a5-445c5d84fb0e", "43633d0f-73df-4552-8cff-39e3890f26de", "f34d9322-d788-42bb-bf88-135adec97e73", "07d1cb71-5e59-4b80-8db8-968c8d561eca", "126d84f9-c9d0-4aba-90eb-6dfd82e8f9b9"]}]}, {"BlockType": "LINE", "Confidence": 96.4016342163086, "Text": "was duty authorized to inspect and take possession of the equipment as a", "Geometry": {"BoundingBox": {"Width": 0.8501176238059998, "Height": 0.024983368813991547, "Left": 0.04958534985780716, "Top": 0.350536048412323}, "Polygon": [{"X": 0.04958534985780716, "Y": 0.35067951679229736}, {"X": 0.8996999859809875, "Y": 0.350536048412323}, {"X": 0.8997029662132263, "Y": 0.3753766417503357}, {"X": 0.049588873982429504, "Y": 0.37551942467689514}]}, "Id": "8e34748b-e3d1-4822-8412-e8c46ed2033d", "Relationships": [{"Type": "CHILD", "Ids": ["b820a8d2-4356-4b77-aa3d-fe1e1248230d", "7ca010e6-74cd-4cc4-a0c3-b33df70b618b", "e1ff82ac-1993-4f20-8b3c-35b8cbd62e64", "aa01dafe-b8ce-45f1-9567-a4442e592b4d", "c729926e-e72e-4ebb-aaea-a7bf60a36166", "7818c08d-1595-4299-82ea-9232a7153f76", "fd6bd5bf-7c8c-4469-bc13-b32ec209961e", "84f6f0d6-e529-4b6f-b4e4-0cb219210b6c", "36430e0a-a314-47f1-9724-3be7a84b5676", "16b0700b-bb7a-4252-962b-838b739d58b3", "3d284c4f-1014-46d4-88e3-506d053e3f61", "a8fc817b-5b8d-449f-88f7-6b8e0e072390", "2b48320b-7500-414c-b0d8-b6b1284198fb"]}]}, {"BlockType": "LINE", "Confidence": 92.9823226928711, "Text": "representative of the acquiring carrier. In consideration of the acceptance", "Geometry": {"BoundingBox": {"Width": 0.8496787548065186, "Height": 0.02799217402935028, "Left": 0.04829128459095955, "Top": 0.36970600485801697}, "Polygon": [{"X": 0.04829128459095955, "Y": 0.3698488473892212}, {"X": 0.8979666829109192, "Y": 0.36970600485801697}, {"X": 0.8979700803756714, "Y": 0.3975560665130615}, {"X": 0.04829523712396622, "Y": 0.39769816398620605}]}, "Id": "14160c75-7c06-42a3-bed7-d558395cb2b8", "Relationships": [{"Type": "CHILD", "Ids": ["d739529f-e52f-4b64-b2ba-d7ede0af3d88", "43c9187f-ce5e-485b-bb00-323fddd3137e", "2c2b07b3-970e-48ea-94e4-954316ff8a25", "929070ba-4575-403f-b050-389d902a4621", "202a53bf-192f-4a68-84d1-3ae71eca9aa3", "d0441f0e-9605-4f19-a344-649966b80f1c", "995c68d6-2fd0-4ee9-830a-ca9fe75b365b", "c686a57f-8234-4594-8b98-a52c93e1eb6a", "92a362f8-228e-43ca-9df6-3c214b16ee1d", "b9f51fcb-cbb3-4dc2-9340-0d404c9145db"]}]}, {"BlockType": "LINE", "Confidence": 97.2555160522461, "Text": "of the above container for storage. the Carrier acknowledges that all", "Geometry": {"BoundingBox": {"Width": 0.7930896282196045, "Height": 0.025572296231985092, "Left": 0.046886052936315536, "Top": 0.3888850808143616}, "Polygon": [{"X": 0.046886052936315536, "Y": 0.3890179395675659}, {"X": 0.8399726152420044, "Y": 0.3888850808143616}, {"X": 0.8399757146835327, "Y": 0.41432517766952515}, {"X": 0.046889662742614746, "Y": 0.41445738077163696}]}, "Id": "7cf22665-b6af-48c0-9e7d-be86c60a442c", "Relationships": [{"Type": "CHILD", "Ids": ["e5d31d0c-6cff-4cbc-bc64-124f02cc5115", "d3939192-2e1a-4fc0-9422-f105deb0ea73", "53d42384-117e-4922-8268-26abdb690b72", "7f4916a4-7177-4cd1-b495-f36b80440b41", "d24e1d19-028b-446b-9368-6bbd3af54572", "2417cf52-6fea-4c64-bf32-e830cea16a75", "1c8780fe-b928-4259-808c-768bdbbfcff2", "66ef889e-5a67-441d-87a9-22ab59273333", "cf6b23b2-e442-4184-bb26-b4ea5172014f", "4cfe6c64-0451-4eb9-b2e0-f75243a0568f", "31cbdaf6-d4f4-41f0-a325-dbd09dc5fced"]}]}, {"BlockType": "LINE", "Confidence": 92.20732116699219, "Text": "freight charges have been paid or agrees to look salely to the consignor.", "Geometry": {"BoundingBox": {"Width": 0.8403946757316589, "Height": 0.02852640673518181, "Left": 0.04450959712266922, "Top": 0.4079294502735138}, "Polygon": [{"X": 0.04450959712266922, "Y": 0.4080697000026703}, {"X": 0.8849008083343506, "Y": 0.4079294502735138}, {"X": 0.8849042654037476, "Y": 0.4363163709640503}, {"X": 0.044513627886772156, "Y": 0.4364558458328247}]}, "Id": "8a805306-c68b-43d5-bdff-6cd71af34e58", "Relationships": [{"Type": "CHILD", "Ids": ["b4bc7013-1ee0-44e5-b937-d31ae8d374d8", "16360d30-07b4-4b4d-8ad2-d105aae810e8", "2c6bd893-57eb-4936-85f0-6746b28d120f", "1326adbe-49c7-4772-8310-a91e815ba43a", "f58f0ab3-49c1-4dd0-a236-99a9d7835571", "3cff94f1-8362-485e-bb81-f44daf45f6cd", "cd112b9f-0d5a-458b-a167-70dac2525787", "6b9dd421-986f-4d3f-9672-64103a71aac0", "ec04304d-a352-478c-b465-75a472eb96f3", "1ef38da1-b397-44ce-9813-48ce042d8ffc", "feedb17a-51bf-4534-8666-ddde1b13fa92", "a76c03e5-830c-4ccb-b1c1-c1cce3cb46f8", "6a3772a0-72c2-4011-aa4c-bcd1bbc688d2"]}]}, {"BlockType": "LINE", "Confidence": 94.66529846191406, "Text": "broker, or the beneficial owner of the freight for payment. The Corrier", "Geometry": {"BoundingBox": {"Width": 0.8136122226715088, "Height": 0.027236610651016235, "Left": 0.04394690692424774, "Top": 0.42703157663345337}, "Polygon": [{"X": 0.04394690692424774, "Y": 0.4271668493747711}, {"X": 0.8575558662414551, "Y": 0.42703157663345337}, {"X": 0.8575591444969177, "Y": 0.4541335999965668}, {"X": 0.04395075514912605, "Y": 0.4542681872844696}]}, "Id": "af095b00-98af-4f41-a1fe-17c47e9ffecc", "Relationships": [{"Type": "CHILD", "Ids": ["ba21d69f-517e-45de-a7db-5661c46ef3b4", "fa4f5412-3743-4ee1-bb01-a72a30a2b03d", "c3db133c-5317-4822-8baa-e3caa2a3bd17", "50772ee6-865f-4887-a44d-2e74f400bb6d", "de89cc8e-cd6b-408e-9097-5ea6bcbe4584", "606d328c-7673-4c2f-ac95-e63768647384", "ea264668-9230-4550-8879-dfac4bf451bd", "72c7360a-e9f5-4f35-9f16-74e5581a0f0e", "0b17c2ad-5b1e-4f0a-a6f9-ca78d8cd482a", "f0e11c48-4e14-49ee-8259-f28b47b8844f", "c847bf44-b10b-4bd1-af57-cd958ea5e71b", "84998faf-95f2-4cf7-81c0-7ecb187a7fbd"]}]}, {"BlockType": "LINE", "Confidence": 99.1118392944336, "Text": "waives any right it may have to pursue TSL for payment.", "Geometry": {"BoundingBox": {"Width": 0.654319167137146, "Height": 0.027147721499204636, "Left": 0.04229499027132988, "Top": 0.4470602571964264}, "Polygon": [{"X": 0.04229499027132988, "Y": 0.44716864824295044}, {"X": 0.6966108083724976, "Y": 0.4470602571964264}, {"X": 0.6966142058372498, "Y": 0.4741002023220062}, {"X": 0.042298831045627594, "Y": 0.4742079973220825}]}, "Id": "e6f82ca4-2447-420c-9631-332f63516a1d", "Relationships": [{"Type": "CHILD", "Ids": ["eee2d46f-0f20-4c68-8a29-5e81527fbe06", "5e2f2291-bc18-47cd-bddb-b4f345ace4ea", "2d25abdc-f0ec-44ae-bc94-d78397775ae0", "aa3c5fbf-3ff1-4404-8f5b-7bf560e5e1cb", "3cd2e186-d765-4532-8f82-5b2cddef6c95", "b5c8f055-76af-450f-bf05-e1c0f47a2721", "b7b18c4d-537a-4dbe-a5db-7cd41db7a8f6", "10c08798-b6ed-44ee-a815-3bc1aa0f10af", "301dfc62-90f5-4069-8b87-608dd58b9fe0", "014064f0-d785-42ef-9dc9-e746c637b1c5", "407557c6-1e8f-41e8-bcc3-129f2bedb126"]}]}, {"BlockType": "LINE", "Confidence": 99.40250396728516, "Text": "Sign here", "Geometry": {"BoundingBox": {"Width": 0.1422746330499649, "Height": 0.025157682597637177, "Left": 0.04167556017637253, "Top": 0.4771602153778076}, "Polygon": [{"X": 0.04167556017637253, "Y": 0.47718364000320435}, {"X": 0.18394671380519867, "Y": 0.4771602153778076}, {"X": 0.18395018577575684, "Y": 0.5022946000099182}, {"X": 0.04167913272976875, "Y": 0.5023179054260254}]}, "Id": "f164c004-fda2-4311-9566-af2fe408b91a", "Relationships": [{"Type": "CHILD", "Ids": ["aeece569-0322-4b5a-ba7a-7ec8bffc0b01", "1e095991-e61a-4dba-b78e-dbade207a809"]}]}, {"BlockType": "WORD", "Confidence": 99.64604187011719, "Text": "TSL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049904122948646545, "Height": 0.018383700400590897, "Left": 0.03936121240258217, "Top": 0.02481713891029358}, "Polygon": [{"X": 0.03936121240258217, "Y": 0.024826090782880783}, {"X": 0.08926274627447128, "Y": 0.02481713891029358}, {"X": 0.08926533907651901, "Y": 0.04319191724061966}, {"X": 0.03936382383108139, "Y": 0.043200839310884476}]}, "Id": "1ab7a803-1065-426d-8917-4cae68c527d3"}, {"BlockType": "WORD", "Confidence": 96.33689880371094, "Text": "Ingate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10224048793315887, "Height": 0.021643884479999542, "Left": 0.0959751233458519, "Top": 0.02094125561416149}, "Polygon": [{"X": 0.0959751233458519, "Y": 0.020959608256816864}, {"X": 0.1982126235961914, "Y": 0.02094125561416149}, {"X": 0.19821561872959137, "Y": 0.04256685823202133}, {"X": 0.09597817063331604, "Y": 0.042585138231515884}]}, "Id": "52974dc1-1bec-41d0-b6e4-a6f737f123a5"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "OMA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0775766596198082, "Height": 0.01885310560464859, "Left": 0.20644155144691467, "Top": 0.015461914241313934}, "Polygon": [{"X": 0.20644155144691467, "Y": 0.015475853346288204}, {"X": 0.2840156555175781, "Y": 0.015461914241313934}, {"X": 0.28401821851730347, "Y": 0.03430112823843956}, {"X": 0.2064441442489624, "Y": 0.034315019845962524}]}, "Id": "a061a6b2-078f-4e06-810f-13d4e9446fb6"}, {"BlockType": "WORD", "Confidence": 93.62942504882812, "Text": "ActivityDate:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19091224670410156, "Height": 0.0265765730291605, "Left": 0.031467240303754807, "Top": 0.042694661766290665}, "Polygon": [{"X": 0.031467240303754807, "Y": 0.04272879660129547}, {"X": 0.22237583994865417, "Y": 0.042694661766290665}, {"X": 0.22237949073314667, "Y": 0.06923726946115494}, {"X": 0.03147101774811745, "Y": 0.06927123665809631}]}, "Id": "477cf1e2-b941-44a0-a8f8-cfc28b5de331"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "08/15/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17585493624210358, "Height": 0.026147346943616867, "Left": 0.23037055134773254, "Top": 0.034354813396930695}, "Polygon": [{"X": 0.23037055134773254, "Y": 0.03438630327582359}, {"X": 0.40622201561927795, "Y": 0.034354813396930695}, {"X": 0.4062255024909973, "Y": 0.0604708194732666}, {"X": 0.23037414252758026, "Y": 0.06050216034054756}]}, "Id": "8b62a307-7440-4d22-9504-1ff72f5b637d"}, {"BlockType": "WORD", "Confidence": 98.97640228271484, "Text": "13:59", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0818595439195633, "Height": 0.019882868975400925, "Left": 0.4168146848678589, "Top": 0.030731603503227234}, "Polygon": [{"X": 0.4168146848678589, "Y": 0.03074626997113228}, {"X": 0.49867165088653564, "Y": 0.030731603503227234}, {"X": 0.4986742436885834, "Y": 0.05059985816478729}, {"X": 0.4168173372745514, "Y": 0.05061447247862816}]}, "Id": "6a9003fa-a33c-46e9-9efb-a28f5aab7707"}, {"BlockType": "WORD", "Confidence": 99.63309478759766, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10772011429071426, "Height": 0.021185290068387985, "Left": 0.03090273216366768, "Top": 0.09601850062608719}, "Polygon": [{"X": 0.03090273216366768, "Y": 0.09603757411241531}, {"X": 0.13861988484859467, "Y": 0.09601850062608719}, {"X": 0.13862285017967224, "Y": 0.11718479543924332}, {"X": 0.030905745923519135, "Y": 0.11720379441976547}]}, "Id": "cd743b96-9eb7-480a-93de-e681b025cf9a"}, {"BlockType": "WORD", "Confidence": 99.41426086425781, "Text": "Inspector", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13984841108322144, "Height": 0.025443289428949356, "Left": 0.031330034136772156, "Top": 0.07016243040561676}, "Polygon": [{"X": 0.031330034136772156, "Y": 0.07018730789422989}, {"X": 0.17117491364479065, "Y": 0.07016243040561676}, {"X": 0.1711784452199936, "Y": 0.09558095782995224}, {"X": 0.03133365139365196, "Y": 0.09560572355985641}]}, "Id": "c30e9eb8-5685-4cba-84ad-8cf18dcb786b"}, {"BlockType": "WORD", "Confidence": 99.80229949951172, "Text": "CALHOUN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14924389123916626, "Height": 0.02140197716653347, "Left": 0.14471694827079773, "Top": 0.09062421321868896}, "Polygon": [{"X": 0.14471694827079773, "Y": 0.09065066277980804}, {"X": 0.2939579486846924, "Y": 0.09062421321868896}, {"X": 0.293960839509964, "Y": 0.11199983954429626}, {"X": 0.1447199285030365, "Y": 0.11202618479728699}]}, "Id": "eda9ed5e-23c7-42a1-89c1-35f6bb148db0"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "TRUCK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10793855786323547, "Height": 0.02020406164228916, "Left": 0.3002835512161255, "Top": 0.0861734077334404}, "Polygon": [{"X": 0.3002835512161255, "Y": 0.0861925557255745}, {"X": 0.40821945667266846, "Y": 0.0861734077334404}, {"X": 0.40822213888168335, "Y": 0.10635839402675629}, {"X": 0.30028629302978516, "Y": 0.10637746751308441}]}, "Id": "dbb8b32e-75ed-4e03-ab96-37d4e0f12d25"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "LINES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08917517960071564, "Height": 0.01959068328142166, "Left": 0.4159552752971649, "Top": 0.08234962075948715}, "Polygon": [{"X": 0.4159552752971649, "Y": 0.08236544579267502}, {"X": 0.5051279067993164, "Y": 0.08234962075948715}, {"X": 0.5051304697990417, "Y": 0.10192453116178513}, {"X": 0.41595786809921265, "Y": 0.10194030404090881}]}, "Id": "46f2a963-a482-4eae-bc4f-d47ded50136d"}, {"BlockType": "WORD", "Confidence": 21.44730567932129, "Text": "COG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.8956819772720337, "Height": 0.054371681064367294, "Left": 0.03478820621967316, "Top": 0.1116977408528328}, "Polygon": [{"X": 0.03478820621967316, "Y": 0.1118558719754219}, {"X": 0.9304637312889099, "Y": 0.1116977408528328}, {"X": 0.9304702281951904, "Y": 0.16591288149356842}, {"X": 0.03479591757059097, "Y": 0.1660694181919098}]}, "Id": "1ffe7c24-c845-4694-8bbb-815a9937da68"}, {"BlockType": "WORD", "Confidence": 95.44025421142578, "Text": "Loaded:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12036998569965363, "Height": 0.022383427247405052, "Left": 0.038106877356767654, "Top": 0.1682979315519333}, "Polygon": [{"X": 0.038106877356767654, "Y": 0.1683189570903778}, {"X": 0.15847374498844147, "Y": 0.1682979315519333}, {"X": 0.15847685933113098, "Y": 0.19066041707992554}, {"X": 0.03811005502939224, "Y": 0.1906813532114029}]}, "Id": "9cd5c801-1814-43e7-bbd8-023c7991ebee"}, {"BlockType": "WORD", "Confidence": 91.43395233154297, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04184575378894806, "Height": 0.015821153298020363, "Left": 0.16092555224895477, "Top": 0.16867420077323914}, "Polygon": [{"X": 0.16092555224895477, "Y": 0.16868151724338531}, {"X": 0.20276911556720734, "Y": 0.16867420077323914}, {"X": 0.20277130603790283, "Y": 0.18448807299137115}, {"X": 0.16092774271965027, "Y": 0.18449535965919495}]}, "Id": "0cda3578-3d2d-42ef-8c67-c9acfe6209e1"}, {"BlockType": "WORD", "Confidence": 95.56939697265625, "Text": "Hazardous:No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21104492247104645, "Height": 0.023618174716830254, "Left": 0.2089109718799591, "Top": 0.1604679971933365}, "Polygon": [{"X": 0.2089109718799591, "Y": 0.16050492227077484}, {"X": 0.41995278000831604, "Y": 0.1604679971933365}, {"X": 0.41995590925216675, "Y": 0.18404941260814667}, {"X": 0.20891423523426056, "Y": 0.1840861737728119}]}, "Id": "55026030-b005-413a-99f1-e2b3adfdc518"}, {"BlockType": "WORD", "Confidence": 95.61543273925781, "Text": "Lifted:Yes", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14094379544258118, "Height": 0.01827024295926094, "Left": 0.4280482828617096, "Top": 0.15732228755950928}, "Polygon": [{"X": 0.4280482828617096, "Y": 0.1573469638824463}, {"X": 0.5689897537231445, "Y": 0.15732228755950928}, {"X": 0.5689920783042908, "Y": 0.17556793987751007}, {"X": 0.428050696849823, "Y": 0.17559252679347992}]}, "Id": "5f8bd77b-d414-48bb-be14-0ad575df7d7b"}, {"BlockType": "WORD", "Confidence": 95.24095153808594, "Text": "Grade:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10019009560346603, "Height": 0.022864993661642075, "Left": 0.03992033377289772, "Top": 0.19390757381916046}, "Polygon": [{"X": 0.03992033377289772, "Y": 0.19392499327659607}, {"X": 0.14010722935199738, "Y": 0.19390757381916046}, {"X": 0.14011043310165405, "Y": 0.2167552262544632}, {"X": 0.03992357850074768, "Y": 0.21677257120609283}]}, "Id": "6dbeef77-053d-4a12-bdc4-100e7090df6a"}, {"BlockType": "WORD", "Confidence": 92.30329132080078, "Text": "BF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03504106402397156, "Height": 0.01760450378060341, "Left": 0.1435573399066925, "Top": 0.19381310045719147}, "Polygon": [{"X": 0.1435573399066925, "Y": 0.19381919503211975}, {"X": 0.17859596014022827, "Y": 0.19381310045719147}, {"X": 0.17859840393066406, "Y": 0.21141153573989868}, {"X": 0.1435597985982895, "Y": 0.21141761541366577}]}, "Id": "46d76150-4bf7-4f55-8f19-c0fba76b92e2"}, {"BlockType": "WORD", "Confidence": 98.95468139648438, "Text": "Seal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07237143814563751, "Height": 0.02000451646745205, "Left": 0.04212222620844841, "Top": 0.22218768298625946}, "Polygon": [{"X": 0.04212222620844841, "Y": 0.2222001999616623}, {"X": 0.11449086666107178, "Y": 0.22218768298625946}, {"X": 0.11449366807937622, "Y": 0.242179736495018}, {"X": 0.042125068604946136, "Y": 0.24219219386577606}]}, "Id": "eaaa121f-08ab-4e0d-9034-cdd67b97ec99"}, {"BlockType": "WORD", "Confidence": 96.52323913574219, "Text": "Genset:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1064411997795105, "Height": 0.017406200990080833, "Left": 0.12255845963954926, "Top": 0.22016273438930511}, "Polygon": [{"X": 0.12255845963954926, "Y": 0.2201811522245407}, {"X": 0.22899726033210754, "Y": 0.22016273438930511}, {"X": 0.22899965941905975, "Y": 0.23755058646202087}, {"X": 0.12256088852882385, "Y": 0.2375689446926117}]}, "Id": "272e84e6-773d-4eaa-86d3-bebf116a1e54"}, {"BlockType": "WORD", "Confidence": 97.79814910888672, "Text": "Apmz405128", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19366540014743805, "Height": 0.029242724180221558, "Left": 0.04364442080259323, "Top": 0.24209386110305786}, "Polygon": [{"X": 0.04364442080259323, "Y": 0.24212723970413208}, {"X": 0.23730581998825073, "Y": 0.24209386110305786}, {"X": 0.23730982840061188, "Y": 0.2713034152984619}, {"X": 0.04364856705069542, "Y": 0.2713365852832794}]}, "Id": "eba8784e-b796-40e0-b87f-7bfa3e17dfd6"}, {"BlockType": "WORD", "Confidence": 98.13397216796875, "Text": "40CH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07857346534729004, "Height": 0.018297825008630753, "Left": 0.24427564442157745, "Top": 0.2415504902601242}, "Polygon": [{"X": 0.24427564442157745, "Y": 0.24156403541564941}, {"X": 0.3228466510772705, "Y": 0.2415504902601242}, {"X": 0.3228491246700287, "Y": 0.25983482599258423}, {"X": 0.24427814781665802, "Y": 0.25984832644462585}]}, "Id": "bc6f734c-07cd-403f-b262-aaaaa45dabca"}, {"BlockType": "WORD", "Confidence": 86.45188903808594, "Text": "Owner:DIRECTCHASSISLIN<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.47348296642303467, "Height": 0.02370806410908699, "Left": 0.33166995644569397, "Top": 0.23328445851802826}, "Polygon": [{"X": 0.33166995644569397, "Y": 0.23336617648601532}, {"X": 0.805150032043457, "Y": 0.23328445851802826}, {"X": 0.8051528930664062, "Y": 0.25691118836402893}, {"X": 0.33167314529418945, "Y": 0.25699251890182495}]}, "Id": "03ebe590-91ab-4a32-9289-2638ab36e463"}, {"BlockType": "WORD", "Confidence": 98.69678497314453, "Text": "Genset:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11230316758155823, "Height": 0.019448228180408478, "Left": 0.04797616973519325, "Top": 0.2723015546798706}, "Polygon": [{"X": 0.04797616973519325, "Y": 0.27232077717781067}, {"X": 0.1602766364812851, "Y": 0.2723015546798706}, {"X": 0.16027933359146118, "Y": 0.2917306125164032}, {"X": 0.047978926450014114, "Y": 0.2917497754096985}]}, "Id": "d5b2d714-da69-4113-b5b2-b2c50bf66ab2"}, {"BlockType": "WORD", "Confidence": 95.89684295654297, "Text": "Hubmeter:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15280891954898834, "Height": 0.018336603417992592, "Left": 0.16913431882858276, "Top": 0.2686631381511688}, "Polygon": [{"X": 0.16913431882858276, "Y": 0.2686893343925476}, {"X": 0.3219407796859741, "Y": 0.2686631381511688}, {"X": 0.3219432532787323, "Y": 0.28697365522384644}, {"X": 0.1691368669271469, "Y": 0.28699976205825806}]}, "Id": "ac700831-218e-4c98-a00e-9c17f1e83136"}, {"BlockType": "WORD", "Confidence": 88.2957992553711, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006087555084377527, "Height": 0.012001746334135532, "Left": 0.05296342447400093, "Top": 0.3235416114330292}, "Polygon": [{"X": 0.05296342447400093, "Y": 0.32354265451431274}, {"X": 0.05904928222298622, "Y": 0.3235416114330292}, {"X": 0.05905098095536232, "Y": 0.33554232120513916}, {"X": 0.05296512693166733, "Y": 0.33554336428642273}]}, "Id": "5e8552d0-fe8a-41bc-8a86-5705f3b43698"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "hereby", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08022820949554443, "Height": 0.017058268189430237, "Left": 0.06556232273578644, "Top": 0.3202902674674988}, "Polygon": [{"X": 0.06556232273578644, "Y": 0.32030388712882996}, {"X": 0.14578816294670105, "Y": 0.3202902674674988}, {"X": 0.14579053223133087, "Y": 0.3373349606990814}, {"X": 0.06556472927331924, "Y": 0.3373485207557678}]}, "Id": "cb70ac78-bf9d-4924-b25d-2836f88f7bb1"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07301195710897446, "Height": 0.01767352968454361, "Left": 0.15136034786701202, "Top": 0.3180060386657715}, "Polygon": [{"X": 0.15136034786701202, "Y": 0.31801843643188477}, {"X": 0.2243698686361313, "Y": 0.3180060386657715}, {"X": 0.22437229752540588, "Y": 0.3356671929359436}, {"X": 0.151362806558609, "Y": 0.3356795608997345}]}, "Id": "45ccdcbb-b182-4469-9498-ed28b1380f18"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046941738575696945, "Height": 0.013230279088020325, "Left": 0.22974808514118195, "Top": 0.3181338906288147}, "Polygon": [{"X": 0.22974808514118195, "Y": 0.3181418776512146}, {"X": 0.27668800950050354, "Y": 0.3181338906288147}, {"X": 0.2766898274421692, "Y": 0.3313562273979187}, {"X": 0.2297499030828476, "Y": 0.3313641846179962}]}, "Id": "b4e7c216-1b27-4e56-8e11-0564a2e59522"}, {"BlockType": "WORD", "Confidence": 99.69328308105469, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028146320953965187, "Height": 0.009499738924205303, "Left": 0.2835807204246521, "Top": 0.3199842870235443}, "Polygon": [{"X": 0.2835807204246521, "Y": 0.31998905539512634}, {"X": 0.3117257356643677, "Y": 0.3199842870235443}, {"X": 0.31172704696655273, "Y": 0.32947924733161926}, {"X": 0.28358200192451477, "Y": 0.3294840157032013}]}, "Id": "12520db1-0cb6-4c0b-bafd-e023200146fe"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037719495594501495, "Height": 0.01385627593845129, "Left": 0.3181818425655365, "Top": 0.3149910569190979}, "Polygon": [{"X": 0.3181818425655365, "Y": 0.31499749422073364}, {"X": 0.35589948296546936, "Y": 0.3149910569190979}, {"X": 0.3559013307094574, "Y": 0.3288409411907196}, {"X": 0.3181837201118469, "Y": 0.32884734869003296}]}, "Id": "93f0302e-dd6d-4fa0-bec3-b86f53d99a12"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049972910434007645, "Height": 0.013413429260253906, "Left": 0.36313295364379883, "Top": 0.3141891062259674}, "Polygon": [{"X": 0.36313295364379883, "Y": 0.3141975998878479}, {"X": 0.4131040871143341, "Y": 0.3141891062259674}, {"X": 0.41310587525367737, "Y": 0.3275940716266632}, {"X": 0.3631347715854645, "Y": 0.3276025354862213}]}, "Id": "e90da3ba-ff1f-4895-b31a-0dd52ea32b90"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07032384723424911, "Height": 0.014378642663359642, "Left": 0.41935601830482483, "Top": 0.3124241232872009}, "Polygon": [{"X": 0.41935601830482483, "Y": 0.3124360740184784}, {"X": 0.4896779954433441, "Y": 0.3124241232872009}, {"X": 0.48967987298965454, "Y": 0.32679083943367004}, {"X": 0.41935792565345764, "Y": 0.3268027603626251}]}, "Id": "90f13c2b-0c5f-4112-b97c-c00ff3c3767e"}, {"BlockType": "WORD", "Confidence": 79.75127410888672, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07320590317249298, "Height": 0.013601352460682392, "Left": 0.4970913827419281, "Top": 0.3124698996543884}, "Polygon": [{"X": 0.4970913827419281, "Y": 0.3124823570251465}, {"X": 0.5702955722808838, "Y": 0.3124698996543884}, {"X": 0.5702973008155823, "Y": 0.32605883479118347}, {"X": 0.49709317088127136, "Y": 0.32607126235961914}]}, "Id": "6f2762a5-0f58-4db3-b148-f3097f5a5e1b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03730877488851547, "Height": 0.013654269278049469, "Left": 0.5775945782661438, "Top": 0.3122507631778717}, "Polygon": [{"X": 0.5775945782661438, "Y": 0.3122570812702179}, {"X": 0.614901602268219, "Y": 0.3122507631778717}, {"X": 0.6149033308029175, "Y": 0.3258987069129944}, {"X": 0.5775963068008423, "Y": 0.3259050250053406}]}, "Id": "304ccb09-47e2-4211-b52f-43eaa8f8122d"}, {"BlockType": "WORD", "Confidence": 98.25753784179688, "Text": "person", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08040416240692139, "Height": 0.013270371593534946, "Left": 0.6209564805030823, "Top": 0.31638601422309875}, "Polygon": [{"X": 0.6209564805030823, "Y": 0.3163996636867523}, {"X": 0.70135897397995, "Y": 0.31638601422309875}, {"X": 0.7013606429100037, "Y": 0.32964274287223816}, {"X": 0.620958149433136, "Y": 0.32965636253356934}]}, "Id": "10a9bf51-d5f5-4330-b42d-22eeda670ed6"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "who", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052097171545028687, "Height": 0.01371721737086773, "Left": 0.7073460221290588, "Top": 0.3118565082550049}, "Polygon": [{"X": 0.7073460221290588, "Y": 0.3118653893470764}, {"X": 0.7594414949417114, "Y": 0.3118565082550049}, {"X": 0.7594432234764099, "Y": 0.3255649209022522}, {"X": 0.7073477506637573, "Y": 0.32557374238967896}]}, "Id": "cab79880-31d2-4be6-8682-9bb7fb6150ab"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "conducted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.128463476896286, "Height": 0.014527339488267899, "Left": 0.7664714455604553, "Top": 0.31102287769317627}, "Polygon": [{"X": 0.7664714455604553, "Y": 0.31104472279548645}, {"X": 0.8949331641197205, "Y": 0.31102287769317627}, {"X": 0.8949349522590637, "Y": 0.32552844285964966}, {"X": 0.7664732336997986, "Y": 0.32555022835731506}]}, "Id": "c1106812-431e-499b-9c12-e3848e7e6b19"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04010381922125816, "Height": 0.013845609501004219, "Left": 0.9013198018074036, "Top": 0.3108457326889038}, "Polygon": [{"X": 0.9013198018074036, "Y": 0.3108525574207306}, {"X": 0.9414219260215759, "Y": 0.3108457326889038}, {"X": 0.9414235949516296, "Y": 0.3246845602989197}, {"X": 0.9013214707374573, "Y": 0.3246913552284241}]}, "Id": "614ffcaa-ad2a-480d-9d6e-226885aa6818"}, {"BlockType": "WORD", "Confidence": 78.68721771240234, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11770032346248627, "Height": 0.015291759744286537, "Left": 0.05162839964032173, "Top": 0.33966347575187683}, "Polygon": [{"X": 0.05162839964032173, "Y": 0.3396833837032318}, {"X": 0.16932660341262817, "Y": 0.33966347575187683}, {"X": 0.1693287193775177, "Y": 0.3549353778362274}, {"X": 0.05163056403398514, "Y": 0.3549552261829376}]}, "Id": "47163788-70e3-4acf-a9e5-0e48608f3bb4"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "covered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08953910320997238, "Height": 0.014613255858421326, "Left": 0.17508994042873383, "Top": 0.3364926874637604}, "Polygon": [{"X": 0.17508994042873383, "Y": 0.3365078568458557}, {"X": 0.26462703943252563, "Y": 0.3364926874637604}, {"X": 0.2646290361881256, "Y": 0.35109084844589233}, {"X": 0.1750919669866562, "Y": 0.3511059582233429}]}, "Id": "82e58e4d-1313-447d-816b-0814d34e6930"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028455331921577454, "Height": 0.01446154061704874, "Left": 0.27035585045814514, "Top": 0.33592239022254944}, "Polygon": [{"X": 0.27035585045814514, "Y": 0.33592721819877625}, {"X": 0.2988092005252838, "Y": 0.33592239022254944}, {"X": 0.2988111674785614, "Y": 0.3503791391849518}, {"X": 0.27035781741142273, "Y": 0.3503839373588562}]}, "Id": "b8df7eec-8d8f-4684-937f-650bb1d4bb1c"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04231357201933861, "Height": 0.013380712829530239, "Left": 0.3046886920928955, "Top": 0.3346436619758606}, "Polygon": [{"X": 0.3046886920928955, "Y": 0.33465081453323364}, {"X": 0.34700047969818115, "Y": 0.3346436619758606}, {"X": 0.3470022678375244, "Y": 0.34801721572875977}, {"X": 0.30469051003456116, "Y": 0.3480243682861328}]}, "Id": "7a7723fd-bb8a-435f-9deb-fc558d04a5cf"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "report", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07397963106632233, "Height": 0.012665718793869019, "Left": 0.3532530963420868, "Top": 0.3346816599369049}, "Polygon": [{"X": 0.3532530963420868, "Y": 0.33469417691230774}, {"X": 0.4272310435771942, "Y": 0.3346816599369049}, {"X": 0.4272327125072479, "Y": 0.3473348915576935}, {"X": 0.3532547950744629, "Y": 0.3473473787307739}]}, "Id": "b9331491-13db-4c9a-a6a1-8842b2c07ef1"}, {"BlockType": "WORD", "Confidence": 99.39313507080078, "Text": "was", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04548897594213486, "Height": 0.009408190846443176, "Left": 0.43192383646965027, "Top": 0.33601370453834534}, "Polygon": [{"X": 0.43192383646965027, "Y": 0.33602142333984375}, {"X": 0.4774115979671478, "Y": 0.33601370453834534}, {"X": 0.4774128198623657, "Y": 0.3454142212867737}, {"X": 0.43192508816719055, "Y": 0.3454219102859497}]}, "Id": "91b0be5d-3787-44d1-b2cf-c78780c60926"}, {"BlockType": "WORD", "Confidence": 97.41968536376953, "Text": "competent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12340303510427475, "Height": 0.011562434025108814, "Left": 0.48330608010292053, "Top": 0.33343106508255005}, "Polygon": [{"X": 0.48330608010292053, "Y": 0.3334519565105438}, {"X": 0.6067076325416565, "Y": 0.33343106508255005}, {"X": 0.6067091226577759, "Y": 0.3449726402759552}, {"X": 0.4833076000213623, "Y": 0.3449935019016266}]}, "Id": "1abaad37-13aa-401e-aac2-974332ee<PERSON><PERSON>e"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04269378259778023, "Height": 0.012190469540655613, "Left": 0.6121882796287537, "Top": 0.3325255215167999}, "Polygon": [{"X": 0.6121882796287537, "Y": 0.33253273367881775}, {"X": 0.6548805236816406, "Y": 0.3325255215167999}, {"X": 0.6548820734024048, "Y": 0.34470877051353455}, {"X": 0.6121898889541626, "Y": 0.34471598267555237}]}, "Id": "1c22f4b6-acdd-4b40-909c-86b227eac69b"}, {"BlockType": "WORD", "Confidence": 98.635009765625, "Text": "qualified", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10546554625034332, "Height": 0.013585279695689678, "Left": 0.6610891222953796, "Top": 0.3312774896621704}, "Polygon": [{"X": 0.6610891222953796, "Y": 0.33129534125328064}, {"X": 0.7665530443191528, "Y": 0.3312774896621704}, {"X": 0.7665547132492065, "Y": 0.3448449373245239}, {"X": 0.6610908508300781, "Y": 0.34486275911331177}]}, "Id": "f01ec58b-28c4-45c5-b5a5-445c5d84fb0e"}, {"BlockType": "WORD", "Confidence": 99.77379608154297, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02589491941034794, "Height": 0.011339022777974606, "Left": 0.7730605602264404, "Top": 0.3331761360168457}, "Polygon": [{"X": 0.7730605602264404, "Y": 0.3331805169582367}, {"X": 0.7989541292190552, "Y": 0.3331761360168457}, {"X": 0.798955500125885, "Y": 0.3445107638835907}, {"X": 0.773061990737915, "Y": 0.3445151448249817}]}, "Id": "43633d0f-73df-4552-8cff-39e3890f26de"}, {"BlockType": "WORD", "Confidence": 76.83174896240234, "Text": "do", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03208400681614876, "Height": 0.013326924294233322, "Left": 0.8053373098373413, "Top": 0.33122390508651733}, "Polygon": [{"X": 0.8053373098373413, "Y": 0.3312293589115143}, {"X": 0.8374196887016296, "Y": 0.33122390508651733}, {"X": 0.8374212980270386, "Y": 0.3445454239845276}, {"X": 0.805338978767395, "Y": 0.34455084800720215}]}, "Id": "f34d9322-d788-42bb-bf88-135adec97e73"}, {"BlockType": "WORD", "Confidence": 76.45667266845703, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027687126770615578, "Height": 0.009303836151957512, "Left": 0.8437379598617554, "Top": 0.3349294662475586}, "Polygon": [{"X": 0.8437379598617554, "Y": 0.33493414521217346}, {"X": 0.8714239597320557, "Y": 0.3349294662475586}, {"X": 0.8714250922203064, "Y": 0.3442286252975464}, {"X": 0.8437390923500061, "Y": 0.34423330426216125}]}, "Id": "07d1cb71-5e59-4b80-8db8-968c8d561eca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04630092903971672, "Height": 0.013254810124635696, "Left": 0.878433108329773, "Top": 0.33084073662757874}, "Polygon": [{"X": 0.878433108329773, "Y": 0.3308485746383667}, {"X": 0.9247324466705322, "Y": 0.33084073662757874}, {"X": 0.9247339963912964, "Y": 0.34408771991729736}, {"X": 0.8784347176551819, "Y": 0.34409552812576294}]}, "Id": "126d84f9-c9d0-4aba-90eb-6dfd82e8f9b9"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "was", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04499310627579689, "Height": 0.01071204338222742, "Left": 0.049587227404117584, "Top": 0.3638893961906433}, "Polygon": [{"X": 0.049587227404117584, "Y": 0.3638969659805298}, {"X": 0.0945788249373436, "Y": 0.3638893961906433}, {"X": 0.09458032995462418, "Y": 0.3745938837528229}, {"X": 0.04958874359726906, "Y": 0.37460142374038696}]}, "Id": "b820a8d2-4356-4b77-aa3d-fe1e1248230d"}, {"BlockType": "WORD", "Confidence": 90.62320709228516, "Text": "duty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05101358890533447, "Height": 0.016733210533857346, "Left": 0.10021155327558517, "Top": 0.3587777018547058}, "Polygon": [{"X": 0.10021155327558517, "Y": 0.35878631472587585}, {"X": 0.1512228101491928, "Y": 0.3587777018547058}, {"X": 0.15122514963150024, "Y": 0.375502347946167}, {"X": 0.1002139076590538, "Y": 0.37551093101501465}]}, "Id": "7ca010e6-74cd-4cc4-a0c3-b33df70b618b"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "authorized", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12107308954000473, "Height": 0.015927506610751152, "Left": 0.15752579271793365, "Top": 0.35532963275909424}, "Polygon": [{"X": 0.15752579271793365, "Y": 0.3553500473499298}, {"X": 0.2785967290401459, "Y": 0.35532963275909424}, {"X": 0.2785988748073578, "Y": 0.37123677134513855}, {"X": 0.15752801299095154, "Y": 0.37125712633132935}]}, "Id": "e1ff82ac-1993-4f20-8b3c-35b8cbd62e64"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023277368396520615, "Height": 0.011469170451164246, "Left": 0.2845053970813751, "Top": 0.35730716586112976}, "Polygon": [{"X": 0.2845053970813751, "Y": 0.35731106996536255}, {"X": 0.3077812194824219, "Y": 0.35730716586112976}, {"X": 0.30778276920318604, "Y": 0.3687724173069}, {"X": 0.2845069468021393, "Y": 0.3687763214111328}]}, "Id": "aa01dafe-b8ce-45f1-9567-a4442e592b4d"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "inspect", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08460626751184464, "Height": 0.015533927828073502, "Left": 0.31417566537857056, "Top": 0.3546975255012512}, "Polygon": [{"X": 0.31417566537857056, "Y": 0.35471177101135254}, {"X": 0.39877986907958984, "Y": 0.3546975255012512}, {"X": 0.3987819254398346, "Y": 0.3702172040939331}, {"X": 0.3141777813434601, "Y": 0.3702314496040344}]}, "Id": "c729926e-e72e-4ebb-aaea-a7bf60a36166"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0442284420132637, "Height": 0.013710553757846355, "Left": 0.4036499261856079, "Top": 0.3521255850791931}, "Polygon": [{"X": 0.4036499261856079, "Y": 0.35213303565979004}, {"X": 0.44787657260894775, "Y": 0.3521255850791931}, {"X": 0.447878360748291, "Y": 0.3658286929130554}, {"X": 0.40365174412727356, "Y": 0.36583614349365234}]}, "Id": "7818c08d-1595-4299-82ea-9232a7153f76"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "take", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04964754357933998, "Height": 0.013236276805400848, "Left": 0.45405369997024536, "Top": 0.3512878715991974}, "Polygon": [{"X": 0.45405369997024536, "Y": 0.35129624605178833}, {"X": 0.503699541091919, "Y": 0.3512878715991974}, {"X": 0.5037012696266174, "Y": 0.3645157814025879}, {"X": 0.45405545830726624, "Y": 0.36452415585517883}]}, "Id": "fd6bd5bf-7c8c-4469-bc13-b32ec209961e"}, {"BlockType": "WORD", "Confidence": 93.71273040771484, "Text": "possession", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1227685734629631, "Height": 0.016357943415641785, "Left": 0.5102618932723999, "Top": 0.35095709562301636}, "Polygon": [{"X": 0.5102618932723999, "Y": 0.3509778082370758}, {"X": 0.6330283880233765, "Y": 0.35095709562301636}, {"X": 0.6330304741859436, "Y": 0.3672943711280823}, {"X": 0.5102640390396118, "Y": 0.36731502413749695}]}, "Id": "84f6f0d6-e529-4b6f-b4e4-0cb219210b6c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02636965923011303, "Height": 0.013804682530462742, "Left": 0.6388880610466003, "Top": 0.35057562589645386}, "Polygon": [{"X": 0.6388880610466003, "Y": 0.3505800664424896}, {"X": 0.6652559638023376, "Y": 0.35057562589645386}, {"X": 0.6652576923370361, "Y": 0.36437585949897766}, {"X": 0.6388897895812988, "Y": 0.3643803000450134}]}, "Id": "36430e0a-a314-47f1-9724-3be7a84b5676"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03824473172426224, "Height": 0.013262390159070492, "Left": 0.6694982647895813, "Top": 0.3512263894081116}, "Polygon": [{"X": 0.6694982647895813, "Y": 0.3512328565120697}, {"X": 0.7077413201332092, "Y": 0.3512263894081116}, {"X": 0.7077429890632629, "Y": 0.3644823431968689}, {"X": 0.669499933719635, "Y": 0.36448878049850464}]}, "Id": "16b0700b-bb7a-4252-962b-838b739d58b3"}, {"BlockType": "WORD", "Confidence": 96.37276458740234, "Text": "equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1305491328239441, "Height": 0.01647934317588806, "Left": 0.7146256566047668, "Top": 0.3511945605278015}, "Polygon": [{"X": 0.7146256566047668, "Y": 0.3512166142463684}, {"X": 0.8451727628707886, "Y": 0.3511945605278015}, {"X": 0.8451747894287109, "Y": 0.36765196919441223}, {"X": 0.7146276831626892, "Y": 0.3676739037036896}]}, "Id": "3d284c4f-1014-46d4-88e3-506d053e3f61"}, {"BlockType": "WORD", "Confidence": 96.50929260253906, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026076892390847206, "Height": 0.00933870393782854, "Left": 0.8516355752944946, "Top": 0.354704886674881}, "Polygon": [{"X": 0.8516355752944946, "Y": 0.35470929741859436}, {"X": 0.877711296081543, "Y": 0.354704886674881}, {"X": 0.8777124285697937, "Y": 0.36403921246528625}, {"X": 0.8516367077827454, "Y": 0.36404359340667725}]}, "Id": "a8fc817b-5b8d-449f-88f7-6b8e0e072390"}, {"BlockType": "WORD", "Confidence": 77.00394439697266, "Text": "a", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014384111389517784, "Height": 0.009279408492147923, "Left": 0.8853174448013306, "Top": 0.3545309901237488}, "Polygon": [{"X": 0.8853174448013306, "Y": 0.3545334041118622}, {"X": 0.8997004628181458, "Y": 0.3545309901237488}, {"X": 0.8997015953063965, "Y": 0.3638079762458801}, {"X": 0.8853185772895813, "Y": 0.36381039023399353}]}, "Id": "2b48320b-7500-414c-b0d8-b6b1284198fb"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "representative", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16648738086223602, "Height": 0.020957179367542267, "Left": 0.04829226806759834, "Top": 0.3767409920692444}, "Polygon": [{"X": 0.04829226806759834, "Y": 0.37676894664764404}, {"X": 0.21477676928043365, "Y": 0.3767409920692444}, {"X": 0.21477964520454407, "Y": 0.39767032861709595}, {"X": 0.04829523712396622, "Y": 0.39769816398620605}]}, "Id": "d739529f-e52f-4b64-b2ba-d7ede0af3d88"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024820586666464806, "Height": 0.01452626846730709, "Left": 0.21987569332122803, "Top": 0.3755193054676056}, "Polygon": [{"X": 0.21987569332122803, "Y": 0.37552347779273987}, {"X": 0.2446942776441574, "Y": 0.3755193054676056}, {"X": 0.24469627439975739, "Y": 0.39004144072532654}, {"X": 0.219877690076828, "Y": 0.3900455832481384}]}, "Id": "43c9187f-ce5e-485b-bb00-323fddd3137e"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037527699023485184, "Height": 0.014085118658840656, "Left": 0.2492939829826355, "Top": 0.37531453371047974}, "Polygon": [{"X": 0.2492939829826355, "Y": 0.3753208518028259}, {"X": 0.2868197560310364, "Y": 0.37531453371047974}, {"X": 0.2868216931819916, "Y": 0.3893933892250061}, {"X": 0.2492959052324295, "Y": 0.3893996477127075}]}, "Id": "2c2b07b3-970e-48ea-94e4-954316ff8a25"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "acquiring", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10994724184274673, "Height": 0.01774846762418747, "Left": 0.2928054928779602, "Top": 0.37305811047554016}, "Polygon": [{"X": 0.2928054928779602, "Y": 0.37307658791542053}, {"X": 0.4027503728866577, "Y": 0.37305811047554016}, {"X": 0.40275272727012634, "Y": 0.39078816771507263}, {"X": 0.2928078770637512, "Y": 0.39080655574798584}]}, "Id": "929070ba-4575-403f-b050-389d902a4621"}, {"BlockType": "WORD", "Confidence": 48.83071517944336, "Text": "carrier.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07993807643651962, "Height": 0.014304905198514462, "Left": 0.4091128408908844, "Top": 0.3709888756275177}, "Polygon": [{"X": 0.4091128408908844, "Y": 0.37100231647491455}, {"X": 0.4890490472316742, "Y": 0.3709888756275177}, {"X": 0.4890509247779846, "Y": 0.3852803707122803}, {"X": 0.4091147184371948, "Y": 0.38529378175735474}]}, "Id": "202a53bf-192f-4a68-84d1-3ae71eca9aa3"}, {"BlockType": "WORD", "Confidence": 81.83534240722656, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02349560149013996, "Height": 0.013309501111507416, "Left": 0.49494898319244385, "Top": 0.37072136998176575}, "Polygon": [{"X": 0.49494898319244385, "Y": 0.3707253336906433}, {"X": 0.5184428691864014, "Y": 0.37072136998176575}, {"X": 0.5184445977210999, "Y": 0.3840269446372986}, {"X": 0.49495071172714233, "Y": 0.38403087854385376}]}, "Id": "d0441f0e-9605-4f19-a344-649966b80f1c"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "consideration", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15588515996932983, "Height": 0.014416364952921867, "Left": 0.5246858596801758, "Top": 0.3697425425052643}, "Polygon": [{"X": 0.5246858596801758, "Y": 0.36976876854896545}, {"X": 0.6805692315101624, "Y": 0.3697425425052643}, {"X": 0.6805710196495056, "Y": 0.3841327726840973}, {"X": 0.5246877670288086, "Y": 0.3841589093208313}]}, "Id": "995c68d6-2fd0-4ee9-830a-ca9fe75b365b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026542015373706818, "Height": 0.013873482123017311, "Left": 0.6864663362503052, "Top": 0.3705160617828369}, "Polygon": [{"X": 0.6864663362503052, "Y": 0.3705205023288727}, {"X": 0.7130066156387329, "Y": 0.3705160617828369}, {"X": 0.7130083441734314, "Y": 0.3843850791454315}, {"X": 0.6864680647850037, "Y": 0.3843895196914673}]}, "Id": "c686a57f-8234-4594-8b98-a52c93e1eb6a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0389484204351902, "Height": 0.013857690617442131, "Left": 0.7185695171356201, "Top": 0.37085431814193726}, "Polygon": [{"X": 0.7185695171356201, "Y": 0.37086087465286255}, {"X": 0.7575162053108215, "Y": 0.37085431814193726}, {"X": 0.75751793384552, "Y": 0.38470548391342163}, {"X": 0.7185712456703186, "Y": 0.38471201062202454}]}, "Id": "92a362f8-228e-43ca-9df6-3c214b16ee1d"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "acceptance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13517649471759796, "Height": 0.0149599714204669, "Left": 0.7627923488616943, "Top": 0.37253493070602417}, "Polygon": [{"X": 0.7627923488616943, "Y": 0.3725576400756836}, {"X": 0.8979670405387878, "Y": 0.37253493070602417}, {"X": 0.8979688286781311, "Y": 0.3874722421169281}, {"X": 0.7627941966056824, "Y": 0.38749489188194275}]}, "Id": "b9f51fcb-cbb3-4dc2-9340-0d404c9145db"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02611433155834675, "Height": 0.014717181213200092, "Left": 0.04688757285475731, "Top": 0.39974018931388855}, "Polygon": [{"X": 0.04688757285475731, "Y": 0.39974457025527954}, {"X": 0.07299982756376266, "Y": 0.39974018931388855}, {"X": 0.0730019062757492, "Y": 0.41445302963256836}, {"X": 0.046889662742614746, "Y": 0.41445738077163696}]}, "Id": "e5d31d0c-6cff-4cbc-bc64-124f02cc5115"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03951907902956009, "Height": 0.014227421022951603, "Left": 0.07779370993375778, "Top": 0.3988713324069977}, "Polygon": [{"X": 0.07779370993375778, "Y": 0.39887794852256775}, {"X": 0.11731079965829849, "Y": 0.3988713324069977}, {"X": 0.11731278896331787, "Y": 0.41309216618537903}, {"X": 0.07779571413993835, "Y": 0.4130987524986267}]}, "Id": "d3939192-2e1a-4fc0-9422-f105deb0ea73"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07022920250892639, "Height": 0.01435683760792017, "Left": 0.12316790968179703, "Top": 0.3969947397708893}, "Polygon": [{"X": 0.12316790968179703, "Y": 0.39700648188591003}, {"X": 0.19339512288570404, "Y": 0.3969947397708893}, {"X": 0.19339711964130402, "Y": 0.4113398492336273}, {"X": 0.1231699138879776, "Y": 0.4113515615463257}]}, "Id": "53d42384-117e-4922-8268-26abdb690b72"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10754972696304321, "Height": 0.014972852542996407, "Left": 0.19960106909275055, "Top": 0.39439818263053894}, "Polygon": [{"X": 0.19960106909275055, "Y": 0.3944161832332611}, {"X": 0.3071487545967102, "Y": 0.39439818263053894}, {"X": 0.30715078115463257, "Y": 0.409353107213974}, {"X": 0.1996031254529953, "Y": 0.4093710482120514}]}, "Id": "7f4916a4-7177-4cd1-b495-f36b80440b41"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0360880009829998, "Height": 0.014115755446255207, "Left": 0.3117254972457886, "Top": 0.39323607087135315}, "Polygon": [{"X": 0.3117254972457886, "Y": 0.39324212074279785}, {"X": 0.34781160950660706, "Y": 0.39323607087135315}, {"X": 0.34781351685523987, "Y": 0.40734580159187317}, {"X": 0.3117274045944214, "Y": 0.4073518216609955}]}, "Id": "d24e1d19-028b-446b-9368-6bbd3af54572"}, {"BlockType": "WORD", "Confidence": 73.46400451660156, "Text": "storage.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0947682335972786, "Height": 0.014671646989881992, "Left": 0.35254690051078796, "Top": 0.39429277181625366}, "Polygon": [{"X": 0.35254690051078796, "Y": 0.3943086266517639}, {"X": 0.44731321930885315, "Y": 0.39429277181625366}, {"X": 0.44731515645980835, "Y": 0.4089486002922058}, {"X": 0.35254886746406555, "Y": 0.4089643955230713}]}, "Id": "2417cf52-6fea-4c64-bf32-e830cea16a75"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03851557895541191, "Height": 0.013622095808386803, "Left": 0.45465338230133057, "Top": 0.39011654257774353}, "Polygon": [{"X": 0.45465338230133057, "Y": 0.39012300968170166}, {"X": 0.4931671917438507, "Y": 0.39011654257774353}, {"X": 0.49316897988319397, "Y": 0.40373221039772034}, {"X": 0.45465517044067383, "Y": 0.4037386476993561}]}, "Id": "1c8780fe-b928-4259-808c-768bdbbfcff2"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07874243706464767, "Height": 0.013934392482042313, "Left": 0.4994639456272125, "Top": 0.3891895115375519}, "Polygon": [{"X": 0.4994639456272125, "Y": 0.38920268416404724}, {"X": 0.5782045722007751, "Y": 0.3891895115375519}, {"X": 0.5782063603401184, "Y": 0.4031107425689697}, {"X": 0.4994657635688782, "Y": 0.4031238853931427}]}, "Id": "66ef889e-5a67-441d-87a9-22ab59273333"}, {"BlockType": "WORD", "Confidence": 98.95468139648438, "Text": "acknowledges", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16418343782424927, "Height": 0.018175430595874786, "Left": 0.5840388536453247, "Top": 0.3889004588127136}, "Polygon": [{"X": 0.5840388536453247, "Y": 0.3889279365539551}, {"X": 0.7482200264930725, "Y": 0.3889004588127136}, {"X": 0.748222291469574, "Y": 0.40704846382141113}, {"X": 0.584041178226471, "Y": 0.4070758819580078}]}, "Id": "cf6b23b2-e442-4184-bb26-b4ea5172014f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05059822276234627, "Height": 0.014233733527362347, "Left": 0.7544691562652588, "Top": 0.3895854651927948}, "Polygon": [{"X": 0.7544691562652588, "Y": 0.3895939290523529}, {"X": 0.8050656318664551, "Y": 0.3895854651927948}, {"X": 0.8050673604011536, "Y": 0.4038107395172119}, {"X": 0.754470944404602, "Y": 0.40381917357444763}]}, "Id": "4cfe6c64-0451-4eb9-b2e0-f75243a0568f"}, {"BlockType": "WORD", "Confidence": 97.998046875, "Text": "all", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030495792627334595, "Height": 0.01390391867607832, "Left": 0.8094786405563354, "Top": 0.3900679349899292}, "Polygon": [{"X": 0.8094786405563354, "Y": 0.3900730609893799}, {"X": 0.839972734451294, "Y": 0.3900679349899292}, {"X": 0.8399744629859924, "Y": 0.4039667844772339}, {"X": 0.8094803690910339, "Y": 0.4039718806743622}]}, "Id": "31cbdaf6-d4f4-41f0-a325-dbd09dc5fced"}, {"BlockType": "WORD", "Confidence": 99.24346160888672, "Text": "freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08206742256879807, "Height": 0.018240058794617653, "Left": 0.044511038810014725, "Top": 0.4182157814502716}, "Polygon": [{"X": 0.044511038810014725, "Y": 0.41822946071624756}, {"X": 0.12657591700553894, "Y": 0.4182157814502716}, {"X": 0.1265784651041031, "Y": 0.43644222617149353}, {"X": 0.044513627886772156, "Y": 0.4364558458328247}]}, "Id": "b4bc7013-1ee0-44e5-b937-d31ae8d374d8"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "charges", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08923279494047165, "Height": 0.016977986320853233, "Left": 0.13268424570560455, "Top": 0.4163816571235657}, "Polygon": [{"X": 0.13268424570560455, "Y": 0.41639652848243713}, {"X": 0.22191470861434937, "Y": 0.4163816571235657}, {"X": 0.2219170480966568, "Y": 0.43334484100341797}, {"X": 0.13268661499023438, "Y": 0.43335965275764465}]}, "Id": "16360d30-07b4-4b4d-8ad2-d105aae810e8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "have", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05404317378997803, "Height": 0.013629279099404812, "Left": 0.2266068458557129, "Top": 0.4147481620311737}, "Polygon": [{"X": 0.2266068458557129, "Y": 0.4147571921348572}, {"X": 0.2806481719017029, "Y": 0.4147481620311737}, {"X": 0.2806500196456909, "Y": 0.428368479013443}, {"X": 0.22660870850086212, "Y": 0.4283774495124817}]}, "Id": "2c6bd893-57eb-4936-85f0-6746b28d120f"}, {"BlockType": "WORD", "Confidence": 95.88468933105469, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05618499964475632, "Height": 0.01352123823016882, "Left": 0.2864598333835602, "Top": 0.41341155767440796}, "Polygon": [{"X": 0.2864598333835602, "Y": 0.4134209156036377}, {"X": 0.34264302253723145, "Y": 0.41341155767440796}, {"X": 0.3426448404788971, "Y": 0.4269234538078308}, {"X": 0.2864616811275482, "Y": 0.42693278193473816}]}, "Id": "1326adbe-49c7-4772-8310-a91e815ba43a"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "paid", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05217823386192322, "Height": 0.0187089703977108, "Left": 0.3486011326313019, "Top": 0.411056250333786}, "Polygon": [{"X": 0.3486011326313019, "Y": 0.4110649526119232}, {"X": 0.40077686309814453, "Y": 0.411056250333786}, {"X": 0.4007793664932251, "Y": 0.4297565519809723}, {"X": 0.34860363602638245, "Y": 0.4297652244567871}]}, "Id": "f58f0ab3-49c1-4dd0-a236-99a9d7835571"}, {"BlockType": "WORD", "Confidence": 72.7782211303711, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027580291032791138, "Height": 0.009375913999974728, "Left": 0.4071705937385559, "Top": 0.41492143273353577}, "Polygon": [{"X": 0.4071705937385559, "Y": 0.41492602229118347}, {"X": 0.43474966287612915, "Y": 0.41492143273353577}, {"X": 0.43475088477134705, "Y": 0.42429274320602417}, {"X": 0.4071718454360962, "Y": 0.4242973327636719}]}, "Id": "3cff94f1-8362-485e-bb81-f44daf45f6cd"}, {"BlockType": "WORD", "Confidence": 98.3665542602539, "Text": "agrees", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07875362038612366, "Height": 0.013810909353196621, "Left": 0.439053475856781, "Top": 0.4132779836654663}, "Polygon": [{"X": 0.439053475856781, "Y": 0.4132911264896393}, {"X": 0.517805278301239, "Y": 0.4132779836654663}, {"X": 0.5178070664405823, "Y": 0.4270758032798767}, {"X": 0.43905529379844666, "Y": 0.4270889163017273}]}, "Id": "cd112b9f-0d5a-458b-a167-70dac2525787"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025352276861667633, "Height": 0.011747178621590137, "Left": 0.5228246450424194, "Top": 0.4100643992424011}, "Polygon": [{"X": 0.5228246450424194, "Y": 0.4100686311721802}, {"X": 0.5481753945350647, "Y": 0.4100643992424011}, {"X": 0.5481768846511841, "Y": 0.4218073785305023}, {"X": 0.5228261351585388, "Y": 0.421811580657959}]}, "Id": "6b9dd421-986f-4d3f-9672-64103a71aac0"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "look", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051401179283857346, "Height": 0.013797339051961899, "Left": 0.5544657707214355, "Top": 0.4079760015010834}, "Polygon": [{"X": 0.5544657707214355, "Y": 0.40798458456993103}, {"X": 0.6058651804924011, "Y": 0.4079760015010834}, {"X": 0.6058669686317444, "Y": 0.4217647910118103}, {"X": 0.5544675588607788, "Y": 0.42177334427833557}]}, "Id": "ec04304d-a352-478c-b465-75a472eb96f3"}, {"BlockType": "WORD", "Confidence": 49.68730163574219, "Text": "salely", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06726250797510147, "Height": 0.017675789073109627, "Left": 0.6113167405128479, "Top": 0.40823474526405334}, "Polygon": [{"X": 0.6113167405128479, "Y": 0.4082459509372711}, {"X": 0.6785770058631897, "Y": 0.40823474526405334}, {"X": 0.6785792708396912, "Y": 0.42589935660362244}, {"X": 0.6113190054893494, "Y": 0.4259105324745178}]}, "Id": "1ef38da1-b397-44ce-9813-48ce042d8ffc"}, {"BlockType": "WORD", "Confidence": 99.45332336425781, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026078488677740097, "Height": 0.011884920299053192, "Left": 0.6837009191513062, "Top": 0.41054654121398926}, "Polygon": [{"X": 0.6837009191513062, "Y": 0.41055089235305786}, {"X": 0.7097779512405396, "Y": 0.41054654121398926}, {"X": 0.7097794413566589, "Y": 0.42242711782455444}, {"X": 0.6837024092674255, "Y": 0.42243146896362305}]}, "Id": "feedb17a-51bf-4534-8666-ddde1b13fa92"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03892510384321213, "Height": 0.013536449521780014, "Left": 0.7162430286407471, "Top": 0.4090203046798706}, "Polygon": [{"X": 0.7162430286407471, "Y": 0.4090268015861511}, {"X": 0.7551664113998413, "Y": 0.4090203046798706}, {"X": 0.7551681399345398, "Y": 0.4225502610206604}, {"X": 0.7162446975708008, "Y": 0.4225567579269409}]}, "Id": "a76c03e5-830c-4ccb-b1c1-c1cce3cb46f8"}, {"BlockType": "WORD", "Confidence": 84.365234375, "Text": "consignor.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12295430153608322, "Height": 0.01708119921386242, "Left": 0.761948823928833, "Top": 0.4096592664718628}, "Polygon": [{"X": 0.761948823928833, "Y": 0.4096798002719879}, {"X": 0.8849010467529297, "Y": 0.4096592664718628}, {"X": 0.8849031329154968, "Y": 0.4267200231552124}, {"X": 0.7619509100914001, "Y": 0.42674046754837036}]}, "Id": "6a3772a0-72c2-4011-aa4c-bcd1bbc688d2"}, {"BlockType": "WORD", "Confidence": 92.22815704345703, "Text": "broker,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08213591575622559, "Height": 0.01577257178723812, "Left": 0.04394851624965668, "Top": 0.43849560618400574}, "Polygon": [{"X": 0.04394851624965668, "Y": 0.4385092258453369}, {"X": 0.12608222663402557, "Y": 0.43849560618400574}, {"X": 0.12608443200588226, "Y": 0.4542545974254608}, {"X": 0.04395075514912605, "Y": 0.4542681872844696}]}, "Id": "ba21d69f-517e-45de-a7db-5661c46ef3b4"}, {"BlockType": "WORD", "Confidence": 99.61515808105469, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02749827690422535, "Height": 0.010043405927717686, "Left": 0.13400639593601227, "Top": 0.44026780128479004}, "Polygon": [{"X": 0.13400639593601227, "Y": 0.44027236104011536}, {"X": 0.16150328516960144, "Y": 0.44026780128479004}, {"X": 0.16150468587875366, "Y": 0.45030665397644043}, {"X": 0.13400781154632568, "Y": 0.45031121373176575}]}, "Id": "fa4f5412-3743-4ee1-bb01-a72a30a2b03d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03705333173274994, "Height": 0.014124070294201374, "Left": 0.16649049520492554, "Top": 0.43553006649017334}, "Polygon": [{"X": 0.16649049520492554, "Y": 0.4355362355709076}, {"X": 0.20354187488555908, "Y": 0.43553006649017334}, {"X": 0.20354381203651428, "Y": 0.44964802265167236}, {"X": 0.16649244725704193, "Y": 0.44965413212776184}]}, "Id": "c3db133c-5317-4822-8baa-e3caa2a3bd17"}, {"BlockType": "WORD", "Confidence": 99.63229370117188, "Text": "beneficial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1093299612402916, "Height": 0.015179994516074657, "Left": 0.2101667821407318, "Top": 0.4331597089767456}, "Polygon": [{"X": 0.2101667821407318, "Y": 0.4331778883934021}, {"X": 0.31949469447135925, "Y": 0.4331597089767456}, {"X": 0.319496750831604, "Y": 0.4483216106891632}, {"X": 0.21016886830329895, "Y": 0.44833970069885254}]}, "Id": "50772ee6-865f-4887-a44d-2e74f400bb6d"}, {"BlockType": "WORD", "Confidence": 99.85192108154297, "Text": "owner", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07571214437484741, "Height": 0.011581008322536945, "Left": 0.3253975212574005, "Top": 0.43454650044441223}, "Polygon": [{"X": 0.3253975212574005, "Y": 0.43455904722213745}, {"X": 0.40110814571380615, "Y": 0.43454650044441223}, {"X": 0.4011096656322479, "Y": 0.4461149573326111}, {"X": 0.32539910078048706, "Y": 0.4461275041103363}]}, "Id": "de89cc8e-cd6b-408e-9097-5ea6bcbe4584"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027140669524669647, "Height": 0.014151415787637234, "Left": 0.4059135317802429, "Top": 0.4300853908061981}, "Polygon": [{"X": 0.4059135317802429, "Y": 0.43008992075920105}, {"X": 0.43305233120918274, "Y": 0.4300853908061981}, {"X": 0.43305420875549316, "Y": 0.444232314825058}, {"X": 0.40591540932655334, "Y": 0.4442368149757385}]}, "Id": "606d328c-7673-4c2f-ac95-e63768647384"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038398999720811844, "Height": 0.013965423218905926, "Left": 0.43782415986061096, "Top": 0.4296512305736542}, "Polygon": [{"X": 0.43782415986061096, "Y": 0.42965760827064514}, {"X": 0.47622132301330566, "Y": 0.4296512305736542}, {"X": 0.4762231707572937, "Y": 0.443610280752182}, {"X": 0.437826007604599, "Y": 0.4436166286468506}]}, "Id": "ea264668-9230-4550-8879-dfac4bf451bd"}, {"BlockType": "WORD", "Confidence": 99.20280456542969, "Text": "freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08184577524662018, "Height": 0.0178552083671093, "Left": 0.4829394519329071, "Top": 0.42789942026138306}, "Polygon": [{"X": 0.4829394519329071, "Y": 0.42791301012039185}, {"X": 0.5647829174995422, "Y": 0.42789942026138306}, {"X": 0.5647852420806885, "Y": 0.44574105739593506}, {"X": 0.48294177651405334, "Y": 0.44575461745262146}]}, "Id": "72c7360a-e9f5-4f35-9f16-74e5581a0f0e"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035686835646629333, "Height": 0.014294162392616272, "Left": 0.5693551301956177, "Top": 0.42707353830337524}, "Polygon": [{"X": 0.5693551301956177, "Y": 0.4270794987678528}, {"X": 0.6050401329994202, "Y": 0.42707353830337524}, {"X": 0.6050419807434082, "Y": 0.44136178493499756}, {"X": 0.5693569779396057, "Y": 0.4413677155971527}]}, "Id": "0b17c2ad-5b1e-4f0a-a6f9-ca78d8cd482a"}, {"BlockType": "WORD", "Confidence": 70.34439086914062, "Text": "payment.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10905758291482925, "Height": 0.015172089450061321, "Left": 0.6105743646621704, "Top": 0.4303177297115326}, "Polygon": [{"X": 0.6105743646621704, "Y": 0.4303358495235443}, {"X": 0.7196300625801086, "Y": 0.4303177297115326}, {"X": 0.7196319699287415, "Y": 0.44547173380851746}, {"X": 0.610576331615448, "Y": 0.4454897940158844}]}, "Id": "f0e11c48-4e14-49ee-8259-f28b47b8844f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04429316148161888, "Height": 0.014163387939333916, "Left": 0.7254946231842041, "Top": 0.4285042881965637}, "Polygon": [{"X": 0.7254946231842041, "Y": 0.4285116493701935}, {"X": 0.7697860598564148, "Y": 0.4285042881965637}, {"X": 0.7697877883911133, "Y": 0.4426603317260742}, {"X": 0.7254964113235474, "Y": 0.4426676630973816}]}, "Id": "c847bf44-b10b-4bd1-af57-cd958ea5e71b"}, {"BlockType": "WORD", "Confidence": 75.43666076660156, "Text": "Corrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08069120347499847, "Height": 0.014758857898414135, "Left": 0.7768666744232178, "Top": 0.42881014943122864}, "Polygon": [{"X": 0.7768666744232178, "Y": 0.4288235604763031}, {"X": 0.8575560450553894, "Y": 0.42881014943122864}, {"X": 0.8575578927993774, "Y": 0.443555623292923}, {"X": 0.776868462562561, "Y": 0.44356900453567505}]}, "Id": "84998faf-95f2-4cf7-81c0-7ecb187a7fbd"}, {"BlockType": "WORD", "Confidence": 96.14417266845703, "Text": "waives", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07967495918273926, "Height": 0.015381800942122936, "Left": 0.042296648025512695, "Top": 0.4588261842727661}, "Polygon": [{"X": 0.042296648025512695, "Y": 0.4588393568992615}, {"X": 0.12196945399045944, "Y": 0.4588261842727661}, {"X": 0.12197160720825195, "Y": 0.47419485449790955}, {"X": 0.042298831045627594, "Y": 0.4742079973220825}]}, "Id": "eee2d46f-0f20-4c68-8a29-5e81527fbe06"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04316272214055061, "Height": 0.014283710159361362, "Left": 0.12854665517807007, "Top": 0.4599021375179291}, "Polygon": [{"X": 0.12854665517807007, "Y": 0.45990926027297974}, {"X": 0.1717073917388916, "Y": 0.4599021375179291}, {"X": 0.17170937359333038, "Y": 0.4741787314414978}, {"X": 0.12854865193367004, "Y": 0.47418585419654846}]}, "Id": "5e2f2291-bc18-47cd-bddb-b4f345ace4ea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "right", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05617339536547661, "Height": 0.018810002133250237, "Left": 0.17703098058700562, "Top": 0.45458561182022095}, "Polygon": [{"X": 0.17703098058700562, "Y": 0.4545949101448059}, {"X": 0.233201801776886, "Y": 0.45458561182022095}, {"X": 0.23320437967777252, "Y": 0.47338634729385376}, {"X": 0.17703358829021454, "Y": 0.47339561581611633}]}, "Id": "2d25abdc-f0ec-44ae-bc94-d78397775ae0"}, {"BlockType": "WORD", "Confidence": 96.57366180419922, "Text": "it", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017435861751437187, "Height": 0.013484595343470573, "Left": 0.23824669420719147, "Top": 0.4551680088043213}, "Polygon": [{"X": 0.23824669420719147, "Y": 0.4551708996295929}, {"X": 0.25568071007728577, "Y": 0.4551680088043213}, {"X": 0.2556825578212738, "Y": 0.4686497151851654}, {"X": 0.2382485419511795, "Y": 0.468652606010437}]}, "Id": "aa3c5fbf-3ff1-4404-8f5b-7bf560e5e1cb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05031772330403328, "Height": 0.014353840611875057, "Left": 0.2611197531223297, "Top": 0.45766499638557434}, "Polygon": [{"X": 0.2611197531223297, "Y": 0.4576733112335205}, {"X": 0.3114355504512787, "Y": 0.45766499638557434}, {"X": 0.3114374876022339, "Y": 0.4720105230808258}, {"X": 0.2611217200756073, "Y": 0.472018837928772}]}, "Id": "3cd2e186-d765-4532-8f82-5b2cddef6c95"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "have", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0560782328248024, "Height": 0.013197450898587704, "Left": 0.3168846070766449, "Top": 0.4536465108394623}, "Polygon": [{"X": 0.3168846070766449, "Y": 0.45365577936172485}, {"X": 0.3729610741138458, "Y": 0.4536465108394623}, {"X": 0.3729628622531891, "Y": 0.4668347239494324}, {"X": 0.31688639521598816, "Y": 0.46684396266937256}]}, "Id": "b5c8f055-76af-450f-bf05-e1c0f47a2721"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0249168798327446, "Height": 0.011730543337762356, "Left": 0.37909364700317383, "Top": 0.4535321295261383}, "Polygon": [{"X": 0.37909364700317383, "Y": 0.4535362422466278}, {"X": 0.40400898456573486, "Y": 0.4535321295261383}, {"X": 0.404010534286499, "Y": 0.46525856852531433}, {"X": 0.3790952265262604, "Y": 0.46526268124580383}]}, "Id": "b7b18c4d-537a-4dbe-a5db-7cd41db7a8f6"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "pursue", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08181323111057281, "Height": 0.015316672623157501, "Left": 0.41103923320770264, "Top": 0.4533834755420685}, "Polygon": [{"X": 0.41103923320770264, "Y": 0.4533970057964325}, {"X": 0.4928504526615143, "Y": 0.4533834755420685}, {"X": 0.49285244941711426, "Y": 0.46868667006492615}, {"X": 0.411041259765625, "Y": 0.4687001705169678}]}, "Id": "10c08798-b6ed-44ee-a815-3bc1aa0f10af"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "TSL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04295334219932556, "Height": 0.013470999896526337, "Left": 0.49756890535354614, "Top": 0.44887271523475647}, "Polygon": [{"X": 0.49756890535354614, "Y": 0.44887983798980713}, {"X": 0.5405204892158508, "Y": 0.44887271523475647}, {"X": 0.5405222773551941, "Y": 0.46233662962913513}, {"X": 0.497570663690567, "Y": 0.4623437225818634}]}, "Id": "301dfc62-90f5-4069-8b87-608dd58b9fe0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035849425941705704, "Height": 0.014527108520269394, "Left": 0.5463675260543823, "Top": 0.44707921147346497}, "Polygon": [{"X": 0.5463675260543823, "Y": 0.4470851421356201}, {"X": 0.5822150707244873, "Y": 0.44707921147346497}, {"X": 0.5822169184684753, "Y": 0.4616003930568695}, {"X": 0.5463693737983704, "Y": 0.46160632371902466}]}, "Id": "014064f0-d785-42ef-9dc9-e746c637b1c5"}, {"BlockType": "WORD", "Confidence": 98.3958511352539, "Text": "payment.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10825730115175247, "Height": 0.015188436023890972, "Left": 0.5883557796478271, "Top": 0.4503110945224762}, "Polygon": [{"X": 0.5883557796478271, "Y": 0.4503290355205536}, {"X": 0.696611225605011, "Y": 0.4503110945224762}, {"X": 0.6966131329536438, "Y": 0.4654816687107086}, {"X": 0.5883577466011047, "Y": 0.46549955010414124}]}, "Id": "407557c6-1e8f-41e8-bcc3-129f2bedb126"}, {"BlockType": "WORD", "Confidence": 98.83430480957031, "Text": "Sign", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06655172258615494, "Height": 0.0224614255130291, "Left": 0.04167594388127327, "Top": 0.4798564612865448}, "Polygon": [{"X": 0.04167594388127327, "Y": 0.47986742854118347}, {"X": 0.10822451114654541, "Y": 0.4798564612865448}, {"X": 0.10822766274213791, "Y": 0.5023069977760315}, {"X": 0.04167913272976875, "Y": 0.5023179054260254}]}, "Id": "aeece569-0322-4b5a-ba7a-7ec8bffc0b01"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "here", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06594162434339523, "Height": 0.018405133858323097, "Left": 0.11800763010978699, "Top": 0.4771602153778076}, "Polygon": [{"X": 0.11800763010978699, "Y": 0.47717106342315674}, {"X": 0.18394671380519867, "Y": 0.4771602153778076}, {"X": 0.18394924700260162, "Y": 0.49555453658103943}, {"X": 0.11801020801067352, "Y": 0.49556535482406616}]}, "Id": "1e095991-e61a-4dba-b78e-dbade207a809"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b8efe220-6bfb-41f9-b28b-85f476067b91", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b8efe220-6bfb-41f9-b28b-85f476067b91", "content-type": "application/x-amz-json-1.1", "content-length": "74818", "date": "Wed, 24 Sep 2025 13:28:32 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "7e749e6a-bc72-45a0-a520-f87f0b976820", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:28:35 GMT", "content-type": "application/json", "content-length": "747", "connection": "keep-alive", "x-amzn-requestid": "7e749e6a-bc72-45a0-a520-f87f0b976820"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We need to examine the page. It shows \"TSL Ingate: OMA\" \"ActivityDate:...\" \"Carrier Inspector...\" So it's an Ingate document. The description: \"Ingate Document: Record of vehicle/container entering a facility (gate-in).\" The keywords: Ingate, Equipment In-Gated, Arrival Activity, Time In, etc. So doc_type is \"ingate\". Output JSON with page_no 1 and doc_type ingate."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"ingate\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2790, "outputTokens": 123, "totalTokens": 2913}, "metrics": {"latencyMs": 790}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/a32a3b56_AYZVGH1N2J32RSF72XVM.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nTSL Ingate: OMA\nActivityDate: 08/15/2025 13:59\nCarrier Inspector CALHOUN TRUCK LINES\nCOG\nLoaded: No Hazardous:No Lifted:Yes\nGrade: BF\nSeal: Genset:\nApmz405128 40CH Owner:DIRECTCHASSISLINKING\nGenset: Hubmeter:\nI hereby certify that on the date stated above the person who conducted the\nInspection covered by this report was competent and qualified to do so and\nwas duty authorized to inspect and take possession of the equipment as a\nrepresentative of the acquiring carrier. In consideration of the acceptance\nof the above container for storage. the Carrier acknowledges that all\nfreight charges have been paid or agrees to look salely to the consignor.\nbroker, or the beneficial owner of the freight for payment. The Corrier\nwaives any right it may have to pursue TSL for payment.\nSign here\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}