{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.5281452536582947, "Height": 0.82681804895401, "Left": 0.1712264120578766, "Top": 0.06443558633327484}, "Polygon": [{"X": 0.1712264120578766, "Y": 0.11482226103544235}, {"X": 0.6301570534706116, "Y": 0.06443558633327484}, {"X": 0.6993716359138489, "Y": 0.891031801700592}, {"X": 0.2520110607147217, "Y": 0.891253650188446}]}, "Id": "6cddfb73-922c-4cc1-bcb4-35c6a0e25aa4", "Relationships": [{"Type": "CHILD", "Ids": ["09ef9407-f5ca-40de-abed-bd11bb8e7ee2", "78bf7f49-1894-42c8-929c-1bd7e427820c", "be0fb859-da74-4d01-9e95-d6084d7f4e68", "4bd67b33-0191-4063-b974-470e3b055bec", "4837683d-5bc9-4f78-935b-3453d752c086", "6ecc242e-b4cb-467b-950a-a185651cc86d", "e5426e1f-74a9-40bf-a320-9594758686e5", "aad40ac7-536f-4639-9f4a-82f261882a3c", "f2174b63-8de2-41e1-bf69-61a2dac8d79f", "26339179-bf96-4a28-b9a6-e5d7e0db44c0", "a3cbe3f5-2dee-42ce-861c-5843cd13f7dc", "18e603cd-bcc7-4950-b112-4bfea96db13b", "47d615f1-ef20-4ec1-b870-e6e9822bfd97", "2a51e16e-1417-4073-92c7-b2df805e1f80", "5cdc8caf-6fd2-4e55-8cdd-41c27e9b5f5b", "459f6e32-7104-4c57-ad23-8dc917b98325", "1b858cf9-4c1f-4af8-aed0-d10a6e0d0077", "a0e753d5-688c-4e8f-b585-2f015065b4e8", "dd20da75-bdfe-4f44-a1f8-302758aa8f92", "774d1187-324f-4293-b064-b145acb3d84a", "a5d97fe8-add2-4b4b-9281-247919770b1f", "e848d811-3fb2-4e34-a2c2-1f62960fac79", "30b224d9-1270-4f19-9754-60166e26a196", "0cb4a8ca-fdba-4215-8f1f-3958b5055a49", "e8807f9c-e0c7-4dbb-95b9-631bca53e6ff", "5b725fb7-9cd3-416b-b407-0ac680c4149d", "86ce97aa-6b9a-4453-80f1-0772ceee1ca3", "5fd0cc0c-4812-4f10-acdf-f2247850d91e"]}]}, {"BlockType": "LINE", "Confidence": 99.9571533203125, "Text": "KWIK TRIP #209", "Geometry": {"BoundingBox": {"Width": 0.21327339112758636, "Height": 0.033020537346601486, "Left": 0.290267676115036, "Top": 0.3265790343284607}, "Polygon": [{"X": 0.290267676115036, "Y": 0.34275898337364197}, {"X": 0.5019733309745789, "Y": 0.3265790343284607}, {"X": 0.5035410523414612, "Y": 0.34392523765563965}, {"X": 0.29194876551628113, "Y": 0.3595995604991913}]}, "Id": "09ef9407-f5ca-40de-abed-bd11bb8e7ee2", "Relationships": [{"Type": "CHILD", "Ids": ["20a66baf-a957-4668-a53b-f31e99493059", "c3dc8091-834f-4065-81ed-cb66ba4f1674", "b0af35d1-3a35-490f-8594-a7e3548019a7"]}]}, {"BlockType": "LINE", "Confidence": 99.8313217163086, "Text": "1101 GERTRUDE STREET", "Geometry": {"BoundingBox": {"Width": 0.30218660831451416, "Height": 0.03888526186347008, "Left": 0.24924273788928986, "Top": 0.3465563952922821}, "Polygon": [{"X": 0.24924273788928986, "Y": 0.3685831129550934}, {"X": 0.5498762726783752, "Y": 0.3465563952922821}, {"X": 0.5514293313026428, "Y": 0.364137202501297}, {"X": 0.2509582042694092, "Y": 0.3854416608810425}]}, "Id": "78bf7f49-1894-42c8-929c-1bd7e427820c", "Relationships": [{"Type": "CHILD", "Ids": ["ede2641b-f164-4214-bf8b-d8fc7dfdf687", "0adb7edc-b2b9-42a8-940b-906e2ab4fb71", "8a6bbc0a-880b-4346-b6f0-e32d34ea2f77"]}]}, {"BlockType": "LINE", "Confidence": 99.57210540771484, "Text": "KAUKAUNA, WI", "Geometry": {"BoundingBox": {"Width": 0.18187780678272247, "Height": 0.03215523436665535, "Left": 0.3113258481025696, "Top": 0.3747329115867615}, "Polygon": [{"X": 0.3113258481025696, "Y": 0.3873456120491028}, {"X": 0.4913792312145233, "Y": 0.3747329115867615}, {"X": 0.49320363998413086, "Y": 0.394771933555603}, {"X": 0.3132622241973877, "Y": 0.4068881571292877}]}, "Id": "be0fb859-da74-4d01-9e95-d6084d7f4e68", "Relationships": [{"Type": "CHILD", "Ids": ["510d516b-a793-4f80-b846-3cb34ef8395f", "f629eb95-cd9f-4521-8da2-234564fbfb24"]}]}, {"BlockType": "LINE", "Confidence": 99.44764709472656, "Text": "Invoice# 0091442", "Geometry": {"BoundingBox": {"Width": 0.2525741159915924, "Height": 0.032874517142772675, "Left": 0.259107768535614, "Top": 0.41846251487731934}, "Polygon": [{"X": 0.259107768535614, "Y": 0.4345261752605438}, {"X": 0.510108232498169, "Y": 0.41846251487731934}, {"X": 0.511681854724884, "Y": 0.43587204813957214}, {"X": 0.2608160078525543, "Y": 0.4513370394706726}]}, "Id": "4bd67b33-0191-4063-b974-470e3b055bec", "Relationships": [{"Type": "CHILD", "Ids": ["eb096a82-0ba8-4e2b-9697-33f6854525d4", "0d4e760c-b883-4dc9-b9ac-6bf06291bcf2"]}]}, {"BlockType": "LINE", "Confidence": 96.38951110839844, "Text": "Trans # 0900-1442", "Geometry": {"BoundingBox": {"Width": 0.2843874990940094, "Height": 0.03344716131687164, "Left": 0.25814923644065857, "Top": 0.4401582181453705}, "Polygon": [{"X": 0.25814923644065857, "Y": 0.4573662281036377}, {"X": 0.5410317778587341, "Y": 0.4401582181453705}, {"X": 0.542536735534668, "Y": 0.457048624753952}, {"X": 0.2598017752170563, "Y": 0.4736053943634033}]}, "Id": "4837683d-5bc9-4f78-935b-3453d752c086", "Relationships": [{"Type": "CHILD", "Ids": ["ea6c3f27-f5fc-437c-a587-84a115f93d06", "cefd781d-497b-4615-a40e-e1f68c7a8eb1", "e47a403b-8520-4e5b-bbeb-aa96a2f02c36"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.05978527292609215, "Height": 0.01742297224700451, "Left": 0.26228219270706177, "Top": 0.4760996401309967}, "Polygon": [{"X": 0.26228219270706177, "Y": 0.4794696569442749}, {"X": 0.3206638693809509, "Y": 0.4760996401309967}, {"X": 0.3220674693584442, "Y": 0.49026864767074585}, {"X": 0.2637110650539398, "Y": 0.49352261424064636}]}, "Id": "6ecc242e-b4cb-467b-950a-a185651cc86d", "Relationships": [{"Type": "CHILD", "Ids": ["479ecece-917f-4f52-8dff-7dc255796c42"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "04/22/2025", "Geometry": {"BoundingBox": {"Width": 0.14782235026359558, "Height": 0.025508040562272072, "Left": 0.4125238060951233, "Top": 0.463314950466156}, "Polygon": [{"X": 0.4125238060951233, "Y": 0.47174015641212463}, {"X": 0.5588054060935974, "Y": 0.463314950466156}, {"X": 0.5603461861610413, "Y": 0.48074427247047424}, {"X": 0.414145827293396, "Y": 0.4888229966163635}]}, "Id": "e5426e1f-74a9-40bf-a320-9594758686e5", "Relationships": [{"Type": "CHILD", "Ids": ["93542b16-fc13-407a-9839-8bc0f21e348e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.0613318495452404, "Height": 0.018493013456463814, "Left": 0.26313695311546326, "Top": 0.49719947576522827}, "Polygon": [{"X": 0.26313695311546326, "Y": 0.5004770755767822}, {"X": 0.3229489326477051, "Y": 0.49719947576522827}, {"X": 0.32446882128715515, "Y": 0.5125435590744019}, {"X": 0.26468491554260254, "Y": 0.5156925320625305}]}, "Id": "aad40ac7-536f-4639-9f4a-82f261882a3c", "Relationships": [{"Type": "CHILD", "Ids": ["82a3206c-03d4-4621-a0e4-90857ca25a15"]}]}, {"BlockType": "LINE", "Confidence": 99.31282043457031, "Text": "01:00 PM", "Geometry": {"BoundingBox": {"Width": 0.11740975081920624, "Height": 0.022115113213658333, "Left": 0.4147174656391144, "Top": 0.4873727858066559}, "Polygon": [{"X": 0.4147174656391144, "Y": 0.49370354413986206}, {"X": 0.5306878685951233, "Y": 0.4873727858066559}, {"X": 0.5321272015571594, "Y": 0.5034106373786926}, {"X": 0.416216105222702, "Y": 0.5094879269599915}]}, "Id": "f2174b63-8de2-41e1-bf69-61a2dac8d79f", "Relationships": [{"Type": "CHILD", "Ids": ["7e3f2182-8290-49df-8581-329ed1d6c6fa", "e3ef596c-3870-4134-9370-8e05db7278b5"]}]}, {"BlockType": "LINE", "Confidence": 99.90074920654297, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.2993241548538208, "Height": 0.03362613916397095, "Left": 0.268963485956192, "Top": 0.5296956300735474}, "Polygon": [{"X": 0.268963485956192, "Y": 0.544182538986206}, {"X": 0.5665264129638672, "Y": 0.5296956300735474}, {"X": 0.5682876706123352, "Y": 0.5496378540992737}, {"X": 0.27090945839881897, "Y": 0.5633217692375183}]}, "Id": "26339179-bf96-4a28-b9a6-e5d7e0db44c0", "Relationships": [{"Type": "CHILD", "Ids": ["5f52d47f-3f59-4fed-a6b7-831897b61b32", "2699ae40-e50c-411c-8a6c-02a2a54295cb", "17b221a4-7d43-4f32-bb3f-470c887547d3"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "02", "Geometry": {"BoundingBox": {"Width": 0.03029600903391838, "Height": 0.015355591662228107, "Left": 0.28632232546806335, "Top": 0.5655689835548401}, "Polygon": [{"X": 0.28632232546806335, "Y": 0.5668812990188599}, {"X": 0.3152124881744385, "Y": 0.5655689835548401}, {"X": 0.31661832332611084, "Y": 0.5796692967414856}, {"X": 0.28774070739746094, "Y": 0.5809246301651001}]}, "Id": "a3cbe3f5-2dee-42ce-861c-5843cd13f7dc", "Relationships": [{"Type": "CHILD", "Ids": ["305eb8df-d49c-4100-aab7-22d8c326cef8"]}]}, {"BlockType": "LINE", "Confidence": 99.40778350830078, "Text": "156.532 3.089", "Geometry": {"BoundingBox": {"Width": 0.20858456194400787, "Height": 0.024122711271047592, "Left": 0.36177319288253784, "Top": 0.5533989667892456}, "Polygon": [{"X": 0.36177319288253784, "Y": 0.5628308057785034}, {"X": 0.5690231919288635, "Y": 0.5533989667892456}, {"X": 0.5703577995300293, "Y": 0.5685132741928101}, {"X": 0.3632071316242218, "Y": 0.5775216817855835}]}, "Id": "18e603cd-bcc7-4950-b112-4bfea96db13b", "Relationships": [{"Type": "CHILD", "Ids": ["dc0b7fcd-a9d4-42d0-b0ae-a69e1da625eb", "a56789ed-971c-4516-a5ca-6c3e68d07e8a"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "02", "Geometry": {"BoundingBox": {"Width": 0.03030087612569332, "Height": 0.015318077057600021, "Left": 0.2882184684276581, "Top": 0.5875791907310486}, "Polygon": [{"X": 0.2882184684276581, "Y": 0.5888034701347351}, {"X": 0.31710827350616455, "Y": 0.5875791907310486}, {"X": 0.31851935386657715, "Y": 0.6017301678657532}, {"X": 0.2896421551704407, "Y": 0.6028972864151001}]}, "Id": "47d615f1-ef20-4ec1-b870-e6e9822bfd97", "Relationships": [{"Type": "CHILD", "Ids": ["870b71bf-920b-4577-a5ff-a8ccd49f31f2"]}]}, {"BlockType": "LINE", "Confidence": 99.83647155761719, "Text": "10.434 3.599", "Geometry": {"BoundingBox": {"Width": 0.19410419464111328, "Height": 0.02243700809776783, "Left": 0.3788573443889618, "Top": 0.5759351253509521}, "Polygon": [{"X": 0.3788573443889618, "Y": 0.5841279029846191}, {"X": 0.5716705918312073, "Y": 0.5759351253509521}, {"X": 0.5729615688323975, "Y": 0.5905601382255554}, {"X": 0.3802381157875061, "Y": 0.5983721613883972}]}, "Id": "2a51e16e-1417-4073-92c7-b2df805e1f80", "Relationships": [{"Type": "CHILD", "Ids": ["e7b91241-4357-4772-b7a5-930b2b0fe644", "8713ad93-7edd-4de4-b739-bba84c3727a0"]}]}, {"BlockType": "LINE", "Confidence": 99.6021957397461, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.10629858076572418, "Height": 0.01903393678367138, "Left": 0.276034951210022, "Top": 0.6285870671272278}, "Polygon": [{"X": 0.276034951210022, "Y": 0.6324018239974976}, {"X": 0.3808349668979645, "Y": 0.6285870671272278}, {"X": 0.38233354687690735, "Y": 0.644029974937439}, {"X": 0.27758365869522095, "Y": 0.6476210355758667}]}, "Id": "5cdc8caf-6fd2-4e55-8cdd-41c27e9b5f5b", "Relationships": [{"Type": "CHILD", "Ids": ["1c05897f-0f33-4839-9e55-f41ce6d8a5fe"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.09043598920106888, "Height": 0.017723411321640015, "Left": 0.4877380132675171, "Top": 0.6199406385421753}, "Polygon": [{"X": 0.4877380132675171, "Y": 0.623203456401825}, {"X": 0.5768829584121704, "Y": 0.6199406385421753}, {"X": 0.5781739950180054, "Y": 0.6345769762992859}, {"X": 0.4890716075897217, "Y": 0.6376640796661377}]}, "Id": "459f6e32-7104-4c57-ad23-8dc917b98325", "Relationships": [{"Type": "CHILD", "Ids": ["0deb1159-df9a-44c8-83d8-88d36dbf9180"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.06042682006955147, "Height": 0.01638370007276535, "Left": 0.2782221734523773, "Top": 0.6532166004180908}, "Polygon": [{"X": 0.2782221734523773, "Y": 0.6551767587661743}, {"X": 0.33720770478248596, "Y": 0.6532166004180908}, {"X": 0.3386490046977997, "Y": 0.6677592992782593}, {"X": 0.2796899974346161, "Y": 0.6696003079414368}]}, "Id": "1b858cf9-4c1f-4af8-aed0-d10a6e0d0077", "Relationships": [{"Type": "CHILD", "Ids": ["b685f906-3630-421b-8f8d-27fdfd1af25c"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "483.53", "Geometry": {"BoundingBox": {"Width": 0.08999377489089966, "Height": 0.01728050969541073, "Left": 0.49009913206100464, "Top": 0.6426473259925842}, "Polygon": [{"X": 0.49009913206100464, "Y": 0.6456254720687866}, {"X": 0.5788159966468811, "Y": 0.6426473259925842}, {"X": 0.5800929069519043, "Y": 0.6571224331855774}, {"X": 0.49141791462898254, "Y": 0.6599278450012207}]}, "Id": "a0e753d5-688c-4e8f-b585-2f015065b4e8", "Relationships": [{"Type": "CHILD", "Ids": ["50dbd2a6-61e4-4e74-b0bd-4a54439e0fc1"]}]}, {"BlockType": "LINE", "Confidence": 99.57848358154297, "Text": "DEFBULK", "Geometry": {"BoundingBox": {"Width": 0.10585343092679977, "Height": 0.019275188446044922, "Left": 0.27985337376594543, "Top": 0.6732056140899658}, "Polygon": [{"X": 0.27985337376594543, "Y": 0.6763634085655212}, {"X": 0.3841192126274109, "Y": 0.6732056140899658}, {"X": 0.3857068121433258, "Y": 0.6895580887794495}, {"X": 0.2814939618110657, "Y": 0.6924808025360107}]}, "Id": "dd20da75-bdfe-4f44-a1f8-302758aa8f92", "Relationships": [{"Type": "CHILD", "Ids": ["2b7e705a-67e3-406c-aa08-7578c87ce7f2"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "37.55", "Geometry": {"BoundingBox": {"Width": 0.07600042968988419, "Height": 0.017031880095601082, "Left": 0.5067011713981628, "Top": 0.6655344367027283}, "Polygon": [{"X": 0.5067011713981628, "Y": 0.667813241481781}, {"X": 0.5813874006271362, "Y": 0.6655344367027283}, {"X": 0.5827016234397888, "Y": 0.6804370284080505}, {"X": 0.508051872253418, "Y": 0.6825662851333618}]}, "Id": "774d1187-324f-4293-b064-b145acb3d84a", "Relationships": [{"Type": "CHILD", "Ids": ["3c62d7e3-bc0a-4b4b-b87c-12bb2a6a8c4f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.07593023031949997, "Height": 0.016096197068691254, "Left": 0.2827349901199341, "Top": 0.7203758955001831}, "Polygon": [{"X": 0.2827349901199341, "Y": 0.722158670425415}, {"X": 0.35724061727523804, "Y": 0.7203758955001831}, {"X": 0.35866522789001465, "Y": 0.7348381876945496}, {"X": 0.28419309854507446, "Y": 0.7364721298217773}]}, "Id": "a5d97fe8-add2-4b4b-9281-247919770b1f", "Relationships": [{"Type": "CHILD", "Ids": ["203b1240-871d-495b-9327-cb4cb5fedb23"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "521.08", "Geometry": {"BoundingBox": {"Width": 0.09177115559577942, "Height": 0.017236866056919098, "Left": 0.48000794649124146, "Top": 0.7122962474822998}, "Polygon": [{"X": 0.48000794649124146, "Y": 0.7144956588745117}, {"X": 0.570426344871521, "Y": 0.7122962474822998}, {"X": 0.5717790722846985, "Y": 0.7275184988975525}, {"X": 0.481405645608902, "Y": 0.7295331358909607}]}, "Id": "e848d811-3fb2-4e34-a2c2-1f62960fac79", "Relationships": [{"Type": "CHILD", "Ids": ["cf36baaa-ddc7-41e3-81c7-5acd738a51d0"]}]}, {"BlockType": "LINE", "Confidence": 99.89530181884766, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.28693991899490356, "Height": 0.02354934997856617, "Left": 0.28882771730422974, "Top": 0.7581726312637329}, "Polygon": [{"X": 0.28882771730422974, "Y": 0.7633645534515381}, {"X": 0.5740711092948914, "Y": 0.7581726312637329}, {"X": 0.5757676362991333, "Y": 0.7772589325904846}, {"X": 0.2906962037086487, "Y": 0.7817219495773315}]}, "Id": "30b224d9-1270-4f19-9754-60166e26a196", "Relationships": [{"Type": "CHILD", "Ids": ["e7911341-d6a3-44a5-af01-e6408a9dc6a2", "ba8e282a-4dc7-4236-814d-f464da64f6ae", "f779d6aa-9156-423b-903b-ad23c32192b3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.13328787684440613, "Height": 0.015709785744547844, "Left": 0.2923172414302826, "Top": 0.7845606803894043}, "Polygon": [{"X": 0.2923172414302826, "Y": 0.7865380048751831}, {"X": 0.*****************, "Y": 0.7845606803894043}, {"X": 0.****************, "Y": 0.7985449433326721}, {"X": 0.2937142848968506, "Y": 0.8002704381942749}]}, "Id": "0cb4a8ca-fdba-4215-8f1f-3958b5055a49", "Relationships": [{"Type": "CHILD", "Ids": ["7cb986bd-ec8e-4313-b784-dc32715c2b7b", "bef06c9e-5b1b-4b6a-8964-b14263378a13"]}]}, {"BlockType": "LINE", "Confidence": 99.86288452148438, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.08864691108465195, "Height": 0.014130685478448868, "Left": 0.29920560121536255, "Top": 0.8254739046096802}, "Polygon": [{"X": 0.29920560121536255, "Y": 0.8263012170791626}, {"X": 0.3865377604961395, "Y": 0.8254739046096802}, {"X": 0.3878525197505951, "Y": 0.838938295841217}, {"X": 0.30055728554725647, "Y": 0.8396045565605164}]}, "Id": "e8807f9c-e0c7-4dbb-95b9-631bca53e6ff", "Relationships": [{"Type": "CHILD", "Ids": ["c7cece7c-55b0-4620-8101-01637c34852b", "bd1fc9ff-60eb-4ad2-a59b-4939f71aca31"]}]}, {"BlockType": "LINE", "Confidence": 99.21337127685547, "Text": "P50332", "Geometry": {"BoundingBox": {"Width": 0.09131455421447754, "Height": 0.013932195492088795, "Left": 0.5061689615249634, "Top": 0.8226902484893799}, "Polygon": [{"X": 0.5061689615249634, "Y": 0.8235538005828857}, {"X": 0.5963176488876343, "Y": 0.8226902484893799}, {"X": 0.5974835157394409, "Y": 0.8359177112579346}, {"X": 0.5073742270469666, "Y": 0.8366224765777588}]}, "Id": "5b725fb7-9cd3-416b-b407-0ac680c4149d", "Relationships": [{"Type": "CHILD", "Ids": ["8eb5bcb5-5fd7-4989-ae12-e82ce880ff42"]}]}, {"BlockType": "LINE", "Confidence": 99.5566635131836, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.2664298415184021, "Height": 0.01609138771891594, "Left": 0.30230939388275146, "Top": 0.8427984118461609}, "Polygon": [{"X": 0.30230939388275146, "Y": 0.8446370363235474}, {"X": 0.5674163699150085, "Y": 0.8427984118461609}, {"X": 0.5687392354011536, "Y": 0.8575743436813354}, {"X": 0.3037567436695099, "Y": 0.8588898181915283}]}, "Id": "86ce97aa-6b9a-4453-80f1-0772ceee1ca3", "Relationships": [{"Type": "CHILD", "Ids": ["2da2d551-775f-4827-b959-0ead9b06688f", "adfb1c08-d5c1-4aed-8fdb-c97005a41e6b"]}]}, {"BlockType": "LINE", "Confidence": 99.50892639160156, "Text": "AUTH REF#: P50332", "Geometry": {"BoundingBox": {"Width": 0.25114279985427856, "Height": 0.016241956502199173, "Left": 0.32009533047676086, "Top": 0.8618265390396118}, "Polygon": [{"X": 0.32009533047676086, "Y": 0.8629246354103088}, {"X": 0.5698360800743103, "Y": 0.8618265390396118}, {"X": 0.571238100528717, "Y": 0.8774922490119934}, {"X": 0.32162219285964966, "Y": 0.8780685067176819}]}, "Id": "5fd0cc0c-4812-4f10-acdf-f2247850d91e", "Relationships": [{"Type": "CHILD", "Ids": ["604df21a-17a9-416c-bcce-a4d7b871a979", "e05a004e-af81-463f-9e07-2e2aaa31c762", "158dd6a3-d344-46f6-8015-94eae8d9c89a"]}]}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060759659856557846, "Height": 0.020039265975356102, "Left": 0.290267676115036, "Top": 0.33823150396347046}, "Polygon": [{"X": 0.290267676115036, "Y": 0.34275898337364197}, {"X": 0.34950724244117737, "Y": 0.33823150396347046}, {"X": 0.35102733969688416, "Y": 0.3538735508918762}, {"X": 0.2918161153793335, "Y": 0.3582707643508911}]}, "Id": "20a66baf-a957-4668-a53b-f31e99493059"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062195006757974625, "Height": 0.019919486716389656, "Left": 0.36426544189453125, "Top": 0.33358103036880493}, "Polygon": [{"X": 0.36426544189453125, "Y": 0.33821460604667664}, {"X": 0.4250141680240631, "Y": 0.33358103036880493}, {"X": 0.42646047472953796, "Y": 0.3489972949028015}, {"X": 0.36574095487594604, "Y": 0.35350051522254944}]}, "Id": "c3dc8091-834f-4065-81ed-cb66ba4f1674"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "#209", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06265989691019058, "Height": 0.019801031798124313, "Left": 0.4408811628818512, "Top": 0.3286615312099457}, "Polygon": [{"X": 0.4408811628818512, "Y": 0.33332768082618713}, {"X": 0.5021615028381348, "Y": 0.3286615312099457}, {"X": 0.5035410523414612, "Y": 0.34392523765563965}, {"X": 0.44229039549827576, "Y": 0.3484625518321991}]}, "Id": "b0af35d1-3a35-490f-8594-a7e3548019a7"}, {"BlockType": "WORD", "Confidence": 99.77300262451172, "Text": "1101", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05610014125704765, "Height": 0.019142797216773033, "Left": 0.24924273788928986, "Top": 0.3645838499069214}, "Polygon": [{"X": 0.24924273788928986, "Y": 0.3685831129550934}, {"X": 0.3038271367549896, "Y": 0.3645838499069214}, {"X": 0.3053428828716278, "Y": 0.3798450827598572}, {"X": 0.25078368186950684, "Y": 0.38372665643692017}]}, "Id": "ede2641b-f164-4214-bf8b-d8fc7dfdf687"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "GERTRUDE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1214914619922638, "Height": 0.024504652246832848, "Left": 0.32324591279029846, "Top": 0.35515910387039185}, "Polygon": [{"X": 0.32324591279029846, "Y": 0.363938570022583}, {"X": 0.4432486295700073, "Y": 0.35515910387039185}, {"X": 0.44473737478256226, "Y": 0.3711503744125366}, {"X": 0.32479414343833923, "Y": 0.37966373562812805}]}, "Id": "0adb7edc-b2b9-42a8-940b-906e2ab4fb71"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "STREET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09097463637590408, "Height": 0.021463289856910706, "Left": 0.46045470237731934, "Top": 0.349026620388031}, "Polygon": [{"X": 0.46045470237731934, "Y": 0.35556453466415405}, {"X": 0.550094485282898, "Y": 0.349026620388031}, {"X": 0.5514293313026428, "Y": 0.364137202501297}, {"X": 0.46183285117149353, "Y": 0.3704899251461029}]}, "Id": "8a6bbc0a-880b-4346-b6f0-e32d34ea2f77"}, {"BlockType": "WORD", "Confidence": 99.23369598388672, "Text": "KAUKAUNA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1329302340745926, "Height": 0.028724275529384613, "Left": 0.3113258481025696, "Top": 0.3781638741493225}, "Polygon": [{"X": 0.3113258481025696, "Y": 0.3873456120491028}, {"X": 0.44240042567253113, "Y": 0.3781638741493225}, {"X": 0.444256067276001, "Y": 0.39806777238845825}, {"X": 0.3132622241973877, "Y": 0.4068881571292877}]}, "Id": "510d516b-a793-4f80-b846-3cb34ef8395f"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03216695040464401, "Height": 0.017061008140444756, "Left": 0.4607207477092743, "Top": 0.37632840871810913}, "Polygon": [{"X": 0.4607207477092743, "Y": 0.378479540348053}, {"X": 0.49152448773384094, "Y": 0.37632840871810913}, {"X": 0.4928877055644989, "Y": 0.3913017809391022}, {"X": 0.46209871768951416, "Y": 0.39338940382003784}]}, "Id": "f629eb95-cd9f-4521-8da2-234564fbfb24"}, {"BlockType": "WORD", "Confidence": 99.49238586425781, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11913804709911346, "Height": 0.023392299190163612, "Left": 0.259107768535614, "Top": 0.4270009994506836}, "Polygon": [{"X": 0.259107768535614, "Y": 0.4345261752605438}, {"X": 0.37669143080711365, "Y": 0.4270009994506836}, {"X": 0.3782458007335663, "Y": 0.4431327283382416}, {"X": 0.2607201337814331, "Y": 0.45039328932762146}]}, "Id": "eb096a82-0ba8-4e2b-9697-33f6854525d4"}, {"BlockType": "WORD", "Confidence": 99.40290832519531, "Text": "0091442", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10437242686748505, "Height": 0.021465882658958435, "Left": 0.4073094427585602, "Top": 0.4207530617713928}, "Polygon": [{"X": 0.4073094427585602, "Y": 0.4273133873939514}, {"X": 0.5103152990341187, "Y": 0.4207530617713928}, {"X": 0.511681854724884, "Y": 0.43587204813957214}, {"X": 0.408725380897522, "Y": 0.44221892952919006}]}, "Id": "0d4e760c-b883-4dc9-b9ac-6bf06291bcf2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07657147943973541, "Height": 0.019441720098257065, "Left": 0.25814923644065857, "Top": 0.4527983069419861}, "Polygon": [{"X": 0.25814923644065857, "Y": 0.4573662281036377}, {"X": 0.3332415521144867, "Y": 0.4527983069419861}, {"X": 0.3347207009792328, "Y": 0.4678303599357605}, {"X": 0.25966283679008484, "Y": 0.47224003076553345}]}, "Id": "ea6c3f27-f5fc-437c-a587-84a115f93d06"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01766776852309704, "Height": 0.015075468458235264, "Left": 0.36345937848091125, "Top": 0.45096153020858765}, "Polygon": [{"X": 0.36345937848091125, "Y": 0.4519513249397278}, {"X": 0.3797673285007477, "Y": 0.45096153020858765}, {"X": 0.38112714886665344, "Y": 0.46507927775382996}, {"X": 0.36482641100883484, "Y": 0.46603700518608093}]}, "Id": "cefd781d-497b-4615-a40e-e1f68c7a8eb1"}, {"BlockType": "WORD", "Confidence": 89.32477569580078, "Text": "0900-1442", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.132725790143013, "Height": 0.023291926831007004, "Left": 0.4098109304904938, "Top": 0.4414438009262085}, "Polygon": [{"X": 0.4098109304904938, "Y": 0.4494103193283081}, {"X": 0.5411463379859924, "Y": 0.4414438009262085}, {"X": 0.542536735534668, "Y": 0.457048624753952}, {"X": 0.4112665057182312, "Y": 0.4647357165813446}]}, "Id": "e47a403b-8520-4e5b-bbeb-aa96a2f02c36"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05978527292609215, "Height": 0.01742297224700451, "Left": 0.26228219270706177, "Top": 0.4760996401309967}, "Polygon": [{"X": 0.26228219270706177, "Y": 0.4794696569442749}, {"X": 0.3206638693809509, "Y": 0.4760996401309967}, {"X": 0.3220674693584442, "Y": 0.49026864767074585}, {"X": 0.2637110650539398, "Y": 0.49352261424064636}]}, "Id": "479ecece-917f-4f52-8dff-7dc255796c42"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "04/22/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14782235026359558, "Height": 0.025508040562272072, "Left": 0.4125238060951233, "Top": 0.463314950466156}, "Polygon": [{"X": 0.4125238060951233, "Y": 0.47174015641212463}, {"X": 0.5588054060935974, "Y": 0.463314950466156}, {"X": 0.5603461861610413, "Y": 0.48074427247047424}, {"X": 0.414145827293396, "Y": 0.4888229966163635}]}, "Id": "93542b16-fc13-407a-9839-8bc0f21e348e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0613318495452404, "Height": 0.018493013456463814, "Left": 0.26313695311546326, "Top": 0.49719947576522827}, "Polygon": [{"X": 0.26313695311546326, "Y": 0.5004770755767822}, {"X": 0.3229489326477051, "Y": 0.49719947576522827}, {"X": 0.32446882128715515, "Y": 0.5125435590744019}, {"X": 0.26468491554260254, "Y": 0.5156925320625305}]}, "Id": "82a3206c-03d4-4621-a0e4-90857ca25a15"}, {"BlockType": "WORD", "Confidence": 98.78189086914062, "Text": "01:00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07259397208690643, "Height": 0.01887928508222103, "Left": 0.4147174656391144, "Top": 0.4898165166378021}, "Polygon": [{"X": 0.4147174656391144, "Y": 0.49370354413986206}, {"X": 0.485922247171402, "Y": 0.4898165166378021}, {"X": 0.4873114228248596, "Y": 0.5049566030502319}, {"X": 0.4161408841609955, "Y": 0.5086957812309265}]}, "Id": "7e3f2182-8290-49df-8581-329ed1d6c6fa"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030569026246666908, "Height": 0.016283441334962845, "Left": 0.5015581846237183, "Top": 0.4886597692966461}, "Polygon": [{"X": 0.5015581846237183, "Y": 0.49025118350982666}, {"X": 0.5308033227920532, "Y": 0.4886597692966461}, {"X": 0.5321272015571594, "Y": 0.5034106373786926}, {"X": 0.5028960108757019, "Y": 0.5049432516098022}]}, "Id": "e3ef596c-3870-4134-9370-8e05db7278b5"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06045157462358475, "Height": 0.02056756801903248, "Left": 0.2691075801849365, "Top": 0.5427542328834534}, "Polygon": [{"X": 0.2691075801849365, "Y": 0.5455995202064514}, {"X": 0.32778945565223694, "Y": 0.5427542328834534}, {"X": 0.3295591473579407, "Y": 0.5606229901313782}, {"X": 0.27090945839881897, "Y": 0.5633217692375183}]}, "Id": "5f52d47f-3f59-4fed-a6b7-831897b61b32"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1189301460981369, "Height": 0.024943185970187187, "Left": 0.34443560242652893, "Top": 0.5348066091537476}, "Polygon": [{"X": 0.34443560242652893, "Y": 0.5405081510543823}, {"X": 0.46154651045799255, "Y": 0.5348066091537476}, {"X": 0.46336573362350464, "Y": 0.5543625950813293}, {"X": 0.3463267683982849, "Y": 0.5597497820854187}]}, "Id": "2699ae40-e50c-411c-8a6c-02a2a54295cb"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07666930556297302, "Height": 0.01882089488208294, "Left": 0.4913475811481476, "Top": 0.5312460064888}, "Polygon": [{"X": 0.4913475811481476, "Y": 0.5348971486091614}, {"X": 0.5666633248329163, "Y": 0.5312460064888}, {"X": 0.5680168867111206, "Y": 0.5465720891952515}, {"X": 0.49273860454559326, "Y": 0.5500669479370117}]}, "Id": "17b221a4-7d43-4f32-bb3f-470c887547d3"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03029600903391838, "Height": 0.015355591662228107, "Left": 0.28632232546806335, "Top": 0.5655689835548401}, "Polygon": [{"X": 0.28632232546806335, "Y": 0.5668812990188599}, {"X": 0.3152124881744385, "Y": 0.5655689835548401}, {"X": 0.31661832332611084, "Y": 0.5796692967414856}, {"X": 0.28774070739746094, "Y": 0.5809246301651001}]}, "Id": "305eb8df-d49c-4100-aab7-22d8c326cef8"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "156.532", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10321372002363205, "Height": 0.018761632964015007, "Left": 0.36177319288253784, "Top": 0.5581942200660706}, "Polygon": [{"X": 0.36177319288253784, "Y": 0.5628308057785034}, {"X": 0.4636542499065399, "Y": 0.5581942200660706}, {"X": 0.4649869203567505, "Y": 0.5725194215774536}, {"X": 0.3631519079208374, "Y": 0.5769558548927307}]}, "Id": "dc0b7fcd-a9d4-42d0-b0ae-a69e1da625eb"}, {"BlockType": "WORD", "Confidence": 98.89448547363281, "Text": "3.089", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07548598945140839, "Height": 0.017378900200128555, "Left": 0.4948717951774597, "Top": 0.5543612241744995}, "Polygon": [{"X": 0.4948717951774597, "Y": 0.5577301383018494}, {"X": 0.5691081881523132, "Y": 0.5543612241744995}, {"X": 0.5703577995300293, "Y": 0.5685132741928101}, {"X": 0.4961555600166321, "Y": 0.5717401504516602}]}, "Id": "a56789ed-971c-4516-a5ca-6c3e68d07e8a"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03030087612569332, "Height": 0.015318077057600021, "Left": 0.2882184684276581, "Top": 0.5875791907310486}, "Polygon": [{"X": 0.2882184684276581, "Y": 0.5888034701347351}, {"X": 0.31710827350616455, "Y": 0.5875791907310486}, {"X": 0.31851935386657715, "Y": 0.6017301678657532}, {"X": 0.2896421551704407, "Y": 0.6028972864151001}]}, "Id": "870b71bf-920b-4577-a5ff-a8ccd49f31f2"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "10.434", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08776884526014328, "Height": 0.017691615968942642, "Left": 0.3788573443889618, "Top": 0.5804546475410461}, "Polygon": [{"X": 0.3788573443889618, "Y": 0.5841279029846191}, {"X": 0.4653061628341675, "Y": 0.5804546475410461}, {"X": 0.46662619709968567, "Y": 0.5946409702301025}, {"X": 0.38021621108055115, "Y": 0.5981462597846985}]}, "Id": "e7b91241-4357-4772-b7a5-930b2b0fe644"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "3.599", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07549432665109634, "Height": 0.01728568971157074, "Left": 0.4974672198295593, "Top": 0.5762821435928345}, "Polygon": [{"X": 0.4974672198295593, "Y": 0.5794329047203064}, {"X": 0.5717012286186218, "Y": 0.5762821435928345}, {"X": 0.5729615688323975, "Y": 0.5905601382255554}, {"X": 0.4987620413303375, "Y": 0.5935677886009216}]}, "Id": "8713ad93-7edd-4de4-b739-bba84c3727a0"}, {"BlockType": "WORD", "Confidence": 99.6021957397461, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10629858076572418, "Height": 0.01903393678367138, "Left": 0.276034951210022, "Top": 0.6285870671272278}, "Polygon": [{"X": 0.276034951210022, "Y": 0.6324018239974976}, {"X": 0.3808349668979645, "Y": 0.6285870671272278}, {"X": 0.38233354687690735, "Y": 0.644029974937439}, {"X": 0.27758365869522095, "Y": 0.6476210355758667}]}, "Id": "1c05897f-0f33-4839-9e55-f41ce6d8a5fe"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09043598920106888, "Height": 0.017723411321640015, "Left": 0.4877380132675171, "Top": 0.6199406385421753}, "Polygon": [{"X": 0.4877380132675171, "Y": 0.623203456401825}, {"X": 0.5768829584121704, "Y": 0.6199406385421753}, {"X": 0.5781739950180054, "Y": 0.6345769762992859}, {"X": 0.4890716075897217, "Y": 0.6376640796661377}]}, "Id": "0deb1159-df9a-44c8-83d8-88d36dbf9180"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06042682006955147, "Height": 0.01638370007276535, "Left": 0.2782221734523773, "Top": 0.6532166004180908}, "Polygon": [{"X": 0.2782221734523773, "Y": 0.6551767587661743}, {"X": 0.33720770478248596, "Y": 0.6532166004180908}, {"X": 0.3386490046977997, "Y": 0.6677592992782593}, {"X": 0.2796899974346161, "Y": 0.6696003079414368}]}, "Id": "b685f906-3630-421b-8f8d-27fdfd1af25c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "483.53", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08999377489089966, "Height": 0.01728050969541073, "Left": 0.49009913206100464, "Top": 0.6426473259925842}, "Polygon": [{"X": 0.49009913206100464, "Y": 0.6456254720687866}, {"X": 0.5788159966468811, "Y": 0.6426473259925842}, {"X": 0.5800929069519043, "Y": 0.6571224331855774}, {"X": 0.49141791462898254, "Y": 0.6599278450012207}]}, "Id": "50dbd2a6-61e4-4e74-b0bd-4a54439e0fc1"}, {"BlockType": "WORD", "Confidence": 99.57848358154297, "Text": "DEFBULK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10585343092679977, "Height": 0.019275188446044922, "Left": 0.27985337376594543, "Top": 0.6732056140899658}, "Polygon": [{"X": 0.27985337376594543, "Y": 0.6763634085655212}, {"X": 0.3841192126274109, "Y": 0.6732056140899658}, {"X": 0.3857068121433258, "Y": 0.6895580887794495}, {"X": 0.2814939618110657, "Y": 0.6924808025360107}]}, "Id": "2b7e705a-67e3-406c-aa08-7578c87ce7f2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "37.55", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07600042968988419, "Height": 0.017031880095601082, "Left": 0.5067011713981628, "Top": 0.6655344367027283}, "Polygon": [{"X": 0.5067011713981628, "Y": 0.667813241481781}, {"X": 0.5813874006271362, "Y": 0.6655344367027283}, {"X": 0.5827016234397888, "Y": 0.6804370284080505}, {"X": 0.508051872253418, "Y": 0.6825662851333618}]}, "Id": "3c62d7e3-bc0a-4b4b-b87c-12bb2a6a8c4f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07593023031949997, "Height": 0.016096197068691254, "Left": 0.2827349901199341, "Top": 0.7203758955001831}, "Polygon": [{"X": 0.2827349901199341, "Y": 0.722158670425415}, {"X": 0.35724061727523804, "Y": 0.7203758955001831}, {"X": 0.35866522789001465, "Y": 0.7348381876945496}, {"X": 0.28419309854507446, "Y": 0.7364721298217773}]}, "Id": "203b1240-871d-495b-9327-cb4cb5fedb23"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "521.08", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09177115559577942, "Height": 0.017236866056919098, "Left": 0.48000794649124146, "Top": 0.7122962474822998}, "Polygon": [{"X": 0.48000794649124146, "Y": 0.7144956588745117}, {"X": 0.570426344871521, "Y": 0.7122962474822998}, {"X": 0.5717790722846985, "Y": 0.7275184988975525}, {"X": 0.481405645608902, "Y": 0.7295331358909607}]}, "Id": "cf36baaa-ddc7-41e3-81c7-5acd738a51d0"}, {"BlockType": "WORD", "Confidence": 99.68590545654297, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11930003017187119, "Height": 0.018559912219643593, "Left": 0.28902196884155273, "Top": 0.7631620764732361}, "Polygon": [{"X": 0.28902196884155273, "Y": 0.7652730345726013}, {"X": 0.40670931339263916, "Y": 0.7631620764732361}, {"X": 0.4083220064640045, "Y": 0.7798804640769958}, {"X": 0.2906962037086487, "Y": 0.7817219495773315}]}, "Id": "e7911341-d6a3-44a5-af01-e6408a9dc6a2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061614640057086945, "Height": 0.015499393455684185, "Left": 0.42175593972206116, "Top": 0.7611485123634338}, "Polygon": [{"X": 0.42175593972206116, "Y": 0.7622348666191101}, {"X": 0.4820183515548706, "Y": 0.7611485123634338}, {"X": 0.4833706021308899, "Y": 0.7756803631782532}, {"X": 0.4231364130973816, "Y": 0.7766479253768921}]}, "Id": "ba8e282a-4dc7-4236-814d-f464da64f6ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07653439790010452, "Height": 0.01590917631983757, "Left": 0.****************, "Top": 0.7581726312637329}, "Polygon": [{"X": 0.****************, "Y": 0.759541928768158}, {"X": 0.5740711092948914, "Y": 0.7581726312637329}, {"X": 0.5753766894340515, "Y": 0.7728604674339294}, {"X": 0.5001841187477112, "Y": 0.774081826210022}]}, "Id": "f779d6aa-9156-423b-903b-ad23c32192b3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07465524226427078, "Height": 0.014422754757106304, "Left": 0.2923583984375, "Top": 0.7858476638793945}, "Polygon": [{"X": 0.2923583984375, "Y": 0.7869424819946289}, {"X": 0.36568859219551086, "Y": 0.7858476638793945}, {"X": 0.3670136332511902, "Y": 0.7993114590644836}, {"X": 0.2937142848968506, "Y": 0.8002704381942749}]}, "Id": "7cb986bd-ec8e-4313-b784-dc32715c2b7b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04279923066496849, "Height": 0.014176077209413052, "Left": 0.3827720880508423, "Top": 0.7845606803894043}, "Polygon": [{"X": 0.3827720880508423, "Y": 0.7851824760437012}, {"X": 0.*****************, "Y": 0.7845606803894043}, {"X": 0.****************, "Y": 0.7981921434402466}, {"X": 0.38409551978111267, "Y": 0.7987367510795593}]}, "Id": "bef06c9e-5b1b-4b6a-8964-b14263378a13"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05883470177650452, "Height": 0.0138481380417943, "Left": 0.29920560121536255, "Top": 0.8257564306259155}, "Polygon": [{"X": 0.29920560121536255, "Y": 0.8263012170791626}, {"X": 0.35671278834342957, "Y": 0.8257564306259155}, {"X": 0.35804030299186707, "Y": 0.8391658067703247}, {"X": 0.30055728554725647, "Y": 0.8396045565605164}]}, "Id": "c7cece7c-55b0-4620-8101-01637c34852b"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01658078096807003, "Height": 0.013020748272538185, "Left": 0.3712441921234131, "Top": 0.8257529735565186}, "Polygon": [{"X": 0.3712441921234131, "Y": 0.8258975744247437}, {"X": 0.3865649998188019, "Y": 0.8257529735565186}, {"X": 0.38782498240470886, "Y": 0.8386562466621399}, {"X": 0.3725104331970215, "Y": 0.8387737274169922}]}, "Id": "bd1fc9ff-60eb-4ad2-a59b-4939f71aca31"}, {"BlockType": "WORD", "Confidence": 99.21337127685547, "Text": "P50332", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09131455421447754, "Height": 0.013932195492088795, "Left": 0.5061689615249634, "Top": 0.8226902484893799}, "Polygon": [{"X": 0.5061689615249634, "Y": 0.8235538005828857}, {"X": 0.5963176488876343, "Y": 0.8226902484893799}, {"X": 0.5974835157394409, "Y": 0.8359177112579346}, {"X": 0.5073742270469666, "Y": 0.8366224765777588}]}, "Id": "8eb5bcb5-5fd7-4989-ae12-e82ce880ff42"}, {"BlockType": "WORD", "Confidence": 99.11331939697266, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12804368138313293, "Height": 0.014143113046884537, "Left": 0.3024080693721771, "Top": 0.8447467088699341}, "Polygon": [{"X": 0.3024080693721771, "Y": 0.8456087112426758}, {"X": 0.4291570782661438, "Y": 0.8447467088699341}, {"X": 0.43045175075531006, "Y": 0.8582608699798584}, {"X": 0.3037567436695099, "Y": 0.8588898181915283}]}, "Id": "2da2d551-775f-4827-b959-0ead9b06688f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11924300342798233, "Height": 0.0139918839558959, "Left": 0.4493716359138489, "Top": 0.8427984118461609}, "Polygon": [{"X": 0.4493716359138489, "Y": 0.8436170816421509}, {"X": 0.5674163699150085, "Y": 0.8427984118461609}, {"X": 0.568614661693573, "Y": 0.8561826348304749}, {"X": 0.45062151551246643, "Y": 0.85679030418396}]}, "Id": "adfb1c08-d5c1-4aed-8fdb-c97005a41e6b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05844935402274132, "Height": 0.01308942399919033, "Left": 0.3203260004520416, "Top": 0.8649790287017822}, "Polygon": [{"X": 0.3203260004520416, "Y": 0.8652124404907227}, {"X": 0.3775025010108948, "Y": 0.8649790287017822}, {"X": 0.37877532839775085, "Y": 0.8779365420341492}, {"X": 0.32162219285964966, "Y": 0.8780685067176819}]}, "Id": "604df21a-17a9-416c-bcce-a4d7b871a979"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07040300220251083, "Height": 0.01275283470749855, "Left": 0.392814576625824, "Top": 0.8636322617530823}, "Polygon": [{"X": 0.392814576625824, "Y": 0.8639241456985474}, {"X": 0.46203023195266724, "Y": 0.8636322617530823}, {"X": 0.4632175862789154, "Y": 0.8762110471725464}, {"X": 0.3940299451351166, "Y": 0.8763850927352905}]}, "Id": "e05a004e-af81-463f-9e07-2e2aaa31c762"}, {"BlockType": "WORD", "Confidence": 98.77490997314453, "Text": "P50332", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08940593898296356, "Height": 0.012796090915799141, "Left": 0.481553852558136, "Top": 0.8618265390396118}, "Polygon": [{"X": 0.481553852558136, "Y": 0.8622146844863892}, {"X": 0.5698360800743103, "Y": 0.8618265390396118}, {"X": 0.5709598064422607, "Y": 0.8743823170661926}, {"X": 0.4827139973640442, "Y": 0.874622642993927}]}, "Id": "158dd6a3-d344-46f6-8015-94eae8d9c89a"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d8c9a6c3-0876-4163-84fe-a60edc16afb0", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d8c9a6c3-0876-4163-84fe-a60edc16afb0", "content-type": "application/x-amz-json-1.1", "content-length": "40112", "date": "Wed, 24 Sep 2025 12:47:13 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "2288cd4b-786a-4aee-9708-34a505b0586b", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 12:47:16 GMT", "content-type": "application/json", "content-length": "834", "connection": "keep-alive", "x-amzn-requestid": "2288cd4b-786a-4aee-9708-34a505b0586b"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The content appears to be a fuel receipt maybe? It has fields: KWIK TRIP #209, address, Invoice#, Date, Time, Pump Quantity Price, etc. \"NBS/KWIK TRIP FLEET CARD# ICR AUTH # P50332 RESPONSE: APPROVED AUTH REF#: P50332\". Likely a fuel receipt. Keywords: Fuel, Pump, Quantity, Price. No explicit \"Fuel Receipt\" but typical fuel receipt content. The doc type should be fuel_receipt. There's no other type that matches better. So output: page_no 1, doc_type fuel_receipt. Use the tool call."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2665, "outputTokens": 155, "totalTokens": 2820}, "metrics": {"latencyMs": 876}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/e435e2c7_H0ZEPTKWB941MNO7G87A.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP #209\n1101 GERTRUDE STREET\nKAUKAUNA, WI\nInvoice# 0091442\nTrans # 0900-1442\nDate\n04/22/2025\nTime\n01:00 PM\nPump Quantity Price\n02\n156.532 3.089\n02\n10.434 3.599\nProduct\nAmount\nDSL2\n483.53\nDEFBULK\n37.55\nTOTAL\n521.08\nNBS/KWIK TRIP FLEET\nCARD# ICR\nAUTH #\nP50332\nRESPONSE: APPROVED\nAUTH REF#: P50332\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 2500}}}