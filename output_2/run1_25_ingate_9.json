{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.5725078128525638e-08, "Y": 1.0}]}, "Id": "c548fdde-7010-486c-ae9a-f13ee863bf98", "Relationships": [{"Type": "CHILD", "Ids": ["4dedef9b-5978-4c05-8535-007cbf663147", "b74d5602-12be-44de-85d3-8ad0256fce06", "c72677fc-4b0a-4d26-a61d-4fa0b3a59abc", "dacd624c-1ddb-4ec3-8618-79f0e3d1135f", "cb91663d-4933-46e1-9131-d466b789a1f5", "a2ba85f3-2f8c-45c3-980b-7841c62f0b4b", "465ebfdb-5a2b-413a-8f5a-e322d6b91d80", "011e0944-b9a3-4549-9cf2-2cf864df5261", "667ced50-28e1-4481-b08c-83cefa52aa4c", "93de9dbf-4c1c-4492-8a03-6ecfc3c3cef3", "8f222282-d03e-4e21-a653-3baeb46da70b", "ee0c7549-af1a-4965-afb5-e07596938dbd", "51ca6421-fd37-4be5-80dc-59b1299bf923", "f3cdc3e6-0b27-421b-93b9-3b74b8029066", "b441944e-e310-42a5-8d18-5fe798b707b7"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "TLLU3608177", "Geometry": {"BoundingBox": {"Width": 0.2687617838382721, "Height": 0.058741021901369095, "Left": 0.29509884119033813, "Top": 0.050430528819561005}, "Polygon": [{"X": 0.29509884119033813, "Y": 0.050433188676834106}, {"X": 0.5638601183891296, "Y": 0.050430528819561005}, {"X": 0.5638605952262878, "Y": 0.10916909575462341}, {"X": 0.2950993776321411, "Y": 0.1091715469956398}]}, "Id": "4dedef9b-5978-4c05-8535-007cbf663147", "Relationships": [{"Type": "CHILD", "Ids": ["f537651d-959d-4ad5-b6de-4d4e540651c5"]}]}, {"BlockType": "LINE", "Confidence": 99.26787567138672, "Text": "ConGlobal Industries", "Geometry": {"BoundingBox": {"Width": 0.23937083780765533, "Height": 0.03674349933862686, "Left": 0.3112500309944153, "Top": 0.16674184799194336}, "Polygon": [{"X": 0.3112500309944153, "Y": 0.16674385964870453}, {"X": 0.5506205558776855, "Y": 0.16674184799194336}, {"X": 0.5506208539009094, "Y": 0.203483447432518}, {"X": 0.31125038862228394, "Y": 0.20348535478115082}]}, "Id": "b74d5602-12be-44de-85d3-8ad0256fce06", "Relationships": [{"Type": "CHILD", "Ids": ["4b56a2d8-a386-4feb-a744-1539e8481f81", "523d3540-76cf-49ba-bffd-615d7bc269f1"]}]}, {"BlockType": "LINE", "Confidence": 99.95018005371094, "Text": "500 Mayo Shell Road", "Geometry": {"BoundingBox": {"Width": 0.2395089864730835, "Height": 0.046877555549144745, "Left": 0.3111484944820404, "Top": 0.23097626864910126}, "Polygon": [{"X": 0.3111484944820404, "Y": 0.2309780865907669}, {"X": 0.5506570935249329, "Y": 0.23097626864910126}, {"X": 0.5506574511528015, "Y": 0.2778521478176117}, {"X": 0.31114891171455383, "Y": 0.2778538167476654}]}, "Id": "c72677fc-4b0a-4d26-a61d-4fa0b3a59abc", "Relationships": [{"Type": "CHILD", "Ids": ["356c3232-1eea-4cff-9d13-85aa2bd25ee6", "0a425f52-0ebc-4109-8d73-ba4b034bba72", "0dc96dee-9128-4e7d-a10c-22a3b5d051c7", "e302b20c-8ed5-49e6-8813-e52e98eb82ff"]}]}, {"BlockType": "LINE", "Confidence": 99.70489501953125, "Text": "Galena Park, TX 77547", "Geometry": {"BoundingBox": {"Width": 0.25349724292755127, "Height": 0.0411062017083168, "Left": 0.30410119891166687, "Top": 0.295520544052124}, "Polygon": [{"X": 0.30410119891166687, "Y": 0.2955222427845001}, {"X": 0.5575981140136719, "Y": 0.295520544052124}, {"X": 0.5575984716415405, "Y": 0.3366251587867737}, {"X": 0.3041015863418579, "Y": 0.33662673830986023}]}, "Id": "dacd624c-1ddb-4ec3-8618-79f0e3d1135f", "Relationships": [{"Type": "CHILD", "Ids": ["f210b426-807a-4952-aa0c-d0846f444b2c", "1375b623-bc1b-4274-8d09-c718518eeef3", "b388866f-f336-442c-a428-271659ae4776", "ee8536e8-8ba4-46fd-b154-616e08e0ba06"]}]}, {"BlockType": "LINE", "Confidence": 99.72257995605469, "Text": "(713)-675-7587", "Geometry": {"BoundingBox": {"Width": 0.17537260055541992, "Height": 0.04340914264321327, "Left": 0.34376299381256104, "Top": 0.3611062467098236}, "Polygon": [{"X": 0.34376299381256104, "Y": 0.3611072897911072}, {"X": 0.5191352367401123, "Y": 0.3611062467098236}, {"X": 0.519135594367981, "Y": 0.4045144319534302}, {"X": 0.3437633812427521, "Y": 0.4045153856277466}]}, "Id": "cb91663d-4933-46e1-9131-d466b789a1f5", "Relationships": [{"Type": "CHILD", "Ids": ["3b068599-34be-4664-a79a-43fe2773224d"]}]}, {"BlockType": "LINE", "Confidence": 99.72098541259766, "Text": "Customer: TRITN", "Geometry": {"BoundingBox": {"Width": 0.200606107711792, "Height": 0.035420872271060944, "Left": 0.019274786114692688, "Top": 0.5010012984275818}, "Polygon": [{"X": 0.019274786114692688, "Y": 0.5010021328926086}, {"X": 0.21988055109977722, "Y": 0.5010012984275818}, {"X": 0.21988089382648468, "Y": 0.5364214181900024}, {"X": 0.019275154918432236, "Y": 0.5364221334457397}]}, "Id": "a2ba85f3-2f8c-45c3-980b-7841c62f0b4b", "Relationships": [{"Type": "CHILD", "Ids": ["c06c3f90-52b7-4e27-83dd-5d232d9cefd9", "5529f45d-7c2c-4d4a-b4ee-f16396df721f"]}]}, {"BlockType": "LINE", "Confidence": 99.62850952148438, "Text": "In Date: 2023-01-11", "Geometry": {"BoundingBox": {"Width": 0.23169055581092834, "Height": 0.035177040845155716, "Left": 0.6019259691238403, "Top": 0.5013899803161621}, "Polygon": [{"X": 0.6019259691238403, "Y": 0.5013909339904785}, {"X": 0.8336162567138672, "Y": 0.5013899803161621}, {"X": 0.8336164951324463, "Y": 0.5365661978721619}, {"X": 0.6019262671470642, "Y": 0.5365670323371887}]}, "Id": "465ebfdb-5a2b-413a-8f5a-e322d6b91d80", "Relationships": [{"Type": "CHILD", "Ids": ["7fb6bd33-23e1-4239-8fe4-0dbaf6de5f96", "9ad7b75a-b84a-4058-9bc7-d778231d486f", "cc39c77b-60f9-4f6d-a8d3-2b468fade824"]}]}, {"BlockType": "LINE", "Confidence": 99.78605651855469, "Text": "Depot: HOU05", "Geometry": {"BoundingBox": {"Width": 0.17208261787891388, "Height": 0.04487423971295357, "Left": 0.020069705322384834, "Top": 0.5873712301254272}, "Polygon": [{"X": 0.020069705322384834, "Y": 0.5873717665672302}, {"X": 0.19215188920497894, "Y": 0.5873712301254272}, {"X": 0.19215232133865356, "Y": 0.6322450637817383}, {"X": 0.020070170983672142, "Y": 0.6322454810142517}]}, "Id": "011e0944-b9a3-4549-9cf2-2cf864df5261", "Relationships": [{"Type": "CHILD", "Ids": ["547c4f7b-e8b9-4953-8e95-9a246ddfc67b", "89e1d646-2738-410f-99fb-b6cc10674ae6"]}]}, {"BlockType": "LINE", "Confidence": 99.72450256347656, "Text": "In Time: 15:54", "Geometry": {"BoundingBox": {"Width": 0.16783247888088226, "Height": 0.036445364356040955, "Left": 0.6020058393478394, "Top": 0.5860887765884399}, "Polygon": [{"X": 0.6020058393478394, "Y": 0.5860892534255981}, {"X": 0.7698380351066589, "Y": 0.5860887765884399}, {"X": 0.769838273525238, "Y": 0.6225336790084839}, {"X": 0.6020061373710632, "Y": 0.6225340962409973}]}, "Id": "667ced50-28e1-4481-b08c-83cefa52aa4c", "Relationships": [{"Type": "CHILD", "Ids": ["feafcbca-24e7-47bd-9c97-c3b21b742e0d", "7e6f998d-0b88-475f-8bb9-6fbd922a0e27", "0808ebbe-cbf3-47b6-a4dc-ce9f92f34c0e"]}]}, {"BlockType": "LINE", "Confidence": 99.69593811035156, "Text": "Size: 20 D", "Geometry": {"BoundingBox": {"Width": 0.11609220504760742, "Height": 0.03613060340285301, "Left": 0.019010651856660843, "Top": 0.6719678044319153}, "Polygon": [{"X": 0.019010651856660843, "Y": 0.6719680428504944}, {"X": 0.13510249555110931, "Y": 0.6719678044319153}, {"X": 0.13510285317897797, "Y": 0.7080982327461243}, {"X": 0.01901102624833584, "Y": 0.7080984115600586}]}, "Id": "93de9dbf-4c1c-4492-8a03-6ecfc3c3cef3", "Relationships": [{"Type": "CHILD", "Ids": ["58c76873-5bc3-4f59-9d67-a2f5a2e12261", "56097c1a-d692-4d30-92e8-4cbeeb8740e8", "34dfb9be-5e2d-4a97-8868-89ce3d2ae503"]}]}, {"BlockType": "LINE", "Confidence": 99.58160400390625, "Text": "In Condition: AV", "Geometry": {"BoundingBox": {"Width": 0.19711507856845856, "Height": 0.03670370951294899, "Left": 0.601939857006073, "Top": 0.6714617013931274}, "Polygon": [{"X": 0.601939857006073, "Y": 0.6714621186256409}, {"X": 0.7990546226501465, "Y": 0.6714617013931274}, {"X": 0.7990549206733704, "Y": 0.7081651091575623}, {"X": 0.6019401550292969, "Y": 0.7081654071807861}]}, "Id": "8f222282-d03e-4e21-a653-3baeb46da70b", "Relationships": [{"Type": "CHILD", "Ids": ["64e36592-4b43-4cf7-8371-d397a8d2a1fc", "83f7d41a-79e9-496a-957a-2fa446310d5a", "1ab9a9d9-51b3-462a-8ed5-ad40b744aca8"]}]}, {"BlockType": "LINE", "Confidence": 99.8456802368164, "Text": "Equipment Type: CON", "Geometry": {"BoundingBox": {"Width": 0.26497790217399597, "Height": 0.045673832297325134, "Left": 0.019643746316432953, "Top": 0.75826495885849}, "Polygon": [{"X": 0.019643746316432953, "Y": 0.7582651972770691}, {"X": 0.2846212089061737, "Y": 0.75826495885849}, {"X": 0.28462162613868713, "Y": 0.8039387464523315}, {"X": 0.019644219428300858, "Y": 0.8039388060569763}]}, "Id": "ee0c7549-af1a-4965-afb5-e07596938dbd", "Relationships": [{"Type": "CHILD", "Ids": ["c3df5098-c351-424a-8e8f-12fa941e152a", "7323b853-a24a-4607-b9c0-2a4a1c0d136f", "d50f74dc-7181-4295-b619-3e0cf4806ca1"]}]}, {"BlockType": "LINE", "Confidence": 99.69115447998047, "Text": "In Empty: True", "Geometry": {"BoundingBox": {"Width": 0.1709911972284317, "Height": 0.04526115953922272, "Left": 0.601962685585022, "Top": 0.7590265274047852}, "Polygon": [{"X": 0.601962685585022, "Y": 0.7590267062187195}, {"X": 0.7729535698890686, "Y": 0.7590265274047852}, {"X": 0.7729539275169373, "Y": 0.8042876720428467}, {"X": 0.6019631028175354, "Y": 0.8042877316474915}]}, "Id": "51ca6421-fd37-4be5-80dc-59b1299bf923", "Relationships": [{"Type": "CHILD", "Ids": ["10db6f19-ac48-4165-9396-f0f0439a05a3", "0b289045-6229-435e-bc63-eb4c2ac10435", "08918277-301d-4156-82b1-ca32bde814d9"]}]}, {"BlockType": "LINE", "Confidence": 99.96717834472656, "Text": "EIR Num: 1673474173", "Geometry": {"BoundingBox": {"Width": 0.2582053542137146, "Height": 0.03480743244290352, "Left": 0.6019786596298218, "Top": 0.8450754284858704}, "Polygon": [{"X": 0.6019786596298218, "Y": 0.8450754284858704}, {"X": 0.8601837754249573, "Y": 0.8450754880905151}, {"X": 0.8601840138435364, "Y": 0.8798828721046448}, {"X": 0.6019789576530457, "Y": 0.8798826932907104}]}, "Id": "f3cdc3e6-0b27-421b-93b9-3b74b8029066", "Relationships": [{"Type": "CHILD", "Ids": ["66e4c28b-93ec-4bcb-a583-0716340b98fb", "be565062-e341-48af-86d1-cd512dc41c09", "541e6815-f5bf-4e1f-9783-6397cfebd189"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Type: SINGLE", "Geometry": {"BoundingBox": {"Width": 0.15668626129627228, "Height": 0.04453757405281067, "Left": 0.6006550192832947, "Top": 0.9315704703330994}, "Polygon": [{"X": 0.6006550192832947, "Y": 0.9315704703330994}, {"X": 0.7573409676551819, "Y": 0.9315707087516785}, {"X": 0.7573412656784058, "Y": 0.9761080741882324}, {"X": 0.6006553769111633, "Y": 0.9761077165603638}]}, "Id": "b441944e-e310-42a5-8d18-5fe798b707b7", "Relationships": [{"Type": "CHILD", "Ids": ["7a845d84-da26-4a91-8d31-930caa01edb0", "e058996a-9295-4db8-84f0-2de7571ccbbf"]}]}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "TLLU3608177", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2687617838382721, "Height": 0.058741021901369095, "Left": 0.29509884119033813, "Top": 0.050430528819561005}, "Polygon": [{"X": 0.29509884119033813, "Y": 0.050433188676834106}, {"X": 0.5638601183891296, "Y": 0.050430528819561005}, {"X": 0.5638605952262878, "Y": 0.10916909575462341}, {"X": 0.2950993776321411, "Y": 0.1091715469956398}]}, "Id": "f537651d-959d-4ad5-b6de-4d4e540651c5"}, {"BlockType": "WORD", "Confidence": 98.89448547363281, "Text": "ConGlobal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12065636366605759, "Height": 0.03663988783955574, "Left": 0.3112500309944153, "Top": 0.16684545576572418}, "Polygon": [{"X": 0.3112500309944153, "Y": 0.16684646904468536}, {"X": 0.4319060742855072, "Y": 0.16684545576572418}, {"X": 0.43190640211105347, "Y": 0.20348438620567322}, {"X": 0.31125038862228394, "Y": 0.20348535478115082}]}, "Id": "4b56a2d8-a386-4feb-a744-1539e8481f81"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Industries", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11100180447101593, "Height": 0.036549896001815796, "Left": 0.4396190643310547, "Top": 0.16674184799194336}, "Polygon": [{"X": 0.4396190643310547, "Y": 0.16674278676509857}, {"X": 0.5506205558776855, "Y": 0.16674184799194336}, {"X": 0.5506208539009094, "Y": 0.20329086482524872}, {"X": 0.43961939215660095, "Y": 0.20329174399375916}]}, "Id": "523d3540-76cf-49ba-bffd-615d7bc269f1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "500", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041576676070690155, "Height": 0.03431238606572151, "Left": 0.3111484944820404, "Top": 0.23313544690608978}, "Polygon": [{"X": 0.3111484944820404, "Y": 0.23313575983047485}, {"X": 0.3527248799800873, "Y": 0.23313544690608978}, {"X": 0.35272517800331116, "Y": 0.2674475312232971}, {"X": 0.31114882230758667, "Y": 0.267447829246521}]}, "Id": "356c3232-1eea-4cff-9d13-85aa2bd25ee6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Mayo", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06366068124771118, "Height": 0.0448039211332798, "Left": 0.3603149354457855, "Top": 0.23304957151412964}, "Polygon": [{"X": 0.3603149354457855, "Y": 0.23305004835128784}, {"X": 0.4239751994609833, "Y": 0.23304957151412964}, {"X": 0.4239756166934967, "Y": 0.2778530418872833}, {"X": 0.36031532287597656, "Y": 0.27785348892211914}]}, "Id": "0a425f52-0ebc-4109-8d73-ba4b034bba72"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Shell", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05546620488166809, "Height": 0.03725741058588028, "Left": 0.4305455982685089, "Top": 0.23097676038742065}, "Polygon": [{"X": 0.4305455982685089, "Y": 0.23097717761993408}, {"X": 0.48601147532463074, "Y": 0.23097676038742065}, {"X": 0.4860117733478546, "Y": 0.2682337760925293}, {"X": 0.4305459260940552, "Y": 0.26823416352272034}]}, "Id": "0dc96dee-9128-4e7d-a10c-22a3b5d051c7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05721883103251457, "Height": 0.036731328815221786, "Left": 0.4934385418891907, "Top": 0.23127099871635437}, "Polygon": [{"X": 0.4934385418891907, "Y": 0.231271430850029}, {"X": 0.5506570935249329, "Y": 0.23127099871635437}, {"X": 0.5506573915481567, "Y": 0.268001914024353}, {"X": 0.49343886971473694, "Y": 0.26800233125686646}]}, "Id": "e302b20c-8ed5-49e6-8813-e52e98eb82ff"}, {"BlockType": "WORD", "Confidence": 99.28252410888672, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07885593175888062, "Height": 0.03707883879542351, "Left": 0.30410119891166687, "Top": 0.29572683572769165}, "Polygon": [{"X": 0.30410119891166687, "Y": 0.29572737216949463}, {"X": 0.3829568028450012, "Y": 0.29572683572769165}, {"X": 0.3829571306705475, "Y": 0.33280518651008606}, {"X": 0.3041015565395355, "Y": 0.33280569314956665}]}, "Id": "f210b426-807a-4952-aa0c-d0846f444b2c"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "Park,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053850214928388596, "Height": 0.04110489785671234, "Left": 0.39042237401008606, "Top": 0.2955213189125061}, "Polygon": [{"X": 0.39042237401008606, "Y": 0.29552167654037476}, {"X": 0.4442722499370575, "Y": 0.2955213189125061}, {"X": 0.44427260756492615, "Y": 0.336625874042511}, {"X": 0.3904227614402771, "Y": 0.33662620186805725}]}, "Id": "1375b623-bc1b-4274-8d09-c718518eeef3"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030846506357192993, "Height": 0.03473132848739624, "Left": 0.45049190521240234, "Top": 0.29708436131477356}, "Polygon": [{"X": 0.45049190521240234, "Y": 0.2970845699310303}, {"X": 0.48133811354637146, "Y": 0.29708436131477356}, {"X": 0.48133841156959534, "Y": 0.3318155109882355}, {"X": 0.4504922032356262, "Y": 0.3318156898021698}]}, "Id": "b388866f-f336-442c-a428-271659ae4776"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "77547", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06981024891138077, "Height": 0.034885305911302567, "Left": 0.4877881705760956, "Top": 0.296995609998703}, "Polygon": [{"X": 0.4877881705760956, "Y": 0.2969960570335388}, {"X": 0.5575981140136719, "Y": 0.296995609998703}, {"X": 0.5575984120368958, "Y": 0.33188045024871826}, {"X": 0.48778846859931946, "Y": 0.3318808972835541}]}, "Id": "ee8536e8-8ba4-46fd-b154-616e08e0ba06"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "(713)-675-7587", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17537260055541992, "Height": 0.04340914264321327, "Left": 0.34376299381256104, "Top": 0.3611062467098236}, "Polygon": [{"X": 0.34376299381256104, "Y": 0.3611072897911072}, {"X": 0.5191352367401123, "Y": 0.3611062467098236}, {"X": 0.519135594367981, "Y": 0.4045144319534302}, {"X": 0.3437633812427521, "Y": 0.4045153856277466}]}, "Id": "3b068599-34be-4664-a79a-43fe2773224d"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Customer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12535929679870605, "Height": 0.03468937426805496, "Left": 0.019274793565273285, "Top": 0.5017327666282654}, "Polygon": [{"X": 0.019274793565273285, "Y": 0.5017333030700684}, {"X": 0.14463374018669128, "Y": 0.5017327666282654}, {"X": 0.14463408291339874, "Y": 0.5364217162132263}, {"X": 0.019275154918432236, "Y": 0.5364221334457397}]}, "Id": "c06c3f90-52b7-4e27-83dd-5d232d9cefd9"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "TRITN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06858895719051361, "Height": 0.034979529678821564, "Left": 0.15129192173480988, "Top": 0.5010012984275818}, "Polygon": [{"X": 0.15129192173480988, "Y": 0.5010015368461609}, {"X": 0.21988055109977722, "Y": 0.5010012984275818}, {"X": 0.2198808789253235, "Y": 0.5359805822372437}, {"X": 0.15129226446151733, "Y": 0.5359808206558228}]}, "Id": "5529f45d-7c2c-4d4a-b4ee-f16396df721f"}, {"BlockType": "WORD", "Confidence": 98.97421264648438, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023858914151787758, "Height": 0.03464866429567337, "Left": 0.6019259691238403, "Top": 0.5016747117042542}, "Polygon": [{"X": 0.6019259691238403, "Y": 0.5016748309135437}, {"X": 0.6257845759391785, "Y": 0.5016747117042542}, {"X": 0.6257848739624023, "Y": 0.5363233089447021}, {"X": 0.6019262671470642, "Y": 0.5363233685493469}]}, "Id": "7fb6bd33-23e1-4239-8fe4-0dbaf6de5f96"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06371892988681793, "Height": 0.035006582736968994, "Left": 0.6331599950790405, "Top": 0.5015603303909302}, "Polygon": [{"X": 0.6331599950790405, "Y": 0.5015605688095093}, {"X": 0.6968786120414734, "Y": 0.5015603303909302}, {"X": 0.6968789100646973, "Y": 0.5365666747093201}, {"X": 0.6331602334976196, "Y": 0.5365669131278992}]}, "Id": "9ad7b75a-b84a-4058-9bc7-d778231d486f"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "2023-01-11", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12966948747634888, "Height": 0.03477771580219269, "Left": 0.7039470076560974, "Top": 0.5013899803161621}, "Polygon": [{"X": 0.7039470076560974, "Y": 0.5013905167579651}, {"X": 0.8336162567138672, "Y": 0.5013899803161621}, {"X": 0.8336164951324463, "Y": 0.5361672043800354}, {"X": 0.7039473056793213, "Y": 0.5361676812171936}]}, "Id": "cc39c77b-60f9-4f6d-a8d3-2b468fade824"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Depot:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08188211172819138, "Height": 0.04484044760465622, "Left": 0.020069705322384834, "Top": 0.5874050259590149}, "Polygon": [{"X": 0.020069705322384834, "Y": 0.587405264377594}, {"X": 0.10195136070251465, "Y": 0.5874050259590149}, {"X": 0.10195181518793106, "Y": 0.6322453022003174}, {"X": 0.020070170983672142, "Y": 0.6322454810142517}]}, "Id": "547c4f7b-e8b9-4953-8e95-9a246ddfc67b"}, {"BlockType": "WORD", "Confidence": 99.65182495117188, "Text": "HOU05", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08301360160112381, "Height": 0.034810621291399, "Left": 0.109138622879982, "Top": 0.5873712301254272}, "Polygon": [{"X": 0.109138622879982, "Y": 0.5873714685440063}, {"X": 0.19215188920497894, "Y": 0.5873712301254272}, {"X": 0.1921522319316864, "Y": 0.6221816539764404}, {"X": 0.10913897305727005, "Y": 0.6221818923950195}]}, "Id": "89e1d646-2738-410f-99fb-b6cc10674ae6"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023959921672940254, "Height": 0.03436741605401039, "Left": 0.6020058393478394, "Top": 0.5877364873886108}, "Polygon": [{"X": 0.6020058393478394, "Y": 0.5877365469932556}, {"X": 0.6259654760360718, "Y": 0.5877364873886108}, {"X": 0.6259657740592957, "Y": 0.6221038699150085}, {"X": 0.6020061373710632, "Y": 0.6221039295196533}]}, "Id": "feafcbca-24e7-47bd-9c97-c3b21b742e0d"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06877980381250381, "Height": 0.03644508123397827, "Left": 0.6316844820976257, "Top": 0.5860889554023743}, "Polygon": [{"X": 0.6316844820976257, "Y": 0.5860891938209534}, {"X": 0.7004639506340027, "Y": 0.5860889554023743}, {"X": 0.7004642486572266, "Y": 0.6225338578224182}, {"X": 0.6316847801208496, "Y": 0.6225340366363525}]}, "Id": "7e6f998d-0b88-475f-8bb9-6fbd922a0e27"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "15:54", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06147466227412224, "Height": 0.035339295864105225, "Left": 0.7083636522293091, "Top": 0.5868896245956421}, "Polygon": [{"X": 0.7083636522293091, "Y": 0.5868898034095764}, {"X": 0.7698380351066589, "Y": 0.5868896245956421}, {"X": 0.769838273525238, "Y": 0.622228741645813}, {"X": 0.7083638906478882, "Y": 0.6222289204597473}]}, "Id": "0808ebbe-cbf3-47b6-a4dc-ce9f92f34c0e"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Size:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05561504885554314, "Height": 0.0360557958483696, "Left": 0.019010651856660843, "Top": 0.6719679236412048}, "Polygon": [{"X": 0.019010651856660843, "Y": 0.6719680428504944}, {"X": 0.07462533563375473, "Y": 0.6719679236412048}, {"X": 0.07462570071220398, "Y": 0.7080236077308655}, {"X": 0.01901102624833584, "Y": 0.708023726940155}]}, "Id": "58c76873-5bc3-4f59-9d67-a2f5a2e12261"}, {"BlockType": "WORD", "Confidence": 99.44255828857422, "Text": "20", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028829269111156464, "Height": 0.034777119755744934, "Left": 0.08137298375368118, "Top": 0.6733211874961853}, "Polygon": [{"X": 0.08137298375368118, "Y": 0.6733212471008301}, {"X": 0.11020190268754959, "Y": 0.6733211874961853}, {"X": 0.11020225286483765, "Y": 0.708098292350769}, {"X": 0.08137333393096924, "Y": 0.708098292350769}]}, "Id": "56097c1a-d692-4d30-92e8-4cbeeb8740e8"}, {"BlockType": "WORD", "Confidence": 99.76483154296875, "Text": "D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01727955974638462, "Height": 0.034689921885728836, "Left": 0.1178232952952385, "Top": 0.6733840107917786}, "Polygon": [{"X": 0.1178232952952385, "Y": 0.6733840107917786}, {"X": 0.1351025104522705, "Y": 0.6733840107917786}, {"X": 0.13510285317897797, "Y": 0.7080739140510559}, {"X": 0.11782364547252655, "Y": 0.7080739140510559}]}, "Id": "34dfb9be-5e2d-4a97-8868-89ce3d2ae503"}, {"BlockType": "WORD", "Confidence": 99.23290252685547, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023702347651124, "Height": 0.03472741320729256, "Left": 0.601939857006073, "Top": 0.6734380125999451}, "Polygon": [{"X": 0.601939857006073, "Y": 0.6734380722045898}, {"X": 0.625641942024231, "Y": 0.6734380125999451}, {"X": 0.6256421804428101, "Y": 0.7081654071807861}, {"X": 0.6019401550292969, "Y": 0.7081654071807861}]}, "Id": "64e36592-4b43-4cf7-8371-d397a8d2a1fc"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Condition:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12737320363521576, "Height": 0.036311302334070206, "Left": 0.6326886415481567, "Top": 0.6714617609977722}, "Polygon": [{"X": 0.6326886415481567, "Y": 0.6714620590209961}, {"X": 0.7600615620613098, "Y": 0.6714617609977722}, {"X": 0.7600618004798889, "Y": 0.7077729105949402}, {"X": 0.6326889395713806, "Y": 0.7077730894088745}]}, "Id": "83f7d41a-79e9-496a-957a-2fa446310d5a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "AV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03300359100103378, "Height": 0.03428565338253975, "Left": 0.7660513520240784, "Top": 0.6735186576843262}, "Polygon": [{"X": 0.7660513520240784, "Y": 0.673518717288971}, {"X": 0.7990546822547913, "Y": 0.6735186576843262}, {"X": 0.7990549206733704, "Y": 0.707804262638092}, {"X": 0.7660515904426575, "Y": 0.7078043222427368}]}, "Id": "1ab9a9d9-51b3-462a-8ed5-ad40b744aca8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1333981454372406, "Height": 0.04556860029697418, "Left": 0.019643746316432953, "Top": 0.7582650780677795}, "Polygon": [{"X": 0.019643746316432953, "Y": 0.7582651972770691}, {"X": 0.15304143726825714, "Y": 0.7582650780677795}, {"X": 0.15304188430309296, "Y": 0.8038336634635925}, {"X": 0.019644219428300858, "Y": 0.8038336634635925}]}, "Id": "c3df5098-c351-424a-8e8f-12fa941e152a"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0660298690199852, "Height": 0.044539667665958405, "Left": 0.15841054916381836, "Top": 0.7593991160392761}, "Polygon": [{"X": 0.15841054916381836, "Y": 0.7593991756439209}, {"X": 0.22443999350070953, "Y": 0.7593991160392761}, {"X": 0.22444042563438416, "Y": 0.8039387464523315}, {"X": 0.15841099619865417, "Y": 0.8039388060569763}]}, "Id": "7323b853-a24a-4607-b9c0-2a4a1c0d136f"}, {"BlockType": "WORD", "Confidence": 99.65660858154297, "Text": "CON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05343931168317795, "Height": 0.03451742231845856, "Left": 0.23118223249912262, "Top": 0.7592459917068481}, "Polygon": [{"X": 0.23118223249912262, "Y": 0.7592460513114929}, {"X": 0.2846212089061737, "Y": 0.7592459917068481}, {"X": 0.28462153673171997, "Y": 0.7937633991241455}, {"X": 0.23118256032466888, "Y": 0.7937633991241455}]}, "Id": "d50f74dc-7181-4295-b619-3e0cf4806ca1"}, {"BlockType": "WORD", "Confidence": 99.23290252685547, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023725617676973343, "Height": 0.03395689278841019, "Left": 0.601962685585022, "Top": 0.7597889304161072}, "Polygon": [{"X": 0.601962685585022, "Y": 0.759788990020752}, {"X": 0.6256880760192871, "Y": 0.7597889304161072}, {"X": 0.6256883144378662, "Y": 0.7937458157539368}, {"X": 0.6019629836082458, "Y": 0.7937458157539368}]}, "Id": "10db6f19-ac48-4165-9396-f0f0439a05a3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Empty:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08399675786495209, "Height": 0.045024700462818146, "Left": 0.6335922479629517, "Top": 0.7592629790306091}, "Polygon": [{"X": 0.6335922479629517, "Y": 0.7592630386352539}, {"X": 0.7175886631011963, "Y": 0.7592629790306091}, {"X": 0.7175890207290649, "Y": 0.8042876720428467}, {"X": 0.6335926055908203, "Y": 0.8042876720428467}]}, "Id": "0b289045-6229-435e-bc63-eb4c2ac10435"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "True", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04924315586686134, "Height": 0.035380035638809204, "Left": 0.7237106561660767, "Top": 0.7590265274047852}, "Polygon": [{"X": 0.7237106561660767, "Y": 0.7590265870094299}, {"X": 0.7729535698890686, "Y": 0.7590265274047852}, {"X": 0.7729538083076477, "Y": 0.794406533241272}, {"X": 0.7237109541893005, "Y": 0.7944065928459167}]}, "Id": "08918277-301d-4156-82b1-ca32bde814d9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "EIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040034808218479156, "Height": 0.034136947244405746, "Left": 0.6019786596298218, "Top": 0.8456000685691833}, "Polygon": [{"X": 0.6019786596298218, "Y": 0.8456000685691833}, {"X": 0.6420131921768188, "Y": 0.8456000685691833}, {"X": 0.6420134902000427, "Y": 0.8797370195388794}, {"X": 0.6019789576530457, "Y": 0.8797370195388794}]}, "Id": "66e4c28b-93ec-4bcb-a583-0716340b98fb"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Num:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06636407226324081, "Height": 0.03448822721838951, "Left": 0.648421049118042, "Top": 0.8453945517539978}, "Polygon": [{"X": 0.648421049118042, "Y": 0.8453945517539978}, {"X": 0.7147848010063171, "Y": 0.8453945517539978}, {"X": 0.714785099029541, "Y": 0.8798827528953552}, {"X": 0.6484212875366211, "Y": 0.8798826932907104}]}, "Id": "be565062-e341-48af-86d1-cd512dc41c09"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1673474173", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1374512016773224, "Height": 0.034436408430337906, "Left": 0.7227328419685364, "Top": 0.8450754880905151}, "Polygon": [{"X": 0.7227328419685364, "Y": 0.8450754880905151}, {"X": 0.8601837754249573, "Y": 0.8450754880905151}, {"X": 0.8601840138435364, "Y": 0.8795118927955627}, {"X": 0.7227330803871155, "Y": 0.8795117735862732}]}, "Id": "541e6815-f5bf-4e1f-9783-6397cfebd189"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0664646252989769, "Height": 0.04416213184595108, "Left": 0.6006550192832947, "Top": 0.9319457411766052}, "Polygon": [{"X": 0.6006550192832947, "Y": 0.9319457411766052}, {"X": 0.6671193242073059, "Y": 0.93194580078125}, {"X": 0.6671196222305298, "Y": 0.9761078953742981}, {"X": 0.6006553769111633, "Y": 0.9761077165603638}]}, "Id": "7a845d84-da26-4a91-8d31-930caa01edb0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "SINGLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08340451866388321, "Height": 0.034200090914964676, "Left": 0.673936665058136, "Top": 0.9315705895423889}, "Polygon": [{"X": 0.673936665058136, "Y": 0.9315705895423889}, {"X": 0.7573409676551819, "Y": 0.9315707087516785}, {"X": 0.757341206073761, "Y": 0.9657706618309021}, {"X": 0.6739369630813599, "Y": 0.9657704830169678}]}, "Id": "e058996a-9295-4db8-84f0-2de7571ccbbf"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "a11d930a-aa8e-4b4f-a457-c0b7e86311c1", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "a11d930a-aa8e-4b4f-a457-c0b7e86311c1", "content-type": "application/x-amz-json-1.1", "content-length": "28606", "date": "Thu, 18 Sep 2025 07:52:57 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a4d7e458-906f-4df5-847d-819b1458d7b4", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:52:59 GMT", "content-type": "application/json", "content-length": "1042", "connection": "keep-alive", "x-amzn-requestid": "a4d7e458-906f-4df5-847d-819b1458d7b4"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It shows a header: TLLU3608177. Then \"ConGlobal Industries\", address, phone, Customer: TRITN, In Date, Depot, In Time, Size, In Condition, Equipment Type, In Empty, EIR Num, Type. Looks like an ingate or outgate? It has \"In Date\", \"In Time\", \"In Condition\". That seems like an ingate. The page shows vehicle entering. \"In Empty: True\" maybe indicates empty container? \"Equipment Type: CON\" likely container. So likely Ingate Document. It matches ingate keywords: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In. So doc_type = ingate. Since only one page, we output that."}}}, {"toolUse": {"toolUseId": "tooluse_spXPc78vSi2ZnESCvc0iuA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2186, "outputTokens": 216, "totalTokens": 2402}, "metrics": {"latencyMs": 1259}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/9504847a_25_ingate_9.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nTLLU3608177\nConGlobal Industries\n500 Mayo Shell Road\nGalena Park, TX 77547\n(713)-675-7587\nCustomer: TRITN\nIn Date: 2023-01-11\nDepot: HOU05\nIn Time: 15:54\nSize: 20 D\nIn Condition: AV\nEquipment Type: CON\nIn Empty: True\nEIR Num: 1673474173\nType: SINGLE\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}