2025-09-24 19:09:46,809 - INFO - Logging initialized. Log file: logs/test_classification_20250924_190946.log
2025-09-24 19:09:46,809 - INFO - 📁 Found 11 files to process
2025-09-24 19:09:46,809 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 19:09:46,809 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-24 19:09:46,809 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-24 19:09:46,809 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-24 19:09:46,809 - INFO - ⬆️ [19:09:46] Uploading: 8_log_1.pdf
2025-09-24 19:09:48,671 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_1.pdf -> s3://document-extraction-logistically/temp/21baca2b_8_log_1.pdf
2025-09-24 19:09:48,671 - INFO - 🔍 [19:09:48] Starting classification: 8_log_1.pdf
2025-09-24 19:09:48,672 - INFO - ⬆️ [19:09:48] Uploading: 8_log_10.jpeg
2025-09-24 19:09:48,675 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:48,706 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:48,716 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/21baca2b_8_log_1.pdf
2025-09-24 19:09:48,717 - INFO - Processing PDF from S3...
2025-09-24 19:09:48,717 - INFO - Downloading PDF from S3 to /tmp/tmpvigvsnh6/21baca2b_8_log_1.pdf
2025-09-24 19:09:50,317 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 19:09:50,318 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:50,319 - INFO - Splitting PDF 21baca2b_8_log_1 into 3 pages
2025-09-24 19:09:50,323 - INFO - Split PDF into 3 pages
2025-09-24 19:09:50,324 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:50,324 - INFO - Expected pages: [1, 2, 3]
2025-09-24 19:09:51,294 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_10.jpeg -> s3://document-extraction-logistically/temp/41413f1b_8_log_10.jpeg
2025-09-24 19:09:51,294 - INFO - 🔍 [19:09:51] Starting classification: 8_log_10.jpeg
2025-09-24 19:09:51,295 - INFO - ⬆️ [19:09:51] Uploading: 8_log_11.jpg
2025-09-24 19:09:51,297 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:51,312 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:51,318 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/41413f1b_8_log_10.jpeg
2025-09-24 19:09:51,318 - INFO - Processing image from S3...
2025-09-24 19:09:52,522 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_11.jpg -> s3://document-extraction-logistically/temp/bff659ab_8_log_11.jpg
2025-09-24 19:09:52,523 - INFO - 🔍 [19:09:52] Starting classification: 8_log_11.jpg
2025-09-24 19:09:52,524 - INFO - ⬆️ [19:09:52] Uploading: 8_log_2.jpeg
2025-09-24 19:09:52,525 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:52,545 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:52,550 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bff659ab_8_log_11.jpg
2025-09-24 19:09:52,550 - INFO - Processing image from S3...
2025-09-24 19:09:53,546 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_2.jpeg -> s3://document-extraction-logistically/temp/b800bb45_8_log_2.jpeg
2025-09-24 19:09:53,547 - INFO - 🔍 [19:09:53] Starting classification: 8_log_2.jpeg
2025-09-24 19:09:53,547 - INFO - ⬆️ [19:09:53] Uploading: 8_log_3.jpeg
2025-09-24 19:09:53,548 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:53,567 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:53,571 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b800bb45_8_log_2.jpeg
2025-09-24 19:09:53,572 - INFO - Processing image from S3...
2025-09-24 19:09:54,171 - INFO - Page 1: Extracted 535 characters, 49 lines from 21baca2b_8_log_1_81d660c7_page_001.pdf
2025-09-24 19:09:54,172 - INFO - Successfully processed page 1
2025-09-24 19:09:54,263 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_3.jpeg -> s3://document-extraction-logistically/temp/35bbc50d_8_log_3.jpeg
2025-09-24 19:09:54,263 - INFO - 🔍 [19:09:54] Starting classification: 8_log_3.jpeg
2025-09-24 19:09:54,264 - INFO - ⬆️ [19:09:54] Uploading: 8_log_4.jpeg
2025-09-24 19:09:54,266 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:54,282 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:54,287 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/35bbc50d_8_log_3.jpeg
2025-09-24 19:09:54,287 - INFO - Processing image from S3...
2025-09-24 19:09:54,373 - INFO - Page 3: Extracted 804 characters, 46 lines from 21baca2b_8_log_1_81d660c7_page_003.pdf
2025-09-24 19:09:54,374 - INFO - Successfully processed page 3
2025-09-24 19:09:54,926 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_4.jpeg -> s3://document-extraction-logistically/temp/638ccfad_8_log_4.jpeg
2025-09-24 19:09:54,927 - INFO - 🔍 [19:09:54] Starting classification: 8_log_4.jpeg
2025-09-24 19:09:54,927 - INFO - ⬆️ [19:09:54] Uploading: 8_log_5.png
2025-09-24 19:09:54,929 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:54,947 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:54,951 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/638ccfad_8_log_4.jpeg
2025-09-24 19:09:54,951 - INFO - Processing image from S3...
2025-09-24 19:09:55,196 - INFO - Page 2: Extracted 492 characters, 33 lines from 21baca2b_8_log_1_81d660c7_page_002.pdf
2025-09-24 19:09:55,197 - INFO - Successfully processed page 2
2025-09-24 19:09:55,197 - INFO - Combined 3 pages into final text
2025-09-24 19:09:55,197 - INFO - Text validation for 21baca2b_8_log_1: 1886 characters, 3 pages
2025-09-24 19:09:55,198 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:55,198 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:55,531 - INFO - S3 Image temp/41413f1b_8_log_10.jpeg: Extracted 526 characters, 56 lines
2025-09-24 19:09:55,532 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:55,532 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:55,903 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_5.png -> s3://document-extraction-logistically/temp/2c7b45cd_8_log_5.png
2025-09-24 19:09:55,903 - INFO - 🔍 [19:09:55] Starting classification: 8_log_5.png
2025-09-24 19:09:55,904 - INFO - ⬆️ [19:09:55] Uploading: 8_log_6.jpeg
2025-09-24 19:09:55,906 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:55,956 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:55,960 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2c7b45cd_8_log_5.png
2025-09-24 19:09:55,961 - INFO - Processing image from S3...
2025-09-24 19:09:56,994 - INFO - S3 Image temp/bff659ab_8_log_11.jpg: Extracted 1060 characters, 100 lines
2025-09-24 19:09:56,995 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:56,995 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:57,094 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_6.jpeg -> s3://document-extraction-logistically/temp/84fe3b46_8_log_6.jpeg
2025-09-24 19:09:57,094 - INFO - 🔍 [19:09:57] Starting classification: 8_log_6.jpeg
2025-09-24 19:09:57,095 - INFO - ⬆️ [19:09:57] Uploading: 8_log_7.pdf
2025-09-24 19:09:57,096 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:57,119 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:57,123 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/84fe3b46_8_log_6.jpeg
2025-09-24 19:09:57,124 - INFO - Processing image from S3...
2025-09-24 19:09:57,282 - INFO - S3 Image temp/35bbc50d_8_log_3.jpeg: Extracted 261 characters, 24 lines
2025-09-24 19:09:57,282 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:57,282 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:57,742 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_7.pdf -> s3://document-extraction-logistically/temp/9a8c506c_8_log_7.pdf
2025-09-24 19:09:57,748 - INFO - 🔍 [19:09:57] Starting classification: 8_log_7.pdf
2025-09-24 19:09:57,750 - INFO - S3 Image temp/b800bb45_8_log_2.jpeg: Extracted 485 characters, 49 lines
2025-09-24 19:09:57,750 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:57,750 - INFO - ⬆️ [19:09:57] Uploading: 8_log_8.jpeg
2025-09-24 19:09:57,751 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:57,751 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:57,764 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:57,766 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9a8c506c_8_log_7.pdf
2025-09-24 19:09:57,766 - INFO - Processing PDF from S3...
2025-09-24 19:09:57,766 - INFO - Downloading PDF from S3 to /tmp/tmpuuaz70q1/9a8c506c_8_log_7.pdf
2025-09-24 19:09:57,976 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '75a860e0-840f-43cb-a997-a53f8b122044', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:57 GMT', 'content-type': 'application/json', 'content-length': '1493', 'connection': 'keep-alive', 'x-amzn-requestid': '75a860e0-840f-43cb-a997-a53f8b122044'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have pages 1-3. They seem to be pickup detail screens from a freight system, likely internal system "Dayton Freight Lines" showing pickup details. This is not a standard logistics document like invoice, BOL, POD, etc. This looks like an internal log or record but not a typical doc type. Could be "log" maybe? It has driver info, pickup number, etc. It\'s an activity record. According to doc types, "log" includes driver log, activity records. This seems like a log of a pickup: "Pickup Number", "Shipper", "Consignee", "Driver", "Pickup date", etc. So likely "log".\n\nWe have to output for each page, doc_type "log". But check if any of the pages are continuation of previous: page 1 maybe partial. But no "continued" marker. So each page is separate but part of same document type. The same type repeated.\n\nThus output:\n\n{\n  "documents": [\n    {"page_no": 1, "doc_type": "log"},\n    {"page_no": 2, "doc_type": "log"},\n    {"page_no": 3, "doc_type": "log"}\n  ]\n}\n\nWe need to use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3389, 'outputTokens': 305, 'totalTokens': 3694}, 'metrics': {'latencyMs': 1491}}
2025-09-24 19:09:57,977 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/21baca2b_8_log_1.pdf
2025-09-24 19:09:58,262 - INFO - S3 Image temp/638ccfad_8_log_4.jpeg: Extracted 469 characters, 51 lines
2025-09-24 19:09:58,263 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:09:58,263 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:09:58,353 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bd251418-1060-4d88-a321-5e9a9088a6a5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:58 GMT', 'content-type': 'application/json', 'content-length': '1489', 'connection': 'keep-alive', 'x-amzn-requestid': 'bd251418-1060-4d88-a321-5e9a9088a6a5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to examine keywords. This looks like an Ingate log: "Truck Ready Time", "Truck Departure", "Dock Time", "PICK-UP", "PICKUP". It seems like a gate log or an Ingate document. Contains times and pickup details. Could be ingate or outgate. It\'s an inbound truck record. The page shows pick-up times, customer names, etc. It\'s a log of truck arrival times, "Truck Ready Time", "Truck Departure: 7:00". There is "PICKUP" at end. The page has many vehicles. Likely an ingate document: "Equipment In-Gated" etc. The keywords: "Truck Ready Time", "Truck Departure", "Dock Time", "PICKUP". That\'s typical ingate. So classify as ingate.\n\nBut must consider "log" also? Log is for driver log, activity record. The page is an activity record log of truck arrival times. It\'s a log. But we have a dedicated doc_type "ingate". Log could also be used for driver logs. But ingate is specific. The presence of "Truck Ready Time", "Truck Departure" indicates arrival. So ingate.\n\nThus output JSON with page_no 1, doc_type "ingate".\n\nWe need to output via tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"ingate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2844, 'outputTokens': 301, 'totalTokens': 3145}, 'metrics': {'latencyMs': 1485}}
2025-09-24 19:09:58,354 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/41413f1b_8_log_10.jpeg
2025-09-24 19:09:58,870 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_8.jpeg -> s3://document-extraction-logistically/temp/e188e460_8_log_8.jpeg
2025-09-24 19:09:58,870 - INFO - 🔍 [19:09:58] Starting classification: 8_log_8.jpeg
2025-09-24 19:09:58,871 - INFO - ⬆️ [19:09:58] Uploading: 8_log_9.jpeg
2025-09-24 19:09:58,872 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:58,892 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:58,898 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e188e460_8_log_8.jpeg
2025-09-24 19:09:58,898 - INFO - Processing image from S3...
2025-09-24 19:09:59,234 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e3271be0-f4d6-4411-bc33-1a95beef1b53', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:59 GMT', 'content-type': 'application/json', 'content-length': '999', 'connection': 'keep-alive', 'x-amzn-requestid': 'e3271be0-f4d6-4411-bc33-1a95beef1b53'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify doc_type. The content looks like a Delivery Report, maybe a log or POD? It has driver, miles, delivery/pickup addresses, "Weights", "Delivered Picked up". It seems like a driver log or a POD. Does it contain "Proof of Delivery"? Not explicitly. Many addresses, start and end miles, driver name, date/time. Could be a "log" document: driver logbook. Keywords: driver log, hours, driver, route. So likely "log". It could also be a "pod" but POD usually has signature, delivered by, etc. This page has no signature block. It is more like a log sheet. So doc_type: "log".\n\nWe must return a JSON with documents array with page_no 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3021, 'outputTokens': 183, 'totalTokens': 3204}, 'metrics': {'latencyMs': 1022}}
2025-09-24 19:09:59,234 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bff659ab_8_log_11.jpg
2025-09-24 19:09:59,861 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 19:09:59,862 - INFO - Splitting PDF into individual pages...
2025-09-24 19:09:59,863 - INFO - Splitting PDF 9a8c506c_8_log_7 into 1 pages
2025-09-24 19:09:59,870 - INFO - Split PDF into 1 pages
2025-09-24 19:09:59,870 - INFO - Processing pages with Textract in parallel...
2025-09-24 19:09:59,871 - INFO - Expected pages: [1]
2025-09-24 19:09:59,910 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_9.jpeg -> s3://document-extraction-logistically/temp/d8a84095_8_log_9.jpeg
2025-09-24 19:09:59,911 - INFO - 🔍 [19:09:59] Starting classification: 8_log_9.jpeg
2025-09-24 19:09:59,913 - INFO - Initializing TextractProcessor...
2025-09-24 19:09:59,928 - INFO - Initializing BedrockProcessor...
2025-09-24 19:09:59,933 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d8a84095_8_log_9.jpeg
2025-09-24 19:09:59,933 - INFO - Processing image from S3...
2025-09-24 19:09:59,956 - INFO - 

8_log_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        },
        {
            "page_no": 2,
            "doc_type": "log"
        },
        {
            "page_no": 3,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:09:59,956 - INFO - 

✓ Saved result: output/run1_8_log_1.json
2025-09-24 19:10:00,038 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5a2a6f5f-4a21-43fa-ba01-26949592c914', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:39:59 GMT', 'content-type': 'application/json', 'content-length': '1553', 'connection': 'keep-alive', 'x-amzn-requestid': '5a2a6f5f-4a21-43fa-ba01-26949592c914'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The content looks like an Ingate or Outgate? It shows "Truck Ready Time", "Truck Departure", "Driver", "Dock Time", "Work Order #", "Customer". This likely is a gate log: Ingate or Outgate? It\'s "Truck Ready Time" and "Truck Departure" maybe the departure time at gate. This might be a Ingate or Outgate? Let\'s parse: It shows "Unit #", "Truck Ready Time", "Driver", "Truck Departure", "Departure Time", "Dock Time", "Work Order #". It might be an "ingate" document because it records arrival. But it also has departure times. The heading "Truck Ready Time" and "Truck Departure" suggests it\'s a departure log (Outgate). Usually, an Outgate record would have "Truck departure" and "Departure time" and "Dock time" maybe the times when truck left the dock. Ingate would have arrival times. This looks more like departure/outgate. So doc_type likely "outgate". The instructions: "Record of vehicle/container exiting a facility (gate-out/release)." This matches. So classify as "outgate". Ensure output format.\n\nReturn:\n\n{\n "documents": [\n  {"page_no": 1, "doc_type": "outgate"}\n ]\n}\n\nWe must not add extraneous.\n\nLet\'s output.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"outgate"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2705, 'outputTokens': 303, 'totalTokens': 3008}, 'metrics': {'latencyMs': 1518}}
2025-09-24 19:10:00,038 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/35bbc50d_8_log_3.jpeg
2025-09-24 19:10:00,262 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/21baca2b_8_log_1.pdf
2025-09-24 19:10:00,279 - INFO - 

8_log_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        }
    ]
}
2025-09-24 19:10:00,279 - INFO - 

✓ Saved result: output/run1_8_log_10.json
2025-09-24 19:10:00,397 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c20445f6-b48f-4640-b391-f7b33682015a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:40:00 GMT', 'content-type': 'application/json', 'content-length': '1597', 'connection': 'keep-alive', 'x-amzn-requestid': 'c20445f6-b48f-4640-b391-f7b33682015a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The text seems like some kind of gate entry or work order. There\'s "WORK ORDER #", "SO-", "PARKER HANNIFIN", "Truck Ready Time", "Truck Departure". This looks like an ingate or outgate? Let\'s analyze.\n\nWords: "Inland plt", "Truck Ready Time". Could be a gate log? There\'s no "Ingate" header explicitly. But "Truck Ready Time", "Dock Time", etc. It might be an ingate document (vehicle arrival). But we don\'t see "Ingate" specifically. There\'s "Truck Ready Time: 5:00", "Truck Departure: 5:00". Could be a gate log or a log document. The presence of "Work Order #" suggests maybe a gate log. The categories: ingate, outgate, log. "Ingate" definition: Record of vehicle/container entering a facility. It includes truck number, time in, etc. This page has "Truck Ready Time", "Truck Departure". Might be both entry and exit? Might be a log page. The "log" category includes driver log, activity record, etc. So likely it\'s a log.\n\nCheck for keywords: "Ingate" is not present. "Outgate" not present. But "Log" has keywords: "driver log", "logbook", "activity". There\'s "Dock Time". That seems like a log.\n\nThus classify as "log". We need to output JSON with page_no 1 and doc_type "log".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2813, 'outputTokens': 333, 'totalTokens': 3146}, 'metrics': {'latencyMs': 1466}}
2025-09-24 19:10:00,398 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b800bb45_8_log_2.jpeg
2025-09-24 19:10:00,405 - INFO - S3 Image temp/2c7b45cd_8_log_5.png: Extracted 328 characters, 39 lines
2025-09-24 19:10:00,405 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:10:00,405 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:10:00,611 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/41413f1b_8_log_10.jpeg
2025-09-24 19:10:00,635 - INFO - 

8_log_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:10:00,635 - INFO - 

✓ Saved result: output/run1_8_log_11.json
2025-09-24 19:10:00,677 - ERROR - Processing failed for s3://document-extraction-logistically/temp/638ccfad_8_log_4.jpeg: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 19:10:00,678 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/638ccfad_8_log_4.jpeg: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 19:10:00,680 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}

2025-09-24 19:10:00,681 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 19:10:00,931 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bff659ab_8_log_11.jpg
2025-09-24 19:10:00,943 - INFO - 

8_log_3.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-24 19:10:00,943 - INFO - 

✓ Saved result: output/run1_8_log_3.json
2025-09-24 19:10:01,029 - INFO - S3 Image temp/84fe3b46_8_log_6.jpeg: Extracted 548 characters, 53 lines
2025-09-24 19:10:01,029 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:10:01,029 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:10:01,241 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/35bbc50d_8_log_3.jpeg
2025-09-24 19:10:01,258 - INFO - 

8_log_2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:10:01,258 - INFO - 

✓ Saved result: output/run1_8_log_2.json
2025-09-24 19:10:01,561 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b800bb45_8_log_2.jpeg
2025-09-24 19:10:01,562 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_4.jpeg: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 19:10:01,566 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}

2025-09-24 19:10:01,567 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 19:10:01,867 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/638ccfad_8_log_4.jpeg
2025-09-24 19:10:02,903 - INFO - S3 Image temp/e188e460_8_log_8.jpeg: Extracted 416 characters, 42 lines
2025-09-24 19:10:02,904 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:10:02,904 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:10:03,275 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0bd53531-dd5b-4051-af48-0d9661a96f5e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:40:03 GMT', 'content-type': 'application/json', 'content-length': '890', 'connection': 'keep-alive', 'x-amzn-requestid': '0bd53531-dd5b-4051-af48-0d9661a96f5e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page only. Need to classify. The content seems like a Work Order or loading list, not a recognized document type. It lists work order numbers, SKUs, times, etc. This could be a "work order" but not in list. Could be "pack_list"? But pack list contains "packing list" header. Not present. Could be "po"? No. Could be "ingate" or "outgate"? No gate info. Could be "tender_from_cust"? No tender. Could be "combined_carrier_documents"? No multiple headers. Could be "other". So classify as other. But check for continuation: no. So page 1 other.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2842, 'outputTokens': 159, 'totalTokens': 3001}, 'metrics': {'latencyMs': 913}}
2025-09-24 19:10:03,275 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/84fe3b46_8_log_6.jpeg
2025-09-24 19:10:03,294 - INFO - 

8_log_6.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 19:10:03,294 - INFO - 

✓ Saved result: output/run1_8_log_6.json
2025-09-24 19:10:03,582 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '79f88f4c-0cae-4d10-9150-c26334978a88', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:40:03 GMT', 'content-type': 'application/json', 'content-length': '1891', 'connection': 'keep-alive', 'x-amzn-requestid': '79f88f4c-0cae-4d10-9150-c26334978a88'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content seems like a truck ready sheet with work order numbers, driver, dock time. Not obviously a recognized doc type like invoice, bol, pod, etc. Let\'s inspect: There\'s "***********" etc. Work order numbers. Not "invoice". Could be a "log" (driver log). But it\'s more like a loading sheet. Might be "ingate" or "outgate"? It has dock time, truck ready time, departure time. This looks like an "Ingate" document? But ingate has arrival activity, truck in. This appears to be a dispatch sheet. None of the categories exactly match. Might be "other".\n\nCheck for "log" keywords: "Dock Time" "Truck Departure" "Driver". Could be "log". But the log doc type includes "Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries". This might fit. It\'s a log of driver activity times. But no explicit "Logbook" header. It\'s more a sheet for driver. I\'d classify as "log".\n\nBut also check "ingate" - includes "Truck In" and "Entry Time". But this page has "Truck Ready Time", "Truck Departure", "Departure Time". That is more like a departure sheet, maybe a "log" or "outgate". "Outgate" includes "Gate Out, Release, Exit Time". It has "Departure Time". Could be "outgate". But typically outgate would have exit time, release stamp. Not present.\n\nThus "log" seems appropriate.\n\nWe should output JSON with documents array containing one object: page_no 1, doc_type "log".\n\nLet\'s produce accordingly.'}}}, {'text': '{\n  "documents": [\n    {\n      "page_no": 1,\n      "doc_type": "log"\n    }\n  ]\n}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2738, 'outputTokens': 389, 'totalTokens': 3127}, 'metrics': {'latencyMs': 1847}}
2025-09-24 19:10:03,583 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2c7b45cd_8_log_5.png
2025-09-24 19:10:03,683 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/84fe3b46_8_log_6.jpeg
2025-09-24 19:10:03,697 - INFO - 

8_log_5.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:10:03,698 - INFO - 

✓ Saved result: output/run1_8_log_5.json
2025-09-24 19:10:04,007 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2c7b45cd_8_log_5.png
2025-09-24 19:10:04,776 - INFO - S3 Image temp/d8a84095_8_log_9.jpeg: Extracted 448 characters, 40 lines
2025-09-24 19:10:04,776 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:10:04,776 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:10:04,913 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '21804056-bf91-4183-8276-9f41b4b07686', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:40:04 GMT', 'content-type': 'application/json', 'content-length': '640', 'connection': 'keep-alive', 'x-amzn-requestid': '21804056-bf91-4183-8276-9f41b4b07686'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content looks like a log of dock times, customer, work orders. It\'s likely a log file. The keywords: "Dock Time", "CUSTOMER", "WORK ORDER #". This seems like a driver log or activity log. So doc_type = "log". Check if any other type? It\'s not invoice, BOL, POD, etc. So log. Output for page 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2784, 'outputTokens': 103, 'totalTokens': 2887}, 'metrics': {'latencyMs': 657}}
2025-09-24 19:10:04,913 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e188e460_8_log_8.jpeg
2025-09-24 19:10:04,928 - INFO - 

8_log_8.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:10:04,929 - INFO - 

✓ Saved result: output/run1_8_log_8.json
2025-09-24 19:10:05,285 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e188e460_8_log_8.jpeg
2025-09-24 19:10:07,232 - INFO - Page 1: Extracted 519 characters, 34 lines from 9a8c506c_8_log_7_2ad6ae76_page_001.pdf
2025-09-24 19:10:07,232 - INFO - Successfully processed page 1
2025-09-24 19:10:07,232 - INFO - Combined 1 pages into final text
2025-09-24 19:10:07,232 - INFO - Text validation for 9a8c506c_8_log_7: 536 characters, 1 pages
2025-09-24 19:10:07,232 - INFO - Analyzing document types with Bedrock...
2025-09-24 19:10:07,232 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 19:10:07,460 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ae5971f8-4acc-4ecc-85b8-386ac655d4a5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:40:07 GMT', 'content-type': 'application/json', 'content-length': '1405', 'connection': 'keep-alive', 'x-amzn-requestid': 'ae5971f8-4acc-4ecc-85b8-386ac655d4a5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have only page 1 content. Determine document type. The text seems like a work order list with work order numbers, times, truck ready times, driver, etc. Looks like a "Work Order" or "Truck Manifest" maybe. Not clearly any of the defined types. Let\'s see keywords: "WORK ORDER #" appears. "SO-" maybe Sales Order. But likely it\'s a work order sheet for a truck. Not invoice, bol, pod, rate_confirmation, etc. Could be a "log" type? It could be a log of truck activity: driver log, activity record. The doc has times, truck ready time, departure time, dock time, etc. So it might be "log" – driver log or activity record.\n\nDefinition of log: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log or 3. Driver detention certificate. This page has times and driver. So "log" seems plausible.\n\nCheck for "log" keywords: "Truck Ready Time", "Driver", "Dock Time", "Departure Time" – yes. So classify as "log".\n\nThus output: page 1 -> doc_type "log".\n\nWe need to output JSON with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2804, 'outputTokens': 273, 'totalTokens': 3077}, 'metrics': {'latencyMs': 1354}}
2025-09-24 19:10:07,461 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d8a84095_8_log_9.jpeg
2025-09-24 19:10:07,477 - INFO - 

8_log_9.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:10:07,477 - INFO - 

✓ Saved result: output/run1_8_log_9.json
2025-09-24 19:10:07,765 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d8a84095_8_log_9.jpeg
2025-09-24 19:10:09,724 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b9fb46dd-f1ed-4c6c-b3b2-0e24834b6edc', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 13:40:09 GMT', 'content-type': 'application/json', 'content-length': '1380', 'connection': 'keep-alive', 'x-amzn-requestid': 'b9fb46dd-f1ed-4c6c-b3b2-0e24834b6edc'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Need to determine doc_type. The content: "W&I Cert #1 ; RECVD: 5/25/2025 8:06:20 AM CENTRAL TRANSPORT Driver Detention Certificate Customer Name ILLING PACKAGING % ENVOY LOGISTICS Customer Number 54903000114 Detention Company ILLING COMPANY Address ... Trailer 2400970 Date of Detention 05/20/2025 Free Time 30 Pro Number Weight Time In Time Out Detention Time ********* 2229 13:41 14:34 53 Total Billable Time 23 Total Weight 2229 Driver Certification: Time required for live pickup exceeds free time allowed per stop."\n\nThis is clearly a Driver Detention Certificate, which is a type of log. The doc_type "log" is defined as activity record (driver log, tracking log) with dock time for operational/regulatory use or Temperature log etc. This is a detention certificate, which is a driver log. So doc_type = log.\n\nCheck if any other doc_type fits: "driver detention certificate" is specifically a type of log. So assign "log". Also check if maybe "log" is correct. Yes.\n\nThus output one object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2761, 'outputTokens': 278, 'totalTokens': 3039}, 'metrics': {'latencyMs': 1262}}
2025-09-24 19:10:09,724 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9a8c506c_8_log_7.pdf
2025-09-24 19:10:09,740 - INFO - 

8_log_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 19:10:09,741 - INFO - 

✓ Saved result: output/run1_8_log_7.json
2025-09-24 19:10:10,061 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9a8c506c_8_log_7.pdf
2025-09-24 19:10:10,062 - INFO - 
📊 Processing Summary:
2025-09-24 19:10:10,062 - INFO -    Total files: 11
2025-09-24 19:10:10,062 - INFO -    Successful: 10
2025-09-24 19:10:10,062 - INFO -    Failed: 1
2025-09-24 19:10:10,062 - INFO -    Duration: 23.25 seconds
2025-09-24 19:10:10,063 - INFO -    Output directory: output
2025-09-24 19:10:10,063 - INFO - 
📋 Successfully Processed Files:
2025-09-24 19:10:10,063 - INFO -    📄 8_log_1.pdf: {"documents":[{"page_no":1,"doc_type":"log"},{"page_no":2,"doc_type":"log"},{"page_no":3,"doc_type":"log"}]}
2025-09-24 19:10:10,063 - INFO -    📄 8_log_10.jpeg: {"documents":[{"page_no":1,"doc_type":"ingate"}]}
2025-09-24 19:10:10,063 - INFO -    📄 8_log_11.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 19:10:10,063 - INFO -    📄 8_log_2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 19:10:10,063 - INFO -    📄 8_log_3.jpeg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-24 19:10:10,064 - INFO -    📄 8_log_5.png: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 19:10:10,064 - INFO -    📄 8_log_6.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 19:10:10,064 - INFO -    📄 8_log_7.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 19:10:10,064 - INFO -    📄 8_log_8.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 19:10:10,064 - INFO -    📄 8_log_9.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 19:10:10,064 - ERROR - 
❌ Errors:
2025-09-24 19:10:10,064 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_4.jpeg: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 19:10:10,065 - INFO - 
============================================================================================================================================
2025-09-24 19:10:10,065 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 19:10:10,065 - INFO - ============================================================================================================================================
2025-09-24 19:10:10,065 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 19:10:10,065 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:10:10,065 - INFO - 8_log_1.pdf                                        1      log                                      run1_8_log_1.json                                                               
2025-09-24 19:10:10,066 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_1.pdf
2025-09-24 19:10:10,066 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_1.json
2025-09-24 19:10:10,066 - INFO - 
2025-09-24 19:10:10,066 - INFO - 8_log_1.pdf                                        2      log                                      run1_8_log_1.json                                                               
2025-09-24 19:10:10,066 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_1.pdf
2025-09-24 19:10:10,066 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_1.json
2025-09-24 19:10:10,066 - INFO - 
2025-09-24 19:10:10,066 - INFO - 8_log_1.pdf                                        3      log                                      run1_8_log_1.json                                                               
2025-09-24 19:10:10,066 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_1.pdf
2025-09-24 19:10:10,066 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_1.json
2025-09-24 19:10:10,066 - INFO - 
2025-09-24 19:10:10,066 - INFO - 8_log_10.jpeg                                      1      ingate                                   run1_8_log_10.json                                                              
2025-09-24 19:10:10,066 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_10.jpeg
2025-09-24 19:10:10,066 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_10.json
2025-09-24 19:10:10,066 - INFO - 
2025-09-24 19:10:10,066 - INFO - 8_log_11.jpg                                       1      log                                      run1_8_log_11.json                                                              
2025-09-24 19:10:10,067 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_11.jpg
2025-09-24 19:10:10,067 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_11.json
2025-09-24 19:10:10,067 - INFO - 
2025-09-24 19:10:10,067 - INFO - 8_log_2.jpeg                                       1      log                                      run1_8_log_2.json                                                               
2025-09-24 19:10:10,067 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_2.jpeg
2025-09-24 19:10:10,067 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_2.json
2025-09-24 19:10:10,067 - INFO - 
2025-09-24 19:10:10,067 - INFO - 8_log_3.jpeg                                       1      outgate                                  run1_8_log_3.json                                                               
2025-09-24 19:10:10,067 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_3.jpeg
2025-09-24 19:10:10,067 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_3.json
2025-09-24 19:10:10,067 - INFO - 
2025-09-24 19:10:10,067 - INFO - 8_log_5.png                                        1      log                                      run1_8_log_5.json                                                               
2025-09-24 19:10:10,067 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_5.png
2025-09-24 19:10:10,067 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_5.json
2025-09-24 19:10:10,067 - INFO - 
2025-09-24 19:10:10,067 - INFO - 8_log_6.jpeg                                       1      other                                    run1_8_log_6.json                                                               
2025-09-24 19:10:10,067 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_6.jpeg
2025-09-24 19:10:10,067 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_6.json
2025-09-24 19:10:10,067 - INFO - 
2025-09-24 19:10:10,067 - INFO - 8_log_7.pdf                                        1      log                                      run1_8_log_7.json                                                               
2025-09-24 19:10:10,067 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_7.pdf
2025-09-24 19:10:10,068 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_7.json
2025-09-24 19:10:10,068 - INFO - 
2025-09-24 19:10:10,068 - INFO - 8_log_8.jpeg                                       1      log                                      run1_8_log_8.json                                                               
2025-09-24 19:10:10,068 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_8.jpeg
2025-09-24 19:10:10,068 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_8.json
2025-09-24 19:10:10,068 - INFO - 
2025-09-24 19:10:10,068 - INFO - 8_log_9.jpeg                                       1      log                                      run1_8_log_9.json                                                               
2025-09-24 19:10:10,068 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_9.jpeg
2025-09-24 19:10:10,068 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_8_log_9.json
2025-09-24 19:10:10,068 - INFO - 
2025-09-24 19:10:10,068 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 19:10:10,068 - INFO - Total entries: 12
2025-09-24 19:10:10,068 - INFO - ============================================================================================================================================
2025-09-24 19:10:10,068 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 19:10:10,068 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:10:10,068 - INFO -   1. 8_log_1.pdf                         Page 1   → log             | run1_8_log_1.json
2025-09-24 19:10:10,068 - INFO -   2. 8_log_1.pdf                         Page 2   → log             | run1_8_log_1.json
2025-09-24 19:10:10,068 - INFO -   3. 8_log_1.pdf                         Page 3   → log             | run1_8_log_1.json
2025-09-24 19:10:10,068 - INFO -   4. 8_log_10.jpeg                       Page 1   → ingate          | run1_8_log_10.json
2025-09-24 19:10:10,068 - INFO -   5. 8_log_11.jpg                        Page 1   → log             | run1_8_log_11.json
2025-09-24 19:10:10,069 - INFO -   6. 8_log_2.jpeg                        Page 1   → log             | run1_8_log_2.json
2025-09-24 19:10:10,069 - INFO -   7. 8_log_3.jpeg                        Page 1   → outgate         | run1_8_log_3.json
2025-09-24 19:10:10,069 - INFO -   8. 8_log_5.png                         Page 1   → log             | run1_8_log_5.json
2025-09-24 19:10:10,069 - INFO -   9. 8_log_6.jpeg                        Page 1   → other           | run1_8_log_6.json
2025-09-24 19:10:10,069 - INFO -  10. 8_log_7.pdf                         Page 1   → log             | run1_8_log_7.json
2025-09-24 19:10:10,069 - INFO -  11. 8_log_8.jpeg                        Page 1   → log             | run1_8_log_8.json
2025-09-24 19:10:10,069 - INFO -  12. 8_log_9.jpeg                        Page 1   → log             | run1_8_log_9.json
2025-09-24 19:10:10,069 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 19:10:10,069 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 19:10:10,069 - ERROR - 🐛 Debug info: {'exception': ValidationException('An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \\"BadRequestError\\", code: Some(400), message: \\"unexpected tokens remaining in message header: [\\\\\\"<|constrain|>calls\\\\\\"]\\", param: None } }","type":"invalid_request_error"}}'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock\n    response = bedrock_processor.call_bedrock_converse(\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse\n    return self.bedrock_client.converse(**call_params)\n  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call\n    return self._make_api_call(operation_name, kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper\n    return func(*args, **kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call\n    raise error_class(parsed_response, operation_name)\nbotocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \\"BadRequestError\\", code: Some(400), message: \\"unexpected tokens remaining in message header: [\\\\\\"<|constrain|>calls\\\\\\"]\\", param: None } }","type":"invalid_request_error"}}\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/input_data_individual/8_log/8_log_4.jpeg', 's3_uri': 's3://document-extraction-logistically/temp/638ccfad_8_log_4.jpeg', 'location': 'process_single_file'}
2025-09-24 19:10:10,070 - ERROR - 🐛 Location: process_single_file
2025-09-24 19:10:10,070 - ERROR - 🐛 Exception: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 19:10:10,070 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>calls\\\"]\", param: None } }","type":"invalid_request_error"}}

