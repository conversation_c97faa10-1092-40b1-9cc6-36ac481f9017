2025-09-24 18:18:01,495 - INFO - Logging initialized. Log file: logs/test_classification_20250924_181801.log
2025-09-24 18:18:01,496 - INFO - 📁 Found 18 files to process
2025-09-24 18:18:01,496 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 18:18:01,496 - INFO - 🚀 Processing 18 files in FORCED PARALLEL MODE...
2025-09-24 18:18:01,496 - INFO - 🚀 Creating 18 parallel tasks...
2025-09-24 18:18:01,496 - INFO - 🚀 All 18 tasks created - executing in parallel...
2025-09-24 18:18:01,496 - INFO - ⬆️ [18:18:01] Uploading: CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:03,995 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf -> s3://document-extraction-logistically/temp/80e80179_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:03,995 - INFO - 🔍 [18:18:03] Starting classification: CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:03,996 - INFO - ⬆️ [18:18:03] Uploading: CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:03,997 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:04,019 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:04,025 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/80e80179_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:04,025 - INFO - Processing PDF from S3...
2025-09-24 18:18:04,025 - INFO - Downloading PDF from S3 to /tmp/tmphfgwasqv/80e80179_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:04,598 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CMGYRKEPEEGIJHOFEAQ9.pdf -> s3://document-extraction-logistically/temp/9aa514bc_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:04,599 - INFO - 🔍 [18:18:04] Starting classification: CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:04,600 - INFO - ⬆️ [18:18:04] Uploading: DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:04,602 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:04,616 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:04,619 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9aa514bc_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:04,619 - INFO - Processing PDF from S3...
2025-09-24 18:18:04,620 - INFO - Downloading PDF from S3 to /tmp/tmphkwh1yq6/9aa514bc_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:05,820 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:18:05,820 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:05,822 - INFO - Splitting PDF 9aa514bc_CMGYRKEPEEGIJHOFEAQ9 into 1 pages
2025-09-24 18:18:05,829 - INFO - Split PDF into 1 pages
2025-09-24 18:18:05,829 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:05,829 - INFO - Expected pages: [1]
2025-09-24 18:18:05,873 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:18:05,873 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:05,873 - INFO - Splitting PDF 80e80179_CHU18SQU5Q2XCX57ABTP into 2 pages
2025-09-24 18:18:05,874 - INFO - Split PDF into 2 pages
2025-09-24 18:18:05,875 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:05,875 - INFO - Expected pages: [1, 2]
2025-09-24 18:18:06,362 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DKI9846PWRKYUQA0DQ4A.pdf -> s3://document-extraction-logistically/temp/45c28bc9_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:06,362 - INFO - 🔍 [18:18:06] Starting classification: DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:06,363 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:06,364 - INFO - ⬆️ [18:18:06] Uploading: DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:06,375 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:06,382 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/45c28bc9_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:06,383 - INFO - Processing PDF from S3...
2025-09-24 18:18:06,383 - INFO - Downloading PDF from S3 to /tmp/tmpru9fsdah/45c28bc9_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:07,092 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DTOGYRLS96ZKXQOO6EY5.pdf -> s3://document-extraction-logistically/temp/e0281466_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:07,092 - INFO - 🔍 [18:18:07] Starting classification: DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:07,093 - INFO - ⬆️ [18:18:07] Uploading: EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:07,095 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:07,113 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:07,117 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e0281466_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:07,117 - INFO - Processing PDF from S3...
2025-09-24 18:18:07,117 - INFO - Downloading PDF from S3 to /tmp/tmpf1jl3xmz/e0281466_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:08,913 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:18:08,913 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:08,915 - INFO - Splitting PDF e0281466_DTOGYRLS96ZKXQOO6EY5 into 1 pages
2025-09-24 18:18:08,917 - INFO - Split PDF into 1 pages
2025-09-24 18:18:08,918 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:08,918 - INFO - Expected pages: [1]
2025-09-24 18:18:09,014 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 18:18:09,014 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:09,015 - INFO - Splitting PDF 45c28bc9_DKI9846PWRKYUQA0DQ4A into 1 pages
2025-09-24 18:18:09,016 - INFO - Split PDF into 1 pages
2025-09-24 18:18:09,016 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:09,016 - INFO - Expected pages: [1]
2025-09-24 18:18:09,062 - INFO - Page 2: Extracted 301 characters, 16 lines from 80e80179_CHU18SQU5Q2XCX57ABTP_76fa036b_page_002.pdf
2025-09-24 18:18:09,063 - INFO - Successfully processed page 2
2025-09-24 18:18:09,309 - INFO - Page 1: Extracted 671 characters, 43 lines from 9aa514bc_CMGYRKEPEEGIJHOFEAQ9_063f4cba_page_001.pdf
2025-09-24 18:18:09,309 - INFO - Successfully processed page 1
2025-09-24 18:18:09,310 - INFO - Combined 1 pages into final text
2025-09-24 18:18:09,310 - INFO - Text validation for 9aa514bc_CMGYRKEPEEGIJHOFEAQ9: 688 characters, 1 pages
2025-09-24 18:18:09,310 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:09,310 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:10,632 - INFO - Page 1: Extracted 1169 characters, 61 lines from 80e80179_CHU18SQU5Q2XCX57ABTP_76fa036b_page_001.pdf
2025-09-24 18:18:10,632 - INFO - Successfully processed page 1
2025-09-24 18:18:10,633 - INFO - Combined 2 pages into final text
2025-09-24 18:18:10,633 - INFO - Text validation for 80e80179_CHU18SQU5Q2XCX57ABTP: 1506 characters, 2 pages
2025-09-24 18:18:10,634 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:10,634 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:11,114 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ed338546-b900-4dca-a6fc-1b787a2a652f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:10 GMT', 'content-type': 'application/json', 'content-length': '479', 'connection': 'keep-alive', 'x-amzn-requestid': 'ed338546-b900-4dca-a6fc-1b787a2a652f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this page. It contains \'Invoice\', with no HS/HTS code, no customs info. So it\'s a carrier invoice. So doc_type: "invoice". We output via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2775, 'outputTokens': 59, 'totalTokens': 2834}, 'metrics': {'latencyMs': 474}}
2025-09-24 18:18:11,115 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9aa514bc_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:11,119 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf -> s3://document-extraction-logistically/temp/fd753767_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:11,119 - INFO - 🔍 [18:18:11] Starting classification: EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:11,120 - INFO - ⬆️ [18:18:11] Uploading: ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:11,125 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:11,137 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:11,141 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fd753767_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:11,141 - INFO - Processing PDF from S3...
2025-09-24 18:18:11,141 - INFO - Downloading PDF from S3 to /tmp/tmpuftsoz6_/fd753767_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:12,802 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7ada8c60-2249-49da-a24d-76d153d664f2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:12 GMT', 'content-type': 'application/json', 'content-length': '1015', 'connection': 'keep-alive', 'x-amzn-requestid': '7ada8c60-2249-49da-a24d-76d153d664f2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Need to classify each page. The content appears to be an invoice from Millhouse Logistics Services, with header "Invoice Number" and details. There are no explicit "Commercial Invoice" but it includes shipping details, PO, but no HS codes etc. So it\'s an invoice, not comm_invoice. So both pages are part of same document. No continuation markers. So page1 and page2 both are invoice type. Must output array of objects: documents: each with page_no and doc_type. Use tool call.\n\nThe tool function signature: classify_logistics_doc_type with documents: array of objects with page_no and doc_type. So we call it with those two entries.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3060, 'outputTokens': 167, 'totalTokens': 3227}, 'metrics': {'latencyMs': 961}}
2025-09-24 18:18:12,803 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/80e80179_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:13,126 - INFO - Page 1: Extracted 808 characters, 53 lines from e0281466_DTOGYRLS96ZKXQOO6EY5_e4cab2d4_page_001.pdf
2025-09-24 18:18:13,126 - INFO - Successfully processed page 1
2025-09-24 18:18:13,127 - INFO - Combined 1 pages into final text
2025-09-24 18:18:13,127 - INFO - Text validation for e0281466_DTOGYRLS96ZKXQOO6EY5: 825 characters, 1 pages
2025-09-24 18:18:13,128 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:13,128 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:13,216 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf -> s3://document-extraction-logistically/temp/f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:13,216 - INFO - 🔍 [18:18:13] Starting classification: ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:13,216 - INFO - ⬆️ [18:18:13] Uploading: IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:13,218 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:13,267 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:13,269 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:13,269 - INFO - Processing PDF from S3...
2025-09-24 18:18:13,269 - INFO - Downloading PDF from S3 to /tmp/tmp9d1jdtlu/f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:13,836 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/IGQ6ARKKVIX04G5ZQDJ5.pdf -> s3://document-extraction-logistically/temp/4667d3fe_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:13,836 - INFO - 🔍 [18:18:13] Starting classification: IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:13,837 - INFO - ⬆️ [18:18:13] Uploading: J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:13,839 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:13,861 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:13,864 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4667d3fe_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:13,864 - INFO - Processing PDF from S3...
2025-09-24 18:18:13,864 - INFO - Downloading PDF from S3 to /tmp/tmp1t0xtfh3/4667d3fe_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:13,939 - INFO - Downloaded PDF size: 1.2 MB
2025-09-24 18:18:13,939 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:13,942 - INFO - Splitting PDF fd753767_EJJM2MNOTED9686H34VA into 7 pages
2025-09-24 18:18:13,952 - INFO - Split PDF into 7 pages
2025-09-24 18:18:13,952 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:13,952 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 18:18:14,107 - INFO - Page 1: Extracted 808 characters, 63 lines from 45c28bc9_DKI9846PWRKYUQA0DQ4A_7365ef47_page_001.pdf
2025-09-24 18:18:14,107 - INFO - Successfully processed page 1
2025-09-24 18:18:14,108 - INFO - Combined 1 pages into final text
2025-09-24 18:18:14,108 - INFO - Text validation for 45c28bc9_DKI9846PWRKYUQA0DQ4A: 825 characters, 1 pages
2025-09-24 18:18:14,108 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:14,108 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:14,884 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'aa139242-b9d8-4040-a918-283375091de3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:14 GMT', 'content-type': 'application/json', 'content-length': '552', 'connection': 'keep-alive', 'x-amzn-requestid': 'aa139242-b9d8-4040-a918-283375091de3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The user provided page1. It\'s an invoice. We need to call the function classify_logistics_doc_type with documents array containing page 1 summary: page_no 1, doc_type "invoice". No continuation. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2811, 'outputTokens': 70, 'totalTokens': 2881}, 'metrics': {'latencyMs': 551}}
2025-09-24 18:18:14,884 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e0281466_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:15,396 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 18:18:15,397 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:15,399 - INFO - Splitting PDF 4667d3fe_IGQ6ARKKVIX04G5ZQDJ5 into 1 pages
2025-09-24 18:18:15,404 - INFO - Split PDF into 1 pages
2025-09-24 18:18:15,404 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:15,404 - INFO - Expected pages: [1]
2025-09-24 18:18:15,675 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf -> s3://document-extraction-logistically/temp/a82b9b2c_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:15,676 - INFO - 🔍 [18:18:15] Starting classification: J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:15,676 - INFO - ⬆️ [18:18:15] Uploading: MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:15,682 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:15,699 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:15,702 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a82b9b2c_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:15,702 - INFO - Processing PDF from S3...
2025-09-24 18:18:15,702 - INFO - Downloading PDF from S3 to /tmp/tmpgpf0ibuw/a82b9b2c_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:15,982 - INFO - Downloaded PDF size: 0.9 MB
2025-09-24 18:18:15,982 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:15,983 - INFO - Splitting PDF f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21 into 2 pages
2025-09-24 18:18:15,990 - INFO - Split PDF into 2 pages
2025-09-24 18:18:15,991 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:15,991 - INFO - Expected pages: [1, 2]
2025-09-24 18:18:16,071 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5c02bde9-952a-4757-8520-3a2cdec4987d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:15 GMT', 'content-type': 'application/json', 'content-length': '706', 'connection': 'keep-alive', 'x-amzn-requestid': '5c02bde9-952a-4757-8520-3a2cdec4987d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. We must output a call to classify_logistics_doc_type with documents array containing object page_no:1 and doc_type. Determine doc_type: invoice? We see "Invoice ID" and "Bill to", "Remit to", "Due Date", "Charges", "Total", etc. No commercial invoice. So it\'s a carrier invoice. So doc_type: "invoice". Output accordingly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2835, 'outputTokens': 107, 'totalTokens': 2942}, 'metrics': {'latencyMs': 749}}
2025-09-24 18:18:16,071 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/45c28bc9_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:16,705 - INFO - Page 5: Extracted 117 characters, 3 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_005.pdf
2025-09-24 18:18:16,706 - INFO - Successfully processed page 5
2025-09-24 18:18:16,858 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf -> s3://document-extraction-logistically/temp/9a9d0063_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:16,859 - INFO - 🔍 [18:18:16] Starting classification: MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:16,860 - INFO - ⬆️ [18:18:16] Uploading: NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:16,861 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:16,877 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:16,882 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9a9d0063_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:16,882 - INFO - Processing PDF from S3...
2025-09-24 18:18:16,883 - INFO - Downloading PDF from S3 to /tmp/tmpf8l5o9c1/9a9d0063_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:17,887 - INFO - Page 4: Extracted 443 characters, 22 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_004.pdf
2025-09-24 18:18:17,888 - INFO - Successfully processed page 4
2025-09-24 18:18:18,249 - INFO - Page 1: Extracted 1119 characters, 42 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_001.pdf
2025-09-24 18:18:18,250 - INFO - Successfully processed page 1
2025-09-24 18:18:18,572 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf -> s3://document-extraction-logistically/temp/066e94b8_NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:18,572 - INFO - 🔍 [18:18:18] Starting classification: NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:18,573 - INFO - ⬆️ [18:18:18] Uploading: SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:18,575 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:18,592 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:18,596 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/066e94b8_NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:18,597 - INFO - Processing PDF from S3...
2025-09-24 18:18:18,597 - INFO - Downloading PDF from S3 to /tmp/tmpdm6r0682/066e94b8_NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:19,265 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf -> s3://document-extraction-logistically/temp/e09c9599_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:19,265 - INFO - 🔍 [18:18:19] Starting classification: SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:19,266 - INFO - ⬆️ [18:18:19] Uploading: TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:19,275 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:19,294 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:19,297 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e09c9599_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:19,297 - INFO - Processing PDF from S3...
2025-09-24 18:18:19,297 - INFO - Downloading PDF from S3 to /tmp/tmpjnp8uxvk/e09c9599_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:19,634 - INFO - Page 1: Extracted 609 characters, 31 lines from 4667d3fe_IGQ6ARKKVIX04G5ZQDJ5_606c2326_page_001.pdf
2025-09-24 18:18:19,635 - INFO - Successfully processed page 1
2025-09-24 18:18:19,635 - INFO - Combined 1 pages into final text
2025-09-24 18:18:19,635 - INFO - Text validation for 4667d3fe_IGQ6ARKKVIX04G5ZQDJ5: 626 characters, 1 pages
2025-09-24 18:18:19,636 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:19,636 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:19,843 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TAIJ9HY2DEXLZTHT5SXH.pdf -> s3://document-extraction-logistically/temp/e9f1a592_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:19,844 - INFO - 🔍 [18:18:19] Starting classification: TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:19,845 - INFO - ⬆️ [18:18:19] Uploading: TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:19,845 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:19,863 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:19,867 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e9f1a592_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:19,868 - INFO - Processing PDF from S3...
2025-09-24 18:18:19,868 - INFO - Downloading PDF from S3 to /tmp/tmppeb9eum7/e9f1a592_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:20,178 - INFO - Page 6: Extracted 1476 characters, 75 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_006.pdf
2025-09-24 18:18:20,179 - INFO - Successfully processed page 6
2025-09-24 18:18:20,448 - INFO - Page 7: Extracted 1440 characters, 24 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_007.pdf
2025-09-24 18:18:20,448 - INFO - Successfully processed page 7
2025-09-24 18:18:20,476 - INFO - Page 2: Extracted 1831 characters, 39 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_002.pdf
2025-09-24 18:18:20,478 - INFO - Successfully processed page 2
2025-09-24 18:18:20,478 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf -> s3://document-extraction-logistically/temp/8e0113a8_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:20,478 - INFO - 🔍 [18:18:20] Starting classification: TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:20,479 - INFO - ⬆️ [18:18:20] Uploading: UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:20,480 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:20,497 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:20,501 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8e0113a8_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:20,501 - INFO - Processing PDF from S3...
2025-09-24 18:18:20,501 - INFO - Downloading PDF from S3 to /tmp/tmpir1_x7yo/8e0113a8_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:20,916 - INFO - Page 3: Extracted 1520 characters, 38 lines from fd753767_EJJM2MNOTED9686H34VA_d9c8d66a_page_003.pdf
2025-09-24 18:18:20,916 - INFO - Successfully processed page 3
2025-09-24 18:18:20,917 - INFO - Combined 7 pages into final text
2025-09-24 18:18:20,917 - INFO - Text validation for fd753767_EJJM2MNOTED9686H34VA: 8077 characters, 7 pages
2025-09-24 18:18:20,918 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:20,918 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:21,204 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf -> s3://document-extraction-logistically/temp/b5b17d3a_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:21,204 - INFO - 🔍 [18:18:21] Starting classification: UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:21,205 - INFO - ⬆️ [18:18:21] Uploading: US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:21,205 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:21,225 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:21,268 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b5b17d3a_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:21,268 - INFO - Processing PDF from S3...
2025-09-24 18:18:21,268 - INFO - Downloading PDF from S3 to /tmp/tmps6qm40uy/b5b17d3a_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:21,323 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:18:21,323 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:21,325 - INFO - Splitting PDF e9f1a592_TAIJ9HY2DEXLZTHT5SXH into 1 pages
2025-09-24 18:18:21,332 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7b675979-84f6-48ad-a156-239b2e9ddb3e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:21 GMT', 'content-type': 'application/json', 'content-length': '638', 'connection': 'keep-alive', 'x-amzn-requestid': '7b675979-84f6-48ad-a156-239b2e9ddb3e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. It shows "Rush Logistics" heading and "Invoice". It\'s a carrier invoice. No HS codes. So doc_type = invoice. We need to return a JSON via classify_logistics_doc_type tool. The input to function should be an object with documents: array of objects page_no and doc_type. Only one page. So use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2727, 'outputTokens': 91, 'totalTokens': 2818}, 'metrics': {'latencyMs': 605}}
2025-09-24 18:18:21,333 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4667d3fe_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:21,335 - INFO - Split PDF into 1 pages
2025-09-24 18:18:21,337 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:21,337 - INFO - Expected pages: [1]
2025-09-24 18:18:21,910 - INFO - Page 1: Extracted 888 characters, 70 lines from f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21_d11b638d_page_001.pdf
2025-09-24 18:18:21,910 - INFO - Successfully processed page 1
2025-09-24 18:18:22,164 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf -> s3://document-extraction-logistically/temp/3ff710bd_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:22,164 - INFO - 🔍 [18:18:22] Starting classification: US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:22,164 - INFO - ⬆️ [18:18:22] Uploading: W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:22,165 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:22,174 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:22,176 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3ff710bd_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:22,177 - INFO - Processing PDF from S3...
2025-09-24 18:18:22,177 - INFO - Downloading PDF from S3 to /tmp/tmpa58rjfg3/3ff710bd_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:22,477 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:18:22,477 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:22,483 - INFO - Splitting PDF 8e0113a8_TWW70AYZ4HHN0G7UPMM7 into 3 pages
2025-09-24 18:18:22,510 - INFO - Split PDF into 3 pages
2025-09-24 18:18:22,510 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:22,511 - INFO - Expected pages: [1, 2, 3]
2025-09-24 18:18:22,638 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 18:18:22,638 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:22,639 - INFO - Splitting PDF e09c9599_SPKHMNJUMC10ZF1SJUVA into 2 pages
2025-09-24 18:18:22,642 - INFO - Split PDF into 2 pages
2025-09-24 18:18:22,642 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:22,642 - INFO - Expected pages: [1, 2]
2025-09-24 18:18:22,743 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/W1P39VXY7XRKDJG29JXQ.pdf -> s3://document-extraction-logistically/temp/5f093a32_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:22,743 - INFO - 🔍 [18:18:22] Starting classification: W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:22,744 - INFO - ⬆️ [18:18:22] Uploading: ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:22,745 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:22,761 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:22,767 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5f093a32_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:22,767 - INFO - Processing PDF from S3...
2025-09-24 18:18:22,768 - INFO - Downloading PDF from S3 to /tmp/tmp8581m1gv/5f093a32_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:22,803 - INFO - Page 2: Extracted 3067 characters, 129 lines from f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21_d11b638d_page_002.pdf
2025-09-24 18:18:22,803 - INFO - Successfully processed page 2
2025-09-24 18:18:22,803 - INFO - Combined 2 pages into final text
2025-09-24 18:18:22,803 - INFO - Text validation for f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21: 3991 characters, 2 pages
2025-09-24 18:18:22,804 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:22,804 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:23,308 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:18:23,308 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:23,310 - INFO - Splitting PDF b5b17d3a_UMKAW947G6NUUYO725CC into 2 pages
2025-09-24 18:18:23,315 - INFO - Split PDF into 2 pages
2025-09-24 18:18:23,315 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:23,315 - INFO - Expected pages: [1, 2]
2025-09-24 18:18:23,422 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZNZPCNCYXXVUIEOEH7SH.pdf -> s3://document-extraction-logistically/temp/ca15dc48_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:23,422 - INFO - 🔍 [18:18:23] Starting classification: ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:23,423 - INFO - ⬆️ [18:18:23] Uploading: ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:23,423 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:23,447 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:23,452 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ca15dc48_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:23,452 - INFO - Processing PDF from S3...
2025-09-24 18:18:23,453 - INFO - Downloading PDF from S3 to /tmp/tmpoe01fe75/ca15dc48_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:24,267 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 18:18:24,267 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:24,269 - INFO - Splitting PDF 5f093a32_W1P39VXY7XRKDJG29JXQ into 1 pages
2025-09-24 18:18:24,272 - INFO - Split PDF into 1 pages
2025-09-24 18:18:24,273 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:24,273 - INFO - Expected pages: [1]
2025-09-24 18:18:24,300 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf -> s3://document-extraction-logistically/temp/3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:24,301 - INFO - 🔍 [18:18:24] Starting classification: ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:24,302 - INFO - Initializing TextractProcessor...
2025-09-24 18:18:24,319 - INFO - Initializing BedrockProcessor...
2025-09-24 18:18:24,325 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:24,327 - INFO - Processing PDF from S3...
2025-09-24 18:18:24,334 - INFO - Downloading PDF from S3 to /tmp/tmp4k26696v/3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:24,335 - INFO - 

CMGYRKEPEEGIJHOFEAQ9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:24,340 - INFO - 

✓ Saved result: output/run1_CMGYRKEPEEGIJHOFEAQ9.json
2025-09-24 18:18:24,626 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9aa514bc_CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:24,639 - INFO - 

CHU18SQU5Q2XCX57ABTP.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:24,639 - INFO - 

✓ Saved result: output/run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:18:24,686 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 18:18:24,686 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:24,687 - INFO - Splitting PDF 3ff710bd_US3F65B5JD70IE5R8PG9 into 5 pages
2025-09-24 18:18:24,718 - INFO - Split PDF into 5 pages
2025-09-24 18:18:24,718 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:24,718 - INFO - Expected pages: [1, 2, 3, 4, 5]
2025-09-24 18:18:24,937 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/80e80179_CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:24,960 - INFO - 

DTOGYRLS96ZKXQOO6EY5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:24,960 - INFO - 

✓ Saved result: output/run1_DTOGYRLS96ZKXQOO6EY5.json
2025-09-24 18:18:24,983 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '596ffb92-e5de-4e9c-b578-00ef5293c07e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:24 GMT', 'content-type': 'application/json', 'content-length': '953', 'connection': 'keep-alive', 'x-amzn-requestid': '596ffb92-e5de-4e9c-b578-00ef5293c07e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 appears to be an invoice (carrier invoice). It has Remittance Stub, Bill To, Amount Due, Freight Bill No. It also includes BOL# etc. It looks like invoice. So doc_type: invoice.\n\nPage2 is a Bill of Lading. It has "BILL OF LADING" header, BOL# etc. So doc_type: bol.\n\nWe must output using classify_logistics_doc_type tool. Provide array of objects, each with page_no and doc_type. Need to include pages 1 and 2. Also we need to consider continuation? Page2 might be the same as page1? No, page2 is separate doc type. So just two entries.\n\nReturn tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3711, 'outputTokens': 180, 'totalTokens': 3891}, 'metrics': {'latencyMs': 963}}
2025-09-24 18:18:24,984 - INFO - Page 1: Extracted 779 characters, 52 lines from e9f1a592_TAIJ9HY2DEXLZTHT5SXH_16563f8f_page_001.pdf
2025-09-24 18:18:24,985 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:24,986 - INFO - Successfully processed page 1
2025-09-24 18:18:24,986 - INFO - Combined 1 pages into final text
2025-09-24 18:18:24,988 - INFO - Text validation for e9f1a592_TAIJ9HY2DEXLZTHT5SXH: 796 characters, 1 pages
2025-09-24 18:18:24,990 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:24,991 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:25,260 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e0281466_DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:25,276 - INFO - 

DKI9846PWRKYUQA0DQ4A.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:25,276 - INFO - 

✓ Saved result: output/run1_DKI9846PWRKYUQA0DQ4A.json
2025-09-24 18:18:25,402 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a6da5df1-6101-4ad7-9a83-199fed538152', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:25 GMT', 'content-type': 'application/json', 'content-length': '3680', 'connection': 'keep-alive', 'x-amzn-requestid': 'a6da5df1-6101-4ad7-9a83-199fed538152'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have 7 pages.\n\nWe need to output one object per page with page_no and doc_type. Must use the tool classify_logistics_doc_type with documents array.\n\nWe need to infer doc types.\n\nPage1: shows invoice header, "Invoice Date", "Invoice #", "Amount". Also note "Continued on next page". So page1 is part of invoice. Keywords: Invoice. It\'s a carrier invoice. Also mention "FirstLine Funding Group" and assignment notice. But still invoice. So doc_type invoice.\n\nPage2: Notice of Assignment: It\'s a "Notice of Assignment". This is not in the enum list. But it might be a continuation of previous? The text is a second page separate. It\'s a separate document: "Notice of Assignment". This is not part of invoice? It is separate notice; but the document type is not in list; we can classify as other. But note that page1 had "Continued on next page" referring to page2 maybe? Wait page1 says "Continued on next page" and then also shows the assignment info again. Page2 is a formal assignment letter. So page1 and page2 might be part of same assignment notice? But page1 includes invoice info + assignment notice. Page2 also has assignment letter. Might be separate but could be same doc. However instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." So page1 and page2 might be same document: "Notice of Assignment" or "Assignment Notice". Which type? Not in enum. So we classify as other. But we need to check if page2 is a continuation of page1? It does not start with "continued" but page1 says "Continued on next page". So yes it\'s continuation. So doc_type same as page1? But page1 is invoice. But page1 continues to page2? Actually page1 has invoice and assignment, page2 is separate letter. But page1 says "Continued on next page". So page2 is part of same document as page1. So we assign same doc_type as page1: invoice. But page2 is not invoice but assignment letter. However classification should follow the doc type of the multi-page doc. The doc is maybe "Invoice with Assignment" but still invoice. So page2 doc_type invoice.\n\nPage3: Another invoice. Same as page1. So invoice.\n\nPage4: Another invoice. So invoice.\n\nPage5: Just a pickup/delivered message. Not invoice. It says "PICKED: BUTLER,WI DELIVERED TO : CANONSBURG,PA". Could be part of a POD? It is just a pickup/delivery note. Might be part of a POD or a BOL. However no signature or delivery receipt. It\'s just a note. Maybe part of a POD? But no "Proof of Delivery". So likely other. But maybe it\'s a "POD" but minimal. Let\'s look: It contains only pickup and delivered location. No signature. So other.\n\nPage6: "Shipping Instructions & Rate Confirmation" page. This is a rate confirmation. Keywords: Rate Confirmation. So doc_type rate_confirmation. It\'s multi-page (page 1 of 2). Page7 continues. So page7 same doc_type rate_confirmation.\n\nThus output 7 objects.\n\nWe need to call tool once with documents array containing all pages.\n\nLet\'s construct.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"invoice"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"rate_confirmation"},{"page_no":7,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4782, 'outputTokens': 793, 'totalTokens': 5575}, 'metrics': {'latencyMs': 3252}}
2025-09-24 18:18:25,402 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fd753767_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:25,573 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/45c28bc9_DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:25,588 - INFO - 

IGQ6ARKKVIX04G5ZQDJ5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:25,588 - INFO - 

✓ Saved result: output/run1_IGQ6ARKKVIX04G5ZQDJ5.json
2025-09-24 18:18:25,870 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4667d3fe_IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:25,918 - INFO - 

ESRNPUZ2WJ48Y3NTAX21.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:18:25,918 - INFO - 

✓ Saved result: output/run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:18:26,205 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f0b5d5c5_ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:26,275 - INFO - 

EJJM2MNOTED9686H34VA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        },
        {
            "page_no": 4,
            "doc_type": "invoice"
        },
        {
            "page_no": 5,
            "doc_type": "other"
        },
        {
            "page_no": 6,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 7,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 18:18:26,275 - INFO - 

✓ Saved result: output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:26,562 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fd753767_EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:26,800 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '75545562-562e-49f2-9d10-58d3258150fb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:26 GMT', 'content-type': 'application/json', 'content-length': '662', 'connection': 'keep-alive', 'x-amzn-requestid': '75545562-562e-49f2-9d10-58d3258150fb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Only one page. Determine type. Text: "Invoice" header. Contains Invoice #, Invoice Date, Terms, Bill To, Load details, Rates and Charges. No HS code, no customs. So it\'s carrier invoice. Not comm_invoice. So doc_type: "invoice". Output with array containing single object: page_no 1, doc_type "invoice". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2807, 'outputTokens': 99, 'totalTokens': 2906}, 'metrics': {'latencyMs': 670}}
2025-09-24 18:18:26,800 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e9f1a592_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:26,823 - INFO - 

TAIJ9HY2DEXLZTHT5SXH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:26,823 - INFO - 

✓ Saved result: output/run1_TAIJ9HY2DEXLZTHT5SXH.json
2025-09-24 18:18:26,976 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 18:18:26,976 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:26,979 - INFO - Splitting PDF 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON into 10 pages
2025-09-24 18:18:27,003 - INFO - Downloaded PDF size: 0.8 MB
2025-09-24 18:18:27,004 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:27,007 - INFO - Splitting PDF 9a9d0063_MVSX79LFMEJ1OI57DGBO into 2 pages
2025-09-24 18:18:27,019 - INFO - Split PDF into 2 pages
2025-09-24 18:18:27,019 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:27,020 - INFO - Expected pages: [1, 2]
2025-09-24 18:18:27,020 - INFO - Split PDF into 10 pages
2025-09-24 18:18:27,020 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:27,020 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-09-24 18:18:27,093 - INFO - Page 3: Extracted 884 characters, 71 lines from 8e0113a8_TWW70AYZ4HHN0G7UPMM7_b9cf3f94_page_003.pdf
2025-09-24 18:18:27,093 - INFO - Successfully processed page 3
2025-09-24 18:18:27,118 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e9f1a592_TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:27,305 - INFO - Downloaded PDF size: 1.2 MB
2025-09-24 18:18:27,306 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:27,309 - INFO - Splitting PDF a82b9b2c_J2HEKQMZPE7HUYJF14PH into 10 pages
2025-09-24 18:18:27,343 - INFO - Split PDF into 10 pages
2025-09-24 18:18:27,343 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:27,343 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-09-24 18:18:27,469 - INFO - Page 1: Extracted 835 characters, 66 lines from 8e0113a8_TWW70AYZ4HHN0G7UPMM7_b9cf3f94_page_001.pdf
2025-09-24 18:18:27,469 - INFO - Successfully processed page 1
2025-09-24 18:18:27,713 - INFO - Page 1: Extracted 734 characters, 58 lines from e09c9599_SPKHMNJUMC10ZF1SJUVA_a41dba87_page_001.pdf
2025-09-24 18:18:27,713 - INFO - Successfully processed page 1
2025-09-24 18:18:27,928 - INFO - Page 1: Extracted 623 characters, 35 lines from 5f093a32_W1P39VXY7XRKDJG29JXQ_d445c055_page_001.pdf
2025-09-24 18:18:27,928 - INFO - Successfully processed page 1
2025-09-24 18:18:27,929 - INFO - Combined 1 pages into final text
2025-09-24 18:18:27,929 - INFO - Text validation for 5f093a32_W1P39VXY7XRKDJG29JXQ: 640 characters, 1 pages
2025-09-24 18:18:27,929 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:27,930 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:28,178 - INFO - Page 2: Extracted 3342 characters, 95 lines from 8e0113a8_TWW70AYZ4HHN0G7UPMM7_b9cf3f94_page_002.pdf
2025-09-24 18:18:28,178 - INFO - Successfully processed page 2
2025-09-24 18:18:28,178 - INFO - Combined 3 pages into final text
2025-09-24 18:18:28,178 - INFO - Text validation for 8e0113a8_TWW70AYZ4HHN0G7UPMM7: 5116 characters, 3 pages
2025-09-24 18:18:28,179 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:28,179 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:28,396 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 18:18:28,396 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:28,398 - INFO - Splitting PDF ca15dc48_ZNZPCNCYXXVUIEOEH7SH into 1 pages
2025-09-24 18:18:28,400 - INFO - Split PDF into 1 pages
2025-09-24 18:18:28,400 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:28,400 - INFO - Expected pages: [1]
2025-09-24 18:18:28,926 - INFO - Page 2: Extracted 2945 characters, 113 lines from e09c9599_SPKHMNJUMC10ZF1SJUVA_a41dba87_page_002.pdf
2025-09-24 18:18:28,926 - INFO - Successfully processed page 2
2025-09-24 18:18:28,927 - INFO - Combined 2 pages into final text
2025-09-24 18:18:28,927 - INFO - Text validation for e09c9599_SPKHMNJUMC10ZF1SJUVA: 3715 characters, 2 pages
2025-09-24 18:18:28,927 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:28,928 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:29,394 - INFO - Page 4: Extracted 1636 characters, 75 lines from 3ff710bd_US3F65B5JD70IE5R8PG9_766c6e35_page_004.pdf
2025-09-24 18:18:29,394 - INFO - Successfully processed page 4
2025-09-24 18:18:29,478 - INFO - Page 3: Extracted 560 characters, 33 lines from 3ff710bd_US3F65B5JD70IE5R8PG9_766c6e35_page_003.pdf
2025-09-24 18:18:29,478 - INFO - Successfully processed page 3
2025-09-24 18:18:29,604 - INFO - Page 2: Extracted 4442 characters, 192 lines from b5b17d3a_UMKAW947G6NUUYO725CC_d6102454_page_002.pdf
2025-09-24 18:18:29,604 - INFO - Successfully processed page 2
2025-09-24 18:18:29,679 - INFO - Page 5: Extracted 1435 characters, 22 lines from 3ff710bd_US3F65B5JD70IE5R8PG9_766c6e35_page_005.pdf
2025-09-24 18:18:29,679 - INFO - Successfully processed page 5
2025-09-24 18:18:29,716 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a25ae1a6-b70f-4e32-ba96-9fcada6e6d0f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:29 GMT', 'content-type': 'application/json', 'content-length': '553', 'connection': 'keep-alive', 'x-amzn-requestid': 'a25ae1a6-b70f-4e32-ba96-9fcada6e6d0f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page that is clearly an invoice. It contains "INVOICE" header, "Invoice #", "Bill To", "Amount", "Total", etc. So classify as "invoice". There is only page 1. No continuation. So output JSON via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2762, 'outputTokens': 75, 'totalTokens': 2837}, 'metrics': {'latencyMs': 586}}
2025-09-24 18:18:29,716 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5f093a32_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:29,741 - INFO - 

W1P39VXY7XRKDJG29JXQ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:29,741 - INFO - 

✓ Saved result: output/run1_W1P39VXY7XRKDJG29JXQ.json
2025-09-24 18:18:29,992 - INFO - Page 1: Extracted 4110 characters, 187 lines from b5b17d3a_UMKAW947G6NUUYO725CC_d6102454_page_001.pdf
2025-09-24 18:18:29,992 - INFO - Successfully processed page 1
2025-09-24 18:18:29,992 - INFO - Combined 2 pages into final text
2025-09-24 18:18:29,992 - INFO - Text validation for b5b17d3a_UMKAW947G6NUUYO725CC: 8588 characters, 2 pages
2025-09-24 18:18:29,993 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:29,993 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:30,021 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5f093a32_W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:30,059 - INFO - Page 1: Extracted 759 characters, 40 lines from 3ff710bd_US3F65B5JD70IE5R8PG9_766c6e35_page_001.pdf
2025-09-24 18:18:30,060 - INFO - Successfully processed page 1
2025-09-24 18:18:30,393 - INFO - Page 2: Extracted 2306 characters, 55 lines from 3ff710bd_US3F65B5JD70IE5R8PG9_766c6e35_page_002.pdf
2025-09-24 18:18:30,393 - INFO - Successfully processed page 2
2025-09-24 18:18:30,394 - INFO - Combined 5 pages into final text
2025-09-24 18:18:30,394 - INFO - Text validation for 3ff710bd_US3F65B5JD70IE5R8PG9: 6789 characters, 5 pages
2025-09-24 18:18:30,395 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:30,395 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:30,413 - INFO - Downloaded PDF size: 2.0 MB
2025-09-24 18:18:30,414 - INFO - Splitting PDF into individual pages...
2025-09-24 18:18:30,416 - INFO - Splitting PDF 066e94b8_NTI160L8QH452446HMQV into 4 pages
2025-09-24 18:18:30,421 - INFO - Split PDF into 4 pages
2025-09-24 18:18:30,421 - INFO - Processing pages with Textract in parallel...
2025-09-24 18:18:30,421 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 18:18:30,571 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd00aadb0-ebc1-4768-af2b-8a275af33027', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:30 GMT', 'content-type': 'application/json', 'content-length': '1130', 'connection': 'keep-alive', 'x-amzn-requestid': 'd00aadb0-ebc1-4768-af2b-8a275af33027'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page.\n\nPage1: contains "Freight Bill" and "THIS IS YOUR INVOICE" and "Bill of Lading: 873131" and "Purchase Order: 499621". It has invoice details, amounts, "Please Pay $138.26". So likely an invoice. It\'s a carrier invoice, not commercial invoice (no HS code). So doc_type: invoice.\n\nPage2: "UNIFORM STRAIGHT BILL OF LADING" header, contains BOL details. So doc_type: bol.\n\nPage3: "Delivery Receipt" header. It includes "Delivery Receipt" and lists driver signatures etc. This is a Proof of Delivery (POD). So doc_type: pod.\n\nReturn tool call with array of documents objects: page_no 1 doc_type invoice, 2 bol, 3 pod.\n\nCheck continuation: none.\n\nReturn via classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4050, 'outputTokens': 222, 'totalTokens': 4272}, 'metrics': {'latencyMs': 1256}}
2025-09-24 18:18:30,571 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8e0113a8_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:30,629 - INFO - 

TWW70AYZ4HHN0G7UPMM7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        },
        {
            "page_no": 3,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 18:18:30,629 - INFO - 

✓ Saved result: output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:30,922 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8e0113a8_TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:30,935 - INFO - Page 1: Extracted 1120 characters, 59 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_001.pdf
2025-09-24 18:18:30,935 - INFO - Successfully processed page 1
2025-09-24 18:18:31,064 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '674faafb-67ce-4ea5-9ce9-63aa13b78fd8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:30 GMT', 'content-type': 'application/json', 'content-length': '830', 'connection': 'keep-alive', 'x-amzn-requestid': '674faafb-67ce-4ea5-9ce9-63aa13b78fd8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Page1: appears to be an invoice. Contains "INVOICE" header, "Bill To", "Total Amount", etc. So doc_type invoice. We need to classify each page. The second page is a bill of lading. It has "BILL OF LADING" header. So doc_type bol. Check continuation: page1 says "Page 1 of 1" internal. Page2 says "PAGE 1 of 1" but it\'s a separate doc. So two distinct documents. So output two objects: page_no 1 doc_type invoice; page_no 2 doc_type bol. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3602, 'outputTokens': 158, 'totalTokens': 3760}, 'metrics': {'latencyMs': 983}}
2025-09-24 18:18:31,064 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e09c9599_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:31,113 - INFO - 

SPKHMNJUMC10ZF1SJUVA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:18:31,113 - INFO - 

✓ Saved result: output/run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:18:31,268 - INFO - Page 4: Extracted 502 characters, 36 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_004.pdf
2025-09-24 18:18:31,269 - INFO - Successfully processed page 4
2025-09-24 18:18:31,294 - INFO - Page 2: Extracted 535 characters, 40 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_002.pdf
2025-09-24 18:18:31,294 - INFO - Successfully processed page 2
2025-09-24 18:18:31,399 - INFO - Page 1: Extracted 644 characters, 46 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_001.pdf
2025-09-24 18:18:31,400 - INFO - Successfully processed page 1
2025-09-24 18:18:31,405 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e09c9599_SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:31,625 - INFO - Page 1: Extracted 1031 characters, 65 lines from 9a9d0063_MVSX79LFMEJ1OI57DGBO_3ab7bd27_page_001.pdf
2025-09-24 18:18:31,626 - INFO - Successfully processed page 1
2025-09-24 18:18:31,718 - INFO - Page 6: Extracted 28 characters, 8 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_006.pdf
2025-09-24 18:18:31,719 - INFO - Successfully processed page 6
2025-09-24 18:18:31,800 - INFO - Page 3: Extracted 835 characters, 38 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_003.pdf
2025-09-24 18:18:31,800 - INFO - Successfully processed page 3
2025-09-24 18:18:32,033 - INFO - Page 5: Extracted 0 characters, 0 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_005.pdf
2025-09-24 18:18:32,033 - INFO - Successfully processed page 5
2025-09-24 18:18:32,299 - INFO - Page 2: Extracted 2306 characters, 44 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_002.pdf
2025-09-24 18:18:32,299 - INFO - Successfully processed page 2
2025-09-24 18:18:32,750 - INFO - Page 3: Extracted 3655 characters, 106 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_003.pdf
2025-09-24 18:18:32,750 - INFO - Successfully processed page 3
2025-09-24 18:18:32,766 - INFO - Page 6: Extracted 3053 characters, 112 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_006.pdf
2025-09-24 18:18:32,766 - INFO - Successfully processed page 6
2025-09-24 18:18:33,287 - INFO - Page 4: Extracted 2909 characters, 106 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_004.pdf
2025-09-24 18:18:33,287 - INFO - Successfully processed page 4
2025-09-24 18:18:33,570 - INFO - Page 5: Extracted 3946 characters, 133 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_005.pdf
2025-09-24 18:18:33,570 - INFO - Successfully processed page 5
2025-09-24 18:18:33,709 - INFO - Page 1: Extracted 1118 characters, 71 lines from ca15dc48_ZNZPCNCYXXVUIEOEH7SH_eaa2d655_page_001.pdf
2025-09-24 18:18:33,709 - INFO - Successfully processed page 1
2025-09-24 18:18:33,709 - INFO - Combined 1 pages into final text
2025-09-24 18:18:33,710 - INFO - Text validation for ca15dc48_ZNZPCNCYXXVUIEOEH7SH: 1135 characters, 1 pages
2025-09-24 18:18:33,710 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:33,710 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:34,020 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6bd84e5e-024e-41c4-95de-6151bf26409f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:33 GMT', 'content-type': 'application/json', 'content-length': '2817', 'connection': 'keep-alive', 'x-amzn-requestid': '6bd84e5e-024e-41c4-95de-6151bf26409f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 5 pages. Need classify each page.\n\nPage1: Looks like a statement with billing info. Contains "INV DATE", "INV #", "INV AMOUNT" etc. It\'s an invoice statement, but the header could be "STATEMENT" but not "Invoice". The doc includes "PAYABLE TO" etc. It\'s a statement of invoice. Probably an "invoice" type. No HS codes etc. So doc_type "invoice".\n\nPage2: This is a letter about assignment and payment instructions. It\'s a "clear to pay" or maybe "clear to pay" document. It has "Clear to Pay" instructions? Actually it\'s a notice of assignment. Not necessarily payment authorization. It\'s about assignment and payment instructions. Might be a "clear_to_pay"? The keyword "Clear to Pay" not present. There\'s "Notice of Assignment" but it\'s about assignment. Could be "clear_to_pay" as it\'s a payment authorization letter. But "clear_to_pay" defined as "Approval for payment or ACH/Wire/Check transfer request or approval." This is more of a notification of assignment, not approval. But likely classification as "clear_to_pay" because it instructs payments to be sent to RTS Financial Service. However, "clear_to_pay" includes stamp "Clear to Pay" etc. This letter does not have explicit "Clear to Pay" phrase. It does have "Notice of Assignment" which is a variant of clear to pay. Many vendors use "Notice of Assignment" as payment instructions. I\'d classify as "clear_to_pay". Alternatively "other". But I\'ll choose clear_to_pay.\n\nPage3: This page is an invoice again. It has "Invoice #" etc. So "invoice". Same as page1.\n\nPage4: This page is "Shipping Instructions & Rate Confirmation". So it\'s a "rate_confirmation" document. Contains "Rate Confirmation" in header. So doc_type "rate_confirmation".\n\nPage5: Also part of same "Shipping Instructions & Rate Confirmation" document, page 2. So also "rate_confirmation".\n\nWe need to ensure continuity: pages 4 and 5 are same doc. They are page 1 and page 2 of the same. So same type. No continuation markers but page numbers show page 1 of 2 and page 2 of 2. So treat same.\n\nAlso check if page2 is continuation? It says "DocuSign Envelope ID" etc. No continuation. So separate doc.\n\nThus output array of objects with page_no and doc_type.\n\nReturn via function call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"clear_to_pay"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"rate_confirmation"},{"page_no":5,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4479, 'outputTokens': 576, 'totalTokens': 5055}, 'metrics': {'latencyMs': 2457}}
2025-09-24 18:18:34,020 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3ff710bd_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:34,075 - INFO - 

US3F65B5JD70IE5R8PG9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "clear_to_pay"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        },
        {
            "page_no": 4,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 5,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 18:18:34,075 - INFO - 

✓ Saved result: output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:34,301 - INFO - Page 7: Extracted 51 characters, 10 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_007.pdf
2025-09-24 18:18:34,301 - INFO - Successfully processed page 7
2025-09-24 18:18:34,419 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3ff710bd_US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:34,762 - INFO - Page 2: Extracted 3706 characters, 111 lines from 9a9d0063_MVSX79LFMEJ1OI57DGBO_3ab7bd27_page_002.pdf
2025-09-24 18:18:34,762 - INFO - Successfully processed page 2
2025-09-24 18:18:34,762 - INFO - Combined 2 pages into final text
2025-09-24 18:18:34,763 - INFO - Text validation for 9a9d0063_MVSX79LFMEJ1OI57DGBO: 4773 characters, 2 pages
2025-09-24 18:18:34,763 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:34,763 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:34,772 - INFO - Page 8: Extracted 1189 characters, 66 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_008.pdf
2025-09-24 18:18:34,772 - INFO - Successfully processed page 8
2025-09-24 18:18:34,875 - INFO - Page 9: Extracted 1541 characters, 74 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_009.pdf
2025-09-24 18:18:34,876 - INFO - Successfully processed page 9
2025-09-24 18:18:35,033 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '84b47b2c-0acd-4c97-8499-9bd9460c64c5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:34 GMT', 'content-type': 'application/json', 'content-length': '4158', 'connection': 'keep-alive', 'x-amzn-requestid': '84b47b2c-0acd-4c97-8499-9bd9460c64c5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page1 contains invoice and bill of lading. The page says "ORIGINAL INVOICE" and has total amount due. It also has "BILL OF LADING" header. It\'s a combined page containing both invoice and BOL. According to definition combined_carrier_documents: page containing multiple carrier documents bundled. So doc_type should be combined_carrier_documents.\n\nBut check guidelines: If page is part of multi-page single document: each page same type.\n\nPage1 is page 1 of 2. Page2 is delivery receipt and also has BOL. Page2 says "Delivery Receipt" and has bill of lading. So it\'s a Proof of Delivery? It says "Delivery Receipt" with signatures etc. So doc_type: pod? It says "Delivery Receipt" and signatures. That matches POD. However, page2 also contains BOL. It might be combined? But the page likely is a POD (Proof of Delivery). The presence of BOL indicates maybe a combined? But the definition of combined_carrier_documents: page containing multiple carrier documents bundled together (BOL + invoice + POD). Here page2 has BOL + POD. That\'s two documents. So could be combined. But the page seems to be a POD with BOL details. The typical POD includes BOL reference but not full BOL. But the page includes BOL header? It says "BILL OF LADING" header. It might be a full BOL included for reference. This might still be considered POD.\n\nWe need to decide. The guidelines: combined_carrier_documents: "Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.)". That includes BOL + POD. So page2 qualifies. But is it exactly a combination? It has Delivery Receipt, BOL, and maybe invoice? No invoice. So two docs BOL + POD. That fits combined. But maybe they want pod for page2. The two examples: page1 clearly has invoice + BOL. So combined. Page2 has POD + BOL. So combined as well. But maybe they want pod for page2 because it\'s a POD. Which to choose? The tool expects one doc_type.\n\nCheck if page2 includes "Delivery Receipt" label. Yes. That is typical POD. Also includes signatures. So doc_type pod. The presence of BOL might be as part of the POD. But the definition of combined_carrier_documents says multiple distinct document headers on one page. Here we have "Delivery Receipt" and "BILL OF LADING". So yes multiple distinct. So combined. However, is "Delivery Receipt" considered a distinct header? Yes. So combined.\n\nBut maybe the correct classification is pod. Let\'s examine guidelines: For each page, we must return exactly one doc_type. If page includes multiple distinct document headers, it\'s combined. So page2 has both "Delivery Receipt" and "BILL OF LADING". So combined. But we need to decide. I think the correct is pod because the primary purpose is POD. But the presence of BOL suggests it\'s a combined. The instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." But here page1 and page2 part of same shipment? Could be separate documents. They are separate pages of a single PDF, but each page maybe separate documents. But page1 and page2 are separate documents? They both seem part of same physical shipment but separate docs. If PDF contains multiple documents, each page is separate doc. But the instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type." So if page1 and page2 are part of one combined document, they share same type. But are they? They have different main headers: page1 is invoice, page2 is POD. Likely separate. So each page separate doc. So page1 => combined_carrier_documents. page2 => pod.\n\nLet\'s decide that.\n\nThus output two objects: page_no 1 doc_type combined_carrier_documents, page_no 2 doc_type pod.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"pod"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4950, 'outputTokens': 879, 'totalTokens': 5829}, 'metrics': {'latencyMs': 3849}}
2025-09-24 18:18:35,033 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b5b17d3a_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:35,118 - INFO - 

UMKAW947G6NUUYO725CC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 2,
            "doc_type": "pod"
        }
    ]
}
2025-09-24 18:18:35,118 - INFO - 

✓ Saved result: output/run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:18:35,392 - INFO - Page 9: Extracted 2002 characters, 30 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_009.pdf
2025-09-24 18:18:35,393 - INFO - Successfully processed page 9
2025-09-24 18:18:35,404 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b5b17d3a_UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:35,593 - INFO - Page 1: Extracted 771 characters, 36 lines from 066e94b8_NTI160L8QH452446HMQV_5d1cf493_page_001.pdf
2025-09-24 18:18:35,593 - INFO - Successfully processed page 1
2025-09-24 18:18:35,653 - INFO - Page 8: Extracted 3132 characters, 115 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_008.pdf
2025-09-24 18:18:35,653 - INFO - Successfully processed page 8
2025-09-24 18:18:35,760 - INFO - Page 10: Extracted 1044 characters, 32 lines from a82b9b2c_J2HEKQMZPE7HUYJF14PH_ad404a91_page_010.pdf
2025-09-24 18:18:35,760 - INFO - Successfully processed page 10
2025-09-24 18:18:35,761 - INFO - Combined 10 pages into final text
2025-09-24 18:18:35,761 - INFO - Text validation for a82b9b2c_J2HEKQMZPE7HUYJF14PH: 11673 characters, 10 pages
2025-09-24 18:18:35,763 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:35,763 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:35,807 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6d03ece8-68c3-4a6d-b933-2f4a98985f3c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:35 GMT', 'content-type': 'application/json', 'content-length': '1018', 'connection': 'keep-alive', 'x-amzn-requestid': '6d03ece8-68c3-4a6d-b933-2f4a98985f3c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page present. Identify document type. This looks like a carrier invoice. It has Invoice Number, Invoice Date, Bill To, Ship Date, Bill of Lading number, description, weight, rate, charges, total due $356.93. No HS/HTS codes, not a commercial invoice. So classify as invoice. Output JSON via tool classify_logistics_doc_type.\n\nWe need to output exactly one object for page 1.\n\nLet\'s call doc_type "invoice". Provide array of objects? The function expects documents: array of objects with page_no and doc_type. For each page we output an object.\n\nThus output: { "documents": [ { "page_no": 1, "doc_type": "invoice" } ] }\n\nWe need to use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2919, 'outputTokens': 182, 'totalTokens': 3101}, 'metrics': {'latencyMs': 967}}
2025-09-24 18:18:35,808 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ca15dc48_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:35,833 - INFO - 

ZNZPCNCYXXVUIEOEH7SH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-24 18:18:35,833 - INFO - 

✓ Saved result: output/run1_ZNZPCNCYXXVUIEOEH7SH.json
2025-09-24 18:18:36,117 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ca15dc48_ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:36,254 - INFO - Page 10: Extracted 1217 characters, 39 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_010.pdf
2025-09-24 18:18:36,254 - INFO - Successfully processed page 10
2025-09-24 18:18:36,370 - INFO - Page 2: Extracted 2000 characters, 30 lines from 066e94b8_NTI160L8QH452446HMQV_5d1cf493_page_002.pdf
2025-09-24 18:18:36,370 - INFO - Successfully processed page 2
2025-09-24 18:18:36,417 - INFO - Page 7: Extracted 3086 characters, 113 lines from 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON_459b2b7e_page_007.pdf
2025-09-24 18:18:36,417 - INFO - Successfully processed page 7
2025-09-24 18:18:36,417 - INFO - Combined 10 pages into final text
2025-09-24 18:18:36,418 - INFO - Text validation for 3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON: 21501 characters, 10 pages
2025-09-24 18:18:36,418 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:36,418 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:36,659 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '442778c1-5488-4b91-9830-4a9d31133ef3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:36 GMT', 'content-type': 'application/json', 'content-length': '659', 'connection': 'keep-alive', 'x-amzn-requestid': '442778c1-5488-4b91-9830-4a9d31133ef3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 2 pages. Need to classify each page. Page1 is an invoice. It says "INVOICE / FACTURE". So doc_type invoice. Page2 has a "STRAIGHT BILL OF LADING ORIGINAL - NOT NEGOTIABLE". So doc_type bol. It\'s a separate document. Not continuation. So return two objects. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3932, 'outputTokens': 103, 'totalTokens': 4035}, 'metrics': {'latencyMs': 693}}
2025-09-24 18:18:36,660 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9a9d0063_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:36,724 - INFO - 

MVSX79LFMEJ1OI57DGBO.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:18:36,724 - INFO - 

✓ Saved result: output/run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:18:36,814 - INFO - Page 3: Extracted 1176 characters, 65 lines from 066e94b8_NTI160L8QH452446HMQV_5d1cf493_page_003.pdf
2025-09-24 18:18:36,815 - INFO - Successfully processed page 3
2025-09-24 18:18:37,036 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9a9d0063_MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:38,008 - INFO - Page 4: Extracted 3498 characters, 100 lines from 066e94b8_NTI160L8QH452446HMQV_5d1cf493_page_004.pdf
2025-09-24 18:18:38,008 - INFO - Successfully processed page 4
2025-09-24 18:18:38,008 - INFO - Combined 4 pages into final text
2025-09-24 18:18:38,008 - INFO - Text validation for 066e94b8_NTI160L8QH452446HMQV: 7519 characters, 4 pages
2025-09-24 18:18:38,009 - INFO - Analyzing document types with Bedrock...
2025-09-24 18:18:38,009 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 18:18:40,153 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fe16b6d1-0938-4c29-8852-1e1d71429a88', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:39 GMT', 'content-type': 'application/json', 'content-length': '3167', 'connection': 'keep-alive', 'x-amzn-requestid': 'fe16b6d1-0938-4c29-8852-1e1d71429a88'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 10 pages. Need classify each.\n\nPage1: Invoice with Invoice #, Bill To, etc. No HS codes etc. So "invoice". Page2: Notice of Assignment and Change of Payee letter. No header of type. It\'s a letter; likely continuation of page1? Actually page2 seems separate letter. It is continuation? It starts with "Empire National Inc. (MC-966111) EXP ... Notice of Assignment and Change of Payee". It\'s a separate document. But it\'s continuation? It is a letter that says "This letter hereby authorizes and instructs you to remit your payment of all invoices from Empire National Inc...". This could be part of the same invoice? It is separate letter. But not a standard doc type. It\'s a notice, but not invoice, not BOL etc. Could be "other". But we could check if it\'s continuation of previous page? It doesn\'t start with "continued". So doc type "other". Page3: BOL. So doc_type bol. Page4: BOL. Also bol. Page5: blank page? No content. If blank page and no header, likely continuation? But it\'s a blank page. According to rules, if blank page and no header, and maybe part of previous doc? Could be same doc type as previous page? It\'s a blank page in between pages? Might be continuation of BOL? But page4 is BOL. Blank page could be part of same doc? But if blank page, we can classify as continuation? But no header. According to rule: if page is part of multi-page single document: each page still gets same doc_type. So if this is part of BOL, page4 was BOL page1. Page5 blank may be page2 of BOL? But page4 says "PAGE 1 of 1". So no continuation. So page5 blank maybe separate blank. According to rule, if page is continuation, assign same doc_type. But it\'s blank; but maybe it\'s just a blank page, not part of any doc. Maybe classify as "other". But if it\'s not continuation, no doc_type. But we need one per page. So "other".\n\nPage6 and page7 have garbled text. Probably not meaningful. Likely "other".\n\nPage8: Shipping Instructions & Rate Confirmation. This is rate confirmation. So doc_type rate_confirmation. But check if it\'s "cust_rate_confirmation"? It has "Carrier Rate Confirmation"? The header says "Shipping Instructions & Rate Confirmation". So "rate_confirmation". Page9: continues rate confirmation; page8 had Page 1 of 2; page9 page 2; still rate_confirmation.\n\nPage10: Notice of Release letter from WEX. It\'s a notice, not standard doc. So "other".\n\nThus produce list of 10 objects.\n\nNeed to output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"},{"page_no":8,"doc_type":"rate_confirmation"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 5870, 'outputTokens': 692, 'totalTokens': 6562}, 'metrics': {'latencyMs': 3172}}
2025-09-24 18:18:40,153 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a82b9b2c_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:40,260 - INFO - 

J2HEKQMZPE7HUYJF14PH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "bol"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        },
        {
            "page_no": 5,
            "doc_type": "other"
        },
        {
            "page_no": 6,
            "doc_type": "other"
        },
        {
            "page_no": 7,
            "doc_type": "other"
        },
        {
            "page_no": 8,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 9,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 10,
            "doc_type": "other"
        }
    ]
}
2025-09-24 18:18:40,260 - INFO - 

✓ Saved result: output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:40,540 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a82b9b2c_J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:41,076 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2e9cabd9-3eda-4741-bbee-3ab38435bf51', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:40 GMT', 'content-type': 'application/json', 'content-length': '3476', 'connection': 'keep-alive', 'x-amzn-requestid': '2e9cabd9-3eda-4741-bbee-3ab38435bf51'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There are 10 pages. We need to use classify_logistics_doc_type with documents array containing objects with page_no and doc_type. For each page, determine type.\n\nWe have multiple types: invoice, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, comm_invoice, customs_doc, weight_and_inspection_cert, inspection_cert, nmfc_cert, tender_from_cust, so_confirmation, ingate, outgate, other.\n\nLet\'s examine each page.\n\nPage1: It says "Invoice" at top. Contains Invoice #, Bill To, Ship To, etc. Also includes Fuel Surcharge, etc. No HS codes or customs. So it\'s a carrier invoice. So doc_type: invoice.\n\nPage2: It seems a Freight Bill (Bill of Lading? Actually "FREIGHT BILL" header). Contains details like Consignee, Shipper, Pieces, Weight, Rate. It\'s a freight bill which is a Bill of Lading? Actually "FREIGHT BILL" might be a type of BOL. The header "FREIGHT BILL" but also contains "PRO. NO." maybe a freight bill. It likely is a BOL too. But does it contain BOL? The document might be a "Bill of Lading" but the header says "FREIGHT BILL". The keywords for bol: "Bill of Lading", "B/L", "Shipper", "Consignee", etc. This page includes Consignee, Shipper, but not "Bill of Lading" phrase. Also not "BOL number". But likely it\'s a freight bill which is a type of BOL (short form). I\'d classify as bol.\n\nPage3: This is a "Bill Of Lading - Short Form" header. Yes, doc_type: bol.\n\nPage4: Also a "FREIGHT BILL" but appears similar to page2. So bol.\n\nPage5: "Straight Bill Of Lading - Short Form" header. So bol.\n\nPage6: "BILL OF LADING" header. So bol.\n\nPages7: Also BOL. Page8: BOL.\n\nPages9: "Shipping Instructions & Rate Confirmation" header. Rate Confirmation type. So doc_type: rate_confirmation.\n\nPage10: Same header, rate confirmation. So rate_confirmation.\n\nBut we need to consider continuity: page2-8 are all part of same document? They are separate pages but likely part of the same BOL or freight bill. However, we need to decide if each page is separate doc. The instruction: If page is part of a multi-page single document, each page still gets same doc_type. So we can assign bol for pages 2-8.\n\nAlso page1 is invoice. It might be a separate document. So separate.\n\nPage9-10: rate_confirmation. They are two pages of same doc.\n\nNow check if any page indicates continuation lines. Page2-8 may have "ORIGINAL" header. But no \'continued\' indicator. So each page is separate but same type.\n\nThus output array of 10 objects. Let\'s produce.\n\nWe must call tool classify_logistics_doc_type with the documents array. Each object has page_no and doc_type. Ensure page numbers ascending.\n\nReturn.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"bol"},{"page_no":7,"doc_type":"bol"},{"page_no":8,"doc_type":"bol"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"rate_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 8363, 'outputTokens': 793, 'totalTokens': 9156}, 'metrics': {'latencyMs': 3445}}
2025-09-24 18:18:41,076 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:41,263 - INFO - 

ZT8R6RF8DXQOQ3Q4N7ON.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "bol"
        },
        {
            "page_no": 3,
            "doc_type": "bol"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        },
        {
            "page_no": 5,
            "doc_type": "bol"
        },
        {
            "page_no": 6,
            "doc_type": "bol"
        },
        {
            "page_no": 7,
            "doc_type": "bol"
        },
        {
            "page_no": 8,
            "doc_type": "bol"
        },
        {
            "page_no": 9,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 10,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-24 18:18:41,263 - INFO - 

✓ Saved result: output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:41,587 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3a826f5f_ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,335 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c7246f5e-0698-4956-b830-b2aefa904f29', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 12:48:42 GMT', 'content-type': 'application/json', 'content-length': '2989', 'connection': 'keep-alive', 'x-amzn-requestid': 'c7246f5e-0698-4956-b830-b2aefa904f29'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 4 pages provided. Need to classify each page into doc_type. We must output via function classify_logistics_doc_type, with documents array containing objects: page_no and doc_type. For each page, we must decide the type.\n\nWe need to examine each page content.\n\nPage1: The content starts with a header: "Triumph Assigned for SAFELINE FREIGHT LLC... Invoice # ...". It looks like an Invoice. It has "Invoice #" and "Bill to:" etc. It seems like a carrier invoice. Also there\'s "NOTICE OF ASSIGNMENT" and "Contract Amount $1,000.00". That is a carrier invoice. Not a commercial invoice? No mention of HS code, customs. So doc_type: invoice.\n\nPage2: Looks like a continuation of page1 or separate? Page2 starts with "Payment: Line Haul: $1,000..., Terms:". Then "Carrier certifies that the assigned driver is compliant...". This appears to be part of the same invoice? It includes "Shipping Instructions & Rate Confirmation" at bottom. But still part of same invoice. It also has "Proof of delivery (POD), invoices, payment inquiries, rate verification..." It\'s still part of invoice. It also has "Page 2 of 2". So page2 is continuation of page1, thus same doc_type: invoice.\n\nPage3: "Shipping Instructions & Rate Confirmation" heading. This looks like a rate confirmation or shipping instructions, not an invoice. There\'s "Order #: ALLAUS11775580 ... Pickup Date: 09/18/2025 ... Carrier ... 53\' Van $1,000.00 USD ...". This is a "Rate Confirmation". It has signature lines: "Signed By: (Packer Freight) Signed By: (SAFELINE FREIGHT LLC)". So it is a "rate_confirmation" document. Also "Shipping Instructions & Rate Confirmation" header. So doc_type: rate_confirmation.\n\nPage4: This is "STRAIGHT BILL OF LADING SHORT FORM Original - Not Negotiable". This clearly is a Bill of Lading. So doc_type: bol. Even though might have "Bill of Lading" header. So bol.\n\nThus mapping:\nPage1: invoice\nPage2: invoice\nPage3: rate_confirmation\nPage4: bol\n\nWe must output in ascending page order. Use function call.\n\nLet\'s produce JSON array of objects with page_no and doc_type.\n\nCheck if any page is "continuation" rule: page2 started with "Payment: ...". Not start with "continued" or "continued on next page". But page2 is second page of same invoice. We should classify it as invoice. Good.\n\nSo produce:\n\n[\n{page_no:1, doc_type:"invoice"},\n{page_no:2, doc_type:"invoice"},\n{page_no:3, doc_type:"rate_confirmation"},\n{page_no:4, doc_type:"bol"}\n]\n\nReturn via function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"rate_confirmation"},{"page_no":4,"doc_type":"bol"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4560, 'outputTokens': 656, 'totalTokens': 5216}, 'metrics': {'latencyMs': 2968}}
2025-09-24 18:18:42,336 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/066e94b8_NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:42,409 - INFO - 

NTI160L8QH452446HMQV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        }
    ]
}
2025-09-24 18:18:42,410 - INFO - 

✓ Saved result: output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,801 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/066e94b8_NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:42,805 - INFO - 
📊 Processing Summary:
2025-09-24 18:18:42,805 - INFO -    Total files: 18
2025-09-24 18:18:42,805 - INFO -    Successful: 18
2025-09-24 18:18:42,805 - INFO -    Failed: 0
2025-09-24 18:18:42,805 - INFO -    Duration: 41.31 seconds
2025-09-24 18:18:42,806 - INFO -    Output directory: output
2025-09-24 18:18:42,806 - INFO - 
📋 Successfully Processed Files:
2025-09-24 18:18:42,806 - INFO -    📄 CHU18SQU5Q2XCX57ABTP.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"}]}
2025-09-24 18:18:42,806 - INFO -    📄 CMGYRKEPEEGIJHOFEAQ9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,806 - INFO -    📄 DKI9846PWRKYUQA0DQ4A.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,806 - INFO -    📄 DTOGYRLS96ZKXQOO6EY5.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,806 - INFO -    📄 EJJM2MNOTED9686H34VA.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"invoice"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"rate_confirmation"},{"page_no":7,"doc_type":"rate_confirmation"}]}
2025-09-24 18:18:42,807 - INFO -    📄 ESRNPUZ2WJ48Y3NTAX21.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:18:42,807 - INFO -    📄 IGQ6ARKKVIX04G5ZQDJ5.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,807 - INFO -    📄 J2HEKQMZPE7HUYJF14PH.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"other"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"other"},{"page_no":6,"doc_type":"other"},{"page_no":7,"doc_type":"other"},{"page_no":8,"doc_type":"rate_confirmation"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"other"}]}
2025-09-24 18:18:42,807 - INFO -    📄 MVSX79LFMEJ1OI57DGBO.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:18:42,807 - INFO -    📄 NTI160L8QH452446HMQV.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"rate_confirmation"},{"page_no":4,"doc_type":"bol"}]}
2025-09-24 18:18:42,807 - INFO -    📄 SPKHMNJUMC10ZF1SJUVA.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"}]}
2025-09-24 18:18:42,808 - INFO -    📄 TAIJ9HY2DEXLZTHT5SXH.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,808 - INFO -    📄 TWW70AYZ4HHN0G7UPMM7.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"pod"}]}
2025-09-24 18:18:42,808 - INFO -    📄 UMKAW947G6NUUYO725CC.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"pod"}]}
2025-09-24 18:18:42,808 - INFO -    📄 US3F65B5JD70IE5R8PG9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"clear_to_pay"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"rate_confirmation"},{"page_no":5,"doc_type":"rate_confirmation"}]}
2025-09-24 18:18:42,808 - INFO -    📄 W1P39VXY7XRKDJG29JXQ.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,808 - INFO -    📄 ZNZPCNCYXXVUIEOEH7SH.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-24 18:18:42,808 - INFO -    📄 ZT8R6RF8DXQOQ3Q4N7ON.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"bol"},{"page_no":3,"doc_type":"bol"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"bol"},{"page_no":7,"doc_type":"bol"},{"page_no":8,"doc_type":"bol"},{"page_no":9,"doc_type":"rate_confirmation"},{"page_no":10,"doc_type":"rate_confirmation"}]}
2025-09-24 18:18:42,809 - INFO - 
============================================================================================================================================
2025-09-24 18:18:42,809 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 18:18:42,809 - INFO - ============================================================================================================================================
2025-09-24 18:18:42,809 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 18:18:42,809 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:18:42,810 - INFO - CHU18SQU5Q2XCX57ABTP.pdf                           1      invoice                                  run1_CHU18SQU5Q2XCX57ABTP.json                                                  
2025-09-24 18:18:42,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:42,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:18:42,810 - INFO - 
2025-09-24 18:18:42,810 - INFO - CHU18SQU5Q2XCX57ABTP.pdf                           2      invoice                                  run1_CHU18SQU5Q2XCX57ABTP.json                                                  
2025-09-24 18:18:42,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf
2025-09-24 18:18:42,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:18:42,810 - INFO - 
2025-09-24 18:18:42,810 - INFO - CMGYRKEPEEGIJHOFEAQ9.pdf                           1      invoice                                  run1_CMGYRKEPEEGIJHOFEAQ9.json                                                  
2025-09-24 18:18:42,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CMGYRKEPEEGIJHOFEAQ9.pdf
2025-09-24 18:18:42,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CMGYRKEPEEGIJHOFEAQ9.json
2025-09-24 18:18:42,810 - INFO - 
2025-09-24 18:18:42,811 - INFO - DKI9846PWRKYUQA0DQ4A.pdf                           1      invoice                                  run1_DKI9846PWRKYUQA0DQ4A.json                                                  
2025-09-24 18:18:42,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DKI9846PWRKYUQA0DQ4A.pdf
2025-09-24 18:18:42,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DKI9846PWRKYUQA0DQ4A.json
2025-09-24 18:18:42,811 - INFO - 
2025-09-24 18:18:42,811 - INFO - DTOGYRLS96ZKXQOO6EY5.pdf                           1      invoice                                  run1_DTOGYRLS96ZKXQOO6EY5.json                                                  
2025-09-24 18:18:42,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DTOGYRLS96ZKXQOO6EY5.pdf
2025-09-24 18:18:42,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTOGYRLS96ZKXQOO6EY5.json
2025-09-24 18:18:42,811 - INFO - 
2025-09-24 18:18:42,811 - INFO - EJJM2MNOTED9686H34VA.pdf                           1      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,811 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,811 - INFO - 
2025-09-24 18:18:42,811 - INFO - EJJM2MNOTED9686H34VA.pdf                           2      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,811 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,812 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,812 - INFO - 
2025-09-24 18:18:42,812 - INFO - EJJM2MNOTED9686H34VA.pdf                           3      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,812 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,812 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,812 - INFO - 
2025-09-24 18:18:42,812 - INFO - EJJM2MNOTED9686H34VA.pdf                           4      invoice                                  run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,812 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,812 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,812 - INFO - 
2025-09-24 18:18:42,812 - INFO - EJJM2MNOTED9686H34VA.pdf                           5      other                                    run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,812 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,812 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,813 - INFO - 
2025-09-24 18:18:42,813 - INFO - EJJM2MNOTED9686H34VA.pdf                           6      rate_confirmation                        run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,813 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,813 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,813 - INFO - 
2025-09-24 18:18:42,813 - INFO - EJJM2MNOTED9686H34VA.pdf                           7      rate_confirmation                        run1_EJJM2MNOTED9686H34VA.json                                                  
2025-09-24 18:18:42,813 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf
2025-09-24 18:18:42,813 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,813 - INFO - 
2025-09-24 18:18:42,813 - INFO - ESRNPUZ2WJ48Y3NTAX21.pdf                           1      invoice                                  run1_ESRNPUZ2WJ48Y3NTAX21.json                                                  
2025-09-24 18:18:42,813 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:42,813 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:18:42,813 - INFO - 
2025-09-24 18:18:42,814 - INFO - ESRNPUZ2WJ48Y3NTAX21.pdf                           2      bol                                      run1_ESRNPUZ2WJ48Y3NTAX21.json                                                  
2025-09-24 18:18:42,814 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf
2025-09-24 18:18:42,814 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:18:42,814 - INFO - 
2025-09-24 18:18:42,814 - INFO - IGQ6ARKKVIX04G5ZQDJ5.pdf                           1      invoice                                  run1_IGQ6ARKKVIX04G5ZQDJ5.json                                                  
2025-09-24 18:18:42,814 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/IGQ6ARKKVIX04G5ZQDJ5.pdf
2025-09-24 18:18:42,814 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IGQ6ARKKVIX04G5ZQDJ5.json
2025-09-24 18:18:42,814 - INFO - 
2025-09-24 18:18:42,814 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           1      invoice                                  run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,814 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,814 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,814 - INFO - 
2025-09-24 18:18:42,814 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           2      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,815 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,815 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,815 - INFO - 
2025-09-24 18:18:42,815 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           3      bol                                      run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,815 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,815 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,815 - INFO - 
2025-09-24 18:18:42,815 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           4      bol                                      run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,815 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,815 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,815 - INFO - 
2025-09-24 18:18:42,815 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           5      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,815 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,815 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,816 - INFO - 
2025-09-24 18:18:42,816 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           6      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,816 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,816 - INFO - 
2025-09-24 18:18:42,816 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           7      other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,816 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,816 - INFO - 
2025-09-24 18:18:42,816 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           8      rate_confirmation                        run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,816 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,816 - INFO - 
2025-09-24 18:18:42,816 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           9      rate_confirmation                        run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,816 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,816 - INFO - 
2025-09-24 18:18:42,816 - INFO - J2HEKQMZPE7HUYJF14PH.pdf                           10     other                                    run1_J2HEKQMZPE7HUYJF14PH.json                                                  
2025-09-24 18:18:42,816 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf
2025-09-24 18:18:42,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,817 - INFO - 
2025-09-24 18:18:42,817 - INFO - MVSX79LFMEJ1OI57DGBO.pdf                           1      invoice                                  run1_MVSX79LFMEJ1OI57DGBO.json                                                  
2025-09-24 18:18:42,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:42,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:18:42,817 - INFO - 
2025-09-24 18:18:42,817 - INFO - MVSX79LFMEJ1OI57DGBO.pdf                           2      bol                                      run1_MVSX79LFMEJ1OI57DGBO.json                                                  
2025-09-24 18:18:42,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf
2025-09-24 18:18:42,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:18:42,817 - INFO - 
2025-09-24 18:18:42,817 - INFO - NTI160L8QH452446HMQV.pdf                           1      invoice                                  run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:18:42,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:42,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,817 - INFO - 
2025-09-24 18:18:42,817 - INFO - NTI160L8QH452446HMQV.pdf                           2      invoice                                  run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:18:42,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:42,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,817 - INFO - 
2025-09-24 18:18:42,817 - INFO - NTI160L8QH452446HMQV.pdf                           3      rate_confirmation                        run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:18:42,817 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:42,817 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,818 - INFO - 
2025-09-24 18:18:42,818 - INFO - NTI160L8QH452446HMQV.pdf                           4      bol                                      run1_NTI160L8QH452446HMQV.json                                                  
2025-09-24 18:18:42,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf
2025-09-24 18:18:42,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,818 - INFO - 
2025-09-24 18:18:42,818 - INFO - SPKHMNJUMC10ZF1SJUVA.pdf                           1      invoice                                  run1_SPKHMNJUMC10ZF1SJUVA.json                                                  
2025-09-24 18:18:42,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:42,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:18:42,818 - INFO - 
2025-09-24 18:18:42,818 - INFO - SPKHMNJUMC10ZF1SJUVA.pdf                           2      bol                                      run1_SPKHMNJUMC10ZF1SJUVA.json                                                  
2025-09-24 18:18:42,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf
2025-09-24 18:18:42,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:18:42,818 - INFO - 
2025-09-24 18:18:42,818 - INFO - TAIJ9HY2DEXLZTHT5SXH.pdf                           1      invoice                                  run1_TAIJ9HY2DEXLZTHT5SXH.json                                                  
2025-09-24 18:18:42,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TAIJ9HY2DEXLZTHT5SXH.pdf
2025-09-24 18:18:42,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TAIJ9HY2DEXLZTHT5SXH.json
2025-09-24 18:18:42,818 - INFO - 
2025-09-24 18:18:42,818 - INFO - TWW70AYZ4HHN0G7UPMM7.pdf                           1      invoice                                  run1_TWW70AYZ4HHN0G7UPMM7.json                                                  
2025-09-24 18:18:42,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:42,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:42,818 - INFO - 
2025-09-24 18:18:42,818 - INFO - TWW70AYZ4HHN0G7UPMM7.pdf                           2      bol                                      run1_TWW70AYZ4HHN0G7UPMM7.json                                                  
2025-09-24 18:18:42,818 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:42,818 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:42,819 - INFO - 
2025-09-24 18:18:42,819 - INFO - TWW70AYZ4HHN0G7UPMM7.pdf                           3      pod                                      run1_TWW70AYZ4HHN0G7UPMM7.json                                                  
2025-09-24 18:18:42,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf
2025-09-24 18:18:42,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:42,819 - INFO - 
2025-09-24 18:18:42,819 - INFO - UMKAW947G6NUUYO725CC.pdf                           1      combined_carrier_d...                    run1_UMKAW947G6NUUYO725CC.json                                                  
2025-09-24 18:18:42,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:42,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:18:42,819 - INFO - 
2025-09-24 18:18:42,819 - INFO - UMKAW947G6NUUYO725CC.pdf                           2      pod                                      run1_UMKAW947G6NUUYO725CC.json                                                  
2025-09-24 18:18:42,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf
2025-09-24 18:18:42,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:18:42,819 - INFO - 
2025-09-24 18:18:42,819 - INFO - US3F65B5JD70IE5R8PG9.pdf                           1      invoice                                  run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:18:42,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:42,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,819 - INFO - 
2025-09-24 18:18:42,819 - INFO - US3F65B5JD70IE5R8PG9.pdf                           2      clear_to_pay                             run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:18:42,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:42,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,819 - INFO - 
2025-09-24 18:18:42,819 - INFO - US3F65B5JD70IE5R8PG9.pdf                           3      invoice                                  run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:18:42,819 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:42,819 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,820 - INFO - 
2025-09-24 18:18:42,820 - INFO - US3F65B5JD70IE5R8PG9.pdf                           4      rate_confirmation                        run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:18:42,820 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:42,820 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,820 - INFO - 
2025-09-24 18:18:42,820 - INFO - US3F65B5JD70IE5R8PG9.pdf                           5      rate_confirmation                        run1_US3F65B5JD70IE5R8PG9.json                                                  
2025-09-24 18:18:42,820 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf
2025-09-24 18:18:42,820 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,820 - INFO - 
2025-09-24 18:18:42,820 - INFO - W1P39VXY7XRKDJG29JXQ.pdf                           1      invoice                                  run1_W1P39VXY7XRKDJG29JXQ.json                                                  
2025-09-24 18:18:42,820 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/W1P39VXY7XRKDJG29JXQ.pdf
2025-09-24 18:18:42,820 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W1P39VXY7XRKDJG29JXQ.json
2025-09-24 18:18:42,820 - INFO - 
2025-09-24 18:18:42,820 - INFO - ZNZPCNCYXXVUIEOEH7SH.pdf                           1      invoice                                  run1_ZNZPCNCYXXVUIEOEH7SH.json                                                  
2025-09-24 18:18:42,820 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZNZPCNCYXXVUIEOEH7SH.pdf
2025-09-24 18:18:42,820 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZNZPCNCYXXVUIEOEH7SH.json
2025-09-24 18:18:42,820 - INFO - 
2025-09-24 18:18:42,821 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           1      invoice                                  run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,821 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,821 - INFO - 
2025-09-24 18:18:42,821 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           2      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,821 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,821 - INFO - 
2025-09-24 18:18:42,821 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           3      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,821 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,821 - INFO - 
2025-09-24 18:18:42,821 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           4      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,821 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,822 - INFO - 
2025-09-24 18:18:42,822 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           5      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,822 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,822 - INFO - 
2025-09-24 18:18:42,822 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           6      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,822 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,822 - INFO - 
2025-09-24 18:18:42,822 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           7      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,822 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,822 - INFO - 
2025-09-24 18:18:42,822 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           8      bol                                      run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,822 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,822 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,823 - INFO - 
2025-09-24 18:18:42,823 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           9      rate_confirmation                        run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,823 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,823 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,823 - INFO - 
2025-09-24 18:18:42,823 - INFO - ZT8R6RF8DXQOQ3Q4N7ON.pdf                           10     rate_confirmation                        run1_ZT8R6RF8DXQOQ3Q4N7ON.json                                                  
2025-09-24 18:18:42,823 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf
2025-09-24 18:18:42,823 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,823 - INFO - 
2025-09-24 18:18:42,823 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 18:18:42,823 - INFO - Total entries: 56
2025-09-24 18:18:42,823 - INFO - ============================================================================================================================================
2025-09-24 18:18:42,823 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 18:18:42,823 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:18:42,823 - INFO -   1. CHU18SQU5Q2XCX57ABTP.pdf            Page 1   → invoice         | run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:18:42,823 - INFO -   2. CHU18SQU5Q2XCX57ABTP.pdf            Page 2   → invoice         | run1_CHU18SQU5Q2XCX57ABTP.json
2025-09-24 18:18:42,824 - INFO -   3. CMGYRKEPEEGIJHOFEAQ9.pdf            Page 1   → invoice         | run1_CMGYRKEPEEGIJHOFEAQ9.json
2025-09-24 18:18:42,824 - INFO -   4. DKI9846PWRKYUQA0DQ4A.pdf            Page 1   → invoice         | run1_DKI9846PWRKYUQA0DQ4A.json
2025-09-24 18:18:42,824 - INFO -   5. DTOGYRLS96ZKXQOO6EY5.pdf            Page 1   → invoice         | run1_DTOGYRLS96ZKXQOO6EY5.json
2025-09-24 18:18:42,824 - INFO -   6. EJJM2MNOTED9686H34VA.pdf            Page 1   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -   7. EJJM2MNOTED9686H34VA.pdf            Page 2   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -   8. EJJM2MNOTED9686H34VA.pdf            Page 3   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -   9. EJJM2MNOTED9686H34VA.pdf            Page 4   → invoice         | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -  10. EJJM2MNOTED9686H34VA.pdf            Page 5   → other           | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -  11. EJJM2MNOTED9686H34VA.pdf            Page 6   → rate_confirmation | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -  12. EJJM2MNOTED9686H34VA.pdf            Page 7   → rate_confirmation | run1_EJJM2MNOTED9686H34VA.json
2025-09-24 18:18:42,824 - INFO -  13. ESRNPUZ2WJ48Y3NTAX21.pdf            Page 1   → invoice         | run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:18:42,824 - INFO -  14. ESRNPUZ2WJ48Y3NTAX21.pdf            Page 2   → bol             | run1_ESRNPUZ2WJ48Y3NTAX21.json
2025-09-24 18:18:42,824 - INFO -  15. IGQ6ARKKVIX04G5ZQDJ5.pdf            Page 1   → invoice         | run1_IGQ6ARKKVIX04G5ZQDJ5.json
2025-09-24 18:18:42,824 - INFO -  16. J2HEKQMZPE7HUYJF14PH.pdf            Page 1   → invoice         | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,824 - INFO -  17. J2HEKQMZPE7HUYJF14PH.pdf            Page 2   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,824 - INFO -  18. J2HEKQMZPE7HUYJF14PH.pdf            Page 3   → bol             | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,824 - INFO -  19. J2HEKQMZPE7HUYJF14PH.pdf            Page 4   → bol             | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,824 - INFO -  20. J2HEKQMZPE7HUYJF14PH.pdf            Page 5   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,825 - INFO -  21. J2HEKQMZPE7HUYJF14PH.pdf            Page 6   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,825 - INFO -  22. J2HEKQMZPE7HUYJF14PH.pdf            Page 7   → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,825 - INFO -  23. J2HEKQMZPE7HUYJF14PH.pdf            Page 8   → rate_confirmation | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,825 - INFO -  24. J2HEKQMZPE7HUYJF14PH.pdf            Page 9   → rate_confirmation | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,825 - INFO -  25. J2HEKQMZPE7HUYJF14PH.pdf            Page 10  → other           | run1_J2HEKQMZPE7HUYJF14PH.json
2025-09-24 18:18:42,825 - INFO -  26. MVSX79LFMEJ1OI57DGBO.pdf            Page 1   → invoice         | run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:18:42,825 - INFO -  27. MVSX79LFMEJ1OI57DGBO.pdf            Page 2   → bol             | run1_MVSX79LFMEJ1OI57DGBO.json
2025-09-24 18:18:42,825 - INFO -  28. NTI160L8QH452446HMQV.pdf            Page 1   → invoice         | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,825 - INFO -  29. NTI160L8QH452446HMQV.pdf            Page 2   → invoice         | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,825 - INFO -  30. NTI160L8QH452446HMQV.pdf            Page 3   → rate_confirmation | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,825 - INFO -  31. NTI160L8QH452446HMQV.pdf            Page 4   → bol             | run1_NTI160L8QH452446HMQV.json
2025-09-24 18:18:42,826 - INFO -  32. SPKHMNJUMC10ZF1SJUVA.pdf            Page 1   → invoice         | run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:18:42,826 - INFO -  33. SPKHMNJUMC10ZF1SJUVA.pdf            Page 2   → bol             | run1_SPKHMNJUMC10ZF1SJUVA.json
2025-09-24 18:18:42,826 - INFO -  34. TAIJ9HY2DEXLZTHT5SXH.pdf            Page 1   → invoice         | run1_TAIJ9HY2DEXLZTHT5SXH.json
2025-09-24 18:18:42,826 - INFO -  35. TWW70AYZ4HHN0G7UPMM7.pdf            Page 1   → invoice         | run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:42,826 - INFO -  36. TWW70AYZ4HHN0G7UPMM7.pdf            Page 2   → bol             | run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:42,826 - INFO -  37. TWW70AYZ4HHN0G7UPMM7.pdf            Page 3   → pod             | run1_TWW70AYZ4HHN0G7UPMM7.json
2025-09-24 18:18:42,826 - INFO -  38. UMKAW947G6NUUYO725CC.pdf            Page 1   → combined_carrier_documents | run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:18:42,826 - INFO -  39. UMKAW947G6NUUYO725CC.pdf            Page 2   → pod             | run1_UMKAW947G6NUUYO725CC.json
2025-09-24 18:18:42,826 - INFO -  40. US3F65B5JD70IE5R8PG9.pdf            Page 1   → invoice         | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,826 - INFO -  41. US3F65B5JD70IE5R8PG9.pdf            Page 2   → clear_to_pay    | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,826 - INFO -  42. US3F65B5JD70IE5R8PG9.pdf            Page 3   → invoice         | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,826 - INFO -  43. US3F65B5JD70IE5R8PG9.pdf            Page 4   → rate_confirmation | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,826 - INFO -  44. US3F65B5JD70IE5R8PG9.pdf            Page 5   → rate_confirmation | run1_US3F65B5JD70IE5R8PG9.json
2025-09-24 18:18:42,826 - INFO -  45. W1P39VXY7XRKDJG29JXQ.pdf            Page 1   → invoice         | run1_W1P39VXY7XRKDJG29JXQ.json
2025-09-24 18:18:42,826 - INFO -  46. ZNZPCNCYXXVUIEOEH7SH.pdf            Page 1   → invoice         | run1_ZNZPCNCYXXVUIEOEH7SH.json
2025-09-24 18:18:42,826 - INFO -  47. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 1   → invoice         | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  48. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 2   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  49. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 3   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  50. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 4   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  51. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 5   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  52. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 6   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  53. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 7   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  54. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 8   → bol             | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,826 - INFO -  55. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 9   → rate_confirmation | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,827 - INFO -  56. ZT8R6RF8DXQOQ3Q4N7ON.pdf            Page 10  → rate_confirmation | run1_ZT8R6RF8DXQOQ3Q4N7ON.json
2025-09-24 18:18:42,827 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 18:18:42,827 - INFO - 
✅ Test completed: {'total_files': 18, 'processed': 18, 'failed': 0, 'errors': [], 'duration_seconds': 41.308666, 'processed_files': [{'filename': 'CHU18SQU5Q2XCX57ABTP.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CHU18SQU5Q2XCX57ABTP.pdf'}, {'filename': 'CMGYRKEPEEGIJHOFEAQ9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/CMGYRKEPEEGIJHOFEAQ9.pdf'}, {'filename': 'DKI9846PWRKYUQA0DQ4A.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DKI9846PWRKYUQA0DQ4A.pdf'}, {'filename': 'DTOGYRLS96ZKXQOO6EY5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/DTOGYRLS96ZKXQOO6EY5.pdf'}, {'filename': 'EJJM2MNOTED9686H34VA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}, {'page_no': 4, 'doc_type': 'invoice'}, {'page_no': 5, 'doc_type': 'other'}, {'page_no': 6, 'doc_type': 'rate_confirmation'}, {'page_no': 7, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/EJJM2MNOTED9686H34VA.pdf'}, {'filename': 'ESRNPUZ2WJ48Y3NTAX21.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ESRNPUZ2WJ48Y3NTAX21.pdf'}, {'filename': 'IGQ6ARKKVIX04G5ZQDJ5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/IGQ6ARKKVIX04G5ZQDJ5.pdf'}, {'filename': 'J2HEKQMZPE7HUYJF14PH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'other'}, {'page_no': 3, 'doc_type': 'bol'}, {'page_no': 4, 'doc_type': 'bol'}, {'page_no': 5, 'doc_type': 'other'}, {'page_no': 6, 'doc_type': 'other'}, {'page_no': 7, 'doc_type': 'other'}, {'page_no': 8, 'doc_type': 'rate_confirmation'}, {'page_no': 9, 'doc_type': 'rate_confirmation'}, {'page_no': 10, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/J2HEKQMZPE7HUYJF14PH.pdf'}, {'filename': 'MVSX79LFMEJ1OI57DGBO.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/MVSX79LFMEJ1OI57DGBO.pdf'}, {'filename': 'NTI160L8QH452446HMQV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'rate_confirmation'}, {'page_no': 4, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/NTI160L8QH452446HMQV.pdf'}, {'filename': 'SPKHMNJUMC10ZF1SJUVA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/SPKHMNJUMC10ZF1SJUVA.pdf'}, {'filename': 'TAIJ9HY2DEXLZTHT5SXH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TAIJ9HY2DEXLZTHT5SXH.pdf'}, {'filename': 'TWW70AYZ4HHN0G7UPMM7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}, {'page_no': 3, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/TWW70AYZ4HHN0G7UPMM7.pdf'}, {'filename': 'UMKAW947G6NUUYO725CC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}, {'page_no': 2, 'doc_type': 'pod'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/UMKAW947G6NUUYO725CC.pdf'}, {'filename': 'US3F65B5JD70IE5R8PG9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'clear_to_pay'}, {'page_no': 3, 'doc_type': 'invoice'}, {'page_no': 4, 'doc_type': 'rate_confirmation'}, {'page_no': 5, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/US3F65B5JD70IE5R8PG9.pdf'}, {'filename': 'W1P39VXY7XRKDJG29JXQ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/W1P39VXY7XRKDJG29JXQ.pdf'}, {'filename': 'ZNZPCNCYXXVUIEOEH7SH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZNZPCNCYXXVUIEOEH7SH.pdf'}, {'filename': 'ZT8R6RF8DXQOQ3Q4N7ON.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'bol'}, {'page_no': 3, 'doc_type': 'bol'}, {'page_no': 4, 'doc_type': 'bol'}, {'page_no': 5, 'doc_type': 'bol'}, {'page_no': 6, 'doc_type': 'bol'}, {'page_no': 7, 'doc_type': 'bol'}, {'page_no': 8, 'doc_type': 'bol'}, {'page_no': 9, 'doc_type': 'rate_confirmation'}, {'page_no': 10, 'doc_type': 'rate_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v2/invoice/ZT8R6RF8DXQOQ3Q4N7ON.pdf'}]}
