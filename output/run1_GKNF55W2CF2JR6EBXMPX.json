{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9998396039009094, "Height": 1.0, "Left": 0.00016038602916523814, "Top": 0.0}, "Polygon": [{"X": 0.00016038602916523814, "Y": 1.5528131669384493e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.00016992581367958337, "Y": 1.0}]}, "Id": "6bc657f5-c826-4dbd-aae2-81d594baf547", "Relationships": [{"Type": "CHILD", "Ids": ["fe9d5bf7-e3e0-4fde-acde-9b81de0947d5", "b5d401a0-c997-4f76-bb7a-ddd5c83e47b6", "7112c5dc-1f24-4eff-a61c-c3fa339710e4", "cd4f58fa-2d89-4d07-af2f-0e5aa926decd", "b110ac11-7a45-4dcb-a926-7b5a004444b5", "f2a7b332-40ce-4f07-8222-db2e2c422e76", "aabda136-0aa5-4185-a3bf-673d44e9a62a", "daa7bdff-379c-4e39-9cc4-07d010fd2f2b", "25a17dfc-fbda-465a-b136-c2fceebdc870", "d6006d79-ad69-436a-b948-58a0d1b9c0cb", "f7491b07-1dad-4d08-a6f8-56f5ec2559dc", "62949721-0943-402e-866b-4682c40ae45a", "9e9987dc-efcc-4435-8357-5699913b2ddb", "5f4cd833-cfa8-4664-b179-963508fe786e", "4460ad84-0f4a-4ba8-9743-a11b0c33312a", "1a2454f6-eef5-4748-85a4-ea8ecb08db8e", "03804593-11c6-49c9-8a29-38e582fd37e3", "cbc0debf-f451-4a8a-b5a7-96e072ba7689", "5eaf20c5-8017-4442-b4e7-071197aec613", "f25e2b36-f354-491c-bca5-e7b6aee980ce", "93ecdbb5-f9b8-4c7f-a771-b6c6df25c923", "799886a7-d6e2-40ee-acd4-ff0e4e6eb740", "b44d9e93-f6b5-4c41-970d-bcf54c1d705b", "c2791944-0eb5-4a04-a9b3-2afc77b06338", "36a9ec70-2d3f-4326-9ae0-7d8e3f337f77", "f11604b7-b9e4-465e-9431-27c57c7ea17b", "b42b14e0-3632-4c29-9bc1-fd28656a7aa5", "8c244dff-70ba-403a-aada-4c54f57f16fe", "1647f48d-04d8-4d1e-84b9-6bad434605c5", "adbdc99e-e210-4caf-a3bc-d65bc39bf8ce", "96709f70-52b5-4a64-aa25-0bf2e510fe82", "6e5d11e0-e581-4ee7-994e-553a1a76a774", "44f2c128-f3a6-419f-843b-ffb1c73ad6cd", "288bb4f9-5ac0-4c63-ba47-7cf698d60c57", "de55c621-ddf1-4ccf-8201-2440776382e6", "6c337a3f-2935-4757-83aa-2ffc8d2afeb3", "d0f1360c-8386-4fed-9668-de0c2cb158c1", "1495437f-7fe8-4b9f-865b-2ff6dc1c50cf", "4440230d-8de3-44d7-aa77-72f8eb32c28b", "d120a464-2377-406e-9c8e-e03b13ba1eb1", "a8996110-d375-4f61-b51b-780c5d938aeb", "14733cce-8866-4bf5-8eb8-b1ed5fe9ba76"]}]}, {"BlockType": "LINE", "Confidence": 79.26498413085938, "Text": "IDSH", "Geometry": {"BoundingBox": {"Width": 0.29960623383522034, "Height": 0.07476698607206345, "Left": 0.09147091209888458, "Top": 0.06498025357723236}, "Polygon": [{"X": 0.09147091209888458, "Y": 0.06498327106237411}, {"X": 0.39107662439346313, "Y": 0.06498025357723236}, {"X": 0.3910771310329437, "Y": 0.13974392414093018}, {"X": 0.0914715826511383, "Y": 0.1397472321987152}]}, "Id": "fe9d5bf7-e3e0-4fde-acde-9b81de0947d5", "Relationships": [{"Type": "CHILD", "Ids": ["287d9f6a-0474-46ea-b9fb-c2052da1dd02"]}]}, {"BlockType": "LINE", "Confidence": 92.85263061523438, "Text": "iNNOVATIVE DISTRIBUTION SERVICES", "Geometry": {"BoundingBox": {"Width": 0.2972712814807892, "Height": 0.012811906635761261, "Left": 0.09128565341234207, "Top": 0.13858506083488464}, "Polygon": [{"X": 0.09128565341234207, "Y": 0.1385883390903473}, {"X": 0.3885568678379059, "Y": 0.13858506083488464}, {"X": 0.38855695724487305, "Y": 0.15139362215995789}, {"X": 0.09128576517105103, "Y": 0.1513969600200653}]}, "Id": "b5d401a0-c997-4f76-bb7a-ddd5c83e47b6", "Relationships": [{"Type": "CHILD", "Ids": ["57e939f5-0196-4ed8-8e7e-4304abc5af59", "2a96883a-e2c1-4a85-b24f-f8af8373c612", "f2a807c1-0fb2-4220-a588-ccde1600c5f1"]}]}, {"BlockType": "LINE", "Confidence": 99.92334747314453, "Text": "15 Holly St, Ste 208", "Geometry": {"BoundingBox": {"Width": 0.10253734141588211, "Height": 0.009265915490686893, "Left": 0.09871988743543625, "Top": 0.16996805369853973}, "Polygon": [{"X": 0.09871988743543625, "Y": 0.16996923089027405}, {"X": 0.2012571543455124, "Y": 0.16996805369853973}, {"X": 0.20125722885131836, "Y": 0.17923277616500854}, {"X": 0.09871996939182281, "Y": 0.17923396825790405}]}, "Id": "7112c5dc-1f24-4eff-a61c-c3fa339710e4", "Relationships": [{"Type": "CHILD", "Ids": ["6bb4b806-87a9-4de0-ae1c-86ee3685c4bd", "6a69ae5c-7433-4684-9369-114ce52a8c2a", "d5cb4bb7-5b35-45c6-896d-b721eeb9dcf0", "ae7c2d14-fbfe-46a4-9813-0295a2608085", "9f715d55-40c5-4d4d-bcb5-9e9c29faec04"]}]}, {"BlockType": "LINE", "Confidence": 99.9471206665039, "Text": "Scarborough, ME 04074", "Geometry": {"BoundingBox": {"Width": 0.12981843948364258, "Height": 0.009205928072333336, "Left": 0.09805096685886383, "Top": 0.18519563972949982}, "Polygon": [{"X": 0.09805096685886383, "Y": 0.1851971447467804}, {"X": 0.22786933183670044, "Y": 0.18519563972949982}, {"X": 0.2278694063425064, "Y": 0.19440002739429474}, {"X": 0.0980510488152504, "Y": 0.1944015622138977}]}, "Id": "cd4f58fa-2d89-4d07-af2f-0e5aa926decd", "Relationships": [{"Type": "CHILD", "Ids": ["19c0f170-0395-4286-8a4f-760ad72b6f53", "73edbb33-a978-4093-afbe-3ac5cbbc9fa3", "4fd8176c-e300-49b8-82d6-dce1f3a1c14f"]}]}, {"BlockType": "LINE", "Confidence": 98.4619140625, "Text": "(207) 797-2520", "Geometry": {"BoundingBox": {"Width": 0.07953182607889175, "Height": 0.009272538125514984, "Left": 0.098347507417202, "Top": 0.20039115846157074}, "Polygon": [{"X": 0.098347507417202, "Y": 0.20039211213588715}, {"X": 0.17787925899028778, "Y": 0.20039115846157074}, {"X": 0.17787933349609375, "Y": 0.20966275036334991}, {"X": 0.09834758937358856, "Y": 0.20966370403766632}]}, "Id": "b110ac11-7a45-4dcb-a926-7b5a004444b5", "Relationships": [{"Type": "CHILD", "Ids": ["371193cc-1b79-4158-8e03-8ebd758ac82b", "c9ba6ae6-fe7c-4433-a5b3-072adc1341ef"]}]}, {"BlockType": "LINE", "Confidence": 99.63216400146484, "Text": "Fed ID# 01-0546613", "Geometry": {"BoundingBox": {"Width": 0.10715410858392715, "Height": 0.007439058274030685, "Left": 0.09811855852603912, "Top": 0.21549773216247559}, "Polygon": [{"X": 0.09811855852603912, "Y": 0.21549902856349945}, {"X": 0.2052726000547409, "Y": 0.21549773216247559}, {"X": 0.20527265965938568, "Y": 0.2229354828596115}, {"X": 0.0981186255812645, "Y": 0.22293679416179657}]}, "Id": "f2a7b332-40ce-4f07-8222-db2e2c422e76", "Relationships": [{"Type": "CHILD", "Ids": ["4055f44d-3ac5-42be-b5a2-d8cafcfed586", "b0f190b1-5de0-4b99-bbed-e132fd85dc65", "ae79a0a4-4cf2-4207-adde-59d40dae2947"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "CCF", "Geometry": {"BoundingBox": {"Width": 0.04677104949951172, "Height": 0.013972749002277851, "Left": 0.08888953924179077, "Top": 0.24558323621749878}, "Polygon": [{"X": 0.08888953924179077, "Y": 0.24558381736278534}, {"X": 0.13566046953201294, "Y": 0.24558323621749878}, {"X": 0.1356605887413025, "Y": 0.2595553994178772}, {"X": 0.08888966590166092, "Y": 0.25955596566200256}]}, "Id": "aabda136-0aa5-4185-a3bf-673d44e9a62a", "Relationships": [{"Type": "CHILD", "Ids": ["7233fa3e-b8b7-44b7-9c93-f9b1eb43a88a"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Status:", "Geometry": {"BoundingBox": {"Width": 0.04472110792994499, "Height": 0.009133701212704182, "Left": 0.08833416551351547, "Top": 0.26811468601226807}, "Polygon": [{"X": 0.08833416551351547, "Y": 0.26811525225639343}, {"X": 0.1330551952123642, "Y": 0.26811468601226807}, {"X": 0.13305526971817017, "Y": 0.277247816324234}, {"X": 0.08833424746990204, "Y": 0.2772483825683594}]}, "Id": "daa7bdff-379c-4e39-9cc4-07d010fd2f2b", "Relationships": [{"Type": "CHILD", "Ids": ["98c73a20-c912-4413-98fd-bda92d693589"]}]}, {"BlockType": "LINE", "Confidence": 99.69407653808594, "Text": "P", "Geometry": {"BoundingBox": {"Width": 0.009375185705721378, "Height": 0.008919443003833294, "Left": 0.3042791485786438, "Top": 0.26691684126853943}, "Polygon": [{"X": 0.3042791485786438, "Y": 0.266916960477829}, {"X": 0.3136542737483978, "Y": 0.26691684126853943}, {"X": 0.3136543333530426, "Y": 0.27583616971969604}, {"X": 0.3042792081832886, "Y": 0.2758362889289856}]}, "Id": "25a17dfc-fbda-465a-b136-c2fceebdc870", "Relationships": [{"Type": "CHILD", "Ids": ["6487a0bb-fdb3-44c1-a911-96d94e84cdbf"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Manager:", "Geometry": {"BoundingBox": {"Width": 0.06050926446914673, "Height": 0.011111101135611534, "Left": 0.08860442787408829, "Top": 0.2833758294582367}, "Polygon": [{"X": 0.08860442787408829, "Y": 0.2833766043186188}, {"X": 0.14911359548568726, "Y": 0.2833758294582367}, {"X": 0.14911368489265442, "Y": 0.2944861352443695}, {"X": 0.08860452473163605, "Y": 0.294486939907074}]}, "Id": "d6006d79-ad69-436a-b948-58a0d1b9c0cb", "Relationships": [{"Type": "CHILD", "Ids": ["2720187f-9a6b-434f-8b9b-97bebc663b6f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1469", "Geometry": {"BoundingBox": {"Width": 0.031306877732276917, "Height": 0.008991367183625698, "Left": 0.3089882433414459, "Top": 0.28211820125579834}, "Polygon": [{"X": 0.3089882433414459, "Y": 0.28211861848831177}, {"X": 0.34029504656791687, "Y": 0.28211820125579834}, {"X": 0.34029513597488403, "Y": 0.291109174489975}, {"X": 0.3089883029460907, "Y": 0.291109561920166}]}, "Id": "f7491b07-1dad-4d08-a6f8-56f5ec2559dc", "Relationships": [{"Type": "CHILD", "Ids": ["675562ec-7f9e-41fd-8b40-ac3ebcddf4aa"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date:", "Geometry": {"BoundingBox": {"Width": 0.033994294703006744, "Height": 0.0090746795758605, "Left": 0.0886283665895462, "Top": 0.3136776387691498}, "Polygon": [{"X": 0.0886283665895462, "Y": 0.3136780858039856}, {"X": 0.12262258678674698, "Y": 0.3136776387691498}, {"X": 0.12262266129255295, "Y": 0.32275184988975525}, {"X": 0.08862844854593277, "Y": 0.32275232672691345}]}, "Id": "62949721-0943-402e-866b-4682c40ae45a", "Relationships": [{"Type": "CHILD", "Ids": ["c25abc59-9e90-4a38-983a-830c09c78383"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "09/19/2025", "Geometry": {"BoundingBox": {"Width": 0.07285484671592712, "Height": 0.009370890446007252, "Left": 0.3041985034942627, "Top": 0.3123221695423126}, "Polygon": [{"X": 0.3041985034942627, "Y": 0.31232312321662903}, {"X": 0.37705326080322266, "Y": 0.3123221695423126}, {"X": 0.3770533502101898, "Y": 0.3216920793056488}, {"X": 0.30419856309890747, "Y": 0.3216930329799652}]}, "Id": "9e9987dc-efcc-4435-8357-5699913b2ddb", "Relationships": [{"Type": "CHILD", "Ids": ["aa5b7ef7-666f-4ad3-8c73-dea4e21b7ec2"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Truck:", "Geometry": {"BoundingBox": {"Width": 0.0396861806511879, "Height": 0.009020203724503517, "Left": 0.08784928917884827, "Top": 0.3438844382762909}, "Polygon": [{"X": 0.08784928917884827, "Y": 0.34388497471809387}, {"X": 0.1275353878736496, "Y": 0.3438844382762909}, {"X": 0.12753546237945557, "Y": 0.3529040813446045}, {"X": 0.08784937113523483, "Y": 0.35290464758872986}]}, "Id": "5f4cd833-cfa8-4664-b179-963508fe786e", "Relationships": [{"Type": "CHILD", "Ids": ["dbf0feb0-0a1d-48ed-b083-0799b76bf021"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0297", "Geometry": {"BoundingBox": {"Width": 0.032357584685087204, "Height": 0.009099001996219158, "Left": 0.30405038595199585, "Top": 0.3426889479160309}, "Polygon": [{"X": 0.30405038595199585, "Y": 0.3426893949508667}, {"X": 0.33640792965888977, "Y": 0.3426889479160309}, {"X": 0.33640798926353455, "Y": 0.3517875075340271}, {"X": 0.304050475358963, "Y": 0.3517879545688629}]}, "Id": "4460ad84-0f4a-4ba8-9743-a11b0c33312a", "Relationships": [{"Type": "CHILD", "Ids": ["0de0079c-9955-4297-ba17-3fe66c482469"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Trailer:", "Geometry": {"BoundingBox": {"Width": 0.044617779552936554, "Height": 0.009016900323331356, "Left": 0.08795762807130814, "Top": 0.3590334355831146}, "Polygon": [{"X": 0.08795762807130814, "Y": 0.35903406143188477}, {"X": 0.13257533311843872, "Y": 0.3590334355831146}, {"X": 0.1325754076242447, "Y": 0.3680497109889984}, {"X": 0.0879577100276947, "Y": 0.36805033683776855}]}, "Id": "1a2454f6-eef5-4748-85a4-ea8ecb08db8e", "Relationships": [{"Type": "CHILD", "Ids": ["e86992a5-2175-4baa-91c4-602e3dcc2447"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "BA10", "Geometry": {"BoundingBox": {"Width": 0.03535750135779381, "Height": 0.009184202179312706, "Left": 0.30426836013793945, "Top": 0.3578023314476013}, "Polygon": [{"X": 0.30426836013793945, "Y": 0.3578028082847595}, {"X": 0.3396258056163788, "Y": 0.3578023314476013}, {"X": 0.33962586522102356, "Y": 0.3669860363006592}, {"X": 0.3042684495449066, "Y": 0.3669865131378174}]}, "Id": "03804593-11c6-49c9-8a29-38e582fd37e3", "Relationships": [{"Type": "CHILD", "Ids": ["9632d6ca-8736-4c81-b3cb-478c08687a71"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.03444141894578934, "Height": 0.008951269090175629, "Left": 0.08864934742450714, "Top": 0.37422502040863037}, "Polygon": [{"X": 0.08864934742450714, "Y": 0.3742254972457886}, {"X": 0.12309068441390991, "Y": 0.37422502040863037}, {"X": 0.12309076637029648, "Y": 0.383175790309906}, {"X": 0.08864942193031311, "Y": 0.3831762671470642}]}, "Id": "cbc0debf-f451-4a8a-b5a7-96e072ba7689", "Relationships": [{"Type": "CHILD", "Ids": ["3d5d6143-1fa7-4668-b748-61ef4094aa89"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "20", "Geometry": {"BoundingBox": {"Width": 0.016008911654353142, "Height": 0.009008090011775494, "Left": 0.30412012338638306, "Top": 0.3731766939163208}, "Polygon": [{"X": 0.30412012338638306, "Y": 0.3731769025325775}, {"X": 0.3201289474964142, "Y": 0.3731766939163208}, {"X": 0.32012903690338135, "Y": 0.38218453526496887}, {"X": 0.30412018299102783, "Y": 0.382184773683548}]}, "Id": "5eaf20c5-8017-4442-b4e7-071197aec613", "Relationships": [{"Type": "CHILD", "Ids": ["5997c7e2-67c9-40e8-8205-ad643d96f5ec"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.04775600880384445, "Height": 0.009096341207623482, "Left": 0.088441863656044, "Top": 0.4044295847415924}, "Polygon": [{"X": 0.088441863656044, "Y": 0.4044302701950073}, {"X": 0.1361977905035019, "Y": 0.4044295847415924}, {"X": 0.13619787991046906, "Y": 0.41352522373199463}, {"X": 0.08844194561243057, "Y": 0.41352590918540955}]}, "Id": "f25e2b36-f354-491c-bca5-e7b6aee980ce", "Relationships": [{"Type": "CHILD", "Ids": ["429b5b63-415f-4b6f-9222-2453796bae7d"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Modus Logistics", "Geometry": {"BoundingBox": {"Width": 0.104210764169693, "Height": 0.011213158257305622, "Left": 0.3044572174549103, "Top": 0.4032476246356964}, "Polygon": [{"X": 0.3044572174549103, "Y": 0.4032491445541382}, {"X": 0.4086678922176361, "Y": 0.4032476246356964}, {"X": 0.40866798162460327, "Y": 0.414459228515625}, {"X": 0.30445730686187744, "Y": 0.41446077823638916}]}, "Id": "93ecdbb5-f9b8-4c7f-a771-b6c6df25c923", "Relationships": [{"Type": "CHILD", "Ids": ["ce9c5354-2b39-4393-8c8c-2eb0469930a3", "8e3a064d-7b87-4e29-a403-279cb339423e"]}]}, {"BlockType": "LINE", "Confidence": 99.61834716796875, "Text": "PO(s):", "Geometry": {"BoundingBox": {"Width": 0.04117774963378906, "Height": 0.011611591093242168, "Left": 0.08856520801782608, "Top": 0.4193271994590759}, "Polygon": [{"X": 0.08856520801782608, "Y": 0.41932782530784607}, {"X": 0.12974286079406738, "Y": 0.4193271994590759}, {"X": 0.12974295020103455, "Y": 0.430938184261322}, {"X": 0.08856531232595444, "Y": 0.43093881011009216}]}, "Id": "799886a7-d6e2-40ee-acd4-ff0e4e6eb740", "Relationships": [{"Type": "CHILD", "Ids": ["779af040-848e-464e-9a72-b5821b997767"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "2644663", "Geometry": {"BoundingBox": {"Width": 0.05694644898176193, "Height": 0.00913756899535656, "Left": 0.3040180206298828, "Top": 0.41837915778160095}, "Polygon": [{"X": 0.3040180206298828, "Y": 0.4183800220489502}, {"X": 0.36096441745758057, "Y": 0.41837915778160095}, {"X": 0.36096447706222534, "Y": 0.4275158941745758}, {"X": 0.30401811003685, "Y": 0.42751672863960266}]}, "Id": "b44d9e93-f6b5-4c41-970d-bcf54c1d705b", "Relationships": [{"Type": "CHILD", "Ids": ["f4a96553-74f9-4243-96be-f7a72b91d889"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "Vendor(s):", "Geometry": {"BoundingBox": {"Width": 0.06714997440576553, "Height": 0.011405736207962036, "Left": 0.0879560261964798, "Top": 0.43471312522888184}, "Polygon": [{"X": 0.0879560261964798, "Y": 0.43471410870552063}, {"X": 0.15510590374469757, "Y": 0.43471312522888184}, {"X": 0.15510599315166473, "Y": 0.4461178481578827}, {"X": 0.08795613050460815, "Y": 0.44611886143684387}]}, "Id": "c2791944-0eb5-4a04-a9b3-2afc77b06338", "Relationships": [{"Type": "CHILD", "Ids": ["d9a39733-60e0-43ff-818f-241c6b6488db"]}]}, {"BlockType": "LINE", "Confidence": 99.7237777709961, "Text": "AMERCAREROYAL LLC", "Geometry": {"BoundingBox": {"Width": 0.1619282215833664, "Height": 0.009205213747918606, "Left": 0.30348894000053406, "Top": 0.4335234463214874}, "Polygon": [{"X": 0.30348894000053406, "Y": 0.43352586030960083}, {"X": 0.46541711688041687, "Y": 0.4335234463214874}, {"X": 0.46541717648506165, "Y": 0.4427262246608734}, {"X": 0.30348899960517883, "Y": 0.4427286684513092}]}, "Id": "36a9ec70-2d3f-4326-9ae0-7d8e3f337f77", "Relationships": [{"Type": "CHILD", "Ids": ["7e568f41-7442-4448-9695-3032884b8325", "525ad63f-d4cb-40ba-af68-0a18e6eaec9f"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Subtotal:", "Geometry": {"BoundingBox": {"Width": 0.05671493709087372, "Height": 0.009117635898292065, "Left": 0.08853387832641602, "Top": 0.4653353691101074}, "Polygon": [{"X": 0.08853387832641602, "Y": 0.46533623337745667}, {"X": 0.14524874091148376, "Y": 0.4653353691101074}, {"X": 0.14524881541728973, "Y": 0.4744521379470825}, {"X": 0.08853396028280258, "Y": 0.47445300221443176}]}, "Id": "f11604b7-b9e4-465e-9431-27c57c7ea17b", "Relationships": [{"Type": "CHILD", "Ids": ["e4ee115a-f056-4c7d-b844-091ebbc7be90"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$255.00", "Geometry": {"BoundingBox": {"Width": 0.05237512290477753, "Height": 0.01075194776058197, "Left": 0.30420154333114624, "Top": 0.4639718234539032}, "Polygon": [{"X": 0.30420154333114624, "Y": 0.46397262811660767}, {"X": 0.3565765619277954, "Y": 0.4639718234539032}, {"X": 0.3565766513347626, "Y": 0.4747229516506195}, {"X": 0.304201602935791, "Y": 0.474723756313324}]}, "Id": "b42b14e0-3632-4c29-9bc1-fd28656a7aa5", "Relationships": [{"Type": "CHILD", "Ids": ["bdeb7898-500a-4fa8-8939-362cc4c31467"]}]}, {"BlockType": "LINE", "Confidence": 99.9255599975586, "Text": "Convenience Fee:", "Geometry": {"BoundingBox": {"Width": 0.11788487434387207, "Height": 0.008913163095712662, "Left": 0.08841660618782043, "Top": 0.4805949926376343}, "Polygon": [{"X": 0.08841660618782043, "Y": 0.4805968403816223}, {"X": 0.20630140602588654, "Y": 0.4805949926376343}, {"X": 0.2063014805316925, "Y": 0.4895063042640686}, {"X": 0.088416688144207, "Y": 0.48950815200805664}]}, "Id": "8c244dff-70ba-403a-aada-4c54f57f16fe", "Relationships": [{"Type": "CHILD", "Ids": ["223d5563-ad74-400e-b35e-4f028ad05a4e", "00a0924b-ff31-4bfb-b8a8-b0bf8eeeb2a2"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$5.00", "Geometry": {"BoundingBox": {"Width": 0.036628980189561844, "Height": 0.010674250312149525, "Left": 0.3041354715824127, "Top": 0.4791393578052521}, "Polygon": [{"X": 0.3041354715824127, "Y": 0.47913992404937744}, {"X": 0.3407643735408783, "Y": 0.4791393578052521}, {"X": 0.34076446294784546, "Y": 0.48981302976608276}, {"X": 0.3041355609893799, "Y": 0.48981359601020813}]}, "Id": "1647f48d-04d8-4d1e-84b9-6bad434605c5", "Relationships": [{"Type": "CHILD", "Ids": ["56ac1db8-de77-46f9-b032-4bf2b4d0ce5d"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Total Charge:", "Geometry": {"BoundingBox": {"Width": 0.08654822409152985, "Height": 0.0109922019764781, "Left": 0.08805864304304123, "Top": 0.4943627715110779}, "Polygon": [{"X": 0.08805864304304123, "Y": 0.4943641424179077}, {"X": 0.17460677027702332, "Y": 0.4943627715110779}, {"X": 0.17460685968399048, "Y": 0.505353569984436}, {"X": 0.08805873990058899, "Y": 0.5053550004959106}]}, "Id": "adbdc99e-e210-4caf-a3bc-d65bc39bf8ce", "Relationships": [{"Type": "CHILD", "Ids": ["334b178b-e8aa-4527-9ae9-9d164a2ba1af", "8b8f1abf-4a14-4691-95f6-40841bc242ab"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$260.00", "Geometry": {"BoundingBox": {"Width": 0.05247848108410835, "Height": 0.010681424289941788, "Left": 0.30400988459587097, "Top": 0.49299296736717224}, "Polygon": [{"X": 0.30400988459587097, "Y": 0.4929938018321991}, {"X": 0.35648831725120544, "Y": 0.49299296736717224}, {"X": 0.3564883768558502, "Y": 0.5036735534667969}, {"X": 0.30400997400283813, "Y": 0.5036743879318237}]}, "Id": "96709f70-52b5-4a64-aa25-0bf2e510fe82", "Relationships": [{"Type": "CHILD", "Ids": ["116e5837-8a11-4266-a6e0-ada2e08e3944"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Billing Type:", "Geometry": {"BoundingBox": {"Width": 0.07760204374790192, "Height": 0.011018003337085247, "Left": 0.08861210197210312, "Top": 0.5095947980880737}, "Polygon": [{"X": 0.08861210197210312, "Y": 0.509596049785614}, {"X": 0.16621404886245728, "Y": 0.5095947980880737}, {"X": 0.16621413826942444, "Y": 0.520611584186554}, {"X": 0.08861219882965088, "Y": 0.5206128358840942}]}, "Id": "6e5d11e0-e581-4ee7-994e-553a1a76a774", "Relationships": [{"Type": "CHILD", "Ids": ["6817afd7-b831-4ae1-9077-067c55a67ffb", "77ce2cdc-480a-4f85-80d5-76fde41b607e"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Relay Checkout", "Geometry": {"BoundingBox": {"Width": 0.10316216945648193, "Height": 0.011445408686995506, "Left": 0.30450764298439026, "Top": 0.5085292458534241}, "Polygon": [{"X": 0.30450764298439026, "Y": 0.508530855178833}, {"X": 0.4076697528362274, "Y": 0.5085292458534241}, {"X": 0.4076698124408722, "Y": 0.5199729800224304}, {"X": 0.3045077323913574, "Y": 0.5199746489524841}]}, "Id": "44f2c128-f3a6-419f-843b-ffb1c73ad6cd", "Relationships": [{"Type": "CHILD", "Ids": ["898cc740-4266-44f3-a86a-327cefb3fe71", "730cb05d-797e-49d2-9d41-2b1f241f2329"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Relay Checkout ID:", "Geometry": {"BoundingBox": {"Width": 0.12473390996456146, "Height": 0.011507580988109112, "Left": 0.08869021385908127, "Top": 0.5394941568374634}, "Polygon": [{"X": 0.08869021385908127, "Y": 0.5394961833953857}, {"X": 0.21342404186725616, "Y": 0.5394941568374634}, {"X": 0.21342413127422333, "Y": 0.550999641418457}, {"X": 0.08869031816720963, "Y": 0.5510017275810242}]}, "Id": "288bb4f9-5ac0-4c63-ba47-7cf698d60c57", "Relationships": [{"Type": "CHILD", "Ids": ["3bb2a54a-cf4c-4677-897a-dba5a8e18ddb", "55b86d21-af72-4e22-8603-904b504e2802", "4d9912e8-41ea-451c-ba15-5576481a3071"]}]}, {"BlockType": "LINE", "Confidence": 98.33147430419922, "Text": "pc_7rJApoFijZrg7D", "Geometry": {"BoundingBox": {"Width": 0.12548042833805084, "Height": 0.011416055262088776, "Left": 0.30386608839035034, "Top": 0.5384123921394348}, "Polygon": [{"X": 0.30386608839035034, "Y": 0.5384144186973572}, {"X": 0.4293464422225952, "Y": 0.5384123921394348}, {"X": 0.4293465316295624, "Y": 0.5498263835906982}, {"X": 0.3038661777973175, "Y": 0.5498284101486206}]}, "Id": "de55c621-ddf1-4ccf-8201-2440776382e6", "Relationships": [{"Type": "CHILD", "Ids": ["7a730040-8455-4b30-ad9d-019b497a14e7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Relay Payment Type:", "Geometry": {"BoundingBox": {"Width": 0.13776414096355438, "Height": 0.011404715478420258, "Left": 0.08873464912176132, "Top": 0.****************}, "Polygon": [{"X": 0.08873464912176132, "Y": 0.****************}, {"X": 0.22649869322776794, "Y": 0.****************}, {"X": 0.2264987826347351, "Y": 0.5660926699638367}, {"X": 0.08873474597930908, "Y": 0.5660949945449829}]}, "Id": "6c337a3f-2935-4757-83aa-2ffc8d2afeb3", "Relationships": [{"Type": "CHILD", "Ids": ["13b32013-9add-450e-8da3-da6b4baef084", "c0a25b75-2c94-4140-8720-0ddc5a854511", "e7fd51ab-1fd0-477e-ba90-ea64a98d5c48"]}]}, {"BlockType": "LINE", "Confidence": 99.33513641357422, "Text": "credit_card", "Geometry": {"BoundingBox": {"Width": 0.0717218741774559, "Height": 0.011268489994108677, "Left": 0.3042885661125183, "Top": 0.****************}, "Polygon": [{"X": 0.3042885661125183, "Y": 0.****************}, {"X": 0.37601038813591003, "Y": 0.****************}, {"X": 0.3760104477405548, "Y": 0.5648341178894043}, {"X": 0.3042886555194855, "Y": 0.5648353099822998}]}, "Id": "d0f1360c-8386-4fed-9668-de0c2cb158c1", "Relationships": [{"Type": "CHILD", "Ids": ["2e6303a5-7123-4160-95fa-83ad57bae909"]}]}, {"BlockType": "LINE", "Confidence": 99.99755859375, "Text": "Relay Payment Sub Type:", "Geometry": {"BoundingBox": {"Width": 0.1679890900850296, "Height": 0.011458407156169415, "Left": 0.08873946964740753, "Top": 0.569876492023468}, "Polygon": [{"X": 0.08873946964740753, "Y": 0.5698793530464172}, {"X": 0.25672847032546997, "Y": 0.569876492023468}, {"X": 0.25672855973243713, "Y": 0.5813320875167847}, {"X": 0.08873957395553589, "Y": 0.5813348889350891}]}, "Id": "1495437f-7fe8-4b9f-865b-2ff6dc1c50cf", "Relationships": [{"Type": "CHILD", "Ids": ["dff94b4b-c435-47ce-a8f6-daa27ff4636c", "ccb57ee2-ea60-47ba-b228-1631db570bbc", "1d9a44f0-5576-43e0-92c9-5f4d4e2df5a1", "906fb9c3-47a8-49c9-b0dd-122ecae2e5ec"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "visa", "Geometry": {"BoundingBox": {"Width": 0.02605065517127514, "Height": 0.008943475782871246, "Left": 0.30363500118255615, "Top": 0.5689603686332703}, "Polygon": [{"X": 0.30363500118255615, "Y": 0.5689607858657837}, {"X": 0.3296855688095093, "Y": 0.5689603686332703}, {"X": 0.32968565821647644, "Y": 0.5779033899307251}, {"X": 0.3036350607872009, "Y": 0.5779038667678833}]}, "Id": "4440230d-8de3-44d7-aa77-72f8eb32c28b", "Relationships": [{"Type": "CHILD", "Ids": ["37658824-f9e7-4d56-b642-a46cf8aed28a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver Name:", "Geometry": {"BoundingBox": {"Width": 0.08517702668905258, "Height": 0.009094242006540298, "Left": 0.08858705312013626, "Top": 0.6077963709831238}, "Polygon": [{"X": 0.08858705312013626, "Y": 0.6077978610992432}, {"X": 0.17376400530338287, "Y": 0.6077963709831238}, {"X": 0.17376407980918884, "Y": 0.6168891191482544}, {"X": 0.08858713507652283, "Y": 0.6168906092643738}]}, "Id": "d120a464-2377-406e-9c8e-e03b13ba1eb1", "Relationships": [{"Type": "CHILD", "Ids": ["85f044ef-9cec-47ae-8832-e604b0274140", "98d4fecd-7e03-4033-89a8-0387a4ba6656"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.058393821120262146, "Height": 0.011292398907244205, "Left": 0.3044041395187378, "Top": 0.6065451502799988}, "Polygon": [{"X": 0.3044041395187378, "Y": 0.60654616355896}, {"X": 0.36279788613319397, "Y": 0.6065451502799988}, {"X": 0.36279797554016113, "Y": 0.6178365349769592}, {"X": 0.30440422892570496, "Y": 0.6178375482559204}]}, "Id": "a8996110-d375-4f61-b51b-780c5d938aeb", "Relationships": [{"Type": "CHILD", "Ids": ["55b45c37-978e-4a05-8515-e1550540e99b"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Driver Signature:", "Geometry": {"BoundingBox": {"Width": 0.10857317596673965, "Height": 0.011285706423223019, "Left": 0.08866287022829056, "Top": 0.6228972673416138}, "Polygon": [{"X": 0.08866287022829056, "Y": 0.6228991746902466}, {"X": 0.19723594188690186, "Y": 0.6228972673416138}, {"X": 0.1972360461950302, "Y": 0.6341810822486877}, {"X": 0.08866296708583832, "Y": 0.6341829895973206}]}, "Id": "14733cce-8866-4bf5-8eb8-b1ed5fe9ba76", "Relationships": [{"Type": "CHILD", "Ids": ["c51ac453-3eaa-4fff-a69d-3abad537bf4e", "42f61228-ba2d-467e-8c8b-52bf11e1cf74"]}]}, {"BlockType": "WORD", "Confidence": 79.26498413085938, "Text": "IDSH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.29960623383522034, "Height": 0.07476698607206345, "Left": 0.09147091209888458, "Top": 0.06498025357723236}, "Polygon": [{"X": 0.09147091209888458, "Y": 0.06498327106237411}, {"X": 0.39107662439346313, "Y": 0.06498025357723236}, {"X": 0.3910771310329437, "Y": 0.13974392414093018}, {"X": 0.0914715826511383, "Y": 0.1397472321987152}]}, "Id": "287d9f6a-0474-46ea-b9fb-c2052da1dd02"}, {"BlockType": "WORD", "Confidence": 79.23469543457031, "Text": "iNNOVATIVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09391409158706665, "Height": 0.012809657491743565, "Left": 0.09128565341234207, "Top": 0.13858731091022491}, "Polygon": [{"X": 0.09128565341234207, "Y": 0.1385883390903473}, {"X": 0.18519963324069977, "Y": 0.13858731091022491}, {"X": 0.18519973754882812, "Y": 0.15139591693878174}, {"X": 0.09128576517105103, "Y": 0.1513969600200653}]}, "Id": "57e939f5-0196-4ed8-8e7e-4304abc5af59"}, {"BlockType": "WORD", "Confidence": 99.33294677734375, "Text": "DISTRIBUTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11380281299352646, "Height": 0.009335914626717567, "Left": 0.19057224690914154, "Top": 0.14202189445495605}, "Polygon": [{"X": 0.19057224690914154, "Y": 0.14202316105365753}, {"X": 0.3043749928474426, "Y": 0.14202189445495605}, {"X": 0.3043750524520874, "Y": 0.1513565331697464}, {"X": 0.1905723214149475, "Y": 0.15135781466960907}]}, "Id": "2a96883a-e2c1-4a85-b24f-f8af8373c612"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SERVICES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0784129723906517, "Height": 0.009007123298943043, "Left": 0.31014397740364075, "Top": 0.14219370484352112}, "Polygon": [{"X": 0.31014397740364075, "Y": 0.14219458401203156}, {"X": 0.3885568678379059, "Y": 0.14219370484352112}, {"X": 0.38855692744255066, "Y": 0.15119995176792145}, {"X": 0.3101440370082855, "Y": 0.15120083093643188}]}, "Id": "f2a807c1-0fb2-4220-a588-ccde1600c5f1"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012250810861587524, "Height": 0.007320394739508629, "Left": 0.09871988743543625, "Top": 0.1701817661523819}, "Polygon": [{"X": 0.09871988743543625, "Y": 0.17018190026283264}, {"X": 0.110970638692379, "Y": 0.1701817661523819}, {"X": 0.11097069829702377, "Y": 0.17750202119350433}, {"X": 0.09871995449066162, "Y": 0.17750215530395508}]}, "Id": "6bb4b806-87a9-4de0-ae1c-86ee3685c4bd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02582763135433197, "Height": 0.009074250236153603, "Left": 0.114714615046978, "Top": 0.17015953361988068}, "Polygon": [{"X": 0.114714615046978, "Y": 0.17015981674194336}, {"X": 0.1405421644449234, "Y": 0.17015953361988068}, {"X": 0.14054223895072937, "Y": 0.17923347651958466}, {"X": 0.11471469700336456, "Y": 0.17923377454280853}]}, "Id": "6a69ae5c-7433-4684-9369-114ce52a8c2a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "St,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013506787829101086, "Height": 0.008462578058242798, "Left": 0.1437758356332779, "Top": 0.1699911504983902}, "Polygon": [{"X": 0.1437758356332779, "Y": 0.16999129951000214}, {"X": 0.15728256106376648, "Y": 0.1699911504983902}, {"X": 0.15728262066841125, "Y": 0.17845356464385986}, {"X": 0.14377591013908386, "Y": 0.178453728556633}]}, "Id": "d5cb4bb7-5b35-45c6-896d-b721eeb9dcf0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Ste", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01737145148217678, "Height": 0.0074023655615746975, "Left": 0.16113807260990143, "Top": 0.16996830701828003}, "Polygon": [{"X": 0.16113807260990143, "Y": 0.16996850073337555}, {"X": 0.17850945889949799, "Y": 0.16996830701828003}, {"X": 0.17850951850414276, "Y": 0.17737047374248505}, {"X": 0.1611381322145462, "Y": 0.17737066745758057}]}, "Id": "ae7c2d14-fbfe-46a4-9813-0295a2608085"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "208", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019385039806365967, "Height": 0.007251962553709745, "Left": 0.1818721741437912, "Top": 0.1701468676328659}, "Polygon": [{"X": 0.1818721741437912, "Y": 0.1701470911502838}, {"X": 0.2012571543455124, "Y": 0.1701468676328659}, {"X": 0.20125721395015717, "Y": 0.17739860713481903}, {"X": 0.18187223374843597, "Y": 0.17739883065223694}]}, "Id": "9f715d55-40c5-4d4d-bcb5-9e9c29faec04"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Scarborough,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06948329508304596, "Height": 0.009122430346906185, "Left": 0.09805096685886383, "Top": 0.18527913093566895}, "Polygon": [{"X": 0.09805096685886383, "Y": 0.1852799355983734}, {"X": 0.16753418743610382, "Y": 0.18527913093566895}, {"X": 0.1675342619419098, "Y": 0.19440074265003204}, {"X": 0.0980510488152504, "Y": 0.1944015622138977}]}, "Id": "19c0f170-0395-4286-8a4f-760ad72b6f53"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ME", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016935817897319794, "Height": 0.007352172862738371, "Left": 0.17176535725593567, "Top": 0.18519608676433563}, "Polygon": [{"X": 0.17176535725593567, "Y": 0.18519629538059235}, {"X": 0.18870112299919128, "Y": 0.18519608676433563}, {"X": 0.18870118260383606, "Y": 0.19254806637763977}, {"X": 0.17176541686058044, "Y": 0.1925482600927353}]}, "Id": "73edbb33-a978-4093-afbe-3ac5cbbc9fa3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "04074", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03242085129022598, "Height": 0.0073724593967199326, "Left": 0.19544853270053864, "Top": 0.18523138761520386}, "Polygon": [{"X": 0.19544853270053864, "Y": 0.1852317750453949}, {"X": 0.22786933183670044, "Y": 0.18523138761520386}, {"X": 0.22786939144134521, "Y": 0.1926034688949585}, {"X": 0.1954485923051834, "Y": 0.19260385632514954}]}, "Id": "4fd8176c-e300-49b8-82d6-dce1f3a1c14f"}, {"BlockType": "WORD", "Confidence": 98.564453125, "Text": "(207)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02690676599740982, "Height": 0.009271913208067417, "Left": 0.098347507417202, "Top": 0.20039178431034088}, "Polygon": [{"X": 0.098347507417202, "Y": 0.20039211213588715}, {"X": 0.12525419890880585, "Y": 0.20039178431034088}, {"X": 0.12525427341461182, "Y": 0.20966337621212006}, {"X": 0.09834758937358856, "Y": 0.20966370403766632}]}, "Id": "371193cc-1b79-4158-8e03-8ebd758ac82b"}, {"BlockType": "WORD", "Confidence": 98.359375, "Text": "797-2520", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049265116453170776, "Height": 0.007367501500993967, "Left": 0.12861420214176178, "Top": 0.2004631757736206}, "Polygon": [{"X": 0.12861420214176178, "Y": 0.20046375691890717}, {"X": 0.17787925899028778, "Y": 0.2004631757736206}, {"X": 0.17787931859493256, "Y": 0.20783008635044098}, {"X": 0.12861426174640656, "Y": 0.20783066749572754}]}, "Id": "c9ba6ae6-fe7c-4433-a5b3-072adc1341ef"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Fed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019891787320375443, "Height": 0.0074282558634877205, "Left": 0.09811855852603912, "Top": 0.21550853550434113}, "Polygon": [{"X": 0.09811855852603912, "Y": 0.21550877392292023}, {"X": 0.11801028251647949, "Y": 0.21550853550434113}, {"X": 0.11801034212112427, "Y": 0.22293655574321747}, {"X": 0.0981186255812645, "Y": 0.22293679416179657}]}, "Id": "4055f44d-3ac5-42be-b5a2-d8cafcfed586"}, {"BlockType": "WORD", "Confidence": 99.013671875, "Text": "ID#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017957719042897224, "Height": 0.007344744633883238, "Left": 0.12187423557043076, "Top": 0.21553263068199158}, "Polygon": [{"X": 0.12187423557043076, "Y": 0.21553285419940948}, {"X": 0.13983190059661865, "Y": 0.21553263068199158}, {"X": 0.13983196020126343, "Y": 0.2228771597146988}, {"X": 0.12187430262565613, "Y": 0.2228773832321167}]}, "Id": "b0f190b1-5de0-4b99-bbed-e132fd85dc65"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "01-0546613", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06249607726931572, "Height": 0.007393739651888609, "Left": 0.14277659356594086, "Top": 0.21549773216247559}, "Polygon": [{"X": 0.14277659356594086, "Y": 0.21549849212169647}, {"X": 0.2052726000547409, "Y": 0.21549773216247559}, {"X": 0.20527265965938568, "Y": 0.2228907197713852}, {"X": 0.14277665317058563, "Y": 0.22289147973060608}]}, "Id": "ae79a0a4-4cf2-4207-adde-59d40dae2947"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "CCF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04677104949951172, "Height": 0.013972749002277851, "Left": 0.08888953924179077, "Top": 0.24558323621749878}, "Polygon": [{"X": 0.08888953924179077, "Y": 0.24558381736278534}, {"X": 0.13566046953201294, "Y": 0.24558323621749878}, {"X": 0.1356605887413025, "Y": 0.2595553994178772}, {"X": 0.08888966590166092, "Y": 0.25955596566200256}]}, "Id": "7233fa3e-b8b7-44b7-9c93-f9b1eb43a88a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Status:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04472110792994499, "Height": 0.009133701212704182, "Left": 0.08833416551351547, "Top": 0.26811468601226807}, "Polygon": [{"X": 0.08833416551351547, "Y": 0.26811525225639343}, {"X": 0.1330551952123642, "Y": 0.26811468601226807}, {"X": 0.13305526971817017, "Y": 0.277247816324234}, {"X": 0.08833424746990204, "Y": 0.2772483825683594}]}, "Id": "98c73a20-c912-4413-98fd-bda92d693589"}, {"BlockType": "WORD", "Confidence": 99.69407653808594, "Text": "P", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009375185705721378, "Height": 0.008919443003833294, "Left": 0.3042791485786438, "Top": 0.26691684126853943}, "Polygon": [{"X": 0.3042791485786438, "Y": 0.266916960477829}, {"X": 0.3136542737483978, "Y": 0.26691684126853943}, {"X": 0.3136543333530426, "Y": 0.27583616971969604}, {"X": 0.3042792081832886, "Y": 0.2758362889289856}]}, "Id": "6487a0bb-fdb3-44c1-a911-96d94e84cdbf"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Manager:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06050926446914673, "Height": 0.011111101135611534, "Left": 0.08860442787408829, "Top": 0.2833758294582367}, "Polygon": [{"X": 0.08860442787408829, "Y": 0.2833766043186188}, {"X": 0.14911359548568726, "Y": 0.2833758294582367}, {"X": 0.14911368489265442, "Y": 0.2944861352443695}, {"X": 0.08860452473163605, "Y": 0.294486939907074}]}, "Id": "2720187f-9a6b-434f-8b9b-97bebc663b6f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1469", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031306877732276917, "Height": 0.008991367183625698, "Left": 0.3089882433414459, "Top": 0.28211820125579834}, "Polygon": [{"X": 0.3089882433414459, "Y": 0.28211861848831177}, {"X": 0.34029504656791687, "Y": 0.28211820125579834}, {"X": 0.34029513597488403, "Y": 0.291109174489975}, {"X": 0.3089883029460907, "Y": 0.291109561920166}]}, "Id": "675562ec-7f9e-41fd-8b40-ac3ebcddf4aa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033994294703006744, "Height": 0.0090746795758605, "Left": 0.0886283665895462, "Top": 0.3136776387691498}, "Polygon": [{"X": 0.0886283665895462, "Y": 0.3136780858039856}, {"X": 0.12262258678674698, "Y": 0.3136776387691498}, {"X": 0.12262266129255295, "Y": 0.32275184988975525}, {"X": 0.08862844854593277, "Y": 0.32275232672691345}]}, "Id": "c25abc59-9e90-4a38-983a-830c09c78383"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07285484671592712, "Height": 0.009370890446007252, "Left": 0.3041985034942627, "Top": 0.3123221695423126}, "Polygon": [{"X": 0.3041985034942627, "Y": 0.31232312321662903}, {"X": 0.37705326080322266, "Y": 0.3123221695423126}, {"X": 0.3770533502101898, "Y": 0.3216920793056488}, {"X": 0.30419856309890747, "Y": 0.3216930329799652}]}, "Id": "aa5b7ef7-666f-4ad3-8c73-dea4e21b7ec2"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Truck:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0396861806511879, "Height": 0.009020203724503517, "Left": 0.08784928917884827, "Top": 0.3438844382762909}, "Polygon": [{"X": 0.08784928917884827, "Y": 0.34388497471809387}, {"X": 0.1275353878736496, "Y": 0.3438844382762909}, {"X": 0.12753546237945557, "Y": 0.3529040813446045}, {"X": 0.08784937113523483, "Y": 0.35290464758872986}]}, "Id": "dbf0feb0-0a1d-48ed-b083-0799b76bf021"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0297", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032357584685087204, "Height": 0.009099001996219158, "Left": 0.30405038595199585, "Top": 0.3426889479160309}, "Polygon": [{"X": 0.30405038595199585, "Y": 0.3426893949508667}, {"X": 0.33640792965888977, "Y": 0.3426889479160309}, {"X": 0.33640798926353455, "Y": 0.3517875075340271}, {"X": 0.304050475358963, "Y": 0.3517879545688629}]}, "Id": "0de0079c-9955-4297-ba17-3fe66c482469"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Trailer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044617779552936554, "Height": 0.009016900323331356, "Left": 0.08795762807130814, "Top": 0.3590334355831146}, "Polygon": [{"X": 0.08795762807130814, "Y": 0.35903406143188477}, {"X": 0.13257533311843872, "Y": 0.3590334355831146}, {"X": 0.1325754076242447, "Y": 0.3680497109889984}, {"X": 0.0879577100276947, "Y": 0.36805033683776855}]}, "Id": "e86992a5-2175-4baa-91c4-602e3dcc2447"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "BA10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03535750135779381, "Height": 0.009184202179312706, "Left": 0.30426836013793945, "Top": 0.3578023314476013}, "Polygon": [{"X": 0.30426836013793945, "Y": 0.3578028082847595}, {"X": 0.3396258056163788, "Y": 0.3578023314476013}, {"X": 0.33962586522102356, "Y": 0.3669860363006592}, {"X": 0.3042684495449066, "Y": 0.3669865131378174}]}, "Id": "9632d6ca-8736-4c81-b3cb-478c08687a71"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03444141894578934, "Height": 0.008951269090175629, "Left": 0.08864934742450714, "Top": 0.37422502040863037}, "Polygon": [{"X": 0.08864934742450714, "Y": 0.3742254972457886}, {"X": 0.12309068441390991, "Y": 0.37422502040863037}, {"X": 0.12309076637029648, "Y": 0.383175790309906}, {"X": 0.08864942193031311, "Y": 0.3831762671470642}]}, "Id": "3d5d6143-1fa7-4668-b748-61ef4094aa89"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "20", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016008911654353142, "Height": 0.009008090011775494, "Left": 0.30412012338638306, "Top": 0.3731766939163208}, "Polygon": [{"X": 0.30412012338638306, "Y": 0.3731769025325775}, {"X": 0.3201289474964142, "Y": 0.3731766939163208}, {"X": 0.32012903690338135, "Y": 0.38218453526496887}, {"X": 0.30412018299102783, "Y": 0.382184773683548}]}, "Id": "5997c7e2-67c9-40e8-8205-ad643d96f5ec"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04775600880384445, "Height": 0.009096341207623482, "Left": 0.088441863656044, "Top": 0.4044295847415924}, "Polygon": [{"X": 0.088441863656044, "Y": 0.4044302701950073}, {"X": 0.1361977905035019, "Y": 0.4044295847415924}, {"X": 0.13619787991046906, "Y": 0.41352522373199463}, {"X": 0.08844194561243057, "Y": 0.41352590918540955}]}, "Id": "429b5b63-415f-4b6f-9222-2453796bae7d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Modus", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04331675171852112, "Height": 0.009086806327104568, "Left": 0.3044572174549103, "Top": 0.40324848890304565}, "Polygon": [{"X": 0.3044572174549103, "Y": 0.4032491445541382}, {"X": 0.3477739095687866, "Y": 0.40324848890304565}, {"X": 0.3477739691734314, "Y": 0.412334680557251}, {"X": 0.30445727705955505, "Y": 0.4123353064060211}]}, "Id": "ce9c5354-2b39-4393-8c8c-2eb0469930a3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056331817060709, "Height": 0.011131981387734413, "Left": 0.35233616828918457, "Top": 0.4033280909061432}, "Polygon": [{"X": 0.35233616828918457, "Y": 0.40332889556884766}, {"X": 0.4086678922176361, "Y": 0.4033280909061432}, {"X": 0.40866798162460327, "Y": 0.414459228515625}, {"X": 0.35233625769615173, "Y": 0.41446006298065186}]}, "Id": "8e3a064d-7b87-4e29-a403-279cb339423e"}, {"BlockType": "WORD", "Confidence": 99.61834716796875, "Text": "PO(s):", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04117774963378906, "Height": 0.011611591093242168, "Left": 0.08856520801782608, "Top": 0.4193271994590759}, "Polygon": [{"X": 0.08856520801782608, "Y": 0.41932782530784607}, {"X": 0.12974286079406738, "Y": 0.4193271994590759}, {"X": 0.12974295020103455, "Y": 0.430938184261322}, {"X": 0.08856531232595444, "Y": 0.43093881011009216}]}, "Id": "779af040-848e-464e-9a72-b5821b997767"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2644663", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05694644898176193, "Height": 0.00913756899535656, "Left": 0.3040180206298828, "Top": 0.41837915778160095}, "Polygon": [{"X": 0.3040180206298828, "Y": 0.4183800220489502}, {"X": 0.36096441745758057, "Y": 0.41837915778160095}, {"X": 0.36096447706222534, "Y": 0.4275158941745758}, {"X": 0.30401811003685, "Y": 0.42751672863960266}]}, "Id": "f4a96553-74f9-4243-96be-f7a72b91d889"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "Vendor(s):", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06714997440576553, "Height": 0.011405736207962036, "Left": 0.0879560261964798, "Top": 0.43471312522888184}, "Polygon": [{"X": 0.0879560261964798, "Y": 0.43471410870552063}, {"X": 0.15510590374469757, "Y": 0.43471312522888184}, {"X": 0.15510599315166473, "Y": 0.4461178481578827}, {"X": 0.08795613050460815, "Y": 0.44611886143684387}]}, "Id": "d9a39733-60e0-43ff-818f-241c6b6488db"}, {"BlockType": "WORD", "Confidence": 99.67215728759766, "Text": "AMERCAREROYAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13186176121234894, "Height": 0.009204762987792492, "Left": 0.30348894000053406, "Top": 0.43352389335632324}, "Polygon": [{"X": 0.30348894000053406, "Y": 0.43352586030960083}, {"X": 0.4353506565093994, "Y": 0.43352389335632324}, {"X": 0.4353507161140442, "Y": 0.44272667169570923}, {"X": 0.30348899960517883, "Y": 0.4427286684513092}]}, "Id": "7e568f41-7442-4448-9695-3032884b8325"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02606622874736786, "Height": 0.009076369926333427, "Left": 0.4393509328365326, "Top": 0.43355226516723633}, "Polygon": [{"X": 0.4393509328365326, "Y": 0.43355265259742737}, {"X": 0.46541711688041687, "Y": 0.43355226516723633}, {"X": 0.46541717648506165, "Y": 0.44262823462486267}, {"X": 0.43935099244117737, "Y": 0.4426286220550537}]}, "Id": "525ad63f-d4cb-40ba-af68-0a18e6eaec9f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Subtotal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05671493709087372, "Height": 0.009117635898292065, "Left": 0.08853387832641602, "Top": 0.4653353691101074}, "Polygon": [{"X": 0.08853387832641602, "Y": 0.46533623337745667}, {"X": 0.14524874091148376, "Y": 0.4653353691101074}, {"X": 0.14524881541728973, "Y": 0.4744521379470825}, {"X": 0.08853396028280258, "Y": 0.47445300221443176}]}, "Id": "e4ee115a-f056-4c7d-b844-091ebbc7be90"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$255.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05237512290477753, "Height": 0.01075194776058197, "Left": 0.30420154333114624, "Top": 0.4639718234539032}, "Polygon": [{"X": 0.30420154333114624, "Y": 0.46397262811660767}, {"X": 0.3565765619277954, "Y": 0.4639718234539032}, {"X": 0.3565766513347626, "Y": 0.4747229516506195}, {"X": 0.304201602935791, "Y": 0.474723756313324}]}, "Id": "bdeb7898-500a-4fa8-8939-362cc4c31467"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08501233160495758, "Height": 0.008770388551056385, "Left": 0.08841660618782043, "Top": 0.48061230778694153}, "Polygon": [{"X": 0.08841660618782043, "Y": 0.480613648891449}, {"X": 0.17342886328697205, "Y": 0.48061230778694153}, {"X": 0.17342893779277802, "Y": 0.4893813729286194}, {"X": 0.088416688144207, "Y": 0.48938271403312683}]}, "Id": "223d5563-ad74-400e-b35e-4f028ad05a4e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027966681867837906, "Height": 0.008911751210689545, "Left": 0.1783348023891449, "Top": 0.4805949926376343}, "Polygon": [{"X": 0.1783348023891449, "Y": 0.4805954396724701}, {"X": 0.20630140602588654, "Y": 0.4805949926376343}, {"X": 0.2063014805316925, "Y": 0.4895063042640686}, {"X": 0.17833487689495087, "Y": 0.4895067512989044}]}, "Id": "00a0924b-ff31-4bfb-b8a8-b0bf8eeeb2a2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$5.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036628980189561844, "Height": 0.010674250312149525, "Left": 0.3041354715824127, "Top": 0.4791393578052521}, "Polygon": [{"X": 0.3041354715824127, "Y": 0.47913992404937744}, {"X": 0.3407643735408783, "Y": 0.4791393578052521}, {"X": 0.34076446294784546, "Y": 0.48981302976608276}, {"X": 0.3041355609893799, "Y": 0.48981359601020813}]}, "Id": "56ac1db8-de77-46f9-b032-4bf2b4d0ce5d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03092132695019245, "Height": 0.009115415625274181, "Left": 0.08805864304304123, "Top": 0.494451642036438}, "Polygon": [{"X": 0.08805864304304123, "Y": 0.4944521486759186}, {"X": 0.11897988617420197, "Y": 0.494451642036438}, {"X": 0.11897996813058853, "Y": 0.503566563129425}, {"X": 0.0880587249994278, "Y": 0.5035670399665833}]}, "Id": "334b178b-e8aa-4527-9ae9-9d164a2ba1af"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05099992826581001, "Height": 0.010991636663675308, "Left": 0.12360693514347076, "Top": 0.4943627715110779}, "Polygon": [{"X": 0.12360693514347076, "Y": 0.49436357617378235}, {"X": 0.17460677027702332, "Y": 0.4943627715110779}, {"X": 0.17460685968399048, "Y": 0.505353569984436}, {"X": 0.12360703200101852, "Y": 0.5053544044494629}]}, "Id": "8b8f1abf-4a14-4691-95f6-40841bc242ab"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$260.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05247848108410835, "Height": 0.010681424289941788, "Left": 0.30400988459587097, "Top": 0.49299296736717224}, "Polygon": [{"X": 0.30400988459587097, "Y": 0.4929938018321991}, {"X": 0.35648831725120544, "Y": 0.49299296736717224}, {"X": 0.3564883768558502, "Y": 0.5036735534667969}, {"X": 0.30400997400283813, "Y": 0.5036743879318237}]}, "Id": "116e5837-8a11-4266-a6e0-ada2e08e3944"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03821379318833351, "Height": 0.011017374694347382, "Left": 0.08861210197210312, "Top": 0.5095954537391663}, "Polygon": [{"X": 0.08861210197210312, "Y": 0.509596049785614}, {"X": 0.12682579457759857, "Y": 0.5095954537391663}, {"X": 0.12682589888572693, "Y": 0.5206121802330017}, {"X": 0.08861219882965088, "Y": 0.5206128358840942}]}, "Id": "6817afd7-b831-4ae1-9077-067c55a67ffb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035314057022333145, "Height": 0.010909413918852806, "Left": 0.1309000849723816, "Top": 0.5096877813339233}, "Polygon": [{"X": 0.1309000849723816, "Y": 0.5096883177757263}, {"X": 0.16621404886245728, "Y": 0.5096877813339233}, {"X": 0.16621413826942444, "Y": 0.5205966234207153}, {"X": 0.13090017437934875, "Y": 0.5205972194671631}]}, "Id": "77ce2cdc-480a-4f85-80d5-76fde41b607e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03697781637310982, "Height": 0.011185431852936745, "Left": 0.30450764298439026, "Top": 0.5087891817092896}, "Polygon": [{"X": 0.30450764298439026, "Y": 0.5087897777557373}, {"X": 0.3414853811264038, "Y": 0.5087891817092896}, {"X": 0.34148547053337097, "Y": 0.5199740529060364}, {"X": 0.3045077323913574, "Y": 0.5199746489524841}]}, "Id": "898cc740-4266-44f3-a86a-327cefb3fe71"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Checkout", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062115322798490524, "Height": 0.009305569343268871, "Left": 0.34555450081825256, "Top": 0.5085292458534241}, "Polygon": [{"X": 0.34555450081825256, "Y": 0.5085301995277405}, {"X": 0.4076697528362274, "Y": 0.5085292458534241}, {"X": 0.4076698124408722, "Y": 0.5178337693214417}, {"X": 0.34555456042289734, "Y": 0.5178347826004028}]}, "Id": "730cb05d-797e-49d2-9d41-2b1f241f2329"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03721807524561882, "Height": 0.011452052742242813, "Left": 0.08869021385908127, "Top": 0.5395496487617493}, "Polygon": [{"X": 0.08869021385908127, "Y": 0.539550244808197}, {"X": 0.12590819597244263, "Y": 0.5395496487617493}, {"X": 0.12590830028057098, "Y": 0.5510010719299316}, {"X": 0.08869031816720963, "Y": 0.5510017275810242}]}, "Id": "3bb2a54a-cf4c-4677-897a-dba5a8e18ddb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Checkout", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061754804104566574, "Height": 0.009196441620588303, "Left": 0.13022580742835999, "Top": 0.5394944548606873}, "Polygon": [{"X": 0.13022580742835999, "Y": 0.5394954681396484}, {"X": 0.1919805407524109, "Y": 0.5394944548606873}, {"X": 0.19198061525821686, "Y": 0.5486899018287659}, {"X": 0.13022588193416595, "Y": 0.548690915107727}]}, "Id": "55b86d21-af72-4e22-8603-904b504e2802"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01714724861085415, "Height": 0.009014853276312351, "Left": 0.19627685844898224, "Top": 0.5397011637687683}, "Polygon": [{"X": 0.19627685844898224, "Y": 0.5397014617919922}, {"X": 0.21342404186725616, "Y": 0.5397011637687683}, {"X": 0.21342411637306213, "Y": 0.5487157106399536}, {"X": 0.1962769329547882, "Y": 0.5487160086631775}]}, "Id": "4d9912e8-41ea-451c-ba15-5576481a3071"}, {"BlockType": "WORD", "Confidence": 98.33147430419922, "Text": "pc_7rJApoFijZrg7D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12548042833805084, "Height": 0.011416055262088776, "Left": 0.30386608839035034, "Top": 0.5384123921394348}, "Polygon": [{"X": 0.30386608839035034, "Y": 0.5384144186973572}, {"X": 0.4293464422225952, "Y": 0.5384123921394348}, {"X": 0.4293465316295624, "Y": 0.5498263835906982}, {"X": 0.3038661777973175, "Y": 0.5498284101486206}]}, "Id": "7a730040-8455-4b30-ad9d-019b497a14e7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037241220474243164, "Height": 0.011293350718915462, "Left": 0.08873464912176132, "Top": 0.5547857284545898}, "Polygon": [{"X": 0.08873464912176132, "Y": 0.5547863245010376}, {"X": 0.12597577273845673, "Y": 0.5547857284545898}, {"X": 0.1259758621454239, "Y": 0.5660784244537354}, {"X": 0.08873474597930908, "Y": 0.5660790801048279}]}, "Id": "13b32013-9add-450e-8da3-da6b4baef084"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05730007588863373, "Height": 0.01120131928473711, "Left": 0.13038142025470734, "Top": 0.5548929572105408}, "Polygon": [{"X": 0.13038142025470734, "Y": 0.5548939108848572}, {"X": 0.1876813918352127, "Y": 0.5548929572105408}, {"X": 0.18768149614334106, "Y": 0.5660933256149292}, {"X": 0.1303815096616745, "Y": 0.5660942792892456}]}, "Id": "c0a25b75-2c94-4140-8720-0ddc5a854511"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03537574037909508, "Height": 0.011291392147541046, "Left": 0.19112305343151093, "Top": 0.****************}, "Polygon": [{"X": 0.19112305343151093, "Y": 0.****************}, {"X": 0.22649869322776794, "Y": 0.****************}, {"X": 0.2264987826347351, "Y": 0.5659810900688171}, {"X": 0.1911231428384781, "Y": 0.5659816861152649}]}, "Id": "e7fd51ab-1fd0-477e-ba90-ea64a98d5c48"}, {"BlockType": "WORD", "Confidence": 99.33513641357422, "Text": "credit_card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0717218741774559, "Height": 0.011268489994108677, "Left": 0.3042885661125183, "Top": 0.****************}, "Polygon": [{"X": 0.3042885661125183, "Y": 0.****************}, {"X": 0.37601038813591003, "Y": 0.****************}, {"X": 0.3760104477405548, "Y": 0.5648341178894043}, {"X": 0.3042886555194855, "Y": 0.5648353099822998}]}, "Id": "2e6303a5-7123-4160-95fa-83ad57bae909"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037188369780778885, "Height": 0.011253383941948414, "Left": 0.08873947709798813, "Top": 0.5700268149375916}, "Polygon": [{"X": 0.08873947709798813, "Y": 0.5700274705886841}, {"X": 0.12592774629592896, "Y": 0.5700268149375916}, {"X": 0.1259278506040573, "Y": 0.5812795758247375}, {"X": 0.08873957395553589, "Y": 0.5812802314758301}]}, "Id": "dff94b4b-c435-47ce-a8f6-daa27ff4636c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05732933059334755, "Height": 0.01126637402921915, "Left": 0.1302911788225174, "Top": 0.570067822933197}, "Polygon": [{"X": 0.1302911788225174, "Y": 0.5700687766075134}, {"X": 0.18762041628360748, "Y": 0.570067822933197}, {"X": 0.18762050569057465, "Y": 0.5813332200050354}, {"X": 0.13029128313064575, "Y": 0.5813342332839966}]}, "Id": "ccb57ee2-ea60-47ba-b228-1631db570bbc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Sub", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025520876049995422, "Height": 0.00903836265206337, "Left": 0.19188156723976135, "Top": 0.5698771476745605}, "Polygon": [{"X": 0.19188156723976135, "Y": 0.5698776245117188}, {"X": 0.2174023687839508, "Y": 0.5698771476745605}, {"X": 0.21740244328975677, "Y": 0.5789151191711426}, {"X": 0.19188164174556732, "Y": 0.578915536403656}]}, "Id": "1d9a44f0-5576-43e0-92c9-5f4d4e2df5a1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03555265814065933, "Height": 0.011277555488049984, "Left": 0.2211759090423584, "Top": 0.5699173808097839}, "Polygon": [{"X": 0.2211759090423584, "Y": 0.5699179768562317}, {"X": 0.25672847032546997, "Y": 0.5699173808097839}, {"X": 0.25672855973243713, "Y": 0.5811943411827087}, {"X": 0.22117599844932556, "Y": 0.5811949372291565}]}, "Id": "906fb9c3-47a8-49c9-b0dd-122ecae2e5ec"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "visa", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02605065517127514, "Height": 0.008943475782871246, "Left": 0.30363500118255615, "Top": 0.5689603686332703}, "Polygon": [{"X": 0.30363500118255615, "Y": 0.5689607858657837}, {"X": 0.3296855688095093, "Y": 0.5689603686332703}, {"X": 0.32968565821647644, "Y": 0.5779033899307251}, {"X": 0.3036350607872009, "Y": 0.5779038667678833}]}, "Id": "37658824-f9e7-4d56-b642-a46cf8aed28a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039142951369285583, "Height": 0.009093447588384151, "Left": 0.08858705312013626, "Top": 0.6077971458435059}, "Polygon": [{"X": 0.08858705312013626, "Y": 0.6077978610992432}, {"X": 0.12772992253303528, "Y": 0.6077971458435059}, {"X": 0.12773001194000244, "Y": 0.6168899536132812}, {"X": 0.08858713507652283, "Y": 0.6168906092643738}]}, "Id": "85f044ef-9cec-47ae-8832-e604b0274140"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041850496083498, "Height": 0.00895834993571043, "Left": 0.13191358745098114, "Top": 0.6079067587852478}, "Polygon": [{"X": 0.13191358745098114, "Y": 0.6079074740409851}, {"X": 0.17376400530338287, "Y": 0.6079067587852478}, {"X": 0.17376407980918884, "Y": 0.6168643832206726}, {"X": 0.1319136768579483, "Y": 0.6168650984764099}]}, "Id": "98d4fecd-7e03-4033-89a8-0387a4ba6656"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058393821120262146, "Height": 0.011292398907244205, "Left": 0.3044041395187378, "Top": 0.6065451502799988}, "Polygon": [{"X": 0.3044041395187378, "Y": 0.60654616355896}, {"X": 0.36279788613319397, "Y": 0.6065451502799988}, {"X": 0.36279797554016113, "Y": 0.6178365349769592}, {"X": 0.30440422892570496, "Y": 0.6178375482559204}]}, "Id": "55b45c37-978e-4a05-8515-e1550540e99b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03907347843050957, "Height": 0.00909243430942297, "Left": 0.08866287022829056, "Top": 0.6228984594345093}, "Polygon": [{"X": 0.08866287022829056, "Y": 0.6228991746902466}, {"X": 0.12773627042770386, "Y": 0.6228984594345093}, {"X": 0.12773634493350983, "Y": 0.6319902539253235}, {"X": 0.08866295218467712, "Y": 0.631990909576416}]}, "Id": "c51ac453-3eaa-4fff-a69d-3abad537bf4e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06560692191123962, "Height": 0.011282263323664665, "Left": 0.1316291242837906, "Top": 0.6228999495506287}, "Polygon": [{"X": 0.1316291242837906, "Y": 0.6229010820388794}, {"X": 0.19723594188690186, "Y": 0.6228999495506287}, {"X": 0.1972360461950302, "Y": 0.6341810822486877}, {"X": 0.13162921369075775, "Y": 0.6341822147369385}]}, "Id": "42f61228-ba2d-467e-8c8b-52bf11e1cf74"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "4e46dba1-36d7-4f9a-a313-eb93e712c3fa", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "4e46dba1-36d7-4f9a-a313-eb93e712c3fa", "content-type": "application/x-amz-json-1.1", "content-length": "58122", "date": "Wed, 24 Sep 2025 13:26:48 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "bca0650c-d9d0-4708-8701-be3691ff6d5d", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 13:26:51 GMT", "content-type": "application/json", "content-length": "1334", "connection": "keep-alive", "x-amzn-requestid": "bca0650c-d9d0-4708-8701-be3691ff6d5d"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only one page provided. We need to parse content. It's a receipt/invoice? It says \"Subtotal\", \"Convenience Fee\", \"Total Charge\". No explicit header like \"Invoice\". But it's a billing type, likely invoice. But check differences: It's from IDSH \"Innovative Distribution Services\" - maybe a billing receipt. It includes PO(s), Vendor(s), etc. No HS code, no customs. So likely \"invoice\". Not a lumper receipt because no door number? It has door number 20. But lumper receipt includes PO, door number, date-time, initial pallets, finished pallets, service charge, total amount. This page has PO(s): 2644663. It has door number. It has driver name. Could be lumper receipt. But lumper receipt is for loading/unloading labor. This seems more like a billing for a relay checkout. The presence of \"Billing Type: Relay Checkout\" suggests it's an invoice from a relay payment system. So classify as \"invoice\". So output JSON with page 1, doc_type: \"invoice\".\n\nReturn via tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"invoice\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2767, "outputTokens": 248, "totalTokens": 3015}, "metrics": {"latencyMs": 1169}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/a771d0eb_GKNF55W2CF2JR6EBXMPX.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - PDF/image to text is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice or Freight Bill\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load conformation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or over an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or over an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nIDSH\niNNOVATIVE DISTRIBUTION SERVICES\n15 Holly St, Ste 208\nScarborough, ME 04074\n(207) 797-2520\nFed ID# 01-0546613\nCCF\nStatus:\nP\nManager:\n1469\nDate:\n09/19/2025\nTruck:\n0297\nTrailer:\nBA10\nDoor:\n20\nCarrier:\nModus Logistics\nPO(s):\n2644663\nVendor(s):\nAMERCAREROYAL LLC\nSubtotal:\n$255.00\nConvenience Fee:\n$5.00\nTotal Charge:\n$260.00\nBilling Type:\nRelay Checkout\nRelay Checkout ID:\npc_7rJApoFijZrg7D\nRelay Payment Type:\ncredit_card\nRelay Payment Sub Type:\nvisa\nDriver Name:\nSiyovush\nDriver Signature:\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}